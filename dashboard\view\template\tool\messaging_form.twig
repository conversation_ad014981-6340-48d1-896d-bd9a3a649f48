{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="submit" form="tool\messaging-form" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fas fa-save"></i></button>
        <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-light"><i class="fas fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    
    <div class="card">
      <div class="card-header"><i class="fas fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}chart-bar{% elif screen_type == 'settings' %}cog{% else %}pencil-alt{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="card-body">
        <form id="tool\messaging-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-add">{{ text_add }}</label>
            <div class="col-sm-10">
              <input type="text" name="add" value="{{ add }}" placeholder="{{ text_add }}" id="input-add" class="form-control" />
              {% if error_add %}
                <div class="invalid-feedback">{{ error_add }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-all">{{ text_all }}</label>
            <div class="col-sm-10">
              <input type="text" name="all" value="{{ all }}" placeholder="{{ text_all }}" id="input-all" class="form-control" />
              {% if error_all %}
                <div class="invalid-feedback">{{ error_all }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-archived">{{ text_archived }}</label>
            <div class="col-sm-10">
              <input type="text" name="archived" value="{{ archived }}" placeholder="{{ text_archived }}" id="input-archived" class="form-control" />
              {% if error_archived %}
                <div class="invalid-feedback">{{ error_archived }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-archived_count">{{ text_archived_count }}</label>
            <div class="col-sm-10">
              <input type="text" name="archived_count" value="{{ archived_count }}" placeholder="{{ text_archived_count }}" id="input-archived_count" class="form-control" />
              {% if error_archived_count %}
                <div class="invalid-feedback">{{ error_archived_count }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-attachment">{{ text_attachment }}</label>
            <div class="col-sm-10">
              <input type="text" name="attachment" value="{{ attachment }}" placeholder="{{ text_attachment }}" id="input-attachment" class="form-control" />
              {% if error_attachment %}
                <div class="invalid-feedback">{{ error_attachment }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-conversations">{{ text_conversations }}</label>
            <div class="col-sm-10">
              <input type="text" name="conversations" value="{{ conversations }}" placeholder="{{ text_conversations }}" id="input-conversations" class="form-control" />
              {% if error_conversations %}
                <div class="invalid-feedback">{{ error_conversations }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-delete">{{ text_delete }}</label>
            <div class="col-sm-10">
              <input type="text" name="delete" value="{{ delete }}" placeholder="{{ text_delete }}" id="input-delete" class="form-control" />
              {% if error_delete %}
                <div class="invalid-feedback">{{ error_delete }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-draft">{{ text_draft }}</label>
            <div class="col-sm-10">
              <input type="text" name="draft" value="{{ draft }}" placeholder="{{ text_draft }}" id="input-draft" class="form-control" />
              {% if error_draft %}
                <div class="invalid-feedback">{{ error_draft }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-draft_count">{{ text_draft_count }}</label>
            <div class="col-sm-10">
              <input type="text" name="draft_count" value="{{ draft_count }}" placeholder="{{ text_draft_count }}" id="input-draft_count" class="form-control" />
              {% if error_draft_count %}
                <div class="invalid-feedback">{{ error_draft_count }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_message">{{ text_error_message }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_message" value="{{ error_message }}" placeholder="{{ text_error_message }}" id="input-error_message" class="form-control" />
              {% if error_error_message %}
                <div class="invalid-feedback">{{ error_error_message }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_reply">{{ text_error_reply }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_reply" value="{{ error_reply }}" placeholder="{{ text_error_reply }}" id="input-error_reply" class="form-control" />
              {% if error_error_reply %}
                <div class="invalid-feedback">{{ error_error_reply }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_subject">{{ text_error_subject }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_subject" value="{{ error_subject }}" placeholder="{{ text_error_subject }}" id="input-error_subject" class="form-control" />
              {% if error_error_subject %}
                <div class="invalid-feedback">{{ error_error_subject }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter">{{ text_filter }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter" value="{{ filter }}" placeholder="{{ text_filter }}" id="input-filter" class="form-control" />
              {% if error_filter %}
                <div class="invalid-feedback">{{ error_filter }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_conversation_id">{{ text_filter_conversation_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_conversation_id" value="{{ filter_conversation_id }}" placeholder="{{ text_filter_conversation_id }}" id="input-filter_conversation_id" class="form-control" />
              {% if error_filter_conversation_id %}
                <div class="invalid-feedback">{{ error_filter_conversation_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_date_end">{{ text_filter_date_end }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_date_end" value="{{ filter_date_end }}" placeholder="{{ text_filter_date_end }}" id="input-filter_date_end" class="form-control" />
              {% if error_filter_date_end %}
                <div class="invalid-feedback">{{ error_filter_date_end }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_date_start">{{ text_filter_date_start }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_date_start" value="{{ filter_date_start }}" placeholder="{{ text_filter_date_start }}" id="input-filter_date_start" class="form-control" />
              {% if error_filter_date_start %}
                <div class="invalid-feedback">{{ error_filter_date_start }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_has_attachment">{{ text_filter_has_attachment }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_has_attachment" value="{{ filter_has_attachment }}" placeholder="{{ text_filter_has_attachment }}" id="input-filter_has_attachment" class="form-control" />
              {% if error_filter_has_attachment %}
                <div class="invalid-feedback">{{ error_filter_has_attachment }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_priority">{{ text_filter_priority }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_priority" value="{{ filter_priority }}" placeholder="{{ text_filter_priority }}" id="input-filter_priority" class="form-control" />
              {% if error_filter_priority %}
                <div class="invalid-feedback">{{ error_filter_priority }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_read">{{ text_filter_read }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_read" value="{{ filter_read }}" placeholder="{{ text_filter_read }}" id="input-filter_read" class="form-control" />
              {% if error_filter_read %}
                <div class="invalid-feedback">{{ error_filter_read }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_recipient">{{ text_filter_recipient }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_recipient" value="{{ filter_recipient }}" placeholder="{{ text_filter_recipient }}" id="input-filter_recipient" class="form-control" />
              {% if error_filter_recipient %}
                <div class="invalid-feedback">{{ error_filter_recipient }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_sender">{{ text_filter_sender }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_sender" value="{{ filter_sender }}" placeholder="{{ text_filter_sender }}" id="input-filter_sender" class="form-control" />
              {% if error_filter_sender %}
                <div class="invalid-feedback">{{ error_filter_sender }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_subject">{{ text_filter_subject }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_subject" value="{{ filter_subject }}" placeholder="{{ text_filter_subject }}" id="input-filter_subject" class="form-control" />
              {% if error_filter_subject %}
                <div class="invalid-feedback">{{ error_filter_subject }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-folder">{{ text_folder }}</label>
            <div class="col-sm-10">
              <input type="text" name="folder" value="{{ folder }}" placeholder="{{ text_folder }}" id="input-folder" class="form-control" />
              {% if error_folder %}
                <div class="invalid-feedback">{{ error_folder }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="invalid-feedback">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-history">{{ text_history }}</label>
            <div class="col-sm-10">
              <input type="text" name="history" value="{{ history }}" placeholder="{{ text_history }}" id="input-history" class="form-control" />
              {% if error_history %}
                <div class="invalid-feedback">{{ error_history }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-inbox">{{ text_inbox }}</label>
            <div class="col-sm-10">
              <input type="text" name="inbox" value="{{ inbox }}" placeholder="{{ text_inbox }}" id="input-inbox" class="form-control" />
              {% if error_inbox %}
                <div class="invalid-feedback">{{ error_inbox }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-inbox_count">{{ text_inbox_count }}</label>
            <div class="col-sm-10">
              <input type="text" name="inbox_count" value="{{ inbox_count }}" placeholder="{{ text_inbox_count }}" id="input-inbox_count" class="form-control" />
              {% if error_inbox_count %}
                <div class="invalid-feedback">{{ error_inbox_count }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-message">{{ text_message }}</label>
            <div class="col-sm-10">
              <input type="text" name="message" value="{{ message }}" placeholder="{{ text_message }}" id="input-message" class="form-control" />
              {% if error_message %}
                <div class="invalid-feedback">{{ error_message }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-message_info">{{ text_message_info }}</label>
            <div class="col-sm-10">
              <input type="text" name="message_info" value="{{ message_info }}" placeholder="{{ text_message_info }}" id="input-message_info" class="form-control" />
              {% if error_message_info %}
                <div class="invalid-feedback">{{ error_message_info }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-messages">{{ text_messages }}</label>
            <div class="col-sm-10">
              <input type="text" name="messages" value="{{ messages }}" placeholder="{{ text_messages }}" id="input-messages" class="form-control" />
              {% if error_messages %}
                <div class="invalid-feedback">{{ error_messages }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-notification_count">{{ text_notification_count }}</label>
            <div class="col-sm-10">
              <input type="text" name="notification_count" value="{{ notification_count }}" placeholder="{{ text_notification_count }}" id="input-notification_count" class="form-control" />
              {% if error_notification_count %}
                <div class="invalid-feedback">{{ error_notification_count }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-notifications">{{ text_notifications }}</label>
            <div class="col-sm-10">
              <input type="text" name="notifications" value="{{ notifications }}" placeholder="{{ text_notifications }}" id="input-notifications" class="form-control" />
              {% if error_notifications %}
                <div class="invalid-feedback">{{ error_notifications }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-order">{{ text_order }}</label>
            <div class="col-sm-10">
              <input type="text" name="order" value="{{ order }}" placeholder="{{ text_order }}" id="input-order" class="form-control" />
              {% if error_order %}
                <div class="invalid-feedback">{{ error_order }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-pagination">{{ text_pagination }}</label>
            <div class="col-sm-10">
              <input type="text" name="pagination" value="{{ pagination }}" placeholder="{{ text_pagination }}" id="input-pagination" class="form-control" />
              {% if error_pagination %}
                <div class="invalid-feedback">{{ error_pagination }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-parent_id">{{ text_parent_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="parent_id" value="{{ parent_id }}" placeholder="{{ text_parent_id }}" id="input-parent_id" class="form-control" />
              {% if error_parent_id %}
                <div class="invalid-feedback">{{ error_parent_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-recipient">{{ text_recipient }}</label>
            <div class="col-sm-10">
              <input type="text" name="recipient" value="{{ recipient }}" placeholder="{{ text_recipient }}" id="input-recipient" class="form-control" />
              {% if error_recipient %}
                <div class="invalid-feedback">{{ error_recipient }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-reply">{{ text_reply }}</label>
            <div class="col-sm-10">
              <input type="text" name="reply" value="{{ reply }}" placeholder="{{ text_reply }}" id="input-reply" class="form-control" />
              {% if error_reply %}
                <div class="invalid-feedback">{{ error_reply }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-results">{{ text_results }}</label>
            <div class="col-sm-10">
              <input type="text" name="results" value="{{ results }}" placeholder="{{ text_results }}" id="input-results" class="form-control" />
              {% if error_results %}
                <div class="invalid-feedback">{{ error_results }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-selected">{{ text_selected }}</label>
            <div class="col-sm-10">
              <input type="text" name="selected" value="{{ selected }}" placeholder="{{ text_selected }}" id="input-selected" class="form-control" />
              {% if error_selected %}
                <div class="invalid-feedback">{{ error_selected }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sender">{{ text_sender }}</label>
            <div class="col-sm-10">
              <input type="text" name="sender" value="{{ sender }}" placeholder="{{ text_sender }}" id="input-sender" class="form-control" />
              {% if error_sender %}
                <div class="invalid-feedback">{{ error_sender }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sent">{{ text_sent }}</label>
            <div class="col-sm-10">
              <input type="text" name="sent" value="{{ sent }}" placeholder="{{ text_sent }}" id="input-sent" class="form-control" />
              {% if error_sent %}
                <div class="invalid-feedback">{{ error_sent }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sent_count">{{ text_sent_count }}</label>
            <div class="col-sm-10">
              <input type="text" name="sent_count" value="{{ sent_count }}" placeholder="{{ text_sent_count }}" id="input-sent_count" class="form-control" />
              {% if error_sent_count %}
                <div class="invalid-feedback">{{ error_sent_count }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort">{{ text_sort }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort" value="{{ sort }}" placeholder="{{ text_sort }}" id="input-sort" class="form-control" />
              {% if error_sort %}
                <div class="invalid-feedback">{{ error_sort }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_date_added">{{ text_sort_date_added }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_date_added" value="{{ sort_date_added }}" placeholder="{{ text_sort_date_added }}" id="input-sort_date_added" class="form-control" />
              {% if error_sort_date_added %}
                <div class="invalid-feedback">{{ error_sort_date_added }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_date_modified">{{ text_sort_date_modified }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_date_modified" value="{{ sort_date_modified }}" placeholder="{{ text_sort_date_modified }}" id="input-sort_date_modified" class="form-control" />
              {% if error_sort_date_modified %}
                <div class="invalid-feedback">{{ error_sort_date_modified }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_priority">{{ text_sort_priority }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_priority" value="{{ sort_priority }}" placeholder="{{ text_sort_priority }}" id="input-sort_priority" class="form-control" />
              {% if error_sort_priority %}
                <div class="invalid-feedback">{{ error_sort_priority }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_recipient">{{ text_sort_recipient }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_recipient" value="{{ sort_recipient }}" placeholder="{{ text_sort_recipient }}" id="input-sort_recipient" class="form-control" />
              {% if error_sort_recipient %}
                <div class="invalid-feedback">{{ error_sort_recipient }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_sender">{{ text_sort_sender }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_sender" value="{{ sort_sender }}" placeholder="{{ text_sort_sender }}" id="input-sort_sender" class="form-control" />
              {% if error_sort_sender %}
                <div class="invalid-feedback">{{ error_sort_sender }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_subject">{{ text_sort_subject }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_subject" value="{{ sort_subject }}" placeholder="{{ text_sort_subject }}" id="input-sort_subject" class="form-control" />
              {% if error_sort_subject %}
                <div class="invalid-feedback">{{ error_sort_subject }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-status">{{ text_status }}</label>
            <div class="col-sm-10">
              <input type="text" name="status" value="{{ status }}" placeholder="{{ text_status }}" id="input-status" class="form-control" />
              {% if error_status %}
                <div class="invalid-feedback">{{ error_status }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-subject">{{ text_subject }}</label>
            <div class="col-sm-10">
              <input type="text" name="subject" value="{{ subject }}" placeholder="{{ text_subject }}" id="input-subject" class="form-control" />
              {% if error_subject %}
                <div class="invalid-feedback">{{ error_subject }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_form">{{ text_text_form }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_form" value="{{ text_form }}" placeholder="{{ text_text_form }}" id="input-text_form" class="form-control" />
              {% if error_text_form %}
                <div class="invalid-feedback">{{ error_text_form }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="invalid-feedback">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-users">{{ text_users }}</label>
            <div class="col-sm-10">
              <input type="text" name="users" value="{{ users }}" placeholder="{{ text_users }}" id="input-users" class="form-control" />
              {% if error_users %}
                <div class="invalid-feedback">{{ error_users }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}