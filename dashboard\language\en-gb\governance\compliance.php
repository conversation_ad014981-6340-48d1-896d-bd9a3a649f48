<?php
// Heading
$_['heading_title'] = 'Commit Log';

// Text
$_['text_list'] = 'Commit List';
$_['text_modal_add'] = 'Add Commit Log';
$_['text_modal_edit'] = 'Modify Commit Log';
$_['text_confirm_delete'] = 'Are you sure you want to delete?';
$_['text_home'] = 'Home';

// Statuses
$_['text_all_statuses'] = '- All Statuses -';
$_['text_pending'] = 'Pending';
$_['text_submitted'] = 'Submitted';
$_['text_approved'] = 'Approved';
$_['text_rejected'] = 'Rejected';
$_['text_closed'] = 'Closed';

// Entry
$_['entry_status'] = 'Status';
$_['entry_compliance_type'] = 'Commitment Type';
$_['entry_reference_code'] = 'Reference Number';
$_['entry_description'] = 'Description';
$_['entry_due_date'] = 'Due Date';
$_['entry_responsible_user_id']= 'Responsible';

// Columns
$_['column_compliance_id'] = 'Number';
$_['column_compliance_type'] = 'Commitment Type';
$_['column_reference_code'] = 'Reference';
$_['column_due_date'] = 'Due Date';
$_['column_status'] = 'Status';
$_['column_responsible'] = 'Administrator';
$_['column_action'] = 'Action';

// Buttons
$_['button_filter'] = 'Filter';
$_['button_add'] = 'Add';
$_['button_edit'] = 'Edit';
$_['button_delete'] = 'Delete';
$_['button_save'] = 'Save';
$_['button_close'] = 'Close';