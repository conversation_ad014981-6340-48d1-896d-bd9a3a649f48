{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right"><a href="{{ add }}" data-toggle="tooltip" title="{{ button_add }}" class="btn btn-primary"><i class="fa fa-plus"></i></a>
        <button type="button" data-toggle="tooltip" title="{{ button_delete }}" class="btn btn-danger" onclick="confirm('{{ text_confirm }}') ? $('#form-length-class').submit() : false;"><i class="fa fa-trash-o"></i></button>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    {% if success %}
    <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-list"></i> {{ text_list }}</h3>
      </div>
      <div class="panel-body">
        <form action="{{ delete }}" method="post" enctype="multipart/form-data" id="form-length-class">
          <div class="table-responsive">
            <table class="table table-bordered table-hover">
              <thead>
                <tr>
                  <td style="width: 1px;" class="text-center"><input type="checkbox" onclick="$('input[name*=\'selected\']').prop('checked', this.checked);" /></td>
                  <td class="text-left">{% if sort == 'title' %}
                    <a href="{{ sort_title }}" class="{{ order|lower }}">{{ column_title }}</a>
                    {% else %}
                    <a href="{{ sort_title }}">{{ column_title }}</a>
                    {% endif %}</td>
                  <td class="text-left">{% if sort == 'unit' %}
                    <a href="{{ sort_unit }}" class="{{ order|lower }}">{{ column_unit }}</a>
                    {% else %}
                    <a href="{{ sort_unit }}">{{ column_unit }}</a>
                    {% endif %}</td>
                  <td class="text-right">{% if sort == 'value' %}
                    <a href="{{ sort_value }}" class="{{ order|lower }}">{{ column_value }}</a>
                    {% else %}
                    <a href="{{ sort_value }}">{{ column_value }}</a>
                    {% endif %}</td>
                  <td class="text-right">{{ column_action }}</td>
                </tr>
              </thead>
              <tbody>
                {% if length_classes %}
                {% for length_class in length_classes %}
                <tr>
                  <td class="text-center">{% if length_class.length_class_id in selected %}
                    <input type="checkbox" name="selected[]" value="{{ length_class.length_class_id }}" checked="checked" />
                    {% else %}
                    <input type="checkbox" name="selected[]" value="{{ length_class.length_class_id }}" />
                    {% endif %}</td>
                  <td class="text-left">{{ length_class.title }}</td>
                  <td class="text-left">{{ length_class.unit }}</td>
                  <td class="text-right">{{ length_class.value }}</td>
                  <td class="text-right"><a href="{{ length_class.edit }}" data-toggle="tooltip" title="{{ button_edit }}" class="btn btn-primary"><i class="fa fa-pencil"></i></a></td>
                </tr>
                {% endfor %}
                {% else %}
                <tr>
                  <td class="text-center" colspan="5">{{ text_no_results }}</td>
                </tr>
                {% endif %}
              </tbody>
            </table>
          </div>
        </form>
        <div class="row">
          <div class="col-sm-6 text-left">{{ pagination }}</div>
          <div class="col-sm-6 text-right">{{ results }}</div>
        </div>
      </div>
    </div>
  </div>
</div>
{{ footer }}