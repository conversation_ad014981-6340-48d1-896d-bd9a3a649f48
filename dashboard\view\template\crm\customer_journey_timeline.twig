{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="submit" form="crm\customer_journey-form" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fas fa-save"></i></button>
        <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-light"><i class="fas fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    
    <div class="card">
      <div class="card-header"><i class="fas fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}chart-bar{% elif screen_type == 'settings' %}cog{% else %}pencil-alt{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="card-body">
        <form id="crm\customer_journey-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-alternative_paths">{{ text_alternative_paths }}</label>
            <div class="col-sm-10">
              <input type="text" name="alternative_paths" value="{{ alternative_paths }}" placeholder="{{ text_alternative_paths }}" id="input-alternative_paths" class="form-control" />
              {% if error_alternative_paths %}
                <div class="invalid-feedback">{{ error_alternative_paths }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-analytics">{{ text_analytics }}</label>
            <div class="col-sm-10">
              <input type="text" name="analytics" value="{{ analytics }}" placeholder="{{ text_analytics }}" id="input-analytics" class="form-control" />
              {% if error_analytics %}
                <div class="invalid-feedback">{{ error_analytics }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-charts">{{ text_charts }}</label>
            <div class="col-sm-10">
              <input type="text" name="charts" value="{{ charts }}" placeholder="{{ text_charts }}" id="input-charts" class="form-control" />
              {% if error_charts %}
                <div class="invalid-feedback">{{ error_charts }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-create">{{ text_create }}</label>
            <div class="col-sm-10">
              <input type="text" name="create" value="{{ create }}" placeholder="{{ text_create }}" id="input-create" class="form-control" />
              {% if error_create %}
                <div class="invalid-feedback">{{ error_create }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-export">{{ text_export }}</label>
            <div class="col-sm-10">
              <input type="text" name="export" value="{{ export }}" placeholder="{{ text_export }}" id="input-export" class="form-control" />
              {% if error_export %}
                <div class="invalid-feedback">{{ error_export }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_assigned_to">{{ text_filter_assigned_to }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_assigned_to" value="{{ filter_assigned_to }}" placeholder="{{ text_filter_assigned_to }}" id="input-filter_assigned_to" class="form-control" />
              {% if error_filter_assigned_to %}
                <div class="invalid-feedback">{{ error_filter_assigned_to }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_customer">{{ text_filter_customer }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_customer" value="{{ filter_customer }}" placeholder="{{ text_filter_customer }}" id="input-filter_customer" class="form-control" />
              {% if error_filter_customer %}
                <div class="invalid-feedback">{{ error_filter_customer }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_health">{{ text_filter_health }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_health" value="{{ filter_health }}" placeholder="{{ text_filter_health }}" id="input-filter_health" class="form-control" />
              {% if error_filter_health %}
                <div class="invalid-feedback">{{ error_filter_health }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_stage">{{ text_filter_stage }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_stage" value="{{ filter_stage }}" placeholder="{{ text_filter_stage }}" id="input-filter_stage" class="form-control" />
              {% if error_filter_stage %}
                <div class="invalid-feedback">{{ error_filter_stage }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_touchpoint">{{ text_filter_touchpoint }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_touchpoint" value="{{ filter_touchpoint }}" placeholder="{{ text_filter_touchpoint }}" id="input-filter_touchpoint" class="form-control" />
              {% if error_filter_touchpoint %}
                <div class="invalid-feedback">{{ error_filter_touchpoint }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="invalid-feedback">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-health_levels">{{ text_health_levels }}</label>
            <div class="col-sm-10">
              <input type="text" name="health_levels" value="{{ health_levels }}" placeholder="{{ text_health_levels }}" id="input-health_levels" class="form-control" />
              {% if error_health_levels %}
                <div class="invalid-feedback">{{ error_health_levels }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-journey">{{ text_journey }}</label>
            <div class="col-sm-10">
              <input type="text" name="journey" value="{{ journey }}" placeholder="{{ text_journey }}" id="input-journey" class="form-control" />
              {% if error_journey %}
                <div class="invalid-feedback">{{ error_journey }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-journey_map">{{ text_journey_map }}</label>
            <div class="col-sm-10">
              <input type="text" name="journey_map" value="{{ journey_map }}" placeholder="{{ text_journey_map }}" id="input-journey_map" class="form-control" />
              {% if error_journey_map %}
                <div class="invalid-feedback">{{ error_journey_map }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-journey_statistics">{{ text_journey_statistics }}</label>
            <div class="col-sm-10">
              <input type="text" name="journey_statistics" value="{{ journey_statistics }}" placeholder="{{ text_journey_statistics }}" id="input-journey_statistics" class="form-control" />
              {% if error_journey_statistics %}
                <div class="invalid-feedback">{{ error_journey_statistics }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-journeys">{{ text_journeys }}</label>
            <div class="col-sm-10">
              <input type="text" name="journeys" value="{{ journeys }}" placeholder="{{ text_journeys }}" id="input-journeys" class="form-control" />
              {% if error_journeys %}
                <div class="invalid-feedback">{{ error_journeys }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-kpis">{{ text_kpis }}</label>
            <div class="col-sm-10">
              <input type="text" name="kpis" value="{{ kpis }}" placeholder="{{ text_kpis }}" id="input-kpis" class="form-control" />
              {% if error_kpis %}
                <div class="invalid-feedback">{{ error_kpis }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-milestones">{{ text_milestones }}</label>
            <div class="col-sm-10">
              <input type="text" name="milestones" value="{{ milestones }}" placeholder="{{ text_milestones }}" id="input-milestones" class="form-control" />
              {% if error_milestones %}
                <div class="invalid-feedback">{{ error_milestones }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-pagination">{{ text_pagination }}</label>
            <div class="col-sm-10">
              <input type="text" name="pagination" value="{{ pagination }}" placeholder="{{ text_pagination }}" id="input-pagination" class="form-control" />
              {% if error_pagination %}
                <div class="invalid-feedback">{{ error_pagination }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-results">{{ text_results }}</label>
            <div class="col-sm-10">
              <input type="text" name="results" value="{{ results }}" placeholder="{{ text_results }}" id="input-results" class="form-control" />
              {% if error_results %}
                <div class="invalid-feedback">{{ error_results }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-stages">{{ text_stages }}</label>
            <div class="col-sm-10">
              <input type="text" name="stages" value="{{ stages }}" placeholder="{{ text_stages }}" id="input-stages" class="form-control" />
              {% if error_stages %}
                <div class="invalid-feedback">{{ error_stages }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-statistics">{{ text_statistics }}</label>
            <div class="col-sm-10">
              <input type="text" name="statistics" value="{{ statistics }}" placeholder="{{ text_statistics }}" id="input-statistics" class="form-control" />
              {% if error_statistics %}
                <div class="invalid-feedback">{{ error_statistics }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-templates">{{ text_templates }}</label>
            <div class="col-sm-10">
              <input type="text" name="templates" value="{{ templates }}" placeholder="{{ text_templates }}" id="input-templates" class="form-control" />
              {% if error_templates %}
                <div class="invalid-feedback">{{ error_templates }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-timeline">{{ text_timeline }}</label>
            <div class="col-sm-10">
              <input type="text" name="timeline" value="{{ timeline }}" placeholder="{{ text_timeline }}" id="input-timeline" class="form-control" />
              {% if error_timeline %}
                <div class="invalid-feedback">{{ error_timeline }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-touchpoint_details">{{ text_touchpoint_details }}</label>
            <div class="col-sm-10">
              <input type="text" name="touchpoint_details" value="{{ touchpoint_details }}" placeholder="{{ text_touchpoint_details }}" id="input-touchpoint_details" class="form-control" />
              {% if error_touchpoint_details %}
                <div class="invalid-feedback">{{ error_touchpoint_details }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-touchpoints">{{ text_touchpoints }}</label>
            <div class="col-sm-10">
              <input type="text" name="touchpoints" value="{{ touchpoints }}" placeholder="{{ text_touchpoints }}" id="input-touchpoints" class="form-control" />
              {% if error_touchpoints %}
                <div class="invalid-feedback">{{ error_touchpoints }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="invalid-feedback">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-users">{{ text_users }}</label>
            <div class="col-sm-10">
              <input type="text" name="users" value="{{ users }}" placeholder="{{ text_users }}" id="input-users" class="form-control" />
              {% if error_users %}
                <div class="invalid-feedback">{{ error_users }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}