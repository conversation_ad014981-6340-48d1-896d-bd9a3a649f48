<?php
/**
 * نموذج تاريخ حركة المخزون المتقدم - Enterprise Grade
 * 
 * التحسينات الجديدة:
 * - تطبيق الخدمات المركزية (central_service_manager.php)
 * - نظام WAC (المتوسط المرجح للتكلفة) المتقدم
 * - تحليلات متقدمة للحركات
 * - تكامل مع النظام المحاسبي
 * - فلاتر ذكية وبحث متقدم
 * - تقارير تفصيلية
 * - معالجة الأخطاء الشاملة
 * - تحسين الأداء مع الفهرسة
 * - دعم الدفعات وتواريخ الانتهاء
 * - تتبع المستخدمين والأنشطة
 * 
 * <AUTHOR> Team - Enhanced by Kiro AI
 * @version 2.0 - Enterprise Grade
 * @since 2025
 * @reference current_stock.php - Proven Example
 */

class ModelInventoryMovementHistory extends Model {
    
    private $central_service;
    
    public function __construct($registry) {
        parent::__construct($registry);
        
        // تحميل الخدمات المركزية
        $this->load->model('common/central_service_manager');
        $this->central_service = new CentralServiceManager($this->registry);
    }
    
    /**
     * الحصول على حركات المخزون مع فلاتر متقدمة
     */
    public function getMovements($data = array()) {
        try {
            $sql = "SELECT 
                        sm.movement_id,
                        sm.product_id,
                        pd.name as product_name,
                        p.model,
                        p.sku,
                        sm.warehouse_id,
                        w.name as warehouse_name,
                        sm.movement_type,
                        sm.quantity,
                        sm.unit_cost,
                        sm.total_cost,
                        sm.reference_type,
                        sm.reference_id,
                        sm.batch_number,
                        sm.expiry_date,
                        sm.notes,
                        sm.user_id,
                        CONCAT(u.firstname, ' ', u.lastname) as user_name,
                        sm.date_added,
                        sm.date_modified,
                        
                        -- حساب الرصيد الجاري
                        (SELECT SUM(CASE 
                            WHEN sm2.movement_type IN ('in', 'adjustment_in', 'transfer_in', 'return_in') THEN sm2.quantity
                            WHEN sm2.movement_type IN ('out', 'adjustment_out', 'transfer_out', 'return_out') THEN -sm2.quantity
                            ELSE 0
                        END)
                        FROM " . DB_PREFIX . "stock_movement sm2 
                        WHERE sm2.product_id = sm.product_id 
                        AND sm2.warehouse_id = sm.warehouse_id 
                        AND sm2.date_added <= sm.date_added
                        AND sm2.movement_id <= sm.movement_id) as running_balance,
                        
                        -- حساب التكلفة المتوسطة المرجحة
                        (SELECT 
                            CASE 
                                WHEN SUM(CASE WHEN sm3.movement_type IN ('in', 'adjustment_in', 'transfer_in', 'return_in') THEN sm3.quantity ELSE 0 END) > 0
                                THEN SUM(CASE WHEN sm3.movement_type IN ('in', 'adjustment_in', 'transfer_in', 'return_in') THEN sm3.total_cost ELSE 0 END) / 
                                     SUM(CASE WHEN sm3.movement_type IN ('in', 'adjustment_in', 'transfer_in', 'return_in') THEN sm3.quantity ELSE 0 END)
                                ELSE 0
                            END
                        FROM " . DB_PREFIX . "stock_movement sm3 
                        WHERE sm3.product_id = sm.product_id 
                        AND sm3.warehouse_id = sm.warehouse_id 
                        AND sm3.date_added <= sm.date_added) as wac_cost
                        
                    FROM " . DB_PREFIX . "stock_movement sm
                    LEFT JOIN " . DB_PREFIX . "product p ON (sm.product_id = p.product_id)
                    LEFT JOIN " . DB_PREFIX . "product_description pd ON (p.product_id = pd.product_id AND pd.language_id = '" . (int)$this->config->get('config_language_id') . "')
                    LEFT JOIN " . DB_PREFIX . "warehouse w ON (sm.warehouse_id = w.warehouse_id)
                    LEFT JOIN " . DB_PREFIX . "user u ON (sm.user_id = u.user_id)
                    WHERE 1=1";
            
            // تطبيق الفلاتر
            if (!empty($data['filter_product_id'])) {
                $sql .= " AND sm.product_id = '" . (int)$data['filter_product_id'] . "'";
            }
            
            if (!empty($data['filter_product_name'])) {
                $sql .= " AND LCASE(pd.name) LIKE '%" . $this->db->escape(strtolower($data['filter_product_name'])) . "%'";
            }
            
            if (!empty($data['filter_sku'])) {
                $sql .= " AND LCASE(p.sku) LIKE '%" . $this->db->escape(strtolower($data['filter_sku'])) . "%'";
            }
            
            if (!empty($data['filter_warehouse_id'])) {
                $sql .= " AND sm.warehouse_id = '" . (int)$data['filter_warehouse_id'] . "'";
            }
            
            if (!empty($data['filter_movement_type'])) {
                $sql .= " AND sm.movement_type = '" . $this->db->escape($data['filter_movement_type']) . "'";
            }
            
            if (!empty($data['filter_reference_type'])) {
                $sql .= " AND sm.reference_type = '" . $this->db->escape($data['filter_reference_type']) . "'";
            }
            
            if (!empty($data['filter_reference_id'])) {
                $sql .= " AND sm.reference_id = '" . (int)$data['filter_reference_id'] . "'";
            }
            
            if (!empty($data['filter_batch_number'])) {
                $sql .= " AND sm.batch_number LIKE '%" . $this->db->escape($data['filter_batch_number']) . "%'";
            }
            
            if (!empty($data['filter_user_id'])) {
                $sql .= " AND sm.user_id = '" . (int)$data['filter_user_id'] . "'";
            }
            
            if (!empty($data['filter_date_start'])) {
                $sql .= " AND DATE(sm.date_added) >= '" . $this->db->escape($data['filter_date_start']) . "'";
            }
            
            if (!empty($data['filter_date_end'])) {
                $sql .= " AND DATE(sm.date_added) <= '" . $this->db->escape($data['filter_date_end']) . "'";
            }
            
            if (!empty($data['filter_quantity_min'])) {
                $sql .= " AND sm.quantity >= '" . (float)$data['filter_quantity_min'] . "'";
            }
            
            if (!empty($data['filter_quantity_max'])) {
                $sql .= " AND sm.quantity <= '" . (float)$data['filter_quantity_max'] . "'";
            }
            
            if (!empty($data['filter_cost_min'])) {
                $sql .= " AND sm.unit_cost >= '" . (float)$data['filter_cost_min'] . "'";
            }
            
            if (!empty($data['filter_cost_max'])) {
                $sql .= " AND sm.unit_cost <= '" . (float)$data['filter_cost_max'] . "'";
            }
            
            // ترتيب النتائج
            $sort_data = array(
                'pd.name',
                'p.model',
                'p.sku',
                'w.name',
                'sm.movement_type',
                'sm.quantity',
                'sm.unit_cost',
                'sm.total_cost',
                'sm.reference_type',
                'sm.batch_number',
                'user_name',
                'sm.date_added'
            );
            
            if (isset($data['sort']) && in_array($data['sort'], $sort_data)) {
                $sql .= " ORDER BY " . $data['sort'];
            } else {
                $sql .= " ORDER BY sm.date_added";
            }
            
            if (isset($data['order']) && ($data['order'] == 'ASC')) {
                $sql .= " ASC";
            } else {
                $sql .= " DESC";
            }
            
            // تحديد النطاق
            if (isset($data['start']) || isset($data['limit'])) {
                if ($data['start'] < 0) {
                    $data['start'] = 0;
                }
                
                if ($data['limit'] < 1) {
                    $data['limit'] = 20;
                }
                
                $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
            }
            
            $query = $this->db->query($sql);
            
            return $query->rows;
            
        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'movement_history_model',
                'خطأ في الحصول على حركات المخزون: ' . $e->getMessage(),
                array('error' => $e->getTraceAsString())
            );
            
            return array();
        }
    }
    
    /**
     * الحصول على إجمالي عدد الحركات
     */
    public function getTotalMovements($data = array()) {
        try {
            $sql = "SELECT COUNT(*) AS total 
                    FROM " . DB_PREFIX . "stock_movement sm
                    LEFT JOIN " . DB_PREFIX . "product p ON (sm.product_id = p.product_id)
                    LEFT JOIN " . DB_PREFIX . "product_description pd ON (p.product_id = pd.product_id AND pd.language_id = '" . (int)$this->config->get('config_language_id') . "')
                    LEFT JOIN " . DB_PREFIX . "warehouse w ON (sm.warehouse_id = w.warehouse_id)
                    LEFT JOIN " . DB_PREFIX . "user u ON (sm.user_id = u.user_id)
                    WHERE 1=1";
            
            // تطبيق نفس الفلاتر
            if (!empty($data['filter_product_id'])) {
                $sql .= " AND sm.product_id = '" . (int)$data['filter_product_id'] . "'";
            }
            
            if (!empty($data['filter_product_name'])) {
                $sql .= " AND LCASE(pd.name) LIKE '%" . $this->db->escape(strtolower($data['filter_product_name'])) . "%'";
            }
            
            if (!empty($data['filter_sku'])) {
                $sql .= " AND LCASE(p.sku) LIKE '%" . $this->db->escape(strtolower($data['filter_sku'])) . "%'";
            }
            
            if (!empty($data['filter_warehouse_id'])) {
                $sql .= " AND sm.warehouse_id = '" . (int)$data['filter_warehouse_id'] . "'";
            }
            
            if (!empty($data['filter_movement_type'])) {
                $sql .= " AND sm.movement_type = '" . $this->db->escape($data['filter_movement_type']) . "'";
            }
            
            if (!empty($data['filter_reference_type'])) {
                $sql .= " AND sm.reference_type = '" . $this->db->escape($data['filter_reference_type']) . "'";
            }
            
            if (!empty($data['filter_reference_id'])) {
                $sql .= " AND sm.reference_id = '" . (int)$data['filter_reference_id'] . "'";
            }
            
            if (!empty($data['filter_batch_number'])) {
                $sql .= " AND sm.batch_number LIKE '%" . $this->db->escape($data['filter_batch_number']) . "%'";
            }
            
            if (!empty($data['filter_user_id'])) {
                $sql .= " AND sm.user_id = '" . (int)$data['filter_user_id'] . "'";
            }
            
            if (!empty($data['filter_date_start'])) {
                $sql .= " AND DATE(sm.date_added) >= '" . $this->db->escape($data['filter_date_start']) . "'";
            }
            
            if (!empty($data['filter_date_end'])) {
                $sql .= " AND DATE(sm.date_added) <= '" . $this->db->escape($data['filter_date_end']) . "'";
            }
            
            if (!empty($data['filter_quantity_min'])) {
                $sql .= " AND sm.quantity >= '" . (float)$data['filter_quantity_min'] . "'";
            }
            
            if (!empty($data['filter_quantity_max'])) {
                $sql .= " AND sm.quantity <= '" . (float)$data['filter_quantity_max'] . "'";
            }
            
            if (!empty($data['filter_cost_min'])) {
                $sql .= " AND sm.unit_cost >= '" . (float)$data['filter_cost_min'] . "'";
            }
            
            if (!empty($data['filter_cost_max'])) {
                $sql .= " AND sm.unit_cost <= '" . (float)$data['filter_cost_max'] . "'";
            }
            
            $query = $this->db->query($sql);
            
            return $query->row['total'];
            
        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'movement_history_model',
                'خطأ في حساب إجمالي الحركات: ' . $e->getMessage(),
                array('error' => $e->getTraceAsString())
            );
            
            return 0;
        }
    }

    /**
     * الحصول على ملخص الحركات للتحليلات
     */
    public function getMovementSummary($data = array()) {
        try {
            $sql = "SELECT
                        COUNT(*) as total_movements,
                        SUM(CASE WHEN movement_type IN ('in', 'adjustment_in', 'transfer_in', 'return_in') THEN quantity ELSE 0 END) as total_in,
                        SUM(CASE WHEN movement_type IN ('out', 'adjustment_out', 'transfer_out', 'return_out') THEN quantity ELSE 0 END) as total_out,
                        SUM(CASE WHEN movement_type IN ('in', 'adjustment_in', 'transfer_in', 'return_in') THEN total_cost ELSE 0 END) as total_cost_in,
                        SUM(CASE WHEN movement_type IN ('out', 'adjustment_out', 'transfer_out', 'return_out') THEN total_cost ELSE 0 END) as total_cost_out,
                        AVG(unit_cost) as avg_unit_cost,
                        MIN(date_added) as first_movement,
                        MAX(date_added) as last_movement,
                        COUNT(DISTINCT product_id) as unique_products,
                        COUNT(DISTINCT warehouse_id) as unique_warehouses,
                        COUNT(DISTINCT user_id) as unique_users
                    FROM " . DB_PREFIX . "stock_movement
                    WHERE 1=1";

            // تطبيق فلاتر التاريخ إذا وجدت
            if (!empty($data['filter_date_start'])) {
                $sql .= " AND DATE(date_added) >= '" . $this->db->escape($data['filter_date_start']) . "'";
            }

            if (!empty($data['filter_date_end'])) {
                $sql .= " AND DATE(date_added) <= '" . $this->db->escape($data['filter_date_end']) . "'";
            }

            $query = $this->db->query($sql);

            return $query->row;

        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'movement_history_model',
                'خطأ في الحصول على ملخص الحركات: ' . $e->getMessage(),
                array('error' => $e->getTraceAsString())
            );

            return array();
        }
    }

    /**
     * الحصول على الاتجاهات اليومية
     */
    public function getDailyTrends($days = 30) {
        try {
            $sql = "SELECT
                        DATE(date_added) as movement_date,
                        COUNT(*) as total_movements,
                        SUM(CASE WHEN movement_type IN ('in', 'adjustment_in', 'transfer_in', 'return_in') THEN quantity ELSE 0 END) as total_in,
                        SUM(CASE WHEN movement_type IN ('out', 'adjustment_out', 'transfer_out', 'return_out') THEN quantity ELSE 0 END) as total_out,
                        SUM(CASE WHEN movement_type IN ('in', 'adjustment_in', 'transfer_in', 'return_in') THEN total_cost ELSE 0 END) as cost_in,
                        SUM(CASE WHEN movement_type IN ('out', 'adjustment_out', 'transfer_out', 'return_out') THEN total_cost ELSE 0 END) as cost_out
                    FROM " . DB_PREFIX . "stock_movement
                    WHERE DATE(date_added) >= DATE_SUB(CURDATE(), INTERVAL " . (int)$days . " DAY)
                    GROUP BY DATE(date_added)
                    ORDER BY movement_date DESC";

            $query = $this->db->query($sql);

            return $query->rows;

        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'movement_history_model',
                'خطأ في الحصول على الاتجاهات اليومية: ' . $e->getMessage(),
                array('error' => $e->getTraceAsString())
            );

            return array();
        }
    }

    /**
     * تحليل المنتجات الأكثر حركة
     */
    public function getProductAnalysis($limit = 20) {
        try {
            $sql = "SELECT
                        sm.product_id,
                        pd.name as product_name,
                        p.model,
                        p.sku,
                        COUNT(*) as total_movements,
                        SUM(CASE WHEN sm.movement_type IN ('in', 'adjustment_in', 'transfer_in', 'return_in') THEN sm.quantity ELSE 0 END) as total_in,
                        SUM(CASE WHEN sm.movement_type IN ('out', 'adjustment_out', 'transfer_out', 'return_out') THEN sm.quantity ELSE 0 END) as total_out,
                        SUM(CASE WHEN sm.movement_type IN ('in', 'adjustment_in', 'transfer_in', 'return_in') THEN sm.total_cost ELSE 0 END) as cost_in,
                        SUM(CASE WHEN sm.movement_type IN ('out', 'adjustment_out', 'transfer_out', 'return_out') THEN sm.total_cost ELSE 0 END) as cost_out,
                        AVG(sm.unit_cost) as avg_unit_cost,
                        MAX(sm.date_added) as last_movement
                    FROM " . DB_PREFIX . "stock_movement sm
                    LEFT JOIN " . DB_PREFIX . "product p ON (sm.product_id = p.product_id)
                    LEFT JOIN " . DB_PREFIX . "product_description pd ON (p.product_id = pd.product_id AND pd.language_id = '" . (int)$this->config->get('config_language_id') . "')
                    WHERE DATE(sm.date_added) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
                    GROUP BY sm.product_id
                    ORDER BY total_movements DESC
                    LIMIT " . (int)$limit;

            $query = $this->db->query($sql);

            return $query->rows;

        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'movement_history_model',
                'خطأ في تحليل المنتجات: ' . $e->getMessage(),
                array('error' => $e->getTraceAsString())
            );

            return array();
        }
    }

    /**
     * تحليل المخازن
     */
    public function getWarehouseAnalysis($limit = 10) {
        try {
            $sql = "SELECT
                        sm.warehouse_id,
                        w.name as warehouse_name,
                        COUNT(*) as total_movements,
                        SUM(CASE WHEN sm.movement_type IN ('in', 'adjustment_in', 'transfer_in', 'return_in') THEN sm.quantity ELSE 0 END) as total_in,
                        SUM(CASE WHEN sm.movement_type IN ('out', 'adjustment_out', 'transfer_out', 'return_out') THEN sm.quantity ELSE 0 END) as total_out,
                        SUM(CASE WHEN sm.movement_type IN ('in', 'adjustment_in', 'transfer_in', 'return_in') THEN sm.total_cost ELSE 0 END) as cost_in,
                        SUM(CASE WHEN sm.movement_type IN ('out', 'adjustment_out', 'transfer_out', 'return_out') THEN sm.total_cost ELSE 0 END) as cost_out,
                        COUNT(DISTINCT sm.product_id) as unique_products,
                        COUNT(DISTINCT sm.user_id) as unique_users
                    FROM " . DB_PREFIX . "stock_movement sm
                    LEFT JOIN " . DB_PREFIX . "warehouse w ON (sm.warehouse_id = w.warehouse_id)
                    WHERE DATE(sm.date_added) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
                    GROUP BY sm.warehouse_id
                    ORDER BY total_movements DESC
                    LIMIT " . (int)$limit;

            $query = $this->db->query($sql);

            return $query->rows;

        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'movement_history_model',
                'خطأ في تحليل المخازن: ' . $e->getMessage(),
                array('error' => $e->getTraceAsString())
            );

            return array();
        }
    }

    /**
     * تحليل أنواع الحركات
     */
    public function getMovementTypeAnalysis() {
        try {
            $sql = "SELECT
                        movement_type,
                        COUNT(*) as total_count,
                        SUM(quantity) as total_quantity,
                        SUM(total_cost) as total_cost,
                        AVG(unit_cost) as avg_unit_cost,
                        MIN(date_added) as first_occurrence,
                        MAX(date_added) as last_occurrence
                    FROM " . DB_PREFIX . "stock_movement
                    WHERE DATE(date_added) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
                    GROUP BY movement_type
                    ORDER BY total_count DESC";

            $query = $this->db->query($sql);

            return $query->rows;

        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'movement_history_model',
                'خطأ في تحليل أنواع الحركات: ' . $e->getMessage(),
                array('error' => $e->getTraceAsString())
            );

            return array();
        }
    }

    /**
     * البحث المتقدم
     */
    public function advancedSearch($criteria) {
        try {
            $sql = "SELECT sm.*, pd.name as product_name, p.model, p.sku, w.name as warehouse_name,
                           CONCAT(u.firstname, ' ', u.lastname) as user_name
                    FROM " . DB_PREFIX . "stock_movement sm
                    LEFT JOIN " . DB_PREFIX . "product p ON (sm.product_id = p.product_id)
                    LEFT JOIN " . DB_PREFIX . "product_description pd ON (p.product_id = pd.product_id AND pd.language_id = '" . (int)$this->config->get('config_language_id') . "')
                    LEFT JOIN " . DB_PREFIX . "warehouse w ON (sm.warehouse_id = w.warehouse_id)
                    LEFT JOIN " . DB_PREFIX . "user u ON (sm.user_id = u.user_id)
                    WHERE 1=1";

            if (!empty($criteria['product_name'])) {
                $sql .= " AND LCASE(pd.name) LIKE '%" . $this->db->escape(strtolower($criteria['product_name'])) . "%'";
            }

            if (!empty($criteria['movement_type'])) {
                $sql .= " AND sm.movement_type = '" . $this->db->escape($criteria['movement_type']) . "'";
            }

            $sql .= " ORDER BY sm.date_added DESC LIMIT 100";

            $query = $this->db->query($sql);
            return $query->rows;

        } catch (Exception $e) {
            $this->central_service->logActivity('error', 'movement_history_model', 'خطأ في البحث المتقدم: ' . $e->getMessage());
            return array();
        }
    }

    /**
     * الحصول على تفاصيل حركة واحدة
     */
    public function getMovement($movement_id) {
        try {
            $sql = "SELECT sm.*, pd.name as product_name, p.model, p.sku, w.name as warehouse_name,
                           CONCAT(u.firstname, ' ', u.lastname) as user_name
                    FROM " . DB_PREFIX . "stock_movement sm
                    LEFT JOIN " . DB_PREFIX . "product p ON (sm.product_id = p.product_id)
                    LEFT JOIN " . DB_PREFIX . "product_description pd ON (p.product_id = pd.product_id AND pd.language_id = '" . (int)$this->config->get('config_language_id') . "')
                    LEFT JOIN " . DB_PREFIX . "warehouse w ON (sm.warehouse_id = w.warehouse_id)
                    LEFT JOIN " . DB_PREFIX . "user u ON (sm.user_id = u.user_id)
                    WHERE sm.movement_id = '" . (int)$movement_id . "'";

            $query = $this->db->query($sql);
            return $query->row;

        } catch (Exception $e) {
            $this->central_service->logActivity('error', 'movement_history_model', 'خطأ في الحصول على تفاصيل الحركة: ' . $e->getMessage());
            return array();
        }
    }
}
