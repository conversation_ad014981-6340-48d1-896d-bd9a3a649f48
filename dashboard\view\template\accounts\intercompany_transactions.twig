{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        {% if has_permission %}
        <button type="button" id="button-add" data-toggle="tooltip" title="{{ button_add }}" class="btn btn-primary">
          <i class="fa fa-plus"></i> {{ button_add }}
        </button>
        <button type="button" id="button-match" data-toggle="tooltip" title="{{ button_match }}" class="btn btn-info">
          <i class="fa fa-link"></i> {{ button_match }}
        </button>
        <button type="button" id="button-eliminate" data-toggle="tooltip" title="{{ button_eliminate }}" class="btn btn-warning">
          <i class="fa fa-minus-circle"></i> {{ button_eliminate }}
        </button>
        {% endif %}
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    {% if success %}
    <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    
    <!-- Statistics Dashboard -->
    <div class="row">
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-primary">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-exchange fa-5x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ total_transactions }}</div>
                <div>{{ text_total_transactions }}</div>
              </div>
            </div>
          </div>
          <div class="panel-footer">
            <span class="pull-left">{{ text_this_month }}</span>
            <span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
            <div class="clearfix"></div>
          </div>
        </div>
      </div>
      
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-green">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-link fa-5x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ matched_transactions }}</div>
                <div>{{ text_matched_transactions }}</div>
              </div>
            </div>
          </div>
          <div class="panel-footer">
            <span class="pull-left">{{ text_auto_matched }}</span>
            <span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
            <div class="clearfix"></div>
          </div>
        </div>
      </div>
      
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-yellow">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-exclamation-triangle fa-5x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ unmatched_transactions }}</div>
                <div>{{ text_unmatched_transactions }}</div>
              </div>
            </div>
          </div>
          <div class="panel-footer">
            <span class="pull-left">{{ text_requires_attention }}</span>
            <span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
            <div class="clearfix"></div>
          </div>
        </div>
      </div>
      
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-red">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-minus-circle fa-5x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ eliminated_amount }}</div>
                <div>{{ text_eliminated_amount }}</div>
              </div>
            </div>
          </div>
          <div class="panel-footer">
            <span class="pull-left">{{ text_this_period }}</span>
            <span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
            <div class="clearfix"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Filters -->
    <div class="row">
      <div class="col-md-12">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-filter"></i> {{ text_filters }}</h3>
          </div>
          <div class="panel-body">
            <div class="row">
              <div class="col-md-3">
                <div class="form-group">
                  <label for="filter-company">{{ entry_company }}</label>
                  <select name="filter_company" id="filter-company" class="form-control">
                    <option value="">{{ text_all_companies }}</option>
                    {% for company in companies %}
                    <option value="{{ company.company_id }}" {% if company.company_id == filter_company %}selected="selected"{% endif %}>{{ company.name }}</option>
                    {% endfor %}
                  </select>
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label for="filter-type">{{ entry_transaction_type }}</label>
                  <select name="filter_type" id="filter-type" class="form-control">
                    <option value="">{{ text_all_types }}</option>
                    <option value="sale" {% if filter_type == 'sale' %}selected="selected"{% endif %}>{{ text_sale }}</option>
                    <option value="purchase" {% if filter_type == 'purchase' %}selected="selected"{% endif %}>{{ text_purchase }}</option>
                    <option value="loan" {% if filter_type == 'loan' %}selected="selected"{% endif %}>{{ text_loan }}</option>
                    <option value="dividend" {% if filter_type == 'dividend' %}selected="selected"{% endif %}>{{ text_dividend }}</option>
                    <option value="management_fee" {% if filter_type == 'management_fee' %}selected="selected"{% endif %}>{{ text_management_fee }}</option>
                  </select>
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label for="filter-status">{{ entry_status }}</label>
                  <select name="filter_status" id="filter-status" class="form-control">
                    <option value="">{{ text_all_statuses }}</option>
                    <option value="unmatched" {% if filter_status == 'unmatched' %}selected="selected"{% endif %}>{{ text_unmatched }}</option>
                    <option value="matched" {% if filter_status == 'matched' %}selected="selected"{% endif %}>{{ text_matched }}</option>
                    <option value="eliminated" {% if filter_status == 'eliminated' %}selected="selected"{% endif %}>{{ text_eliminated }}</option>
                  </select>
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>&nbsp;</label>
                  <div>
                    <button type="button" id="button-filter" class="btn btn-primary">
                      <i class="fa fa-search"></i> {{ button_filter }}
                    </button>
                    <button type="button" id="button-clear" class="btn btn-default">
                      <i class="fa fa-refresh"></i> {{ button_clear }}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Transactions List -->
    <div class="row">
      <div class="col-md-12">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-list"></i> {{ text_transactions_list }}</h3>
          </div>
          <div class="panel-body">
            <form id="form-transaction">
              <div class="table-responsive">
                <table class="table table-striped table-hover">
                  <thead>
                    <tr>
                      <th width="1"><input type="checkbox" onclick="$('input[name*=\'selected\']').prop('checked', this.checked);" /></th>
                      <th>{{ column_reference }}</th>
                      <th>{{ column_company }}</th>
                      <th>{{ column_related_company }}</th>
                      <th>{{ column_type }}</th>
                      <th>{{ column_date }}</th>
                      <th>{{ column_amount }}</th>
                      <th>{{ column_status }}</th>
                      <th>{{ column_action }}</th>
                    </tr>
                  </thead>
                  <tbody>
                    {% if transactions %}
                    {% for transaction in transactions %}
                    <tr>
                      <td><input type="checkbox" name="selected[]" value="{{ transaction.transaction_id }}" /></td>
                      <td>{{ transaction.reference }}</td>
                      <td>{{ transaction.company_name }}</td>
                      <td>{{ transaction.related_company_name }}</td>
                      <td>
                        <span class="label label-info">{{ transaction.type_text }}</span>
                      </td>
                      <td>{{ transaction.transaction_date }}</td>
                      <td class="text-right">{{ transaction.amount_formatted }}</td>
                      <td>
                        {% if transaction.status == 'eliminated' %}
                        <span class="label label-success">{{ transaction.status_text }}</span>
                        {% elseif transaction.status == 'matched' %}
                        <span class="label label-info">{{ transaction.status_text }}</span>
                        {% else %}
                        <span class="label label-warning">{{ transaction.status_text }}</span>
                        {% endif %}
                      </td>
                      <td>
                        <div class="btn-group">
                          <a href="{{ transaction.view }}" class="btn btn-sm btn-info" title="{{ button_view }}">
                            <i class="fa fa-eye"></i>
                          </a>
                          {% if transaction.edit %}
                          <a href="{{ transaction.edit }}" class="btn btn-sm btn-primary" title="{{ button_edit }}">
                            <i class="fa fa-pencil"></i>
                          </a>
                          {% endif %}
                          {% if transaction.match %}
                          <button type="button" class="btn btn-sm btn-success btn-match" data-id="{{ transaction.transaction_id }}" title="{{ button_match }}">
                            <i class="fa fa-link"></i>
                          </button>
                          {% endif %}
                          {% if transaction.eliminate %}
                          <button type="button" class="btn btn-sm btn-warning btn-eliminate" data-id="{{ transaction.transaction_id }}" title="{{ button_eliminate }}">
                            <i class="fa fa-minus-circle"></i>
                          </button>
                          {% endif %}
                        </div>
                      </td>
                    </tr>
                    {% endfor %}
                    {% else %}
                    <tr>
                      <td colspan="9" class="text-center">{{ text_no_results }}</td>
                    </tr>
                    {% endif %}
                  </tbody>
                </table>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>

    <!-- Matching Panel -->
    <div class="row">
      <div class="col-md-6">
        <div class="panel panel-info">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-link"></i> {{ text_auto_matching }}</h3>
          </div>
          <div class="panel-body">
            <p>{{ text_auto_matching_help }}</p>
            <div class="form-group">
              <label for="tolerance-amount">{{ entry_tolerance_amount }}</label>
              <div class="input-group">
                <input type="number" step="0.01" name="tolerance_amount" value="0.00" id="tolerance-amount" class="form-control" />
                <span class="input-group-addon">{{ base_currency }}</span>
              </div>
            </div>
            <button type="button" id="button-auto-match" class="btn btn-info btn-block">
              <i class="fa fa-magic"></i> {{ button_auto_match }}
            </button>
          </div>
        </div>
      </div>
      
      <div class="col-md-6">
        <div class="panel panel-warning">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-minus-circle"></i> {{ text_elimination }}</h3>
          </div>
          <div class="panel-body">
            <p>{{ text_elimination_help }}</p>
            <div class="form-group">
              <label for="elimination-method">{{ entry_elimination_method }}</label>
              <select name="elimination_method" id="elimination-method" class="form-control">
                <option value="full">{{ text_full_elimination }}</option>
                <option value="partial">{{ text_partial_elimination }}</option>
                <option value="proportional">{{ text_proportional_elimination }}</option>
              </select>
            </div>
            <button type="button" id="button-eliminate-selected" class="btn btn-warning btn-block">
              <i class="fa fa-minus-circle"></i> {{ button_eliminate_selected }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Pagination -->
    <div class="row">
      <div class="col-sm-6 text-left">{{ pagination }}</div>
      <div class="col-sm-6 text-right">{{ results }}</div>
    </div>
  </div>
</div>

<script type="text/javascript">
$(document).ready(function() {
    // Initialize tooltips
    $('[data-toggle="tooltip"]').tooltip();
    
    // Filter functionality
    $('#button-filter').on('click', function() {
        var url = 'index.php?route=accounts/intercompany_transactions&token={{ token }}';
        
        var filter_company = $('#filter-company').val();
        if (filter_company) {
            url += '&filter_company=' + encodeURIComponent(filter_company);
        }
        
        var filter_type = $('#filter-type').val();
        if (filter_type) {
            url += '&filter_type=' + encodeURIComponent(filter_type);
        }
        
        var filter_status = $('#filter-status').val();
        if (filter_status) {
            url += '&filter_status=' + encodeURIComponent(filter_status);
        }
        
        location = url;
    });
    
    // Clear filters
    $('#button-clear').on('click', function() {
        location = 'index.php?route=accounts/intercompany_transactions&token={{ token }}';
    });
    
    // Auto matching
    $('#button-auto-match').on('click', function() {
        var tolerance = $('#tolerance-amount').val();
        
        $.ajax({
            url: 'index.php?route=accounts/intercompany_transactions/autoMatch&token={{ token }}',
            type: 'post',
            data: {tolerance_amount: tolerance},
            dataType: 'json',
            beforeSend: function() {
                $('#button-auto-match').button('loading');
            },
            complete: function() {
                $('#button-auto-match').button('reset');
            },
            success: function(json) {
                if (json.success) {
                    location.reload();
                } else {
                    alert(json.error);
                }
            }
        });
    });
    
    // Eliminate selected transactions
    $('#button-eliminate-selected').on('click', function() {
        var selected = [];
        $('input[name*="selected"]:checked').each(function() {
            selected.push($(this).val());
        });
        
        if (selected.length == 0) {
            alert('{{ text_no_selection }}');
            return;
        }
        
        if (confirm('{{ text_confirm_eliminate }}')) {
            var method = $('#elimination-method').val();
            
            $.ajax({
                url: 'index.php?route=accounts/intercompany_transactions/eliminate&token={{ token }}',
                type: 'post',
                data: {selected: selected, method: method},
                dataType: 'json',
                beforeSend: function() {
                    $('#button-eliminate-selected').button('loading');
                },
                complete: function() {
                    $('#button-eliminate-selected').button('reset');
                },
                success: function(json) {
                    if (json.success) {
                        location.reload();
                    } else {
                        alert(json.error);
                    }
                }
            });
        }
    });
});
</script>

{{ footer }}
