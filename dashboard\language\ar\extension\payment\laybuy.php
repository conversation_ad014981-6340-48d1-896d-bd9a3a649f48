<?php
// Heading
$_['heading_title']                 = 'Lay-Buy';
$_['heading_transaction_title']     = 'Transaction';

// Tab
$_['tab_settings']                  = 'Settings';
$_['tab_reports']                   = 'Reports';
$_['tab_reference']                 = 'Reference Information';
$_['tab_customer']                  = 'Customer Information';
$_['tab_payment']                   = 'Payment Plan';
$_['tab_modify']                    = 'Modify Plan';

// Text
$_['text_extension']                = 'الموديولات';
$_['text_success']                  = 'Success: You have modified Lay-Buy payment module!';
$_['text_edit']                     = 'Edit Lay-Buy';
$_['text_laybuy']                   = '<a href="https://www.lay-buys.com" target="_blank"><img src="view/image/payment/laybuys.png" style="width:94px;height:25px" alt="Lay-Buys" title="Lay-Buys"></a>';
$_['text_cancel_success']           = 'Transaction was canceled successfully.';
$_['text_cancel_failure']           = 'Cancel request was unsuccessful. Please try again!';
$_['text_revise_success']           = 'Revise request was successful.';
$_['text_revise_failure']           = 'Revise request was unsuccessful. Please try again!';
$_['text_type_laybuy']              = 'Lay-Buy';
$_['text_type_buynow']              = 'Buy-Now';
$_['text_status_1']                 = 'Pending';
$_['text_status_5']                 = 'Completed';
$_['text_status_7']                 = 'Canceled';
$_['text_status_50']                = 'Revise Requested';
$_['text_status_51']                = 'Revised';
$_['text_fetched_some']             = 'Successfully updated %d transaction(s)';
$_['text_fetched_none']             = 'There are no transactions to update.';
$_['text_transaction_details']      = 'Transaction Details';
$_['text_not_found']                = 'There is no transaction with that ID.';
$_['text_paypal_profile_id']        = 'PayPal Profile ID';
$_['text_laybuy_ref_no']            = 'Lay-Buy Reference ID';
$_['text_order_id']                 = 'Order ID';
$_['text_status']                   = 'Status';
$_['text_amount']                   = 'Amount';
$_['text_downpayment_percent']      = 'Down Payment Percent';
$_['text_month']                    = 'Month';
$_['text_months']                   = 'Months';
$_['text_downpayment_amount']       = 'Down Payment Amount';
$_['text_payment_amounts']          = 'Payment Amounts';
$_['text_first_payment_due']        = 'First Payment Due';
$_['text_last_payment_due']         = 'Last Payment Due';
$_['text_instalment']               = 'Instalment';
$_['text_date']                     = 'Date';
$_['text_pp_trans_id']              = 'PayPal Transaction ID';
$_['text_downpayment']              = 'Down Payment';
$_['text_report']                   = 'Payment Record';
$_['text_revise_plan']              = 'Revise Plan';
$_['text_today']                    = 'Today';
$_['text_due_date']                 = 'Due Date';
$_['text_cancel_plan']              = 'Cancel Plan';
$_['text_firstname']                = 'First Name';
$_['text_lastname']                 = 'Last Name';
$_['text_email']                    = 'E-Mail';
$_['text_address']                  = 'Address';
$_['text_suburb']                   = 'Suburb';
$_['text_state']                    = 'State';
$_['text_country']                  = 'Country';
$_['text_postcode']                 = 'Postcode';
$_['text_payment_info']		     	= 'Payment Information';
$_['text_no_cron_time']             = 'The cron has not yet been executed';
$_['text_comment'] 	                = 'Updated by Lay-Buy';
$_['text_comment_canceled'] 	    = 'Order canceled and recurring PayPal Profile #%s canceled.';
$_['text_remaining'] 	            = 'Remaining:';
$_['text_payment'] 	                = 'Payment';

// Column
$_['column_order_id']               = 'Order ID';
$_['column_customer']               = 'Customer';
$_['column_amount']                 = 'Amount';
$_['column_dp_percent']             = 'Down Payment Percent';
$_['column_months']                 = 'Months';
$_['column_dp_amount']              = 'Down Payment Amount';
$_['column_first_payment']          = 'First Payment Due';
$_['column_last_payment']           = 'Last Payment Due';
$_['column_status']                 = 'Status';
$_['column_date_added']             = 'Date Added';
$_['column_action']                 = 'Action';

// Entry
$_['entry_membership_id']           = 'Lay-Buys Membership ID';
$_['entry_token']                   = 'Secret Token';
$_['entry_minimum']                 = 'Minimum Down Payment (%)';
$_['entry_maximum']                 = 'Maximum Down Payment (%)';
$_['entry_max_months']              = 'Months';
$_['entry_category']                = 'Allowed Categories';
$_['entry_product_ids']             = 'Excluded Product IDs';
$_['entry_customer_group']          = 'Allowed Customer Groups';
$_['entry_logging']                 = 'Debug Logging';
$_['entry_total']                   = 'Total';
$_['entry_order_status_pending']    = 'Order Status (Pending)';
$_['entry_order_status_canceled']   = 'Order Status (Canceled)';
$_['entry_order_status_processing'] = 'Order Status (Processing)';
$_['entry_gateway_url']             = 'Gateway URL';
$_['entry_api_url']                 = 'API URL';
$_['entry_geo_zone']                = 'Geo Zone';
$_['entry_status']                  = 'Status';
$_['entry_sort_order']              = 'Sort Order';
$_['entry_cron_url']                = 'Cron Job URL';
$_['entry_cron_time']               = 'Cron Job Last Run';
$_['entry_order_id']                = 'Order ID';
$_['entry_customer']                = 'Customer';
$_['entry_dp_percent']              = 'Down Payment Percent';
$_['entry_months']                  = 'Months';
$_['entry_date_added']              = 'Date Added';

// Help
$_['help_membership_id']            = 'Your personal Lay-Buy account membership number. (attained when you register a merchant account at https://www.lay-buys.com/index.php/vtmob/login)';
$_['help_token']                    = 'Enter a random token for security or use the one generated.';
$_['help_minimum']                  = 'Minimum Deposit Amount.';
$_['help_maximum']                  = 'Maximum Deposit Amount.';
$_['help_months']                   = 'Maximum Number of Months to pay Balance.';
$_['help_category']                 = 'Select for which categories the payment option will be available. Leave blank if no restriction.';
$_['help_product_ids']              = 'Add product IDs separated by comma(,) for which the method will not be available.';
$_['help_customer_group']           = 'The customer must be in these customer groups before this payment method becomes active. Leave blank if there is no restriction.';
$_['help_logging']                  = 'Enabling debug will write sensitive data to a log file. You should always disable unless instructed otherwise.';
$_['help_total']                    = 'The checkout total the order must reach before this payment method becomes active. Must be a value with no currency sign.';
$_['help_order_status_pending']     = 'The order status after the customer\'s order has been placed.';
$_['help_order_status_canceled']    = 'The order status after the customer\'s order is canceled.';
$_['help_order_status_processing']  = 'The order status after the customer\'s order is paid.';
$_['help_cron_url']                 = 'Set a cron job to call this URL so that the reports are auto fetched.';
$_['help_cron_time']                = 'This is the last time that the cron job URL was executed.';

// Error
$_['error_permission']              = 'Warning: You do not have permission to modify payment Lay-buy!';
$_['error_membership_id']           = 'Lay-Buys Membership ID Required!';
$_['error_token']                   = 'Lay-Buy Secret Token Required!';
$_['error_min_deposit']             = 'Cannot exceed the Maximum Down Payment amount!';

// Button
$_['button_fetch']                  = 'Fetch';
$_['button_revise_plan']            = 'Revise Plan';
$_['button_cancel_plan']            = 'Cancel Plan';