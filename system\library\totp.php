<?php
/**
 * مكتبة TOTP (Time-based One-Time Password) - AYM ERP
 * 
 * @package    AYM ERP
 * <AUTHOR> Team
 * @copyright  2025 AYM ERP
 * @license    Commercial
 * @version    1.0.0
 */

class TOTP {
    private $secret;
    private $digits;
    private $period;
    private $algorithm;

    /**
     * Constructor
     */
    public function __construct($secret, $digits = 6, $period = 30, $algorithm = 'sha1') {
        $this->secret = $secret;
        $this->digits = $digits;
        $this->period = $period;
        $this->algorithm = $algorithm;
    }

    /**
     * توليد مفتاح سري عشوائي
     */
    public static function generateSecret($length = 32) {
        $chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
        $secret = '';
        
        for ($i = 0; $i < $length; $i++) {
            $secret .= $chars[random_int(0, strlen($chars) - 1)];
        }
        
        return $secret;
    }

    /**
     * توليد رمز TOTP للوقت الحالي
     */
    public function generate($timestamp = null) {
        if ($timestamp === null) {
            $timestamp = time();
        }
        
        $timeCounter = intval($timestamp / $this->period);
        
        return $this->generateHOTP($timeCounter);
    }

    /**
     * التحقق من رمز TOTP
     */
    public function verify($code, $window = 1, $timestamp = null) {
        if ($timestamp === null) {
            $timestamp = time();
        }
        
        $timeCounter = intval($timestamp / $this->period);
        
        // التحقق من النافذة الزمنية الحالية والنوافذ المجاورة
        for ($i = -$window; $i <= $window; $i++) {
            $testCode = $this->generateHOTP($timeCounter + $i);
            if ($this->constantTimeEquals($code, $testCode)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * توليد رمز HOTP
     */
    private function generateHOTP($counter) {
        // تحويل المفتاح السري من Base32
        $secret = $this->base32Decode($this->secret);
        
        // تحويل العداد إلى 8 bytes
        $counterBytes = pack('N*', 0) . pack('N*', $counter);
        
        // حساب HMAC
        $hash = hash_hmac($this->algorithm, $counterBytes, $secret, true);
        
        // Dynamic truncation
        $offset = ord($hash[strlen($hash) - 1]) & 0xf;
        
        $code = (
            ((ord($hash[$offset]) & 0x7f) << 24) |
            ((ord($hash[$offset + 1]) & 0xff) << 16) |
            ((ord($hash[$offset + 2]) & 0xff) << 8) |
            (ord($hash[$offset + 3]) & 0xff)
        ) % pow(10, $this->digits);
        
        return str_pad($code, $this->digits, '0', STR_PAD_LEFT);
    }

    /**
     * فك تشفير Base32
     */
    private function base32Decode($input) {
        $input = strtoupper($input);
        $input = preg_replace('/[^A-Z2-7]/', '', $input);
        
        $map = [
            'A' => 0, 'B' => 1, 'C' => 2, 'D' => 3, 'E' => 4, 'F' => 5, 'G' => 6, 'H' => 7,
            'I' => 8, 'J' => 9, 'K' => 10, 'L' => 11, 'M' => 12, 'N' => 13, 'O' => 14, 'P' => 15,
            'Q' => 16, 'R' => 17, 'S' => 18, 'T' => 19, 'U' => 20, 'V' => 21, 'W' => 22, 'X' => 23,
            'Y' => 24, 'Z' => 25, '2' => 26, '3' => 27, '4' => 28, '5' => 29, '6' => 30, '7' => 31
        ];
        
        $output = '';
        $buffer = 0;
        $bitsLeft = 0;
        
        for ($i = 0; $i < strlen($input); $i++) {
            $char = $input[$i];
            if (!isset($map[$char])) {
                continue;
            }
            
            $buffer = ($buffer << 5) | $map[$char];
            $bitsLeft += 5;
            
            if ($bitsLeft >= 8) {
                $output .= chr(($buffer >> ($bitsLeft - 8)) & 255);
                $bitsLeft -= 8;
            }
        }
        
        return $output;
    }

    /**
     * مقارنة آمنة للسلاسل النصية
     */
    private function constantTimeEquals($a, $b) {
        if (strlen($a) !== strlen($b)) {
            return false;
        }
        
        $result = 0;
        for ($i = 0; $i < strlen($a); $i++) {
            $result |= ord($a[$i]) ^ ord($b[$i]);
        }
        
        return $result === 0;
    }

    /**
     * الحصول على URL لـ QR Code
     */
    public function getQRCodeURL($label, $issuer = 'AYM ERP') {
        $params = [
            'secret' => $this->secret,
            'issuer' => $issuer,
            'algorithm' => strtoupper($this->algorithm),
            'digits' => $this->digits,
            'period' => $this->period
        ];
        
        $url = 'otpauth://totp/' . urlencode($label) . '?' . http_build_query($params);
        
        return 'https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=' . urlencode($url);
    }

    /**
     * الحصول على URL للمصادقة
     */
    public function getProvisioningURI($label, $issuer = 'AYM ERP') {
        $params = [
            'secret' => $this->secret,
            'issuer' => $issuer,
            'algorithm' => strtoupper($this->algorithm),
            'digits' => $this->digits,
            'period' => $this->period
        ];
        
        return 'otpauth://totp/' . urlencode($label) . '?' . http_build_query($params);
    }

    /**
     * تشفير Base32
     */
    public static function base32Encode($input) {
        $chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
        $output = '';
        $buffer = 0;
        $bitsLeft = 0;
        
        for ($i = 0; $i < strlen($input); $i++) {
            $buffer = ($buffer << 8) | ord($input[$i]);
            $bitsLeft += 8;
            
            while ($bitsLeft >= 5) {
                $output .= $chars[($buffer >> ($bitsLeft - 5)) & 31];
                $bitsLeft -= 5;
            }
        }
        
        if ($bitsLeft > 0) {
            $output .= $chars[($buffer << (5 - $bitsLeft)) & 31];
        }
        
        // إضافة padding
        while (strlen($output) % 8 !== 0) {
            $output .= '=';
        }
        
        return $output;
    }

    /**
     * توليد رموز النسخ الاحتياطي
     */
    public static function generateBackupCodes($count = 10) {
        $codes = [];
        
        for ($i = 0; $i < $count; $i++) {
            $codes[] = sprintf('%08d', random_int(10000000, 99999999));
        }
        
        return $codes;
    }

    /**
     * التحقق من صحة المفتاح السري
     */
    public static function isValidSecret($secret) {
        // التحقق من أن المفتاح يحتوي على أحرف Base32 صحيحة فقط
        return preg_match('/^[A-Z2-7]+=*$/', strtoupper($secret));
    }

    /**
     * تنظيف المفتاح السري
     */
    public static function cleanSecret($secret) {
        // إزالة المسافات والأحرف غير المرغوب فيها
        $secret = strtoupper(preg_replace('/[^A-Z2-7]/', '', $secret));
        
        // إزالة padding إذا وجد
        $secret = rtrim($secret, '=');
        
        return $secret;
    }

    /**
     * الحصول على الوقت المتبقي للرمز الحالي
     */
    public function getRemainingTime($timestamp = null) {
        if ($timestamp === null) {
            $timestamp = time();
        }
        
        return $this->period - ($timestamp % $this->period);
    }

    /**
     * الحصول على معلومات الرمز الحالي
     */
    public function getCurrentCodeInfo($timestamp = null) {
        if ($timestamp === null) {
            $timestamp = time();
        }
        
        return [
            'code' => $this->generate($timestamp),
            'remaining_time' => $this->getRemainingTime($timestamp),
            'period' => $this->period,
            'timestamp' => $timestamp
        ];
    }
}
?>
