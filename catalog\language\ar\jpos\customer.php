<?php
// Text
$_['text_customers']       = 'Customers';

$_['text_no_customer']      = 'No Customer!';
$_['text_no_results']       = 'Customers Not Found!';

// Legend
$_['legend_your_details']  = 'Personal Details';
$_['legend_address']       = 'Address';

// Success
$_['success_add_guest']		= 'Success: Guest added.';
$_['success_add_customer']	= 'Success: Customer added.';
$_['text_success']			= 'Success: You have modified customers!';

// Entry
$_['entry_firstname']       = 'First Name';
$_['entry_lastname']       	= 'Last Name';
$_['entry_email']       	= 'Email';
$_['entry_telephone']       = 'Telephone';
$_['entry_company']       	= 'Company';
$_['entry_address_1']       = 'Address 1';
$_['entry_address_2']       = 'Address 2';
$_['entry_city']       		= 'City';
$_['entry_postcode']       	= 'Zip/Postal Code';
$_['entry_country']       	= 'Country';
$_['entry_zone']       		= 'State';
$_['entry_fax']       		= 'Fax';
$_['entry_password']       	= 'Password';
$_['entry_password_confirm']= 'Confirm Password';
$_['entry_customer_group']	= 'Customer Group';

// Button
$_['button_edit']       		= 'Edit';
$_['button_delete']       		= 'Delete';
$_['button_customer_to_cart']   = 'Assign Customer To Cart';
$_['button_add_new_customer']   = 'Add New Customer';
$_['button_add_as_guest']       = 'Add As Guest';
$_['button_save']       		= 'Save';
$_['button_cancel']       		= 'Cancel';

// Placeholder
$_['placeholder_search_customers']       = 'Enter Customer name or email to search';

// Error
$_['error_warning']        = 'Warning: Please check the form carefully for errors!';
$_['error_exists']         = 'Warning: E-Mail Address is already registered!';
$_['error_firstname']      = 'First Name must be between 1 and 32 characters!';
$_['error_lastname']       = 'Last Name must be between 1 and 32 characters!';
$_['error_email']          = 'E-Mail Address does not appear to be valid!';
$_['error_telephone']      = 'Telephone must be between 3 and 32 characters!';
$_['error_address_1']      = 'Address 1 must be between 3 and 128 characters!';
$_['error_city']           = 'City must be between 2 and 128 characters!';
$_['error_postcode']       = 'Postcode must be between 2 and 10 characters!';
$_['error_country']        = 'Please select a country!';
$_['error_zone']           = 'Please select a region / state!';
$_['error_custom_field']   = '%s required!';
$_['error_password']       = 'Password must be between 4 and 20 characters!';