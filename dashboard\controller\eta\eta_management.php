<?php
/**
 * كونترولر إدارة تكامل ETA
 * إدارة شاملة لتكامل مصلحة الضرائب المصرية
 * يتضمن إعداد الاتصال، إرسال الفواتير، مراقبة الحالة، والتقارير
 */
class ControllerEtaEtaManagement extends Controller {
    private $error = array();
    private $central_service;

    public function __construct($registry) {
        parent::__construct($registry);
        
        // تحميل الخدمات المركزية
        $this->load->model('core/central_service_manager');
        $this->central_service = $this->model_core_central_service_manager;
    }

    public function index() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'eta/eta_management') || 
            !$this->user->hasKey('eta_management_view')) {
            
            $this->central_service->logActivity('unauthorized_access', 'eta', 
                'محاولة وصول غير مصرح بها لإدارة ETA', [
                'user_id' => $this->user->getId(),
                'ip_address' => $this->request->server['REMOTE_ADDR']
            ]);
            
            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('eta/eta_management');
        $this->document->setTitle($this->language->get('heading_title'));
        $this->load->model('eta/eta_client');

        // إضافة CSS و JavaScript المتقدم
        $this->document->addStyle('view/stylesheet/eta/eta_management.css');
        $this->document->addScript('view/javascript/eta/eta_management.js');
        $this->document->addScript('view/javascript/jquery/datatables.min.js');
        $this->document->addStyle('view/javascript/jquery/datatables.min.css');

        // تسجيل الوصول للشاشة
        $this->central_service->logActivity('view', 'eta', 
            'عرض شاشة إدارة ETA', [
            'user_id' => $this->user->getId()
        ]);

        // الحصول على إحصائيات ETA
        $data['eta_statistics'] = $this->getETAStatistics();
        $data['recent_submissions'] = $this->getRecentSubmissions();
        $data['connection_status'] = $this->checkETAConnection();

        $data['breadcrumbs'] = array();
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('eta/eta_management', 'user_token=' . $this->session->data['user_token'], true)
        );

        $data['submit_invoice'] = $this->url->link('eta/eta_management/submitInvoice', 'user_token=' . $this->session->data['user_token'], true);
        $data['test_connection'] = $this->url->link('eta/eta_management/testConnection', 'user_token=' . $this->session->data['user_token'], true);
        $data['sync_status'] = $this->url->link('eta/eta_management/syncStatus', 'user_token=' . $this->session->data['user_token'], true);
        $data['settings'] = $this->url->link('setting/setting', 'user_token=' . $this->session->data['user_token'] . '#eta', true);

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('eta/eta_management_dashboard', $data));
    }

    /**
     * إرسال فاتورة إلى ETA
     */
    public function submitInvoice() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('modify', 'eta/eta_management') || 
            !$this->user->hasKey('eta_submit_invoice')) {
            
            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('eta/eta_management');
        $this->load->model('eta/eta_client');

        if ($this->request->server['REQUEST_METHOD'] == 'POST') {
            try {
                $invoice_id = $this->request->post['invoice_id'];
                
                // الحصول على بيانات الفاتورة
                $invoice_data = $this->getInvoiceData($invoice_id);
                
                if (!$invoice_data) {
                    throw new Exception('الفاتورة غير موجودة');
                }

                // تسجيل محاولة الإرسال
                $this->central_service->logActivity('submit_invoice_eta', 'eta', 
                    'محاولة إرسال فاتورة إلى ETA: ' . $invoice_id, [
                    'user_id' => $this->user->getId(),
                    'invoice_id' => $invoice_id
                ]);

                // إرسال الفاتورة إلى ETA
                $result = $this->model_eta_eta_client->submitInvoice($invoice_data);

                if ($result['success']) {
                    // تحديث حالة الفاتورة في قاعدة البيانات
                    $this->updateInvoiceETAStatus($invoice_id, 'submitted', $result);

                    // إرسال إشعار للإدارة المالية
                    $this->central_service->sendNotification(
                        'eta_invoice_submitted',
                        'تم إرسال فاتورة إلى ETA',
                        'تم إرسال الفاتورة رقم ' . $invoice_data['invoice_number'] . ' إلى مصلحة الضرائب بنجاح',
                        [$this->config->get('config_cfo_id'), $this->config->get('config_tax_manager_id')],
                        [
                            'invoice_id' => $invoice_id,
                            'invoice_number' => $invoice_data['invoice_number'],
                            'submission_uuid' => $result['submission_uuid'],
                            'submitted_by' => $this->user->getFirstName() . ' ' . $this->user->getLastName()
                        ]
                    );

                    $this->session->data['success'] = $this->language->get('text_invoice_submitted_success');
                } else {
                    $this->updateInvoiceETAStatus($invoice_id, 'failed', $result);
                    $this->session->data['error'] = $this->language->get('text_invoice_submit_failed') . ': ' . $result['error'];
                }

            } catch (Exception $e) {
                $this->central_service->logActivity('submit_invoice_eta_error', 'eta', 
                    'خطأ في إرسال فاتورة إلى ETA: ' . $e->getMessage(), [
                    'user_id' => $this->user->getId(),
                    'invoice_id' => $invoice_id ?? null,
                    'error' => $e->getMessage()
                ]);

                $this->session->data['error'] = $e->getMessage();
            }

            $this->response->redirect($this->url->link('eta/eta_management', 'user_token=' . $this->session->data['user_token'], true));
        }

        // عرض نموذج إرسال الفاتورة
        $data['breadcrumbs'] = array();
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('eta/eta_management', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_submit_invoice'),
            'href' => $this->url->link('eta/eta_management/submitInvoice', 'user_token=' . $this->session->data['user_token'], true)
        );

        $data['action'] = $this->url->link('eta/eta_management/submitInvoice', 'user_token=' . $this->session->data['user_token'], true);
        $data['cancel'] = $this->url->link('eta/eta_management', 'user_token=' . $this->session->data['user_token'], true);

        // الحصول على قائمة الفواتير غير المرسلة
        $data['pending_invoices'] = $this->getPendingInvoices();

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('eta/eta_submit_invoice_form', $data));
    }

    /**
     * اختبار الاتصال مع ETA
     */
    public function testConnection() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'eta/eta_management') || 
            !$this->user->hasKey('eta_test_connection')) {
            
            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->model('eta/eta_client');

        try {
            // محاولة المصادقة مع ETA
            $auth_result = $this->model_eta_eta_client->authenticate();

            if ($auth_result) {
                $response = array(
                    'success' => true,
                    'message' => 'تم الاتصال بنجاح مع مصلحة الضرائب المصرية'
                );

                // تسجيل نجاح الاتصال
                $this->central_service->logActivity('eta_connection_test_success', 'eta', 
                    'نجح اختبار الاتصال مع ETA', [
                    'user_id' => $this->user->getId()
                ]);
            } else {
                $response = array(
                    'success' => false,
                    'message' => 'فشل في الاتصال مع مصلحة الضرائب المصرية'
                );
            }

        } catch (Exception $e) {
            $response = array(
                'success' => false,
                'message' => 'خطأ في الاتصال: ' . $e->getMessage()
            );

            // تسجيل فشل الاتصال
            $this->central_service->logActivity('eta_connection_test_failed', 'eta', 
                'فشل اختبار الاتصال مع ETA: ' . $e->getMessage(), [
                'user_id' => $this->user->getId(),
                'error' => $e->getMessage()
            ]);
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($response));
    }

    /**
     * مزامنة حالة المستندات مع ETA
     */
    public function syncStatus() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('modify', 'eta/eta_management') || 
            !$this->user->hasKey('eta_sync_status')) {
            
            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->model('eta/eta_client');

        try {
            // الحصول على المستندات المرسلة والتي تحتاج مزامنة
            $pending_documents = $this->getPendingDocuments();
            $synced_count = 0;

            foreach ($pending_documents as $document) {
                $status_result = $this->model_eta_eta_client->getDocumentStatus($document['eta_uuid']);
                
                if ($status_result['success']) {
                    $this->updateDocumentStatus($document['id'], $status_result['document']);
                    $synced_count++;
                }
            }

            // تسجيل المزامنة
            $this->central_service->logActivity('eta_status_sync', 'eta', 
                'مزامنة حالة المستندات مع ETA: ' . $synced_count . ' مستند', [
                'user_id' => $this->user->getId(),
                'synced_count' => $synced_count
            ]);

            $response = array(
                'success' => true,
                'message' => 'تم مزامنة ' . $synced_count . ' مستند بنجاح',
                'synced_count' => $synced_count
            );

        } catch (Exception $e) {
            $response = array(
                'success' => false,
                'message' => 'خطأ في المزامنة: ' . $e->getMessage()
            );
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($response));
    }

    // دوال مساعدة
    private function getETAStatistics() {
        $query = $this->db->query("
            SELECT 
                COUNT(*) as total_submissions,
                COUNT(CASE WHEN status = 'submitted' THEN 1 END) as submitted_count,
                COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_count,
                COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected_count,
                COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_count
            FROM " . DB_PREFIX . "eta_submissions
            WHERE submitted_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        ");

        return $query->row;
    }

    private function getRecentSubmissions() {
        $query = $this->db->query("
            SELECT es.*, o.invoice_number, o.total, o.customer_name
            FROM " . DB_PREFIX . "eta_submissions es
            LEFT JOIN " . DB_PREFIX . "orders o ON (es.invoice_id = o.order_id)
            ORDER BY es.submitted_at DESC
            LIMIT 10
        ");

        return $query->rows;
    }

    private function checkETAConnection() {
        try {
            $this->load->model('eta/eta_client');
            return $this->model_eta_eta_client->authenticate();
        } catch (Exception $e) {
            return false;
        }
    }

    private function getInvoiceData($invoice_id) {
        // الحصول على بيانات الفاتورة من قاعدة البيانات
        $query = $this->db->query("
            SELECT o.*, c.firstname, c.lastname, c.email, c.telephone
            FROM " . DB_PREFIX . "orders o
            LEFT JOIN " . DB_PREFIX . "customers c ON (o.customer_id = c.customer_id)
            WHERE o.order_id = '" . (int)$invoice_id . "'
        ");

        if ($query->num_rows) {
            $invoice = $query->row;
            
            // الحصول على بنود الفاتورة
            $items_query = $this->db->query("
                SELECT op.*, p.tax_class_id
                FROM " . DB_PREFIX . "order_products op
                LEFT JOIN " . DB_PREFIX . "products p ON (op.product_id = p.product_id)
                WHERE op.order_id = '" . (int)$invoice_id . "'
            ");

            $invoice['items'] = $items_query->rows;
            return $invoice;
        }

        return false;
    }

    private function updateInvoiceETAStatus($invoice_id, $status, $result) {
        $this->db->query("
            UPDATE " . DB_PREFIX . "orders 
            SET eta_status = '" . $this->db->escape($status) . "',
                eta_submission_uuid = '" . $this->db->escape($result['submission_uuid'] ?? '') . "',
                eta_updated_at = NOW()
            WHERE order_id = '" . (int)$invoice_id . "'
        ");
    }

    private function getPendingInvoices() {
        $query = $this->db->query("
            SELECT order_id, invoice_number, total, customer_name, date_added
            FROM " . DB_PREFIX . "orders
            WHERE (eta_status IS NULL OR eta_status = 'failed')
              AND order_status_id IN (2, 3, 5) -- حالات الطلبات المكتملة
            ORDER BY date_added DESC
            LIMIT 50
        ");

        return $query->rows;
    }

    private function getPendingDocuments() {
        $query = $this->db->query("
            SELECT id, eta_uuid, invoice_id
            FROM " . DB_PREFIX . "eta_submissions
            WHERE status IN ('submitted', 'processing')
              AND submitted_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        ");

        return $query->rows;
    }

    private function updateDocumentStatus($submission_id, $eta_document) {
        $status = $eta_document['status'] ?? 'unknown';
        
        $this->db->query("
            UPDATE " . DB_PREFIX . "eta_submissions
            SET status = '" . $this->db->escape($status) . "',
                eta_response = '" . $this->db->escape(json_encode($eta_document)) . "',
                updated_at = NOW()
            WHERE id = '" . (int)$submission_id . "'
        ");
    }
}
