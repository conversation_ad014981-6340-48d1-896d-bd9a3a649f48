<?php
// Heading
$_['heading_title']                = 'Advanced Account Query';

// Missing Variables from Audit Report
$_['accounts/account_query']       = 'Account Query';
$_['ajax_balance_history_url']     = 'Balance History URL';
$_['ajax_export_url']              = 'Export URL';
$_['ajax_query_url']               = 'Query URL';
$_['ajax_transactions_url']        = 'Transactions URL';
$_['button_load_chart']            = 'Load Chart';
$_['column_left']                  = 'Left Column';
$_['column_source']                = 'Source';
$_['error_account_not_found']      = 'Account not found';
$_['error_accounts_required']      = 'Accounts required';
$_['error_delete_failed']          = 'Delete failed';
$_['error_favorite_id_required']   = 'Favorite ID required';
$_['error_favorite_not_found']     = 'Favorite not found';
$_['error_invalid_format']         = 'Invalid format';
$_['error_query_failed']           = 'Query failed';
$_['error_query_name_required']    = 'Query name required';
$_['error_save_failed']            = 'Save failed';
$_['footer']                       = 'Footer';
$_['header']                       = 'Header';
$_['text_avg_credit']              = 'Average Credit';
$_['text_avg_debit']               = 'Average Debit';
$_['text_balance']                 = 'Balance';
$_['text_balance_history']         = 'Balance History';
$_['text_chart_period']            = 'Chart Period';
$_['text_credit']                  = 'Credit';
$_['text_daily']                   = 'Daily';
$_['text_debit']                   = 'Debit';
$_['text_description']             = 'Description';
$_['text_general_statistics']      = 'General Statistics';
$_['text_home']                    = 'Home';
$_['text_max_credit']              = 'Maximum Credit';
$_['text_max_debit']               = 'Maximum Debit';
$_['text_month']                   = 'Month';
$_['text_monthly']                 = 'Monthly';
$_['text_monthly_statistics']      = 'Monthly Statistics';
$_['text_status']                  = 'Status';
$_['text_success_delete_favorite'] = 'Favorite deleted successfully';
$_['text_success_save_favorite']   = 'Favorite saved successfully';
$_['text_weekly']                  = 'Weekly';
$_['text_yearly']                  = 'Yearly';
$_['user_token']                   = 'User Token';

// Text
$_['text_query_form']              = 'Query Form';
$_['text_account']                 = 'Account';
$_['text_select_account']          = 'Select Account...';
$_['text_date_from']               = 'Date From';
$_['text_date_to']                 = 'Date To';
$_['text_account_info']            = 'Account Information';
$_['text_balance_summary']         = 'Balance Summary';
$_['text_transactions']            = 'Transactions';
$_['text_balance_chart']           = 'Balance Chart';
$_['text_statistics']              = 'Statistics';
$_['text_loading']                 = 'Loading...';
$_['text_ajax_error']              = 'An error occurred while connecting to the server';
$_['text_none']                    = 'None';
$_['text_no_data']                 = 'No data available';

// Account Information
$_['text_account_code']            = 'Account Code';
$_['text_account_name']            = 'Account Name';
$_['text_account_type']            = 'Account Type';
$_['text_parent_account']          = 'Parent Account';
$_['text_account_status']          = 'Account Status';
$_['text_account_description']     = 'Account Description';

// Balance Information
$_['text_opening_balance']         = 'Opening Balance';
$_['text_closing_balance']         = 'Closing Balance';
$_['text_current_balance']         = 'Current Balance';
$_['text_total_debit']             = 'Total Debit';
$_['text_total_credit']            = 'Total Credit';
$_['text_net_movement']            = 'Net Movement';
$_['text_balance_change']          = 'Balance Change';
$_['text_percentage_change']       = 'Percentage Change';

// Transaction Information
$_['text_transaction_count']       = 'Transaction Count';
$_['text_last_transaction']        = 'Last Transaction';
$_['text_first_transaction']       = 'First Transaction';
$_['text_transaction_date']        = 'Transaction Date';
$_['text_transaction_description'] = 'Transaction Description';
$_['text_transaction_reference']   = 'Transaction Reference';
$_['text_transaction_amount']      = 'Transaction Amount';
$_['text_transaction_type']        = 'Transaction Type';

// Statistics
$_['text_average_balance']         = 'Average Balance';
$_['text_minimum_balance']         = 'Minimum Balance';
$_['text_maximum_balance']         = 'Maximum Balance';
$_['text_average_transaction']     = 'Average Transaction';
$_['text_largest_transaction']     = 'Largest Transaction';
$_['text_smallest_transaction']    = 'Smallest Transaction';
$_['text_active_days']             = 'Active Days';
$_['text_inactive_days']           = 'Inactive Days';
$_['text_activity_rate']           = 'Activity Rate';
$_['text_volatility']              = 'Volatility';

// Advanced Analysis
$_['text_performance_analysis']    = 'Performance Analysis';
$_['text_risk_analysis']           = 'Risk Analysis';
$_['text_seasonality_analysis']    = 'Seasonality Analysis';
$_['text_trend_analysis']          = 'Trend Analysis';
$_['text_activity_analysis']       = 'Activity Analysis';
$_['text_comparative_analysis']    = 'Comparative Analysis';

// Performance Metrics
$_['text_growth_rate']             = 'Growth Rate';
$_['text_turnover_ratio']          = 'Turnover Ratio';
$_['text_efficiency_ratio']        = 'Efficiency Ratio';
$_['text_utilization_rate']        = 'Utilization Rate';
$_['text_performance_score']       = 'Performance Score';
$_['text_benchmark_comparison']    = 'Benchmark Comparison';

// Risk Metrics
$_['text_risk_score']              = 'Risk Score';
$_['text_risk_level']              = 'Risk Level';
$_['text_risk_factors']            = 'Risk Factors';
$_['text_risk_mitigation']         = 'Risk Mitigation';
$_['text_credit_risk']             = 'Credit Risk';
$_['text_liquidity_risk']          = 'Liquidity Risk';
$_['text_operational_risk']        = 'Operational Risk';
$_['text_market_risk']             = 'Market Risk';

// Seasonality
$_['text_seasonal_pattern']        = 'Seasonal Pattern';
$_['text_seasonal_index']          = 'Seasonal Index';
$_['text_peak_season']             = 'Peak Season';
$_['text_low_season']              = 'Low Season';
$_['text_seasonal_variance']       = 'Seasonal Variance';

// Trend Analysis
$_['text_trend_direction']         = 'Trend Direction';
$_['text_trend_strength']          = 'Trend Strength';
$_['text_trend_forecast']          = 'Trend Forecast';
$_['text_upward_trend']            = 'Upward Trend';
$_['text_downward_trend']          = 'Downward Trend';
$_['text_stable_trend']            = 'Stable Trend';

// Favorites
$_['text_favorite_queries']        = 'Favorite Queries';
$_['text_save_favorite']           = 'Save as Favorite';
$_['text_load_favorite']           = 'Load Favorite';
$_['text_delete_favorite']         = 'Delete Favorite';
$_['text_favorite_name']           = 'Favorite Name';
$_['text_favorite_description']    = 'Favorite Description';
$_['text_no_favorites']            = 'No favorite queries saved';

// Comparison
$_['text_compare_accounts']        = 'Compare Accounts';
$_['text_comparison_chart']        = 'Comparison Chart';
$_['text_comparison_table']        = 'Comparison Table';
$_['text_select_accounts']         = 'Select Accounts to Compare';
$_['text_comparison_period']       = 'Comparison Period';
$_['text_comparison_metrics']      = 'Comparison Metrics';

// Export Options
$_['text_export_options']          = 'Export Options';
$_['text_export_excel']            = 'Export to Excel';
$_['text_export_pdf']              = 'Export to PDF';
$_['text_export_csv']              = 'Export to CSV';
$_['text_export_xml']              = 'Export to XML';
$_['text_export_json']             = 'Export to JSON';
$_['text_advanced_export']         = 'Advanced Export';

// Search Options
$_['text_advanced_search']         = 'Advanced Search';
$_['text_search_criteria']         = 'Search Criteria';
$_['text_search_amount_from']      = 'Amount From';
$_['text_search_amount_to']        = 'Amount To';
$_['text_search_description']      = 'Description Contains';
$_['text_search_reference']        = 'Reference Contains';
$_['text_search_type']             = 'Transaction Type';

// Reports
$_['text_comprehensive_report']    = 'Comprehensive Report';
$_['text_advanced_report']         = 'Advanced Report';
$_['text_summary_report']          = 'Summary Report';
$_['text_detailed_report']         = 'Detailed Report';
$_['text_custom_report']           = 'Custom Report';

// Buttons
$_['button_query']                 = 'Query';
$_['button_clear']                 = 'Clear';
$_['button_export']                = 'Export';
$_['button_print']                 = 'Print';
$_['button_save_favorite']         = 'Save Favorite';
$_['button_load_favorite']         = 'Load Favorite';
$_['button_compare']               = 'Compare';
$_['button_analyze']               = 'Analyze';
$_['button_search']                = 'Search';
$_['button_reset']                 = 'Reset';
$_['button_refresh']               = 'Refresh';

// Column Headers
$_['column_date']                  = 'Date';
$_['column_description']           = 'Description';
$_['column_reference']             = 'Reference';
$_['column_debit']                 = 'Debit';
$_['column_credit']                = 'Credit';
$_['column_balance']               = 'Balance';
$_['column_type']                  = 'Type';
$_['column_amount']                = 'Amount';
$_['column_account']               = 'Account';
$_['column_period']                = 'Period';
$_['column_change']                = 'Change';
$_['column_percentage']            = 'Percentage';

// Error Messages
$_['error_permission']             = 'Warning: You do not have permission to access account query!';
$_['error_account_required']       = 'Account selection is required!';
$_['error_date_invalid']           = 'Invalid date format!';
$_['error_date_range']             = 'End date must be after start date!';
$_['error_no_data']                = 'No data found for the selected criteria!';
$_['error_export_failed']          = 'Export failed! Please try again.';
$_['error_save_favorite']          = 'Failed to save favorite query!';
$_['error_load_favorite']          = 'Failed to load favorite query!';
$_['error_delete_favorite']        = 'Failed to delete favorite query!';
$_['error_comparison_accounts']    = 'Please select at least 2 accounts for comparison!';
$_['error_analysis_failed']        = 'Analysis failed! Please try again.';

// Success Messages
$_['success_export']               = 'Data exported successfully!';
$_['success_save_favorite']        = 'Favorite query saved successfully!';
$_['success_load_favorite']        = 'Favorite query loaded successfully!';
$_['success_delete_favorite']      = 'Favorite query deleted successfully!';
$_['success_analysis']             = 'Analysis completed successfully!';

// Help Text
$_['help_account_query']           = 'Use this tool to query account balances, transactions, and perform advanced analysis.';
$_['help_date_range']              = 'Select a date range to filter transactions. Leave empty to include all transactions.';
$_['help_advanced_search']         = 'Use advanced search to find specific transactions based on amount, description, or reference.';
$_['help_favorites']               = 'Save frequently used queries as favorites for quick access.';
$_['help_comparison']              = 'Compare multiple accounts side by side to analyze performance differences.';
$_['help_analysis']                = 'Perform advanced analysis including trend, risk, and seasonality analysis.';

// Chart Labels
$_['chart_balance_over_time']      = 'Balance Over Time';
$_['chart_monthly_activity']       = 'Monthly Activity';
$_['chart_transaction_types']      = 'Transaction Types';
$_['chart_account_comparison']     = 'Account Comparison';
$_['chart_trend_analysis']         = 'Trend Analysis';
$_['chart_risk_assessment']        = 'Risk Assessment';

// Status
$_['text_status_active']           = 'Active';
$_['text_status_inactive']         = 'Inactive';
$_['text_status_suspended']        = 'Suspended';
$_['text_status_closed']           = 'Closed';

// Account Types
$_['text_type_asset']              = 'Asset';
$_['text_type_liability']          = 'Liability';
$_['text_type_equity']             = 'Equity';
$_['text_type_revenue']            = 'Revenue';
$_['text_type_expense']            = 'Expense';

// Transaction Types
$_['text_type_debit']              = 'Debit';
$_['text_type_credit']             = 'Credit';
$_['text_type_transfer']           = 'Transfer';
$_['text_type_adjustment']         = 'Adjustment';
$_['text_type_opening']            = 'Opening Balance';
$_['text_type_closing']            = 'Closing Balance';

// Periods
$_['text_period_daily']            = 'Daily';
$_['text_period_weekly']           = 'Weekly';
$_['text_period_monthly']          = 'Monthly';
$_['text_period_quarterly']        = 'Quarterly';
$_['text_period_yearly']           = 'Yearly';
$_['text_period_custom']           = 'Custom Period';

// Analysis Results
$_['text_analysis_positive']       = 'Positive';
$_['text_analysis_negative']       = 'Negative';
$_['text_analysis_neutral']        = 'Neutral';
$_['text_analysis_improving']      = 'Improving';
$_['text_analysis_declining']      = 'Declining';
$_['text_analysis_stable']         = 'Stable';

// Risk Levels
$_['text_risk_low']                = 'Low Risk';
$_['text_risk_medium']             = 'Medium Risk';
$_['text_risk_high']               = 'High Risk';
$_['text_risk_critical']           = 'Critical Risk';

// Performance Levels
$_['text_performance_excellent']   = 'Excellent';
$_['text_performance_good']        = 'Good';
$_['text_performance_average']     = 'Average';
$_['text_performance_poor']        = 'Poor';

// Additional
$_['text_total']                   = 'Total';
$_['text_average']                 = 'Average';
$_['text_minimum']                 = 'Minimum';
$_['text_maximum']                 = 'Maximum';
$_['text_count']                   = 'Count';
$_['text_sum']                     = 'Sum';
$_['text_percentage']              = 'Percentage';
$_['text_ratio']                   = 'Ratio';
$_['text_index']                   = 'Index';
$_['text_score']                   = 'Score';
$_['text_rank']                    = 'Rank';
$_['text_grade']                   = 'Grade';

// Machine Learning & AI
$_['text_machine_learning']        = 'Machine Learning';
$_['text_predictive_modeling']     = 'Predictive Modeling';
$_['text_anomaly_detection']       = 'Anomaly Detection';
$_['text_data_mining']             = 'Data Mining';
$_['text_artificial_intelligence'] = 'Artificial Intelligence';
$_['text_neural_network']          = 'Neural Network';
$_['text_deep_learning']           = 'Deep Learning';
$_['text_pattern_recognition']     = 'Pattern Recognition';

// Currency & Formatting
$_['format_currency']              = '%s EGP';
$_['format_date']                  = 'd/m/Y';
$_['format_datetime']              = 'd/m/Y H:i:s';
$_['format_percentage']            = '%s%%';
$_['format_number']                = '%s';

// Compliance
$_['text_eas_compliant']           = 'Egyptian Accounting Standards Compliant';
$_['text_eta_ready']               = 'ETA Integration Ready';
$_['text_egyptian_gaap']           = 'Egyptian GAAP Compliant';
$_['text_ifrs_compatible']         = 'IFRS Compatible';

// Additional Analysis Features
$_['text_correlation_analysis']    = 'Correlation Analysis';
$_['text_regression_analysis']     = 'Regression Analysis';
$_['text_variance_analysis']       = 'Variance Analysis';
$_['text_sensitivity_analysis']    = 'Sensitivity Analysis';
$_['text_scenario_analysis']       = 'Scenario Analysis';
$_['text_monte_carlo']             = 'Monte Carlo Simulation';
$_['text_stress_testing']          = 'Stress Testing';
$_['text_backtesting']             = 'Backtesting';

// Advanced Metrics
$_['text_sharpe_ratio']            = 'Sharpe Ratio';
$_['text_beta_coefficient']        = 'Beta Coefficient';
$_['text_alpha_value']             = 'Alpha Value';
$_['text_correlation_coefficient'] = 'Correlation Coefficient';
$_['text_standard_deviation']      = 'Standard Deviation';
$_['text_coefficient_variation']   = 'Coefficient of Variation';
$_['text_skewness']                = 'Skewness';
$_['text_kurtosis']                = 'Kurtosis';

// Time Series Analysis
$_['text_moving_average']          = 'Moving Average';
$_['text_exponential_smoothing']   = 'Exponential Smoothing';
$_['text_autoregression']          = 'Autoregression';
$_['text_seasonal_decomposition']  = 'Seasonal Decomposition';
$_['text_fourier_analysis']        = 'Fourier Analysis';
$_['text_wavelet_analysis']        = 'Wavelet Analysis';

// Forecasting
$_['text_forecasting']             = 'Forecasting';
$_['text_forecast_accuracy']       = 'Forecast Accuracy';
$_['text_forecast_horizon']        = 'Forecast Horizon';
$_['text_forecast_confidence']     = 'Forecast Confidence';
$_['text_forecast_interval']       = 'Forecast Interval';
$_['text_forecast_error']          = 'Forecast Error';

// Data Quality
$_['text_data_quality']            = 'Data Quality';
$_['text_data_completeness']       = 'Data Completeness';
$_['text_data_accuracy']           = 'Data Accuracy';
$_['text_data_consistency']        = 'Data Consistency';
$_['text_data_timeliness']         = 'Data Timeliness';
$_['text_data_validity']           = 'Data Validity';

// Visualization
$_['text_visualization']           = 'Visualization';
$_['text_interactive_charts']      = 'Interactive Charts';
$_['text_dashboard']               = 'Dashboard';
$_['text_heatmap']                 = 'Heatmap';
$_['text_scatter_plot']            = 'Scatter Plot';
$_['text_histogram']               = 'Histogram';
$_['text_box_plot']                = 'Box Plot';
$_['text_violin_plot']             = 'Violin Plot';

// Alerts and Notifications
$_['text_alerts']                  = 'Alerts';
$_['text_notifications']           = 'Notifications';
$_['text_threshold_alerts']        = 'Threshold Alerts';
$_['text_anomaly_alerts']          = 'Anomaly Alerts';
$_['text_trend_alerts']            = 'Trend Alerts';
$_['text_performance_alerts']      = 'Performance Alerts';

// Automation
$_['text_automation']              = 'Automation';
$_['text_automated_analysis']      = 'Automated Analysis';
$_['text_scheduled_reports']       = 'Scheduled Reports';
$_['text_auto_refresh']            = 'Auto Refresh';
$_['text_real_time_updates']       = 'Real-time Updates';

// Integration
$_['text_integration']             = 'Integration';
$_['text_api_integration']         = 'API Integration';
$_['text_data_import']             = 'Data Import';
$_['text_data_export']             = 'Data Export';
$_['text_external_data']           = 'External Data';
$_['text_third_party']             = 'Third Party';

// Collaboration
$_['text_collaboration']           = 'Collaboration';
$_['text_shared_queries']          = 'Shared Queries';
$_['text_team_analysis']           = 'Team Analysis';
$_['text_comments']                = 'Comments';
$_['text_annotations']             = 'Annotations';
$_['text_version_control']         = 'Version Control';

// Performance
$_['text_performance']             = 'Performance';
$_['text_query_performance']       = 'Query Performance';
$_['text_execution_time']          = 'Execution Time';
$_['text_memory_usage']            = 'Memory Usage';
$_['text_cpu_usage']               = 'CPU Usage';
$_['text_optimization']            = 'Optimization';

// Security
$_['text_security']                = 'Security';
$_['text_data_encryption']         = 'Data Encryption';
$_['text_access_control']          = 'Access Control';
$_['text_user_authentication']     = 'User Authentication';
$_['text_security_audit']          = 'Security Audit';
$_['text_automated_alerts']        = 'Automated Alerts';

// Data Mining
$_['text_data_mining']             = 'Data Mining';
$_['text_pattern_recognition']     = 'Pattern Recognition';
$_['text_correlation_analysis']    = 'Correlation Analysis';
$_['text_clustering_analysis']     = 'Clustering Analysis';

// Machine Learning
$_['text_machine_learning']        = 'Machine Learning';
$_['text_predictive_modeling']     = 'Predictive Modeling';
$_['text_classification']          = 'Classification';
$_['text_regression_analysis']     = 'Regression Analysis';

// Advanced Filters
$_['text_dynamic_filters']         = 'Dynamic Filters';
$_['text_conditional_filters']     = 'Conditional Filters';
$_['text_multi_criteria_filters']  = 'Multi-Criteria Filters';
$_['text_smart_filters']           = 'Smart Filters';

// Audit and Compliance
$_['text_audit_features']          = 'Audit Features';
$_['text_compliance_check']        = 'Compliance Check';
$_['text_regulatory_reporting']    = 'Regulatory Reporting';
$_['text_audit_trail_analysis']    = 'Audit Trail Analysis';

// Integration Features
$_['text_api_integration']         = 'API Integration';
$_['text_external_data_sources']   = 'External Data Sources';
$_['text_real_time_sync']          = 'Real-time Sync';
$_['text_cloud_integration']       = 'Cloud Integration';

// Mobile Features
$_['text_mobile_optimization']     = 'Mobile Optimization';
$_['text_responsive_design']       = 'Responsive Design';
$_['text_mobile_alerts']           = 'Mobile Alerts';
$_['text_offline_access']          = 'Offline Access';

// Security Features
$_['text_data_encryption']         = 'Data Encryption';
$_['text_access_control']          = 'Access Control';
$_['text_user_authentication']     = 'User Authentication';
$_['text_security_audit']          = 'Security Audit';

// Missing variables from audit report - Critical fixes
$_['accounts/account_query']           = '';
$_['code']                             = 'Code';
$_['date_format_short']                = 'm/d/Y';
$_['direction']                        = 'ltr';
$_['lang']                             = 'en';

// Controller language variables
$_['log_unauthorized_access_account_query'] = 'Unauthorized access attempt to account query';
$_['log_view_account_query_screen']    = 'View account query screen';
$_['log_unauthorized_query_account']   = 'Unauthorized account query attempt';
$_['log_successful_query_account']     = 'Successful account query';
$_['text_advanced_account_query_report'] = 'Advanced Account Query Report';
$_['text_account_information']         = 'Account Information';
$_['text_balance_summary']             = 'Balance Summary';
$_['text_item']                        = 'Item';
$_['text_opening_balance']             = 'Opening Balance';
$_['text_total_debit']                 = 'Total Debit';
$_['text_total_credit']                = 'Total Credit';
$_['text_closing_balance']             = 'Closing Balance';

// Enhanced performance and security variables
$_['error_invalid_data']               = 'Invalid input data';
$_['error_missing_data']               = 'Required data missing';
$_['error_invalid_account']            = 'Invalid account ID';
$_['error_missing_account']            = 'Account ID required';
$_['error_system']                     = 'System error, please try again later';
$_['text_optimized_balance']           = 'Optimized Balance';
$_['text_advanced_analysis']           = 'Advanced Analysis';
$_['text_performance_analysis']        = 'Performance Analysis';
$_['text_cache_enabled']               = 'Cache Enabled';
$_['text_query_optimized']             = 'Query Optimized';
$_['button_advanced_analysis']         = 'Advanced Analysis';
$_['button_performance_report']        = 'Performance Report';
$_['text_loading_analysis']            = 'Analyzing data...';
$_['text_analysis_complete']           = 'Analysis complete';
?>
