{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="submit" form="extension\module\webpimages-form" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fas fa-save"></i></button>
        <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-light"><i class="fas fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    
    <div class="card">
      <div class="card-header"><i class="fas fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}chart-bar{% elif screen_type == 'settings' %}cog{% else %}pencil-alt{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="card-body">
        <form id="extension\module\webpimages-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-button_cancel">{{ text_button_cancel }}</label>
            <div class="col-sm-10">
              <input type="text" name="button_cancel" value="{{ button_cancel }}" placeholder="{{ text_button_cancel }}" id="input-button_cancel" class="form-control" />
              {% if error_button_cancel %}
                <div class="invalid-feedback">{{ error_button_cancel }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-button_save">{{ text_button_save }}</label>
            <div class="col-sm-10">
              <input type="text" name="button_save" value="{{ button_save }}" placeholder="{{ text_button_save }}" id="input-button_save" class="form-control" />
              {% if error_button_save %}
                <div class="invalid-feedback">{{ error_button_save }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-entry_cookie">{{ text_entry_cookie }}</label>
            <div class="col-sm-10">
              <input type="text" name="entry_cookie" value="{{ entry_cookie }}" placeholder="{{ text_entry_cookie }}" id="input-entry_cookie" class="form-control" />
              {% if error_entry_cookie %}
                <div class="invalid-feedback">{{ error_entry_cookie }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-entry_quality">{{ text_entry_quality }}</label>
            <div class="col-sm-10">
              <input type="text" name="entry_quality" value="{{ entry_quality }}" placeholder="{{ text_entry_quality }}" id="input-entry_quality" class="form-control" />
              {% if error_entry_quality %}
                <div class="invalid-feedback">{{ error_entry_quality }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-entry_status">{{ text_entry_status }}</label>
            <div class="col-sm-10">
              <input type="text" name="entry_status" value="{{ entry_status }}" placeholder="{{ text_entry_status }}" id="input-entry_status" class="form-control" />
              {% if error_entry_status %}
                <div class="invalid-feedback">{{ error_entry_status }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-gdinfo">{{ text_gdinfo }}</label>
            <div class="col-sm-10">
              <input type="text" name="gdinfo" value="{{ gdinfo }}" placeholder="{{ text_gdinfo }}" id="input-gdinfo" class="form-control" />
              {% if error_gdinfo %}
                <div class="invalid-feedback">{{ error_gdinfo }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="invalid-feedback">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-help_cookie">{{ text_help_cookie }}</label>
            <div class="col-sm-10">
              <input type="text" name="help_cookie" value="{{ help_cookie }}" placeholder="{{ text_help_cookie }}" id="input-help_cookie" class="form-control" />
              {% if error_help_cookie %}
                <div class="invalid-feedback">{{ error_help_cookie }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-help_gd">{{ text_help_gd }}</label>
            <div class="col-sm-10">
              <input type="text" name="help_gd" value="{{ help_gd }}" placeholder="{{ text_help_gd }}" id="input-help_gd" class="form-control" />
              {% if error_help_gd %}
                <div class="invalid-feedback">{{ error_help_gd }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-help_quality">{{ text_help_quality }}</label>
            <div class="col-sm-10">
              <input type="text" name="help_quality" value="{{ help_quality }}" placeholder="{{ text_help_quality }}" id="input-help_quality" class="form-control" />
              {% if error_help_quality %}
                <div class="invalid-feedback">{{ error_help_quality }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-module_webpimages_cookie">{{ text_module_webpimages_cookie }}</label>
            <div class="col-sm-10">
              <input type="text" name="module_webpimages_cookie" value="{{ module_webpimages_cookie }}" placeholder="{{ text_module_webpimages_cookie }}" id="input-module_webpimages_cookie" class="form-control" />
              {% if error_module_webpimages_cookie %}
                <div class="invalid-feedback">{{ error_module_webpimages_cookie }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-module_webpimages_quality">{{ text_module_webpimages_quality }}</label>
            <div class="col-sm-10">
              <input type="text" name="module_webpimages_quality" value="{{ module_webpimages_quality }}" placeholder="{{ text_module_webpimages_quality }}" id="input-module_webpimages_quality" class="form-control" />
              {% if error_module_webpimages_quality %}
                <div class="invalid-feedback">{{ error_module_webpimages_quality }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-module_webpimages_status">{{ text_module_webpimages_status }}</label>
            <div class="col-sm-10">
              <input type="text" name="module_webpimages_status" value="{{ module_webpimages_status }}" placeholder="{{ text_module_webpimages_status }}" id="input-module_webpimages_status" class="form-control" />
              {% if error_module_webpimages_status %}
                <div class="invalid-feedback">{{ error_module_webpimages_status }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_alert">{{ text_text_alert }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_alert" value="{{ text_alert }}" placeholder="{{ text_text_alert }}" id="input-text_alert" class="form-control" />
              {% if error_text_alert %}
                <div class="invalid-feedback">{{ error_text_alert }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_edit">{{ text_text_edit }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_edit" value="{{ text_edit }}" placeholder="{{ text_text_edit }}" id="input-text_edit" class="form-control" />
              {% if error_text_edit %}
                <div class="invalid-feedback">{{ error_text_edit }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_success">{{ text_text_success }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_success" value="{{ text_success }}" placeholder="{{ text_text_success }}" id="input-text_success" class="form-control" />
              {% if error_text_success %}
                <div class="invalid-feedback">{{ error_text_success }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_version">{{ text_text_version }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_version" value="{{ text_version }}" placeholder="{{ text_text_version }}" id="input-text_version" class="form-control" />
              {% if error_text_version %}
                <div class="invalid-feedback">{{ error_text_version }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-token">{{ text_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="token" value="{{ token }}" placeholder="{{ text_token }}" id="input-token" class="form-control" />
              {% if error_token %}
                <div class="invalid-feedback">{{ error_token }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="invalid-feedback">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-webp">{{ text_webp }}</label>
            <div class="col-sm-10">
              <input type="text" name="webp" value="{{ webp }}" placeholder="{{ text_webp }}" id="input-webp" class="form-control" />
              {% if error_webp %}
                <div class="invalid-feedback">{{ error_webp }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}