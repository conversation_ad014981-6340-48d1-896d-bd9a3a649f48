{{ header }}
<div id="account-order" class="container">
  <div class="row">{{ column_left }}
    <div id="content" class="col" style="padding-left:10px;padding-right:10px;padding-top:10px">{{ content_top }}
      {% if orders %}
        <div class="table-responsive">
          <table class="table table-bordered table-hover">
            <thead>
              <tr>
                <td class="text-end">{{ column_order_id }}</td>
                <td class="text-start">{{ column_customer }}</td>
                <td class="text-end">{{ column_product }}</td>
                <td class="text-start">{{ column_status }}</td>
                <td class="text-end">{{ column_total }}</td>
                <td class="text-start">{{ column_date_added }}</td>
                <td></td>
              </tr>
            </thead>
            <tbody>
              {% for order in orders %}
                <tr>
                  <td class="text-end">#{{ order.order_id }}</td>
                  <td class="text-start">{{ order.name }}</td>
                  <td class="text-end">{{ order.products }}</td>
                  <td class="text-start">{{ order.status }}</td>
                  <td class="text-end">{{ order.total }}</td>
                  <td class="text-start">{{ order.date_added }}</td>
                  <td class="text-end"><a href="{{ order.view }}" data-bs-toggle="tooltip" title="{{ button_view }}" class="btn btn-info"><i class="fa-solid fa-eye"></i></a></td>
                </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
        <div class="row mb-3">
          <div class="col-sm-6 text-start">{{ pagination }}</div>
          <div class="col-sm-6 text-end">{{ results }}</div>
        </div>
      {% else %}
        <p>{{ text_no_results }}</p>
      {% endif %}
      <div class="text-end"><a href="{{ continue }}" class="btn btn-primary">{{ button_continue }}</a></div>
      {{ content_bottom }}</div>
    {{ column_right }}</div>
</div>
{{ footer }}
