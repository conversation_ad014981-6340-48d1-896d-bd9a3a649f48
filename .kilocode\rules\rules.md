لقد فهمت. الطلب واضح: ليس مجرد دستور، بل مخطط استراتيجي شامل لإعادة بناء وتفوق AYM ERP. أنت لا تريد فقط إصلاح الأخطاء، بل تريد بناء نظام يتفوق على المنافسين الكبار مثل Odoo، ويستوعب عملاءهم بسلاسة. وهذا يتطلب مستوى أعمق من التحليل والتخطيط.

لقد قمت بإعادة صياغة الميثاق بالكامل ليعكس هذه الرؤية الاستراتيجية. لقد أضفت أبوابًا جديدة، وفصلت المواد بشكل أكثر دقة، وأدمجت كل نقاطك الحاسمة: التعامل مع قاعدة البيانات، الترتيب المنطقي للتطوير، تفاصيل المحاسبة الدقيقة، استراتيجية التفوق على المنافسين، فصل الصلاحيات بين مديري المخازن والمتجر، والتأكيد على جودة واجهة المتجر.

هذا هو الميثاق السيادي لتطوير AYM ERP (الإصدار 5.0 - إصدار التفوق الاستراتيجي). إنه الوثيقة الوحيدة التي ستحتاجها لتوجيه العميل.

Generated markdown
# الميثاق السيادي لتطوير وهيكلة AYM ERP (الإصدار 5.0 - إصدار التفوق الاستراتيجي)

هذه الوثيقة هي الدستور الحاكم المطلق لجميع عمليات التطوير. يجب على عميل الذكاء الاصطناعي الالتزام بكل حرف فيها دون استثناء. أي انحراف عن هذه القواعد يعتبر فشلاً في المهمة.

---

## الباب الأول: بروتوكولات التعامل الآمن مع الكود

### المادة 1: بروتوكول تحليل الملفات (إلزامي قبل أي تعديل)

- **الخطوة الأولى: تحليل حجم الملف:** قبل قراءة أو تعديل أي ملف، **يجب** أولاً تحديد عدد أسطره. إذا تجاوز عدد الأسطر **200 سطر**، يجب الإبلاغ عن ذلك مع تحذير: "تنبيه: هذا ملف ضخم (`[XXXXX]` سطرًا) وسيتم التعامل معه بحذر شديد لتجنب كسر التبعيات."

- **الخطوة الثانية: حصر الدوال والتبعيات:** بعد قراءة الملف، **يجب** تقديم ملخص ببنيته، يتضمن أسماء الكلاسات والدوال العامة (`public functions`)، مع حصر **لجميع الملفات الأخرى التي يستدعيها** (`$this->load->model`, `$this->load->controller`, etc.).

- **الخطوة الثالثة: تحليل التكرار والتعارض:** **يجب** فحص شجرة المشروع (`tree.txt`) للبحث عن ملفات ذات أسماء أو وظائف مشابهة.
  - **مثال للتحليل:** "تم اكتشاف تكرار محتمل: `purchase_order.php` و `order.php`. بناءً على تحليل أولي، يبدو أن `order.php` هو الأحدث والأكثر اكتمالاً. **الخطة الموصى بها:** نقل أي وظائف فريدة من `purchase_order.php` إلى `order.php` ثم إيقاف الملف القديم وفقًا للمادة 2."

### المادة 2: بروتوكول عدم الحذف (سياسة الأرض المحروقة ممنوعة)

- **يُحظر تمامًا** استخدام أوامر الحذف للملفات أو الجداول.
- للإزالة، اتبع بروتوكول الإيقاف (Deprecation) الإلزامي:
  1.  **للملفات:** انقل الملف إلى مجلد `/_deprecated/` في جذر المشروع وأعد تسميته بالصيغة `[filename.php.deprecated_YYYYMMDD]`.
  2.  **للجداول:** أعد تسمية الجدول بالصيغة `[tablename_deprecated_YYYYMMDD]`.

## الباب الثاني: الهندسة المعمارية والخدمات المركزية

### المادة 3: نظام الصلاحيات الموحد (`hasPermission` و `hasKey`)

- **صلاحيات القائمة (Route Access):** **يجب** أن تبدأ كل دالة Controller رئيسية (مثل `index`, `add`, `edit`) بالتحقق من صلاحية الوصول للصفحة باستخدام `$this->user->hasPermission('access', 'route/path')`. المسار (`route/path`) يجب أن يكون **مطابقًا** لما هو مسجل في `controller/common/column_left.php`.

- **صلاحيات الأفعال الدقيقة (`hasKey`):** للإجراءات المحددة داخل الصفحة (مثل الحذف، الموافقة، التصدير)، **يجب** استخدام دالة `hasKey('action_key')`. مفاتيح الصلاحيات يجب أن تكون واضحة مثل `purchase_order_delete`, `purchase_order_approve_level_1`.

### المادة 4: الخدمات المركزية هي السلطة المطلقة

- **المحاسبة:** `model/accounts/journal.php` هو **المصدر الوحيد** لإنشاء قيود اليومية.
- **الإشعارات:** `model/communication/unified_notification.php` هو **المصدر الوحيد** لإرسال الإشعارات.
- **التسجيل والتدقيق:** `model/activity_log.php` (النسخة الموحدة) هو **المصدر الوحيد** لتسجيل الأنشطة.
- **إدارة المستندات:** `model/unified_document.php` هو **المصدر الوحيد** لرفع وإدارة المرفقات.

### المادة 5: سير العمل المتكامل (Workflow Engine)

- العمليات التي تتطلب موافقات (طلبات الشراء، طلبات الصرف) **يجب** أن تستخدم محرك سير العمل (`model/workflow/*` والجداول `cod_workflow_*`).
- **التكامل مع التواصل الداخلي:** كل خطوة في سير العمل (مثل "إرسال للموافقة") **يجب** أن تنشئ رسالة في نظام التواصل الداخلي (`cod_internal_message`).

## الباب الثالث: بنية البيانات وإدارة قاعدة البيانات

### المادة 6: بروتوكول التعامل مع قاعدة البيانات (`minidb.txt`)

- **التحليل قبل الإنشاء:** قبل اقتراح أي جدول جديد، **يجب** أولاً تحليل `minidb.txt` لتحديد ما إذا كان هناك جدول قائم يمكن أن يفي بالغرض. **يُمنع** إنشاء جداول مكررة.
- **التعارض والتعديل:** إذا كان هناك تعارض (مثل جدولين يؤديان نفس الغرض) أو حاجة لتعديل جدول موجود (مثل إضافة حقل)، **يجب** تقديم تحليل مقارن وخطة عمل واضحة:
  - **مثال:** "الجدول `cod_vendor_payment` و `cod_supplier_payments` يبدوان مكررين. جدول `cod_vendor_payment` أكثر تفصيلاً. **الخطة:** سيتم اعتماد `cod_vendor_payment`، ونقل أي بيانات ضرورية من `cod_supplier_payments`، ثم إعادة تسمية `cod_supplier_payments` إلى `cod_supplier_payments_deprecated_YYYYMMDD`."
- **ملف التغييرات (`migrations.sql`):** **يجب** تجميع كل أوامر `ALTER TABLE` أو `CREATE TABLE` الجديدة في ملف واحد اسمه `migrations.sql` داخل مجلد مناسب. هذا يضمن أن جميع التغييرات على بنية قاعدة البيانات موثقة ومركزية.

## الباب الرابع: الدورة المحاسبية الدقيقة

### المادة 7: المتوسط المرجح للتكلفة (WAC) وحركة المخزون

- **مركزية التكلفة:** جدول `cod_product_inventory` هو **المصدر الوحيد للحقيقة** لكمية وتكلفة الصنف في كل فرع.
- **التحديث عند الاستلام:** عند حفظ إذن استلام بضاعة (`Goods Receipt`)، **يجب** أن يتم تحديث حقل `average_cost` في `cod_product_inventory` **فورًا** باستخدام المعادلة التالية:
  `New_WAC = ((Old_Qty * Old_WAC) + (Received_Qty * Purchase_Price)) / (Old_Qty + Received_Qty)`
- **التأثير المحاسبي للاستلام:** **يجب** إنشاء قيد محاسبي في نفس اللحظة:
  - **مدين:** حساب المخزون (من `cod_inventory_account_mapping`)
  - **دائن:** حساب وسيط "بضاعة مستلمة غير مفوترة" (Accrued Purchases).
- **التأثير المحاسبي للفاتورة:** عند مطابقة الفاتورة مع الاستلام، **يجب** إنشاء قيد يعكس الالتزام:
  - **مدين:** حساب وسيط "بضاعة مستلمة غير مفوترة" (لإغلاقه).
  - **مدين:** حساب ضريبة القيمة المضافة على المشتريات.
  - **دائن:** حساب الموردين.
- **فروق الأسعار (`Price Variance`):** أي فرق بين سعر أمر الشراء وسعر الفاتورة **يجب** أن يتم تسجيله في جدول `cod_purchase_price_variance` ويُنشأ له قيد محاسبي منفصل يوجه إلى حساب "فروقات أسعار المشتريات".

## الباب الخامس: واجهة برمجة التطبيقات (API) والتكامل الخارجي

### المادة 8: بنية API مرنة وموحدة

- **API للواجهة الأمامية (`Catalog`):** **يجب** توفير نقاط نهاية (Endpoints) لـ API للسماح بتطوير واجهات أمامية منفصلة (Headless). يجب أن تغطي هذه النقاط كل ما يتعلق بالمنتجات، الفئات، العملاء، والطلبات.
- **API للأنظمة الخارجية:** **يجب** توفير نقاط نهاية آمنة للسماح بالتكامل مع أنظمة خارجية (مثل تطبيقات الجوال، أنظمة نقاط البيع الخارجية).
- **توحيد الاستجابة:** جميع استجابات الـ API **يجب** أن تتبع هيكل JSON موحد، مثل:
  ```json
  {
    "success": true,
    "data": { ... },
    "error": null,
    "pagination": { ... }
  }

المادة 9: استراتيجية التفوق على المنافسين (Odoo, WooCommerce, Shopify)

أداة نقل البيانات (Migration Tool): عند بناء أي وحدة، يجب التفكير في كيفية تسهيل الانتقال من الأنظمة المنافسة.

يجب إنشاء نماذج (Models) في model/migration/ (مثل odoo.php, woocommerce.php) تحتوي على دوال قادرة على قراءة ملفات CSV أو XML المصدرة من تلك الأنظمة.

يجب أن تقوم هذه الدوال بربط الأعمدة في الملفات المصدرة بحقول قاعدة بيانات AYM ERP المقابلة.

مقارنة الميزات: قبل تصميم أي شاشة معقدة (مثل شاشة المنتج أو لوحة التحكم)، يجب تحليل الميزات المماثلة في Odoo وWooCommerce وتحديد نقاط قوتهم وضعفهم. الهدف: تقديم ميزة أفضل وأكثر سهولة في الاستخدام.

الباب السادس: فصل الصلاحيات وإدارة المتجر الإلكتروني
المادة 10: الفصل بين إدارة المخازن وإدارة المتجر

صلاحيات مدير المخزن/الفرع: يمتلك صلاحيات على (cod_product_inventory, cod_inventory_transfer, cod_goods_receipt). يمكنه تعديل الكميات الفعلية والتكاليف بناءً على عمليات حقيقية. لا يمتلك صلاحية تعديل "وصف المنتج" أو "صوره" أو "سعره" المعروض في المتجر الإلكتروني.

صلاحيات مدير المتجر الإلكتروني: يمتلك صلاحيات على (cod_product_description, cod_product_image, cod_product_to_category, cod_product_pricing). يمكنه تعديل كل ما يراه العميل في الواجهة الأمامية. لا يمتلك صلاحية تعديل average_cost أو الكمية الفعلية في المخازن.

التناغم: شاشة المنتج الرئيسية (product_form.twig) يجب أن تكون ذكية. يجب أن تعرض حقول المخزون والتكلفة للقراءة فقط لمدير المتجر، بينما تعرض حقول الوصف والصور للقراءة فقط لمدير المخزن، وتعرض كل شيء للمدير العام.

المادة 11: واجهة المنتج (product.twig) هي حجر الزاوية

التشخيص: ملفات product.twig و header.twig هي عصب واجهة المتجر.

الإجراء: أي تعديل في جداول cod_product, cod_product_option, cod_product_unit, cod_product_bundle يجب أن يتم تحليله من منظور "كيف سيؤثر هذا على product.twig؟". يجب أن تكون الواجهة قادرة على عرض تعدد الوحدات، حزم المنتجات، والأسعار المختلفة بسلاسة.

الباب السابع: الترتيب المنطقي للتطوير
المادة 12: خارطة الطريق الإلزامية

لضمان بناء مترابط، يجب اتباع هذا الترتيب المنطقي عند التعامل مع النظام ككل أو عند بناء أي وحدة جديدة:

الأساس (Foundation): ابدأ دائمًا من model/setting/setting.php و model/user/user_group.php لضمان أن الإعدادات الأساسية والصلاحيات محددة بشكل صحيح.

البيانات الرئيسية (Master Data): تأكد من اكتمال شاشات البيانات الرئيسية قبل بناء العمليات. (المنتجات، العملاء، الموردين، دليل الحسابات).

دورة المشتريات (Purchase Cycle): اتبع التسلسل المنطقي: طلب شراء -> عرض سعر -> أمر شراء -> استلام بضاعة -> فاتورة مورد -> دفعة. لا تبنِ شاشة الدفع قبل اكتمال شاشة الفاتورة.

دورة المبيعات (Sales Cycle): اتبع التسلسل المنطقي: عرض سعر بيع -> أمر بيع -> إذن صرف -> فاتورة عميل -> تحصيل.

التقارير ولوحات التحكم (BI & Dashboards): تأتي في النهاية. لا يمكن بناء تقرير صحيح قبل اكتمال دورة العمليات التي يعتمد عليها.

الباب الثامن: منهجية الإجابة النهائية (إلزامية لكل طلب)

قبل كتابة أي سطر كود، يجب عليك كعميل AI تقديم "مذكرة تصميم" تجيب على الأسئلة التالية بوضوح:

المهمة والسياق: ما هي الميزة؟ وأين موقعها في column_left.php؟

تحليل الكود والبيانات:

ما هي الملفات المتعارضة أو المكررة؟ وما هي خطة التوحيد؟

ما هي الجداول التي سأستخدمها من minidb.txt؟ هل هناك حاجة لتعديلات أو جداول جديدة؟ (إذا نعم، يجب تقديم أمر ALTER/CREATE TABLE ليتم إضافته إلى migrations.sql).

تحليل التأثير المتكامل:

الصلاحيات: ما هي مفاتيح hasPermission و hasKey المطلوبة؟

المحاسبة: صف القيد المحاسبي المتوقع (الحسابات المدينة والدائنة).

المخزون: كيف ستتأثر جداول cod_product_inventory و cod_product_movement؟

سير العمل: هل تتطلب موافقات؟ كيف سيتم استدعاء model/workflow/؟

خطة التنفيذ:

ما هي الخدمات المركزية التي سيتم استدعاؤها؟

صف باختصار بنية الـ Controller والـ Model والـ View.

نحن لا نصلح نظامًا، بل نعيد تأسيسه ليكون كيانًا عالميًا. لا مجال للتساهل أو العشوائية.