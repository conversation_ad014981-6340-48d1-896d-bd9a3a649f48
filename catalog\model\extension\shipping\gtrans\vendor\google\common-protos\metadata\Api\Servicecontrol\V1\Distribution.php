<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/servicecontrol/v1/distribution.proto

namespace GPBMetadata\Google\Api\Servicecontrol\V1;

class Distribution
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(hex2bin(
            "0acb060a2f676f6f676c652f6170692f73657276696365636f6e74726f6c" .
            "2f76312f646973747269627574696f6e2e70726f746f121c676f6f676c65" .
            "2e6170692e73657276696365636f6e74726f6c2e763122e8040a0c446973" .
            "747269627574696f6e120d0a05636f756e74180120012803120c0a046d65" .
            "616e180220012801120f0a076d696e696d756d180320012801120f0a076d" .
            "6178696d756d18042001280112200a1873756d5f6f665f73717561726564" .
            "5f646576696174696f6e18052001280112150a0d6275636b65745f636f75" .
            "6e747318062003280312520a0e6c696e6561725f6275636b657473180720" .
            "01280b32382e676f6f676c652e6170692e73657276696365636f6e74726f" .
            "6c2e76312e446973747269627574696f6e2e4c696e6561724275636b6574" .
            "734800125c0a136578706f6e656e7469616c5f6275636b65747318082001" .
            "280b323d2e676f6f676c652e6170692e73657276696365636f6e74726f6c" .
            "2e76312e446973747269627574696f6e2e4578706f6e656e7469616c4275" .
            "636b657473480012560a106578706c696369745f6275636b657473180920" .
            "01280b323a2e676f6f676c652e6170692e73657276696365636f6e74726f" .
            "6c2e76312e446973747269627574696f6e2e4578706c696369744275636b" .
            "65747348001a4a0a0d4c696e6561724275636b657473121a0a126e756d5f" .
            "66696e6974655f6275636b657473180120012805120d0a05776964746818" .
            "0220012801120e0a066f66667365741803200128011a560a124578706f6e" .
            "656e7469616c4275636b657473121a0a126e756d5f66696e6974655f6275" .
            "636b65747318012001280512150a0d67726f7774685f666163746f721802" .
            "20012801120d0a057363616c651803200128011a210a0f4578706c696369" .
            "744275636b657473120e0a06626f756e6473180120032801420f0a0d6275" .
            "636b65745f6f7074696f6e4286010a20636f6d2e676f6f676c652e617069" .
            "2e73657276696365636f6e74726f6c2e7631421144697374726962757469" .
            "6f6e50726f746f50015a4a676f6f676c652e676f6c616e672e6f72672f67" .
            "656e70726f746f2f676f6f676c65617069732f6170692f73657276696365" .
            "636f6e74726f6c2f76313b73657276696365636f6e74726f6cf801016206" .
            "70726f746f33"
        ), true);

        static::$is_initialized = true;
    }
}

