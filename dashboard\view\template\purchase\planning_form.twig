{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="submit" form="purchase\planning-form" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fas fa-save"></i></button>
        <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-light"><i class="fas fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    
    <div class="card">
      <div class="card-header"><i class="fas fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}chart-bar{% elif screen_type == 'settings' %}cog{% else %}pencil-alt{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="card-body">
        <form id="purchase\planning-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-add">{{ text_add }}</label>
            <div class="col-sm-10">
              <input type="text" name="add" value="{{ add }}" placeholder="{{ text_add }}" id="input-add" class="form-control" />
              {% if error_add %}
                <div class="invalid-feedback">{{ error_add }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-budget_analysis">{{ text_budget_analysis }}</label>
            <div class="col-sm-10">
              <input type="text" name="budget_analysis" value="{{ budget_analysis }}" placeholder="{{ text_budget_analysis }}" id="input-budget_analysis" class="form-control" />
              {% if error_budget_analysis %}
                <div class="invalid-feedback">{{ error_budget_analysis }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-categories">{{ text_categories }}</label>
            <div class="col-sm-10">
              <input type="text" name="categories" value="{{ categories }}" placeholder="{{ text_categories }}" id="input-categories" class="form-control" />
              {% if error_categories %}
                <div class="invalid-feedback">{{ error_categories }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-date_end">{{ text_date_end }}</label>
            <div class="col-sm-10">
              <input type="text" name="date_end" value="{{ date_end }}" placeholder="{{ text_date_end }}" id="input-date_end" class="form-control" />
              {% if error_date_end %}
                <div class="invalid-feedback">{{ error_date_end }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-date_start">{{ text_date_start }}</label>
            <div class="col-sm-10">
              <input type="text" name="date_start" value="{{ date_start }}" placeholder="{{ text_date_start }}" id="input-date_start" class="form-control" />
              {% if error_date_start %}
                <div class="invalid-feedback">{{ error_date_start }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-delete">{{ text_delete }}</label>
            <div class="col-sm-10">
              <input type="text" name="delete" value="{{ delete }}" placeholder="{{ text_delete }}" id="input-delete" class="form-control" />
              {% if error_delete %}
                <div class="invalid-feedback">{{ error_delete }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-end_date">{{ text_end_date }}</label>
            <div class="col-sm-10">
              <input type="text" name="end_date" value="{{ end_date }}" placeholder="{{ text_end_date }}" id="input-end_date" class="form-control" />
              {% if error_end_date %}
                <div class="invalid-feedback">{{ error_end_date }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_end_date">{{ text_error_end_date }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_end_date" value="{{ error_end_date }}" placeholder="{{ text_error_end_date }}" id="input-error_end_date" class="form-control" />
              {% if error_error_end_date %}
                <div class="invalid-feedback">{{ error_error_end_date }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_plan_name">{{ text_error_plan_name }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_plan_name" value="{{ error_plan_name }}" placeholder="{{ text_error_plan_name }}" id="input-error_plan_name" class="form-control" />
              {% if error_error_plan_name %}
                <div class="invalid-feedback">{{ error_error_plan_name }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_start_date">{{ text_error_start_date }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_start_date" value="{{ error_start_date }}" placeholder="{{ text_error_start_date }}" id="input-error_start_date" class="form-control" />
              {% if error_error_start_date %}
                <div class="invalid-feedback">{{ error_error_start_date }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_total_budget">{{ text_error_total_budget }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_total_budget" value="{{ error_total_budget }}" placeholder="{{ text_error_total_budget }}" id="input-error_total_budget" class="form-control" />
              {% if error_error_total_budget %}
                <div class="invalid-feedback">{{ error_error_total_budget }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_name">{{ text_filter_name }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_name" value="{{ filter_name }}" placeholder="{{ text_filter_name }}" id="input-filter_name" class="form-control" />
              {% if error_filter_name %}
                <div class="invalid-feedback">{{ error_filter_name }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_period">{{ text_filter_period }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_period" value="{{ filter_period }}" placeholder="{{ text_filter_period }}" id="input-filter_period" class="form-control" />
              {% if error_filter_period %}
                <div class="invalid-feedback">{{ error_filter_period }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_status">{{ text_filter_status }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_status" value="{{ filter_status }}" placeholder="{{ text_filter_status }}" id="input-filter_status" class="form-control" />
              {% if error_filter_status %}
                <div class="invalid-feedback">{{ error_filter_status }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="invalid-feedback">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-notes">{{ text_notes }}</label>
            <div class="col-sm-10">
              <input type="text" name="notes" value="{{ notes }}" placeholder="{{ text_notes }}" id="input-notes" class="form-control" />
              {% if error_notes %}
                <div class="invalid-feedback">{{ error_notes }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-order">{{ text_order }}</label>
            <div class="col-sm-10">
              <input type="text" name="order" value="{{ order }}" placeholder="{{ text_order }}" id="input-order" class="form-control" />
              {% if error_order %}
                <div class="invalid-feedback">{{ error_order }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-pagination">{{ text_pagination }}</label>
            <div class="col-sm-10">
              <input type="text" name="pagination" value="{{ pagination }}" placeholder="{{ text_pagination }}" id="input-pagination" class="form-control" />
              {% if error_pagination %}
                <div class="invalid-feedback">{{ error_pagination }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-performance_metrics">{{ text_performance_metrics }}</label>
            <div class="col-sm-10">
              <input type="text" name="performance_metrics" value="{{ performance_metrics }}" placeholder="{{ text_performance_metrics }}" id="input-performance_metrics" class="form-control" />
              {% if error_performance_metrics %}
                <div class="invalid-feedback">{{ error_performance_metrics }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-periods">{{ text_periods }}</label>
            <div class="col-sm-10">
              <input type="text" name="periods" value="{{ periods }}" placeholder="{{ text_periods }}" id="input-periods" class="form-control" />
              {% if error_periods %}
                <div class="invalid-feedback">{{ error_periods }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-plan">{{ text_plan }}</label>
            <div class="col-sm-10">
              <input type="text" name="plan" value="{{ plan }}" placeholder="{{ text_plan }}" id="input-plan" class="form-control" />
              {% if error_plan %}
                <div class="invalid-feedback">{{ error_plan }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-plan_analytics">{{ text_plan_analytics }}</label>
            <div class="col-sm-10">
              <input type="text" name="plan_analytics" value="{{ plan_analytics }}" placeholder="{{ text_plan_analytics }}" id="input-plan_analytics" class="form-control" />
              {% if error_plan_analytics %}
                <div class="invalid-feedback">{{ error_plan_analytics }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-plan_description">{{ text_plan_description }}</label>
            <div class="col-sm-10">
              <input type="text" name="plan_description" value="{{ plan_description }}" placeholder="{{ text_plan_description }}" id="input-plan_description" class="form-control" />
              {% if error_plan_description %}
                <div class="invalid-feedback">{{ error_plan_description }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-plan_id">{{ text_plan_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="plan_id" value="{{ plan_id }}" placeholder="{{ text_plan_id }}" id="input-plan_id" class="form-control" />
              {% if error_plan_id %}
                <div class="invalid-feedback">{{ error_plan_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-plan_items">{{ text_plan_items }}</label>
            <div class="col-sm-10">
              <input type="text" name="plan_items" value="{{ plan_items }}" placeholder="{{ text_plan_items }}" id="input-plan_items" class="form-control" />
              {% if error_plan_items %}
                <div class="invalid-feedback">{{ error_plan_items }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-plan_name">{{ text_plan_name }}</label>
            <div class="col-sm-10">
              <input type="text" name="plan_name" value="{{ plan_name }}" placeholder="{{ text_plan_name }}" id="input-plan_name" class="form-control" />
              {% if error_plan_name %}
                <div class="invalid-feedback">{{ error_plan_name }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-plan_period">{{ text_plan_period }}</label>
            <div class="col-sm-10">
              <input type="text" name="plan_period" value="{{ plan_period }}" placeholder="{{ text_plan_period }}" id="input-plan_period" class="form-control" />
              {% if error_plan_period %}
                <div class="invalid-feedback">{{ error_plan_period }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-plan_progress">{{ text_plan_progress }}</label>
            <div class="col-sm-10">
              <input type="text" name="plan_progress" value="{{ plan_progress }}" placeholder="{{ text_plan_progress }}" id="input-plan_progress" class="form-control" />
              {% if error_plan_progress %}
                <div class="invalid-feedback">{{ error_plan_progress }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-planning_report">{{ text_planning_report }}</label>
            <div class="col-sm-10">
              <input type="text" name="planning_report" value="{{ planning_report }}" placeholder="{{ text_planning_report }}" id="input-planning_report" class="form-control" />
              {% if error_planning_report %}
                <div class="invalid-feedback">{{ error_planning_report }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-planning_statistics">{{ text_planning_statistics }}</label>
            <div class="col-sm-10">
              <input type="text" name="planning_statistics" value="{{ planning_statistics }}" placeholder="{{ text_planning_statistics }}" id="input-planning_statistics" class="form-control" />
              {% if error_planning_statistics %}
                <div class="invalid-feedback">{{ error_planning_statistics }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-plans">{{ text_plans }}</label>
            <div class="col-sm-10">
              <input type="text" name="plans" value="{{ plans }}" placeholder="{{ text_plans }}" id="input-plans" class="form-control" />
              {% if error_plans %}
                <div class="invalid-feedback">{{ error_plans }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-products">{{ text_products }}</label>
            <div class="col-sm-10">
              <input type="text" name="products" value="{{ products }}" placeholder="{{ text_products }}" id="input-products" class="form-control" />
              {% if error_products %}
                <div class="invalid-feedback">{{ error_products }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-results">{{ text_results }}</label>
            <div class="col-sm-10">
              <input type="text" name="results" value="{{ results }}" placeholder="{{ text_results }}" id="input-results" class="form-control" />
              {% if error_results %}
                <div class="invalid-feedback">{{ error_results }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-selected">{{ text_selected }}</label>
            <div class="col-sm-10">
              <input type="text" name="selected" value="{{ selected }}" placeholder="{{ text_selected }}" id="input-selected" class="form-control" />
              {% if error_selected %}
                <div class="invalid-feedback">{{ error_selected }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort">{{ text_sort }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort" value="{{ sort }}" placeholder="{{ text_sort }}" id="input-sort" class="form-control" />
              {% if error_sort %}
                <div class="invalid-feedback">{{ error_sort }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_budget">{{ text_sort_budget }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_budget" value="{{ sort_budget }}" placeholder="{{ text_sort_budget }}" id="input-sort_budget" class="form-control" />
              {% if error_sort_budget %}
                <div class="invalid-feedback">{{ error_sort_budget }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_name">{{ text_sort_name }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_name" value="{{ sort_name }}" placeholder="{{ text_sort_name }}" id="input-sort_name" class="form-control" />
              {% if error_sort_name %}
                <div class="invalid-feedback">{{ error_sort_name }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_period">{{ text_sort_period }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_period" value="{{ sort_period }}" placeholder="{{ text_sort_period }}" id="input-sort_period" class="form-control" />
              {% if error_sort_period %}
                <div class="invalid-feedback">{{ error_sort_period }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_status">{{ text_sort_status }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_status" value="{{ sort_status }}" placeholder="{{ text_sort_status }}" id="input-sort_status" class="form-control" />
              {% if error_sort_status %}
                <div class="invalid-feedback">{{ error_sort_status }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-start_date">{{ text_start_date }}</label>
            <div class="col-sm-10">
              <input type="text" name="start_date" value="{{ start_date }}" placeholder="{{ text_start_date }}" id="input-start_date" class="form-control" />
              {% if error_start_date %}
                <div class="invalid-feedback">{{ error_start_date }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-status">{{ text_status }}</label>
            <div class="col-sm-10">
              <input type="text" name="status" value="{{ status }}" placeholder="{{ text_status }}" id="input-status" class="form-control" />
              {% if error_status %}
                <div class="invalid-feedback">{{ error_status }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-statuses">{{ text_statuses }}</label>
            <div class="col-sm-10">
              <input type="text" name="statuses" value="{{ statuses }}" placeholder="{{ text_statuses }}" id="input-statuses" class="form-control" />
              {% if error_statuses %}
                <div class="invalid-feedback">{{ error_statuses }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_form">{{ text_text_form }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_form" value="{{ text_form }}" placeholder="{{ text_text_form }}" id="input-text_form" class="form-control" />
              {% if error_text_form %}
                <div class="invalid-feedback">{{ error_text_form }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-total_budget">{{ text_total_budget }}</label>
            <div class="col-sm-10">
              <input type="text" name="total_budget" value="{{ total_budget }}" placeholder="{{ text_total_budget }}" id="input-total_budget" class="form-control" />
              {% if error_total_budget %}
                <div class="invalid-feedback">{{ error_total_budget }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="invalid-feedback">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}