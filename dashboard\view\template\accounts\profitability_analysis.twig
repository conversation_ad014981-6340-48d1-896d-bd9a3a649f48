{{ header }}{{ column_left }}

<!-- Enterprise Grade Plus CSS for Profitability Analysis -->
<style>
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --danger-color: #e74c3c;
    --warning-color: #f39c12;
    --info-color: #17a2b8;
    --profit-color: #28a745;
    --loss-color: #dc3545;
    --light-bg: #f8f9fa;
    --border-color: #dee2e6;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

.profitability-container {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: var(--shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.profitability-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--profit-color), var(--primary-color), var(--secondary-color));
}

.profitability-header {
    text-align: center;
    border-bottom: 3px solid var(--profit-color);
    padding-bottom: 20px;
    margin-bottom: 30px;
    position: relative;
}

.profitability-header h2 {
    color: var(--profit-color);
    font-weight: 700;
    font-size: 1.8rem;
    margin: 0;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.profitability-kpis {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.profitability-kpi {
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    text-align: center;
    box-shadow: var(--shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.profitability-kpi:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0,0,0,0.1);
}

.profitability-kpi::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
}

.profitability-kpi.revenue::before { background: var(--info-color); }
.profitability-kpi.gross-profit::before { background: var(--success-color); }
.profitability-kpi.operating-profit::before { background: var(--warning-color); }
.profitability-kpi.net-profit::before { background: var(--profit-color); }
.profitability-kpi.roi::before { background: var(--secondary-color); }

.profitability-kpi h4 {
    color: var(--primary-color);
    margin-bottom: 10px;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.profitability-kpi .amount {
    font-size: 1.4rem;
    font-weight: 700;
    font-family: 'Courier New', monospace;
    margin-bottom: 5px;
}

.profitability-kpi .percentage {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 5px;
}

.profitability-kpi .description {
    font-size: 0.8rem;
    color: #6c757d;
    font-weight: 500;
}

.kpi-revenue .amount { color: var(--info-color); }
.kpi-gross-profit .amount { color: var(--success-color); }
.kpi-operating-profit .amount { color: var(--warning-color); }
.kpi-net-profit .amount { color: var(--profit-color); }
.kpi-roi .amount { color: var(--secondary-color); }

.profitability-charts {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.profitability-chart {
    background: #ffffff;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: var(--shadow);
}

.profitability-chart h4 {
    color: var(--primary-color);
    margin-bottom: 15px;
    font-weight: 600;
    text-align: center;
}

.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
}

.profitability-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    background: #ffffff;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.profitability-table th {
    background: linear-gradient(135deg, var(--profit-color), #1e7e34);
    color: white;
    padding: 15px 12px;
    text-align: center;
    font-weight: 600;
    font-size: 0.9rem;
    border-bottom: 2px solid var(--border-color);
}

.profitability-table td {
    padding: 12px;
    border-bottom: 1px solid var(--border-color);
    text-align: center;
    transition: var(--transition);
}

.profitability-table tbody tr:hover {
    background: var(--light-bg);
    transform: scale(1.005);
}

.profitability-table tbody tr:nth-child(even) {
    background: #fafbfc;
}

.amount-cell {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    text-align: {{ direction == 'rtl' ? 'right' : 'left' }};
}

.amount-positive { 
    color: var(--profit-color); 
    font-weight: 600;
}

.amount-negative { 
    color: var(--loss-color); 
    font-weight: 600;
}

.amount-neutral { 
    color: var(--secondary-color); 
    font-weight: 600;
}

.percentage-positive {
    color: var(--profit-color);
    font-weight: 600;
}

.percentage-negative {
    color: var(--loss-color);
    font-weight: 600;
}

/* Filter Panel */
.filter-panel {
    background: linear-gradient(135deg, #f8f9fa, #ffffff);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: var(--shadow);
}

.filter-panel h4 {
    color: var(--profit-color);
    margin-bottom: 15px;
    font-weight: 600;
}

.form-group {
    margin-bottom: 15px;
}

.form-label {
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 5px;
    display: block;
}

.form-control {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 8px 12px;
    transition: var(--transition);
    width: 100%;
}

.form-control:focus {
    border-color: var(--profit-color);
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    outline: none;
}

/* Trend Indicators */
.trend-indicator {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    font-size: 0.8rem;
    font-weight: 600;
}

.trend-up {
    color: var(--profit-color);
}

.trend-down {
    color: var(--loss-color);
}

.trend-neutral {
    color: #6c757d;
}

/* RTL Support */
[dir="rtl"] .profitability-table {
    direction: rtl;
}

[dir="rtl"] .amount-cell {
    text-align: right;
}

/* Print Styles */
@media print {
    .profitability-container {
        box-shadow: none;
        border: 1px solid #000;
    }
    
    .profitability-table th {
        background: #f0f0f0 !important;
        color: #000 !important;
    }
    
    .btn, .panel-heading, .filter-panel {
        display: none !important;
    }
    
    .profitability-charts {
        page-break-inside: avoid;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .profitability-table {
        font-size: 0.8rem;
    }
    
    .profitability-table th,
    .profitability-table td {
        padding: 8px 6px;
    }
    
    .profitability-kpis {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .profitability-charts {
        grid-template-columns: 1fr;
    }
}
</style>

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-{{ direction == 'rtl' ? 'start' : 'end' }}">
        <div class="btn-group" role="group" aria-label="{{ text_actions }}">
          <button type="button" class="btn btn-success btn-lg" onclick="generateReport()"
                  data-toggle="tooltip" title="{{ button_generate_report }}">
            <i class="fa fa-line-chart"></i> {{ button_generate }}
          </button>
          <div class="btn-group" role="group">
            <button type="button" class="btn btn-info dropdown-toggle" data-toggle="dropdown"
                    aria-expanded="false" data-toggle="tooltip" title="{{ text_export_options }}">
              <i class="fa fa-download"></i> {{ text_export }}
            </button>
            <ul class="dropdown-menu">
              <li><a class="dropdown-item" href="#" onclick="exportAnalysis('excel')">
                <i class="fa fa-file-excel text-success"></i> Excel
              </a></li>
              <li><a class="dropdown-item" href="#" onclick="exportAnalysis('pdf')">
                <i class="fa fa-file-pdf text-danger"></i> PDF
              </a></li>
              <li><a class="dropdown-item" href="#" onclick="exportAnalysis('csv')">
                <i class="fa fa-file-csv text-info"></i> CSV
              </a></li>
              <li><hr class="dropdown-divider"></li>
              <li><a class="dropdown-item" href="#" onclick="printAnalysis()">
                <i class="fa fa-print text-muted"></i> {{ text_print }}
              </a></li>
            </ul>
          </div>
          <button type="button" class="btn btn-primary" onclick="compareAnalysis()"
                  data-toggle="tooltip" title="{{ button_compare_periods }}">
            <i class="fa fa-balance-scale"></i>
          </button>
          <button type="button" class="btn btn-warning" onclick="forecastAnalysis()"
                  data-toggle="tooltip" title="{{ button_forecast }}">
            <i class="fa fa-magic"></i>
          </button>
        </div>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li class="breadcrumb-item"><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>

  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible">
      <i class="fa fa-exclamation-triangle"></i>
      {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}

    {% if success %}
    <div class="alert alert-success alert-dismissible">
      <i class="fa fa-check-circle"></i>
      {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}

    <!-- Filter Panel -->
    <div class="filter-panel">
      <h4>{{ text_analysis_filters }}</h4>
      <form id="profitability-filter-form" method="post">
        <div class="row">
          <div class="col-md-3">
            <div class="form-group">
              <label for="date_start" class="form-label">{{ entry_date_start }}</label>
              <input type="date" name="date_start" id="date_start" value="{{ date_start }}" class="form-control" required>
            </div>
          </div>
          <div class="col-md-3">
            <div class="form-group">
              <label for="date_end" class="form-label">{{ entry_date_end }}</label>
              <input type="date" name="date_end" id="date_end" value="{{ date_end }}" class="form-control" required>
            </div>
          </div>
          <div class="col-md-2">
            <div class="form-group">
              <label for="analysis_type" class="form-label">{{ entry_analysis_type }}</label>
              <select name="analysis_type" id="analysis_type" class="form-control">
                <option value="overall"{% if analysis_type == 'overall' %} selected{% endif %}>{{ text_overall_analysis }}</option>
                <option value="product"{% if analysis_type == 'product' %} selected{% endif %}>{{ text_product_analysis }}</option>
                <option value="customer"{% if analysis_type == 'customer' %} selected{% endif %}>{{ text_customer_analysis }}</option>
                <option value="category"{% if analysis_type == 'category' %} selected{% endif %}>{{ text_category_analysis }}</option>
              </select>
            </div>
          </div>
          <div class="col-md-2">
            <div class="form-group">
              <label for="comparison_period" class="form-label">{{ entry_comparison_period }}</label>
              <select name="comparison_period" id="comparison_period" class="form-control">
                <option value="none"{% if comparison_period == 'none' %} selected{% endif %}>{{ text_no_comparison }}</option>
                <option value="previous_period"{% if comparison_period == 'previous_period' %} selected{% endif %}>{{ text_previous_period }}</option>
                <option value="previous_year"{% if comparison_period == 'previous_year' %} selected{% endif %}>{{ text_previous_year }}</option>
                <option value="custom"{% if comparison_period == 'custom' %} selected{% endif %}>{{ text_custom_period }}</option>
              </select>
            </div>
          </div>
          <div class="col-md-2">
            <div class="form-group">
              <label class="form-label">&nbsp;</label>
              <div>
                <button type="submit" class="btn btn-primary btn-block">
                  <i class="fa fa-search"></i> {{ button_analyze }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>

    <!-- Profitability Analysis Content -->
    {% if analysis_data %}
    <!-- Key Performance Indicators -->
    <div class="profitability-kpis">
      <div class="profitability-kpi kpi-revenue revenue">
        <h4>{{ text_total_revenue }}</h4>
        <div class="amount">{{ analysis_data.revenue_formatted }}</div>
        <div class="trend-indicator trend-{{ analysis_data.revenue_trend }}">
          <i class="fa fa-arrow-{{ analysis_data.revenue_trend == 'up' ? 'up' : analysis_data.revenue_trend == 'down' ? 'down' : 'right' }}"></i>
          {{ analysis_data.revenue_change }}%
        </div>
        <div class="description">{{ text_revenue_description }}</div>
      </div>

      <div class="profitability-kpi kpi-gross-profit gross-profit">
        <h4>{{ text_gross_profit }}</h4>
        <div class="amount">{{ analysis_data.gross_profit_formatted }}</div>
        <div class="percentage">{{ analysis_data.gross_margin }}%</div>
        <div class="trend-indicator trend-{{ analysis_data.gross_profit_trend }}">
          <i class="fa fa-arrow-{{ analysis_data.gross_profit_trend == 'up' ? 'up' : analysis_data.gross_profit_trend == 'down' ? 'down' : 'right' }}"></i>
          {{ analysis_data.gross_profit_change }}%
        </div>
        <div class="description">{{ text_gross_profit_description }}</div>
      </div>

      <div class="profitability-kpi kpi-operating-profit operating-profit">
        <h4>{{ text_operating_profit }}</h4>
        <div class="amount">{{ analysis_data.operating_profit_formatted }}</div>
        <div class="percentage">{{ analysis_data.operating_margin }}%</div>
        <div class="trend-indicator trend-{{ analysis_data.operating_profit_trend }}">
          <i class="fa fa-arrow-{{ analysis_data.operating_profit_trend == 'up' ? 'up' : analysis_data.operating_profit_trend == 'down' ? 'down' : 'right' }}"></i>
          {{ analysis_data.operating_profit_change }}%
        </div>
        <div class="description">{{ text_operating_profit_description }}</div>
      </div>

      <div class="profitability-kpi kpi-net-profit net-profit">
        <h4>{{ text_net_profit }}</h4>
        <div class="amount">{{ analysis_data.net_profit_formatted }}</div>
        <div class="percentage">{{ analysis_data.net_margin }}%</div>
        <div class="trend-indicator trend-{{ analysis_data.net_profit_trend }}">
          <i class="fa fa-arrow-{{ analysis_data.net_profit_trend == 'up' ? 'up' : analysis_data.net_profit_trend == 'down' ? 'down' : 'right' }}"></i>
          {{ analysis_data.net_profit_change }}%
        </div>
        <div class="description">{{ text_net_profit_description }}</div>
      </div>

      <div class="profitability-kpi kpi-roi roi">
        <h4>{{ text_roi }}</h4>
        <div class="amount">{{ analysis_data.roi }}%</div>
        <div class="trend-indicator trend-{{ analysis_data.roi_trend }}">
          <i class="fa fa-arrow-{{ analysis_data.roi_trend == 'up' ? 'up' : analysis_data.roi_trend == 'down' ? 'down' : 'right' }}"></i>
          {{ analysis_data.roi_change }}%
        </div>
        <div class="description">{{ text_roi_description }}</div>
      </div>
    </div>

    <!-- Charts Section -->
    <div class="profitability-charts">
      <div class="profitability-chart">
        <h4>{{ text_profit_trend_chart }}</h4>
        <div class="chart-container">
          <canvas id="profitTrendChart"></canvas>
        </div>
      </div>

      <div class="profitability-chart">
        <h4>{{ text_margin_analysis_chart }}</h4>
        <div class="chart-container">
          <canvas id="marginAnalysisChart"></canvas>
        </div>
      </div>

      <div class="profitability-chart">
        <h4>{{ text_revenue_breakdown_chart }}</h4>
        <div class="chart-container">
          <canvas id="revenueBreakdownChart"></canvas>
        </div>
      </div>

      <div class="profitability-chart">
        <h4>{{ text_cost_analysis_chart }}</h4>
        <div class="chart-container">
          <canvas id="costAnalysisChart"></canvas>
        </div>
      </div>
    </div>
    {% else %}
    <div class="alert alert-info">
      <i class="fa fa-info-circle"></i>
      {{ text_no_data }}
    </div>
    {% endif %}
  </div>
</div>

<script>
// Enterprise Grade Plus JavaScript for Profitability Analysis
class ProfitabilityAnalysisManager {
    constructor() {
        this.charts = {};
        this.initializeTooltips();
        this.initializeKeyboardShortcuts();
        this.initializeCharts();
        this.initializeFormValidation();
    }

    initializeTooltips() {
        $('[data-toggle="tooltip"]').tooltip();
    }

    initializeKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case 'g':
                        e.preventDefault();
                        this.generateReport();
                        break;
                    case 'e':
                        e.preventDefault();
                        this.showExportMenu();
                        break;
                    case 'p':
                        e.preventDefault();
                        this.printAnalysis();
                        break;
                    case 'c':
                        e.preventDefault();
                        this.compareAnalysis();
                        break;
                    case 'f':
                        e.preventDefault();
                        this.forecastAnalysis();
                        break;
                }
            }
        });
    }

    initializeCharts() {
        // Chart initialization would go here
        console.log('Charts initialized');
    }

    initializeFormValidation() {
        const form = document.getElementById('profitability-filter-form');
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.validateAndSubmitForm();
            });
        }
    }

    validateAndSubmitForm() {
        const form = document.getElementById('profitability-filter-form');
        const formData = new FormData(form);

        // Validate date range
        const startDate = new Date(formData.get('date_start'));
        const endDate = new Date(formData.get('date_end'));

        if (startDate >= endDate) {
            this.showAlert('{{ error_invalid_date_range }}', 'danger');
            return;
        }

        // Submit form
        this.showLoadingState(true);
        form.submit();
    }

    generateReport() {
        this.showLoadingState(true);

        const formData = this.getFormData();

        fetch('{{ url_link('accounts/profitability_analysis', 'generate') }}', {
            method: 'POST',
            body: JSON.stringify(formData),
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            this.showLoadingState(false);
            if (data.success) {
                this.showAlert('{{ success_report_generated }}', 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                this.showAlert(data.error || '{{ error_report_generation }}', 'danger');
            }
        })
        .catch(error => {
            this.showLoadingState(false);
            this.showAlert('{{ error_report_generation }}: ' + error.message, 'danger');
        });
    }

    exportAnalysis(format) {
        const params = new URLSearchParams({
            format: format,
            date_start: document.getElementById('date_start').value,
            date_end: document.getElementById('date_end').value,
            analysis_type: document.getElementById('analysis_type').value,
            comparison_period: document.getElementById('comparison_period').value
        });

        this.showAlert('{{ text_exporting }}...', 'info');
        window.open('{{ export_url }}&' + params.toString(), '_blank');
    }

    printAnalysis() {
        window.print();
    }

    compareAnalysis() {
        window.open('{{ url_link('accounts/profitability_analysis', 'compare') }}', '_blank');
    }

    forecastAnalysis() {
        window.open('{{ url_link('accounts/profitability_analysis', 'forecast') }}', '_blank');
    }

    getFormData() {
        const form = document.getElementById('profitability-filter-form');
        const formData = new FormData(form);
        const data = {};

        for (let [key, value] of formData.entries()) {
            data[key] = value;
        }

        return data;
    }

    showExportMenu() {
        const exportButton = document.querySelector('.dropdown-toggle');
        if (exportButton) {
            exportButton.click();
        }
    }

    showLoadingState(show) {
        const buttons = document.querySelectorAll('.btn');
        buttons.forEach(btn => {
            if (show) {
                btn.disabled = true;
                if (btn.querySelector('i')) {
                    btn.querySelector('i').className = 'fa fa-spinner fa-spin';
                }
            } else {
                btn.disabled = false;
                location.reload();
            }
        });
    }

    showAlert(message, type) {
        const alertContainer = document.createElement('div');
        alertContainer.className = `alert alert-${type} alert-dismissible fade show`;
        alertContainer.innerHTML = `
            <i class="fa fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'}"></i>
            ${message}
            <button type="button" class="close" data-dismiss="alert">&times;</button>
        `;

        const container = document.querySelector('#content .container-fluid');
        container.insertBefore(alertContainer, container.firstChild);

        setTimeout(() => {
            alertContainer.remove();
        }, 5000);
    }
}

// Global functions for backward compatibility
function generateReport() {
    profitabilityAnalysisManager.generateReport();
}

function exportAnalysis(format) {
    profitabilityAnalysisManager.exportAnalysis(format);
}

function printAnalysis() {
    profitabilityAnalysisManager.printAnalysis();
}

function compareAnalysis() {
    profitabilityAnalysisManager.compareAnalysis();
}

function forecastAnalysis() {
    profitabilityAnalysisManager.forecastAnalysis();
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.profitabilityAnalysisManager = new ProfitabilityAnalysisManager();
});
</script>

{{ footer }}
