{{ header }}{{ column_left }}

<!-- Enterprise Grade Plus CSS for Advanced Journal Security -->
<style>
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --danger-color: #e74c3c;
    --warning-color: #f39c12;
    --info-color: #17a2b8;
    --security-color: #dc3545;
    --light-bg: #f8f9fa;
    --border-color: #dee2e6;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

.journal-security-container {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: var(--shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.journal-security-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--security-color), var(--primary-color), var(--secondary-color));
}

.journal-security-header {
    text-align: center;
    border-bottom: 3px solid var(--security-color);
    padding-bottom: 20px;
    margin-bottom: 30px;
    position: relative;
}

.journal-security-header h2 {
    color: var(--security-color);
    font-weight: 700;
    font-size: 1.8rem;
    margin: 0;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.journal-security-summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.journal-security-summary-card {
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    text-align: center;
    box-shadow: var(--shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.journal-security-summary-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0,0,0,0.1);
}

.journal-security-summary-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
}

.journal-security-summary-card.secured::before { background: var(--security-color); }
.journal-security-summary-card.unsecured::before { background: var(--warning-color); }
.journal-security-summary-card.violations::before { background: var(--danger-color); }
.journal-security-summary-card.monitoring::before { background: var(--info-color); }

.journal-security-summary-card h4 {
    color: var(--primary-color);
    margin-bottom: 10px;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.journal-security-summary-card .amount {
    font-size: 1.4rem;
    font-weight: 700;
    font-family: 'Courier New', monospace;
    margin-bottom: 5px;
}

.journal-security-summary-card .description {
    font-size: 0.8rem;
    color: #6c757d;
    font-weight: 500;
}

.card-secured .amount { color: var(--security-color); }
.card-unsecured .amount { color: var(--warning-color); }
.card-violations .amount { color: var(--danger-color); }
.card-monitoring .amount { color: var(--info-color); }

.journal-security-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    background: #ffffff;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.journal-security-table th {
    background: linear-gradient(135deg, var(--security-color), #c82333);
    color: white;
    padding: 15px 12px;
    text-align: center;
    font-weight: 600;
    font-size: 0.9rem;
    border-bottom: 2px solid var(--border-color);
}

.journal-security-table td {
    padding: 12px;
    border-bottom: 1px solid var(--border-color);
    text-align: center;
    transition: var(--transition);
}

.journal-security-table tbody tr:hover {
    background: var(--light-bg);
    transform: scale(1.005);
}

.journal-security-table tbody tr:nth-child(even) {
    background: #fafbfc;
}

.amount-cell {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    text-align: {{ direction == 'rtl' ? 'right' : 'left' }};
}

.amount-positive { 
    color: var(--success-color); 
    font-weight: 600;
}

.amount-negative { 
    color: var(--danger-color); 
    font-weight: 600;
}

.amount-neutral { 
    color: var(--secondary-color); 
    font-weight: 600;
}

.journal-security-actions {
    display: flex;
    gap: 5px;
    justify-content: center;
}

.journal-security-actions .btn {
    padding: 5px 10px;
    font-size: 0.8rem;
}

/* Security Status Badges */
.security-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.security-secured { background: #d4edda; color: #155724; }
.security-unsecured { background: #fff3cd; color: #856404; }
.security-locked { background: #f8d7da; color: #721c24; }
.security-protected { background: #d1ecf1; color: #0c5460; }

/* Security Level Badges */
.level-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
}

.level-basic { background: #6c757d; color: white; }
.level-advanced { background: #17a2b8; color: white; }
.level-enterprise { background: #fd7e14; color: white; }
.level-maximum { background: #dc3545; color: white; }

/* Risk Level Badges */
.risk-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
}

.risk-low { background: #28a745; color: white; }
.risk-medium { background: #ffc107; color: #212529; }
.risk-high { background: #fd7e14; color: white; }
.risk-critical { background: #dc3545; color: white; }

/* Filter Panel */
.filter-panel {
    background: linear-gradient(135deg, #f8f9fa, #ffffff);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: var(--shadow);
}

/* Security Alert */
.security-alert {
    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
    border: 1px solid #f5c6cb;
    border-radius: var(--border-radius);
    padding: 15px;
    margin-bottom: 20px;
    color: #721c24;
}

.security-alert .alert-icon {
    font-size: 1.2rem;
    margin-right: 10px;
}

/* RTL Support */
[dir="rtl"] .journal-security-table {
    direction: rtl;
}

[dir="rtl"] .amount-cell {
    text-align: right;
}

/* Print Styles */
@media print {
    .journal-security-container {
        box-shadow: none;
        border: 1px solid #000;
    }
    
    .journal-security-table th {
        background: #f0f0f0 !important;
        color: #000 !important;
    }
    
    .btn, .panel-heading, .filter-panel {
        display: none !important;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .journal-security-table {
        font-size: 0.8rem;
    }
    
    .journal-security-table th,
    .journal-security-table td {
        padding: 8px 6px;
    }
    
    .journal-security-summary-cards {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .journal-security-actions {
        flex-direction: column;
    }
}
</style>

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-{{ direction == 'rtl' ? 'start' : 'end' }}">
        <div class="btn-group" role="group" aria-label="{{ text_actions }}">
          <button type="button" class="btn btn-success btn-lg" onclick="addSecurity()"
                  data-toggle="tooltip" title="{{ text_add_security }}">
            <i class="fa fa-shield"></i> {{ button_add }}
          </button>
          <div class="btn-group" role="group">
            <button type="button" class="btn btn-info dropdown-toggle" data-toggle="dropdown"
                    aria-expanded="false" data-toggle="tooltip" title="{{ text_export_options }}">
              <i class="fa fa-download"></i> {{ text_export }}
            </button>
            <ul class="dropdown-menu">
              <li><a class="dropdown-item" href="#" onclick="exportSecurity('excel')">
                <i class="fa fa-file-excel text-success"></i> Excel
              </a></li>
              <li><a class="dropdown-item" href="#" onclick="exportSecurity('pdf')">
                <i class="fa fa-file-pdf text-danger"></i> PDF
              </a></li>
              <li><a class="dropdown-item" href="#" onclick="exportSecurity('csv')">
                <i class="fa fa-file-csv text-info"></i> CSV
              </a></li>
              <li><hr class="dropdown-divider"></li>
              <li><a class="dropdown-item" href="#" onclick="printSecurity()">
                <i class="fa fa-print text-muted"></i> {{ text_print }}
              </a></li>
            </ul>
          </div>
          <button type="button" class="btn btn-danger" onclick="bulkSecure()"
                  data-toggle="tooltip" title="{{ button_bulk_secure }}">
            <i class="fa fa-lock"></i>
          </button>
          <button type="button" class="btn btn-warning" onclick="bulkUnsecure()"
                  data-toggle="tooltip" title="{{ button_bulk_unsecure }}">
            <i class="fa fa-unlock"></i>
          </button>
          <button type="button" class="btn btn-info" onclick="securityReport()"
                  data-toggle="tooltip" title="{{ button_security_report }}">
            <i class="fa fa-bar-chart"></i>
          </button>
        </div>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li class="breadcrumb-item"><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>

  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible">
      <i class="fa fa-exclamation-triangle"></i>
      {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}

    {% if success %}
    <div class="alert alert-success alert-dismissible">
      <i class="fa fa-check-circle"></i>
      {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}

    <!-- Security Alert -->
    {% if security_violations > 0 %}
    <div class="security-alert">
      <i class="fa fa-exclamation-triangle alert-icon"></i>
      <strong>{{ text_security_alert }}:</strong> {{ security_violations }} {{ text_security_violations }}
    </div>
    {% endif %}

    <!-- Filter Panel -->
    <div class="filter-panel">
      <h4>{{ text_security_filters }}</h4>
      <form id="journal-security-filter-form" method="post">
        <div class="row">
          <div class="col-md-2">
            <div class="form-group">
              <label for="filter_security_level" class="form-label">{{ entry_security_level }}</label>
              <select name="filter_security_level" id="filter_security_level" class="form-control">
                <option value="">{{ text_all_levels }}</option>
                <option value="basic"{% if filter_security_level == 'basic' %} selected{% endif %}>{{ text_basic_security }}</option>
                <option value="advanced"{% if filter_security_level == 'advanced' %} selected{% endif %}>{{ text_advanced_security }}</option>
                <option value="enterprise"{% if filter_security_level == 'enterprise' %} selected{% endif %}>{{ text_enterprise_security }}</option>
                <option value="maximum"{% if filter_security_level == 'maximum' %} selected{% endif %}>{{ text_maximum_security }}</option>
              </select>
            </div>
          </div>
          <div class="col-md-2">
            <div class="form-group">
              <label for="filter_protection_type" class="form-label">{{ entry_protection_type }}</label>
              <select name="filter_protection_type" id="filter_protection_type" class="form-control">
                <option value="">{{ text_all_types }}</option>
                <option value="edit"{% if filter_protection_type == 'edit' %} selected{% endif %}>{{ text_edit_protection }}</option>
                <option value="delete"{% if filter_protection_type == 'delete' %} selected{% endif %}>{{ text_delete_protection }}</option>
                <option value="view"{% if filter_protection_type == 'view' %} selected{% endif %}>{{ text_view_protection }}</option>
                <option value="full"{% if filter_protection_type == 'full' %} selected{% endif %}>{{ text_full_protection }}</option>
              </select>
            </div>
          </div>
          <div class="col-md-2">
            <div class="form-group">
              <label for="filter_status" class="form-label">{{ column_status }}</label>
              <select name="filter_status" id="filter_status" class="form-control">
                <option value="">{{ text_all_statuses }}</option>
                <option value="secured"{% if filter_status == 'secured' %} selected{% endif %}>{{ text_secured }}</option>
                <option value="unsecured"{% if filter_status == 'unsecured' %} selected{% endif %}>{{ text_unsecured }}</option>
                <option value="locked"{% if filter_status == 'locked' %} selected{% endif %}>{{ text_locked }}</option>
                <option value="protected"{% if filter_status == 'protected' %} selected{% endif %}>{{ text_protected }}</option>
              </select>
            </div>
          </div>
          <div class="col-md-2">
            <div class="form-group">
              <label for="filter_date_start" class="form-label">{{ entry_effective_date }}</label>
              <input type="date" name="filter_date_start" id="filter_date_start" value="{{ filter_date_start }}" class="form-control">
            </div>
          </div>
          <div class="col-md-2">
            <div class="form-group">
              <label for="filter_date_end" class="form-label">{{ entry_expiry_date }}</label>
              <input type="date" name="filter_date_end" id="filter_date_end" value="{{ filter_date_end }}" class="form-control">
            </div>
          </div>
          <div class="col-md-2">
            <div class="form-group">
              <label class="form-label">&nbsp;</label>
              <div>
                <button type="submit" class="btn btn-primary btn-block">
                  <i class="fa fa-search"></i> {{ button_filter }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>

    <!-- Journal Security Content -->
    {% if security_entries %}
    <!-- Summary Cards -->
    <div class="journal-security-summary-cards">
      <div class="journal-security-summary-card card-secured secured">
        <h4>{{ text_total_secured }}</h4>
        <div class="amount">{{ summary.total_secured }}</div>
        <div class="description">{{ text_secured }}</div>
      </div>
      <div class="journal-security-summary-card card-unsecured unsecured">
        <h4>{{ text_total_unsecured }}</h4>
        <div class="amount">{{ summary.total_unsecured }}</div>
        <div class="description">{{ text_unsecured }}</div>
      </div>
      <div class="journal-security-summary-card card-violations violations">
        <h4>{{ text_security_violations }}</h4>
        <div class="amount">{{ summary.security_violations }}</div>
        <div class="description">{{ text_violations }}</div>
      </div>
      <div class="journal-security-summary-card card-monitoring monitoring">
        <h4>{{ text_real_time_monitoring }}</h4>
        <div class="amount">{{ summary.monitoring_active }}</div>
        <div class="description">{{ text_active }}</div>
      </div>
    </div>

    <!-- Journal Security Table -->
    <div class="journal-security-container">
      <div class="journal-security-header">
        <h2>{{ text_security_list }}</h2>
      </div>

      <div class="table-responsive">
        <table class="journal-security-table" id="journal-security-table">
          <thead>
            <tr>
              <th><input type="checkbox" id="select-all"></th>
              <th>{{ column_journal_number }}</th>
              <th>{{ column_journal_date }}</th>
              <th>{{ column_description }}</th>
              <th>{{ column_amount }}</th>
              <th>{{ column_security_level }}</th>
              <th>{{ column_protection_type }}</th>
              <th>{{ column_status }}</th>
              <th>{{ column_secured_by }}</th>
              <th>{{ column_secured_date }}</th>
              <th>{{ column_action }}</th>
            </tr>
          </thead>
          <tbody>
            {% for entry in security_entries %}
            <tr data-security-id="{{ entry.security_id }}">
              <td><input type="checkbox" name="selected[]" value="{{ entry.security_id }}"></td>
              <td>
                <strong>{{ entry.journal_number }}</strong>
                <br>
                <small class="text-muted">{{ entry.journal_reference }}</small>
              </td>
              <td>{{ entry.journal_date_formatted }}</td>
              <td>
                <div class="text-truncate" style="max-width: 200px;" title="{{ entry.description }}">
                  {{ entry.description }}
                </div>
              </td>
              <td class="amount-cell">
                <strong class="amount-neutral">{{ entry.amount_formatted }}</strong>
              </td>
              <td>
                <span class="level-badge level-{{ entry.security_level }}">
                  {{ entry.security_level_text }}
                </span>
              </td>
              <td>
                <span class="badge bg-info">
                  {{ entry.protection_type_text }}
                </span>
              </td>
              <td>
                <span class="security-badge security-{{ entry.status }}">
                  {{ entry.status_text }}
                </span>
              </td>
              <td>
                {% if entry.secured_by_name %}
                  {{ entry.secured_by_name }}
                {% else %}
                  <span class="text-muted">{{ text_system }}</span>
                {% endif %}
              </td>
              <td>
                {% if entry.secured_date %}
                  {{ entry.secured_date_formatted }}
                {% else %}
                  <span class="text-muted">-</span>
                {% endif %}
              </td>
              <td>
                <div class="journal-security-actions">
                  <button type="button" class="btn btn-info btn-sm"
                          onclick="viewSecurity({{ entry.security_id }})"
                          data-toggle="tooltip" title="{{ text_view }}">
                    <i class="fa fa-eye"></i>
                  </button>
                  {% if entry.status == 'unsecured' %}
                  <button type="button" class="btn btn-danger btn-sm"
                          onclick="secureSingle({{ entry.security_id }})"
                          data-toggle="tooltip" title="{{ text_secure }}">
                    <i class="fa fa-lock"></i>
                  </button>
                  {% elseif entry.status == 'secured' %}
                  <button type="button" class="btn btn-warning btn-sm"
                          onclick="unsecureSingle({{ entry.security_id }})"
                          data-toggle="tooltip" title="{{ text_unsecure }}">
                    <i class="fa fa-unlock"></i>
                  </button>
                  {% endif %}
                  <button type="button" class="btn btn-default btn-sm"
                          onclick="editSecurity({{ entry.security_id }})"
                          data-toggle="tooltip" title="{{ text_edit }}">
                    <i class="fa fa-edit"></i>
                  </button>
                  <button type="button" class="btn btn-primary btn-sm"
                          onclick="auditTrail({{ entry.security_id }})"
                          data-toggle="tooltip" title="{{ button_audit_trail }}">
                    <i class="fa fa-history"></i>
                  </button>
                </div>
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>
    {% else %}
    <div class="alert alert-info">
      <i class="fa fa-info-circle"></i>
      {{ text_no_security }}
    </div>
    {% endif %}
  </div>
</div>

<script>
// Enterprise Grade Plus JavaScript for Advanced Journal Security
class JournalSecurityAdvancedManager {
    constructor() {
        this.initializeTooltips();
        this.initializeDataTable();
        this.initializeKeyboardShortcuts();
        this.initializeSelectAll();
        this.initializeRealTimeMonitoring();
    }

    initializeTooltips() {
        $('[data-toggle="tooltip"]').tooltip();
    }

    initializeDataTable() {
        const table = document.getElementById('journal-security-table');
        if (table && typeof $ !== 'undefined' && $.fn.DataTable) {
            $(table).DataTable({
                responsive: true,
                pageLength: 25,
                order: [[9, 'desc']], // Sort by secured date desc
                columnDefs: [
                    { targets: [0, 10], orderable: false },
                    { targets: [4], className: 'text-end' },
                    { targets: [5, 6, 7], className: 'text-center' }
                ],
                language: {
                    url: '{{ direction == "rtl" ? "/view/javascript/jquery/datatables-ar.json" : "/view/javascript/jquery/datatables-en.json" }}'
                }
            });
        }
    }

    initializeKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case 'n':
                        e.preventDefault();
                        this.addSecurity();
                        break;
                    case 'e':
                        e.preventDefault();
                        this.showExportMenu();
                        break;
                    case 'p':
                        e.preventDefault();
                        this.printSecurity();
                        break;
                    case 'l':
                        e.preventDefault();
                        this.bulkSecure();
                        break;
                    case 'u':
                        e.preventDefault();
                        this.bulkUnsecure();
                        break;
                    case 'r':
                        e.preventDefault();
                        this.securityReport();
                        break;
                }
            }
        });
    }

    initializeSelectAll() {
        const selectAllCheckbox = document.getElementById('select-all');
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', (e) => {
                const checkboxes = document.querySelectorAll('input[name="selected[]"]');
                checkboxes.forEach(checkbox => {
                    checkbox.checked = e.target.checked;
                });
            });
        }
    }

    initializeRealTimeMonitoring() {
        // Real-time security monitoring
        setInterval(() => {
            this.checkSecurityStatus();
        }, 30000); // Check every 30 seconds
    }

    addSecurity() {
        window.open('{{ url_link('accounts/journal_security_advanced', 'add') }}', '_blank');
    }

    viewSecurity(securityId) {
        window.open('{{ url_link('accounts/journal_security_advanced', 'view') }}&security_id=' + securityId, '_blank');
    }

    editSecurity(securityId) {
        window.open('{{ url_link('accounts/journal_security_advanced', 'edit') }}&security_id=' + securityId, '_blank');
    }

    secureSingle(securityId) {
        const reason = prompt('{{ entry_reason }}:');
        if (reason) {
            this.showLoadingState(true);

            fetch('{{ url_link('accounts/journal_security_advanced', 'secure') }}', {
                method: 'POST',
                body: JSON.stringify({ security_id: securityId, reason: reason }),
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                this.showLoadingState(false);
                if (data.success) {
                    this.showAlert('{{ success_journal_secured }}', 'success');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    this.showAlert(data.error || '{{ error_secure_journal }}', 'danger');
                }
            })
            .catch(error => {
                this.showLoadingState(false);
                this.showAlert('{{ error_secure_journal }}: ' + error.message, 'danger');
            });
        }
    }

    unsecureSingle(securityId) {
        const reason = prompt('{{ entry_reason }}:');
        if (reason) {
            this.showLoadingState(true);

            fetch('{{ url_link('accounts/journal_security_advanced', 'unsecure') }}', {
                method: 'POST',
                body: JSON.stringify({ security_id: securityId, reason: reason }),
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                this.showLoadingState(false);
                if (data.success) {
                    this.showAlert('{{ success_journal_unsecured }}', 'success');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    this.showAlert(data.error || '{{ error_unsecure_journal }}', 'danger');
                }
            })
            .catch(error => {
                this.showLoadingState(false);
                this.showAlert('{{ error_unsecure_journal }}: ' + error.message, 'danger');
            });
        }
    }

    bulkSecure() {
        const selected = this.getSelectedEntries();
        if (selected.length === 0) {
            this.showAlert('{{ text_no_selection }}', 'warning');
            return;
        }

        const reason = prompt('{{ entry_reason }}:');
        if (reason) {
            this.showLoadingState(true);

            fetch('{{ url_link('accounts/journal_security_advanced', 'bulkSecure') }}', {
                method: 'POST',
                body: JSON.stringify({ selected: selected, reason: reason }),
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                this.showLoadingState(false);
                if (data.success) {
                    this.showAlert('{{ success_bulk_secured }}', 'success');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    this.showAlert(data.error || '{{ error_bulk_secure }}', 'danger');
                }
            })
            .catch(error => {
                this.showLoadingState(false);
                this.showAlert('{{ error_bulk_secure }}: ' + error.message, 'danger');
            });
        }
    }

    bulkUnsecure() {
        const selected = this.getSelectedEntries();
        if (selected.length === 0) {
            this.showAlert('{{ text_no_selection }}', 'warning');
            return;
        }

        const reason = prompt('{{ entry_reason }}:');
        if (reason) {
            this.showLoadingState(true);

            fetch('{{ url_link('accounts/journal_security_advanced', 'bulkUnsecure') }}', {
                method: 'POST',
                body: JSON.stringify({ selected: selected, reason: reason }),
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                this.showLoadingState(false);
                if (data.success) {
                    this.showAlert('{{ success_bulk_unsecured }}', 'success');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    this.showAlert(data.error || '{{ error_bulk_unsecure }}', 'danger');
                }
            })
            .catch(error => {
                this.showLoadingState(false);
                this.showAlert('{{ error_bulk_unsecure }}: ' + error.message, 'danger');
            });
        }
    }

    auditTrail(securityId) {
        window.open('{{ url_link('accounts/journal_security_advanced', 'auditTrail') }}&security_id=' + securityId, '_blank');
    }

    securityReport() {
        window.open('{{ url_link('accounts/journal_security_advanced', 'report') }}', '_blank');
    }

    exportSecurity(format) {
        const params = new URLSearchParams({
            format: format,
            filter_security_level: document.getElementById('filter_security_level').value,
            filter_protection_type: document.getElementById('filter_protection_type').value,
            filter_status: document.getElementById('filter_status').value,
            filter_date_start: document.getElementById('filter_date_start').value,
            filter_date_end: document.getElementById('filter_date_end').value
        });

        this.showAlert('{{ text_exporting }}...', 'info');
        window.open('{{ export_url }}&' + params.toString(), '_blank');
    }

    printSecurity() {
        window.print();
    }

    checkSecurityStatus() {
        fetch('{{ url_link('accounts/journal_security_advanced', 'checkStatus') }}', {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.violations && data.violations > 0) {
                this.showSecurityAlert(data.violations);
            }
        })
        .catch(error => {
            console.log('Security check failed:', error);
        });
    }

    showSecurityAlert(violations) {
        const alertContainer = document.createElement('div');
        alertContainer.className = 'security-alert';
        alertContainer.innerHTML = `
            <i class="fa fa-exclamation-triangle alert-icon"></i>
            <strong>{{ text_security_alert }}:</strong> ${violations} {{ text_security_violations }}
        `;

        const container = document.querySelector('#content .container-fluid');
        const existingAlert = container.querySelector('.security-alert');
        if (existingAlert) {
            existingAlert.remove();
        }
        container.insertBefore(alertContainer, container.firstChild);
    }

    getSelectedEntries() {
        const checkboxes = document.querySelectorAll('input[name="selected[]"]:checked');
        return Array.from(checkboxes).map(cb => cb.value);
    }

    showExportMenu() {
        const exportButton = document.querySelector('.dropdown-toggle');
        if (exportButton) {
            exportButton.click();
        }
    }

    showLoadingState(show) {
        const buttons = document.querySelectorAll('.btn');
        buttons.forEach(btn => {
            if (show) {
                btn.disabled = true;
            } else {
                btn.disabled = false;
            }
        });
    }

    showAlert(message, type) {
        const alertContainer = document.createElement('div');
        alertContainer.className = `alert alert-${type} alert-dismissible fade show`;
        alertContainer.innerHTML = `
            <i class="fa fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'}"></i>
            ${message}
            <button type="button" class="close" data-dismiss="alert">&times;</button>
        `;

        const container = document.querySelector('#content .container-fluid');
        container.insertBefore(alertContainer, container.firstChild);

        setTimeout(() => {
            alertContainer.remove();
        }, 5000);
    }
}

// Global functions for backward compatibility
function addSecurity() {
    journalSecurityAdvancedManager.addSecurity();
}

function viewSecurity(securityId) {
    journalSecurityAdvancedManager.viewSecurity(securityId);
}

function editSecurity(securityId) {
    journalSecurityAdvancedManager.editSecurity(securityId);
}

function secureSingle(securityId) {
    journalSecurityAdvancedManager.secureSingle(securityId);
}

function unsecureSingle(securityId) {
    journalSecurityAdvancedManager.unsecureSingle(securityId);
}

function bulkSecure() {
    journalSecurityAdvancedManager.bulkSecure();
}

function bulkUnsecure() {
    journalSecurityAdvancedManager.bulkUnsecure();
}

function auditTrail(securityId) {
    journalSecurityAdvancedManager.auditTrail(securityId);
}

function securityReport() {
    journalSecurityAdvancedManager.securityReport();
}

function exportSecurity(format) {
    journalSecurityAdvancedManager.exportSecurity(format);
}

function printSecurity() {
    journalSecurityAdvancedManager.printSecurity();
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.journalSecurityAdvancedManager = new JournalSecurityAdvancedManager();
});
</script>

{{ footer }}
