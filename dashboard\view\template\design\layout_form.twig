{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="submit" form="form-layout" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fa fa-save"></i></button>
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a></div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pencil"></i> {{ text_form }}</h3>
      </div>
      <div class="panel-body">
        <form action="{{ action }}" method="post" enctype="multipart/form-data" id="form-layout" class="form-horizontal">
          <fieldset>
            <legend>{{ text_route }}</legend>
            <div class="form-group required">
              <label class="col-sm-2 control-label" for="input-name">{{ entry_name }}</label>
              <div class="col-sm-10">
                <input type="text" name="name" value="{{ name }}" placeholder="{{ entry_name }}" id="input-name" class="form-control" />
                {% if error_name %}
                <div class="text-danger">{{ error_name }}</div>
                {% endif %}
              </div>
            </div>
            <table id="route" class="table table-striped table-bordered table-hover">
              <thead>
                <tr>
                  <td class="text-left">{{ entry_store }}</td>
                  <td class="text-left">{{ entry_route }}</td>
                  <td></td>
                </tr>
              </thead>
              <tbody>
                {% set route_row = 0 %}
                {% for layout_route in layout_routes %}
                <tr id="route-row{{ route_row }}">
                  <td class="text-left"><select name="layout_route[{{ route_row }}][store_id]" class="form-control">
                      <option value="0">{{ text_default }}</option>
                      {% for store in stores %}
                      {% if store.store_id == layout_route.store_id %}
                      <option value="{{ store.store_id }}" selected="selected">{{ store.name }}</option>
                      {% else %}
                      <option value="{{ store.store_id }}">{{ store.name }}</option>
                      {% endif %}
                      {% endfor %}
                    </select></td>
                  <td class="text-left"><input type="text" name="layout_route[{{ route_row }}][route]" value="{{ layout_route.route }}" placeholder="{{ entry_route }}" class="form-control" /></td>
                  <td class="text-left"><button type="button" onclick="$('#route-row{{ route_row }}').remove();" data-toggle="tooltip" title="{{ button_remove }}" class="btn btn-danger"><i class="fa fa-minus-circle"></i></button></td>
                </tr>
                {% set route_row = route_row + 1 %}
                {% endfor %}
              </tbody>
              <tfoot>
                <tr>
                  <td colspan="2"></td>
                  <td class="text-left"><button type="button" onclick="addRoute();" data-toggle="tooltip" title="{{ button_route_add }}" class="btn btn-primary"><i class="fa fa-plus-circle"></i></button></td>
                </tr>
              </tfoot>
            </table>
          </fieldset>
          <fieldset>
            <legend>{{ text_module }}</legend>
            {% set module_row = 0 %}
            <div class="row">

              <div class="col-lg-12 col-md-12 col-sm-12">
                <table id="module-content-slider" class="table table-striped table-bordered table-hover">
                  <thead>
                    <tr>
                      <td class="text-center">Slider Area</td>
                    </tr>
                  </thead>
                  <tbody>
                    {% for layout_module in layout_modules %}
                    {% if layout_module.position == 'content_slider' %}
                    <tr id="module-row{{ module_row }}">
                      <td class="text-left"><div class="input-group">
                          <select name="layout_module[{{ module_row }}][code]" class="form-control input-sm">
                            {% for extension in extensions %}
                            <optgroup label="{{ extension.name }}">
                            {% if not extension.module %}
                            {% if extension.code == layout_module.code %}
                            <option value="{{ extension.code }}" selected="selected">{{ extension.name }}</option>
                            {% else %}
                            <option value="{{ extension.code }}">{{ extension.name }}</option>
                            {% endif %}
                            {% else %}
                            {% for module in extension.module %}
                            {% if module.code == layout_module.code %}
                            <option value="{{ module.code }}" selected="selected">{{ module.name }}</option>
                            {% else %}
                            <option value="{{ module.code }}">{{ module.name }}</option>
                            {% endif %}
                            {% endfor %}
                            {% endif %}
                            </optgroup>
                            {% endfor %}
                          </select>
                          <input type="hidden" name="layout_module[{{ module_row }}][position]" value="{{ layout_module.position }}" />
                          <input type="hidden" name="layout_module[{{ module_row }}][sort_order]" value="{{ layout_module.sort_order }}" />
                          <div class="input-group-btn"> <a href="{{ layout_module.edit }}" type="button" data-toggle="tooltip" title="{{ button_edit }}" target="_blank" class="btn btn-primary btn-sm"><i class="fa fa-pencil"></i></a>
                            <button type="button" onclick="$('#module-row{{ module_row }}').remove();" data-toggle="tooltip" title="{{ button_remove }}" class="btn btn-danger btn-sm"><i class="fa fa fa-minus-circle"></i></button>
                          </div>
                        </div></td>
                    </tr>
                    {% set module_row = module_row + 1 %}
                    {% endif %}
                    {% endfor %}
                  </tbody>
                  <tfoot>
                    <tr>
                      <td class="text-left"><div class="input-group">
                          <select class="form-control input-sm">
                            <option value=""></option>
                            {% for extension in extensions %}
                            <optgroup label="{{ extension.name }}">
                            {% if not extension.module %}
                            <option value="{{ extension.code }}">{{ extension.name }}</option>
                            {% else %}
                            {% for module in extension.module %}
                            <option value="{{ module.code }}">{{ module.name }}</option>
                            {% endfor %}
                            {% endif %}
                            </optgroup>
                            {% endfor %}
                          </select>
                          <div class="input-group-btn">
                            <button type="button" onclick="addModule('content-slider');" data-toggle="tooltip" title="{{ button_module_add }}" class="btn btn-primary btn-sm"><i class="fa fa-plus-circle"></i></button>
                          </div>
                        </div></td>
                    </tr>
                  </tfoot>
                </table>
              </div>
                            
              <div class="col-lg-3 col-md-4 col-sm-12">
                <table id="module-column-left" class="table table-striped table-bordered table-hover">
                  <thead>
                    <tr>
                      <td class="text-center">{{ text_column_left }}</td>
                    </tr>
                  </thead>
                  <tbody>
                    {% for layout_module in layout_modules %}
                    {% if layout_module.position == 'column_left' %}
                    <tr id="module-row{{ module_row }}">
                      <td class="text-left"><div class="input-group">
                          <select name="layout_module[{{ module_row }}][code]" class="form-control input-sm">
                            {% for extension in extensions %}
                            <optgroup label="{{ extension.name }}">
                            {% if not extension.module %}
                            {% if extension.code == layout_module.code %}
                            <option value="{{ extension.code }}" selected="selected">{{ extension.name }}</option>
                            {% else %}
                            <option value="{{ extension.code }}">{{ extension.name }}</option>
                            {% endif %}
                            {% else %}
                            {% for module in extension.module %}
                            {% if module.code == layout_module.code %}
                            <option value="{{ module.code }}" selected="selected">{{ module.name }}</option>
                            {% else %}
                            <option value="{{ module.code }}">{{ module.name }}</option>
                            {% endif %}
                            {% endfor %}
                            {% endif %}
                            </optgroup>
                            {% endfor %}
                          </select>
                          <input type="hidden" name="layout_module[{{ module_row }}][position]" value="{{ layout_module.position }}" />
                          <input type="hidden" name="layout_module[{{ module_row }}][sort_order]" value="{{ layout_module.sort_order }}" />
                          <div class="input-group-btn"><a href="{{ layout_module.edit }}" type="button" data-toggle="tooltip" title="{{ button_edit }}" target="_blank" class="btn btn-primary btn-sm"><i class="fa fa-pencil"></i></a>
                            <button type="button" onclick="$('#module-row{{ module_row }}').remove();" data-toggle="tooltip" title="{{ button_remove }}" class="btn btn-danger btn-sm"><i class="fa fa fa-minus-circle"></i></button>
                          </div>
                        </div></td>
                    </tr>
                    {% set module_row = module_row + 1 %}
                    {% endif %}
                    {% endfor %}
                  </tbody>
                  <tfoot>
                    <tr>
                      <td class="text-left"><div class="input-group">
                          <select class="form-control input-sm">
                            <option value=""></option>
                            {% for extension in extensions %}
                            <optgroup label="{{ extension.name }}">
                            {% if not extension.module %}
                            <option value="{{ extension.code }}">{{ extension.name }}</option>
                            {% else %}
                            {% for module in extension.module %}
                            <option value="{{ module.code }}">{{ module.name }}</option>
                            {% endfor %}
                            {% endif %}
                            </optgroup>
                            {% endfor %}
                          </select>
                          <div class="input-group-btn">
                            <button type="button" onclick="addModule('column-left');" data-toggle="tooltip" title="{{ button_module_add }}" class="btn btn-primary btn-sm"><i class="fa fa-plus-circle"></i></button>
                          </div>
                        </div></td>
                    </tr>
                  </tfoot>
                </table>
              </div>
              <div class="col-lg-6 col-md-4 col-sm-12">
                <table id="module-content-top" class="table table-striped table-bordered table-hover">
                  <thead>
                    <tr>
                      <td class="text-center">{{ text_content_top }}</td>
                    </tr>
                  </thead>
                  <tbody>
                    {% for layout_module in layout_modules %}
                    {% if layout_module.position == 'content_top' %}
                    <tr id="module-row{{ module_row }}">
                      <td class="text-left"><div class="input-group">
                          <select name="layout_module[{{ module_row }}][code]" class="form-control input-sm">
                            {% for extension in extensions %}
                            <optgroup label="{{ extension.name }}">
                            {% if not extension.module %}
                            {% if extension.code == layout_module.code %}
                            <option value="{{ extension.code }}" selected="selected">{{ extension.name }}</option>
                            {% else %}
                            <option value="{{ extension.code }}">{{ extension.name }}</option>
                            {% endif %}
                            {% else %}
                            {% for module in extension.module %}
                            {% if module.code == layout_module.code %}
                            <option value="{{ module.code }}" selected="selected">{{ module.name }}</option>
                            {% else %}
                            <option value="{{ module.code }}">{{ module.name }}</option>
                            {% endif %}
                            {% endfor %}
                            {% endif %}
                            </optgroup>
                            {% endfor %}
                          </select>
                          <input type="hidden" name="layout_module[{{ module_row }}][position]" value="{{ layout_module.position }}" />
                          <input type="hidden" name="layout_module[{{ module_row }}][sort_order]" value="{{ layout_module.sort_order }}" />
                          <div class="input-group-btn"> <a href="{{ layout_module.edit }}" type="button" data-toggle="tooltip" title="{{ button_edit }}" target="_blank" class="btn btn-primary btn-sm"><i class="fa fa-pencil"></i></a>
                            <button type="button" onclick="$('#module-row{{ module_row }}').remove();" data-toggle="tooltip" title="{{ button_remove }}" class="btn btn-danger btn-sm"><i class="fa fa fa-minus-circle"></i></button>
                          </div>
                        </div></td>
                    </tr>
                    {% set module_row = module_row + 1 %}
                    {% endif %}
                    {% endfor %}
                  </tbody>
                  <tfoot>
                    <tr>
                      <td class="text-left"><div class="input-group">
                          <select class="form-control input-sm">
                            <option value=""></option>
                            {% for extension in extensions %}
                            <optgroup label="{{ extension.name }}">
                            {% if not extension.module %}
                            <option value="{{ extension.code }}">{{ extension.name }}</option>
                            {% else %}
                            {% for module in extension.module %}
                            <option value="{{ module.code }}">{{ module.name }}</option>
                            {% endfor %}
                            {% endif %}
                            </optgroup>
                            {% endfor %}
                          </select>
                          <div class="input-group-btn">
                            <button type="button" onclick="addModule('content-top');" data-toggle="tooltip" title="{{ button_module_add }}" class="btn btn-primary btn-sm"><i class="fa fa-plus-circle"></i></button>
                          </div>
                        </div></td>
                    </tr>
                  </tfoot>
                </table>
                <table id="module-content-bottom" class="table table-striped table-bordered table-hover">
                  <thead>
                    <tr>
                      <td class="text-center">{{ text_content_bottom }}</td>
                    </tr>
                  </thead>
                  <tbody>
                    {% for layout_module in layout_modules %}
                    {% if layout_module.position == 'content_bottom' %}
                    <tr id="module-row{{ module_row }}">
                      <td class="text-left"><div class="input-group">
                          <select name="layout_module[{{ module_row }}][code]" class="form-control input-sm">
                            {% for extension in extensions %}
                            <optgroup label="{{ extension.name }}">
                            {% if not extension.module %}
                            {% if extension.code == layout_module.code %}
                            <option value="{{ extension.code }}" selected="selected">{{ extension.name }}</option>
                            {% else %}
                            <option value="{{ extension.code }}">{{ extension.name }}</option>
                            {% endif %}
                            {% else %}
                            {% for module in extension.module %}
                            {% if module.code == layout_module.code %}
                            <option value="{{ module.code }}" selected="selected">{{ module.name }}</option>
                            {% else %}
                            <option value="{{ module.code }}">{{ module.name }}</option>
                            {% endif %}
                            {% endfor %}
                            {% endif %}
                            </optgroup>
                            {% endfor %}
                          </select>
                          <input type="hidden" name="layout_module[{{ module_row }}][position]" value="{{ layout_module.position }}" />
                          <input type="hidden" name="layout_module[{{ module_row }}][sort_order]" value="{{ layout_module.sort_order }}" />
                          <div class="input-group-btn"><a href="{{ layout_module.edit }}" type="button" data-toggle="tooltip" title="{{ button_edit }}" target="_blank" class="btn btn-primary btn-sm"><i class="fa fa-pencil"></i></a>
                            <button type="button" onclick="$('#module-row{{ module_row }}').remove();" data-toggle="tooltip" title="{{ button_remove }}" class="btn btn-danger btn-sm"><i class="fa fa fa-minus-circle"></i></button>
                          </div>
                        </div></td>
                    </tr>
                    {% set module_row = module_row + 1 %}
                    {% endif %}
                    {% endfor %}
                  </tbody>
                  <tfoot>
                    <tr>
                      <td class="text-left"><div class="input-group">
                          <select class="form-control input-sm">
                            <option value=""></option>
                            {% for extension in extensions %}
                            <optgroup label="{{ extension.name }}">
                            {% if not extension.module %}
                            <option value="{{ extension.code }}">{{ extension.name }}</option>
                            {% else %}
                            {% for module in extension.module %}
                            <option value="{{ module.code }}">{{ module.name }}</option>
                            {% endfor %}
                            {% endif %}
                            </optgroup>
                            {% endfor %}
                          </select>
                          <div class="input-group-btn">
                            <button type="button" onclick="addModule('content-bottom');" data-toggle="tooltip" title="{{ button_module_add }}" class="btn btn-primary btn-sm"><i class="fa fa-plus-circle"></i></button>
                          </div>
                        </div></td>
                    </tr>
                  </tfoot>
                </table>
              </div>
              <div class="col-lg-3 col-md-4 col-sm-12">
                <table id="module-column-right" class="table table-striped table-bordered table-hover">
                  <thead>
                    <tr>
                      <td class="text-center">{{ text_column_right }}</td>
                    </tr>
                  </thead>
                  <tbody>
                    {% for layout_module in layout_modules %}
                    {% if layout_module.position == 'column_right' %}
                    <tr id="module-row{{ module_row }}">
                      <td class="text-left"><div class="input-group">
                          <select name="layout_module[{{ module_row }}][code]" class="form-control input-sm">
                            {% for extension in extensions %}
                            <optgroup label="{{ extension.name }}">
                            {% if not extension.module %}
                            {% if extension.code == layout_module.code %}
                            <option value="{{ extension.code }}" selected="selected">{{ extension.name }}</option>
                            {% else %}
                            <option value="{{ extension.code }}">{{ extension.name }}</option>
                            {% endif %}
                            {% else %}
                            {% for module in extension.module %}
                            {% if module.code == layout_module.code %}
                            <option value="{{ module.code }}" selected="selected">{{ module.name }}</option>
                            {% else %}
                            <option value="{{ module.code }}">{{ module.name }}</option>
                            {% endif %}
                            {% endfor %}
                            {% endif %}
                            </optgroup>
                            {% endfor %}
                          </select>
                          <input type="hidden" name="layout_module[{{ module_row }}][position]" value="{{ layout_module.position }}" />
                          <input type="hidden" name="layout_module[{{ module_row }}][sort_order]" value="{{ layout_module.sort_order }}" />
                          <div class="input-group-btn"><a href="{{ layout_module.edit }}" data-toggle="tooltip" title="{{ button_edit }}" target="_blank" class="btn btn-primary btn-sm"><i class="fa fa-pencil"></i></a>
                            <button type="button" onclick="$('#module-row{{ module_row }}').remove();" data-toggle="tooltip" title="{{ button_remove }}" class="btn btn-danger btn-sm"><i class="fa fa fa-minus-circle"></i></button>
                          </div>
                        </div></td>
                    </tr>
                    {% set module_row = module_row + 1 %}
                    {% endif %}
                    {% endfor %}
                  </tbody>
                  <tfoot>
                    <tr>
                      <td class="text-left"><div class="input-group">
                          <select class="form-control input-sm">
                            <option value=""></option>
                            {% for extension in extensions %}
                            <optgroup label="{{ extension.name }}">
                            {% if not extension.module %}
                            <option value="{{ extension.code }}">{{ extension.name }}</option>
                            {% else %}
                            {% for module in extension.module %}
                            <option value="{{ module.code }}">{{ module.name }}</option>
                            {% endfor %}
                            {% endif %}
                            </optgroup>
                            {% endfor %}
                          </select>
                          <div class="input-group-btn">
                            <button type="button" onclick="addModule('column-right');" data-toggle="tooltip" title="{{ button_module_add }}" class="btn btn-primary btn-sm"><i class="fa fa-plus-circle"></i></button>
                          </div>
                        </div></td>
                    </tr>
                  </tfoot>
                </table>
              </div>
            </div>
          </fieldset>
        </form>
      </div>
    </div>
  </div>
  <script type="text/javascript"><!--
var route_row = {{ route_row }};

function addRoute() {
	html  = '<tr id="route-row' + route_row + '">';
	html += '  <td class="text-left"><select name="layout_route[' + route_row + '][store_id]" class="form-control">';
	html += '  <option value="0">{{ text_default }}</option>';
	{% for store in stores %}
	html += '<option value="{{ store.store_id }}">{{ store.name|escape('js') }}</option>';
	{% endfor %}
	html += '  </select></td>';
	html += '  <td class="text-left"><input type="text" name="layout_route[' + route_row + '][route]" value="" placeholder="{{ entry_route }}" class="form-control" /></td>';
	html += '  <td class="text-left"><button type="button" onclick="$(\'#route-row' + route_row + '\').remove();" data-toggle="tooltip" title="{{ button_remove }}" class="btn btn-danger"><i class="fa fa-minus-circle"></i></button></td>';
	html += '</tr>';
	
	$('#route tbody').append(html);
	
	route_row++;
}

var module_row = {{ module_row }};

function addModule(type) {
	html  = '<tr id="module-row' + module_row + '">';
    html += '  <td class="text-left"><div class="input-group"><select name="layout_module[' + module_row + '][code]" class="form-control input-sm">';
	{% for extension in extensions %}
	html += '    <optgroup label="{{ extension.name|escape('js') }}">';
	{% if not extension.module %}
	html += '      <option value="{{ extension.code }}">{{ extension.name|escape('js') }}</option>';
	{% else %}
	{% for module in extension.module %}
	html += '      <option value="{{ module.code }}">{{ module.name|escape('js') }}</option>';
	{% endfor %}
	{% endif %}
	html += '    </optgroup>';
	{% endfor %}
	html += '  </select>';
    html += '  <input type="hidden" name="layout_module[' + module_row + '][position]" value="' + type.replace('-', '_') + '" />';
    html += '  <input type="hidden" name="layout_module[' + module_row + '][sort_order]" value="" />';
	html += '  <div class="input-group-btn"><a href="" target="_blank" type="button" data-toggle="tooltip" title="{{ button_edit }}" class="btn btn-primary btn-sm"><i class="fa fa-pencil"></i></a><button type="button" onclick="$(\'#module-row' + module_row + '\').remove();" data-toggle="tooltip" title="{{ button_remove }}" class="btn btn-danger btn-sm"><i class="fa fa fa-minus-circle"></i></button></div></div></td>';
	html += '</tr>';
	
	$('#module-' + type + ' tbody').append(html);
	
	$('#module-' + type + ' tbody select[name=\'layout_module[' + module_row + '][code]\']').val($('#module-' + type + ' tfoot select').val());
	
	$('#module-' + type + ' select[name*=\'code\']').trigger('change');
		
	$('#module-' + type + ' tbody input[name*=\'sort_order\']').each(function(i, element) {
		$(element).val(i);
	});
	
	module_row++;
}

$('#module-column-left, #module-column-right, #module-content-top,#module-content-slider, #module-content-bottom').delegate('select[name*=\'code\']', 'change', function() {
	var part = this.value.split('.');
	
	if (!part[1]) {
		$(this).parent().find('a').attr('href', 'index.php?route=extension/module/' + part[0] + '&user_token={{ user_token }}');
	} else {
		$(this).parent().find('a').attr('href', 'index.php?route=extension/module/' + part[0] + '&user_token={{ user_token }}&module_id=' + part[1]);
	}
});

$('#module-column-left, #module-column-right, #module-content-top, #module-content-silder,#module-content-bottom').trigger('change');
//--></script> 
</div>
{{ footer }}