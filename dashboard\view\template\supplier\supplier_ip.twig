{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="submit" form="supplier\supplier-form" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fas fa-save"></i></button>
        <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-light"><i class="fas fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    
    <div class="card">
      <div class="card-header"><i class="fas fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}chart-bar{% elif screen_type == 'settings' %}cog{% else %}pencil-alt{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="card-body">
        <form id="supplier\supplier-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-account_custom_field">{{ text_account_custom_field }}</label>
            <div class="col-sm-10">
              <input type="text" name="account_custom_field" value="{{ account_custom_field }}" placeholder="{{ text_account_custom_field }}" id="input-account_custom_field" class="form-control" />
              {% if error_account_custom_field %}
                <div class="invalid-feedback">{{ error_account_custom_field }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-add">{{ text_add }}</label>
            <div class="col-sm-10">
              <input type="text" name="add" value="{{ add }}" placeholder="{{ text_add }}" id="input-add" class="form-control" />
              {% if error_add %}
                <div class="invalid-feedback">{{ error_add }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-address_id">{{ text_address_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="address_id" value="{{ address_id }}" placeholder="{{ text_address_id }}" id="input-address_id" class="form-control" />
              {% if error_address_id %}
                <div class="invalid-feedback">{{ error_address_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-addresses">{{ text_addresses }}</label>
            <div class="col-sm-10">
              <input type="text" name="addresses" value="{{ addresses }}" placeholder="{{ text_addresses }}" id="input-addresses" class="form-control" />
              {% if error_addresses %}
                <div class="invalid-feedback">{{ error_addresses }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-affiliate">{{ text_affiliate }}</label>
            <div class="col-sm-10">
              <input type="text" name="affiliate" value="{{ affiliate }}" placeholder="{{ text_affiliate }}" id="input-affiliate" class="form-control" />
              {% if error_affiliate %}
                <div class="invalid-feedback">{{ error_affiliate }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-affiliate_custom_field">{{ text_affiliate_custom_field }}</label>
            <div class="col-sm-10">
              <input type="text" name="affiliate_custom_field" value="{{ affiliate_custom_field }}" placeholder="{{ text_affiliate_custom_field }}" id="input-affiliate_custom_field" class="form-control" />
              {% if error_affiliate_custom_field %}
                <div class="invalid-feedback">{{ error_affiliate_custom_field }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-bank_account_name">{{ text_bank_account_name }}</label>
            <div class="col-sm-10">
              <input type="text" name="bank_account_name" value="{{ bank_account_name }}" placeholder="{{ text_bank_account_name }}" id="input-bank_account_name" class="form-control" />
              {% if error_bank_account_name %}
                <div class="invalid-feedback">{{ error_bank_account_name }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-bank_account_number">{{ text_bank_account_number }}</label>
            <div class="col-sm-10">
              <input type="text" name="bank_account_number" value="{{ bank_account_number }}" placeholder="{{ text_bank_account_number }}" id="input-bank_account_number" class="form-control" />
              {% if error_bank_account_number %}
                <div class="invalid-feedback">{{ error_bank_account_number }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-bank_branch_number">{{ text_bank_branch_number }}</label>
            <div class="col-sm-10">
              <input type="text" name="bank_branch_number" value="{{ bank_branch_number }}" placeholder="{{ text_bank_branch_number }}" id="input-bank_branch_number" class="form-control" />
              {% if error_bank_branch_number %}
                <div class="invalid-feedback">{{ error_bank_branch_number }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-bank_name">{{ text_bank_name }}</label>
            <div class="col-sm-10">
              <input type="text" name="bank_name" value="{{ bank_name }}" placeholder="{{ text_bank_name }}" id="input-bank_name" class="form-control" />
              {% if error_bank_name %}
                <div class="invalid-feedback">{{ error_bank_name }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-bank_swift_code">{{ text_bank_swift_code }}</label>
            <div class="col-sm-10">
              <input type="text" name="bank_swift_code" value="{{ bank_swift_code }}" placeholder="{{ text_bank_swift_code }}" id="input-bank_swift_code" class="form-control" />
              {% if error_bank_swift_code %}
                <div class="invalid-feedback">{{ error_bank_swift_code }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-cheque">{{ text_cheque }}</label>
            <div class="col-sm-10">
              <input type="text" name="cheque" value="{{ cheque }}" placeholder="{{ text_cheque }}" id="input-cheque" class="form-control" />
              {% if error_cheque %}
                <div class="invalid-feedback">{{ error_cheque }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-commission">{{ text_commission }}</label>
            <div class="col-sm-10">
              <input type="text" name="commission" value="{{ commission }}" placeholder="{{ text_commission }}" id="input-commission" class="form-control" />
              {% if error_commission %}
                <div class="invalid-feedback">{{ error_commission }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-company">{{ text_company }}</label>
            <div class="col-sm-10">
              <input type="text" name="company" value="{{ company }}" placeholder="{{ text_company }}" id="input-company" class="form-control" />
              {% if error_company %}
                <div class="invalid-feedback">{{ error_company }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-confirm">{{ text_confirm }}</label>
            <div class="col-sm-10">
              <input type="text" name="confirm" value="{{ confirm }}" placeholder="{{ text_confirm }}" id="input-confirm" class="form-control" />
              {% if error_confirm %}
                <div class="invalid-feedback">{{ error_confirm }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-countries">{{ text_countries }}</label>
            <div class="col-sm-10">
              <input type="text" name="countries" value="{{ countries }}" placeholder="{{ text_countries }}" id="input-countries" class="form-control" />
              {% if error_countries %}
                <div class="invalid-feedback">{{ error_countries }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-delete">{{ text_delete }}</label>
            <div class="col-sm-10">
              <input type="text" name="delete" value="{{ delete }}" placeholder="{{ text_delete }}" id="input-delete" class="form-control" />
              {% if error_delete %}
                <div class="invalid-feedback">{{ error_delete }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-email">{{ text_email }}</label>
            <div class="col-sm-10">
              <input type="text" name="email" value="{{ email }}" placeholder="{{ text_email }}" id="input-email" class="form-control" />
              {% if error_email %}
                <div class="invalid-feedback">{{ error_email }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_address">{{ text_error_address }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_address" value="{{ error_address }}" placeholder="{{ text_error_address }}" id="input-error_address" class="form-control" />
              {% if error_error_address %}
                <div class="invalid-feedback">{{ error_error_address }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_bank_account_name">{{ text_error_bank_account_name }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_bank_account_name" value="{{ error_bank_account_name }}" placeholder="{{ text_error_bank_account_name }}" id="input-error_bank_account_name" class="form-control" />
              {% if error_error_bank_account_name %}
                <div class="invalid-feedback">{{ error_error_bank_account_name }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_bank_account_number">{{ text_error_bank_account_number }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_bank_account_number" value="{{ error_bank_account_number }}" placeholder="{{ text_error_bank_account_number }}" id="input-error_bank_account_number" class="form-control" />
              {% if error_error_bank_account_number %}
                <div class="invalid-feedback">{{ error_error_bank_account_number }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_cheque">{{ text_error_cheque }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_cheque" value="{{ error_cheque }}" placeholder="{{ text_error_cheque }}" id="input-error_cheque" class="form-control" />
              {% if error_error_cheque %}
                <div class="invalid-feedback">{{ error_error_cheque }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_confirm">{{ text_error_confirm }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_confirm" value="{{ error_confirm }}" placeholder="{{ text_error_confirm }}" id="input-error_confirm" class="form-control" />
              {% if error_error_confirm %}
                <div class="invalid-feedback">{{ error_error_confirm }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_custom_field">{{ text_error_custom_field }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_custom_field" value="{{ error_custom_field }}" placeholder="{{ text_error_custom_field }}" id="input-error_custom_field" class="form-control" />
              {% if error_error_custom_field %}
                <div class="invalid-feedback">{{ error_error_custom_field }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_email">{{ text_error_email }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_email" value="{{ error_email }}" placeholder="{{ text_error_email }}" id="input-error_email" class="form-control" />
              {% if error_error_email %}
                <div class="invalid-feedback">{{ error_error_email }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_firstname">{{ text_error_firstname }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_firstname" value="{{ error_firstname }}" placeholder="{{ text_error_firstname }}" id="input-error_firstname" class="form-control" />
              {% if error_error_firstname %}
                <div class="invalid-feedback">{{ error_error_firstname }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_lastname">{{ text_error_lastname }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_lastname" value="{{ error_lastname }}" placeholder="{{ text_error_lastname }}" id="input-error_lastname" class="form-control" />
              {% if error_error_lastname %}
                <div class="invalid-feedback">{{ error_error_lastname }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_password">{{ text_error_password }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_password" value="{{ error_password }}" placeholder="{{ text_error_password }}" id="input-error_password" class="form-control" />
              {% if error_error_password %}
                <div class="invalid-feedback">{{ error_error_password }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_paypal">{{ text_error_paypal }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_paypal" value="{{ error_paypal }}" placeholder="{{ text_error_paypal }}" id="input-error_paypal" class="form-control" />
              {% if error_error_paypal %}
                <div class="invalid-feedback">{{ error_error_paypal }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_telephone">{{ text_error_telephone }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_telephone" value="{{ error_telephone }}" placeholder="{{ text_error_telephone }}" id="input-error_telephone" class="form-control" />
              {% if error_error_telephone %}
                <div class="invalid-feedback">{{ error_error_telephone }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_tracking">{{ text_error_tracking }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_tracking" value="{{ error_tracking }}" placeholder="{{ text_error_tracking }}" id="input-error_tracking" class="form-control" />
              {% if error_error_tracking %}
                <div class="invalid-feedback">{{ error_error_tracking }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_date_added">{{ text_filter_date_added }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_date_added" value="{{ filter_date_added }}" placeholder="{{ text_filter_date_added }}" id="input-filter_date_added" class="form-control" />
              {% if error_filter_date_added %}
                <div class="invalid-feedback">{{ error_filter_date_added }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_email">{{ text_filter_email }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_email" value="{{ filter_email }}" placeholder="{{ text_filter_email }}" id="input-filter_email" class="form-control" />
              {% if error_filter_email %}
                <div class="invalid-feedback">{{ error_filter_email }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_ip">{{ text_filter_ip }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_ip" value="{{ filter_ip }}" placeholder="{{ text_filter_ip }}" id="input-filter_ip" class="form-control" />
              {% if error_filter_ip %}
                <div class="invalid-feedback">{{ error_filter_ip }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_name">{{ text_filter_name }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_name" value="{{ filter_name }}" placeholder="{{ text_filter_name }}" id="input-filter_name" class="form-control" />
              {% if error_filter_name %}
                <div class="invalid-feedback">{{ error_filter_name }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_status">{{ text_filter_status }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_status" value="{{ filter_status }}" placeholder="{{ text_filter_status }}" id="input-filter_status" class="form-control" />
              {% if error_filter_status %}
                <div class="invalid-feedback">{{ error_filter_status }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_supplier_group_id">{{ text_filter_supplier_group_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_supplier_group_id" value="{{ filter_supplier_group_id }}" placeholder="{{ text_filter_supplier_group_id }}" id="input-filter_supplier_group_id" class="form-control" />
              {% if error_filter_supplier_group_id %}
                <div class="invalid-feedback">{{ error_filter_supplier_group_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_telephone">{{ text_filter_telephone }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_telephone" value="{{ filter_telephone }}" placeholder="{{ text_filter_telephone }}" id="input-filter_telephone" class="form-control" />
              {% if error_filter_telephone %}
                <div class="invalid-feedback">{{ error_filter_telephone }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-firstname">{{ text_firstname }}</label>
            <div class="col-sm-10">
              <input type="text" name="firstname" value="{{ firstname }}" placeholder="{{ text_firstname }}" id="input-firstname" class="form-control" />
              {% if error_firstname %}
                <div class="invalid-feedback">{{ error_firstname }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="invalid-feedback">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-ips">{{ text_ips }}</label>
            <div class="col-sm-10">
              <input type="text" name="ips" value="{{ ips }}" placeholder="{{ text_ips }}" id="input-ips" class="form-control" />
              {% if error_ips %}
                <div class="invalid-feedback">{{ error_ips }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-lastname">{{ text_lastname }}</label>
            <div class="col-sm-10">
              <input type="text" name="lastname" value="{{ lastname }}" placeholder="{{ text_lastname }}" id="input-lastname" class="form-control" />
              {% if error_lastname %}
                <div class="invalid-feedback">{{ error_lastname }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-newsletter">{{ text_newsletter }}</label>
            <div class="col-sm-10">
              <input type="text" name="newsletter" value="{{ newsletter }}" placeholder="{{ text_newsletter }}" id="input-newsletter" class="form-control" />
              {% if error_newsletter %}
                <div class="invalid-feedback">{{ error_newsletter }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-order">{{ text_order }}</label>
            <div class="col-sm-10">
              <input type="text" name="order" value="{{ order }}" placeholder="{{ text_order }}" id="input-order" class="form-control" />
              {% if error_order %}
                <div class="invalid-feedback">{{ error_order }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-pagination">{{ text_pagination }}</label>
            <div class="col-sm-10">
              <input type="text" name="pagination" value="{{ pagination }}" placeholder="{{ text_pagination }}" id="input-pagination" class="form-control" />
              {% if error_pagination %}
                <div class="invalid-feedback">{{ error_pagination }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-password">{{ text_password }}</label>
            <div class="col-sm-10">
              <input type="text" name="password" value="{{ password }}" placeholder="{{ text_password }}" id="input-password" class="form-control" />
              {% if error_password %}
                <div class="invalid-feedback">{{ error_password }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-payment">{{ text_payment }}</label>
            <div class="col-sm-10">
              <input type="text" name="payment" value="{{ payment }}" placeholder="{{ text_payment }}" id="input-payment" class="form-control" />
              {% if error_payment %}
                <div class="invalid-feedback">{{ error_payment }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-paypal">{{ text_paypal }}</label>
            <div class="col-sm-10">
              <input type="text" name="paypal" value="{{ paypal }}" placeholder="{{ text_paypal }}" id="input-paypal" class="form-control" />
              {% if error_paypal %}
                <div class="invalid-feedback">{{ error_paypal }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-results">{{ text_results }}</label>
            <div class="col-sm-10">
              <input type="text" name="results" value="{{ results }}" placeholder="{{ text_results }}" id="input-results" class="form-control" />
              {% if error_results %}
                <div class="invalid-feedback">{{ error_results }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-safe">{{ text_safe }}</label>
            <div class="col-sm-10">
              <input type="text" name="safe" value="{{ safe }}" placeholder="{{ text_safe }}" id="input-safe" class="form-control" />
              {% if error_safe %}
                <div class="invalid-feedback">{{ error_safe }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-selected">{{ text_selected }}</label>
            <div class="col-sm-10">
              <input type="text" name="selected" value="{{ selected }}" placeholder="{{ text_selected }}" id="input-selected" class="form-control" />
              {% if error_selected %}
                <div class="invalid-feedback">{{ error_selected }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort">{{ text_sort }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort" value="{{ sort }}" placeholder="{{ text_sort }}" id="input-sort" class="form-control" />
              {% if error_sort %}
                <div class="invalid-feedback">{{ error_sort }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_date_added">{{ text_sort_date_added }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_date_added" value="{{ sort_date_added }}" placeholder="{{ text_sort_date_added }}" id="input-sort_date_added" class="form-control" />
              {% if error_sort_date_added %}
                <div class="invalid-feedback">{{ error_sort_date_added }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_email">{{ text_sort_email }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_email" value="{{ sort_email }}" placeholder="{{ text_sort_email }}" id="input-sort_email" class="form-control" />
              {% if error_sort_email %}
                <div class="invalid-feedback">{{ error_sort_email }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_ip">{{ text_sort_ip }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_ip" value="{{ sort_ip }}" placeholder="{{ text_sort_ip }}" id="input-sort_ip" class="form-control" />
              {% if error_sort_ip %}
                <div class="invalid-feedback">{{ error_sort_ip }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_name">{{ text_sort_name }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_name" value="{{ sort_name }}" placeholder="{{ text_sort_name }}" id="input-sort_name" class="form-control" />
              {% if error_sort_name %}
                <div class="invalid-feedback">{{ error_sort_name }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_status">{{ text_sort_status }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_status" value="{{ sort_status }}" placeholder="{{ text_sort_status }}" id="input-sort_status" class="form-control" />
              {% if error_sort_status %}
                <div class="invalid-feedback">{{ error_sort_status }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_supplier_group">{{ text_sort_supplier_group }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_supplier_group" value="{{ sort_supplier_group }}" placeholder="{{ text_sort_supplier_group }}" id="input-sort_supplier_group" class="form-control" />
              {% if error_sort_supplier_group %}
                <div class="invalid-feedback">{{ error_sort_supplier_group }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_telephone">{{ text_sort_telephone }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_telephone" value="{{ sort_telephone }}" placeholder="{{ text_sort_telephone }}" id="input-sort_telephone" class="form-control" />
              {% if error_sort_telephone %}
                <div class="invalid-feedback">{{ error_sort_telephone }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-status">{{ text_status }}</label>
            <div class="col-sm-10">
              <input type="text" name="status" value="{{ status }}" placeholder="{{ text_status }}" id="input-status" class="form-control" />
              {% if error_status %}
                <div class="invalid-feedback">{{ error_status }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-supplier_group_id">{{ text_supplier_group_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="supplier_group_id" value="{{ supplier_group_id }}" placeholder="{{ text_supplier_group_id }}" id="input-supplier_group_id" class="form-control" />
              {% if error_supplier_group_id %}
                <div class="invalid-feedback">{{ error_supplier_group_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-supplier_groups">{{ text_supplier_groups }}</label>
            <div class="col-sm-10">
              <input type="text" name="supplier_groups" value="{{ supplier_groups }}" placeholder="{{ text_supplier_groups }}" id="input-supplier_groups" class="form-control" />
              {% if error_supplier_groups %}
                <div class="invalid-feedback">{{ error_supplier_groups }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-supplier_id">{{ text_supplier_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="supplier_id" value="{{ supplier_id }}" placeholder="{{ text_supplier_id }}" id="input-supplier_id" class="form-control" />
              {% if error_supplier_id %}
                <div class="invalid-feedback">{{ error_supplier_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-suppliers">{{ text_suppliers }}</label>
            <div class="col-sm-10">
              <input type="text" name="suppliers" value="{{ suppliers }}" placeholder="{{ text_suppliers }}" id="input-suppliers" class="form-control" />
              {% if error_suppliers %}
                <div class="invalid-feedback">{{ error_suppliers }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-tax">{{ text_tax }}</label>
            <div class="col-sm-10">
              <input type="text" name="tax" value="{{ tax }}" placeholder="{{ text_tax }}" id="input-tax" class="form-control" />
              {% if error_tax %}
                <div class="invalid-feedback">{{ error_tax }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-telephone">{{ text_telephone }}</label>
            <div class="col-sm-10">
              <input type="text" name="telephone" value="{{ telephone }}" placeholder="{{ text_telephone }}" id="input-telephone" class="form-control" />
              {% if error_telephone %}
                <div class="invalid-feedback">{{ error_telephone }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_form">{{ text_text_form }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_form" value="{{ text_form }}" placeholder="{{ text_text_form }}" id="input-text_form" class="form-control" />
              {% if error_text_form %}
                <div class="invalid-feedback">{{ error_text_form }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-tracking">{{ text_tracking }}</label>
            <div class="col-sm-10">
              <input type="text" name="tracking" value="{{ tracking }}" placeholder="{{ text_tracking }}" id="input-tracking" class="form-control" />
              {% if error_tracking %}
                <div class="invalid-feedback">{{ error_tracking }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="invalid-feedback">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-website">{{ text_website }}</label>
            <div class="col-sm-10">
              <input type="text" name="website" value="{{ website }}" placeholder="{{ text_website }}" id="input-website" class="form-control" />
              {% if error_website %}
                <div class="invalid-feedback">{{ error_website }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}