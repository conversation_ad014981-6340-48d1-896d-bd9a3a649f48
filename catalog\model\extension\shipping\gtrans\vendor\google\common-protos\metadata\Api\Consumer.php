<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/consumer.proto

namespace GPBMetadata\Google\Api;

class Consumer
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(hex2bin(
            "0a87030a19676f6f676c652f6170692f636f6e73756d65722e70726f746f" .
            "120a676f6f676c652e617069223d0a1150726f6a65637450726f70657274" .
            "69657312280a0a70726f7065727469657318012003280b32142e676f6f67" .
            "6c652e6170692e50726f706572747922ac010a0850726f7065727479120c" .
            "0a046e616d65180120012809122f0a047479706518022001280e32212e67" .
            "6f6f676c652e6170692e50726f70657274792e50726f7065727479547970" .
            "6512130a0b6465736372697074696f6e180320012809224c0a0c50726f70" .
            "6572747954797065120f0a0b554e535045434946494544100012090a0549" .
            "4e543634100112080a04424f4f4c1002120a0a06535452494e471003120a" .
            "0a06444f55424c45100442680a0e636f6d2e676f6f676c652e617069420d" .
            "436f6e73756d657250726f746f50015a45676f6f676c652e676f6c616e67" .
            "2e6f72672f67656e70726f746f2f676f6f676c65617069732f6170692f73" .
            "657276696365636f6e6669673b73657276696365636f6e66696762067072" .
            "6f746f33"
        ), true);

        static::$is_initialized = true;
    }
}

