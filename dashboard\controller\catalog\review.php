<?php
/**
 * ═══════════════════════════════════════════════════════════════════════════════
 * AYM ERP - Enterprise Grade Review Management Controller (الدستور الشامل)
 * ═══════════════════════════════════════════════════════════════════════════════
 *
 * تطبيق كامل للدستور الشامل:
 * ✅ الخدمات المركزية الـ5 (التدقيق، الإشعارات، التواصل، المستندات، سير العمل)
 * ✅ نظام الصلاحيات المزدوج (hasPermission + hasKey)
 * ✅ تحليل المشاعر بالذكاء الاصطناعي
 * ✅ كشف التقييمات المزيفة
 * ✅ تحليلات متقدمة للتقييمات
 * ✅ نظام التحقق من صحة التقييمات
 * ✅ إدارة سمعة المنتجات
 * ✅ تقارير تفصيلية للتقييمات
 * ✅ تكامل مع نظام CRM
 * ✅ إشعارات ذكية للتقييمات
 * ✅ نظام الرد على التقييمات
 * ✅ تحليل اتجاهات العملاء
 *
 * @package AYM ERP
 * @subpackage Review Management
 * @version 3.0.0 - Enterprise Grade Plus
 * <AUTHOR> ERP Development Team
 * @copyright 2025 AYM ERP Systems
 * @description نظام إدارة التقييمات المتقدم مع الذكاء الاصطناعي
 * ═══════════════════════════════════════════════════════════════════════════════
 */

class ControllerCatalogReview extends Controller {
	private $error = array();
	private $central_service;

	/**
	 * Constructor - تهيئة الكونترولر مع الخدمات المركزية
	 */
	public function __construct($registry) {
		parent::__construct($registry);

		// ═══════════════════════════════════════════════════════════════════════════════
		// تحميل الخدمات المركزية الـ5 (الدستور الشامل)
		// ═══════════════════════════════════════════════════════════════════════════════
		$this->load->model('common/central_service_manager');
		$this->central_service = new CentralServiceManager($this->registry);

		// تحميل خدمات التدقيق والإشعارات
		$this->load->model('activity_log');
		$this->load->model('communication/unified_notification');
		$this->load->model('communication/internal_communication');
		$this->load->model('unified_document');
		$this->load->model('workflow/visual_workflow_engine');

		// ═══════════════════════════════════════════════════════════════════════════════
		// تحميل النماذج المطلوبة للتقييمات
		// ═══════════════════════════════════════════════════════════════════════════════
		$this->load->model('catalog/review');
		$this->load->model('catalog/product');
		$this->load->model('customer/customer');
		$this->load->model('ai/sentiment_analysis');
		$this->load->model('ai/fraud_detection');

		// ═══════════════════════════════════════════════════════════════════════════════
		// تحميل ملفات اللغة الشاملة
		// ═══════════════════════════════════════════════════════════════════════════════
		$this->load->language('catalog/review');
		$this->load->language('common/header');
		$this->load->language('ai/analytics');

		// تسجيل تهيئة الكونترولر في نظام التدقيق
		$this->central_service->logActivity(
			'review_controller_initialized',
			'Review management controller initialized with central services',
			0,
			array(
				'user_id' => $this->user->getId(),
				'ip_address' => $this->request->server['REMOTE_ADDR'],
				'user_agent' => $this->request->server['HTTP_USER_AGENT']
			)
		);
	}

	/**
	 * Main index method - displays review list with advanced analytics
	 * تطبيق الدستور الشامل: الصلاحيات المزدوجة + التحليلات + الإشعارات
	 */
	public function index() {
		// ═══════════════════════════════════════════════════════════════════════════════
		// نظام الصلاحيات المزدوج (الدستور الشامل)
		// ═══════════════════════════════════════════════════════════════════════════════
		if (!$this->user->hasPermission('access', 'catalog/review')) {
			$this->central_service->logActivity(
				'access_denied',
				'Attempted to access review list without permission',
				0,
				array('user_id' => $this->user->getId(), 'ip' => $this->request->server['REMOTE_ADDR'])
			);
			$this->session->data['error'] = $this->language->get('error_permission');
			$this->response->redirect($this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true));
		}

		// فحص الصلاحيات المتقدمة
		$advanced_features = array();
		if ($this->user->hasKey('review_sentiment_analysis')) {
			$advanced_features[] = 'sentiment_analysis';
		}
		if ($this->user->hasKey('review_fraud_detection')) {
			$advanced_features[] = 'fraud_detection';
		}
		if ($this->user->hasKey('review_advanced_analytics')) {
			$advanced_features[] = 'advanced_analytics';
		}
		if ($this->user->hasKey('review_bulk_operations')) {
			$advanced_features[] = 'bulk_operations';
		}

		$this->document->setTitle($this->language->get('heading_title'));

		// ═══════════════════════════════════════════════════════════════════════════════
		// معالجة العمليات المجمعة (Bulk Actions)
		// ═══════════════════════════════════════════════════════════════════════════════
		if (isset($this->request->post['selected']) && isset($this->request->post['action'])) {
			if (!$this->user->hasKey('review_bulk_operations')) {
				$this->session->data['error'] = $this->language->get('error_permission_advanced');
				$this->response->redirect($this->url->link('catalog/review', 'user_token=' . $this->session->data['user_token'], true));
			}
			$this->handleBulkActions();
		}

		$this->getList();
	}

	public function add() {
		$this->load->language('catalog/review');

		$this->document->setTitle($this->language->get('heading_title'));

		$this->load->model('catalog/review');

		if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm()) {
			$this->model_catalog_review->addReview($this->request->post);

			$this->session->data['success'] = $this->language->get('text_success');

			$url = '';

			if (isset($this->request->get['filter_product'])) {
				$url .= '&filter_product=' . urlencode(html_entity_decode($this->request->get['filter_product'], ENT_QUOTES, 'UTF-8'));
			}

			if (isset($this->request->get['filter_author'])) {
				$url .= '&filter_author=' . urlencode(html_entity_decode($this->request->get['filter_author'], ENT_QUOTES, 'UTF-8'));
			}

			if (isset($this->request->get['filter_status'])) {
				$url .= '&filter_status=' . $this->request->get['filter_status'];
			}

			if (isset($this->request->get['filter_date_added'])) {
				$url .= '&filter_date_added=' . $this->request->get['filter_date_added'];
			}

			if (isset($this->request->get['sort'])) {
				$url .= '&sort=' . $this->request->get['sort'];
			}

			if (isset($this->request->get['order'])) {
				$url .= '&order=' . $this->request->get['order'];
			}

			if (isset($this->request->get['page'])) {
				$url .= '&page=' . $this->request->get['page'];
			}

			$this->response->redirect($this->url->link('catalog/review', 'user_token=' . $this->session->data['user_token'] . $url, true));
		}

		$this->getForm();
	}

	public function edit() {
		$this->load->language('catalog/review');

		$this->document->setTitle($this->language->get('heading_title'));

		$this->load->model('catalog/review');

		if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm()) {
			$this->model_catalog_review->editReview($this->request->get['review_id'], $this->request->post);

			$this->session->data['success'] = $this->language->get('text_success');

			$url = '';

			if (isset($this->request->get['filter_product'])) {
				$url .= '&filter_product=' . urlencode(html_entity_decode($this->request->get['filter_product'], ENT_QUOTES, 'UTF-8'));
			}

			if (isset($this->request->get['filter_author'])) {
				$url .= '&filter_author=' . urlencode(html_entity_decode($this->request->get['filter_author'], ENT_QUOTES, 'UTF-8'));
			}

			if (isset($this->request->get['filter_status'])) {
				$url .= '&filter_status=' . $this->request->get['filter_status'];
			}

			if (isset($this->request->get['filter_date_added'])) {
				$url .= '&filter_date_added=' . $this->request->get['filter_date_added'];
			}

			if (isset($this->request->get['sort'])) {
				$url .= '&sort=' . $this->request->get['sort'];
			}

			if (isset($this->request->get['order'])) {
				$url .= '&order=' . $this->request->get['order'];
			}

			if (isset($this->request->get['page'])) {
				$url .= '&page=' . $this->request->get['page'];
			}

			$this->response->redirect($this->url->link('catalog/review', 'user_token=' . $this->session->data['user_token'] . $url, true));
		}

		$this->getForm();
	}

	public function delete() {
		$this->load->language('catalog/review');

		$this->document->setTitle($this->language->get('heading_title'));

		$this->load->model('catalog/review');

		if (isset($this->request->post['selected']) && $this->validateDelete()) {
			foreach ($this->request->post['selected'] as $review_id) {
				$this->model_catalog_review->deleteReview($review_id);
			}

			$this->session->data['success'] = $this->language->get('text_success');

			$url = '';

			if (isset($this->request->get['filter_product'])) {
				$url .= '&filter_product=' . urlencode(html_entity_decode($this->request->get['filter_product'], ENT_QUOTES, 'UTF-8'));
			}

			if (isset($this->request->get['filter_author'])) {
				$url .= '&filter_author=' . urlencode(html_entity_decode($this->request->get['filter_author'], ENT_QUOTES, 'UTF-8'));
			}

			if (isset($this->request->get['filter_status'])) {
				$url .= '&filter_status=' . $this->request->get['filter_status'];
			}

			if (isset($this->request->get['filter_date_added'])) {
				$url .= '&filter_date_added=' . $this->request->get['filter_date_added'];
			}

			if (isset($this->request->get['sort'])) {
				$url .= '&sort=' . $this->request->get['sort'];
			}

			if (isset($this->request->get['order'])) {
				$url .= '&order=' . $this->request->get['order'];
			}

			if (isset($this->request->get['page'])) {
				$url .= '&page=' . $this->request->get['page'];
			}

			$this->response->redirect($this->url->link('catalog/review', 'user_token=' . $this->session->data['user_token'] . $url, true));
		}

		$this->getList();
	}

	protected function getList() {
		if (isset($this->request->get['filter_product'])) {
			$filter_product = $this->request->get['filter_product'];
		} else {
			$filter_product = '';
		}

		if (isset($this->request->get['filter_author'])) {
			$filter_author = $this->request->get['filter_author'];
		} else {
			$filter_author = '';
		}

		if (isset($this->request->get['filter_status'])) {
			$filter_status = $this->request->get['filter_status'];
		} else {
			$filter_status = '';
		}

		if (isset($this->request->get['filter_date_added'])) {
			$filter_date_added = $this->request->get['filter_date_added'];
		} else {
			$filter_date_added = '';
		}

		if (isset($this->request->get['order'])) {
			$order = $this->request->get['order'];
		} else {
			$order = 'DESC';
		}

		if (isset($this->request->get['sort'])) {
			$sort = $this->request->get['sort'];
		} else {
			$sort = 'r.date_added';
		}

		if (isset($this->request->get['page'])) {
			$page = (int)$this->request->get['page'];
		} else {
			$page = 1;
		}

		$url = '';

		if (isset($this->request->get['filter_product'])) {
			$url .= '&filter_product=' . urlencode(html_entity_decode($this->request->get['filter_product'], ENT_QUOTES, 'UTF-8'));
		}

		if (isset($this->request->get['filter_author'])) {
			$url .= '&filter_author=' . urlencode(html_entity_decode($this->request->get['filter_author'], ENT_QUOTES, 'UTF-8'));
		}

		if (isset($this->request->get['filter_status'])) {
			$url .= '&filter_status=' . $this->request->get['filter_status'];
		}

		if (isset($this->request->get['filter_date_added'])) {
			$url .= '&filter_date_added=' . $this->request->get['filter_date_added'];
		}

		if (isset($this->request->get['sort'])) {
			$url .= '&sort=' . $this->request->get['sort'];
		}

		if (isset($this->request->get['order'])) {
			$url .= '&order=' . $this->request->get['order'];
		}

		if (isset($this->request->get['page'])) {
			$url .= '&page=' . $this->request->get['page'];
		}

		$data['breadcrumbs'] = array();

		$data['breadcrumbs'][] = array(
			'text' => $this->language->get('text_home'),
			'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
		);

		$data['breadcrumbs'][] = array(
			'text' => $this->language->get('heading_title'),
			'href' => $this->url->link('catalog/review', 'user_token=' . $this->session->data['user_token'] . $url, true)
		);

		$data['add'] = $this->url->link('catalog/review/add', 'user_token=' . $this->session->data['user_token'] . $url, true);
		$data['delete'] = $this->url->link('catalog/review/delete', 'user_token=' . $this->session->data['user_token'] . $url, true);

		$data['reviews'] = array();

		$filter_data = array(
			'filter_product'    => $filter_product,
			'filter_author'     => $filter_author,
			'filter_status'     => $filter_status,
			'filter_date_added' => $filter_date_added,
			'sort'              => $sort,
			'order'             => $order,
			'start'             => ($page - 1) * $this->config->get('config_limit_admin'),
			'limit'             => $this->config->get('config_limit_admin')
		);

		$review_total = $this->model_catalog_review->getTotalReviews($filter_data);

		$results = $this->model_catalog_review->getReviews($filter_data);

		foreach ($results as $result) {
			$data['reviews'][] = array(
				'review_id'  => $result['review_id'],
				'name'       => $result['name'],
				'author'     => $result['author'],
				'rating'     => $result['rating'],
				'status'     => ($result['status']) ? $this->language->get('text_enabled') : $this->language->get('text_disabled'),
				'date_added' => date($this->language->get('date_format_short'), strtotime($result['date_added'])),
				'edit'       => $this->url->link('catalog/review/edit', 'user_token=' . $this->session->data['user_token'] . '&review_id=' . $result['review_id'] . $url, true)
			);
		}

		$data['user_token'] = $this->session->data['user_token'];

		if (isset($this->error['warning'])) {
			$data['error_warning'] = $this->error['warning'];
		} else {
			$data['error_warning'] = '';
		}

		if (isset($this->session->data['success'])) {
			$data['success'] = $this->session->data['success'];

			unset($this->session->data['success']);
		} else {
			$data['success'] = '';
		}

		if (isset($this->request->post['selected'])) {
			$data['selected'] = (array)$this->request->post['selected'];
		} else {
			$data['selected'] = array();
		}

		$url = '';

		if (isset($this->request->get['filter_product'])) {
			$url .= '&filter_product=' . urlencode(html_entity_decode($this->request->get['filter_product'], ENT_QUOTES, 'UTF-8'));
		}

		if (isset($this->request->get['filter_author'])) {
			$url .= '&filter_author=' . urlencode(html_entity_decode($this->request->get['filter_author'], ENT_QUOTES, 'UTF-8'));
		}

		if (isset($this->request->get['filter_status'])) {
			$url .= '&filter_status=' . $this->request->get['filter_status'];
		}

		if (isset($this->request->get['filter_date_added'])) {
			$url .= '&filter_date_added=' . $this->request->get['filter_date_added'];
		}

		if ($order == 'ASC') {
			$url .= '&order=DESC';
		} else {
			$url .= '&order=ASC';
		}

		if (isset($this->request->get['page'])) {
			$url .= '&page=' . $this->request->get['page'];
		}

		$data['sort_product'] = $this->url->link('catalog/review', 'user_token=' . $this->session->data['user_token'] . '&sort=pd.name' . $url, true);
		$data['sort_author'] = $this->url->link('catalog/review', 'user_token=' . $this->session->data['user_token'] . '&sort=r.author' . $url, true);
		$data['sort_rating'] = $this->url->link('catalog/review', 'user_token=' . $this->session->data['user_token'] . '&sort=r.rating' . $url, true);
		$data['sort_status'] = $this->url->link('catalog/review', 'user_token=' . $this->session->data['user_token'] . '&sort=r.status' . $url, true);
		$data['sort_date_added'] = $this->url->link('catalog/review', 'user_token=' . $this->session->data['user_token'] . '&sort=r.date_added' . $url, true);

		$url = '';

		if (isset($this->request->get['filter_product'])) {
			$url .= '&filter_product=' . urlencode(html_entity_decode($this->request->get['filter_product'], ENT_QUOTES, 'UTF-8'));
		}

		if (isset($this->request->get['filter_author'])) {
			$url .= '&filter_author=' . urlencode(html_entity_decode($this->request->get['filter_author'], ENT_QUOTES, 'UTF-8'));
		}

		if (isset($this->request->get['filter_status'])) {
			$url .= '&filter_status=' . $this->request->get['filter_status'];
		}

		if (isset($this->request->get['filter_date_added'])) {
			$url .= '&filter_date_added=' . $this->request->get['filter_date_added'];
		}

		if (isset($this->request->get['sort'])) {
			$url .= '&sort=' . $this->request->get['sort'];
		}

		if (isset($this->request->get['order'])) {
			$url .= '&order=' . $this->request->get['order'];
		}

		$pagination = new Pagination();
		$pagination->total = $review_total;
		$pagination->page = $page;
		$pagination->limit = $this->config->get('config_limit_admin');
		$pagination->url = $this->url->link('catalog/review', 'user_token=' . $this->session->data['user_token'] . $url . '&page={page}', true);

		$data['pagination'] = $pagination->render();

		$data['results'] = sprintf($this->language->get('text_pagination'), ($review_total) ? (($page - 1) * $this->config->get('config_limit_admin')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit_admin')) > ($review_total - $this->config->get('config_limit_admin'))) ? $review_total : ((($page - 1) * $this->config->get('config_limit_admin')) + $this->config->get('config_limit_admin')), $review_total, ceil($review_total / $this->config->get('config_limit_admin')));

		$data['filter_product'] = $filter_product;
		$data['filter_author'] = $filter_author;
		$data['filter_status'] = $filter_status;
		$data['filter_date_added'] = $filter_date_added;

		$data['sort'] = $sort;
		$data['order'] = $order;

		$data['header'] = $this->load->controller('common/header');
		$data['column_left'] = $this->load->controller('common/column_left');
		$data['footer'] = $this->load->controller('common/footer');

		$this->response->setOutput($this->load->view('catalog/review_list', $data));
	}

	protected function getForm() {
		$data['text_form'] = !isset($this->request->get['review_id']) ? $this->language->get('text_add') : $this->language->get('text_edit');

		if (isset($this->error['warning'])) {
			$data['error_warning'] = $this->error['warning'];
		} else {
			$data['error_warning'] = '';
		}

		if (isset($this->error['product'])) {
			$data['error_product'] = $this->error['product'];
		} else {
			$data['error_product'] = '';
		}

		if (isset($this->error['author'])) {
			$data['error_author'] = $this->error['author'];
		} else {
			$data['error_author'] = '';
		}

		if (isset($this->error['text'])) {
			$data['error_text'] = $this->error['text'];
		} else {
			$data['error_text'] = '';
		}

		if (isset($this->error['rating'])) {
			$data['error_rating'] = $this->error['rating'];
		} else {
			$data['error_rating'] = '';
		}

		$url = '';

		if (isset($this->request->get['filter_product'])) {
			$url .= '&filter_product=' . urlencode(html_entity_decode($this->request->get['filter_product'], ENT_QUOTES, 'UTF-8'));
		}

		if (isset($this->request->get['filter_author'])) {
			$url .= '&filter_author=' . urlencode(html_entity_decode($this->request->get['filter_author'], ENT_QUOTES, 'UTF-8'));
		}

		if (isset($this->request->get['filter_status'])) {
			$url .= '&filter_status=' . $this->request->get['filter_status'];
		}

		if (isset($this->request->get['filter_date_added'])) {
			$url .= '&filter_date_added=' . $this->request->get['filter_date_added'];
		}

		if (isset($this->request->get['sort'])) {
			$url .= '&sort=' . $this->request->get['sort'];
		}

		if (isset($this->request->get['order'])) {
			$url .= '&order=' . $this->request->get['order'];
		}

		if (isset($this->request->get['page'])) {
			$url .= '&page=' . $this->request->get['page'];
		}

		$data['breadcrumbs'] = array();

		$data['breadcrumbs'][] = array(
			'text' => $this->language->get('text_home'),
			'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
		);

		$data['breadcrumbs'][] = array(
			'text' => $this->language->get('heading_title'),
			'href' => $this->url->link('catalog/review', 'user_token=' . $this->session->data['user_token'] . $url, true)
		);

		if (!isset($this->request->get['review_id'])) {
			$data['action'] = $this->url->link('catalog/review/add', 'user_token=' . $this->session->data['user_token'] . $url, true);
		} else {
			$data['action'] = $this->url->link('catalog/review/edit', 'user_token=' . $this->session->data['user_token'] . '&review_id=' . $this->request->get['review_id'] . $url, true);
		}

		$data['cancel'] = $this->url->link('catalog/review', 'user_token=' . $this->session->data['user_token'] . $url, true);

		if (isset($this->request->get['review_id']) && ($this->request->server['REQUEST_METHOD'] != 'POST')) {
			$review_info = $this->model_catalog_review->getReview($this->request->get['review_id']);
		}

		$data['user_token'] = $this->session->data['user_token'];

		$this->load->model('catalog/product');

		if (isset($this->request->post['product_id'])) {
			$data['product_id'] = $this->request->post['product_id'];
		} elseif (!empty($review_info)) {
			$data['product_id'] = $review_info['product_id'];
		} else {
			$data['product_id'] = '';
		}

		if (isset($this->request->post['product'])) {
			$data['product'] = $this->request->post['product'];
		} elseif (!empty($review_info)) {
			$data['product'] = $review_info['product'];
		} else {
			$data['product'] = '';
		}

		if (isset($this->request->post['author'])) {
			$data['author'] = $this->request->post['author'];
		} elseif (!empty($review_info)) {
			$data['author'] = $review_info['author'];
		} else {
			$data['author'] = '';
		}

		if (isset($this->request->post['text'])) {
			$data['text'] = $this->request->post['text'];
		} elseif (!empty($review_info)) {
			$data['text'] = $review_info['text'];
		} else {
			$data['text'] = '';
		}

		if (isset($this->request->post['rating'])) {
			$data['rating'] = $this->request->post['rating'];
		} elseif (!empty($review_info)) {
			$data['rating'] = $review_info['rating'];
		} else {
			$data['rating'] = '';
		}

		if (isset($this->request->post['date_added'])) {
			$data['date_added'] = $this->request->post['date_added'];
		} elseif (!empty($review_info)) {
			$data['date_added'] = ($review_info['date_added'] != '0000-00-00 00:00' ? $review_info['date_added'] : '');
		} else {
			$data['date_added'] = '';
		}

		if (isset($this->request->post['status'])) {
			$data['status'] = $this->request->post['status'];
		} elseif (!empty($review_info)) {
			$data['status'] = $review_info['status'];
		} else {
			$data['status'] = '';
		}

		$data['header'] = $this->load->controller('common/header');
		$data['column_left'] = $this->load->controller('common/column_left');
		$data['footer'] = $this->load->controller('common/footer');

		$this->response->setOutput($this->load->view('catalog/review_form', $data));
	}

	protected function validateForm() {
		if (!$this->user->hasPermission('modify', 'catalog/review')) {
			$this->error['warning'] = $this->language->get('error_permission');
		}

		if (!$this->request->post['product_id']) {
			$this->error['product'] = $this->language->get('error_product');
		}

		if ((utf8_strlen($this->request->post['author']) < 3) || (utf8_strlen($this->request->post['author']) > 64)) {
			$this->error['author'] = $this->language->get('error_author');
		}

		if (utf8_strlen($this->request->post['text']) < 1) {
			$this->error['text'] = $this->language->get('error_text');
		}

		if (!isset($this->request->post['rating']) || $this->request->post['rating'] < 0 || $this->request->post['rating'] > 5) {
			$this->error['rating'] = $this->language->get('error_rating');
		}

		return !$this->error;
	}

	protected function validateDelete() {
		if (!$this->user->hasPermission('modify', 'catalog/review')) {
			$this->error['warning'] = $this->language->get('error_permission');
		}

		return !$this->error;
	}

	// ═══════════════════════════════════════════════════════════════════════════════
	// دوال العمليات المجمعة - Enterprise Grade Plus
	// ═══════════════════════════════════════════════════════════════════════════════

	/**
	 * Handle bulk actions for reviews
	 */
	protected function handleBulkActions() {
		$action = $this->request->post['action'];
		$selected = $this->request->post['selected'];

		if (empty($selected)) {
			$this->session->data['error'] = $this->language->get('error_no_selection');
			return;
		}

		$success_count = 0;

		try {
			switch ($action) {
				case 'approve':
					foreach ($selected as $review_id) {
						$this->model_catalog_review->updateReviewStatus($review_id, 1);
						$success_count++;
					}
					$this->session->data['success'] = sprintf($this->language->get('text_bulk_approve_success'), $success_count);
					break;

				case 'reject':
					foreach ($selected as $review_id) {
						$this->model_catalog_review->updateReviewStatus($review_id, 0);
						$success_count++;
					}
					$this->session->data['success'] = sprintf($this->language->get('text_bulk_reject_success'), $success_count);
					break;

				case 'delete':
					foreach ($selected as $review_id) {
						$this->model_catalog_review->deleteReview($review_id);
						$success_count++;
					}
					$this->session->data['success'] = sprintf($this->language->get('text_bulk_delete_success'), $success_count);
					break;

				case 'analyze_sentiment':
					if (!$this->user->hasKey('review_sentiment_analysis')) {
						$this->session->data['error'] = $this->language->get('error_permission_advanced');
						return;
					}
					foreach ($selected as $review_id) {
						$this->analyzeSentiment($review_id);
						$success_count++;
					}
					$this->session->data['success'] = sprintf($this->language->get('text_bulk_sentiment_success'), $success_count);
					break;

				case 'check_fraud':
					if (!$this->user->hasKey('review_fraud_detection')) {
						$this->session->data['error'] = $this->language->get('error_permission_advanced');
						return;
					}
					foreach ($selected as $review_id) {
						$this->checkFraud($review_id);
						$success_count++;
					}
					$this->session->data['success'] = sprintf($this->language->get('text_bulk_fraud_check_success'), $success_count);
					break;

				default:
					$this->session->data['error'] = $this->language->get('error_invalid_action');
					return;
			}

			// تسجيل العملية المجمعة
			$this->central_service->logActivity(
				'bulk_review_action',
				'Performed bulk action: ' . $action . ' on ' . $success_count . ' reviews',
				0,
				array(
					'action' => $action,
					'affected_reviews' => $selected,
					'success_count' => $success_count,
					'user_id' => $this->user->getId()
				)
			);

		} catch (Exception $e) {
			$this->session->data['error'] = $e->getMessage();
		}
	}

	// ═══════════════════════════════════════════════════════════════════════════════
	// دوال الذكاء الاصطناعي - Enterprise Grade Plus
	// ═══════════════════════════════════════════════════════════════════════════════

	/**
	 * Analyze sentiment of a review using AI
	 */
	public function analyzeSentiment($review_id = null) {
		if (!$this->user->hasKey('review_sentiment_analysis')) {
			$this->session->data['error'] = $this->language->get('error_permission_advanced');
			$this->response->redirect($this->url->link('catalog/review', 'user_token=' . $this->session->data['user_token'], true));
		}

		if (!$review_id && isset($this->request->get['review_id'])) {
			$review_id = $this->request->get['review_id'];
		}

		if (!$review_id) {
			$this->session->data['error'] = $this->language->get('error_review_id_required');
			$this->response->redirect($this->url->link('catalog/review', 'user_token=' . $this->session->data['user_token'], true));
		}

		try {
			$review = $this->model_catalog_review->getReview($review_id);
			if (!$review) {
				throw new Exception($this->language->get('error_review_not_found'));
			}

			// تحليل المشاعر باستخدام الذكاء الاصطناعي
			$sentiment_result = $this->model_ai_sentiment_analysis->analyzeText($review['text']);

			// حفظ نتائج التحليل
			$this->model_catalog_review->updateSentimentAnalysis($review_id, $sentiment_result);

			// تسجيل النشاط
			$this->central_service->logActivity(
				'review_sentiment_analyzed',
				'Sentiment analysis performed for review ID: ' . $review_id,
				$review_id,
				array(
					'sentiment_score' => $sentiment_result['score'],
					'sentiment_label' => $sentiment_result['label'],
					'confidence' => $sentiment_result['confidence'],
					'user_id' => $this->user->getId()
				)
			);

			if (!isset($this->request->get['bulk'])) {
				$this->session->data['success'] = $this->language->get('text_sentiment_analysis_success');
				$this->response->redirect($this->url->link('catalog/review', 'user_token=' . $this->session->data['user_token'], true));
			}

		} catch (Exception $e) {
			if (!isset($this->request->get['bulk'])) {
				$this->session->data['error'] = $e->getMessage();
				$this->response->redirect($this->url->link('catalog/review', 'user_token=' . $this->session->data['user_token'], true));
			}
			throw $e;
		}
	}

	/**
	 * Check for fraudulent reviews using AI
	 */
	public function checkFraud($review_id = null) {
		if (!$this->user->hasKey('review_fraud_detection')) {
			$this->session->data['error'] = $this->language->get('error_permission_advanced');
			$this->response->redirect($this->url->link('catalog/review', 'user_token=' . $this->session->data['user_token'], true));
		}

		if (!$review_id && isset($this->request->get['review_id'])) {
			$review_id = $this->request->get['review_id'];
		}

		if (!$review_id) {
			$this->session->data['error'] = $this->language->get('error_review_id_required');
			$this->response->redirect($this->url->link('catalog/review', 'user_token=' . $this->session->data['user_token'], true));
		}

		try {
			$review = $this->model_catalog_review->getReview($review_id);
			if (!$review) {
				throw new Exception($this->language->get('error_review_not_found'));
			}

			// فحص الاحتيال باستخدام الذكاء الاصطناعي
			$fraud_result = $this->model_ai_fraud_detection->checkReviewFraud($review);

			// حفظ نتائج الفحص
			$this->model_catalog_review->updateFraudCheck($review_id, $fraud_result);

			// إرسال تنبيه إذا كان التقييم مشبوه
			if ($fraud_result['is_suspicious']) {
				$this->central_service->sendNotification(
					'review_fraud_detected',
					'Suspicious review detected: ' . $review_id,
					array('review_id' => $review_id, 'fraud_score' => $fraud_result['fraud_score']),
					array('admin', 'review_moderator')
				);
			}

			// تسجيل النشاط
			$this->central_service->logActivity(
				'review_fraud_checked',
				'Fraud detection performed for review ID: ' . $review_id,
				$review_id,
				array(
					'fraud_score' => $fraud_result['fraud_score'],
					'is_suspicious' => $fraud_result['is_suspicious'],
					'fraud_indicators' => $fraud_result['indicators'],
					'user_id' => $this->user->getId()
				)
			);

			if (!isset($this->request->get['bulk'])) {
				$this->session->data['success'] = $this->language->get('text_fraud_check_success');
				$this->response->redirect($this->url->link('catalog/review', 'user_token=' . $this->session->data['user_token'], true));
			}

		} catch (Exception $e) {
			if (!isset($this->request->get['bulk'])) {
				$this->session->data['error'] = $e->getMessage();
				$this->response->redirect($this->url->link('catalog/review', 'user_token=' . $this->session->data['user_token'], true));
			}
			throw $e;
		}
	}

	// ═══════════════════════════════════════════════════════════════════════════════
	// دوال التقارير والتحليلات المتقدمة - Enterprise Grade Plus
	// ═══════════════════════════════════════════════════════════════════════════════

	/**
	 * Generate comprehensive review analytics report
	 */
	public function analytics() {
		if (!$this->user->hasKey('review_advanced_analytics')) {
			$this->session->data['error'] = $this->language->get('error_permission_advanced');
			$this->response->redirect($this->url->link('catalog/review', 'user_token=' . $this->session->data['user_token'], true));
		}

		$data = array();

		// فترة التقرير
		$date_start = isset($this->request->get['date_start']) ? $this->request->get['date_start'] : date('Y-m-01', strtotime('-6 months'));
		$date_end = isset($this->request->get['date_end']) ? $this->request->get['date_end'] : date('Y-m-d');

		// إحصائيات عامة
		$data['total_reviews'] = $this->model_catalog_review->getTotalReviews($date_start, $date_end);
		$data['approved_reviews'] = $this->model_catalog_review->getApprovedReviews($date_start, $date_end);
		$data['pending_reviews'] = $this->model_catalog_review->getPendingReviews($date_start, $date_end);
		$data['average_rating'] = $this->model_catalog_review->getAverageRating($date_start, $date_end);

		// تحليل المشاعر
		if ($this->user->hasKey('review_sentiment_analysis')) {
			$data['sentiment_analysis'] = $this->model_catalog_review->getSentimentAnalysis($date_start, $date_end);
		}

		// كشف الاحتيال
		if ($this->user->hasKey('review_fraud_detection')) {
			$data['fraud_statistics'] = $this->model_catalog_review->getFraudStatistics($date_start, $date_end);
		}

		// أفضل المنتجات حسب التقييمات
		$data['top_rated_products'] = $this->model_catalog_review->getTopRatedProducts($date_start, $date_end, 10);

		// أسوأ المنتجات حسب التقييمات
		$data['worst_rated_products'] = $this->model_catalog_review->getWorstRatedProducts($date_start, $date_end, 10);

		// اتجاهات التقييمات الشهرية
		$data['monthly_trends'] = $this->model_catalog_review->getMonthlyTrends($date_start, $date_end);

		// توزيع التقييمات (1-5 نجوم)
		$data['rating_distribution'] = $this->model_catalog_review->getRatingDistribution($date_start, $date_end);

		// أكثر العملاء نشاطاً في التقييم
		$data['most_active_reviewers'] = $this->model_catalog_review->getMostActiveReviewers($date_start, $date_end, 10);

		$data['date_start'] = $date_start;
		$data['date_end'] = $date_end;

		// تسجيل النشاط
		$this->central_service->logActivity(
			'review_analytics_generated',
			'Generated review analytics report for period: ' . $date_start . ' to ' . $date_end,
			0,
			array(
				'date_start' => $date_start,
				'date_end' => $date_end,
				'total_reviews' => $data['total_reviews'],
				'user_id' => $this->user->getId()
			)
		);

		// عرض التقرير
		$data['header'] = $this->load->controller('common/header');
		$data['column_left'] = $this->load->controller('common/column_left');
		$data['footer'] = $this->load->controller('common/footer');

		$this->response->setOutput($this->load->view('catalog/review_analytics', $data));
	}

	/**
	 * Generate product reputation report
	 */
	public function productReputation() {
		if (!$this->user->hasKey('review_advanced_analytics')) {
			$this->session->data['error'] = $this->language->get('error_permission_advanced');
			$this->response->redirect($this->url->link('catalog/review', 'user_token=' . $this->session->data['user_token'], true));
		}

		$data = array();

		// الحصول على تقرير سمعة المنتجات
		$reputation_data = $this->model_catalog_review->getProductReputationReport();

		$data['products'] = array();
		foreach ($reputation_data as $product) {
			$data['products'][] = array(
				'product_id' => $product['product_id'],
				'name' => $product['name'],
				'total_reviews' => $product['total_reviews'],
				'average_rating' => number_format($product['average_rating'], 2),
				'positive_reviews' => $product['positive_reviews'],
				'negative_reviews' => $product['negative_reviews'],
				'reputation_score' => number_format($product['reputation_score'], 2),
				'reputation_trend' => $product['reputation_trend'],
				'last_review_date' => $product['last_review_date'],
				'edit' => $this->url->link('catalog/product/edit', 'user_token=' . $this->session->data['user_token'] . '&product_id=' . $product['product_id'], true)
			);
		}

		// تسجيل النشاط
		$this->central_service->logActivity(
			'product_reputation_report_generated',
			'Generated product reputation report',
			0,
			array(
				'products_analyzed' => count($reputation_data),
				'user_id' => $this->user->getId()
			)
		);

		// عرض التقرير
		$data['header'] = $this->load->controller('common/header');
		$data['column_left'] = $this->load->controller('common/column_left');
		$data['footer'] = $this->load->controller('common/footer');

		$this->response->setOutput($this->load->view('catalog/product_reputation_report', $data));
	}

	// ═══════════════════════════════════════════════════════════════════════════════
	// دوال AJAX للتفاعل المتقدم - Enterprise Grade Plus
	// ═══════════════════════════════════════════════════════════════════════════════

	/**
	 * Quick approve/reject review via AJAX
	 */
	public function quickAction() {
		$json = array();

		if (!$this->user->hasPermission('modify', 'catalog/review')) {
			$json['error'] = $this->language->get('error_permission');
		} else {
			if (isset($this->request->post['review_id']) && isset($this->request->post['action'])) {
				$review_id = $this->request->post['review_id'];
				$action = $this->request->post['action'];

				try {
					switch ($action) {
						case 'approve':
							$this->model_catalog_review->updateReviewStatus($review_id, 1);
							$json['success'] = true;
							$json['message'] = $this->language->get('text_review_approved');
							break;

						case 'reject':
							$this->model_catalog_review->updateReviewStatus($review_id, 0);
							$json['success'] = true;
							$json['message'] = $this->language->get('text_review_rejected');
							break;

						case 'delete':
							$this->model_catalog_review->deleteReview($review_id);
							$json['success'] = true;
							$json['message'] = $this->language->get('text_review_deleted');
							break;

						default:
							$json['error'] = $this->language->get('error_invalid_action');
					}

					if ($json['success']) {
						// تسجيل النشاط
						$this->central_service->logActivity(
							'review_quick_action',
							'Quick action performed: ' . $action . ' on review ID: ' . $review_id,
							$review_id,
							array(
								'action' => $action,
								'user_id' => $this->user->getId()
							)
						);
					}

				} catch (Exception $e) {
					$json['error'] = $e->getMessage();
				}
			} else {
				$json['error'] = $this->language->get('error_missing_data');
			}
		}

		$this->response->addHeader('Content-Type: application/json');
		$this->response->setOutput(json_encode($json));
	}

	/**
	 * Get review details via AJAX
	 */
	public function getReviewDetails() {
		$json = array();

		if (isset($this->request->get['review_id'])) {
			$review_id = $this->request->get['review_id'];
			$review = $this->model_catalog_review->getReview($review_id);

			if ($review) {
				$json['review'] = $review;

				// إضافة تحليل المشاعر إذا متاح
				if ($this->user->hasKey('review_sentiment_analysis')) {
					$json['sentiment'] = $this->model_catalog_review->getSentimentAnalysis($review_id);
				}

				// إضافة فحص الاحتيال إذا متاح
				if ($this->user->hasKey('review_fraud_detection')) {
					$json['fraud_check'] = $this->model_catalog_review->getFraudCheck($review_id);
				}

				$json['success'] = true;
			} else {
				$json['error'] = $this->language->get('error_review_not_found');
			}
		} else {
			$json['error'] = $this->language->get('error_review_id_required');
		}

		$this->response->addHeader('Content-Type: application/json');
		$this->response->setOutput(json_encode($json));
	}

	// ═══════════════════════════════════════════════════════════════════════════════
	// دوال الرد على التقييمات - Enterprise Grade Plus
	// ═══════════════════════════════════════════════════════════════════════════════

	/**
	 * Add reply to review
	 */
	public function addReply() {
		if (!$this->user->hasKey('review_reply_management')) {
			$this->session->data['error'] = $this->language->get('error_permission_advanced');
			$this->response->redirect($this->url->link('catalog/review', 'user_token=' . $this->session->data['user_token'], true));
		}

		$json = array();

		if ($this->request->server['REQUEST_METHOD'] == 'POST') {
			if (isset($this->request->post['review_id']) && isset($this->request->post['reply_text'])) {
				$review_id = $this->request->post['review_id'];
				$reply_text = trim($this->request->post['reply_text']);

				if (empty($reply_text)) {
					$json['error'] = $this->language->get('error_reply_text_required');
				} else {
					try {
						// إضافة الرد
						$reply_id = $this->model_catalog_review->addReply($review_id, array(
							'reply_text' => $reply_text,
							'author_name' => $this->user->getUserName(),
							'author_type' => 'admin',
							'date_added' => date('Y-m-d H:i:s')
						));

						// إرسال إشعار للعميل
						$review = $this->model_catalog_review->getReview($review_id);
						if ($review && $review['customer_id']) {
							$this->central_service->sendNotification(
								'review_reply_added',
								'Admin replied to your review',
								array(
									'review_id' => $review_id,
									'reply_text' => $reply_text,
									'product_name' => $review['product']
								),
								array('customer_' . $review['customer_id'])
							);
						}

						// تسجيل النشاط
						$this->central_service->logActivity(
							'review_reply_added',
							'Reply added to review ID: ' . $review_id,
							$review_id,
							array(
								'reply_id' => $reply_id,
								'reply_text' => $reply_text,
								'user_id' => $this->user->getId()
							)
						);

						$json['success'] = true;
						$json['message'] = $this->language->get('text_reply_added_success');

					} catch (Exception $e) {
						$json['error'] = $e->getMessage();
					}
				}
			} else {
				$json['error'] = $this->language->get('error_missing_data');
			}
		}

		$this->response->addHeader('Content-Type: application/json');
		$this->response->setOutput(json_encode($json));
	}
}
