{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="submit" form="form-shipping" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fa fa-save"></i></button>
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a></div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>

  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}

    <form action="{{ action }}" method="post" enctype="multipart/form-data" id="form-shipping" class="form-horizontal">
     <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pencil"></i> {{ text_edit }}</h3>
      </div>
      <div class="panel-body">
          <div class="form-group">
            <label class="col-sm-2 control-label">{{ entry_test }}</label>
            <div class="col-sm-10">
              <label class="radio-inline">
                {% if shipping_hitshippo_fedex_test %}
                <input type="radio" name="shipping_hitshippo_fedex_test" value="1" checked="checked" />
                {{ text_yes }}
                {% else %}
                <input type="radio" name="shipping_hitshippo_fedex_test" value="1" />
                {{ text_yes }}
                {% endif %}
              </label>
              <label class="radio-inline">
                {% if not shipping_hitshippo_fedex_test %}
                <input type="radio" name="shipping_hitshippo_fedex_test" value="0" checked="checked" />
                {{ text_no }}
                {% else %}
                <input type="radio" name="shipping_hitshippo_fedex_test" value="0" />
                {{ text_no }}
                {% endif %}
              </label>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label">{{ entry_api_type }}</label>
            <div class="col-sm-10">
              <label class="radio-inline">
                {% if shipping_hitshippo_fedex_api_type == "REST" %}
                <input type="radio" name="shipping_hitshippo_fedex_api_type" value="REST" checked="checked" />
                {{ entry_api_type_rest }}
                {% else %}
                <input type="radio" name="shipping_hitshippo_fedex_api_type" value="REST" />
                {{ entry_api_type_rest }}
                {% endif %}
              </label>
              <label class="radio-inline">
                {% if shipping_hitshippo_fedex_api_type == "SOAP" %}
                <input type="radio" name="shipping_hitshippo_fedex_api_type" value="SOAP" checked="checked" />
                {{ entry_api_type_soap }}
                {% else %}
                <input type="radio" name="shipping_hitshippo_fedex_api_type" value="SOAP" />
                {{ entry_api_type_soap }}
                {% endif %}
              </label>
            </div>
          </div>
          <div class="form-group REST_field">
            <label class="col-sm-2 control-label">{{ entry_rest_api_key }}</label>
            <div class="col-sm-10">
              <input type="text" name="shipping_hitshippo_fedex_rest_api_key" value="{{ shipping_hitshippo_fedex_rest_api_key }}" class="form-control" />
              {% if rest_key %}
              <div class="text-danger">{{ rest_key }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group REST_field">
            <label class="col-sm-2 control-label">{{ entry_rest_api_sec }}</label>
            <div class="col-sm-10">
              <input type="text" name="shipping_hitshippo_fedex_rest_api_sec" value="{{ shipping_hitshippo_fedex_rest_api_sec }}" class="form-control" />
              {% if rest_pwd %}
              <div class="text-danger">{{ rest_pwd }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group REST_field">
            <label class="col-sm-2 control-label">{{ entry_account }}</label>
            <div class="col-sm-10">
              <input type="text" name="shipping_hitshippo_fedex_rest_acc_num" value="{{ shipping_hitshippo_fedex_rest_acc_num }}" class="form-control" />
              {% if rest_account %}
              <div class="text-danger">{{ rest_account }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group REST_field">
            <label class="col-sm-2 control-label">{{ entry_rest_api_grant }}</label>
            <div class="col-sm-10">
              <select name="shipping_hitshippo_fedex_rest_grant_type" class="form-control">
                {% if shipping_hitshippo_fedex_rest_grant_type == "client_credentials" %}
                  <option value="client_credentials" selected="selected">{{ entry_rest_api_grant_client }}</option>
                {% else %}
                  <option value="client_credentials">{{ entry_rest_api_grant_client }}</option>
                {% endif %}
              </select>
            </div>
          </div>
      <div class="form-group SOAP_field">
            <label class="col-sm-2 control-label" for="input-key">{{ entry_key }}</label>
            <div class="col-sm-10">
              <input type="text" name="shipping_hitshippo_fedex_key" value="{{ shipping_hitshippo_fedex_key }}" placeholder="{{ entry_key }}" id="input-key" class="form-control" />
              {% if error_key %}
              <div class="text-danger">{{ error_key }}</div>
              {% endif %}
            </div>
          </div>
            <div class="form-group SOAP_field">
            <label class="col-sm-2 control-label" for="input-account">{{ entry_password }}</label>
            <div class="col-sm-10">
              <input type="text" name="shipping_hitshippo_fedex_password" value="{{ shipping_hitshippo_fedex_password }}" placeholder="{{ entry_password }}" id="input-password" class="form-control" />
             {% if error_password %}
              <div class="text-danger">{{ error_password }}</div>
              {% endif %}
             </div>
          </div>
          
          <div class="form-group SOAP_field">
            <label class="col-sm-2 control-label" for="input-account">{{ entry_account }}</label>
            <div class="col-sm-10">
              <input type="text" name="shipping_hitshippo_fedex_account" value="{{ shipping_hitshippo_fedex_account }}" placeholder="{{ entry_account }}" id="input-account" class="form-control" />
              {% if error_account %}
              <div class="text-danger">{{ error_account }}</div>
              {% endif %}
            </div>
          </div>

          <div class="form-group SOAP_field">
            <label class="col-sm-2 control-label" for="input-meter">{{ entry_meter }}</label>
            <div class="col-sm-10">
              <input type="text" name="shipping_hitshippo_fedex_meter" value="{{ shipping_hitshippo_fedex_meter }}" placeholder="{{ entry_meter }}" id="input-meter" class="form-control" />
              {% if error_meter %}
              <div class="text-danger">{{ error_meter }}</div>
              {% endif %}
            </div>
          </div>

        <div class="form-group">
            <label class="col-sm-2 control-label">{{ entry_front_end_logs }}<span data-toggle="tooltip" title="{{ entry_front_end_logs }}"></span></label>
            <div class="col-sm-10">
              <label class="radio-inline">
                {% if shipping_hitshippo_fedex_front_end_logs %}
                <input type="radio" name="shipping_hitshippo_fedex_front_end_logs" value="1" checked="checked" />
                {{ text_yes }}
                {% else %}
                <input type="radio" name="shipping_hitshippo_fedex_front_end_logs" value="1" />
                {{ text_yes }}
                {% endif %}
              </label>
              <label class="radio-inline">
                {% if not shipping_hitshippo_fedex_front_end_logs  %}
                <input type="radio" name="shipping_hitshippo_fedex_front_end_logs" value="0" checked="checked" />
                {{ text_no }}
                {% else %}
                <input type="radio" name="shipping_hitshippo_fedex_front_end_logs" value="0" />
                {{ text_no }}
                {% endif %}
              </label>
            </div>
          </div>

      <div class="form-group">
            <label class="col-sm-2 control-label" for="input-status">{{ entry_status }}</label>
            <div class="col-sm-10">
              <select name="shipping_hitshippo_fedex_status" id="input-status" class="form-control">
                {% if shipping_hitshippo_fedex_status %}
                <option value="1" selected="selected">{{ text_enabled }}</option>
                <option value="0">{{ text_disabled }}</option>
                {% else %}
                <option value="1">{{ text_enabled }}</option>
                <option value="0" selected="selected">{{ text_disabled }}</option>
                {% endif %}
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-sort-order">{{ entry_sort_order }}</label>
            <div class="col-sm-10">
              <input type="text" name="shipping_hitshippo_fedex_sort_order" value="{{ shipping_hitshippo_fedex_sort_order }}" placeholder="{{ entry_sort_order }}" id="input-sort-order" class="form-control" />
            </div>
          </div>
    </div>
  </div>
  <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pencil"></i> {{ text_shiiping_address }}</h3>
      </div>
      <div class="panel-body">
      <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-shipper_name">{{ entry_shipper_name }}</label>
            <div class="col-sm-10">
              <input type="text" name="shipping_hitshippo_fedex_shipper_name" value="{{ shipping_hitshippo_fedex_shipper_name }}" placeholder="{{ entry_shipper_name }}" id="input-shipper_name" class="form-control" />
              {% if error_shipper_name %}
              <div class="text-danger">{{ error_shipper_name }}</div>
              {% endif %}
            </div>
          </div>
        <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-company_name">{{ entry_company_name }}</label>
            <div class="col-sm-10">
              <input type="text" name="shipping_hitshippo_fedex_company_name" value="{{ shipping_hitshippo_fedex_company_name }}" placeholder="{{ entry_company_name }}" id="input-company_name" class="form-control" />
              {% if error_company_name %}
              <div class="text-danger">{{ error_company_name }}</div>
              {% endif %}
            </div>
          </div>
      <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-phone_num">{{ entry_phone_num }}</label>
            <div class="col-sm-10">
              <input type="text" name="shipping_hitshippo_fedex_phone_num" value="{{ shipping_hitshippo_fedex_phone_num }}" placeholder="{{ entry_phone_num }}" id="input-phone_num" class="form-control" />
              {% if error_phone_num %}
              <div class="text-danger">{{ error_phone_num }}</div>
              {% endif %}
            </div>
          </div>
      <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-email_addr">{{ entry_email_addr }}</label>
            <div class="col-sm-10">
              <input type="text" name="shipping_hitshippo_fedex_email_addr" value="{{ shipping_hitshippo_fedex_email_addr }}" placeholder="{{ entry_email_addr }}" id="input-email_addr" class="form-control" />
              {% if error_email_addr %}
              <div class="text-danger">{{ error_email_addr }}</div>
              {% endif %}
            </div>
          </div>
      <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-address1">{{ entry_address1 }}</label>
            <div class="col-sm-10">
              <input type="text" name="shipping_hitshippo_fedex_address1" value="{{ shipping_hitshippo_fedex_address1 }}" placeholder="{{ entry_address1 }}" id="input-address1" class="form-control" />
              {% if error_address1 %}
              <div class="text-danger">{{ error_address1 }}</div>
              {% endif %}
            </div>
          </div>
      <div class="form-group">
            <label class="col-sm-2 control-label" for="input-address2">{{ entry_address2 }}</label>
            <div class="col-sm-10">
              <input type="text" name="shipping_hitshippo_fedex_address2" value="{{ shipping_hitshippo_fedex_address2 }}" placeholder="{{ entry_address2 }}" id="input-address2" class="form-control" />
              {% if error_address2 %}
              <div class="text-danger">{{ error_address2 }}</div>
              {% endif %}
            </div>
          </div>
      <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-city">{{ entry_city }}</label>
            <div class="col-sm-10">
              <input type="text" name="shipping_hitshippo_fedex_city" value="{{ shipping_hitshippo_fedex_city }}" placeholder="{{ entry_city }}" id="input-city" class="form-control" />
              {% if error_city %}
              <div class="text-danger">{{ error_city }}</div>
              {% endif %}
            </div>
          </div>
      <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-state">{{ entry_state }}</label>
            <div class="col-sm-10">
              <input type="text" name="shipping_hitshippo_fedex_state" value="{{ shipping_hitshippo_fedex_state }}" placeholder="{{ entry_state }}" id="input-state" class="form-control" />
              {% if error_state %}
              <div class="text-danger">{{ error_state }}</div>
              {% endif %}
            </div>
          </div>
        <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-country_code">{{ entry_country_code }}</label>
            <div class="col-sm-10">
        <select name="shipping_hitshippo_fedex_country_code" class="form-control control-label">
        {% for key,value in countrylist %}
        <option value="{{key}}" {% if key == shipping_hitshippo_fedex_country_code %}selected="true"{% endif %}>{{value}}</option>
        {% endfor %}
        </select>
        
              {% if error_country_code %}
              <div class="text-danger">{{ error_country_code }}</div>
              {% endif %}
            </div>
          </div>
        
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-postcode">{{ entry_postcode }}</label>
            <div class="col-sm-10">
              <input type="text" name="shipping_hitshippo_fedex_postcode" value="{{ shipping_hitshippo_fedex_postcode }}" placeholder="{{ entry_postcode }}" id="input-postcode" class="form-control" />
              {% if error_postcode %}
              <div class="text-danger">{{ error_postcode }}</div>
              {% endif %}
            </div>
          </div>
          
    </div>
  </div>
  <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pencil"></i> {{ text_rates }}</h3>
      </div>
      <div class="panel-body">
          <div class="form-group">
            <label class="col-sm-2 control-label">{{ entry_realtime_rates }}</label>
            <div class="col-sm-10">
              <label class="radio-inline">
                {% if shipping_hitshippo_fedex_realtime_rates %}
                <input type="radio" name="shipping_hitshippo_fedex_realtime_rates" value="1" checked="checked" />
                {{ text_yes }}
                {% else %}
                <input type="radio" name="shipping_hitshippo_fedex_realtime_rates" value="1" />
                {{ text_yes }}
                {% endif %}
              </label>
              <label class="radio-inline">
                {% if not shipping_hitshippo_fedex_realtime_rates %}
                <input type="radio" name="shipping_hitshippo_fedex_realtime_rates" value="0" checked="checked" />
                {{ text_no }}
                {% else %}
                <input type="radio" name="shipping_hitshippo_fedex_realtime_rates" value="0" />
                {{ text_no }}
                {% endif %}
              </label>
            </div>
          </div>

          <div class="form-group">
            <label class="col-sm-2 control-label">{{ entry_residential }}</label>
            <div class="col-sm-10">
              <label class="radio-inline">
                {% if shipping_hitshippo_fedex_residential %}
                <input type="radio" name="shipping_hitshippo_fedex_residential" value="1" checked="checked" />
                {{ text_yes }}
                {% else %}
                <input type="radio" name="shipping_hitshippo_fedex_residential" value="1" />
                {{ text_yes }}
                {% endif %}
              </label>
              <label class="radio-inline">
                {% if not shipping_hitshippo_fedex_residential %}
                <input type="radio" name="shipping_hitshippo_fedex_residential" value="0" checked="checked" />
                {{ text_no }}
                {% else %}
                <input type="radio" name="shipping_hitshippo_fedex_residential" value="0" />
                {{ text_no }}
                {% endif %}
              </label>
            </div>
          </div>

{#       <div class="form-group">
            <label class="col-sm-2 control-label">{{ entry_insurance }}</label>
            <div class="col-sm-10">
              <label class="radio-inline">
                {% if shipping_hitshippo_fedex_insurance %}
                <input type="radio" name="shipping_hitshippo_fedex_insurance" value="1" checked="checked" />
                {{ text_yes }}
                {% else %}
                <input type="radio" name="shipping_hitshippo_fedex_insurance" value="1" />
                {{ text_yes }}
                {% endif %}
              </label>
              <label class="radio-inline">
                {% if not shipping_hitshippo_fedex_insurance %}
                <input type="radio" name="shipping_hitshippo_fedex_insurance" value="0" checked="checked" />
                {{ text_no }}
                {% else %}
                <input type="radio" name="shipping_hitshippo_fedex_insurance" value="0" />
                {{ text_no }}
                {% endif %}
              </label>
            </div>
          </div> #}
     
       <div class="form-group">
            <label class="col-sm-2 control-label" for="input-rate-type">{{ entry_rate_type }}<span data-toggle="tooltip" title="Set rate request type to 'NONE/ACCOUNT'."></span></label>
            <div class="col-sm-10">
              <select name="shipping_hitshippo_fedex_rate_type" id="input-rate-type" class="form-control">
                {% if shipping_hitshippo_fedex_rate_type == 'LIST' %}
                <option value="LIST" selected="selected">{{ text_list_rate }}</option>
                {% else %}
                <option value="LIST">{{ text_list_rate }}</option>
                {% endif %}
                {% if shipping_hitshippo_fedex_rate_type == 'NONE' %}
                <option value="NONE" selected="selected">{{ text_account_rate }}</option>
                {% else %}
                <option value="NONE">{{ text_account_rate }}</option>
                {% endif %}
              </select>
            </div>
          </div>
      
      <div class="form-group">
            <label class="col-sm-2 control-label">{{ entry_service }}</label>
            <div class="col-sm-10">
              <div class="well well-sm" style="height: 150px; overflow: auto;">
                {% for service in services %}
                <div class="checkbox">
                  <label>
                    {% if service.value in shipping_hitshippo_fedex_service %}
                    <input type="checkbox" name="shipping_hitshippo_fedex_service[]" value="{{ service.value }}" checked="checked" />
                    {{ service.text }}
                    {% else %}
                    <input type="checkbox" name="shipping_hitshippo_fedex_service[]" value="{{ service.value }}" />
                    {{ service.text }}
                    {% endif %}
                  </label>
                </div>
                {% endfor %}
              </div>
              <button type="button" onclick="$(this).parent().find(':checkbox').prop('checked', true);" class="btn btn-link">{{ text_select_all }}</button> / <button type="button" onclick="$(this).parent().find(':checkbox').prop('checked', false);" class="btn btn-link">{{ text_unselect_all }}</button></div>
          </div>
      </div>
    </div>
  <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pencil"></i> {{ text_packing }}</h3>
      </div>
      <div class="panel-body">
     <div class="form-group">
            <label class="col-sm-2 control-label">{{ _entry_weight }}</label>
            <div class="col-sm-10">
              <label class="radio-inline">
                {% if shipping_hitshippo_fedex_weight %}
                <input type="radio" name="shipping_hitshippo_fedex_weight" value="1" checked="checked" />
                {{ _entry_lbin }}
                {% else %}
                <input type="radio" name="shipping_hitshippo_fedex_weight" value="1" />
                {{ _entry_lbin }}
                {% endif %}
              </label>
              <label class="radio-inline">
                {% if not shipping_hitshippo_fedex_weight %}
                <input type="radio" name="shipping_hitshippo_fedex_weight" value="0" checked="checked" />
                {{ _entry_kgcm }}
                {% else %}
                <input type="radio" name="shipping_hitshippo_fedex_weight" value="0" />
                {{ _entry_kgcm }}
                {% endif %}
              </label>
            </div>
          </div>
       <div class="form-group">
            <label class="col-sm-2 control-label" for="input-packaging-type">{{ _entry_packing_type }}</label>
            <div class="col-sm-10">
              <select name="shipping_hitshippo_fedex_packing_type" id="input-packing-type" class="form-control">
                {% if shipping_hitshippo_fedex_packing_type == 'per_item' %}
                <option value="per_item" selected="selected">{{ text_per_item }}</option>
                {% else %}
                <option value="per_item">{{ text_per_item }}</option>
                {% endif %}
                {% if shipping_hitshippo_fedex_packing_type == 'weight_based' %}
                <option value="weight_based" selected="selected">{{ text_fedex_weight_based }}</option>
                {% else %}
                <option value="weight_based">{{ text_fedex_weight_based }}</option>
                {% endif %}
              </select>
            </div>
          </div>
         </div>
    </div>
{#   <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pencil"></i> {{ text_peritem_head }}</h3>
      </div>
      <div class="panel-body">
    <div class="form-group">
            <label class="col-sm-2 control-label" for="input-packaging-type">{{ _entry_packing_type }}</label>
            <div class="col-sm-10">
              <select name="shipping_hitshippo_fedex_per_item" id="input-packing-type" class="form-control">
                {% if shipping_hitshippo_fedex_per_item == 'BOX' %}
                <option value="BOX" selected="selected">{{ text_box }}</option>
                {% else %}
                <option value="BOX">{{ text_box }}</option>
                {% endif %}
                {% if shipping_hitshippo_fedex_per_item == 'FLY' %}
                <option value="FLY" selected="selected">{{ text_fly }}</option>
                {% else %}
                <option value="FLY">{{ text_fly }}</option>
                {% endif %}
                {% if shipping_hitshippo_fedex_per_item == 'YP' %}
                <option value="YP" selected="selected">{{ text_fedex_yp }}</option>
                {% else %}
                <option value="YP">{{ text_fedex_yp }}</option>
                {% endif %}
              </select>
            </div>
          </div>
      </div>
    </div> #}
  
  <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pencil"></i> {{ text_weight_head }}</h3>
      </div>
      <div class="panel-body">
      
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-postcode">{{ text_head12 }}</label>
            <div class="col-sm-10">
              <input type="text" name="shipping_hitshippo_fedex_wight_b" value="{{ shipping_hitshippo_fedex_wight_b }}" placeholder="{{ text_head12 }}" id="input-wight_b" class="form-control" />
              {% if error_wight_b %}
              <div class="text-danger">{{ error_wight_b }}</div>
              {% endif %}
            </div>
          </div>
 {#      <div class="form-group">
            <label class="col-sm-2 control-label" for="input-packaging-type">{{ _entry_packing_type }}</label>
            <div class="col-sm-10">
              <select name="shipping_hitshippo_fedex_weight_c" id="input-packing-type" class="form-control">
                {% if shipping_hitshippo_fedex_weight_c == 'pack_descending' %}
                <option value="pack_descending" selected="selected">{{ text_head13 }}</option>
                {% else %}
                <option value="pack_descending">{{ text_head13 }}</option>
                {% endif %}
                {% if shipping_hitshippo_fedex_weight_c == 'pack_ascending' %}
                <option value="pack_ascending" selected="selected">{{ text_head14 }}</option>
                {% else %}
                <option value="pack_ascending">{{ text_head14 }}</option>
                {% endif %}
                {% if shipping_hitshippo_fedex_weight_c == 'pack_simple' %}
                <option value="pack_simple" selected="selected">{{ text_head15 }}</option>
                {% else %}
                <option value="pack_simple">{{ text_head15 }}</option>
                {% endif %}
              </select>
            </div>
          </div> #}
     </div>
    </div>
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pencil"></i> {{ text_label }} </h3>
      </div>
      <div class="panel-body">
         <div class="form-group">
            <label class="col-sm-2 control-label" for="input-fedex_int_key">HIT-Shipo Integration Key<span data-toggle="tooltip" title="Enter/Get integration key"><b style="color:red;">*</b></span></label>
            <div class="col-sm-10">
             <input type="text" name="shipping_hitshippo_fedex_int_key" value="{{ shipping_hitshippo_fedex_int_key }}" placeholder="" id="input-fedex_int_key" class="form-control" />
             <a href="https://app.hitshipo.com/" target="_blank">Don't have a key? Signup for free</a>
            </div>
          </div>
         <div class="form-group">
            <label class="col-sm-2 control-label" for="input-fedex_send_mail_to">Auto label generation<span data-toggle="tooltip" title="Need automated label generation??"><b style="color:red;">*</b></span></label>
            <div class="col-sm-10">
              <label class="radio-inline">
                {% if shipping_hitshippo_fedex_auto_label %}
                <input type="radio" name="shipping_hitshippo_fedex_auto_label" value="1" checked="checked" />
                {{ text_enable }}
                {% else %}
                <input type="radio" name="shipping_hitshippo_fedex_auto_label" value="1" />
                {{ text_enable }}
                {% endif %}
              </label>
              <label class="radio-inline">
                {% if not shipping_hitshippo_fedex_auto_label %}
                <input type="radio" name="shipping_hitshippo_fedex_auto_label" value="0" checked="checked" />
                {{ text_disable }}
                {% else %}
                <input type="radio" name="shipping_hitshippo_fedex_auto_label" value="0" />
                {{ text_disable }}
                {% endif %}
              </label>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-fedex_send_mail_to">Change Order Status<span data-toggle="tooltip" title="Enable to change the order status to 'shipped' once label generated."><b style="color:red;">*</b></span></label>
            <div class="col-sm-10">
              <label class="radio-inline">
                {% if shipping_hitshippo_fedex_auto_status %}
                <input type="radio" name="shipping_hitshippo_fedex_auto_status" value="1" checked="checked" />
                {{ text_enable }}
                {% else %}
                <input type="radio" name="shipping_hitshippo_fedex_auto_status" value="1" />
                {{ text_enable }}
                {% endif %}
              </label>
              <label class="radio-inline">
                {% if not shipping_hitshippo_fedex_auto_status %}
                <input type="radio" name="shipping_hitshippo_fedex_auto_status" value="0" checked="checked" />
                {{ text_disable }}
                {% else %}
                <input type="radio" name="shipping_hitshippo_fedex_auto_status" value="0" />
                {{ text_disable }}
                {% endif %}
              </label>
            </div>
          </div>
         <div class="form-group">
            <label class="col-sm-2 control-label" for="input-fedex_send_mail_to">Email to receive generated labels<span data-toggle="tooltip" title="To whom you want to sent the shipping label once created"><b style="color:red;">*</b></span></label>
            <div class="col-sm-10">
             <input type="text" name="shipping_hitshippo_fedex_send_mail_to" value="{{ shipping_hitshippo_fedex_send_mail_to }}" placeholder="" id="input-fedex_send_mail_to" class="form-control" />
            </div>
          </div>

      <div class="form-group">
            <label class="col-sm-2 control-label" for="input-print-size">{{ text_head20 }}</label>
            <div class="col-sm-10">
              <select name="shipping_hitshippo_fedex_print_size" id="input-print-size" class="form-control">
                {% if shipping_hitshippo_fedex_print_size == 'PAPER_7X4.75' %}
                <option value="PAPER_7X4.75" selected="selected">{{ text_head21 }}</option>
                {% else %}
                <option value="PAPER_7X4.75">{{ text_head21 }}</option>
                {% endif %}
                {% if shipping_hitshippo_fedex_print_size == 'PAPER_4X6' %}
                <option value="PAPER_4X6" selected="selected">{{ text_head22 }}</option>
                {% else %}
                <option value="PAPER_4X6">{{ text_head22 }}</option>
                {% endif %}
                {% if shipping_hitshippo_fedex_print_size == 'PAPER_4X8' %}
                <option value="PAPER_4X8" selected="selected">{{ text_head23 }}</option>
                {% else %}
                <option value="PAPER_4X8">{{ text_head23 }}</option>
                {% endif %}
                {% if shipping_hitshippo_fedex_print_size == 'PAPER_4X9' %}
                <option value="PAPER_4X9" selected="selected">{{ text_head24 }}</option>
                {% else %}
                <option value="PAPER_4X9">{{ text_head24 }}</option>
                {% endif %}
                {% if shipping_hitshippo_fedex_print_size == 'PAPER_7X4.75' %}
                <option value="PAPER_7X4.75" selected="selected">{{ text_head25 }}</option>
                {% else %}
                <option value="PAPER_7X4.75">{{ text_head25 }}</option>
                {% endif %}
                {% if shipping_hitshippo_fedex_print_size == 'PAPER_8.5X11_BOTTOM_HALF_LABEL' %}
                <option value="PAPER_8.5X11_BOTTOM_HALF_LABEL" selected="selected">{{ text_head26 }}</option>
                {% else %}
                <option value="PAPER_8.5X11_BOTTOM_HALF_LABEL">{{ text_head26 }}</option>
                {% endif %}
                {% if shipping_hitshippo_fedex_print_size == 'PAPER_8.5X11_TOP_HALF_LABEL' %}
                <option value="PAPER_8.5X11_TOP_HALF_LABEL" selected="selected">{{ text_head27 }}</option>
                {% else %}
                <option value="PAPER_8.5X11_TOP_HALF_LABEL">{{ text_head27 }}</option>
                {% endif %}
                {% if shipping_hitshippo_fedex_print_size == 'PAPER_LETTER' %}
                <option value="PAPER_LETTER" selected="selected">{{ text_head28 }}</option>
                {% else %}
                <option value="PAPER_LETTER">{{ text_head28 }}</option>
                {% endif %}
                {% if shipping_hitshippo_fedex_print_size == 'STOCK_4X6' %}
                <option value="STOCK_4X6" selected="selected">{{ text_head29 }}</option>
                {% else %}
                <option value="STOCK_4X6">{{ text_head29 }}</option>
                {% endif %}
                {% if shipping_hitshippo_fedex_print_size == 'STOCK_4X6.75_LEADING_DOC_TAB' %}
                <option value="STOCK_4X6.75_LEADING_DOC_TAB" selected="selected">{{ text_head30 }}</option>
                {% else %}
                <option value="STOCK_4X6.75_LEADING_DOC_TAB">{{ text_head30 }}</option>
                {% endif %}
                {% if shipping_hitshippo_fedex_print_size == 'STOCK_4X6.75_TRAILING_DOC_TAB' %}
                <option value="6X4_thermal" selected="selected">{{ text_head31 }}</option>
                {% else %}
                <option value="6X4_thermal">{{ text_head31 }}</option>
                {% endif %}
                {% if shipping_hitshippo_fedex_print_size == 'STOCK_4X8' %}
                <option value="STOCK_4X8" selected="selected">{{ text_head32 }}</option>
                {% else %}
                <option value="STOCK_4X8">{{ text_head32 }}</option>
                {% endif %}
                {% if shipping_hitshippo_fedex_print_size == 'STOCK_4X9_LEADING_DOC_TAB' %}
                <option value="STOCK_4X9_LEADING_DOC_TAB" selected="selected">{{ text_head44 }}</option>
                {% else %}
                <option value="STOCK_4X9_LEADING_DOC_TAB">{{ text_head44 }}</option>
                {% endif %}
                {% if shipping_hitshippo_fedex_print_size == 'STOCK_4X9_TRAILING_DOC_TAB' %}
                <option value="STOCK_4X9_TRAILING_DOC_TAB" selected="selected">{{ text_head45 }}</option>
                {% else %}
                <option value="STOCK_4X9_TRAILING_DOC_TAB">{{ text_head45 }}</option>
                {% endif %}
              </select>
            </div>
          </div>
      
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-print-type">{{ text_head33 }}</label>
            <div class="col-sm-10">
              <select name="shipping_hitshippo_fedex_print_type" id="input-print-type" class="form-control">
                {% if shipping_hitshippo_fedex_print_type == 'COMMON2D' %}
                <option value="COMMON2D" selected="selected">{{ text_head34 }}</option>
                {% else %}
                <option value="COMMON2D">{{ text_head34 }}</option>
                {% endif %}
                {% if shipping_hitshippo_fedex_print_type == 'LABEL_DATA_ONLY' %}
                <option value="LABEL_DATA_ONLY" selected="selected">{{ text_head35 }}</option>
                {% else %}
                <option value="LABEL_DATA_ONLY">{{ text_head35 }}</option>
                {% endif %}
                {% if shipping_hitshippo_fedex_print_type == 'VICS_BILL_OF_LADING' %}
                <option value="VICS_BILL_OF_LADING" selected="selected">{{ text_head36 }}</option>
                {% else %}
                <option value="VICS_BILL_OF_LADING">{{ text_head36 }}</option>
                {% endif %}
                {% if shipping_hitshippo_fedex_print_type == 'FEDEX_FREIGHT_STRAIGHT_BILL_OF_LADING' %}
                <option value="FEDEX_FREIGHT_STRAIGHT_BILL_OF_LADING" selected="selected">{{ text_head46 }}</option>
                {% else %}
                <option value="FEDEX_FREIGHT_STRAIGHT_BILL_OF_LADING">{{ text_head46 }}</option>
                {% endif %}
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-document-types">{{ text_head48 }}</label>
            <div class="col-sm-10">
              <select name="shipping_hitshippo_fedex_shipping_document_types" id="input-document-types" class="form-control">
                {% if shipping_hitshippo_fedex_shipping_document_types == 'COMMERCIAL_INVOICE' %}
                <option value="COMMERCIAL_INVOICE" selected="selected">{{ text_head49 }}</option>
                {% else %}
                <option value="COMMERCIAL_INVOICE">{{ text_head49 }}</option>
                {% endif %}
                {% if shipping_hitshippo_fedex_shipping_document_types == 'PRO_FORMA_INVOICE' %}
                <option value="PRO_FORMA_INVOICE" selected="selected">{{ text_head50 }}</option>
                {% else %}
                <option value="PRO_FORMA_INVOICE">{{ text_head50 }}</option>
                {% endif %}
              </select>
            </div>
          </div>
        <div class="form-group">
            <label class="col-sm-2 control-label" for="input-dropoff-type">{{ text_head41 }}</label>
            <div class="col-sm-10">
              <select name="shipping_hitshippo_fedex_dropoff_type" id="input-dropoff-type" class="form-control">
                {% if shipping_hitshippo_fedex_dropoff_type == 'REGULAR_PICKUP' %}
                <option value="REGULAR_PICKUP" selected="selected">{{ text_head37 }}</option>
                {% else %}
                <option value="REGULAR_PICKUP">{{ text_head37 }}</option>
                {% endif %}
                {% if shipping_hitshippo_fedex_dropoff_type == 'REQUEST_COURIER' %}
                <option value="REQUEST_COURIER" selected="selected">{{ text_head38 }}</option>
                {% else %}
                <option value="REQUEST_COURIER">{{ text_head38 }}</option>
                {% endif %}
                {% if shipping_hitshippo_fedex_dropoff_type == 'DROP_BOX' %}
                <option value="DROP_BOX" selected="selected">{{ text_head39 }}</option>
                {% else %}
                <option value="DROP_BOX">{{ text_head39 }}</option>
                {% endif %}
                {% if shipping_hitshippo_fedex_dropoff_type == 'BUSINESS_SERVICE_CENTER' %}
                <option value="BUSINESS_SERVICE_CENTER" selected="selected">{{ text_head40 }}</option>
                {% else %}
                <option value="BUSINESS_SERVICE_CENTER">{{ text_head40 }}</option>
                {% endif %}
                {% if shipping_hitshippo_fedex_dropoff_type == 'STATION' %}
                <option value="STATION" selected="selected">{{ text_head47 }}</option>
                {% else %}
                <option value="STATION">{{ text_head47 }}</option>
                {% endif %}
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-fedpack-type">{{ text_fedpack }}</label>
            <div class="col-sm-10">
              <select name="shipping_hitshippo_fedex_fedpack_type" id="input-fedpack-type" class="form-control">
                {% if shipping_hitshippo_fedex_fedpack_type == 'YOUR_PACKAGING' %}
                <option value="YOUR_PACKAGING" selected="selected">{{ text_fedpack_1 }}</option>
                {% else %}
                <option value="YOUR_PACKAGING">{{ text_fedpack_1 }}</option>
                {% endif %}
                {% if shipping_hitshippo_fedex_fedpack_type == 'FEDEX_BOX' %}
                <option value="FEDEX_BOX" selected="selected">{{ text_fedpack_2 }}</option>
                {% else %}
                <option value="FEDEX_BOX">{{ text_fedpack_2 }}</option>
                {% endif %}
                {% if shipping_hitshippo_fedex_fedpack_type == 'FEDEX_PAK' %}
                <option value="FEDEX_PAK" selected="selected">{{ text_fedpack_3 }}</option>
                {% else %}
                <option value="FEDEX_PAK">{{ text_fedpack_3 }}</option>
                {% endif %}
                {% if shipping_hitshippo_fedex_fedpack_type == 'FEDEX_TUBE' %}
                <option value="FEDEX_TUBE" selected="selected">{{ text_fedpack_4 }}</option>
                {% else %}
                <option value="FEDEX_TUBE">{{ text_fedpack_4 }}</option>
                {% endif %}
                {% if shipping_hitshippo_fedex_fedpack_type == 'FEDEX_10KG_BOX' %}
                <option value="FEDEX_10KG_BOX" selected="selected">{{ text_fedpack_5 }}</option>
                {% else %}
                <option value="FEDEX_10KG_BOX">{{ text_fedpack_5 }}</option>
                {% endif %}
                {% if shipping_hitshippo_fedex_fedpack_type == 'FEDEX_25KG_BOX' %}
                <option value="FEDEX_25KG_BOX" selected="selected">{{ text_fedpack_6 }}</option>
                {% else %}
                <option value="FEDEX_25KG_BOX">{{ text_fedpack_6 }}</option>
                {% endif %}
                {% if shipping_hitshippo_fedex_fedpack_type == 'FEDEX_ENVELOPE' %}
                <option value="FEDEX_ENVELOPE" selected="selected">{{ text_fedpack_7 }}</option>
                {% else %}
                <option value="FEDEX_ENVELOPE">{{ text_fedpack_7 }}</option>
                {% endif %}
                {% if shipping_hitshippo_fedex_fedpack_type == 'FEDEX_EXTRA_LARGE_BOX' %}
                <option value="FEDEX_EXTRA_LARGE_BOX" selected="selected">{{ text_fedpack_8 }}</option>
                {% else %}
                <option value="FEDEX_EXTRA_LARGE_BOX">{{ text_fedpack_8 }}</option>
                {% endif %}
                {% if shipping_hitshippo_fedex_fedpack_type == 'FEDEX_LARGE_BOX' %}
                <option value="FEDEX_LARGE_BOX" selected="selected">{{ text_fedpack_9 }}</option>
                {% else %}
                <option value="FEDEX_LARGE_BOX">{{ text_fedpack_9 }}</option>
                {% endif %}
                {% if shipping_hitshippo_fedex_fedpack_type == 'FEDEX_MEDIUM_BOX' %}
                <option value="FEDEX_MEDIUM_BOX" selected="selected">{{ text_fedpack_10 }}</option>
                {% else %}
                <option value="FEDEX_MEDIUM_BOX">{{ text_fedpack_10 }}</option>
                {% endif %}
                {% if shipping_hitshippo_fedex_fedpack_type == 'FEDEX_SMALL_BOX' %}
                <option value="FEDEX_SMALL_BOX" selected="selected">{{ text_fedpack_11 }}</option>
                {% else %}
                <option value="FEDEX_SMALL_BOX">{{ text_fedpack_11 }}</option>
                {% endif %}
              </select>
            </div>
          </div>
      
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-shipment_content">{{ text_head42 }}</label>
            <div class="col-sm-10">
              <input type="text" name="shipping_hitshippo_fedex_shipment_content" value="{{ shipping_hitshippo_fedex_shipment_content }}" placeholder="{{ text_head42 }}" id="input-shipment_content" class="form-control" />
            </div>
          </div>

          <div class="form-group">
            <label class="col-sm-2 control-label" for="shipping_hitshippo_fedex_language">Product Name Language</label>
            <div class="col-sm-10">
              <select name="shipping_hitshippo_fedex_language" id="shipping_hitshippo_fedex_language" class="form-control">
              {% if shipping_hitshippo_fedex_language == 'default' %}
                <option value="default" selected="selected">Default</option>
                {% else %}
                <option value="default">Default</option>
                {% endif %}
                 {% for key,value in languages %}
                    <option value="{{value.language_id}}" {% if value.language_id == shipping_hitshippo_fedex_language %}selected="true"{% endif %}>{{value.name}}</option>
              {% endfor %}
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label">ELECTRONIC TRADE DOCUMENTS</label>
            <div class="col-sm-10" style="margin-top: 18px;">
              {% if shipping_hitshippo_fedex_ETD_check %}
                <input type="checkbox" name="shipping_hitshippo_fedex_ETD_check" checked="checked">
              {% else %}
                <input type="checkbox" name="shipping_hitshippo_fedex_ETD_check">
              {% endif %}
              <span style="margin-left:5px;">Enable / Disable</span>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label"><span data-toggle="tooltip" title="It adds history when creating shipment Enter comment text to show on history">Add History</span></label>
            <div class="col-sm-10">
              {% if shipping_hitshippo_fedex_addcomment_check %}
                <input type="checkbox" name="shipping_hitshippo_fedex_addcomment_check" checked="checked">
              {% else %}
                <input type="checkbox" name="shipping_hitshippo_fedex_addcomment_check">
              {% endif %}
              <span style="margin-left:5px;">Enable / Disable</span>
            </div>
          </div>
          
          <div class="form-group">
            <label class="col-sm-2 control-label">Add comment</label>
            <div class="col-sm-10"> 
              <textarea name="shipping_hitshippo_fedex_addcomment_box" value="" placeholder="{{ text_head46}}" class="form-control" >{{ shipping_hitshippo_fedex_addcomment_box }}</textarea>
            </div>
          </div>

{#       <div class="form-group">
        <label class="col-sm-2 control-label" for="input-packaging-type">Contact person Name (Pickup)</label>
        <div class="col-sm-10">
         <input type="text" name="shipping_hitshippo_fedex_picper" value="{{ shipping_hitshippo_fedex_picper }}" placeholder="Person Name" id="input-account" class="form-control" />
        </div>
      </div>
      <div class="form-group">
        <label class="col-sm-2 control-label" for="input-packaging-type">Contact Phone Number (Pickup)</label>
        <div class="col-sm-10">
         <input type="text" name="shipping_hitshippo_fedex_piccon" value="{{ shipping_hitshippo_fedex_piccon }}" placeholder="Person Mobile" id="input-account" class="form-control" />
        </div>
      </div>
      <div class="form-group">
            <label class="col-sm-2 control-label" for="input-packaging-type">Pickup Open Time</label>
            <div class="col-sm-10">
              <select name="shipping_hitshippo_fedex_pickup_time" id="input-packing-type" class="form-control">
            <option value="01:00" {% if shipping_hitshippo_fedex_pickup_time == '01:00' %}selected="selected"{% endif %} >01:00</option>
            <option value="02:00" {% if shipping_hitshippo_fedex_pickup_time == '02:00' %}selected="selected"{% endif %}>02:00</option>
            <option value="03:00" {% if shipping_hitshippo_fedex_pickup_time == '03:00' %}selected="selected"{% endif %}>03:00</option>
            <option value="04:00" {% if shipping_hitshippo_fedex_pickup_time == '04:00' %}selected="selected"{% endif %}>04:00</option>
            <option value="05:00" {% if shipping_hitshippo_fedex_pickup_time == '05:00' %}selected="selected"{% endif %}>05:00</option>
            <option value="06:00" {% if shipping_hitshippo_fedex_pickup_time == '06:00' %}selected="selected"{% endif %}>06:00</option>
            <option value="07:00" {% if shipping_hitshippo_fedex_pickup_time == '07:00' %}selected="selected"{% endif %}>07:00</option>
            <option value="08:00" {% if shipping_hitshippo_fedex_pickup_time == '08:00' %}selected="selected"{% endif %}>08:00</option>
            <option value="09:00" {% if shipping_hitshippo_fedex_pickup_time == '09:00' %}selected="selected"{% endif %}>09:00</option>
            <option value="10:00" {% if shipping_hitshippo_fedex_pickup_time == '10:00' %}selected="selected"{% endif %}>10:00</option>
            <option value="11:00" {% if shipping_hitshippo_fedex_pickup_time == '11:00' %}selected="selected"{% endif %}>11:00</option>
            <option value="12:00" {% if shipping_hitshippo_fedex_pickup_time == '12:00' %}selected="selected"{% endif %}>12:00</option>
            <option value="13:00" {% if shipping_hitshippo_fedex_pickup_time == '13:00' %}selected="selected"{% endif %}>13:00</option>
            <option value="14:00" {% if shipping_hitshippo_fedex_pickup_time == '14:00' %}selected="selected"{% endif %}>14:00</option>
            <option value="15:00" {% if shipping_hitshippo_fedex_pickup_time == '15:00' %}selected="selected"{% endif %}>15:00</option>
            <option value="16:00" {% if shipping_hitshippo_fedex_pickup_time == '16:00' %}selected="selected"{% endif %}>16:00</option>
            <option value="17:00" {% if shipping_hitshippo_fedex_pickup_time == '17:00' %}selected="selected"{% endif %}>17:00</option>
            <option value="18:00" {% if shipping_hitshippo_fedex_pickup_time == '18:00' %}selected="selected"{% endif %}>18:00</option>
            <option value="19:00" {% if shipping_hitshippo_fedex_pickup_time == '19:00' %}selected="selected"{% endif %}>19:00</option>
            <option value="20:00" {% if shipping_hitshippo_fedex_pickup_time == '20:00' %}selected="selected"{% endif %}>20:00</option>
            <option value="21:00" {% if shipping_hitshippo_fedex_pickup_time == '21:00' %}selected="selected"{% endif %}>21:00</option>
            <option value="22:00" {% if shipping_hitshippo_fedex_pickup_time == '22:00' %}selected="selected"{% endif %}>22:00</option>
            <option value="23:00" {% if shipping_hitshippo_fedex_pickup_time == '23:00' %}selected="selected"{% endif %}>23:00</option>
            <option value="00:00" {% if shipping_hitshippo_fedex_pickup_time == '00:00' %}selected="selected"{% endif %}>00:00</option>
          </select>
            </div>
          </div>
      <div class="form-group">
            <label class="col-sm-2 control-label" for="input-packaging-type">Pickup Close Time</label>
            <div class="col-sm-10">
              <select name="shipping_hitshippo_fedex_close_time" id="input-packing-type" class="form-control">
            <option value="01:00" {% if shipping_hitshippo_fedex_close_time == '01:00' %}selected="selected"{% endif %} >01:00</option>
            <option value="02:00" {% if shipping_hitshippo_fedex_close_time == '02:00' %}selected="selected"{% endif %}>02:00</option>
            <option value="03:00" {% if shipping_hitshippo_fedex_close_time == '03:00' %}selected="selected"{% endif %}>03:00</option>
            <option value="04:00" {% if shipping_hitshippo_fedex_close_time == '04:00' %}selected="selected"{% endif %}>04:00</option>
            <option value="05:00" {% if shipping_hitshippo_fedex_close_time == '05:00' %}selected="selected"{% endif %}>05:00</option>
            <option value="06:00" {% if shipping_hitshippo_fedex_close_time == '06:00' %}selected="selected"{% endif %}>06:00</option>
            <option value="07:00" {% if shipping_hitshippo_fedex_close_time == '07:00' %}selected="selected"{% endif %}>07:00</option>
            <option value="08:00" {% if shipping_hitshippo_fedex_close_time == '08:00' %}selected="selected"{% endif %}>08:00</option>
            <option value="09:00" {% if shipping_hitshippo_fedex_close_time == '09:00' %}selected="selected"{% endif %}>09:00</option>
            <option value="10:00" {% if shipping_hitshippo_fedex_close_time == '10:00' %}selected="selected"{% endif %}>10:00</option>
            <option value="11:00" {% if shipping_hitshippo_fedex_close_time == '11:00' %}selected="selected"{% endif %}>11:00</option>
            <option value="12:00" {% if shipping_hitshippo_fedex_close_time == '12:00' %}selected="selected"{% endif %}>12:00</option>
            <option value="13:00" {% if shipping_hitshippo_fedex_close_time == '13:00' %}selected="selected"{% endif %}>13:00</option>
            <option value="14:00" {% if shipping_hitshippo_fedex_close_time == '14:00' %}selected="selected"{% endif %}>14:00</option>
            <option value="15:00" {% if shipping_hitshippo_fedex_close_time == '15:00' %}selected="selected"{% endif %}>15:00</option>
            <option value="16:00" {% if shipping_hitshippo_fedex_close_time == '16:00' %}selected="selected"{% endif %}>16:00</option>
            <option value="17:00" {% if shipping_hitshippo_fedex_close_time == '17:00' %}selected="selected"{% endif %}>17:00</option>
            <option value="18:00" {% if shipping_hitshippo_fedex_close_time == '18:00' %}selected="selected"{% endif %}>18:00</option>
            <option value="19:00" {% if shipping_hitshippo_fedex_close_time == '19:00' %}selected="selected"{% endif %}>19:00</option>
            <option value="20:00" {% if shipping_hitshippo_fedex_close_time == '20:00' %}selected="selected"{% endif %}>20:00</option>
            <option value="21:00" {% if shipping_hitshippo_fedex_close_time == '21:00' %}selected="selected"{% endif %}>21:00</option>
            <option value="22:00" {% if shipping_hitshippo_fedex_close_time == '22:00' %}selected="selected"{% endif %}>22:00</option>
            <option value="23:00" {% if shipping_hitshippo_fedex_close_time == '23:00' %}selected="selected"{% endif %}>23:00</option>
            <option value="00:00" {% if shipping_hitshippo_fedex_close_time == '00:00' %}selected="selected"{% endif %}>00:00</option>
          </select>
            </div>
          </div> #}
    
        </form>

    
    <div class="pull-right">
      <button type="submit" form="form-shipping" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fa fa-save"></i></button>
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a></div>
    </div>
      </div>
    </div>
    </div>
  
  </div>
</div>
{% block javascripts %}
<script type="text/javascript">
  $(document).ready(function() {
    var sel_api_type = $('input[name="shipping_hitshippo_fedex_api_type"]:checked').val();
    if (sel_api_type == "REST") {
        $('.REST_field').show();
        $('.SOAP_field').hide();
      } else {
        $('.REST_field').hide();
        $('.SOAP_field').show();
      }
    $('input[name="shipping_hitshippo_fedex_api_type"]').change(function(){
      if ($(this).val() == "REST") {
        $('.REST_field').show();
        $('.SOAP_field').hide();
      } else {
        $('.REST_field').hide();
        $('.SOAP_field').show();
      }
    });
  });
</script>
{% endblock %}

{{ footer }}
