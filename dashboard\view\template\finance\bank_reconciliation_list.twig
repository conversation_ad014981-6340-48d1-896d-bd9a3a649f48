{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="submit" form="finance\bank_reconciliation-form" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fas fa-save"></i></button>
        <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-light"><i class="fas fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    
    <div class="card">
      <div class="card-header"><i class="fas fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}chart-bar{% elif screen_type == 'settings' %}cog{% else %}pencil-alt{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="card-body">
        <form id="finance\bank_reconciliation-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-add">{{ text_add }}</label>
            <div class="col-sm-10">
              <input type="text" name="add" value="{{ add }}" placeholder="{{ text_add }}" id="input-add" class="form-control" />
              {% if error_add %}
                <div class="invalid-feedback">{{ error_add }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-auto_reconcile_url">{{ text_auto_reconcile_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="auto_reconcile_url" value="{{ auto_reconcile_url }}" placeholder="{{ text_auto_reconcile_url }}" id="input-auto_reconcile_url" class="form-control" />
              {% if error_auto_reconcile_url %}
                <div class="invalid-feedback">{{ error_auto_reconcile_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-bank_accounts">{{ text_bank_accounts }}</label>
            <div class="col-sm-10">
              <input type="text" name="bank_accounts" value="{{ bank_accounts }}" placeholder="{{ text_bank_accounts }}" id="input-bank_accounts" class="form-control" />
              {% if error_bank_accounts %}
                <div class="invalid-feedback">{{ error_bank_accounts }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-delete">{{ text_delete }}</label>
            <div class="col-sm-10">
              <input type="text" name="delete" value="{{ delete }}" placeholder="{{ text_delete }}" id="input-delete" class="form-control" />
              {% if error_delete %}
                <div class="invalid-feedback">{{ error_delete }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_bank_account_id">{{ text_error_bank_account_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_bank_account_id" value="{{ error_bank_account_id }}" placeholder="{{ text_error_bank_account_id }}" id="input-error_bank_account_id" class="form-control" />
              {% if error_error_bank_account_id %}
                <div class="invalid-feedback">{{ error_error_bank_account_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_period_from">{{ text_error_period_from }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_period_from" value="{{ error_period_from }}" placeholder="{{ text_error_period_from }}" id="input-error_period_from" class="form-control" />
              {% if error_error_period_from %}
                <div class="invalid-feedback">{{ error_error_period_from }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_period_to">{{ text_error_period_to }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_period_to" value="{{ error_period_to }}" placeholder="{{ text_error_period_to }}" id="input-error_period_to" class="form-control" />
              {% if error_error_period_to %}
                <div class="invalid-feedback">{{ error_error_period_to }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-finalize_url">{{ text_finalize_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="finalize_url" value="{{ finalize_url }}" placeholder="{{ text_finalize_url }}" id="input-finalize_url" class="form-control" />
              {% if error_finalize_url %}
                <div class="invalid-feedback">{{ error_finalize_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="invalid-feedback">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-import_statement_url">{{ text_import_statement_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="import_statement_url" value="{{ import_statement_url }}" placeholder="{{ text_import_statement_url }}" id="input-import_statement_url" class="form-control" />
              {% if error_import_statement_url %}
                <div class="invalid-feedback">{{ error_import_statement_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-mark_reconciled_url">{{ text_mark_reconciled_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="mark_reconciled_url" value="{{ mark_reconciled_url }}" placeholder="{{ text_mark_reconciled_url }}" id="input-mark_reconciled_url" class="form-control" />
              {% if error_mark_reconciled_url %}
                <div class="invalid-feedback">{{ error_mark_reconciled_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-order">{{ text_order }}</label>
            <div class="col-sm-10">
              <input type="text" name="order" value="{{ order }}" placeholder="{{ text_order }}" id="input-order" class="form-control" />
              {% if error_order %}
                <div class="invalid-feedback">{{ error_order }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-pagination">{{ text_pagination }}</label>
            <div class="col-sm-10">
              <input type="text" name="pagination" value="{{ pagination }}" placeholder="{{ text_pagination }}" id="input-pagination" class="form-control" />
              {% if error_pagination %}
                <div class="invalid-feedback">{{ error_pagination }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-period_from">{{ text_period_from }}</label>
            <div class="col-sm-10">
              <input type="text" name="period_from" value="{{ period_from }}" placeholder="{{ text_period_from }}" id="input-period_from" class="form-control" />
              {% if error_period_from %}
                <div class="invalid-feedback">{{ error_period_from }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-period_to">{{ text_period_to }}</label>
            <div class="col-sm-10">
              <input type="text" name="period_to" value="{{ period_to }}" placeholder="{{ text_period_to }}" id="input-period_to" class="form-control" />
              {% if error_period_to %}
                <div class="invalid-feedback">{{ error_period_to }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-reconciliations">{{ text_reconciliations }}</label>
            <div class="col-sm-10">
              <input type="text" name="reconciliations" value="{{ reconciliations }}" placeholder="{{ text_reconciliations }}" id="input-reconciliations" class="form-control" />
              {% if error_reconciliations %}
                <div class="invalid-feedback">{{ error_reconciliations }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-results">{{ text_results }}</label>
            <div class="col-sm-10">
              <input type="text" name="results" value="{{ results }}" placeholder="{{ text_results }}" id="input-results" class="form-control" />
              {% if error_results %}
                <div class="invalid-feedback">{{ error_results }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-smart_match_url">{{ text_smart_match_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="smart_match_url" value="{{ smart_match_url }}" placeholder="{{ text_smart_match_url }}" id="input-smart_match_url" class="form-control" />
              {% if error_smart_match_url %}
                <div class="invalid-feedback">{{ error_smart_match_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort">{{ text_sort }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort" value="{{ sort }}" placeholder="{{ text_sort }}" id="input-sort" class="form-control" />
              {% if error_sort %}
                <div class="invalid-feedback">{{ error_sort }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_bank_account">{{ text_sort_bank_account }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_bank_account" value="{{ sort_bank_account }}" placeholder="{{ text_sort_bank_account }}" id="input-sort_bank_account" class="form-control" />
              {% if error_sort_bank_account %}
                <div class="invalid-feedback">{{ error_sort_bank_account }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_difference">{{ text_sort_difference }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_difference" value="{{ sort_difference }}" placeholder="{{ text_sort_difference }}" id="input-sort_difference" class="form-control" />
              {% if error_sort_difference %}
                <div class="invalid-feedback">{{ error_sort_difference }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_period">{{ text_sort_period }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_period" value="{{ sort_period }}" placeholder="{{ text_sort_period }}" id="input-sort_period" class="form-control" />
              {% if error_sort_period %}
                <div class="invalid-feedback">{{ error_sort_period }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_status">{{ text_sort_status }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_status" value="{{ sort_status }}" placeholder="{{ text_sort_status }}" id="input-sort_status" class="form-control" />
              {% if error_sort_status %}
                <div class="invalid-feedback">{{ error_sort_status }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-summary_url">{{ text_summary_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="summary_url" value="{{ summary_url }}" placeholder="{{ text_summary_url }}" id="input-summary_url" class="form-control" />
              {% if error_summary_url %}
                <div class="invalid-feedback">{{ error_summary_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_form">{{ text_text_form }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_form" value="{{ text_form }}" placeholder="{{ text_text_form }}" id="input-text_form" class="form-control" />
              {% if error_text_form %}
                <div class="invalid-feedback">{{ error_text_form }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-unreconciled_items_url">{{ text_unreconciled_items_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="unreconciled_items_url" value="{{ unreconciled_items_url }}" placeholder="{{ text_unreconciled_items_url }}" id="input-unreconciled_items_url" class="form-control" />
              {% if error_unreconciled_items_url %}
                <div class="invalid-feedback">{{ error_unreconciled_items_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="invalid-feedback">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}