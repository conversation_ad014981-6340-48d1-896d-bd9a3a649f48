{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <a href="{{ back }}" class="btn btn-default"><i class="fa fa-arrow-left"></i> {{ button_back }}</a>
        <a href="{{ print }}" target="_blank" class="btn btn-info"><i class="fa fa-print"></i> {{ button_print }}</a>
        <a href="{{ export_excel }}" class="btn btn-success"><i class="fa fa-file-excel-o"></i> {{ button_export_excel }}</a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible">
      <i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    
    <!-- معلومات المنتج -->
    <div class="panel panel-primary">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-cube"></i> {{ text_product_info }}</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-md-6">
            <table class="table table-condensed">
              <tr>
                <td><strong>{{ column_product_name }}:</strong></td>
                <td>{{ product_name }}</td>
              </tr>
              <tr>
                <td><strong>{{ column_model }}:</strong></td>
                <td>{{ model }}</td>
              </tr>
              <tr>
                <td><strong>{{ column_sku }}:</strong></td>
                <td>{{ sku }}</td>
              </tr>
            </table>
          </div>
          <div class="col-md-6">
            <table class="table table-condensed">
              <tr>
                <td><strong>{{ column_category }}:</strong></td>
                <td>{{ category_name }}</td>
              </tr>
              <tr>
                <td><strong>{{ column_manufacturer }}:</strong></td>
                <td>{{ manufacturer_name }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_current_balance }}:</strong></td>
                <td><span class="label label-info">{{ current_balance }}</span></td>
              </tr>
            </table>
          </div>
        </div>
      </div>
    </div>
    
    <!-- ملخص الحركات -->
    <div class="row">
      <div class="col-md-3">
        <div class="panel panel-success">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-arrow-down fa-2x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ summary.total_in }}</div>
                <div>{{ text_total_in }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-md-3">
        <div class="panel panel-danger">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-arrow-up fa-2x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ summary.total_out }}</div>
                <div>{{ text_total_out }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-md-3">
        <div class="panel panel-info">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-balance-scale fa-2x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ summary.net_movement }}</div>
                <div>{{ text_net_movement }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-md-3">
        <div class="panel panel-warning">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-money fa-2x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ summary.total_value }}</div>
                <div>{{ text_total_value }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- جدول الحركات -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-list"></i> {{ text_movement_history }}</h3>
      </div>
      <div class="panel-body">
        <div class="table-responsive">
          <table class="table table-bordered table-hover" id="movements-table">
            <thead>
              <tr>
                <th>{{ column_date }}</th>
                <th>{{ column_movement_type }}</th>
                <th>{{ column_reference }}</th>
                <th>{{ column_lot_number }}</th>
                <th class="text-right">{{ column_quantity_in }}</th>
                <th class="text-right">{{ column_quantity_out }}</th>
                <th class="text-right">{{ column_running_balance }}</th>
                {% if can_view_cost %}
                <th class="text-right">{{ column_unit_cost }}</th>
                <th class="text-right">{{ column_total_cost }}</th>
                {% endif %}
                <th>{{ column_user }}</th>
              </tr>
            </thead>
            <tbody>
              {% for movement in movements %}
              <tr>
                <td>{{ movement.date_added }}</td>
                <td>
                  <span class="label label-{{ movement.movement_type_class }}">
                    {{ movement.movement_type_text }}
                  </span>
                </td>
                <td>
                  {% if movement.reference_number %}
                    {% if movement.view_reference %}
                      <a href="{{ movement.view_reference }}" target="_blank">{{ movement.reference_number }}</a>
                    {% else %}
                      {{ movement.reference_number }}
                    {% endif %}
                  {% else %}
                    <span class="text-muted">---</span>
                  {% endif %}
                </td>
                <td>
                  {% if movement.lot_number %}
                    <span class="label label-default">{{ movement.lot_number }}</span>
                    {% if movement.expiry_date %}
                      <br><small class="text-{{ movement.expiry_status }}">{{ movement.expiry_date }}</small>
                    {% endif %}
                  {% else %}
                    <span class="text-muted">---</span>
                  {% endif %}
                </td>
                <td class="text-right">
                  {% if movement.quantity_in_raw > 0 %}
                    <span class="text-success"><strong>+{{ movement.quantity_in }}</strong></span>
                  {% else %}
                    <span class="text-muted">---</span>
                  {% endif %}
                </td>
                <td class="text-right">
                  {% if movement.quantity_out_raw > 0 %}
                    <span class="text-danger"><strong>-{{ movement.quantity_out }}</strong></span>
                  {% else %}
                    <span class="text-muted">---</span>
                  {% endif %}
                </td>
                <td class="text-right">
                  <strong>{{ movement.running_balance }}</strong>
                </td>
                {% if can_view_cost %}
                <td class="text-right">{{ movement.unit_cost }}</td>
                <td class="text-right">{{ movement.total_cost }}</td>
                {% endif %}
                <td>
                  <small>{{ movement.user_name }}</small>
                  {% if movement.notes %}
                    <br><small class="text-muted">{{ movement.notes }}</small>
                  {% endif %}
                </td>
              </tr>
              {% else %}
              <tr>
                <td class="text-center" colspan="{% if can_view_cost %}10{% else %}8{% endif %}">{{ text_no_results }}</td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
.huge {
    font-size: 30px;
}

.panel-success > .panel-heading {
    color: white;
    background-color: #5cb85c;
    border-color: #5cb85c;
}

.panel-danger > .panel-heading {
    color: white;
    background-color: #d9534f;
    border-color: #d9534f;
}

.panel-info > .panel-heading {
    color: white;
    background-color: #5bc0de;
    border-color: #5bc0de;
}

.panel-warning > .panel-heading {
    color: white;
    background-color: #f0ad4e;
    border-color: #f0ad4e;
}

#movements-table {
    font-size: 13px;
}

#movements-table th {
    background-color: #f5f5f5;
    font-weight: 600;
}
</style>

<script>
$(document).ready(function() {
    // تهيئة DataTable
    $('#movements-table').DataTable({
        "paging": true,
        "searching": true,
        "ordering": true,
        "info": true,
        "responsive": true,
        "pageLength": 25,
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Arabic.json"
        },
        "order": [[ 0, "desc" ]]
    });
});
</script>

{{ footer }}