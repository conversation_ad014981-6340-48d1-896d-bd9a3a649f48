<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/label.proto

namespace GPBMetadata\Google\Api;

class Label
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(hex2bin(
            "0aac020a16676f6f676c652f6170692f6c6162656c2e70726f746f120a67" .
            "6f6f676c652e617069229c010a0f4c6162656c44657363726970746f7212" .
            "0b0a036b657918012001280912390a0a76616c75655f7479706518022001" .
            "280e32252e676f6f676c652e6170692e4c6162656c44657363726970746f" .
            "722e56616c75655479706512130a0b6465736372697074696f6e18032001" .
            "2809222c0a0956616c756554797065120a0a06535452494e47100012080a" .
            "04424f4f4c100112090a05494e5436341002425f0a0e636f6d2e676f6f67" .
            "6c652e617069420a4c6162656c50726f746f50015a35676f6f676c652e67" .
            "6f6c616e672e6f72672f67656e70726f746f2f676f6f676c65617069732f" .
            "6170692f6c6162656c3b6c6162656cf80101a2020447415049620670726f" .
            "746f33"
        ), true);

        static::$is_initialized = true;
    }
}

