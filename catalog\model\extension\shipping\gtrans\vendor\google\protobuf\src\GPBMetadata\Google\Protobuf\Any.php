<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/protobuf/any.proto

namespace GPBMetadata\Google\Protobuf;

class Any
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(hex2bin(
            "0acd010a19676f6f676c652f70726f746f6275662f616e792e70726f746f" .
            "120f676f6f676c652e70726f746f62756622260a03416e7912100a087479" .
            "70655f75726c180120012809120d0a0576616c756518022001280c426f0a" .
            "13636f6d2e676f6f676c652e70726f746f6275664208416e7950726f746f" .
            "50015a256769746875622e636f6d2f676f6c616e672f70726f746f627566" .
            "2f7074797065732f616e79a20203475042aa021e476f6f676c652e50726f" .
            "746f6275662e57656c6c4b6e6f776e5479706573620670726f746f33"
        ));

        static::$is_initialized = true;
    }
}

