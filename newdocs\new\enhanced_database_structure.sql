-- Enhanced Database Structure for AYM ERP
-- Complete Inventory & E-commerce System
-- Replacement for db.txt, minidb.txt, dbindex.txt
-- Date: 20/7/2025

-- =====================================================
-- CORE PRODUCT TABLES (Enhanced)
-- =====================================================

-- Enhanced Product Table
CREATE TABLE IF NOT EXISTS `cod_product` (
  `product_id` int(11) NOT NULL AUTO_INCREMENT,
  `model` varchar(64) NOT NULL,
  `sku` varchar(64) DEFAULT NULL,
  `upc` varchar(12) DEFAULT NULL,
  `ean` varchar(14) DEFAULT NULL,
  `jan` varchar(13) DEFAULT NULL,
  `isbn` varchar(17) DEFAULT NULL,
  `mpn` varchar(64) DEFAULT NULL,
  `location` varchar(128) DEFAULT NULL,
  `quantity` int(4) NOT NULL DEFAULT 0,
  `stock_status_id` int(11) NOT NULL,
  `image` varchar(255) DEFAULT NULL,
  `manufacturer_id` int(11) NOT NULL DEFAULT 0,
  `shipping` tinyint(1) NOT NULL DEFAULT 1,
  `price` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `points` int(8) NOT NULL DEFAULT 0,
  `tax_class_id` int(11) NOT NULL DEFAULT 0,
  `date_available` date NOT NULL DEFAULT '0000-00-00',
  `weight` decimal(15,8) NOT NULL DEFAULT 0.00000000,
  `weight_class_id` int(11) NOT NULL DEFAULT 0,
  `length` decimal(15,8) NOT NULL DEFAULT 0.00000000,
  `width` decimal(15,8) NOT NULL DEFAULT 0.00000000,
  `height` decimal(15,8) NOT NULL DEFAULT 0.00000000,
  `length_class_id` int(11) NOT NULL DEFAULT 0,
  `subtract` tinyint(1) NOT NULL DEFAULT 1,
  `minimum` int(11) NOT NULL DEFAULT 1,
  `sort_order` int(11) NOT NULL DEFAULT 0,
  `status` tinyint(1) NOT NULL DEFAULT 0,
  `viewed` int(5) NOT NULL DEFAULT 0,
  `date_added` datetime NOT NULL,
  `date_modified` datetime NOT NULL,
  -- Enhanced fields for advanced inventory
  `cost_price` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `average_cost` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `last_cost` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `reorder_level` int(11) NOT NULL DEFAULT 0,
  `max_level` int(11) NOT NULL DEFAULT 0,
  `abc_class` enum('A','B','C') DEFAULT NULL,
  `is_serialized` tinyint(1) NOT NULL DEFAULT 0,
  `is_batch_tracked` tinyint(1) NOT NULL DEFAULT 0,
  `shelf_life_days` int(11) DEFAULT NULL,
  `lead_time_days` int(11) DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `updated_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`product_id`),
  UNIQUE KEY `unique_model` (`model`),
  UNIQUE KEY `unique_sku` (`sku`),
  KEY `idx_product_status` (`status`),
  KEY `idx_product_manufacturer` (`manufacturer_id`),
  KEY `idx_product_stock_status` (`stock_status_id`),
  KEY `idx_product_abc_class` (`abc_class`),
  KEY `idx_product_reorder` (`reorder_level`),
  KEY `idx_product_cost` (`cost_price`, `average_cost`),
  KEY `idx_product_dates` (`date_added`, `date_modified`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Enhanced Product Description
CREATE TABLE IF NOT EXISTS `cod_product_description` (
  `product_id` int(11) NOT NULL,
  `language_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `tag` text NOT NULL,
  `meta_title` varchar(255) NOT NULL,
  `meta_description` varchar(255) NOT NULL,
  `meta_keyword` varchar(255) NOT NULL,
  -- Enhanced SEO fields
  `slug` varchar(255) DEFAULT NULL,
  `short_description` text DEFAULT NULL,
  `specifications` text DEFAULT NULL,
  `features` text DEFAULT NULL,
  `benefits` text DEFAULT NULL,
  `usage_instructions` text DEFAULT NULL,
  `warranty_info` text DEFAULT NULL,
  PRIMARY KEY (`product_id`,`language_id`),
  UNIQUE KEY `unique_product_slug` (`product_id`, `language_id`, `slug`),
  KEY `fk_product_description_product` (`product_id`),
  KEY `fk_product_description_language` (`language_id`),
  FULLTEXT KEY `idx_product_search` (`name`, `description`, `tag`, `meta_title`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Enhanced Product Inventory
CREATE TABLE IF NOT EXISTS `cod_product_inventory` (
  `inventory_id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL,
  `warehouse_id` int(11) NOT NULL,
  `location_id` int(11) DEFAULT NULL,
  `unit_id` int(11) NOT NULL,
  `quantity` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `reserved_quantity` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `available_quantity` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `virtual_quantity` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `cost_price` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `average_cost` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `last_cost` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `total_value` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `reorder_level` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `max_level` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `safety_stock` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `last_movement_date` datetime DEFAULT NULL,
  `last_count_date` datetime DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  PRIMARY KEY (`inventory_id`),
  UNIQUE KEY `unique_product_warehouse_unit` (`product_id`, `warehouse_id`, `unit_id`),
  KEY `idx_inventory_warehouse_product` (`warehouse_id`, `product_id`),
  KEY `idx_inventory_quantity` (`quantity`),
  KEY `idx_inventory_reserved` (`reserved_quantity`),
  KEY `idx_inventory_available` (`available_quantity`),
  KEY `idx_inventory_cost` (`average_cost`),
  KEY `idx_inventory_reorder` (`reorder_level`),
  KEY `idx_inventory_updated` (`updated_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Enhanced Product Movement
CREATE TABLE IF NOT EXISTS `cod_product_movement` (
  `movement_id` int(11) NOT NULL AUTO_INCREMENT,
  `movement_number` varchar(50) NOT NULL,
  `product_id` int(11) NOT NULL,
  `warehouse_id` int(11) NOT NULL,
  `location_id` int(11) DEFAULT NULL,
  `unit_id` int(11) NOT NULL,
  `batch_id` int(11) DEFAULT NULL,
  `movement_type` enum('in','out','transfer','adjustment','count','return','damage','expire') NOT NULL,
  `movement_subtype` varchar(50) DEFAULT NULL,
  `reference_type` varchar(50) DEFAULT NULL,
  `reference_id` int(11) DEFAULT NULL,
  `reference_number` varchar(100) DEFAULT NULL,
  `quantity` decimal(15,4) NOT NULL,
  `unit_cost` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `total_cost` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `running_quantity` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `running_cost` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `movement_date` datetime NOT NULL,
  `notes` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`movement_id`),
  UNIQUE KEY `unique_movement_number` (`movement_number`),
  KEY `idx_movement_product` (`product_id`),
  KEY `idx_movement_warehouse` (`warehouse_id`),
  KEY `idx_movement_date_product` (`movement_date`, `product_id`),
  KEY `idx_movement_type` (`movement_type`),
  KEY `idx_movement_reference` (`reference_type`, `reference_id`),
  KEY `idx_movement_batch` (`batch_id`),
  KEY `idx_movement_cost` (`unit_cost`),
  KEY `idx_movement_user` (`created_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Enhanced Warehouse Structure
CREATE TABLE IF NOT EXISTS `cod_warehouse` (
  `warehouse_id` int(11) NOT NULL AUTO_INCREMENT,
  `warehouse_code` varchar(20) NOT NULL,
  `warehouse_name` varchar(255) NOT NULL,
  `warehouse_type` enum('main','branch','virtual','transit','quarantine') NOT NULL DEFAULT 'main',
  `parent_warehouse_id` int(11) DEFAULT NULL,
  `branch_id` int(11) DEFAULT NULL,
  `address` text DEFAULT NULL,
  `city` varchar(100) DEFAULT NULL,
  `postcode` varchar(20) DEFAULT NULL,
  `country_id` int(11) DEFAULT NULL,
  `zone_id` int(11) DEFAULT NULL,
  `manager_id` int(11) DEFAULT NULL,
  `contact_phone` varchar(20) DEFAULT NULL,
  `contact_email` varchar(100) DEFAULT NULL,
  `capacity` decimal(15,4) DEFAULT NULL,
  `current_usage` decimal(15,4) DEFAULT 0.0000,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `is_default` tinyint(1) NOT NULL DEFAULT 0,
  `allow_negative_stock` tinyint(1) NOT NULL DEFAULT 0,
  `auto_reorder` tinyint(1) NOT NULL DEFAULT 0,
  `sort_order` int(11) NOT NULL DEFAULT 0,
  `notes` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_by` int(11) DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  PRIMARY KEY (`warehouse_id`),
  UNIQUE KEY `unique_warehouse_code` (`warehouse_code`),
  KEY `idx_warehouse_type` (`warehouse_type`),
  KEY `idx_warehouse_branch` (`branch_id`),
  KEY `idx_warehouse_parent` (`parent_warehouse_id`),
  KEY `idx_warehouse_manager` (`manager_id`),
  KEY `idx_warehouse_active` (`is_active`),
  KEY `idx_warehouse_default` (`is_default`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- =====================================================
-- ADVANCED INVENTORY TABLES
-- =====================================================

-- Warehouse Locations (3D positioning)
CREATE TABLE IF NOT EXISTS `cod_warehouse_location` (
  `location_id` int(11) NOT NULL AUTO_INCREMENT,
  `warehouse_id` int(11) NOT NULL,
  `location_code` varchar(50) NOT NULL,
  `location_name` varchar(255) NOT NULL,
  `location_type` enum('zone','aisle','shelf','bin','rack','floor') NOT NULL DEFAULT 'bin',
  `parent_location_id` int(11) DEFAULT NULL,
  `capacity` decimal(15,4) DEFAULT NULL,
  `current_usage` decimal(15,4) DEFAULT 0.0000,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `barcode` varchar(100) DEFAULT NULL,
  `coordinates_x` decimal(10,2) DEFAULT NULL,
  `coordinates_y` decimal(10,2) DEFAULT NULL,
  `coordinates_z` decimal(10,2) DEFAULT NULL,
  `temperature_controlled` tinyint(1) NOT NULL DEFAULT 0,
  `min_temperature` decimal(5,2) DEFAULT NULL,
  `max_temperature` decimal(5,2) DEFAULT NULL,
  `humidity_controlled` tinyint(1) NOT NULL DEFAULT 0,
  `min_humidity` decimal(5,2) DEFAULT NULL,
  `max_humidity` decimal(5,2) DEFAULT NULL,
  `security_level` enum('low','medium','high','maximum') NOT NULL DEFAULT 'medium',
  `access_restrictions` text DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_by` int(11) DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  PRIMARY KEY (`location_id`),
  UNIQUE KEY `unique_warehouse_location_code` (`warehouse_id`,`location_code`),
  KEY `idx_location_warehouse` (`warehouse_id`),
  KEY `idx_location_parent` (`parent_location_id`),
  KEY `idx_location_type` (`location_type`),
  KEY `idx_location_active` (`is_active`),
  KEY `idx_location_barcode` (`barcode`),
  KEY `idx_location_coordinates` (`coordinates_x`, `coordinates_y`, `coordinates_z`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Advanced Batch Tracking
CREATE TABLE IF NOT EXISTS `cod_batch_tracking` (
  `batch_id` int(11) NOT NULL AUTO_INCREMENT,
  `batch_number` varchar(100) NOT NULL,
  `product_id` int(11) NOT NULL,
  `supplier_id` int(11) DEFAULT NULL,
  `manufacture_date` date DEFAULT NULL,
  `expiry_date` date DEFAULT NULL,
  `received_date` datetime NOT NULL,
  `initial_quantity` decimal(15,4) NOT NULL,
  `current_quantity` decimal(15,4) NOT NULL,
  `unit_id` int(11) NOT NULL,
  `cost_per_unit` decimal(15,4) NOT NULL,
  `total_cost` decimal(15,4) NOT NULL,
  `warehouse_id` int(11) NOT NULL,
  `location_id` int(11) DEFAULT NULL,
  `status` enum('active','expired','recalled','consumed','transferred','quarantine') NOT NULL DEFAULT 'active',
  `quality_status` enum('pending','approved','rejected','quarantine','tested') NOT NULL DEFAULT 'pending',
  `quality_notes` text DEFAULT NULL,
  `quality_checked_by` int(11) DEFAULT NULL,
  `quality_checked_at` datetime DEFAULT NULL,
  `certificate_number` varchar(100) DEFAULT NULL,
  `test_results` text DEFAULT NULL,
  `barcode` varchar(100) DEFAULT NULL,
  `qr_code` varchar(255) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`batch_id`),
  UNIQUE KEY `unique_batch_number` (`batch_number`),
  KEY `idx_batch_product` (`product_id`),
  KEY `idx_batch_supplier` (`supplier_id`),
  KEY `idx_batch_expiry` (`expiry_date`),
  KEY `idx_batch_status` (`status`),
  KEY `idx_batch_quality` (`quality_status`),
  KEY `idx_batch_warehouse` (`warehouse_id`),
  KEY `idx_batch_location` (`location_id`),
  KEY `idx_batch_barcode` (`barcode`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- =====================================================
-- POS SYSTEM TABLES
-- =====================================================

-- POS Sessions
CREATE TABLE IF NOT EXISTS `cod_pos_session` (
  `session_id` int(11) NOT NULL AUTO_INCREMENT,
  `session_number` varchar(50) NOT NULL,
  `terminal_id` int(11) NOT NULL,
  `cashier_id` int(11) NOT NULL,
  `shift_id` int(11) DEFAULT NULL,
  `start_time` datetime NOT NULL,
  `end_time` datetime DEFAULT NULL,
  `status` enum('active','closed','suspended','cancelled') NOT NULL DEFAULT 'active',
  `opening_cash` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `closing_cash` decimal(15,4) DEFAULT NULL,
  `expected_cash` decimal(15,4) DEFAULT NULL,
  `cash_difference` decimal(15,4) DEFAULT NULL,
  `total_sales` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `total_transactions` int(11) NOT NULL DEFAULT 0,
  `total_items` int(11) NOT NULL DEFAULT 0,
  `total_discounts` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `total_tax` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `notes` text DEFAULT NULL,
  `closed_by` int(11) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`session_id`),
  UNIQUE KEY `unique_session_number` (`session_number`),
  KEY `idx_session_terminal` (`terminal_id`),
  KEY `idx_session_cashier` (`cashier_id`),
  KEY `idx_session_shift` (`shift_id`),
  KEY `idx_session_status` (`status`),
  KEY `idx_session_date` (`start_time`, `end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- POS Transactions
CREATE TABLE IF NOT EXISTS `cod_pos_transaction` (
  `transaction_id` int(11) NOT NULL AUTO_INCREMENT,
  `transaction_number` varchar(50) NOT NULL,
  `session_id` int(11) NOT NULL,
  `customer_id` int(11) DEFAULT NULL,
  `transaction_type` enum('sale','return','void','exchange','layaway') NOT NULL DEFAULT 'sale',
  `transaction_date` datetime NOT NULL,
  `subtotal` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `discount_amount` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `tax_amount` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `total_amount` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `paid_amount` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `change_amount` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `status` enum('pending','completed','cancelled','refunded','partial_refund') NOT NULL DEFAULT 'pending',
  `payment_status` enum('pending','paid','partial','overpaid','refunded') NOT NULL DEFAULT 'pending',
  `receipt_printed` tinyint(1) NOT NULL DEFAULT 0,
  `receipt_number` varchar(50) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`transaction_id`),
  UNIQUE KEY `unique_transaction_number` (`transaction_number`),
  KEY `idx_transaction_session` (`session_id`),
  KEY `idx_transaction_customer` (`customer_id`),
  KEY `idx_transaction_type` (`transaction_type`),
  KEY `idx_transaction_date` (`transaction_date`),
  KEY `idx_transaction_status` (`status`),
  KEY `idx_transaction_receipt` (`receipt_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- POS Transaction Items
CREATE TABLE IF NOT EXISTS `cod_pos_transaction_item` (
  `item_id` int(11) NOT NULL AUTO_INCREMENT,
  `transaction_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `unit_id` int(11) NOT NULL,
  `batch_id` int(11) DEFAULT NULL,
  `quantity` decimal(15,4) NOT NULL,
  `unit_price` decimal(15,4) NOT NULL,
  `discount_amount` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `tax_amount` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `line_total` decimal(15,4) NOT NULL,
  `cost_price` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `profit_amount` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `price_level` enum('basic','special','wholesale','semi_wholesale','custom') NOT NULL DEFAULT 'basic',
  `notes` text DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`item_id`),
  KEY `idx_item_transaction` (`transaction_id`),
  KEY `idx_item_product` (`product_id`),
  KEY `idx_item_batch` (`batch_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- =====================================================
-- E-COMMERCE TABLES
-- =====================================================

-- Enhanced Cart
CREATE TABLE IF NOT EXISTS `cod_cart` (
  `cart_id` int(11) NOT NULL AUTO_INCREMENT,
  `api_id` int(11) DEFAULT NULL,
  `customer_id` int(11) DEFAULT NULL,
  `session_id` varchar(32) NOT NULL,
  `product_id` int(11) NOT NULL,
  `recurring_id` int(11) NOT NULL DEFAULT 0,
  `option` text NOT NULL,
  `quantity` int(4) NOT NULL,
  `date_added` datetime NOT NULL,
  -- Enhanced fields
  `unit_id` int(11) DEFAULT NULL,
  `warehouse_id` int(11) DEFAULT NULL,
  `reserved_until` datetime DEFAULT NULL,
  `price_locked` tinyint(1) NOT NULL DEFAULT 0,
  `locked_price` decimal(15,4) DEFAULT NULL,
  `bundle_id` int(11) DEFAULT NULL,
  `parent_cart_id` int(11) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  PRIMARY KEY (`cart_id`),
  KEY `idx_cart_customer` (`customer_id`),
  KEY `idx_cart_session` (`session_id`),
  KEY `idx_cart_product` (`product_id`),
  KEY `idx_cart_date` (`date_added`),
  KEY `idx_cart_reserved` (`reserved_until`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Enhanced Order
CREATE TABLE IF NOT EXISTS `cod_order` (
  `order_id` int(11) NOT NULL AUTO_INCREMENT,
  `invoice_no` int(11) NOT NULL DEFAULT 0,
  `invoice_prefix` varchar(26) NOT NULL,
  `store_id` int(11) NOT NULL DEFAULT 0,
  `store_name` varchar(64) NOT NULL,
  `store_url` varchar(255) NOT NULL,
  `customer_id` int(11) NOT NULL DEFAULT 0,
  `customer_group_id` int(11) NOT NULL DEFAULT 0,
  `firstname` varchar(32) NOT NULL,
  `lastname` varchar(32) NOT NULL,
  `email` varchar(96) NOT NULL,
  `telephone` varchar(32) NOT NULL,
  `custom_field` text NOT NULL,
  `payment_firstname` varchar(32) NOT NULL,
  `payment_lastname` varchar(32) NOT NULL,
  `payment_company` varchar(60) NOT NULL,
  `payment_address_1` varchar(128) NOT NULL,
  `payment_address_2` varchar(128) NOT NULL,
  `payment_city` varchar(128) NOT NULL,
  `payment_postcode` varchar(10) NOT NULL,
  `payment_country` varchar(128) NOT NULL,
  `payment_country_id` int(11) NOT NULL,
  `payment_zone` varchar(128) NOT NULL,
  `payment_zone_id` int(11) NOT NULL,
  `payment_address_format` text NOT NULL,
  `payment_custom_field` text NOT NULL,
  `payment_method` varchar(128) NOT NULL,
  `payment_code` varchar(128) NOT NULL,
  `shipping_firstname` varchar(32) NOT NULL,
  `shipping_lastname` varchar(32) NOT NULL,
  `shipping_company` varchar(60) NOT NULL,
  `shipping_address_1` varchar(128) NOT NULL,
  `shipping_address_2` varchar(128) NOT NULL,
  `shipping_city` varchar(128) NOT NULL,
  `shipping_postcode` varchar(10) NOT NULL,
  `shipping_country` varchar(128) NOT NULL,
  `shipping_country_id` int(11) NOT NULL,
  `shipping_zone` varchar(128) NOT NULL,
  `shipping_zone_id` int(11) NOT NULL,
  `shipping_address_format` text NOT NULL,
  `shipping_custom_field` text NOT NULL,
  `shipping_method` varchar(128) NOT NULL,
  `shipping_code` varchar(128) NOT NULL,
  `comment` text NOT NULL,
  `total` decimal(15,4) NOT NULL DEFAULT 0.0000,
  `order_status_id` int(11) NOT NULL DEFAULT 0,
  `affiliate_id` int(11) NOT NULL,
  `commission` decimal(15,4) NOT NULL,
  `marketing_id` int(11) NOT NULL,
  `tracking` varchar(64) NOT NULL,
  `language_id` int(11) NOT NULL,
  `currency_id` int(11) NOT NULL,
  `currency_code` varchar(3) NOT NULL,
  `currency_value` decimal(15,8) NOT NULL DEFAULT 1.00000000,
  `ip` varchar(40) NOT NULL,
  `forwarded_ip` varchar(40) NOT NULL,
  `user_agent` varchar(255) NOT NULL,
  `accept_language` varchar(255) NOT NULL,
  `date_added` datetime NOT NULL,
  `date_modified` datetime NOT NULL,
  -- Enhanced fields
  `branch_id` int(11) DEFAULT NULL,
  `warehouse_id` int(11) DEFAULT NULL,
  `priority` enum('low','normal','high','urgent') NOT NULL DEFAULT 'normal',
  `source` enum('website','mobile','pos','phone','admin') NOT NULL DEFAULT 'website',
  `fulfillment_status` enum('pending','processing','shipped','delivered','cancelled') NOT NULL DEFAULT 'pending',
  `payment_status` enum('pending','paid','partial','refunded','cancelled') NOT NULL DEFAULT 'pending',
  `estimated_delivery` datetime DEFAULT NULL,
  `actual_delivery` datetime DEFAULT NULL,
  `delivery_notes` text DEFAULT NULL,
  PRIMARY KEY (`order_id`),
  KEY `idx_order_customer` (`customer_id`),
  KEY `idx_order_status` (`order_status_id`),
  KEY `idx_order_date` (`date_added`),
  KEY `idx_order_branch` (`branch_id`),
  KEY `idx_order_warehouse` (`warehouse_id`),
  KEY `idx_order_fulfillment` (`fulfillment_status`),
  KEY `idx_order_payment` (`payment_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
