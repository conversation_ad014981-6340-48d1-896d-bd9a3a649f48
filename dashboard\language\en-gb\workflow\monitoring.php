<?php
/**
 * English Language File - Workflow Monitoring
 * 
 * @package    AYM ERP
 * <AUTHOR> Development Team
 * @copyright  2025 AYM ERP Systems
 * @license    Commercial License
 * @version    1.0.0
 * @since      2025-07-15
 */

// Main heading
$_['heading_title'] = 'Workflow Monitoring';

// General texts
$_['text_success'] = 'Success: You have updated monitoring settings!';
$_['text_monitoring'] = 'Monitoring';
$_['text_dashboard'] = 'Monitoring Dashboard';
$_['text_real_time'] = 'Real Time';
$_['text_historical'] = 'Historical Data';
$_['text_alerts'] = 'Alerts';
$_['text_reports'] = 'Reports';
$_['text_loading'] = 'Loading...';
$_['text_no_data'] = 'No data available!';

// Monitoring dashboard
$_['text_monitoring_dashboard'] = 'Workflow Monitoring Dashboard';
$_['text_overview'] = 'Overview';
$_['text_active_workflows'] = 'Active Workflows';
$_['text_pending_tasks'] = 'Pending Tasks';
$_['text_completed_today'] = 'Completed Today';
$_['text_overdue_items'] = 'Overdue Items';
$_['text_system_health'] = 'System Health';
$_['text_performance_metrics'] = 'Performance Metrics';

// Status and statistics
$_['text_workflow_status'] = 'Workflow Status';
$_['text_status_running'] = 'Running';
$_['text_status_paused'] = 'Paused';
$_['text_status_completed'] = 'Completed';
$_['text_status_failed'] = 'Failed';
$_['text_status_cancelled'] = 'Cancelled';
$_['text_status_waiting'] = 'Waiting';

// Performance metrics
$_['text_performance'] = 'Performance';
$_['text_throughput'] = 'Throughput';
$_['text_cycle_time'] = 'Cycle Time';
$_['text_lead_time'] = 'Lead Time';
$_['text_processing_time'] = 'Processing Time';
$_['text_wait_time'] = 'Wait Time';
$_['text_efficiency'] = 'Efficiency';
$_['text_utilization'] = 'Utilization';
$_['text_success_rate'] = 'Success Rate';
$_['text_error_rate'] = 'Error Rate';

// Live monitoring
$_['text_live_monitoring'] = 'Live Monitoring';
$_['text_active_instances'] = 'Active Instances';
$_['text_current_step'] = 'Current Step';
$_['text_assigned_to'] = 'Assigned To';
$_['text_time_elapsed'] = 'Time Elapsed';
$_['text_time_remaining'] = 'Time Remaining';
$_['text_progress'] = 'Progress';
$_['text_bottlenecks'] = 'Bottlenecks';

// Alerts and notifications
$_['text_alerts_notifications'] = 'Alerts & Notifications';
$_['text_critical_alerts'] = 'Critical Alerts';
$_['text_warning_alerts'] = 'Warning Alerts';
$_['text_info_alerts'] = 'Info Alerts';
$_['text_sla_violations'] = 'SLA Violations';
$_['text_timeout_alerts'] = 'Timeout Alerts';
$_['text_error_alerts'] = 'Error Alerts';
$_['text_escalation_alerts'] = 'Escalation Alerts';

// Alert types
$_['text_alert_types'] = 'Alert Types';
$_['text_alert_overdue'] = 'Overdue';
$_['text_alert_stuck'] = 'Stuck';
$_['text_alert_high_volume'] = 'High Volume';
$_['text_alert_low_performance'] = 'Low Performance';
$_['text_alert_system_error'] = 'System Error';
$_['text_alert_resource_limit'] = 'Resource Limit';

// Logs and tracking
$_['text_logs_tracking'] = 'Logs & Tracking';
$_['text_execution_logs'] = 'Execution Logs';
$_['text_audit_trail'] = 'Audit Trail';
$_['text_activity_log'] = 'Activity Log';
$_['text_error_logs'] = 'Error Logs';
$_['text_performance_logs'] = 'Performance Logs';
$_['text_user_actions'] = 'User Actions';

// Reports
$_['text_monitoring_reports'] = 'Monitoring Reports';
$_['text_daily_report'] = 'Daily Report';
$_['text_weekly_report'] = 'Weekly Report';
$_['text_monthly_report'] = 'Monthly Report';
$_['text_custom_report'] = 'Custom Report';
$_['text_executive_summary'] = 'Executive Summary';
$_['text_detailed_analysis'] = 'Detailed Analysis';

// Charts and graphs
$_['text_charts_graphs'] = 'Charts & Graphs';
$_['text_workflow_timeline'] = 'Workflow Timeline';
$_['text_performance_trends'] = 'Performance Trends';
$_['text_volume_analysis'] = 'Volume Analysis';
$_['text_distribution_chart'] = 'Distribution Chart';
$_['text_heat_map'] = 'Heat Map';
$_['text_gantt_chart'] = 'Gantt Chart';

// Filters and search
$_['text_filters'] = 'Filters';
$_['text_filter_by_workflow'] = 'Filter by Workflow';
$_['text_filter_by_status'] = 'Filter by Status';
$_['text_filter_by_user'] = 'Filter by User';
$_['text_filter_by_department'] = 'Filter by Department';
$_['text_filter_by_date'] = 'Filter by Date';
$_['text_filter_by_priority'] = 'Filter by Priority';
$_['text_date_range'] = 'Date Range';
$_['text_time_period'] = 'Time Period';

// Settings
$_['text_monitoring_settings'] = 'Monitoring Settings';
$_['text_alert_settings'] = 'Alert Settings';
$_['text_notification_settings'] = 'Notification Settings';
$_['text_threshold_settings'] = 'Threshold Settings';
$_['text_refresh_interval'] = 'Refresh Interval';
$_['text_data_retention'] = 'Data Retention';

// Thresholds and limits
$_['text_thresholds'] = 'Thresholds & Limits';
$_['text_performance_threshold'] = 'Performance Threshold';
$_['text_time_threshold'] = 'Time Threshold';
$_['text_volume_threshold'] = 'Volume Threshold';
$_['text_error_threshold'] = 'Error Threshold';
$_['text_warning_level'] = 'Warning Level';
$_['text_critical_level'] = 'Critical Level';

// Analytics and intelligence
$_['text_analytics'] = 'Analytics & Intelligence';
$_['text_predictive_analysis'] = 'Predictive Analysis';
$_['text_trend_analysis'] = 'Trend Analysis';
$_['text_pattern_recognition'] = 'Pattern Recognition';
$_['text_anomaly_detection'] = 'Anomaly Detection';
$_['text_capacity_planning'] = 'Capacity Planning';
$_['text_optimization_suggestions'] = 'Optimization Suggestions';

// Export and sharing
$_['text_export'] = 'Export';
$_['text_export_data'] = 'Export Data';
$_['text_export_report'] = 'Export Report';
$_['text_export_chart'] = 'Export Chart';
$_['text_share_dashboard'] = 'Share Dashboard';
$_['text_schedule_report'] = 'Schedule Report';
$_['text_email_report'] = 'Email Report';

// Columns and fields
$_['column_workflow_name'] = 'Workflow Name';
$_['column_instance_id'] = 'Instance ID';
$_['column_current_step'] = 'Current Step';
$_['column_status'] = 'Status';
$_['column_assignee'] = 'Assignee';
$_['column_start_time'] = 'Start Time';
$_['column_duration'] = 'Duration';
$_['column_progress'] = 'Progress';
$_['column_priority'] = 'Priority';
$_['column_action'] = 'Action';

// Buttons and actions
$_['button_refresh'] = 'Refresh';
$_['button_pause'] = 'Pause';
$_['button_resume'] = 'Resume';
$_['button_cancel'] = 'Cancel';
$_['button_restart'] = 'Restart';
$_['button_view_details'] = 'View Details';
$_['button_export_data'] = 'Export Data';
$_['button_configure_alerts'] = 'Configure Alerts';
$_['button_generate_report'] = 'Generate Report';

// Advanced monitoring
$_['text_advanced_monitoring'] = 'Advanced Monitoring';
$_['text_custom_metrics'] = 'Custom Metrics';
$_['text_business_metrics'] = 'Business Metrics';
$_['text_technical_metrics'] = 'Technical Metrics';
$_['text_compliance_monitoring'] = 'Compliance Monitoring';
$_['text_security_monitoring'] = 'Security Monitoring';

// Integrations
$_['text_integrations'] = 'Integrations';
$_['text_external_monitoring'] = 'External Monitoring';
$_['text_third_party_tools'] = 'Third Party Tools';
$_['text_api_monitoring'] = 'API Monitoring';
$_['text_webhook_monitoring'] = 'Webhook Monitoring';

// Error messages
$_['error_permission'] = 'Warning: You do not have permission to access workflow monitoring!';
$_['error_no_data'] = 'No data to display!';
$_['error_invalid_date_range'] = 'Invalid date range!';
$_['error_export_failed'] = 'Data export failed!';
$_['error_report_generation_failed'] = 'Report generation failed!';

// Help and tips
$_['help_monitoring'] = 'Use the monitoring dashboard to track workflow performance';
$_['help_alerts'] = 'Configure alerts to get instant notifications';
$_['help_thresholds'] = 'Set appropriate thresholds for performance monitoring';
$_['help_reports'] = 'Generate regular reports for performance analysis';

// Alerts
$_['alert_threshold_exceeded'] = 'Threshold exceeded';
$_['alert_workflow_stuck'] = 'Workflow stuck at step';
$_['alert_high_error_rate'] = 'High error rate detected';
$_['alert_performance_degradation'] = 'Performance degradation';
$_['alert_sla_breach'] = 'SLA breach detected';

// Dates and times
$_['text_last_updated'] = 'Last Updated';
$_['text_refresh_time'] = 'Refresh Time';
$_['text_data_as_of'] = 'Data As Of';
$_['text_real_time_data'] = 'Real-time Data';
$_['text_historical_data'] = 'Historical Data';

// Units and measurements
$_['text_units'] = 'Units';
$_['text_seconds'] = 'Seconds';
$_['text_minutes'] = 'Minutes';
$_['text_hours'] = 'Hours';
$_['text_days'] = 'Days';
$_['text_percentage'] = 'Percentage';
$_['text_count'] = 'Count';
$_['text_rate'] = 'Rate';

// System status
$_['text_system_status'] = 'System Status';
$_['text_healthy'] = 'Healthy';
$_['text_warning'] = 'Warning';
$_['text_critical'] = 'Critical';
$_['text_maintenance'] = 'Maintenance';
$_['text_offline'] = 'Offline';

// Performance indicators
$_['text_kpi'] = 'Key Performance Indicators';
$_['text_sla_compliance'] = 'SLA Compliance';
$_['text_availability'] = 'Availability';
$_['text_response_time'] = 'Response Time';
$_['text_error_count'] = 'Error Count';
$_['text_active_users'] = 'Active Users';

// Notifications
$_['text_notification_channels'] = 'Notification Channels';
$_['text_email_notifications'] = 'Email Notifications';
$_['text_sms_notifications'] = 'SMS Notifications';
$_['text_push_notifications'] = 'Push Notifications';
$_['text_webhook_notifications'] = 'Webhook Notifications';

// Automation
$_['text_automated_responses'] = 'Automated Responses';
$_['text_auto_scaling'] = 'Auto Scaling';
$_['text_self_healing'] = 'Self Healing';
$_['text_predictive_maintenance'] = 'Predictive Maintenance';
$_['text_intelligent_routing'] = 'Intelligent Routing';
?>
