<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/cloud/translate/v3/translation_service.proto

namespace Google\Cloud\Translate\V3;

if (false) {
    /**
     * This class is deprecated. Use Google\Cloud\Translate\V3\DeleteGlossaryMetadata\State instead.
     * @deprecated
     */
    class DeleteGlossaryMetadata_State {}
}
class_exists(DeleteGlossaryMetadata\State::class);
@trigger_error('Google\Cloud\Translate\V3\DeleteGlossaryMetadata_State is deprecated and will be removed in a future release. Use Google\Cloud\Translate\V3\DeleteGlossaryMetadata\State instead', E_USER_DEPRECATED);

