<?php
// Heading
$_['text_title']				= 'Credit / Debit card (First Data)';

// Button
$_['button_confirm']			= 'Continue';

// Text
$_['text_new_card']				= 'New card';
$_['text_store_card']			= 'Remember my card details';
$_['text_address_response']		= 'Address verification: ';
$_['text_address_ppx']			= 'No address data provided or Address not checked by the Card Issuer';
$_['text_address_yyy']			= 'Card Issuer confirmed that street and postcode match with their records';
$_['text_address_yna']			= 'Card Issuer confirmed that street matches with their records but postcode does not match';
$_['text_address_nyz']			= 'Card Issuer confirmed that postcode matches with their records but street does not match';
$_['text_address_nnn']			= 'Both street and postcode do not match with the Card Issuer records';
$_['text_address_ypx']			= 'Card Issuer confirmed that street matches with their records. The Issuer did not check the postcode';
$_['text_address_pyx']			= 'Card Issuer confirmed that postcode matches with their records. The Issuer did not check the street';
$_['text_address_xxu']			= 'Card Issuer did not check the AVS information';
$_['text_card_code_verify']		= 'Security code: ';
$_['text_card_code_m']			= 'Card security code match';
$_['text_card_code_n']			= 'Card security code does not match';
$_['text_card_code_p']			= 'Not processed';
$_['text_card_code_s']			= 'Merchant has indicated that the card security code is not present on the card';
$_['text_card_code_u']			= 'Issuer is not certified and/or has not provided encryption keys';
$_['text_card_code_x']			= 'No response from the credit card association was received';
$_['text_card_code_blank']		= 'A blank response should indicate that no code was sent and that there was no indication that the code was not present on the card.';
$_['text_card_type_m']			= 'Mastercard';
$_['text_card_type_v']			= 'Visa (Credit/Debit/Electron/Delta)';
$_['text_card_type_c']			= 'Diners';
$_['text_card_type_a']			= 'American Express';
$_['text_card_type_ma']			= 'Maestro';
$_['text_card_type_mauk']		= 'Maestro UK/Solo';
$_['text_response_code_full']	= 'Approval code: ';
$_['text_response_code']		= 'Full response code: ';
$_['text_response_card']		= 'Card used: ';
$_['text_response_card_type']	= 'Card type: ';
$_['text_response_proc_code']	= 'Processor code: ';
$_['text_response_ref']			= 'Ref number: ';

// Error
$_['error_failed']				= 'Unable to process your payment, please try again';