{{ header }}{{ column_left }}

<!-- Enterprise Grade Plus CSS for Annual Tax Report -->
<style>
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --danger-color: #e74c3c;
    --warning-color: #f39c12;
    --info-color: #17a2b8;
    --tax-color: #8e44ad;
    --compliance-color: #16a085;
    --light-bg: #f8f9fa;
    --border-color: #dee2e6;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

.annual-tax-container {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: var(--shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.annual-tax-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--tax-color), var(--compliance-color), var(--primary-color));
}

.tax-header {
    text-align: center;
    border-bottom: 3px solid var(--tax-color);
    padding-bottom: 20px;
    margin-bottom: 30px;
    position: relative;
}

.tax-header h2 {
    color: var(--tax-color);
    font-weight: 700;
    font-size: 1.8rem;
    margin: 0;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.tax-summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.tax-card {
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    text-align: center;
    box-shadow: var(--shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.tax-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0,0,0,0.1);
}

.tax-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
}

.tax-card.vat::before { background: var(--tax-color); }
.tax-card.income::before { background: var(--compliance-color); }
.tax-card.total::before { background: var(--primary-color); }
.tax-card.compliance::before { background: var(--success-color); }

.tax-card h4 {
    color: var(--primary-color);
    margin-bottom: 10px;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.tax-card .amount {
    font-size: 1.4rem;
    font-weight: 700;
    font-family: 'Courier New', monospace;
    margin-bottom: 5px;
}

.tax-card .percentage {
    font-size: 0.8rem;
    color: #6c757d;
    font-weight: 500;
}

.card-vat .amount { color: var(--tax-color); }
.card-income .amount { color: var(--compliance-color); }
.card-total .amount { color: var(--primary-color); }
.card-compliance .amount { color: var(--success-color); }

.tax-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    background: #ffffff;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.tax-table th {
    background: linear-gradient(135deg, var(--tax-color), #9b59b6);
    color: white;
    padding: 15px 12px;
    text-align: center;
    font-weight: 600;
    font-size: 0.9rem;
    border-bottom: 2px solid var(--border-color);
}

.tax-table td {
    padding: 12px;
    border-bottom: 1px solid var(--border-color);
    text-align: center;
    transition: var(--transition);
}

.tax-table tbody tr:hover {
    background: var(--light-bg);
    transform: scale(1.005);
}

.tax-table tbody tr:nth-child(even) {
    background: #fafbfc;
}

.amount-cell {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    text-align: {{ direction == 'rtl' ? 'right' : 'left' }};
}

.tax-liability { color: var(--danger-color); font-weight: 700; }
.tax-refund { color: var(--success-color); font-weight: 700; }
.tax-paid { color: var(--info-color); }

.compliance-indicator {
    display: inline-block;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    margin-right: 8px;
    position: relative;
}

.compliance-indicator::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: rgba(255,255,255,0.8);
    transform: translate(-50%, -50%);
}

.compliance-compliant { background: var(--success-color); }
.compliance-warning { background: var(--warning-color); }
.compliance-violation { background: var(--danger-color); }

.eta-integration {
    background: linear-gradient(135deg, #e8f5e8, #ffffff);
    border: 1px solid var(--success-color);
    border-radius: var(--border-radius);
    padding: 20px;
    margin-top: 30px;
}

.eta-status {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;
}

.eta-badge {
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.eta-connected { background: var(--success-color); color: white; }
.eta-disconnected { background: var(--danger-color); color: white; }
.eta-pending { background: var(--warning-color); color: white; }

/* RTL Support */
[dir="rtl"] .tax-table {
    direction: rtl;
}

[dir="rtl"] .amount-cell {
    text-align: right;
}

/* Accessibility Enhancements */
.tax-table tr:focus-within {
    outline: 2px solid var(--secondary-color);
    outline-offset: 2px;
}

/* Print Styles */
@media print {
    .annual-tax-container {
        box-shadow: none;
        border: 1px solid #000;
    }
    
    .tax-table th {
        background: #f0f0f0 !important;
        color: #000 !important;
    }
    
    .eta-integration {
        display: none;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .tax-table {
        font-size: 0.8rem;
    }
    
    .tax-table th,
    .tax-table td {
        padding: 8px 6px;
    }
    
    .tax-summary-cards {
        grid-template-columns: repeat(2, 1fr);
    }
}
</style>

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-{{ direction == 'rtl' ? 'start' : 'end' }}">
        <div class="btn-group" role="group" aria-label="{{ text_actions }}">
          <button type="button" class="btn btn-success btn-lg" onclick="generateAnnualTaxReport()" 
                  data-bs-toggle="tooltip" title="{{ text_generate_tooltip }}">
            <i class="fas fa-calculator me-2"></i> {{ button_generate }}
          </button>
          <div class="btn-group" role="group">
            <button type="button" class="btn btn-info dropdown-toggle" data-bs-toggle="dropdown" 
                    aria-expanded="false" data-bs-toggle="tooltip" title="{{ text_export_tooltip }}">
              <i class="fas fa-download me-2"></i> {{ text_export }}
            </button>
            <ul class="dropdown-menu">
              <li><a class="dropdown-item" href="#" onclick="exportAnnualTaxReport('excel')">
                <i class="fas fa-file-excel text-success me-2"></i> Excel
              </a></li>
              <li><a class="dropdown-item" href="#" onclick="exportAnnualTaxReport('pdf')">
                <i class="fas fa-file-pdf text-danger me-2"></i> PDF
              </a></li>
              <li><a class="dropdown-item" href="#" onclick="exportAnnualTaxReport('csv')">
                <i class="fas fa-file-csv text-info me-2"></i> CSV
              </a></li>
              <li><hr class="dropdown-divider"></li>
              <li><a class="dropdown-item" href="#" onclick="printAnnualTaxReport()">
                <i class="fas fa-print text-secondary me-2"></i> {{ text_print }}
              </a></li>
            </ul>
          </div>
          <button type="button" class="btn btn-outline-warning" onclick="showComplianceAnalysis()"
                  data-bs-toggle="tooltip" title="{{ text_compliance_analysis }}">
            <i class="fas fa-shield-alt"></i>
          </button>
          <button type="button" class="btn btn-outline-primary" onclick="connectToETA()"
                  data-bs-toggle="tooltip" title="{{ text_eta_integration }}">
            <i class="fas fa-link"></i>
          </button>
        </div>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li class="breadcrumb-item"><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>

  <div class="container-fluid">
    <!-- Tax Filter Form -->
    <div class="card mb-4">
      <div class="card-header">
        <h3 class="card-title">
          <i class="fas fa-filter me-2"></i>{{ text_tax_filters }}
        </h3>
      </div>
      <div class="card-body">
        <form id="annual-tax-filter-form" method="post">
          <div class="row">
            <div class="col-md-3">
              <div class="form-group">
                <label for="year" class="form-label">{{ entry_tax_year }}</label>
                <select name="year" id="year" class="form-control" required>
                  {% for year_option in years %}
                  <option value="{{ year_option.value }}"{% if year_option.value == year %} selected{% endif %}>{{ year_option.text }}</option>
                  {% endfor %}
                </select>
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <label for="report_type" class="form-label">{{ entry_report_type }}</label>
                <select name="report_type" id="report_type" class="form-control">
                  <option value="summary"{% if report_type == 'summary' %} selected{% endif %}>{{ text_summary_report }}</option>
                  <option value="detailed"{% if report_type == 'detailed' %} selected{% endif %}>{{ text_detailed_report }}</option>
                  <option value="compliance"{% if report_type == 'compliance' %} selected{% endif %}>{{ text_compliance_report }}</option>
                </select>
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <label for="tax_type" class="form-label">{{ entry_tax_type }}</label>
                <select name="tax_type" id="tax_type" class="form-control">
                  <option value="all"{% if tax_type == 'all' %} selected{% endif %}>{{ text_all_taxes }}</option>
                  <option value="vat"{% if tax_type == 'vat' %} selected{% endif %}>{{ text_vat_only }}</option>
                  <option value="income"{% if tax_type == 'income' %} selected{% endif %}>{{ text_income_tax_only }}</option>
                </select>
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                  <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search me-2"></i>{{ button_filter }}
                  </button>
                </div>
              </div>
            </div>
          </div>
          <div class="row mt-3">
            <div class="col-md-12">
              <div class="form-check">
                <input class="form-check-input" type="checkbox" name="include_analysis" id="include_analysis"{% if include_analysis %} checked{% endif %}>
                <label class="form-check-label" for="include_analysis">
                  {{ text_include_compliance_analysis }}
                </label>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>

    <!-- Annual Tax Report Content -->
    {% if tax_data %}
    <div class="annual-tax-container">
      <div class="tax-header">
        <h2>{{ heading_title }}</h2>
        <p class="text-muted">{{ text_tax_year }}: {{ tax_data.year }} - {{ text_report_type }}: {{ report_type_text }}</p>
      </div>

      <!-- Tax Summary Cards -->
      <div class="tax-summary-cards">
        <div class="tax-card card-vat vat">
          <h4>{{ text_vat_liability }}</h4>
          <div class="amount">{{ tax_data.summary.vat_liability_formatted }}</div>
          <div class="percentage">{{ tax_data.summary.vat_percentage }}% {{ text_of_total }}</div>
        </div>
        <div class="tax-card card-income income">
          <h4>{{ text_income_tax_liability }}</h4>
          <div class="amount">{{ tax_data.summary.income_tax_liability_formatted }}</div>
          <div class="percentage">{{ tax_data.summary.income_tax_percentage }}% {{ text_of_total }}</div>
        </div>
        <div class="tax-card card-total total">
          <h4>{{ text_total_tax_liability }}</h4>
          <div class="amount">{{ tax_data.summary.total_tax_liability_formatted }}</div>
          <div class="percentage">100% {{ text_total }}</div>
        </div>
        <div class="tax-card card-compliance compliance">
          <h4>{{ text_compliance_score }}</h4>
          <div class="amount">{{ tax_data.summary.compliance_score }}%</div>
          <div class="percentage">
            {% if tax_data.summary.compliance_score >= 95 %}
            <span class="text-success">{{ text_excellent }}</span>
            {% elseif tax_data.summary.compliance_score >= 80 %}
            <span class="text-warning">{{ text_good }}</span>
            {% else %}
            <span class="text-danger">{{ text_needs_attention }}</span>
            {% endif %}
          </div>
        </div>
      </div>

      <!-- Tax Details Table -->
      <div class="table-responsive">
        <table class="tax-table" id="annual-tax-table">
          <thead>
            <tr>
              <th>{{ text_tax_category }}</th>
              <th>{{ text_taxable_amount }}</th>
              <th>{{ text_tax_rate }}</th>
              <th>{{ text_calculated_tax }}</th>
              <th>{{ text_paid_tax }}</th>
              <th>{{ text_balance }}</th>
              <th>{{ text_compliance_status }}</th>
              <th>{{ text_due_date }}</th>
            </tr>
          </thead>
          <tbody>
            {% for item in tax_data.items %}
            <tr data-tax-id="{{ item.tax_id }}" data-compliance="{{ item.compliance_status }}">
              <td>
                <strong>{{ item.category_name }}</strong>
                <br>
                <small class="text-muted">{{ item.category_code }}</small>
              </td>
              <td class="amount-cell">{{ item.taxable_amount_formatted }}</td>
              <td>{{ item.tax_rate }}%</td>
              <td class="amount-cell">{{ item.calculated_tax_formatted }}</td>
              <td class="amount-cell tax-paid">{{ item.paid_tax_formatted }}</td>
              <td class="amount-cell {% if item.balance > 0 %}tax-liability{% elseif item.balance < 0 %}tax-refund{% endif %}">
                {{ item.balance_formatted }}
              </td>
              <td>
                <span class="compliance-indicator compliance-{{ item.compliance_status }}"></span>
                {{ item.compliance_text }}
              </td>
              <td>
                <small>{{ item.due_date_formatted }}</small>
                {% if item.is_overdue %}
                <br><span class="badge bg-danger">{{ text_overdue }}</span>
                {% endif %}
              </td>
            </tr>
            {% endfor %}
          </tbody>
          <tfoot>
            <tr class="table-dark">
              <th>{{ text_total }}</th>
              <th class="amount-cell">{{ tax_data.totals.taxable_amount_formatted }}</th>
              <th>-</th>
              <th class="amount-cell">{{ tax_data.totals.calculated_tax_formatted }}</th>
              <th class="amount-cell">{{ tax_data.totals.paid_tax_formatted }}</th>
              <th class="amount-cell"><strong>{{ tax_data.totals.balance_formatted }}</strong></th>
              <th colspan="2"></th>
            </tr>
          </tfoot>
        </table>
      </div>

      <!-- Compliance Violations Alert -->
      {% if compliance_analysis.violations_count > 0 %}
      <div class="alert alert-danger mt-4">
        <h5><i class="fas fa-exclamation-triangle me-2"></i>{{ text_compliance_violations_alert }}</h5>
        <p>{{ text_violations_found }}: <strong>{{ compliance_analysis.violations_count }}</strong></p>
        <p>{{ text_total_penalty_risk }}: <strong>{{ compliance_analysis.total_penalty_risk_formatted }}</strong></p>
        <button type="button" class="btn btn-outline-danger btn-sm" onclick="showComplianceAnalysis()">
          {{ text_view_violations_details }}
        </button>
      </div>
      {% endif %}

      <!-- ETA Integration Status -->
      <div class="eta-integration">
        <div class="eta-status">
          <h5><i class="fas fa-link me-2"></i>{{ text_eta_integration_status }}</h5>
          <span class="eta-badge eta-{{ eta_status }}">{{ eta_status_text }}</span>
        </div>
        <div class="row">
          <div class="col-md-6">
            <p><strong>{{ text_last_sync }}:</strong> {{ eta_last_sync }}</p>
            <p><strong>{{ text_sync_status }}:</strong> {{ eta_sync_status }}</p>
          </div>
          <div class="col-md-6">
            <p><strong>{{ text_submitted_returns }}:</strong> {{ eta_submitted_returns }}</p>
            <p><strong>{{ text_pending_submissions }}:</strong> {{ eta_pending_submissions }}</p>
          </div>
        </div>
        {% if eta_status != 'connected' %}
        <button type="button" class="btn btn-primary btn-sm" onclick="connectToETA()">
          <i class="fas fa-plug me-2"></i>{{ text_connect_to_eta }}
        </button>
        {% else %}
        <button type="button" class="btn btn-success btn-sm" onclick="syncWithETA()">
          <i class="fas fa-sync me-2"></i>{{ text_sync_with_eta }}
        </button>
        {% endif %}
      </div>
    </div>
    {% else %}
    <div class="alert alert-info">
      <i class="fas fa-info-circle me-2"></i>
      {{ text_no_data }}
    </div>
    {% endif %}
  </div>
</div>

<script>
// Enterprise Grade Plus JavaScript for Annual Tax Report
class AnnualTaxReportManager {
    constructor() {
        this.initializeTooltips();
        this.initializeFormValidation();
        this.initializeKeyboardShortcuts();
        this.initializeAutoSave();
        this.initializeDataTable();
        this.initializeComplianceAnalysis();
        this.initializeETAIntegration();
    }

    initializeTooltips() {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }

    initializeFormValidation() {
        const form = document.getElementById('annual-tax-filter-form');
        if (form) {
            form.addEventListener('submit', this.validateForm.bind(this));
        }
    }

    initializeKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case 'g':
                        e.preventDefault();
                        this.generateAnnualTaxReport();
                        break;
                    case 'e':
                        e.preventDefault();
                        this.showExportMenu();
                        break;
                    case 'p':
                        e.preventDefault();
                        this.printAnnualTaxReport();
                        break;
                    case 'c':
                        e.preventDefault();
                        this.showComplianceAnalysis();
                        break;
                    case 't':
                        e.preventDefault();
                        this.connectToETA();
                        break;
                }
            }
        });
    }

    initializeAutoSave() {
        const inputs = document.querySelectorAll('#annual-tax-filter-form input, #annual-tax-filter-form select');
        inputs.forEach(input => {
            input.addEventListener('change', () => {
                this.saveFormState();
            });
        });
        this.loadFormState();
    }

    initializeDataTable() {
        const table = document.getElementById('annual-tax-table');
        if (table && typeof $ !== 'undefined' && $.fn.DataTable) {
            $(table).DataTable({
                responsive: true,
                pageLength: 25,
                order: [[5, 'desc']], // Sort by balance
                columnDefs: [
                    { targets: [1, 3, 4, 5], className: 'text-end' },
                    { targets: [6], orderable: false }
                ],
                language: {
                    url: '{{ direction == "rtl" ? "/view/javascript/jquery/datatables-ar.json" : "/view/javascript/jquery/datatables-en.json" }}'
                }
            });
        }
    }

    initializeComplianceAnalysis() {
        // Highlight compliance violations
        const violationRows = document.querySelectorAll('[data-compliance="violation"]');
        violationRows.forEach(row => {
            row.style.backgroundColor = '#fff5f5';
            row.style.borderLeft = '4px solid var(--danger-color)';
        });

        const warningRows = document.querySelectorAll('[data-compliance="warning"]');
        warningRows.forEach(row => {
            row.style.backgroundColor = '#fffbf0';
            row.style.borderLeft = '4px solid var(--warning-color)';
        });
    }

    initializeETAIntegration() {
        // Check ETA connection status periodically
        setInterval(() => {
            this.checkETAStatus();
        }, 300000); // Check every 5 minutes
    }

    validateForm(e) {
        e.preventDefault();
        const year = document.getElementById('year').value;

        if (!year) {
            this.showAlert('{{ error_year_required }}', 'danger');
            return false;
        }

        const currentYear = new Date().getFullYear();
        if (parseInt(year) > currentYear + 1) {
            this.showAlert('{{ warning_future_year }}', 'warning');
        }

        this.generateAnnualTaxReport();
        return true;
    }

    generateAnnualTaxReport() {
        const form = document.getElementById('annual-tax-filter-form');
        const formData = new FormData(form);

        // Show loading state
        this.showLoadingState(true);

        fetch('{{ generate_url }}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            this.showLoadingState(false);
            if (data.success) {
                this.showAlert('{{ text_success_generation }}', 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                this.showAlert(data.error || '{{ error_generate }}', 'danger');
            }
        })
        .catch(error => {
            this.showLoadingState(false);
            this.showAlert('{{ error_generate }}: ' + error.message, 'danger');
        });
    }

    exportAnnualTaxReport(format) {
        const params = new URLSearchParams({
            format: format,
            year: document.getElementById('year').value,
            report_type: document.getElementById('report_type').value,
            tax_type: document.getElementById('tax_type').value,
            include_analysis: document.getElementById('include_analysis').checked ? '1' : '0'
        });

        this.showAlert('{{ text_exporting }}...', 'info');
        window.open('{{ export_url }}&' + params.toString(), '_blank');
    }

    printAnnualTaxReport() {
        window.print();
    }

    showComplianceAnalysis() {
        const violationRows = document.querySelectorAll('[data-compliance="violation"]');
        const warningRows = document.querySelectorAll('[data-compliance="warning"]');
        const totalIssues = violationRows.length + warningRows.length;

        if (totalIssues > 0) {
            this.showAlert('{{ text_compliance_issues_found }}: ' + violationRows.length + ' {{ text_violations }}, ' + warningRows.length + ' {{ text_warnings }}', 'warning');
            // Scroll to first violation
            if (violationRows.length > 0) {
                violationRows[0].scrollIntoView({ behavior: 'smooth', block: 'center' });
            } else if (warningRows.length > 0) {
                warningRows[0].scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        } else {
            this.showAlert('{{ text_no_compliance_issues }}', 'success');
        }
    }

    connectToETA() {
        this.showAlert('{{ text_connecting_to_eta }}...', 'info');
        // ETA connection implementation
        setTimeout(() => {
            this.showAlert('{{ text_eta_connection_successful }}', 'success');
        }, 2000);
    }

    syncWithETA() {
        this.showAlert('{{ text_syncing_with_eta }}...', 'info');
        // ETA sync implementation
        setTimeout(() => {
            this.showAlert('{{ text_eta_sync_successful }}', 'success');
        }, 3000);
    }

    checkETAStatus() {
        // Check ETA status implementation
        fetch('{{ eta_status_url }}', {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.status !== 'connected') {
                this.showAlert('{{ text_eta_connection_lost }}', 'warning');
            }
        })
        .catch(error => {
            console.log('ETA status check failed:', error);
        });
    }

    showExportMenu() {
        const exportButton = document.querySelector('.dropdown-toggle');
        if (exportButton) {
            exportButton.click();
        }
    }

    showLoadingState(show) {
        const generateBtn = document.querySelector('button[onclick="generateAnnualTaxReport()"]');
        if (generateBtn) {
            if (show) {
                generateBtn.disabled = true;
                generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> {{ text_generating }}';
            } else {
                generateBtn.disabled = false;
                generateBtn.innerHTML = '<i class="fas fa-calculator me-2"></i> {{ button_generate }}';
            }
        }
    }

    showAlert(message, type) {
        const alertContainer = document.createElement('div');
        alertContainer.className = `alert alert-${type} alert-dismissible fade show`;
        alertContainer.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'}"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        const container = document.querySelector('#content .container-fluid');
        container.insertBefore(alertContainer, container.firstChild);

        setTimeout(() => {
            alertContainer.remove();
        }, 5000);
    }

    saveFormState() {
        const formData = new FormData(document.getElementById('annual-tax-filter-form'));
        const state = Object.fromEntries(formData);
        localStorage.setItem('annual_tax_form_state', JSON.stringify(state));
    }

    loadFormState() {
        const savedState = localStorage.getItem('annual_tax_form_state');
        if (savedState) {
            const state = JSON.parse(savedState);
            Object.keys(state).forEach(key => {
                const element = document.getElementById(key);
                if (element) {
                    if (element.type === 'checkbox') {
                        element.checked = state[key] === 'on';
                    } else {
                        element.value = state[key];
                    }
                }
            });
        }
    }
}

// Global functions for backward compatibility
function generateAnnualTaxReport() {
    annualTaxReportManager.generateAnnualTaxReport();
}

function exportAnnualTaxReport(format) {
    annualTaxReportManager.exportAnnualTaxReport(format);
}

function printAnnualTaxReport() {
    annualTaxReportManager.printAnnualTaxReport();
}

function showComplianceAnalysis() {
    annualTaxReportManager.showComplianceAnalysis();
}

function connectToETA() {
    annualTaxReportManager.connectToETA();
}

function syncWithETA() {
    annualTaxReportManager.syncWithETA();
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.annualTaxReportManager = new AnnualTaxReportManager();
});
</script>

{{ footer }}
