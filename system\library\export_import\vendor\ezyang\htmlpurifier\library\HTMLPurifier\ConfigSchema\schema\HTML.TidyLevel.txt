HTML.TidyLevel
TYPE: string
VERSION: 2.0.0
DEFAULT: 'medium'
--DESCRIPTION--

<p>General level of cleanliness the Tidy module should enforce.
There are four allowed values:</p>
<dl>
    <dt>none</dt>
    <dd>No extra tidying should be done</dd>
    <dt>light</dt>
    <dd>Only fix elements that would be discarded otherwise due to
    lack of support in doctype</dd>
    <dt>medium</dt>
    <dd>Enforce best practices</dd>
    <dt>heavy</dt>
    <dd>Transform all deprecated elements and attributes to standards
    compliant equivalents</dd>
</dl>

--ALLOWED--
'none', 'light', 'medium', 'heavy'
--# vim: et sw=4 sts=4
