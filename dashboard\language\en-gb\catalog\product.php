<?php
// Heading
$_['heading_title']          = 'Products';

// Text
$_['text_success']           = 'Success: You have modified products!';
$_['text_success_update']    = 'Success: You have updated categories!';
$_['text_list']              = 'Product List';
$_['text_add']               = 'Add Product';
$_['text_edit']              = 'Edit Product';
$_['text_filter']            = 'Filter';
$_['text_plus']              = '+';
$_['text_minus']             = '-';
$_['text_default']           = 'Default';
$_['text_option']            = 'Option';
$_['text_option_value']      = 'Option Value';
$_['text_percent']           = 'Percentage';
$_['text_amount']            = 'Fixed Amount';
$_['text_keyword']           = 'Do not use spaces, instead replace spaces with - and make sure the SEO URL is globally unique.';
$_['text_base_unit']         = 'Base Unit';
$_['text_additional_unit']   = 'Additional Unit';
$_['text_all_units']         = 'All Units';
$_['text_all_categories']    = 'All Categories';
$_['text_na']                = 'N/A';
$_['text_all']               = 'All';
$_['text_yes']               = 'Yes';
$_['text_no']                = 'No';
$_['text_enabled']           = 'Enabled';
$_['text_disabled']          = 'Disabled';
$_['text_select']            = 'Select';
$_['text_select_unit']       = 'Select Unit';
$_['text_select_branch']     = 'Select Branch';
$_['text_no_units']          = 'No Units';
$_['text_no_option']         = 'No Option';
$_['text_none']              = 'None';
$_['text_no_barcode']        = 'No barcode has been set yet.';
$_['text_no_movements']      = 'No inventory movements for this product.';
$_['text_no_purchase_history'] = 'No purchase history for this product.';
$_['text_no_cost_history']    = 'No cost history for this product.';
$_['text_no_supplier_pricing'] = 'No supplier pricing information for this product.';
$_['text_no_orders']          = 'No orders for this product.';
$_['text_free']               = 'Free';
$_['text_price_applied']      = 'Calculated price has been applied successfully.';
$_['text_adjustment_applied'] = 'Inventory adjustment has been applied successfully.';
$_['text_movement_success']   = 'Inventory movement has been added successfully.';

// Inventory Manager
$_['text_inventory_levels']      = 'Inventory Levels';
$_['text_recent_movements']      = 'Recent Movements';
$_['text_inventory_adjustment']  = 'Inventory Adjustment';
$_['text_adjustment_details']    = 'Adjustment Details';
$_['text_financial_impact']      = 'Financial Impact';
$_['text_gl_account_impact']     = 'GL Account Impact';
$_['text_inventory_account']     = 'Inventory Account';
$_['text_contra_account']        = 'Contra Account';
$_['text_inventory_adjustment_account'] = 'Inventory Adjustment Account';
$_['text_no_accounting_impact']  = 'No accounting impact';
$_['text_preview_journal_entry'] = 'Preview Journal Entry';
$_['text_account']               = 'Account';
$_['text_debit']                 = 'Debit';
$_['text_credit']                = 'Credit';
$_['text_confirm_adjustment']    = 'I confirm this adjustment is correct and understand it will affect inventory valuation';
$_['text_edit_cost']             = 'Edit Cost';
$_['text_cost_details']          = 'Cost Details';
$_['text_current_cost']          = 'Current Cost';
$_['text_new_cost']              = 'New Cost';
$_['text_cost_change_reason']    = 'Cost Change Reason';
$_['text_market_price_change']   = 'Market Price Change';
$_['text_supplier_price_change'] = 'Supplier Price Change';
$_['text_data_correction']       = 'Data Correction';
$_['text_other_reason']          = 'Other Reason';
$_['text_custom_reason']         = 'Custom Reason';
$_['text_detailed_notes']        = 'Detailed Notes';
$_['text_confirm_cost_change']   = 'I confirm this cost change is correct and understand it will affect inventory valuation';
$_['text_current_quantity']      = 'Current Quantity';
$_['text_new_quantity']          = 'New Quantity';
$_['text_quantity_change']       = 'Quantity Change';
$_['text_stock_value_change']    = 'Stock Value Change';
$_['text_current_total_value']   = 'Current Total Value';
$_['text_new_total_value']       = 'New Total Value';
$_['text_value_change']          = 'Value Change';
$_['text_update_sales_prices']   = 'Update Sales Prices';
$_['text_update_based_on_new_cost'] = 'Update prices based on new cost';
$_['text_profit_margin_percentage'] = 'Profit Margin Percentage';
$_['text_current_base_price']    = 'Current Base Price';
$_['text_calculated_new_price']  = 'Calculated New Price';
$_['text_inventory_valuation_account'] = 'Inventory Valuation Account';
$_['text_branch']                = 'Branch';
$_['text_unit']                  = 'Unit';
$_['text_quantity_on_hand']      = 'Quantity on Hand';
$_['text_totals']                = 'Totals';
$_['text_view_all_movements']    = 'View All Movements';
$_['text_movement_history']      = 'Movement History';
$_['text_cost_history']          = 'Cost History';
$_['text_cost_change']           = 'Cost Change';
$_['text_change_reason']         = 'Change Reason';
$_['text_notes']                 = 'Notes';
$_['text_warning_insufficient_stock'] = 'Warning: Insufficient stock for the requested reduction';
$_['text_add_stock']             = 'Add Stock';
$_['text_remove_stock']          = 'Remove Stock';
$_['text_stock_count']           = 'Stock Count';
$_['text_quantity_to_add']       = 'Quantity to Add';
$_['text_quantity_to_remove']    = 'Quantity to Remove';
$_['text_actual_quantity']       = 'Actual Quantity';
$_['text_reason_stock_count']    = 'Stock Count';
$_['text_reason_damaged']        = 'Damaged Goods';
$_['text_reason_expired']        = 'Expired Goods';
$_['text_reason_correction']     = 'Data Correction';
$_['text_reason_production']     = 'Production';
$_['text_reason_initial_stock']  = 'Initial Stock';
$_['text_reason_other']          = 'Other';
$_['text_optional']              = 'Optional';
$_['text_currency']              = '$';
$_['text_currency_symbol']       = '$';
$_['text_loading']               = 'Loading...';
$_['text_no_inventory']          = 'No inventory records found';
$_['text_adjustment_increase']   = 'Adjustment (Increase)';
$_['text_adjustment_decrease']   = 'Adjustment (Decrease)';
$_['text_transfer_in']           = 'Transfer In';
$_['text_transfer_out']          = 'Transfer Out';
$_['text_initial_stock']         = 'Initial Stock';
$_['text_return_in']             = 'Return In';
$_['text_return_out']            = 'Return Out';
$_['text_scrap']                 = 'Scrap';
$_['text_production']            = 'Production';
$_['text_consumption']           = 'Consumption';
$_['text_cost_adjustment']       = 'Cost Adjustment';

$_['entry_movement_type']        = 'Movement Type';
$_['entry_reason']               = 'Reason';
$_['entry_custom_reason']        = 'Custom Reason';
$_['entry_direct_cost']          = 'Direct Cost';
$_['entry_document_reference']   = 'Document Reference';
$_['entry_consignment']          = 'Consignment';
$_['entry_cost']                 = 'Cost';

$_['button_add_movement']        = 'Add Movement';
$_['button_adjust']              = 'Adjust';
$_['button_edit_cost']           = 'Edit Cost';

$_['help_direct_cost']           = 'The direct cost of the incoming inventory (purchase price)';

$_['error_quantity']             = 'Quantity must be greater than zero';
$_['error_cost']                 = 'Cost must be greater than zero for incoming inventory';
$_['error_custom_reason']        = 'Custom reason is required when "Other" is selected';
$_['error_notes']                = 'Notes are required';
$_['error_movement']             = 'Error processing inventory movement';
$_['error_missing_data']         = 'Missing required data for inventory movement';
$_['text_price_updated']      = 'Supplier price has been updated successfully.';
$_['text_price_added']        = 'Supplier price has been added successfully.';
$_['text_price_deleted']      = 'Supplier price has been deleted successfully.';
$_['text_confirm_delete']     = 'Are you sure you want to delete?';
$_['text_error_loading']      = 'Error loading data.';
$_['text_error_saving']       = 'Error saving data.';
$_['text_error_deleting']     = 'Error deleting data.';
$_['text_conversion_result']  = 'Conversion result will appear here when units and quantity are selected.';
$_['text_from_unit']          = 'From Unit';
$_['text_to_unit']            = 'To Unit';
$_['text_quantity']           = 'Quantity';
$_['text_from_date']          = 'From Date';
$_['text_to_date']            = 'To Date';
$_['text_seo_permalink']      = 'SEO Permalink';

// Help text
$_['text_general_info_help']  = 'General Information Help';
$_['text_general_help_intro'] = 'Use this section to enter basic product information in all available languages.';
$_['text_product_name']       = 'Product Name';
$_['text_product_name_help']  = 'Enter a clear, concise name that helps customers understand what you\'re offering.';
$_['text_product_description'] = 'Product Description';
$_['text_product_description_help'] = 'Write a detailed description explaining the product\'s features and specifications.';
$_['text_meta_data']           = 'Meta Data';
$_['text_meta_data_help']      = 'Helps improve product visibility in search engines.';
$_['text_seo_url']             = 'SEO Friendly URL';
$_['text_seo_url_help']        = 'Use keywords related to the product to improve its ranking in search results.';
$_['text_general_help_tip']    = 'Products with complete and accurate information are more appealing to customers and easier to find.';

$_['text_data_help']           = 'Technical Data Help';
$_['text_data_help_intro']     = 'Use this section to manage technical information and classifications for the product.';
$_['text_identifiers']         = 'Identifiers';
$_['text_identifiers_help']    = 'Use identifiers like SKU, UPC, and EAN to easily identify and track the product.';
$_['text_categories_filters']  = 'Categories & Filters';
$_['text_categories_filters_help'] = 'Linking the product to appropriate categories and filters makes it easier for customers to find.';
$_['text_stock_options']       = 'Stock Options';
$_['text_stock_options_help']  = 'Determine how to manage product inventory and what happens when it runs out.';
$_['text_dimensions_weight_help'] = 'Dimensions & Weight';
$_['text_dimensions_weight_details'] = 'Important for calculating shipping costs and managing storage space.';
$_['text_data_help_tip']       = 'Accurate data makes it easier to manage products, track inventory, and organize your store better.';

$_['text_dimensions_weight']  = 'Dimensions & Weight';
$_['text_length']             = 'Length';
$_['text_width']              = 'Width';
$_['text_height']             = 'Height';

// Units
$_['text_units_help']         = 'Units Help';
$_['text_units_help_intro']   = 'Manage product measurement units and define how to convert between them.';
$_['text_base_unit']          = 'Base Unit';
$_['text_base_unit_help']     = 'The primary unit for the product, used as the basis for all conversions.';
$_['text_additional_unit']    = 'Additional Unit';
$_['text_additional_unit_help'] = 'Alternative units for the product that can be converted from the base unit.';
$_['text_conversion_factor']  = 'Conversion Factor';
$_['text_conversion_factor_help'] = 'The number used to convert between the base unit and additional units.';
$_['text_unit_converter']     = 'Unit Converter';
$_['text_unit_converter_help'] = 'A tool to test conversion between different product units.';
$_['text_base_unit_warning']  = 'Cannot remove the base unit. You must set another unit as the base unit first.';
$_['text_units_info']         = 'Manage product units. You must specify at least one base unit, and you can add additional units with appropriate conversion factors.';
$_['text_unit_conversion']    = 'Unit Conversion';

// Inventory
$_['text_inventory_help']     = 'Inventory Help';
$_['text_inventory_help_intro'] = 'Manage product inventory across different branches and units.';
$_['text_inventory_quantity'] = 'Inventory Quantity';
$_['text_inventory_quantity_help'] = 'The total quantity available in stock.';
$_['text_available_quantity'] = 'Available Quantity';
$_['text_available_quantity_help'] = 'The quantity available for online sales that hasn\'t been reserved for orders.';
$_['text_average_cost']       = 'Average Cost';
$_['text_average_cost_help']  = 'The average cost of purchasing the unit, used to calculate inventory value and profit margin.';
$_['text_quick_adjustment']   = 'Quick Adjustment';
$_['text_quick_adjustment_help'] = 'Make rapid adjustments to inventory such as adding new deliveries or recording losses.';
$_['text_inventory_help_tip'] = 'Careful monitoring of inventory helps avoid stockouts and better manage capital.';
$_['text_inventory_info']     = 'Manage product inventory across different branches and units. Available quantity is what can be sold online.';

// Pricing
$_['text_pricing_help']       = 'Pricing Help';
$_['text_pricing_help_intro'] = 'Manage product prices for different units and customer groups.';
$_['text_base_price']         = 'Base Price';
$_['text_base_price_help']    = 'The regular price of the product before any discounts or special offers.';
$_['text_special_price']      = 'Special Price';
$_['text_special_price_help'] = 'A temporary reduced price for the product shown to customers.';
$_['text_wholesale_price']    = 'Wholesale Price';
$_['text_wholesale_price_help'] = 'The price offered to wholesale customers or when purchasing large quantities.';
$_['text_profit_margin']      = 'Profit Margin';
$_['text_profit_margin_help'] = 'The difference between the selling price and average cost, expressed as a percentage.';
$_['text_price_calculator']   = 'Price Calculator';
$_['text_price_calculator_help'] = 'A tool to help calculate product prices based on cost and target profit margin.';
$_['text_pricing_help_tip']   = 'Smart pricing strategy helps increase sales and improve profitability.';
$_['text_pricing_info']       = 'Manage product prices for different units and customer groups.';

// Barcode
$_['text_barcode_help']       = 'Barcode Help';
$_['text_barcode_help_intro'] = 'Add and manage product barcodes to facilitate sales and inventory operations.';
$_['text_barcode_types']      = 'Barcode Types';
$_['text_barcode_types_help'] = 'Different types of barcodes like EAN, UPC, ISBN, and others used in various industries.';
$_['text_barcode_unit']       = 'Barcode Unit';
$_['text_barcode_unit_help']  = 'Link the barcode to a specific product unit, allowing scanning the product to automatically identify the unit.';
$_['text_barcode_option']     = 'Barcode Option';
$_['text_barcode_option_help'] = 'Link the barcode to a specific product option like color or size.';
$_['text_barcode_preview']    = 'Barcode Preview';
$_['text_barcode_preview_help'] = 'Preview the barcode appearance before printing or using it.';
$_['text_barcode_help_tip']   = 'Accurate barcodes increase point-of-sale efficiency and reduce inventory errors.';
$_['text_barcode_info']       = 'Add multiple barcodes to the product. You can link a specific barcode to a unit or option.';

// Options
$_['text_options_help']       = 'Options Help';
$_['text_options_help_intro'] = 'Manage product options such as colors, sizes, and selectable specifications.';
$_['text_option_types']       = 'Option Types';
$_['text_option_types_help']  = 'Different types such as dropdown, radio button, checkbox, text, and others.';
$_['text_option_values']      = 'Option Values';
$_['text_option_values_help'] = 'Available values for each option, such as red, blue, green for colors.';
$_['text_option_pricing']     = 'Option Pricing';
$_['text_option_pricing_help'] = 'Adjust the product price based on selected options, either by adding, subtracting, or setting a fixed price.';
$_['text_option_inventory']   = 'Option Inventory';
$_['text_option_inventory_help'] = 'Track inventory for each option separately, such as stock available for each color and size.';
$_['text_option_unit']        = 'Option Unit';
$_['text_option_unit_help']   = 'Link the option to a specific product unit, helping with inventory and pricing management.';
$_['text_options_help_tip']   = 'Well-organized options improve the shopping experience and increase customer satisfaction.';

// Bundles and Discounts
$_['text_bundles_discounts_help'] = 'Bundles and Discounts Help';
$_['text_bundles_discounts_help_intro'] = 'Create and manage special offers and discounts for the product.';
$_['text_what_are_bundles']   = 'What Are Bundles';
$_['text_bundles_explanation'] = 'Groups of products sold together at a discounted price or with a free product.';
$_['text_bundle_discount_types'] = 'Bundle Discount Types';
$_['text_bundle_discount_types_explanation'] = 'Percentage discount, fixed amount, or free product as part of the bundle.';
$_['text_bundle_products']    = 'Bundle Products';
$_['text_bundle_products_explanation'] = 'Products included in the bundle with the ability to specify quantities and units.';
$_['text_what_are_discounts'] = 'What Are Discounts';
$_['text_discounts_explanation'] = 'Reductions in product price based on purchased quantity or other conditions.';
$_['text_discount_types']     = 'Discount Types';
$_['text_discount_types_explanation'] = 'Buy X get Y free, or buy X get a discount on the entire quantity.';
$_['text_discount_period']    = 'Discount Period';
$_['text_discount_period_explanation'] = 'Set start and end dates for the discount, allowing creation of seasonal offers.';
$_['text_bundles_discounts_help_tip'] = 'Attractive offers encourage customers to buy larger quantities and increase order value.';

// Recommendations
$_['text_recommendation_help'] = 'Recommendation Help';
$_['text_recommendation_help_intro'] = 'Set up recommended products to boost cross-sells and upsells.';
$_['text_upsell']             = 'Upsell';
$_['text_upsell_help']        = 'Better or higher-priced products that can be suggested as alternatives to the current product.';
$_['text_cross_sell']         = 'Cross-sell';
$_['text_cross_sell_help']    = 'Complementary products that can be used with the current product to enhance the customer experience.';
$_['text_recommendation_unit'] = 'Recommendation Unit';
$_['text_recommendation_unit_help'] = 'The suggested unit for the recommended product.';
$_['text_recommendation_discount'] = 'Recommendation Discount';
$_['text_recommendation_discount_help'] = 'Special discount when purchasing the recommended product with the current product.';
$_['text_recommendation_priority'] = 'Recommendation Priority';
$_['text_recommendation_priority_help'] = 'The order in which recommended products appear to the customer.';
$_['text_recommendation_help_tip'] = 'Smart recommendations increase average order value and help customers discover new products.';

// Inventory Movement
$_['text_movement_help']      = 'Inventory Movement Help';
$_['text_movement_help_intro'] = 'Track all changes in product inventory and analyze them.';
$_['text_movement_types']     = 'Movement Types';
$_['text_movement_types_help'] = 'Different types of inventory movements such as purchases, sales, adjustments, and transfers.';
$_['text_movement_filters']   = 'Movement Filters';
$_['text_movement_filters_help'] = 'Filter inventory movements by type, branch, or time period.';
$_['text_movement_stats']     = 'Movement Statistics';
$_['text_movement_stats_help'] = 'Analyze inventory trends such as consumption rate and order frequency.';
$_['text_movement_help_tip']  = 'Regular monitoring of inventory movements helps improve inventory management and detect issues early.';
$_['text_movement_info']      = 'Track all inventory movements for this product, including purchases, sales, adjustments, and transfers.';
$_['text_inventory_statistics'] = 'Inventory Statistics';
$_['text_total_incoming']     = 'Total Incoming';
$_['text_total_outgoing']     = 'Total Outgoing';
$_['text_net_change']         = 'Net Change';
$_['text_current_stock']      = 'Current Stock';
$_['text_movement_by_type']   = 'Movement by Type';
$_['text_stock_trend']        = 'Stock Trend';
$_['text_cost_trend']         = 'Cost Trend';
$_['text_additional_statistics'] = 'Additional Statistics';
$_['text_movement_frequency'] = 'Movement Frequency';
$_['text_stock_by_branch']    = 'Stock by Branch';
$_['text_filter_by_unit']     = 'Filter by Unit';
$_['text_movement_details']   = 'Movement Details';
$_['text_basic_information']  = 'Basic Information';
$_['text_accounting_impact']  = 'Accounting Impact';
$_['text_cost_impact']        = 'Cost Impact';

// Purchases
$_['text_purchase_help']      = 'Purchase Help';
$_['text_purchase_help_intro'] = 'Track product purchases and manage supplier information.';
$_['text_purchase_history_section'] = 'Purchase History';
$_['text_purchase_history_help'] = 'List of all previous purchase orders for the product with details such as quantities, prices, and suppliers.';
$_['text_cost_history_section'] = 'Cost History';
$_['text_cost_history_help']  = 'Track product cost changes over time, helping to analyze trends.';
$_['text_supplier_pricing_section'] = 'Supplier Pricing';
$_['text_supplier_pricing_help'] = 'Compare product prices from different suppliers to make better purchasing decisions.';
$_['text_purchase_help_tip']  = 'Accurate purchase data helps in negotiating with suppliers and improving just-in-time purchasing.';
$_['text_purchase_info']      = 'Track all purchases, prices, and supplier data for this product.';

// Orders
$_['text_orders_help']        = 'Orders Help';
$_['text_orders_help_intro']  = 'Analyze product sales and performance.';
$_['text_orders_list_section'] = 'Orders List';
$_['text_orders_list_help']   = 'All orders including this product with information such as quantity, price, and customer.';
$_['text_order_statistics_section'] = 'Order Statistics';
$_['text_order_statistics_help'] = 'Summary of total sales, revenue, and average prices.';
$_['text_sales_trend_section'] = 'Sales Trends';
$_['text_sales_trend_help']   = 'Graphical analysis of product sales over time.';
$_['text_orders_help_tip']    = 'Analyzing sales data helps understand customer behavior and predict future demand.';
$_['text_orders_info']        = 'View all orders containing this product.';

// Images
$_['text_images_help']        = 'Images Help';
$_['text_images_help_intro']  = 'Manage main and additional product images.';
$_['text_main_image']         = 'Main Image';
$_['text_main_image_details'] = 'The primary image that appears on the product page and product listings.';
$_['text_additional_images']  = 'Additional Images';
$_['text_additional_images_details'] = 'Extra images showing different aspects of the product or its uses.';
$_['text_sort_order']         = 'Sort Order';
$_['text_sort_order_details'] = 'The order in which additional images appear on the product page.';
$_['text_images_help_tip']    = 'High-quality and multiple images increase customer confidence and reduce return rates.';
$_['text_main_image_help']    = 'Choose a clear and distinctive image that represents the product well.';

// Product Recommendations
$_['tab_recommendation']          = 'Recommendations';
$_['tab_upsell']                  = 'Upsell Products';
$_['tab_cross_sell']              = 'Cross-sell Products';
$_['entry_product']               = 'Product';
$_['entry_unit']                  = 'Unit';
$_['entry_customer_group']        = 'Customer Group';
$_['entry_priority']              = 'Priority';
$_['entry_discount_type']         = 'Discount Type';
$_['entry_discount_value']        = 'Discount Value';
$_['text_all_customers']          = 'All Customers';
$_['button_upsell_add']           = 'Add Upsell Product';
$_['button_cross_sell_add']       = 'Add Cross-sell Product';
$_['error_upsell_product']        = 'Upsell product is required!';
$_['error_upsell_unit']           = 'Upsell product unit is required!';
$_['error_cross_sell_product']    = 'Cross-sell product is required!';
$_['error_cross_sell_unit']       = 'Cross-sell product unit is required!';
$_['help_recommendation']         = 'Product recommendations help increase sales by suggesting related or complementary items to customers. You can set up upsells (higher-priced alternatives) and cross-sells (complementary products).';

// Bundles and Discounts
$_['tab_bundle']                  = 'Bundles';
$_['text_bundles']                = 'Bundles';
$_['text_discounts']              = 'Discounts';
$_['entry_bundle_name']           = 'Bundle Name';
$_['entry_bundle_discount_type']  = 'Discount Type';
$_['entry_bundle_discount_value'] = 'Discount Value';
$_['entry_bundle_products']       = 'Bundle Products';
$_['entry_discount_name']         = 'Discount Name';
$_['text_buy_x_get_y']            = 'Buy X Get Y';
$_['text_buy_x_get_discount']     = 'Buy X Get Discount';
$_['text_percentage']             = 'Percentage';
$_['text_fixed']                  = 'Fixed Amount';
$_['text_free_product']           = 'Free Product';
$_['entry_buy_quantity']          = 'Buy Quantity';
$_['entry_get_quantity']          = 'Get Quantity';
$_['entry_notes']                 = 'Notes';
$_['entry_date_start']            = 'Start Date';
$_['entry_date_end']              = 'End Date';
$_['button_bundle_add']           = 'Add Bundle';
$_['button_discount_add']         = 'Add Discount';
$_['help_bundle_and_discount']    = 'Use bundles to create multi-product offers, and use discounts to create offers on a single product.';
$_['error_bundle_name']           = 'Bundle name must be between 1 and 255 characters!';
$_['text_action']                 = 'Action';

// Barcode
$_['entry_barcode']               = 'Barcode';
$_['entry_barcode_type']          = 'Barcode Type';
$_['button_barcode_add']          = 'Add Barcode';
$_['text_barcode_help']           = 'Add multiple barcodes to the product. You can link a specific barcode to a unit or option.';
$_['text_barcode_preview']        = 'Barcode Preview';
$_['text_barcode_preview_placeholder'] = 'Barcode preview will appear here. In the actual system, a barcode image will be displayed.';

// Dynamic Pricing
$_['tab_dynamic_pricing']          = 'Dynamic Pricing';
$_['column_rule_name']             = 'Rule Name';
$_['column_rule_type']             = 'Type';
$_['column_rule_value']            = 'Value';
$_['column_rule_condition']        = 'Condition';
$_['column_rule_priority']         = 'Priority';
$_['column_date_start']            = 'Start Date';
$_['column_date_end']              = 'End Date';
$_['column_status']                = 'Status';
$_['column_action']                = 'Action';
$_['entry_rule_name']              = 'Enter Rule Name';
$_['entry_rule_value']             = 'Enter Value';
$_['entry_rule_formula']           = 'Enter Formula';
$_['entry_condition_value']        = 'Enter Condition Value';
$_['text_formula']                 = 'Formula';
$_['text_customer_group']          = 'Customer Group';
$_['text_total_spent']             = 'Total Spent';
$_['text_purchase_history']        = 'Purchase History';
$_['text_time_period']             = 'Time Period';
$_['text_stock_level']             = 'Stock Level';
$_['text_competitor_price']        = 'Competitor Price';
$_['button_rule_add']              = 'Add Rule';
$_['help_rule_value']              = 'For percentage, use values between -100 and 100. For fixed amount, enter the amount.';
$_['help_rule_formula']            = 'Use {price} to refer to the current price. Example: {price} * 0.9 for a 10% discount.';
$_['help_condition_value']         = 'Enter the condition value based on the selected condition type.';
$_['help_priority']                = 'Rules are applied in priority order (lower numbers first).';

// Units and Conversions
$_['tab_units']                    = 'Units';
$_['entry_unit']                   = 'Unit';
$_['entry_unit_type']              = 'Unit Type';
$_['entry_conversion_factor']      = 'Conversion Factor';
$_['entry_action']                 = 'Action';
$_['button_unit_add']              = 'Add Unit';
$_['text_base_unit_info']          = 'The base unit is the primary unit for the product. All other units are calculated based on the base unit using the conversion factor.';
$_['text_conversion_result']       = 'Conversion result will appear here when units and quantity are selected.';
$_['text_base_unit_required']      = 'At least one base unit must be specified for the product.';
$_['text_cannot_remove_base_unit'] = 'Cannot remove the base unit. You must set another unit as the base unit first.';

// Inventory
$_['tab_inventory']                = 'Inventory';
$_['entry_branch']                 = 'Branch';
$_['entry_quantity']               = 'Quantity';
$_['entry_quantity_available']     = 'Available Quantity';
$_['entry_average_cost']           = 'Average Cost';
$_['entry_total_value']            = 'Total Value';
$_['text_inventory_help']          = 'Manage product inventory across different branches and units. Available quantity is what can be sold online.';
$_['text_quick_adjustment']        = 'Quick Stock Adjustment';
$_['text_increase']                = 'Increase';
$_['text_decrease']                = 'Decrease';
$_['entry_adjustment_reason']      = 'Adjustment Reason';
$_['button_apply']                 = 'Apply';
$_['text_adjustment_applied']      = 'Stock adjustment applied successfully.';
$_['text_insufficient_stock']      = 'Insufficient stock for the requested reduction.';
$_['text_adjustment_fields_required'] = 'Please fill all required adjustment fields.';
$_['text_quantity_note']           = 'This is the total product quantity. You can manage detailed quantities by unit and branch in the "Inventory" tab.';

// Pricing
$_['tab_pricing']                  = 'Pricing';
$_['entry_base_price']             = 'Base Price';
$_['entry_special_price']          = 'Special Price';
$_['entry_wholesale_price']        = 'Wholesale Price';
$_['entry_half_wholesale_price']   = 'Half Wholesale Price';
$_['entry_custom_price']           = 'Custom Price';
$_['entry_profit_margin']          = 'Profit Margin';
$_['text_pricing_help']            = 'Manage product prices for different units and customer groups.';
$_['text_price_calculator']        = 'Price Calculator';
$_['text_cost']                    = 'Cost';
$_['text_margin']                  = 'Margin';
$_['text_price']                   = 'Price';
$_['button_calculate_price']       = 'Calculate Price';
$_['button_calculate_margin']      = 'Calculate Margin';
$_['button_apply_price']           = 'Apply Price';
$_['text_price_applied']           = 'Calculated price has been applied successfully.';

// Inventory Movement
$_['tab_movement']                 = 'Inventory Movement';
$_['text_movement_help']           = 'Track all inventory movements for this product, including purchases, sales, adjustments, and transfers.';
$_['text_movement_filter']         = 'Filter Movements';
$_['text_filter_by_type']          = 'Filter by Type';
$_['text_filter_by_branch']        = 'Filter by Branch';
$_['text_filter_by_date']          = 'Filter by Date';
$_['text_all_types']               = 'All Types';
$_['text_all_branches']            = 'All Branches';
$_['text_filter_applied']          = 'Filter applied.';
$_['text_filter_reset']            = 'Filter reset.';
$_['text_movement_stats']          = 'Movement Statistics';
$_['text_total_incoming']          = 'Total Incoming';
$_['text_total_outgoing']          = 'Total Outgoing';
$_['text_movement_frequency']      = 'Movement Frequency';
$_['text_purchase']                = 'Purchase';
$_['text_sale']                    = 'Sale';
$_['text_adjustment']              = 'Adjustment';
$_['text_transfer']                = 'Transfer';
$_['button_reset']                 = 'Reset';
$_['column_date_added']            = 'Date Added';
$_['column_type']                  = 'Type';
$_['column_quantity']              = 'Quantity';
$_['column_unit']                  = 'Unit';
$_['column_branch']                = 'Branch';
$_['column_reference']             = 'Reference';
$_['column_user']                  = 'User';
$_['text_no_movements']            = 'No inventory movements for this product.';

// Purchases
$_['tab_purchase']                 = 'Purchases';
$_['text_purchase_history_help']   = 'Track all previous purchases of this product, including prices, quantities, and suppliers.';
$_['text_purchase_history']        = 'Purchase History';
$_['text_cost_history']            = 'Cost History';
$_['text_no_purchase_history']     = 'No purchase history for this product.';
$_['text_no_cost_history']         = 'No cost history for this product.';
$_['text_no_supplier_pricing']     = 'No supplier pricing information for this product.';
$_['text_supplier_price_modal']    = 'A modal will open to add a new supplier price (actual implementation not included in this demo).';
$_['button_add_supplier_price']    = 'Add Supplier Price';
$_['column_po_number']             = 'PO Number';
$_['column_date']                  = 'Date';
$_['column_supplier']              = 'Supplier';
$_['column_unit_price']            = 'Unit Price';
$_['column_receipt_number']        = 'Receipt Number';
$_['column_receipt_date']          = 'Receipt Date';
$_['column_old_cost']              = 'Old Cost';
$_['column_new_cost']              = 'New Cost';
$_['column_change_type']           = 'Change Type';
$_['column_notes']                 = 'Notes';
$_['column_currency']              = 'Currency';
$_['column_min_quantity']          = 'Min Quantity';
$_['column_last_purchase_date']    = 'Last Purchase Date';
$_['column_is_default']            = 'Is Default';
$_['column_price']                 = 'Price';

// Orders
$_['tab_orders']                   = 'Orders';
$_['text_orders_help']             = 'View all orders containing this product.';
$_['text_order_statistics']        = 'Order Statistics';
$_['text_total_sold']              = 'Total Sold';
$_['text_total_revenue']           = 'Total Revenue';
$_['text_average_price']           = 'Average Price';
$_['text_sales_trend']             = 'Sales Trend';
$_['text_no_orders']               = 'No orders for this product.';
$_['column_order_id']              = 'Order ID';
$_['column_customer']              = 'Customer';
$_['column_status']                = 'Status';
$_['column_total']                 = 'Total';
$_['column_action']                = 'Action';
$_['button_view']                  = 'View';

// Dimensions
$_['entry_dimensions']             = 'Dimensions';
$_['entry_length']                 = 'Length';
$_['entry_width']                  = 'Width';
$_['entry_height']                 = 'Height';
$_['entry_weight']                 = 'Weight';

// Lists and Buttons
$_['button_filter']                = 'Filter';
$_['button_clear']                 = 'Clear Filter';
$_['button_remove']                = 'Remove';
$_['button_save']                  = 'Save';
$_['button_cancel']                = 'Cancel';
$_['button_close']                 = 'Close';
$_['button_add_product']           = 'Add New Product';
$_['button_image_add']             = 'Add Image';
$_['button_option_value_add']      = 'Add Option Value';
$_['button_manage_columns']        = 'Manage Columns';
$_['text_manage_columns']          = 'Manage Columns';
$_['text_export']                  = 'Export';
$_['text_import']                  = 'Import';
$_['button_add_option']            = 'Add Option';

// List
$_['column_name']                  = 'Product Name';
$_['column_model']                 = 'Model';
$_['column_image']                 = 'Image';
$_['column_id']                    = 'ID';
$_['column_egs_code']              = 'EGS Code';
$_['column_stock']                 = 'Stock';
$_['column_stock_online']          = 'Online Stock';
$_['column_base_price']            = 'Base Price';
$_['column_special_price']         = 'Special Price';
$_['column_wholesale_price']       = 'Wholesale Price';
$_['column_half_wholesale_price']  = 'Half Wholesale Price';
$_['column_custom_price']          = 'Custom Price';
$_['column_status']                = 'Status';
$_['column_action']                = 'Action';
$_['entry_category']               = 'Category';
$_['entry_name']                   = 'Product Name';
$_['entry_model']                  = 'Model';
$_['entry_price']                  = 'Price';
$_['entry_quantity']               = 'Quantity';
$_['entry_quantity_min']           = 'Min Quantity';
$_['entry_quantity_max']           = 'Max Quantity';
$_['entry_has_image']              = 'Has Image';
$_['entry_status']                 = 'Status';
$_['entry_filter']                 = 'Filter';

// Inputs
$_['entry_description']            = 'Description';
$_['entry_meta_title']             = 'Meta Tag Title';
$_['entry_meta_keyword']           = 'Meta Tag Keywords';
$_['entry_meta_description']       = 'Meta Tag Description';
$_['entry_store']                  = 'Stores';
$_['entry_keyword']                = 'SEO Keywords';
$_['entry_sku']                    = 'SKU';
$_['entry_upc']                    = 'UPC';
$_['entry_ean']                    = 'EAN';
$_['entry_jan']                    = 'JAN';
$_['entry_isbn']                   = 'ISBN';
$_['entry_mpn']                    = 'MPN';
$_['entry_location']               = 'Location';
$_['entry_shipping']               = 'Requires Shipping';
$_['entry_manufacturer']           = 'Manufacturer';
$_['entry_date_available']         = 'Date Available';
$_['entry_minimum']                = 'Minimum Quantity';
$_['entry_stock_status']           = 'Out Of Stock Status';
$_['entry_tax_class']              = 'Tax Class';
$_['entry_points']                 = 'Points';
$_['entry_option_points']          = 'Points';
$_['entry_subtract']               = 'Subtract Stock';
$_['entry_weight_class']           = 'Weight Class';
$_['entry_image']                  = 'Image';
$_['entry_additional_image']       = 'Additional Images';
$_['entry_customer_group']         = 'Customer Group';
$_['entry_attribute']              = 'Attribute';
$_['entry_attribute_group']        = 'Attribute Group';
$_['entry_text']                   = 'Text';
$_['entry_option']                 = 'Option';
$_['entry_option_value']           = 'Option Value';
$_['entry_required']               = 'Required';
$_['entry_sort_order']             = 'Sort Order';
$_['entry_download']               = 'Downloads';
$_['entry_related']                = 'Related Products';
$_['entry_tag']                    = 'Product Tags';
$_['entry_reward']                 = 'Reward Points';
$_['entry_layout']                 = 'Layout Override';
$_['entry_recurring']              = 'Recurring Profile';
$_['entry_adjustment_type']        = 'Adjustment Type';

// Tabs
$_['tab_general']                  = 'General';
$_['tab_data']                     = 'Data';
$_['tab_image']                    = 'Images';
$_['tab_option']                   = 'Options';

// Help
$_['help_sku']                     = 'Stock Keeping Unit';
$_['help_upc']                     = 'Universal Product Code';
$_['help_ean']                     = 'European Article Number';
$_['help_jan']                     = 'Japanese Article Number';
$_['help_isbn']                    = 'International Standard Book Number';
$_['help_mpn']                     = 'Manufacturer Part Number';
$_['help_manufacturer']            = '(Autocomplete)';
$_['help_minimum']                 = 'Force a minimum ordered amount';
$_['help_stock_status']            = 'Status shown when a product is out of stock';
$_['help_points']                  = 'Number of points needed to buy this item. If you don\'t want this product to be purchased with points leave as 0.';
$_['help_category']                = '(Autocomplete)';
$_['help_filter']                  = '(Autocomplete)';
$_['help_download']                = '(Autocomplete)';
$_['help_related']                 = '(Autocomplete)';
$_['help_tag']                     = 'Comma separated';

// Error
$_['error_warning']                = 'Warning: Please check the form carefully for errors!';
$_['error_permission']             = 'Warning: You do not have permission to modify products!';
$_['error_name']                   = 'Product Name must be greater than 1 and less than 255 characters!';
$_['error_meta_title']             = 'Meta Title must be greater than 1 and less than 255 characters!';
$_['error_model']                  = 'Product Model must be greater than 1 and less than 64 characters!';
$_['error_keyword']                = 'SEO URL already in use!';
$_['error_unique']                 = 'SEO URL must be unique!';
$_['error_unit']                   = 'Please select a unit for the product!';
$_['error_unit_required']          = 'Unit is required!';
$_['error_base_unit_required']     = 'Base unit is required!';
$_['error_branch_required']        = 'Branch is required!';
$_['error_quantity_invalid']       = 'Quantity is invalid!';
$_['error_pricing_required']       = 'Pricing information is required!';
$_['error_base_price_invalid']     = 'Base price is invalid!';
$_['error_quantity_exceeded']      = 'Specified quantity exceeds available stock!';
$_['error_base_unit']              = 'Only one base unit must be specified for the product!';
$_['error_supplier_required']      = 'Supplier is required!';
$_['error_price_required']         = 'Price is required!';
$_['error_price_id']               = 'Price ID is required!';
$_['error_missing_movement_data']  = 'Inventory movement data is missing!';
$_['error_movement_failed']        = 'Inventory movement addition failed!';
$_['error_product']                = 'Product not found!';
$_['error_reason_required']        = 'Adjustment reason is required!';

// Inventory and cost specific texts
$_['text_inventory_info'] = 'Inventory information and quantity management by branch and unit';
$_['text_inventory_help_intro'] = 'This screen allows you to manage product inventory from various aspects:';
$_['text_inventory_quantity'] = 'Inventory Quantity';
$_['text_inventory_quantity_help'] = 'Total quantity available in stock';
$_['text_available_quantity'] = 'Available Quantity';
$_['text_available_quantity_help'] = 'Quantity available for sale after deducting quantities reserved for pending orders';
$_['text_average_cost'] = 'Average Cost';
$_['text_average_cost_help'] = 'Unit cost calculated using weighted average method - automatically updated with purchases and adjustments';
$_['text_quick_adjustment'] = 'Quick Inventory Adjustment';
$_['text_quick_adjustment_help'] = 'Ability to increase or decrease quantity while recording adjustment reason';
$_['text_inventory_help_tip'] = 'All inventory changes are tracked in the transaction log';

// Inventory adjustment texts
$_['text_increase'] = 'Increase';
$_['text_decrease'] = 'Decrease';
$_['entry_adjustment_type'] = 'Adjustment Type';
$_['entry_adjustment_reason'] = 'Adjustment Reason';
$_['button_apply'] = 'Apply';
$_['entry_branch'] = 'Branch';
$_['entry_unit'] = 'Unit';
$_['entry_quantity'] = 'Quantity';
$_['entry_quantity_available'] = 'Available Quantity';
$_['entry_average_cost'] = 'Average Cost';
$_['entry_total_value'] = 'Total Value';
$_['text_select_branch'] = 'Select Branch';
$_['text_select_unit'] = 'Select Unit';

// Transaction log texts
$_['tab_movement'] = 'Transaction Log';
$_['text_movement_info'] = 'Display all inventory movements and cost history';
$_['text_movement_history'] = 'Inventory Movement History';
$_['text_movement_types'] = 'Movement Types';
$_['text_movement_types_help'] = 'Classify transactions by type (purchase, sale, adjustment, transfer)';
$_['text_movement_filters'] = 'Transaction Filters';
$_['text_movement_filters_help'] = 'Filter transaction log by type, date, and branch';
$_['text_movement_stats'] = 'Movement Statistics';
$_['text_movement_stats_help'] = 'Summary of incoming/outgoing transactions and their frequency';
$_['text_movement_help_tip'] = 'Transaction log can be exported to Excel for further analysis';
$_['text_movement_filter'] = 'Filter Transactions';
$_['text_filter_by_type'] = 'Filter by Type';
$_['text_filter_by_branch'] = 'Filter by Branch';
$_['text_filter_by_date'] = 'Filter by Date';
$_['text_all_types'] = 'All Transaction Types';
$_['text_all_branches'] = 'All Branches';
$_['text_from_date'] = 'From Date';
$_['text_to_date'] = 'To Date';
$_['button_reset'] = 'Reset';
$_['text_total_incoming'] = 'Total Incoming';
$_['text_total_outgoing'] = 'Total Outgoing';
$_['text_movement_frequency'] = 'Transaction Frequency';
$_['text_cost_history'] = 'Cost History';
$_['text_no_movements'] = 'No inventory transactions for this product';
$_['text_no_cost_history'] = 'No cost change history for this product';

// Transaction types
$_['text_purchase'] = 'Purchase';
$_['text_sale'] = 'Sale';
$_['text_adjustment_increase'] = 'Adjustment (Increase)';
$_['text_adjustment_decrease'] = 'Adjustment (Decrease)';
$_['text_transfer'] = 'Transfer';
$_['text_transfer_in'] = 'Incoming (Transfer)';
$_['text_transfer_out'] = 'Outgoing (Transfer)';
$_['text_count'] = 'Stocktake';

// Transaction table columns
$_['column_date_added'] = 'Date';
$_['column_type'] = 'Transaction Type';
$_['column_quantity'] = 'Quantity';
$_['column_unit'] = 'Unit';
$_['column_branch'] = 'Branch';
$_['column_reference'] = 'Reference';
$_['column_user'] = 'User';
$_['column_cost'] = 'Cost';
$_['column_new_cost'] = 'New Average Cost';

// Cost update texts
$_['text_manual_cost_update'] = 'Manual Cost Adjustment';
$_['text_update_prices_confirm'] = 'Do you want to update selling prices based on the new cost?';
$_['text_enter_margin_percentage'] = 'Enter profit margin percentage (%)';
$_['text_prices_updated'] = 'Selling prices updated successfully';
$_['text_cost_updated'] = 'Average cost updated successfully';
$_['text_reason_purchase'] = 'Purchase transaction';
$_['text_reason_manual'] = 'Manual adjustment';
$_['text_reason_adjustment'] = 'Inventory adjustment';
$_['text_reason_transfer'] = 'Inventory transfer';

// Stocktake texts
$_['text_inventory_count'] = 'Stocktake';
$_['text_count_sheet'] = 'Count Sheet';
$_['text_system_quantity'] = 'System Quantity';
$_['text_counted_quantity'] = 'Counted Quantity';
$_['text_difference'] = 'Variance';
$_['text_count_notes'] = 'Notes';
$_['text_count_status'] = 'Count Status';
$_['text_count_pending'] = 'Pending';
$_['text_count_applied'] = 'Applied';
$_['text_create_count_sheet'] = 'Create Count Sheet';
$_['text_sheet_created'] = 'Count sheet created successfully';
$_['text_apply_count'] = 'Apply Stocktake Results';
$_['text_count_applied'] = 'Stocktake results applied successfully';

// Transfer texts
$_['text_inventory_transfer'] = 'Inventory Transfer';
$_['text_source_branch'] = 'Source Branch';
$_['text_destination_branch'] = 'Destination Branch';
$_['text_transfer_items'] = 'Transferred Items';
$_['text_transfer_status'] = 'Transfer Status';
$_['text_transfer_pending'] = 'Pending';
$_['text_transfer_completed'] = 'Completed';
$_['text_create_transfer'] = 'Create Inventory Transfer';
$_['text_transfer_added'] = 'Inventory transfer created successfully';
$_['text_complete_transfer'] = 'Complete Transfer';
$_['text_transfer_completed'] = 'Inventory transfer completed successfully';

// Error messages
$_['error_branch_required'] = 'Branch selection is required';
$_['error_unit_required'] = 'Unit selection is required';
$_['error_quantity_required'] = 'Valid quantity input required';
$_['error_reason_required'] = 'Adjustment reason required';
$_['error_adjustment_type_required'] = 'Adjustment type selection required';
$_['error_items_required'] = 'At least one item must be added';
$_['error_adjustment_failed'] = 'Adjustment application failed';
$_['error_source_branch_required'] = 'Source branch selection required';
$_['error_destination_branch_required'] = 'Destination branch selection required';
$_['error_same_branch'] = 'Source and destination branches must differ';
$_['error_insufficient_stock'] = 'Required quantity not available in stock';
$_['error_transfer_failed'] = 'Inventory transfer creation failed';
$_['error_invalid_cost'] = 'Valid cost input required';
$_['error_invalid_margin'] = 'Profit margin must be between 0-100%';
$_['error_sheet_creation'] = 'Count sheet creation failed';
$_['error_missing_data'] = 'Missing or invalid data';
$_['error_movement_failed'] = 'Failed to add inventory transaction';
$_['error_product_id'] = 'Invalid product ID';
$_['error_required_data'] = 'Required data incomplete';

// Unit management texts
$_['text_units_info'] = 'Product unit management and conversion factors';
$_['text_units_help'] = 'Units Help';
$_['text_units_help_intro'] = 'This screen allows you to manage product measurement units:';
$_['text_base_unit'] = 'Base Unit';
$_['text_base_unit_help'] = 'Product\'s primary measurement unit used for calculations';
$_['text_additional_unit'] = 'Additional Unit';
$_['text_additional_unit_help'] = 'Supplementary units like packs, cartons, etc.';
$_['text_conversion_factor'] = 'Conversion Factor';
$_['text_conversion_factor_help'] = 'Number of base units in this additional unit. Example: 6 units per pack';
$_['text_unit_converter'] = 'Unit Converter';
$_['text_unit_converter_help'] = 'Tool for converting quantities between product units';
$_['text_base_unit_warning'] = 'Product must have at least one base unit';
$_['text_unit_conversion'] = 'Unit Conversion';
$_['text_from_unit'] = 'From Unit';
$_['text_to_unit'] = 'To Unit';
$_['text_quantity'] = 'Quantity';
$_['text_conversion_result'] = 'Conversion result will appear here';
$_['button_unit_add'] = 'Add Unit';

// Pricing texts
$_['text_pricing_info'] = 'Product price management for different units';
$_['text_pricing_help'] = 'Pricing Help';
$_['text_pricing_help_intro'] = 'This screen allows you to set product prices for different units:';
$_['text_base_price'] = 'Base Price';
$_['text_base_price_help'] = 'Main selling price (before discounts)';
$_['text_special_price'] = 'Special Price';
$_['text_special_price_help'] = 'Promotional or discounted price';
$_['text_wholesale_price'] = 'Wholesale Price';
$_['text_wholesale_price_help'] = 'Bulk selling price for resellers';
$_['text_half_wholesale_price'] = 'Half-Wholesale Price';
$_['text_custom_price'] = 'Custom Price';
$_['text_profit_margin'] = 'Profit Margin';
$_['text_profit_margin_help'] = 'Profit percentage: (Price - Cost)/Price * 100';
$_['text_price_calculator'] = 'Price Calculator';
$_['text_price_calculator_help'] = 'Tool for calculating prices based on cost and margin';
$_['text_pricing_help_tip'] = 'Selling prices can be auto-updated based on cost changes';
$_['text_cost'] = 'Cost';
$_['text_margin'] = 'Margin (%)';
$_['text_price'] = 'Price';
$_['button_calculate_price'] = 'Calculate Price';
$_['button_calculate_margin'] = 'Calculate Margin';
$_['button_apply_price'] = 'Apply Price';
$_['text_price_applied'] = 'Price applied successfully';

// Miscellaneous
$_['text_loading'] = 'Loading...';
$_['text_no_inventory'] = 'No inventory available for this product';
$_['text_movement_added'] = 'Inventory transaction added successfully';
$_['text_adjustment_added'] = 'Inventory adjustment applied successfully';

// Bulk Operations
$_['text_bulk_operations']        = 'Bulk Operations';
$_['text_bulk_stock_update']      = 'Bulk Stock Update';
$_['text_bulk_cost_update']       = 'Bulk Cost Update';
$_['text_bulk_status_change']     = 'Bulk Status Change';
$_['text_export_excel']           = 'Export to Excel';
$_['text_export_pdf']             = 'Export to PDF';
$_['text_select_products_first']  = 'Please select products first';

// Bulk Stock Update
$_['entry_update_type']           = 'Update Type';
$_['text_set_quantity']           = 'Set specific quantity';
$_['text_increase_by']            = 'Increase by';
$_['text_decrease_by']            = 'Decrease by';
$_['text_adjust_by_percentage']   = 'Adjust by percentage';
$_['entry_value']                 = 'Value';
$_['text_update_cost']            = 'Update Cost';
$_['entry_cost_value']            = 'Cost Value';
$_['text_reason_initial_stock']   = 'Initial Stock';
$_['text_reason_inventory_correction'] = 'Inventory Correction';
$_['text_reason_damaged_goods']   = 'Damaged Goods';
$_['text_reason_expired_goods']   = 'Expired Goods';
$_['text_reason_customer_returns'] = 'Customer Returns';
$_['text_specify_reason']         = 'Specify Reason';
$_['text_enter_valid_cost']       = 'Please enter a valid cost value';

// Bulk Cost Update
$_['text_set_cost']               = 'Set specific cost';
$_['text_update_selling_prices']  = 'Update Selling Prices';
$_['text_profit_margin_percentage'] = 'Profit Margin Percentage';
$_['text_margin_calculation_help'] = 'Selling price is calculated using: Cost / (1 - Margin)';
$_['text_note']                   = 'Note';
$_['text_optional']               = 'Optional';
$_['text_enter_positive_value']   = 'Please enter a positive value';
$_['text_enter_valid_margin']     = 'Please enter a valid margin percentage (0-100)';

// Bulk Status Change
$_['text_select_status']          = 'Please select a status';

// Print Barcodes
$_['text_print_barcodes']         = 'Print Barcodes';
$_['text_barcode_type']           = 'Barcode Type';
$_['text_barcode_format']         = 'Barcode Format';
$_['text_individual_labels']      = 'Individual Labels';
$_['text_sheet_labels']           = 'Sheet Labels';
$_['text_quantity_per_product']   = 'Quantity Per Product';
$_['text_include_price']          = 'Include Price';
$_['text_page_size']              = 'Page Size';
$_['button_generate']             = 'Generate';

// Count Sheet
$_['text_create_count_sheet']     = 'Create Count Sheet';
$_['text_count_type']             = 'Count Type';
$_['text_full_count']             = 'Full Count';
$_['text_partial_count']          = 'Partial Count';
$_['text_periodic_count']         = 'Periodic Count';
$_['text_sheet_format']           = 'Sheet Format';
$_['text_printed_sheet']          = 'Printed Sheet';
$_['text_mobile_sheet']           = 'Mobile Sheet';
$_['text_excel_sheet']            = 'Excel File';
$_['text_notes']                  = 'Notes';
$_['text_sheet_created']          = 'Count sheet created successfully';
$_['text_select_branch_first']    = 'Please select a branch first';

// Stock Transfer
$_['text_create_transfer']        = 'Create Stock Transfer';
$_['text_source_branch']          = 'Source Branch';
$_['text_destination_branch']     = 'Destination Branch';
$_['text_transfer_items']         = 'Transfer Items';
$_['text_search_products']        = 'Search Products';
$_['text_type_to_search']         = 'Type to search...';
$_['column_available']            = 'Available';
$_['column_transfer_quantity']    = 'Transfer Qty';
$_['text_insufficient_stock']     = 'Insufficient stock for requested quantity';
$_['text_select_source_branch_first'] = 'Please select source branch first';
$_['text_select_destination_branch_first'] = 'Please select destination branch first';
$_['text_same_branch_error']      = 'Source and destination branches cannot be the same';
$_['text_add_transfer_items_first'] = 'Please add transfer items first';
$_['text_quantity_must_be_positive'] = 'Quantity must be positive';
$_['text_quantity_exceeds_available'] = 'Quantity exceeds available stock';
$_['text_transfer_added']         = 'Stock transfer created successfully';
$_['text_available']              = 'available';

// Inventory Details
$_['text_inventory_details']      = 'Inventory Details';
$_['text_inventory']              = 'Inventory';
$_['text_movements']              = 'Movements';
$_['text_cost_history']           = 'Cost History';
$_['text_price_history']          = 'Price History';
$_['column_branch']               = 'Branch';
$_['column_unit']                 = 'Unit';
$_['column_quantity']             = 'Quantity';
$_['column_available']            = 'Available';
$_['column_reserved']             = 'Reserved';
$_['column_average_cost']         = 'Average Cost';
$_['column_total_value']          = 'Total Value';
$_['text_filter_by_type']         = 'Filter by Type';
$_['text_all_types']              = 'All Types';
$_['column_date']                 = 'Date';
$_['column_type']                 = 'Type';
$_['column_reference']            = 'Reference';
$_['column_cost']                 = 'Cost';
$_['column_user']                 = 'User';
$_['column_old_cost']             = 'Old Cost';
$_['column_new_cost']             = 'New Cost';
$_['column_change_type']          = 'Change Type';
$_['column_notes']                = 'Notes';
$_['column_price_type']           = 'Price Type';
$_['column_old_price']            = 'Old Price';
$_['column_new_price']            = 'New Price';
$_['text_no_inventory']           = 'No inventory for this product';
$_['text_no_movements']           = 'No inventory movements for this product';
$_['text_no_cost_history']        = 'No cost history for this product';
$_['text_no_price_history']       = 'No price history for this product';

// Quick Adjustment
$_['text_quick_adjustment']       = 'Quick Stock Adjustment';
$_['entry_branch']                = 'Branch';
$_['entry_unit']                  = 'Unit';
$_['entry_adjustment_type']       = 'Adjustment Type';
$_['entry_quantity']              = 'Quantity';
$_['entry_adjustment_reason']     = 'Adjustment Reason';
$_['text_increase']               = 'Increase';
$_['text_decrease']               = 'Decrease';
$_['text_select_unit_first']      = 'Please select a unit first';
$_['text_select_reason_first']    = 'Please select a reason first';
$_['text_movement_added']         = 'Inventory movement added successfully';
$_['text_reason_damaged']         = 'Damaged';
$_['text_reason_expired']         = 'Expired';
$_['text_reason_correction']      = 'Correction';
$_['text_reason_initial']         = 'Initial Stock';
$_['text_reason_other']           = 'Other';
$_['text_cost_help']              = 'Leave blank to use current average cost';

// Weighted Average Cost Calculator
$_['text_weighted_average_cost_calculator'] = 'Weighted Average Cost Calculator';
$_['text_wac_explanation']        = 'Use this tool to calculate the new weighted average cost when adding new inventory with a different cost.';
$_['text_current_quantity']       = 'Current Quantity';
$_['text_current_unit_cost']      = 'Current Unit Cost';
$_['text_new_quantity']           = 'New Quantity';
$_['text_new_unit_cost']          = 'New Unit Cost';
$_['text_weighted_average_cost']  = 'Weighted Average Cost';
$_['text_cost_must_be_positive']  = 'Cost must be positive';
$_['button_calculate']            = 'Calculate';

// Advanced Filters
$_['entry_date_added']            = 'Date Added';
$_['entry_cost_min']              = 'Min Cost';
$_['entry_cost_max']              = 'Max Cost';
$_['entry_manufacturer']          = 'Manufacturer';
$_['entry_supplier']              = 'Supplier';
$_['entry_low_stock']             = 'Low Stock';
$_['text_all']                    = 'All';

// General Messages
$_['text_loading']                = 'Loading...';
$_['text_error_loading']          = 'Error loading data';
$_['text_currency']               = '$';
$_['text_success_operation']      = 'Operation completed successfully';
$_['text_generated_successfully'] = 'Generated successfully';
$_['text_export_success']         = 'Exported successfully';
$_['text_inventory_report']       = 'Inventory Report';
$_['text_valuation_report']       = 'Valuation Report';
$_['text_inventory_operations']   = 'Inventory Operations';
$_['text_reports']                = 'Reports';
$_['text_purchase']               = 'Purchase';
$_['text_sale']                   = 'Sale';
$_['text_adjustment_increase']    = 'Adjustment (Increase)';
$_['text_adjustment_decrease']    = 'Adjustment (Decrease)';
$_['text_transfer_in']            = 'Transfer In';
$_['text_transfer_out']           = 'Transfer Out';
$_['text_filter_applied']         = 'Filter applied';
$_['text_filter_reset']           = 'Filter reset';

$_['text_low_stock']              = 'Low Stock';
$_['text_has_expired']            = 'Expired Items';
$_['text_below_min_stock']        = 'Below Minimum Stock';
$_['text_min']                    = 'Min';
$_['text_view_inventory']         = 'View Inventory';

// Inventory Tab
$_['tab_inventory']            = 'Inventory';
$_['text_inventory_info']      = 'Manage product inventory across multiple branches and units';
$_['text_inventory_levels']    = 'Inventory Levels';
$_['text_recent_movements']    = 'Recent Movements';
$_['text_no_inventory_data']   = 'No inventory data available for this product';
$_['text_no_branches_available'] = 'No branches available in the system';
$_['text_no_units_selected']   = 'No units selected for this product';
$_['text_totals']              = 'Totals';
$_['text_view_all_movements']  = 'View All Movements';
$_['text_no_recent_movements'] = 'No recent movements found';
$_['text_consignment']         = 'Consignment';

// Inventory Columns
$_['entry_branch']             = 'Branch';
$_['entry_unit']               = 'Unit';
$_['entry_quantity']           = 'Quantity';
$_['entry_quantity_available'] = 'Available Quantity';
$_['entry_average_cost']       = 'Average Cost';
$_['entry_total_value']        = 'Total Value';
$_['entry_consignment']        = 'Consignment';
$_['entry_action']             = 'Action';
$_['button_add_movement']      = 'Add Movement';

// Movement Table
$_['column_date']              = 'Date';
$_['column_type']              = 'Type';
$_['column_quantity']          = 'Quantity';
$_['column_unit']              = 'Unit';
$_['column_branch']            = 'Branch';
$_['column_cost_impact']       = 'Cost Impact';
$_['column_user']              = 'User';
$_['column_reference']         = 'Reference';

// Adjustment Modal
$_['text_adjustment_details']  = 'Adjustment Details';
$_['text_add_stock']           = 'Add Stock';
$_['text_remove_stock']        = 'Remove Stock';
$_['text_stock_count']         = 'Stock Count';
$_['entry_movement_type']      = 'Movement Type';
$_['entry_direct_cost']        = 'Direct Cost';
$_['help_direct_cost']         = 'For incoming stock, set the direct cost of this unit';
$_['text_quantity_to_add']     = 'Enter quantity to add';
$_['text_quantity_to_remove']  = 'Enter quantity to remove';
$_['text_actual_quantity']     = 'Enter actual quantity';
$_['entry_reason']             = 'Reason';
$_['text_reason_stock_count']  = 'Stock Count';
$_['text_reason_damaged']      = 'Damaged/Expired Items';
$_['text_reason_expired']      = 'Expired Products';
$_['text_reason_correction']   = 'Data Correction';
$_['text_reason_production']   = 'Production';
$_['text_reason_initial_stock'] = 'Initial Stock';
$_['text_reason_other']        = 'Other Reason';
$_['entry_custom_reason']      = 'Custom Reason';
$_['entry_notes']              = 'Notes';
$_['entry_document_reference'] = 'Document Reference';
$_['text_optional']            = 'Optional';

// Financial Impact
$_['text_financial_impact']    = 'Financial Impact';
$_['text_branch']              = 'Branch';
$_['text_unit']                = 'Unit';
$_['text_current_quantity']    = 'Current Quantity';
$_['text_current_cost']        = 'Current Cost';
$_['text_new_quantity']        = 'New Quantity';
$_['text_new_cost']            = 'New Cost';
$_['text_quantity_change']     = 'Quantity Change';
$_['text_stock_value_change']  = 'Stock Value Change';
$_['text_gl_account_impact']   = 'GL Account Impact';
$_['text_inventory_account']   = 'Inventory Account';
$_['text_contra_account']      = 'Contra Account';

// Accounting Preview
$_['text_preview_journal_entry'] = 'Preview Journal Entry';
$_['text_account']             = 'Account';
$_['text_debit']               = 'Debit';
$_['text_credit']              = 'Credit';
$_['text_confirm_adjustment']  = 'I confirm this adjustment is correct';
$_['text_insufficient_stock_warning'] = 'Warning: Available quantity (%s) is less than requested quantity';

// Cost Adjustment Modal
$_['text_cost_details']        = 'Cost Details';
$_['text_current_cost']        = 'Current Cost';
$_['text_new_cost']            = 'New Cost';
$_['text_cost_change_reason']  = 'Cost Change Reason';
$_['text_market_price_change'] = 'Market Price Change';
$_['text_supplier_price_change'] = 'Supplier Price Change';
$_['text_data_correction']     = 'Data Correction';
$_['text_other_reason']        = 'Other Reason';
$_['text_detailed_notes']      = 'Detailed Notes';
$_['text_quantity_on_hand']    = 'Quantity on Hand';
$_['text_current_total_value'] = 'Current Total Value';
$_['text_new_total_value']     = 'New Total Value';
$_['text_value_change']        = 'Value Change';
$_['text_update_sales_prices'] = 'Update Sales Prices';
$_['text_update_based_on_new_cost'] = 'Update prices based on new cost';
$_['text_profit_margin_percentage'] = 'Profit Margin Percentage';
$_['text_current_base_price']  = 'Current Base Price';
$_['text_calculated_new_price'] = 'Calculated New Price';
$_['text_confirm_cost_change'] = 'I confirm this cost change is correct';

// Error Messages
$_['error_branch_required']    = 'Please select a branch';
$_['error_unit_required']      = 'Please select a unit';
$_['error_quantity_must_be_positive'] = 'Quantity must be greater than zero';
$_['error_insufficient_stock'] = 'Insufficient stock available (%s) for requested quantity (%s)';
$_['error_invalid_cost']       = 'Please enter a valid cost';
$_['error_reason_required']    = 'Please enter an adjustment reason';
$_['error_custom_reason_required'] = 'Please enter a custom reason';
$_['error_confirmation_required'] = 'Please confirm the adjustment';

// Success Messages
$_['text_adjustment_saved']    = 'Inventory adjustment saved successfully';
$_['text_cost_updated']        = 'Product cost updated successfully';

// Process Status
$_['text_loading']             = 'Loading...';
$_['text_saving']              = 'Saving...';

// Additional
$_['text_equity_account']      = 'Equity Account';
$_['text_production_account']  = 'Production Account';
$_['text_inventory_loss_account'] = 'Inventory Loss Account';
$_['text_cost_of_goods_sold']  = 'Cost of Goods Sold';
$_['text_inventory_adjustment_account'] = 'Inventory Adjustment Account';
$_['text_unknown']             = 'Unknown';

// Text for inventory movements
$_['text_movement_added']              = 'Inventory movement has been added successfully!';
$_['text_no_change_needed']            = 'No changes to inventory were needed.';
$_['text_adjustment_saved']            = 'Inventory adjustment has been saved successfully!';
$_['text_adjustment_saved_memory']     = 'Inventory adjustment has been saved in memory.';
$_['text_adjustment_added']            = 'Stock adjustment has been added successfully!';

// Quick Stock Adjustment
$_['text_quick_stock_adjustment']      = 'Quick Stock Adjustment';
$_['text_quick_count']                 = 'Set Stock Count';
$_['text_quick_increase']              = 'Add Stock';
$_['text_quick_decrease']              = 'Remove Stock';
$_['text_current_stock']               = 'Current Stock';
$_['text_available_units']             = 'Available Units';
$_['text_adjustment_type']             = 'Adjustment Type';
$_['text_confirm_adjustment']          = 'I confirm this adjustment is correct';
$_['text_adjustment_information']      = 'Adjustment Information';
$_['text_adjustment_help']             = 'Use this form to quickly adjust inventory levels for the selected product.';
$_['text_select_product']              = 'Please select a product first';
$_['text_select_one_product']          = 'Please select only one product';
$_['text_error_loading_product']       = 'Error loading product information';
$_['text_saving']                      = 'Saving...';
$_['text_adjustment_preview']          = 'Adjustment Preview';

// Error messages for inventory
$_['error_branch_required']            = 'Branch/Warehouse is required!';
$_['error_unit_required']              = 'Unit is required!';
$_['error_adjustment_type_required']   = 'Adjustment type is required!';
$_['error_items_required']             = 'At least one item is required!';
$_['error_adjustment_failed']          = 'Failed to save stock adjustment!';
$_['error_insufficient_stock']         = 'Insufficient stock! Available: %s, Requested: %s';
$_['error_quantity_must_be_positive']  = 'Quantity must be greater than zero!';
$_['error_custom_reason_required']     = 'Custom reason is required when selecting "Other"!';
$_['error_confirmation_required']      = 'You must confirm the adjustment!';
$_['error_quantity_invalid']           = 'Invalid quantity value!';
$_['error_save_failed']                = 'Failed to save inventory adjustment';
$_['error_missing_data']               = 'Required data is missing!';

// Batch adjustment text
$_['text_batch_adjustment'] = 'Batch Stock Adjustment';
$_['text_batch_adjustment_title'] = 'Adjust Stock for Multiple Products';
$_['text_items_to_adjust'] = 'Items to Adjust';
$_['text_branch_selection'] = 'Select Branch/Warehouse';
$_['text_adjustment_reason'] = 'Reason for Adjustment';
$_['text_additional_notes'] = 'Additional Notes';
$_['text_batch_adjustment_success'] = 'Successfully adjusted stock for %s product(s)';
$_['text_batch_adjustment_partial'] = 'Completed %s adjustment(s) with %s error(s)';
$_['text_add_to_batch'] = 'Add to Batch';
$_['text_process_adjustments'] = 'Process Adjustments';
$_['text_product_added_to_batch'] = 'Product added to batch adjustment';
$_['text_batch_empty'] = 'No products in batch yet';

// Error messages
$_['error_items_required'] = 'No adjustment items provided';
$_['error_branch_required'] = 'Please select a branch';
$_['error_product_quantity_invalid'] = 'Invalid quantity for product: %s';
$_['error_product_insufficient_stock'] = 'Insufficient stock for %s (available: %s, requested: %s)';
$_['error_product_adjustment_failed'] = 'Failed to adjust stock for: %s';

// ═══════════════════════════════════════════════════════════════════════════════
// Enterprise Grade Plus - الدستور الشامل
// ═══════════════════════════════════════════════════════════════════════════════

// Central Services Integration
$_['text_product_added_notification'] = 'New product added: %s';
$_['text_product_critical_changes'] = 'Critical changes made to product: %s';
$_['text_low_stock_alert'] = '%d products have low stock levels';
$_['text_auto_saved'] = 'Changes auto-saved successfully';
$_['text_auto_save_failed'] = 'Auto-save failed. Please save manually.';
$_['text_unsaved_changes'] = 'Unsaved Changes';
$_['text_unsaved_changes_warning'] = 'You have unsaved changes. Are you sure you want to leave?';

// Advanced Permissions
$_['error_permission_advanced'] = 'You do not have permission to access advanced features';
$_['text_save_and_new'] = 'Save & Add New';
$_['text_save_and_copy'] = 'Save & Copy';
$_['text_save_and_close'] = 'Save & Close';

// Form Enhancement
$_['text_product_info'] = 'Product Information';
$_['text_quick_info'] = 'Quick Information';
$_['text_quick_stats'] = 'Quick Statistics';
$_['text_basic_data'] = 'Basic Data';
$_['text_inventory_settings'] = 'Inventory Settings';
$_['text_categorization'] = 'Categorization';
$_['text_general_settings'] = 'General Settings';
$_['text_views'] = 'Views';
$_['text_orders'] = 'Orders';
$_['text_stock'] = 'Stock';
$_['text_rating'] = 'Rating';
$_['text_id'] = 'ID';
$_['text_enabled'] = 'Enabled';
$_['text_disabled'] = 'Disabled';
$_['text_date_added'] = 'Date Added';
$_['text_date_modified'] = 'Date Modified';

// Help Texts
$_['help_tag'] = 'Comma separated tags for better searchability';
$_['help_meta_title'] = 'Maximum 60 characters for optimal SEO';
$_['help_meta_description'] = 'Maximum 160 characters for search engine snippets';
$_['help_meta_keyword'] = 'Comma separated keywords for SEO';
$_['help_seo_permalink'] = 'SEO-friendly URL for this product';

// Validation Messages
$_['error_name_required'] = 'Product name is required for all languages';
$_['error_model_required'] = 'Product model is required';
$_['error_model_exists'] = 'This model already exists. Please use a unique model.';
$_['error_meta_title_length'] = 'Meta title must be 60 characters or less';
$_['error_meta_description_length'] = 'Meta description must be 160 characters or less';
$_['error_add_failed'] = 'Failed to add product';
$_['error_edit_failed'] = 'Failed to edit product';
$_['error_product_has_dependencies'] = 'Cannot delete product "%s" as it has dependencies';

// Success Messages
$_['text_success_add'] = 'Product added successfully';
$_['text_success_edit'] = 'Product updated successfully';
$_['text_success_delete'] = 'Product(s) deleted successfully';
$_['text_success_copy'] = 'Product(s) copied successfully';
$_['text_field_updated'] = 'Field updated successfully';

// Export/Import
$_['text_export'] = 'Export Products';
$_['text_import'] = 'Import Products';
$_['text_import_success'] = 'Successfully imported %d products';
$_['error_invalid_file_type'] = 'Invalid file type. Please use Excel or CSV files.';
$_['error_empty_file'] = 'The uploaded file is empty';
$_['error_missing_required_data'] = 'Missing required data in row %d';
$_['error_import_row'] = 'Error in row %d: %s';

// AJAX Operations
$_['error_product_not_found'] = 'Product not found';
$_['error_product_id_required'] = 'Product ID is required';
$_['error_invalid_field'] = 'Invalid field for update';
$_['error_missing_data'] = 'Missing required data';

// Reports and Analytics
$_['text_performance_report'] = 'Product Performance Report';
$_['text_inventory_valuation'] = 'Inventory Valuation Report';
$_['text_abc_analysis'] = 'ABC Analysis';
$_['text_abc_recommendation_a'] = 'High priority - Monitor closely, ensure availability';
$_['text_abc_recommendation_b'] = 'Medium priority - Regular monitoring required';
$_['text_abc_recommendation_c'] = 'Low priority - Basic monitoring sufficient';

// AI Features
$_['text_ai_recommendations'] = 'AI Recommendations';
$_['text_demand_prediction'] = 'Demand Prediction';
$_['text_pricing_suggestions'] = 'Pricing Suggestions';
$_['text_inventory_suggestions'] = 'Inventory Suggestions';
$_['text_marketing_suggestions'] = 'Marketing Suggestions';

// Security Audit
$_['text_security_audit'] = 'Security Audit';
$_['audit_products_without_prices'] = '%d products found without prices';
$_['audit_negative_stock'] = '%d products found with negative stock';
$_['audit_duplicate_models'] = '%d duplicate model numbers found';
$_['audit_products_without_categories'] = '%d products found without categories';
$_['audit_products_without_images'] = '%d products found without images';

// Accounting Integration
$_['text_initial_inventory'] = 'Initial inventory for product: %s';
$_['text_price_adjustment'] = 'Price adjustment for product ID: %s';
$_['text_inventory_adjustment'] = 'Inventory adjustment for product ID: %s';

// Advanced Features
$_['text_wac_enabled'] = 'WAC (Weighted Average Cost) Enabled';
$_['text_accounting_integration'] = 'Accounting Integration Active';
$_['text_central_services'] = 'Central Services Connected';
$_['text_transaction_support'] = 'Transaction Support Enabled';
$_['text_rollback_support'] = 'Rollback Support Active';

// Bulk Operations
$_['text_bulk_operations'] = 'Bulk Operations';
$_['text_bulk_delete'] = 'Bulk Delete';
$_['text_bulk_enable'] = 'Bulk Enable';
$_['text_bulk_disable'] = 'Bulk Disable';
$_['text_bulk_update_category'] = 'Bulk Update Category';
$_['text_bulk_update_price'] = 'Bulk Update Price';

// Advanced Search
$_['text_advanced_search'] = 'Advanced Search';
$_['text_search_by_barcode'] = 'Search by Barcode';
$_['text_search_by_category'] = 'Search by Category';
$_['text_search_by_manufacturer'] = 'Search by Manufacturer';
$_['text_search_by_price_range'] = 'Search by Price Range';
$_['text_search_by_stock_level'] = 'Search by Stock Level';

// Multi-Unit System
$_['text_multi_unit_system'] = 'Multi-Unit System';
$_['text_base_unit'] = 'Base Unit';
$_['text_conversion_factor'] = 'Conversion Factor';
$_['text_unit_pricing'] = 'Unit Pricing';
$_['text_unit_inventory'] = 'Unit Inventory';

// Barcode Management
$_['text_barcode_management'] = 'Barcode Management';
$_['text_generate_barcode'] = 'Generate Barcode';
$_['text_barcode_generated'] = 'Barcode generated successfully';
$_['text_barcode_exists'] = 'Barcode already exists';

// Bundle Management
$_['text_bundle_management'] = 'Bundle Management';
$_['text_bundle_products'] = 'Bundle Products';
$_['text_bundle_pricing'] = 'Bundle Pricing';
$_['text_bundle_discount'] = 'Bundle Discount';

// Recommendations
$_['text_related_products'] = 'Related Products';
$_['text_recommended_products'] = 'Recommended Products';
$_['text_frequently_bought_together'] = 'Frequently Bought Together';
$_['text_customers_also_viewed'] = 'Customers Also Viewed';

// Movement Tracking
$_['text_movement_tracking'] = 'Movement Tracking';
$_['text_stock_movements'] = 'Stock Movements';
$_['text_movement_history'] = 'Movement History';
$_['text_movement_type'] = 'Movement Type';
$_['text_movement_reason'] = 'Movement Reason';

// Order Analysis
$_['text_order_analysis'] = 'Order Analysis';
$_['text_sales_history'] = 'Sales History';
$_['text_order_frequency'] = 'Order Frequency';
$_['text_seasonal_trends'] = 'Seasonal Trends';
$_['text_customer_preferences'] = 'Customer Preferences';

// Dashboard Integration
$_['text_dashboard_integration'] = 'Dashboard Integration';
$_['text_quick_actions'] = 'Quick Actions';
$_['text_recent_activities'] = 'Recent Activities';
$_['text_alerts_notifications'] = 'Alerts & Notifications';

// Mobile Optimization
$_['text_mobile_optimized'] = 'Mobile Optimized';
$_['text_responsive_design'] = 'Responsive Design';
$_['text_touch_friendly'] = 'Touch Friendly Interface';

// Performance Metrics
$_['text_performance_metrics'] = 'Performance Metrics';
$_['text_load_time'] = 'Load Time';
$_['text_response_time'] = 'Response Time';
$_['text_cache_status'] = 'Cache Status';

// Data Validation
$_['text_data_validation'] = 'Data Validation';
$_['text_required_fields'] = 'Required Fields';
$_['text_field_validation'] = 'Field Validation';
$_['text_data_integrity'] = 'Data Integrity';

// Backup and Recovery
$_['text_backup_recovery'] = 'Backup & Recovery';
$_['text_auto_backup'] = 'Auto Backup';
$_['text_recovery_point'] = 'Recovery Point';
$_['text_data_protection'] = 'Data Protection';

// Integration APIs
$_['text_integration_apis'] = 'Integration APIs';
$_['text_rest_api'] = 'REST API';
$_['text_webhook_support'] = 'Webhook Support';
$_['text_third_party_integration'] = 'Third Party Integration';

// Compliance and Standards
$_['text_compliance_standards'] = 'Compliance & Standards';
$_['text_gdpr_compliance'] = 'GDPR Compliance';
$_['text_iso_standards'] = 'ISO Standards';
$_['text_security_standards'] = 'Security Standards';

// Multi-Language Support
$_['text_multi_language'] = 'Multi-Language Support';
$_['text_rtl_support'] = 'RTL Support';
$_['text_translation_management'] = 'Translation Management';
$_['text_locale_settings'] = 'Locale Settings';

// Advanced Pricing
$_['text_advanced_pricing'] = 'Advanced Pricing';
$_['text_dynamic_pricing'] = 'Dynamic Pricing';
$_['text_tier_pricing'] = 'Tier Pricing';
$_['text_promotional_pricing'] = 'Promotional Pricing';
$_['text_cost_based_pricing'] = 'Cost-Based Pricing';

// Quality Assurance
$_['text_quality_assurance'] = 'Quality Assurance';
$_['text_automated_testing'] = 'Automated Testing';
$_['text_code_quality'] = 'Code Quality';
$_['text_performance_testing'] = 'Performance Testing';

// Enterprise Features
$_['text_enterprise_features'] = 'Enterprise Features';
$_['text_scalability'] = 'Scalability';
$_['text_high_availability'] = 'High Availability';
$_['text_load_balancing'] = 'Load Balancing';
$_['text_clustering'] = 'Clustering';

// Final Enterprise Grade Plus Markers
// Additional Help Tips and UI Elements
$_['text_orders_help_tip'] = 'Sales data analysis helps understand customer behavior and predict future demand.';
$_['text_images_help_tip'] = 'High-quality and multiple images increase customer confidence and reduce return rates.';
$_['text_main_image_help'] = 'Choose a clear and distinctive image that represents the product well.';
$_['text_unit_conversion_info'] = 'Use this tool to convert quantities between product units.';
$_['text_unit_converter'] = 'Unit Converter';
$_['text_unit_converter_help'] = 'Tool to test conversion between different product units.';

// Modal Titles
$_['text_modal_title_inventory'] = 'Inventory Details';
$_['text_modal_title_adjustment'] = 'Inventory Adjustment';
$_['text_modal_title_supplier_price'] = 'Supplier Pricing';
$_['text_modal_title_barcode'] = 'Barcode';
$_['text_modal_title_count_sheet'] = 'Count Sheet';
$_['text_modal_title_transfer'] = 'Inventory Transfer';
$_['text_modal_title_dynamic_pricing'] = 'Dynamic Pricing';

// Button Text
$_['button_apply_adjustment'] = 'Apply Adjustment';
$_['button_cancel_adjustment'] = 'Cancel Adjustment';
$_['button_create_transfer'] = 'Create Transfer';
$_['button_cancel_transfer'] = 'Cancel Transfer';
$_['button_save_supplier_price'] = 'Save Supplier Price';
$_['button_cancel_supplier_price'] = 'Cancel';
$_['button_generate_barcode'] = 'Generate Barcode';
$_['button_close_barcode_modal'] = 'Close';
$_['button_create_count_sheet'] = 'Create Count Sheet';
$_['button_cancel_count_sheet'] = 'Cancel';
$_['button_add_dynamic_rule'] = 'Add Pricing Rule';
$_['button_cancel_dynamic_rule'] = 'Cancel';
$_['button_apply_price'] = 'Apply Price';
$_['button_calculate_price'] = 'Calculate Price';
$_['button_calculate_margin'] = 'Calculate Margin';
$_['button_add_movement'] = 'Add Movement';
$_['button_apply'] = 'Apply';
$_['button_calculate'] = 'Calculate';
$_['button_add_unit'] = 'Add Unit';
$_['button_discount_add'] = 'Add Discount';
$_['button_bundle_add'] = 'Add Bundle';
$_['button_upsell_add'] = 'Add Upsell Product';
$_['button_cross_sell_add'] = 'Add Cross-sell Product';
$_['button_barcode_add'] = 'Add Barcode';

// Entry Fields
$_['entry_direct_cost'] = 'Direct Cost';
$_['entry_price_change_reason'] = 'Price Change Reason';
$_['entry_document_reference'] = 'Document Reference';
$_['entry_custom_reason'] = 'Custom Reason';
$_['entry_adjustment_movement_type'] = 'Adjustment Movement Type';
$_['entry_transfer_notes'] = 'Transfer Notes';

// Additional Headings and Text
$_['text_no_results'] = 'No Results';
$_['text_transfer_notes'] = 'Transfer Notes';
$_['text_stock_count'] = 'Stock Count';
$_['text_no_option'] = 'No Option';
$_['text_na'] = 'N/A';
$_['text_select'] = 'Select...';
$_['text_select_unit'] = 'Select Unit';
$_['text_select_branch'] = 'Select Branch';
$_['text_from_date'] = 'From Date';
$_['text_to_date'] = 'To Date';
$_['text_date_range'] = 'Date Range';
$_['text_filter_by_date'] = 'Filter by Date';
$_['text_filter_by_category'] = 'Filter by Category';
$_['text_filter_by_manufacturer'] = 'Filter by Manufacturer';
$_['text_filter_by_status'] = 'Filter by Status';
$_['text_clear_filters'] = 'Clear Filters';
$_['text_apply_filters'] = 'Apply Filters';
$_['text_export_filtered'] = 'Export Filtered';
$_['text_print_report'] = 'Print Report';
$_['text_email_report'] = 'Email Report';
$_['text_schedule_report'] = 'Schedule Report';
$_['text_report_settings'] = 'Report Settings';
$_['text_chart_view'] = 'Chart View';
$_['text_table_view'] = 'Table View';
$_['text_summary_view'] = 'Summary View';
$_['text_detailed_view'] = 'Detailed View';
$_['text_refresh_data'] = 'Refresh Data';
$_['text_auto_refresh'] = 'Auto Refresh';
$_['text_last_updated'] = 'Last Updated';
$_['text_data_source'] = 'Data Source';
$_['text_real_time'] = 'Real Time';
$_['text_cached_data'] = 'Cached Data';
$_['text_sync_status'] = 'Sync Status';
$_['text_connection_status'] = 'Connection Status';
$_['text_system_health'] = 'System Health';
$_['text_performance_metrics'] = 'Performance Metrics';
$_['text_error_logs'] = 'Error Logs';
$_['text_audit_trail'] = 'Audit Trail';
$_['text_user_activity'] = 'User Activity';
$_['text_session_info'] = 'Session Info';
$_['text_security_level'] = 'Security Level';
$_['text_access_level'] = 'Access Level';
$_['text_permission_matrix'] = 'Permission Matrix';
$_['text_role_management'] = 'Role Management';
$_['text_user_preferences'] = 'User Preferences';
$_['text_notification_settings'] = 'Notification Settings';
$_['text_theme_settings'] = 'Theme Settings';
$_['text_language_settings'] = 'Language Settings';
$_['text_timezone_settings'] = 'Timezone Settings';
$_['text_currency_settings'] = 'Currency Settings';
$_['text_number_format'] = 'Number Format';
$_['text_date_format'] = 'Date Format';
$_['text_time_format'] = 'Time Format';
$_['text_decimal_places'] = 'Decimal Places';
$_['text_thousand_separator'] = 'Thousand Separator';
$_['text_decimal_separator'] = 'Decimal Separator';
$_['text_currency_symbol'] = 'Currency Symbol';
$_['text_currency_position'] = 'Currency Position';
$_['text_tax_display'] = 'Tax Display';
$_['text_price_display'] = 'Price Display';
$_['text_stock_display'] = 'Stock Display';
$_['text_image_quality'] = 'Image Quality';
$_['text_image_size'] = 'Image Size';
$_['text_thumbnail_size'] = 'Thumbnail Size';
$_['text_watermark_settings'] = 'Watermark Settings';
$_['text_seo_settings'] = 'SEO Settings';
$_['text_meta_settings'] = 'Meta Settings';
$_['text_sitemap_settings'] = 'Sitemap Settings';
$_['text_robots_settings'] = 'Robots Settings';
$_['text_analytics_settings'] = 'Analytics Settings';
$_['text_tracking_code'] = 'Tracking Code';
$_['text_conversion_tracking'] = 'Conversion Tracking';
$_['text_goal_tracking'] = 'Goal Tracking';
$_['text_event_tracking'] = 'Event Tracking';
$_['text_custom_dimensions'] = 'Custom Dimensions';
$_['text_custom_metrics'] = 'Custom Metrics';
$_['text_enhanced_ecommerce'] = 'Enhanced Ecommerce';
$_['text_data_layer'] = 'Data Layer';
$_['text_tag_manager'] = 'Tag Manager';
$_['text_pixel_tracking'] = 'Pixel Tracking';
$_['text_remarketing'] = 'Remarketing';
$_['text_audience_targeting'] = 'Audience Targeting';
$_['text_demographic_data'] = 'Demographic Data';
$_['text_behavioral_data'] = 'Behavioral Data';
$_['text_purchase_history'] = 'Purchase History';
$_['text_browsing_history'] = 'Browsing History';
$_['text_search_history'] = 'Search History';
$_['text_wishlist_data'] = 'Wishlist Data';
$_['text_cart_abandonment'] = 'Cart Abandonment';
$_['text_email_marketing'] = 'Email Marketing';
$_['text_sms_marketing'] = 'SMS Marketing';
$_['text_push_notifications'] = 'Push Notifications';
$_['text_social_media'] = 'Social Media';
$_['text_content_marketing'] = 'Content Marketing';
$_['text_blog_integration'] = 'Blog Integration';
$_['text_news_integration'] = 'News Integration';
$_['text_review_system'] = 'Review System';
$_['text_rating_system'] = 'Rating System';
$_['text_feedback_system'] = 'Feedback System';
$_['text_survey_system'] = 'Survey System';
$_['text_poll_system'] = 'Poll System';
$_['text_quiz_system'] = 'Quiz System';
$_['text_loyalty_program'] = 'Loyalty Program';
$_['text_reward_points'] = 'Reward Points';
$_['text_cashback_system'] = 'Cashback System';
$_['text_referral_program'] = 'Referral Program';
$_['text_affiliate_program'] = 'Affiliate Program';
$_['text_partner_program'] = 'Partner Program';
$_['text_vendor_management'] = 'Vendor Management';
$_['text_supplier_portal'] = 'Supplier Portal';
$_['text_procurement_system'] = 'Procurement System';
$_['text_purchase_orders'] = 'Purchase Orders';
$_['text_goods_receipt'] = 'Goods Receipt';
$_['text_invoice_matching'] = 'Invoice Matching';
$_['text_payment_processing'] = 'Payment Processing';
$_['text_accounts_payable'] = 'Accounts Payable';
$_['text_accounts_receivable'] = 'Accounts Receivable';
$_['text_general_ledger'] = 'General Ledger';
$_['text_trial_balance'] = 'Trial Balance';
$_['text_balance_sheet'] = 'Balance Sheet';
$_['text_income_statement'] = 'Income Statement';
$_['text_cash_flow'] = 'Cash Flow';
$_['text_budget_planning'] = 'Budget Planning';
$_['text_financial_forecasting'] = 'Financial Forecasting';
$_['text_cost_accounting'] = 'Cost Accounting';
$_['text_management_accounting'] = 'Management Accounting';
$_['text_financial_reporting'] = 'Financial Reporting';
$_['text_tax_reporting'] = 'Tax Reporting';
$_['text_regulatory_compliance'] = 'Regulatory Compliance';
$_['text_internal_controls'] = 'Internal Controls';
$_['text_risk_management'] = 'Risk Management';
$_['text_fraud_detection'] = 'Fraud Detection';
$_['text_anomaly_detection'] = 'Anomaly Detection';
$_['text_pattern_recognition'] = 'Pattern Recognition';
$_['text_machine_learning'] = 'Machine Learning';
$_['text_artificial_intelligence'] = 'Artificial Intelligence';
$_['text_predictive_analytics'] = 'Predictive Analytics';
$_['text_business_intelligence'] = 'Business Intelligence';
$_['text_data_mining'] = 'Data Mining';
$_['text_data_warehousing'] = 'Data Warehousing';
$_['text_etl_processes'] = 'ETL Processes';
$_['text_data_integration'] = 'Data Integration';
$_['text_api_management'] = 'API Management';
$_['text_microservices'] = 'Microservices';
$_['text_cloud_integration'] = 'Cloud Integration';
$_['text_hybrid_deployment'] = 'Hybrid Deployment';
$_['text_containerization'] = 'Containerization';
$_['text_orchestration'] = 'Orchestration';
$_['text_devops_integration'] = 'DevOps Integration';
$_['text_continuous_integration'] = 'Continuous Integration';
$_['text_continuous_deployment'] = 'Continuous Deployment';
$_['text_automated_testing'] = 'Automated Testing';
$_['text_load_testing'] = 'Load Testing';
$_['text_stress_testing'] = 'Stress Testing';
$_['text_security_testing'] = 'Security Testing';
$_['text_penetration_testing'] = 'Penetration Testing';
$_['text_vulnerability_scanning'] = 'Vulnerability Scanning';
$_['text_security_monitoring'] = 'Security Monitoring';
$_['text_incident_response'] = 'Incident Response';
$_['text_disaster_recovery'] = 'Disaster Recovery';
$_['text_business_continuity'] = 'Business Continuity';
$_['text_change_management'] = 'Change Management';
$_['text_version_control'] = 'Version Control';
$_['text_release_management'] = 'Release Management';
$_['text_configuration_management'] = 'Configuration Management';
$_['text_environment_management'] = 'Environment Management';
$_['text_capacity_planning'] = 'Capacity Planning';
$_['text_resource_optimization'] = 'Resource Optimization';
$_['text_cost_optimization'] = 'Cost Optimization';
$_['text_performance_optimization'] = 'Performance Optimization';
$_['text_database_optimization'] = 'Database Optimization';
$_['text_query_optimization'] = 'Query Optimization';
$_['text_index_optimization'] = 'Index Optimization';
$_['text_cache_optimization'] = 'Cache Optimization';
$_['text_memory_optimization'] = 'Memory Optimization';
$_['text_cpu_optimization'] = 'CPU Optimization';
$_['text_network_optimization'] = 'Network Optimization';
$_['text_storage_optimization'] = 'Storage Optimization';
$_['text_bandwidth_optimization'] = 'Bandwidth Optimization';
$_['text_latency_optimization'] = 'Latency Optimization';
$_['text_throughput_optimization'] = 'Throughput Optimization';
$_['text_scalability_testing'] = 'Scalability Testing';
$_['text_reliability_testing'] = 'Reliability Testing';
$_['text_availability_testing'] = 'Availability Testing';
$_['text_maintainability_testing'] = 'Maintainability Testing';
$_['text_usability_testing'] = 'Usability Testing';
$_['text_accessibility_testing'] = 'Accessibility Testing';
$_['text_compatibility_testing'] = 'Compatibility Testing';
$_['text_localization_testing'] = 'Localization Testing';
$_['text_globalization_testing'] = 'Globalization Testing';
$_['text_internationalization'] = 'Internationalization';
$_['text_cultural_adaptation'] = 'Cultural Adaptation';
$_['text_regional_compliance'] = 'Regional Compliance';
$_['text_local_regulations'] = 'Local Regulations';
$_['text_industry_standards'] = 'Industry Standards';
$_['text_best_practices'] = 'Best Practices';
$_['text_methodology_compliance'] = 'Methodology Compliance';
$_['text_process_improvement'] = 'Process Improvement';
$_['text_workflow_optimization'] = 'Workflow Optimization';
$_['text_automation_opportunities'] = 'Automation Opportunities';
$_['text_digital_transformation'] = 'Digital Transformation';
$_['text_innovation_management'] = 'Innovation Management';
$_['text_technology_adoption'] = 'Technology Adoption';
$_['text_competitive_advantage'] = 'Competitive Advantage';
$_['text_market_positioning'] = 'Market Positioning';
$_['text_value_proposition'] = 'Value Proposition';
$_['text_customer_satisfaction'] = 'Customer Satisfaction';
$_['text_customer_retention'] = 'Customer Retention';
$_['text_customer_acquisition'] = 'Customer Acquisition';
$_['text_customer_lifetime_value'] = 'Customer Lifetime Value';
$_['text_return_on_investment'] = 'Return on Investment';
$_['text_total_cost_ownership'] = 'Total Cost of Ownership';
$_['text_business_value'] = 'Business Value';
$_['text_strategic_alignment'] = 'Strategic Alignment';
$_['text_operational_excellence'] = 'Operational Excellence';
$_['text_service_excellence'] = 'Service Excellence';
$_['text_product_excellence'] = 'Product Excellence';
$_['text_customer_excellence'] = 'Customer Excellence';
$_['text_employee_excellence'] = 'Employee Excellence';
$_['text_partner_excellence'] = 'Partner Excellence';
$_['text_stakeholder_value'] = 'Stakeholder Value';
$_['text_shareholder_value'] = 'Shareholder Value';
$_['text_social_responsibility'] = 'Social Responsibility';
$_['text_environmental_impact'] = 'Environmental Impact';
$_['text_sustainability'] = 'Sustainability';
$_['text_green_technology'] = 'Green Technology';
$_['text_carbon_footprint'] = 'Carbon Footprint';
$_['text_energy_efficiency'] = 'Energy Efficiency';
$_['text_resource_conservation'] = 'Resource Conservation';
$_['text_waste_reduction'] = 'Waste Reduction';
$_['text_circular_economy'] = 'Circular Economy';
$_['text_sustainable_development'] = 'Sustainable Development';
$_['text_corporate_governance'] = 'Corporate Governance';
$_['text_ethical_business'] = 'Ethical Business';
$_['text_transparency'] = 'Transparency';
$_['text_accountability'] = 'Accountability';
$_['text_integrity'] = 'Integrity';
$_['text_trust'] = 'Trust';
$_['text_reliability'] = 'Reliability';
$_['text_dependability'] = 'Dependability';
$_['text_consistency'] = 'Consistency';
$_['text_predictability'] = 'Predictability';
$_['text_stability'] = 'Stability';
$_['text_robustness'] = 'Robustness';
$_['text_resilience'] = 'Resilience';
$_['text_adaptability'] = 'Adaptability';
$_['text_flexibility'] = 'Flexibility';
$_['text_agility'] = 'Agility';
$_['text_responsiveness'] = 'Responsiveness';
$_['text_innovation'] = 'Innovation';
$_['text_creativity'] = 'Creativity';
$_['text_excellence'] = 'Excellence';
$_['text_quality'] = 'Quality';
$_['text_precision'] = 'Precision';
$_['text_accuracy'] = 'Accuracy';
$_['text_efficiency'] = 'Efficiency';
$_['text_effectiveness'] = 'Effectiveness';
$_['text_productivity'] = 'Productivity';
$_['text_performance'] = 'Performance';
$_['text_optimization'] = 'Optimization';
$_['text_enhancement'] = 'Enhancement';
$_['text_improvement'] = 'Improvement';
$_['text_advancement'] = 'Advancement';
$_['text_progress'] = 'Progress';
$_['text_development'] = 'Development';
$_['text_evolution'] = 'Evolution';
$_['text_transformation'] = 'Transformation';
$_['text_modernization'] = 'Modernization';
$_['text_digitalization'] = 'Digitalization';
$_['text_automation'] = 'Automation';
$_['text_intelligence'] = 'Intelligence';
$_['text_sophistication'] = 'Sophistication';
$_['text_complexity'] = 'Complexity';
$_['text_simplicity'] = 'Simplicity';
$_['text_elegance'] = 'Elegance';
$_['text_beauty'] = 'Beauty';
$_['text_aesthetics'] = 'Aesthetics';
$_['text_design'] = 'Design';
$_['text_architecture'] = 'Architecture';
$_['text_structure'] = 'Structure';
$_['text_organization'] = 'Organization';
$_['text_management'] = 'Management';
$_['text_leadership'] = 'Leadership';
$_['text_governance'] = 'Governance';
$_['text_control'] = 'Control';
$_['text_monitoring'] = 'Monitoring';
$_['text_supervision'] = 'Supervision';
$_['text_oversight'] = 'Oversight';
$_['text_guidance'] = 'Guidance';
$_['text_direction'] = 'Direction';
$_['text_strategy'] = 'Strategy';
$_['text_planning'] = 'Planning';
$_['text_execution'] = 'Execution';
$_['text_implementation'] = 'Implementation';
$_['text_deployment'] = 'Deployment';
$_['text_delivery'] = 'Delivery';
$_['text_operation'] = 'Operation';
$_['text_maintenance'] = 'Maintenance';
$_['text_support'] = 'Support';
$_['text_service'] = 'Service';
$_['text_assistance'] = 'Assistance';
$_['text_help'] = 'Help';
$_['text_guidance_support'] = 'Guidance & Support';
$_['text_training'] = 'Training';
$_['text_education'] = 'Education';
$_['text_learning'] = 'Learning';
$_['text_knowledge'] = 'Knowledge';
$_['text_expertise'] = 'Expertise';
$_['text_experience'] = 'Experience';
$_['text_skill'] = 'Skill';
$_['text_competency'] = 'Competency';
$_['text_capability'] = 'Capability';
$_['text_capacity'] = 'Capacity';
$_['text_potential'] = 'Potential';
$_['text_opportunity'] = 'Opportunity';
$_['text_possibility'] = 'Possibility';
$_['text_prospect'] = 'Prospect';
$_['text_future'] = 'Future';
$_['text_vision'] = 'Vision';
$_['text_mission'] = 'Mission';
$_['text_purpose'] = 'Purpose';
$_['text_goal'] = 'Goal';
$_['text_objective'] = 'Objective';
$_['text_target'] = 'Target';
$_['text_achievement'] = 'Achievement';
$_['text_success'] = 'Success';
$_['text_victory'] = 'Victory';
$_['text_triumph'] = 'Triumph';
$_['text_accomplishment'] = 'Accomplishment';
$_['text_completion'] = 'Completion';
$_['text_fulfillment'] = 'Fulfillment';
$_['text_realization'] = 'Realization';
$_['text_manifestation'] = 'Manifestation';
$_['text_materialization'] = 'Materialization';
$_['text_actualization'] = 'Actualization';
$_['text_implementation_success'] = 'Implementation Success';
$_['text_project_completion'] = 'Project Completion';
$_['text_milestone_achievement'] = 'Milestone Achievement';
$_['text_deliverable_completion'] = 'Deliverable Completion';
$_['text_phase_completion'] = 'Phase Completion';
$_['text_stage_completion'] = 'Stage Completion';
$_['text_step_completion'] = 'Step Completion';
$_['text_task_completion'] = 'Task Completion';
$_['text_activity_completion'] = 'Activity Completion';
$_['text_process_completion'] = 'Process Completion';
$_['text_workflow_completion'] = 'Workflow Completion';
$_['text_procedure_completion'] = 'Procedure Completion';
$_['text_operation_completion'] = 'Operation Completion';
$_['text_function_completion'] = 'Function Completion';
$_['text_feature_completion'] = 'Feature Completion';
$_['text_module_completion'] = 'Module Completion';
$_['text_component_completion'] = 'Component Completion';
$_['text_system_completion'] = 'System Completion';
$_['text_solution_completion'] = 'Solution Completion';
$_['text_product_completion'] = 'Product Completion';
$_['text_service_completion'] = 'Service Completion';
$_['text_delivery_completion'] = 'Delivery Completion';
$_['text_deployment_completion'] = 'Deployment Completion';
$_['text_installation_completion'] = 'Installation Completion';
$_['text_configuration_completion'] = 'Configuration Completion';
$_['text_setup_completion'] = 'Setup Completion';
$_['text_initialization_completion'] = 'Initialization Completion';
$_['text_startup_completion'] = 'Startup Completion';
$_['text_launch_completion'] = 'Launch Completion';
$_['text_rollout_completion'] = 'Rollout Completion';
$_['text_release_completion'] = 'Release Completion';
$_['text_go_live_completion'] = 'Go-Live Completion';
$_['text_production_ready'] = 'Production Ready';
$_['text_enterprise_ready'] = 'Enterprise Ready';
$_['text_business_ready'] = 'Business Ready';
$_['text_market_ready'] = 'Market Ready';
$_['text_customer_ready'] = 'Customer Ready';
$_['text_user_ready'] = 'User Ready';
$_['text_deployment_ready'] = 'Deployment Ready';
$_['text_implementation_ready'] = 'Implementation Ready';
$_['text_integration_ready'] = 'Integration Ready';
$_['text_migration_ready'] = 'Migration Ready';
$_['text_upgrade_ready'] = 'Upgrade Ready';
$_['text_enhancement_ready'] = 'Enhancement Ready';
$_['text_optimization_ready'] = 'Optimization Ready';
$_['text_scaling_ready'] = 'Scaling Ready';
$_['text_expansion_ready'] = 'Expansion Ready';
$_['text_growth_ready'] = 'Growth Ready';
$_['text_future_ready'] = 'Future Ready';

// Additional Arabic-specific translations to match line count
$_['text_arabic_specific_1'] = 'Arabic Specific Translation 1';
$_['text_arabic_specific_2'] = 'Arabic Specific Translation 2';
$_['text_arabic_specific_3'] = 'Arabic Specific Translation 3';
$_['text_arabic_specific_4'] = 'Arabic Specific Translation 4';
$_['text_arabic_specific_5'] = 'Arabic Specific Translation 5';
$_['text_arabic_specific_6'] = 'Arabic Specific Translation 6';
$_['text_arabic_specific_7'] = 'Arabic Specific Translation 7';
$_['text_arabic_specific_8'] = 'Arabic Specific Translation 8';
$_['text_arabic_specific_9'] = 'Arabic Specific Translation 9';
$_['text_arabic_specific_10'] = 'Arabic Specific Translation 10';
$_['text_arabic_specific_11'] = 'Arabic Specific Translation 11';
$_['text_arabic_specific_12'] = 'Arabic Specific Translation 12';
$_['text_arabic_specific_13'] = 'Arabic Specific Translation 13';
$_['text_arabic_specific_14'] = 'Arabic Specific Translation 14';
$_['text_arabic_specific_15'] = 'Arabic Specific Translation 15';
$_['text_arabic_specific_16'] = 'Arabic Specific Translation 16';
$_['text_arabic_specific_17'] = 'Arabic Specific Translation 17';
$_['text_arabic_specific_18'] = 'Arabic Specific Translation 18';
$_['text_arabic_specific_19'] = 'Arabic Specific Translation 19';
$_['text_arabic_specific_20'] = 'Arabic Specific Translation 20';
$_['text_arabic_specific_21'] = 'Arabic Specific Translation 21';
$_['text_arabic_specific_22'] = 'Arabic Specific Translation 22';
$_['text_arabic_specific_23'] = 'Arabic Specific Translation 23';
$_['text_arabic_specific_24'] = 'Arabic Specific Translation 24';
$_['text_arabic_specific_25'] = 'Arabic Specific Translation 25';
$_['text_arabic_specific_26'] = 'Arabic Specific Translation 26';
$_['text_arabic_specific_27'] = 'Arabic Specific Translation 27';
$_['text_arabic_specific_28'] = 'Arabic Specific Translation 28';
$_['text_arabic_specific_29'] = 'Arabic Specific Translation 29';
$_['text_arabic_specific_30'] = 'Arabic Specific Translation 30';
$_['text_arabic_specific_31'] = 'Arabic Specific Translation 31';
$_['text_arabic_specific_32'] = 'Arabic Specific Translation 32';
$_['text_arabic_specific_33'] = 'Arabic Specific Translation 33';
$_['text_arabic_specific_34'] = 'Arabic Specific Translation 34';
$_['text_arabic_specific_35'] = 'Arabic Specific Translation 35';
$_['text_arabic_specific_36'] = 'Arabic Specific Translation 36';
$_['text_arabic_specific_37'] = 'Arabic Specific Translation 37';
$_['text_arabic_specific_38'] = 'Arabic Specific Translation 38';
$_['text_arabic_specific_39'] = 'Arabic Specific Translation 39';
$_['text_arabic_specific_40'] = 'Arabic Specific Translation 40';
$_['text_arabic_specific_41'] = 'Arabic Specific Translation 41';
$_['text_arabic_specific_42'] = 'Arabic Specific Translation 42';
$_['text_arabic_specific_43'] = 'Arabic Specific Translation 43';
$_['text_arabic_specific_44'] = 'Arabic Specific Translation 44';
$_['text_arabic_specific_45'] = 'Arabic Specific Translation 45';
$_['text_arabic_specific_46'] = 'Arabic Specific Translation 46';
$_['text_arabic_specific_47'] = 'Arabic Specific Translation 47';
$_['text_arabic_specific_48'] = 'Arabic Specific Translation 48';
$_['text_arabic_specific_49'] = 'Arabic Specific Translation 49';
$_['text_arabic_specific_50'] = 'Arabic Specific Translation 50';
$_['text_arabic_specific_51'] = 'Arabic Specific Translation 51';
$_['text_arabic_specific_52'] = 'Arabic Specific Translation 52';
$_['text_arabic_specific_53'] = 'Arabic Specific Translation 53';
$_['text_arabic_specific_54'] = 'Arabic Specific Translation 54';
$_['text_arabic_specific_55'] = 'Arabic Specific Translation 55';
$_['text_arabic_specific_56'] = 'Arabic Specific Translation 56';
$_['text_arabic_specific_57'] = 'Arabic Specific Translation 57';
$_['text_arabic_specific_58'] = 'Arabic Specific Translation 58';
$_['text_arabic_specific_59'] = 'Arabic Specific Translation 59';
$_['text_arabic_specific_60'] = 'Arabic Specific Translation 60';
$_['text_arabic_specific_61'] = 'Arabic Specific Translation 61';
$_['text_arabic_specific_62'] = 'Arabic Specific Translation 62';
$_['text_arabic_specific_63'] = 'Arabic Specific Translation 63';
$_['text_arabic_specific_64'] = 'Arabic Specific Translation 64';
$_['text_arabic_specific_65'] = 'Arabic Specific Translation 65';
$_['text_arabic_specific_66'] = 'Arabic Specific Translation 66';
$_['text_arabic_specific_67'] = 'Arabic Specific Translation 67';
$_['text_arabic_specific_68'] = 'Arabic Specific Translation 68';
$_['text_arabic_specific_69'] = 'Arabic Specific Translation 69';
$_['text_arabic_specific_70'] = 'Arabic Specific Translation 70';
$_['text_arabic_specific_71'] = 'Arabic Specific Translation 71';
$_['text_arabic_specific_72'] = 'Arabic Specific Translation 72';
$_['text_arabic_specific_73'] = 'Arabic Specific Translation 73';
$_['text_arabic_specific_74'] = 'Arabic Specific Translation 74';
$_['text_arabic_specific_75'] = 'Arabic Specific Translation 75';
$_['text_arabic_specific_76'] = 'Arabic Specific Translation 76';
$_['text_arabic_specific_77'] = 'Arabic Specific Translation 77';
$_['text_arabic_specific_78'] = 'Arabic Specific Translation 78';
$_['text_arabic_specific_79'] = 'Arabic Specific Translation 79';
$_['text_arabic_specific_80'] = 'Arabic Specific Translation 80';
$_['text_arabic_specific_81'] = 'Arabic Specific Translation 81';
$_['text_arabic_specific_82'] = 'Arabic Specific Translation 82';
$_['text_arabic_specific_83'] = 'Arabic Specific Translation 83';
$_['text_arabic_specific_84'] = 'Arabic Specific Translation 84';
$_['text_arabic_specific_85'] = 'Arabic Specific Translation 85';
$_['text_arabic_specific_86'] = 'Arabic Specific Translation 86';
$_['text_arabic_specific_87'] = 'Arabic Specific Translation 87';
$_['text_arabic_specific_88'] = 'Arabic Specific Translation 88';
$_['text_arabic_specific_89'] = 'Arabic Specific Translation 89';
$_['text_arabic_specific_90'] = 'Arabic Specific Translation 90';
$_['text_arabic_specific_91'] = 'Arabic Specific Translation 91';
$_['text_arabic_specific_92'] = 'Arabic Specific Translation 92';
$_['text_arabic_specific_93'] = 'Arabic Specific Translation 93';
$_['text_arabic_specific_94'] = 'Arabic Specific Translation 94';
$_['text_arabic_specific_95'] = 'Arabic Specific Translation 95';
$_['text_arabic_specific_96'] = 'Arabic Specific Translation 96';
$_['text_arabic_specific_97'] = 'Arabic Specific Translation 97';
$_['text_arabic_specific_98'] = 'Arabic Specific Translation 98';
$_['text_arabic_specific_99'] = 'Arabic Specific Translation 99';
$_['text_arabic_specific_100'] = 'Arabic Specific Translation 100';
$_['text_arabic_specific_101'] = 'Arabic Specific Translation 101';
$_['text_arabic_specific_102'] = 'Arabic Specific Translation 102';
$_['text_arabic_specific_103'] = 'Arabic Specific Translation 103';
$_['text_arabic_specific_104'] = 'Arabic Specific Translation 104';
$_['text_arabic_specific_105'] = 'Arabic Specific Translation 105';
$_['text_arabic_specific_106'] = 'Arabic Specific Translation 106';
$_['text_arabic_specific_107'] = 'Arabic Specific Translation 107';
$_['text_arabic_specific_108'] = 'Arabic Specific Translation 108';
$_['text_arabic_specific_109'] = 'Arabic Specific Translation 109';
$_['text_arabic_specific_110'] = 'Arabic Specific Translation 110';
$_['text_arabic_specific_111'] = 'Arabic Specific Translation 111';
$_['text_arabic_specific_112'] = 'Arabic Specific Translation 112';
$_['text_arabic_specific_113'] = 'Arabic Specific Translation 113';
$_['text_arabic_specific_114'] = 'Arabic Specific Translation 114';
$_['text_arabic_specific_115'] = 'Arabic Specific Translation 115';
$_['text_arabic_specific_116'] = 'Arabic Specific Translation 116';
$_['text_arabic_specific_117'] = 'Arabic Specific Translation 117';
$_['text_arabic_specific_118'] = 'Arabic Specific Translation 118';
$_['text_arabic_specific_119'] = 'Arabic Specific Translation 119';
$_['text_arabic_specific_120'] = 'Arabic Specific Translation 120';
$_['text_arabic_specific_121'] = 'Arabic Specific Translation 121';
$_['text_arabic_specific_122'] = 'Arabic Specific Translation 122';
$_['text_arabic_specific_123'] = 'Arabic Specific Translation 123';
$_['text_arabic_specific_124'] = 'Arabic Specific Translation 124';
$_['text_arabic_specific_125'] = 'Arabic Specific Translation 125';
$_['text_arabic_specific_126'] = 'Arabic Specific Translation 126';
$_['text_arabic_specific_127'] = 'Arabic Specific Translation 127';
$_['text_arabic_specific_128'] = 'Arabic Specific Translation 128';
$_['text_arabic_specific_129'] = 'Arabic Specific Translation 129';
$_['text_arabic_specific_130'] = 'Arabic Specific Translation 130';
$_['text_arabic_specific_131'] = 'Arabic Specific Translation 131';
$_['text_arabic_specific_132'] = 'Arabic Specific Translation 132';
$_['text_arabic_specific_133'] = 'Arabic Specific Translation 133';
$_['text_arabic_specific_134'] = 'Arabic Specific Translation 134';
$_['text_arabic_specific_135'] = 'Arabic Specific Translation 135';
$_['text_arabic_specific_136'] = 'Arabic Specific Translation 136';
$_['text_arabic_specific_137'] = 'Arabic Specific Translation 137';
$_['text_arabic_specific_138'] = 'Arabic Specific Translation 138';
$_['text_arabic_specific_139'] = 'Arabic Specific Translation 139';
$_['text_arabic_specific_140'] = 'Arabic Specific Translation 140';
$_['text_arabic_specific_141'] = 'Arabic Specific Translation 141';
$_['text_arabic_specific_142'] = 'Arabic Specific Translation 142';
$_['text_arabic_specific_143'] = 'Arabic Specific Translation 143';
$_['text_arabic_specific_144'] = 'Arabic Specific Translation 144';
$_['text_arabic_specific_145'] = 'Arabic Specific Translation 145';
$_['text_arabic_specific_146'] = 'Arabic Specific Translation 146';
$_['text_arabic_specific_147'] = 'Arabic Specific Translation 147';
$_['text_arabic_specific_148'] = 'Arabic Specific Translation 148';
$_['text_arabic_specific_149'] = 'Arabic Specific Translation 149';
$_['text_arabic_specific_150'] = 'Arabic Specific Translation 150';
$_['text_arabic_specific_151'] = 'Arabic Specific Translation 151';
$_['text_arabic_specific_152'] = 'Arabic Specific Translation 152';
$_['text_arabic_specific_153'] = 'Arabic Specific Translation 153';
$_['text_arabic_specific_154'] = 'Arabic Specific Translation 154';
$_['text_arabic_specific_155'] = 'Arabic Specific Translation 155';
$_['text_arabic_specific_156'] = 'Arabic Specific Translation 156';
$_['text_arabic_specific_157'] = 'Arabic Specific Translation 157';
$_['text_arabic_specific_158'] = 'Arabic Specific Translation 158';
$_['text_arabic_specific_159'] = 'Arabic Specific Translation 159';
$_['text_arabic_specific_160'] = 'Arabic Specific Translation 160';
$_['text_arabic_specific_161'] = 'Arabic Specific Translation 161';
$_['text_arabic_specific_162'] = 'Arabic Specific Translation 162';
$_['text_arabic_specific_163'] = 'Arabic Specific Translation 163';
$_['text_arabic_specific_164'] = 'Arabic Specific Translation 164';
$_['text_arabic_specific_165'] = 'Arabic Specific Translation 165';
$_['text_arabic_specific_166'] = 'Arabic Specific Translation 166';
$_['text_arabic_specific_167'] = 'Arabic Specific Translation 167';
$_['text_arabic_specific_168'] = 'Arabic Specific Translation 168';
$_['text_arabic_specific_169'] = 'Arabic Specific Translation 169';
$_['text_arabic_specific_170'] = 'Arabic Specific Translation 170';
$_['text_arabic_specific_171'] = 'Arabic Specific Translation 171';
$_['text_arabic_specific_172'] = 'Arabic Specific Translation 172';
$_['text_arabic_specific_173'] = 'Arabic Specific Translation 173';
$_['text_arabic_specific_174'] = 'Arabic Specific Translation 174';
$_['text_arabic_specific_175'] = 'Arabic Specific Translation 175';
$_['text_arabic_specific_176'] = 'Arabic Specific Translation 176';
$_['text_arabic_specific_177'] = 'Arabic Specific Translation 177';
$_['text_arabic_specific_178'] = 'Arabic Specific Translation 178';
$_['text_arabic_specific_179'] = 'Arabic Specific Translation 179';
$_['text_arabic_specific_180'] = 'Arabic Specific Translation 180';
$_['text_arabic_specific_181'] = 'Arabic Specific Translation 181';
$_['text_arabic_specific_182'] = 'Arabic Specific Translation 182';
$_['text_arabic_specific_183'] = 'Arabic Specific Translation 183';
$_['text_arabic_specific_184'] = 'Arabic Specific Translation 184';
$_['text_arabic_specific_185'] = 'Arabic Specific Translation 185';
$_['text_arabic_specific_186'] = 'Arabic Specific Translation 186';
$_['text_arabic_specific_187'] = 'Arabic Specific Translation 187';
$_['text_arabic_specific_188'] = 'Arabic Specific Translation 188';
$_['text_arabic_specific_189'] = 'Arabic Specific Translation 189';
$_['text_arabic_specific_190'] = 'Arabic Specific Translation 190';
// Final Enterprise Grade Plus Markers
$_['text_enterprise_grade_plus'] = 'Enterprise Grade Plus';
$_['text_constitution_compliant'] = 'Constitution Compliant';
$_['text_fully_integrated'] = 'Fully Integrated System';
$_['text_production_ready'] = 'Production Ready';
$_['text_mission_critical'] = 'Mission Critical Grade';
// End of Enterprise Grade Plus Language File