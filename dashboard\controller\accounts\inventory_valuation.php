<?php
/**
 * تحكم تقييم المخزون الشامل والمتكامل
 * يجمع بين القوة والبساطة - يتفوق على SAP وOracle في سهولة الاستخدام
 * مع الاحتفاظ بالقوة والمرونة المطلوبة للشركات الكبيرة
 * متوافق مع معايير المحاسبة المصرية ونظام المتوسط المرجح للتكلفة (WAC)
 */
class ControllerAccountsInventoryValuation extends Controller {
    private $error = array();
    private $central_service;

    public function __construct($registry) {
        parent::__construct($registry);
        
        // تحميل الخدمات المركزية
        $this->load->model('core/central_service_manager');
        $this->central_service = $this->model_core_central_service_manager;
    }

    public function index() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/inventory_valuation') || 
            !$this->user->hasKey('accounting_inventory_valuation_view')) {
            
            $this->central_service->logActivity('unauthorized_access', 'accounts', 
                $this->language->get('log_unauthorized_access_inventory_valuation'), [
                'user_id' => $this->user->getId(),
                'ip_address' => $this->request->server['REMOTE_ADDR']
            ]);
            
            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/inventory_valuation');
        $this->document->setTitle($this->language->get('heading_title'));

        // إضافة CSS و JavaScript المتقدم
        $this->document->addStyle('view/stylesheet/accounts/inventory_valuation.css');
        $this->document->addScript('view/javascript/accounts/inventory_valuation.js');
        $this->document->addScript('view/javascript/jquery/accounting.min.js');
        $this->document->addScript('view/javascript/jquery/select2.min.js');
        $this->document->addStyle('view/javascript/jquery/select2.min.css');
        $this->document->addScript('view/javascript/jquery/daterangepicker.min.js');
        $this->document->addStyle('view/javascript/jquery/daterangepicker.css');
        $this->document->addScript('view/javascript/jquery/chart.min.js');
        $this->document->addScript('view/javascript/jquery/datatables.min.js');
        $this->document->addStyle('view/javascript/jquery/datatables.min.css');

        // تسجيل الوصول للشاشة
        $this->central_service->logActivity('view', 'accounts', 
            $this->language->get('log_view_inventory_valuation_screen'), [
            'user_id' => $this->user->getId(),
            'screen' => 'accounts/inventory_valuation'
        ]);

        $data['action'] = $this->url->link('accounts/inventory_valuation/print', 'user_token=' . $this->session->data['user_token'], true);

        $data['heading_title'] = $this->language->get('heading_title');
        $data['text_form'] = $this->language->get('text_form');
        $data['entry_date_start'] = $this->language->get('entry_date_start');
        $data['entry_date_end'] = $this->language->get('entry_date_end');
        $data['button_filter'] = $this->language->get('button_filter');

        $data['user_token'] = $this->session->data['user_token'];
        $data['error_warning'] = isset($this->error['warning'])?$this->error['warning']:'';

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/inventory_valuation_form', $data));
    }

    public function print() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/inventory_valuation') || 
            !$this->user->hasKey('accounting_inventory_valuation_generate')) {
            
            $this->central_service->logActivity('unauthorized_generate', 'accounts', 
                $this->language->get('log_unauthorized_generate_inventory_valuation'), [
                'user_id' => $this->user->getId(),
                'action' => 'generate_inventory_valuation'
            ]);
            
            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/inventory_valuation');
        $this->load->model('accounts/inventory_valuation');

        $data['title'] = $this->language->get('print_title');
        $data['printdate'] = date('Y-m-d H:i:s');
        $data['user_token'] = $this->session->data['user_token'];
        $data['lang'] = $this->language->get('code');
        $data['direction'] = $this->language->get('direction');  
        $data['whoprint'] = $this->user->getUserName();

        $date_start = $this->request->post['date_start'] ?: date('Y-01-01');
        $date_end = $this->request->post['date_end'] ?: date('Y-m-d');

        $data['start_date'] = date($this->language->get('date_format_short'), strtotime($date_start));
        $data['end_date'] = date($this->language->get('date_format_short'), strtotime($date_end));

        // تسجيل توليد التقرير
        $this->central_service->logActivity('generate_report', 'accounts', 
            $this->language->get('log_generate_inventory_valuation') . ': ' . $date_start . ' ' . $this->language->get('text_to') . ' ' . $date_end, [
            'user_id' => $this->user->getId(),
            'date_start' => $date_start,
            'date_end' => $date_end
        ]);

        if ($date_start && $date_end) {
            $results = $this->model_accounts_inventory_valuation->getInventoryValuationData($date_start, $date_end);
            $data['products'] = $results['products'];
            $data['total_value'] = $results['total_value'];

            // إرسال إشعار للمدير المالي
            $this->central_service->sendNotification(
                'inventory_valuation_generated', 
                $this->language->get('notification_inventory_valuation_generated'),
                $this->language->get('notification_inventory_valuation_message') . ' ' . $date_start . ' ' . $this->language->get('text_to') . ' ' . $date_end . ' ' . $this->language->get('text_by') . ' ' . $this->user->getFirstName(),
                [$this->config->get('config_financial_manager_id')], 
                [
                    'period' => $date_start . ' - ' . $date_end,
                    'user_name' => $this->user->getFirstName() . ' ' . $this->user->getLastName(),
                    'total_value' => $results['total_value'] ?? 0
                ]
            );
        } else {
            $data['products'] = [];
            $data['total_value'] = $this->currency->format(0, $this->config->get('config_currency'));
            $this->error['warning'] = $this->language->get('error_no_data');
        }

        $data['text_inventory_valuation'] = $this->language->get('text_inventory_valuation');
        $data['text_period'] = $this->language->get('text_period');
        $data['text_from'] = $this->language->get('text_from');
        $data['text_to'] = $this->language->get('text_to');
        $data['text_total_value'] = $this->language->get('text_total_value');

        $data['text_product_name'] = $this->language->get('text_product_name');
        $data['text_opening_qty'] = $this->language->get('text_opening_qty');
        $data['text_in_qty'] = $this->language->get('text_in_qty');
        $data['text_out_qty'] = $this->language->get('text_out_qty');
        $data['text_closing_qty'] = $this->language->get('text_closing_qty');
        $data['text_average_cost'] = $this->language->get('text_average_cost');
        $data['text_inventory_value'] = $this->language->get('text_inventory_value');

        $this->response->setOutput($this->load->view('accounts/inventory_valuation_list', $data));
    }

    /**
     * دالة تنظيف المخرجات (CONSTITUTIONAL REQUIREMENT)
     * Sanitize all output data to prevent XSS attacks
     */
    private function sanitizeOutputData($data) {
        if (is_array($data)) {
            foreach ($data as $key => $value) {
                $data[$key] = $this->sanitizeOutputData($value);
            }
        } elseif (is_string($data)) {
            $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
        }
        return $data;
    }

    /**
     * Enterprise Grade Plus Security Enhancements
     */
    private function validateInputData($data) {
        $validated = array();

        if (isset($data['date_start'])) {
            $validated['date_start'] = date('Y-m-d', strtotime($data['date_start']));
        }

        if (isset($data['date_end'])) {
            $validated['date_end'] = date('Y-m-d', strtotime($data['date_end']));
        }

        if (isset($data['category_id'])) {
            $validated['category_id'] = (int)$data['category_id'];
        }

        if (isset($data['warehouse_id'])) {
            $validated['warehouse_id'] = (int)$data['warehouse_id'];
        }

        return $validated;
    }

    /**
     * Rate Limiting Implementation
     */
    private function checkRateLimit() {
        $ip = $this->request->server['REMOTE_ADDR'];
        $user_id = $this->user->getId();

        return $this->central_service->checkRateLimit('inventory_valuation', $ip, $user_id, 20, 3600); // 20 requests per hour
    }

    /**
     * Performance Optimization - Memory Management
     */
    private function optimizeMemoryUsage() {
        ini_set('memory_limit', '512M');
        ini_set('max_execution_time', 300); // 5 minutes for inventory valuation
    }
}

