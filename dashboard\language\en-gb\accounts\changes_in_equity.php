<?php
// Heading
$_['heading_title']                    = 'Statement of Changes in Equity';

// Text
$_['text_success']                     = 'Success: Statement of Changes in Equity has been generated successfully!';
$_['text_list']                        = 'Statement of Changes in Equity List';
$_['text_form']                        = 'Statement of Changes in Equity Form';
$_['text_view']                        = 'View Statement of Changes in Equity';
$_['text_generate']                    = 'Generate Statement of Changes in Equity';
$_['text_export']                      = 'Export Statement of Changes in Equity';
$_['text_compare']                     = 'Compare Statement of Changes in Equity';
$_['text_print']                       = 'Print Statement of Changes in Equity';
$_['text_changes_in_equity']           = 'Statement of Changes in Equity';
$_['text_period']                      = 'Period';
$_['text_from']                        = 'From';
$_['text_to']                          = 'To';
$_['text_loading']                     = 'Loading...';
$_['text_processing']                  = 'Processing...';
$_['text_success_generate']            = 'Statement of Changes in Equity generated successfully!';
$_['text_success_export']              = 'Statement of Changes in Equity exported successfully!';
$_['text_success_compare']             = 'Comparison completed successfully!';

// Equity Components
$_['text_share_capital']               = 'Share Capital';
$_['text_authorized_capital']          = 'Authorized Capital';
$_['text_issued_capital']              = 'Issued Capital';
$_['text_paid_capital']                = 'Paid-up Capital';
$_['text_retained_earnings']           = 'Retained Earnings';
$_['text_accumulated_losses']          = 'Accumulated Losses';
$_['text_legal_reserve']               = 'Legal Reserve';
$_['text_statutory_reserve']           = 'Statutory Reserve';
$_['text_general_reserve']             = 'General Reserve';
$_['text_special_reserve']             = 'Special Reserve';
$_['text_revaluation_reserve']         = 'Revaluation Reserve';
$_['text_translation_reserve']         = 'Foreign Currency Translation Reserve';
$_['text_treasury_shares']             = 'Treasury Shares';
$_['text_other_comprehensive_income']  = 'Other Comprehensive Income';
$_['text_minority_interest']           = 'Minority Interest';

// Movements
$_['text_opening_balance']             = 'Opening Balance';
$_['text_closing_balance']             = 'Closing Balance';
$_['text_movement']                    = 'Movement';
$_['text_increase']                    = 'Increase';
$_['text_decrease']                    = 'Decrease';
$_['text_net_change']                  = 'Net Change';
$_['text_capital_increase']            = 'Capital Increase';
$_['text_capital_decrease']            = 'Capital Decrease';
$_['text_profit_distribution']         = 'Profit Distribution';
$_['text_dividend_payment']            = 'Dividend Payment';
$_['text_bonus_shares']                = 'Bonus Shares';
$_['text_rights_issue']                = 'Rights Issue';
$_['text_share_premium']               = 'Share Premium';
$_['text_share_buyback']               = 'Share Buyback';
$_['text_reserve_transfer']            = 'Transfer to Reserves';
$_['text_prior_period_adjustment']     = 'Prior Period Adjustment';

// Column
$_['column_component']                 = 'Equity Component';
$_['column_opening_balance']           = 'Opening Balance';
$_['column_additions']                 = 'Additions';
$_['column_deductions']                = 'Deductions';
$_['column_transfers']                 = 'Transfers';
$_['column_closing_balance']           = 'Closing Balance';
$_['column_current_period']            = 'Current Period';
$_['column_previous_period']           = 'Previous Period';
$_['column_variance']                  = 'Variance';
$_['column_percentage']                = 'Percentage';

// Entry
$_['entry_date_start']                 = 'Period Start Date';
$_['entry_date_end']                   = 'Period End Date';
$_['entry_comparison_start']           = 'Comparison Period Start Date';
$_['entry_comparison_end']             = 'Comparison Period End Date';
$_['entry_branch']                     = 'Branch';
$_['entry_include_zero_balances']      = 'Include Zero Balance Accounts';
$_['entry_show_comparative']           = 'Show Comparative';
$_['entry_show_percentages']           = 'Show Percentages';
$_['entry_equity_accounts']            = 'Equity Accounts';
$_['entry_export_format']              = 'Export Format';

// Button
$_['button_generate']                  = 'Generate';
$_['button_export']                    = 'Export';
$_['button_compare']                   = 'Compare';
$_['button_print']                     = 'Print';
$_['button_filter']                    = 'Filter';
$_['button_reset']                     = 'Reset';
$_['button_close']                     = 'Close';

// Tab
$_['tab_general']                      = 'General';
$_['tab_filters']                      = 'Filters';
$_['tab_options']                      = 'Options';
$_['tab_comparison']                   = 'Comparison';
$_['tab_capital']                      = 'Capital';
$_['tab_reserves']                     = 'Reserves';
$_['tab_earnings']                     = 'Retained Earnings';

// Help
$_['help_date_start']                  = 'Select the start date for changes in equity period';
$_['help_date_end']                    = 'Select the end date for changes in equity period';
$_['help_comparison']                  = 'Select period to compare with current period';
$_['help_equity_accounts']             = 'Select specific equity accounts';

// Error
$_['error_warning']                    = 'Warning: Please check the form carefully for errors!';
$_['error_permission']                 = 'Warning: You do not have permission to access Statement of Changes in Equity!';
$_['error_date_start']                 = 'Period start date is required!';
$_['error_date_end']                   = 'Period end date is required!';
$_['error_date_range']                 = 'Start date must be before end date!';
$_['error_no_data']                    = 'No data for selected period!';
$_['error_export']                     = 'Error exporting data!';
$_['error_form']                       = 'Form data error!';
$_['error_missing_data']               = 'Missing data!';

// Export Formats
$_['text_excel']                       = 'Excel';
$_['text_pdf']                         = 'PDF';
$_['text_csv']                         = 'CSV';

// Print Options
$_['text_print_title']                 = 'Statement of Changes in Equity';
$_['print_title']                      = 'Print Statement of Changes in Equity';
$_['text_print_date']                  = 'Print Date';
$_['text_print_user']                  = 'Printed By';

// Status Messages
$_['text_generating']                  = 'Generating statement of changes in equity...';
$_['text_exporting']                   = 'Exporting data...';
$_['text_comparing']                   = 'Performing comparison...';
$_['text_completed']                   = 'Completed successfully!';

// Totals
$_['text_total_equity']                = 'Total Equity';
$_['text_total_opening']               = 'Total Opening Balance';
$_['text_total_closing']               = 'Total Closing Balance';
$_['text_total_movement']              = 'Total Movement';
$_['text_total']                       = 'Total';

// Egyptian Corporate Law
$_['text_egyptian_corporate_law']      = 'Compliant with Egyptian Corporate Law';
$_['text_eas_compliant']               = 'Compliant with Egyptian Accounting Standards';
$_['text_eta_ready']                   = 'Ready for ETA Integration';
$_['text_egyptian_gaap']               = 'According to Egyptian Generally Accepted Accounting Principles';

// Additional Fields
$_['text_account_name']                = 'Account Name';
$_['text_account']                     = 'Account';

// Controller language variables
$_['log_unauthorized_access_changes_in_equity'] = 'Unauthorized access attempt to changes in equity';
$_['log_view_changes_in_equity_screen'] = 'View changes in equity screen';
$_['log_unauthorized_generate_equity_changes'] = 'Unauthorized equity changes generation attempt';
$_['log_generate_equity_changes_period'] = 'Generate changes in equity for period';
$_['text_equity_changes_generated'] = 'Changes in Equity Generated';
$_['text_equity_changes_generated_message'] = 'Changes in equity generated for period';
$_['text_by_user'] = 'by';
$_['log_view_equity_changes_report'] = 'View changes in equity report';
$_['log_unauthorized_export_equity_changes'] = 'Unauthorized equity changes export attempt';
$_['log_export_equity_changes'] = 'Export changes in equity';
$_['text_equity_changes_exported'] = 'Changes in Equity Exported';
$_['text_equity_changes_exported_message'] = 'Changes in equity exported in format';

// Additional variables for template
$_['text_actions'] = 'Actions';
$_['text_generate_statement'] = 'Generate Statement';
$_['text_export_options'] = 'Export Options';
$_['text_equity_analysis'] = 'Equity Analysis';
$_['text_statement_filters'] = 'Statement Filters';
$_['text_all_branches'] = 'All Branches';
$_['entry_currency'] = 'Currency';

// Missing variables from audit report - Critical fixes
$_['accounts/changes_in_equity'] = '';
$_['code']                             = 'Code';
$_['date_format_short']                = 'm/d/Y';
$_['direction']                        = 'ltr';
$_['lang']                             = 'en';
?>
