{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        {% if has_permission %}
        <button type="button" id="button-add" data-toggle="tooltip" title="{{ button_add }}" class="btn btn-primary">
          <i class="fa fa-plus"></i> {{ button_add }}
        </button>
        <button type="button" id="button-ai-forecast" data-toggle="tooltip" title="{{ button_ai_forecast }}" class="btn btn-warning">
          <i class="fa fa-magic"></i> {{ button_ai_forecast }}
        </button>
        <button type="button" id="button-export" data-toggle="tooltip" title="{{ button_export }}" class="btn btn-success">
          <i class="fa fa-download"></i> {{ button_export }}
        </button>
        {% endif %}
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    {% if success %}
    <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    
    <!-- مؤشرات الأداء الرئيسية -->
    <div class="row">
      <div class="col-lg-3 col-xs-6">
        <div class="small-box bg-aqua">
          <div class="inner">
            <h3>{{ kpi.current_cash_balance }}</h3>
            <p>{{ text_current_cash_balance }}</p>
          </div>
          <div class="icon">
            <i class="fa fa-money"></i>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-xs-6">
        <div class="small-box bg-green">
          <div class="inner">
            <h3>{{ kpi.projected_inflow }}</h3>
            <p>{{ text_projected_inflow }}</p>
          </div>
          <div class="icon">
            <i class="fa fa-arrow-down"></i>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-xs-6">
        <div class="small-box bg-yellow">
          <div class="inner">
            <h3>{{ kpi.projected_outflow }}</h3>
            <p>{{ text_projected_outflow }}</p>
          </div>
          <div class="icon">
            <i class="fa fa-arrow-up"></i>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-xs-6">
        <div class="small-box bg-red">
          <div class="inner">
            <h3>{{ kpi.net_cash_flow }}</h3>
            <p>{{ text_net_cash_flow }}</p>
          </div>
          <div class="icon">
            <i class="fa fa-line-chart"></i>
          </div>
        </div>
      </div>
    </div>

    <!-- الرسم البياني للتنبؤ -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-line-chart"></i> {{ text_cash_flow_chart }}</h3>
      </div>
      <div class="panel-body">
        <canvas id="cashFlowChart" width="400" height="100"></canvas>
      </div>
    </div>

    <!-- فلاتر البحث -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-filter"></i> {{ text_filter }}</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-md-3">
            <div class="form-group">
              <label class="control-label">{{ text_forecast_name }}</label>
              <input type="text" name="filter_forecast_name" value="{{ filter_forecast_name }}" placeholder="{{ text_forecast_name }}" class="form-control" />
            </div>
          </div>
          <div class="col-md-3">
            <div class="form-group">
              <label class="control-label">{{ text_forecast_period }}</label>
              <select name="filter_forecast_period" class="form-control">
                <option value="">{{ text_all_periods }}</option>
                <option value="weekly" {% if filter_forecast_period == 'weekly' %}selected{% endif %}>{{ text_weekly }}</option>
                <option value="monthly" {% if filter_forecast_period == 'monthly' %}selected{% endif %}>{{ text_monthly }}</option>
                <option value="quarterly" {% if filter_forecast_period == 'quarterly' %}selected{% endif %}>{{ text_quarterly }}</option>
                <option value="yearly" {% if filter_forecast_period == 'yearly' %}selected{% endif %}>{{ text_yearly }}</option>
              </select>
            </div>
          </div>
          <div class="col-md-3">
            <div class="form-group">
              <label class="control-label">{{ text_forecast_method }}</label>
              <select name="filter_forecast_method" class="form-control">
                <option value="">{{ text_all_methods }}</option>
                <option value="moving_average" {% if filter_forecast_method == 'moving_average' %}selected{% endif %}>{{ text_moving_average }}</option>
                <option value="linear_regression" {% if filter_forecast_method == 'linear_regression' %}selected{% endif %}>{{ text_linear_regression }}</option>
                <option value="seasonal_decomposition" {% if filter_forecast_method == 'seasonal_decomposition' %}selected{% endif %}>{{ text_seasonal_decomposition }}</option>
                <option value="ai_prediction" {% if filter_forecast_method == 'ai_prediction' %}selected{% endif %}>{{ text_ai_prediction }}</option>
              </select>
            </div>
          </div>
          <div class="col-md-3">
            <div class="form-group">
              <label class="control-label">{{ text_status }}</label>
              <select name="filter_status" class="form-control">
                <option value="">{{ text_all_statuses }}</option>
                <option value="draft" {% if filter_status == 'draft' %}selected{% endif %}>{{ text_status_draft }}</option>
                <option value="active" {% if filter_status == 'active' %}selected{% endif %}>{{ text_status_active }}</option>
                <option value="archived" {% if filter_status == 'archived' %}selected{% endif %}>{{ text_status_archived }}</option>
              </select>
            </div>
          </div>
        </div>
        <div class="text-right">
          <button type="button" id="button-filter" class="btn btn-default">
            <i class="fa fa-search"></i> {{ button_filter }}
          </button>
        </div>
      </div>
    </div>

    <!-- قائمة التنبؤات -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-list"></i> {{ text_forecast_list }}</h3>
      </div>
      <div class="panel-body">
        <form action="{{ delete }}" method="post" enctype="multipart/form-data" id="form-forecast">
          <div class="table-responsive">
            <table class="table table-bordered table-hover">
              <thead>
                <tr>
                  <td style="width: 1px;" class="text-center">
                    <input type="checkbox" onclick="$('input[name*=\'selected\']').prop('checked', this.checked);" />
                  </td>
                  <td class="text-left">
                    {% if sort == 'forecast_name' %}
                    <a href="{{ sort_forecast_name }}" class="{% if order == 'ASC' %}asc{% else %}desc{% endif %}">{{ column_forecast_name }}</a>
                    {% else %}
                    <a href="{{ sort_forecast_name }}">{{ column_forecast_name }}</a>
                    {% endif %}
                  </td>
                  <td class="text-left">{{ column_forecast_period }}</td>
                  <td class="text-left">{{ column_forecast_method }}</td>
                  <td class="text-right">{{ column_accuracy_score }}</td>
                  <td class="text-center">{{ column_status }}</td>
                  <td class="text-left">{{ column_date_created }}</td>
                  <td class="text-right">{{ column_action }}</td>
                </tr>
              </thead>
              <tbody>
                {% if forecasts %}
                {% for forecast in forecasts %}
                <tr>
                  <td class="text-center">
                    {% if forecast.selected %}
                    <input type="checkbox" name="selected[]" value="{{ forecast.forecast_id }}" checked="checked" />
                    {% else %}
                    <input type="checkbox" name="selected[]" value="{{ forecast.forecast_id }}" />
                    {% endif %}
                  </td>
                  <td class="text-left">
                    <a href="{{ forecast.view }}">{{ forecast.forecast_name }}</a>
                  </td>
                  <td class="text-left">{{ forecast.forecast_period }}</td>
                  <td class="text-left">
                    {% if forecast.forecast_method == 'ai_prediction' %}
                      <span class="label label-warning">{{ text_ai_prediction }}</span>
                    {% else %}
                      {{ forecast.forecast_method }}
                    {% endif %}
                  </td>
                  <td class="text-right">
                    {% if forecast.accuracy_score %}
                      <span class="label {% if forecast.accuracy_score >= 90 %}label-success{% elseif forecast.accuracy_score >= 70 %}label-warning{% else %}label-danger{% endif %}">
                        {{ forecast.accuracy_score }}%
                      </span>
                    {% else %}
                      <span class="label label-default">{{ text_no_data }}</span>
                    {% endif %}
                  </td>
                  <td class="text-center">
                    {% if forecast.status == 'draft' %}
                      <span class="label label-default">{{ text_status_draft }}</span>
                    {% elseif forecast.status == 'active' %}
                      <span class="label label-success">{{ text_status_active }}</span>
                    {% elseif forecast.status == 'archived' %}
                      <span class="label label-info">{{ text_status_archived }}</span>
                    {% endif %}
                  </td>
                  <td class="text-left">{{ forecast.date_created }}</td>
                  <td class="text-right">
                    <div class="btn-group">
                      <a href="{{ forecast.view }}" data-toggle="tooltip" title="{{ button_view }}" class="btn btn-info btn-xs">
                        <i class="fa fa-eye"></i>
                      </a>
                      {% if forecast.edit %}
                      <a href="{{ forecast.edit }}" data-toggle="tooltip" title="{{ button_edit }}" class="btn btn-primary btn-xs">
                        <i class="fa fa-pencil"></i>
                      </a>
                      {% endif %}
                      {% if forecast.generate %}
                      <a href="{{ forecast.generate }}" data-toggle="tooltip" title="{{ button_generate }}" class="btn btn-success btn-xs">
                        <i class="fa fa-refresh"></i>
                      </a>
                      {% endif %}
                      {% if forecast.scenario %}
                      <a href="{{ forecast.scenario }}" data-toggle="tooltip" title="{{ button_scenario }}" class="btn btn-warning btn-xs">
                        <i class="fa fa-sitemap"></i>
                      </a>
                      {% endif %}
                    </div>
                  </td>
                </tr>
                {% endfor %}
                {% else %}
                <tr>
                  <td class="text-center" colspan="8">{{ text_no_results }}</td>
                </tr>
                {% endif %}
              </tbody>
            </table>
          </div>
        </form>
        <div class="row">
          <div class="col-sm-6 text-left">{{ pagination }}</div>
          <div class="col-sm-6 text-right">{{ results }}</div>
        </div>
      </div>
    </div>

    <!-- السيناريوهات النشطة -->
    {% if active_scenarios %}
    <div class="panel panel-info">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-sitemap"></i> {{ text_active_scenarios }}</h3>
      </div>
      <div class="panel-body">
        <div class="table-responsive">
          <table class="table table-bordered table-striped">
            <thead>
              <tr>
                <th>{{ column_scenario_name }}</th>
                <th>{{ column_scenario_type }}</th>
                <th class="text-right">{{ column_projected_balance }}</th>
                <th class="text-right">{{ column_variance_from_base }}</th>
                <th>{{ column_action }}</th>
              </tr>
            </thead>
            <tbody>
              {% for scenario in active_scenarios %}
              <tr>
                <td>{{ scenario.scenario_name }}</td>
                <td>
                  {% if scenario.scenario_type == 'optimistic' %}
                    <span class="label label-success">{{ text_optimistic }}</span>
                  {% elseif scenario.scenario_type == 'pessimistic' %}
                    <span class="label label-danger">{{ text_pessimistic }}</span>
                  {% else %}
                    <span class="label label-info">{{ text_realistic }}</span>
                  {% endif %}
                </td>
                <td class="text-right">{{ scenario.projected_balance }}</td>
                <td class="text-right {% if scenario.variance_from_base < 0 %}text-danger{% else %}text-success{% endif %}">
                  {{ scenario.variance_from_base }}
                </td>
                <td>
                  <a href="{{ scenario.view }}" class="btn btn-xs btn-info">
                    <i class="fa fa-eye"></i> {{ button_view }}
                  </a>
                </td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    </div>
    {% endif %}

    <!-- التنبيهات والتحذيرات -->
    {% if cash_flow_alerts %}
    <div class="panel panel-warning">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-exclamation-triangle"></i> {{ text_cash_flow_alerts }}</h3>
      </div>
      <div class="panel-body">
        {% for alert in cash_flow_alerts %}
        <div class="alert alert-{% if alert.severity == 'high' %}danger{% elseif alert.severity == 'medium' %}warning{% else %}info{% endif %} alert-dismissible">
          <button type="button" class="close" data-dismiss="alert">&times;</button>
          <strong>{{ alert.alert_type }}:</strong> {{ alert.message }}
          <small class="pull-right">{{ alert.alert_date }}</small>
        </div>
        {% endfor %}
      </div>
    </div>
    {% endif %}

  </div>
</div>

<!-- نموذج إضافة تنبؤ جديد -->
<div id="modal-forecast" class="modal fade">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
        <h4 class="modal-title">{{ text_add_forecast }}</h4>
      </div>
      <div class="modal-body">
        <form action="{{ add }}" method="post" enctype="multipart/form-data" id="form-forecast-add">
          <div class="form-group required">
            <label class="control-label">{{ entry_forecast_name }}</label>
            <input type="text" name="forecast_name" value="" placeholder="{{ entry_forecast_name }}" class="form-control" />
          </div>
          <div class="row">
            <div class="col-md-6">
              <div class="form-group required">
                <label class="control-label">{{ entry_forecast_period }}</label>
                <select name="forecast_period" class="form-control">
                  <option value="weekly">{{ text_weekly }}</option>
                  <option value="monthly" selected>{{ text_monthly }}</option>
                  <option value="quarterly">{{ text_quarterly }}</option>
                  <option value="yearly">{{ text_yearly }}</option>
                </select>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group required">
                <label class="control-label">{{ entry_forecast_method }}</label>
                <select name="forecast_method" class="form-control">
                  <option value="moving_average">{{ text_moving_average }}</option>
                  <option value="linear_regression">{{ text_linear_regression }}</option>
                  <option value="seasonal_decomposition">{{ text_seasonal_decomposition }}</option>
                  <option value="ai_prediction">{{ text_ai_prediction }}</option>
                </select>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-6">
              <div class="form-group required">
                <label class="control-label">{{ entry_start_date }}</label>
                <div class="input-group date">
                  <input type="text" name="start_date" value="" placeholder="{{ entry_start_date }}" data-date-format="YYYY-MM-DD" class="form-control" />
                  <span class="input-group-btn">
                    <button class="btn btn-default" type="button"><i class="fa fa-calendar"></i></button>
                  </span>
                </div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group required">
                <label class="control-label">{{ entry_end_date }}</label>
                <div class="input-group date">
                  <input type="text" name="end_date" value="" placeholder="{{ entry_end_date }}" data-date-format="YYYY-MM-DD" class="form-control" />
                  <span class="input-group-btn">
                    <button class="btn btn-default" type="button"><i class="fa fa-calendar"></i></button>
                  </span>
                </div>
              </div>
            </div>
          </div>
          <div class="form-group">
            <label class="control-label">{{ entry_description }}</label>
            <textarea name="description" rows="3" placeholder="{{ entry_description }}" class="form-control"></textarea>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_cancel }}</button>
        <button type="button" id="button-forecast-save" class="btn btn-primary">{{ button_save }}</button>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript">
$(document).ready(function() {
    // رسم بياني للتدفق النقدي
    {% if chart_data %}
    var ctx = document.getElementById('cashFlowChart').getContext('2d');
    var cashFlowChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: {{ chart_data.labels|json_encode|raw }},
            datasets: [{
                label: '{{ text_actual }}',
                data: {{ chart_data.actual|json_encode|raw }},
                borderColor: '#3c8dbc',
                backgroundColor: 'rgba(60, 141, 188, 0.1)',
                fill: true
            }, {
                label: '{{ text_forecast }}',
                data: {{ chart_data.forecast|json_encode|raw }},
                borderColor: '#f39c12',
                backgroundColor: 'rgba(243, 156, 18, 0.1)',
                fill: true,
                borderDash: [5, 5]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
    {% endif %}

    // تهيئة منتقي التاريخ
    $('.date').datetimepicker({
        language: '{{ datepicker }}',
        pickTime: false
    });

    // إضافة تنبؤ جديد
    $('#button-add').on('click', function() {
        $('#modal-forecast').modal('show');
    });

    // حفظ التنبؤ الجديد
    $('#button-forecast-save').on('click', function() {
        $.ajax({
            url: '{{ add }}',
            type: 'post',
            data: $('#form-forecast-add').serialize(),
            dataType: 'json',
            success: function(json) {
                if (json['success']) {
                    window.location = json['redirect'];
                } else {
                    alert(json['error']);
                }
            }
        });
    });

    // تنبؤ بالذكاء الاصطناعي
    $('#button-ai-forecast').on('click', function() {
        if (confirm('{{ text_confirm_ai_forecast }}')) {
            $.ajax({
                url: '{{ ai_forecast }}',
                type: 'post',
                dataType: 'json',
                beforeSend: function() {
                    $('#button-ai-forecast').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> {{ text_processing }}');
                },
                success: function(json) {
                    if (json['success']) {
                        window.location = json['redirect'];
                    } else {
                        alert(json['error']);
                    }
                },
                complete: function() {
                    $('#button-ai-forecast').prop('disabled', false).html('<i class="fa fa-magic"></i> {{ button_ai_forecast }}');
                }
            });
        }
    });

    // فلترة النتائج
    $('#button-filter').on('click', function() {
        var url = 'index.php?route=accounts/cash_flow_forecasting&user_token={{ user_token }}';
        
        var filter_forecast_name = $('input[name=\'filter_forecast_name\']').val();
        if (filter_forecast_name) {
            url += '&filter_forecast_name=' + encodeURIComponent(filter_forecast_name);
        }

        var filter_forecast_period = $('select[name=\'filter_forecast_period\']').val();
        if (filter_forecast_period) {
            url += '&filter_forecast_period=' + encodeURIComponent(filter_forecast_period);
        }

        var filter_forecast_method = $('select[name=\'filter_forecast_method\']').val();
        if (filter_forecast_method) {
            url += '&filter_forecast_method=' + encodeURIComponent(filter_forecast_method);
        }

        var filter_status = $('select[name=\'filter_status\']').val();
        if (filter_status) {
            url += '&filter_status=' + encodeURIComponent(filter_status);
        }

        location = url;
    });

    // تصدير البيانات
    $('#button-export').on('click', function() {
        window.open('{{ export }}', '_blank');
    });
});
</script>

{{ footer }}
