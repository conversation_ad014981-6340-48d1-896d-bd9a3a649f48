{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="submit" form="inventory\current_stock-form" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fas fa-save"></i></button>
        <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-light"><i class="fas fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    
    <div class="card">
      <div class="card-header"><i class="fas fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}chart-bar{% elif screen_type == 'settings' %}cog{% else %}pencil-alt{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="card-body">
        <form id="inventory\current_stock-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-aging_analysis">{{ text_aging_analysis }}</label>
            <div class="col-sm-10">
              <input type="text" name="aging_analysis" value="{{ aging_analysis }}" placeholder="{{ text_aging_analysis }}" id="input-aging_analysis" class="form-control" />
              {% if error_aging_analysis %}
                <div class="invalid-feedback">{{ error_aging_analysis }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-analytics">{{ text_analytics }}</label>
            <div class="col-sm-10">
              <input type="text" name="analytics" value="{{ analytics }}" placeholder="{{ text_analytics }}" id="input-analytics" class="form-control" />
              {% if error_analytics %}
                <div class="invalid-feedback">{{ error_analytics }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-back">{{ text_back }}</label>
            <div class="col-sm-10">
              <input type="text" name="back" value="{{ back }}" placeholder="{{ text_back }}" id="input-back" class="form-control" />
              {% if error_back %}
                <div class="invalid-feedback">{{ error_back }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-categories">{{ text_categories }}</label>
            <div class="col-sm-10">
              <input type="text" name="categories" value="{{ categories }}" placeholder="{{ text_categories }}" id="input-categories" class="form-control" />
              {% if error_categories %}
                <div class="invalid-feedback">{{ error_categories }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-category_analysis">{{ text_category_analysis }}</label>
            <div class="col-sm-10">
              <input type="text" name="category_analysis" value="{{ category_analysis }}" placeholder="{{ text_category_analysis }}" id="input-category_analysis" class="form-control" />
              {% if error_category_analysis %}
                <div class="invalid-feedback">{{ error_category_analysis }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-export_excel">{{ text_export_excel }}</label>
            <div class="col-sm-10">
              <input type="text" name="export_excel" value="{{ export_excel }}" placeholder="{{ text_export_excel }}" id="input-export_excel" class="form-control" />
              {% if error_export_excel %}
                <div class="invalid-feedback">{{ error_export_excel }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-export_pdf">{{ text_export_pdf }}</label>
            <div class="col-sm-10">
              <input type="text" name="export_pdf" value="{{ export_pdf }}" placeholder="{{ text_export_pdf }}" id="input-export_pdf" class="form-control" />
              {% if error_export_pdf %}
                <div class="invalid-feedback">{{ error_export_pdf }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_category_id">{{ text_filter_category_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_category_id" value="{{ filter_category_id }}" placeholder="{{ text_filter_category_id }}" id="input-filter_category_id" class="form-control" />
              {% if error_filter_category_id %}
                <div class="invalid-feedback">{{ error_filter_category_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_data">{{ text_filter_data }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_data" value="{{ filter_data }}" placeholder="{{ text_filter_data }}" id="input-filter_data" class="form-control" />
              {% if error_filter_data %}
                <div class="invalid-feedback">{{ error_filter_data }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_product_name">{{ text_filter_product_name }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_product_name" value="{{ filter_product_name }}" placeholder="{{ text_filter_product_name }}" id="input-filter_product_name" class="form-control" />
              {% if error_filter_product_name %}
                <div class="invalid-feedback">{{ error_filter_product_name }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_sku">{{ text_filter_sku }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_sku" value="{{ filter_sku }}" placeholder="{{ text_filter_sku }}" id="input-filter_sku" class="form-control" />
              {% if error_filter_sku %}
                <div class="invalid-feedback">{{ error_filter_sku }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_stock_status">{{ text_filter_stock_status }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_stock_status" value="{{ filter_stock_status }}" placeholder="{{ text_filter_stock_status }}" id="input-filter_stock_status" class="form-control" />
              {% if error_filter_stock_status %}
                <div class="invalid-feedback">{{ error_filter_stock_status }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_warehouse_id">{{ text_filter_warehouse_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_warehouse_id" value="{{ filter_warehouse_id }}" placeholder="{{ text_filter_warehouse_id }}" id="input-filter_warehouse_id" class="form-control" />
              {% if error_filter_warehouse_id %}
                <div class="invalid-feedback">{{ error_filter_warehouse_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="invalid-feedback">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-low_stock_alerts">{{ text_low_stock_alerts }}</label>
            <div class="col-sm-10">
              <input type="text" name="low_stock_alerts" value="{{ low_stock_alerts }}" placeholder="{{ text_low_stock_alerts }}" id="input-low_stock_alerts" class="form-control" />
              {% if error_low_stock_alerts %}
                <div class="invalid-feedback">{{ error_low_stock_alerts }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-movement_trends">{{ text_movement_trends }}</label>
            <div class="col-sm-10">
              <input type="text" name="movement_trends" value="{{ movement_trends }}" placeholder="{{ text_movement_trends }}" id="input-movement_trends" class="form-control" />
              {% if error_movement_trends %}
                <div class="invalid-feedback">{{ error_movement_trends }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-order">{{ text_order }}</label>
            <div class="col-sm-10">
              <input type="text" name="order" value="{{ order }}" placeholder="{{ text_order }}" id="input-order" class="form-control" />
              {% if error_order %}
                <div class="invalid-feedback">{{ error_order }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-overstock_alerts">{{ text_overstock_alerts }}</label>
            <div class="col-sm-10">
              <input type="text" name="overstock_alerts" value="{{ overstock_alerts }}" placeholder="{{ text_overstock_alerts }}" id="input-overstock_alerts" class="form-control" />
              {% if error_overstock_alerts %}
                <div class="invalid-feedback">{{ error_overstock_alerts }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-pagination">{{ text_pagination }}</label>
            <div class="col-sm-10">
              <input type="text" name="pagination" value="{{ pagination }}" placeholder="{{ text_pagination }}" id="input-pagination" class="form-control" />
              {% if error_pagination %}
                <div class="invalid-feedback">{{ error_pagination }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-print">{{ text_print }}</label>
            <div class="col-sm-10">
              <input type="text" name="print" value="{{ print }}" placeholder="{{ text_print }}" id="input-print" class="form-control" />
              {% if error_print %}
                <div class="invalid-feedback">{{ error_print }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-results">{{ text_results }}</label>
            <div class="col-sm-10">
              <input type="text" name="results" value="{{ results }}" placeholder="{{ text_results }}" id="input-results" class="form-control" />
              {% if error_results %}
                <div class="invalid-feedback">{{ error_results }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort">{{ text_sort }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort" value="{{ sort }}" placeholder="{{ text_sort }}" id="input-sort" class="form-control" />
              {% if error_sort %}
                <div class="invalid-feedback">{{ error_sort }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_current_stock">{{ text_sort_current_stock }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_current_stock" value="{{ sort_current_stock }}" placeholder="{{ text_sort_current_stock }}" id="input-sort_current_stock" class="form-control" />
              {% if error_sort_current_stock %}
                <div class="invalid-feedback">{{ error_sort_current_stock }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_product_name">{{ text_sort_product_name }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_product_name" value="{{ sort_product_name }}" placeholder="{{ text_sort_product_name }}" id="input-sort_product_name" class="form-control" />
              {% if error_sort_product_name %}
                <div class="invalid-feedback">{{ error_sort_product_name }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_sku">{{ text_sort_sku }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_sku" value="{{ sort_sku }}" placeholder="{{ text_sort_sku }}" id="input-sort_sku" class="form-control" />
              {% if error_sort_sku %}
                <div class="invalid-feedback">{{ error_sort_sku }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_total_value">{{ text_sort_total_value }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_total_value" value="{{ sort_total_value }}" placeholder="{{ text_sort_total_value }}" id="input-sort_total_value" class="form-control" />
              {% if error_sort_total_value %}
                <div class="invalid-feedback">{{ error_sort_total_value }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-stock_summary">{{ text_stock_summary }}</label>
            <div class="col-sm-10">
              <input type="text" name="stock_summary" value="{{ stock_summary }}" placeholder="{{ text_stock_summary }}" id="input-stock_summary" class="form-control" />
              {% if error_stock_summary %}
                <div class="invalid-feedback">{{ error_stock_summary }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-stocks">{{ text_stocks }}</label>
            <div class="col-sm-10">
              <input type="text" name="stocks" value="{{ stocks }}" placeholder="{{ text_stocks }}" id="input-stocks" class="form-control" />
              {% if error_stocks %}
                <div class="invalid-feedback">{{ error_stocks }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="invalid-feedback">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-valuation_analysis">{{ text_valuation_analysis }}</label>
            <div class="col-sm-10">
              <input type="text" name="valuation_analysis" value="{{ valuation_analysis }}" placeholder="{{ text_valuation_analysis }}" id="input-valuation_analysis" class="form-control" />
              {% if error_valuation_analysis %}
                <div class="invalid-feedback">{{ error_valuation_analysis }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-warehouse_analysis">{{ text_warehouse_analysis }}</label>
            <div class="col-sm-10">
              <input type="text" name="warehouse_analysis" value="{{ warehouse_analysis }}" placeholder="{{ text_warehouse_analysis }}" id="input-warehouse_analysis" class="form-control" />
              {% if error_warehouse_analysis %}
                <div class="invalid-feedback">{{ error_warehouse_analysis }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-warehouses">{{ text_warehouses }}</label>
            <div class="col-sm-10">
              <input type="text" name="warehouses" value="{{ warehouses }}" placeholder="{{ text_warehouses }}" id="input-warehouses" class="form-control" />
              {% if error_warehouses %}
                <div class="invalid-feedback">{{ error_warehouses }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}