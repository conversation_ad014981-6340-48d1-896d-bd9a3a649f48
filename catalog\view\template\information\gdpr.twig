{{ header }}
<div id="account-gdpr" class="container-fluid">
  {% if success %}
    <div class="alert alert-success alert-dismissible"><i class="fa-solid fa-circle-check"></i> {{ success }} <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>
  {% endif %}
  <div class="row">{{ column_left }}
    <div id="content" class="col" style="padding-left:10px;padding-right:10px">{{ content_top }}
      <h1>{{ heading_title }}</h1>
      <p>{{ text_gdpr|format(store, gdpr, title) }}</p>
      <form action="{{ action }}" method="post" data-oc-toggle="ajax">
        <fieldset>
          <legend id="account">{{ text_verification }}</legend>
          <div class="mb-3">
            <p><label for="input-email" class="form-check-label">{{ text_email }}</label></p>
            <div class="input-group">
              <input type="text" name="email" value="{{ email }}" placeholder="{{ entry_email }}" id="input-email" class="form-control"/>
              <div class="input-group-text"><i class="fa-solid fa-envelope"></i></div>
            </div>
            <div id="error-email" class="invalid-feedback"></div>
          </div>
        </fieldset>
        <fieldset>
          <legend id="action">{{ text_action }}</legend>

          <div class="mb-3">
            <div class="form-check">
              <input type="radio" name="action" value="export" id="input-export" checked class="form-check-input"/> <label for="input-export" class="form-check-label"><strong>{{ text_export }}</strong></label>
            </div>
            <div class="form-check">
              <input type="radio" name="action" value="remove" id="input-remove" class="form-check-input"/> <label for="input-remove" class="form-check-label"><strong>{{ text_remove }}</strong></label>
            </div>
          </div>

          <div id="collapse-remove" class="alert alert-warning collapse">
            <p><i class="fa-solid fa-triangle-exclamation"></i> {{ text_warning }}</p>
            <ul>
              <li>{{ text_access|format(store) }}</li>
              <li>{{ text_history }}</li>
              <li>{{ text_limit|format(limit) }}</li>
            </ul>
          </div>
        </fieldset>
        <div class="row">
          <div class="col">
            <a href="{{ cancel }}" class="btn btn-light">{{ button_cancel }}</a>
          </div>
          <div class="col text-end">
            <button type="submit" class="btn btn-primary">{{ button_continue }}</button>
          </div>
        </div>
      </form>
      {{ content_bottom }}</div>
    {{ column_right }}</div>
</div>
<script type="text/javascript"><!--
$('input[name=\'action\']').on('change', function () {
    if (this.value == 'remove') {
        $('#collapse-remove').slideDown();
    } else {
        $('#collapse-remove').slideUp();
    }
});
//--></script>
{{ footer }}
