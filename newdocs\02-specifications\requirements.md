# متطلبات مراجعة وتطوير نظام AYM ERP الشامل

## مقدمة

هذا المشروع يهدف إلى مراجعة شاملة لنظام AYM ERP لضمان أنه يتفوق على المنافسين الأقوياء مثل SAP وOracle وMicrosoft وOdoo المدعومين بقوة Shopify وMagento وWooCommerce. النظام يجب أن يكون أول نظام ERP مصري متكامل مع متجر إلكتروني متخصص في خدمة الشركات التجارية التي تهتم بالبيع عبر الإنترنت مع وجود فروع ومخازن فعلية.

## 🚨 الاكتشافات الحرجة المحدثة

### الانقسام التقني الشامل المكتشف:
- **واجهات متطورة جداً** مع أنظمة خلفية متخلفة
- **نظام مخزون معقد** - فصل بين مخزون المتجر والمخزون الفعلي
- **مخزون وهمي للمتجر** - يمكن البيع قبل الشراء من السوق
- **header.twig متطور** - نظام طلب سريع من أي مكان (ميزة تنافسية)
- **productspro معقد** - موديل منتجات متطور مع إعدادات متقدمة
- **نظام POS معقد** - مرتبط بالفروع والموظفين
- **WAC في كل العمليات** - المتوسط المرجح للتكلفة يجب تطبيقه بدقة
- **عدم تكامل مع ETA** - مخاطر قانونية وضريبية في مصر

### الخدمات المركزية الـ5 (مشكلة معقدة):
1. **📊 اللوج والتدقيق** - 4 كونترولرز منفصلة
2. **🔔 الإشعارات** - 3 كونترولرز منفصلة  
3. **💬 التواصل الداخلي** - 4 كونترولرز منفصلة
4. **📁 المستندات والمرفقات** - 4 كونترولرز + 7 جداول متخصصة
5. **⚙️ محرر سير العمل المرئي** - 8 كونترولرز منفصلة

**المشكلة الحرجة**: central_service_manager.php موجود (157 دالة) لكن الكونترولرز الأساسية لا تستخدمه!

## المتطلبات

### المتطلب 1: مراجعة شاملة للشاشات والوظائف

**قصة المستخدم:** كمطور نظام، أريد مراجعة كل شاشة في النظام، حتى أتأكد من أنها تنافس أقوى الأنظمة العالمية وتلبي احتياجات الشركات التجارية المصرية.

#### معايير القبول

1. WHEN يتم فتح أي شاشة في النظام THEN يجب أن تحتوي على جميع الوظائف المتوقعة من منافسينا الأقوياء
2. WHEN يتم مقارنة الشاشة مع SAP/Oracle/Microsoft/Odoo THEN يجب ألا تقل الوظائف عن المعايير العالمية
3. WHEN يتم تحليل الشاشة THEN يجب أن تدعم البيع عبر الإنترنت والفروع الفعلية والمخازن المتعددة
4. WHEN يتم فحص الشاشة THEN يجب أن تدعم المنتجات ذات الوحدات المتنوعة
5. WHEN يتم اختبار الشاشة THEN يجب أن تتكامل مع الخدمات المركزية والصلاحيات

### المتطلب 2: فحص التكامل مع قاعدة البيانات

**قصة المستخدم:** كمحاسب ومدير مخزون، أريد أن تكون كل شاشة متكاملة تماماً مع قاعدة البيانات، حتى تعكس البيانات الصحيحة وتنفذ القيود المحاسبية التلقائية.

#### معايير القبول

1. WHEN يتم حفظ أي بيانات THEN يجب أن تنعكس فوراً في الجداول المرتبطة في قاعدة البيانات
2. WHEN يتم تنفيذ عملية محاسبية THEN يجب أن تنشئ القيود التلقائية المناسبة
3. WHEN يتم تحديث المخزون THEN يجب أن يحدث وفق نظام المتوسط المرجح للتكلفة (WAC)
4. WHEN يتم الوصول للبيانات THEN يجب أن تمر عبر الخدمات المركزية
5. WHEN يتم تنفيذ عملية THEN يجب أن تسجل في نظام التدقيق والسجلات

### المتطلب 3: فحص التوافق مع MVC والخدمات المركزية

**قصة المستخدم:** كمطور، أريد أن تكون كل شاشة متوافقة مع معمارية MVC والخدمات المركزية، حتى يكون النظام قابلاً للصيانة والتطوير.

#### معايير القبول

1. WHEN يتم فحص أي كونترولر THEN يجب أن يستخدم الخدمات المركزية للعمليات المشتركة
2. WHEN يتم فحص أي موديل THEN يجب أن تكون استعلاماته صحيحة ومحسنة
3. WHEN يتم فحص أي view THEN يجب أن يعرض البيانات بشكل صحيح ومتجاوب
4. WHEN يتم تنفيذ عملية THEN يجب أن تمر عبر نظام الصلاحيات
5. WHEN يتم استخدام الإعدادات THEN يجب أن تأتي من النظام المركزي للإعدادات

### المتطلب 4: فحص التعارضات بين الشاشات

**قصة المستخدم:** كمستخدم نهائي، أريد أن تكون جميع الشاشات متسقة ومتكاملة، حتى لا أواجه تعارضات أو تضارب في البيانات.

#### معايير القبول

1. WHEN يتم تحديث بيانات في شاشة THEN يجب أن تنعكس في جميع الشاشات المرتبطة
2. WHEN يتم حذف عنصر THEN يجب أن يتم التحقق من التبعيات في الشاشات الأخرى
3. WHEN يتم تغيير إعداد THEN يجب أن يؤثر على جميع الشاشات المتأثرة
4. WHEN يتم إنشاء عنصر جديد THEN يجب أن يظهر في جميع القوائم والتقارير المناسبة
5. WHEN يتم تنفيذ عملية THEN يجب ألا تتعارض مع العمليات في الشاشات الأخرى

### المتطلب 5: تحسين تجربة المستخدم للشركات التجارية

**قصة المستخدم:** كمدير شركة تجارية، أريد أن تكون الشاشات سهلة الاستخدام ومصممة خصيصاً لاحتياجات الشركات التي تبيع عبر الإنترنت ولها فروع فعلية.

#### معايير القبول

1. WHEN يتم استخدام أي شاشة THEN يجب أن تدعم العمليات السريعة للبيع اليومي
2. WHEN يتم إدارة المخزون THEN يجب أن تدعم المخازن المتعددة والفروع المختلفة
3. WHEN يتم إدارة المنتجات THEN يجب أن تدعم الوحدات المتنوعة والتحويلات
4. WHEN يتم إدارة المبيعات THEN يجب أن تدعم البيع عبر الإنترنت ونقاط البيع الفعلية
5. WHEN يتم إدارة العملاء THEN يجب أن تدعم عملاء الإنترنت وعملاء الفروع

### المتطلب 6: التكامل مع الأنظمة الخارجية

**قصة المستخدم:** كمدير تقني، أريد أن يتكامل النظام مع الأنظمة الخارجية المهمة، حتى يكون شاملاً ومتكاملاً مع البيئة التجارية المصرية.

#### معايير القبول

1. WHEN يتم التكامل مع نظام الضرائب المصري THEN يجب أن يكون سلساً وتلقائياً
2. WHEN يتم التكامل مع بوابات الدفع THEN يجب أن يدعم البوابات المصرية والعالمية
3. WHEN يتم التكامل مع شركات الشحن THEN يجب أن يدعم الشركات المحلية والعالمية
4. WHEN يتم التكامل مع البنوك THEN يجب أن يدعم التحويلات والمدفوعات الإلكترونية
5. WHEN يتم التكامل مع منصات التجارة الإلكترونية THEN يجب أن يدعم المنصات الشائعة

### المتطلب 7: الأمان والصلاحيات المتقدمة

**قصة المستخدم:** كمدير أمان، أريد أن يكون النظام آمناً ومحمياً بنظام صلاحيات متقدم، حتى أضمن حماية البيانات الحساسة.

#### معايير القبول

1. WHEN يتم الوصول لأي شاشة THEN يجب التحقق من صلاحيات المستخدم
2. WHEN يتم تنفيذ عملية حساسة THEN يجب تسجيلها في نظام التدقيق
3. WHEN يتم تسجيل الدخول THEN يجب استخدام آليات الأمان المتقدمة
4. WHEN يتم نقل البيانات THEN يجب تشفيرها وحمايتها
5. WHEN يتم النسخ الاحتياطي THEN يجب أن يكون آمناً ومشفراً

### المتطلب 8: الأداء والاستجابة

**قصة المستخدم:** كمستخدم نهائي، أريد أن تكون جميع الشاشات سريعة ومتجاوبة، حتى أتمكن من العمل بكفاءة عالية.

#### معايير القبول

1. WHEN يتم تحميل أي شاشة THEN يجب أن تظهر في أقل من 3 ثوانٍ
2. WHEN يتم البحث في البيانات THEN يجب أن تظهر النتائج فوراً
3. WHEN يتم حفظ البيانات THEN يجب أن يتم في أقل من ثانيتين
4. WHEN يتم تحديث الشاشة THEN يجب أن يحدث بسلاسة دون تأخير
5. WHEN يتم استخدام النظام بكثافة THEN يجب أن يحافظ على الأداء العالي

### المتطلب 9: التقارير والتحليلات المتقدمة

**قصة المستخدم:** كمدير تنفيذي، أريد تقارير وتحليلات متقدمة من كل شاشة، حتى أتمكن من اتخاذ قرارات مدروسة.

#### معايير القبول

1. WHEN يتم طلب تقرير من أي شاشة THEN يجب أن يكون شاملاً ودقيقاً
2. WHEN يتم عرض التحليلات THEN يجب أن تكون مرئية وسهلة الفهم
3. WHEN يتم تصدير البيانات THEN يجب أن تدعم صيغ متعددة (Excel, PDF, CSV)
4. WHEN يتم جدولة التقارير THEN يجب أن تصل تلقائياً في المواعيد المحددة
5. WHEN يتم مقارنة البيانات THEN يجب أن تدعم المقارنات الزمنية والفرعية

### المتطلب 10: إصلاح وتوحيد الخدمات المركزية الـ5

**قصة المستخدم:** كمطور ومستخدم، أريد أن تعمل الخدمات المركزية الـ5 بشكل فعلي وموحد في جميع الشاشات، حتى أحصل على تجربة متكاملة دون تضارب أو ازدواجية.

#### الوضع الحالي المكتشف:
- ✅ **12 كونترولر محدث** بالكامل
- ✅ **8 نماذج محدثة** بالكامل (إضافة unified_document.php)
- ✅ **37+ دالة رئيسية** تم تحديثها
- ✅ **52+ استدعاء** تم إصلاحه
- ✅ **3 ملفات آمنة** تم حذفها
- ✅ **17 مصدر فوضى** تم اكتشافه وإصلاحه

#### الخدمات المركزية الـ5:
1. **📊 اللوج والتدقيق** - 4 كونترولرز (audit_trail, user_activity, system_logs, performance)
2. **🔔 الإشعارات** - 3 كونترولرز (automation, settings, templates)
3. **💬 التواصل الداخلي** - 4 كونترولرز (announcements, chat, messages, teams)
4. **📁 المستندات والمرفقات** - 4 كونترولرز (archive, approval, templates, versioning) + 7 جداول متخصصة
5. **⚙️ محرر سير العمل المرئي** - 8 كونترولرز (actions, conditions, designer, triggers, etc.)

#### معايير القبول

1. WHEN يتم استخدام أي شاشة THEN يجب أن تستخدم المدير المركزي للخدمات (central_service_manager) بدلاً من الكونترولرز المنفصلة
2. WHEN يتم تسجيل أي نشاط THEN يجب أن يمر عبر الخدمة المركزية للسجلات مع النظام الاحتياطي
3. WHEN يتم إرسال إشعار THEN يجب أن يمر عبر الخدمة المركزية للإشعارات مع قوالب موحدة
4. WHEN يتم رفع مستند THEN يجب أن يمر عبر نظام المستندات المعقد (7 جداول + 4 كونترولرز)
5. WHEN يتم بدء سير عمل THEN يجب أن يمر عبر محرر سير العمل المرئي (8 كونترولرز)

### المتطلب 11: تطوير الهيدر المتكامل مع الإشعارات

**قصة المستخدم:** كمستخدم، أريد هيدر متكامل يعرض الإشعارات والتواصل السريع، حتى أبقى على اطلاع دائم بكل ما يحدث في النظام.

#### معايير القبول

1. WHEN يتم تحميل أي صفحة THEN يجب أن يظهر الهيدر مع أيقونة الإشعارات وعدد الإشعارات غير المقروءة
2. WHEN يتم النقر على أيقونة الإشعارات THEN يجب أن تفتح لوحة متكاملة تعرض جميع الإشعارات
3. WHEN يتم فتح لوحة الإشعارات THEN يجب أن تحتوي على قائمة التواصل السريع
4. WHEN يتم استلام إشعار جديد THEN يجب أن يظهر فوراً في الهيدر مع تحديث العدد
5. WHEN يتم قراءة إشعار THEN يجب أن يقل العدد في الهيدر فوراً

### المتطلب 12: توثيق وشرح تكامل الشاشات مع الخدمات المركزية

**قصة المستخدم:** كمطور ومستخدم، أريد فهم كيفية تكامل كل شاشة مع الخدمات المركزية، حتى أتمكن من استخدام النظام بكفاءة وتطويره بسهولة.

#### معايير القبول

1. WHEN يتم فتح أي شاشة THEN يجب أن يكون واضحاً كيف تتكامل مع خدمة الإشعارات
2. WHEN يتم تنفيذ عملية THEN يجب أن يكون واضحاً كيف تتكامل مع خدمة السجلات
3. WHEN يتم التعامل مع المستندات THEN يجب أن يكون واضحاً كيف تتكامل مع خدمة المستندات
4. WHEN يتم استخدام التواصل THEN يجب أن يكون واضحاً كيف تتكامل مع خدمة التواصل
5. WHEN يتم بدء سير عمل THEN يجب أن يكون واضحاً كيف تتكامل مع خدمة سير العمل

### المتطلب 13: معالجة نظام المستندات والمرفقات المعقد

**قصة المستخدم:** كمستخدم ومطور، أريد أن يعمل نظام المستندات والمرفقات المعقد بسلاسة مع جميع الشاشات، حتى أتمكن من إدارة المستندات بكفاءة عبر النظام.

#### التعقيد المكتشف:
- **7 جداول متخصصة:** cod_unified_document, cod_document_permission, cod_announcement_attachment, cod_internal_attachment, cod_journal_attachments, cod_purchase_document, cod_employee_documents
- **4 كونترولرز معقدة:** documents/archive.php (599 سطر), documents/approval.php, documents/templates.php, documents/versioning.php
- **نموذج معقد:** unified_document.php (458 سطر) - تم تحديثه للخدمات المركزية

#### معايير القبول

1. WHEN يتم رفع مستند في أي شاشة THEN يجب أن يحفظ في الجدول المناسب من الـ7 جداول
2. WHEN يتم تعديل صلاحيات مستند THEN يجب أن تنعكس في جدول cod_document_permission
3. WHEN يتم إرفاق مستند بعملية THEN يجب أن يربط بالجدول المتخصص (journal_attachments, purchase_document, etc.)
4. WHEN يتم الوصول لمستند THEN يجب التحقق من الصلاحيات والموافقات
5. WHEN يتم إنشاء إصدار جديد من مستند THEN يجب أن يحفظ التاريخ والإصدارات السابقة

### المتطلب 14: الذكاء الاصطناعي والأتمتة

**قصة المستخدم:** كمستخدم متقدم، أريد أن يساعدني الذكاء الاصطناعي في كل شاشة، حتى أتمكن من العمل بذكاء وكفاءة أكبر.

#### معايير القبول

1. WHEN يتم استخدام أي شاشة THEN يجب أن تقدم اقتراحات ذكية
2. WHEN يتم إدخال البيانات THEN يجب أن تكتمل تلقائياً عند الإمكان
3. WHEN يتم اكتشاف أنماط THEN يجب أن ينبه المستخدم للفرص أو المخاطر
4. WHEN يتم تحليل البيانات THEN يجب أن يقدم رؤى ذكية وتوصيات
5. WHEN يتم تكرار العمليات THEN يجب أن يقترح الأتمتة

### المتطلب 15: إدارة أنظمة الشحن والدفع المتقدمة

**قصة المستخدم:** كمدير متجر إلكتروني، أريد إدارة شاملة لأنظمة الشحن والدفع مع ربطها بالحسابات والاستلامات، حتى أتمكن من التحكم الكامل في العمليات التجارية.

#### معايير القبول

1. WHEN يتم إعداد طريقة دفع جديدة THEN يجب أن تربط تلقائياً بالحسابات المحاسبية
2. WHEN يتم إعداد شركة شحن THEN يجب أن تتكامل مع API الخاص بها وتحسب التكلفة تلقائياً
3. WHEN يتم استلام دفعة THEN يجب أن تسجل في النظام المحاسبي فوراً
4. WHEN يتم تغيير إعدادات الضرائب THEN يجب أن تنعكس على جميع المعاملات
5. WHEN يتم تفعيل/إلغاء طريقة دفع THEN يجب أن تظهر/تختفي من المتجر فوراً

### المتطلب 16: مراجعة شاملة للعمود الجانبي وجميع الشاشات

**قصة المستخدم:** كمطور ومستخدم، أريد مراجعة شاملة لجميع شاشات النظام في العمود الجانبي، حتى أتأكد من تكاملها مع الخدمات المركزية والمعايير الحديثة.

#### معايير القبول

1. WHEN يتم فتح أي شاشة من العمود الجانبي THEN يجب أن تكون متكاملة مع الخدمات المركزية
2. WHEN يتم مراجعة شاشة THEN يجب أن تدعم احتياجات الشركات متعددة الفروع
3. WHEN يتم فحص الوظائف THEN يجب أن تطبق نظام WAC والصلاحيات المزدوجة
4. WHEN يتم اختبار التكامل THEN يجب أن تعمل مع نظام المخزون المعقد
5. WHEN يتم تقييم الأداء THEN يجب أن تحمل في أقل من 3 ثوان

### المتطلب 17: دعم الشركات متعددة الفروع والمركز الرئيسي

**قصة المستخدم:** كمدير شركة لها فروع متعددة، أريد نظام يدعم المركز الرئيسي والفروع بشكل متكامل، حتى أتمكن من إدارة العمليات بكفاءة عالية.

#### معايير القبول

1. WHEN يتم إنشاء فرع جديد THEN يجب أن يرث الإعدادات من المركز الرئيسي
2. WHEN يتم تعيين موظف لفرع THEN يجب أن يصل فقط لمخزون فرعه
3. WHEN يتم نقل مخزون بين الفروع THEN يجب أن يسجل في النظام المحاسبي
4. WHEN يتم إنشاء تقرير THEN يجب أن يدعم العرض حسب الفرع أو إجمالي
5. WHEN يتم تحديد صلاحيات THEN يجب أن تدعم التحكم على مستوى الفرع

### المتطلب 18: نظام التسويق المتقدم مع Google Tag Manager

**قصة المستخدم:** كمدير تسويق، أريد نظام تسويق متقدم يدعم Google Tag Manager والبيكسل، حتى أتمكن من تتبع وتحليل أداء الحملات التسويقية.

#### معايير القبول

1. WHEN يتم اشتراك عميل جديد THEN يجب أن يحصل على Google Tag Manager مُعد مسبقاً
2. WHEN يتم إضافة بيكسل تتبع THEN يجب أن يتكامل مع جميع صفحات المتجر
3. WHEN يتم تتبع تحويل THEN يجب أن يرسل البيانات لجميع منصات التتبع
4. WHEN يتم تحليل البيانات THEN يجب أن تظهر في تقارير مفصلة
5. WHEN يتم تخصيص التتبع THEN يجب أن يكون سهل الإعداد للعميل

### المتطلب 19: فهم وتوثيق نظام المخزون المعقد (أولوية قصوى)

**قصة المستخدم:** كمطور ومدير مخزون، أريد فهم عميق لنظام المخزون المعقد المكتشف، حتى أتمكن من التعامل معه بشكل صحيح دون كسر الوظائف الموجودة.

#### التعقيد المكتشف:
- **فصل بين مخزون المتجر والمخزون الفعلي** - نظامان منفصلان
- **مخزون وهمي للمتجر** - يمكن البيع قبل الشراء من السوق
- **سياسات تحكم متقدمة** - مثل بيع 5 من أصل 10 وإيقاف البيع
- **ربط بالفروع والموظفين** - كل موظف يبيع من مخزون فرعه فقط
- **نظام POS معقد** - جلسات متعددة المستخدمين في المتصفح
- **WAC في كل العمليات** - المتوسط المرجح للتكلفة يجب تطبيقه بدقة

#### معايير القبول

1. WHEN يتم فحص نظام المخزون THEN يجب فهم الفرق بين المخزون الوهمي والفعلي
2. WHEN يتم تحليل الأدوار THEN يجب توثيق دور كل من (أمين المخزن، مدير المتجر، الكاشير)
3. WHEN يتم دراسة POS THEN يجب فهم كيفية ربط الموظفين بالفروع والمخازن
4. WHEN يتم فحص WAC THEN يجب فهم تطبيقه في جميع عمليات المخزون والمحاسبة
5. WHEN يتم توثيق النظام THEN يجب إنشاء دليل شامل لجميع السيناريوهات

### المتطلب 20: فهم وتوثيق header.twig والطلب السريع (ميزة تنافسية)

**قصة المستخدم:** كمطور ومدير منتج، أريد فهم عميق لنظام الطلب السريع المتطور في header.twig، حتى أتمكن من الاستفادة من هذه الميزة التنافسية وتطويرها.

#### الميزة المكتشفة:
- **نظام طلب سريع من أي مكان** في المتجر
- **تكامل متقدم مع JavaScript** للتفاعل الفوري
- **ربط مع نظام السلة والدفع** بشكل سلس
- **واجهة تفاعلية متطورة** تنافس أقوى المتاجر العالمية

#### معايير القبول

1. WHEN يتم فحص header.twig THEN يجب فهم آلية الطلب السريع بالتفصيل
2. WHEN يتم تحليل JavaScript THEN يجب توثيق جميع الدوال والتفاعلات
3. WHEN يتم دراسة التكامل THEN يجب فهم الربط مع لوحة التحكم والنظام الخلفي
4. WHEN يتم اختبار الميزة THEN يجب التأكد من عملها مع جميع أنواع المنتجات
5. WHEN يتم توثيق الميزة THEN يجب إنشاء دليل للاستفادة منها وتطويرها

### المتطلب 21: فهم وتوثيق productspro المتطور (موديل معقد)

**قصة المستخدم:** كمطور ومدير منتجات، أريد فهم عميق لموديل productspro المتطور، حتى أتمكن من استغلال إمكانياته الكاملة في إدارة المنتجات المعقدة.

#### التعقيد المكتشف:
- **نظام وحدات متعددة** لكل منتج
- **باقات ديناميكية** مع خيارات متداخلة
- **حساب أسعار معقد** في الوقت الفعلي
- **خصومات كمية متقدمة** حسب الوحدة والباقة
- **خيارات معقدة** مرتبطة بالوحدات والباقات

#### معايير القبول

1. WHEN يتم فحص productspro THEN يجب فهم جميع الوظائف المتقدمة
2. WHEN يتم تحليل الوحدات THEN يجب توثيق نظام الوحدات المتعددة والتحويلات
3. WHEN يتم دراسة الباقات THEN يجب فهم آلية الباقات الديناميكية والخصومات
4. WHEN يتم فحص التسعير THEN يجب فهم حساب الأسعار المعقد والعوامل المؤثرة
5. WHEN يتم توثيق النظام THEN يجب إنشاء دليل شامل لإعداد المنتجات المعقدة

### المتطلب 22: تأمين وتطوير API متقدم (أولوية حرجة)

**قصة المستخدم:** كمطور ومدير تقني، أريد API آمن ومتطور يدعم جميع الميزات الجديدة، حتى يتمكن النظام من التكامل مع التطبيقات الخارجية بكفاءة.

#### المشكلة الحرجة المكتشفة:
- **API تقليدي متخلف** لا يدعم الميزات الجديدة
- **ثغرات أمنية خطيرة** - عدم تشفير، عدم Rate Limiting
- **فجوة تقنية كبيرة** بين الواجهة المتطورة والـ API المتخلف
- **فشل التكامل** مع التطبيقات الخارجية والمحمولة

#### معايير القبول

1. WHEN يتم تطوير API جديد THEN يجب أن يدعم الوحدات المتعددة والباقات والخصومات
2. WHEN يتم تأمين API THEN يجب تطبيق OAuth 2.0، JWT، Rate Limiting، وتشفير البيانات
3. WHEN يتم اختبار API THEN يجب أن يعمل مع جميع الميزات المتقدمة للمنتجات
4. WHEN يتم توثيق API THEN يجب إنشاء Swagger/OpenAPI documentation شامل
5. WHEN يتم نشر API THEN يجب أن يكون متوافق مع التطبيقات المحمولة وأنظمة POS

### المتطلب 23: التكامل الإجباري مع ETA (الضرائب المصرية)

**قصة المستخدم:** كمدير مالي وقانوني، أريد تكامل كامل مع نظام الضرائب المصرية ETA، حتى أتجنب المخاطر القانونية والغرامات.

#### المخاطر القانونية:
- **عدم إصدار فواتير إلكترونية** - مخالفة قانونية
- **عدم تقديم تقارير دورية** - غرامات مالية
- **عدم حفظ البيانات للهيئة** - مخاطر قانونية

#### معايير القبول

1. WHEN يتم تكامل ETA SDK THEN يجب إصدار فواتير إلكترونية لجميع المبيعات
2. WHEN يتم إنشاء فاتورة THEN يجب أن تحتوي على QR Code والتوقيع الرقمي
3. WHEN يتم حفظ البيانات THEN يجب أن تُحفظ بالصيغة المطلوبة للهيئة
4. WHEN يتم إنشاء تقارير THEN يجب أن تكون متوافقة مع متطلبات الهيئة
5. WHEN يتم اختبار النظام THEN يجب التأكد من الامتثال الكامل للقوانين المصرية
