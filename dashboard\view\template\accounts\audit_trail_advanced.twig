{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        {% if has_permission %}
        <button type="button" id="button-create" data-toggle="tooltip" title="{{ button_create }}" class="btn btn-primary">
          <i class="fa fa-plus"></i> {{ button_create }}
        </button>
        <button type="button" id="button-ai-audit" data-toggle="tooltip" title="{{ button_ai_audit }}" class="btn btn-info">
          <i class="fa fa-magic"></i> {{ button_ai_audit }}
        </button>
        <button type="button" id="button-export" data-toggle="tooltip" title="{{ button_export }}" class="btn btn-success">
          <i class="fa fa-download"></i> {{ button_export }}
        </button>
        {% endif %}
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    {% if success %}
    <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    
    <!-- Audit Statistics -->
    <div class="row">
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-primary">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-search fa-5x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ total_audits }}</div>
                <div>{{ text_total_audits }}</div>
              </div>
            </div>
          </div>
          <div class="panel-footer">
            <span class="pull-left">{{ text_this_year }}</span>
            <span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
            <div class="clearfix"></div>
          </div>
        </div>
      </div>
      
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-green">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-check-circle fa-5x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ completed_audits }}</div>
                <div>{{ text_completed_audits }}</div>
              </div>
            </div>
          </div>
          <div class="panel-footer">
            <span class="pull-left">{{ completion_rate }}% {{ text_completion_rate }}</span>
            <span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
            <div class="clearfix"></div>
          </div>
        </div>
      </div>
      
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-yellow">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-exclamation-triangle fa-5x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ total_findings }}</div>
                <div>{{ text_total_findings }}</div>
              </div>
            </div>
          </div>
          <div class="panel-footer">
            <span class="pull-left">{{ high_risk_findings }} {{ text_high_risk }}</span>
            <span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
            <div class="clearfix"></div>
          </div>
        </div>
      </div>
      
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-red">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-clock-o fa-5x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ overdue_audits }}</div>
                <div>{{ text_overdue_audits }}</div>
              </div>
            </div>
          </div>
          <div class="panel-footer">
            <span class="pull-left">{{ text_requires_attention }}</span>
            <span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
            <div class="clearfix"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="row">
      <div class="col-md-12">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-flash"></i> {{ text_quick_actions }}</h3>
          </div>
          <div class="panel-body">
            <div class="btn-group" role="group">
              <a href="{{ new_audit }}" class="btn btn-primary">
                <i class="fa fa-plus"></i> {{ text_new_audit }}
              </a>
              <button type="button" id="button-ai-risk-assessment" class="btn btn-info">
                <i class="fa fa-magic"></i> {{ text_ai_risk_assessment }}
              </button>
              <a href="{{ workflow_designer }}" class="btn btn-success">
                <i class="fa fa-sitemap"></i> {{ text_workflow_designer }}
              </a>
              <a href="{{ reports_link }}" class="btn btn-warning">
                <i class="fa fa-file-pdf-o"></i> {{ text_audit_reports }}
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Filters -->
    <div class="row">
      <div class="col-md-12">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-filter"></i> {{ text_filters }}</h3>
          </div>
          <div class="panel-body">
            <div class="row">
              <div class="col-md-3">
                <div class="form-group">
                  <label for="filter-type">{{ entry_audit_type }}</label>
                  <select name="filter_type" id="filter-type" class="form-control">
                    <option value="">{{ text_all_types }}</option>
                    <option value="financial" {% if filter_type == 'financial' %}selected="selected"{% endif %}>{{ text_financial_audit }}</option>
                    <option value="operational" {% if filter_type == 'operational' %}selected="selected"{% endif %}>{{ text_operational_audit }}</option>
                    <option value="compliance" {% if filter_type == 'compliance' %}selected="selected"{% endif %}>{{ text_compliance_audit }}</option>
                    <option value="it" {% if filter_type == 'it' %}selected="selected"{% endif %}>{{ text_it_audit }}</option>
                    <option value="internal" {% if filter_type == 'internal' %}selected="selected"{% endif %}>{{ text_internal_audit }}</option>
                  </select>
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label for="filter-status">{{ entry_status }}</label>
                  <select name="filter_status" id="filter-status" class="form-control">
                    <option value="">{{ text_all_statuses }}</option>
                    <option value="planning" {% if filter_status == 'planning' %}selected="selected"{% endif %}>{{ text_planning }}</option>
                    <option value="in_progress" {% if filter_status == 'in_progress' %}selected="selected"{% endif %}>{{ text_in_progress }}</option>
                    <option value="review" {% if filter_status == 'review' %}selected="selected"{% endif %}>{{ text_review }}</option>
                    <option value="completed" {% if filter_status == 'completed' %}selected="selected"{% endif %}>{{ text_completed }}</option>
                  </select>
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label for="filter-risk">{{ entry_risk_level }}</label>
                  <select name="filter_risk" id="filter-risk" class="form-control">
                    <option value="">{{ text_all_risks }}</option>
                    <option value="low" {% if filter_risk == 'low' %}selected="selected"{% endif %}>{{ text_low_risk }}</option>
                    <option value="medium" {% if filter_risk == 'medium' %}selected="selected"{% endif %}>{{ text_medium_risk }}</option>
                    <option value="high" {% if filter_risk == 'high' %}selected="selected"{% endif %}>{{ text_high_risk }}</option>
                    <option value="critical" {% if filter_risk == 'critical' %}selected="selected"{% endif %}>{{ text_critical_risk }}</option>
                  </select>
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>&nbsp;</label>
                  <div>
                    <button type="button" id="button-filter" class="btn btn-primary">
                      <i class="fa fa-search"></i> {{ button_filter }}
                    </button>
                    <button type="button" id="button-clear" class="btn btn-default">
                      <i class="fa fa-refresh"></i> {{ button_clear }}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Audits List -->
    <div class="row">
      <div class="col-md-8">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-list"></i> {{ text_audits_list }}</h3>
          </div>
          <div class="panel-body">
            <div class="table-responsive">
              <table class="table table-striped table-hover">
                <thead>
                  <tr>
                    <th>{{ column_reference }}</th>
                    <th>{{ column_title }}</th>
                    <th>{{ column_type }}</th>
                    <th>{{ column_status }}</th>
                    <th>{{ column_risk }}</th>
                    <th>{{ column_due_date }}</th>
                    <th>{{ column_action }}</th>
                  </tr>
                </thead>
                <tbody>
                  {% if audits %}
                  {% for audit in audits %}
                  <tr>
                    <td>{{ audit.reference }}</td>
                    <td>{{ audit.audit_title }}</td>
                    <td>
                      <span class="label label-info">{{ audit.audit_type_text }}</span>
                    </td>
                    <td>
                      {% if audit.status == 'completed' %}
                      <span class="label label-success">{{ audit.status_text }}</span>
                      {% elseif audit.status == 'in_progress' %}
                      <span class="label label-warning">{{ audit.status_text }}</span>
                      {% elseif audit.status == 'review' %}
                      <span class="label label-info">{{ audit.status_text }}</span>
                      {% else %}
                      <span class="label label-default">{{ audit.status_text }}</span>
                      {% endif %}
                    </td>
                    <td>
                      {% if audit.risk_level == 'critical' %}
                      <span class="label label-danger">{{ audit.risk_text }}</span>
                      {% elseif audit.risk_level == 'high' %}
                      <span class="label label-warning">{{ audit.risk_text }}</span>
                      {% elseif audit.risk_level == 'medium' %}
                      <span class="label label-info">{{ audit.risk_text }}</span>
                      {% else %}
                      <span class="label label-success">{{ audit.risk_text }}</span>
                      {% endif %}
                    </td>
                    <td>
                      {% if audit.is_overdue %}
                      <span class="text-danger">{{ audit.due_date }}</span>
                      {% else %}
                      {{ audit.due_date }}
                      {% endif %}
                    </td>
                    <td>
                      <div class="btn-group">
                        <a href="{{ audit.view }}" class="btn btn-sm btn-info" title="{{ button_view }}">
                          <i class="fa fa-eye"></i>
                        </a>
                        {% if audit.edit %}
                        <a href="{{ audit.edit }}" class="btn btn-sm btn-primary" title="{{ button_edit }}">
                          <i class="fa fa-pencil"></i>
                        </a>
                        {% endif %}
                        {% if audit.workflow %}
                        <a href="{{ audit.workflow }}" class="btn btn-sm btn-success" title="{{ button_workflow }}">
                          <i class="fa fa-sitemap"></i>
                        </a>
                        {% endif %}
                        {% if audit.findings %}
                        <a href="{{ audit.findings }}" class="btn btn-sm btn-warning" title="{{ button_findings }}">
                          <i class="fa fa-exclamation-triangle"></i>
                        </a>
                        {% endif %}
                      </div>
                    </td>
                  </tr>
                  {% endfor %}
                  {% else %}
                  <tr>
                    <td colspan="7" class="text-center">{{ text_no_results }}</td>
                  </tr>
                  {% endif %}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Recent Findings -->
      <div class="col-md-4">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-exclamation-triangle"></i> {{ text_recent_findings }}</h3>
          </div>
          <div class="panel-body">
            {% if recent_findings %}
            {% for finding in recent_findings %}
            <div class="media">
              <div class="media-left">
                {% if finding.risk_level == 'critical' %}
                <span class="label label-danger">{{ finding.risk_text }}</span>
                {% elseif finding.risk_level == 'high' %}
                <span class="label label-warning">{{ finding.risk_text }}</span>
                {% else %}
                <span class="label label-info">{{ finding.risk_text }}</span>
                {% endif %}
              </div>
              <div class="media-body">
                <h6 class="media-heading">{{ finding.finding_title }}</h6>
                <small class="text-muted">{{ finding.audit_title }} - {{ finding.date_created }}</small>
                <p><small>{{ finding.description|slice(0, 100) }}...</small></p>
              </div>
            </div>
            <hr>
            {% endfor %}
            {% else %}
            <p class="text-center text-muted">{{ text_no_recent_findings }}</p>
            {% endif %}
          </div>
        </div>
        
        <!-- AI Insights -->
        <div class="panel panel-info">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-magic"></i> {{ text_ai_insights }}</h3>
          </div>
          <div class="panel-body">
            {% if ai_insights %}
            {% for insight in ai_insights %}
            <div class="alert alert-info">
              <strong>{{ insight.title }}</strong><br>
              <small>{{ insight.description }}</small>
            </div>
            {% endfor %}
            {% else %}
            <p class="text-center text-muted">{{ text_no_ai_insights }}</p>
            <button type="button" id="button-generate-insights" class="btn btn-info btn-block">
              <i class="fa fa-magic"></i> {{ button_generate_insights }}
            </button>
            {% endif %}
          </div>
        </div>
      </div>
    </div>

    <!-- Pagination -->
    <div class="row">
      <div class="col-sm-6 text-left">{{ pagination }}</div>
      <div class="col-sm-6 text-right">{{ results }}</div>
    </div>
  </div>
</div>

<script type="text/javascript">
$(document).ready(function() {
    // Initialize tooltips
    $('[data-toggle="tooltip"]').tooltip();
    
    // Filter functionality
    $('#button-filter').on('click', function() {
        var url = 'index.php?route=accounts/audit_trail_advanced&token={{ token }}';
        
        var filter_type = $('#filter-type').val();
        if (filter_type) {
            url += '&filter_type=' + encodeURIComponent(filter_type);
        }
        
        var filter_status = $('#filter-status').val();
        if (filter_status) {
            url += '&filter_status=' + encodeURIComponent(filter_status);
        }
        
        var filter_risk = $('#filter-risk').val();
        if (filter_risk) {
            url += '&filter_risk=' + encodeURIComponent(filter_risk);
        }
        
        location = url;
    });
    
    // Clear filters
    $('#button-clear').on('click', function() {
        location = 'index.php?route=accounts/audit_trail_advanced&token={{ token }}';
    });
    
    // AI Risk Assessment
    $('#button-ai-risk-assessment').on('click', function() {
        $.ajax({
            url: 'index.php?route=accounts/audit_trail_advanced/aiRiskAssessment&token={{ token }}',
            type: 'post',
            dataType: 'json',
            beforeSend: function() {
                $('#button-ai-risk-assessment').button('loading');
            },
            complete: function() {
                $('#button-ai-risk-assessment').button('reset');
            },
            success: function(json) {
                if (json.success) {
                    alert(json.message);
                    location.reload();
                } else {
                    alert(json.error);
                }
            }
        });
    });
    
    // Generate AI Insights
    $('#button-generate-insights').on('click', function() {
        $.ajax({
            url: 'index.php?route=accounts/audit_trail_advanced/generateInsights&token={{ token }}',
            type: 'post',
            dataType: 'json',
            beforeSend: function() {
                $('#button-generate-insights').button('loading');
            },
            complete: function() {
                $('#button-generate-insights').button('reset');
            },
            success: function(json) {
                if (json.success) {
                    location.reload();
                } else {
                    alert(json.error);
                }
            }
        });
    });
    
    // Auto-refresh for real-time updates
    setInterval(function() {
        // Refresh AI insights panel
        $.ajax({
            url: 'index.php?route=accounts/audit_trail_advanced/getInsights&token={{ token }}',
            type: 'get',
            dataType: 'json',
            success: function(json) {
                if (json.insights && json.insights.length > 0) {
                    // Update insights panel
                    var html = '';
                    $.each(json.insights, function(index, insight) {
                        html += '<div class="alert alert-info">';
                        html += '<strong>' + insight.title + '</strong><br>';
                        html += '<small>' + insight.description + '</small>';
                        html += '</div>';
                    });
                    $('.panel-info .panel-body').html(html);
                }
            }
        });
    }, 60000); // Refresh every minute
});
</script>

{{ footer }}
