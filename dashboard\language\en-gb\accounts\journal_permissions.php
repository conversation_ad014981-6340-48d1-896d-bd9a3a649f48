<?php
// Heading
$_['heading_title']                    = 'Journal Permissions';

// Text
$_['text_success']                     = 'Success: Journal permissions have been saved successfully!';
$_['text_list']                        = 'Journal Permissions List';
$_['text_form']                        = 'Journal Permission Form';
$_['text_add']                         = 'Add Permission';
$_['text_edit']                        = 'Edit Permission';
$_['text_view']                        = 'View Permission';
$_['text_delete']                      = 'Delete Permission';
$_['text_journal_permissions']         = 'Journal Permissions';
$_['text_loading']                     = 'Loading...';
$_['text_processing']                  = 'Processing...';

// Permission Types
$_['text_edit_permission']             = 'Edit Permission';
$_['text_delete_permission']           = 'Delete Permission';
$_['text_post_permission']             = 'Post Permission';
$_['text_approve_permission']          = 'Approve Permission';
$_['text_view_permission']             = 'View Permission';

// Journal Status
$_['text_draft']                       = 'Draft';
$_['text_posted']                      = 'Posted';
$_['text_approved']                    = 'Approved';
$_['text_cancelled']                   = 'Cancelled';

// User Roles
$_['text_financial_manager']           = 'Financial Manager';
$_['text_cfo']                         = 'Chief Financial Officer';
$_['text_system_admin']                = 'System Administrator';
$_['text_accountant']                  = 'Accountant';
$_['text_junior_accountant']           = 'Junior Accountant';

// Permission Messages - Controller language variables
$_['error_no_edit_permission']         = 'No permission to edit journal entries';
$_['error_no_edit_posted']             = 'Posted entries can only be edited by financial manager';
$_['error_old_posted_journals']        = 'Entries posted more than 30 days ago require special approval';
$_['restriction_edit_posted']          = 'Editing posted entry - all changes will be logged in audit trail';
$_['error_no_edit_approved']           = 'Approved entries can only be edited by CFO';
$_['restriction_edit_approved']        = 'Editing approved entry - requires re-approval';
$_['error_closed_period']              = 'Cannot edit entries in closed periods';
$_['restriction_closed_period']        = 'Editing in closed period - requires financial manager approval';
$_['error_no_account_access']          = 'No permission to access one of the used accounts';
$_['restriction_large_amount']         = 'Large amount - requires additional approval';
$_['restriction_after_hours']          = 'Editing after business hours - manager will be notified';
$_['error_no_delete_permission']       = 'No permission to delete journal entries';
$_['error_no_delete_posted']           = 'Cannot delete posted entries - unpost first';
$_['error_no_delete_approved']         = 'Cannot delete approved entries';
$_['error_no_delete_auto']             = 'Cannot delete auto-generated entries';
$_['error_no_post_permission']         = 'No permission to post journal entries';
$_['error_only_draft_post']            = 'Only drafts can be posted';
$_['error_unbalanced_journal']         = 'Journal entry is not balanced';
$_['error_no_post_closed_period']      = 'Cannot post in closed period';
$_['warning_large_amount']             = 'Large amount - verify data accuracy';
$_['warning_weekend_posting']          = 'Posting on weekend';

// Column
$_['column_user']                      = 'User';
$_['column_role']                      = 'Role';
$_['column_permission']                = 'Permission';
$_['column_journal_type']              = 'Journal Type';
$_['column_amount_limit']              = 'Amount Limit';
$_['column_date_created']              = 'Date Created';
$_['column_status']                    = 'Status';
$_['column_action']                    = 'Action';

// Entry
$_['entry_user']                       = 'User';
$_['entry_role']                       = 'Role';
$_['entry_permission_type']            = 'Permission Type';
$_['entry_journal_type']               = 'Journal Type';
$_['entry_amount_limit']               = 'Amount Limit';
$_['entry_start_date']                 = 'Start Date';
$_['entry_end_date']                   = 'End Date';
$_['entry_description']                = 'Description';
$_['entry_status']                     = 'Status';

// Button
$_['button_add_permission']            = 'Add Permission';
$_['button_edit_permission']           = 'Edit Permission';
$_['button_delete_permission']         = 'Delete Permission';
$_['button_check_permission']          = 'Check Permission';
$_['button_grant_permission']          = 'Grant Permission';
$_['button_revoke_permission']         = 'Revoke Permission';
$_['button_save_and_new']              = 'Save & New';
$_['button_export']                    = 'Export';
$_['button_import']                    = 'Import';
$_['button_print']                     = 'Print';

// Tab
$_['tab_general']                      = 'General';
$_['tab_permissions']                  = 'Permissions';
$_['tab_restrictions']                 = 'Restrictions';
$_['tab_audit']                        = 'Audit';
$_['tab_history']                      = 'History';

// Help
$_['help_permission_type']             = 'Type of permission to grant to the user';
$_['help_amount_limit']                = 'Maximum amount the user can handle';
$_['help_journal_type']                = 'Type of journal entries this permission applies to';

// Error
$_['error_warning']                    = 'Warning: Please check the form carefully for errors!';
$_['error_permission']                 = 'Warning: You do not have permission to access journal permissions!';
$_['error_user_required']              = 'User is required!';
$_['error_permission_type_required']   = 'Permission type is required!';
$_['error_amount_limit_invalid']       = 'Amount limit is invalid!';
$_['error_date_range_invalid']         = 'Date range is invalid!';
$_['error_permission_exists']          = 'Permission already exists!';
$_['error_permission_not_found']       = 'Permission not found!';

// Success
$_['success_permission_added']         = 'Permission added successfully!';
$_['success_permission_updated']       = 'Permission updated successfully!';
$_['success_permission_deleted']       = 'Permission deleted successfully!';
$_['success_permission_granted']       = 'Permission granted successfully!';
$_['success_permission_revoked']       = 'Permission revoked successfully!';

// Additional variables for template
$_['text_actions'] = 'Actions';
$_['text_add_permission'] = 'Add Permission';
$_['text_export_options'] = 'Export Options';
$_['text_permission_filters'] = 'Permission Filters';
$_['text_all_users'] = 'All Users';
$_['text_all_roles'] = 'All Roles';
$_['text_all_types'] = 'All Types';
$_['text_active'] = 'Active';
$_['text_inactive'] = 'Inactive';
$_['text_total_permissions'] = 'Total Permissions';
$_['text_active_permissions'] = 'Active Permissions';
$_['text_expired_permissions'] = 'Expired Permissions';
$_['text_permissions'] = 'Permissions';
$_['text_permissions_list'] = 'Permissions List';
$_['text_no_permissions'] = 'No permissions found';
$_['text_exporting'] = 'Exporting';
$_['text_loading'] = 'Loading';
$_['button_filter'] = 'Filter';
$_['entry_user_id'] = 'User';
$_['entry_role_id'] = 'Role';
?>
