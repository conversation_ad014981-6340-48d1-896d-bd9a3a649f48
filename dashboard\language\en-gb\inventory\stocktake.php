<?php
// Heading
$_['heading_title']                 = 'Inventory Stocktake';
$_['heading_stocktake_form']        = 'Stocktake Form';
$_['heading_stocktake_view']        = 'Stocktake View';

// Text
$_['text_success']                  = 'Success: You have modified stocktake!';
$_['text_list']                     = 'Stocktake List';
$_['text_add']                      = 'Add Stocktake';
$_['text_edit']                     = 'Edit Stocktake';
$_['text_form']                     = 'Stocktake Form';
$_['text_select']                   = '--- Select ---';
$_['text_all_status']               = 'All Statuses';
$_['text_all_types']                = 'All Types';
$_['text_all_categories']           = 'All Categories';
$_['text_status_draft']             = 'Draft';
$_['text_status_in_progress']       = 'In Progress';
$_['text_status_completed']         = 'Completed';
$_['text_status_cancelled']         = 'Cancelled';
$_['text_type_full']                = 'Full Stocktake';
$_['text_type_partial']             = 'Partial Stocktake';
$_['text_type_spot']                = 'Spot Check';
$_['text_type_cycle']               = 'Cycle Count';
$_['text_products']                 = 'Products';
$_['text_summary']                  = 'Stocktake Summary';
$_['text_confirm_complete']         = 'Are you sure you want to complete this stocktake? This will adjust inventory based on the count results.';
$_['text_confirm_cancel']           = 'Are you sure you want to cancel this stocktake?';
$_['text_confirm_delete']           = 'Are you sure you want to delete this stocktake?';
$_['text_complete_success']         = 'Success: Stocktake has been completed!';
$_['text_cancel_success']           = 'Success: Stocktake has been cancelled!';
$_['text_stocktake_details']        = 'Stocktake Details';
$_['text_stocktake_products']       = 'Stocktake Products';
$_['text_stocktake_summary']        = 'Stocktake Summary';
$_['text_total_products']           = 'Total Products:';
$_['text_total_expected']           = 'Total Expected:';
$_['text_total_counted']            = 'Total Counted:';
$_['text_total_variance']           = 'Total Variance:';
$_['text_variance_percentage']      = 'Variance Percentage:';
$_['text_variance_value']           = 'Variance Value:';
$_['text_stocktake_instructions']   = 'Stocktake Instructions';
$_['text_instruction_1']            = '1. Create a new stocktake and select the type and branch.';
$_['text_instruction_2']            = '2. Add the products you want to count.';
$_['text_instruction_3']            = '3. Enter the counted quantities for each product.';
$_['text_instruction_4']            = '4. Click "Complete" to finalize the stocktake and adjust inventory.';
$_['text_instruction_5']            = '5. You can print a stocktake report to keep as a record.';
$_['text_loading_products']         = 'Loading products...';
$_['text_no_products']              = 'No products in this branch.';
$_['text_add_products']             = 'Add Products';
$_['text_add_all_products']         = 'Add All Products';
$_['text_add_category_products']    = 'Add Category Products';
$_['text_add_selected_products']    = 'Add Selected Products';
$_['text_export_to_excel']          = 'Export to Excel';
$_['text_export_to_pdf']            = 'Export to PDF';
$_['text_import_from_excel']        = 'Import from Excel';
$_['text_import_instructions']      = 'Excel file must contain the following columns: Product ID, Counted Quantity, Notes';
$_['text_download_template']        = 'Download Excel Template';
$_['text_upload_file']              = 'Upload File';
$_['text_import_success']           = 'Success: Your data has been imported!';
$_['text_variance_positive']        = 'Surplus';
$_['text_variance_negative']        = 'Shortage';
$_['text_variance_zero']            = 'No Variance';
$_['text_stocktake_history']        = 'Stocktake History';
$_['text_created_by']               = 'Created By:';
$_['text_completed_by']             = 'Completed By:';
$_['text_date_created']             = 'Date Created:';
$_['text_date_completed']           = 'Date Completed:';
$_['text_pagination']               = 'Showing %d to %d of %d (%d Pages)';
$_['text_loading']                  = 'Loading...';
$_['text_total']                    = 'Total';
$_['text_stocktake_created']        = 'New Stocktake Created';
$_['text_stocktake_created_message'] = 'A new stocktake with reference %s has been created';
$_['text_stocktake_updated']        = 'Stocktake Updated';
$_['text_stocktake_updated_message'] = 'Stocktake with reference %s has been updated';
$_['text_stocktake_completed']      = 'Stocktake Completed';
$_['text_stocktake_completed_message'] = 'Stocktake with reference %s has been completed';
$_['text_stocktake_cancelled']      = 'Stocktake Cancelled';
$_['text_stocktake_cancelled_message'] = 'Stocktake with reference %s has been cancelled';
$_['text_stocktake_deleted']        = 'Stocktake Deleted';
$_['text_stocktake_deleted_message'] = 'Stocktake with reference %s has been deleted';

// Column
$_['column_reference']              = 'Reference';
$_['column_branch']                 = 'Branch';
$_['column_date']                   = 'Stocktake Date';
$_['column_type']                   = 'Type';
$_['column_status']                 = 'Status';
$_['column_total_items']            = 'Total Items';
$_['column_created_by']             = 'Created By';
$_['column_date_added']             = 'Date Added';
$_['column_action']                 = 'Action';
$_['column_product']                = 'Product';
$_['column_model']                  = 'Model';
$_['column_sku']                    = 'SKU';
$_['column_unit']                   = 'Unit';
$_['column_expected_quantity']      = 'Expected Qty';
$_['column_counted_quantity']       = 'Counted Qty';
$_['column_variance_quantity']      = 'Variance';
$_['column_variance_percentage']    = 'Variance %';
$_['column_notes']                  = 'Notes';

// Entry
$_['entry_reference']               = 'Reference';
$_['entry_branch']                  = 'Branch';
$_['entry_stocktake_date']          = 'Stocktake Date';
$_['entry_type']                    = 'Type';
$_['entry_status']                  = 'Status';
$_['entry_notes']                   = 'Notes';
$_['entry_product']                 = 'Product';
$_['entry_unit']                    = 'Unit';
$_['entry_expected_quantity']       = 'Expected Quantity';
$_['entry_counted_quantity']        = 'Counted Quantity';
$_['entry_category']                = 'Category';
$_['entry_date_from']               = 'Date From';
$_['entry_date_to']                 = 'Date To';

// Button
$_['button_filter']                 = 'Filter';
$_['button_add_product']            = 'Add Product';
$_['button_remove']                 = 'Remove';
$_['button_complete']               = 'Complete';
$_['button_cancel']                 = 'Cancel';
$_['button_print']                  = 'Print';
$_['button_export']                 = 'Export';
$_['button_import']                 = 'Import';
$_['button_view']                   = 'View';

// Error
$_['error_permission']              = 'Warning: You do not have permission to modify stocktake!';
$_['error_reference']               = 'Reference is required!';
$_['error_branch']                  = 'Branch is required!';
$_['error_stocktake_date']          = 'Stocktake date is required!';
$_['error_type']                    = 'Type is required!';
$_['error_products']                = 'You must add at least one product!';
$_['error_counted_quantity']        = 'Counted quantity must be a number!';
$_['error_stocktake_id']            = 'Stocktake ID is required!';
$_['error_stocktake_completed']     = 'Cannot modify a completed stocktake!';
$_['error_stocktake_cancelled']     = 'Cannot modify a cancelled stocktake!';
$_['error_import_file']             = 'Please select a file to import!';
$_['error_import_format']           = 'Invalid file format!';
$_['error_product']                 = 'Product is required!';
$_['error_unit']                    = 'Unit is required!';
$_['error_expected_quantity']       = 'Expected quantity is required!';
$_['error_counted_quantity_required'] = 'Counted quantity is required!';
$_['error_reference_exists']        = 'This reference already exists!';
