{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <a href="{{ return }}" data-toggle="tooltip" title="{{ button_return }}" class="btn btn-default"><i class="fa fa-reply"></i></a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    <div class="panel panel-default">
      <div id="search-code">
        <div class="panel-heading">
          <h3 class="panel-title"><i class="fa fa-filter"></i> {{ text_search }}</h3>
        </div>
        <div class="panel-body">
          <div class="form-group">
            <label class="control-label" for="input-name">{{ entry_name }}</label>
            <input type="text" name="search_query" value="{{ search_query }}" placeholder="{{ entry_search_name }}" id="search_query" class="form-control" />
          </div>
          <div class="form-group text-right">
            <button type="button" id="button-search" class="btn btn-default"><i class="fa fa-search"></i> {{ button_search_query }}</button>
          </div>
        </div>
      </div>
    </div>
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-list"></i> {{ text_list }}</h3>
      </div>
      <div class="panel-body">
          <div class="table-responsive">
            <table class="table table-bordered table-hover">
              <thead>
              <tr>
                <td class="text-left">{{ column_name }}</td>
                <td class="text-center">{{ column_date_added }}</td>
                <td class="text-right">{{ column_action }}</td>
              </tr>
              </thead>
              <tbody>
                {% if modifications %}
                {% for modification in modifications %}
                <tr>
                  <td class="text-left">{{ modification.name }}</td>
                  <td class="text-center">{{ modification.date_added }}</td>
                  <td class="text-right">
                    <a href="{{ modification.link_editor }}" data-toggle="tooltip" title="{{ button_edit }}" class="btn btn-primary"><i class="fa fa-pencil"></i></a>
                  </td>
                </tr>
                {% endfor %}
                {% else %}
                <tr>
                  <td class="text-center" colspan="3">{{ text_no_results }}</td>
                </tr>
                {% endif %}
              </tbody>
            </table>
          </div>
        <div class="row">
          <div class="col-sm-6 text-left">{{ pagination }}</div>
          <div class="col-sm-6 text-right">{{ results }}</div>
        </div>
      </div>
    </div>
  </div>
  <script type="text/javascript">
    $('#button-search').on('click', function() {

      var url = 'index.php?route=extension/modification/search&user_token={{ user_token }}';

      var search_query = $('input[name=\'search_query\']').val();

      if (search_query) {
        url += '&search_query=' + encodeURIComponent(search_query);
      }

      location = url;
    });
  </script>
</div>
{{ footer }}