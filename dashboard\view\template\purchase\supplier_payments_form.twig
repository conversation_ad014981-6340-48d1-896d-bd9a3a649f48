{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="submit" form="purchase\supplier_payments-form" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fas fa-save"></i></button>
        <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-light"><i class="fas fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    
    <div class="card">
      <div class="card-header"><i class="fas fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}chart-bar{% elif screen_type == 'settings' %}cog{% else %}pencil-alt{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="card-body">
        <form id="purchase\supplier_payments-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-add">{{ text_add }}</label>
            <div class="col-sm-10">
              <input type="text" name="add" value="{{ add }}" placeholder="{{ text_add }}" id="input-add" class="form-control" />
              {% if error_add %}
                <div class="invalid-feedback">{{ error_add }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-date_end">{{ text_date_end }}</label>
            <div class="col-sm-10">
              <input type="text" name="date_end" value="{{ date_end }}" placeholder="{{ text_date_end }}" id="input-date_end" class="form-control" />
              {% if error_date_end %}
                <div class="invalid-feedback">{{ error_date_end }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-date_start">{{ text_date_start }}</label>
            <div class="col-sm-10">
              <input type="text" name="date_start" value="{{ date_start }}" placeholder="{{ text_date_start }}" id="input-date_start" class="form-control" />
              {% if error_date_start %}
                <div class="invalid-feedback">{{ error_date_start }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-delete">{{ text_delete }}</label>
            <div class="col-sm-10">
              <input type="text" name="delete" value="{{ delete }}" placeholder="{{ text_delete }}" id="input-delete" class="form-control" />
              {% if error_delete %}
                <div class="invalid-feedback">{{ error_delete }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_payment_amount">{{ text_error_payment_amount }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_payment_amount" value="{{ error_payment_amount }}" placeholder="{{ text_error_payment_amount }}" id="input-error_payment_amount" class="form-control" />
              {% if error_error_payment_amount %}
                <div class="invalid-feedback">{{ error_error_payment_amount }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_payment_date">{{ text_error_payment_date }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_payment_date" value="{{ error_payment_date }}" placeholder="{{ text_error_payment_date }}" id="input-error_payment_date" class="form-control" />
              {% if error_error_payment_date %}
                <div class="invalid-feedback">{{ error_error_payment_date }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_payment_method">{{ text_error_payment_method }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_payment_method" value="{{ error_payment_method }}" placeholder="{{ text_error_payment_method }}" id="input-error_payment_method" class="form-control" />
              {% if error_error_payment_method %}
                <div class="invalid-feedback">{{ error_error_payment_method }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_supplier_id">{{ text_error_supplier_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_supplier_id" value="{{ error_supplier_id }}" placeholder="{{ text_error_supplier_id }}" id="input-error_supplier_id" class="form-control" />
              {% if error_error_supplier_id %}
                <div class="invalid-feedback">{{ error_error_supplier_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_date_end">{{ text_filter_date_end }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_date_end" value="{{ filter_date_end }}" placeholder="{{ text_filter_date_end }}" id="input-filter_date_end" class="form-control" />
              {% if error_filter_date_end %}
                <div class="invalid-feedback">{{ error_filter_date_end }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_date_start">{{ text_filter_date_start }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_date_start" value="{{ filter_date_start }}" placeholder="{{ text_filter_date_start }}" id="input-filter_date_start" class="form-control" />
              {% if error_filter_date_start %}
                <div class="invalid-feedback">{{ error_filter_date_start }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_payment_method">{{ text_filter_payment_method }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_payment_method" value="{{ filter_payment_method }}" placeholder="{{ text_filter_payment_method }}" id="input-filter_payment_method" class="form-control" />
              {% if error_filter_payment_method %}
                <div class="invalid-feedback">{{ error_filter_payment_method }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_status">{{ text_filter_status }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_status" value="{{ filter_status }}" placeholder="{{ text_filter_status }}" id="input-filter_status" class="form-control" />
              {% if error_filter_status %}
                <div class="invalid-feedback">{{ error_filter_status }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_supplier_id">{{ text_filter_supplier_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_supplier_id" value="{{ filter_supplier_id }}" placeholder="{{ text_filter_supplier_id }}" id="input-filter_supplier_id" class="form-control" />
              {% if error_filter_supplier_id %}
                <div class="invalid-feedback">{{ error_filter_supplier_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="invalid-feedback">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-notes">{{ text_notes }}</label>
            <div class="col-sm-10">
              <input type="text" name="notes" value="{{ notes }}" placeholder="{{ text_notes }}" id="input-notes" class="form-control" />
              {% if error_notes %}
                <div class="invalid-feedback">{{ error_notes }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-order">{{ text_order }}</label>
            <div class="col-sm-10">
              <input type="text" name="order" value="{{ order }}" placeholder="{{ text_order }}" id="input-order" class="form-control" />
              {% if error_order %}
                <div class="invalid-feedback">{{ error_order }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-pagination">{{ text_pagination }}</label>
            <div class="col-sm-10">
              <input type="text" name="pagination" value="{{ pagination }}" placeholder="{{ text_pagination }}" id="input-pagination" class="form-control" />
              {% if error_pagination %}
                <div class="invalid-feedback">{{ error_pagination }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-payment_amount">{{ text_payment_amount }}</label>
            <div class="col-sm-10">
              <input type="text" name="payment_amount" value="{{ payment_amount }}" placeholder="{{ text_payment_amount }}" id="input-payment_amount" class="form-control" />
              {% if error_payment_amount %}
                <div class="invalid-feedback">{{ error_payment_amount }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-payment_date">{{ text_payment_date }}</label>
            <div class="col-sm-10">
              <input type="text" name="payment_date" value="{{ payment_date }}" placeholder="{{ text_payment_date }}" id="input-payment_date" class="form-control" />
              {% if error_payment_date %}
                <div class="invalid-feedback">{{ error_payment_date }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-payment_method_id">{{ text_payment_method_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="payment_method_id" value="{{ payment_method_id }}" placeholder="{{ text_payment_method_id }}" id="input-payment_method_id" class="form-control" />
              {% if error_payment_method_id %}
                <div class="invalid-feedback">{{ error_payment_method_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-payment_methods">{{ text_payment_methods }}</label>
            <div class="col-sm-10">
              <input type="text" name="payment_methods" value="{{ payment_methods }}" placeholder="{{ text_payment_methods }}" id="input-payment_methods" class="form-control" />
              {% if error_payment_methods %}
                <div class="invalid-feedback">{{ error_payment_methods }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-payment_report">{{ text_payment_report }}</label>
            <div class="col-sm-10">
              <input type="text" name="payment_report" value="{{ payment_report }}" placeholder="{{ text_payment_report }}" id="input-payment_report" class="form-control" />
              {% if error_payment_report %}
                <div class="invalid-feedback">{{ error_payment_report }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-payment_statistics">{{ text_payment_statistics }}</label>
            <div class="col-sm-10">
              <input type="text" name="payment_statistics" value="{{ payment_statistics }}" placeholder="{{ text_payment_statistics }}" id="input-payment_statistics" class="form-control" />
              {% if error_payment_statistics %}
                <div class="invalid-feedback">{{ error_payment_statistics }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-payment_summary">{{ text_payment_summary }}</label>
            <div class="col-sm-10">
              <input type="text" name="payment_summary" value="{{ payment_summary }}" placeholder="{{ text_payment_summary }}" id="input-payment_summary" class="form-control" />
              {% if error_payment_summary %}
                <div class="invalid-feedback">{{ error_payment_summary }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-payments">{{ text_payments }}</label>
            <div class="col-sm-10">
              <input type="text" name="payments" value="{{ payments }}" placeholder="{{ text_payments }}" id="input-payments" class="form-control" />
              {% if error_payments %}
                <div class="invalid-feedback">{{ error_payments }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-reference_number">{{ text_reference_number }}</label>
            <div class="col-sm-10">
              <input type="text" name="reference_number" value="{{ reference_number }}" placeholder="{{ text_reference_number }}" id="input-reference_number" class="form-control" />
              {% if error_reference_number %}
                <div class="invalid-feedback">{{ error_reference_number }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-results">{{ text_results }}</label>
            <div class="col-sm-10">
              <input type="text" name="results" value="{{ results }}" placeholder="{{ text_results }}" id="input-results" class="form-control" />
              {% if error_results %}
                <div class="invalid-feedback">{{ error_results }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-selected">{{ text_selected }}</label>
            <div class="col-sm-10">
              <input type="text" name="selected" value="{{ selected }}" placeholder="{{ text_selected }}" id="input-selected" class="form-control" />
              {% if error_selected %}
                <div class="invalid-feedback">{{ error_selected }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort">{{ text_sort }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort" value="{{ sort }}" placeholder="{{ text_sort }}" id="input-sort" class="form-control" />
              {% if error_sort %}
                <div class="invalid-feedback">{{ error_sort }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_amount">{{ text_sort_amount }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_amount" value="{{ sort_amount }}" placeholder="{{ text_sort_amount }}" id="input-sort_amount" class="form-control" />
              {% if error_sort_amount %}
                <div class="invalid-feedback">{{ error_sort_amount }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_date">{{ text_sort_date }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_date" value="{{ sort_date }}" placeholder="{{ text_sort_date }}" id="input-sort_date" class="form-control" />
              {% if error_sort_date %}
                <div class="invalid-feedback">{{ error_sort_date }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_payment_number">{{ text_sort_payment_number }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_payment_number" value="{{ sort_payment_number }}" placeholder="{{ text_sort_payment_number }}" id="input-sort_payment_number" class="form-control" />
              {% if error_sort_payment_number %}
                <div class="invalid-feedback">{{ error_sort_payment_number }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_status">{{ text_sort_status }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_status" value="{{ sort_status }}" placeholder="{{ text_sort_status }}" id="input-sort_status" class="form-control" />
              {% if error_sort_status %}
                <div class="invalid-feedback">{{ error_sort_status }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_supplier">{{ text_sort_supplier }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_supplier" value="{{ sort_supplier }}" placeholder="{{ text_sort_supplier }}" id="input-sort_supplier" class="form-control" />
              {% if error_sort_supplier %}
                <div class="invalid-feedback">{{ error_sort_supplier }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-status">{{ text_status }}</label>
            <div class="col-sm-10">
              <input type="text" name="status" value="{{ status }}" placeholder="{{ text_status }}" id="input-status" class="form-control" />
              {% if error_status %}
                <div class="invalid-feedback">{{ error_status }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-statuses">{{ text_statuses }}</label>
            <div class="col-sm-10">
              <input type="text" name="statuses" value="{{ statuses }}" placeholder="{{ text_statuses }}" id="input-statuses" class="form-control" />
              {% if error_statuses %}
                <div class="invalid-feedback">{{ error_statuses }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-supplier_id">{{ text_supplier_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="supplier_id" value="{{ supplier_id }}" placeholder="{{ text_supplier_id }}" id="input-supplier_id" class="form-control" />
              {% if error_supplier_id %}
                <div class="invalid-feedback">{{ error_supplier_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-suppliers">{{ text_suppliers }}</label>
            <div class="col-sm-10">
              <input type="text" name="suppliers" value="{{ suppliers }}" placeholder="{{ text_suppliers }}" id="input-suppliers" class="form-control" />
              {% if error_suppliers %}
                <div class="invalid-feedback">{{ error_suppliers }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_form">{{ text_text_form }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_form" value="{{ text_form }}" placeholder="{{ text_text_form }}" id="input-text_form" class="form-control" />
              {% if error_text_form %}
                <div class="invalid-feedback">{{ error_text_form }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="invalid-feedback">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}