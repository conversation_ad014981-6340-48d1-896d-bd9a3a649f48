!function(t){"function"==typeof define&&define.amd?define(["jquery"],t):"object"==typeof module&&module.exports?module.exports=function(e,s){return void 0===s&&(s="undefined"!=typeof window?require("jquery"):require("jquery")(e)),t(s),s}:t(jQuery)}(function(t){var e;!function(t){var e=function(){function t(){}return t.calculate=function(t){var e,s,o;if(t.length<=0)throw new TypeError("Invalid container trail specified for scrollbar dimensions calculation");for(var l=0,i=t;l<i.length;l++){var n=i[l];s=document.createElement(n.tagName),s.className=n.classes,o?o.appendChild(s):e=s,o=s}e.style.position="fixed",e.style.top="0",e.style.left="0",e.style.visibility="hidden",e.style.width="200px",e.style.height="200px",s.style.overflow="hidden",document.body.appendChild(e);var r=s.clientWidth;s.style.overflow="scroll";var a=s.clientWidth;return document.body.removeChild(e),r-a},t}();t.ScrollbarDimensions=e}(e||(e={}));var e;return function(e){var s=function(){function s(o,l){var i=this;this.containerElm=o,this.options={axes:["x","y"],preventOuterScroll:!1,cssPrefix:"scrollert",eventNamespace:"scrollert",contentSelector:null,useNativeFloatingScrollbars:!0},this.scrollbarElms={x:null,y:null},this.scrollCache={x:null,y:null},this.browserHasFloatingScrollbars=!1,this.onScrollWheel=function(t){for(var e=t.originalEvent,s=0,o=i.options.axes;s<o.length;s++){var l=o[s],n=e["delta"+l.toUpperCase()];n&&i.scrollbarElms[l]&&(t.target===i.scrollbarElms[l].scrollbar.get(0)||t.target===i.scrollbarElms[l].track.get(0))?(t.preventDefault(),i.contentElm["y"===l?"scrollTop":"scrollLeft"](i.getValue(i.contentElm,"scrollPos",l)+n)):i.options.preventOuterScroll===!0&&0!==n&&i.preventOuterScroll(l,n<0?"heen":"weer",t)}},this.onKeyDown=function(t){document.activeElement===i.contentElm[0]&&([37,38,33,36].indexOf(t.which)!==-1?i.preventOuterScroll([38,33,36].indexOf(t.which)!==-1?"y":"x","heen",t):[39,40,32,34,35].indexOf(t.which)!==-1&&i.preventOuterScroll([40,35,36,34,32].indexOf(t.which)!==-1?"y":"x","weer",t))},this.offsetContentElmScrollbars=function(t){void 0===t&&(t=!1);var s=e.ScrollbarDimensions.calculate([{tagName:i.containerElm.prop("tagName"),classes:i.containerElm.prop("class")},{tagName:i.contentElm.prop("tagName"),classes:i.contentElm.prop("class")}]),o=!1;0===s&&i.hasVisibleFloatingScrollbar()===!0&&(o=!0,s=20);var l={};i.options.axes.indexOf("y")!==-1&&(l["overflow-y"]="scroll",s&&(l.right=-s+"px"),o&&(l["padding-right"]=!1)),i.options.axes.indexOf("x")!==-1&&(l["overflow-x"]="scroll",s&&(l.bottom=-s+"px"),o&&(l["padding-bottom"]=!1)),i.originalCssValues||(i.originalCssValues=i.contentElm.css(Object.keys(l))),o&&l["padding-right"]===!1&&(l["padding-right"]=parseInt(i.originalCssValues["padding-right"])+s+"px"),o&&l["padding-bottom"]===!1&&(l["padding-bottom"]=parseInt(i.originalCssValues["padding-bottom"])+s+"px"),i.contentElm.css(l)},this.onScrollbarMousedown=function(t,e,s,o){o.target===e[0]?(i.scrollToClickedPosition(t,o),i.trackMousedown(t,e,o)):o.target===s[0]&&i.trackMousedown(t,e,o)},this.options=t.extend({},this.options,l),this.options.eventNamespace=this.options.eventNamespace+ ++s.eventNamespaceId,this.contentElm=this.containerElm.children(this.options.contentSelector||"."+this.options.cssPrefix+"-content"),this.options.useNativeFloatingScrollbars===!0&&(this.browserHasFloatingScrollbars=e.ScrollbarDimensions.calculate([{tagName:"div",classes:""}])<=0),this.options.useNativeFloatingScrollbars===!1||this.browserHasFloatingScrollbars===!1?(this.offsetContentElmScrollbars(),this.update(),this.containerElm.on("wheel."+this.options.eventNamespace,this.onScrollWheel),this.options.preventOuterScroll===!0,t(window).on("resize."+this.options.eventNamespace,this.offsetContentElmScrollbars.bind(this,!0))):this.contentElm.addClass(this.options.cssPrefix+"-disabled")}return s.prototype.update=function(){if(this.options.useNativeFloatingScrollbars===!1||this.browserHasFloatingScrollbars===!1){for(var t=!1,e=0,s=this.options.axes;e<s.length;e++){var o=s[e];this.updateAxis(o),0!==this.getValue(this.contentElm,"scrollPos",o)&&(t=!0)}t===!0&&this.contentElm.trigger("scroll."+this.options.eventNamespace)}},s.prototype.addScrollbar=function(e,s){var o,l;return s.append(o=t("<div />").addClass(this.options.cssPrefix+"-scrollbar "+this.options.cssPrefix+"-scrollbar-"+e).append(l=t("<div />").addClass(this.options.cssPrefix+"-track"))),{scrollbar:o,track:l}},s.prototype.preventOuterScroll=function(t,e,s){var o=this.getValue(this.contentElm,"scrollPos",t);switch(e){case"heen":o<=0&&s.preventDefault();break;case"weer":var l=this.getValue(this.contentElm,"scrollSize",t),i=this.getValue(this.contentElm,"clientSize",t);l-o===i&&s.preventDefault()}},s.prototype.hasVisibleFloatingScrollbar=function(){return null===window.navigator.userAgent.match(/AppleWebKit/i)},s.prototype.updateAxis=function(t){var e=this.hasScroll(t);if(e===!0&&null===this.scrollbarElms[t]){this.containerElm.addClass(this.options.cssPrefix+"-axis-"+t);var s=this.addScrollbar(t,this.containerElm),o=s.scrollbar,l=s.track;o.on("mousedown."+t+"."+this.options.eventNamespace,this.onScrollbarMousedown.bind(this,t,o,l)),this.contentElm.on("scroll."+t+"."+this.options.eventNamespace,this.onScroll.bind(this,t,o,l)),this.scrollbarElms[t]=s}else e===!1&&null!==this.scrollbarElms[t]&&(this.containerElm.removeClass(this.options.cssPrefix+"-axis-"+t),this.scrollbarElms[t].scrollbar.remove(),this.scrollbarElms[t]=null,this.contentElm.off("."+t+"."+this.options.eventNamespace));null!==this.scrollbarElms[t]&&this.resizeTrack(t,this.scrollbarElms[t].scrollbar,this.scrollbarElms[t].track)},s.prototype.getValue=function(t,e,s){switch(e){case"size":return t["y"===s?"outerHeight":"outerWidth"]();case"clientSize":return t[0]["y"===s?"clientHeight":"clientWidth"];case"scrollSize":return t[0]["y"===s?"scrollHeight":"scrollWidth"];case"scrollPos":return t["y"===s?"scrollTop":"scrollLeft"]()}},s.prototype.hasScroll=function(t){var e=Math.round(this.getValue(this.contentElm,"size",t)),s=Math.round(this.getValue(this.contentElm,"scrollSize",t));return e<s},s.prototype.resizeTrack=function(t,e,s){var o=this.getValue(this.contentElm,"size",t),l=this.getValue(this.contentElm,"scrollSize",t);if(o<l){e.removeClass("hidden");var i=this.getValue(e,"size",t);s.css("y"===t?"height":"width",i*(o/l))}else e.addClass("hidden")},s.prototype.positionTrack=function(t,e,s){var o=this.getValue(this.contentElm,"scrollPos",t)/(this.getValue(this.contentElm,"scrollSize",t)-this.getValue(this.contentElm,"size",t)),l=this.getValue(s,"size",t),i=this.getValue(e,"size",t);s.css("y"===t?"top":"left",(i-l)*Math.min(o,1))},s.prototype.onScroll=function(t,e,s,o){this.scrollCache[t]!==(this.scrollCache[t]=this.getValue(this.contentElm,"scrollPos",t))&&this.positionTrack(t,e,s)},s.prototype.trackMousedown=function(e,s,o){var l=this;o.preventDefault();var i={startPos:o["y"===e?"pageY":"pageX"],startScroll:this.contentElm["y"===e?"scrollTop":"scrollLeft"](),scrollFactor:this.getValue(this.contentElm,"scrollSize",e)/this.getValue(s,"size",e)},n=t(window),r=this.onTrackDrag.bind(this,e,i);this.containerElm.addClass(this.options.cssPrefix+"-trackdrag-"+e),n.on("mousemove."+this.options.eventNamespace,r).one("mouseup."+this.options.eventNamespace,function(){n.off("mousemove",r),l.containerElm.removeClass(l.options.cssPrefix+"-trackdrag-"+e)})},s.prototype.onTrackDrag=function(t,e,s){s.preventDefault(),this.contentElm["y"===t?"scrollTop":"scrollLeft"](e.startScroll+(s["y"===t?"pageY":"pageX"]-e.startPos)*e.scrollFactor)},s.prototype.scrollToClickedPosition=function(e,s){s.preventDefault();var o=s["y"===e?"offsetY":"offsetX"];o<=10&&(o=0),this.contentElm["y"===e?"scrollTop":"scrollLeft"](this.getValue(this.contentElm,"scrollSize",e)*(o/this.getValue(t(s.target),"size",e)))},s.prototype.destroy=function(){this.contentElm.off("."+this.options.eventNamespace),t(window).off("."+this.options.eventNamespace);for(var e in this.scrollbarElms)this.scrollbarElms[e]&&this.scrollbarElms[e].scrollbar instanceof t==!0&&(this.scrollbarElms[e].scrollbar.remove(),this.scrollbarElms[e]=null);this.contentElm.css(this.originalCssValues)},s.NAME="scrollert",s.eventNamespaceId=0,s}();e.Plugin=s}(e||(e={})),t.fn[e.Plugin.NAME]=function(){for(var s=[],o=0;o<arguments.length;o++)s[o-0]=arguments[o];var l="string"==typeof s[0]?s[0]:"init",i="object"==typeof s[1]?s[1]:"object"==typeof s[0]?s[0]:{};return this.each(function(){var s=t(this),o="plugin-"+e.Plugin.NAME,n=s.data(o);if("init"===l&&n instanceof e.Plugin==!1)s.data(o,n=new e.Plugin(t(this),i));else if(n instanceof e.Plugin==!1)throw new TypeError("The Scrollert plugin is not yet initialized");switch(l){case"init":return;case"update":n.update();break;case"destroy":n.destroy(),s.removeData(o);break;default:throw new TypeError("Invalid Scrollert action "+l)}})},t});