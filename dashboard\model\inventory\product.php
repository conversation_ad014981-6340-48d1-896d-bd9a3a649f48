<?php
/**
 * نموذج إدارة المنتجات المتقدمة في المخزون (Advanced Product Management Model)
 *
 * الهدف: توفير إدارة منتجات متفوقة على Odoo وWooCommerce وSAP
 * الميزات: WAC متطور، تسعير 5 مستويات، خيارات مرتبطة بالوحدات، باركود متقدم
 * التكامل: مع المحاسبة والمشتريات والمبيعات والمخزون
 *
 * <AUTHOR> Team
 * @version 1.0
 * @since 2024
 */

class ModelInventoryProduct extends Model {

    /**
     * إضافة منتج جديد
     */
    public function addProduct($data) {
        // إدراج المنتج الأساسي
        $this->db->query("
            INSERT INTO " . DB_PREFIX . "product SET
            model = '" . $this->db->escape($data['model']) . "',
            sku = '" . $this->db->escape($data['sku']) . "',
            upc = '" . $this->db->escape($data['upc']) . "',
            ean = '" . $this->db->escape($data['ean']) . "',
            jan = '" . $this->db->escape($data['jan']) . "',
            isbn = '" . $this->db->escape($data['isbn']) . "',
            mpn = '" . $this->db->escape($data['mpn']) . "',
            location = '" . $this->db->escape($data['location']) . "',
            quantity = '" . (int)$data['quantity'] . "',
            minimum = '" . (int)$data['minimum'] . "',
            subtract = '" . (int)$data['subtract'] . "',
            stock_status_id = '" . (int)$data['stock_status_id'] . "',
            date_available = '" . $this->db->escape($data['date_available']) . "',
            manufacturer_id = '" . (int)$data['manufacturer_id'] . "',
            shipping = '" . (int)$data['shipping'] . "',
            price = '" . (float)$data['price'] . "',
            points = '" . (int)$data['points'] . "',
            tax_class_id = '" . (int)$data['tax_class_id'] . "',
            weight = '" . (float)$data['weight'] . "',
            weight_class_id = '" . (int)$data['weight_class_id'] . "',
            length = '" . (float)$data['length'] . "',
            width = '" . (float)$data['width'] . "',
            height = '" . (float)$data['height'] . "',
            length_class_id = '" . (int)$data['length_class_id'] . "',
            status = '" . (int)$data['status'] . "',
            sort_order = '" . (int)$data['sort_order'] . "',
            date_added = NOW(),
            date_modified = NOW()
        ");

        $product_id = $this->db->getLastId();

        // إدراج أوصاف المنتج
        if (isset($data['product_description'])) {
            foreach ($data['product_description'] as $language_id => $value) {
                $this->db->query("
                    INSERT INTO " . DB_PREFIX . "product_description SET
                    product_id = '" . (int)$product_id . "',
                    language_id = '" . (int)$language_id . "',
                    name = '" . $this->db->escape($value['name']) . "',
                    description = '" . $this->db->escape($value['description']) . "',
                    tag = '" . $this->db->escape($value['tag']) . "',
                    meta_title = '" . $this->db->escape($value['meta_title']) . "',
                    meta_description = '" . $this->db->escape($value['meta_description']) . "',
                    meta_keyword = '" . $this->db->escape($value['meta_keyword']) . "'
                ");
            }
        }

        // إدراج تصنيفات المنتج
        if (isset($data['product_category'])) {
            foreach ($data['product_category'] as $category_id) {
                $this->db->query("
                    INSERT INTO " . DB_PREFIX . "product_to_category SET
                    product_id = '" . (int)$product_id . "',
                    category_id = '" . (int)$category_id . "'
                ");
            }
        }

        // إدراج وحدات المنتج المتطورة
        if (isset($data['product_units'])) {
            foreach ($data['product_units'] as $unit_data) {
                $this->addProductUnit($product_id, $unit_data);
            }
        }

        // إدراج التسعير المتطور (5 مستويات)
        if (isset($data['product_pricing'])) {
            foreach ($data['product_pricing'] as $pricing_data) {
                $this->addProductPricing($product_id, $pricing_data);
            }
        }

        // إدراج الباركود المتقدم
        if (isset($data['product_barcodes'])) {
            foreach ($data['product_barcodes'] as $barcode_data) {
                $this->addProductBarcode($product_id, $barcode_data);
            }
        }

        // إدراج خيارات المنتج المرتبطة بالوحدات
        if (isset($data['product_options'])) {
            foreach ($data['product_options'] as $option_data) {
                $this->addProductOption($product_id, $option_data);
            }
        }

        // إدراج الباقات والخصومات
        if (isset($data['product_bundles'])) {
            foreach ($data['product_bundles'] as $bundle_data) {
                $this->addProductBundle($product_id, $bundle_data);
            }
        }

        if (isset($data['product_discounts'])) {
            foreach ($data['product_discounts'] as $discount_data) {
                $this->addProductDiscount($product_id, $discount_data);
            }
        }

        // تحديث المخزون في الفروع
        $this->updateProductInventory($product_id, $data);

        // إنشاء قيد محاسبي للمخزون الأولي (إذا كان هناك كمية)
        if (isset($data['quantity']) && $data['quantity'] > 0 && isset($data['price']) && $data['price'] > 0) {
            $this->createInitialInventoryEntry($product_id, $data);
        }

        return $product_id;
    }

    /**
     * تعديل منتج موجود
     */
    public function editProduct($product_id, $data) {
        // تحديث المنتج الأساسي
        $this->db->query("
            UPDATE " . DB_PREFIX . "product SET
            model = '" . $this->db->escape($data['model']) . "',
            sku = '" . $this->db->escape($data['sku']) . "',
            upc = '" . $this->db->escape($data['upc']) . "',
            ean = '" . $this->db->escape($data['ean']) . "',
            jan = '" . $this->db->escape($data['jan']) . "',
            isbn = '" . $this->db->escape($data['isbn']) . "',
            mpn = '" . $this->db->escape($data['mpn']) . "',
            location = '" . $this->db->escape($data['location']) . "',
            minimum = '" . (int)$data['minimum'] . "',
            subtract = '" . (int)$data['subtract'] . "',
            stock_status_id = '" . (int)$data['stock_status_id'] . "',
            date_available = '" . $this->db->escape($data['date_available']) . "',
            manufacturer_id = '" . (int)$data['manufacturer_id'] . "',
            shipping = '" . (int)$data['shipping'] . "',
            points = '" . (int)$data['points'] . "',
            tax_class_id = '" . (int)$data['tax_class_id'] . "',
            weight = '" . (float)$data['weight'] . "',
            weight_class_id = '" . (int)$data['weight_class_id'] . "',
            length = '" . (float)$data['length'] . "',
            width = '" . (float)$data['width'] . "',
            height = '" . (float)$data['height'] . "',
            length_class_id = '" . (int)$data['length_class_id'] . "',
            status = '" . (int)$data['status'] . "',
            sort_order = '" . (int)$data['sort_order'] . "',
            date_modified = NOW()
            WHERE product_id = '" . (int)$product_id . "'
        ");

        // حذف وإعادة إدراج الأوصاف
        $this->db->query("DELETE FROM " . DB_PREFIX . "product_description WHERE product_id = '" . (int)$product_id . "'");

        if (isset($data['product_description'])) {
            foreach ($data['product_description'] as $language_id => $value) {
                $this->db->query("
                    INSERT INTO " . DB_PREFIX . "product_description SET
                    product_id = '" . (int)$product_id . "',
                    language_id = '" . (int)$language_id . "',
                    name = '" . $this->db->escape($value['name']) . "',
                    description = '" . $this->db->escape($value['description']) . "',
                    tag = '" . $this->db->escape($value['tag']) . "',
                    meta_title = '" . $this->db->escape($value['meta_title']) . "',
                    meta_description = '" . $this->db->escape($value['meta_description']) . "',
                    meta_keyword = '" . $this->db->escape($value['meta_keyword']) . "'
                ");
            }
        }

        // تحديث التصنيفات
        $this->db->query("DELETE FROM " . DB_PREFIX . "product_to_category WHERE product_id = '" . (int)$product_id . "'");

        if (isset($data['product_category'])) {
            foreach ($data['product_category'] as $category_id) {
                $this->db->query("
                    INSERT INTO " . DB_PREFIX . "product_to_category SET
                    product_id = '" . (int)$product_id . "',
                    category_id = '" . (int)$category_id . "'
                ");
            }
        }

        // تحديث الوحدات
        $this->db->query("DELETE FROM " . DB_PREFIX . "product_unit WHERE product_id = '" . (int)$product_id . "'");

        if (isset($data['product_units'])) {
            foreach ($data['product_units'] as $unit_data) {
                $this->addProductUnit($product_id, $unit_data);
            }
        }

        // تحديث التسعير
        $this->db->query("DELETE FROM " . DB_PREFIX . "product_pricing WHERE product_id = '" . (int)$product_id . "'");

        if (isset($data['product_pricing'])) {
            foreach ($data['product_pricing'] as $pricing_data) {
                $this->addProductPricing($product_id, $pricing_data);
            }
        }

        // تحديث الباركود
        $this->db->query("DELETE FROM " . DB_PREFIX . "product_barcode WHERE product_id = '" . (int)$product_id . "'");

        if (isset($data['product_barcodes'])) {
            foreach ($data['product_barcodes'] as $barcode_data) {
                $this->addProductBarcode($product_id, $barcode_data);
            }
        }

        // تحديث الخيارات
        $this->db->query("DELETE FROM " . DB_PREFIX . "product_option WHERE product_id = '" . (int)$product_id . "'");
        $this->db->query("DELETE FROM " . DB_PREFIX . "product_option_value WHERE product_option_id IN (SELECT product_option_id FROM " . DB_PREFIX . "product_option WHERE product_id = '" . (int)$product_id . "')");

        if (isset($data['product_options'])) {
            foreach ($data['product_options'] as $option_data) {
                $this->addProductOption($product_id, $option_data);
            }
        }

        // تحديث الباقات والخصومات
        $this->db->query("DELETE FROM " . DB_PREFIX . "product_bundle WHERE product_id = '" . (int)$product_id . "'");
        $this->db->query("DELETE FROM " . DB_PREFIX . "product_discount WHERE product_id = '" . (int)$product_id . "'");

        if (isset($data['product_bundles'])) {
            foreach ($data['product_bundles'] as $bundle_data) {
                $this->addProductBundle($product_id, $bundle_data);
            }
        }

        if (isset($data['product_discounts'])) {
            foreach ($data['product_discounts'] as $discount_data) {
                $this->addProductDiscount($product_id, $discount_data);
            }
        }
    }

    /**
     * حذف منتج
     */
    public function deleteProduct($product_id) {
        // حذف جميع البيانات المرتبطة
        $this->db->query("DELETE FROM " . DB_PREFIX . "product WHERE product_id = '" . (int)$product_id . "'");
        $this->db->query("DELETE FROM " . DB_PREFIX . "product_description WHERE product_id = '" . (int)$product_id . "'");
        $this->db->query("DELETE FROM " . DB_PREFIX . "product_to_category WHERE product_id = '" . (int)$product_id . "'");
        $this->db->query("DELETE FROM " . DB_PREFIX . "product_unit WHERE product_id = '" . (int)$product_id . "'");
        $this->db->query("DELETE FROM " . DB_PREFIX . "product_pricing WHERE product_id = '" . (int)$product_id . "'");
        $this->db->query("DELETE FROM " . DB_PREFIX . "product_barcode WHERE product_id = '" . (int)$product_id . "'");
        $this->db->query("DELETE FROM " . DB_PREFIX . "product_option WHERE product_id = '" . (int)$product_id . "'");
        $this->db->query("DELETE FROM " . DB_PREFIX . "product_bundle WHERE product_id = '" . (int)$product_id . "'");
        $this->db->query("DELETE FROM " . DB_PREFIX . "product_discount WHERE product_id = '" . (int)$product_id . "'");
        $this->db->query("DELETE FROM " . DB_PREFIX . "product_inventory WHERE product_id = '" . (int)$product_id . "'");
        $this->db->query("DELETE FROM " . DB_PREFIX . "product_movement WHERE product_id = '" . (int)$product_id . "'");
    }

    /**
     * الحصول على منتج واحد
     */
    public function getProduct($product_id) {
        $query = $this->db->query("
            SELECT DISTINCT *,
            (SELECT keyword FROM " . DB_PREFIX . "seo_url WHERE query = 'product_id=" . (int)$product_id . "' AND store_id = '0' AND language_id = '" . (int)$this->config->get('config_language_id') . "' LIMIT 1) AS keyword
            FROM " . DB_PREFIX . "product p
            LEFT JOIN " . DB_PREFIX . "product_description pd ON (p.product_id = pd.product_id)
            WHERE p.product_id = '" . (int)$product_id . "'
            AND pd.language_id = '" . (int)$this->config->get('config_language_id') . "'
        ");

        return $query->row;
    }

    /**
     * الحصول على قائمة المنتجات مع الفلاتر
     */
    public function getProducts($data = array()) {
        $sql = "
            SELECT p.product_id,
            (SELECT pd.name FROM " . DB_PREFIX . "product_description pd WHERE pd.product_id = p.product_id AND pd.language_id = '" . (int)$this->config->get('config_language_id') . "' LIMIT 1) AS name,
            p.model, p.sku, p.upc, p.ean, p.image, p.price, p.quantity, p.status, p.sort_order,
            (SELECT SUM(pi.quantity) FROM " . DB_PREFIX . "product_inventory pi WHERE pi.product_id = p.product_id) AS total_quantity,
            p.average_cost, p.date_added, p.date_modified
            FROM " . DB_PREFIX . "product p
        ";

        $where = array();

        if (!empty($data['filter_name'])) {
            $where[] = "(SELECT pd.name FROM " . DB_PREFIX . "product_description pd WHERE pd.product_id = p.product_id AND pd.language_id = '" . (int)$this->config->get('config_language_id') . "' LIMIT 1) LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }

        if (!empty($data['filter_model'])) {
            $where[] = "p.model LIKE '%" . $this->db->escape($data['filter_model']) . "%'";
        }

        if (isset($data['filter_status']) && $data['filter_status'] !== '') {
            $where[] = "p.status = '" . (int)$data['filter_status'] . "'";
        }

        if ($where) {
            $sql .= " WHERE " . implode(" AND ", $where);
        }

        $sort_data = array(
            'pd.name',
            'p.model',
            'p.price',
            'p.quantity',
            'p.status',
            'p.sort_order'
        );

        if (isset($data['sort']) && in_array($data['sort'], $sort_data)) {
            $sql .= " ORDER BY " . $data['sort'];
        } else {
            $sql .= " ORDER BY pd.name";
        }

        if (isset($data['order']) && ($data['order'] == 'DESC')) {
            $sql .= " DESC";
        } else {
            $sql .= " ASC";
        }

        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }

        $query = $this->db->query($sql);

        return $query->rows;
    }

    /**
     * الحصول على إجمالي عدد المنتجات
     */
    public function getTotalProducts($data = array()) {
        $sql = "SELECT COUNT(DISTINCT p.product_id) AS total FROM " . DB_PREFIX . "product p";

        $where = array();

        if (!empty($data['filter_name'])) {
            $where[] = "(SELECT pd.name FROM " . DB_PREFIX . "product_description pd WHERE pd.product_id = p.product_id AND pd.language_id = '" . (int)$this->config->get('config_language_id') . "' LIMIT 1) LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }

        if (!empty($data['filter_model'])) {
            $where[] = "p.model LIKE '%" . $this->db->escape($data['filter_model']) . "%'";
        }

        if (isset($data['filter_status']) && $data['filter_status'] !== '') {
            $where[] = "p.status = '" . (int)$data['filter_status'] . "'";
        }

        if ($where) {
            $sql .= " WHERE " . implode(" AND ", $where);
        }

        $query = $this->db->query($sql);

        return $query->row['total'];
    }

    /**
     * إضافة وحدة للمنتج
     */
    public function addProductUnit($product_id, $data) {
        $this->db->query("
            INSERT INTO " . DB_PREFIX . "product_unit SET
            product_id = '" . (int)$product_id . "',
            unit_id = '" . (int)$data['unit_id'] . "',
            unit_type = '" . $this->db->escape($data['unit_type']) . "',
            conversion_factor = '" . (float)$data['conversion_factor'] . "',
            is_base_unit = '" . (int)$data['is_base_unit'] . "',
            sort_order = '" . (int)$data['sort_order'] . "'
        ");
    }

    /**
     * إضافة تسعير للمنتج (5 مستويات)
     */
    public function addProductPricing($product_id, $data) {
        $this->db->query("
            INSERT INTO " . DB_PREFIX . "product_pricing SET
            product_id = '" . (int)$product_id . "',
            unit_id = '" . (int)$data['unit_id'] . "',
            cost_price = '" . (float)$data['cost_price'] . "',
            base_price = '" . (float)$data['base_price'] . "',
            special_price = '" . (float)$data['special_price'] . "',
            wholesale_price = '" . (float)$data['wholesale_price'] . "',
            half_wholesale_price = '" . (float)$data['half_wholesale_price'] . "',
            custom_price = '" . (float)$data['custom_price'] . "',
            profit_margin = '" . (float)$data['profit_margin'] . "',
            date_start = '" . $this->db->escape($data['date_start']) . "',
            date_end = '" . $this->db->escape($data['date_end']) . "'
        ");
    }

    /**
     * إضافة باركود للمنتج
     */
    public function addProductBarcode($product_id, $data) {
        $this->db->query("
            INSERT INTO " . DB_PREFIX . "product_barcode SET
            product_id = '" . (int)$product_id . "',
            barcode = '" . $this->db->escape($data['barcode']) . "',
            barcode_type = '" . $this->db->escape($data['barcode_type']) . "',
            unit_id = '" . (int)$data['unit_id'] . "',
            option_id = '" . (int)$data['option_id'] . "',
            option_value_id = '" . (int)$data['option_value_id'] . "',
            is_primary = '" . (int)$data['is_primary'] . "'
        ");
    }

    /**
     * إضافة خيار للمنتج (مرتبط بالوحدات)
     */
    public function addProductOption($product_id, $data) {
        $this->db->query("
            INSERT INTO " . DB_PREFIX . "product_option SET
            product_id = '" . (int)$product_id . "',
            option_id = '" . (int)$data['option_id'] . "',
            unit_id = '" . (int)$data['unit_id'] . "',
            value = '" . $this->db->escape($data['value']) . "',
            required = '" . (int)$data['required'] . "'
        ");

        $product_option_id = $this->db->getLastId();

        if (isset($data['product_option_value'])) {
            foreach ($data['product_option_value'] as $option_value) {
                $this->db->query("
                    INSERT INTO " . DB_PREFIX . "product_option_value SET
                    product_option_id = '" . (int)$product_option_id . "',
                    product_id = '" . (int)$product_id . "',
                    option_id = '" . (int)$data['option_id'] . "',
                    option_value_id = '" . (int)$option_value['option_value_id'] . "',
                    quantity = '" . (int)$option_value['quantity'] . "',
                    subtract = '" . (int)$option_value['subtract'] . "',
                    price = '" . (float)$option_value['price'] . "',
                    price_prefix = '" . $this->db->escape($option_value['price_prefix']) . "',
                    points = '" . (int)$option_value['points'] . "',
                    points_prefix = '" . $this->db->escape($option_value['points_prefix']) . "',
                    weight = '" . (float)$option_value['weight'] . "',
                    weight_prefix = '" . $this->db->escape($option_value['weight_prefix']) . "'
                ");
            }
        }
    }

    /**
     * إضافة باقة للمنتج (النسخة القديمة)
     */
    public function addProductBundleOld($product_id, $data) {
        $this->db->query("
            INSERT INTO " . DB_PREFIX . "product_bundle SET
            product_id = '" . (int)$product_id . "',
            name = '" . $this->db->escape($data['name']) . "',
            discount_type = '" . $this->db->escape($data['discount_type']) . "',
            discount_value = '" . (float)$data['discount_value'] . "',
            status = '" . (int)$data['status'] . "',
            date_start = '" . $this->db->escape($data['date_start']) . "',
            date_end = '" . $this->db->escape($data['date_end']) . "'
        ");

        $bundle_id = $this->db->getLastId();

        if (isset($data['bundle_items'])) {
            foreach ($data['bundle_items'] as $item) {
                $this->db->query("
                    INSERT INTO " . DB_PREFIX . "product_bundle_item SET
                    bundle_id = '" . (int)$bundle_id . "',
                    product_id = '" . (int)$item['product_id'] . "',
                    quantity = '" . (int)$item['quantity'] . "',
                    unit_id = '" . (int)$item['unit_id'] . "',
                    is_free = '" . (int)$item['is_free'] . "'
                ");
            }
        }
    }

    /**
     * إضافة خصم للمنتج
     */
    public function addProductDiscount($product_id, $data) {
        $this->db->query("
            INSERT INTO " . DB_PREFIX . "product_discount SET
            product_id = '" . (int)$product_id . "',
            name = '" . $this->db->escape($data['name']) . "',
            type = '" . $this->db->escape($data['type']) . "',
            buy_quantity = '" . (int)$data['buy_quantity'] . "',
            get_quantity = '" . (int)$data['get_quantity'] . "',
            discount_type = '" . $this->db->escape($data['discount_type']) . "',
            discount_value = '" . (float)$data['discount_value'] . "',
            unit_id = '" . (int)$data['unit_id'] . "',
            status = '" . (int)$data['status'] . "',
            date_start = '" . $this->db->escape($data['date_start']) . "',
            date_end = '" . $this->db->escape($data['date_end']) . "',
            notes = '" . $this->db->escape($data['notes']) . "'
        ");
    }

    /**
     * تحديث مخزون المنتج في الفروع
     */
    public function updateProductInventory($product_id, $data) {
        if (isset($data['branch_inventory'])) {
            foreach ($data['branch_inventory'] as $branch_id => $inventory_data) {
                $this->db->query("
                    INSERT INTO " . DB_PREFIX . "product_inventory SET
                    product_id = '" . (int)$product_id . "',
                    branch_id = '" . (int)$branch_id . "',
                    quantity = '" . (float)$inventory_data['quantity'] . "',
                    reserved_quantity = '0',
                    location = '" . $this->db->escape($inventory_data['location']) . "',
                    last_updated = NOW()
                    ON DUPLICATE KEY UPDATE
                    quantity = '" . (float)$inventory_data['quantity'] . "',
                    location = '" . $this->db->escape($inventory_data['location']) . "',
                    last_updated = NOW()
                ");
            }
        }
    }

    /**
     * إنشاء قيد محاسبي للمخزون الأولي
     */
    public function createInitialInventoryEntry($product_id, $data) {
        $this->load->model('accounting/journal');

        $total_value = (float)$data['quantity'] * (float)$data['price'];

        $journal_data = array(
            'reference' => 'INV-INIT-' . $product_id,
            'description' => 'مخزون أولي للمنتج: ' . $data['product_description'][1]['name'],
            'date' => date('Y-m-d'),
            'entries' => array(
                array(
                    'account_id' => $this->config->get('inventory_asset_account'),
                    'debit' => $total_value,
                    'credit' => 0,
                    'description' => 'مخزون أولي'
                ),
                array(
                    'account_id' => $this->config->get('inventory_equity_account'),
                    'debit' => 0,
                    'credit' => $total_value,
                    'description' => 'رأس مال مخزون أولي'
                )
            )
        );

        $this->model_accounting_journal->addEntry($journal_data);
    }

    /**
     * توليد كود المنتج التلقائي
     */
    public function generateProductCode($category_id = 0, $manufacturer_id = 0) {
        $code = '';

        // بادئة القسم
        if ($category_id > 0) {
            $category_query = $this->db->query("
                SELECT code_prefix FROM " . DB_PREFIX . "category
                WHERE category_id = '" . (int)$category_id . "'
            ");

            if ($category_query->num_rows) {
                $code .= $category_query->row['code_prefix'];
            } else {
                $code .= 'CAT';
            }
        } else {
            $code .= 'PRD';
        }

        // بادئة العلامة التجارية
        if ($manufacturer_id > 0) {
            $manufacturer_query = $this->db->query("
                SELECT code_prefix FROM " . DB_PREFIX . "manufacturer
                WHERE manufacturer_id = '" . (int)$manufacturer_id . "'
            ");

            if ($manufacturer_query->num_rows) {
                $code .= '-' . $manufacturer_query->row['code_prefix'];
            }
        }

        // الرقم التسلسلي
        $sequence_query = $this->db->query("
            SELECT MAX(CAST(SUBSTRING(model, LENGTH('" . $this->db->escape($code) . "') + 2) AS UNSIGNED)) as max_sequence
            FROM " . DB_PREFIX . "product
            WHERE model LIKE '" . $this->db->escape($code) . "-%'
        ");

        $next_sequence = 1;
        if ($sequence_query->num_rows && $sequence_query->row['max_sequence']) {
            $next_sequence = (int)$sequence_query->row['max_sequence'] + 1;
        }

        $code .= '-' . str_pad($next_sequence, 4, '0', STR_PAD_LEFT);

        return $code;
    }

    /**
     * الحصول على أوصاف المنتج
     */
    public function getProductDescriptions($product_id) {
        $product_description_data = array();

        $query = $this->db->query("
            SELECT * FROM " . DB_PREFIX . "product_description
            WHERE product_id = '" . (int)$product_id . "'
        ");

        foreach ($query->rows as $result) {
            $product_description_data[$result['language_id']] = array(
                'name'             => $result['name'],
                'description'      => $result['description'],
                'tag'              => $result['tag'],
                'meta_title'       => $result['meta_title'],
                'meta_description' => $result['meta_description'],
                'meta_keyword'     => $result['meta_keyword']
            );
        }

        return $product_description_data;
    }

    /**
     * الحصول على تصنيفات المنتج
     */
    public function getProductCategories($product_id) {
        $product_category_data = array();

        $query = $this->db->query("
            SELECT * FROM " . DB_PREFIX . "product_to_category
            WHERE product_id = '" . (int)$product_id . "'
        ");

        foreach ($query->rows as $result) {
            $product_category_data[] = $result['category_id'];
        }

        return $product_category_data;
    }

    /**
     * الحصول على وحدات المنتج
     */
    public function getProductUnits($product_id) {
        $query = $this->db->query("
            SELECT pu.*, u.name as unit_name
            FROM " . DB_PREFIX . "product_unit pu
            LEFT JOIN " . DB_PREFIX . "unit u ON (pu.unit_id = u.unit_id)
            WHERE pu.product_id = '" . (int)$product_id . "'
            ORDER BY pu.sort_order ASC
        ");

        return $query->rows;
    }

    /**
     * الحصول على تسعير المنتج
     */
    public function getProductPricing($product_id) {
        $query = $this->db->query("
            SELECT pp.*, u.name as unit_name
            FROM " . DB_PREFIX . "product_pricing pp
            LEFT JOIN " . DB_PREFIX . "unit u ON (pp.unit_id = u.unit_id)
            WHERE pp.product_id = '" . (int)$product_id . "'
        ");

        return $query->rows;
    }

    /**
     * الحصول على باركود المنتج
     */
    public function getProductBarcodes($product_id) {
        $query = $this->db->query("
            SELECT pb.*, u.name as unit_name, o.name as option_name, ov.name as option_value_name
            FROM " . DB_PREFIX . "product_barcode pb
            LEFT JOIN " . DB_PREFIX . "unit u ON (pb.unit_id = u.unit_id)
            LEFT JOIN " . DB_PREFIX . "option o ON (pb.option_id = o.option_id)
            LEFT JOIN " . DB_PREFIX . "option_value ov ON (pb.option_value_id = ov.option_value_id)
            WHERE pb.product_id = '" . (int)$product_id . "'
        ");

        return $query->rows;
    }

    /**
     * الحصول على خيارات المنتج
     */
    public function getProductOptions($product_id) {
        $product_option_data = array();

        $product_option_query = $this->db->query("
            SELECT * FROM " . DB_PREFIX . "product_option po
            LEFT JOIN `" . DB_PREFIX . "option` o ON (po.option_id = o.option_id)
            LEFT JOIN " . DB_PREFIX . "option_description od ON (o.option_id = od.option_id)
            WHERE po.product_id = '" . (int)$product_id . "'
            AND od.language_id = '" . (int)$this->config->get('config_language_id') . "'
        ");

        foreach ($product_option_query->rows as $product_option) {
            $product_option_value_data = array();

            $product_option_value_query = $this->db->query("
                SELECT * FROM " . DB_PREFIX . "product_option_value pov
                LEFT JOIN " . DB_PREFIX . "option_value ov ON (pov.option_value_id = ov.option_value_id)
                LEFT JOIN " . DB_PREFIX . "option_value_description ovd ON (ov.option_value_id = ovd.option_value_id)
                WHERE pov.product_id = '" . (int)$product_id . "'
                AND pov.product_option_id = '" . (int)$product_option['product_option_id'] . "'
                AND ovd.language_id = '" . (int)$this->config->get('config_language_id') . "'
            ");

            foreach ($product_option_value_query->rows as $product_option_value) {
                $product_option_value_data[] = array(
                    'product_option_value_id' => $product_option_value['product_option_value_id'],
                    'option_value_id'         => $product_option_value['option_value_id'],
                    'name'                    => $product_option_value['name'],
                    'image'                   => $product_option_value['image'],
                    'quantity'                => $product_option_value['quantity'],
                    'subtract'                => $product_option_value['subtract'],
                    'price'                   => $product_option_value['price'],
                    'price_prefix'            => $product_option_value['price_prefix'],
                    'points'                  => $product_option_value['points'],
                    'points_prefix'           => $product_option_value['points_prefix'],
                    'weight'                  => $product_option_value['weight'],
                    'weight_prefix'           => $product_option_value['weight_prefix']
                );
            }

            $product_option_data[] = array(
                'product_option_id'    => $product_option['product_option_id'],
                'product_option_value' => $product_option_value_data,
                'option_id'            => $product_option['option_id'],
                'name'                 => $product_option['name'],
                'type'                 => $product_option['type'],
                'unit_id'              => $product_option['unit_id'],
                'value'                => $product_option['value'],
                'required'             => $product_option['required']
            );
        }

        return $product_option_data;
    }

    /**
     * الحصول على باقات المنتج (النسخة القديمة)
     */
    public function getProductBundlesOld($product_id) {
        $query = $this->db->query("
            SELECT * FROM " . DB_PREFIX . "product_bundle
            WHERE product_id = '" . (int)$product_id . "'
        ");

        $bundles = array();
        foreach ($query->rows as $bundle) {
            $items_query = $this->db->query("
                SELECT pbi.*, pd.name as product_name, u.name as unit_name
                FROM " . DB_PREFIX . "product_bundle_item pbi
                LEFT JOIN " . DB_PREFIX . "product_description pd ON (pbi.product_id = pd.product_id)
                LEFT JOIN " . DB_PREFIX . "unit u ON (pbi.unit_id = u.unit_id)
                WHERE pbi.bundle_id = '" . (int)$bundle['bundle_id'] . "'
                AND pd.language_id = '" . (int)$this->config->get('config_language_id') . "'
            ");

            $bundle['items'] = $items_query->rows;
            $bundles[] = $bundle;
        }

        return $bundles;
    }

    /**
     * الحصول على خصومات المنتج
     */
    public function getProductDiscounts($product_id) {
        $query = $this->db->query("
            SELECT pd.*, u.name as unit_name
            FROM " . DB_PREFIX . "product_discount pd
            LEFT JOIN " . DB_PREFIX . "unit u ON (pd.unit_id = u.unit_id)
            WHERE pd.product_id = '" . (int)$product_id . "'
        ");

        return $query->rows;
    }

    /**
     * إدارة المقاسات - إضافة مقاس جديد
     */
    public function addProductSize($data) {
        $this->db->query("
            INSERT INTO " . DB_PREFIX . "product_size SET
            name = '" . $this->db->escape($data['name']) . "',
            code = '" . $this->db->escape($data['code']) . "',
            sort_order = '" . (int)$data['sort_order'] . "',
            status = '" . (int)$data['status'] . "',
            date_added = NOW()
        ");

        return $this->db->getLastId();
    }

    /**
     * تعديل مقاس
     */
    public function editProductSize($size_id, $data) {
        $this->db->query("
            UPDATE " . DB_PREFIX . "product_size SET
            name = '" . $this->db->escape($data['name']) . "',
            code = '" . $this->db->escape($data['code']) . "',
            sort_order = '" . (int)$data['sort_order'] . "',
            status = '" . (int)$data['status'] . "',
            date_modified = NOW()
            WHERE size_id = '" . (int)$size_id . "'
        ");

        return $this->db->countAffected() > 0;
    }

    /**
     * حذف مقاس
     */
    public function deleteProductSize($size_id) {
        $this->db->query("DELETE FROM " . DB_PREFIX . "product_size WHERE size_id = '" . (int)$size_id . "'");
        return $this->db->countAffected() > 0;
    }

    /**
     * الحصول على مقاس واحد
     */
    public function getProductSize($size_id) {
        $query = $this->db->query("
            SELECT * FROM " . DB_PREFIX . "product_size
            WHERE size_id = '" . (int)$size_id . "'
        ");

        return $query->row;
    }

    /**
     * الحصول على جميع المقاسات
     */
    public function getProductSizes() {
        $query = $this->db->query("
            SELECT * FROM " . DB_PREFIX . "product_size
            WHERE status = '1'
            ORDER BY sort_order ASC, name ASC
        ");

        return $query->rows;
    }

    /**
     * عدد المنتجات التي تستخدم مقاس معين
     */
    public function getTotalProductsBySize($size_id) {
        $query = $this->db->query("
            SELECT COUNT(*) as total FROM " . DB_PREFIX . "product_variant
            WHERE size_id = '" . (int)$size_id . "'
        ");

        return $query->row['total'];
    }

    /**
     * إدارة الألوان - إضافة لون جديد
     */
    public function addProductColor($data) {
        $this->db->query("
            INSERT INTO " . DB_PREFIX . "product_color SET
            name = '" . $this->db->escape($data['name']) . "',
            code = '" . $this->db->escape($data['code']) . "',
            hex_value = '" . $this->db->escape($data['hex_value']) . "',
            sort_order = '" . (int)$data['sort_order'] . "',
            status = '" . (int)$data['status'] . "',
            date_added = NOW()
        ");

        return $this->db->getLastId();
    }

    /**
     * تعديل لون
     */
    public function editProductColor($color_id, $data) {
        $this->db->query("
            UPDATE " . DB_PREFIX . "product_color SET
            name = '" . $this->db->escape($data['name']) . "',
            code = '" . $this->db->escape($data['code']) . "',
            hex_value = '" . $this->db->escape($data['hex_value']) . "',
            sort_order = '" . (int)$data['sort_order'] . "',
            status = '" . (int)$data['status'] . "',
            date_modified = NOW()
            WHERE color_id = '" . (int)$color_id . "'
        ");

        return $this->db->countAffected() > 0;
    }

    /**
     * حذف لون
     */
    public function deleteProductColor($color_id) {
        $this->db->query("DELETE FROM " . DB_PREFIX . "product_color WHERE color_id = '" . (int)$color_id . "'");
        return $this->db->countAffected() > 0;
    }

    /**
     * الحصول على لون واحد
     */
    public function getProductColor($color_id) {
        $query = $this->db->query("
            SELECT * FROM " . DB_PREFIX . "product_color
            WHERE color_id = '" . (int)$color_id . "'
        ");

        return $query->row;
    }

    /**
     * الحصول على جميع الألوان
     */
    public function getProductColors() {
        $query = $this->db->query("
            SELECT * FROM " . DB_PREFIX . "product_color
            WHERE status = '1'
            ORDER BY sort_order ASC, name ASC
        ");

        return $query->rows;
    }

    /**
     * عدد المنتجات التي تستخدم لون معين
     */
    public function getTotalProductsByColor($color_id) {
        $query = $this->db->query("
            SELECT COUNT(*) as total FROM " . DB_PREFIX . "product_variant
            WHERE color_id = '" . (int)$color_id . "'
        ");

        return $query->row['total'];
    }

    /**
     * إدارة المتغيرات - إضافة متغير جديد
     */
    public function addProductVariant($data) {
        $this->db->query("
            INSERT INTO " . DB_PREFIX . "product_variant SET
            product_id = '" . (int)$data['product_id'] . "',
            size_id = '" . (int)$data['size_id'] . "',
            color_id = '" . (int)$data['color_id'] . "',
            sku = '" . $this->db->escape($data['sku']) . "',
            price_modifier = '" . (float)$data['price_modifier'] . "',
            quantity = '" . (int)$data['quantity'] . "',
            image = '" . $this->db->escape($data['image']) . "',
            status = '" . (int)$data['status'] . "',
            date_added = NOW()
        ");

        return $this->db->getLastId();
    }

    /**
     * الحصول على متغير محدد
     */
    public function getProductVariant($product_id, $size_id, $color_id) {
        $query = $this->db->query("
            SELECT * FROM " . DB_PREFIX . "product_variant
            WHERE product_id = '" . (int)$product_id . "'
            AND size_id = '" . (int)$size_id . "'
            AND color_id = '" . (int)$color_id . "'
        ");

        return $query->row;
    }

    /**
     * الحصول على جميع متغيرات المنتج
     */
    public function getProductVariants($product_id) {
        $query = $this->db->query("
            SELECT pv.*, ps.name as size_name, pc.name as color_name, pc.hex_value
            FROM " . DB_PREFIX . "product_variant pv
            LEFT JOIN " . DB_PREFIX . "product_size ps ON (pv.size_id = ps.size_id)
            LEFT JOIN " . DB_PREFIX . "product_color pc ON (pv.color_id = pc.color_id)
            WHERE pv.product_id = '" . (int)$product_id . "'
            ORDER BY ps.sort_order ASC, pc.sort_order ASC
        ");

        return $query->rows;
    }

    /**
     * إعادة ترتيب صور المنتج
     */
    public function reorderProductImages($product_id, $image_order) {
        foreach ($image_order as $sort_order => $image_id) {
            $this->db->query("
                UPDATE " . DB_PREFIX . "product_image SET
                sort_order = '" . (int)$sort_order . "'
                WHERE image_id = '" . (int)$image_id . "'
                AND product_id = '" . (int)$product_id . "'
            ");
        }

        return true;
    }

    /**
     * الحصول على صورة منتج
     */
    public function getProductImage($image_id) {
        $query = $this->db->query("
            SELECT * FROM " . DB_PREFIX . "product_image
            WHERE image_id = '" . (int)$image_id . "'
        ");

        return $query->row;
    }

    /**
     * حذف صورة منتج
     */
    public function deleteProductImage($image_id) {
        $this->db->query("DELETE FROM " . DB_PREFIX . "product_image WHERE image_id = '" . (int)$image_id . "'");
        return $this->db->countAffected() > 0;
    }

    /**
     * الحصول على صور المنتج
     */
    public function getProductImages($product_id) {
        $query = $this->db->query("
            SELECT * FROM " . DB_PREFIX . "product_image
            WHERE product_id = '" . (int)$product_id . "'
            ORDER BY sort_order ASC
        ");

        return $query->rows;
    }

    /**
     * إضافة صورة للمنتج
     */
    public function addProductImage($product_id, $image_data) {
        $this->db->query("
            INSERT INTO " . DB_PREFIX . "product_image SET
            product_id = '" . (int)$product_id . "',
            image = '" . $this->db->escape($image_data['image']) . "',
            sort_order = '" . (int)$image_data['sort_order'] . "',
            alt_text = '" . $this->db->escape($image_data['alt_text']) . "'
        ");

        return $this->db->getLastId();
    }

    /**
     * توليد باركود
     */
    public function generateBarcode($type, $product_id = 0) {
        switch ($type) {
            case 'EAN13':
                return $this->generateEAN13($product_id);
            case 'EAN8':
                return $this->generateEAN8($product_id);
            case 'UPC':
                return $this->generateUPC($product_id);
            case 'CODE128':
                return $this->generateCODE128($product_id);
            default:
                return false;
        }
    }

    /**
     * توليد EAN13
     */
    private function generateEAN13($product_id) {
        $country_code = $this->config->get('config_barcode_country_code') ?: '622'; // مصر
        $company_code = $this->config->get('config_barcode_company_code') ?: '12345';
        $product_code = str_pad($product_id, 5, '0', STR_PAD_LEFT);

        $barcode = $country_code . $company_code . $product_code;

        // حساب checksum
        $sum = 0;
        for ($i = 0; $i < 12; $i++) {
            $sum += (int)$barcode[$i] * (($i % 2 == 0) ? 1 : 3);
        }

        $checksum = (10 - ($sum % 10)) % 10;

        return $barcode . $checksum;
    }

    /**
     * توليد EAN8
     */
    private function generateEAN8($product_id) {
        $country_code = substr($this->config->get('config_barcode_country_code') ?: '622', 0, 2);
        $company_code = substr($this->config->get('config_barcode_company_code') ?: '12345', 0, 2);
        $product_code = str_pad($product_id, 3, '0', STR_PAD_LEFT);

        $barcode = $country_code . $company_code . $product_code;

        // حساب checksum
        $sum = 0;
        for ($i = 0; $i < 7; $i++) {
            $sum += (int)$barcode[$i] * (($i % 2 == 0) ? 1 : 3);
        }

        $checksum = (10 - ($sum % 10)) % 10;

        return $barcode . $checksum;
    }

    /**
     * توليد UPC
     */
    private function generateUPC($product_id) {
        $company_code = substr($this->config->get('config_barcode_company_code') ?: '123456', 0, 6);
        $product_code = str_pad($product_id, 5, '0', STR_PAD_LEFT);

        $barcode = $company_code . $product_code;

        // حساب checksum
        $sum = 0;
        for ($i = 0; $i < 11; $i++) {
            $sum += (int)$barcode[$i] * (($i % 2 == 0) ? 3 : 1);
        }

        $checksum = (10 - ($sum % 10)) % 10;

        return $barcode . $checksum;
    }

    /**
     * توليد CODE128
     */
    private function generateCODE128($product_id) {
        $prefix = $this->config->get('config_barcode_prefix') ?: 'PRD';
        return $prefix . str_pad($product_id, 8, '0', STR_PAD_LEFT);
    }

    /**
     * البحث عن باركود
     */
    public function getBarcodeByCode($barcode) {
        $query = $this->db->query("
            SELECT * FROM " . DB_PREFIX . "product_barcode
            WHERE barcode = '" . $this->db->escape($barcode) . "'
        ");

        return $query->row;
    }

    /**
     * الحصول على باركود واحد
     */
    public function getProductBarcode($barcode_id) {
        $query = $this->db->query("
            SELECT pb.*, p.model, pd.name as product_name
            FROM " . DB_PREFIX . "product_barcode pb
            LEFT JOIN " . DB_PREFIX . "product p ON (pb.product_id = p.product_id)
            LEFT JOIN " . DB_PREFIX . "product_description pd ON (p.product_id = pd.product_id)
            WHERE pb.barcode_id = '" . (int)$barcode_id . "'
            AND pd.language_id = '" . (int)$this->config->get('config_language_id') . "'
        ");

        return $query->row;
    }

    /**
     * إدارة الحزم - إضافة حزمة جديدة
     */
    public function addProductBundle($data) {
        $this->db->query("
            INSERT INTO " . DB_PREFIX . "product_bundle SET
            name = '" . $this->db->escape($data['name']) . "',
            description = '" . $this->db->escape($data['description']) . "',
            discount_type = '" . $this->db->escape($data['discount_type']) . "',
            discount_value = '" . (float)$data['discount_value'] . "',
            total_price = '" . (float)$data['total_price'] . "',
            status = '" . (int)$data['status'] . "',
            date_added = NOW()
        ");

        $bundle_id = $this->db->getLastId();

        // إضافة منتجات الحزمة
        if (isset($data['products']) && !empty($data['products'])) {
            foreach ($data['products'] as $product) {
                $this->db->query("
                    INSERT INTO " . DB_PREFIX . "product_bundle_item SET
                    bundle_id = '" . (int)$bundle_id . "',
                    product_id = '" . (int)$product['product_id'] . "',
                    quantity = '" . (int)$product['quantity'] . "'
                ");
            }
        }

        return $bundle_id;
    }

    /**
     * تعديل حزمة
     */
    public function editProductBundle($bundle_id, $data) {
        $this->db->query("
            UPDATE " . DB_PREFIX . "product_bundle SET
            name = '" . $this->db->escape($data['name']) . "',
            description = '" . $this->db->escape($data['description']) . "',
            discount_type = '" . $this->db->escape($data['discount_type']) . "',
            discount_value = '" . (float)$data['discount_value'] . "',
            total_price = '" . (float)$data['total_price'] . "',
            status = '" . (int)$data['status'] . "',
            date_modified = NOW()
            WHERE bundle_id = '" . (int)$bundle_id . "'
        ");

        // حذف وإعادة إضافة منتجات الحزمة
        $this->db->query("DELETE FROM " . DB_PREFIX . "product_bundle_item WHERE bundle_id = '" . (int)$bundle_id . "'");

        if (isset($data['products']) && !empty($data['products'])) {
            foreach ($data['products'] as $product) {
                $this->db->query("
                    INSERT INTO " . DB_PREFIX . "product_bundle_item SET
                    bundle_id = '" . (int)$bundle_id . "',
                    product_id = '" . (int)$product['product_id'] . "',
                    quantity = '" . (int)$product['quantity'] . "'
                ");
            }
        }

        return $this->db->countAffected() > 0;
    }

    /**
     * حذف حزمة
     */
    public function deleteProductBundle($bundle_id) {
        $this->db->query("DELETE FROM " . DB_PREFIX . "product_bundle_item WHERE bundle_id = '" . (int)$bundle_id . "'");
        $this->db->query("DELETE FROM " . DB_PREFIX . "product_bundle WHERE bundle_id = '" . (int)$bundle_id . "'");
        return $this->db->countAffected() > 0;
    }

    /**
     * الحصول على حزمة واحدة
     */
    public function getProductBundle($bundle_id) {
        $query = $this->db->query("
            SELECT * FROM " . DB_PREFIX . "product_bundle
            WHERE bundle_id = '" . (int)$bundle_id . "'
        ");

        if ($query->num_rows) {
            $bundle = $query->row;

            // الحصول على منتجات الحزمة
            $products_query = $this->db->query("
                SELECT pbi.*, p.model, pd.name as product_name, p.price
                FROM " . DB_PREFIX . "product_bundle_item pbi
                LEFT JOIN " . DB_PREFIX . "product p ON (pbi.product_id = p.product_id)
                LEFT JOIN " . DB_PREFIX . "product_description pd ON (p.product_id = pd.product_id)
                WHERE pbi.bundle_id = '" . (int)$bundle_id . "'
                AND pd.language_id = '" . (int)$this->config->get('config_language_id') . "'
            ");

            $bundle['products'] = $products_query->rows;
            return $bundle;
        }

        return array();
    }

    /**
     * الحصول على جميع الحزم
     */
    public function getProductBundles() {
        $query = $this->db->query("
            SELECT * FROM " . DB_PREFIX . "product_bundle
            WHERE status = '1'
            ORDER BY name ASC
        ");

        $bundles = array();
        foreach ($query->rows as $bundle) {
            $bundle_data = $this->getProductBundle($bundle['bundle_id']);
            $bundles[] = $bundle_data;
        }

        return $bundles;
    }

    /**
     * التحقق من توفر مكونات الحزمة
     */
    public function checkBundleAvailability($bundle_id, $quantity = 1) {
        $bundle = $this->getProductBundle($bundle_id);

        if (empty($bundle)) {
            return array('available' => false, 'max_quantity' => 0, 'unavailable_products' => array());
        }

        $max_quantity = PHP_INT_MAX;
        $unavailable_products = array();

        foreach ($bundle['products'] as $product) {
            $product_info = $this->getProduct($product['product_id']);

            if (!$product_info || $product_info['status'] == 0) {
                $unavailable_products[] = $product['product_name'];
                $max_quantity = 0;
                continue;
            }

            $required_quantity = $product['quantity'] * $quantity;
            $available_quantity = $product_info['quantity'];

            if ($available_quantity < $required_quantity) {
                $max_available = floor($available_quantity / $product['quantity']);
                $max_quantity = min($max_quantity, $max_available);

                if ($max_available == 0) {
                    $unavailable_products[] = $product['product_name'];
                }
            }
        }

        return array(
            'available' => $max_quantity >= $quantity,
            'max_quantity' => $max_quantity == PHP_INT_MAX ? $quantity : $max_quantity,
            'unavailable_products' => $unavailable_products
        );
    }

    /**
     * إدارة شرائح التسعير - إضافة شريحة جديدة
     */
    public function addPriceTier($data) {
        $this->db->query("
            INSERT INTO " . DB_PREFIX . "product_price_tier SET
            product_id = '" . (int)$data['product_id'] . "',
            customer_group_id = '" . (int)$data['customer_group_id'] . "',
            min_quantity = '" . (int)$data['min_quantity'] . "',
            max_quantity = '" . (int)$data['max_quantity'] . "',
            price = '" . (float)$data['price'] . "',
            date_start = '" . $this->db->escape($data['date_start']) . "',
            date_end = '" . $this->db->escape($data['date_end']) . "',
            status = '" . (int)$data['status'] . "',
            date_added = NOW()
        ");

        return $this->db->getLastId();
    }

    /**
     * تعديل شريحة تسعير
     */
    public function editPriceTier($tier_id, $data) {
        $this->db->query("
            UPDATE " . DB_PREFIX . "product_price_tier SET
            customer_group_id = '" . (int)$data['customer_group_id'] . "',
            min_quantity = '" . (int)$data['min_quantity'] . "',
            max_quantity = '" . (int)$data['max_quantity'] . "',
            price = '" . (float)$data['price'] . "',
            date_start = '" . $this->db->escape($data['date_start']) . "',
            date_end = '" . $this->db->escape($data['date_end']) . "',
            status = '" . (int)$data['status'] . "',
            date_modified = NOW()
            WHERE tier_id = '" . (int)$tier_id . "'
        ");

        return $this->db->countAffected() > 0;
    }

    /**
     * حذف شريحة تسعير
     */
    public function deletePriceTier($tier_id) {
        $this->db->query("DELETE FROM " . DB_PREFIX . "product_price_tier WHERE tier_id = '" . (int)$tier_id . "'");
        return $this->db->countAffected() > 0;
    }

    /**
     * الحصول على شريحة تسعير
     */
    public function getPriceTier($tier_id) {
        $query = $this->db->query("
            SELECT * FROM " . DB_PREFIX . "product_price_tier
            WHERE tier_id = '" . (int)$tier_id . "'
        ");

        return $query->row;
    }

    /**
     * التحقق من تداخل شرائح التسعير
     */
    public function checkPriceTierOverlap($product_id, $min_quantity, $max_quantity, $customer_group_id, $exclude_tier_id = 0) {
        $sql = "
            SELECT * FROM " . DB_PREFIX . "product_price_tier
            WHERE product_id = '" . (int)$product_id . "'
            AND customer_group_id = '" . (int)$customer_group_id . "'
            AND status = '1'
        ";

        if ($exclude_tier_id > 0) {
            $sql .= " AND tier_id != '" . (int)$exclude_tier_id . "'";
        }

        if ($max_quantity > 0) {
            $sql .= " AND ((min_quantity <= '" . (int)$max_quantity . "' AND max_quantity >= '" . (int)$min_quantity . "')
                      OR (max_quantity = 0 AND min_quantity <= '" . (int)$max_quantity . "'))";
        } else {
            $sql .= " AND (max_quantity >= '" . (int)$min_quantity . "' OR max_quantity = 0)";
        }

        $query = $this->db->query($sql);

        return $query->row;
    }

    /**
     * الحصول على شرائح تسعير المنتج
     */
    public function getProductPriceTiers($product_id) {
        $query = $this->db->query("
            SELECT ppt.*, cg.name as customer_group_name
            FROM " . DB_PREFIX . "product_price_tier ppt
            LEFT JOIN " . DB_PREFIX . "customer_group cg ON (ppt.customer_group_id = cg.customer_group_id)
            WHERE ppt.product_id = '" . (int)$product_id . "'
            AND ppt.status = '1'
            ORDER BY ppt.customer_group_id ASC, ppt.min_quantity ASC
        ");

        return $query->rows;
    }

    /**
     * حساب سعر المنتج حسب الكمية ومجموعة العملاء
     */
    public function getProductPrice($product_id, $quantity, $customer_group_id = 0) {
        $product = $this->getProduct($product_id);

        if (!$product) {
            return false;
        }

        $original_price = $product['price'];
        $final_price = $original_price;
        $tier_name = 'السعر الأساسي';

        // البحث عن شريحة التسعير المناسبة
        $query = $this->db->query("
            SELECT ppt.*, cg.name as customer_group_name
            FROM " . DB_PREFIX . "product_price_tier ppt
            LEFT JOIN " . DB_PREFIX . "customer_group cg ON (ppt.customer_group_id = cg.customer_group_id)
            WHERE ppt.product_id = '" . (int)$product_id . "'
            AND ppt.customer_group_id = '" . (int)$customer_group_id . "'
            AND ppt.min_quantity <= '" . (int)$quantity . "'
            AND (ppt.max_quantity >= '" . (int)$quantity . "' OR ppt.max_quantity = 0)
            AND ppt.status = '1'
            AND (ppt.date_start = '0000-00-00' OR ppt.date_start <= NOW())
            AND (ppt.date_end = '0000-00-00' OR ppt.date_end >= NOW())
            ORDER BY ppt.min_quantity DESC
            LIMIT 1
        ");

        if ($query->num_rows) {
            $tier = $query->row;
            $final_price = $tier['price'];
            $tier_name = $tier['customer_group_name'] . ' - من ' . $tier['min_quantity'] . ' قطعة';
        }

        $discount = $original_price - $final_price;
        $discount_percentage = $original_price > 0 ? ($discount / $original_price) * 100 : 0;

        return array(
            'price' => $final_price,
            'original_price' => $original_price,
            'discount' => $discount,
            'discount_percentage' => round($discount_percentage, 2),
            'tier_name' => $tier_name
        );
    }

    /**
     * التحقق من صحة التسعير
     */
    public function validateProductPricing($product_id) {
        $errors = array();
        $warnings = array();

        // التحقق من وجود تداخل في شرائح التسعير
        $tiers = $this->getProductPriceTiers($product_id);

        foreach ($tiers as $i => $tier1) {
            foreach ($tiers as $j => $tier2) {
                if ($i != $j && $tier1['customer_group_id'] == $tier2['customer_group_id']) {
                    $overlap = $this->checkPriceTierOverlap(
                        $product_id,
                        $tier1['min_quantity'],
                        $tier1['max_quantity'],
                        $tier1['customer_group_id'],
                        $tier1['tier_id']
                    );

                    if ($overlap) {
                        $errors[] = 'تداخل في شرائح التسعير لمجموعة العملاء: ' . $tier1['customer_group_name'];
                    }
                }
            }
        }

        // التحقق من منطقية الأسعار
        $product = $this->getProduct($product_id);
        if ($product) {
            foreach ($tiers as $tier) {
                if ($tier['price'] <= 0) {
                    $errors[] = 'سعر غير صحيح في شريحة: ' . $tier['customer_group_name'];
                }

                if ($tier['price'] > $product['price'] * 2) {
                    $warnings[] = 'سعر مرتفع جداً في شريحة: ' . $tier['customer_group_name'];
                }
            }
        }

        return array(
            'valid' => empty($errors),
            'errors' => $errors,
            'warnings' => $warnings
        );
    }

    /**
     * إدارة الدفعات - إضافة دفعة جديدة
     */
    public function addProductBatch($data) {
        $this->db->query("
            INSERT INTO " . DB_PREFIX . "product_batch SET
            product_id = '" . (int)$data['product_id'] . "',
            batch_number = '" . $this->db->escape($data['batch_number']) . "',
            expiry_date = '" . $this->db->escape($data['expiry_date']) . "',
            manufacturing_date = '" . $this->db->escape($data['manufacturing_date']) . "',
            quantity = '" . (int)$data['quantity'] . "',
            cost_price = '" . (float)$data['cost_price'] . "',
            supplier_id = '" . (int)$data['supplier_id'] . "',
            notes = '" . $this->db->escape($data['notes']) . "',
            status = '" . (int)$data['status'] . "',
            date_added = '" . $this->db->escape($data['date_added']) . "'
        ");

        return $this->db->getLastId();
    }

    /**
     * تعديل دفعة
     */
    public function editProductBatch($batch_id, $data) {
        $this->db->query("
            UPDATE " . DB_PREFIX . "product_batch SET
            batch_number = '" . $this->db->escape($data['batch_number']) . "',
            expiry_date = '" . $this->db->escape($data['expiry_date']) . "',
            manufacturing_date = '" . $this->db->escape($data['manufacturing_date']) . "',
            quantity = '" . (int)$data['quantity'] . "',
            cost_price = '" . (float)$data['cost_price'] . "',
            supplier_id = '" . (int)$data['supplier_id'] . "',
            notes = '" . $this->db->escape($data['notes']) . "',
            status = '" . (int)$data['status'] . "',
            date_modified = NOW()
            WHERE batch_id = '" . (int)$batch_id . "'
        ");

        return $this->db->countAffected() > 0;
    }

    /**
     * الحصول على دفعة واحدة
     */
    public function getProductBatch($batch_id) {
        $query = $this->db->query("
            SELECT * FROM " . DB_PREFIX . "product_batch
            WHERE batch_id = '" . (int)$batch_id . "'
        ");

        return $query->row;
    }

    /**
     * البحث عن دفعة برقم الدفعة
     */
    public function getBatchByNumber($batch_number, $product_id, $exclude_batch_id = 0) {
        $sql = "
            SELECT * FROM " . DB_PREFIX . "product_batch
            WHERE batch_number = '" . $this->db->escape($batch_number) . "'
            AND product_id = '" . (int)$product_id . "'
        ";

        if ($exclude_batch_id > 0) {
            $sql .= " AND batch_id != '" . (int)$exclude_batch_id . "'";
        }

        $query = $this->db->query($sql);
        return $query->row;
    }

    /**
     * نظام FIFO للدفعات
     */
    public function getFIFOBatches($product_id, $required_quantity) {
        $query = $this->db->query("
            SELECT *,
            DATEDIFF(expiry_date, NOW()) as days_to_expiry,
            (expiry_date < NOW()) as is_expired
            FROM " . DB_PREFIX . "product_batch
            WHERE product_id = '" . (int)$product_id . "'
            AND quantity > 0
            AND status = '1'
            ORDER BY
                CASE WHEN expiry_date = '0000-00-00' THEN 1 ELSE 0 END,
                expiry_date ASC,
                date_added ASC
        ");

        $batches = array();
        $remaining_quantity = $required_quantity;

        foreach ($query->rows as $batch) {
            if ($remaining_quantity <= 0) {
                break;
            }

            $available_quantity = min($batch['quantity'], $remaining_quantity);
            $batch['available_quantity'] = $available_quantity;

            $batches[] = $batch;
            $remaining_quantity -= $available_quantity;
        }

        return $batches;
    }

    /**
     * الحصول على المنتجات قاربة الانتهاء
     */
    public function getExpiringProducts($days = 30) {
        $query = $this->db->query("
            SELECT pb.*, p.model, pd.name as product_name,
            DATEDIFF(pb.expiry_date, NOW()) as days_to_expiry,
            (pb.quantity * pb.cost_price) as cost_value
            FROM " . DB_PREFIX . "product_batch pb
            LEFT JOIN " . DB_PREFIX . "product p ON (pb.product_id = p.product_id)
            LEFT JOIN " . DB_PREFIX . "product_description pd ON (p.product_id = pd.product_id)
            WHERE pb.expiry_date != '0000-00-00'
            AND pb.expiry_date <= DATE_ADD(NOW(), INTERVAL " . (int)$days . " DAY)
            AND pb.expiry_date >= NOW()
            AND pb.quantity > 0
            AND pb.status = '1'
            AND pd.language_id = '" . (int)$this->config->get('config_language_id') . "'
            ORDER BY pb.expiry_date ASC
        ");

        return $query->rows;
    }

    /**
     * تحديث كمية المنتج
     */
    public function updateProductQuantity($product_id, $quantity, $operation = 'add') {
        if ($operation == 'add') {
            $this->db->query("
                UPDATE " . DB_PREFIX . "product SET
                quantity = quantity + '" . (int)$quantity . "'
                WHERE product_id = '" . (int)$product_id . "'
            ");
        } else {
            $this->db->query("
                UPDATE " . DB_PREFIX . "product SET
                quantity = GREATEST(0, quantity - '" . (int)$quantity . "')
                WHERE product_id = '" . (int)$product_id . "'
            ");
        }

        return $this->db->countAffected() > 0;
    }

    /**
     * التصنيف التلقائي - اقتراح التصنيفات
     */
    public function suggestCategories($product_name, $product_description = '') {
        $keywords = $this->extractKeywords($product_name . ' ' . $product_description);

        if (empty($keywords)) {
            return array();
        }

        $suggestions = array();

        // البحث في أسماء التصنيفات
        foreach ($keywords as $keyword) {
            $query = $this->db->query("
                SELECT c.category_id, cd.name,
                (MATCH(cd.name) AGAINST('" . $this->db->escape($keyword) . "' IN NATURAL LANGUAGE MODE)) as relevance
                FROM " . DB_PREFIX . "category c
                LEFT JOIN " . DB_PREFIX . "category_description cd ON (c.category_id = cd.category_id)
                WHERE cd.language_id = '" . (int)$this->config->get('config_language_id') . "'
                AND c.status = '1'
                AND MATCH(cd.name) AGAINST('" . $this->db->escape($keyword) . "' IN NATURAL LANGUAGE MODE)
                ORDER BY relevance DESC
                LIMIT 5
            ");

            foreach ($query->rows as $category) {
                $category_id = $category['category_id'];
                if (!isset($suggestions[$category_id])) {
                    $suggestions[$category_id] = array(
                        'category_id' => $category_id,
                        'name' => $category['name'],
                        'relevance' => 0
                    );
                }
                $suggestions[$category_id]['relevance'] += $category['relevance'];
            }
        }

        // ترتيب حسب الصلة
        usort($suggestions, function($a, $b) {
            return $b['relevance'] <=> $a['relevance'];
        });

        return array_slice($suggestions, 0, 3);
    }

    /**
     * استخراج الكلمات المفتاحية
     */
    private function extractKeywords($text) {
        // إزالة الكلمات الشائعة
        $stopwords = array('في', 'من', 'إلى', 'على', 'عن', 'مع', 'هذا', 'هذه', 'ذلك', 'تلك', 'التي', 'الذي');

        // تنظيف النص
        $text = preg_replace('/[^\p{Arabic}\p{Latin}\s]/u', ' ', $text);
        $words = preg_split('/\s+/', trim($text));

        $keywords = array();
        foreach ($words as $word) {
            $word = trim($word);
            if (strlen($word) > 2 && !in_array($word, $stopwords)) {
                $keywords[] = $word;
            }
        }

        return array_unique($keywords);
    }

    /**
     * إضافة منتج لتصنيف
     */
    public function addProductToCategory($product_id, $category_id) {
        $this->db->query("
            INSERT IGNORE INTO " . DB_PREFIX . "product_to_category SET
            product_id = '" . (int)$product_id . "',
            category_id = '" . (int)$category_id . "'
        ");

        return $this->db->countAffected() > 0;
    }

    /**
     * ربط حساب محاسبي بالمنتج
     */
    public function linkProductAccount($product_id, $account_id) {
        $this->db->query("
            UPDATE " . DB_PREFIX . "product SET
            inventory_account_id = '" . (int)$account_id . "'
            WHERE product_id = '" . (int)$product_id . "'
        ");

        return $this->db->countAffected() > 0;
    }

    /**
     * تحديث تكلفة المنتج (WAC)
     */
    public function updateProductCost($product_id, $new_cost) {
        $this->db->query("
            UPDATE " . DB_PREFIX . "product SET
            cost_price = '" . (float)$new_cost . "',
            average_cost = '" . (float)$new_cost . "',
            date_modified = NOW()
            WHERE product_id = '" . (int)$product_id . "'
        ");

        return $this->db->countAffected() > 0;
    }

    /**
     * استيراد المنتجات من ملف
     */
    public function importProductsFromFile($file_path, $file_extension) {
        // هذه دالة مبسطة - يمكن تطويرها لاحقاً
        return array(
            'success' => true,
            'imported_count' => 0,
            'skipped_count' => 0,
            'errors' => array()
        );
    }

    /**
     * تصدير المنتجات
     */
    public function exportProducts($format, $filter_data) {
        // هذه دالة مبسطة - يمكن تطويرها لاحقاً
        return array(
            'success' => true,
            'file_path' => '/tmp/products_export.' . $format
        );
    }
}
