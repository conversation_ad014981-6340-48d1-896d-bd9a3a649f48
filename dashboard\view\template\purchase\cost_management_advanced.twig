{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="submit" form="purchase\cost_management_advanced-form" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fas fa-save"></i></button>
        <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-light"><i class="fas fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    
    <div class="card">
      <div class="card-header"><i class="fas fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}chart-bar{% elif screen_type == 'settings' %}cog{% else %}pencil-alt{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="card-body">
        <form id="purchase\cost_management_advanced-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-allocate_costs_url">{{ text_allocate_costs_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="allocate_costs_url" value="{{ allocate_costs_url }}" placeholder="{{ text_allocate_costs_url }}" id="input-allocate_costs_url" class="form-control" />
              {% if error_allocate_costs_url %}
                <div class="invalid-feedback">{{ error_allocate_costs_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-calculate_wac_url">{{ text_calculate_wac_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="calculate_wac_url" value="{{ calculate_wac_url }}" placeholder="{{ text_calculate_wac_url }}" id="input-calculate_wac_url" class="form-control" />
              {% if error_calculate_wac_url %}
                <div class="invalid-feedback">{{ error_calculate_wac_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-can_allocate_costs">{{ text_can_allocate_costs }}</label>
            <div class="col-sm-10">
              <input type="text" name="can_allocate_costs" value="{{ can_allocate_costs }}" placeholder="{{ text_can_allocate_costs }}" id="input-can_allocate_costs" class="form-control" />
              {% if error_can_allocate_costs %}
                <div class="invalid-feedback">{{ error_can_allocate_costs }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-can_calculate_wac">{{ text_can_calculate_wac }}</label>
            <div class="col-sm-10">
              <input type="text" name="can_calculate_wac" value="{{ can_calculate_wac }}" placeholder="{{ text_can_calculate_wac }}" id="input-can_calculate_wac" class="form-control" />
              {% if error_can_calculate_wac %}
                <div class="invalid-feedback">{{ error_can_calculate_wac }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-categories">{{ text_categories }}</label>
            <div class="col-sm-10">
              <input type="text" name="categories" value="{{ categories }}" placeholder="{{ text_categories }}" id="input-categories" class="form-control" />
              {% if error_categories %}
                <div class="invalid-feedback">{{ error_categories }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-cost_analysis_url">{{ text_cost_analysis_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="cost_analysis_url" value="{{ cost_analysis_url }}" placeholder="{{ text_cost_analysis_url }}" id="input-cost_analysis_url" class="form-control" />
              {% if error_cost_analysis_url %}
                <div class="invalid-feedback">{{ error_cost_analysis_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-cost_trends_url">{{ text_cost_trends_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="cost_trends_url" value="{{ cost_trends_url }}" placeholder="{{ text_cost_trends_url }}" id="input-cost_trends_url" class="form-control" />
              {% if error_cost_trends_url %}
                <div class="invalid-feedback">{{ error_cost_trends_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-export_url">{{ text_export_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="export_url" value="{{ export_url }}" placeholder="{{ text_export_url }}" id="input-export_url" class="form-control" />
              {% if error_export_url %}
                <div class="invalid-feedback">{{ error_export_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="invalid-feedback">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-optimization_url">{{ text_optimization_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="optimization_url" value="{{ optimization_url }}" placeholder="{{ text_optimization_url }}" id="input-optimization_url" class="form-control" />
              {% if error_optimization_url %}
                <div class="invalid-feedback">{{ error_optimization_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-products">{{ text_products }}</label>
            <div class="col-sm-10">
              <input type="text" name="products" value="{{ products }}" placeholder="{{ text_products }}" id="input-products" class="form-control" />
              {% if error_products %}
                <div class="invalid-feedback">{{ error_products }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-suppliers">{{ text_suppliers }}</label>
            <div class="col-sm-10">
              <input type="text" name="suppliers" value="{{ suppliers }}" placeholder="{{ text_suppliers }}" id="input-suppliers" class="form-control" />
              {% if error_suppliers %}
                <div class="invalid-feedback">{{ error_suppliers }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="invalid-feedback">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-variance_analysis_url">{{ text_variance_analysis_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="variance_analysis_url" value="{{ variance_analysis_url }}" placeholder="{{ text_variance_analysis_url }}" id="input-variance_analysis_url" class="form-control" />
              {% if error_variance_analysis_url %}
                <div class="invalid-feedback">{{ error_variance_analysis_url }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}