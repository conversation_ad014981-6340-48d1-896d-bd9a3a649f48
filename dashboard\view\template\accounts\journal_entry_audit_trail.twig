{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="submit" form="accounts\journal_entry-form" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fas fa-save"></i></button>
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fas fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="close" data-dismiss="alert">&times;</button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="close" data-dismiss="alert">&times;</button>
      </div>
    {{% endif %}}
    
    <div class="card">
      <div class="card-header"><i class="fas fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}chart-bar{% elif screen_type == 'settings' %}cog{% else %}pencil-alt{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="card-body">
        <form id="accounts\journal_entry-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-audit_trail">{{ text_audit_trail }}</label>
            <div class="col-sm-10">
              <input type="text" name="audit_trail" value="{{ audit_trail }}" placeholder="{{ text_audit_trail }}" id="input-audit_trail" class="form-control" />
              {% if error_audit_trail %}
                <div class="text-danger">{{ error_audit_trail }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-company_name">{{ text_company_name }}</label>
            <div class="col-sm-10">
              <input type="text" name="company_name" value="{{ company_name }}" placeholder="{{ text_company_name }}" id="input-company_name" class="form-control" />
              {% if error_company_name %}
                <div class="text-danger">{{ error_company_name }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-cost_centers">{{ text_cost_centers }}</label>
            <div class="col-sm-10">
              <input type="text" name="cost_centers" value="{{ cost_centers }}" placeholder="{{ text_cost_centers }}" id="input-cost_centers" class="form-control" />
              {% if error_cost_centers %}
                <div class="text-danger">{{ error_cost_centers }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-departments">{{ text_departments }}</label>
            <div class="col-sm-10">
              <input type="text" name="departments" value="{{ departments }}" placeholder="{{ text_departments }}" id="input-departments" class="form-control" />
              {% if error_departments %}
                <div class="text-danger">{{ error_departments }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-error_description">{{ text_error_description }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_description" value="{{ error_description }}" placeholder="{{ text_error_description }}" id="input-error_description" class="form-control" />
              {% if error_error_description %}
                <div class="text-danger">{{ error_error_description }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-error_journal_date">{{ text_error_journal_date }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_journal_date" value="{{ error_journal_date }}" placeholder="{{ text_error_journal_date }}" id="input-error_journal_date" class="form-control" />
              {% if error_error_journal_date %}
                <div class="text-danger">{{ error_error_journal_date }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-error_lines">{{ text_error_lines }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_lines" value="{{ error_lines }}" placeholder="{{ text_error_lines }}" id="input-error_lines" class="form-control" />
              {% if error_error_lines %}
                <div class="text-danger">{{ error_error_lines }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-get_account_info_url">{{ text_get_account_info_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="get_account_info_url" value="{{ get_account_info_url }}" placeholder="{{ text_get_account_info_url }}" id="input-get_account_info_url" class="form-control" />
              {% if error_get_account_info_url %}
                <div class="text-danger">{{ error_get_account_info_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-get_templates_url">{{ text_get_templates_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="get_templates_url" value="{{ get_templates_url }}" placeholder="{{ text_get_templates_url }}" id="input-get_templates_url" class="form-control" />
              {% if error_get_templates_url %}
                <div class="text-danger">{{ error_get_templates_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="text-danger">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-journal">{{ text_journal }}</label>
            <div class="col-sm-10">
              <input type="text" name="journal" value="{{ journal }}" placeholder="{{ text_journal }}" id="input-journal" class="form-control" />
              {% if error_journal %}
                <div class="text-danger">{{ error_journal }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-journal_id">{{ text_journal_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="journal_id" value="{{ journal_id }}" placeholder="{{ text_journal_id }}" id="input-journal_id" class="form-control" />
              {% if error_journal_id %}
                <div class="text-danger">{{ error_journal_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-journals">{{ text_journals }}</label>
            <div class="col-sm-10">
              <input type="text" name="journals" value="{{ journals }}" placeholder="{{ text_journals }}" id="input-journals" class="form-control" />
              {% if error_journals %}
                <div class="text-danger">{{ error_journals }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-lines">{{ text_lines }}</label>
            <div class="col-sm-10">
              <input type="text" name="lines" value="{{ lines }}" placeholder="{{ text_lines }}" id="input-lines" class="form-control" />
              {% if error_lines %}
                <div class="text-danger">{{ error_lines }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-print_date">{{ text_print_date }}</label>
            <div class="col-sm-10">
              <input type="text" name="print_date" value="{{ print_date }}" placeholder="{{ text_print_date }}" id="input-print_date" class="form-control" />
              {% if error_print_date %}
                <div class="text-danger">{{ error_print_date }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-projects">{{ text_projects }}</label>
            <div class="col-sm-10">
              <input type="text" name="projects" value="{{ projects }}" placeholder="{{ text_projects }}" id="input-projects" class="form-control" />
              {% if error_projects %}
                <div class="text-danger">{{ error_projects }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-reference_types">{{ text_reference_types }}</label>
            <div class="col-sm-10">
              <input type="text" name="reference_types" value="{{ reference_types }}" placeholder="{{ text_reference_types }}" id="input-reference_types" class="form-control" />
              {% if error_reference_types %}
                <div class="text-danger">{{ error_reference_types }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-save_template_url">{{ text_save_template_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="save_template_url" value="{{ save_template_url }}" placeholder="{{ text_save_template_url }}" id="input-save_template_url" class="form-control" />
              {% if error_save_template_url %}
                <div class="text-danger">{{ error_save_template_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-search_accounts_url">{{ text_search_accounts_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="search_accounts_url" value="{{ search_accounts_url }}" placeholder="{{ text_search_accounts_url }}" id="input-search_accounts_url" class="form-control" />
              {% if error_search_accounts_url %}
                <div class="text-danger">{{ error_search_accounts_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-statuses">{{ text_statuses }}</label>
            <div class="col-sm-10">
              <input type="text" name="statuses" value="{{ statuses }}" placeholder="{{ text_statuses }}" id="input-statuses" class="form-control" />
              {% if error_statuses %}
                <div class="text-danger">{{ error_statuses }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-text_form">{{ text_text_form }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_form" value="{{ text_form }}" placeholder="{{ text_text_form }}" id="input-text_form" class="form-control" />
              {% if error_text_form %}
                <div class="text-danger">{{ error_text_form }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-validate_balance_url">{{ text_validate_balance_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="validate_balance_url" value="{{ validate_balance_url }}" placeholder="{{ text_validate_balance_url }}" id="input-validate_balance_url" class="form-control" />
              {% if error_validate_balance_url %}
                <div class="text-danger">{{ error_validate_balance_url }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}