<?php
/**
 * نموذج قوالب الإشعارات
 * Notification Templates Model
 * 
 * نموذج البيانات لقوالب الإشعارات والرسائل
 * 
 * @package    AYM ERP
 * <AUTHOR> ERP Development Team
 * @copyright  2024 AYM ERP
 * @license    Proprietary
 * @version    1.0.0
 * @since      2024-12-19
 */

class ModelNotificationTemplates extends Model {
    
    /**
     * إضافة قالب جديد
     */
    public function addTemplate($data) {
        $this->db->query("INSERT INTO " . DB_PREFIX . "notification_template SET
            name = '" . $this->db->escape($data['name']) . "',
            description = '" . $this->db->escape($data['description']) . "',
            title = '" . $this->db->escape($data['title']) . "',
            content = '" . $this->db->escape($data['content']) . "',
            type = '" . $this->db->escape($data['type']) . "',
            priority = '" . $this->db->escape($data['priority']) . "',
            category = '" . $this->db->escape($data['category']) . "',
            channels = '" . $this->db->escape(json_encode($data['channels'])) . "',
            variables = '" . $this->db->escape(json_encode($data['variables'] ?? [])) . "',
            email_subject = '" . $this->db->escape($data['email_subject'] ?? '') . "',
            email_content = '" . $this->db->escape($data['email_content'] ?? '') . "',
            sms_content = '" . $this->db->escape($data['sms_content'] ?? '') . "',
            status = '" . $this->db->escape($data['status']) . "',
            is_system = '" . (int)($data['is_system'] ?? 0) . "',
            created_by = '" . (int)$this->user->getId() . "',
            created_at = NOW()");
        
        $template_id = $this->db->getLastId();

        // تسجيل النشاط (محدث للخدمات المركزية)
        try {
            $this->load->model('core/central_service_manager');
            $this->model_core_central_service_manager->logCreate(
                'notification',
                'template',
                $template_id,
                'تم إنشاء قالب إشعار جديد: ' . ($data['name'] ?? 'بدون اسم'),
                [
                    'template_data' => $data,
                    'template_type' => $data['type'] ?? 'general'
                ]
            );
        } catch (Exception $e) {
            // النظام الاحتياطي
            error_log("Failed to log template creation via central service: " . $e->getMessage());
            try {
                $this->load->model('activity_log');
                $this->model_activity_log->logCreate('notification', 'template', $template_id, $data);
            } catch (Exception $e2) {
                error_log("CRITICAL: Both logging systems failed: " . $e2->getMessage());
            }
        }

        return $template_id;
    }
    
    /**
     * تحديث قالب
     */
    public function editTemplate($template_id, $data) {
        $this->db->query("UPDATE " . DB_PREFIX . "notification_template SET
            name = '" . $this->db->escape($data['name']) . "',
            description = '" . $this->db->escape($data['description']) . "',
            title = '" . $this->db->escape($data['title']) . "',
            content = '" . $this->db->escape($data['content']) . "',
            type = '" . $this->db->escape($data['type']) . "',
            priority = '" . $this->db->escape($data['priority']) . "',
            category = '" . $this->db->escape($data['category']) . "',
            channels = '" . $this->db->escape(json_encode($data['channels'])) . "',
            variables = '" . $this->db->escape(json_encode($data['variables'] ?? [])) . "',
            email_subject = '" . $this->db->escape($data['email_subject'] ?? '') . "',
            email_content = '" . $this->db->escape($data['email_content'] ?? '') . "',
            sms_content = '" . $this->db->escape($data['sms_content'] ?? '') . "',
            status = '" . $this->db->escape($data['status']) . "',
            updated_at = NOW()
            WHERE template_id = '" . (int)$template_id . "'
            AND is_system = 0");
        
        // تسجيل النشاط (محدث للخدمات المركزية)
        try {
            $this->load->model('core/central_service_manager');
            $this->model_core_central_service_manager->logUpdate(
                'notification',
                'template',
                $template_id,
                'تم تحديث قالب الإشعار: ' . ($data['name'] ?? 'قالب #' . $template_id),
                [
                    'updated_data' => $data,
                    'template_type' => $data['type'] ?? 'general'
                ]
            );
        } catch (Exception $e) {
            // النظام الاحتياطي
            error_log("Failed to log template update via central service: " . $e->getMessage());
            try {
                $this->load->model('activity_log');
                $this->model_activity_log->logUpdate('notification', 'template', $template_id, [], $data);
            } catch (Exception $e2) {
                error_log("CRITICAL: Both logging systems failed: " . $e2->getMessage());
            }
        }
    }
    
    /**
     * حذف قالب
     */
    public function deleteTemplate($template_id) {
        $template = $this->getTemplate($template_id);
        
        // لا يمكن حذف القوالب النظامية
        if ($template && $template['is_system']) {
            return false;
        }
        
        $this->db->query("DELETE FROM " . DB_PREFIX . "notification_template WHERE template_id = '" . (int)$template_id . "' AND is_system = 0");
        
        // تسجيل النشاط (محدث للخدمات المركزية)
        try {
            $this->load->model('core/central_service_manager');
            $this->model_core_central_service_manager->logDelete(
                'notification',
                'template',
                $template_id,
                'تم حذف قالب الإشعار: ' . ($template['name'] ?? 'قالب #' . $template_id),
                [
                    'deleted_template' => $template,
                    'template_type' => $template['type'] ?? 'general'
                ]
            );
        } catch (Exception $e) {
            // النظام الاحتياطي
            error_log("Failed to log template deletion via central service: " . $e->getMessage());
            try {
                $this->load->model('activity_log');
                $this->model_activity_log->logDelete('notification', 'template', $template_id, $template);
            } catch (Exception $e2) {
                error_log("CRITICAL: Both logging systems failed: " . $e2->getMessage());
            }
        }

        return true;
    }
    
    /**
     * الحصول على قالب
     */
    public function getTemplate($template_id) {
        $query = $this->db->query("SELECT nt.*, 
            CONCAT(u.firstname, ' ', u.lastname) as creator_name
            FROM " . DB_PREFIX . "notification_template nt
            LEFT JOIN " . DB_PREFIX . "user u ON (nt.created_by = u.user_id)
            WHERE nt.template_id = '" . (int)$template_id . "'");
        
        if ($query->num_rows) {
            $template = $query->row;
            $template['channels'] = json_decode($template['channels'], true);
            $template['variables'] = json_decode($template['variables'], true);
            return $template;
        }
        
        return false;
    }
    
    /**
     * الحصول على القوالب
     */
    public function getTemplates($data = []) {
        $sql = "SELECT nt.*, 
            CONCAT(u.firstname, ' ', u.lastname) as creator_name,
            (SELECT COUNT(*) FROM " . DB_PREFIX . "notification_automation na 
             WHERE na.notification_template_id = nt.template_id) as usage_count
            FROM " . DB_PREFIX . "notification_template nt
            LEFT JOIN " . DB_PREFIX . "user u ON (nt.created_by = u.user_id)
            WHERE 1=1";
        
        if (!empty($data['filter_category'])) {
            $sql .= " AND nt.category = '" . $this->db->escape($data['filter_category']) . "'";
        }
        
        if (!empty($data['filter_type'])) {
            $sql .= " AND nt.type = '" . $this->db->escape($data['filter_type']) . "'";
        }
        
        if (!empty($data['filter_status'])) {
            $sql .= " AND nt.status = '" . $this->db->escape($data['filter_status']) . "'";
        }
        
        if (isset($data['filter_system'])) {
            $sql .= " AND nt.is_system = '" . (int)$data['filter_system'] . "'";
        }
        
        $sql .= " ORDER BY nt.is_system DESC, nt.created_at DESC";
        
        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }
            
            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }
            
            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }
        
        $query = $this->db->query($sql);
        
        return $query->rows;
    }
    
    /**
     * الحصول على القوالب حسب الفئة
     */
    public function getTemplatesByCategory($category) {
        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "notification_template 
            WHERE category = '" . $this->db->escape($category) . "' 
            AND status = 'active'
            ORDER BY name ASC");
        
        return $query->rows;
    }
    
    /**
     * نسخ قالب
     */
    public function copyTemplate($template_id, $new_name) {
        $template = $this->getTemplate($template_id);
        if (!$template) {
            return false;
        }
        
        $data = [
            'name' => $new_name,
            'description' => $template['description'] . ' (نسخة)',
            'title' => $template['title'],
            'content' => $template['content'],
            'type' => $template['type'],
            'priority' => $template['priority'],
            'category' => $template['category'],
            'channels' => $template['channels'],
            'variables' => $template['variables'],
            'email_subject' => $template['email_subject'],
            'email_content' => $template['email_content'],
            'sms_content' => $template['sms_content'],
            'status' => 'draft'
        ];
        
        return $this->addTemplate($data);
    }
    
    /**
     * معاينة قالب مع البيانات
     */
    public function previewTemplate($template_id, $sample_data = []) {
        $template = $this->getTemplate($template_id);
        if (!$template) {
            return false;
        }
        
        $preview = [
            'title' => $template['title'],
            'content' => $template['content'],
            'email_subject' => $template['email_subject'],
            'email_content' => $template['email_content'],
            'sms_content' => $template['sms_content']
        ];
        
        // استبدال المتغيرات بالبيانات النموذجية
        foreach ($sample_data as $key => $value) {
            $placeholder = '{' . $key . '}';
            $preview['title'] = str_replace($placeholder, $value, $preview['title']);
            $preview['content'] = str_replace($placeholder, $value, $preview['content']);
            $preview['email_subject'] = str_replace($placeholder, $value, $preview['email_subject']);
            $preview['email_content'] = str_replace($placeholder, $value, $preview['email_content']);
            $preview['sms_content'] = str_replace($placeholder, $value, $preview['sms_content']);
        }
        
        return $preview;
    }
    
    /**
     * الحصول على المتغيرات المتاحة للقالب
     */
    public function getAvailableVariables($category) {
        $variables = [
            'common' => [
                'user_name' => 'اسم المستخدم',
                'company_name' => 'اسم الشركة',
                'current_date' => 'التاريخ الحالي',
                'current_time' => 'الوقت الحالي'
            ]
        ];
        
        switch ($category) {
            case 'order':
                $variables['order'] = [
                    'order_id' => 'رقم الطلب',
                    'order_total' => 'إجمالي الطلب',
                    'customer_name' => 'اسم العميل',
                    'order_status' => 'حالة الطلب'
                ];
                break;
                
            case 'inventory':
                $variables['inventory'] = [
                    'product_name' => 'اسم المنتج',
                    'current_stock' => 'المخزون الحالي',
                    'minimum_stock' => 'الحد الأدنى للمخزون',
                    'warehouse_name' => 'اسم المستودع'
                ];
                break;
                
            case 'user':
                $variables['user'] = [
                    'user_email' => 'بريد المستخدم الإلكتروني',
                    'user_role' => 'دور المستخدم',
                    'last_login' => 'آخر تسجيل دخول'
                ];
                break;
        }
        
        return $variables;
    }
    
    /**
     * تصدير قالب
     */
    public function exportTemplate($template_id) {
        $template = $this->getTemplate($template_id);
        if (!$template) {
            return false;
        }
        
        // إزالة البيانات غير المطلوبة للتصدير
        unset($template['template_id']);
        unset($template['created_by']);
        unset($template['creator_name']);
        unset($template['created_at']);
        unset($template['updated_at']);
        
        return $template;
    }
    
    /**
     * استيراد قالب
     */
    public function importTemplate($template_data) {
        // التحقق من صحة البيانات
        if (empty($template_data['name']) || empty($template_data['title'])) {
            return false;
        }
        
        // التحقق من عدم وجود قالب بنفس الاسم
        $existing = $this->db->query("SELECT template_id FROM " . DB_PREFIX . "notification_template 
            WHERE name = '" . $this->db->escape($template_data['name']) . "'");
        
        if ($existing->num_rows) {
            $template_data['name'] .= ' (مستورد)';
        }
        
        $template_data['status'] = 'draft';
        $template_data['is_system'] = 0;
        
        return $this->addTemplate($template_data);
    }
    
    /**
     * الحصول على إحصائيات القوالب
     */
    public function getTemplateStats() {
        $stats = [];
        
        // إجمالي القوالب
        $query = $this->db->query("SELECT COUNT(*) as total FROM " . DB_PREFIX . "notification_template");
        $stats['total_templates'] = $query->row['total'];
        
        // القوالب النشطة
        $query = $this->db->query("SELECT COUNT(*) as total FROM " . DB_PREFIX . "notification_template WHERE status = 'active'");
        $stats['active_templates'] = $query->row['total'];
        
        // القوالب النظامية
        $query = $this->db->query("SELECT COUNT(*) as total FROM " . DB_PREFIX . "notification_template WHERE is_system = 1");
        $stats['system_templates'] = $query->row['total'];
        
        // القوالب حسب الفئة
        $query = $this->db->query("SELECT category, COUNT(*) as count FROM " . DB_PREFIX . "notification_template GROUP BY category");
        $stats['by_category'] = [];
        foreach ($query->rows as $row) {
            $stats['by_category'][$row['category']] = $row['count'];
        }
        
        return $stats;
    }
    
    /**
     * البحث في القوالب
     */
    public function searchTemplates($search_term, $limit = 20) {
        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "notification_template 
            WHERE (name LIKE '%" . $this->db->escape($search_term) . "%'
                   OR title LIKE '%" . $this->db->escape($search_term) . "%'
                   OR content LIKE '%" . $this->db->escape($search_term) . "%')
            ORDER BY name ASC
            LIMIT " . (int)$limit);
        
        return $query->rows;
    }
}
