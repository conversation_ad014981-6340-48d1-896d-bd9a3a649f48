<div class="table-responsive">
  <table class="table table-bordered">
    <thead>
      <tr>
        <td class="text-start"><strong>{{ column_date_added }}</strong></td>
        <td class="text-start"><strong>{{ column_comment }}</strong></td>
        <td class="text-start"><strong>{{ column_status }}</strong></td>
      </tr>
    </thead>
    <tbody>
      {% if histories %}
        {% for history in histories %}
          <tr>
            <td class="text-start">{{ history.date_added }}</td>
            <td class="text-start">{{ history.comment }}</td>
            <td class="text-start">{{ history.status }}</td>
          </tr>
        {% endfor %}
      {% else %}
        <tr>
          <td class="text-center" colspan="3">{{ text_no_results }}</td>
        </tr>
      {% endif %}
    </tbody>
  </table>
</div>
<div class="row">
  <div class="col-sm-6 text-start">{{ pagination }}</div>
  <div class="col-sm-6 text-end">{{ results }}</div>
</div>
