<?php
/**
 * AYM ERP Column Left Model
 * Model for sidebar navigation functionality
 */

class ModelCommonColumnLeft extends Model {
    
    /**
     * Get user menu permissions
     * @param int $user_group_id
     * @return array
     */
    public function getUserPermissions($user_group_id) {
        $query = $this->db->query("SELECT permission FROM " . DB_PREFIX . "user_group WHERE user_group_id = '" . (int)$user_group_id . "'");

        if ($query->num_rows && isset($query->row['permission'])) {
            // Check if permission is already an array or needs to be decoded
            if (is_array($query->row['permission'])) {
                return $query->row['permission'];
            } elseif (is_string($query->row['permission'])) {
                $permissions = json_decode($query->row['permission'], true);
                return $permissions ? $permissions : [];
            }
        }

        return [];
    }
    
    /**
     * Check if user has permission for specific route
     * @param string $route
     * @param array $permissions
     * @return bool
     */
    public function hasRoutePermission($route, $permissions) {
        if (empty($permissions)) {
            return false;
        }
        
        // Check access permission
        if (isset($permissions['access']) && is_array($permissions['access'])) {
            return in_array($route, $permissions['access']);
        }
        
        return false;
    }
    
    /**
     * Get system status for menu indicators
     * @return array
     */
    public function getSystemStatus() {
        $status = [
            'health' => 'good',
            'notifications' => 0,
            'critical_alerts' => 0,
            'pending_approvals' => 0,
            'low_stock_items' => 0
        ];
        
        try {
            // Get notification count
            $notification_query = $this->db->query("
                SELECT COUNT(*) as total 
                FROM " . DB_PREFIX . "unified_notification 
                WHERE user_id = '" . (int)$this->user->getId() . "' 
                AND is_read = 0
            ");
            
            if ($notification_query->num_rows) {
                $status['notifications'] = (int)$notification_query->row['total'];
            }
            
            // Get critical alerts count
            $alert_query = $this->db->query("
                SELECT COUNT(*) as total 
                FROM " . DB_PREFIX . "unified_notification 
                WHERE user_id = '" . (int)$this->user->getId() . "' 
                AND priority = 'urgent' 
                AND is_read = 0
            ");
            
            if ($alert_query->num_rows) {
                $status['critical_alerts'] = (int)$alert_query->row['total'];
            }
            
            // Get pending approvals count
            $approval_query = $this->db->query("
                SELECT COUNT(*) as total 
                FROM " . DB_PREFIX . "workflow_request 
                WHERE status = 'pending' 
                AND current_approver_id = '" . (int)$this->user->getId() . "'
            ");
            
            if ($approval_query->num_rows) {
                $status['pending_approvals'] = (int)$approval_query->row['total'];
            }
            
            // Get low stock items count
            $stock_query = $this->db->query("
                SELECT COUNT(*) as total 
                FROM " . DB_PREFIX . "product p
                LEFT JOIN " . DB_PREFIX . "product_inventory pi ON p.product_id = pi.product_id
                WHERE pi.quantity <= pi.minimum_quantity 
                AND p.status = 1
            ");
            
            if ($stock_query->num_rows) {
                $status['low_stock_items'] = (int)$stock_query->row['total'];
            }
            
            // Determine overall health
            if ($status['critical_alerts'] > 0) {
                $status['health'] = 'critical';
            } elseif ($status['low_stock_items'] > 10 || $status['pending_approvals'] > 5) {
                $status['health'] = 'warning';
            } else {
                $status['health'] = 'good';
            }
            
        } catch (Exception $e) {
            error_log('Column Left Model Error: ' . $e->getMessage());
        }
        
        return $status;
    }
    
    /**
     * Get quick access items for user
     * @return array
     */
    public function getQuickAccessItems() {
        $items = [];
        
        try {
            // Get user's most accessed routes from activity log
            $query = $this->db->query("
                SELECT route, COUNT(*) as access_count
                FROM " . DB_PREFIX . "user_activity 
                WHERE user_id = '" . (int)$this->user->getId() . "'
                AND date_added >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                GROUP BY route
                ORDER BY access_count DESC
                LIMIT 5
            ");
            
            foreach ($query->rows as $row) {
                $items[] = [
                    'route' => $row['route'],
                    'count' => $row['access_count']
                ];
            }
            
        } catch (Exception $e) {
            error_log('Quick Access Items Error: ' . $e->getMessage());
        }
        
        return $items;
    }
    
    /**
     * Log menu access for analytics
     * @param string $menu_id
     * @param string $route
     */
    public function logMenuAccess($menu_id, $route) {
        try {
            $this->db->query("
                INSERT INTO " . DB_PREFIX . "menu_analytics 
                (user_id, menu_id, route, access_time, ip_address) 
                VALUES (
                    '" . (int)$this->user->getId() . "',
                    '" . $this->db->escape($menu_id) . "',
                    '" . $this->db->escape($route) . "',
                    NOW(),
                    '" . $this->db->escape($this->request->server['REMOTE_ADDR']) . "'
                )
            ");
        } catch (Exception $e) {
            error_log('Menu Access Logging Error: ' . $e->getMessage());
        }
    }
    
    /**
     * Get menu favorites for user
     * @return array
     */
    public function getUserFavorites() {
        $favorites = [];
        
        try {
            $query = $this->db->query("
                SELECT menu_id, route, display_name
                FROM " . DB_PREFIX . "user_menu_favorites 
                WHERE user_id = '" . (int)$this->user->getId() . "'
                ORDER BY sort_order ASC
            ");
            
            $favorites = $query->rows;
            
        } catch (Exception $e) {
            error_log('User Favorites Error: ' . $e->getMessage());
        }
        
        return $favorites;
    }
    
    /**
     * Add menu item to favorites
     * @param string $menu_id
     * @param string $route
     * @param string $display_name
     * @return bool
     */
    public function addToFavorites($menu_id, $route, $display_name) {
        try {
            // Check if already exists
            $check_query = $this->db->query("
                SELECT favorite_id 
                FROM " . DB_PREFIX . "user_menu_favorites 
                WHERE user_id = '" . (int)$this->user->getId() . "' 
                AND menu_id = '" . $this->db->escape($menu_id) . "'
            ");
            
            if ($check_query->num_rows) {
                return false; // Already exists
            }
            
            // Get next sort order
            $sort_query = $this->db->query("
                SELECT MAX(sort_order) as max_sort 
                FROM " . DB_PREFIX . "user_menu_favorites 
                WHERE user_id = '" . (int)$this->user->getId() . "'
            ");
            
            $sort_order = $sort_query->num_rows ? (int)$sort_query->row['max_sort'] + 1 : 1;
            
            // Insert new favorite
            $this->db->query("
                INSERT INTO " . DB_PREFIX . "user_menu_favorites 
                (user_id, menu_id, route, display_name, sort_order, date_added) 
                VALUES (
                    '" . (int)$this->user->getId() . "',
                    '" . $this->db->escape($menu_id) . "',
                    '" . $this->db->escape($route) . "',
                    '" . $this->db->escape($display_name) . "',
                    '" . (int)$sort_order . "',
                    NOW()
                )
            ");
            
            return true;
            
        } catch (Exception $e) {
            error_log('Add to Favorites Error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Remove menu item from favorites
     * @param string $menu_id
     * @return bool
     */
    public function removeFromFavorites($menu_id) {
        try {
            $this->db->query("
                DELETE FROM " . DB_PREFIX . "user_menu_favorites 
                WHERE user_id = '" . (int)$this->user->getId() . "' 
                AND menu_id = '" . $this->db->escape($menu_id) . "'
            ");
            
            return true;
            
        } catch (Exception $e) {
            error_log('Remove from Favorites Error: ' . $e->getMessage());
            return false;
        }
    }
}
