{{ header }}
{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="button" data-bs-toggle="tooltip" title="{{ button_filter }}" onclick="$('#filter-stocktake').toggleClass('d-none');" class="btn btn-light d-md-none d-lg-none"><i class="fas fa-filter"></i></button>
        <a href="{{ add }}" data-bs-toggle="tooltip" title="{{ button_add }}" class="btn btn-primary"><i class="fas fa-plus"></i></a>
        <button type="submit" form="form-stocktake" formaction="{{ delete }}" data-bs-toggle="tooltip" title="{{ button_delete }}" onclick="return confirm('{{ text_confirm_delete }}');" class="btn btn-danger"><i class="fas fa-trash-alt"></i></button>
        <a href="{{ export }}" data-bs-toggle="tooltip" title="{{ button_export }}" class="btn btn-success"><i class="fas fa-file-excel"></i></a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ol class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
          <li class="breadcrumb-item"><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{ error_warning }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {% endif %}
    {% if success %}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{ success }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {% endif %}
    <div class="row">
      <div id="filter-stocktake" class="col-lg-3 col-md-12 order-lg-last d-none d-lg-block mb-3">
        <div class="card">
          <div class="card-header"><i class="fas fa-filter"></i> {{ button_filter }}</div>
          <div class="card-body">
            <div class="mb-3">
              <label for="input-reference" class="form-label">{{ entry_reference }}</label>
              <input type="text" name="filter_reference" value="{{ filter_reference }}" placeholder="{{ entry_reference }}" id="input-reference" class="form-control"/>
            </div>
            <div class="mb-3">
              <label for="input-branch" class="form-label">{{ entry_branch }}</label>
              <select name="filter_branch" id="input-branch" class="form-select">
                <option value="">{{ text_all_status }}</option>
                {% for branch in branches %}
                  <option value="{{ branch.branch_id }}" {% if branch.branch_id == filter_branch %}selected{% endif %}>{{ branch.name }}</option>
                {% endfor %}
              </select>
            </div>
            <div class="mb-3">
              <label for="input-date-from" class="form-label">{{ entry_date_from }}</label>
              <input type="date" name="filter_date_from" value="{{ filter_date_from }}" id="input-date-from" class="form-control"/>
            </div>
            <div class="mb-3">
              <label for="input-date-to" class="form-label">{{ entry_date_to }}</label>
              <input type="date" name="filter_date_to" value="{{ filter_date_to }}" id="input-date-to" class="form-control"/>
            </div>
            <div class="mb-3">
              <label for="input-status" class="form-label">{{ entry_status }}</label>
              <select name="filter_status" id="input-status" class="form-select">
                {% for key, value in stocktake_statuses %}
                  <option value="{{ key }}" {% if key == filter_status %}selected{% endif %}>{{ value }}</option>
                {% endfor %}
              </select>
            </div>
            <div class="mb-3">
              <label for="input-type" class="form-label">{{ entry_type }}</label>
              <select name="filter_type" id="input-type" class="form-select">
                {% for key, value in stocktake_types %}
                  <option value="{{ key }}" {% if key == filter_type %}selected{% endif %}>{{ value }}</option>
                {% endfor %}
              </select>
            </div>
            <div class="text-end">
              <button type="button" id="button-filter" class="btn btn-primary"><i class="fas fa-filter"></i> {{ button_filter }}</button>
            </div>
          </div>
        </div>
      </div>
      <div class="col-lg-9 col-md-12">
        <div class="card">
          <div class="card-header"><i class="fas fa-list"></i> {{ text_list }}</div>
          <div class="card-body">
            <form id="form-stocktake" method="post">
              <div class="table-responsive">
                <table class="table table-bordered table-hover">
                  <thead>
                    <tr>
                      <td style="width: 1px;" class="text-center"><input type="checkbox" onclick="$('input[name*=\'selected\']').prop('checked', $(this).prop('checked'));" class="form-check-input"/></td>
                      <td class="text-start"><a href="{{ sort_reference }}" {% if sort == 's.reference' %}class="{{ order|lower }}"{% endif %}>{{ column_reference }}</a></td>
                      <td class="text-start"><a href="{{ sort_branch }}" {% if sort == 'b.name' %}class="{{ order|lower }}"{% endif %}>{{ column_branch }}</a></td>
                      <td class="text-start"><a href="{{ sort_date }}" {% if sort == 's.stocktake_date' %}class="{{ order|lower }}"{% endif %}>{{ column_date }}</a></td>
                      <td class="text-start"><a href="{{ sort_type }}" {% if sort == 's.type' %}class="{{ order|lower }}"{% endif %}>{{ column_type }}</a></td>
                      <td class="text-center">{{ column_total_items }}</td>
                      <td class="text-start"><a href="{{ sort_status }}" {% if sort == 's.status' %}class="{{ order|lower }}"{% endif %}>{{ column_status }}</a></td>
                      <td class="text-start"><a href="{{ sort_date_added }}" {% if sort == 's.date_added' %}class="{{ order|lower }}"{% endif %}>{{ column_date_added }}</a></td>
                      <td class="text-end">{{ column_action }}</td>
                    </tr>
                  </thead>
                  <tbody>
                    {% if stocktakes %}
                      {% for stocktake in stocktakes %}
                        <tr>
                          <td class="text-center"><input type="checkbox" name="selected[]" value="{{ stocktake.stocktake_id }}" class="form-check-input"/></td>
                          <td class="text-start">{{ stocktake.reference }}</td>
                          <td class="text-start">{{ stocktake.branch_name }}</td>
                          <td class="text-start">{{ stocktake.stocktake_date }}</td>
                          <td class="text-start">{{ stocktake.type_text }}</td>
                          <td class="text-center">{{ stocktake.total_items }}</td>
                          <td class="text-start">
                            {% if stocktake.status == 'draft' %}
                              <span class="badge bg-secondary">{{ stocktake.status_text }}</span>
                            {% elseif stocktake.status == 'in_progress' %}
                              <span class="badge bg-primary">{{ stocktake.status_text }}</span>
                            {% elseif stocktake.status == 'completed' %}
                              <span class="badge bg-success">{{ stocktake.status_text }}</span>
                            {% elseif stocktake.status == 'cancelled' %}
                              <span class="badge bg-danger">{{ stocktake.status_text }}</span>
                            {% endif %}
                          </td>
                          <td class="text-start">{{ stocktake.date_added }}</td>
                          <td class="text-end">
                            <div class="btn-group">
                              <a href="{{ stocktake.view }}" data-bs-toggle="tooltip" title="{{ button_view }}" class="btn btn-info"><i class="fas fa-eye"></i></a>
                              {% if stocktake.status == 'draft' or stocktake.status == 'in_progress' %}
                                <a href="{{ stocktake.edit }}" data-bs-toggle="tooltip" title="{{ button_edit }}" class="btn btn-primary"><i class="fas fa-pencil-alt"></i></a>
                              {% endif %}
                              <a href="{{ stocktake.print }}" data-bs-toggle="tooltip" title="{{ button_print }}" class="btn btn-success" target="_blank"><i class="fas fa-print"></i></a>
                              {% if stocktake.status == 'in_progress' %}
                                <a href="{{ stocktake.complete }}" data-bs-toggle="tooltip" title="{{ button_complete }}" class="btn btn-warning" onclick="return confirm('{{ text_confirm_complete }}');"><i class="fas fa-check"></i></a>
                              {% endif %}
                              {% if stocktake.status == 'draft' or stocktake.status == 'in_progress' %}
                                <a href="{{ stocktake.cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-danger" onclick="return confirm('{{ text_confirm_cancel }}');"><i class="fas fa-ban"></i></a>
                              {% endif %}
                            </div>
                          </td>
                        </tr>
                      {% endfor %}
                    {% else %}
                      <tr>
                        <td class="text-center" colspan="9">{{ text_no_results }}</td>
                      </tr>
                    {% endif %}
                  </tbody>
                </table>
              </div>
            </form>
            <div class="row">
              <div class="col-sm-6 text-start">{{ pagination }}</div>
              <div class="col-sm-6 text-end">{{ results }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<script type="text/javascript">
$('#button-filter').on('click', function() {
	var url = 'index.php?route=inventory/stocktake&user_token={{ user_token }}';

	var filter_reference = $('#input-reference').val();
	if (filter_reference) {
		url += '&filter_reference=' + encodeURIComponent(filter_reference);
	}

	var filter_branch = $('#input-branch').val();
	if (filter_branch) {
		url += '&filter_branch=' + encodeURIComponent(filter_branch);
	}

	var filter_date_from = $('#input-date-from').val();
	if (filter_date_from) {
		url += '&filter_date_from=' + encodeURIComponent(filter_date_from);
	}

	var filter_date_to = $('#input-date-to').val();
	if (filter_date_to) {
		url += '&filter_date_to=' + encodeURIComponent(filter_date_to);
	}

	var filter_status = $('#input-status').val();
	if (filter_status) {
		url += '&filter_status=' + encodeURIComponent(filter_status);
	}

	var filter_type = $('#input-type').val();
	if (filter_type) {
		url += '&filter_type=' + encodeURIComponent(filter_type);
	}

	location = url;
});
</script>
{{ footer }}
