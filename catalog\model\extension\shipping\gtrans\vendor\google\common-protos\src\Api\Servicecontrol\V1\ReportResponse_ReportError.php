<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/servicecontrol/v1/service_controller.proto

namespace Google\Api\Servicecontrol\V1;

if (false) {
    /**
     * This class is deprecated. Use Google\Api\Servicecontrol\V1\ReportResponse\ReportError instead.
     * @deprecated
     */
    class ReportResponse_ReportError {}
}
class_exists(ReportResponse\ReportError::class);
@trigger_error('Google\Api\Servicecontrol\V1\ReportResponse_ReportError is deprecated and will be removed in the next major release. Use Google\Api\Servicecontrol\V1\ReportResponse\ReportError instead', E_USER_DEPRECATED);

