<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/resource.proto

namespace Google\Api;

if (false) {
    /**
     * This class is deprecated. Use Google\Api\ResourceDescriptor\History instead.
     * @deprecated
     */
    class ResourceDescriptor_History {}
}
class_exists(ResourceDescriptor\History::class);
@trigger_error('Google\Api\ResourceDescriptor_History is deprecated and will be removed in the next major release. Use Google\Api\ResourceDescriptor\History instead', E_USER_DEPRECATED);

