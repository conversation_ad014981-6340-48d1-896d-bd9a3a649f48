/**
 * AYM ERP Dashboard Widgets JavaScript
 * ملف JavaScript للتفاعل مع الـ Widgets المتقدمة
 * 
 * يحتوي على:
 * - تحميل البيانات عبر Ajax
 * - محرك التخصيص وحفظ التخطيط
 * - محرك الفلاتر الشامل
 * - التفاعل العميق (Drill-Down)
 * - المساعد الذكي المدمج
 */

class DashboardWidgets {
    constructor() {
        this.widgetsData = {};
        this.currentFilters = {
            date_range: 'month',
            branch_id: null,
            channel: null
        };
        this.charts = {};
        this.isLoading = false;
        
        this.init();
    }
    
    /**
     * Initialize Dashboard Widgets
     * تهيئة مكونات لوحة المعلومات
     */
    init() {
        this.bindEvents();
        this.loadUserLayout();
        this.loadWidgetsData();
        this.initGlobalFilters();
        this.initCommandBar();
    }
    
    /**
     * Bind Event Listeners
     * ربط مستمعي الأحداث
     */
    bindEvents() {
        // Refresh button
        $('#refresh-dashboard').on('click', () => {
            this.loadWidgetsData();
        });
        
        // Export button
        $('#export-dashboard').on('click', () => {
            this.exportDashboard();
        });
        
        // Print button
        $('#print-dashboard').on('click', () => {
            this.printDashboard();
        });
        
        // Global filters
        $('#global-filters select, #global-filters input').on('change', () => {
            this.updateFilters();
        });
        
        // Widget customization
        this.initWidgetCustomization();
        
        // Drill-down events
        this.initDrillDownEvents();
    }
    
    /**
     * Load Widgets Data via Ajax
     * تحميل بيانات الـ Widgets عبر Ajax
     */
    loadWidgetsData() {
        if (this.isLoading) return;
        
        this.isLoading = true;
        this.showLoadingIndicator();
        
        const url = 'index.php?route=common/dashboard_widgets/loadWidgets&user_token=' + user_token;
        const data = this.currentFilters;
        
        $.ajax({
            url: url,
            type: 'POST',
            data: data,
            dataType: 'json',
            success: (response) => {
                if (response.success) {
                    this.widgetsData = response.data;
                    this.renderWidgets();
                    this.updateTimestamp();
                } else {
                    this.showError('فشل في تحميل البيانات: ' + response.error);
                }
            },
            error: (xhr, status, error) => {
                this.showError('خطأ في الاتصال: ' + error);
            },
            complete: () => {
                this.isLoading = false;
                this.hideLoadingIndicator();
            }
        });
    }
    
    /**
     * Render All Widgets
     * عرض جميع الـ Widgets
     */
    renderWidgets() {
        // Render Executive Widgets
        this.renderGlobalKPIs();
        this.renderProfitLossSummary();
        this.renderSalesByChannel();
        this.renderCompanyHealthGauges();
        this.renderSalesHeatmap();
        this.renderGoalTracking();
        this.renderHighValueApprovals();
        
        // Render Sales Widgets
        this.renderEcommerceFunnel();
        this.renderOmnichannelSales();
        this.renderPOSShiftSummary();
        this.renderAbandonedCarts();
        this.renderTopProducts();
        this.renderTopCategories();
        this.renderTopBrands();
        this.renderSalesTeamLeaderboard();
        this.renderOrderStatusOverview();
        
        // Render Inventory Widgets
        this.renderInventoryKPIs();
        this.renderStockByLocation();
        this.renderStockLevelAlerts();
        this.renderAgingStock();
        this.renderExpiringProducts();
        this.renderFulfillmentEfficiency();
        this.renderWarehouseQueue();
        this.renderStockCountVariance();
        
        // Render Finance Widgets
        this.renderCashBankBalances();
        this.renderCashFlowForecast();
        this.renderARAging();
        this.renderExpenseVsBudget();
        this.renderProfitabilityByDimension();
        this.renderETAStatus();
        this.renderPendingReconciliations();
        this.renderVATSummary();
    }
    
    /**
     * Render Global KPIs Widget
     * عرض مكون المؤشرات الرئيسية العالمية
     */
    renderGlobalKPIs() {
        if (!this.widgetsData.global_kpis) return;
        
        const data = this.widgetsData.global_kpis;
        
        // Update KPI cards
        $('#global-kpis .total-revenue').text(this.formatCurrency(data.total_revenue));
        $('#global-kpis .net-profit').text(this.formatCurrency(data.net_profit));
        $('#global-kpis .inventory-value').text(this.formatCurrency(data.inventory_value));
        $('#global-kpis .cash-flow').text(this.formatCurrency(data.cash_flow));
        $('#global-kpis .total-orders').text(this.formatNumber(data.total_orders));
        $('#global-kpis .total-customers').text(this.formatNumber(data.total_customers));
        $('#global-kpis .avg-order-value').text(this.formatCurrency(data.avg_order_value));
        $('#global-kpis .conversion-rate').text(this.formatPercentage(data.conversion_rate));
        
        // Update trend indicators
        this.updateTrendIndicator('#global-kpis .revenue-trend', data.revenue_trend);
        this.updateTrendIndicator('#global-kpis .profit-trend', data.profit_trend);
    }
    
    /**
     * Render Profit & Loss Summary Widget
     * عرض مكون ملخص الأرباح والخسائر
     */
    renderProfitLossSummary() {
        if (!this.widgetsData.pnl_summary) return;
        
        const data = this.widgetsData.pnl_summary;
        
        // Update P&L values
        $('#pnl-summary .revenue').text(this.formatCurrency(data.revenue));
        $('#pnl-summary .cost-of-goods').text(this.formatCurrency(data.cost_of_goods));
        $('#pnl-summary .gross-profit').text(this.formatCurrency(data.gross_profit));
        $('#pnl-summary .operating-expenses').text(this.formatCurrency(data.operating_expenses));
        $('#pnl-summary .net-profit').text(this.formatCurrency(data.net_profit));
        $('#pnl-summary .gross-margin').text(this.formatPercentage(data.gross_margin));
        $('#pnl-summary .net-margin').text(this.formatPercentage(data.net_margin));
        
        // Create P&L chart
        this.createPNLChart(data);
    }
    
    /**
     * Render Sales by Channel Widget
     * عرض مكون المبيعات حسب القناة
     */
    renderSalesByChannel() {
        if (!this.widgetsData.sales_by_channel) return;
        
        const data = this.widgetsData.sales_by_channel;
        
        // Update channel table
        const tbody = $('#sales-by-channel tbody');
        tbody.empty();
        
        data.forEach(channel => {
            const row = `
                <tr>
                    <td>${channel.channel}</td>
                    <td>${this.formatCurrency(channel.total_sales)}</td>
                    <td>${this.formatNumber(channel.order_count)}</td>
                    <td>${this.formatCurrency(channel.avg_order_value)}</td>
                    <td>${this.formatNumber(channel.unique_customers)}</td>
                </tr>
            `;
            tbody.append(row);
        });
        
        // Create pie chart
        this.createSalesByChannelChart(data);
    }
    
    /**
     * Render Company Health Gauges Widget
     * عرض مكون مؤشرات صحة الشركة
     */
    renderCompanyHealthGauges() {
        if (!this.widgetsData.company_health_gauges) return;
        
        const data = this.widgetsData.company_health_gauges;
        
        // Update gauge values
        $('#company-health .current-ratio').text(data.current_ratio.toFixed(2));
        $('#company-health .net-profit-margin').text(this.formatPercentage(data.net_profit_margin));
        $('#company-health .inventory-turnover').text(data.inventory_turnover.toFixed(1));
        $('#company-health .customer-satisfaction').text(data.customer_satisfaction.toFixed(1));
        $('#company-health .overall-health-score').text(data.overall_health_score.toFixed(1));
        
        // Create gauge charts
        this.createHealthGaugeCharts(data);
    }
    
    /**
     * Render E-commerce Funnel Widget
     * عرض مكون قمع التجارة الإلكترونية
     */
    renderEcommerceFunnel() {
        if (!this.widgetsData.ecommerce_funnel) return;
        
        const data = this.widgetsData.ecommerce_funnel;
        
        // Update funnel values
        $('#ecommerce-funnel .visitors').text(this.formatNumber(data.visitors));
        $('#ecommerce-funnel .cart-additions').text(this.formatNumber(data.cart_additions));
        $('#ecommerce-funnel .completed-purchases').text(this.formatNumber(data.completed_purchases));
        $('#ecommerce-funnel .cart-conversion-rate').text(this.formatPercentage(data.cart_conversion_rate));
        $('#ecommerce-funnel .purchase-conversion-rate').text(this.formatPercentage(data.purchase_conversion_rate));
        $('#ecommerce-funnel .overall-conversion-rate').text(this.formatPercentage(data.overall_conversion_rate));
        
        // Create funnel chart
        this.createEcommerceFunnelChart(data);
    }
    
    /**
     * Render Inventory KPIs Widget
     * عرض مكون مؤشرات أداء المخزون
     */
    renderInventoryKPIs() {
        if (!this.widgetsData.inventory_kpis) return;
        
        const data = this.widgetsData.inventory_kpis;
        
        // Update inventory values
        $('#inventory-kpis .total-inventory-value').text(this.formatCurrency(data.total_inventory_value));
        $('#inventory-kpis .total-sku-count').text(this.formatNumber(data.total_sku_count));
        $('#inventory-kpis .avg-inventory-turnover').text(data.avg_inventory_turnover.toFixed(1));
        $('#inventory-kpis .inventory-accuracy').text(this.formatPercentage(data.inventory_accuracy));
        $('#inventory-kpis .stockout-rate').text(this.formatPercentage(data.stockout_rate));
        $('#inventory-kpis .overstock-rate').text(this.formatPercentage(data.overstock_rate));
        $('#inventory-kpis .dead-stock-value').text(this.formatCurrency(data.dead_stock_value));
        $('#inventory-kpis .low-stock-items').text(this.formatNumber(data.low_stock_items));
        
        // Create inventory charts
        this.createInventoryCharts(data);
    }
    
    /**
     * Render Stock Level Alerts Widget
     * عرض مكون تنبيهات مستوى المخزون
     */
    renderStockLevelAlerts() {
        if (!this.widgetsData.stock_level_alerts) return;
        
        const data = this.widgetsData.stock_level_alerts;
        
        // Update alerts table
        const tbody = $('#stock-level-alerts tbody');
        tbody.empty();
        
        data.forEach(item => {
            const statusClass = this.getStockStatusClass(item.stock_status);
            const row = `
                <tr class="${statusClass}">
                    <td>${item.name}</td>
                    <td>${item.model}</td>
                    <td>${this.formatNumber(item.quantity)}</td>
                    <td>${this.formatNumber(item.reorder_point)}</td>
                    <td>${this.formatCurrency(item.inventory_value)}</td>
                    <td><span class="badge badge-${this.getStockStatusBadge(item.stock_status)}">${this.getStockStatusText(item.stock_status)}</span></td>
                    <td>${item.days_since_update} يوم</td>
                </tr>
            `;
            tbody.append(row);
        });
        
        // Update alert count
        $('#stock-alerts-count').text(data.length);
    }
    
    /**
     * Render Cash & Bank Balances Widget
     * عرض مكون أرصدة النقد والبنوك
     */
    renderCashBankBalances() {
        if (!this.widgetsData.cash_bank_balances) return;
        
        const data = this.widgetsData.cash_bank_balances;
        
        // Update balance values
        $('#cash-bank-balances .total-cash-balance').text(this.formatCurrency(data.total_cash_balance));
        $('#cash-bank-balances .total-bank-balance').text(this.formatCurrency(data.total_bank_balance));
        $('#cash-bank-balances .total-balance').text(this.formatCurrency(data.total_balance));
        
        // Update accounts list
        const tbody = $('#cash-bank-accounts tbody');
        tbody.empty();
        
        data.accounts_list.forEach(account => {
            const row = `
                <tr>
                    <td>${account.account_name}</td>
                    <td>${account.account_number}</td>
                    <td>${this.getAccountTypeText(account.account_type)}</td>
                    <td>${this.formatCurrency(account.current_balance)}</td>
                    <td>${account.currency_code}</td>
                    <td>${account.bank_name || '-'}</td>
                </tr>
            `;
            tbody.append(row);
        });
        
        // Create balance chart
        this.createCashBankChart(data);
    }
    
    /**
     * Create Charts
     * إنشاء المخططات البيانية
     */
    createPNLChart(data) {
        const ctx = document.getElementById('pnl-chart');
        if (!ctx) return;
        
        if (this.charts.pnl) {
            this.charts.pnl.destroy();
        }
        
        this.charts.pnl = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: ['الإيرادات', 'تكلفة البضاعة', 'الربح الإجمالي', 'المصروفات التشغيلية', 'صافي الربح'],
                datasets: [{
                    label: 'القيمة',
                    data: [data.revenue, data.cost_of_goods, data.gross_profit, data.operating_expenses, data.net_profit],
                    backgroundColor: ['#28a745', '#dc3545', '#17a2b8', '#ffc107', '#6f42c1'],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }
    
    createSalesByChannelChart(data) {
        const ctx = document.getElementById('sales-by-channel-chart');
        if (!ctx) return;
        
        if (this.charts.salesByChannel) {
            this.charts.salesByChannel.destroy();
        }
        
        const chartData = {
            labels: data.map(item => item.channel),
            datasets: [{
                data: data.map(item => item.total_sales),
                backgroundColor: ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF'],
                borderWidth: 2
            }]
        };
        
        this.charts.salesByChannel = new Chart(ctx, {
            type: 'doughnut',
            data: chartData,
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }
    
    createHealthGaugeCharts(data) {
        // Create gauge charts for health indicators
        this.createGaugeChart('current-ratio-gauge', data.current_ratio, 5, 'النسبة الحالية');
        this.createGaugeChart('profit-margin-gauge', data.net_profit_margin, 100, 'هامش الربح %');
        this.createGaugeChart('turnover-gauge', data.inventory_turnover, 20, 'دوران المخزون');
        this.createGaugeChart('satisfaction-gauge', data.customer_satisfaction, 5, 'رضا العملاء');
    }
    
    createGaugeChart(elementId, value, max, label) {
        const element = document.getElementById(elementId);
        if (!element) return;
        
        const percentage = (value / max) * 100;
        const color = this.getGaugeColor(percentage);
        
        element.innerHTML = `
            <div class="gauge-container">
                <div class="gauge-value">${value.toFixed(1)}</div>
                <div class="gauge-label">${label}</div>
                <div class="gauge-bar">
                    <div class="gauge-fill" style="width: ${percentage}%; background-color: ${color};"></div>
                </div>
            </div>
        `;
    }
    
    createEcommerceFunnelChart(data) {
        const ctx = document.getElementById('ecommerce-funnel-chart');
        if (!ctx) return;
        
        if (this.charts.ecommerceFunnel) {
            this.charts.ecommerceFunnel.destroy();
        }
        
        this.charts.ecommerceFunnel = new Chart(ctx, {
            type: 'funnel',
            data: {
                labels: ['الزوار', 'الإضافات للسلة', 'المشتريات المكتملة'],
                datasets: [{
                    data: [data.visitors, data.cart_additions, data.completed_purchases],
                    backgroundColor: ['#FF6384', '#36A2EB', '#4BC0C0']
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }
    
    createInventoryCharts(data) {
        // Create inventory distribution chart
        this.createInventoryDistributionChart(data);
        
        // Create stock level chart
        this.createStockLevelChart(data);
    }
    
    createCashBankChart(data) {
        const ctx = document.getElementById('cash-bank-chart');
        if (!ctx) return;
        
        if (this.charts.cashBank) {
            this.charts.cashBank.destroy();
        }
        
        this.charts.cashBank = new Chart(ctx, {
            type: 'pie',
            data: {
                labels: ['النقد', 'البنوك'],
                datasets: [{
                    data: [data.total_cash_balance, data.total_bank_balance],
                    backgroundColor: ['#28a745', '#17a2b8']
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }
    
    /**
     * Widget Customization
     * تخصيص الـ Widgets
     */
    initWidgetCustomization() {
        // Make widgets draggable
        $('.dashboard-widget').draggable({
            handle: '.widget-header',
            containment: '.dashboard-container',
            stop: () => {
                this.saveLayout();
            }
        });
        
        // Make widgets resizable
        $('.dashboard-widget').resizable({
            handles: 'all',
            minHeight: 200,
            minWidth: 300,
            stop: () => {
                this.saveLayout();
            }
        });
        
        // Add widget button
        $('#add-widget-btn').on('click', () => {
            this.showWidgetSelector();
        });
    }
    
    /**
     * Show Widget Selector
     * عرض محدد الـ Widgets
     */
    showWidgetSelector() {
        const availableWidgets = this.getAvailableWidgets();
        
        let modal = `
            <div class="modal fade" id="widget-selector" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">إضافة مكون جديد</h5>
                            <button type="button" class="close" data-dismiss="modal">
                                <span>&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
        `;
        
        availableWidgets.forEach(widget => {
            modal += `
                <div class="col-md-6 mb-3">
                    <div class="widget-option" data-widget="${widget.key}">
                        <div class="widget-option-header">
                            <i class="${widget.icon}"></i>
                            <h6>${widget.name}</h6>
                        </div>
                        <p class="text-muted">${widget.description}</p>
                    </div>
                </div>
            `;
        });
        
        modal += `
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        $('body').append(modal);
        $('#widget-selector').modal('show');
        
        // Handle widget selection
        $('.widget-option').on('click', (e) => {
            const widgetKey = $(e.currentTarget).data('widget');
            this.addWidget(widgetKey);
            $('#widget-selector').modal('hide');
        });
    }
    
    /**
     * Add Widget
     * إضافة مكون جديد
     */
    addWidget(widgetKey) {
        const widget = this.getWidgetTemplate(widgetKey);
        if (!widget) return;
        
        const widgetHtml = `
            <div class="dashboard-widget" data-widget="${widgetKey}">
                <div class="widget-header">
                    <h5><i class="${widget.icon}"></i> ${widget.name}</h5>
                    <div class="widget-controls">
                        <button class="btn btn-sm btn-outline-secondary refresh-widget" title="تحديث">
                            <i class="fa fa-refresh"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger remove-widget" title="إزالة">
                            <i class="fa fa-times"></i>
                        </button>
                    </div>
                </div>
                <div class="widget-body">
                    ${widget.content}
                </div>
            </div>
        `;
        
        $('.dashboard-container').append(widgetHtml);
        this.initWidgetEvents(widgetKey);
        this.saveLayout();
    }
    
    /**
     * Save Layout
     * حفظ التخطيط
     */
    saveLayout() {
        const layout = this.getCurrentLayout();
        
        $.ajax({
            url: 'index.php?route=common/dashboard_widgets/updateLayout&user_token=' + user_token,
            type: 'POST',
            data: { layout: layout },
            dataType: 'json',
            success: (response) => {
                if (response.success) {
                    this.showSuccess('تم حفظ التخطيط بنجاح');
                } else {
                    this.showError('فشل في حفظ التخطيط: ' + response.error);
                }
            }
        });
    }
    
    /**
     * Load User Layout
     * تحميل تخطيط المستخدم
     */
    loadUserLayout() {
        $.ajax({
            url: 'index.php?route=common/dashboard_widgets/getUserLayout&user_token=' + user_token,
            type: 'GET',
            dataType: 'json',
            success: (response) => {
                if (response && response.length > 0) {
                    this.applyLayout(response);
                }
            }
        });
    }
    
    /**
     * Global Filters
     * الفلاتر الشاملة
     */
    initGlobalFilters() {
        // Date range filter
        $('#date-range-filter').on('change', () => {
            this.currentFilters.date_range = $('#date-range-filter').val();
            this.loadWidgetsData();
        });
        
        // Branch filter
        $('#branch-filter').on('change', () => {
            this.currentFilters.branch_id = $('#branch-filter').val();
            this.loadWidgetsData();
        });
        
        // Channel filter
        $('#channel-filter').on('change', () => {
            this.currentFilters.channel = $('#channel-filter').val();
            this.loadWidgetsData();
        });
    }
    
    /**
     * Drill-Down Events
     * أحداث التفاعل العميق
     */
    initDrillDownEvents() {
        // Chart click events for drill-down
        $(document).on('click', '.chart-container canvas', (e) => {
            const chart = Chart.getChart(e.target);
            if (chart) {
                const activeElements = chart.getActiveElements();
                if (activeElements.length > 0) {
                    const dataIndex = activeElements[0].index;
                    const label = chart.data.labels[dataIndex];
                    this.handleDrillDown(chart.config.type, label, dataIndex);
                }
            }
        });
        
        // Table row click events
        $(document).on('click', '.widget-table tbody tr', (e) => {
            const row = $(e.currentTarget);
            const data = row.data();
            if (data) {
                this.handleTableDrillDown(data);
            }
        });
    }
    
    /**
     * Handle Drill-Down
     * معالجة التفاعل العميق
     */
    handleDrillDown(chartType, label, dataIndex) {
        // Add filter based on clicked element
        this.addDrillDownFilter(chartType, label);
        
        // Reload data with new filter
        this.loadWidgetsData();
        
        // Show drill-down indicator
        this.showDrillDownIndicator(label);
    }
    
    /**
     * Command Bar (AI Copilot)
     * شريط الأوامر (المساعد الذكي)
     */
    initCommandBar() {
        const commandBar = `
            <div class="command-bar">
                <div class="input-group">
                    <input type="text" class="form-control" id="command-input" placeholder="اكتب استفسارك هنا... مثال: صافي ربح الشهر الماضي">
                    <div class="input-group-append">
                        <button class="btn btn-primary" id="execute-command">
                            <i class="fa fa-search"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        $('.dashboard-header').append(commandBar);
        
        // Handle command execution
        $('#execute-command').on('click', () => {
            this.executeCommand();
        });
        
        $('#command-input').on('keypress', (e) => {
            if (e.which === 13) {
                this.executeCommand();
            }
        });
    }
    
    /**
     * Execute Command
     * تنفيذ الأمر
     */
    executeCommand() {
        const command = $('#command-input').val().trim();
        if (!command) return;
        
        this.showLoadingIndicator();
        
        $.ajax({
            url: 'index.php?route=ai/query&user_token=' + user_token,
            type: 'POST',
            data: { query: command },
            dataType: 'json',
            success: (response) => {
                if (response.success) {
                    this.showCommandResult(response.data);
                } else {
                    this.showError('فشل في تنفيذ الأمر: ' + response.error);
                }
            },
            error: () => {
                this.showError('خطأ في الاتصال');
            },
            complete: () => {
                this.hideLoadingIndicator();
            }
        });
    }
    
    /**
     * Show Command Result
     * عرض نتيجة الأمر
     */
    showCommandResult(data) {
        const modal = `
            <div class="modal fade" id="command-result" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">نتيجة الاستفسار</h5>
                            <button type="button" class="close" data-dismiss="modal">
                                <span>&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <div class="command-result">
                                <h6>${data.question}</h6>
                                <div class="result-value">${data.answer}</div>
                                <div class="result-chart" id="command-chart"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        $('body').append(modal);
        $('#command-result').modal('show');
        
        // Create chart if data available
        if (data.chart_data) {
            this.createCommandChart(data.chart_data);
        }
    }
    
    /**
     * Utility Functions
     * الدوال المساعدة
     */
    formatCurrency(value) {
        return new Intl.NumberFormat('ar-EG', {
            style: 'currency',
            currency: 'EGP'
        }).format(value);
    }
    
    formatNumber(value) {
        return new Intl.NumberFormat('ar-EG').format(value);
    }
    
    formatPercentage(value) {
        return value.toFixed(2) + '%';
    }
    
    getStockStatusClass(status) {
        switch(status) {
            case 'out_of_stock': return 'table-danger';
            case 'low_stock': return 'table-warning';
            case 'near_minimum': return 'table-info';
            default: return '';
        }
    }
    
    getStockStatusBadge(status) {
        switch(status) {
            case 'out_of_stock': return 'danger';
            case 'low_stock': return 'warning';
            case 'near_minimum': return 'info';
            default: return 'success';
        }
    }
    
    getStockStatusText(status) {
        switch(status) {
            case 'out_of_stock': return 'نفذ من المخزون';
            case 'low_stock': return 'مخزون منخفض';
            case 'near_minimum': return 'قريب من الحد الأدنى';
            default: return 'عادي';
        }
    }
    
    getAccountTypeText(type) {
        switch(type) {
            case 'cash': return 'نقد';
            case 'bank': return 'بنك';
            default: return type;
        }
    }
    
    getGaugeColor(percentage) {
        if (percentage >= 80) return '#28a745';
        if (percentage >= 60) return '#17a2b8';
        if (percentage >= 40) return '#ffc107';
        return '#dc3545';
    }
    
    showLoadingIndicator() {
        $('.dashboard-container').append('<div class="loading-overlay"><i class="fa fa-spinner fa-spin"></i></div>');
    }
    
    hideLoadingIndicator() {
        $('.loading-overlay').remove();
    }
    
    showSuccess(message) {
        // Show success notification
        this.showNotification(message, 'success');
    }
    
    showError(message) {
        // Show error notification
        this.showNotification(message, 'error');
    }
    
    showNotification(message, type) {
        const notification = `
            <div class="alert alert-${type === 'error' ? 'danger' : 'success'} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="close" data-dismiss="alert">
                    <span>&times;</span>
                </button>
            </div>
        `;
        
        $('.dashboard-header').after(notification);
        
        setTimeout(() => {
            $('.alert').fadeOut();
        }, 5000);
    }
    
    updateTimestamp() {
        $('#dashboard-timestamp').text(new Date().toLocaleString('ar-EG'));
    }
    
    exportDashboard() {
        // Export dashboard data
        const data = {
            widgets: this.widgetsData,
            filters: this.currentFilters,
            timestamp: new Date().toISOString()
        };
        
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = 'dashboard-export-' + new Date().toISOString().split('T')[0] + '.json';
        a.click();
        
        URL.revokeObjectURL(url);
    }
    
    printDashboard() {
        window.print();
    }
}

// Initialize Dashboard Widgets when document is ready
$(document).ready(function() {
    window.dashboardWidgets = new DashboardWidgets();
}); 