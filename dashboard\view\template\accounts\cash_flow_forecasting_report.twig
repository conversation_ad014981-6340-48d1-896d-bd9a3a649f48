{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        {% if has_permission %}
        <button type="button" id="button-export-excel" data-toggle="tooltip" title="{{ button_export_excel }}" class="btn btn-success">
          <i class="fa fa-file-excel-o"></i> {{ button_export_excel }}
        </button>
        <button type="button" id="button-export-pdf" data-toggle="tooltip" title="{{ button_export_pdf }}" class="btn btn-danger">
          <i class="fa fa-file-pdf-o"></i> {{ button_export_pdf }}
        </button>
        <button type="button" id="button-print" data-toggle="tooltip" title="{{ button_print }}" class="btn btn-info">
          <i class="fa fa-print"></i> {{ button_print }}
        </button>
        {% endif %}
        <a href="{{ back }}" data-toggle="tooltip" title="{{ button_back }}" class="btn btn-default">
          <i class="fa fa-reply"></i> {{ button_back }}
        </a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    
    <!-- فلاتر التقرير -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-filter"></i> {{ text_report_filters }}</h3>
      </div>
      <div class="panel-body">
        <form action="{{ action }}" method="get" enctype="multipart/form-data" id="form-filter">
          <div class="row">
            <div class="col-md-3">
              <div class="form-group">
                <label class="control-label">{{ text_date_from }}</label>
                <div class="input-group date">
                  <input type="text" name="filter_date_from" value="{{ filter_date_from }}" placeholder="{{ text_date_from }}" data-date-format="YYYY-MM-DD" class="form-control" />
                  <span class="input-group-btn">
                    <button class="btn btn-default" type="button"><i class="fa fa-calendar"></i></button>
                  </span>
                </div>
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <label class="control-label">{{ text_date_to }}</label>
                <div class="input-group date">
                  <input type="text" name="filter_date_to" value="{{ filter_date_to }}" placeholder="{{ text_date_to }}" data-date-format="YYYY-MM-DD" class="form-control" />
                  <span class="input-group-btn">
                    <button class="btn btn-default" type="button"><i class="fa fa-calendar"></i></button>
                  </span>
                </div>
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <label class="control-label">{{ text_forecast_method }}</label>
                <select name="filter_forecast_method" class="form-control">
                  <option value="">{{ text_all_methods }}</option>
                  <option value="moving_average" {% if filter_forecast_method == 'moving_average' %}selected{% endif %}>{{ text_moving_average }}</option>
                  <option value="linear_regression" {% if filter_forecast_method == 'linear_regression' %}selected{% endif %}>{{ text_linear_regression }}</option>
                  <option value="seasonal_decomposition" {% if filter_forecast_method == 'seasonal_decomposition' %}selected{% endif %}>{{ text_seasonal_decomposition }}</option>
                  <option value="ai_prediction" {% if filter_forecast_method == 'ai_prediction' %}selected{% endif %}>{{ text_ai_prediction }}</option>
                </select>
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <label class="control-label">{{ text_department }}</label>
                <select name="filter_department" class="form-control">
                  <option value="">{{ text_all_departments }}</option>
                  {% for department in departments %}
                  <option value="{{ department.department_id }}" {% if department.department_id == filter_department %}selected{% endif %}>{{ department.name }}</option>
                  {% endfor %}
                </select>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-3">
              <div class="form-group">
                <label class="control-label">{{ text_forecast_period }}</label>
                <select name="filter_forecast_period" class="form-control">
                  <option value="">{{ text_all_periods }}</option>
                  <option value="weekly" {% if filter_forecast_period == 'weekly' %}selected{% endif %}>{{ text_weekly }}</option>
                  <option value="monthly" {% if filter_forecast_period == 'monthly' %}selected{% endif %}>{{ text_monthly }}</option>
                  <option value="quarterly" {% if filter_forecast_period == 'quarterly' %}selected{% endif %}>{{ text_quarterly }}</option>
                  <option value="yearly" {% if filter_forecast_period == 'yearly' %}selected{% endif %}>{{ text_yearly }}</option>
                </select>
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <label class="control-label">{{ text_accuracy_threshold }}</label>
                <select name="filter_accuracy_threshold" class="form-control">
                  <option value="">{{ text_all_accuracy_levels }}</option>
                  <option value="90" {% if filter_accuracy_threshold == '90' %}selected{% endif %}>{{ text_above_90_percent }}</option>
                  <option value="70" {% if filter_accuracy_threshold == '70' %}selected{% endif %}>{{ text_above_70_percent }}</option>
                  <option value="50" {% if filter_accuracy_threshold == '50' %}selected{% endif %}>{{ text_above_50_percent }}</option>
                </select>
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <label class="control-label">{{ text_scenario_type }}</label>
                <select name="filter_scenario_type" class="form-control">
                  <option value="">{{ text_all_scenarios }}</option>
                  <option value="optimistic" {% if filter_scenario_type == 'optimistic' %}selected{% endif %}>{{ text_optimistic }}</option>
                  <option value="realistic" {% if filter_scenario_type == 'realistic' %}selected{% endif %}>{{ text_realistic }}</option>
                  <option value="pessimistic" {% if filter_scenario_type == 'pessimistic' %}selected{% endif %}>{{ text_pessimistic }}</option>
                </select>
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <label class="control-label">&nbsp;</label>
                <div>
                  <button type="submit" id="button-filter" class="btn btn-primary">
                    <i class="fa fa-search"></i> {{ button_filter }}
                  </button>
                  <button type="button" id="button-clear" class="btn btn-default">
                    <i class="fa fa-refresh"></i> {{ button_clear }}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>

    <!-- ملخص التقرير -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-bar-chart"></i> {{ text_report_summary }}</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-md-3">
            <div class="info-box bg-aqua">
              <span class="info-box-icon"><i class="fa fa-list"></i></span>
              <div class="info-box-content">
                <span class="info-box-text">{{ text_total_forecasts }}</span>
                <span class="info-box-number">{{ summary.total_forecasts }}</span>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="info-box bg-green">
              <span class="info-box-icon"><i class="fa fa-check"></i></span>
              <div class="info-box-content">
                <span class="info-box-text">{{ text_active_forecasts }}</span>
                <span class="info-box-number">{{ summary.active_forecasts }}</span>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="info-box bg-yellow">
              <span class="info-box-icon"><i class="fa fa-magic"></i></span>
              <div class="info-box-content">
                <span class="info-box-text">{{ text_ai_forecasts }}</span>
                <span class="info-box-number">{{ summary.ai_forecasts }}</span>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="info-box bg-red">
              <span class="info-box-icon"><i class="fa fa-percent"></i></span>
              <div class="info-box-content">
                <span class="info-box-text">{{ text_average_accuracy }}</span>
                <span class="info-box-number">{{ summary.average_accuracy }}%</span>
              </div>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-md-4">
            <div class="info-box bg-purple">
              <span class="info-box-icon"><i class="fa fa-money"></i></span>
              <div class="info-box-content">
                <span class="info-box-text">{{ text_total_projected_inflow }}</span>
                <span class="info-box-number">{{ summary.total_projected_inflow }}</span>
              </div>
            </div>
          </div>
          <div class="col-md-4">
            <div class="info-box bg-orange">
              <span class="info-box-icon"><i class="fa fa-arrow-up"></i></span>
              <div class="info-box-content">
                <span class="info-box-text">{{ text_total_projected_outflow }}</span>
                <span class="info-box-number">{{ summary.total_projected_outflow }}</span>
              </div>
            </div>
          </div>
          <div class="col-md-4">
            <div class="info-box bg-blue">
              <span class="info-box-icon"><i class="fa fa-line-chart"></i></span>
              <div class="info-box-content">
                <span class="info-box-text">{{ text_net_projected_flow }}</span>
                <span class="info-box-number">{{ summary.net_projected_flow }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- الرسم البياني الإجمالي -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-line-chart"></i> {{ text_overall_trend }}</h3>
      </div>
      <div class="panel-body">
        <canvas id="overallTrendChart" width="400" height="100"></canvas>
      </div>
    </div>

    <!-- تفاصيل التنبؤات -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-table"></i> {{ text_forecast_details }}</h3>
      </div>
      <div class="panel-body">
        <div class="table-responsive">
          <table class="table table-bordered table-hover">
            <thead>
              <tr>
                <th>{{ column_forecast_name }}</th>
                <th>{{ column_forecast_period }}</th>
                <th>{{ column_forecast_method }}</th>
                <th>{{ column_department }}</th>
                <th class="text-right">{{ column_projected_inflow }}</th>
                <th class="text-right">{{ column_projected_outflow }}</th>
                <th class="text-right">{{ column_net_flow }}</th>
                <th class="text-right">{{ column_accuracy_score }}</th>
                <th>{{ column_status }}</th>
                <th>{{ column_date_created }}</th>
              </tr>
            </thead>
            <tbody>
              {% for forecast in forecasts %}
              <tr>
                <td>
                  <a href="{{ forecast.view_link }}">{{ forecast.forecast_name }}</a>
                </td>
                <td>{{ forecast.forecast_period }}</td>
                <td>
                  {% if forecast.forecast_method == 'ai_prediction' %}
                    <span class="label label-warning">{{ text_ai_prediction }}</span>
                  {% else %}
                    {{ forecast.forecast_method }}
                  {% endif %}
                </td>
                <td>{{ forecast.department_name }}</td>
                <td class="text-right text-success">{{ forecast.projected_inflow }}</td>
                <td class="text-right text-danger">{{ forecast.projected_outflow }}</td>
                <td class="text-right {% if forecast.net_flow < 0 %}text-danger{% else %}text-success{% endif %}">
                  {{ forecast.net_flow }}
                </td>
                <td class="text-right">
                  {% if forecast.accuracy_score %}
                    <span class="label {% if forecast.accuracy_score >= 90 %}label-success{% elseif forecast.accuracy_score >= 70 %}label-warning{% else %}label-danger{% endif %}">
                      {{ forecast.accuracy_score }}%
                    </span>
                  {% else %}
                    <span class="label label-default">{{ text_no_data }}</span>
                  {% endif %}
                </td>
                <td>
                  {% if forecast.status == 'active' %}
                    <span class="label label-success">{{ text_status_active }}</span>
                  {% elseif forecast.status == 'draft' %}
                    <span class="label label-default">{{ text_status_draft }}</span>
                  {% else %}
                    <span class="label label-info">{{ text_status_archived }}</span>
                  {% endif %}
                </td>
                <td>{{ forecast.date_created }}</td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
        <div class="row">
          <div class="col-sm-6 text-left">{{ pagination }}</div>
          <div class="col-sm-6 text-right">{{ results }}</div>
        </div>
      </div>
    </div>

    <!-- تحليل حسب الطريقة -->
    {% if method_analysis %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pie-chart"></i> {{ text_method_analysis }}</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-md-6">
            <canvas id="methodChart" width="400" height="200"></canvas>
          </div>
          <div class="col-md-6">
            <div class="table-responsive">
              <table class="table table-bordered">
                <thead>
                  <tr>
                    <th>{{ column_forecast_method }}</th>
                    <th class="text-right">{{ column_count }}</th>
                    <th class="text-right">{{ column_average_accuracy }}</th>
                    <th class="text-right">{{ column_success_rate }}</th>
                  </tr>
                </thead>
                <tbody>
                  {% for method in method_analysis %}
                  <tr>
                    <td>
                      {% if method.forecast_method == 'ai_prediction' %}
                        <span class="label label-warning">{{ text_ai_prediction }}</span>
                      {% else %}
                        {{ method.forecast_method }}
                      {% endif %}
                    </td>
                    <td class="text-right">{{ method.count }}</td>
                    <td class="text-right">
                      <span class="label {% if method.average_accuracy >= 90 %}label-success{% elseif method.average_accuracy >= 70 %}label-warning{% else %}label-danger{% endif %}">
                        {{ method.average_accuracy }}%
                      </span>
                    </td>
                    <td class="text-right">
                      <div class="progress" style="margin-bottom: 0;">
                        <div class="progress-bar progress-bar-success" style="width: {{ method.success_rate }}%">
                          {{ method.success_rate }}%
                        </div>
                      </div>
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
    {% endif %}

    <!-- تحليل حسب القسم -->
    {% if department_analysis %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-building"></i> {{ text_department_analysis }}</h3>
      </div>
      <div class="panel-body">
        <div class="table-responsive">
          <table class="table table-bordered">
            <thead>
              <tr>
                <th>{{ column_department }}</th>
                <th class="text-right">{{ column_total_forecasts }}</th>
                <th class="text-right">{{ column_projected_inflow }}</th>
                <th class="text-right">{{ column_projected_outflow }}</th>
                <th class="text-right">{{ column_net_flow }}</th>
                <th class="text-right">{{ column_average_accuracy }}</th>
              </tr>
            </thead>
            <tbody>
              {% for department in department_analysis %}
              <tr>
                <td>{{ department.department_name }}</td>
                <td class="text-right">{{ department.total_forecasts }}</td>
                <td class="text-right text-success">{{ department.projected_inflow }}</td>
                <td class="text-right text-danger">{{ department.projected_outflow }}</td>
                <td class="text-right {% if department.net_flow < 0 %}text-danger{% else %}text-success{% endif %}">
                  {{ department.net_flow }}
                </td>
                <td class="text-right">
                  <span class="label {% if department.average_accuracy >= 90 %}label-success{% elseif department.average_accuracy >= 70 %}label-warning{% else %}label-danger{% endif %}">
                    {{ department.average_accuracy }}%
                  </span>
                </td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    </div>
    {% endif %}

    <!-- تحليل السيناريوهات -->
    {% if scenario_analysis %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-sitemap"></i> {{ text_scenario_analysis }}</h3>
      </div>
      <div class="panel-body">
        <div class="table-responsive">
          <table class="table table-bordered">
            <thead>
              <tr>
                <th>{{ column_scenario_type }}</th>
                <th class="text-right">{{ column_count }}</th>
                <th class="text-right">{{ column_average_projected_balance }}</th>
                <th class="text-right">{{ column_average_variance }}</th>
                <th class="text-right">{{ column_probability_range }}</th>
              </tr>
            </thead>
            <tbody>
              {% for scenario in scenario_analysis %}
              <tr>
                <td>
                  {% if scenario.scenario_type == 'optimistic' %}
                    <span class="label label-success">{{ text_optimistic }}</span>
                  {% elseif scenario.scenario_type == 'pessimistic' %}
                    <span class="label label-danger">{{ text_pessimistic }}</span>
                  {% else %}
                    <span class="label label-info">{{ text_realistic }}</span>
                  {% endif %}
                </td>
                <td class="text-right">{{ scenario.count }}</td>
                <td class="text-right">{{ scenario.average_projected_balance }}</td>
                <td class="text-right {% if scenario.average_variance < 0 %}text-danger{% else %}text-success{% endif %}">
                  {{ scenario.average_variance }}
                </td>
                <td class="text-right">{{ scenario.probability_range }}</td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    </div>
    {% endif %}

  </div>
</div>

<script type="text/javascript">
$(document).ready(function() {
    // تهيئة منتقي التاريخ
    $('.date').datetimepicker({
        language: '{{ datepicker }}',
        pickTime: false
    });

    // الرسم البياني الإجمالي
    {% if overall_trend_data %}
    var ctx = document.getElementById('overallTrendChart').getContext('2d');
    var overallTrendChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: {{ overall_trend_data.labels|json_encode|raw }},
            datasets: [{
                label: '{{ text_projected_inflow }}',
                data: {{ overall_trend_data.inflows|json_encode|raw }},
                borderColor: '#5cb85c',
                backgroundColor: 'rgba(92, 184, 92, 0.1)',
                fill: false
            }, {
                label: '{{ text_projected_outflow }}',
                data: {{ overall_trend_data.outflows|json_encode|raw }},
                borderColor: '#d9534f',
                backgroundColor: 'rgba(217, 83, 79, 0.1)',
                fill: false
            }, {
                label: '{{ text_net_flow }}',
                data: {{ overall_trend_data.net_flows|json_encode|raw }},
                borderColor: '#f0ad4e',
                backgroundColor: 'rgba(240, 173, 78, 0.1)',
                fill: false
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
    {% endif %}

    // رسم بياني لتحليل الطرق
    {% if method_chart_data %}
    var ctx2 = document.getElementById('methodChart').getContext('2d');
    var methodChart = new Chart(ctx2, {
        type: 'doughnut',
        data: {
            labels: {{ method_chart_data.labels|json_encode|raw }},
            datasets: [{
                data: {{ method_chart_data.data|json_encode|raw }},
                backgroundColor: ['#3c8dbc', '#00a65a', '#f39c12', '#dd4b39', '#605ca8']
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
    {% endif %}

    // تصدير إكسل
    $('#button-export-excel').on('click', function() {
        var url = '{{ export_excel }}';
        var filter = $('#form-filter').serialize();
        window.open(url + '&' + filter, '_blank');
    });

    // تصدير PDF
    $('#button-export-pdf').on('click', function() {
        var url = '{{ export_pdf }}';
        var filter = $('#form-filter').serialize();
        window.open(url + '&' + filter, '_blank');
    });

    // طباعة
    $('#button-print').on('click', function() {
        window.print();
    });

    // مسح الفلاتر
    $('#button-clear').on('click', function() {
        window.location = '{{ clear }}';
    });
});
</script>

{{ footer }}
