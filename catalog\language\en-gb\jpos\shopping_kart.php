<?php
// Heading
$_['heading_checkout']  = 'Checkout';

// Text
$_['text_cart']  = 'Cart (%s)';
$_['text_editorder']  = 'Edit Order #%s';
$_['text_enter_fullscreen']  = 'Enter fullscreen mode';
$_['text_exit_fullscreen']  = 'Exit fullscreen mode';
$_['text_guest']  = 'Guest';
$_['text_cart_empty']  = 'Your Shopping Cart is empty!';
$_['text_look']  = "Look like you haven't added any products to your cart yet.";
$_['text_add_new_customer']  = "Add New Customer";
$_['text_total_pay']  = "Total Pay:";

// Button
$_['button_pay']  			= 'Payment';
$_['button_add_note']  		= 'Add Order Note';

// Success
$_['success_cart_add'] 	  = 'Success: You have added %s to shopping cart!';
$_['success_cart_update'] = 'Success: You have modified shopping cart!';
$_['success_cart_remove'] = 'Success: You have modified shopping cart!';
$_['success_cart_clear']  = 'Success: Cart Clear.';

// Error
$_['erorr_cart_notadd']   = 'Warning: Add To Cart!';
$_['error_cart_update']   = 'Warning: Update Shopping Cart!';
$_['error_cart_remove']   = 'Warning: Remove Shopping Cart!';
$_['error_cart_empty']    = 'Warning: Shopping cart already empty!';
$_['error_required']	  = '%s required!';
$_['error_customer_notfound']	= 'Warning: Customer not found!';
$_['error_payment_address']		= 'Warning: Payment address required!';
$_['error_minimum']       		= 'Minimum order quantity for %s is %s!';
$_['error_stock']         		= 'Products marked with *** are not available in the desired quantity or not in stock!';