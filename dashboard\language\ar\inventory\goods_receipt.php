<?php
// Heading
$_['heading_title']          = 'Goods Receipt';

// Text
$_['text_success']           = 'Success: You have modified goods receipts!';
$_['text_list']              = 'Goods Receipt List';
$_['text_add']               = 'Add Goods Receipt';
$_['text_edit']              = 'Edit Goods Receipt';
$_['text_status_draft']      = 'Draft';
$_['text_status_completed']  = 'Completed';
$_['text_status_cancelled']  = 'Cancelled';
$_['text_quality_pass']      = 'Pass';
$_['text_quality_fail']      = 'Fail';

// Entry
$_['entry_receipt_number']   = 'Receipt Number';
$_['entry_receipt_date']     = 'Receipt Date';
$_['entry_status']           = 'Status';
$_['entry_notes']            = 'Notes';
$_['entry_quality_status']   = 'Quality Status';
$_['entry_inspection_grade'] = 'Inspection Grade';
$_['entry_inspection_notes'] = 'Inspection Notes';

// Column
$_['column_product']         = 'Product';
$_['column_quantity_ordered']= 'Quantity Ordered';
$_['column_quantity_received']= 'Quantity Received';
$_['column_unit']            = 'Unit';
$_['column_batch_number']    = 'Batch Number';
$_['column_expiry_date']     = 'Expiry Date';
$_['column_quality_status']  = 'Quality Status';
$_['column_inspection_grade']= 'Inspection Grade';
$_['column_inspection_notes']= 'Inspection Notes';
$_['column_notes']           = 'Notes';

// Error
$_['error_permission']       = 'Warning: You do not have permission to modify goods receipts!';
$_['error_receipt_number']   = 'Receipt Number must be between 3 and 64 characters!';
$_['error_receipt_date']     = 'Receipt Date is required!';
$_['error_quality_status']   = 'Quality status is required!';
$_['error_inspection_grade'] = 'Inspection grade must be between 1 and 5!';

// Text
$_['text_no_purchase_order']  = 'No Purchase Order';
$_['text_select_purchase_order'] = 'Select a Purchase Order';
$_['entry_purchase_order']  = 'Purchase Order';
$_['error_quantity_exceed'] = 'The received quantity exceeds the remaining quantity in the purchase order!';
