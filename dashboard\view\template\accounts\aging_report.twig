{{ header }}{{ column_left }}

<!-- Enterprise Grade Plus CSS for Aging Report -->
<style>
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --danger-color: #e74c3c;
    --warning-color: #f39c12;
    --info-color: #17a2b8;
    --light-bg: #f8f9fa;
    --border-color: #dee2e6;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

.aging-report-container {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: var(--shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.aging-report-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--warning-color), var(--danger-color));
}

.aging-header {
    text-align: center;
    border-bottom: 3px solid var(--primary-color);
    padding-bottom: 20px;
    margin-bottom: 30px;
    position: relative;
}

.aging-header h2 {
    color: var(--primary-color);
    font-weight: 700;
    font-size: 1.8rem;
    margin: 0;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.aging-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    background: #ffffff;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.aging-table th {
    background: linear-gradient(135deg, var(--primary-color), #34495e);
    color: white;
    padding: 15px 12px;
    text-align: center;
    font-weight: 600;
    font-size: 0.9rem;
    border-bottom: 2px solid var(--border-color);
}

.aging-table td {
    padding: 12px;
    border-bottom: 1px solid var(--border-color);
    text-align: center;
    transition: var(--transition);
}

.aging-table tbody tr:hover {
    background: var(--light-bg);
    transform: scale(1.01);
}

.aging-table tbody tr:nth-child(even) {
    background: #fafbfc;
}

.aging-period {
    font-weight: 600;
    padding: 8px 12px;
    border-radius: 4px;
    color: white;
    text-align: center;
    font-size: 0.85rem;
}

.period-current { background: var(--success-color); }
.period-30 { background: var(--info-color); }
.period-60 { background: var(--warning-color); }
.period-90 { background: var(--danger-color); }
.period-over90 { background: #6c757d; }

.amount-cell {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    text-align: {{ direction == 'rtl' ? 'right' : 'left' }};
}

.amount-positive { color: var(--success-color); }
.amount-negative { color: var(--danger-color); }
.amount-overdue { color: var(--danger-color); font-weight: 700; }

.risk-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-left: 8px;
}

.risk-low { background: var(--success-color); }
.risk-medium { background: var(--warning-color); }
.risk-high { background: var(--danger-color); }

.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.summary-card {
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    text-align: center;
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.summary-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0,0,0,0.1);
}

.summary-card h4 {
    color: var(--primary-color);
    margin-bottom: 10px;
    font-size: 1rem;
}

.summary-card .amount {
    font-size: 1.5rem;
    font-weight: 700;
    font-family: 'Courier New', monospace;
}

.card-current .amount { color: var(--success-color); }
.card-overdue .amount { color: var(--danger-color); }
.card-total .amount { color: var(--primary-color); }

/* RTL Support */
[dir="rtl"] .aging-table {
    direction: rtl;
}

[dir="rtl"] .amount-cell {
    text-align: right;
}

/* Accessibility Enhancements */
.aging-table tr:focus-within {
    outline: 2px solid var(--secondary-color);
    outline-offset: 2px;
}

/* Print Styles */
@media print {
    .aging-report-container {
        box-shadow: none;
        border: 1px solid #000;
    }
    
    .aging-table th {
        background: #f0f0f0 !important;
        color: #000 !important;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .aging-table {
        font-size: 0.8rem;
    }
    
    .aging-table th,
    .aging-table td {
        padding: 8px 6px;
    }
    
    .summary-cards {
        grid-template-columns: 1fr;
    }
}
</style>

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-{{ direction == 'rtl' ? 'start' : 'end' }}">
        <div class="btn-group" role="group" aria-label="{{ text_actions }}">
          <button type="button" class="btn btn-success btn-lg" onclick="generateAgingReport()" 
                  data-bs-toggle="tooltip" title="{{ text_generate_tooltip }}">
            <i class="fas fa-clock me-2"></i> {{ button_generate }}
          </button>
          <div class="btn-group" role="group">
            <button type="button" class="btn btn-info dropdown-toggle" data-bs-toggle="dropdown" 
                    aria-expanded="false" data-bs-toggle="tooltip" title="{{ text_export_tooltip }}">
              <i class="fas fa-download me-2"></i> {{ text_export }}
            </button>
            <ul class="dropdown-menu">
              <li><a class="dropdown-item" href="#" onclick="exportAgingReport('excel')">
                <i class="fas fa-file-excel text-success me-2"></i> Excel
              </a></li>
              <li><a class="dropdown-item" href="#" onclick="exportAgingReport('pdf')">
                <i class="fas fa-file-pdf text-danger me-2"></i> PDF
              </a></li>
              <li><a class="dropdown-item" href="#" onclick="exportAgingReport('csv')">
                <i class="fas fa-file-csv text-info me-2"></i> CSV
              </a></li>
              <li><hr class="dropdown-divider"></li>
              <li><a class="dropdown-item" href="#" onclick="printAgingReport()">
                <i class="fas fa-print text-secondary me-2"></i> {{ text_print }}
              </a></li>
            </ul>
          </div>
          <button type="button" class="btn btn-outline-warning" onclick="showRiskAnalysis()"
                  data-bs-toggle="tooltip" title="{{ text_risk_analysis }}">
            <i class="fas fa-exclamation-triangle"></i>
          </button>
        </div>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li class="breadcrumb-item"><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>

  <div class="container-fluid">
    <!-- Filter Form -->
    <div class="card mb-4">
      <div class="card-header">
        <h3 class="card-title">
          <i class="fas fa-filter me-2"></i>{{ text_filter }}
        </h3>
      </div>
      <div class="card-body">
        <form id="aging-report-filter-form" method="post">
          <div class="row">
            <div class="col-md-3">
              <div class="form-group">
                <label for="date_end" class="form-label">{{ entry_date_end }}</label>
                <input type="date" name="date_end" id="date_end" value="{{ date_end }}" class="form-control" required>
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <label for="report_type" class="form-label">{{ entry_report_type }}</label>
                <select name="report_type" id="report_type" class="form-control">
                  <option value="customers"{% if report_type == 'customers' %} selected{% endif %}>{{ text_customers }}</option>
                  <option value="suppliers"{% if report_type == 'suppliers' %} selected{% endif %}>{{ text_suppliers }}</option>
                </select>
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <label for="branch_id" class="form-label">{{ entry_branch }}</label>
                <select name="branch_id" id="branch_id" class="form-control">
                  <option value="">{{ text_all_branches }}</option>
                  {% for branch in branches %}
                  <option value="{{ branch.branch_id }}"{% if branch.branch_id == branch_id %} selected{% endif %}>{{ branch.name }}</option>
                  {% endfor %}
                </select>
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                  <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search me-2"></i>{{ button_filter }}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>

    <!-- Aging Report Content -->
    {% if aging_data %}
    <div class="aging-report-container">
      <div class="aging-header">
        <h2>{{ heading_title }}</h2>
        <p class="text-muted">{{ text_as_of }} {{ date_end_formatted }} - {{ text_report_type }}: {{ report_type_text }}</p>
      </div>

      <!-- Summary Cards -->
      <div class="summary-cards">
        <div class="summary-card card-current">
          <h4>{{ text_current_amount }}</h4>
          <div class="amount">{{ aging_data.summary.current_formatted }}</div>
        </div>
        <div class="summary-card card-overdue">
          <h4>{{ text_overdue_amount }}</h4>
          <div class="amount">{{ aging_data.summary.overdue_formatted }}</div>
        </div>
        <div class="summary-card card-total">
          <h4>{{ text_total_amount }}</h4>
          <div class="amount">{{ aging_data.summary.total_formatted }}</div>
        </div>
        <div class="summary-card">
          <h4>{{ text_overdue_percentage }}</h4>
          <div class="amount text-warning">{{ aging_data.summary.overdue_percentage }}%</div>
        </div>
      </div>

      <!-- Aging Table -->
      <div class="table-responsive">
        <table class="aging-table" id="aging-table">
          <thead>
            <tr>
              <th>{{ text_customer_supplier }}</th>
              <th>{{ text_contact_info }}</th>
              <th>
                <span class="aging-period period-current">{{ text_current }}</span>
              </th>
              <th>
                <span class="aging-period period-30">1-30 {{ text_days }}</span>
              </th>
              <th>
                <span class="aging-period period-60">31-60 {{ text_days }}</span>
              </th>
              <th>
                <span class="aging-period period-90">61-90 {{ text_days }}</span>
              </th>
              <th>
                <span class="aging-period period-over90">90+ {{ text_days }}</span>
              </th>
              <th>{{ text_total }}</th>
              <th>{{ text_risk_level }}</th>
            </tr>
          </thead>
          <tbody>
            {% for item in aging_data.items %}
            <tr data-customer-id="{{ item.customer_id }}" data-risk="{{ item.risk_level }}">
              <td>
                <strong>{{ item.name }}</strong>
                <br>
                <small class="text-muted">{{ item.code }}</small>
              </td>
              <td>
                <small>
                  {% if item.email %}<i class="fas fa-envelope"></i> {{ item.email }}<br>{% endif %}
                  {% if item.telephone %}<i class="fas fa-phone"></i> {{ item.telephone }}{% endif %}
                </small>
              </td>
              <td class="amount-cell amount-positive">{{ item.current_formatted }}</td>
              <td class="amount-cell {% if item.days_1_30 > 0 %}amount-overdue{% endif %}">{{ item.days_1_30_formatted }}</td>
              <td class="amount-cell {% if item.days_31_60 > 0 %}amount-overdue{% endif %}">{{ item.days_31_60_formatted }}</td>
              <td class="amount-cell {% if item.days_61_90 > 0 %}amount-overdue{% endif %}">{{ item.days_61_90_formatted }}</td>
              <td class="amount-cell {% if item.days_over_90 > 0 %}amount-overdue{% endif %}">{{ item.days_over_90_formatted }}</td>
              <td class="amount-cell amount-positive"><strong>{{ item.total_formatted }}</strong></td>
              <td>
                <span class="risk-indicator risk-{{ item.risk_level }}"></span>
                {{ item.risk_text }}
              </td>
            </tr>
            {% endfor %}
          </tbody>
          <tfoot>
            <tr class="table-dark">
              <th colspan="2">{{ text_total }}</th>
              <th class="amount-cell">{{ aging_data.totals.current_formatted }}</th>
              <th class="amount-cell">{{ aging_data.totals.days_1_30_formatted }}</th>
              <th class="amount-cell">{{ aging_data.totals.days_31_60_formatted }}</th>
              <th class="amount-cell">{{ aging_data.totals.days_61_90_formatted }}</th>
              <th class="amount-cell">{{ aging_data.totals.days_over_90_formatted }}</th>
              <th class="amount-cell"><strong>{{ aging_data.totals.total_formatted }}</strong></th>
              <th></th>
            </tr>
          </tfoot>
        </table>
      </div>

      <!-- Risk Analysis Alert -->
      {% if aging_data.risk_analysis.high_risk_count > 0 %}
      <div class="alert alert-danger mt-4">
        <h5><i class="fas fa-exclamation-triangle me-2"></i>{{ text_high_risk_alert }}</h5>
        <p>{{ text_high_risk_customers_found }}: <strong>{{ aging_data.risk_analysis.high_risk_count }}</strong></p>
        <p>{{ text_total_risk_amount }}: <strong>{{ aging_data.risk_analysis.total_risk_amount_formatted }}</strong></p>
        <button type="button" class="btn btn-outline-danger btn-sm" onclick="showRiskAnalysis()">
          {{ text_view_risk_details }}
        </button>
      </div>
      {% endif %}
    </div>
    {% else %}
    <div class="alert alert-info">
      <i class="fas fa-info-circle me-2"></i>
      {{ text_no_data }}
    </div>
    {% endif %}
  </div>
</div>

<script>
// Enterprise Grade Plus JavaScript for Aging Report
class AgingReportManager {
    constructor() {
        this.initializeTooltips();
        this.initializeFormValidation();
        this.initializeKeyboardShortcuts();
        this.initializeAutoSave();
        this.initializeDataTable();
        this.initializeRiskAnalysis();
    }

    initializeTooltips() {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }

    initializeFormValidation() {
        const form = document.getElementById('aging-report-filter-form');
        if (form) {
            form.addEventListener('submit', this.validateForm.bind(this));
        }
    }

    initializeKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case 'g':
                        e.preventDefault();
                        this.generateAgingReport();
                        break;
                    case 'e':
                        e.preventDefault();
                        this.showExportMenu();
                        break;
                    case 'p':
                        e.preventDefault();
                        this.printAgingReport();
                        break;
                    case 'r':
                        e.preventDefault();
                        this.showRiskAnalysis();
                        break;
                }
            }
        });
    }

    initializeAutoSave() {
        const inputs = document.querySelectorAll('#aging-report-filter-form input, #aging-report-filter-form select');
        inputs.forEach(input => {
            input.addEventListener('change', () => {
                this.saveFormState();
            });
        });
        this.loadFormState();
    }

    initializeDataTable() {
        const table = document.getElementById('aging-table');
        if (table && typeof $ !== 'undefined' && $.fn.DataTable) {
            $(table).DataTable({
                responsive: true,
                pageLength: 25,
                order: [[7, 'desc']], // Sort by total amount
                columnDefs: [
                    { targets: [2, 3, 4, 5, 6, 7], className: 'text-end' },
                    { targets: [8], orderable: false }
                ],
                language: {
                    url: '{{ direction == "rtl" ? "/view/javascript/jquery/datatables-ar.json" : "/view/javascript/jquery/datatables-en.json" }}'
                }
            });
        }
    }

    initializeRiskAnalysis() {
        // Highlight high-risk rows
        const highRiskRows = document.querySelectorAll('[data-risk="high"]');
        highRiskRows.forEach(row => {
            row.style.backgroundColor = '#fff5f5';
            row.style.borderLeft = '4px solid var(--danger-color)';
        });
    }

    validateForm(e) {
        e.preventDefault();
        const dateEnd = document.getElementById('date_end').value;
        const reportType = document.getElementById('report_type').value;

        if (!dateEnd) {
            this.showAlert('{{ error_date_required }}', 'danger');
            return false;
        }

        if (new Date(dateEnd) > new Date()) {
            this.showAlert('{{ warning_future_date }}', 'warning');
        }

        this.generateAgingReport();
        return true;
    }

    generateAgingReport() {
        const form = document.getElementById('aging-report-filter-form');
        const formData = new FormData(form);

        // Show loading state
        this.showLoadingState(true);

        fetch('{{ generate_url }}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            this.showLoadingState(false);
            if (data.success) {
                this.showAlert('{{ text_success_generation }}', 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                this.showAlert(data.error || '{{ error_generate }}', 'danger');
            }
        })
        .catch(error => {
            this.showLoadingState(false);
            this.showAlert('{{ error_generate }}: ' + error.message, 'danger');
        });
    }

    exportAgingReport(format) {
        const params = new URLSearchParams({
            format: format,
            date_end: document.getElementById('date_end').value,
            report_type: document.getElementById('report_type').value,
            branch_id: document.getElementById('branch_id').value
        });

        this.showAlert('{{ text_exporting }}...', 'info');
        window.open('{{ export_url }}&' + params.toString(), '_blank');
    }

    printAgingReport() {
        window.print();
    }

    showRiskAnalysis() {
        // Risk analysis implementation
        const highRiskRows = document.querySelectorAll('[data-risk="high"]');
        if (highRiskRows.length > 0) {
            this.showAlert('{{ text_high_risk_customers_found }}: ' + highRiskRows.length, 'warning');
            // Scroll to first high-risk customer
            highRiskRows[0].scrollIntoView({ behavior: 'smooth', block: 'center' });
        } else {
            this.showAlert('{{ text_no_high_risk_customers }}', 'success');
        }
    }

    showExportMenu() {
        const exportButton = document.querySelector('.dropdown-toggle');
        if (exportButton) {
            exportButton.click();
        }
    }

    showLoadingState(show) {
        const generateBtn = document.querySelector('button[onclick="generateAgingReport()"]');
        if (generateBtn) {
            if (show) {
                generateBtn.disabled = true;
                generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> {{ text_generating }}';
            } else {
                generateBtn.disabled = false;
                generateBtn.innerHTML = '<i class="fas fa-clock me-2"></i> {{ button_generate }}';
            }
        }
    }

    showAlert(message, type) {
        const alertContainer = document.createElement('div');
        alertContainer.className = `alert alert-${type} alert-dismissible fade show`;
        alertContainer.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'}"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        const container = document.querySelector('#content .container-fluid');
        container.insertBefore(alertContainer, container.firstChild);

        setTimeout(() => {
            alertContainer.remove();
        }, 5000);
    }

    saveFormState() {
        const formData = new FormData(document.getElementById('aging-report-filter-form'));
        const state = Object.fromEntries(formData);
        localStorage.setItem('aging_report_form_state', JSON.stringify(state));
    }

    loadFormState() {
        const savedState = localStorage.getItem('aging_report_form_state');
        if (savedState) {
            const state = JSON.parse(savedState);
            Object.keys(state).forEach(key => {
                const element = document.getElementById(key);
                if (element) {
                    element.value = state[key];
                }
            });
        }
    }
}

// Global functions for backward compatibility
function generateAgingReport() {
    agingReportManager.generateAgingReport();
}

function exportAgingReport(format) {
    agingReportManager.exportAgingReport(format);
}

function printAgingReport() {
    agingReportManager.printAgingReport();
}

function showRiskAnalysis() {
    agingReportManager.showRiskAnalysis();
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.agingReportManager = new AgingReportManager();
});
</script>

{{ footer }}
