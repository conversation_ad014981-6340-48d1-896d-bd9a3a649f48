{{ header }}
{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="button" data-bs-toggle="tooltip" title="{{ button_filter }}" onclick="$('#filter-alerts').toggleClass('d-none');" class="btn btn-light d-md-none d-lg-none"><i class="fas fa-filter"></i></button>
        <a href="{{ refresh }}" data-bs-toggle="tooltip" title="{{ button_refresh }}" class="btn btn-warning"><i class="fas fa-sync"></i></a>
        <a href="{{ settings }}" data-bs-toggle="tooltip" title="{{ button_settings }}" class="btn btn-info"><i class="fas fa-cog"></i></a>
        <a href="{{ analytics }}" data-bs-toggle="tooltip" title="{{ button_analytics }}" class="btn btn-primary"><i class="fas fa-chart-bar"></i></a>
        <a href="{{ export }}" data-bs-toggle="tooltip" title="{{ button_export }}" class="btn btn-success"><i class="fas fa-file-excel"></i></a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ol class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
          <li class="breadcrumb-item"><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    
    <!-- إحصائيات التنبيهات -->
    <div class="row mb-3">
      <div class="col-lg-3 col-md-6">
        <div class="card bg-danger text-white">
          <div class="card-body">
            <div class="d-flex justify-content-between">
              <div>
                <h4>{{ alert_stats.critical }}</h4>
                <p class="mb-0">{{ text_critical_alerts }}</p>
              </div>
              <div class="align-self-center">
                <i class="fas fa-exclamation-triangle fa-2x"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-md-6">
        <div class="card bg-warning text-white">
          <div class="card-body">
            <div class="d-flex justify-content-between">
              <div>
                <h4>{{ alert_stats.high }}</h4>
                <p class="mb-0">{{ text_high_alerts }}</p>
              </div>
              <div class="align-self-center">
                <i class="fas fa-exclamation fa-2x"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-md-6">
        <div class="card bg-info text-white">
          <div class="card-body">
            <div class="d-flex justify-content-between">
              <div>
                <h4>{{ alert_stats.medium }}</h4>
                <p class="mb-0">{{ text_medium_alerts }}</p>
              </div>
              <div class="align-self-center">
                <i class="fas fa-info fa-2x"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-md-6">
        <div class="card bg-success text-white">
          <div class="card-body">
            <div class="d-flex justify-content-between">
              <div>
                <h4>{{ alert_stats.total }}</h4>
                <p class="mb-0">{{ text_total_alerts }}</p>
              </div>
              <div class="align-self-center">
                <i class="fas fa-bell fa-2x"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="row">
      <!-- فلاتر التنبيهات -->
      <div id="filter-alerts" class="col-lg-3 col-md-12 order-lg-last d-none d-lg-block mb-3">
        <div class="card">
          <div class="card-header"><i class="fas fa-filter"></i> {{ text_filter }}</div>
          <div class="card-body">
            <div class="mb-3">
              <label for="input-alert-type" class="form-label">{{ entry_alert_type }}</label>
              <select name="filter_alert_type" id="input-alert-type" class="form-select">
                <option value="">{{ text_all_types }}</option>
                {% for key, value in alert_types %}
                  <option value="{{ key }}" {% if key == filter_alert_type %}selected="selected"{% endif %}>{{ value }}</option>
                {% endfor %}
              </select>
            </div>
            <div class="mb-3">
              <label for="input-severity" class="form-label">{{ entry_severity }}</label>
              <select name="filter_severity" id="input-severity" class="form-select">
                <option value="">{{ text_all_severities }}</option>
                {% for key, value in severity_levels %}
                  <option value="{{ key }}" {% if key == filter_severity %}selected="selected"{% endif %}>{{ value }}</option>
                {% endfor %}
              </select>
            </div>
            <div class="mb-3">
              <label for="input-warehouse" class="form-label">{{ entry_warehouse }}</label>
              <select name="filter_warehouse_id" id="input-warehouse" class="form-select">
                <option value="">{{ text_all_warehouses }}</option>
                {% for warehouse in warehouses %}
                  <option value="{{ warehouse.warehouse_id }}" {% if warehouse.warehouse_id == filter_warehouse_id %}selected="selected"{% endif %}>{{ warehouse.name }}</option>
                {% endfor %}
              </select>
            </div>
            <div class="mb-3">
              <label for="input-category" class="form-label">{{ entry_category }}</label>
              <select name="filter_category_id" id="input-category" class="form-select">
                <option value="">{{ text_all_categories }}</option>
                {% for category in categories %}
                  <option value="{{ category.category_id }}" {% if category.category_id == filter_category_id %}selected="selected"{% endif %}>{{ category.name }}</option>
                {% endfor %}
              </select>
            </div>
            <div class="mb-3">
              <label for="input-status" class="form-label">{{ entry_status }}</label>
              <select name="filter_status" id="input-status" class="form-select">
                <option value="">{{ text_all_statuses }}</option>
                <option value="1" {% if filter_status == '1' %}selected="selected"{% endif %}>{{ text_active }}</option>
                <option value="0" {% if filter_status == '0' %}selected="selected"{% endif %}>{{ text_resolved }}</option>
              </select>
            </div>
            <div class="mb-3">
              <label for="input-date-start" class="form-label">{{ entry_date_start }}</label>
              <input type="date" name="filter_date_start" value="{{ filter_date_start }}" id="input-date-start" class="form-control"/>
            </div>
            <div class="mb-3">
              <label for="input-date-end" class="form-label">{{ entry_date_end }}</label>
              <input type="date" name="filter_date_end" value="{{ filter_date_end }}" id="input-date-end" class="form-control"/>
            </div>
            <div class="text-end">
              <button type="button" id="button-filter" class="btn btn-light"><i class="fas fa-filter"></i> {{ button_filter }}</button>
            </div>
          </div>
        </div>
      </div>
      
      <!-- قائمة التنبيهات -->
      <div class="col-lg-9 col-md-12">
        <div class="card">
          <div class="card-header">
            <i class="fas fa-bell"></i> {{ text_list }}
            <div class="card-tools">
              <button type="button" class="btn btn-sm btn-outline-secondary" onclick="refreshAlerts()">
                <i class="fas fa-sync"></i> {{ button_refresh }}
              </button>
            </div>
          </div>
          <div class="card-body">
            {% if alerts %}
            <div class="table-responsive">
              <table class="table table-bordered table-hover">
                <thead>
                  <tr>
                    <td class="text-center">{{ column_severity }}</td>
                    <td><a href="{{ sort_alert_type }}" {% if sort == 'alert_type' %}class="{% if order == 'ASC' %}asc{% else %}desc{% endif %}"{% endif %}>{{ column_alert_type }}</a></td>
                    <td><a href="{{ sort_product }}" {% if sort == 'product_name' %}class="{% if order == 'ASC' %}asc{% else %}desc{% endif %}"{% endif %}>{{ column_product }}</a></td>
                    <td><a href="{{ sort_warehouse }}" {% if sort == 'warehouse_name' %}class="{% if order == 'ASC' %}asc{% else %}desc{% endif %}"{% endif %}>{{ column_warehouse }}</a></td>
                    <td class="text-center">{{ column_current_stock }}</td>
                    <td class="text-center">{{ column_threshold }}</td>
                    <td>{{ column_message }}</td>
                    <td class="text-center">{{ column_status }}</td>
                    <td class="text-center"><a href="{{ sort_date }}" {% if sort == 'date_created' %}class="{% if order == 'ASC' %}asc{% else %}desc{% endif %}"{% endif %}>{{ column_date_created }}</a></td>
                    <td class="text-center">{{ column_action }}</td>
                  </tr>
                </thead>
                <tbody>
                  {% for alert in alerts %}
                  <tr id="alert-row-{{ alert.alert_id }}">
                    <td class="text-center">
                      <span class="badge bg-{{ alert.severity_class }}">
                        <i class="fas fa-{% if alert.severity == 'critical' %}exclamation-triangle{% elseif alert.severity == 'high' %}exclamation{% elseif alert.severity == 'medium' %}info{% else %}check{% endif %}"></i>
                        {{ alert.severity_text }}
                      </span>
                    </td>
                    <td>{{ alert.alert_type_text }}</td>
                    <td>{{ alert.product_name }}</td>
                    <td>{{ alert.warehouse_name }}</td>
                    <td class="text-center">
                      <span class="badge bg-{% if alert.current_stock <= alert.threshold_value %}danger{% else %}success{% endif %}">
                        {{ alert.current_stock }}
                      </span>
                    </td>
                    <td class="text-center">{{ alert.threshold_value }}</td>
                    <td>
                      <small>{{ alert.message }}</small>
                    </td>
                    <td class="text-center">
                      <span class="badge bg-{{ alert.status_class }}">{{ alert.status_text }}</span>
                    </td>
                    <td class="text-center">{{ alert.date_created }}</td>
                    <td class="text-center">
                      <div class="btn-group btn-group-sm">
                        {% if alert.status %}
                        <button type="button" class="btn btn-success" onclick="resolveAlert({{ alert.alert_id }})" data-bs-toggle="tooltip" title="{{ button_resolve }}">
                          <i class="fas fa-check"></i>
                        </button>
                        {% endif %}
                        <button type="button" class="btn btn-info" onclick="viewAlert({{ alert.alert_id }})" data-bs-toggle="tooltip" title="{{ button_view }}">
                          <i class="fas fa-eye"></i>
                        </button>
                      </div>
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
            <div class="row">
              <div class="col-sm-6 text-start">{{ pagination }}</div>
              <div class="col-sm-6 text-end">{{ results }}</div>
            </div>
            {% else %}
            <div class="text-center">
              <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
              <h4>{{ text_no_results }}</h4>
              <p class="text-muted">{{ text_no_alerts_message }}</p>
              <button type="button" class="btn btn-primary" onclick="refreshAlerts()">
                <i class="fas fa-sync"></i> {{ button_refresh_now }}
              </button>
            </div>
            {% endif %}
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Modal لحل التنبيه -->
<div class="modal fade" id="modal-resolve-alert" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">{{ text_resolve_alert }}</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <form id="form-resolve-alert">
          <input type="hidden" name="alert_id" id="resolve-alert-id">
          <div class="mb-3">
            <label for="resolve-notes" class="form-label">{{ entry_resolution_notes }}</label>
            <textarea name="notes" id="resolve-notes" class="form-control" rows="3" placeholder="{{ placeholder_resolution_notes }}"></textarea>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ button_cancel }}</button>
        <button type="button" class="btn btn-success" onclick="confirmResolveAlert()">{{ button_resolve }}</button>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript">
// تطبيق الفلاتر
$('#button-filter').on('click', function() {
    var url = 'index.php?route=inventory/stock_alerts&user_token={{ user_token }}';
    
    var filter_alert_type = $('select[name=\'filter_alert_type\']').val();
    if (filter_alert_type) {
        url += '&filter_alert_type=' + encodeURIComponent(filter_alert_type);
    }
    
    var filter_severity = $('select[name=\'filter_severity\']').val();
    if (filter_severity) {
        url += '&filter_severity=' + encodeURIComponent(filter_severity);
    }
    
    var filter_warehouse_id = $('select[name=\'filter_warehouse_id\']').val();
    if (filter_warehouse_id) {
        url += '&filter_warehouse_id=' + filter_warehouse_id;
    }
    
    var filter_category_id = $('select[name=\'filter_category_id\']').val();
    if (filter_category_id) {
        url += '&filter_category_id=' + filter_category_id;
    }
    
    var filter_status = $('select[name=\'filter_status\']').val();
    if (filter_status !== '') {
        url += '&filter_status=' + filter_status;
    }
    
    var filter_date_start = $('input[name=\'filter_date_start\']').val();
    if (filter_date_start) {
        url += '&filter_date_start=' + filter_date_start;
    }
    
    var filter_date_end = $('input[name=\'filter_date_end\']').val();
    if (filter_date_end) {
        url += '&filter_date_end=' + filter_date_end;
    }
    
    location = url;
});

// تحديث التنبيهات
function refreshAlerts() {
    $.ajax({
        url: 'index.php?route=inventory/stock_alerts/refresh&user_token={{ user_token }}',
        type: 'GET',
        beforeSend: function() {
            $('#button-refresh').html('<i class="fas fa-spinner fa-spin"></i> {{ text_refreshing }}');
        },
        complete: function() {
            location.reload();
        }
    });
}

// حل التنبيه
function resolveAlert(alertId) {
    $('#resolve-alert-id').val(alertId);
    $('#modal-resolve-alert').modal('show');
}

function confirmResolveAlert() {
    $.ajax({
        url: 'index.php?route=inventory/stock_alerts/resolve&user_token={{ user_token }}',
        type: 'POST',
        data: $('#form-resolve-alert').serialize(),
        dataType: 'json',
        success: function(json) {
            if (json.success) {
                $('#modal-resolve-alert').modal('hide');
                location.reload();
            } else {
                alert(json.error);
            }
        }
    });
}

// عرض تفاصيل التنبيه
function viewAlert(alertId) {
    window.open('index.php?route=inventory/stock_alerts/view&user_token={{ user_token }}&alert_id=' + alertId, '_blank');
}

// تحديث تلقائي كل 5 دقائق
setInterval(function() {
    if (confirm('{{ text_auto_refresh_confirm }}')) {
        refreshAlerts();
    }
}, 300000);
</script>

{{ footer }}
