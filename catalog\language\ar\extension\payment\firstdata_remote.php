<?php
// Text
$_['text_title']				= 'Credit or Debit Card';
$_['text_credit_card']			= 'Credit Card Details';
$_['text_wait']					= 'Please wait!';
$_['text_store_card']           = 'Store card details?';

// Entry
$_['entry_cc_number']			= 'Card number';
$_['entry_cc_name']				= 'Cardholder name';
$_['entry_cc_expire_date']		= 'Card expiry date';
$_['entry_cc_cvv2']				= 'Card security code (CVV2)';

// Help
$_['help_start_date']			= '(if available)';
$_['help_issue']				= '(for Maestro and Solo cards only)';

// Text
$_['text_result']				= 'Result: ';
$_['text_approval_code']		= 'Approval code: ';
$_['text_reference_number']		= 'Reference: ';
$_['text_card_number_ref']		= 'Card last 4 digits: xxxx ';
$_['text_card_brand']			= 'Card brand: ';
$_['text_response_code']		= 'Response code: ';
$_['text_fault']				= 'Fault message: ';
$_['text_error']				= 'Error message: ';
$_['text_avs']					= 'Address verification: ';
$_['text_address_ppx']			= 'No address data provided or Address not checked by the Card Issuer';
$_['text_address_yyy']			= 'Card Issuer confirmed that street and postcode match with their records';
$_['text_address_yna']			= 'Card Issuer confirmed that street matches with their records but postcode does not match';
$_['text_address_nyz']			= 'Card Issuer confirmed that postcode matches with their records but street does not match';
$_['text_address_nnn']			= 'Both street and postcode do not match with the Card Issuer records';
$_['text_address_ypx']			= 'Card Issuer confirmed that street matches with their records. The Issuer did not check the postcode';
$_['text_address_pyx']			= 'Card Issuer confirmed that postcode matches with their records. The Issuer did not check the street';
$_['text_address_xxu']			= 'Card Issuer did not check the AVS information';
$_['text_card_code_verify']		= 'Security code: ';
$_['text_card_code_m']			= 'Card security code match';
$_['text_card_code_n']			= 'Card security code does not match';
$_['text_card_code_p']			= 'Not processed';
$_['text_card_code_s']			= 'Merchant has indicated that the card security code is not present on the card';
$_['text_card_code_u']			= 'Issuer is not certified and/or has not provided encryption keys';
$_['text_card_code_x']			= 'No response from the credit card association was received';
$_['text_card_code_blank']		= 'A blank response should indicate that no code was sent and that there was no indication that the code was not present on the card.';
$_['text_card_accepted']		= 'Accepted cards: ';
$_['text_card_type_m']			= 'Mastercard';
$_['text_card_type_v']			= 'Visa (Credit/Debit/Electron/Delta)';
$_['text_card_type_c']			= 'Diners';
$_['text_card_type_a']			= 'American Express';
$_['text_card_type_ma']			= 'Maestro';
$_['text_card_new']				= 'New card';
$_['text_response_proc_code']	= 'Processor code: ';
$_['text_response_ref']			= 'Ref number: ';

// Error
$_['error_card_number']			= 'Please check your card number is valid';
$_['error_card_name']			= 'Please check the card holder name is valid';
$_['error_card_cvv']			= 'Please check the CVV2 is valid';
$_['error_failed']				= 'Unable to process your payment, please contact the merchant';
