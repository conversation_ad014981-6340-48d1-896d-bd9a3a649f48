<?php
/**
 * تحكم قائمة الدخل الشاملة والمتكاملة
 * يجمع بين القوة والبساطة - يتفوق على SAP وOracle في سهولة الاستخدام
 * مع الاحتفاظ بالقوة والمرونة المطلوبة للشركات الكبيرة
 * متوافق مع معايير المحاسبة المصرية ومتطلبات ETA
 */
class ControllerAccountsIncomeStatement extends Controller {
    private $error = array();
    private $central_service;

    public function __construct($registry) {
        parent::__construct($registry);

        // تحميل الخدمات المركزية
        $this->load->model('core/central_service_manager');
        $this->central_service = $this->model_core_central_service_manager;
    }

    public function index() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/income_statement') ||
            !$this->user->hasKey('accounting_income_statement_view')) {

            $this->central_service->logActivity('unauthorized_access', 'accounts',
                $this->language->get('log_unauthorized_access_income_statement'), [
                'user_id' => $this->user->getId(),
                'ip_address' => $this->request->server['REMOTE_ADDR']
            ]);

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/income_statement');
        $this->document->setTitle($this->language->get('heading_title'));
        $this->load->model('accounts/income_statement');

        // إضافة CSS و JavaScript المتقدم
        $this->document->addStyle('view/stylesheet/accounts/income_statement.css');
        $this->document->addScript('view/javascript/accounts/income_statement.js');
        $this->document->addScript('view/javascript/jquery/accounting.min.js');
        $this->document->addScript('view/javascript/jquery/daterangepicker.min.js');
        $this->document->addStyle('view/javascript/jquery/daterangepicker.css');
        $this->document->addScript('view/javascript/chart.min.js');

        // تسجيل الوصول للشاشة
        $this->central_service->logActivity('view', 'accounts',
            $this->language->get('log_view_income_statement_screen'), [
            'user_id' => $this->user->getId(),
            'screen' => 'accounts/income_statement'
        ]);

        $this->getReport();
    }

    /**
     * عرض قائمة الدخل
     */
    public function view() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/income_statement') ||
            !$this->user->hasKey('accounting_income_statement_view')) {

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/income_statement');
        $this->document->setTitle($this->language->get('heading_title'));

        if (!isset($this->session->data['income_statement_data'])) {
            $this->session->data['error'] = $this->language->get('error_no_data');
            $this->response->redirect($this->url->link('accounts/income_statement', 'user_token=' . $this->session->data['user_token'], true));
            return;
        }

        $data = $this->session->data['income_statement_data'];

        // تسجيل عرض التقرير
        $this->central_service->logActivity('view_report', 'accounts',
            $this->language->get('log_view_income_statement'), [
            'user_id' => $this->user->getId(),
            'action' => 'view_income_statement'
        ]);

        $data['breadcrumbs'] = array();
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('accounts/income_statement', 'user_token=' . $this->session->data['user_token'], true)
        );

        $data['export_excel'] = $this->url->link('accounts/income_statement/export', 'format=excel&user_token=' . $this->session->data['user_token'], true);
        $data['export_pdf'] = $this->url->link('accounts/income_statement/export', 'format=pdf&user_token=' . $this->session->data['user_token'], true);
        $data['export_csv'] = $this->url->link('accounts/income_statement/export', 'format=csv&user_token=' . $this->session->data['user_token'], true);

        $data['user_token'] = $this->session->data['user_token'];
        $data['heading_title'] = $this->language->get('heading_title');

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/income_statement_view', $data));
    }

    public function generate() {
        $this->load->language('accounts/income_statement');
        $this->load->model('accounts/income_statement');

        $json = array();

        if ($this->validateForm()) {
            try {
                $filter_data = $this->prepareFilterData();

                $report_data = $this->model_accounts_income_statement->generateIncomeStatement($filter_data);

                $json['success'] = true;
                $json['report'] = $report_data;

                // تسجيل إنشاء التقرير
                $this->central_service->logActivity('generate_report', 'accounts',
                    $this->language->get('log_generate_income_statement_period') . ': ' . $filter_data['date_from'] . ' - ' . $filter_data['date_to'], [
                    'user_id' => $this->user->getId(),
                    'date_from' => $filter_data['date_from'],
                    'date_to' => $filter_data['date_to'],
                    'module' => 'income_statement'
                ]);

                // إرسال إشعار للمحاسب الرئيسي
                $this->central_service->sendNotification(
                    'income_statement_generated',
                    $this->language->get('text_generate_income_statement'),
                    $this->language->get('text_income_statement_generated_notification') . ' ' . $this->language->get('text_for_period') . ' ' . $filter_data['date_from'] . ' ' . $this->language->get('text_to') . ' ' . $filter_data['date_to'] . ' ' . $this->language->get('text_by') . ' ' . $this->user->getFirstName(),
                    [$this->config->get('config_chief_accountant_id')],
                    [
                        'period' => $filter_data['date_from'] . ' - ' . $filter_data['date_to'],
                        'user_name' => $this->user->getFirstName() . ' ' . $this->user->getLastName()
                    ]
                );

                // حفظ البيانات في الجلسة
                $this->session->data['income_statement_data'] = $report_data;
                $this->session->data['income_statement_filter'] = $filter_data;

                $json['success'] = $this->language->get('text_success_generate');

                // إعادة توجيه حسب الإجراء المطلوب
                if (isset($this->request->post['generate_and_new'])) {
                    $json['redirect'] = $this->url->link('accounts/income_statement', 'user_token=' . $this->session->data['user_token'], true);
                } elseif (isset($this->request->post['generate_and_export'])) {
                    $json['redirect'] = $this->url->link('accounts/income_statement/export', 'format=excel&user_token=' . $this->session->data['user_token'], true);
                } else {
                    $json['redirect'] = $this->url->link('accounts/income_statement/view', 'user_token=' . $this->session->data['user_token'], true);
                }

            } catch (Exception $e) {
                $json['error'] = $this->language->get('error_generate') . ': ' . $e->getMessage();
            }
        } else {
            $json['error'] = $this->language->get('error_invalid_data');
            $json['errors'] = $this->error;
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    public function drill_down() {
        $this->load->model('accounts/income_statement');

        $json = array();

        if (isset($this->request->get['account_id']) && isset($this->request->get['date_from']) && isset($this->request->get['date_to'])) {
            $account_id = $this->request->get['account_id'];
            $date_from = $this->request->get['date_from'];
            $date_to = $this->request->get['date_to'];

            try {
                $drill_down_data = $this->model_accounts_income_statement->getDrillDownData($account_id, $date_from, $date_to);

                $json['success'] = true;
                $json['data'] = $drill_down_data;

            } catch (Exception $e) {
                $json['error'] = $e->getMessage();
            }
        } else {
            $json['error'] = $this->language->get('error_missing_parameters');
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    public function getAnalytics() {
        $this->load->model('accounts/income_statement');

        $json = array();

        if ($this->validateForm()) {
            try {
                $filter_data = $this->prepareFilterData();

                $analytics = $this->model_accounts_income_statement->getIncomeStatementAnalytics($filter_data);

                // إضافة مؤشرات إضافية
                $json['success'] = true;
                $json['analytics'] = $analytics;
                $json['kpis'] = array(
                    'gross_profit_margin' => ($analytics['total_revenue'] > 0) ?
                        round(($analytics['gross_profit'] / $analytics['total_revenue']) * 100, 2) : 0,
                    'net_profit_margin' => ($analytics['total_revenue'] > 0) ?
                        round(($analytics['net_income'] / $analytics['total_revenue']) * 100, 2) : 0,
                    'operating_margin' => ($analytics['total_revenue'] > 0) ?
                        round(($analytics['operating_income'] / $analytics['total_revenue']) * 100, 2) : 0,
                    'expense_ratio' => ($analytics['total_revenue'] > 0) ?
                        round(($analytics['total_expenses'] / $analytics['total_revenue']) * 100, 2) : 0
                );

            } catch (Exception $e) {
                $json['error'] = $e->getMessage();
            }
        } else {
            $json['error'] = $this->language->get('error_invalid_data');
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    protected function validateForm() {
        if (empty($this->request->post['date_from'])) {
            $this->error['date_from'] = $this->language->get('error_date_from_required');
        }

        if (empty($this->request->post['date_to'])) {
            $this->error['date_to'] = $this->language->get('error_date_to_required');
        }

        if (!empty($this->request->post['date_from']) && !empty($this->request->post['date_to'])) {
            if (strtotime($this->request->post['date_from']) > strtotime($this->request->post['date_to'])) {
                $this->error['date_range'] = $this->language->get('error_date_range');
            }
        }

        return !$this->error;
    }

    protected function validateCompareForm() {
        if (empty($this->request->post['period1_from']) || empty($this->request->post['period1_to'])) {
            $this->error['period1'] = $this->language->get('error_period1_required');
        }

        if (empty($this->request->post['period2_from']) || empty($this->request->post['period2_to'])) {
            $this->error['period2'] = $this->language->get('error_period2_required');
        }

        return !$this->error;
    }

    protected function prepareFilterData() {
        return array(
            'date_from' => isset($this->request->post['date_from']) ? $this->db->escape($this->request->post['date_from']) : '',
            'date_to' => isset($this->request->post['date_to']) ? $this->db->escape($this->request->post['date_to']) : '',
            'cost_center_id' => isset($this->request->post['cost_center_id']) ? (int)$this->request->post['cost_center_id'] : null,
            'branch_id' => isset($this->request->post['branch_id']) ? (int)$this->request->post['branch_id'] : null,
            'currency' => isset($this->request->post['currency']) ? $this->db->escape($this->request->post['currency']) : $this->config->get('config_currency'),
            'show_zero_balances' => isset($this->request->post['show_zero_balances']) ? (int)$this->request->post['show_zero_balances'] : 0,
            'group_by_category' => isset($this->request->post['group_by_category']) ? (int)$this->request->post['group_by_category'] : 1,
            'include_budget_comparison' => isset($this->request->post['include_budget_comparison']) ? (int)$this->request->post['include_budget_comparison'] : 0
        );
    }

    protected function prepareCompareFilterData() {
        return array(
            'period1_from' => $this->request->post['period1_from'],
            'period1_to' => $this->request->post['period1_to'],
            'period2_from' => $this->request->post['period2_from'],
            'period2_to' => $this->request->post['period2_to'],
            'cost_center_id' => $this->request->post['cost_center_id'] ?? null,
            'branch_id' => $this->request->post['branch_id'] ?? null,
            'currency' => $this->request->post['currency'] ?? $this->config->get('config_currency')
        );
    }

    protected function prepareCompareData() {
        return array(
            'period1' => array(
                'from' => $this->request->post['period1_from'],
                'to' => $this->request->post['period1_to']
            ),
            'period2' => array(
                'from' => $this->request->post['period2_from'],
                'to' => $this->request->post['period2_to']
            ),
            'cost_center_id' => $this->request->post['cost_center_id'] ?? null,
            'branch_id' => $this->request->post['branch_id'] ?? null,
            'currency' => $this->request->post['currency'] ?? $this->config->get('config_currency')
        );
    }

    public function print() {
        $this->load->language('accounts/income_statement');
        $this->load->model('accounts/income_statement');

        $data['title'] = $this->language->get('print_title');
        $data['printdate'] = date('Y-m-d H:i:s');
        $data['user_token'] = $this->session->data['user_token'];
        $data['lang'] = $this->language->get('code');
        $data['direction'] = $this->language->get('direction');
        $data['whoprint'] = $this->user->getUserName();

        $date_start = $this->request->post['date_start'] ?: date('Y-01-01');
        $date_end = $this->request->post['date_end'] ?: date('Y-m-d');

        $data['start_date'] = date($this->language->get('date_format_short'), strtotime($date_start));
        $data['end_date'] = date($this->language->get('date_format_short'), strtotime($date_end));

        if ($date_start && $date_end) {
            $results = $this->model_accounts_income_statement->getIncomeStatementData($date_start, $date_end);
            $data['revenues'] = $results['revenues'];
            $data['expenses'] = $results['expenses'];
            $data['total_revenues'] = $results['total_revenues'];
            $data['total_expenses'] = $results['total_expenses'];
            $data['net_income'] = $results['net_income'];
        } else {
            $data['revenues'] = [];
            $data['expenses'] = [];
            $data['total_revenues'] = $this->currency->format(0, $this->config->get('config_currency'));
            $data['total_expenses'] = $this->currency->format(0, $this->config->get('config_currency'));
            $data['net_income'] = $this->currency->format(0, $this->config->get('config_currency'));
            $this->error['warning'] = $this->language->get('error_no_data');
        }

        $data['text_income_statement'] = $this->language->get('text_income_statement');
        $data['text_period'] = $this->language->get('text_period');
        $data['text_from'] = $this->language->get('text_from');
        $data['text_to'] = $this->language->get('text_to');
        $data['text_revenues'] = $this->language->get('text_revenues');
        $data['text_expenses'] = $this->language->get('text_expenses');
        $data['text_total_revenues'] = $this->language->get('text_total_revenues');
        $data['text_total_expenses'] = $this->language->get('text_total_expenses');
        $data['text_net_income'] = $this->language->get('text_net_income');

        $this->response->setOutput($this->load->view('accounts/income_statement_list', $data));
    }

    protected function getReport() {
        $data['breadcrumbs'] = array();

        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );

        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('accounts/income_statement', 'user_token=' . $this->session->data['user_token'], true)
        );

        // URLs للـ AJAX
        $data['generate_url'] = $this->url->link('accounts/income_statement/generate', 'user_token=' . $this->session->data['user_token'], true);
        $data['compare_url'] = $this->url->link('accounts/income_statement/compare', 'user_token=' . $this->session->data['user_token'], true);
        $data['export_url'] = $this->url->link('accounts/income_statement/export', 'user_token=' . $this->session->data['user_token'], true);
        $data['drill_down_url'] = $this->url->link('accounts/income_statement/drill_down', 'user_token=' . $this->session->data['user_token'], true);
        $data['analytics_url'] = $this->url->link('accounts/income_statement/getAnalytics', 'user_token=' . $this->session->data['user_token'], true);

        $data['user_token'] = $this->session->data['user_token'];

        // فلاتر متقدمة
        $this->load->model('localisation/branch');
        $this->load->model('accounts/cost_center');

        $data['branches'] = array();
        $data['branches'][] = array(
            'branch_id' => '',
            'name' => $this->language->get('text_all_branches')
        );
        $branches = $this->model_localisation_branch->getBranches();
        foreach ($branches as $branch) {
            $data['branches'][] = array(
                'branch_id' => $branch['branch_id'],
                'name' => $branch['name']
            );
        }

        $data['cost_centers'] = array();
        $data['cost_centers'][] = array(
            'cost_center_id' => '',
            'name' => $this->language->get('text_all_cost_centers')
        );
        $cost_centers = $this->model_accounts_cost_center->getCostCenters();
        foreach ($cost_centers as $cost_center) {
            $data['cost_centers'][] = array(
                'cost_center_id' => $cost_center['cost_center_id'],
                'name' => $cost_center['name']
            );
        }

        // تحميل قوائم البيانات
        $this->load->model('accounts/cost_center');
        $data['cost_centers'] = $this->model_accounts_cost_center->getCostCenters();

        $this->load->model('setting/branch');
        $data['branches'] = $this->model_setting_branch->getBranches();

        // التواريخ الافتراضية
        $data['default_date_from'] = date('Y-01-01');
        $data['default_date_to'] = date('Y-m-d');

        if (isset($this->error['warning'])) {
            $data['error_warning'] = $this->error['warning'];
        } else {
            $data['error_warning'] = '';
        }

        if (isset($this->session->data['success'])) {
            $data['success'] = $this->session->data['success'];
            unset($this->session->data['success']);
        } else {
            $data['success'] = '';
        }

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/income_statement', $data));
    }

    /**
     * تصدير قائمة الدخل (محدث بالخدمات المركزية)
     */
    public function export() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/income_statement') ||
            !$this->user->hasKey('accounting_income_statement_export')) {

            $this->central_service->logActivity('unauthorized_export', 'accounts',
                $this->language->get('log_unauthorized_export_income_statement'), [
                'user_id' => $this->user->getId(),
                'action' => 'export_income_statement'
            ]);

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/income_statement');
        $this->load->model('accounts/income_statement');

        if (!isset($this->session->data['income_statement_data'])) {
            $this->session->data['error'] = $this->language->get('error_no_data');
            $this->response->redirect($this->url->link('accounts/income_statement', 'user_token=' . $this->session->data['user_token'], true));
            return;
        }

        $format = isset($this->request->get['format']) ? $this->db->escape($this->request->get['format']) : 'excel';
        $report_data = $this->session->data['income_statement_data'];
        $filter_data = $this->session->data['income_statement_filter'] ?? array();

        // تسجيل عملية التصدير
        $this->central_service->logActivity('export', 'accounts',
            $this->language->get('log_export_income_statement') . ' - ' . strtoupper($format), [
            'user_id' => $this->user->getId(),
            'format' => $format,
            'period' => ($filter_data['date_start'] ?? '') . ' - ' . ($filter_data['date_end'] ?? '')
        ]);

        // إرسال إشعار للمحاسب الرئيسي
        $this->central_service->sendNotification(
            'income_statement_exported',
            $this->language->get('text_export_income_statement'),
            $this->language->get('text_income_statement_exported_notification') . ' ' . $this->language->get('text_format') . ' ' . strtoupper($format) . ' ' . $this->language->get('text_by') . ' ' . $this->user->getFirstName(),
            [$this->config->get('config_chief_accountant_id')],
            [
                'format' => $format,
                'period' => ($filter_data['date_start'] ?? '') . ' - ' . ($filter_data['date_end'] ?? ''),
                'user_name' => $this->user->getFirstName() . ' ' . $this->user->getLastName()
            ]
        );

        switch ($format) {
            case 'excel':
                $this->exportToExcel($report_data, $filter_data);
                break;
            case 'pdf':
                $this->exportToPdf($report_data, $filter_data);
                break;
            case 'csv':
                $this->exportToCsv($report_data, $filter_data);
                break;
            default:
                $this->exportToExcel($report_data, $filter_data);
        }
    }

    /**
     * مقارنة قائمة الدخل بين فترتين (محدث بالخدمات المركزية)
     */
    public function compare() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/income_statement') ||
            !$this->user->hasKey('accounting_income_statement_compare')) {

            $this->central_service->logActivity('unauthorized_compare', 'accounts',
                $this->language->get('log_unauthorized_compare_income_statement'), [
                'user_id' => $this->user->getId(),
                'action' => 'compare_income_statement'
            ]);

            $json['error'] = $this->language->get('error_permission');
            $this->response->addHeader('Content-Type: application/json');
            $this->response->setOutput(json_encode($json));
            return;
        }

        $this->load->language('accounts/income_statement');
        $this->load->model('accounts/income_statement');

        $json = array();

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateCompareForm()) {
            try {
                $compare_data = $this->prepareCompareData();
                $comparison_result = $this->model_accounts_income_statement->compareIncomeStatements($compare_data);

                // تسجيل عملية المقارنة
                $this->central_service->logActivity('compare', 'accounts',
                    $this->language->get('log_compare_income_statement_periods'), [
                    'user_id' => $this->user->getId(),
                    'period1' => $compare_data['period1'],
                    'period2' => $compare_data['period2']
                ]);

                $json['success'] = $this->language->get('text_success_compare');
                $json['comparison_data'] = $comparison_result;
            } catch (Exception $e) {
                $json['error'] = $e->getMessage();
            }
        } else {
            $json['error'] = $this->language->get('error_form');
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    private function exportToExcel($report_data, $filter_data) {
        $filename = 'income_statement_' . date('Y-m-d') . '.xls';

        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        echo '<table border="1">';
        echo '<tr><th colspan="2">' . $this->language->get('heading_title') . '</th></tr>';
        echo '<tr><th>' . $this->language->get('text_period') . '</th><td>' . ($filter_data['date_from'] ?? '') . ' - ' . ($filter_data['date_to'] ?? '') . '</td></tr>';

        // الإيرادات
        echo '<tr><th colspan="2">' . $this->language->get('text_revenues') . '</th></tr>';
        if (isset($report_data['revenues'])) {
            foreach ($report_data['revenues'] as $revenue) {
                echo '<tr><td>' . $revenue['account_name'] . '</td><td>' . number_format($revenue['amount'], 2) . '</td></tr>';
            }
        }
        echo '<tr><th>' . $this->language->get('text_total_revenues') . '</th><td>' . number_format($report_data['total_revenues'] ?? 0, 2) . '</td></tr>';

        // المصروفات
        echo '<tr><th colspan="2">' . $this->language->get('text_expenses') . '</th></tr>';
        if (isset($report_data['expenses'])) {
            foreach ($report_data['expenses'] as $expense) {
                echo '<tr><td>' . $expense['account_name'] . '</td><td>' . number_format($expense['amount'], 2) . '</td></tr>';
            }
        }
        echo '<tr><th>' . $this->language->get('text_total_expenses') . '</th><td>' . number_format($report_data['total_expenses'] ?? 0, 2) . '</td></tr>';

        // صافي الدخل
        echo '<tr><th>' . $this->language->get('text_net_income') . '</th><td>' . number_format($report_data['net_income'] ?? 0, 2) . '</td></tr>';
        echo '</table>';
        exit;
    }

    private function exportToPdf($report_data, $filter_data) {
        require_once(DIR_SYSTEM . 'library/tcpdf/tcpdf.php');

        $pdf = new TCPDF('P', 'mm', 'A4', true, 'UTF-8');
        $pdf->SetCreator('AYM ERP System');
        $pdf->SetAuthor($this->config->get('config_name'));
        $pdf->SetTitle($this->language->get('heading_title'));

        $pdf->AddPage();
        $pdf->SetFont('dejavusans', 'B', 16);
        $pdf->Cell(0, 10, $this->language->get('heading_title'), 0, 1, 'C');

        $pdf->SetFont('dejavusans', '', 10);
        $html = '<table border="1" cellpadding="4">';
        $html .= '<tr><th colspan="2">' . $this->language->get('text_period') . ': ' . ($filter_data['date_from'] ?? '') . ' - ' . ($filter_data['date_to'] ?? '') . '</th></tr>';

        // الإيرادات
        $html .= '<tr style="background-color:#f0f0f0;"><th colspan="2">' . $this->language->get('text_revenues') . '</th></tr>';
        if (isset($report_data['revenues'])) {
            foreach ($report_data['revenues'] as $revenue) {
                $html .= '<tr><td>' . $revenue['account_name'] . '</td><td>' . number_format($revenue['amount'], 2) . '</td></tr>';
            }
        }
        $html .= '<tr style="background-color:#e0e0e0;"><th>' . $this->language->get('text_total_revenues') . '</th><td>' . number_format($report_data['total_revenues'] ?? 0, 2) . '</td></tr>';

        // المصروفات
        $html .= '<tr style="background-color:#f0f0f0;"><th colspan="2">' . $this->language->get('text_expenses') . '</th></tr>';
        if (isset($report_data['expenses'])) {
            foreach ($report_data['expenses'] as $expense) {
                $html .= '<tr><td>' . $expense['account_name'] . '</td><td>' . number_format($expense['amount'], 2) . '</td></tr>';
            }
        }
        $html .= '<tr style="background-color:#e0e0e0;"><th>' . $this->language->get('text_total_expenses') . '</th><td>' . number_format($report_data['total_expenses'] ?? 0, 2) . '</td></tr>';

        // صافي الدخل
        $html .= '<tr style="background-color:#d0d0d0;"><th>' . $this->language->get('text_net_income') . '</th><td>' . number_format($report_data['net_income'] ?? 0, 2) . '</td></tr>';
        $html .= '</table>';

        $pdf->writeHTML($html, true, false, true, false, '');
        $pdf->Output('income_statement_' . date('Y-m-d') . '.pdf', 'D');
        exit;
    }

    private function exportToCsv($report_data, $filter_data) {
        $filename = 'income_statement_' . date('Y-m-d') . '.csv';

        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $output = fopen('php://output', 'w');

        // إضافة BOM للدعم العربي
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

        // رؤوس الأعمدة
        fputcsv($output, array(
            $this->language->get('heading_title'),
            $this->language->get('text_period') . ': ' . ($filter_data['date_from'] ?? '') . ' - ' . ($filter_data['date_to'] ?? '')
        ));

        // الإيرادات
        fputcsv($output, array($this->language->get('text_revenues'), ''));
        if (isset($report_data['revenues'])) {
            foreach ($report_data['revenues'] as $revenue) {
                fputcsv($output, array($revenue['account_name'], number_format($revenue['amount'], 2)));
            }
        }
        fputcsv($output, array($this->language->get('text_total_revenues'), number_format($report_data['total_revenues'] ?? 0, 2)));

        // المصروفات
        fputcsv($output, array($this->language->get('text_expenses'), ''));
        if (isset($report_data['expenses'])) {
            foreach ($report_data['expenses'] as $expense) {
                fputcsv($output, array($expense['account_name'], number_format($expense['amount'], 2)));
            }
        }
        fputcsv($output, array($this->language->get('text_total_expenses'), number_format($report_data['total_expenses'] ?? 0, 2)));

        // صافي الدخل
        fputcsv($output, array($this->language->get('text_net_income'), number_format($report_data['net_income'] ?? 0, 2)));

        fclose($output);
        exit;
    }

    /**
     * التحليل المتقدم لقائمة الدخل مع النسب المالية والمقارنات
     */
    public function advancedAnalysis() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/income_statement') ||
            !$this->user->hasKey('accounting_income_statement_analysis')) {

            $this->central_service->logActivity('unauthorized_access', 'accounts',
                $this->language->get('log_unauthorized_access_advanced_income_statement'), [
                'user_id' => $this->user->getId(),
                'ip_address' => $this->request->server['REMOTE_ADDR']
            ]);

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/income_statement');
        $this->document->setTitle($this->language->get('heading_title') . ' - ' . $this->language->get('text_advanced_analysis'));
        $this->load->model('accounts/income_statement');

        // إضافة CSS و JavaScript المتقدم للتحليل
        $this->document->addStyle('view/stylesheet/accounts/income_statement_analysis.css');
        $this->document->addScript('view/javascript/accounts/income_statement_analysis.js');
        $this->document->addScript('view/javascript/jquery/chart.min.js');

        if ($this->request->server['REQUEST_METHOD'] == 'POST') {
            try {
                $date_start = $this->request->post['date_start'];
                $date_end = $this->request->post['date_end'];

                // إعداد فترة المقارنة إذا طُلبت
                $comparison_period = null;
                if (!empty($this->request->post['comparison_period'])) {
                    $comparison_period = array(
                        'date_start' => $this->request->post['comparison_date_start'],
                        'date_end' => $this->request->post['comparison_date_end']
                    );
                }

                // تسجيل إنشاء التحليل المتقدم
                $this->central_service->logActivity('generate_advanced_income_analysis', 'accounts',
                    $this->language->get('log_generate_advanced_income_analysis') . ' ' . $this->language->get('text_for_period') . ': ' . $date_start . ' ' . $this->language->get('text_to') . ' ' . $date_end, [
                    'user_id' => $this->user->getId(),
                    'date_start' => $date_start,
                    'date_end' => $date_end,
                    'has_comparison' => !empty($comparison_period)
                ]);

                // الحصول على التحليل المتقدم
                $advanced_analysis = $this->model_accounts_income_statement->getAdvancedIncomeAnalysis(
                    $date_start, $date_end, $comparison_period
                );

                // إرسال إشعار للإدارة المالية
                $this->central_service->sendNotification(
                    'advanced_income_analysis_generated',
                    $this->language->get('text_advanced_income_analysis'),
                    $this->language->get('text_advanced_income_analysis_generated') . ' ' . $this->language->get('text_for_period') . ' ' . $date_start . ' ' . $this->language->get('text_to') . ' ' . $date_end,
                    [$this->config->get('config_cfo_id'), $this->config->get('config_chief_accountant_id')],
                    [
                        'date_start' => $date_start,
                        'date_end' => $date_end,
                        'net_income' => $advanced_analysis['current_period']['net_income'],
                        'profit_margin' => $advanced_analysis['financial_ratios']['net_profit_margin'],
                        'generated_by' => $this->user->getFirstName() . ' ' . $this->user->getLastName()
                    ]
                );

                $this->session->data['success'] = $this->language->get('text_success_analysis');
                $this->session->data['advanced_analysis'] = $advanced_analysis;

                $this->response->redirect($this->url->link('accounts/income_statement/analysisView', 'user_token=' . $this->session->data['user_token'], true));
            } catch (Exception $e) {
                $this->error['warning'] = $e->getMessage();
            }
        }

        // عرض نموذج التحليل المتقدم
        $data['breadcrumbs'] = array();
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('accounts/income_statement', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_advanced_analysis'),
            'href' => $this->url->link('accounts/income_statement/advancedAnalysis', 'user_token=' . $this->session->data['user_token'], true)
        );

        $data['action'] = $this->url->link('accounts/income_statement/advancedAnalysis', 'user_token=' . $this->session->data['user_token'], true);
        $data['cancel'] = $this->url->link('accounts/income_statement', 'user_token=' . $this->session->data['user_token'], true);

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/income_statement_advanced_analysis_form', $data));
    }

    /**
     * عرض التحليل المتقدم
     */
    public function analysisView() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/income_statement') ||
            !$this->user->hasKey('accounting_income_statement_view')) {

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/income_statement');

        if (!isset($this->session->data['advanced_analysis'])) {
            $this->session->data['error'] = $this->language->get('error_no_analysis_data');
            $this->response->redirect($this->url->link('accounts/income_statement', 'user_token=' . $this->session->data['user_token'], true));
            return;
        }

        // تسجيل عرض التحليل
        $this->central_service->logActivity('view_advanced_income_analysis', 'accounts',
            $this->language->get('log_view_advanced_income_analysis'), [
            'user_id' => $this->user->getId()
        ]);

        $data = $this->session->data['advanced_analysis'];

        $data['breadcrumbs'] = array();
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('accounts/income_statement', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_analysis_view'),
            'href' => $this->url->link('accounts/income_statement/analysisView', 'user_token=' . $this->session->data['user_token'], true)
        );

        $data['export_excel'] = $this->url->link('accounts/income_statement/export', 'user_token=' . $this->session->data['user_token'] . '&format=excel', true);
        $data['export_pdf'] = $this->url->link('accounts/income_statement/export', 'user_token=' . $this->session->data['user_token'] . '&format=pdf', true);
        $data['print'] = $this->url->link('accounts/income_statement/analysisPrint', 'user_token=' . $this->session->data['user_token'], true);
        $data['back'] = $this->url->link('accounts/income_statement', 'user_token=' . $this->session->data['user_token'], true);

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/income_statement_advanced_analysis_view', $data));
    }

    /**
     * طباعة التحليل المتقدم
     */
    public function analysisPrint() {
        $this->load->language('accounts/income_statement');

        if (!isset($this->session->data['advanced_analysis'])) {
            $this->response->redirect($this->url->link('accounts/income_statement', 'user_token=' . $this->session->data['user_token'], true));
            return;
        }

        $data = $this->session->data['advanced_analysis'];
        $data['company_name'] = $this->config->get('config_name');
        $data['generated_date'] = date('Y-m-d H:i:s');
        $data['generated_by'] = $this->user->getFirstName() . ' ' . $this->user->getLastName();

        $this->response->setOutput($this->load->view('accounts/income_statement_advanced_analysis_print', $data));
    }

    /**
     * دالة تنظيف المخرجات (CONSTITUTIONAL REQUIREMENT)
     * Sanitize all output data to prevent XSS attacks
     */
    private function sanitizeOutputData($data) {
        if (is_array($data)) {
            foreach ($data as $key => $value) {
                $data[$key] = $this->sanitizeOutputData($value);
            }
        } elseif (is_string($data)) {
            $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
        }
        return $data;
    }

    /**
     * Enterprise Grade Plus Security Enhancements
     */
    private function validateInputData($data) {
        $validated = array();

        if (isset($data['date_start'])) {
            $validated['date_start'] = date('Y-m-d', strtotime($data['date_start']));
        }

        if (isset($data['date_end'])) {
            $validated['date_end'] = date('Y-m-d', strtotime($data['date_end']));
        }

        if (isset($data['branch_id'])) {
            $validated['branch_id'] = (int)$data['branch_id'];
        }

        if (isset($data['cost_center_id'])) {
            $validated['cost_center_id'] = (int)$data['cost_center_id'];
        }

        return $validated;
    }

    /**
     * Rate Limiting Implementation
     */
    private function checkRateLimit() {
        $ip = $this->request->server['REMOTE_ADDR'];
        $user_id = $this->user->getId();

        return $this->central_service->checkRateLimit('income_statement_generation', $ip, $user_id, 30, 3600); // 30 requests per hour
    }

    /**
     * Performance Optimization - Memory Management
     */
    private function optimizeMemoryUsage() {
        ini_set('memory_limit', '512M');
        ini_set('max_execution_time', 300); // 5 minutes for complex calculations
    }

    /**
     * Enhanced Error Handling
     */
    private function handleError($error, $context = array()) {
        $this->central_service->logActivity('error', 'accounts', $error, array_merge([
            'user_id' => $this->user->getId(),
            'ip_address' => $this->request->server['REMOTE_ADDR'],
            'timestamp' => date('Y-m-d H:i:s')
        ], $context));

        return array('error' => $this->language->get('error_general'));
    }

    /**
     * Cache Management for Performance
     */
    private function getCacheKey($params) {
        return 'income_statement_' . md5(serialize($params));
    }

    private function getCachedData($key) {
        // Implementation depends on caching system
        return false; // Placeholder
    }

    private function setCachedData($key, $data, $ttl = 3600) {
        // Implementation depends on caching system
        return true; // Placeholder
    }
}
