<?php

/**
 * SCSSPHP
 *
 * @copyright 2012-2020 Leaf Corcoran
 *
 * @license http://opensource.org/licenses/MIT MIT
 *
 * @link http://scssphp.github.io/scssphp
 */

namespace ScssPhp\ScssPhp;

/**
 * Base node
 *
 * <AUTHOR> Pang <<EMAIL>>
 *
 * @internal
 */
abstract class Node
{
    /**
     * @var string
     */
    public $type;

    /**
     * @var int
     */
    public $sourceIndex;

    /**
     * @var int|null
     */
    public $sourceLine;

    /**
     * @var int|null
     */
    public $sourceColumn;
}
