URI.AllowedSchemes
TYPE: lookup
--DEFAULT--
array (
  'http' => true,
  'https' => true,
  'mailto' => true,
  'ftp' => true,
  'nntp' => true,
  'news' => true,
  'tel' => true,
)
--DESCRIPTION--
Whitelist that defines the schemes that a URI is allowed to have.  This
prevents XSS attacks from using pseudo-schemes like javascript or mocha.
There is also support for the <code>data</code> and <code>file</code>
URI schemes, but they are not enabled by default.
--# vim: et sw=4 sts=4
