{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="submit" form="inventory\manufacturer-form" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fas fa-save"></i></button>
        <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-light"><i class="fas fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    
    <div class="card">
      <div class="card-header"><i class="fas fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}chart-bar{% elif screen_type == 'settings' %}cog{% else %}pencil-alt{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="card-body">
        <form id="inventory\manufacturer-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-add">{{ text_add }}</label>
            <div class="col-sm-10">
              <input type="text" name="add" value="{{ add }}" placeholder="{{ text_add }}" id="input-add" class="form-control" />
              {% if error_add %}
                <div class="invalid-feedback">{{ error_add }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-code_prefix">{{ text_code_prefix }}</label>
            <div class="col-sm-10">
              <input type="text" name="code_prefix" value="{{ code_prefix }}" placeholder="{{ text_code_prefix }}" id="input-code_prefix" class="form-control" />
              {% if error_code_prefix %}
                <div class="invalid-feedback">{{ error_code_prefix }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-commission_rate">{{ text_commission_rate }}</label>
            <div class="col-sm-10">
              <input type="text" name="commission_rate" value="{{ commission_rate }}" placeholder="{{ text_commission_rate }}" id="input-commission_rate" class="form-control" />
              {% if error_commission_rate %}
                <div class="invalid-feedback">{{ error_commission_rate }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-countries">{{ text_countries }}</label>
            <div class="col-sm-10">
              <input type="text" name="countries" value="{{ countries }}" placeholder="{{ text_countries }}" id="input-countries" class="form-control" />
              {% if error_countries %}
                <div class="invalid-feedback">{{ error_countries }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-credit_limit">{{ text_credit_limit }}</label>
            <div class="col-sm-10">
              <input type="text" name="credit_limit" value="{{ credit_limit }}" placeholder="{{ text_credit_limit }}" id="input-credit_limit" class="form-control" />
              {% if error_credit_limit %}
                <div class="invalid-feedback">{{ error_credit_limit }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-delete">{{ text_delete }}</label>
            <div class="col-sm-10">
              <input type="text" name="delete" value="{{ delete }}" placeholder="{{ text_delete }}" id="input-delete" class="form-control" />
              {% if error_delete %}
                <div class="invalid-feedback">{{ error_delete }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_name">{{ text_error_name }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_name" value="{{ error_name }}" placeholder="{{ text_error_name }}" id="input-error_name" class="form-control" />
              {% if error_error_name %}
                <div class="invalid-feedback">{{ error_error_name }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_name">{{ text_filter_name }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_name" value="{{ filter_name }}" placeholder="{{ text_filter_name }}" id="input-filter_name" class="form-control" />
              {% if error_filter_name %}
                <div class="invalid-feedback">{{ error_filter_name }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_status">{{ text_filter_status }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_status" value="{{ filter_status }}" placeholder="{{ text_filter_status }}" id="input-filter_status" class="form-control" />
              {% if error_filter_status %}
                <div class="invalid-feedback">{{ error_filter_status }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="invalid-feedback">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-languages">{{ text_languages }}</label>
            <div class="col-sm-10">
              <input type="text" name="languages" value="{{ languages }}" placeholder="{{ text_languages }}" id="input-languages" class="form-control" />
              {% if error_languages %}
                <div class="invalid-feedback">{{ error_languages }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-manufacturer_description">{{ text_manufacturer_description }}</label>
            <div class="col-sm-10">
              <input type="text" name="manufacturer_description" value="{{ manufacturer_description }}" placeholder="{{ text_manufacturer_description }}" id="input-manufacturer_description" class="form-control" />
              {% if error_manufacturer_description %}
                <div class="invalid-feedback">{{ error_manufacturer_description }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-manufacturer_store">{{ text_manufacturer_store }}</label>
            <div class="col-sm-10">
              <input type="text" name="manufacturer_store" value="{{ manufacturer_store }}" placeholder="{{ text_manufacturer_store }}" id="input-manufacturer_store" class="form-control" />
              {% if error_manufacturer_store %}
                <div class="invalid-feedback">{{ error_manufacturer_store }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-manufacturers">{{ text_manufacturers }}</label>
            <div class="col-sm-10">
              <input type="text" name="manufacturers" value="{{ manufacturers }}" placeholder="{{ text_manufacturers }}" id="input-manufacturers" class="form-control" />
              {% if error_manufacturers %}
                <div class="invalid-feedback">{{ error_manufacturers }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-order">{{ text_order }}</label>
            <div class="col-sm-10">
              <input type="text" name="order" value="{{ order }}" placeholder="{{ text_order }}" id="input-order" class="form-control" />
              {% if error_order %}
                <div class="invalid-feedback">{{ error_order }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-payment_terms">{{ text_payment_terms }}</label>
            <div class="col-sm-10">
              <input type="text" name="payment_terms" value="{{ payment_terms }}" placeholder="{{ text_payment_terms }}" id="input-payment_terms" class="form-control" />
              {% if error_payment_terms %}
                <div class="invalid-feedback">{{ error_payment_terms }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-selected">{{ text_selected }}</label>
            <div class="col-sm-10">
              <input type="text" name="selected" value="{{ selected }}" placeholder="{{ text_selected }}" id="input-selected" class="form-control" />
              {% if error_selected %}
                <div class="invalid-feedback">{{ error_selected }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort">{{ text_sort }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort" value="{{ sort }}" placeholder="{{ text_sort }}" id="input-sort" class="form-control" />
              {% if error_sort %}
                <div class="invalid-feedback">{{ error_sort }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-status">{{ text_status }}</label>
            <div class="col-sm-10">
              <input type="text" name="status" value="{{ status }}" placeholder="{{ text_status }}" id="input-status" class="form-control" />
              {% if error_status %}
                <div class="invalid-feedback">{{ error_status }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-stores">{{ text_stores }}</label>
            <div class="col-sm-10">
              <input type="text" name="stores" value="{{ stores }}" placeholder="{{ text_stores }}" id="input-stores" class="form-control" />
              {% if error_stores %}
                <div class="invalid-feedback">{{ error_stores }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_form">{{ text_text_form }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_form" value="{{ text_form }}" placeholder="{{ text_text_form }}" id="input-text_form" class="form-control" />
              {% if error_text_form %}
                <div class="invalid-feedback">{{ error_text_form }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="invalid-feedback">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}