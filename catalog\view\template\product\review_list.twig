{% if reviews %}
  {% for review in reviews %}
    <table class="table table-striped table-bordered">
      <tr>
        <td style="width: 50%;"><strong>{{ review.author }}</strong></td>
        <td class="text-end">{{ review.date_added }}</td>
      </tr>
      <tr>
        <td colspan="2"><p>{{ review.text }}</p>
          <div class="rating">
            {% for i in 1..5 %}
              {% if review.rating < i %}
                <span class="fa-stack"><i class="fa-regular fa-star fa-stack-1x"></i></span>
              {% else %}
                <span class="fa-stack"><i class="fa-solid fa-star fa-stack-1x"></i><i class="fa-regular fa-star fa-stack-1x"></i></span>
              {% endif %}
            {% endfor %}
          </div>
        </td>
      </tr>
    </table>
  {% endfor %}
  <div class="text-end">{{ pagination }}</div>
{% else %}
  <p class="text-center">{{ text_no_results }}</p>
{% endif %}