<?php
/**
 * Finance Dashboard Model
 * نموذج لوحة القيادة للمحاسبة والمالية
 * 
 * يحتوي على جميع الاستعلامات المتعلقة بـ:
 * - أرصدة النقد والبنوك
 * - التنبؤ الديناميكي بالتدفق النقدي
 * - أعمار الذمم المدينة
 * - المصروفات مقابل الميزانية
 * - الربحية حسب الأبعاد
 * - حالة فواتير ETA الإلكترونية
 * - التسويات المعلقة
 * - ملخص ضريبة القيمة المضافة
 */

class ModelDashboardFinance extends Model {
    
    /**
     * Check if table exists
     */
    private function tableExists($table_name) {
        $sql = "SHOW TABLES LIKE '" . DB_PREFIX . $table_name . "'";
        $query = $this->db->query($sql);
        return $query->num_rows > 0;
    }
    
    /**
     * Safe query execution with error handling
     */
    private function safeQuery($sql, $default_value = null) {
        try {
            $query = $this->db->query($sql);
            return $query;
        } catch (Exception $e) {
            error_log('Finance Dashboard Query Error: ' . $e->getMessage() . ' SQL: ' . $sql);
            if ($default_value === null) {
                // إنشاء كائن استعلام فارغ مؤقت
                return (object)['row' => [], 'rows' => [], 'num_rows' => 0];
            }
            return $default_value;
        }
    }
    
    /**
     * Get Cash & Bank Balances
     * أرصدة النقد والبنوك
     */
    public function getCashBankBalances($filters = array()) {
        $branch_filter = isset($filters['branch_id']) ? $filters['branch_id'] : null;
        
        $where_clause = "WHERE 1=1";
        
        // Branch filter
        if ($branch_filter) {
            $where_clause .= " AND a.branch_id = " . (int)$branch_filter;
        }
        
        if (!$this->tableExists('account')) {
            return array(
                'total_cash_balance' => 0,
                'total_bank_balance' => 0,
                'total_balance' => 0,
                'accounts_list' => array()
            );
        }
        
        $sql = "SELECT 
                    a.account_id,
                    a.name as account_name,
                    a.account_number,
                    a.account_type,
                    COALESCE(a.balance, 0) as current_balance,
                    a.currency_code,
                    b.name as bank_name
                FROM " . DB_PREFIX . "account a
                LEFT JOIN " . DB_PREFIX . "bank b ON a.bank_id = b.bank_id
                " . $where_clause . "
                AND a.status = 1
                ORDER BY a.account_type, a.name";
        
        $query = $this->safeQuery($sql);
        $accounts_list = $query && isset($query->rows) ? $query->rows : array();
        
        // Calculate totals
        $total_cash_balance = 0;
        $total_bank_balance = 0;
        
        foreach ($accounts_list as $account) {
            if ($account['account_type'] == 'cash') {
                $total_cash_balance += $account['current_balance'];
            } elseif ($account['account_type'] == 'bank') {
                $total_bank_balance += $account['current_balance'];
            }
        }
        
        return array(
            'total_cash_balance' => $total_cash_balance,
            'total_bank_balance' => $total_bank_balance,
            'total_balance' => $total_cash_balance + $total_bank_balance,
            'accounts_list' => $accounts_list
        );
    }
    
    /**
     * Get Dynamic Cash Flow Forecast
     * التنبؤ الديناميكي بالتدفق النقدي
     */
    public function getDynamicCashFlowForecast($filters = array()) {
        $forecast_days = isset($filters['forecast_days']) ? (int)$filters['forecast_days'] : 90;
        $branch_filter = isset($filters['branch_id']) ? $filters['branch_id'] : null;
        
        $where_clause = "WHERE 1=1";
        
        // Branch filter
        if ($branch_filter) {
            $where_clause .= " AND o.store_id = " . (int)$branch_filter;
        }
        
        if (!$this->tableExists('order')) {
            return array(
                'forecast_data' => array(),
                'total_inflow' => 0,
                'total_outflow' => 0,
                'net_cash_flow' => 0,
                'ending_balance' => 0
            );
        }
        
        // Get current balance
        $current_balance = $this->getCurrentBalance($filters);
        
        // Get expected cash inflows (orders to be paid)
        $sql = "SELECT 
                    DATE(o.date_added) as forecast_date,
                    COALESCE(SUM(o.total), 0) as expected_inflow
                FROM " . DB_PREFIX . "order o
                " . $where_clause . "
                AND o.order_status_id IN (2, 3, 5) -- Processing, Shipped, Complete
                AND o.payment_method LIKE '%cod%' -- Cash on delivery
                AND o.date_added BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL " . $forecast_days . " DAY)
                GROUP BY DATE(o.date_added)
                ORDER BY forecast_date";
        
        $query = $this->safeQuery($sql);
        $inflows = $query && isset($query->rows) ? $query->rows : array();
        
        // Get expected cash outflows (payments to suppliers)
        $outflows = $this->getExpectedOutflows($filters, $forecast_days);
        
        // Combine and calculate forecast
        $forecast_data = array();
        $total_inflow = 0;
        $total_outflow = 0;
        $running_balance = $current_balance;
        
        // Create forecast for each day
        for ($i = 0; $i < $forecast_days; $i++) {
            $date = date('Y-m-d', strtotime("+$i days"));
            
            $inflow = 0;
            $outflow = 0;
            
            // Find inflow for this date
            foreach ($inflows as $flow) {
                if ($flow['forecast_date'] == $date) {
                    $inflow = $flow['expected_inflow'];
                    break;
                }
            }
            
            // Find outflow for this date
            foreach ($outflows as $flow) {
                if ($flow['forecast_date'] == $date) {
                    $outflow = $flow['expected_outflow'];
                    break;
                }
            }
            
            $net_flow = $inflow - $outflow;
            $running_balance += $net_flow;
            
            $forecast_data[] = array(
                'date' => $date,
                'inflow' => $inflow,
                'outflow' => $outflow,
                'net_flow' => $net_flow,
                'running_balance' => $running_balance
            );
            
            $total_inflow += $inflow;
            $total_outflow += $outflow;
        }
        
        return array(
            'forecast_data' => $forecast_data,
            'total_inflow' => $total_inflow,
            'total_outflow' => $total_outflow,
            'net_cash_flow' => $total_inflow - $total_outflow,
            'ending_balance' => $running_balance
        );
    }
    
    /**
     * Get Accounts Receivable Aging
     * أعمار الذمم المدينة
     */
    public function getAccountsReceivableAging($filters = array()) {
        $branch_filter = isset($filters['branch_id']) ? $filters['branch_id'] : null;
        
        $where_clause = "WHERE 1=1";
        
        // Branch filter
        if ($branch_filter) {
            $where_clause .= " AND o.store_id = " . (int)$branch_filter;
        }
        
        if (!$this->tableExists('order')) {
            return array(
                'total_receivables' => 0,
                'aging_buckets' => array(),
                'overdue_amount' => 0,
                'overdue_count' => 0,
                'avg_days_outstanding' => 0
            );
        }
        
        $sql = "SELECT 
                    o.order_id,
                    o.customer_id,
                    o.total,
                    o.date_added,
                    DATEDIFF(CURDATE(), o.date_added) as days_outstanding,
                    CASE 
                        WHEN DATEDIFF(CURDATE(), o.date_added) <= 30 THEN '0-30'
                        WHEN DATEDIFF(CURDATE(), o.date_added) <= 60 THEN '31-60'
                        WHEN DATEDIFF(CURDATE(), o.date_added) <= 90 THEN '61-90'
                        ELSE '90+'
                    END as aging_bucket,
                    CONCAT(c.firstname, ' ', c.lastname) as customer_name,
                    c.email as customer_email
                FROM " . DB_PREFIX . "order o
                LEFT JOIN " . DB_PREFIX . "customer c ON o.customer_id = c.customer_id
                " . $where_clause . "
                AND o.order_status_id IN (3, 5) -- Shipped, Complete
                AND o.payment_method NOT LIKE '%cod%' -- Not cash on delivery
                AND o.total > 0
                ORDER BY days_outstanding DESC";
        
        $query = $this->safeQuery($sql);
        $receivables = $query && isset($query->rows) ? $query->rows : array();
        
        // Calculate aging buckets
        $aging_buckets = array(
            '0-30' => array('amount' => 0, 'count' => 0),
            '31-60' => array('amount' => 0, 'count' => 0),
            '61-90' => array('amount' => 0, 'count' => 0),
            '90+' => array('amount' => 0, 'count' => 0)
        );
        
        $total_receivables = 0;
        $overdue_amount = 0;
        $overdue_count = 0;
        $total_days = 0;
        $total_orders = 0;
        
        foreach ($receivables as $receivable) {
            $bucket = $receivable['aging_bucket'];
            $amount = $receivable['total'];
            
            $aging_buckets[$bucket]['amount'] += $amount;
            $aging_buckets[$bucket]['count']++;
            
            $total_receivables += $amount;
            $total_days += $receivable['days_outstanding'];
            $total_orders++;
            
            if ($receivable['days_outstanding'] > 30) {
                $overdue_amount += $amount;
                $overdue_count++;
            }
        }
        
        $avg_days_outstanding = $total_orders > 0 ? $total_days / $total_orders : 0;
        
        return array(
            'total_receivables' => $total_receivables,
            'aging_buckets' => $aging_buckets,
            'overdue_amount' => $overdue_amount,
            'overdue_count' => $overdue_count,
            'avg_days_outstanding' => round($avg_days_outstanding, 1)
        );
    }
    
    /**
     * Get Expense vs Budget
     * المصروفات مقابل الميزانية
     */
    public function getExpenseVsBudget($filters = array()) {
        $date_filter = isset($filters['date_range']) ? $filters['date_range'] : 'month';
        $branch_filter = isset($filters['branch_id']) ? $filters['branch_id'] : null;
        
        $where_clause = "WHERE 1=1";
        
        // Date filter
        switch($date_filter) {
            case 'month':
                $where_clause .= " AND YEAR(e.date) = YEAR(CURDATE()) AND MONTH(e.date) = MONTH(CURDATE())";
                break;
            case 'quarter':
                $where_clause .= " AND e.date >= DATE_SUB(CURDATE(), INTERVAL 90 DAY)";
                break;
            case 'year':
                $where_clause .= " AND YEAR(e.date) = YEAR(CURDATE())";
                break;
        }
        
        // Branch filter
        if ($branch_filter) {
            $where_clause .= " AND e.branch_id = " . (int)$branch_filter;
        }
        
        if (!$this->tableExists('expense')) {
            return array(
                'total_expenses' => 0,
                'total_budget' => 0,
                'variance' => 0,
                'variance_percentage' => 0,
                'expenses_by_category' => array()
            );
        }
        
        $sql = "SELECT 
                    ec.category_id,
                    ec.name as category_name,
                    COALESCE(SUM(e.amount), 0) as actual_expense,
                    COALESCE(ec.monthly_budget, 0) as budget_amount,
                    COALESCE(SUM(e.amount) - ec.monthly_budget, 0) as variance,
                    CASE 
                        WHEN ec.monthly_budget > 0 
                        THEN ((SUM(e.amount) - ec.monthly_budget) / ec.monthly_budget) * 100 
                        ELSE 0 
                    END as variance_percentage
                FROM " . DB_PREFIX . "expense e
                LEFT JOIN " . DB_PREFIX . "expense_category ec ON e.category_id = ec.category_id
                " . $where_clause . "
                GROUP BY ec.category_id
                ORDER BY actual_expense DESC";
        
        $query = $this->safeQuery($sql);
        $expenses_by_category = $query && isset($query->rows) ? $query->rows : array();
        
        // Calculate totals
        $total_expenses = 0;
        $total_budget = 0;
        
        foreach ($expenses_by_category as $category) {
            $total_expenses += $category['actual_expense'];
            $total_budget += $category['budget_amount'];
        }
        
        $variance = $total_expenses - $total_budget;
        $variance_percentage = $total_budget > 0 ? ($variance / $total_budget) * 100 : 0;
        
        return array(
            'total_expenses' => $total_expenses,
            'total_budget' => $total_budget,
            'variance' => $variance,
            'variance_percentage' => round($variance_percentage, 2),
            'expenses_by_category' => $expenses_by_category
        );
    }
    
    /**
     * Get Profitability by Dimension
     * الربحية حسب الأبعاد
     */
    public function getProfitabilityByDimension($filters = array()) {
        $dimension = isset($filters['dimension']) ? $filters['dimension'] : 'product'; // product, customer, branch, project
        $date_filter = isset($filters['date_range']) ? $filters['date_range'] : 'month';
        $branch_filter = isset($filters['branch_id']) ? $filters['branch_id'] : null;
        
        $where_clause = "WHERE 1=1";
        
        // Date filter
        switch($date_filter) {
            case 'month':
                $where_clause .= " AND YEAR(o.date_added) = YEAR(CURDATE()) AND MONTH(o.date_added) = MONTH(CURDATE())";
                break;
            case 'quarter':
                $where_clause .= " AND o.date_added >= DATE_SUB(CURDATE(), INTERVAL 90 DAY)";
                break;
            case 'year':
                $where_clause .= " AND YEAR(o.date_added) = YEAR(CURDATE())";
                break;
        }
        
        // Branch filter
        if ($branch_filter) {
            $where_clause .= " AND o.store_id = " . (int)$branch_filter;
        }
        
        if (!$this->tableExists('order') || !$this->tableExists('order_product')) {
            return array();
        }
        
        switch($dimension) {
            case 'product':
                return $this->getProfitabilityByProduct($where_clause);
            case 'customer':
                return $this->getProfitabilityByCustomer($where_clause);
            case 'branch':
                return $this->getProfitabilityByBranch($where_clause);
            case 'project':
                return $this->getProfitabilityByProject($where_clause);
            default:
                return array();
        }
    }
    
    /**
     * Get ETA E-Invoicing Status
     * حالة فواتير ETA الإلكترونية
     */
    public function getETAInvoicingStatus($filters = array()) {
        $date_filter = isset($filters['date_range']) ? $filters['date_range'] : 'month';
        $branch_filter = isset($filters['branch_id']) ? $filters['branch_id'] : null;
        
        $where_clause = "WHERE 1=1";
        
        // Date filter
        switch($date_filter) {
            case 'month':
                $where_clause .= " AND YEAR(ei.date_created) = YEAR(CURDATE()) AND MONTH(ei.date_created) = MONTH(CURDATE())";
                break;
            case 'quarter':
                $where_clause .= " AND ei.date_created >= DATE_SUB(CURDATE(), INTERVAL 90 DAY)";
                break;
            case 'year':
                $where_clause .= " AND YEAR(ei.date_created) = YEAR(CURDATE())";
                break;
        }
        
        // Branch filter
        if ($branch_filter) {
            $where_clause .= " AND ei.branch_id = " . (int)$branch_filter;
        }
        
        if (!$this->tableExists('eta_invoice')) {
            return array(
                'total_invoices' => 0,
                'accepted_invoices' => 0,
                'rejected_invoices' => 0,
                'pending_invoices' => 0,
                'acceptance_rate' => 0,
                'total_amount' => 0,
                'status_distribution' => array()
            );
        }
        
        $sql = "SELECT 
                    ei.status,
                    COUNT(*) as invoice_count,
                    COALESCE(SUM(ei.total_amount), 0) as total_amount
                FROM " . DB_PREFIX . "eta_invoice ei
                " . $where_clause . "
                GROUP BY ei.status";
        
        $query = $this->safeQuery($sql);
        $status_data = $query && isset($query->rows) ? $query->rows : array();
        
        $total_invoices = 0;
        $accepted_invoices = 0;
        $rejected_invoices = 0;
        $pending_invoices = 0;
        $total_amount = 0;
        $status_distribution = array();
        
        foreach ($status_data as $status) {
            $total_invoices += $status['invoice_count'];
            $total_amount += $status['total_amount'];
            
            switch($status['status']) {
                case 'accepted':
                    $accepted_invoices += $status['invoice_count'];
                    break;
                case 'rejected':
                    $rejected_invoices += $status['invoice_count'];
                    break;
                case 'pending':
                    $pending_invoices += $status['invoice_count'];
                    break;
            }
            
            $status_distribution[] = array(
                'status' => $status['status'],
                'count' => $status['invoice_count'],
                'amount' => $status['total_amount']
            );
        }
        
        $acceptance_rate = $total_invoices > 0 ? ($accepted_invoices / $total_invoices) * 100 : 0;
        
        return array(
            'total_invoices' => $total_invoices,
            'accepted_invoices' => $accepted_invoices,
            'rejected_invoices' => $rejected_invoices,
            'pending_invoices' => $pending_invoices,
            'acceptance_rate' => round($acceptance_rate, 2),
            'total_amount' => $total_amount,
            'status_distribution' => $status_distribution
        );
    }
    
    /**
     * Get Pending Reconciliations
     * التسويات المعلقة
     */
    public function getPendingReconciliations($filters = array()) {
        $branch_filter = isset($filters['branch_id']) ? $filters['branch_id'] : null;
        
        $where_clause = "WHERE 1=1";
        
        // Branch filter
        if ($branch_filter) {
            $where_clause .= " AND r.branch_id = " . (int)$branch_filter;
        }
        
        if (!$this->tableExists('reconciliation')) {
            return array(
                'pending_reconciliations' => 0,
                'total_amount' => 0,
                'oldest_pending_days' => 0,
                'reconciliations_list' => array()
            );
        }
        
        $sql = "SELECT 
                    r.reconciliation_id,
                    r.account_id,
                    r.description,
                    r.amount,
                    r.date_created,
                    DATEDIFF(CURDATE(), r.date_created) as days_pending,
                    a.name as account_name
                FROM " . DB_PREFIX . "reconciliation r
                LEFT JOIN " . DB_PREFIX . "account a ON r.account_id = a.account_id
                " . $where_clause . "
                AND r.status = 'pending'
                ORDER BY r.date_created ASC";
        
        $query = $this->safeQuery($sql);
        $reconciliations_list = $query && isset($query->rows) ? $query->rows : array();
        
        $total_amount = 0;
        $oldest_pending_days = 0;
        
        foreach ($reconciliations_list as $reconciliation) {
            $total_amount += $reconciliation['amount'];
            if ($reconciliation['days_pending'] > $oldest_pending_days) {
                $oldest_pending_days = $reconciliation['days_pending'];
            }
        }
        
        return array(
            'pending_reconciliations' => count($reconciliations_list),
            'total_amount' => $total_amount,
            'oldest_pending_days' => $oldest_pending_days,
            'reconciliations_list' => $reconciliations_list
        );
    }
    
    /**
     * Get VAT Summary
     * ملخص ضريبة القيمة المضافة
     */
    public function getVATSummary($filters = array()) {
        $date_filter = isset($filters['date_range']) ? $filters['date_range'] : 'month';
        $branch_filter = isset($filters['branch_id']) ? $filters['branch_id'] : null;
        
        $where_clause = "WHERE 1=1";
        
        // Date filter
        switch($date_filter) {
            case 'month':
                $where_clause .= " AND YEAR(o.date_added) = YEAR(CURDATE()) AND MONTH(o.date_added) = MONTH(CURDATE())";
                break;
            case 'quarter':
                $where_clause .= " AND o.date_added >= DATE_SUB(CURDATE(), INTERVAL 90 DAY)";
                break;
            case 'year':
                $where_clause .= " AND YEAR(o.date_added) = YEAR(CURDATE())";
                break;
        }
        
        // Branch filter
        if ($branch_filter) {
            $where_clause .= " AND o.store_id = " . (int)$branch_filter;
        }
        
        if (!$this->tableExists('order')) {
            return array(
                'vat_output' => 0,
                'vat_input' => 0,
                'vat_payable' => 0,
                'vat_receivable' => 0,
                'net_vat' => 0
            );
        }
        
        // Get VAT output (from sales)
        $sql = "SELECT COALESCE(SUM(o.total * 0.14), 0) as vat_output
                FROM " . DB_PREFIX . "order o
                " . $where_clause . "
                AND o.order_status_id NOT IN (7)";
        
        $query = $this->safeQuery($sql);
        $vat_output = $query && isset($query->row['vat_output']) ? $query->row['vat_output'] : 0;
        
        // Get VAT input (from purchases - simplified)
        $vat_input = $this->getVATInput($filters);
        
        $vat_payable = $vat_output;
        $vat_receivable = $vat_input;
        $net_vat = $vat_output - $vat_input;
        
        return array(
            'vat_output' => $vat_output,
            'vat_input' => $vat_input,
            'vat_payable' => $vat_payable,
            'vat_receivable' => $vat_receivable,
            'net_vat' => $net_vat
        );
    }
    
    // Helper methods
    private function getCurrentBalance($filters) {
        $balances = $this->getCashBankBalances($filters);
        return $balances['total_balance'];
    }
    
    private function getExpectedOutflows($filters, $forecast_days) {
        // Simplified - would come from purchase orders and expense schedules
        return array();
    }
    
    private function getProfitabilityByProduct($where_clause) {
        $sql = "SELECT 
                    p.product_id,
                    p.name as product_name,
                    p.model,
                    COALESCE(SUM(op.total), 0) as total_revenue,
                    COALESCE(SUM(op.quantity * op.cost), 0) as total_cost,
                    COALESCE(SUM(op.total - (op.quantity * op.cost)), 0) as total_profit,
                    CASE 
                        WHEN SUM(op.total) > 0 
                        THEN (SUM(op.total - (op.quantity * op.cost)) / SUM(op.total)) * 100 
                        ELSE 0 
                    END as profit_margin
                FROM " . DB_PREFIX . "order_product op
                LEFT JOIN " . DB_PREFIX . "order o ON op.order_id = o.order_id
                LEFT JOIN " . DB_PREFIX . "product p ON op.product_id = p.product_id
                " . $where_clause . "
                AND o.order_status_id NOT IN (7)
                GROUP BY op.product_id
                ORDER BY total_profit DESC
                LIMIT 20";
        
        $query = $this->safeQuery($sql);
        return $query && isset($query->rows) ? $query->rows : array();
    }
    
    private function getProfitabilityByCustomer($where_clause) {
        $sql = "SELECT 
                    c.customer_id,
                    CONCAT(c.firstname, ' ', c.lastname) as customer_name,
                    c.email,
                    COALESCE(SUM(o.total), 0) as total_revenue,
                    COALESCE(SUM(od.total_cost), 0) as total_cost,
                    COALESCE(SUM(o.total - od.total_cost), 0) as total_profit,
                    CASE 
                        WHEN SUM(o.total) > 0 
                        THEN (SUM(o.total - od.total_cost) / SUM(o.total)) * 100 
                        ELSE 0 
                    END as profit_margin
                FROM " . DB_PREFIX . "order o
                LEFT JOIN " . DB_PREFIX . "customer c ON o.customer_id = c.customer_id
                LEFT JOIN (
                    SELECT 
                        order_id,
                        SUM(quantity * cost) as total_cost
                    FROM " . DB_PREFIX . "order_product
                    GROUP BY order_id
                ) od ON o.order_id = od.order_id
                " . $where_clause . "
                AND o.order_status_id NOT IN (7)
                GROUP BY o.customer_id
                ORDER BY total_profit DESC
                LIMIT 20";
        
        $query = $this->safeQuery($sql);
        return $query && isset($query->rows) ? $query->rows : array();
    }
    
    private function getProfitabilityByBranch($where_clause) {
        $sql = "SELECT 
                    s.store_id,
                    s.name as branch_name,
                    COALESCE(SUM(o.total), 0) as total_revenue,
                    COALESCE(SUM(od.total_cost), 0) as total_cost,
                    COALESCE(SUM(o.total - od.total_cost), 0) as total_profit,
                    CASE 
                        WHEN SUM(o.total) > 0 
                        THEN (SUM(o.total - od.total_cost) / SUM(o.total)) * 100 
                        ELSE 0 
                    END as profit_margin
                FROM " . DB_PREFIX . "order o
                LEFT JOIN " . DB_PREFIX . "store s ON o.store_id = s.store_id
                LEFT JOIN (
                    SELECT 
                        order_id,
                        SUM(quantity * cost) as total_cost
                    FROM " . DB_PREFIX . "order_product
                    GROUP BY order_id
                ) od ON o.order_id = od.order_id
                " . $where_clause . "
                AND o.order_status_id NOT IN (7)
                GROUP BY o.store_id
                ORDER BY total_profit DESC";
        
        $query = $this->safeQuery($sql);
        return $query && isset($query->rows) ? $query->rows : array();
    }
    
    private function getProfitabilityByProject($where_clause) {
        // Simplified - would come from project tracking system
        return array();
    }
    
    private function getVATInput($filters) {
        // Simplified calculation
        return 0;
    }
} 