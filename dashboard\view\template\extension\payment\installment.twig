{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="submit" form="extension\payment\installment-form" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fas fa-save"></i></button>
        <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-light"><i class="fas fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    
    <div class="card">
      <div class="card-header"><i class="fas fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}chart-bar{% elif screen_type == 'settings' %}cog{% else %}pencil-alt{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="card-body">
        <form id="extension\payment\installment-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_customer">{{ text_filter_customer }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_customer" value="{{ filter_customer }}" placeholder="{{ text_filter_customer }}" id="input-filter_customer" class="form-control" />
              {% if error_filter_customer %}
                <div class="invalid-feedback">{{ error_filter_customer }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_date_from">{{ text_filter_date_from }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_date_from" value="{{ filter_date_from }}" placeholder="{{ text_filter_date_from }}" id="input-filter_date_from" class="form-control" />
              {% if error_filter_date_from %}
                <div class="invalid-feedback">{{ error_filter_date_from }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_date_to">{{ text_filter_date_to }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_date_to" value="{{ filter_date_to }}" placeholder="{{ text_filter_date_to }}" id="input-filter_date_to" class="form-control" />
              {% if error_filter_date_to %}
                <div class="invalid-feedback">{{ error_filter_date_to }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_provider">{{ text_filter_provider }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_provider" value="{{ filter_provider }}" placeholder="{{ text_filter_provider }}" id="input-filter_provider" class="form-control" />
              {% if error_filter_provider %}
                <div class="invalid-feedback">{{ error_filter_provider }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_status">{{ text_filter_status }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_status" value="{{ filter_status }}" placeholder="{{ text_filter_status }}" id="input-filter_status" class="form-control" />
              {% if error_filter_status %}
                <div class="invalid-feedback">{{ error_filter_status }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="invalid-feedback">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-installment_plans">{{ text_installment_plans }}</label>
            <div class="col-sm-10">
              <input type="text" name="installment_plans" value="{{ installment_plans }}" placeholder="{{ text_installment_plans }}" id="input-installment_plans" class="form-control" />
              {% if error_installment_plans %}
                <div class="invalid-feedback">{{ error_installment_plans }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-pagination">{{ text_pagination }}</label>
            <div class="col-sm-10">
              <input type="text" name="pagination" value="{{ pagination }}" placeholder="{{ text_pagination }}" id="input-pagination" class="form-control" />
              {% if error_pagination %}
                <div class="invalid-feedback">{{ error_pagination }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-results">{{ text_results }}</label>
            <div class="col-sm-10">
              <input type="text" name="results" value="{{ results }}" placeholder="{{ text_results }}" id="input-results" class="form-control" />
              {% if error_results %}
                <div class="invalid-feedback">{{ error_results }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_list">{{ text_text_list }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_list" value="{{ text_list }}" placeholder="{{ text_text_list }}" id="input-text_list" class="form-control" />
              {% if error_text_list %}
                <div class="invalid-feedback">{{ error_text_list }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="invalid-feedback">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}