<?php
/**
 * إدارة المنتجات المتقدمة في المخزون (Advanced Product Management)
 *
 * الهدف: فصل إدارة المنتجات عن المتجر وجعل المخزون هو الأساس (مثل Odoo)
 * الميزات: تسعير 5 مستويات، خيارات مرتبطة بالوحدات، باركود متقدم، تكويد ذكي
 * التفوق: أفضل من Odoo وWooCommerce وSAP في سهولة الاستخدام والميزات
 *
 * <AUTHOR> Team
 * @version 1.0
 * @since 2024
 */

class ControllerInventoryProduct extends Controller {

    private $error = array();
    private $central_service;

    public function __construct($registry) {
        parent::__construct($registry);

        // تحميل الخدمات المركزية
        $this->load->model('core/central_service_manager');
        $this->central_service = $this->model_core_central_service_manager;
    }

    public function index() {
        // تحميل اللغة
        $this->load->language('inventory/product');

        // تحديد عنوان الصفحة
        $this->document->setTitle($this->language->get('heading_title'));

        // تحميل النماذج المطلوبة
        $this->load->model('inventory/product');
        $this->load->model('inventory/category');
        $this->load->model('inventory/manufacturer');
        $this->load->model('inventory/units');

        // معالجة الطلبات
        $this->getList();
    }

    public function add() {
        // تحميل اللغة
        $this->load->language('inventory/product');

        // تحديد عنوان الصفحة
        $this->document->setTitle($this->language->get('heading_title'));

        // تحميل النماذج المطلوبة
        $this->load->model('inventory/product');

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm()) {
            $product_id = $this->model_inventory_product->addProduct($this->request->post);

            $this->session->data['success'] = $this->language->get('text_success');

            $url = '';

            if (isset($this->request->get['filter_name'])) {
                $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
            }

            if (isset($this->request->get['filter_model'])) {
                $url .= '&filter_model=' . urlencode(html_entity_decode($this->request->get['filter_model'], ENT_QUOTES, 'UTF-8'));
            }

            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('inventory/product', 'user_token=' . $this->session->data['user_token'] . $url, true));
        }

        $this->getForm();
    }

    public function edit() {
        // تحميل اللغة
        $this->load->language('inventory/product');

        // تحديد عنوان الصفحة
        $this->document->setTitle($this->language->get('heading_title'));

        // تحميل النماذج المطلوبة
        $this->load->model('inventory/product');

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm()) {
            $this->model_inventory_product->editProduct($this->request->get['product_id'], $this->request->post);

            $this->session->data['success'] = $this->language->get('text_success');

            $url = '';

            if (isset($this->request->get['filter_name'])) {
                $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
            }

            if (isset($this->request->get['filter_model'])) {
                $url .= '&filter_model=' . urlencode(html_entity_decode($this->request->get['filter_model'], ENT_QUOTES, 'UTF-8'));
            }

            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('inventory/product', 'user_token=' . $this->session->data['user_token'] . $url, true));
        }

        $this->getForm();
    }

    public function delete() {
        // تحميل اللغة
        $this->load->language('inventory/product');

        // تحديد عنوان الصفحة
        $this->document->setTitle($this->language->get('heading_title'));

        // تحميل النماذج المطلوبة
        $this->load->model('inventory/product');

        if (isset($this->request->post['selected']) && $this->validateDelete()) {
            foreach ($this->request->post['selected'] as $product_id) {
                $this->model_inventory_product->deleteProduct($product_id);
            }

            $this->session->data['success'] = $this->language->get('text_success');

            $url = '';

            if (isset($this->request->get['filter_name'])) {
                $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
            }

            if (isset($this->request->get['filter_model'])) {
                $url .= '&filter_model=' . urlencode(html_entity_decode($this->request->get['filter_model'], ENT_QUOTES, 'UTF-8'));
            }

            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('inventory/product', 'user_token=' . $this->session->data['user_token'] . $url, true));
        }

        $this->getList();
    }

    protected function getList() {
        // معالجة الفلاتر
        if (isset($this->request->get['filter_name'])) {
            $filter_name = $this->request->get['filter_name'];
        } else {
            $filter_name = '';
        }

        if (isset($this->request->get['filter_model'])) {
            $filter_model = $this->request->get['filter_model'];
        } else {
            $filter_model = '';
        }

        if (isset($this->request->get['filter_status'])) {
            $filter_status = $this->request->get['filter_status'];
        } else {
            $filter_status = '';
        }

        if (isset($this->request->get['sort'])) {
            $sort = $this->request->get['sort'];
        } else {
            $sort = 'pd.name';
        }

        if (isset($this->request->get['order'])) {
            $order = $this->request->get['order'];
        } else {
            $order = 'ASC';
        }

        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_model'])) {
            $url .= '&filter_model=' . urlencode(html_entity_decode($this->request->get['filter_model'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_status'])) {
            $url .= '&filter_status=' . $this->request->get['filter_status'];
        }

        if (isset($this->request->get['sort'])) {
            $url .= '&sort=' . $this->request->get['sort'];
        }

        if (isset($this->request->get['order'])) {
            $url .= '&order=' . $this->request->get['order'];
        }

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        $data['breadcrumbs'] = array();

        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );

        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('inventory/product', 'user_token=' . $this->session->data['user_token'] . $url, true)
        );

        $data['add'] = $this->url->link('inventory/product/add', 'user_token=' . $this->session->data['user_token'] . $url, true);
        $data['delete'] = $this->url->link('inventory/product/delete', 'user_token=' . $this->session->data['user_token'] . $url, true);

        $data['products'] = array();

        $filter_data = array(
            'filter_name'   => $filter_name,
            'filter_model'  => $filter_model,
            'filter_status' => $filter_status,
            'sort'          => $sort,
            'order'         => $order,
            'start'         => ($page - 1) * $this->config->get('config_limit_admin'),
            'limit'         => $this->config->get('config_limit_admin')
        );

        $product_total = $this->model_inventory_product->getTotalProducts($filter_data);

        $results = $this->model_inventory_product->getProducts($filter_data);

        foreach ($results as $result) {
            $data['products'][] = array(
                'product_id'    => $result['product_id'],
                'image'         => $result['image'],
                'name'          => $result['name'],
                'model'         => $result['model'],
                'price'         => $result['price'],
                'quantity'      => $result['quantity'],
                'status'        => $result['status'] ? $this->language->get('text_enabled') : $this->language->get('text_disabled'),
                'edit'          => $this->url->link('inventory/product/edit', 'user_token=' . $this->session->data['user_token'] . '&product_id=' . $result['product_id'] . $url, true)
            );
        }

        $data['user_token'] = $this->session->data['user_token'];

        if (isset($this->error['warning'])) {
            $data['error_warning'] = $this->error['warning'];
        } else {
            $data['error_warning'] = '';
        }

        if (isset($this->session->data['success'])) {
            $data['success'] = $this->session->data['success'];

            unset($this->session->data['success']);
        } else {
            $data['success'] = '';
        }

        if (isset($this->request->post['selected'])) {
            $data['selected'] = (array)$this->request->post['selected'];
        } else {
            $data['selected'] = array();
        }

        $url = '';

        if ($order == 'ASC') {
            $url .= '&order=DESC';
        } else {
            $url .= '&order=ASC';
        }

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        $data['sort_name'] = $this->url->link('inventory/product', 'user_token=' . $this->session->data['user_token'] . '&sort=pd.name' . $url, true);
        $data['sort_model'] = $this->url->link('inventory/product', 'user_token=' . $this->session->data['user_token'] . '&sort=p.model' . $url, true);
        $data['sort_price'] = $this->url->link('inventory/product', 'user_token=' . $this->session->data['user_token'] . '&sort=p.price' . $url, true);
        $data['sort_quantity'] = $this->url->link('inventory/product', 'user_token=' . $this->session->data['user_token'] . '&sort=p.quantity' . $url, true);
        $data['sort_status'] = $this->url->link('inventory/product', 'user_token=' . $this->session->data['user_token'] . '&sort=p.status' . $url, true);
        $data['sort_order'] = $this->url->link('inventory/product', 'user_token=' . $this->session->data['user_token'] . '&sort=p.sort_order' . $url, true);

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_model'])) {
            $url .= '&filter_model=' . urlencode(html_entity_decode($this->request->get['filter_model'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_status'])) {
            $url .= '&filter_status=' . $this->request->get['filter_status'];
        }

        if (isset($this->request->get['sort'])) {
            $url .= '&sort=' . $this->request->get['sort'];
        }

        if (isset($this->request->get['order'])) {
            $url .= '&order=' . $this->request->get['order'];
        }

        $pagination = new Pagination();
        $pagination->total = $product_total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit_admin');
        $pagination->url = $this->url->link('inventory/product', 'user_token=' . $this->session->data['user_token'] . $url . '&page={page}', true);

        $data['pagination'] = $pagination->render();

        $data['results'] = sprintf($this->language->get('text_pagination'), ($product_total) ? (($page - 1) * $this->config->get('config_limit_admin')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit_admin')) > ($product_total - $this->config->get('config_limit_admin'))) ? $product_total : ((($page - 1) * $this->config->get('config_limit_admin')) + $this->config->get('config_limit_admin')), $product_total, ceil($product_total / $this->config->get('config_limit_admin')));

        $data['filter_name'] = $filter_name;
        $data['filter_model'] = $filter_model;
        $data['filter_status'] = $filter_status;

        $data['sort'] = $sort;
        $data['order'] = $order;

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('inventory/product_list', $data));
    }

    protected function getForm() {
        $data['text_form'] = !isset($this->request->get['product_id']) ? $this->language->get('text_add') : $this->language->get('text_edit');

        if (isset($this->error['warning'])) {
            $data['error_warning'] = $this->error['warning'];
        } else {
            $data['error_warning'] = '';
        }

        if (isset($this->error['name'])) {
            $data['error_name'] = $this->error['name'];
        } else {
            $data['error_name'] = array();
        }

        if (isset($this->error['model'])) {
            $data['error_model'] = $this->error['model'];
        } else {
            $data['error_model'] = '';
        }

        $url = '';

        if (isset($this->request->get['filter_name'])) {
            $url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['filter_model'])) {
            $url .= '&filter_model=' . urlencode(html_entity_decode($this->request->get['filter_model'], ENT_QUOTES, 'UTF-8'));
        }

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        $data['breadcrumbs'] = array();

        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );

        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('inventory/product', 'user_token=' . $this->session->data['user_token'] . $url, true)
        );

        if (!isset($this->request->get['product_id'])) {
            $data['action'] = $this->url->link('inventory/product/add', 'user_token=' . $this->session->data['user_token'] . $url, true);
        } else {
            $data['action'] = $this->url->link('inventory/product/edit', 'user_token=' . $this->session->data['user_token'] . '&product_id=' . $this->request->get['product_id'] . $url, true);
        }

        $data['cancel'] = $this->url->link('inventory/product', 'user_token=' . $this->session->data['user_token'] . $url, true);

        if (isset($this->request->get['product_id']) && ($this->request->server['REQUEST_METHOD'] != 'POST')) {
            $product_info = $this->model_inventory_product->getProduct($this->request->get['product_id']);
        }

        $data['user_token'] = $this->session->data['user_token'];

        // تحميل اللغات
        $this->load->model('localisation/language');
        $data['languages'] = $this->model_localisation_language->getLanguages();

        // معلومات المنتج الأساسية
        if (isset($this->request->post['product_description'])) {
            $data['product_description'] = $this->request->post['product_description'];
        } elseif (isset($this->request->get['product_id'])) {
            $data['product_description'] = $this->model_inventory_product->getProductDescriptions($this->request->get['product_id']);
        } else {
            $data['product_description'] = array();
        }

        if (isset($this->request->post['model'])) {
            $data['model'] = $this->request->post['model'];
        } elseif (!empty($product_info)) {
            $data['model'] = $product_info['model'];
        } else {
            $data['model'] = '';
        }

        if (isset($this->request->post['sku'])) {
            $data['sku'] = $this->request->post['sku'];
        } elseif (!empty($product_info)) {
            $data['sku'] = $product_info['sku'];
        } else {
            $data['sku'] = '';
        }

        // التصنيفات
        $this->load->model('inventory/category');
        $data['categories'] = $this->model_inventory_category->getCategories();

        if (isset($this->request->post['product_category'])) {
            $data['product_categories'] = $this->request->post['product_category'];
        } elseif (isset($this->request->get['product_id'])) {
            $data['product_categories'] = $this->model_inventory_product->getProductCategories($this->request->get['product_id']);
        } else {
            $data['product_categories'] = array();
        }

        // العلامات التجارية
        $this->load->model('inventory/manufacturer');
        $data['manufacturers'] = $this->model_inventory_manufacturer->getManufacturers();

        if (isset($this->request->post['manufacturer_id'])) {
            $data['manufacturer_id'] = $this->request->post['manufacturer_id'];
        } elseif (!empty($product_info)) {
            $data['manufacturer_id'] = $product_info['manufacturer_id'];
        } else {
            $data['manufacturer_id'] = 0;
        }

        // الوحدات
        $this->load->model('inventory/units');
        $data['units'] = $this->model_inventory_units->getUnits();

        if (isset($this->request->post['product_units'])) {
            $data['product_units'] = $this->request->post['product_units'];
        } elseif (isset($this->request->get['product_id'])) {
            $data['product_units'] = $this->model_inventory_product->getProductUnits($this->request->get['product_id']);
        } else {
            $data['product_units'] = array();
        }

        // التسعير المتطور (5 مستويات)
        if (isset($this->request->post['product_pricing'])) {
            $data['product_pricing'] = $this->request->post['product_pricing'];
        } elseif (isset($this->request->get['product_id'])) {
            $data['product_pricing'] = $this->model_inventory_product->getProductPricing($this->request->get['product_id']);
        } else {
            $data['product_pricing'] = array();
        }

        // الباركود المتقدم
        if (isset($this->request->post['product_barcodes'])) {
            $data['product_barcodes'] = $this->request->post['product_barcodes'];
        } elseif (isset($this->request->get['product_id'])) {
            $data['product_barcodes'] = $this->model_inventory_product->getProductBarcodes($this->request->get['product_id']);
        } else {
            $data['product_barcodes'] = array();
        }

        // الخيارات المرتبطة بالوحدات (ميزة فريدة)
        $this->load->model('catalog/option');
        $data['options'] = $this->model_catalog_option->getOptions();

        if (isset($this->request->post['product_options'])) {
            $data['product_options'] = $this->request->post['product_options'];
        } elseif (isset($this->request->get['product_id'])) {
            $data['product_options'] = $this->model_inventory_product->getProductOptions($this->request->get['product_id']);
        } else {
            $data['product_options'] = array();
        }

        // الباقات والخصومات
        if (isset($this->request->post['product_bundles'])) {
            $data['product_bundles'] = $this->request->post['product_bundles'];
        } elseif (isset($this->request->get['product_id'])) {
            $data['product_bundles'] = $this->model_inventory_product->getProductBundles($this->request->get['product_id']);
        } else {
            $data['product_bundles'] = array();
        }

        if (isset($this->request->post['product_discounts'])) {
            $data['product_discounts'] = $this->request->post['product_discounts'];
        } elseif (isset($this->request->get['product_id'])) {
            $data['product_discounts'] = $this->model_inventory_product->getProductDiscounts($this->request->get['product_id']);
        } else {
            $data['product_discounts'] = array();
        }

        // المقاسات والألوان
        $data['product_sizes'] = $this->model_inventory_product->getProductSizes();
        $data['product_colors'] = $this->model_inventory_product->getProductColors();

        // متغيرات المنتج (المقاسات والألوان المرتبطة)
        if (isset($this->request->post['product_variants'])) {
            $data['product_variants'] = $this->request->post['product_variants'];
        } elseif (isset($this->request->get['product_id'])) {
            $data['product_variants'] = $this->model_inventory_product->getProductVariants($this->request->get['product_id']);
        } else {
            $data['product_variants'] = array();
        }

        // الحالة والإعدادات
        if (isset($this->request->post['status'])) {
            $data['status'] = $this->request->post['status'];
        } elseif (!empty($product_info)) {
            $data['status'] = $product_info['status'];
        } else {
            $data['status'] = 1;
        }

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('inventory/product_form', $data));
    }

    protected function validateForm() {
        if (!$this->user->hasPermission('modify', 'inventory/product')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }

        foreach ($this->request->post['product_description'] as $language_id => $value) {
            if ((utf8_strlen($value['name']) < 1) || (utf8_strlen($value['name']) > 255)) {
                $this->error['name'][$language_id] = $this->language->get('error_name');
            }
        }

        if ((utf8_strlen($this->request->post['model']) < 1) || (utf8_strlen($this->request->post['model']) > 64)) {
            $this->error['model'] = $this->language->get('error_model');
        }

        return !$this->error;
    }

    protected function validateDelete() {
        if (!$this->user->hasPermission('modify', 'inventory/product')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }

        return !$this->error;
    }

    /**
     * AJAX: البحث في المنتجات
     */
    public function autocomplete() {
        $json = array();

        if (isset($this->request->get['filter_name'])) {
            $this->load->model('inventory/product');

            $filter_data = array(
                'filter_name' => $this->request->get['filter_name'],
                'start'       => 0,
                'limit'       => 5
            );

            $results = $this->model_inventory_product->getProducts($filter_data);

            foreach ($results as $result) {
                $json[] = array(
                    'product_id' => $result['product_id'],
                    'name'       => strip_tags(html_entity_decode($result['name'], ENT_QUOTES, 'UTF-8')),
                    'model'      => $result['model'],
                    'option'     => $result['option'],
                    'price'      => $result['price']
                );
            }
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * AJAX: توليد كود المنتج التلقائي
     */
    public function generateCode() {
        $this->load->model('inventory/product');

        $category_id = isset($this->request->get['category_id']) ? $this->request->get['category_id'] : 0;
        $manufacturer_id = isset($this->request->get['manufacturer_id']) ? $this->request->get['manufacturer_id'] : 0;

        $code = $this->model_inventory_product->generateProductCode($category_id, $manufacturer_id);

        $json = array('code' => $code);

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * إدارة المقاسات - إضافة مقاس جديد
     */
    public function addSize() {
        $this->load->language('inventory/product');

        $json = array();

        if (!$this->user->hasPermission('modify', 'inventory/product')) {
            $json['error'] = $this->language->get('error_permission');
        } else {
            $this->load->model('inventory/product');

            if (isset($this->request->post['size_name']) && !empty($this->request->post['size_name'])) {
                $size_data = array(
                    'name' => $this->request->post['size_name'],
                    'code' => isset($this->request->post['size_code']) ? $this->request->post['size_code'] : '',
                    'sort_order' => isset($this->request->post['sort_order']) ? (int)$this->request->post['sort_order'] : 0,
                    'status' => 1
                );

                $size_id = $this->model_inventory_product->addProductSize($size_data);

                if ($size_id) {
                    $json['success'] = $this->language->get('text_success');
                    $json['size_id'] = $size_id;

                    // تسجيل العملية في نظام التدقيق
                    $this->central_service->logActivity('product_size_add', 'inventory',
                        'تم إضافة مقاس جديد: ' . $this->request->post['size_name'], $size_id);
                } else {
                    $json['error'] = $this->language->get('error_size_add');
                }
            } else {
                $json['error'] = $this->language->get('error_size_name_required');
            }
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * إدارة المقاسات - تعديل مقاس
     */
    public function editSize() {
        $this->load->language('inventory/product');

        $json = array();

        if (!$this->user->hasPermission('modify', 'inventory/product')) {
            $json['error'] = $this->language->get('error_permission');
        } else {
            $this->load->model('inventory/product');

            $size_id = isset($this->request->get['size_id']) ? (int)$this->request->get['size_id'] : 0;

            if ($size_id && isset($this->request->post['size_name']) && !empty($this->request->post['size_name'])) {
                $size_data = array(
                    'name' => $this->request->post['size_name'],
                    'code' => isset($this->request->post['size_code']) ? $this->request->post['size_code'] : '',
                    'sort_order' => isset($this->request->post['sort_order']) ? (int)$this->request->post['sort_order'] : 0,
                    'status' => isset($this->request->post['status']) ? (int)$this->request->post['status'] : 1
                );

                $result = $this->model_inventory_product->editProductSize($size_id, $size_data);

                if ($result) {
                    $json['success'] = $this->language->get('text_success');

                    // تسجيل العملية في نظام التدقيق
                    $this->central_service->logActivity('product_size_edit', 'inventory',
                        'تم تعديل المقاس: ' . $this->request->post['size_name'], $size_id);
                } else {
                    $json['error'] = $this->language->get('error_size_edit');
                }
            } else {
                $json['error'] = $this->language->get('error_size_data_invalid');
            }
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * إدارة المقاسات - حذف مقاس
     */
    public function deleteSize() {
        $this->load->language('inventory/product');

        $json = array();

        if (!$this->user->hasPermission('modify', 'inventory/product')) {
            $json['error'] = $this->language->get('error_permission');
        } else {
            $this->load->model('inventory/product');

            $size_id = isset($this->request->get['size_id']) ? (int)$this->request->get['size_id'] : 0;

            if ($size_id) {
                // التحقق من عدم استخدام المقاس في منتجات
                $products_count = $this->model_inventory_product->getTotalProductsBySize($size_id);

                if ($products_count > 0) {
                    $json['error'] = sprintf($this->language->get('error_size_in_use'), $products_count);
                } else {
                    $size_info = $this->model_inventory_product->getProductSize($size_id);
                    $result = $this->model_inventory_product->deleteProductSize($size_id);

                    if ($result) {
                        $json['success'] = $this->language->get('text_success');

                        // تسجيل العملية في نظام التدقيق
                        $this->central_service->logActivity('product_size_delete', 'inventory',
                            'تم حذف المقاس: ' . ($size_info['name'] ?? 'غير معروف'), $size_id);
                    } else {
                        $json['error'] = $this->language->get('error_size_delete');
                    }
                }
            } else {
                $json['error'] = $this->language->get('error_size_id_invalid');
            }
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * إدارة الألوان - إضافة لون جديد
     */
    public function addColor() {
        $this->load->language('inventory/product');

        $json = array();

        if (!$this->user->hasPermission('modify', 'inventory/product')) {
            $json['error'] = $this->language->get('error_permission');
        } else {
            $this->load->model('inventory/product');

            if (isset($this->request->post['color_name']) && !empty($this->request->post['color_name'])) {
                $color_data = array(
                    'name' => $this->request->post['color_name'],
                    'code' => isset($this->request->post['color_code']) ? $this->request->post['color_code'] : '',
                    'hex_value' => isset($this->request->post['hex_value']) ? $this->request->post['hex_value'] : '#000000',
                    'sort_order' => isset($this->request->post['sort_order']) ? (int)$this->request->post['sort_order'] : 0,
                    'status' => 1
                );

                $color_id = $this->model_inventory_product->addProductColor($color_data);

                if ($color_id) {
                    $json['success'] = $this->language->get('text_success');
                    $json['color_id'] = $color_id;

                    // تسجيل العملية في نظام التدقيق
                    $this->central_service->logActivity('product_color_add', 'inventory',
                        'تم إضافة لون جديد: ' . $this->request->post['color_name'], $color_id);
                } else {
                    $json['error'] = $this->language->get('error_color_add');
                }
            } else {
                $json['error'] = $this->language->get('error_color_name_required');
            }
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * إدارة الألوان - تعديل لون
     */
    public function editColor() {
        $this->load->language('inventory/product');

        $json = array();

        if (!$this->user->hasPermission('modify', 'inventory/product')) {
            $json['error'] = $this->language->get('error_permission');
        } else {
            $this->load->model('inventory/product');

            $color_id = isset($this->request->get['color_id']) ? (int)$this->request->get['color_id'] : 0;

            if ($color_id && isset($this->request->post['color_name']) && !empty($this->request->post['color_name'])) {
                $color_data = array(
                    'name' => $this->request->post['color_name'],
                    'code' => isset($this->request->post['color_code']) ? $this->request->post['color_code'] : '',
                    'hex_value' => isset($this->request->post['hex_value']) ? $this->request->post['hex_value'] : '#000000',
                    'sort_order' => isset($this->request->post['sort_order']) ? (int)$this->request->post['sort_order'] : 0,
                    'status' => isset($this->request->post['status']) ? (int)$this->request->post['status'] : 1
                );

                $result = $this->model_inventory_product->editProductColor($color_id, $color_data);

                if ($result) {
                    $json['success'] = $this->language->get('text_success');

                    // تسجيل العملية في نظام التدقيق
                    $this->central_service->logActivity('product_color_edit', 'inventory',
                        'تم تعديل اللون: ' . $this->request->post['color_name'], $color_id);
                } else {
                    $json['error'] = $this->language->get('error_color_edit');
                }
            } else {
                $json['error'] = $this->language->get('error_color_data_invalid');
            }
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * إدارة الألوان - حذف لون
     */
    public function deleteColor() {
        $this->load->language('inventory/product');

        $json = array();

        if (!$this->user->hasPermission('modify', 'inventory/product')) {
            $json['error'] = $this->language->get('error_permission');
        } else {
            $this->load->model('inventory/product');

            $color_id = isset($this->request->get['color_id']) ? (int)$this->request->get['color_id'] : 0;

            if ($color_id) {
                // التحقق من عدم استخدام اللون في منتجات
                $products_count = $this->model_inventory_product->getTotalProductsByColor($color_id);

                if ($products_count > 0) {
                    $json['error'] = sprintf($this->language->get('error_color_in_use'), $products_count);
                } else {
                    $color_info = $this->model_inventory_product->getProductColor($color_id);
                    $result = $this->model_inventory_product->deleteProductColor($color_id);

                    if ($result) {
                        $json['success'] = $this->language->get('text_success');

                        // تسجيل العملية في نظام التدقيق
                        $this->central_service->logActivity('product_color_delete', 'inventory',
                            'تم حذف اللون: ' . ($color_info['name'] ?? 'غير معروف'), $color_id);
                    } else {
                        $json['error'] = $this->language->get('error_color_delete');
                    }
                }
            } else {
                $json['error'] = $this->language->get('error_color_id_invalid');
            }
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * إدارة المتغيرات - إنشاء متغيرات المنتج (المقاس + اللون)
     */
    public function createVariant() {
        $this->load->language('inventory/product');

        $json = array();

        if (!$this->user->hasPermission('modify', 'inventory/product')) {
            $json['error'] = $this->language->get('error_permission');
        } else {
            $this->load->model('inventory/product');

            $product_id = isset($this->request->post['product_id']) ? (int)$this->request->post['product_id'] : 0;
            $size_id = isset($this->request->post['size_id']) ? (int)$this->request->post['size_id'] : 0;
            $color_id = isset($this->request->post['color_id']) ? (int)$this->request->post['color_id'] : 0;

            if ($product_id && ($size_id || $color_id)) {
                $variant_data = array(
                    'product_id' => $product_id,
                    'size_id' => $size_id,
                    'color_id' => $color_id,
                    'sku' => isset($this->request->post['variant_sku']) ? $this->request->post['variant_sku'] : '',
                    'price_modifier' => isset($this->request->post['price_modifier']) ? (float)$this->request->post['price_modifier'] : 0,
                    'quantity' => isset($this->request->post['quantity']) ? (int)$this->request->post['quantity'] : 0,
                    'image' => isset($this->request->post['image']) ? $this->request->post['image'] : '',
                    'status' => 1
                );

                // التحقق من عدم تكرار المتغير
                $existing_variant = $this->model_inventory_product->getProductVariant($product_id, $size_id, $color_id);

                if ($existing_variant) {
                    $json['error'] = $this->language->get('error_variant_exists');
                } else {
                    $variant_id = $this->model_inventory_product->addProductVariant($variant_data);

                    if ($variant_id) {
                        $json['success'] = $this->language->get('text_success');
                        $json['variant_id'] = $variant_id;

                        // تسجيل العملية في نظام التدقيق
                        $this->central_service->logActivity('product_variant_add', 'inventory',
                            'تم إضافة متغير جديد للمنتج: ' . $product_id, $variant_id);
                    } else {
                        $json['error'] = $this->language->get('error_variant_add');
                    }
                }
            } else {
                $json['error'] = $this->language->get('error_variant_data_invalid');
            }
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * الحصول على قائمة المقاسات
     */
    public function getSizes() {
        $this->load->model('inventory/product');

        $sizes = $this->model_inventory_product->getProductSizes();

        $json = array();
        foreach ($sizes as $size) {
            $json[] = array(
                'size_id' => $size['size_id'],
                'name' => $size['name'],
                'code' => $size['code'],
                'sort_order' => $size['sort_order'],
                'status' => $size['status']
            );
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * الحصول على قائمة الألوان
     */
    public function getColors() {
        $this->load->model('inventory/product');

        $colors = $this->model_inventory_product->getProductColors();

        $json = array();
        foreach ($colors as $color) {
            $json[] = array(
                'color_id' => $color['color_id'],
                'name' => $color['name'],
                'code' => $color['code'],
                'hex_value' => $color['hex_value'],
                'sort_order' => $color['sort_order'],
                'status' => $color['status']
            );
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * رفع الصور المتعددة
     */
    public function uploadMultipleImages() {
        $this->load->language('inventory/product');

        $json = array();

        if (!$this->user->hasPermission('modify', 'inventory/product')) {
            $json['error'] = $this->language->get('error_permission');
        } else {
            $this->load->model('tool/image');

            if (isset($this->request->files['images'])) {
                $uploaded_images = array();

                foreach ($this->request->files['images']['name'] as $key => $filename) {
                    if (!empty($filename)) {
                        $file_info = array(
                            'name' => $this->request->files['images']['name'][$key],
                            'type' => $this->request->files['images']['type'][$key],
                            'tmp_name' => $this->request->files['images']['tmp_name'][$key],
                            'error' => $this->request->files['images']['error'][$key],
                            'size' => $this->request->files['images']['size'][$key]
                        );

                        // التحقق من نوع الملف
                        $allowed_types = array('image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp');

                        if (!in_array($file_info['type'], $allowed_types)) {
                            $json['error'] = sprintf($this->language->get('error_image_type'), $filename);
                            break;
                        }

                        // التحقق من حجم الملف (5MB كحد أقصى)
                        if ($file_info['size'] > 5242880) {
                            $json['error'] = sprintf($this->language->get('error_image_size'), $filename);
                            break;
                        }

                        // رفع الصورة
                        $image_path = $this->uploadProductImage($file_info);

                        if ($image_path) {
                            $uploaded_images[] = array(
                                'image' => $image_path,
                                'sort_order' => count($uploaded_images),
                                'alt_text' => pathinfo($filename, PATHINFO_FILENAME)
                            );
                        } else {
                            $json['error'] = sprintf($this->language->get('error_image_upload'), $filename);
                            break;
                        }
                    }
                }

                if (empty($json['error']) && !empty($uploaded_images)) {
                    $json['success'] = $this->language->get('text_success');
                    $json['images'] = $uploaded_images;

                    // تسجيل العملية في نظام التدقيق
                    $this->central_service->logActivity('product_images_upload', 'inventory',
                        'تم رفع ' . count($uploaded_images) . ' صورة للمنتج');
                }
            } else {
                $json['error'] = $this->language->get('error_no_images');
            }
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * إعادة ترتيب الصور
     */
    public function reorderImages() {
        $this->load->language('inventory/product');

        $json = array();

        if (!$this->user->hasPermission('modify', 'inventory/product')) {
            $json['error'] = $this->language->get('error_permission');
        } else {
            $this->load->model('inventory/product');

            $product_id = isset($this->request->post['product_id']) ? (int)$this->request->post['product_id'] : 0;
            $image_order = isset($this->request->post['image_order']) ? $this->request->post['image_order'] : array();

            if ($product_id && !empty($image_order)) {
                $result = $this->model_inventory_product->reorderProductImages($product_id, $image_order);

                if ($result) {
                    $json['success'] = $this->language->get('text_success');

                    // تسجيل العملية في نظام التدقيق
                    $this->central_service->logActivity('product_images_reorder', 'inventory',
                        'تم إعادة ترتيب صور المنتج: ' . $product_id, $product_id);
                } else {
                    $json['error'] = $this->language->get('error_reorder_images');
                }
            } else {
                $json['error'] = $this->language->get('error_invalid_data');
            }
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * حذف صورة
     */
    public function deleteImage() {
        $this->load->language('inventory/product');

        $json = array();

        if (!$this->user->hasPermission('modify', 'inventory/product')) {
            $json['error'] = $this->language->get('error_permission');
        } else {
            $this->load->model('inventory/product');

            $product_id = isset($this->request->get['product_id']) ? (int)$this->request->get['product_id'] : 0;
            $image_id = isset($this->request->get['image_id']) ? (int)$this->request->get['image_id'] : 0;

            if ($product_id && $image_id) {
                $image_info = $this->model_inventory_product->getProductImage($image_id);

                if ($image_info) {
                    // حذف الملف الفعلي
                    $image_path = DIR_IMAGE . $image_info['image'];
                    if (file_exists($image_path)) {
                        unlink($image_path);
                    }

                    // حذف من قاعدة البيانات
                    $result = $this->model_inventory_product->deleteProductImage($image_id);

                    if ($result) {
                        $json['success'] = $this->language->get('text_success');

                        // تسجيل العملية في نظام التدقيق
                        $this->central_service->logActivity('product_image_delete', 'inventory',
                            'تم حذف صورة من المنتج: ' . $product_id, $image_id);
                    } else {
                        $json['error'] = $this->language->get('error_delete_image');
                    }
                } else {
                    $json['error'] = $this->language->get('error_image_not_found');
                }
            } else {
                $json['error'] = $this->language->get('error_invalid_data');
            }
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * ضغط الصور التلقائي
     */
    private function uploadProductImage($file_info) {
        if ($file_info['error'] !== UPLOAD_ERR_OK) {
            return false;
        }

        // إنشاء اسم ملف فريد
        $extension = pathinfo($file_info['name'], PATHINFO_EXTENSION);
        $filename = 'product_' . uniqid() . '.' . $extension;
        $upload_path = DIR_IMAGE . 'catalog/products/' . $filename;

        // إنشاء المجلد إذا لم يكن موجوداً
        $upload_dir = dirname($upload_path);
        if (!is_dir($upload_dir)) {
            mkdir($upload_dir, 0755, true);
        }

        // نقل الملف
        if (move_uploaded_file($file_info['tmp_name'], $upload_path)) {
            // ضغط الصورة
            $this->compressImage($upload_path, $upload_path, 85);

            return 'catalog/products/' . $filename;
        }

        return false;
    }

    /**
     * ضغط الصورة
     */
    private function compressImage($source, $destination, $quality) {
        $info = getimagesize($source);

        if ($info['mime'] == 'image/jpeg') {
            $image = imagecreatefromjpeg($source);
        } elseif ($info['mime'] == 'image/gif') {
            $image = imagecreatefromgif($source);
        } elseif ($info['mime'] == 'image/png') {
            $image = imagecreatefrompng($source);
        } elseif ($info['mime'] == 'image/webp') {
            $image = imagecreatefromwebp($source);
        } else {
            return false;
        }

        // حفظ الصورة المضغوطة
        if ($info['mime'] == 'image/jpeg') {
            imagejpeg($image, $destination, $quality);
        } elseif ($info['mime'] == 'image/gif') {
            imagegif($image, $destination);
        } elseif ($info['mime'] == 'image/png') {
            imagepng($image, $destination, 9);
        } elseif ($info['mime'] == 'image/webp') {
            imagewebp($image, $destination, $quality);
        }

        imagedestroy($image);
        return true;
    }

    /**
     * إنشاء باركود تلقائي
     */
    public function generateBarcode() {
        $this->load->language('inventory/product');

        $json = array();

        if (!$this->user->hasPermission('modify', 'inventory/product')) {
            $json['error'] = $this->language->get('error_permission');
        } else {
            $this->load->model('inventory/product');

            $barcode_type = isset($this->request->get['type']) ? $this->request->get['type'] : 'EAN13';
            $product_id = isset($this->request->get['product_id']) ? (int)$this->request->get['product_id'] : 0;

            $barcode = $this->model_inventory_product->generateBarcode($barcode_type, $product_id);

            if ($barcode) {
                $json['success'] = $this->language->get('text_success');
                $json['barcode'] = $barcode;
                $json['type'] = $barcode_type;
            } else {
                $json['error'] = $this->language->get('error_barcode_generate');
            }
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * إضافة باركود للمنتج
     */
    public function addBarcode() {
        $this->load->language('inventory/product');

        $json = array();

        if (!$this->user->hasPermission('modify', 'inventory/product')) {
            $json['error'] = $this->language->get('error_permission');
        } else {
            $this->load->model('inventory/product');

            $product_id = isset($this->request->post['product_id']) ? (int)$this->request->post['product_id'] : 0;
            $barcode = isset($this->request->post['barcode']) ? trim($this->request->post['barcode']) : '';
            $barcode_type = isset($this->request->post['barcode_type']) ? $this->request->post['barcode_type'] : 'EAN13';

            if ($product_id && !empty($barcode)) {
                // التحقق من عدم تكرار الباركود
                $existing_barcode = $this->model_inventory_product->getBarcodeByCode($barcode);

                if ($existing_barcode) {
                    $json['error'] = $this->language->get('error_barcode_exists');
                } else {
                    // التحقق من صحة الباركود
                    if ($this->validateBarcode($barcode, $barcode_type)) {
                        $barcode_data = array(
                            'product_id' => $product_id,
                            'barcode' => $barcode,
                            'barcode_type' => $barcode_type,
                            'is_primary' => isset($this->request->post['is_primary']) ? 1 : 0,
                            'status' => 1
                        );

                        $barcode_id = $this->model_inventory_product->addProductBarcode($barcode_data);

                        if ($barcode_id) {
                            $json['success'] = $this->language->get('text_success');
                            $json['barcode_id'] = $barcode_id;

                            // تسجيل العملية في نظام التدقيق
                            $this->central_service->logActivity('product_barcode_add', 'inventory',
                                'تم إضافة باركود جديد: ' . $barcode . ' للمنتج: ' . $product_id, $barcode_id);
                        } else {
                            $json['error'] = $this->language->get('error_barcode_add');
                        }
                    } else {
                        $json['error'] = $this->language->get('error_barcode_invalid');
                    }
                }
            } else {
                $json['error'] = $this->language->get('error_barcode_data_invalid');
            }
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * طباعة الباركود
     */
    public function printBarcode() {
        $this->load->language('inventory/product');

        if (!$this->user->hasPermission('access', 'inventory/product')) {
            $this->response->redirect($this->url->link('error/permission', 'user_token=' . $this->session->data['user_token'], true));
        }

        $this->load->model('inventory/product');

        $barcode_id = isset($this->request->get['barcode_id']) ? (int)$this->request->get['barcode_id'] : 0;
        $quantity = isset($this->request->get['quantity']) ? (int)$this->request->get['quantity'] : 1;

        if ($barcode_id) {
            $barcode_info = $this->model_inventory_product->getProductBarcode($barcode_id);

            if ($barcode_info) {
                // إنشاء صورة الباركود
                $barcode_image = $this->generateBarcodeImage($barcode_info['barcode'], $barcode_info['barcode_type']);

                if ($barcode_image) {
                    // إنشاء PDF للطباعة
                    $this->generateBarcodePDF($barcode_info, $barcode_image, $quantity);
                } else {
                    $this->session->data['error'] = $this->language->get('error_barcode_image');
                    $this->response->redirect($this->url->link('inventory/product', 'user_token=' . $this->session->data['user_token'], true));
                }
            } else {
                $this->session->data['error'] = $this->language->get('error_barcode_not_found');
                $this->response->redirect($this->url->link('inventory/product', 'user_token=' . $this->session->data['user_token'], true));
            }
        } else {
            $this->response->redirect($this->url->link('inventory/product', 'user_token=' . $this->session->data['user_token'], true));
        }
    }

    /**
     * التحقق من صحة الباركود
     */
    private function validateBarcode($barcode, $type) {
        switch ($type) {
            case 'EAN13':
                return $this->validateEAN13($barcode);
            case 'EAN8':
                return $this->validateEAN8($barcode);
            case 'UPC':
                return $this->validateUPC($barcode);
            case 'CODE128':
                return $this->validateCODE128($barcode);
            default:
                return strlen($barcode) > 0;
        }
    }

    /**
     * التحقق من صحة EAN13
     */
    private function validateEAN13($barcode) {
        if (strlen($barcode) != 13 || !ctype_digit($barcode)) {
            return false;
        }

        $sum = 0;
        for ($i = 0; $i < 12; $i++) {
            $sum += (int)$barcode[$i] * (($i % 2 == 0) ? 1 : 3);
        }

        $checksum = (10 - ($sum % 10)) % 10;
        return $checksum == (int)$barcode[12];
    }

    /**
     * التحقق من صحة EAN8
     */
    private function validateEAN8($barcode) {
        if (strlen($barcode) != 8 || !ctype_digit($barcode)) {
            return false;
        }

        $sum = 0;
        for ($i = 0; $i < 7; $i++) {
            $sum += (int)$barcode[$i] * (($i % 2 == 0) ? 1 : 3);
        }

        $checksum = (10 - ($sum % 10)) % 10;
        return $checksum == (int)$barcode[7];
    }

    /**
     * التحقق من صحة UPC
     */
    private function validateUPC($barcode) {
        if (strlen($barcode) != 12 || !ctype_digit($barcode)) {
            return false;
        }

        $sum = 0;
        for ($i = 0; $i < 11; $i++) {
            $sum += (int)$barcode[$i] * (($i % 2 == 0) ? 3 : 1);
        }

        $checksum = (10 - ($sum % 10)) % 10;
        return $checksum == (int)$barcode[11];
    }

    /**
     * التحقق من صحة CODE128
     */
    private function validateCODE128($barcode) {
        // CODE128 يمكن أن يحتوي على أحرف وأرقام
        return strlen($barcode) > 0 && strlen($barcode) <= 48;
    }

    /**
     * إنشاء صورة الباركود
     */
    private function generateBarcodeImage($barcode, $type) {
        // هنا يمكن استخدام مكتبة خارجية لإنشاء الباركود
        // مثل TCPDF أو مكتبة أخرى
        // للتبسيط، سنعيد مسار وهمي
        return 'catalog/barcodes/' . $barcode . '.png';
    }

    /**
     * إنشاء PDF للباركود
     */
    private function generateBarcodePDF($barcode_info, $barcode_image, $quantity) {
        // هنا يمكن استخدام TCPDF لإنشاء PDF
        // للتبسيط، سنعيد استجابة JSON
        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode(array(
            'success' => 'تم إنشاء ملف PDF للطباعة',
            'pdf_url' => 'path/to/barcode.pdf'
        )));
    }

    /**
     * إنشاء حزمة منتجات (Bundle)
     */
    public function createBundle() {
        $this->load->language('inventory/product');

        $json = array();

        if (!$this->user->hasPermission('modify', 'inventory/product')) {
            $json['error'] = $this->language->get('error_permission');
        } else {
            $this->load->model('inventory/product');

            $bundle_data = array(
                'name' => isset($this->request->post['bundle_name']) ? $this->request->post['bundle_name'] : '',
                'description' => isset($this->request->post['bundle_description']) ? $this->request->post['bundle_description'] : '',
                'discount_type' => isset($this->request->post['discount_type']) ? $this->request->post['discount_type'] : 'percentage',
                'discount_value' => isset($this->request->post['discount_value']) ? (float)$this->request->post['discount_value'] : 0,
                'products' => isset($this->request->post['bundle_products']) ? $this->request->post['bundle_products'] : array(),
                'status' => 1
            );

            if (empty($bundle_data['name'])) {
                $json['error'] = $this->language->get('error_bundle_name_required');
            } elseif (empty($bundle_data['products']) || count($bundle_data['products']) < 2) {
                $json['error'] = $this->language->get('error_bundle_products_minimum');
            } else {
                // حساب السعر الإجمالي للحزمة
                $total_price = $this->calculateBundlePrice($bundle_data['products'], $bundle_data['discount_type'], $bundle_data['discount_value']);
                $bundle_data['total_price'] = $total_price;

                $bundle_id = $this->model_inventory_product->addProductBundle($bundle_data);

                if ($bundle_id) {
                    $json['success'] = $this->language->get('text_success');
                    $json['bundle_id'] = $bundle_id;
                    $json['total_price'] = $total_price;

                    // تسجيل العملية في نظام التدقيق
                    $this->central_service->logActivity('product_bundle_create', 'inventory',
                        'تم إنشاء حزمة منتجات جديدة: ' . $bundle_data['name'], $bundle_id);
                } else {
                    $json['error'] = $this->language->get('error_bundle_create');
                }
            }
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * تعديل حزمة منتجات
     */
    public function editBundle() {
        $this->load->language('inventory/product');

        $json = array();

        if (!$this->user->hasPermission('modify', 'inventory/product')) {
            $json['error'] = $this->language->get('error_permission');
        } else {
            $this->load->model('inventory/product');

            $bundle_id = isset($this->request->get['bundle_id']) ? (int)$this->request->get['bundle_id'] : 0;

            if ($bundle_id) {
                $bundle_data = array(
                    'name' => isset($this->request->post['bundle_name']) ? $this->request->post['bundle_name'] : '',
                    'description' => isset($this->request->post['bundle_description']) ? $this->request->post['bundle_description'] : '',
                    'discount_type' => isset($this->request->post['discount_type']) ? $this->request->post['discount_type'] : 'percentage',
                    'discount_value' => isset($this->request->post['discount_value']) ? (float)$this->request->post['discount_value'] : 0,
                    'products' => isset($this->request->post['bundle_products']) ? $this->request->post['bundle_products'] : array(),
                    'status' => isset($this->request->post['status']) ? (int)$this->request->post['status'] : 1
                );

                if (empty($bundle_data['name'])) {
                    $json['error'] = $this->language->get('error_bundle_name_required');
                } elseif (empty($bundle_data['products']) || count($bundle_data['products']) < 2) {
                    $json['error'] = $this->language->get('error_bundle_products_minimum');
                } else {
                    // حساب السعر الإجمالي للحزمة
                    $total_price = $this->calculateBundlePrice($bundle_data['products'], $bundle_data['discount_type'], $bundle_data['discount_value']);
                    $bundle_data['total_price'] = $total_price;

                    $result = $this->model_inventory_product->editProductBundle($bundle_id, $bundle_data);

                    if ($result) {
                        $json['success'] = $this->language->get('text_success');
                        $json['total_price'] = $total_price;

                        // تسجيل العملية في نظام التدقيق
                        $this->central_service->logActivity('product_bundle_edit', 'inventory',
                            'تم تعديل حزمة المنتجات: ' . $bundle_data['name'], $bundle_id);
                    } else {
                        $json['error'] = $this->language->get('error_bundle_edit');
                    }
                }
            } else {
                $json['error'] = $this->language->get('error_bundle_id_invalid');
            }
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * حذف حزمة منتجات
     */
    public function deleteBundle() {
        $this->load->language('inventory/product');

        $json = array();

        if (!$this->user->hasPermission('modify', 'inventory/product')) {
            $json['error'] = $this->language->get('error_permission');
        } else {
            $this->load->model('inventory/product');

            $bundle_id = isset($this->request->get['bundle_id']) ? (int)$this->request->get['bundle_id'] : 0;

            if ($bundle_id) {
                $bundle_info = $this->model_inventory_product->getProductBundle($bundle_id);

                if ($bundle_info) {
                    $result = $this->model_inventory_product->deleteProductBundle($bundle_id);

                    if ($result) {
                        $json['success'] = $this->language->get('text_success');

                        // تسجيل العملية في نظام التدقيق
                        $this->central_service->logActivity('product_bundle_delete', 'inventory',
                            'تم حذف حزمة المنتجات: ' . $bundle_info['name'], $bundle_id);
                    } else {
                        $json['error'] = $this->language->get('error_bundle_delete');
                    }
                } else {
                    $json['error'] = $this->language->get('error_bundle_not_found');
                }
            } else {
                $json['error'] = $this->language->get('error_bundle_id_invalid');
            }
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * التحقق من توفر مكونات الحزمة
     */
    public function checkBundleAvailability() {
        $this->load->language('inventory/product');

        $json = array();

        $this->load->model('inventory/product');

        $bundle_id = isset($this->request->get['bundle_id']) ? (int)$this->request->get['bundle_id'] : 0;
        $quantity = isset($this->request->get['quantity']) ? (int)$this->request->get['quantity'] : 1;

        if ($bundle_id) {
            $bundle_info = $this->model_inventory_product->getProductBundle($bundle_id);

            if ($bundle_info) {
                $availability = $this->model_inventory_product->checkBundleAvailability($bundle_id, $quantity);

                $json['available'] = $availability['available'];
                $json['max_quantity'] = $availability['max_quantity'];
                $json['unavailable_products'] = $availability['unavailable_products'];

                if ($availability['available']) {
                    $json['message'] = $this->language->get('text_bundle_available');
                } else {
                    $json['message'] = $this->language->get('text_bundle_unavailable');
                }
            } else {
                $json['error'] = $this->language->get('error_bundle_not_found');
            }
        } else {
            $json['error'] = $this->language->get('error_bundle_id_invalid');
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * حساب سعر الحزمة
     */
    private function calculateBundlePrice($products, $discount_type, $discount_value) {
        $this->load->model('inventory/product');

        $total_price = 0;

        foreach ($products as $product) {
            $product_info = $this->model_inventory_product->getProduct($product['product_id']);
            if ($product_info) {
                $total_price += $product_info['price'] * $product['quantity'];
            }
        }

        // تطبيق الخصم
        if ($discount_type == 'percentage') {
            $total_price = $total_price * (1 - ($discount_value / 100));
        } elseif ($discount_type == 'fixed') {
            $total_price = max(0, $total_price - $discount_value);
        }

        return round($total_price, 2);
    }

    /**
     * الحصول على قائمة الحزم
     */
    public function getBundles() {
        $this->load->model('inventory/product');

        $bundles = $this->model_inventory_product->getProductBundles();

        $json = array();
        foreach ($bundles as $bundle) {
            $json[] = array(
                'bundle_id' => $bundle['bundle_id'],
                'name' => $bundle['name'],
                'description' => $bundle['description'],
                'total_price' => $bundle['total_price'],
                'discount_type' => $bundle['discount_type'],
                'discount_value' => $bundle['discount_value'],
                'status' => $bundle['status'],
                'products_count' => count($bundle['products'])
            );
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * إضافة شريحة تسعير جديدة
     */
    public function addPriceTier() {
        $this->load->language('inventory/product');

        $json = array();

        if (!$this->user->hasPermission('modify', 'inventory/product')) {
            $json['error'] = $this->language->get('error_permission');
        } else {
            $this->load->model('inventory/product');

            $product_id = isset($this->request->post['product_id']) ? (int)$this->request->post['product_id'] : 0;
            $min_quantity = isset($this->request->post['min_quantity']) ? (int)$this->request->post['min_quantity'] : 1;
            $max_quantity = isset($this->request->post['max_quantity']) ? (int)$this->request->post['max_quantity'] : 0;
            $price = isset($this->request->post['price']) ? (float)$this->request->post['price'] : 0;
            $customer_group_id = isset($this->request->post['customer_group_id']) ? (int)$this->request->post['customer_group_id'] : 0;

            if ($product_id && $min_quantity > 0 && $price > 0) {
                // التحقق من عدم تداخل الكميات
                $existing_tier = $this->model_inventory_product->checkPriceTierOverlap($product_id, $min_quantity, $max_quantity, $customer_group_id);

                if ($existing_tier) {
                    $json['error'] = $this->language->get('error_price_tier_overlap');
                } else {
                    $tier_data = array(
                        'product_id' => $product_id,
                        'customer_group_id' => $customer_group_id,
                        'min_quantity' => $min_quantity,
                        'max_quantity' => $max_quantity,
                        'price' => $price,
                        'date_start' => isset($this->request->post['date_start']) ? $this->request->post['date_start'] : '0000-00-00',
                        'date_end' => isset($this->request->post['date_end']) ? $this->request->post['date_end'] : '0000-00-00',
                        'status' => 1
                    );

                    $tier_id = $this->model_inventory_product->addPriceTier($tier_data);

                    if ($tier_id) {
                        $json['success'] = $this->language->get('text_success');
                        $json['tier_id'] = $tier_id;

                        // تسجيل العملية في نظام التدقيق
                        $this->central_service->logActivity('product_price_tier_add', 'inventory',
                            'تم إضافة شريحة تسعير جديدة للمنتج: ' . $product_id, $tier_id);
                    } else {
                        $json['error'] = $this->language->get('error_price_tier_add');
                    }
                }
            } else {
                $json['error'] = $this->language->get('error_price_tier_data_invalid');
            }
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * تعديل شريحة تسعير
     */
    public function editPriceTier() {
        $this->load->language('inventory/product');

        $json = array();

        if (!$this->user->hasPermission('modify', 'inventory/product')) {
            $json['error'] = $this->language->get('error_permission');
        } else {
            $this->load->model('inventory/product');

            $tier_id = isset($this->request->get['tier_id']) ? (int)$this->request->get['tier_id'] : 0;
            $product_id = isset($this->request->post['product_id']) ? (int)$this->request->post['product_id'] : 0;
            $min_quantity = isset($this->request->post['min_quantity']) ? (int)$this->request->post['min_quantity'] : 1;
            $max_quantity = isset($this->request->post['max_quantity']) ? (int)$this->request->post['max_quantity'] : 0;
            $price = isset($this->request->post['price']) ? (float)$this->request->post['price'] : 0;
            $customer_group_id = isset($this->request->post['customer_group_id']) ? (int)$this->request->post['customer_group_id'] : 0;

            if ($tier_id && $product_id && $min_quantity > 0 && $price > 0) {
                // التحقق من عدم تداخل الكميات (باستثناء الشريحة الحالية)
                $existing_tier = $this->model_inventory_product->checkPriceTierOverlap($product_id, $min_quantity, $max_quantity, $customer_group_id, $tier_id);

                if ($existing_tier) {
                    $json['error'] = $this->language->get('error_price_tier_overlap');
                } else {
                    $tier_data = array(
                        'customer_group_id' => $customer_group_id,
                        'min_quantity' => $min_quantity,
                        'max_quantity' => $max_quantity,
                        'price' => $price,
                        'date_start' => isset($this->request->post['date_start']) ? $this->request->post['date_start'] : '0000-00-00',
                        'date_end' => isset($this->request->post['date_end']) ? $this->request->post['date_end'] : '0000-00-00',
                        'status' => isset($this->request->post['status']) ? (int)$this->request->post['status'] : 1
                    );

                    $result = $this->model_inventory_product->editPriceTier($tier_id, $tier_data);

                    if ($result) {
                        $json['success'] = $this->language->get('text_success');

                        // تسجيل العملية في نظام التدقيق
                        $this->central_service->logActivity('product_price_tier_edit', 'inventory',
                            'تم تعديل شريحة التسعير للمنتج: ' . $product_id, $tier_id);
                    } else {
                        $json['error'] = $this->language->get('error_price_tier_edit');
                    }
                }
            } else {
                $json['error'] = $this->language->get('error_price_tier_data_invalid');
            }
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * حذف شريحة تسعير
     */
    public function deletePriceTier() {
        $this->load->language('inventory/product');

        $json = array();

        if (!$this->user->hasPermission('modify', 'inventory/product')) {
            $json['error'] = $this->language->get('error_permission');
        } else {
            $this->load->model('inventory/product');

            $tier_id = isset($this->request->get['tier_id']) ? (int)$this->request->get['tier_id'] : 0;

            if ($tier_id) {
                $tier_info = $this->model_inventory_product->getPriceTier($tier_id);

                if ($tier_info) {
                    $result = $this->model_inventory_product->deletePriceTier($tier_id);

                    if ($result) {
                        $json['success'] = $this->language->get('text_success');

                        // تسجيل العملية في نظام التدقيق
                        $this->central_service->logActivity('product_price_tier_delete', 'inventory',
                            'تم حذف شريحة التسعير للمنتج: ' . $tier_info['product_id'], $tier_id);
                    } else {
                        $json['error'] = $this->language->get('error_price_tier_delete');
                    }
                } else {
                    $json['error'] = $this->language->get('error_price_tier_not_found');
                }
            } else {
                $json['error'] = $this->language->get('error_price_tier_id_invalid');
            }
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * حساب السعر حسب الكمية ومجموعة العملاء
     */
    public function calculatePrice() {
        $this->load->language('inventory/product');

        $json = array();

        $this->load->model('inventory/product');

        $product_id = isset($this->request->get['product_id']) ? (int)$this->request->get['product_id'] : 0;
        $quantity = isset($this->request->get['quantity']) ? (int)$this->request->get['quantity'] : 1;
        $customer_group_id = isset($this->request->get['customer_group_id']) ? (int)$this->request->get['customer_group_id'] : 0;

        if ($product_id && $quantity > 0) {
            $price_info = $this->model_inventory_product->getProductPrice($product_id, $quantity, $customer_group_id);

            if ($price_info) {
                $json['price'] = $price_info['price'];
                $json['original_price'] = $price_info['original_price'];
                $json['discount'] = $price_info['discount'];
                $json['discount_percentage'] = $price_info['discount_percentage'];
                $json['total'] = $price_info['price'] * $quantity;
                $json['tier_name'] = $price_info['tier_name'];
                $json['savings'] = ($price_info['original_price'] - $price_info['price']) * $quantity;
            } else {
                $json['error'] = $this->language->get('error_product_not_found');
            }
        } else {
            $json['error'] = $this->language->get('error_invalid_data');
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * الحصول على شرائح التسعير للمنتج
     */
    public function getPriceTiers() {
        $this->load->model('inventory/product');

        $product_id = isset($this->request->get['product_id']) ? (int)$this->request->get['product_id'] : 0;

        if ($product_id) {
            $tiers = $this->model_inventory_product->getProductPriceTiers($product_id);

            $json = array();
            foreach ($tiers as $tier) {
                $json[] = array(
                    'tier_id' => $tier['tier_id'],
                    'customer_group_id' => $tier['customer_group_id'],
                    'customer_group_name' => $tier['customer_group_name'],
                    'min_quantity' => $tier['min_quantity'],
                    'max_quantity' => $tier['max_quantity'],
                    'price' => $tier['price'],
                    'date_start' => $tier['date_start'],
                    'date_end' => $tier['date_end'],
                    'status' => $tier['status']
                );
            }
        } else {
            $json = array();
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * التحقق من صحة الأسعار
     */
    public function validatePricing() {
        $this->load->language('inventory/product');

        $json = array();

        $this->load->model('inventory/product');

        $product_id = isset($this->request->get['product_id']) ? (int)$this->request->get['product_id'] : 0;

        if ($product_id) {
            $validation_result = $this->model_inventory_product->validateProductPricing($product_id);

            $json['valid'] = $validation_result['valid'];
            $json['errors'] = $validation_result['errors'];
            $json['warnings'] = $validation_result['warnings'];

            if ($validation_result['valid']) {
                $json['message'] = $this->language->get('text_pricing_valid');
            } else {
                $json['message'] = $this->language->get('text_pricing_invalid');
            }
        } else {
            $json['error'] = $this->language->get('error_product_id_invalid');
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * إدارة الدفعات - إضافة دفعة جديدة
     */
    public function addBatch() {
        $this->load->language('inventory/product');

        $json = array();

        if (!$this->user->hasPermission('modify', 'inventory/product')) {
            $json['error'] = $this->language->get('error_permission');
        } else {
            $this->load->model('inventory/product');

            $product_id = isset($this->request->post['product_id']) ? (int)$this->request->post['product_id'] : 0;
            $batch_number = isset($this->request->post['batch_number']) ? trim($this->request->post['batch_number']) : '';
            $expiry_date = isset($this->request->post['expiry_date']) ? $this->request->post['expiry_date'] : '';
            $quantity = isset($this->request->post['quantity']) ? (int)$this->request->post['quantity'] : 0;

            if ($product_id && !empty($batch_number) && $quantity > 0) {
                // التحقق من عدم تكرار رقم الدفعة
                $existing_batch = $this->model_inventory_product->getBatchByNumber($batch_number, $product_id);

                if ($existing_batch) {
                    $json['error'] = $this->language->get('error_batch_number_exists');
                } else {
                    $batch_data = array(
                        'product_id' => $product_id,
                        'batch_number' => $batch_number,
                        'expiry_date' => $expiry_date,
                        'manufacturing_date' => isset($this->request->post['manufacturing_date']) ? $this->request->post['manufacturing_date'] : '',
                        'quantity' => $quantity,
                        'cost_price' => isset($this->request->post['cost_price']) ? (float)$this->request->post['cost_price'] : 0,
                        'supplier_id' => isset($this->request->post['supplier_id']) ? (int)$this->request->post['supplier_id'] : 0,
                        'notes' => isset($this->request->post['notes']) ? $this->request->post['notes'] : '',
                        'status' => 1,
                        'date_added' => date('Y-m-d H:i:s')
                    );

                    $batch_id = $this->model_inventory_product->addProductBatch($batch_data);

                    if ($batch_id) {
                        $json['success'] = $this->language->get('text_success');
                        $json['batch_id'] = $batch_id;

                        // تحديث كمية المنتج
                        $this->model_inventory_product->updateProductQuantity($product_id, $quantity, 'add');

                        // إضافة تنبيه إذا كان تاريخ الانتهاء قريب
                        if (!empty($expiry_date)) {
                            $this->checkExpiryAlert($product_id, $batch_id, $expiry_date);
                        }

                        // تسجيل العملية في نظام التدقيق
                        $this->central_service->logActivity('product_batch_add', 'inventory',
                            'تم إضافة دفعة جديدة: ' . $batch_number . ' للمنتج: ' . $product_id, $batch_id);
                    } else {
                        $json['error'] = $this->language->get('error_batch_add');
                    }
                }
            } else {
                $json['error'] = $this->language->get('error_batch_data_invalid');
            }
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * تعديل دفعة
     */
    public function editBatch() {
        $this->load->language('inventory/product');

        $json = array();

        if (!$this->user->hasPermission('modify', 'inventory/product')) {
            $json['error'] = $this->language->get('error_permission');
        } else {
            $this->load->model('inventory/product');

            $batch_id = isset($this->request->get['batch_id']) ? (int)$this->request->get['batch_id'] : 0;
            $batch_number = isset($this->request->post['batch_number']) ? trim($this->request->post['batch_number']) : '';
            $expiry_date = isset($this->request->post['expiry_date']) ? $this->request->post['expiry_date'] : '';
            $quantity = isset($this->request->post['quantity']) ? (int)$this->request->post['quantity'] : 0;

            if ($batch_id && !empty($batch_number) && $quantity >= 0) {
                $batch_info = $this->model_inventory_product->getProductBatch($batch_id);

                if ($batch_info) {
                    // التحقق من عدم تكرار رقم الدفعة (باستثناء الدفعة الحالية)
                    $existing_batch = $this->model_inventory_product->getBatchByNumber($batch_number, $batch_info['product_id'], $batch_id);

                    if ($existing_batch) {
                        $json['error'] = $this->language->get('error_batch_number_exists');
                    } else {
                        $old_quantity = $batch_info['quantity'];

                        $batch_data = array(
                            'batch_number' => $batch_number,
                            'expiry_date' => $expiry_date,
                            'manufacturing_date' => isset($this->request->post['manufacturing_date']) ? $this->request->post['manufacturing_date'] : $batch_info['manufacturing_date'],
                            'quantity' => $quantity,
                            'cost_price' => isset($this->request->post['cost_price']) ? (float)$this->request->post['cost_price'] : $batch_info['cost_price'],
                            'supplier_id' => isset($this->request->post['supplier_id']) ? (int)$this->request->post['supplier_id'] : $batch_info['supplier_id'],
                            'notes' => isset($this->request->post['notes']) ? $this->request->post['notes'] : $batch_info['notes'],
                            'status' => isset($this->request->post['status']) ? (int)$this->request->post['status'] : $batch_info['status']
                        );

                        $result = $this->model_inventory_product->editProductBatch($batch_id, $batch_data);

                        if ($result) {
                            $json['success'] = $this->language->get('text_success');

                            // تحديث كمية المنتج إذا تغيرت
                            if ($quantity != $old_quantity) {
                                $quantity_diff = $quantity - $old_quantity;
                                $this->model_inventory_product->updateProductQuantity($batch_info['product_id'], abs($quantity_diff), $quantity_diff > 0 ? 'add' : 'subtract');
                            }

                            // تحديث تنبيه انتهاء الصلاحية
                            if (!empty($expiry_date)) {
                                $this->checkExpiryAlert($batch_info['product_id'], $batch_id, $expiry_date);
                            }

                            // تسجيل العملية في نظام التدقيق
                            $this->central_service->logActivity('product_batch_edit', 'inventory',
                                'تم تعديل الدفعة: ' . $batch_number, $batch_id);
                        } else {
                            $json['error'] = $this->language->get('error_batch_edit');
                        }
                    }
                } else {
                    $json['error'] = $this->language->get('error_batch_not_found');
                }
            } else {
                $json['error'] = $this->language->get('error_batch_data_invalid');
            }
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * نظام FIFO للدفعات
     */
    public function getFIFOBatches() {
        $this->load->model('inventory/product');

        $product_id = isset($this->request->get['product_id']) ? (int)$this->request->get['product_id'] : 0;
        $required_quantity = isset($this->request->get['quantity']) ? (int)$this->request->get['quantity'] : 1;

        if ($product_id && $required_quantity > 0) {
            $batches = $this->model_inventory_product->getFIFOBatches($product_id, $required_quantity);

            $json = array();
            $total_available = 0;

            foreach ($batches as $batch) {
                $json[] = array(
                    'batch_id' => $batch['batch_id'],
                    'batch_number' => $batch['batch_number'],
                    'quantity' => $batch['quantity'],
                    'expiry_date' => $batch['expiry_date'],
                    'days_to_expiry' => $batch['days_to_expiry'],
                    'is_expired' => $batch['is_expired'],
                    'cost_price' => $batch['cost_price']
                );

                $total_available += $batch['quantity'];
            }

            $json['total_available'] = $total_available;
            $json['sufficient'] = $total_available >= $required_quantity;
        } else {
            $json = array('error' => $this->language->get('error_invalid_data'));
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * التحقق من تنبيهات انتهاء الصلاحية
     */
    private function checkExpiryAlert($product_id, $batch_id, $expiry_date) {
        if (empty($expiry_date) || $expiry_date == '0000-00-00') {
            return;
        }

        $days_to_expiry = (strtotime($expiry_date) - time()) / (60 * 60 * 24);
        $alert_days = $this->config->get('config_expiry_alert_days') ?: 30;

        if ($days_to_expiry <= $alert_days && $days_to_expiry >= 0) {
            // إنشاء تنبيه انتهاء الصلاحية
            $this->load->model('communication/unified_notification');

            $notification_data = array(
                'type' => 'expiry_alert',
                'title' => 'تنبيه انتهاء صلاحية',
                'message' => 'المنتج رقم ' . $product_id . ' - الدفعة ' . $batch_id . ' ستنتهي صلاحيتها خلال ' . ceil($days_to_expiry) . ' يوم',
                'priority' => $days_to_expiry <= 7 ? 'high' : 'medium',
                'module' => 'inventory',
                'reference_id' => $batch_id,
                'user_id' => $this->user->getId()
            );

            $this->model_communication_unified_notification->addNotification($notification_data);
        }
    }

    /**
     * الحصول على المنتجات قاربة الانتهاء
     */
    public function getExpiringProducts() {
        $this->load->model('inventory/product');

        $days = isset($this->request->get['days']) ? (int)$this->request->get['days'] : 30;

        $expiring_products = $this->model_inventory_product->getExpiringProducts($days);

        $json = array();
        foreach ($expiring_products as $product) {
            $json[] = array(
                'product_id' => $product['product_id'],
                'product_name' => $product['product_name'],
                'batch_id' => $product['batch_id'],
                'batch_number' => $product['batch_number'],
                'quantity' => $product['quantity'],
                'expiry_date' => $product['expiry_date'],
                'days_to_expiry' => $product['days_to_expiry'],
                'cost_value' => $product['cost_value'],
                'alert_level' => $product['days_to_expiry'] <= 7 ? 'critical' : ($product['days_to_expiry'] <= 15 ? 'warning' : 'info')
            );
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * نظام التصنيف التلقائي
     */
    public function autoClassify() {
        $this->load->language('inventory/product');

        $json = array();

        if (!$this->user->hasPermission('modify', 'inventory/product')) {
            $json['error'] = $this->language->get('error_permission');
        } else {
            $this->load->model('inventory/product');

            $product_id = isset($this->request->post['product_id']) ? (int)$this->request->post['product_id'] : 0;
            $product_name = isset($this->request->post['product_name']) ? $this->request->post['product_name'] : '';
            $product_description = isset($this->request->post['product_description']) ? $this->request->post['product_description'] : '';

            if ($product_id || !empty($product_name)) {
                $suggested_categories = $this->model_inventory_product->suggestCategories($product_name, $product_description);

                if (!empty($suggested_categories)) {
                    $json['success'] = $this->language->get('text_success');
                    $json['suggested_categories'] = $suggested_categories;

                    // تطبيق التصنيف التلقائي إذا كان هناك منتج محدد
                    if ($product_id && isset($this->request->post['apply_auto_classification'])) {
                        $best_category = $suggested_categories[0];
                        $this->model_inventory_product->addProductToCategory($product_id, $best_category['category_id']);

                        $json['applied_category'] = $best_category;

                        // تسجيل العملية في نظام التدقيق
                        $this->central_service->logActivity('product_auto_classify', 'inventory',
                            'تم تطبيق التصنيف التلقائي للمنتج: ' . $product_id, $product_id);
                    }
                } else {
                    $json['message'] = $this->language->get('text_no_suggestions');
                    $json['suggested_categories'] = array();
                }
            } else {
                $json['error'] = $this->language->get('error_product_data_required');
            }
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * نسخ المنتج (Clone)
     */
    public function cloneProduct() {
        $this->load->language('inventory/product');

        $json = array();

        if (!$this->user->hasPermission('modify', 'inventory/product')) {
            $json['error'] = $this->language->get('error_permission');
        } else {
            $this->load->model('inventory/product');

            $product_id = isset($this->request->get['product_id']) ? (int)$this->request->get['product_id'] : 0;
            $clone_options = isset($this->request->post['clone_options']) ? $this->request->post['clone_options'] : array();

            if ($product_id) {
                $product_info = $this->model_inventory_product->getProduct($product_id);

                if ($product_info) {
                    // إعداد بيانات المنتج المنسوخ
                    $clone_data = $product_info;
                    $clone_data['model'] = $clone_data['model'] . '_copy';
                    $clone_data['sku'] = $clone_data['sku'] . '_copy';
                    $clone_data['quantity'] = 0; // البدء بكمية صفر
                    $clone_data['status'] = 0; // غير مفعل افتراضياً

                    // نسخ الوصف
                    if (in_array('descriptions', $clone_options)) {
                        $clone_data['descriptions'] = $this->model_inventory_product->getProductDescriptions($product_id);
                    }

                    // إنشاء المنتج المنسوخ
                    $new_product_id = $this->model_inventory_product->addProduct($clone_data);

                    if ($new_product_id) {
                        // نسخ المكونات الإضافية حسب الخيارات المحددة
                        if (in_array('categories', $clone_options)) {
                            $categories = $this->model_inventory_product->getProductCategories($product_id);
                            foreach ($categories as $category_id) {
                                $this->model_inventory_product->addProductToCategory($new_product_id, $category_id);
                            }
                        }

                        if (in_array('images', $clone_options)) {
                            $images = $this->model_inventory_product->getProductImages($product_id);
                            foreach ($images as $image) {
                                $this->model_inventory_product->addProductImage($new_product_id, $image);
                            }
                        }

                        if (in_array('variants', $clone_options)) {
                            $variants = $this->model_inventory_product->getProductVariants($product_id);
                            foreach ($variants as $variant) {
                                $variant['product_id'] = $new_product_id;
                                $variant['quantity'] = 0;
                                $this->model_inventory_product->addProductVariant($variant);
                            }
                        }

                        if (in_array('pricing', $clone_options)) {
                            $price_tiers = $this->model_inventory_product->getProductPriceTiers($product_id);
                            foreach ($price_tiers as $tier) {
                                $tier['product_id'] = $new_product_id;
                                $this->model_inventory_product->addPriceTier($tier);
                            }
                        }

                        $json['success'] = $this->language->get('text_success');
                        $json['new_product_id'] = $new_product_id;

                        // تسجيل العملية في نظام التدقيق
                        $this->central_service->logActivity('product_clone', 'inventory',
                            'تم نسخ المنتج ' . $product_id . ' إلى منتج جديد: ' . $new_product_id, $new_product_id);
                    } else {
                        $json['error'] = $this->language->get('error_product_clone');
                    }
                } else {
                    $json['error'] = $this->language->get('error_product_not_found');
                }
            } else {
                $json['error'] = $this->language->get('error_product_id_invalid');
            }
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * التكامل مع المحاسبة - إنشاء حساب المخزون
     */
    public function createInventoryAccount() {
        $this->load->language('inventory/product');

        $json = array();

        if (!$this->user->hasPermission('modify', 'inventory/product')) {
            $json['error'] = $this->language->get('error_permission');
        } else {
            $this->load->model('inventory/product');
            $this->load->model('accounts/journal');

            $product_id = isset($this->request->post['product_id']) ? (int)$this->request->post['product_id'] : 0;

            if ($product_id) {
                $product_info = $this->model_inventory_product->getProduct($product_id);

                if ($product_info) {
                    // إنشاء حساب المخزون للمنتج
                    $account_data = array(
                        'account_code' => $this->generateInventoryAccountCode($product_id),
                        'account_name' => 'مخزون - ' . $product_info['name'],
                        'account_type' => 'asset',
                        'parent_account' => $this->config->get('config_inventory_parent_account'),
                        'is_active' => 1,
                        'reference_type' => 'product',
                        'reference_id' => $product_id
                    );

                    $account_id = $this->model_accounts_journal->addAccount($account_data);

                    if ($account_id) {
                        // ربط الحساب بالمنتج
                        $this->model_inventory_product->linkProductAccount($product_id, $account_id);

                        $json['success'] = $this->language->get('text_success');
                        $json['account_id'] = $account_id;
                        $json['account_code'] = $account_data['account_code'];

                        // تسجيل العملية في نظام التدقيق
                        $this->central_service->logActivity('product_account_create', 'inventory',
                            'تم إنشاء حساب مخزون للمنتج: ' . $product_id, $account_id);
                    } else {
                        $json['error'] = $this->language->get('error_account_create');
                    }
                } else {
                    $json['error'] = $this->language->get('error_product_not_found');
                }
            } else {
                $json['error'] = $this->language->get('error_product_id_invalid');
            }
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * تحديث تكلفة المنتج تلقائياً (WAC)
     */
    public function updateProductCost() {
        $this->load->language('inventory/product');

        $json = array();

        if (!$this->user->hasPermission('modify', 'inventory/product')) {
            $json['error'] = $this->language->get('error_permission');
        } else {
            $this->load->model('inventory/product');
            $this->load->model('accounts/journal');

            $product_id = isset($this->request->post['product_id']) ? (int)$this->request->post['product_id'] : 0;
            $new_cost = isset($this->request->post['new_cost']) ? (float)$this->request->post['new_cost'] : 0;
            $quantity = isset($this->request->post['quantity']) ? (int)$this->request->post['quantity'] : 0;

            if ($product_id && $new_cost > 0 && $quantity > 0) {
                $product_info = $this->model_inventory_product->getProduct($product_id);

                if ($product_info) {
                    // حساب التكلفة المتوسطة المرجحة
                    $current_quantity = $product_info['quantity'];
                    $current_cost = $product_info['cost_price'];

                    $total_value = ($current_quantity * $current_cost) + ($quantity * $new_cost);
                    $total_quantity = $current_quantity + $quantity;
                    $new_average_cost = $total_quantity > 0 ? $total_value / $total_quantity : 0;

                    // تحديث تكلفة المنتج
                    $this->model_inventory_product->updateProductCost($product_id, $new_average_cost);

                    // إنشاء قيد محاسبي
                    $journal_entry = array(
                        'date' => date('Y-m-d'),
                        'reference' => 'INV-COST-' . $product_id . '-' . time(),
                        'description' => 'تحديث تكلفة المنتج: ' . $product_info['name'],
                        'entries' => array(
                            array(
                                'account_id' => $product_info['inventory_account_id'],
                                'debit' => $quantity * $new_cost,
                                'credit' => 0,
                                'description' => 'زيادة قيمة المخزون'
                            ),
                            array(
                                'account_id' => $this->config->get('config_inventory_adjustment_account'),
                                'debit' => 0,
                                'credit' => $quantity * $new_cost,
                                'description' => 'تعديل تكلفة المخزون'
                            )
                        )
                    );

                    $journal_id = $this->model_accounts_journal->addJournalEntry($journal_entry);

                    if ($journal_id) {
                        $json['success'] = $this->language->get('text_success');
                        $json['old_cost'] = $current_cost;
                        $json['new_cost'] = $new_average_cost;
                        $json['journal_id'] = $journal_id;

                        // تسجيل العملية في نظام التدقيق
                        $this->central_service->logActivity('product_cost_update', 'inventory',
                            'تم تحديث تكلفة المنتج ' . $product_id . ' من ' . $current_cost . ' إلى ' . $new_average_cost, $product_id);
                    } else {
                        $json['error'] = $this->language->get('error_journal_entry');
                    }
                } else {
                    $json['error'] = $this->language->get('error_product_not_found');
                }
            } else {
                $json['error'] = $this->language->get('error_cost_data_invalid');
            }
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * إنشاء كود حساب المخزون
     */
    private function generateInventoryAccountCode($product_id) {
        $base_code = $this->config->get('config_inventory_account_prefix') ?: '1301';
        return $base_code . str_pad($product_id, 6, '0', STR_PAD_LEFT);
    }

    /**
     * الاستيراد والتصدير المتقدم
     */
    public function importProducts() {
        $this->load->language('inventory/product');

        $json = array();

        if (!$this->user->hasPermission('modify', 'inventory/product')) {
            $json['error'] = $this->language->get('error_permission');
        } else {
            if (isset($this->request->files['import_file'])) {
                $file = $this->request->files['import_file'];

                if ($file['error'] == UPLOAD_ERR_OK) {
                    $file_extension = pathinfo($file['name'], PATHINFO_EXTENSION);

                    if (in_array(strtolower($file_extension), array('csv', 'xlsx', 'xls'))) {
                        $this->load->model('inventory/product');

                        $import_result = $this->model_inventory_product->importProductsFromFile($file['tmp_name'], $file_extension);

                        if ($import_result['success']) {
                            $json['success'] = $this->language->get('text_import_success');
                            $json['imported_count'] = $import_result['imported_count'];
                            $json['skipped_count'] = $import_result['skipped_count'];
                            $json['errors'] = $import_result['errors'];

                            // تسجيل العملية في نظام التدقيق
                            $this->central_service->logActivity('products_import', 'inventory',
                                'تم استيراد ' . $import_result['imported_count'] . ' منتج من ملف: ' . $file['name']);
                        } else {
                            $json['error'] = $import_result['error'];
                        }
                    } else {
                        $json['error'] = $this->language->get('error_file_type_invalid');
                    }
                } else {
                    $json['error'] = $this->language->get('error_file_upload');
                }
            } else {
                $json['error'] = $this->language->get('error_no_file');
            }
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * تصدير المنتجات
     */
    public function exportProducts() {
        $this->load->language('inventory/product');

        if (!$this->user->hasPermission('access', 'inventory/product')) {
            $this->response->redirect($this->url->link('error/permission', 'user_token=' . $this->session->data['user_token'], true));
        }

        $this->load->model('inventory/product');

        $export_format = isset($this->request->get['format']) ? $this->request->get['format'] : 'csv';
        $filter_data = array(
            'filter_category' => isset($this->request->get['filter_category']) ? $this->request->get['filter_category'] : '',
            'filter_status' => isset($this->request->get['filter_status']) ? $this->request->get['filter_status'] : '',
            'include_variants' => isset($this->request->get['include_variants']) ? true : false,
            'include_pricing' => isset($this->request->get['include_pricing']) ? true : false
        );

        $export_result = $this->model_inventory_product->exportProducts($export_format, $filter_data);

        if ($export_result['success']) {
            // تحديد headers للتحميل
            $filename = 'products_export_' . date('Y-m-d_H-i-s') . '.' . $export_format;

            header('Content-Type: application/octet-stream');
            header('Content-Disposition: attachment; filename="' . $filename . '"');
            header('Content-Length: ' . filesize($export_result['file_path']));

            readfile($export_result['file_path']);

            // حذف الملف المؤقت
            unlink($export_result['file_path']);

            // تسجيل العملية في نظام التدقيق
            $this->central_service->logActivity('products_export', 'inventory',
                'تم تصدير المنتجات بصيغة: ' . $export_format);
        } else {
            $this->session->data['error'] = $export_result['error'];
            $this->response->redirect($this->url->link('inventory/product', 'user_token=' . $this->session->data['user_token'], true));
        }
    }
}
