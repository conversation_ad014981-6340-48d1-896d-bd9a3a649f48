{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="submit" form="form-shipping" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fa fa-save"></i></button>
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a></div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>

  <div class="container-fluid">
    <form action="{{ action }}" method="post" enctype="multipart/form-data" id="form-shipping" class="form-horizontal">
     <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pencil"></i> {{ text_edit }}</h3>
      </div>
      <div class="panel-body">
          <div class="form-group">
            <label class="col-sm-2 control-label">{{ entry_test }}</label>
            <div class="col-sm-10">
              <label class="radio-inline">
                {% if shipping_hitshippo_aramex_test %}
                <input type="radio" name="shipping_hitshippo_aramex_test" value="1" checked="checked" />
                {{ text_yes }}
                {% else %}
                <input type="radio" name="shipping_hitshippo_aramex_test" value="1" />
                {{ text_yes }}
                {% endif %}
              </label>
              <label class="radio-inline">
                {% if not shipping_hitshippo_aramex_test %}
                <input type="radio" name="shipping_hitshippo_aramex_test" value="0" checked="checked" />
                {{ text_no }}
                {% else %}
                <input type="radio" name="shipping_hitshippo_aramex_test" value="0" />
                {{ text_no }}
                {% endif %}
              </label>
            </div>
          </div>
      <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-key">{{ entry_key }}</label>
            <div class="col-sm-10">
              <input type="text" name="shipping_hitshippo_aramex_key" value="{{ shipping_hitshippo_aramex_key }}" placeholder="{{ entry_key }}" id="input-key" class="form-control" />
              {% if error_key %}
              <div class="text-danger">{{ error_key }}</div>
              {% endif %}
            </div>
          </div>
            <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-account">{{ entry_password }}</label>
            <div class="col-sm-10">
              <input type="text" name="shipping_hitshippo_aramex_password" value="{{ shipping_hitshippo_aramex_password }}" placeholder="{{ entry_password }}" id="input-account" class="form-control" />
             </div>
          </div>
          
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-account">{{ entry_account }}</label>
            <div class="col-sm-10">
              <input type="text" name="shipping_hitshippo_aramex_account" value="{{ shipping_hitshippo_aramex_account }}" placeholder="{{ entry_account }}" id="input-account" class="form-control" />
              {% if error_account %}
              <div class="text-danger">{{ error_account }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-account-entity">{{ entry_account_entity }}</label>
            <div class="col-sm-10">
              <input type="text" name="shipping_hitshippo_aramex_account_entity" value="{{ shipping_hitshippo_aramex_account_entity }}" placeholder="{{ entry_account_entity }}" id="input-account-entity" class="form-control" />
            </div>
          </div>
           <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-account-pin">{{ entry_account_pin }}</label>
            <div class="col-sm-10">
              <input type="text" name="shipping_hitshippo_aramex_account_pin" value="{{ shipping_hitshippo_aramex_account_pin }}" placeholder="{{ entry_account_pin }}" id="input-account-pin" class="form-control" />
            </div>
          </div>
        <div class="form-group">
            <label class="col-sm-2 control-label"><span data-toggle="tooltip" title="{{ entry_front_end_logs }}">{{ entry_front_end_logs }}</span></label>
            <div class="col-sm-10">
              <label class="radio-inline">
                {% if shipping_hitshippo_aramex_front_end_logs %}
                <input type="radio" name="shipping_hitshippo_aramex_front_end_logs" value="1" checked="checked" />
                {{ text_yes }}
                {% else %}
                <input type="radio" name="shipping_hitshippo_aramex_front_end_logs" value="1" />
                {{ text_yes }}
                {% endif %}
              </label>
              <label class="radio-inline">
                {% if not shipping_hitshippo_aramex_front_end_logs  %}
                <input type="radio" name="shipping_hitshippo_aramex_front_end_logs" value="0" checked="checked" />
                {{ text_no }}
                {% else %}
                <input type="radio" name="shipping_hitshippo_aramex_front_end_logs" value="0" />
                {{ text_no }}
                {% endif %}
              </label>
            </div>
          </div>
       <div class="form-group">
            <label class="col-sm-2 control-label"><span data-toggle="tooltip" title="{{ help_display_time }}">{{ entry_display_time }}</span></label>
            <div class="col-sm-10">
              <label class="radio-inline">
                {% if shipping_hitshippo_aramex_display_time %}
                <input type="radio" name="shipping_hitshippo_aramex_display_time" value="1" checked="checked" />
                {{ text_yes }}
                {% else %}
                <input type="radio" name="shipping_hitshippo_aramex_display_time" value="1" />
                {{ text_yes }}
                {% endif %}
              </label>
              <label class="radio-inline">
                {% if not shipping_hitshippo_aramex_display_time %}
                <input type="radio" name="shipping_hitshippo_aramex_display_time" value="0" checked="checked" />
                {{ text_no }}
                {% else %}
                <input type="radio" name="shipping_hitshippo_aramex_display_time" value="0" />
                {{ text_no }}
                {% endif %}
              </label>
            </div>
          </div>
      <div class="form-group">
            <label class="col-sm-2 control-label" for="input-status">{{ entry_status }}</label>
            <div class="col-sm-10">
              <select name="shipping_hitshippo_aramex_status" id="input-status" class="form-control">
                {% if shipping_hitshippo_aramex_status %}
                <option value="1" selected="selected">{{ text_enabled }}</option>
                <option value="0">{{ text_disabled }}</option>
                {% else %}
                <option value="1">{{ text_enabled }}</option>
                <option value="0" selected="selected">{{ text_disabled }}</option>
                {% endif %}
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-sort-order">{{ entry_sort_order }}</label>
            <div class="col-sm-10">
              <input type="text" name="shipping_hitshippo_aramex_sort_order" value="{{ shipping_hitshippo_aramex_sort_order }}" placeholder="{{ entry_sort_order }}" id="input-sort-order" class="form-control" />
            </div>
          </div>
    </div>
  </div>
  <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pencil"></i> {{ text_shiiping_address }}</h3>
      </div>
      <div class="panel-body">
      <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-shipper_name">{{ entry_shipper_name }}</label>
            <div class="col-sm-10">
              <input type="text" name="shipping_hitshippo_aramex_shipper_name" value="{{ shipping_hitshippo_aramex_shipper_name }}" placeholder="{{ entry_shipper_name }}" id="input-shipper_name" class="form-control" />
              {% if error_shipper_name %}
              <div class="text-danger">{{ error_shipper_name }}</div>
              {% endif %}
            </div>
          </div>
        <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-company_name">{{ entry_company_name }}</label>
            <div class="col-sm-10">
              <input type="text" name="shipping_hitshippo_aramex_company_name" value="{{ shipping_hitshippo_aramex_company_name }}" placeholder="{{ entry_company_name }}" id="input-company_name" class="form-control" />
              {% if error_company_name %}
              <div class="text-danger">{{ error_company_name }}</div>
              {% endif %}
            </div>
          </div>
      <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-phone_num">{{ entry_phone_num }}</label>
            <div class="col-sm-10">
              <input type="text" name="shipping_hitshippo_aramex_phone_num" value="{{ shipping_hitshippo_aramex_phone_num }}" placeholder="{{ entry_phone_num }}" id="input-phone_num" class="form-control" />
              {% if error_phone_num %}
              <div class="text-danger">{{ error_phone_num }}</div>
              {% endif %}
            </div>
          </div>
      <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-email_addr">{{ entry_email_addr }}</label>
            <div class="col-sm-10">
              <input type="text" name="shipping_hitshippo_aramex_email_addr" value="{{ shipping_hitshippo_aramex_email_addr }}" placeholder="{{ entry_email_addr }}" id="input-email_addr" class="form-control" />
              {% if error_email_addr %}
              <div class="text-danger">{{ error_email_addr }}</div>
              {% endif %}
            </div>
          </div>
      <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-address1">{{ entry_address1 }}</label>
            <div class="col-sm-10">
              <input type="text" name="shipping_hitshippo_aramex_address1" value="{{ shipping_hitshippo_aramex_address1 }}" placeholder="{{ entry_address1 }}" id="input-address1" class="form-control" />
              {% if error_address1 %}
              <div class="text-danger">{{ error_address1 }}</div>
              {% endif %}
            </div>
          </div>
      <div class="form-group">
            <label class="col-sm-2 control-label" for="input-address2">{{ entry_address2 }}</label>
            <div class="col-sm-10">
              <input type="text" name="shipping_hitshippo_aramex_address2" value="{{ shipping_hitshippo_aramex_address2 }}" placeholder="{{ entry_address2 }}" id="input-address2" class="form-control" />
              {% if error_address2 %}
              <div class="text-danger">{{ error_address2 }}</div>
              {% endif %}
            </div>
          </div>
      <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-city">{{ entry_city }}</label>
            <div class="col-sm-10">
              <input type="text" name="shipping_hitshippo_aramex_city" value="{{ shipping_hitshippo_aramex_city }}" placeholder="{{ entry_city }}" id="input-city" class="form-control" />
              {% if error_city %}
              <div class="text-danger">{{ error_city }}</div>
              {% endif %}
            </div>
          </div>
      <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-state">{{ entry_state }}</label>
            <div class="col-sm-10">
              <input type="text" name="shipping_hitshippo_aramex_state" value="{{ shipping_hitshippo_aramex_state }}" placeholder="{{ entry_state }}" id="input-state" class="form-control" />
              {% if error_state %}
              <div class="text-danger">{{ error_state }}</div>
              {% endif %}
            </div>
          </div>
        <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-country_code">{{ entry_country_code }}</label>
            <div class="col-sm-10">
        <select name="shipping_hitshippo_aramex_country_code" class="form-control control-label">
        {% for key,value in countrylist %}
        <option value="{{key}}" {% if key == shipping_hitshippo_aramex_country_code %}selected="true"{% endif %}>{{value}}</option>
        {% endfor %}
        </select>
        
              {% if error_country_code %}
              <div class="text-danger">{{ error_country_code }}</div>
              {% endif %}
            </div>
          </div>
        
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-postcode">{{ entry_postcode }}</label>
            <div class="col-sm-10">
              <input type="text" name="shipping_hitshippo_aramex_postcode" value="{{ shipping_hitshippo_aramex_postcode }}" placeholder="{{ entry_postcode }}" id="input-postcode" class="form-control" />
              {% if error_postcode %}
              <div class="text-danger">{{ error_postcode }}</div>
              {% endif %}
            </div>
          </div>
          
    </div>
  </div>
  <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pencil"></i> {{ text_rates }}</h3>
      </div>
      <div class="panel-body">
          <div class="form-group">
            <label class="col-sm-2 control-label">{{ entry_realtime_rates }}</label>
            <div class="col-sm-10">
              <label class="radio-inline">
                {% if shipping_hitshippo_aramex_realtime_rates %}
                <input type="radio" name="shipping_hitshippo_aramex_realtime_rates" value="1" checked="checked" />
                {{ text_yes }}
                {% else %}
                <input type="radio" name="shipping_hitshippo_aramex_realtime_rates" value="1" />
                {{ text_yes }}
                {% endif %}
              </label>
              <label class="radio-inline">
                {% if not shipping_hitshippo_aramex_realtime_rates %}
                <input type="radio" name="shipping_hitshippo_aramex_realtime_rates" value="0" checked="checked" />
                {{ text_no }}
                {% else %}
                <input type="radio" name="shipping_hitshippo_aramex_realtime_rates" value="0" />
                {{ text_no }}
                {% endif %}
              </label>
            </div>
          </div>
     

          <div class="form-group">
            <label class="col-sm-2 control-label"><span data-toggle="tooltip" title="If recipient address is in other language, automatically converted to English.">Address translation</span></label>
            <div class="col-sm-10">
              {% if shipping_hitshippo_aramex_translation %}
                <input type="checkbox" name="shipping_hitshippo_aramex_translation" id="shipping_hitshippo_aramex_translation" checked="checked">
              {% else %}
                <input type="checkbox" name="shipping_hitshippo_aramex_translation" id="shipping_hitshippo_aramex_translation">
              {% endif %}
              <span style="margin-left:5px;">Enable / Disable</span>
            </div>
          </div>

          <div class="form-group required" id="trans_key" style="display:none;">
            <label class="col-sm-2 control-label" for="shipping_hitshippo_aramex_translation_key"><span data-toggle="tooltip" title="Enter activated google cloud api key having traslation access.">Google Cloud API Key</label>
            <div class="col-sm-10">
              <input type="text" name="shipping_hitshippo_aramex_translation_key" value="{{ shipping_hitshippo_aramex_translation_key }}" placeholder="Enter google's cloud API key" id="shipping_hitshippo_aramex_translation_key" class="form-control" />
            </div>
          </div>
     
       <div class="form-group">
            <label class="col-sm-2 control-label" for="input-rate-type">{{ entry_payment_type }}</label>
            <div class="col-sm-10">
              <select name="shipping_hitshippo_aramex_payment_type" id="input-rate-type" class="form-control">
                {% if shipping_hitshippo_aramex_payment_type == 'P' %}
                <option value="P" selected="selected">{{ text_prepaid_payment }}</option>
                {% else %}
                <option value="P">{{ text_prepaid_payment }}</option>
                {% endif %}
                
              </select>
            </div>
          </div>
{# 
          <div class="form-group">
            <label class="col-sm-2 control-label" for="shipping_hitshippo_aramex_pay_con">Payment country</label>
            <div class="col-sm-10">
              <select name="shipping_hitshippo_aramex_pay_con" id="shipping_hitshippo_aramex_pay_con" class="form-control">
                {% if shipping_hitshippo_aramex_pay_con == 'S' %}
                <option value="S" selected="selected">Sender</option>
                {% else %}
                <option value="S">Sender</option>
                {% endif %}
                {% if shipping_hitshippo_aramex_pay_con == 'R' %}
                <option value="R" selected="selected">Receiver</option>
                {% else %}
                <option value="R">Receiver</option>
                {% endif %}
                {% if shipping_hitshippo_aramex_pay_con == 'C' %}
                <option value="C" selected="selected">Custom</option>
                {% else %}
                <option value="C">Custom</option>
                {% endif %}
              </select>
            </div>
          </div> #}

        <div class="form-group" id="cus_pay_con" style="display:none;">
            <label class="col-sm-2 control-label" for="shipping_hitshippo_aramex_cus_pay_con">Custom Payment Country</label>
            <div class="col-sm-10">
              <select name="shipping_hitshippo_aramex_cus_pay_con" class="form-control control-label" style="text-align:left;">
              {% for key,value in countrylist %}
              <option value="{{key}}" {% if key == shipping_hitshippo_aramex_cus_pay_con %}selected="true"{% endif %}>{{value}}</option>
              {% endfor %}
              </select>
            </div>
          </div>
      
      <div class="form-group">
            <label class="col-sm-2 control-label">{{ entry_service }}</label>
            <div class="col-sm-10">
              <div class="well well-sm" style="height: 150px; overflow: auto;">
                {% for service in services %}
                <div class="checkbox">
                  <label>
                    {% if service.value in shipping_hitshippo_aramex_service %}
                    <input type="checkbox" name="shipping_hitshippo_aramex_service[]" value="{{ service.value }}" checked="checked" />
                    {{ service.text }}
                    {% else %}
                    <input type="checkbox" name="shipping_hitshippo_aramex_service[]" value="{{ service.value }}" />
                    {{ service.text }}
                    {% endif %}
                  </label>
                </div>
                {% endfor %}
              </div>
              <button type="button" onclick="$(this).parent().find(':checkbox').prop('checked', true);" class="btn btn-link">{{ text_select_all }}</button> / <button type="button" onclick="$(this).parent().find(':checkbox').prop('checked', false);" class="btn btn-link">{{ text_unselect_all }}</button></div>
          </div>
      </div>
    </div>
  <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pencil"></i> {{ text_packing }}</h3>
      </div>
      <div class="panel-body">
     <div class="form-group">
            <label class="col-sm-2 control-label">{{ _entry_weight }}</label>
            <div class="col-sm-10">
              <label class="radio-inline">
                {% if shipping_hitshippo_aramex_weight %}
                <input type="radio" name="shipping_hitshippo_aramex_weight" value="1" checked="checked" />
                {{ _entry_lbin }}
                {% else %}
                <input type="radio" name="shipping_hitshippo_aramex_weight" value="1" />
                {{ _entry_lbin }}
                {% endif %}
              </label>
              <label class="radio-inline">
                {% if not shipping_hitshippo_aramex_weight %}
                <input type="radio" name="shipping_hitshippo_aramex_weight" value="0" checked="checked" />
                {{ _entry_kgcm }}
                {% else %}
                <input type="radio" name="shipping_hitshippo_aramex_weight" value="0" />
                {{ _entry_kgcm }}
                {% endif %}
              </label>
            </div>
          </div>
       <div class="form-group">
            <label class="col-sm-2 control-label" for="input-packaging-type">{{ _entry_packing_type }}</label>
            <div class="col-sm-10">
              <select name="shipping_hitshippo_aramex_packing_type" id="input-packing-type" class="form-control">
                {% if shipping_hitshippo_aramex_packing_type == 'per_item' %}
                <option value="per_item" selected="selected">{{ text_per_item }}</option>
                {% else %}
                <option value="per_item">{{ text_per_item }}</option>
                {% endif %}
                {% if shipping_hitshippo_aramex_packing_type == 'weight_based' %}
                <option value="weight_based" selected="selected">{{ text_aramex_weight_based }}</option>
                {% else %}
                <option value="weight_based">{{ text_aramex_weight_based }}</option>
                {% endif %}
              </select>
            </div>
          </div>
         </div>
    </div>
  
  
  <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pencil"></i> {{ text_weight_head }}</h3>
      </div>
      <div class="panel-body">
      
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-postcode">{{ text_head12 }}</label>
            <div class="col-sm-10">
              <input type="text" name="shipping_hitshippo_aramex_wight_b" value="{{ shipping_hitshippo_aramex_wight_b }}" placeholder="{{ text_head12 }}" id="input-wight_b" class="form-control" />
              {% if error_wight_b %}
              <div class="text-danger">{{ error_wight_b }}</div>
              {% endif %}
            </div>
          </div>
      <div class="form-group">
            <label class="col-sm-2 control-label" for="input-packaging-type">{{ _entry_packing_type }}</label>
            <div class="col-sm-10">
              <select name="shipping_hitshippo_aramex_weight_c" id="input-packing-type" class="form-control">
                {% if shipping_hitshippo_aramex_weight_c == 'pack_descending' %}
                <option value="pack_descending" selected="selected">{{ text_head13 }}</option>
                {% else %}
                <option value="pack_descending">{{ text_head13 }}</option>
                {% endif %}
                {% if shipping_hitshippo_aramex_weight_c == 'pack_ascending' %}
                <option value="pack_ascending" selected="selected">{{ text_head14 }}</option>
                {% else %}
                <option value="pack_ascending">{{ text_head14 }}</option>
                {% endif %}
                {% if shipping_hitshippo_aramex_weight_c == 'pack_simple' %}
                <option value="pack_simple" selected="selected">{{ text_head15 }}</option>
                {% else %}
                <option value="pack_simple">{{ text_head15 }}</option>
                {% endif %}
              </select>
            </div>
          </div>
     </div>
    </div>
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pencil"></i> {{ text_label }} </h3>
      </div>
      <div class="panel-body">
         <div class="form-group">
            <label class="col-sm-2 control-label" for="input-aramex_int_key">HIT-Shipo Integration Key<span data-toggle="tooltip" title="Enter/Get integration key"><b style="color:red;">*</b></span></label>
            <div class="col-sm-10">
             <input type="text" name="shipping_hitshippo_aramex_int_key" value="{{ shipping_hitshippo_aramex_int_key }}" placeholder="" id="input-aramex_int_key" class="form-control" />
             <a href="https://app.hitshipo.com/" target="_blank">Don't have a key? Signup for free</a>
            </div>
          </div>
         <div class="form-group">
            <label class="col-sm-2 control-label" for="input-aramex_send_mail_to">Auto label generation<span data-toggle="tooltip" title="Need automated label generation??"><b style="color:red;">*</b></span></label>
            <div class="col-sm-10">
              <label class="radio-inline">
                {% if shipping_hitshippo_aramex_auto_label %}
                <input type="radio" name="shipping_hitshippo_aramex_auto_label" value="1" checked="checked" />
                {{ text_enable }}
                {% else %}
                <input type="radio" name="shipping_hitshippo_aramex_auto_label" value="1" />
                {{ text_enable }}
                {% endif %}
              </label>
              <label class="radio-inline">
                {% if not shipping_hitshippo_aramex_auto_label %}
                <input type="radio" name="shipping_hitshippo_aramex_auto_label" value="0" checked="checked" />
                {{ text_disable }}
                {% else %}
                <input type="radio" name="shipping_hitshippo_aramex_auto_label" value="0" />
                {{ text_disable }}
                {% endif %}
              </label>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="shipping_hitshippo_aramex_language">Product Name Language</label>
            <div class="col-sm-10">
              <select name="shipping_hitshippo_aramex_language" id="shipping_hitshippo_aramex_language" class="form-control">
              {% if shipping_hitshippo_aramex_language == 'default' %}
                <option value="default" selected="selected">Default</option>
                {% else %}
                <option value="default">Default</option>
                {% endif %}
                 {% for key,value in languages %}
                    <option value="{{value.language_id}}" {% if value.language_id == shipping_hitshippo_aramex_language %}selected="true"{% endif %}>{{value.name}}</option>
              {% endfor %}
              </select>
            </div>
          </div>
         <div class="form-group">
            <label class="col-sm-2 control-label" for="input-aramex_send_mail_to">Email to receive generated labels<span data-toggle="tooltip" title="To whom you want to sent the shipping label once created"><b style="color:red;">*</b></span></label>
            <div class="col-sm-10">
             <input type="text" name="shipping_hitshippo_aramex_send_mail_to" value="{{ shipping_hitshippo_aramex_send_mail_to }}" placeholder="" id="input-aramex_send_mail_to" class="form-control" />
            </div>
          </div>

    
      <div class="form-group">
            <label class="col-sm-2 control-label">{{ text_head17 }}</label>
            <div class="col-sm-10">
              <label class="radio-inline">
                {% if shipping_hitshippo_aramex_sat %}
                <input type="radio" name="shipping_hitshippo_aramex_sat" value="1" checked="checked" />
                {{ text_enable }}
                {% else %}
                <input type="radio" name="shipping_hitshippo_aramex_sat" value="1" />
                {{ text_enable }}
                {% endif %}
              </label>
              <label class="radio-inline">
                {% if not shipping_hitshippo_aramex_sat %}
                <input type="radio" name="shipping_hitshippo_aramex_sat" value="0" checked="checked" />
                {{ text_disable }}
                {% else %}
                <input type="radio" name="shipping_hitshippo_aramex_sat" value="0" />
                {{ text_disable }}
                {% endif %}
              </label>
            </div>
          </div>
      <div class="form-group">
            <label class="col-sm-2 control-label">{{ text_head44 }}</label>
            <div class="col-sm-10">
              <label class="radio-inline">
                {% if shipping_hitshippo_aramex_cod %}
                <input type="radio" name="shipping_hitshippo_aramex_cod" value="1" checked="checked" />
                {{ text_enable }}
                {% else %}
                <input type="radio" name="shipping_hitshippo_aramex_cod" value="1" />
                {{ text_enable }}
                {% endif %}
              </label>
              <label class="radio-inline">
                {% if not shipping_hitshippo_aramex_cod %}
                <input type="radio" name="shipping_hitshippo_aramex_cod" value="0" checked="checked" />
                {{ text_disable }}
                {% else %}
                <input type="radio" name="shipping_hitshippo_aramex_cod" value="0" />
                {{ text_disable }}
                {% endif %}
              </label>
            </div>
          </div>
      <div class="form-group">
            <label class="col-sm-2 control-label">{{ text_head18 }}</label>
            <div class="col-sm-10">
              <label class="radio-inline">
                {% if shipping_hitshippo_aramex_email_trach %}
                <input type="radio" name="shipping_hitshippo_aramex_email_trach" value="1" checked="checked" />
                {{ text_enable }}
                {% else %}
                <input type="radio" name="shipping_hitshippo_aramex_email_trach" value="1" />
                {{ text_enable }}
                {% endif %}
              </label>
              <label class="radio-inline">
                {% if not shipping_hitshippo_aramex_email_trach %}
                <input type="radio" name="shipping_hitshippo_aramex_email_trach" value="0" checked="checked" />
                {{ text_disable }}
                {% else %}
                <input type="radio" name="shipping_hitshippo_aramex_email_trach" value="0" />
                {{ text_disable }}
                {% endif %}
              </label>
            </div>
          </div>
      
     
      
        {# <div class="form-group">
            <label class="col-sm-2 control-label" for="input-dropoff-type">{{ text_head41 }}</label>
            <div class="col-sm-10">
              <select name="shipping_hitshippo_aramex_duty_type" id="input-dropoff-type" class="form-control">
                {% if shipping_hitshippo_aramex_duty_type == '' %}
                <option value="" selected="selected">{{ text_head37 }}</option>
                {% else %}
                <option value="">{{ text_head37 }}</option>
                {% endif %}
                {% if shipping_hitshippo_aramex_duty_type == 'S' %}
                <option value="S" selected="selected">{{ text_head38 }}</option>
                {% else %}
                <option value="S">{{ text_head38 }}</option>
                {% endif %}
                {% if shipping_hitshippo_aramex_duty_type == 'R' %}
                <option value="R" selected="selected">{{ text_head39 }}</option>
                {% else %}
                <option value="R">{{ text_head39 }}</option>
                {% endif %}
        {% if shipping_hitshippo_aramex_duty_type == 'T' %}
                <option value="T" selected="selected">{{ text_head40 }}</option>
                {% else %}
                <option value="T">{{ text_head40 }}</option>
                {% endif %}
              </select>
            </div>
          </div> #}
      
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-shipment_content">{{ text_head42 }}</label>
            <div class="col-sm-10">
              <input type="text" name="shipping_hitshippo_aramex_shipment_content" value="{{ shipping_hitshippo_aramex_shipment_content }}" placeholder="{{ text_head42 }}" id="input-shipment_content" class="form-control" />
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label"><span data-toggle="tooltip" title="It adds history when creating shipment Enter comment text to show on history">Add History</span></label>
            <div class="col-sm-10">
              {% if shipping_hitshippo_aramex_addcomment_check %}
                <input type="checkbox" name="shipping_hitshippo_aramex_addcomment_check" checked="checked">
              {% else %}
                <input type="checkbox" name="shipping_hitshippo_aramex_addcomment_check">
              {% endif %}
              <span style="margin-left:5px;">Enable / Disable</span>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label">Add comment</label>
            <div class="col-sm-10"> 
              <textarea name="shipping_hitshippo_aramex_addcomment_box" value="" placeholder="{{ text_head46}}" class="form-control" >{{ shipping_hitshippo_aramex_addcomment_box }}</textarea>
            </div>
          </div>
          
        </div>
      </div>

{# Pickup start #}
    {# <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pencil"></i> {{ text_pickup }} </h3>
      </div>

        <div class="panel-body">
          <div class="form-group">
            <label class="col-sm-2 control-label">{{ text_head45 }}</label>
            <div class="col-sm-10">
              <label class="radio-inline">
                {% if shipping_hitshippo_aramex_pickup_auto %}
                <input type="radio" name="shipping_hitshippo_aramex_pickup_auto" value="1" checked="checked" />
                {{ text_enable }}
                {% else %}
                <input type="radio" name="shipping_hitshippo_aramex_pickup_auto" value="1" />
                {{ text_enable }}
                {% endif %}
              </label>
              <label class="radio-inline">
                {% if not shipping_hitshippo_aramex_pickup_auto %}
                <input type="radio" name="shipping_hitshippo_aramex_pickup_auto" value="0" checked="checked" />
                {{ text_disable }}
                {% else %}
                <input type="radio" name="shipping_hitshippo_aramex_pickup_auto" value="0" />
                {{ text_disable }}
                {% endif %}
              </label>
            </div>
          </div>

          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-pickup_loc_type">{{ entry_pic_loc_type }}<b style="color:red;">*</b></label>
            <div class="col-sm-10">
            <select name="shipping_hitshippo_aramex_pickup_loc_type" class="form-control">
            {% for key,value in pickup_loc_type %}
            <option value="{{key}}" {% if key == shipping_hitshippo_aramex_pickup_loc_type %}selected="true"{% endif %}>{{value}}</option>
            {% endfor %}
            </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-pickup_del_type">Delivery Type<b style="color:red;">*</b></label>
            <div class="col-sm-10">
            <select name="shipping_hitshippo_aramex_pickup_del_type" class="form-control">
            {% for key,value in pickup_del_type %}
            <option value="{{key}}" {% if key == shipping_hitshippo_aramex_pickup_del_type %}selected="true"{% endif %}>{{value}}</option>
            {% endfor %}
            </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-pickup_type">Pickup Type<b style="color:red;">*</b></label>
            <div class="col-sm-10">
            <select name="shipping_hitshippo_aramex_pickup_type" id="shipping_hitshippo_aramex_pickup_type" class="form-control">
            {% for key,value in pickup_type %}
            <option value="{{key}}" {% if key == shipping_hitshippo_aramex_pickup_type %}selected="true"{% endif %}>{{value}}</option>
            {% endfor %}
            </select>
            </div>
          </div>

        <div class="form-group" id="pic_days_after">
            <label class="col-sm-2 control-label" for="input-pickup_days_after">Schedule pickup after days?<span data-toggle="tooltip" title="Pickup will be scheduled with date after several days (choosen) of order placement"><b style="color:red;">*</b></span></label>
            <div class="col-sm-10">
            <select name="shipping_hitshippo_aramex_pickup_days_after" class="form-control">
            {% for i in 1..10 %}
              {% if i == shipping_hitshippo_aramex_pickup_days_after %}
                <option value="{{i}}" selected="true">{{i}}</option>
              {% else %}
                <option value="{{i}}">{{i}}</option>
              {% endif %}
            {% endfor %}

            </select>
            </div>
          </div>

          <div class="form-group">
        <label class="col-sm-2 control-label" for="input-packaging-type">Package location<span data-toggle="tooltip" title="Where the package is kept in pickup location"><b style="color:red;">*</b></span></label>
        <div class="col-sm-10">
         <input type="text" name="shipping_hitshippo_aramex_pic_pack_lac" value="{{ shipping_hitshippo_aramex_pic_pack_lac }}" placeholder="e.g. front desk" id="input-account" class="form-control" />
        </div>
      </div>

      <div class="form-group">
        <label class="col-sm-2 control-label" for="input-packaging-type">Contact person Name<span data-toggle="tooltip" title="Enter name of person to contact"><b style="color:red;">*</b></span></label>
        <div class="col-sm-10">
         <input type="text" name="shipping_hitshippo_aramex_picper" value="{{ shipping_hitshippo_aramex_picper }}" placeholder="Person Name" id="input-account" class="form-control" />
        </div>
      </div>
      <div class="form-group">
        <label class="col-sm-2 control-label" for="input-packaging-type">Contact Phone Number<span data-toggle="tooltip" title="Enter contact no of person"><b style="color:red;">*</b></span></label>
        <div class="col-sm-10">
         <input type="text" name="shipping_hitshippo_aramex_piccon" value="{{ shipping_hitshippo_aramex_piccon }}" placeholder="Person Mobile" id="input-account" class="form-control" />
        </div>
      </div>
      <div class="form-group">
        <label class="col-sm-2 control-label" for="input-pic_open_time">Pickup Open Time<b style="color:red;">*</b></label>
          <div class="col-sm-10">
          <input type="text" name="shipping_hitshippo_aramex_pic_open_time" value="{{ shipping_hitshippo_aramex_pic_open_time }}" placeholder="e.g. 14:20 (for 2.20 pm)" id="input-account" class="form-control" />
          </div>
        </div>
      <div class="form-group">
        <label class="col-sm-2 control-label" for="input-pic_close_time">Pickup Close Time<b style="color:red;">*</b></label>
          <div class="col-sm-10">
          <input type="text" name="shipping_hitshippo_aramex_pic_close_time" value="{{ shipping_hitshippo_aramex_pic_close_time }}" placeholder="e.g. 14:20 (for 2.20 pm)" id="input-account" class="form-control" />
          </div>
          </div>
        </div>
    </div> #}

{# Pickup End #}
    
        </form>

    
    <div class="pull-right">
      <button type="submit" form="form-shipping" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fa fa-save"></i></button>
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a></div>
    </div>
      </div>
    </div>
    </div>
  
  </div>
</div>
{% block javascripts %}
<script type="text/javascript">

  jQuery(window).load(function(){
    jQuery('#hitshippo_aramex_shipping_add_trackingpin_shipmentid').change(function(){
      if(jQuery(hitshippo_aramex_shipping_add_trackingpin_shipmentid).is(':checked')) {
        jQuery('#aramex_email_service').show();
      }else
      {
        jQuery('#aramex_email_service').hide();
      }
    }).change();

    jQuery('#hitshippo_aramex_shipping_return_label_key').change(function(){
      if(jQuery('#hitshippo_aramex_shipping_return_label_key').is(':checked')) {
        jQuery('#hitshippo_return_label_acc_number').show();
      }else
      {
        jQuery('#hitshippo_return_label_acc_number').hide();
      }
    }).change();

    jQuery('#hitshippo_aramex_shipping_request_archive_airway_label').change(function(){
      if(jQuery('#hitshippo_aramex_shipping_request_archive_airway_label').is(':checked')) {
        jQuery('#hitshippo_no_of_archive_bills').show();
      }else
      {
        jQuery('#hitshippo_no_of_archive_bills').hide();
      }
    }).change();
    jQuery('#hitshippo_aramex_shipping_hitshippo_aramex_email_notification_service').change(function(){
      if(jQuery('#hitshippo_aramex_shipping_hitshippo_aramex_email_notification_service').is(':checked')) {
        jQuery('#hitshippo_aramex_email_notification_message').show();
      }else
      {
        jQuery('#hitshippo_aramex_email_notification_message').hide();
      }
    }).change();
    jQuery('#hitshippo_aramex_shipping_dutypayment_type').change(function(){
      if(jQuery(this).val() == 'T') {
        jQuery('#hitshippo_t_acc_number').show();
      }else
      {
        jQuery('#hitshippo_t_acc_number').hide();
      }
    }).change();

    jQuery('#shipping_hitshippo_aramex_pickup_type').change(function(){
      if(jQuery(this).val() == 'A') {
        jQuery('#pic_days_after').show();
      }else
      {
        jQuery('#pic_days_after').hide();
      }
    }).change();



    var pack_type_options = '{{ option_string }}';
    jQuery('.aramex_boxes .insert').click( function() {
      var $tbody = jQuery('.aramex_boxes').find('#rates');
      var size = $tbody.find('tr').size();
      var code = '<tr class="new">\
  <td  style="padding:3px;" class="check-column"><input type="checkbox" /></td>\
  <input type="hidden" size="1" name="boxes_id[' + size + ']" />\
  <td style="padding:3px;"><input type="text" size="25" name="boxes_name[' + size + ']" /></td>\
  <td style="padding:3px;"><input type="text" style="width:100%;" name="boxes_length[' + size + ']" /></td>\
  <td style="padding:3px;"><input type="text" style="width:100%;" name="boxes_width[' + size + ']" /></td>\
  <td style="padding:3px;"><input type="text" style="width:100%;" name="boxes_height[' + size + ']" /></td>\
  <td style="padding:3px;"><input type="text" style="width:100%;" name="boxes_box_weight[' + size + ']" /></td>\
  <td style="padding:3px;"><input type="text" style="width:100%;" name="boxes_max_weight[' + size + ']" /></td>\
  <td style="padding:3px;"><center><input type="checkbox" name="boxes_enabled[' + size + ']" /></center></td>\
  <td style="padding:3px;"><select name="boxes_pack_type[' + size + ']" >' + pack_type_options + '</select></td>\
  </tr>';
      $tbody.append( code );
      return false;
    });

    jQuery('.aramex_boxes .remove').click(function() {
      var $tbody = jQuery('.aramex_boxes').find('#rates');
      $tbody.find('.check-column input:checked').each(function() {
        jQuery(this).closest('tr').hide().find('input').val('');
      });

      return false;
    });

    var pay_con = jQuery('#shipping_hitshippo_aramex_pay_con').val();
    if (pay_con == "C") {
      jQuery('#cus_pay_con').show();
    }

    jQuery('#shipping_hitshippo_aramex_pay_con').change(function() {
      if ( $(this).val() == "C" ) {
        jQuery('#cus_pay_con').show();
      }else{
        jQuery('#cus_pay_con').hide();
      }
    });

    var translate = jQuery('#shipping_hitshippo_aramex_translation').is(":checked");
    if (translate) {
      jQuery('#trans_key').show();
    }

    jQuery('#shipping_hitshippo_aramex_translation').click(function() {
      if ( $(this).is(":checked") ) {
        jQuery('#trans_key').show();
      }else{
        jQuery('#trans_key').hide();
      }
    });


  });

</script>
{% endblock %}

{{ footer }}
