<?php
/**
 * نموذج المعاملات بين الشركات المتقدم
 * يدير المعاملات بين الشركات التابعة والزميلة مع إمكانية الإلغاء التلقائي
 * متوافق مع معايير التوحيد المحاسبية IFRS 10
 */
class ModelAccountsIntercompanyTransactions extends Model {
    private $central_service;

    public function __construct($registry) {
        parent::__construct($registry);
        
        // تحميل الخدمات المركزية
        $this->load->model('core/central_service_manager');
        $this->central_service = $this->model_core_central_service_manager;
    }

    /**
     * إنشاء معاملة بين الشركات
     */
    public function createTransaction($data) {
        $this->db->query("START TRANSACTION");

        try {
            // إنشاء المعاملة الأساسية
            $reference = $this->generateTransactionReference($data['transaction_type']);
            
            $this->db->query("INSERT INTO cod_intercompany_transactions SET 
                reference = '" . $this->db->escape($reference) . "',
                company_id = '" . (int)$data['company_id'] . "',
                related_company_id = '" . (int)$data['related_company_id'] . "',
                transaction_type = '" . $this->db->escape($data['transaction_type']) . "',
                transaction_date = '" . $this->db->escape($data['transaction_date']) . "',
                amount = '" . (float)$data['amount'] . "',
                currency = '" . $this->db->escape($data['currency']) . "',
                description = '" . $this->db->escape($data['description']) . "',
                external_reference = '" . $this->db->escape($data['reference']) . "',
                account_id = '" . (int)$data['account_id'] . "',
                status = 'pending',
                created_by = '" . (int)$this->user->getId() . "',
                date_added = NOW(),
                date_modified = NOW()
            ");

            $transaction_id = $this->db->getLastId();

            // إنشاء القيد المحاسبي
            $this->createTransactionJournalEntry($transaction_id, $data);

            // إنشاء المعاملة العكسية تلقائياً إذا كان مطلوباً
            if (isset($data['auto_create_reverse']) && $data['auto_create_reverse']) {
                $this->createReverseTransaction($transaction_id, $data);
            }

            $this->db->query("COMMIT");
            return $transaction_id;

        } catch (Exception $e) {
            $this->db->query("ROLLBACK");
            throw $e;
        }
    }

    /**
     * إنشاء المعاملة العكسية
     */
    private function createReverseTransaction($original_transaction_id, $original_data) {
        $reverse_reference = $this->generateTransactionReference($original_data['transaction_type'], 'REV');
        
        $this->db->query("INSERT INTO cod_intercompany_transactions SET 
            reference = '" . $this->db->escape($reverse_reference) . "',
            company_id = '" . (int)$original_data['related_company_id'] . "',
            related_company_id = '" . (int)$original_data['company_id'] . "',
            transaction_type = '" . $this->db->escape($this->getReverseTransactionType($original_data['transaction_type'])) . "',
            transaction_date = '" . $this->db->escape($original_data['transaction_date']) . "',
            amount = '" . (float)$original_data['amount'] . "',
            currency = '" . $this->db->escape($original_data['currency']) . "',
            description = 'Reverse: ' . $this->db->escape($original_data['description']) . "',
            external_reference = '" . $this->db->escape($original_data['reference']) . "',
            account_id = '" . (int)$this->getCorrespondingAccount($original_data['account_id'], $original_data['related_company_id']) . "',
            status = 'pending',
            related_transaction_id = '" . (int)$original_transaction_id . "',
            created_by = '" . (int)$this->user->getId() . "',
            date_added = NOW(),
            date_modified = NOW()
        ");

        $reverse_transaction_id = $this->db->getLastId();

        // ربط المعاملة الأصلية بالعكسية
        $this->db->query("UPDATE cod_intercompany_transactions SET 
            related_transaction_id = '" . (int)$reverse_transaction_id . "'
            WHERE transaction_id = '" . (int)$original_transaction_id . "'
        ");

        return $reverse_transaction_id;
    }

    /**
     * إنشاء القيد المحاسبي للمعاملة
     */
    private function createTransactionJournalEntry($transaction_id, $data) {
        $this->load->model('accounts/journal');
        
        // تحديد الحسابات المقابلة حسب نوع المعاملة
        $accounts = $this->getTransactionAccounts($data['transaction_type'], $data['company_id']);
        
        $journal_data = array(
            'reference' => 'IC-' . $transaction_id,
            'description' => 'Intercompany Transaction: ' . $data['description'],
            'journal_date' => $data['transaction_date'],
            'entries' => array()
        );

        // قيد الحساب الأساسي
        $journal_data['entries'][] = array(
            'account_id' => $data['account_id'],
            'debit' => $this->isDebitTransaction($data['transaction_type']) ? $data['amount'] : 0,
            'credit' => $this->isDebitTransaction($data['transaction_type']) ? 0 : $data['amount'],
            'description' => $data['description'],
            'currency' => $data['currency']
        );

        // قيد الحساب المقابل
        $journal_data['entries'][] = array(
            'account_id' => $accounts['contra_account'],
            'debit' => $this->isDebitTransaction($data['transaction_type']) ? 0 : $data['amount'],
            'credit' => $this->isDebitTransaction($data['transaction_type']) ? $data['amount'] : 0,
            'description' => $data['description'],
            'currency' => $data['currency']
        );

        $journal_id = $this->model_accounts_journal->addJournalEntry($journal_data);
        
        // ربط القيد بالمعاملة
        $this->db->query("UPDATE cod_intercompany_transactions SET 
            journal_id = '" . (int)$journal_id . "'
            WHERE transaction_id = '" . (int)$transaction_id . "'
        ");

        return $journal_id;
    }

    /**
     * إلغاء المعاملات بين الشركات
     */
    public function eliminateTransactions($data) {
        $this->db->query("START TRANSACTION");

        try {
            // إنشاء سجل الإلغاء
            $elimination_reference = $this->generateEliminationReference();
            
            $this->db->query("INSERT INTO cod_intercompany_eliminations SET 
                reference = '" . $this->db->escape($elimination_reference) . "',
                elimination_date = '" . $this->db->escape($data['elimination_date']) . "',
                elimination_method = '" . $this->db->escape($data['elimination_method']) . "',
                notes = '" . $this->db->escape($data['notes']) . "',
                status = 'completed',
                created_by = '" . (int)$this->user->getId() . "',
                date_created = NOW()
            ");

            $elimination_id = $this->db->getLastId();

            // معالجة كل معاملة
            $total_eliminated = 0;
            foreach ($data['transaction_ids'] as $transaction_id) {
                $transaction = $this->getTransaction($transaction_id);
                
                if ($transaction && $transaction['status'] != 'eliminated') {
                    // إضافة المعاملة لسجل الإلغاء
                    $this->db->query("INSERT INTO cod_intercompany_elimination_details SET 
                        elimination_id = '" . (int)$elimination_id . "',
                        transaction_id = '" . (int)$transaction_id . "',
                        eliminated_amount = '" . (float)$transaction['amount'] . "',
                        date_added = NOW()
                    ");

                    // تحديث حالة المعاملة
                    $this->db->query("UPDATE cod_intercompany_transactions SET 
                        status = 'eliminated',
                        elimination_id = '" . (int)$elimination_id . "',
                        date_modified = NOW()
                        WHERE transaction_id = '" . (int)$transaction_id . "'
                    ");

                    $total_eliminated += $transaction['amount'];
                }
            }

            // إنشاء قيود الإلغاء
            if ($total_eliminated > 0) {
                $this->createEliminationJournalEntries($elimination_id, $data['elimination_date']);
            }

            // تحديث إجمالي المبلغ المُلغى
            $this->db->query("UPDATE cod_intercompany_eliminations SET 
                total_eliminated = '" . (float)$total_eliminated . "'
                WHERE elimination_id = '" . (int)$elimination_id . "'
            ");

            $this->db->query("COMMIT");
            return $elimination_id;

        } catch (Exception $e) {
            $this->db->query("ROLLBACK");
            throw $e;
        }
    }

    /**
     * مطابقة المعاملات
     */
    public function matchTransactions($data) {
        $this->db->query("START TRANSACTION");

        try {
            // التحقق من صحة المطابقة
            $transaction1 = $this->getTransaction($data['transaction_id_1']);
            $transaction2 = $this->getTransaction($data['transaction_id_2']);

            if (!$this->validateTransactionMatch($transaction1, $transaction2, $data['tolerance_amount'])) {
                throw new Exception('Transactions cannot be matched - validation failed');
            }

            // إنشاء سجل المطابقة
            $this->db->query("INSERT INTO cod_intercompany_matches SET 
                transaction_id_1 = '" . (int)$data['transaction_id_1'] . "',
                transaction_id_2 = '" . (int)$data['transaction_id_2'] . "',
                match_type = '" . $this->db->escape($data['match_type']) . "',
                tolerance_amount = '" . (float)$data['tolerance_amount'] . "',
                notes = '" . $this->db->escape($data['notes']) . "',
                matched_by = '" . (int)$this->user->getId() . "',
                date_matched = NOW()
            ");

            $match_id = $this->db->getLastId();

            // تحديث حالة المعاملات
            $this->db->query("UPDATE cod_intercompany_transactions SET 
                status = 'matched',
                match_id = '" . (int)$match_id . "',
                date_modified = NOW()
                WHERE transaction_id IN ('" . (int)$data['transaction_id_1'] . "', '" . (int)$data['transaction_id_2'] . "')
            ");

            $this->db->query("COMMIT");
            return $match_id;

        } catch (Exception $e) {
            $this->db->query("ROLLBACK");
            throw $e;
        }
    }

    /**
     * البحث في المعاملات
     */
    public function searchTransactions($criteria) {
        $sql = "SELECT it.*, c1.name as company_name, c2.name as related_company_name,
                       a.code as account_code, ad.name as account_name,
                       u.username as created_by_name
                FROM cod_intercompany_transactions it
                LEFT JOIN cod_companies c1 ON (it.company_id = c1.company_id)
                LEFT JOIN cod_companies c2 ON (it.related_company_id = c2.company_id)
                LEFT JOIN cod_accounts a ON (it.account_id = a.account_id)
                LEFT JOIN cod_account_description ad ON (a.account_id = ad.account_id AND ad.language_id = '" . (int)$this->config->get('config_language_id') . "')
                LEFT JOIN cod_user u ON (it.created_by = u.user_id)
                WHERE 1=1";

        $params = array();

        if (!empty($criteria['company_id'])) {
            $sql .= " AND it.company_id = '" . (int)$criteria['company_id'] . "'";
        }

        if (!empty($criteria['related_company_id'])) {
            $sql .= " AND it.related_company_id = '" . (int)$criteria['related_company_id'] . "'";
        }

        if (!empty($criteria['transaction_type'])) {
            $sql .= " AND it.transaction_type = '" . $this->db->escape($criteria['transaction_type']) . "'";
        }

        if (!empty($criteria['date_from'])) {
            $sql .= " AND it.transaction_date >= '" . $this->db->escape($criteria['date_from']) . "'";
        }

        if (!empty($criteria['date_to'])) {
            $sql .= " AND it.transaction_date <= '" . $this->db->escape($criteria['date_to']) . "'";
        }

        if (!empty($criteria['amount_from'])) {
            $sql .= " AND it.amount >= '" . (float)$criteria['amount_from'] . "'";
        }

        if (!empty($criteria['amount_to'])) {
            $sql .= " AND it.amount <= '" . (float)$criteria['amount_to'] . "'";
        }

        if (!empty($criteria['status'])) {
            $sql .= " AND it.status = '" . $this->db->escape($criteria['status']) . "'";
        }

        if (!empty($criteria['reference'])) {
            $sql .= " AND (it.reference LIKE '%" . $this->db->escape($criteria['reference']) . "%' OR it.external_reference LIKE '%" . $this->db->escape($criteria['reference']) . "%')";
        }

        $sql .= " ORDER BY it.transaction_date DESC, it.transaction_id DESC";

        $query = $this->db->query($sql);
        return $query->rows;
    }

    /**
     * جلب الشركات المتاحة
     */
    public function getCompanies() {
        $query = $this->db->query("SELECT company_id, name, code, status 
            FROM cod_companies 
            WHERE status = '1' 
            ORDER BY name ASC
        ");

        return $query->rows;
    }

    /**
     * جلب آخر المعاملات
     */
    public function getRecentTransactions($limit = 20) {
        $query = $this->db->query("SELECT it.*, c1.name as company_name, c2.name as related_company_name,
                                          u.username as created_by_name
                                   FROM cod_intercompany_transactions it
                                   LEFT JOIN cod_companies c1 ON (it.company_id = c1.company_id)
                                   LEFT JOIN cod_companies c2 ON (it.related_company_id = c2.company_id)
                                   LEFT JOIN cod_user u ON (it.created_by = u.user_id)
                                   ORDER BY it.date_added DESC
                                   LIMIT " . (int)$limit
        );

        return $query->rows;
    }

    /**
     * جلب المعاملات غير المطابقة
     */
    public function getUnmatchedTransactions() {
        $query = $this->db->query("SELECT it.*, c1.name as company_name, c2.name as related_company_name
                                   FROM cod_intercompany_transactions it
                                   LEFT JOIN cod_companies c1 ON (it.company_id = c1.company_id)
                                   LEFT JOIN cod_companies c2 ON (it.related_company_id = c2.company_id)
                                   WHERE it.status = 'pending'
                                   ORDER BY it.transaction_date DESC
        ");

        return $query->rows;
    }

    /**
     * جلب الإحصائيات
     */
    public function getStatistics() {
        $stats = array();

        // إجمالي المعاملات
        $query = $this->db->query("SELECT COUNT(*) as total FROM cod_intercompany_transactions");
        $stats['total_transactions'] = $query->row['total'];

        // المعاملات المعلقة
        $query = $this->db->query("SELECT COUNT(*) as pending FROM cod_intercompany_transactions WHERE status = 'pending'");
        $stats['pending_transactions'] = $query->row['pending'];

        // المعاملات المطابقة
        $query = $this->db->query("SELECT COUNT(*) as matched FROM cod_intercompany_transactions WHERE status = 'matched'");
        $stats['matched_transactions'] = $query->row['matched'];

        // المعاملات المُلغاة
        $query = $this->db->query("SELECT COUNT(*) as eliminated FROM cod_intercompany_transactions WHERE status = 'eliminated'");
        $stats['eliminated_transactions'] = $query->row['eliminated'];

        // إجمالي المبالغ
        $query = $this->db->query("SELECT SUM(amount) as total_amount FROM cod_intercompany_transactions WHERE status != 'eliminated'");
        $stats['total_amount'] = $query->row['total_amount'] ?? 0;

        return $stats;
    }

    /**
     * جلب بيانات المعاملة
     */
    public function getTransaction($transaction_id) {
        $query = $this->db->query("SELECT it.*, c1.name as company_name, c2.name as related_company_name,
                                          a.code as account_code, ad.name as account_name,
                                          u.username as created_by_name
                                   FROM cod_intercompany_transactions it
                                   LEFT JOIN cod_companies c1 ON (it.company_id = c1.company_id)
                                   LEFT JOIN cod_companies c2 ON (it.related_company_id = c2.company_id)
                                   LEFT JOIN cod_accounts a ON (it.account_id = a.account_id)
                                   LEFT JOIN cod_account_description ad ON (a.account_id = ad.account_id AND ad.language_id = '" . (int)$this->config->get('config_language_id') . "')
                                   LEFT JOIN cod_user u ON (it.created_by = u.user_id)
                                   WHERE it.transaction_id = '" . (int)$transaction_id . "'
        ");

        return $query->num_rows ? $query->row : null;
    }

    /**
     * جلب المعاملات المرتبطة
     */
    public function getRelatedTransactions($transaction_id) {
        $query = $this->db->query("SELECT it.*, c1.name as company_name, c2.name as related_company_name
                                   FROM cod_intercompany_transactions it
                                   LEFT JOIN cod_companies c1 ON (it.company_id = c1.company_id)
                                   LEFT JOIN cod_companies c2 ON (it.related_company_id = c2.company_id)
                                   WHERE it.related_transaction_id = '" . (int)$transaction_id . "'
                                   OR it.transaction_id IN (
                                       SELECT related_transaction_id 
                                       FROM cod_intercompany_transactions 
                                       WHERE transaction_id = '" . (int)$transaction_id . "'
                                   )
        ");

        return $query->rows;
    }

    /**
     * دوال مساعدة
     */
    private function generateTransactionReference($type, $prefix = '') {
        $type_prefix = strtoupper(substr($type, 0, 3));
        $full_prefix = $prefix ? $prefix . '-' . $type_prefix : $type_prefix;
        $year = date('Y');
        $month = date('m');
        
        $query = $this->db->query("SELECT reference FROM cod_intercompany_transactions 
            WHERE reference LIKE '" . $full_prefix . "-" . $year . $month . "%' 
            ORDER BY reference DESC LIMIT 1
        ");

        if ($query->num_rows) {
            $last_number = (int)substr($query->row['reference'], -4);
            $new_number = $last_number + 1;
        } else {
            $new_number = 1;
        }

        return $full_prefix . '-' . $year . $month . '-' . str_pad($new_number, 4, '0', STR_PAD_LEFT);
    }

    private function generateEliminationReference() {
        $prefix = 'ELIM';
        $year = date('Y');
        $month = date('m');
        
        $query = $this->db->query("SELECT reference FROM cod_intercompany_eliminations 
            WHERE reference LIKE '" . $prefix . "-" . $year . $month . "%' 
            ORDER BY reference DESC LIMIT 1
        ");

        if ($query->num_rows) {
            $last_number = (int)substr($query->row['reference'], -4);
            $new_number = $last_number + 1;
        } else {
            $new_number = 1;
        }

        return $prefix . '-' . $year . $month . '-' . str_pad($new_number, 4, '0', STR_PAD_LEFT);
    }

    private function getReverseTransactionType($type) {
        $reverse_types = array(
            'sale' => 'purchase',
            'purchase' => 'sale',
            'loan' => 'loan',
            'dividend' => 'dividend',
            'management_fee' => 'management_fee'
        );

        return $reverse_types[$type] ?? $type;
    }

    private function isDebitTransaction($type) {
        $debit_types = array('purchase', 'loan', 'management_fee');
        return in_array($type, $debit_types);
    }

    private function validateTransactionMatch($transaction1, $transaction2, $tolerance) {
        if (!$transaction1 || !$transaction2) {
            return false;
        }

        // التحقق من أن الشركات متقابلة
        if ($transaction1['company_id'] != $transaction2['related_company_id'] ||
            $transaction1['related_company_id'] != $transaction2['company_id']) {
            return false;
        }

        // التحقق من المبلغ ضمن حدود التسامح
        $amount_diff = abs($transaction1['amount'] - $transaction2['amount']);
        if ($amount_diff > $tolerance) {
            return false;
        }

        return true;
    }

    private function getTransactionAccounts($type, $company_id) {
        // يمكن تطوير هذه الدالة لجلب الحسابات المناسبة حسب نوع المعاملة والشركة
        $accounts = array();
        
        switch ($type) {
            case 'sale':
                $accounts['contra_account'] = $this->config->get('config_intercompany_receivables_account');
                break;
            case 'purchase':
                $accounts['contra_account'] = $this->config->get('config_intercompany_payables_account');
                break;
            case 'loan':
                $accounts['contra_account'] = $this->config->get('config_intercompany_loans_account');
                break;
            default:
                $accounts['contra_account'] = $this->config->get('config_intercompany_general_account');
                break;
        }

        return $accounts;
    }

    private function getCorrespondingAccount($account_id, $company_id) {
        // يمكن تطوير هذه الدالة لجلب الحساب المقابل في الشركة الأخرى
        return $account_id; // مؤقتاً
    }

    /**
     * الحصول على تاريخ الإلغاء
     */
    public function getEliminationHistory($transaction_id) {
        $query = $this->db->query("
            SELECT e.*, u.firstname, u.lastname, u.username
            FROM cod_intercompany_eliminations e
            LEFT JOIN cod_user u ON (e.eliminated_by = u.user_id)
            WHERE e.transaction_id = '" . (int)$transaction_id . "'
            ORDER BY e.date_eliminated DESC
        ");

        $history = array();
        foreach ($query->rows as $row) {
            $history[] = array(
                'elimination_id'     => $row['elimination_id'],
                'elimination_type'   => $row['elimination_type'],
                'elimination_amount' => $this->currency->format($row['elimination_amount'], $this->config->get('config_currency')),
                'elimination_method' => $row['elimination_method'],
                'notes'             => $row['notes'],
                'eliminated_by'     => $row['firstname'] . ' ' . $row['lastname'] . ' (' . $row['username'] . ')',
                'date_eliminated'   => date($this->language->get('date_format_short'), strtotime($row['date_eliminated']))
            );
        }

        return $history;
    }

    /**
     * الحصول على قيود اليومية للمعاملة
     */
    public function getTransactionJournalEntries($transaction_id) {
        $query = $this->db->query("
            SELECT je.*, jel.account_id, jel.debit, jel.credit, jel.description as line_description,
                   a.account_code, a.account_name
            FROM cod_journals je
            LEFT JOIN cod_journal_entries jel ON (je.journal_id = jel.journal_id)
            LEFT JOIN cod_accounts a ON (jel.account_id = a.account_id)
            WHERE je.reference_type = 'intercompany_transaction'
            AND je.reference_id = '" . (int)$transaction_id . "'
            ORDER BY je.journal_id, jel.line_number
        ");

        $entries = array();
        $current_journal = null;

        foreach ($query->rows as $row) {
            if ($current_journal != $row['journal_id']) {
                $current_journal = $row['journal_id'];
                $entries[$current_journal] = array(
                    'journal_id'     => $row['journal_id'],
                    'journal_date'   => date($this->language->get('date_format_short'), strtotime($row['journal_date'])),
                    'reference'      => $row['reference'],
                    'description'    => $row['description'],
                    'total_debit'    => 0,
                    'total_credit'   => 0,
                    'lines'          => array()
                );
            }

            if ($row['account_id']) {
                $entries[$current_journal]['lines'][] = array(
                    'account_code'   => $row['account_code'],
                    'account_name'   => $row['account_name'],
                    'description'    => $row['line_description'],
                    'debit'         => $this->currency->format($row['debit'], $this->config->get('config_currency')),
                    'credit'        => $this->currency->format($row['credit'], $this->config->get('config_currency'))
                );

                $entries[$current_journal]['total_debit'] += $row['debit'];
                $entries[$current_journal]['total_credit'] += $row['credit'];
            }
        }

        // تنسيق المجاميع
        foreach ($entries as $journal_id => $entry) {
            $entries[$journal_id]['total_debit'] = $this->currency->format($entry['total_debit'], $this->config->get('config_currency'));
            $entries[$journal_id]['total_credit'] = $this->currency->format($entry['total_credit'], $this->config->get('config_currency'));
        }

        return $entries;
    }

    /**
     * توليد تقرير المعاملات بين الشركات
     */
    public function generateReport($data = array()) {
        $sql = "SELECT it.*,
                       c1.company_name as from_company_name,
                       c2.company_name as to_company_name,
                       a.account_code, a.account_name,
                       u.firstname, u.lastname
                FROM cod_intercompany_transactions it
                LEFT JOIN cod_companies c1 ON (it.from_company_id = c1.company_id)
                LEFT JOIN cod_companies c2 ON (it.to_company_id = c2.company_id)
                LEFT JOIN cod_accounts a ON (it.account_id = a.account_id)
                LEFT JOIN cod_user u ON (it.created_by = u.user_id)
                WHERE 1=1";

        $params = array();

        // فلترة حسب التاريخ
        if (!empty($data['filter_date_from'])) {
            $sql .= " AND DATE(it.transaction_date) >= '" . $this->db->escape($data['filter_date_from']) . "'";
        }

        if (!empty($data['filter_date_to'])) {
            $sql .= " AND DATE(it.transaction_date) <= '" . $this->db->escape($data['filter_date_to']) . "'";
        }

        // فلترة حسب الشركة
        if (!empty($data['filter_company_id'])) {
            $sql .= " AND (it.from_company_id = '" . (int)$data['filter_company_id'] . "'
                          OR it.to_company_id = '" . (int)$data['filter_company_id'] . "')";
        }

        // فلترة حسب نوع المعاملة
        if (!empty($data['filter_transaction_type'])) {
            $sql .= " AND it.transaction_type = '" . $this->db->escape($data['filter_transaction_type']) . "'";
        }

        // فلترة حسب الحالة
        if (!empty($data['filter_status'])) {
            $sql .= " AND it.status = '" . $this->db->escape($data['filter_status']) . "'";
        }

        // فلترة حسب المبلغ
        if (!empty($data['filter_amount_from'])) {
            $sql .= " AND it.amount >= '" . (float)$data['filter_amount_from'] . "'";
        }

        if (!empty($data['filter_amount_to'])) {
            $sql .= " AND it.amount <= '" . (float)$data['filter_amount_to'] . "'";
        }

        // ترتيب النتائج
        $sort_data = array(
            'it.transaction_date',
            'it.amount',
            'it.transaction_type',
            'it.status',
            'c1.company_name',
            'c2.company_name'
        );

        if (isset($data['sort']) && in_array($data['sort'], $sort_data)) {
            $sql .= " ORDER BY " . $data['sort'];
        } else {
            $sql .= " ORDER BY it.transaction_date";
        }

        if (isset($data['order']) && ($data['order'] == 'DESC')) {
            $sql .= " DESC";
        } else {
            $sql .= " ASC";
        }

        // تحديد عدد النتائج
        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }

        $query = $this->db->query($sql);

        $results = array();
        foreach ($query->rows as $row) {
            $results[] = array(
                'transaction_id'      => $row['transaction_id'],
                'reference'          => $row['reference'],
                'transaction_type'   => $row['transaction_type'],
                'from_company_name'  => $row['from_company_name'],
                'to_company_name'    => $row['to_company_name'],
                'account_code'       => $row['account_code'],
                'account_name'       => $row['account_name'],
                'amount'            => $this->currency->format($row['amount'], $row['currency']),
                'currency'          => $row['currency'],
                'status'            => $row['status'],
                'transaction_date'  => date($this->language->get('date_format_short'), strtotime($row['transaction_date'])),
                'created_by'        => $row['firstname'] . ' ' . $row['lastname'],
                'date_created'      => date($this->language->get('datetime_format'), strtotime($row['date_created']))
            );
        }

        return $results;
    }
}
