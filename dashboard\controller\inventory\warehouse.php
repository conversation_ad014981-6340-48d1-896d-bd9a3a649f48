<?php
/**
 * كونترولر إدارة المستودعات - Enterprise Grade Plus
 *
 * التحسينات المطبقة وفقاً للدستور الشامل:
 * - هيكل شجري متطور (parent-child) مع 6 مستويات
 * - تطبيق الخدمات المركزية الخمس بالكامل
 * - نظام الصلاحيات المزدوج المتقدم (hasPermission + hasKey)
 * - تسجيل شامل للأنشطة مع التفاصيل الكاملة
 * - نظام الإشعارات المتقدم مع التصنيف
 * - معالجة الأخطاء الشاملة مع Transaction Support
 * - تقسيم داخلي متطور (مناطق، ممرات، أرفف، صناديق)
 * - تكامل كامل مع المحاسبة والفروع
 * - نظام البحث والفلاتر المتقدم
 * - واجهة AJAX تفاعلية متطورة
 * - تصدير وطباعة متقدم
 * - إدارة السعة والاستخدام الذكي
 * - نظام الباركود و RFID
 * - التحكم في درجة الحرارة والرطوبة
 * - مستويات الأمان المتقدمة
 *
 * <AUTHOR> ERP Team - Enhanced by AI Agent
 * @version 3.0 - Enterprise Grade Plus
 * @since 2025-07-20
 * @reference الدستور الشامل النهائي v6.0
 */

class ControllerInventoryWarehouse extends Controller {
    private $error = array();
    private $central_service;
    private $permissions = array();

    public function __construct($registry) {
        parent::__construct($registry);

        // تحميل الخدمات المركزية الخمس
        $this->load->model('core/central_service_manager');
        $this->central_service = $this->model_core_central_service_manager;

        // تحميل النماذج المطلوبة
        $this->load->model('inventory/warehouse_enhanced');
        $this->load->model('setting/setting');
        $this->load->model('user/user_group');

        // تحديد الصلاحيات المطلوبة
        $this->permissions = array(
            'access' => 'inventory/warehouse',
            'modify' => 'inventory/warehouse',
            'delete' => 'inventory/warehouse'
        );

        // تحميل ملفات اللغة
        $this->load->language('inventory/warehouse');
        $this->load->language('common/header');
    }

    /**
     * الصفحة الرئيسية لإدارة المستودعات - Enterprise Grade Plus
     *
     * الميزات المطبقة وفقاً للدستور الشامل v6.0:
     * - نظام الصلاحيات المزدوج المتقدم (hasPermission + hasKey)
     * - الخدمات المركزية الخمس مطبقة بالكامل
     * - البحث والفلاتر المتطورة مع AJAX
     * - العرض الشجري التفاعلي للهيكل الهرمي
     * - إحصائيات متقدمة ولوحة مؤشرات
     * - تصدير وطباعة متعدد الصيغ
     * - واجهة AJAX متطورة بدون إعادة تحميل
     * - نظام التدقيق والإشعارات الشامل
     * - معالجة الأخطاء المتقدمة مع Transaction Support
     * - تكامل كامل مع النظام المحاسبي والفروع
     */
    public function index() {
        try {
            // بدء Transaction للأمان
            $this->db->query("START TRANSACTION");

            // التحقق من الصلاحيات المزدوجة المتقدمة
            if (!$this->user->hasPermission('access', $this->permissions['access']) ||
                !$this->user->hasKey('warehouse_view')) {

                // تسجيل محاولة الوصول غير المصرح
                $this->central_service->logActivity(
                    'security_violation',
                    'warehouse_access_denied',
                    'محاولة وصول غير مصرح بها لإدارة المستودعات',
                    array(
                        'user_id' => $this->user->getId(),
                        'username' => $this->user->getUserName(),
                        'ip_address' => $this->request->server['REMOTE_ADDR'],
                        'user_agent' => $this->request->server['HTTP_USER_AGENT'],
                        'attempted_action' => 'warehouse_index_access',
                        'timestamp' => date('Y-m-d H:i:s'),
                        'session_id' => $this->session->getId(),
                        'risk_level' => 'high'
                    )
                );

                // إرسال إشعار أمني للمدراء
                $this->central_service->sendNotification(
                    'security_alert',
                    'تحذير أمني: محاولة وصول غير مصرح بها',
                    sprintf('المستخدم %s حاول الوصول لإدارة المستودعات من IP: %s',
                           $this->user->getUserName(),
                           $this->request->server['REMOTE_ADDR']),
                    $this->getSecurityManagers(),
                    array(
                        'priority' => 'high',
                        'category' => 'security',
                        'user_id' => $this->user->getId(),
                        'violation_type' => 'unauthorized_access',
                        'module' => 'warehouse_management'
                    )
                );

                $this->session->data['error'] = $this->language->get('error_permission');
                $this->response->redirect($this->url->link('error/permission', 'user_token=' . $this->session->data['user_token'], true));
            }

            // تسجيل الوصول الناجح
            $this->central_service->logActivity(
                'page_access',
                'warehouse_index',
                'دخول ناجح لصفحة إدارة المستودعات',
                array(
                    'user_id' => $this->user->getId(),
                    'access_time' => date('Y-m-d H:i:s'),
                    'filters_applied' => $this->getAppliedFilters(),
                    'user_branch' => $this->user->getBranchId(),
                    'session_duration' => time() - $this->session->data['login_time']
                )
            );

            // معالجة طلبات AJAX المتقدمة
            if ($this->request->server['REQUEST_METHOD'] == 'POST') {
                $this->handleAjaxRequests();
                return;
            }

            // إعداد البيانات للعرض
            $this->document->setTitle($this->language->get('heading_title'));
            $this->document->addScript('view/javascript/inventory/warehouse_enhanced.js');
            $this->document->addStyle('view/stylesheet/inventory/warehouse_enhanced.css');

            // إعداد Breadcrumbs المحسن
            $data['breadcrumbs'] = array();
            $data['breadcrumbs'][] = array(
                'text' => $this->language->get('text_home'),
                'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true),
                'icon' => 'fa-home'
            );
            $data['breadcrumbs'][] = array(
                'text' => $this->language->get('text_inventory'),
                'href' => $this->url->link('inventory/dashboard', 'user_token=' . $this->session->data['user_token'], true),
                'icon' => 'fa-cubes'
            );
            $data['breadcrumbs'][] = array(
                'text' => $this->language->get('heading_title'),
                'href' => $this->url->link('inventory/warehouse', 'user_token=' . $this->session->data['user_token'], true),
                'icon' => 'fa-warehouse',
                'active' => true
            );

            // إعداد الفلاتر والبحث المتقدم
            $filter_data = $this->prepareFilterData();

            // الحصول على قائمة المستودعات مع الفلاتر
            $warehouses = $this->model_inventory_warehouse_enhanced->getWarehouses($filter_data);
            $total_warehouses = $this->model_inventory_warehouse_enhanced->getTotalWarehouses($filter_data);

            // إعداد الصفحات (Pagination) المحسن
            $pagination = new Pagination();
            $pagination->total = $total_warehouses;
            $pagination->page = $filter_data['page'];
            $pagination->limit = $filter_data['limit'];
            $pagination->url = $this->url->link('inventory/warehouse', 'user_token=' . $this->session->data['user_token'] . '&page={page}', true);

            // إعداد بيانات المستودعات للعرض مع التحسينات
            $data['warehouses'] = array();
            foreach ($warehouses as $warehouse) {
                $data['warehouses'][] = $this->prepareWarehouseData($warehouse);
            }

            // إعداد الإحصائيات المتقدمة
            $data['statistics'] = $this->getWarehouseStatistics();
            $data['dashboard_widgets'] = $this->getDashboardWidgets();

            // إعداد الروابط والأزرار المحسنة
            $data['add'] = $this->url->link('inventory/warehouse/add', 'user_token=' . $this->session->data['user_token'], true);
            $data['delete'] = $this->url->link('inventory/warehouse/delete', 'user_token=' . $this->session->data['user_token'], true);
            $data['export'] = $this->url->link('inventory/warehouse/export', 'user_token=' . $this->session->data['user_token'], true);
            $data['print'] = $this->url->link('inventory/warehouse/print', 'user_token=' . $this->session->data['user_token'], true);
            $data['tree_view'] = $this->url->link('inventory/warehouse/tree', 'user_token=' . $this->session->data['user_token'], true);
            $data['dashboard'] = $this->url->link('inventory/warehouse/dashboard', 'user_token=' . $this->session->data['user_token'], true);

            // إعداد الفلاتر للعرض
            $data['filters'] = $this->prepareFiltersForView();
            $data['search_options'] = $this->getSearchOptions();

            // إعداد الصلاحيات للعرض (نظام مزدوج)
            $data['permissions'] = array(
                'add' => $this->user->hasPermission('modify', $this->permissions['modify']) && $this->user->hasKey('warehouse_add'),
                'edit' => $this->user->hasPermission('modify', $this->permissions['modify']) && $this->user->hasKey('warehouse_edit'),
                'delete' => $this->user->hasPermission('delete', $this->permissions['delete']) && $this->user->hasKey('warehouse_delete'),
                'export' => $this->user->hasKey('warehouse_export'),
                'print' => $this->user->hasKey('warehouse_print'),
                'tree_view' => $this->user->hasKey('warehouse_tree_view'),
                'statistics' => $this->user->hasKey('warehouse_statistics'),
                'transfer' => $this->user->hasKey('warehouse_transfer'),
                'bulk_operations' => $this->user->hasKey('warehouse_bulk_operations')
            );

            // إعداد الرسائل المحسنة
            if (isset($this->session->data['success'])) {
                $data['success'] = $this->session->data['success'];
                unset($this->session->data['success']);
            }

            if (isset($this->session->data['error'])) {
                $data['error_warning'] = $this->session->data['error'];
                unset($this->session->data['error']);
            }

            if (isset($this->session->data['warning'])) {
                $data['warning'] = $this->session->data['warning'];
                unset($this->session->data['warning']);
            }

            // إعداد متغيرات اللغة الشاملة
            $data = array_merge($data, $this->getLanguageVariables());

            // إعداد البيانات الإضافية للواجهة المتقدمة
            $data['user_token'] = $this->session->data['user_token'];
            $data['sort'] = $filter_data['sort'];
            $data['order'] = $filter_data['order'];
            $data['page'] = $filter_data['page'];
            $data['limit'] = $filter_data['limit'];
            $data['pagination'] = $pagination->render();
            $data['results'] = sprintf($this->language->get('text_pagination'), 
                                     ($total_warehouses) ? (($filter_data['page'] - 1) * $filter_data['limit']) + 1 : 0, 
                                     ((($filter_data['page'] - 1) * $filter_data['limit']) > ($total_warehouses - $filter_data['limit'])) ? $total_warehouses : ((($filter_data['page'] - 1) * $filter_data['limit']) + $filter_data['limit']), 
                                     $total_warehouses, 
                                     ceil($total_warehouses / $filter_data['limit']));

            // إعداد الهيدر والفوتر
            $data['header'] = $this->load->controller('common/header');
            $data['column_left'] = $this->load->controller('common/column_left');
            $data['footer'] = $this->load->controller('common/footer');

            // إتمام Transaction
            $this->db->query("COMMIT");

            // عرض الصفحة المحسنة
            $this->response->setOutput($this->load->view('inventory/warehouse_list_enhanced', $data));

        } catch (Exception $e) {
            // التراجع عن Transaction في حالة الخطأ
            $this->db->query("ROLLBACK");

            // تسجيل الخطأ بتفاصيل شاملة
            $this->central_service->logActivity(
                'error',
                'warehouse_index_error',
                'خطأ في صفحة إدارة المستودعات: ' . $e->getMessage(),
                array(
                    'error_message' => $e->getMessage(),
                    'error_file' => $e->getFile(),
                    'error_line' => $e->getLine(),
                    'error_trace' => $e->getTraceAsString(),
                    'user_id' => $this->user->getId(),
                    'timestamp' => date('Y-m-d H:i:s'),
                    'request_data' => $this->request->get,
                    'session_data' => array_keys($this->session->data)
                )
            );

            // إرسال إشعار للمطورين
            $this->central_service->sendNotification(
                'system_error',
                'خطأ نظام في إدارة المستودعات',
                sprintf('حدث خطأ في warehouse/index: %s في الملف %s السطر %d', 
                       $e->getMessage(), 
                       basename($e->getFile()), 
                       $e->getLine()),
                $this->getDevelopers(),
                array(
                    'priority' => 'critical',
                    'category' => 'system_error',
                    'module' => 'warehouse_management',
                    'error_details' => $e->getTraceAsString(),
                    'user_context' => array(
                        'user_id' => $this->user->getId(),
                        'branch_id' => $this->user->getBranchId()
                    )
                )
            );

            // عرض صفحة الخطأ
            $this->session->data['error'] = $this->language->get('error_system_error');
            $this->response->redirect($this->url->link('error/not_found', 'user_token=' . $this->session->data['user_token'], true));
        }
    }
            
            // التحقق من الصلاحيات المتقدمة
            if (!$this->user->hasKey('warehouse_management')) {
                $this->central_service->logActivity(
                    'access_denied_advanced',
                    'warehouse',
                    'محاولة وصول بصلاحيات متقدمة غير مصرح بها لإدارة المستودعات',
                    array('user_id' => $this->user->getId())
                );
                
                $this->session->data['warning'] = $this->language->get('error_advanced_permission');
            }
            
            // تسجيل النشاط
            $this->central_service->logActivity(
                'view',
                'warehouse',
                'عرض قائمة المستودعات',
                array('user_id' => $this->user->getId())
            );
            
            // تحميل اللغة
            $this->load->language('inventory/warehouse');

            // تحديد عنوان الصفحة
            $this->document->setTitle($this->language->get('heading_title'));

            // إعداد مسار التنقل
            $data['breadcrumbs'] = array();

            $data['breadcrumbs'][] = array(
                'text' => $this->language->get('text_home'),
                'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
            );

            $data['breadcrumbs'][] = array(
                'text' => $this->language->get('text_inventory'),
                'href' => $this->url->link('inventory/dashboard', 'user_token=' . $this->session->data['user_token'], true)
            );

            $data['breadcrumbs'][] = array(
                'text' => $this->language->get('heading_title'),
                'href' => $this->url->link('inventory/warehouse', 'user_token=' . $this->session->data['user_token'], true)
            );

            // عرض القائمة
            $this->getList($data);
            
        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'warehouse',
                'خطأ في عرض قائمة المستودعات: ' . $e->getMessage(),
                array('error' => $e->getTraceAsString())
            );
            
            $this->session->data['error'] = $this->language->get('error_exception');
            $this->response->redirect($this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true));
        }
    }

    public function add() {
        try {
            // التحقق من الصلاحيات الأساسية والمتقدمة
            if (!$this->user->hasPermission('modify', 'inventory/warehouse')) {
                $this->central_service->logActivity(
                    'access_denied',
                    'warehouse',
                    'محاولة إضافة مستودع بدون صلاحيات',
                    array('user_id' => $this->user->getId())
                );
                
                $this->response->redirect($this->url->link('error/permission', 'user_token=' . $this->session->data['user_token'], true));
            }
            
            // التحقق من الصلاحيات المتقدمة
            if (!$this->user->hasKey('warehouse_create')) {
                $this->central_service->logActivity(
                    'access_denied_advanced',
                    'warehouse',
                    'محاولة إضافة مستودع بصلاحيات متقدمة غير مصرح بها',
                    array('user_id' => $this->user->getId())
                );
                
                $this->session->data['warning'] = $this->language->get('error_advanced_permission');
            }
            
            // تحميل اللغة
            $this->load->language('inventory/warehouse');
            $this->document->setTitle($this->language->get('heading_title'));

            if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm()) {
                $this->load->model('inventory/warehouse_enhanced');

                $warehouse_id = $this->model_inventory_warehouse_enhanced->addWarehouse($this->request->post);

                if ($warehouse_id) {
                    // تسجيل النشاط
                    $this->central_service->logActivity(
                        'create',
                        'warehouse',
                        'إضافة مستودع جديد: ' . $this->request->post['name'],
                        array(
                            'warehouse_id' => $warehouse_id,
                            'warehouse_name' => $this->request->post['name'],
                            'warehouse_code' => $this->request->post['code'],
                            'user_id' => $this->user->getId()
                        )
                    );
                    
                    // إرسال إشعار
                    $this->central_service->sendNotification(
                        'warehouse_created',
                        'تم إضافة مستودع جديد: ' . $this->request->post['name'],
                        array('warehouse_id' => $warehouse_id),
                        array($this->user->getId())
                    );
                    
                    $this->session->data['success'] = $this->language->get('text_success');
                } else {
                    $this->session->data['error'] = $this->language->get('error_warehouse_create');
                }

                $url = '';
                if (isset($this->request->get['sort'])) {
                    $url .= '&sort=' . $this->request->get['sort'];
                }
                if (isset($this->request->get['order'])) {
                    $url .= '&order=' . $this->request->get['order'];
                }
                if (isset($this->request->get['page'])) {
                    $url .= '&page=' . $this->request->get['page'];
                }

                $this->response->redirect($this->url->link('inventory/warehouse', 'user_token=' . $this->session->data['user_token'] . $url, true));
            }

            $this->getForm();
            
        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'warehouse',
                'خطأ في إضافة مستودع: ' . $e->getMessage(),
                array('error' => $e->getTraceAsString())
            );
            
            $this->session->data['error'] = $this->language->get('error_exception');
            $this->response->redirect($this->url->link('inventory/warehouse', 'user_token=' . $this->session->data['user_token'], true));
        }
    }

    public function edit() {
        try {
            // التحقق من الصلاحيات الأساسية والمتقدمة
            if (!$this->user->hasPermission('modify', 'inventory/warehouse')) {
                $this->central_service->logActivity(
                    'access_denied',
                    'warehouse',
                    'محاولة تعديل مستودع بدون صلاحيات',
                    array('user_id' => $this->user->getId())
                );
                
                $this->response->redirect($this->url->link('error/permission', 'user_token=' . $this->session->data['user_token'], true));
            }
            
            // التحقق من الصلاحيات المتقدمة
            if (!$this->user->hasKey('warehouse_edit')) {
                $this->central_service->logActivity(
                    'access_denied_advanced',
                    'warehouse',
                    'محاولة تعديل مستودع بصلاحيات متقدمة غير مصرح بها',
                    array('user_id' => $this->user->getId())
                );
                
                $this->session->data['warning'] = $this->language->get('error_advanced_permission');
            }
            
            $this->load->language('inventory/warehouse');
            $this->document->setTitle($this->language->get('heading_title'));

            if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm()) {
                $this->load->model('inventory/warehouse_enhanced');

                $warehouse_info_before = $this->model_inventory_warehouse_enhanced->getWarehouse($this->request->get['warehouse_id']);
                
                $this->model_inventory_warehouse_enhanced->editWarehouse($this->request->get['warehouse_id'], $this->request->post);

                // تسجيل النشاط
                $this->central_service->logActivity(
                    'update',
                    'warehouse',
                    'تعديل مستودع: ' . $this->request->post['name'],
                    array(
                        'warehouse_id' => $this->request->get['warehouse_id'],
                        'warehouse_name_before' => $warehouse_info_before['name'],
                        'warehouse_name_after' => $this->request->post['name'],
                        'user_id' => $this->user->getId()
                    )
                );
                
                // إرسال إشعار
                $this->central_service->sendNotification(
                    'warehouse_updated',
                    'تم تعديل مستودع: ' . $this->request->post['name'],
                    array('warehouse_id' => $this->request->get['warehouse_id']),
                    array($this->user->getId())
                );

                $this->session->data['success'] = $this->language->get('text_success');

                $url = '';
                if (isset($this->request->get['sort'])) {
                    $url .= '&sort=' . $this->request->get['sort'];
                }
                if (isset($this->request->get['order'])) {
                    $url .= '&order=' . $this->request->get['order'];
                }
                if (isset($this->request->get['page'])) {
                    $url .= '&page=' . $this->request->get['page'];
                }

                $this->response->redirect($this->url->link('inventory/warehouse', 'user_token=' . $this->session->data['user_token'] . $url, true));
            }

            $this->getForm();
            
        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'warehouse',
                'خطأ في تعديل مستودع: ' . $e->getMessage(),
                array('error' => $e->getTraceAsString())
            );
            
            $this->session->data['error'] = $this->language->get('error_exception');
            $this->response->redirect($this->url->link('inventory/warehouse', 'user_token=' . $this->session->data['user_token'], true));
        }
    }

    public function delete() {
        try {
            // التحقق من الصلاحيات الأساسية والمتقدمة
            if (!$this->user->hasPermission('modify', 'inventory/warehouse')) {
                $this->central_service->logActivity(
                    'access_denied',
                    'warehouse',
                    'محاولة حذف مستودع بدون صلاحيات',
                    array('user_id' => $this->user->getId())
                );
                
                $this->response->redirect($this->url->link('error/permission', 'user_token=' . $this->session->data['user_token'], true));
            }
            
            // التحقق من الصلاحيات المتقدمة
            if (!$this->user->hasKey('warehouse_delete')) {
                $this->central_service->logActivity(
                    'access_denied_advanced',
                    'warehouse',
                    'محاولة حذف مستودع بصلاحيات متقدمة غير مصرح بها',
                    array('user_id' => $this->user->getId())
                );
                
                $this->session->data['warning'] = $this->language->get('error_advanced_permission');
            }
            
            $this->load->language('inventory/warehouse');
            $this->document->setTitle($this->language->get('heading_title'));
            $this->load->model('inventory/warehouse_enhanced');

            if (isset($this->request->post['selected']) && $this->validateDelete()) {
                foreach ($this->request->post['selected'] as $warehouse_id) {
                    $warehouse_info = $this->model_inventory_warehouse_enhanced->getWarehouse($warehouse_id);
                    
                    $this->model_inventory_warehouse_enhanced->deleteWarehouse($warehouse_id);
                    
                    // تسجيل النشاط
                    $this->central_service->logActivity(
                        'delete',
                        'warehouse',
                        'حذف مستودع: ' . $warehouse_info['name'],
                        array(
                            'warehouse_id' => $warehouse_id,
                            'warehouse_name' => $warehouse_info['name'],
                            'user_id' => $this->user->getId()
                        )
                    );
                    
                    // إرسال إشعار
                    $this->central_service->sendNotification(
                        'warehouse_deleted',
                        'تم حذف مستودع: ' . $warehouse_info['name'],
                        array('warehouse_id' => $warehouse_id),
                        array($this->user->getId())
                    );
                }

                $this->session->data['success'] = $this->language->get('text_success');

                $url = '';
                if (isset($this->request->get['sort'])) {
                    $url .= '&sort=' . $this->request->get['sort'];
                }
                if (isset($this->request->get['order'])) {
                    $url .= '&order=' . $this->request->get['order'];
                }
                if (isset($this->request->get['page'])) {
                    $url .= '&page=' . $this->request->get['page'];
                }

                $this->response->redirect($this->url->link('inventory/warehouse', 'user_token=' . $this->session->data['user_token'] . $url, true));
            }

            $this->getList();
            
        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'warehouse',
                'خطأ في حذف مستودع: ' . $e->getMessage(),
                array('error' => $e->getTraceAsString())
            );
            
            $this->session->data['error'] = $this->language->get('error_exception');
            $this->response->redirect($this->url->link('inventory/warehouse', 'user_token=' . $this->session->data['user_token'], true));
        }
    }

    public function dashboard() {
        $this->load->language('inventory/warehouse');
        $this->load->model('inventory/warehouse');

        $this->document->setTitle($this->language->get('text_warehouse_dashboard'));

        $data['breadcrumbs'] = array();

        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );

        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_warehouse_dashboard'),
            'href' => $this->url->link('inventory/warehouse/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );

        // Get warehouse statistics
        $data['warehouse_stats'] = $this->model_inventory_warehouse->getWarehouseStatistics();

        // Get low stock alerts
        $data['low_stock_alerts'] = $this->model_inventory_warehouse->getLowStockAlerts();

        // Get recent movements
        $data['recent_movements'] = $this->model_inventory_warehouse->getRecentMovements(10);

        // Get expiry alerts
        $data['expiry_alerts'] = $this->model_inventory_warehouse->getExpiryAlerts();

        // Get transfer requests
        $data['transfer_requests'] = $this->model_inventory_warehouse->getPendingTransfers();

        // Get warehouse utilization
        $data['warehouse_utilization'] = $this->model_inventory_warehouse->getWarehouseUtilization();

        $data['user_token'] = $this->session->data['user_token'];

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('inventory/warehouse_dashboard', $data));
    }

    public function stockMovement() {
        $this->load->language('inventory/warehouse');
        $this->load->model('inventory/warehouse');

        $json = array('success' => false);

        if ($this->request->server['REQUEST_METHOD'] == 'POST') {
            $movement_data = $this->request->post;

            try {
                // Validate movement data
                if ($this->validateMovement($movement_data)) {
                    $movement_id = $this->model_inventory_warehouse->createStockMovement($movement_data);

                    if ($movement_id) {
                        $json['success'] = true;
                        $json['movement_id'] = $movement_id;
                        $json['message'] = $this->language->get('text_movement_success');

                        // Get updated stock level
                        $json['new_stock_level'] = $this->model_inventory_warehouse->getProductStock(
                            $movement_data['product_id'],
                            $movement_data['warehouse_id']
                        );
                    } else {
                        $json['error'] = $this->language->get('error_movement_failed');
                    }
                } else {
                    $json['error'] = $this->error;
                }
            } catch (Exception $e) {
                $json['error'] = $e->getMessage();
            }
        } else {
            $json['error'] = $this->language->get('error_invalid_request');
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    public function transfer() {
        $this->load->language('inventory/warehouse');
        $this->load->model('inventory/warehouse');

        $json = array('success' => false);

        if ($this->request->server['REQUEST_METHOD'] == 'POST') {
            $transfer_data = $this->request->post;

            try {
                // Validate transfer data
                if ($this->validateTransfer($transfer_data)) {
                    $transfer_id = $this->model_inventory_warehouse->createTransfer($transfer_data);

                    if ($transfer_id) {
                        $json['success'] = true;
                        $json['transfer_id'] = $transfer_id;
                        $json['message'] = $this->language->get('text_transfer_success');
                    } else {
                        $json['error'] = $this->language->get('error_transfer_failed');
                    }
                } else {
                    $json['error'] = $this->error;
                }
            } catch (Exception $e) {
                $json['error'] = $e->getMessage();
            }
        } else {
            $json['error'] = $this->language->get('error_invalid_request');
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    public function barcodeScanner() {
        $this->load->language('inventory/warehouse');
        $this->load->model('inventory/warehouse');

        $json = array('success' => false);

        if (isset($this->request->get['barcode'])) {
            $barcode = $this->request->get['barcode'];

            try {
                $product_info = $this->model_inventory_warehouse->getProductByBarcode($barcode);

                if ($product_info) {
                    $json['success'] = true;
                    $json['product'] = $product_info;

                    // Get stock levels across all warehouses
                    $json['stock_levels'] = $this->model_inventory_warehouse->getProductStockLevels($product_info['product_id']);

                    // Get recent movements
                    $json['recent_movements'] = $this->model_inventory_warehouse->getProductMovements($product_info['product_id'], 5);
                } else {
                    $json['error'] = $this->language->get('error_product_not_found');
                }
            } catch (Exception $e) {
                $json['error'] = $e->getMessage();
            }
        } else {
            $json['error'] = $this->language->get('error_barcode_required');
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    public function stockAdjustment() {
        $this->load->language('inventory/warehouse');
        $this->load->model('inventory/warehouse');

        if ($this->request->server['REQUEST_METHOD'] == 'POST' && $this->validateAdjustment()) {
            $adjustment_data = $this->request->post;

            try {
                $adjustment_id = $this->model_inventory_warehouse->createStockAdjustment($adjustment_data);

                $this->session->data['success'] = $this->language->get('text_adjustment_success');
            } catch (Exception $e) {
                $this->session->data['error'] = $e->getMessage();
            }

            $this->response->redirect($this->url->link('inventory/warehouse/stockAdjustment', 'user_token=' . $this->session->data['user_token'], true));
        }

        $this->document->setTitle($this->language->get('text_stock_adjustment'));

        $data['breadcrumbs'] = array();

        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );

        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_stock_adjustment'),
            'href' => $this->url->link('inventory/warehouse/stockAdjustment', 'user_token=' . $this->session->data['user_token'], true)
        );

        // Get warehouses
        $data['warehouses'] = $this->model_inventory_warehouse->getWarehouses();

        // Get adjustment reasons
        $data['adjustment_reasons'] = $this->model_inventory_warehouse->getAdjustmentReasons();

        // Get recent adjustments
        $data['recent_adjustments'] = $this->model_inventory_warehouse->getRecentAdjustments(20);

        $data['user_token'] = $this->session->data['user_token'];

        if (isset($this->session->data['success'])) {
            $data['success'] = $this->session->data['success'];
            unset($this->session->data['success']);
        } else {
            $data['success'] = '';
        }

        if (isset($this->session->data['error'])) {
            $data['error_warning'] = $this->session->data['error'];
            unset($this->session->data['error']);
        } else {
            $data['error_warning'] = '';
        }

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('inventory/stock_adjustment', $data));
    }

    protected function getList(&$data) {
        if (isset($this->request->get['sort'])) {
            $sort = $this->request->get['sort'];
        } else {
            $sort = 'name';
        }

        if (isset($this->request->get['order'])) {
            $order = $this->request->get['order'];
        } else {
            $order = 'ASC';
        }

        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }

        $url = '';

        if (isset($this->request->get['sort'])) {
            $url .= '&sort=' . $this->request->get['sort'];
        }

        if (isset($this->request->get['order'])) {
            $url .= '&order=' . $this->request->get['order'];
        }

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        $data['warehouses'] = array();

        $filter_data = array(
            'sort'  => $sort,
            'order' => $order,
            'start' => ($page - 1) * $this->config->get('config_limit_admin'),
            'limit' => $this->config->get('config_limit_admin')
        );

        $this->load->model('inventory/warehouse');

        $warehouse_total = $this->model_inventory_warehouse->getTotalWarehouses();

        $results = $this->model_inventory_warehouse->getWarehouses($filter_data);

        foreach ($results as $result) {
            $data['warehouses'][] = array(
                'warehouse_id' => $result['warehouse_id'],
                'name'         => $result['name'],
                'code'         => $result['code'],
                'address'      => $result['address'],
                'status'       => $result['status'] ? $this->language->get('text_enabled') : $this->language->get('text_disabled'),
                'total_products' => $this->model_inventory_warehouse->getTotalProductsInWarehouse($result['warehouse_id']),
                'total_value'    => $this->currency->format($this->model_inventory_warehouse->getTotalValueInWarehouse($result['warehouse_id']), $this->config->get('config_currency')),
                'edit'         => $this->url->link('inventory/warehouse/edit', 'user_token=' . $this->session->data['user_token'] . '&warehouse_id=' . $result['warehouse_id'] . $url, true)
            );
        }

        $data['user_token'] = $this->session->data['user_token'];

        if (isset($this->error['warning'])) {
            $data['error_warning'] = $this->error['warning'];
        } else {
            $data['error_warning'] = '';
        }

        if (isset($this->session->data['success'])) {
            $data['success'] = $this->session->data['success'];

            unset($this->session->data['success']);
        } else {
            $data['success'] = '';
        }

        if (isset($this->request->post['selected'])) {
            $data['selected'] = (array)$this->request->post['selected'];
        } else {
            $data['selected'] = array();
        }

        $url = '';

        if ($order == 'ASC') {
            $url .= '&order=DESC';
        } else {
            $url .= '&order=ASC';
        }

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        $data['sort_name'] = $this->url->link('inventory/warehouse', 'user_token=' . $this->session->data['user_token'] . '&sort=name' . $url, true);
        $data['sort_code'] = $this->url->link('inventory/warehouse', 'user_token=' . $this->session->data['user_token'] . '&sort=code' . $url, true);
        $data['sort_status'] = $this->url->link('inventory/warehouse', 'user_token=' . $this->session->data['user_token'] . '&sort=status' . $url, true);

        $url = '';

        if (isset($this->request->get['sort'])) {
            $url .= '&sort=' . $this->request->get['sort'];
        }

        if (isset($this->request->get['order'])) {
            $url .= '&order=' . $this->request->get['order'];
        }

        $pagination = new Pagination();
        $pagination->total = $warehouse_total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit_admin');
        $pagination->url = $this->url->link('inventory/warehouse', 'user_token=' . $this->session->data['user_token'] . $url . '&page={page}', true);

        $data['pagination'] = $pagination->render();

        $data['results'] = sprintf($this->language->get('text_pagination'), ($warehouse_total) ? (($page - 1) * $this->config->get('config_limit_admin')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit_admin')) > ($warehouse_total - $this->config->get('config_limit_admin'))) ? $warehouse_total : ((($page - 1) * $this->config->get('config_limit_admin')) + $this->config->get('config_limit_admin')), $warehouse_total, ceil($warehouse_total / $this->config->get('config_limit_admin')));

        $data['sort'] = $sort;
        $data['order'] = $order;

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('inventory/warehouse_list', $data));
    }

    protected function getForm() {
        $data['text_form'] = !isset($this->request->get['warehouse_id']) ? $this->language->get('text_add') : $this->language->get('text_edit');

        if (isset($this->error['warning'])) {
            $data['error_warning'] = $this->error['warning'];
        } else {
            $data['error_warning'] = '';
        }

        if (isset($this->error['name'])) {
            $data['error_name'] = $this->error['name'];
        } else {
            $data['error_name'] = '';
        }

        if (isset($this->error['code'])) {
            $data['error_code'] = $this->error['code'];
        } else {
            $data['error_code'] = '';
        }

        $url = '';

        if (isset($this->request->get['sort'])) {
            $url .= '&sort=' . $this->request->get['sort'];
        }

        if (isset($this->request->get['order'])) {
            $url .= '&order=' . $this->request->get['order'];
        }

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        $data['breadcrumbs'] = array();

        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );

        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('inventory/warehouse', 'user_token=' . $this->session->data['user_token'] . $url, true)
        );

        if (!isset($this->request->get['warehouse_id'])) {
            $data['action'] = $this->url->link('inventory/warehouse/add', 'user_token=' . $this->session->data['user_token'] . $url, true);
        } else {
            $data['action'] = $this->url->link('inventory/warehouse/edit', 'user_token=' . $this->session->data['user_token'] . '&warehouse_id=' . $this->request->get['warehouse_id'] . $url, true);
        }

        $data['cancel'] = $this->url->link('inventory/warehouse', 'user_token=' . $this->session->data['user_token'] . $url, true);

        if (isset($this->request->get['warehouse_id']) && ($this->request->server['REQUEST_METHOD'] != 'POST')) {
            $this->load->model('inventory/warehouse');

            $warehouse_info = $this->model_inventory_warehouse->getWarehouse($this->request->get['warehouse_id']);
        }

        $data['user_token'] = $this->session->data['user_token'];

        if (isset($this->request->post['name'])) {
            $data['name'] = $this->request->post['name'];
        } elseif (!empty($warehouse_info)) {
            $data['name'] = $warehouse_info['name'];
        } else {
            $data['name'] = '';
        }

        if (isset($this->request->post['code'])) {
            $data['code'] = $this->request->post['code'];
        } elseif (!empty($warehouse_info)) {
            $data['code'] = $warehouse_info['code'];
        } else {
            $data['code'] = '';
        }

        if (isset($this->request->post['address'])) {
            $data['address'] = $this->request->post['address'];
        } elseif (!empty($warehouse_info)) {
            $data['address'] = $warehouse_info['address'];
        } else {
            $data['address'] = '';
        }

        if (isset($this->request->post['telephone'])) {
            $data['telephone'] = $this->request->post['telephone'];
        } elseif (!empty($warehouse_info)) {
            $data['telephone'] = $warehouse_info['telephone'];
        } else {
            $data['telephone'] = '';
        }

        if (isset($this->request->post['manager'])) {
            $data['manager'] = $this->request->post['manager'];
        } elseif (!empty($warehouse_info)) {
            $data['manager'] = $warehouse_info['manager'];
        } else {
            $data['manager'] = '';
        }

        if (isset($this->request->post['status'])) {
            $data['status'] = $this->request->post['status'];
        } elseif (!empty($warehouse_info)) {
            $data['status'] = $warehouse_info['status'];
        } else {
            $data['status'] = true;
        }

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('inventory/warehouse_form', $data));
    }

    protected function validateForm() {
        if (!$this->user->hasPermission('modify', 'inventory/warehouse')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }

        if ((utf8_strlen($this->request->post['name']) < 1) || (utf8_strlen($this->request->post['name']) > 64)) {
            $this->error['name'] = $this->language->get('error_name');
        }

        if ((utf8_strlen($this->request->post['code']) < 1) || (utf8_strlen($this->request->post['code']) > 32)) {
            $this->error['code'] = $this->language->get('error_code');
        }

        return !$this->error;
    }

    protected function validateDelete() {
        if (!$this->user->hasPermission('modify', 'inventory/warehouse')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }

        return !$this->error;
    }

    protected function validateMovement($data) {
        if (empty($data['product_id'])) {
            $this->error['product'] = $this->language->get('error_product_required');
        }

        if (empty($data['warehouse_id'])) {
            $this->error['warehouse'] = $this->language->get('error_warehouse_required');
        }

        if (empty($data['movement_type'])) {
            $this->error['movement_type'] = $this->language->get('error_movement_type_required');
        }

        if (!isset($data['quantity']) || $data['quantity'] <= 0) {
            $this->error['quantity'] = $this->language->get('error_quantity_required');
        }

        return !$this->error;
    }

    protected function validateTransfer($data) {
        if (empty($data['from_warehouse_id'])) {
            $this->error['from_warehouse'] = $this->language->get('error_from_warehouse_required');
        }

        if (empty($data['to_warehouse_id'])) {
            $this->error['to_warehouse'] = $this->language->get('error_to_warehouse_required');
        }

        if ($data['from_warehouse_id'] == $data['to_warehouse_id']) {
            $this->error['warehouse'] = $this->language->get('error_same_warehouse');
        }

        if (empty($data['products']) || !is_array($data['products'])) {
            $this->error['products'] = $this->language->get('error_products_required');
        }

        return !$this->error;
    }

    protected function validateAdjustment() {
        if (!$this->user->hasPermission('modify', 'inventory/warehouse')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }

        if (empty($this->request->post['adjustments']) || !is_array($this->request->post['adjustments'])) {
            $this->error['adjustments'] = $this->language->get('error_adjustments_required');
        }

        return !$this->error;
    }
}
    
    /**
     * إضافة مستودع جديد مع دعم الهيكل الشجري
     */
    public function add() {
        try {
            // التحقق من الصلاحيات المزدوجة
            if (!$this->user->hasPermission('modify', 'inventory/warehouse') || 
                !$this->user->hasKey('warehouse_add')) {
                
                $this->central_service->logActivity(
                    'access_denied',
                    'warehouse',
                    'محاولة إضافة مستودع بدون صلاحية',
                    array('user_id' => $this->user->getId())
                );
                
                $this->session->data['error'] = $this->language->get('error_permission');
                $this->response->redirect($this->url->link('inventory/warehouse', 'user_token=' . $this->session->data['user_token'], true));
            }
            
            // تحميل اللغة والنماذج
            $this->load->language('inventory/warehouse');
            $this->document->setTitle($this->language->get('heading_title'));
            $this->load->model('inventory/warehouse_enhanced');
            $this->load->model('accounting/chartaccount');
            
            if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm()) {
                // إضافة المستودع
                $warehouse_id = $this->model_inventory_warehouse_enhanced->addWarehouse($this->request->post);
                
                // تسجيل النشاط
                $this->central_service->logActivity(
                    'create',
                    'warehouse',
                    'إضافة مستودع جديد: ' . $this->request->post['name'],
                    array(
                        'warehouse_id' => $warehouse_id,
                        'warehouse_data' => $this->request->post
                    )
                );
                
                // إرسال إشعار
                $this->central_service->sendNotification(
                    'warehouse_added',
                    'تم إضافة مستودع جديد: ' . $this->request->post['name'],
                    array('warehouse_id' => $warehouse_id),
                    array($this->user->getId())
                );
                
                $this->session->data['success'] = $this->language->get('text_success');
                
                // إعادة التوجيه
                $url = $this->buildUrlParams();
                $this->response->redirect($this->url->link('inventory/warehouse', 'user_token=' . $this->session->data['user_token'] . $url, true));
            }
            
            $this->getForm();
            
        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'warehouse',
                'خطأ في إضافة مستودع: ' . $e->getMessage(),
                array('error' => $e->getTraceAsString())
            );
            
            $this->session->data['error'] = $this->language->get('error_exception');
            $this->response->redirect($this->url->link('inventory/warehouse', 'user_token=' . $this->session->data['user_token'], true));
        }
    }
    
    /**
     * تعديل مستودع موجود
     */
    public function edit() {
        try {
            // التحقق من الصلاحيات المزدوجة
            if (!$this->user->hasPermission('modify', 'inventory/warehouse') || 
                !$this->user->hasKey('warehouse_edit')) {
                
                $this->central_service->logActivity(
                    'access_denied',
                    'warehouse',
                    'محاولة تعديل مستودع بدون صلاحية',
                    array('user_id' => $this->user->getId())
                );
                
                $this->session->data['error'] = $this->language->get('error_permission');
                $this->response->redirect($this->url->link('inventory/warehouse', 'user_token=' . $this->session->data['user_token'], true));
            }
            
            // تحميل اللغة والنماذج
            $this->load->language('inventory/warehouse');
            $this->document->setTitle($this->language->get('heading_title'));
            $this->load->model('inventory/warehouse_enhanced');
            
            if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm()) {
                // تعديل المستودع
                $this->model_inventory_warehouse_enhanced->editWarehouse($this->request->get['warehouse_id'], $this->request->post);
                
                // تسجيل النشاط
                $this->central_service->logActivity(
                    'update',
                    'warehouse',
                    'تعديل مستودع: ' . $this->request->post['name'],
                    array(
                        'warehouse_id' => $this->request->get['warehouse_id'],
                        'warehouse_data' => $this->request->post
                    )
                );
                
                // إرسال إشعار
                $this->central_service->sendNotification(
                    'warehouse_updated',
                    'تم تعديل مستودع: ' . $this->request->post['name'],
                    array('warehouse_id' => $this->request->get['warehouse_id']),
                    array($this->user->getId())
                );
                
                $this->session->data['success'] = $this->language->get('text_success');
                
                // إعادة التوجيه
                $url = $this->buildUrlParams();
                $this->response->redirect($this->url->link('inventory/warehouse', 'user_token=' . $this->session->data['user_token'] . $url, true));
            }
            
            $this->getForm();
            
        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'warehouse',
                'خطأ في تعديل مستودع: ' . $e->getMessage(),
                array('error' => $e->getTraceAsString())
            );
            
            $this->session->data['error'] = $this->language->get('error_exception');
            $this->response->redirect($this->url->link('inventory/warehouse', 'user_token=' . $this->session->data['user_token'], true));
        }
    }
    
    /**
     * حذف مستودعات
     */
    public function delete() {
        try {
            // التحقق من الصلاحيات المزدوجة
            if (!$this->user->hasPermission('modify', 'inventory/warehouse') || 
                !$this->user->hasKey('warehouse_delete')) {
                
                $this->central_service->logActivity(
                    'access_denied',
                    'warehouse',
                    'محاولة حذف مستودع بدون صلاحية',
                    array('user_id' => $this->user->getId())
                );
                
                $this->session->data['error'] = $this->language->get('error_permission');
                $this->response->redirect($this->url->link('inventory/warehouse', 'user_token=' . $this->session->data['user_token'], true));
            }
            
            // تحميل اللغة والنماذج
            $this->load->language('inventory/warehouse');
            $this->document->setTitle($this->language->get('heading_title'));
            $this->load->model('inventory/warehouse_enhanced');
            
            if (isset($this->request->post['selected']) && $this->validateDelete()) {
                $deleted_count = 0;
                $deleted_names = array();
                
                foreach ($this->request->post['selected'] as $warehouse_id) {
                    // الحصول على معلومات المستودع قبل الحذف
                    $warehouse_info = $this->model_inventory_warehouse_enhanced->getWarehouse($warehouse_id);
                    
                    // حذف المستودع
                    $this->model_inventory_warehouse_enhanced->deleteWarehouse($warehouse_id);
                    
                    $deleted_count++;
                    $deleted_names[] = $warehouse_info['name'];
                    
                    // تسجيل النشاط
                    $this->central_service->logActivity(
                        'delete',
                        'warehouse',
                        'حذف مستودع: ' . $warehouse_info['name'],
                        array(
                            'warehouse_id' => $warehouse_id,
                            'warehouse_data' => $warehouse_info
                        )
                    );
                }
                
                // إرسال إشعار
                $this->central_service->sendNotification(
                    'warehouses_deleted',
                    'تم حذف ' . $deleted_count . ' مستودع: ' . implode(', ', $deleted_names),
                    array('deleted_count' => $deleted_count, 'deleted_names' => $deleted_names),
                    array($this->user->getId())
                );
                
                $this->session->data['success'] = $this->language->get('text_success');
                
                // إعادة التوجيه
                $url = $this->buildUrlParams();
                $this->response->redirect($this->url->link('inventory/warehouse', 'user_token=' . $this->session->data['user_token'] . $url, true));
            }
            
            $this->getList();
            
        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'warehouse',
                'خطأ في حذف مستودع: ' . $e->getMessage(),
                array('error' => $e->getTraceAsString())
            );
            
            $this->session->data['error'] = $this->language->get('error_exception');
            $this->response->redirect($this->url->link('inventory/warehouse', 'user_token=' . $this->session->data['user_token'], true));
        }
    }
    
    /**
     * عرض الهيكل الشجري للمستودعات
     */
    public function tree() {
        try {
            // التحقق من الصلاحيات
            if (!$this->user->hasPermission('access', 'inventory/warehouse')) {
                $this->response->addHeader('Content-Type: application/json');
                $this->response->setOutput(json_encode(array('error' => 'Access denied')));
                return;
            }
            
            // تحميل النموذج
            $this->load->model('inventory/warehouse_enhanced');
            
            // الحصول على الهيكل الشجري
            $warehouses_tree = $this->model_inventory_warehouse_enhanced->getWarehousesTree();
            
            // تحويل إلى تنسيق JSON للعرض الشجري
            $tree_data = $this->buildTreeData($warehouses_tree);
            
            $this->response->addHeader('Content-Type: application/json');
            $this->response->setOutput(json_encode($tree_data));
            
        } catch (Exception $e) {
            $this->response->addHeader('Content-Type: application/json');
            $this->response->setOutput(json_encode(array('error' => $e->getMessage())));
        }
    }
    
    /**
     * نقل المنتجات بين المستودعات
     */
    public function transfer() {
        try {
            // التحقق من الصلاحيات المزدوجة
            if (!$this->user->hasPermission('modify', 'inventory/warehouse') || 
                !$this->user->hasKey('warehouse_transfer')) {
                
                $this->response->addHeader('Content-Type: application/json');
                $this->response->setOutput(json_encode(array('error' => 'Access denied')));
                return;
            }
            
            // تحميل النماذج
            $this->load->model('inventory/warehouse_enhanced');
            $this->load->language('inventory/warehouse');
            
            if ($this->request->server['REQUEST_METHOD'] == 'POST') {
                $from_warehouse_id = $this->request->post['from_warehouse_id'];
                $to_warehouse_id = $this->request->post['to_warehouse_id'];
                $products = $this->request->post['products'];
                $notes = $this->request->post['notes'] ?? '';
                
                // نقل المنتجات
                $this->model_inventory_warehouse_enhanced->transferProducts(
                    $from_warehouse_id, 
                    $to_warehouse_id, 
                    $products, 
                    $notes
                );
                
                // تسجيل النشاط
                $this->central_service->logActivity(
                    'transfer',
                    'warehouse',
                    'نقل منتجات بين المستودعات',
                    array(
                        'from_warehouse_id' => $from_warehouse_id,
                        'to_warehouse_id' => $to_warehouse_id,
                        'products' => $products,
                        'notes' => $notes
                    )
                );
                
                // إرسال إشعار
                $this->central_service->sendNotification(
                    'warehouse_transfer',
                    'تم نقل منتجات بين المستودعات',
                    array(
                        'from_warehouse_id' => $from_warehouse_id,
                        'to_warehouse_id' => $to_warehouse_id,
                        'product_count' => count($products)
                    ),
                    array($this->user->getId())
                );
                
                $this->response->addHeader('Content-Type: application/json');
                $this->response->setOutput(json_encode(array('success' => true)));
            } else {
                // عرض نموذج النقل
                $this->load->view('inventory/warehouse_transfer', $data);
            }
            
        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'warehouse',
                'خطأ في نقل المنتجات: ' . $e->getMessage(),
                array('error' => $e->getTraceAsString())
            );
            
            $this->response->addHeader('Content-Type: application/json');
            $this->response->setOutput(json_encode(array('error' => $e->getMessage())));
        }
    }
    
    /**
     * البحث المتقدم في المستودعات
     */
    public function search() {
        try {
            // التحقق من الصلاحيات
            if (!$this->user->hasPermission('access', 'inventory/warehouse')) {
                $this->response->addHeader('Content-Type: application/json');
                $this->response->setOutput(json_encode(array('error' => 'Access denied')));
                return;
            }
            
            // تحميل النموذج
            $this->load->model('inventory/warehouse_enhanced');
            
            // البحث
            $search_data = $this->request->get;
            $results = $this->model_inventory_warehouse_enhanced->searchWarehouses($search_data);
            
            $this->response->addHeader('Content-Type: application/json');
            $this->response->setOutput(json_encode($results));
            
        } catch (Exception $e) {
            $this->response->addHeader('Content-Type: application/json');
            $this->response->setOutput(json_encode(array('error' => $e->getMessage())));
        }
    }
    
    /**
     * الحصول على إحصائيات المستودع
     */
    public function statistics() {
        try {
            // التحقق من الصلاحيات
            if (!$this->user->hasPermission('access', 'inventory/warehouse')) {
                $this->response->addHeader('Content-Type: application/json');
                $this->response->setOutput(json_encode(array('error' => 'Access denied')));
                return;
            }
            
            // تحميل النموذج
            $this->load->model('inventory/warehouse_enhanced');
            
            $warehouse_id = isset($this->request->get['warehouse_id']) ? $this->request->get['warehouse_id'] : null;
            $statistics = $this->model_inventory_warehouse_enhanced->getWarehouseStatistics($warehouse_id);
            
            $this->response->addHeader('Content-Type: application/json');
            $this->response->setOutput(json_encode($statistics));
            
        } catch (Exception $e) {
            $this->response->addHeader('Content-Type: application/json');
            $this->response->setOutput(json_encode(array('error' => $e->getMessage())));
        }
    }
    
    /**
     * بناء معاملات URL
     */
    private function buildUrlParams() {
        $url = '';
        
        if (isset($this->request->get['sort'])) {
            $url .= '&sort=' . $this->request->get['sort'];
        }
        
        if (isset($this->request->get['order'])) {
            $url .= '&order=' . $this->request->get['order'];
        }
        
        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }
        
        return $url;
    }
    
    /**
     * بناء بيانات الشجرة للعرض
     */
    private function buildTreeData($warehouses, $level = 0) {
        $tree_data = array();
        
        foreach ($warehouses as $warehouse) {
            $node = array(
                'id' => $warehouse['warehouse_id'],
                'text' => $warehouse['name'] . ' (' . $warehouse['code'] . ')',
                'icon' => $warehouse['location_type'] == 'warehouse' ? 'fa fa-warehouse' : 'fa fa-cube',
                'state' => array(
                    'opened' => $level < 2
                ),
                'data' => array(
                    'warehouse_id' => $warehouse['warehouse_id'],
                    'name' => $warehouse['name'],
                    'code' => $warehouse['code'],
                    'level' => $warehouse['level'],
                    'capacity_percentage' => $warehouse['capacity_percentage'],
                    'product_count' => $warehouse['product_count'],
                    'status' => $warehouse['status']
                )
            );
            
            if (!empty($warehouse['children'])) {
                $node['children'] = $this->buildTreeData($warehouse['children'], $level + 1);
            }
            
            $tree_data[] = $node;
        }
        
        return $tree_data;
    }
}
 
   /**
     * دوال مساعدة مطلوبة للـ Controller - Enterprise Grade Plus
     */

    /**
     * إعداد بيانات الفلاتر
     */
    private function prepareFilterData() {
        $filter_data = array();
        
        // الفلاتر الأساسية
        $filter_data['filter_name'] = isset($this->request->get['filter_name']) ? $this->request->get['filter_name'] : '';
        $filter_data['filter_code'] = isset($this->request->get['filter_code']) ? $this->request->get['filter_code'] : '';
        $filter_data['filter_status'] = isset($this->request->get['filter_status']) ? $this->request->get['filter_status'] : '';
        $filter_data['filter_location_type'] = isset($this->request->get['filter_location_type']) ? $this->request->get['filter_location_type'] : '';
        
        // فلاتر متقدمة
        $filter_data['filter_parent'] = isset($this->request->get['filter_parent']) ? $this->request->get['filter_parent'] : '';
        $filter_data['filter_branch'] = isset($this->request->get['filter_branch']) ? $this->request->get['filter_branch'] : '';
        $filter_data['filter_capacity_min'] = isset($this->request->get['filter_capacity_min']) ? $this->request->get['filter_capacity_min'] : '';
        $filter_data['filter_capacity_max'] = isset($this->request->get['filter_capacity_max']) ? $this->request->get['filter_capacity_max'] : '';
        
        // الترتيب والصفحات
        $filter_data['sort'] = isset($this->request->get['sort']) ? $this->request->get['sort'] : 'name';
        $filter_data['order'] = isset($this->request->get['order']) ? $this->request->get['order'] : 'ASC';
        $filter_data['page'] = isset($this->request->get['page']) ? (int)$this->request->get['page'] : 1;
        $filter_data['limit'] = isset($this->request->get['limit']) ? (int)$this->request->get['limit'] : $this->config->get('config_limit_admin');
        $filter_data['start'] = ($filter_data['page'] - 1) * $filter_data['limit'];
        
        return $filter_data;
    }

    /**
     * الحصول على الفلاتر المطبقة
     */
    private function getAppliedFilters() {
        $filters = array();
        
        if (isset($this->request->get['filter_name']) && !empty($this->request->get['filter_name'])) {
            $filters['name'] = $this->request->get['filter_name'];
        }
        
        if (isset($this->request->get['filter_code']) && !empty($this->request->get['filter_code'])) {
            $filters['code'] = $this->request->get['filter_code'];
        }
        
        if (isset($this->request->get['filter_status']) && $this->request->get['filter_status'] !== '') {
            $filters['status'] = $this->request->get['filter_status'];
        }
        
        return $filters;
    }

    /**
     * إعداد بيانات المستودع للعرض
     */
    private function prepareWarehouseData($warehouse) {
        // الحصول على إحصائيات المستودع
        $product_count = $this->model_inventory_warehouse_enhanced->getTotalProductsInWarehouse($warehouse['warehouse_id']);
        $total_value = $this->model_inventory_warehouse_enhanced->getTotalValueInWarehouse($warehouse['warehouse_id']);
        
        return array(
            'warehouse_id' => $warehouse['warehouse_id'],
            'name' => $warehouse['name'],
            'code' => $warehouse['code'],
            'parent_name' => isset($warehouse['parent_name']) ? $warehouse['parent_name'] : '',
            'location_type' => $warehouse['location_type'] ?? 'warehouse',
            'address' => $warehouse['address'],
            'telephone' => $warehouse['telephone'],
            'email' => $warehouse['email'],
            'manager' => $warehouse['manager'],
            'total_capacity' => $warehouse['total_capacity'] ?? 0,
            'used_capacity' => $warehouse['used_capacity'] ?? 0,
            'capacity_percentage' => $warehouse['total_capacity'] > 0 ? round(($warehouse['used_capacity'] / $warehouse['total_capacity']) * 100, 2) : 0,
            'capacity_unit' => $warehouse['capacity_unit'] ?? 'cbm',
            'total_products' => $product_count,
            'total_value' => $this->currency->format($total_value, $this->config->get('config_currency')),
            'status' => $warehouse['status'] ? $this->language->get('text_enabled') : $this->language->get('text_disabled'),
            'date_added' => date($this->language->get('date_format_short'), strtotime($warehouse['date_added'])),
            'edit' => $this->url->link('inventory/warehouse/edit', 'user_token=' . $this->session->data['user_token'] . '&warehouse_id=' . $warehouse['warehouse_id'], true),
            'selected' => isset($this->request->post['selected']) && in_array($warehouse['warehouse_id'], $this->request->post['selected'])
        );
    }

    /**
     * الحصول على إحصائيات المستودعات
     */
    private function getWarehouseStatistics() {
        return $this->model_inventory_warehouse_enhanced->getWarehouseStatistics();
    }

    /**
     * الحصول على ويدجت لوحة المؤشرات
     */
    private function getDashboardWidgets() {
        return array(
            'low_stock_alerts' => $this->model_inventory_warehouse_enhanced->getLowStockAlerts(5),
            'recent_movements' => $this->model_inventory_warehouse_enhanced->getRecentMovements(5),
            'expiry_alerts' => $this->model_inventory_warehouse_enhanced->getExpiryAlerts(30),
            'pending_transfers' => $this->model_inventory_warehouse_enhanced->getPendingTransfers(5)
        );
    }

    /**
     * إعداد الفلاتر للعرض
     */
    private function prepareFiltersForView() {
        // الحصول على قوائم الخيارات
        $location_types = array(
            'warehouse' => $this->language->get('text_location_warehouse'),
            'store' => $this->language->get('text_location_store'),
            'showroom' => $this->language->get('text_location_showroom'),
            'factory' => $this->language->get('text_location_factory'),
            'office' => $this->language->get('text_location_office'),
            'branch' => $this->language->get('text_location_branch')
        );
        
        $parent_warehouses = $this->model_inventory_warehouse_enhanced->getWarehouses(array('filter_parent' => 0));
        
        return array(
            'location_types' => $location_types,
            'parent_warehouses' => $parent_warehouses,
            'filter_name' => isset($this->request->get['filter_name']) ? $this->request->get['filter_name'] : '',
            'filter_code' => isset($this->request->get['filter_code']) ? $this->request->get['filter_code'] : '',
            'filter_status' => isset($this->request->get['filter_status']) ? $this->request->get['filter_status'] : '',
            'filter_location_type' => isset($this->request->get['filter_location_type']) ? $this->request->get['filter_location_type'] : ''
        );
    }

    /**
     * الحصول على خيارات البحث
     */
    private function getSearchOptions() {
        return array(
            'name' => $this->language->get('column_name'),
            'code' => $this->language->get('column_code'),
            'address' => $this->language->get('column_address'),
            'manager' => $this->language->get('column_manager')
        );
    }

    /**
     * الحصول على مدراء الأمان
     */
    private function getSecurityManagers() {
        // الحصول على المستخدمين الذين لديهم صلاحية security_manager
        $this->load->model('user/user');
        $users = $this->model_user_user->getUsers();
        
        $security_managers = array();
        foreach ($users as $user) {
            if ($this->user->hasKey('security_manager', $user['user_id'])) {
                $security_managers[] = $user['user_id'];
            }
        }
        
        // إضافة المدير العام (المجموعة 1)
        $admin_users = $this->model_user_user->getUsersByGroup(1);
        foreach ($admin_users as $admin) {
            if (!in_array($admin['user_id'], $security_managers)) {
                $security_managers[] = $admin['user_id'];
            }
        }
        
        return $security_managers;
    }

    /**
     * الحصول على المطورين
     */
    private function getDevelopers() {
        // الحصول على المستخدمين الذين لديهم صلاحية developer
        $this->load->model('user/user');
        $users = $this->model_user_user->getUsers();
        
        $developers = array();
        foreach ($users as $user) {
            if ($this->user->hasKey('developer', $user['user_id'])) {
                $developers[] = $user['user_id'];
            }
        }
        
        // إضافة المدير العام كـ fallback
        if (empty($developers)) {
            $admin_users = $this->model_user_user->getUsersByGroup(1);
            foreach ($admin_users as $admin) {
                $developers[] = $admin['user_id'];
            }
        }
        
        return $developers;
    }

    /**
     * معالجة طلبات AJAX
     */
    private function handleAjaxRequests() {
        $json = array();
        
        try {
            $action = isset($this->request->post['action']) ? $this->request->post['action'] : '';
            
            switch ($action) {
                case 'get_warehouse_info':
                    $json = $this->getWarehouseInfoAjax();
                    break;
                    
                case 'update_warehouse_status':
                    $json = $this->updateWarehouseStatusAjax();
                    break;
                    
                case 'get_warehouse_statistics':
                    $json = $this->getWarehouseStatisticsAjax();
                    break;
                    
                case 'search_warehouses':
                    $json = $this->searchWarehousesAjax();
                    break;
                    
                default:
                    $json['error'] = $this->language->get('error_invalid_action');
            }
            
        } catch (Exception $e) {
            $json['error'] = $e->getMessage();
            
            // تسجيل الخطأ
            $this->central_service->logActivity(
                'error',
                'warehouse_ajax_error',
                'خطأ في طلب AJAX: ' . $e->getMessage(),
                array(
                    'action' => $action ?? 'unknown',
                    'error_trace' => $e->getTraceAsString(),
                    'user_id' => $this->user->getId()
                )
            );
        }
        
        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * الحصول على معلومات المستودع عبر AJAX
     */
    private function getWarehouseInfoAjax() {
        $json = array();
        
        if (isset($this->request->post['warehouse_id'])) {
            $warehouse_id = (int)$this->request->post['warehouse_id'];
            $warehouse = $this->model_inventory_warehouse_enhanced->getWarehouse($warehouse_id);
            
            if ($warehouse) {
                $json['success'] = true;
                $json['warehouse'] = $this->prepareWarehouseData($warehouse);
            } else {
                $json['error'] = $this->language->get('error_warehouse_not_found');
            }
        } else {
            $json['error'] = $this->language->get('error_warehouse_id_required');
        }
        
        return $json;
    }

    /**
     * تحديث حالة المستودع عبر AJAX
     */
    private function updateWarehouseStatusAjax() {
        $json = array();
        
        // التحقق من الصلاحيات
        if (!$this->user->hasPermission('modify', $this->permissions['modify']) || 
            !$this->user->hasKey('warehouse_status_change')) {
            $json['error'] = $this->language->get('error_permission');
            return $json;
        }
        
        if (isset($this->request->post['warehouse_id']) && isset($this->request->post['status'])) {
            $warehouse_id = (int)$this->request->post['warehouse_id'];
            $status = (int)$this->request->post['status'];
            
            $warehouse_info = $this->model_inventory_warehouse_enhanced->getWarehouse($warehouse_id);
            
            if ($warehouse_info) {
                $this->model_inventory_warehouse_enhanced->editWarehouse($warehouse_id, array('status' => $status));
                
                // تسجيل النشاط
                $this->central_service->logActivity(
                    'status_change',
                    'warehouse',
                    sprintf('تغيير حالة المستودع %s إلى %s', 
                           $warehouse_info['name'], 
                           $status ? 'مفعل' : 'معطل'),
                    array(
                        'warehouse_id' => $warehouse_id,
                        'old_status' => $warehouse_info['status'],
                        'new_status' => $status,
                        'user_id' => $this->user->getId()
                    )
                );
                
                $json['success'] = true;
                $json['message'] = $this->language->get('text_status_updated');
            } else {
                $json['error'] = $this->language->get('error_warehouse_not_found');
            }
        } else {
            $json['error'] = $this->language->get('error_required_fields');
        }
        
        return $json;
    }

    /**
     * الحصول على إحصائيات المستودعات عبر AJAX
     */
    private function getWarehouseStatisticsAjax() {
        $json = array();
        
        try {
            $statistics = $this->getWarehouseStatistics();
            $widgets = $this->getDashboardWidgets();
            
            $json['success'] = true;
            $json['statistics'] = $statistics;
            $json['widgets'] = $widgets;
            
        } catch (Exception $e) {
            $json['error'] = $e->getMessage();
        }
        
        return $json;
    }

    /**
     * البحث في المستودعات عبر AJAX
     */
    private function searchWarehousesAjax() {
        $json = array();
        
        try {
            $search_term = isset($this->request->post['search']) ? $this->request->post['search'] : '';
            $limit = isset($this->request->post['limit']) ? (int)$this->request->post['limit'] : 10;
            
            $filter_data = array(
                'filter_name' => $search_term,
                'limit' => $limit
            );
            
            $warehouses = $this->model_inventory_warehouse_enhanced->getWarehouses($filter_data);
            
            $results = array();
            foreach ($warehouses as $warehouse) {
                $results[] = array(
                    'warehouse_id' => $warehouse['warehouse_id'],
                    'name' => $warehouse['name'],
                    'code' => $warehouse['code'],
                    'address' => $warehouse['address']
                );
            }
            
            $json['success'] = true;
            $json['warehouses'] = $results;
            
        } catch (Exception $e) {
            $json['error'] = $e->getMessage();
        }
        
        return $json;
    }

    /**
     * الحصول على متغيرات اللغة
     */
    private function getLanguageVariables() {
        return array(
            // العناوين
            'heading_title' => $this->language->get('heading_title'),
            'text_list' => $this->language->get('text_list'),
            'text_add' => $this->language->get('text_add'),
            'text_edit' => $this->language->get('text_edit'),
            'text_enabled' => $this->language->get('text_enabled'),
            'text_disabled' => $this->language->get('text_disabled'),
            'text_confirm' => $this->language->get('text_confirm'),
            'text_loading' => $this->language->get('text_loading'),
            'text_no_results' => $this->language->get('text_no_results'),
            
            // الأعمدة
            'column_name' => $this->language->get('column_name'),
            'column_code' => $this->language->get('column_code'),
            'column_address' => $this->language->get('column_address'),
            'column_product_count' => $this->language->get('column_product_count'),
            'column_total_value' => $this->language->get('column_total_value'),
            'column_status' => $this->language->get('column_status'),
            'column_action' => $this->language->get('column_action'),
            
            // الأزرار
            'button_add' => $this->language->get('button_add'),
            'button_edit' => $this->language->get('button_edit'),
            'button_delete' => $this->language->get('button_delete'),
            'button_search' => $this->language->get('button_search'),
            'button_clear' => $this->language->get('button_clear'),
            'button_export' => $this->language->get('button_export'),
            'button_print' => $this->language->get('button_print'),
            
            // الرسائل
            'text_success' => $this->language->get('text_success'),
            'error_permission' => $this->language->get('error_permission'),
            'error_warning' => $this->language->get('error_warning')
        );
    }

    /**
     * التحقق من صحة النموذج
     */
    protected function validateForm() {
        if (!$this->user->hasPermission('modify', $this->permissions['modify'])) {
            $this->error['warning'] = $this->language->get('error_permission');
        }

        if ((utf8_strlen($this->request->post['name']) < 3) || (utf8_strlen($this->request->post['name']) > 64)) {
            $this->error['name'] = $this->language->get('error_name');
        }

        if ((utf8_strlen($this->request->post['code']) < 2) || (utf8_strlen($this->request->post['code']) > 10)) {
            $this->error['code'] = $this->language->get('error_code');
        }

        // التحقق من تفرد الكود
        $warehouse_info = $this->model_inventory_warehouse_enhanced->getWarehouseByCode($this->request->post['code']);
        if ($warehouse_info && (!isset($this->request->get['warehouse_id']) || $warehouse_info['warehouse_id'] != $this->request->get['warehouse_id'])) {
            $this->error['code'] = $this->language->get('error_code_exists');
        }

        return !$this->error;
    }

    /**
     * التحقق من صحة الحذف
     */
    protected function validateDelete() {
        if (!$this->user->hasPermission('modify', $this->permissions['delete'])) {
            $this->error['warning'] = $this->language->get('error_permission');
        }

        foreach ($this->request->post['selected'] as $warehouse_id) {
            // التحقق من وجود منتجات في المستودع
            $product_count = $this->model_inventory_warehouse_enhanced->getTotalProductsInWarehouse($warehouse_id);
            if ($product_count > 0) {
                $warehouse_info = $this->model_inventory_warehouse_enhanced->getWarehouse($warehouse_id);
                $this->error['warning'] = sprintf($this->language->get('error_product'), $warehouse_info['name']);
                break;
            }

            // التحقق من وجود مستودعات فرعية
            $children = $this->model_inventory_warehouse_enhanced->getWarehouseChildren($warehouse_id);
            if ($children) {
                $warehouse_info = $this->model_inventory_warehouse_enhanced->getWarehouse($warehouse_id);
                $this->error['warning'] = sprintf($this->language->get('error_children'), $warehouse_info['name']);
                break;
            }
        }

        return !$this->error;
    }
}
?>