<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/protobuf/struct.proto

namespace GPBMetadata\Google\Protobuf;

class Struct
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(hex2bin(
            "0a81050a1c676f6f676c652f70726f746f6275662f7374727563742e7072" .
            "6f746f120f676f6f676c652e70726f746f6275662284010a065374727563" .
            "7412330a066669656c647318012003280b32232e676f6f676c652e70726f" .
            "746f6275662e5374727563742e4669656c6473456e7472791a450a0b4669" .
            "656c6473456e747279120b0a036b657918012001280912250a0576616c75" .
            "6518022001280b32162e676f6f676c652e70726f746f6275662e56616c75" .
            "653a02380122ea010a0556616c756512300a0a6e756c6c5f76616c756518" .
            "012001280e321a2e676f6f676c652e70726f746f6275662e4e756c6c5661" .
            "6c7565480012160a0c6e756d6265725f76616c7565180220012801480012" .
            "160a0c737472696e675f76616c7565180320012809480012140a0a626f6f" .
            "6c5f76616c75651804200128084800122f0a0c7374727563745f76616c75" .
            "6518052001280b32172e676f6f676c652e70726f746f6275662e53747275" .
            "6374480012300a0a6c6973745f76616c756518062001280b321a2e676f6f" .
            "676c652e70726f746f6275662e4c69737456616c7565480042060a046b69" .
            "6e6422330a094c69737456616c756512260a0676616c7565731801200328" .
            "0b32162e676f6f676c652e70726f746f6275662e56616c75652a1b0a094e" .
            "756c6c56616c7565120e0a0a4e554c4c5f56414c554510004281010a1363" .
            "6f6d2e676f6f676c652e70726f746f627566420b53747275637450726f74" .
            "6f50015a316769746875622e636f6d2f676f6c616e672f70726f746f6275" .
            "662f7074797065732f7374727563743b7374727563747062f80101a20203" .
            "475042aa021e476f6f676c652e50726f746f6275662e57656c6c4b6e6f77" .
            "6e5479706573620670726f746f33"
        ));

        static::$is_initialized = true;
    }
}

