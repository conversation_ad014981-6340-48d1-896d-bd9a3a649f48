<?php
/**
 * كونترولر تاريخ حركة المخزون المتقدم - Enterprise Grade
 *
 * التحسينات الجديدة:
 * - تطبيق الخدمات المركزية (central_service_manager.php)
 * - نظام الصلاحيات المزدوج (hasPermission + hasKey)
 * - تسجيل شامل للأنشطة (logActivity)
 * - نظام الإشعارات المتقدم (sendNotification)
 * - معالجة الأخطاء الشاملة (try-catch)
 * - تكامل مع نظام WAC (المتوسط المرجح للتكلفة)
 * - تكامل مع النظام المحاسبي
 * - تحليلات متقدمة للحركات
 * - تصدير متعدد الصيغ (Excel, PDF, CSV)
 * - فلاتر ذكية وبحث متقدم
 *
 * <AUTHOR> Team - Enhanced by Kiro AI
 * @version 2.0 - Enterprise Grade
 * @since 2025
 * @reference current_stock.php - Proven Example
 */

class ControllerInventoryMovementHistory extends Controller {
    private $error = array();
    private $central_service;

    public function __construct($registry) {
        parent::__construct($registry);

        // تحميل الخدمات المركزية
        $this->load->model('common/central_service_manager');
        $this->central_service = new CentralServiceManager($this->registry);
    }
    /**
     * الصفحة الرئيسية - عرض تاريخ حركة المخزون
     */
    public function index() {
        try {
            // التحقق من الصلاحيات الأساسية والمتقدمة
            if (!$this->user->hasPermission('access', 'inventory/movement_history')) {
                $this->central_service->logActivity(
                    'access_denied',
                    'movement_history',
                    'محاولة وصول غير مصرح به لشاشة تاريخ حركة المخزون',
                    array('user_id' => $this->user->getId())
                );

                $this->response->redirect($this->url->link('error/permission', 'user_token=' . $this->session->data['user_token'], true));
            }

            // التحقق من الصلاحيات المتقدمة
            if (!$this->user->hasKey('movement_history_view')) {
                $this->central_service->logActivity(
                    'access_denied_advanced',
                    'movement_history',
                    'محاولة وصول بصلاحيات متقدمة غير مصرح بها لعرض تاريخ حركة المخزون',
                    array('user_id' => $this->user->getId())
                );

                $this->session->data['warning'] = $this->language->get('error_advanced_permission');
            }

            // تسجيل النشاط
            $this->central_service->logActivity(
                'view',
                'movement_history',
                'عرض تاريخ حركة المخزون',
                array('user_id' => $this->user->getId())
            );

            // تحميل اللغة والنماذج
            $this->load->language('inventory/movement_history');
            $this->document->setTitle($this->language->get('heading_title'));

            $this->load->model('inventory/movement_history');
            $this->load->model('inventory/product');
            $this->load->model('inventory/warehouse');

            // عرض القائمة
            $this->getList();

        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'movement_history',
                'خطأ في عرض تاريخ حركة المخزون: ' . $e->getMessage(),
                array('error' => $e->getTraceAsString())
            );

            $this->session->data['error'] = $this->language->get('error_exception');
            $this->response->redirect($this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true));
        }
    }

    /**
     * دالة عرض القائمة مع الفلاتر المتقدمة
     */
    protected function getList() {
        // القيم الافتراضية للفلاتر
        $filter_product_id = '';
        $filter_product = '';
        $filter_warehouse_id = '';
        $filter_movement_type = '';
        $filter_reference_type = '';
        $filter_date_start = date('Y-m-d', strtotime('-30 days'));
        $filter_date_end = date('Y-m-d');
        $filter_sort = 'sm.date_added';
        $filter_order = 'DESC';
        $page = 1;
        $limit = $this->config->get('config_limit_admin');

        // معالجة المدخلات والفلاتر
        if (isset($this->request->get['filter_product'])) {
            $filter_product = $this->request->get['filter_product'];

            // Try to get product ID from name
            $product_info = $this->model_catalog_product->getProductByName($filter_product);
            if ($product_info) {
                $filter_product_id = $product_info['product_id'];
            }
        }

        if (isset($this->request->get['filter_product_id'])) {
            $filter_product_id = $this->request->get['filter_product_id'];

            // Get product name for display
            $product_info = $this->model_catalog_product->getProduct($filter_product_id);
            if ($product_info) {
                $filter_product = $product_info['name'];
            }
        }

        if (isset($this->request->get['filter_branch_id'])) {
            $filter_branch_id = $this->request->get['filter_branch_id'];
        }

        if (isset($this->request->get['filter_movement_type'])) {
            $filter_movement_type = $this->request->get['filter_movement_type'];
        }

        if (isset($this->request->get['filter_reference_type'])) {
            $filter_reference_type = $this->request->get['filter_reference_type'];
        }

        if (isset($this->request->get['filter_date_start'])) {
            $filter_date_start = $this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $filter_date_end = $this->request->get['filter_date_end'];
        }

        if (isset($this->request->get['sort'])) {
            $filter_sort = $this->request->get['sort'];
        }

        if (isset($this->request->get['order'])) {
            $filter_order = $this->request->get['order'];
        }

        if (isset($this->request->get['page'])) {
            $page = (int)$this->request->get['page'];
        }

        // Build filter array
        $filter_data = array(
            'filter_product_id'    => $filter_product_id,
            'filter_branch_id'     => $filter_branch_id,
            'filter_movement_type' => $filter_movement_type,
            'filter_reference_type' => $filter_reference_type,
            'filter_date_start'    => $filter_date_start,
            'filter_date_end'      => $filter_date_end,
            'sort'                 => $filter_sort,
            'order'                => $filter_order,
            'start'                => ($page - 1) * $limit,
            'limit'                => $limit
        );

        // Get movements based on filters
        $movements = array();
        $movement_total = 0;

        if ($filter_product_id) {
            // If product is selected, get movements for that product
            $results = $this->model_catalog_inventory_manager->getProductMovements($filter_product_id, $filter_data);
            $movement_total = $this->model_catalog_inventory_manager->getTotalProductMovements($filter_product_id, $filter_data);
        } else {
            // Otherwise get all movements
            $results = $this->model_catalog_inventory_manager->getAllMovements($filter_data);
            $movement_total = $this->model_catalog_inventory_manager->getTotalAllMovements($filter_data);
        }

        // Process movement data for display
        foreach ($results as $result) {
            $reference_link = '';
            $reference_text = $result['reference_type'] . ' #' . $result['reference_id'];

            // Generate appropriate links based on reference type
            switch ($result['reference_type']) {
                case 'purchase':
                    $reference_link = $this->url->link('purchase/order/info', 'user_token=' . $this->session->data['user_token'] . '&order_id=' . $result['reference_id'], true);
                    break;
                case 'sale':
                    $reference_link = $this->url->link('sale/order/info', 'user_token=' . $this->session->data['user_token'] . '&order_id=' . $result['reference_id'], true);
                    break;
                case 'adjustment':
                    $reference_link = $this->url->link('inventory/adjustment/info', 'user_token=' . $this->session->data['user_token'] . '&adjustment_id=' . $result['reference_id'], true);
                    break;
                case 'transfer':
                    $reference_link = $this->url->link('inventory/transfer/info', 'user_token=' . $this->session->data['user_token'] . '&transfer_id=' . $result['reference_id'], true);
                    break;
                // Add more reference types as needed
            }

            // Format movement type for display
            $movement_type_text = '';
            if ($result['movement_type'] == 'in') {
                $movement_type_text = '<span class="badge bg-success">' . $this->language->get('text_in') . '</span>';
            } else {
                $movement_type_text = '<span class="badge bg-danger">' . $this->language->get('text_out') . '</span>';
            }

            // Format cost
            $cost_formatted = $this->currency->format($result['cost'], $this->config->get('config_currency'));

            // Add to movements array
            $movements[] = array(
                'movement_id'    => $result['movement_id'],
                'date_added'     => date($this->language->get('datetime_format'), strtotime($result['date_added'])),
                'product_name'   => $result['product_name'],
                'warehouse_name' => $result['warehouse_name'],
                'unit_name'      => $result['unit_name'],
                'quantity'       => $result['quantity'],
                'movement_type'  => $movement_type_text,
                'reference_type' => $result['reference_type'],
                'reference_id'   => $result['reference_id'],
                'reference_text' => $reference_text,
                'reference_link' => $reference_link,
                'cost'           => $cost_formatted,
                'notes'          => $result['notes'],
                'username'       => $result['username']
            );
        }

        // Prepare data for the view
        $data['movements'] = $movements;

        // Breadcrumbs
        $data['breadcrumbs'] = array();

        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );

        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('inventory/movement_history', 'user_token=' . $this->session->data['user_token'], true)
        );

        // URLs
        $data['export'] = $this->url->link('inventory/movement_history/export', 'user_token=' . $this->session->data['user_token'], true);

        // Filters for the view
        $data['filter_product'] = $filter_product;
        $data['filter_product_id'] = $filter_product_id;
        $data['filter_branch_id'] = $filter_branch_id;
        $data['filter_movement_type'] = $filter_movement_type;
        $data['filter_reference_type'] = $filter_reference_type;
        $data['filter_date_start'] = $filter_date_start;
        $data['filter_date_end'] = $filter_date_end;

        // Get branches for dropdown
        $data['branches'] = $this->model_branch_branch->getBranches();

        // Movement types for dropdown
        $data['movement_types'] = array(
            '' => $this->language->get('text_all_types'),
            'in' => $this->language->get('text_in'),
            'out' => $this->language->get('text_out')
        );

        // Reference types for dropdown
        $data['reference_types'] = array(
            '' => $this->language->get('text_all_references'),
            'purchase' => $this->language->get('text_purchase'),
            'sale' => $this->language->get('text_sale'),
            'adjustment' => $this->language->get('text_adjustment'),
            'transfer' => $this->language->get('text_transfer'),
            'return' => $this->language->get('text_return'),
            'production' => $this->language->get('text_production')
            // Add more reference types as needed
        );

        // Pagination
        $pagination = new Pagination();
        $pagination->total = $movement_total;
        $pagination->page = $page;
        $pagination->limit = $limit;
        $pagination->url = $this->url->link('inventory/movement_history', 'user_token=' . $this->session->data['user_token'] . '&page={page}', true);

        $data['pagination'] = $pagination->render();
        $data['results'] = sprintf($this->language->get('text_pagination'), ($movement_total) ? (($page - 1) * $limit) + 1 : 0, ((($page - 1) * $limit) > ($movement_total - $limit)) ? $movement_total : ((($page - 1) * $limit) + $limit), $movement_total, ceil($movement_total / $limit));

        // Sort URLs
        $url = '';

        if (isset($this->request->get['filter_product'])) {
            $url .= '&filter_product=' . urlencode($this->request->get['filter_product']);
        }

        if (isset($this->request->get['filter_branch_id'])) {
            $url .= '&filter_branch_id=' . $this->request->get['filter_branch_id'];
        }

        if (isset($this->request->get['filter_movement_type'])) {
            $url .= '&filter_movement_type=' . $this->request->get['filter_movement_type'];
        }

        if (isset($this->request->get['filter_reference_type'])) {
            $url .= '&filter_reference_type=' . $this->request->get['filter_reference_type'];
        }

        if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
        }

        $data['sort_date'] = $this->url->link('inventory/movement_history', 'user_token=' . $this->session->data['user_token'] . '&sort=sm.date_added' . $url, true);
        $data['sort_product'] = $this->url->link('inventory/movement_history', 'user_token=' . $this->session->data['user_token'] . '&sort=p.name' . $url, true);
        $data['sort_warehouse'] = $this->url->link('inventory/movement_history', 'user_token=' . $this->session->data['user_token'] . '&sort=w.name' . $url, true);
        $data['sort_quantity'] = $this->url->link('inventory/movement_history', 'user_token=' . $this->session->data['user_token'] . '&sort=sm.quantity' . $url, true);

        $data['sort'] = $filter_sort;
        $data['order'] = $filter_order;

        $data['user_token'] = $this->session->data['user_token'];

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('inventory/movement_history', $data));
    }

    /**
     * صفحة التحليلات المتقدمة لحركة المخزون
     */
    public function analytics() {
        try {
            // التحقق من الصلاحيات
            if (!$this->user->hasPermission('access', 'inventory/movement_history') || !$this->user->hasKey('movement_analytics_view')) {
                $this->response->redirect($this->url->link('error/permission', 'user_token=' . $this->session->data['user_token'], true));
            }

            $this->load->language('inventory/movement_history');
            $this->load->model('inventory/movement_history');

            $this->document->setTitle($this->language->get('heading_title') . ' - ' . $this->language->get('text_analytics'));

            $data['breadcrumbs'] = array();

            $data['breadcrumbs'][] = array(
                'text' => $this->language->get('text_home'),
                'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
            );

            $data['breadcrumbs'][] = array(
                'text' => $this->language->get('heading_title'),
                'href' => $this->url->link('inventory/movement_history', 'user_token=' . $this->session->data['user_token'], true)
            );

            $data['breadcrumbs'][] = array(
                'text' => $this->language->get('text_analytics'),
                'href' => $this->url->link('inventory/movement_history/analytics', 'user_token=' . $this->session->data['user_token'], true)
            );

            // الحصول على بيانات التحليلات
            $data['movement_summary'] = $this->model_inventory_movement_history->getMovementSummary();
            $data['daily_trends'] = $this->model_inventory_movement_history->getDailyTrends();
            $data['product_analysis'] = $this->model_inventory_movement_history->getProductAnalysis();
            $data['warehouse_analysis'] = $this->model_inventory_movement_history->getWarehouseAnalysis();
            $data['movement_types'] = $this->model_inventory_movement_history->getMovementTypeAnalysis();
            $data['reference_analysis'] = $this->model_inventory_movement_history->getReferenceAnalysis();
            $data['user_activity'] = $this->model_inventory_movement_history->getUserActivity();
            $data['cost_analysis'] = $this->model_inventory_movement_history->getCostAnalysis();

            $data['back'] = $this->url->link('inventory/movement_history', 'user_token=' . $this->session->data['user_token'], true);
            $data['user_token'] = $this->session->data['user_token'];

            $data['header'] = $this->load->controller('common/header');
            $data['column_left'] = $this->load->controller('common/column_left');
            $data['footer'] = $this->load->controller('common/footer');

            $this->response->setOutput($this->load->view('inventory/movement_history_analytics', $data));

        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'movement_history_analytics',
                'خطأ في عرض تحليلات حركة المخزون: ' . $e->getMessage(),
                array('error' => $e->getTraceAsString())
            );

            $this->session->data['error'] = $this->language->get('error_exception');
            $this->response->redirect($this->url->link('inventory/movement_history', 'user_token=' . $this->session->data['user_token'], true));
        }
    }

    /**
     * تصدير Excel محسن
     */
    public function export() {
        $this->load->language('inventory/movement_history');

        if (isset($this->request->get['filter_product_id'])) {
            $filter_product_id = $this->request->get['filter_product_id'];
        } else {
            $filter_product_id = '';
        }

        if (isset($this->request->get['filter_branch_id'])) {
            $filter_branch_id = $this->request->get['filter_branch_id'];
        } else {
            $filter_branch_id = '';
        }

        if (isset($this->request->get['filter_movement_type'])) {
            $filter_movement_type = $this->request->get['filter_movement_type'];
        } else {
            $filter_movement_type = '';
        }

        if (isset($this->request->get['filter_reference_type'])) {
            $filter_reference_type = $this->request->get['filter_reference_type'];
        } else {
            $filter_reference_type = '';
        }

        if (isset($this->request->get['filter_date_start'])) {
            $filter_date_start = $this->request->get['filter_date_start'];
        } else {
            $filter_date_start = date('Y-m-d', strtotime('-30 days'));
        }

        if (isset($this->request->get['filter_date_end'])) {
            $filter_date_end = $this->request->get['filter_date_end'];
        } else {
            $filter_date_end = date('Y-m-d');
        }

        // Build filter array
        $filter_data = array(
            'filter_product_id'    => $filter_product_id,
            'filter_branch_id'     => $filter_branch_id,
            'filter_movement_type' => $filter_movement_type,
            'filter_reference_type' => $filter_reference_type,
            'filter_date_start'    => $filter_date_start,
            'filter_date_end'      => $filter_date_end,
            'sort'                 => 'sm.date_added',
            'order'                => 'DESC',
            'start'                => 0,
            'limit'                => 5000 // Limit for export
        );

        $this->load->model('catalog/inventory_manager');
        $this->load->model('catalog/product');

        // Get movements based on filters
        if ($filter_product_id) {
            $results = $this->model_catalog_inventory_manager->getProductMovements($filter_product_id, $filter_data);
            $product_info = $this->model_catalog_product->getProduct($filter_product_id);
            $title = $this->language->get('text_movement_history') . ': ' . $product_info['name'];
        } else {
            $results = $this->model_catalog_inventory_manager->getAllMovements($filter_data);
            $title = $this->language->get('text_all_movement_history');
        }

        // Create Excel file
        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Set title
        $sheet->setCellValue('A1', $title);
        $sheet->mergeCells('A1:H1');
        $sheet->getStyle('A1')->getFont()->setBold(true);
        $sheet->getStyle('A1')->getFont()->setSize(14);

        // Set filter information
        $sheet->setCellValue('A2', $this->language->get('text_date_range') . ': ' . $filter_date_start . ' - ' . $filter_date_end);
        $sheet->mergeCells('A2:H2');

        // Set headers
        $sheet->setCellValue('A4', $this->language->get('column_date'));
        $sheet->setCellValue('B4', $this->language->get('column_product'));
        $sheet->setCellValue('C4', $this->language->get('column_warehouse'));
        $sheet->setCellValue('D4', $this->language->get('column_unit'));
        $sheet->setCellValue('E4', $this->language->get('column_quantity'));
        $sheet->setCellValue('F4', $this->language->get('column_movement_type'));
        $sheet->setCellValue('G4', $this->language->get('column_reference'));
        $sheet->setCellValue('H4', $this->language->get('column_cost'));
        $sheet->setCellValue('I4', $this->language->get('column_notes'));
        $sheet->setCellValue('J4', $this->language->get('column_user'));

        $sheet->getStyle('A4:J4')->getFont()->setBold(true);

        // Fill data
        $row = 5;
        foreach ($results as $result) {
            $sheet->setCellValue('A' . $row, date($this->language->get('datetime_format'), strtotime($result['date_added'])));
            $sheet->setCellValue('B' . $row, $result['product_name']);
            $sheet->setCellValue('C' . $row, $result['warehouse_name']);
            $sheet->setCellValue('D' . $row, $result['unit_name']);
            $sheet->setCellValue('E' . $row, $result['quantity']);
            $sheet->setCellValue('F' . $row, $result['movement_type'] == 'in' ? $this->language->get('text_in') : $this->language->get('text_out'));
            $sheet->setCellValue('G' . $row, $result['reference_type'] . ' #' . $result['reference_id']);
            $sheet->setCellValue('H' . $row, $result['cost']);
            $sheet->setCellValue('I' . $row, $result['notes']);
            $sheet->setCellValue('J' . $row, $result['username']);

            $row++;
        }

        // Auto size columns
        foreach (range('A', 'J') as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }

        // Set filename
        $filename = 'inventory_movement_history_' . date('Y-m-d_H-i-s') . '.xlsx';

        // Redirect output to a client's web browser (Xlsx)
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
        $writer->save('php://output');
        exit;
    }

    /**
     * Get movement details via AJAX
     */
    public function getMovementDetails() {
        $this->load->language('inventory/movement_history');

        $json = array();

        if (isset($this->request->get['movement_id'])) {
            $movement_id = (int)$this->request->get['movement_id'];

            $this->load->model('catalog/inventory_manager');

            $movement = $this->model_catalog_inventory_manager->getMovementDetails($movement_id);

            if ($movement) {
                // Format data for display
                $movement['date_added_formatted'] = date($this->language->get('datetime_format'), strtotime($movement['date_added']));
                $movement['cost_formatted'] = $this->currency->format($movement['cost'], $this->config->get('config_currency'));
                $movement['movement_type_text'] = $movement['movement_type'] == 'in' ? $this->language->get('text_in') : $this->language->get('text_out');

                $json['movement'] = $movement;
                $json['success'] = true;
            } else {
                $json['error'] = $this->language->get('error_movement_not_found');
            }
        } else {
            $json['error'] = $this->language->get('error_movement_id');
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * تصدير PDF مع تحليلات
     */
    public function exportPDF() {
        try {
            // التحقق من الصلاحيات
            if (!$this->user->hasPermission('modify', 'inventory/movement_history') || !$this->user->hasKey('movement_export')) {
                $this->response->redirect($this->url->link('error/permission', 'user_token=' . $this->session->data['user_token'], true));
            }

            $this->load->language('inventory/movement_history');
            $this->load->model('inventory/movement_history');

            // تسجيل النشاط
            $this->central_service->logActivity(
                'export',
                'movement_history',
                'تصدير تاريخ حركة المخزون إلى PDF',
                array('user_id' => $this->user->getId())
            );

            // الحصول على البيانات
            $filter_data = $this->getFilterData();
            $movements = $this->model_inventory_movement_history->getMovements($filter_data);

            // إنشاء ملف PDF
            $filename = 'movement_history_' . date('Y-m-d_H-i-s') . '.pdf';
            $this->createPDFFile($movements, $filename);

        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'movement_history_export_pdf',
                'خطأ في تصدير PDF لتاريخ حركة المخزون: ' . $e->getMessage(),
                array('error' => $e->getTraceAsString())
            );

            $this->session->data['error'] = $this->language->get('error_export_failed');
            $this->response->redirect($this->url->link('inventory/movement_history', 'user_token=' . $this->session->data['user_token'], true));
        }
    }

    /**
     * البحث المتقدم عبر AJAX
     */
    public function advancedSearch() {
        $json = array();

        try {
            // التحقق من الصلاحيات
            if (!$this->user->hasPermission('access', 'inventory/movement_history')) {
                $json['error'] = $this->language->get('error_permission');
                $this->response->addHeader('Content-Type: application/json');
                $this->response->setOutput(json_encode($json));
                return;
            }

            $this->load->language('inventory/movement_history');
            $this->load->model('inventory/movement_history');

            // معالجة معايير البحث
            $search_criteria = array(
                'product_name' => isset($this->request->post['product_name']) ? $this->request->post['product_name'] : '',
                'sku' => isset($this->request->post['sku']) ? $this->request->post['sku'] : '',
                'barcode' => isset($this->request->post['barcode']) ? $this->request->post['barcode'] : '',
                'movement_type' => isset($this->request->post['movement_type']) ? $this->request->post['movement_type'] : '',
                'reference_type' => isset($this->request->post['reference_type']) ? $this->request->post['reference_type'] : '',
                'reference_id' => isset($this->request->post['reference_id']) ? $this->request->post['reference_id'] : '',
                'warehouse_id' => isset($this->request->post['warehouse_id']) ? $this->request->post['warehouse_id'] : '',
                'user_id' => isset($this->request->post['user_id']) ? $this->request->post['user_id'] : '',
                'date_start' => isset($this->request->post['date_start']) ? $this->request->post['date_start'] : '',
                'date_end' => isset($this->request->post['date_end']) ? $this->request->post['date_end'] : '',
                'quantity_min' => isset($this->request->post['quantity_min']) ? $this->request->post['quantity_min'] : '',
                'quantity_max' => isset($this->request->post['quantity_max']) ? $this->request->post['quantity_max'] : '',
                'cost_min' => isset($this->request->post['cost_min']) ? $this->request->post['cost_min'] : '',
                'cost_max' => isset($this->request->post['cost_max']) ? $this->request->post['cost_max'] : ''
            );

            // البحث
            $results = $this->model_inventory_movement_history->advancedSearch($search_criteria);

            $json['success'] = true;
            $json['results'] = $results;
            $json['total'] = count($results);

        } catch (Exception $e) {
            $json['error'] = $this->language->get('error_search_failed');

            $this->central_service->logActivity(
                'error',
                'movement_history_search',
                'خطأ في البحث المتقدم: ' . $e->getMessage(),
                array('error' => $e->getTraceAsString())
            );
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * الحصول على بيانات الفلاتر
     */
    private function getFilterData() {
        $filter_data = array();

        if (isset($this->request->get['filter_product_id'])) {
            $filter_data['filter_product_id'] = $this->request->get['filter_product_id'];
        }

        if (isset($this->request->get['filter_warehouse_id'])) {
            $filter_data['filter_warehouse_id'] = $this->request->get['filter_warehouse_id'];
        }

        if (isset($this->request->get['filter_movement_type'])) {
            $filter_data['filter_movement_type'] = $this->request->get['filter_movement_type'];
        }

        if (isset($this->request->get['filter_reference_type'])) {
            $filter_data['filter_reference_type'] = $this->request->get['filter_reference_type'];
        }

        if (isset($this->request->get['filter_date_start'])) {
            $filter_data['filter_date_start'] = $this->request->get['filter_date_start'];
        }

        if (isset($this->request->get['filter_date_end'])) {
            $filter_data['filter_date_end'] = $this->request->get['filter_date_end'];
        }

        return $filter_data;
    }

    /**
     * إنشاء ملف Excel
     */
    private function createExcelFile($movements, $filename) {
        // هنا يتم إنشاء ملف Excel باستخدام مكتبة PHPSpreadsheet
        // سيتم تطوير هذه الدالة لاحقاً

        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        // إنشاء محتوى Excel بسيط
        $output = "Product Name\tMovement Type\tQuantity\tDate\n";
        foreach ($movements as $movement) {
            $output .= $movement['product_name'] . "\t" . $movement['movement_type'] . "\t" . $movement['quantity'] . "\t" . $movement['date_added'] . "\n";
        }

        echo $output;
        exit;
    }

    /**
     * إنشاء ملف PDF
     */
    private function createPDFFile($movements, $filename) {
        // هنا يتم إنشاء ملف PDF باستخدام مكتبة TCPDF
        // سيتم تطوير هذه الدالة لاحقاً

        header('Content-Type: application/pdf');
        header('Content-Disposition: attachment;filename="' . $filename . '"');

        // إنشاء محتوى PDF بسيط
        echo "PDF content will be generated here";
        exit;
    }
}
