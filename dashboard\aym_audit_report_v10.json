{"audit_info": {"version": "10.0", "timestamp": "2025-07-27T04:58:54.221962", "total_files_analyzed": 0}, "overall_scores": {"average_system_score": 0, "mvc_compliance_rate": 0, "bootstrap_consistency_score": 75.0}, "critical_issues": [], "system_recommendations": [], "detailed_analysis": {"bootstrap_analysis": {"dashboard": {"3.x": 0, "5.x": 0, "unknown": 0}, "catalog": {"3.x": 0, "5.x": 0, "unknown": 0}, "consistency_score": 75.0, "recommendation": "يُنصح بترقية الواجهة الأمامية إلى Bootstrap 5.x للميزات الحديثة"}, "bundle_system": {"files_with_bundle_support": [], "bundle_coverage_percentage": 0, "total_catalog_files": 0, "files_with_support": 0, "status": "needs_improvement", "recommendation": "يجب تطوير وتطبيق نظام الباقات في المزيد من الملفات"}, "wac_system": {"files_with_wac_support": [], "wac_coverage_percentage": 0, "total_inventory_files": 0, "files_with_support": 0, "status": "needs_improvement", "recommendation": "يجب تطوير وتطبيق نظام WAC في جميع ملفات المخزون"}, "target_categories": {"categories": {"clothing": {"files_with_support": [], "coverage_percentage": 0, "files_count": 0, "status": "needs_development"}, "perfumes": {"files_with_support": [], "coverage_percentage": 0, "files_count": 0, "status": "needs_development"}, "electronics": {"files_with_support": [], "coverage_percentage": 0, "files_count": 0, "status": "needs_development"}, "supermarket": {"files_with_support": [], "coverage_percentage": 0, "files_count": 0, "status": "needs_development"}, "mall": {"files_with_support": [], "coverage_percentage": 0, "files_count": 0, "status": "needs_development"}}, "overall_recommendation": "يجب تطوير دعم شامل لجميع الفئات المستهدفة", "priority_categories": ["clothing", "perfumes", "electronics", "supermarket", "mall"]}, "performance": {"large_files": [], "complex_files": [], "performance_recommendation": "الأداء جيد عموماً، مراجعة دورية للملفات الكبيرة مُنصح بها"}}, "file_results": [], "dependency_graph": {}}