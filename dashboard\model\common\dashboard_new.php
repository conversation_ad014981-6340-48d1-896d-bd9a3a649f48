<?php
/**
 * ═══════════════════════════════════════════════════════════════════════════════
 * AYM ERP - نموذج لوحة المعلومات التنفيذية المتكاملة
 * ═══════════════════════════════════════════════════════════════════════════════
 * 
 * @description نموذج متقدم لجلب البيانات من جميع أنحاء النظام
 * مع تركيز خاص على التجارة المتكاملة (إلكترونية + فروع + مناديب)
 */

class ModelCommonDashboardNew extends Model {
    
    /**
     * الحصول على إيرادات اليوم
     */
    public function getTodayRevenue() {
        $today = date('Y-m-d');
        
        $query = $this->db->query("
            SELECT 
                COALESCE(SUM(total), 0) as revenue,
                COUNT(*) as order_count,
                AVG(total) as avg_order_value
            FROM " . DB_PREFIX . "order 
            WHERE DATE(date_added) = '" . $today . "' 
            AND order_status_id > 0
        ");
        
        $result = $query->row;
        
        // إضافة مبيعات نقاط البيع
        $pos_query = $this->db->query("
            SELECT COALESCE(SUM(amount), 0) as pos_revenue
            FROM " . DB_PREFIX . "pos_transaction 
            WHERE DATE(transaction_date) = '" . $today . "'
            AND transaction_type = 'sale'
        ");
        
        $result['revenue'] = (float)$result['revenue'] + (float)$pos_query->row['pos_revenue'];
        
        return $result;
    }
    
    /**
     * الحصول على طلبات اليوم
     */
    public function getTodayOrders() {
        $today = date('Y-m-d');
        
        $query = $this->db->query("
            SELECT 
                COUNT(*) as total_orders,
                SUM(CASE WHEN order_status_id = 1 THEN 1 ELSE 0 END) as pending_orders,
                SUM(CASE WHEN order_status_id = 5 THEN 1 ELSE 0 END) as completed_orders,
                SUM(CASE WHEN order_status_id = 7 THEN 1 ELSE 0 END) as cancelled_orders
            FROM " . DB_PREFIX . "order 
            WHERE DATE(date_added) = '" . $today . "'
        ");
        
        return $query->row;
    }
    
    /**
     * الحصول على عملاء اليوم الجدد
     */
    public function getTodayCustomers() {
        $today = date('Y-m-d');
        
        $query = $this->db->query("
            SELECT COUNT(*) as new_customers
            FROM " . DB_PREFIX . "customer 
            WHERE DATE(date_added) = '" . $today . "'
        ");
        
        return (int)$query->row['new_customers'];
    }
    
    /**
     * حساب هامش الربح لليوم
     */
    public function getTodayProfitMargin() {
        $today = date('Y-m-d');
        
        // حساب إجمالي المبيعات
        $sales_query = $this->db->query("
            SELECT COALESCE(SUM(total), 0) as total_sales
            FROM " . DB_PREFIX . "order 
            WHERE DATE(date_added) = '" . $today . "' 
            AND order_status_id > 0
        ");
        
        // حساب تكلفة البضاعة المباعة
        $cogs_query = $this->db->query("
            SELECT COALESCE(SUM(oc.total_cost), 0) as total_cogs
            FROM " . DB_PREFIX . "order o
            JOIN " . DB_PREFIX . "order_cogs oc ON o.order_id = oc.order_id
            WHERE DATE(o.date_added) = '" . $today . "'
            AND o.order_status_id > 0
        ");
        
        $total_sales = (float)$sales_query->row['total_sales'];
        $total_cogs = (float)$cogs_query->row['total_cogs'];
        
        $profit_margin = $total_sales > 0 ? (($total_sales - $total_cogs) / $total_sales) * 100 : 0;
        
        return [
            'margin_percentage' => round($profit_margin, 2),
            'gross_profit' => $total_sales - $total_cogs,
            'total_sales' => $total_sales,
            'total_cogs' => $total_cogs
        ];
    }
    
    /**
     * إيرادات الشهر الحالي
     */
    public function getMonthRevenue() {
        $start_of_month = date('Y-m-01');
        $end_of_month = date('Y-m-t');
        
        $query = $this->db->query("
            SELECT 
                COALESCE(SUM(total), 0) as revenue,
                COUNT(*) as order_count
            FROM " . DB_PREFIX . "order 
            WHERE date_added BETWEEN '" . $start_of_month . " 00:00:00' 
            AND '" . $end_of_month . " 23:59:59'
            AND order_status_id > 0
        ");
        
        return $query->row;
    }
    
    /**
     * نمو الإيرادات مقارنة بالشهر الماضي
     */
    public function getMonthGrowth() {
        $current_month_start = date('Y-m-01');
        $current_month_end = date('Y-m-t');
        $last_month_start = date('Y-m-01', strtotime('-1 month'));
        $last_month_end = date('Y-m-t', strtotime('-1 month'));
        
        // إيرادات الشهر الحالي
        $current_query = $this->db->query("
            SELECT COALESCE(SUM(total), 0) as revenue
            FROM " . DB_PREFIX . "order 
            WHERE date_added BETWEEN '" . $current_month_start . " 00:00:00' 
            AND '" . $current_month_end . " 23:59:59'
            AND order_status_id > 0
        ");
        
        // إيرادات الشهر الماضي
        $last_query = $this->db->query("
            SELECT COALESCE(SUM(total), 0) as revenue
            FROM " . DB_PREFIX . "order 
            WHERE date_added BETWEEN '" . $last_month_start . " 00:00:00' 
            AND '" . $last_month_end . " 23:59:59'
            AND order_status_id > 0
        ");
        
        $current_revenue = (float)$current_query->row['revenue'];
        $last_revenue = (float)$last_query->row['revenue'];
        
        $growth_percentage = $last_revenue > 0 ? (($current_revenue - $last_revenue) / $last_revenue) * 100 : 0;
        
        return [
            'current_month' => $current_revenue,
            'last_month' => $last_revenue,
            'growth_percentage' => round($growth_percentage, 2),
            'growth_amount' => $current_revenue - $last_revenue
        ];
    }
    
    /**
     * أداء المتجر الإلكتروني
     */
    public function getOnlineRevenue() {
        $today = date('Y-m-d');
        
        $query = $this->db->query("
            SELECT 
                COALESCE(SUM(total), 0) as revenue,
                COUNT(*) as orders,
                AVG(total) as avg_order_value
            FROM " . DB_PREFIX . "order 
            WHERE DATE(date_added) = '" . $today . "' 
            AND order_status_id > 0
            AND store_id > 0  -- طلبات المتجر الإلكتروني
        ");
        
        return $query->row;
    }
    
    /**
     * أداء نقاط البيع
     */
    public function getPOSRevenue() {
        $today = date('Y-m-d');
        
        $query = $this->db->query("
            SELECT 
                COALESCE(SUM(pt.amount), 0) as revenue,
                COUNT(*) as transactions,
                AVG(pt.amount) as avg_transaction_value,
                COUNT(DISTINCT pt.terminal_id) as active_terminals
            FROM " . DB_PREFIX . "pos_transaction pt
            JOIN " . DB_PREFIX . "pos_shift ps ON pt.shift_id = ps.shift_id
            WHERE DATE(pt.transaction_date) = '" . $today . "'
            AND pt.transaction_type = 'sale'
        ");
        
        return $query->row;
    }
    
    /**
     * مقارنة أداء الفروع
     */
    public function getBranchComparison() {
        $today = date('Y-m-d');
        
        $query = $this->db->query("
            SELECT 
                b.branch_id,
                b.name as branch_name,
                COALESCE(SUM(pt.amount), 0) as revenue,
                COUNT(pt.transaction_id) as transactions,
                COUNT(DISTINCT ps.user_id) as active_cashiers
            FROM " . DB_PREFIX . "branch b
            LEFT JOIN " . DB_PREFIX . "pos_terminal pterm ON b.branch_id = pterm.branch_id
            LEFT JOIN " . DB_PREFIX . "pos_shift ps ON pterm.terminal_id = ps.terminal_id
            LEFT JOIN " . DB_PREFIX . "pos_transaction pt ON ps.shift_id = pt.shift_id 
                AND DATE(pt.transaction_date) = '" . $today . "'
                AND pt.transaction_type = 'sale'
            WHERE b.status = 1
            GROUP BY b.branch_id, b.name
            ORDER BY revenue DESC
        ");
        
        return $query->rows;
    }
    
    /**
     * حالة المخزون المنخفض
     */
    public function getLowStockAlerts() {
        $query = $this->db->query("
            SELECT 
                p.product_id,
                pd.name,
                p.model,
                p.quantity,
                p.minimum,
                (p.minimum - p.quantity) as shortage,
                p.price,
                (p.minimum - p.quantity) * p.price as shortage_value
            FROM " . DB_PREFIX . "product p
            JOIN " . DB_PREFIX . "product_description pd ON p.product_id = pd.product_id
            WHERE p.quantity <= p.minimum 
            AND p.status = 1
            AND pd.language_id = " . (int)$this->config->get('config_language_id') . "
            ORDER BY shortage DESC
            LIMIT 20
        ");
        
        return $query->rows;
    }
    
    /**
     * المنتجات الأكثر مبيعاً
     */
    public function getTopProducts($limit = 10) {
        $start_date = date('Y-m-01'); // بداية الشهر
        
        $query = $this->db->query("
            SELECT 
                p.product_id,
                pd.name,
                p.model,
                SUM(op.quantity) as total_sold,
                SUM(op.total) as total_revenue,
                AVG(op.price) as avg_price,
                p.quantity as current_stock
            FROM " . DB_PREFIX . "order_product op
            JOIN " . DB_PREFIX . "order o ON op.order_id = o.order_id
            JOIN " . DB_PREFIX . "product p ON op.product_id = p.product_id
            JOIN " . DB_PREFIX . "product_description pd ON p.product_id = pd.product_id
            WHERE o.date_added >= '" . $start_date . "'
            AND o.order_status_id > 0
            AND pd.language_id = " . (int)$this->config->get('config_language_id') . "
            GROUP BY p.product_id, pd.name, p.model, p.quantity
            ORDER BY total_sold DESC
            LIMIT " . (int)$limit
        );
        
        return $query->rows;
    }
    
    /**
     * الموافقات المعلقة
     */
    public function getPendingPurchaseOrders() {
        $query = $this->db->query("
            SELECT 
                po.purchase_order_id,
                po.reference,
                po.total,
                po.date_added,
                s.name as supplier_name,
                DATEDIFF(NOW(), po.date_added) as days_pending
            FROM " . DB_PREFIX . "purchase_order po
            JOIN " . DB_PREFIX . "supplier s ON po.supplier_id = s.supplier_id
            WHERE po.status = 'pending_approval'
            ORDER BY po.date_added ASC
            LIMIT 10
        ");
        
        return $query->rows;
    }
    
    /**
     * تحليل العملاء الجدد
     */
    public function getNewCustomers($days = 30) {
        $start_date = date('Y-m-d', strtotime('-' . $days . ' days'));
        
        $query = $this->db->query("
            SELECT 
                COUNT(*) as total_new,
                COUNT(CASE WHEN DATE(date_added) = CURDATE() THEN 1 END) as today,
                COUNT(CASE WHEN DATE(date_added) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) THEN 1 END) as this_week
            FROM " . DB_PREFIX . "customer 
            WHERE date_added >= '" . $start_date . "'
        ");
        
        return $query->row;
    }
    
    /**
     * أفضل العملاء
     */
    public function getTopCustomers($limit = 10) {
        $query = $this->db->query("
            SELECT 
                c.customer_id,
                CONCAT(c.firstname, ' ', c.lastname) as customer_name,
                c.email,
                COUNT(o.order_id) as total_orders,
                SUM(o.total) as total_spent,
                AVG(o.total) as avg_order_value,
                MAX(o.date_added) as last_order_date
            FROM " . DB_PREFIX . "customer c
            JOIN " . DB_PREFIX . "order o ON c.customer_id = o.customer_id
            WHERE o.order_status_id > 0
            GROUP BY c.customer_id, customer_name, c.email
            ORDER BY total_spent DESC
            LIMIT " . (int)$limit
        );
        
        return $query->rows;
    }
    
    /**
     * تحليل ABC للمنتجات
     */
    public function getABCAnalysis() {
        $query = $this->db->query("
            SELECT 
                abc_category,
                COUNT(*) as product_count,
                SUM(annual_revenue) as total_revenue,
                AVG(annual_revenue) as avg_revenue
            FROM " . DB_PREFIX . "inventory_abc_analysis
            WHERE analysis_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
            GROUP BY abc_category
            ORDER BY 
                CASE abc_category 
                    WHEN 'A' THEN 1 
                    WHEN 'B' THEN 2 
                    WHEN 'C' THEN 3 
                END
        ");
        
        return $query->rows;
    }
    
    /**
     * اتجاهات الإيرادات
     */
    public function getRevenueTrends($days = 30) {
        $start_date = date('Y-m-d', strtotime('-' . $days . ' days'));
        
        $query = $this->db->query("
            SELECT 
                DATE(date_added) as date,
                SUM(total) as revenue,
                COUNT(*) as orders
            FROM " . DB_PREFIX . "order 
            WHERE date_added >= '" . $start_date . "'
            AND order_status_id > 0
            GROUP BY DATE(date_added)
            ORDER BY date ASC
        ");
        
        return $query->rows;
    }
    
    /**
     * معدل التحويل للمتجر الإلكتروني
     */
    public function getConversionRate() {
        $today = date('Y-m-d');
        
        // عدد الزوار (تقريبي من جلسات العملاء)
        $visitors_query = $this->db->query("
            SELECT COUNT(DISTINCT session_id) as visitors
            FROM " . DB_PREFIX . "cart 
            WHERE DATE(date_added) = '" . $today . "'
        ");
        
        // عدد الطلبات
        $orders_query = $this->db->query("
            SELECT COUNT(*) as orders
            FROM " . DB_PREFIX . "order 
            WHERE DATE(date_added) = '" . $today . "'
            AND order_status_id > 0
        ");
        
        $visitors = (int)$visitors_query->row['visitors'];
        $orders = (int)$orders_query->row['orders'];
        
        $conversion_rate = $visitors > 0 ? ($orders / $visitors) * 100 : 0;
        
        return [
            'visitors' => $visitors,
            'orders' => $orders,
            'conversion_rate' => round($conversion_rate, 2)
        ];
    }
    
    /**
     * السلات المهجورة
     */
    public function getAbandonedCarts() {
        $query = $this->db->query("
            SELECT 
                COUNT(*) as total_abandoned,
                SUM(total_value) as total_value,
                AVG(total_value) as avg_value
            FROM " . DB_PREFIX . "abandoned_cart 
            WHERE status = 'active'
            AND last_activity >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        ");
        
        return $query->row;
    }
    
    /**
     * إجمالي قيمة المخزون
     */
    public function getTotalInventoryValue() {
        $query = $this->db->query("
            SELECT 
                SUM(p.quantity * p.cost) as total_value,
                COUNT(*) as total_products,
                SUM(p.quantity) as total_quantity
            FROM " . DB_PREFIX . "product p
            WHERE p.status = 1
        ");
        
        return $query->row;
    }
    
    /**
     * عدد المنتجات منخفضة المخزون
     */
    public function getLowStockCount() {
        $query = $this->db->query("
            SELECT COUNT(*) as count
            FROM " . DB_PREFIX . "product 
            WHERE quantity <= minimum 
            AND status = 1
        ");
        
        return (int)$query->row['count'];
    }
    
    /**
     * عدد المنتجات نفدت من المخزون
     */
    public function getOutOfStockCount() {
        $query = $this->db->query("
            SELECT COUNT(*) as count
            FROM " . DB_PREFIX . "product 
            WHERE quantity <= 0 
            AND status = 1
        ");
        
        return (int)$query->row['count'];
    }
    
    /**
     * المنتجات سريعة الحركة
     */
    public function getFastMovingProducts($limit = 5) {
        $start_date = date('Y-m-d', strtotime('-30 days'));
        
        $query = $this->db->query("
            SELECT 
                p.product_id,
                pd.name,
                SUM(op.quantity) as total_sold,
                p.quantity as current_stock,
                (SUM(op.quantity) / 30) as daily_avg_sales
            FROM " . DB_PREFIX . "order_product op
            JOIN " . DB_PREFIX . "order o ON op.order_id = o.order_id
            JOIN " . DB_PREFIX . "product p ON op.product_id = p.product_id
            JOIN " . DB_PREFIX . "product_description pd ON p.product_id = pd.product_id
            WHERE o.date_added >= '" . $start_date . "'
            AND o.order_status_id > 0
            AND pd.language_id = " . (int)$this->config->get('config_language_id') . "
            GROUP BY p.product_id, pd.name, p.quantity
            ORDER BY daily_avg_sales DESC
            LIMIT " . (int)$limit
        );
        
        return $query->rows;
    }
    
    /**
     * المنتجات بطيئة الحركة
     */
    public function getSlowMovingProducts($limit = 5) {
        $start_date = date('Y-m-d', strtotime('-90 days'));
        
        $query = $this->db->query("
            SELECT 
                p.product_id,
                pd.name,
                p.quantity as current_stock,
                p.cost,
                (p.quantity * p.cost) as stock_value,
                COALESCE(SUM(op.quantity), 0) as total_sold
            FROM " . DB_PREFIX . "product p
            JOIN " . DB_PREFIX . "product_description pd ON p.product_id = pd.product_id
            LEFT JOIN " . DB_PREFIX . "order_product op ON p.product_id = op.product_id
            LEFT JOIN " . DB_PREFIX . "order o ON op.order_id = o.order_id 
                AND o.date_added >= '" . $start_date . "'
                AND o.order_status_id > 0
            WHERE p.status = 1
            AND p.quantity > 0
            AND pd.language_id = " . (int)$this->config->get('config_language_id') . "
            GROUP BY p.product_id, pd.name, p.quantity, p.cost
            HAVING total_sold <= 1
            ORDER BY stock_value DESC
            LIMIT " . (int)$limit
        );
        
        return $query->rows;
    }
}