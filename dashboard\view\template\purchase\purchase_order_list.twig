{{ header }}
{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="button" id="refresh-list" class="btn btn-default" onclick="OrderManager.loadOrders(1);">
          <i class="fa fa-refresh"></i> {{ text_refresh }}
        </button>
        {% if can_add %}
        <button type="button" id="add-order" class="btn btn-primary" onclick="OrderManager.addOrder();">
          <i class="fa fa-plus"></i> {{ button_add_po }}
        </button>
        {% endif %}
      </div>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>

  <div class="container-fluid">
    <!-- التنبيهات -->
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible fade in">
      <i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert" aria-label="Close">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    {% endif %}

    {% if success %}
    <div class="alert alert-success alert-dismissible fade in">
      <i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert" aria-label="Close">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    {% endif %}

    <!-- إحصائيات لوحة المعلومات -->
    <div class="row statistics-container">
      <div class="col-lg-3 col-md-6 col-sm-6">
        <div class="panel panel-primary">
          <div class="panel-heading">
            <h3 class="panel-title text-center"><i class="fa fa-file-text-o"></i> {{ text_total_orders }}</h3>
          </div>
          <div class="panel-body text-center">
            <h2><strong id="stats-total">{{ stats.total }}</strong></h2>
          </div>
        </div>
      </div>

      <div class="col-lg-3 col-md-6 col-sm-6">
        <div class="panel panel-warning">
          <div class="panel-heading">
            <h3 class="panel-title text-center"><i class="fa fa-clock-o"></i> {{ text_pending_orders }}</h3>
          </div>
          <div class="panel-body text-center">
            <h2><strong id="stats-pending">{{ stats.pending }}</strong></h2>
          </div>
        </div>
      </div>

      <div class="col-lg-3 col-md-6 col-sm-6">
        <div class="panel panel-success">
          <div class="panel-heading">
            <h3 class="panel-title text-center"><i class="fa fa-check-circle"></i> {{ text_approved_orders }}</h3>
          </div>
          <div class="panel-body text-center">
            <h2><strong id="stats-approved">{{ stats.approved }}</strong></h2>
          </div>
        </div>
      </div>

      <div class="col-lg-3 col-md-6 col-sm-6">
        <div class="panel panel-info">
          <div class="panel-heading">
            <h3 class="panel-title text-center"><i class="fa fa-truck"></i> {{ text_received_orders }}</h3>
          </div>
          <div class="panel-body text-center">
            <h2><strong id="stats-received">{{ stats.received }}</strong></h2>
          </div>
        </div>
      </div>
    </div>

    <!-- فلاتر البحث -->
    <div class="panel panel-default filter-panel">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-filter"></i> {{ text_filter }}</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-sm-3">
            <div class="form-group">
              <label for="filter-po-number" class="control-label">{{ column_po_number }}</label>
              <select id="filter-po-number" class="form-control select2"></select>
            </div>
          </div>

          <div class="col-sm-3">
            <div class="form-group">
              <label for="filter-quotation" class="control-label">{{ text_quotation }}</label>
              <select id="filter-quotation" class="form-control select2"></select>
            </div>
          </div>

          <div class="col-sm-3">
            <div class="form-group">
              <label for="filter-supplier" class="control-label">{{ column_supplier }}</label>
              <select id="filter-supplier" class="form-control select2">
                <option value="">{{ text_all_suppliers }}</option>
                {% for supplier in suppliers %}
                <option value="{{ supplier.supplier_id }}">{{ supplier.name }}</option>
                {% endfor %}
              </select>
            </div>
          </div>

          <div class="col-sm-3">
            <div class="form-group">
              <label for="filter-status" class="control-label">{{ column_status }}</label>
              <select id="filter-status" class="form-control">
                <option value="">{{ text_all_statuses }}</option>
                {% for status in status_options %}
                <option value="{{ status.value }}">{{ status.text }}</option>
                {% endfor %}
              </select>
            </div>
          </div>
        </div>

        <div class="row">
          <div class="col-sm-4">
            <div class="form-group">
              <label for="filter-date-start" class="control-label">{{ text_date_start }}</label>
              <div class="input-group date">
                <input type="text" name="filter_date_start" placeholder="{{ text_date_start }}" id="filter-date-start" class="form-control" data-date-format="YYYY-MM-DD">
                <span class="input-group-btn">
                  <button type="button" class="btn btn-default"><i class="fa fa-calendar"></i></button>
                </span>
              </div>
            </div>
          </div>

          <div class="col-sm-4">
            <div class="form-group">
              <label for="filter-date-end" class="control-label">{{ text_date_end }}</label>
              <div class="input-group date">
                <input type="text" name="filter_date_end" placeholder="{{ text_date_end }}" id="filter-date-end" class="form-control" data-date-format="YYYY-MM-DD">
                <span class="input-group-btn">
                  <button type="button" class="btn btn-default"><i class="fa fa-calendar"></i></button>
                </span>
              </div>
            </div>
          </div>

          <div class="col-sm-4">
            <div class="form-group" style="margin-top: 24px;">
              <button type="button" id="clear-filter" class="btn btn-default btn-block" onclick="OrderManager.clearFilters();">
                <i class="fa fa-eraser"></i> {{ button_clear }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- قائمة أوامر الشراء -->
    <div class="panel panel-default list-panel">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-list"></i> {{ text_list }}</h3>
      </div>
      <div class="panel-body">
        <form id="form-order" method="post">
          <div class="table-responsive">
            <table id="order-table" class="table table-bordered table-hover">
              <thead>
                <tr>
                  <th style="width: 1px;" class="text-center">
                    <input type="checkbox" onclick="$('input[name*=\'selected\']').prop('checked', this.checked);" />
                  </th>
                  <th>{{ column_po_number }}</th>
                  <th>{{ column_quotation_number }}</th>
                  <th>{{ column_supplier }}</th>
                  <th class="text-right">{{ column_total }}</th>
                  <th class="text-center">{{ column_status }}</th>
                  <th>{{ column_order_date }}</th>
                  <th>{{ column_expected_delivery }}</th>
                  <th class="text-right">{{ column_action }}</th>
                </tr>
              </thead>
              <tbody id="order-list">
                {% if orders %}
                  {% for order in orders %}
                  <tr>
                    <td class="text-center">
                      <input type="checkbox" name="selected[]" value="{{ order.po_id }}" />
                    </td>
                    <td>{{ order.po_number }}</td>
                    <td>{{ order.quotation_number }}</td>
                    <td>{{ order.supplier_name }}</td>
                    <td class="text-right">{{ order.total_formatted }}</td>
                    <td class="text-center"><span class="label label-{{ order.status_class }}">{{ order.status_text }}</span></td>
                    <td>{{ order.order_date }}</td>
                    <td>{{ order.expected_delivery_date }}</td>
                    <td class="text-right">
                      <div class="btn-group" role="group">
                        {% if order.can_view %}
                        <button type="button" class="btn btn-info btn-sm" onclick="OrderManager.viewOrder({{ order.po_id }});" data-toggle="tooltip" title="{{ button_view }}">
                          <i class="fa fa-eye"></i>
                        </button>
                        {% endif %}

                        {% if order.can_edit %}
                        <button type="button" class="btn btn-primary btn-sm" onclick="OrderManager.editOrder({{ order.po_id }});" data-toggle="tooltip" title="{{ button_edit }}">
                          <i class="fa fa-pencil"></i>
                        </button>
                        {% endif %}

                        {% if order.can_approve %}
                        <button type="button" class="btn btn-success btn-sm" onclick="OrderManager.approveOrder({{ order.po_id }});" data-toggle="tooltip" title="{{ button_approve }}">
                          <i class="fa fa-check"></i>
                        </button>
                        {% endif %}

                        {% if order.can_reject %}
                        <button type="button" class="btn btn-warning btn-sm" onclick="OrderManager.rejectOrder({{ order.po_id }});" data-toggle="tooltip" title="{{ button_reject }}">
                          <i class="fa fa-times"></i>
                        </button>
                        {% endif %}

                        {% if order.can_print %}
                        <button type="button" class="btn btn-default btn-sm" onclick="OrderManager.printOrder({{ order.po_id }});" data-toggle="tooltip" title="{{ button_print }}">
                          <i class="fa fa-print"></i>
                        </button>
                        {% endif %}

                        {% if order.can_create_receipt %}
                        <button type="button" class="btn btn-info btn-sm" onclick="OrderManager.createReceipt({{ order.po_id }});" data-toggle="tooltip" title="{{ button_create_receipt }}">
                          <i class="fa fa-truck"></i>
                        </button>
                        {% endif %}

                        {% if order.can_match %}
                        <button type="button" class="btn btn-success btn-sm" onclick="OrderManager.matchOrder({{ order.po_id }});" data-toggle="tooltip" title="{{ button_match }}">
                          <i class="fa fa-balance-scale"></i>
                        </button>
                        {% endif %}

                        {% if order.can_delete %}
                        <button type="button" class="btn btn-danger btn-sm" onclick="OrderManager.deleteOrder({{ order.po_id }});" data-toggle="tooltip" title="{{ button_delete }}">
                          <i class="fa fa-trash"></i>
                        </button>
                        {% endif %}
                      </div>
                    </td>
                  </tr>
                  {% endfor %}
                {% else %}
                  <tr>
                    <td class="text-center" colspan="9">{{ text_no_results }}</td>
                  </tr>
                {% endif %}
              </tbody>
            </table>
          </div>
        </form>

        <!-- الإجراءات الجماعية -->
        <div class="row">
          <div class="col-sm-6">
            <div class="form-inline bulk-actions">
              <div class="form-group">
                <select id="bulk-action" class="form-control">
                  <option value="">{{ text_bulk_action }}</option>
                  {% if can_approve %}
                  <option value="approve">{{ text_approve_selected }}</option>
                  {% endif %}
                  {% if can_reject %}
                  <option value="reject">{{ text_reject_selected }}</option>
                  {% endif %}
                  {% if can_delete %}
                  <option value="delete">{{ text_delete_selected }}</option>
                  {% endif %}
                </select>
              </div>
              <button type="button" id="bulk-action-apply" class="btn btn-primary" onclick="OrderManager.executeBulkAction();">
                <i class="fa fa-check"></i> {{ button_apply }}
              </button>
            </div>
          </div>
          <div class="col-sm-6 text-right">
            <div class="btn-group">
              <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                <i class="fa fa-download"></i> {{ button_export }} <span class="caret"></span>
              </button>
              <ul class="dropdown-menu dropdown-menu-right">
                <li>
                  <a href="javascript:void(0);" onclick="OrderManager.exportOrders('excel');">
                    <i class="fa fa-file-excel-o"></i> {{ text_export_excel }}
                  </a>
                </li>
                <li>
                  <a href="javascript:void(0);" onclick="OrderManager.exportOrders('pdf');">
                    <i class="fa fa-file-pdf-o"></i> {{ text_export_pdf }}
                  </a>
                </li>
              </ul>
            </div>
          </div>
        </div>

        <!-- الترقيم -->
        <div class="row">
          <div class="col-sm-6 text-left">{{ pagination }}</div>
          <div class="col-sm-6 text-right pagination-info">{{ results }}</div>
        </div>

        <!-- دليل الاستخدام -->
        <div class="well help-section" id="help-section" style="display: none; margin-top: 30px;">
          <h3><i class="fa fa-question-circle"></i> دليل استخدام نظام أوامر الشراء</h3>

          <div class="panel-group" id="help-accordion">
            <!-- قسم المقدمة -->
            <div class="panel panel-default">
              <div class="panel-heading">
                <h4 class="panel-title">
                  <a data-toggle="collapse" data-parent="#help-accordion" href="#help-intro">
                    <i class="fa fa-info-circle"></i> مقدمة ونظرة عامة
                  </a>
                </h4>
              </div>
              <div id="help-intro" class="panel-collapse collapse in">
                <div class="panel-body">
                  <p>نظام أوامر الشراء هو جزء من نظام المشتريات الشامل، ويوفر إدارة كاملة لعملية طلب واعتماد وتنفيذ أوامر الشراء للموردين. يتيح النظام للمستخدمين:</p>
                  <ul>
                    <li>إنشاء أوامر شراء جديدة مباشرة أو استناداً إلى عروض الأسعار المعتمدة</li>
                    <li>إضافة وإدارة بنود أوامر الشراء والمرفقات</li>
                    <li>متابعة دورة حياة أمر الشراء من الإنشاء وحتى الاستلام والمطابقة</li>
                    <li>إنشاء إذن استلام كامل أو جزئي للبضائع</li>
                    <li>إجراء مطابقة ثلاثية بين أمر الشراء والاستلام وفواتير الموردين</li>
                    <li>تصدير بيانات أوامر الشراء إلى Excel أو PDF</li>
                  </ul>
                </div>
              </div>
            </div>

            <!-- قسم دورة العمل -->
            <div class="panel panel-default">
              <div class="panel-heading">
                <h4 class="panel-title">
                  <a data-toggle="collapse" data-parent="#help-accordion" href="#help-workflow" class="collapsed">
                    <i class="fa fa-random"></i> دورة عمل أوامر الشراء
                  </a>
                </h4>
              </div>
              <div id="help-workflow" class="panel-collapse collapse">
                <div class="panel-body">
                  <div class="row">
                    <div class="col-md-12">
                      <div class="workflow-visualization">
                        <div class="workflow-step">
                          <div class="step-icon"><i class="fa fa-pencil"></i></div>
                          <div class="step-label">مسودة</div>
                          <div class="step-desc">في هذه المرحلة يمكن إنشاء وتعديل أمر الشراء قبل إرساله للاعتماد.</div>
                        </div>
                        <div class="workflow-arrow"><i class="fa fa-arrow-right"></i></div>
                        <div class="workflow-step">
                          <div class="step-icon"><i class="fa fa-clock-o"></i></div>
                          <div class="step-label">معلق</div>
                          <div class="step-desc">أمر الشراء في انتظار الاعتماد من قبل المسؤول المختص.</div>
                        </div>
                        <div class="workflow-arrow"><i class="fa fa-arrow-right"></i></div>
                        <div class="workflow-step">
                          <div class="step-icon"><i class="fa fa-check"></i></div>
                          <div class="step-label">معتمد</div>
                          <div class="step-desc">تمت الموافقة على أمر الشراء ويمكن إرساله للمورد.</div>
                        </div>
                        <div class="workflow-arrow"><i class="fa fa-arrow-right"></i></div>
                        <div class="workflow-step">
                          <div class="step-icon"><i class="fa fa-truck"></i></div>
                          <div class="step-label">مستلم</div>
                          <div class="step-desc">تم استلام المنتجات المطلوبة كلياً أو جزئياً.</div>
                        </div>
                        <div class="workflow-arrow"><i class="fa fa-arrow-right"></i></div>
                        <div class="workflow-step">
                          <div class="step-icon"><i class="fa fa-check-circle"></i></div>
                          <div class="step-label">مكتمل</div>
                          <div class="step-desc">تم إغلاق أمر الشراء بعد استلام كافة المنتجات وإتمام المدفوعات.</div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="alert alert-info margin-top-15">
                    <i class="fa fa-info-circle"></i> يمكن أيضاً رفض أمر الشراء في أي مرحلة، ويتحول إلى حالة <span class="label label-danger">مرفوض</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- قسم إنشاء أمر الشراء -->
            <div class="panel panel-default">
              <div class="panel-heading">
                <h4 class="panel-title">
                  <a data-toggle="collapse" data-parent="#help-accordion" href="#help-create" class="collapsed">
                    <i class="fa fa-plus-circle"></i> إنشاء أمر شراء جديد
                  </a>
                </h4>
              </div>
              <div id="help-create" class="panel-collapse collapse">
                <div class="panel-body">
                  <ol>
                    <li>انقر على زر <button class="btn btn-primary btn-xs"><i class="fa fa-plus"></i> {{ button_add_po }}</button> في الصفحة الرئيسية لأوامر الشراء.</li>
                    <li>اختر طريقة الإنشاء:
                      <ul>
                        <li><strong>مباشر:</strong> إنشاء أمر شراء جديد من البداية.</li>
                        <li><strong>من عرض سعر:</strong> تحويل عرض سعر معتمد إلى أمر شراء.</li>
                      </ul>
                    </li>
                    <li>إذا اخترت الإنشاء المباشر، قم بتحديد المورد وإدخال التفاصيل الأساسية.</li>
                    <li>أضف بنود أمر الشراء مع تحديد المنتجات والكميات والأسعار.</li>
                    <li>يمكنك إضافة مرفقات مثل المواصفات الفنية أو مستندات أخرى.</li>
                    <li>أخيراً، انقر على <button class="btn btn-success btn-xs"><i class="fa fa-save"></i> {{ text_save_as_draft }}</button> لحفظ أمر الشراء كمسودة، أو <button class="btn btn-primary btn-xs"><i class="fa fa-save"></i> {{ text_submit }}</button> لإرساله للاعتماد.</li>
                  </ol>

                  <div class="alert alert-warning">
                    <i class="fa fa-exclamation-triangle"></i> <strong>ملاحظة:</strong> تأكد من إدخال جميع البيانات المطلوبة بشكل صحيح. يجب أن يحتوي أمر الشراء على بند واحد على الأقل مع كمية وسعر صالحين.
                  </div>
                </div>
              </div>
            </div>

            <!-- قسم الاستلام -->
            <div class="panel panel-default">
              <div class="panel-heading">
                <h4 class="panel-title">
                  <a data-toggle="collapse" data-parent="#help-accordion" href="#help-receipt" class="collapsed">
                    <i class="fa fa-truck"></i> استلام البضائع
                  </a>
                </h4>
              </div>
              <div id="help-receipt" class="panel-collapse collapse">
                <div class="panel-body">
                  <p>عندما يتم استلام البضائع المطلوبة، يمكنك إنشاء إذن استلام لتسجيل الكميات والتفاصيل:</p>

                  <ol>
                    <li>انقر على زر <button class="btn btn-info btn-xs"><i class="fa fa-truck"></i></button> في صف أمر الشراء المراد استلام بضائعه.</li>
                    <li>حدد المخزن/الفرع الذي سيتم الاستلام فيه.</li>
                    <li>أدخل تاريخ الاستلام ورقم فاتورة المورد (إن وجدت).</li>
                    <li>لكل بند في أمر الشراء، أدخل الكمية المستلمة فعلياً.</li>
                    <li>إذا كان الاستلام جزئياً، أدخل الكميات المستلمة فقط وترك الباقي للاستلام اللاحق.</li>
                    <li>إذا كان هناك حاجة لفحص الجودة، حدد خيار "فحص الجودة مطلوب".</li>
                    <li>انقر على "حفظ" لتسجيل الاستلام.</li>
                  </ol>

                  <div class="alert alert-info">
                    <i class="fa fa-lightbulb-o"></i> <strong>مهم:</strong> عند استلام البضائع، يتم تحديث المخزون تلقائياً وحساب التكلفة المتوسطة المرجحة للمنتجات.
                  </div>
                </div>
              </div>
            </div>

            <!-- قسم المطابقة -->
            <div class="panel panel-default">
              <div class="panel-heading">
                <h4 class="panel-title">
                  <a data-toggle="collapse" data-parent="#help-accordion" href="#help-matching" class="collapsed">
                    <i class="fa fa-balance-scale"></i> المطابقة الثلاثية
                  </a>
                </h4>
              </div>
              <div id="help-matching" class="panel-collapse collapse">
                <div class="panel-body">
                  <p>المطابقة الثلاثية هي عملية مقارنة ومطابقة بين أمر الشراء وإذن الاستلام وفاتورة المورد للتأكد من تطابق الكميات والأسعار:</p>

                  <ol>
                    <li>انقر على زر <button class="btn btn-success btn-xs"><i class="fa fa-balance-scale"></i></button> في صف أمر الشراء المراد مطابقته.</li>
                    <li>ستظهر شاشة المطابقة التي تعرض تفاصيل أمر الشراء والاستلام والفواتير.</li>
                    <li>قم بفحص أي فروقات في الكميات أو الأسعار بين المستندات الثلاثة.</li>
                    <li>إذا كانت هناك فروقات، أدخل سبب الفروقات وكيفية معالجتها.</li>
                    <li>انقر على "حفظ المطابقة" لتسجيل نتيجة المطابقة.</li>
                  </ol>

                  <div class="alert alert-warning">
                    <i class="fa fa-exclamation-triangle"></i> <strong>تنبيه:</strong> في حالة وجود فروقات كبيرة، قد تحتاج إلى مراجعة الإدارة المالية أو المخزون قبل الاعتماد النهائي.
                  </div>
                </div>
              </div>
            </div>
          </div>

          <button type="button" class="btn btn-default margin-top-15" onclick="$('#help-section').slideUp();">{{ button_close }}</button>
        </div>

        <div style="text-align: center; margin: 30px 0 15px;">
          <button type="button" class="btn btn-info btn-lg" onclick="$('#help-section').slideToggle();">
            <i class="fa fa-question-circle"></i> دليل استخدام نظام أوامر الشراء
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- طبقة التحميل -->
<div id="loading-overlay">
  <div class="loading-spinner">
    <i class="fa fa-spinner fa-spin fa-3x fa-fw"></i>
    <span class="sr-only">{{ text_loading }}</span>
  </div>
</div>

<!-- نافذة نموذج أمر الشراء -->
<div class="modal fade" id="modal-order-form" tabindex="-1" role="dialog" aria-labelledby="modal-order-form-label">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <!-- سيتم تحميله عبر AJAX -->
    </div>
  </div>
</div>

<!-- نافذة عرض أمر الشراء -->
<div class="modal fade" id="modal-order-view" tabindex="-1" role="dialog" aria-labelledby="modal-order-view-label">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <!-- سيتم تحميله عبر AJAX -->
    </div>
  </div>
</div>

<!-- نافذة إنشاء إذن استلام -->
<div class="modal fade" id="modal-receipt-form" tabindex="-1" role="dialog" aria-labelledby="modal-receipt-form-label">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <!-- سيتم تحميله عبر AJAX -->
    </div>
  </div>
</div>

<!-- نافذة المطابقة الثلاثية -->
<div class="modal fade" id="modal-matching" tabindex="-1" role="dialog" aria-labelledby="modal-matching-label">
  <div class="modal-dialog modal-xl" role="document">
    <div class="modal-content">
      <!-- سيتم تحميله عبر AJAX -->
    </div>
  </div>
</div>

<!-- قسم عرض المستندات -->
<div class="modal-body tab-content">
  <div class="tab-pane" id="documents-tab">
    <div class="row">
      <div class="col-md-12">
        <div class="documents-section">
          <!-- منطقة سحب وإفلات الملفات -->
          <div id="file-drop-zone" class="file-drop-zone">
            <div class="drop-zone-message">
              <i class="fa fa-cloud-upload fa-3x"></i>
              <p>{{ text_drop_files_here }} {{ text_or }}</p>
              <button type="button" id="browse-files" class="btn btn-primary">{{ button_browse }}</button>
              <input type="file" id="document-upload" style="display: none;" multiple>
            </div>
          </div>

          <!-- قائمة الملفات المحددة للتحميل -->
          <div id="upload-file-list" style="display: none;">
            <h4>{{ text_selected_files }}</h4>
            <table class="table table-bordered table-striped">
              <thead>
                <tr>
                  <th>{{ column_file_name }}</th>
                  <th>{{ column_file_size }}</th>
                  <th>{{ column_document_type }}</th>
                  <th>{{ column_action }}</th>
                </tr>
              </thead>
              <tbody id="file-queue"></tbody>
            </table>
            <button type="button" id="upload-all-documents" class="btn btn-success">
              <i class="fa fa-upload"></i> {{ button_upload_all }}
            </button>
          </div>

          <!-- قائمة المستندات المحملة -->
          <div class="uploaded-documents-section">
            <h4>{{ text_uploaded_documents }}</h4>
            <div id="no-documents-message" style="display: none;">
              <div class="alert alert-info">{{ text_no_documents }}</div>
            </div>

            <!-- عرض المستندات كمصغرات -->
            <div id="document-preview-container"></div>

            <!-- عرض المستندات كجدول -->
            <table class="table table-bordered table-striped">
              <thead>
                <tr>
                  <th>{{ column_document_name }}</th>
                  <th>{{ column_document_type }}</th>
                  <th>{{ column_uploaded_by }}</th>
                  <th>{{ column_upload_date }}</th>
                  <th class="text-right">{{ column_action }}</th>
                </tr>
              </thead>
              <tbody id="documents-list"></tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- قالب صف العناصر -->
<template id="item-row-template">
<tr class="item-row">
  <input type="hidden" name="item[po_item_id][]" value="{{ item.po_item_id|default(0) }}">
  <input type="hidden" name="item[quotation_item_id][]" value="{{ item.quotation_item_id|default(0) }}">
  <input type="hidden" name="item[requisition_item_id][]" value="{{ item.requisition_item_id|default(0) }}">
  <td>
    <select class="form-control select2-product" name="item[product_id][]">
      <option value="">{{ text_select_product }}</option>
      {% if item.product_id %}
        <option value="{{ item.product_id }}" selected>{{ item.product_name }}</option>
      {% endif %}
    </select>
    <input type="hidden" name="item_product_validator[]" class="product-validator" value="">
    <div class="product-details mt-2"></div>
  </td>
  <td>
    <input type="number" name="item[quantity][]" min="0.01" step="0.01" class="form-control calc-trigger item-quantity" value="1">
    <select name="item[unit_id][]" class="form-control unit-select mt-2" required></select>
  </td>
  <td>
    <input type="number" name="item[unit_price][]" min="0" step="0.01" class="form-control calc-trigger item-unit-price" value="0">
    <input type="hidden" name="item[original_unit_price][]" value="0">
  </td>
  <td>
    <select name="item[discount_type][]" class="form-control calc-trigger item-discount-type">
      <option value="fixed">{{ text_fixed }}</option>
      <option value="percentage">{{ text_percentage }}</option>
    </select>
    <input type="number" name="item[discount_rate][]" min="0" step="0.01" class="form-control calc-trigger item-discount-value mt-2" value="0">
    <input type="hidden" name="item[discount_amount][]" value="0">
  </td>
  <td>
    <input type="number" name="item[tax_rate][]" min="0" max="100" step="0.01" class="form-control calc-trigger item-tax-rate" value="{{ default_tax_rate }}">
    <input type="hidden" name="item[tax_amount][]" value="0">
  </td>
  <td>
    <input type="hidden" name="item[total_price][]" class="line-total" value="0">
    <div class="item-total">0.00</div>
  </td>
  <td>
    <textarea name="item[description][]" class="form-control item-description" rows="1"></textarea>
  </td>
  <td>
    <button type="button" class="btn btn-danger remove-item-btn">
      <i class="fa fa-trash"></i>
    </button>
  </td>
</tr>
</template>

{{footer}}

<script type="text/javascript">
// Global variables
var user_token = '{{ user_token }}';
var currentPage = 1;
var language = {
    text_no_results: '{{ text_no_results }}',
    text_confirm_approve: '{{ text_confirm_approve }}',
    text_confirm_reject: '{{ text_confirm_reject }}',
    text_confirm_delete: '{{ text_confirm_delete }}',
    text_confirm_bulk_approve: '{{ text_confirm_bulk_approve }}',
    text_confirm_bulk_reject: '{{ text_confirm_bulk_reject }}',
    text_confirm_bulk_delete: '{{ text_confirm_bulk_delete }}',
    text_enter_rejection_reason: '{{ text_enter_rejection_reason }}',
    text_select_supplier: '{{ text_select_supplier }}',
    text_select_status: '{{ text_all_statuses }}',
    text_select_quotation: '{{ text_select_quotation }}',
    error_no_selection: '{{ text_no_items_selected }}',
    error_select_action: '{{ text_select_action }}'
};

// Permissions
window.can_approve = {{ can_approve ? 'true' : 'false' }};
window.can_reject = {{ can_reject ? 'true' : 'false' }};
window.can_delete = {{ can_delete ? 'true' : 'false' }};
window.can_edit = {{ can_edit ? 'true' : 'false' }};
window.can_add = {{ can_add ? 'true' : 'false' }};
</script>

<script src="view/javascript/purchase/order_list.js"></script>

<style type="text/css">
/* أنماط محسنة لمظهر أكثر احترافية */
.statistics-container {
  margin-bottom: 20px;
}

.statistics-container .panel {
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
}

.statistics-container .panel:hover {
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
  transform: translateY(-2px);
}

.statistics-container .panel-heading {
  padding: 15px;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}

.statistics-container .panel-heading i {
  margin-right: 10px;
}

.statistics-container .panel-body {
  padding: 20px 15px;
}

.statistics-container .panel-body h2 {
  margin: 0;
  font-size: 32px;
}

.filter-panel, .list-panel {
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  margin-bottom: 20px;
}

.filter-panel .panel-heading, .list-panel .panel-heading {
  background-color: #f8f8f8;
  border-bottom: 1px solid #e7e7e7;
  padding: 12px 15px;
}

.filter-panel .panel-title, .list-panel .panel-title {
  font-size: 16px;
  font-weight: 600;
}

.filter-panel .panel-body {
  padding: 20px 15px 10px;
}

.filter-panel .form-group {
  margin-bottom: 15px;
}

.filter-panel label {
  font-weight: 600;
}

#order-table {
  margin-bottom: 15px;
}

#order-table thead th {
  background-color: #f5f5f5;
  font-weight: 600;
  vertical-align: middle;
}

#order-table .label {
  display: inline-block;
  min-width: 80px;
  text-align: center;
  padding: 5px 8px;
  font-size: 12px;
}

.btn-group .btn {
  margin-right: 2px;
}

.btn-group .btn:last-child {
  margin-right: 0;
}

.bulk-actions {
  margin-bottom: 15px;
}

.bulk-actions .form-group {
  margin-right: 10px;
}

.pagination-info {
  padding-top: 7px;
}

/* طبقة التحميل المحسنة */
#loading-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.8);
  z-index: 9999;
}

.loading-spinner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

/* أنماط النوافذ */
.modal-xl {
  width: 90%;
  max-width: 1200px;
}

.modal-body {
  max-height: calc(100vh - 200px);
  overflow-y: auto;
}

.modal-footer {
  border-top: 1px solid #e7e7e7;
  background-color: #f8f8f8;
  padding: 15px;
}

/* تنسيق صف العناصر */
.item-row td {
  vertical-align: middle !important;
}

.item-row .product-name {
  font-weight: 600;
}

.item-row .product-details {
  max-height: 200px;
  overflow-y: auto;
  margin-top: 10px;
}

.item-row .item-total {
  font-weight: 600;
  text-align: right;
}

/* تنسيق صف مقارنة المطابقة */
.matching-row {
  background-color: #f9f9f9;
}

.matching-row.mismatch {
  background-color: #fff3cd;
}

.variance-positive {
  color: #28a745;
}

.variance-negative {
  color: #dc3545;
}

/* تخصيص Select2 */
.select2-container--default .select2-selection--single {
  height: 34px;
  border: 1px solid #ccc;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
  line-height: 32px;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
  height: 32px;
}

/* تصحيحات للتجاوب */
@media (max-width: 767px) {
  .filter-panel .form-group {
    margin-bottom: 20px;
  }

  .bulk-actions {
    margin-bottom: 20px;
  }

  .btn-group .btn {
    padding: 6px 8px;
  }

  .statistics-container .panel-body h2 {
    font-size: 24px;
  }
}

.select2-container {
    width: 100%;
    display: table;
}

/* أنماط قسم دليل المساعدة */
.help-section {
  background-color: #f9f9f9;
  border-radius: 6px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  padding: 20px;
  margin-bottom: 30px;
}

.help-section h3 {
  margin-top: 0;
  color: #337ab7;
  font-weight: bold;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.help-section .panel-title {
  font-size: 16px;
  font-weight: 600;
}

.help-section .panel-title i {
  margin-right: 8px;
}

.help-section .panel-body {
  padding: 20px;
}

.margin-top-15 {
  margin-top: 15px;
}

.workflow-visualization {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  margin: 20px 0;
}

.workflow-step {
  text-align: center;
  width: 120px;
  padding: 10px;
}

.step-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #337ab7;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 10px;
  font-size: 20px;
}

.step-label {
  font-weight: bold;
  margin-bottom: 5px;
}

.step-desc {
  font-size: 12px;
  color: #666;
}

.workflow-arrow {
  font-size: 20px;
  color: #999;
  margin: 0 10px;
}

@media (max-width: 767px) {
  .workflow-visualization {
    flex-direction: column;
  }

  .workflow-step {
    width: 100%;
    margin-bottom: 20px;
  }

  .workflow-arrow {
    transform: rotate(90deg);
    margin: 10px 0;
  }
}
/* أنماط منطقة سحب وإفلات الملفات */
.file-drop-zone {
  border: 2px dashed #ccc;
  border-radius: 5px;
  padding: 25px;
  text-align: center;
  margin-bottom: 20px;
  transition: all 0.3s ease;
}

.file-drop-zone.drag-over {
  border-color: #007bff;
  background-color: rgba(0, 123, 255, 0.05);
}

.drop-zone-message {
  color: #777;
}

.drop-zone-message i {
  margin-bottom: 15px;
  color: #aaa;
}

.drop-zone-message p {
  margin-bottom: 15px;
}

/* أنماط العرض المصغر للمستندات */
.document-preview-item {
  margin-bottom: 20px;
}

.document-thumbnail {
  height: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.document-thumbnail:hover {
  border-color: #007bff;
  box-shadow: 0 0 5px rgba(0, 123, 255, 0.3);
}

.document-thumbnail img {
  max-height: 100%;
  max-width: 100%;
}

.document-icon {
  color: #6c757d;
}

.document-name {
  margin: 10px 0 5px;
  font-size: 14px;
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.document-info {
  margin-bottom: 5px;
  font-size: 12px;
  color: #6c757d;
}

.document-date {
  margin-bottom: 10px;
  color: #999;
}
/* أنماط المطابقة الثلاثية المحسنة */
.matching-row .variance-details {
    font-size: 11px;
    margin-top: 5px;
}

.matching-row .variance-item {
    display: block;
    margin-bottom: 2px;
}

.matching-info-panel .panel-heading {
    background-color: #f5f5f5;
    border-color: #ddd;
    color: #333;
}

.matching-comparison .table th {
    text-align: center;
    vertical-align: middle;
}

.matching-comparison .table .bg-primary {
    background-color: #d9edf7;
    color: #31708f;
}

.matching-comparison .table .bg-success {
    background-color: #dff0d8;
    color: #3c763d;
}

.matching-comparison .table .bg-warning {
    background-color: #fcf8e3;
    color: #8a6d3b;
}

/* أنماط تقرير PDF */
@media print {
    .no-print {
        display: none !important;
    }

    .print-only {
        display: block !important;
    }

    .matching-row.mismatch {
        background-color: #fff3cd !important;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
    }

    .matching-comparison .table .bg-primary,
    .matching-comparison .table .bg-success,
    .matching-comparison .table .bg-warning {
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
    }

    body {
        width: 100%;
        margin: 0;
        padding: 0;
    }

    table {
        page-break-inside: auto;
    }

    tr {
        page-break-inside: avoid;
        page-break-after: auto;
    }

    thead {
        display: table-header-group;
    }

    tfoot {
        display: table-footer-group;
    }
}

</style>

<script type="text/javascript">
/**
 * Order Manager - كائن لإدارة أوامر الشراء
 * يتعامل مع جميع الوظائف المتعلقة بقائمة أوامر الشراء، والتصفية، والعمليات
 */
var OrderManager = {
  user_token: '{{ user_token }}',

  /**
   * تهيئة مدير أوامر الشراء
   */
  init: function() {
    this.initializeFilters();
    this.loadOrders(1);
    this.setupEventHandlers();

    // تهيئة tooltips
    $('[data-toggle="tooltip"]').tooltip({container: 'body'});
  },

  /**
   * تهيئة عناصر التصفية
   */
  initializeFilters: function() {
    // تهيئة عناصر Select2 للقوائم المنسدلة المحسنة
    $('#filter-po-number').select2({
      placeholder: '{{ text_select }}',
      allowClear: true,
      dropdownParent: $('#filter-po-number').parent(),
      ajax: {
        url: 'index.php?route=purchase/order/ajaxSearchPO&user_token=' + this.user_token,
        dataType: 'json',
        delay: 300,
        data: function(params) {
          return {
            q: params.term || ''
          };
        },
        processResults: function(data) {
          return {
            results: data
          };
        },
        cache: true
      }
    });

    $('#filter-quotation').select2({
      placeholder: '{{ text_select_quotation }}',
      allowClear: true,
      dropdownParent: $('#filter-quotation').parent(),
      ajax: {
        url: 'index.php?route=purchase/quotation/ajaxQuotations&user_token=' + this.user_token,
        dataType: 'json',
        delay: 250,
        data: function(params) {
          return {
            q: params.term || ''
          };
        },
        processResults: function(data) {
          return {
            results: data
          };
        }
      }
    });

    $('#filter-supplier').select2({
      placeholder: '{{ text_select }}',
      allowClear: true,
      dropdownParent: $('#filter-supplier').parent()
    });

    // تهيئة منتقيات التاريخ
    $('.date').datetimepicker({
      pickTime: false,
      format: 'YYYY-MM-DD',
      useCurrent: false
    });
  },

  /**
   * إعداد معالجات الأحداث للفلاتر وعناصر التحكم الأخرى
   */
  setupEventHandlers: function() {
    // معالجة تغييرات الفلتر
    $('#filter-po-number, #filter-quotation, #filter-supplier').on('change', function() {
      OrderManager.loadOrders(1);
    });

    $('#filter-status').on('change', function() {
      OrderManager.loadOrders(1);
    });

    $('.date').on('dp.change', function() {
      OrderManager.loadOrders(1);
    });

    // معالجة تغيير حجم النافذة لميزات الاستجابة
    $(window).resize(function() {
      OrderManager.handleResponsiveLayout();
    });

    // استدعاء أولي للتخطيط المستجيب
    this.handleResponsiveLayout();
  },

  /**
   * معالجة تعديلات تخطيط الاستجابة
   */
  handleResponsiveLayout: function() {
    var windowWidth = $(window).width();

    // ضبط عرض الجدول بناءً على عرض النافذة
    if (windowWidth < 768) {
      // تحسينات عرض الجوال
      $('.btn-group .btn-sm').each(function() {
        $(this).addClass('btn-xs').removeClass('btn-sm');
      });
    } else {
      // تحسينات عرض سطح المكتب
      $('.btn-group .btn-xs').each(function() {
        $(this).addClass('btn-sm').removeClass('btn-xs');
      });
    }
  },

  /**
   * إظهار طبقة التحميل
   */
  showLoading: function() {
    $('#loading-overlay').fadeIn(200);
  },

  /**
   * إخفاء طبقة التحميل
   */
  hideLoading: function() {
    $('#loading-overlay').fadeOut(200);
  },

  /**
   * تحميل قائمة أوامر الشراء مع رقم الصفحة المحدد
   * @param {number} page - رقم الصفحة المراد تحميلها
   */
  loadOrders: function(page) {
    this.showLoading();

    $.ajax({
      url: 'index.php?route=purchase/order/ajaxList&user_token=' + this.user_token,
      type: 'GET',
      data: {
        filter_po_number: $('#filter-po-number').val() || '',
        filter_quotation_id: $('#filter-quotation').val() || '',
        filter_supplier_id: $('#filter-supplier').val() || '',
        filter_status: $('#filter-status').val() || '',
        filter_date_start: $('#filter-date-start').val() || '',
        filter_date_end: $('#filter-date-end').val() || '',
        page: page || 1
      },
      dataType: 'json',
      success: function(json) {
        OrderManager.renderOrders(json);
        OrderManager.updateStatistics(json.stats);
        OrderManager.hideLoading();
      },
      error: function(xhr, status, error) {
        toastr.error('{{ error_ajax }}');
        OrderManager.hideLoading();
      }
    });
  },

  /**
   * عرض جدول أوامر الشراء بالبيانات المقدمة
   * @param {object} json - بيانات JSON التي تحتوي على قائمة أوامر الشراء
   */
  renderOrders: function(json) {
    var html = '';

    if (json.orders && json.orders.length > 0) {
      for (var i = 0; i < json.orders.length; i++) {
        var order = json.orders[i];
        html += '<tr>';
        html += '  <td class="text-center"><input type="checkbox" name="selected[]" value="' + order.po_id + '" /></td>';
        html += '  <td>' + order.po_number + '</td>';
        html += '  <td>' + (order.quotation_number || '') + '</td>';
        html += '  <td>' + order.supplier_name + '</td>';
        html += '  <td class="text-right">' + order.total_formatted + '</td>';
        html += '  <td class="text-center"><span class="label label-' + order.status_class + '">' + order.status_text + '</span></td>';
        html += '  <td>' + order.order_date + '</td>';
        html += '  <td>' + (order.expected_delivery_date || '') + '</td>';
        html += '  <td class="text-right">';

        html += '<div class="btn-group" role="group">';

        if (order.can_view) {
          html += '<button type="button" class="btn btn-info btn-sm" onclick="OrderManager.viewOrder(' + order.po_id + ');" data-toggle="tooltip" title="{{ button_view }}"><i class="fa fa-eye"></i></button>';
        }

        if (order.can_edit) {
          html += '<button type="button" class="btn btn-primary btn-sm" onclick="OrderManager.editOrder(' + order.po_id + ');" data-toggle="tooltip" title="{{ button_edit }}"><i class="fa fa-pencil"></i></button>';
        }

        if (order.can_approve) {
          html += '<button type="button" class="btn btn-success btn-sm" onclick="OrderManager.approveOrder(' + order.po_id + ');" data-toggle="tooltip" title="{{ button_approve }}"><i class="fa fa-check"></i></button>';
        }

        if (order.can_reject) {
          html += '<button type="button" class="btn btn-warning btn-sm" onclick="OrderManager.rejectOrder(' + order.po_id + ');" data-toggle="tooltip" title="{{ button_reject }}"><i class="fa fa-times"></i></button>';
        }

        if (order.can_print) {
          html += '<button type="button" class="btn btn-default btn-sm" onclick="OrderManager.printOrder(' + order.po_id + ');" data-toggle="tooltip" title="{{ button_print }}"><i class="fa fa-print"></i></button>';
        }

        if (order.can_create_receipt) {
          html += '<button type="button" class="btn btn-info btn-sm" onclick="OrderManager.createReceipt(' + order.po_id + ');" data-toggle="tooltip" title="{{ button_create_receipt }}"><i class="fa fa-truck"></i></button>';
        }

        if (order.can_match) {
          html += '<button type="button" class="btn btn-success btn-sm" onclick="OrderManager.matchOrder(' + order.po_id + ');" data-toggle="tooltip" title="{{ button_match }}"><i class="fa fa-balance-scale"></i></button>';
        }

        if (order.can_delete) {
          html += '<button type="button" class="btn btn-danger btn-sm" onclick="OrderManager.deleteOrder(' + order.po_id + ');" data-toggle="tooltip" title="{{ button_delete }}"><i class="fa fa-trash"></i></button>';
        }

        html += '</div>';
        html += '  </td>';
        html += '</tr>';
      }
    } else {
      html += '<tr>';
      html += '  <td class="text-center" colspan="9">{{ text_no_results }}</td>';
      html += '</tr>';
    }

    $('#order-list').html(html);

    // إعادة تهيئة tooltips على الأزرار المضافة حديثاً
    $('[data-toggle="tooltip"]').tooltip({container: 'body'});

    // تحديث الترقيم
    if (json.pagination) {
      $('#pagination').html(json.pagination);

      // إضافة معالجات الأحداث لروابط الترقيم
      $('#pagination a').on('click', function(e) {
        e.preventDefault();
        var page = $(this).attr('href').match(/page=(\d+)/);
        page = page ? page[1] : 1;
        OrderManager.loadOrders(page);
      });
    }

    // تحديث معلومات الترقيم
    if (json.results) {
      $('#results').html(json.results);
    }
  },

  /**
   * تحديث إحصائيات لوحة المعلومات
   * @param {object} stats - بيانات الإحصائيات
   */
  updateStatistics: function(stats) {
    if (stats) {
      $('#stats-total').text(stats.total || 0);
      $('#stats-pending').text(stats.pending || 0);
      $('#stats-approved').text(stats.approved || 0);
      $('#stats-received').text(stats.received || 0);
    }
  },

  /**
   * مسح جميع حقول الفلاتر
   */
  clearFilters: function() {
    $('#filter-po-number').val(null).trigger('change');
    $('#filter-quotation').val(null).trigger('change');
    $('#filter-supplier').val(null).trigger('change');
    $('#filter-status').val('');
    $('#filter-date-start').val('');
    $('#filter-date-end').val('');

    this.loadOrders(1);
  },

  // ========== دوال إدارة أوامر الشراء ==========

  /**
   * فتح نافذة لإضافة أمر شراء جديد
   */
  addOrder: function() {
    this.showLoading();

    $('#modal-order-form .modal-content').load('index.php?route=purchase/order/form&user_token=' + this.user_token, function() {
      $('#modal-order-form').modal('show');

      // تهيئة مكونات النموذج بعد التحميل
      setTimeout(function() {
        // OrderManager.initializeFormComponents(); // This function is now in the form template
        OrderManager.hideLoading();
      }, 500);
    });
  },

  /**
   * فتح نافذة لتعديل أمر شراء موجود
   * @param {number} po_id - معرف أمر الشراء المراد تعديله
   */
  editOrder: function(po_id) {
    this.showLoading();

    $('#modal-order-form .modal-content').load('index.php?route=purchase/order/form&user_token=' + this.user_token + '&po_id=' + po_id, function() {
      $('#modal-order-form').modal('show');

      // تهيئة مكونات النموذج بعد التحميل
      setTimeout(function() {
        // OrderManager.initializeFormComponents(); // This function is now in the form template
        OrderManager.hideLoading();
      }, 500);
    });
  },

  /**
   * عرض تفاصيل أمر شراء
   * @param {number} po_id - معرف أمر الشراء المراد عرضه
   */
  viewOrder: function(po_id) {
    this.showLoading();

    $('#modal-order-view .modal-content').load('index.php?route=purchase/order/view&user_token=' + this.user_token + '&po_id=' + po_id, function() {
      $('#modal-order-view').modal('show');

      // إضافة معالج أحداث لزر التعديل داخل نافذة العرض
      $('#modal-order-view').on('click', '#edit-order-btn', function() {
        var orderId = $(this).data('po-id');
        $('#modal-order-view').modal('hide');

        // استدعاء وظيفة التعديل بعد إغلاق نافذة العرض
        setTimeout(function() {
          OrderManager.editOrder(orderId);
        }, 500);
      });

      OrderManager.hideLoading();
    });
  },

  /**
   * اعتماد أمر شراء
   * @param {number} po_id - معرف أمر الشراء المراد اعتماده
   */
  approveOrder: function(po_id) {
    Swal.fire({
      title: '{{ text_confirm_approve }}',
      text: '',
      icon: 'question',
      showCancelButton: true,
      confirmButtonText: '{{ button_approve }}',
      cancelButtonText: '{{ button_cancel }}',
      confirmButtonColor: '#28a745',
      cancelButtonColor: '#6c757d'
    }).then((result) => {
      if (result.isConfirmed) {
        this.showLoading();

        $.ajax({
          url: 'index.php?route=purchase/order/approve&user_token=' + this.user_token,
          type: 'POST',
          data: { po_id: po_id },
          dataType: 'json',
          success: function(json) {
            if (json.error) {
              toastr.error(json.error);
            } else if (json.success) {
              toastr.success(json.success);
              OrderManager.loadOrders();
            }
            OrderManager.hideLoading();
          },
          error: function(xhr, status, error) {
            toastr.error('{{ error_ajax }}');
            OrderManager.hideLoading();
          }
        });
      }
    });
  },

  /**
   * رفض أمر شراء
   * @param {number} po_id - معرف أمر الشراء المراد رفضه
   */
  rejectOrder: function(po_id) {
    Swal.fire({
      title: '{{ text_confirm_reject }}',
      text: '{{ text_enter_rejection_reason }}',
      input: 'textarea',
      inputPlaceholder: '{{ text_enter_rejection_reason }}',
      showCancelButton: true,
      confirmButtonText: '{{ button_reject }}',
      cancelButtonText: '{{ button_cancel }}',
      confirmButtonColor: '#ffc107',
      cancelButtonColor: '#6c757d',
      inputValidator: (value) => {
        if (!value || value.trim() === '') {
          return '{{ error_rejection_reason_required }}';
        }
      }
    }).then((result) => {
      if (result.isConfirmed) {
        this.showLoading();

        $.ajax({
          url: 'index.php?route=purchase/order/reject&user_token=' + this.user_token,
          type: 'POST',
          data: {
            po_id: po_id,
            reason: result.value
          },
          dataType: 'json',
          success: function(json) {
            if (json.error) {
              toastr.error(json.error);
            } else if (json.success) {
              toastr.success(json.success);
              OrderManager.loadOrders();
            }
            OrderManager.hideLoading();
          },
          error: function(xhr, status, error) {
            toastr.error('{{ error_ajax }}');
            OrderManager.hideLoading();
          }
        });
      }
    });
  },

  /**
   * حذف أمر شراء
   * @param {number} po_id - معرف أمر الشراء المراد حذفه
   */
  deleteOrder: function(po_id) {
    Swal.fire({
      title: '{{ text_confirm_delete }}',
      text: '',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: '{{ button_delete }}',
      cancelButtonText: '{{ button_cancel }}',
      confirmButtonColor: '#dc3545',
      cancelButtonColor: '#6c757d'
    }).then((result) => {
      if (result.isConfirmed) {
        this.showLoading();

        $.ajax({
          url: 'index.php?route=purchase/order/delete&user_token=' + this.user_token,
          type: 'POST',
          data: { po_id: po_id },
          dataType: 'json',
          success: function(json) {
            if (json.error) {
              toastr.error(json.error);
            } else if (json.success) {
              toastr.success(json.success);
              OrderManager.loadOrders();
            }
            OrderManager.hideLoading();
          },
          error: function(xhr, status, error) {
            toastr.error('{{ error_ajax }}');
            OrderManager.hideLoading();
          }
        });
      }
    });
  },

  /**
   * طباعة أمر شراء
   * @param {number} po_id - معرف أمر الشراء المراد طباعته
   */
  printOrder: function(po_id) {
    window.open('index.php?route=purchase/order/print&user_token=' + this.user_token + '&po_id=' + po_id, '_blank');
  },

  /**
   * إنشاء إذن استلام
   * @param {number} po_id - معرف أمر الشراء
   */
  createReceipt: function(po_id) {
    this.showLoading();

    $('#modal-receipt-form .modal-content').load('index.php?route=purchase/order/createReceipt&user_token=' + this.user_token + '&po_id=' + po_id, function() {
      $('#modal-receipt-form').modal('show');

      // تهيئة مكونات النموذج
      setTimeout(function() {
        // OrderManager.initializeReceiptForm(); // This function is now in the receipt form template
        OrderManager.hideLoading();
      }, 500);
    });
  },

  /**
   * فتح نافذة المطابقة الثلاثية
   * @param {number} po_id - معرف أمر الشراء
   */
  matchOrder: function(po_id) {
    this.showLoading();

    $('#modal-matching .modal-content').load('index.php?route=purchase/order/match&user_token=' + this.user_token + '&po_id=' + po_id, function() {
      $('#modal-matching').modal('show');

      // تهيئة مكونات نموذج المطابقة
      setTimeout(function() {
        // OrderManager.initializeMatchingForm(); // This function is now in the matching form template
        OrderManager.hideLoading();
      }, 500);
    });
  },

  /**
   * تنفيذ إجراء مجمع على أوامر الشراء المحددة
   */
  executeBulkAction: function() {
    var action = $('#bulk-action').val();

    if (!action) {
      toastr.warning('{{ text_select_action }}');
      return;
    }

    var selected = [];
    $('input[name="selected[]"]:checked').each(function() {
      selected.push($(this).val());
    });

    if (selected.length === 0) {
      toastr.warning('{{ text_no_items_selected }}');
      return;
    }

    var swalConfig = {
      showCancelButton: true,
      cancelButtonText: '{{ button_cancel }}',
      cancelButtonColor: '#6c757d'
    };

    switch(action) {
      case 'approve':
        swalConfig.title = '{{ text_confirm_bulk_action }}';
        swalConfig.icon = 'question';
        swalConfig.confirmButtonText = '{{ button_approve }}';
        swalConfig.confirmButtonColor = '#28a745';
        break;

      case 'reject':
        swalConfig.title = '{{ text_confirm_bulk_action }}';
        swalConfig.icon = 'question';
        swalConfig.confirmButtonText = '{{ button_reject }}';
        swalConfig.confirmButtonColor = '#ffc107';
        swalConfig.input = 'textarea';
        swalConfig.inputPlaceholder = '{{ text_enter_rejection_reason }}';
        swalConfig.inputValidator = (value) => {
          if (!value || value.trim() === '') {
            return '{{ text_reject_reason_required }}';
          }
        };
        break;

      case 'delete':
        swalConfig.title = '{{ text_confirm_bulk_delete }}';
        swalConfig.icon = 'warning';
        swalConfig.confirmButtonText = '{{ button_delete }}';
        swalConfig.confirmButtonColor = '#dc3545';
        break;
    }

    Swal.fire(swalConfig).then((result) => {
      if (result.isConfirmed) {
        this.showLoading();

        var postData = {
          action: action,
          selected: selected
        };

        if (action === 'reject' && result.value) {
          postData.reason = result.value;
        }

        $.ajax({
          url: 'index.php?route=purchase/order/bulkAction&user_token=' + this.user_token,
          type: 'POST',
          data: postData,
          dataType: 'json',
          success: function(json) {
            if (json.error) {
              toastr.error(json.error);

              if (json.errors && Array.isArray(json.errors)) {
                for (var i = 0; i < json.errors.length; i++) {
                  toastr.error(json.errors[i]);
                }
              }
            }

            if (json.success) {
              toastr.success(json.success);
              OrderManager.loadOrders();
            }

            OrderManager.hideLoading();
          },
          error: function(xhr, status, error) {
            toastr.error('{{ error_ajax }}');
            OrderManager.hideLoading();
          }
        });
      }
    });
  },

  /**
   * تصدير أوامر الشراء إلى Excel أو PDF
   * @param {string} type - نوع التصدير ('excel' أو 'pdf')
   */
  exportOrders: function(type) {
    var url = 'index.php?route=purchase/order/export&user_token=' + this.user_token +
      '&type=' + type +
      '&filter_po_number=' + encodeURIComponent($('#filter-po-number').val() || '') +
      '&filter_quotation_id=' + encodeURIComponent($('#filter-quotation').val() || '') +
      '&filter_supplier_id=' + encodeURIComponent($('#filter-supplier').val() || '') +
      '&filter_status=' + encodeURIComponent($('#filter-status').val() || '') +
      '&filter_date_start=' + encodeURIComponent($('#filter-date-start').val() || '') +
      '&filter_date_end=' + encodeURIComponent($('#filter-date-end').val() || '');

    window.open(url, '_blank');
  }

};

// التهيئة عند جاهزية المستند
$(document).ready(function() {
  OrderManager.init();
});
</script>
