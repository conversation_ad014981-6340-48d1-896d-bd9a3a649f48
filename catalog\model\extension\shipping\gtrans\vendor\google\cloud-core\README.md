# Google Cloud Core Libraries for PHP

[![Latest Stable Version](https://poser.pugx.org/google/cloud-core/v/stable)](https://packagist.org/packages/google/cloud-core) [![Packagist](https://img.shields.io/packagist/dm/google/cloud-core.svg)](https://packagist.org/packages/google/cloud-core)

* [API documentation](http://googleapis.github.io/google-cloud-php/#/docs/cloud-core/latest)

**NOTE:** This repository is part of [Google Cloud PHP](https://github.com/googleapis/google-cloud-php). Any
support requests, bug reports, or development contributions should be directed to
that project.

### Installation

**NOTE** This package is not intended for direct use. It provides common infrastructure
to the rest of the Google Cloud PHP components.

```sh
$ composer require google/cloud-core
```

### Version

This component is considered GA (generally available). As such, it will not introduce backwards-incompatible changes in
any minor or patch releases. We will address issues and requests with the highest priority.
