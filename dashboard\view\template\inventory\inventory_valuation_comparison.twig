{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="submit" form="inventory\inventory_valuation-form" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fas fa-save"></i></button>
        <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-light"><i class="fas fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    
    <div class="card">
      <div class="card-header"><i class="fas fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}chart-bar{% elif screen_type == 'settings' %}cog{% else %}pencil-alt{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="card-body">
        <form id="inventory\inventory_valuation-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-branch_type_options">{{ text_branch_type_options }}</label>
            <div class="col-sm-10">
              <input type="text" name="branch_type_options" value="{{ branch_type_options }}" placeholder="{{ text_branch_type_options }}" id="input-branch_type_options" class="form-control" />
              {% if error_branch_type_options %}
                <div class="invalid-feedback">{{ error_branch_type_options }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-branches">{{ text_branches }}</label>
            <div class="col-sm-10">
              <input type="text" name="branches" value="{{ branches }}" placeholder="{{ text_branches }}" id="input-branches" class="form-control" />
              {% if error_branches %}
                <div class="invalid-feedback">{{ error_branches }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-categories">{{ text_categories }}</label>
            <div class="col-sm-10">
              <input type="text" name="categories" value="{{ categories }}" placeholder="{{ text_categories }}" id="input-categories" class="form-control" />
              {% if error_categories %}
                <div class="invalid-feedback">{{ error_categories }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-compare_dates">{{ text_compare_dates }}</label>
            <div class="col-sm-10">
              <input type="text" name="compare_dates" value="{{ compare_dates }}" placeholder="{{ text_compare_dates }}" id="input-compare_dates" class="form-control" />
              {% if error_compare_dates %}
                <div class="invalid-feedback">{{ error_compare_dates }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-comparison">{{ text_comparison }}</label>
            <div class="col-sm-10">
              <input type="text" name="comparison" value="{{ comparison }}" placeholder="{{ text_comparison }}" id="input-comparison" class="form-control" />
              {% if error_comparison %}
                <div class="invalid-feedback">{{ error_comparison }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-export_excel">{{ text_export_excel }}</label>
            <div class="col-sm-10">
              <input type="text" name="export_excel" value="{{ export_excel }}" placeholder="{{ text_export_excel }}" id="input-export_excel" class="form-control" />
              {% if error_export_excel %}
                <div class="invalid-feedback">{{ error_export_excel }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-export_pdf">{{ text_export_pdf }}</label>
            <div class="col-sm-10">
              <input type="text" name="export_pdf" value="{{ export_pdf }}" placeholder="{{ text_export_pdf }}" id="input-export_pdf" class="form-control" />
              {% if error_export_pdf %}
                <div class="invalid-feedback">{{ error_export_pdf }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="invalid-feedback">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-inventory_valuation">{{ text_inventory_valuation }}</label>
            <div class="col-sm-10">
              <input type="text" name="inventory_valuation" value="{{ inventory_valuation }}" placeholder="{{ text_inventory_valuation }}" id="input-inventory_valuation" class="form-control" />
              {% if error_inventory_valuation %}
                <div class="invalid-feedback">{{ error_inventory_valuation }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-manufacturers">{{ text_manufacturers }}</label>
            <div class="col-sm-10">
              <input type="text" name="manufacturers" value="{{ manufacturers }}" placeholder="{{ text_manufacturers }}" id="input-manufacturers" class="form-control" />
              {% if error_manufacturers %}
                <div class="invalid-feedback">{{ error_manufacturers }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-most_profitable_products">{{ text_most_profitable_products }}</label>
            <div class="col-sm-10">
              <input type="text" name="most_profitable_products" value="{{ most_profitable_products }}" placeholder="{{ text_most_profitable_products }}" id="input-most_profitable_products" class="form-control" />
              {% if error_most_profitable_products %}
                <div class="invalid-feedback">{{ error_most_profitable_products }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-order">{{ text_order }}</label>
            <div class="col-sm-10">
              <input type="text" name="order" value="{{ order }}" placeholder="{{ text_order }}" id="input-order" class="form-control" />
              {% if error_order %}
                <div class="invalid-feedback">{{ error_order }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-pagination">{{ text_pagination }}</label>
            <div class="col-sm-10">
              <input type="text" name="pagination" value="{{ pagination }}" placeholder="{{ text_pagination }}" id="input-pagination" class="form-control" />
              {% if error_pagination %}
                <div class="invalid-feedback">{{ error_pagination }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-print">{{ text_print }}</label>
            <div class="col-sm-10">
              <input type="text" name="print" value="{{ print }}" placeholder="{{ text_print }}" id="input-print" class="form-control" />
              {% if error_print %}
                <div class="invalid-feedback">{{ error_print }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-refresh">{{ text_refresh }}</label>
            <div class="col-sm-10">
              <input type="text" name="refresh" value="{{ refresh }}" placeholder="{{ text_refresh }}" id="input-refresh" class="form-control" />
              {% if error_refresh %}
                <div class="invalid-feedback">{{ error_refresh }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-results">{{ text_results }}</label>
            <div class="col-sm-10">
              <input type="text" name="results" value="{{ results }}" placeholder="{{ text_results }}" id="input-results" class="form-control" />
              {% if error_results %}
                <div class="invalid-feedback">{{ error_results }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort">{{ text_sort }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort" value="{{ sort }}" placeholder="{{ text_sort }}" id="input-sort" class="form-control" />
              {% if error_sort %}
                <div class="invalid-feedback">{{ error_sort }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-stock_status_options">{{ text_stock_status_options }}</label>
            <div class="col-sm-10">
              <input type="text" name="stock_status_options" value="{{ stock_status_options }}" placeholder="{{ text_stock_status_options }}" id="input-stock_status_options" class="form-control" />
              {% if error_stock_status_options %}
                <div class="invalid-feedback">{{ error_stock_status_options }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-summary">{{ text_summary }}</label>
            <div class="col-sm-10">
              <input type="text" name="summary" value="{{ summary }}" placeholder="{{ text_summary }}" id="input-summary" class="form-control" />
              {% if error_summary %}
                <div class="invalid-feedback">{{ error_summary }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-top_value_products">{{ text_top_value_products }}</label>
            <div class="col-sm-10">
              <input type="text" name="top_value_products" value="{{ top_value_products }}" placeholder="{{ text_top_value_products }}" id="input-top_value_products" class="form-control" />
              {% if error_top_value_products %}
                <div class="invalid-feedback">{{ error_top_value_products }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="invalid-feedback">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-valuation_by_branch">{{ text_valuation_by_branch }}</label>
            <div class="col-sm-10">
              <input type="text" name="valuation_by_branch" value="{{ valuation_by_branch }}" placeholder="{{ text_valuation_by_branch }}" id="input-valuation_by_branch" class="form-control" />
              {% if error_valuation_by_branch %}
                <div class="invalid-feedback">{{ error_valuation_by_branch }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-valuation_by_category">{{ text_valuation_by_category }}</label>
            <div class="col-sm-10">
              <input type="text" name="valuation_by_category" value="{{ valuation_by_category }}" placeholder="{{ text_valuation_by_category }}" id="input-valuation_by_category" class="form-control" />
              {% if error_valuation_by_category %}
                <div class="invalid-feedback">{{ error_valuation_by_category }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}