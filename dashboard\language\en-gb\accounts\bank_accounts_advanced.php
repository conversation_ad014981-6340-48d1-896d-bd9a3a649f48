<?php
// Heading
$_['heading_title']                    = 'Advanced Bank Accounts Management';

// Text
$_['text_list']                        = 'Bank Accounts List';
$_['text_add']                         = 'Add Bank Account';
$_['text_edit']                        = 'Edit Bank Account';
$_['text_view']                        = 'View Bank Account';
$_['text_default']                     = 'Default';
$_['text_success_add']                 = 'Bank account added successfully!';
$_['text_success_edit']                = 'Bank account updated successfully!';
$_['text_success_delete']              = 'Bank account deleted successfully!';
$_['text_success_reconcile']           = 'Bank reconciliation completed successfully!';
$_['text_success_transfer']            = 'Bank transfer completed successfully!';
$_['text_confirm_delete']              = 'Are you sure you want to delete this bank account?';
$_['text_no_results']                  = 'No bank accounts found';
$_['text_home']                        = 'Home';
$_['text_loading']                     = 'Loading...';
$_['text_processing']                  = 'Processing...';

// Bank Account Types
$_['text_account_type_current']        = 'Current Account';
$_['text_account_type_savings']        = 'Savings Account';
$_['text_account_type_fixed_deposit']  = 'Fixed Deposit';
$_['text_account_type_credit']         = 'Credit Account';
$_['text_account_type_loan']           = 'Loan Account';
$_['text_account_type_investment']     = 'Investment Account';

// Bank Account Status
$_['text_status_active']               = 'Active';
$_['text_status_inactive']             = 'Inactive';
$_['text_status_frozen']               = 'Frozen';
$_['text_status_closed']               = 'Closed';

// Egyptian Banks
$_['text_bank_nbe']                    = 'National Bank of Egypt';
$_['text_bank_cib']                    = 'Commercial International Bank';
$_['text_bank_aaib']                   = 'Arab African International Bank';
$_['text_bank_banque_misr']            = 'Banque Misr';
$_['text_bank_qnb']                    = 'QNB ALAHLI';
$_['text_bank_hsbc']                   = 'HSBC Bank Egypt';
$_['text_bank_alex_bank']              = 'Bank of Alexandria';
$_['text_bank_cairo']                  = 'Banque du Caire';

// Banking Operations
$_['text_reconciliation']              = 'Bank Reconciliation';
$_['text_bank_transfer']               = 'Bank Transfer';
$_['text_cash_flow_analysis']          = 'Cash Flow Analysis';
$_['text_account_analysis']            = 'Account Analysis';
$_['text_transaction_history']         = 'Transaction History';
$_['text_balance_history']             = 'Balance History';

// Reconciliation
$_['text_reconcile_account']           = 'Reconcile Account';
$_['text_bank_statement']              = 'Bank Statement';
$_['text_book_balance']                = 'Book Balance';
$_['text_bank_balance']                = 'Bank Balance';
$_['text_outstanding_deposits']        = 'Outstanding Deposits';
$_['text_outstanding_checks']          = 'Outstanding Checks';
$_['text_bank_charges']                = 'Bank Charges';
$_['text_interest_earned']             = 'Interest Earned';
$_['text_reconciled_balance']          = 'Reconciled Balance';

// Transfer Operations
$_['text_internal_transfer']           = 'Internal Transfer';
$_['text_external_transfer']           = 'External Transfer';
$_['text_from_account']                = 'From Account';
$_['text_to_account']                  = 'To Account';
$_['text_transfer_amount']             = 'Transfer Amount';
$_['text_transfer_fees']               = 'Transfer Fees';
$_['text_transfer_reference']          = 'Transfer Reference';
$_['text_transfer_purpose']            = 'Transfer Purpose';

// Cash Flow Analysis
$_['text_cash_inflow']                 = 'Cash Inflow';
$_['text_cash_outflow']                = 'Cash Outflow';
$_['text_net_cash_flow']               = 'Net Cash Flow';
$_['text_opening_balance']             = 'Opening Balance';
$_['text_closing_balance']             = 'Closing Balance';
$_['text_average_balance']             = 'Average Balance';
$_['text_minimum_balance']             = 'Minimum Balance';
$_['text_maximum_balance']             = 'Maximum Balance';

// Performance Indicators
$_['text_account_utilization']         = 'Account Utilization';
$_['text_transaction_volume']          = 'Transaction Volume';
$_['text_average_transaction']         = 'Average Transaction';
$_['text_monthly_charges']             = 'Monthly Charges';
$_['text_interest_rate']               = 'Interest Rate';
$_['text_return_on_balance']           = 'Return on Balance';

// Column Headers
$_['column_account_number']            = 'Account Number';
$_['column_account_name']              = 'Account Name';
$_['column_bank_name']                 = 'Bank Name';
$_['column_account_type']              = 'Account Type';
$_['column_currency']                  = 'Currency';
$_['column_current_balance']           = 'Current Balance';
$_['column_available_balance']         = 'Available Balance';
$_['column_last_transaction']          = 'Last Transaction';
$_['column_status']                    = 'Status';
$_['column_action']                    = 'Action';

// Entry Fields
$_['entry_account_number']             = 'Account Number';
$_['entry_account_name']               = 'Account Name';
$_['entry_bank_name']                  = 'Bank Name';
$_['entry_bank_code']                  = 'Bank Code';
$_['entry_branch_name']                = 'Branch Name';
$_['entry_branch_code']                = 'Branch Code';
$_['entry_swift_code']                 = 'SWIFT Code';
$_['entry_iban']                       = 'IBAN';
$_['entry_account_type']               = 'Account Type';
$_['entry_currency']                   = 'Currency';
$_['entry_opening_balance']            = 'Opening Balance';
$_['entry_minimum_balance']            = 'Minimum Balance';
$_['entry_overdraft_limit']            = 'Overdraft Limit';
$_['entry_interest_rate']              = 'Interest Rate';
$_['entry_monthly_charges']            = 'Monthly Charges';
$_['entry_contact_person']             = 'Contact Person';
$_['entry_phone']                      = 'Phone';
$_['entry_email']                      = 'Email';
$_['entry_address']                    = 'Address';
$_['entry_notes']                      = 'Notes';

// Buttons
$_['button_add_account']               = 'Add Account';
$_['button_edit_account']              = 'Edit Account';
$_['button_delete_account']            = 'Delete Account';
$_['button_view_account']              = 'View Account';
$_['button_reconcile']                 = 'Reconcile';
$_['button_transfer']                  = 'Transfer';
$_['button_analyze']                   = 'Analyze';
$_['button_export_excel']              = 'Export Excel';
$_['button_export_pdf']                = 'Export PDF';
$_['button_export_csv']                = 'Export CSV';
$_['button_print']                     = 'Print';
$_['button_refresh']                   = 'Refresh';

// Error Messages
$_['error_permission']                 = 'Warning: You do not have permission to access bank accounts!';
$_['error_account_number_required']    = 'Account number is required!';
$_['error_account_name_required']      = 'Account name is required!';
$_['error_bank_name_required']         = 'Bank name is required!';
$_['error_account_type_required']      = 'Account type is required!';
$_['error_currency_required']          = 'Currency is required!';
$_['error_opening_balance_invalid']    = 'Opening balance is invalid!';
$_['error_account_exists']             = 'Account number already exists!';
$_['error_account_not_found']          = 'Bank account not found!';
$_['error_insufficient_balance']       = 'Insufficient balance!';
$_['error_transfer_same_account']      = 'Cannot transfer to the same account!';
$_['error_transfer_amount_invalid']    = 'Transfer amount is invalid!';
$_['error_reconciliation_failed']      = 'Bank reconciliation failed!';

// Success Messages
$_['text_success_export']              = 'Data exported successfully!';
$_['text_success_analysis']            = 'Analysis generated successfully!';
$_['text_success_refresh']             = 'Data refreshed successfully!';

// Help Text
$_['help_account_number']              = 'Bank account number as shown on bank statement';
$_['help_swift_code']                  = 'SWIFT code for international transfers';
$_['help_iban']                        = 'International Bank Account Number';
$_['help_overdraft_limit']             = 'Maximum overdraft limit allowed';
$_['help_reconciliation']              = 'Match book balance with bank statement';

// Dashboard Elements
$_['text_total_accounts']              = 'Total Accounts';
$_['text_active_accounts']             = 'Active Accounts';
$_['text_total_balance']               = 'Total Balance';
$_['text_monthly_transactions']        = 'Monthly Transactions';
$_['text_pending_reconciliation']      = 'Pending Reconciliation';
$_['text_recent_transactions']         = 'Recent Transactions';

// Currencies
$_['text_currency_egp']                = 'Egyptian Pound';
$_['text_currency_usd']                = 'US Dollar';
$_['text_currency_eur']                = 'Euro';
$_['text_currency_gbp']                = 'British Pound';
$_['text_currency_sar']                = 'Saudi Riyal';
$_['text_currency_aed']                = 'UAE Dirham';

// Payment Systems
$_['text_fawry']                       = 'Fawry';
$_['text_instapay']                    = 'InstaPay';
$_['text_meeza']                       = 'Meeza';
$_['text_swift']                       = 'SWIFT';

// Reports
$_['text_account_statement']           = 'Account Statement';
$_['text_reconciliation_report']       = 'Reconciliation Report';
$_['text_cash_flow_report']            = 'Cash Flow Report';
$_['text_bank_charges_report']         = 'Bank Charges Report';
$_['text_interest_report']             = 'Interest Report';

// Additional
$_['text_total']                       = 'Total';
$_['text_subtotal']                    = 'Subtotal';
$_['text_balance']                     = 'Balance';
$_['text_debit']                       = 'Debit';
$_['text_credit']                      = 'Credit';
$_['text_date']                        = 'Date';
$_['text_description']                 = 'Description';
$_['text_reference']                   = 'Reference';
$_['text_amount']                      = 'Amount';
$_['text_fees']                        = 'Fees';
$_['text_charges']                     = 'Charges';
$_['text_interest']                    = 'Interest';

// Generated Information
$_['text_generated_by']                = 'Generated By';
$_['text_generated_on']                = 'Generated On';
$_['text_report_date']                 = 'Report Date';
$_['text_page']                        = 'Page';
$_['text_of']                          = 'of';
$_['text_confidential']                = 'Confidential';
$_['text_internal_use']                = 'Internal Use Only';

// Compliance
$_['text_cbe_compliant']               = 'Central Bank of Egypt Compliant';
$_['text_banking_law_compliant']       = 'Egyptian Banking Law Compliant';
$_['text_anti_money_laundering']       = 'Anti-Money Laundering';
$_['text_kyc_compliant']               = 'Know Your Customer Compliant';

// Risk Management
$_['text_risk_assessment']             = 'Risk Assessment';
$_['text_credit_risk']                 = 'Credit Risk';
$_['text_liquidity_risk']              = 'Liquidity Risk';
$_['text_operational_risk']            = 'Operational Risk';
$_['text_market_risk']                 = 'Market Risk';

// Controller language variables
$_['log_unauthorized_access_bank_accounts_advanced'] = 'Unauthorized access attempt to advanced bank accounts';
$_['log_view_bank_accounts_advanced_screen'] = 'View advanced bank accounts screen';
$_['log_view_bank_accounts_management_screen'] = 'View bank accounts management screen';
$_['log_unauthorized_add_bank_account'] = 'Unauthorized bank account addition attempt';
$_['log_add_bank_account'] = 'Add new bank account';
$_['text_bank_account_added'] = 'New Bank Account Added';
$_['text_bank_account_added_notification'] = 'New bank account has been added';
$_['text_in_bank'] = 'in bank';
$_['text_success_add'] = 'Success: Bank account has been added successfully!';
$_['error_add_bank_account'] = 'Error adding bank account';
$_['log_unauthorized_edit_bank_account'] = 'Unauthorized bank account edit attempt';

// Additional variables for template
$_['text_actions'] = 'Actions';
$_['text_bank_operations'] = 'Bank Operations';
$_['text_reconcile'] = 'Reconcile';
$_['text_transfer'] = 'Transfer';
$_['text_statement'] = 'Statement';
$_['text_import'] = 'Import';
$_['text_refresh_balances'] = 'Refresh Balances';
$_['text_total_accounts'] = 'Total Accounts';
$_['text_active_accounts'] = 'Active Accounts';
$_['text_inactive_accounts'] = 'Inactive Accounts';
$_['text_total_balance'] = 'Total Balance';
$_['text_accounts'] = 'Accounts';
$_['text_active'] = 'Active';
$_['text_inactive'] = 'Inactive';
$_['text_all_currencies'] = 'All Currencies';
$_['text_bank_accounts_list'] = 'Bank Accounts List';
$_['column_account_name'] = 'Account Name';
$_['column_bank_name'] = 'Bank Name';
$_['column_account_number'] = 'Account Number';
$_['column_iban'] = 'IBAN';
$_['column_currency'] = 'Currency';
$_['column_balance'] = 'Balance';
$_['column_status'] = 'Status';
$_['column_last_reconciled'] = 'Last Reconciled';
$_['column_action'] = 'Action';
$_['text_never'] = 'Never';
$_['text_deactivate'] = 'Deactivate';
$_['text_activate'] = 'Activate';
$_['text_refreshing_balances'] = 'Refreshing balances';
$_['text_balances_refreshed'] = 'Balances refreshed successfully';
$_['error_refresh_balances'] = 'Error refreshing balances';
$_['text_confirm_activate'] = 'Are you sure you want to activate this account?';
$_['text_confirm_deactivate'] = 'Are you sure you want to deactivate this account?';
$_['text_status_updated'] = 'Account status updated successfully';
$_['error_update_status'] = 'Error updating account status';
?>
