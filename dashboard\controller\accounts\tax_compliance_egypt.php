<?php
/**
 * Tax Compliance Egypt Controller - ETA Integration
 * Enterprise Grade Plus - Egyptian Tax Authority Integration
 * Compliant with https://sdk.invoice.eta.gov.eg/start
 */
class ControllerAccountsTaxComplianceEgypt extends Controller {
    
    private $error = array();
    
    public function index() {
        // CONSTITUTIONAL REQUIREMENT: Load central services
        $this->load->model('core/central_service_manager');
        $this->central_service = new ModelCoreCentralServiceManager($this->registry);
        
        $this->load->language('accounts/tax_compliance_egypt');
        
        $this->document->setTitle($this->language->get('heading_title'));
        
        // Check permissions
        if (!$this->user->hasPermission('access', 'accounts/tax_compliance_egypt')) {
            $this->central_service->logActivity('unauthorized_access', 'tax_compliance_egypt',
                $this->language->get('log_unauthorized_access'), [
                'user_id' => $this->user->getId(),
                'ip_address' => $this->request->server['REMOTE_ADDR']
            ]);
            
            $this->session->data['error'] = $this->language->get('error_permission');
            $this->response->redirect($this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true));
        }
        
        $this->load->model('accounts/tax_compliance_egypt');
        $this->load->model('setting/setting');
        
        // Log screen access
        $this->central_service->logActivity('view_screen', 'tax_compliance_egypt',
            $this->language->get('log_view_screen'), [
            'user_id' => $this->user->getId(),
            'screen' => 'tax_compliance_egypt'
        ]);
        
        $data['breadcrumbs'] = array();
        
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );
        
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('accounts/tax_compliance_egypt', 'user_token=' . $this->session->data['user_token'], true)
        );
        
        // Get ETA connection status
        $data['eta_connection_status'] = $this->model_accounts_tax_compliance_egypt->getETAConnectionStatus();
        
        // Get compliance dashboard data
        $data['compliance_dashboard'] = $this->model_accounts_tax_compliance_egypt->getComplianceDashboard();
        
        // Get pending submissions
        $data['pending_submissions'] = $this->model_accounts_tax_compliance_egypt->getPendingSubmissions();
        
        // Get recent activities
        $data['recent_activities'] = $this->model_accounts_tax_compliance_egypt->getRecentActivities();
        
        // URLs
        $data['submit_invoices'] = $this->url->link('accounts/tax_compliance_egypt/submitInvoices', 'user_token=' . $this->session->data['user_token'], true);
        $data['submit_vat_return'] = $this->url->link('accounts/tax_compliance_egypt/submitVATReturn', 'user_token=' . $this->session->data['user_token'], true);
        $data['sync_codes'] = $this->url->link('accounts/tax_compliance_egypt/syncCodes', 'user_token=' . $this->session->data['user_token'], true);
        $data['test_connection'] = $this->url->link('accounts/tax_compliance_egypt/testConnection', 'user_token=' . $this->session->data['user_token'], true);
        $data['settings'] = $this->url->link('accounts/tax_compliance_egypt/settings', 'user_token=' . $this->session->data['user_token'], true);
        
        $data['user_token'] = $this->session->data['user_token'];
        
        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');
        
        $this->response->setOutput($this->load->view('accounts/tax_compliance_egypt', $data));
    }
    
    public function submitInvoices() {
        $this->load->language('accounts/tax_compliance_egypt');
        $this->load->model('accounts/tax_compliance_egypt');
        
        $json = array();
        
        if (!$this->user->hasPermission('modify', 'accounts/tax_compliance_egypt')) {
            $json['error'] = $this->language->get('error_permission');
        } else {
            try {
                $filter_data = array(
                    'date_start' => $this->request->post['date_start'] ?? date('Y-m-01'),
                    'date_end' => $this->request->post['date_end'] ?? date('Y-m-t'),
                    'invoice_type' => $this->request->post['invoice_type'] ?? 'all'
                );
                
                $result = $this->model_accounts_tax_compliance_egypt->submitInvoicesToETA($filter_data);
                
                if ($result['success']) {
                    $json['success'] = $this->language->get('text_invoices_submitted');
                    $json['data'] = $result['data'];
                    
                    // Log successful submission
                    $this->central_service->logActivity('submit_invoices_eta', 'tax_compliance_egypt',
                        $this->language->get('log_invoices_submitted'), [
                        'user_id' => $this->user->getId(),
                        'submitted_count' => $result['data']['submitted_count'],
                        'period' => $filter_data['date_start'] . ' to ' . $filter_data['date_end']
                    ]);
                } else {
                    $json['error'] = $result['error'];
                }
                
            } catch (Exception $e) {
                $json['error'] = $e->getMessage();
            }
        }
        
        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }
    
    public function submitVATReturn() {
        $this->load->language('accounts/tax_compliance_egypt');
        $this->load->model('accounts/tax_compliance_egypt');
        
        $json = array();
        
        if (!$this->user->hasPermission('modify', 'accounts/tax_compliance_egypt')) {
            $json['error'] = $this->language->get('error_permission');
        } else {
            try {
                $period_data = array(
                    'year' => $this->request->post['year'] ?? date('Y'),
                    'month' => $this->request->post['month'] ?? date('m'),
                    'period_type' => $this->request->post['period_type'] ?? 'monthly'
                );
                
                $result = $this->model_accounts_tax_compliance_egypt->submitVATReturnToETA($period_data);
                
                if ($result['success']) {
                    $json['success'] = $this->language->get('text_vat_return_submitted');
                    $json['data'] = $result['data'];
                    
                    // Log successful submission
                    $this->central_service->logActivity('submit_vat_return_eta', 'tax_compliance_egypt',
                        $this->language->get('log_vat_return_submitted'), [
                        'user_id' => $this->user->getId(),
                        'period' => $period_data['year'] . '-' . $period_data['month'],
                        'submission_id' => $result['data']['submission_id']
                    ]);
                } else {
                    $json['error'] = $result['error'];
                }
                
            } catch (Exception $e) {
                $json['error'] = $e->getMessage();
            }
        }
        
        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }
    
    public function syncCodes() {
        $this->load->language('accounts/tax_compliance_egypt');
        $this->load->model('accounts/tax_compliance_egypt');
        
        $json = array();
        
        if (!$this->user->hasPermission('modify', 'accounts/tax_compliance_egypt')) {
            $json['error'] = $this->language->get('error_permission');
        } else {
            try {
                $result = $this->model_accounts_tax_compliance_egypt->syncETACodes();
                
                if ($result['success']) {
                    $json['success'] = $this->language->get('text_codes_synced');
                    $json['data'] = $result['data'];
                    
                    // Log successful sync
                    $this->central_service->logActivity('sync_eta_codes', 'tax_compliance_egypt',
                        $this->language->get('log_codes_synced'), [
                        'user_id' => $this->user->getId(),
                        'synced_count' => $result['data']['synced_count']
                    ]);
                } else {
                    $json['error'] = $result['error'];
                }
                
            } catch (Exception $e) {
                $json['error'] = $e->getMessage();
            }
        }
        
        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }
    
    public function testConnection() {
        $this->load->language('accounts/tax_compliance_egypt');
        $this->load->model('accounts/tax_compliance_egypt');
        
        $json = array();
        
        try {
            $result = $this->model_accounts_tax_compliance_egypt->testETAConnection();
            
            if ($result['success']) {
                $json['success'] = $this->language->get('text_connection_successful');
                $json['data'] = $result['data'];
            } else {
                $json['error'] = $result['error'];
            }
            
        } catch (Exception $e) {
            $json['error'] = $e->getMessage();
        }
        
        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }
    
    public function settings() {
        $this->load->language('accounts/tax_compliance_egypt');
        
        $this->document->setTitle($this->language->get('heading_title_settings'));
        
        $this->load->model('accounts/tax_compliance_egypt');
        $this->load->model('setting/setting');
        
        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateSettings()) {
            $this->model_setting_setting->editSetting('eta_compliance', $this->request->post);
            
            $this->session->data['success'] = $this->language->get('text_success_settings');
            
            $this->response->redirect($this->url->link('accounts/tax_compliance_egypt', 'user_token=' . $this->session->data['user_token'], true));
        }
        
        $data['breadcrumbs'] = array();
        
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );
        
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('accounts/tax_compliance_egypt', 'user_token=' . $this->session->data['user_token'], true)
        );
        
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title_settings'),
            'href' => $this->url->link('accounts/tax_compliance_egypt/settings', 'user_token=' . $this->session->data['user_token'], true)
        );
        
        // Get current settings
        $data['eta_api_url'] = $this->config->get('eta_api_url') ?: 'https://api.invoicing.eta.gov.eg';
        $data['eta_client_id'] = $this->config->get('eta_client_id') ?: '';
        $data['eta_client_secret'] = $this->config->get('eta_client_secret') ?: '';
        $data['eta_tax_number'] = $this->config->get('eta_tax_number') ?: '';
        $data['eta_environment'] = $this->config->get('eta_environment') ?: 'production';
        $data['eta_auto_submit'] = $this->config->get('eta_auto_submit') ?: 0;
        
        $data['action'] = $this->url->link('accounts/tax_compliance_egypt/settings', 'user_token=' . $this->session->data['user_token'], true);
        $data['cancel'] = $this->url->link('accounts/tax_compliance_egypt', 'user_token=' . $this->session->data['user_token'], true);
        
        if (isset($this->error['warning'])) {
            $data['error_warning'] = $this->error['warning'];
        } else {
            $data['error_warning'] = '';
        }
        
        $data['user_token'] = $this->session->data['user_token'];
        
        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');
        
        $this->response->setOutput($this->load->view('accounts/tax_compliance_egypt_settings', $data));
    }
    
    protected function validateSettings() {
        if (!$this->user->hasPermission('modify', 'accounts/tax_compliance_egypt')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }
        
        if (empty($this->request->post['eta_client_id'])) {
            $this->error['eta_client_id'] = $this->language->get('error_client_id');
        }
        
        if (empty($this->request->post['eta_client_secret'])) {
            $this->error['eta_client_secret'] = $this->language->get('error_client_secret');
        }
        
        if (empty($this->request->post['eta_tax_number'])) {
            $this->error['eta_tax_number'] = $this->language->get('error_tax_number');
        }
        
        return !$this->error;
    }
    
    /**
     * Sanitize all output data to prevent XSS attacks
     * CONSTITUTIONAL REQUIREMENT
     */
    private function sanitizeOutput($data) {
        if (is_array($data)) {
            return array_map(array($this, 'sanitizeOutput'), $data);
        }
        return htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    }
}
?>
