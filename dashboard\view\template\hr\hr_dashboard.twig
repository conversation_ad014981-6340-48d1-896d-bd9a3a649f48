{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    
    <div class="card">
      <div class="card-header"><i class="fas fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}chart-bar{% elif screen_type == 'settings' %}cog{% else %}pencil-alt{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="card-body">
        <div class="row">
          <!-- KPI Cards -->
          <div class="col-lg-3 col-md-6">
            <div class="card mb-3 bg-primary text-white">
              <div class="card-body d-flex justify-content-between">
                <div>
                  <h5 class="card-title">{{ text_total_sales }}</h5>
                  <h3 class="mb-0">{{ total_sales }}</h3>
                </div>
                <div class="card-icon">
                  <i class="fas fa-shopping-cart fa-3x"></i>
                </div>
              </div>
              <div class="card-footer d-flex align-items-center justify-content-between">
                <a href="{{ sales_url }}" class="text-white">{{ text_view_details }}</a>
                <span class="ms-auto">
                  {% if sales_growth > 0 %}
                  <i class="fas fa-arrow-up"></i> {{ sales_growth }}%
                  {% else %}
                  <i class="fas fa-arrow-down"></i> {{ sales_growth|abs }}%
                  {% endif %}
                </span>
              </div>
            </div>
          </div>
          
          <div class="col-lg-3 col-md-6">
            <div class="card mb-3 bg-success text-white">
              <div class="card-body d-flex justify-content-between">
                <div>
                  <h5 class="card-title">{{ text_total_customers }}</h5>
                  <h3 class="mb-0">{{ total_customers }}</h3>
                </div>
                <div class="card-icon">
                  <i class="fas fa-users fa-3x"></i>
                </div>
              </div>
              <div class="card-footer d-flex align-items-center justify-content-between">
                <a href="{{ customers_url }}" class="text-white">{{ text_view_details }}</a>
                <span class="ms-auto">
                  {% if customer_growth > 0 %}
                  <i class="fas fa-arrow-up"></i> {{ customer_growth }}%
                  {% else %}
                  <i class="fas fa-arrow-down"></i> {{ customer_growth|abs }}%
                  {% endif %}
                </span>
              </div>
            </div>
          </div>
          
          <div class="col-lg-3 col-md-6">
            <div class="card mb-3 bg-info text-white">
              <div class="card-body d-flex justify-content-between">
                <div>
                  <h5 class="card-title">{{ text_total_products }}</h5>
                  <h3 class="mb-0">{{ total_products }}</h3>
                </div>
                <div class="card-icon">
                  <i class="fas fa-box fa-3x"></i>
                </div>
              </div>
              <div class="card-footer d-flex align-items-center justify-content-between">
                <a href="{{ products_url }}" class="text-white">{{ text_view_details }}</a>
                <span class="ms-auto">
                  {% if product_count_change > 0 %}
                  <i class="fas fa-arrow-up"></i> {{ product_count_change }}
                  {% else %}
                  <i class="fas fa-arrow-down"></i> {{ product_count_change|abs }}
                  {% endif %}
                </span>
              </div>
            </div>
          </div>
          
          <div class="col-lg-3 col-md-6">
            <div class="card mb-3 bg-warning text-white">
              <div class="card-body d-flex justify-content-between">
                <div>
                  <h5 class="card-title">{{ text_total_orders }}</h5>
                  <h3 class="mb-0">{{ total_orders }}</h3>
                </div>
                <div class="card-icon">
                  <i class="fas fa-file-invoice fa-3x"></i>
                </div>
              </div>
              <div class="card-footer d-flex align-items-center justify-content-between">
                <a href="{{ orders_url }}" class="text-white">{{ text_view_details }}</a>
                <span class="ms-auto">
                  {% if order_growth > 0 %}
                  <i class="fas fa-arrow-up"></i> {{ order_growth }}%
                  {% else %}
                  <i class="fas fa-arrow-down"></i> {{ order_growth|abs }}%
                  {% endif %}
                </span>
              </div>
            </div>
          </div>
        </div>
        
        <div class="row">
          <!-- Chart Section -->
          <div class="col-lg-8 col-md-12">
            <div class="card mb-3">
              <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-chart-line me-2"></i>{{ text_sales_analytics }}</h5>
                <div class="btn-group" role="group">
                  <button type="button" class="btn btn-sm btn-outline-secondary active" id="btn-week">{{ text_week }}</button>
                  <button type="button" class="btn btn-sm btn-outline-secondary" id="btn-month">{{ text_month }}</button>
                  <button type="button" class="btn btn-sm btn-outline-secondary" id="btn-year">{{ text_year }}</button>
                </div>
              </div>
              <div class="card-body">
                <canvas id="sales-chart" height="300"></canvas>
              </div>
            </div>
          </div>
          
          <!-- Recent Activity -->
          <div class="col-lg-4 col-md-12">
            <div class="card mb-3">
              <div class="card-header">
                <h5><i class="fas fa-history me-2"></i>{{ text_recent_activity }}</h5>
              </div>
              <div class="card-body p-0">
                <div class="list-group list-group-flush">
                  {% if activities %}
                    {% for activity in activities %}
                      <div class="list-group-item">
                        <div class="d-flex w-100 justify-content-between">
                          <h6 class="mb-1">{{ activity.title }}</h6>
                          <small>{{ activity.time_ago }}</small>
                        </div>
                        <p class="mb-1">{{ activity.description }}</p>
                        <small>{{ activity.user }}</small>
                      </div>
                    {% endfor %}
                  {% else %}
                    <div class="list-group-item">
                      <p class="mb-1">{{ text_no_activity }}</p>
                    </div>
                  {% endif %}
                </div>
              </div>
              <div class="card-footer">
                <a href="{{ activity_url }}" class="btn btn-sm btn-outline-secondary w-100">{{ text_view_all_activity }}</a>
              </div>
            </div>
          </div>
        </div>
        
        <div class="row">
          <!-- Recent Orders -->
          <div class="col-lg-6 col-md-12">
            <div class="card mb-3">
              <div class="card-header">
                <h5><i class="fas fa-shopping-bag me-2"></i>{{ text_recent_orders }}</h5>
              </div>
              <div class="card-body p-0">
                <div class="table-responsive">
                  <table class="table mb-0">
                    <thead>
                      <tr>
                        <th>{{ column_order_id }}</th>
                        <th>{{ column_customer }}</th>
                        <th>{{ column_status }}</th>
                        <th>{{ column_total }}</th>
                        <th>{{ column_date_added }}</th>
                        <th>{{ column_action }}</th>
                      </tr>
                    </thead>
                    <tbody>
                      {% if orders %}
                        {% for order in orders %}
                          <tr>
                            <td>{{ order.order_id }}</td>
                            <td>{{ order.customer }}</td>
                            <td>{{ order.status }}</td>
                            <td>{{ order.total }}</td>
                            <td>{{ order.date_added }}</td>
                            <td><a href="{{ order.view }}" data-bs-toggle="tooltip" title="{{ button_view }}"><i class="fas fa-eye"></i></a></td>
                          </tr>
                        {% endfor %}
                      {% else %}
                        <tr>
                          <td colspan="6" class="text-center">{{ text_no_results }}</td>
                        </tr>
                      {% endif %}
                    </tbody>
                  </table>
                </div>
              </div>
              <div class="card-footer">
                <a href="{{ order_list_url }}" class="btn btn-sm btn-outline-secondary w-100">{{ text_view_all_orders }}</a>
              </div>
            </div>
          </div>
          
          <!-- Quick Actions & Notifications -->
          <div class="col-lg-6 col-md-12">
            <div class="row">
              <div class="col-md-12">
                <div class="card mb-3">
                  <div class="card-header">
                    <h5><i class="fas fa-bolt me-2"></i>{{ text_quick_actions }}</h5>
                  </div>
                  <div class="card-body">
                    <div class="row">
                      <div class="col-4 text-center mb-3">
                        <a href="{{ add_product_url }}" class="btn btn-outline-primary btn-icon">
                          <i class="fas fa-plus-circle fa-2x mb-2"></i><br>
                          {{ text_add_product }}
                        </a>
                      </div>
                      <div class="col-4 text-center mb-3">
                        <a href="{{ add_order_url }}" class="btn btn-outline-primary btn-icon">
                          <i class="fas fa-shopping-cart fa-2x mb-2"></i><br>
                          {{ text_add_order }}
                        </a>
                      </div>
                      <div class="col-4 text-center mb-3">
                        <a href="{{ add_customer_url }}" class="btn btn-outline-primary btn-icon">
                          <i class="fas fa-user-plus fa-2x mb-2"></i><br>
                          {{ text_add_customer }}
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="col-md-12">
                <div class="card mb-3">
                  <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><i class="fas fa-bell me-2"></i>{{ text_notifications }}</h5>
                    <span class="badge bg-danger">{{ notification_count }}</span>
                  </div>
                  <div class="card-body p-0">
                    <div class="list-group list-group-flush">
                      {% if notifications %}
                        {% for notification in notifications %}
                          <div class="list-group-item">
                            <div class="d-flex w-100 justify-content-between">
                              <h6 class="mb-1">{{ notification.title }}</h6>
                              <small>{{ notification.time_ago }}</small>
                            </div>
                            <p class="mb-1">{{ notification.message }}</p>
                          </div>
                        {% endfor %}
                      {% else %}
                        <div class="list-group-item">
                          <p class="mb-1">{{ text_no_notifications }}</p>
                        </div>
                      {% endif %}
                    </div>
                  </div>
                  <div class="card-footer">
                    <a href="{{ notification_url }}" class="btn btn-sm btn-outline-secondary w-100">{{ text_view_all_notifications }}</a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

<script type="text/javascript"><!--
// Initialize sales chart
$(document).ready(function() {
    if (typeof Chart !== 'undefined' && $('#sales-chart').length) {
        var ctx = document.getElementById('sales-chart').getContext('2d');
        
        // Sample data - this would be replaced with real data from the controller
        var salesData = {
            labels: {% if chart_labels %}[{% for label in chart_labels %}'{{ label }}'{% if not loop.last %}, {% endif %}{% endfor %}]{% else %}['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']{% endif %},
            datasets: [{
                label: '{{ text_sales }}',
                data: {% if chart_data %}{% for data in chart_data %}{{ data }}{% if not loop.last %}, {% endif %}{% endfor %}{% else %}[65, 59, 80, 81, 56, 55, 40]{% endif %},
                backgroundColor: 'rgba(54, 162, 235, 0.2)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 1,
                tension: 0.4
            }]
        };
        
        var salesChart = new Chart(ctx, {
            type: 'line',
            data: salesData,
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    title: {
                        display: false,
                        text: '{{ text_sales_analytics }}'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
        
        // Time period buttons
        $('#btn-week, #btn-month, #btn-year').on('click', function() {
            $('#btn-week, #btn-month, #btn-year').removeClass('active');
            $(this).addClass('active');
            
            var period = this.id.replace('btn-', '');
            
            // Ajax call to get new data based on period
            $.ajax({
                url: 'index.php?route={screen_name}/chart&user_token={{ user_token }}&period=' + period,
                type: 'GET',
                dataType: 'json',
                success: function(json) {
                    if (json.success) {
                        salesChart.data.labels = json.labels;
                        salesChart.data.datasets[0].data = json.data;
                        salesChart.update();
                    }
                },
                error: function(xhr, ajaxOptions, thrownError) {
                    console.error(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
                }
            });
        });
    }
});
//--></script>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}