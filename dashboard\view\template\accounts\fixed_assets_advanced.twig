{{ header }}{{ column_left }}

<!-- Enterprise Grade Plus CSS for Advanced Fixed Assets -->
<style>
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --danger-color: #e74c3c;
    --warning-color: #f39c12;
    --info-color: #17a2b8;
    --assets-advanced-color: #8e44ad;
    --light-bg: #f8f9fa;
    --border-color: #dee2e6;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

.assets-advanced-container {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: var(--shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.assets-advanced-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--assets-advanced-color), var(--primary-color), var(--secondary-color));
}

.assets-advanced-header {
    text-align: center;
    border-bottom: 3px solid var(--assets-advanced-color);
    padding-bottom: 20px;
    margin-bottom: 30px;
    position: relative;
}

.assets-advanced-header h2 {
    color: var(--assets-advanced-color);
    font-weight: 700;
    font-size: 1.8rem;
    margin: 0;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.assets-advanced-summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.assets-advanced-summary-card {
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    text-align: center;
    box-shadow: var(--shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.assets-advanced-summary-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0,0,0,0.1);
}

.assets-advanced-summary-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
}

.assets-advanced-summary-card.total::before { background: var(--assets-advanced-color); }
.assets-advanced-summary-card.cost::before { background: var(--info-color); }
.assets-advanced-summary-card.depreciation::before { background: var(--warning-color); }
.assets-advanced-summary-card.net::before { background: var(--success-color); }

.assets-advanced-summary-card h4 {
    color: var(--primary-color);
    margin-bottom: 10px;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.assets-advanced-summary-card .amount {
    font-size: 1.4rem;
    font-weight: 700;
    font-family: 'Courier New', monospace;
    margin-bottom: 5px;
}

.assets-advanced-summary-card .description {
    font-size: 0.8rem;
    color: #6c757d;
    font-weight: 500;
}

.card-total .amount { color: var(--assets-advanced-color); }
.card-cost .amount { color: var(--info-color); }
.card-depreciation .amount { color: var(--warning-color); }
.card-net .amount { color: var(--success-color); }

.assets-advanced-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    background: #ffffff;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.assets-advanced-table th {
    background: linear-gradient(135deg, var(--assets-advanced-color), #7d3c98);
    color: white;
    padding: 15px 12px;
    text-align: center;
    font-weight: 600;
    font-size: 0.9rem;
    border-bottom: 2px solid var(--border-color);
}

.assets-advanced-table td {
    padding: 12px;
    border-bottom: 1px solid var(--border-color);
    text-align: center;
    transition: var(--transition);
}

.assets-advanced-table tbody tr:hover {
    background: var(--light-bg);
    transform: scale(1.005);
}

.assets-advanced-table tbody tr:nth-child(even) {
    background: #fafbfc;
}

.amount-cell {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    text-align: {{ direction == 'rtl' ? 'right' : 'left' }};
}

.amount-positive { 
    color: var(--success-color); 
    font-weight: 600;
}

.amount-negative { 
    color: var(--danger-color); 
    font-weight: 600;
}

.amount-neutral { 
    color: var(--secondary-color); 
    font-weight: 600;
}

.assets-advanced-actions {
    display: flex;
    gap: 5px;
    justify-content: center;
}

.assets-advanced-actions .btn {
    padding: 5px 10px;
    font-size: 0.8rem;
}

/* Chart Container */
.chart-container {
    background: #ffffff;
    border-radius: var(--border-radius);
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: var(--shadow);
}

.chart-container canvas {
    max-height: 400px;
}

/* Filter Panel */
.filter-panel {
    background: linear-gradient(135deg, #f8f9fa, #ffffff);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: var(--shadow);
}

/* RTL Support */
[dir="rtl"] .assets-advanced-table {
    direction: rtl;
}

[dir="rtl"] .amount-cell {
    text-align: right;
}

/* Print Styles */
@media print {
    .assets-advanced-container {
        box-shadow: none;
        border: 1px solid #000;
    }
    
    .assets-advanced-table th {
        background: #f0f0f0 !important;
        color: #000 !important;
    }
    
    .btn, .card-header, .filter-panel {
        display: none !important;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .assets-advanced-table {
        font-size: 0.8rem;
    }
    
    .assets-advanced-table th,
    .assets-advanced-table td {
        padding: 8px 6px;
    }
    
    .assets-advanced-summary-cards {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .assets-advanced-actions {
        flex-direction: column;
    }
}
</style>

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-{{ direction == 'rtl' ? 'start' : 'end' }}">
        <div class="btn-group" role="group" aria-label="{{ text_actions }}">
          <button type="button" class="btn btn-success btn-lg" onclick="addAsset()" 
                  data-bs-toggle="tooltip" title="{{ text_add_asset }}">
            <i class="fas fa-plus me-2"></i> {{ button_add }}
          </button>
          <div class="btn-group" role="group">
            <button type="button" class="btn btn-info dropdown-toggle" data-bs-toggle="dropdown" 
                    aria-expanded="false" data-bs-toggle="tooltip" title="{{ text_export_options }}">
              <i class="fas fa-download me-2"></i> {{ text_export }}
            </button>
            <ul class="dropdown-menu">
              <li><a class="dropdown-item" href="#" onclick="exportAssets('excel')">
                <i class="fas fa-file-excel text-success me-2"></i> Excel
              </a></li>
              <li><a class="dropdown-item" href="#" onclick="exportAssets('pdf')">
                <i class="fas fa-file-pdf text-danger me-2"></i> PDF
              </a></li>
              <li><a class="dropdown-item" href="#" onclick="exportAssets('csv')">
                <i class="fas fa-file-csv text-info me-2"></i> CSV
              </a></li>
              <li><hr class="dropdown-divider"></li>
              <li><a class="dropdown-item" href="#" onclick="printAssets()">
                <i class="fas fa-print text-secondary me-2"></i> {{ text_print }}
              </a></li>
            </ul>
          </div>
          <button type="button" class="btn btn-outline-primary" onclick="calculateDepreciation()"
                  data-bs-toggle="tooltip" title="{{ text_calculate_depreciation }}">
            <i class="fas fa-calculator"></i>
          </button>
          <button type="button" class="btn btn-outline-warning" onclick="showDepreciationSchedule()"
                  data-bs-toggle="tooltip" title="{{ text_depreciation_schedule }}">
            <i class="fas fa-calendar-alt"></i>
          </button>
        </div>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li class="breadcrumb-item"><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>

  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible fade show">
      <i class="fas fa-exclamation-triangle"></i>
      {{ error_warning }}
      <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    {% endif %}

    {% if success %}
    <div class="alert alert-success alert-dismissible fade show">
      <i class="fas fa-check-circle"></i>
      {{ success }}
      <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    {% endif %}

    <!-- Filter Panel -->
    <div class="filter-panel">
      <h4>{{ text_asset_filters }}</h4>
      <form id="assets-advanced-filter-form" method="post">
        <div class="row">
          <div class="col-md-2">
            <div class="form-group">
              <label for="category_id" class="form-label">{{ entry_category }}</label>
              <select name="category_id" id="category_id" class="form-control select2">
                <option value="">{{ text_all_categories }}</option>
                {% for category in categories %}
                <option value="{{ category.category_id }}"{% if category.category_id == category_id %} selected{% endif %}>
                  {{ category.name }}
                </option>
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="col-md-2">
            <div class="form-group">
              <label for="status" class="form-label">{{ entry_status }}</label>
              <select name="status" id="status" class="form-control">
                <option value="">{{ text_all_statuses }}</option>
                <option value="active"{% if status == 'active' %} selected{% endif %}>{{ text_active }}</option>
                <option value="disposed"{% if status == 'disposed' %} selected{% endif %}>{{ text_disposed }}</option>
                <option value="under_maintenance"{% if status == 'under_maintenance' %} selected{% endif %}>{{ text_under_maintenance }}</option>
              </select>
            </div>
          </div>
          <div class="col-md-2">
            <div class="form-group">
              <label for="location_id" class="form-label">{{ entry_location }}</label>
              <select name="location_id" id="location_id" class="form-control">
                <option value="">{{ text_all_locations }}</option>
                {% for location in locations %}
                <option value="{{ location.location_id }}"{% if location.location_id == location_id %} selected{% endif %}>{{ location.name }}</option>
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="col-md-2">
            <div class="form-group">
              <label for="department_id" class="form-label">{{ entry_department }}</label>
              <select name="department_id" id="department_id" class="form-control">
                <option value="">{{ text_all_departments }}</option>
                {% for department in departments %}
                <option value="{{ department.department_id }}"{% if department.department_id == department_id %} selected{% endif %}>{{ department.name }}</option>
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="col-md-2">
            <div class="form-group">
              <label for="depreciation_method" class="form-label">{{ entry_depreciation_method }}</label>
              <select name="depreciation_method" id="depreciation_method" class="form-control">
                <option value="">{{ text_all_methods }}</option>
                <option value="straight_line">{{ text_straight_line }}</option>
                <option value="declining_balance">{{ text_declining_balance }}</option>
                <option value="sum_of_years">{{ text_sum_of_years }}</option>
              </select>
            </div>
          </div>
          <div class="col-md-2">
            <div class="form-group">
              <label class="form-label">&nbsp;</label>
              <div class="d-grid">
                <button type="submit" class="btn btn-primary">
                  <i class="fas fa-search me-2"></i>{{ button_filter }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>

    <!-- Advanced Fixed Assets Content -->
    {% if assets %}
    <!-- Summary Cards -->
    <div class="assets-advanced-summary-cards">
      <div class="assets-advanced-summary-card card-total total">
        <h4>{{ text_total_assets }}</h4>
        <div class="amount">{{ summary.total_assets }}</div>
        <div class="description">{{ text_assets }}</div>
      </div>
      <div class="assets-advanced-summary-card card-cost cost">
        <h4>{{ text_total_cost }}</h4>
        <div class="amount">{{ summary.total_cost_formatted }}</div>
        <div class="description">{{ text_original_cost }}</div>
      </div>
      <div class="assets-advanced-summary-card card-depreciation depreciation">
        <h4>{{ text_total_depreciation }}</h4>
        <div class="amount">{{ summary.total_depreciation_formatted }}</div>
        <div class="description">{{ text_accumulated_depreciation }}</div>
      </div>
      <div class="assets-advanced-summary-card card-net net">
        <h4>{{ text_net_book_value }}</h4>
        <div class="amount">{{ summary.net_book_value_formatted }}</div>
        <div class="description">{{ text_current_value }}</div>
      </div>
    </div>

    <!-- Advanced Fixed Assets Table -->
    <div class="assets-advanced-container">
      <div class="assets-advanced-header">
        <h2>{{ text_fixed_assets_list }}</h2>
      </div>

      <div class="table-responsive">
        <table class="assets-advanced-table" id="assets-advanced-table">
          <thead>
            <tr>
              <th>{{ column_asset_name }}</th>
              <th>{{ column_asset_code }}</th>
              <th>{{ column_category }}</th>
              <th>{{ column_cost }}</th>
              <th>{{ column_accumulated_depreciation }}</th>
              <th>{{ column_book_value }}</th>
              <th>{{ column_depreciation_method }}</th>
              <th>{{ column_useful_life }}</th>
              <th>{{ column_status }}</th>
              <th>{{ column_action }}</th>
            </tr>
          </thead>
          <tbody>
            {% for asset in assets %}
            <tr data-asset-id="{{ asset.asset_id }}">
              <td>
                <strong>{{ asset.asset_name }}</strong>
                <br>
                <small class="text-muted">{{ asset.serial_number }}</small>
              </td>
              <td>{{ asset.asset_code }}</td>
              <td>{{ asset.category_name }}</td>
              <td class="amount-cell">
                <strong class="amount-neutral">{{ asset.cost_formatted }}</strong>
              </td>
              <td class="amount-cell">
                <span class="amount-negative">{{ asset.accumulated_depreciation_formatted }}</span>
              </td>
              <td class="amount-cell">
                <strong class="amount-positive">{{ asset.book_value_formatted }}</strong>
              </td>
              <td>{{ asset.depreciation_method_text }}</td>
              <td>{{ asset.useful_life }} {{ text_years }}</td>
              <td>
                <span class="badge bg-{% if asset.status == 'active' %}success{% elseif asset.status == 'disposed' %}danger{% elseif asset.status == 'under_maintenance' %}warning{% else %}secondary{% endif %}">
                  {{ asset.status_text }}
                </span>
              </td>
              <td>
                <div class="assets-advanced-actions">
                  <button type="button" class="btn btn-outline-info btn-sm"
                          onclick="viewAsset({{ asset.asset_id }})"
                          data-bs-toggle="tooltip" title="{{ text_view }}">
                    <i class="fas fa-eye"></i>
                  </button>
                  <button type="button" class="btn btn-outline-warning btn-sm"
                          onclick="editAsset({{ asset.asset_id }})"
                          data-bs-toggle="tooltip" title="{{ text_edit }}">
                    <i class="fas fa-edit"></i>
                  </button>
                  <button type="button" class="btn btn-outline-primary btn-sm"
                          onclick="calculateAssetDepreciation({{ asset.asset_id }})"
                          data-bs-toggle="tooltip" title="{{ text_calculate_depreciation }}">
                    <i class="fas fa-calculator"></i>
                  </button>
                  <button type="button" class="btn btn-outline-secondary btn-sm"
                          onclick="viewDepreciationSchedule({{ asset.asset_id }})"
                          data-bs-toggle="tooltip" title="{{ text_depreciation_schedule }}">
                    <i class="fas fa-calendar-alt"></i>
                  </button>
                  {% if asset.status == 'active' %}
                  <button type="button" class="btn btn-outline-success btn-sm"
                          onclick="transferAsset({{ asset.asset_id }})"
                          data-bs-toggle="tooltip" title="{{ text_transfer }}">
                    <i class="fas fa-exchange-alt"></i>
                  </button>
                  <button type="button" class="btn btn-outline-danger btn-sm"
                          onclick="disposeAsset({{ asset.asset_id }})"
                          data-bs-toggle="tooltip" title="{{ text_dispose }}">
                    <i class="fas fa-trash"></i>
                  </button>
                  {% endif %}
                </div>
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>

    <!-- Charts Section -->
    <div class="row">
      <div class="col-md-4">
        <div class="chart-container">
          <h4>{{ text_assets_by_category_chart }}</h4>
          <canvas id="assetsByCategoryChart"></canvas>
        </div>
      </div>
      <div class="col-md-4">
        <div class="chart-container">
          <h4>{{ text_depreciation_methods_chart }}</h4>
          <canvas id="depreciationMethodsChart"></canvas>
        </div>
      </div>
      <div class="col-md-4">
        <div class="chart-container">
          <h4>{{ text_asset_age_distribution_chart }}</h4>
          <canvas id="assetAgeChart"></canvas>
        </div>
      </div>
    </div>

    <!-- Depreciation Trend Chart -->
    <div class="row">
      <div class="col-md-12">
        <div class="chart-container">
          <h4>{{ text_depreciation_trend_chart }}</h4>
          <canvas id="depreciationTrendChart"></canvas>
        </div>
      </div>
    </div>
    {% else %}
    <div class="alert alert-info">
      <i class="fas fa-info-circle me-2"></i>
      {{ text_no_assets }}
    </div>
    {% endif %}
  </div>
</div>

<script>
// Enterprise Grade Plus JavaScript for Advanced Fixed Assets
class AdvancedFixedAssetsManager {
    constructor() {
        this.initializeTooltips();
        this.initializeDataTable();
        this.initializeKeyboardShortcuts();
        this.initializeCharts();
        this.initializeSelect2();
    }

    initializeTooltips() {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }

    initializeDataTable() {
        const table = document.getElementById('assets-advanced-table');
        if (table && typeof $ !== 'undefined' && $.fn.DataTable) {
            $(table).DataTable({
                responsive: true,
                pageLength: 25,
                order: [[0, 'asc']], // Sort by asset name asc
                columnDefs: [
                    { targets: [3, 4, 5], className: 'text-end' },
                    { targets: [9], orderable: false }
                ],
                language: {
                    url: '{{ direction == "rtl" ? "/view/javascript/jquery/datatables-ar.json" : "/view/javascript/jquery/datatables-en.json" }}'
                }
            });
        }
    }

    initializeKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case 'n':
                        e.preventDefault();
                        this.addAsset();
                        break;
                    case 'e':
                        e.preventDefault();
                        this.showExportMenu();
                        break;
                    case 'p':
                        e.preventDefault();
                        this.printAssets();
                        break;
                    case 'd':
                        e.preventDefault();
                        this.calculateDepreciation();
                        break;
                    case 's':
                        e.preventDefault();
                        this.showDepreciationSchedule();
                        break;
                }
            }
        });
    }

    initializeCharts() {
        if (typeof Chart !== 'undefined') {
            this.createAssetsByCategoryChart();
            this.createDepreciationMethodsChart();
            this.createAssetAgeChart();
            this.createDepreciationTrendChart();
        }
    }

    initializeSelect2() {
        if (typeof $ !== 'undefined' && $.fn.select2) {
            $('.select2').select2({
                theme: 'bootstrap-5',
                width: '100%'
            });
        }
    }

    addAsset() {
        window.open('{{ url_link('accounts/fixed_assets_advanced', 'add') }}', '_blank');
    }

    viewAsset(assetId) {
        window.open('{{ url_link('accounts/fixed_assets_advanced', 'view') }}&asset_id=' + assetId, '_blank');
    }

    editAsset(assetId) {
        window.open('{{ url_link('accounts/fixed_assets_advanced', 'edit') }}&asset_id=' + assetId, '_blank');
    }

    calculateAssetDepreciation(assetId) {
        this.showLoadingState(true);

        fetch('{{ url_link('accounts/fixed_assets_advanced', 'calculateDepreciation') }}', {
            method: 'POST',
            body: JSON.stringify({ asset_id: assetId }),
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            this.showLoadingState(false);
            if (data.success) {
                this.showAlert('{{ text_depreciation_calculated_success }}', 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                this.showAlert(data.error || '{{ error_calculate_depreciation }}', 'danger');
            }
        })
        .catch(error => {
            this.showLoadingState(false);
            this.showAlert('{{ error_calculate_depreciation }}: ' + error.message, 'danger');
        });
    }

    viewDepreciationSchedule(assetId) {
        window.open('{{ url_link('accounts/fixed_assets_advanced', 'viewSchedule') }}&asset_id=' + assetId, '_blank');
    }

    transferAsset(assetId) {
        window.open('{{ url_link('accounts/fixed_assets_advanced', 'transfer') }}&asset_id=' + assetId, '_blank');
    }

    disposeAsset(assetId) {
        if (confirm('{{ text_confirm_dispose }}')) {
            this.showLoadingState(true);

            fetch('{{ url_link('accounts/fixed_assets_advanced', 'dispose') }}', {
                method: 'POST',
                body: JSON.stringify({ asset_id: assetId }),
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                this.showLoadingState(false);
                if (data.success) {
                    this.showAlert('{{ text_asset_disposed_success }}', 'success');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    this.showAlert(data.error || '{{ error_dispose_asset }}', 'danger');
                }
            })
            .catch(error => {
                this.showLoadingState(false);
                this.showAlert('{{ error_dispose_asset }}: ' + error.message, 'danger');
            });
        }
    }

    calculateDepreciation() {
        this.showLoadingState(true);

        fetch('{{ url_link('accounts/fixed_assets_advanced', 'calculateAllDepreciation') }}', {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            this.showLoadingState(false);
            if (data.success) {
                this.showAlert('{{ text_all_depreciation_calculated }}', 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                this.showAlert(data.error || '{{ error_calculate_depreciation }}', 'danger');
            }
        })
        .catch(error => {
            this.showLoadingState(false);
            this.showAlert('{{ error_calculate_depreciation }}: ' + error.message, 'danger');
        });
    }

    showDepreciationSchedule() {
        window.open('{{ url_link('accounts/fixed_assets_advanced', 'depreciationSchedule') }}', '_blank');
    }

    exportAssets(format) {
        const params = new URLSearchParams({
            format: format,
            category_id: document.getElementById('category_id').value,
            status: document.getElementById('status').value,
            location_id: document.getElementById('location_id').value,
            department_id: document.getElementById('department_id').value,
            depreciation_method: document.getElementById('depreciation_method').value
        });

        this.showAlert('{{ text_exporting }}...', 'info');
        window.open('{{ export_url }}&' + params.toString(), '_blank');
    }

    printAssets() {
        window.print();
    }

    createAssetsByCategoryChart() {
        const ctx = document.getElementById('assetsByCategoryChart');
        if (!ctx) return;

        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: {{ category_names|json_encode|raw }},
                datasets: [{
                    data: {{ category_values|json_encode|raw }},
                    backgroundColor: ['#8e44ad', '#3498db', '#e74c3c', '#f39c12', '#27ae60'],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: '{{ text_assets_by_category_chart }}'
                    },
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    createDepreciationMethodsChart() {
        const ctx = document.getElementById('depreciationMethodsChart');
        if (!ctx) return;

        new Chart(ctx, {
            type: 'pie',
            data: {
                labels: {{ depreciation_method_names|json_encode|raw }},
                datasets: [{
                    data: {{ depreciation_method_values|json_encode|raw }},
                    backgroundColor: ['#2c3e50', '#e67e22', '#9b59b6'],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: '{{ text_depreciation_methods_chart }}'
                    },
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    createAssetAgeChart() {
        const ctx = document.getElementById('assetAgeChart');
        if (!ctx) return;

        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: {{ age_ranges|json_encode|raw }},
                datasets: [{
                    label: '{{ text_assets }}',
                    data: {{ age_values|json_encode|raw }},
                    backgroundColor: '#17a2b8',
                    borderColor: '#138496',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: '{{ text_asset_age_distribution_chart }}'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    createDepreciationTrendChart() {
        const ctx = document.getElementById('depreciationTrendChart');
        if (!ctx) return;

        new Chart(ctx, {
            type: 'line',
            data: {
                labels: {{ depreciation_months|json_encode|raw }},
                datasets: [{
                    label: '{{ text_monthly_depreciation }}',
                    data: {{ depreciation_values|json_encode|raw }},
                    borderColor: '#e74c3c',
                    backgroundColor: 'rgba(231, 76, 60, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: '{{ text_depreciation_trend_chart }}'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    showExportMenu() {
        const exportButton = document.querySelector('.dropdown-toggle');
        if (exportButton) {
            exportButton.click();
        }
    }

    showLoadingState(show) {
        const buttons = document.querySelectorAll('.btn');
        buttons.forEach(btn => {
            if (show) {
                btn.disabled = true;
            } else {
                btn.disabled = false;
            }
        });
    }

    showAlert(message, type) {
        const alertContainer = document.createElement('div');
        alertContainer.className = `alert alert-${type} alert-dismissible fade show`;
        alertContainer.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'}"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        const container = document.querySelector('#content .container-fluid');
        container.insertBefore(alertContainer, container.firstChild);

        setTimeout(() => {
            alertContainer.remove();
        }, 5000);
    }
}

// Global functions for backward compatibility
function addAsset() {
    advancedFixedAssetsManager.addAsset();
}

function viewAsset(assetId) {
    advancedFixedAssetsManager.viewAsset(assetId);
}

function editAsset(assetId) {
    advancedFixedAssetsManager.editAsset(assetId);
}

function calculateAssetDepreciation(assetId) {
    advancedFixedAssetsManager.calculateAssetDepreciation(assetId);
}

function viewDepreciationSchedule(assetId) {
    advancedFixedAssetsManager.viewDepreciationSchedule(assetId);
}

function transferAsset(assetId) {
    advancedFixedAssetsManager.transferAsset(assetId);
}

function disposeAsset(assetId) {
    advancedFixedAssetsManager.disposeAsset(assetId);
}

function calculateDepreciation() {
    advancedFixedAssetsManager.calculateDepreciation();
}

function showDepreciationSchedule() {
    advancedFixedAssetsManager.showDepreciationSchedule();
}

function exportAssets(format) {
    advancedFixedAssetsManager.exportAssets(format);
}

function printAssets() {
    advancedFixedAssetsManager.printAssets();
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.advancedFixedAssetsManager = new AdvancedFixedAssetsManager();
});
</script>

{{ footer }}
