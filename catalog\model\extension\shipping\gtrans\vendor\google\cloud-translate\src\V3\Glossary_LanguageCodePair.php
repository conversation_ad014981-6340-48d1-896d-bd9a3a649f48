<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/cloud/translate/v3/translation_service.proto

namespace Google\Cloud\Translate\V3;

if (false) {
    /**
     * This class is deprecated. Use Google\Cloud\Translate\V3\Glossary\LanguageCodePair instead.
     * @deprecated
     */
    class Glossary_LanguageCodePair {}
}
class_exists(Glossary\LanguageCodePair::class);
@trigger_error('Google\Cloud\Translate\V3\Glossary_LanguageCodePair is deprecated and will be removed in a future release. Use Google\Cloud\Translate\V3\Glossary\LanguageCodePair instead', E_USER_DEPRECATED);

