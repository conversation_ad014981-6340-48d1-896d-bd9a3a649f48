<?php
/**
 * Tax Compliance Egypt Model - ETA Integration
 * Enterprise Grade Plus - Egyptian Tax Authority Integration
 * Compliant with https://sdk.invoice.eta.gov.eg/start
 */
class ModelAccountsTaxComplianceEgypt extends Model {
    
    /**
     * Get ETA connection status
     */
    public function getETAConnectionStatus() {
        $cache_key = 'eta_connection_status';
        
        $cached_result = $this->cache->get($cache_key);
        if ($cached_result !== false) {
            return $cached_result;
        }
        
        try {
            $connection_test = $this->testETAConnection();
            
            $status = array(
                'connected' => $connection_test['success'],
                'last_check' => date('Y-m-d H:i:s'),
                'api_url' => $this->config->get('eta_api_url'),
                'environment' => $this->config->get('eta_environment'),
                'tax_number' => $this->config->get('eta_tax_number'),
                'status_message' => $connection_test['success'] ? 'Connected' : $connection_test['error']
            );
            
            $this->cache->set($cache_key, $status, 300); // Cache for 5 minutes
            
            return $status;
            
        } catch (Exception $e) {
            return array(
                'connected' => false,
                'last_check' => date('Y-m-d H:i:s'),
                'status_message' => $e->getMessage()
            );
        }
    }
    
    /**
     * Get compliance dashboard data
     */
    public function getComplianceDashboard() {
        $cache_key = 'eta_compliance_dashboard';
        
        $cached_result = $this->cache->get($cache_key);
        if ($cached_result !== false) {
            return $cached_result;
        }
        
        $dashboard = array();
        
        // Get invoice submission statistics
        $invoice_stats = $this->db->query("
            SELECT 
                COUNT(*) as total_invoices,
                COUNT(CASE WHEN eta_status = 'submitted' THEN 1 END) as submitted_invoices,
                COUNT(CASE WHEN eta_status = 'pending' THEN 1 END) as pending_invoices,
                COUNT(CASE WHEN eta_status = 'failed' THEN 1 END) as failed_invoices,
                COUNT(CASE WHEN eta_status IS NULL THEN 1 END) as not_submitted_invoices
            FROM " . DB_PREFIX . "order 
            WHERE date_added >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        ");
        
        $dashboard['invoice_stats'] = $invoice_stats->row;
        
        // Get VAT return status
        $vat_return_stats = $this->db->query("
            SELECT 
                COUNT(*) as total_returns,
                COUNT(CASE WHEN status = 'submitted' THEN 1 END) as submitted_returns,
                COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_returns
            FROM " . DB_PREFIX . "eta_vat_returns 
            WHERE created_date >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
        ");
        
        $dashboard['vat_return_stats'] = $vat_return_stats->row;
        
        // Get compliance score
        $total_invoices = $dashboard['invoice_stats']['total_invoices'];
        $submitted_invoices = $dashboard['invoice_stats']['submitted_invoices'];
        
        $dashboard['compliance_score'] = $total_invoices > 0 ? 
            round(($submitted_invoices / $total_invoices) * 100, 2) : 100;
        
        // Get recent errors
        $recent_errors = $this->db->query("
            SELECT 
                error_message,
                error_code,
                created_date,
                COUNT(*) as occurrence_count
            FROM " . DB_PREFIX . "eta_submission_log 
            WHERE status = 'error' 
            AND created_date >= DATE_SUB(NOW(), INTERVAL 7 DAY)
            GROUP BY error_code, error_message
            ORDER BY occurrence_count DESC, created_date DESC
            LIMIT 5
        ");
        
        $dashboard['recent_errors'] = $recent_errors->rows;
        
        $this->cache->set($cache_key, $dashboard, 1800); // Cache for 30 minutes
        
        return $dashboard;
    }
    
    /**
     * Get pending submissions
     */
    public function getPendingSubmissions() {
        $query = $this->db->query("
            SELECT 
                o.order_id,
                o.invoice_no,
                o.total,
                o.date_added,
                o.eta_status,
                o.eta_error_message,
                CONCAT(o.firstname, ' ', o.lastname) as customer_name
            FROM " . DB_PREFIX . "order o
            WHERE o.eta_status IN ('pending', 'failed', NULL)
            AND o.order_status_id IN (2, 3, 5) -- Completed orders
            ORDER BY o.date_added DESC
            LIMIT 20
        ");
        
        return $query->rows;
    }
    
    /**
     * Get recent activities
     */
    public function getRecentActivities() {
        $query = $this->db->query("
            SELECT 
                activity_type,
                description,
                status,
                created_date,
                user_id,
                reference_id
            FROM " . DB_PREFIX . "eta_activity_log
            ORDER BY created_date DESC
            LIMIT 10
        ");
        
        return $query->rows;
    }
    
    /**
     * Submit invoices to ETA
     */
    public function submitInvoicesToETA($filter_data) {
        try {
            // Get invoices to submit
            $invoices = $this->getInvoicesForSubmission($filter_data);
            
            $submitted_count = 0;
            $failed_count = 0;
            $errors = array();
            
            foreach ($invoices as $invoice) {
                try {
                    $eta_invoice = $this->convertToETAFormat($invoice);
                    $result = $this->submitSingleInvoiceToETA($eta_invoice);
                    
                    if ($result['success']) {
                        $this->updateInvoiceETAStatus($invoice['order_id'], 'submitted', $result['eta_uuid']);
                        $submitted_count++;
                    } else {
                        $this->updateInvoiceETAStatus($invoice['order_id'], 'failed', null, $result['error']);
                        $failed_count++;
                        $errors[] = $result['error'];
                    }
                    
                } catch (Exception $e) {
                    $this->updateInvoiceETAStatus($invoice['order_id'], 'failed', null, $e->getMessage());
                    $failed_count++;
                    $errors[] = $e->getMessage();
                }
            }
            
            // Log submission activity
            $this->logETAActivity('invoice_submission', 'Bulk invoice submission', 'completed', array(
                'submitted_count' => $submitted_count,
                'failed_count' => $failed_count,
                'total_count' => count($invoices)
            ));
            
            return array(
                'success' => true,
                'data' => array(
                    'submitted_count' => $submitted_count,
                    'failed_count' => $failed_count,
                    'total_count' => count($invoices),
                    'errors' => $errors
                )
            );
            
        } catch (Exception $e) {
            return array(
                'success' => false,
                'error' => $e->getMessage()
            );
        }
    }
    
    /**
     * Submit VAT return to ETA
     */
    public function submitVATReturnToETA($period_data) {
        try {
            // Calculate VAT return data
            $vat_data = $this->calculateVATReturnData($period_data);
            
            // Convert to ETA format
            $eta_vat_return = $this->convertVATReturnToETAFormat($vat_data, $period_data);
            
            // Submit to ETA
            $result = $this->submitVATReturnToETAAPI($eta_vat_return);
            
            if ($result['success']) {
                // Save VAT return record
                $vat_return_id = $this->saveVATReturnRecord($period_data, $vat_data, $result['submission_id']);
                
                // Log activity
                $this->logETAActivity('vat_return_submission', 'VAT return submitted', 'completed', array(
                    'period' => $period_data['year'] . '-' . $period_data['month'],
                    'submission_id' => $result['submission_id'],
                    'vat_return_id' => $vat_return_id
                ));
                
                return array(
                    'success' => true,
                    'data' => array(
                        'submission_id' => $result['submission_id'],
                        'vat_return_id' => $vat_return_id,
                        'total_vat' => $vat_data['total_vat'],
                        'total_sales' => $vat_data['total_sales']
                    )
                );
            } else {
                return array(
                    'success' => false,
                    'error' => $result['error']
                );
            }
            
        } catch (Exception $e) {
            return array(
                'success' => false,
                'error' => $e->getMessage()
            );
        }
    }
    
    /**
     * Sync ETA codes
     */
    public function syncETACodes() {
        try {
            $codes = $this->fetchETACodesFromAPI();
            
            $synced_count = 0;
            
            foreach ($codes as $code_type => $code_list) {
                foreach ($code_list as $code) {
                    $this->saveETACode($code_type, $code);
                    $synced_count++;
                }
            }
            
            // Log sync activity
            $this->logETAActivity('code_sync', 'ETA codes synchronized', 'completed', array(
                'synced_count' => $synced_count
            ));
            
            return array(
                'success' => true,
                'data' => array(
                    'synced_count' => $synced_count
                )
            );
            
        } catch (Exception $e) {
            return array(
                'success' => false,
                'error' => $e->getMessage()
            );
        }
    }
    
    /**
     * Test ETA connection
     */
    public function testETAConnection() {
        try {
            $api_url = $this->config->get('eta_api_url');
            $client_id = $this->config->get('eta_client_id');
            $client_secret = $this->config->get('eta_client_secret');
            
            if (empty($api_url) || empty($client_id) || empty($client_secret)) {
                throw new Exception('ETA configuration is incomplete');
            }
            
            // Test authentication
            $auth_result = $this->authenticateWithETA();
            
            if ($auth_result['success']) {
                return array(
                    'success' => true,
                    'data' => array(
                        'status' => 'connected',
                        'api_url' => $api_url,
                        'authenticated' => true,
                        'test_time' => date('Y-m-d H:i:s')
                    )
                );
            } else {
                return array(
                    'success' => false,
                    'error' => $auth_result['error']
                );
            }
            
        } catch (Exception $e) {
            return array(
                'success' => false,
                'error' => $e->getMessage()
            );
        }
    }
    
    /**
     * Authenticate with ETA API
     */
    private function authenticateWithETA() {
        try {
            $api_url = $this->config->get('eta_api_url');
            $client_id = $this->config->get('eta_client_id');
            $client_secret = $this->config->get('eta_client_secret');
            
            $auth_data = array(
                'grant_type' => 'client_credentials',
                'client_id' => $client_id,
                'client_secret' => $client_secret,
                'scope' => 'InvoicingAPI'
            );
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $api_url . '/connect/token');
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($auth_data));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                'Content-Type: application/x-www-form-urlencoded'
            ));
            
            $response = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($http_code === 200) {
                $auth_response = json_decode($response, true);
                
                if (isset($auth_response['access_token'])) {
                    // Cache the token
                    $this->cache->set('eta_access_token', $auth_response['access_token'], 
                        $auth_response['expires_in'] - 60); // Cache for token lifetime minus 1 minute
                    
                    return array(
                        'success' => true,
                        'access_token' => $auth_response['access_token']
                    );
                } else {
                    return array(
                        'success' => false,
                        'error' => 'Invalid authentication response'
                    );
                }
            } else {
                return array(
                    'success' => false,
                    'error' => 'Authentication failed with HTTP code: ' . $http_code
                );
            }
            
        } catch (Exception $e) {
            return array(
                'success' => false,
                'error' => $e->getMessage()
            );
        }
    }
    
    /**
     * Get invoices for submission
     */
    private function getInvoicesForSubmission($filter_data) {
        $sql = "
            SELECT 
                o.*,
                op.product_id,
                op.name as product_name,
                op.quantity,
                op.price,
                op.total as line_total,
                op.tax as line_tax
            FROM " . DB_PREFIX . "order o
            LEFT JOIN " . DB_PREFIX . "order_product op ON o.order_id = op.order_id
            WHERE o.order_status_id IN (2, 3, 5)
            AND (o.eta_status IS NULL OR o.eta_status = 'failed')
            AND o.date_added BETWEEN '" . $this->db->escape($filter_data['date_start']) . "' 
            AND '" . $this->db->escape($filter_data['date_end']) . "'
        ";
        
        if ($filter_data['invoice_type'] !== 'all') {
            $sql .= " AND o.invoice_type = '" . $this->db->escape($filter_data['invoice_type']) . "'";
        }
        
        $sql .= " ORDER BY o.date_added ASC";
        
        $query = $this->db->query($sql);
        
        // Group by order_id
        $invoices = array();
        foreach ($query->rows as $row) {
            if (!isset($invoices[$row['order_id']])) {
                $invoices[$row['order_id']] = $row;
                $invoices[$row['order_id']]['products'] = array();
            }
            
            if ($row['product_id']) {
                $invoices[$row['order_id']]['products'][] = array(
                    'product_id' => $row['product_id'],
                    'name' => $row['product_name'],
                    'quantity' => $row['quantity'],
                    'price' => $row['price'],
                    'total' => $row['line_total'],
                    'tax' => $row['line_tax']
                );
            }
        }
        
        return array_values($invoices);
    }
    
    /**
     * Convert invoice to ETA format
     */
    private function convertToETAFormat($invoice) {
        $eta_invoice = array(
            'issuer' => array(
                'address' => array(
                    'branchID' => $this->config->get('eta_branch_id') ?: '0',
                    'country' => 'EG',
                    'governate' => $this->config->get('company_governate'),
                    'regionCity' => $this->config->get('company_city'),
                    'street' => $this->config->get('company_address'),
                    'buildingNumber' => $this->config->get('company_building_number'),
                    'postalCode' => $this->config->get('company_postal_code'),
                    'floor' => $this->config->get('company_floor'),
                    'room' => $this->config->get('company_room'),
                    'landmark' => $this->config->get('company_landmark'),
                    'additionalInformation' => $this->config->get('company_additional_info')
                ),
                'type' => 'B', // Business
                'id' => $this->config->get('eta_tax_number'),
                'name' => $this->config->get('config_name')
            ),
            'receiver' => array(
                'address' => array(
                    'country' => 'EG',
                    'governate' => $invoice['shipping_zone'],
                    'regionCity' => $invoice['shipping_city'],
                    'street' => $invoice['shipping_address_1'],
                    'buildingNumber' => '',
                    'postalCode' => $invoice['shipping_postcode']
                ),
                'type' => $invoice['customer_group_id'] == 1 ? 'P' : 'B', // Person or Business
                'id' => $invoice['tax_id'] ?: $invoice['telephone'],
                'name' => $invoice['firstname'] . ' ' . $invoice['lastname']
            ),
            'documentType' => 'I', // Invoice
            'documentTypeVersion' => '1.0',
            'dateTimeIssued' => date('c', strtotime($invoice['date_added'])),
            'taxpayerActivityCode' => $this->config->get('eta_activity_code'),
            'internalID' => $invoice['invoice_no'],
            'invoiceLines' => array()
        );

        // Add invoice lines
        foreach ($invoice['products'] as $product) {
            $eta_invoice['invoiceLines'][] = array(
                'description' => $product['name'],
                'itemType' => 'GS1', // Goods
                'itemCode' => $product['product_id'],
                'unitType' => 'EA', // Each
                'quantity' => (float)$product['quantity'],
                'unitValue' => array(
                    'currencySold' => $this->config->get('config_currency'),
                    'amountEGP' => (float)$product['price']
                ),
                'salesTotal' => (float)$product['total'],
                'total' => (float)($product['total'] + $product['tax']),
                'valueDifference' => 0,
                'totalTaxableFees' => 0,
                'netTotal' => (float)$product['total'],
                'itemsDiscount' => 0,
                'taxableItems' => array(
                    array(
                        'taxType' => 'T1', // VAT
                        'amount' => (float)$product['tax'],
                        'subType' => 'V009', // 14% VAT
                        'rate' => 14
                    )
                )
            );
        }

        return $eta_invoice;
    }

    /**
     * Submit single invoice to ETA
     */
    private function submitSingleInvoiceToETA($eta_invoice) {
        try {
            $access_token = $this->getETAAccessToken();

            if (!$access_token) {
                throw new Exception('Failed to get ETA access token');
            }

            $api_url = $this->config->get('eta_api_url');

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $api_url . '/api/v1/documentsubmissions');
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode(array('documents' => array($eta_invoice))));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                'Content-Type: application/json',
                'Authorization: Bearer ' . $access_token
            ));

            $response = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($http_code === 202) { // Accepted
                $response_data = json_decode($response, true);

                return array(
                    'success' => true,
                    'eta_uuid' => $response_data['submissionId'],
                    'status' => 'submitted'
                );
            } else {
                $error_data = json_decode($response, true);
                return array(
                    'success' => false,
                    'error' => $error_data['error']['message'] ?? 'Unknown error'
                );
            }

        } catch (Exception $e) {
            return array(
                'success' => false,
                'error' => $e->getMessage()
            );
        }
    }

    /**
     * Get ETA access token
     */
    private function getETAAccessToken() {
        $cached_token = $this->cache->get('eta_access_token');

        if ($cached_token) {
            return $cached_token;
        }

        $auth_result = $this->authenticateWithETA();

        if ($auth_result['success']) {
            return $auth_result['access_token'];
        }

        return false;
    }

    /**
     * Update invoice ETA status
     */
    private function updateInvoiceETAStatus($order_id, $status, $eta_uuid = null, $error_message = null) {
        $sql = "UPDATE " . DB_PREFIX . "order
                SET eta_status = '" . $this->db->escape($status) . "',
                    eta_submission_date = NOW()";

        if ($eta_uuid) {
            $sql .= ", eta_uuid = '" . $this->db->escape($eta_uuid) . "'";
        }

        if ($error_message) {
            $sql .= ", eta_error_message = '" . $this->db->escape($error_message) . "'";
        }

        $sql .= " WHERE order_id = '" . (int)$order_id . "'";

        $this->db->query($sql);

        // Log submission
        $this->db->query("
            INSERT INTO " . DB_PREFIX . "eta_submission_log
            SET order_id = '" . (int)$order_id . "',
                status = '" . $this->db->escape($status) . "',
                eta_uuid = '" . $this->db->escape($eta_uuid) . "',
                error_message = '" . $this->db->escape($error_message) . "',
                created_date = NOW()
        ");
    }

    /**
     * Calculate VAT return data
     */
    private function calculateVATReturnData($period_data) {
        $start_date = $period_data['year'] . '-' . str_pad($period_data['month'], 2, '0', STR_PAD_LEFT) . '-01';
        $end_date = date('Y-m-t', strtotime($start_date));

        // Calculate sales VAT
        $sales_query = $this->db->query("
            SELECT
                SUM(o.total) as total_sales,
                SUM(ot.value) as total_vat
            FROM " . DB_PREFIX . "order o
            LEFT JOIN " . DB_PREFIX . "order_total ot ON o.order_id = ot.order_id AND ot.code = 'tax'
            WHERE o.order_status_id IN (2, 3, 5)
            AND o.date_added BETWEEN '" . $this->db->escape($start_date) . "' AND '" . $this->db->escape($end_date) . "'
        ");

        // Calculate purchase VAT
        $purchase_query = $this->db->query("
            SELECT
                SUM(po.total) as total_purchases,
                SUM(pot.value) as total_purchase_vat
            FROM " . DB_PREFIX . "purchase_order po
            LEFT JOIN " . DB_PREFIX . "purchase_order_total pot ON po.purchase_order_id = pot.purchase_order_id AND pot.code = 'tax'
            WHERE po.status = 'completed'
            AND po.date_added BETWEEN '" . $this->db->escape($start_date) . "' AND '" . $this->db->escape($end_date) . "'
        ");

        $sales_data = $sales_query->row;
        $purchase_data = $purchase_query->row;

        return array(
            'period_start' => $start_date,
            'period_end' => $end_date,
            'total_sales' => (float)$sales_data['total_sales'],
            'total_vat_collected' => (float)$sales_data['total_vat'],
            'total_purchases' => (float)$purchase_data['total_purchases'],
            'total_vat_paid' => (float)$purchase_data['total_purchase_vat'],
            'net_vat_due' => (float)$sales_data['total_vat'] - (float)$purchase_data['total_purchase_vat']
        );
    }

    /**
     * Log ETA activity
     */
    private function logETAActivity($activity_type, $description, $status, $data = array()) {
        $this->db->query("
            INSERT INTO " . DB_PREFIX . "eta_activity_log
            SET activity_type = '" . $this->db->escape($activity_type) . "',
                description = '" . $this->db->escape($description) . "',
                status = '" . $this->db->escape($status) . "',
                data = '" . $this->db->escape(json_encode($data)) . "',
                user_id = '" . (int)$this->user->getId() . "',
                created_date = NOW()
        ");
    }
}
?>
