<?php
/**
 * English Language File for Advanced Product Management in Inventory
 * 
 * <AUTHOR> Team
 * @version 1.0
 * @since 2024
 */

// Main Title
$_['heading_title'] = 'Advanced Product Management';

// Basic Texts
$_['text_success'] = 'Success: You have modified products!';
$_['text_list'] = 'Product List';
$_['text_add'] = 'Add Product';
$_['text_edit'] = 'Edit Product';
$_['text_default'] = 'Default';
$_['text_enabled'] = 'Enabled';
$_['text_disabled'] = 'Disabled';
$_['text_yes'] = 'Yes';
$_['text_no'] = 'No';
$_['text_plus'] = '+';
$_['text_minus'] = '-';
$_['text_none'] = '--- None ---';
$_['text_select'] = '--- Please Select ---';
$_['text_all_zones'] = 'All Zones';

// Tabs
$_['tab_general'] = 'General';
$_['tab_data'] = 'Data';
$_['tab_links'] = 'Links';
$_['tab_attribute'] = 'Attribute';
$_['tab_option'] = 'Option';
$_['tab_recurring'] = 'Recurring';
$_['tab_discount'] = 'Discount';
$_['tab_special'] = 'Special';
$_['tab_image'] = 'Image';
$_['tab_reward'] = 'Reward Points';
$_['tab_seo'] = 'SEO';
$_['tab_design'] = 'Design';

// Advanced New Tabs
$_['tab_pricing'] = 'Advanced Pricing';
$_['tab_units'] = 'Advanced Units';
$_['tab_barcode'] = 'Advanced Barcode';
$_['tab_options'] = 'Linked Options';
$_['tab_bundles'] = 'Bundles & Offers';
$_['tab_discounts'] = 'Advanced Discounts';
$_['tab_inventory'] = 'Inventory Management';
$_['tab_variants'] = 'Variants (Sizes & Colors)';
$_['tab_images'] = 'Multiple Images';
$_['tab_batches'] = 'Batches & Expiry Dates';

// Information Sections
$_['text_product_info'] = 'Product Information';
$_['text_categorization'] = 'Categorization & Brand';
$_['text_product_image'] = 'Product Image';
$_['text_technical_info'] = 'Technical Information';
$_['text_status_settings'] = 'Status Settings';
$_['text_advanced_pricing'] = 'Advanced Pricing (5 Levels)';
$_['text_advanced_units'] = 'Advanced Units';
$_['text_advanced_barcode'] = 'Advanced Barcode';

// Table Columns
$_['column_image'] = 'Image';
$_['column_name'] = 'Product Name';
$_['column_model'] = 'Model';
$_['column_price'] = 'Price';
$_['column_quantity'] = 'Quantity';
$_['column_status'] = 'Status';
$_['column_action'] = 'Action';

// Advanced Pricing Columns
$_['column_unit'] = 'Unit';
$_['column_cost_price'] = 'Cost Price';
$_['column_base_price'] = 'Base Price';
$_['column_special_price'] = 'Special Price';
$_['column_wholesale_price'] = 'Wholesale Price';
$_['column_half_wholesale_price'] = 'Half Wholesale Price';
$_['column_custom_price'] = 'Custom Price';
$_['column_profit_margin'] = 'Profit Margin';

// Unit Columns
$_['column_unit_name'] = 'Unit Name';
$_['column_unit_type'] = 'Unit Type';
$_['column_conversion_factor'] = 'Conversion Factor';
$_['column_is_base_unit'] = 'Base Unit';
$_['column_sort_order'] = 'Sort Order';

// Barcode Columns
$_['column_barcode'] = 'Barcode';
$_['column_barcode_type'] = 'Barcode Type';
$_['column_linked_unit'] = 'Linked Unit';
$_['column_linked_option'] = 'Linked Option';
$_['column_is_primary'] = 'Primary Barcode';

// Input Fields
$_['entry_name'] = 'Product Name';
$_['entry_description'] = 'Description';
$_['entry_meta_title'] = 'Meta Tag Title';
$_['entry_meta_keyword'] = 'Meta Tag Keywords';
$_['entry_meta_description'] = 'Meta Tag Description';
$_['entry_keyword'] = 'SEO URL';
$_['entry_model'] = 'Model';
$_['entry_sku'] = 'SKU';
$_['entry_upc'] = 'UPC';
$_['entry_ean'] = 'EAN';
$_['entry_jan'] = 'JAN';
$_['entry_isbn'] = 'ISBN';
$_['entry_mpn'] = 'MPN';
$_['entry_location'] = 'Location';
$_['entry_shipping'] = 'Requires Shipping';
$_['entry_manufacturer'] = 'Manufacturer';
$_['entry_store'] = 'Stores';
$_['entry_date_available'] = 'Date Available';
$_['entry_dimension'] = 'Dimensions (L x W x H)';
$_['entry_length'] = 'Length';
$_['entry_width'] = 'Width';
$_['entry_height'] = 'Height';
$_['entry_image'] = 'Image';
$_['entry_additional_image'] = 'Additional Images';
$_['entry_customer_group'] = 'Customer Group';
$_['entry_date_start'] = 'Date Start';
$_['entry_date_end'] = 'Date End';
$_['entry_priority'] = 'Priority';
$_['entry_attribute'] = 'Attribute';
$_['entry_attribute_group'] = 'Attribute Group';
$_['entry_text'] = 'Text';
$_['entry_option'] = 'Option';
$_['entry_option_value'] = 'Option Value';
$_['entry_required'] = 'Required';
$_['entry_status'] = 'Status';
$_['entry_sort_order'] = 'Sort Order';
$_['entry_category'] = 'Categories';
$_['entry_filter'] = 'Filters';
$_['entry_download'] = 'Downloads';
$_['entry_related'] = 'Related Products';
$_['entry_tag'] = 'Product Tags';
$_['entry_reward'] = 'Reward Points';
$_['entry_layout'] = 'Layout Override';
$_['entry_recurring'] = 'Recurring Profile';

// Advanced Filters
$_['text_advanced_filters'] = 'Advanced Filters';
$_['text_all_status'] = 'All Status';
$_['text_all_categories'] = 'All Categories';
$_['text_all_manufacturers'] = 'All Manufacturers';
$_['text_all_stock_status'] = 'All Stock Status';
$_['text_low_stock'] = 'Low Stock';
$_['text_out_of_stock'] = 'Out of Stock';
$_['text_in_stock'] = 'In Stock';

// Statistics
$_['text_total_products'] = 'Total Products';
$_['text_active_products'] = 'Active Products';
$_['text_low_stock_products'] = 'Low Stock Products';
$_['text_out_of_stock_products'] = 'Out of Stock Products';

// Unit Types
$_['text_base_unit'] = 'Base Unit';
$_['text_sub_unit'] = 'Sub Unit';
$_['text_super_unit'] = 'Super Unit';

// Barcode Types
$_['text_barcode_ean13'] = 'EAN-13';
$_['text_barcode_ean8'] = 'EAN-8';
$_['text_barcode_upc'] = 'UPC';
$_['text_barcode_code128'] = 'CODE-128';
$_['text_barcode_code39'] = 'CODE-39';
$_['text_barcode_isbn'] = 'ISBN';

// Buttons
$_['button_add'] = 'Add';
$_['button_edit'] = 'Edit';
$_['button_delete'] = 'Delete';
$_['button_save'] = 'Save';
$_['button_cancel'] = 'Cancel';
$_['button_clear'] = 'Clear';
$_['button_close'] = 'Close';
$_['button_filter'] = 'Filter';
$_['button_send'] = 'Send';
$_['button_copy'] = 'Copy';
$_['button_back'] = 'Back';
$_['button_remove'] = 'Remove';
$_['button_refresh'] = 'Refresh';
$_['button_backup'] = 'Backup';
$_['button_restore'] = 'Restore';
$_['button_download'] = 'Download';
$_['button_rebuild'] = 'Rebuild';
$_['button_upload'] = 'Upload';

// Advanced Buttons
$_['button_add_pricing'] = 'Add Pricing';
$_['button_add_unit'] = 'Add Unit';
$_['button_add_barcode'] = 'Add Barcode';
$_['button_add_option'] = 'Add Option';
$_['button_add_bundle'] = 'Add Bundle';
$_['button_add_discount'] = 'Add Discount';
$_['button_generate_code'] = 'Generate Code';
$_['button_generate_barcode'] = 'Generate Barcode';
$_['button_print_barcode'] = 'Print Barcode';

// Interactive Texts
$_['text_view_movements'] = 'View Movements';
$_['text_view_pricing'] = 'View Pricing';
$_['text_print_barcode'] = 'Print Barcode';
$_['text_confirm'] = 'Are you sure?';
$_['text_confirm_copy'] = 'Do you want to copy this product?';
$_['text_no_results'] = 'No results!';

// Help Messages
$_['help_keyword'] = 'Do not use spaces, instead replace spaces with - and make sure the SEO URL is globally unique.';
$_['help_sku'] = 'Stock Keeping Unit';
$_['help_upc'] = 'Universal Product Code';
$_['help_ean'] = 'European Article Number';
$_['help_jan'] = 'Japanese Article Number';
$_['help_isbn'] = 'International Standard Book Number';
$_['help_mpn'] = 'Manufacturer Part Number';
$_['help_manufacturer'] = '(Autocomplete)';
$_['help_minimum'] = 'Force a minimum ordered amount';
$_['help_stock_status'] = 'Status shown when a product is out of stock';
$_['help_points'] = 'Number of points needed to buy this item. If you don\'t want this product to be purchased with points leave as 0.';
$_['help_category'] = '(Autocomplete)';
$_['help_filter'] = '(Autocomplete)';
$_['help_download'] = '(Autocomplete)';
$_['help_related'] = '(Autocomplete)';
$_['help_tag'] = 'Comma separated';

// Advanced Help
$_['help_advanced_pricing'] = 'You can set 5 different price levels for each unit: Cost, Base, Special, Wholesale, Half Wholesale, and Custom.';
$_['help_advanced_units'] = 'You can set multiple units for the product with automatic conversion factors between them.';
$_['help_advanced_barcode'] = 'You can create multiple barcodes for one product linked to different units and options.';

// Error Messages
$_['error_warning'] = 'Warning: Please check the form carefully for errors!';
$_['error_permission'] = 'Warning: You do not have permission to modify products!';
$_['error_name'] = 'Product Name must be between 1 and 255 characters!';
$_['error_meta_title'] = 'Meta Title must be between 1 and 255 characters!';
$_['error_model'] = 'Product Model must be between 1 and 64 characters!';
$_['error_keyword'] = 'SEO URL already in use!';
$_['error_unique'] = 'SEO URL must be unique!';

// Success Messages
$_['text_success_add'] = 'Success: You have added the product!';
$_['text_success_edit'] = 'Success: You have modified the product!';
$_['text_success_delete'] = 'Success: You have deleted selected products!';
$_['text_success_copy'] = 'Success: You have copied the product!';

// Additional Texts for Advanced Features
$_['text_inventory_management'] = 'Inventory Management';
$_['text_branch_inventory'] = 'Branch Inventory';
$_['text_movement_history'] = 'Movement History';
$_['text_cost_tracking'] = 'Cost Tracking';
$_['text_wac_calculation'] = 'Weighted Average Cost Calculation';

// Accounting Integration Texts
$_['text_accounting_integration'] = 'Accounting Integration';
$_['text_journal_entries'] = 'Journal Entries';
$_['text_inventory_valuation'] = 'Inventory Valuation';

// Report Texts
$_['text_reports'] = 'Reports';
$_['text_inventory_report'] = 'Inventory Report';
$_['text_movement_report'] = 'Movement Report';
$_['text_valuation_report'] = 'Valuation Report';
$_['text_abc_analysis'] = 'ABC Analysis';

// Notification Texts
$_['text_notifications'] = 'Notifications';
$_['text_low_stock_alert'] = 'Low Stock Alert';
$_['text_expiry_alert'] = 'Expiry Alert';
$_['text_reorder_alert'] = 'Reorder Alert';

// Print Texts
$_['text_print_options'] = 'Print Options';
$_['text_print_labels'] = 'Print Labels';
$_['text_print_price_tags'] = 'Print Price Tags';

// Import/Export Texts
$_['text_import_export'] = 'Import/Export';
$_['text_export_products'] = 'Export Products';
$_['text_import_products'] = 'Import Products';
$_['text_bulk_update'] = 'Bulk Update';

// Security and Permissions Texts
$_['text_security'] = 'Security';
$_['text_user_permissions'] = 'User Permissions';
$_['text_audit_trail'] = 'Audit Trail';
$_['text_change_log'] = 'Change Log';

// ===== NEW TEXTS FOR ADVANCED FEATURES =====

// Sizes and Colors
$_['text_sizes_colors'] = 'Sizes and Colors';
$_['text_manage_sizes'] = 'Manage Sizes';
$_['text_manage_colors'] = 'Manage Colors';
$_['text_size_name'] = 'Size Name';
$_['text_size_code'] = 'Size Code';
$_['text_color_name'] = 'Color Name';
$_['text_color_code'] = 'Color Code';
$_['text_color_hex'] = 'Color Value (Hex)';
$_['text_add_size'] = 'Add Size';
$_['text_add_color'] = 'Add Color';
$_['text_edit_size'] = 'Edit Size';
$_['text_edit_color'] = 'Edit Color';
$_['text_delete_size'] = 'Delete Size';
$_['text_delete_color'] = 'Delete Color';

// Variants
$_['text_product_variants'] = 'Product Variants';
$_['text_add_variant'] = 'Add Variant';
$_['text_edit_variant'] = 'Edit Variant';
$_['text_delete_variant'] = 'Delete Variant';
$_['text_generate_variants'] = 'Auto Generate';
$_['text_variant_sku'] = 'Variant SKU';
$_['text_price_modifier'] = 'Price Modifier';
$_['text_variant_quantity'] = 'Variant Quantity';
$_['text_variant_image'] = 'Variant Image';
$_['text_variant_status'] = 'Variant Status';

// Multiple Images
$_['text_multiple_images'] = 'Multiple Images';
$_['text_upload_images'] = 'Upload Multiple Images';
$_['text_drag_drop_images'] = 'Drag images here or click to select';
$_['text_supported_formats'] = 'You can upload multiple images at once (JPG, PNG, GIF, WEBP)';
$_['text_image_alt_text'] = 'Alt Text';
$_['text_image_sort_order'] = 'Image Sort Order';
$_['text_reorder_images'] = 'Reorder Images';
$_['text_delete_image'] = 'Delete Image';
$_['text_image_preview'] = 'Image Preview';
$_['text_upload_progress'] = 'Upload Progress';

// Multiple Barcodes
$_['text_multiple_barcodes'] = 'Multiple Barcodes';
$_['text_add_barcode'] = 'Add Barcode';
$_['text_edit_barcode'] = 'Edit Barcode';
$_['text_delete_barcode'] = 'Delete Barcode';
$_['text_barcode_number'] = 'Barcode Number';
$_['text_barcode_type'] = 'Barcode Type';
$_['text_primary_barcode'] = 'Primary Barcode';
$_['text_generate_barcode'] = 'Generate Barcode';
$_['text_print_barcode'] = 'Print Barcode';
$_['text_validate_barcode'] = 'Validate Barcode';

// Product Bundles
$_['text_product_bundles'] = 'Product Bundles';
$_['text_create_bundle'] = 'Create Bundle';
$_['text_edit_bundle'] = 'Edit Bundle';
$_['text_delete_bundle'] = 'Delete Bundle';
$_['text_bundle_name'] = 'Bundle Name';
$_['text_bundle_description'] = 'Bundle Description';
$_['text_bundle_products'] = 'Bundle Products';
$_['text_bundle_discount'] = 'Bundle Discount';
$_['text_discount_type'] = 'Discount Type';
$_['text_discount_value'] = 'Discount Value';
$_['text_bundle_price'] = 'Bundle Price';
$_['text_check_availability'] = 'Check Availability';

// Tiered Pricing
$_['text_tiered_pricing'] = 'Tiered Pricing';
$_['text_add_price_tier'] = 'Add Price Tier';
$_['text_edit_price_tier'] = 'Edit Price Tier';
$_['text_delete_price_tier'] = 'Delete Price Tier';
$_['text_min_quantity'] = 'Minimum Quantity';
$_['text_max_quantity'] = 'Maximum Quantity';
$_['text_tier_price'] = 'Tier Price';
$_['text_customer_group'] = 'Customer Group';
$_['text_date_start'] = 'Start Date';
$_['text_date_end'] = 'End Date';
$_['text_calculate_price'] = 'Calculate Price';
$_['text_validate_pricing'] = 'Validate Pricing';

// Batches and Expiry Dates
$_['text_batch_management'] = 'Batch Management';
$_['text_add_batch'] = 'Add Batch';
$_['text_edit_batch'] = 'Edit Batch';
$_['text_delete_batch'] = 'Delete Batch';
$_['text_batch_number'] = 'Batch Number';
$_['text_manufacturing_date'] = 'Manufacturing Date';
$_['text_expiry_date'] = 'Expiry Date';
$_['text_batch_quantity'] = 'Batch Quantity';
$_['text_batch_cost'] = 'Batch Cost';
$_['text_batch_supplier'] = 'Batch Supplier';
$_['text_batch_notes'] = 'Batch Notes';
$_['text_days_to_expiry'] = 'Days to Expiry';
$_['text_expired'] = 'Expired';
$_['text_expiring_soon'] = 'Expiring Soon';
$_['text_fifo_system'] = 'FIFO System';
$_['text_enable_fifo'] = 'Enable Automatic FIFO System';
$_['text_expiry_report'] = 'Expiry Report';

// Batch Statistics
$_['text_batch_statistics'] = 'Batch Statistics';
$_['text_valid_batches'] = 'Valid Batches';
$_['text_expiring_batches'] = 'Expiring Batches';
$_['text_expired_batches'] = 'Expired Batches';
$_['text_total_batch_quantity'] = 'Total Quantity';

// Auto Classification
$_['text_auto_classification'] = 'Auto Classification';
$_['text_ai_classification'] = 'AI Classification';
$_['text_suggested_categories'] = 'Suggested Categories';
$_['text_apply_classification'] = 'Apply Classification';
$_['text_no_suggestions'] = 'No Suggestions';

// Product Cloning
$_['text_clone_product'] = 'Clone Product';
$_['text_clone_options'] = 'Clone Options';
$_['text_clone_descriptions'] = 'Clone Descriptions';
$_['text_clone_categories'] = 'Clone Categories';
$_['text_clone_images'] = 'Clone Images';
$_['text_clone_variants'] = 'Clone Variants';
$_['text_clone_pricing'] = 'Clone Pricing';

// Advanced Accounting Integration
$_['text_accounting_integration'] = 'Accounting Integration';
$_['text_create_inventory_account'] = 'Create Inventory Account';
$_['text_update_product_cost'] = 'Update Product Cost';
$_['text_wac_calculation'] = 'Weighted Average Cost Calculation';
$_['text_journal_entry'] = 'Journal Entry';
$_['text_inventory_valuation'] = 'Inventory Valuation';

// Advanced Import/Export
$_['text_advanced_import'] = 'Advanced Import';
$_['text_advanced_export'] = 'Advanced Export';
$_['text_import_file'] = 'Import File';
$_['text_export_format'] = 'Export Format';
$_['text_include_variants'] = 'Include Variants';
$_['text_include_pricing'] = 'Include Pricing';
$_['text_import_preview'] = 'Import Preview';
$_['text_export_filters'] = 'Export Filters';

// New Success Messages
$_['text_size_added'] = 'Size added successfully';
$_['text_color_added'] = 'Color added successfully';
$_['text_variant_added'] = 'Variant added successfully';
$_['text_images_uploaded'] = 'Images uploaded successfully';
$_['text_barcode_generated'] = 'Barcode generated successfully';
$_['text_bundle_created'] = 'Bundle created successfully';
$_['text_price_tier_added'] = 'Price tier added successfully';
$_['text_batch_added'] = 'Batch added successfully';
$_['text_product_cloned'] = 'Product cloned successfully';
$_['text_cost_updated'] = 'Cost updated successfully';
$_['text_import_success'] = 'Import completed successfully';
$_['text_export_success'] = 'Export completed successfully';

// New Error Messages
$_['error_size_name_required'] = 'Size name is required';
$_['error_color_name_required'] = 'Color name is required';
$_['error_variant_exists'] = 'This variant already exists';
$_['error_size_in_use'] = 'Cannot delete size, used in %s products';
$_['error_color_in_use'] = 'Cannot delete color, used in %s products';
$_['error_image_type'] = 'Unsupported file type: %s';
$_['error_image_size'] = 'File size too large: %s';
$_['error_image_upload'] = 'Failed to upload image: %s';
$_['error_no_images'] = 'No images selected';
$_['error_barcode_exists'] = 'Barcode number already exists';
$_['error_barcode_invalid'] = 'Invalid barcode number';
$_['error_bundle_name_required'] = 'Bundle name is required';
$_['error_bundle_products_minimum'] = 'Bundle needs at least two products';
$_['error_price_tier_overlap'] = 'Price tier overlap detected';
$_['error_batch_number_exists'] = 'Batch number already exists';
$_['error_file_type_invalid'] = 'Unsupported file type';
$_['error_file_upload'] = 'File upload failed';
$_['error_no_file'] = 'No file selected';

// Confirmation Messages
$_['confirm_delete_size'] = 'Are you sure you want to delete this size?';
$_['confirm_delete_color'] = 'Are you sure you want to delete this color?';
$_['confirm_delete_variant'] = 'Are you sure you want to delete this variant?';
$_['confirm_delete_image'] = 'Are you sure you want to delete this image?';
$_['confirm_delete_barcode'] = 'Are you sure you want to delete this barcode?';
$_['confirm_delete_bundle'] = 'Are you sure you want to delete this bundle?';
$_['confirm_delete_batch'] = 'Are you sure you want to delete this batch?';
$_['confirm_clone_product'] = 'Do you want to clone this product?';

// Advanced Help Texts
$_['help_variants'] = 'You can create product variants using different sizes and colors';
$_['help_multiple_images'] = 'You can upload multiple images for the product and reorder them by drag and drop';
$_['help_multiple_barcodes'] = 'You can create multiple barcodes for one product';
$_['help_bundles'] = 'You can create bundles from multiple products with special discount';
$_['help_tiered_pricing'] = 'You can set different prices based on quantity and customer group';
$_['help_batches'] = 'You can track batches and expiry dates with automatic FIFO system';
$_['help_auto_classification'] = 'Uses artificial intelligence to suggest appropriate categories';
$_['help_clone_product'] = 'You can clone the product with all its data or specific parts';
$_['help_wac'] = 'Weighted average cost is calculated automatically with each inventory movement';

?>
