<?php
class ControllerSaleDynamicPricing extends Controller {
    public function index() {
        $this->load->language('sale/dynamic_pricing');
        $this->document->setTitle($this->language->get('heading_title'));
        
        $this->load->model('sale/dynamic_pricing');
        
        $data['breadcrumbs'] = array();
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_sales'),
            'href' => $this->url->link('sale/sale', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('sale/dynamic_pricing', 'user_token=' . $this->session->data['user_token'], true)
        );
        
        $data['add'] = $this->url->link('sale/dynamic_pricing/add', 'user_token=' . $this->session->data['user_token'], true);
        $data['delete'] = $this->url->link('sale/dynamic_pricing/delete', 'user_token=' . $this->session->data['user_token'], true);
        $data['user_token'] = $this->session->data['user_token'];
        
        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');
        
        $this->response->setOutput($this->load->view('sale/dynamic_pricing', $data));
    }
    
    public function getList() {
        $this->load->language('sale/dynamic_pricing');
        $this->load->model('sale/dynamic_pricing');
        
        $data['rules'] = array();
        
        $results = $this->model_sale_dynamic_pricing->getPricingRules();
        
        foreach ($results as $result) {
            $data['rules'][] = array(
                'rule_id' => $result['rule_id'],
                'name' => $result['name'],
                'rule_type' => $result['rule_type'],
                'condition_type' => $result['condition_type'],
                'condition_value' => $result['condition_value'],
                'action_type' => $result['action_type'],
                'action_value' => $result['action_value'],
                'priority' => $result['priority'],
                'status' => $result['status'],
                'date_added' => date($this->language->get('date_format_short'), strtotime($result['date_added'])),
                'edit' => $this->url->link('sale/dynamic_pricing/edit', 'user_token=' . $this->session->data['user_token'] . '&rule_id=' . $result['rule_id'], true)
            );
        }
        
        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($data));
    }
    
    public function add() {
        $this->load->language('sale/dynamic_pricing');
        $this->load->model('sale/dynamic_pricing');
        
        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validate()) {
            $this->model_sale_dynamic_pricing->addPricingRule($this->request->post);
            
            $this->session->data['success'] = $this->language->get('text_success');
            
            $this->response->redirect($this->url->link('sale/dynamic_pricing', 'user_token=' . $this->session->data['user_token'], true));
        }
        
        $this->getForm();
    }
    
    public function edit() {
        $this->load->language('sale/dynamic_pricing');
        $this->load->model('sale/dynamic_pricing');
        
        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validate()) {
            $this->model_sale_dynamic_pricing->updatePricingRule($this->request->get['rule_id'], $this->request->post);
            
            $this->session->data['success'] = $this->language->get('text_success');
            
            $this->response->redirect($this->url->link('sale/dynamic_pricing', 'user_token=' . $this->session->data['user_token'], true));
        }
        
        $this->getForm();
    }
    
    public function delete() {
        $this->load->language('sale/dynamic_pricing');
        $this->load->model('sale/dynamic_pricing');
        
        if (isset($this->request->post['selected']) && $this->validateDelete()) {
            foreach ($this->request->post['selected'] as $rule_id) {
                $this->model_sale_dynamic_pricing->deletePricingRule($rule_id);
            }
            
            $this->session->data['success'] = $this->language->get('text_success');
            
            $this->response->redirect($this->url->link('sale/dynamic_pricing', 'user_token=' . $this->session->data['user_token'], true));
        }
        
        $this->getList();
    }
    
    public function test() {
        $this->load->language('sale/dynamic_pricing');
        $this->load->model('sale/dynamic_pricing');
        
        $json = array();
        
        if (isset($this->request->post['product_id']) && isset($this->request->post['customer_id'])) {
            $product_id = $this->request->post['product_id'];
            $customer_id = $this->request->post['customer_id'];
            $quantity = isset($this->request->post['quantity']) ? $this->request->post['quantity'] : 1;
            
            $original_price = $this->model_sale_dynamic_pricing->getProductPrice($product_id);
            $final_price = $this->model_sale_dynamic_pricing->calculateDynamicPrice($product_id, $customer_id, $quantity);
            
            $json['success'] = true;
            $json['original_price'] = $original_price;
            $json['final_price'] = $final_price;
            $json['discount'] = $original_price - $final_price;
            $json['discount_percentage'] = ($original_price > 0) ? (($original_price - $final_price) / $original_price) * 100 : 0;
        } else {
            $json['error'] = $this->language->get('error_test_data');
        }
        
        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }
    
    protected function getForm() {
        $this->load->language('sale/dynamic_pricing');
        $this->load->model('sale/dynamic_pricing');
        
        $data['text_form'] = !isset($this->request->get['rule_id']) ? $this->language->get('text_add') : $this->language->get('text_edit');
        
        if (isset($this->request->get['rule_id'])) {
            $rule_info = $this->model_sale_dynamic_pricing->getPricingRule($this->request->get['rule_id']);
        }
        
        if (isset($this->request->post['name'])) {
            $data['name'] = $this->request->post['name'];
        } elseif (!empty($rule_info)) {
            $data['name'] = $rule_info['name'];
        } else {
            $data['name'] = '';
        }
        
        if (isset($this->request->post['rule_type'])) {
            $data['rule_type'] = $this->request->post['rule_type'];
        } elseif (!empty($rule_info)) {
            $data['rule_type'] = $rule_info['rule_type'];
        } else {
            $data['rule_type'] = 'product';
        }
        
        if (isset($this->request->post['condition_type'])) {
            $data['condition_type'] = $this->request->post['condition_type'];
        } elseif (!empty($rule_info)) {
            $data['condition_type'] = $rule_info['condition_type'];
        } else {
            $data['condition_type'] = 'quantity';
        }
        
        if (isset($this->request->post['condition_value'])) {
            $data['condition_value'] = $this->request->post['condition_value'];
        } elseif (!empty($rule_info)) {
            $data['condition_value'] = $rule_info['condition_value'];
        } else {
            $data['condition_value'] = '';
        }
        
        if (isset($this->request->post['action_type'])) {
            $data['action_type'] = $this->request->post['action_type'];
        } elseif (!empty($rule_info)) {
            $data['action_type'] = $rule_info['action_type'];
        } else {
            $data['action_type'] = 'percentage';
        }
        
        if (isset($this->request->post['action_value'])) {
            $data['action_value'] = $this->request->post['action_value'];
        } elseif (!empty($rule_info)) {
            $data['action_value'] = $rule_info['action_value'];
        } else {
            $data['action_value'] = '';
        }
        
        if (isset($this->request->post['priority'])) {
            $data['priority'] = $this->request->post['priority'];
        } elseif (!empty($rule_info)) {
            $data['priority'] = $rule_info['priority'];
        } else {
            $data['priority'] = 1;
        }
        
        if (isset($this->request->post['status'])) {
            $data['status'] = $this->request->post['status'];
        } elseif (!empty($rule_info)) {
            $data['status'] = $rule_info['status'];
        } else {
            $data['status'] = 1;
        }
        
        $data['user_token'] = $this->session->data['user_token'];
        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');
        
        $this->response->setOutput($this->load->view('sale/dynamic_pricing_form', $data));
    }
    
    protected function validate() {
        if (!$this->user->hasPermission('modify', 'sale/dynamic_pricing')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }
        
        if (empty($this->request->post['name'])) {
            $this->error['name'] = $this->language->get('error_name');
        }
        
        if (empty($this->request->post['condition_value'])) {
            $this->error['condition_value'] = $this->language->get('error_condition_value');
        }
        
        if (empty($this->request->post['action_value'])) {
            $this->error['action_value'] = $this->language->get('error_action_value');
        }
        
        return !$this->error;
    }
    
    protected function validateDelete() {
        if (!$this->user->hasPermission('modify', 'sale/dynamic_pricing')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }
        
        return !$this->error;
    }
} 