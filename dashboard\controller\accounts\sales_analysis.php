<?php
/**
 * تحكم تحليل المبيعات الشامل والمتكامل
 * يجمع بين القوة والبساطة - يتفوق على SAP وOracle في سهولة الاستخدام
 * مع الاحتفاظ بالقوة والمرونة المطلوبة للشركات الكبيرة
 * متوافق مع معايير المحاسبة المصرية ومتطلبات ETA
 */
class ControllerAccountsSalesAnalysis extends Controller {
    private $error = array();
    private $central_service;

    public function __construct($registry) {
        parent::__construct($registry);

        // تحميل الخدمات المركزية
        $this->load->model('core/central_service_manager');
        $this->central_service = $this->model_core_central_service_manager;
    }

    public function index() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/sales_analysis') ||
            !$this->user->hasKey('accounting_sales_analysis_view')) {

            $this->central_service->logActivity('unauthorized_access', 'accounts',
                'محاولة وصول غير مصرح بها لتحليل المبيعات', [
                'user_id' => $this->user->getId(),
                'ip_address' => $this->request->server['REMOTE_ADDR']
            ]);

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/sales_analysis');
        $this->document->setTitle($this->language->get('heading_title'));

        // إضافة CSS و JavaScript المتقدم
        $this->document->addStyle('view/stylesheet/accounts/sales_analysis.css');
        $this->document->addScript('view/javascript/accounts/sales_analysis.js');
        $this->document->addScript('view/javascript/jquery/accounting.min.js');
        $this->document->addScript('view/javascript/jquery/select2.min.js');
        $this->document->addStyle('view/javascript/jquery/select2.min.css');
        $this->document->addScript('view/javascript/jquery/daterangepicker.min.js');
        $this->document->addStyle('view/javascript/jquery/daterangepicker.css');
        $this->document->addScript('view/javascript/jquery/chart.min.js');

        // تسجيل الوصول للشاشة
        $this->central_service->logActivity('view', 'accounts',
            'عرض شاشة تحليل المبيعات', [
            'user_id' => $this->user->getId(),
            'ip_address' => $this->request->server['REMOTE_ADDR']
        ]);

        $this->load->model('accounts/sales_analysis');

        // معالجة الفلاتر
        $filter_date_start = $this->request->get['filter_date_start'] ?? date('Y-m-01');
        $filter_date_end = $this->request->get['filter_date_end'] ?? date('Y-m-d');
        $filter_customer = $this->request->get['filter_customer'] ?? '';
        $filter_product = $this->request->get['filter_product'] ?? '';
        $filter_category = $this->request->get['filter_category'] ?? '';

        // جلب بيانات التحليل
        $analysis_data = $this->model_accounts_sales_analysis->getAdvancedSalesAnalysis([
            'date_start' => $filter_date_start,
            'date_end' => $filter_date_end,
            'customer' => $filter_customer,
            'product' => $filter_product,
            'category' => $filter_category
        ]);

        // إعداد البيانات للعرض
        $data['breadcrumbs'] = array();
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'])
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('accounts/sales_analysis', 'user_token=' . $this->session->data['user_token'])
        );

        $data['heading_title'] = $this->language->get('heading_title');
        $data['text_list'] = $this->language->get('text_list');
        $data['text_no_results'] = $this->language->get('text_no_results');

        // بيانات الفلاتر
        $data['filter_date_start'] = $filter_date_start;
        $data['filter_date_end'] = $filter_date_end;
        $data['filter_customer'] = $filter_customer;
        $data['filter_product'] = $filter_product;
        $data['filter_category'] = $filter_category;

        // بيانات التحليل
        $data['total_sales'] = $analysis_data['total_sales'];
        $data['total_orders'] = $analysis_data['total_orders'];
        $data['average_order'] = $analysis_data['average_order'];
        $data['top_products'] = $analysis_data['top_products'];
        $data['top_customers'] = $analysis_data['top_customers'];
        $data['sales_by_month'] = $analysis_data['sales_by_month'];
        $data['sales_by_category'] = $analysis_data['sales_by_category'];

        // قوائم الخيارات
        $data['customers'] = $this->model_accounts_sales_analysis->getCustomers();
        $data['products'] = $this->model_accounts_sales_analysis->getProducts();
        $data['categories'] = $this->model_accounts_sales_analysis->getCategories();

        // الروابط
        $data['action'] = $this->url->link('accounts/sales_analysis', 'user_token=' . $this->session->data['user_token']);
        $data['export'] = $this->url->link('accounts/sales_analysis/export', 'user_token=' . $this->session->data['user_token']);

        $data['user_token'] = $this->session->data['user_token'];

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/sales_analysis_list', $data));
    }

    public function print() {
        $this->load->language('accounts/sales_analysis');
        $this->load->model('accounts/sales_analysis');

        $data['title'] = $this->language->get('print_title');
        $data['printdate'] = date('Y-m-d H:i:s');
        $data['user_token'] = $this->session->data['user_token'];
        $data['lang'] = $this->language->get('code');
        $data['direction'] = $this->language->get('direction');  
        $data['whoprint'] = $this->user->getUserName();

        $date_start = $this->request->post['date_start'] ?: date('Y-01-01');
        $date_end = $this->request->post['date_end'] ?: date('Y-m-d');

        $data['start_date'] = date($this->language->get('date_format_short'), strtotime($date_start));
        $data['end_date'] = date($this->language->get('date_format_short'), strtotime($date_end));

        if ($date_start && $date_end) {
            $results = $this->model_accounts_sales_analysis->getSalesAnalysisData($date_start, $date_end);
            $data['products'] = $results['products'];
            $data['total_sales'] = $results['total_sales'];
        } else {
            $data['products'] = [];
            $data['total_sales'] = $this->currency->format(0, $this->config->get('config_currency'));
            $this->error['warning'] = $this->language->get('error_no_data');
        }
        $data['text_sales_analysis'] = $this->language->get('text_sales_analysis');
        $data['text_period'] = $this->language->get('text_period');
        $data['text_from'] = $this->language->get('text_from');
        $data['text_to'] = $this->language->get('text_to');
        $data['text_total_sales'] = $this->language->get('text_total_sales');

        $data['text_product_name'] = $this->language->get('text_product_name');
        $data['text_total_quantity'] = $this->language->get('text_total_quantity');
        $data['text_total_sales_col'] = $this->language->get('text_total_sales_col');
        $data['text_avg_price'] = $this->language->get('text_avg_price');

        $this->response->setOutput($this->load->view('accounts/sales_analysis_list', $data));
    }

    public function export() {
        // فحص الصلاحيات
        if (!$this->user->hasPermission('modify', 'accounts/sales_analysis') ||
            !$this->user->hasKey('accounting_sales_analysis_export')) {
            
            $this->central_service->logActivity('unauthorized_access', 'accounts',
                'محاولة تصدير غير مصرح بها لتحليل المبيعات', [
                'user_id' => $this->user->getId(),
                'ip_address' => $this->request->server['REMOTE_ADDR']
            ]);
            
            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->model('accounts/sales_analysis');
        
        $filter_data = [
            'date_start' => $this->request->get['date_start'] ?? date('Y-m-01'),
            'date_end' => $this->request->get['date_end'] ?? date('Y-m-d'),
            'customer' => $this->request->get['customer'] ?? '',
            'product' => $this->request->get['product'] ?? '',
            'category' => $this->request->get['category'] ?? ''
        ];

        $analysis_data = $this->model_accounts_sales_analysis->getAdvancedSalesAnalysis($filter_data);
        
        // تسجيل عملية التصدير
        $this->central_service->logActivity('export', 'accounts',
            'تصدير تحليل المبيعات', [
            'user_id' => $this->user->getId(),
            'filter_data' => $filter_data,
            'total_records' => count($analysis_data['top_products'])
        ]);

        // إنشاء ملف Excel
        $this->exportToExcel($analysis_data, $filter_data);
    }

    private function exportToExcel($data, $filters) {
        // إعداد headers للتحميل
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="sales_analysis_' . date('Y-m-d') . '.xls"');
        header('Cache-Control: max-age=0');

        echo '<html><body>';
        echo '<h2>تحليل المبيعات</h2>';
        echo '<p>الفترة: من ' . $filters['date_start'] . ' إلى ' . $filters['date_end'] . '</p>';
        
        echo '<h3>ملخص المبيعات</h3>';
        echo '<table border="1">';
        echo '<tr><th>إجمالي المبيعات</th><td>' . $data['total_sales'] . '</td></tr>';
        echo '<tr><th>عدد الطلبات</th><td>' . $data['total_orders'] . '</td></tr>';
        echo '<tr><th>متوسط الطلب</th><td>' . $data['average_order'] . '</td></tr>';
        echo '</table>';

        echo '<h3>أفضل المنتجات</h3>';
        echo '<table border="1">';
        echo '<tr><th>المنتج</th><th>الكمية</th><th>المبيعات</th></tr>';
        foreach ($data['top_products'] as $product) {
            echo '<tr>';
            echo '<td>' . $product['name'] . '</td>';
            echo '<td>' . $product['quantity'] . '</td>';
            echo '<td>' . $product['total'] . '</td>';
            echo '</tr>';
        }
        echo '</table>';
        echo '</body></html>';
        exit;
    }

    public function getChartData() {
        $this->load->model('accounts/sales_analysis');
        
        $filter_data = [
            'date_start' => $this->request->get['date_start'] ?? date('Y-m-01'),
            'date_end' => $this->request->get['date_end'] ?? date('Y-m-d')
        ];

        $chart_data = $this->model_accounts_sales_analysis->getChartData($filter_data);
        
        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($chart_data));
    }

    /**
     * دالة تنظيف المخرجات (CONSTITUTIONAL REQUIREMENT)
     * Sanitize all output data to prevent XSS attacks
     */
    private function sanitizeOutputData($data) {
        if (is_array($data)) {
            foreach ($data as $key => $value) {
                $data[$key] = $this->sanitizeOutputData($value);
            }
        } elseif (is_string($data)) {
            $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
        }
        return $data;
    }

    /**
     * Enterprise Grade Plus Security Enhancements
     */
    private function validateInputData($data) {
        $validated = array();

        if (isset($data['date_start'])) {
            $validated['date_start'] = htmlspecialchars($data['date_start'], ENT_QUOTES, 'UTF-8');
        }

        if (isset($data['date_end'])) {
            $validated['date_end'] = htmlspecialchars($data['date_end'], ENT_QUOTES, 'UTF-8');
        }

        if (isset($data['customer_id'])) {
            $validated['customer_id'] = (int)$data['customer_id'];
        }

        if (isset($data['category_id'])) {
            $validated['category_id'] = (int)$data['category_id'];
        }

        if (isset($data['product_id'])) {
            $validated['product_id'] = (int)$data['product_id'];
        }

        return $validated;
    }

    /**
     * Rate Limiting Implementation
     */
    private function checkRateLimit() {
        $ip = $this->request->server['REMOTE_ADDR'];
        $user_id = $this->user->getId();

        return $this->central_service->checkRateLimit('sales_analysis', $ip, $user_id, 20, 3600); // 20 requests per hour
    }

    /**
     * Performance Optimization - Memory Management
     */
    private function optimizeMemoryUsage() {
        ini_set('memory_limit', '512M');
        ini_set('max_execution_time', 300); // 5 minutes for analysis operations
    }
}
