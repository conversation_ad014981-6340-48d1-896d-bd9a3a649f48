<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/type/fraction.proto

namespace GPBMetadata\Google\Type;

class Fraction
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(hex2bin(
            "0acd010a1a676f6f676c652f747970652f6672616374696f6e2e70726f74" .
            "6f120b676f6f676c652e7479706522320a084672616374696f6e12110a09" .
            "6e756d657261746f7218012001280312130a0b64656e6f6d696e61746f72" .
            "18022001280342660a0f636f6d2e676f6f676c652e74797065420d467261" .
            "6374696f6e50726f746f50015a3c676f6f676c652e676f6c616e672e6f72" .
            "672f67656e70726f746f2f676f6f676c65617069732f747970652f667261" .
            "6374696f6e3b6672616374696f6ea20203475450620670726f746f33"
        ), true);

        static::$is_initialized = true;
    }
}

