<?php
$_['heading_title']           = 'Inventory Valuation & Movement Report';
$_['text_form']               = 'Filter Inventory Valuation Report';
$_['entry_date_start']        = 'Start Date';
$_['entry_date_end']          = 'End Date';
$_['button_filter']           = 'Filter';

$_['print_title']             = 'Print Inventory Valuation & Movement Report';
$_['text_inventory_valuation']= 'Inventory Valuation & Movement Report';
$_['text_period']             = 'Period';
$_['text_from']               = 'From';
$_['text_to']                 = 'To';
$_['text_total_value']        = 'Total Inventory Value';

$_['text_product_name']       = 'Product Name';
$_['text_opening_qty']        = 'Opening Qty';
$_['text_in_qty']             = 'In Qty';
$_['text_out_qty']            = 'Out Qty';
$_['text_closing_qty']        = 'Closing Qty';
$_['text_average_cost']       = 'Average Cost';
$_['text_inventory_value']    = 'Inventory Value';

$_['error_no_data']           = 'No data for selected period!';

// Controller language variables
$_['log_unauthorized_access_inventory_valuation'] = 'Unauthorized access attempt to inventory valuation';
$_['log_view_inventory_valuation_screen'] = 'View inventory valuation screen';
$_['log_unauthorized_generate_inventory_valuation'] = 'Unauthorized inventory valuation report generation attempt';
$_['log_generate_inventory_valuation'] = 'Generate inventory valuation report for period';
$_['text_to'] = 'to';
$_['notification_inventory_valuation_generated'] = 'Inventory valuation report generated';
$_['notification_inventory_valuation_message'] = 'Inventory valuation report generated for period';
$_['text_by'] = 'by';

// Additional variables for template
$_['text_actions'] = 'Actions';
$_['text_generate_valuation'] = 'Generate Inventory Valuation';
$_['button_generate'] = 'Generate';
$_['text_export_options'] = 'Export Options';
$_['text_export'] = 'Export';
$_['text_print'] = 'Print';
$_['text_inventory_analysis'] = 'Inventory Analysis';
$_['text_variance_analysis'] = 'Variance Analysis';
$_['text_valuation_filters'] = 'Valuation Filters';
$_['text_all_categories'] = 'All Categories';
$_['text_all_warehouses'] = 'All Warehouses';
$_['entry_warehouse'] = 'Warehouse';
$_['text_total_items'] = 'Total Items';
$_['text_inventory_items'] = 'Inventory Items';
$_['text_total_cost'] = 'Total Cost';
$_['text_cost_value'] = 'Cost Value';
$_['text_market_value'] = 'Market Value';
$_['text_current_market_value'] = 'Current Market Value';
$_['text_variance'] = 'Variance';
$_['text_cost_vs_market'] = 'Cost vs Market';
$_['text_inventory_valuation_details'] = 'Inventory Valuation Details';
$_['column_product_name'] = 'Product Name';
$_['column_product_code'] = 'Product Code';
$_['column_category'] = 'Category';
$_['column_warehouse'] = 'Warehouse';
$_['column_quantity'] = 'Quantity';
$_['column_unit_cost'] = 'Unit Cost';
$_['column_total_cost'] = 'Total Cost';
$_['column_market_price'] = 'Market Price';
$_['column_market_value'] = 'Market Value';
$_['column_variance'] = 'Variance';
$_['column_variance_percent'] = 'Variance %';
$_['text_totals'] = 'Totals';
$_['text_inventory_by_category_chart'] = 'Inventory by Category Chart';
$_['text_cost_vs_market_chart'] = 'Cost vs Market Chart';
$_['text_variance_analysis_chart'] = 'Variance Analysis Chart';
$_['text_no_inventory_found'] = 'No inventory items found';
$_['text_valuation_generated'] = 'Inventory valuation generated successfully';
$_['error_generate_valuation'] = 'Error generating inventory valuation';
$_['text_exporting'] = 'Exporting';
$_['text_loading'] = 'Loading';

// Enhanced performance and analytics variables
$_['text_optimized_valuation']         = 'Optimized Inventory Valuation';
$_['text_inventory_analysis']          = 'Inventory Analysis';
$_['text_cache_enabled']               = 'Cache Enabled';
$_['text_total_products']              = 'Total Products';
$_['text_turnover_analysis']           = 'Turnover Analysis';
$_['text_turnover_ratio']              = 'Turnover Ratio';
$_['text_turnover_days']               = 'Turnover Days';
$_['button_inventory_analysis']        = 'Inventory Analysis';
$_['text_loading_analysis']            = 'Loading inventory analysis...';
$_['text_analysis_ready']              = 'Analysis ready';
?>
