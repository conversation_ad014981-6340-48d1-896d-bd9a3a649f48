{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="submit" form="form-terminal" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fa fa-save"></i></button>
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a>
      </div>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pencil"></i> {{ text_form }}</h3>
      </div>
      <div class="panel-body">
        <form action="{{ action }}" method="post" enctype="multipart/form-data" id="form-terminal" class="form-horizontal">
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-name">{{ entry_name }}</label>
            <div class="col-sm-10">
              <input type="text" name="name" value="{{ name }}" placeholder="{{ entry_name }}" id="input-name" class="form-control" />
              {% if error_name %}
              <div class="text-danger">{{ error_name }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-branch">{{ entry_branch }}</label>
            <div class="col-sm-10">
              <select name="branch_id" id="input-branch" class="form-control">
                <option value="">{{ text_select }}</option>
                {% for branch in branches %}
                <option value="{{ branch.branch_id }}" {{ branch_id == branch.branch_id ? 'selected="selected"' }}>{{ branch.name }}</option>
                {% endfor %}
              </select>
              {% if error_branch %}
              <div class="text-danger">{{ error_branch }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-printer-type">{{ entry_printer_type }}</label>
            <div class="col-sm-10">
              <select name="printer_type" id="input-printer-type" class="form-control">
                <option value="">{{ text_select }}</option>
                {% for type_key, printer_type in printer_types %}
                <option value="{{ type_key }}" {{ printer_type == type_key ? 'selected="selected"' }}>{{ printer_type }}</option>
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-printer-name">{{ entry_printer_name }}</label>
            <div class="col-sm-10">
              <input type="text" name="printer_name" value="{{ printer_name }}" placeholder="{{ entry_printer_name }}" id="input-printer-name" class="form-control" />
              <span class="help-block">{{ help_printer_name }}</span>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-status">{{ entry_status }}</label>
            <div class="col-sm-10">
              <select name="status" id="input-status" class="form-control">
                <option value="1" {{ status ? 'selected="selected"' }}>{{ text_enabled }}</option>
                <option value="0" {{ not status ? 'selected="selected"' }}>{{ text_disabled }}</option>
              </select>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
{{ footer }}