/**
 * Workflow Designer - CSS Styles
 */

/* Main container */
#workflow-designer {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    min-height: 600px;
    background-color: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
}

/* Node palette */
#node-palette {
    padding: 15px;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    background-color: #fff;
    border-bottom: 1px solid #ddd;
}

.node-item {
    width: 120px;
    padding: 8px;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: move;
    display: flex;
    align-items: center;
    transition: all 0.2s ease;
}

.node-item:hover {
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.node-icon {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
}

.node-icon i {
    color: white;
}

.node-label {
    font-size: 12px;
    font-weight: 500;
}

/* Workflow canvas */
#workflow-canvas {
    flex: 1;
    position: relative;
    overflow: auto;
    background-color: #f5f5f5;
    background-image: 
        linear-gradient(to right, rgba(0, 0, 0, 0.05) 1px, transparent 1px),
        linear-gradient(to bottom, rgba(0, 0, 0, 0.05) 1px, transparent 1px);
    background-size: 20px 20px;
}

.canvas-content {
    position: relative;
    min-width: 2000px;
    min-height: 1500px;
    transform-origin: 0 0;
}

/* Workflow nodes */
.workflow-node {
    position: absolute;
    width: 180px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
    cursor: move;
    user-select: none;
    z-index: 10;
}

.workflow-node.selected {
    box-shadow: 0 0 0 2px #2196F3, 0 2px 10px rgba(0, 0, 0, 0.2);
}

.workflow-node.dragging {
    opacity: 0.7;
    z-index: 11;
}

.node-header {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}

.node-header i {
    color: white;
    margin-right: 8px;
}

.node-header span {
    color: white;
    font-weight: 500;
}

.node-connectors {
    display: flex;
    justify-content: space-between;
    padding: 10px;
}

.connector {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: #90a4ae;
    cursor: pointer;
    position: relative;
    border: 2px solid white;
    transition: all 0.2s ease;
}

.connector:hover {
    background-color: #2196F3;
    transform: scale(1.2);
}

.connector-highlight {
    background-color: #4CAF50;
    transform: scale(1.2);
    box-shadow: 0 0 8px #4CAF50;
}

.connector-in {
    position: relative;
    left: -6px;
}

.connector-out {
    position: relative;
    right: -6px;
}

/* Connections */
.workflow-connection {
    pointer-events: none;
    z-index: 5;
}

.workflow-connection path {
    pointer-events: stroke;
    cursor: pointer;
}

.workflow-connection.selected path {
    stroke-width: 3px;
    filter: drop-shadow(0 0 3px rgba(33, 150, 243, 0.5));
}

.temp-connection path {
    pointer-events: none;
}

/* Property panel */
#property-panel {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 280px;
    background-color: white;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 15px;
    z-index: 20;
    display: none;
}

#property-panel.visible {
    display: block;
}

.properties-form h4 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 14px;
    color: #333;
    border-bottom: 1px solid #eee;
    padding-bottom: 5px;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    font-size: 12px;
    color: #555;
}

.form-control {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 12px;
}

textarea.form-control {
    min-height: 80px;
}

/* Buttons */
.btn {
    padding: 6px 12px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    transition: background-color 0.2s;
}

.btn-primary {
    background-color: #2196F3;
    color: white;
}

.btn-primary:hover {
    background-color: #0d8aee;
}

.btn-danger {
    background-color: #F44336;
    color: white;
}

.btn-danger:hover {
    background-color: #e63930;
}

/* Toolbar */
.workflow-toolbar {
    display: flex;
    align-items: center;
    padding: 10px;
    background-color: #fff;
    border-bottom: 1px solid #ddd;
}

.toolbar-button {
    width: 32px;
    height: 32px;
    margin-right: 5px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #fff;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
}

.toolbar-button:hover {
    background-color: #f5f5f5;
}

.toolbar-spacer {
    flex: 1;
}

/* Responsive design */
@media (max-width: 992px) {
    #property-panel {
        width: 240px;
    }
}

@media (max-width: 768px) {
    .node-item {
        width: 100px;
    }
}

/* Animation */
@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(33, 150, 243, 0.4); }
    70% { box-shadow: 0 0 0 6px rgba(33, 150, 243, 0); }
    100% { box-shadow: 0 0 0 0 rgba(33, 150, 243, 0); }
} 