{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="submit" form="purchase\supplier_analytics_advanced-form" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fas fa-save"></i></button>
        <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-light"><i class="fas fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    
    <div class="card">
      <div class="card-header"><i class="fas fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}chart-bar{% elif screen_type == 'settings' %}cog{% else %}pencil-alt{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="card-body">
        <form id="purchase\supplier_analytics_advanced-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-can_update_rating">{{ text_can_update_rating }}</label>
            <div class="col-sm-10">
              <input type="text" name="can_update_rating" value="{{ can_update_rating }}" placeholder="{{ text_can_update_rating }}" id="input-can_update_rating" class="form-control" />
              {% if error_can_update_rating %}
                <div class="invalid-feedback">{{ error_can_update_rating }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-categories">{{ text_categories }}</label>
            <div class="col-sm-10">
              <input type="text" name="categories" value="{{ categories }}" placeholder="{{ text_categories }}" id="input-categories" class="form-control" />
              {% if error_categories %}
                <div class="invalid-feedback">{{ error_categories }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-delivery_url">{{ text_delivery_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="delivery_url" value="{{ delivery_url }}" placeholder="{{ text_delivery_url }}" id="input-delivery_url" class="form-control" />
              {% if error_delivery_url %}
                <div class="invalid-feedback">{{ error_delivery_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-export_url">{{ text_export_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="export_url" value="{{ export_url }}" placeholder="{{ text_export_url }}" id="input-export_url" class="form-control" />
              {% if error_export_url %}
                <div class="invalid-feedback">{{ error_export_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="invalid-feedback">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-performance_url">{{ text_performance_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="performance_url" value="{{ performance_url }}" placeholder="{{ text_performance_url }}" id="input-performance_url" class="form-control" />
              {% if error_performance_url %}
                <div class="invalid-feedback">{{ error_performance_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-price_url">{{ text_price_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="price_url" value="{{ price_url }}" placeholder="{{ text_price_url }}" id="input-price_url" class="form-control" />
              {% if error_price_url %}
                <div class="invalid-feedback">{{ error_price_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-products">{{ text_products }}</label>
            <div class="col-sm-10">
              <input type="text" name="products" value="{{ products }}" placeholder="{{ text_products }}" id="input-products" class="form-control" />
              {% if error_products %}
                <div class="invalid-feedback">{{ error_products }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-quality_url">{{ text_quality_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="quality_url" value="{{ quality_url }}" placeholder="{{ text_quality_url }}" id="input-quality_url" class="form-control" />
              {% if error_quality_url %}
                <div class="invalid-feedback">{{ error_quality_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-ranking_url">{{ text_ranking_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="ranking_url" value="{{ ranking_url }}" placeholder="{{ text_ranking_url }}" id="input-ranking_url" class="form-control" />
              {% if error_ranking_url %}
                <div class="invalid-feedback">{{ error_ranking_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-recommendations_url">{{ text_recommendations_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="recommendations_url" value="{{ recommendations_url }}" placeholder="{{ text_recommendations_url }}" id="input-recommendations_url" class="form-control" />
              {% if error_recommendations_url %}
                <div class="invalid-feedback">{{ error_recommendations_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-report_url">{{ text_report_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="report_url" value="{{ report_url }}" placeholder="{{ text_report_url }}" id="input-report_url" class="form-control" />
              {% if error_report_url %}
                <div class="invalid-feedback">{{ error_report_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-risk_url">{{ text_risk_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="risk_url" value="{{ risk_url }}" placeholder="{{ text_risk_url }}" id="input-risk_url" class="form-control" />
              {% if error_risk_url %}
                <div class="invalid-feedback">{{ error_risk_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-suppliers">{{ text_suppliers }}</label>
            <div class="col-sm-10">
              <input type="text" name="suppliers" value="{{ suppliers }}" placeholder="{{ text_suppliers }}" id="input-suppliers" class="form-control" />
              {% if error_suppliers %}
                <div class="invalid-feedback">{{ error_suppliers }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-update_rating_url">{{ text_update_rating_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="update_rating_url" value="{{ update_rating_url }}" placeholder="{{ text_update_rating_url }}" id="input-update_rating_url" class="form-control" />
              {% if error_update_rating_url %}
                <div class="invalid-feedback">{{ error_update_rating_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="invalid-feedback">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}