{{ header }}{{ column_left }}
<div id="content">
    
    
    

<link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css" rel="stylesheet" />
<script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>
<link href="https://cdn.datatables.net/2.1.7/css/dataTables.dataTables.min.css" rel="stylesheet" />
<script src="https://cdn.datatables.net/2.1.7/js/dataTables.min.js"></script>
<style>
.select2-container--default .select2-selection--single {
  height: 36px;
}

.attachment-item, .preview-item {
    display: inline-block;
    margin-right: 10px;
    border: 1px dashed #ddd;
    padding: 5px;
}

</style>  

  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="submit" form="form-requisition" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fa fa-save"></i></button>
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a>
      </div>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pencil"></i> {{ text_form }}</h3>
      </div>
      <div class="panel-body">
        <form action="{{ action }}" method="post" enctype="multipart/form-data" id="form-requisition" class="form-horizontal">
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-date-required">{{ entry_date_required }}</label>
            <div class="col-sm-10">
              <!-- Input for required date -->
              <input type="date" name="date_required" value="{{ date_required }}" placeholder="{{ entry_date_required }}" id="input-date-required" class="form-control" />
              {% if error_date_required %}
              <div class="text-danger">{{ error_date_required }}</div>
              {% endif %}
            </div>
          </div>
          
          <table id="products" class="table table-striped table-bordered table-hover">
            <thead>
              <tr>
                <td class="text-left">{{ entry_product }}</td>
                <td class="text-right">{{ entry_quantity }}</td>
                <td class="text-left">{{ entry_unit }}</td>
                <td class="text-left">{{ entry_inventory }}</td>
                <td></td>
              </tr>
            </thead>
            <tbody>
              {% set product_row = 0 %}
              {% for requisition_product in requisition_products %}
              <tr id="product-row{{ product_row }}">
                <td class="text-left">
                  <!-- Product select dropdown -->
                  <select name="requisition_product[{{ product_row }}][product_id]" class="form-control product-select" data-row="{{ product_row }}">
                    <option value="{{ requisition_product.product_id }}" selected="selected">{{ requisition_product.name }}</option>
                  </select>
                </td>
                <!-- Quantity input field -->
                <td class="text-right"><input type="number" name="requisition_product[{{ product_row }}][quantity]" value="{{ requisition_product.quantity }}" placeholder="{{ entry_quantity }}" class="form-control" min="1" /></td>
                <td class="text-left">
                  <!-- Unit select dropdown -->
                  <select name="requisition_product[{{ product_row }}][unit_id]" class="form-control unit-select" data-row="{{ product_row }}">
                    <option value="{{ requisition_product.unit_id }}" selected="selected">{{ requisition_product.unit_name }}</option>
                  </select>
                </td>
                <!-- Inventory information for the selected unit -->
                <td class="text-left">{{ requisition_product.inventory }}</td>
                <!-- Button to remove the product row -->
                <td class="text-left"><button type="button" onclick="$('#product-row{{ product_row }}').remove();" data-toggle="tooltip" title="{{ button_remove }}" class="btn btn-danger"><i class="fa fa-minus-circle"></i></button></td>
              </tr>
              {% set product_row = product_row + 1 %}
              {% endfor %}
            </tbody>
            <tfoot>
              <tr>
                <td colspan="3"></td>
                <!-- Button to add a new product row -->
                <td class="text-left"><button type="button" onclick="addProduct();" data-toggle="tooltip" title="{{ button_product_add }}" class="btn btn-primary"><i class="fa fa-plus-circle"></i></button></td>
              </tr>
            </tfoot>
          </table>
        </form>
      </div>
    </div>
  </div>
</div>
<script type="text/javascript"><!--
var product_row = {{ product_row }};

// Function to add a new product row to the table
function addProduct() {
  html  = '<tr id="product-row' + product_row + '">';
  html += '  <td class="text-left"><select name="requisition_product[' + product_row + '][product_id]" class="form-control product-select" data-row="' + product_row + '"></select></td>';
  html += '  <td class="text-right"><input type="number" name="requisition_product[' + product_row + '][quantity]" value="1" placeholder="{{ entry_quantity }}" class="form-control" min="1" /></td>';
  html += '  <td class="text-left"><select name="requisition_product[' + product_row + '][unit_id]" class="form-control unit-select" data-row="' + product_row + '"></select></td>';
  html += '  <td class="text-left"><button type="button" onclick="$(\'#product-row' + product_row + '\').remove();" data-toggle="tooltip" title="{{ button_remove }}" class="btn btn-danger"><i class="fa fa-minus-circle"></i></button></td>';
  html += '</tr>';

  // Append the new product row to the table body
  $('#products tbody').append(html);
  initializeSelect2(product_row);
  product_row++;
}

// Function to initialize Select2 for the product dropdown
function initializeSelect2(row) {
  $('.product-select[data-row="' + row + '"]').select2({
    ajax: {
      url: 'index.php?route=catalog/product/autocomplete&user_token={{ user_token }}',
      dataType: 'json',
      delay: 250, // Delay to reduce the number of requests
      data: function (params) {
        return {
          filter_name: params.term // Filter products based on user input
        };
      },
      processResults: function (data) {
        return {
          results: $.map(data, function (item) {
            return {
              text: item.name,
              id: item.product_id
            }
          })
        };
      },
      cache: true
    },
    minimumInputLength: 1 // Minimum characters required before search
  }).on('select2:select', function (e) {
    var data = e.params.data;
    // AJAX request to get the units for the selected product
    $.ajax({
      url: 'index.php?route=catalog/product/getProductUnits&user_token={{ user_token }}&product_id=' + data.id,
      dataType: 'json',
      success: function(json) {
        var unitSelect = $('.unit-select[data-row="' + row + '"]');
        unitSelect.empty(); // Clear existing options
        $.each(json, function(key, value) {
          // Append new units to the unit dropdown
          unitSelect.append($('<option></option>').attr('value', value.unit_id).text(value.unit_name));
        });
      }
    });
  });
}

$(document).ready(function() {
  // Initialize DataTable for the products table
  $('#products').DataTable({
    'paging'      : true,
    'lengthChange': false,
    'searching'   : false,
    'ordering'    : true,
    'info'        : true,
    'autoWidth'   : false
  });

  // Initialize Select2 for each existing product row
  $('.product-select').each(function() {
    var row = $(this).data('row');
    initializeSelect2(row);
  });
});
//--></script>
{{ footer }}