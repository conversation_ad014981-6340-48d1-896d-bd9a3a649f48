{{ header }}
{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <div class="btn-group">
          <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown">
            <i class="fa fa-cog"></i> {{ text_quick_actions }} <span class="caret"></span>
          </button>
          <ul class="dropdown-menu dropdown-menu-right">
            <li><a href="{{ add_employee_url }}"><i class="fa fa-user-plus"></i> {{ text_add_employee }}</a></li>
            <li><a href="{{ attendance_url }}"><i class="fa fa-clock-o"></i> {{ text_record_attendance }}</a></li>
            <li><a href="{{ leave_url }}"><i class="fa fa-calendar"></i> {{ text_manage_leaves }}</a></li>
            <li><a href="{{ payroll_url }}"><i class="fa fa-money"></i> {{ text_process_payroll }}</a></li>
            <li class="divider"></li>
            <li><a href="{{ reports_url }}"><i class="fa fa-bar-chart"></i> {{ text_hr_reports }}</a></li>
          </ul>
        </div>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        <li><a href="{{ home }}">{{ text_home }}</a></li>
        <li class="active">{{ heading_title }}</li>
      </ul>
    </div>
  </div>

  <div class="container-fluid">
    <!-- KPI Cards Row -->
    <div class="row">
      <div class="col-lg-3 col-md-6 col-sm-6">
        <div class="tile-stats tile-primary">
          <div class="icon"><i class="fa fa-users"></i></div>
          <div class="count">{{ total_employees }}</div>
          <h3>{{ text_total_employees }}</h3>
          <p>{{ text_active_employees }}: <span class="badge badge-success">{{ active_employees }}</span></p>
          <div class="progress progress-sm">
            <div class="progress-bar progress-bar-success" style="width: {{ (active_employees / total_employees * 100)|round }}%"></div>
          </div>
        </div>
      </div>

      <div class="col-lg-3 col-md-6 col-sm-6">
        <div class="tile-stats tile-success">
          <div class="icon"><i class="fa fa-clock-o"></i></div>
          <div class="count">{{ present_today }}</div>
          <h3>{{ text_present_today }}</h3>
          <p>{{ text_absent_today }}: <span class="badge badge-danger">{{ absent_today }}</span></p>
          <div class="progress progress-sm">
            <div class="progress-bar progress-bar-success" style="width: {{ attendance_percentage }}%"></div>
          </div>
        </div>
      </div>

      <div class="col-lg-3 col-md-6 col-sm-6">
        <div class="tile-stats tile-warning">
          <div class="icon"><i class="fa fa-calendar"></i></div>
          <div class="count">{{ pending_leaves }}</div>
          <h3>{{ text_pending_leaves }}</h3>
          <p>{{ text_approved_leaves }}: <span class="badge badge-info">{{ approved_leaves }}</span></p>
          <div class="progress progress-sm">
            <div class="progress-bar progress-bar-warning" style="width: {{ leave_approval_rate }}%"></div>
          </div>
        </div>
      </div>

      <div class="col-lg-3 col-md-6 col-sm-6">
        <div class="tile-stats tile-info">
          <div class="icon"><i class="fa fa-money"></i></div>
          <div class="count">{{ currency_symbol }}{{ monthly_payroll|number_format }}</div>
          <h3>{{ text_monthly_payroll }}</h3>
          <p>{{ text_vs_last_month }}: 
            {% if payroll_change >= 0 %}
              <span class="badge badge-success">+{{ payroll_change }}%</span>
            {% else %}
              <span class="badge badge-danger">{{ payroll_change }}%</span>
            {% endif %}
          </p>
          <div class="progress progress-sm">
            <div class="progress-bar progress-bar-info" style="width: 75%"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Charts and Analytics Row -->
    <div class="row">
      <div class="col-md-8">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title">
              <i class="fa fa-line-chart"></i> {{ text_attendance_trends }}
              <div class="pull-right">
                <div class="btn-group btn-group-xs">
                  <button type="button" class="btn btn-default active" data-period="7">7D</button>
                  <button type="button" class="btn btn-default" data-period="30">30D</button>
                  <button type="button" class="btn btn-default" data-period="90">90D</button>
                </div>
              </div>
            </h3>
          </div>
          <div class="panel-body">
            <canvas id="attendanceChart" height="100"></canvas>
          </div>
        </div>
      </div>

      <div class="col-md-4">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-pie-chart"></i> {{ text_department_distribution }}</h3>
          </div>
          <div class="panel-body">
            <canvas id="departmentChart" height="200"></canvas>
          </div>
        </div>
      </div>
    </div>

    <!-- Activities and Alerts Row -->
    <div class="row">
      <div class="col-md-4">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title">
              <i class="fa fa-bell"></i> {{ text_recent_activities }}
              <span class="badge">{{ recent_activities|length }}</span>
            </h3>
          </div>
          <div class="panel-body activity-feed">
            {% for activity in recent_activities %}
            <div class="activity-item">
              <div class="activity-icon activity-{{ activity.type }}">
                <i class="fa fa-{{ activity.icon }}"></i>
              </div>
              <div class="activity-content">
                <strong>{{ activity.employee_name }}</strong>
                <p>{{ activity.description }}</p>
                <small class="text-muted">
                  <i class="fa fa-clock-o"></i> {{ activity.time_ago }}
                </small>
              </div>
            </div>
            {% endfor %}
            {% if recent_activities|length == 0 %}
            <div class="text-center text-muted">
              <i class="fa fa-info-circle"></i> {{ text_no_recent_activities }}
            </div>
            {% endif %}
          </div>
        </div>
      </div>

      <div class="col-md-4">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title">
              <i class="fa fa-birthday-cake"></i> {{ text_upcoming_events }}
            </h3>
          </div>
          <div class="panel-body">
            <!-- Birthdays -->
            {% if birthdays %}
            <h5><i class="fa fa-gift"></i> {{ text_birthdays_this_week }}</h5>
            <ul class="list-unstyled event-list">
              {% for birthday in birthdays %}
              <li class="event-item">
                <div class="event-date">{{ birthday.day }}</div>
                <div class="event-content">
                  <strong>{{ birthday.employee_name }}</strong>
                  <small>{{ birthday.department }}</small>
                </div>
              </li>
              {% endfor %}
            </ul>
            {% endif %}

            <!-- Work Anniversaries -->
            {% if anniversaries %}
            <h5><i class="fa fa-trophy"></i> {{ text_work_anniversaries }}</h5>
            <ul class="list-unstyled event-list">
              {% for anniversary in anniversaries %}
              <li class="event-item">
                <div class="event-date">{{ anniversary.years }}Y</div>
                <div class="event-content">
                  <strong>{{ anniversary.employee_name }}</strong>
                  <small>{{ anniversary.department }}</small>
                </div>
              </li>
              {% endfor %}
            </ul>
            {% endif %}

            {% if not birthdays and not anniversaries %}
            <div class="text-center text-muted">
              <i class="fa fa-calendar-o"></i> {{ text_no_upcoming_events }}
            </div>
            {% endif %}
          </div>
        </div>
      </div>

      <div class="col-md-4">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title">
              <i class="fa fa-exclamation-triangle"></i> {{ text_hr_alerts }}
              {% if alerts|length > 0 %}
                <span class="badge badge-danger">{{ alerts|length }}</span>
              {% endif %}
            </h3>
          </div>
          <div class="panel-body">
            {% if alerts %}
            <ul class="list-unstyled alert-list">
              {% for alert in alerts %}
              <li class="alert-item alert-{{ alert.priority }}">
                <div class="alert-icon">
                  <i class="fa fa-{{ alert.icon }}"></i>
                </div>
                <div class="alert-content">
                  <strong>{{ alert.title }}</strong>
                  <p>{{ alert.message }}</p>
                  <small class="text-muted">{{ alert.date_created }}</small>
                </div>
                {% if alert.action_url %}
                <div class="alert-action">
                  <a href="{{ alert.action_url }}" class="btn btn-xs btn-primary">{{ text_take_action }}</a>
                </div>
                {% endif %}
              </li>
              {% endfor %}
            </ul>
            {% else %}
            <div class="text-center text-muted">
              <i class="fa fa-check-circle"></i> {{ text_no_alerts }}
            </div>
            {% endif %}
          </div>
        </div>
      </div>
    </div>

    <!-- Quick Stats Row -->
    <div class="row">
      <div class="col-md-12">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-dashboard"></i> {{ text_quick_stats }}</h3>
          </div>
          <div class="panel-body">
            <div class="row">
              <div class="col-md-2 col-sm-4 col-xs-6">
                <div class="stat-box">
                  <div class="stat-number">{{ new_hires_this_month }}</div>
                  <div class="stat-label">{{ text_new_hires }}</div>
                </div>
              </div>
              <div class="col-md-2 col-sm-4 col-xs-6">
                <div class="stat-box">
                  <div class="stat-number">{{ resignations_this_month }}</div>
                  <div class="stat-label">{{ text_resignations }}</div>
                </div>
              </div>
              <div class="col-md-2 col-sm-4 col-xs-6">
                <div class="stat-box">
                  <div class="stat-number">{{ avg_attendance_rate }}%</div>
                  <div class="stat-label">{{ text_avg_attendance }}</div>
                </div>
              </div>
              <div class="col-md-2 col-sm-4 col-xs-6">
                <div class="stat-box">
                  <div class="stat-number">{{ pending_reviews }}</div>
                  <div class="stat-label">{{ text_pending_reviews }}</div>
                </div>
              </div>
              <div class="col-md-2 col-sm-4 col-xs-6">
                <div class="stat-box">
                  <div class="stat-number">{{ training_sessions }}</div>
                  <div class="stat-label">{{ text_training_sessions }}</div>
                </div>
              </div>
              <div class="col-md-2 col-sm-4 col-xs-6">
                <div class="stat-box">
                  <div class="stat-number">{{ employee_satisfaction }}%</div>
                  <div class="stat-label">{{ text_satisfaction_rate }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
/* Enhanced Tile Stats */
.tile-stats {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
}

.tile-stats:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.tile-stats::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #26B99A, #2ECC71);
}

.tile-primary::before { background: linear-gradient(90deg, #3498DB, #2980B9); }
.tile-success::before { background: linear-gradient(90deg, #2ECC71, #27AE60); }
.tile-warning::before { background: linear-gradient(90deg, #F39C12, #E67E22); }
.tile-info::before { background: linear-gradient(90deg, #1ABC9C, #16A085); }

.tile-stats .icon {
  float: left;
  font-size: 48px;
  color: #26B99A;
  margin-right: 15px;
  opacity: 0.8;
}

.tile-stats .count {
  font-size: 32px;
  font-weight: 700;
  color: #2C3E50;
  line-height: 1;
  margin-bottom: 5px;
}

.tile-stats h3 {
  margin: 5px 0 10px 0;
  color: #34495E;
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.tile-stats p {
  color: #7F8C8D;
  margin: 0 0 10px 0;
  font-size: 13px;
}

.progress-sm {
  height: 4px;
  margin-bottom: 0;
}

/* Activity Feed */
.activity-feed {
  max-height: 400px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ECF0F1;
}

.activity-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  flex-shrink: 0;
}

.activity-success { background: #D5EDDA; color: #155724; }
.activity-warning { background: #FFF3CD; color: #856404; }
.activity-info { background: #D1ECF1; color: #0C5460; }
.activity-danger { background: #F8D7DA; color: #721C24; }

.activity-content {
  flex: 1;
}

.activity-content p {
  margin: 5px 0;
  color: #6C757D;
  font-size: 13px;
}

/* Event List */
.event-list {
  max-height: 300px;
  overflow-y: auto;
}

.event-item {
  display: flex;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #ECF0F1;
}

.event-item:last-child {
  border-bottom: none;
}

.event-date {
  width: 40px;
  height: 40px;
  background: #3498DB;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 12px;
  margin-right: 15px;
  flex-shrink: 0;
}

.event-content {
  flex: 1;
}

.event-content small {
  display: block;
  color: #6C757D;
}

/* Alert List */
.alert-list {
  max-height: 400px;
  overflow-y: auto;
}

.alert-item {
  display: flex;
  padding: 12px;
  margin-bottom: 10px;
  border-radius: 6px;
  border-left: 4px solid;
}

.alert-high { 
  background: #FFF5F5; 
  border-left-color: #E53E3E; 
}

.alert-medium { 
  background: #FFFBF0; 
  border-left-color: #DD6B20; 
}

.alert-low { 
  background: #F0FFF4; 
  border-left-color: #38A169; 
}

.alert-icon {
  margin-right: 12px;
  color: inherit;
}

.alert-content {
  flex: 1;
}

.alert-content p {
  margin: 5px 0;
  font-size: 13px;
}

.alert-action {
  margin-left: 10px;
}

/* Quick Stats */
.stat-box {
  text-align: center;
  padding: 15px;
  background: #F8F9FA;
  border-radius: 6px;
  margin-bottom: 15px;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: #2C3E50;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 12px;
  color: #6C757D;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .tile-stats .icon {
    font-size: 36px;
  }
  
  .tile-stats .count {
    font-size: 24px;
  }
  
  .activity-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .activity-icon {
    margin-bottom: 10px;
  }
}

/* Badge Styles */
.badge {
  font-size: 11px;
  padding: 3px 8px;
}

.badge-success { background-color: #28A745; }
.badge-danger { background-color: #DC3545; }
.badge-warning { background-color: #FFC107; color: #212529; }
.badge-info { background-color: #17A2B8; }
</style>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
$(document).ready(function() {
    // Attendance Trends Chart
    var ctx1 = document.getElementById('attendanceChart').getContext('2d');
    var attendanceChart = new Chart(ctx1, {
        type: 'line',
        data: {
            labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
            datasets: [{
                label: 'Present',
                data: [85, 90, 88, 92, 87, 45, 30],
                borderColor: '#2ECC71',
                backgroundColor: 'rgba(46, 204, 113, 0.1)',
                tension: 0.4,
                fill: true
            }, {
                label: 'Absent',
                data: [15, 10, 12, 8, 13, 5, 2],
                borderColor: '#E74C3C',
                backgroundColor: 'rgba(231, 76, 60, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    }
                },
                x: {
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    }
                }
            }
        }
    });

    // Department Distribution Chart
    var ctx2 = document.getElementById('departmentChart').getContext('2d');
    var departmentChart = new Chart(ctx2, {
        type: 'doughnut',
        data: {
            labels: ['IT', 'Sales', 'HR', 'Finance', 'Marketing'],
            datasets: [{
                data: [30, 25, 15, 20, 10],
                backgroundColor: [
                    '#3498DB',
                    '#2ECC71', 
                    '#F39C12',
                    '#E74C3C',
                    '#9B59B6'
                ],
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                }
            }
        }
    });

    // Period filter for attendance chart
    $('.btn-group button').on('click', function() {
        $('.btn-group button').removeClass('active');
        $(this).addClass('active');
        
        var period = $(this).data('period');
        // Load new data based on period
        loadAttendanceData(period);
    });

    function loadAttendanceData(period) {
        // Simulate data loading
        var newData = {
            '7': {
                labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                present: [85, 90, 88, 92, 87, 45, 30],
                absent: [15, 10, 12, 8, 13, 5, 2]
            },
            '30': {
                labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
                present: [450, 440, 460, 435],
                absent: [50, 60, 40, 65]
            },
            '90': {
                labels: ['Month 1', 'Month 2', 'Month 3'],
                present: [1800, 1750, 1820],
                absent: [200, 250, 180]
            }
        };
        
        if (newData[period]) {
            attendanceChart.data.labels = newData[period].labels;
            attendanceChart.data.datasets[0].data = newData[period].present;
            attendanceChart.data.datasets[1].data = newData[period].absent;
            attendanceChart.update();
        }
    }

    // Auto-refresh dashboard every 5 minutes
    setInterval(function() {
        // Refresh specific sections instead of full page reload
        refreshDashboardData();
    }, 300000);
    
    function refreshDashboardData() {
        // AJAX call to refresh dashboard data
        console.log('Refreshing dashboard data...');
    }
});
</script>

{{ footer }}