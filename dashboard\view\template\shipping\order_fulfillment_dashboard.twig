{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="submit" form="shipping\order_fulfillment-form" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fas fa-save"></i></button>
        <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-light"><i class="fas fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    
    <div class="card">
      <div class="card-header"><i class="fas fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}chart-bar{% elif screen_type == 'settings' %}cog{% else %}pencil-alt{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="card-body">
        <form id="shipping\order_fulfillment-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-alerts">{{ text_alerts }}</label>
            <div class="col-sm-10">
              <input type="text" name="alerts" value="{{ alerts }}" placeholder="{{ text_alerts }}" id="input-alerts" class="form-control" />
              {% if error_alerts %}
                <div class="invalid-feedback">{{ error_alerts }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-charts_data">{{ text_charts_data }}</label>
            <div class="col-sm-10">
              <input type="text" name="charts_data" value="{{ charts_data }}" placeholder="{{ text_charts_data }}" id="input-charts_data" class="form-control" />
              {% if error_charts_data %}
                <div class="invalid-feedback">{{ error_charts_data }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-dashboard">{{ text_dashboard }}</label>
            <div class="col-sm-10">
              <input type="text" name="dashboard" value="{{ dashboard }}" placeholder="{{ text_dashboard }}" id="input-dashboard" class="form-control" />
              {% if error_dashboard %}
                <div class="invalid-feedback">{{ error_dashboard }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_customer">{{ text_filter_customer }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_customer" value="{{ filter_customer }}" placeholder="{{ text_filter_customer }}" id="input-filter_customer" class="form-control" />
              {% if error_filter_customer %}
                <div class="invalid-feedback">{{ error_filter_customer }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_date_end">{{ text_filter_date_end }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_date_end" value="{{ filter_date_end }}" placeholder="{{ text_filter_date_end }}" id="input-filter_date_end" class="form-control" />
              {% if error_filter_date_end %}
                <div class="invalid-feedback">{{ error_filter_date_end }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_date_start">{{ text_filter_date_start }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_date_start" value="{{ filter_date_start }}" placeholder="{{ text_filter_date_start }}" id="input-filter_date_start" class="form-control" />
              {% if error_filter_date_start %}
                <div class="invalid-feedback">{{ error_filter_date_start }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_order_id">{{ text_filter_order_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_order_id" value="{{ filter_order_id }}" placeholder="{{ text_filter_order_id }}" id="input-filter_order_id" class="form-control" />
              {% if error_filter_order_id %}
                <div class="invalid-feedback">{{ error_filter_order_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="invalid-feedback">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-order">{{ text_order }}</label>
            <div class="col-sm-10">
              <input type="text" name="order" value="{{ order }}" placeholder="{{ text_order }}" id="input-order" class="form-control" />
              {% if error_order %}
                <div class="invalid-feedback">{{ error_order }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-orders">{{ text_orders }}</label>
            <div class="col-sm-10">
              <input type="text" name="orders" value="{{ orders }}" placeholder="{{ text_orders }}" id="input-orders" class="form-control" />
              {% if error_orders %}
                <div class="invalid-feedback">{{ error_orders }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-shipping_companies">{{ text_shipping_companies }}</label>
            <div class="col-sm-10">
              <input type="text" name="shipping_companies" value="{{ shipping_companies }}" placeholder="{{ text_shipping_companies }}" id="input-shipping_companies" class="form-control" />
              {% if error_shipping_companies %}
                <div class="invalid-feedback">{{ error_shipping_companies }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-statistics">{{ text_statistics }}</label>
            <div class="col-sm-10">
              <input type="text" name="statistics" value="{{ statistics }}" placeholder="{{ text_statistics }}" id="input-statistics" class="form-control" />
              {% if error_statistics %}
                <div class="invalid-feedback">{{ error_statistics }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="invalid-feedback">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}