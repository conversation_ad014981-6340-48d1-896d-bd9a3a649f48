<?php
/**
 * نموذج الرسائل الداخلية المتقدم
 * Internal Messages Model
 * 
 * نموذج البيانات لنظام الرسائل الداخلية مع threads ومرفقات
 * مطور بمستوى عالمي لتفوق على Odoo
 * 
 * @package    AYM ERP
 * <AUTHOR> ERP Development Team
 * @copyright  2024 AYM ERP
 * @license    Proprietary
 * @version    1.0.0
 * @link       https://aym-erp.com
 * @since      2024-12-19
 */

class ModelCommunicationMessages extends Model {
    
    /**
     * إرسال رسالة جديدة - محدث للتوافق مع الجداول الجديدة
     */
    public function sendMessage($data) {
        // استخدام الجدول الصحيح cod_message
        $this->db->query("INSERT INTO " . DB_PREFIX . "message SET
            sender_id = '" . (int)$this->user->getId() . "',
            subject = '" . $this->db->escape($data['subject']) . "',
            content = '" . $this->db->escape($data['content'] ?? $data['message']) . "',
            message_type = '" . $this->db->escape($data['message_type'] ?? 'personal') . "',
            priority = '" . $this->db->escape($data['priority'] ?? 'normal') . "',
            parent_message_id = '" . (int)($data['parent_message_id'] ?? 0) . "',
            thread_id = '" . $this->db->escape($data['thread_id'] ?? uniqid('thread_')) . "',
            attachments = '" . $this->db->escape(json_encode($data['attachments'] ?? [])) . "',
            metadata = '" . $this->db->escape(json_encode($data['metadata'] ?? [])) . "',
            scheduled_at = " . (isset($data['scheduled_at']) ? "'" . $this->db->escape($data['scheduled_at']) . "'" : "NULL") . ",
            expires_at = " . (isset($data['expires_at']) ? "'" . $this->db->escape($data['expires_at']) . "'" : "NULL") . ",
            read_receipt_required = '" . (int)($data['read_receipt_required'] ?? 0) . "',
            delivery_receipt_required = '" . (int)($data['delivery_receipt_required'] ?? 0) . "',
            is_encrypted = '" . (int)($data['is_encrypted'] ?? 0) . "',
            status = 'sent',
            created_at = NOW()");

        $message_id = $this->db->getLastId();
        
        // إضافة المستقبلين باستخدام الجدول الصحيح
        if (!empty($data['recipients'])) {
            foreach ($data['recipients'] as $recipient_id) {
                $this->addMessageRecipient($message_id, $recipient_id);
            }
        }

        // إرسال إشعارات للمستقبلين باستخدام الخدمات المركزية
        if (!empty($data['recipients'])) {
            try {
                $this->load->model('core/central_service_manager');
                $this->model_core_central_service_manager->sendBulkNotification(
                    $data['recipients'],
                    'رسالة جديدة: ' . $data['subject'],
                    'لديك رسالة جديدة من ' . $this->user->getFirstName(),
                    'info',
                    [
                        'message_id' => $message_id,
                        'link' => 'communication/messages/view&message_id=' . $message_id,
                        'priority' => $data['priority'] ?? 'normal'
                    ]
                );
            } catch (Exception $e) {
                // Fallback للإشعارات المباشرة
                $this->load->model('communication/unified_notification');
                foreach ($data['recipients'] as $recipient_id) {
                    $this->model_communication_unified_notification->addNotification([
                        'title' => 'رسالة جديدة: ' . $data['subject'],
                        'content' => substr($data['content'] ?? $data['message'], 0, 100) . '...',
                        'type' => 'info',
                        'module' => 'communication',
                        'reference_type' => 'message',
                        'reference_id' => $message_id,
                        'user_id' => $recipient_id
                    ]);
                }
            }
        }
        
        // تسجيل النشاط باستخدام الخدمات المركزية
        try {
            $this->load->model('core/central_service_manager');
            $this->model_core_central_service_manager->logCreate(
                'communication',
                'message',
                $message_id,
                'تم إنشاء رسالة جديدة: ' . ($data['subject'] ?? 'بدون موضوع'),
                [
                    'message_data' => $data,
                    'recipients_count' => count($data['recipients'] ?? [])
                ]
            );
        } catch (Exception $e) {
            error_log("Failed to log message creation via central service: " . $e->getMessage());

            // النظام الاحتياطي
            try {
                $this->load->model('activity_log');
                $this->model_activity_log->logCreate('communication', 'message', $message_id, $data);
            } catch (Exception $e2) {
                error_log("CRITICAL: Both logging systems failed: " . $e2->getMessage());
            }
        }
        
        return $message_id;
    }
    
    /**
     * إضافة مستقبل للرسالة - محدث للتوافق مع الجداول الجديدة
     */
    private function addMessageRecipient($message_id, $recipient_id) {
        $this->db->query("INSERT INTO " . DB_PREFIX . "message_recipient SET
            message_id = '" . (int)$message_id . "',
            user_id = '" . (int)$recipient_id . "',
            is_read = '0',
            received_at = NOW()");
    }
    
    /**
     * الحصول على رسائل المستخدم
     */
    public function getMessages($user_id, $start = 0, $limit = 10) {
        $query = $this->db->query("SELECT m.*, 
            CONCAT(u.firstname, ' ', u.lastname) as sender_name,
            u.image as sender_image,
            mr.status as read_status,
            mr.read_at,
            mr.starred,
            GROUP_CONCAT(DISTINCT CONCAT(ur.firstname, ' ', ur.lastname) SEPARATOR ', ') as other_recipients
            FROM " . DB_PREFIX . "message m
            LEFT JOIN " . DB_PREFIX . "user u ON (m.sender_id = u.user_id)
            INNER JOIN " . DB_PREFIX . "message_recipient mr ON (m.message_id = mr.message_id)
            LEFT JOIN " . DB_PREFIX . "message_recipient mr2 ON (m.message_id = mr2.message_id AND mr2.user_id != '" . (int)$user_id . "')
            LEFT JOIN " . DB_PREFIX . "user ur ON (mr2.user_id = ur.user_id)
            WHERE mr.user_id = '" . (int)$user_id . "'
            AND m.parent_message_id = 0
            GROUP BY m.message_id
            ORDER BY m.created_at DESC
            LIMIT " . (int)$start . ", " . (int)$limit);
        
        return $query->rows;
    }
    
    /**
     * الحصول على رسالة محددة
     */
    public function getMessage($message_id, $user_id) {
        $query = $this->db->query("SELECT m.*, 
            CONCAT(u.firstname, ' ', u.lastname) as sender_name,
            u.image as sender_image,
            mr.status as read_status,
            mr.read_at,
            mr.starred
            FROM " . DB_PREFIX . "message m
            LEFT JOIN " . DB_PREFIX . "user u ON (m.sender_id = u.user_id)
            INNER JOIN " . DB_PREFIX . "message_recipient mr ON (m.message_id = mr.message_id)
            WHERE m.message_id = '" . (int)$message_id . "'
            AND mr.user_id = '" . (int)$user_id . "'");
        
        if ($query->num_rows) {
            $message = $query->row;
            $message['attachments'] = json_decode($message['attachments'], true);
            $message['mentions'] = json_decode($message['mentions'], true);
            return $message;
        }
        
        return false;
    }
    
    /**
     * الحصول على thread الرسالة (الردود)
     */
    public function getThread($message_id, $user_id) {
        $query = $this->db->query("SELECT m.*, 
            CONCAT(u.firstname, ' ', u.lastname) as sender_name,
            u.image as sender_image
            FROM " . DB_PREFIX . "message m
            LEFT JOIN " . DB_PREFIX . "user u ON (m.sender_id = u.user_id)
            INNER JOIN " . DB_PREFIX . "message_recipient mr ON (m.message_id = mr.message_id)
            WHERE (m.parent_message_id = '" . (int)$message_id . "'
                   OR m.message_id = '" . (int)$message_id . "')
            AND mr.user_id = '" . (int)$user_id . "'
            ORDER BY m.created_at ASC");
        
        $messages = [];
        foreach ($query->rows as $row) {
            $row['attachments'] = json_decode($row['attachments'], true);
            $row['mentions'] = json_decode($row['mentions'], true);
            $messages[] = $row;
        }
        
        return $messages;
    }
    
    /**
     * الحصول على مشاركي الرسالة
     */
    public function getParticipants($message_id) {
        $query = $this->db->query("SELECT mr.*, 
            CONCAT(u.firstname, ' ', u.lastname) as name,
            u.image,
            u.email
            FROM " . DB_PREFIX . "message_recipient mr
            LEFT JOIN " . DB_PREFIX . "user u ON (mr.recipient_id = u.user_id)
            WHERE mr.message_id = '" . (int)$message_id . "'");
        
        return $query->rows;
    }
    
    /**
     * تحديد رسالة كمقروءة
     */
    public function markAsRead($message_id, $user_id) {
        $this->db->query("UPDATE " . DB_PREFIX . "message_recipient SET
            status = 'read',
            read_at = NOW()
            WHERE message_id = '" . (int)$message_id . "'
            AND recipient_id = '" . (int)$user_id . "'");
    }
    
    /**
     * تحديد جميع الرسائل كمقروءة
     */
    public function markAllAsRead($user_id) {
        $this->db->query("UPDATE " . DB_PREFIX . "message_recipient SET
            status = 'read',
            read_at = NOW()
            WHERE recipient_id = '" . (int)$user_id . "'
            AND status = 'unread'");
    }
    
    /**
     * إضافة/إزالة نجمة للرسالة
     */
    public function toggleStar($message_id, $user_id) {
        $query = $this->db->query("SELECT starred FROM " . DB_PREFIX . "message_recipient 
            WHERE message_id = '" . (int)$message_id . "' 
            AND recipient_id = '" . (int)$user_id . "'");
        
        if ($query->num_rows) {
            $starred = $query->row['starred'] ? 0 : 1;
            $this->db->query("UPDATE " . DB_PREFIX . "message_recipient SET
                starred = '" . (int)$starred . "'
                WHERE message_id = '" . (int)$message_id . "'
                AND recipient_id = '" . (int)$user_id . "'");
            
            return $starred;
        }
        
        return false;
    }
    
    /**
     * حذف رسالة (للمستخدم فقط)
     */
    public function deleteMessage($message_id, $user_id) {
        $this->db->query("UPDATE " . DB_PREFIX . "message_recipient SET
            status = 'deleted',
            deleted_at = NOW()
            WHERE message_id = '" . (int)$message_id . "'
            AND recipient_id = '" . (int)$user_id . "'");
    }
    
    /**
     * الحصول على عدد الرسائل غير المقروءة
     */
    public function getUnreadCount($user_id) {
        $query = $this->db->query("SELECT COUNT(*) as total
            FROM " . DB_PREFIX . "message_recipient mr
            INNER JOIN " . DB_PREFIX . "message m ON (mr.message_id = m.message_id)
            WHERE mr.user_id = '" . (int)$user_id . "'
            AND mr.is_read = '0'");
        
        return $query->row['total'];
    }
    
    /**
     * البحث في الرسائل
     */
    public function searchMessages($user_id, $search_term, $start = 0, $limit = 10) {
        $query = $this->db->query("SELECT m.*, 
            CONCAT(u.firstname, ' ', u.lastname) as sender_name,
            u.image as sender_image,
            mr.status as read_status,
            mr.starred
            FROM " . DB_PREFIX . "message m
            LEFT JOIN " . DB_PREFIX . "user u ON (m.sender_id = u.user_id)
            INNER JOIN " . DB_PREFIX . "message_recipient mr ON (m.message_id = mr.message_id)
            WHERE mr.user_id = '" . (int)$user_id . "'
            AND m.status != 'deleted'
            AND (m.subject LIKE '%" . $this->db->escape($search_term) . "%'
                 OR m.message LIKE '%" . $this->db->escape($search_term) . "%')
            ORDER BY m.created_at DESC
            LIMIT " . (int)$start . ", " . (int)$limit);
        
        return $query->rows;
    }
    
    /**
     * الحصول على الرسائل المميزة بنجمة
     */
    public function getStarredMessages($user_id, $start = 0, $limit = 10) {
        $query = $this->db->query("SELECT m.*, 
            CONCAT(u.firstname, ' ', u.lastname) as sender_name,
            u.image as sender_image,
            mr.status as read_status,
            mr.starred
            FROM " . DB_PREFIX . "message m
            LEFT JOIN " . DB_PREFIX . "user u ON (m.sender_id = u.user_id)
            INNER JOIN " . DB_PREFIX . "message_recipient mr ON (m.message_id = mr.message_id)
            WHERE mr.user_id = '" . (int)$user_id . "'
            AND JSON_EXTRACT(mr.metadata, '$.starred') = 1
            AND m.status != 'deleted'
            ORDER BY m.created_at DESC
            LIMIT " . (int)$start . ", " . (int)$limit);
        
        return $query->rows;
    }
    
    /**
     * الحصول على الرسائل المرسلة
     */
    public function getSentMessages($user_id, $start = 0, $limit = 10) {
        $query = $this->db->query("SELECT m.*, 
            GROUP_CONCAT(DISTINCT CONCAT(u.firstname, ' ', u.lastname) SEPARATOR ', ') as recipients
            FROM " . DB_PREFIX . "message m
            LEFT JOIN " . DB_PREFIX . "message_recipient mr ON (m.message_id = mr.message_id)
            LEFT JOIN " . DB_PREFIX . "user u ON (mr.user_id = u.user_id)
            WHERE m.sender_id = '" . (int)$user_id . "'
            GROUP BY m.message_id
            ORDER BY m.created_at DESC
            LIMIT " . (int)$start . ", " . (int)$limit);
        
        return $query->rows;
    }

    /**
     * إضافة رسالة جديدة - دالة مطلوبة من الكونترولر
     */
    public function addMessage($data) {
        return $this->sendMessage($data);
    }

    /**
     * الحصول على رسالة محددة (بدون معرف المستخدم)
     */
    public function getMessageById($message_id) {
        $query = $this->db->query("SELECT m.*,
            CONCAT(u.firstname, ' ', u.lastname) as sender_name,
            u.image as sender_image
            FROM " . DB_PREFIX . "message m
            LEFT JOIN " . DB_PREFIX . "user u ON (m.sender_id = u.user_id)
            WHERE m.message_id = '" . (int)$message_id . "'");

        return $query->row;
    }

    /**
     * تحديد رسالة كمقروءة (نسخة محدثة)
     */
    public function markMessageAsRead($message_id, $user_id) {
        $this->db->query("UPDATE " . DB_PREFIX . "message_recipient SET
            is_read = '1',
            read_at = NOW()
            WHERE message_id = '" . (int)$message_id . "'
            AND user_id = '" . (int)$user_id . "'");
    }

    /**
     * الحصول على مرفقات الرسالة
     */
    public function getMessageAttachments($message_id) {
        $query = $this->db->query("SELECT attachments FROM " . DB_PREFIX . "message
            WHERE message_id = '" . (int)$message_id . "'");

        if ($query->num_rows && !empty($query->row['attachments'])) {
            return json_decode($query->row['attachments'], true);
        }

        return [];
    }

    /**
     * الحصول على ردود الرسالة
     */
    public function getMessageReplies($message_id) {
        $query = $this->db->query("SELECT m.*,
            CONCAT(u.firstname, ' ', u.lastname) as sender_name,
            u.image as sender_image
            FROM " . DB_PREFIX . "message m
            LEFT JOIN " . DB_PREFIX . "user u ON (m.sender_id = u.user_id)
            WHERE m.parent_message_id = '" . (int)$message_id . "'
            ORDER BY m.created_at ASC");

        return $query->rows;
    }

    /**
     * حذف رسالة (نسخة محدثة)
     */
    public function deleteMessagePermanently($message_id, $user_id) {
        // حذف ناعم - تحديث الحالة فقط
        $this->db->query("UPDATE " . DB_PREFIX . "message SET
            status = 'deleted',
            deleted_at = NOW()
            WHERE message_id = '" . (int)$message_id . "'
            AND sender_id = '" . (int)$user_id . "'");

        // أو حذف من جدول المستقبلين إذا كان مستقبل
        $this->db->query("DELETE FROM " . DB_PREFIX . "message_recipient
            WHERE message_id = '" . (int)$message_id . "'
            AND user_id = '" . (int)$user_id . "'");
    }

    /**
     * الحصول على مستقبلي الرسالة
     */
    public function getMessageRecipients($message_id) {
        $query = $this->db->query("SELECT mr.*,
            CONCAT(u.firstname, ' ', u.lastname) as recipient_name,
            u.email as recipient_email
            FROM " . DB_PREFIX . "message_recipient mr
            LEFT JOIN " . DB_PREFIX . "user u ON (mr.user_id = u.user_id)
            WHERE mr.message_id = '" . (int)$message_id . "'");

        return $query->rows;
    }

    /**
     * الحصول على الرسائل الواردة
     */
    public function getInboxMessages($user_id, $filter_data = []) {
        $sql = "SELECT m.*,
            CONCAT(u.firstname, ' ', u.lastname) as sender_name,
            u.image as sender_image,
            mr.is_read,
            mr.read_at
            FROM " . DB_PREFIX . "message m
            LEFT JOIN " . DB_PREFIX . "user u ON (m.sender_id = u.user_id)
            INNER JOIN " . DB_PREFIX . "message_recipient mr ON (m.message_id = mr.message_id)
            WHERE mr.user_id = '" . (int)$user_id . "'
            AND m.status != 'deleted'
            ORDER BY m.created_at DESC";

        if (isset($filter_data['start']) && isset($filter_data['limit'])) {
            $sql .= " LIMIT " . (int)$filter_data['start'] . "," . (int)$filter_data['limit'];
        }

        $query = $this->db->query($sql);
        return $query->rows;
    }

    /**
     * الحصول على إجمالي الرسائل الواردة
     */
    public function getTotalInboxMessages($user_id) {
        $query = $this->db->query("SELECT COUNT(*) as total
            FROM " . DB_PREFIX . "message_recipient mr
            INNER JOIN " . DB_PREFIX . "message m ON (mr.message_id = m.message_id)
            WHERE mr.user_id = '" . (int)$user_id . "'
            AND m.status != 'deleted'");

        return $query->row['total'];
    }

    /**
     * الحصول على إجمالي الرسائل المرسلة
     */
    public function getTotalSentMessages($user_id) {
        $query = $this->db->query("SELECT COUNT(*) as total
            FROM " . DB_PREFIX . "message m
            WHERE m.sender_id = '" . (int)$user_id . "'
            AND m.status != 'deleted'");

        return $query->row['total'];
    }

    /**
     * الحصول على الرسائل المحذوفة
     */
    public function getDeletedMessages($user_id, $filter_data = []) {
        $sql = "SELECT m.*,
            CONCAT(u.firstname, ' ', u.lastname) as sender_name,
            u.image as sender_image
            FROM " . DB_PREFIX . "message m
            LEFT JOIN " . DB_PREFIX . "user u ON (m.sender_id = u.user_id)
            WHERE m.sender_id = '" . (int)$user_id . "'
            AND m.status = 'deleted'
            ORDER BY m.deleted_at DESC";

        if (isset($filter_data['start']) && isset($filter_data['limit'])) {
            $sql .= " LIMIT " . (int)$filter_data['start'] . "," . (int)$filter_data['limit'];
        }

        $query = $this->db->query($sql);
        return $query->rows;
    }

    /**
     * الحصول على إجمالي الرسائل المحذوفة
     */
    public function getTotalDeletedMessages($user_id) {
        $query = $this->db->query("SELECT COUNT(*) as total
            FROM " . DB_PREFIX . "message m
            WHERE m.sender_id = '" . (int)$user_id . "'
            AND m.status = 'deleted'");

        return $query->row['total'];
    }
}
