{{ header }}{{ column_left }}
<div id="content">
<style>
/* تحسينات متقدمة لدفتر الأستاذ العام - Enterprise Grade */
.panel-default {
    border: none;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.panel-default:hover {
    box-shadow: 0 0.25rem 2rem 0 rgba(58, 59, 69, 0.2);
}

.panel-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    border-radius: 8px 8px 0 0;
    padding: 15px;
}

/* تحسين الجدول */
.table {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 0.125rem 0.25rem rgba(0,0,0,0.075);
}

.table thead th {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    border: none;
    font-weight: 600;
    text-align: center;
}

.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
    transform: translateX(2px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* تحسين الأرصدة */
.debit-amount {
    color: #28a745;
    font-weight: 600;
}

.credit-amount {
    color: #dc3545;
    font-weight: 600;
}

.balance-amount {
    color: #007bff;
    font-weight: 700;
}

/* تحسين الفلاتر */
.form-inline {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.form-group {
    flex-grow: 1;
    margin-bottom: 10px;
    margin-right: 10px;
}

.form-control {
    border-radius: 6px;
    border: 1px solid #ced4da;
    transition: all 0.2s ease;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
    transform: scale(1.02);
}

/* تحسين الأزرار */
.btn {
    transition: all 0.2s ease;
    border-radius: 6px;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8 0%, #117a8b 100%);
    border: none;
}

/* تحسين ملخص دفتر الأستاذ */
.ledger-summary {
    background: linear-gradient(135deg, #f1f8ff 0%, #e6f3ff 100%);
    border-left: 4px solid #007bff;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.ledger-summary h4 {
    color: #0056b3;
    margin-top: 0;
    font-weight: 600;
}

/* تحسين بطاقات الحسابات */
.account-card {
    background: white;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.account-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.account-name {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 10px;
}

.account-balance {
    font-size: 1.2em;
    font-weight: 700;
    text-align: center;
}

/* تحسين الرسم البياني */
.chart-container {
    background: white;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 0.125rem 0.25rem rgba(0,0,0,0.075);
    margin-bottom: 20px;
}
</style>

<div class="page-header">
    <div class="container-fluid">
        <div class="pull-right">
            <button type="button" data-toggle="tooltip" title="{{ button_filter }}" onclick="$('#filter-ledger').toggle();" class="btn btn-default"><i class="fa fa-filter"></i></button>
            <button type="button" data-toggle="tooltip" title="{{ button_export }}" onclick="exportLedger('excel')" class="btn btn-success"><i class="fa fa-download"></i></button>
            <button type="button" data-toggle="tooltip" title="{{ button_print }}" onclick="printLedger()" class="btn btn-info"><i class="fa fa-print"></i></button>
        </div>
        <h1>{{ heading_title }}</h1>
        <ul class="breadcrumb">
            {% for breadcrumb in breadcrumbs %}
            <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
            {% endfor %}
        </ul>
    </div>
</div>

<div class="container-fluid">
    <div class="panel panel-default">
        <div class="panel-header">
            <h3 class="panel-title"><i class="fa fa-book"></i> دفتر الأستاذ العام</h3>
        </div>
        
        <!-- ملخص دفتر الأستاذ -->
        <div class="ledger-summary">
            <h4><i class="fa fa-chart-bar"></i> ملخص دفتر الأستاذ</h4>
            <div class="row">
                <div class="col-md-3">
                    <div class="text-center">
                        <h5>إجمالي المدين</h5>
                        <span class="debit-amount">{{ total_debit }}</span>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h5>إجمالي الدائن</h5>
                        <span class="credit-amount">{{ total_credit }}</span>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h5>عدد الحسابات</h5>
                        <span class="badge badge-info">{{ total_accounts }}</span>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h5>عدد الحركات</h5>
                        <span class="badge badge-secondary">{{ total_transactions }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- فلاتر البحث -->
        <div id="filter-ledger" class="well" style="display: none;">
            <div class="row">
                <div class="col-sm-4">
                    <div class="form-group">
                        <label class="control-label" for="input-account">{{ entry_account }}</label>
                        <select name="filter_account" id="input-account" class="form-control select2">
                            <option value="">{{ text_select }}</option>
                            {% for account in accounts %}
                            {% if account.account_id == filter_account %}
                            <option value="{{ account.account_id }}" selected="selected">{{ account.name }}</option>
                            {% else %}
                            <option value="{{ account.account_id }}">{{ account.name }}</option>
                            {% endif %}
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="col-sm-4">
                    <div class="form-group">
                        <label class="control-label" for="input-date-start">{{ entry_date_start }}</label>
                        <div class="input-group date">
                            <input type="text" name="filter_date_start" value="{{ filter_date_start }}" placeholder="{{ entry_date_start }}" data-date-format="YYYY-MM-DD" id="input-date-start" class="form-control" />
                            <span class="input-group-btn">
                                <button class="btn btn-default" type="button"><i class="fa fa-calendar"></i></button>
                            </span>
                        </div>
                    </div>
                </div>
                <div class="col-sm-4">
                    <div class="form-group">
                        <label class="control-label" for="input-date-end">{{ entry_date_end }}</label>
                        <div class="input-group date">
                            <input type="text" name="filter_date_end" value="{{ filter_date_end }}" placeholder="{{ entry_date_end }}" data-date-format="YYYY-MM-DD" id="input-date-end" class="form-control" />
                            <span class="input-group-btn">
                                <button class="btn btn-default" type="button"><i class="fa fa-calendar"></i></button>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="text-right">
                <button type="button" id="button-filter" class="btn btn-primary"><i class="fa fa-search"></i> {{ button_filter }}</button>
            </div>
        </div>

        <!-- الرسم البياني -->
        <div class="chart-container">
            <canvas id="ledgerChart" width="400" height="200"></canvas>
        </div>

        <!-- جدول دفتر الأستاذ -->
        <div class="table-responsive">
            <table class="table table-bordered table-hover" id="ledger-table">
                <thead>
                    <tr>
                        <td class="text-left">{{ column_date }}</td>
                        <td class="text-left">{{ column_account }}</td>
                        <td class="text-left">{{ column_description }}</td>
                        <td class="text-right">{{ column_debit }}</td>
                        <td class="text-right">{{ column_credit }}</td>
                        <td class="text-right">{{ column_balance }}</td>
                        <td class="text-center">{{ column_action }}</td>
                    </tr>
                </thead>
                <tbody>
                    {% if ledger_entries %}
                    {% for entry in ledger_entries %}
                    <tr>
                        <td class="text-left">{{ entry.date }}</td>
                        <td class="text-left">{{ entry.account_name }}</td>
                        <td class="text-left">{{ entry.description }}</td>
                        <td class="text-right debit-amount">{{ entry.debit }}</td>
                        <td class="text-right credit-amount">{{ entry.credit }}</td>
                        <td class="text-right balance-amount">{{ entry.balance }}</td>
                        <td class="text-center">
                            <a href="{{ entry.view }}" data-toggle="tooltip" title="{{ button_view }}" class="btn btn-info btn-sm"><i class="fa fa-eye"></i></a>
                        </td>
                    </tr>
                    {% endfor %}
                    {% else %}
                    <tr>
                        <td class="text-center" colspan="7">{{ text_no_results }}</td>
                    </tr>
                    {% endif %}
                </tbody>
            </table>
        </div>
        
        <div class="row">
            <div class="col-sm-6 text-left">{{ pagination }}</div>
            <div class="col-sm-6 text-right">{{ results }}</div>
        </div>
    </div>
</div>

<script type="text/javascript"><!--
// تحسينات متقدمة لدفتر الأستاذ العام
$(document).ready(function() {
    // تهيئة Select2 للحسابات
    $('.select2').select2({
        placeholder: 'اختر الحساب',
        allowClear: true
    });
    
    // تهيئة DataTables
    $('#ledger-table').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Arabic.json"
        },
        "order": [[ 0, "desc" ]],
        "pageLength": 25
    });
    
    // إضافة تأثيرات متقدمة للجدول
    $('.table tbody tr').hover(function() {
        $(this).find('td').css('background-color', '#f8f9fa');
    }, function() {
        $(this).find('td').css('background-color', '');
    });
    
    // تحسين عرض الأرصدة
    $('.debit-amount').each(function() {
        const value = parseFloat($(this).text().replace(/[^0-9.-]+/g, ''));
        if (value > 0) {
            $(this).addClass('font-weight-bold');
        }
    });
    
    $('.credit-amount').each(function() {
        const value = parseFloat($(this).text().replace(/[^0-9.-]+/g, ''));
        if (value > 0) {
            $(this).addClass('font-weight-bold');
        }
    });
    
    $('.balance-amount').each(function() {
        const value = parseFloat($(this).text().replace(/[^0-9.-]+/g, ''));
        if (value > 0) {
            $(this).addClass('text-success');
        } else if (value < 0) {
            $(this).addClass('text-danger');
        }
    });
    
    // إضافة مؤشر تحميل للعمليات
    $(document).ajaxStart(function() {
        $('body').addClass('loading');
    }).ajaxStop(function() {
        $('body').removeClass('loading');
    });
    
    // تحسين فلاتر التاريخ
    $('.input-group.date').datetimepicker({
        pickTime: false,
        format: 'YYYY-MM-DD'
    });
    
    // إنشاء الرسم البياني
    if ($('#ledgerChart').length) {
        const ctx = document.getElementById('ledgerChart').getContext('2d');
        const ledgerChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [{% for entry in chart_data %}'{{ entry.date }}'{% if not loop.last %},{% endif %}{% endfor %}],
                datasets: [{
                    label: 'الرصيد',
                    data: [{% for entry in chart_data %}{{ entry.balance }}{% if not loop.last %},{% endif %}{% endfor %}],
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0,123,255,0.1)',
                    borderWidth: 2,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }
});

// دالة تصدير دفتر الأستاذ
function exportLedger(format) {
    const account = $('#input-account').val();
    const startDate = $('#input-date-start').val();
    const endDate = $('#input-date-end').val();
    const url = 'index.php?route=accounts/general_ledger/export&format=' + format + 
                '&account=' + account + '&start_date=' + startDate + '&end_date=' + endDate;
    window.open(url, '_blank');
}

// دالة طباعة دفتر الأستاذ
function printLedger() {
    window.print();
}

// دالة فلترة البيانات
$('#button-filter').on('click', function() {
    const account = $('#input-account').val();
    const startDate = $('#input-date-start').val();
    const endDate = $('#input-date-end').val();
    
    const url = 'index.php?route=accounts/general_ledger&filter_account=' + account + 
                '&filter_date_start=' + startDate + '&filter_date_end=' + endDate;
    window.location = url;
});
//--></script>
{{ footer }}