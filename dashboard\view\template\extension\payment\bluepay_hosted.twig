{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="submit" form="form-payment" class="btn btn-primary"><i class="fa fa-save"></i> {{ button_save }}</button>
        <a href="{{ cancel }}" class="btn btn-default"><i class="fa fa-reply"></i> {{ button_cancel }}</a></div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pencil"></i> {{ text_edit }}</h3>
      </div>
      <div class="panel-body">
        <form action="{{ action }}" method="post" enctype="multipart/form-data" id="form-payment" class="form-horizontal">
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-account-name">{{ entry_account_name }}</label>
            <div class="col-sm-10">
              <input type="text" name="payment_bluepay_hosted_account_name" value="{{ payment_bluepay_hosted_account_name }}" placeholder="{{ entry_account_name }}" id="input-account-name" class="form-control" />
              {% if error_account_name %}
              <div class="text-danger">{{ error_account_name }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-account-id">{{ entry_account_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="payment_bluepay_hosted_account_id" value="{{ payment_bluepay_hosted_account_id }}" placeholder="{{ entry_account_id }}" id="input-account-id" class="form-control" />
              {% if error_account_id %}
              <div class="text-danger">{{ error_account_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="payment-bluepay-hosted-secret-key">{{ entry_secret_key }}</label>
            <div class="col-sm-10">
              <input type="text" name="payment_bluepay_hosted_secret_key" value="{{ payment_bluepay_hosted_secret_key }}" placeholder="{{ entry_secret_key }}" id="payment-bluepay-hosted-secret_key" class="form-control" />
              {% if error_secret_key %}
              <div class="text-danger">{{ error_secret_key }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-test">{{ entry_test }}</label>
            <div class="col-sm-10">
              <select name="payment_bluepay_hosted_test" id="input-test" class="form-control">
                {% if payment_bluepay_hosted_test == 'test' %}
                <option value="test" selected="selected">{{ text_test }}</option>
                {% else %}
                <option value="test">{{ text_test }}</option>
                {% endif %}
                {% if payment_bluepay_hosted_test == 'live' %}
                <option value="live" selected="selected">{{ text_live }}</option>
                {% else %}
                <option value="live">{{ text_live }}</option>
                {% endif %}
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-transaction"><span data-toggle="tooltip" title="{{ help_transaction }}">{{ entry_transaction }}</span></label>
            <div class="col-sm-10">
              <select name="payment_bluepay_hosted_transaction" id="input-transaction" class="form-control">
                {% if payment_bluepay_hosted_transaction == 'SALE' %}
                <option value="SALE" selected="selected">{{ text_sale }}</option>
                {% else %}
                <option value="SALE">{{ text_sale }}</option>
                {% endif %}
                {% if payment_bluepay_hosted_transaction == 'AUTH' %}
                <option value="AUTH" selected="selected">{{ text_authenticate }}</option>
                {% else %}
                <option value="AUTH">{{ text_authenticate }}</option>
                {% endif %}
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-amex">{{ entry_card_amex }}</label>
            <div class="col-sm-10">
              <select name="payment_bluepay_hosted_amex" id="input-amex" class="form-control">
                {% if payment_bluepay_hosted_amex %}
                <option value="1" selected="selected">{{ text_enabled }}</option>
                <option value="0">{{ text_disabled }}</option>
                {% else %}
                <option value="1">{{ text_enabled }}</option>
                <option value="0" selected="selected">{{ text_disabled }}</option>
                {% endif %}
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-discover">{{ entry_card_discover }}</label>
            <div class="col-sm-10">
              <select name="payment_bluepay_hosted_discover" id="input-discover" class="form-control">
                {% if payment_bluepay_hosted_discover %}
                <option value="1" selected="selected">{{ text_enabled }}</option>
                <option value="0">{{ text_disabled }}</option>
                {% else %}
                <option value="1">{{ text_enabled }}</option>
                <option value="0" selected="selected">{{ text_disabled }}</option>
                {% endif %}
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-total"><span data-toggle="tooltip" title="{{ help_total }}">{{ entry_total }}</span></label>
            <div class="col-sm-10">
              <input type="text" name="payment_bluepay_hosted_total" value="{{ payment_bluepay_hosted_total }}" placeholder="{{ entry_total }}" id="input-total" class="form-control" />
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-order-status">{{ entry_order_status }}</label>
            <div class="col-sm-10">
              <select name="payment_bluepay_hosted_order_status_id" id="input-order-status" class="form-control">
                {% for order_status in order_statuses %}
                {% if order_status.order_status_id == payment_bluepay_hosted_order_status_id %}
                <option value="{{ order_status.order_status_id }}" selected="selected">{{ order_status.name }}</option>
                {% else %}
                <option value="{{ order_status.order_status_id }}">{{ order_status.name }}</option>
                {% endif %}
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-geo-zone">{{ entry_geo_zone }}</label>
            <div class="col-sm-10">
              <select name="payment_bluepay_hosted_geo_zone_id" id="input-geo-zone" class="form-control">
                <option value="0">{{ text_all_zones }}</option>
                {% for geo_zone in geo_zones %}
                {% if geo_zone.geo_zone_id == payment_bluepay_hosted_geo_zone_id %}
                <option value="{{ geo_zone.geo_zone_id }}" selected="selected">{{ geo_zone.name }}</option>
                {% else %}
                <option value="{{ geo_zone.geo_zone_id }}">{{ geo_zone.name }}</option>
                {% endif %}
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-debug"><span data-toggle="tooltip" title="{{ help_debug }}">{{ entry_debug }}</span></label>
            <div class="col-sm-10">
              <select name="payment_bluepay_hosted_debug" id="input-debug" class="form-control">
                {% if payment_bluepay_hosted_debug %}
                <option value="1" selected="selected">{{ text_enabled }}</option>
                <option value="0">{{ text_disabled }}</option>
                {% else %}
                <option value="1">{{ text_enabled }}</option>
                <option value="0" selected="selected">{{ text_disabled }}</option>
                {% endif %}
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-status">{{ entry_status }}</label>
            <div class="col-sm-10">
              <select name="payment_bluepay_hosted_status" id="input-status" class="form-control">
                {% if payment_bluepay_hosted_status %}
                <option value="1" selected="selected">{{ text_enabled }}</option>
                <option value="0">{{ text_disabled }}</option>
                {% else %}
                <option value="1">{{ text_enabled }}</option>
                <option value="0" selected="selected">{{ text_disabled }}</option>
                {% endif %}
              </select>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
{{ footer }}