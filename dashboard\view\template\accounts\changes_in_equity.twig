{{ header }}{{ column_left }}

<!-- Enterprise Grade Plus CSS for Changes in Equity -->
<style>
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --danger-color: #e74c3c;
    --warning-color: #f39c12;
    --info-color: #17a2b8;
    --equity-color: #6f42c1;
    --light-bg: #f8f9fa;
    --border-color: #dee2e6;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

.equity-container {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: var(--shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.equity-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--equity-color), var(--primary-color), var(--secondary-color));
}

.equity-header {
    text-align: center;
    border-bottom: 3px solid var(--equity-color);
    padding-bottom: 20px;
    margin-bottom: 30px;
    position: relative;
}

.equity-header h2 {
    color: var(--equity-color);
    font-weight: 700;
    font-size: 1.8rem;
    margin: 0;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.equity-summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.equity-summary-card {
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    text-align: center;
    box-shadow: var(--shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.equity-summary-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0,0,0,0.1);
}

.equity-summary-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
}

.equity-summary-card.opening::before { background: var(--equity-color); }
.equity-summary-card.additions::before { background: var(--success-color); }
.equity-summary-card.deductions::before { background: var(--danger-color); }
.equity-summary-card.closing::before { background: var(--info-color); }

.equity-summary-card h4 {
    color: var(--primary-color);
    margin-bottom: 10px;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.equity-summary-card .amount {
    font-size: 1.4rem;
    font-weight: 700;
    font-family: 'Courier New', monospace;
    margin-bottom: 5px;
}

.equity-summary-card .description {
    font-size: 0.8rem;
    color: #6c757d;
    font-weight: 500;
}

.card-opening .amount { color: var(--equity-color); }
.card-additions .amount { color: var(--success-color); }
.card-deductions .amount { color: var(--danger-color); }
.card-closing .amount { color: var(--info-color); }

.equity-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    background: #ffffff;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.equity-table th {
    background: linear-gradient(135deg, var(--equity-color), #5a2d91);
    color: white;
    padding: 15px 12px;
    text-align: center;
    font-weight: 600;
    font-size: 0.9rem;
    border-bottom: 2px solid var(--border-color);
}

.equity-table td {
    padding: 12px;
    border-bottom: 1px solid var(--border-color);
    text-align: center;
    transition: var(--transition);
}

.equity-table tbody tr:hover {
    background: var(--light-bg);
    transform: scale(1.005);
}

.equity-table tbody tr:nth-child(even) {
    background: #fafbfc;
}

.amount-cell {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    text-align: {{ direction == 'rtl' ? 'right' : 'left' }};
}

.amount-positive { 
    color: var(--success-color); 
    font-weight: 600;
}

.amount-negative { 
    color: var(--danger-color); 
    font-weight: 600;
}

.amount-neutral { 
    color: var(--secondary-color); 
    font-weight: 600;
}

.equity-actions {
    display: flex;
    gap: 5px;
    justify-content: center;
}

.equity-actions .btn {
    padding: 5px 10px;
    font-size: 0.8rem;
}

/* Chart Container */
.chart-container {
    background: #ffffff;
    border-radius: var(--border-radius);
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: var(--shadow);
}

.chart-container canvas {
    max-height: 400px;
}

/* Filter Panel */
.filter-panel {
    background: linear-gradient(135deg, #f8f9fa, #ffffff);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: var(--shadow);
}

/* RTL Support */
[dir="rtl"] .equity-table {
    direction: rtl;
}

[dir="rtl"] .amount-cell {
    text-align: right;
}

/* Print Styles */
@media print {
    .equity-container {
        box-shadow: none;
        border: 1px solid #000;
    }
    
    .equity-table th {
        background: #f0f0f0 !important;
        color: #000 !important;
    }
    
    .btn, .card-header, .filter-panel {
        display: none !important;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .equity-table {
        font-size: 0.8rem;
    }
    
    .equity-table th,
    .equity-table td {
        padding: 8px 6px;
    }
    
    .equity-summary-cards {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .equity-actions {
        flex-direction: column;
    }
}
</style>

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-{{ direction == 'rtl' ? 'start' : 'end' }}">
        <div class="btn-group" role="group" aria-label="{{ text_actions }}">
          <button type="button" class="btn btn-success btn-lg" onclick="generateStatement()" 
                  data-bs-toggle="tooltip" title="{{ text_generate_statement }}">
            <i class="fas fa-chart-line me-2"></i> {{ button_generate }}
          </button>
          <div class="btn-group" role="group">
            <button type="button" class="btn btn-info dropdown-toggle" data-bs-toggle="dropdown" 
                    aria-expanded="false" data-bs-toggle="tooltip" title="{{ text_export_options }}">
              <i class="fas fa-download me-2"></i> {{ text_export }}
            </button>
            <ul class="dropdown-menu">
              <li><a class="dropdown-item" href="#" onclick="exportStatement('excel')">
                <i class="fas fa-file-excel text-success me-2"></i> Excel
              </a></li>
              <li><a class="dropdown-item" href="#" onclick="exportStatement('pdf')">
                <i class="fas fa-file-pdf text-danger me-2"></i> PDF
              </a></li>
              <li><a class="dropdown-item" href="#" onclick="exportStatement('csv')">
                <i class="fas fa-file-csv text-info me-2"></i> CSV
              </a></li>
              <li><hr class="dropdown-divider"></li>
              <li><a class="dropdown-item" href="#" onclick="printStatement()">
                <i class="fas fa-print text-secondary me-2"></i> {{ text_print }}
              </a></li>
            </ul>
          </div>
          <button type="button" class="btn btn-outline-primary" onclick="showAnalysis()"
                  data-bs-toggle="tooltip" title="{{ text_equity_analysis }}">
            <i class="fas fa-analytics"></i>
          </button>
        </div>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li class="breadcrumb-item"><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>

  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible fade show">
      <i class="fas fa-exclamation-triangle"></i>
      {{ error_warning }}
      <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    {% endif %}

    {% if success %}
    <div class="alert alert-success alert-dismissible fade show">
      <i class="fas fa-check-circle"></i>
      {{ success }}
      <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    {% endif %}

    <!-- Filter Panel -->
    <div class="filter-panel">
      <h4>{{ text_statement_filters }}</h4>
      <form id="equity-statement-form" method="post">
        <div class="row">
          <div class="col-md-3">
            <div class="form-group">
              <label for="date_start" class="form-label">{{ entry_date_start }}</label>
              <input type="date" name="date_start" id="date_start" value="{{ date_start }}" class="form-control" required>
            </div>
          </div>
          <div class="col-md-3">
            <div class="form-group">
              <label for="date_end" class="form-label">{{ entry_date_end }}</label>
              <input type="date" name="date_end" id="date_end" value="{{ date_end }}" class="form-control" required>
            </div>
          </div>
          <div class="col-md-2">
            <div class="form-group">
              <label for="currency_id" class="form-label">{{ entry_currency }}</label>
              <select name="currency_id" id="currency_id" class="form-control">
                {% for currency in currencies %}
                <option value="{{ currency.currency_id }}"{% if currency.currency_id == currency_id %} selected{% endif %}>{{ currency.code }}</option>
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="col-md-2">
            <div class="form-group">
              <label for="branch_id" class="form-label">{{ entry_branch }}</label>
              <select name="branch_id" id="branch_id" class="form-control">
                <option value="">{{ text_all_branches }}</option>
                {% for branch in branches %}
                <option value="{{ branch.branch_id }}"{% if branch.branch_id == branch_id %} selected{% endif %}>{{ branch.name }}</option>
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="col-md-2">
            <div class="form-group">
              <label class="form-label">&nbsp;</label>
              <div class="d-grid">
                <button type="submit" class="btn btn-primary">
                  <i class="fas fa-search me-2"></i>{{ button_filter }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
