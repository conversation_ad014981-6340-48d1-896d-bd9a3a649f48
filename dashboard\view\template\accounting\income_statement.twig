{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <a href="{{ back }}" data-toggle="tooltip" title="{{ button_back }}" class="btn btn-default"><i class="fa fa-reply"></i></a>
        <button type="button" id="button-filter" data-toggle="tooltip" title="{{ button_filter }}" class="btn btn-primary"><i class="fa fa-filter"></i></button>
        <a href="{{ export }}" data-toggle="tooltip" title="{{ button_export }}" class="btn btn-success"><i class="fa fa-download"></i></a>
        <button type="button" onclick="window.print();" data-toggle="tooltip" title="{{ button_print }}" class="btn btn-info"><i class="fa fa-print"></i></button>
      </div>
      <h1>{{ heading_income_statement }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-bar-chart"></i> {{ text_income_statement }}</h3>
      </div>
      <div class="panel-body">
        <div class="well" style="display: none;">
          <div class="row">
            <div class="col-sm-6">
              <div class="form-group">
                <label class="control-label" for="input-date-from">{{ entry_date_from }}</label>
                <div class="input-group date">
                  <input type="text" name="filter_date_from" value="{{ filter_date_from }}" placeholder="{{ entry_date_from }}" data-date-format="YYYY-MM-DD" id="input-date-from" class="form-control" />
                  <span class="input-group-btn">
                  <button type="button" class="btn btn-default"><i class="fa fa-calendar"></i></button>
                  </span>
                </div>
              </div>
            </div>
            <div class="col-sm-6">
              <div class="form-group">
                <label class="control-label" for="input-date-to">{{ entry_date_to }}</label>
                <div class="input-group date">
                  <input type="text" name="filter_date_to" value="{{ filter_date_to }}" placeholder="{{ entry_date_to }}" data-date-format="YYYY-MM-DD" id="input-date-to" class="form-control" />
                  <span class="input-group-btn">
                  <button type="button" class="btn btn-default"><i class="fa fa-calendar"></i></button>
                  </span>
                </div>
              </div>
              <button type="button" id="button-apply-filter" class="btn btn-primary pull-right"><i class="fa fa-search"></i> {{ button_apply }}</button>
            </div>
          </div>
        </div>
        <div class="text-center">
          <h3>{{ heading_income_statement }}</h3>
          <p>{{ text_date_range }}: {{ filter_date_from }} - {{ filter_date_to }}</p>
        </div>
        
        <!-- Revenue Section -->
        <h4 class="text-primary">{{ text_revenue }}</h4>
        <div class="table-responsive">
          <table class="table table-bordered">
            <thead>
              <tr>
                <th class="text-left">{{ column_account_code }}</th>
                <th class="text-left">{{ column_account_name }}</th>
                <th class="text-right">{{ column_amount }}</th>
              </tr>
            </thead>
            <tbody>
              {% if revenue %}
              {% for account in revenue %}
              <tr>
                <td class="text-left">{{ account.code }}</td>
                <td class="text-left">{{ account.name }}</td>
                <td class="text-right">{{ account.balance|number_format(2, '.', ',') }}</td>
              </tr>
              {% endfor %}
              {% else %}
              <tr>
                <td class="text-center" colspan="3">{{ text_no_results }}</td>
              </tr>
              {% endif %}
              <tr class="success">
                <td class="text-right" colspan="2"><strong>{{ text_total_revenue }}</strong></td>
                <td class="text-right"><strong>{{ total_revenue|number_format(2, '.', ',') }}</strong></td>
              </tr>
            </tbody>
          </table>
        </div>
        
        <!-- Expense Section -->
        <h4 class="text-danger">{{ text_expense }}</h4>
        <div class="table-responsive">
          <table class="table table-bordered">
            <thead>
              <tr>
                <th class="text-left">{{ column_account_code }}</th>
                <th class="text-left">{{ column_account_name }}</th>
                <th class="text-right">{{ column_amount }}</th>
              </tr>
            </thead>
            <tbody>
              {% if expense %}
              {% for account in expense %}
              <tr>
                <td class="text-left">{{ account.code }}</td>
                <td class="text-left">{{ account.name }}</td>
                <td class="text-right">{{ account.balance|number_format(2, '.', ',') }}</td>
              </tr>
              {% endfor %}
              {% else %}
              <tr>
                <td class="text-center" colspan="3">{{ text_no_results }}</td>
              </tr>
              {% endif %}
              <tr class="danger">
                <td class="text-right" colspan="2"><strong>{{ text_total_expense }}</strong></td>
                <td class="text-right"><strong>{{ total_expense|number_format(2, '.', ',') }}</strong></td>
              </tr>
            </tbody>
          </table>
        </div>
        
        <!-- Net Income -->
        <div class="row">
          <div class="col-md-6 col-md-offset-6">
            <div class="panel panel-default">
              <div class="panel-heading">
                <h3 class="panel-title">{{ text_summary }}</h3>
              </div>
              <div class="panel-body">
                <div class="table-responsive">
                  <table class="table table-bordered">
                    <tr>
                      <td><strong>{{ text_total_revenue }}</strong></td>
                      <td class="text-right">{{ total_revenue|number_format(2, '.', ',') }}</td>
                    </tr>
                    <tr>
                      <td><strong>{{ text_total_expense }}</strong></td>
                      <td class="text-right">{{ total_expense|number_format(2, '.', ',') }}</td>
                    </tr>
                    <tr class="{% if net_income >= 0 %}success{% else %}danger{% endif %}">
                      <td><strong>{{ text_net_income }}</strong></td>
                      <td class="text-right"><strong>{{ net_income|number_format(2, '.', ',') }}</strong></td>
                    </tr>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <script type="text/javascript"><!--
$('#button-filter').on('click', function() {
  $('.well').slideToggle();
});

$('#button-apply-filter').on('click', function() {
  var url = 'index.php?route=accounting/report/incomeStatement&user_token={{ user_token }}';
  
  var filter_date_from = $('input[name=\'filter_date_from\']').val();
  
  if (filter_date_from) {
    url += '&filter_date_from=' + encodeURIComponent(filter_date_from);
  }
  
  var filter_date_to = $('input[name=\'filter_date_to\']').val();
  
  if (filter_date_to) {
    url += '&filter_date_to=' + encodeURIComponent(filter_date_to);
  }
  
  location = url;
});

$('.date').datetimepicker({
  pickTime: false
});
//--></script>
</div>
{{ footer }}
