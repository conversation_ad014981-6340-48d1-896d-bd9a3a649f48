<!DOCTYPE html>
<html dir="{{ direction }}" lang="{{ lang }}">
<head>
    <meta charset="UTF-8" />
    <title>{{ heading_title }} - AYM ERP</title>
    <base href="{{ base }}" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
    <meta name="robots" content="noindex, nofollow" />
    <!-- CSS Files with CDN Fallback -->
    <link href="{{ base }}view/stylesheet/bootstrap.css" rel="stylesheet" media="screen" />
    <link href="{{ base }}view/javascript/font-awesome/css/font-awesome.min.css" rel="stylesheet" type="text/css" />
    <!-- Font Awesome CDN Fallback -->
    <script>
        // Test if Font Awesome is loaded - wait for DOM to be ready
        document.addEventListener('DOMContentLoaded', function() {
            var testFA = document.createElement('i');
            testFA.className = 'fa';
            testFA.style.display = 'none';

            if (document.body) {
                document.body.appendChild(testFA);

                setTimeout(function() {
                    var faLoaded = false;
                    try {
                        var computedStyle = window.getComputedStyle(testFA, ':before');
                        faLoaded = computedStyle && computedStyle.getPropertyValue('font-family').indexOf('FontAwesome') !== -1;
                    } catch(e) {
                        faLoaded = false;
                    }

                    if (!faLoaded) {
                        var link = document.createElement('link');
                        link.rel = 'stylesheet';
                        link.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css';
                        document.head.appendChild(link);
                    }

                    // Clean up test element
                    if (testFA.parentNode) {
                        testFA.parentNode.removeChild(testFA);
                    }
                }, 100);
            }
        });
    </script>
    <link href="{{ base }}view/stylesheet/login.css" rel="stylesheet" type="text/css" />

    <!-- Enhanced Login Styles -->
    <style>
        /* متغيرات CSS للتصميم المتقدم - نظام الألوان الجديد */
        :root {
            /* الألوان الأساسية الجديدة */
            --primary-color: #FF6600;           /* Primary CTA - برتقالي */
            --secondary-color: #A020F0;         /* Secondary CTA - بنفسجي */
            --accent-color: #C71585;            /* Headings/Highlights - وردي */
            --success-color: #00C2A8;           /* نجاح - أخضر نعناعي */
            --warning-color: #FF9900;           /* تحذير - برتقالي */
            --danger-color: #E60026;            /* خطر - أحمر */
            --border-light: #EDEDED;            /* حدود فاتحة */
            --border-dark: #2C2C2C;             /* حدود داكنة */
            --bg-table: #FAFAFA;                /* خلفية جداول */
            --text-dark: #333;                  /* نص داكن */

            /* التدرجات الجديدة */
            --primary-gradient: linear-gradient(135deg, #FF6600 0%, #FF8533 100%);
            --secondary-gradient: linear-gradient(135deg, #A020F0 0%, #C71585 100%);
            --success-gradient: linear-gradient(135deg, #00C2A8 0%, #00E6CC 100%);
            --warning-gradient: linear-gradient(135deg, #FF9900 0%, #FFB333 100%);
            --danger-gradient: linear-gradient(135deg, #E60026 0%, #FF3355 100%);

            /* تأثيرات زجاجية */
            --glass-bg: rgba(255, 255, 255, 0.15);
            --glass-border: rgba(255, 255, 255, 0.25);
            --text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            --box-shadow-main: 0 25px 50px rgba(0,0,0,0.15);
            --box-shadow-hover: 0 35px 70px rgba(0,0,0,0.25);
            --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* تحسينات إضافية للخلفية المتحركة */
        .animated-background::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
            animation: backgroundShift 10s ease-in-out infinite;
        }

        @keyframes backgroundShift {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.8; transform: scale(1.1); }
        }

        /* تحسين بطاقة تسجيل الدخول */
        .login-card {
            background: var(--glass-bg);
            backdrop-filter: blur(25px);
            border: 1px solid var(--glass-border);
            border-radius: 24px;
            box-shadow: var(--box-shadow-main);
            transition: var(--transition-smooth);
            overflow: hidden;
            position: relative;
        }

        .login-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: var(--success-gradient);
            animation: shimmer 2s ease-in-out infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .login-card:hover {
            box-shadow: var(--box-shadow-hover);
            transform: translateY(-5px);
        }

        /* تحسين الهيدر */
        .login-header {
            background: linear-gradient(135deg, rgba(255,255,255,0.2), rgba(255,255,255,0.1));
            padding: 50px 40px 40px;
            text-align: center;
            border-bottom: 1px solid var(--glass-border);
            position: relative;
        }

        .logo-circle {
            width: 90px;
            height: 90px;
            background: var(--primary-gradient);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 25px;
            box-shadow: 0 15px 35px rgba(255, 102, 0, 0.4);
            animation: logoFloat 3s ease-in-out infinite;
            position: relative;
            overflow: hidden;
        }

        .logo-circle::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: logoShine 3s ease-in-out infinite;
        }

        @keyframes logoFloat {
            0%, 100% { transform: translateY(0) rotate(0deg); }
            50% { transform: translateY(-10px) rotate(5deg); }
        }

        @keyframes logoShine {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
            100% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
        }

        .logo-circle i {
            font-size: 45px;
            color: white;
            text-shadow: var(--text-shadow);
            z-index: 1;
            position: relative;
        }

        .system-title {
            color: white;
            font-size: 32px;
            font-weight: 800;
            margin-bottom: 10px;
            text-shadow: var(--text-shadow);
            letter-spacing: 2px;
            background: linear-gradient(45deg, var(--accent-color), #fff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .system-subtitle {
            color: rgba(255, 255, 255, 0.9);
            font-size: 16px;
            font-weight: 400;
            text-shadow: 0 1px 2px rgba(0,0,0,0.2);
        }

        /* تصميم النموذج المتقدم */
        .login-form {
            padding: 40px;
            position: relative;
        }

        .form-group {
            margin-bottom: 30px;
            position: relative;
        }

        .input-container {
            position: relative;
            display: flex;
            align-items: center;
        }

        .input-icon {
            position: absolute;
            left: 20px;
            color: rgba(255, 255, 255, 0.7);
            font-size: 18px;
            z-index: 2;
            transition: var(--transition-smooth);
        }

        .form-control {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 18px 20px 18px 55px;
            color: white;
            font-size: 16px;
            width: 100%;
            transition: var(--transition-smooth);
            backdrop-filter: blur(10px);
        }

        .form-control:focus {
            background: rgba(255, 255, 255, 0.15);
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(255, 102, 0, 0.2);
            outline: none;
            transform: translateY(-2px);
        }

        .form-control:focus + .floating-label,
        .form-control:not(:placeholder-shown) + .floating-label {
            transform: translateY(-35px) scale(0.85);
            color: var(--primary-color);
        }

        .form-control::placeholder {
            color: rgba(255, 255, 255, 0.5);
            opacity: 0;
            transition: var(--transition-smooth);
        }

        .form-control:focus::placeholder {
            opacity: 1;
        }

        .floating-label {
            position: absolute;
            left: 55px;
            top: 50%;
            transform: translateY(-50%);
            color: rgba(255, 255, 255, 0.7);
            font-size: 16px;
            pointer-events: none;
            transition: var(--transition-smooth);
            background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
            padding: 0 8px;
            border-radius: 4px;
            backdrop-filter: blur(5px);
        }

        /* تصميم الأزرار المتقدم */
        .btn-login {
            background: var(--primary-gradient);
            border: none;
            border-radius: 15px;
            padding: 18px 30px;
            color: white;
            font-size: 18px;
            font-weight: 600;
            width: 100%;
            cursor: pointer;
            transition: var(--transition-smooth);
            position: relative;
            overflow: hidden;
            text-transform: uppercase;
            letter-spacing: 1px;
            box-shadow: 0 10px 25px rgba(255, 102, 0, 0.3);
        }

        .btn-login::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: var(--transition-smooth);
        }

        .btn-login:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(255, 102, 0, 0.4);
        }

        .btn-login:hover::before {
            left: 100%;
        }

        .btn-login:active {
            transform: translateY(-1px);
        }

        .btn-login.loading {
            pointer-events: none;
            opacity: 0.8;
        }

        .btn-login.loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            margin: -10px 0 0 -10px;
            border: 2px solid rgba(255,255,255,0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* تصميم الرسائل التنبيهية */
        .alert {
            margin: 20px 40px 0;
            padding: 15px 20px;
            border-radius: 12px;
            border: none;
            display: flex;
            align-items: center;
            gap: 12px;
            font-weight: 500;
            backdrop-filter: blur(10px);
            animation: slideInDown 0.5s ease-out;
        }

        @keyframes slideInDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .alert-danger {
            background: var(--danger-gradient);
            color: white;
            box-shadow: 0 8px 25px rgba(230, 0, 38, 0.3);
        }

        .alert-success {
            background: var(--success-gradient);
            color: white;
            box-shadow: 0 8px 25px rgba(0, 194, 168, 0.3);
        }

        .alert-warning {
            background: var(--warning-gradient);
            color: white;
            box-shadow: 0 8px 25px rgba(255, 153, 0, 0.3);
        }

        /* خيارات إضافية */
        .form-options {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding: 0 5px;
        }

        .remember-me {
            display: flex;
            align-items: center;
            gap: 8px;
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
        }

        .remember-me input[type="checkbox"] {
            width: 18px;
            height: 18px;
            accent-color: var(--primary-color);
        }

        .forgot-link {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            font-size: 14px;
            transition: var(--transition-smooth);
        }

        .forgot-link:hover {
            color: var(--accent-color);
            text-decoration: underline;
        }

        /* فوتر البطاقة */
        .login-footer {
            padding: 25px 40px;
            text-align: center;
            border-top: 1px solid var(--glass-border);
            background: linear-gradient(135deg, rgba(255,255,255,0.05), rgba(255,255,255,0.02));
        }

        .security-info {
            color: rgba(255, 255, 255, 0.7);
            font-size: 14px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .version-info {
            color: rgba(255, 255, 255, 0.5);
            font-size: 12px;
        }

        /* تحسينات الاستجابة */
        @media (max-width: 768px) {
            .login-container {
                padding: 15px;
            }

            .login-card {
                margin: 0;
                border-radius: 16px;
            }

            .login-header {
                padding: 40px 30px 30px;
            }

            .login-form {
                padding: 30px;
            }

            .system-title {
                font-size: 28px;
            }

            .logo-circle {
                width: 75px;
                height: 75px;
            }

            .logo-circle i {
                font-size: 35px;
            }
        }
    </style>

    <!-- JavaScript Files with CDN Fallback -->
    <script src="{{ base }}view/javascript/jquery/jquery-3.7.0.min.js" type="text/javascript"></script>
    <script>
        // jQuery CDN Fallback with better error handling
        if (typeof jQuery === 'undefined') {
            console.warn('Local jQuery not loaded, loading from CDN...');
            document.write('<script src="https://code.jquery.com/jquery-3.7.0.min.js"><\/script>');
        } else {
            console.log('jQuery loaded successfully from local file');
        }
    </script>
    <script src="{{ base }}view/javascript/bootstrap/js/bootstrap.min.js" type="text/javascript"></script>
</head>
<body class="login-page">
    <!-- Background Animation -->
    <div class="animated-background">
        <div class="floating-shapes">
            <div class="shape shape-1"></div>
            <div class="shape shape-2"></div>
            <div class="shape shape-3"></div>
            <div class="shape shape-4"></div>
            <div class="shape shape-5"></div>
        </div>
    </div>

    <!-- Main Login Container -->
    <div class="login-container">
        <div class="login-card">
            <!-- Header Section -->
            <div class="login-header">
                <div class="logo-container">
                    <div class="logo-circle">
                        <i class="fa fa-cube"></i>
                    </div>
                    <h1 class="system-title">AYM ERP</h1>
                    <p class="system-subtitle">{{ text_login_subtitle }}</p>
                </div>
            </div>

            <!-- Alert Messages -->
            {% if error_warning %}
                <div class="alert alert-danger animated fadeInDown">
                    <i class="fa fa-exclamation-triangle"></i>
                    <span>{{ error_warning }}</span>
                </div>
            {% endif %}

            {% if success %}
                <div class="alert alert-success animated fadeInDown">
                    <i class="fa fa-check-circle"></i>
                    <span>{{ success }}</span>
                </div>
            {% endif %}

            <!-- Login Form -->
            <form action="{{ action }}" method="post" id="login-form" class="login-form">
                <input type="hidden" name="redirect" value="{{ redirect }}" />

                <!-- Username Field -->
                <div class="form-group">
                    <div class="input-container">
                        <i class="fa fa-user input-icon"></i>
                        <input type="text" name="username" value="{{ username }}" placeholder="{{ entry_username }}" 
                               class="form-control" id="input-username" autocomplete="username" required />
                        <label for="input-username" class="floating-label">{{ entry_username }}</label>
                    </div>
                </div>

                <!-- Password Field -->
                <div class="form-group">
                    <div class="input-container">
                        <i class="fa fa-lock input-icon"></i>
                        <input type="password" name="password" value="{{ password }}" placeholder="{{ entry_password }}"
                               class="form-control" id="input-password" autocomplete="current-password" required />
                        <label for="input-password" class="floating-label">{{ entry_password }}</label>
                        <button type="button" class="password-toggle" id="password-toggle" style="position: absolute; right: 15px; background: none; border: none; color: rgba(255,255,255,0.7); font-size: 16px; cursor: pointer; z-index: 3; transition: all 0.3s ease;">
                            <i class="fa fa-eye"></i>
                        </button>
                    </div>
                </div>

                <!-- Remember Me & Forgot Password -->
                <div class="form-options">
                    <div class="remember-me">
                        <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                            <input type="checkbox" name="remember" value="1" style="width: 18px; height: 18px; accent-color: #4facfe;">
                            <span>{{ text_remember_me }}</span>
                        </label>
                    </div>
                    {% if forgotten %}
                        <div class="forgot-password">
                            <a href="{{ forgotten }}" class="forgot-link">{{ text_forgotten }}</a>
                        </div>
                    {% endif %}
                </div>

                <!-- Login Button -->
                <div class="form-group">
                    <button type="submit" class="btn-login" id="login-btn">
                        <span class="btn-text">
                            <i class="fa fa-sign-in"></i>
                            {{ button_login }}
                        </span>
                        <span class="btn-loading" style="display: none;">
                            <i class="fa fa-spinner fa-spin"></i>
                            {{ text_logging_in }}
                        </span>
                    </button>
                </div>

            </form>

            <!-- Footer -->
            <div class="login-footer">
                <div class="security-info">
                    <i class="fa fa-lock"></i>
                    <span>{{ text_secure }}</span>
                </div>
                <div class="version-info">
                    {{ text_powered_by }} AYM Team | {{ text_version }} 2.0.0
                </div>
            </div>
        </div>
    </div>

    <!-- تم إزالة Loading Overlay لتجنب التضارب -->

    <!-- Enhanced JavaScript -->
    <script type="text/javascript">
    // تحقق من تحميل jQuery وبدء التطبيق
    $(document).ready(function() {
        // تحسين تجربة المستخدم
        const loginForm = $('#login-form');
        const usernameInput = $('#input-username');
        const passwordInput = $('#input-password');
        const loginBtn = $('#login-btn');
        const passwordToggle = $('#password-toggle');
        const loadingOverlay = $('#loading-overlay');

        // تركيز تلقائي على حقل اسم المستخدم
        usernameInput.focus();

        // تأثيرات الحقول المتحركة
        $('.form-control').on('focus', function() {
            $(this).closest('.input-container').addClass('focused');
            $(this).siblings('.input-icon').css('color', '#FF6600');
        });

        $('.form-control').on('blur', function() {
            if (!$(this).val()) {
                $(this).closest('.input-container').removeClass('focused');
                $(this).siblings('.input-icon').css('color', 'rgba(255, 255, 255, 0.7)');
            }
        });

        // فحص الحقول عند التحميل
        $('.form-control').each(function() {
            if ($(this).val()) {
                $(this).closest('.input-container').addClass('focused');
            }
        });

        // تبديل إظهار كلمة المرور
        passwordToggle.on('click', function() {
            const icon = $(this).find('i');
            if (passwordInput.attr('type') === 'password') {
                passwordInput.attr('type', 'text');
                icon.removeClass('fa-eye').addClass('fa-eye-slash');
                $(this).css('color', '#FF6600');
            } else {
                passwordInput.attr('type', 'password');
                icon.removeClass('fa-eye-slash').addClass('fa-eye');
                $(this).css('color', 'rgba(255,255,255,0.7)');
            }
        });

        // تحسين إرسال النموذج
        loginForm.on('submit', function(e) {
            e.preventDefault();

            const username = usernameInput.val().trim();
            const password = passwordInput.val().trim();

            // التحقق من صحة البيانات
            if (!username) {
                usernameInput.focus().addClass('error');
                showError('يرجى إدخال اسم المستخدم');
                return false;
            }

            if (!password) {
                passwordInput.focus().addClass('error');
                showError('يرجى إدخال كلمة المرور');
                return false;
            }

            // إظهار حالة التحميل
            showLoading();

            // إرسال النموذج
            setTimeout(() => {
                this.submit();
            }, 500);
        });

        // إزالة رسائل الخطأ عند الكتابة
        $('.form-control').on('input', function() {
            $(this).removeClass('error');
            $('.alert-danger').fadeOut();
        });

        // دالة إظهار التحميل
        function showLoading() {
            loginBtn.addClass('loading').prop('disabled', true);
            loginBtn.find('.btn-text').hide();
            loginBtn.find('.btn-loading').show();
            loadingOverlay.fadeIn();
        }

        // دالة إظهار الأخطاء
        function showError(message) {
            const alertHtml = `
                <div class="alert alert-danger" style="margin: 20px 40px 0; animation: slideInDown 0.5s ease-out;">
                    <i class="fa fa-exclamation-triangle"></i>
                    <span>${message}</span>
                </div>
            `;
            $('.login-header').after(alertHtml);

            setTimeout(() => {
                $('.alert-danger').fadeOut();
            }, 5000);
        }

        // تحسين الأداء - تأخير تحميل الرسوم المتحركة
        setTimeout(() => {
            $('.login-card').css('opacity', '1');
        }, 100);

        // اختصارات لوحة المفاتيح
        $(document).on('keydown', function(e) {
            // Enter للإرسال
            if (e.key === 'Enter' && (usernameInput.is(':focus') || passwordInput.is(':focus'))) {
                loginForm.submit();
            }

            // Escape لإلغاء التحميل
            if (e.key === 'Escape') {
                loadingOverlay.fadeOut();
                loginBtn.removeClass('loading').prop('disabled', false);
                loginBtn.find('.btn-text').show();
                loginBtn.find('.btn-loading').hide();
            }
        });

        // تحسين الاستجابة للأجهزة المحمولة
        if (window.innerWidth <= 768) {
            $('.login-card').css('margin', '10px');
            $('.login-header').css('padding', '30px 25px 25px');
            $('.login-form').css('padding', '25px');
        }
    });

    // Fallback JavaScript إذا لم يتم تحميل jQuery
    if (typeof jQuery === 'undefined') {
        console.warn('jQuery not loaded, using vanilla JavaScript fallback');

        // تركيز على حقل اسم المستخدم
        document.addEventListener('DOMContentLoaded', function() {
            var usernameInput = document.getElementById('input-username');
            if (usernameInput) {
                usernameInput.focus();
            }

            // معالجة إرسال النموذج
            var loginForm = document.getElementById('login-form');
            if (loginForm) {
                loginForm.addEventListener('submit', function(e) {
                    var username = document.getElementById('input-username').value.trim();
                    var password = document.getElementById('input-password').value.trim();

                    if (!username || !password) {
                        e.preventDefault();
                        alert('يرجى إدخال اسم المستخدم وكلمة المرور');
                        return false;
                    }
                });
            }

            // معالجة زر إظهار كلمة المرور
            var passwordToggle = document.getElementById('password-toggle');
            var passwordInput = document.getElementById('input-password');

            if (passwordToggle && passwordInput) {
                passwordToggle.addEventListener('click', function() {
                    var icon = this.querySelector('i');
                    if (passwordInput.type === 'password') {
                        passwordInput.type = 'text';
                        icon.className = 'fa fa-eye-slash';
                    } else {
                        passwordInput.type = 'password';
                        icon.className = 'fa fa-eye';
                    }
                });
            }
        });
    }
    </script>

</body>
</html>
