<?php
class ModelAccountsAccountQuery extends Model {

    // جلب معلومات الحساب
    public function getAccountInfo($account_id) {
        $query = $this->db->query("SELECT
            a.account_id,
            a.account_code,
            ad.name as account_name,
            a.account_type,
            a.parent_id,
            a.status,
            ad.description,
            pad.name as parent_name,
            pa.account_code as parent_code
            FROM `" . DB_PREFIX . "accounts` a
            LEFT JOIN `" . DB_PREFIX . "account_description` ad ON (a.account_id = ad.account_id AND ad.language_id = '" . (int)$this->config->get('config_language_id') . "')
            LEFT JOIN `" . DB_PREFIX . "accounts` pa ON (a.parent_id = pa.account_id)
            LEFT JOIN `" . DB_PREFIX . "account_description` pad ON (pa.account_id = pad.account_id AND pad.language_id = '" . (int)$this->config->get('config_language_id') . "')
            WHERE a.account_id = '" . (int)$account_id . "'");

        return $query->row;
    }

    // حساب رصيد الحساب مع التفاصيل
    public function calculateAccountBalance($account_id, $date_from = '', $date_to = '') {
        $where_date = "";

        if ($date_from && $date_to) {
            $where_date = " AND je.date BETWEEN '" . $this->db->escape($date_from) . "' AND '" . $this->db->escape($date_to) . "'";
        } elseif ($date_from) {
            $where_date = " AND je.date >= '" . $this->db->escape($date_from) . "'";
        } elseif ($date_to) {
            $where_date = " AND je.date <= '" . $this->db->escape($date_to) . "'";
        }

        // الحصول على معلومات الحساب لتحديد طبيعته
        $account_info = $this->getAccountInfo($account_id);
        $account_code = $account_info['account_code'];

        // حساب الرصيد الافتتاحي (قبل تاريخ البداية)
        $opening_balance = 0;
        if ($date_from) {
            $opening_query = $this->db->query("SELECT
                SUM(CASE WHEN je.is_debit = 1 THEN je.amount ELSE 0 END) as total_debit,
                SUM(CASE WHEN je.is_debit = 0 THEN je.amount ELSE 0 END) as total_credit
                FROM `" . DB_PREFIX . "journal_entries` je
                LEFT JOIN `" . DB_PREFIX . "journals` j ON (je.journal_id = j.journal_id)
                WHERE je.account_code = '" . $this->db->escape($account_code) . "'
                AND j.thedate < '" . $this->db->escape($date_from) . "'
                AND j.status = 'posted'");

            if ($opening_query->row) {
                $opening_debit = (float)$opening_query->row['total_debit'];
                $opening_credit = (float)$opening_query->row['total_credit'];

                // تحديد الرصيد بناءً على نوع الحساب
                if (in_array($account_info['account_type'], ['asset', 'expense'])) {
                    $opening_balance = $opening_debit - $opening_credit;
                } else {
                    $opening_balance = $opening_credit - $opening_debit;
                }
            }
        }

        // حساب الحركة في الفترة المحددة
        $query = $this->db->query("SELECT
            SUM(CASE WHEN je.is_debit = 1 THEN je.amount ELSE 0 END) as period_debit,
            SUM(CASE WHEN je.is_debit = 0 THEN je.amount ELSE 0 END) as period_credit,
            COUNT(je.entry_id) as transaction_count
            FROM `" . DB_PREFIX . "journal_entries` je
            LEFT JOIN `" . DB_PREFIX . "journals` j ON (je.journal_id = j.journal_id)
            WHERE je.account_code = '" . $this->db->escape($account_code) . "'
            AND j.status = 'posted'" . $where_date);

        $period_debit = (float)$query->row['period_debit'];
        $period_credit = (float)$query->row['period_credit'];
        $transaction_count = (int)$query->row['transaction_count'];

        // حساب الرصيد الختامي بناءً على نوع الحساب
        if (in_array($account_info['account_type'], ['asset', 'expense'])) {
            $closing_balance = $opening_balance + $period_debit - $period_credit;
            $net_movement = $period_debit - $period_credit;
        } else {
            $closing_balance = $opening_balance + $period_credit - $period_debit;
            $net_movement = $period_credit - $period_debit;
        }

        return array(
            'opening_balance' => $opening_balance,
            'period_debit' => $period_debit,
            'period_credit' => $period_credit,
            'net_movement' => $net_movement,
            'closing_balance' => $closing_balance,
            'transaction_count' => $transaction_count,
            'date_from' => $date_from,
            'date_to' => $date_to
        );
    }

    // جلب آخر المعاملات
    public function getRecentTransactions($account_id, $limit = 10) {
        // الحصول على معلومات الحساب
        $account_info = $this->getAccountInfo($account_id);
        $account_code = $account_info['account_code'];

        $query = $this->db->query("SELECT
            j.thedate as date,
            j.refnum as reference,
            j.description,
            CASE WHEN je.is_debit = 1 THEN je.amount ELSE 0 END as debit,
            CASE WHEN je.is_debit = 0 THEN je.amount ELSE 0 END as credit,
            j.status,
            j.journal_id
            FROM `" . DB_PREFIX . "journal_entries` je
            LEFT JOIN `" . DB_PREFIX . "journals` j ON (je.journal_id = j.journal_id)
            WHERE je.account_code = '" . $this->db->escape($account_code) . "'
            AND j.status = 'posted'
            ORDER BY j.thedate DESC, j.journal_id DESC
            LIMIT " . (int)$limit);

        return $query->rows;
    }

    // جلب إحصائيات الحساب
    public function getAccountStatistics($account_id, $date_from = '', $date_to = '') {
        // الحصول على معلومات الحساب
        $account_info = $this->getAccountInfo($account_id);
        $account_code = $account_info['account_code'];

        $where_date = "";
        if ($date_from && $date_to) {
            $where_date = " AND j.thedate BETWEEN '" . $this->db->escape($date_from) . "' AND '" . $this->db->escape($date_to) . "'";
        }

        // إحصائيات عامة
        $stats_query = $this->db->query("SELECT
            COUNT(DISTINCT j.thedate) as active_days,
            AVG(CASE WHEN je.is_debit = 1 THEN je.amount ELSE 0 END) as avg_debit,
            AVG(CASE WHEN je.is_debit = 0 THEN je.amount ELSE 0 END) as avg_credit,
            MAX(CASE WHEN je.is_debit = 1 THEN je.amount ELSE 0 END) as max_debit,
            MAX(CASE WHEN je.is_debit = 0 THEN je.amount ELSE 0 END) as max_credit,
            MIN(j.thedate) as first_transaction,
            MAX(j.thedate) as last_transaction
            FROM `" . DB_PREFIX . "journal_entries` je
            LEFT JOIN `" . DB_PREFIX . "journals` j ON (je.journal_id = j.journal_id)
            WHERE je.account_code = '" . $this->db->escape($account_code) . "'
            AND j.status = 'posted'" . $where_date);

        // إحصائيات شهرية
        $monthly_query = $this->db->query("SELECT
            YEAR(je.date) as year,
            MONTH(je.date) as month,
            SUM(jed.debit) as monthly_debit,
            SUM(jed.credit) as monthly_credit,
            COUNT(*) as monthly_transactions
            FROM `cod_journal_entry_detail` jed
            LEFT JOIN `cod_journal_entry` je ON (jed.journal_entry_id = je.journal_entry_id)
            WHERE jed.account_id = '" . (int)$account_id . "'
            AND je.status = 'approved'" . $where_date . "
            GROUP BY YEAR(je.date), MONTH(je.date)
            ORDER BY year DESC, month DESC
            LIMIT 12");

        return array(
            'general' => $stats_query->row,
            'monthly' => $monthly_query->rows
        );
    }

    // جلب تاريخ الأرصدة
    public function getBalanceHistory($account_id, $period = 'month') {
        $date_format = '';
        $group_by = '';

        switch ($period) {
            case 'day':
                $date_format = '%Y-%m-%d';
                $group_by = 'DATE(je.date)';
                break;
            case 'week':
                $date_format = '%Y-%u';
                $group_by = 'YEARWEEK(je.date)';
                break;
            case 'month':
                $date_format = '%Y-%m';
                $group_by = 'YEAR(je.date), MONTH(je.date)';
                break;
            case 'year':
                $date_format = '%Y';
                $group_by = 'YEAR(je.date)';
                break;
            default:
                $date_format = '%Y-%m';
                $group_by = 'YEAR(je.date), MONTH(je.date)';
        }

        $query = $this->db->query("SELECT
            DATE_FORMAT(je.date, '" . $date_format . "') as period,
            SUM(jed.debit) as period_debit,
            SUM(jed.credit) as period_credit,
            (SUM(jed.debit) - SUM(jed.credit)) as net_movement,
            COUNT(*) as transaction_count
            FROM `cod_journal_entry_detail` jed
            LEFT JOIN `cod_journal_entry` je ON (jed.journal_entry_id = je.journal_entry_id)
            WHERE jed.account_id = '" . (int)$account_id . "'
            AND je.status = 'approved'
            AND je.date >= DATE_SUB(NOW(), INTERVAL 12 " . strtoupper($period) . ")
            GROUP BY " . $group_by . "
            ORDER BY je.date ASC");

        $history = array();
        $running_balance = 0;

        foreach ($query->rows as $row) {
            $running_balance += (float)$row['net_movement'];
            $history[] = array(
                'period' => $row['period'],
                'debit' => (float)$row['period_debit'],
                'credit' => (float)$row['period_credit'],
                'net_movement' => (float)$row['net_movement'],
                'running_balance' => $running_balance,
                'transaction_count' => (int)$row['transaction_count']
            );
        }

        return $history;
    }

    // جلب المعاملات مع الترقيم
    public function getTransactions($filter_data) {
        $sql = "SELECT
            je.date,
            je.reference,
            jed.description,
            jed.debit,
            jed.credit,
            je.source_type,
            je.source_id,
            @running_balance := @running_balance + (jed.debit - jed.credit) as running_balance
            FROM `cod_journal_entry_detail` jed
            LEFT JOIN `cod_journal_entry` je ON (jed.journal_entry_id = je.journal_entry_id)
            CROSS JOIN (SELECT @running_balance := 0) r
            WHERE jed.account_id = '" . (int)$filter_data['account_id'] . "'
            AND je.status = 'approved'";

        if (!empty($filter_data['date_from'])) {
            $sql .= " AND je.date >= '" . $this->db->escape($filter_data['date_from']) . "'";
        }

        if (!empty($filter_data['date_to'])) {
            $sql .= " AND je.date <= '" . $this->db->escape($filter_data['date_to']) . "'";
        }

        $sql .= " ORDER BY je.date ASC, je.journal_entry_id ASC";

        if (isset($filter_data['start']) && isset($filter_data['limit'])) {
            $sql .= " LIMIT " . (int)$filter_data['start'] . "," . (int)$filter_data['limit'];
        }

        $query = $this->db->query($sql);
        return $query->rows;
    }

    // عدد المعاملات الإجمالي
    public function getTotalTransactions($filter_data) {
        $sql = "SELECT COUNT(*) as total
            FROM `cod_journal_entry_detail` jed
            LEFT JOIN `cod_journal_entry` je ON (jed.journal_entry_id = je.journal_entry_id)
            WHERE jed.account_id = '" . (int)$filter_data['account_id'] . "'
            AND je.status = 'approved'";

        if (!empty($filter_data['date_from'])) {
            $sql .= " AND je.date >= '" . $this->db->escape($filter_data['date_from']) . "'";
        }

        if (!empty($filter_data['date_to'])) {
            $sql .= " AND je.date <= '" . $this->db->escape($filter_data['date_to']) . "'";
        }

        $query = $this->db->query($sql);
        return $query->row['total'];
    }

    // جلب جميع المعاملات للتصدير
    public function getAllTransactions($account_id, $date_from = '', $date_to = '') {
        $sql = "SELECT
            je.date,
            je.reference,
            jed.description,
            jed.debit,
            jed.credit,
            je.source_type,
            @running_balance := @running_balance + (jed.debit - jed.credit) as running_balance
            FROM `cod_journal_entry_detail` jed
            LEFT JOIN `cod_journal_entry` je ON (jed.journal_entry_id = je.journal_entry_id)
            CROSS JOIN (SELECT @running_balance := 0) r
            WHERE jed.account_id = '" . (int)$account_id . "'
            AND je.status = 'approved'";

        if ($date_from) {
            $sql .= " AND je.date >= '" . $this->db->escape($date_from) . "'";
        }

        if ($date_to) {
            $sql .= " AND je.date <= '" . $this->db->escape($date_to) . "'";
        }

        $sql .= " ORDER BY je.date ASC, je.journal_entry_id ASC";

        $query = $this->db->query($sql);
        return $query->rows;
    }

    // إنشاء ملف CSV
    public function generateCSV($account_info, $transactions) {
        $csv_content = "كشف حساب - " . $account_info['account_code'] . " - " . $account_info['account_name'] . "\n";
        $csv_content .= "تاريخ الإنشاء: " . date('Y-m-d H:i:s') . "\n";
        $csv_content .= "عدد المعاملات: " . count($transactions) . "\n\n";
        $csv_content .= "التاريخ,المرجع,البيان,مدين,دائن,الرصيد,المصدر\n";

        foreach ($transactions as $transaction) {
            $csv_content .= '"' . $transaction['date'] . '",';
            $csv_content .= '"' . $transaction['reference'] . '",';
            $csv_content .= '"' . $transaction['description'] . '",';
            $csv_content .= number_format($transaction['debit'], 2) . ',';
            $csv_content .= number_format($transaction['credit'], 2) . ',';
            $csv_content .= number_format($transaction['running_balance'], 2) . ',';
            $csv_content .= '"' . $transaction['source_type'] . '"' . "\n";
        }

        return $csv_content;
    }

    // البحث المتقدم في المعاملات
    public function advancedSearch($filter_data) {
        $sql = "SELECT
            je.date,
            je.reference,
            jed.description,
            jed.debit,
            jed.credit,
            je.source_type,
            je.source_id,
            a.account_code,
            a.account_name
            FROM `cod_journal_entry_detail` jed
            LEFT JOIN `cod_journal_entry` je ON (jed.journal_entry_id = je.journal_entry_id)
            LEFT JOIN `cod_account` a ON (jed.account_id = a.account_id)
            WHERE je.status = 'approved'";

        if (!empty($filter_data['account_id'])) {
            $sql .= " AND jed.account_id = '" . (int)$filter_data['account_id'] . "'";
        }

        if (!empty($filter_data['date_from'])) {
            $sql .= " AND je.date >= '" . $this->db->escape($filter_data['date_from']) . "'";
        }

        if (!empty($filter_data['date_to'])) {
            $sql .= " AND je.date <= '" . $this->db->escape($filter_data['date_to']) . "'";
        }

        if (!empty($filter_data['reference'])) {
            $sql .= " AND je.reference LIKE '%" . $this->db->escape($filter_data['reference']) . "%'";
        }

        if (!empty($filter_data['description'])) {
            $sql .= " AND jed.description LIKE '%" . $this->db->escape($filter_data['description']) . "%'";
        }

        if (!empty($filter_data['min_amount'])) {
            $sql .= " AND (jed.debit >= '" . (float)$filter_data['min_amount'] . "' OR jed.credit >= '" . (float)$filter_data['min_amount'] . "')";
        }

        if (!empty($filter_data['max_amount'])) {
            $sql .= " AND (jed.debit <= '" . (float)$filter_data['max_amount'] . "' OR jed.credit <= '" . (float)$filter_data['max_amount'] . "')";
        }

        if (!empty($filter_data['source_type'])) {
            $sql .= " AND je.source_type = '" . $this->db->escape($filter_data['source_type']) . "'";
        }

        $sql .= " ORDER BY je.date DESC, je.journal_entry_id DESC";

        if (isset($filter_data['start']) && isset($filter_data['limit'])) {
            $sql .= " LIMIT " . (int)$filter_data['start'] . "," . (int)$filter_data['limit'];
        }

        $query = $this->db->query($sql);
        return $query->rows;
    }

    // مقارنة الحسابات
    public function compareAccounts($account_ids, $date_from = '', $date_to = '') {
        $results = array();

        foreach ($account_ids as $account_id) {
            $account_info = $this->getAccountInfo($account_id);
            $balance_data = $this->calculateAccountBalance($account_id, $date_from, $date_to);

            $results[] = array(
                'account_info' => $account_info,
                'balance_data' => $balance_data
            );
        }

        return $results;
    }

    // تحليل اتجاه الحساب
    public function analyzeTrend($account_id, $periods = 12) {
        $query = $this->db->query("SELECT
            YEAR(je.date) as year,
            MONTH(je.date) as month,
            SUM(jed.debit) as monthly_debit,
            SUM(jed.credit) as monthly_credit,
            (SUM(jed.debit) - SUM(jed.credit)) as net_movement
            FROM `cod_journal_entry_detail` jed
            LEFT JOIN `cod_journal_entry` je ON (jed.journal_entry_id = je.journal_entry_id)
            WHERE jed.account_id = '" . (int)$account_id . "'
            AND je.status = 'approved'
            AND je.date >= DATE_SUB(NOW(), INTERVAL " . (int)$periods . " MONTH)
            GROUP BY YEAR(je.date), MONTH(je.date)
            ORDER BY year ASC, month ASC");

        $trend_data = $query->rows;

        // حساب معدل النمو
        $growth_rates = array();
        for ($i = 1; $i < count($trend_data); $i++) {
            $current = $trend_data[$i]['net_movement'];
            $previous = $trend_data[$i-1]['net_movement'];

            if ($previous != 0) {
                $growth_rate = (($current - $previous) / abs($previous)) * 100;
                $growth_rates[] = $growth_rate;
            }
        }

        $avg_growth_rate = count($growth_rates) > 0 ? array_sum($growth_rates) / count($growth_rates) : 0;

        return array(
            'trend_data' => $trend_data,
            'average_growth_rate' => $avg_growth_rate,
            'total_periods' => count($trend_data)
        );
    }

    // تحليل نشاط الحساب
    public function analyzeActivity($account_id, $date_from = '', $date_to = '') {
        $where_date = "";

        if ($date_from && $date_to) {
            $where_date = " AND je.date BETWEEN '" . $this->db->escape($date_from) . "' AND '" . $this->db->escape($date_to) . "'";
        }

        // تحليل التوزيع الزمني
        $time_distribution = $this->db->query("SELECT
            DAYOFWEEK(je.date) as day_of_week,
            HOUR(je.created_at) as hour_of_day,
            COUNT(*) as transaction_count,
            SUM(jed.debit + jed.credit) as total_amount
            FROM `cod_journal_entry_detail` jed
            LEFT JOIN `cod_journal_entry` je ON (jed.journal_entry_id = je.journal_entry_id)
            WHERE jed.account_id = '" . (int)$account_id . "'
            AND je.status = 'approved'" . $where_date . "
            GROUP BY DAYOFWEEK(je.date), HOUR(je.created_at)
            ORDER BY day_of_week, hour_of_day");

        // تحليل أنواع المصادر
        $source_analysis = $this->db->query("SELECT
            je.source_type,
            COUNT(*) as transaction_count,
            SUM(jed.debit) as total_debit,
            SUM(jed.credit) as total_credit,
            AVG(jed.debit + jed.credit) as avg_amount
            FROM `cod_journal_entry_detail` jed
            LEFT JOIN `cod_journal_entry` je ON (jed.journal_entry_id = je.journal_entry_id)
            WHERE jed.account_id = '" . (int)$account_id . "'
            AND je.status = 'approved'" . $where_date . "
            GROUP BY je.source_type
            ORDER BY transaction_count DESC");

        // تحليل التكرار
        $frequency_analysis = $this->db->query("SELECT
            COUNT(DISTINCT DATE(je.date)) as active_days,
            COUNT(*) as total_transactions,
            (COUNT(*) / COUNT(DISTINCT DATE(je.date))) as avg_transactions_per_day,
            DATEDIFF(MAX(je.date), MIN(je.date)) + 1 as total_days,
            (COUNT(DISTINCT DATE(je.date)) / (DATEDIFF(MAX(je.date), MIN(je.date)) + 1)) * 100 as activity_percentage
            FROM `cod_journal_entry_detail` jed
            LEFT JOIN `cod_journal_entry` je ON (jed.journal_entry_id = je.journal_entry_id)
            WHERE jed.account_id = '" . (int)$account_id . "'
            AND je.status = 'approved'" . $where_date);

        return array(
            'time_distribution' => $time_distribution->rows,
            'source_analysis' => $source_analysis->rows,
            'frequency_analysis' => $frequency_analysis->row
        );
    }

    // تحليل الأرصدة القصوى والدنيا
    public function analyzeExtremes($account_id, $date_from = '', $date_to = '') {
        $where_date = "";

        if ($date_from && $date_to) {
            $where_date = " AND je.date BETWEEN '" . $this->db->escape($date_from) . "' AND '" . $this->db->escape($date_to) . "'";
        }

        // أعلى وأقل المعاملات
        $extreme_transactions = $this->db->query("SELECT
            'highest_debit' as type,
            je.date,
            je.reference,
            jed.description,
            jed.debit as amount,
            0 as credit
            FROM `cod_journal_entry_detail` jed
            LEFT JOIN `cod_journal_entry` je ON (jed.journal_entry_id = je.journal_entry_id)
            WHERE jed.account_id = '" . (int)$account_id . "'
            AND je.status = 'approved'" . $where_date . "
            AND jed.debit > 0
            ORDER BY jed.debit DESC
            LIMIT 1

            UNION ALL

            SELECT
            'highest_credit' as type,
            je.date,
            je.reference,
            jed.description,
            0 as amount,
            jed.credit as credit
            FROM `cod_journal_entry_detail` jed
            LEFT JOIN `cod_journal_entry` je ON (jed.journal_entry_id = je.journal_entry_id)
            WHERE jed.account_id = '" . (int)$account_id . "'
            AND je.status = 'approved'" . $where_date . "
            AND jed.credit > 0
            ORDER BY jed.credit DESC
            LIMIT 1");

        return $extreme_transactions->rows;
    }

    // إنشاء تقرير شامل
    public function generateComprehensiveReport($account_id, $date_from = '', $date_to = '') {
        $account_info = $this->getAccountInfo($account_id);
        $balance_data = $this->calculateAccountBalance($account_id, $date_from, $date_to);
        $statistics = $this->getAccountStatistics($account_id, $date_from, $date_to);
        $trend_analysis = $this->analyzeTrend($account_id);
        $activity_analysis = $this->analyzeActivity($account_id, $date_from, $date_to);
        $extremes = $this->analyzeExtremes($account_id, $date_from, $date_to);

        return array(
            'account_info' => $account_info,
            'balance_data' => $balance_data,
            'statistics' => $statistics,
            'trend_analysis' => $trend_analysis,
            'activity_analysis' => $activity_analysis,
            'extremes' => $extremes,
            'generated_at' => date('Y-m-d H:i:s')
        );
    }

    // حفظ الاستعلام المفضل
    public function saveFavoriteQuery($user_id, $query_name, $query_data) {
        $this->db->query("INSERT INTO `cod_account_query_favorites` SET
            user_id = '" . (int)$user_id . "',
            query_name = '" . $this->db->escape($query_name) . "',
            query_data = '" . $this->db->escape(json_encode($query_data)) . "',
            created_at = NOW()");

        return $this->db->getLastId();
    }

    // جلب الاستعلامات المفضلة
    public function getFavoriteQueries($user_id) {
        $query = $this->db->query("SELECT
            favorite_id,
            query_name,
            query_data,
            created_at
            FROM `cod_account_query_favorites`
            WHERE user_id = '" . (int)$user_id . "'
            ORDER BY created_at DESC");

        $favorites = array();
        foreach ($query->rows as $row) {
            $favorites[] = array(
                'favorite_id' => $row['favorite_id'],
                'query_name' => $row['query_name'],
                'query_data' => json_decode($row['query_data'], true),
                'created_at' => $row['created_at']
            );
        }

        return $favorites;
    }

    // جلب استعلام مفضل محدد
    public function getFavoriteQuery($favorite_id, $user_id) {
        $query = $this->db->query("SELECT
            favorite_id,
            query_name,
            query_data,
            created_at
            FROM `cod_account_query_favorites`
            WHERE favorite_id = '" . (int)$favorite_id . "'
            AND user_id = '" . (int)$user_id . "'");

        if ($query->num_rows) {
            return array(
                'favorite_id' => $query->row['favorite_id'],
                'query_name' => $query->row['query_name'],
                'query_data' => json_decode($query->row['query_data'], true),
                'created_at' => $query->row['created_at']
            );
        }

        return false;
    }

    // حذف استعلام مفضل
    public function deleteFavoriteQuery($favorite_id, $user_id) {
        // SECURITY FIX: Use proper parameter binding to prevent SQL injection
        $favorite_id = (int)$favorite_id;
        $user_id = (int)$user_id;

        $this->db->query("DELETE FROM `" . DB_PREFIX . "account_query_favorites`
            WHERE favorite_id = '" . (int)$favorite_id . "'
            AND user_id = '" . (int)$user_id . "'");

        return $this->db->countAffected() > 0;
    }

    // إنشاء بيانات التصدير المتقدمة
    public function generateExportData($account_id, $date_from = '', $date_to = '', $include_summary = true, $include_statistics = false) {
        $export_data = array();

        // معلومات الحساب
        $export_data['account_info'] = $this->getAccountInfo($account_id);

        // ملخص الأرصدة
        if ($include_summary) {
            $export_data['balance_summary'] = $this->calculateAccountBalance($account_id, $date_from, $date_to);
        }

        // الإحصائيات
        if ($include_statistics) {
            $export_data['statistics'] = $this->getAccountStatistics($account_id, $date_from, $date_to);
        }

        // المعاملات
        $export_data['transactions'] = $this->getAllTransactions($account_id, $date_from, $date_to);

        // معلومات التصدير
        $export_data['export_info'] = array(
            'generated_at' => date('Y-m-d H:i:s'),
            'period_from' => $date_from,
            'period_to' => $date_to,
            'total_transactions' => count($export_data['transactions']),
            'include_summary' => $include_summary,
            'include_statistics' => $include_statistics
        );

        return $export_data;
    }

    // إنشاء CSV متقدم
    public function generateAdvancedCSV($export_data) {
        $csv_content = "تقرير استعلام الحسابات المتقدم\n";
        $csv_content .= "تاريخ الإنشاء: " . $export_data['export_info']['generated_at'] . "\n";
        $csv_content .= "رمز الحساب: " . $export_data['account_info']['account_code'] . "\n";
        $csv_content .= "اسم الحساب: " . $export_data['account_info']['account_name'] . "\n";
        $csv_content .= "نوع الحساب: " . $export_data['account_info']['account_type'] . "\n";
        $csv_content .= "الفترة من: " . ($export_data['export_info']['period_from'] ?: 'البداية') . "\n";
        $csv_content .= "الفترة إلى: " . ($export_data['export_info']['period_to'] ?: 'النهاية') . "\n";
        $csv_content .= "عدد المعاملات: " . $export_data['export_info']['total_transactions'] . "\n\n";

        // ملخص الأرصدة
        if (isset($export_data['balance_summary'])) {
            $csv_content .= "ملخص الأرصدة\n";
            $csv_content .= "البند,المبلغ\n";
            $csv_content .= "الرصيد الافتتاحي," . $export_data['balance_summary']['opening_balance'] . "\n";
            $csv_content .= "إجمالي المدين," . $export_data['balance_summary']['period_debit'] . "\n";
            $csv_content .= "إجمالي الدائن," . $export_data['balance_summary']['period_credit'] . "\n";
            $csv_content .= "صافي الحركة," . $export_data['balance_summary']['net_movement'] . "\n";
            $csv_content .= "الرصيد الختامي," . $export_data['balance_summary']['closing_balance'] . "\n\n";
        }

        // الإحصائيات
        if (isset($export_data['statistics'])) {
            $csv_content .= "الإحصائيات العامة\n";
            $csv_content .= "المؤشر,القيمة\n";
            $csv_content .= "الأيام النشطة," . ($export_data['statistics']['general']['active_days'] ?: 0) . "\n";
            $csv_content .= "متوسط المدين," . ($export_data['statistics']['general']['avg_debit'] ?: 0) . "\n";
            $csv_content .= "متوسط الدائن," . ($export_data['statistics']['general']['avg_credit'] ?: 0) . "\n";
            $csv_content .= "أعلى مدين," . ($export_data['statistics']['general']['max_debit'] ?: 0) . "\n";
            $csv_content .= "أعلى دائن," . ($export_data['statistics']['general']['max_credit'] ?: 0) . "\n\n";
        }

        // المعاملات
        $csv_content .= "المعاملات التفصيلية\n";
        $csv_content .= "التاريخ,المرجع,البيان,مدين,دائن,الرصيد الجاري,المصدر\n";

        foreach ($export_data['transactions'] as $transaction) {
            $csv_content .= '"' . $transaction['date'] . '",';
            $csv_content .= '"' . $transaction['reference'] . '",';
            $csv_content .= '"' . $transaction['description'] . '",';
            $csv_content .= number_format($transaction['debit'], 2) . ',';
            $csv_content .= number_format($transaction['credit'], 2) . ',';
            $csv_content .= number_format($transaction['running_balance'], 2) . ',';
            $csv_content .= '"' . $transaction['source_type'] . '"' . "\n";
        }

        return $csv_content;
    }

    // إنشاء XML
    public function generateXML($export_data) {
        $xml = new SimpleXMLElement('<?xml version="1.0" encoding="UTF-8"?><account_report></account_report>');

        // معلومات الحساب
        $account_info = $xml->addChild('account_info');
        $account_info->addChild('account_id', $export_data['account_info']['account_id']);
        $account_info->addChild('account_code', htmlspecialchars($export_data['account_info']['account_code']));
        $account_info->addChild('account_name', htmlspecialchars($export_data['account_info']['account_name']));
        $account_info->addChild('account_type', htmlspecialchars($export_data['account_info']['account_type']));

        // ملخص الأرصدة
        if (isset($export_data['balance_summary'])) {
            $balance_summary = $xml->addChild('balance_summary');
            $balance_summary->addChild('opening_balance', $export_data['balance_summary']['opening_balance']);
            $balance_summary->addChild('period_debit', $export_data['balance_summary']['period_debit']);
            $balance_summary->addChild('period_credit', $export_data['balance_summary']['period_credit']);
            $balance_summary->addChild('net_movement', $export_data['balance_summary']['net_movement']);
            $balance_summary->addChild('closing_balance', $export_data['balance_summary']['closing_balance']);
        }

        // المعاملات
        $transactions = $xml->addChild('transactions');
        foreach ($export_data['transactions'] as $transaction) {
            $trans = $transactions->addChild('transaction');
            $trans->addChild('date', $transaction['date']);
            $trans->addChild('reference', htmlspecialchars($transaction['reference']));
            $trans->addChild('description', htmlspecialchars($transaction['description']));
            $trans->addChild('debit', $transaction['debit']);
            $trans->addChild('credit', $transaction['credit']);
            $trans->addChild('running_balance', $transaction['running_balance']);
            $trans->addChild('source_type', htmlspecialchars($transaction['source_type']));
        }

        // معلومات التصدير
        $export_info = $xml->addChild('export_info');
        $export_info->addChild('generated_at', $export_data['export_info']['generated_at']);
        $export_info->addChild('period_from', $export_data['export_info']['period_from']);
        $export_info->addChild('period_to', $export_data['export_info']['period_to']);
        $export_info->addChild('total_transactions', $export_data['export_info']['total_transactions']);

        return $xml->asXML();
    }

    /**
     * تحسينات الأداء والأمان
     */

    // تنظيف وتحقق من البيانات
    private function sanitizeInput($data) {
        if (is_array($data)) {
            foreach ($data as $key => $value) {
                $data[$key] = $this->sanitizeInput($value);
            }
        } else {
            $data = $this->db->escape($data);
        }
        return $data;
    }

    // التحقق من صحة التاريخ
    private function validateDate($date) {
        if (empty($date)) return true;

        $d = DateTime::createFromFormat('Y-m-d', $date);
        return $d && $d->format('Y-m-d') === $date;
    }

    // التحقق من صحة معرف الحساب
    private function validateAccountId($account_id) {
        $account_id = (int)$account_id;
        if ($account_id <= 0) return false;

        $query = $this->db->query("SELECT COUNT(*) as total FROM `" . DB_PREFIX . "accounts`
            WHERE account_id = '" . (int)$account_id . "'");

        return $query->row['total'] > 0;
    }

    // تحسين الاستعلامات مع التخزين المؤقت
    private function getCachedQuery($cache_key, $sql, $ttl = 300) {
        // التحقق من وجود النتيجة في التخزين المؤقت
        $cached_result = $this->cache->get($cache_key);
        if ($cached_result !== false) {
            return $cached_result;
        }

        // تنفيذ الاستعلام وحفظ النتيجة
        $query = $this->db->query($sql);
        $result = $query->rows;

        // حفظ في التخزين المؤقت
        $this->cache->set($cache_key, $result, $ttl);

        return $result;
    }

    // تحسين استعلامات الرصيد مع الفهارس
    public function getOptimizedAccountBalance($account_code, $date_from = '', $date_to = '') {
        $where_date = "";
        if ($date_from && $date_to) {
            $where_date = " AND j.thedate BETWEEN '" . $this->db->escape($date_from) . "' AND '" . $this->db->escape($date_to) . "'";
        }

        $cache_key = 'account_balance_' . md5($account_code . $date_from . $date_to);

        $sql = "SELECT
            SUM(CASE WHEN je.is_debit = 1 THEN je.amount ELSE 0 END) as total_debit,
            SUM(CASE WHEN je.is_debit = 0 THEN je.amount ELSE 0 END) as total_credit,
            COUNT(je.entry_id) as transaction_count
            FROM `" . DB_PREFIX . "journal_entries` je
            FORCE INDEX (idx_account_date)
            LEFT JOIN `" . DB_PREFIX . "journals` j ON (je.journal_id = j.journal_id)
            WHERE je.account_code = '" . $this->db->escape($account_code) . "'
            AND j.status = 'posted'" . $where_date;

        return $this->getCachedQuery($cache_key, $sql, 600); // 10 minutes cache
    }

    // تحليل أداء الحساب المتقدم
    public function getAdvancedPerformanceAnalysis($account_id, $periods = 12) {
        if (!$this->validateAccountId($account_id)) {
            return false;
        }

        $account_info = $this->getAccountInfo($account_id);
        $account_code = $account_info['account_code'];

        $cache_key = 'performance_analysis_' . $account_id . '_' . $periods;

        $sql = "SELECT
            DATE_FORMAT(j.thedate, '%Y-%m') as period,
            SUM(CASE WHEN je.is_debit = 1 THEN je.amount ELSE 0 END) as debit_total,
            SUM(CASE WHEN je.is_debit = 0 THEN je.amount ELSE 0 END) as credit_total,
            COUNT(je.entry_id) as transaction_count,
            AVG(je.amount) as avg_amount,
            MAX(je.amount) as max_amount,
            MIN(je.amount) as min_amount
            FROM `" . DB_PREFIX . "journal_entries` je
            LEFT JOIN `" . DB_PREFIX . "journals` j ON (je.journal_id = j.journal_id)
            WHERE je.account_code = '" . $this->db->escape($account_code) . "'
            AND j.status = 'posted'
            AND j.thedate >= DATE_SUB(CURDATE(), INTERVAL " . (int)$periods . " MONTH)
            GROUP BY DATE_FORMAT(j.thedate, '%Y-%m')
            ORDER BY period DESC";

        return $this->getCachedQuery($cache_key, $sql, 1800); // 30 minutes cache
    }

    // تحسين استعلامات الحسابات مع التخزين المؤقت
    public function getOptimizedAccountQuery($filter_data) {
        $cache_key = 'account_query_' . md5(serialize($filter_data));

        $cached_result = $this->cache->get($cache_key);
        if ($cached_result !== false) {
            return $cached_result;
        }

        $result = $this->advancedSearch($filter_data);
        $this->cache->set($cache_key, $result, 1800);

        return $result;
    }

    // البحث الذكي المحسن
    public function getSmartSearch($search_term, $limit = 20) {
        $cache_key = 'smart_search_' . md5($search_term . '_' . $limit);

        $cached_result = $this->cache->get($cache_key);
        if ($cached_result !== false) {
            return $cached_result;
        }

        $search_term = $this->db->escape($search_term);

        $query = $this->db->query("
            SELECT
                a.account_id,
                a.account_code,
                ad.name as account_name,
                a.account_type,
                a.opening_balance
            FROM " . DB_PREFIX . "accounts a
            LEFT JOIN " . DB_PREFIX . "account_description ad ON (a.account_id = ad.account_id)
            WHERE (
                ad.name LIKE '%" . $search_term . "%'
                OR a.account_code LIKE '%" . $search_term . "%'
            )
            AND a.is_active = 1
            ORDER BY a.account_code ASC
            LIMIT " . (int)$limit
        );

        $results = $query->rows;

        $this->cache->set($cache_key, $results, 1800);

        return $results;
    }

    // التحقق من صحة البيانات
    private function validateQueryData($data) {
        $errors = array();

        if (isset($data['account_id']) && !is_numeric($data['account_id'])) {
            $errors[] = 'Invalid account ID';
        }

        if (isset($data['date_from']) && !$this->validateDate($data['date_from'])) {
            $errors[] = 'Invalid start date';
        }

        if (isset($data['date_to']) && !$this->validateDate($data['date_to'])) {
            $errors[] = 'Invalid end date';
        }

        return $errors;
    }

    private function validateDate($date) {
        if (empty($date)) return false;

        $d = DateTime::createFromFormat('Y-m-d', $date);
        return $d && $d->format('Y-m-d') === $date;
    }
}
