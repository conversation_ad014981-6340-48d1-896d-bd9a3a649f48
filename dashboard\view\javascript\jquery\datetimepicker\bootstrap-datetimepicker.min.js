!function(e,t){"use strict";if("function"==typeof define&&define.amd)define(["jquery","moment"],t);else if("object"==typeof exports)t(require("jquery"),require("moment"));else{if(!jQuery)throw new Error("bootstrap-datetimepicker requires jQuery to be loaded first");if(!moment)throw new Error("bootstrap-datetimepicker requires moment.js to be loaded first");t(e.jQuery,moment)}}(this,function(U,N){"use strict";if(void 0===N)throw new Error("momentjs is required");function i(i,o){function a(){var e,t="absolute",i=(w.component||w.element).offset(),o=U(window);w.width=(w.component||w.element).outerWidth(),i.top=i.top+w.element.outerHeight(),"up"===w.options.direction?e="top":"bottom"===w.options.direction?e="bottom":"auto"===w.options.direction&&(e=i.top+w.widget.height()>o.height()+o.scrollTop()&&w.widget.height()+w.element.outerHeight()<i.top?"top":"bottom"),"top"===e?(i.bottom=o.height()-i.top+w.element.outerHeight()+3,w.widget.addClass("top").removeClass("bottom")):(i.top+=1,w.widget.addClass("bottom").removeClass("top")),void 0!==w.options.width&&w.widget.width(w.options.width),"left"===w.options.orientation&&(w.widget.addClass("left-oriented"),i.left=i.left-w.widget.width()+20),q()&&(t="fixed",i.top-=o.scrollTop(),i.left-=o.scrollLeft()),o.width()<i.left+w.widget.outerWidth()?(i.right=o.width()-i.left-w.width,i.left="auto",w.widget.addClass("pull-right")):(i.right="auto",w.widget.removeClass("pull-right")),"top"===e?w.widget.css({position:t,bottom:i.bottom,top:"auto",left:i.left,right:i.right}):w.widget.css({position:t,top:i.top,bottom:"auto",left:i.left,right:i.right})}function d(e,t){N(w.date).isSame(N(e))&&!f||(f=!1,w.element.trigger({type:"dp.change",date:N(w.date),oldDate:N(e)}),"change"!==t&&w.element.change())}function n(e){f=!0,w.element.trigger({type:"dp.error",date:N(e,w.format,w.options.useStrict)})}function e(e){e.stopPropagation(),e.preventDefault(),w.unset=!1;var t,i,o,a,n=U(e.target).closest("span, td, th"),s=N(w.date);if(1===n.length&&!n.is(".disabled"))switch(n[0].nodeName.toLowerCase()){case"th":switch(n[0].className){case"picker-switch":P(1);break;case"prev":case"next":o=j.modes[w.viewMode].navStep,"prev"===n[0].className&&(o*=-1),w.viewDate.add(o,j.modes[w.viewMode].navFnc),y()}break;case"span":n.is(".month")?(t=n.parent().find("span").index(n),w.viewDate.month(t)):(i=parseInt(n.text(),10)||0,w.viewDate.year(i)),w.viewMode===w.minViewMode&&(w.date=N({y:w.viewDate.year(),M:w.viewDate.month(),d:w.viewDate.date(),h:w.date.hours(),m:w.date.minutes(),s:w.date.seconds()}),I(),d(s,e.type)),P(-1),y();break;case"td":n.is(".day")&&(a=parseInt(n.text(),10)||1,t=w.viewDate.month(),i=w.viewDate.year(),n.is(".old")?0===t?(t=11,--i):--t:n.is(".new")&&(11===t?(t=0,i+=1):t+=1),w.date=N({y:i,M:t,d:a,h:w.date.hours(),m:w.date.minutes(),s:w.date.seconds()}),w.viewDate=N({y:i,M:t,d:Math.min(28,a)}),y(),I(),d(s,e.type))}}function s(e){var t=N(w.date),i=U(e.currentTarget).data("action"),i=C[i].apply(w,arguments);return T(e),w.date||(w.date=N({y:1970})),I(),x(),d(t,e.type),i}function r(e){27===e.keyCode&&w.hide()}function c(e){N.locale(w.options.language);var t=U(e.target),i=N(w.date);(t=N(t.val(),w.format,w.options.useStrict)).isValid()&&!O(t)&&H(t)?(k(),w.setValue(t),d(i,e.type),I()):(w.viewDate=i,w.unset=!0,d(i,e.type),n(t))}function t(){w.widget.off("click",".datepicker *",w.click),w.widget.off("click","[data-action]"),w.widget.off("mousedown",w.stopEvent),w.options.pickDate&&w.options.pickTime&&w.widget.off("click.togglePicker"),w.isInput?w.element.off({focus:w.show,change:c,click:w.show,blur:w.hide}):(w.element.off({change:c},"input"),w.component?(w.component.off("click",w.show),w.component.off("mousedown",w.stopEvent)):w.element.off("click",w.show))}function p(){U(window).off("resize.datetimepicker"+w.id),w.isInput||U(document).off("mousedown.datetimepicker"+w.id)}var l,m=U.fn.datetimepicker.defaults,u={time:"glyphicon glyphicon-time",date:"glyphicon glyphicon-calendar",up:"glyphicon glyphicon-chevron-up",down:"glyphicon glyphicon-chevron-down"},w=this,f=!1,h=function(){var e;if(w.isInput)return w.element;if(0===(e=w.element.find(".datepickerinput")).length)e=w.element.find("input");else if(!e.is("input"))throw new Error('CSS class "datepickerinput" cannot be applied to non input element');return e},g=function(){var e=(w.element.is("input")?w.element:w.element.find("input")).data();void 0!==e.dateFormat&&(w.options.format=e.dateFormat),void 0!==e.datePickdate&&(w.options.pickDate=e.datePickdate),void 0!==e.datePicktime&&(w.options.pickTime=e.datePicktime),void 0!==e.dateUseminutes&&(w.options.useMinutes=e.dateUseminutes),void 0!==e.dateUseseconds&&(w.options.useSeconds=e.dateUseseconds),void 0!==e.dateUsecurrent&&(w.options.useCurrent=e.dateUsecurrent),void 0!==e.calendarWeeks&&(w.options.calendarWeeks=e.calendarWeeks),void 0!==e.dateMinutestepping&&(w.options.minuteStepping=e.dateMinutestepping),void 0!==e.dateMindate&&(w.options.minDate=e.dateMindate),void 0!==e.dateMaxdate&&(w.options.maxDate=e.dateMaxdate),void 0!==e.dateShowtoday&&(w.options.showToday=e.dateShowtoday),void 0!==e.dateCollapse&&(w.options.collapse=e.dateCollapse),void 0!==e.dateLanguage&&(w.options.language=e.dateLanguage),void 0!==e.dateDefaultdate&&(w.options.defaultDate=e.dateDefaultdate),void 0!==e.dateDisableddates&&(w.options.disabledDates=e.dateDisableddates),void 0!==e.dateEnableddates&&(w.options.enabledDates=e.dateEnableddates),void 0!==e.dateIcons&&(w.options.icons=e.dateIcons),void 0!==e.dateUsestrict&&(w.options.useStrict=e.dateUsestrict),void 0!==e.dateDirection&&(w.options.direction=e.dateDirection),void 0!==e.dateSidebyside&&(w.options.sideBySide=e.dateSidebyside),void 0!==e.dateDaysofweekdisabled&&(w.options.daysOfWeekDisabled=e.dateDaysofweekdisabled)},k=function(e){N.locale(w.options.language);e||((e=h().val())&&(w.date=N(e,w.format,w.options.useStrict)),w.date||(w.date=N())),w.viewDate=N(w.date).startOf("month"),y(),x()},v=function(){N.locale(w.options.language);var e,t=U("<tr>"),i=N.weekdaysMin();if(!0===w.options.calendarWeeks&&t.append('<th class="cw">#</th>'),0===N().localeData()._week.dow)for(e=0;e<7;e++)t.append('<th class="dow">'+i[e]+"</th>");else for(e=1;e<8;e++)7===e?t.append('<th class="dow">'+i[0]+"</th>"):t.append('<th class="dow">'+i[e]+"</th>");w.widget.find(".datepicker-days thead").append(t)},b=function(){N.locale(w.options.language);for(var e="",t=N.monthsShort(),i=0;i<12;i++)e+='<span class="month">'+t[i]+"</span>";w.widget.find(".datepicker-months td").append(e)},y=function(){if(w.options.pickDate){N.locale(w.options.language);var e,t,i,o,a,n,s,d,r=w.viewDate.year(),c=w.viewDate.month(),p=w.options.minDate.year(),l=w.options.minDate.month(),m=w.options.maxDate.year(),u=w.options.maxDate.month(),f=[],h=N.months();for(w.widget.find(".datepicker-days").find(".disabled").removeClass("disabled"),w.widget.find(".datepicker-months").find(".disabled").removeClass("disabled"),w.widget.find(".datepicker-years").find(".disabled").removeClass("disabled"),w.widget.find(".datepicker-days th:eq(1)").text(h[c]+" "+r),s=(t=N(w.viewDate,w.format,w.options.useStrict).subtract(1,"months")).daysInMonth(),t.date(s).startOf("week"),(r===p&&c<=l||r<p)&&w.widget.find(".datepicker-days th:eq(0)").addClass("disabled"),(r===m&&u<=c||m<r)&&w.widget.find(".datepicker-days th:eq(2)").addClass("disabled"),i=N(t).add(42,"d");t.isBefore(i);){if(t.weekday()===N().startOf("week").weekday()&&(o=U("<tr>"),f.push(o),!0===w.options.calendarWeeks&&o.append('<td class="cw">'+t.week()+"</td>")),a="",t.year()<r||t.year()===r&&t.month()<c?a+=" old":(t.year()>r||t.year()===r&&t.month()>c)&&(a+=" new"),t.isSame(N({y:w.date.year(),M:w.date.month(),d:w.date.date()}))&&(a+=" active"),!O(t,"day")&&H(t)||(a+=" disabled"),!0===w.options.showToday&&t.isSame(N(),"day")&&(a+=" today"),w.options.daysOfWeekDisabled)for(n=0;n<w.options.daysOfWeekDisabled.length;n++)if(t.day()===w.options.daysOfWeekDisabled[n]){a+=" disabled";break}o.append('<td class="day'+a+'">'+t.date()+"</td>"),e=t.date(),t.add(1,"d"),e===t.date()&&t.add(1,"d")}for(w.widget.find(".datepicker-days tbody").empty().append(f),d=w.date.year(),h=w.widget.find(".datepicker-months").find("th:eq(1)").text(r).end().find("span").removeClass("active"),d===r&&h.eq(w.date.month()).addClass("active"),r-1<p&&w.widget.find(".datepicker-months th:eq(0)").addClass("disabled"),m<r+1&&w.widget.find(".datepicker-months th:eq(2)").addClass("disabled"),n=0;n<12;n++)(r===p&&n<l||r<p||r===m&&u<n||m<r)&&U(h[n]).addClass("disabled");for(f="",r=10*parseInt(r/10,10),s=w.widget.find(".datepicker-years").find("th:eq(1)").text(r+"-"+(r+9)).parents("table").find("td"),w.widget.find(".datepicker-years").find("th").removeClass("disabled"),r<p&&w.widget.find(".datepicker-years").find("th:eq(0)").addClass("disabled"),m<r+9&&w.widget.find(".datepicker-years").find("th:eq(2)").addClass("disabled"),--r,n=-1;n<11;n++)f+='<span class="year'+(-1===n||10===n?" old":"")+(d===r?" active":"")+(r<p||m<r?" disabled":"")+'">'+r+"</span>",r+=1;s.html(f)}},D=function(){N.locale(w.options.language);var e,t,i,o=w.widget.find(".timepicker .timepicker-hours table"),a="";if(o.parent().hide(),w.use24hours)for(t=e=0;t<6;t+=1){for(a+="<tr>",i=0;i<4;i+=1)a+='<td class="hour">'+E(e.toString())+"</td>",e++;a+="</tr>"}else for(e=1,t=0;t<3;t+=1){for(a+="<tr>",i=0;i<4;i+=1)a+='<td class="hour">'+E(e.toString())+"</td>",e++;a+="</tr>"}o.html(a)},M=function(){var e,t,i=w.widget.find(".timepicker .timepicker-minutes table"),o="",a=0,n=w.options.minuteStepping;for(i.parent().hide(),1===n&&(n=5),e=0;e<Math.ceil(60/n/4);e++){for(o+="<tr>",t=0;t<4;t+=1)a<60?(o+='<td class="minute">'+E(a.toString())+"</td>",a+=n):o+="<td></td>";o+="</tr>"}i.html(o)},S=function(){var e,t,i=w.widget.find(".timepicker .timepicker-seconds table"),o="",a=0;for(i.parent().hide(),e=0;e<3;e++){for(o+="<tr>",t=0;t<4;t+=1)o+='<td class="second">'+E(a.toString())+"</td>",a+=5;o+="</tr>"}i.html(o)},x=function(){var e,t,i;w.date&&(e=w.widget.find(".timepicker span[data-time-component]"),t=w.date.hours(),i=w.date.format("A"),w.use24hours||(0===t?t=12:12!==t&&(t%=12),w.widget.find(".timepicker [data-action=togglePeriod]").text(i)),e.filter("[data-time-component=hours]").text(E(t)),e.filter("[data-time-component=minutes]").text(E(w.date.minutes())),e.filter("[data-time-component=seconds]").text(E(w.date.second())))},C={incrementHours:function(){W("add","hours",1)},incrementMinutes:function(){W("add","minutes",w.options.minuteStepping)},incrementSeconds:function(){W("add","seconds",1)},decrementHours:function(){W("subtract","hours",1)},decrementMinutes:function(){W("subtract","minutes",w.options.minuteStepping)},decrementSeconds:function(){W("subtract","seconds",1)},togglePeriod:function(){var e=w.date.hours();12<=e?e-=12:e+=12,w.date.hours(e)},showPicker:function(){w.widget.find(".timepicker > div:not(.timepicker-picker)").hide(),w.widget.find(".timepicker .timepicker-picker").show()},showHours:function(){w.widget.find(".timepicker .timepicker-picker").hide(),w.widget.find(".timepicker .timepicker-hours").show()},showMinutes:function(){w.widget.find(".timepicker .timepicker-picker").hide(),w.widget.find(".timepicker .timepicker-minutes").show()},showSeconds:function(){w.widget.find(".timepicker .timepicker-picker").hide(),w.widget.find(".timepicker .timepicker-seconds").show()},selectHour:function(e){e=parseInt(U(e.target).text(),10);w.use24hours||(12<=w.date.hours()?12!==e&&(e+=12):12===e&&(e=0)),w.date.hours(e),C.showPicker.call(w)},selectMinute:function(e){w.date.minutes(parseInt(U(e.target).text(),10)),C.showPicker.call(w)},selectSecond:function(e){w.date.seconds(parseInt(U(e.target).text(),10)),C.showPicker.call(w)}},T=function(e){e.stopPropagation(),e.preventDefault()},P=function(e){e&&(w.viewMode=Math.max(w.minViewMode,Math.min(2,w.viewMode+e))),w.widget.find(".datepicker > div").hide().filter(".datepicker-"+j.modes[w.viewMode].clsName).show()},V=function(){var t,i,o,a;w.widget.on("click",".datepicker *",U.proxy(e,this)),w.widget.on("click","[data-action]",U.proxy(s,this)),w.widget.on("mousedown",U.proxy(T,this)),w.element.on("keydown",U.proxy(r,this)),w.options.pickDate&&w.options.pickTime&&w.widget.on("click.togglePicker",".accordion-toggle",function(e){e.stopPropagation(),t=U(this),a=t.closest("ul"),i=a.find(".in"),o=a.find(".collapse:not(.in)"),i&&i.length&&((a=i.data("collapse"))&&a.transitioning||(i.collapse("hide"),o.collapse("show"),t.find("span").toggleClass(w.options.icons.time+" "+w.options.icons.date),w.component&&w.component.find("span").toggleClass(w.options.icons.time+" "+w.options.icons.date)))}),w.isInput?w.element.on({click:U.proxy(w.show,this),focus:U.proxy(w.show,this),change:U.proxy(c,this),blur:U.proxy(w.hide,this)}):(w.element.on({change:U.proxy(c,this)},"input"),w.component?(w.component.on("click",U.proxy(w.show,this)),w.component.on("mousedown",U.proxy(T,this))):w.element.on("click",U.proxy(w.show,this)))},q=function(){if(w.element){for(var e=w.element.parents(),t=!1,i=0;i<e.length;i++)if("fixed"===U(e[i]).css("position")){t=!0;break}return t}return!1},I=function(){N.locale(w.options.language);var e="";w.unset||(e=N(w.date).format(w.format)),h().val(e),w.element.data("date",e),w.options.pickTime||w.hide()},W=function(e,t,i){var o;N.locale(w.options.language),"add"===e?(23===(o=N(w.date)).hours()&&o.add(i,t),o.add(i,t)):o=N(w.date).subtract(i,t),O(N(o.subtract(i,t)))||O(o)?n(o.format(w.format)):("add"===e?w.date.add(i,t):w.date.subtract(i,t),w.unset=!1)},O=function(e,t){N.locale(w.options.language);var i=N(w.options.maxDate,w.format,w.options.useStrict),o=N(w.options.minDate,w.format,w.options.useStrict);return t&&(i=i.endOf(t),o=o.startOf(t)),!(!e.isAfter(i)&&!e.isBefore(o))||!1!==w.options.disabledDates&&!0===w.options.disabledDates[e.format("YYYY-MM-DD")]},H=function(e){return N.locale(w.options.language),!1===w.options.enabledDates||!0===w.options.enabledDates[e.format("YYYY-MM-DD")]},Y=function(e){for(var t={},i=0,o=0;o<e.length;o++)(l=N.isMoment(e[o])||e[o]instanceof Date?N(e[o]):N(e[o],w.format,w.options.useStrict)).isValid()&&(t[l.format("YYYY-MM-DD")]=!0,i++);return 0<i&&t},E=function(e){return 2<=(e=e.toString()).length?e:"0"+e},F=function(){var e='<thead><tr><th class="prev">&lsaquo;</th><th colspan="'+(w.options.calendarWeeks?"6":"5")+'" class="picker-switch"></th><th class="next">&rsaquo;</th></tr></thead>',t='<tbody><tr><td colspan="'+(w.options.calendarWeeks?"8":"7")+'"></td></tr></tbody>',e='<div class="datepicker-days"><table class="table-condensed">'+e+'<tbody></tbody></table></div><div class="datepicker-months"><table class="table-condensed">'+e+t+'</table></div><div class="datepicker-years"><table class="table-condensed">'+e+t+"</table></div>",t="";return w.options.pickDate&&w.options.pickTime?(t='<div class="bootstrap-datetimepicker-widget'+(w.options.sideBySide?" timepicker-sbs":"")+(w.use24hours?" usetwentyfour":"")+' dropdown-menu" style="z-index:9999 !important;">',w.options.sideBySide?t+='<div class="row"><div class="col-sm-6 datepicker">'+e+'</div><div class="col-sm-6 timepicker">'+L.getTemplate()+"</div></div>":t+='<ul class="list-unstyled"><li'+(w.options.collapse?' class="collapse in"':"")+'><div class="datepicker">'+e+'</div></li><li class="picker-switch accordion-toggle"><a class="btn" style="width:100%"><span class="'+w.options.icons.time+'"></span></a></li><li'+(w.options.collapse?' class="collapse"':"")+'><div class="timepicker">'+L.getTemplate()+"</div></li></ul>",t+="</div>"):w.options.pickTime?'<div class="bootstrap-datetimepicker-widget dropdown-menu"><div class="timepicker">'+L.getTemplate()+"</div></div>":'<div class="bootstrap-datetimepicker-widget dropdown-menu"><div class="datepicker">'+e+"</div></div>"},j={modes:[{clsName:"days",navFnc:"month",navStep:1},{clsName:"months",navFnc:"year",navStep:1},{clsName:"years",navFnc:"year",navStep:10}]},L={hourTemplate:'<span data-action="showHours"   data-time-component="hours"   class="timepicker-hour"></span>',minuteTemplate:'<span data-action="showMinutes" data-time-component="minutes" class="timepicker-minute"></span>',secondTemplate:'<span data-action="showSeconds"  data-time-component="seconds" class="timepicker-second"></span>',getTemplate:function(){return'<div class="timepicker-picker"><table class="table-condensed"><tr><td><a href="#" class="btn" data-action="incrementHours"><span class="'+w.options.icons.up+'"></span></a></td><td class="separator"></td><td>'+(w.options.useMinutes?'<a href="#" class="btn" data-action="incrementMinutes"><span class="'+w.options.icons.up+'"></span></a>':"")+"</td>"+(w.options.useSeconds?'<td class="separator"></td><td><a href="#" class="btn" data-action="incrementSeconds"><span class="'+w.options.icons.up+'"></span></a></td>':"")+(w.use24hours?"":'<td class="separator"></td>')+"</tr><tr><td>"+L.hourTemplate+'</td> <td class="separator">:</td><td>'+(w.options.useMinutes?L.minuteTemplate:'<span class="timepicker-minute">00</span>')+"</td> "+(w.options.useSeconds?'<td class="separator">:</td><td>'+L.secondTemplate+"</td>":"")+(w.use24hours?"":'<td class="separator"></td><td><button type="button" class="btn btn-primary" data-action="togglePeriod"></button></td>')+'</tr><tr><td><a href="#" class="btn" data-action="decrementHours"><span class="'+w.options.icons.down+'"></span></a></td><td class="separator"></td><td>'+(w.options.useMinutes?'<a href="#" class="btn" data-action="decrementMinutes"><span class="'+w.options.icons.down+'"></span></a>':"")+"</td>"+(w.options.useSeconds?'<td class="separator"></td><td><a href="#" class="btn" data-action="decrementSeconds"><span class="'+w.options.icons.down+'"></span></a></td>':"")+(w.use24hours?"":'<td class="separator"></td>')+'</tr></table></div><div class="timepicker-hours" data-action="selectHour"><table class="table-condensed"></table></div><div class="timepicker-minutes" data-action="selectMinute"><table class="table-condensed"></table></div>'+(w.options.useSeconds?'<div class="timepicker-seconds" data-action="selectSecond"><table class="table-condensed"></table></div>':"")}};w.destroy=function(){t(),p(),w.widget.remove(),w.element.removeData("DateTimePicker"),w.component&&w.component.removeData("DateTimePicker")},w.show=function(e){var t,i;h().prop("disabled")||(w.options.useCurrent&&""===h().val()&&(1!==w.options.minuteStepping?(t=N(),i=w.options.minuteStepping,t.minutes(Math.round(t.minutes()/i)*i%60).seconds(0),w.setValue(t.format(w.format))):w.setValue(N().format(w.format)),d("",e.type)),e&&"click"===e.type&&w.isInput&&w.widget.hasClass("picker-open")||(w.widget.hasClass("picker-open")?(w.widget.hide(),w.widget.removeClass("picker-open")):(w.widget.show(),w.widget.addClass("picker-open")),w.height=(w.component||w.element).outerHeight(),a(),w.element.trigger({type:"dp.show",date:N(w.date)}),function(){U(window).on("resize.datetimepicker"+w.id,U.proxy(a,this)),w.isInput||U(document).on("mousedown.datetimepicker"+w.id,U.proxy(w.hide,this))}(),e&&T(e)))},w.disable=function(){var e=h();e.prop("disabled")||(e.prop("disabled",!0),t())},w.enable=function(){var e=h();e.prop("disabled")&&(e.prop("disabled",!1),V())},w.hide=function(){for(var e,t=w.widget.find(".collapse"),i=0;i<t.length;i++)if((e=t.eq(i).data("collapse"))&&e.transitioning)return;w.widget.hide(),w.widget.removeClass("picker-open"),w.viewMode=w.startViewMode,P(),w.element.trigger({type:"dp.hide",date:N(w.date)}),p()},w.setValue=function(e){N.locale(w.options.language),e?w.unset=!1:(w.unset=!0,I()),(e=N.isMoment(e)?e.locale(w.options.language):e instanceof Date?N(e):N(e,w.format,w.options.useStrict)).isValid()?(w.date=e,I(),w.viewDate=N({y:w.date.year(),M:w.date.month()}),y(),x()):n(e)},w.getDate=function(){return w.unset?null:N(w.date)},w.setDate=function(e){var t=N(w.date);e?w.setValue(e):w.setValue(null),d(t,"function")},w.setDisabledDates=function(e){w.options.disabledDates=Y(e),w.viewDate&&k()},w.setEnabledDates=function(e){w.options.enabledDates=Y(e),w.viewDate&&k()},w.setMaxDate=function(e){void 0!==e&&(N.isMoment(e)||e instanceof Date?w.options.maxDate=N(e):w.options.maxDate=N(e,w.format,w.options.useStrict),w.viewDate&&k())},w.setMinDate=function(e){void 0!==e&&(N.isMoment(e)||e instanceof Date?w.options.minDate=N(e):w.options.minDate=N(e,w.format,w.options.useStrict),w.viewDate&&k())},function(){var e,t=!1;if(w.options=U.extend({},m,o),w.options.icons=U.extend({},u,w.options.icons),w.element=U(i),g(),!w.options.pickTime&&!w.options.pickDate)throw new Error("Must choose at least one picker");if(w.id=B++,N.locale(w.options.language),w.date=N(),w.unset=!1,w.isInput=w.element.is("input"),w.component=!1,w.element.hasClass("input-group")&&(0===w.element.find(".datepickerbutton").length?w.component=w.element.find('[class^="input-group-"]'):w.component=w.element.find(".datepickerbutton")),w.format=w.options.format,e=N().localeData(),w.format||(w.format=w.options.pickDate?e.longDateFormat("L"):"",w.options.pickDate&&w.options.pickTime&&(w.format+=" "),w.format+=w.options.pickTime?e.longDateFormat("LT"):"",w.options.useSeconds&&(-1!==e.longDateFormat("LT").indexOf(" A")?w.format=w.format.split(" A")[0]+":ss A":w.format+=":ss")),w.use24hours=w.format.toLowerCase().indexOf("a")<0&&w.format.indexOf("h")<0,w.component&&(t=w.component.find("span")),w.options.pickTime&&t&&t.addClass(w.options.icons.time),w.options.pickDate&&t&&(t.removeClass(w.options.icons.time),t.addClass(w.options.icons.date)),w.options.widgetParent="string"==typeof w.options.widgetParent&&w.options.widgetParent||w.element.parents().filter(function(){return"scroll"===U(this).css("overflow-y")}).get(0)||"body",w.widget=U(F()).appendTo(w.options.widgetParent),w.minViewMode=w.options.minViewMode||0,"string"==typeof w.minViewMode)switch(w.minViewMode){case"months":w.minViewMode=1;break;case"years":w.minViewMode=2;break;default:w.minViewMode=0}if(w.viewMode=w.options.viewMode||0,"string"==typeof w.viewMode)switch(w.viewMode){case"months":w.viewMode=1;break;case"years":w.viewMode=2;break;default:w.viewMode=0}w.viewMode=Math.max(w.viewMode,w.minViewMode),w.options.disabledDates=Y(w.options.disabledDates),w.options.enabledDates=Y(w.options.enabledDates),w.startViewMode=w.viewMode,w.setMinDate(w.options.minDate),w.setMaxDate(w.options.maxDate),v(),b(),D(),M(),S(),k(),P(),h().prop("disabled")||V(),""!==w.options.defaultDate&&""===h().val()&&w.setValue(w.options.defaultDate),1!==w.options.minuteStepping&&(t=w.options.minuteStepping,w.date.minutes(Math.round(w.date.minutes()/t)*t%60).seconds(0))}()}var B=0;U.fn.datetimepicker=function(t){return this.each(function(){var e=U(this);e.data("DateTimePicker")||e.data("DateTimePicker",new i(this,t))})},U.fn.datetimepicker.defaults={format:!1,pickDate:!0,pickTime:!0,useMinutes:!0,useSeconds:!1,useCurrent:!0,calendarWeeks:!1,minuteStepping:1,minDate:N({y:1900}),maxDate:N().add(100,"y"),showToday:!0,collapse:!0,language:N.locale(),defaultDate:"",disabledDates:!1,enabledDates:!1,icons:{},useStrict:!1,direction:"auto",sideBySide:!1,daysOfWeekDisabled:[],widgetParent:!1}});