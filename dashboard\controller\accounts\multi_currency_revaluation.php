<?php
/**
 * تحكم إعادة تقييم العملات المتقدم
 * يجمع بين القوة والبساطة - يتفوق على SAP وOracle في سهولة الاستخدام
 * مع الاحتفاظ بالقوة والمرونة المطلوبة للشركات الكبيرة
 * متوافق مع معايير المحاسبة المصرية والدولية IAS 21
 */
class ControllerAccountsMultiCurrencyRevaluation extends Controller {
    private $error = array();
    private $central_service;

    public function __construct($registry) {
        parent::__construct($registry);

        // تحميل الخدمات المركزية
        $this->load->model('core/central_service_manager');
        $this->central_service = $this->model_core_central_service_manager;
    }

    public function index() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/multi_currency_revaluation') ||
            !$this->user->hasKey('accounting_multi_currency_revaluation_view')) {

            $this->central_service->logActivity('unauthorized_access', 'accounts',
                $this->language->get('log_unauthorized_access_revaluation'), [
                'user_id' => $this->user->getId(),
                'ip_address' => $this->request->server['REMOTE_ADDR']
            ]);

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/multi_currency_revaluation');
        $this->document->setTitle($this->language->get('heading_title'));
        $this->load->model('accounts/multi_currency_revaluation');

        // إضافة CSS و JavaScript المتقدم
        $this->document->addStyle('view/stylesheet/accounts/multi_currency_revaluation.css');
        $this->document->addScript('view/javascript/accounts/multi_currency_revaluation.js');
        $this->document->addScript('view/javascript/jquery/accounting.min.js');
        $this->document->addScript('view/javascript/jquery/select2.min.js');
        $this->document->addStyle('view/javascript/jquery/select2.min.css');
        $this->document->addScript('view/javascript/jquery/daterangepicker.min.js');
        $this->document->addStyle('view/javascript/jquery/daterangepicker.css');
        $this->document->addScript('view/javascript/jquery/chart.min.js');
        $this->document->addScript('view/javascript/jquery/datatables.min.js');
        $this->document->addStyle('view/javascript/jquery/datatables.min.css');

        // تسجيل الوصول للشاشة
        $this->central_service->logActivity('view', 'accounts',
            $this->language->get('log_view_revaluation_screen'), [
            'user_id' => $this->user->getId(),
            'screen' => 'multi_currency_revaluation'
        ]);

        // معالجة الطلبات
        if ($this->request->server['REQUEST_METHOD'] == 'POST') {
            if (isset($this->request->post['action'])) {
                switch ($this->request->post['action']) {
                    case 'process_revaluation':
                        $this->processRevaluation();
                        break;
                    case 'approve_revaluation':
                        $this->approveRevaluation();
                        break;
                    case 'reverse_revaluation':
                        $this->reverseRevaluation();
                        break;
                }
            }
        }

        $data = $this->getCommonData();

        // جلب العملات النشطة
        $data['currencies'] = $this->model_accounts_multi_currency_revaluation->getActiveCurrencies();
        
        // جلب آخر عمليات إعادة التقييم
        $data['recent_revaluations'] = $this->model_accounts_multi_currency_revaluation->getRecentRevaluations(10);

        // جلب أسعار الصرف الحالية
        $data['exchange_rates'] = $this->model_accounts_multi_currency_revaluation->getCurrentExchangeRates();

        // جلب الحسابات المتأثرة بالعملات الأجنبية
        $data['foreign_currency_accounts'] = $this->model_accounts_multi_currency_revaluation->getForeignCurrencyAccounts();

        // روابط Ajax
        $data['ajax_process_url'] = $this->url->link('accounts/multi_currency_revaluation/process', 'user_token=' . $this->session->data['user_token'], true);
        $data['ajax_rates_url'] = $this->url->link('accounts/multi_currency_revaluation/getRates', 'user_token=' . $this->session->data['user_token'], true);
        $data['ajax_preview_url'] = $this->url->link('accounts/multi_currency_revaluation/preview', 'user_token=' . $this->session->data['user_token'], true);
        $data['ajax_approve_url'] = $this->url->link('accounts/multi_currency_revaluation/approve', 'user_token=' . $this->session->data['user_token'], true);

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/multi_currency_revaluation', $data));
    }

    /**
     * معالجة إعادة تقييم العملات
     */
    public function process() {
        // فحص الصلاحيات المتقدمة
        if (!$this->user->hasKey('accounting_multi_currency_revaluation_process')) {
            $json['error'] = $this->language->get('error_permission_process');
            $this->response->addHeader('Content-Type: application/json');
            $this->response->setOutput(json_encode($json));
            return;
        }

        $this->load->model('accounts/multi_currency_revaluation');
        $json = array();

        try {
            $revaluation_data = array(
                'revaluation_date' => $this->request->post['revaluation_date'],
                'currencies' => $this->request->post['currencies'],
                'exchange_rates' => $this->request->post['exchange_rates'],
                'account_types' => $this->request->post['account_types'],
                'auto_post' => isset($this->request->post['auto_post'])
            );

            $revaluation_id = $this->model_accounts_multi_currency_revaluation->processRevaluation($revaluation_data);

            if ($revaluation_id) {
                $json['success'] = $this->language->get('text_revaluation_processed');
                $json['revaluation_id'] = $revaluation_id;
                
                // تسجيل العملية
                $this->central_service->logActivity('process', 'accounts',
                    $this->language->get('log_revaluation_processed'), [
                    'user_id' => $this->user->getId(),
                    'revaluation_id' => $revaluation_id,
                    'date' => $revaluation_data['revaluation_date']
                ]);

                // إرسال إشعار
                $this->central_service->sendNotification([
                    'type' => 'revaluation_processed',
                    'title' => $this->language->get('notification_revaluation_title'),
                    'message' => sprintf($this->language->get('notification_revaluation_message'), $revaluation_id),
                    'user_id' => $this->user->getId(),
                    'url' => $this->url->link('accounts/multi_currency_revaluation/view', 'revaluation_id=' . $revaluation_id)
                ]);

            } else {
                $json['error'] = $this->language->get('error_revaluation_failed');
            }

        } catch (Exception $e) {
            $json['error'] = $e->getMessage();
            
            // تسجيل الخطأ
            $this->central_service->logActivity('error', 'accounts',
                'Currency revaluation failed: ' . $e->getMessage(), [
                'user_id' => $this->user->getId(),
                'error_details' => $e->getTraceAsString()
            ]);
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * معاينة إعادة التقييم
     */
    public function preview() {
        if (!$this->user->hasKey('accounting_multi_currency_revaluation_view')) {
            $json['error'] = $this->language->get('error_permission');
            $this->response->addHeader('Content-Type: application/json');
            $this->response->setOutput(json_encode($json));
            return;
        }

        $this->load->model('accounts/multi_currency_revaluation');
        $json = array();

        try {
            $preview_data = array(
                'revaluation_date' => $this->request->post['revaluation_date'],
                'currencies' => $this->request->post['currencies'],
                'exchange_rates' => $this->request->post['exchange_rates'],
                'account_types' => $this->request->post['account_types']
            );

            $preview_results = $this->model_accounts_multi_currency_revaluation->previewRevaluation($preview_data);

            $json['success'] = true;
            $json['preview'] = $preview_results;

        } catch (Exception $e) {
            $json['error'] = $e->getMessage();
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * اعتماد إعادة التقييم
     */
    public function approve() {
        if (!$this->user->hasKey('accounting_multi_currency_revaluation_approve')) {
            $json['error'] = $this->language->get('error_permission_approve');
            $this->response->addHeader('Content-Type: application/json');
            $this->response->setOutput(json_encode($json));
            return;
        }

        $this->load->model('accounts/multi_currency_revaluation');
        $json = array();

        try {
            $revaluation_id = $this->request->post['revaluation_id'];
            $approval_notes = $this->request->post['approval_notes'];

            $result = $this->model_accounts_multi_currency_revaluation->approveRevaluation($revaluation_id, $approval_notes);

            if ($result) {
                $json['success'] = $this->language->get('text_revaluation_approved');
                
                // تسجيل العملية
                $this->central_service->logActivity('approve', 'accounts',
                    $this->language->get('log_revaluation_approved'), [
                    'user_id' => $this->user->getId(),
                    'revaluation_id' => $revaluation_id
                ]);

            } else {
                $json['error'] = $this->language->get('error_approval_failed');
            }

        } catch (Exception $e) {
            $json['error'] = $e->getMessage();
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * عكس إعادة التقييم
     */
    public function reverse() {
        if (!$this->user->hasKey('accounting_multi_currency_revaluation_approve')) {
            $json['error'] = $this->language->get('error_permission_reverse');
            $this->response->addHeader('Content-Type: application/json');
            $this->response->setOutput(json_encode($json));
            return;
        }

        $this->load->model('accounts/multi_currency_revaluation');
        $json = array();

        try {
            $revaluation_id = $this->request->post['revaluation_id'];
            $reverse_reason = $this->request->post['reverse_reason'];

            $result = $this->model_accounts_multi_currency_revaluation->reverseRevaluation($revaluation_id, $reverse_reason);

            if ($result) {
                $json['success'] = $this->language->get('text_revaluation_reversed');
                
                // تسجيل العملية
                $this->central_service->logActivity('reverse', 'accounts',
                    $this->language->get('log_revaluation_reversed'), [
                    'user_id' => $this->user->getId(),
                    'revaluation_id' => $revaluation_id,
                    'reason' => $reverse_reason
                ]);

            } else {
                $json['error'] = $this->language->get('error_reverse_failed');
            }

        } catch (Exception $e) {
            $json['error'] = $e->getMessage();
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * عرض تفاصيل إعادة التقييم
     */
    public function view() {
        if (!$this->user->hasPermission('access', 'accounts/multi_currency_revaluation') ||
            !$this->user->hasKey('accounting_multi_currency_revaluation_view')) {
            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $revaluation_id = isset($this->request->get['revaluation_id']) ? (int)$this->request->get['revaluation_id'] : 0;

        if (!$revaluation_id) {
            $this->response->redirect($this->url->link('accounts/multi_currency_revaluation'));
            return;
        }

        $this->load->language('accounts/multi_currency_revaluation');
        $this->load->model('accounts/multi_currency_revaluation');

        $data = $this->getCommonData();
        
        // جلب بيانات إعادة التقييم
        $revaluation = $this->model_accounts_multi_currency_revaluation->getRevaluation($revaluation_id);
        
        if (!$revaluation) {
            $this->response->redirect($this->url->link('accounts/multi_currency_revaluation'));
            return;
        }

        $data['revaluation'] = $revaluation;
        $data['revaluation_details'] = $this->model_accounts_multi_currency_revaluation->getRevaluationDetails($revaluation_id);
        $data['journal_entries'] = $this->model_accounts_multi_currency_revaluation->getRevaluationJournalEntries($revaluation_id);

        $this->document->setTitle($this->language->get('heading_title_view') . ' - ' . $revaluation['reference']);

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/multi_currency_revaluation_view', $data));
    }

    /**
     * جلب أسعار الصرف الحالية
     */
    public function getRates() {
        $this->load->model('accounts/multi_currency_revaluation');
        $json = array();

        try {
            $currency = $this->request->get['currency'];
            $date = $this->request->get['date'];

            $rates = $this->model_accounts_multi_currency_revaluation->getExchangeRatesForDate($currency, $date);

            $json['success'] = true;
            $json['rates'] = $rates;

        } catch (Exception $e) {
            $json['error'] = $e->getMessage();
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * جلب البيانات المشتركة
     */
    private function getCommonData() {
        $data = array();

        // النصوص
        $data['heading_title'] = $this->language->get('heading_title');
        $data['text_list'] = $this->language->get('text_list');
        $data['text_no_results'] = $this->language->get('text_no_results');
        $data['text_confirm'] = $this->language->get('text_confirm');

        // الأزرار
        $data['button_process'] = $this->language->get('button_process');
        $data['button_preview'] = $this->language->get('button_preview');
        $data['button_approve'] = $this->language->get('button_approve');
        $data['button_reverse'] = $this->language->get('button_reverse');
        $data['button_view'] = $this->language->get('button_view');
        $data['button_cancel'] = $this->language->get('button_cancel');

        // الروابط
        $data['cancel'] = $this->url->link('accounts/multi_currency_revaluation', 'user_token=' . $this->session->data['user_token'], true);
        $data['user_token'] = $this->session->data['user_token'];

        return $data;
    }
}
