{{ header }}{{ column_left }}
<div class="container-fluid">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="button" data-toggle="tooltip" title="{{ button_create }}" class="btn btn-primary" onclick="location = '{{ create }}';"><i class="fa fa-plus"></i></button>
        <button type="button" data-toggle="tooltip" title="{{ button_export }}" class="btn btn-success" onclick="exportData();"><i class="fa fa-download"></i></button>
        <button type="button" data-toggle="tooltip" title="{{ button_analytics }}" class="btn btn-warning" onclick="location = '{{ analytics }}';"><i class="fa fa-bar-chart"></i></button>
        <button type="button" data-toggle="tooltip" title="{{ button_templates }}" class="btn btn-info" onclick="location = '{{ templates }}';"><i class="fa fa-file-text"></i></button>
        <button type="button" data-toggle="tooltip" title="{{ button_touchpoints }}" class="btn btn-default" onclick="location = '{{ touchpoints }}';"><i class="fa fa-hand-pointer-o"></i></button>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  
  <!-- إحصائيات سريعة -->
  <div class="row">
    <div class="col-lg-3 col-md-6">
      <div class="panel panel-primary">
        <div class="panel-heading">
          <div class="row">
            <div class="col-xs-3">
              <i class="fa fa-road fa-5x"></i>
            </div>
            <div class="col-xs-9 text-right">
              <div class="huge">{{ statistics.total_journeys }}</div>
              <div>{{ text_total_journeys }}</div>
            </div>
          </div>
        </div>
        <div class="panel-footer">
          <span class="pull-left">{{ text_total_journeys }}</span>
          <span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
          <div class="clearfix"></div>
        </div>
      </div>
    </div>
    
    <div class="col-lg-3 col-md-6">
      <div class="panel panel-green">
        <div class="panel-heading">
          <div class="row">
            <div class="col-xs-3">
              <i class="fa fa-play fa-5x"></i>
            </div>
            <div class="col-xs-9 text-right">
              <div class="huge">{{ statistics.active_journeys }}</div>
              <div>{{ text_active_journeys }}</div>
            </div>
          </div>
        </div>
        <div class="panel-footer">
          <span class="pull-left">{{ text_active_journeys }}</span>
          <span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
          <div class="clearfix"></div>
        </div>
      </div>
    </div>
    
    <div class="col-lg-3 col-md-6">
      <div class="panel panel-yellow">
        <div class="panel-heading">
          <div class="row">
            <div class="col-xs-3">
              <i class="fa fa-clock-o fa-5x"></i>
            </div>
            <div class="col-xs-9 text-right">
              <div class="huge">{{ statistics.avg_duration }}</div>
              <div>{{ text_avg_duration }} {{ text_days }}</div>
            </div>
          </div>
        </div>
        <div class="panel-footer">
          <span class="pull-left">{{ text_avg_duration }}</span>
          <span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
          <div class="clearfix"></div>
        </div>
      </div>
    </div>
    
    <div class="col-lg-3 col-md-6">
      <div class="panel panel-red">
        <div class="panel-heading">
          <div class="row">
            <div class="col-xs-3">
              <i class="fa fa-exchange fa-5x"></i>
            </div>
            <div class="col-xs-9 text-right">
              <div class="huge">{{ statistics.conversion_rate }}%</div>
              <div>{{ text_conversion_rate }}</div>
            </div>
          </div>
        </div>
        <div class="panel-footer">
          <span class="pull-left">{{ text_conversion_rate }}</span>
          <span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
          <div class="clearfix"></div>
        </div>
      </div>
    </div>
  </div>

  <!-- الرسوم البيانية -->
  <div class="row">
    <div class="col-md-6">
      <div class="panel panel-default">
        <div class="panel-heading">
          <h3 class="panel-title"><i class="fa fa-filter"></i> {{ text_stage_funnel }}</h3>
        </div>
        <div class="panel-body">
          <canvas id="stageFunnelChart" height="200"></canvas>
        </div>
      </div>
    </div>
    <div class="col-md-6">
      <div class="panel panel-default">
        <div class="panel-heading">
          <h3 class="panel-title"><i class="fa fa-pie-chart"></i> {{ text_health_pie }}</h3>
        </div>
        <div class="panel-body">
          <canvas id="healthPieChart" height="200"></canvas>
        </div>
      </div>
    </div>
  </div>

  <div class="panel panel-default">
    <div class="panel-heading">
      <h3 class="panel-title"><i class="fa fa-list"></i> {{ text_list }}</h3>
    </div>
    <div class="panel-body">
      <!-- فلاتر البحث -->
      <div class="well">
        <div class="row">
          <div class="col-sm-3">
            <div class="form-group">
              <label class="control-label" for="input-customer">{{ entry_filter_customer }}</label>
              <input type="text" name="filter_customer" value="{{ filter_customer }}" placeholder="{{ entry_filter_customer }}" id="input-customer" class="form-control" />
            </div>
          </div>
          <div class="col-sm-3">
            <div class="form-group">
              <label class="control-label" for="input-stage">{{ entry_filter_stage }}</label>
              <select name="filter_stage" id="input-stage" class="form-control">
                <option value="">{{ text_all }}</option>
                {% for key, value in stages %}
                <option value="{{ key }}"{% if filter_stage == key %} selected="selected"{% endif %}>{{ value }}</option>
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="col-sm-3">
            <div class="form-group">
              <label class="control-label" for="input-health">{{ entry_filter_health }}</label>
              <select name="filter_health" id="input-health" class="form-control">
                <option value="">{{ text_all }}</option>
                {% for key, value in health_levels %}
                <option value="{{ key }}"{% if filter_health == key %} selected="selected"{% endif %}>{{ value }}</option>
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="col-sm-3">
            <div class="form-group">
              <label class="control-label" for="input-touchpoint">{{ entry_filter_touchpoint }}</label>
              <select name="filter_touchpoint" id="input-touchpoint" class="form-control">
                <option value="">{{ text_all }}</option>
                {% for key, value in touchpoints %}
                <option value="{{ key }}"{% if filter_touchpoint == key %} selected="selected"{% endif %}>{{ value }}</option>
                {% endfor %}
              </select>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-sm-4">
            <div class="form-group">
              <label class="control-label" for="input-assigned-to">{{ entry_filter_assigned_to }}</label>
              <select name="filter_assigned_to" id="input-assigned-to" class="form-control">
                <option value="">{{ text_all }}</option>
                {% for user in users %}
                <option value="{{ user.user_id }}"{% if filter_assigned_to == user.user_id %} selected="selected"{% endif %}>{{ user.firstname }}</option>
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="col-sm-4">
            <div class="form-group">
              <label class="control-label" for="input-date-from">{{ entry_filter_date_from }}</label>
              <div class="input-group date">
                <input type="text" name="filter_date_from" value="{{ filter_date_from }}" placeholder="{{ entry_filter_date_from }}" data-date-format="YYYY-MM-DD" id="input-date-from" class="form-control" />
                <span class="input-group-btn">
                  <button type="button" class="btn btn-default"><i class="fa fa-calendar"></i></button>
                </span>
              </div>
            </div>
          </div>
          <div class="col-sm-4">
            <div class="form-group">
              <label class="control-label" for="input-date-to">{{ entry_filter_date_to }}</label>
              <div class="input-group date">
                <input type="text" name="filter_date_to" value="{{ filter_date_to }}" placeholder="{{ entry_filter_date_to }}" data-date-format="YYYY-MM-DD" id="input-date-to" class="form-control" />
                <span class="input-group-btn">
                  <button type="button" class="btn btn-default"><i class="fa fa-calendar"></i></button>
                </span>
              </div>
            </div>
          </div>
        </div>
        <div class="text-right">
          <button type="button" id="button-filter" class="btn btn-primary"><i class="fa fa-search"></i> {{ button_filter }}</button>
          <button type="button" id="button-clear" class="btn btn-default"><i class="fa fa-refresh"></i> {{ button_clear }}</button>
        </div>
      </div>

      <form action="{{ delete }}" method="post" enctype="multipart/form-data" id="form-journey">
        <div class="table-responsive">
          <table class="table table-bordered table-hover">
            <thead>
              <tr>
                <td style="width: 1px;" class="text-center"><input type="checkbox" onclick="$('input[name*=\'selected\']').prop('checked', this.checked);" /></td>
                <td class="text-left">{% if sort == 'customer_name' %}<a href="{{ sort_customer }}" class="{{ order|lower }}">{{ column_customer_name }}</a>{% else %}<a href="{{ sort_customer }}">{{ column_customer_name }}</a>{% endif %}</td>
                <td class="text-center">{{ column_current_stage }}</td>
                <td class="text-center">{{ column_total_touchpoints }}</td>
                <td class="text-center">{{ column_journey_duration }}</td>
                <td class="text-center">{{ column_journey_health }}</td>
                <td class="text-center">{{ column_conversion_probability }}</td>
                <td class="text-right">{{ column_total_value }}</td>
                <td class="text-center">{{ column_last_activity }}</td>
                <td class="text-left">{{ column_assigned_to }}</td>
                <td class="text-right">{{ column_action }}</td>
              </tr>
            </thead>
            <tbody>
              {% if journeys %}
              {% for journey in journeys %}
              <tr>
                <td class="text-center">{% if journey.selected %}<input type="checkbox" name="selected[]" value="{{ journey.journey_id }}" checked="checked" />{% else %}<input type="checkbox" name="selected[]" value="{{ journey.journey_id }}" />{% endif %}</td>
                <td class="text-left">
                  <strong>{{ journey.customer_name }}</strong><br>
                  <small class="text-muted">
                    <i class="fa fa-envelope"></i> {{ journey.email }}<br>
                    <i class="fa fa-phone"></i> {{ journey.phone }}
                  </small>
                </td>
                <td class="text-center">
                  <span class="label label-{{ journey.stage_class }}">{{ journey.current_stage_text }}</span>
                </td>
                <td class="text-center">
                  <span class="badge">{{ journey.total_touchpoints }}</span>
                </td>
                <td class="text-center">
                  {{ journey.journey_duration }}
                </td>
                <td class="text-center">
                  <span class="label label-{{ journey.health_class }}">{{ journey.journey_health_text }}</span>
                </td>
                <td class="text-center">
                  <div class="progress" style="margin-bottom: 0; width: 80px;">
                    <div class="progress-bar progress-bar-{{ journey.stage_class }}" role="progressbar" style="width: {{ journey.conversion_probability }}%">
                      {{ journey.conversion_probability }}%
                    </div>
                  </div>
                </td>
                <td class="text-right">
                  {% if journey.total_value > 0 %}
                    <strong>{{ journey.total_value }} {{ text_currency }}</strong>
                  {% else %}
                    <span class="text-muted">-</span>
                  {% endif %}
                </td>
                <td class="text-center">
                  {% if journey.last_activity %}
                    {{ journey.last_activity }}
                  {% else %}
                    <span class="text-muted">{{ text_no_activity }}</span>
                  {% endif %}
                </td>
                <td class="text-left">{{ journey.assigned_to }}</td>
                <td class="text-right">
                  <div class="btn-group">
                    <button type="button" class="btn btn-primary btn-sm dropdown-toggle" data-toggle="dropdown">
                      {{ button_action }} <span class="caret"></span>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-right">
                      <li><a href="{{ journey.view }}"><i class="fa fa-eye"></i> {{ button_view }}</a></li>
                      <li><a href="{{ journey.map }}"><i class="fa fa-map"></i> {{ button_map }}</a></li>
                      <li><a href="{{ journey.timeline }}"><i class="fa fa-clock-o"></i> {{ button_timeline }}</a></li>
                      <li class="divider"></li>
                      <li><a href="{{ journey.optimize }}"><i class="fa fa-magic"></i> {{ button_optimize }}</a></li>
                      <li><a href="javascript:void(0);" onclick="addTouchpoint({{ journey.journey_id }});"><i class="fa fa-plus"></i> {{ button_add_touchpoint }}</a></li>
                      <li><a href="javascript:void(0);" onclick="updateStage({{ journey.journey_id }});"><i class="fa fa-arrow-right"></i> {{ button_update_stage }}</a></li>
                    </ul>
                  </div>
                </td>
              </tr>
              {% endfor %}
              {% else %}
              <tr>
                <td class="text-center" colspan="11">{{ text_no_journeys }}</td>
              </tr>
              {% endif %}
            </tbody>
          </table>
        </div>
      </form>
      
      <div class="row">
        <div class="col-sm-6 text-left">{{ pagination }}</div>
        <div class="col-sm-6 text-right">{{ results }}</div>
      </div>
    </div>
  </div>
</div>

<!-- Modal إضافة نقطة لمس -->
<div class="modal fade" id="modal-add-touchpoint" tabindex="-1" role="dialog">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal"><span>&times;</span></button>
        <h4 class="modal-title">{{ button_add_touchpoint }}</h4>
      </div>
      <div class="modal-body">
        <form id="form-touchpoint">
          <input type="hidden" id="touchpoint-journey-id" name="journey_id" value="" />
          <div class="form-group">
            <label for="touchpoint-type">{{ entry_touchpoint_type }}</label>
            <select class="form-control" id="touchpoint-type" name="touchpoint_type" required>
              {% for key, value in touchpoints %}
              <option value="{{ key }}">{{ value }}</option>
              {% endfor %}
            </select>
          </div>
          <div class="form-group">
            <label for="activity-type">{{ entry_activity_type }}</label>
            <select class="form-control" id="activity-type" name="activity_type" required>
              <option value="email_open">{{ text_activity_email_open }}</option>
              <option value="email_click">{{ text_activity_email_click }}</option>
              <option value="website_visit">{{ text_activity_website_visit }}</option>
              <option value="phone_call">{{ text_activity_phone_call }}</option>
              <option value="meeting">{{ text_activity_meeting }}</option>
              <option value="purchase">{{ text_activity_purchase }}</option>
            </select>
          </div>
          <div class="form-group">
            <label for="touchpoint-description">{{ entry_description }}</label>
            <textarea class="form-control" id="touchpoint-description" name="description" rows="3" required></textarea>
          </div>
          <div class="form-group">
            <label for="engagement-value">{{ entry_engagement_value }}</label>
            <select class="form-control" id="engagement-value" name="engagement_value">
              <option value="1">{{ text_engagement_low }}</option>
              <option value="2" selected>{{ text_engagement_medium }}</option>
              <option value="3">{{ text_engagement_high }}</option>
            </select>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_cancel }}</button>
        <button type="button" class="btn btn-primary" onclick="saveTouchpoint();">{{ button_save }}</button>
      </div>
    </div>
  </div>
</div>

<!-- Modal تحديث المرحلة -->
<div class="modal fade" id="modal-update-stage" tabindex="-1" role="dialog">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal"><span>&times;</span></button>
        <h4 class="modal-title">{{ button_update_stage }}</h4>
      </div>
      <div class="modal-body">
        <form id="form-stage">
          <input type="hidden" id="stage-journey-id" name="journey_id" value="" />
          <div class="form-group">
            <label for="new-stage">{{ entry_new_stage }}</label>
            <select class="form-control" id="new-stage" name="new_stage" required>
              {% for key, value in stages %}
              <option value="{{ key }}">{{ value }}</option>
              {% endfor %}
            </select>
          </div>
          <div class="form-group">
            <label for="stage-notes">{{ entry_notes }}</label>
            <textarea class="form-control" id="stage-notes" name="notes" rows="3"></textarea>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_cancel }}</button>
        <button type="button" class="btn btn-primary" onclick="saveStageUpdate();">{{ button_save }}</button>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript">
// فلترة البيانات
$('#button-filter').on('click', function() {
  var url = 'index.php?route=crm/customer_journey&user_token={{ user_token }}';
  
  var filter_customer = $('input[name=\'filter_customer\']').val();
  if (filter_customer) {
    url += '&filter_customer=' + encodeURIComponent(filter_customer);
  }
  
  var filter_stage = $('select[name=\'filter_stage\']').val();
  if (filter_stage) {
    url += '&filter_stage=' + encodeURIComponent(filter_stage);
  }
  
  var filter_health = $('select[name=\'filter_health\']').val();
  if (filter_health) {
    url += '&filter_health=' + encodeURIComponent(filter_health);
  }
  
  var filter_touchpoint = $('select[name=\'filter_touchpoint\']').val();
  if (filter_touchpoint) {
    url += '&filter_touchpoint=' + encodeURIComponent(filter_touchpoint);
  }
  
  var filter_assigned_to = $('select[name=\'filter_assigned_to\']').val();
  if (filter_assigned_to) {
    url += '&filter_assigned_to=' + encodeURIComponent(filter_assigned_to);
  }
  
  var filter_date_from = $('input[name=\'filter_date_from\']').val();
  if (filter_date_from) {
    url += '&filter_date_from=' + encodeURIComponent(filter_date_from);
  }
  
  var filter_date_to = $('input[name=\'filter_date_to\']').val();
  if (filter_date_to) {
    url += '&filter_date_to=' + encodeURIComponent(filter_date_to);
  }
  
  location = url;
});

// مسح الفلاتر
$('#button-clear').on('click', function() {
  location = 'index.php?route=crm/customer_journey&user_token={{ user_token }}';
});

// إضافة نقطة لمس
function addTouchpoint(journey_id) {
  $('#touchpoint-journey-id').val(journey_id);
  $('#modal-add-touchpoint').modal('show');
}

function saveTouchpoint() {
  var formData = $('#form-touchpoint').serialize();
  
  $.ajax({
    url: 'index.php?route=crm/customer_journey/addTouchpoint&user_token={{ user_token }}',
    type: 'post',
    data: formData,
    dataType: 'json',
    beforeSend: function() {
      $('#modal-add-touchpoint').modal('hide');
    },
    success: function(json) {
      if (json['success']) {
        location.reload();
      }
      
      if (json['error']) {
        alert(json['error']);
      }
    }
  });
}

// تحديث المرحلة
function updateStage(journey_id) {
  $('#stage-journey-id').val(journey_id);
  $('#modal-update-stage').modal('show');
}

function saveStageUpdate() {
  var formData = $('#form-stage').serialize();
  
  $.ajax({
    url: 'index.php?route=crm/customer_journey/updateStage&user_token={{ user_token }}',
    type: 'post',
    data: formData,
    dataType: 'json',
    beforeSend: function() {
      $('#modal-update-stage').modal('hide');
    },
    success: function(json) {
      if (json['success']) {
        location.reload();
      }
      
      if (json['error']) {
        alert(json['error']);
      }
    }
  });
}

// تصدير البيانات
function exportData() {
  var url = 'index.php?route=crm/customer_journey/export&user_token={{ user_token }}';
  window.open(url, '_blank');
}

// تفعيل التلميحات
$('[data-toggle="tooltip"]').tooltip();

// تفعيل منتقي التاريخ
$('.input-group.date').datetimepicker({
  pickTime: false
});

// الرسوم البيانية
$(document).ready(function() {
  // رسم بياني قمع المراحل
  var ctx1 = document.getElementById('stageFunnelChart').getContext('2d');
  var stageFunnelChart = new Chart(ctx1, {
    type: 'bar',
    data: {
      labels: {{ charts.stage_funnel.labels|json_encode|raw }},
      datasets: [{
        label: '{{ text_customers }}',
        data: {{ charts.stage_funnel.data|json_encode|raw }},
        backgroundColor: [
          '#007bff',
          '#28a745',
          '#ffc107',
          '#dc3545',
          '#17a2b8',
          '#6f42c1'
        ]
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        y: {
          beginAtZero: true
        }
      }
    }
  });

  // رسم بياني دائري لصحة الرحلة
  var ctx2 = document.getElementById('healthPieChart').getContext('2d');
  var healthPieChart = new Chart(ctx2, {
    type: 'doughnut',
    data: {
      labels: {{ charts.health_pie.labels|json_encode|raw }},
      datasets: [{
        data: {{ charts.health_pie.data|json_encode|raw }},
        backgroundColor: [
          '#28a745',
          '#007bff',
          '#ffc107',
          '#dc3545'
        ]
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      legend: {
        position: 'bottom'
      }
    }
  });
});
</script>

{{ footer }}
