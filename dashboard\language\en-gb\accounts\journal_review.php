<?php
// Heading
$_['heading_title']                    = 'Journal Review & Approval';

// Text
$_['text_success']                     = 'Success: Journal review has been saved successfully!';
$_['text_success_approved']            = 'Journal entry approved successfully!';
$_['text_success_rejected']            = 'Journal entry rejected successfully!';
$_['text_success_returned']            = 'Journal entry returned for review successfully!';
$_['text_list']                        = 'Journal Review List';
$_['text_form']                        = 'Journal Review Form';
$_['text_add']                         = 'Add Review';
$_['text_edit']                        = 'Edit Review';
$_['text_view']                        = 'View Review';
$_['text_delete']                      = 'Delete Review';
$_['text_journal_review']              = 'Journal Review';
$_['text_loading']                     = 'Loading...';
$_['text_processing']                  = 'Processing...';

// Review Status
$_['text_pending_review']              = 'Pending Review';
$_['text_under_review']                = 'Under Review';
$_['text_approved']                    = 'Approved';
$_['text_rejected']                    = 'Rejected';
$_['text_returned']                    = 'Returned for Review';
$_['text_draft']                       = 'Draft';
$_['text_posted']                      = 'Posted';

// Review Actions
$_['text_approve']                     = 'Approve';
$_['text_reject']                      = 'Reject';
$_['text_return']                      = 'Return for Review';
$_['text_review']                      = 'Review';
$_['text_approve_journal']             = 'Approve Journal';
$_['text_reject_journal']              = 'Reject Journal';
$_['text_return_journal']              = 'Return Journal';

// Review Types
$_['text_financial_review']            = 'Financial Review';
$_['text_compliance_review']           = 'Compliance Review';
$_['text_audit_review']                = 'Audit Review';
$_['text_management_review']           = 'Management Review';

// Priority Levels
$_['text_high_priority']               = 'High Priority';
$_['text_medium_priority']             = 'Medium Priority';
$_['text_low_priority']                = 'Low Priority';
$_['text_urgent']                      = 'Urgent';

// Column
$_['column_journal_number']            = 'Journal Number';
$_['column_journal_date']              = 'Journal Date';
$_['column_description']               = 'Description';
$_['column_amount']                    = 'Amount';
$_['column_status']                    = 'Status';
$_['column_reviewer']                  = 'Reviewer';
$_['column_review_date']               = 'Review Date';
$_['column_priority']                  = 'Priority';
$_['column_created_by']                = 'Created By';
$_['column_created_date']              = 'Created Date';
$_['column_action']                    = 'Action';

// Entry
$_['entry_journal_id']                 = 'Journal ID';
$_['entry_reviewer']                   = 'Reviewer';
$_['entry_review_status']              = 'Review Status';
$_['entry_priority']                   = 'Priority';
$_['entry_review_notes']               = 'Review Notes';
$_['entry_approval_notes']             = 'Approval Notes';
$_['entry_rejection_reason']           = 'Rejection Reason';
$_['entry_return_reason']              = 'Return Reason';
$_['entry_review_date']                = 'Review Date';
$_['entry_due_date']                   = 'Due Date';
$_['entry_filter_status']              = 'Filter by Status';
$_['entry_filter_reviewer']            = 'Filter by Reviewer';
$_['entry_filter_priority']            = 'Filter by Priority';
$_['entry_filter_date_start']          = 'Date From';
$_['entry_filter_date_end']            = 'Date To';

// Button
$_['button_approve']                   = 'Approve';
$_['button_reject']                    = 'Reject';
$_['button_return']                    = 'Return';
$_['button_review']                    = 'Review';
$_['button_assign_reviewer']           = 'Assign Reviewer';
$_['button_bulk_approve']              = 'Bulk Approve';
$_['button_bulk_reject']               = 'Bulk Reject';
$_['button_export_review']             = 'Export Review';
$_['button_print_review']              = 'Print Review';
$_['button_save_and_approve']          = 'Save & Approve';
$_['button_save_and_reject']           = 'Save & Reject';

// Tab
$_['tab_general']                      = 'General';
$_['tab_review_details']               = 'Review Details';
$_['tab_approval_workflow']            = 'Approval Workflow';
$_['tab_audit_trail']                  = 'Audit Trail';
$_['tab_comments']                     = 'Comments';
$_['tab_attachments']                  = 'Attachments';
$_['tab_history']                      = 'History';

// Help
$_['help_review_notes']                = 'Reviewer notes about the journal entry';
$_['help_approval_notes']              = 'Notes for approval or rejection';
$_['help_priority']                    = 'Priority level for journal review';
$_['help_due_date']                    = 'Due date for review completion';

// Error
$_['error_warning']                    = 'Warning: Please check the form carefully for errors!';
$_['error_permission']                 = 'Warning: You do not have permission to access journal review!';
$_['error_journal_id_required']        = 'Journal ID is required!';
$_['error_reviewer_required']          = 'Reviewer is required!';
$_['error_review_notes_required']      = 'Review notes are required!';
$_['error_approval_notes_required']    = 'Approval notes are required!';
$_['error_rejection_reason_required']  = 'Rejection reason is required!';
$_['error_return_reason_required']     = 'Return reason is required!';
$_['error_journal_not_found']          = 'Journal entry not found!';
$_['error_already_reviewed']           = 'This journal has already been reviewed!';
$_['error_cannot_review_own']          = 'Cannot review journals you created yourself!';
$_['error_invalid_status']             = 'Invalid journal status for review!';

// Success
$_['success_review_saved']             = 'Review saved successfully!';
$_['success_journal_approved']         = 'Journal approved successfully!';
$_['success_journal_rejected']         = 'Journal rejected successfully!';
$_['success_journal_returned']         = 'Journal returned successfully!';
$_['success_reviewer_assigned']        = 'Reviewer assigned successfully!';
$_['success_bulk_approved']            = 'Selected journals approved successfully!';
$_['success_bulk_rejected']            = 'Selected journals rejected successfully!';

// Additional variables for template
$_['text_actions'] = 'Actions';
$_['text_add_review'] = 'Add Review';
$_['text_export_options'] = 'Export Options';
$_['text_review_filters'] = 'Review Filters';
$_['text_all_statuses'] = 'All Statuses';
$_['text_all_reviewers'] = 'All Reviewers';
$_['text_all_priorities'] = 'All Priorities';
$_['text_active'] = 'Active';
$_['text_inactive'] = 'Inactive';
$_['text_total_reviews'] = 'Total Reviews';
$_['text_pending_reviews'] = 'Pending Reviews';
$_['text_completed_reviews'] = 'Completed Reviews';
$_['text_reviews'] = 'Reviews';
$_['text_reviews_list'] = 'Reviews List';
$_['text_no_reviews'] = 'No reviews found';
$_['text_exporting'] = 'Exporting';
$_['text_loading'] = 'Loading';
$_['button_filter'] = 'Filter';
$_['text_journal_details'] = 'Journal Details';
$_['text_review_workflow'] = 'Review Workflow';
$_['text_approval_history'] = 'Approval History';
$_['text_reviewer_comments'] = 'Reviewer Comments';
$_['text_management_approval'] = 'Management Approval';
$_['text_financial_approval'] = 'Financial Approval';
$_['text_compliance_check'] = 'Compliance Check';
$_['text_audit_verification'] = 'Audit Verification';
$_['text_auto_approval'] = 'Auto Approval';
$_['text_manual_approval'] = 'Manual Approval';
$_['text_escalation'] = 'Escalation';
$_['text_delegation'] = 'Delegation';
$_['text_notification'] = 'Notification';
$_['text_reminder'] = 'Reminder';
$_['text_overdue'] = 'Overdue';
$_['text_on_time'] = 'On Time';
$_['text_early'] = 'Early';

// Workflow Status
$_['text_workflow_pending'] = 'Workflow Pending';
$_['text_workflow_in_progress'] = 'Workflow In Progress';
$_['text_workflow_completed'] = 'Workflow Completed';
$_['text_workflow_cancelled'] = 'Workflow Cancelled';

// Review Levels
$_['text_level_1_review'] = 'Level 1 Review';
$_['text_level_2_review'] = 'Level 2 Review';
$_['text_level_3_review'] = 'Level 3 Review';
$_['text_final_approval'] = 'Final Approval';

// Risk Assessment
$_['text_low_risk'] = 'Low Risk';
$_['text_medium_risk'] = 'Medium Risk';
$_['text_high_risk'] = 'High Risk';
$_['text_critical_risk'] = 'Critical Risk';

// Compliance Status
$_['text_compliant'] = 'Compliant';
$_['text_non_compliant'] = 'Non-Compliant';
$_['text_partially_compliant'] = 'Partially Compliant';
$_['text_under_review_compliance'] = 'Under Compliance Review';
?>
