{{ header }}
{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="button" id="button-check-rankings" data-toggle="tooltip" title="{{ button_check_rankings }}" class="btn btn-info"><i class="fa fa-refresh"></i></button>
        <a href="{{ add }}" data-toggle="tooltip" title="{{ button_add }}" class="btn btn-primary"><i class="fa fa-plus"></i></a>
        <button type="button" data-toggle="tooltip" title="{{ button_delete }}" class="btn btn-danger" onclick="confirm('{{ text_confirm }}') ? $('#form-keyword').submit() : false;"><i class="fa fa-trash-o"></i></button>
        <a href="{{ back }}" data-toggle="tooltip" title="{{ button_back }}" class="btn btn-default"><i class="fa fa-reply"></i></a>
      </div>
      <h1><i class="fa fa-line-chart"></i> {{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    {% if success %}
    <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-list"></i> {{ text_keyword_list }}</h3>
      </div>
      <div class="panel-body">
        <div class="well">
          <div class="row">
            <div class="col-sm-4">
              <div class="form-group">
                <label class="control-label" for="input-keyword">{{ column_keyword }}</label>
                <input type="text" name="filter_keyword" value="{{ filter_keyword }}" placeholder="{{ column_keyword }}" id="input-keyword" class="form-control" />
              </div>
            </div>
            <div class="col-sm-4">
              <div class="form-group">
                <label class="control-label" for="input-search-engine">{{ column_search_engine }}</label>
                <select name="filter_search_engine" id="input-search-engine" class="form-control">
                  <option value=""></option>
                  <option value="google" {% if filter_search_engine == 'google' %}selected="selected"{% endif %}>Google</option>
                  <option value="bing" {% if filter_search_engine == 'bing' %}selected="selected"{% endif %}>Bing</option>
                  <option value="yahoo" {% if filter_search_engine == 'yahoo' %}selected="selected"{% endif %}>Yahoo</option>
                  <option value="yandex" {% if filter_search_engine == 'yandex' %}selected="selected"{% endif %}>Yandex</option>
                </select>
              </div>
            </div>
            <div class="col-sm-4">
              <div class="form-group">
                <label class="control-label" for="input-status">{{ column_status }}</label>
                <select name="filter_status" id="input-status" class="form-control">
                  <option value=""></option>
                  <option value="improved" {% if filter_status == 'improved' %}selected="selected"{% endif %}>{{ text_status_improved }}</option>
                  <option value="declined" {% if filter_status == 'declined' %}selected="selected"{% endif %}>{{ text_status_declined }}</option>
                  <option value="unchanged" {% if filter_status == 'unchanged' %}selected="selected"{% endif %}>{{ text_status_unchanged }}</option>
                  <option value="new" {% if filter_status == 'new' %}selected="selected"{% endif %}>{{ text_status_new }}</option>
                </select>
              </div>
              <button type="button" id="button-filter" class="btn btn-primary pull-right"><i class="fa fa-filter"></i> {{ button_filter }}</button>
            </div>
          </div>
        </div>
        <form action="{{ delete }}" method="post" enctype="multipart/form-data" id="form-keyword">
          <div class="table-responsive">
            <table class="table table-bordered table-hover">
              <thead>
                <tr>
                  <td style="width: 1px;" class="text-center"><input type="checkbox" onclick="$('input[name*=\'selected\']').prop('checked', this.checked);" /></td>
                  <td class="text-left">{% if sort == 'keyword' %}<a href="{{ sort_keyword }}" class="{{ order|lower }}">{{ column_keyword }}</a>{% else %}<a href="{{ sort_keyword }}">{{ column_keyword }}</a>{% endif %}</td>
                  <td class="text-left">{% if sort == 'search_engine' %}<a href="{{ sort_search_engine }}" class="{{ order|lower }}">{{ column_search_engine }}</a>{% else %}<a href="{{ sort_search_engine }}">{{ column_search_engine }}</a>{% endif %}</td>
                  <td class="text-center">{% if sort == 'position' %}<a href="{{ sort_position }}" class="{{ order|lower }}">{{ column_position }}</a>{% else %}<a href="{{ sort_position }}">{{ column_position }}</a>{% endif %}</td>
                  <td class="text-center">{{ column_previous_position }}</td>
                  <td class="text-left">{{ column_url }}</td>
                  <td class="text-left">{% if sort == 'last_checked' %}<a href="{{ sort_last_checked }}" class="{{ order|lower }}">{{ column_last_checked }}</a>{% else %}<a href="{{ sort_last_checked }}">{{ column_last_checked }}</a>{% endif %}</td>
                  <td class="text-center">{% if sort == 'status' %}<a href="{{ sort_status }}" class="{{ order|lower }}">{{ column_status }}</a>{% else %}<a href="{{ sort_status }}">{{ column_status }}</a>{% endif %}</td>
                  <td class="text-right">{{ column_action }}</td>
                </tr>
              </thead>
              <tbody>
                {% if keyword_trackings %}
                {% for tracking in keyword_trackings %}
                <tr>
                  <td class="text-center">{% if tracking.tracking_id in selected %}
                    <input type="checkbox" name="selected[]" value="{{ tracking.tracking_id }}" checked="checked" />
                    {% else %}
                    <input type="checkbox" name="selected[]" value="{{ tracking.tracking_id }}" />
                    {% endif %}</td>
                  <td class="text-left">{{ tracking.keyword }}</td>
                  <td class="text-left">{{ tracking.search_engine }}</td>
                  <td class="text-center">{{ tracking.position }}</td>
                  <td class="text-center">
                    {% if tracking.previous_position %}
                      {% if tracking.previous_position > tracking.position %}
                        <span class="text-success"><i class="fa fa-arrow-up"></i> {{ tracking.previous_position }}</span>
                      {% elseif tracking.previous_position < tracking.position %}
                        <span class="text-danger"><i class="fa fa-arrow-down"></i> {{ tracking.previous_position }}</span>
                      {% else %}
                        <span class="text-info">{{ tracking.previous_position }}</span>
                      {% endif %}
                    {% else %}
                      <span class="text-muted">-</span>
                    {% endif %}
                  </td>
                  <td class="text-left">{{ tracking.url }}</td>
                  <td class="text-left">{{ tracking.last_checked }}</td>
                  <td class="text-center"><span class="label label-{{ tracking.status_class }}">{{ tracking.status }}</span></td>
                  <td class="text-right">
                    <a href="{{ tracking.edit }}" data-toggle="tooltip" title="{{ button_edit }}" class="btn btn-primary"><i class="fa fa-pencil"></i></a>
                  </td>
                </tr>
                {% endfor %}
                {% else %}
                <tr>
                  <td class="text-center" colspan="9">{{ text_no_results }}</td>
                </tr>
                {% endif %}
              </tbody>
            </table>
          </div>
        </form>
        <div class="row">
          <div class="col-sm-6 text-left">{{ pagination }}</div>
          <div class="col-sm-6 text-right">{{ results }}</div>
        </div>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript">
$('#button-filter').on('click', function() {
	var url = 'index.php?route=catalog/seo/keywordTrackings&user_token={{ user_token }}';

	var filter_keyword = $('input[name=\'filter_keyword\']').val();
	if (filter_keyword) {
		url += '&filter_keyword=' + encodeURIComponent(filter_keyword);
	}

	var filter_search_engine = $('select[name=\'filter_search_engine\']').val();
	if (filter_search_engine) {
		url += '&filter_search_engine=' + encodeURIComponent(filter_search_engine);
	}

	var filter_status = $('select[name=\'filter_status\']').val();
	if (filter_status) {
		url += '&filter_status=' + encodeURIComponent(filter_status);
	}

	location = url;
});

// Reload ranking data
$('#button-check-rankings').on('click', function() {
    var $btn = $(this);
    $btn.button('loading');
    
    $.ajax({
        url: '{{ check_rankings }}',
        dataType: 'json',
        beforeSend: function() {
            $btn.button('loading');
        },
        complete: function() {
            $btn.button('reset');
        },
        success: function(json) {
            if (json['success']) {
                location.reload();
            }
        },
        error: function(xhr, ajaxOptions, thrownError) {
            alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
            $btn.button('reset');
        }
    });
});
</script>
<style>
/* Estilos generales para el módulo SEO */
.seo-navigation {
    margin-bottom: 30px;
}

.seo-navigation .panel-body {
    padding: 15px;
    transition: all 0.3s ease;
}

.seo-navigation .panel-body:hover {
    background-color: #f8f8f8;
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
}

.seo-navigation .btn-link {
    color: #333;
    text-decoration: none;
    padding: 15px 0;
}

.seo-navigation i.fa {
    margin-bottom: 10px;
    color: #23a1d1;
}

/* Tarjetas de estadísticas */
.panel-primary, .panel-green, .panel-red, .panel-yellow {
    border-color: #ddd;
}

.panel-primary .panel-heading {
    background-color: #337ab7;
    border-color: #337ab7;
    color: #fff;
}

.panel-green .panel-heading {
    background-color: #5cb85c;
    border-color: #5cb85c;
    color: #fff;
}

.panel-red .panel-heading {
    background-color: #d9534f;
    border-color: #d9534f;
    color: #fff;
}

.panel-yellow .panel-heading {
    background-color: #f0ad4e;
    border-color: #f0ad4e;
    color: #fff;
}

.panel-footer {
    color: #333;
}

.panel .huge {
    font-size: 40px;
    line-height: 1;
    margin-bottom: 5px;
}

/* Barras de progreso */
.progress {
    margin-bottom: 10px;
    height: 20px;
}

.progress-bar {
    line-height: 20px;
    font-size: 12px;
}

.progress-bar-success {
    background-color: #5cb85c;
}

.progress-bar-warning {
    background-color: #f0ad4e;
}

.progress-bar-danger {
    background-color: #d9534f;
}

/* Lista de sugerencias */
.seo-suggestions-list {
    padding-left: 15px;
    list-style-type: none;
}

.seo-suggestions-list li {
    padding: 5px 0;
    border-bottom: 1px dotted #eee;
}

.seo-suggestions-list i {
    margin-right: 8px;
}

/* Estilos para estados */
.label-improved {
    background-color: #5cb85c;
}

.label-declined {
    background-color: #d9534f;
}

.label-unchanged {
    background-color: #5bc0de;
}

.label-new {
    background-color: #f0ad4e;
}

/* Indicadores de cambio en posiciones */
.text-success .fa, 
.text-danger .fa {
    margin-right: 3px;
}

/* Formularios de análisis */
.analysis-results-container {
    margin-top: 20px;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #f9f9f9;
}

/* Responsive */
@media (max-width: 768px) {
    .panel .huge {
        font-size: 30px;
    }
    
    .form-group [class*='col-xs-'] {
        margin-bottom: 15px;
    }
}

/* Animaciones */
.fade-in {
    animation: fadeIn ease 0.5s;
    -webkit-animation: fadeIn ease 0.5s;
    -moz-animation: fadeIn ease 0.5s;
    -o-animation: fadeIn ease 0.5s;
    -ms-animation: fadeIn ease 0.5s;
}

@keyframes fadeIn {
    0% {opacity:0;}
    100% {opacity:1;}
}

@-moz-keyframes fadeIn {
    0% {opacity:0;}
    100% {opacity:1;}
}

@-webkit-keyframes fadeIn {
    0% {opacity:0;}
    100% {opacity:1;}
}

@-o-keyframes fadeIn {
    0% {opacity:0;}
    100% {opacity:1;}
}

@-ms-keyframes fadeIn {
    0% {opacity:0;}
    100% {opacity:1;}
}    
</style>
{{ footer }}