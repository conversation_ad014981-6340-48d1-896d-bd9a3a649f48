{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <a href="{{{{ edit }}}}" data-toggle="tooltip" title="{{{{ button_edit }}}}" class="btn btn-primary"><i class="fa fa-pencil"></i></a>
        <a href="{{{{ cancel }}}}" data-toggle="tooltip" title="{{{{ button_back }}}}" class="btn btn-default"><i class="fa fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="close" data-dismiss="alert">&times;</button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="close" data-dismiss="alert">&times;</button>
      </div>
    {{% endif %}}
    
    <div class="panel panel-default">
      <div class="panel-heading"><i class="fa fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}bar-chart{% elif screen_type == 'settings' %}cog{% else %}pencil{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="panel-body">
        <div class="row">
          <div class="col-md-4">
            <div class="panel panel-default">
              <div class="panel-heading">{{ text_summary }}</div>
              <div class="panel-body">
                <table class="table">
                  <tr>
                    <td>{{ text_id }}:</td>
                    <td>{{ accounts\journal_review_id }}</td>
                  </tr>
                  <tr>
                    <td>{{ text_reject }}:</td>
                    <td>{{ reject }}</td>
                  </tr>
                  <tr>
                    <td>{{ text_heading_title }}:</td>
                    <td>{{ heading_title }}</td>
                  </tr>
                  <tr>
                    <td>{{ text_filter_date_start }}:</td>
                    <td>{{ filter_date_start }}</td>
                  </tr>
                  <tr>
                    <td>{{ text_sort_date_added }}:</td>
                    <td>{{ sort_date_added }}</td>
                  </tr>
                  <tr>
                    <td>{{ text_sort_description }}:</td>
                    <td>{{ sort_description }}</td>
                  </tr>
                  <tr>
                    <td>{{ text_order }}:</td>
                    <td>{{ order }}</td>
                  </tr>
                  <tr>
                    <td>{{ text_sort }}:</td>
                    <td>{{ sort }}</td>
                  </tr>
                  <tr>
                    <td>{{ text_sort_journal_number }}:</td>
                    <td>{{ sort_journal_number }}</td>
                  </tr>
                </table>
              </div>
            </div>
          </div>
          
          <div class="col-md-8">
            <div class="panel panel-default">
              <div class="panel-heading">{{{{ text_details }}}}</div>
              <div class="panel-body">
                <div class="mb-3">
                  <h5>{{{{ text_description }}}}</h5>
                  <p>{{{{ description }}}}</p>
                </div>
                
                {{% if history %}}
                <div class="mb-3">
                  <h5>{{{{ text_history }}}}</h5>
                  <table class="table table-bordered">
                    <thead>
                      <tr>
                        <td>{{{{ column_date_added }}}}</td>
                        <td>{{{{ column_status }}}}</td>
                        <td>{{{{ column_comment }}}}</td>
                      </tr>
                    </thead>
                    <tbody>
                      {{% for history_item in history %}}
                      <tr>
                        <td>{{{{ history_item.date_added }}}}</td>
                        <td>{{{{ history_item.status }}}}</td>
                        <td>{{{{ history_item.comment }}}}</td>
                      </tr>
                      {{% endfor %}}
                    </tbody>
                  </table>
                </div>
                {{% endif %}}
              </div>
            </div>
          </div>
        </div>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}