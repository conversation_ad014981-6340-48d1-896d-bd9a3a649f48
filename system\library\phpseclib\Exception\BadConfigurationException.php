<?php

/**
 * BadConfigurationException
 *
 * PHP version 5
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2015 <PERSON>
 * @license   http://www.opensource.org/licenses/mit-license.html  MIT License
 * @link      http://phpseclib.sourceforge.net
 */

declare(strict_types=1);

namespace phpseclib3\Exception;

/**
 * BadConfigurationException
 *
 * <AUTHOR> <<EMAIL>>
 */
class BadConfigurationException extends \RuntimeException implements ExceptionInterface
{
}
