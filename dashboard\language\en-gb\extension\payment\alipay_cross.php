<?php
// Heading
$_['heading_title']                  = 'Alipay Cross-border';

// Text
$_['text_extension']                 = 'Extensions';
$_['text_success']                   = 'Success: You have modified Alipay account details!';
$_['text_edit']                      = 'Edit Alipay Pay';
$_['text_alipay_cross']              = '<a target="_BLANK" href="https://global.alipay.com"><img src="view/image/payment/alipay-cross-border.png" alt="Alipay Pay Website" title="Alipay Pay Website" style="border: 1px solid #EEEEEE;" /></a>';
$_['text_live']                      = 'Live';
$_['text_sandbox']                   = 'Sandbox';

// Entry
$_['entry_app_id']                   = 'Partner ID';
$_['entry_merchant_private_key']     = 'Key';
$_['entry_test']                     = 'Test mode';
$_['entry_total']                    = 'Total';
$_['entry_currency']                 = 'Currency Code';
$_['entry_order_status']             = 'Completed Status';
$_['entry_geo_zone']                 = 'Geo Zone';
$_['entry_status']                   = 'Status';
$_['entry_sort_order']               = 'Sort Order';

// Help
$_['help_total']                     = 'The checkout total the order must reach before this payment method becomes active';
$_['help_currency']                  = 'The settlement currency code the merchant specifies in the contract. You can add a new currency in System-&gt;Localisation-&gt;Currency if your settlement currency is not in the list';
$_['help_alipay_setup']              = '<a target="_blank" href="http://www.opencart.cn/docs/alipay">Click here</a> to learn how to set up Alipay account.';

// Error
$_['error_permission']               = 'Warning: You do not have permission to modify pay Alipay!';
$_['error_app_id']                   = 'Partner ID required!';
$_['error_merchant_private_key']     = 'Key required!';
