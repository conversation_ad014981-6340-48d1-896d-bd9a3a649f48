<?php
// Heading
$_['heading_title']                 = 'Inventory Movement History';

// Text
$_['text_success']                  = 'Success: You have modified inventory movement history!';
$_['text_list']                     = 'Inventory Movement List';
$_['text_movement_history']         = 'Movement History';
$_['text_all_movement_history']     = 'All Movement History';
$_['text_filter']                   = 'Filter';
$_['text_date_range']               = 'Date Range';
$_['text_all_types']                = '-- All Types --';
$_['text_all_references']           = '-- All References --';
$_['text_in']                       = 'In';
$_['text_out']                      = 'Out';
$_['text_purchase']                 = 'Purchase';
$_['text_sale']                     = 'Sale';
$_['text_adjustment']               = 'Adjustment';
$_['text_transfer']                 = 'Transfer';
$_['text_return']                   = 'Return';
$_['text_production']               = 'Production';
$_['text_no_results']               = 'No inventory movements to display!';
$_['text_confirm']                  = 'Are you sure?';
$_['text_movement_details']         = 'Movement Details';
$_['text_reference_details']        = 'Reference Details';
$_['text_pagination']               = 'Showing %d to %d of %d (%d Pages)';
$_['text_loading']                  = 'Loading...';

// Column
$_['column_date']                   = 'Date';
$_['column_product']                = 'Product';
$_['column_warehouse']              = 'Warehouse';
$_['column_unit']                   = 'Unit';
$_['column_quantity']               = 'Quantity';
$_['column_movement_type']          = 'Movement Type';
$_['column_reference']              = 'Reference';
$_['column_cost']                   = 'Cost';
$_['column_notes']                  = 'Notes';
$_['column_user']                   = 'User';
$_['column_action']                 = 'Action';

// Entry
$_['entry_product']                 = 'Product';
$_['entry_branch']                  = 'Branch';
$_['entry_movement_type']           = 'Movement Type';
$_['entry_reference_type']          = 'Reference Type';
$_['entry_date_start']              = 'Date Start';
$_['entry_date_end']                = 'Date End';

// Button
$_['button_filter']                 = 'Filter';
$_['button_export']                 = 'Export to Excel';
$_['button_view']                   = 'View';
$_['button_close']                  = 'Close';

// Error
$_['error_permission']              = 'Warning: You do not have permission to modify inventory movement history!';
$_['error_movement_id']             = 'Movement ID required!';
$_['error_movement_not_found']      = 'Requested movement not found!';

// Date Format
$_['datetime_format']               = 'Y-m-d H:i:s';
