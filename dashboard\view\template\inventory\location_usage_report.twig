{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="button" data-toggle="tooltip" title="تحديث التقرير" class="btn btn-primary" onclick="refreshReport()">
          <i class="fa fa-refresh"></i>
        </button>
        <button type="button" data-toggle="tooltip" title="تصدير PDF" class="btn btn-danger" onclick="exportPDF()">
          <i class="fa fa-file-pdf-o"></i>
        </button>
        <button type="button" data-toggle="tooltip" title="تصدير Excel" class="btn btn-success" onclick="exportExcel()">
          <i class="fa fa-file-excel-o"></i>
        </button>
        <button type="button" data-toggle="tooltip" title="طباعة" class="btn btn-info" onclick="printReport()">
          <i class="fa fa-print"></i>
        </button>
        <a href="{{ back }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default">
          <i class="fa fa-reply"></i>
        </a>
      </div>
      <h1>{{ heading_title }} - {{ text_usage_report }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>

  <div class="container-fluid">
    <!-- فلاتر التقرير -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title">
          <i class="fa fa-filter"></i> فلاتر التقرير
          <button type="button" class="btn btn-xs btn-default pull-right" data-toggle="collapse" data-target="#filter-panel">
            <i class="fa fa-chevron-down"></i>
          </button>
        </h3>
      </div>
      <div id="filter-panel" class="panel-collapse collapse in">
        <div class="panel-body">
          <form id="report-filters">
            <div class="row">
              <div class="col-md-3">
                <div class="form-group">
                  <label>الفترة الزمنية:</label>
                  <select id="period-filter" class="form-control" onchange="updateDateRange()">
                    <option value="today">اليوم</option>
                    <option value="week">هذا الأسبوع</option>
                    <option value="month" selected>هذا الشهر</option>
                    <option value="quarter">هذا الربع</option>
                    <option value="year">هذا العام</option>
                    <option value="custom">فترة مخصصة</option>
                  </select>
                </div>
              </div>

              <div class="col-md-3">
                <div class="form-group">
                  <label>من تاريخ:</label>
                  <input type="date" id="date-from" class="form-control" />
                </div>
              </div>

              <div class="col-md-3">
                <div class="form-group">
                  <label>إلى تاريخ:</label>
                  <input type="date" id="date-to" class="form-control" />
                </div>
              </div>

              <div class="col-md-3">
                <div class="form-group">
                  <label>نوع الموقع:</label>
                  <select id="location-type-filter" class="form-control">
                    <option value="">جميع الأنواع</option>
                    <option value="warehouse">مستودع</option>
                    <option value="zone">منطقة</option>
                    <option value="aisle">ممر</option>
                    <option value="rack">رف</option>
                    <option value="shelf">رفة</option>
                    <option value="bin">صندوق</option>
                  </select>
                </div>
              </div>
            </div>

            <div class="row">
              <div class="col-md-3">
                <div class="form-group">
                  <label>الفرع:</label>
                  <select id="branch-filter" class="form-control">
                    <option value="">جميع الفروع</option>
                    {% for branch in branches %}
                    <option value="{{ branch.branch_id }}">{{ branch.name }}</option>
                    {% endfor %}
                  </select>
                </div>
              </div>

              <div class="col-md-3">
                <div class="form-group">
                  <label>المستودع:</label>
                  <select id="warehouse-filter" class="form-control">
                    <option value="">جميع المستودعات</option>
                    {% for warehouse in warehouses %}
                    <option value="{{ warehouse.warehouse_id }}">{{ warehouse.name }}</option>
                    {% endfor %}
                  </select>
                </div>
              </div>

              <div class="col-md-3">
                <div class="form-group">
                  <label>حالة الإشغال:</label>
                  <select id="occupancy-filter" class="form-control">
                    <option value="">جميع الحالات</option>
                    <option value="empty">فارغ (0%)</option>
                    <option value="low">منخفض (1-25%)</option>
                    <option value="medium">متوسط (26-75%)</option>
                    <option value="high">عالي (76-99%)</option>
                    <option value="full">ممتلئ (100%)</option>
                  </select>
                </div>
              </div>

              <div class="col-md-3">
                <div class="form-group">
                  <label>&nbsp;</label>
                  <div>
                    <button type="button" class="btn btn-primary" onclick="generateReport()">
                      <i class="fa fa-bar-chart"></i> إنشاء التقرير
                    </button>
                    <button type="button" class="btn btn-default" onclick="resetFilters()">
                      <i class="fa fa-refresh"></i> إعادة تعيين
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- ملخص الإحصائيات -->
    <div class="row">
      <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="panel panel-primary">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-map-marker fa-2x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge" id="total-locations">{{ statistics.total_locations }}</div>
                <div>إجمالي المواقع</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="panel panel-success">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-check-circle fa-2x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge" id="active-locations">{{ statistics.active_locations }}</div>
                <div>المواقع النشطة</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="panel panel-info">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-cubes fa-2x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge" id="occupied-locations">{{ statistics.occupied_locations }}</div>
                <div>المواقع المشغولة</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="panel panel-warning">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-pie-chart fa-2x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge" id="usage-percentage">{{ statistics.overall_usage }}%</div>
                <div>نسبة الاستخدام</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="panel panel-green">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-exchange fa-2x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge" id="total-movements">{{ statistics.total_movements }}</div>
                <div>إجمالي الحركات</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="panel panel-purple">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-dollar fa-2x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge" id="total-value">{{ statistics.total_value|number_format(0) }}</div>
                <div>إجمالي القيمة</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- الرسوم البيانية -->
    <div class="row">
      <div class="col-md-6">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title">
              <i class="fa fa-pie-chart"></i> توزيع المواقع حسب النوع
            </h3>
          </div>
          <div class="panel-body">
            <canvas id="location-types-chart" width="400" height="300"></canvas>
          </div>
        </div>
      </div>

      <div class="col-md-6">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title">
              <i class="fa fa-bar-chart"></i> نسب الإشغال
            </h3>
          </div>
          <div class="panel-body">
            <canvas id="occupancy-chart" width="400" height="300"></canvas>
          </div>
        </div>
      </div>
    </div>

    <div class="row">
      <div class="col-md-12">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title">
              <i class="fa fa-line-chart"></i> اتجاه الاستخدام خلال الفترة
            </h3>
          </div>
          <div class="panel-body">
            <canvas id="usage-trend-chart" width="800" height="400"></canvas>
          </div>
        </div>
      </div>
    </div>

    <!-- جداول التفاصيل -->
    <div class="row">
      <div class="col-md-6">
        <div class="panel panel-success">
          <div class="panel-heading">
            <h3 class="panel-title">
              <i class="fa fa-trophy"></i> أكثر المواقع استخداماً
            </h3>
          </div>
          <div class="panel-body">
            <div class="table-responsive">
              <table class="table table-striped table-condensed">
                <thead>
                  <tr>
                    <th>الترتيب</th>
                    <th>الموقع</th>
                    <th>نسبة الاستخدام</th>
                    <th>الحركات</th>
                  </tr>
                </thead>
                <tbody id="most-used-locations">
                  {% for location in most_used_locations %}
                  <tr>
                    <td>{{ loop.index }}</td>
                    <td>
                      <strong>{{ location.name }}</strong><br>
                      <small class="text-muted">{{ location.location_code }}</small>
                    </td>
                    <td>
                      <div class="progress progress-xs">
                        <div class="progress-bar progress-bar-success" style="width: {{ location.usage_percentage }}%"></div>
                      </div>
                      {{ location.usage_percentage }}%
                    </td>
                    <td>{{ location.movements_count }}</td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      <div class="col-md-6">
        <div class="panel panel-warning">
          <div class="panel-heading">
            <h3 class="panel-title">
              <i class="fa fa-exclamation-triangle"></i> المواقع التي تحتاج انتباه
            </h3>
          </div>
          <div class="panel-body">
            <div class="table-responsive">
              <table class="table table-striped table-condensed">
                <thead>
                  <tr>
                    <th>الموقع</th>
                    <th>المشكلة</th>
                    <th>الحالة</th>
                    <th>إجراء</th>
                  </tr>
                </thead>
                <tbody id="attention-locations">
                  {% for location in attention_locations %}
                  <tr>
                    <td>
                      <strong>{{ location.name }}</strong><br>
                      <small class="text-muted">{{ location.location_code }}</small>
                    </td>
                    <td>{{ location.issue }}</td>
                    <td>
                      <span class="label label-{{ location.status_class }}">{{ location.status_text }}</span>
                    </td>
                    <td>
                      <a href="{{ location.view_url }}" class="btn btn-xs btn-info">
                        <i class="fa fa-eye"></i>
                      </a>
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- تحليل الكفاءة -->
    <div class="row">
      <div class="col-md-12">
        <div class="panel panel-info">
          <div class="panel-heading">
            <h3 class="panel-title">
              <i class="fa fa-lightbulb-o"></i> تحليل الكفاءة واقتراحات التحسين
            </h3>
          </div>
          <div class="panel-body">
            <div id="efficiency-analysis">
              <div class="row">
                <div class="col-md-4">
                  <div class="efficiency-metric">
                    <h4><i class="fa fa-tachometer text-primary"></i> كفاءة الاستخدام</h4>
                    <div class="progress">
                      <div class="progress-bar progress-bar-primary" style="width: {{ efficiency.usage_efficiency }}%">
                        {{ efficiency.usage_efficiency }}%
                      </div>
                    </div>
                    <p class="text-muted">{{ efficiency.usage_description }}</p>
                  </div>
                </div>

                <div class="col-md-4">
                  <div class="efficiency-metric">
                    <h4><i class="fa fa-random text-success"></i> كفاءة التوزيع</h4>
                    <div class="progress">
                      <div class="progress-bar progress-bar-success" style="width: {{ efficiency.distribution_efficiency }}%">
                        {{ efficiency.distribution_efficiency }}%
                      </div>
                    </div>
                    <p class="text-muted">{{ efficiency.distribution_description }}</p>
                  </div>
                </div>

                <div class="col-md-4">
                  <div class="efficiency-metric">
                    <h4><i class="fa fa-clock-o text-warning"></i> كفاءة الوقت</h4>
                    <div class="progress">
                      <div class="progress-bar progress-bar-warning" style="width: {{ efficiency.time_efficiency }}%">
                        {{ efficiency.time_efficiency }}%
                      </div>
                    </div>
                    <p class="text-muted">{{ efficiency.time_description }}</p>
                  </div>
                </div>
              </div>

              <hr>

              <div class="row">
                <div class="col-md-12">
                  <h5><i class="fa fa-lightbulb-o"></i> اقتراحات التحسين:</h5>
                  <ul class="list-unstyled" id="improvement-suggestions">
                    {% for suggestion in improvement_suggestions %}
                    <li class="suggestion-item">
                      <i class="fa fa-{{ suggestion.icon }} text-{{ suggestion.type }}"></i>
                      <strong>{{ suggestion.title }}:</strong> {{ suggestion.description }}
                    </li>
                    {% endfor %}
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
.huge {
  font-size: 28px;
  font-weight: bold;
}

.panel-green {
  border-color: #5cb85c;
}
.panel-green > .panel-heading {
  border-color: #5cb85c;
  color: white;
  background-color: #5cb85c;
}

.panel-purple {
  border-color: #9b59b6;
}
.panel-purple > .panel-heading {
  border-color: #9b59b6;
  color: white;
  background-color: #9b59b6;
}

.progress-xs {
  height: 4px;
  margin-bottom: 2px;
}

.efficiency-metric {
  text-align: center;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-bottom: 15px;
}

.efficiency-metric h4 {
  margin-bottom: 15px;
}

.suggestion-item {
  padding: 8px 0;
  border-bottom: 1px solid #eee;
}

.suggestion-item:last-child {
  border-bottom: none;
}

.suggestion-item i {
  margin-right: 8px;
  width: 20px;
}

@media print {
  .page-header,
  .breadcrumb,
  .btn,
  .panel-heading .btn {
    display: none !important;
  }

  .panel {
    border: 1px solid #ddd !important;
    box-shadow: none !important;
  }

  .huge {
    font-size: 18px !important;
  }
}
</style>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script type="text/javascript">
var charts = {};

$(document).ready(function() {
    $('[data-toggle="tooltip"]').tooltip();

    // تحديث نطاق التاريخ عند تغيير الفترة
    updateDateRange();

    // إنشاء الرسوم البيانية
    initializeCharts();

    // تأثيرات بصرية للإحصائيات
    animateStatistics();

    // تحديث التقرير كل 5 دقائق
    setInterval(function() {
        if ($('#auto-refresh').is(':checked')) {
            refreshReport();
        }
    }, 300000);
});

function updateDateRange() {
    var period = $('#period-filter').val();
    var today = new Date();
    var fromDate, toDate;

    switch (period) {
        case 'today':
            fromDate = toDate = today.toISOString().split('T')[0];
            break;
        case 'week':
            var weekStart = new Date(today.setDate(today.getDate() - today.getDay()));
            fromDate = weekStart.toISOString().split('T')[0];
            toDate = new Date().toISOString().split('T')[0];
            break;
        case 'month':
            fromDate = new Date(today.getFullYear(), today.getMonth(), 1).toISOString().split('T')[0];
            toDate = new Date().toISOString().split('T')[0];
            break;
        case 'quarter':
            var quarter = Math.floor(today.getMonth() / 3);
            fromDate = new Date(today.getFullYear(), quarter * 3, 1).toISOString().split('T')[0];
            toDate = new Date().toISOString().split('T')[0];
            break;
        case 'year':
            fromDate = new Date(today.getFullYear(), 0, 1).toISOString().split('T')[0];
            toDate = new Date().toISOString().split('T')[0];
            break;
        case 'custom':
            return; // لا تغيير للتواريخ المخصصة
    }

    $('#date-from').val(fromDate);
    $('#date-to').val(toDate);
}

function initializeCharts() {
    // رسم بياني دائري لتوزيع أنواع المواقع
    var ctx1 = document.getElementById('location-types-chart').getContext('2d');
    charts.locationTypes = new Chart(ctx1, {
        type: 'doughnut',
        data: {
            labels: ['مستودع', 'منطقة', 'ممر', 'رف', 'رفة', 'صندوق'],
            datasets: [{
                data: [{{ chart_data.location_types|join(',') }}],
                backgroundColor: [
                    '#337ab7',
                    '#5cb85c',
                    '#f0ad4e',
                    '#d9534f',
                    '#5bc0de',
                    '#9b59b6'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // رسم بياني عمودي لنسب الإشغال
    var ctx2 = document.getElementById('occupancy-chart').getContext('2d');
    charts.occupancy = new Chart(ctx2, {
        type: 'bar',
        data: {
            labels: ['فارغ', 'منخفض', 'متوسط', 'عالي', 'ممتلئ'],
            datasets: [{
                label: 'عدد المواقع',
                data: [{{ chart_data.occupancy_levels|join(',') }}],
                backgroundColor: [
                    '#d9534f',
                    '#f0ad4e',
                    '#5bc0de',
                    '#337ab7',
                    '#5cb85c'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // رسم بياني خطي لاتجاه الاستخدام
    var ctx3 = document.getElementById('usage-trend-chart').getContext('2d');
    charts.usageTrend = new Chart(ctx3, {
        type: 'line',
        data: {
            labels: [{{ chart_data.trend_labels|map(l => '"' ~ l ~ '"')|join(',') }}],
            datasets: [{
                label: 'نسبة الاستخدام %',
                data: [{{ chart_data.trend_data|join(',') }}],
                borderColor: '#337ab7',
                backgroundColor: 'rgba(51, 122, 183, 0.1)',
                tension: 0.4
            }, {
                label: 'عدد الحركات',
                data: [{{ chart_data.movements_data|join(',') }}],
                borderColor: '#5cb85c',
                backgroundColor: 'rgba(92, 184, 92, 0.1)',
                tension: 0.4,
                yAxisID: 'y1'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    beginAtZero: true,
                    max: 100
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    beginAtZero: true,
                    grid: {
                        drawOnChartArea: false,
                    },
                }
            }
        }
    });
}

function animateStatistics() {
    $('.huge').each(function() {
        var $this = $(this);
        var finalValue = parseInt($this.text().replace(/,/g, '').replace('%', ''));
        if (!isNaN(finalValue)) {
            $this.text('0');

            $({ counter: 0 }).animate({ counter: finalValue }, {
                duration: 2000,
                easing: 'swing',
                step: function() {
                    var value = Math.ceil(this.counter);
                    if ($this.attr('id') === 'usage-percentage') {
                        $this.text(value + '%');
                    } else {
                        $this.text(value.toLocaleString());
                    }
                }
            });
        }
    });
}

function generateReport() {
    showNotification('جاري إنشاء التقرير...', 'info');

    var filters = {
        period: $('#period-filter').val(),
        date_from: $('#date-from').val(),
        date_to: $('#date-to').val(),
        location_type: $('#location-type-filter').val(),
        branch_id: $('#branch-filter').val(),
        warehouse_id: $('#warehouse-filter').val(),
        occupancy: $('#occupancy-filter').val()
    };

    $.ajax({
        url: 'index.php?route=inventory/location_management/generateUsageReport&user_token={{ user_token }}',
        type: 'POST',
        data: filters,
        dataType: 'json',
        success: function(data) {
            if (data.success) {
                updateReportData(data.report);
                showNotification('تم إنشاء التقرير بنجاح', 'success');
            } else {
                showNotification(data.error || 'خطأ في إنشاء التقرير', 'error');
            }
        },
        error: function() {
            showNotification('خطأ في الاتصال بالخادم', 'error');
        }
    });
}

function updateReportData(reportData) {
    // تحديث الإحصائيات
    $('#total-locations').text(reportData.statistics.total_locations);
    $('#active-locations').text(reportData.statistics.active_locations);
    $('#occupied-locations').text(reportData.statistics.occupied_locations);
    $('#usage-percentage').text(reportData.statistics.overall_usage + '%');
    $('#total-movements').text(reportData.statistics.total_movements.toLocaleString());
    $('#total-value').text(reportData.statistics.total_value.toLocaleString());

    // تحديث الرسوم البيانية
    updateCharts(reportData.charts);

    // تحديث الجداول
    updateTables(reportData.tables);

    // تحديث تحليل الكفاءة
    updateEfficiencyAnalysis(reportData.efficiency);

    // إعادة تشغيل التأثيرات البصرية
    animateStatistics();
}

function updateCharts(chartData) {
    // تحديث رسم أنواع المواقع
    charts.locationTypes.data.datasets[0].data = chartData.location_types;
    charts.locationTypes.update();

    // تحديث رسم نسب الإشغال
    charts.occupancy.data.datasets[0].data = chartData.occupancy_levels;
    charts.occupancy.update();

    // تحديث رسم الاتجاه
    charts.usageTrend.data.labels = chartData.trend_labels;
    charts.usageTrend.data.datasets[0].data = chartData.trend_data;
    charts.usageTrend.data.datasets[1].data = chartData.movements_data;
    charts.usageTrend.update();
}

function updateTables(tableData) {
    // تحديث جدول أكثر المواقع استخداماً
    var mostUsedHtml = '';
    tableData.most_used.forEach(function(location, index) {
        mostUsedHtml += '<tr>';
        mostUsedHtml += '<td>' + (index + 1) + '</td>';
        mostUsedHtml += '<td><strong>' + location.name + '</strong><br><small class="text-muted">' + location.location_code + '</small></td>';
        mostUsedHtml += '<td>';
        mostUsedHtml += '<div class="progress progress-xs">';
        mostUsedHtml += '<div class="progress-bar progress-bar-success" style="width: ' + location.usage_percentage + '%"></div>';
        mostUsedHtml += '</div>';
        mostUsedHtml += location.usage_percentage + '%';
        mostUsedHtml += '</td>';
        mostUsedHtml += '<td>' + location.movements_count + '</td>';
        mostUsedHtml += '</tr>';
    });
    $('#most-used-locations').html(mostUsedHtml);

    // تحديث جدول المواقع التي تحتاج انتباه
    var attentionHtml = '';
    tableData.attention_needed.forEach(function(location) {
        attentionHtml += '<tr>';
        attentionHtml += '<td><strong>' + location.name + '</strong><br><small class="text-muted">' + location.location_code + '</small></td>';
        attentionHtml += '<td>' + location.issue + '</td>';
        attentionHtml += '<td><span class="label label-' + location.status_class + '">' + location.status_text + '</span></td>';
        attentionHtml += '<td><a href="' + location.view_url + '" class="btn btn-xs btn-info"><i class="fa fa-eye"></i></a></td>';
        attentionHtml += '</tr>';
    });
    $('#attention-locations').html(attentionHtml);
}

function updateEfficiencyAnalysis(efficiency) {
    // تحديث مؤشرات الكفاءة
    $('.efficiency-metric .progress-bar').each(function() {
        var metric = $(this).closest('.efficiency-metric').find('h4 i').attr('class');
        var percentage = 0;

        if (metric.includes('tachometer')) {
            percentage = efficiency.usage_efficiency;
        } else if (metric.includes('random')) {
            percentage = efficiency.distribution_efficiency;
        } else if (metric.includes('clock')) {
            percentage = efficiency.time_efficiency;
        }

        $(this).css('width', percentage + '%').text(percentage + '%');
    });

    // تحديث اقتراحات التحسين
    var suggestionsHtml = '';
    efficiency.suggestions.forEach(function(suggestion) {
        suggestionsHtml += '<li class="suggestion-item">';
        suggestionsHtml += '<i class="fa fa-' + suggestion.icon + ' text-' + suggestion.type + '"></i>';
        suggestionsHtml += '<strong>' + suggestion.title + ':</strong> ' + suggestion.description;
        suggestionsHtml += '</li>';
    });
    $('#improvement-suggestions').html(suggestionsHtml);
}

function refreshReport() {
    generateReport();
}

function resetFilters() {
    $('#period-filter').val('month');
    $('#location-type-filter').val('');
    $('#branch-filter').val('');
    $('#warehouse-filter').val('');
    $('#occupancy-filter').val('');
    updateDateRange();
    showNotification('تم إعادة تعيين الفلاتر', 'success');
}

function exportPDF() {
    showNotification('جاري تصدير التقرير كـ PDF...', 'info');

    var filters = getFilters();
    var url = 'index.php?route=inventory/location_management/exportUsageReportPDF&user_token={{ user_token }}';

    // إضافة الفلاتر كمعاملات URL
    var params = new URLSearchParams(filters);
    window.open(url + '&' + params.toString(), '_blank');

    showNotification('تم بدء تحميل ملف PDF', 'success');
}

function exportExcel() {
    showNotification('جاري تصدير التقرير كـ Excel...', 'info');

    var filters = getFilters();
    var url = 'index.php?route=inventory/location_management/exportUsageReportExcel&user_token={{ user_token }}';

    // إضافة الفلاتر كمعاملات URL
    var params = new URLSearchParams(filters);
    window.open(url + '&' + params.toString(), '_blank');

    showNotification('تم بدء تحميل ملف Excel', 'success');
}

function printReport() {
    window.print();
}

function getFilters() {
    return {
        period: $('#period-filter').val(),
        date_from: $('#date-from').val(),
        date_to: $('#date-to').val(),
        location_type: $('#location-type-filter').val(),
        branch_id: $('#branch-filter').val(),
        warehouse_id: $('#warehouse-filter').val(),
        occupancy: $('#occupancy-filter').val()
    };
}

function showNotification(message, type) {
    var alertClass = type === 'success' ? 'alert-success' : type === 'info' ? 'alert-info' : 'alert-danger';
    var icon = type === 'success' ? 'fa-check-circle' : type === 'info' ? 'fa-info-circle' : 'fa-exclamation-circle';

    var notification = $('<div class="alert ' + alertClass + ' alert-dismissible" style="position: fixed; top: 70px; right: 20px; z-index: 9999; min-width: 300px;"><i class="fa ' + icon + '"></i> ' + message + '<button type="button" class="close" data-dismiss="alert">&times;</button></div>');

    $('body').append(notification);

    setTimeout(function() {
        notification.fadeOut(function() { notification.remove(); });
    }, 4000);
}

// تحديث نطاق التاريخ عند تغيير الفترة
$('#period-filter').change(function() {
    updateDateRange();
});

// إنشاء التقرير تلقائياً عند تغيير الفلاتر
$('#report-filters select, #report-filters input').change(function() {
    if ($('#auto-generate').is(':checked')) {
        generateReport();
    }
});
</script>

{{ footer }}
