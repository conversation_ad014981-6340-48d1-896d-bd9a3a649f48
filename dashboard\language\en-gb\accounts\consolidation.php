<?php
// Heading
$_['heading_title']                    = 'Financial Statements Consolidation';
$_['heading_title_view']               = 'View Consolidation';

// Text
$_['text_list']                        = 'Consolidation List';
$_['text_add']                         = 'Add Consolidation';
$_['text_edit']                        = 'Edit Consolidation';
$_['text_view']                        = 'View Consolidation';
$_['text_confirm']                     = 'Confirm';
$_['text_select']                      = 'Select';
$_['text_no_results']                  = 'No results found';
$_['text_home']                        = 'Home';
$_['text_accounts']                    = 'Accounts';
$_['text_new_consolidation']           = 'Create New Consolidation';
$_['text_recent_consolidations']       = 'Recent Consolidations';
$_['text_subsidiaries_list']           = 'Subsidiaries List';
$_['text_subsidiaries']                = 'Subsidiaries';
$_['text_recent_reports']              = 'Recent Reports';
$_['text_current_period']              = 'Current Period';
$_['text_period']                      = 'Period';
$_['text_currency']                    = 'Currency';
$_['text_processing']                  = 'Processing...';
$_['text_confirm_generate']            = 'Are you sure you want to generate the consolidation report?';
$_['text_select_subsidiaries']         = 'Select Subsidiaries';
$_['text_include_adjustments']         = 'Include Adjustments';

// Consolidation Methods
$_['text_full_consolidation']          = 'Full Consolidation';
$_['text_proportional_consolidation']  = 'Proportional Consolidation';
$_['text_equity_method']               = 'Equity Method';

// Subsidiary Types
$_['text_subsidiary']                  = 'Subsidiary';
$_['text_associate']                   = 'Associate';
$_['text_joint_venture']               = 'Joint Venture';

// Currencies
$_['text_egp']                         = 'Egyptian Pound (EGP)';
$_['text_usd']                         = 'US Dollar (USD)';
$_['text_eur']                         = 'Euro (EUR)';

// Status
$_['text_completed']                   = 'Completed';
$_['text_failed']                      = 'Failed';
$_['text_active']                      = 'Active';
$_['text_inactive']                    = 'Inactive';
$_['text_no_subsidiaries']             = 'No subsidiaries found';

// Entry
$_['entry_period_start']               = 'Period Start';
$_['entry_period_end']                 = 'Period End';
$_['entry_consolidation_method']       = 'Consolidation Method';
$_['entry_currency']                   = 'Reporting Currency';
$_['entry_subsidiaries']               = 'Subsidiaries';

// Column
$_['column_reference']                 = 'Reference';
$_['column_period']                    = 'Period';
$_['column_method']                    = 'Method';
$_['column_currency']                  = 'Currency';
$_['column_status']                    = 'Status';
$_['column_created_by']                = 'Created By';
$_['column_date_created']              = 'Date Created';
$_['column_action']                    = 'Action';
$_['column_name']                      = 'Name';
$_['column_type']                      = 'Type';
$_['column_ownership']                 = 'Ownership %';
$_['column_account_code']              = 'Account Code';
$_['column_account_name']              = 'Account Name';
$_['column_amount']                    = 'Amount';
$_['column_statement_type']            = 'Statement Type';

// Button
$_['button_generate']                  = 'Generate Consolidation';
$_['button_view']                      = 'View';
$_['button_export']                    = 'Export';
$_['button_cancel']                    = 'Cancel';
$_['button_save']                      = 'Save';
$_['button_print']                     = 'Print';

// Success
$_['text_consolidation_generated']     = 'Consolidation report generated successfully!';
$_['text_adjustments_saved']           = 'Adjustments saved successfully!';

// Error
$_['error_permission']                 = 'Warning: You do not have permission to access financial statements consolidation!';
$_['error_permission_generate']        = 'Warning: You do not have permission to generate consolidation!';
$_['error_permission_adjust']          = 'Warning: You do not have permission to adjust consolidation!';
$_['error_permission_export']          = 'Warning: You do not have permission to export consolidation!';
$_['error_consolidation_failed']       = 'Failed to generate consolidation report!';
$_['error_adjustments_failed']         = 'Failed to save adjustments!';
$_['error_ajax']                       = 'Connection error occurred!';
$_['error_period_start']               = 'Period start is required!';
$_['error_period_end']                 = 'Period end is required!';
$_['error_method']                     = 'Consolidation method is required!';
$_['error_currency']                   = 'Reporting currency is required!';
$_['error_subsidiaries']               = 'At least one subsidiary must be selected!';

// Help
$_['help_subsidiaries']                = 'Select the subsidiaries to include in the consolidation';
$_['help_consolidation_method']        = 'Choose the appropriate consolidation method based on investment type';
$_['help_currency']                    = 'The currency in which the report will be displayed';
$_['help_adjustments']                 = 'Include consolidation adjustments such as intercompany eliminations';

// Log Messages
$_['log_unauthorized_access_consolidation'] = 'Unauthorized access attempt to financial statements consolidation screen';
$_['log_view_consolidation_screen']    = 'Viewed financial statements consolidation screen';
$_['log_consolidation_generated']      = 'Financial statements consolidation report generated';
$_['log_consolidation_adjusted']       = 'Financial statements consolidation report adjusted';
$_['log_consolidation_exported']       = 'Financial statements consolidation report exported';

// Notification Messages
$_['notification_consolidation_title'] = 'Consolidation Report Ready';
$_['notification_consolidation_message'] = 'Consolidation report #%s has been generated successfully';

// Statement Types
$_['text_balance_sheet']               = 'Balance Sheet';
$_['text_income_statement']            = 'Income Statement';
$_['text_cash_flow']                   = 'Cash Flow Statement';
$_['text_equity_changes']              = 'Statement of Changes in Equity';

// Adjustment Types
$_['text_intercompany_elimination']    = 'Intercompany Eliminations';
$_['text_fair_value_adjustment']       = 'Fair Value Adjustments';
$_['text_currency_translation']        = 'Currency Translation';
$_['text_goodwill_calculation']        = 'Goodwill Calculation';

// Advanced Features
$_['text_consolidation_workpaper']     = 'Consolidation Workpaper';
$_['text_elimination_entries']         = 'Elimination Entries';
$_['text_minority_interest']           = 'Minority Interest';
$_['text_goodwill']                    = 'Goodwill';
$_['text_fair_value_adjustments']      = 'Fair Value Adjustments';
$_['text_currency_translation_adj']    = 'Currency Translation Adjustments';

// Export Formats
$_['text_export_pdf']                  = 'Export PDF';
$_['text_export_excel']                = 'Export Excel';
$_['text_export_csv']                  = 'Export CSV';

// Validation Messages
$_['text_validation_period']           = 'Period end must be after period start';
$_['text_validation_subsidiaries']     = 'At least one subsidiary must be selected';
$_['text_validation_method']           = 'Consolidation method is required';
?>
