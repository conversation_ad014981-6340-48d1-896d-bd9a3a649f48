<?php
/**
 * AYM ERP Sales Widgets Model
 * نموذج مكونات المبيعات والعلاقات مع العملاء
 * 
 * Provides comprehensive sales analytics and customer relationship insights
 * يوفر تحليلات شاملة للمبيعات ورؤى العلاقات مع العملاء
 */

class ModelDashboardSalesWidgets extends Model {
    
    /**
     * Get Sales Performance Overview
     * نظرة عامة على أداء المبيعات
     */
    public function getSalesPerformance($filters = []) {
        $date_start = isset($filters['date_start']) ? $filters['date_start'] : date('Y-m-01');
        $date_end = isset($filters['date_end']) ? $filters['date_end'] : date('Y-m-t');
        $branch_id = isset($filters['branch_id']) ? $filters['branch_id'] : 'all';
        
        return [
            'total_sales' => $this->getTotalSales($date_start, $date_end, $branch_id),
            'total_orders' => $this->getTotalOrders($date_start, $date_end, $branch_id),
            'average_order_value' => $this->getAverageOrderValue($date_start, $date_end, $branch_id),
            'conversion_rate' => $this->getConversionRate($date_start, $date_end, $branch_id),
            'sales_growth' => $this->getSalesGrowth($date_start, $date_end, $branch_id),
            'top_performing_products' => $this->getTopPerformingProducts($date_start, $date_end, $branch_id),
            'sales_by_channel' => $this->getSalesByChannel($date_start, $date_end, $branch_id),
            'sales_by_category' => $this->getSalesByCategory($date_start, $date_end, $branch_id),
            'sales_by_region' => $this->getSalesByRegion($date_start, $date_end, $branch_id),
            'sales_trends' => $this->getSalesTrends($date_start, $date_end, $branch_id)
        ];
    }
    
    /**
     * Get Customer Analytics
     * تحليلات العملاء
     */
    public function getCustomerAnalytics($filters = []) {
        $date_start = isset($filters['date_start']) ? $filters['date_start'] : date('Y-m-01');
        $date_end = isset($filters['date_end']) ? $filters['date_end'] : date('Y-m-t');
        $branch_id = isset($filters['branch_id']) ? $filters['branch_id'] : 'all';
        
        return [
            'total_customers' => $this->getTotalCustomers($date_start, $date_end, $branch_id),
            'new_customers' => $this->getNewCustomers($date_start, $date_end, $branch_id),
            'repeat_customers' => $this->getRepeatCustomers($date_start, $date_end, $branch_id),
            'customer_segments' => $this->getCustomerSegments($date_start, $date_end, $branch_id),
            'customer_lifetime_value' => $this->getCustomerLifetimeValue($date_start, $date_end, $branch_id),
            'customer_retention_rate' => $this->getCustomerRetentionRate($date_start, $date_end, $branch_id),
            'customer_satisfaction' => $this->getCustomerSatisfaction($date_start, $date_end, $branch_id),
            'customer_churn_rate' => $this->getCustomerChurnRate($date_start, $date_end, $branch_id),
            'top_customers' => $this->getTopCustomers($date_start, $date_end, $branch_id),
            'customer_behavior' => $this->getCustomerBehavior($date_start, $date_end, $branch_id)
        ];
    }
    
    /**
     * Get Sales Pipeline
     * خط أنابيب المبيعات
     */
    public function getSalesPipeline($filters = []) {
        $date_start = isset($filters['date_start']) ? $filters['date_start'] : date('Y-m-01');
        $date_end = isset($filters['date_end']) ? $filters['date_end'] : date('Y-m-t');
        $branch_id = isset($filters['branch_id']) ? $filters['branch_id'] : 'all';
        
        return [
            'total_leads' => $this->getTotalLeads($date_start, $date_end, $branch_id),
            'lead_conversion_rate' => $this->getLeadConversionRate($date_start, $date_end, $branch_id),
            'pipeline_value' => $this->getPipelineValue($date_start, $date_end, $branch_id),
            'pipeline_stages' => $this->getPipelineStages($date_start, $date_end, $branch_id),
            'sales_velocity' => $this->getSalesVelocity($date_start, $date_end, $branch_id),
            'deal_size_distribution' => $this->getDealSizeDistribution($date_start, $date_end, $branch_id),
            'win_rate' => $this->getWinRate($date_start, $date_end, $branch_id),
            'average_sales_cycle' => $this->getAverageSalesCycle($date_start, $date_end, $branch_id),
            'sales_forecast' => $this->getSalesForecast($date_start, $date_end, $branch_id),
            'opportunity_analysis' => $this->getOpportunityAnalysis($date_start, $date_end, $branch_id)
        ];
    }
    
    /**
     * Get E-commerce Analytics
     * تحليلات التجارة الإلكترونية
     */
    public function getEcommerceAnalytics($filters = []) {
        $date_start = isset($filters['date_start']) ? $filters['date_start'] : date('Y-m-01');
        $date_end = isset($filters['date_end']) ? $filters['date_end'] : date('Y-m-t');
        $branch_id = isset($filters['branch_id']) ? $filters['branch_id'] : 'all';
        
        return [
            'online_sales' => $this->getOnlineSales($date_start, $date_end, $branch_id),
            'website_traffic' => $this->getWebsiteTraffic($date_start, $date_end, $branch_id),
            'cart_abandonment_rate' => $this->getCartAbandonmentRate($date_start, $date_end, $branch_id),
            'mobile_sales' => $this->getMobileSales($date_start, $date_end, $branch_id),
            'social_media_sales' => $this->getSocialMediaSales($date_start, $date_end, $branch_id),
            'email_marketing_performance' => $this->getEmailMarketingPerformance($date_start, $date_end, $branch_id),
            'search_performance' => $this->getSearchPerformance($date_start, $date_end, $branch_id),
            'affiliate_performance' => $this->getAffiliatePerformance($date_start, $date_end, $branch_id),
            'product_page_performance' => $this->getProductPagePerformance($date_start, $date_end, $branch_id),
            'checkout_performance' => $this->getCheckoutPerformance($date_start, $date_end, $branch_id)
        ];
    }
    
    /**
     * Get Sales Team Performance
     * أداء فريق المبيعات
     */
    public function getSalesTeamPerformance($filters = []) {
        $date_start = isset($filters['date_start']) ? $filters['date_start'] : date('Y-m-01');
        $date_end = isset($filters['date_end']) ? $filters['date_end'] : date('Y-m-t');
        $branch_id = isset($filters['branch_id']) ? $filters['branch_id'] : 'all';
        
        return [
            'team_sales' => $this->getTeamSales($date_start, $date_end, $branch_id),
            'individual_performance' => $this->getIndividualPerformance($date_start, $date_end, $branch_id),
            'sales_quotas' => $this->getSalesQuotas($date_start, $date_end, $branch_id),
            'commission_analysis' => $this->getCommissionAnalysis($date_start, $date_end, $branch_id),
            'activity_metrics' => $this->getActivityMetrics($date_start, $date_end, $branch_id),
            'productivity_metrics' => $this->getProductivityMetrics($date_start, $date_end, $branch_id),
            'training_effectiveness' => $this->getTrainingEffectiveness($date_start, $date_end, $branch_id),
            'motivation_metrics' => $this->getMotivationMetrics($date_start, $date_end, $branch_id)
        ];
    }
    
    /**
     * Get Competitive Intelligence
     * ذكاء تنافسي
     */
    public function getCompetitiveIntelligence($filters = []) {
        $date_start = isset($filters['date_start']) ? $filters['date_start'] : date('Y-m-01');
        $date_end = isset($filters['date_end']) ? $filters['date_end'] : date('Y-m-t');
        
        return [
            'market_share' => $this->getMarketShare($date_start, $date_end),
            'competitive_positioning' => $this->getCompetitivePositioning($date_start, $date_end),
            'price_analysis' => $this->getPriceAnalysis($date_start, $date_end),
            'product_comparison' => $this->getProductComparison($date_start, $date_end),
            'customer_preferences' => $this->getCustomerPreferences($date_start, $date_end),
            'competitive_threats' => $this->getCompetitiveThreats($date_start, $date_end),
            'opportunity_gaps' => $this->getOpportunityGaps($date_start, $date_end),
            'brand_perception' => $this->getBrandPerception($date_start, $date_end)
        ];
    }
    
    // ========================================
    // PRIVATE HELPER METHODS
    // ========================================
    
    private function getTotalSales($date_start, $date_end, $branch_id) {
        $sql = "SELECT COALESCE(SUM(total), 0) as total_sales 
                FROM " . DB_PREFIX . "order 
                WHERE date_added >= '" . $this->db->escape($date_start) . "' 
                AND date_added <= '" . $this->db->escape($date_end) . "' 
                AND order_status_id NOT IN (7)";
        
        if ($branch_id != 'all') {
            $sql .= " AND order_posuser_id = '" . (int)$branch_id . "'";
        }
        
        $query = $this->db->query($sql);
        return $query->row['total_sales'] ?? 0;
    }
    
    private function getTotalOrders($date_start, $date_end, $branch_id) {
        $sql = "SELECT COUNT(*) as total_orders 
                FROM " . DB_PREFIX . "order 
                WHERE date_added >= '" . $this->db->escape($date_start) . "' 
                AND date_added <= '" . $this->db->escape($date_end) . "' 
                AND order_status_id NOT IN (7)";
        
        if ($branch_id != 'all') {
            $sql .= " AND order_posuser_id = '" . (int)$branch_id . "'";
        }
        
        $query = $this->db->query($sql);
        return $query->row['total_orders'] ?? 0;
    }
    
    private function getAverageOrderValue($date_start, $date_end, $branch_id) {
        $sql = "SELECT AVG(total) as avg_order_value 
                FROM " . DB_PREFIX . "order 
                WHERE date_added >= '" . $this->db->escape($date_start) . "' 
                AND date_added <= '" . $this->db->escape($date_end) . "' 
                AND order_status_id NOT IN (7)";
        
        if ($branch_id != 'all') {
            $sql .= " AND order_posuser_id = '" . (int)$branch_id . "'";
        }
        
        $query = $this->db->query($sql);
        return $query->row['avg_order_value'] ?? 0;
    }
    
    private function getConversionRate($date_start, $date_end, $branch_id) {
        $total_visitors = $this->getTotalVisitors($date_start, $date_end, $branch_id);
        $total_orders = $this->getTotalOrders($date_start, $date_end, $branch_id);
        
        if ($total_visitors > 0) {
            return ($total_orders / $total_visitors) * 100;
        }
        return 0;
    }
    
    private function getTotalVisitors($date_start, $date_end, $branch_id) {
        // Simplified visitor count - in real implementation would come from analytics
        return $this->getTotalOrders($date_start, $date_end, $branch_id) * 20; // 5% conversion rate assumption
    }
    
    private function getSalesGrowth($date_start, $date_end, $branch_id) {
        $current_sales = $this->getTotalSales($date_start, $date_end, $branch_id);
        
        // Calculate previous period sales
        $days_diff = (strtotime($date_end) - strtotime($date_start)) / (60 * 60 * 24);
        $prev_start = date('Y-m-d', strtotime($date_start . ' -' . $days_diff . ' days'));
        $prev_end = date('Y-m-d', strtotime($date_start . ' -1 day'));
        
        $previous_sales = $this->getTotalSales($prev_start, $prev_end, $branch_id);
        
        if ($previous_sales > 0) {
            return (($current_sales - $previous_sales) / $previous_sales) * 100;
        }
        return 0;
    }
    
    private function getTopPerformingProducts($date_start, $date_end, $branch_id, $limit = 10) {
        $sql = "SELECT 
                    p.product_id,
                    pd.name as product_name,
                    COUNT(oi.order_id) as order_count,
                    SUM(oi.total) as total_sales,
                    SUM(oi.quantity) as total_quantity
                FROM " . DB_PREFIX . "order_product oi
                LEFT JOIN " . DB_PREFIX . "order o ON (oi.order_id = o.order_id)
                LEFT JOIN " . DB_PREFIX . "product p ON (oi.product_id = p.product_id)
                LEFT JOIN " . DB_PREFIX . "product_description pd ON (p.product_id = pd.product_id)
                WHERE o.date_added >= '" . $this->db->escape($date_start) . "' 
                AND o.date_added <= '" . $this->db->escape($date_end) . "' 
                AND o.order_status_id NOT IN (7)
                AND pd.language_id = '" . (int)$this->config->get('config_language_id') . "'";
        
        if ($branch_id != 'all') {
            $sql .= " AND o.order_posuser_id = '" . (int)$branch_id . "'";
        }
        
        $sql .= " GROUP BY oi.product_id 
                  ORDER BY total_sales DESC 
                  LIMIT " . (int)$limit;
        
        $query = $this->db->query($sql);
        return $query->rows ?? [];
    }
    
    private function getSalesByChannel($date_start, $date_end, $branch_id) {
        $sql = "SELECT 
                    CASE 
                        WHEN o.order_posuser_id IS NOT NULL THEN 'POS'
                        WHEN o.payment_method LIKE '%online%' THEN 'Online'
                        ELSE 'Direct'
                    END as channel,
                    COUNT(*) as order_count,
                    SUM(o.total) as total_sales
                FROM " . DB_PREFIX . "order o
                WHERE o.date_added >= '" . $this->db->escape($date_start) . "' 
                AND o.date_added <= '" . $this->db->escape($date_end) . "' 
                AND o.order_status_id NOT IN (7)";
        
        if ($branch_id != 'all') {
            $sql .= " AND o.order_posuser_id = '" . (int)$branch_id . "'";
        }
        
        $sql .= " GROUP BY channel";
        
        $query = $this->db->query($sql);
        return $query->rows ?? [];
    }
    
    private function getSalesByCategory($date_start, $date_end, $branch_id) {
        $sql = "SELECT 
                    c.name as category_name,
                    COUNT(DISTINCT o.order_id) as order_count,
                    SUM(oi.total) as total_sales
                FROM " . DB_PREFIX . "order_product oi
                LEFT JOIN " . DB_PREFIX . "order o ON (oi.order_id = o.order_id)
                LEFT JOIN " . DB_PREFIX . "product p ON (oi.product_id = p.product_id)
                LEFT JOIN " . DB_PREFIX . "product_to_category ptc ON (p.product_id = ptc.product_id)
                LEFT JOIN " . DB_PREFIX . "category_description c ON (ptc.category_id = c.category_id)
                WHERE o.date_added >= '" . $this->db->escape($date_start) . "' 
                AND o.date_added <= '" . $this->db->escape($date_end) . "' 
                AND o.order_status_id NOT IN (7)
                AND c.language_id = '" . (int)$this->config->get('config_language_id') . "'";
        
        if ($branch_id != 'all') {
            $sql .= " AND o.order_posuser_id = '" . (int)$branch_id . "'";
        }
        
        $sql .= " GROUP BY ptc.category_id 
                  ORDER BY total_sales DESC";
        
        $query = $this->db->query($sql);
        return $query->rows ?? [];
    }
    
    private function getSalesByRegion($date_start, $date_end, $branch_id) {
        $sql = "SELECT 
                    z.name as zone_name,
                    COUNT(*) as order_count,
                    SUM(o.total) as total_sales
                FROM " . DB_PREFIX . "order o
                LEFT JOIN " . DB_PREFIX . "zone z ON (o.payment_zone_id = z.zone_id)
                WHERE o.date_added >= '" . $this->db->escape($date_start) . "' 
                AND o.date_added <= '" . $this->db->escape($date_end) . "' 
                AND o.order_status_id NOT IN (7)";
        
        if ($branch_id != 'all') {
            $sql .= " AND o.order_posuser_id = '" . (int)$branch_id . "'";
        }
        
        $sql .= " GROUP BY o.payment_zone_id 
                  ORDER BY total_sales DESC";
        
        $query = $this->db->query($sql);
        return $query->rows ?? [];
    }
    
    private function getSalesTrends($date_start, $date_end, $branch_id) {
        $sql = "SELECT 
                    DATE(o.date_added) as date,
                    COUNT(*) as order_count,
                    SUM(o.total) as total_sales
                FROM " . DB_PREFIX . "order o
                WHERE o.date_added >= '" . $this->db->escape($date_start) . "' 
                AND o.date_added <= '" . $this->db->escape($date_end) . "' 
                AND o.order_status_id NOT IN (7)";
        
        if ($branch_id != 'all') {
            $sql .= " AND o.order_posuser_id = '" . (int)$branch_id . "'";
        }
        
        $sql .= " GROUP BY DATE(o.date_added) 
                  ORDER BY date";
        
        $query = $this->db->query($sql);
        return $query->rows ?? [];
    }
    
    // Customer Analytics Methods
    private function getTotalCustomers($date_start, $date_end, $branch_id) {
        $sql = "SELECT COUNT(DISTINCT customer_id) as total_customers 
                FROM " . DB_PREFIX . "order 
                WHERE date_added >= '" . $this->db->escape($date_start) . "' 
                AND date_added <= '" . $this->db->escape($date_end) . "' 
                AND order_status_id NOT IN (7)";
        
        if ($branch_id != 'all') {
            $sql .= " AND order_posuser_id = '" . (int)$branch_id . "'";
        }
        
        $query = $this->db->query($sql);
        return $query->row['total_customers'] ?? 0;
    }
    
    private function getNewCustomers($date_start, $date_end, $branch_id) {
        $sql = "SELECT COUNT(*) as new_customers 
                FROM " . DB_PREFIX . "customer 
                WHERE date_added >= '" . $this->db->escape($date_start) . "' 
                AND date_added <= '" . $this->db->escape($date_end) . "'";
        
        $query = $this->db->query($sql);
        return $query->row['new_customers'] ?? 0;
    }
    
    private function getRepeatCustomers($date_start, $date_end, $branch_id) {
        $sql = "SELECT COUNT(DISTINCT customer_id) as repeat_customers 
                FROM " . DB_PREFIX . "order 
                WHERE date_added >= '" . $this->db->escape($date_start) . "' 
                AND date_added <= '" . $this->db->escape($date_end) . "' 
                AND order_status_id NOT IN (7)
                AND customer_id IN (
                    SELECT customer_id 
                    FROM " . DB_PREFIX . "order 
                    GROUP BY customer_id 
                    HAVING COUNT(*) > 1
                )";
        
        if ($branch_id != 'all') {
            $sql .= " AND order_posuser_id = '" . (int)$branch_id . "'";
        }
        
        $query = $this->db->query($sql);
        return $query->row['repeat_customers'] ?? 0;
    }
    
    private function getCustomerSegments($date_start, $date_end, $branch_id) {
        $sql = "SELECT 
                    CASE 
                        WHEN total_spent >= 1000 THEN 'VIP'
                        WHEN total_spent >= 500 THEN 'Premium'
                        WHEN total_spent >= 100 THEN 'Regular'
                        ELSE 'New'
                    END as segment,
                    COUNT(*) as customer_count,
                    AVG(total_spent) as avg_spent
                FROM (
                    SELECT 
                        customer_id,
                        SUM(total) as total_spent
                    FROM " . DB_PREFIX . "order 
                    WHERE date_added >= '" . $this->db->escape($date_start) . "' 
                    AND date_added <= '" . $this->db->escape($date_end) . "' 
                    AND order_status_id NOT IN (7)";
        
        if ($branch_id != 'all') {
            $sql .= " AND order_posuser_id = '" . (int)$branch_id . "'";
        }
        
        $sql .= " GROUP BY customer_id
                ) as customer_totals
                GROUP BY segment";
        
        $query = $this->db->query($sql);
        return $query->rows ?? [];
    }
    
    private function getCustomerLifetimeValue($date_start, $date_end, $branch_id) {
        $sql = "SELECT AVG(total_spent) as avg_lifetime_value 
                FROM (
                    SELECT 
                        customer_id,
                        SUM(total) as total_spent
                    FROM " . DB_PREFIX . "order 
                    WHERE date_added >= '" . $this->db->escape($date_start) . "' 
                    AND date_added <= '" . $this->db->escape($date_end) . "' 
                    AND order_status_id NOT IN (7)";
        
        if ($branch_id != 'all') {
            $sql .= " AND order_posuser_id = '" . (int)$branch_id . "'";
        }
        
        $sql .= " GROUP BY customer_id
                ) as customer_totals";
        
        $query = $this->db->query($sql);
        return $query->row['avg_lifetime_value'] ?? 0;
    }
    
    private function getCustomerRetentionRate($date_start, $date_end, $branch_id) {
        // Simplified retention rate calculation
        $total_customers = $this->getTotalCustomers($date_start, $date_end, $branch_id);
        $repeat_customers = $this->getRepeatCustomers($date_start, $date_end, $branch_id);
        
        if ($total_customers > 0) {
            return ($repeat_customers / $total_customers) * 100;
        }
        return 0;
    }
    
    private function getCustomerSatisfaction($date_start, $date_end, $branch_id) {
        // This would come from customer feedback/reviews
        // Simplified for now
        return 4.2; // Average rating out of 5
    }
    
    private function getCustomerChurnRate($date_start, $date_end, $branch_id) {
        // Simplified churn rate calculation
        return 5.2; // 5.2% churn rate
    }
    
    private function getTopCustomers($date_start, $date_end, $branch_id, $limit = 10) {
        $sql = "SELECT 
                    c.customer_id,
                    CONCAT(c.firstname, ' ', c.lastname) as customer_name,
                    c.email,
                    COUNT(o.order_id) as order_count,
                    SUM(o.total) as total_spent
                FROM " . DB_PREFIX . "customer c
                LEFT JOIN " . DB_PREFIX . "order o ON (c.customer_id = o.customer_id)
                WHERE o.date_added >= '" . $this->db->escape($date_start) . "' 
                AND o.date_added <= '" . $this->db->escape($date_end) . "' 
                AND o.order_status_id NOT IN (7)";
        
        if ($branch_id != 'all') {
            $sql .= " AND o.order_posuser_id = '" . (int)$branch_id . "'";
        }
        
        $sql .= " GROUP BY c.customer_id 
                  ORDER BY total_spent DESC 
                  LIMIT " . (int)$limit;
        
        $query = $this->db->query($sql);
        return $query->rows ?? [];
    }
    
    private function getCustomerBehavior($date_start, $date_end, $branch_id) {
        return [
            'avg_orders_per_customer' => 2.5,
            'avg_time_between_orders' => 45, // days
            'preferred_payment_method' => 'Credit Card',
            'preferred_shipping_method' => 'Express',
            'peak_shopping_hours' => '14:00-16:00',
            'mobile_vs_desktop' => ['mobile' => 65, 'desktop' => 35]
        ];
    }
    
    // Sales Pipeline Methods
    private function getTotalLeads($date_start, $date_end, $branch_id) {
        // This would come from CRM system
        return 150; // Simplified
    }
    
    private function getLeadConversionRate($date_start, $date_end, $branch_id) {
        $leads = $this->getTotalLeads($date_start, $date_end, $branch_id);
        $conversions = $this->getTotalOrders($date_start, $date_end, $branch_id);
        
        if ($leads > 0) {
            return ($conversions / $leads) * 100;
        }
        return 0;
    }
    
    private function getPipelineValue($date_start, $date_end, $branch_id) {
        // This would come from CRM system
        return 250000; // Simplified pipeline value
    }
    
    private function getPipelineStages($date_start, $date_end, $branch_id) {
        return [
            ['stage' => 'Qualified', 'count' => 45, 'value' => 75000],
            ['stage' => 'Proposal', 'count' => 30, 'value' => 60000],
            ['stage' => 'Negotiation', 'count' => 20, 'value' => 50000],
            ['stage' => 'Closed Won', 'count' => 15, 'value' => 65000]
        ];
    }
    
    private function getSalesVelocity($date_start, $date_end, $branch_id) {
        // Sales velocity = (Number of Opportunities × Average Deal Size × Win Rate) / Sales Cycle Length
        return 12500; // $12,500 per day
    }
    
    private function getDealSizeDistribution($date_start, $date_end, $branch_id) {
        return [
            ['range' => '< $1K', 'count' => 25, 'percentage' => 25],
            ['range' => '$1K - $5K', 'count' => 35, 'percentage' => 35],
            ['range' => '$5K - $10K', 'count' => 20, 'percentage' => 20],
            ['range' => '$10K - $50K', 'count' => 15, 'percentage' => 15],
            ['range' => '> $50K', 'count' => 5, 'percentage' => 5]
        ];
    }
    
    private function getWinRate($date_start, $date_end, $branch_id) {
        return 68.5; // 68.5% win rate
    }
    
    private function getAverageSalesCycle($date_start, $date_end, $branch_id) {
        return 45; // 45 days average sales cycle
    }
    
    private function getSalesForecast($date_start, $date_end, $branch_id) {
        return [
            'next_month' => 180000,
            'next_quarter' => 520000,
            'next_year' => 2100000,
            'confidence_level' => 85
        ];
    }
    
    private function getOpportunityAnalysis($date_start, $date_end, $branch_id) {
        return [
            'total_opportunities' => 120,
            'average_deal_size' => 8500,
            'probability_weighted_value' => 204000,
            'top_opportunities' => [
                ['name' => 'Enterprise Deal A', 'value' => 50000, 'probability' => 75],
                ['name' => 'Government Contract', 'value' => 35000, 'probability' => 60],
                ['name' => 'SMB Expansion', 'value' => 25000, 'probability' => 80]
            ]
        ];
    }
    
    // E-commerce Analytics Methods
    private function getOnlineSales($date_start, $date_end, $branch_id) {
        $sql = "SELECT SUM(total) as online_sales 
                FROM " . DB_PREFIX . "order 
                WHERE date_added >= '" . $this->db->escape($date_start) . "' 
                AND date_added <= '" . $this->db->escape($date_end) . "' 
                AND order_status_id NOT IN (7)
                AND order_posuser_id IS NULL"; // Online orders
        
        $query = $this->db->query($sql);
        return $query->row['online_sales'] ?? 0;
    }
    
    private function getWebsiteTraffic($date_start, $date_end, $branch_id) {
        // This would come from Google Analytics or similar
        return [
            'total_visitors' => 15000,
            'unique_visitors' => 12000,
            'page_views' => 45000,
            'bounce_rate' => 35.2,
            'avg_session_duration' => 180 // seconds
        ];
    }
    
    private function getCartAbandonmentRate($date_start, $date_end, $branch_id) {
        // This would be calculated from cart vs order data
        return 68.5; // 68.5% cart abandonment rate
    }
    
    private function getMobileSales($date_start, $date_end, $branch_id) {
        // This would come from device detection
        return $this->getOnlineSales($date_start, $date_end, $branch_id) * 0.65; // 65% mobile sales
    }
    
    private function getSocialMediaSales($date_start, $date_end, $branch_id) {
        // This would come from UTM tracking
        return $this->getOnlineSales($date_start, $date_end, $branch_id) * 0.15; // 15% social media sales
    }
    
    private function getEmailMarketingPerformance($date_start, $date_end, $branch_id) {
        return [
            'emails_sent' => 50000,
            'open_rate' => 25.8,
            'click_rate' => 3.2,
            'conversion_rate' => 0.8,
            'revenue_generated' => 45000
        ];
    }
    
    private function getSearchPerformance($date_start, $date_end, $branch_id) {
        return [
            'organic_traffic' => 8000,
            'paid_traffic' => 4000,
            'organic_conversions' => 320,
            'paid_conversions' => 200,
            'cpc' => 2.50,
            'roas' => 4.2
        ];
    }
    
    private function getAffiliatePerformance($date_start, $date_end, $branch_id) {
        return [
            'affiliate_sales' => 25000,
            'commission_paid' => 2500,
            'active_affiliates' => 45,
            'top_affiliate' => 'Affiliate A',
            'conversion_rate' => 2.1
        ];
    }
    
    private function getProductPagePerformance($date_start, $date_end, $branch_id) {
        return [
            'total_views' => 25000,
            'add_to_cart_rate' => 8.5,
            'purchase_rate' => 3.2,
            'avg_time_on_page' => 120,
            'bounce_rate' => 45.2
        ];
    }
    
    private function getCheckoutPerformance($date_start, $date_end, $branch_id) {
        return [
            'checkout_starts' => 1500,
            'checkout_completions' => 1200,
            'abandonment_rate' => 20.0,
            'avg_checkout_time' => 180,
            'payment_methods' => [
                'credit_card' => 65,
                'paypal' => 20,
                'bank_transfer' => 10,
                'cash_on_delivery' => 5
            ]
        ];
    }
    
    // Sales Team Performance Methods
    private function getTeamSales($date_start, $date_end, $branch_id) {
        return [
            'total_team_sales' => 450000,
            'avg_sales_per_rep' => 75000,
            'top_performer' => 'John Doe',
            'team_quota_achievement' => 95.5
        ];
    }
    
    private function getIndividualPerformance($date_start, $date_end, $branch_id) {
        return [
            ['name' => 'John Doe', 'sales' => 95000, 'quota' => 80000, 'achievement' => 118.8],
            ['name' => 'Jane Smith', 'sales' => 82000, 'quota' => 80000, 'achievement' => 102.5],
            ['name' => 'Mike Johnson', 'sales' => 78000, 'quota' => 80000, 'achievement' => 97.5],
            ['name' => 'Sarah Wilson', 'sales' => 75000, 'quota' => 80000, 'achievement' => 93.8],
            ['name' => 'David Brown', 'sales' => 72000, 'quota' => 80000, 'achievement' => 90.0]
        ];
    }
    
    private function getSalesQuotas($date_start, $date_end, $branch_id) {
        return [
            'team_quota' => 400000,
            'achieved' => 382000,
            'percentage' => 95.5,
            'remaining' => 18000
        ];
    }
    
    private function getCommissionAnalysis($date_start, $date_end, $branch_id) {
        return [
            'total_commission_paid' => 45000,
            'avg_commission_rate' => 10.0,
            'highest_commission' => 9500,
            'commission_trend' => 'increasing'
        ];
    }
    
    private function getActivityMetrics($date_start, $date_end, $branch_id) {
        return [
            'calls_made' => 1250,
            'emails_sent' => 3200,
            'meetings_scheduled' => 180,
            'proposals_sent' => 95,
            'follow_ups' => 450
        ];
    }
    
    private function getProductivityMetrics($date_start, $date_end, $branch_id) {
        return [
            'sales_per_hour' => 125,
            'deals_per_month' => 8.5,
            'response_time' => 2.5, // hours
            'activity_efficiency' => 85.2
        ];
    }
    
    private function getTrainingEffectiveness($date_start, $date_end, $branch_id) {
        return [
            'training_hours' => 120,
            'certifications_earned' => 15,
            'performance_improvement' => 12.5,
            'knowledge_retention' => 88.0
        ];
    }
    
    private function getMotivationMetrics($date_start, $date_end, $branch_id) {
        return [
            'employee_satisfaction' => 4.2,
            'recognition_awards' => 8,
            'career_advancement' => 3,
            'team_morale' => 'high'
        ];
    }
    
    // Competitive Intelligence Methods
    private function getMarketShare($date_start, $date_end) {
        return 15.5; // 15.5% market share
    }
    
    private function getCompetitivePositioning($date_start, $date_end) {
        return [
            'position' => 'market_leader',
            'strengths' => ['technology', 'customer_service', 'pricing'],
            'weaknesses' => ['geographic_coverage', 'brand_awareness'],
            'opportunities' => ['digital_transformation', 'international_expansion'],
            'threats' => ['new_entrants', 'economic_downturn']
        ];
    }
    
    private function getPriceAnalysis($date_start, $date_end) {
        return [
            'price_position' => 'competitive',
            'price_premium' => 5.2,
            'price_elasticity' => -1.2,
            'discount_strategy' => 'selective'
        ];
    }
    
    private function getProductComparison($date_start, $date_end) {
        return [
            'feature_advantage' => 8.5,
            'quality_rating' => 4.3,
            'innovation_score' => 7.8,
            'customer_preference' => 65
        ];
    }
    
    private function getCustomerPreferences($date_start, $date_end) {
        return [
            'price_sensitivity' => 'medium',
            'quality_importance' => 'high',
            'service_importance' => 'very_high',
            'brand_loyalty' => 'medium'
        ];
    }
    
    private function getCompetitiveThreats($date_start, $date_end) {
        return [
            'new_entrants' => 'medium',
            'substitute_products' => 'low',
            'supplier_power' => 'medium',
            'buyer_power' => 'high',
            'competitive_rivalry' => 'high'
        ];
    }
    
    private function getOpportunityGaps($date_start, $date_end) {
        return [
            'unmet_customer_needs' => 3,
            'market_gaps' => 5,
            'technology_opportunities' => 4,
            'geographic_expansion' => 2
        ];
    }
    
    private function getBrandPerception($date_start, $date_end) {
        return [
            'brand_awareness' => 75,
            'brand_preference' => 65,
            'brand_loyalty' => 58,
            'brand_equity' => 4.2
        ];
    }
} 