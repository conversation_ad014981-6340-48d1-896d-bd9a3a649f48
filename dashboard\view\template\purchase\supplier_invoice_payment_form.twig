{{ header }}{{ column_left }}
<div id="content">
    <div class="page-header">
        <div class="container-fluid">
            <div class="pull-right">
                <button type="submit" form="form-payment" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fa fa-save"></i></button>
                <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a>
            </div>
            <h1>{{ heading_title }}</h1>
            <ul class="breadcrumb">
                {% for breadcrumb in breadcrumbs %}
                    <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
                {% endfor %}
            </ul>
        </div>
    </div>
    <div class="container-fluid">
        {% if error_warning %}
            <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
                <button type="button" class="close" data-dismiss="alert">&times;</button>
            </div>
        {% endif %}
        <div class="panel panel-default">
            <div class="panel-heading">
                <h3 class="panel-title"><i class="fa fa-money"></i> {{ text_payment_form }}</h3>
            </div>
            <div class="panel-body">
                <form action="{{ action }}" method="post" enctype="multipart/form-data" id="form-payment" class="form-horizontal">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h3 class="panel-title">{{ text_invoice_details }}</h3>
                        </div>
                        <div class="panel-body">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">{{ entry_invoice_number }}</label>
                                <div class="col-sm-10">
                                    <div class="form-control-static">{{ invoice_number }}</div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label">{{ entry_supplier }}</label>
                                <div class="col-sm-10">
                                    <div class="form-control-static">{{ supplier }}</div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label">{{ entry_total }}</label>
                                <div class="col-sm-10">
                                    <div class="form-control-static">{{ total }}</div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label">{{ entry_amount_paid }}</label>
                                <div class="col-sm-10">
                                    <div class="form-control-static">{{ amount_paid }}</div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label">{{ entry_amount_due }}</label>
                                <div class="col-sm-10">
                                    <div class="form-control-static">{{ amount_due }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h3 class="panel-title">{{ text_payment_details }}</h3>
                        </div>
                        <div class="panel-body">
                            <div class="form-group required">
                                <label class="col-sm-2 control-label" for="input-payment-amount">{{ entry_payment_amount }}</label>
                                <div class="col-sm-10">
                                    <div class="input-group">
                                        <input type="text" name="payment_amount" value="{{ payment_amount }}" placeholder="{{ entry_payment_amount }}" id="input-payment-amount" class="form-control" />
                                        <span class="input-group-addon">{{ currency_code }}</span>
                                    </div>
                                    {% if error_payment_amount %}
                                        <div class="text-danger">{{ error_payment_amount }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="form-group required">
                                <label class="col-sm-2 control-label" for="input-payment-date">{{ entry_payment_date }}</label>
                                <div class="col-sm-10">
                                    <div class="input-group date">
                                        <input type="text" name="payment_date" value="{{ payment_date }}" placeholder="{{ entry_payment_date }}" data-date-format="YYYY-MM-DD" id="input-payment-date" class="form-control" />
                                        <span class="input-group-btn">
                                            <button type="button" class="btn btn-default"><i class="fa fa-calendar"></i></button>
                                        </span>
                                    </div>
                                    {% if error_payment_date %}
                                        <div class="text-danger">{{ error_payment_date }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label" for="input-payment-method">{{ entry_payment_method }}</label>
                                <div class="col-sm-10">
                                    <select name="payment_method" id="input-payment-method" class="form-control">
                                        <option value="bank_transfer" {% if payment_method == 'bank_transfer' %}selected="selected"{% endif %}>{{ text_bank_transfer }}</option>
                                        <option value="cash" {% if payment_method == 'cash' %}selected="selected"{% endif %}>{{ text_cash }}</option>
                                        <option value="cheque" {% if payment_method == 'cheque' %}selected="selected"{% endif %}>{{ text_cheque }}</option>
                                        <option value="other" {% if payment_method == 'other' %}selected="selected"{% endif %}>{{ text_other }}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group required">
                                <label class="col-sm-2 control-label" for="input-account">{{ entry_account }}</label>
                                <div class="col-sm-10">
                                    <select name="account_id" id="input-account" class="form-control">
                                        <option value="">{{ text_select }}</option>
                                        {% for account in accounts %}
                                            <option value="{{ account.account_id }}" {% if account.account_id == account_id %}selected="selected"{% endif %}>{{ account.name }}</option>
                                        {% endfor %}
                                    </select>
                                    {% if error_account %}
                                        <div class="text-danger">{{ error_account }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label" for="input-reference">{{ entry_reference }}</label>
                                <div class="col-sm-10">
                                    <input type="text" name="reference" value="{{ reference }}" placeholder="{{ entry_reference }}" id="input-reference" class="form-control" />
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label" for="input-description">{{ entry_description }}</label>
                                <div class="col-sm-10">
                                    <textarea name="description" rows="5" placeholder="{{ entry_description }}" id="input-description" class="form-control">{{ description }}</textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript"><!--
$('.date').datetimepicker({
    pickTime: false
});
//--></script>
{{ footer }} 