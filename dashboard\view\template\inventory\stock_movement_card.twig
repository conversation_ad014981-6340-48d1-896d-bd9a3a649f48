{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="submit" form="inventory\stock_movement-form" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fas fa-save"></i></button>
        <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-light"><i class="fas fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    
    <div class="card">
      <div class="card-header"><i class="fas fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}chart-bar{% elif screen_type == 'settings' %}cog{% else %}pencil-alt{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="card-body">
        <form id="inventory\stock_movement-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-branch_id">{{ text_branch_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="branch_id" value="{{ branch_id }}" placeholder="{{ text_branch_id }}" id="input-branch_id" class="form-control" />
              {% if error_branch_id %}
                <div class="invalid-feedback">{{ error_branch_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-branch_type_options">{{ text_branch_type_options }}</label>
            <div class="col-sm-10">
              <input type="text" name="branch_type_options" value="{{ branch_type_options }}" placeholder="{{ text_branch_type_options }}" id="input-branch_type_options" class="form-control" />
              {% if error_branch_type_options %}
                <div class="invalid-feedback">{{ error_branch_type_options }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-branches">{{ text_branches }}</label>
            <div class="col-sm-10">
              <input type="text" name="branches" value="{{ branches }}" placeholder="{{ text_branches }}" id="input-branches" class="form-control" />
              {% if error_branches %}
                <div class="invalid-feedback">{{ error_branches }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-categories">{{ text_categories }}</label>
            <div class="col-sm-10">
              <input type="text" name="categories" value="{{ categories }}" placeholder="{{ text_categories }}" id="input-categories" class="form-control" />
              {% if error_categories %}
                <div class="invalid-feedback">{{ error_categories }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-expiring_lots">{{ text_expiring_lots }}</label>
            <div class="col-sm-10">
              <input type="text" name="expiring_lots" value="{{ expiring_lots }}" placeholder="{{ text_expiring_lots }}" id="input-expiring_lots" class="form-control" />
              {% if error_expiring_lots %}
                <div class="invalid-feedback">{{ error_expiring_lots }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-expiry_options">{{ text_expiry_options }}</label>
            <div class="col-sm-10">
              <input type="text" name="expiry_options" value="{{ expiry_options }}" placeholder="{{ text_expiry_options }}" id="input-expiry_options" class="form-control" />
              {% if error_expiry_options %}
                <div class="invalid-feedback">{{ error_expiry_options }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-export_excel">{{ text_export_excel }}</label>
            <div class="col-sm-10">
              <input type="text" name="export_excel" value="{{ export_excel }}" placeholder="{{ text_export_excel }}" id="input-export_excel" class="form-control" />
              {% if error_export_excel %}
                <div class="invalid-feedback">{{ error_export_excel }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-export_pdf">{{ text_export_pdf }}</label>
            <div class="col-sm-10">
              <input type="text" name="export_pdf" value="{{ export_pdf }}" placeholder="{{ text_export_pdf }}" id="input-export_pdf" class="form-control" />
              {% if error_export_pdf %}
                <div class="invalid-feedback">{{ error_export_pdf }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="invalid-feedback">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-lot_report">{{ text_lot_report }}</label>
            <div class="col-sm-10">
              <input type="text" name="lot_report" value="{{ lot_report }}" placeholder="{{ text_lot_report }}" id="input-lot_report" class="form-control" />
              {% if error_lot_report %}
                <div class="invalid-feedback">{{ error_lot_report }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-manufacturers">{{ text_manufacturers }}</label>
            <div class="col-sm-10">
              <input type="text" name="manufacturers" value="{{ manufacturers }}" placeholder="{{ text_manufacturers }}" id="input-manufacturers" class="form-control" />
              {% if error_manufacturers %}
                <div class="invalid-feedback">{{ error_manufacturers }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-movement_type_options">{{ text_movement_type_options }}</label>
            <div class="col-sm-10">
              <input type="text" name="movement_type_options" value="{{ movement_type_options }}" placeholder="{{ text_movement_type_options }}" id="input-movement_type_options" class="form-control" />
              {% if error_movement_type_options %}
                <div class="invalid-feedback">{{ error_movement_type_options }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-movements">{{ text_movements }}</label>
            <div class="col-sm-10">
              <input type="text" name="movements" value="{{ movements }}" placeholder="{{ text_movements }}" id="input-movements" class="form-control" />
              {% if error_movements %}
                <div class="invalid-feedback">{{ error_movements }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-movements_by_type">{{ text_movements_by_type }}</label>
            <div class="col-sm-10">
              <input type="text" name="movements_by_type" value="{{ movements_by_type }}" placeholder="{{ text_movements_by_type }}" id="input-movements_by_type" class="form-control" />
              {% if error_movements_by_type %}
                <div class="invalid-feedback">{{ error_movements_by_type }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-order">{{ text_order }}</label>
            <div class="col-sm-10">
              <input type="text" name="order" value="{{ order }}" placeholder="{{ text_order }}" id="input-order" class="form-control" />
              {% if error_order %}
                <div class="invalid-feedback">{{ error_order }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-pagination">{{ text_pagination }}</label>
            <div class="col-sm-10">
              <input type="text" name="pagination" value="{{ pagination }}" placeholder="{{ text_pagination }}" id="input-pagination" class="form-control" />
              {% if error_pagination %}
                <div class="invalid-feedback">{{ error_pagination }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-print">{{ text_print }}</label>
            <div class="col-sm-10">
              <input type="text" name="print" value="{{ print }}" placeholder="{{ text_print }}" id="input-print" class="form-control" />
              {% if error_print %}
                <div class="invalid-feedback">{{ error_print }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-product_id">{{ text_product_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="product_id" value="{{ product_id }}" placeholder="{{ text_product_id }}" id="input-product_id" class="form-control" />
              {% if error_product_id %}
                <div class="invalid-feedback">{{ error_product_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-reference_type_options">{{ text_reference_type_options }}</label>
            <div class="col-sm-10">
              <input type="text" name="reference_type_options" value="{{ reference_type_options }}" placeholder="{{ text_reference_type_options }}" id="input-reference_type_options" class="form-control" />
              {% if error_reference_type_options %}
                <div class="invalid-feedback">{{ error_reference_type_options }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-refresh">{{ text_refresh }}</label>
            <div class="col-sm-10">
              <input type="text" name="refresh" value="{{ refresh }}" placeholder="{{ text_refresh }}" id="input-refresh" class="form-control" />
              {% if error_refresh %}
                <div class="invalid-feedback">{{ error_refresh }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-results">{{ text_results }}</label>
            <div class="col-sm-10">
              <input type="text" name="results" value="{{ results }}" placeholder="{{ text_results }}" id="input-results" class="form-control" />
              {% if error_results %}
                <div class="invalid-feedback">{{ error_results }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort">{{ text_sort }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort" value="{{ sort }}" placeholder="{{ text_sort }}" id="input-sort" class="form-control" />
              {% if error_sort %}
                <div class="invalid-feedback">{{ error_sort }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-stock_movements">{{ text_stock_movements }}</label>
            <div class="col-sm-10">
              <input type="text" name="stock_movements" value="{{ stock_movements }}" placeholder="{{ text_stock_movements }}" id="input-stock_movements" class="form-control" />
              {% if error_stock_movements %}
                <div class="invalid-feedback">{{ error_stock_movements }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-summary">{{ text_summary }}</label>
            <div class="col-sm-10">
              <input type="text" name="summary" value="{{ summary }}" placeholder="{{ text_summary }}" id="input-summary" class="form-control" />
              {% if error_summary %}
                <div class="invalid-feedback">{{ error_summary }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="invalid-feedback">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-users">{{ text_users }}</label>
            <div class="col-sm-10">
              <input type="text" name="users" value="{{ users }}" placeholder="{{ text_users }}" id="input-users" class="form-control" />
              {% if error_users %}
                <div class="invalid-feedback">{{ error_users }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}