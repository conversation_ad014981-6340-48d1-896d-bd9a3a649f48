<?php
//  Website: WWW.OpenCartArab.com
//  E-Mail : <EMAIL>

// Heading
$_['heading_title']     = 'بحث';
$_['heading_tag']		= 'وسم - ';

// Text
$_['text_search']       = 'المنتجات التي تفي معايير البحث';
$_['text_keyword']      = ' ادخل كلمة البحث';
$_['text_category']     = 'جميع الأقسام';
$_['text_sub_category'] = 'البحث في الأقسام الفرعية';
$_['text_empty']        = 'لا يوجد أي منتجات تطابق معايير البحث.';
$_['text_quantity']     = 'الكمية :';
$_['text_manufacturer'] = 'الشركة :';
$_['text_model']        = 'النوع :'; 
$_['text_points']       = 'نقاط المكافآت :'; 
$_['text_price']        = 'السعر :'; 
$_['text_tax']          = 'السعر بدون ضريبة :';
$_['text_reviews']      = '(%s التقييمات)';
$_['text_compare']      = 'مقارنة المنتج (%s)';
$_['text_sort']         = 'ترتيب';
$_['text_default']      = 'الافتراضي';
$_['text_name_asc']     = 'الإسم من أ - ي';
$_['text_name_desc']    = 'الإسم من ي - أ';
$_['text_price_asc']    = 'حسب السعر (منخفض &gt; مرتفع)';
$_['text_price_desc']   = 'حسب السعر (مرتفع &gt; منخفض)';
$_['text_rating_asc']   = 'تقييم (منخفض)';
$_['text_rating_desc']  = 'تقييم (مرتفع)';
$_['text_model_asc']    = 'النوع (أ - ي)';
$_['text_model_desc']   = 'النوع (ي - أ)';
$_['text_limit']        = 'عرض';
// Entry
$_['entry_search']      = 'بحث:';
$_['entry_description'] = 'البحث في تفاصيل المنتجات';