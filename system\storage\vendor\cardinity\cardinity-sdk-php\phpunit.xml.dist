<?xml version="1.0" encoding="UTF-8"?>

<!-- http://www.phpunit.de/manual/current/en/appendixes.configuration.html -->
<phpunit
        backupGlobals="false"
        backupStaticAttributes="false"
        colors="true"
        convertErrorsToExceptions="true"
        convertNoticesToExceptions="true"
        convertWarningsToExceptions="true"
        processIsolation="false"
        stopOnFailure="false"
        syntaxCheck="false"
        bootstrap="vendor/autoload.php"
        mapTestClassNameToCoveredClassName="true"
        forceCoversAnnotation="false">

    <php>
        <!-- For integration tests -->
        <const name="CONSUMER_KEY" value=""/>
        <const name="CONSUMER_SECRET" value=""/>
    </php>

    <testsuites>
        <testsuite name="Default">
            <directory>./tests</directory>
        </testsuite>
    </testsuites>

    <filter>
        <whitelist>
            <directory>./src</directory>
        </whitelist>
    </filter>
</phpunit>
