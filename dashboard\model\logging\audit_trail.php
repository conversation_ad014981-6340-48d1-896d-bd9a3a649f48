<?php
/**
 * مسار التدقيق - AYM ERP
 * Audit Trail Model
 * 
 * @package    AYM ERP
 * <AUTHOR> Team
 * @copyright  2025 AYM ERP
 * @license    Commercial
 * @version    2.0.0
 */

class ModelLoggingAuditTrail extends Model {
    
    /**
     * تسجيل حدث تدقيق
     */
    public function logAuditEvent($data) {
        $sql = "INSERT INTO " . DB_PREFIX . "audit_trail SET
                event_type = '" . $this->db->escape($data['event_type']) . "',
                table_name = '" . $this->db->escape($data['table_name']) . "',
                record_id = '" . (int)($data['record_id'] ?? 0) . "',
                action = '" . $this->db->escape($data['action']) . "',
                old_values = '" . $this->db->escape(json_encode($data['old_values'] ?? array())) . "',
                new_values = '" . $this->db->escape(json_encode($data['new_values'] ?? array())) . "',
                user_id = '" . (int)($data['user_id'] ?? $this->user->getId()) . "',
                ip_address = '" . $this->db->escape($data['ip_address'] ?? $this->request->server['REMOTE_ADDR'] ?? '') . "',
                user_agent = '" . $this->db->escape($data['user_agent'] ?? $this->request->server['HTTP_USER_AGENT'] ?? '') . "',
                session_id = '" . $this->db->escape($this->session->getId()) . "',
                created_at = NOW()";
        
        $this->db->query($sql);
        
        return $this->db->getLastId();
    }
    
    /**
     * تسجيل تغيير في البيانات
     */
    public function logDataChange($table_name, $record_id, $old_data, $new_data, $action = 'update') {
        $changes = $this->detectChanges($old_data, $new_data);
        
        if (!empty($changes)) {
            $audit_data = array(
                'event_type' => 'data_change',
                'table_name' => $table_name,
                'record_id' => $record_id,
                'action' => $action,
                'old_values' => $old_data,
                'new_values' => $new_data
            );
            
            return $this->logAuditEvent($audit_data);
        }
        
        return false;
    }
    
    /**
     * اكتشاف التغييرات بين البيانات القديمة والجديدة
     */
    private function detectChanges($old_data, $new_data) {
        $changes = array();
        
        // فحص القيم الجديدة أو المحدثة
        foreach ($new_data as $key => $new_value) {
            $old_value = isset($old_data[$key]) ? $old_data[$key] : null;
            
            if ($old_value !== $new_value) {
                $changes[$key] = array(
                    'old' => $old_value,
                    'new' => $new_value
                );
            }
        }
        
        // فحص القيم المحذوفة
        foreach ($old_data as $key => $old_value) {
            if (!isset($new_data[$key])) {
                $changes[$key] = array(
                    'old' => $old_value,
                    'new' => null
                );
            }
        }
        
        return $changes;
    }
    
    /**
     * تسجيل محاولة دخول
     */
    public function logLoginAttempt($username, $success, $ip_address = '', $user_agent = '') {
        $audit_data = array(
            'event_type' => 'login_attempt',
            'table_name' => 'user',
            'action' => $success ? 'login_success' : 'login_failed',
            'new_values' => array(
                'username' => $username,
                'success' => $success
            ),
            'ip_address' => $ip_address ?: ($this->request->server['REMOTE_ADDR'] ?? ''),
            'user_agent' => $user_agent ?: ($this->request->server['HTTP_USER_AGENT'] ?? '')
        );
        
        return $this->logAuditEvent($audit_data);
    }
    
    /**
     * تسجيل تغيير كلمة المرور
     */
    public function logPasswordChange($user_id) {
        $audit_data = array(
            'event_type' => 'security',
            'table_name' => 'user',
            'record_id' => $user_id,
            'action' => 'password_change',
            'user_id' => $user_id
        );
        
        return $this->logAuditEvent($audit_data);
    }
    
    /**
     * تسجيل تغيير الصلاحيات
     */
    public function logPermissionChange($user_id, $old_permissions, $new_permissions) {
        $audit_data = array(
            'event_type' => 'security',
            'table_name' => 'user_group',
            'record_id' => $user_id,
            'action' => 'permission_change',
            'old_values' => $old_permissions,
            'new_values' => $new_permissions
        );
        
        return $this->logAuditEvent($audit_data);
    }
    
    /**
     * الحصول على سجل التدقيق
     */
    public function getAuditTrail($filters = array(), $limit = 50, $offset = 0) {
        $sql = "SELECT at.*, u.firstname, u.lastname, u.username
                FROM " . DB_PREFIX . "audit_trail at
                LEFT JOIN " . DB_PREFIX . "user u ON (at.user_id = u.user_id)
                WHERE 1=1";
        
        // تطبيق الفلاتر
        if (!empty($filters['event_type'])) {
            $sql .= " AND at.event_type = '" . $this->db->escape($filters['event_type']) . "'";
        }
        
        if (!empty($filters['table_name'])) {
            $sql .= " AND at.table_name = '" . $this->db->escape($filters['table_name']) . "'";
        }
        
        if (!empty($filters['user_id'])) {
            $sql .= " AND at.user_id = '" . (int)$filters['user_id'] . "'";
        }
        
        if (!empty($filters['action'])) {
            $sql .= " AND at.action = '" . $this->db->escape($filters['action']) . "'";
        }
        
        if (!empty($filters['date_from'])) {
            $sql .= " AND DATE(at.created_at) >= '" . $this->db->escape($filters['date_from']) . "'";
        }
        
        if (!empty($filters['date_to'])) {
            $sql .= " AND DATE(at.created_at) <= '" . $this->db->escape($filters['date_to']) . "'";
        }
        
        if (!empty($filters['ip_address'])) {
            $sql .= " AND at.ip_address = '" . $this->db->escape($filters['ip_address']) . "'";
        }
        
        $sql .= " ORDER BY at.created_at DESC";
        
        if ($limit > 0) {
            $sql .= " LIMIT " . (int)$offset . ", " . (int)$limit;
        }
        
        $query = $this->db->query($sql);
        
        return $query->rows;
    }
    
    /**
     * الحصول على إجمالي عدد السجلات
     */
    public function getTotalAuditRecords($filters = array()) {
        $sql = "SELECT COUNT(*) as total
                FROM " . DB_PREFIX . "audit_trail at
                WHERE 1=1";
        
        // تطبيق نفس الفلاتر
        if (!empty($filters['event_type'])) {
            $sql .= " AND at.event_type = '" . $this->db->escape($filters['event_type']) . "'";
        }
        
        if (!empty($filters['table_name'])) {
            $sql .= " AND at.table_name = '" . $this->db->escape($filters['table_name']) . "'";
        }
        
        if (!empty($filters['user_id'])) {
            $sql .= " AND at.user_id = '" . (int)$filters['user_id'] . "'";
        }
        
        if (!empty($filters['action'])) {
            $sql .= " AND at.action = '" . $this->db->escape($filters['action']) . "'";
        }
        
        if (!empty($filters['date_from'])) {
            $sql .= " AND DATE(at.created_at) >= '" . $this->db->escape($filters['date_from']) . "'";
        }
        
        if (!empty($filters['date_to'])) {
            $sql .= " AND DATE(at.created_at) <= '" . $this->db->escape($filters['date_to']) . "'";
        }
        
        if (!empty($filters['ip_address'])) {
            $sql .= " AND at.ip_address = '" . $this->db->escape($filters['ip_address']) . "'";
        }
        
        $query = $this->db->query($sql);
        
        return $query->row['total'];
    }
    
    /**
     * الحصول على إحصائيات التدقيق
     */
    public function getAuditStatistics($period = 'today') {
        $stats = array();
        
        $date_condition = '';
        switch ($period) {
            case 'today':
                $date_condition = "DATE(created_at) = CURDATE()";
                break;
            case 'week':
                $date_condition = "created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)";
                break;
            case 'month':
                $date_condition = "created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)";
                break;
            default:
                $date_condition = "1=1";
        }
        
        // إجمالي الأحداث
        $sql = "SELECT COUNT(*) as total FROM " . DB_PREFIX . "audit_trail WHERE " . $date_condition;
        $query = $this->db->query($sql);
        $stats['total_events'] = $query->row['total'];
        
        // محاولات الدخول الفاشلة
        $sql = "SELECT COUNT(*) as failed_logins FROM " . DB_PREFIX . "audit_trail 
                WHERE action = 'login_failed' AND " . $date_condition;
        $query = $this->db->query($sql);
        $stats['failed_logins'] = $query->row['failed_logins'];
        
        // تغييرات البيانات
        $sql = "SELECT COUNT(*) as data_changes FROM " . DB_PREFIX . "audit_trail 
                WHERE event_type = 'data_change' AND " . $date_condition;
        $query = $this->db->query($sql);
        $stats['data_changes'] = $query->row['data_changes'];
        
        // الأحداث الأمنية
        $sql = "SELECT COUNT(*) as security_events FROM " . DB_PREFIX . "audit_trail 
                WHERE event_type = 'security' AND " . $date_condition;
        $query = $this->db->query($sql);
        $stats['security_events'] = $query->row['security_events'];
        
        return $stats;
    }
    
    /**
     * اكتشاف الأنشطة المشبوهة
     */
    public function detectSuspiciousActivity() {
        $suspicious = array();
        
        // محاولات دخول متعددة فاشلة من نفس IP
        $sql = "SELECT ip_address, COUNT(*) as attempts
                FROM " . DB_PREFIX . "audit_trail
                WHERE action = 'login_failed'
                AND created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
                GROUP BY ip_address
                HAVING attempts >= 5";
        
        $query = $this->db->query($sql);
        
        foreach ($query->rows as $row) {
            $suspicious[] = array(
                'type' => 'multiple_failed_logins',
                'description' => 'محاولات دخول متعددة فاشلة من IP: ' . $row['ip_address'],
                'severity' => 'high',
                'count' => $row['attempts']
            );
        }
        
        // تغييرات متعددة في البيانات الحساسة
        $sql = "SELECT user_id, COUNT(*) as changes
                FROM " . DB_PREFIX . "audit_trail
                WHERE event_type = 'data_change'
                AND table_name IN ('user', 'user_group', 'setting')
                AND created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
                GROUP BY user_id
                HAVING changes >= 10";
        
        $query = $this->db->query($sql);
        
        foreach ($query->rows as $row) {
            $suspicious[] = array(
                'type' => 'excessive_data_changes',
                'description' => 'تغييرات مفرطة في البيانات الحساسة من المستخدم: ' . $row['user_id'],
                'severity' => 'medium',
                'count' => $row['changes']
            );
        }
        
        return $suspicious;
    }
    
    /**
     * تنظيف سجلات التدقيق القديمة
     */
    public function cleanupOldLogs($days = 365) {
        $sql = "DELETE FROM " . DB_PREFIX . "audit_trail 
                WHERE created_at < DATE_SUB(NOW(), INTERVAL " . (int)$days . " DAY)";
        
        $this->db->query($sql);
        
        return $this->db->countAffected();
    }
    
    /**
     * تصدير سجل التدقيق
     */
    public function exportAuditTrail($filters = array(), $format = 'csv') {
        $data = $this->getAuditTrail($filters, 0); // بدون حد أقصى
        
        if ($format == 'csv') {
            return $this->exportToCsv($data);
        } elseif ($format == 'json') {
            return json_encode($data);
        }
        
        return $data;
    }
    
    /**
     * تصدير إلى CSV
     */
    private function exportToCsv($data) {
        $csv = "التاريخ,المستخدم,نوع الحدث,الجدول,الإجراء,عنوان IP\n";
        
        foreach ($data as $row) {
            $csv .= '"' . $row['created_at'] . '",';
            $csv .= '"' . ($row['firstname'] . ' ' . $row['lastname']) . '",';
            $csv .= '"' . $row['event_type'] . '",';
            $csv .= '"' . $row['table_name'] . '",';
            $csv .= '"' . $row['action'] . '",';
            $csv .= '"' . $row['ip_address'] . '"' . "\n";
        }
        
        return $csv;
    }
}
?>
