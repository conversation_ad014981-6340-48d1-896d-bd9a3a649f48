<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/cloud/translate/v3/translation_service.proto

namespace Google\Cloud\Translate\V3\Glossary;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Used with equivalent term set glossaries.
 *
 * Generated from protobuf message <code>google.cloud.translation.v3.Glossary.LanguageCodesSet</code>
 */
class LanguageCodesSet extends \Google\Protobuf\Internal\Message
{
    /**
     * The BCP-47 language code(s) for terms defined in the glossary.
     * All entries are unique. The list contains at least two entries.
     * Expected to be an exact match for GlossaryTerm.language_code.
     *
     * Generated from protobuf field <code>repeated string language_codes = 1;</code>
     */
    private $language_codes;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string[]|\Google\Protobuf\Internal\RepeatedField $language_codes
     *           The BCP-47 language code(s) for terms defined in the glossary.
     *           All entries are unique. The list contains at least two entries.
     *           Expected to be an exact match for GlossaryTerm.language_code.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Cloud\Translate\V3\TranslationService::initOnce();
        parent::__construct($data);
    }

    /**
     * The BCP-47 language code(s) for terms defined in the glossary.
     * All entries are unique. The list contains at least two entries.
     * Expected to be an exact match for GlossaryTerm.language_code.
     *
     * Generated from protobuf field <code>repeated string language_codes = 1;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getLanguageCodes()
    {
        return $this->language_codes;
    }

    /**
     * The BCP-47 language code(s) for terms defined in the glossary.
     * All entries are unique. The list contains at least two entries.
     * Expected to be an exact match for GlossaryTerm.language_code.
     *
     * Generated from protobuf field <code>repeated string language_codes = 1;</code>
     * @param string[]|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setLanguageCodes($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::STRING);
        $this->language_codes = $arr;

        return $this;
    }

}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(LanguageCodesSet::class, \Google\Cloud\Translate\V3\Glossary_LanguageCodesSet::class);

