{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="submit" form="workflow\actions-form" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fa fa-save"></i></button>
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="close" data-dismiss="alert">&times;</button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="close" data-dismiss="alert">&times;</button>
      </div>
    {{% endif %}}

    <div class="panel panel-default">
      <div class="panel-heading"><i class="fa fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}bar-chart{% elif screen_type == 'settings' %}cog{% else %}pencil{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="panel-body">
        <form id="workflow\actions-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-action_builder">{{ text_action_builder }}</label>
            <div class="col-sm-10">
              <input type="text" name="action_builder" value="{{ action_builder }}" placeholder="{{ text_action_builder }}" id="input-action_builder" class="form-control" />
              {% if error_action_builder %}
                <div class="text-danger">{{ error_action_builder }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-action_components">{{ text_action_components }}</label>
            <div class="col-sm-10">
              <input type="text" name="action_components" value="{{ action_components }}" placeholder="{{ text_action_components }}" id="input-action_components" class="form-control" />
              {% if error_action_components %}
                <div class="text-danger">{{ error_action_components }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-action_library">{{ text_action_library }}</label>
            <div class="col-sm-10">
              <input type="text" name="action_library" value="{{ action_library }}" placeholder="{{ text_action_library }}" id="input-action_library" class="form-control" />
              {% if error_action_library %}
                <div class="text-danger">{{ error_action_library }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-action_stats">{{ text_action_stats }}</label>
            <div class="col-sm-10">
              <input type="text" name="action_stats" value="{{ action_stats }}" placeholder="{{ text_action_stats }}" id="input-action_stats" class="form-control" />
              {% if error_action_stats %}
                <div class="text-danger">{{ error_action_stats }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-action_templates">{{ text_action_templates }}</label>
            <div class="col-sm-10">
              <input type="text" name="action_templates" value="{{ action_templates }}" placeholder="{{ text_action_templates }}" id="input-action_templates" class="form-control" />
              {% if error_action_templates %}
                <div class="text-danger">{{ error_action_templates }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-actions">{{ text_actions }}</label>
            <div class="col-sm-10">
              <input type="text" name="actions" value="{{ actions }}" placeholder="{{ text_actions }}" id="input-actions" class="form-control" />
              {% if error_actions %}
                <div class="text-danger">{{ error_actions }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-add">{{ text_add }}</label>
            <div class="col-sm-10">
              <input type="text" name="add" value="{{ add }}" placeholder="{{ text_add }}" id="input-add" class="form-control" />
              {% if error_add %}
                <div class="text-danger">{{ error_add }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-back">{{ text_back }}</label>
            <div class="col-sm-10">
              <input type="text" name="back" value="{{ back }}" placeholder="{{ text_back }}" id="input-back" class="form-control" />
              {% if error_back %}
                <div class="text-danger">{{ error_back }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-builder_config">{{ text_builder_config }}</label>
            <div class="col-sm-10">
              <input type="text" name="builder_config" value="{{ builder_config }}" placeholder="{{ text_builder_config }}" id="input-builder_config" class="form-control" />
              {% if error_builder_config %}
                <div class="text-danger">{{ error_builder_config }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="text-danger">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-import_export">{{ text_import_export }}</label>
            <div class="col-sm-10">
              <input type="text" name="import_export" value="{{ import_export }}" placeholder="{{ text_import_export }}" id="input-import_export" class="form-control" />
              {% if error_import_export %}
                <div class="text-danger">{{ error_import_export }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-monitoring">{{ text_monitoring }}</label>
            <div class="col-sm-10">
              <input type="text" name="monitoring" value="{{ monitoring }}" placeholder="{{ text_monitoring }}" id="input-monitoring" class="form-control" />
              {% if error_monitoring %}
                <div class="text-danger">{{ error_monitoring }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-performance_metrics">{{ text_performance_metrics }}</label>
            <div class="col-sm-10">
              <input type="text" name="performance_metrics" value="{{ performance_metrics }}" placeholder="{{ text_performance_metrics }}" id="input-performance_metrics" class="form-control" />
              {% if error_performance_metrics %}
                <div class="text-danger">{{ error_performance_metrics }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-popular_actions">{{ text_popular_actions }}</label>
            <div class="col-sm-10">
              <input type="text" name="popular_actions" value="{{ popular_actions }}" placeholder="{{ text_popular_actions }}" id="input-popular_actions" class="form-control" />
              {% if error_popular_actions %}
                <div class="text-danger">{{ error_popular_actions }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-preview_action">{{ text_preview_action }}</label>
            <div class="col-sm-10">
              <input type="text" name="preview_action" value="{{ preview_action }}" placeholder="{{ text_preview_action }}" id="input-preview_action" class="form-control" />
              {% if error_preview_action %}
                <div class="text-danger">{{ error_preview_action }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-recent_actions">{{ text_recent_actions }}</label>
            <div class="col-sm-10">
              <input type="text" name="recent_actions" value="{{ recent_actions }}" placeholder="{{ text_recent_actions }}" id="input-recent_actions" class="form-control" />
              {% if error_recent_actions %}
                <div class="text-danger">{{ error_recent_actions }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-save_action">{{ text_save_action }}</label>
            <div class="col-sm-10">
              <input type="text" name="save_action" value="{{ save_action }}" placeholder="{{ text_save_action }}" id="input-save_action" class="form-control" />
              {% if error_save_action %}
                <div class="text-danger">{{ error_save_action }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-settings">{{ text_settings }}</label>
            <div class="col-sm-10">
              <input type="text" name="settings" value="{{ settings }}" placeholder="{{ text_settings }}" id="input-settings" class="form-control" />
              {% if error_settings %}
                <div class="text-danger">{{ error_settings }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-system_variables">{{ text_system_variables }}</label>
            <div class="col-sm-10">
              <input type="text" name="system_variables" value="{{ system_variables }}" placeholder="{{ text_system_variables }}" id="input-system_variables" class="form-control" />
              {% if error_system_variables %}
                <div class="text-danger">{{ error_system_variables }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-test_action">{{ text_test_action }}</label>
            <div class="col-sm-10">
              <input type="text" name="test_action" value="{{ test_action }}" placeholder="{{ text_test_action }}" id="input-test_action" class="form-control" />
              {% if error_test_action %}
                <div class="text-danger">{{ error_test_action }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-total">{{ text_total }}</label>
            <div class="col-sm-10">
              <input type="text" name="total" value="{{ total }}" placeholder="{{ text_total }}" id="input-total" class="form-control" />
              {% if error_total %}
                <div class="text-danger">{{ error_total }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="text-danger">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}