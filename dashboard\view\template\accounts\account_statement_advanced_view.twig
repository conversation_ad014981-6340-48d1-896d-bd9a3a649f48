{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <a href="{{ export_excel }}" data-toggle="tooltip" title="{{ button_export_excel }}" class="btn btn-success"><i class="fa fa-file-excel-o"></i></a>
        <a href="{{ export_pdf }}" data-toggle="tooltip" title="{{ button_export_pdf }}" class="btn btn-danger"><i class="fa fa-file-pdf-o"></i></a>
        <a href="{{ export_csv }}" data-toggle="tooltip" title="{{ button_export_csv }}" class="btn btn-info"><i class="fa fa-file-text-o"></i></a>
        <a href="{{ print }}" data-toggle="tooltip" title="{{ button_print }}" class="btn btn-warning" target="_blank"><i class="fa fa-print"></i></a>
        <a href="{{ back }}" data-toggle="tooltip" title="{{ button_back }}" class="btn btn-default"><i class="fa fa-reply"></i></a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if success %}
    <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    
    <!-- معلومات الحساب -->
    <div class="panel panel-primary">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-info-circle"></i> {{ text_account_info }}</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-md-6">
            <table class="table table-striped">
              <tr>
                <td><strong>{{ text_account_code }}:</strong></td>
                <td>{{ account.code }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_account_name }}:</strong></td>
                <td>{{ account.name }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_account_type }}:</strong></td>
                <td>
                  <span class="label label-{% if account.type == 'asset' %}primary{% elseif account.type == 'liability' %}danger{% elseif account.type == 'equity' %}warning{% elseif account.type == 'revenue' %}success{% else %}info{% endif %}">
                    {{ account.type_name }}
                  </span>
                </td>
              </tr>
            </table>
          </div>
          <div class="col-md-6">
            <table class="table table-striped">
              <tr>
                <td><strong>{{ text_statement_period }}:</strong></td>
                <td>{{ date_start }} {{ text_to }} {{ date_end }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_generated_on }}:</strong></td>
                <td>{{ generated_date }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_generated_by }}:</strong></td>
                <td>{{ generated_by }}</td>
              </tr>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- ملخص كشف الحساب -->
    <div class="panel panel-success">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-bar-chart"></i> {{ text_statement_summary }}</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-md-3">
            <div class="info-box bg-blue">
              <span class="info-box-icon"><i class="fa fa-play"></i></span>
              <div class="info-box-content">
                <span class="info-box-text">{{ text_opening_balance }}</span>
                <span class="info-box-number">{{ summary.opening_balance }}</span>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="info-box bg-green">
              <span class="info-box-icon"><i class="fa fa-arrow-up"></i></span>
              <div class="info-box-content">
                <span class="info-box-text">{{ text_total_debit }}</span>
                <span class="info-box-number">{{ summary.total_debit }}</span>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="info-box bg-yellow">
              <span class="info-box-icon"><i class="fa fa-arrow-down"></i></span>
              <div class="info-box-content">
                <span class="info-box-text">{{ text_total_credit }}</span>
                <span class="info-box-number">{{ summary.total_credit }}</span>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="info-box bg-red">
              <span class="info-box-icon"><i class="fa fa-stop"></i></span>
              <div class="info-box-content">
                <span class="info-box-text">{{ text_closing_balance }}</span>
                <span class="info-box-number">{{ summary.closing_balance }}</span>
              </div>
            </div>
          </div>
        </div>
        
        <div class="row">
          <div class="col-md-6">
            <table class="table table-bordered">
              <tr>
                <td><strong>{{ text_net_movement }}:</strong></td>
                <td class="text-right{% if summary.net_movement >= 0 %} text-success{% else %} text-danger{% endif %}">
                  {{ summary.net_movement }}
                </td>
              </tr>
              <tr>
                <td><strong>{{ text_balance_change }}:</strong></td>
                <td class="text-right{% if summary.balance_change >= 0 %} text-success{% else %} text-danger{% endif %}">
                  {{ summary.balance_change }}
                </td>
              </tr>
              <tr>
                <td><strong>{{ text_percentage_change }}:</strong></td>
                <td class="text-right{% if summary.percentage_change >= 0 %} text-success{% else %} text-danger{% endif %}">
                  {{ summary.percentage_change }}%
                </td>
              </tr>
            </table>
          </div>
          <div class="col-md-6">
            <table class="table table-bordered">
              <tr>
                <td><strong>{{ text_transaction_count }}:</strong></td>
                <td class="text-right">{{ summary.transaction_count }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_average_transaction }}:</strong></td>
                <td class="text-right">{{ summary.average_transaction }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_activity_rate }}:</strong></td>
                <td class="text-right">{{ summary.activity_rate }}%</td>
              </tr>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- رسم بياني للرصيد -->
    {% if chart_data %}
    <div class="panel panel-info">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-line-chart"></i> {{ text_balance_chart }}</h3>
      </div>
      <div class="panel-body">
        <canvas id="balance-chart" width="400" height="150"></canvas>
      </div>
    </div>
    {% endif %}

    <!-- تفاصيل المعاملات -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-list"></i> {{ text_statement_details }}</h3>
      </div>
      <div class="panel-body">
        <div class="table-responsive">
          <table class="table table-bordered table-hover" id="statement-table">
            <thead>
              <tr>
                <th>{{ column_date }}</th>
                <th>{{ column_description }}</th>
                <th>{{ column_reference }}</th>
                <th class="text-right">{{ column_debit }}</th>
                <th class="text-right">{{ column_credit }}</th>
                <th class="text-right">{{ column_running_balance }}</th>
              </tr>
            </thead>
            <tbody>
              {% if include_opening and summary.opening_balance != '0.00' %}
              <tr class="info">
                <td>{{ date_start }}</td>
                <td><strong>{{ text_opening_balance }}</strong></td>
                <td>-</td>
                <td class="text-right">-</td>
                <td class="text-right">-</td>
                <td class="text-right"><strong>{{ summary.opening_balance }}</strong></td>
              </tr>
              {% endif %}
              
              {% for transaction in transactions %}
              <tr>
                <td>{{ transaction.date }}</td>
                <td>{{ transaction.description }}</td>
                <td>{{ transaction.reference }}</td>
                <td class="text-right{% if transaction.debit > 0 %} text-danger{% endif %}">
                  {% if transaction.debit > 0 %}{{ transaction.debit }}{% endif %}
                </td>
                <td class="text-right{% if transaction.credit > 0 %} text-success{% endif %}">
                  {% if transaction.credit > 0 %}{{ transaction.credit }}{% endif %}
                </td>
                <td class="text-right">{{ transaction.running_balance }}</td>
              </tr>
              {% endfor %}
              
              {% if include_closing %}
              <tr class="warning">
                <td>{{ date_end }}</td>
                <td><strong>{{ text_closing_balance }}</strong></td>
                <td>-</td>
                <td class="text-right">-</td>
                <td class="text-right">-</td>
                <td class="text-right"><strong>{{ summary.closing_balance }}</strong></td>
              </tr>
              {% endif %}
            </tbody>
            <tfoot>
              <tr class="active">
                <th colspan="3">{{ text_total }}</th>
                <th class="text-right">{{ summary.total_debit }}</th>
                <th class="text-right">{{ summary.total_credit }}</th>
                <th class="text-right">{{ summary.closing_balance }}</th>
              </tr>
            </tfoot>
          </table>
        </div>
      </div>
    </div>

    <!-- التحليل المتقدم -->
    {% if analysis %}
    <div class="panel panel-warning">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-analytics"></i> {{ text_advanced_analysis }}</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-md-6">
            <h4>{{ text_statistics }}</h4>
            <table class="table table-striped">
              <tr>
                <td>{{ text_average_balance }}:</td>
                <td class="text-right">{{ analysis.average_balance }}</td>
              </tr>
              <tr>
                <td>{{ text_minimum_balance }}:</td>
                <td class="text-right">{{ analysis.minimum_balance }}</td>
              </tr>
              <tr>
                <td>{{ text_maximum_balance }}:</td>
                <td class="text-right">{{ analysis.maximum_balance }}</td>
              </tr>
              <tr>
                <td>{{ text_largest_transaction }}:</td>
                <td class="text-right">{{ analysis.largest_transaction }}</td>
              </tr>
              <tr>
                <td>{{ text_smallest_transaction }}:</td>
                <td class="text-right">{{ analysis.smallest_transaction }}</td>
              </tr>
              <tr>
                <td>{{ text_volatility }}:</td>
                <td class="text-right">{{ analysis.volatility }}%</td>
              </tr>
            </table>
          </div>
          <div class="col-md-6">
            <h4>{{ text_activity_analysis }}</h4>
            <table class="table table-striped">
              <tr>
                <td>{{ text_active_days }}:</td>
                <td class="text-right">{{ analysis.active_days }}</td>
              </tr>
              <tr>
                <td>{{ text_inactive_days }}:</td>
                <td class="text-right">{{ analysis.inactive_days }}</td>
              </tr>
              <tr>
                <td>{{ text_activity_rate }}:</td>
                <td class="text-right">{{ analysis.activity_rate }}%</td>
              </tr>
            </table>
            
            {% if analysis.trend %}
            <h4>{{ text_trend_analysis }}</h4>
            <div class="alert alert-{% if analysis.trend == 'up' %}success{% elseif analysis.trend == 'down' %}danger{% else %}info{% endif %}">
              <i class="fa fa-{% if analysis.trend == 'up' %}arrow-up{% elseif analysis.trend == 'down' %}arrow-down{% else %}minus{% endif %}"></i>
              {% if analysis.trend == 'up' %}
                {{ text_improvement }}
              {% elseif analysis.trend == 'down' %}
                {{ text_deterioration }}
              {% else %}
                {{ text_stable }}
              {% endif %}
            </div>
            {% endif %}
          </div>
        </div>
      </div>
    </div>
    {% endif %}

    <!-- مقارنة الفترات -->
    {% if comparison %}
    <div class="panel panel-primary">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-exchange"></i> {{ text_period_comparison }}</h3>
      </div>
      <div class="panel-body">
        <div class="table-responsive">
          <table class="table table-bordered">
            <thead>
              <tr>
                <th>{{ text_metric }}</th>
                <th class="text-right">{{ text_previous_period }}</th>
                <th class="text-right">{{ text_current_period }}</th>
                <th class="text-right">{{ text_variance }}</th>
                <th class="text-right">{{ text_variance_percentage }}</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>{{ text_opening_balance }}</td>
                <td class="text-right">{{ comparison.previous.opening_balance }}</td>
                <td class="text-right">{{ comparison.current.opening_balance }}</td>
                <td class="text-right{% if comparison.variance.opening_balance >= 0 %} text-success{% else %} text-danger{% endif %}">
                  {{ comparison.variance.opening_balance }}
                </td>
                <td class="text-right{% if comparison.variance_percentage.opening_balance >= 0 %} text-success{% else %} text-danger{% endif %}">
                  {{ comparison.variance_percentage.opening_balance }}%
                </td>
              </tr>
              <tr>
                <td>{{ text_closing_balance }}</td>
                <td class="text-right">{{ comparison.previous.closing_balance }}</td>
                <td class="text-right">{{ comparison.current.closing_balance }}</td>
                <td class="text-right{% if comparison.variance.closing_balance >= 0 %} text-success{% else %} text-danger{% endif %}">
                  {{ comparison.variance.closing_balance }}
                </td>
                <td class="text-right{% if comparison.variance_percentage.closing_balance >= 0 %} text-success{% else %} text-danger{% endif %}">
                  {{ comparison.variance_percentage.closing_balance }}%
                </td>
              </tr>
              <tr>
                <td>{{ text_transaction_count }}</td>
                <td class="text-right">{{ comparison.previous.transaction_count }}</td>
                <td class="text-right">{{ comparison.current.transaction_count }}</td>
                <td class="text-right{% if comparison.variance.transaction_count >= 0 %} text-success{% else %} text-danger{% endif %}">
                  {{ comparison.variance.transaction_count }}
                </td>
                <td class="text-right{% if comparison.variance_percentage.transaction_count >= 0 %} text-success{% else %} text-danger{% endif %}">
                  {{ comparison.variance_percentage.transaction_count }}%
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
    {% endif %}

    <!-- معلومات الامتثال -->
    <div class="panel panel-success">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-check-circle"></i> {{ text_compliance_info }}</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-md-4">
            <div class="alert alert-success">
              <i class="fa fa-check"></i> {{ text_eas_compliant }}
            </div>
          </div>
          <div class="col-md-4">
            <div class="alert alert-info">
              <i class="fa fa-globe"></i> {{ text_eta_ready }}
            </div>
          </div>
          <div class="col-md-4">
            <div class="alert alert-warning">
              <i class="fa fa-balance-scale"></i> {{ text_egyptian_gaap }}
            </div>
          </div>
        </div>
      </div>
    </div>

  </div>
</div>

<script type="text/javascript"><!--
// DataTables initialization
$('#statement-table').DataTable({
    "language": {
        "url": "{{ datatable_language }}"
    },
    "order": [[ 0, "asc" ]],
    "pageLength": 25,
    "dom": 'Bfrtip',
    "buttons": [
        'copy', 'csv', 'excel', 'pdf', 'print'
    ]
});

// Balance Chart
{% if chart_data %}
var ctx = document.getElementById('balance-chart').getContext('2d');
var balanceChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: {{ chart_data.labels|json_encode|raw }},
        datasets: [{
            label: '{{ text_balance }}',
            data: {{ chart_data.data|json_encode|raw }},
            borderColor: '#3c8dbc',
            backgroundColor: 'rgba(60, 141, 188, 0.1)',
            fill: true
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: false
            }
        }
    }
});
{% endif %}

// Tooltips
$('[data-toggle="tooltip"]').tooltip();
//--></script>

{{ footer }}
