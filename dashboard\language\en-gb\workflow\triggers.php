<?php
// Heading
$_['heading_title'] = 'Workflow Triggers';

// Text
$_['text_success'] = 'Success: You have modified workflow triggers!';

// All variables (existing + new from 36 direct texts)
$_['text_schedule_trigger_desc'] = 'Run at specific times (daily, weekly, monthly)';
$_['text_interval_trigger_desc'] = 'Run every specific time interval';
$_['text_interval_example_5min'] = 'every 5 minutes';
$_['text_interval_example_hour'] = 'every hour';
$_['text_interval_example_day'] = 'every day';
$_['text_delay_trigger_desc'] = 'Delay execution for specific period';
$_['text_delay_example_10min'] = 'after 10 minutes';
$_['text_delay_example_hour'] = 'after 1 hour';
$_['text_delay_example_day'] = 'after 1 day';
$_['text_database_change_desc'] = 'Run when database changes';
$_['text_db_example_add_product'] = 'add new product';
$_['text_db_example_update_inventory'] = 'update inventory';
$_['text_db_example_delete_customer'] = 'delete customer';
$_['text_user_action_desc'] = 'Run when specific user action occurs';
$_['text_user_example_login'] = 'user login';
$_['text_user_example_create_order'] = 'create order';
$_['text_user_example_approve_document'] = 'approve document';
$_['text_system_event_desc'] = 'Run when system event occurs';
$_['text_system_example_error'] = 'system error';
$_['text_system_example_out_of_stock'] = 'out of stock';
$_['text_system_example_exceed_limit'] = 'exceed allowed limit';
$_['text_webhook_trigger_desc'] = 'Run when receiving webhook from external system';
$_['text_webhook_example_ecommerce'] = 'order from e-commerce store';
$_['text_webhook_example_crm'] = 'update from CRM';
$_['text_webhook_example_bank'] = 'notification from bank';
$_['text_api_call_desc'] = 'Run when specific API is called';
$_['text_email_received_desc'] = 'Run when email is received';
$_['text_email_example_support'] = 'support request';
$_['text_email_example_supplier_invoice'] = 'invoice from supplier';
$_['text_email_example_order_confirmation'] = 'order confirmation';
$_['text_file_upload_desc'] = 'Run when file is uploaded';
$_['text_file_example_invoice'] = 'upload invoice';
$_['text_file_example_import_data'] = 'import data';
$_['text_file_example_update_catalog'] = 'update catalog';
$_['text_ai_prediction_desc'] = 'Run when specific AI prediction occurs';
$_['text_ai_example_stock_out'] = 'predict stock out';
$_['text_ai_example_demand_increase'] = 'predict demand increase';
$_['text_ai_example_fraud_detection'] = 'fraud detection';
$_['text_pattern_detection_desc'] = 'Run when specific pattern is detected';
$_['text_pattern_example_unusual_purchase'] = 'unusual purchase pattern';
$_['text_pattern_example_behavior_change'] = 'behavior change';
$_['text_pattern_example_new_trend'] = 'new trend';
$_['text_anomaly_detection_desc'] = 'Run when anomaly is detected';
$_['text_anomaly_example_suspicious_transaction'] = 'suspicious transaction';
$_['text_anomaly_example_abnormal_consumption'] = 'abnormal consumption';
$_['text_anomaly_example_low_performance'] = 'low performance';
$_['text_inventory_level_desc'] = 'Run when inventory reaches specific level';
$_['text_inventory_example_less_than_10'] = 'inventory less than 10';
$_['text_inventory_example_zero'] = 'zero inventory';
$_['text_inventory_example_excess'] = 'excess inventory';
$_['text_sales_target_desc'] = 'Run when sales target is achieved';
$_['text_sales_example_monthly_target'] = 'achieve monthly target';
$_['text_sales_example_exceed_expected'] = 'exceed expected';
$_['text_sales_example_sales_decline'] = 'sales decline';
$_['text_customer_behavior_desc'] = 'Run when specific customer behavior occurs';
$_['text_customer_example_new'] = 'new customer';
$_['text_customer_example_no_purchase'] = 'no purchase for period';
$_['text_customer_example_large_purchase'] = 'large purchase';
$_['text_google_sheets_desc'] = 'Run when Google Sheets changes';
$_['text_sheets_example_add_row'] = 'add new row';
$_['text_sheets_example_update_cell'] = 'update cell';
$_['text_sheets_example_delete_data'] = 'delete data';
$_['text_slack_message_desc'] = 'Run when Slack message is received';
$_['text_slack_example_channel_message'] = 'message in specific channel';
$_['text_slack_example_keyword_mention'] = 'mention keyword';
$_['text_slack_example_user_message'] = 'message from user';
$_['text_whatsapp_message_desc'] = 'Run when WhatsApp message is received';
$_['text_whatsapp_example_customer_message'] = 'message from customer';
$_['text_whatsapp_example_support_request'] = 'support request';
$_['text_whatsapp_example_product_inquiry'] = 'product inquiry';
$_['text_auto_inventory_name'] = 'Automatic inventory management';
$_['text_auto_inventory_desc'] = 'Alert when out of stock and create automatic purchase order';
$_['text_welcome_customers_name'] = 'Welcome new customers';
$_['text_welcome_customers_desc'] = 'Welcome workflow for new customers with welcome messages';
$_['text_ai_demand_prediction_name'] = 'AI demand prediction';
$_['text_ai_demand_prediction_desc'] = 'Analyze sales data and predict future demand';
$_['text_document_approval_name'] = 'Document approval';
$_['text_document_approval_desc'] = 'Document approval workflow with automatic escalation';
$_['text_trigger_action_completed'] = 'Trigger action completed ';
$_['text_trigger_notification_title'] = 'Trigger notification: ';
$_['text_trigger_action_executed'] = 'Action executed on trigger number ';
?>
