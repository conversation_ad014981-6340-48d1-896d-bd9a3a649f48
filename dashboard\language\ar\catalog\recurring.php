<?php
//  Website: WWW.OpenCartArab.com
//  E-Mail : <EMAIL>

// Heading
$_['heading_title']			= 'المدفوعات المتكررة';

// Text
$_['text_success']          = 'تم التعديل !';
$_['text_list']          = 'القائمة';
$_['text_add']           = 'ادراج';
$_['text_edit']          = 'تحرير';
$_['text_day']				= 'يوم';
$_['text_week']				= 'أسبوع';
$_['text_semi_month']		= 'شبه شهري';
$_['text_month']			= 'شهر';
$_['text_year']				= 'سنة';
$_['text_recurring']	    = '<p><i class="fa fa-info-circle"></i> المدفوعات المتكررة هي عبارة عن مدفوعات يتم تحصيلها بشكل تلقائي حسب الدورة والتكرار.</p><p>مثلا: اذا تم تحديد التكرار كل اسبوع والدورة 2, سيتم تحصيل دفعة تلقائية من العميل كل اسبوعين.</p><p>الفترة هي عدد المرات التي سيتم تحصيل دفعات من العميل, ضع القيمة صفر للفترة اذا كنت ترغب بتحصيل الدفعات بشكل مستمر الى ان يتم الغاء التحصيل بشكل يدوي.</p>';
$_['text_profile']			= 'المدفوعات المتكررة';
$_['text_trial']			= 'المدفوعات المتكررة التجريبية';

// Entry
$_['entry_name']			= 'الاسم';
$_['entry_price']			= 'السعر';
$_['entry_duration']		= 'الفترة';
$_['entry_cycle']			= 'الدورة';
$_['entry_frequency']		= 'التكرار';
$_['entry_trial_price']		= 'سعر تجريبي';
$_['entry_trial_duration']	= 'فترة تجريبية';
$_['entry_trial_status']	= 'حالة تجريبية';
$_['entry_trial_cycle']	    = 'دورة تجريبية';
$_['entry_trial_frequency']	= 'تكرار تجريبي';
$_['entry_status']			= 'الحالة';
$_['entry_sort_order']		= 'ترتيب الفرز';

// Column
$_['column_name']			= 'الاسم';
$_['column_sort_order']	    = 'ترتيب الفرز';
$_['column_action']         = 'تحرير';

// Error
$_['error_warning']         = 'تحذير: الرجاء تعبئة الخانات المطلوبة!';
$_['error_permission']		= 'تحذير : أنت لا تمتلك صلاحيات التعديل!';
$_['error_name']			= 'يجب ان يكون اكبر من 3 ولايزيد عن 255 حرف !';
$_['error_product']			= 'تحذير: لايمكن حذف المدفوعات المتكررة لأنها مرتبطة بـ %s منتجات !';
