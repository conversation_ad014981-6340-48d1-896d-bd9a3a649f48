{{ header }}
{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        {% if can_add %}
        <a href="{{ add }}" data-toggle="tooltip" title="{{ button_add }}" class="btn btn-primary"><i class="fa fa-plus"></i></a>
        {% endif %}
        {% if can_delete %}
        <button type="button" data-toggle="tooltip" title="{{ button_delete }}" class="btn btn-danger" onclick="confirm('{{ text_confirm }}') ? $('#form-comment').submit() : false;"><i class="fa fa-trash-o"></i></button>
        {% endif %}
      </div>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    {% if success %}
    <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    
    <!-- Panel de filtros -->
    <div class="panel panel-default filter-panel">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-filter"></i> {{ text_filter }}</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-sm-4">
            <div class="form-group">
              <label class="control-label" for="input-post">{{ entry_post }}</label>
              <select name="filter_post_id" id="input-post" class="form-control select2">
                <option value="">{{ text_all_posts }}</option>
                {% for post in posts %}
                {% if post.post_id == filter_post_id %}
                <option value="{{ post.post_id }}" selected="selected">{{ post.title }}</option>
                {% else %}
                <option value="{{ post.post_id }}">{{ post.title }}</option>
                {% endif %}
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="col-sm-4">
            <div class="form-group">
              <label class="control-label" for="input-author">{{ entry_author }}</label>
              <input type="text" name="filter_author" value="{{ filter_author }}" placeholder="{{ entry_author }}" id="input-author" class="form-control" />
            </div>
          </div>
          <div class="col-sm-4">
            <div class="form-group">
              <label class="control-label" for="input-status">{{ entry_status }}</label>
              <select name="filter_status" id="input-status" class="form-control">
                <option value="">{{ text_all_statuses }}</option>
                {% if filter_status == '1' %}
                <option value="1" selected="selected">{{ text_enabled }}</option>
                {% else %}
                <option value="1">{{ text_enabled }}</option>
                {% endif %}
                {% if filter_status == '0' %}
                <option value="0" selected="selected">{{ text_disabled }}</option>
                {% else %}
                <option value="0">{{ text_disabled }}</option>
                {% endif %}
              </select>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-sm-4">
            <div class="form-group">
              <label class="control-label" for="input-date-added">{{ entry_date_added }}</label>
              <div class="input-group date">
                <input type="text" name="filter_date_added" value="{{ filter_date_added }}" placeholder="{{ entry_date_added }}" data-date-format="YYYY-MM-DD" id="input-date-added" class="form-control" />
                <span class="input-group-btn">
                <button type="button" class="btn btn-default"><i class="fa fa-calendar"></i></button>
                </span>
              </div>
            </div>
          </div>
          <div class="col-sm-4 col-sm-offset-4">
            <div class="form-group">
              <label class="control-label">&nbsp;</label>
              <div>
                <button type="button" id="button-filter" class="btn btn-primary pull-right"><i class="fa fa-filter"></i> {{ button_filter }}</button>
                <button type="button" id="button-clear-filter" class="btn btn-default pull-right margin-r-5"><i class="fa fa-eraser"></i> {{ button_clear }}</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Tabla de comentarios -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-list"></i> {{ text_list }}</h3>
      </div>
      <div class="panel-body">
        <form action="{{ delete }}" method="post" enctype="multipart/form-data" id="form-comment">
          <div class="table-responsive">
            <table class="table table-bordered table-hover">
              <thead>
                <tr>
                  <td style="width: 1px;" class="text-center"><input type="checkbox" onclick="$('input[name*=\'selected\']').prop('checked', this.checked);" /></td>
                  <td class="text-left">{{ column_post }}</td>
                  <td class="text-left"><a href="{{ sort_author }}" class="{{ sort == 'c.author' ? order|lower : 'asc' }}">{{ column_author }}</a></td>
                  <td class="text-left">{{ column_comment }}</td>
                  <td class="text-center"><a href="{{ sort_status }}" class="{{ sort == 'c.status' ? order|lower : 'asc' }}">{{ column_status }}</a></td>
                  <td class="text-center"><a href="{{ sort_date_added }}" class="{{ sort == 'c.date_added' ? order|lower : 'asc' }}">{{ column_date_added }}</a></td>
                  <td class="text-center">{{ column_replies }}</td>
                  <td class="text-right">{{ column_action }}</td>
                </tr>
              </thead>
              <tbody>
                {% if comments %}
                {% for comment in comments %}
                <tr>
                  <td class="text-center">
                    <input type="checkbox" name="selected[]" value="{{ comment.comment_id }}" />
                  </td>
                  <td class="text-left">{{ comment.post_title }}</td>
                  <td class="text-left">{{ comment.author }}<br><small>{{ comment.email }}</small></td>
                  <td class="text-left">{{ comment.content_short }}</td>
                  <td class="text-center"><span class="label label-{{ comment.status_class }}">{{ comment.status }}</span></td>
                  <td class="text-center">{{ comment.date_added }}</td>
                  <td class="text-center">{{ comment.replies }}</td>
                  <td class="text-right">
                    <div class="btn-group">
                      <a href="{{ comment.view }}" data-toggle="tooltip" title="{{ button_view }}" class="btn btn-info btn-sm"><i class="fa fa-eye"></i></a>
                      {% if can_edit %}
                        {% if comment.status_class == 'danger' %}
                          <button type="button" id="approve-{{ comment.comment_id }}" class="btn btn-success btn-sm" onclick="approveComment('{{ comment.comment_id }}', 1)" data-toggle="tooltip" title="{{ button_approve }}"><i class="fa fa-check"></i></button>
                        {% else %}
                          <button type="button" id="disapprove-{{ comment.comment_id }}" class="btn btn-warning btn-sm" onclick="approveComment('{{ comment.comment_id }}', 0)" data-toggle="tooltip" title="{{ button_disapprove }}"><i class="fa fa-ban"></i></button>
                        {% endif %}
                        <a href="{{ comment.edit }}" data-toggle="tooltip" title="{{ button_edit }}" class="btn btn-primary btn-sm"><i class="fa fa-pencil"></i></a>
                      {% endif %}
                    </div>
                  </td>
                </tr>
                {% endfor %}
                {% else %}
                <tr>
                  <td class="text-center" colspan="8">{{ text_no_results }}</td>
                </tr>
                {% endif %}
              </tbody>
            </table>
          </div>
        </form>
        <div class="row">
          <div class="col-sm-6 text-left">{{ pagination }}</div>
          <div class="col-sm-6 text-right">{{ results }}</div>
        </div>
      </div>
    </div>
  </div>
</div>

<style type="text/css">
.filter-panel {
  margin-bottom: 20px;
}
.margin-r-5 {
  margin-right: 5px;
}
.label {
  display: inline-block;
  min-width: 80px;
}
</style>

<script type="text/javascript"><!--
$('#button-filter').on('click', function() {
  var url = 'index.php?route=catalog/blog_comment&user_token={{ user_token }}';

  var filter_post_id = $('select[name=\'filter_post_id\']').val();

  if (filter_post_id) {
    url += '&filter_post_id=' + encodeURIComponent(filter_post_id);
  }

  var filter_author = $('input[name=\'filter_author\']').val();

  if (filter_author) {
    url += '&filter_author=' + encodeURIComponent(filter_author);
  }

  var filter_status = $('select[name=\'filter_status\']').val();

  if (filter_status !== '') {
    url += '&filter_status=' + encodeURIComponent(filter_status);
  }

  var filter_date_added = $('input[name=\'filter_date_added\']').val();

  if (filter_date_added) {
    url += '&filter_date_added=' + encodeURIComponent(filter_date_added);
  }

  location = url;
});

$('#button-clear-filter').on('click', function() {
  location = 'index.php?route=catalog/blog_comment&user_token={{ user_token }}';
});

// Datepicker
$('.date').datetimepicker({
  pickTime: false
});

// Initialize Select2
$(document).ready(function() {
  $('.select2').select2();
  
  // Establecer tooltips
  $('[data-toggle=\'tooltip\']').tooltip({container: 'body'});
});

// Función para aprobar/desaprobar comentarios vía AJAX
function approveComment(commentId, status) {
  $.ajax({
    url: 'index.php?route=catalog/blog_comment/ajaxApprove&user_token={{ user_token }}',
    type: 'POST',
    dataType: 'json',
    data: {
      comment_id: commentId,
      status: status
    },
    beforeSend: function() {
      $('#approve-' + commentId + ', #disapprove-' + commentId).button('loading');
    },
    complete: function() {
      $('#approve-' + commentId + ', #disapprove-' + commentId).button('reset');
    },
    success: function(json) {
      if (json.success) {
        // Mostrar notificación
        var alertClass = (status == 1) ? 'alert-success' : 'alert-warning';
        $('<div class="alert ' + alertClass + ' alert-dismissible"><i class="fa fa-check-circle"></i> ' + json.success + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>').insertBefore('.panel-default:first');
        
        // Recargar la página después de 1 segundo
        setTimeout(function() {
          location.reload();
        }, 1000);
      }
      
      if (json.error) {
        alert(json.error);
      }
    },
    error: function(xhr, ajaxOptions, thrownError) {
      alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
    }
  });
}
//--></script>

{{ footer }}