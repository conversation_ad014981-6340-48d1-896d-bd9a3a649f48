<div class="row">
  <div class="col-sm-6">
    <fieldset id="account">
      <legend>{{ text_your_details }}</legend>
      <div class="form-group" style="display: {% if customer_groups|length > 1 %} block {% else %} none {% endif %};">
        <label class="control-label">{{ entry_customer_group }}</label>
        {% for customer_group in customer_groups %}
          {% if customer_group.customer_group_id == customer_group_id %}
            <div class="radio">
              <label><input type="radio" name="customer_group_id" value="{{ customer_group.customer_group_id }}" checked="checked"/>{{ customer_group.name }}</label>
            </div>
          {% else %}
            <div class="radio">
              <label><input type="radio" name="customer_group_id" value="{{ customer_group.customer_group_id }}"/>{{ customer_group.name }}</label>
            </div>
          {% endif %}
        {% endfor %}
      </div>
      <div class="form-group required">
        <label class="control-label" for="input-payment-firstname">{{ entry_firstname }}</label>
        <input type="text" name="firstname" value="{{ firstname }}" placeholder="{{ entry_firstname }}" class="form-control" id="input-payment-firstname"/>
      </div>
      <div class="form-group required">
        <label class="control-label" for="input-payment-lastname">{{ entry_lastname }}</label>
        <input type="text" name="lastname" value="{{ lastname }}" placeholder="{{ entry_lastname }}" class="form-control" id="input-payment-lastname"/>
      </div>
      <div class="form-group required">
        <label class="control-label" for="input-payment-email">{{ entry_email }}</label>
        <input type="text" name="email" value="{{ email }}" placeholder="{{ entry_email }}" class="form-control" id="input-payment-email"/>
      </div>
      <div class="form-group required">
        <label class="control-label" for="input-payment-telephone">{{ entry_telephone }}</label>
        <input type="text" name="telephone" value="{{ telephone }}" placeholder="{{ entry_telephone }}" class="form-control" id="input-payment-telephone"/>
      </div>
      {% for custom_field in custom_fields %}
        {% if custom_field.location == 'account' %}
          {% if custom_field.type == 'select' %}
            <div class="form-group custom-field" data-sort="{{ custom_field.sort_order }}" id="payment-custom-field{{ custom_field.custom_field_id }}">
              <label class="control-label" for="input-payment-custom-field{{ custom_field.custom_field_id }}">{{ custom_field.name }}</label>
              <select name="custom_field[{{ custom_field.location }}][{{ custom_field.custom_field_id }}]" class="form-control" id="input-payment-custom-field{{ custom_field.custom_field_id }}"/>
                <option value="">{{ text_select }}</option>
                {% for custom_field_value in custom_field.custom_field_value %}
                  {% if guest_custom_field[custom_field.custom_field_id] and custom_field_value.custom_field_value_id == guest_custom_field[custom_field.custom_field_id] %}
                    <option value="{{ custom_field_value.custom_field_value_id }}" selected="selected">{{ custom_field_value.name }}</option>
                  {% else %}
                    <option value="{{ custom_field_value.custom_field_value_id }}">{{ custom_field_value.name }}</option>
                  {% endif %}
                {% endfor %}
              </select>
            </div>
          {% endif %}
          {% if custom_field.type == 'radio' %}
            <div class="form-group custom-field" data-sort="{{ custom_field.sort_order }}" id="payment-custom-field{{ custom_field.custom_field_id }}">
              <label class="control-label">{{ custom_field.name }}</label>
              <div id="input-payment-custom-field{{ custom_field.custom_field_id }}">
                {% for custom_field_value in custom_field.custom_field_value %}
                  <div class="radio"> {% if guest_custom_field[custom_field.custom_field_id] and custom_field_value.custom_field_value_id == guest_custom_field[custom_field.custom_field_id] %}
                      <label><input type="radio" name="custom_field[{{ custom_field.location }}][{{ custom_field.custom_field_id }}]" value="{{ custom_field_value.custom_field_value_id }}" checked="checked"/>{{ custom_field_value.name }}</label>
                    {% else %}
                      <label><input type="radio" name="custom_field[{{ custom_field.location }}][{{ custom_field.custom_field_id }}]" value="{{ custom_field_value.custom_field_value_id }}"/>{{ custom_field_value.name }}</label>
                    {% endif %}</div>
                {% endfor %}
              </div>
            </div>
          {% endif %}
          {% if custom_field.type == 'checkbox' %}
            <div class="form-group custom-field" data-sort="{{ custom_field.sort_order }}" id="payment-custom-field{{ custom_field.custom_field_id }}">
              <label class="control-label">{{ custom_field.name }}</label>
              <div id="input-payment-custom-field{{ custom_field.custom_field_id }}">
                {% for custom_field_value in custom_field.custom_field_value %}
                  <div class="checkbox">{% if guest_custom_field[custom_field.custom_field_id] and custom_field_value.custom_field_value_id in guest_custom_field[custom_field.custom_field_id] %}
                      <label><input type="checkbox" name="custom_field[{{ custom_field.location }}][{{ custom_field.custom_field_id }}][]" value="{{ custom_field_value.custom_field_value_id }}" checked="checked"/>{{ custom_field_value.name }}</label>
                    {% else %}
                      <label><input type="checkbox" name="custom_field[{{ custom_field.location }}][{{ custom_field.custom_field_id }}][]" value="{{ custom_field_value.custom_field_value_id }}"/>{{ custom_field_value.name }}</label>
                    {% endif %}</div>
                {% endfor %}
              </div>
            </div>
          {% endif %}
          {% if custom_field.type == 'text' %}
            <div class="form-group custom-field" data-sort="{{ custom_field.sort_order }}" id="payment-custom-field{{ custom_field.custom_field_id }}">
              <label class="control-label" for="input-payment-custom-field{{ custom_field.custom_field_id }}">{{ custom_field.name }}</label>
              <input type="text" name="custom_field[{{ custom_field.location }}][{{ custom_field.custom_field_id }}]" value="{% if guest_custom_field[custom_field.custom_field_id] %} {{ guest_custom_field[custom_field.custom_field_id] }} {% else %} {{ custom_field.value }} {% endif %}" placeholder="{{ custom_field.name }}" class="form-control" id="input-payment-custom-field{{ custom_field.custom_field_id }}"/>
            </div>
          {% endif %}
          {% if custom_field.type == 'textarea' %}
            <div class="form-group custom-field" data-sort="{{ custom_field.sort_order }}" id="payment-custom-field{{ custom_field.custom_field_id }}">
              <label class="control-label" for="input-payment-custom-field{{ custom_field.custom_field_id }}">{{ custom_field.name }}</label>
              <textarea name="custom_field[{{ custom_field.location }}][{{ custom_field.custom_field_id }}]" rows="5" placeholder="{{ custom_field.name }}" class="form-control" id="input-payment-custom-field{{ custom_field.custom_field_id }}"/>{% if guest_custom_field[custom_field.custom_field_id] %} {{ guest_custom_field[custom_field.custom_field_id] }} {% else %} {{ custom_field.value }} {% endif %}</textarea>
            </div>
          {% endif %}
          {% if custom_field.type == 'file' %}
            <div class="form-group custom-field" data-sort="{{ custom_field.sort_order }}" id="payment-custom-field{{ custom_field.custom_field_id }}">
              <label class="control-label">{{ custom_field.name }}</label>
              <br/>
              <button type="button" data-loading-text="{{ text_loading }}" class="btn btn-default" id="button-payment-custom-field{{ custom_field.custom_field_id }}"><i class="fa fa-upload"></i> {{ button_upload }}</button>
              <input type="hidden" name="custom_field[{{ custom_field.location }}][{{ custom_field.custom_field_id }}]" value="{% if guest_custom_field[custom_field.custom_field_id] %} {{ guest_custom_field[custom_field.custom_field_id] }} {% endif %}" id="input-payment-custom-field{{ custom_field.custom_field_id }}"/>
            </div>
          {% endif %}
          {% if custom_field.type == 'date' %}
            <div class="form-group custom-field" data-sort="{{ custom_field.sort_order }}" id="payment-custom-field{{ custom_field.custom_field_id }}">
              <label class="control-label" for="input-payment-custom-field{{ custom_field.custom_field_id }}">{{ custom_field.name }}</label>
              <div class="input-group date">
                <input type="text" name="custom_field[{{ custom_field.location }}][{{ custom_field.custom_field_id }}]" value="{% if guest_custom_field[custom_field.custom_field_id] %} {{ guest_custom_field[custom_field.custom_field_id] }} {% else %} {{ custom_field.value }} {% endif %}" placeholder="{{ custom_field.name }}" data-date-format="YYYY-MM-DD" class="form-control" id="input-payment-custom-field{{ custom_field.custom_field_id }}"/>
                <span class="input-group-btn"><button type="button" class="btn btn-default"><i class="fa fa-calendar"></i></button></span>
              </div>
            </div>
          {% endif %}
          {% if custom_field.type == 'time' %}
            <div class="form-group custom-field" data-sort="{{ custom_field.sort_order }}" id="payment-custom-field{{ custom_field.custom_field_id }}">
              <label class="control-label" for="input-payment-custom-field{{ custom_field.custom_field_id }}">{{ custom_field.name }}</label>
              <div class="input-group time">
                <input type="text" name="custom_field[{{ custom_field.location }}][{{ custom_field.custom_field_id }}]" value="{% if guest_custom_field[custom_field.custom_field_id] %} {{ guest_custom_field[custom_field.custom_field_id] }} {% else %} {{ custom_field.value }} {% endif %}" placeholder="{{ custom_field.name }}" data-date-format="HH:mm" class="form-control" id="input-payment-custom-field{{ custom_field.custom_field_id }}"/>
                <span class="input-group-btn"><button type="button" class="btn btn-default"><i class="fa fa-calendar"></i></button></span>
              </div>
            </div>
          {% endif %}
          {% if custom_field.type == 'datetime' %}
            <div class="form-group custom-field" data-sort="{{ custom_field.sort_order }}" id="payment-custom-field{{ custom_field.custom_field_id }}">
              <label class="control-label" for="input-payment-custom-field{{ custom_field.custom_field_id }}">{{ custom_field.name }}</label>
              <div class="input-group datetime">
                <input type="text" name="custom_field[{{ custom_field.location }}][{{ custom_field.custom_field_id }}]" value="{% if guest_custom_field[custom_field.custom_field_id] %} {{ guest_custom_field[custom_field.custom_field_id] }} {% else %} {{ custom_field.value }} {% endif %}" placeholder="{{ custom_field.name }}" data-date-format="YYYY-MM-DD HH:mm" class="form-control" id="input-payment-custom-field{{ custom_field.custom_field_id }}"/>
                <span class="input-group-btn"><button type="button" class="btn btn-default"><i class="fa fa-calendar"></i></button></span>
              </div>
            </div>
          {% endif %}
        {% endif %}
      {% endfor %}
    </fieldset>
  </div>
  <div class="col-sm-6">
    <fieldset id="address">
      <legend>{{ text_your_address }}</legend>
      <div class="form-group">
        <label class="control-label" for="input-payment-company">{{ entry_company }}</label>
        <input type="text" name="company" value="{{ company }}" placeholder="{{ entry_company }}" class="form-control" id="input-payment-company"/>
      </div>
      <div class="form-group required">
        <label class="control-label" for="input-payment-address-1">{{ entry_address_1 }}</label>
        <input type="text" name="address_1" value="{{ address_1 }}" placeholder="{{ entry_address_1 }}" class="form-control" id="input-payment-address-1"/>
      </div>
      <div class="form-group">
        <label class="control-label" for="input-payment-address-2">{{ entry_address_2 }}</label>
        <input type="text" name="address_2" value="{{ address_2 }}" placeholder="{{ entry_address_2 }}" class="form-control" id="input-payment-address-2"/>
      </div>
      <div class="form-group required">
        <label class="control-label" for="input-payment-city">{{ entry_city }}</label>
        <input type="text" name="city" value="{{ city }}" placeholder="{{ entry_city }}" class="form-control" id="input-payment-city"/>
      </div>
      <div class="form-group required">
        <label class="control-label" for="input-payment-postcode">{{ entry_postcode }}</label>
        <input type="text" name="postcode" value="{{ postcode }}" placeholder="{{ entry_postcode }}" class="form-control" id="input-payment-postcode"/>
      </div>
      <div class="form-group required">
        <label class="control-label" for="input-payment-country">{{ entry_country }}</label>
        <select name="country_id" class="form-control" id="input-payment-country"/>
          <option value="">{{ text_select }}</option>
          {% for country in countries %}
            {% if country.country_id == country_id %}
              <option value="{{ country.country_id }}" selected="selected">{{ country.name }}</option>
            {% else %}
              <option value="{{ country.country_id }}">{{ country.name }}</option>
            {% endif %}
          {% endfor %}
        </select>
      </div>
      <div class="form-group required">
        <label class="control-label" for="input-payment-zone">{{ entry_zone }}</label>
        <select name="zone_id" class="form-control" id="input-payment-zone"/>
        </select>
      </div>
      {% for custom_field in custom_fields %}
        {% if custom_field.location == 'address' %}
          {% if custom_field.type == 'select' %}
            <div class="form-group custom-field" data-sort="{{ custom_field.sort_order }}" id="payment-custom-field{{ custom_field.custom_field_id }}">
              <label class="control-label" for="input-payment-custom-field{{ custom_field.custom_field_id }}">{{ custom_field.name }}</label>
              <select name="custom_field[{{ custom_field.location }}][{{ custom_field.custom_field_id }}]" class="form-control" id="input-payment-custom-field{{ custom_field.custom_field_id }}"/>
                <option value="">{{ text_select }}</option>
                {% for custom_field_value in custom_field.custom_field_value %}
                  {% if guest_custom_field[custom_field.custom_field_id] and custom_field_value.custom_field_value_id == guest_custom_field[custom_field.custom_field_id] %}
                    <option value="{{ custom_field_value.custom_field_value_id }}" selected="selected">{{ custom_field_value.name }}</option>
                  {% else %}
                    <option value="{{ custom_field_value.custom_field_value_id }}">{{ custom_field_value.name }}</option>
                  {% endif %}
                {% endfor %}
              </select>
            </div>
          {% endif %}
          {% if custom_field.type == 'radio' %}
            <div class="form-group custom-field" data-sort="{{ custom_field.sort_order }}" id="payment-custom-field{{ custom_field.custom_field_id }}">
              <label class="control-label">{{ custom_field.name }}</label>
              <div id="input-payment-custom-field{{ custom_field.custom_field_id }}">
                {% for custom_field_value in custom_field.custom_field_value %}
                  <div class="radio">
		                {% if guest_custom_field[custom_field.custom_field_id] and custom_field_value.custom_field_value_id == guest_custom_field[custom_field.custom_field_id] %}
                      <label><input type="radio" name="custom_field[{{ custom_field.location }}][{{ custom_field.custom_field_id }}]" value="{{ custom_field_value.custom_field_value_id }}" checked="checked"/>{{ custom_field_value.name }}</label>
                    {% else %}
                      <label><input type="radio" name="custom_field[{{ custom_field.location }}][{{ custom_field.custom_field_id }}]" value="{{ custom_field_value.custom_field_value_id }}"/>{{ custom_field_value.name }}</label>
                    {% endif %}</div>
                {% endfor %}
              </div>
            </div>
          {% endif %}
          {% if custom_field.type == 'checkbox' %}
            <div class="form-group custom-field" data-sort="{{ custom_field.sort_order }}" id="payment-custom-field{{ custom_field.custom_field_id }}">
              <label class="control-label">{{ custom_field.name }}</label>
              <div id="input-payment-custom-field{{ custom_field.custom_field_id }}">
                {% for custom_field_value in custom_field.custom_field_value %}
                  <div class="checkbox">{% if guest_custom_field[custom_field.custom_field_id] and custom_field_value.custom_field_value_id in guest_custom_field[custom_field.custom_field_id] %}
                      <label><input type="checkbox" name="custom_field[{{ custom_field.location }}][{{ custom_field.custom_field_id }}][]" value="{{ custom_field_value.custom_field_value_id }}" checked="checked"/>{{ custom_field_value.name }}</label>
                    {% else %}
                      <label><input type="checkbox" name="custom_field[{{ custom_field.location }}][{{ custom_field.custom_field_id }}][]" value="{{ custom_field_value.custom_field_value_id }}"/>{{ custom_field_value.name }}</label>
                    {% endif %}</div>
                {% endfor %}
              </div>
            </div>
          {% endif %}
          {% if custom_field.type == 'text' %}
            <div class="form-group custom-field" data-sort="{{ custom_field.sort_order }}" id="payment-custom-field{{ custom_field.custom_field_id }}">
              <label class="control-label" for="input-payment-custom-field{{ custom_field.custom_field_id }}">{{ custom_field.name }}</label>
              <input type="text" name="custom_field[{{ custom_field.location }}][{{ custom_field.custom_field_id }}]" value="{% if guest_custom_field[custom_field.custom_field_id] %} {{ guest_custom_field[custom_field.custom_field_id] }} {% else %} {{ custom_field.value }} {% endif %}" placeholder="{{ custom_field.name }}" class="form-control" id="input-payment-custom-field{{ custom_field.custom_field_id }}"/>
            </div>
          {% endif %}
          {% if custom_field.type == 'textarea' %}
            <div class="form-group custom-field" data-sort="{{ custom_field.sort_order }}" id="payment-custom-field{{ custom_field.custom_field_id }}">
              <label class="control-label" for="input-payment-custom-field{{ custom_field.custom_field_id }}">{{ custom_field.name }}</label>
              <textarea name="custom_field[{{ custom_field.location }}][{{ custom_field.custom_field_id }}]" rows="5" placeholder="{{ custom_field.name }}" class="form-control" id="input-payment-custom-field{{ custom_field.custom_field_id }}"/>{% if guest_custom_field[custom_field.custom_field_id] %} {{ guest_custom_field[custom_field.custom_field_id] }} {% else %} {{ custom_field.value }} {% endif %}</textarea>
            </div>
          {% endif %}
          {% if custom_field.type == 'file' %}
            <div class="form-group custom-field" data-sort="{{ custom_field.sort_order }}" id="payment-custom-field{{ custom_field.custom_field_id }}">
              <label class="control-label">{{ custom_field.name }}</label>
              <br/>
              <button type="button" data-loading-text="{{ text_loading }}" class="btn btn-default" id="button-payment-custom-field{{ custom_field.custom_field_id }}"><i class="fa fa-upload"></i> {{ button_upload }}</button>
              <input type="hidden" name="custom_field[{{ custom_field.location }}][{{ custom_field.custom_field_id }}]" value="{% if guest_custom_field[custom_field.custom_field_id] %} {{ guest_custom_field[custom_field.custom_field_id] }} {% endif %}" id="input-payment-custom-field{{ custom_field.custom_field_id }}"/>
            </div>
          {% endif %}
          {% if custom_field.type == 'date' %}
            <div class="form-group custom-field" data-sort="{{ custom_field.sort_order }}" id="payment-custom-field{{ custom_field.custom_field_id }}">
              <label class="control-label" for="input-payment-custom-field{{ custom_field.custom_field_id }}">{{ custom_field.name }}</label>
              <div class="input-group date">
                <input type="text" name="custom_field[{{ custom_field.location }}][{{ custom_field.custom_field_id }}]" value="{% if guest_custom_field[custom_field.custom_field_id] %} {{ guest_custom_field[custom_field.custom_field_id] }} {% else %} {{ custom_field.value }} {% endif %}" placeholder="{{ custom_field.name }}" data-date-format="YYYY-MM-DD" class="form-control" id="input-payment-custom-field{{ custom_field.custom_field_id }}"/>
                <span class="input-group-btn"><button type="button" class="btn btn-default"><i class="fa fa-calendar"></i></button></span>
              </div>
            </div>
          {% endif %}
          {% if custom_field.type == 'time' %}
            <div class="form-group custom-field" data-sort="{{ custom_field.sort_order }}" id="payment-custom-field{{ custom_field.custom_field_id }}">
              <label class="control-label" for="input-payment-custom-field{{ custom_field.custom_field_id }}">{{ custom_field.name }}</label>
              <div class="input-group time">
                <input type="text" name="custom_field[{{ custom_field.location }}][{{ custom_field.custom_field_id }}]" value="{% if guest_custom_field[custom_field.custom_field_id] %} {{ guest_custom_field[custom_field.custom_field_id] }} {% else %} {{ custom_field.value }} {% endif %}" placeholder="{{ custom_field.name }}" data-date-format="HH:mm" class="form-control" id="input-payment-custom-field{{ custom_field.custom_field_id }}"/>
                <span class="input-group-btn"><button type="button" class="btn btn-default"><i class="fa fa-calendar"></i></button></span>
              </div>
            </div>
          {% endif %}
          {% if custom_field.type == 'time' %}
            <div class="form-group custom-field" data-sort="{{ custom_field.sort_order }}" id="payment-custom-field{{ custom_field.custom_field_id }}">
              <label class="control-label" for="input-payment-custom-field{{ custom_field.custom_field_id }}">{{ custom_field.name }}</label>
              <div class="input-group datetime">
                <input type="text" name="custom_field[{{ custom_field.location }}][{{ custom_field.custom_field_id }}]" value="{% if guest_custom_field[custom_field.custom_field_id] %} {{ guest_custom_field[custom_field.custom_field_id] }} {% else %} {{ custom_field.value }} {% endif %}" placeholder="{{ custom_field.name }}" data-date-format="YYYY-MM-DD HH:mm" class="form-control" id="input-payment-custom-field{{ custom_field.custom_field_id }}"/>
                <span class="input-group-btn"><button type="button" class="btn btn-default"><i class="fa fa-calendar"></i></button></span>
              </div>
            </div>
          {% endif %}
        {% endif %}
      {% endfor %}
    </fieldset>
    {{ captcha }}
  </div>
</div>
{% if shipping_required %}
<div class="checkbox">
  <label>{% if shipping_address %}
      <input type="checkbox" name="shipping_address" value="1" checked="checked"/>
    {% else %}
      <input type="checkbox" name="shipping_address" value="1"/>
    {% endif %}
    {{ entry_shipping }}</label>
</div>
{% endif %}
<div class="buttons">
  <div class="pull-right">
    <input type="button" value="{{ button_continue }}" data-loading-text="{{ text_loading }}" class="btn btn-primary" id="button-guest"/>
  </div>
</div>
<script type="text/javascript"><!--
// Sort the custom fields
$('#account .form-group[data-sort]').detach().each(function() {
    if ($(this).attr('data-sort') >= 0 && $(this).attr('data-sort') <= $('#account .form-group').length) {
        $('#account .form-group').eq($(this).attr('data-sort')).before(this);
    }

    if ($(this).attr('data-sort') > $('#account .form-group').length) {
        $('#account .form-group:last').after(this);
    }

    if ($(this).attr('data-sort') == $('#account .form-group').length) {
        $('#account .form-group:last').after(this);
    }

    if ($(this).attr('data-sort') < -$('#account .form-group').length) {
        $('#account .form-group:first').before(this);
    }
});

$('#address .form-group[data-sort]').detach().each(function() {
    if ($(this).attr('data-sort') >= 0 && $(this).attr('data-sort') <= $('#address .form-group').length) {
        $('#address .form-group').eq($(this).attr('data-sort')).before(this);
    }

    if ($(this).attr('data-sort') > $('#address .form-group').length) {
        $('#address .form-group:last').after(this);
    }

    if ($(this).attr('data-sort') == $('#address .form-group').length) {
        $('#address .form-group:last').after(this);
    }

    if ($(this).attr('data-sort') < -$('#address .form-group').length) {
        $('#address .form-group:first').before(this);
    }
});

$('#collapse-payment-address input[name=\'customer_group_id\']').on('change', function() {
    $.ajax({
        url: 'index.php?route=checkout/checkout/customfield&customer_group_id=' + this.value,
        dataType: 'json',
        success: function (json) {
            $('#collapse-payment-address .custom-field').hide();
            $('#collapse-payment-address .custom-field').removeClass('required');

            for (i = 0; i < json.length; i++) {
                custom_field = json[i];

                $('#payment-custom-field' + custom_field['custom_field_id']).show();

                if (custom_field['required']) {
                    $('#payment-custom-field' + custom_field['custom_field_id']).addClass('required');
                } else {
                    $('#payment-custom-field' + custom_field['custom_field_id']).removeClass('required');
                }
            }
        },
        error: function (xhr, ajaxOptions, thrownError) {
            alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
    });
});

$('#collapse-payment-address input[name=\'customer_group_id\']:checked').trigger('change');
//--></script>
<script type="text/javascript"><!--
$('#collapse-payment-address button[id^=\'button-payment-custom-field\']').on('click', function() {
    var element = this;

    $('#form-upload').remove();

    $('body').prepend('<form enctype="multipart/form-data" style="display: none;" id="form-upload"><input type="file" name="file"/></form>');

    $('#form-upload input[name=\'file\']').trigger('click');

    if (typeof timer != 'undefined') {
        clearInterval(timer);
    }

    timer = setInterval(function() {
        if ($('#form-upload input[name=\'file\']').val() != '') {
            clearInterval(timer);

            $.ajax({
                url: 'index.php?route=tool/upload',
                type: 'post',
                dataType: 'json',
                data: new FormData($('#form-upload')[0]),
                cache: false,
                contentType: false,
                processData: false,
                beforeSend: function() {
                    $(element).button('loading');
                },
                complete: function() {
                    $(element).button('reset');
                },
                success: function (json) {
                    $(element).parent().find('.text-danger').remove();

                    if (json['error']) {
                        $(element).parent().find('input[name^=\'custom_field\']').after('<div class="text-danger">' + json['error'] + '</div>');
                    }

                    if (json['success']) {
                        alert(json['success']);

                        $(element).parent().find('input[name^=\'custom_field\']').val(json['code']);
                    }
                },
                error: function (xhr, ajaxOptions, thrownError) {
                    alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
                }
            });
        }
    }, 500);
});
//--></script>
<script type="text/javascript"><!--
$('.date').datetimepicker({
    language: '{{ datepicker }}',
    pickTime: false
});

$('.time').datetimepicker({
    language: '{{ datepicker }}',
    pickDate: false
});

$('.datetime').datetimepicker({
    language: '{{ datepicker }}',
    pickDate: true,
    pickTime: true
});
//--></script>
<script type="text/javascript"><!--
$('#collapse-payment-address select[name=\'country_id\']').on('change', function() {
    $.ajax({
        url: 'index.php?route=checkout/checkout/country&country_id=' + this.value,
        dataType: 'json',
        beforeSend: function() {
            $('#collapse-payment-address select[name=\'country_id\']').prop('disabled', true);
        },
        complete: function() {
            $('#collapse-payment-address select[name=\'country_id\']').prop('disabled', false);
        },
        success: function (json) {
            if (json['postcode_required'] == '1') {
                $('#collapse-payment-address input[name=\'postcode\']').parent().parent().addClass('required');
            } else {
                $('#collapse-payment-address input[name=\'postcode\']').parent().parent().removeClass('required');
            }

            html = '<option value="">{{ text_select|escape('js') }}</option>';

            if (json['zone'] && json['zone'] != '') {
                for (i = 0; i < json['zone'].length; i++) {
                    html += '<option value="' + json['zone'][i]['zone_id'] + '"';

                    if (json['zone'][i]['zone_id'] == '{{ zone_id }}') {
                        html += ' selected="selected"';
                    }

                    html += '>' + json['zone'][i]['name'] + '</option>';
                }
            } else {
                html += '<option value="0" selected="selected">{{ text_none|escape('js') }}</option>';
            }

            $('#collapse-payment-address select[name=\'zone_id\']').html(html);
        },
        error: function (xhr, ajaxOptions, thrownError) {
            alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
    });
});

$('#collapse-payment-address select[name=\'country_id\']').trigger('change');
//--></script>