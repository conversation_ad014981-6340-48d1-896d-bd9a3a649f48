<?php
// Heading
$_['heading_title']          = 'Blog Management';

// Text
$_['text_success_add']       = 'Article added successfully!';
$_['text_success_edit']      = 'Article updated successfully!';
$_['text_success_delete']    = 'Article(s) deleted successfully!';
$_['text_success_copy']      = 'Article(s) copied successfully!';
$_['text_list']              = 'Blog Article List';
$_['text_add']               = 'Add New Article';
$_['text_edit']              = 'Edit Article';
$_['text_default']           = 'Default';
$_['text_enabled']           = 'Enabled';
$_['text_disabled']          = 'Disabled';
$_['text_all_statuses']      = '-- All Statuses --';
$_['text_all_categories']    = '-- All Categories --';
$_['text_no_results']        = 'No results';
$_['text_form']              = 'Article Form';
$_['text_filter']            = 'Filter';
$_['text_unknown']           = 'Unknown';
$_['text_active']            = 'Active';
$_['text_select']            = '-- Select --';
$_['text_published_posts']   = 'Published Posts';
$_['text_draft_posts']       = 'Draft Posts';
$_['text_total_posts']       = 'Total Posts';
$_['text_comments']          = 'Comments';
$_['text_uploaded']          = 'File uploaded successfully!';
$_['text_confirm']           = 'Are you sure?';

// Column
$_['column_title']           = 'Article Title';
$_['column_author']          = 'Author';
$_['column_category']        = 'Category';
$_['column_status']          = 'Status';
$_['column_date_published']  = 'Date Published';
$_['column_date_added']      = 'Date Added';
$_['column_hits']            = 'Views';
$_['column_comments']        = 'Comments';
$_['column_action']          = 'Action';

// Entry
$_['entry_title']            = 'Article Title';
$_['entry_slug']             = 'Slug';
$_['entry_content']          = 'Article Content';
$_['entry_short_description'] = 'Short Description';
$_['entry_meta_title']       = 'Meta Title';
$_['entry_meta_description'] = 'Meta Description';
$_['entry_meta_keywords']    = 'Meta Keywords';
$_['entry_status']           = 'Status';
$_['entry_comment_status']   = 'Allow Comments';
$_['entry_sort_order']       = 'Sort Order';
$_['entry_featured_image']   = 'Featured Image';
$_['entry_date_published']   = 'Date Published';
$_['entry_category']         = 'Categories';
$_['entry_tag']              = 'Tags';
$_['entry_date_start']       = 'Start Date';
$_['entry_date_end']         = 'End Date';

// Help
$_['help_slug']              = 'The slug is the URL-friendly version of the article title. It must be unique.';
$_['help_short_description'] = 'A short description that appears on the blog homepage.';

// Tab
$_['tab_general']            = 'General';
$_['tab_data']               = 'Data';
$_['tab_links']              = 'Links';
$_['tab_seo']                = 'SEO';
$_['tab_image']              = 'Image';

// Button
$_['button_save']            = 'Save';
$_['button_cancel']          = 'Cancel';
$_['button_add']             = 'Add New';
$_['button_edit']            = 'Edit';
$_['button_delete']          = 'Delete';
$_['button_filter']          = 'Filter';
$_['button_clear']           = 'Clear';
$_['button_copy']            = 'Copy';
$_['button_generate']        = 'Generate';

// Error
$_['error_permission']       = 'Warning: You do not have permission to modify the blog!';
$_['error_title']            = 'Article Title must be between 1 and 255 characters!';
$_['error_content']          = 'Article Content is required!';
$_['error_slug']             = 'Slug is required!';
$_['error_slug_exists']      = 'This slug already exists!';
$_['error_filename']         = 'Filename must be between 3 and 255 characters!';
$_['error_filetype']         = 'Invalid file type!';
$_['error_filesize']         = 'File is too large!';
$_['error_upload']          = 'Error uploading file!';
$_['error_form']             = 'Please check the form for errors!';
$_['error_ajax']             = 'Error in operation! Please try again.';