{{ header }}{{ column_left }}

<!-- Enterprise Grade Plus CSS for Balance Sheet -->
<style>
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --danger-color: #e74c3c;
    --warning-color: #f39c12;
    --info-color: #17a2b8;
    --light-bg: #f8f9fa;
    --border-color: #dee2e6;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

.balance-sheet-container {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: var(--shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.balance-sheet-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color), var(--success-color));
}

.balance-sheet-header {
    text-align: center;
    border-bottom: 3px solid var(--primary-color);
    padding-bottom: 20px;
    margin-bottom: 30px;
    position: relative;
}

.balance-sheet-header h2 {
    color: var(--primary-color);
    font-weight: 700;
    font-size: 1.8rem;
    margin: 0;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.balance-sheet-section {
    margin-bottom: 30px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    background: #ffffff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    transition: var(--transition);
}

.balance-sheet-section:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.section-header {
    background: linear-gradient(135deg, var(--secondary-color), #2980b9);
    color: white;
    padding: 15px 20px;
    font-weight: 600;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.section-header i {
    margin-right: 10px;
    font-size: 1.2rem;
}

.section-content {
    padding: 20px;
    background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
}

.account-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    border-bottom: 1px solid var(--border-color);
    border-radius: 4px;
    margin-bottom: 5px;
    transition: var(--transition);
    background: #ffffff;
}

.account-row:hover {
    background: var(--light-bg);
    transform: translateX(5px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.account-row:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.account-name {
    flex: 1;
    color: var(--primary-color);
    font-weight: 500;
    font-size: 0.95rem;
}

.account-amount {
    font-weight: 600;
    color: var(--success-color);
    min-width: 140px;
    text-align: {{ direction == 'rtl' ? 'right' : 'left' }};
    font-size: 1rem;
    font-family: 'Courier New', monospace;
}

.total-row {
    background: linear-gradient(135deg, var(--light-bg), #e9ecef);
    padding: 15px 20px;
    font-weight: 700;
    font-size: 1.1rem;
    border-top: 3px solid var(--primary-color);
    color: var(--primary-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.balance-summary {
    background: linear-gradient(135deg, var(--success-color), #229954);
    color: white;
    padding: 25px;
    border-radius: var(--border-radius);
    text-align: center;
    font-size: 1.3rem;
    font-weight: 700;
    box-shadow: var(--shadow);
    position: relative;
    overflow: hidden;
}

.balance-summary::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
    transform: rotate(45deg);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

/* RTL Support */
[dir="rtl"] .account-row {
    direction: rtl;
}

[dir="rtl"] .account-amount {
    text-align: right;
}

/* Accessibility Enhancements */
.account-row:focus-within {
    outline: 2px solid var(--secondary-color);
    outline-offset: 2px;
}

/* Print Styles */
@media print {
    .balance-sheet-container {
        box-shadow: none;
        border: 1px solid #000;
    }
    
    .section-header {
        background: #f0f0f0 !important;
        color: #000 !important;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .balance-sheet-container {
        padding: 15px;
        margin-bottom: 15px;
    }
    
    .account-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
    
    .account-amount {
        min-width: auto;
        font-size: 1.1rem;
    }
}
</style>

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-{{ direction == 'rtl' ? 'left' : 'right' }}">
        <div class="btn-group" role="group" aria-label="{{ text_actions }}">
          <button type="button" class="btn btn-success btn-lg" onclick="generateBalanceSheet()"
                  data-toggle="tooltip" title="{{ text_generate_tooltip }}">
            <i class="fa fa-balance-scale"></i> {{ button_generate }}
          </button>
          <div class="btn-group" role="group">
            <button type="button" class="btn btn-info dropdown-toggle" data-toggle="dropdown"
                    aria-expanded="false" data-toggle="tooltip" title="{{ text_export_tooltip }}">
              <i class="fa fa-download"></i> {{ text_export }}
            </button>
            <ul class="dropdown-menu">
              <li><a href="#" onclick="exportBalanceSheet('excel')">
                <i class="fa fa-file-excel-o text-success"></i> Excel
              </a></li>
              <li><a href="#" onclick="exportBalanceSheet('pdf')">
                <i class="fa fa-file-pdf-o text-danger"></i> PDF
              </a></li>
              <li><a href="#" onclick="exportBalanceSheet('csv')">
                <i class="fa fa-file-text-o text-info"></i> CSV
              </a></li>
              <li class="divider"></li>
              <li><a href="#" onclick="printBalanceSheet()">
                <i class="fa fa-print"></i> {{ text_print }}
              </a></li>
            </ul>
          </div>
          <button type="button" class="btn btn-default" onclick="showAdvancedAnalysis()"
                  data-toggle="tooltip" title="{{ text_advanced_analysis }}">
            <i class="fa fa-line-chart"></i>
          </button>
        </div>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li class="breadcrumb-item"><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>

  <div class="container-fluid">
    <!-- Filter Form -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title">
          <i class="fa fa-filter"></i> {{ text_filter }}
        </h3>
      </div>
      <div class="panel-body">
        <form id="balance-sheet-filter-form" method="post">
          <div class="row">
            <div class="col-md-3">
              <div class="form-group">
                <label for="date_end" class="control-label">{{ entry_date_end }}</label>
                <input type="date" name="date_end" id="date_end" value="{{ date_end }}" class="form-control" required>
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <label for="branch_id" class="form-label">{{ entry_branch }}</label>
                <select name="branch_id" id="branch_id" class="form-control">
                  <option value="">{{ text_all_branches }}</option>
                  {% for branch in branches %}
                  <option value="{{ branch.branch_id }}"{% if branch.branch_id == branch_id %} selected{% endif %}>{{ branch.name }}</option>
                  {% endfor %}
                </select>
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <label for="cost_center_id" class="form-label">{{ entry_cost_center }}</label>
                <select name="cost_center_id" id="cost_center_id" class="form-control">
                  <option value="">{{ text_all_cost_centers }}</option>
                  {% for cost_center in cost_centers %}
                  <option value="{{ cost_center.cost_center_id }}"{% if cost_center.cost_center_id == cost_center_id %} selected{% endif %}>{{ cost_center.name }}</option>
                  {% endfor %}
                </select>
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <label class="control-label">&nbsp;</label>
                <div>
                  <button type="submit" class="btn btn-primary btn-block">
                    <i class="fa fa-search"></i> {{ button_filter }}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>

    <!-- Balance Sheet Content -->
    {% if balance_sheet_data %}
    <div class="balance-sheet-container">
      <div class="balance-sheet-header">
        <h2>{{ heading_title }}</h2>
        <p class="text-muted">{{ text_as_of }} {{ date_end_formatted }}</p>
      </div>

      <div class="row">
        <!-- Assets Section -->
        <div class="col-md-6">
          <div class="balance-sheet-section">
            <div class="section-header">
              <span><i class="fa fa-building"></i> {{ text_assets }}</span>
              <span class="label label-default">{{ assets_count }} {{ text_accounts }}</span>
            </div>
            <div class="section-content">
              {% for asset_category in balance_sheet_data.assets %}
              <div class="mb-3">
                <h5 class="text-primary">{{ asset_category.name }}</h5>
                {% for account in asset_category.accounts %}
                <div class="account-row">
                  <span class="account-name">{{ account.name }}</span>
                  <span class="account-amount">{{ account.balance_formatted }}</span>
                </div>
                {% endfor %}
                <div class="total-row">
                  <span>{{ text_total }} {{ asset_category.name }}</span>
                  <span>{{ asset_category.total_formatted }}</span>
                </div>
              </div>
              {% endfor %}
              <div class="total-row bg-primary text-white">
                <span>{{ text_total_assets }}</span>
                <span>{{ balance_sheet_data.total_assets_formatted }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Liabilities & Equity Section -->
        <div class="col-md-6">
          <!-- Liabilities -->
          <div class="balance-sheet-section">
            <div class="section-header">
              <span><i class="fa fa-credit-card"></i> {{ text_liabilities }}</span>
              <span class="label label-default">{{ liabilities_count }} {{ text_accounts }}</span>
            </div>
            <div class="section-content">
              {% for liability_category in balance_sheet_data.liabilities %}
              <div class="mb-3">
                <h5 class="text-warning">{{ liability_category.name }}</h5>
                {% for account in liability_category.accounts %}
                <div class="account-row">
                  <span class="account-name">{{ account.name }}</span>
                  <span class="account-amount text-warning">{{ account.balance_formatted }}</span>
                </div>
                {% endfor %}
                <div class="total-row">
                  <span>{{ text_total }} {{ liability_category.name }}</span>
                  <span>{{ liability_category.total_formatted }}</span>
                </div>
              </div>
              {% endfor %}
              <div class="total-row bg-warning text-white">
                <span>{{ text_total_liabilities }}</span>
                <span>{{ balance_sheet_data.total_liabilities_formatted }}</span>
              </div>
            </div>
          </div>

          <!-- Equity -->
          <div class="balance-sheet-section">
            <div class="section-header">
              <span><i class="fa fa-pie-chart"></i> {{ text_equity }}</span>
              <span class="label label-default">{{ equity_count }} {{ text_accounts }}</span>
            </div>
            <div class="section-content">
              {% for equity_account in balance_sheet_data.equity %}
              <div class="account-row">
                <span class="account-name">{{ equity_account.name }}</span>
                <span class="account-amount text-info">{{ equity_account.balance_formatted }}</span>
              </div>
              {% endfor %}
              <div class="total-row bg-info text-white">
                <span>{{ text_total_equity }}</span>
                <span>{{ balance_sheet_data.total_equity_formatted }}</span>
              </div>
            </div>
          </div>

          <!-- Total Liabilities & Equity -->
          <div class="total-row bg-success text-white">
            <span>{{ text_total_liabilities_equity }}</span>
            <span>{{ balance_sheet_data.total_liabilities_equity_formatted }}</span>
          </div>
        </div>
      </div>

      <!-- Balance Verification -->
      <div class="balance-summary mt-4">
        {% if balance_sheet_data.is_balanced %}
        <i class="fa fa-check-circle"></i>
        {{ text_balance_verified }}
        {% else %}
        <i class="fa fa-exclamation-triangle"></i>
        {{ text_balance_error }}
        <br>
        <small>{{ text_difference }}: {{ balance_sheet_data.difference_formatted }}</small>
        {% endif %}
      </div>
    </div>
    {% else %}
    <div class="alert alert-info">
      <i class="fa fa-info-circle"></i>
      {{ text_no_data }}
    </div>
    {% endif %}
  </div>
</div>

<script>
// Enterprise Grade Plus JavaScript for Balance Sheet
class BalanceSheetManager {
    constructor() {
        this.initializeTooltips();
        this.initializeFormValidation();
        this.initializeKeyboardShortcuts();
        this.initializeAutoSave();
        this.initializeRealTimeValidation();
    }

    initializeTooltips() {
        $('[data-toggle="tooltip"]').tooltip();
    }

    initializeFormValidation() {
        const form = document.getElementById('balance-sheet-filter-form');
        if (form) {
            form.addEventListener('submit', this.validateForm.bind(this));
        }
    }

    initializeKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case 'g':
                        e.preventDefault();
                        this.generateBalanceSheet();
                        break;
                    case 'e':
                        e.preventDefault();
                        this.showExportMenu();
                        break;
                    case 'p':
                        e.preventDefault();
                        this.printBalanceSheet();
                        break;
                    case 'a':
                        e.preventDefault();
                        this.showAdvancedAnalysis();
                        break;
                }
            }
        });
    }

    initializeAutoSave() {
        const inputs = document.querySelectorAll('#balance-sheet-filter-form input, #balance-sheet-filter-form select');
        inputs.forEach(input => {
            input.addEventListener('change', () => {
                this.saveFormState();
            });
        });
        this.loadFormState();
    }

    initializeRealTimeValidation() {
        const dateEnd = document.getElementById('date_end');
        if (dateEnd) {
            dateEnd.addEventListener('change', () => {
                this.validateDate();
            });
        }
    }

    validateForm(e) {
        e.preventDefault();
        const dateEnd = document.getElementById('date_end').value;

        if (!dateEnd) {
            this.showAlert('{{ error_date_required }}', 'danger');
            return false;
        }

        if (new Date(dateEnd) > new Date()) {
            this.showAlert('{{ error_future_date }}', 'warning');
        }

        this.generateBalanceSheet();
        return true;
    }

    validateDate() {
        const dateEnd = document.getElementById('date_end');
        const dateValue = new Date(dateEnd.value);
        const today = new Date();

        if (dateValue > today) {
            this.showAlert('{{ warning_future_date }}', 'warning');
        }
    }

    generateBalanceSheet() {
        const form = document.getElementById('balance-sheet-filter-form');
        const formData = new FormData(form);

        // Show loading state
        this.showLoadingState(true);

        fetch('{{ generate_url }}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            this.showLoadingState(false);
            if (data.success) {
                this.showAlert('{{ text_success_generation }}', 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                this.showAlert(data.error || '{{ error_generate }}', 'danger');
            }
        })
        .catch(error => {
            this.showLoadingState(false);
            this.showAlert('{{ error_generate }}: ' + error.message, 'danger');
        });
    }

    exportBalanceSheet(format) {
        const params = new URLSearchParams({
            format: format,
            date_end: document.getElementById('date_end').value,
            branch_id: document.getElementById('branch_id').value,
            cost_center_id: document.getElementById('cost_center_id').value
        });

        this.showAlert('{{ text_exporting }}...', 'info');
        window.open('{{ export_url }}&' + params.toString(), '_blank');
    }

    printBalanceSheet() {
        window.print();
    }

    showAdvancedAnalysis() {
        // Advanced analysis implementation
        this.showAlert('{{ text_advanced_analysis_loading }}', 'info');
        // Modal or new page implementation here
    }

    showExportMenu() {
        const exportButton = document.querySelector('.dropdown-toggle');
        if (exportButton) {
            exportButton.click();
        }
    }

    showLoadingState(show) {
        const generateBtn = document.querySelector('button[onclick="generateBalanceSheet()"]');
        if (generateBtn) {
            if (show) {
                generateBtn.disabled = true;
                generateBtn.innerHTML = '<i class="fa fa-spinner fa-spin"></i> {{ text_generating }}';
            } else {
                generateBtn.disabled = false;
                generateBtn.innerHTML = '<i class="fa fa-balance-scale"></i> {{ button_generate }}';
            }
        }
    }

    showAlert(message, type) {
        const alertContainer = document.createElement('div');
        alertContainer.className = `alert alert-${type} alert-dismissible fade show`;
        alertContainer.innerHTML = `
            <i class="fa fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'}"></i>
            ${message}
            <button type="button" class="close" data-dismiss="alert">&times;</button>
        `;

        const container = document.querySelector('#content .container-fluid');
        container.insertBefore(alertContainer, container.firstChild);

        setTimeout(() => {
            alertContainer.remove();
        }, 5000);
    }

    saveFormState() {
        const formData = new FormData(document.getElementById('balance-sheet-filter-form'));
        const state = Object.fromEntries(formData);
        localStorage.setItem('balance_sheet_form_state', JSON.stringify(state));
    }

    loadFormState() {
        const savedState = localStorage.getItem('balance_sheet_form_state');
        if (savedState) {
            const state = JSON.parse(savedState);
            Object.keys(state).forEach(key => {
                const element = document.getElementById(key);
                if (element) {
                    element.value = state[key];
                }
            });
        }
    }
}

// Global functions for backward compatibility
function generateBalanceSheet() {
    balanceSheetManager.generateBalanceSheet();
}

function exportBalanceSheet(format) {
    balanceSheetManager.exportBalanceSheet(format);
}

function printBalanceSheet() {
    balanceSheetManager.printBalanceSheet();
}

function showAdvancedAnalysis() {
    balanceSheetManager.showAdvancedAnalysis();
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.balanceSheetManager = new BalanceSheetManager();
});
</script>

{{ footer }}
