<?php

/**
 * Private<PERSON>ey interface
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2009 <PERSON>
 * @license   http://www.opensource.org/licenses/mit-license.html  MIT License
 * @link      http://phpseclib.sourceforge.net
 */

declare(strict_types=1);

namespace phpseclib3\Crypt\Common;

/**
 * PrivateKey interface
 *
 * <AUTHOR> <<EMAIL>>
 */
interface PrivateKey
{
    public function sign($message);
    //public function decrypt($ciphertext);
    public function getPublicKey();
    public function toString(string $type, array $options = []): string;

    /**
     * @return static
     */
    public function withPassword(?string $password = null): PrivateKey;
}
