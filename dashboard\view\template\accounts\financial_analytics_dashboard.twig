{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        {% if has_permission %}
        <button type="button" id="button-add-widget" data-toggle="tooltip" title="{{ button_add_widget }}" class="btn btn-primary">
          <i class="fa fa-plus"></i> {{ button_add_widget }}
        </button>
        <button type="button" id="button-save-layout" data-toggle="tooltip" title="{{ button_save_layout }}" class="btn btn-success">
          <i class="fa fa-save"></i> {{ button_save_layout }}
        </button>
        <button type="button" id="button-reset-layout" data-toggle="tooltip" title="{{ button_reset_layout }}" class="btn btn-warning">
          <i class="fa fa-refresh"></i> {{ button_reset_layout }}
        </button>
        {% endif %}
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    {% if success %}
    <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    
    <!-- Dashboard Controls -->
    <div class="row">
      <div class="col-md-12">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-cog"></i> {{ text_dashboard_controls }}</h3>
          </div>
          <div class="panel-body">
            <div class="row">
              <div class="col-md-3">
                <div class="form-group">
                  <label for="dashboard-type">{{ entry_dashboard_type }}</label>
                  <select name="dashboard_type" id="dashboard-type" class="form-control">
                    <option value="executive" {% if dashboard_type == 'executive' %}selected="selected"{% endif %}>{{ text_executive }}</option>
                    <option value="operational" {% if dashboard_type == 'operational' %}selected="selected"{% endif %}>{{ text_operational }}</option>
                    <option value="financial" {% if dashboard_type == 'financial' %}selected="selected"{% endif %}>{{ text_financial }}</option>
                    <option value="custom" {% if dashboard_type == 'custom' %}selected="selected"{% endif %}>{{ text_custom }}</option>
                  </select>
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label for="date-range">{{ entry_date_range }}</label>
                  <select name="date_range" id="date-range" class="form-control">
                    <option value="today" {% if date_range == 'today' %}selected="selected"{% endif %}>{{ text_today }}</option>
                    <option value="week" {% if date_range == 'week' %}selected="selected"{% endif %}>{{ text_this_week }}</option>
                    <option value="month" {% if date_range == 'month' %}selected="selected"{% endif %}>{{ text_this_month }}</option>
                    <option value="quarter" {% if date_range == 'quarter' %}selected="selected"{% endif %}>{{ text_this_quarter }}</option>
                    <option value="year" {% if date_range == 'year' %}selected="selected"{% endif %}>{{ text_this_year }}</option>
                    <option value="custom" {% if date_range == 'custom' %}selected="selected"{% endif %}>{{ text_custom_range }}</option>
                  </select>
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label for="refresh-interval">{{ entry_refresh_interval }}</label>
                  <select name="refresh_interval" id="refresh-interval" class="form-control">
                    <option value="0">{{ text_manual }}</option>
                    <option value="30" {% if refresh_interval == '30' %}selected="selected"{% endif %}>30 {{ text_seconds }}</option>
                    <option value="60" {% if refresh_interval == '60' %}selected="selected"{% endif %}>1 {{ text_minute }}</option>
                    <option value="300" {% if refresh_interval == '300' %}selected="selected"{% endif %}>5 {{ text_minutes }}</option>
                    <option value="900" {% if refresh_interval == '900' %}selected="selected"{% endif %}>15 {{ text_minutes }}</option>
                  </select>
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label>&nbsp;</label>
                  <div>
                    <button type="button" id="button-refresh" class="btn btn-info">
                      <i class="fa fa-refresh"></i> {{ button_refresh }}
                    </button>
                    <button type="button" id="button-fullscreen" class="btn btn-default">
                      <i class="fa fa-expand"></i> {{ button_fullscreen }}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Key Performance Indicators -->
    <div class="row" id="kpi-row">
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-primary">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-money fa-5x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ total_revenue }}</div>
                <div>{{ text_total_revenue }}</div>
              </div>
            </div>
          </div>
          <div class="panel-footer">
            <span class="pull-left">
              {% if revenue_change > 0 %}
              <span class="text-success">+{{ revenue_change }}%</span>
              {% elseif revenue_change < 0 %}
              <span class="text-danger">{{ revenue_change }}%</span>
              {% else %}
              <span class="text-muted">{{ revenue_change }}%</span>
              {% endif %}
            </span>
            <span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
            <div class="clearfix"></div>
          </div>
        </div>
      </div>
      
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-green">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-line-chart fa-5x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ net_profit }}</div>
                <div>{{ text_net_profit }}</div>
              </div>
            </div>
          </div>
          <div class="panel-footer">
            <span class="pull-left">
              {% if profit_margin > 0 %}
              <span class="text-success">{{ profit_margin }}%</span>
              {% else %}
              <span class="text-danger">{{ profit_margin }}%</span>
              {% endif %}
            </span>
            <span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
            <div class="clearfix"></div>
          </div>
        </div>
      </div>
      
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-yellow">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-tint fa-5x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ cash_flow }}</div>
                <div>{{ text_cash_flow }}</div>
              </div>
            </div>
          </div>
          <div class="panel-footer">
            <span class="pull-left">
              {% if cash_flow_change > 0 %}
              <span class="text-success">+{{ cash_flow_change }}%</span>
              {% elseif cash_flow_change < 0 %}
              <span class="text-danger">{{ cash_flow_change }}%</span>
              {% else %}
              <span class="text-muted">{{ cash_flow_change }}%</span>
              {% endif %}
            </span>
            <span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
            <div class="clearfix"></div>
          </div>
        </div>
      </div>
      
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-red">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-percent fa-5x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ roi_percentage }}</div>
                <div>{{ text_roi }}</div>
              </div>
            </div>
          </div>
          <div class="panel-footer">
            <span class="pull-left">
              {% if roi_change > 0 %}
              <span class="text-success">+{{ roi_change }}%</span>
              {% elseif roi_change < 0 %}
              <span class="text-danger">{{ roi_change }}%</span>
              {% else %}
              <span class="text-muted">{{ roi_change }}%</span>
              {% endif %}
            </span>
            <span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
            <div class="clearfix"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Widgets Grid -->
    <div class="row" id="widgets-container">
      {% for widget in widgets %}
      <div class="col-md-{{ widget.width }}" data-widget-id="{{ widget.widget_id }}">
        <div class="panel panel-default widget-panel" data-widget-type="{{ widget.widget_type }}">
          <div class="panel-heading">
            <h3 class="panel-title">
              <i class="fa fa-{{ widget.icon }}"></i> {{ widget.widget_title }}
              <div class="pull-right">
                <button type="button" class="btn btn-xs btn-default btn-widget-config" data-widget-id="{{ widget.widget_id }}">
                  <i class="fa fa-cog"></i>
                </button>
                <button type="button" class="btn btn-xs btn-danger btn-widget-remove" data-widget-id="{{ widget.widget_id }}">
                  <i class="fa fa-times"></i>
                </button>
              </div>
            </h3>
          </div>
          <div class="panel-body">
            <div class="widget-content" id="widget-content-{{ widget.widget_id }}">
              {% if widget.widget_type == 'chart' %}
              <canvas id="chart-{{ widget.widget_id }}" width="400" height="200"></canvas>
              {% elseif widget.widget_type == 'table' %}
              <div class="table-responsive">
                <table class="table table-striped table-condensed">
                  <thead>
                    <tr>
                      {% for header in widget.table_headers %}
                      <th>{{ header }}</th>
                      {% endfor %}
                    </tr>
                  </thead>
                  <tbody>
                    {% for row in widget.table_data %}
                    <tr>
                      {% for cell in row %}
                      <td>{{ cell }}</td>
                      {% endfor %}
                    </tr>
                    {% endfor %}
                  </tbody>
                </table>
              </div>
              {% elseif widget.widget_type == 'kpi' %}
              <div class="text-center">
                <h2 class="text-primary">{{ widget.kpi_value }}</h2>
                <p class="text-muted">{{ widget.kpi_description }}</p>
                {% if widget.kpi_trend > 0 %}
                <span class="label label-success">+{{ widget.kpi_trend }}%</span>
                {% elseif widget.kpi_trend < 0 %}
                <span class="label label-danger">{{ widget.kpi_trend }}%</span>
                {% else %}
                <span class="label label-default">{{ widget.kpi_trend }}%</span>
                {% endif %}
              </div>
              {% elseif widget.widget_type == 'gauge' %}
              <div class="text-center">
                <canvas id="gauge-{{ widget.widget_id }}" width="200" height="200"></canvas>
              </div>
              {% endif %}
            </div>
          </div>
          <div class="panel-footer">
            <small class="text-muted">{{ text_last_updated }}: {{ widget.last_updated }}</small>
          </div>
        </div>
      </div>
      {% endfor %}
    </div>

    <!-- Add Widget Button -->
    <div class="row">
      <div class="col-md-12 text-center">
        <button type="button" id="button-add-widget-large" class="btn btn-lg btn-primary">
          <i class="fa fa-plus"></i> {{ button_add_widget }}
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Add Widget Modal -->
<div class="modal fade" id="modal-add-widget" tabindex="-1" role="dialog">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{{ text_add_widget }}</h4>
      </div>
      <div class="modal-body">
        <form id="form-add-widget" class="form-horizontal">
          <div class="form-group">
            <label class="col-sm-3 control-label" for="widget-type">{{ entry_widget_type }}</label>
            <div class="col-sm-9">
              <select name="widget_type" id="widget-type" class="form-control">
                <option value="chart">{{ text_chart }}</option>
                <option value="table">{{ text_table }}</option>
                <option value="kpi">{{ text_kpi }}</option>
                <option value="gauge">{{ text_gauge }}</option>
                <option value="trend">{{ text_trend }}</option>
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-3 control-label" for="widget-title">{{ entry_widget_title }}</label>
            <div class="col-sm-9">
              <input type="text" name="widget_title" id="widget-title" class="form-control" />
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-3 control-label" for="data-source">{{ entry_data_source }}</label>
            <div class="col-sm-9">
              <select name="data_source" id="data-source" class="form-control">
                <option value="revenue">{{ text_revenue_analysis }}</option>
                <option value="expenses">{{ text_expense_analysis }}</option>
                <option value="profit">{{ text_profit_analysis }}</option>
                <option value="cash_flow">{{ text_cash_flow_analysis }}</option>
                <option value="balance_sheet">{{ text_balance_sheet_analysis }}</option>
                <option value="custom">{{ text_custom_query }}</option>
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-3 control-label" for="widget-width">{{ entry_widget_width }}</label>
            <div class="col-sm-9">
              <select name="widget_width" id="widget-width" class="form-control">
                <option value="3">{{ text_quarter_width }}</option>
                <option value="4">{{ text_third_width }}</option>
                <option value="6">{{ text_half_width }}</option>
                <option value="8">{{ text_two_thirds_width }}</option>
                <option value="12">{{ text_full_width }}</option>
              </select>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_cancel }}</button>
        <button type="button" id="button-save-widget" class="btn btn-primary">{{ button_save }}</button>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript">
$(document).ready(function() {
    // Initialize tooltips
    $('[data-toggle="tooltip"]').tooltip();
    
    // Make widgets sortable
    $('#widgets-container').sortable({
        handle: '.panel-heading',
        placeholder: 'widget-placeholder',
        update: function(event, ui) {
            // Auto-save layout when widgets are moved
            saveLayout();
        }
    });
    
    // Add widget functionality
    $('#button-add-widget, #button-add-widget-large').on('click', function() {
        $('#modal-add-widget').modal('show');
    });
    
    // Save widget
    $('#button-save-widget').on('click', function() {
        $.ajax({
            url: 'index.php?route=accounts/financial_analytics_dashboard/addWidget&token={{ token }}',
            type: 'post',
            data: $('#form-add-widget').serialize(),
            dataType: 'json',
            beforeSend: function() {
                $('#button-save-widget').button('loading');
            },
            complete: function() {
                $('#button-save-widget').button('reset');
            },
            success: function(json) {
                if (json.success) {
                    $('#modal-add-widget').modal('hide');
                    location.reload();
                } else {
                    alert(json.error);
                }
            }
        });
    });
    
    // Remove widget
    $(document).on('click', '.btn-widget-remove', function() {
        if (confirm('{{ text_confirm_remove_widget }}')) {
            var widget_id = $(this).data('widget-id');
            
            $.ajax({
                url: 'index.php?route=accounts/financial_analytics_dashboard/removeWidget&token={{ token }}',
                type: 'post',
                data: {widget_id: widget_id},
                dataType: 'json',
                success: function(json) {
                    if (json.success) {
                        $('[data-widget-id="' + widget_id + '"]').fadeOut(function() {
                            $(this).remove();
                        });
                    }
                }
            });
        }
    });
    
    // Save layout
    function saveLayout() {
        var layout = [];
        $('#widgets-container .col-md-3, #widgets-container .col-md-4, #widgets-container .col-md-6, #widgets-container .col-md-8, #widgets-container .col-md-12').each(function(index) {
            layout.push({
                widget_id: $(this).data('widget-id'),
                position: index
            });
        });
        
        $.ajax({
            url: 'index.php?route=accounts/financial_analytics_dashboard/saveLayout&token={{ token }}',
            type: 'post',
            data: {layout: layout},
            dataType: 'json'
        });
    }
    
    // Auto-refresh functionality
    var refreshInterval = parseInt($('#refresh-interval').val());
    if (refreshInterval > 0) {
        setInterval(function() {
            location.reload();
        }, refreshInterval * 1000);
    }
    
    // Manual refresh
    $('#button-refresh').on('click', function() {
        location.reload();
    });
    
    // Fullscreen mode
    $('#button-fullscreen').on('click', function() {
        if (document.fullscreenElement) {
            document.exitFullscreen();
        } else {
            document.documentElement.requestFullscreen();
        }
    });
});
</script>

{{ footer }}
