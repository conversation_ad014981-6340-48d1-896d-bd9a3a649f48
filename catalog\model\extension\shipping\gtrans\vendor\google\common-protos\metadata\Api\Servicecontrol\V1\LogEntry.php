<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/servicecontrol/v1/log_entry.proto

namespace GPBMetadata\Google\Api\Servicecontrol\V1;

class LogEntry
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        \GPBMetadata\Google\Api\Annotations::initOnce();
        \GPBMetadata\Google\Logging\Type\LogSeverity::initOnce();
        \GPBMetadata\Google\Protobuf\Any::initOnce();
        \GPBMetadata\Google\Protobuf\Struct::initOnce();
        \GPBMetadata\Google\Protobuf\Timestamp::initOnce();
        $pool->internalAddGeneratedFile(hex2bin(
            "0ae0050a2c676f6f676c652f6170692f73657276696365636f6e74726f6c" .
            "2f76312f6c6f675f656e7472792e70726f746f121c676f6f676c652e6170" .
            "692e73657276696365636f6e74726f6c2e76311a26676f6f676c652f6c6f" .
            "6767696e672f747970652f6c6f675f73657665726974792e70726f746f1a" .
            "19676f6f676c652f70726f746f6275662f616e792e70726f746f1a1c676f" .
            "6f676c652f70726f746f6275662f7374727563742e70726f746f1a1f676f" .
            "6f676c652f70726f746f6275662f74696d657374616d702e70726f746f22" .
            "86030a084c6f67456e747279120c0a046e616d65180a20012809122d0a09" .
            "74696d657374616d70180b2001280b321a2e676f6f676c652e70726f746f" .
            "6275662e54696d657374616d7012320a087365766572697479180c200128" .
            "0e32202e676f6f676c652e6c6f6767696e672e747970652e4c6f67536576" .
            "657269747912110a09696e736572745f696418042001280912420a066c61" .
            "62656c73180d2003280b32322e676f6f676c652e6170692e736572766963" .
            "65636f6e74726f6c2e76312e4c6f67456e7472792e4c6162656c73456e74" .
            "7279122d0a0d70726f746f5f7061796c6f616418022001280b32142e676f" .
            "6f676c652e70726f746f6275662e416e79480012160a0c746578745f7061" .
            "796c6f6164180320012809480012310a0e7374727563745f7061796c6f61" .
            "6418062001280b32172e676f6f676c652e70726f746f6275662e53747275" .
            "637448001a2d0a0b4c6162656c73456e747279120b0a036b657918012001" .
            "2809120d0a0576616c75651802200128093a02380142090a077061796c6f" .
            "6164427f0a20636f6d2e676f6f676c652e6170692e73657276696365636f" .
            "6e74726f6c2e7631420d4c6f67456e74727950726f746f50015a4a676f6f" .
            "676c652e676f6c616e672e6f72672f67656e70726f746f2f676f6f676c65" .
            "617069732f6170692f73657276696365636f6e74726f6c2f76313b736572" .
            "76696365636f6e74726f6c620670726f746f33"
        ), true);

        static::$is_initialized = true;
    }
}

