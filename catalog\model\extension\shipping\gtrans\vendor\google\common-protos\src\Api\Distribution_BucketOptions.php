<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/distribution.proto

namespace Google\Api;

if (false) {
    /**
     * This class is deprecated. Use Google\Api\Distribution\BucketOptions instead.
     * @deprecated
     */
    class Distribution_BucketOptions {}
}
class_exists(Distribution\BucketOptions::class);
@trigger_error('Google\Api\Distribution_BucketOptions is deprecated and will be removed in the next major release. Use Google\Api\Distribution\BucketOptions instead', E_USER_DEPRECATED);

