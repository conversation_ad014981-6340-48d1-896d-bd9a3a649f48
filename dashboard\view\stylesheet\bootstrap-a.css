@charset "UTF-8";
/*!
 * Bootstrap v3.3.7 (http://getbootstrap.com)
 * Copyright 2011-2016 Twitter, Inc.
 * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)
 */
/*! normalize.css v3.0.3 | MIT License | github.com/necolas/normalize.css */
html {
  font-family: sans-serif;
  -ms-text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%;
}
body {
  margin: 0;
}
article, aside, details, figcaption, figure, footer, header, hgroup, main, menu, nav, section, summary {
  display: block;
}
audio, canvas, progress, video {
  display: inline-block;
  vertical-align: baseline;
}
audio:not([controls]) {
  display: none;
  height: 0;
}
[hidden], template {
  display: none;
}
a {
  background-color: transparent;
}
a:active, a:hover {
  outline: 0;
}
abbr[title] {
  border-bottom: 1px dotted;
}
b, strong {
  font-weight: bold;
}
dfn {
  font-style: italic;
}
h1 {
  font-size: 2em;
  margin: 0.67em 0;
}
mark {
  background: #ff0;
  color: #000;
}
small {
  font-size: 80%;
}
sub, sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}
sup {
  top: -0.5em;
}
sub {
  bottom: -0.25em;
}
img {
  border: 0;
}
svg:not(:root) {
  overflow: hidden;
}
figure {
  margin: 1em 40px;
}
hr {
  box-sizing: content-box;
  height: 0;
}
pre {
  overflow: auto;
}
code, kbd, pre, samp {
  font-family: monospace, monospace;
  font-size: 1em;
}
button, input, optgroup, select, textarea {
  color: inherit;
  font: inherit;
  margin: 0;
}
button {
  overflow: visible;
}
button, select {
  text-transform: none;
}
button, html input[type="button"], input[type="reset"], input[type="submit"] {
  -webkit-appearance: button;
  cursor: pointer;
}
button[disabled], html input[disabled] {
  cursor: default;
}
button::-moz-focus-inner, input::-moz-focus-inner {
  border: 0;
  padding: 0;
}
input {
  line-height: normal;
}
input[type="checkbox"], input[type="radio"] {
  box-sizing: border-box;
  padding: 0;
}
input[type="number"]::-webkit-inner-spin-button, input[type="number"]::-webkit-outer-spin-button {
  height: auto;
}
input[type="search"] {
  -webkit-appearance: textfield;
  box-sizing: content-box;
}
input[type="search"]::-webkit-search-cancel-button, input[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}
fieldset {
  border: 1px solid #c0c0c0;
  margin: 0 2px;
  padding: 0.35em 0.625em 0.75em;
}
legend {
  border: 0;
  padding: 0;
}
textarea {
  overflow: auto;
}
optgroup {
  font-weight: bold;
}
table {
  border-collapse: collapse;
  border-spacing: 0;
}
td, th {
  padding: 0;
}
/*! Source: https://github.com/h5bp/html5-boilerplate/blob/master/src/css/main.css */
@media print {
  *, *:before, *:after {
    background: transparent !important;
    color: #000 !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }
  a, a:visited {
    text-decoration: underline;
  }
  a[href]:after {
    content: " (" attr(href) ")";
  }
  abbr[title]:after {
    content: " (" attr(title) ")";
  }
  a[href^="#"]:after, a[href^="javascript:"]:after {
    content: "";
  }
  pre, blockquote {
    border: 1px solid #999;
    page-break-inside: avoid;
  }
  thead {
    display: table-header-group;
  }
  tr, img {
    page-break-inside: avoid;
  }
  img {
    max-width: 100% !important;
  }
  p, h2, h3 {
    orphans: 3;
    widows: 3;
  }
  h2, h3 {
    page-break-after: avoid;
  }
  .navbar {
    display: none;
  }
  .btn > .caret, .dropup > .btn > .caret {
    border-top-color: #000 !important;
  }
  .label {
    border: 1px solid #000;
  }
  .table {
    border-collapse: collapse !important;
  }
  .table td, .table th {
    background-color: #fff !important;
  }
  .table-bordered th, .table-bordered td {
    border: 1px solid #ddd !important;
  }
}
@font-face {
  font-family: "Glyphicons Halflings";
  src: url("../javascript/bootstrap/fonts/glyphicons-halflings-regular.eot");
  src: url("../javascript/bootstrap/fonts/glyphicons-halflings-regular.eot?#iefix") format("embedded-opentype"), url("../javascript/bootstrap/fonts/glyphicons-halflings-regular.woff2") format("woff2"), url("../javascript/bootstrap/fonts/glyphicons-halflings-regular.woff") format("woff"), url("../javascript/bootstrap/fonts/glyphicons-halflings-regular.ttf") format("truetype"), url("../javascript/bootstrap/fonts/glyphicons-halflings-regular.svg#glyphicons_halflingsregular") format("svg");
}
.glyphicon {
  position: relative;
  top: 1px;
  display: inline-block;
  font-family: "Glyphicons Halflings";
  font-style: normal;
  font-weight: normal;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.glyphicon-asterisk:before {
  content: "*";
}
.glyphicon-plus:before {
  content: "+";
}
.glyphicon-euro:before, .glyphicon-eur:before {
  content: "€";
}
.glyphicon-minus:before {
  content: "−";
}
.glyphicon-cloud:before {
  content: "☁";
}
.glyphicon-envelope:before {
  content: "✉";
}
.glyphicon-pencil:before {
  content: "✏";
}
.glyphicon-glass:before {
  content: "";
}
.glyphicon-music:before {
  content: "";
}
.glyphicon-search:before {
  content: "";
}
.glyphicon-heart:before {
  content: "";
}
.glyphicon-star:before {
  content: "";
}
.glyphicon-star-empty:before {
  content: "";
}
.glyphicon-user:before {
  content: "";
}
.glyphicon-film:before {
  content: "";
}
.glyphicon-th-large:before {
  content: "";
}
.glyphicon-th:before {
  content: "";
}
.glyphicon-th-list:before {
  content: "";
}
.glyphicon-ok:before {
  content: "";
}
.glyphicon-remove:before {
  content: "";
}
.glyphicon-zoom-in:before {
  content: "";
}
.glyphicon-zoom-out:before {
  content: "";
}
.glyphicon-off:before {
  content: "";
}
.glyphicon-signal:before {
  content: "";
}
.glyphicon-cog:before {
  content: "";
}
.glyphicon-trash:before {
  content: "";
}
.glyphicon-home:before {
  content: "";
}
.glyphicon-file:before {
  content: "";
}
.glyphicon-time:before {
  content: "";
}
.glyphicon-road:before {
  content: "";
}
.glyphicon-download-alt:before {
  content: "";
}
.glyphicon-download:before {
  content: "";
}
.glyphicon-upload:before {
  content: "";
}
.glyphicon-inbox:before {
  content: "";
}
.glyphicon-play-circle:before {
  content: "";
}
.glyphicon-repeat:before {
  content: "";
}
.glyphicon-refresh:before {
  content: "";
}
.glyphicon-list-alt:before {
  content: "";
}
.glyphicon-lock:before {
  content: "";
}
.glyphicon-flag:before {
  content: "";
}
.glyphicon-headphones:before {
  content: "";
}
.glyphicon-volume-off:before {
  content: "";
}
.glyphicon-volume-down:before {
  content: "";
}
.glyphicon-volume-up:before {
  content: "";
}
.glyphicon-qrcode:before {
  content: "";
}
.glyphicon-barcode:before {
  content: "";
}
.glyphicon-tag:before {
  content: "";
}
.glyphicon-tags:before {
  content: "";
}
.glyphicon-book:before {
  content: "";
}
.glyphicon-bookmark:before {
  content: "";
}
.glyphicon-print:before {
  content: "";
}
.glyphicon-camera:before {
  content: "";
}
.glyphicon-font:before {
  content: "";
}
.glyphicon-bold:before {
  content: "";
}
.glyphicon-italic:before {
  content: "";
}
.glyphicon-text-height:before {
  content: "";
}
.glyphicon-text-width:before {
  content: "";
}
.glyphicon-align-left:before {
  content: "";
}
.glyphicon-align-center:before {
  content: "";
}
.glyphicon-align-right:before {
  content: "";
}
.glyphicon-align-justify:before {
  content: "";
}
.glyphicon-list:before {
  content: "";
}
.glyphicon-indent-left:before {
  content: "";
}
.glyphicon-indent-right:before {
  content: "";
}
.glyphicon-facetime-video:before {
  content: "";
}
.glyphicon-picture:before {
  content: "";
}
.glyphicon-map-marker:before {
  content: "";
}
.glyphicon-adjust:before {
  content: "";
}
.glyphicon-tint:before {
  content: "";
}
.glyphicon-edit:before {
  content: "";
}
.glyphicon-share:before {
  content: "";
}
.glyphicon-check:before {
  content: "";
}
.glyphicon-move:before {
  content: "";
}
.glyphicon-step-backward:before {
  content: "";
}
.glyphicon-fast-backward:before {
  content: "";
}
.glyphicon-backward:before {
  content: "";
}
.glyphicon-play:before {
  content: "";
}
.glyphicon-pause:before {
  content: "";
}
.glyphicon-stop:before {
  content: "";
}
.glyphicon-forward:before {
  content: "";
}
.glyphicon-fast-forward:before {
  content: "";
}
.glyphicon-step-forward:before {
  content: "";
}
.glyphicon-eject:before {
  content: "";
}
.glyphicon-chevron-left:before {
  content: "";
}
.glyphicon-chevron-right:before {
  content: "";
}
.glyphicon-plus-sign:before {
  content: "";
}
.glyphicon-minus-sign:before {
  content: "";
}
.glyphicon-remove-sign:before {
  content: "";
}
.glyphicon-ok-sign:before {
  content: "";
}
.glyphicon-question-sign:before {
  content: "";
}
.glyphicon-info-sign:before {
  content: "";
}
.glyphicon-screenshot:before {
  content: "";
}
.glyphicon-remove-circle:before {
  content: "";
}
.glyphicon-ok-circle:before {
  content: "";
}
.glyphicon-ban-circle:before {
  content: "";
}
.glyphicon-arrow-left:before {
  content: "";
}
.glyphicon-arrow-right:before {
  content: "";
}
.glyphicon-arrow-up:before {
  content: "";
}
.glyphicon-arrow-down:before {
  content: "";
}
.glyphicon-share-alt:before {
  content: "";
}
.glyphicon-resize-full:before {
  content: "";
}
.glyphicon-resize-small:before {
  content: "";
}
.glyphicon-exclamation-sign:before {
  content: "";
}
.glyphicon-gift:before {
  content: "";
}
.glyphicon-leaf:before {
  content: "";
}
.glyphicon-fire:before {
  content: "";
}
.glyphicon-eye-open:before {
  content: "";
}
.glyphicon-eye-close:before {
  content: "";
}
.glyphicon-warning-sign:before {
  content: "";
}
.glyphicon-plane:before {
  content: "";
}
.glyphicon-calendar:before {
  content: "";
}
.glyphicon-random:before {
  content: "";
}
.glyphicon-comment:before {
  content: "";
}
.glyphicon-magnet:before {
  content: "";
}
.glyphicon-chevron-up:before {
  content: "";
}
.glyphicon-chevron-down:before {
  content: "";
}
.glyphicon-retweet:before {
  content: "";
}
.glyphicon-shopping-cart:before {
  content: "";
}
.glyphicon-folder-close:before {
  content: "";
}
.glyphicon-folder-open:before {
  content: "";
}
.glyphicon-resize-vertical:before {
  content: "";
}
.glyphicon-resize-horizontal:before {
  content: "";
}
.glyphicon-hdd:before {
  content: "";
}
.glyphicon-bullhorn:before {
  content: "";
}
.glyphicon-bell:before {
  content: "";
}
.glyphicon-certificate:before {
  content: "";
}
.glyphicon-thumbs-up:before {
  content: "";
}
.glyphicon-thumbs-down:before {
  content: "";
}
.glyphicon-hand-right:before {
  content: "";
}
.glyphicon-hand-left:before {
  content: "";
}
.glyphicon-hand-up:before {
  content: "";
}
.glyphicon-hand-down:before {
  content: "";
}
.glyphicon-circle-arrow-right:before {
  content: "";
}
.glyphicon-circle-arrow-left:before {
  content: "";
}
.glyphicon-circle-arrow-up:before {
  content: "";
}
.glyphicon-circle-arrow-down:before {
  content: "";
}
.glyphicon-globe:before {
  content: "";
}
.glyphicon-wrench:before {
  content: "";
}
.glyphicon-tasks:before {
  content: "";
}
.glyphicon-filter:before {
  content: "";
}
.glyphicon-briefcase:before {
  content: "";
}
.glyphicon-fullscreen:before {
  content: "";
}
.glyphicon-dashboard:before {
  content: "";
}
.glyphicon-paperclip:before {
  content: "";
}
.glyphicon-heart-empty:before {
  content: "";
}
.glyphicon-link:before {
  content: "";
}
.glyphicon-phone:before {
  content: "";
}
.glyphicon-pushpin:before {
  content: "";
}
.glyphicon-usd:before {
  content: "";
}
.glyphicon-gbp:before {
  content: "";
}
.glyphicon-sort:before {
  content: "";
}
.glyphicon-sort-by-alphabet:before {
  content: "";
}
.glyphicon-sort-by-alphabet-alt:before {
  content: "";
}
.glyphicon-sort-by-order:before {
  content: "";
}
.glyphicon-sort-by-order-alt:before {
  content: "";
}
.glyphicon-sort-by-attributes:before {
  content: "";
}
.glyphicon-sort-by-attributes-alt:before {
  content: "";
}
.glyphicon-unchecked:before {
  content: "";
}
.glyphicon-expand:before {
  content: "";
}
.glyphicon-collapse-down:before {
  content: "";
}
.glyphicon-collapse-up:before {
  content: "";
}
.glyphicon-log-in:before {
  content: "";
}
.glyphicon-flash:before {
  content: "";
}
.glyphicon-log-out:before {
  content: "";
}
.glyphicon-new-window:before {
  content: "";
}
.glyphicon-record:before {
  content: "";
}
.glyphicon-save:before {
  content: "";
}
.glyphicon-open:before {
  content: "";
}
.glyphicon-saved:before {
  content: "";
}
.glyphicon-import:before {
  content: "";
}
.glyphicon-export:before {
  content: "";
}
.glyphicon-send:before {
  content: "";
}
.glyphicon-floppy-disk:before {
  content: "";
}
.glyphicon-floppy-saved:before {
  content: "";
}
.glyphicon-floppy-remove:before {
  content: "";
}
.glyphicon-floppy-save:before {
  content: "";
}
.glyphicon-floppy-open:before {
  content: "";
}
.glyphicon-credit-card:before {
  content: "";
}
.glyphicon-transfer:before {
  content: "";
}
.glyphicon-cutlery:before {
  content: "";
}
.glyphicon-header:before {
  content: "";
}
.glyphicon-compressed:before {
  content: "";
}
.glyphicon-earphone:before {
  content: "";
}
.glyphicon-phone-alt:before {
  content: "";
}
.glyphicon-tower:before {
  content: "";
}
.glyphicon-stats:before {
  content: "";
}
.glyphicon-sd-video:before {
  content: "";
}
.glyphicon-hd-video:before {
  content: "";
}
.glyphicon-subtitles:before {
  content: "";
}
.glyphicon-sound-stereo:before {
  content: "";
}
.glyphicon-sound-dolby:before {
  content: "";
}
.glyphicon-sound-5-1:before {
  content: "";
}
.glyphicon-sound-6-1:before {
  content: "";
}
.glyphicon-sound-7-1:before {
  content: "";
}
.glyphicon-copyright-mark:before {
  content: "";
}
.glyphicon-registration-mark:before {
  content: "";
}
.glyphicon-cloud-download:before {
  content: "";
}
.glyphicon-cloud-upload:before {
  content: "";
}
.glyphicon-tree-conifer:before {
  content: "";
}
.glyphicon-tree-deciduous:before {
  content: "";
}
.glyphicon-cd:before {
  content: "";
}
.glyphicon-save-file:before {
  content: "";
}
.glyphicon-open-file:before {
  content: "";
}
.glyphicon-level-up:before {
  content: "";
}
.glyphicon-copy:before {
  content: "";
}
.glyphicon-paste:before {
  content: "";
}
.glyphicon-alert:before {
  content: "";
}
.glyphicon-equalizer:before {
  content: "";
}
.glyphicon-king:before {
  content: "";
}
.glyphicon-queen:before {
  content: "";
}
.glyphicon-pawn:before {
  content: "";
}
.glyphicon-bishop:before {
  content: "";
}
.glyphicon-knight:before {
  content: "";
}
.glyphicon-baby-formula:before {
  content: "";
}
.glyphicon-tent:before {
  content: "⛺";
}
.glyphicon-blackboard:before {
  content: "";
}
.glyphicon-bed:before {
  content: "";
}
.glyphicon-apple:before {
  content: "";
}
.glyphicon-erase:before {
  content: "";
}
.glyphicon-hourglass:before {
  content: "⌛";
}
.glyphicon-lamp:before {
  content: "";
}
.glyphicon-duplicate:before {
  content: "";
}
.glyphicon-piggy-bank:before {
  content: "";
}
.glyphicon-scissors:before {
  content: "";
}
.glyphicon-bitcoin:before {
  content: "";
}
.glyphicon-btc:before {
  content: "";
}
.glyphicon-xbt:before {
  content: "";
}
.glyphicon-yen:before {
  content: "¥";
}
.glyphicon-jpy:before {
  content: "¥";
}
.glyphicon-ruble:before {
  content: "₽";
}
.glyphicon-rub:before {
  content: "₽";
}
.glyphicon-scale:before {
  content: "";
}
.glyphicon-ice-lolly:before {
  content: "";
}
.glyphicon-ice-lolly-tasted:before {
  content: "";
}
.glyphicon-education:before {
  content: "";
}
.glyphicon-option-horizontal:before {
  content: "";
}
.glyphicon-option-vertical:before {
  content: "";
}
.glyphicon-menu-hamburger:before {
  content: "";
}
.glyphicon-modal-window:before {
  content: "";
}
.glyphicon-oil:before {
  content: "";
}
.glyphicon-grain:before {
  content: "";
}
.glyphicon-sunglasses:before {
  content: "";
}
.glyphicon-text-size:before {
  content: "";
}
.glyphicon-text-color:before {
  content: "";
}
.glyphicon-text-background:before {
  content: "";
}
.glyphicon-object-align-top:before {
  content: "";
}
.glyphicon-object-align-bottom:before {
  content: "";
}
.glyphicon-object-align-horizontal:before {
  content: "";
}
.glyphicon-object-align-left:before {
  content: "";
}
.glyphicon-object-align-vertical:before {
  content: "";
}
.glyphicon-object-align-right:before {
  content: "";
}
.glyphicon-triangle-right:before {
  content: "";
}
.glyphicon-triangle-left:before {
  content: "";
}
.glyphicon-triangle-bottom:before {
  content: "";
}
.glyphicon-triangle-top:before {
  content: "";
}
.glyphicon-console:before {
  content: "";
}
.glyphicon-superscript:before {
  content: "";
}
.glyphicon-subscript:before {
  content: "";
}
.glyphicon-menu-left:before {
  content: "";
}
.glyphicon-menu-right:before {
  content: "";
}
.glyphicon-menu-down:before {
  content: "";
}
.glyphicon-menu-up:before {
  content: "";
}
* {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
*:before, *:after {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
html {
  font-size: 10px;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
body {
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 13px;
  line-height: 1.428571429;
  color: #333;
  background-color: #fff;
}
input, button, select, textarea {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}
a {
  color: #1e91cf;
  text-decoration: none;
}
a:hover, a:focus {
  color: #14628c;
  text-decoration: underline;
}
a:focus {
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px;
}
figure {
  margin: 0;
}
img {
  vertical-align: middle;
}
.img-responsive {
  display: block;
  max-width: 100%;
  height: auto;
}
.img-rounded {
  border-radius: 5px;
}
.img-thumbnail {
  padding: 4px;
  line-height: 1.428571429;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 3px;
  -webkit-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  display: inline-block;
  max-width: 100%;
  height: auto;
}
.img-circle {
  border-radius: 50%;
}
hr {
  margin-top: 18px;
  margin-bottom: 18px;
  border: 0;
  border-top: 1px solid #eee;
}
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}
.sr-only-focusable:active, .sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  margin: 0;
  overflow: visible;
  clip: auto;
}
[role="button"] {
  cursor: pointer;
}
h1, h2, h3, h4, h5, h6, .h1, .h2, .h3, .h4, .h5, .h6 {
  font-family: inherit;
  font-weight: 500;
  line-height: 1.1;
  color: inherit;
}
h1 small, h1 .small, h2 small, h2 .small, h3 small, h3 .small, h4 small, h4 .small, h5 small, h5 .small, h6 small, h6 .small, .h1 small, .h1 .small, .h2 small, .h2 .small, .h3 small, .h3 .small, .h4 small, .h4 .small, .h5 small, .h5 .small, .h6 small, .h6 .small {
  font-weight: normal;
  line-height: 1;
  color: #777;
}
h1, .h1, h2, .h2, h3, .h3 {
  margin-top: 18px;
  margin-bottom: 9px;
}
h1 small, h1 .small, .h1 small, .h1 .small, h2 small, h2 .small, .h2 small, .h2 .small, h3 small, h3 .small, .h3 small, .h3 .small {
  font-size: 65%;
}
h4, .h4, h5, .h5, h6, .h6 {
  margin-top: 9px;
  margin-bottom: 9px;
}
h4 small, h4 .small, .h4 small, .h4 .small, h5 small, h5 .small, .h5 small, .h5 .small, h6 small, h6 .small, .h6 small, .h6 .small {
  font-size: 75%;
}
h1, .h1 {
  font-size: 33px;
}
h2, .h2 {
  font-size: 27px;
}
h3, .h3 {
  font-size: 23px;
}
h4, .h4 {
  font-size: 17px;
}
h5, .h5 {
  font-size: 13px;
}
h6, .h6 {
  font-size: 12px;
}
p {
  margin: 0 0 9px;
}
.lead {
  margin-bottom: 18px;
  font-size: 14px;
  font-weight: 300;
  line-height: 1.4;
}
@media (min-width: 768px) {
  .lead {
    font-size: 19.5px;
  }
}
small, .small {
  font-size: 92%;
}
mark, .mark {
  background-color: #fce7c8;
  padding: 0.2em;
}
.text-left {
  text-align: right;
}
.text-right {
  text-align: left;
}
.text-center {
  text-align: center;
}
.text-justify {
  text-align: justify;
}
.text-nowrap {
  white-space: nowrap;
}
.text-lowercase {
  text-transform: lowercase;
}
.text-uppercase, .initialism {
  text-transform: uppercase;
}
.text-capitalize {
  text-transform: capitalize;
}
.text-muted {
  color: #777;
}
.text-primary {
  color: #1e91cf;
}
a.text-primary:hover, a.text-primary:focus {
  color: #1872a2;
}
.text-success {
  color: #4cb64c;
}
a.text-success:hover, a.text-success:focus {
  color: #3c933c;
}
.text-info {
  color: #31708f;
}
a.text-info:hover, a.text-info:focus {
  color: #245269;
}
.text-warning {
  color: #f3a638;
}
a.text-warning:hover, a.text-warning:focus {
  color: #ea8f0e;
}
.text-danger {
  color: #e3503e;
}
a.text-danger:hover, a.text-danger:focus {
  color: #d0321e;
}
.bg-primary {
  color: #fff;
}
.bg-primary {
  background-color: #1e91cf;
}
a.bg-primary:hover, a.bg-primary:focus {
  background-color: #1872a2;
}
.bg-success {
  background-color: #cbeacb;
}
a.bg-success:hover, a.bg-success:focus {
  background-color: #a7dba7;
}
.bg-info {
  background-color: #d9edf7;
}
a.bg-info:hover, a.bg-info:focus {
  background-color: #afd9ee;
}
.bg-warning {
  background-color: #fce7c8;
}
a.bg-warning:hover, a.bg-warning:focus {
  background-color: #f9d198;
}
.bg-danger {
  background-color: #f5c1bb;
}
a.bg-danger:hover, a.bg-danger:focus {
  background-color: #ef998e;
}
.page-header {
  padding-bottom: 8px;
  margin: 36px 0 18px;
  border-bottom: 1px solid #eee;
}
ul, ol {
  margin-top: 0;
  margin-bottom: 9px;
}
ul ul, ul ol, ol ul, ol ol {
  margin-bottom: 0;
}
.list-unstyled {
  padding-right: 0;
  list-style: none;
}
.list-inline {
  padding-right: 0;
  list-style: none;
  margin-right: -5px;
}
.list-inline > li {
  display: inline-block;
  padding-right: 5px;
  padding-left: 5px;
}
dl {
  margin-top: 0;
  margin-bottom: 18px;
}
dt, dd {
  line-height: 1.428571429;
}
dt {
  font-weight: bold;
}
dd {
  margin-right: 0;
}
.dl-horizontal dd:before, .dl-horizontal dd:after {
  content: " ";
  display: table;
}
.dl-horizontal dd:after {
  clear: both;
}
@media (min-width: 768px) {
  .dl-horizontal dt {
    float: right;
    width: 160px;
    clear: right;
    text-align: left;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .dl-horizontal dd {
    margin-right: 180px;
  }
}
abbr[title], abbr[data-original-title] {
  cursor: help;
  border-bottom: 1px dotted #777;
}
.initialism {
  font-size: 90%;
}
blockquote {
  padding: 9px 18px;
  margin: 0 0 18px;
  font-size: 16.25px;
  border-right: 5px solid #eee;
}
blockquote p:last-child, blockquote ul:last-child, blockquote ol:last-child {
  margin-bottom: 0;
}
blockquote footer, blockquote small, blockquote .small {
  display: block;
  font-size: 80%;
  line-height: 1.428571429;
  color: #777;
}
blockquote footer:before, blockquote small:before, blockquote .small:before {
  content: "— ";
}
.blockquote-reverse, blockquote.pull-right {
  padding-left: 15px;
  padding-right: 0;
  border-left: 5px solid #eee;
  border-right: 0;
  text-align: left;
}
.blockquote-reverse footer:before, .blockquote-reverse small:before, .blockquote-reverse .small:before, blockquote.pull-right footer:before, blockquote.pull-right small:before, blockquote.pull-right .small:before {
  content: "";
}
.blockquote-reverse footer:after, .blockquote-reverse small:after, .blockquote-reverse .small:after, blockquote.pull-right footer:after, blockquote.pull-right small:after, blockquote.pull-right .small:after {
  content: " —";
}
address {
  margin-bottom: 18px;
  font-style: normal;
  line-height: 1.428571429;
}
code, kbd, pre, samp {
  font-family: Menlo, Monaco, Consolas, "Courier New", monospace;
}
code {
  padding: 2px 4px;
  font-size: 90%;
  color: #c7254e;
  background-color: #f9f2f4;
  border-radius: 3px;
}
kbd {
  padding: 2px 4px;
  font-size: 90%;
  color: #fff;
  background-color: #333;
  border-radius: 2px;
  box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.25);
}
kbd kbd {
  padding: 0;
  font-size: 100%;
  font-weight: bold;
  box-shadow: none;
}
pre {
  display: block;
  padding: 8.5px;
  margin: 0 0 9px;
  font-size: 12px;
  line-height: 1.428571429;
  word-break: break-all;
  word-wrap: break-word;
  color: #333;
  background-color: #f5f5f5;
  border: 1px solid #ccc;
  border-radius: 3px;
}
pre code {
  padding: 0;
  font-size: inherit;
  color: inherit;
  white-space: pre-wrap;
  background-color: transparent;
  border-radius: 0;
}
.pre-scrollable {
  max-height: 340px;
  overflow-y: scroll;
}
.container {
  margin-left: auto;
  margin-right: auto;
  padding-right: 15px;
  padding-left: 15px;
}
.container:before, .container:after {
  content: " ";
  display: table;
}
.container:after {
  clear: both;
}
@media (min-width: 768px) {
  .container {
    width: 750px;
  }
}
@media (min-width: 992px) {
  .container {
    width: 970px;
  }
}
@media (min-width: 1200px) {
  .container {
    width: 1170px;
  }
}
.container-fluid {
  margin-left: auto;
  margin-right: auto;
  padding-right: 15px;
  padding-left: 15px;
}
.container-fluid:before, .container-fluid:after {
  content: " ";
  display: table;
}
.container-fluid:after {
  clear: both;
}
.row {
  margin-right: -15px;
  margin-left: -15px;
}
.row:before, .row:after {
  content: " ";
  display: table;
}
.row:after {
  clear: both;
}
.col-xs-1, .col-sm-1, .col-md-1, .col-lg-1, .col-xs-2, .col-sm-2, .col-md-2, .col-lg-2, .col-xs-3, .col-sm-3, .col-md-3, .col-lg-3, .col-xs-4, .col-sm-4, .col-md-4, .col-lg-4, .col-xs-5, .col-sm-5, .col-md-5, .col-lg-5, .col-xs-6, .col-sm-6, .col-md-6, .col-lg-6, .col-xs-7, .col-sm-7, .col-md-7, .col-lg-7, .col-xs-8, .col-sm-8, .col-md-8, .col-lg-8, .col-xs-9, .col-sm-9, .col-md-9, .col-lg-9, .col-xs-10, .col-sm-10, .col-md-10, .col-lg-10, .col-xs-11, .col-sm-11, .col-md-11, .col-lg-11, .col-xs-12, .col-sm-12, .col-md-12, .col-lg-12 {
  position: relative;
  min-height: 1px;
  padding-right: 15px;
  padding-left: 15px;
}
.col-xs-1, .col-xs-2, .col-xs-3, .col-xs-4, .col-xs-5, .col-xs-6, .col-xs-7, .col-xs-8, .col-xs-9, .col-xs-10, .col-xs-11, .col-xs-12 {
  float: right;
}
.col-xs-1 {
  width: 8.3333333333%;
}
.col-xs-2 {
  width: 16.6666666667%;
}
.col-xs-3 {
  width: 25%;
}
.col-xs-4 {
  width: 33.3333333333%;
}
.col-xs-5 {
  width: 41.6666666667%;
}
.col-xs-6 {
  width: 50%;
}
.col-xs-7 {
  width: 58.3333333333%;
}
.col-xs-8 {
  width: 66.6666666667%;
}
.col-xs-9 {
  width: 75%;
}
.col-xs-10 {
  width: 83.3333333333%;
}
.col-xs-11 {
  width: 91.6666666667%;
}
.col-xs-12 {
  width: 100%;
}
.col-xs-pull-0 {
  left: auto;
}
.col-xs-pull-1 {
  left: 8.3333333333%;
}
.col-xs-pull-2 {
  left: 16.6666666667%;
}
.col-xs-pull-3 {
  left: 25%;
}
.col-xs-pull-4 {
  left: 33.3333333333%;
}
.col-xs-pull-5 {
  left: 41.6666666667%;
}
.col-xs-pull-6 {
  left: 50%;
}
.col-xs-pull-7 {
  left: 58.3333333333%;
}
.col-xs-pull-8 {
  left: 66.6666666667%;
}
.col-xs-pull-9 {
  left: 75%;
}
.col-xs-pull-10 {
  left: 83.3333333333%;
}
.col-xs-pull-11 {
  left: 91.6666666667%;
}
.col-xs-pull-12 {
  left: 100%;
}
.col-xs-push-0 {
  right: auto;
}
.col-xs-push-1 {
  right: 8.3333333333%;
}
.col-xs-push-2 {
  right: 16.6666666667%;
}
.col-xs-push-3 {
  right: 25%;
}
.col-xs-push-4 {
  right: 33.3333333333%;
}
.col-xs-push-5 {
  right: 41.6666666667%;
}
.col-xs-push-6 {
  right: 50%;
}
.col-xs-push-7 {
  right: 58.3333333333%;
}
.col-xs-push-8 {
  right: 66.6666666667%;
}
.col-xs-push-9 {
  right: 75%;
}
.col-xs-push-10 {
  right: 83.3333333333%;
}
.col-xs-push-11 {
  right: 91.6666666667%;
}
.col-xs-push-12 {
  right: 100%;
}
.col-xs-offset-0 {
  margin-right: 0%;
}
.col-xs-offset-1 {
  margin-right: 8.3333333333%;
}
.col-xs-offset-2 {
  margin-right: 16.6666666667%;
}
.col-xs-offset-3 {
  margin-right: 25%;
}
.col-xs-offset-4 {
  margin-right: 33.3333333333%;
}
.col-xs-offset-5 {
  margin-right: 41.6666666667%;
}
.col-xs-offset-6 {
  margin-right: 50%;
}
.col-xs-offset-7 {
  margin-right: 58.3333333333%;
}
.col-xs-offset-8 {
  margin-right: 66.6666666667%;
}
.col-xs-offset-9 {
  margin-right: 75%;
}
.col-xs-offset-10 {
  margin-right: 83.3333333333%;
}
.col-xs-offset-11 {
  margin-right: 91.6666666667%;
}
.col-xs-offset-12 {
  margin-right: 100%;
}
@media (min-width: 768px) {
  .col-sm-1, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9, .col-sm-10, .col-sm-11, .col-sm-12 {
    float: right;
  }
  .col-sm-1 {
    width: 8.3333333333%;
  }
  .col-sm-2 {
    width: 16.6666666667%;
  }
  .col-sm-3 {
    width: 25%;
  }
  .col-sm-4 {
    width: 33.3333333333%;
  }
  .col-sm-5 {
    width: 41.6666666667%;
  }
  .col-sm-6 {
    width: 50%;
  }
  .col-sm-7 {
    width: 58.3333333333%;
  }
  .col-sm-8 {
    width: 66.6666666667%;
  }
  .col-sm-9 {
    width: 75%;
  }
  .col-sm-10 {
    width: 83.3333333333%;
  }
  .col-sm-11 {
    width: 91.6666666667%;
  }
  .col-sm-12 {
    width: 100%;
  }
  .col-sm-pull-0 {
    left: auto;
  }
  .col-sm-pull-1 {
    left: 8.3333333333%;
  }
  .col-sm-pull-2 {
    left: 16.6666666667%;
  }
  .col-sm-pull-3 {
    left: 25%;
  }
  .col-sm-pull-4 {
    left: 33.3333333333%;
  }
  .col-sm-pull-5 {
    left: 41.6666666667%;
  }
  .col-sm-pull-6 {
    left: 50%;
  }
  .col-sm-pull-7 {
    left: 58.3333333333%;
  }
  .col-sm-pull-8 {
    left: 66.6666666667%;
  }
  .col-sm-pull-9 {
    left: 75%;
  }
  .col-sm-pull-10 {
    left: 83.3333333333%;
  }
  .col-sm-pull-11 {
    left: 91.6666666667%;
  }
  .col-sm-pull-12 {
    left: 100%;
  }
  .col-sm-push-0 {
    right: auto;
  }
  .col-sm-push-1 {
    right: 8.3333333333%;
  }
  .col-sm-push-2 {
    right: 16.6666666667%;
  }
  .col-sm-push-3 {
    right: 25%;
  }
  .col-sm-push-4 {
    right: 33.3333333333%;
  }
  .col-sm-push-5 {
    right: 41.6666666667%;
  }
  .col-sm-push-6 {
    right: 50%;
  }
  .col-sm-push-7 {
    right: 58.3333333333%;
  }
  .col-sm-push-8 {
    right: 66.6666666667%;
  }
  .col-sm-push-9 {
    right: 75%;
  }
  .col-sm-push-10 {
    right: 83.3333333333%;
  }
  .col-sm-push-11 {
    right: 91.6666666667%;
  }
  .col-sm-push-12 {
    right: 100%;
  }
  .col-sm-offset-0 {
    margin-right: 0%;
  }
  .col-sm-offset-1 {
    margin-right: 8.3333333333%;
  }
  .col-sm-offset-2 {
    margin-right: 16.6666666667%;
  }
  .col-sm-offset-3 {
    margin-right: 25%;
  }
  .col-sm-offset-4 {
    margin-right: 33.3333333333%;
  }
  .col-sm-offset-5 {
    margin-right: 41.6666666667%;
  }
  .col-sm-offset-6 {
    margin-right: 50%;
  }
  .col-sm-offset-7 {
    margin-right: 58.3333333333%;
  }
  .col-sm-offset-8 {
    margin-right: 66.6666666667%;
  }
  .col-sm-offset-9 {
    margin-right: 75%;
  }
  .col-sm-offset-10 {
    margin-right: 83.3333333333%;
  }
  .col-sm-offset-11 {
    margin-right: 91.6666666667%;
  }
  .col-sm-offset-12 {
    margin-right: 100%;
  }
}
@media (min-width: 992px) {
  .col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12 {
    float: right;
  }
  .col-md-1 {
    width: 8.3333333333%;
  }
  .col-md-2 {
    width: 16.6666666667%;
  }
  .col-md-3 {
    width: 25%;
  }
  .col-md-4 {
    width: 33.3333333333%;
  }
  .col-md-5 {
    width: 41.6666666667%;
  }
  .col-md-6 {
    width: 50%;
  }
  .col-md-7 {
    width: 58.3333333333%;
  }
  .col-md-8 {
    width: 66.6666666667%;
  }
  .col-md-9 {
    width: 75%;
  }
  .col-md-10 {
    width: 83.3333333333%;
  }
  .col-md-11 {
    width: 91.6666666667%;
  }
  .col-md-12 {
    width: 100%;
  }
  .col-md-pull-0 {
    left: auto;
  }
  .col-md-pull-1 {
    left: 8.3333333333%;
  }
  .col-md-pull-2 {
    left: 16.6666666667%;
  }
  .col-md-pull-3 {
    left: 25%;
  }
  .col-md-pull-4 {
    left: 33.3333333333%;
  }
  .col-md-pull-5 {
    left: 41.6666666667%;
  }
  .col-md-pull-6 {
    left: 50%;
  }
  .col-md-pull-7 {
    left: 58.3333333333%;
  }
  .col-md-pull-8 {
    left: 66.6666666667%;
  }
  .col-md-pull-9 {
    left: 75%;
  }
  .col-md-pull-10 {
    left: 83.3333333333%;
  }
  .col-md-pull-11 {
    left: 91.6666666667%;
  }
  .col-md-pull-12 {
    left: 100%;
  }
  .col-md-push-0 {
    right: auto;
  }
  .col-md-push-1 {
    right: 8.3333333333%;
  }
  .col-md-push-2 {
    right: 16.6666666667%;
  }
  .col-md-push-3 {
    right: 25%;
  }
  .col-md-push-4 {
    right: 33.3333333333%;
  }
  .col-md-push-5 {
    right: 41.6666666667%;
  }
  .col-md-push-6 {
    right: 50%;
  }
  .col-md-push-7 {
    right: 58.3333333333%;
  }
  .col-md-push-8 {
    right: 66.6666666667%;
  }
  .col-md-push-9 {
    right: 75%;
  }
  .col-md-push-10 {
    right: 83.3333333333%;
  }
  .col-md-push-11 {
    right: 91.6666666667%;
  }
  .col-md-push-12 {
    right: 100%;
  }
  .col-md-offset-0 {
    margin-right: 0%;
  }
  .col-md-offset-1 {
    margin-right: 8.3333333333%;
  }
  .col-md-offset-2 {
    margin-right: 16.6666666667%;
  }
  .col-md-offset-3 {
    margin-right: 25%;
  }
  .col-md-offset-4 {
    margin-right: 33.3333333333%;
  }
  .col-md-offset-5 {
    margin-right: 41.6666666667%;
  }
  .col-md-offset-6 {
    margin-right: 50%;
  }
  .col-md-offset-7 {
    margin-right: 58.3333333333%;
  }
  .col-md-offset-8 {
    margin-right: 66.6666666667%;
  }
  .col-md-offset-9 {
    margin-right: 75%;
  }
  .col-md-offset-10 {
    margin-right: 83.3333333333%;
  }
  .col-md-offset-11 {
    margin-right: 91.6666666667%;
  }
  .col-md-offset-12 {
    margin-right: 100%;
  }
}
@media (min-width: 1200px) {
  .col-lg-1, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9, .col-lg-10, .col-lg-11, .col-lg-12 {
    float: right;
  }
  .col-lg-1 {
    width: 8.3333333333%;
  }
  .col-lg-2 {
    width: 16.6666666667%;
  }
  .col-lg-3 {
    width: 25%;
  }
  .col-lg-4 {
    width: 33.3333333333%;
  }
  .col-lg-5 {
    width: 41.6666666667%;
  }
  .col-lg-6 {
    width: 50%;
  }
  .col-lg-7 {
    width: 58.3333333333%;
  }
  .col-lg-8 {
    width: 66.6666666667%;
  }
  .col-lg-9 {
    width: 75%;
  }
  .col-lg-10 {
    width: 83.3333333333%;
  }
  .col-lg-11 {
    width: 91.6666666667%;
  }
  .col-lg-12 {
    width: 100%;
  }
  .col-lg-pull-0 {
    left: auto;
  }
  .col-lg-pull-1 {
    left: 8.3333333333%;
  }
  .col-lg-pull-2 {
    left: 16.6666666667%;
  }
  .col-lg-pull-3 {
    left: 25%;
  }
  .col-lg-pull-4 {
    left: 33.3333333333%;
  }
  .col-lg-pull-5 {
    left: 41.6666666667%;
  }
  .col-lg-pull-6 {
    left: 50%;
  }
  .col-lg-pull-7 {
    left: 58.3333333333%;
  }
  .col-lg-pull-8 {
    left: 66.6666666667%;
  }
  .col-lg-pull-9 {
    left: 75%;
  }
  .col-lg-pull-10 {
    left: 83.3333333333%;
  }
  .col-lg-pull-11 {
    left: 91.6666666667%;
  }
  .col-lg-pull-12 {
    left: 100%;
  }
  .col-lg-push-0 {
    right: auto;
  }
  .col-lg-push-1 {
    right: 8.3333333333%;
  }
  .col-lg-push-2 {
    right: 16.6666666667%;
  }
  .col-lg-push-3 {
    right: 25%;
  }
  .col-lg-push-4 {
    right: 33.3333333333%;
  }
  .col-lg-push-5 {
    right: 41.6666666667%;
  }
  .col-lg-push-6 {
    right: 50%;
  }
  .col-lg-push-7 {
    right: 58.3333333333%;
  }
  .col-lg-push-8 {
    right: 66.6666666667%;
  }
  .col-lg-push-9 {
    right: 75%;
  }
  .col-lg-push-10 {
    right: 83.3333333333%;
  }
  .col-lg-push-11 {
    right: 91.6666666667%;
  }
  .col-lg-push-12 {
    right: 100%;
  }
  .col-lg-offset-0 {
    margin-right: 0%;
  }
  .col-lg-offset-1 {
    margin-right: 8.3333333333%;
  }
  .col-lg-offset-2 {
    margin-right: 16.6666666667%;
  }
  .col-lg-offset-3 {
    margin-right: 25%;
  }
  .col-lg-offset-4 {
    margin-right: 33.3333333333%;
  }
  .col-lg-offset-5 {
    margin-right: 41.6666666667%;
  }
  .col-lg-offset-6 {
    margin-right: 50%;
  }
  .col-lg-offset-7 {
    margin-right: 58.3333333333%;
  }
  .col-lg-offset-8 {
    margin-right: 66.6666666667%;
  }
  .col-lg-offset-9 {
    margin-right: 75%;
  }
  .col-lg-offset-10 {
    margin-right: 83.3333333333%;
  }
  .col-lg-offset-11 {
    margin-right: 91.6666666667%;
  }
  .col-lg-offset-12 {
    margin-right: 100%;
  }
}
table {
  background-color: transparent;
}
caption {
  padding-top: 8px;
  padding-bottom: 8px;
  color: #777;
  text-align: right;
}
th {
  text-align: right;
}
.table {
  width: 100%;
  max-width: 100%;
  margin-bottom: 18px;
}
.table > thead > tr > th, .table > thead > tr > td, .table > tbody > tr > th, .table > tbody > tr > td, .table > tfoot > tr > th, .table > tfoot > tr > td {
  padding: 8px;
  line-height: 1.428571429;
  vertical-align: top;
  border-top: 1px solid #ddd;
}
.table > thead > tr > th {
  vertical-align: bottom;
  border-bottom: 2px solid #ddd;
}
.table > caption + thead > tr:first-child > th, .table > caption + thead > tr:first-child > td, .table > colgroup + thead > tr:first-child > th, .table > colgroup + thead > tr:first-child > td, .table > thead:first-child > tr:first-child > th, .table > thead:first-child > tr:first-child > td {
  border-top: 0;
}
.table > tbody + tbody {
  border-top: 2px solid #ddd;
}
.table .table {
  background-color: #fff;
}
.table-condensed > thead > tr > th, .table-condensed > thead > tr > td, .table-condensed > tbody > tr > th, .table-condensed > tbody > tr > td, .table-condensed > tfoot > tr > th, .table-condensed > tfoot > tr > td {
  padding: 5px;
}
.table-bordered {
  border: 1px solid #ddd;
}
.table-bordered > thead > tr > th, .table-bordered > thead > tr > td, .table-bordered > tbody > tr > th, .table-bordered > tbody > tr > td, .table-bordered > tfoot > tr > th, .table-bordered > tfoot > tr > td {
  border: 1px solid #ddd;
}
.table-bordered > thead > tr > th, .table-bordered > thead > tr > td {
  border-bottom-width: 2px;
}
.table-striped > tbody > tr:nth-of-type(odd) {
  background-color: #f9f9f9;
}
.table-hover > tbody > tr:hover {
  background-color: #f5f5f5;
}
table col[class*="col-"] {
  position: static;
  float: none;
  display: table-column;
}
table td[class*="col-"], table th[class*="col-"] {
  position: static;
  float: none;
  display: table-cell;
}
.table > thead > tr > td.active, .table > thead > tr > th.active, .table > thead > tr.active > td, .table > thead > tr.active > th, .table > tbody > tr > td.active, .table > tbody > tr > th.active, .table > tbody > tr.active > td, .table > tbody > tr.active > th, .table > tfoot > tr > td.active, .table > tfoot > tr > th.active, .table > tfoot > tr.active > td, .table > tfoot > tr.active > th {
  background-color: #f5f5f5;
}
.table-hover > tbody > tr > td.active:hover, .table-hover > tbody > tr > th.active:hover, .table-hover > tbody > tr.active:hover > td, .table-hover > tbody > tr:hover > .active, .table-hover > tbody > tr.active:hover > th {
  background-color: #e8e8e8;
}
.table > thead > tr > td.success, .table > thead > tr > th.success, .table > thead > tr.success > td, .table > thead > tr.success > th, .table > tbody > tr > td.success, .table > tbody > tr > th.success, .table > tbody > tr.success > td, .table > tbody > tr.success > th, .table > tfoot > tr > td.success, .table > tfoot > tr > th.success, .table > tfoot > tr.success > td, .table > tfoot > tr.success > th {
  background-color: #cbeacb;
}
.table-hover > tbody > tr > td.success:hover, .table-hover > tbody > tr > th.success:hover, .table-hover > tbody > tr.success:hover > td, .table-hover > tbody > tr:hover > .success, .table-hover > tbody > tr.success:hover > th {
  background-color: #b9e2b9;
}
.table > thead > tr > td.info, .table > thead > tr > th.info, .table > thead > tr.info > td, .table > thead > tr.info > th, .table > tbody > tr > td.info, .table > tbody > tr > th.info, .table > tbody > tr.info > td, .table > tbody > tr.info > th, .table > tfoot > tr > td.info, .table > tfoot > tr > th.info, .table > tfoot > tr.info > td, .table > tfoot > tr.info > th {
  background-color: #d9edf7;
}
.table-hover > tbody > tr > td.info:hover, .table-hover > tbody > tr > th.info:hover, .table-hover > tbody > tr.info:hover > td, .table-hover > tbody > tr:hover > .info, .table-hover > tbody > tr.info:hover > th {
  background-color: #c4e3f3;
}
.table > thead > tr > td.warning, .table > thead > tr > th.warning, .table > thead > tr.warning > td, .table > thead > tr.warning > th, .table > tbody > tr > td.warning, .table > tbody > tr > th.warning, .table > tbody > tr.warning > td, .table > tbody > tr.warning > th, .table > tfoot > tr > td.warning, .table > tfoot > tr > th.warning, .table > tfoot > tr.warning > td, .table > tfoot > tr.warning > th {
  background-color: #fce7c8;
}
.table-hover > tbody > tr > td.warning:hover, .table-hover > tbody > tr > th.warning:hover, .table-hover > tbody > tr.warning:hover > td, .table-hover > tbody > tr:hover > .warning, .table-hover > tbody > tr.warning:hover > th {
  background-color: #fadcb0;
}
.table > thead > tr > td.danger, .table > thead > tr > th.danger, .table > thead > tr.danger > td, .table > thead > tr.danger > th, .table > tbody > tr > td.danger, .table > tbody > tr > th.danger, .table > tbody > tr.danger > td, .table > tbody > tr.danger > th, .table > tfoot > tr > td.danger, .table > tfoot > tr > th.danger, .table > tfoot > tr.danger > td, .table > tfoot > tr.danger > th {
  background-color: #f5c1bb;
}
.table-hover > tbody > tr > td.danger:hover, .table-hover > tbody > tr > th.danger:hover, .table-hover > tbody > tr.danger:hover > td, .table-hover > tbody > tr:hover > .danger, .table-hover > tbody > tr.danger:hover > th {
  background-color: #f2ada4;
}
.table-responsive {
  overflow-x: auto;
  min-height: 0.01%;
}
@media screen and (max-width: 767px) {
  .table-responsive {
    width: 100%;
    margin-bottom: 13.5px;
    overflow-y: hidden;
    -ms-overflow-style: -ms-autohiding-scrollbar;
    border: 1px solid #ddd;
  }
  .table-responsive > .table {
    margin-bottom: 0;
  }
  .table-responsive > .table > thead > tr > th, .table-responsive > .table > thead > tr > td, .table-responsive > .table > tbody > tr > th, .table-responsive > .table > tbody > tr > td, .table-responsive > .table > tfoot > tr > th, .table-responsive > .table > tfoot > tr > td {
    white-space: nowrap;
  }
  .table-responsive > .table-bordered {
    border: 0;
  }
  .table-responsive > .table-bordered > thead > tr > th:first-child, .table-responsive > .table-bordered > thead > tr > td:first-child, .table-responsive > .table-bordered > tbody > tr > th:first-child, .table-responsive > .table-bordered > tbody > tr > td:first-child, .table-responsive > .table-bordered > tfoot > tr > th:first-child, .table-responsive > .table-bordered > tfoot > tr > td:first-child {
    border-right: 0;
  }
  .table-responsive > .table-bordered > thead > tr > th:last-child, .table-responsive > .table-bordered > thead > tr > td:last-child, .table-responsive > .table-bordered > tbody > tr > th:last-child, .table-responsive > .table-bordered > tbody > tr > td:last-child, .table-responsive > .table-bordered > tfoot > tr > th:last-child, .table-responsive > .table-bordered > tfoot > tr > td:last-child {
    border-left: 0;
  }
  .table-responsive > .table-bordered > tbody > tr:last-child > th, .table-responsive > .table-bordered > tbody > tr:last-child > td, .table-responsive > .table-bordered > tfoot > tr:last-child > th, .table-responsive > .table-bordered > tfoot > tr:last-child > td {
    border-bottom: 0;
  }
}
fieldset {
  padding: 0;
  margin: 0;
  border: 0;
  min-width: 0;
}
legend {
  display: block;
  width: 100%;
  padding: 0;
  margin-bottom: 18px;
  font-size: 19.5px;
  line-height: inherit;
  color: #333;
  border: 0;
  border-bottom: 1px solid #e5e5e5;
}
label {
  display: inline-block;
  max-width: 100%;
  margin-bottom: 5px;
  font-weight: bold;
}
input[type="search"] {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
input[type="radio"], input[type="checkbox"] {
  margin: 4px 0 0;
  margin-top: 1px \9 ;
  line-height: normal;
}
input[type="file"] {
  display: block;
}
input[type="range"] {
  display: block;
  width: 100%;
}
select[multiple], select[size] {
  height: auto;
}
input[type="file"]:focus, input[type="radio"]:focus, input[type="checkbox"]:focus {
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px;
}
output {
  display: block;
  padding-top: 9px;
  font-size: 13px;
  line-height: 1.428571429;
  color: #555;
}
.form-control {
  display: block;
  width: 100%;
  height: 36px;
  padding: 8px 13px;
  font-size: 13px;
  line-height: 1.428571429;
  color: #555;
  background-color: #fff;
  background-image: none;
  border: 1px solid #ccc;
  border-radius: 3px;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  -webkit-transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
  -o-transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
  transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
}
.form-control:focus {
  border-color: #66afe9;
  outline: 0;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(102, 175, 233, 0.6);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(102, 175, 233, 0.6);
}
.form-control::-moz-placeholder {
  color: #999;
  opacity: 1;
}
.form-control:-ms-input-placeholder {
  color: #999;
}
.form-control::-webkit-input-placeholder {
  color: #999;
}
.form-control::-ms-expand {
  border: 0;
  background-color: transparent;
}
.form-control[disabled], .form-control[readonly], fieldset[disabled] .form-control {
  background-color: #eee;
  opacity: 1;
}
.form-control[disabled], fieldset[disabled] .form-control {
  cursor: not-allowed;
}
textarea.form-control {
  height: auto;
}
input[type="search"] {
  -webkit-appearance: none;
}
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  input[type="date"].form-control, input[type="time"].form-control, input[type="datetime-local"].form-control, input[type="month"].form-control {
    line-height: 36px;
  }
  input[type="date"].input-sm, .input-group-sm > input.form-control[type="date"], .input-group-sm > input.input-group-addon[type="date"], .input-group-sm > .input-group-btn > input.btn[type="date"], .input-group-sm input[type="date"], input[type="time"].input-sm, .input-group-sm > input.form-control[type="time"], .input-group-sm > input.input-group-addon[type="time"], .input-group-sm > .input-group-btn > input.btn[type="time"], .input-group-sm input[type="time"], input[type="datetime-local"].input-sm, .input-group-sm > input.form-control[type="datetime-local"], .input-group-sm > input.input-group-addon[type="datetime-local"], .input-group-sm > .input-group-btn > input.btn[type="datetime-local"], .input-group-sm input[type="datetime-local"], input[type="month"].input-sm, .input-group-sm > input.form-control[type="month"], .input-group-sm > input.input-group-addon[type="month"], .input-group-sm > .input-group-btn > input.btn[type="month"], .input-group-sm input[type="month"] {
    line-height: 28px;
  }
  input[type="date"].input-lg, .input-group-lg > input.form-control[type="date"], .input-group-lg > input.input-group-addon[type="date"], .input-group-lg > .input-group-btn > input.btn[type="date"], .input-group-lg input[type="date"], input[type="time"].input-lg, .input-group-lg > input.form-control[type="time"], .input-group-lg > input.input-group-addon[type="time"], .input-group-lg > .input-group-btn > input.btn[type="time"], .input-group-lg input[type="time"], input[type="datetime-local"].input-lg, .input-group-lg > input.form-control[type="datetime-local"], .input-group-lg > input.input-group-addon[type="datetime-local"], .input-group-lg > .input-group-btn > input.btn[type="datetime-local"], .input-group-lg input[type="datetime-local"], input[type="month"].input-lg, .input-group-lg > input.form-control[type="month"], .input-group-lg > input.input-group-addon[type="month"], .input-group-lg > .input-group-btn > input.btn[type="month"], .input-group-lg input[type="month"] {
    line-height: 43px;
  }
}
.form-group {
  margin-bottom: 15px;
}
.radio, .checkbox {
  position: relative;
  display: block;
  margin-top: 10px;
  margin-bottom: 10px;
}
.radio label, .checkbox label {
  min-height: 18px;
  padding-right: 20px;
  margin-bottom: 0;
  font-weight: normal;
  cursor: pointer;
}
.radio input[type="radio"], .radio-inline input[type="radio"], .checkbox input[type="checkbox"], .checkbox-inline input[type="checkbox"] {
  position: absolute;
  margin-right: -20px;
  margin-top: 4px \9 ;
}
.radio + .radio, .checkbox + .checkbox {
  margin-top: -5px;
}
.radio-inline, .checkbox-inline {
  position: relative;
  display: inline-block;
  padding-right: 20px;
  margin-bottom: 0;
  vertical-align: middle;
  font-weight: normal;
  cursor: pointer;
}
.radio-inline + .radio-inline, .checkbox-inline + .checkbox-inline {
  margin-top: 0;
  margin-right: 10px;
}
input[type="radio"][disabled], input[type="radio"].disabled, fieldset[disabled] input[type="radio"], input[type="checkbox"][disabled], input[type="checkbox"].disabled, fieldset[disabled] input[type="checkbox"] {
  cursor: not-allowed;
}
.radio-inline.disabled, fieldset[disabled] .radio-inline, .checkbox-inline.disabled, fieldset[disabled] .checkbox-inline {
  cursor: not-allowed;
}
.radio.disabled label, fieldset[disabled] .radio label, .checkbox.disabled label, fieldset[disabled] .checkbox label {
  cursor: not-allowed;
}
.form-control-static {
  padding-top: 9px;
  padding-bottom: 9px;
  margin-bottom: 0;
  min-height: 31px;
}
.form-control-static.input-lg, .input-group-lg > .form-control-static.form-control, .input-group-lg > .form-control-static.input-group-addon, .input-group-lg > .input-group-btn > .form-control-static.btn, .form-control-static.input-sm, .input-group-sm > .form-control-static.form-control, .input-group-sm > .form-control-static.input-group-addon, .input-group-sm > .input-group-btn > .form-control-static.btn {
  padding-right: 0;
  padding-left: 0;
}
.input-sm, .input-group-sm > .form-control, .input-group-sm > .input-group-addon, .input-group-sm > .input-group-btn > .btn {
  height: 28px;
  padding: 4px 9px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 2px;
}
select.input-sm, .input-group-sm > select.form-control, .input-group-sm > select.input-group-addon, .input-group-sm > .input-group-btn > select.btn {
  height: 28px;
  line-height: 28px;
}
textarea.input-sm, .input-group-sm > textarea.form-control, .input-group-sm > textarea.input-group-addon, .input-group-sm > .input-group-btn > textarea.btn, select[multiple].input-sm, .input-group-sm > select.form-control[multiple], .input-group-sm > select.input-group-addon[multiple], .input-group-sm > .input-group-btn > select.btn[multiple] {
  height: auto;
}
.form-group-sm .form-control {
  height: 28px;
  padding: 4px 9px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 2px;
}
.form-group-sm select.form-control {
  height: 28px;
  line-height: 28px;
}
.form-group-sm textarea.form-control, .form-group-sm select[multiple].form-control {
  height: auto;
}
.form-group-sm .form-control-static {
  height: 28px;
  min-height: 30px;
  padding: 5px 9px;
  font-size: 12px;
  line-height: 1.5;
}
.input-lg, .input-group-lg > .form-control, .input-group-lg > .input-group-addon, .input-group-lg > .input-group-btn > .btn {
  height: 43px;
  padding: 9px 18px;
  font-size: 17px;
  line-height: 1.3333333;
  border-radius: 5px;
}
select.input-lg, .input-group-lg > select.form-control, .input-group-lg > select.input-group-addon, .input-group-lg > .input-group-btn > select.btn {
  height: 43px;
  line-height: 43px;
}
textarea.input-lg, .input-group-lg > textarea.form-control, .input-group-lg > textarea.input-group-addon, .input-group-lg > .input-group-btn > textarea.btn, select[multiple].input-lg, .input-group-lg > select.form-control[multiple], .input-group-lg > select.input-group-addon[multiple], .input-group-lg > .input-group-btn > select.btn[multiple] {
  height: auto;
}
.form-group-lg .form-control {
  height: 43px;
  padding: 9px 18px;
  font-size: 17px;
  line-height: 1.3333333;
  border-radius: 5px;
}
.form-group-lg select.form-control {
  height: 43px;
  line-height: 43px;
}
.form-group-lg textarea.form-control, .form-group-lg select[multiple].form-control {
  height: auto;
}
.form-group-lg .form-control-static {
  height: 43px;
  min-height: 35px;
  padding: 10px 18px;
  font-size: 17px;
  line-height: 1.3333333;
}
.has-feedback {
  position: relative;
}
.has-feedback .form-control {
  padding-left: 45px;
}
.form-control-feedback {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
  display: block;
  width: 36px;
  height: 36px;
  line-height: 36px;
  text-align: center;
  pointer-events: none;
}
.input-lg + .form-control-feedback, .input-group-lg > .form-control + .form-control-feedback, .input-group-lg > .input-group-addon + .form-control-feedback, .input-group-lg > .input-group-btn > .btn + .form-control-feedback, .input-group-lg + .form-control-feedback, .form-group-lg .form-control + .form-control-feedback {
  width: 43px;
  height: 43px;
  line-height: 43px;
}
.input-sm + .form-control-feedback, .input-group-sm > .form-control + .form-control-feedback, .input-group-sm > .input-group-addon + .form-control-feedback, .input-group-sm > .input-group-btn > .btn + .form-control-feedback, .input-group-sm + .form-control-feedback, .form-group-sm .form-control + .form-control-feedback {
  width: 28px;
  height: 28px;
  line-height: 28px;
}
.has-success .help-block, .has-success .control-label, .has-success .radio, .has-success .checkbox, .has-success .radio-inline, .has-success .checkbox-inline, .has-success.radio label, .has-success.checkbox label, .has-success.radio-inline label, .has-success.checkbox-inline label {
  color: #4cb64c;
}
.has-success .form-control {
  border-color: #4cb64c;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}
.has-success .form-control:focus {
  border-color: #3c933c;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #94d494;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #94d494;
}
.has-success .input-group-addon {
  color: #4cb64c;
  border-color: #4cb64c;
  background-color: #cbeacb;
}
.has-success .form-control-feedback {
  color: #4cb64c;
}
.has-warning .help-block, .has-warning .control-label, .has-warning .radio, .has-warning .checkbox, .has-warning .radio-inline, .has-warning .checkbox-inline, .has-warning.radio label, .has-warning.checkbox label, .has-warning.radio-inline label, .has-warning.checkbox-inline label {
  color: #f3a638;
}
.has-warning .form-control {
  border-color: #f3a638;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}
.has-warning .form-control:focus {
  border-color: #ea8f0e;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #f9d198;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #f9d198;
}
.has-warning .input-group-addon {
  color: #f3a638;
  border-color: #f3a638;
  background-color: #fce7c8;
}
.has-warning .form-control-feedback {
  color: #f3a638;
}
.has-error .help-block, .has-error .control-label, .has-error .radio, .has-error .checkbox, .has-error .radio-inline, .has-error .checkbox-inline, .has-error.radio label, .has-error.checkbox label, .has-error.radio-inline label, .has-error.checkbox-inline label {
  color: #e3503e;
}
.has-error .form-control {
  border-color: #e3503e;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
}
.has-error .form-control:focus {
  border-color: #d0321e;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #f0a197;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 6px #f0a197;
}
.has-error .input-group-addon {
  color: #e3503e;
  border-color: #e3503e;
  background-color: #f5c1bb;
}
.has-error .form-control-feedback {
  color: #e3503e;
}
.has-feedback label ~ .form-control-feedback {
  top: 23px;
}
.has-feedback label.sr-only ~ .form-control-feedback {
  top: 0;
}
.help-block {
  display: block;
  margin-top: 5px;
  margin-bottom: 10px;
  color: #737373;
}
@media (min-width: 768px) {
  .form-inline .form-group {
    display: inline-block;
    margin-bottom: 0;
    vertical-align: middle;
  }
  .form-inline .form-control {
    display: inline-block;
    width: auto;
    vertical-align: middle;
  }
  .form-inline .form-control-static {
    display: inline-block;
  }
  .form-inline .input-group {
    display: inline-table;
    vertical-align: middle;
  }
  .form-inline .input-group .input-group-addon, .form-inline .input-group .input-group-btn, .form-inline .input-group .form-control {
    width: auto;
  }
  .form-inline .input-group > .form-control {
    width: 100%;
  }
  .form-inline .control-label {
    margin-bottom: 0;
    vertical-align: middle;
  }
  .form-inline .radio, .form-inline .checkbox {
    display: inline-block;
    margin-top: 0;
    margin-bottom: 0;
    vertical-align: middle;
  }
  .form-inline .radio label, .form-inline .checkbox label {
    padding-right: 0;
  }
  .form-inline .radio input[type="radio"], .form-inline .checkbox input[type="checkbox"] {
    position: relative;
    margin-right: 0;
  }
  .form-inline .has-feedback .form-control-feedback {
    top: 0;
  }
}
.form-horizontal .radio, .form-horizontal .checkbox, .form-horizontal .radio-inline, .form-horizontal .checkbox-inline {
  margin-top: 0;
  margin-bottom: 0;
  padding-top: 9px;
}
.form-horizontal .radio, .form-horizontal .checkbox {
  min-height: 27px;
}
.form-horizontal .form-group {
  margin-right: -15px;
  margin-left: -15px;
}
.form-horizontal .form-group:before, .form-horizontal .form-group:after {
  content: " ";
  display: table;
}
.form-horizontal .form-group:after {
  clear: both;
}
@media (min-width: 768px) {
  .form-horizontal .control-label {
    text-align: left;
    margin-bottom: 0;
    padding-top: 9px;
  }
}
.form-horizontal .has-feedback .form-control-feedback {
  left: 15px;
}
@media (min-width: 768px) {
  .form-horizontal .form-group-lg .control-label {
    padding-top: 10px;
    font-size: 17px;
  }
}
@media (min-width: 768px) {
  .form-horizontal .form-group-sm .control-label {
    padding-top: 5px;
    font-size: 12px;
  }
}
.btn {
  display: inline-block;
  margin-bottom: 0;
  font-weight: normal;
  text-align: center;
  vertical-align: middle;
  touch-action: manipulation;
  cursor: pointer;
  background-image: none;
  border: 1px solid transparent;
  white-space: nowrap;
  padding: 8px 13px;
  font-size: 13px;
  line-height: 1.428571429;
  border-radius: 3px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.btn:focus, .btn.focus, .btn:active:focus, .btn:active.focus, .btn.active:focus, .btn.active.focus {
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px;
}
.btn:hover, .btn:focus, .btn.focus {
  color: #666;
  text-decoration: none;
}
.btn:active, .btn.active {
  outline: 0;
  background-image: none;
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
}
.btn.disabled, .btn[disabled], fieldset[disabled] .btn {
  cursor: not-allowed;
  opacity: 0.65;
  filter: alpha(opacity=65);
  -webkit-box-shadow: none;
  box-shadow: none;
}
a.btn.disabled, fieldset[disabled] a.btn {
  pointer-events: none;
}
.btn-default {
  color: #666;
  background-color: #fff;
  border-color: #ddd;
}
.btn-default:focus, .btn-default.focus {
  color: #666;
  background-color: #e6e6e6;
  border-color: #9d9d9d;
}
.btn-default:hover {
  color: #666;
  background-color: #e6e6e6;
  border-color: #bebebe;
}
.btn-default:active, .btn-default.active, .open > .btn-default.dropdown-toggle {
  color: #666;
  background-color: #e6e6e6;
  border-color: #bebebe;
}
.btn-default:active:hover, .btn-default:active:focus, .btn-default:active.focus, .btn-default.active:hover, .btn-default.active:focus, .btn-default.active.focus, .open > .btn-default.dropdown-toggle:hover, .open > .btn-default.dropdown-toggle:focus, .open > .btn-default.dropdown-toggle.focus {
  color: #666;
  background-color: #d4d4d4;
  border-color: #9d9d9d;
}
.btn-default:active, .btn-default.active, .open > .btn-default.dropdown-toggle {
  background-image: none;
}
.btn-default.disabled:hover, .btn-default.disabled:focus, .btn-default.disabled.focus, .btn-default[disabled]:hover, .btn-default[disabled]:focus, .btn-default[disabled].focus, fieldset[disabled] .btn-default:hover, fieldset[disabled] .btn-default:focus, fieldset[disabled] .btn-default.focus {
  background-color: #fff;
  border-color: #ddd;
}
.btn-default .badge {
  color: #fff;
  background-color: #666;
}
.btn-primary {
  color: #fff;
  background-color: #1e91cf;
  border-color: #197bb0;
}
.btn-primary:focus, .btn-primary.focus {
  color: #fff;
  background-color: #1872a2;
  border-color: #092d40;
}
.btn-primary:hover {
  color: #fff;
  background-color: #1872a2;
  border-color: #12567a;
}
.btn-primary:active, .btn-primary.active, .open > .btn-primary.dropdown-toggle {
  color: #fff;
  background-color: #1872a2;
  border-color: #12567a;
}
.btn-primary:active:hover, .btn-primary:active:focus, .btn-primary:active.focus, .btn-primary.active:hover, .btn-primary.active:focus, .btn-primary.active.focus, .open > .btn-primary.dropdown-toggle:hover, .open > .btn-primary.dropdown-toggle:focus, .open > .btn-primary.dropdown-toggle.focus {
  color: #fff;
  background-color: #135c83;
  border-color: #092d40;
}
.btn-primary:active, .btn-primary.active, .open > .btn-primary.dropdown-toggle {
  background-image: none;
}
.btn-primary.disabled:hover, .btn-primary.disabled:focus, .btn-primary.disabled.focus, .btn-primary[disabled]:hover, .btn-primary[disabled]:focus, .btn-primary[disabled].focus, fieldset[disabled] .btn-primary:hover, fieldset[disabled] .btn-primary:focus, fieldset[disabled] .btn-primary.focus {
  background-color: #1e91cf;
  border-color: #197bb0;
}
.btn-primary .badge {
  color: #1e91cf;
  background-color: #fff;
}
.btn-success {
  color: #fff;
  background-color: #4cb64c;
  border-color: #409e40;
}
.btn-success:focus, .btn-success.focus {
  color: #fff;
  background-color: #3c933c;
  border-color: #1b431b;
}
.btn-success:hover {
  color: #fff;
  background-color: #3c933c;
  border-color: #2f722f;
}
.btn-success:active, .btn-success.active, .open > .btn-success.dropdown-toggle {
  color: #fff;
  background-color: #3c933c;
  border-color: #2f722f;
}
.btn-success:active:hover, .btn-success:active:focus, .btn-success:active.focus, .btn-success.active:hover, .btn-success.active:focus, .btn-success.active.focus, .open > .btn-success.dropdown-toggle:hover, .open > .btn-success.dropdown-toggle:focus, .open > .btn-success.dropdown-toggle.focus {
  color: #fff;
  background-color: #327a32;
  border-color: #1b431b;
}
.btn-success:active, .btn-success.active, .open > .btn-success.dropdown-toggle {
  background-image: none;
}
.btn-success.disabled:hover, .btn-success.disabled:focus, .btn-success.disabled.focus, .btn-success[disabled]:hover, .btn-success[disabled]:focus, .btn-success[disabled].focus, fieldset[disabled] .btn-success:hover, fieldset[disabled] .btn-success:focus, fieldset[disabled] .btn-success.focus {
  background-color: #4cb64c;
  border-color: #409e40;
}
.btn-success .badge {
  color: #4cb64c;
  background-color: #fff;
}
.btn-info {
  color: #fff;
  background-color: #54b7d3;
  border-color: #38abcc;
}
.btn-info:focus, .btn-info.focus {
  color: #fff;
  background-color: #32a2c2;
  border-color: #1b5869;
}
.btn-info:hover {
  color: #fff;
  background-color: #32a2c2;
  border-color: #29849e;
}
.btn-info:active, .btn-info.active, .open > .btn-info.dropdown-toggle {
  color: #fff;
  background-color: #32a2c2;
  border-color: #29849e;
}
.btn-info:active:hover, .btn-info:active:focus, .btn-info:active.focus, .btn-info.active:hover, .btn-info.active:focus, .btn-info.active.focus, .open > .btn-info.dropdown-toggle:hover, .open > .btn-info.dropdown-toggle:focus, .open > .btn-info.dropdown-toggle.focus {
  color: #fff;
  background-color: #2b8ba6;
  border-color: #1b5869;
}
.btn-info:active, .btn-info.active, .open > .btn-info.dropdown-toggle {
  background-image: none;
}
.btn-info.disabled:hover, .btn-info.disabled:focus, .btn-info.disabled.focus, .btn-info[disabled]:hover, .btn-info[disabled]:focus, .btn-info[disabled].focus, fieldset[disabled] .btn-info:hover, fieldset[disabled] .btn-info:focus, fieldset[disabled] .btn-info.focus {
  background-color: #54b7d3;
  border-color: #38abcc;
}
.btn-info .badge {
  color: #54b7d3;
  background-color: #fff;
}
.btn-warning {
  color: #fff;
  background-color: #f3a638;
  border-color: #f19716;
}
.btn-warning:focus, .btn-warning.focus {
  color: #fff;
  background-color: #ea8f0e;
  border-color: #804f08;
}
.btn-warning:hover {
  color: #fff;
  background-color: #ea8f0e;
  border-color: #bf750b;
}
.btn-warning:active, .btn-warning.active, .open > .btn-warning.dropdown-toggle {
  color: #fff;
  background-color: #ea8f0e;
  border-color: #bf750b;
}
.btn-warning:active:hover, .btn-warning:active:focus, .btn-warning:active.focus, .btn-warning.active:hover, .btn-warning.active:focus, .btn-warning.active.focus, .open > .btn-warning.dropdown-toggle:hover, .open > .btn-warning.dropdown-toggle:focus, .open > .btn-warning.dropdown-toggle.focus {
  color: #fff;
  background-color: #c87b0c;
  border-color: #804f08;
}
.btn-warning:active, .btn-warning.active, .open > .btn-warning.dropdown-toggle {
  background-image: none;
}
.btn-warning.disabled:hover, .btn-warning.disabled:focus, .btn-warning.disabled.focus, .btn-warning[disabled]:hover, .btn-warning[disabled]:focus, .btn-warning[disabled].focus, fieldset[disabled] .btn-warning:hover, fieldset[disabled] .btn-warning:focus, fieldset[disabled] .btn-warning.focus {
  background-color: #f3a638;
  border-color: #f19716;
}
.btn-warning .badge {
  color: #f3a638;
  background-color: #fff;
}
.btn-danger {
  color: #fff;
  background-color: #e3503e;
  border-color: #dd3520;
}
.btn-danger:focus, .btn-danger.focus {
  color: #fff;
  background-color: #d0321e;
  border-color: #6e1a10;
}
.btn-danger:hover {
  color: #fff;
  background-color: #d0321e;
  border-color: #a82818;
}
.btn-danger:active, .btn-danger.active, .open > .btn-danger.dropdown-toggle {
  color: #fff;
  background-color: #d0321e;
  border-color: #a82818;
}
.btn-danger:active:hover, .btn-danger:active:focus, .btn-danger:active.focus, .btn-danger.active:hover, .btn-danger.active:focus, .btn-danger.active.focus, .open > .btn-danger.dropdown-toggle:hover, .open > .btn-danger.dropdown-toggle:focus, .open > .btn-danger.dropdown-toggle.focus {
  color: #fff;
  background-color: #b12a1a;
  border-color: #6e1a10;
}
.btn-danger:active, .btn-danger.active, .open > .btn-danger.dropdown-toggle {
  background-image: none;
}
.btn-danger.disabled:hover, .btn-danger.disabled:focus, .btn-danger.disabled.focus, .btn-danger[disabled]:hover, .btn-danger[disabled]:focus, .btn-danger[disabled].focus, fieldset[disabled] .btn-danger:hover, fieldset[disabled] .btn-danger:focus, fieldset[disabled] .btn-danger.focus {
  background-color: #e3503e;
  border-color: #dd3520;
}
.btn-danger .badge {
  color: #e3503e;
  background-color: #fff;
}
.btn-link {
  color: #1e91cf;
  font-weight: normal;
  border-radius: 0;
}
.btn-link, .btn-link:active, .btn-link.active, .btn-link[disabled], fieldset[disabled] .btn-link {
  background-color: transparent;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.btn-link, .btn-link:hover, .btn-link:focus, .btn-link:active {
  border-color: transparent;
}
.btn-link:hover, .btn-link:focus {
  color: #14628c;
  text-decoration: underline;
  background-color: transparent;
}
.btn-link[disabled]:hover, .btn-link[disabled]:focus, fieldset[disabled] .btn-link:hover, fieldset[disabled] .btn-link:focus {
  color: #777;
  text-decoration: none;
}
.btn-lg, .btn-group-lg > .btn {
  padding: 9px 18px;
  font-size: 17px;
  line-height: 1.3333333;
  border-radius: 5px;
}
.btn-sm, .btn-group-sm > .btn {
  padding: 4px 9px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-xs, .btn-group-xs > .btn {
  padding: 1px 5px;
  font-size: 12px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-block {
  display: block;
  width: 100%;
}
.btn-block + .btn-block {
  margin-top: 5px;
}
input[type="submit"].btn-block, input[type="reset"].btn-block, input[type="button"].btn-block {
  width: 100%;
}
.fade {
  opacity: 0;
  -webkit-transition: opacity 0.15s linear;
  -o-transition: opacity 0.15s linear;
  transition: opacity 0.15s linear;
}
.fade.in {
  opacity: 1;
}
.collapse {
  display: none;
}
.collapse.in {
  display: block;
}
tr.collapse.in {
  display: table-row;
}
tbody.collapse.in {
  display: table-row-group;
}
.collapsing {
  position: relative;
  height: 0;
  overflow: hidden;
  -webkit-transition-property: height, visibility;
  transition-property: height, visibility;
  -webkit-transition-duration: 0.35s;
  transition-duration: 0.35s;
  -webkit-transition-timing-function: ease;
  transition-timing-function: ease;
}
.caret {
  display: inline-block;
  width: 0;
  height: 0;
  margin-right: 2px;
  vertical-align: middle;
  border-top: 4px dashed;
  border-top: 4px solid \9 ;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
}
.dropup, .dropdown {
  position: relative;
}
.dropdown-toggle:focus {
  outline: 0;
}
.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  z-index: 1000;
  display: none;
  float: right;
  min-width: 160px;
  padding: 5px 0;
  margin: 2px 0 0;
  list-style: none;
  font-size: 13px;
  text-align: right;
  background-color: #fff;
  border: 1px solid #ccc;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 3px;
  -webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);
  background-clip: padding-box;
}
.dropdown-menu.pull-right {
  left: 0;
  right: auto;
}
.dropdown-menu .divider {
  height: 1px;
  margin: 8px 0;
  overflow: hidden;
  background-color: #e5e5e5;
}
.dropdown-menu > li > a {
  display: block;
  padding: 5px 20px;
  clear: both;
  font-weight: normal;
  line-height: 1.428571429;
  color: #333;
  white-space: nowrap;
}
.dropdown-menu > li > a:hover, .dropdown-menu > li > a:focus {
  text-decoration: none;
  color: #262626;
  background-color: #f5f5f5;
}
.dropdown-menu > .active > a, .dropdown-menu > .active > a:hover, .dropdown-menu > .active > a:focus {
  color: #fff;
  text-decoration: none;
  outline: 0;
  background-color: #1e91cf;
}
.dropdown-menu > .disabled > a, .dropdown-menu > .disabled > a:hover, .dropdown-menu > .disabled > a:focus {
  color: #777;
}
.dropdown-menu > .disabled > a:hover, .dropdown-menu > .disabled > a:focus {
  text-decoration: none;
  background-color: transparent;
  background-image: none;
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
  cursor: not-allowed;
}
.open > .dropdown-menu {
  display: block;
}
.open > a {
  outline: 0;
}
.dropdown-menu-right {
  right: auto;
  left: 0;
}
.dropdown-menu-left {
  right: 0;
  left: auto;
}
.dropdown-header {
  display: block;
  padding: 3px 20px;
  font-size: 12px;
  line-height: 1.428571429;
  color: #777;
  white-space: nowrap;
}
.dropdown-backdrop {
  position: fixed;
  right: 0;
  left: 0;
  bottom: 0;
  top: 0;
  z-index: 990;
}
.pull-right > .dropdown-menu {
  left: 0;
  right: auto;
}
.dropup .caret, .navbar-fixed-bottom .dropdown .caret {
  border-top: 0;
  border-bottom: 4px dashed;
  border-bottom: 4px solid \9 ;
  content: "";
}
.dropup .dropdown-menu, .navbar-fixed-bottom .dropdown .dropdown-menu {
  top: auto;
  bottom: 100%;
  margin-bottom: 2px;
}
@media (min-width: 768px) {
  .navbar-right .dropdown-menu {
    left: 0;
    right: auto;
  }
  .navbar-right .dropdown-menu-left {
    right: 0;
    left: auto;
  }
}
.btn-group, .btn-group-vertical {
  position: relative;
  display: inline-block;
  vertical-align: middle;
}
.btn-group > .btn, .btn-group-vertical > .btn {
  position: relative;
  float: right;
}
.btn-group > .btn:hover, .btn-group > .btn:focus, .btn-group > .btn:active, .btn-group > .btn.active, .btn-group-vertical > .btn:hover, .btn-group-vertical > .btn:focus, .btn-group-vertical > .btn:active, .btn-group-vertical > .btn.active {
  z-index: 2;
}
.btn-group .btn + .btn, .btn-group .btn + .btn-group, .btn-group .btn-group + .btn, .btn-group .btn-group + .btn-group {
  margin-right: -1px;
}
.btn-toolbar {
  margin-right: -5px;
}
.btn-toolbar:before, .btn-toolbar:after {
  content: " ";
  display: table;
}
.btn-toolbar:after {
  clear: both;
}
.btn-toolbar .btn, .btn-toolbar .btn-group, .btn-toolbar .input-group {
  float: right;
}
.btn-toolbar > .btn, .btn-toolbar > .btn-group, .btn-toolbar > .input-group {
  margin-right: 5px;
}
.btn-group > .btn:not(:first-child):not(:last-child):not(.dropdown-toggle) {
  border-radius: 0;
}
.btn-group > .btn:first-child {
  margin-right: 0;
}
.btn-group > .btn:first-child:not(:last-child):not(.dropdown-toggle) {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
}
.btn-group > .btn:last-child:not(:first-child), .btn-group > .dropdown-toggle:not(:first-child) {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
}
.btn-group > .btn-group {
  float: right;
}
.btn-group > .btn-group:not(:first-child):not(:last-child) > .btn {
  border-radius: 0;
}
.btn-group > .btn-group:first-child:not(:last-child) > .btn:last-child, .btn-group > .btn-group:first-child:not(:last-child) > .dropdown-toggle {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
}
.btn-group > .btn-group:last-child:not(:first-child) > .btn:first-child {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
}
.btn-group .dropdown-toggle:active, .btn-group.open .dropdown-toggle {
  outline: 0;
}
.btn-group > .btn + .dropdown-toggle {
  padding-right: 8px;
  padding-left: 8px;
}
.btn-group > .btn-lg + .dropdown-toggle, .btn-group-lg.btn-group > .btn + .dropdown-toggle {
  padding-right: 12px;
  padding-left: 12px;
}
.btn-group.open .dropdown-toggle {
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
}
.btn-group.open .dropdown-toggle.btn-link {
  -webkit-box-shadow: none;
  box-shadow: none;
}
.btn .caret {
  margin-right: 0;
}
.btn-lg .caret, .btn-group-lg > .btn .caret {
  border-width: 5px 5px 0;
  border-bottom-width: 0;
}
.dropup .btn-lg .caret, .dropup .btn-group-lg > .btn .caret {
  border-width: 0 5px 5px;
}
.btn-group-vertical > .btn, .btn-group-vertical > .btn-group, .btn-group-vertical > .btn-group > .btn {
  display: block;
  float: none;
  width: 100%;
  max-width: 100%;
}
.btn-group-vertical > .btn-group:before, .btn-group-vertical > .btn-group:after {
  content: " ";
  display: table;
}
.btn-group-vertical > .btn-group:after {
  clear: both;
}
.btn-group-vertical > .btn-group > .btn {
  float: none;
}
.btn-group-vertical > .btn + .btn, .btn-group-vertical > .btn + .btn-group, .btn-group-vertical > .btn-group + .btn, .btn-group-vertical > .btn-group + .btn-group {
  margin-top: -1px;
  margin-right: 0;
}
.btn-group-vertical > .btn:not(:first-child):not(:last-child) {
  border-radius: 0;
}
.btn-group-vertical > .btn:first-child:not(:last-child) {
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}
.btn-group-vertical > .btn:last-child:not(:first-child) {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  border-bottom-left-radius: 3px;
  border-bottom-right-radius: 3px;
}
.btn-group-vertical > .btn-group:not(:first-child):not(:last-child) > .btn {
  border-radius: 0;
}
.btn-group-vertical > .btn-group:first-child:not(:last-child) > .btn:last-child, .btn-group-vertical > .btn-group:first-child:not(:last-child) > .dropdown-toggle {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}
.btn-group-vertical > .btn-group:last-child:not(:first-child) > .btn:first-child {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}
.btn-group-justified {
  display: table;
  width: 100%;
  table-layout: fixed;
  border-collapse: separate;
}
.btn-group-justified > .btn, .btn-group-justified > .btn-group {
  float: none;
  display: table-cell;
  width: 1%;
}
.btn-group-justified > .btn-group .btn {
  width: 100%;
}
.btn-group-justified > .btn-group .dropdown-menu {
  right: auto;
}
[data-toggle="buttons"] > .btn input[type="radio"], [data-toggle="buttons"] > .btn input[type="checkbox"], [data-toggle="buttons"] > .btn-group > .btn input[type="radio"], [data-toggle="buttons"] > .btn-group > .btn input[type="checkbox"] {
  position: absolute;
  clip: rect(0, 0, 0, 0);
  pointer-events: none;
}
.input-group {
  position: relative;
  display: table;
  border-collapse: separate;
}
.input-group[class*="col-"] {
  float: none;
  padding-right: 0;
  padding-left: 0;
}
.input-group .form-control {
  position: relative;
  z-index: 2;
  float: right;
  width: 100%;
  margin-bottom: 0;
}
.input-group .form-control:focus {
  z-index: 3;
}
.input-group-addon, .input-group-btn, .input-group .form-control {
  display: table-cell;
}
.input-group-addon:not(:first-child):not(:last-child), .input-group-btn:not(:first-child):not(:last-child), .input-group .form-control:not(:first-child):not(:last-child) {
  border-radius: 0;
}
.input-group-addon, .input-group-btn {
  width: 1%;
  white-space: nowrap;
  vertical-align: middle;
}
.input-group-addon {
  padding: 8px 13px;
  font-size: 13px;
  font-weight: normal;
  line-height: 1;
  color: #555;
  text-align: center;
  background-color: #eee;
  border: 1px solid #ccc;
  border-radius: 3px;
}
.input-group-addon.input-sm, .input-group-sm > .input-group-addon.form-control, .input-group-sm > .input-group-addon, .input-group-sm > .input-group-btn > .input-group-addon.btn {
  padding: 4px 9px;
  font-size: 12px;
  border-radius: 2px;
}
.input-group-addon.input-lg, .input-group-lg > .input-group-addon.form-control, .input-group-lg > .input-group-addon, .input-group-lg > .input-group-btn > .input-group-addon.btn {
  padding: 9px 18px;
  font-size: 17px;
  border-radius: 5px;
}
.input-group-addon input[type="radio"], .input-group-addon input[type="checkbox"] {
  margin-top: 0;
}
.input-group .form-control:first-child, .input-group-addon:first-child, .input-group-btn:first-child > .btn, .input-group-btn:first-child > .btn-group > .btn, .input-group-btn:first-child > .dropdown-toggle, .input-group-btn:last-child > .btn:not(:last-child):not(.dropdown-toggle), .input-group-btn:last-child > .btn-group:not(:last-child) > .btn {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
}
.input-group-addon:first-child {
  border-left: 0;
}
.input-group .form-control:last-child, .input-group-addon:last-child, .input-group-btn:last-child > .btn, .input-group-btn:last-child > .btn-group > .btn, .input-group-btn:last-child > .dropdown-toggle, .input-group-btn:first-child > .btn:not(:first-child), .input-group-btn:first-child > .btn-group:not(:first-child) > .btn {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
}
.input-group-addon:last-child {
  border-right: 0;
}
.input-group-btn {
  position: relative;
  font-size: 0;
  white-space: nowrap;
}
.input-group-btn > .btn {
  position: relative;
}
.input-group-btn > .btn + .btn {
  margin-right: -1px;
}
.input-group-btn > .btn:hover, .input-group-btn > .btn:focus, .input-group-btn > .btn:active {
  z-index: 2;
}
.input-group-btn:first-child > .btn, .input-group-btn:first-child > .btn-group {
  margin-left: -1px;
}
.input-group-btn:last-child > .btn, .input-group-btn:last-child > .btn-group {
  z-index: 2;
  margin-right: -1px;
}
.nav {
  margin-bottom: 0;
  padding-right: 0;
  list-style: none;
}
.nav:before, .nav:after {
  content: " ";
  display: table;
}
.nav:after {
  clear: both;
}
.nav > li {
  position: relative;
  display: block;
}
.nav > li > a {
  position: relative;
  display: block;
  padding: 10px 15px;
}
.nav > li > a:hover, .nav > li > a:focus {
  text-decoration: none;
  background-color: #eee;
}
.nav > li.disabled > a {
  color: #777;
}
.nav > li.disabled > a:hover, .nav > li.disabled > a:focus {
  color: #777;
  text-decoration: none;
  background-color: transparent;
  cursor: not-allowed;
}
.nav .open > a, .nav .open > a:hover, .nav .open > a:focus {
  background-color: #eee;
  border-color: #1e91cf;
}
.nav .nav-divider {
  height: 1px;
  margin: 8px 0;
  overflow: hidden;
  background-color: #e5e5e5;
}
.nav > li > a > img {
  max-width: none;
}
.nav-tabs {
  border-bottom: 1px solid #ddd;
}
.nav-tabs > li {
  float: right;
  margin-bottom: -1px;
}
.nav-tabs > li > a {
  margin-left: 2px;
  line-height: 1.428571429;
  border: 1px solid transparent;
  border-radius: 3px 3px 0 0;
}
.nav-tabs > li > a:hover {
  border-color: #eee #eee #ddd;
}
.nav-tabs > li.active > a, .nav-tabs > li.active > a:hover, .nav-tabs > li.active > a:focus {
  color: #555;
  background-color: #fff;
  border: 1px solid #ddd;
  border-bottom-color: transparent;
  cursor: default;
}
.nav-pills > li {
  float: right;
}
.nav-pills > li > a {
  border-radius: 3px;
}
.nav-pills > li + li {
  margin-right: 2px;
}
.nav-pills > li.active > a, .nav-pills > li.active > a:hover, .nav-pills > li.active > a:focus {
  color: #fff;
  background-color: #1e91cf;
}
.nav-stacked > li {
  float: none;
}
.nav-stacked > li + li {
  margin-top: 2px;
  margin-right: 0;
}
.nav-justified, .nav-tabs.nav-justified {
  width: 100%;
}
.nav-justified > li, .nav-tabs.nav-justified > li {
  float: none;
}
.nav-justified > li > a, .nav-tabs.nav-justified > li > a {
  text-align: center;
  margin-bottom: 5px;
}
.nav-justified > .dropdown .dropdown-menu, .nav-tabs.nav-justified > .dropdown .dropdown-menu {
  top: auto;
  right: auto;
}
@media (min-width: 768px) {
  .nav-justified > li, .nav-tabs.nav-justified > li {
    display: table-cell;
    width: 1%;
  }
  .nav-justified > li > a, .nav-tabs.nav-justified > li > a {
    margin-bottom: 0;
  }
}
.nav-tabs-justified, .nav-tabs.nav-justified {
  border-bottom: 0;
}
.nav-tabs-justified > li > a, .nav-tabs.nav-justified > li > a {
  margin-left: 0;
  border-radius: 3px;
}
.nav-tabs-justified > .active > a, .nav-tabs.nav-justified > .active > a, .nav-tabs-justified > .active > a:hover, .nav-tabs.nav-justified > .active > a:hover, .nav-tabs-justified > .active > a:focus, .nav-tabs.nav-justified > .active > a:focus {
  border: 1px solid #ddd;
}
@media (min-width: 768px) {
  .nav-tabs-justified > li > a, .nav-tabs.nav-justified > li > a {
    border-bottom: 1px solid #ddd;
    border-radius: 3px 3px 0 0;
  }
  .nav-tabs-justified > .active > a, .nav-tabs.nav-justified > .active > a, .nav-tabs-justified > .active > a:hover, .nav-tabs.nav-justified > .active > a:hover, .nav-tabs-justified > .active > a:focus, .nav-tabs.nav-justified > .active > a:focus {
    border-bottom-color: #fff;
  }
}
.tab-content > .tab-pane {
  display: none;
}
.tab-content > .active {
  display: block;
}
.nav-tabs .dropdown-menu {
  margin-top: -1px;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}
.navbar {
  position: relative;
  min-height: 60px;
  margin-bottom: 18px;
  border: 1px solid transparent;
}
.navbar:before, .navbar:after {
  content: " ";
  display: table;
}
.navbar:after {
  clear: both;
}
@media (min-width: 768px) {
  .navbar {
    border-radius: 3px;
  }
}
.navbar-header:before, .navbar-header:after {
  content: " ";
  display: table;
}
.navbar-header:after {
  clear: both;
}
@media (min-width: 768px) {
  .navbar-header {
    float: right;
  }
}
.navbar-collapse {
  overflow-x: visible;
  padding-left: 15px;
  padding-right: 15px;
  border-top: 1px solid transparent;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1);
  -webkit-overflow-scrolling: touch;
}
.navbar-collapse:before, .navbar-collapse:after {
  content: " ";
  display: table;
}
.navbar-collapse:after {
  clear: both;
}
.navbar-collapse.in {
  overflow-y: auto;
}
@media (min-width: 768px) {
  .navbar-collapse {
    width: auto;
    border-top: 0;
    box-shadow: none;
  }
  .navbar-collapse.collapse {
    display: block !important;
    height: auto !important;
    padding-bottom: 0;
    overflow: visible !important;
  }
  .navbar-collapse.in {
    overflow-y: visible;
  }
  .navbar-fixed-top .navbar-collapse, .navbar-static-top .navbar-collapse, .navbar-fixed-bottom .navbar-collapse {
    padding-right: 0;
    padding-left: 0;
  }
}
.navbar-fixed-top .navbar-collapse, .navbar-fixed-bottom .navbar-collapse {
  max-height: 340px;
}
@media (max-device-width: 480px) and (orientation: landscape) {
  .navbar-fixed-top .navbar-collapse, .navbar-fixed-bottom .navbar-collapse {
    max-height: 200px;
  }
}
.container > .navbar-header, .container > .navbar-collapse, .container-fluid > .navbar-header, .container-fluid > .navbar-collapse {
  margin-left: -15px;
  margin-right: -15px;
}
@media (min-width: 768px) {
  .container > .navbar-header, .container > .navbar-collapse, .container-fluid > .navbar-header, .container-fluid > .navbar-collapse {
    margin-left: 0;
    margin-right: 0;
  }
}
.navbar-static-top {
  z-index: 1000;
  border-width: 0 0 1px;
}
@media (min-width: 768px) {
  .navbar-static-top {
    border-radius: 0;
  }
}
.navbar-fixed-top, .navbar-fixed-bottom {
  position: fixed;
  left: 0;
  right: 0;
  z-index: 1030;
}
@media (min-width: 768px) {
  .navbar-fixed-top, .navbar-fixed-bottom {
    border-radius: 0;
  }
}
.navbar-fixed-top {
  top: 0;
  border-width: 0 0 1px;
}
.navbar-fixed-bottom {
  bottom: 0;
  margin-bottom: 0;
  border-width: 1px 0 0;
}
.navbar-brand {
  float: right;
  padding: 14px 15px;
  font-size: 17px;
  line-height: 18px;
  height: 60px;
}
.navbar-brand:hover, .navbar-brand:focus {
  text-decoration: none;
}
.navbar-brand > img {
  display: block;
}
@media (min-width: 768px) {
  .navbar > .container .navbar-brand, .navbar > .container-fluid .navbar-brand {
    margin-right: -15px;
  }
}
@media screen and (max-width: 768px) {
  .navbar-brand {
    padding: 6px 7.5px;
    height: 45px;
    padding-right: 12px;
    padding-top: 5.6px;
    padding-bottom: 7px;
    padding-left: 15px;
  }
}
.navbar-toggle {
  position: relative;
  float: left;
  margin-left: 15px;
  padding: 9px 10px;
  margin-top: 13px;
  margin-bottom: 13px;
  background-color: transparent;
  background-image: none;
  border: 1px solid transparent;
  border-radius: 3px;
}
.navbar-toggle:focus {
  outline: 0;
}
.navbar-toggle .icon-bar {
  display: block;
  width: 22px;
  height: 2px;
  border-radius: 1px;
}
.navbar-toggle .icon-bar + .icon-bar {
  margin-top: 4px;
}
@media (min-width: 768px) {
  .navbar-toggle {
    display: none;
  }
}
.navbar-nav {
  margin: 7px -15px;
}
.navbar-nav > li > a {
  padding-top: 10px;
  padding-bottom: 10px;
  line-height: 18px;
}
@media (max-width: 767px) {
  .navbar-nav .open .dropdown-menu {
    position: absolute;
    float: none;
    width: auto;
    margin-top: 0;
    background-color: #fff;
    border: 1px solid #eee;
    box-shadow: none;
  }
  .navbar-nav .open .dropdown-menu > li > a, .navbar-nav .open .dropdown-menu .dropdown-header {
    padding: 5px 25px 5px 15px;
  }
  .navbar-nav .open .dropdown-menu > li > a {
    line-height: 18px;
  }
  .navbar-nav .open .dropdown-menu > li > a:hover, .navbar-nav .open .dropdown-menu > li > a:focus {
    background-image: none;
  }
}
@media (min-width: 768px) {
  .navbar-nav {
    float: right;
    margin: 0;
  }
  .navbar-nav > li {
    float: right;
  }
  .navbar-nav > li > a {
    padding-top: 14px;
    padding-bottom: 14px;
  }
}
.navbar-form {
  margin-right: -15px;
  margin-left: -15px;
  padding: 10px 15px;
  border-top: 1px solid transparent;
  border-bottom: 1px solid transparent;
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1), 0 1px 0 rgba(255, 255, 255, 0.1);
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1), 0 1px 0 rgba(255, 255, 255, 0.1);
  margin-top: 12px;
  margin-bottom: 12px;
}
@media (min-width: 768px) {
  .navbar-form .form-group {
    display: inline-block;
    margin-bottom: 0;
    vertical-align: middle;
  }
  .navbar-form .form-control {
    display: inline-block;
    width: auto;
    vertical-align: middle;
  }
  .navbar-form .form-control-static {
    display: inline-block;
  }
  .navbar-form .input-group {
    display: inline-table;
    vertical-align: middle;
  }
  .navbar-form .input-group .input-group-addon, .navbar-form .input-group .input-group-btn, .navbar-form .input-group .form-control {
    width: auto;
  }
  .navbar-form .input-group > .form-control {
    width: 100%;
  }
  .navbar-form .control-label {
    margin-bottom: 0;
    vertical-align: middle;
  }
  .navbar-form .radio, .navbar-form .checkbox {
    display: inline-block;
    margin-top: 0;
    margin-bottom: 0;
    vertical-align: middle;
  }
  .navbar-form .radio label, .navbar-form .checkbox label {
    padding-right: 0;
  }
  .navbar-form .radio input[type="radio"], .navbar-form .checkbox input[type="checkbox"] {
    position: relative;
    margin-right: 0;
  }
  .navbar-form .has-feedback .form-control-feedback {
    top: 0;
  }
}
@media (max-width: 767px) {
  .navbar-form .form-group {
    margin-bottom: 5px;
  }
  .navbar-form .form-group:last-child {
    margin-bottom: 0;
  }
}
@media (min-width: 768px) {
  .navbar-form {
    width: auto;
    border: 0;
    margin-right: 0;
    margin-left: 0;
    padding-top: 0;
    padding-bottom: 0;
    -webkit-box-shadow: none;
    box-shadow: none;
  }
}
.navbar-nav > li > .dropdown-menu {
  margin-top: 0;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}
.navbar-fixed-bottom .navbar-nav > li > .dropdown-menu {
  margin-bottom: 0;
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}
.navbar-btn {
  margin-top: 12px;
  margin-bottom: 12px;
}
.navbar-btn.btn-sm, .btn-group-sm > .navbar-btn.btn {
  margin-top: 16px;
  margin-bottom: 16px;
}
.navbar-btn.btn-xs, .btn-group-xs > .navbar-btn.btn {
  margin-top: 19px;
  margin-bottom: 19px;
}
.navbar-text {
  margin-top: 10px;
  margin-bottom: 10px;
}
@media (min-width: 768px) {
  .navbar-text {
    float: right;
    margin-right: 15px;
    margin-left: 15px;
  }
}
@media screen and (max-width: 767px) {
  .navbar-text {
    margin-top: 3px;
    margin-bottom: 2px;
    margin-right: 5px;
    margin-left: 5px;
  }
}
@media (min-width: 768px) {
  .navbar-left {
    float: right !important;
  }
  .navbar-right {
    float: left !important;
    margin-left: -15px;
  }
  .navbar-right ~ .navbar-right {
    margin-left: 0;
  }
}
.navbar-default {
  background-color: #f8f8f8;
  border-color: #e7e7e7;
}
.navbar-default .navbar-brand {
  color: #777;
}
.navbar-default .navbar-brand:hover, .navbar-default .navbar-brand:focus {
  color: #5e5e5e;
  background-color: transparent;
}
.navbar-default .navbar-text {
  color: #777;
}
.navbar-default .navbar-nav > li > a {
  color: #777;
}
.navbar-default .navbar-nav > li > a:hover, .navbar-default .navbar-nav > li > a:focus {
  color: #333;
  background-color: transparent;
}
.navbar-default .navbar-nav > .active > a, .navbar-default .navbar-nav > .active > a:hover, .navbar-default .navbar-nav > .active > a:focus {
  color: #555;
  background-color: #e7e7e7;
}
.navbar-default .navbar-nav > .disabled > a, .navbar-default .navbar-nav > .disabled > a:hover, .navbar-default .navbar-nav > .disabled > a:focus {
  color: #ccc;
  background-color: transparent;
}
.navbar-default .navbar-toggle {
  border-color: #ddd;
}
.navbar-default .navbar-toggle:hover, .navbar-default .navbar-toggle:focus {
  background-color: #ddd;
}
.navbar-default .navbar-toggle .icon-bar {
  background-color: #888;
}
.navbar-default .navbar-collapse, .navbar-default .navbar-form {
  border-color: #e7e7e7;
}
.navbar-default .navbar-nav > .open > a, .navbar-default .navbar-nav > .open > a:hover, .navbar-default .navbar-nav > .open > a:focus {
  background-color: #e7e7e7;
  color: #555;
}
@media (max-width: 767px) {
  .navbar-default .navbar-nav .open .dropdown-menu > li > a {
    color: #777;
  }
  .navbar-default .navbar-nav .open .dropdown-menu > li > a:hover, .navbar-default .navbar-nav .open .dropdown-menu > li > a:focus {
    color: #333;
    background-color: transparent;
  }
  .navbar-default .navbar-nav .open .dropdown-menu > .active > a, .navbar-default .navbar-nav .open .dropdown-menu > .active > a:hover, .navbar-default .navbar-nav .open .dropdown-menu > .active > a:focus {
    color: #555;
    background-color: #e7e7e7;
  }
  .navbar-default .navbar-nav .open .dropdown-menu > .disabled > a, .navbar-default .navbar-nav .open .dropdown-menu > .disabled > a:hover, .navbar-default .navbar-nav .open .dropdown-menu > .disabled > a:focus {
    color: #ccc;
    background-color: transparent;
  }
}
.navbar-default .navbar-link {
  color: #777;
}
.navbar-default .navbar-link:hover {
  color: #333;
}
.navbar-default .btn-link {
  color: #777;
}
.navbar-default .btn-link:hover, .navbar-default .btn-link:focus {
  color: #333;
}
.navbar-default .btn-link[disabled]:hover, .navbar-default .btn-link[disabled]:focus, fieldset[disabled] .navbar-default .btn-link:hover, fieldset[disabled] .navbar-default .btn-link:focus {
  color: #ccc;
}
.navbar-inverse {
  background-color: #222;
  border-color: #090909;
}
.navbar-inverse .navbar-brand {
  color: #9d9d9d;
}
.navbar-inverse .navbar-brand:hover, .navbar-inverse .navbar-brand:focus {
  color: #fff;
  background-color: transparent;
}
.navbar-inverse .navbar-text {
  color: #9d9d9d;
}
.navbar-inverse .navbar-nav > li > a {
  color: #9d9d9d;
}
.navbar-inverse .navbar-nav > li > a:hover, .navbar-inverse .navbar-nav > li > a:focus {
  color: #fff;
  background-color: transparent;
}
.navbar-inverse .navbar-nav > .active > a, .navbar-inverse .navbar-nav > .active > a:hover, .navbar-inverse .navbar-nav > .active > a:focus {
  color: #fff;
  background-color: #090909;
}
.navbar-inverse .navbar-nav > .disabled > a, .navbar-inverse .navbar-nav > .disabled > a:hover, .navbar-inverse .navbar-nav > .disabled > a:focus {
  color: #444;
  background-color: transparent;
}
.navbar-inverse .navbar-toggle {
  border-color: #333;
}
.navbar-inverse .navbar-toggle:hover, .navbar-inverse .navbar-toggle:focus {
  background-color: #333;
}
.navbar-inverse .navbar-toggle .icon-bar {
  background-color: #fff;
}
.navbar-inverse .navbar-collapse, .navbar-inverse .navbar-form {
  border-color: #101010;
}
.navbar-inverse .navbar-nav > .open > a, .navbar-inverse .navbar-nav > .open > a:hover, .navbar-inverse .navbar-nav > .open > a:focus {
  background-color: #090909;
  color: #fff;
}
@media (max-width: 767px) {
  .navbar-inverse .navbar-nav .open .dropdown-menu > .dropdown-header {
    border-color: #090909;
  }
  .navbar-inverse .navbar-nav .open .dropdown-menu .divider {
    background-color: #090909;
  }
  .navbar-inverse .navbar-nav .open .dropdown-menu > li > a {
    color: #9d9d9d;
  }
  .navbar-inverse .navbar-nav .open .dropdown-menu > li > a:hover, .navbar-inverse .navbar-nav .open .dropdown-menu > li > a:focus {
    color: #fff;
    background-color: transparent;
  }
  .navbar-inverse .navbar-nav .open .dropdown-menu > .active > a, .navbar-inverse .navbar-nav .open .dropdown-menu > .active > a:hover, .navbar-inverse .navbar-nav .open .dropdown-menu > .active > a:focus {
    color: #fff;
    background-color: #090909;
  }
  .navbar-inverse .navbar-nav .open .dropdown-menu > .disabled > a, .navbar-inverse .navbar-nav .open .dropdown-menu > .disabled > a:hover, .navbar-inverse .navbar-nav .open .dropdown-menu > .disabled > a:focus {
    color: #444;
    background-color: transparent;
  }
}
.navbar-inverse .navbar-link {
  color: #9d9d9d;
}
.navbar-inverse .navbar-link:hover {
  color: #fff;
}
.navbar-inverse .btn-link {
  color: #9d9d9d;
}
.navbar-inverse .btn-link:hover, .navbar-inverse .btn-link:focus {
  color: #fff;
}
.navbar-inverse .btn-link[disabled]:hover, .navbar-inverse .btn-link[disabled]:focus, fieldset[disabled] .navbar-inverse .btn-link:hover, fieldset[disabled] .navbar-inverse .btn-link:focus {
  color: #444;
}
.breadcrumb {
  padding: 8px 15px;
  margin-bottom: 18px;
  list-style: none;
  background-color: #f5f5f5;
  border-radius: 3px;
}
.breadcrumb > li {
  display: inline-block;
  text-shadow: 0 1px #fff;
}
.breadcrumb > li + li:before {
  content: "/ ";
  padding: 0 5px;
  color: #ccc;
}
.breadcrumb > li:last-child a {
  color: #1e91cf;
}
.breadcrumb > .active {
  color: #777;
}
.pagination {
  display: inline-block;
  padding-right: 0;
  margin: 18px 0;
  border-radius: 3px;
}
.pagination > li {
  display: inline;
}
.pagination > li > a, .pagination > li > span {
  position: relative;
  float: right;
  padding: 8px 13px;
  line-height: 1.428571429;
  text-decoration: none;
  color: #1e91cf;
  background-color: #fff;
  border: 1px solid #ddd;
  margin-right: -1px;
}
.pagination > li:first-child > a, .pagination > li:first-child > span {
  margin-right: 0;
  border-bottom-right-radius: 3px;
  border-top-right-radius: 3px;
}
.pagination > li:last-child > a, .pagination > li:last-child > span {
  border-bottom-left-radius: 3px;
  border-top-left-radius: 3px;
}
.pagination > li > a:hover, .pagination > li > a:focus, .pagination > li > span:hover, .pagination > li > span:focus {
  z-index: 2;
  color: #14628c;
  background-color: #eee;
  border-color: #ddd;
}
.pagination > .active > a, .pagination > .active > a:hover, .pagination > .active > a:focus, .pagination > .active > span, .pagination > .active > span:hover, .pagination > .active > span:focus {
  z-index: 3;
  color: #fff;
  background-color: #1e91cf;
  border-color: #1e91cf;
  cursor: default;
}
.pagination > .disabled > span, .pagination > .disabled > span:hover, .pagination > .disabled > span:focus, .pagination > .disabled > a, .pagination > .disabled > a:hover, .pagination > .disabled > a:focus {
  color: #777;
  background-color: #fff;
  border-color: #ddd;
  cursor: not-allowed;
}
.pagination-lg > li > a, .pagination-lg > li > span {
  padding: 9px 18px;
  font-size: 17px;
  line-height: 1.3333333;
}
.pagination-lg > li:first-child > a, .pagination-lg > li:first-child > span {
  border-bottom-right-radius: 5px;
  border-top-right-radius: 5px;
}
.pagination-lg > li:last-child > a, .pagination-lg > li:last-child > span {
  border-bottom-left-radius: 5px;
  border-top-left-radius: 5px;
}
.pagination-sm > li > a, .pagination-sm > li > span {
  padding: 4px 9px;
  font-size: 12px;
  line-height: 1.5;
}
.pagination-sm > li:first-child > a, .pagination-sm > li:first-child > span {
  border-bottom-right-radius: 2px;
  border-top-right-radius: 2px;
}
.pagination-sm > li:last-child > a, .pagination-sm > li:last-child > span {
  border-bottom-left-radius: 2px;
  border-top-left-radius: 2px;
}
.pager {
  padding-right: 0;
  margin: 18px 0;
  list-style: none;
  text-align: center;
}
.pager:before, .pager:after {
  content: " ";
  display: table;
}
.pager:after {
  clear: both;
}
.pager li {
  display: inline;
}
.pager li > a, .pager li > span {
  display: inline-block;
  padding: 5px 14px;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 15px;
}
.pager li > a:hover, .pager li > a:focus {
  text-decoration: none;
  background-color: #eee;
}
.pager .next > a, .pager .next > span {
  float: left;
}
.pager .previous > a, .pager .previous > span {
  float: right;
}
.pager .disabled > a, .pager .disabled > a:hover, .pager .disabled > a:focus, .pager .disabled > span {
  color: #777;
  background-color: #fff;
  cursor: not-allowed;
}
.label {
  display: inline;
  padding: 0.2em 0.6em 0.3em;
  font-size: 75%;
  font-weight: bold;
  line-height: 1;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0.25em;
}
.label:empty {
  display: none;
}
.btn .label {
  position: relative;
  top: -1px;
}
a.label:hover, a.label:focus {
  color: #fff;
  text-decoration: none;
  cursor: pointer;
}
.label-default {
  background-color: #777;
}
.label-default[href]:hover, .label-default[href]:focus {
  background-color: #5e5e5e;
}
.label-primary {
  background-color: #1e91cf;
}
.label-primary[href]:hover, .label-primary[href]:focus {
  background-color: #1872a2;
}
.label-success {
  background-color: #4cb64c;
}
.label-success[href]:hover, .label-success[href]:focus {
  background-color: #3c933c;
}
.label-info {
  background-color: #54b7d3;
}
.label-info[href]:hover, .label-info[href]:focus {
  background-color: #32a2c2;
}
.label-warning {
  background-color: #f3a638;
}
.label-warning[href]:hover, .label-warning[href]:focus {
  background-color: #ea8f0e;
}
.label-danger {
  background-color: #e3503e;
}
.label-danger[href]:hover, .label-danger[href]:focus {
  background-color: #d0321e;
}
.badge {
  display: inline-block;
  min-width: 10px;
  padding: 3px 7px;
  font-size: 12px;
  font-weight: bold;
  color: #fff;
  line-height: 1;
  vertical-align: middle;
  white-space: nowrap;
  text-align: center;
  background-color: #777;
  border-radius: 10px;
}
.badge:empty {
  display: none;
}
.btn .badge {
  position: relative;
  top: -1px;
}
.btn-xs .badge, .btn-group-xs > .btn .badge {
  top: 0;
  padding: 1px 5px;
}
.list-group-item.active > .badge, .nav-pills > .active > a > .badge {
  color: #1e91cf;
  background-color: #fff;
}
.list-group-item > .badge {
  float: left;
}
.list-group-item > .badge + .badge {
  margin-left: 5px;
}
.nav-pills > li > a > .badge {
  margin-right: 3px;
}
a.badge:hover, a.badge:focus {
  color: #fff;
  text-decoration: none;
  cursor: pointer;
}
.jumbotron {
  padding-top: 30px;
  padding-bottom: 30px;
  margin-bottom: 30px;
  color: inherit;
  background-color: #eee;
}
.jumbotron h1, .jumbotron .h1 {
  color: inherit;
}
.jumbotron p {
  margin-bottom: 15px;
  font-size: 20px;
  font-weight: 200;
}
.jumbotron > hr {
  border-top-color: #d5d5d5;
}
.container .jumbotron, .container-fluid .jumbotron {
  border-radius: 5px;
  padding-right: 15px;
  padding-left: 15px;
}
.jumbotron .container {
  max-width: 100%;
}
@media screen and (min-width: 768px) {
  .jumbotron {
    padding-top: 48px;
    padding-bottom: 48px;
  }
  .container .jumbotron, .container-fluid .jumbotron {
    padding-right: 60px;
    padding-left: 60px;
  }
  .jumbotron h1, .jumbotron .h1 {
    font-size: 59px;
  }
}
.thumbnail {
  display: block;
  padding: 4px;
  margin-bottom: 18px;
  line-height: 1.428571429;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 3px;
  -webkit-transition: border 0.2s ease-in-out;
  -o-transition: border 0.2s ease-in-out;
  transition: border 0.2s ease-in-out;
}
.thumbnail > img, .thumbnail a > img {
  display: block;
  max-width: 100%;
  height: auto;
  margin-right: auto;
  margin-left: auto;
}
.thumbnail .caption {
  padding: 9px;
  color: #333;
}
a.thumbnail:hover, a.thumbnail:focus, a.thumbnail.active {
  border-color: #1e91cf;
}
.alert {
  padding: 10px;
  margin-bottom: 18px;
  border: 1px solid transparent;
  border-radius: 3px;
}
.alert h4 {
  margin-top: 0;
  color: inherit;
}
.alert .alert-link {
  font-weight: bold;
}
.alert > p, .alert > ul {
  margin-bottom: 0;
}
.alert > p + p {
  margin-top: 5px;
}
.alert-dismissable, .alert-dismissible {
  padding-left: 30px;
}
.alert-dismissable .close, .alert-dismissible .close {
  position: relative;
  top: -2px;
  left: -21px;
  color: inherit;
}
.alert-success {
  background-color: #cbeacb;
  border-color: #b9e2b9;
  color: #398c39;
}
.alert-success hr {
  border-top-color: #a7dba7;
}
.alert-success .alert-link {
  color: #2a682a;
}
.alert-info {
  background-color: #d9edf7;
  border-color: #bce8f1;
  color: #214c61;
}
.alert-info hr {
  border-top-color: #a6e1ec;
}
.alert-info .alert-link {
  color: #142f3b;
}
.alert-warning {
  background-color: #fce7c8;
  border-color: #f9d5a2;
  color: #e0890e;
}
.alert-warning hr {
  border-top-color: #f8cb8a;
}
.alert-warning .alert-link {
  color: #b06c0b;
}
.alert-danger {
  background-color: #f5c1bb;
  border-color: #f3b5ad;
  color: #c72f1d;
}
.alert-danger hr {
  border-top-color: #f0a197;
}
.alert-danger .alert-link {
  color: #9a2516;
}
@-webkit-keyframes progress-bar-stripes {
  from {
    background-position: right 40px top 0;
  }
  to {
    background-position: 100% 0;
  }
}
@keyframes progress-bar-stripes {
  from {
    background-position: right 40px top 0;
  }
  to {
    background-position: 100% 0;
  }
}
.progress {
  overflow: hidden;
  height: 18px;
  margin-bottom: 18px;
  background-color: #f5f5f5;
  border-radius: 3px;
  -webkit-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}
.progress-bar {
  float: right;
  width: 0%;
  height: 100%;
  font-size: 12px;
  line-height: 18px;
  color: #fff;
  text-align: center;
  background-color: #1e91cf;
  -webkit-box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.15);
  box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.15);
  -webkit-transition: width 0.6s ease;
  -o-transition: width 0.6s ease;
  transition: width 0.6s ease;
}
.progress-striped .progress-bar, .progress-bar-striped {
  background-image: -webkit-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 40px 40px;
}
.progress.active .progress-bar, .progress-bar.active {
  -webkit-animation: progress-bar-stripes 2s linear infinite;
  -o-animation: progress-bar-stripes 2s linear infinite;
  animation: progress-bar-stripes 2s linear infinite;
}
.progress-bar-success {
  background-color: #4cb64c;
}
.progress-striped .progress-bar-success {
  background-image: -webkit-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
}
.progress-bar-info {
  background-color: #54b7d3;
}
.progress-striped .progress-bar-info {
  background-image: -webkit-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
}
.progress-bar-warning {
  background-color: #f3a638;
}
.progress-striped .progress-bar-warning {
  background-image: -webkit-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
}
.progress-bar-danger {
  background-color: #e3503e;
}
.progress-striped .progress-bar-danger {
  background-image: -webkit-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
}
.media {
  margin-top: 15px;
}
.media:first-child {
  margin-top: 0;
}
.media, .media-body {
  zoom: 1;
  overflow: hidden;
}
.media-body {
  width: 10000px;
}
.media-object {
  display: block;
}
.media-object.img-thumbnail {
  max-width: none;
}
.media-right, .media > .pull-right {
  padding-right: 10px;
}
.media-left, .media > .pull-left {
  padding-left: 10px;
}
.media-left, .media-right, .media-body {
  display: table-cell;
  vertical-align: top;
}
.media-middle {
  vertical-align: middle;
}
.media-bottom {
  vertical-align: bottom;
}
.media-heading {
  margin-top: 0;
  margin-bottom: 5px;
}
.media-list {
  padding-right: 0;
  list-style: none;
}
.list-group {
  margin-bottom: 20px;
  padding-right: 0;
}
.list-group-item {
  position: relative;
  display: block;
  padding: 10px 15px;
  margin-bottom: -1px;
  background-color: #fff;
  border: 1px solid #ddd;
}
.list-group-item:first-child {
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
}
.list-group-item:last-child {
  margin-bottom: 0;
  border-bottom-left-radius: 3px;
  border-bottom-right-radius: 3px;
}
a.list-group-item, button.list-group-item {
  color: #555;
}
a.list-group-item .list-group-item-heading, button.list-group-item .list-group-item-heading {
  color: #333;
}
a.list-group-item:hover, a.list-group-item:focus, button.list-group-item:hover, button.list-group-item:focus {
  text-decoration: none;
  color: #555;
  background-color: #f5f5f5;
}
button.list-group-item {
  width: 100%;
  text-align: right;
}
.list-group-item.disabled, .list-group-item.disabled:hover, .list-group-item.disabled:focus {
  background-color: #eee;
  color: #777;
  cursor: not-allowed;
}
.list-group-item.disabled .list-group-item-heading, .list-group-item.disabled:hover .list-group-item-heading, .list-group-item.disabled:focus .list-group-item-heading {
  color: inherit;
}
.list-group-item.disabled .list-group-item-text, .list-group-item.disabled:hover .list-group-item-text, .list-group-item.disabled:focus .list-group-item-text {
  color: #777;
}
.list-group-item.active, .list-group-item.active:hover, .list-group-item.active:focus {
  z-index: 2;
  color: #fff;
  background-color: #1e91cf;
  border-color: #1e91cf;
}
.list-group-item.active .list-group-item-heading, .list-group-item.active .list-group-item-heading > small, .list-group-item.active .list-group-item-heading > .small, .list-group-item.active:hover .list-group-item-heading, .list-group-item.active:hover .list-group-item-heading > small, .list-group-item.active:hover .list-group-item-heading > .small, .list-group-item.active:focus .list-group-item-heading, .list-group-item.active:focus .list-group-item-heading > small, .list-group-item.active:focus .list-group-item-heading > .small {
  color: inherit;
}
.list-group-item.active .list-group-item-text, .list-group-item.active:hover .list-group-item-text, .list-group-item.active:focus .list-group-item-text {
  color: #c3e4f6;
}
.list-group-item-success {
  color: #4cb64c;
  background-color: #cbeacb;
}
a.list-group-item-success, button.list-group-item-success {
  color: #4cb64c;
}
a.list-group-item-success .list-group-item-heading, button.list-group-item-success .list-group-item-heading {
  color: inherit;
}
a.list-group-item-success:hover, a.list-group-item-success:focus, button.list-group-item-success:hover, button.list-group-item-success:focus {
  color: #4cb64c;
  background-color: #b9e2b9;
}
a.list-group-item-success.active, a.list-group-item-success.active:hover, a.list-group-item-success.active:focus, button.list-group-item-success.active, button.list-group-item-success.active:hover, button.list-group-item-success.active:focus {
  color: #fff;
  background-color: #4cb64c;
  border-color: #4cb64c;
}
.list-group-item-info {
  color: #31708f;
  background-color: #d9edf7;
}
a.list-group-item-info, button.list-group-item-info {
  color: #31708f;
}
a.list-group-item-info .list-group-item-heading, button.list-group-item-info .list-group-item-heading {
  color: inherit;
}
a.list-group-item-info:hover, a.list-group-item-info:focus, button.list-group-item-info:hover, button.list-group-item-info:focus {
  color: #31708f;
  background-color: #c4e3f3;
}
a.list-group-item-info.active, a.list-group-item-info.active:hover, a.list-group-item-info.active:focus, button.list-group-item-info.active, button.list-group-item-info.active:hover, button.list-group-item-info.active:focus {
  color: #fff;
  background-color: #31708f;
  border-color: #31708f;
}
.list-group-item-warning {
  color: #f3a638;
  background-color: #fce7c8;
}
a.list-group-item-warning, button.list-group-item-warning {
  color: #f3a638;
}
a.list-group-item-warning .list-group-item-heading, button.list-group-item-warning .list-group-item-heading {
  color: inherit;
}
a.list-group-item-warning:hover, a.list-group-item-warning:focus, button.list-group-item-warning:hover, button.list-group-item-warning:focus {
  color: #f3a638;
  background-color: #fadcb0;
}
a.list-group-item-warning.active, a.list-group-item-warning.active:hover, a.list-group-item-warning.active:focus, button.list-group-item-warning.active, button.list-group-item-warning.active:hover, button.list-group-item-warning.active:focus {
  color: #fff;
  background-color: #f3a638;
  border-color: #f3a638;
}
.list-group-item-danger {
  color: #e3503e;
  background-color: #f5c1bb;
}
a.list-group-item-danger, button.list-group-item-danger {
  color: #e3503e;
}
a.list-group-item-danger .list-group-item-heading, button.list-group-item-danger .list-group-item-heading {
  color: inherit;
}
a.list-group-item-danger:hover, a.list-group-item-danger:focus, button.list-group-item-danger:hover, button.list-group-item-danger:focus {
  color: #e3503e;
  background-color: #f2ada4;
}
a.list-group-item-danger.active, a.list-group-item-danger.active:hover, a.list-group-item-danger.active:focus, button.list-group-item-danger.active, button.list-group-item-danger.active:hover, button.list-group-item-danger.active:focus {
  color: #fff;
  background-color: #e3503e;
  border-color: #e3503e;
}
.list-group-item-heading {
  margin-top: 0;
  margin-bottom: 5px;
}
.list-group-item-text {
  margin-bottom: 0;
  line-height: 1.3;
}
.panel {
  margin-bottom: 18px;
  background-color: #fff;
  border: 1px solid transparent;
  border-radius: 3px;
  -webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
}
.panel-body {
  padding: 15px;
}
.panel-body:before, .panel-body:after {
  content: " ";
  display: table;
}
.panel-body:after {
  clear: both;
}
.panel-heading {
  padding: 12px 15px;
  border-bottom: 1px solid transparent;
  border-top-left-radius: 2px;
  border-top-right-radius: 2px;
}
.panel-heading > .dropdown .dropdown-toggle {
  color: inherit;
}
.panel-title {
  margin-top: 0;
  margin-bottom: 0;
  font-size: 15px;
  color: inherit;
}
.panel-title > a, .panel-title > small, .panel-title > .small, .panel-title > small > a, .panel-title > .small > a {
  color: inherit;
}
.panel-footer {
  padding: 12px 15px;
  background-color: #f5f5f5;
  border-top: 1px solid #ddd;
  border-bottom-left-radius: 2px;
  border-bottom-right-radius: 2px;
}
.panel > .list-group, .panel > .panel-collapse > .list-group {
  margin-bottom: 0;
}
.panel > .list-group .list-group-item, .panel > .panel-collapse > .list-group .list-group-item {
  border-width: 1px 0;
  border-radius: 0;
}
.panel > .list-group:first-child .list-group-item:first-child, .panel > .panel-collapse > .list-group:first-child .list-group-item:first-child {
  border-top: 0;
  border-top-left-radius: 2px;
  border-top-right-radius: 2px;
}
.panel > .list-group:last-child .list-group-item:last-child, .panel > .panel-collapse > .list-group:last-child .list-group-item:last-child {
  border-bottom: 0;
  border-bottom-left-radius: 2px;
  border-bottom-right-radius: 2px;
}
.panel > .panel-heading + .panel-collapse > .list-group .list-group-item:first-child {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}
.panel-heading + .list-group .list-group-item:first-child {
  border-top-width: 0;
}
.list-group + .panel-footer {
  border-top-width: 0;
}
.panel > .table, .panel > .table-responsive > .table, .panel > .panel-collapse > .table {
  margin-bottom: 0;
}
.panel > .table caption, .panel > .table-responsive > .table caption, .panel > .panel-collapse > .table caption {
  padding-right: 15px;
  padding-left: 15px;
}
.panel > .table:first-child, .panel > .table-responsive:first-child > .table:first-child {
  border-top-left-radius: 2px;
  border-top-right-radius: 2px;
}
.panel > .table:first-child > thead:first-child > tr:first-child, .panel > .table:first-child > tbody:first-child > tr:first-child, .panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child, .panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child {
  border-top-right-radius: 2px;
  border-top-left-radius: 2px;
}
.panel > .table:first-child > thead:first-child > tr:first-child td:first-child, .panel > .table:first-child > thead:first-child > tr:first-child th:first-child, .panel > .table:first-child > tbody:first-child > tr:first-child td:first-child, .panel > .table:first-child > tbody:first-child > tr:first-child th:first-child, .panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child td:first-child, .panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child th:first-child, .panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child td:first-child, .panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child th:first-child {
  border-top-right-radius: 2px;
}
.panel > .table:first-child > thead:first-child > tr:first-child td:last-child, .panel > .table:first-child > thead:first-child > tr:first-child th:last-child, .panel > .table:first-child > tbody:first-child > tr:first-child td:last-child, .panel > .table:first-child > tbody:first-child > tr:first-child th:last-child, .panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child td:last-child, .panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child th:last-child, .panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child td:last-child, .panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child th:last-child {
  border-top-left-radius: 2px;
}
.panel > .table:last-child, .panel > .table-responsive:last-child > .table:last-child {
  border-bottom-left-radius: 2px;
  border-bottom-right-radius: 2px;
}
.panel > .table:last-child > tbody:last-child > tr:last-child, .panel > .table:last-child > tfoot:last-child > tr:last-child, .panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child, .panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child {
  border-bottom-right-radius: 2px;
  border-bottom-left-radius: 2px;
}
.panel > .table:last-child > tbody:last-child > tr:last-child td:first-child, .panel > .table:last-child > tbody:last-child > tr:last-child th:first-child, .panel > .table:last-child > tfoot:last-child > tr:last-child td:first-child, .panel > .table:last-child > tfoot:last-child > tr:last-child th:first-child, .panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child td:first-child, .panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child th:first-child, .panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child td:first-child, .panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child th:first-child {
  border-bottom-right-radius: 2px;
}
.panel > .table:last-child > tbody:last-child > tr:last-child td:last-child, .panel > .table:last-child > tbody:last-child > tr:last-child th:last-child, .panel > .table:last-child > tfoot:last-child > tr:last-child td:last-child, .panel > .table:last-child > tfoot:last-child > tr:last-child th:last-child, .panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child td:last-child, .panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child th:last-child, .panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child td:last-child, .panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child th:last-child {
  border-bottom-left-radius: 2px;
}
.panel > .panel-body + .table, .panel > .panel-body + .table-responsive, .panel > .table + .panel-body, .panel > .table-responsive + .panel-body {
  border-top: 1px solid #ddd;
}
.panel > .table > tbody:first-child > tr:first-child th, .panel > .table > tbody:first-child > tr:first-child td {
  border-top: 0;
}
.panel > .table-bordered, .panel > .table-responsive > .table-bordered {
  border: 0;
}
.panel > .table-bordered > thead > tr > th:first-child, .panel > .table-bordered > thead > tr > td:first-child, .panel > .table-bordered > tbody > tr > th:first-child, .panel > .table-bordered > tbody > tr > td:first-child, .panel > .table-bordered > tfoot > tr > th:first-child, .panel > .table-bordered > tfoot > tr > td:first-child, .panel > .table-responsive > .table-bordered > thead > tr > th:first-child, .panel > .table-responsive > .table-bordered > thead > tr > td:first-child, .panel > .table-responsive > .table-bordered > tbody > tr > th:first-child, .panel > .table-responsive > .table-bordered > tbody > tr > td:first-child, .panel > .table-responsive > .table-bordered > tfoot > tr > th:first-child, .panel > .table-responsive > .table-bordered > tfoot > tr > td:first-child {
  border-right: 0;
}
.panel > .table-bordered > thead > tr > th:last-child, .panel > .table-bordered > thead > tr > td:last-child, .panel > .table-bordered > tbody > tr > th:last-child, .panel > .table-bordered > tbody > tr > td:last-child, .panel > .table-bordered > tfoot > tr > th:last-child, .panel > .table-bordered > tfoot > tr > td:last-child, .panel > .table-responsive > .table-bordered > thead > tr > th:last-child, .panel > .table-responsive > .table-bordered > thead > tr > td:last-child, .panel > .table-responsive > .table-bordered > tbody > tr > th:last-child, .panel > .table-responsive > .table-bordered > tbody > tr > td:last-child, .panel > .table-responsive > .table-bordered > tfoot > tr > th:last-child, .panel > .table-responsive > .table-bordered > tfoot > tr > td:last-child {
  border-left: 0;
}
.panel > .table-bordered > thead > tr:first-child > td, .panel > .table-bordered > thead > tr:first-child > th, .panel > .table-bordered > tbody > tr:first-child > td, .panel > .table-bordered > tbody > tr:first-child > th, .panel > .table-responsive > .table-bordered > thead > tr:first-child > td, .panel > .table-responsive > .table-bordered > thead > tr:first-child > th, .panel > .table-responsive > .table-bordered > tbody > tr:first-child > td, .panel > .table-responsive > .table-bordered > tbody > tr:first-child > th {
  border-bottom: 0;
}
.panel > .table-bordered > tbody > tr:last-child > td, .panel > .table-bordered > tbody > tr:last-child > th, .panel > .table-bordered > tfoot > tr:last-child > td, .panel > .table-bordered > tfoot > tr:last-child > th, .panel > .table-responsive > .table-bordered > tbody > tr:last-child > td, .panel > .table-responsive > .table-bordered > tbody > tr:last-child > th, .panel > .table-responsive > .table-bordered > tfoot > tr:last-child > td, .panel > .table-responsive > .table-bordered > tfoot > tr:last-child > th {
  border-bottom: 0;
}
.panel > .table-responsive {
  border: 0;
  margin-bottom: 0;
}
.panel-group {
  margin-bottom: 18px;
}
.panel-group .panel {
  margin-bottom: 0;
  border-radius: 3px;
}
.panel-group .panel + .panel {
  margin-top: 5px;
}
.panel-group .panel-heading {
  border-bottom: 0;
}
.panel-group .panel-heading + .panel-collapse > .panel-body, .panel-group .panel-heading + .panel-collapse > .list-group {
  border-top: 1px solid #ddd;
}
.panel-group .panel-footer {
  border-top: 0;
}
.panel-group .panel-footer + .panel-collapse .panel-body {
  border-bottom: 1px solid #ddd;
}
.panel-default {
  border-color: #ddd;
}
.panel-default > .panel-heading {
  color: #333;
  background-color: #fafafa;
  border-color: #ddd;
}
.panel-default > .panel-heading + .panel-collapse > .panel-body {
  border-top-color: #ddd;
}
.panel-default > .panel-heading .badge {
  color: #fafafa;
  background-color: #333;
}
.panel-default > .panel-footer + .panel-collapse > .panel-body {
  border-bottom-color: #ddd;
}
.panel-primary {
  border-color: #1e91cf;
}
.panel-primary > .panel-heading {
  color: #fff;
  background-color: #1e91cf;
  border-color: #1e91cf;
}
.panel-primary > .panel-heading + .panel-collapse > .panel-body {
  border-top-color: #1e91cf;
}
.panel-primary > .panel-heading .badge {
  color: #1e91cf;
  background-color: #fff;
}
.panel-primary > .panel-footer + .panel-collapse > .panel-body {
  border-bottom-color: #1e91cf;
}
.panel-success {
  border-color: #b9e2b9;
}
.panel-success > .panel-heading {
  color: #4cb64c;
  background-color: #cbeacb;
  border-color: #b9e2b9;
}
.panel-success > .panel-heading + .panel-collapse > .panel-body {
  border-top-color: #b9e2b9;
}
.panel-success > .panel-heading .badge {
  color: #cbeacb;
  background-color: #4cb64c;
}
.panel-success > .panel-footer + .panel-collapse > .panel-body {
  border-bottom-color: #b9e2b9;
}
.panel-info {
  border-color: #bce8f1;
}
.panel-info > .panel-heading {
  color: #31708f;
  background-color: #d9edf7;
  border-color: #bce8f1;
}
.panel-info > .panel-heading + .panel-collapse > .panel-body {
  border-top-color: #bce8f1;
}
.panel-info > .panel-heading .badge {
  color: #d9edf7;
  background-color: #31708f;
}
.panel-info > .panel-footer + .panel-collapse > .panel-body {
  border-bottom-color: #bce8f1;
}
.panel-warning {
  border-color: #f9d5a2;
}
.panel-warning > .panel-heading {
  color: #f3a638;
  background-color: #fce7c8;
  border-color: #f9d5a2;
}
.panel-warning > .panel-heading + .panel-collapse > .panel-body {
  border-top-color: #f9d5a2;
}
.panel-warning > .panel-heading .badge {
  color: #fce7c8;
  background-color: #f3a638;
}
.panel-warning > .panel-footer + .panel-collapse > .panel-body {
  border-bottom-color: #f9d5a2;
}
.panel-danger {
  border-color: #f3b5ad;
}
.panel-danger > .panel-heading {
  color: #e3503e;
  background-color: #f5c1bb;
  border-color: #f3b5ad;
}
.panel-danger > .panel-heading + .panel-collapse > .panel-body {
  border-top-color: #f3b5ad;
}
.panel-danger > .panel-heading .badge {
  color: #f5c1bb;
  background-color: #e3503e;
}
.panel-danger > .panel-footer + .panel-collapse > .panel-body {
  border-bottom-color: #f3b5ad;
}
.embed-responsive {
  position: relative;
  display: block;
  height: 0;
  padding: 0;
  overflow: hidden;
}
.embed-responsive .embed-responsive-item, .embed-responsive iframe, .embed-responsive embed, .embed-responsive object, .embed-responsive video {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  height: 100%;
  width: 100%;
  border: 0;
}
.embed-responsive-16by9 {
  padding-bottom: 56.25%;
}
.embed-responsive-4by3 {
  padding-bottom: 75%;
}
.well {
  min-height: 20px;
  padding: 19px;
  margin-bottom: 20px;
  background-color: #f5f5f5;
  border: 1px solid #e3e3e3;
  border-radius: 3px;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);
}
.well blockquote {
  border-color: #ddd;
  border-color: rgba(0, 0, 0, 0.15);
}
.well-lg {
  padding: 24px;
  border-radius: 5px;
}
.well-sm {
  padding: 9px;
  border-radius: 2px;
}
.close {
  float: left;
  font-size: 19.5px;
  font-weight: bold;
  line-height: 1;
  color: #000;
  text-shadow: 0 1px 0 #fff;
  opacity: 0.2;
  filter: alpha(opacity=20);
}
.close:hover, .close:focus {
  color: #000;
  text-decoration: none;
  cursor: pointer;
  opacity: 0.5;
  filter: alpha(opacity=50);
}
button.close {
  padding: 0;
  cursor: pointer;
  background: transparent;
  border: 0;
  -webkit-appearance: none;
}
.modal-open {
  overflow: hidden;
}
.modal {
  display: none;
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  z-index: 1050;
  -webkit-overflow-scrolling: touch;
  outline: 0;
}
.modal.fade .modal-dialog {
  -webkit-transform: translate(0, -25%);
  -ms-transform: translate(0, -25%);
  -o-transform: translate(0, -25%);
  transform: translate(0, -25%);
  -webkit-transition: -webkit-transform 0.3s ease-out;
  -moz-transition: -moz-transform 0.3s ease-out;
  -o-transition: -o-transform 0.3s ease-out;
  transition: transform 0.3s ease-out;
}
.modal.in .modal-dialog {
  -webkit-transform: translate(0, 0);
  -ms-transform: translate(0, 0);
  -o-transform: translate(0, 0);
  transform: translate(0, 0);
}
.modal-open .modal {
  overflow-x: hidden;
  overflow-y: auto;
}
.modal-dialog {
  position: relative;
  width: auto;
  margin: 10px;
}
.modal-content {
  position: relative;
  background-color: #fff;
  border: 1px solid #999;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 5px;
  -webkit-box-shadow: 0 3px 9px rgba(0, 0, 0, 0.5);
  box-shadow: 0 3px 9px rgba(0, 0, 0, 0.5);
  background-clip: padding-box;
  outline: 0;
}
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  z-index: 1040;
  background-color: #000;
}
.modal-backdrop.fade {
  opacity: 0;
  filter: alpha(opacity=0);
}
.modal-backdrop.in {
  opacity: 0.5;
  filter: alpha(opacity=50);
}
.modal-header {
  padding: 15px;
  border-bottom: 1px solid #e5e5e5;
}
.modal-header:before, .modal-header:after {
  content: " ";
  display: table;
}
.modal-header:after {
  clear: both;
}
.modal-header .close {
  margin-top: -2px;
}
.modal-title {
  margin: 0;
  line-height: 1.428571429;
}
.modal-body {
  position: relative;
  padding: 15px;
}
.modal-footer {
  padding: 15px;
  text-align: left;
  border-top: 1px solid #e5e5e5;
}
.modal-footer:before, .modal-footer:after {
  content: " ";
  display: table;
}
.modal-footer:after {
  clear: both;
}
.modal-footer .btn + .btn {
  margin-right: 5px;
  margin-bottom: 0;
}
.modal-footer .btn-group .btn + .btn {
  margin-right: -1px;
}
.modal-footer .btn-block + .btn-block {
  margin-right: 0;
}
.modal-scrollbar-measure {
  position: absolute;
  top: -9999px;
  width: 50px;
  height: 50px;
  overflow: scroll;
}
@media (min-width: 768px) {
  .modal-dialog {
    width: 600px;
    margin: 30px auto;
  }
  .modal-content {
    -webkit-box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
  }
  .modal-sm {
    width: 300px;
  }
}
@media (min-width: 992px) {
  .modal-lg {
    width: 900px;
  }
}
.tooltip {
  position: absolute;
  z-index: 1070;
  display: block;
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-style: normal;
  font-weight: normal;
  letter-spacing: normal;
  line-break: auto;
  line-height: 1.428571429;
  text-align: right;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  white-space: normal;
  word-break: normal;
  word-spacing: normal;
  word-wrap: normal;
  font-size: 12px;
  opacity: 0;
  filter: alpha(opacity=0);
}
.tooltip.in {
  opacity: 0.9;
  filter: alpha(opacity=90);
}
.tooltip.top {
  margin-top: -3px;
  padding: 5px 0;
}
.tooltip.right {
  margin-right: 3px;
  padding: 0 5px;
}
.tooltip.bottom {
  margin-top: 3px;
  padding: 5px 0;
}
.tooltip.left {
  margin-right: -3px;
  padding: 0 5px;
}
.tooltip-inner {
  max-width: 200px;
  padding: 3px 8px;
  color: #fff;
  text-align: center;
  background-color: #000;
  border-radius: 3px;
}
.tooltip-arrow {
  position: absolute;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid;
}
.tooltip.top .tooltip-arrow {
  bottom: 0;
  right: 50%;
  margin-right: -5px;
  border-width: 5px 5px 0;
  border-top-color: #000;
}
.tooltip.top-left .tooltip-arrow {
  bottom: 0;
  left: 5px;
  margin-bottom: -5px;
  border-width: 5px 5px 0;
  border-top-color: #000;
}
.tooltip.top-right .tooltip-arrow {
  bottom: 0;
  right: 5px;
  margin-bottom: -5px;
  border-width: 5px 5px 0;
  border-top-color: #000;
}
.tooltip.right .tooltip-arrow {
  top: 50%;
  right: 0;
  margin-top: -5px;
  border-width: 5px 0 5px 5px;
  border-left-color: #000;
}
.tooltip.left .tooltip-arrow {
  top: 50%;
  left: 0;
  margin-top: -5px;
  border-width: 5px 5px 5px 0;
  border-right-color: #000;
}
.tooltip.bottom .tooltip-arrow {
  top: 0;
  right: 50%;
  margin-right: -5px;
  border-width: 0 5px 5px;
  border-bottom-color: #000;
}
.tooltip.bottom-left .tooltip-arrow {
  top: 0;
  left: 5px;
  margin-top: -5px;
  border-width: 0 5px 5px;
  border-bottom-color: #000;
}
.tooltip.bottom-right .tooltip-arrow {
  top: 0;
  right: 5px;
  margin-top: -5px;
  border-width: 0 5px 5px;
  border-bottom-color: #000;
}
.popover {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 1060;
  display: none;
  max-width: 276px;
  padding: 1px;
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-style: normal;
  font-weight: normal;
  letter-spacing: normal;
  line-break: auto;
  line-height: 1.428571429;
  text-align: right;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  white-space: normal;
  word-break: normal;
  word-spacing: normal;
  word-wrap: normal;
  font-size: 13px;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #ccc;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 5px;
  -webkit-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
}
.popover.top {
  margin-top: -10px;
}
.popover.right {
  margin-right: 10px;
}
.popover.bottom {
  margin-top: 10px;
}
.popover.left {
  margin-right: -10px;
}
.popover-title {
  margin: 0;
  padding: 8px 14px;
  font-size: 13px;
  background-color: #f7f7f7;
  border-bottom: 1px solid #ebebeb;
  border-radius: 4px 4px 0 0;
}
.popover-content {
  padding: 9px 14px;
}
.popover > .arrow, .popover > .arrow:after {
  position: absolute;
  display: block;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid;
}
.popover > .arrow {
  border-width: 11px;
}
.popover > .arrow:after {
  border-width: 10px;
  content: "";
}
.popover.top > .arrow {
  right: 50%;
  margin-right: -11px;
  border-bottom-width: 0;
  border-top-color: #999;
  border-top-color: rgba(0, 0, 0, 0.25);
  bottom: -11px;
}
.popover.top > .arrow:after {
  content: " ";
  bottom: 1px;
  margin-right: -10px;
  border-bottom-width: 0;
  border-top-color: #fff;
}
.popover.right > .arrow {
  top: 50%;
  right: -11px;
  margin-top: -11px;
  border-right-width: 0;
  border-left-color: #999;
  border-left-color: rgba(0, 0, 0, 0.25);
}
.popover.right > .arrow:after {
  content: " ";
  right: 1px;
  bottom: -10px;
  border-right-width: 0;
  border-left-color: #fff;
}
.popover.bottom > .arrow {
  right: 50%;
  margin-right: -11px;
  border-top-width: 0;
  border-bottom-color: #999;
  border-bottom-color: rgba(0, 0, 0, 0.25);
  top: -11px;
}
.popover.bottom > .arrow:after {
  content: " ";
  top: 1px;
  margin-right: -10px;
  border-top-width: 0;
  border-bottom-color: #fff;
}
.popover.left > .arrow {
  top: 50%;
  left: -11px;
  margin-top: -11px;
  border-left-width: 0;
  border-right-color: #999;
  border-right-color: rgba(0, 0, 0, 0.25);
}
.popover.left > .arrow:after {
  content: " ";
  left: 1px;
  border-left-width: 0;
  border-right-color: #fff;
  bottom: -10px;
}
.carousel {
  position: relative;
}
.carousel-inner {
  position: relative;
  overflow: hidden;
  width: 100%;
}
.carousel-inner > .item {
  display: none;
  position: relative;
  -webkit-transition: 0.6s ease-in-out right;
  -o-transition: 0.6s ease-in-out right;
  transition: 0.6s ease-in-out right;
}
.carousel-inner > .item > img, .carousel-inner > .item > a > img {
  display: block;
  max-width: 100%;
  height: auto;
  line-height: 1;
}
@media (transform-3d), (-webkit-transform-3d) {
  .carousel-inner > .item {
    -webkit-transition: -webkit-transform 0.6s ease-in-out;
    -moz-transition: -moz-transform 0.6s ease-in-out;
    -o-transition: -o-transform 0.6s ease-in-out;
    transition: transform 0.6s ease-in-out;
    -webkit-backface-visibility: hidden;
    -moz-backface-visibility: hidden;
    backface-visibility: hidden;
    -webkit-perspective: 1000px;
    -moz-perspective: 1000px;
    perspective: 1000px;
  }
  .carousel-inner > .item.next, .carousel-inner > .item.active.right {
    -webkit-transform: translate3d(-100%, 0, 0);
    transform: translate3d(-100%, 0, 0);
    right: 0;
  }
  .carousel-inner > .item.prev, .carousel-inner > .item.active.left {
    -webkit-transform: translate3d(100%, 0, 0);
    transform: translate3d(100%, 0, 0);
    right: 0;
  }
  .carousel-inner > .item.next.left, .carousel-inner > .item.prev.right, .carousel-inner > .item.active {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    right: 0;
  }
}
.carousel-inner > .active, .carousel-inner > .next, .carousel-inner > .prev {
  display: block;
}
.carousel-inner > .active {
  right: 0;
}
.carousel-inner > .next, .carousel-inner > .prev {
  position: absolute;
  top: 0;
  width: 100%;
}
.carousel-inner > .next {
  right: 100%;
}
.carousel-inner > .prev {
  right: -100%;
}
.carousel-inner > .next.left, .carousel-inner > .prev.right {
  right: 0;
}
.carousel-inner > .active.left {
  right: -100%;
}
.carousel-inner > .active.right {
  right: 100%;
}
.carousel-control {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 15%;
  opacity: 0.5;
  filter: alpha(opacity=50);
  font-size: 20px;
  color: #fff;
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6);
  background-color: rgba(0, 0, 0, 0);
}
.carousel-control.left {
  background-image: -webkit-linear-gradient(right, rgba(0, 0, 0, 0.5) 0%, rgba(0, 0, 0, 0.0001) 100%);
  background-image: -o-linear-gradient(right, rgba(0, 0, 0, 0.5) 0%, rgba(0, 0, 0, 0.0001) 100%);
  background-image: linear-gradient(to left, rgba(0, 0, 0, 0.5) 0%, rgba(0, 0, 0, 0.0001) 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr="#80000000", endColorstr="#00000000", GradientType=1);
}
.carousel-control.right {
  right: auto;
  left: 0;
  background-image: -webkit-linear-gradient(right, rgba(0, 0, 0, 0.0001) 0%, rgba(0, 0, 0, 0.5) 100%);
  background-image: -o-linear-gradient(right, rgba(0, 0, 0, 0.0001) 0%, rgba(0, 0, 0, 0.5) 100%);
  background-image: linear-gradient(to left, rgba(0, 0, 0, 0.0001) 0%, rgba(0, 0, 0, 0.5) 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr="#00000000", endColorstr="#80000000", GradientType=1);
}
.carousel-control:hover, .carousel-control:focus {
  outline: 0;
  color: #fff;
  text-decoration: none;
  opacity: 0.9;
  filter: alpha(opacity=90);
}
.carousel-control .icon-prev, .carousel-control .icon-next, .carousel-control .glyphicon-chevron-left, .carousel-control .glyphicon-chevron-right {
  position: absolute;
  top: 50%;
  margin-top: -10px;
  z-index: 5;
  display: inline-block;
}
.carousel-control .icon-prev, .carousel-control .glyphicon-chevron-left {
  right: 50%;
  margin-right: -10px;
}
.carousel-control .icon-next, .carousel-control .glyphicon-chevron-right {
  left: 50%;
  margin-left: -10px;
}
.carousel-control .icon-prev, .carousel-control .icon-next {
  width: 20px;
  height: 20px;
  line-height: 1;
  font-family: serif;
}
.carousel-control .icon-prev:before {
  content: "‹";
}
.carousel-control .icon-next:before {
  content: "›";
}
.carousel-indicators {
  position: absolute;
  bottom: 10px;
  right: 50%;
  z-index: 15;
  width: 60%;
  margin-right: -30%;
  padding-right: 0;
  list-style: none;
  text-align: center;
}
.carousel-indicators li {
  display: inline-block;
  width: 10px;
  height: 10px;
  margin: 1px;
  text-indent: -999px;
  border: 1px solid #fff;
  border-radius: 10px;
  cursor: pointer;
  background-color: #000 \9 ;
  background-color: rgba(0, 0, 0, 0);
}
.carousel-indicators .active {
  margin: 0;
  width: 12px;
  height: 12px;
  background-color: #fff;
}
.carousel-caption {
  position: absolute;
  right: 15%;
  left: 15%;
  bottom: 20px;
  z-index: 10;
  padding-top: 20px;
  padding-bottom: 20px;
  color: #fff;
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6);
}
.carousel-caption .btn {
  text-shadow: none;
}
@media screen and (min-width: 768px) {
  .carousel-control .glyphicon-chevron-left, .carousel-control .glyphicon-chevron-right, .carousel-control .icon-prev, .carousel-control .icon-next {
    width: 30px;
    height: 30px;
    margin-top: -10px;
    font-size: 30px;
  }
  .carousel-control .glyphicon-chevron-left, .carousel-control .icon-prev {
    margin-right: -10px;
  }
  .carousel-control .glyphicon-chevron-right, .carousel-control .icon-next {
    margin-left: -10px;
  }
  .carousel-caption {
    right: 20%;
    left: 20%;
    padding-bottom: 30px;
  }
  .carousel-indicators {
    bottom: 20px;
  }
}
.clearfix:before, .clearfix:after {
  content: " ";
  display: table;
}
.clearfix:after {
  clear: both;
}
.center-block {
  display: block;
  margin-right: auto;
  margin-left: auto;
}
.pull-right {
  float: left !important;
}
.pull-left {
  float: right !important;
}
.hide {
  display: none !important;
}
.show {
  display: block !important;
}
.invisible {
  visibility: hidden;
}
.text-hide {
  font: 0/0 a;
  color: transparent;
  text-shadow: none;
  background-color: transparent;
  border: 0;
}
.hidden {
  display: none !important;
}
.affix {
  position: fixed;
}
@-ms-viewport {
  .carousel-control .glyphicon-chevron-right, .carousel-control .icon-next {
    width: device-width;
  }
}
.visible-xs {
  display: none !important;
}
.visible-sm {
  display: none !important;
}
.visible-md {
  display: none !important;
}
.visible-lg {
  display: none !important;
}
.visible-xs-block, .visible-xs-inline, .visible-xs-inline-block, .visible-sm-block, .visible-sm-inline, .visible-sm-inline-block, .visible-md-block, .visible-md-inline, .visible-md-inline-block, .visible-lg-block, .visible-lg-inline, .visible-lg-inline-block {
  display: none !important;
}
@media (max-width: 767px) {
  .visible-xs {
    display: block !important;
  }
  table.visible-xs {
    display: table !important;
  }
  tr.visible-xs {
    display: table-row !important;
  }
  th.visible-xs, td.visible-xs {
    display: table-cell !important;
  }
}
@media (max-width: 767px) {
  .visible-xs-block {
    display: block !important;
  }
}
@media (max-width: 767px) {
  .visible-xs-inline {
    display: inline !important;
  }
}
@media (max-width: 767px) {
  .visible-xs-inline-block {
    display: inline-block !important;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .visible-sm {
    display: block !important;
  }
  table.visible-sm {
    display: table !important;
  }
  tr.visible-sm {
    display: table-row !important;
  }
  th.visible-sm, td.visible-sm {
    display: table-cell !important;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .visible-sm-block {
    display: block !important;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .visible-sm-inline {
    display: inline !important;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .visible-sm-inline-block {
    display: inline-block !important;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .visible-md {
    display: block !important;
  }
  table.visible-md {
    display: table !important;
  }
  tr.visible-md {
    display: table-row !important;
  }
  th.visible-md, td.visible-md {
    display: table-cell !important;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .visible-md-block {
    display: block !important;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .visible-md-inline {
    display: inline !important;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .visible-md-inline-block {
    display: inline-block !important;
  }
}
@media (min-width: 1200px) {
  .visible-lg {
    display: block !important;
  }
  table.visible-lg {
    display: table !important;
  }
  tr.visible-lg {
    display: table-row !important;
  }
  th.visible-lg, td.visible-lg {
    display: table-cell !important;
  }
}
@media (min-width: 1200px) {
  .visible-lg-block {
    display: block !important;
  }
}
@media (min-width: 1200px) {
  .visible-lg-inline {
    display: inline !important;
  }
}
@media (min-width: 1200px) {
  .visible-lg-inline-block {
    display: inline-block !important;
  }
}
@media (max-width: 767px) {
  .hidden-xs {
    display: none !important;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .hidden-sm {
    display: none !important;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .hidden-md {
    display: none !important;
  }
}
@media (min-width: 1200px) {
  .hidden-lg {
    display: none !important;
  }
}
.visible-print {
  display: none !important;
}
@media print {
  .visible-print {
    display: block !important;
  }
  table.visible-print {
    display: table !important;
  }
  tr.visible-print {
    display: table-row !important;
  }
  th.visible-print, td.visible-print {
    display: table-cell !important;
  }
}
.visible-print-block {
  display: none !important;
}
@media print {
  .visible-print-block {
    display: block !important;
  }
}
.visible-print-inline {
  display: none !important;
}
@media print {
  .visible-print-inline {
    display: inline !important;
  }
}
.visible-print-inline-block {
  display: none !important;
}
@media print {
  .visible-print-inline-block {
    display: inline-block !important;
  }
}
@media print {
  .hidden-print {
    display: none !important;
  }
}
