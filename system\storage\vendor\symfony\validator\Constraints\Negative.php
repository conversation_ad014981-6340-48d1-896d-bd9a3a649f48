<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Symfony\Component\Validator\Constraints;

/**
 * @Annotation
 * @Target({"PROPERTY", "METHOD", "ANNOTATION"})
 *
 * <AUTHOR> <jan.scha<PERSON><PERSON>@sensiolabs.de>
 */
class Negative extends LessThan
{
    use NumberConstraintTrait;

    public $message = 'This value should be negative.';

    public function __construct($options = null)
    {
        parent::__construct($this->configureNumberConstraintOptions($options));
    }

    public function validatedBy(): string
    {
        return LessThanValidator::class;
    }
}
