{{ header }}{{ column_left }}

<!-- CSS مخصص لنموذج دليل الحسابات - Enterprise Grade Enhanced -->
<style>
/* تحسينات متقدمة للنموذج */
.form-container {
    background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid #e3e6f0;
    border-radius: 12px;
    padding: 30px;
    margin-bottom: 25px;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    transition: all 0.3s ease;
}

.form-container:hover {
    box-shadow: 0 0.25rem 2rem 0 rgba(58, 59, 69, 0.2);
    transform: translateY(-2px);
}

.form-group {
    margin-bottom: 20px;
    position: relative;
}

.form-label {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
    display: block;
    font-size: 14px;
}

.form-control {
    border: 2px solid #e3e6f0;
    border-radius: 8px;
    padding: 12px 15px;
    font-size: 14px;
    transition: all 0.3s ease;
    width: 100%;
    background-color: #ffffff;
}

.form-control:focus {
    border-color: #4e73df;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
    outline: none;
    transform: translateY(-1px);
}

.form-control:hover {
    border-color: #5a6c7d;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.btn-primary {
    background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
    border: none;
    border-radius: 8px;
    padding: 12px 25px;
    font-weight: 600;
    color: white;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #224abe 0%, #1e3a8a 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(78, 115, 223, 0.4);
}

.btn-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
    border: none;
    border-radius: 8px;
    padding: 12px 25px;
    font-weight: 600;
    color: white;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #5a6268 0%, #495057 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.4);
}

.alert {
    border-radius: 8px;
    padding: 15px 20px;
    margin-bottom: 20px;
    border: none;
    font-weight: 500;
}

.alert-danger {
    background: linear-gradient(135deg, #e74a3b 0%, #c0392b 100%);
    color: white;
}

.alert-success {
    background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
    color: white;
}

.required {
    color: #e74a3b;
    font-weight: bold;
}

/* تحسينات RTL */
[dir="rtl"] .form-control {
    text-align: right;
}

[dir="rtl"] .btn {
    margin-left: 10px;
    margin-right: 0;
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    .form-container {
        padding: 20px;
        margin: 10px;
    }
    
    .btn {
        width: 100%;
        margin-bottom: 10px;
    }
}

/* تأثيرات التحميل */
.loading {
    opacity: 0.7;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #4e73df;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}
</style>

<div id="content">
    <div class="page-header">
        <div class="container-fluid">
            <div class="float-end">
                <button type="submit" form="form-account" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary">
                    <i class="fas fa-save"></i> {{ button_save }}
                </button>
                <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-secondary">
                    <i class="fas fa-reply"></i> {{ button_cancel }}
                </a>
            </div>
            <h1>{{ heading_title }}</h1>
            <ol class="breadcrumb">
                {% for breadcrumb in breadcrumbs %}
                    <li class="breadcrumb-item">
                        {% if breadcrumb.href %}
                            <a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a>
                        {% else %}
                            {{ breadcrumb.text }}
                        {% endif %}
                    </li>
                {% endfor %}
            </ol>
        </div>
    </div>

    <div class="container-fluid">
        {% if error_warning %}
            <div class="alert alert-danger alert-dismissible">
                <i class="fas fa-exclamation-circle"></i> {{ error_warning }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        {% endif %}

        <div class="card">
            <div class="card-header">
                <i class="fas fa-plus-circle"></i> {{ text_form }}
            </div>
            <div class="card-body">
                <form id="form-account" action="{{ action }}" method="post" data-oc-toggle="ajax">
                    <div class="form-container">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="input-account-code" class="form-label">
                                        {{ entry_account_code }} <span class="required">*</span>
                                    </label>
                                    <input type="text" name="account_code" value="{{ account_code }}" 
                                           placeholder="{{ entry_account_code }}" id="input-account-code" 
                                           class="form-control{% if error_account_code %} is-invalid{% endif %}" required />
                                    {% if error_account_code %}
                                        <div class="invalid-feedback">{{ error_account_code }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="input-account-type" class="form-label">
                                        {{ entry_account_type }} <span class="required">*</span>
                                    </label>
                                    <select name="account_type" id="input-account-type" 
                                            class="form-control{% if error_account_type %} is-invalid{% endif %}" required>
                                        <option value="">{{ text_select }}</option>
                                        <option value="asset"{% if account_type == 'asset' %} selected{% endif %}>{{ text_asset }}</option>
                                        <option value="liability"{% if account_type == 'liability' %} selected{% endif %}>{{ text_liability }}</option>
                                        <option value="equity"{% if account_type == 'equity' %} selected{% endif %}>{{ text_equity }}</option>
                                        <option value="revenue"{% if account_type == 'revenue' %} selected{% endif %}>{{ text_revenue }}</option>
                                        <option value="expense"{% if account_type == 'expense' %} selected{% endif %}>{{ text_expense }}</option>
                                    </select>
                                    {% if error_account_type %}
                                        <div class="invalid-feedback">{{ error_account_type }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="input-account-name" class="form-label">
                                        {{ entry_account_name }} <span class="required">*</span>
                                    </label>
                                    <input type="text" name="account_description[{{ language_id }}][name]" 
                                           value="{{ account_description[language_id] ? account_description[language_id].name : '' }}" 
                                           placeholder="{{ entry_account_name }}" id="input-account-name" 
                                           class="form-control{% if error_name %} is-invalid{% endif %}" required />
                                    {% if error_name %}
                                        <div class="invalid-feedback">{{ error_name }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="input-parent-account" class="form-label">{{ entry_parent_account }}</label>
                                    <select name="parent_id" id="input-parent-account" class="form-control">
                                        <option value="0">{{ text_none }}</option>
                                        {% for parent_account in parent_accounts %}
                                            <option value="{{ parent_account.account_id }}"{% if parent_id == parent_account.account_id %} selected{% endif %}>
                                                {{ parent_account.account_code }} - {{ parent_account.name }}
                                            </option>
                                        {% endfor %}
                                    </select>
                                    {% if error_parent %}
                                        <div class="invalid-feedback">{{ error_parent }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="input-status" class="form-label">{{ entry_status }}</label>
                                    <select name="status" id="input-status" class="form-control">
                                        <option value="1"{% if status %} selected{% endif %}>{{ text_enabled }}</option>
                                        <option value="0"{% if not status %} selected{% endif %}>{{ text_disabled }}</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label for="input-opening-balance" class="form-label">{{ entry_opening_balance }}</label>
                                    <input type="number" step="0.01" name="opening_balance" value="{{ opening_balance }}" 
                                           placeholder="{{ entry_opening_balance }}" id="input-opening-balance" 
                                           class="form-control" />
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // تحسين تجربة المستخدم
    $('.form-control').on('focus', function() {
        $(this).closest('.form-group').addClass('focused');
    });
    
    $('.form-control').on('blur', function() {
        $(this).closest('.form-group').removeClass('focused');
    });
    
    // التحقق من صحة النموذج
    $('#form-account').on('submit', function(e) {
        var isValid = true;
        
        // التحقق من الحقول المطلوبة
        $('.form-control[required]').each(function() {
            if (!$(this).val()) {
                $(this).addClass('is-invalid');
                isValid = false;
            } else {
                $(this).removeClass('is-invalid');
            }
        });
        
        if (!isValid) {
            e.preventDefault();
            alert('{{ error_required_fields }}');
        }
    });
    
    // تأثيرات التحميل
    $('#form-account').on('submit', function() {
        $(this).addClass('loading');
        $('.btn').prop('disabled', true);
    });
});
</script>

{{ footer }}
