<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/service.proto

namespace GPBMetadata\Google\Api;

class Service
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        \GPBMetadata\Google\Api\Annotations::initOnce();
        \GPBMetadata\Google\Api\Auth::initOnce();
        \GPBMetadata\Google\Api\Backend::initOnce();
        \GPBMetadata\Google\Api\Billing::initOnce();
        \GPBMetadata\Google\Api\Context::initOnce();
        \GPBMetadata\Google\Api\Control::initOnce();
        \GPBMetadata\Google\Api\Documentation::initOnce();
        \GPBMetadata\Google\Api\Endpoint::initOnce();
        \GPBMetadata\Google\Api\Http::initOnce();
        \GPBMetadata\Google\Api\Log::initOnce();
        \GPBMetadata\Google\Api\Logging::initOnce();
        \GPBMetadata\Google\Api\Metric::initOnce();
        \GPBMetadata\Google\Api\MonitoredResource::initOnce();
        \GPBMetadata\Google\Api\Monitoring::initOnce();
        \GPBMetadata\Google\Api\Quota::initOnce();
        \GPBMetadata\Google\Api\SourceInfo::initOnce();
        \GPBMetadata\Google\Api\SystemParameter::initOnce();
        \GPBMetadata\Google\Api\Usage::initOnce();
        \GPBMetadata\Google\Protobuf\Api::initOnce();
        \GPBMetadata\Google\Protobuf\Type::initOnce();
        \GPBMetadata\Google\Protobuf\Wrappers::initOnce();
        $pool->internalAddGeneratedFile(hex2bin(
            "0aa10d0a18676f6f676c652f6170692f736572766963652e70726f746f12" .
            "0a676f6f676c652e6170691a15676f6f676c652f6170692f617574682e70" .
            "726f746f1a18676f6f676c652f6170692f6261636b656e642e70726f746f" .
            "1a18676f6f676c652f6170692f62696c6c696e672e70726f746f1a18676f" .
            "6f676c652f6170692f636f6e746578742e70726f746f1a18676f6f676c65" .
            "2f6170692f636f6e74726f6c2e70726f746f1a1e676f6f676c652f617069" .
            "2f646f63756d656e746174696f6e2e70726f746f1a19676f6f676c652f61" .
            "70692f656e64706f696e742e70726f746f1a15676f6f676c652f6170692f" .
            "687474702e70726f746f1a14676f6f676c652f6170692f6c6f672e70726f" .
            "746f1a18676f6f676c652f6170692f6c6f6767696e672e70726f746f1a17" .
            "676f6f676c652f6170692f6d65747269632e70726f746f1a23676f6f676c" .
            "652f6170692f6d6f6e69746f7265645f7265736f757263652e70726f746f" .
            "1a1b676f6f676c652f6170692f6d6f6e69746f72696e672e70726f746f1a" .
            "16676f6f676c652f6170692f71756f74612e70726f746f1a1c676f6f676c" .
            "652f6170692f736f757263655f696e666f2e70726f746f1a21676f6f676c" .
            "652f6170692f73797374656d5f706172616d657465722e70726f746f1a16" .
            "676f6f676c652f6170692f75736167652e70726f746f1a19676f6f676c65" .
            "2f70726f746f6275662f6170692e70726f746f1a1a676f6f676c652f7072" .
            "6f746f6275662f747970652e70726f746f1a1e676f6f676c652f70726f74" .
            "6f6275662f77726170706572732e70726f746f22dc070a07536572766963" .
            "6512340a0e636f6e6669675f76657273696f6e18142001280b321c2e676f" .
            "6f676c652e70726f746f6275662e55496e74333256616c7565120c0a046e" .
            "616d65180120012809120a0a026964182120012809120d0a057469746c65" .
            "180220012809121b0a1370726f64756365725f70726f6a6563745f696418" .
            "162001280912220a046170697318032003280b32142e676f6f676c652e70" .
            "726f746f6275662e41706912240a05747970657318042003280b32152e67" .
            "6f6f676c652e70726f746f6275662e5479706512240a05656e756d731805" .
            "2003280b32152e676f6f676c652e70726f746f6275662e456e756d12300a" .
            "0d646f63756d656e746174696f6e18062001280b32192e676f6f676c652e" .
            "6170692e446f63756d656e746174696f6e12240a076261636b656e641808" .
            "2001280b32132e676f6f676c652e6170692e4261636b656e64121e0a0468" .
            "74747018092001280b32102e676f6f676c652e6170692e4874747012200a" .
            "0571756f7461180a2001280b32112e676f6f676c652e6170692e51756f74" .
            "6112320a0e61757468656e7469636174696f6e180b2001280b321a2e676f" .
            "6f676c652e6170692e41757468656e7469636174696f6e12240a07636f6e" .
            "74657874180c2001280b32132e676f6f676c652e6170692e436f6e746578" .
            "7412200a057573616765180f2001280b32112e676f6f676c652e6170692e" .
            "557361676512270a09656e64706f696e747318122003280b32142e676f6f" .
            "676c652e6170692e456e64706f696e7412240a07636f6e74726f6c181520" .
            "01280b32132e676f6f676c652e6170692e436f6e74726f6c12270a046c6f" .
            "677318172003280b32192e676f6f676c652e6170692e4c6f674465736372" .
            "6970746f72122d0a076d65747269637318182003280b321c2e676f6f676c" .
            "652e6170692e4d657472696344657363726970746f7212440a136d6f6e69" .
            "746f7265645f7265736f757263657318192003280b32272e676f6f676c65" .
            "2e6170692e4d6f6e69746f7265645265736f757263654465736372697074" .
            "6f7212240a0762696c6c696e67181a2001280b32132e676f6f676c652e61" .
            "70692e42696c6c696e6712240a076c6f6767696e67181b2001280b32132e" .
            "676f6f676c652e6170692e4c6f6767696e67122a0a0a6d6f6e69746f7269" .
            "6e67181c2001280b32162e676f6f676c652e6170692e4d6f6e69746f7269" .
            "6e6712370a1173797374656d5f706172616d6574657273181d2001280b32" .
            "1c2e676f6f676c652e6170692e53797374656d506172616d657465727312" .
            "2b0a0b736f757263655f696e666f18252001280b32162e676f6f676c652e" .
            "6170692e536f75726365496e666f4a0408651066426e0a0e636f6d2e676f" .
            "6f676c652e617069420c5365727669636550726f746f50015a45676f6f67" .
            "6c652e676f6c616e672e6f72672f67656e70726f746f2f676f6f676c6561" .
            "7069732f6170692f73657276696365636f6e6669673b7365727669636563" .
            "6f6e666967a2020447415049620670726f746f33"
        ), true);

        static::$is_initialized = true;
    }
}

