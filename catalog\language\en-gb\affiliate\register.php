<?php
// Heading
$_['heading_title']             = 'Affiliate Program';

// Text
$_['text_account']              = 'Account';
$_['text_register']             = 'Affiliate Register';
$_['text_account_already']      = 'If you already have an account with us, please login at the <a href="%s">login page</a>.';
$_['text_signup']               = 'To create an affiliate account, fill in the form below ensuring you complete all the required fields:';
$_['text_your_details']         = 'Your Personal Details';
$_['text_your_address']         = 'Your Address Details';
$_['text_your_affiliate']       = 'Your Affiliate Information';
$_['text_your_password']        = 'Your Password';
$_['text_cheque']               = 'Cheque';
$_['text_paypal']               = 'PayPal';
$_['text_bank']                 = 'Bank Transfer';
$_['text_agree']                = 'I have read and agree to the <a href="%s" class="agree"><b>%s</b></a>';

// Entry
$_['entry_customer_group']      = 'Customer Group';
$_['entry_firstname']           = 'First Name';
$_['entry_lastname']            = 'Last Name';
$_['entry_email']               = 'E-Mail';
$_['entry_telephone']           = 'Telephone';
$_['entry_company']             = 'Company';
$_['entry_website']             = 'Web Site';
$_['entry_tax']                 = 'Tax ID';
$_['entry_payment']             = 'Payment Method';
$_['entry_cheque']              = 'Cheque Payee Name';
$_['entry_paypal']              = 'PayPal Email Account';
$_['entry_bank_name']           = 'Bank Name';
$_['entry_bank_branch_number']  = 'ABA/BSB number (Branch Number)';
$_['entry_bank_swift_code']     = 'SWIFT Code';
$_['entry_bank_account_name']   = 'Account Name';
$_['entry_bank_account_number'] = 'Account Number';
$_['entry_password']            = 'Password';
$_['entry_confirm']             = 'Password Confirm';

// Error
$_['error_exists']              = 'Warning: E-Mail Address is already registered!';
$_['error_firstname']           = 'First Name must be between 1 and 32 characters!';
$_['error_lastname']            = 'Last Name must be between 1 and 32 characters!';
$_['error_email']               = 'E-Mail Address does not appear to be valid!';
$_['error_telephone']           = 'Telephone must be between 3 and 32 characters!';
$_['error_custom_field']        = '%s required!';
$_['error_cheque']              = 'Cheque Payee Name required!';
$_['error_paypal']              = 'PayPal Email Address does not appear to be valid!';
$_['error_bank_account_name']   = 'Account Name required!';
$_['error_bank_account_number'] = 'Account Number required!';
$_['error_password']            = 'Password must be between 4 and 20 characters!';
$_['error_confirm']             = 'Password confirmation does not match password!';
$_['error_agree']               = 'Warning: You must agree to the %s!';