{{ header }}{{ column_left }}

<!-- Enterprise Grade Plus CSS for Purchase Analysis -->
<style>
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --danger-color: #e74c3c;
    --warning-color: #f39c12;
    --info-color: #17a2b8;
    --purchase-color: #6f42c1;
    --light-bg: #f8f9fa;
    --border-color: #dee2e6;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

.purchase-analysis-container {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: var(--shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.purchase-analysis-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--purchase-color), var(--primary-color), var(--secondary-color));
}

.purchase-analysis-header {
    text-align: center;
    border-bottom: 3px solid var(--purchase-color);
    padding-bottom: 20px;
    margin-bottom: 30px;
    position: relative;
}

.purchase-analysis-header h2 {
    color: var(--purchase-color);
    font-weight: 700;
    font-size: 1.8rem;
    margin: 0;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.purchase-kpis {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.purchase-kpi {
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    text-align: center;
    box-shadow: var(--shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.purchase-kpi:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0,0,0,0.1);
}

.purchase-kpi::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
}

.purchase-kpi.total-purchases::before { background: var(--purchase-color); }
.purchase-kpi.total-orders::before { background: var(--info-color); }
.purchase-kpi.average-order::before { background: var(--success-color); }
.purchase-kpi.top-supplier::before { background: var(--warning-color); }
.purchase-kpi.cost-savings::before { background: var(--secondary-color); }

.purchase-kpi h4 {
    color: var(--primary-color);
    margin-bottom: 10px;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.purchase-kpi .amount {
    font-size: 1.4rem;
    font-weight: 700;
    font-family: 'Courier New', monospace;
    margin-bottom: 5px;
}

.purchase-kpi .description {
    font-size: 0.8rem;
    color: #6c757d;
    font-weight: 500;
}

.kpi-total-purchases .amount { color: var(--purchase-color); }
.kpi-total-orders .amount { color: var(--info-color); }
.kpi-average-order .amount { color: var(--success-color); }
.kpi-top-supplier .amount { color: var(--warning-color); }
.kpi-cost-savings .amount { color: var(--secondary-color); }

.purchase-charts {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.purchase-chart {
    background: #ffffff;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: var(--shadow);
}

.purchase-chart h4 {
    color: var(--primary-color);
    margin-bottom: 15px;
    font-weight: 600;
    text-align: center;
}

.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
}

.purchase-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    background: #ffffff;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.purchase-table th {
    background: linear-gradient(135deg, var(--purchase-color), #5a2d91);
    color: white;
    padding: 15px 12px;
    text-align: center;
    font-weight: 600;
    font-size: 0.9rem;
    border-bottom: 2px solid var(--border-color);
}

.purchase-table td {
    padding: 12px;
    border-bottom: 1px solid var(--border-color);
    text-align: center;
    transition: var(--transition);
}

.purchase-table tbody tr:hover {
    background: var(--light-bg);
    transform: scale(1.005);
}

.purchase-table tbody tr:nth-child(even) {
    background: #fafbfc;
}

.amount-cell {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    text-align: {{ direction == 'rtl' ? 'right' : 'left' }};
}

.amount-positive { 
    color: var(--success-color); 
    font-weight: 600;
}

.amount-negative { 
    color: var(--danger-color); 
    font-weight: 600;
}

.amount-neutral { 
    color: var(--secondary-color); 
    font-weight: 600;
}

/* Filter Panel */
.filter-panel {
    background: linear-gradient(135deg, #f8f9fa, #ffffff);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: var(--shadow);
}

.filter-panel h4 {
    color: var(--purchase-color);
    margin-bottom: 15px;
    font-weight: 600;
}

.form-group {
    margin-bottom: 15px;
}

.form-label {
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 5px;
    display: block;
}

.form-control {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 8px 12px;
    transition: var(--transition);
    width: 100%;
}

.form-control:focus {
    border-color: var(--purchase-color);
    box-shadow: 0 0 0 0.2rem rgba(111, 66, 193, 0.25);
    outline: none;
}

/* Trend Indicators */
.trend-indicator {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    font-size: 0.8rem;
    font-weight: 600;
}

.trend-up {
    color: var(--success-color);
}

.trend-down {
    color: var(--danger-color);
}

.trend-neutral {
    color: #6c757d;
}

/* RTL Support */
[dir="rtl"] .purchase-table {
    direction: rtl;
}

[dir="rtl"] .amount-cell {
    text-align: right;
}

/* Print Styles */
@media print {
    .purchase-analysis-container {
        box-shadow: none;
        border: 1px solid #000;
    }
    
    .purchase-table th {
        background: #f0f0f0 !important;
        color: #000 !important;
    }
    
    .btn, .panel-heading, .filter-panel {
        display: none !important;
    }
    
    .purchase-charts {
        page-break-inside: avoid;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .purchase-table {
        font-size: 0.8rem;
    }
    
    .purchase-table th,
    .purchase-table td {
        padding: 8px 6px;
    }
    
    .purchase-kpis {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .purchase-charts {
        grid-template-columns: 1fr;
    }
}
</style>

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-{{ direction == 'rtl' ? 'start' : 'end' }}">
        <div class="btn-group" role="group" aria-label="{{ text_actions }}">
          <button type="button" class="btn btn-success btn-lg" onclick="generateReport()"
                  data-toggle="tooltip" title="{{ button_generate_report }}">
            <i class="fa fa-bar-chart"></i> {{ button_generate }}
          </button>
          <div class="btn-group" role="group">
            <button type="button" class="btn btn-info dropdown-toggle" data-toggle="dropdown"
                    aria-expanded="false" data-toggle="tooltip" title="{{ text_export_options }}">
              <i class="fa fa-download"></i> {{ text_export }}
            </button>
            <ul class="dropdown-menu">
              <li><a class="dropdown-item" href="#" onclick="exportAnalysis('excel')">
                <i class="fa fa-file-excel text-success"></i> Excel
              </a></li>
              <li><a class="dropdown-item" href="#" onclick="exportAnalysis('pdf')">
                <i class="fa fa-file-pdf text-danger"></i> PDF
              </a></li>
              <li><a class="dropdown-item" href="#" onclick="exportAnalysis('csv')">
                <i class="fa fa-file-csv text-info"></i> CSV
              </a></li>
              <li><hr class="dropdown-divider"></li>
              <li><a class="dropdown-item" href="#" onclick="printAnalysis()">
                <i class="fa fa-print text-muted"></i> {{ text_print }}
              </a></li>
            </ul>
          </div>
          <button type="button" class="btn btn-primary" onclick="compareAnalysis()"
                  data-toggle="tooltip" title="{{ button_compare_periods }}">
            <i class="fa fa-balance-scale"></i>
          </button>
          <button type="button" class="btn btn-warning" onclick="supplierAnalysis()"
                  data-toggle="tooltip" title="{{ button_supplier_analysis }}">
            <i class="fa fa-users"></i>
          </button>
        </div>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li class="breadcrumb-item"><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>

  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible">
      <i class="fa fa-exclamation-triangle"></i>
      {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}

    {% if success %}
    <div class="alert alert-success alert-dismissible">
      <i class="fa fa-check-circle"></i>
      {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}

    <!-- Filter Panel -->
    <div class="filter-panel">
      <h4>{{ text_analysis_filters }}</h4>
      <form id="purchase-analysis-filter-form" method="post">
        <div class="row">
          <div class="col-md-3">
            <div class="form-group">
              <label for="date_start" class="form-label">{{ entry_date_start }}</label>
              <input type="date" name="date_start" id="date_start" value="{{ date_start }}" class="form-control" required>
            </div>
          </div>
          <div class="col-md-3">
            <div class="form-group">
              <label for="date_end" class="form-label">{{ entry_date_end }}</label>
              <input type="date" name="date_end" id="date_end" value="{{ date_end }}" class="form-control" required>
            </div>
          </div>
          <div class="col-md-2">
            <div class="form-group">
              <label for="supplier_id" class="form-label">{{ entry_supplier }}</label>
              <select name="supplier_id" id="supplier_id" class="form-control">
                <option value="">{{ text_all_suppliers }}</option>
                {% for supplier in suppliers %}
                <option value="{{ supplier.supplier_id }}"{% if supplier.supplier_id == supplier_id %} selected{% endif %}>{{ supplier.name }}</option>
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="col-md-2">
            <div class="form-group">
              <label for="category_id" class="form-label">{{ entry_category }}</label>
              <select name="category_id" id="category_id" class="form-control">
                <option value="">{{ text_all_categories }}</option>
                {% for category in categories %}
                <option value="{{ category.category_id }}"{% if category.category_id == category_id %} selected{% endif %}>{{ category.name }}</option>
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="col-md-2">
            <div class="form-group">
              <label class="form-label">&nbsp;</label>
              <div>
                <button type="submit" class="btn btn-primary btn-block">
                  <i class="fa fa-search"></i> {{ button_analyze }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>

    <!-- Purchase Analysis Content -->
    {% if analysis_data %}
    <!-- Key Performance Indicators -->
    <div class="purchase-kpis">
      <div class="purchase-kpi kpi-total-purchases total-purchases">
        <h4>{{ text_total_purchases }}</h4>
        <div class="amount">{{ analysis_data.total_purchases_formatted }}</div>
        <div class="description">{{ text_total_purchases_description }}</div>
      </div>

      <div class="purchase-kpi kpi-total-orders total-orders">
        <h4>{{ text_total_orders }}</h4>
        <div class="amount">{{ analysis_data.total_orders }}</div>
        <div class="description">{{ text_total_orders_description }}</div>
      </div>

      <div class="purchase-kpi kpi-average-order average-order">
        <h4>{{ text_average_order }}</h4>
        <div class="amount">{{ analysis_data.average_order_formatted }}</div>
        <div class="description">{{ text_average_order_description }}</div>
      </div>

      <div class="purchase-kpi kpi-top-supplier top-supplier">
        <h4>{{ text_top_supplier }}</h4>
        <div class="amount">{{ analysis_data.top_supplier_name }}</div>
        <div class="description">{{ analysis_data.top_supplier_amount_formatted }}</div>
      </div>

      <div class="purchase-kpi kpi-cost-savings cost-savings">
        <h4>{{ text_cost_savings }}</h4>
        <div class="amount">{{ analysis_data.cost_savings_formatted }}</div>
        <div class="description">{{ text_cost_savings_description }}</div>
      </div>
    </div>

    <!-- Charts Section -->
    <div class="purchase-charts">
      <div class="purchase-chart">
        <h4>{{ text_purchase_trend_chart }}</h4>
        <div class="chart-container">
          <canvas id="purchaseTrendChart"></canvas>
        </div>
      </div>

      <div class="purchase-chart">
        <h4>{{ text_supplier_breakdown_chart }}</h4>
        <div class="chart-container">
          <canvas id="supplierBreakdownChart"></canvas>
        </div>
      </div>

      <div class="purchase-chart">
        <h4>{{ text_category_analysis_chart }}</h4>
        <div class="chart-container">
          <canvas id="categoryAnalysisChart"></canvas>
        </div>
      </div>

      <div class="purchase-chart">
        <h4>{{ text_monthly_comparison_chart }}</h4>
        <div class="chart-container">
          <canvas id="monthlyComparisonChart"></canvas>
        </div>
      </div>
    </div>

    <!-- Top Products Table -->
    <div class="purchase-analysis-container">
      <div class="purchase-analysis-header">
        <h2>{{ text_top_products }}</h2>
      </div>

      <div class="table-responsive">
        <table class="purchase-table" id="top-products-table">
          <thead>
            <tr>
              <th>{{ column_product_name }}</th>
              <th>{{ column_quantity }}</th>
              <th>{{ column_total_cost }}</th>
              <th>{{ column_average_cost }}</th>
              <th>{{ column_orders_count }}</th>
              <th>{{ column_last_purchase_date }}</th>
              <th>{{ column_trend }}</th>
            </tr>
          </thead>
          <tbody>
            {% for product in analysis_data.top_products %}
            <tr>
              <td>{{ product.name }}</td>
              <td>{{ product.quantity }}</td>
              <td class="amount-cell amount-neutral">{{ product.total_cost_formatted }}</td>
              <td class="amount-cell amount-neutral">{{ product.average_cost_formatted }}</td>
              <td>{{ product.orders_count }}</td>
              <td>{{ product.last_purchase_date_formatted }}</td>
              <td>
                <div class="trend-indicator trend-{{ product.trend }}">
                  <i class="fa fa-arrow-{{ product.trend == 'up' ? 'up' : product.trend == 'down' ? 'down' : 'right' }}"></i>
                  {{ product.trend_percentage }}%
                </div>
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>

    <!-- Top Suppliers Table -->
    <div class="purchase-analysis-container">
      <div class="purchase-analysis-header">
        <h2>{{ text_top_suppliers }}</h2>
      </div>

      <div class="table-responsive">
        <table class="purchase-table" id="top-suppliers-table">
          <thead>
            <tr>
              <th>{{ column_supplier_name }}</th>
              <th>{{ column_total_purchases }}</th>
              <th>{{ column_orders_count }}</th>
              <th>{{ column_average_order_value }}</th>
              <th>{{ column_payment_terms }}</th>
              <th>{{ column_last_order_date }}</th>
              <th>{{ column_performance_rating }}</th>
            </tr>
          </thead>
          <tbody>
            {% for supplier in analysis_data.top_suppliers %}
            <tr>
              <td>{{ supplier.name }}</td>
              <td class="amount-cell amount-neutral">{{ supplier.total_purchases_formatted }}</td>
              <td>{{ supplier.orders_count }}</td>
              <td class="amount-cell amount-neutral">{{ supplier.average_order_value_formatted }}</td>
              <td>{{ supplier.payment_terms }}</td>
              <td>{{ supplier.last_order_date_formatted }}</td>
              <td>
                <div class="performance-rating rating-{{ supplier.performance_level }}">
                  {{ supplier.performance_rating }}/5
                  <div class="rating-stars">
                    {% for i in 1..5 %}
                    <i class="fa fa-star{% if i > supplier.performance_rating %}-o{% endif %}"></i>
                    {% endfor %}
                  </div>
                </div>
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>
    {% else %}
    <div class="alert alert-info">
      <i class="fa fa-info-circle"></i>
      {{ text_no_data }}
    </div>
    {% endif %}
  </div>
</div>

<script>
// Enterprise Grade Plus JavaScript for Purchase Analysis
class PurchaseAnalysisManager {
    constructor() {
        this.charts = {};
        this.initializeTooltips();
        this.initializeDataTables();
        this.initializeKeyboardShortcuts();
        this.initializeCharts();
        this.initializeFormValidation();
    }

    initializeTooltips() {
        $('[data-toggle="tooltip"]').tooltip();
    }

    initializeDataTables() {
        const tables = ['top-products-table', 'top-suppliers-table'];

        tables.forEach(tableId => {
            const table = document.getElementById(tableId);
            if (table && typeof $ !== 'undefined' && $.fn.DataTable) {
                $(table).DataTable({
                    responsive: true,
                    pageLength: 25,
                    order: [[2, 'desc']], // Sort by total cost desc
                    columnDefs: [
                        { targets: [2, 3], className: 'text-end' },
                        { targets: [6], className: 'text-center' }
                    ],
                    language: {
                        url: '{{ direction == "rtl" ? "/view/javascript/jquery/datatables-ar.json" : "/view/javascript/jquery/datatables-en.json" }}'
                    }
                });
            }
        });
    }

    initializeKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case 'g':
                        e.preventDefault();
                        this.generateReport();
                        break;
                    case 'e':
                        e.preventDefault();
                        this.showExportMenu();
                        break;
                    case 'p':
                        e.preventDefault();
                        this.printAnalysis();
                        break;
                    case 'c':
                        e.preventDefault();
                        this.compareAnalysis();
                        break;
                    case 's':
                        e.preventDefault();
                        this.supplierAnalysis();
                        break;
                }
            }
        });
    }

    initializeCharts() {
        // Chart initialization would go here
        console.log('Charts initialized');
    }

    initializeFormValidation() {
        const form = document.getElementById('purchase-analysis-filter-form');
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.validateAndSubmitForm();
            });
        }
    }

    validateAndSubmitForm() {
        const form = document.getElementById('purchase-analysis-filter-form');
        const formData = new FormData(form);

        // Validate date range
        const startDate = new Date(formData.get('date_start'));
        const endDate = new Date(formData.get('date_end'));

        if (startDate >= endDate) {
            this.showAlert('{{ error_invalid_date_range }}', 'danger');
            return;
        }

        // Submit form
        this.showLoadingState(true);
        form.submit();
    }

    generateReport() {
        this.showLoadingState(true);

        const formData = this.getFormData();

        fetch('{{ url_link('accounts/purchase_analysis', 'generate') }}', {
            method: 'POST',
            body: JSON.stringify(formData),
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            this.showLoadingState(false);
            if (data.success) {
                this.showAlert('{{ success_report_generated }}', 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                this.showAlert(data.error || '{{ error_report_generation }}', 'danger');
            }
        })
        .catch(error => {
            this.showLoadingState(false);
            this.showAlert('{{ error_report_generation }}: ' + error.message, 'danger');
        });
    }

    exportAnalysis(format) {
        const params = new URLSearchParams({
            format: format,
            date_start: document.getElementById('date_start').value,
            date_end: document.getElementById('date_end').value,
            supplier_id: document.getElementById('supplier_id').value,
            category_id: document.getElementById('category_id').value
        });

        this.showAlert('{{ text_exporting }}...', 'info');
        window.open('{{ export_url }}&' + params.toString(), '_blank');
    }

    printAnalysis() {
        window.print();
    }

    compareAnalysis() {
        window.open('{{ url_link('accounts/purchase_analysis', 'compare') }}', '_blank');
    }

    supplierAnalysis() {
        window.open('{{ url_link('accounts/purchase_analysis', 'suppliers') }}', '_blank');
    }

    getFormData() {
        const form = document.getElementById('purchase-analysis-filter-form');
        const formData = new FormData(form);
        const data = {};

        for (let [key, value] of formData.entries()) {
            data[key] = value;
        }

        return data;
    }

    showExportMenu() {
        const exportButton = document.querySelector('.dropdown-toggle');
        if (exportButton) {
            exportButton.click();
        }
    }

    showLoadingState(show) {
        const buttons = document.querySelectorAll('.btn');
        buttons.forEach(btn => {
            if (show) {
                btn.disabled = true;
                if (btn.querySelector('i')) {
                    btn.querySelector('i').className = 'fa fa-spinner fa-spin';
                }
            } else {
                btn.disabled = false;
                location.reload();
            }
        });
    }

    showAlert(message, type) {
        const alertContainer = document.createElement('div');
        alertContainer.className = `alert alert-${type} alert-dismissible fade show`;
        alertContainer.innerHTML = `
            <i class="fa fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'}"></i>
            ${message}
            <button type="button" class="close" data-dismiss="alert">&times;</button>
        `;

        const container = document.querySelector('#content .container-fluid');
        container.insertBefore(alertContainer, container.firstChild);

        setTimeout(() => {
            alertContainer.remove();
        }, 5000);
    }
}

// Global functions for backward compatibility
function generateReport() {
    purchaseAnalysisManager.generateReport();
}

function exportAnalysis(format) {
    purchaseAnalysisManager.exportAnalysis(format);
}

function printAnalysis() {
    purchaseAnalysisManager.printAnalysis();
}

function compareAnalysis() {
    purchaseAnalysisManager.compareAnalysis();
}

function supplierAnalysis() {
    purchaseAnalysisManager.supplierAnalysis();
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.purchaseAnalysisManager = new PurchaseAnalysisManager();
});
</script>

{{ footer }}
