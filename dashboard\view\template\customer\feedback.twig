{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <a href="{{ add }}" data-toggle="tooltip" title="{{ button_add }}" class="btn btn-primary"><i class="fa fa-plus"></i></a>
        <button type="button" data-toggle="tooltip" title="{{ button_delete }}" class="btn btn-danger" onclick="confirm('{{ text_confirm }}') ? $('#form-feedback').submit() : false;"><i class="fa fa-trash-o"></i></button>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    {% if success %}
    <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-list"></i> {{ text_list }}</h3>
      </div>
      <div class="panel-body">
        <form method="post" action="{{ delete }}" enctype="multipart/form-data" id="form-feedback">
          <div class="table-responsive">
            <table class="table table-bordered table-hover">
              <thead>
                <tr>
                  <td style="width: 1px;" class="text-center"><input type="checkbox" onclick="$('input[name*=\'selected\']').prop('checked', this.checked);" /></td>
                  <td class="text-left">{{ column_customer }}</td>
                  <td class="text-left">{{ column_email }}</td>
                  <td class="text-left">{{ column_subject }}</td>
                  <td class="text-left">{{ column_feedback_type }}</td>
                  <td class="text-left">{{ column_status }}</td>
                  <td class="text-left">{{ column_priority }}</td>
                  <td class="text-left">{{ column_created_at }}</td>
                  <td class="text-right">{{ column_action }}</td>
                </tr>
              </thead>
              <tbody id="feedback-list">
                <!-- سيتم تحميل البيانات عبر AJAX -->
              </tbody>
            </table>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript">
$(document).ready(function() {
    loadFeedbacks();
});

function loadFeedbacks() {
    $.ajax({
        url: 'index.php?route=customer/feedback/getList&user_token={{ user_token }}',
        type: 'GET',
        dataType: 'json',
        success: function(data) {
            var html = '';
            
            $.each(data.feedbacks, function(index, feedback) {
                html += '<tr>';
                html += '<td class="text-center"><input type="checkbox" name="selected[]" value="' + feedback.feedback_id + '" /></td>';
                html += '<td class="text-left">' + feedback.customer_name + '</td>';
                html += '<td class="text-left">' + feedback.customer_email + '</td>';
                html += '<td class="text-left">' + feedback.subject + '</td>';
                html += '<td class="text-left"><span class="label label-info">' + feedback.feedback_type + '</span></td>';
                html += '<td class="text-left"><span class="label label-' + getStatusClass(feedback.status) + '">' + feedback.status + '</span></td>';
                html += '<td class="text-left"><span class="label label-' + getPriorityClass(feedback.priority) + '">' + feedback.priority + '</span></td>';
                html += '<td class="text-left">' + feedback.created_at + '</td>';
                html += '<td class="text-right"><a href="' + feedback.edit + '" data-toggle="tooltip" title="{{ button_edit }}" class="btn btn-primary"><i class="fa fa-pencil"></i></a></td>';
                html += '</tr>';
            });
            
            $('#feedback-list').html(html);
        }
    });
}

function getStatusClass(status) {
    switch(status) {
        case 'new': return 'primary';
        case 'in_progress': return 'warning';
        case 'resolved': return 'success';
        case 'closed': return 'default';
        case 'cancelled': return 'danger';
        default: return 'default';
    }
}

function getPriorityClass(priority) {
    switch(priority) {
        case 'low': return 'success';
        case 'medium': return 'warning';
        case 'high': return 'danger';
        case 'critical': return 'danger';
        default: return 'default';
    }
}
</script>

{{ footer }} 