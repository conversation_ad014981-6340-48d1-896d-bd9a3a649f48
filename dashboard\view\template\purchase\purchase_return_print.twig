<!DOCTYPE html>
<html dir="{{ direction }}" lang="{{ lang }}">
<head>
  <meta charset="UTF-8" />
  <title>{{ title }}</title>
  <base href="{{ base }}" />
  <link href="view/stylesheet/bootstrap.css" rel="stylesheet" media="all" />
  <script type="text/javascript" src="view/javascript/jquery/jquery-3.6.0.min.js"></script>
  <script type="text/javascript" src="view/javascript/bootstrap/js/bootstrap.bundle.min.js"></script>
  <link href="view/javascript/font-awesome/css/all.min.css" rel="stylesheet" type="text/css" />
  <link type="text/css" href="view/stylesheet/stylesheet.css" rel="stylesheet" media="all" />
  <style type="text/css">
    @page {
      size: A4;
      margin: 15mm 15mm 15mm 15mm;
    }
    
    body {
      background-color: #FFFFFF;
      color: #000000;
    }
    
    .invoice-box {
      max-width: 800px;
      margin: auto;
      padding: 30px;
      font-size: 14px;
      line-height: 24px;
      color: #555;
    }
    
    .invoice-box table {
      width: 100%;
      line-height: inherit;
      text-align: left;
    }
    
    .invoice-box table td {
      padding: 5px;
      vertical-align: top;
    }
    
    .invoice-box table tr td:nth-child(2) {
      text-align: right;
    }
    
    .invoice-box table tr.top table td {
      padding-bottom: 20px;
    }
    
    .invoice-box table tr.top table td.title {
      font-size: 45px;
      line-height: 45px;
      color: #333;
    }
    
    .invoice-box table tr.information table td {
      padding-bottom: 40px;
    }
    
    .invoice-box table tr.heading td {
      background: #eee;
      border-bottom: 1px solid #ddd;
      font-weight: bold;
    }
    
    .invoice-box table tr.details td {
      padding-bottom: 20px;
    }
    
    .invoice-box table tr.item td {
      border-bottom: 1px solid #eee;
    }
    
    .invoice-box table tr.item.last td {
      border-bottom: none;
    }
    
    .invoice-box table tr.total td:nth-child(2) {
      border-top: 2px solid #eee;
      font-weight: bold;
    }
    
    @media only screen and (max-width: 600px) {
      .invoice-box table tr.top table td {
        width: 100%;
        display: block;
        text-align: center;
      }
      
      .invoice-box table tr.information table td {
        width: 100%;
        display: block;
        text-align: center;
      }
    }
    
    /** RTL **/
    .rtl {
      direction: rtl;
      font-family: Tahoma, 'Helvetica Neue', 'Helvetica', Helvetica, Arial, sans-serif;
    }
    
    .rtl table {
      text-align: right;
    }
    
    .rtl table tr td:nth-child(2) {
      text-align: left;
    }
    
    @media print {
      body {
        margin: 0;
        padding: 0;
      }
      
      .invoice-box {
        margin: 0;
        padding: 0;
        max-width: 100%;
      }
      
      #print-button {
        display: none;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <div id="print-button" class="text-right mb-3">
      <button class="btn btn-primary" onclick="window.print();"><i class="fas fa-print"></i> {{ text_print }}</button>
    </div>
    
    <div class="invoice-box">
      <table cellpadding="0" cellspacing="0">
        <tr class="top">
          <td colspan="5">
            <table>
              <tr>
                <td class="title">
                  {% if store_logo %}
                    <img src="{{ store_logo }}" style="width:100%; max-width:300px;">
                  {% else %}
                    <h2>{{ store_name }}</h2>
                  {% endif %}
                </td>
                <td>
                  <h2>{{ text_return }}</h2>
                  <br>
                  {{ text_return_number }}: {{ return.return_number }}<br>
                  {{ text_date_added }}: {{ return.date_added }}<br>
                  {{ text_status }}: {{ return.status }}
                </td>
              </tr>
            </table>
          </td>
        </tr>
        
        <tr class="information">
          <td colspan="5">
            <table>
              <tr>
                <td>
                  {{ store_name }}<br>
                  {{ store_address }}<br>
                  {{ store_email }}<br>
                  {{ store_telephone }}
                </td>
                <td>
                  <strong>{{ text_supplier }}</strong><br>
                  {{ return.supplier }}<br>
                  <br>
                  <strong>{{ text_order_number }}</strong>: {{ return.order_number }}<br>
                  <strong>{{ text_receipt_number }}</strong>: {{ return.receipt_number }}
                </td>
              </tr>
            </table>
          </td>
        </tr>
        
        {% if return.note %}
        <tr class="details">
          <td colspan="5">
            <strong>{{ text_note }}</strong><br>
            {{ return.note }}
          </td>
        </tr>
        {% endif %}
        
        <tr class="heading">
          <td width="40%">{{ column_product }}</td>
          <td style="text-align: right;" width="15%">{{ column_quantity }}</td>
          <td style="text-align: center;" width="15%">{{ column_unit }}</td>
          <td style="text-align: right;" width="15%">{{ column_unit_price }}</td>
          <td style="text-align: right;" width="15%">{{ column_total }}</td>
        </tr>
        
        {% for item in return_items %}
        <tr class="item">
          <td>{{ item.product_name }}</td>
          <td style="text-align: right;">{{ item.quantity }}</td>
          <td style="text-align: center;">{{ item.unit }}</td>
          <td style="text-align: right;">{{ item.unit_price }}</td>
          <td style="text-align: right;">{{ item.total }}</td>
        </tr>
        {% endfor %}
        
        <tr class="total">
          <td colspan="4" style="text-align: right;"><strong>{{ text_total_amount }}</strong></td>
          <td style="text-align: right;"><strong>{{ total_amount }}</strong></td>
        </tr>
      </table>
      
      <div class="row mt-5">
        <div class="col-md-6">
          <div style="margin-top: 50px; border-top: 1px solid #ddd; padding-top: 10px;">
            <p>{{ text_supplier }} {{ text_signature }}</p>
          </div>
        </div>
        <div class="col-md-6 text-right">
          <div style="margin-top: 50px; border-top: 1px solid #ddd; padding-top: 10px;">
            <p>{{ text_authorized }} {{ text_signature }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>
</html> 