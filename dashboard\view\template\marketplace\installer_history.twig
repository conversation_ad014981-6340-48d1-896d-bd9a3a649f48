<div class="table-responsive">
  <table class="table table-bordered">
    <thead>
      <tr>
        <th>{{ column_filename }}</th>
        <th>{{ column_date_added }}</th>
        <th class="text-right">{{ column_action }}</th>
      </tr>
    </thead>
    <tbody>
    
    {% if histories %}
    {% for history in histories %}
    <tr>
      <td>{{ history.filename }}</td>
      <td>{{ history.date_added }}</td>
      <td class="text-right"><button type="button" value="{{ history.extension_install_id }}" data-loading="{{ text_loading }}" data-toggle="tooltip" title="{{ button_uninstall }}" class="btn btn-danger"><i class="fa fa-trash-o"></i></button></td>
    </tr>
    {% endfor %}
    {% else %}
    <tr>
      <td colspan="3" class="text-center">{{ text_no_results }}</td>
    </tr>
    {% endif %}
    </tbody>
    
  </table>
</div>
<div class="row">
  <div class="col-sm-6 text-left">{{ pagination }}</div>
  <div class="col-sm-6 text-right">{{ results }}</div>
</div>
