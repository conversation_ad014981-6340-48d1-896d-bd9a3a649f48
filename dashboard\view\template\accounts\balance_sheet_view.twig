{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="submit" form="accounts\balance_sheet_advanced-form" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fas fa-save"></i></button>
        <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-light"><i class="fas fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    
    <div class="card">
      <div class="card-header"><i class="fas fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}chart-bar{% elif screen_type == 'settings' %}cog{% else %}pencil-alt{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="card-body">
        <form id="accounts\balance_sheet_advanced-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-analysis_url">{{ text_analysis_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="analysis_url" value="{{ analysis_url }}" placeholder="{{ text_analysis_url }}" id="input-analysis_url" class="form-control" />
              {% if error_analysis_url %}
                <div class="invalid-feedback">{{ error_analysis_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-back_url">{{ text_back_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="back_url" value="{{ back_url }}" placeholder="{{ text_back_url }}" id="input-back_url" class="form-control" />
              {% if error_back_url %}
                <div class="invalid-feedback">{{ error_back_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-balance_check">{{ text_balance_check }}</label>
            <div class="col-sm-10">
              <input type="text" name="balance_check" value="{{ balance_check }}" placeholder="{{ text_balance_check }}" id="input-balance_check" class="form-control" />
              {% if error_balance_check %}
                <div class="invalid-feedback">{{ error_balance_check }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-balance_sheet_data">{{ text_balance_sheet_data }}</label>
            <div class="col-sm-10">
              <input type="text" name="balance_sheet_data" value="{{ balance_sheet_data }}" placeholder="{{ text_balance_sheet_data }}" id="input-balance_sheet_data" class="form-control" />
              {% if error_balance_sheet_data %}
                <div class="invalid-feedback">{{ error_balance_sheet_data }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-company_address">{{ text_company_address }}</label>
            <div class="col-sm-10">
              <input type="text" name="company_address" value="{{ company_address }}" placeholder="{{ text_company_address }}" id="input-company_address" class="form-control" />
              {% if error_company_address %}
                <div class="invalid-feedback">{{ error_company_address }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-company_name">{{ text_company_name }}</label>
            <div class="col-sm-10">
              <input type="text" name="company_name" value="{{ company_name }}" placeholder="{{ text_company_name }}" id="input-company_name" class="form-control" />
              {% if error_company_name %}
                <div class="invalid-feedback">{{ error_company_name }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-compare_url">{{ text_compare_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="compare_url" value="{{ compare_url }}" placeholder="{{ text_compare_url }}" id="input-compare_url" class="form-control" />
              {% if error_compare_url %}
                <div class="invalid-feedback">{{ error_compare_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-currencies">{{ text_currencies }}</label>
            <div class="col-sm-10">
              <input type="text" name="currencies" value="{{ currencies }}" placeholder="{{ text_currencies }}" id="input-currencies" class="form-control" />
              {% if error_currencies %}
                <div class="invalid-feedback">{{ error_currencies }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-export_excel">{{ text_export_excel }}</label>
            <div class="col-sm-10">
              <input type="text" name="export_excel" value="{{ export_excel }}" placeholder="{{ text_export_excel }}" id="input-export_excel" class="form-control" />
              {% if error_export_excel %}
                <div class="invalid-feedback">{{ error_export_excel }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-export_pdf">{{ text_export_pdf }}</label>
            <div class="col-sm-10">
              <input type="text" name="export_pdf" value="{{ export_pdf }}" placeholder="{{ text_export_pdf }}" id="input-export_pdf" class="form-control" />
              {% if error_export_pdf %}
                <div class="invalid-feedback">{{ error_export_pdf }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_data">{{ text_filter_data }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_data" value="{{ filter_data }}" placeholder="{{ text_filter_data }}" id="input-filter_data" class="form-control" />
              {% if error_filter_data %}
                <div class="invalid-feedback">{{ error_filter_data }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-generated_by">{{ text_generated_by }}</label>
            <div class="col-sm-10">
              <input type="text" name="generated_by" value="{{ generated_by }}" placeholder="{{ text_generated_by }}" id="input-generated_by" class="form-control" />
              {% if error_generated_by %}
                <div class="invalid-feedback">{{ error_generated_by }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="invalid-feedback">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-integrity_check_url">{{ text_integrity_check_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="integrity_check_url" value="{{ integrity_check_url }}" placeholder="{{ text_integrity_check_url }}" id="input-integrity_check_url" class="form-control" />
              {% if error_integrity_check_url %}
                <div class="invalid-feedback">{{ error_integrity_check_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-print_mode">{{ text_print_mode }}</label>
            <div class="col-sm-10">
              <input type="text" name="print_mode" value="{{ print_mode }}" placeholder="{{ text_print_mode }}" id="input-print_mode" class="form-control" />
              {% if error_print_mode %}
                <div class="invalid-feedback">{{ error_print_mode }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-print_url">{{ text_print_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="print_url" value="{{ print_url }}" placeholder="{{ text_print_url }}" id="input-print_url" class="form-control" />
              {% if error_print_url %}
                <div class="invalid-feedback">{{ error_print_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-ratios_url">{{ text_ratios_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="ratios_url" value="{{ ratios_url }}" placeholder="{{ text_ratios_url }}" id="input-ratios_url" class="form-control" />
              {% if error_ratios_url %}
                <div class="invalid-feedback">{{ error_ratios_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-report_date">{{ text_report_date }}</label>
            <div class="col-sm-10">
              <input type="text" name="report_date" value="{{ report_date }}" placeholder="{{ text_report_date }}" id="input-report_date" class="form-control" />
              {% if error_report_date %}
                <div class="invalid-feedback">{{ error_report_date }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-report_title">{{ text_report_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="report_title" value="{{ report_title }}" placeholder="{{ text_report_title }}" id="input-report_title" class="form-control" />
              {% if error_report_title %}
                <div class="invalid-feedback">{{ error_report_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-warning">{{ text_warning }}</label>
            <div class="col-sm-10">
              <input type="text" name="warning" value="{{ warning }}" placeholder="{{ text_warning }}" id="input-warning" class="form-control" />
              {% if error_warning %}
                <div class="invalid-feedback">{{ error_warning }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}