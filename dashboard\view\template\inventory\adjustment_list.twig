{{ header }}
{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="button" data-bs-toggle="tooltip" title="{{ button_filter }}" onclick="$('#filter-adjustment').toggleClass('d-none');" class="btn btn-light d-md-none d-lg-none"><i class="fas fa-filter"></i></button>
        <a href="{{ add }}" data-bs-toggle="tooltip" title="{{ button_add }}" class="btn btn-primary"><i class="fas fa-plus"></i></a>
        <button type="submit" form="form-adjustment" formaction="{{ delete }}" data-bs-toggle="tooltip" title="{{ button_delete }}" onclick="return confirm('{{ text_confirm_delete }}');" class="btn btn-danger"><i class="fas fa-trash-alt"></i></button>
      </div>
      <h1>{{ heading_title }}</h1>
      <ol class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
          <li class="breadcrumb-item"><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{ error_warning }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {% endif %}
    {% if success %}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{ success }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {% endif %}
    <div class="row">
      <div id="filter-adjustment" class="col-lg-3 col-md-12 order-lg-last d-none d-lg-block mb-3">
        <div class="card">
          <div class="card-header"><i class="fas fa-filter"></i> {{ button_filter }}</div>
          <div class="card-body">
            <div class="mb-3">
              <label for="input-reference" class="form-label">{{ filter_reference }}</label>
              <input type="text" name="filter_reference" value="{{ filter_reference }}" placeholder="{{ filter_reference }}" id="input-reference" class="form-control"/>
            </div>
            <div class="mb-3">
              <label for="input-branch" class="form-label">{{ filter_branch }}</label>
              <select name="filter_branch" id="input-branch" class="form-select">
                <option value="">{{ text_select }}</option>
                {% for branch in branches %}
                  <option value="{{ branch.branch_id }}" {% if branch.branch_id == filter_branch %}selected{% endif %}>{{ branch.name }}</option>
                {% endfor %}
              </select>
            </div>
            <div class="mb-3">
              <label for="input-status" class="form-label">{{ filter_status }}</label>
              <select name="filter_status" id="input-status" class="form-select">
                <option value="">{{ text_select }}</option>
                <option value="pending" {% if filter_status == 'pending' %}selected{% endif %}>{{ text_pending }}</option>
                <option value="approved" {% if filter_status == 'approved' %}selected{% endif %}>{{ text_approved }}</option>
                <option value="rejected" {% if filter_status == 'rejected' %}selected{% endif %}>{{ text_rejected }}</option>
                <option value="cancelled" {% if filter_status == 'cancelled' %}selected{% endif %}>{{ text_cancelled }}</option>
              </select>
            </div>
            <div class="mb-3">
              <label for="input-date-start" class="form-label">{{ filter_date_start }}</label>
              <input type="date" name="filter_date_start" value="{{ filter_date_start }}" id="input-date-start" class="form-control"/>
            </div>
            <div class="mb-3">
              <label for="input-date-end" class="form-label">{{ filter_date_end }}</label>
              <input type="date" name="filter_date_end" value="{{ filter_date_end }}" id="input-date-end" class="form-control"/>
            </div>
            <div class="text-end">
              <button type="button" id="button-filter" class="btn btn-primary"><i class="fas fa-filter"></i> {{ button_filter }}</button>
            </div>
          </div>
        </div>
      </div>
      <div class="col-lg-9 col-md-12">
        <div class="card">
          <div class="card-header"><i class="fas fa-list"></i> {{ text_list }}</div>
          <div class="card-body">
            <form id="form-adjustment" method="post">
              <div class="table-responsive">
                <table class="table table-bordered table-hover">
                  <thead>
                    <tr>
                      <td style="width: 1px;" class="text-center"><input type="checkbox" onclick="$('input[name*=\'selected\']').prop('checked', $(this).prop('checked'));" class="form-check-input"/></td>
                      <td class="text-start"><a href="{{ sort_reference }}" {% if sort == 'a.reference_number' %}class="{{ order|lower }}"{% endif %}>{{ column_reference }}</a></td>
                      <td class="text-start"><a href="{{ sort_branch }}" {% if sort == 'b.name' %}class="{{ order|lower }}"{% endif %}>{{ column_branch }}</a></td>
                      <td class="text-start"><a href="{{ sort_date }}" {% if sort == 'a.adjustment_date' %}class="{{ order|lower }}"{% endif %}>{{ column_adjustment_date }}</a></td>
                      <td class="text-start"><a href="{{ sort_status }}" {% if sort == 'a.status' %}class="{{ order|lower }}"{% endif %}>{{ column_status }}</a></td>
                      <td class="text-start">{{ column_created_by }}</td>
                      <td class="text-start"><a href="{{ sort_created_at }}" {% if sort == 'a.created_at' %}class="{{ order|lower }}"{% endif %}>{{ column_created_at }}</a></td>
                      <td class="text-end">{{ column_action }}</td>
                    </tr>
                  </thead>
                  <tbody>
                    {% if adjustments %}
                      {% for adjustment in adjustments %}
                        <tr>
                          <td class="text-center"><input type="checkbox" name="selected[]" value="{{ adjustment.adjustment_id }}" class="form-check-input"/></td>
                          <td class="text-start">{{ adjustment.reference_number }}</td>
                          <td class="text-start">{{ adjustment.branch_name }}</td>
                          <td class="text-start">{{ adjustment.adjustment_date }}</td>
                          <td class="text-start">
                            {% if adjustment.status == 'pending' %}
                              <span class="badge bg-warning">{{ text_pending }}</span>
                            {% elseif adjustment.status == 'approved' %}
                              <span class="badge bg-success">{{ text_approved }}</span>
                            {% elseif adjustment.status == 'rejected' %}
                              <span class="badge bg-danger">{{ text_rejected }}</span>
                            {% elseif adjustment.status == 'cancelled' %}
                              <span class="badge bg-secondary">{{ text_cancelled }}</span>
                            {% else %}
                              <span class="badge bg-light text-dark">{{ adjustment.status }}</span>
                            {% endif %}
                          </td>
                          <td class="text-start">{{ adjustment.created_by }}</td>
                          <td class="text-start">{{ adjustment.created_at }}</td>
                          <td class="text-end">
                            <div class="btn-group">
                              <a href="{{ adjustment.edit }}" data-bs-toggle="tooltip" title="{{ button_edit }}" class="btn btn-primary"><i class="fas fa-pencil-alt"></i></a>
                              {% if adjustment.status == 'pending' %}
                                <button type="button" data-adjustment-id="{{ adjustment.adjustment_id }}" data-bs-toggle="tooltip" title="{{ button_approve }}" class="btn btn-success btn-approve"><i class="fas fa-check"></i></button>
                                <button type="button" data-adjustment-id="{{ adjustment.adjustment_id }}" data-bs-toggle="tooltip" title="{{ button_reject }}" class="btn btn-danger btn-reject"><i class="fas fa-times"></i></button>
                              {% endif %}
                            </div>
                          </td>
                        </tr>
                      {% endfor %}
                    {% else %}
                      <tr>
                        <td class="text-center" colspan="8">{{ text_no_results }}</td>
                      </tr>
                    {% endif %}
                  </tbody>
                </table>
              </div>
            </form>
            <div class="row">
              <div class="col-sm-6 text-start">{{ pagination }}</div>
              <div class="col-sm-6 text-end">{{ results }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<script type="text/javascript">
$('#button-filter').on('click', function() {
	var url = 'index.php?route=inventory/adjustment&user_token={{ user_token }}';

	var filter_reference = $('#input-reference').val();
	if (filter_reference) {
		url += '&filter_reference=' + encodeURIComponent(filter_reference);
	}

	var filter_branch = $('#input-branch').val();
	if (filter_branch) {
		url += '&filter_branch=' + encodeURIComponent(filter_branch);
	}

	var filter_status = $('#input-status').val();
	if (filter_status) {
		url += '&filter_status=' + encodeURIComponent(filter_status);
	}

	var filter_date_start = $('#input-date-start').val();
	if (filter_date_start) {
		url += '&filter_date_start=' + encodeURIComponent(filter_date_start);
	}

	var filter_date_end = $('#input-date-end').val();
	if (filter_date_end) {
		url += '&filter_date_end=' + encodeURIComponent(filter_date_end);
	}

	location = url;
});

// اعتماد التعديل
$('.btn-approve').on('click', function() {
    var adjustment_id = $(this).data('adjustment-id');
    
    if (confirm('{{ text_confirm_approve }}')) {
        $.ajax({
            url: 'index.php?route=inventory/adjustment/approve&user_token={{ user_token }}&adjustment_id=' + adjustment_id,
            dataType: 'json',
            beforeSend: function() {
                $('.btn-approve[data-adjustment-id="' + adjustment_id + '"]').prop('disabled', true);
            },
            complete: function() {
                $('.btn-approve[data-adjustment-id="' + adjustment_id + '"]').prop('disabled', false);
            },
            success: function(json) {
                if (json['error']) {
                    alert(json['error']);
                }
                
                if (json['success']) {
                    alert(json['success']);
                    location.reload();
                }
            },
            error: function(xhr, ajaxOptions, thrownError) {
                alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
            }
        });
    }
});

// رفض التعديل
$('.btn-reject').on('click', function() {
    var adjustment_id = $(this).data('adjustment-id');
    
    if (confirm('{{ text_confirm_reject }}')) {
        $.ajax({
            url: 'index.php?route=inventory/adjustment/reject&user_token={{ user_token }}&adjustment_id=' + adjustment_id,
            dataType: 'json',
            beforeSend: function() {
                $('.btn-reject[data-adjustment-id="' + adjustment_id + '"]').prop('disabled', true);
            },
            complete: function() {
                $('.btn-reject[data-adjustment-id="' + adjustment_id + '"]').prop('disabled', false);
            },
            success: function(json) {
                if (json['error']) {
                    alert(json['error']);
                }
                
                if (json['success']) {
                    alert(json['success']);
                    location.reload();
                }
            },
            error: function(xhr, ajaxOptions, thrownError) {
                alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
            }
        });
    }
});
</script>
{{ footer }}
