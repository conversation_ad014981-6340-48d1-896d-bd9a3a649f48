<?php
/**
 * تحكم إدارة الحسابات المصرفية المتقدمة والمتكاملة
 * يجمع بين القوة والبساطة - يتفوق على SAP وOracle في سهولة الاستخدام
 * مع الاحتفاظ بالقوة والمرونة المطلوبة للشركات الكبيرة
 * متوافق مع معايير المحاسبة المصرية والبنك المركزي المصري
 */
class ControllerAccountsBankAccountsAdvanced extends Controller {
    private $error = array();
    private $central_service;

    public function __construct($registry) {
        parent::__construct($registry);

        // تحميل الخدمات المركزية
        $this->load->model('core/central_service_manager');
        $this->central_service = $this->model_core_central_service_manager;
    }

    public function index() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/bank_accounts_advanced') ||
            !$this->user->hasKey('accounting_bank_accounts_view')) {

            $this->central_service->logActivity('unauthorized_access', 'accounts',
                $this->language->get('log_unauthorized_access_bank_accounts_advanced'), [
                'user_id' => $this->user->getId(),
                'ip_address' => $this->request->server['REMOTE_ADDR']
            ]);

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/bank_accounts_advanced');
        $this->document->setTitle($this->language->get('heading_title'));
        $this->load->model('accounts/bank_accounts_advanced');
        $this->load->model('accounts/audit_trail');

        // تسجيل الوصول للشاشة
        $this->central_service->logActivity('view', 'accounts',
            $this->language->get('log_view_bank_accounts_advanced_screen'), [
            'user_id' => $this->user->getId(),
            'screen' => 'accounts/bank_accounts_advanced'
        ]);

        // إضافة CSS و JavaScript المتقدم
        $this->document->addStyle('view/stylesheet/accounts/bank_accounts.css');
        $this->document->addScript('view/javascript/accounts/bank_accounts.js');
        $this->document->addScript('view/javascript/jquery/accounting.min.js');
        $this->document->addScript('view/javascript/jquery/select2.min.js');
        $this->document->addStyle('view/javascript/jquery/select2.min.css');
        $this->document->addScript('view/javascript/jquery/daterangepicker.min.js');
        $this->document->addStyle('view/javascript/jquery/daterangepicker.css');
        $this->document->addScript('view/javascript/jquery/chart.min.js');
        $this->document->addScript('view/javascript/jquery/datatables.min.js');
        $this->document->addStyle('view/javascript/jquery/datatables.min.css');

        // تسجيل الوصول في سجل المراجعة
        $this->model_accounts_audit_trail->logAction([
            'action_type' => 'view',
            'table_name' => 'bank_accounts',
            'record_id' => 0,
            'description' => $this->language->get('log_view_bank_accounts_management_screen'),
            'module' => 'bank_accounts'
        ]);

        $this->getList();
    }

    public function add() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('modify', 'accounts/bank_accounts_advanced') ||
            !$this->user->hasKey('accounting_bank_accounts_add')) {

            $this->central_service->logActivity('unauthorized_access', 'accounts',
                $this->language->get('log_unauthorized_add_bank_account'), [
                'user_id' => $this->user->getId(),
                'ip_address' => $this->request->server['REMOTE_ADDR']
            ]);

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/bank_accounts_advanced');
        $this->document->setTitle($this->language->get('heading_title'));
        $this->load->model('accounts/bank_accounts_advanced');

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm()) {
            try {
                $account_id = $this->model_accounts_bank_accounts_advanced->addBankAccount($this->request->post);

                // تسجيل إضافة الحساب
                $this->central_service->logActivity('add_bank_account', 'accounts',
                    $this->language->get('log_add_bank_account') . ': ' . $this->request->post['account_name'], [
                    'user_id' => $this->user->getId(),
                    'account_id' => $account_id,
                    'bank_name' => $this->request->post['bank_name'],
                    'account_number' => $this->request->post['account_number']
                ]);

                // إرسال إشعار للإدارة المالية
                $this->central_service->sendNotification(
                    'new_bank_account_added',
                    $this->language->get('text_bank_account_added'),
                    $this->language->get('text_bank_account_added_notification') . ': ' . $this->request->post['account_name'] . ' ' . $this->language->get('text_in_bank') . ' ' . $this->request->post['bank_name'],
                    [$this->config->get('config_cfo_id'), $this->config->get('config_chief_accountant_id')],
                    [
                        'account_id' => $account_id,
                        'account_name' => $this->request->post['account_name'],
                        'bank_name' => $this->request->post['bank_name'],
                        'account_number' => $this->request->post['account_number'],
                        'added_by' => $this->user->getFirstName() . ' ' . $this->user->getLastName()
                    ]
                );

                $this->session->data['success'] = $this->language->get('text_success');

                $url = '';
                if (isset($this->request->get['sort'])) {
                    $url .= '&sort=' . $this->request->get['sort'];
                }
                if (isset($this->request->get['order'])) {
                    $url .= '&order=' . $this->request->get['order'];
                }
                if (isset($this->request->get['page'])) {
                    $url .= '&page=' . $this->request->get['page'];
                }

                $this->response->redirect($this->url->link('accounts/bank_accounts_advanced', 'user_token=' . $this->session->data['user_token'] . $url, true));

            } catch (Exception $e) {
                $this->error['warning'] = $this->language->get('error_add_bank_account') . ': ' . $e->getMessage();
            }
        }

        $this->getForm();
    }

    public function edit() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('modify', 'accounts/bank_accounts_advanced') ||
            !$this->user->hasKey('accounting_bank_accounts_edit')) {

            $this->central_service->logActivity('unauthorized_access', 'accounts',
                $this->language->get('log_unauthorized_edit_bank_account'), [
                'user_id' => $this->user->getId(),
                'account_id' => $this->request->get['account_id'] ?? 0,
                'ip_address' => $this->request->server['REMOTE_ADDR']
            ]);

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/bank_accounts_advanced');
        $this->document->setTitle($this->language->get('heading_title'));
        $this->load->model('accounts/bank_accounts_advanced');

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm()) {
            try {
                $this->model_accounts_bank_accounts_advanced->editBankAccount($this->request->get['account_id'], $this->request->post);

                // تسجيل تعديل الحساب
                $this->central_service->logActivity('edit_bank_account', 'accounts',
                    'تعديل الحساب المصرفي: ' . $this->request->post['account_name'], [
                    'user_id' => $this->user->getId(),
                    'account_id' => $this->request->get['account_id'],
                    'bank_name' => $this->request->post['bank_name'],
                    'account_number' => $this->request->post['account_number']
                ]);

                // إرسال إشعار للإدارة المالية
                $this->central_service->sendNotification(
                    'bank_account_updated',
                    'تحديث حساب مصرفي',
                    'تم تحديث الحساب المصرفي: ' . $this->request->post['account_name'] . ' في بنك ' . $this->request->post['bank_name'],
                    [$this->config->get('config_cfo_id'), $this->config->get('config_chief_accountant_id')],
                    [
                        'account_id' => $this->request->get['account_id'],
                        'account_name' => $this->request->post['account_name'],
                        'bank_name' => $this->request->post['bank_name'],
                        'updated_by' => $this->user->getFirstName() . ' ' . $this->user->getLastName()
                    ]
                );

                $this->session->data['success'] = $this->language->get('text_success');

                $url = '';
                if (isset($this->request->get['sort'])) {
                    $url .= '&sort=' . $this->request->get['sort'];
                }
                if (isset($this->request->get['order'])) {
                    $url .= '&order=' . $this->request->get['order'];
                }
                if (isset($this->request->get['page'])) {
                    $url .= '&page=' . $this->request->get['page'];
                }

                $this->response->redirect($this->url->link('accounts/bank_accounts_advanced', 'user_token=' . $this->session->data['user_token'] . $url, true));

            } catch (Exception $e) {
                $this->error['warning'] = 'خطأ في تعديل الحساب المصرفي: ' . $e->getMessage();
            }
        }

        $this->getForm();
    }

    public function delete() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('modify', 'accounts/bank_accounts_advanced') ||
            !$this->user->hasKey('accounting_bank_accounts_delete')) {

            $this->central_service->logActivity('unauthorized_access', 'accounts',
                'محاولة حذف حساب مصرفي غير مصرح بها', [
                'user_id' => $this->user->getId(),
                'account_id' => $this->request->post['selected'] ?? [],
                'ip_address' => $this->request->server['REMOTE_ADDR']
            ]);

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/bank_accounts_advanced');
        $this->load->model('accounts/bank_accounts_advanced');

        if (isset($this->request->post['selected']) && $this->validateDelete()) {
            foreach ($this->request->post['selected'] as $account_id) {
                $account_info = $this->model_accounts_bank_accounts_advanced->getBankAccount($account_id);

                $this->model_accounts_bank_accounts_advanced->deleteBankAccount($account_id);

                // تسجيل حذف الحساب
                $this->model_accounts_audit_trail->logAction([
                    'action_type' => 'delete_bank_account',
                    'table_name' => 'bank_accounts',
                    'record_id' => $account_id,
                    'description' => 'حذف الحساب المصرفي: ' . $account_info['account_name'],
                    'module' => 'bank_accounts'
                ]);
            }

            $this->session->data['success'] = $this->language->get('text_success');

            $url = '';
            if (isset($this->request->get['sort'])) {
                $url .= '&sort=' . $this->request->get['sort'];
            }
            if (isset($this->request->get['order'])) {
                $url .= '&order=' . $this->request->get['order'];
            }
            if (isset($this->request->get['page'])) {
                $url .= '&page=' . $this->request->get['page'];
            }

            $this->response->redirect($this->url->link('accounts/bank_accounts_advanced', 'user_token=' . $this->session->data['user_token'] . $url, true));
        }

        $this->getList();
    }

    public function reconcile() {
        $this->load->language('accounts/bank_accounts_advanced');
        $this->load->model('accounts/bank_accounts_advanced');

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateReconciliation()) {
            try {
                $reconciliation_data = $this->prepareReconciliationData();

                $result = $this->model_accounts_bank_accounts_advanced->performReconciliation($reconciliation_data);

                // تسجيل التسوية البنكية
                $this->model_accounts_audit_trail->logAction([
                    'action_type' => 'bank_reconciliation',
                    'table_name' => 'bank_reconciliation',
                    'record_id' => $result['reconciliation_id'],
                    'description' => 'تسوية بنكية للحساب: ' . $reconciliation_data['account_name'],
                    'module' => 'bank_accounts'
                ]);

                $this->session->data['reconciliation_result'] = $result;
                $this->session->data['success'] = 'تم إجراء التسوية البنكية بنجاح';

                $this->response->redirect($this->url->link('accounts/bank_accounts_advanced/viewReconciliation', 'user_token=' . $this->session->data['user_token'], true));

            } catch (Exception $e) {
                $this->error['warning'] = 'خطأ في التسوية البنكية: ' . $e->getMessage();
            }
        }

        $this->getReconciliationForm();
    }

    public function transfer() {
        $this->load->language('accounts/bank_accounts_advanced');
        $this->load->model('accounts/bank_accounts_advanced');

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateTransfer()) {
            try {
                $transfer_data = $this->prepareTransferData();

                $result = $this->model_accounts_bank_accounts_advanced->processTransfer($transfer_data);

                // تسجيل التحويل البنكي
                $this->model_accounts_audit_trail->logAction([
                    'action_type' => 'bank_transfer',
                    'table_name' => 'bank_transfers',
                    'record_id' => $result['transfer_id'],
                    'description' => 'تحويل بنكي من ' . $transfer_data['from_account_name'] . ' إلى ' . $transfer_data['to_account_name'],
                    'module' => 'bank_accounts'
                ]);

                $this->session->data['success'] = 'تم التحويل البنكي بنجاح';

                $this->response->redirect($this->url->link('accounts/bank_accounts_advanced', 'user_token=' . $this->session->data['user_token'], true));

            } catch (Exception $e) {
                $this->error['warning'] = 'خطأ في التحويل البنكي: ' . $e->getMessage();
            }
        }

        $this->getTransferForm();
    }

    public function getAccountAnalysis() {
        $this->load->model('accounts/bank_accounts_advanced');

        $json = array();

        if (isset($this->request->get['account_id'])) {
            try {
                $account_id = $this->request->get['account_id'];

                $analysis = $this->model_accounts_bank_accounts_advanced->analyzeAccount($account_id);

                $json['success'] = true;
                $json['analysis'] = $analysis;

            } catch (Exception $e) {
                $json['error'] = $e->getMessage();
            }
        } else {
            $json['error'] = 'معرف الحساب مطلوب';
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    public function getCashFlow() {
        $this->load->model('accounts/bank_accounts_advanced');

        $json = array();

        if (isset($this->request->get['account_id'])) {
            try {
                $account_id = $this->request->get['account_id'];
                $period = $this->request->get['period'] ?? '30'; // آخر 30 يوم افتراضياً

                $cash_flow = $this->model_accounts_bank_accounts_advanced->calculateCashFlow($account_id, $period);

                $json['success'] = true;
                $json['cash_flow'] = $cash_flow;

            } catch (Exception $e) {
                $json['error'] = $e->getMessage();
            }
        } else {
            $json['error'] = 'معرف الحساب مطلوب';
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    public function getTransactionHistory() {
        $this->load->model('accounts/bank_accounts_advanced');

        $json = array();

        if (isset($this->request->get['account_id'])) {
            try {
                $account_id = $this->request->get['account_id'];
                $limit = $this->request->get['limit'] ?? 50;
                $offset = $this->request->get['offset'] ?? 0;

                $transactions = $this->model_accounts_bank_accounts_advanced->getTransactionHistory($account_id, $limit, $offset);

                $json['success'] = true;
                $json['transactions'] = $transactions;

            } catch (Exception $e) {
                $json['error'] = $e->getMessage();
            }
        } else {
            $json['error'] = 'معرف الحساب مطلوب';
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    public function getBalanceHistory() {
        $this->load->model('accounts/bank_accounts_advanced');

        $json = array();

        if (isset($this->request->get['account_id'])) {
            try {
                $account_id = $this->request->get['account_id'];
                $period = $this->request->get['period'] ?? '90'; // آخر 90 يوم افتراضياً

                $balance_history = $this->model_accounts_bank_accounts_advanced->getBalanceHistory($account_id, $period);

                $json['success'] = true;
                $json['balance_history'] = $balance_history;

            } catch (Exception $e) {
                $json['error'] = $e->getMessage();
            }
        } else {
            $json['error'] = 'معرف الحساب مطلوب';
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * تكامل مع البنوك المصرية
     */
    public function syncWithEgyptianBanks() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('modify', 'accounts/bank_accounts_advanced') ||
            !$this->user->hasKey('accounting_bank_integration')) {

            $this->central_service->logActivity('unauthorized_access', 'accounts',
                'محاولة مزامنة البنوك المصرية غير مصرح بها', [
                'user_id' => $this->user->getId(),
                'ip_address' => $this->request->server['REMOTE_ADDR']
            ]);

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/bank_accounts_advanced');
        $this->load->model('accounts/bank_accounts_advanced');

        if ($this->request->server['REQUEST_METHOD'] == 'POST') {
            try {
                $bank_code = $this->request->post['bank_code'];
                $account_number = $this->request->post['account_number'];

                // مزامنة مع البنك المصري
                $sync_result = $this->model_accounts_bank_accounts_advanced->syncWithEgyptianBank($bank_code, $account_number);

                // تسجيل المزامنة
                $this->central_service->logActivity('bank_sync', 'accounts',
                    'مزامنة مع البنك المصري: ' . $bank_code, [
                    'user_id' => $this->user->getId(),
                    'bank_code' => $bank_code,
                    'account_number' => $account_number,
                    'sync_status' => $sync_result['status']
                ]);

                // إرسال إشعار بنتيجة المزامنة
                $this->central_service->sendNotification(
                    'bank_sync_completed',
                    'اكتمال مزامنة البنك المصري',
                    'تم ' . ($sync_result['status'] == 'success' ? 'بنجاح' : 'بفشل') . ' مزامنة الحساب ' . $account_number . ' مع البنك ' . $bank_code,
                    [$this->config->get('config_cfo_id'), $this->config->get('config_chief_accountant_id')],
                    [
                        'bank_code' => $bank_code,
                        'account_number' => $account_number,
                        'sync_status' => $sync_result['status'],
                        'transactions_synced' => $sync_result['transactions_count'] ?? 0,
                        'synced_by' => $this->user->getFirstName() . ' ' . $this->user->getLastName()
                    ]
                );

                if ($sync_result['status'] == 'success') {
                    $this->session->data['success'] = 'تم مزامنة البيانات مع البنك بنجاح!';
                } else {
                    $this->error['warning'] = 'فشل في مزامنة البيانات: ' . $sync_result['message'];
                }

            } catch (Exception $e) {
                $this->error['warning'] = 'خطأ في المزامنة: ' . $e->getMessage();
            }
        }

        // عرض نموذج المزامنة
        $data['egyptian_banks'] = $this->model_accounts_bank_accounts_advanced->getEgyptianBanks();
        $data['user_accounts'] = $this->model_accounts_bank_accounts_advanced->getUserBankAccounts();

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/bank_sync_form', $data));
    }

    public function export() {
        $this->load->language('accounts/bank_accounts_advanced');
        $this->load->model('accounts/bank_accounts_advanced');

        $format = $this->request->get['format'] ?? 'excel';
        $filter_data = $this->prepareFilterData();

        $accounts_data = $this->model_accounts_bank_accounts_advanced->getAccountsForExport($filter_data);

        // تسجيل التصدير
        $this->model_accounts_audit_trail->logAction([
            'action_type' => 'export_bank_accounts',
            'table_name' => 'bank_accounts',
            'record_id' => 0,
            'description' => "تصدير الحسابات المصرفية بصيغة {$format}",
            'module' => 'bank_accounts'
        ]);

        switch ($format) {
            case 'excel':
                $this->exportToExcel($accounts_data);
                break;
            case 'pdf':
                $this->exportToPdf($accounts_data);
                break;
            case 'csv':
                $this->exportToCsv($accounts_data);
                break;
            default:
                $this->exportToExcel($accounts_data);
        }
    }

    protected function getList() {
        if (isset($this->request->get['sort'])) {
            $sort = $this->request->get['sort'];
        } else {
            $sort = 'account_name';
        }

        if (isset($this->request->get['order'])) {
            $order = $this->request->get['order'];
        } else {
            $order = 'ASC';
        }

        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }

        $url = '';

        if (isset($this->request->get['sort'])) {
            $url .= '&sort=' . $this->request->get['sort'];
        }

        if (isset($this->request->get['order'])) {
            $url .= '&order=' . $this->request->get['order'];
        }

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        $data['breadcrumbs'] = array();

        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );

        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('accounts/bank_accounts_advanced', 'user_token=' . $this->session->data['user_token'] . $url, true)
        );

        $data['add'] = $this->url->link('accounts/bank_accounts_advanced/add', 'user_token=' . $this->session->data['user_token'] . $url, true);
        $data['delete'] = $this->url->link('accounts/bank_accounts_advanced/delete', 'user_token=' . $this->session->data['user_token'] . $url, true);
        $data['reconcile'] = $this->url->link('accounts/bank_accounts_advanced/reconcile', 'user_token=' . $this->session->data['user_token'], true);
        $data['transfer'] = $this->url->link('accounts/bank_accounts_advanced/transfer', 'user_token=' . $this->session->data['user_token'], true);

        // URLs للـ AJAX
        $data['analysis_url'] = $this->url->link('accounts/bank_accounts_advanced/getAccountAnalysis', 'user_token=' . $this->session->data['user_token'], true);
        $data['cash_flow_url'] = $this->url->link('accounts/bank_accounts_advanced/getCashFlow', 'user_token=' . $this->session->data['user_token'], true);
        $data['transactions_url'] = $this->url->link('accounts/bank_accounts_advanced/getTransactionHistory', 'user_token=' . $this->session->data['user_token'], true);
        $data['balance_history_url'] = $this->url->link('accounts/bank_accounts_advanced/getBalanceHistory', 'user_token=' . $this->session->data['user_token'], true);

        $data['accounts'] = array();

        $filter_data = array(
            'sort'  => $sort,
            'order' => $order,
            'start' => ($page - 1) * $this->config->get('config_limit_admin'),
            'limit' => $this->config->get('config_limit_admin')
        );

        $account_total = $this->model_accounts_bank_accounts_advanced->getTotalBankAccounts();

        $results = $this->model_accounts_bank_accounts_advanced->getBankAccounts($filter_data);

        foreach ($results as $result) {
            $data['accounts'][] = array(
                'account_id'        => $result['account_id'],
                'account_number'    => $result['account_number'],
                'account_name'      => $result['account_name'],
                'bank_name'         => $result['bank_name'],
                'branch_name'       => $result['branch_name'],
                'currency'          => $result['currency'],
                'current_balance'   => $this->currency->format($result['current_balance'], $result['currency']),
                'status'            => $result['status'],
                'last_reconciled'   => $result['last_reconciled'] ? date($this->language->get('date_format_short'), strtotime($result['last_reconciled'])) : 'لم يتم',
                'edit'              => $this->url->link('accounts/bank_accounts_advanced/edit', 'user_token=' . $this->session->data['user_token'] . '&account_id=' . $result['account_id'] . $url, true),
                'reconcile'         => $this->url->link('accounts/bank_accounts_advanced/reconcile', 'user_token=' . $this->session->data['user_token'] . '&account_id=' . $result['account_id'], true)
            );
        }

        $data['user_token'] = $this->session->data['user_token'];

        if (isset($this->error['warning'])) {
            $data['error_warning'] = $this->error['warning'];
        } else {
            $data['error_warning'] = '';
        }

        if (isset($this->session->data['success'])) {
            $data['success'] = $this->session->data['success'];

            unset($this->session->data['success']);
        } else {
            $data['success'] = '';
        }

        if (isset($this->request->post['selected'])) {
            $data['selected'] = (array)$this->request->post['selected'];
        } else {
            $data['selected'] = array();
        }

        $url = '';

        if ($order == 'ASC') {
            $url .= '&order=DESC';
        } else {
            $url .= '&order=ASC';
        }

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        $data['sort_account_name'] = $this->url->link('accounts/bank_accounts_advanced', 'user_token=' . $this->session->data['user_token'] . '&sort=account_name' . $url, true);
        $data['sort_bank_name'] = $this->url->link('accounts/bank_accounts_advanced', 'user_token=' . $this->session->data['user_token'] . '&sort=bank_name' . $url, true);
        $data['sort_current_balance'] = $this->url->link('accounts/bank_accounts_advanced', 'user_token=' . $this->session->data['user_token'] . '&sort=current_balance' . $url, true);
        $data['sort_status'] = $this->url->link('accounts/bank_accounts_advanced', 'user_token=' . $this->session->data['user_token'] . '&sort=status' . $url, true);

        $url = '';

        if (isset($this->request->get['sort'])) {
            $url .= '&sort=' . $this->request->get['sort'];
        }

        if (isset($this->request->get['order'])) {
            $url .= '&order=' . $this->request->get['order'];
        }

        $pagination = new Pagination();
        $pagination->total = $account_total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit_admin');
        $pagination->url = $this->url->link('accounts/bank_accounts_advanced', 'user_token=' . $this->session->data['user_token'] . $url . '&page={page}', true);

        $data['pagination'] = $pagination->render();

        $data['results'] = sprintf($this->language->get('text_pagination'), ($account_total) ? (($page - 1) * $this->config->get('config_limit_admin')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit_admin')) > ($account_total - $this->config->get('config_limit_admin'))) ? $account_total : ((($page - 1) * $this->config->get('config_limit_admin')) + $this->config->get('config_limit_admin')), $account_total, ceil($account_total / $this->config->get('config_limit_admin')));

        $data['sort'] = $sort;
        $data['order'] = $order;

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/bank_accounts_advanced_list', $data));
    }

    /**
     * التحقق من صحة النموذج
     */
    protected function validateForm() {
        if (!$this->user->hasPermission('modify', 'accounts/bank_accounts_advanced')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }

        if ((utf8_strlen($this->request->post['account_name']) < 3) || (utf8_strlen($this->request->post['account_name']) > 64)) {
            $this->error['account_name'] = $this->language->get('error_account_name');
        }

        if (empty($this->request->post['bank_name'])) {
            $this->error['bank_name'] = $this->language->get('error_bank_name');
        }

        if (empty($this->request->post['account_number'])) {
            $this->error['account_number'] = $this->language->get('error_account_number');
        }

        return !$this->error;
    }

    /**
     * التحقق من صحة الحذف
     */
    protected function validateDelete() {
        if (!$this->user->hasPermission('modify', 'accounts/bank_accounts_advanced')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }

        return !$this->error;
    }

    /**
     * إعداد النموذج
     */
    protected function getForm() {
        $data = array();

        if (isset($this->error['warning'])) {
            $data['error_warning'] = $this->error['warning'];
        } else {
            $data['error_warning'] = '';
        }

        if (isset($this->error['account_name'])) {
            $data['error_account_name'] = $this->error['account_name'];
        } else {
            $data['error_account_name'] = '';
        }

        $url = '';

        if (isset($this->request->get['sort'])) {
            $url .= '&sort=' . $this->request->get['sort'];
        }

        if (isset($this->request->get['order'])) {
            $url .= '&order=' . $this->request->get['order'];
        }

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        $data['breadcrumbs'] = array();

        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );

        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('accounts/bank_accounts_advanced', 'user_token=' . $this->session->data['user_token'] . $url, true)
        );

        if (!isset($this->request->get['account_id'])) {
            $data['action'] = $this->url->link('accounts/bank_accounts_advanced/add', 'user_token=' . $this->session->data['user_token'] . $url, true);
        } else {
            $data['action'] = $this->url->link('accounts/bank_accounts_advanced/edit', 'user_token=' . $this->session->data['user_token'] . '&account_id=' . $this->request->get['account_id'] . $url, true);
        }

        $data['cancel'] = $this->url->link('accounts/bank_accounts_advanced', 'user_token=' . $this->session->data['user_token'] . $url, true);

        if (isset($this->request->get['account_id']) && ($this->request->server['REQUEST_METHOD'] != 'POST')) {
            $account_info = $this->model_accounts_bank_accounts_advanced->getBankAccount($this->request->get['account_id']);
        }

        if (isset($this->request->post['account_name'])) {
            $data['account_name'] = $this->request->post['account_name'];
        } elseif (!empty($account_info)) {
            $data['account_name'] = $account_info['account_name'];
        } else {
            $data['account_name'] = '';
        }

        if (isset($this->request->post['bank_name'])) {
            $data['bank_name'] = $this->request->post['bank_name'];
        } elseif (!empty($account_info)) {
            $data['bank_name'] = $account_info['bank_name'];
        } else {
            $data['bank_name'] = '';
        }

        if (isset($this->request->post['account_number'])) {
            $data['account_number'] = $this->request->post['account_number'];
        } elseif (!empty($account_info)) {
            $data['account_number'] = $account_info['account_number'];
        } else {
            $data['account_number'] = '';
        }

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/bank_accounts_advanced_form', $data));
    }

    /**
     * دالة تنظيف المخرجات (CONSTITUTIONAL REQUIREMENT)
     * Sanitize all output data to prevent XSS attacks
     */
    private function sanitizeOutputData($data) {
        if (is_array($data)) {
            foreach ($data as $key => $value) {
                $data[$key] = $this->sanitizeOutputData($value);
            }
        } elseif (is_string($data)) {
            $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
        }
        return $data;
    }

    /**
     * Enterprise Grade Plus Security Enhancements
     */
    private function validateInputData($data) {
        $validated = array();

        if (isset($data['account_name'])) {
            $validated['account_name'] = trim(strip_tags($data['account_name']));
        }

        if (isset($data['bank_name'])) {
            $validated['bank_name'] = trim(strip_tags($data['bank_name']));
        }

        if (isset($data['account_number'])) {
            $validated['account_number'] = preg_replace('/[^0-9]/', '', $data['account_number']);
        }

        if (isset($data['iban'])) {
            $validated['iban'] = strtoupper(preg_replace('/[^A-Z0-9]/', '', $data['iban']));
        }

        if (isset($data['balance'])) {
            $validated['balance'] = (float)$data['balance'];
        }

        return $validated;
    }

    /**
     * Rate Limiting Implementation
     */
    private function checkRateLimit() {
        $ip = $this->request->server['REMOTE_ADDR'];
        $user_id = $this->user->getId();

        return $this->central_service->checkRateLimit('bank_operations', $ip, $user_id, 50, 3600); // 50 requests per hour
    }

    /**
     * Performance Optimization - Memory Management
     */
    private function optimizeMemoryUsage() {
        ini_set('memory_limit', '256M');
        ini_set('max_execution_time', 120); // 2 minutes for bank operations
    }
}
?>