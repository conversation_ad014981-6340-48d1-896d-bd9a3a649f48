{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <a href="{{ export_excel }}" data-toggle="tooltip" title="{{ button_export_excel }}" class="btn btn-success"><i class="fa fa-file-excel-o"></i></a>
        <a href="{{ export_pdf }}" data-toggle="tooltip" title="{{ button_export_pdf }}" class="btn btn-danger"><i class="fa fa-file-pdf-o"></i></a>
        <a href="{{ export_csv }}" data-toggle="tooltip" title="{{ button_export_csv }}" class="btn btn-info"><i class="fa fa-file-text-o"></i></a>
        <a href="{{ print }}" data-toggle="tooltip" title="{{ button_print }}" class="btn btn-warning"><i class="fa fa-print"></i></a>
        <a href="{{ back }}" data-toggle="tooltip" title="{{ button_back }}" class="btn btn-default"><i class="fa fa-reply"></i></a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if success %}
    <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    
    <!-- معلومات التقرير -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-info-circle"></i> {{ text_report_info }}</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-md-6">
            <strong>{{ text_period }}:</strong> {{ filter.date_start }} {{ text_to }} {{ filter.date_end }}<br>
            <strong>{{ text_generated_on }}:</strong> {{ generated_date }}<br>
            <strong>{{ text_generated_by }}:</strong> {{ generated_by }}
          </div>
          <div class="col-md-6">
            {% if filter.account_name %}
            <strong>{{ text_account }}:</strong> {{ filter.account_name }}<br>
            {% endif %}
            {% if filter.account_type_name %}
            <strong>{{ text_account_type }}:</strong> {{ filter.account_type_name }}<br>
            {% endif %}
            {% if filter.cost_center_name %}
            <strong>{{ text_cost_center }}:</strong> {{ filter.cost_center_name }}<br>
            {% endif %}
          </div>
        </div>
      </div>
    </div>

    <!-- ملخص دفتر الأستاذ -->
    <div class="panel panel-primary">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-bar-chart"></i> {{ text_summary }}</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-md-3">
            <div class="info-box bg-aqua">
              <span class="info-box-icon"><i class="fa fa-list"></i></span>
              <div class="info-box-content">
                <span class="info-box-text">{{ text_total_accounts }}</span>
                <span class="info-box-number">{{ summary.total_accounts }}</span>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="info-box bg-green">
              <span class="info-box-icon"><i class="fa fa-arrow-up"></i></span>
              <div class="info-box-content">
                <span class="info-box-text">{{ text_total_debit }}</span>
                <span class="info-box-number">{{ summary.total_debit }}</span>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="info-box bg-yellow">
              <span class="info-box-icon"><i class="fa fa-arrow-down"></i></span>
              <div class="info-box-content">
                <span class="info-box-text">{{ text_total_credit }}</span>
                <span class="info-box-number">{{ summary.total_credit }}</span>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="info-box bg-red">
              <span class="info-box-icon"><i class="fa fa-balance-scale"></i></span>
              <div class="info-box-content">
                <span class="info-box-text">{{ text_balance_difference }}</span>
                <span class="info-box-number">{{ summary.balance_difference }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- دفتر الأستاذ العام -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-book"></i> {{ text_general_ledger_details }}</h3>
      </div>
      <div class="panel-body">
        <div class="table-responsive">
          <table class="table table-bordered table-hover" id="general-ledger-table">
            <thead>
              <tr>
                <th>{{ column_code }}</th>
                <th>{{ column_account_name }}</th>
                <th>{{ column_account_type }}</th>
                <th class="text-right">{{ column_opening_balance }}</th>
                <th class="text-right">{{ column_total_debit }}</th>
                <th class="text-right">{{ column_total_credit }}</th>
                <th class="text-right">{{ column_net_movement }}</th>
                <th class="text-right">{{ column_closing_balance }}</th>
                <th class="text-center">{{ column_transactions }}</th>
                <th class="text-center">{{ column_action }}</th>
              </tr>
            </thead>
            <tbody>
              {% for account in general_ledger %}
              <tr{% if account.closing_balance|replace({',': ''})|number_format == '0.00' %} class="text-muted"{% endif %}>
                <td><strong>{{ account.code }}</strong></td>
                <td>{{ account.name }}</td>
                <td>
                  <span class="label label-{% if account.type == 'asset' %}primary{% elseif account.type == 'liability' %}danger{% elseif account.type == 'equity' %}warning{% elseif account.type == 'revenue' %}success{% else %}info{% endif %}">
                    {{ account.type_name }}
                  </span>
                </td>
                <td class="text-right">{{ account.opening_balance }}</td>
                <td class="text-right text-success">{{ account.total_debit }}</td>
                <td class="text-right text-danger">{{ account.total_credit }}</td>
                <td class="text-right{% if account.net_movement|replace({',': ''})|number_format > 0 %} text-success{% elseif account.net_movement|replace({',': ''})|number_format < 0 %} text-danger{% endif %}">{{ account.net_movement }}</td>
                <td class="text-right"><strong>{{ account.closing_balance }}</strong></td>
                <td class="text-center">
                  <span class="badge">{{ account.transaction_count }}</span>
                </td>
                <td class="text-center">
                  <a href="{{ account.view_transactions }}" class="btn btn-xs btn-info" data-toggle="tooltip" title="{{ button_view_transactions }}"><i class="fa fa-eye"></i></a>
                  <a href="{{ account.account_statement }}" class="btn btn-xs btn-primary" data-toggle="tooltip" title="{{ button_account_statement }}"><i class="fa fa-file-text"></i></a>
                </td>
              </tr>
              {% endfor %}
            </tbody>
            <tfoot>
              <tr class="info">
                <th colspan="3">{{ text_total }}</th>
                <th class="text-right">{{ summary.total_opening_balance }}</th>
                <th class="text-right">{{ summary.total_debit }}</th>
                <th class="text-right">{{ summary.total_credit }}</th>
                <th class="text-right">{{ summary.total_net_movement }}</th>
                <th class="text-right">{{ summary.total_closing_balance }}</th>
                <th class="text-center">{{ summary.total_transactions }}</th>
                <th></th>
              </tr>
            </tfoot>
          </table>
        </div>
      </div>
    </div>

    <!-- توزيع الحسابات حسب النوع -->
    {% if summary.accounts_by_type %}
    <div class="panel panel-info">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pie-chart"></i> {{ text_accounts_by_type }}</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-md-6">
            <canvas id="accounts-type-chart" width="400" height="200"></canvas>
          </div>
          <div class="col-md-6">
            <table class="table table-striped">
              <thead>
                <tr>
                  <th>{{ column_account_type }}</th>
                  <th class="text-right">{{ column_count }}</th>
                  <th class="text-right">{{ column_percentage }}</th>
                </tr>
              </thead>
              <tbody>
                {% for type, count in summary.accounts_by_type %}
                <tr>
                  <td>{{ type_names[type] }}</td>
                  <td class="text-right">{{ count }}</td>
                  <td class="text-right">{{ ((count / summary.total_accounts) * 100)|number_format(1) }}%</td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
    {% endif %}

    <!-- معلومات الامتثال -->
    <div class="panel panel-success">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-check-circle"></i> {{ text_compliance_info }}</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-md-4">
            <div class="alert alert-success">
              <i class="fa fa-check"></i> {{ text_eas_compliant }}
            </div>
          </div>
          <div class="col-md-4">
            <div class="alert alert-info">
              <i class="fa fa-globe"></i> {{ text_eta_ready }}
            </div>
          </div>
          <div class="col-md-4">
            <div class="alert alert-warning">
              <i class="fa fa-balance-scale"></i> {{ text_egyptian_gaap }}
            </div>
          </div>
        </div>
      </div>
    </div>

  </div>
</div>

<script type="text/javascript"><!--
// DataTables initialization
$('#general-ledger-table').DataTable({
    "language": {
        "url": "{{ datatable_language }}"
    },
    "order": [[ 0, "asc" ]],
    "columnDefs": [
        { "orderable": false, "targets": 9 }
    ],
    "pageLength": 25,
    "dom": 'Bfrtip',
    "buttons": [
        'copy', 'csv', 'excel', 'pdf', 'print'
    ]
});

// Accounts by type chart
{% if summary.accounts_by_type %}
var ctx = document.getElementById('accounts-type-chart').getContext('2d');
var accountsTypeChart = new Chart(ctx, {
    type: 'doughnut',
    data: {
        labels: [{% for type, count in summary.accounts_by_type %}'{{ type_names[type] }}'{% if not loop.last %},{% endif %}{% endfor %}],
        datasets: [{
            data: [{% for type, count in summary.accounts_by_type %}{{ count }}{% if not loop.last %},{% endif %}{% endfor %}],
            backgroundColor: [
                '#3c8dbc', '#dd4b39', '#f39c12', '#00a65a', '#605ca8'
            ]
        }]
    },
    options: {
        responsive: true,
        legend: {
            position: 'bottom'
        }
    }
});
{% endif %}

// Tooltips
$('[data-toggle="tooltip"]').tooltip();
//--></script>

{{ footer }}
