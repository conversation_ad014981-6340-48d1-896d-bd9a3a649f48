<?php
/**
 * تحكم التقارير المالية المتقدمة والمتكاملة
 * يجمع بين القوة والبساطة - يتفوق على SAP وOracle في سهولة الاستخدام
 * مع الاحتفاظ بالقوة والمرونة المطلوبة للشركات الكبيرة
 * متوافق مع معايير المحاسبة المصرية ومتطلبات ETA
 */
class ControllerAccountsFinancialReportsAdvanced extends Controller {
    private $error = array();
    private $central_service;

    public function __construct($registry) {
        parent::__construct($registry);

        // تحميل الخدمات المركزية
        $this->load->model('core/central_service_manager');
        $this->central_service = $this->model_core_central_service_manager;
    }

    public function index() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/financial_reports_advanced') ||
            !$this->user->hasKey('accounting_financial_reports_view')) {

            $this->central_service->logActivity('unauthorized_access', 'accounts',
                $this->language->get('log_unauthorized_access_financial_reports'), [
                'user_id' => $this->user->getId(),
                'ip_address' => $this->request->server['REMOTE_ADDR']
            ]);

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/financial_reports_advanced');
        $this->document->setTitle($this->language->get('heading_title'));
        $this->load->model('accounts/financial_reports_advanced');
        $this->load->model('accounts/audit_trail');

        // إضافة CSS و JavaScript المتقدم
        $this->document->addStyle('view/stylesheet/accounts/financial_reports.css');
        $this->document->addScript('view/javascript/accounts/financial_reports.js');
        $this->document->addScript('view/javascript/jquery/accounting.min.js');
        $this->document->addScript('view/javascript/jquery/select2.min.js');
        $this->document->addStyle('view/javascript/jquery/select2.min.css');
        $this->document->addScript('view/javascript/jquery/daterangepicker.min.js');
        $this->document->addStyle('view/javascript/jquery/daterangepicker.css');
        $this->document->addScript('view/javascript/jquery/chart.min.js');
        $this->document->addScript('view/javascript/jquery/datatables.min.js');
        $this->document->addStyle('view/javascript/jquery/datatables.min.css');

        // تسجيل الوصول للشاشة
        $this->central_service->logActivity('view', 'accounts',
            $this->language->get('log_view_financial_reports_screen'), [
            'user_id' => $this->user->getId(),
            'screen' => 'accounts/financial_reports_advanced'
        ]);

        $this->getDashboard();
    }

    public function generateReport() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('modify', 'accounts/financial_reports_advanced') ||
            !$this->user->hasKey('accounting_financial_reports_generate')) {

            $this->central_service->logActivity('unauthorized_generate', 'accounts',
                'محاولة إنشاء تقرير مالي غير مصرح بها', [
                'user_id' => $this->user->getId(),
                'action' => 'generate_financial_report'
            ]);

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/financial_reports');
        $this->load->model('accounts/financial_reports_advanced');
        $this->load->model('accounts/audit_trail');

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm()) {
            try {
                $filter_data = $this->prepareFilterData();

                // تسجيل إنشاء التقرير
                $this->central_service->logActivity('generate_financial_report', 'accounts',
                    'إنشاء تقرير مالي: ' . $filter_data['report_type'] . ' للفترة: ' . $filter_data['date_start'] . ' إلى ' . $filter_data['date_end'], [
                    'user_id' => $this->user->getId(),
                    'report_type' => $filter_data['report_type'],
                    'date_start' => $filter_data['date_start'],
                    'date_end' => $filter_data['date_end']
                ]);

                // إرسال إشعار للإدارة العليا
                $this->central_service->sendNotification(
                    'financial_report_generated',
                    'إنشاء تقرير مالي متقدم',
                    'تم إنشاء تقرير مالي متقدم "' . $filter_data['report_type'] . '" بواسطة ' . $this->user->getFirstName(),
                    [$this->config->get('config_chief_accountant_id'), $this->config->get('config_ceo_id')],
                    [
                        'report_type' => $filter_data['report_type'],
                        'user_name' => $this->user->getFirstName() . ' ' . $this->user->getLastName(),
                        'period' => $filter_data['date_start'] . ' إلى ' . $filter_data['date_end']
                    ]
                );

                // إنشاء التقرير المالي
                $report_data = $this->model_accounts_financial_reports_advanced->generateFinancialReport($filter_data);

                // التحقق من وجود بيانات
                if (empty($report_data['data'])) {
                    $this->session->data['warning'] = 'لا توجد بيانات في الفترة المحددة';
                }

                $this->session->data['report_data'] = $report_data;
                $this->session->data['filter_data'] = $filter_data;

                $this->response->redirect($this->url->link('accounts/financial_reports_advanced/view', 'user_token=' . $this->session->data['user_token'], true));

            } catch (Exception $e) {
                $this->error['warning'] = 'خطأ في إنشاء التقرير المالي: ' . $e->getMessage();

                // تسجيل الخطأ
                $this->model_accounts_audit_trail->logAction([
                    'action_type' => 'generate_financial_report_failed',
                    'table_name' => 'financial_reports',
                    'record_id' => 0,
                    'description' => 'فشل في إنشاء التقرير المالي: ' . $e->getMessage(),
                    'module' => 'financial_reports'
                ]);
            }
        }

        $this->getDashboard();
    }

    public function view() {
        $this->load->language('accounts/financial_reports');
        $this->document->setTitle($this->language->get('heading_title') . ' - ' . $this->language->get('text_view'));

        if (!isset($this->session->data['report_data'])) {
            $this->session->data['error'] = 'لا توجد بيانات تقرير مالي للعرض';
            $this->response->redirect($this->url->link('accounts/financial_reports_advanced', 'user_token=' . $this->session->data['user_token'], true));
        }

        $data = $this->prepareViewData();

        $this->response->setOutput($this->load->view('accounts/financial_reports_view', $data));
    }

    public function export() {
        $this->load->language('accounts/financial_reports');
        $this->load->model('accounts/financial_reports_advanced');
        $this->load->model('accounts/audit_trail');

        if (!isset($this->session->data['report_data'])) {
            $this->session->data['error'] = 'لا توجد بيانات للتصدير';
            $this->response->redirect($this->url->link('accounts/financial_reports_advanced', 'user_token=' . $this->session->data['user_token'], true));
        }

        $format = $this->request->get['format'] ?? 'excel';
        $report_data = $this->session->data['report_data'];
        $filter_data = $this->session->data['filter_data'];

        // تسجيل التصدير
        $this->model_accounts_audit_trail->logAction([
            'action_type' => 'export_financial_report',
            'table_name' => 'financial_reports',
            'record_id' => 0,
            'description' => "تصدير التقرير المالي بصيغة {$format}",
            'module' => 'financial_reports'
        ]);

        switch ($format) {
            case 'excel':
                $this->exportToExcel($report_data, $filter_data);
                break;
            case 'pdf':
                $this->exportToPdf($report_data, $filter_data);
                break;
            case 'csv':
                $this->exportToCsv($report_data, $filter_data);
                break;
            default:
                $this->exportToExcel($report_data, $filter_data);
        }
    }

    public function getFinancialRatios() {
        $this->load->model('accounts/financial_reports_advanced');

        $json = array();

        if (isset($this->session->data['filter_data'])) {
            try {
                $filter_data = $this->session->data['filter_data'];

                $ratios = $this->model_accounts_financial_reports_advanced->calculateFinancialRatios($filter_data);

                $json['success'] = true;
                $json['ratios'] = $ratios;

            } catch (Exception $e) {
                $json['error'] = $e->getMessage();
            }
        } else {
            $json['error'] = 'لا توجد بيانات لحساب النسب المالية';
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    public function getFinancialAnalysis() {
        $this->load->model('accounts/financial_reports_advanced');

        $json = array();

        if (isset($this->session->data['report_data']) && isset($this->session->data['filter_data'])) {
            try {
                $report_data = $this->session->data['report_data'];
                $filter_data = $this->session->data['filter_data'];

                $analysis = $this->model_accounts_financial_reports_advanced->analyzeFinancialData($report_data, $filter_data);

                $json['success'] = true;
                $json['analysis'] = $analysis;

            } catch (Exception $e) {
                $json['error'] = $e->getMessage();
            }
        } else {
            $json['error'] = 'لا توجد بيانات للتحليل المالي';
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    public function getPerformanceIndicators() {
        $this->load->model('accounts/financial_reports_advanced');

        $json = array();

        if (isset($this->session->data['filter_data'])) {
            try {
                $filter_data = $this->session->data['filter_data'];

                $kpis = $this->model_accounts_financial_reports_advanced->calculateKPIs($filter_data);

                $json['success'] = true;
                $json['kpis'] = $kpis;

            } catch (Exception $e) {
                $json['error'] = $e->getMessage();
            }
        } else {
            $json['error'] = 'لا توجد بيانات لحساب مؤشرات الأداء';
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    public function getTrendAnalysis() {
        $this->load->model('accounts/financial_reports_advanced');

        $json = array();

        if (isset($this->session->data['filter_data'])) {
            try {
                $filter_data = $this->session->data['filter_data'];

                $trends = $this->model_accounts_financial_reports_advanced->analyzeTrends($filter_data);

                $json['success'] = true;
                $json['trends'] = $trends;

            } catch (Exception $e) {
                $json['error'] = $e->getMessage();
            }
        } else {
            $json['error'] = 'لا توجد بيانات لتحليل الاتجاهات';
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    public function getBenchmarkAnalysis() {
        $this->load->model('accounts/financial_reports_advanced');

        $json = array();

        if (isset($this->session->data['filter_data'])) {
            try {
                $filter_data = $this->session->data['filter_data'];

                $benchmark = $this->model_accounts_financial_reports_advanced->benchmarkAnalysis($filter_data);

                $json['success'] = true;
                $json['benchmark'] = $benchmark;

            } catch (Exception $e) {
                $json['error'] = $e->getMessage();
            }
        } else {
            $json['error'] = 'لا توجد بيانات للمقارنة المرجعية';
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    public function getVarianceAnalysis() {
        $this->load->model('accounts/financial_reports_advanced');

        $json = array();

        if (isset($this->session->data['filter_data'])) {
            try {
                $filter_data = $this->session->data['filter_data'];

                $variance = $this->model_accounts_financial_reports_advanced->varianceAnalysis($filter_data);

                $json['success'] = true;
                $json['variance'] = $variance;

            } catch (Exception $e) {
                $json['error'] = $e->getMessage();
            }
        } else {
            $json['error'] = 'لا توجد بيانات لتحليل الانحرافات';
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    public function getSegmentAnalysis() {
        $this->load->model('accounts/financial_reports_advanced');

        $json = array();

        if (isset($this->session->data['filter_data'])) {
            try {
                $filter_data = $this->session->data['filter_data'];

                $segments = $this->model_accounts_financial_reports_advanced->segmentAnalysis($filter_data);

                $json['success'] = true;
                $json['segments'] = $segments;

            } catch (Exception $e) {
                $json['error'] = $e->getMessage();
            }
        } else {
            $json['error'] = 'لا توجد بيانات لتحليل القطاعات';
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    protected function getDashboard() {
        $data['breadcrumbs'] = array();
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('accounts/financial_reports_advanced', 'user_token=' . $this->session->data['user_token'], true)
        );

        // URLs للإجراءات
        $data['action'] = $this->url->link('accounts/financial_reports_advanced/generateReport', 'user_token=' . $this->session->data['user_token'], true);
        $data['cancel'] = $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true);

        // URLs للـ AJAX
        $data['ratios_url'] = $this->url->link('accounts/financial_reports_advanced/getFinancialRatios', 'user_token=' . $this->session->data['user_token'], true);
        $data['analysis_url'] = $this->url->link('accounts/financial_reports_advanced/getFinancialAnalysis', 'user_token=' . $this->session->data['user_token'], true);
        $data['kpis_url'] = $this->url->link('accounts/financial_reports_advanced/getPerformanceIndicators', 'user_token=' . $this->session->data['user_token'], true);
        $data['trends_url'] = $this->url->link('accounts/financial_reports_advanced/getTrendAnalysis', 'user_token=' . $this->session->data['user_token'], true);
        $data['benchmark_url'] = $this->url->link('accounts/financial_reports_advanced/getBenchmarkAnalysis', 'user_token=' . $this->session->data['user_token'], true);
        $data['variance_url'] = $this->url->link('accounts/financial_reports_advanced/getVarianceAnalysis', 'user_token=' . $this->session->data['user_token'], true);
        $data['segments_url'] = $this->url->link('accounts/financial_reports_advanced/getSegmentAnalysis', 'user_token=' . $this->session->data['user_token'], true);

        // بيانات النموذج
        $fields = ['report_type', 'date_start', 'date_end', 'comparison_period', 'include_budget',
                   'currency', 'consolidation_level', 'segment_analysis', 'show_details'];

        foreach ($fields as $field) {
            if (isset($this->request->post[$field])) {
                $data[$field] = $this->request->post[$field];
            } else {
                $default_values = [
                    'report_type' => 'comprehensive',
                    'date_start' => date('Y-01-01'),
                    'date_end' => date('Y-m-d'),
                    'comparison_period' => 'previous_year',
                    'include_budget' => 0,
                    'currency' => $this->config->get('config_currency'),
                    'consolidation_level' => 'company',
                    'segment_analysis' => 0,
                    'show_details' => 1
                ];
                $data[$field] = $default_values[$field] ?? '';
            }
        }

        // أنواع التقارير
        $data['report_types'] = array(
            'comprehensive' => 'تقرير مالي شامل',
            'income_statement' => 'قائمة الدخل',
            'balance_sheet' => 'الميزانية العمومية',
            'cash_flow' => 'قائمة التدفقات النقدية',
            'equity_changes' => 'قائمة التغير في حقوق الملكية',
            'financial_ratios' => 'النسب المالية',
            'performance_analysis' => 'تحليل الأداء'
        );

        // فترات المقارنة
        $data['comparison_periods'] = array(
            'none' => 'بدون مقارنة',
            'previous_month' => 'الشهر السابق',
            'previous_quarter' => 'الربع السابق',
            'previous_year' => 'السنة السابقة',
            'budget' => 'الموازنة التقديرية',
            'custom' => 'فترة مخصصة'
        );

        // مستويات التوحيد
        $data['consolidation_levels'] = array(
            'company' => 'مستوى الشركة',
            'division' => 'مستوى القسم',
            'department' => 'مستوى الإدارة',
            'cost_center' => 'مستوى مركز التكلفة'
        );

        // العملات المتاحة
        $this->load->model('localisation/currency');
        $data['currencies'] = $this->model_localisation_currency->getCurrencies();

        // الرسائل
        if (isset($this->error['warning'])) {
            $data['error_warning'] = $this->error['warning'];
        } else {
            $data['error_warning'] = '';
        }

        if (isset($this->session->data['success'])) {
            $data['success'] = $this->session->data['success'];
            unset($this->session->data['success']);
        } else {
            $data['success'] = '';
        }

        if (isset($this->session->data['warning'])) {
            $data['warning'] = $this->session->data['warning'];
            unset($this->session->data['warning']);
        } else {
            $data['warning'] = '';
        }

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/financial_reports_advanced_dashboard', $data));
    }

    protected function prepareFilterData() {
        return array(
            'report_type' => $this->request->post['report_type'] ?? 'comprehensive',
            'date_start' => $this->request->post['date_start'],
            'date_end' => $this->request->post['date_end'],
            'comparison_period' => $this->request->post['comparison_period'] ?? 'none',
            'include_budget' => isset($this->request->post['include_budget']) ? 1 : 0,
            'currency' => $this->request->post['currency'] ?? $this->config->get('config_currency'),
            'consolidation_level' => $this->request->post['consolidation_level'] ?? 'company',
            'segment_analysis' => isset($this->request->post['segment_analysis']) ? 1 : 0,
            'show_details' => isset($this->request->post['show_details']) ? 1 : 0
        );
    }

    protected function validateForm() {
        if (!$this->user->hasPermission('access', 'accounts/financial_reports')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }

        if (empty($this->request->post['date_start'])) {
            $this->error['date_start'] = 'تاريخ البداية مطلوب';
        }

        if (empty($this->request->post['date_end'])) {
            $this->error['date_end'] = 'تاريخ النهاية مطلوب';
        }

        if (!empty($this->request->post['date_start']) && !empty($this->request->post['date_end'])) {
            if (strtotime($this->request->post['date_start']) > strtotime($this->request->post['date_end'])) {
                $this->error['date_range'] = 'تاريخ البداية يجب أن يكون قبل تاريخ النهاية';
            }
        }

        return !$this->error;
    }

    protected function prepareViewData() {
        $data = array();

        $data['report_data'] = $this->session->data['report_data'];
        $data['filter_data'] = $this->session->data['filter_data'];

        // معلومات الشركة
        $data['company_name'] = $this->config->get('config_name');
        $data['company_address'] = $this->config->get('config_address');

        // معلومات التقرير
        $data['report_title'] = $this->language->get('heading_title');
        $data['report_date'] = date($this->language->get('date_format_long'));
        $data['generated_by'] = $this->user->getUserName();

        // URLs
        $data['export_excel'] = $this->url->link('accounts/financial_reports_advanced/export', 'user_token=' . $this->session->data['user_token'] . '&format=excel', true);
        $data['export_pdf'] = $this->url->link('accounts/financial_reports_advanced/export', 'user_token=' . $this->session->data['user_token'] . '&format=pdf', true);
        $data['print_url'] = $this->url->link('accounts/financial_reports_advanced/print', 'user_token=' . $this->session->data['user_token'], true);
        $data['back_url'] = $this->url->link('accounts/financial_reports_advanced', 'user_token=' . $this->session->data['user_token'], true);

        // URLs للـ AJAX
        $data['ratios_url'] = $this->url->link('accounts/financial_reports_advanced/getFinancialRatios', 'user_token=' . $this->session->data['user_token'], true);
        $data['analysis_url'] = $this->url->link('accounts/financial_reports_advanced/getFinancialAnalysis', 'user_token=' . $this->session->data['user_token'], true);
        $data['kpis_url'] = $this->url->link('accounts/financial_reports_advanced/getPerformanceIndicators', 'user_token=' . $this->session->data['user_token'], true);
        $data['trends_url'] = $this->url->link('accounts/financial_reports_advanced/getTrendAnalysis', 'user_token=' . $this->session->data['user_token'], true);
        $data['benchmark_url'] = $this->url->link('accounts/financial_reports_advanced/getBenchmarkAnalysis', 'user_token=' . $this->session->data['user_token'], true);
        $data['variance_url'] = $this->url->link('accounts/financial_reports_advanced/getVarianceAnalysis', 'user_token=' . $this->session->data['user_token'], true);
        $data['segments_url'] = $this->url->link('accounts/financial_reports_advanced/getSegmentAnalysis', 'user_token=' . $this->session->data['user_token'], true);

        $data['breadcrumbs'] = array();
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('accounts/financial_reports_advanced', 'user_token=' . $this->session->data['user_token'], true)
        );

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        return $data;
    }

    /**
     * نموذج إنشاء التقارير المالية المتقدمة
     */
    public function form() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('modify', 'accounts/financial_reports_advanced') ||
            !$this->user->hasKey('accounting_financial_reports_create')) {

            $this->central_service->logActivity('unauthorized_access', 'accounts',
                'محاولة إنشاء تقرير مالي غير مصرح بها', [
                'user_id' => $this->user->getId(),
                'ip_address' => $this->request->server['REMOTE_ADDR']
            ]);

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/financial_reports');
        $this->document->setTitle($this->language->get('heading_title'));

        // إضافة CSS و JavaScript المتقدم للنموذج
        $this->document->addStyle('view/stylesheet/accounts/financial_reports_form.css');
        $this->document->addScript('view/javascript/accounts/financial_reports_form.js');
        $this->document->addScript('view/javascript/jquery/select2.min.js');
        $this->document->addStyle('view/javascript/jquery/select2.min.css');
        $this->document->addScript('view/javascript/jquery/daterangepicker.min.js');
        $this->document->addStyle('view/javascript/jquery/daterangepicker.css');

        if ($this->request->server['REQUEST_METHOD'] == 'POST') {
            try {
                // تسجيل إنشاء التقرير
                $this->central_service->logActivity('generate_financial_report', 'accounts',
                    'إنشاء تقرير مالي متقدم: ' . $this->request->post['report_type'], [
                    'user_id' => $this->user->getId(),
                    'report_type' => $this->request->post['report_type'],
                    'date_start' => $this->request->post['date_start'],
                    'date_end' => $this->request->post['date_end']
                ]);

                // إنشاء التقرير
                $report_data = $this->model_accounts_financial_reports_advanced->generateFinancialReport($this->request->post);

                // إرسال إشعار للإدارة العليا
                $this->central_service->sendNotification(
                    'financial_report_generated',
                    'تقرير مالي متقدم جديد',
                    'تم إنشاء تقرير مالي متقدم (' . $this->request->post['report_type'] . ') للفترة ' . $this->request->post['date_start'] . ' إلى ' . $this->request->post['date_end'],
                    [$this->config->get('config_ceo_id'), $this->config->get('config_cfo_id'), $this->config->get('config_chief_accountant_id')],
                    [
                        'report_type' => $this->request->post['report_type'],
                        'date_start' => $this->request->post['date_start'],
                        'date_end' => $this->request->post['date_end'],
                        'net_income' => $report_data['summary']['net_income'] ?? 0,
                        'total_assets' => $report_data['summary']['total_assets'] ?? 0,
                        'generated_by' => $this->user->getFirstName() . ' ' . $this->user->getLastName()
                    ]
                );

                $this->session->data['success'] = $this->language->get('text_success');
                $this->session->data['report_data'] = $report_data;

                $this->response->redirect($this->url->link('accounts/financial_reports_advanced/view', 'user_token=' . $this->session->data['user_token'], true));
            } catch (Exception $e) {
                $this->error['warning'] = $e->getMessage();
            }
        }

        // إعداد البيانات الافتراضية
        $data['date_start'] = $this->request->post['date_start'] ?? date('Y-m-01');
        $data['date_end'] = $this->request->post['date_end'] ?? date('Y-m-d');

        // الحصول على مراكز التكلفة
        $this->load->model('accounts/cost_centers');
        $data['cost_centers'] = $this->model_accounts_cost_centers->getCostCenters();

        // الحصول على الفروع
        $this->load->model('setting/store');
        $data['branches'] = $this->model_setting_store->getStores();

        $data['breadcrumbs'] = array();
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('accounts/financial_reports_advanced', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_generate_report'),
            'href' => $this->url->link('accounts/financial_reports_advanced/form', 'user_token=' . $this->session->data['user_token'], true)
        );

        $data['action'] = $this->url->link('accounts/financial_reports_advanced/form', 'user_token=' . $this->session->data['user_token'], true);
        $data['cancel'] = $this->url->link('accounts/financial_reports_advanced', 'user_token=' . $this->session->data['user_token'], true);
        $data['dashboard'] = $this->url->link('accounts/financial_reports_advanced/dashboard', 'user_token=' . $this->session->data['user_token'], true);
        $data['preview_url'] = $this->url->link('accounts/financial_reports_advanced/preview', 'user_token=' . $this->session->data['user_token'], true);

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/financial_reports_advanced_form', $data));
    }

    /**
     * طباعة التقرير المالي المتقدم
     */
    public function print() {
        $this->load->language('accounts/financial_reports');

        if (!isset($this->session->data['report_data'])) {
            $this->response->redirect($this->url->link('accounts/financial_reports_advanced', 'user_token=' . $this->session->data['user_token'], true));
            return;
        }

        $data = $this->session->data['report_data'];
        $data['company_name'] = $this->config->get('config_name');
        $data['generated_date'] = date('Y-m-d H:i:s');
        $data['generated_by'] = $this->user->getFirstName() . ' ' . $this->user->getLastName();

        $this->response->setOutput($this->load->view('accounts/financial_reports_advanced_print', $data));
    }

    /**
     * معاينة سريعة للتقرير
     */
    public function preview() {
        $this->load->language('accounts/financial_reports');
        $this->load->model('accounts/financial_reports_advanced');

        $json = array();

        try {
            // إنشاء معاينة مبسطة
            $preview_data = $this->model_accounts_financial_reports_advanced->generateQuickPreview($this->request->post);

            $preview_html = '<div class="row">';
            $preview_html .= '<div class="col-md-4"><strong>' . $this->language->get('text_total_assets') . ':</strong> ' . $preview_data['total_assets'] . '</div>';
            $preview_html .= '<div class="col-md-4"><strong>' . $this->language->get('text_total_liabilities') . ':</strong> ' . $preview_data['total_liabilities'] . '</div>';
            $preview_html .= '<div class="col-md-4"><strong>' . $this->language->get('text_net_income') . ':</strong> ' . $preview_data['net_income'] . '</div>';
            $preview_html .= '</div>';

            $json['preview'] = $preview_html;
        } catch (Exception $e) {
            $json['error'] = $e->getMessage();
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * دالة تنظيف المخرجات (CONSTITUTIONAL REQUIREMENT)
     * Sanitize all output data to prevent XSS attacks
     */
    private function sanitizeOutputData($data) {
        if (is_array($data)) {
            foreach ($data as $key => $value) {
                $data[$key] = $this->sanitizeOutputData($value);
            }
        } elseif (is_string($data)) {
            $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
        }
        return $data;
    }

    /**
     * Enterprise Grade Plus Security Enhancements
     */
    private function validateInputData($data) {
        $validated = array();

        if (isset($data['report_type'])) {
            $validated['report_type'] = htmlspecialchars($data['report_type'], ENT_QUOTES, 'UTF-8');
        }

        if (isset($data['date_start'])) {
            $validated['date_start'] = date('Y-m-d', strtotime($data['date_start']));
        }

        if (isset($data['date_end'])) {
            $validated['date_end'] = date('Y-m-d', strtotime($data['date_end']));
        }

        if (isset($data['comparison_period'])) {
            $validated['comparison_period'] = htmlspecialchars($data['comparison_period'], ENT_QUOTES, 'UTF-8');
        }

        return $validated;
    }

    /**
     * Rate Limiting Implementation
     */
    private function checkRateLimit() {
        $ip = $this->request->server['REMOTE_ADDR'];
        $user_id = $this->user->getId();

        return $this->central_service->checkRateLimit('financial_reports', $ip, $user_id, 20, 3600); // 20 requests per hour
    }

    /**
     * Performance Optimization - Memory Management
     */
    private function optimizeMemoryUsage() {
        ini_set('memory_limit', '1024M');
        ini_set('max_execution_time', 600); // 10 minutes for complex financial reports
    }
}