/**
 * إعدادات متقدمة للنظام - JavaScript Enterprise Grade
 * يتضمن التحقق من صحة البيانات، التفاعل المتقدم، واختبار الاتصال مع ETA
 */

$(document).ready(function() {
    
    // تهيئة Select2 للحسابات
    $('.select2').select2({
        placeholder: 'اختر حساب...',
        allowClear: true,
        width: '100%'
    });

    // تهيئة Date Pickers
    $('.date').datetimepicker({
        format: 'YYYY-MM-DD',
        pickTime: false,
        language: 'ar'
    });

    // تحسين أزرار الراديو
    $('[data-toggle="buttons"] input[type="radio"]').on('change', function() {
        var $this = $(this);
        var $parent = $this.closest('.btn-group');
        
        $parent.find('.btn').removeClass('active');
        $this.closest('.btn').addClass('active');
    });

    // التحقق من صحة البيانات المتقدم
    $('#form-setting').on('submit', function(e) {
        var isValid = true;
        var errors = [];

        // التحقق من السنة المالية
        var startDate = $('input[name="config_financial_year_start"]').val();
        var endDate = $('input[name="config_financial_year_end"]').val();
        
        if (startDate && endDate) {
            if (new Date(startDate) >= new Date(endDate)) {
                errors.push('تاريخ نهاية السنة المالية يجب أن يكون بعد تاريخ البداية');
                isValid = false;
            }
        }

        // التحقق من إعدادات ETA
        if ($('input[name="config_eta_enabled"]:checked').val() == '1') {
            var requiredETAFields = [
                'config_eta_taxpayer_id',
                'config_eta_activity_code',
                'config_eta_client_id'
            ];

            requiredETAFields.forEach(function(field) {
                if (!$('input[name="' + field + '"]').val().trim()) {
                    errors.push('حقل ' + field + ' مطلوب عند تفعيل ETA');
                    isValid = false;
                }
            });

            // التحقق من رقم التسجيل الضريبي
            var taxpayerId = $('input[name="config_eta_taxpayer_id"]').val();
            if (taxpayerId && !validateEgyptianTaxId(taxpayerId)) {
                errors.push('رقم التسجيل الضريبي غير صحيح');
                isValid = false;
            }
        }

        // عرض الأخطاء
        if (!isValid) {
            e.preventDefault();
            showValidationErrors(errors);
        } else {
            showLoadingOverlay();
        }
    });

    // اختبار الاتصال مع ETA
    $('#test-eta-connection').on('click', function() {
        var $btn = $(this);
        var originalText = $btn.text();
        
        $btn.prop('disabled', true).text('جاري الاختبار...');
        
        $.ajax({
            url: 'index.php?route=setting/setting/testETAConnection&user_token=' + getToken(),
            type: 'POST',
            dataType: 'json',
            data: {
                client_id: $('input[name="config_eta_client_id"]').val(),
                client_secret: $('input[name="config_eta_client_secret"]').val(),
                environment: $('select[name="config_eta_environment"]').val()
            },
            success: function(response) {
                if (response.success) {
                    updateETAStatus('connected', response.message);
                    showAlert('success', response.message);
                } else {
                    updateETAStatus('disconnected', response.message);
                    showAlert('danger', response.message);
                }
            },
            error: function() {
                updateETAStatus('disconnected', 'خطأ في الاتصال');
                showAlert('danger', 'حدث خطأ أثناء اختبار الاتصال');
            },
            complete: function() {
                $btn.prop('disabled', false).text(originalText);
            }
        });
    });

    // تحديث حالة ETA
    function updateETAStatus(status, message) {
        var $statusDot = $('#eta-status-dot');
        var $statusText = $('#eta-status-text');
        
        $statusDot.removeClass('connected disconnected not-configured').addClass(status);
        $statusText.text(message);
        
        // تحديث الألوان
        var colors = {
            'connected': '#28a745',
            'disconnected': '#dc3545',
            'not-configured': '#ffc107'
        };
        
        $statusDot.css('background-color', colors[status]);
    }

    // التحقق من رقم التسجيل الضريبي المصري
    function validateEgyptianTaxId(taxId) {
        // التحقق من طول الرقم (9 أرقام)
        var pattern = /^\d{9}$/;
        return pattern.test(taxId);
    }

    // عرض أخطاء التحقق
    function showValidationErrors(errors) {
        var errorHtml = '<div class="alert alert-danger alert-dismissible">';
        errorHtml += '<button type="button" class="close" data-dismiss="alert">&times;</button>';
        errorHtml += '<h4><i class="fa fa-exclamation-triangle"></i> أخطاء في البيانات:</h4>';
        errorHtml += '<ul>';
        
        errors.forEach(function(error) {
            errorHtml += '<li>' + error + '</li>';
        });
        
        errorHtml += '</ul></div>';
        
        // إزالة التنبيهات السابقة وإضافة الجديدة
        $('.alert').remove();
        $('#form-setting').prepend(errorHtml);
        
        // التمرير للأعلى
        $('html, body').animate({scrollTop: 0}, 500);
    }

    // عرض تنبيه
    function showAlert(type, message) {
        var alertHtml = '<div class="alert alert-' + type + ' alert-dismissible">';
        alertHtml += '<button type="button" class="close" data-dismiss="alert">&times;</button>';
        alertHtml += '<i class="fa fa-' + (type === 'success' ? 'check' : 'exclamation-triangle') + '"></i> ';
        alertHtml += message + '</div>';
        
        $('.alert').remove();
        $('#form-setting').prepend(alertHtml);
        
        // إخفاء التنبيه تلقائياً بعد 5 ثوان
        setTimeout(function() {
            $('.alert').fadeOut();
        }, 5000);
    }

    // عرض شاشة التحميل
    function showLoadingOverlay() {
        var loadingHtml = '<div class="loading-overlay">';
        loadingHtml += '<div class="loading-spinner"></div>';
        loadingHtml += '</div>';
        
        $('body').append(loadingHtml);
    }

    // إخفاء شاشة التحميل
    function hideLoadingOverlay() {
        $('.loading-overlay').remove();
    }

    // الحصول على التوكن
    function getToken() {
        return $('input[name="user_token"]').val() || '';
    }

    // تحديث إحصائيات ETA
    function updateETAStatistics() {
        $.ajax({
            url: 'index.php?route=setting/setting/getETAStatistics&user_token=' + getToken(),
            type: 'GET',
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    $('#eta-invoices-sent').text(response.data.invoices_sent);
                    $('#eta-receipts-sent').text(response.data.receipts_sent);
                    $('#eta-queue-count').text(response.data.queue_count);
                    $('#eta-success-rate').text(response.data.success_rate + '%');
                    $('#eta-last-sync').text(response.data.last_sync);
                }
            }
        });
    }

    // تحديث الإحصائيات كل 30 ثانية
    setInterval(updateETAStatistics, 30000);

    // تحديث أولي للإحصائيات
    updateETAStatistics();

    // تفعيل/إلغاء تفعيل حقول ETA
    $('input[name="config_eta_enabled"]').on('change', function() {
        var isEnabled = $(this).val() == '1';
        var $etaFields = $('.eta-field');
        
        if (isEnabled) {
            $etaFields.prop('disabled', false).closest('.form-group').show();
        } else {
            $etaFields.prop('disabled', true).closest('.form-group').hide();
        }
    });

    // تحديث معاينة الحساب المحدد
    $('.account-select').on('change', function() {
        var $this = $(this);
        var accountCode = $this.val();
        var $preview = $this.siblings('.account-preview');
        
        if (accountCode && accountCode !== '0') {
            var accountName = $this.find('option:selected').text();
            $preview.html('<small class="text-info"><i class="fa fa-info-circle"></i> ' + accountName + '</small>').show();
        } else {
            $preview.hide();
        }
    });

    // حفظ الإعدادات بـ AJAX
    $('#quick-save').on('click', function() {
        var $btn = $(this);
        var originalText = $btn.text();
        
        $btn.prop('disabled', true).text('جاري الحفظ...');
        
        $.ajax({
            url: $('#form-setting').attr('action'),
            type: 'POST',
            data: $('#form-setting').serialize(),
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    showAlert('success', 'تم حفظ الإعدادات بنجاح');
                } else {
                    showAlert('danger', response.error || 'حدث خطأ أثناء الحفظ');
                }
            },
            error: function() {
                showAlert('danger', 'حدث خطأ أثناء الحفظ');
            },
            complete: function() {
                $btn.prop('disabled', false).text(originalText);
            }
        });
    });

    // تصدير الإعدادات
    $('#export-settings').on('click', function() {
        window.location.href = 'index.php?route=setting/setting/export&user_token=' + getToken();
    });

    // استيراد الإعدادات
    $('#import-settings').on('change', function() {
        var file = this.files[0];
        if (file) {
            var formData = new FormData();
            formData.append('settings_file', file);
            formData.append('user_token', getToken());
            
            $.ajax({
                url: 'index.php?route=setting/setting/import',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        showAlert('success', 'تم استيراد الإعدادات بنجاح');
                        setTimeout(function() {
                            location.reload();
                        }, 2000);
                    } else {
                        showAlert('danger', response.error || 'حدث خطأ أثناء الاستيراد');
                    }
                },
                error: function() {
                    showAlert('danger', 'حدث خطأ أثناء الاستيراد');
                }
            });
        }
    });

    // تحسين تجربة المستخدم
    $('input, select, textarea').on('focus', function() {
        $(this).closest('.form-group').addClass('focused');
    }).on('blur', function() {
        $(this).closest('.form-group').removeClass('focused');
    });

    // إضافة تأثيرات بصرية للتبويبات
    $('.nav-tabs a').on('shown.bs.tab', function(e) {
        var target = $(e.target).attr('href');
        $(target).addClass('tab-fade-in');
        
        setTimeout(function() {
            $(target).removeClass('tab-fade-in');
        }, 300);
    });

    // تحديد التبويب النشط من URL
    var hash = window.location.hash;
    if (hash) {
        $('.nav-tabs a[href="' + hash + '"]').tab('show');
    }

    // تحديث URL عند تغيير التبويب
    $('.nav-tabs a').on('shown.bs.tab', function(e) {
        window.location.hash = e.target.hash;
    });

});

// إضافة CSS للتأثيرات
$('<style>')
    .prop('type', 'text/css')
    .html(`
        .form-group.focused {
            background-color: rgba(0,123,255,0.05);
            border-radius: 4px;
            transition: all 0.3s ease;
        }
        
        .tab-fade-in {
            animation: fadeIn 0.3s ease-in-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .account-preview {
            margin-top: 5px;
        }
    `)
    .appendTo('head');
