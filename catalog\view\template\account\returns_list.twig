{{ header }}
<div id="account-return" class="container-fluid">
  <div class="row">{{ column_left }}
    <div id="content" class="col" style="padding-left:10px;padding-right:10px;padding-top:10px">{{ content_top }}
      {% if returns %}
        <div class="table-responsive">
          <table class="table table-bordered table-hover">
            <thead>
              <tr>
                <td class="text-end">{{ column_return_id }}</td>
                <td class="text-start">{{ column_status }}</td>
                <td class="text-start">{{ column_date_added }}</td>
                <td class="text-end">{{ column_order_id }}</td>
                <td class="text-start">{{ column_customer }}</td>
                <td></td>
              </tr>
            </thead>
            <tbody>
              {% for return in returns %}
                <tr>
                  <td class="text-end">#{{ return.return_id }}</td>
                  <td class="text-start">{{ return.status }}</td>
                  <td class="text-start">{{ return.date_added }}</td>
                  <td class="text-end">{{ return.order_id }}</td>
                  <td class="text-start">{{ return.name }}</td>
                  <td class="text-end"><a href="{{ return.href }}" data-bs-toggle="tooltip" title="{{ button_view }}" class="btn btn-info"><i class="fa-solid fa-eye"></i></a></td>
                </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
        <div class="row mb-3">
          <div class="col-sm-6 text-start">{{ pagination }}</div>
          <div class="col-sm-6 text-end">{{ results }}</div>
        </div>
      {% else %}
        <p>{{ text_no_results }}</p>
      {% endif %}
      <div class="text-end"><a href="{{ continue }}" class="btn btn-primary">{{ button_continue }}</a></div>
      {{ content_bottom }}</div>
    {{ column_right }}</div>
</div>
{{ footer }}
