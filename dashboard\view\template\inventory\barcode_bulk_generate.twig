{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="submit" form="inventory\barcode_management-form" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fas fa-save"></i></button>
        <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-light"><i class="fas fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    
    <div class="card">
      <div class="card-header"><i class="fas fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}chart-bar{% elif screen_type == 'settings' %}cog{% else %}pencil-alt{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="card-body">
        <form id="inventory\barcode_management-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-add">{{ text_add }}</label>
            <div class="col-sm-10">
              <input type="text" name="add" value="{{ add }}" placeholder="{{ text_add }}" id="input-add" class="form-control" />
              {% if error_add %}
                <div class="invalid-feedback">{{ error_add }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-auto_generated_options">{{ text_auto_generated_options }}</label>
            <div class="col-sm-10">
              <input type="text" name="auto_generated_options" value="{{ auto_generated_options }}" placeholder="{{ text_auto_generated_options }}" id="input-auto_generated_options" class="form-control" />
              {% if error_auto_generated_options %}
                <div class="invalid-feedback">{{ error_auto_generated_options }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-back">{{ text_back }}</label>
            <div class="col-sm-10">
              <input type="text" name="back" value="{{ back }}" placeholder="{{ text_back }}" id="input-back" class="form-control" />
              {% if error_back %}
                <div class="invalid-feedback">{{ error_back }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-barcode_info">{{ text_barcode_info }}</label>
            <div class="col-sm-10">
              <input type="text" name="barcode_info" value="{{ barcode_info }}" placeholder="{{ text_barcode_info }}" id="input-barcode_info" class="form-control" />
              {% if error_barcode_info %}
                <div class="invalid-feedback">{{ error_barcode_info }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-barcode_types">{{ text_barcode_types }}</label>
            <div class="col-sm-10">
              <input type="text" name="barcode_types" value="{{ barcode_types }}" placeholder="{{ text_barcode_types }}" id="input-barcode_types" class="form-control" />
              {% if error_barcode_types %}
                <div class="invalid-feedback">{{ error_barcode_types }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-barcodes">{{ text_barcodes }}</label>
            <div class="col-sm-10">
              <input type="text" name="barcodes" value="{{ barcodes }}" placeholder="{{ text_barcodes }}" id="input-barcodes" class="form-control" />
              {% if error_barcodes %}
                <div class="invalid-feedback">{{ error_barcodes }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-edit">{{ text_edit }}</label>
            <div class="col-sm-10">
              <input type="text" name="edit" value="{{ edit }}" placeholder="{{ text_edit }}" id="input-edit" class="form-control" />
              {% if error_edit %}
                <div class="invalid-feedback">{{ error_edit }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-export_excel">{{ text_export_excel }}</label>
            <div class="col-sm-10">
              <input type="text" name="export_excel" value="{{ export_excel }}" placeholder="{{ text_export_excel }}" id="input-export_excel" class="form-control" />
              {% if error_export_excel %}
                <div class="invalid-feedback">{{ error_export_excel }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-generate_barcode">{{ text_generate_barcode }}</label>
            <div class="col-sm-10">
              <input type="text" name="generate_barcode" value="{{ generate_barcode }}" placeholder="{{ text_generate_barcode }}" id="input-generate_barcode" class="form-control" />
              {% if error_generate_barcode %}
                <div class="invalid-feedback">{{ error_generate_barcode }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-generate_bulk">{{ text_generate_bulk }}</label>
            <div class="col-sm-10">
              <input type="text" name="generate_bulk" value="{{ generate_bulk }}" placeholder="{{ text_generate_bulk }}" id="input-generate_bulk" class="form-control" />
              {% if error_generate_bulk %}
                <div class="invalid-feedback">{{ error_generate_bulk }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="invalid-feedback">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-most_used_barcodes">{{ text_most_used_barcodes }}</label>
            <div class="col-sm-10">
              <input type="text" name="most_used_barcodes" value="{{ most_used_barcodes }}" placeholder="{{ text_most_used_barcodes }}" id="input-most_used_barcodes" class="form-control" />
              {% if error_most_used_barcodes %}
                <div class="invalid-feedback">{{ error_most_used_barcodes }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-order">{{ text_order }}</label>
            <div class="col-sm-10">
              <input type="text" name="order" value="{{ order }}" placeholder="{{ text_order }}" id="input-order" class="form-control" />
              {% if error_order %}
                <div class="invalid-feedback">{{ error_order }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-pagination">{{ text_pagination }}</label>
            <div class="col-sm-10">
              <input type="text" name="pagination" value="{{ pagination }}" placeholder="{{ text_pagination }}" id="input-pagination" class="form-control" />
              {% if error_pagination %}
                <div class="invalid-feedback">{{ error_pagination }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-primary_options">{{ text_primary_options }}</label>
            <div class="col-sm-10">
              <input type="text" name="primary_options" value="{{ primary_options }}" placeholder="{{ text_primary_options }}" id="input-primary_options" class="form-control" />
              {% if error_primary_options %}
                <div class="invalid-feedback">{{ error_primary_options }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-print">{{ text_print }}</label>
            <div class="col-sm-10">
              <input type="text" name="print" value="{{ print }}" placeholder="{{ text_print }}" id="input-print" class="form-control" />
              {% if error_print %}
                <div class="invalid-feedback">{{ error_print }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-print_labels">{{ text_print_labels }}</label>
            <div class="col-sm-10">
              <input type="text" name="print_labels" value="{{ print_labels }}" placeholder="{{ text_print_labels }}" id="input-print_labels" class="form-control" />
              {% if error_print_labels %}
                <div class="invalid-feedback">{{ error_print_labels }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-print_log">{{ text_print_log }}</label>
            <div class="col-sm-10">
              <input type="text" name="print_log" value="{{ print_log }}" placeholder="{{ text_print_log }}" id="input-print_log" class="form-control" />
              {% if error_print_log %}
                <div class="invalid-feedback">{{ error_print_log }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-products">{{ text_products }}</label>
            <div class="col-sm-10">
              <input type="text" name="products" value="{{ products }}" placeholder="{{ text_products }}" id="input-products" class="form-control" />
              {% if error_products %}
                <div class="invalid-feedback">{{ error_products }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-quantity">{{ text_quantity }}</label>
            <div class="col-sm-10">
              <input type="text" name="quantity" value="{{ quantity }}" placeholder="{{ text_quantity }}" id="input-quantity" class="form-control" />
              {% if error_quantity %}
                <div class="invalid-feedback">{{ error_quantity }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-refresh">{{ text_refresh }}</label>
            <div class="col-sm-10">
              <input type="text" name="refresh" value="{{ refresh }}" placeholder="{{ text_refresh }}" id="input-refresh" class="form-control" />
              {% if error_refresh %}
                <div class="invalid-feedback">{{ error_refresh }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-results">{{ text_results }}</label>
            <div class="col-sm-10">
              <input type="text" name="results" value="{{ results }}" placeholder="{{ text_results }}" id="input-results" class="form-control" />
              {% if error_results %}
                <div class="invalid-feedback">{{ error_results }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-scan_barcode">{{ text_scan_barcode }}</label>
            <div class="col-sm-10">
              <input type="text" name="scan_barcode" value="{{ scan_barcode }}" placeholder="{{ text_scan_barcode }}" id="input-scan_barcode" class="form-control" />
              {% if error_scan_barcode %}
                <div class="invalid-feedback">{{ error_scan_barcode }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-scan_log">{{ text_scan_log }}</label>
            <div class="col-sm-10">
              <input type="text" name="scan_log" value="{{ scan_log }}" placeholder="{{ text_scan_log }}" id="input-scan_log" class="form-control" />
              {% if error_scan_log %}
                <div class="invalid-feedback">{{ error_scan_log }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort">{{ text_sort }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort" value="{{ sort }}" placeholder="{{ text_sort }}" id="input-sort" class="form-control" />
              {% if error_sort %}
                <div class="invalid-feedback">{{ error_sort }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-statistics">{{ text_statistics }}</label>
            <div class="col-sm-10">
              <input type="text" name="statistics" value="{{ statistics }}" placeholder="{{ text_statistics }}" id="input-statistics" class="form-control" />
              {% if error_statistics %}
                <div class="invalid-feedback">{{ error_statistics }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-status_options">{{ text_status_options }}</label>
            <div class="col-sm-10">
              <input type="text" name="status_options" value="{{ status_options }}" placeholder="{{ text_status_options }}" id="input-status_options" class="form-control" />
              {% if error_status_options %}
                <div class="invalid-feedback">{{ error_status_options }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_form">{{ text_text_form }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_form" value="{{ text_form }}" placeholder="{{ text_text_form }}" id="input-text_form" class="form-control" />
              {% if error_text_form %}
                <div class="invalid-feedback">{{ error_text_form }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-type_statistics">{{ text_type_statistics }}</label>
            <div class="col-sm-10">
              <input type="text" name="type_statistics" value="{{ type_statistics }}" placeholder="{{ text_type_statistics }}" id="input-type_statistics" class="form-control" />
              {% if error_type_statistics %}
                <div class="invalid-feedback">{{ error_type_statistics }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-units">{{ text_units }}</label>
            <div class="col-sm-10">
              <input type="text" name="units" value="{{ units }}" placeholder="{{ text_units }}" id="input-units" class="form-control" />
              {% if error_units %}
                <div class="invalid-feedback">{{ error_units }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="invalid-feedback">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-validate_barcode">{{ text_validate_barcode }}</label>
            <div class="col-sm-10">
              <input type="text" name="validate_barcode" value="{{ validate_barcode }}" placeholder="{{ text_validate_barcode }}" id="input-validate_barcode" class="form-control" />
              {% if error_validate_barcode %}
                <div class="invalid-feedback">{{ error_validate_barcode }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}