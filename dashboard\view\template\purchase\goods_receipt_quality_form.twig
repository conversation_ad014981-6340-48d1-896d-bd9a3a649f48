<div class="modal-header">
  <button type="button" class="close" data-dismiss="modal">&times;</button>
  <h4 class="modal-title">{{ text_quality_check }} - {{ receipt_number }}</h4>
</div>
<div class="modal-body">
  <form id="form-quality-check" class="form-horizontal">
    <input type="hidden" name="goods_receipt_id" value="{{ goods_receipt_id }}" />
    
    <div class="alert alert-info">
      <i class="fa fa-info-circle"></i> Please check the quality of all received items and mark them as passed or failed.
    </div>
    
    <div class="form-group">
      <label class="col-sm-3 control-label">{{ text_quality_status }}</label>
      <div class="col-sm-9">
        <select name="status" class="form-control">
          <option value="passed">{{ text_quality_pass }}</option>
          <option value="failed">{{ text_quality_fail }}</option>
          <option value="partial">{{ text_quality_partial }}</option>
        </select>
      </div>
    </div>
    
    <div class="form-group">
      <label class="col-sm-3 control-label">{{ text_quality_notes }}</label>
      <div class="col-sm-9">
        <textarea name="notes" class="form-control" rows="3"></textarea>
      </div>
    </div>
    
    <h4>Items Quality Check</h4>
    <div class="table-responsive">
      <table class="table table-bordered table-hover">
        <thead>
          <tr>
            <th>{{ column_product }}</th>
            <th width="100">{{ column_received_quantity }}</th>
            <th width="150">{{ column_quality_result }}</th>
            <th>{{ column_notes }}</th>
          </tr>
        </thead>
        <tbody>
          {% if receipt_items %}
            {% for item in receipt_items %}
            <tr>
              <td>{{ item.product_name }}</td>
              <td>{{ item.quantity_received }} {{ item.unit_name }}</td>
              <td>
                <select name="item[{{ item.receipt_item_id }}][result]" class="form-control quality-result">
                  <option value="passed">{{ text_quality_pass }}</option>
                  <option value="failed">{{ text_quality_fail }}</option>
                  <option value="partial">{{ text_quality_partial }}</option>
                </select>
              </td>
              <td>
                <input type="text" name="item[{{ item.receipt_item_id }}][notes]" class="form-control" />
              </td>
            </tr>
            {% endfor %}
          {% else %}
            <tr>
              <td colspan="4" class="text-center">No items found</td>
            </tr>
          {% endif %}
        </tbody>
      </table>
    </div>
  </form>
</div>
<div class="modal-footer">
  <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_cancel }}</button>
  <button type="button" class="btn btn-primary" onclick="saveQualityCheck()">{{ button_save }}</button>
</div>

<script type="text/javascript">
function initializeQualityForm() {
  // Auto-detect overall status based on individual results
  $('.quality-result').on('change', function() {
    updateOverallStatus();
  });
}

function updateOverallStatus() {
  var allPassed = true;
  var allFailed = true;
  
  $('.quality-result').each(function() {
    var value = $(this).val();
    if (value !== 'passed') allPassed = false;
    if (value !== 'failed') allFailed = false;
  });
  
  if (allPassed) {
    $('select[name="status"]').val('passed');
  } else if (allFailed) {
    $('select[name="status"]').val('failed');
  } else {
    $('select[name="status"]').val('partial');
  }
}

function saveQualityCheck() {
  $.ajax({
    url: 'index.php?route=purchase/goods_receipt/ajaxQualityCheck&user_token=' + user_token,
    type: 'post',
    dataType: 'json',
    data: $('#form-quality-check').serialize(),
    beforeSend: function() {
      $('.btn-primary').button('loading');
    },
    complete: function() {
      $('.btn-primary').button('reset');
    },
    success: function(json) {
      $('.alert, .text-danger').remove();
      $('.form-group').removeClass('has-error');
      
      if (json.error) {
        $('#form-quality-check').before('<div class="alert alert-danger"><i class="fa fa-exclamation-circle"></i> ' + json.error + '</div>');
      }
      
      if (json.success) {
        $('#modal-quality-check').modal('hide');
        
        // Show success message and reload list
        showSuccess(json.success);
        loadReceipts();
      }
    },
    error: function(xhr, ajaxOptions, thrownError) {
      alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
    }
  });
}
</script> 