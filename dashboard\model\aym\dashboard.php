<?php
/**
 * AYM ERP Dashboard Model
 * نموذج البيانات المخصص للوحة معلومات AYM ERP
 *
 * يوفر استعلامات وتحليلات متخصصة للوحة معلومات AYM ERP
 * - مؤشرات الأداء الرئيسية (KPIs)
 * - تحليلات المبيعات والمشتريات
 * - مؤشرات المخزون والتمويل
 * - تقارير الأنشطة الحديثة والإشعارات
 * - معلومات سير العمل والموافقات
 */

class ModelAymDashboard extends Model {

    /**
     * Check if table exists
     * التحقق من وجود الجدول
     */
    private function tableExists($table_name) {
        $sql = "SHOW TABLES LIKE '" . DB_PREFIX . $table_name . "'";
        $query = $this->db->query($sql);
        return $query->num_rows > 0;
    }

    /**
     * Safe query execution with error handling
     * تنفيذ الاستعلام بأمان مع معالجة الأخطاء
     */
    private function safeQuery($sql, $default_value = null) {
        try {
            $query = $this->db->query($sql);
            return $query;
        } catch (Exception $e) {
            // Log error for debugging
            error_log('AYM Dashboard Query Error: ' . $e->getMessage() . ' SQL: ' . $sql);
            if ($default_value === null) {
                // إنشاء كائن استعلام فارغ مؤقت
                return (object)['row' => [], 'rows' => [], 'num_rows' => 0];
            }
            return $default_value;
        }
    }

    /**
     * Get user dashboard widgets
     * الحصول على عناصر لوحة المعلومات للمستخدم
     */
    public function getUserDashboardWidgets($user_id) {
        if (!$this->tableExists('cod_dashboard_widgets')) {
            return [];
        }

        $sql = "SELECT * FROM " . DB_PREFIX . "dashboard_widgets 
                WHERE user_id = '" . (int)$user_id . "' 
                AND is_active = 1 
                ORDER BY position_y ASC, position_x ASC";
        
        $query = $this->safeQuery($sql);
        return $query->rows ?? [];
    }

    /**
     * Save user dashboard widget
     * حفظ عنصر لوحة المعلومات للمستخدم
     */
    public function saveWidget($data) {
        if (!$this->tableExists('cod_dashboard_widgets')) {
            return false;
        }

        if (!empty($data['widget_id'])) {
            // Update existing widget
            $sql = "UPDATE " . DB_PREFIX . "dashboard_widgets SET 
                    widget_type = '" . $this->db->escape($data['widget_type']) . "',
                    widget_name = '" . $this->db->escape($data['widget_name']) . "',
                    widget_config = '" . $this->db->escape(json_encode($data['widget_config'])) . "',
                    position_x = '" . (int)$data['position_x'] . "',
                    position_y = '" . (int)$data['position_y'] . "',
                    width = '" . (int)$data['width'] . "',
                    height = '" . (int)$data['height'] . "',
                    is_active = '" . (int)$data['is_active'] . "',
                    date_modified = NOW()
                    WHERE widget_id = '" . (int)$data['widget_id'] . "'";
            
            $this->safeQuery($sql);
            return $data['widget_id'];
        } else {
            // Create new widget
            $sql = "INSERT INTO " . DB_PREFIX . "dashboard_widgets SET 
                    user_id = '" . (int)$data['user_id'] . "',
                    widget_type = '" . $this->db->escape($data['widget_type']) . "',
                    widget_name = '" . $this->db->escape($data['widget_name']) . "',
                    widget_config = '" . $this->db->escape(json_encode($data['widget_config'])) . "',
                    position_x = '" . (int)$data['position_x'] . "',
                    position_y = '" . (int)$data['position_y'] . "',
                    width = '" . (int)$data['width'] . "',
                    height = '" . (int)$data['height'] . "',
                    is_active = '" . (int)$data['is_active'] . "',
                    date_added = NOW(),
                    date_modified = NOW()";
            
            $this->safeQuery($sql);
            return $this->db->getLastId();
        }
    }

    /**
     * Delete dashboard widget
     * حذف عنصر لوحة المعلومات
     */
    public function deleteWidget($widget_id) {
        if (!$this->tableExists('cod_dashboard_widgets')) {
            return false;
        }

        $sql = "DELETE FROM " . DB_PREFIX . "dashboard_widgets 
                WHERE widget_id = '" . (int)$widget_id . "'";
        
        $this->safeQuery($sql);
        return true;
    }

    /**
     * Get user preferences
     * الحصول على تفضيلات المستخدم
     */
    public function getUserPreferences($user_id) {
        if (!$this->tableExists('cod_dashboard_user_preferences')) {
            return [];
        }

        $sql = "SELECT preference_key, preference_value 
                FROM " . DB_PREFIX . "dashboard_user_preferences 
                WHERE user_id = '" . (int)$user_id . "'";
        
        $query = $this->safeQuery($sql);
        
        $preferences = [];
        foreach ($query->rows as $row) {
            $preferences[$row['preference_key']] = json_decode($row['preference_value'], true);
        }
        
        return $preferences;
    }

    /**
     * Save user preference
     * حفظ تفضيلات المستخدم
     */
    public function saveUserPreference($user_id, $preference_key, $preference_value) {
        if (!$this->tableExists('cod_dashboard_user_preferences')) {
            return false;
        }

        // Check if preference already exists
        $sql = "SELECT preference_id FROM " . DB_PREFIX . "dashboard_user_preferences 
                WHERE user_id = '" . (int)$user_id . "' 
                AND preference_key = '" . $this->db->escape($preference_key) . "'";
        
        $query = $this->safeQuery($sql);
        
        if ($query->num_rows) {
            // Update existing preference
            $sql = "UPDATE " . DB_PREFIX . "dashboard_user_preferences SET 
                    preference_value = '" . $this->db->escape(json_encode($preference_value)) . "',
                    date_modified = NOW()
                    WHERE preference_id = '" . (int)$query->row['preference_id'] . "'";
            
            $this->safeQuery($sql);
        } else {
            // Create new preference
            $sql = "INSERT INTO " . DB_PREFIX . "dashboard_user_preferences SET 
                    user_id = '" . (int)$user_id . "',
                    preference_key = '" . $this->db->escape($preference_key) . "',
                    preference_value = '" . $this->db->escape(json_encode($preference_value)) . "',
                    date_added = NOW(),
                    date_modified = NOW()";
            
            $this->safeQuery($sql);
        }
        
        return true;
    }

    /**
     * Get user sessions
     * الحصول على جلسات المستخدم
     */
    public function getUserSessions($user_id) {
        if (!$this->tableExists('cod_user_session')) {
            return [];
        }

        $sql = "SELECT * FROM " . DB_PREFIX . "user_session 
                WHERE user_id = '" . (int)$user_id . "' 
                AND expire > NOW()
                ORDER BY date_added DESC";
        
        $query = $this->safeQuery($sql);
        return $query->rows ?? [];
    }

    /**
     * Get ERP modules status
     * الحصول على حالة وحدات نظام ERP
     */
    public function getERPModulesStatus() {
        $modules = [
            'sales' => [
                'name' => 'المبيعات',
                'status' => $this->checkModuleStatus('sale'),
                'total_records' => $this->getModuleTotalRecords('sale'),
                'last_activity' => $this->getModuleLastActivity('sale')
            ],
            'purchase' => [
                'name' => 'المشتريات',
                'status' => $this->checkModuleStatus('purchase'),
                'total_records' => $this->getModuleTotalRecords('purchase'),
                'last_activity' => $this->getModuleLastActivity('purchase')
            ],
            'inventory' => [
                'name' => 'المخزون',
                'status' => $this->checkModuleStatus('inventory'),
                'total_records' => $this->getModuleTotalRecords('inventory'),
                'last_activity' => $this->getModuleLastActivity('inventory')
            ],
            'accounting' => [
                'name' => 'المحاسبة',
                'status' => $this->checkModuleStatus('accounting'),
                'total_records' => $this->getModuleTotalRecords('accounting'),
                'last_activity' => $this->getModuleLastActivity('accounting')
            ],
            'crm' => [
                'name' => 'إدارة علاقات العملاء',
                'status' => $this->checkModuleStatus('crm'),
                'total_records' => $this->getModuleTotalRecords('crm'),
                'last_activity' => $this->getModuleLastActivity('crm')
            ],
            'hr' => [
                'name' => 'الموارد البشرية',
                'status' => $this->checkModuleStatus('hr'),
                'total_records' => $this->getModuleTotalRecords('hr'),
                'last_activity' => $this->getModuleLastActivity('hr')
            ],
            'shipping' => [
                'name' => 'الشحن',
                'status' => $this->checkModuleStatus('shipping'),
                'total_records' => $this->getModuleTotalRecords('shipping'),
                'last_activity' => $this->getModuleLastActivity('shipping')
            ],
            'eta' => [
                'name' => 'الفاتورة الإلكترونية',
                'status' => $this->checkModuleStatus('eta'),
                'total_records' => $this->getModuleTotalRecords('eta'),
                'last_activity' => $this->getModuleLastActivity('eta')
            ]
        ];

        return $modules;
    }

    /**
     * Check module status
     * فحص حالة الوحدة
     */
    private function checkModuleStatus($module_code) {
        // Check if the module's main controller exists and is accessible
        $controller_path = DIR_APPLICATION . 'controller/' . $module_code;
        
        if (is_dir($controller_path)) {
            return 'active';
        }
        
        return 'inactive';
    }

    /**
     * Get module total records
     * الحصول على إجمالي سجلات الوحدة
     */
    private function getModuleTotalRecords($module_code) {
        // Map modules to their primary tables
        $module_tables = [
            'sale' => 'order',
            'purchase' => 'purchase_order',
            'inventory' => 'product',
            'accounting' => 'journal_entry',
            'crm' => 'customer',
            'hr' => 'employee',
            'shipping' => 'shipping_order',
            'eta' => 'eta_invoice'
        ];
        
        if (isset($module_tables[$module_code]) && $this->tableExists($module_tables[$module_code])) {
            $sql = "SELECT COUNT(*) as total FROM " . DB_PREFIX . $module_tables[$module_code];
            $query = $this->safeQuery($sql);
            
            return $query->row['total'] ?? 0;
        }
        
        return 0;
    }

    /**
     * Get module last activity
     * الحصول على آخر نشاط للوحدة
     */
    private function getModuleLastActivity($module_code) {
        if (!$this->tableExists('cod_activity_log')) {
            return null;
        }
        
        $sql = "SELECT date_added FROM " . DB_PREFIX . "activity_log 
                WHERE module = '" . $this->db->escape($module_code) . "' 
                ORDER BY date_added DESC 
                LIMIT 1";
        
        $query = $this->safeQuery($sql);
        
        return $query->row['date_added'] ?? null;
    }

    /**
     * Get system health
     * الحصول على حالة النظام
     */
    public function getSystemHealth() {
        $health = [
            'database' => $this->getDatabaseHealth(),
            'disk_space' => $this->getDiskSpaceHealth(),
            'memory_usage' => $this->getMemoryUsage(),
            'php_version' => PHP_VERSION,
            'opencart_version' => VERSION,
            'erp_version' => '1.7',
            'last_backup' => $this->getLastBackupTime(),
            'security_status' => $this->getSecurityStatus()
        ];

        return $health;
    }

    /**
     * Get database health
     * الحصول على حالة قاعدة البيانات
     */
    private function getDatabaseHealth() {
        $health = [
            'status' => 'good',
            'message' => 'قاعدة البيانات تعمل بشكل طبيعي',
            'details' => []
        ];

        // Check table status
        $sql = "SHOW TABLE STATUS";
        $query = $this->safeQuery($sql);
        
        $total_size = 0;
        $table_count = 0;
        
        foreach ($query->rows as $row) {
            $table_count++;
            
            // Calculate table size in MB
            $table_size = ($row['Data_length'] + $row['Index_length']) / (1024 * 1024);
            $total_size += $table_size;
            
            // Check for table issues
            if ($row['Engine'] != 'InnoDB') {
                $health['details'][] = "جدول {$row['Name']} يستخدم محرك {$row['Engine']} بدلاً من InnoDB";
                $health['status'] = 'warning';
            }
            
            if ($row['Collation'] != 'utf8mb4_general_ci' && $row['Collation'] != 'utf8_general_ci') {
                $health['details'][] = "جدول {$row['Name']} يستخدم ترميز {$row['Collation']} غير مدعوم للغة العربية";
                $health['status'] = 'warning';
            }
        }
        
        $health['table_count'] = $table_count;
        $health['total_size_mb'] = round($total_size, 2);
        
        return $health;
    }

    /**
     * Get disk space health
     * الحصول على حالة مساحة القرص
     */
    private function getDiskSpaceHealth() {
        $disk_space = [
            'status' => 'good',
            'message' => 'مساحة القرص كافية',
            'details' => []
        ];
        
        $total_space = disk_total_space(DIR_APPLICATION);
        $free_space = disk_free_space(DIR_APPLICATION);
        
        $disk_space['total_gb'] = round($total_space / (1024 * 1024 * 1024), 2);
        $disk_space['free_gb'] = round($free_space / (1024 * 1024 * 1024), 2);
        $disk_space['used_gb'] = $disk_space['total_gb'] - $disk_space['free_gb'];
        $disk_space['used_percentage'] = round(($disk_space['used_gb'] / $disk_space['total_gb']) * 100, 2);
        
        if ($disk_space['used_percentage'] > 90) {
            $disk_space['status'] = 'critical';
            $disk_space['message'] = 'مساحة القرص منخفضة جداً';
        } elseif ($disk_space['used_percentage'] > 75) {
            $disk_space['status'] = 'warning';
            $disk_space['message'] = 'مساحة القرص منخفضة';
        }
        
        return $disk_space;
    }

    /**
     * Get memory usage
     * الحصول على استخدام الذاكرة
     */
    private function getMemoryUsage() {
        $memory = [
            'status' => 'good',
            'message' => 'استخدام الذاكرة طبيعي',
            'details' => []
        ];
        
        $memory['limit'] = ini_get('memory_limit');
        $memory['used'] = round(memory_get_usage() / (1024 * 1024), 2) . ' MB';
        $memory['peak'] = round(memory_get_peak_usage() / (1024 * 1024), 2) . ' MB';
        
        return $memory;
    }

    /**
     * Get last backup time
     * الحصول على وقت آخر نسخة احتياطية
     */
    private function getLastBackupTime() {
        $backup_dir = DIR_SYSTEM . 'backups';
        
        if (!is_dir($backup_dir)) {
            return null;
        }
        
        $latest_backup = null;
        $latest_time = 0;
        
        foreach (scandir($backup_dir) as $file) {
            if ($file == '.' || $file == '..') {
                continue;
            }
            
            $file_path = $backup_dir . '/' . $file;
            $file_time = filemtime($file_path);
            
            if ($file_time > $latest_time) {
                $latest_time = $file_time;
                $latest_backup = $file;
            }
        }
        
        return $latest_time ? date('Y-m-d H:i:s', $latest_time) : null;
    }

    /**
     * Get security status
     * الحصول على حالة الأمان
     */
    private function getSecurityStatus() {
        $security = [
            'status' => 'good',
            'message' => 'النظام آمن',
            'issues' => []
        ];
        
        // Check for admin folder rename
        if (basename(DIR_APPLICATION) == 'admin') {
            $security['status'] = 'warning';
            $security['issues'][] = 'مجلد الإدارة يستخدم الاسم الافتراضي "admin"';
        }
        
        // Check for SSL
        if (!$this->request->server['HTTPS']) {
            $security['status'] = 'warning';
            $security['issues'][] = 'اتصال غير آمن (HTTP بدلاً من HTTPS)';
        }
        
        // Check for storage folder protection
        $storage_path = DIR_SYSTEM . '../storage';
        if (is_dir($storage_path) && !file_exists($storage_path . '/.htaccess')) {
            $security['status'] = 'critical';
            $security['issues'][] = 'مجلد التخزين غير محمي بملف .htaccess';
        }
        
        return $security;
    }
} 