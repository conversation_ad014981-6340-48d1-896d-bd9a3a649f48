<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/type/expr.proto

namespace GPBMetadata\Google\Type;

class Expr
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(hex2bin(
            "0adb010a16676f6f676c652f747970652f657870722e70726f746f120b67" .
            "6f6f676c652e7479706522500a044578707212120a0a6578707265737369" .
            "6f6e180120012809120d0a057469746c6518022001280912130a0b646573" .
            "6372697074696f6e18032001280912100a086c6f636174696f6e18042001" .
            "2809425a0a0f636f6d2e676f6f676c652e7479706542094578707250726f" .
            "746f50015a34676f6f676c652e676f6c616e672e6f72672f67656e70726f" .
            "746f2f676f6f676c65617069732f747970652f657870723b65787072a202" .
            "03475450620670726f746f33"
        ), true);

        static::$is_initialized = true;
    }
}

