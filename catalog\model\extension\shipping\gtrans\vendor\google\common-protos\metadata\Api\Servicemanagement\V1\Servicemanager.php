<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/servicemanagement/v1/servicemanager.proto

namespace GPBMetadata\Google\Api\Servicemanagement\V1;

class Servicemanager
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        \GPBMetadata\Google\Api\Annotations::initOnce();
        \GPBMetadata\Google\Api\Client::initOnce();
        \GPBMetadata\Google\Api\FieldBehavior::initOnce();
        \GPBMetadata\Google\Api\Service::initOnce();
        \GPBMetadata\Google\Api\Servicemanagement\V1\Resources::initOnce();
        \GPBMetadata\Google\Longrunning\Operations::initOnce();
        \GPBMetadata\Google\Protobuf\Any::initOnce();
        \GPBMetadata\Google\Protobuf\FieldMask::initOnce();
        \GPBMetadata\Google\Rpc\Status::initOnce();
        $pool->internalAddGeneratedFile(hex2bin(
            "0ab9340a34676f6f676c652f6170692f736572766963656d616e6167656d" .
            "656e742f76312f736572766963656d616e616765722e70726f746f121f67" .
            "6f6f676c652e6170692e736572766963656d616e6167656d656e742e7631" .
            "1a17676f6f676c652f6170692f636c69656e742e70726f746f1a1f676f6f" .
            "676c652f6170692f6669656c645f6265686176696f722e70726f746f1a18" .
            "676f6f676c652f6170692f736572766963652e70726f746f1a2f676f6f67" .
            "6c652f6170692f736572766963656d616e6167656d656e742f76312f7265" .
            "736f75726365732e70726f746f1a23676f6f676c652f6c6f6e6772756e6e" .
            "696e672f6f7065726174696f6e732e70726f746f1a19676f6f676c652f70" .
            "726f746f6275662f616e792e70726f746f1a20676f6f676c652f70726f74" .
            "6f6275662f6669656c645f6d61736b2e70726f746f1a17676f6f676c652f" .
            "7270632f7374617475732e70726f746f22720a134c697374536572766963" .
            "657352657175657374121b0a1370726f64756365725f70726f6a6563745f" .
            "696418012001280912110a09706167655f73697a6518052001280512120a" .
            "0a706167655f746f6b656e18062001280912170a0b636f6e73756d65725f" .
            "69641807200128094202180122720a144c69737453657276696365735265" .
            "73706f6e736512410a08736572766963657318012003280b322f2e676f6f" .
            "676c652e6170692e736572766963656d616e6167656d656e742e76312e4d" .
            "616e616765645365727669636512170a0f6e6578745f706167655f746f6b" .
            "656e180220012809222e0a11476574536572766963655265717565737412" .
            "190a0c736572766963655f6e616d651801200128094203e04102225d0a14" .
            "437265617465536572766963655265717565737412450a07736572766963" .
            "6518012001280b322f2e676f6f676c652e6170692e736572766963656d61" .
            "6e6167656d656e742e76312e4d616e61676564536572766963654203e041" .
            "0222310a1444656c657465536572766963655265717565737412190a0c73" .
            "6572766963655f6e616d651801200128094203e0410222330a16556e6465" .
            "6c657465536572766963655265717565737412190a0c736572766963655f" .
            "6e616d651801200128094203e04102225b0a17556e64656c657465536572" .
            "76696365526573706f6e736512400a077365727669636518012001280b32" .
            "2f2e676f6f676c652e6170692e736572766963656d616e6167656d656e74" .
            "2e76312e4d616e616765645365727669636522c2010a1747657453657276" .
            "696365436f6e6669675265717565737412190a0c736572766963655f6e61" .
            "6d651801200128094203e0410212160a09636f6e6669675f696418022001" .
            "28094203e0410212510a047669657718032001280e32432e676f6f676c65" .
            "2e6170692e736572766963656d616e6167656d656e742e76312e47657453" .
            "657276696365436f6e666967526571756573742e436f6e66696756696577" .
            "22210a0a436f6e6669675669657712090a054241534943100012080a0446" .
            "554c4c1001225d0a194c69737453657276696365436f6e66696773526571" .
            "7565737412190a0c736572766963655f6e616d651801200128094203e041" .
            "0212120a0a706167655f746f6b656e18022001280912110a09706167655f" .
            "73697a6518032001280522630a1a4c69737453657276696365436f6e6669" .
            "6773526573706f6e7365122c0a0f736572766963655f636f6e6669677318" .
            "012003280b32132e676f6f676c652e6170692e5365727669636512170a0f" .
            "6e6578745f706167655f746f6b656e18022001280922690a1a4372656174" .
            "6553657276696365436f6e6669675265717565737412190a0c7365727669" .
            "63655f6e616d651801200128094203e0410212300a0e736572766963655f" .
            "636f6e66696718022001280b32132e676f6f676c652e6170692e53657276" .
            "6963654203e04102229d010a195375626d6974436f6e666967536f757263" .
            "655265717565737412190a0c736572766963655f6e616d65180120012809" .
            "4203e0410212490a0d636f6e6669675f736f7572636518022001280b322d" .
            "2e676f6f676c652e6170692e736572766963656d616e6167656d656e742e" .
            "76312e436f6e666967536f757263654203e04102121a0a0d76616c696461" .
            "74655f6f6e6c791803200128084203e0410122490a1a5375626d6974436f" .
            "6e666967536f75726365526573706f6e7365122b0a0e736572766963655f" .
            "636f6e66696718012001280b32132e676f6f676c652e6170692e53657276" .
            "69636522780a1b43726561746553657276696365526f6c6c6f7574526571" .
            "7565737412190a0c736572766963655f6e616d651801200128094203e041" .
            "02123e0a07726f6c6c6f757418022001280b32282e676f6f676c652e6170" .
            "692e736572766963656d616e6167656d656e742e76312e526f6c6c6f7574" .
            "4203e0410222730a1a4c69737453657276696365526f6c6c6f7574735265" .
            "717565737412190a0c736572766963655f6e616d651801200128094203e0" .
            "410212120a0a706167655f746f6b656e18022001280912110a0970616765" .
            "5f73697a6518032001280512130a0666696c7465721804200128094203e0" .
            "410222720a1b4c69737453657276696365526f6c6c6f757473526573706f" .
            "6e7365123a0a08726f6c6c6f75747318012003280b32282e676f6f676c65" .
            "2e6170692e736572766963656d616e6167656d656e742e76312e526f6c6c" .
            "6f757412170a0f6e6578745f706167655f746f6b656e180220012809224e" .
            "0a1847657453657276696365526f6c6c6f75745265717565737412190a0c" .
            "736572766963655f6e616d651801200128094203e0410212170a0a726f6c" .
            "6c6f75745f69641802200128094203e04102224b0a14456e61626c655365" .
            "72766963655265717565737412190a0c736572766963655f6e616d651801" .
            "200128094203e0410212180a0b636f6e73756d65725f6964180220012809" .
            "4203e0410222170a15456e61626c6553657276696365526573706f6e7365" .
            "224c0a1544697361626c65536572766963655265717565737412190a0c73" .
            "6572766963655f6e616d651801200128094203e0410212180a0b636f6e73" .
            "756d65725f69641802200128094203e0410222180a1644697361626c6553" .
            "657276696365526573706f6e7365227b0a1b47656e6572617465436f6e66" .
            "69675265706f727452657175657374122d0a0a6e65775f636f6e66696718" .
            "012001280b32142e676f6f676c652e70726f746f6275662e416e794203e0" .
            "4102122d0a0a6f6c645f636f6e66696718022001280b32142e676f6f676c" .
            "652e70726f746f6275662e416e794203e0410122c9010a1c47656e657261" .
            "7465436f6e6669675265706f7274526573706f6e736512140a0c73657276" .
            "6963655f6e616d65180120012809120a0a02696418022001280912450a0e" .
            "6368616e67655f7265706f72747318032003280b322d2e676f6f676c652e" .
            "6170692e736572766963656d616e6167656d656e742e76312e4368616e67" .
            "655265706f727412400a0b646961676e6f737469637318042003280b322b" .
            "2e676f6f676c652e6170692e736572766963656d616e6167656d656e742e" .
            "76312e446961676e6f7374696332921e0a0e536572766963654d616e6167" .
            "657212b3010a0c4c697374536572766963657312342e676f6f676c652e61" .
            "70692e736572766963656d616e6167656d656e742e76312e4c6973745365" .
            "727669636573526571756573741a352e676f6f676c652e6170692e736572" .
            "766963656d616e6167656d656e742e76312e4c6973745365727669636573" .
            "526573706f6e7365223682d3e493020e120c2f76312f7365727669636573" .
            "da411f70726f64756365725f70726f6a6563745f69642c636f6e73756d65" .
            "725f696412a5010a0a4765745365727669636512322e676f6f676c652e61" .
            "70692e736572766963656d616e6167656d656e742e76312e476574536572" .
            "76696365526571756573741a2f2e676f6f676c652e6170692e7365727669" .
            "63656d616e6167656d656e742e76312e4d616e6167656453657276696365" .
            "223282d3e493021d121b2f76312f73657276696365732f7b736572766963" .
            "655f6e616d657dda410c736572766963655f6e616d6512f5010a0d437265" .
            "6174655365727669636512352e676f6f676c652e6170692e736572766963" .
            "656d616e6167656d656e742e76312e437265617465536572766963655265" .
            "71756573741a1d2e676f6f676c652e6c6f6e6772756e6e696e672e4f7065" .
            "726174696f6e228d0182d3e4930217220c2f76312f73657276696365733a" .
            "0773657276696365da410773657276696365ca41630a2e676f6f676c652e" .
            "6170692e736572766963656d616e6167656d656e742e76312e4d616e6167" .
            "6564536572766963651231676f6f676c652e6170692e736572766963656d" .
            "616e6167656d656e742e76312e4f7065726174696f6e4d65746164617461" .
            "12e6010a0d44656c6574655365727669636512352e676f6f676c652e6170" .
            "692e736572766963656d616e6167656d656e742e76312e44656c65746553" .
            "657276696365526571756573741a1d2e676f6f676c652e6c6f6e6772756e" .
            "6e696e672e4f7065726174696f6e227f82d3e493021d2a1b2f76312f7365" .
            "7276696365732f7b736572766963655f6e616d657dda410c736572766963" .
            "655f6e616d65ca414a0a15676f6f676c652e70726f746f6275662e456d70" .
            "74791231676f6f676c652e6170692e736572766963656d616e6167656d65" .
            "6e742e76312e4f7065726174696f6e4d657461646174611296020a0f556e" .
            "64656c6574655365727669636512372e676f6f676c652e6170692e736572" .
            "766963656d616e6167656d656e742e76312e556e64656c65746553657276" .
            "696365526571756573741a1d2e676f6f676c652e6c6f6e6772756e6e696e" .
            "672e4f7065726174696f6e22aa0182d3e493022622242f76312f73657276" .
            "696365732f7b736572766963655f6e616d657d3a756e64656c657465da41" .
            "0c736572766963655f6e616d65ca416c0a37676f6f676c652e6170692e73" .
            "6572766963656d616e6167656d656e742e76312e556e64656c6574655365" .
            "7276696365526573706f6e73651231676f6f676c652e6170692e73657276" .
            "6963656d616e6167656d656e742e76312e4f7065726174696f6e4d657461" .
            "6461746112c9010a124c69737453657276696365436f6e66696773123a2e" .
            "676f6f676c652e6170692e736572766963656d616e6167656d656e742e76" .
            "312e4c69737453657276696365436f6e66696773526571756573741a3b2e" .
            "676f6f676c652e6170692e736572766963656d616e6167656d656e742e76" .
            "312e4c69737453657276696365436f6e66696773526573706f6e7365223a" .
            "82d3e493022512232f76312f73657276696365732f7b736572766963655f" .
            "6e616d657d2f636f6e66696773da410c736572766963655f6e616d6512de" .
            "010a1047657453657276696365436f6e66696712382e676f6f676c652e61" .
            "70692e736572766963656d616e6167656d656e742e76312e476574536572" .
            "76696365436f6e666967526571756573741a132e676f6f676c652e617069" .
            "2e53657276696365227b82d3e4930257122f2f76312f7365727669636573" .
            "2f7b736572766963655f6e616d657d2f636f6e666967732f7b636f6e6669" .
            "675f69647d5a2412222f76312f73657276696365732f7b73657276696365" .
            "5f6e616d657d2f636f6e666967da411b736572766963655f6e616d652c63" .
            "6f6e6669675f69642c7669657712c2010a13437265617465536572766963" .
            "65436f6e666967123b2e676f6f676c652e6170692e736572766963656d61" .
            "6e6167656d656e742e76312e43726561746553657276696365436f6e6669" .
            "67526571756573741a132e676f6f676c652e6170692e5365727669636522" .
            "5982d3e493023522232f76312f73657276696365732f7b73657276696365" .
            "5f6e616d657d2f636f6e666967733a0e736572766963655f636f6e666967" .
            "da411b736572766963655f6e616d652c736572766963655f636f6e666967" .
            "12c4020a125375626d6974436f6e666967536f75726365123a2e676f6f67" .
            "6c652e6170692e736572766963656d616e6167656d656e742e76312e5375" .
            "626d6974436f6e666967536f75726365526571756573741a1d2e676f6f67" .
            "6c652e6c6f6e6772756e6e696e672e4f7065726174696f6e22d20182d3e4" .
            "93022f222a2f76312f73657276696365732f7b736572766963655f6e616d" .
            "657d2f636f6e666967733a7375626d69743a012ada412873657276696365" .
            "5f6e616d652c636f6e6669675f736f757263652c76616c69646174655f6f" .
            "6e6c79ca416f0a3a676f6f676c652e6170692e736572766963656d616e61" .
            "67656d656e742e76312e5375626d6974436f6e666967536f757263655265" .
            "73706f6e73651231676f6f676c652e6170692e736572766963656d616e61" .
            "67656d656e742e76312e4f7065726174696f6e4d6574616461746112d401" .
            "0a134c69737453657276696365526f6c6c6f757473123b2e676f6f676c65" .
            "2e6170692e736572766963656d616e6167656d656e742e76312e4c697374" .
            "53657276696365526f6c6c6f757473526571756573741a3c2e676f6f676c" .
            "652e6170692e736572766963656d616e6167656d656e742e76312e4c6973" .
            "7453657276696365526f6c6c6f757473526573706f6e7365224282d3e493" .
            "022612242f76312f73657276696365732f7b736572766963655f6e616d65" .
            "7d2f726f6c6c6f757473da4113736572766963655f6e616d652c66696c74" .
            "657212cd010a1147657453657276696365526f6c6c6f757412392e676f6f" .
            "676c652e6170692e736572766963656d616e6167656d656e742e76312e47" .
            "657453657276696365526f6c6c6f7574526571756573741a282e676f6f67" .
            "6c652e6170692e736572766963656d616e6167656d656e742e76312e526f" .
            "6c6c6f7574225382d3e493023312312f76312f73657276696365732f7b73" .
            "6572766963655f6e616d657d2f726f6c6c6f7574732f7b726f6c6c6f7574" .
            "5f69647dda4117736572766963655f6e616d652c726f6c6c6f75745f6964" .
            "12a1020a1443726561746553657276696365526f6c6c6f7574123c2e676f" .
            "6f676c652e6170692e736572766963656d616e6167656d656e742e76312e" .
            "43726561746553657276696365526f6c6c6f7574526571756573741a1d2e" .
            "676f6f676c652e6c6f6e6772756e6e696e672e4f7065726174696f6e22ab" .
            "0182d3e493022f22242f76312f73657276696365732f7b73657276696365" .
            "5f6e616d657d2f726f6c6c6f7574733a07726f6c6c6f7574da4114736572" .
            "766963655f6e616d652c726f6c6c6f7574ca415c0a27676f6f676c652e61" .
            "70692e736572766963656d616e6167656d656e742e76312e526f6c6c6f75" .
            "741231676f6f676c652e6170692e736572766963656d616e6167656d656e" .
            "742e76312e4f7065726174696f6e4d6574616461746112d9010a1447656e" .
            "6572617465436f6e6669675265706f7274123c2e676f6f676c652e617069" .
            "2e736572766963656d616e6167656d656e742e76312e47656e6572617465" .
            "436f6e6669675265706f7274526571756573741a3d2e676f6f676c652e61" .
            "70692e736572766963656d616e6167656d656e742e76312e47656e657261" .
            "7465436f6e6669675265706f7274526573706f6e7365224482d3e4930226" .
            "22212f76312f73657276696365733a67656e6572617465436f6e66696752" .
            "65706f72743a012ada41156e65775f636f6e6669672c6f6c645f636f6e66" .
            "696712a0020a0d456e61626c655365727669636512352e676f6f676c652e" .
            "6170692e736572766963656d616e6167656d656e742e76312e456e61626c" .
            "6553657276696365526571756573741a1d2e676f6f676c652e6c6f6e6772" .
            "756e6e696e672e4f7065726174696f6e22b80188020182d3e49302272222" .
            "2f76312f73657276696365732f7b736572766963655f6e616d657d3a656e" .
            "61626c653a012ada4118736572766963655f6e616d652c636f6e73756d65" .
            "725f6964ca416a0a35676f6f676c652e6170692e736572766963656d616e" .
            "6167656d656e742e76312e456e61626c6553657276696365526573706f6e" .
            "73651231676f6f676c652e6170692e736572766963656d616e6167656d65" .
            "6e742e76312e4f7065726174696f6e4d6574616461746112a4020a0e4469" .
            "7361626c655365727669636512362e676f6f676c652e6170692e73657276" .
            "6963656d616e6167656d656e742e76312e44697361626c65536572766963" .
            "65526571756573741a1d2e676f6f676c652e6c6f6e6772756e6e696e672e" .
            "4f7065726174696f6e22ba0188020182d3e493022822232f76312f736572" .
            "76696365732f7b736572766963655f6e616d657d3a64697361626c653a01" .
            "2ada4118736572766963655f6e616d652c636f6e73756d65725f6964ca41" .
            "6b0a36676f6f676c652e6170692e736572766963656d616e6167656d656e" .
            "742e76312e44697361626c6553657276696365526573706f6e7365123167" .
            "6f6f676c652e6170692e736572766963656d616e6167656d656e742e7631" .
            "2e4f7065726174696f6e4d657461646174611afd01ca4120736572766963" .
            "656d616e6167656d656e742e676f6f676c65617069732e636f6dd241d601" .
            "68747470733a2f2f7777772e676f6f676c65617069732e636f6d2f617574" .
            "682f636c6f75642d706c6174666f726d2c68747470733a2f2f7777772e67" .
            "6f6f676c65617069732e636f6d2f617574682f636c6f75642d706c617466" .
            "6f726d2e726561642d6f6e6c792c68747470733a2f2f7777772e676f6f67" .
            "6c65617069732e636f6d2f617574682f736572766963652e6d616e616765" .
            "6d656e742c68747470733a2f2f7777772e676f6f676c65617069732e636f" .
            "6d2f617574682f736572766963652e6d616e6167656d656e742e72656164" .
            "6f6e6c7942dd010a23636f6d2e676f6f676c652e6170692e736572766963" .
            "656d616e6167656d656e742e76314213536572766963654d616e61676572" .
            "50726f746f50015a50676f6f676c652e676f6c616e672e6f72672f67656e" .
            "70726f746f2f676f6f676c65617069732f6170692f736572766963656d61" .
            "6e6167656d656e742f76313b736572766963656d616e6167656d656e74a2" .
            "02044741534daa0221476f6f676c652e436c6f75642e536572766963654d" .
            "616e6167656d656e742e5631ca0221476f6f676c655c436c6f75645c5365" .
            "72766963654d616e6167656d656e745c5631620670726f746f33"
        ), true);

        static::$is_initialized = true;
    }
}

