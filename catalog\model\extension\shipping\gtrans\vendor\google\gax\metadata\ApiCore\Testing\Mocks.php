<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: ApiCore/Testing/mocks.proto

namespace GPBMetadata\ApiCore\Testing;

class Mocks
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        \GPBMetadata\Google\Protobuf\FieldMask::initOnce();
        \GPBMetadata\Google\Protobuf\Timestamp::initOnce();
        \GPBMetadata\Google\Protobuf\Duration::initOnce();
        \GPBMetadata\Google\Protobuf\Struct::initOnce();
        \GPBMetadata\Google\Protobuf\Wrappers::initOnce();
        $pool->internalAddGeneratedFile(hex2bin(
            "0aeb070a0b6d6f636b732e70726f746f1216676f6f676c652e617069636f" .
            "72652e74657374696e671a1f676f6f676c652f70726f746f6275662f7469" .
            "6d657374616d702e70726f746f1a1e676f6f676c652f70726f746f627566" .
            "2f6475726174696f6e2e70726f746f1a1c676f6f676c652f70726f746f62" .
            "75662f7374727563742e70726f746f1a1e676f6f676c652f70726f746f62" .
            "75662f77726170706572732e70726f746f22340a0b4d6f636b5265717565" .
            "737412120a0a706167655f746f6b656e18012001280912110a0970616765" .
            "5f73697a65180220012804225d0a0c4d6f636b526573706f6e7365120c0a" .
            "046e616d65180120012809120e0a066e756d62657218022001280412160a" .
            "0e7265736f75726365735f6c69737418032003280912170a0f6e6578745f" .
            "706167655f746f6b656e180420012809228c050a0f4d6f636b5265717565" .
            "7374426f6479120c0a046e616d65180120012809120e0a066e756d626572" .
            "18022001280412160a0e72657065617465645f6669656c64180320032809" .
            "123f0a0e6e65737465645f6d65737361676518042001280b32272e676f6f" .
            "676c652e617069636f72652e74657374696e672e4d6f636b526571756573" .
            "74426f647912300a0b62797465735f76616c756518052001280b321b2e67" .
            "6f6f676c652e70726f746f6275662e427974657356616c756512310a0e64" .
            "75726174696f6e5f76616c756518062001280b32192e676f6f676c652e70" .
            "726f746f6275662e4475726174696f6e122e0a0a6669656c645f6d61736b" .
            "18072001280b321a2e676f6f676c652e70726f746f6275662e4669656c64" .
            "4d61736b12300a0b696e7436345f76616c756518082001280b321b2e676f" .
            "6f676c652e70726f746f6275662e496e74363456616c7565122e0a0a6c69" .
            "73745f76616c756518092001280b321a2e676f6f676c652e70726f746f62" .
            "75662e4c69737456616c756512320a0c737472696e675f76616c7565180a" .
            "2001280b321c2e676f6f676c652e70726f746f6275662e537472696e6756" .
            "616c7565122d0a0c7374727563745f76616c7565180b2001280b32172e67" .
            "6f6f676c652e70726f746f6275662e53747275637412330a0f74696d6573" .
            "74616d705f76616c7565180c2001280b321a2e676f6f676c652e70726f74" .
            "6f6275662e54696d657374616d70122b0a0b76616c75655f76616c756518" .
            "0d2001280b32162e676f6f676c652e70726f746f6275662e56616c756512" .
            "110a076669656c645f31180e20012809480012110a076669656c645f3218" .
            "0f20012809480012110a076669656c645f331810200128094800420d0a0b" .
            "6f6e656f665f6669656c644219ca0216476f6f676c655c417069436f7265" .
            "5c54657374696e67620670726f746f33"
        ));

        static::$is_initialized = true;
    }
}

