{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="submit" form="project\project-form" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fas fa-save"></i></button>
        <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-light"><i class="fas fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    
    <div class="card">
      <div class="card-header"><i class="fas fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}chart-bar{% elif screen_type == 'settings' %}cog{% else %}pencil-alt{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="card-body">
        <form id="project\project-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-active_projects">{{ text_active_projects }}</label>
            <div class="col-sm-10">
              <input type="text" name="active_projects" value="{{ active_projects }}" placeholder="{{ text_active_projects }}" id="input-active_projects" class="form-control" />
              {% if error_active_projects %}
                <div class="invalid-feedback">{{ error_active_projects }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-gantt_data">{{ text_gantt_data }}</label>
            <div class="col-sm-10">
              <input type="text" name="gantt_data" value="{{ gantt_data }}" placeholder="{{ text_gantt_data }}" id="input-gantt_data" class="form-control" />
              {% if error_gantt_data %}
                <div class="invalid-feedback">{{ error_gantt_data }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="invalid-feedback">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-overdue_tasks">{{ text_overdue_tasks }}</label>
            <div class="col-sm-10">
              <input type="text" name="overdue_tasks" value="{{ overdue_tasks }}" placeholder="{{ text_overdue_tasks }}" id="input-overdue_tasks" class="form-control" />
              {% if error_overdue_tasks %}
                <div class="invalid-feedback">{{ error_overdue_tasks }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-performance_metrics">{{ text_performance_metrics }}</label>
            <div class="col-sm-10">
              <input type="text" name="performance_metrics" value="{{ performance_metrics }}" placeholder="{{ text_performance_metrics }}" id="input-performance_metrics" class="form-control" />
              {% if error_performance_metrics %}
                <div class="invalid-feedback">{{ error_performance_metrics }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-project">{{ text_project }}</label>
            <div class="col-sm-10">
              <input type="text" name="project" value="{{ project }}" placeholder="{{ text_project }}" id="input-project" class="form-control" />
              {% if error_project %}
                <div class="invalid-feedback">{{ error_project }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-project_budget">{{ text_project_budget }}</label>
            <div class="col-sm-10">
              <input type="text" name="project_budget" value="{{ project_budget }}" placeholder="{{ text_project_budget }}" id="input-project_budget" class="form-control" />
              {% if error_project_budget %}
                <div class="invalid-feedback">{{ error_project_budget }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-project_documents">{{ text_project_documents }}</label>
            <div class="col-sm-10">
              <input type="text" name="project_documents" value="{{ project_documents }}" placeholder="{{ text_project_documents }}" id="input-project_documents" class="form-control" />
              {% if error_project_documents %}
                <div class="invalid-feedback">{{ error_project_documents }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-project_milestones">{{ text_project_milestones }}</label>
            <div class="col-sm-10">
              <input type="text" name="project_milestones" value="{{ project_milestones }}" placeholder="{{ text_project_milestones }}" id="input-project_milestones" class="form-control" />
              {% if error_project_milestones %}
                <div class="invalid-feedback">{{ error_project_milestones }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-project_risks">{{ text_project_risks }}</label>
            <div class="col-sm-10">
              <input type="text" name="project_risks" value="{{ project_risks }}" placeholder="{{ text_project_risks }}" id="input-project_risks" class="form-control" />
              {% if error_project_risks %}
                <div class="invalid-feedback">{{ error_project_risks }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-project_stats">{{ text_project_stats }}</label>
            <div class="col-sm-10">
              <input type="text" name="project_stats" value="{{ project_stats }}" placeholder="{{ text_project_stats }}" id="input-project_stats" class="form-control" />
              {% if error_project_stats %}
                <div class="invalid-feedback">{{ error_project_stats }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-project_tasks">{{ text_project_tasks }}</label>
            <div class="col-sm-10">
              <input type="text" name="project_tasks" value="{{ project_tasks }}" placeholder="{{ text_project_tasks }}" id="input-project_tasks" class="form-control" />
              {% if error_project_tasks %}
                <div class="invalid-feedback">{{ error_project_tasks }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-project_timeline">{{ text_project_timeline }}</label>
            <div class="col-sm-10">
              <input type="text" name="project_timeline" value="{{ project_timeline }}" placeholder="{{ text_project_timeline }}" id="input-project_timeline" class="form-control" />
              {% if error_project_timeline %}
                <div class="invalid-feedback">{{ error_project_timeline }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-recent_activities">{{ text_recent_activities }}</label>
            <div class="col-sm-10">
              <input type="text" name="recent_activities" value="{{ recent_activities }}" placeholder="{{ text_recent_activities }}" id="input-recent_activities" class="form-control" />
              {% if error_recent_activities %}
                <div class="invalid-feedback">{{ error_recent_activities }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-team_members">{{ text_team_members }}</label>
            <div class="col-sm-10">
              <input type="text" name="team_members" value="{{ team_members }}" placeholder="{{ text_team_members }}" id="input-team_members" class="form-control" />
              {% if error_team_members %}
                <div class="invalid-feedback">{{ error_team_members }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-team_workload">{{ text_team_workload }}</label>
            <div class="col-sm-10">
              <input type="text" name="team_workload" value="{{ team_workload }}" placeholder="{{ text_team_workload }}" id="input-team_workload" class="form-control" />
              {% if error_team_workload %}
                <div class="invalid-feedback">{{ error_team_workload }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-upcoming_milestones">{{ text_upcoming_milestones }}</label>
            <div class="col-sm-10">
              <input type="text" name="upcoming_milestones" value="{{ upcoming_milestones }}" placeholder="{{ text_upcoming_milestones }}" id="input-upcoming_milestones" class="form-control" />
              {% if error_upcoming_milestones %}
                <div class="invalid-feedback">{{ error_upcoming_milestones }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="invalid-feedback">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}