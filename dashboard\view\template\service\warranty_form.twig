{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="submit" form="form-warranty" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fa fa-save"></i></button>
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a></div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    {% if error_customer %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_customer }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    {% if error_product %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_product }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    {% if error_warranty_period %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warranty_period }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pencil"></i> {{ text_form }}</h3>
      </div>
      <div class="panel-body">
        <form action="{{ action }}" method="post" enctype="multipart/form-data" id="form-warranty" class="form-horizontal">
          <ul class="nav nav-tabs">
            <li class="active"><a href="#tab-general" data-toggle="tab">{{ tab_general }}</a></li>
            <li><a href="#tab-details" data-toggle="tab">{{ tab_details }}</a></li>
          </ul>
          <div class="tab-content">
            <div class="tab-pane active" id="tab-general">
              <div class="form-group required">
                <label class="col-sm-2 control-label" for="input-warranty-number">{{ entry_warranty_number }}</label>
                <div class="col-sm-10">
                  <input type="text" name="warranty_number" value="{{ warranty_number }}" placeholder="{{ entry_warranty_number }}" id="input-warranty-number" class="form-control" />
                  {% if error_warranty_number %}
                  <div class="text-danger">{{ error_warranty_number }}</div>
                  {% endif %}
                </div>
              </div>
              <div class="form-group required">
                <label class="col-sm-2 control-label" for="input-customer">{{ entry_customer }}</label>
                <div class="col-sm-10">
                  <input type="text" name="customer" value="{{ customer }}" placeholder="{{ entry_customer }}" id="input-customer" class="form-control" />
                  <input type="hidden" name="customer_id" value="{{ customer_id }}" />
                  {% if error_customer %}
                  <div class="text-danger">{{ error_customer }}</div>
                  {% endif %}
                </div>
              </div>
              <div class="form-group required">
                <label class="col-sm-2 control-label" for="input-product">{{ entry_product }}</label>
                <div class="col-sm-10">
                  <input type="text" name="product" value="{{ product }}" placeholder="{{ entry_product }}" id="input-product" class="form-control" />
                  <input type="hidden" name="product_id" value="{{ product_id }}" />
                  {% if error_product %}
                  <div class="text-danger">{{ error_product }}</div>
                  {% endif %}
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-order-id">{{ entry_order_id }}</label>
                <div class="col-sm-10">
                  <input type="text" name="order_id" value="{{ order_id }}" placeholder="{{ entry_order_id }}" id="input-order-id" class="form-control" />
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-warranty-type">{{ entry_warranty_type }}</label>
                <div class="col-sm-10">
                  <select name="warranty_type" id="input-warranty-type" class="form-control">
                    <option value="standard"{% if warranty_type == 'standard' %} selected="selected"{% endif %}>{{ text_standard_warranty }}</option>
                    <option value="extended"{% if warranty_type == 'extended' %} selected="selected"{% endif %}>{{ text_extended_warranty }}</option>
                    <option value="premium"{% if warranty_type == 'premium' %} selected="selected"{% endif %}>{{ text_premium_warranty }}</option>
                    <option value="custom"{% if warranty_type == 'custom' %} selected="selected"{% endif %}>{{ text_custom_warranty }}</option>
                  </select>
                </div>
              </div>
            </div>
            <div class="tab-pane" id="tab-details">
              <div class="form-group required">
                <label class="col-sm-2 control-label" for="input-warranty-period">{{ entry_warranty_period }}</label>
                <div class="col-sm-6">
                  <input type="number" name="warranty_period" value="{{ warranty_period }}" placeholder="{{ entry_warranty_period }}" id="input-warranty-period" class="form-control" min="1" />
                  {% if error_warranty_period %}
                  <div class="text-danger">{{ error_warranty_period }}</div>
                  {% endif %}
                </div>
                <div class="col-sm-4">
                  <select name="period_unit" id="input-period-unit" class="form-control">
                    <option value="days"{% if period_unit == 'days' %} selected="selected"{% endif %}>{{ text_days }}</option>
                    <option value="weeks"{% if period_unit == 'weeks' %} selected="selected"{% endif %}>{{ text_weeks }}</option>
                    <option value="months"{% if period_unit == 'months' %} selected="selected"{% endif %}>{{ text_months }}</option>
                    <option value="years"{% if period_unit == 'years' %} selected="selected"{% endif %}>{{ text_years }}</option>
                  </select>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-start-date">{{ entry_start_date }}</label>
                <div class="col-sm-10">
                  <div class="input-group date">
                    <input type="text" name="start_date" value="{{ start_date }}" placeholder="{{ entry_start_date }}" data-date-format="YYYY-MM-DD" id="input-start-date" class="form-control" />
                    <span class="input-group-btn">
                    <button class="btn btn-default" type="button"><i class="fa fa-calendar"></i></button>
                    </span></div>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-description">{{ entry_description }}</label>
                <div class="col-sm-10">
                  <textarea name="description" rows="3" placeholder="{{ entry_description }}" id="input-description" class="form-control">{{ description }}</textarea>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-terms-conditions">{{ entry_terms_conditions }}</label>
                <div class="col-sm-10">
                  <textarea name="terms_conditions" rows="5" placeholder="{{ entry_terms_conditions }}" id="input-terms-conditions" class="form-control">{{ terms_conditions }}</textarea>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-status">{{ entry_status }}</label>
                <div class="col-sm-10">
                  <label class="radio-inline">
                    {% if status %}
                    <input type="radio" name="status" value="1" checked="checked" />
                    {% else %}
                    <input type="radio" name="status" value="1" />
                    {% endif %}
                    {{ text_enabled }}
                  </label>
                  <label class="radio-inline">
                    {% if not status %}
                    <input type="radio" name="status" value="0" checked="checked" />
                    {% else %}
                    <input type="radio" name="status" value="0" />
                    {% endif %}
                    {{ text_disabled }}
                  </label>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript">
$('input[name=\'customer\']').autocomplete({
    'source': function(request, response) {
        $.ajax({
            url: 'index.php?route=customer/customer/autocomplete&user_token={{ user_token }}&filter_name=' + encodeURIComponent(request),
            dataType: 'json',
            success: function(json) {
                response($.map(json, function(item) {
                    return {
                        label: item['name'],
                        value: item['customer_id']
                    }
                }));
            }
        });
    },
    'select': function(item) {
        $('input[name=\'customer\']').val(item['label']);
        $('input[name=\'customer_id\']').val(item['value']);
    }
});

$('input[name=\'product\']').autocomplete({
    'source': function(request, response) {
        $.ajax({
            url: 'index.php?route=catalog/product/autocomplete&user_token={{ user_token }}&filter_name=' + encodeURIComponent(request),
            dataType: 'json',
            success: function(json) {
                response($.map(json, function(item) {
                    return {
                        label: item['name'],
                        value: item['product_id']
                    }
                }));
            }
        });
    },
    'select': function(item) {
        $('input[name=\'product\']').val(item['label']);
        $('input[name=\'product_id\']').val(item['value']);
    }
});

$('.date').datetimepicker({
    pickTime: false
});
</script>
{{ footer }} 