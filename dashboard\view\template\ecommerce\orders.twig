{{ header }}
{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="button" data-bs-toggle="tooltip" title="{{ button_filter }}" onclick="$('#filter-orders').toggleClass('d-none');" class="btn btn-light d-md-none d-lg-none"><i class="fas fa-filter"></i></button>
        <button type="button" data-bs-toggle="tooltip" title="{{ button_sync_orders }}" onclick="syncOrders()" class="btn btn-primary"><i class="fas fa-sync"></i></button>
        <button type="button" data-bs-toggle="tooltip" title="{{ button_bulk_actions }}" onclick="$('#bulk-actions').toggleClass('d-none');" class="btn btn-secondary"><i class="fas fa-tasks"></i></button>
        <a href="{{ export }}" data-bs-toggle="tooltip" title="{{ button_export }}" class="btn btn-success"><i class="fas fa-file-excel"></i></a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ol class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
          <li class="breadcrumb-item"><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    
    <!-- إحصائيات الطلبات -->
    <div class="row mb-3">
      <div class="col-lg-3 col-md-6">
        <div class="card bg-primary text-white">
          <div class="card-body">
            <div class="d-flex justify-content-between">
              <div>
                <h4>{{ summary.total_orders|number_format }}</h4>
                <p class="mb-0">{{ text_total_orders }}</p>
              </div>
              <div class="align-self-center">
                <i class="fas fa-shopping-cart fa-2x"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-md-6">
        <div class="card bg-warning text-white">
          <div class="card-body">
            <div class="d-flex justify-content-between">
              <div>
                <h4>{{ summary.pending_orders|number_format }}</h4>
                <p class="mb-0">{{ text_pending_orders }}</p>
              </div>
              <div class="align-self-center">
                <i class="fas fa-clock fa-2x"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-md-6">
        <div class="card bg-info text-white">
          <div class="card-body">
            <div class="d-flex justify-content-between">
              <div>
                <h4>{{ summary.processing_orders|number_format }}</h4>
                <p class="mb-0">{{ text_processing_orders }}</p>
              </div>
              <div class="align-self-center">
                <i class="fas fa-cogs fa-2x"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-md-6">
        <div class="card bg-success text-white">
          <div class="card-body">
            <div class="d-flex justify-content-between">
              <div>
                <h4>{{ summary.total_sales|number_format(2) }}</h4>
                <p class="mb-0">{{ text_total_sales }}</p>
              </div>
              <div class="align-self-center">
                <i class="fas fa-dollar-sign fa-2x"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- إحصائيات إضافية -->
    <div class="row mb-3">
      <div class="col-lg-6">
        <div class="card">
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <h5 class="mb-1">{{ text_average_order_value }}</h5>
                <p class="text-muted mb-0">{{ text_per_order }}</p>
              </div>
              <div class="text-end">
                <h3 class="mb-0 text-info">{{ summary.average_order_value|number_format(2) }}</h3>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-lg-6">
        <div class="card">
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <h5 class="mb-1">{{ text_orders_today }}</h5>
                <p class="text-muted mb-0">{{ text_sales_today }}: {{ summary.sales_today|number_format(2) }}</p>
              </div>
              <div class="text-end">
                <h3 class="mb-0 text-success">{{ summary.orders_today|number_format }}</h3>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="row">
      <!-- فلاتر الطلبات -->
      <div id="filter-orders" class="col-lg-3 col-md-12 order-lg-last d-none d-lg-block mb-3">
        <div class="card">
          <div class="card-header"><i class="fas fa-filter"></i> {{ text_filter }}</div>
          <div class="card-body">
            <div class="mb-3">
              <label for="input-order-id" class="form-label">{{ entry_order_id }}</label>
              <input type="text" name="filter_order_id" value="{{ filter_order_id }}" placeholder="{{ entry_order_id }}" id="input-order-id" class="form-control"/>
            </div>
            <div class="mb-3">
              <label for="input-customer" class="form-label">{{ entry_customer }}</label>
              <input type="text" name="filter_customer" value="{{ filter_customer }}" placeholder="{{ entry_customer }}" id="input-customer" class="form-control"/>
            </div>
            <div class="mb-3">
              <label for="input-order-status" class="form-label">{{ entry_order_status }}</label>
              <select name="filter_order_status_id" id="input-order-status" class="form-select">
                <option value="">{{ text_all_statuses }}</option>
                {% for order_status in order_statuses %}
                  <option value="{{ order_status.order_status_id }}" {% if order_status.order_status_id == filter_order_status_id %}selected="selected"{% endif %}>{{ order_status.name }}</option>
                {% endfor %}
              </select>
            </div>
            <div class="mb-3">
              <label for="input-payment-method" class="form-label">{{ entry_payment_method }}</label>
              <select name="filter_payment_method" id="input-payment-method" class="form-select">
                <option value="">{{ text_all_payment_methods }}</option>
                {% for key, value in payment_methods %}
                  <option value="{{ key }}" {% if key == filter_payment_method %}selected="selected"{% endif %}>{{ value }}</option>
                {% endfor %}
              </select>
            </div>
            <div class="mb-3">
              <label for="input-shipping-method" class="form-label">{{ entry_shipping_method }}</label>
              <select name="filter_shipping_method" id="input-shipping-method" class="form-select">
                <option value="">{{ text_all_shipping_methods }}</option>
                {% for key, value in shipping_methods %}
                  <option value="{{ key }}" {% if key == filter_shipping_method %}selected="selected"{% endif %}>{{ value }}</option>
                {% endfor %}
              </select>
            </div>
            <div class="mb-3">
              <label for="input-total-min" class="form-label">{{ entry_total_min }}</label>
              <input type="number" name="filter_total_min" value="{{ filter_total_min }}" placeholder="{{ entry_total_min }}" id="input-total-min" class="form-control" step="0.01"/>
            </div>
            <div class="mb-3">
              <label for="input-total-max" class="form-label">{{ entry_total_max }}</label>
              <input type="number" name="filter_total_max" value="{{ filter_total_max }}" placeholder="{{ entry_total_max }}" id="input-total-max" class="form-control" step="0.01"/>
            </div>
            <div class="mb-3">
              <label for="input-date-start" class="form-label">{{ entry_date_start }}</label>
              <input type="date" name="filter_date_start" value="{{ filter_date_start }}" id="input-date-start" class="form-control"/>
            </div>
            <div class="mb-3">
              <label for="input-date-end" class="form-label">{{ entry_date_end }}</label>
              <input type="date" name="filter_date_end" value="{{ filter_date_end }}" id="input-date-end" class="form-control"/>
            </div>
            <div class="text-end">
              <button type="button" id="button-filter" class="btn btn-light"><i class="fas fa-filter"></i> {{ button_filter }}</button>
            </div>
          </div>
        </div>
      </div>
      
      <!-- قائمة الطلبات -->
      <div class="col-lg-9 col-md-12">
        <!-- الإجراءات المجمعة -->
        <div id="bulk-actions" class="card mb-3 d-none">
          <div class="card-header"><i class="fas fa-tasks"></i> {{ text_bulk_actions }}</div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-8">
                <select id="bulk-action-select" class="form-select">
                  <option value="">{{ text_select_action }}</option>
                  <option value="confirm">{{ button_bulk_confirm }}</option>
                  <option value="ship">{{ button_bulk_ship }}</option>
                  <option value="complete">{{ button_bulk_complete }}</option>
                  <option value="cancel">{{ button_bulk_cancel }}</option>
                  <option value="export">{{ button_bulk_export }}</option>
                </select>
              </div>
              <div class="col-md-4">
                <button type="button" id="button-bulk-execute" class="btn btn-primary w-100">{{ button_execute }}</button>
              </div>
            </div>
          </div>
        </div>
        
        <div class="card">
          <div class="card-header">
            <i class="fas fa-shopping-cart"></i> {{ text_list }}
            <div class="card-tools">
              <button type="button" class="btn btn-sm btn-outline-secondary" onclick="location.reload();">
                <i class="fas fa-sync"></i> {{ button_refresh }}
              </button>
            </div>
          </div>
          <div class="card-body">
            {% if orders %}
            <div class="table-responsive">
              <table class="table table-bordered table-hover">
                <thead>
                  <tr>
                    <td class="text-center" width="1">
                      <input type="checkbox" onclick="$('input[name*=\'selected\']').prop('checked', this.checked);"/>
                    </td>
                    <td><a href="{{ sort_order_id }}" {% if sort == 'o.order_id' %}class="{% if order == 'ASC' %}asc{% else %}desc{% endif %}"{% endif %}>{{ column_order_id }}</a></td>
                    <td><a href="{{ sort_customer }}" {% if sort == 'customer_name' %}class="{% if order == 'ASC' %}asc{% else %}desc{% endif %}"{% endif %}>{{ column_customer }}</a></td>
                    <td class="text-center">{{ column_items_count }}</td>
                    <td class="text-center">{{ column_payment_status }}</td>
                    <td class="text-center">{{ column_shipping_status }}</td>
                    <td class="text-center"><a href="{{ sort_status }}" {% if sort == 'status' %}class="{% if order == 'ASC' %}asc{% else %}desc{% endif %}"{% endif %}>{{ column_status }}</a></td>
                    <td class="text-center"><a href="{{ sort_total }}" {% if sort == 'o.total' %}class="{% if order == 'ASC' %}asc{% else %}desc{% endif %}"{% endif %}>{{ column_total }}</a></td>
                    <td class="text-center"><a href="{{ sort_date_added }}" {% if sort == 'o.date_added' %}class="{% if order == 'ASC' %}asc{% else %}desc{% endif %}"{% endif %}>{{ column_date_added }}</a></td>
                    <td class="text-center">{{ column_action }}</td>
                  </tr>
                </thead>
                <tbody>
                  {% for order in orders %}
                  <tr id="order-row-{{ order.order_id }}">
                    <td class="text-center">
                      <input type="checkbox" name="selected[]" value="{{ order.order_id }}"/>
                    </td>
                    <td>
                      <a href="{{ order.actions[0].href }}" class="fw-bold">#{{ order.order_id }}</a>
                      {% if order.is_urgent %}
                        <span class="badge bg-danger ms-1">{{ text_urgent }}</span>
                      {% endif %}
                      <br><small class="text-muted">{{ order.source }}</small>
                    </td>
                    <td>
                      <strong>{{ order.customer_name }}</strong>
                      <br><small class="text-muted">{{ order.customer_email }}</small>
                      {% if order.customer_phone %}
                        <br><small class="text-muted">{{ order.customer_phone }}</small>
                      {% endif %}
                    </td>
                    <td class="text-center">
                      <span class="badge bg-info">{{ order.items_count }}</span>
                    </td>
                    <td class="text-center">
                      <span class="badge bg-{{ order.payment_status_class }}">{{ order.payment_status_text }}</span>
                    </td>
                    <td class="text-center">
                      <span class="badge bg-{{ order.shipping_status_class }}">{{ order.shipping_status_text }}</span>
                    </td>
                    <td class="text-center">
                      <span class="badge bg-{{ order.status_class }}">{{ order.status_text }}</span>
                    </td>
                    <td class="text-center">
                      <strong>{{ order.total }}</strong>
                    </td>
                    <td class="text-center">
                      {{ order.date_added }}
                      {% if order.date_modified %}
                        <br><small class="text-muted">{{ text_modified }}: {{ order.date_modified }}</small>
                      {% endif %}
                    </td>
                    <td class="text-center">
                      <div class="btn-group btn-group-sm">
                        {% for action in order.actions %}
                          {% if action.href starts with 'javascript:' %}
                            <button type="button" class="{{ action.class }}" onclick="{{ action.href|replace({'javascript:': ''}) }}" data-bs-toggle="tooltip" title="{{ action.text }}">
                              <i class="fas fa-{% if 'view' in action.text %}eye{% elseif 'edit' in action.text %}pencil-alt{% elseif 'confirm' in action.text %}check{% elseif 'ship' in action.text %}truck{% else %}cog{% endif %}"></i>
                            </button>
                          {% else %}
                            <a href="{{ action.href }}" class="{{ action.class }}" data-bs-toggle="tooltip" title="{{ action.text }}">
                              <i class="fas fa-{% if 'view' in action.text %}eye{% elseif 'edit' in action.text %}pencil-alt{% else %}cog{% endif %}"></i>
                            </a>
                          {% endif %}
                        {% endfor %}
                      </div>
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
            <div class="row">
              <div class="col-sm-6 text-start">{{ pagination }}</div>
              <div class="col-sm-6 text-end">{{ results }}</div>
            </div>
            {% else %}
            <div class="text-center">
              <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
              <h4>{{ text_no_results }}</h4>
              <p class="text-muted">{{ text_no_orders_message }}</p>
              <button type="button" class="btn btn-primary" onclick="syncOrders()">
                <i class="fas fa-sync"></i> {{ button_sync_orders_now }}
              </button>
            </div>
            {% endif %}
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript">
// تطبيق الفلاتر
$('#button-filter').on('click', function() {
    var url = 'index.php?route=ecommerce/orders&user_token={{ user_token }}';
    
    var filter_order_id = $('input[name=\'filter_order_id\']').val();
    if (filter_order_id) {
        url += '&filter_order_id=' + filter_order_id;
    }
    
    var filter_customer = $('input[name=\'filter_customer\']').val();
    if (filter_customer) {
        url += '&filter_customer=' + encodeURIComponent(filter_customer);
    }
    
    var filter_order_status_id = $('select[name=\'filter_order_status_id\']').val();
    if (filter_order_status_id) {
        url += '&filter_order_status_id=' + filter_order_status_id;
    }
    
    var filter_payment_method = $('select[name=\'filter_payment_method\']').val();
    if (filter_payment_method) {
        url += '&filter_payment_method=' + encodeURIComponent(filter_payment_method);
    }
    
    var filter_shipping_method = $('select[name=\'filter_shipping_method\']').val();
    if (filter_shipping_method) {
        url += '&filter_shipping_method=' + encodeURIComponent(filter_shipping_method);
    }
    
    var filter_total_min = $('input[name=\'filter_total_min\']').val();
    if (filter_total_min) {
        url += '&filter_total_min=' + filter_total_min;
    }
    
    var filter_total_max = $('input[name=\'filter_total_max\']').val();
    if (filter_total_max) {
        url += '&filter_total_max=' + filter_total_max;
    }
    
    var filter_date_start = $('input[name=\'filter_date_start\']').val();
    if (filter_date_start) {
        url += '&filter_date_start=' + filter_date_start;
    }
    
    var filter_date_end = $('input[name=\'filter_date_end\']').val();
    if (filter_date_end) {
        url += '&filter_date_end=' + filter_date_end;
    }
    
    location = url;
});

// مزامنة الطلبات
function syncOrders() {
    if (confirm('{{ text_confirm_sync_orders }}')) {
        $.ajax({
            url: '{{ sync_orders }}',
            type: 'GET',
            dataType: 'json',
            beforeSend: function() {
                $('button[onclick="syncOrders()"]').html('<i class="fas fa-spinner fa-spin"></i> {{ text_syncing }}');
            },
            success: function(json) {
                if (json.success) {
                    alert(json.success);
                    location.reload();
                } else {
                    alert(json.error);
                }
            },
            complete: function() {
                $('button[onclick="syncOrders()"]').html('<i class="fas fa-sync"></i> {{ button_sync_orders }}');
            }
        });
    }
}

// تأكيد الطلب
function confirmOrder(orderId) {
    if (confirm('{{ text_confirm_order }}')) {
        $.ajax({
            url: 'index.php?route=ecommerce/orders/confirmOrder&user_token={{ user_token }}',
            type: 'POST',
            data: {order_id: orderId},
            dataType: 'json',
            success: function(json) {
                if (json.success) {
                    location.reload();
                } else {
                    alert(json.error);
                }
            }
        });
    }
}

// شحن الطلب
function shipOrder(orderId) {
    if (confirm('{{ text_confirm_ship_order }}')) {
        $.ajax({
            url: 'index.php?route=ecommerce/orders/shipOrder&user_token={{ user_token }}',
            type: 'POST',
            data: {order_id: orderId},
            dataType: 'json',
            success: function(json) {
                if (json.success) {
                    location.reload();
                } else {
                    alert(json.error);
                }
            }
        });
    }
}

// تنفيذ الإجراءات المجمعة
$('#button-bulk-execute').on('click', function() {
    var action = $('#bulk-action-select').val();
    var selected = $('input[name*=\'selected\']:checked').map(function() {
        return this.value;
    }).get();
    
    if (!action) {
        alert('{{ error_no_action_selected }}');
        return;
    }
    
    if (selected.length === 0) {
        alert('{{ error_no_orders_selected }}');
        return;
    }
    
    if (confirm('{{ text_confirm_bulk_action }}')) {
        $.ajax({
            url: 'index.php?route=ecommerce/orders/bulk' + action.charAt(0).toUpperCase() + action.slice(1) + '&user_token={{ user_token }}',
            type: 'POST',
            data: {selected: selected},
            dataType: 'json',
            success: function(json) {
                if (json.success) {
                    location.reload();
                } else {
                    alert(json.error);
                }
            }
        });
    }
});
</script>

{{ footer }}
