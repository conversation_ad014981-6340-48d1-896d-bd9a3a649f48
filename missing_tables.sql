-- ملف إنشاء الجداول المفقودة في AYM ERP
-- تاريخ الإنشاء: 2025-07-28
-- الهدف: إصلاح أخطاء الجداول المفقودة في dashboard.php

-- ========================================
-- جداول الذكاء الاصطناعي (AI Tables)
-- ========================================

-- جدول التنبؤات بالذكاء الاصطناعي
CREATE TABLE IF NOT EXISTS `cod_ai_predictions` (
  `prediction_id` int(11) NOT NULL AUTO_INCREMENT,
  `prediction_type` varchar(50) NOT NULL COMMENT 'نوع التنبؤ (sales, demand, price)',
  `model_name` varchar(100) NOT NULL COMMENT 'اسم النموذج المستخدم',
  `input_data` text COMMENT 'البيانات المدخلة للنموذج',
  `predicted_value` decimal(15,4) NOT NULL COMMENT 'القيمة المتنبأ بها',
  `actual_value` decimal(15,4) DEFAULT NULL COMMENT 'القيمة الفعلية',
  `confidence_score` decimal(5,4) DEFAULT NULL COMMENT 'درجة الثقة',
  `prediction_date` datetime NOT NULL COMMENT 'تاريخ التنبؤ',
  `target_date` date NOT NULL COMMENT 'التاريخ المستهدف للتنبؤ',
  `status` enum('pending','validated','failed') DEFAULT 'pending',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`prediction_id`),
  KEY `idx_prediction_type` (`prediction_type`),
  KEY `idx_prediction_date` (`prediction_date`),
  KEY `idx_target_date` (`target_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول التنبؤات بالذكاء الاصطناعي';

-- جدول التوصيات بالذكاء الاصطناعي
CREATE TABLE IF NOT EXISTS `cod_ai_recommendations` (
  `recommendation_id` int(11) NOT NULL AUTO_INCREMENT,
  `recommendation_type` varchar(50) NOT NULL COMMENT 'نوع التوصية',
  `target_entity` varchar(50) NOT NULL COMMENT 'الكيان المستهدف',
  `target_id` int(11) NOT NULL COMMENT 'معرف الكيان المستهدف',
  `recommendation_text` text NOT NULL COMMENT 'نص التوصية',
  `confidence_score` decimal(5,4) NOT NULL COMMENT 'درجة الثقة',
  `priority` enum('low','normal','high','urgent') DEFAULT 'normal',
  `status` enum('pending','accepted','rejected','implemented') DEFAULT 'pending',
  `expected_impact` text COMMENT 'التأثير المتوقع',
  `implementation_cost` decimal(10,2) DEFAULT NULL COMMENT 'تكلفة التنفيذ',
  `date_created` datetime NOT NULL,
  `date_implemented` datetime DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `implemented_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`recommendation_id`),
  KEY `idx_recommendation_type` (`recommendation_type`),
  KEY `idx_target_entity` (`target_entity`, `target_id`),
  KEY `idx_status` (`status`),
  KEY `idx_date_created` (`date_created`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول التوصيات بالذكاء الاصطناعي';

-- جدول كشف الاحتيال بالذكاء الاصطناعي
CREATE TABLE IF NOT EXISTS `cod_ai_fraud_detection` (
  `detection_id` int(11) NOT NULL AUTO_INCREMENT,
  `transaction_type` varchar(50) NOT NULL COMMENT 'نوع المعاملة',
  `transaction_id` int(11) NOT NULL COMMENT 'معرف المعاملة',
  `fraud_score` decimal(5,4) NOT NULL COMMENT 'درجة الاحتيال (0-1)',
  `risk_level` enum('low','medium','high','critical') NOT NULL,
  `detection_method` varchar(100) NOT NULL COMMENT 'طريقة الكشف',
  `fraud_indicators` text COMMENT 'مؤشرات الاحتيال',
  `status` enum('flagged','investigating','confirmed','false_positive') DEFAULT 'flagged',
  `action_taken` text COMMENT 'الإجراء المتخذ',
  `detected_at` datetime NOT NULL,
  `resolved_at` datetime DEFAULT NULL,
  `resolved_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`detection_id`),
  KEY `idx_transaction` (`transaction_type`, `transaction_id`),
  KEY `idx_fraud_score` (`fraud_score`),
  KEY `idx_risk_level` (`risk_level`),
  KEY `idx_detected_at` (`detected_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول كشف الاحتيال بالذكاء الاصطناعي';

-- جدول تحليل المشاعر بالذكاء الاصطناعي
CREATE TABLE IF NOT EXISTS `cod_ai_sentiment_analysis` (
  `analysis_id` int(11) NOT NULL AUTO_INCREMENT,
  `content_type` varchar(50) NOT NULL COMMENT 'نوع المحتوى (review, feedback, comment)',
  `content_id` int(11) NOT NULL COMMENT 'معرف المحتوى',
  `text_content` text NOT NULL COMMENT 'النص المحلل',
  `sentiment_score` decimal(5,4) NOT NULL COMMENT 'درجة المشاعر (-1 إلى 1)',
  `sentiment_label` enum('negative','neutral','positive') NOT NULL,
  `confidence_score` decimal(5,4) NOT NULL COMMENT 'درجة الثقة',
  `emotions` text COMMENT 'المشاعر المكتشفة (JSON)',
  `keywords` text COMMENT 'الكلمات المفتاحية',
  `language` varchar(10) DEFAULT 'ar' COMMENT 'لغة النص',
  `analyzed_at` datetime NOT NULL,
  PRIMARY KEY (`analysis_id`),
  KEY `idx_content` (`content_type`, `content_id`),
  KEY `idx_sentiment` (`sentiment_label`, `sentiment_score`),
  KEY `idx_analyzed_at` (`analyzed_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول تحليل المشاعر بالذكاء الاصطناعي';

-- جدول تحسين الأسعار بالذكاء الاصطناعي
CREATE TABLE IF NOT EXISTS `cod_ai_price_optimization` (
  `optimization_id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL,
  `current_price` decimal(10,4) NOT NULL COMMENT 'السعر الحالي',
  `recommended_price` decimal(10,4) NOT NULL COMMENT 'السعر الموصى به',
  `price_change_percentage` decimal(5,2) NOT NULL COMMENT 'نسبة تغيير السعر',
  `expected_demand_change` decimal(5,2) DEFAULT NULL COMMENT 'التغيير المتوقع في الطلب',
  `expected_revenue_impact` decimal(10,2) DEFAULT NULL COMMENT 'التأثير المتوقع على الإيرادات',
  `optimization_factors` text COMMENT 'عوامل التحسين (JSON)',
  `market_conditions` text COMMENT 'ظروف السوق',
  `competitor_prices` text COMMENT 'أسعار المنافسين (JSON)',
  `status` enum('pending','approved','implemented','rejected') DEFAULT 'pending',
  `created_at` datetime NOT NULL,
  `implemented_at` datetime DEFAULT NULL,
  PRIMARY KEY (`optimization_id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول تحسين الأسعار بالذكاء الاصطناعي';

-- جدول التنبؤ بالطلب بالذكاء الاصطناعي
CREATE TABLE IF NOT EXISTS `cod_ai_demand_forecast` (
  `forecast_id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL,
  `forecast_period` varchar(20) NOT NULL COMMENT 'فترة التنبؤ (daily, weekly, monthly)',
  `forecast_date` date NOT NULL COMMENT 'تاريخ التنبؤ',
  `forecasted_demand` decimal(10,2) NOT NULL COMMENT 'الطلب المتنبأ به',
  `actual_demand` decimal(10,2) DEFAULT NULL COMMENT 'الطلب الفعلي',
  `accuracy_score` decimal(5,4) DEFAULT NULL COMMENT 'درجة الدقة',
  `forecast_method` varchar(50) NOT NULL COMMENT 'طريقة التنبؤ',
  `seasonal_factors` text COMMENT 'العوامل الموسمية (JSON)',
  `external_factors` text COMMENT 'العوامل الخارجية (JSON)',
  `confidence_interval_lower` decimal(10,2) DEFAULT NULL,
  `confidence_interval_upper` decimal(10,2) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`forecast_id`),
  KEY `idx_product_forecast` (`product_id`, `forecast_date`),
  KEY `idx_forecast_period` (`forecast_period`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول التنبؤ بالطلب بالذكاء الاصطناعي';

-- ========================================
-- جداول التدريب والموارد البشرية
-- ========================================

-- جدول تدريب الموظفين
CREATE TABLE IF NOT EXISTS `cod_employee_training` (
  `training_id` int(11) NOT NULL AUTO_INCREMENT,
  `employee_id` int(11) NOT NULL COMMENT 'معرف الموظف (من cod_user)',
  `training_program_id` int(11) DEFAULT NULL,
  `training_title` varchar(200) NOT NULL COMMENT 'عنوان التدريب',
  `training_type` enum('internal','external','online','workshop') NOT NULL,
  `training_category` varchar(100) DEFAULT NULL COMMENT 'فئة التدريب',
  `start_date` date NOT NULL,
  `end_date` date DEFAULT NULL,
  `duration_hours` decimal(5,2) DEFAULT NULL COMMENT 'مدة التدريب بالساعات',
  `trainer_name` varchar(100) DEFAULT NULL,
  `training_provider` varchar(200) DEFAULT NULL,
  `cost` decimal(10,2) DEFAULT NULL COMMENT 'تكلفة التدريب',
  `status` enum('scheduled','in_progress','completed','cancelled') DEFAULT 'scheduled',
  `completion_date` date DEFAULT NULL,
  `completion_percentage` decimal(5,2) DEFAULT 0 COMMENT 'نسبة الإنجاز',
  `assessment_score` decimal(5,2) DEFAULT NULL COMMENT 'درجة التقييم',
  `certification_obtained` tinyint(1) DEFAULT 0 COMMENT 'هل تم الحصول على شهادة',
  `feedback` text COMMENT 'تقييم الموظف للتدريب',
  `notes` text COMMENT 'ملاحظات إضافية',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`training_id`),
  KEY `idx_employee_id` (`employee_id`),
  KEY `idx_training_type` (`training_type`),
  KEY `idx_status` (`status`),
  KEY `idx_start_date` (`start_date`),
  KEY `idx_completion_date` (`completion_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول تدريب الموظفين';

-- ========================================
-- جداول التسويق الإلكتروني
-- ========================================

-- جدول حملات البريد الإلكتروني
CREATE TABLE IF NOT EXISTS `cod_email_campaign` (
  `campaign_id` int(11) NOT NULL AUTO_INCREMENT,
  `campaign_name` varchar(200) NOT NULL COMMENT 'اسم الحملة',
  `campaign_type` enum('promotional','newsletter','transactional','welcome','abandoned_cart') NOT NULL,
  `subject_line` varchar(255) NOT NULL COMMENT 'موضوع الرسالة',
  `email_content` longtext NOT NULL COMMENT 'محتوى الرسالة',
  `sender_name` varchar(100) NOT NULL,
  `sender_email` varchar(100) NOT NULL,
  `target_audience` text COMMENT 'الجمهور المستهدف (JSON)',
  `total_recipients` int(11) DEFAULT 0 COMMENT 'إجمالي المستقبلين',
  `emails_sent` int(11) DEFAULT 0 COMMENT 'الرسائل المرسلة',
  `emails_delivered` int(11) DEFAULT 0 COMMENT 'الرسائل المسلمة',
  `emails_opened` int(11) DEFAULT 0 COMMENT 'الرسائل المفتوحة',
  `emails_clicked` int(11) DEFAULT 0 COMMENT 'الرسائل التي تم النقر عليها',
  `emails_bounced` int(11) DEFAULT 0 COMMENT 'الرسائل المرتدة',
  `unsubscribes` int(11) DEFAULT 0 COMMENT 'إلغاء الاشتراكات',
  `spam_complaints` int(11) DEFAULT 0 COMMENT 'شكاوى الرسائل المزعجة',
  `conversion_count` int(11) DEFAULT 0 COMMENT 'عدد التحويلات',
  `revenue_generated` decimal(10,2) DEFAULT 0 COMMENT 'الإيرادات المحققة',
  `campaign_cost` decimal(10,2) DEFAULT 0 COMMENT 'تكلفة الحملة',
  `status` enum('draft','scheduled','sending','sent','paused','cancelled') DEFAULT 'draft',
  `scheduled_date` datetime DEFAULT NULL,
  `date_sent` datetime DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`campaign_id`),
  KEY `idx_campaign_type` (`campaign_type`),
  KEY `idx_status` (`status`),
  KEY `idx_date_sent` (`date_sent`),
  KEY `idx_scheduled_date` (`scheduled_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول حملات البريد الإلكتروني';

-- ========================================
-- جداول الامتثال والأمان
-- ========================================

-- جدول تدقيق الامتثال
CREATE TABLE IF NOT EXISTS `cod_compliance_audit` (
  `audit_id` int(11) NOT NULL AUTO_INCREMENT,
  `audit_name` varchar(200) NOT NULL COMMENT 'اسم التدقيق',
  `audit_type` enum('internal','external','regulatory','self_assessment') NOT NULL,
  `regulation_framework` varchar(100) DEFAULT NULL COMMENT 'إطار التنظيم',
  `audit_scope` text COMMENT 'نطاق التدقيق',
  `auditor_name` varchar(100) DEFAULT NULL,
  `audit_firm` varchar(200) DEFAULT NULL,
  `audit_date` date NOT NULL,
  `start_date` date NOT NULL,
  `end_date` date DEFAULT NULL,
  `compliance_status` enum('compliant','non_compliant','partially_compliant','pending') DEFAULT 'pending',
  `compliance_score` decimal(5,2) DEFAULT NULL COMMENT 'درجة الامتثال (0-100)',
  `findings_count` int(11) DEFAULT 0 COMMENT 'عدد النتائج',
  `critical_findings` int(11) DEFAULT 0 COMMENT 'النتائج الحرجة',
  `major_findings` int(11) DEFAULT 0 COMMENT 'النتائج الرئيسية',
  `minor_findings` int(11) DEFAULT 0 COMMENT 'النتائج الثانوية',
  `recommendations_count` int(11) DEFAULT 0 COMMENT 'عدد التوصيات',
  `corrective_actions_required` int(11) DEFAULT 0 COMMENT 'الإجراءات التصحيحية المطلوبة',
  `corrective_actions_completed` int(11) DEFAULT 0 COMMENT 'الإجراءات التصحيحية المكتملة',
  `audit_cost` decimal(10,2) DEFAULT NULL COMMENT 'تكلفة التدقيق',
  `next_audit_date` date DEFAULT NULL,
  `status` enum('planned','in_progress','completed','cancelled') DEFAULT 'planned',
  `report_file` varchar(255) DEFAULT NULL COMMENT 'ملف التقرير',
  `notes` text COMMENT 'ملاحظات',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`audit_id`),
  KEY `idx_audit_type` (`audit_type`),
  KEY `idx_audit_date` (`audit_date`),
  KEY `idx_compliance_status` (`compliance_status`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول تدقيق الامتثال';

-- جدول الضوابط الداخلية
CREATE TABLE IF NOT EXISTS `cod_internal_controls` (
  `control_id` int(11) NOT NULL AUTO_INCREMENT,
  `control_name` varchar(200) NOT NULL COMMENT 'اسم الضابط',
  `control_type` enum('preventive','detective','corrective','directive') NOT NULL,
  `control_category` varchar(100) DEFAULT NULL COMMENT 'فئة الضابط',
  `process_area` varchar(100) DEFAULT NULL COMMENT 'منطقة العملية',
  `risk_category` varchar(100) DEFAULT NULL COMMENT 'فئة المخاطر',
  `control_description` text COMMENT 'وصف الضابط',
  `control_objective` text COMMENT 'هدف الضابط',
  `control_owner` int(11) DEFAULT NULL COMMENT 'مسؤول الضابط',
  `frequency` enum('continuous','daily','weekly','monthly','quarterly','annually') DEFAULT 'monthly',
  `automation_level` enum('manual','semi_automated','fully_automated') DEFAULT 'manual',
  `effectiveness_rating` enum('ineffective','partially_effective','effective','highly_effective') DEFAULT 'effective',
  `last_test_date` date DEFAULT NULL,
  `next_test_date` date DEFAULT NULL,
  `test_results` text COMMENT 'نتائج الاختبار',
  `deficiencies_identified` text COMMENT 'أوجه القصور المحددة',
  `remediation_plan` text COMMENT 'خطة المعالجة',
  `implementation_date` date DEFAULT NULL,
  `status` enum('active','inactive','under_review','needs_improvement') DEFAULT 'active',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`control_id`),
  KEY `idx_control_type` (`control_type`),
  KEY `idx_process_area` (`process_area`),
  KEY `idx_effectiveness_rating` (`effectiveness_rating`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول الضوابط الداخلية';

-- جدول حوادث الأمان
CREATE TABLE IF NOT EXISTS `cod_security_incidents` (
  `incident_id` int(11) NOT NULL AUTO_INCREMENT,
  `incident_title` varchar(200) NOT NULL COMMENT 'عنوان الحادث',
  `incident_type` enum('data_breach','unauthorized_access','malware','phishing','ddos','insider_threat','physical_security','other') NOT NULL,
  `severity_level` enum('low','medium','high','critical') NOT NULL,
  `incident_description` text COMMENT 'وصف الحادث',
  `affected_systems` text COMMENT 'الأنظمة المتأثرة',
  `affected_data` text COMMENT 'البيانات المتأثرة',
  `discovery_date` datetime NOT NULL COMMENT 'تاريخ اكتشاف الحادث',
  `reported_date` datetime DEFAULT NULL COMMENT 'تاريخ الإبلاغ',
  `reported_by` int(11) DEFAULT NULL COMMENT 'المبلغ عن الحادث',
  `assigned_to` int(11) DEFAULT NULL COMMENT 'المكلف بالتعامل',
  `incident_source` enum('internal','external','unknown') DEFAULT 'unknown',
  `attack_vector` varchar(100) DEFAULT NULL COMMENT 'طريقة الهجوم',
  `impact_assessment` text COMMENT 'تقييم التأثير',
  `containment_actions` text COMMENT 'إجراءات الاحتواء',
  `eradication_actions` text COMMENT 'إجراءات الإزالة',
  `recovery_actions` text COMMENT 'إجراءات الاستعادة',
  `lessons_learned` text COMMENT 'الدروس المستفادة',
  `estimated_cost` decimal(10,2) DEFAULT NULL COMMENT 'التكلفة المقدرة',
  `regulatory_notification_required` tinyint(1) DEFAULT 0 COMMENT 'هل يتطلب إشعار تنظيمي',
  `regulatory_notification_date` datetime DEFAULT NULL,
  `status` enum('new','investigating','contained','resolved','closed') DEFAULT 'new',
  `resolution_date` datetime DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`incident_id`),
  KEY `idx_incident_type` (`incident_type`),
  KEY `idx_severity_level` (`severity_level`),
  KEY `idx_discovery_date` (`discovery_date`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول حوادث الأمان';

-- ========================================
-- جداول التحليلات المتقدمة
-- ========================================

-- جدول نماذج التحليلات
CREATE TABLE IF NOT EXISTS `cod_analytics_models` (
  `model_id` int(11) NOT NULL AUTO_INCREMENT,
  `model_name` varchar(200) NOT NULL COMMENT 'اسم النموذج',
  `model_type` enum('predictive','descriptive','prescriptive','diagnostic') NOT NULL,
  `algorithm_used` varchar(100) DEFAULT NULL COMMENT 'الخوارزمية المستخدمة',
  `model_description` text COMMENT 'وصف النموذج',
  `input_variables` text COMMENT 'المتغيرات المدخلة (JSON)',
  `output_variables` text COMMENT 'المتغيرات المخرجة (JSON)',
  `training_data_size` int(11) DEFAULT NULL COMMENT 'حجم بيانات التدريب',
  `accuracy_score` decimal(5,4) DEFAULT NULL COMMENT 'درجة الدقة',
  `precision_score` decimal(5,4) DEFAULT NULL COMMENT 'درجة الدقة',
  `recall_score` decimal(5,4) DEFAULT NULL COMMENT 'درجة الاستدعاء',
  `f1_score` decimal(5,4) DEFAULT NULL COMMENT 'درجة F1',
  `model_version` varchar(20) DEFAULT '1.0' COMMENT 'إصدار النموذج',
  `training_date` datetime DEFAULT NULL COMMENT 'تاريخ التدريب',
  `last_validation_date` datetime DEFAULT NULL COMMENT 'تاريخ آخر تحقق',
  `deployment_date` datetime DEFAULT NULL COMMENT 'تاريخ النشر',
  `model_file_path` varchar(500) DEFAULT NULL COMMENT 'مسار ملف النموذج',
  `performance_metrics` text COMMENT 'مقاييس الأداء (JSON)',
  `status` enum('development','testing','production','deprecated') DEFAULT 'development',
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`model_id`),
  KEY `idx_model_type` (`model_type`),
  KEY `idx_status` (`status`),
  KEY `idx_training_date` (`training_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول نماذج التحليلات';

-- جدول سجل معالجة البيانات
CREATE TABLE IF NOT EXISTS `cod_data_processing_log` (
  `log_id` int(11) NOT NULL AUTO_INCREMENT,
  `process_name` varchar(200) NOT NULL COMMENT 'اسم العملية',
  `process_type` enum('etl','data_cleaning','aggregation','transformation','validation') NOT NULL,
  `data_source` varchar(200) DEFAULT NULL COMMENT 'مصدر البيانات',
  `data_destination` varchar(200) DEFAULT NULL COMMENT 'وجهة البيانات',
  `records_processed` int(11) DEFAULT 0 COMMENT 'عدد السجلات المعالجة',
  `records_successful` int(11) DEFAULT 0 COMMENT 'السجلات الناجحة',
  `records_failed` int(11) DEFAULT 0 COMMENT 'السجلات الفاشلة',
  `processing_time_seconds` decimal(10,3) DEFAULT NULL COMMENT 'وقت المعالجة بالثواني',
  `memory_usage_mb` decimal(10,2) DEFAULT NULL COMMENT 'استخدام الذاكرة بالميجابايت',
  `cpu_usage_percent` decimal(5,2) DEFAULT NULL COMMENT 'استخدام المعالج بالنسبة المئوية',
  `error_messages` text COMMENT 'رسائل الخطأ',
  `process_parameters` text COMMENT 'معاملات العملية (JSON)',
  `start_time` datetime NOT NULL COMMENT 'وقت البداية',
  `end_time` datetime DEFAULT NULL COMMENT 'وقت النهاية',
  `status` enum('running','completed','failed','cancelled') DEFAULT 'running',
  `triggered_by` enum('manual','scheduled','event','api') DEFAULT 'manual',
  `user_id` int(11) DEFAULT NULL COMMENT 'المستخدم المشغل',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`log_id`),
  KEY `idx_process_type` (`process_type`),
  KEY `idx_start_time` (`start_time`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول سجل معالجة البيانات';

-- جدول سجل استخدام لوحة المعلومات
CREATE TABLE IF NOT EXISTS `cod_dashboard_usage_log` (
  `usage_id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT 'معرف المستخدم',
  `dashboard_name` varchar(200) DEFAULT NULL COMMENT 'اسم لوحة المعلومات',
  `page_url` varchar(500) DEFAULT NULL COMMENT 'رابط الصفحة',
  `action_type` enum('view','filter','export','drill_down','refresh') NOT NULL,
  `widget_name` varchar(200) DEFAULT NULL COMMENT 'اسم الأداة',
  `filter_parameters` text COMMENT 'معاملات التصفية (JSON)',
  `session_id` varchar(100) DEFAULT NULL COMMENT 'معرف الجلسة',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'عنوان IP',
  `user_agent` text COMMENT 'معلومات المتصفح',
  `response_time_ms` int(11) DEFAULT NULL COMMENT 'وقت الاستجابة بالميلي ثانية',
  `data_volume_kb` decimal(10,2) DEFAULT NULL COMMENT 'حجم البيانات بالكيلوبايت',
  `access_time` datetime NOT NULL COMMENT 'وقت الوصول',
  `duration_seconds` int(11) DEFAULT NULL COMMENT 'مدة الاستخدام بالثواني',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`usage_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_dashboard_name` (`dashboard_name`),
  KEY `idx_action_type` (`action_type`),
  KEY `idx_access_time` (`access_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول سجل استخدام لوحة المعلومات';

-- ========================================
-- باقي جداول الذكاء الاصطناعي المفقودة
-- ========================================

-- جدول تفاعلات الشات بوت بالذكاء الاصطناعي
CREATE TABLE IF NOT EXISTS `cod_ai_chatbot_interactions` (
  `interaction_id` int(11) NOT NULL AUTO_INCREMENT,
  `session_id` varchar(100) NOT NULL COMMENT 'معرف الجلسة',
  `user_id` int(11) DEFAULT NULL COMMENT 'معرف المستخدم',
  `user_message` text NOT NULL COMMENT 'رسالة المستخدم',
  `bot_response` text NOT NULL COMMENT 'رد الشات بوت',
  `intent_detected` varchar(100) DEFAULT NULL COMMENT 'النية المكتشفة',
  `confidence_score` decimal(5,4) DEFAULT NULL COMMENT 'درجة الثقة',
  `entities_extracted` text COMMENT 'الكيانات المستخرجة (JSON)',
  `conversation_context` text COMMENT 'سياق المحادثة (JSON)',
  `response_time_ms` int(11) DEFAULT NULL COMMENT 'وقت الاستجابة بالميلي ثانية',
  `user_satisfaction` enum('very_poor','poor','neutral','good','excellent') DEFAULT NULL,
  `escalated_to_human` tinyint(1) DEFAULT 0 COMMENT 'هل تم التصعيد لإنسان',
  `resolved` tinyint(1) DEFAULT 0 COMMENT 'هل تم حل الاستفسار',
  `interaction_timestamp` datetime NOT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`interaction_id`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_intent_detected` (`intent_detected`),
  KEY `idx_interaction_timestamp` (`interaction_timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول تفاعلات الشات بوت بالذكاء الاصطناعي';

-- جدول تحليل سلوك العملاء بالذكاء الاصطناعي
CREATE TABLE IF NOT EXISTS `cod_ai_customer_behavior` (
  `analysis_id` int(11) NOT NULL AUTO_INCREMENT,
  `customer_id` int(11) NOT NULL,
  `analysis_date` date NOT NULL,
  `behavior_pattern` varchar(100) DEFAULT NULL COMMENT 'نمط السلوك',
  `purchase_frequency` decimal(5,2) DEFAULT NULL COMMENT 'تكرار الشراء',
  `average_order_value` decimal(10,2) DEFAULT NULL COMMENT 'متوسط قيمة الطلب',
  `preferred_categories` text COMMENT 'الفئات المفضلة (JSON)',
  `browsing_patterns` text COMMENT 'أنماط التصفح (JSON)',
  `seasonal_preferences` text COMMENT 'التفضيلات الموسمية (JSON)',
  `churn_probability` decimal(5,4) DEFAULT NULL COMMENT 'احتمالية المغادرة',
  `lifetime_value_prediction` decimal(10,2) DEFAULT NULL COMMENT 'التنبؤ بالقيمة الدائمة',
  `next_purchase_prediction` date DEFAULT NULL COMMENT 'التنبؤ بالشراء التالي',
  `recommended_products` text COMMENT 'المنتجات الموصى بها (JSON)',
  `engagement_score` decimal(5,4) DEFAULT NULL COMMENT 'درجة التفاعل',
  `loyalty_segment` enum('new','occasional','regular','loyal','champion') DEFAULT NULL,
  `risk_factors` text COMMENT 'عوامل المخاطر (JSON)',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`analysis_id`),
  KEY `idx_customer_id` (`customer_id`),
  KEY `idx_analysis_date` (`analysis_date`),
  KEY `idx_churn_probability` (`churn_probability`),
  KEY `idx_loyalty_segment` (`loyalty_segment`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول تحليل سلوك العملاء بالذكاء الاصطناعي';

-- جدول تحسين سلسلة التوريد بالذكاء الاصطناعي
CREATE TABLE IF NOT EXISTS `cod_ai_supply_chain_optimization` (
  `optimization_id` int(11) NOT NULL AUTO_INCREMENT,
  `optimization_type` enum('inventory','logistics','procurement','demand_planning','supplier_selection') NOT NULL,
  `target_entity` varchar(50) NOT NULL COMMENT 'الكيان المستهدف',
  `target_id` int(11) NOT NULL COMMENT 'معرف الكيان المستهدف',
  `current_metrics` text COMMENT 'المقاييس الحالية (JSON)',
  `optimized_metrics` text COMMENT 'المقاييس المحسنة (JSON)',
  `improvement_percentage` decimal(5,2) DEFAULT NULL COMMENT 'نسبة التحسن',
  `cost_reduction` decimal(10,2) DEFAULT NULL COMMENT 'تقليل التكلفة',
  `efficiency_gain` decimal(5,2) DEFAULT NULL COMMENT 'مكسب الكفاءة',
  `optimization_algorithm` varchar(100) DEFAULT NULL COMMENT 'خوارزمية التحسين',
  `constraints` text COMMENT 'القيود (JSON)',
  `recommendations` text COMMENT 'التوصيات',
  `implementation_plan` text COMMENT 'خطة التنفيذ',
  `expected_roi` decimal(5,2) DEFAULT NULL COMMENT 'العائد المتوقع على الاستثمار',
  `risk_assessment` text COMMENT 'تقييم المخاطر',
  `status` enum('proposed','approved','implementing','completed','rejected') DEFAULT 'proposed',
  `created_at` datetime NOT NULL,
  `implemented_at` datetime DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  PRIMARY KEY (`optimization_id`),
  KEY `idx_optimization_type` (`optimization_type`),
  KEY `idx_target_entity` (`target_entity`, `target_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول تحسين سلسلة التوريد بالذكاء الاصطناعي';

-- جدول تصنيف البيانات بالذكاء الاصطناعي
CREATE TABLE IF NOT EXISTS `cod_ai_classification` (
  `classification_id` int(11) NOT NULL AUTO_INCREMENT,
  `data_type` varchar(50) NOT NULL COMMENT 'نوع البيانات',
  `data_id` int(11) NOT NULL COMMENT 'معرف البيانات',
  `input_data` text COMMENT 'البيانات المدخلة',
  `predicted_class` varchar(100) NOT NULL COMMENT 'الفئة المتنبأ بها',
  `actual_class` varchar(100) DEFAULT NULL COMMENT 'الفئة الفعلية',
  `confidence_score` decimal(5,4) NOT NULL COMMENT 'درجة الثقة',
  `classification_model` varchar(100) NOT NULL COMMENT 'نموذج التصنيف',
  `feature_importance` text COMMENT 'أهمية الميزات (JSON)',
  `classification_date` datetime NOT NULL,
  `validation_date` datetime DEFAULT NULL,
  `validation_result` enum('correct','incorrect','pending') DEFAULT 'pending',
  `feedback_provided` tinyint(1) DEFAULT 0 COMMENT 'هل تم تقديم تغذية راجعة',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`classification_id`),
  KEY `idx_data_type` (`data_type`, `data_id`),
  KEY `idx_predicted_class` (`predicted_class`),
  KEY `idx_classification_date` (`classification_date`),
  KEY `idx_validation_result` (`validation_result`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول تصنيف البيانات بالذكاء الاصطناعي';

-- ========================================
-- جداول إضافية مطلوبة
-- ========================================

-- جدول السلة المهجورة للمنتجات
CREATE TABLE IF NOT EXISTS `cod_abandoned_cart_product` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `cart_id` varchar(32) NOT NULL COMMENT 'معرف السلة',
  `customer_id` int(11) DEFAULT NULL COMMENT 'معرف العميل',
  `product_id` int(11) NOT NULL COMMENT 'معرف المنتج',
  `quantity` int(11) NOT NULL DEFAULT 1 COMMENT 'الكمية',
  `price` decimal(10,4) NOT NULL COMMENT 'السعر',
  `total` decimal(10,4) NOT NULL COMMENT 'الإجمالي',
  `product_options` text COMMENT 'خيارات المنتج (JSON)',
  `date_added` datetime NOT NULL COMMENT 'تاريخ الإضافة',
  `abandoned_date` datetime DEFAULT NULL COMMENT 'تاريخ الهجر',
  `recovery_email_sent` tinyint(1) DEFAULT 0 COMMENT 'هل تم إرسال إيميل الاستعادة',
  `recovered` tinyint(1) DEFAULT 0 COMMENT 'هل تم الاستعادة',
  `recovery_date` datetime DEFAULT NULL COMMENT 'تاريخ الاستعادة',
  PRIMARY KEY (`id`),
  KEY `idx_cart_id` (`cart_id`),
  KEY `idx_customer_id` (`customer_id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_date_added` (`date_added`),
  KEY `idx_abandoned_date` (`abandoned_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول السلة المهجورة للمنتجات';

-- جدول حركة المخزون
CREATE TABLE IF NOT EXISTS `cod_stock_movement` (
  `movement_id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL COMMENT 'معرف المنتج',
  `warehouse_id` int(11) DEFAULT NULL COMMENT 'معرف المستودع',
  `movement_type` enum('in','out','transfer','adjustment','return') NOT NULL COMMENT 'نوع الحركة',
  `reference_type` varchar(50) DEFAULT NULL COMMENT 'نوع المرجع (order, purchase, etc)',
  `reference_id` int(11) DEFAULT NULL COMMENT 'معرف المرجع',
  `quantity` decimal(10,4) NOT NULL COMMENT 'الكمية',
  `unit_cost` decimal(10,4) DEFAULT NULL COMMENT 'تكلفة الوحدة',
  `total_cost` decimal(10,4) DEFAULT NULL COMMENT 'التكلفة الإجمالية',
  `balance_before` decimal(10,4) DEFAULT NULL COMMENT 'الرصيد قبل الحركة',
  `balance_after` decimal(10,4) DEFAULT NULL COMMENT 'الرصيد بعد الحركة',
  `movement_date` datetime NOT NULL COMMENT 'تاريخ الحركة',
  `notes` text COMMENT 'ملاحظات',
  `created_by` int(11) DEFAULT NULL COMMENT 'المنشئ',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`movement_id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_warehouse_id` (`warehouse_id`),
  KEY `idx_movement_type` (`movement_type`),
  KEY `idx_reference` (`reference_type`, `reference_id`),
  KEY `idx_movement_date` (`movement_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول حركة المخزون';
