<?php
/**
 * English Language File - Workflow Templates
 * 
 * @package    AYM ERP
 * <AUTHOR> Development Team
 * @copyright  2025 AYM ERP Systems
 * @license    Commercial License
 * @version    1.0.0
 * @since      2025-07-15
 */

// Main heading
$_['heading_title'] = 'Workflow Templates';

// General texts
$_['text_success'] = 'Success: You have modified workflow templates!';
$_['text_list'] = 'Templates List';
$_['text_add'] = 'Add Template';
$_['text_edit'] = 'Edit Template';
$_['text_view'] = 'View Template';
$_['text_delete'] = 'Delete';
$_['text_confirm'] = 'Are you sure?';
$_['text_no_results'] = 'No templates found!';
$_['text_loading'] = 'Loading...';

// Template categories
$_['text_template_categories'] = 'Template Categories';
$_['text_category_approval'] = 'Approvals';
$_['text_category_notification'] = 'Notifications';
$_['text_category_automation'] = 'Automation';
$_['text_category_review'] = 'Review';
$_['text_category_escalation'] = 'Escalation';
$_['text_category_hr'] = 'Human Resources';
$_['text_category_finance'] = 'Finance';
$_['text_category_procurement'] = 'Procurement';
$_['text_category_sales'] = 'Sales';
$_['text_category_support'] = 'Support';
$_['text_category_maintenance'] = 'Maintenance';
$_['text_category_quality'] = 'Quality';

// Template types
$_['text_template_types'] = 'Template Types';
$_['text_type_standard'] = 'Standard';
$_['text_type_custom'] = 'Custom';
$_['text_type_system'] = 'System';
$_['text_type_industry'] = 'Industry';
$_['text_type_department'] = 'Department';
$_['text_type_project'] = 'Project';

// Template status
$_['text_status_active'] = 'Active';
$_['text_status_inactive'] = 'Inactive';
$_['text_status_draft'] = 'Draft';
$_['text_status_published'] = 'Published';
$_['text_status_archived'] = 'Archived';
$_['text_status_deprecated'] = 'Deprecated';

// Entry fields
$_['entry_name'] = 'Template Name';
$_['entry_description'] = 'Description';
$_['entry_category'] = 'Category';
$_['entry_type'] = 'Type';
$_['entry_status'] = 'Status';
$_['entry_version'] = 'Version';
$_['entry_author'] = 'Author';
$_['entry_tags'] = 'Tags';
$_['entry_complexity'] = 'Complexity Level';
$_['entry_estimated_time'] = 'Estimated Time';
$_['entry_prerequisites'] = 'Prerequisites';

// Columns
$_['column_name'] = 'Name';
$_['column_category'] = 'Category';
$_['column_type'] = 'Type';
$_['column_status'] = 'Status';
$_['column_version'] = 'Version';
$_['column_author'] = 'Author';
$_['column_usage_count'] = 'Usage Count';
$_['column_rating'] = 'Rating';
$_['column_created'] = 'Created';
$_['column_modified'] = 'Modified';
$_['column_action'] = 'Action';

// Buttons
$_['button_add'] = 'Add Template';
$_['button_edit'] = 'Edit';
$_['button_delete'] = 'Delete';
$_['button_view'] = 'View';
$_['button_use'] = 'Use';
$_['button_copy'] = 'Copy';
$_['button_publish'] = 'Publish';
$_['button_unpublish'] = 'Unpublish';
$_['button_archive'] = 'Archive';
$_['button_export'] = 'Export';
$_['button_import'] = 'Import';
$_['button_preview'] = 'Preview';
$_['button_customize'] = 'Customize';

// Predefined templates
$_['text_predefined_templates'] = 'Predefined Templates';
$_['text_template_purchase_approval'] = 'Purchase Order Approval';
$_['text_template_leave_request'] = 'Leave Request';
$_['text_template_expense_approval'] = 'Expense Approval';
$_['text_template_document_review'] = 'Document Review';
$_['text_template_customer_onboarding'] = 'Customer Onboarding';
$_['text_template_employee_onboarding'] = 'Employee Onboarding';
$_['text_template_invoice_approval'] = 'Invoice Approval';
$_['text_template_contract_review'] = 'Contract Review';
$_['text_template_budget_approval'] = 'Budget Approval';
$_['text_template_project_approval'] = 'Project Approval';
$_['text_template_maintenance_request'] = 'Maintenance Request';
$_['text_template_quality_check'] = 'Quality Check';

// Complexity levels
$_['text_complexity_simple'] = 'Simple';
$_['text_complexity_moderate'] = 'Moderate';
$_['text_complexity_complex'] = 'Complex';
$_['text_complexity_advanced'] = 'Advanced';

// Template components
$_['text_template_components'] = 'Template Components';
$_['text_workflow_steps'] = 'Workflow Steps';
$_['text_conditions'] = 'Conditions';
$_['text_actions'] = 'Actions';
$_['text_triggers'] = 'Triggers';
$_['text_notifications'] = 'Notifications';
$_['text_escalations'] = 'Escalations';
$_['text_approvers'] = 'Approvers';
$_['text_variables'] = 'Variables';

// Customization
$_['text_customization'] = 'Customization';
$_['text_customize_template'] = 'Customize Template';
$_['text_template_parameters'] = 'Template Parameters';
$_['text_configurable_options'] = 'Configurable Options';
$_['text_default_values'] = 'Default Values';
$_['text_validation_rules'] = 'Validation Rules';

// Preview and testing
$_['text_preview'] = 'Preview';
$_['text_template_preview'] = 'Template Preview';
$_['text_visual_preview'] = 'Visual Preview';
$_['text_step_by_step'] = 'Step by Step';
$_['text_test_template'] = 'Test Template';
$_['text_simulation'] = 'Simulation';

// Versions
$_['text_versions'] = 'Versions';
$_['text_version_history'] = 'Version History';
$_['text_current_version'] = 'Current Version';
$_['text_previous_versions'] = 'Previous Versions';
$_['text_version_notes'] = 'Version Notes';
$_['text_create_version'] = 'Create New Version';
$_['text_restore_version'] = 'Restore Version';

// Sharing and collaboration
$_['text_sharing'] = 'Sharing & Collaboration';
$_['text_share_template'] = 'Share Template';
$_['text_public_templates'] = 'Public Templates';
$_['text_private_templates'] = 'Private Templates';
$_['text_team_templates'] = 'Team Templates';
$_['text_organization_templates'] = 'Organization Templates';
$_['text_template_permissions'] = 'Template Permissions';

// Rating and reviews
$_['text_rating'] = 'Rating';
$_['text_reviews'] = 'Reviews';
$_['text_user_feedback'] = 'User Feedback';
$_['text_template_rating'] = 'Template Rating';
$_['text_add_review'] = 'Add Review';
$_['text_helpful'] = 'Helpful';
$_['text_not_helpful'] = 'Not Helpful';

// Statistics
$_['text_statistics'] = 'Statistics';
$_['text_usage_statistics'] = 'Usage Statistics';
$_['text_popularity'] = 'Popularity';
$_['text_download_count'] = 'Download Count';
$_['text_implementation_count'] = 'Implementation Count';
$_['text_success_rate'] = 'Success Rate';
$_['text_average_rating'] = 'Average Rating';

// Search and filter
$_['text_search'] = 'Search';
$_['text_search_templates'] = 'Search Templates';
$_['text_advanced_search'] = 'Advanced Search';
$_['text_filter'] = 'Filter';
$_['text_filter_by_category'] = 'Filter by Category';
$_['text_filter_by_type'] = 'Filter by Type';
$_['text_filter_by_complexity'] = 'Filter by Complexity';
$_['text_filter_by_rating'] = 'Filter by Rating';
$_['text_sort_by'] = 'Sort By';
$_['text_sort_name'] = 'Name';
$_['text_sort_popularity'] = 'Popularity';
$_['text_sort_rating'] = 'Rating';
$_['text_sort_date'] = 'Date';

// Export and import
$_['text_export'] = 'Export';
$_['text_import'] = 'Import';
$_['text_export_template'] = 'Export Template';
$_['text_import_template'] = 'Import Template';
$_['text_export_format'] = 'Export Format';
$_['text_import_format'] = 'Import Format';
$_['text_template_package'] = 'Template Package';

// Documentation
$_['text_documentation'] = 'Documentation';
$_['text_template_documentation'] = 'Template Documentation';
$_['text_user_guide'] = 'User Guide';
$_['text_implementation_guide'] = 'Implementation Guide';
$_['text_best_practices'] = 'Best Practices';
$_['text_troubleshooting'] = 'Troubleshooting';

// Security and compliance
$_['text_security'] = 'Security & Compliance';
$_['text_security_review'] = 'Security Review';
$_['text_compliance_check'] = 'Compliance Check';
$_['text_audit_trail'] = 'Audit Trail';
$_['text_data_protection'] = 'Data Protection';

// Error messages
$_['error_permission'] = 'Warning: You do not have permission to access workflow templates!';
$_['error_name'] = 'Template name must be between 3 and 255 characters!';
$_['error_category'] = 'Template category must be selected!';
$_['error_type'] = 'Template type must be selected!';
$_['error_template_not_found'] = 'Template not found!';
$_['error_template_in_use'] = 'Template is in use and cannot be deleted!';
$_['error_invalid_format'] = 'Invalid template format!';
$_['error_import_failed'] = 'Template import failed!';
$_['error_export_failed'] = 'Template export failed!';

// Confirmation messages
$_['text_confirm_delete'] = 'Are you sure you want to delete this template?';
$_['text_confirm_archive'] = 'Are you sure you want to archive this template?';
$_['text_confirm_publish'] = 'Are you sure you want to publish this template?';
$_['text_confirm_unpublish'] = 'Are you sure you want to unpublish this template?';

// Help and tips
$_['help_name'] = 'Enter a clear and distinctive name for the template';
$_['help_description'] = 'Detailed description of template function and usage';
$_['help_category'] = 'Choose the appropriate category for the template';
$_['help_complexity'] = 'Define the complexity level of the template';
$_['help_tags'] = 'Add tags to make the template easier to find';

// Alerts
$_['alert_template_created'] = 'Template created successfully';
$_['alert_template_updated'] = 'Template updated successfully';
$_['alert_template_deleted'] = 'Template deleted';
$_['alert_template_published'] = 'Template published';
$_['alert_template_archived'] = 'Template archived';
$_['alert_template_imported'] = 'Template imported successfully';
$_['alert_template_exported'] = 'Template exported successfully';

// Dates and times
$_['text_created_at'] = 'Created At';
$_['text_updated_at'] = 'Updated At';
$_['text_published_at'] = 'Published At';
$_['text_last_used'] = 'Last Used';
$_['text_usage_frequency'] = 'Usage Frequency';

// Advanced features
$_['text_advanced_features'] = 'Advanced Features';
$_['text_conditional_logic'] = 'Conditional Logic';
$_['text_dynamic_routing'] = 'Dynamic Routing';
$_['text_parallel_processing'] = 'Parallel Processing';
$_['text_integration_points'] = 'Integration Points';
$_['text_custom_fields'] = 'Custom Fields';
?>
