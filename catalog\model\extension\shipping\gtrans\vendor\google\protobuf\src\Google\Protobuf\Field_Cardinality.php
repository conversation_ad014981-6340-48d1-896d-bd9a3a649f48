<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/protobuf/type.proto

namespace Google\Protobuf;

if (false) {
    /**
     * This class is deprecated. Use Google\Protobuf\Field\Cardinality instead.
     * @deprecated
     */
    class Field_Cardinality {}
}
class_exists(Field\Cardinality::class);
@trigger_error('Google\Protobuf\Field_Cardinality is deprecated and will be removed in the next major release. Use Google\Protobuf\Field\Cardinality instead', E_USER_DEPRECATED);

