{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        {% if permissions.add %}
        <a href="{{ add }}" data-toggle="tooltip" title="{{ button_add }}" class="btn btn-primary">
          <i class="fa fa-plus"></i> {{ button_add }}
        </a>
        {% endif %}
        {% if permissions.export %}
        <button type="button" data-toggle="tooltip" title="{{ button_export }}" class="btn btn-success" onclick="exportWarehouses()">
          <i class="fa fa-download"></i> {{ button_export }}
        </button>
        {% endif %}
        {% if permissions.print %}
        <button type="button" data-toggle="tooltip" title="{{ button_print }}" class="btn btn-info" onclick="printWarehouses()">
          <i class="fa fa-print"></i> {{ button_print }}
        </button>
        {% endif %}
        {% if permissions.tree_view %}
        <a href="{{ tree_view }}" data-toggle="tooltip" title="{{ button_view_tree }}" class="btn btn-warning">
          <i class="fa fa-sitemap"></i> {{ button_view_tree }}
        </a>
        {% endif %}
        {% if permissions.statistics %}
        <button type="button" data-toggle="tooltip" title="{{ button_statistics }}" class="btn btn-secondary" onclick="showStatistics()">
          <i class="fa fa-bar-chart"></i> {{ button_statistics }}
        </button>
        {% endif %}
        {% if permissions.delete %}
        <button type="button" data-toggle="tooltip" title="{{ button_delete }}" class="btn btn-danger" onclick="confirmDelete()">
          <i class="fa fa-trash-o"></i> {{ button_delete }}
        </button>
        {% endif %}
      </div>
      <h1><i class="fa fa-warehouse"></i> {{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li{% if breadcrumb.active %} class="active"{% endif %}>
          {% if breadcrumb.href and not breadcrumb.active %}
          <a href="{{ breadcrumb.href }}">
            {% if breadcrumb.icon %}<i class="fa {{ breadcrumb.icon }}"></i> {% endif %}
            {{ breadcrumb.text }}
          </a>
          {% else %}
          {% if breadcrumb.icon %}<i class="fa {{ breadcrumb.icon }}"></i> {% endif %}
          {{ breadcrumb.text }}
          {% endif %}
        </li>
        {% endfor %}
      </ul>
    </div>
  </div>

  <div class="container-fluid">
    <!-- رسائل التنبيه -->
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible">
      <i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    
    {% if success %}
    <div class="alert alert-success alert-dismissible">
      <i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    
    {% if warning %}
    <div class="alert alert-warning alert-dismissible">
      <i class="fa fa-warning"></i> {{ warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}

    <!-- لوحة الإحصائيات السريعة -->
    {% if statistics %}
    <div class="row" id="statistics-dashboard">
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-primary">
          <div class="panel-body">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-warehouse fa-3x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ statistics.total_warehouses }}</div>
                <div>{{ text_warehouse_count }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-success">
          <div class="panel-body">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-cubes fa-3x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ statistics.total_products }}</div>
                <div>{{ text_total_products }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-info">
          <div class="panel-body">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-dollar fa-3x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ statistics.total_stock_value|number_format(2) }}</div>
                <div>{{ text_total_value }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-warning">
          <div class="panel-body">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-exclamation-triangle fa-3x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ statistics.low_stock_products }}</div>
                <div>{{ text_low_stock_alerts }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    {% endif %}

    <!-- لوحة الفلاتر والبحث -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title">
          <i class="fa fa-filter"></i> {{ text_filters }}
          <button type="button" class="btn btn-xs btn-link pull-right" data-toggle="collapse" data-target="#filter-panel">
            <i class="fa fa-chevron-down"></i>
          </button>
        </h3>
      </div>
      <div class="panel-body collapse" id="filter-panel">
        <form id="filter-form" class="form-horizontal">
          <div class="row">
            <div class="col-md-3">
              <div class="form-group">
                <label class="control-label">{{ entry_name }}</label>
                <input type="text" name="filter_name" value="{{ filters.filter_name }}" placeholder="{{ entry_name }}" class="form-control">
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <label class="control-label">{{ entry_code }}</label>
                <input type="text" name="filter_code" value="{{ filters.filter_code }}" placeholder="{{ entry_code }}" class="form-control">
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <label class="control-label">{{ entry_location_type }}</label>
                <select name="filter_location_type" class="form-control">
                  <option value="">{{ text_all_types }}</option>
                  {% for type_key, type_name in filters.location_types %}
                  <option value="{{ type_key }}"{% if filters.filter_location_type == type_key %} selected{% endif %}>{{ type_name }}</option>
                  {% endfor %}
                </select>
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <label class="control-label">{{ entry_status }}</label>
                <select name="filter_status" class="form-control">
                  <option value="">{{ text_all_statuses }}</option>
                  <option value="1"{% if filters.filter_status == '1' %} selected{% endif %}>{{ text_enabled }}</option>
                  <option value="0"{% if filters.filter_status == '0' %} selected{% endif %}>{{ text_disabled }}</option>
                </select>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-12 text-right">
              <button type="button" class="btn btn-primary" onclick="applyFilters()">
                <i class="fa fa-search"></i> {{ button_search }}
              </button>
              <button type="button" class="btn btn-default" onclick="clearFilters()">
                <i class="fa fa-refresh"></i> {{ button_clear }}
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>

    <!-- جدول المستودعات -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title">
          <i class="fa fa-list"></i> {{ text_list }}
          <span class="badge pull-right">{{ results }}</span>
        </h3>
      </div>
      <div class="panel-body">
        <form action="{{ delete }}" method="post" enctype="multipart/form-data" id="form-warehouse">
          <div class="table-responsive">
            <table class="table table-bordered table-hover" id="warehouse-table">
              <thead>
                <tr>
                  <th style="width: 1px;" class="text-center">
                    <input type="checkbox" onclick="$('input[name*=\'selected\']').prop('checked', this.checked);" />
                  </th>
                  <th class="text-left">
                    {% if sort == 'name' %}
                    <a href="{{ sort_name }}" class="{{ order|lower }}">{{ column_name }}</a>
                    {% else %}
                    <a href="{{ sort_name }}">{{ column_name }}</a>
                    {% endif %}
                  </th>
                  <th class="text-left">
                    {% if sort == 'code' %}
                    <a href="{{ sort_code }}" class="{{ order|lower }}">{{ column_code }}</a>
                    {% else %}
                    <a href="{{ sort_code }}">{{ column_code }}</a>
                    {% endif %}
                  </th>
                  <th class="text-left">{{ column_location_type }}</th>
                  <th class="text-left">{{ column_address }}</th>
                  <th class="text-center">{{ column_capacity_percentage }}</th>
                  <th class="text-center">{{ column_product_count }}</th>
                  <th class="text-right">{{ column_total_value }}</th>
                  <th class="text-center">
                    {% if sort == 'status' %}
                    <a href="{{ sort_status }}" class="{{ order|lower }}">{{ column_status }}</a>
                    {% else %}
                    <a href="{{ sort_status }}">{{ column_status }}</a>
                    {% endif %}
                  </th>
                  <th class="text-right">{{ column_action }}</th>
                </tr>
              </thead>
              <tbody>
                {% if warehouses %}
                {% for warehouse in warehouses %}
                <tr data-warehouse-id="{{ warehouse.warehouse_id }}">
                  <td class="text-center">
                    <input type="checkbox" name="selected[]" value="{{ warehouse.warehouse_id }}"{% if warehouse.selected %} checked="checked"{% endif %} />
                  </td>
                  <td class="text-left">
                    <strong>{{ warehouse.name }}</strong>
                    {% if warehouse.parent_name %}
                    <br><small class="text-muted"><i class="fa fa-level-up fa-rotate-90"></i> {{ warehouse.parent_name }}</small>
                    {% endif %}
                  </td>
                  <td class="text-left">
                    <code>{{ warehouse.code }}</code>
                  </td>
                  <td class="text-left">
                    <span class="label label-info">{{ warehouse.location_type }}</span>
                  </td>
                  <td class="text-left">
                    {{ warehouse.address }}
                    {% if warehouse.telephone %}
                    <br><small><i class="fa fa-phone"></i> {{ warehouse.telephone }}</small>
                    {% endif %}
                  </td>
                  <td class="text-center">
                    {% if warehouse.total_capacity > 0 %}
                    <div class="progress" style="margin-bottom: 0;">
                      <div class="progress-bar 
                        {% if warehouse.capacity_percentage >= 90 %}progress-bar-danger
                        {% elseif warehouse.capacity_percentage >= 75 %}progress-bar-warning
                        {% else %}progress-bar-success{% endif %}" 
                        style="width: {{ warehouse.capacity_percentage }}%">
                        {{ warehouse.capacity_percentage }}%
                      </div>
                    </div>
                    <small>{{ warehouse.used_capacity }}/{{ warehouse.total_capacity }} {{ warehouse.capacity_unit }}</small>
                    {% else %}
                    <span class="text-muted">{{ text_not_set }}</span>
                    {% endif %}
                  </td>
                  <td class="text-center">
                    <span class="badge badge-primary">{{ warehouse.total_products }}</span>
                  </td>
                  <td class="text-right">
                    <strong>{{ warehouse.total_value }}</strong>
                  </td>
                  <td class="text-center">
                    {% if warehouse.status == text_enabled %}
                    <span class="label label-success">{{ warehouse.status }}</span>
                    {% else %}
                    <span class="label label-danger">{{ warehouse.status }}</span>
                    {% endif %}
                  </td>
                  <td class="text-right">
                    <div class="btn-group">
                      {% if permissions.edit %}
                      <a href="{{ warehouse.edit }}" data-toggle="tooltip" title="{{ button_edit }}" class="btn btn-primary btn-sm">
                        <i class="fa fa-pencil"></i>
                      </a>
                      {% endif %}
                      <button type="button" class="btn btn-info btn-sm" data-toggle="tooltip" title="{{ button_info }}" onclick="showWarehouseInfo({{ warehouse.warehouse_id }})">
                        <i class="fa fa-info"></i>
                      </button>
                      {% if permissions.transfer %}
                      <button type="button" class="btn btn-warning btn-sm" data-toggle="tooltip" title="{{ button_transfer }}" onclick="showTransferModal({{ warehouse.warehouse_id }})">
                        <i class="fa fa-exchange"></i>
                      </button>
                      {% endif %}
                    </div>
                  </td>
                </tr>
                {% endfor %}
                {% else %}
                <tr>
                  <td class="text-center" colspan="10">
                    <div class="alert alert-info" style="margin: 20px 0;">
                      <i class="fa fa-info-circle"></i> {{ text_no_results }}
                    </div>
                  </td>
                </tr>
                {% endif %}
              </tbody>
            </table>
          </div>
        </form>
        
        <!-- الصفحات -->
        <div class="row">
          <div class="col-sm-6 text-left">{{ pagination }}</div>
          <div class="col-sm-6 text-right">{{ results }}</div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Modal للإحصائيات -->
<div class="modal fade" id="statistics-modal" tabindex="-1" role="dialog">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title"><i class="fa fa-bar-chart"></i> {{ text_warehouse_statistics }}</h4>
      </div>
      <div class="modal-body" id="statistics-content">
        <div class="text-center">
          <i class="fa fa-spinner fa-spin fa-2x"></i>
          <p>{{ text_loading }}</p>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_close }}</button>
      </div>
    </div>
  </div>
</div>

<!-- Modal لمعلومات المستودع -->
<div class="modal fade" id="warehouse-info-modal" tabindex="-1" role="dialog">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title"><i class="fa fa-info-circle"></i> {{ text_warehouse_info }}</h4>
      </div>
      <div class="modal-body" id="warehouse-info-content">
        <div class="text-center">
          <i class="fa fa-spinner fa-spin fa-2x"></i>
          <p>{{ text_loading }}</p>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_close }}</button>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript">
// متغيرات عامة
var user_token = '{{ user_token }}';
var warehouse_url = 'index.php?route=inventory/warehouse';

// تطبيق الفلاتر
function applyFilters() {
    var url = warehouse_url + '&user_token=' + user_token;
    var filters = $('#filter-form').serialize();
    
    if (filters) {
        url += '&' + filters;
    }
    
    location = url;
}

// مسح الفلاتر
function clearFilters() {
    $('#filter-form')[0].reset();
    location = warehouse_url + '&user_token=' + user_token;
}

// تأكيد الحذف
function confirmDelete() {
    var selected = $('input[name*="selected"]:checked');
    
    if (selected.length === 0) {
        alert('{{ error_no_selection }}');
        return false;
    }
    
    if (confirm('{{ confirm_delete_multiple }}')) {
        $('#form-warehouse').submit();
    }
}

// عرض الإحصائيات
function showStatistics() {
    $('#statistics-modal').modal('show');
    
    $.ajax({
        url: warehouse_url + '&user_token=' + user_token,
        type: 'POST',
        data: {action: 'get_warehouse_statistics'},
        dataType: 'json',
        success: function(json) {
            if (json.success) {
                var html = buildStatisticsHTML(json.statistics, json.widgets);
                $('#statistics-content').html(html);
            } else {
                $('#statistics-content').html('<div class="alert alert-danger">' + json.error + '</div>');
            }
        },
        error: function() {
            $('#statistics-content').html('<div class="alert alert-danger">{{ error_ajax }}</div>');
        }
    });
}

// بناء HTML للإحصائيات
function buildStatisticsHTML(stats, widgets) {
    var html = '<div class="row">';
    
    // إحصائيات عامة
    html += '<div class="col-md-6">';
    html += '<h4><i class="fa fa-bar-chart"></i> {{ text_general_statistics }}</h4>';
    html += '<table class="table table-striped">';
    html += '<tr><td>{{ text_total_warehouses }}</td><td class="text-right"><strong>' + stats.total_warehouses + '</strong></td></tr>';
    html += '<tr><td>{{ text_active_warehouses }}</td><td class="text-right"><strong>' + stats.active_warehouses + '</strong></td></tr>';
    html += '<tr><td>{{ text_total_products }}</td><td class="text-right"><strong>' + stats.total_products + '</strong></td></tr>';
    html += '<tr><td>{{ text_total_stock_value }}</td><td class="text-right"><strong>' + parseFloat(stats.total_stock_value).toLocaleString() + '</strong></td></tr>';
    html += '<tr><td>{{ text_overall_utilization }}</td><td class="text-right"><strong>' + stats.overall_utilization + '%</strong></td></tr>';
    html += '</table>';
    html += '</div>';
    
    // تنبيهات
    html += '<div class="col-md-6">';
    html += '<h4><i class="fa fa-exclamation-triangle"></i> {{ text_alerts }}</h4>';
    html += '<table class="table table-striped">';
    html += '<tr><td>{{ text_low_stock_products }}</td><td class="text-right"><span class="label label-warning">' + stats.low_stock_products + '</span></td></tr>';
    html += '<tr><td>{{ text_out_of_stock_products }}</td><td class="text-right"><span class="label label-danger">' + stats.out_of_stock_products + '</span></td></tr>';
    html += '</table>';
    html += '</div>';
    
    html += '</div>';
    
    return html;
}

// عرض معلومات المستودع
function showWarehouseInfo(warehouse_id) {
    $('#warehouse-info-modal').modal('show');
    
    $.ajax({
        url: warehouse_url + '&user_token=' + user_token,
        type: 'POST',
        data: {action: 'get_warehouse_info', warehouse_id: warehouse_id},
        dataType: 'json',
        success: function(json) {
            if (json.success) {
                var html = buildWarehouseInfoHTML(json.warehouse);
                $('#warehouse-info-content').html(html);
            } else {
                $('#warehouse-info-content').html('<div class="alert alert-danger">' + json.error + '</div>');
            }
        },
        error: function() {
            $('#warehouse-info-content').html('<div class="alert alert-danger">{{ error_ajax }}</div>');
        }
    });
}

// بناء HTML لمعلومات المستودع
function buildWarehouseInfoHTML(warehouse) {
    var html = '<div class="row">';
    html += '<div class="col-md-12">';
    html += '<table class="table table-striped">';
    html += '<tr><td><strong>{{ column_name }}</strong></td><td>' + warehouse.name + '</td></tr>';
    html += '<tr><td><strong>{{ column_code }}</strong></td><td><code>' + warehouse.code + '</code></td></tr>';
    html += '<tr><td><strong>{{ column_location_type }}</strong></td><td>' + warehouse.location_type + '</td></tr>';
    html += '<tr><td><strong>{{ column_address }}</strong></td><td>' + warehouse.address + '</td></tr>';
    html += '<tr><td><strong>{{ column_manager }}</strong></td><td>' + warehouse.manager + '</td></tr>';
    html += '<tr><td><strong>{{ column_total_capacity }}</strong></td><td>' + warehouse.total_capacity + ' ' + warehouse.capacity_unit + '</td></tr>';
    html += '<tr><td><strong>{{ column_capacity_percentage }}</strong></td><td>' + warehouse.capacity_percentage + '%</td></tr>';
    html += '<tr><td><strong>{{ column_product_count }}</strong></td><td>' + warehouse.total_products + '</td></tr>';
    html += '<tr><td><strong>{{ column_total_value }}</strong></td><td>' + warehouse.total_value + '</td></tr>';
    html += '<tr><td><strong>{{ column_status }}</strong></td><td>' + warehouse.status + '</td></tr>';
    html += '</table>';
    html += '</div>';
    html += '</div>';
    
    return html;
}

// تصدير المستودعات
function exportWarehouses() {
    var url = warehouse_url + '/export&user_token=' + user_token;
    var filters = $('#filter-form').serialize();
    
    if (filters) {
        url += '&' + filters;
    }
    
    window.open(url, '_blank');
}

// طباعة المستودعات
function printWarehouses() {
    var url = warehouse_url + '/print&user_token=' + user_token;
    var filters = $('#filter-form').serialize();
    
    if (filters) {
        url += '&' + filters;
    }
    
    window.open(url, '_blank');
}

// تهيئة الصفحة
$(document).ready(function() {
    // تفعيل tooltips
    $('[data-toggle="tooltip"]').tooltip();
    
    // تفعيل البحث السريع
    $('#quick-search').on('keyup', function() {
        var value = $(this).val().toLowerCase();
        $('#warehouse-table tbody tr').filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
        });
    });
});
</script>

<style>
.huge {
    font-size: 40px;
}

.panel-body {
    padding: 15px;
}

.progress {
    height: 10px;
}

.badge {
    font-size: 11px;
}

.btn-group .btn {
    margin-right: 2px;
}

.btn-group .btn:last-child {
    margin-right: 0;
}

#warehouse-table th {
    background-color: #f5f5f5;
    font-weight: bold;
}

#warehouse-table tbody tr:hover {
    background-color: #f9f9f9;
}

.modal-lg {
    width: 90%;
}

@media (max-width: 768px) {
    .huge {
        font-size: 24px;
    }
    
    .btn-group .btn {
        margin-bottom: 5px;
    }
}
</style>

{{ footer }}