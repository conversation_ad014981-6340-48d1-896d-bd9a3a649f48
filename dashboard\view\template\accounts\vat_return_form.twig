{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="submit" form="form-vat-return" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fa fa-save"></i></button>
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    {% if success %}
    <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-file-text"></i> {{ text_vat_return_form }}</h3>
      </div>
      <div class="panel-body">
        <form action="{{ action }}" method="post" enctype="multipart/form-data" id="form-vat-return" class="form-horizontal">
          
          <!-- Period Selection -->
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-period">{{ entry_tax_period }}</label>
            <div class="col-sm-10">
              <div class="row">
                <div class="col-sm-6">
                  <select name="tax_period" id="input-period" class="form-control">
                    <option value="">{{ text_select_period }}</option>
                    <option value="monthly">{{ text_monthly }}</option>
                    <option value="quarterly">{{ text_quarterly }}</option>
                    <option value="annual">{{ text_annual }}</option>
                  </select>
                </div>
                <div class="col-sm-3">
                  <input type="text" name="period_year" value="{{ period_year }}" placeholder="{{ entry_year }}" class="form-control" />
                </div>
                <div class="col-sm-3">
                  <select name="period_month" class="form-control">
                    <option value="">{{ text_select_month }}</option>
                    {% for i in 1..12 %}
                    <option value="{{ i }}" {% if period_month == i %}selected{% endif %}>{{ months[i] }}</option>
                    {% endfor %}
                  </select>
                </div>
              </div>
            </div>
          </div>

          <!-- Company Information -->
          <fieldset>
            <legend>{{ text_company_information }}</legend>
            <div class="form-group">
              <label class="col-sm-2 control-label">{{ entry_company_name }}</label>
              <div class="col-sm-10">
                <input type="text" name="company_name" value="{{ company_name }}" class="form-control" readonly />
              </div>
            </div>
            <div class="form-group">
              <label class="col-sm-2 control-label">{{ entry_tax_number }}</label>
              <div class="col-sm-10">
                <input type="text" name="tax_number" value="{{ tax_number }}" placeholder="{{ entry_tax_number }}" class="form-control" />
              </div>
            </div>
            <div class="form-group">
              <label class="col-sm-2 control-label">{{ entry_commercial_register }}</label>
              <div class="col-sm-10">
                <input type="text" name="commercial_register" value="{{ commercial_register }}" placeholder="{{ entry_commercial_register }}" class="form-control" />
              </div>
            </div>
          </fieldset>

          <!-- VAT Calculations -->
          <fieldset>
            <legend>{{ text_vat_calculations }}</legend>
            
            <!-- Sales VAT by Rate -->
            <div class="panel panel-info">
              <div class="panel-heading">{{ text_sales_vat }}</div>
              <div class="panel-body">
                <div class="table-responsive">
                  <table class="table table-bordered">
                    <thead>
                      <tr>
                        <th>{{ column_vat_rate }}</th>
                        <th>{{ column_taxable_amount }}</th>
                        <th>{{ column_vat_amount }}</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td>14% {{ text_standard_rate }}</td>
                        <td><input type="text" name="sales_taxable_14" value="{{ sales_taxable_14 }}" class="form-control text-right" /></td>
                        <td><input type="text" name="sales_vat_14" value="{{ sales_vat_14 }}" class="form-control text-right" readonly /></td>
                      </tr>
                      <tr>
                        <td>10% {{ text_reduced_rate }}</td>
                        <td><input type="text" name="sales_taxable_10" value="{{ sales_taxable_10 }}" class="form-control text-right" /></td>
                        <td><input type="text" name="sales_vat_10" value="{{ sales_vat_10 }}" class="form-control text-right" readonly /></td>
                      </tr>
                      <tr>
                        <td>5% {{ text_special_rate }}</td>
                        <td><input type="text" name="sales_taxable_5" value="{{ sales_taxable_5 }}" class="form-control text-right" /></td>
                        <td><input type="text" name="sales_vat_5" value="{{ sales_vat_5 }}" class="form-control text-right" readonly /></td>
                      </tr>
                      <tr>
                        <td>0% {{ text_exempt }}</td>
                        <td><input type="text" name="sales_taxable_0" value="{{ sales_taxable_0 }}" class="form-control text-right" /></td>
                        <td><input type="text" name="sales_vat_0" value="0.00" class="form-control text-right" readonly /></td>
                      </tr>
                      <tr class="info">
                        <td><strong>{{ text_total }}</strong></td>
                        <td><strong id="total-sales-taxable">{{ total_sales_taxable }}</strong></td>
                        <td><strong id="total-sales-vat">{{ total_sales_vat }}</strong></td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>

            <!-- Purchases VAT by Rate -->
            <div class="panel panel-warning">
              <div class="panel-heading">{{ text_purchases_vat }}</div>
              <div class="panel-body">
                <div class="table-responsive">
                  <table class="table table-bordered">
                    <thead>
                      <tr>
                        <th>{{ column_vat_rate }}</th>
                        <th>{{ column_taxable_amount }}</th>
                        <th>{{ column_vat_amount }}</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td>14% {{ text_standard_rate }}</td>
                        <td><input type="text" name="purchases_taxable_14" value="{{ purchases_taxable_14 }}" class="form-control text-right" /></td>
                        <td><input type="text" name="purchases_vat_14" value="{{ purchases_vat_14 }}" class="form-control text-right" readonly /></td>
                      </tr>
                      <tr>
                        <td>10% {{ text_reduced_rate }}</td>
                        <td><input type="text" name="purchases_taxable_10" value="{{ purchases_taxable_10 }}" class="form-control text-right" /></td>
                        <td><input type="text" name="purchases_vat_10" value="{{ purchases_vat_10 }}" class="form-control text-right" readonly /></td>
                      </tr>
                      <tr>
                        <td>5% {{ text_special_rate }}</td>
                        <td><input type="text" name="purchases_taxable_5" value="{{ purchases_taxable_5 }}" class="form-control text-right" /></td>
                        <td><input type="text" name="purchases_vat_5" value="{{ purchases_vat_5 }}" class="form-control text-right" readonly /></td>
                      </tr>
                      <tr>
                        <td>0% {{ text_exempt }}</td>
                        <td><input type="text" name="purchases_taxable_0" value="{{ purchases_taxable_0 }}" class="form-control text-right" /></td>
                        <td><input type="text" name="purchases_vat_0" value="0.00" class="form-control text-right" readonly /></td>
                      </tr>
                      <tr class="warning">
                        <td><strong>{{ text_total }}</strong></td>
                        <td><strong id="total-purchases-taxable">{{ total_purchases_taxable }}</strong></td>
                        <td><strong id="total-purchases-vat">{{ total_purchases_vat }}</strong></td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>

            <!-- Net VAT Calculation -->
            <div class="panel panel-success">
              <div class="panel-heading">{{ text_net_vat_calculation }}</div>
              <div class="panel-body">
                <div class="row">
                  <div class="col-sm-4">
                    <div class="form-group">
                      <label>{{ text_total_sales_vat }}</label>
                      <input type="text" id="display-total-sales-vat" value="{{ total_sales_vat }}" class="form-control text-right" readonly />
                    </div>
                  </div>
                  <div class="col-sm-4">
                    <div class="form-group">
                      <label>{{ text_total_purchases_vat }}</label>
                      <input type="text" id="display-total-purchases-vat" value="{{ total_purchases_vat }}" class="form-control text-right" readonly />
                    </div>
                  </div>
                  <div class="col-sm-4">
                    <div class="form-group">
                      <label>{{ text_net_vat_payable }}</label>
                      <input type="text" id="display-net-vat" value="{{ net_vat }}" class="form-control text-right font-weight-bold" readonly />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </fieldset>

          <!-- Additional Information -->
          <fieldset>
            <legend>{{ text_additional_information }}</legend>
            <div class="form-group">
              <label class="col-sm-2 control-label">{{ entry_previous_period_credit }}</label>
              <div class="col-sm-10">
                <input type="text" name="previous_period_credit" value="{{ previous_period_credit }}" placeholder="0.00" class="form-control" />
              </div>
            </div>
            <div class="form-group">
              <label class="col-sm-2 control-label">{{ entry_adjustments }}</label>
              <div class="col-sm-10">
                <textarea name="adjustments" rows="3" placeholder="{{ entry_adjustments }}" class="form-control">{{ adjustments }}</textarea>
              </div>
            </div>
            <div class="form-group">
              <label class="col-sm-2 control-label">{{ entry_payment_method }}</label>
              <div class="col-sm-10">
                <select name="payment_method" class="form-control">
                  <option value="">{{ text_select_payment_method }}</option>
                  <option value="bank_transfer">{{ text_bank_transfer }}</option>
                  <option value="check">{{ text_check }}</option>
                  <option value="cash">{{ text_cash }}</option>
                  <option value="offset">{{ text_offset_credit }}</option>
                </select>
              </div>
            </div>
          </fieldset>

          <!-- ETA Submission -->
          <fieldset>
            <legend>{{ text_eta_submission }}</legend>
            <div class="form-group">
              <label class="col-sm-2 control-label">{{ entry_submission_method }}</label>
              <div class="col-sm-10">
                <div class="radio">
                  <label>
                    <input type="radio" name="submission_method" value="electronic" checked />
                    {{ text_electronic_submission }}
                  </label>
                </div>
                <div class="radio">
                  <label>
                    <input type="radio" name="submission_method" value="manual" />
                    {{ text_manual_submission }}
                  </label>
                </div>
              </div>
            </div>
            <div class="form-group">
              <label class="col-sm-2 control-label">{{ entry_due_date }}</label>
              <div class="col-sm-10">
                <div class="input-group date">
                  <input type="text" name="due_date" value="{{ due_date }}" placeholder="{{ entry_due_date }}" class="form-control" />
                  <span class="input-group-btn">
                    <button class="btn btn-default" type="button"><i class="fa fa-calendar"></i></button>
                  </span>
                </div>
              </div>
            </div>
          </fieldset>

          <!-- Declaration -->
          <fieldset>
            <legend>{{ text_declaration }}</legend>
            <div class="form-group">
              <div class="col-sm-12">
                <div class="checkbox">
                  <label>
                    <input type="checkbox" name="declaration_accuracy" value="1" required />
                    {{ text_declaration_accuracy }}
                  </label>
                </div>
                <div class="checkbox">
                  <label>
                    <input type="checkbox" name="declaration_completeness" value="1" required />
                    {{ text_declaration_completeness }}
                  </label>
                </div>
                <div class="checkbox">
                  <label>
                    <input type="checkbox" name="declaration_compliance" value="1" required />
                    {{ text_declaration_compliance }}
                  </label>
                </div>
              </div>
            </div>
          </fieldset>

        </form>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript">
$(document).ready(function() {
    // Auto-calculate VAT amounts
    function calculateVAT() {
        // Sales VAT calculations
        var salesTaxable14 = parseFloat($('input[name="sales_taxable_14"]').val()) || 0;
        var salesTaxable10 = parseFloat($('input[name="sales_taxable_10"]').val()) || 0;
        var salesTaxable5 = parseFloat($('input[name="sales_taxable_5"]').val()) || 0;
        var salesTaxable0 = parseFloat($('input[name="sales_taxable_0"]').val()) || 0;
        
        $('input[name="sales_vat_14"]').val((salesTaxable14 * 0.14).toFixed(2));
        $('input[name="sales_vat_10"]').val((salesTaxable10 * 0.10).toFixed(2));
        $('input[name="sales_vat_5"]').val((salesTaxable5 * 0.05).toFixed(2));
        
        var totalSalesVat = (salesTaxable14 * 0.14) + (salesTaxable10 * 0.10) + (salesTaxable5 * 0.05);
        var totalSalesTaxable = salesTaxable14 + salesTaxable10 + salesTaxable5 + salesTaxable0;
        
        $('#total-sales-vat').text(totalSalesVat.toFixed(2));
        $('#total-sales-taxable').text(totalSalesTaxable.toFixed(2));
        $('#display-total-sales-vat').val(totalSalesVat.toFixed(2));
        
        // Purchases VAT calculations
        var purchasesTaxable14 = parseFloat($('input[name="purchases_taxable_14"]').val()) || 0;
        var purchasesTaxable10 = parseFloat($('input[name="purchases_taxable_10"]').val()) || 0;
        var purchasesTaxable5 = parseFloat($('input[name="purchases_taxable_5"]').val()) || 0;
        var purchasesTaxable0 = parseFloat($('input[name="purchases_taxable_0"]').val()) || 0;
        
        $('input[name="purchases_vat_14"]').val((purchasesTaxable14 * 0.14).toFixed(2));
        $('input[name="purchases_vat_10"]').val((purchasesTaxable10 * 0.10).toFixed(2));
        $('input[name="purchases_vat_5"]').val((purchasesTaxable5 * 0.05).toFixed(2));
        
        var totalPurchasesVat = (purchasesTaxable14 * 0.14) + (purchasesTaxable10 * 0.10) + (purchasesTaxable5 * 0.05);
        var totalPurchasesTaxable = purchasesTaxable14 + purchasesTaxable10 + purchasesTaxable5 + purchasesTaxable0;
        
        $('#total-purchases-vat').text(totalPurchasesVat.toFixed(2));
        $('#total-purchases-taxable').text(totalPurchasesTaxable.toFixed(2));
        $('#display-total-purchases-vat').val(totalPurchasesVat.toFixed(2));
        
        // Net VAT calculation
        var netVat = totalSalesVat - totalPurchasesVat;
        $('#display-net-vat').val(netVat.toFixed(2));
    }
    
    // Bind calculation to input changes
    $('input[name*="taxable"]').on('input', calculateVAT);
    
    // Initial calculation
    calculateVAT();
    
    // Date picker
    $('.date').datetimepicker({
        language: '{{ language }}',
        pickTime: false
    });
});
</script>

{{ footer }}
