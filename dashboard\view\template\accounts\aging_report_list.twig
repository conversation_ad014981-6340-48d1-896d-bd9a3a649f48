<!DOCTYPE html>
<html dir="{{ direction }}" lang="{{ lang }}">
<head>
<meta charset="UTF-8" />
<title>{{ title }}</title>
<style>
body {
    margin:10px;
    font-family: sans-serif;
    font-size:14px;
}
table {
    border-collapse: collapse;
    margin-bottom:20px;
}
th, td {
    border:1px solid #000;
    padding:5px;
    text-align:right;
}
th {
    background:#eee;
    text-align:center;
}
.section-title {
    font-weight: bold;
    margin-top:20px;
    margin-bottom:10px;
    font-size:16px;
}
</style>
</head>
<body>
<h2>{{ text_aging_report }}</h2>
<p>{{ text_period_end }}: {{ end_date }}</p>

<div class="section-title">{{ text_buckets }}</div>
<table>
<thead>
<tr>
    <th>{{ text_0_30 }}</th>
    <th>{{ text_31_60 }}</th>
    <th>{{ text_61_90 }}</th>
    <th>{{ text_over_90 }}</th>
</tr>
</thead>
<tbody>
<tr>
    <td>{{ buckets['0-30'] }}</td>
    <td>{{ buckets['31-60'] }}</td>
    <td>{{ buckets['61-90'] }}</td>
    <td>{{ buckets['>90'] }}</td>
</tr>
</tbody>
</table>

<div class="section-title">{{ text_customer_details }}</div>
<table>
<thead>
<tr>
    <th>{{ text_customer_name }}</th>
    <th>{{ text_0_30 }}</th>
    <th>{{ text_31_60 }}</th>
    <th>{{ text_61_90 }}</th>
    <th>{{ text_over_90 }}</th>
</tr>
</thead>
<tbody>
{% for cid, cdata in customers_data %}
<tr>
    <td style="text-align:left">{{ cdata.customer_name }}</td>
    <td>{{ cdata['0-30'] }}</td>
    <td>{{ cdata['31-60'] }}</td>
    <td>{{ cdata['61-90'] }}</td>
    <td>{{ cdata['>90'] }}</td>
</tr>
{% endfor %}
{% if customers_data is empty %}
<tr><td colspan="5" style="text-align:center">{{ error_no_data }}</td></tr>
{% endif %}
</tbody>
</table>

<p>{{ whoprint }} - {{ printdate }}</p>
</body>
</html>
