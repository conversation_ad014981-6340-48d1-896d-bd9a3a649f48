{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="submit" form="supplier\accounts-form" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fas fa-save"></i></button>
        <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-light"><i class="fas fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    
    <div class="card">
      <div class="card-header"><i class="fas fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}chart-bar{% elif screen_type == 'settings' %}cog{% else %}pencil-alt{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="card-body">
        <form id="supplier\accounts-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-account_statistics">{{ text_account_statistics }}</label>
            <div class="col-sm-10">
              <input type="text" name="account_statistics" value="{{ account_statistics }}" placeholder="{{ text_account_statistics }}" id="input-account_statistics" class="form-control" />
              {% if error_account_statistics %}
                <div class="invalid-feedback">{{ error_account_statistics }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-account_summary">{{ text_account_summary }}</label>
            <div class="col-sm-10">
              <input type="text" name="account_summary" value="{{ account_summary }}" placeholder="{{ text_account_summary }}" id="input-account_summary" class="form-control" />
              {% if error_account_summary %}
                <div class="invalid-feedback">{{ error_account_summary }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-account_transactions">{{ text_account_transactions }}</label>
            <div class="col-sm-10">
              <input type="text" name="account_transactions" value="{{ account_transactions }}" placeholder="{{ text_account_transactions }}" id="input-account_transactions" class="form-control" />
              {% if error_account_transactions %}
                <div class="invalid-feedback">{{ error_account_transactions }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-accounts">{{ text_accounts }}</label>
            <div class="col-sm-10">
              <input type="text" name="accounts" value="{{ accounts }}" placeholder="{{ text_accounts }}" id="input-accounts" class="form-control" />
              {% if error_accounts %}
                <div class="invalid-feedback">{{ error_accounts }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-aging_report">{{ text_aging_report }}</label>
            <div class="col-sm-10">
              <input type="text" name="aging_report" value="{{ aging_report }}" placeholder="{{ text_aging_report }}" id="input-aging_report" class="form-control" />
              {% if error_aging_report %}
                <div class="invalid-feedback">{{ error_aging_report }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-aging_summary">{{ text_aging_summary }}</label>
            <div class="col-sm-10">
              <input type="text" name="aging_summary" value="{{ aging_summary }}" placeholder="{{ text_aging_summary }}" id="input-aging_summary" class="form-control" />
              {% if error_aging_summary %}
                <div class="invalid-feedback">{{ error_aging_summary }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-closing_balance">{{ text_closing_balance }}</label>
            <div class="col-sm-10">
              <input type="text" name="closing_balance" value="{{ closing_balance }}" placeholder="{{ text_closing_balance }}" id="input-closing_balance" class="form-control" />
              {% if error_closing_balance %}
                <div class="invalid-feedback">{{ error_closing_balance }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-date_end">{{ text_date_end }}</label>
            <div class="col-sm-10">
              <input type="text" name="date_end" value="{{ date_end }}" placeholder="{{ text_date_end }}" id="input-date_end" class="form-control" />
              {% if error_date_end %}
                <div class="invalid-feedback">{{ error_date_end }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-date_start">{{ text_date_start }}</label>
            <div class="col-sm-10">
              <input type="text" name="date_start" value="{{ date_start }}" placeholder="{{ text_date_start }}" id="input-date_start" class="form-control" />
              {% if error_date_start %}
                <div class="invalid-feedback">{{ error_date_start }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_account_status">{{ text_filter_account_status }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_account_status" value="{{ filter_account_status }}" placeholder="{{ text_filter_account_status }}" id="input-filter_account_status" class="form-control" />
              {% if error_filter_account_status %}
                <div class="invalid-feedback">{{ error_filter_account_status }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_balance_max">{{ text_filter_balance_max }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_balance_max" value="{{ filter_balance_max }}" placeholder="{{ text_filter_balance_max }}" id="input-filter_balance_max" class="form-control" />
              {% if error_filter_balance_max %}
                <div class="invalid-feedback">{{ error_filter_balance_max }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_balance_min">{{ text_filter_balance_min }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_balance_min" value="{{ filter_balance_min }}" placeholder="{{ text_filter_balance_min }}" id="input-filter_balance_min" class="form-control" />
              {% if error_filter_balance_min %}
                <div class="invalid-feedback">{{ error_filter_balance_min }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_supplier_name">{{ text_filter_supplier_name }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_supplier_name" value="{{ filter_supplier_name }}" placeholder="{{ text_filter_supplier_name }}" id="input-filter_supplier_name" class="form-control" />
              {% if error_filter_supplier_name %}
                <div class="invalid-feedback">{{ error_filter_supplier_name }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="invalid-feedback">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-opening_balance">{{ text_opening_balance }}</label>
            <div class="col-sm-10">
              <input type="text" name="opening_balance" value="{{ opening_balance }}" placeholder="{{ text_opening_balance }}" id="input-opening_balance" class="form-control" />
              {% if error_opening_balance %}
                <div class="invalid-feedback">{{ error_opening_balance }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-order">{{ text_order }}</label>
            <div class="col-sm-10">
              <input type="text" name="order" value="{{ order }}" placeholder="{{ text_order }}" id="input-order" class="form-control" />
              {% if error_order %}
                <div class="invalid-feedback">{{ error_order }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-pagination">{{ text_pagination }}</label>
            <div class="col-sm-10">
              <input type="text" name="pagination" value="{{ pagination }}" placeholder="{{ text_pagination }}" id="input-pagination" class="form-control" />
              {% if error_pagination %}
                <div class="invalid-feedback">{{ error_pagination }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-payment_history">{{ text_payment_history }}</label>
            <div class="col-sm-10">
              <input type="text" name="payment_history" value="{{ payment_history }}" placeholder="{{ text_payment_history }}" id="input-payment_history" class="form-control" />
              {% if error_payment_history %}
                <div class="invalid-feedback">{{ error_payment_history }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-results">{{ text_results }}</label>
            <div class="col-sm-10">
              <input type="text" name="results" value="{{ results }}" placeholder="{{ text_results }}" id="input-results" class="form-control" />
              {% if error_results %}
                <div class="invalid-feedback">{{ error_results }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort">{{ text_sort }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort" value="{{ sort }}" placeholder="{{ text_sort }}" id="input-sort" class="form-control" />
              {% if error_sort %}
                <div class="invalid-feedback">{{ error_sort }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_balance">{{ text_sort_balance }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_balance" value="{{ sort_balance }}" placeholder="{{ text_sort_balance }}" id="input-sort_balance" class="form-control" />
              {% if error_sort_balance %}
                <div class="invalid-feedback">{{ error_sort_balance }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_status">{{ text_sort_status }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_status" value="{{ sort_status }}" placeholder="{{ text_sort_status }}" id="input-sort_status" class="form-control" />
              {% if error_sort_status %}
                <div class="invalid-feedback">{{ error_sort_status }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_supplier">{{ text_sort_supplier }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_supplier" value="{{ sort_supplier }}" placeholder="{{ text_sort_supplier }}" id="input-sort_supplier" class="form-control" />
              {% if error_sort_supplier %}
                <div class="invalid-feedback">{{ error_sort_supplier }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-statement_transactions">{{ text_statement_transactions }}</label>
            <div class="col-sm-10">
              <input type="text" name="statement_transactions" value="{{ statement_transactions }}" placeholder="{{ text_statement_transactions }}" id="input-statement_transactions" class="form-control" />
              {% if error_statement_transactions %}
                <div class="invalid-feedback">{{ error_statement_transactions }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-supplier_account">{{ text_supplier_account }}</label>
            <div class="col-sm-10">
              <input type="text" name="supplier_account" value="{{ supplier_account }}" placeholder="{{ text_supplier_account }}" id="input-supplier_account" class="form-control" />
              {% if error_supplier_account %}
                <div class="invalid-feedback">{{ error_supplier_account }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-supplier_id">{{ text_supplier_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="supplier_id" value="{{ supplier_id }}" placeholder="{{ text_supplier_id }}" id="input-supplier_id" class="form-control" />
              {% if error_supplier_id %}
                <div class="invalid-feedback">{{ error_supplier_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-supplier_info">{{ text_supplier_info }}</label>
            <div class="col-sm-10">
              <input type="text" name="supplier_info" value="{{ supplier_info }}" placeholder="{{ text_supplier_info }}" id="input-supplier_info" class="form-control" />
              {% if error_supplier_info %}
                <div class="invalid-feedback">{{ error_supplier_info }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-suppliers">{{ text_suppliers }}</label>
            <div class="col-sm-10">
              <input type="text" name="suppliers" value="{{ suppliers }}" placeholder="{{ text_suppliers }}" id="input-suppliers" class="form-control" />
              {% if error_suppliers %}
                <div class="invalid-feedback">{{ error_suppliers }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="invalid-feedback">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}