{{ header }}{{ column_left }}

<!-- ═══════════════════════════════════════════════════════════════════════════════ -->
<!-- AYM ERP - Enterprise Grade Review Management Interface (الدستور الشامل) -->
<!-- ═══════════════════════════════════════════════════════════════════════════════ -->

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <!-- Advanced Filter Toggle -->
        <button type="button" data-toggle="tooltip" title="{{ button_filter }}" onclick="$('#filter-review').toggleClass('hidden-sm hidden-xs');" class="btn btn-default hidden-md hidden-lg">
          <i class="fa fa-filter"></i>
        </button>

        <!-- AI Features Dropdown (Enterprise Grade Plus) -->
        {% if user_permissions.ai_features %}
        <div class="btn-group">
          <button type="button" class="btn btn-info dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
            <i class="fa fa-brain"></i> {{ text_ai_features }} <span class="caret"></span>
          </button>
          <ul class="dropdown-menu">
            {% if user_permissions.sentiment_analysis %}
            <li><a href="#" onclick="bulkSentimentAnalysis()"><i class="fa fa-heart"></i> {{ text_sentiment_analysis }}</a></li>
            {% endif %}
            {% if user_permissions.fraud_detection %}
            <li><a href="#" onclick="bulkFraudCheck()"><i class="fa fa-shield"></i> {{ text_fraud_detection }}</a></li>
            {% endif %}
            {% if user_permissions.advanced_analytics %}
            <li role="separator" class="divider"></li>
            <li><a href="{{ analytics_url }}"><i class="fa fa-chart-line"></i> {{ text_analytics }}</a></li>
            <li><a href="{{ reputation_url }}"><i class="fa fa-star"></i> {{ text_product_reputation }}</a></li>
            {% endif %}
          </ul>
        </div>
        {% endif %}

        <!-- Export Options -->
        <div class="btn-group">
          <button type="button" class="btn btn-success dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
            <i class="fa fa-download"></i> {{ text_export_reviews }} <span class="caret"></span>
          </button>
          <ul class="dropdown-menu">
            <li><a href="#" onclick="exportReviews('excel')"><i class="fa fa-file-excel-o"></i> Excel</a></li>
            <li><a href="#" onclick="exportReviews('csv')"><i class="fa fa-file-text-o"></i> CSV</a></li>
            <li><a href="#" onclick="exportReviews('pdf')"><i class="fa fa-file-pdf-o"></i> PDF</a></li>
          </ul>
        </div>

        <!-- Standard Actions -->
        <a href="{{ add }}" data-toggle="tooltip" title="{{ button_add }}" class="btn btn-primary">
          <i class="fa fa-plus"></i>
        </a>
        <button type="button" data-toggle="tooltip" title="{{ button_delete }}" class="btn btn-danger" onclick="confirm('{{ text_confirm }}') ? $('#form-review').submit() : false;">
          <i class="fa fa-trash-o"></i>
        </button>
      </div>

      <div class="panel-heading">
        <h3 class="panel-title">
          <i class="fa fa-star-half-o"></i> {{ heading_title }}
          <small class="text-muted">{{ text_enterprise_grade_plus }}</small>
        </h3>
      </div>
    </div>
  </div>
  <div class="container-fluid">{% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    {% if success %}
    <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    <div class="row">
      <!-- Advanced Filters Panel (Enterprise Grade Plus) -->
      <div id="filter-review" class="col-md-3 col-md-push-9 col-sm-12 hidden-sm hidden-xs">
        <div class="panel panel-primary">
          <div class="panel-heading">
            <h4 class="panel-title">
              <i class="fa fa-filter"></i> {{ text_advanced_filters }}
            </h4>
          </div>
          <div class="panel-body">
            <!-- Basic Filters -->
            <div class="form-group">
              <label class="control-label" for="input-product">{{ entry_product }}</label>
              <input type="text" name="filter_product" value="{{ filter_product }}" placeholder="{{ entry_product }}" id="input-product" class="form-control" autocomplete="off" />
            </div>

            <div class="form-group">
              <label class="control-label" for="input-author">{{ entry_author }}</label>
              <input type="text" name="filter_author" value="{{ filter_author }}" placeholder="{{ entry_author }}" id="input-author" class="form-control" />
            </div>

            <div class="form-group">
              <label class="control-label" for="input-status">{{ entry_status }}</label>
              <select name="filter_status" id="input-status" class="form-control">
                <option value="">{{ text_select }}</option>
                {% if filter_status == '1' %}
                  <option value="1" selected="selected">{{ text_enabled }}</option>
                {% else %}
                  <option value="1">{{ text_enabled }}</option>
                {% endif %}
                {% if filter_status == '0' %}
                  <option value="0" selected="selected">{{ text_disabled }}</option>
                {% else %}
                  <option value="0">{{ text_disabled }}</option>
                {% endif %}
              </select>
            </div>

            <!-- Rating Filter -->
            <div class="form-group">
              <label class="control-label" for="input-rating">{{ text_filter_by_rating }}</label>
              <select name="filter_rating" id="input-rating" class="form-control">
                <option value="">{{ text_select }}</option>
                {% for i in 1..5 %}
                  {% if filter_rating == i %}
                    <option value="{{ i }}" selected="selected">{{ i }} {{ text_stars }}</option>
                  {% else %}
                    <option value="{{ i }}">{{ i }} {{ text_stars }}</option>
                  {% endif %}
                {% endfor %}
              </select>
            </div>

            <!-- AI Features Filters (Enterprise Grade Plus) -->
            {% if user_permissions.sentiment_analysis %}
            <div class="form-group">
              <label class="control-label" for="input-sentiment">{{ text_filter_by_sentiment }}</label>
              <select name="filter_sentiment" id="input-sentiment" class="form-control">
                <option value="">{{ text_select }}</option>
                <option value="positive" {% if filter_sentiment == 'positive' %}selected="selected"{% endif %}>{{ text_sentiment_positive }}</option>
                <option value="negative" {% if filter_sentiment == 'negative' %}selected="selected"{% endif %}>{{ text_sentiment_negative }}</option>
                <option value="neutral" {% if filter_sentiment == 'neutral' %}selected="selected"{% endif %}>{{ text_sentiment_neutral }}</option>
              </select>
            </div>
            {% endif %}

            {% if user_permissions.fraud_detection %}
            <div class="form-group">
              <label class="control-label" for="input-fraud">{{ text_filter_by_fraud }}</label>
              <select name="filter_fraud" id="input-fraud" class="form-control">
                <option value="">{{ text_select }}</option>
                <option value="suspicious" {% if filter_fraud == 'suspicious' %}selected="selected"{% endif %}>{{ text_suspicious }}</option>
                <option value="not_suspicious" {% if filter_fraud == 'not_suspicious' %}selected="selected"{% endif %}>{{ text_not_suspicious }}</option>
              </select>
            </div>
            {% endif %}

            <!-- Date Range Filter -->
            <div class="form-group">
              <label class="control-label">{{ text_date_range }}</label>
              <div class="row">
                <div class="col-sm-6">
                  <div class="input-group date">
                    <input type="text" name="filter_date_start" value="{{ filter_date_start }}" placeholder="{{ text_from_date }}" data-date-format="YYYY-MM-DD" id="input-date-start" class="form-control" />
                    <span class="input-group-btn">
                      <button type="button" class="btn btn-default"><i class="fa fa-calendar"></i></button>
                    </span>
                  </div>
                </div>
                <div class="col-sm-6">
                  <div class="input-group date">
                    <input type="text" name="filter_date_end" value="{{ filter_date_end }}" placeholder="{{ text_to_date }}" data-date-format="YYYY-MM-DD" id="input-date-end" class="form-control" />
                    <span class="input-group-btn">
                      <button type="button" class="btn btn-default"><i class="fa fa-calendar"></i></button>
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Filter Actions -->
            <div class="form-group">
              <div class="row">
                <div class="col-sm-6">
                  <button type="button" id="button-filter" class="btn btn-primary btn-block">
                    <i class="fa fa-filter"></i> {{ text_apply_filters }}
                  </button>
                </div>
                <div class="col-sm-6">
                  <button type="button" id="button-clear" class="btn btn-default btn-block">
                    <i class="fa fa-times"></i> {{ text_clear_filters }}
                  </button>
                </div>
              </div>
            </div>

            <!-- Quick Stats (Enterprise Grade Plus) -->
            <div class="panel panel-info">
              <div class="panel-heading">
                <h5 class="panel-title"><i class="fa fa-chart-bar"></i> {{ text_quick_stats }}</h5>
              </div>
              <div class="panel-body">
                <div class="row">
                  <div class="col-xs-6">
                    <div class="text-center">
                      <h4 class="text-primary">{{ total_reviews }}</h4>
                      <small>{{ text_total_reviews }}</small>
                    </div>
                  </div>
                  <div class="col-xs-6">
                    <div class="text-center">
                      <h4 class="text-warning">{{ pending_reviews }}</h4>
                      <small>{{ text_pending_approval }}</small>
                    </div>
                  </div>
                </div>
                <div class="row" style="margin-top: 10px;">
                  <div class="col-xs-6">
                    <div class="text-center">
                      <h4 class="text-success">{{ average_rating }}</h4>
                      <small>{{ text_average_rating }}</small>
                    </div>
                  </div>
                  <div class="col-xs-6">
                    <div class="text-center">
                      <h4 class="text-danger">{{ suspicious_reviews }}</h4>
                      <small>{{ text_suspicious_reviews }}</small>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- Main Data Table (Enterprise Grade Plus) -->
      <div class="col-md-9 col-md-pull-3 col-sm-12">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title">
              <i class="fa fa-star-half-o"></i> {{ text_list }}
              <span class="badge badge-info">{{ total_reviews }}</span>
            </h3>

            <!-- Bulk Actions Toolbar -->
            {% if user_permissions.bulk_operations %}
            <div class="pull-right">
              <div class="btn-group">
                <button type="button" class="btn btn-sm btn-default dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                  <i class="fa fa-cogs"></i> {{ text_bulk_operations }} <span class="caret"></span>
                </button>
                <ul class="dropdown-menu">
                  <li><a href="#" onclick="bulkAction('approve')"><i class="fa fa-check text-success"></i> {{ text_bulk_approve }}</a></li>
                  <li><a href="#" onclick="bulkAction('reject')"><i class="fa fa-times text-danger"></i> {{ text_bulk_reject }}</a></li>
                  <li><a href="#" onclick="bulkAction('delete')"><i class="fa fa-trash text-danger"></i> {{ text_bulk_delete }}</a></li>
                  {% if user_permissions.sentiment_analysis %}
                  <li role="separator" class="divider"></li>
                  <li><a href="#" onclick="bulkAction('analyze_sentiment')"><i class="fa fa-heart text-info"></i> {{ text_bulk_analyze_sentiment }}</a></li>
                  {% endif %}
                  {% if user_permissions.fraud_detection %}
                  <li><a href="#" onclick="bulkAction('check_fraud')"><i class="fa fa-shield text-warning"></i> {{ text_bulk_check_fraud }}</a></li>
                  {% endif %}
                </ul>
              </div>
            </div>
            {% endif %}
          </div>

          <div class="panel-body">
            <form action="{{ delete }}" method="post" enctype="multipart/form-data" id="form-review">
              <div class="table-responsive">
                <table class="table table-bordered table-hover table-striped" id="reviews-table">
                  <thead class="thead-dark">
                    <tr>
                      <td style="width: 1px;" class="text-center">
                        <input type="checkbox" onclick="$('input[name*=\'selected\']').prop('checked', this.checked);" />
                      </td>
                      <td class="text-left">
                        {% if sort == 'pd.name' %}
                          <a href="{{ sort_product }}" class="{{ order|lower }}">{{ column_product }}</a>
                        {% else %}
                          <a href="{{ sort_product }}">{{ column_product }}</a>
                        {% endif %}
                      </td>
                      <td class="text-left">
                        {% if sort == 'r.author' %}
                          <a href="{{ sort_author }}" class="{{ order|lower }}">{{ column_author }}</a>
                        {% else %}
                          <a href="{{ sort_author }}">{{ column_author }}</a>
                        {% endif %}
                      </td>
                      <td class="text-center">
                        {% if sort == 'r.rating' %}
                          <a href="{{ sort_rating }}" class="{{ order|lower }}">{{ column_rating }}</a>
                        {% else %}
                          <a href="{{ sort_rating }}">{{ column_rating }}</a>
                        {% endif %}
                      </td>

                      <!-- AI Features Columns (Enterprise Grade Plus) -->
                      {% if user_permissions.sentiment_analysis %}
                      <td class="text-center">{{ text_sentiment_analysis }}</td>
                      {% endif %}
                      {% if user_permissions.fraud_detection %}
                      <td class="text-center">{{ text_fraud_detection }}</td>
                      {% endif %}

                      <td class="text-center">
                        {% if sort == 'r.status' %}
                          <a href="{{ sort_status }}" class="{{ order|lower }}">{{ column_status }}</a>
                        {% else %}
                          <a href="{{ sort_status }}">{{ column_status }}</a>
                        {% endif %}
                      </td>
                      <td class="text-center">
                        {% if sort == 'r.date_added' %}
                          <a href="{{ sort_date_added }}" class="{{ order|lower }}">{{ column_date_added }}</a>
                        {% else %}
                          <a href="{{ sort_date_added }}">{{ column_date_added }}</a>
                        {% endif %}
                      </td>
                      <td class="text-center">{{ text_quick_actions }}</td>
                    </tr>
                  </thead>
                  <tbody>
                  {% if reviews %}
                    {% for review in reviews %}
                    <tr class="review-row" data-review-id="{{ review.review_id }}">
                      <!-- Selection Checkbox -->
                      <td class="text-center">
                        {% if review.review_id in selected %}
                          <input type="checkbox" name="selected[]" value="{{ review.review_id }}" checked="checked" />
                        {% else %}
                          <input type="checkbox" name="selected[]" value="{{ review.review_id }}" />
                        {% endif %}
                      </td>

                      <!-- Product Name -->
                      <td class="text-left">
                        <strong>{{ review.name }}</strong>
                        {% if review.text %}
                        <br><small class="text-muted">{{ review.text|slice(0, 100) }}{% if review.text|length > 100 %}...{% endif %}</small>
                        {% endif %}
                      </td>

                      <!-- Author -->
                      <td class="text-left">
                        <div>
                          <strong>{{ review.author }}</strong>
                          {% if review.customer_id %}
                          <br><small class="text-success"><i class="fa fa-user"></i> {{ text_verified_purchase }}</small>
                          {% endif %}
                        </div>
                      </td>

                      <!-- Rating with Stars -->
                      <td class="text-center">
                        <div class="rating-stars">
                          {% for i in 1..5 %}
                            {% if i <= review.rating %}
                              <i class="fa fa-star text-warning"></i>
                            {% else %}
                              <i class="fa fa-star-o text-muted"></i>
                            {% endif %}
                          {% endfor %}
                          <br><small>({{ review.rating }}/5)</small>
                        </div>
                      </td>

                      <!-- AI Features Columns (Enterprise Grade Plus) -->
                      {% if user_permissions.sentiment_analysis %}
                      <td class="text-center">
                        {% if review.sentiment %}
                          {% if review.sentiment.label == 'positive' %}
                            <span class="label label-success">
                              <i class="fa fa-smile-o"></i> {{ text_sentiment_positive }}
                            </span>
                            <br><small>{{ review.sentiment.score }}%</small>
                          {% elseif review.sentiment.label == 'negative' %}
                            <span class="label label-danger">
                              <i class="fa fa-frown-o"></i> {{ text_sentiment_negative }}
                            </span>
                            <br><small>{{ review.sentiment.score }}%</small>
                          {% else %}
                            <span class="label label-default">
                              <i class="fa fa-meh-o"></i> {{ text_sentiment_neutral }}
                            </span>
                            <br><small>{{ review.sentiment.score }}%</small>
                          {% endif %}
                        {% else %}
                          <button class="btn btn-xs btn-info" onclick="analyzeSentiment({{ review.review_id }})">
                            <i class="fa fa-heart"></i> {{ text_analyze_sentiment }}
                          </button>
                        {% endif %}
                      </td>
                      {% endif %}

                      {% if user_permissions.fraud_detection %}
                      <td class="text-center">
                        {% if review.fraud_check %}
                          {% if review.fraud_check.is_suspicious %}
                            <span class="label label-danger">
                              <i class="fa fa-exclamation-triangle"></i> {{ text_suspicious }}
                            </span>
                            <br><small>{{ review.fraud_check.fraud_score }}%</small>
                          {% else %}
                            <span class="label label-success">
                              <i class="fa fa-check"></i> {{ text_not_suspicious }}
                            </span>
                            <br><small>{{ review.fraud_check.fraud_score }}%</small>
                          {% endif %}
                        {% else %}
                          <button class="btn btn-xs btn-warning" onclick="checkFraud({{ review.review_id }})">
                            <i class="fa fa-shield"></i> {{ text_check_fraud }}
                          </button>
                        {% endif %}
                      </td>
                      {% endif %}

                      <!-- Status -->
                      <td class="text-center">
                        {% if review.status %}
                          <span class="label label-success">
                            <i class="fa fa-check"></i> {{ text_enabled }}
                          </span>
                        {% else %}
                          <span class="label label-warning">
                            <i class="fa fa-clock-o"></i> {{ text_pending_approval }}
                          </span>
                        {% endif %}
                      </td>

                      <!-- Date Added -->
                      <td class="text-center">
                        <div>
                          {{ review.date_added }}
                          {% if review.replies_count > 0 %}
                          <br><small class="text-info">
                            <i class="fa fa-reply"></i> {{ review.replies_count }} {{ text_review_replies }}
                          </small>
                          {% endif %}
                        </div>
                      </td>

                      <!-- Quick Actions -->
                      <td class="text-center">
                        <div class="btn-group">
                          <!-- View Details -->
                          <button type="button" class="btn btn-xs btn-info" onclick="viewReviewDetails({{ review.review_id }})" data-toggle="tooltip" title="{{ text_review_details }}">
                            <i class="fa fa-eye"></i>
                          </button>

                          <!-- Quick Approve/Reject -->
                          {% if not review.status %}
                          <button type="button" class="btn btn-xs btn-success" onclick="quickAction({{ review.review_id }}, 'approve')" data-toggle="tooltip" title="{{ text_approve }}">
                            <i class="fa fa-check"></i>
                          </button>
                          <button type="button" class="btn btn-xs btn-danger" onclick="quickAction({{ review.review_id }}, 'reject')" data-toggle="tooltip" title="{{ text_reject }}">
                            <i class="fa fa-times"></i>
                          </button>
                          {% endif %}

                          <!-- Reply to Review -->
                          {% if user_permissions.reply_management %}
                          <button type="button" class="btn btn-xs btn-primary" onclick="replyToReview({{ review.review_id }})" data-toggle="tooltip" title="{{ text_reply_to_review }}">
                            <i class="fa fa-reply"></i>
                          </button>
                          {% endif %}

                          <!-- Edit -->
                          <a href="{{ review.edit }}" data-toggle="tooltip" title="{{ button_edit }}" class="btn btn-xs btn-warning">
                            <i class="fa fa-pencil"></i>
                          </a>
                        </div>
                      </td>
                    </tr>
                    {% endfor %}
                  {% else %}
                    <tr>
                      <td class="text-center" colspan="{% if user_permissions.sentiment_analysis and user_permissions.fraud_detection %}9{% elseif user_permissions.sentiment_analysis or user_permissions.fraud_detection %}8{% else %}7{% endif %}">
                        <div class="empty-state">
                          <i class="fa fa-star-o fa-3x text-muted"></i>
                          <h4>{{ text_no_results }}</h4>
                          <p class="text-muted">{{ text_no_reviews_found }}</p>
                        </div>
                      </td>
                    </tr>
                  {% endif %}
                  </tbody>
                  
                </table>
              </div>
            </form>
            <div class="row">
              <div class="col-sm-6 text-left">{{ pagination }}</div>
              <div class="col-sm-6 text-right">{{ results }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
<!-- ═══════════════════════════════════════════════════════════════════════════════ -->
<!-- Enterprise Grade Plus JavaScript (الدستور الشامل) -->
<!-- ═══════════════════════════════════════════════════════════════════════════════ -->

<!-- Review Details Modal -->
<div class="modal fade" id="reviewDetailsModal" tabindex="-1" role="dialog">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title"><i class="fa fa-star"></i> {{ text_review_details }}</h4>
      </div>
      <div class="modal-body" id="reviewDetailsContent">
        <div class="text-center">
          <i class="fa fa-spinner fa-spin fa-2x"></i>
          <p>{{ text_loading }}</p>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_close }}</button>
      </div>
    </div>
  </div>
</div>

<!-- Reply Modal -->
<div class="modal fade" id="replyModal" tabindex="-1" role="dialog">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title"><i class="fa fa-reply"></i> {{ text_reply_to_review }}</h4>
      </div>
      <div class="modal-body">
        <form id="replyForm">
          <input type="hidden" id="replyReviewId" name="review_id" />
          <div class="form-group">
            <label for="replyText">{{ text_reply_text }}</label>
            <textarea class="form-control" id="replyText" name="reply_text" rows="4" placeholder="{{ text_reply_text }}"></textarea>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_cancel }}</button>
        <button type="button" class="btn btn-primary" onclick="submitReply()">{{ text_add_reply }}</button>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript">
// ═══════════════════════════════════════════════════════════════════════════════
// Enterprise Grade Plus JavaScript Functions
// ═══════════════════════════════════════════════════════════════════════════════

$(document).ready(function() {
    // Initialize tooltips
    $('[data-toggle="tooltip"]').tooltip();

    // Initialize date pickers
    $('.date').datetimepicker({
        language: '{{ datepicker }}',
        pickTime: false,
        format: 'YYYY-MM-DD'
    });

    // Auto-save filter preferences
    saveFilterPreferences();
});

// Advanced Filter System
$('#button-filter').on('click', function() {
    applyFilters();
});

$('#button-clear').on('click', function() {
    clearFilters();
});

function applyFilters() {
    var url = 'index.php?route=catalog/review&user_token={{ user_token }}';

    // Basic filters
    var filter_product = $('input[name="filter_product"]').val();
    if (filter_product) {
        url += '&filter_product=' + encodeURIComponent(filter_product);
    }

    var filter_author = $('input[name="filter_author"]').val();
    if (filter_author) {
        url += '&filter_author=' + encodeURIComponent(filter_author);
    }

    var filter_status = $('select[name="filter_status"]').val();
    if (filter_status !== '') {
        url += '&filter_status=' + encodeURIComponent(filter_status);
    }

    var filter_rating = $('select[name="filter_rating"]').val();
    if (filter_rating !== '') {
        url += '&filter_rating=' + encodeURIComponent(filter_rating);
    }

    // AI filters
    var filter_sentiment = $('select[name="filter_sentiment"]').val();
    if (filter_sentiment !== '') {
        url += '&filter_sentiment=' + encodeURIComponent(filter_sentiment);
    }

    var filter_fraud = $('select[name="filter_fraud"]').val();
    if (filter_fraud !== '') {
        url += '&filter_fraud=' + encodeURIComponent(filter_fraud);
    }

    // Date range
    var filter_date_start = $('input[name="filter_date_start"]').val();
    if (filter_date_start) {
        url += '&filter_date_start=' + encodeURIComponent(filter_date_start);
    }

    var filter_date_end = $('input[name="filter_date_end"]').val();
    if (filter_date_end) {
        url += '&filter_date_end=' + encodeURIComponent(filter_date_end);
    }

    location = url;
}

function clearFilters() {
    $('input[name="filter_product"]').val('');
    $('input[name="filter_author"]').val('');
    $('select[name="filter_status"]').val('');
    $('select[name="filter_rating"]').val('');
    $('select[name="filter_sentiment"]').val('');
    $('select[name="filter_fraud"]').val('');
    $('input[name="filter_date_start"]').val('');
    $('input[name="filter_date_end"]').val('');

    location = 'index.php?route=catalog/review&user_token={{ user_token }}';
}

// Bulk Operations
function bulkAction(action) {
    var selected = $('input[name="selected[]"]:checked');
    if (selected.length === 0) {
        alert('{{ error_no_selection }}');
        return;
    }

    var reviewIds = [];
    selected.each(function() {
        reviewIds.push($(this).val());
    });

    if (confirm('{{ text_confirm }}')) {
        $.ajax({
            url: 'index.php?route=catalog/review/bulkAction&user_token={{ user_token }}',
            type: 'POST',
            data: {
                action: action,
                selected: reviewIds
            },
            dataType: 'json',
            beforeSend: function() {
                $('#content').prepend('<div class="alert alert-info"><i class="fa fa-spinner fa-spin"></i> {{ text_processing }}</div>');
            },
            success: function(json) {
                $('.alert').remove();

                if (json.success) {
                    $('#content').prepend('<div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> ' + json.message + '<button type="button" class="close" data-dismiss="alert">&times;</button></div>');
                    location.reload();
                } else {
                    $('#content').prepend('<div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> ' + json.error + '<button type="button" class="close" data-dismiss="alert">&times;</button></div>');
                }
            },
            error: function(xhr, ajaxOptions, thrownError) {
                $('.alert').remove();
                $('#content').prepend('<div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> ' + thrownError + '<button type="button" class="close" data-dismiss="alert">&times;</button></div>');
            }
        });
    }
}

// Quick Actions
function quickAction(reviewId, action) {
    $.ajax({
        url: 'index.php?route=catalog/review/quickAction&user_token={{ user_token }}',
        type: 'POST',
        data: {
            review_id: reviewId,
            action: action
        },
        dataType: 'json',
        success: function(json) {
            if (json.success) {
                location.reload();
            } else {
                alert(json.error);
            }
        }
    });
}

// View Review Details
function viewReviewDetails(reviewId) {
    $('#reviewDetailsModal').modal('show');

    $.ajax({
        url: 'index.php?route=catalog/review/getReviewDetails&user_token={{ user_token }}',
        type: 'GET',
        data: {
            review_id: reviewId
        },
        dataType: 'json',
        success: function(json) {
            if (json.success) {
                var content = '<div class="row">';
                content += '<div class="col-md-6">';
                content += '<h5><i class="fa fa-info-circle"></i> {{ text_basic_data }}</h5>';
                content += '<table class="table table-bordered">';
                content += '<tr><td><strong>{{ text_product }}:</strong></td><td>' + json.review.product + '</td></tr>';
                content += '<tr><td><strong>{{ text_author }}:</strong></td><td>' + json.review.author + '</td></tr>';
                content += '<tr><td><strong>{{ text_rating }}:</strong></td><td>' + json.review.rating + '/5</td></tr>';
                content += '<tr><td><strong>{{ text_date_added }}:</strong></td><td>' + json.review.date_added + '</td></tr>';
                content += '</table>';
                content += '</div>';

                content += '<div class="col-md-6">';
                if (json.sentiment) {
                    content += '<h5><i class="fa fa-heart"></i> {{ text_sentiment_analysis }}</h5>';
                    content += '<table class="table table-bordered">';
                    content += '<tr><td><strong>{{ text_sentiment }}:</strong></td><td>' + json.sentiment.sentiment_label + '</td></tr>';
                    content += '<tr><td><strong>{{ text_confidence }}:</strong></td><td>' + json.sentiment.confidence + '%</td></tr>';
                    content += '</table>';
                }

                if (json.fraud_check) {
                    content += '<h5><i class="fa fa-shield"></i> {{ text_fraud_detection }}</h5>';
                    content += '<table class="table table-bordered">';
                    content += '<tr><td><strong>{{ text_fraud_score }}:</strong></td><td>' + json.fraud_check.fraud_score + '%</td></tr>';
                    content += '<tr><td><strong>{{ text_status }}:</strong></td><td>' + (json.fraud_check.is_suspicious ? '{{ text_suspicious }}' : '{{ text_not_suspicious }}') + '</td></tr>';
                    content += '</table>';
                }
                content += '</div>';
                content += '</div>';

                content += '<div class="row">';
                content += '<div class="col-md-12">';
                content += '<h5><i class="fa fa-comment"></i> {{ text_review_text }}</h5>';
                content += '<div class="well">' + json.review.text + '</div>';
                content += '</div>';
                content += '</div>';

                $('#reviewDetailsContent').html(content);
            } else {
                $('#reviewDetailsContent').html('<div class="alert alert-danger">' + json.error + '</div>');
            }
        }
    });
}

// AI Functions
function analyzeSentiment(reviewId) {
    $.ajax({
        url: 'index.php?route=catalog/review/analyzeSentiment&user_token={{ user_token }}',
        type: 'GET',
        data: {
            review_id: reviewId
        },
        dataType: 'json',
        success: function(json) {
            if (json.success) {
                location.reload();
            } else {
                alert(json.error);
            }
        }
    });
}

function checkFraud(reviewId) {
    $.ajax({
        url: 'index.php?route=catalog/review/checkFraud&user_token={{ user_token }}',
        type: 'GET',
        data: {
            review_id: reviewId
        },
        dataType: 'json',
        success: function(json) {
            if (json.success) {
                location.reload();
            } else {
                alert(json.error);
            }
        }
    });
}

// Reply Functions
function replyToReview(reviewId) {
    $('#replyReviewId').val(reviewId);
    $('#replyText').val('');
    $('#replyModal').modal('show');
}

function submitReply() {
    var formData = $('#replyForm').serialize();

    $.ajax({
        url: 'index.php?route=catalog/review/addReply&user_token={{ user_token }}',
        type: 'POST',
        data: formData,
        dataType: 'json',
        success: function(json) {
            if (json.success) {
                $('#replyModal').modal('hide');
                location.reload();
            } else {
                alert(json.error);
            }
        }
    });
}

// Export Functions
function exportReviews(format) {
    var url = 'index.php?route=catalog/review/export&user_token={{ user_token }}&format=' + format;

    // Add current filters to export
    var filters = getActiveFilters();
    for (var key in filters) {
        if (filters[key]) {
            url += '&' + key + '=' + encodeURIComponent(filters[key]);
        }
    }

    window.open(url, '_blank');
}

function getActiveFilters() {
    return {
        filter_product: $('input[name="filter_product"]').val(),
        filter_author: $('input[name="filter_author"]').val(),
        filter_status: $('select[name="filter_status"]').val(),
        filter_rating: $('select[name="filter_rating"]').val(),
        filter_sentiment: $('select[name="filter_sentiment"]').val(),
        filter_fraud: $('select[name="filter_fraud"]').val(),
        filter_date_start: $('input[name="filter_date_start"]').val(),
        filter_date_end: $('input[name="filter_date_end"]').val()
    };
}

// Save filter preferences
function saveFilterPreferences() {
    // Auto-save filters to localStorage for user convenience
    $('input, select').on('change', function() {
        var filters = getActiveFilters();
        localStorage.setItem('review_filters', JSON.stringify(filters));
    });

    // Load saved filters
    var savedFilters = localStorage.getItem('review_filters');
    if (savedFilters) {
        try {
            var filters = JSON.parse(savedFilters);
            for (var key in filters) {
                if (filters[key]) {
                    $('[name="' + key + '"]').val(filters[key]);
                }
            }
        } catch (e) {
            // Ignore parsing errors
        }
    }
}

// Auto-refresh for real-time updates (Enterprise Grade Plus)
{% if user_permissions.auto_refresh %}
setInterval(function() {
    // Check for new reviews every 30 seconds
    $.ajax({
        url: 'index.php?route=catalog/review/checkNewReviews&user_token={{ user_token }}',
        type: 'GET',
        dataType: 'json',
        success: function(json) {
            if (json.new_reviews > 0) {
                $('#content').prepend('<div class="alert alert-info alert-dismissible"><i class="fa fa-info-circle"></i> ' + json.new_reviews + ' {{ text_new_reviews_available }} <a href="#" onclick="location.reload()">{{ text_refresh_now }}</a><button type="button" class="close" data-dismiss="alert">&times;</button></div>');
            }
        }
    });
}, 30000);
{% endif %}

</script>

<!-- Custom CSS for Enterprise Grade Plus -->
<style>
.rating-stars {
    font-size: 14px;
}

.empty-state {
    padding: 40px 20px;
}

.review-row:hover {
    background-color: #f8f9fa;
}

.label {
    font-size: 11px;
    margin: 2px;
}

.btn-group .btn {
    margin: 1px;
}

.panel-title .badge {
    margin-left: 10px;
}

.thead-dark {
    background-color: #343a40;
    color: white;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0,0,0,.05);
}

@media (max-width: 768px) {
    .btn-group {
        display: block;
    }

    .btn-group .btn {
        display: block;
        width: 100%;
        margin: 2px 0;
    }
}
</style>

</div>
{{ footer }}