<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/rpc/error_details.proto

namespace Google\Rpc;

if (false) {
    /**
     * This class is deprecated. Use Google\Rpc\BadRequest\FieldViolation instead.
     * @deprecated
     */
    class BadRequest_FieldViolation {}
}
class_exists(BadRequest\FieldViolation::class);
@trigger_error('Google\Rpc\BadRequest_FieldViolation is deprecated and will be removed in the next major release. Use Google\Rpc\BadRequest\FieldViolation instead', E_USER_DEPRECATED);

