{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="submit" form="form-payment" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fa fa-save"></i></button>
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a></div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pencil"></i> {{ text_edit }}</h3>
      </div>
      <div class="panel-body">
        <form action="{{ action }}" method="post" enctype="multipart/form-data" id="form-payment" class="form-horizontal">
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-login">{{ entry_login }}</label>
            <div class="col-sm-10">
              <input type="text" name="payment_authorizenet_aim_login" value="{{ payment_authorizenet_aim_login }}" placeholder="{{ entry_login }}" id="input-login" class="form-control" />
              {% if error_login %}
              <div class="text-danger">{{ error_login }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-key">{{ entry_key }}</label>
            <div class="col-sm-10">
              <input type="text" name="payment_authorizenet_aim_key" value="{{ payment_authorizenet_aim_key }}" placeholder="{{ entry_key }}" id="input-key" class="form-control" />
              {% if error_key %}
              <div class="text-danger">{{ error_key }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-hash">{{ entry_hash }}</label>
            <div class="col-sm-10">
              <input type="text" name="payment_authorizenet_aim_hash" value="{{ payment_authorizenet_aim_hash }}" placeholder="{{ entry_hash }}" id="input-hash" class="form-control" />
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-server">{{ entry_server }}</label>
            <div class="col-sm-10">
              <select name="payment_authorizenet_aim_server" id="input-server" class="form-control">
                {% if payment_authorizenet_aim_server == 'live' %}
                <option value="live" selected="selected">{{ text_live }}</option>
                {% else %}
                <option value="live">{{ text_live }}</option>
                {% endif %}
                {% if payment_authorizenet_aim_server == 'test' %}
                <option value="test" selected="selected">{{ text_test }}</option>
                {% else %}
                <option value="test">{{ text_test }}</option>
                {% endif %}
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-mode">{{ entry_mode }}</label>
            <div class="col-sm-10">
              <select name="payment_authorizenet_aim_mode" id="input-mode" class="form-control">
                {% if payment_authorizenet_aim_mode == 'live' %}
                <option value="live" selected="selected">{{ text_live }}</option>
                {% else %}
                <option value="live">{{ text_live }}</option>
                {% endif %}
                {% if payment_authorizenet_aim_mode == 'test' %}
                <option value="test" selected="selected">{{ text_test }}</option>
                {% else %}
                <option value="test">{{ text_test }}</option>
                {% endif %}
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-method">{{ entry_method }}</label>
            <div class="col-sm-10">
              <select name="payment_authorizenet_aim_method" id="input-method" class="form-control">
                {% if payment_authorizenet_aim_method == 'authorization' %}
                <option value="authorization" selected="selected">{{ text_authorization }}</option>
                {% else %}
                <option value="authorization">{{ text_authorization }}</option>
                {% endif %}
                {% if payment_authorizenet_aim_method == 'capture' %}
                <option value="capture" selected="selected">{{ text_capture }}</option>
                {% else %}
                <option value="capture">{{ text_capture }}</option>
                {% endif %}
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-total"><span data-toggle="tooltip" title="{{ help_total }}">{{ entry_total }}</span></label>
            <div class="col-sm-10">
              <input type="text" name="payment_authorizenet_aim_total" value="{{ payment_authorizenet_aim_total }}" placeholder="{{ entry_total }}" id="input-total" class="form-control" />
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-order-status">{{ entry_order_status }}</label>
            <div class="col-sm-10">
              <select name="payment_authorizenet_aim_order_status_id" id="input-order-status" class="form-control">
                {% for order_status in order_statuses %}
                {% if order_status.order_status_id == payment_authorizenet_aim_order_status_id %}
                <option value="{{ order_status.order_status_id }}" selected="selected">{{ order_status.name }}</option>
                {% else %}
                <option value="{{ order_status.order_status_id }}">{{ order_status.name }}</option>
                {% endif %}
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-geo-zone">{{ entry_geo_zone }}</label>
            <div class="col-sm-10">
              <select name="payment_authorizenet_aim_geo_zone_id" id="input-geo-zone" class="form-control">
                <option value="0">{{ text_all_zones }}</option>
                {% for geo_zone in geo_zones %}
                {% if geo_zone.geo_zone_id == payment_authorizenet_aim_geo_zone_id %}
                <option value="{{ geo_zone.geo_zone_id }}" selected="selected">{{ geo_zone.name }}</option>
                {% else %}
                <option value="{{ geo_zone.geo_zone_id }}">{{ geo_zone.name }}</option>
                {% endif %}
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-status">{{ entry_status }}</label>
            <div class="col-sm-10">
              <select name="payment_authorizenet_aim_status" id="input-status" class="form-control">
                {% if payment_authorizenet_aim_status %}
                <option value="1" selected="selected">{{ text_enabled }}</option>
                <option value="0">{{ text_disabled }}</option>
                {% else %}
                <option value="1">{{ text_enabled }}</option>
                <option value="0" selected="selected">{{ text_disabled }}</option>
                {% endif %}
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-sort-order">{{ entry_sort_order }}</label>
            <div class="col-sm-10">
              <input type="text" name="payment_authorizenet_aim_sort_order" value="{{ payment_authorizenet_aim_sort_order }}" placeholder="{{ entry_sort_order }}" id="input-sort-order" class="form-control" />
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
{{ footer }} 