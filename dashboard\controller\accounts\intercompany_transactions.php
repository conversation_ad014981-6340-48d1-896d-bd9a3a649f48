<?php
/**
 * تحكم المعاملات بين الشركات المتقدم
 * يدير المعاملات بين الشركات التابعة والزميلة مع إمكانية الإلغاء التلقائي
 * متوافق مع معايير التوحيد المحاسبية IFRS 10
 */
class ControllerAccountsIntercompanyTransactions extends Controller {
    private $error = array();
    private $central_service;

    public function __construct($registry) {
        parent::__construct($registry);

        // تحميل الخدمات المركزية
        $this->load->model('core/central_service_manager');
        $this->central_service = $this->model_core_central_service_manager;
    }

    public function index() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/intercompany_transactions') ||
            !$this->user->hasKey('accounting_intercompany_transactions_view')) {

            $this->central_service->logActivity('unauthorized_access', 'accounts',
                $this->language->get('log_unauthorized_access_intercompany'), [
                'user_id' => $this->user->getId(),
                'ip_address' => $this->request->server['REMOTE_ADDR']
            ]);

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/intercompany_transactions');
        $this->document->setTitle($this->language->get('heading_title'));
        $this->load->model('accounts/intercompany_transactions');

        // إضافة CSS و JavaScript المتقدم
        $this->document->addStyle('view/stylesheet/accounts/intercompany_transactions.css');
        $this->document->addScript('view/javascript/accounts/intercompany_transactions.js');
        $this->document->addScript('view/javascript/jquery/accounting.min.js');
        $this->document->addScript('view/javascript/jquery/select2.min.js');
        $this->document->addStyle('view/javascript/jquery/select2.min.css');
        $this->document->addScript('view/javascript/jquery/daterangepicker.min.js');
        $this->document->addStyle('view/javascript/jquery/daterangepicker.css');
        $this->document->addScript('view/javascript/jquery/datatables.min.js');
        $this->document->addStyle('view/javascript/jquery/datatables.min.css');

        // تسجيل الوصول للشاشة
        $this->central_service->logActivity('view', 'accounts',
            $this->language->get('log_view_intercompany_screen'), [
            'user_id' => $this->user->getId(),
            'screen' => 'intercompany_transactions'
        ]);

        // معالجة الطلبات
        if ($this->request->server['REQUEST_METHOD'] == 'POST') {
            if (isset($this->request->post['action'])) {
                switch ($this->request->post['action']) {
                    case 'create_transaction':
                        $this->createTransaction();
                        break;
                    case 'eliminate_transactions':
                        $this->eliminateTransactions();
                        break;
                    case 'match_transactions':
                        $this->matchTransactions();
                        break;
                    case 'bulk_eliminate':
                        $this->bulkEliminate();
                        break;
                }
            }
        }

        $data = $this->getCommonData();

        // جلب الشركات المتاحة
        $data['companies'] = $this->model_accounts_intercompany_transactions->getCompanies();
        
        // جلب آخر المعاملات
        $data['recent_transactions'] = $this->model_accounts_intercompany_transactions->getRecentTransactions(20);

        // جلب المعاملات غير المطابقة
        $data['unmatched_transactions'] = $this->model_accounts_intercompany_transactions->getUnmatchedTransactions();

        // جلب إحصائيات سريعة
        $data['statistics'] = $this->model_accounts_intercompany_transactions->getStatistics();

        // روابط Ajax
        $data['ajax_create_url'] = $this->url->link('accounts/intercompany_transactions/create', 'user_token=' . $this->session->data['user_token'], true);
        $data['ajax_eliminate_url'] = $this->url->link('accounts/intercompany_transactions/eliminate', 'user_token=' . $this->session->data['user_token'], true);
        $data['ajax_match_url'] = $this->url->link('accounts/intercompany_transactions/match', 'user_token=' . $this->session->data['user_token'], true);
        $data['ajax_search_url'] = $this->url->link('accounts/intercompany_transactions/search', 'user_token=' . $this->session->data['user_token'], true);

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/intercompany_transactions', $data));
    }

    /**
     * إنشاء معاملة بين الشركات
     */
    public function create() {
        // فحص الصلاحيات المتقدمة
        if (!$this->user->hasKey('accounting_intercompany_transactions_create')) {
            $json['error'] = $this->language->get('error_permission_create');
            $this->response->addHeader('Content-Type: application/json');
            $this->response->setOutput(json_encode($json));
            return;
        }

        $this->load->model('accounts/intercompany_transactions');
        $json = array();

        try {
            $transaction_data = array(
                'company_id' => $this->request->post['company_id'],
                'related_company_id' => $this->request->post['related_company_id'],
                'transaction_type' => $this->request->post['transaction_type'],
                'transaction_date' => $this->request->post['transaction_date'],
                'amount' => $this->request->post['amount'],
                'currency' => $this->request->post['currency'],
                'description' => $this->request->post['description'],
                'reference' => $this->request->post['reference'],
                'account_id' => $this->request->post['account_id'],
                'auto_create_reverse' => isset($this->request->post['auto_create_reverse'])
            );

            $transaction_id = $this->model_accounts_intercompany_transactions->createTransaction($transaction_data);

            if ($transaction_id) {
                $json['success'] = $this->language->get('text_transaction_created');
                $json['transaction_id'] = $transaction_id;
                
                // تسجيل العملية
                $this->central_service->logActivity('create', 'accounts',
                    $this->language->get('log_transaction_created'), [
                    'user_id' => $this->user->getId(),
                    'transaction_id' => $transaction_id,
                    'amount' => $transaction_data['amount'],
                    'type' => $transaction_data['transaction_type']
                ]);

                // إرسال إشعار
                $this->central_service->sendNotification([
                    'type' => 'intercompany_transaction_created',
                    'title' => $this->language->get('notification_transaction_title'),
                    'message' => sprintf($this->language->get('notification_transaction_message'), $transaction_id),
                    'user_id' => $this->user->getId(),
                    'url' => $this->url->link('accounts/intercompany_transactions/view', 'transaction_id=' . $transaction_id)
                ]);

            } else {
                $json['error'] = $this->language->get('error_transaction_failed');
            }

        } catch (Exception $e) {
            $json['error'] = $e->getMessage();
            
            // تسجيل الخطأ
            $this->central_service->logActivity('error', 'accounts',
                'Intercompany transaction creation failed: ' . $e->getMessage(), [
                'user_id' => $this->user->getId(),
                'error_details' => $e->getTraceAsString()
            ]);
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * إلغاء المعاملات بين الشركات
     */
    public function eliminate() {
        if (!$this->user->hasKey('accounting_intercompany_transactions_eliminate')) {
            $json['error'] = $this->language->get('error_permission_eliminate');
            $this->response->addHeader('Content-Type: application/json');
            $this->response->setOutput(json_encode($json));
            return;
        }

        $this->load->model('accounts/intercompany_transactions');
        $json = array();

        try {
            $elimination_data = array(
                'transaction_ids' => $this->request->post['transaction_ids'],
                'elimination_date' => $this->request->post['elimination_date'],
                'elimination_method' => $this->request->post['elimination_method'],
                'notes' => $this->request->post['notes']
            );

            $elimination_id = $this->model_accounts_intercompany_transactions->eliminateTransactions($elimination_data);

            if ($elimination_id) {
                $json['success'] = $this->language->get('text_transactions_eliminated');
                $json['elimination_id'] = $elimination_id;
                
                // تسجيل العملية
                $this->central_service->logActivity('eliminate', 'accounts',
                    $this->language->get('log_transactions_eliminated'), [
                    'user_id' => $this->user->getId(),
                    'elimination_id' => $elimination_id,
                    'transaction_count' => count($elimination_data['transaction_ids'])
                ]);

            } else {
                $json['error'] = $this->language->get('error_elimination_failed');
            }

        } catch (Exception $e) {
            $json['error'] = $e->getMessage();
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * مطابقة المعاملات
     */
    public function match() {
        if (!$this->user->hasKey('accounting_intercompany_transactions_create')) {
            $json['error'] = $this->language->get('error_permission_match');
            $this->response->addHeader('Content-Type: application/json');
            $this->response->setOutput(json_encode($json));
            return;
        }

        $this->load->model('accounts/intercompany_transactions');
        $json = array();

        try {
            $match_data = array(
                'transaction_id_1' => $this->request->post['transaction_id_1'],
                'transaction_id_2' => $this->request->post['transaction_id_2'],
                'match_type' => $this->request->post['match_type'],
                'tolerance_amount' => $this->request->post['tolerance_amount'],
                'notes' => $this->request->post['notes']
            );

            $match_id = $this->model_accounts_intercompany_transactions->matchTransactions($match_data);

            if ($match_id) {
                $json['success'] = $this->language->get('text_transactions_matched');
                $json['match_id'] = $match_id;
                
                // تسجيل العملية
                $this->central_service->logActivity('match', 'accounts',
                    $this->language->get('log_transactions_matched'), [
                    'user_id' => $this->user->getId(),
                    'match_id' => $match_id
                ]);

            } else {
                $json['error'] = $this->language->get('error_matching_failed');
            }

        } catch (Exception $e) {
            $json['error'] = $e->getMessage();
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * البحث في المعاملات
     */
    public function search() {
        $this->load->model('accounts/intercompany_transactions');
        $json = array();

        try {
            $search_criteria = array(
                'company_id' => $this->request->get['company_id'],
                'related_company_id' => $this->request->get['related_company_id'],
                'transaction_type' => $this->request->get['transaction_type'],
                'date_from' => $this->request->get['date_from'],
                'date_to' => $this->request->get['date_to'],
                'amount_from' => $this->request->get['amount_from'],
                'amount_to' => $this->request->get['amount_to'],
                'status' => $this->request->get['status'],
                'reference' => $this->request->get['reference']
            );

            $transactions = $this->model_accounts_intercompany_transactions->searchTransactions($search_criteria);

            $json['success'] = true;
            $json['transactions'] = $transactions;

        } catch (Exception $e) {
            $json['error'] = $e->getMessage();
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * عرض تفاصيل المعاملة
     */
    public function view() {
        if (!$this->user->hasPermission('access', 'accounts/intercompany_transactions') ||
            !$this->user->hasKey('accounting_intercompany_transactions_view')) {
            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $transaction_id = isset($this->request->get['transaction_id']) ? (int)$this->request->get['transaction_id'] : 0;

        if (!$transaction_id) {
            $this->response->redirect($this->url->link('accounts/intercompany_transactions'));
            return;
        }

        $this->load->language('accounts/intercompany_transactions');
        $this->load->model('accounts/intercompany_transactions');

        $data = $this->getCommonData();
        
        // جلب بيانات المعاملة
        $transaction = $this->model_accounts_intercompany_transactions->getTransaction($transaction_id);
        
        if (!$transaction) {
            $this->response->redirect($this->url->link('accounts/intercompany_transactions'));
            return;
        }

        $data['transaction'] = $transaction;
        $data['related_transactions'] = $this->model_accounts_intercompany_transactions->getRelatedTransactions($transaction_id);
        $data['elimination_history'] = $this->model_accounts_intercompany_transactions->getEliminationHistory($transaction_id);
        $data['journal_entries'] = $this->model_accounts_intercompany_transactions->getTransactionJournalEntries($transaction_id);

        $this->document->setTitle($this->language->get('heading_title_view') . ' - ' . $transaction['reference']);

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/intercompany_transactions_view', $data));
    }

    /**
     * تقرير المعاملات بين الشركات
     */
    public function report() {
        if (!$this->user->hasPermission('access', 'accounts/intercompany_transactions') ||
            !$this->user->hasKey('accounting_intercompany_transactions_view')) {
            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/intercompany_transactions');
        $this->load->model('accounts/intercompany_transactions');

        $data = $this->getCommonData();
        
        // جلب بيانات التقرير
        $filter = array(
            'date_from' => $this->request->get['date_from'] ?? date('Y-01-01'),
            'date_to' => $this->request->get['date_to'] ?? date('Y-12-31'),
            'company_id' => $this->request->get['company_id'] ?? '',
            'transaction_type' => $this->request->get['transaction_type'] ?? '',
            'status' => $this->request->get['status'] ?? ''
        );

        $data['report_data'] = $this->model_accounts_intercompany_transactions->generateReport($filter);
        $data['filter'] = $filter;
        $data['companies'] = $this->model_accounts_intercompany_transactions->getCompanies();

        $this->document->setTitle($this->language->get('heading_title_report'));

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/intercompany_transactions_report', $data));
    }

    /**
     * جلب البيانات المشتركة
     */
    private function getCommonData() {
        $data = array();

        // النصوص
        $data['heading_title'] = $this->language->get('heading_title');
        $data['text_list'] = $this->language->get('text_list');
        $data['text_no_results'] = $this->language->get('text_no_results');
        $data['text_confirm'] = $this->language->get('text_confirm');

        // الأزرار
        $data['button_create'] = $this->language->get('button_create');
        $data['button_eliminate'] = $this->language->get('button_eliminate');
        $data['button_match'] = $this->language->get('button_match');
        $data['button_search'] = $this->language->get('button_search');
        $data['button_view'] = $this->language->get('button_view');
        $data['button_cancel'] = $this->language->get('button_cancel');

        // الروابط
        $data['cancel'] = $this->url->link('accounts/intercompany_transactions', 'user_token=' . $this->session->data['user_token'], true);
        $data['user_token'] = $this->session->data['user_token'];

        return $data;
    }
}
