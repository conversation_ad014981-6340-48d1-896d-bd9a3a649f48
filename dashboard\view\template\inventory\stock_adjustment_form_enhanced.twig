{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="submit" form="form-adjustment" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fa fa-save"></i></button>
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pencil"></i> {{ text_form }}</h3>
      </div>
      <div class="panel-body">
        <form action="{{ action }}" method=\"post\" enctype=\"multipart/form-data\" id=\"form-adjustment\" class=\"form-horizontal\">
          
          <!-- معلومات التسوية الأساسية -->
          <div class="row">
            <div class="col-md-6">
              <div class="form-group required">
                <label class="col-sm-3 control-label" for="input-reference">{{ entry_reference }}</label>
                <div class="col-sm-9">
                  <input type="text" name="reference" value="{{ reference }}" placeholder="{{ entry_reference }}\" id=\"input-reference\" class=\"form-control\" readonly />
                  {% if error_reference %}
                  <div class="text-danger">{{ error_reference }}</div>
                  {% endif %}
                </div>
              </div>
              
              <div class="form-group required">
                <label class="col-sm-3 control-label" for="input-branch">{{ entry_branch }}</label>
                <div class="col-sm-9">
                  <select name="branch_id" id="input-branch" class="form-control">
                    <option value="">{{ text_select }}</option>
                    {% for branch in branches %}
                    {% if branch.branch_id == branch_id %}
                    <option value="{{ branch.branch_id }}" selected="selected">{{ branch.name }}</option>
                    {% else %}
                    <option value="{{ branch.branch_id }}">{{ branch.name }}</option>
                    {% endif %}
                    {% endfor %}
                  </select>
                  {% if error_branch %}
                  <div class="text-danger">{{ error_branch }}</div>
                  {% endif %}
                </div>
              </div>
              
              <div class="form-group required">
                <label class="col-sm-3 control-label" for="input-reason">{{ entry_reason }}</label>
                <div class="col-sm-9">
                  <textarea name="reason" rows="3" placeholder="{{ entry_reason }}" id="input-reason" class="form-control">{{ reason }}</textarea>
                  {% if error_reason %}
                  <div class="text-danger">{{ error_reason }}</div>
                  {% endif %}
                </div>
              </div>
            </div>
            
            <div class="col-md-6">
              <div class="form-group">
                <label class="col-sm-3 control-label" for="input-notes">{{ entry_notes }}</label>
                <div class="col-sm-9">
                  <textarea name="notes" rows="3" placeholder="{{ entry_notes }}" id="input-notes" class="form-control">{{ notes }}</textarea>
                </div>
              </div>
              
              {% if adjustment_id %}
              <div class="form-group">
                <label class="col-sm-3 control-label">{{ entry_status }}</label>
                <div class="col-sm-9">
                  <span class="label {{ status_class }}">{{ status_text }}</span>
                  {% if status == 'pending' and can_approve %}
                  <div class="btn-group pull-right">
                    <button type="button" class="btn btn-success btn-sm" onclick="approveAdjustment()">
                      <i class="fa fa-check"></i> {{ button_approve }}
                    </button>
                    <button type="button" class="btn btn-danger btn-sm" onclick="rejectAdjustment()">
                      <i class="fa fa-times"></i> {{ button_reject }}
                    </button>
                  </div>
                  {% endif %}
                </div>
              </div>
              
              {% if approved_by %}
              <div class="form-group">
                <label class="col-sm-3 control-label">{{ text_approved_by }}</label>
                <div class="col-sm-9">
                  <p class="form-control-static">{{ approved_by_name }} - {{ approved_at }}</p>
                </div>
              </div>
              {% endif %}
              
              {% if rejected_by %}
              <div class="form-group">
                <label class="col-sm-3 control-label">{{ text_rejected_by }}</label>
                <div class="col-sm-9">
                  <p class="form-control-static">{{ rejected_by_name }} - {{ rejected_at }}</p>
                  {% if reject_reason %}
                  <div class="alert alert-danger">{{ reject_reason }}</div>
                  {% endif %}
                </div>
              </div>
              {% endif %}
              {% endif %}
            </div>
          </div>
          
          <hr>
          
          <!-- منتجات التسوية -->
          <div class="row">
            <div class="col-md-12">
              <h4>{{ text_products }}</h4>
              
              <div class="table-responsive">
                <table class="table table-bordered table-hover" id="adjustment-products">
                  <thead>
                    <tr>
                      <th>{{ column_product }}</th>
                      <th>{{ column_current_quantity }}</th>
                      <th>{{ column_new_quantity }}</th>
                      <th>{{ column_difference }}</th>
                      <th>{{ column_unit_cost }}</th>
                      <th>{{ column_total_cost }}</th>
                      <th>{{ column_reason }}</th>
                      <th>{{ column_action }}</th>
                    </tr>
                  </thead>
                  <tbody>
                    {% set product_row = 0 %}
                    {% for product in products %}
                    <tr id="product-row{{ product_row }}">
                      <td>
                        <input type="text" name="product[{{ product_row }}][name]" value="{{ product.name }}" placeholder="{{ entry_product }}" class="form-control" readonly />
                        <input type="hidden" name="product[{{ product_row }}][product_id]" value="{{ product.product_id }}" />
                      </td>
                      <td>
                        <input type="text" name="product[{{ product_row }}][current_quantity]" value="{{ product.current_quantity }}" class="form-control text-right" readonly />
                      </td>
                      <td>
                        <input type="number" name="product[{{ product_row }}][new_quantity]" value="{{ product.new_quantity }}" step="0.01" class="form-control text-right" onchange="calculateDifference({{ product_row }})" />
                      </td>
                      <td>
                        <input type="text" name="product[{{ product_row }}][difference]" value="{{ product.difference }}" class="form-control text-right" readonly />
                      </td>
                      <td>
                        <input type="text" name="product[{{ product_row }}][unit_cost]" value="{{ product.unit_cost }}" class="form-control text-right" readonly />
                      </td>
                      <td>
                        <input type="text" name="product[{{ product_row }}][total_cost]" value="{{ product.total_cost }}" class="form-control text-right" readonly />
                      </td>
                      <td>
                        <input type="text" name="product[{{ product_row }}][reason]" value="{{ product.reason }}" placeholder="{{ entry_product_reason }}" class="form-control" />
                      </td>
                      <td>
                        <button type="button" onclick="removeProduct({{ product_row }})" data-toggle="tooltip" title="{{ button_remove }}" class="btn btn-danger btn-sm">
                          <i class="fa fa-minus-circle"></i>
                        </button>
                      </td>
                    </tr>
                    {% set product_row = product_row + 1 %}
                    {% endfor %}
                  </tbody>
                  <tfoot>
                    <tr>
                      <td colspan="8">
                        <button type="button" onclick="addProduct()" data-toggle="tooltip" title="{{ button_product_add }}" class="btn btn-primary">
                          <i class="fa fa-plus-circle"></i> {{ button_product_add }}
                        </button>
                      </td>
                    </tr>
                  </tfoot>
                </table>
              </div>
              
              {% if error_product %}
              <div class="text-danger">{{ error_product }}</div>
              {% endif %}
            </div>
          </div>
          
          <!-- ملخص التسوية -->
          <div class="row">
            <div class="col-md-6 col-md-offset-6">
              <div class="panel panel-info">
                <div class="panel-heading">
                  <h4 class="panel-title">{{ text_summary }}</h4>
                </div>
                <div class="panel-body">
                  <div class="row">
                    <div class="col-sm-6">
                      <strong>{{ text_total_products }}:</strong>
                    </div>
                    <div class="col-sm-6 text-right">
                      <span id="total-products">0</span>
                    </div>
                  </div>
                  <div class="row">
                    <div class="col-sm-6">
                      <strong>{{ text_total_quantity }}:</strong>
                    </div>
                    <div class="col-sm-6 text-right">
                      <span id="total-quantity">0</span>
                    </div>
                  </div>
                  <div class="row">
                    <div class="col-sm-6">
                      <strong>{{ text_total_value }}:</strong>
                    </div>
                    <div class="col-sm-6 text-right">
                      <span id="total-value">{{ currency_symbol }}0.00</span>
                    </div>
                  </div>
                  {% if high_value_warning %}
                  <div class="alert alert-warning">
                    <i class="fa fa-warning"></i> {{ alert_high_value }}
                  </div>
                  {% endif %}
                </div>
              </div>
            </div>
          </div>
          
        </form>
      </div>
    </div>
  </div>
</div>

<!-- Modal for Product Selection -->
<div class="modal fade" id="modal-product" tabindex="-1" role="dialog" aria-labelledby="modal-product-label">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
        <h4 class="modal-title" id="modal-product-label">{{ text_select_product }}</h4>
      </div>
      <div class="modal-body">
        <div class="form-group">
          <input type="text" name="product_search" value="" placeholder="{{ text_search_product }}" id="input-product-search" class="form-control" />
        </div>
        <div class="table-responsive">
          <table class="table table-bordered table-hover" id="product-list">
            <thead>
              <tr>
                <th>{{ column_product }}</th>
                <th>{{ column_model }}</th>
                <th>{{ column_current_stock }}</th>
                <th>{{ column_unit_cost }}</th>
                <th>{{ column_action }}</th>
              </tr>
            </thead>
            <tbody>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Modal for Approval -->
<div class="modal fade" id="modal-approve" tabindex="-1" role="dialog" aria-labelledby="modal-approve-label">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
        <h4 class="modal-title" id="modal-approve-label">{{ text_approve_adjustment }}</h4>
      </div>
      <div class="modal-body">
        <form id="form-approve">
          <div class="form-group">
            <label for="approval-notes">{{ entry_approval_note }}</label>
            <textarea name="approval_notes" id="approval-notes" class="form-control" rows="3"></textarea>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_cancel }}</button>
        <button type="button" class="btn btn-success" id="button-approve-confirm">{{ button_approve }}</button>
      </div>
    </div>
  </div>
</div>

<!-- Modal for Rejection -->
<div class="modal fade" id="modal-reject" tabindex="-1" role="dialog" aria-labelledby="modal-reject-label">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
        <h4 class="modal-title" id="modal-reject-label">{{ text_reject_adjustment }}</h4>
      </div>
      <div class="modal-body">
        <form id="form-reject">
          <div class="form-group">
            <label for="reject-reason">{{ entry_reject_reason }}</label>
            <textarea name="reject_reason" id="reject-reason" class="form-control" rows="3" required></textarea>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_cancel }}</button>
        <button type="button" class="btn btn-danger" id="button-reject-confirm">{{ button_reject }}</button>
      </div>
    </div>
  </div>
</div>

<style>
.panel-info {
  border-color: #bce8f1;
}

.panel-info > .panel-heading {
  color: #31708f;
  background-color: #d9edf7;
  border-color: #bce8f1;
}

.label-pending {
  background-color: #f0ad4e;
}

.label-approved {
  background-color: #5cb85c;
}

.label-rejected {
  background-color: #d9534f;
}

#adjustment-products tbody tr:hover {
  background-color: #f5f5f5;
}

.form-control[readonly] {
  background-color: #f9f9f9;
}

.alert-warning {
  border-left: 4px solid #f0ad4e;
}
</style>

<script type="text/javascript"><!--
var product_row = {{ product_row }};

function addProduct() {
    $('#modal-product').modal('show');
    loadProducts();
}

function loadProducts() {
    $.ajax({
        url: 'index.php?route=inventory/stock_adjustment/getProducts&user_token={{ user_token }}',
        type: 'get',
        data: {
            branch_id: $('#input-branch').val()
        },
        dataType: 'json',
        success: function(json) {
            $('#product-list tbody').html('');
            
            if (json.products) {
                for (i = 0; i < json.products.length; i++) {
                    var product = json.products[i];
                    
                    html = '<tr>';
                    html += '<td>' + product.name + '</td>';
                    html += '<td>' + product.model + '</td>';
                    html += '<td class="text-right">' + product.quantity + '</td>';
                    html += '<td class="text-right">' + product.unit_cost + '</td>';
                    html += '<td><button type="button" class="btn btn-primary btn-sm" onclick="selectProduct(' + product.product_id + ', \'' + product.name + '\', ' + product.quantity + ', ' + product.unit_cost + ')">{{ button_select }}</button></td>';
                    html += '</tr>';
                    
                    $('#product-list tbody').append(html);
                }
            }
        }
    });
}

function selectProduct(product_id, name, current_quantity, unit_cost) {
    html = '<tr id="product-row' + product_row + '">';
    html += '<td><input type="text" name="product[' + product_row + '][name]" value="' + name + '" class="form-control" readonly /><input type="hidden" name="product[' + product_row + '][product_id]" value="' + product_id + '" /></td>';
    html += '<td><input type="text" name="product[' + product_row + '][current_quantity]" value="' + current_quantity + '" class="form-control text-right" readonly /></td>';
    html += '<td><input type="number" name="product[' + product_row + '][new_quantity]" value="' + current_quantity + '" step="0.01" class="form-control text-right" onchange="calculateDifference(' + product_row + ')" /></td>';
    html += '<td><input type="text" name="product[' + product_row + '][difference]" value="0" class="form-control text-right" readonly /></td>';
    html += '<td><input type="text" name="product[' + product_row + '][unit_cost]" value="' + unit_cost + '" class="form-control text-right" readonly /></td>';
    html += '<td><input type="text" name="product[' + product_row + '][total_cost]" value="0" class="form-control text-right" readonly /></td>';
    html += '<td><input type="text" name="product[' + product_row + '][reason]" value="" placeholder="{{ entry_product_reason }}" class="form-control" /></td>';
    html += '<td><button type="button" onclick="removeProduct(' + product_row + ')" class="btn btn-danger btn-sm"><i class="fa fa-minus-circle"></i></button></td>';
    html += '</tr>';
    
    $('#adjustment-products tbody').append(html);
    
    product_row++;
    
    $('#modal-product').modal('hide');
    
    updateSummary();
}

function removeProduct(row) {
    $('#product-row' + row).remove();
    updateSummary();
}

function calculateDifference(row) {
    var current_quantity = parseFloat($('input[name="product[' + row + '][current_quantity]"]').val()) || 0;
    var new_quantity = parseFloat($('input[name="product[' + row + '][new_quantity]"]').val()) || 0;
    var unit_cost = parseFloat($('input[name="product[' + row + '][unit_cost]"]').val()) || 0;
    
    var difference = new_quantity - current_quantity;
    var total_cost = difference * unit_cost;
    
    $('input[name="product[' + row + '][difference]"]').val(difference.toFixed(2));
    $('input[name="product[' + row + '][total_cost]"]').val(total_cost.toFixed(2));
    
    updateSummary();
}

function updateSummary() {
    var total_products = $('#adjustment-products tbody tr').length;
    var total_quantity = 0;
    var total_value = 0;
    
    $('#adjustment-products tbody tr').each(function() {
        var difference = parseFloat($(this).find('input[name*="[difference]"]').val()) || 0;
        var total_cost = parseFloat($(this).find('input[name*="[total_cost]"]').val()) || 0;
        
        total_quantity += Math.abs(difference);
        total_value += Math.abs(total_cost);
    });
    
    $('#total-products').text(total_products);
    $('#total-quantity').text(total_quantity.toFixed(2));
    $('#total-value').text('{{ currency_symbol }}' + total_value.toFixed(2));
}

function approveAdjustment() {
    $('#modal-approve').modal('show');
}

function rejectAdjustment() {
    $('#modal-reject').modal('show');
}

$('#button-approve-confirm').on('click', function() {
    var approval_notes = $('#approval-notes').val();
    
    $.ajax({
        url: 'index.php?route=inventory/stock_adjustment/approve&user_token={{ user_token }}',
        type: 'post',
        data: {
            adjustment_id: {{ adjustment_id }},
            approval_notes: approval_notes
        },
        dataType: 'json',
        success: function(json) {
            if (json.success) {
                location.reload();
            }
            
            if (json.error) {
                alert(json.error);
            }
        }
    });
});

$('#button-reject-confirm').on('click', function() {
    var reject_reason = $('#reject-reason').val();
    
    if (reject_reason == '') {
        alert('{{ error_reject_reason }}');
        return;
    }
    
    $.ajax({
        url: 'index.php?route=inventory/stock_adjustment/reject&user_token={{ user_token }}',
        type: 'post',
        data: {
            adjustment_id: {{ adjustment_id }},
            reject_reason: reject_reason
        },
        dataType: 'json',
        success: function(json) {
            if (json.success) {
                location.reload();
            }
            
            if (json.error) {
                alert(json.error);
            }
        }
    });
});

// Product search
$('#input-product-search').on('keyup', function() {
    var search = $(this).val();
    
    $('#product-list tbody tr').each(function() {
        var product_name = $(this).find('td:first').text().toLowerCase();
        
        if (product_name.indexOf(search.toLowerCase()) !== -1) {
            $(this).show();
        } else {
            $(this).hide();
        }
    });
});

// Initialize summary on page load
$(document).ready(function() {
    updateSummary();
});
//--></script>

{{ footer }}