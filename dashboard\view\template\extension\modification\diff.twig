{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="button" id="button-save" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary" data-loading-text="{{ text_loading }}"><i class="fa fa-save"></i></button>
        <a href="{{ return }}" data-toggle="tooltip" title="{{ button_return }}" class="btn btn-default"><i class="fa fa-reply"></i></a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-file-o"></i> {{ file_patch }}</h3>
      </div>
      <div class="panel-body">
        <div class="alert alert-info"><i class="fa fa-info-circle"></i> {{ text_help_diff }}
          <button type="button" class="close" data-dismiss="alert">&times;</button>
        </div>
        <div id="flex-container" style="width:100% !important; height:500px; position:relative; font-size:1.1em;">
          <div>
            <div id="acediff-left-editor">{{ code_original }}</div>
          </div>
          <div id="acediff-gutter"></div>
          <div>
            <div id="acediff-right-editor">{{ code_cache }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/ace/1.2.9/ace.js" type="text/javascript" charset="utf-8"></script>
  <script src="view/javascript/ace/diff.min.js"></script>
  <script type="text/javascript">
    var aceDiffer = new AceDiff({
      mode: "ace/mode/php",
      theme: "ace/theme/chrome",
      left: { editable: false },
      right: { editable: true }
    });

    $('#button-save').on('click', function(){
      $('.alert').remove();

      $.ajax({
        url: 'index.php?route=extension/modification/diff/save&user_token=' + getURLVar('user_token') + '&file_patch=' + getURLVar('file_patch'),
        type: 'post',
        dataType: 'json',
        data: { file_patch: getURLVar('file_patch'), code_cache: aceDiffer.getEditors().right.getValue() },
        cache: false,
        beforeSend: function() {
          $('#button-save').button('loading');
        },
        complete: function() {
          $('#button-save').button('reset');
        },
        success: function(json) {
          if (json['error']) {
            $('.panel-default').before('<div class="alert alert-danger" role="alert">' + json['error'] + '</div>');
          } else {
            $('.panel-default').before('<div class="alert alert-warning" role="alert">' + json['success'] + '</div>');
          }
        },
        error: function(xhr, ajaxOptions, thrownError) {
          alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
      });
    });

    $(document).ready(function() {
      $('#flex-container').height(Math.max($(window).height() - 120, 800)); // minus 400 is to stay in the window
    });
  </script>
</div>
{{ footer }}