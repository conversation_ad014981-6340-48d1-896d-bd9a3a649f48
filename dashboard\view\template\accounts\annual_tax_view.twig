{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <a href="{{ export_excel }}" data-toggle="tooltip" title="{{ button_export_excel }}" class="btn btn-success"><i class="fa fa-file-excel-o"></i></a>
        <a href="{{ export_pdf }}" data-toggle="tooltip" title="{{ button_export_pdf }}" class="btn btn-danger"><i class="fa fa-file-pdf-o"></i></a>
        <a href="{{ export_csv }}" data-toggle="tooltip" title="{{ button_export_csv }}" class="btn btn-info"><i class="fa fa-file-text-o"></i></a>
        <a href="{{ export_eta }}" data-toggle="tooltip" title="{{ button_export_eta }}" class="btn btn-warning"><i class="fa fa-file-code-o"></i></a>
        <a href="{{ print }}" data-toggle="tooltip" title="{{ button_print }}" class="btn btn-primary" target="_blank"><i class="fa fa-print"></i></a>
        <a href="{{ back }}" data-toggle="tooltip" title="{{ button_back }}" class="btn btn-default"><i class="fa fa-reply"></i></a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if success %}
    <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    
    <!-- معلومات التقرير -->
    <div class="panel panel-primary">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-info-circle"></i> {{ text_report_info }}</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-md-6">
            <table class="table table-striped">
              <tr>
                <td><strong>{{ text_year }}:</strong></td>
                <td>{{ tax_data.filter_data.year }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_report_type }}:</strong></td>
                <td>{{ tax_data.filter_data.report_type }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_tax_type }}:</strong></td>
                <td>{{ tax_data.filter_data.tax_type }}</td>
              </tr>
            </table>
          </div>
          <div class="col-md-6">
            <table class="table table-striped">
              <tr>
                <td><strong>{{ text_generated_on }}:</strong></td>
                <td>{{ tax_data.generated_at }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_generated_by }}:</strong></td>
                <td>{{ tax_data.generated_by }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_eta_ready }}:</strong></td>
                <td>
                  {% if tax_data.filter_data.include_eta_format %}
                    <span class="label label-success">{{ text_yes }}</span>
                  {% else %}
                    <span class="label label-default">{{ text_no }}</span>
                  {% endif %}
                </td>
              </tr>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- ملخص الضرائب -->
    <div class="panel panel-success">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-bar-chart"></i> {{ text_summary }}</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-md-3">
            <div class="info-box bg-blue">
              <span class="info-box-icon"><i class="fa fa-calculator"></i></span>
              <div class="info-box-content">
                <span class="info-box-text">{{ text_total_transactions }}</span>
                <span class="info-box-number">{{ tax_data.summary.total_transactions }}</span>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="info-box bg-red">
              <span class="info-box-icon"><i class="fa fa-money"></i></span>
              <div class="info-box-content">
                <span class="info-box-text">{{ text_total_tax_expense }}</span>
                <span class="info-box-number">{{ tax_data.summary.total_tax_expense }}</span>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="info-box bg-green">
              <span class="info-box-icon"><i class="fa fa-credit-card"></i></span>
              <div class="info-box-content">
                <span class="info-box-text">{{ text_total_tax_liability }}</span>
                <span class="info-box-number">{{ tax_data.summary.total_tax_liability }}</span>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="info-box bg-yellow">
              <span class="info-box-icon"><i class="fa fa-balance-scale"></i></span>
              <div class="info-box-content">
                <span class="info-box-text">{{ text_net_tax_impact }}</span>
                <span class="info-box-number">{{ tax_data.summary.net_tax_impact }}</span>
              </div>
            </div>
          </div>
        </div>
        
        <div class="row">
          <div class="col-md-4">
            <table class="table table-striped">
              <tr>
                <td>{{ text_average_tax_amount }}:</td>
                <td class="text-right">{{ tax_data.summary.average_tax_amount }}</td>
              </tr>
              <tr>
                <td>{{ text_highest_tax_amount }}:</td>
                <td class="text-right">{{ tax_data.summary.highest_tax_amount }}</td>
              </tr>
              <tr>
                <td>{{ text_lowest_tax_amount }}:</td>
                <td class="text-right">{{ tax_data.summary.lowest_tax_amount }}</td>
              </tr>
            </table>
          </div>
          <div class="col-md-4">
            <h4>{{ text_tax_efficiency_ratio }}</h4>
            <div class="progress">
              <div class="progress-bar progress-bar-{% if tax_data.summary.tax_efficiency_ratio >= 80 %}success{% elseif tax_data.summary.tax_efficiency_ratio >= 60 %}warning{% else %}danger{% endif %}" 
                   style="width: {{ tax_data.summary.tax_efficiency_ratio }}%">
                {{ tax_data.summary.tax_efficiency_ratio }}%
              </div>
            </div>
          </div>
          <div class="col-md-4">
            {% if compliance_analysis %}
            <h4>{{ text_compliance_score }}</h4>
            <div class="progress">
              <div class="progress-bar progress-bar-{% if compliance_analysis.compliance_score >= 90 %}success{% elseif compliance_analysis.compliance_score >= 70 %}info{% elseif compliance_analysis.compliance_score >= 50 %}warning{% else %}danger{% endif %}" 
                   style="width: {{ compliance_analysis.compliance_score }}%">
                {{ compliance_analysis.compliance_score }}%
              </div>
            </div>
            {% endif %}
          </div>
        </div>
      </div>
    </div>

    <!-- توزيع أنواع الضرائب -->
    {% if tax_data.tax_types_breakdown %}
    <div class="panel panel-info">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pie-chart"></i> {{ text_tax_types_breakdown }}</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-md-8">
            <canvas id="tax-types-chart" width="400" height="200"></canvas>
          </div>
          <div class="col-md-4">
            <table class="table table-striped">
              <thead>
                <tr>
                  <th>{{ text_tax_type }}</th>
                  <th class="text-right">{{ text_amount }}</th>
                  <th class="text-right">{{ text_percentage }}</th>
                </tr>
              </thead>
              <tbody>
                {% for tax_type in tax_data.tax_types_breakdown.tax_types %}
                <tr>
                  <td>{{ tax_type.tax_type }}</td>
                  <td class="text-right">{{ tax_type.tax_amount }}</td>
                  <td class="text-right">{{ tax_type.percentage }}%</td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
    {% endif %}

    <!-- التحليل الشهري -->
    {% if tax_data.monthly_breakdown %}
    <div class="panel panel-warning">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-line-chart"></i> {{ text_monthly_breakdown }}</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-md-12">
            <canvas id="monthly-chart" width="800" height="300"></canvas>
          </div>
        </div>
        <div class="row">
          <div class="col-md-12">
            <div class="table-responsive">
              <table class="table table-bordered table-hover" id="monthly-table">
                <thead>
                  <tr>
                    <th>{{ text_month }}</th>
                    <th class="text-right">{{ text_transactions_count }}</th>
                    <th class="text-right">{{ text_tax_expense }}</th>
                    <th class="text-right">{{ text_tax_liability }}</th>
                    <th class="text-right">{{ text_net_tax_impact }}</th>
                  </tr>
                </thead>
                <tbody>
                  {% for month in tax_data.monthly_breakdown.monthly_data %}
                  <tr>
                    <td>{{ month.month_name }}</td>
                    <td class="text-right">{{ month.transactions_count }}</td>
                    <td class="text-right">{{ month.tax_expense }}</td>
                    <td class="text-right">{{ month.tax_liability }}</td>
                    <td class="text-right">{{ month.net_tax_impact }}</td>
                  </tr>
                  {% endfor %}
                </tbody>
                <tfoot>
                  <tr class="info">
                    <th>{{ text_total }}</th>
                    <th class="text-right">-</th>
                    <th class="text-right">{{ tax_data.monthly_breakdown.total_tax_expense }}</th>
                    <th class="text-right">{{ tax_data.monthly_breakdown.total_tax_liability }}</th>
                    <th class="text-right">-</th>
                  </tr>
                </tfoot>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
    {% endif %}

    <!-- التحليل الربع سنوي -->
    {% if tax_data.quarterly_analysis %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-calendar"></i> {{ text_quarterly_analysis }}</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          {% for quarter in tax_data.quarterly_analysis %}
          <div class="col-md-3">
            <div class="panel panel-primary">
              <div class="panel-heading">
                <h4 class="panel-title">{{ quarter.quarter }}</h4>
              </div>
              <div class="panel-body">
                <table class="table table-condensed">
                  <tr>
                    <td>{{ text_transactions_count }}:</td>
                    <td class="text-right">{{ quarter.transactions_count }}</td>
                  </tr>
                  <tr>
                    <td>{{ text_tax_expense }}:</td>
                    <td class="text-right">{{ quarter.tax_expense }}</td>
                  </tr>
                  <tr>
                    <td>{{ text_tax_liability }}:</td>
                    <td class="text-right">{{ quarter.tax_liability }}</td>
                  </tr>
                </table>
              </div>
            </div>
          </div>
          {% endfor %}
        </div>
      </div>
    </div>
    {% endif %}

    <!-- تحليل الامتثال -->
    {% if compliance_analysis %}
    <div class="panel panel-danger">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-shield"></i> {{ text_compliance_status }}</h3>
      </div>
      <div class="panel-body">
        {% if compliance_analysis.violations %}
        <div class="alert alert-danger">
          <h4>{{ text_violations }}</h4>
          <ul>
            {% for violation in compliance_analysis.violations %}
            <li>
              <strong>{{ violation.description }}</strong>
              {% if violation.severity %}
                <span class="label label-{% if violation.severity == 'critical' %}danger{% elseif violation.severity == 'high' %}warning{% else %}info{% endif %}">
                  {{ violation.severity }}
                </span>
              {% endif %}
            </li>
            {% endfor %}
          </ul>
        </div>
        {% endif %}
        
        {% if compliance_analysis.recommendations %}
        <div class="alert alert-info">
          <h4>{{ text_recommendations }}</h4>
          <ul>
            {% for recommendation in compliance_analysis.recommendations %}
            <li>{{ recommendation }}</li>
            {% endfor %}
          </ul>
        </div>
        {% endif %}
      </div>
    </div>
    {% endif %}

    <!-- معلومات الامتثال -->
    <div class="panel panel-success">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-check-circle"></i> {{ text_compliance_info }}</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-md-3">
            <div class="alert alert-success">
              <i class="fa fa-check"></i> {{ text_egyptian_tax_law }}
            </div>
          </div>
          <div class="col-md-3">
            <div class="alert alert-info">
              <i class="fa fa-globe"></i> {{ text_eta_ready }}
            </div>
          </div>
          <div class="col-md-3">
            <div class="alert alert-warning">
              <i class="fa fa-balance-scale"></i> {{ text_international_standards }}
            </div>
          </div>
          <div class="col-md-3">
            <div class="alert alert-primary">
              <i class="fa fa-list-alt"></i> {{ text_audit_trail }}
            </div>
          </div>
        </div>
      </div>
    </div>

  </div>
</div>

<script type="text/javascript"><!--
// DataTables initialization
$('#monthly-table').DataTable({
    "language": {
        "url": "{{ datatable_language }}"
    },
    "order": [[ 0, "asc" ]],
    "pageLength": 12,
    "dom": 'Bfrtip',
    "buttons": [
        'copy', 'csv', 'excel', 'pdf', 'print'
    ]
});

// Tax Types Chart
{% if tax_data.tax_types_breakdown %}
var ctx1 = document.getElementById('tax-types-chart').getContext('2d');
var taxTypesChart = new Chart(ctx1, {
    type: 'doughnut',
    data: {
        labels: [
            {% for tax_type in tax_data.tax_types_breakdown.tax_types %}
            '{{ tax_type.tax_type }}'{% if not loop.last %},{% endif %}
            {% endfor %}
        ],
        datasets: [{
            data: [
                {% for tax_type in tax_data.tax_types_breakdown.tax_types %}
                {{ tax_type.tax_amount_value }}{% if not loop.last %},{% endif %}
                {% endfor %}
            ],
            backgroundColor: [
                '#3c8dbc',
                '#00a65a',
                '#f39c12',
                '#dd4b39',
                '#605ca8'
            ]
        }]
    },
    options: {
        responsive: true,
        legend: {
            position: 'bottom'
        }
    }
});
{% endif %}

// Monthly Trend Chart
{% if tax_data.monthly_breakdown %}
var ctx2 = document.getElementById('monthly-chart').getContext('2d');
var monthlyChart = new Chart(ctx2, {
    type: 'line',
    data: {
        labels: [
            {% for month in tax_data.monthly_breakdown.monthly_data %}
            '{{ month.month_name }}'{% if not loop.last %},{% endif %}
            {% endfor %}
        ],
        datasets: [{
            label: '{{ text_tax_expense }}',
            data: [
                {% for month in tax_data.monthly_breakdown.monthly_data %}
                {{ month.tax_expense_value }}{% if not loop.last %},{% endif %}
                {% endfor %}
            ],
            borderColor: '#dd4b39',
            backgroundColor: 'rgba(221, 75, 57, 0.1)',
            fill: true
        }, {
            label: '{{ text_tax_liability }}',
            data: [
                {% for month in tax_data.monthly_breakdown.monthly_data %}
                {{ month.tax_liability_value }}{% if not loop.last %},{% endif %}
                {% endfor %}
            ],
            borderColor: '#00a65a',
            backgroundColor: 'rgba(0, 166, 90, 0.1)',
            fill: true
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});
{% endif %}

// Tooltips
$('[data-toggle="tooltip"]').tooltip();
//--></script>

{{ footer }}
