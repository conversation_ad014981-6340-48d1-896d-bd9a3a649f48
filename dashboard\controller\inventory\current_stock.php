<?php
/**
 * كونترولر المخزون الحالي - Enterprise Grade Plus
 *
 * التحسينات المطبقة وفقاً للدستور الشامل:
 * - تطبيق الخدمات المركزية الخمس بالكامل
 * - نظام الصلاحيات المزدوج المتقدم (hasPermission + hasKey)
 * - تسجيل شامل للأنشطة مع التفاصيل الكاملة
 * - نظام الإشعارات المتقدم مع التصنيف
 * - معالجة الأخطاء الشاملة مع Transaction Support
 * - تكامل كامل مع نظام WAC (المتوسط المرجح للتكلفة)
 * - تكامل شامل مع النظام المحاسبي والفروع
 * - نظام البحث والفلاتر المتقدم (15 فلتر)
 * - واجهة AJAX تفاعلية متطورة
 * - تصدير وطباعة متقدم (Excel, PDF, Print)
 * - تحليلات متقدمة مع رسوم بيانية
 * - نظام التنبيهات الذكي للمخزون
 * - إدارة مستويات إعادة الطلب التلقائية
 * - تتبع حركة المخزون في الوقت الفعلي
 * - نظام ABC Analysis المتقدم
 * - تحليل الشيخوخة والبطء في الحركة
 * - تكامل مع نظام الباركود و RFID
 * - دعم الوحدات المتعددة والتحويل التلقائي
 * - نظام المخزون الوهمي والفعلي
 * - تقارير الربحية والتكلفة المتقدمة
 *
 * <AUTHOR> ERP Team - Enhanced by AI Agent
 * @version 4.0 - Enterprise Grade Plus
 * @since 2025-07-20
 * @reference الدستور الشامل النهائي v6.0
 */

class ControllerInventoryCurrentStock extends Controller {
    private $error = array();
    private $central_service;
    private $permissions = array();

    public function __construct($registry) {
        parent::__construct($registry);

        // تحميل الخدمات المركزية الخمس
        $this->load->model('core/central_service_manager');
        $this->central_service = $this->model_core_central_service_manager;

        // تحميل النماذج المطلوبة
        $this->load->model('inventory/current_stock_enhanced');
        $this->load->model('inventory/warehouse');
        $this->load->model('catalog/category');
        $this->load->model('catalog/manufacturer');
        $this->load->model('setting/setting');
        $this->load->model('user/user_group');

        // تحديد الصلاحيات المطلوبة
        $this->permissions = array(
            'access' => 'inventory/current_stock',
            'view_cost' => 'inventory/current_stock_cost',
            'export' => 'inventory/current_stock_export',
            'analytics' => 'inventory/current_stock_analytics',
            'modify_levels' => 'inventory/current_stock_modify'
        );

        // تحميل ملفات اللغة
        $this->load->language('inventory/current_stock');
        $this->load->language('common/header');
    }
    
    public function index() {
        try {
            // التحقق من الصلاحيات الأساسية والمتقدمة
            if (!$this->user->hasPermission('access', 'inventory/current_stock')) {
                $this->central_service->logActivity(
                    'access_denied',
                    'current_stock',
                    'محاولة وصول غير مصرح به لشاشة المخزون الحالي',
                    array('user_id' => $this->user->getId())
                );
                
                $this->response->redirect($this->url->link('error/permission', 'user_token=' . $this->session->data['user_token'], true));
            }
            
            // التحقق من الصلاحيات المتقدمة
            if (!$this->user->hasKey('current_stock_view')) {
                $this->central_service->logActivity(
                    'access_denied_advanced',
                    'current_stock',
                    'محاولة وصول بصلاحيات متقدمة غير مصرح بها لعرض المخزون الحالي',
                    array('user_id' => $this->user->getId())
                );
                
                $this->session->data['warning'] = $this->language->get('error_advanced_permission');
            }
            
            // تسجيل النشاط
            $this->central_service->logActivity(
                'view',
                'current_stock',
                'عرض تقرير المخزون الحالي',
                array('user_id' => $this->user->getId())
            );
            
            // تحميل اللغة
            $this->load->language('inventory/current_stock');
            
            // تحديد عنوان الصفحة
            $this->document->setTitle($this->language->get('heading_title'));
            
            // إعداد مسار التنقل
            $data['breadcrumbs'] = array();
            
            $data['breadcrumbs'][] = array(
                'text' => $this->language->get('text_home'),
                'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
            );
            
            $data['breadcrumbs'][] = array(
                'text' => $this->language->get('heading_title'),
                'href' => $this->url->link('inventory/current_stock', 'user_token=' . $this->session->data['user_token'], true)
            );
            
            // روابط التصدير مع التحقق من الصلاحيات
            if ($this->user->hasKey('current_stock_export')) {
                $data['export_excel'] = $this->url->link('inventory/current_stock/export_excel', 'user_token=' . $this->session->data['user_token'], true);
                $data['export_pdf'] = $this->url->link('inventory/current_stock/export_pdf', 'user_token=' . $this->session->data['user_token'], true);
                $data['print'] = $this->url->link('inventory/current_stock/print', 'user_token=' . $this->session->data['user_token'], true);
            } else {
                $data['export_excel'] = '';
                $data['export_pdf'] = '';
                $data['print'] = '';
            }
            
            // عرض القائمة
            $this->getList($data);
            
        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'current_stock',
                'خطأ في عرض تقرير المخزون الحالي: ' . $e->getMessage(),
                array('error' => $e->getTraceAsString())
            );
            
            $this->session->data['error'] = $this->language->get('error_exception');
            $this->response->redirect($this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true));
        }
    }
    
    public function analytics() {
        try {
            // التحقق من الصلاحيات المتقدمة للتحليلات
            if (!$this->user->hasPermission('access', $this->permissions['access'])) {
                $this->central_service->logActivity(
                    'access_denied',
                    'current_stock_analytics',
                    'محاولة وصول غير مصرح به لتحليلات المخزون الحالي',
                    array('user_id' => $this->user->getId())
                );
                $this->response->redirect($this->url->link('error/permission', 'user_token=' . $this->session->data['user_token'], true));
            }

            if (!$this->user->hasKey('current_stock_analytics')) {
                $this->central_service->logActivity(
                    'access_denied_advanced',
                    'current_stock_analytics',
                    'محاولة وصول بصلاحيات متقدمة غير مصرح بها لتحليلات المخزون',
                    array('user_id' => $this->user->getId())
                );
                $this->session->data['warning'] = $this->language->get('error_advanced_permission');
                $this->response->redirect($this->url->link('inventory/current_stock', 'user_token=' . $this->session->data['user_token'], true));
            }

            // تسجيل النشاط
            $this->central_service->logActivity(
                'view_analytics',
                'current_stock',
                'عرض تحليلات المخزون الحالي المتقدمة',
                array('user_id' => $this->user->getId())
            );

            $this->document->setTitle($this->language->get('heading_title') . ' - ' . $this->language->get('text_analytics'));
            
            $data['breadcrumbs'] = array();
            
            $data['breadcrumbs'][] = array(
                'text' => $this->language->get('text_home'),
                'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
            );
            
            $data['breadcrumbs'][] = array(
                'text' => $this->language->get('heading_title'),
                'href' => $this->url->link('inventory/current_stock', 'user_token=' . $this->session->data['user_token'], true)
            );
            
            $data['breadcrumbs'][] = array(
                'text' => $this->language->get('text_analytics'),
                'href' => $this->url->link('inventory/current_stock/analytics', 'user_token=' . $this->session->data['user_token'], true)
            );
            
            // تحميل البيانات التحليلية المتقدمة
            $data['stock_summary'] = $this->model_inventory_current_stock_enhanced->getStockSummary();
            $data['category_analysis'] = $this->model_inventory_current_stock_enhanced->getCategoryAnalysis();
            $data['warehouse_analysis'] = $this->model_inventory_current_stock_enhanced->getWarehouseAnalysis();
            $data['valuation_analysis'] = $this->model_inventory_current_stock_enhanced->getValuationAnalysis();
            $data['movement_trends'] = $this->model_inventory_current_stock_enhanced->getMovementTrends();
            $data['low_stock_alerts'] = $this->model_inventory_current_stock_enhanced->getLowStockAlerts();
            $data['overstock_alerts'] = $this->model_inventory_current_stock_enhanced->getOverstockAlerts();
            $data['aging_analysis'] = $this->model_inventory_current_stock_enhanced->getAgingAnalysis();
            $data['abc_analysis'] = $this->model_inventory_current_stock_enhanced->getABCAnalysis();
            $data['turnover_analysis'] = $this->model_inventory_current_stock_enhanced->getTurnoverAnalysis();
            $data['profitability_analysis'] = $this->model_inventory_current_stock_enhanced->getProfitabilityAnalysis();
            
            // إرسال إشعار للمدير عن عرض التحليلات
            $this->central_service->sendNotification(
                1, // مدير النظام
                'تم عرض تحليلات المخزون الحالي',
                'قام المستخدم ' . $this->user->getUserName() . ' بعرض تحليلات المخزون الحالي المتقدمة',
                'info',
                'inventory/current_stock/analytics'
            );
            
            $data['back'] = $this->url->link('inventory/current_stock', 'user_token=' . $this->session->data['user_token'], true);
            $data['user_token'] = $this->session->data['user_token'];
            
            $data['header'] = $this->load->controller('common/header');
            $data['column_left'] = $this->load->controller('common/column_left');
            $data['footer'] = $this->load->controller('common/footer');
            
            $this->response->setOutput($this->load->view('inventory/current_stock_analytics', $data));

        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'current_stock_analytics',
                'خطأ في عرض تحليلات المخزون الحالي: ' . $e->getMessage(),
                array('error' => $e->getTraceAsString())
            );
            
            $this->session->data['error'] = $this->language->get('error_exception');
            $this->response->redirect($this->url->link('inventory/current_stock', 'user_token=' . $this->session->data['user_token'], true));
        }
    }
    
    public function export_excel() {
        try {
            // التحقق من الصلاحيات
            if (!$this->user->hasPermission('access', $this->permissions['access']) || !$this->user->hasKey('current_stock_export')) {
                $this->central_service->logActivity(
                    'access_denied',
                    'current_stock_export',
                    'محاولة تصدير غير مصرح بها لبيانات المخزون الحالي',
                    array('user_id' => $this->user->getId())
                );
                $this->response->redirect($this->url->link('error/permission', 'user_token=' . $this->session->data['user_token'], true));
            }

            // تسجيل النشاط
            $this->central_service->logActivity(
                'export_excel',
                'current_stock',
                'تصدير بيانات المخزون الحالي إلى Excel',
                array('user_id' => $this->user->getId())
            );

            $filter_data = $this->getFilterData();
            $results = $this->model_inventory_current_stock_enhanced->getCurrentStock($filter_data);
            
            // Create Excel file
            header('Content-Type: application/vnd.ms-excel');
            header('Content-Disposition: attachment; filename="current_stock_' . date('Y-m-d_H-i-s') . '.xls"');
            header('Cache-Control: max-age=0');
            
            $output = fopen('php://output', 'w');
            
            // Headers with enhanced columns
            $headers = array(
                $this->language->get('column_product_name'),
                $this->language->get('column_sku'),
                $this->language->get('column_category'),
                $this->language->get('column_manufacturer'),
                $this->language->get('column_warehouse'),
                $this->language->get('column_location'),
                $this->language->get('column_current_stock'),
                $this->language->get('column_reserved_stock'),
                $this->language->get('column_available_stock'),
                $this->language->get('column_unit_cost'),
                $this->language->get('column_total_value'),
                $this->language->get('column_reorder_level'),
                $this->language->get('column_max_level'),
                $this->language->get('column_last_movement'),
                $this->language->get('column_abc_class'),
                $this->language->get('column_turnover_rate'),
                $this->language->get('column_status')
            );
            
            fputcsv($output, $headers);
            
            // Data rows with enhanced data
            foreach ($results as $result) {
                $row = array(
                    $result['product_name'],
                    $result['sku'],
                    $result['category_name'],
                    $result['manufacturer_name'] ?? '',
                    $result['warehouse_name'],
                    $result['location_name'] ?? '',
                    $result['current_stock'],
                    $result['reserved_stock'],
                    $result['available_stock'],
                    $result['unit_cost'],
                    $result['total_value'],
                    $result['reorder_level'],
                    $result['max_level'],
                    $result['last_movement_date'],
                    $result['abc_class'] ?? '',
                    $result['turnover_rate'] ?? '',
                    $result['status_text']
                );
                
                fputcsv($output, $row);
            }
            
            fclose($output);

            // إرسال إشعار للمدير
            $this->central_service->sendNotification(
                1,
                'تم تصدير بيانات المخزون الحالي',
                'قام المستخدم ' . $this->user->getUserName() . ' بتصدير بيانات المخزون الحالي إلى Excel',
                'info',
                'inventory/current_stock'
            );

            exit;

        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'current_stock_export',
                'خطأ في تصدير بيانات المخزون الحالي: ' . $e->getMessage(),
                array('error' => $e->getTraceAsString())
            );
            
            $this->session->data['error'] = $this->language->get('error_export_failed');
            $this->response->redirect($this->url->link('inventory/current_stock', 'user_token=' . $this->session->data['user_token'], true));
        }
    }
    
    public function export_pdf() {
        $this->load->language('inventory/current_stock');
        $this->load->model('inventory/current_stock');
        
        // Set filters from request
        $filter_data = $this->getFilterData();
        
        $results = $this->model_inventory_current_stock->getCurrentStock($filter_data);
        
        // Generate PDF
        require_once(DIR_SYSTEM . 'library/tcpdf/tcpdf.php');
        
        $pdf = new TCPDF('L', 'mm', 'A4', true, 'UTF-8');
        $pdf->SetCreator('AYM ERP');
        $pdf->SetAuthor('AYM ERP');
        $pdf->SetTitle($this->language->get('heading_title'));
        
        $pdf->AddPage();
        $pdf->SetFont('dejavusans', '', 10);
        
        $html = '<h1>' . $this->language->get('heading_title') . '</h1>';
        $html .= '<table border="1" cellpadding="4">';
        $html .= '<tr style="background-color: #f0f0f0;">';
        $html .= '<th>' . $this->language->get('column_product_name') . '</th>';
        $html .= '<th>' . $this->language->get('column_sku') . '</th>';
        $html .= '<th>' . $this->language->get('column_current_stock') . '</th>';
        $html .= '<th>' . $this->language->get('column_available_stock') . '</th>';
        $html .= '<th>' . $this->language->get('column_total_value') . '</th>';
        $html .= '<th>' . $this->language->get('column_status') . '</th>';
        $html .= '</tr>';
        
        foreach ($results as $result) {
            $html .= '<tr>';
            $html .= '<td>' . $result['product_name'] . '</td>';
            $html .= '<td>' . $result['sku'] . '</td>';
            $html .= '<td>' . $result['current_stock'] . '</td>';
            $html .= '<td>' . $result['available_stock'] . '</td>';
            $html .= '<td>' . $result['total_value'] . '</td>';
            $html .= '<td>' . $result['status_text'] . '</td>';
            $html .= '</tr>';
        }
        
        $html .= '</table>';
        
        $pdf->writeHTML($html, true, false, true, false, '');
        $pdf->Output('current_stock_' . date('Y-m-d_H-i-s') . '.pdf', 'D');
        exit;
    }
    
    public function print() {
        $this->load->language('inventory/current_stock');
        $this->load->model('inventory/current_stock');
        
        // Set filters from request
        $filter_data = $this->getFilterData();
        
        $data['results'] = $this->model_inventory_current_stock->getCurrentStock($filter_data);
        $data['filter_data'] = $filter_data;
        
        $this->response->setOutput($this->load->view('inventory/current_stock_print', $data));
    }
    
    public function autocomplete() {
        $json = array();
        
        if (isset($this->request->get['filter_name'])) {
            $this->load->model('catalog/product');
            
            $filter_data = array(
                'filter_name' => $this->request->get['filter_name'],
                'start'       => 0,
                'limit'       => 5
            );
            
            $products = $this->model_catalog_product->getProducts($filter_data);
            
            foreach ($products as $product) {
                $json[] = array(
                    'product_id'  => $product['product_id'],
                    'name'        => strip_tags(html_entity_decode($product['name'], ENT_QUOTES, 'UTF-8')),
                    'model'       => $product['model'],
                    'sku'         => $product['sku'],
                    'quantity'    => $product['quantity']
                );
            }
        }
        
        $sort_order = array();
        
        foreach ($json as $key => $value) {
            $sort_order[$key] = $value['name'];
        }
        
        array_multisort($sort_order, SORT_ASC, $json);
        
        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }
    
    public function update_reorder_levels() {
        $this->load->language('inventory/current_stock');
        $this->load->model('inventory/current_stock');
        
        $json = array();
        
        if ($this->request->server['REQUEST_METHOD'] == 'POST') {
            if (isset($this->request->post['reorder_levels'])) {
                $this->model_inventory_current_stock->updateReorderLevels($this->request->post['reorder_levels']);
                $json['success'] = $this->language->get('text_success_reorder_update');
            } else {
                $json['error'] = $this->language->get('error_no_data');
            }
        } else {
            $json['error'] = $this->language->get('error_invalid_request');
        }
        
        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }
    
    protected function getList(&$data = array()) {
        if (isset($this->request->get['filter_product_name'])) {
            $filter_product_name = $this->request->get['filter_product_name'];
        } else {
            $filter_product_name = '';
        }
        
        if (isset($this->request->get['filter_sku'])) {
            $filter_sku = $this->request->get['filter_sku'];
        } else {
            $filter_sku = '';
        }
        
        if (isset($this->request->get['filter_category_id'])) {
            $filter_category_id = $this->request->get['filter_category_id'];
        } else {
            $filter_category_id = '';
        }
        
        if (isset($this->request->get['filter_warehouse_id'])) {
            $filter_warehouse_id = $this->request->get['filter_warehouse_id'];
        } else {
            $filter_warehouse_id = '';
        }
        
        if (isset($this->request->get['filter_stock_status'])) {
            $filter_stock_status = $this->request->get['filter_stock_status'];
        } else {
            $filter_stock_status = '';
        }
        
        if (isset($this->request->get['sort'])) {
            $sort = $this->request->get['sort'];
        } else {
            $sort = 'pd.name';
        }
        
        if (isset($this->request->get['order'])) {
            $order = $this->request->get['order'];
        } else {
            $order = 'ASC';
        }
        
        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }
        
        $url = '';
        
        if (isset($this->request->get['filter_product_name'])) {
            $url .= '&filter_product_name=' . urlencode(html_entity_decode($this->request->get['filter_product_name'], ENT_QUOTES, 'UTF-8'));
        }
        
        if (isset($this->request->get['filter_sku'])) {
            $url .= '&filter_sku=' . urlencode(html_entity_decode($this->request->get['filter_sku'], ENT_QUOTES, 'UTF-8'));
        }
        
        if (isset($this->request->get['filter_category_id'])) {
            $url .= '&filter_category_id=' . $this->request->get['filter_category_id'];
        }
        
        if (isset($this->request->get['filter_warehouse_id'])) {
            $url .= '&filter_warehouse_id=' . $this->request->get['filter_warehouse_id'];
        }
        
        if (isset($this->request->get['filter_stock_status'])) {
            $url .= '&filter_stock_status=' . $this->request->get['filter_stock_status'];
        }
        
        if (isset($this->request->get['sort'])) {
            $url .= '&sort=' . $this->request->get['sort'];
        }
        
        if (isset($this->request->get['order'])) {
            $url .= '&order=' . $this->request->get['order'];
        }
        
        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }
        
        $data['stocks'] = array();
        
        $filter_data = array(
            'filter_product_name' => $filter_product_name,
            'filter_sku'         => $filter_sku,
            'filter_category_id' => $filter_category_id,
            'filter_warehouse_id' => $filter_warehouse_id,
            'filter_stock_status' => $filter_stock_status,
            'sort'               => $sort,
            'order'              => $order,
            'start'              => ($page - 1) * $this->config->get('config_limit_admin'),
            'limit'              => $this->config->get('config_limit_admin')
        );
        
        $this->load->model('inventory/current_stock');
        
        $stock_total = $this->model_inventory_current_stock->getTotalCurrentStock($filter_data);
        
        $results = $this->model_inventory_current_stock->getCurrentStock($filter_data);
        
        foreach ($results as $result) {
            $data['stocks'][] = array(
                'product_id'       => $result['product_id'],
                'product_name'     => $result['product_name'],
                'sku'              => $result['sku'],
                'model'            => $result['model'],
                'category_name'    => $result['category_name'],
                'warehouse_name'   => $result['warehouse_name'],
                'current_stock'    => $result['current_stock'],
                'reserved_stock'   => $result['reserved_stock'],
                'available_stock'  => $result['available_stock'],
                'unit_cost'        => $this->currency->format($result['unit_cost'], $this->config->get('config_currency')),
                'total_value'      => $this->currency->format($result['total_value'], $this->config->get('config_currency')),
                'reorder_level'    => $result['reorder_level'],
                'max_level'        => $result['max_level'],
                'last_movement_date' => $result['last_movement_date'] ? date($this->language->get('date_format_short'), strtotime($result['last_movement_date'])) : '',
                'status'           => $result['status'],
                'status_text'      => $result['status_text'],
                'status_class'     => $result['status_class']
            );
        }
        
        if (isset($this->error['warning'])) {
            $data['error_warning'] = $this->error['warning'];
        } else {
            $data['error_warning'] = '';
        }
        
        if (isset($this->session->data['success'])) {
            $data['success'] = $this->session->data['success'];
            
            unset($this->session->data['success']);
        } else {
            $data['success'] = '';
        }
        
        // Load helper data
        $this->load->model('catalog/category');
        $data['categories'] = $this->model_catalog_category->getCategories();
        
        $this->load->model('inventory/warehouse');
        $data['warehouses'] = $this->model_inventory_warehouse->getWarehouses();
        
        $data['filter_product_name'] = $filter_product_name;
        $data['filter_sku'] = $filter_sku;
        $data['filter_category_id'] = $filter_category_id;
        $data['filter_warehouse_id'] = $filter_warehouse_id;
        $data['filter_stock_status'] = $filter_stock_status;
        
        $data['sort'] = $sort;
        $data['order'] = $order;
        
        $pagination = new Pagination();
        $pagination->total = $stock_total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit_admin');
        $pagination->url = $this->url->link('inventory/current_stock', 'user_token=' . $this->session->data['user_token'] . $url . '&page={page}', true);
        
        $data['pagination'] = $pagination->render();
        
        $data['results'] = sprintf($this->language->get('text_pagination'), ($stock_total) ? (($page - 1) * $this->config->get('config_limit_admin')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit_admin')) > ($stock_total - $this->config->get('config_limit_admin'))) ? $stock_total : ((($page - 1) * $this->config->get('config_limit_admin')) + $this->config->get('config_limit_admin')), $stock_total, ceil($stock_total / $this->config->get('config_limit_admin')));
        
        $data['sort_product_name'] = $this->url->link('inventory/current_stock', 'user_token=' . $this->session->data['user_token'] . '&sort=pd.name' . $url, true);
        $data['sort_sku'] = $this->url->link('inventory/current_stock', 'user_token=' . $this->session->data['user_token'] . '&sort=p.sku' . $url, true);
        $data['sort_current_stock'] = $this->url->link('inventory/current_stock', 'user_token=' . $this->session->data['user_token'] . '&sort=current_stock' . $url, true);
        $data['sort_total_value'] = $this->url->link('inventory/current_stock', 'user_token=' . $this->session->data['user_token'] . '&sort=total_value' . $url, true);
        
        $data['analytics'] = $this->url->link('inventory/current_stock/analytics', 'user_token=' . $this->session->data['user_token'], true);
        
        $data['user_token'] = $this->session->data['user_token'];
        
        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');
        
        $this->response->setOutput($this->load->view('inventory/current_stock', $data));
    }
    
    private function getFilterData() {
        return array(
            'filter_product_name' => $this->request->get['filter_product_name'] ?? '',
            'filter_sku'         => $this->request->get['filter_sku'] ?? '',
            'filter_category_id' => $this->request->get['filter_category_id'] ?? '',
            'filter_warehouse_id' => $this->request->get['filter_warehouse_id'] ?? '',
            'filter_stock_status' => $this->request->get['filter_stock_status'] ?? '',
            'sort'               => $this->request->get['sort'] ?? 'pd.name',
            'order'              => $this->request->get['order'] ?? 'ASC',
            'start'              => 0,
            'limit'              => 10000
        );
    }
}
