<?php
// Heading
$_['heading_title'] = 'Meetings';

// Text
$_['text_list'] = 'Meetings List';
$_['text_modal_add'] = 'Add Meeting';
$_['text_modal_edit'] = 'Edit Meeting';
$_['text_confirm_delete'] = 'Are you sure you want to delete?';
$_['text_home'] = 'Home';
$_['text_all_types'] = '- All Types -';
$_['text_attendees'] = 'Attendees';
$_['text_meeting_attendees'] = 'Meeting Attendance No.';
$_['text_user_id'] = 'User ID';
$_['text_external_name'] = 'External Name';
$_['text_role_in_meeting'] = 'Role';
$_['text_presence_status'] = 'Status';
$_['text_attended'] = 'Attended';
$_['text_excused'] = 'Excused';
$_['text_absent'] = 'Absent';
$_['text_add_attendee'] = 'Add Attendee';
$_['text_delete'] = 'Delete';

// Entry
$_['entry_meeting_type'] = 'Meeting Type';
$_['entry_title'] = 'Title';
$_['entry_meeting_date'] = 'Meeting Date';
$_['entry_location'] = 'Location';
$_['entry_agenda'] = 'Agenda';
$_['entry_decisions'] = 'Decisions';

// Columns
$_['column_meeting_id'] = 'Number';
$_['column_meeting_type'] = 'Type';
$_['column_title'] = 'Title';
$_['column_meeting_date'] = 'Date/Time';
$_['column_location'] = 'Location';
$_['column_action'] = 'Action';

// Buttons
$_['button_filter'] = 'Filter';
$_['button_add'] = 'Add';
$_['button_edit'] = 'Edit';
$_['button_delete'] = 'Delete';
$_['button_save'] = 'Save';
$_['button_close'] = 'close';