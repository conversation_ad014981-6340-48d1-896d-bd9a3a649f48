<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/expr/v1beta1/decl.proto

namespace GPBMetadata\Google\Api\Expr\V1Beta1;

class Decl
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        \GPBMetadata\Google\Api\Expr\V1Beta1\Expr::initOnce();
        $pool->internalAddGeneratedFile(hex2bin(
            "0ab9050a22676f6f676c652f6170692f657870722f763162657461312f64" .
            "65636c2e70726f746f1217676f6f676c652e6170692e657870722e763162" .
            "6574613122a5010a044465636c120a0a026964180120012805120c0a046e" .
            "616d65180220012809120b0a03646f6318032001280912330a056964656e" .
            "7418042001280b32222e676f6f676c652e6170692e657870722e76316265" .
            "7461312e4964656e744465636c480012390a0866756e6374696f6e180520" .
            "01280b32252e676f6f676c652e6170692e657870722e763162657461312e" .
            "46756e6374696f6e4465636c480042060a046b696e64225c0a084465636c" .
            "54797065120a0a026964180120012805120c0a0474797065180220012809" .
            "12360a0b747970655f706172616d7318042003280b32212e676f6f676c65" .
            "2e6170692e657870722e763162657461312e4465636c54797065226a0a09" .
            "4964656e744465636c122f0a047479706518032001280b32212e676f6f67" .
            "6c652e6170692e657870722e763162657461312e4465636c54797065122c" .
            "0a0576616c756518042001280b321d2e676f6f676c652e6170692e657870" .
            "722e763162657461312e457870722293010a0c46756e6374696f6e446563" .
            "6c12300a046172677318012003280b32222e676f6f676c652e6170692e65" .
            "7870722e763162657461312e4964656e744465636c12360a0b7265747572" .
            "6e5f7479706518022001280b32212e676f6f676c652e6170692e65787072" .
            "2e763162657461312e4465636c5479706512190a1172656365697665725f" .
            "66756e6374696f6e180320012808426a0a1b636f6d2e676f6f676c652e61" .
            "70692e657870722e7631626574613142094465636c50726f746f50015a3b" .
            "676f6f676c652e676f6c616e672e6f72672f67656e70726f746f2f676f6f" .
            "676c65617069732f6170692f657870722f763162657461313b65787072f8" .
            "0101620670726f746f33"
        ), true);

        static::$is_initialized = true;
    }
}

