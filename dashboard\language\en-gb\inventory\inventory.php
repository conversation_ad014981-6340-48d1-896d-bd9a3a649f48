<?php
// Heading
$_['heading_title'] = 'Inventory Overview';

// Text
$_['text_home'] = 'Home';
$_['text_filters'] = 'Search Filters';
$_['text_branch'] = 'Branch';
$_['text_all_branches'] = '- All Branches -';
$_['text_product'] = 'Product';
$_['text_all_products'] = '- All Products -';
$_['text_consignment'] = 'Security';
$_['text_consignment_only'] = 'Security Only';
$_['text_owned_only'] = 'Company Owned';
$_['text_all'] = '- All -';
$_['text_select'] = 'Select...';
$_['text_inventory_list'] = 'Inventory List';
$_['text_loading'] = 'Loading...';

$_['text_success_export'] = 'File exported successfully!';
$_['text_success_print'] = 'Preparing to print...';

$_['text_value_total'] = 'Total Inventory Value:';

// Button
$_['button_filter'] = 'Search';
$_['button_reset'] = 'Dump';
$_['button_export_csv'] = 'CSV Export';
$_['button_export_pdf'] = 'Export PDF';
$_['button_print'] = 'Print';

// Column
$_['column_branch'] = 'Branch';
$_['column_product'] = 'Product';
$_['column_unit'] = 'Unit';
$_['column_quantity'] = 'Quantity';
$_['column_average_cost'] = 'Average Cost';
$_['column_total_value'] = 'Total Value';

// Error / Warning
$_['error_permission'] = 'Warning: You do not have edit permission!';