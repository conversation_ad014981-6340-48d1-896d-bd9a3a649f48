/* إعدادات متقدمة للنظام - تصميم Enterprise Grade */

/* تحسينات عامة للإعدادات */
.form-horizontal .form-group {
    margin-bottom: 20px;
}

.form-horizontal .control-label {
    font-weight: 600;
    color: #333;
}

.help-block {
    font-size: 12px;
    color: #777;
    margin-top: 5px;
}

/* تصميم التبويبات المتقدم */
.nav-tabs {
    border-bottom: 2px solid #ddd;
    margin-bottom: 30px;
}

.nav-tabs > li > a {
    border-radius: 4px 4px 0 0;
    font-weight: 600;
    color: #555;
    transition: all 0.3s ease;
}

.nav-tabs > li.active > a,
.nav-tabs > li.active > a:hover,
.nav-tabs > li.active > a:focus {
    background-color: #337ab7;
    color: white;
    border-color: #337ab7;
}

/* تصميم Fieldsets المتقدم */
fieldset {
    border: 1px solid #e5e5e5;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 30px;
    background-color: #fafafa;
}

fieldset legend {
    width: auto;
    padding: 0 15px;
    margin-bottom: 0;
    font-size: 16px;
    font-weight: 600;
    color: #337ab7;
    border: none;
    background-color: #fafafa;
}

fieldset legend i {
    margin-right: 8px;
    color: #337ab7;
}

/* تصميم Select2 المحسن */
.select2-container--default .select2-selection--single {
    height: 34px;
    border: 1px solid #ccc;
    border-radius: 4px;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 32px;
    padding-left: 12px;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 32px;
}

/* تصميم أزرار الراديو المحسن */
.btn-group[data-toggle="buttons"] .btn {
    margin-right: 5px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.btn-group[data-toggle="buttons"] .btn.active {
    background-color: #5cb85c;
    border-color: #4cae4c;
    color: white;
}

/* تصميم ETA المتقدم */
.eta-status-overview {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    border-radius: 12px;
    margin-bottom: 30px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
}

.eta-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.eta-title h3 {
    margin: 0;
    font-size: 24px;
    font-weight: 700;
}

.eta-subtitle {
    margin: 5px 0 0 0;
    opacity: 0.9;
    font-size: 14px;
}

.eta-connection-status {
    text-align: center;
}

.connection-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.status-dot.connected {
    background-color: #28a745;
}

.status-dot.disconnected {
    background-color: #dc3545;
}

.status-dot.not-configured {
    background-color: #ffc107;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* إحصائيات ETA */
.eta-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.stat-box {
    background: rgba(255,255,255,0.1);
    padding: 20px;
    border-radius: 8px;
    text-align: center;
    backdrop-filter: blur(10px);
}

.stat-box h4 {
    margin: 0 0 5px 0;
    font-size: 28px;
    font-weight: 700;
}

.stat-box p {
    margin: 0;
    opacity: 0.9;
    font-size: 14px;
}

/* تصميم إعدادات المحاسبة المتقدمة */
.accounting-section {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 25px;
    margin-bottom: 25px;
}

.accounting-section h3 {
    color: #495057;
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #dee2e6;
}

/* تصميم حقول التاريخ */
.input-group.date .input-group-btn .btn {
    border-left: none;
    background-color: #f8f9fa;
    border-color: #ced4da;
}

.input-group.date .form-control {
    border-right: none;
}

/* تصميم رسائل التحقق */
.text-danger {
    color: #dc3545 !important;
    font-size: 12px;
    margin-top: 5px;
    display: block;
}

.text-success {
    color: #28a745 !important;
    font-size: 12px;
    margin-top: 5px;
    display: block;
}

/* تصميم التنبيهات */
.alert {
    border-radius: 8px;
    border: none;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.alert-info {
    background: linear-gradient(135deg, #17a2b8, #138496);
    color: white;
}

.alert-success {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.alert-warning {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    color: #212529;
}

.alert-danger {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
}

/* تصميم الأزرار المحسن */
.btn-primary {
    background: linear-gradient(135deg, #007bff, #0056b3);
    border: none;
    border-radius: 6px;
    padding: 10px 20px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0,123,255,0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,123,255,0.4);
}

.btn-success {
    background: linear-gradient(135deg, #28a745, #20c997);
    border: none;
    border-radius: 6px;
    padding: 10px 20px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(40,167,69,0.3);
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(40,167,69,0.4);
}

/* تصميم متجاوب */
@media (max-width: 768px) {
    .eta-header {
        flex-direction: column;
        text-align: center;
    }
    
    .eta-stats {
        grid-template-columns: 1fr;
    }
    
    fieldset {
        padding: 15px;
    }
    
    .form-horizontal .col-sm-2,
    .form-horizontal .col-sm-10 {
        width: 100%;
        margin-bottom: 10px;
    }
    
    .form-horizontal .control-label {
        text-align: left;
        margin-bottom: 5px;
    }
}

/* تحسينات إضافية للتفاعل */
.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

.select2-container--default.select2-container--focus .select2-selection--single {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

/* تصميم التحميل */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255,255,255,0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
