{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        {% if can_export %}
        <div class="btn-group">
          <button type="button" class="btn btn-success dropdown-toggle" data-toggle="dropdown">
            <i class="fa fa-download"></i> {{ button_export_excel }}
            <span class="caret"></span>
          </button>
          <ul class="dropdown-menu">
            <li><a href="{{ export_excel }}"><i class="fa fa-file-excel-o"></i> Excel</a></li>
            <li><a href="{{ export_pdf }}"><i class="fa fa-file-pdf-o"></i> PDF</a></li>
            <li><a href="{{ print }}"><i class="fa fa-print"></i> {{ button_print }}</a></li>
          </ul>
        </div>
        {% endif %}
        <button type="button" class="btn btn-info" onclick="showStatistics()">
          <i class="fa fa-bar-chart"></i> الإحصائيات
        </button>
        <a href="{{ refresh }}" class="btn btn-default">
          <i class="fa fa-refresh"></i> {{ button_refresh }}
        </a>
      </div>
      <h1><i class="fa fa-exchange"></i> {{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>

  <div class="container-fluid">
    <!-- رسائل التنبيه -->
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible">
      <i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    
    {% if success %}
    <div class="alert alert-success alert-dismissible">
      <i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}

    <!-- لوحة الملخص السريع -->
    {% if summary %}
    <div class="row">
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-primary">
          <div class="panel-body">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-list fa-3x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ summary.total_movements }}</div>
                <div>{{ text_total_movements }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-success">
          <div class="panel-body">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-cubes fa-3x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ summary.total_products }}</div>
                <div>المنتجات المتأثرة</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-info">
          <div class="panel-body">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-arrow-up fa-3x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ summary.total_quantity_in }}</div>
                <div>إجمالي الوارد</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-warning">
          <div class="panel-body">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-arrow-down fa-3x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ summary.total_quantity_out }}</div>
                <div>إجمالي الصادر</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    {% endif %}

    <!-- لوحة الفلاتر -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title">
          <i class="fa fa-filter"></i> {{ text_filters }}
          <button type="button" class="btn btn-xs btn-link pull-right" data-toggle="collapse" data-target="#filter-panel">
            <i class="fa fa-chevron-down"></i>
          </button>
        </h3>
      </div>
      <div class="panel-body collapse" id="filter-panel">
        <form id="filter-form" class="form-horizontal">
          <div class="row">
            <div class="col-md-3">
              <div class="form-group">
                <label class="control-label">{{ entry_product_name }}</label>
                <input type="text" name="filter_product_name" value="{{ filter_product_name }}" placeholder="{{ entry_product_name }}" class="form-control">
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <label class="control-label">{{ entry_branch }}</label>
                <select name="filter_branch_id" class="form-control">
                  <option value="">{{ text_all }}</option>
                  {% for branch in branches %}
                  <option value="{{ branch.branch_id }}"{% if filter_branch_id == branch.branch_id %} selected{% endif %}>{{ branch.name }}</option>
                  {% endfor %}
                </select>
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <label class="control-label">نوع الحركة</label>
                <select name="filter_movement_type" class="form-control">
                  {% for option in movement_type_options %}
                  <option value="{{ option.value }}"{% if filter_movement_type == option.value %} selected{% endif %}>{{ option.text }}</option>
                  {% endfor %}
                </select>
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <label class="control-label">{{ entry_lot_number }}</label>
                <input type="text" name="filter_lot_number" value="{{ filter_lot_number }}" placeholder="{{ entry_lot_number }}" class="form-control">
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-3">
              <div class="form-group">
                <label class="control-label">من تاريخ</label>
                <input type="date" name="filter_date_from" value="{{ filter_date_from }}" class="form-control">
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <label class="control-label">إلى تاريخ</label>
                <input type="date" name="filter_date_to" value="{{ filter_date_to }}" class="form-control">
              </div>
            </div>
            <div class="col-md-6 text-right">
              <div class="form-group">
                <label class="control-label">&nbsp;</label>
                <div>
                  <button type="button" class="btn btn-primary" onclick="applyFilters()">
                    <i class="fa fa-search"></i> {{ button_filter }}
                  </button>
                  <button type="button" class="btn btn-default" onclick="clearFilters()">
                    <i class="fa fa-refresh"></i> مسح
                  </button>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>

    <!-- جدول الحركات -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title">
          <i class="fa fa-list"></i> {{ text_list }}
          <span class="badge pull-right">{{ results }}</span>
        </h3>
      </div>
      <div class="panel-body">
        <div class="table-responsive">
          <table class="table table-bordered table-hover" id="movement-table">
            <thead>
              <tr>
                <th class="text-center">{{ column_date }}</th>
                <th class="text-left">{{ column_product_name }}</th>
                <th class="text-left">{{ column_branch }}</th>
                <th class="text-center">{{ column_movement_type }}</th>
                <th class="text-center">{{ column_quantity_in }}</th>
                <th class="text-center">{{ column_quantity_out }}</th>
                <th class="text-center">{{ column_running_balance }}</th>
                {% if can_view_cost %}
                <th class="text-right">{{ column_unit_cost }}</th>
                <th class="text-right">{{ column_total_cost }}</th>
                {% endif %}
                <th class="text-left">{{ column_user }}</th>
                <th class="text-center">{{ column_action }}</th>
              </tr>
            </thead>
            <tbody>
              {% if stock_movements %}
              {% for movement in stock_movements %}
              <tr data-movement-id="{{ movement.movement_id }}">
                <td class="text-center">
                  <small>{{ movement.date_added }}</small>
                </td>
                <td class="text-left">
                  <strong>{{ movement.product_name }}</strong>
                  {% if movement.model %}
                  <br><small class="text-muted">{{ movement.model }}</small>
                  {% endif %}
                  {% if movement.sku %}
                  <br><code>{{ movement.sku }}</code>
                  {% endif %}
                </td>
                <td class="text-left">
                  {{ movement.branch_name }}
                  {% if movement.branch_type %}
                  <br><small class="text-muted">{{ movement.branch_type }}</small>
                  {% endif %}
                </td>
                <td class="text-center">
                  <span class="label label-{{ movement.movement_type_class }}">
                    {{ movement.movement_type_text }}
                  </span>
                </td>
                <td class="text-center">
                  {% if movement.quantity_in_raw > 0 %}
                  <span class="text-success">
                    <i class="fa fa-arrow-up"></i> {{ movement.quantity_in }}
                  </span>
                  {% endif %}
                </td>
                <td class="text-center">
                  {% if movement.quantity_out_raw > 0 %}
                  <span class="text-danger">
                    <i class="fa fa-arrow-down"></i> {{ movement.quantity_out }}
                  </span>
                  {% endif %}
                </td>
                <td class="text-center">
                  <strong>{{ movement.running_balance }}</strong>
                  {% if movement.unit_symbol %}
                  <small>{{ movement.unit_symbol }}</small>
                  {% endif %}
                </td>
                {% if can_view_cost %}
                <td class="text-right">{{ movement.unit_cost }}</td>
                <td class="text-right">{{ movement.total_cost }}</td>
                {% endif %}
                <td class="text-left">
                  <small>{{ movement.user_name }}</small>
                </td>
                <td class="text-center">
                  <div class="btn-group">
                    {% if movement.view_reference %}
                    <a href="{{ movement.view_reference }}" class="btn btn-info btn-xs" data-toggle="tooltip" title="عرض المستند">
                      <i class="fa fa-eye"></i>
                    </a>
                    {% endif %}
                    <a href="{{ movement.product_card }}" class="btn btn-primary btn-xs" data-toggle="tooltip" title="كارت الصنف">
                      <i class="fa fa-file-text"></i>
                    </a>
                  </div>
                </td>
              </tr>
              {% endfor %}
              {% else %}
              <tr>
                <td class="text-center" colspan="{% if can_view_cost %}11{% else %}9{% endif %}">
                  <div class="alert alert-info" style="margin: 20px 0;">
                    <i class="fa fa-info-circle"></i> {{ text_no_results }}
                  </div>
                </td>
              </tr>
              {% endif %}
            </tbody>
          </table>
        </div>
        
        <!-- الصفحات -->
        <div class="row">
          <div class="col-sm-6 text-left">{{ pagination }}</div>
          <div class="col-sm-6 text-right">{{ results }}</div>
        </div>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript">
// متغيرات عامة
var user_token = '{{ user_token }}';
var movement_url = 'index.php?route=inventory/stock_movement';

// تطبيق الفلاتر
function applyFilters() {
    var url = movement_url + '&user_token=' + user_token;
    var filters = $('#filter-form').serialize();
    
    if (filters) {
        url += '&' + filters;
    }
    
    location = url;
}

// مسح الفلاتر
function clearFilters() {
    $('#filter-form')[0].reset();
    location = movement_url + '&user_token=' + user_token;
}

// عرض الإحصائيات
function showStatistics() {
    // يمكن إضافة modal للإحصائيات المفصلة
    alert('سيتم إضافة الإحصائيات المفصلة قريباً');
}

// تهيئة الصفحة
$(document).ready(function() {
    // تفعيل tooltips
    $('[data-toggle="tooltip"]').tooltip();
    
    // تفعيل البحث السريع
    $('#quick-search').on('keyup', function() {
        var value = $(this).val().toLowerCase();
        $('#movement-table tbody tr').filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
        });
    });
});
</script>

<style>
.huge {
    font-size: 40px;
}

.panel-body {
    padding: 15px;
}

.btn-group .btn {
    margin-right: 2px;
}

.btn-group .btn:last-child {
    margin-right: 0;
}

#movement-table th {
    background-color: #f5f5f5;
    font-weight: bold;
}

#movement-table tbody tr:hover {
    background-color: #f9f9f9;
}

@media (max-width: 768px) {
    .huge {
        font-size: 24px;
    }
    
    .btn-group .btn {
        margin-bottom: 5px;
    }
}
</style>

{{ footer }}