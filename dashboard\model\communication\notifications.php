<?php
/**
 * ═══════════════════════════════════════════════════════════════════════════════
 * AYM ERP - Notifications Model (الدستور الشامل)
 * ═══════════════════════════════════════════════════════════════════════════════
 * 
 * نموذج الإشعارات المتقدم للنظام
 * يدعم إشعارات متعددة الأنواع والقنوات
 * 
 * @package AYM ERP
 * @subpackage Communication
 * @version 3.0.0 - Enterprise Grade Plus
 * <AUTHOR> ERP Development Team
 * @copyright 2025 AYM ERP Systems
 * ═══════════════════════════════════════════════════════════════════════════════
 */

class ModelCommunicationNotifications extends Model {
    
    /**
     * Add notification
     */
    public function addNotification($data) {
        // التحقق من وجود الجدول
        $this->createTableIfNotExists();
        
        $this->db->query("
            INSERT INTO " . DB_PREFIX . "notification SET
            `type` = '" . $this->db->escape($data['type']) . "',
            `title` = '" . $this->db->escape($data['title']) . "',
            `message` = '" . $this->db->escape($data['message']) . "',
            `data` = '" . $this->db->escape(isset($data['data']) ? json_encode($data['data']) : '') . "',
            `user_id` = '" . (int)(isset($data['user_id']) ? $data['user_id'] : 0) . "',
            `user_group_id` = '" . (int)(isset($data['user_group_id']) ? $data['user_group_id'] : 0) . "',
            `priority` = '" . $this->db->escape(isset($data['priority']) ? $data['priority'] : 'normal') . "',
            `status` = '" . (int)(isset($data['status']) ? $data['status'] : 1) . "',
            `date_added` = NOW()
        ");
        
        return $this->db->getLastId();
    }
    
    /**
     * Get notifications
     */
    public function getNotifications($data = array()) {
        $sql = "SELECT * FROM " . DB_PREFIX . "notification";
        
        $where = array();
        
        if (!empty($data['filter_type'])) {
            $where[] = "`type` = '" . $this->db->escape($data['filter_type']) . "'";
        }
        
        if (!empty($data['filter_user_id'])) {
            $where[] = "(user_id = '" . (int)$data['filter_user_id'] . "' OR user_id = 0)";
        }
        
        if (!empty($data['filter_status'])) {
            $where[] = "status = '" . (int)$data['filter_status'] . "'";
        }
        
        if ($where) {
            $sql .= " WHERE " . implode(" AND ", $where);
        }
        
        $sql .= " ORDER BY priority DESC, date_added DESC";
        
        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }
            
            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }
            
            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }
        
        $query = $this->db->query($sql);
        
        return $query->rows;
    }
    
    /**
     * Get notification
     */
    public function getNotification($notification_id) {
        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "notification WHERE notification_id = '" . (int)$notification_id . "'");
        
        return $query->row;
    }
    
    /**
     * Update notification
     */
    public function editNotification($notification_id, $data) {
        $this->db->query("
            UPDATE " . DB_PREFIX . "notification SET
            `type` = '" . $this->db->escape($data['type']) . "',
            `title` = '" . $this->db->escape($data['title']) . "',
            `message` = '" . $this->db->escape($data['message']) . "',
            `data` = '" . $this->db->escape(isset($data['data']) ? json_encode($data['data']) : '') . "',
            `priority` = '" . $this->db->escape(isset($data['priority']) ? $data['priority'] : 'normal') . "',
            `status` = '" . (int)(isset($data['status']) ? $data['status'] : 1) . "',
            `date_modified` = NOW()
            WHERE notification_id = '" . (int)$notification_id . "'
        ");
    }
    
    /**
     * Delete notification
     */
    public function deleteNotification($notification_id) {
        $this->db->query("DELETE FROM " . DB_PREFIX . "notification WHERE notification_id = '" . (int)$notification_id . "'");
    }
    
    /**
     * Mark as read
     */
    public function markAsRead($notification_id, $user_id) {
        // التحقق من وجود جدول القراءة
        $this->createReadTableIfNotExists();
        
        $this->db->query("
            INSERT IGNORE INTO " . DB_PREFIX . "notification_read SET
            notification_id = '" . (int)$notification_id . "',
            user_id = '" . (int)$user_id . "',
            date_read = NOW()
        ");
    }
    
    /**
     * Get unread count for user
     */
    public function getUnreadCount($user_id) {
        $sql = "
            SELECT COUNT(*) as total 
            FROM " . DB_PREFIX . "notification n
            LEFT JOIN " . DB_PREFIX . "notification_read nr ON (n.notification_id = nr.notification_id AND nr.user_id = '" . (int)$user_id . "')
            WHERE n.status = 1 
            AND (n.user_id = '" . (int)$user_id . "' OR n.user_id = 0)
            AND nr.notification_id IS NULL
        ";
        
        $query = $this->db->query($sql);
        
        return $query->row['total'];
    }
    
    /**
     * Get user notifications
     */
    public function getUserNotifications($user_id, $limit = 10) {
        $sql = "
            SELECT n.*, 
                   CASE WHEN nr.notification_id IS NOT NULL THEN 1 ELSE 0 END as is_read
            FROM " . DB_PREFIX . "notification n
            LEFT JOIN " . DB_PREFIX . "notification_read nr ON (n.notification_id = nr.notification_id AND nr.user_id = '" . (int)$user_id . "')
            WHERE n.status = 1 
            AND (n.user_id = '" . (int)$user_id . "' OR n.user_id = 0)
            ORDER BY n.priority DESC, n.date_added DESC
            LIMIT " . (int)$limit;
        
        $query = $this->db->query($sql);
        
        return $query->rows;
    }
    
    /**
     * Send notification to user group
     */
    public function sendToUserGroup($user_group_id, $data) {
        $data['user_group_id'] = $user_group_id;
        return $this->addNotification($data);
    }
    
    /**
     * Send notification to specific user
     */
    public function sendToUser($user_id, $data) {
        $data['user_id'] = $user_id;
        return $this->addNotification($data);
    }
    
    /**
     * Send broadcast notification
     */
    public function sendBroadcast($data) {
        $data['user_id'] = 0;
        $data['user_group_id'] = 0;
        return $this->addNotification($data);
    }
    
    /**
     * Create table if not exists
     */
    private function createTableIfNotExists() {
        $this->db->query("
            CREATE TABLE IF NOT EXISTS " . DB_PREFIX . "notification (
                `notification_id` int(11) NOT NULL AUTO_INCREMENT,
                `type` varchar(64) NOT NULL,
                `title` varchar(255) NOT NULL,
                `message` text NOT NULL,
                `data` text NOT NULL,
                `user_id` int(11) NOT NULL DEFAULT '0',
                `user_group_id` int(11) NOT NULL DEFAULT '0',
                `priority` enum('low','normal','high','urgent') NOT NULL DEFAULT 'normal',
                `status` tinyint(1) NOT NULL DEFAULT '1',
                `date_added` datetime NOT NULL,
                `date_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`notification_id`),
                KEY `type` (`type`),
                KEY `user_id` (`user_id`),
                KEY `user_group_id` (`user_group_id`),
                KEY `status` (`status`),
                KEY `date_added` (`date_added`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;
        ");
    }
    
    /**
     * Create read table if not exists
     */
    private function createReadTableIfNotExists() {
        $this->db->query("
            CREATE TABLE IF NOT EXISTS " . DB_PREFIX . "notification_read (
                `notification_id` int(11) NOT NULL,
                `user_id` int(11) NOT NULL,
                `date_read` datetime NOT NULL,
                PRIMARY KEY (`notification_id`, `user_id`),
                KEY `user_id` (`user_id`),
                KEY `date_read` (`date_read`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;
        ");
    }
    
    /**
     * Clean old notifications
     */
    public function cleanOldNotifications($days = 30) {
        $this->db->query("DELETE FROM " . DB_PREFIX . "notification WHERE date_added < DATE_SUB(NOW(), INTERVAL " . (int)$days . " DAY)");
    }
    
    /**
     * Get notification statistics
     */
    public function getNotificationStatistics() {
        $sql = "
            SELECT 
                type,
                COUNT(*) as count,
                SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as active_count
            FROM " . DB_PREFIX . "notification 
            WHERE date_added >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            GROUP BY type
            ORDER BY count DESC
        ";
        
        $query = $this->db->query($sql);
        
        return $query->rows;
    }
}
