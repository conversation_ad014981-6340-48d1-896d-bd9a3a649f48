{% if not redirect %}
  <div class="table-responsive">
    <table class="table table-bordered table-hover">
      <thead>
        <tr>
          <td class="text-left">{{ column_name }}</td>
          <td class="text-right">{{ column_quantity }}</td>
          <td class="text-right">{{ column_price }}</td>
          <td class="text-right">{{ column_total }}</td>
        </tr>
      </thead>
      <tbody>
        {% for product in products %}
          <tr>
            <td class="text-left"><a href="{{ product.href }}">{{ product.name }}</a>
              {% for option in product.option %}
                <br/>
                &nbsp;<small> - {{ option.name }}: {{ option.value }}</small>
              {% endfor %}
              {% if product.subscription %}
                <br/>
                <span class="label label-info">{{ text_subscription_item }}</span>
                <small>{{ product.subscription }}</small>
              {% endif %}</td>
            <td class="text-right">{{ product.quantity }}</td>
            <td class="text-right">{{ product.price }}</td>
            <td class="text-right">{{ product.total }}</td>
          </tr>
        {% endfor %}
        {% for voucher in vouchers %}
          <tr>
            <td class="text-left">{{ voucher.description }}</td>
            <td class="text-left"></td>
            <td class="text-right">1</td>
            <td class="text-right">{{ voucher.amount }}</td>
            <td class="text-right">{{ voucher.amount }}</td>
          </tr>
        {% endfor %}
      </tbody>
      <tfoot>
        {% for total in totals %}
          <tr>
            <td colspan="4" class="text-right"><strong>{{ total.title }}:</strong></td>
            <td class="text-right">{{ total.text }}</td>
          </tr>
        {% endfor %}
      </tfoot>
    </table>
  </div>
  {{ payment }}
{% else %}
  <script type="text/javascript"><!--
      location = '{{ redirect }}';
  //--></script>
{% endif %}