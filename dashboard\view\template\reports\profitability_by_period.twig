{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <a href="{{ add }}" data-bs-toggle="tooltip" title="{{ button_add }}" class="btn btn-primary"><i class="fas fa-plus"></i></a>
        <button type="button" data-bs-toggle="tooltip" title="{{ button_delete }}" class="btn btn-danger" onclick="confirm('{{ text_confirm }}') ? $('#form-reports\profitability_analysis').submit() : false;"><i class="fas fa-trash-alt"></i></button>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    
    <div class="card">
      <div class="card-header"><i class="fas fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}chart-bar{% elif screen_type == 'settings' %}cog{% else %}pencil-alt{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="card-body">
        <div id="filter-reports\profitability_analysis" class="filter-container collapse">
          <div class="filter-card card mb-3">
            <div class="card-header">
              <h5 class="card-title">
                <i class="fas fa-filter"></i> {{ text_filter }}
              </h5>
            </div>
            <div class="card-body p-0">
              <div class="filter-form-container p-3">
                <form id="filter-form">
                  <div class="row">
                    <div class="col-sm-4">
                      <div class="mb-3">
                        <label for="input-name">{{ entry_name }}</label>
                        <input type="text" name="filter_name" value="{{ filter_name }}" placeholder="{{ entry_name }}" id="input-name" class="form-control" />
                      </div>
                    </div>
                    <div class="col-sm-4">
                      <div class="mb-3">
                        <label for="input-status">{{ entry_status }}</label>
                        <select name="filter_status" id="input-status" class="form-select">
                          <option value="">{{ text_all }}</option>
                          <option value="1" {% if filter_status == '1' %}selected{% endif %}>{{ text_enabled }}</option>
                          <option value="0" {% if filter_status == '0' %}selected{% endif %}>{{ text_disabled }}</option>
                        </select>
                      </div>
                    </div>
                    <div class="col-sm-4">
                      <div class="mb-3">
                        <label for="input-date">{{ entry_date_added }}</label>
                        <div class="input-group date">
                          <input type="text" name="filter_date_added" value="{{ filter_date_added }}" placeholder="{{ entry_date_added }}" id="input-date" class="form-control" />
                          <div class="input-group-text"><i class="fas fa-calendar"></i></div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="text-end">
                    <button type="button" id="button-filter" class="btn btn-light"><i class="fas fa-filter"></i> {{ button_filter }}</button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
        <form id="form-reports\profitability_analysis" method="post" data-oc-toggle="ajax" data-oc-load="{{ action }}" data-oc-target="#main-container">
          <div class="table-responsive">
            <table class="table table-bordered table-hover">
              <thead>
                <tr>
                  <td class="text-center" style="width: 1px;"><input type="checkbox" onclick="$('input[name*=\'selected\']').prop('checked', $(this).prop('checked'));" class="form-check-input" /></td>
                  <td class="text-start">{{ column_name }}{% if sort == 'name' %} <i class="fas fa-sort-{{ order|lower }}"></i>{% endif %}</td>
                  <td class="text-start">{{ column_status }}{% if sort == 'status' %} <i class="fas fa-sort-{{ order|lower }}"></i>{% endif %}</td>
                  <td class="text-start">{{ column_date_added }}{% if sort == 'date_added' %} <i class="fas fa-sort-{{ order|lower }}"></i>{% endif %}</td>
                  <td class="text-end">{{ column_action }}</td>
                </tr>
              </thead>
              <tbody>
                {% if reports\profitability_analysiss %}
                  {% for reports\profitability_analysi in reports\profitability_analysiss %}
                    <tr>
                      <td class="text-center"><input type="checkbox" name="selected[]" value="{{ reports\profitability_analysi.reports\profitability_analysi_id }}" class="form-check-input" /></td>
                      <td class="text-start">{{ reports\profitability_analysi.name }}</td>
                      <td class="text-start">{{ reports\profitability_analysi.status }}</td>
                      <td class="text-start">{{ reports\profitability_analysi.date_added }}</td>
                      <td class="text-end">
                        <div class="d-inline-block dropdown">
                          <button type="button" data-bs-toggle="dropdown" class="btn btn-sm btn-text dropdown-toggle"><i class="fas fa-cog"></i></button>
                          <div class="dropdown-menu">
                            <a href="{{ reports\profitability_analysi.edit }}" class="dropdown-item"><i class="fas fa-pencil-alt fa-fw"></i> {{ button_edit }}</a>
                            <button type="button" data-bs-toggle="modal" data-bs-target="#modal-reports\profitability_analysi{{ reports\profitability_analysi.reports\profitability_analysi_id }}" class="dropdown-item"><i class="fas fa-trash-alt fa-fw"></i> {{ button_delete }}</button>
                          </div>
                        </div>
                      </td>
                    </tr>
                  {% endfor %}
                {% else %}
                  <tr>
                    <td class="text-center" colspan="{{ columns_count + 2 }}">{{ text_no_results }}</td>
                  </tr>
                {% endif %}
              </tbody>
            </table>
          </div>
          <div class="row">
            <div class="col-sm-6 text-start">{{ pagination }}</div>
            <div class="col-sm-6 text-end">{{ results }}</div>
          </div>
        </form>

<script type="text/javascript"><!--
$('#reports\profitability_analysis-form').on('submit', function(e) {
    e.preventDefault();
    
    var element = this;
    
    $.ajax({
        url: $(element).attr('action'),
        type: 'post',
        data: $(element).serialize(),
        dataType: 'json',
        beforeSend: function() {
            $(element).find('button[type=\'submit\']').prop('disabled', true).addClass('loading');
        },
        complete: function() {
            $(element).find('button[type=\'submit\']').prop('disabled', false).removeClass('loading');
        },
        success: function(json) {
            $('.alert-dismissible').remove();
            $(element).find('.is-invalid').removeClass('is-invalid');
            $(element).find('.invalid-feedback').removeClass('d-block');
            
            if (json['error']) {
                if (json['error']['warning']) {
                    $('#alert').prepend('<div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> ' + json['error']['warning'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
                }
                
                for (key in json['error']) {
                    $('#input-' + key.replaceAll('_', '-')).addClass('is-invalid').next().html(json['error'][key]).addClass('d-block');
                }
            }
            
            if (json['success']) {
                $('#alert').prepend('<div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> ' + json['success'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
                
                // Refresh or redirect
                if (json['redirect']) {
                    location = json['redirect'];
                }
            }
        },
        error: function(xhr, ajaxOptions, thrownError) {
            console.error(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
    });
});

// AJAX action for generate
$('body').on('click', '.btn-generate', function(e) {
    e.preventDefault();
    
    var element = this;
    var id = $(element).data('id');
    
    $.ajax({
        url: 'index.php?route=reports\profitability_analysis/generate&user_token={{ user_token }}',
        type: 'post',
        data: {
            'id': id
        },
        dataType: 'json',
        beforeSend: function() {
            $(element).prop('disabled', true);
        },
        complete: function() {
            $(element).prop('disabled', false);
        },
        success: function(json) {
            $('.alert-dismissible').remove();
            
            if (json['error']) {
                $('#alert').prepend('<div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> ' + json['error'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
            }
            
            if (json['success']) {
                $('#alert').prepend('<div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> ' + json['success'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
                
                // Refresh the table
                $('#button-refresh').trigger('click');
            }
        },
        error: function(xhr, ajaxOptions, thrownError) {
            console.error(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
    });
});

// AJAX action for byProduct
$('body').on('click', '.btn-byProduct', function(e) {
    e.preventDefault();
    
    var element = this;
    var id = $(element).data('id');
    
    $.ajax({
        url: 'index.php?route=reports\profitability_analysis/byProduct&user_token={{ user_token }}',
        type: 'post',
        data: {
            'id': id
        },
        dataType: 'json',
        beforeSend: function() {
            $(element).prop('disabled', true);
        },
        complete: function() {
            $(element).prop('disabled', false);
        },
        success: function(json) {
            $('.alert-dismissible').remove();
            
            if (json['error']) {
                $('#alert').prepend('<div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> ' + json['error'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
            }
            
            if (json['success']) {
                $('#alert').prepend('<div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> ' + json['success'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
                
                // Refresh the table
                $('#button-refresh').trigger('click');
            }
        },
        error: function(xhr, ajaxOptions, thrownError) {
            console.error(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
    });
});

// AJAX action for byCustomer
$('body').on('click', '.btn-byCustomer', function(e) {
    e.preventDefault();
    
    var element = this;
    var id = $(element).data('id');
    
    $.ajax({
        url: 'index.php?route=reports\profitability_analysis/byCustomer&user_token={{ user_token }}',
        type: 'post',
        data: {
            'id': id
        },
        dataType: 'json',
        beforeSend: function() {
            $(element).prop('disabled', true);
        },
        complete: function() {
            $(element).prop('disabled', false);
        },
        success: function(json) {
            $('.alert-dismissible').remove();
            
            if (json['error']) {
                $('#alert').prepend('<div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> ' + json['error'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
            }
            
            if (json['success']) {
                $('#alert').prepend('<div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> ' + json['success'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
                
                // Refresh the table
                $('#button-refresh').trigger('click');
            }
        },
        error: function(xhr, ajaxOptions, thrownError) {
            console.error(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
    });
});

// AJAX action for byCategory
$('body').on('click', '.btn-byCategory', function(e) {
    e.preventDefault();
    
    var element = this;
    var id = $(element).data('id');
    
    $.ajax({
        url: 'index.php?route=reports\profitability_analysis/byCategory&user_token={{ user_token }}',
        type: 'post',
        data: {
            'id': id
        },
        dataType: 'json',
        beforeSend: function() {
            $(element).prop('disabled', true);
        },
        complete: function() {
            $(element).prop('disabled', false);
        },
        success: function(json) {
            $('.alert-dismissible').remove();
            
            if (json['error']) {
                $('#alert').prepend('<div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> ' + json['error'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
            }
            
            if (json['success']) {
                $('#alert').prepend('<div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> ' + json['success'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
                
                // Refresh the table
                $('#button-refresh').trigger('click');
            }
        },
        error: function(xhr, ajaxOptions, thrownError) {
            console.error(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
    });
});

// AJAX action for byPeriod
$('body').on('click', '.btn-byPeriod', function(e) {
    e.preventDefault();
    
    var element = this;
    var id = $(element).data('id');
    
    $.ajax({
        url: 'index.php?route=reports\profitability_analysis/byPeriod&user_token={{ user_token }}',
        type: 'post',
        data: {
            'id': id
        },
        dataType: 'json',
        beforeSend: function() {
            $(element).prop('disabled', true);
        },
        complete: function() {
            $(element).prop('disabled', false);
        },
        success: function(json) {
            $('.alert-dismissible').remove();
            
            if (json['error']) {
                $('#alert').prepend('<div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> ' + json['error'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
            }
            
            if (json['success']) {
                $('#alert').prepend('<div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> ' + json['success'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
                
                // Refresh the table
                $('#button-refresh').trigger('click');
            }
        },
        error: function(xhr, ajaxOptions, thrownError) {
            console.error(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
    });
});

// AJAX action for exportPdf
$('body').on('click', '.btn-exportPdf', function(e) {
    e.preventDefault();
    
    var element = this;
    var id = $(element).data('id');
    
    $.ajax({
        url: 'index.php?route=reports\profitability_analysis/exportPdf&user_token={{ user_token }}',
        type: 'post',
        data: {
            'id': id
        },
        dataType: 'json',
        beforeSend: function() {
            $(element).prop('disabled', true);
        },
        complete: function() {
            $(element).prop('disabled', false);
        },
        success: function(json) {
            $('.alert-dismissible').remove();
            
            if (json['error']) {
                $('#alert').prepend('<div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> ' + json['error'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
            }
            
            if (json['success']) {
                $('#alert').prepend('<div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> ' + json['success'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
                
                // Refresh the table
                $('#button-refresh').trigger('click');
            }
        },
        error: function(xhr, ajaxOptions, thrownError) {
            console.error(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
    });
});

// AJAX action for exportExcel
$('body').on('click', '.btn-exportExcel', function(e) {
    e.preventDefault();
    
    var element = this;
    var id = $(element).data('id');
    
    $.ajax({
        url: 'index.php?route=reports\profitability_analysis/exportExcel&user_token={{ user_token }}',
        type: 'post',
        data: {
            'id': id
        },
        dataType: 'json',
        beforeSend: function() {
            $(element).prop('disabled', true);
        },
        complete: function() {
            $(element).prop('disabled', false);
        },
        success: function(json) {
            $('.alert-dismissible').remove();
            
            if (json['error']) {
                $('#alert').prepend('<div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> ' + json['error'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
            }
            
            if (json['success']) {
                $('#alert').prepend('<div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> ' + json['success'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
                
                // Refresh the table
                $('#button-refresh').trigger('click');
            }
        },
        error: function(xhr, ajaxOptions, thrownError) {
            console.error(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
    });
});
//--></script>
      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}