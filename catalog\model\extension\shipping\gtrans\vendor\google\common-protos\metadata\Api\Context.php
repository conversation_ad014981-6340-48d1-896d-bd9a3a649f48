<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/context.proto

namespace GPBMetadata\Google\Api;

class Context
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(hex2bin(
            "0a97020a18676f6f676c652f6170692f636f6e746578742e70726f746f12" .
            "0a676f6f676c652e61706922310a07436f6e7465787412260a0572756c65" .
            "7318012003280b32172e676f6f676c652e6170692e436f6e746578745275" .
            "6c6522440a0b436f6e7465787452756c6512100a0873656c6563746f7218" .
            "012001280912110a0972657175657374656418022003280912100a087072" .
            "6f7669646564180320032809426e0a0e636f6d2e676f6f676c652e617069" .
            "420c436f6e7465787450726f746f50015a45676f6f676c652e676f6c616e" .
            "672e6f72672f67656e70726f746f2f676f6f676c65617069732f6170692f" .
            "73657276696365636f6e6669673b73657276696365636f6e666967a20204" .
            "47415049620670726f746f33"
        ), true);

        static::$is_initialized = true;
    }
}

