<?php
/**
 * ═══════════════════════════════════════════════════════════════════════════════
 * AYM ERP - لوحة المعلومات التنفيذية المتكاملة للشركات التجارية
 * ═══════════════════════════════════════════════════════════════════════════════
 * 
 * @version 2.0.0 - مطور خصيصاً للشركات التجارية المتكاملة
 * @description لوحة معلومات ذكية تجمع بين التجارة الإلكترونية ونقاط البيع والفروع
 * والمناديب والموافقات في واجهة موحدة وذكية
 * 
 * الميزات الفريدة:
 * - تكامل كامل بين المتجر الإلكتروني ونقاط البيع
 * - مراقبة الفروع والمناديب في الوقت الفعلي
 * - نظام موافقات ذكي مع تنبيهات فورية
 * - تحليلات متقدمة للمنتجات والعملاء
 * - مؤشرات أداء مخصصة للتجارة المتكاملة
 */

class ControllerCommonDashboardNew extends Controller {
    
    public function index() {
        // التحقق من الأمان والصلاحيات
        $this->validateAccess();
        
        // تحميل اللغة والموارد
        $this->load->language('common/dashboard');
        $this->load->model('common/dashboard_new');
        
        // إعداد البيانات الأساسية
        $data = $this->initializeBasicData();
        
        // بناء لوحة المعلومات حسب نوع المستخدم
        $user_role = $this->getUserRole();
        
        switch($user_role) {
            case 'executive':
                $data = $this->buildExecutiveDashboard($data);
                break;
            case 'sales_manager':
                $data = $this->buildSalesManagerDashboard($data);
                break;
            case 'inventory_manager':
                $data = $this->buildInventoryManagerDashboard($data);
                break;
            case 'branch_manager':
                $data = $this->buildBranchManagerDashboard($data);
                break;
            case 'sales_rep':
                $data = $this->buildSalesRepDashboard($data);
                break;
            default:
                $data = $this->buildGeneralDashboard($data);
        }
        
        // إضافة البيانات المشتركة
        $data = $this->addSharedComponents($data);
        
        // تحديد القالب المناسب
        $data['template'] = $this->getTemplate($user_role);
        
        // إرسال البيانات للعرض
        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');
        
        $this->response->setOutput($this->load->view('common/dashboard_new', $data));
    }
    
    /**
     * لوحة المعلومات التنفيذية - للإدارة العليا
     * تركز على المؤشرات الاستراتيجية والنظرة الشاملة
     */
    private function buildExecutiveDashboard($data) {
        // 1. المؤشرات المالية الرئيسية
        $data['financial_kpis'] = $this->getFinancialKPIs();
        
        // 2. أداء القنوات (متجر إلكتروني + فروع + مناديب)
        $data['channel_performance'] = $this->getChannelPerformance();
        
        // 3. نظرة عامة على المخزون والمنتجات
        $data['inventory_overview'] = $this->getInventoryOverview();
        
        // 4. حالة الموافقات والمهام المعلقة
        $data['pending_approvals'] = $this->getPendingApprovals();
        
        // 5. تحليل العملاء والأسواق
        $data['customer_analytics'] = $this->getCustomerAnalytics();
        
        // 6. التنبؤات والتوقعات
        $data['forecasts'] = $this->getBusinessForecasts();
        
        return $data;
    }
    
    /**
     * لوحة مدير المبيعات - تركز على الأداء التجاري
     */
    private function buildSalesManagerDashboard($data) {
        // 1. أداء المبيعات اليومي/الأسبوعي/الشهري
        $data['sales_performance'] = $this->getSalesPerformance();
        
        // 2. أداء المناديب والفروع
        $data['team_performance'] = $this->getTeamPerformance();
        
        // 3. تحليل المنتجات الأكثر مبيعاً
        $data['top_products'] = $this->getTopProducts();
        
        // 4. حالة العملاء والفرص
        $data['customer_pipeline'] = $this->getCustomerPipeline();
        
        // 5. مقارنة الأهداف مع الإنجاز
        $data['targets_vs_actual'] = $this->getTargetsVsActual();
        
        return $data;
    }
    
    /**
     * لوحة مدير المخزون - تركز على إدارة المخزون
     */
    private function buildInventoryManagerDashboard($data) {
        // 1. حالة المخزون الحالية
        $data['stock_status'] = $this->getStockStatus();
        
        // 2. تنبيهات المخزون المنخفض
        $data['low_stock_alerts'] = $this->getLowStockAlerts();
        
        // 3. حركة المخزون والتحويلات
        $data['inventory_movements'] = $this->getInventoryMovements();
        
        // 4. تحليل ABC للمنتجات
        $data['abc_analysis'] = $this->getABCAnalysis();
        
        // 5. طلبات الشراء المعلقة
        $data['pending_purchases'] = $this->getPendingPurchases();
        
        return $data;
    }
    
    /**
     * لوحة مدير الفرع - تركز على عمليات الفرع
     */
    private function buildBranchManagerDashboard($data) {
        $branch_id = $this->getBranchId();
        
        // 1. أداء الفرع اليومي
        $data['branch_performance'] = $this->getBranchPerformance($branch_id);
        
        // 2. حالة نقاط البيع والكاشيرين
        $data['pos_status'] = $this->getPOSStatus($branch_id);
        
        // 3. مخزون الفرع
        $data['branch_inventory'] = $this->getBranchInventory($branch_id);
        
        // 4. أداء الموظفين
        $data['staff_performance'] = $this->getStaffPerformance($branch_id);
        
        return $data;
    }
    
    /**
     * لوحة المندوب - تركز على المهام والعملاء
     */
    private function buildSalesRepDashboard($data) {
        $user_id = $this->user->getId();
        
        // 1. مهام اليوم والأسبوع
        $data['my_tasks'] = $this->getMyTasks($user_id);
        
        // 2. عملائي والفرص
        $data['my_customers'] = $this->getMyCustomers($user_id);
        
        // 3. أدائي الشخصي
        $data['my_performance'] = $this->getMyPerformance($user_id);
        
        // 4. الطلبات والعروض
        $data['my_orders'] = $this->getMyOrders($user_id);
        
        return $data;
    }
    
    /**
     * الحصول على المؤشرات المالية الرئيسية
     */
    private function getFinancialKPIs() {
        return [
            'today' => [
                'revenue' => $this->model_common_dashboard_new->getTodayRevenue(),
                'orders' => $this->model_common_dashboard_new->getTodayOrders(),
                'customers' => $this->model_common_dashboard_new->getTodayCustomers(),
                'profit_margin' => $this->model_common_dashboard_new->getTodayProfitMargin()
            ],
            'month' => [
                'revenue' => $this->model_common_dashboard_new->getMonthRevenue(),
                'growth' => $this->model_common_dashboard_new->getMonthGrowth(),
                'target_achievement' => $this->model_common_dashboard_new->getTargetAchievement()
            ],
            'trends' => $this->model_common_dashboard_new->getRevenueTrends(30)
        ];
    }
    
    /**
     * أداء القنوات المختلفة (متجر إلكتروني، فروع، مناديب)
     */
    private function getChannelPerformance() {
        return [
            'online_store' => [
                'revenue' => $this->model_common_dashboard_new->getOnlineRevenue(),
                'orders' => $this->model_common_dashboard_new->getOnlineOrders(),
                'conversion_rate' => $this->model_common_dashboard_new->getConversionRate(),
                'abandoned_carts' => $this->model_common_dashboard_new->getAbandonedCarts()
            ],
            'pos_branches' => [
                'revenue' => $this->model_common_dashboard_new->getPOSRevenue(),
                'transactions' => $this->model_common_dashboard_new->getPOSTransactions(),
                'active_terminals' => $this->model_common_dashboard_new->getActiveTerminals(),
                'branch_comparison' => $this->model_common_dashboard_new->getBranchComparison()
            ],
            'sales_reps' => [
                'revenue' => $this->model_common_dashboard_new->getSalesRepRevenue(),
                'orders' => $this->model_common_dashboard_new->getSalesRepOrders(),
                'active_reps' => $this->model_common_dashboard_new->getActiveSalesReps(),
                'top_performers' => $this->model_common_dashboard_new->getTopPerformers()
            ]
        ];
    }
    
    /**
     * نظرة عامة على المخزون
     */
    private function getInventoryOverview() {
        return [
            'total_value' => $this->model_common_dashboard_new->getTotalInventoryValue(),
            'low_stock_count' => $this->model_common_dashboard_new->getLowStockCount(),
            'out_of_stock_count' => $this->model_common_dashboard_new->getOutOfStockCount(),
            'fast_moving' => $this->model_common_dashboard_new->getFastMovingProducts(5),
            'slow_moving' => $this->model_common_dashboard_new->getSlowMovingProducts(5),
            'expiring_soon' => $this->model_common_dashboard_new->getExpiringSoonProducts(10)
        ];
    }
    
    /**
     * الموافقات المعلقة
     */
    private function getPendingApprovals() {
        return [
            'purchase_orders' => $this->model_common_dashboard_new->getPendingPurchaseOrders(),
            'expense_requests' => $this->model_common_dashboard_new->getPendingExpenseRequests(),
            'discount_requests' => $this->model_common_dashboard_new->getPendingDiscountRequests(),
            'customer_credit' => $this->model_common_dashboard_new->getPendingCreditRequests(),
            'workflow_tasks' => $this->model_common_dashboard_new->getPendingWorkflowTasks()
        ];
    }
    
    /**
     * تحليل العملاء
     */
    private function getCustomerAnalytics() {
        return [
            'total_customers' => $this->model_common_dashboard_new->getTotalCustomers(),
            'new_customers' => $this->model_common_dashboard_new->getNewCustomers(30),
            'customer_lifetime_value' => $this->model_common_dashboard_new->getCustomerLifetimeValue(),
            'customer_segments' => $this->model_common_dashboard_new->getCustomerSegments(),
            'top_customers' => $this->model_common_dashboard_new->getTopCustomers(10),
            'customer_satisfaction' => $this->model_common_dashboard_new->getCustomerSatisfaction()
        ];
    }
    
    /**
     * التحقق من الوصول والأمان
     */
    private function validateAccess() {
        if (!$this->user->isLogged() || !isset($this->session->data['user_token']) || 
            !isset($this->request->get['user_token']) || 
            ($this->request->get['user_token'] != $this->session->data['user_token'])) {
            $this->response->redirect($this->url->link('common/login', '', true));
        }
    }
    
    /**
     * تحديد دور المستخدم
     */
    private function getUserRole() {
        $user_group_id = $this->user->getGroupId();
        
        // تحديد الدور بناءً على مجموعة المستخدم
        $role_mapping = [
            1 => 'executive',      // الإدارة العليا
            2 => 'sales_manager',  // مدير المبيعات
            3 => 'inventory_manager', // مدير المخزون
            4 => 'branch_manager', // مدير الفرع
            5 => 'sales_rep',      // مندوب مبيعات
            6 => 'cashier',        // كاشير
            7 => 'accountant'      // محاسب
        ];
        
        return $role_mapping[$user_group_id] ?? 'general';
    }
    
    /**
     * الحصول على معرف الفرع للمستخدم
     */
    private function getBranchId() {
        // يمكن الحصول عليه من بيانات المستخدم أو الجلسة
        return $this->user->getBranchId() ?? 1;
    }
    
    /**
     * تهيئة البيانات الأساسية
     */
    private function initializeBasicData() {
        $this->document->setTitle($this->language->get('heading_title'));
        
        return [
            'breadcrumbs' => [
                [
                    'text' => $this->language->get('text_home'),
                    'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
                ]
            ],
            'user_token' => $this->session->data['user_token'],
            'user_name' => $this->user->getUserName(),
            'user_role' => $this->getUserRole(),
            'current_time' => date('Y-m-d H:i:s'),
            'timezone' => $this->config->get('config_timezone') ?? 'UTC'
        ];
    }
    
    /**
     * إضافة المكونات المشتركة
     */
    private function addSharedComponents($data) {
        // الإشعارات العاجلة
        $data['urgent_notifications'] = $this->getUrgentNotifications();
        
        // الطقس (إذا كان متاحاً)
        $data['weather'] = $this->getWeatherInfo();
        
        // الأخبار الداخلية
        $data['company_news'] = $this->getCompanyNews();
        
        // الاختصارات السريعة
        $data['quick_actions'] = $this->getQuickActions();
        
        return $data;
    }
    
    /**
     * الحصول على الإشعارات العاجلة
     */
    private function getUrgentNotifications() {
        $this->load->model('communication/unified_notification');
        return $this->model_communication_unified_notification->getUrgentNotifications($this->user->getId(), 5);
    }
    
    /**
     * الحصول على الاختصارات السريعة حسب دور المستخدم
     */
    private function getQuickActions() {
        $user_role = $this->getUserRole();
        $user_token = $this->session->data['user_token'];
        
        $actions = [];
        
        switch($user_role) {
            case 'executive':
                $actions = [
                    ['name' => 'تقرير المبيعات', 'icon' => 'fa-chart-line', 'url' => $this->url->link('report/sales', 'user_token=' . $user_token, true)],
                    ['name' => 'تقرير المخزون', 'icon' => 'fa-boxes', 'url' => $this->url->link('report/inventory', 'user_token=' . $user_token, true)],
                    ['name' => 'الموافقات المعلقة', 'icon' => 'fa-check-circle', 'url' => $this->url->link('workflow/approvals', 'user_token=' . $user_token, true)]
                ];
                break;
                
            case 'sales_manager':
                $actions = [
                    ['name' => 'طلب جديد', 'icon' => 'fa-plus-circle', 'url' => $this->url->link('sale/order/add', 'user_token=' . $user_token, true)],
                    ['name' => 'عرض سعر', 'icon' => 'fa-file-text', 'url' => $this->url->link('sale/quote/add', 'user_token=' . $user_token, true)],
                    ['name' => 'عميل جديد', 'icon' => 'fa-user-plus', 'url' => $this->url->link('customer/customer/add', 'user_token=' . $user_token, true)]
                ];
                break;
                
            case 'inventory_manager':
                $actions = [
                    ['name' => 'تعديل مخزون', 'icon' => 'fa-edit', 'url' => $this->url->link('inventory/adjustment/add', 'user_token=' . $user_token, true)],
                    ['name' => 'طلب شراء', 'icon' => 'fa-shopping-cart', 'url' => $this->url->link('purchase/order/add', 'user_token=' . $user_token, true)],
                    ['name' => 'جرد مخزون', 'icon' => 'fa-clipboard-list', 'url' => $this->url->link('inventory/count/add', 'user_token=' . $user_token, true)]
                ];
                break;
        }
        
        return $actions;
    }
    
    /**
     * تحديد القالب المناسب
     */
    private function getTemplate($user_role) {
        $templates = [
            'executive' => 'dashboard_executive',
            'sales_manager' => 'dashboard_sales',
            'inventory_manager' => 'dashboard_inventory',
            'branch_manager' => 'dashboard_branch',
            'sales_rep' => 'dashboard_rep'
        ];
        
        return $templates[$user_role] ?? 'dashboard_general';
    }
    
    /**
     * API للحصول على البيانات المحدثة (AJAX)
     */
    public function getData() {
        $this->validateAccess();
        
        $type = $this->request->get['type'] ?? '';
        $data = [];
        
        switch($type) {
            case 'financial_kpis':
                $data = $this->getFinancialKPIs();
                break;
            case 'channel_performance':
                $data = $this->getChannelPerformance();
                break;
            case 'inventory_overview':
                $data = $this->getInventoryOverview();
                break;
            case 'pending_approvals':
                $data = $this->getPendingApprovals();
                break;
            default:
                $data = ['error' => 'Invalid data type'];
        }
        
        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($data));
    }
}