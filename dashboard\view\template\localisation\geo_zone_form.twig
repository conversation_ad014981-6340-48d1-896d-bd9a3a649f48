{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="submit" form="form-geo-zone" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fa fa-save"></i></button>
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a></div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid"> {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pencil"></i> {{ text_form }}</h3>
      </div>
      <div class="panel-body">
        <form action="{{ action }}" method="post" enctype="multipart/form-data" id="form-geo-zone" class="form-horizontal">
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-name">{{ entry_name }}</label>
            <div class="col-sm-10">
              <input type="text" name="name" value="{{ name }}" placeholder="{{ entry_name }}" id="input-name" class="form-control" />
              {% if error_name %}
              <div class="text-danger">{{ error_name }}</div>
              {% endif %} </div>
          </div>
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-description">{{ entry_description }}</label>
            <div class="col-sm-10">
              <input type="text" name="description" value="{{ description }}" placeholder="{{ entry_description }}" id="input-description" class="form-control" />
              {% if error_description %}
              <div class="text-danger">{{ error_description }}</div>
              {% endif %} </div>
          </div>
          <fieldset>
            <legend>{{ text_geo_zone }}</legend>
            <table id="zone-to-geo-zone" class="table table-striped table-bordered table-hover">
              <thead>
                <tr>
                  <td class="text-left">{{ entry_country }}</td>
                  <td class="text-left">{{ entry_zone }}</td>
                  <td></td>
                </tr>
              </thead>
              <tbody>
              
              {% set zone_to_geo_zone_row = 0 %}
              {% for zone_to_geo_zone in zone_to_geo_zones %}
              <tr id="zone-to-geo-zone-row{{ zone_to_geo_zone_row }}">
                <td class="text-left"><select name="zone_to_geo_zone[{{ zone_to_geo_zone_row }}][country_id]" class="form-control" data-index="{{ zone_to_geo_zone_row }}" data-zone-id="{{ zone_to_geo_zone.zone_id }}">                    
                    {% for country in countries %}
                    {% if country.country_id == zone_to_geo_zone.country_id %}                    
                    <option value="{{ country.country_id }}" selected="selected">{{ country.name }}</option>                    
                    {% else %}                    
                    <option value="{{ country.country_id }}">{{ country.name }}</option>                    
                    {% endif %}
                    {% endfor %}                  
                  </select></td>
                <td class="text-left"><select name="zone_to_geo_zone[{{ zone_to_geo_zone_row }}][zone_id]" class="form-control">
                    <option value="0">{{ text_all_zones }}</option>
                    {% for zone_id,zone in zones[zone_to_geo_zone.country_id] %}
                    {% if zone_id == zone_to_geo_zone.zone_id %}
                    <option value="{{ zone_id }}" selected="selected">{{ zone }}</option>
                    {% else %}
                    <option value="{{ zone_id }}">{{ zone }}</option>
                    {% endif %}
                    {% endfor %}
                  </select></td>
                <td class="text-left"><button type="button" onclick="$('#zone-to-geo-zone-row{{ zone_to_geo_zone_row }}').remove();" data-toggle="tooltip" title="{{ button_remove }}" class="btn btn-danger"><i class="fa fa-minus-circle"></i></button></td>
              </tr>
              {% set zone_to_geo_zone_row = zone_to_geo_zone_row + 1 %}
              {% endfor %}
                </tbody>
              
              <tfoot>
                <tr>
                  <td colspan="2"></td>
                  <td class="text-left"><button type="button" id="button-geo-zone" data-toggle="tooltip" title="{{ button_geo_zone_add }}" class="btn btn-primary"><i class="fa fa-plus-circle"></i></button></td>
                </tr>
              </tfoot>
	      
            </table>
          </fieldset>
        </form>
      </div>
    </div>
  </div>
  <script type="text/javascript"><!--
var zone_to_geo_zone_row = {{ zone_to_geo_zone_row }};

$('#button-geo-zone').on('click', function() {
	html  = '<tr id="zone-to-geo-zone-row' + zone_to_geo_zone_row + '">';
	html += '  <td class="text-left"><select name="zone_to_geo_zone[' + zone_to_geo_zone_row + '][country_id]" class="form-control" data-index="' + zone_to_geo_zone_row + '">';
	{% for country in countries %}
	html += '    <option value="{{ country.country_id }}">{{ country.name|escape('js') }}</option>';
	{% endfor %}   
	html += '</select></td>';
	html += '  <td class="text-left"><select name="zone_to_geo_zone[' + zone_to_geo_zone_row + '][zone_id]" class="form-control"><option value="0">{{ text_all_zones }}</option></select></td>';
	html += '  <td class="text-left"><button type="button" onclick="$(\'#zone-to-geo-zone-row' + zone_to_geo_zone_row + '\').remove();" data-toggle="tooltip" title="{{ button_remove }}" class="btn btn-danger"><i class="fa fa-minus-circle"></i></button></td>';
	html += '</tr>';
	
	$('#zone-to-geo-zone tbody').append(html);
	
	$('zone_to_geo_zone[' + zone_to_geo_zone_row + '][country_id]').trigger();
			
	zone_to_geo_zone_row++;
});

$('#zone-to-geo-zone').on('change', 'select[name$=\'[country_id]\']', function() {
	var element = this;
	
	if (element.value) { 
		$.ajax({
			url: 'index.php?route=localisation/country/country&user_token={{ user_token }}&country_id=' + element.value,
			dataType: 'json',
			beforeSend: function() {
				$(element).prop('disabled', true);
				$('button[form=\'form-geo-zone\']').prop('disabled', true);
			},
			complete: function() {
				$(element).prop('disabled', false);
				$('button[form=\'form-geo-zone\']').prop('disabled', false);
			},
			success: function(json) {
				html = '<option value="0">{{ text_all_zones }}</option>';
				
				if (json['zone'] && json['zone'] != '') {	
					for (i = 0; i < json['zone'].length; i++) {
						html += '<option value="' + json['zone'][i]['zone_id'] + '"';
	
						if (json['zone'][i]['zone_id'] == $(element).attr('data-zone-id')) {
							html += ' selected="selected"';
						}
	
						html += '>' + json['zone'][i]['name'] + '</option>';
					}
				}
	
				$('select[name=\'zone_to_geo_zone[' + $(element).attr('data-index') + '][zone_id]\']').html(html);
				
				$('select[name=\'zone_to_geo_zone[' + $(element).attr('data-index') + '][zone_id]\']').prop('disabled', false);
				
				$('select[name$=\'[country_id]\']:disabled:first').trigger('change');
			},
			error: function(xhr, ajaxOptions, thrownError) {
				alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
			}
		});
	}
});

//--></script></div>
{{ footer }}
