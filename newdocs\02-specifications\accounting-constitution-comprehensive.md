# الدستور المحاسبي الشامل لنظام AYM ERP
**التاريخ:** 19/7/2025  
**الإصدار:** 2.0 Enterprise Grade  
**الحالة:** ✅ مطبق ومفعل  
**المطابقة:** المعايير المحاسبية المصرية + IFRS

---

## 🏛️ **المبادئ الأساسية للدستور المحاسبي**

### 1. **مبدأ القيد المزدوج الإلزامي**
```
كل عملية = مدين + دائن (متساويان)
لا توجد عملية بدون قيد محاسبي
كل قيد يجب أن يكون متوازن
```

### 2. **مبدأ التوثيق الشامل**
```
كل قيد له مستند مرجعي
كل قيد له تاريخ وتوقيت دقيق
كل قيد له مستخدم مسؤول
كل قيد له وصف تفصيلي
```

### 3. **مبدأ عدم القابلية للتعديل**
```
القيود المعتمدة لا تُعدل - تُعكس فقط
كل تعديل يتطلب قيد عكسي + قيد جديد
سجل كامل لكل التغييرات
موافقات متعددة المستويات
```

---

## 📊 **هيكل الحسابات المعياري**

### 🏦 **المجموعة الأولى: الأصول (1000-1999)**

#### **الأصول الثابتة (1100-1199)**
- `1101` - أراضي
- `1102` - مباني ومنشآت
- `1103` - آلات ومعدات
- `1104` - أثاث ومفروشات
- `1105` - وسائل نقل
- `1106` - أجهزة كمبيوتر
- `1107` - مجمع إهلاك الأصول الثابتة (-)

#### **الأصول المتداولة (1200-1299)**
- `1201` - النقدية بالصندوق
- `1202` - النقدية بالبنوك
- `1203` - العملاء والأوراق التجارية
- `1204` - المخزون - مواد خام
- `1205` - المخزون - منتجات تامة
- `1206` - المخزون - منتجات تحت التشغيل
- `1207` - المصروفات المدفوعة مقدماً
- `1208` - العهد والسلف

### 🏢 **المجموعة الثانية: الخصوم (2000-2999)**

#### **الخصوم طويلة الأجل (2100-2199)**
- `2101` - قروض طويلة الأجل
- `2102` - سندات مستحقة الدفع
- `2103` - التزامات التأمينات الاجتماعية

#### **الخصوم قصيرة الأجل (2200-2299)**
- `2201` - الموردون والأوراق التجارية
- `2202` - قروض قصيرة الأجل
- `2203` - مصروفات مستحقة الدفع
- `2204` - ضرائب مستحقة الدفع
- `2205` - أرباح موزعة مستحقة الدفع
- `2206` - إيرادات محصلة مقدماً

### 💰 **المجموعة الثالثة: حقوق الملكية (3000-3999)**
- `3001` - رأس المال المدفوع
- `3002` - الاحتياطي القانوني
- `3003` - الاحتياطي الاختياري
- `3004` - الأرباح المحتجزة
- `3005` - أرباح العام الجاري

### 📈 **المجموعة الرابعة: الإيرادات (4000-4999)**

#### **إيرادات التشغيل (4100-4199)**
- `4101` - مبيعات منتجات تامة
- `4102` - مبيعات مواد خام
- `4103` - إيرادات الخدمات
- `4104` - خصومات مكتسبة
- `4105` - مردودات مبيعات (-)

#### **الإيرادات الأخرى (4200-4299)**
- `4201` - إيرادات استثمارات
- `4202` - أرباح بيع أصول ثابتة
- `4203` - إيرادات متنوعة

### 📉 **المجموعة الخامسة: المصروفات (5000-5999)**

#### **تكلفة البضاعة المباعة (5100-5199)**
- `5101` - تكلفة المواد المباشرة
- `5102` - تكلفة العمالة المباشرة
- `5103` - المصروفات الصناعية غير المباشرة
- `5104` - مردودات مشتريات (-)

#### **المصروفات التشغيلية (5200-5299)**
- `5201` - مرتبات وأجور
- `5202` - إيجارات
- `5203` - كهرباء ومياه
- `5204` - صيانة وإصلاحات
- `5205` - مصروفات تسويق
- `5206` - مصروفات إدارية
- `5207` - إهلاك الأصول الثابتة

#### **المصروفات الأخرى (5300-5399)**
- `5301` - فوائد القروض
- `5302` - خسائر بيع أصول ثابتة
- `5303` - مصروفات متنوعة

---

## ⚙️ **آلية عمل النظام المحاسبي**

### 🔄 **دورة المعالجة المحاسبية**

#### **الخطوة 1: تسجيل العملية**
```php
// مثال: عملية بيع
$transaction = [
    'date' => '2025-07-19',
    'reference' => 'INV-2025-001',
    'description' => 'بيع بضاعة للعميل أحمد محمد',
    'entries' => [
        [
            'account_id' => 1203, // العملاء
            'debit' => 1150.00,
            'credit' => 0.00
        ],
        [
            'account_id' => 4101, // مبيعات
            'debit' => 0.00,
            'credit' => 1000.00
        ],
        [
            'account_id' => 2204, // ضريبة القيمة المضافة
            'debit' => 0.00,
            'credit' => 150.00
        ]
    ]
];
```

#### **الخطوة 2: التحقق من التوازن**
```php
function validateBalance($entries) {
    $total_debit = array_sum(array_column($entries, 'debit'));
    $total_credit = array_sum(array_column($entries, 'credit'));
    
    if ($total_debit != $total_credit) {
        throw new Exception('القيد غير متوازن');
    }
    
    return true;
}
```

#### **الخطوة 3: الحفظ مع التدقيق**
```php
function saveTransaction($transaction) {
    DB::beginTransaction();
    
    try {
        // حفظ رأس القيد
        $journal_id = DB::table('cod_journal_entries')->insertGetId([
            'date' => $transaction['date'],
            'reference' => $transaction['reference'],
            'description' => $transaction['description'],
            'user_id' => Auth::user()->id,
            'status' => 'pending'
        ]);
        
        // حفظ تفاصيل القيد
        foreach ($transaction['entries'] as $entry) {
            DB::table('cod_journal_entry_details')->insert([
                'journal_id' => $journal_id,
                'account_id' => $entry['account_id'],
                'debit' => $entry['debit'],
                'credit' => $entry['credit']
            ]);
        }
        
        // تسجيل في سجل التدقيق
        $this->central_service->logActivity(
            'journal_entry_created',
            'accounting',
            'تم إنشاء قيد محاسبي: ' . $transaction['reference'],
            ['journal_id' => $journal_id]
        );
        
        DB::commit();
        return $journal_id;
        
    } catch (Exception $e) {
        DB::rollback();
        throw $e;
    }
}
```

---

## 🏭 **القيود المحاسبية للعمليات المختلفة**

### 📦 **عمليات المخزون**

#### **1. شراء بضاعة**
```
مدين: المخزون                    1000
دائن: الموردون                   1000
الوصف: شراء بضاعة من المورد س
```

#### **2. بيع بضاعة**
```
مدين: العملاء                    1150
دائن: المبيعات                   1000
دائن: ضريبة القيمة المضافة        150
الوصف: بيع بضاعة للعميل ص

مدين: تكلفة البضاعة المباعة       800
دائن: المخزون                    800
الوصف: تكلفة البضاعة المباعة
```

#### **3. تسوية مخزون (زيادة)**
```
مدين: المخزون                    500
دائن: أرباح تسوية المخزون        500
الوصف: تسوية مخزون - زيادة
```

#### **4. تسوية مخزون (نقص)**
```
مدين: خسائر تسوية المخزون        300
دائن: المخزون                    300
الوصف: تسوية مخزون - نقص
```

#### **5. نقل مخزون بين المخازن**
```
مدين: مخزون المخزن الرئيسي       1000
دائن: مخزون مخزن الفرع          1000
الوصف: نقل بضاعة من الفرع للرئيسي
```

### 💰 **العمليات المالية**

#### **1. تحصيل من عميل**
```
مدين: النقدية بالصندوق          1000
دائن: العملاء                   1000
الوصف: تحصيل من العميل أحمد
```

#### **2. دفع لمورد**
```
مدين: الموردون                  1500
دائن: النقدية بالبنك            1500
الوصف: دفع للمورد شركة النور
```

#### **3. صرف مرتبات**
```
مدين: مرتبات وأجور              50000
دائن: النقدية بالبنك            42000
دائن: تأمينات اجتماعية          5000
دائن: ضرائب على الدخل          3000
الوصف: صرف مرتبات شهر يوليو
```

### 🏢 **الأصول الثابتة**

#### **1. شراء أصل ثابت**
```
مدين: آلات ومعدات               100000
دائن: النقدية بالبنك            100000
الوصف: شراء آلة إنتاج جديدة
```

#### **2. إهلاك شهري**
```
مدين: إهلاك الأصول الثابتة      2000
دائن: مجمع إهلاك الأصول        2000
الوصف: إهلاك شهر يوليو
```

---

## 🔒 **نظام الرقابة والتدقيق**

### 📋 **مستويات الموافقة**

#### **المستوى الأول: محاسب**
- قيود يومية عادية (أقل من 10,000 جنيه)
- عمليات مخزون روتينية
- تحصيلات ومدفوعات صغيرة

#### **المستوى الثاني: رئيس الحسابات**
- قيود متوسطة (10,000 - 100,000 جنيه)
- تسويات مخزون
- قيود التسوية الشهرية

#### **المستوى الثالث: المدير المالي**
- قيود كبيرة (أكثر من 100,000 جنيه)
- شراء أصول ثابتة
- قيود إقفال السنة المالية

#### **المستوى الرابع: الإدارة العليا**
- توزيع الأرباح
- تغيير رأس المال
- قرارات استراتيجية مالية

### 🔍 **آليات التدقيق**

#### **1. التدقيق التلقائي**
```php
// فحص توازن القيود
function autoAudit($journal_id) {
    $entries = getJournalEntries($journal_id);
    
    // فحص التوازن
    if (!isBalanced($entries)) {
        flagError('قيد غير متوازن', $journal_id);
    }
    
    // فحص الحسابات المغلقة
    foreach ($entries as $entry) {
        if (isAccountClosed($entry['account_id'])) {
            flagError('حساب مغلق', $journal_id);
        }
    }
    
    // فحص الصلاحيات
    if (!hasPermission($entry['amount'])) {
        flagError('تجاوز صلاحيات', $journal_id);
    }
}
```

#### **2. التدقيق اليدوي**
- مراجعة يومية للقيود الكبيرة
- مراجعة أسبوعية للتسويات
- مراجعة شهرية للميزانيات
- مراجعة سنوية شاملة

### 📊 **التقارير الرقابية**

#### **1. تقرير القيود المعلقة**
- القيود التي تحتاج موافقة
- القيود المرفوضة مع الأسباب
- القيود المتأخرة عن المواعيد

#### **2. تقرير الانحرافات**
- القيود غير المتوازنة
- الحسابات ذات الأرصدة الشاذة
- العمليات المشكوك فيها

#### **3. تقرير الأداء**
- معدل دقة القيود
- سرعة المعالجة
- مستوى الالتزام بالمواعيد

---

## 🎯 **معايير الجودة والأداء**

### ⚡ **معايير الأداء**
- **سرعة المعالجة:** أقل من 3 ثوان لكل قيد
- **دقة البيانات:** 99.9% خالية من الأخطاء
- **التوفر:** 99.5% وقت تشغيل
- **الاستجابة:** أقل من ثانية واحدة للاستعلامات

### 🔐 **معايير الأمان**
- **تشفير البيانات:** AES-256 للبيانات الحساسة
- **النسخ الاحتياطي:** كل 4 ساعات تلقائياً
- **سجل التدقيق:** حفظ دائم لا يُحذف
- **الصلاحيات:** مراجعة شهرية وتحديث

### 📈 **معايير التطوير**
- **التوافق:** المعايير المحاسبية المصرية + IFRS
- **المرونة:** قابلية التخصيص حسب القطاع
- **التكامل:** ربط مع جميع أنظمة الشركة
- **التوسع:** دعم نمو الشركة بلا حدود

---

## 🚀 **الميزات المتقدمة**

### 🤖 **الذكاء الاصطناعي المحاسبي**
- **اقتراح القيود:** تلقائياً حسب نوع العملية
- **كشف الأخطاء:** تحليل ذكي للانحرافات
- **التنبؤ المالي:** توقع التدفقات النقدية
- **التحليل الذكي:** استخراج رؤى من البيانات

### 📱 **التطبيق المحمول**
- **موافقة القيود:** من أي مكان
- **استعلام الأرصدة:** فوري ومحدث
- **التقارير السريعة:** على الهاتف
- **الإشعارات:** تنبيهات فورية

### 🌐 **التكامل الخارجي**
- **البنوك:** ربط مباشر مع كشوف الحساب
- **الضرائب:** تقارير تلقائية لمصلحة الضرائب
- **ETA:** فواتير إلكترونية متوافقة
- **التأمينات:** تقارير شهرية تلقائية

---

## 📋 **خطة التطبيق والتنفيذ**

### 🎯 **المرحلة الأولى: الأساسيات (مكتملة)**
- ✅ إنشاء هيكل الحسابات
- ✅ نظام القيد المزدوج
- ✅ آليات التوازن والتحقق
- ✅ نظام الصلاحيات الأساسي

### 🎯 **المرحلة الثانية: التطوير (جاري)**
- 🔄 تطوير واجهات المستخدم
- 🔄 تحسين نظام التقارير
- 🔄 إضافة الميزات المتقدمة
- 🔄 تطوير التطبيق المحمول

### 🎯 **المرحلة الثالثة: التكامل (قادمة)**
- ⏳ ربط مع الأنظمة الخارجية
- ⏳ تطوير الذكاء الاصطناعي
- ⏳ إضافة التحليلات المتقدمة
- ⏳ تطوير لوحات المعلومات

---

## 🏆 **الخلاصة والتوصيات**

### ✅ **نقاط القوة الحالية**
1. **دستور محاسبي شامل ومتطور**
2. **نظام قيد مزدوج متقن**
3. **رقابة وتدقيق صارمة**
4. **توافق مع المعايير المصرية**
5. **أمان وموثوقية عالية**

### 🎯 **التوصيات للمستقبل**
1. **تطوير الذكاء الاصطناعي** لاقتراح القيود
2. **تحسين واجهات المستخدم** لسهولة أكبر
3. **إضافة المزيد من التقارير** التحليلية
4. **تطوير التطبيق المحمول** للموافقات
5. **تعزيز التكامل** مع الأنظمة الخارجية

---

## 📞 **الدعم والمساعدة**

### 🔧 **الدعم التقني**
- **البريد الإلكتروني:** <EMAIL>
- **الهاتف:** 19999 (على مدار الساعة)
- **الدردشة المباشرة:** متوفرة في النظام

### 📚 **التدريب والتأهيل**
- **دورات تدريبية:** شهرية للمستخدمين الجدد
- **ورش عمل:** متقدمة للمحاسبين
- **مواد تعليمية:** فيديوهات وأدلة مفصلة

---

**تم إعداد هذا الدستور بواسطة:** فريق تطوير AYM ERP  
**تاريخ آخر تحديث:** 19/7/2025  
**الإصدار:** 2.0 Enterprise Grade  
**الحالة:** ✅ مطبق ومفعل في النظام