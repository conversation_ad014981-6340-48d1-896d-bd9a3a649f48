{{ header }}{{ column_left }}
<div class="container-fluid">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="button" data-toggle="tooltip" title="{{ button_settings }}" class="btn btn-default" onclick="location = '{{ settings }}';">
          <i class="fa fa-cog"></i>
        </button>
        <button type="button" data-toggle="tooltip" title="{{ button_refresh }}" class="btn btn-info" onclick="location.reload();">
          <i class="fa fa-refresh"></i>
        </button>
      </div>
      <h1>{{ heading_title }}</h1>
      <ol class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
          <li class="breadcrumb-item"><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ol>
    </div>
  </div>
  
  <div class="container-fluid">
    <!-- Connection Status Card -->
    <div class="row mb-4">
      <div class="col-12">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title">
              <i class="fa fa-plug"></i> {{ text_connection_status }}
            </h3>
            <div class="pull-right">
              <button type="button" class="btn btn-sm btn-primary" onclick="testConnection();">
                <i class="fa fa-wifi"></i> {{ button_test_connection }}
              </button>
            </div>
          </div>
          <div class="panel-body">
            <div class="row">
              <div class="col-md-6">
                <div class="info-box">
                  <span class="info-box-icon {% if eta_connection_status.connected %}bg-success{% else %}bg-danger{% endif %}">
                    <i class="fa {% if eta_connection_status.connected %}fa-check{% else %}fa-times{% endif %}"></i>
                  </span>
                  <div class="info-box-content">
                    <span class="info-box-text">{{ text_connection_status }}</span>
                    <span class="info-box-number">
                      {% if eta_connection_status.connected %}
                        {{ text_connected }}
                      {% else %}
                        {{ text_disconnected }}
                      {% endif %}
                    </span>
                    <span class="info-box-more">{{ eta_connection_status.last_check }}</span>
                  </div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="info-box">
                  <span class="info-box-icon bg-info">
                    <i class="fa-solid fa-server"></i>
                  </span>
                  <div class="info-box-content">
                    <span class="info-box-text">{{ text_eta_integration }}</span>
                    <span class="info-box-number">{{ eta_connection_status.environment|upper }}</span>
                    <span class="info-box-more">{{ eta_connection_status.api_url }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Compliance Dashboard -->
    <div class="row mb-4">
      <div class="col-md-3">
        <div class="info-box">
          <span class="info-box-icon bg-primary">
            <i class="fa-solid fa-file-invoice"></i>
          </span>
          <div class="info-box-content">
            <span class="info-box-text">{{ text_total_invoices }}</span>
            <span class="info-box-number">{{ compliance_dashboard.invoice_stats.total_invoices }}</span>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="info-box">
          <span class="info-box-icon bg-success">
            <i class="fa-solid fa-check-circle"></i>
          </span>
          <div class="info-box-content">
            <span class="info-box-text">{{ text_submitted_invoices }}</span>
            <span class="info-box-number">{{ compliance_dashboard.invoice_stats.submitted_invoices }}</span>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="info-box">
          <span class="info-box-icon bg-warning">
            <i class="fa-solid fa-clock"></i>
          </span>
          <div class="info-box-content">
            <span class="info-box-text">{{ text_pending_invoices }}</span>
            <span class="info-box-number">{{ compliance_dashboard.invoice_stats.pending_invoices }}</span>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="info-box">
          <span class="info-box-icon bg-danger">
            <i class="fa-solid fa-exclamation-triangle"></i>
          </span>
          <div class="info-box-content">
            <span class="info-box-text">{{ text_failed_invoices }}</span>
            <span class="info-box-number">{{ compliance_dashboard.invoice_stats.failed_invoices }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Compliance Score -->
    <div class="row mb-4">
      <div class="col-md-6">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title">
              <i class="fa fa-pie-chart"></i> {{ text_compliance_score }}
            </h3>
          </div>
          <div class="panel-body text-center">
            <div class="progress-circle" data-percentage="{{ compliance_dashboard.compliance_score }}">
              <span class="progress-left">
                <span class="progress-bar"></span>
              </span>
              <span class="progress-right">
                <span class="progress-bar"></span>
              </span>
              <div class="progress-value">{{ compliance_dashboard.compliance_score }}%</div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title">
              <i class="fa fa-tasks"></i> {{ text_recent_activities }}
            </h3>
          </div>
          <div class="panel-body">
            <div class="timeline">
              {% for activity in recent_activities %}
                <div class="timeline-item">
                  <span class="time">{{ activity.created_date }}</span>
                  <h3 class="timeline-header">{{ activity.description }}</h3>
                  <div class="timeline-body">
                    <span class="badge {% if activity.status == 'completed' %}badge-success{% else %}badge-warning{% endif %}">
                      {{ activity.status }}
                    </span>
                  </div>
                </div>
              {% endfor %}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="row mb-4">
      <div class="col-md-4">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title">
              <i class="fa fa-upload"></i> {{ text_invoice_submission }}
            </h3>
          </div>
          <div class="panel-body">
            <form id="invoice-submission-form">
              <div class="form-group">
                <label for="date_start">{{ text_select_period }}</label>
                <div class="row">
                  <div class="col-6">
                    <input type="date" name="date_start" id="date_start" class="form-control" value="{{ 'now'|date('Y-m-01') }}">
                  </div>
                  <div class="col-6">
                    <input type="date" name="date_end" id="date_end" class="form-control" value="{{ 'now'|date('Y-m-t') }}">
                  </div>
                </div>
              </div>
              <div class="form-group">
                <label for="invoice_type">{{ text_invoice_type }}</label>
                <select name="invoice_type" id="invoice_type" class="form-control">
                  <option value="all">{{ text_all_invoices }}</option>
                  <option value="sales">{{ text_sales_invoices }}</option>
                  <option value="purchase">{{ text_purchase_invoices }}</option>
                </select>
              </div>
              <button type="button" class="btn btn-primary btn-block" onclick="submitInvoices();">
                <i class="fa-solid fa-paper-plane"></i> {{ button_submit_invoices }}
              </button>
            </form>
          </div>
        </div>
      </div>
      
      <div class="col-md-4">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title">
              <i class="fa fa-file-text"></i> {{ text_vat_return }}
            </h3>
          </div>
          <div class="panel-body">
            <form id="vat-return-form">
              <div class="form-group">
                <label for="year">{{ text_select_year }}</label>
                <select name="year" id="year" class="form-control">
                  {% for year in range('now'|date('Y') - 2, 'now'|date('Y') + 1) %}
                    <option value="{{ year }}" {% if year == 'now'|date('Y') %}selected{% endif %}>{{ year }}</option>
                  {% endfor %}
                </select>
              </div>
              <div class="form-group">
                <label for="month">{{ text_select_month }}</label>
                <select name="month" id="month" class="form-control">
                  {% for month in range(1, 12) %}
                    <option value="{{ month }}" {% if month == 'now'|date('m') %}selected{% endif %}>
                      {{ month|date('F') }}
                    </option>
                  {% endfor %}
                </select>
              </div>
              <div class="form-group">
                <label for="period_type">{{ text_period_type }}</label>
                <select name="period_type" id="period_type" class="form-control">
                  <option value="monthly">{{ text_monthly }}</option>
                  <option value="quarterly">{{ text_quarterly }}</option>
                </select>
              </div>
              <button type="button" class="btn btn-success btn-block" onclick="submitVATReturn();">
                <i class="fa-solid fa-file-upload"></i> {{ button_submit_vat_return }}
              </button>
            </form>
          </div>
        </div>
      </div>
      
      <div class="col-md-4">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title">
              <i class="fa fa-sync"></i> {{ text_sync_codes }}
            </h3>
          </div>
          <div class="panel-body">
            <p>{{ help_eta_sync_codes }}</p>
            <button type="button" class="btn btn-info btn-block" onclick="syncCodes();">
              <i class="fa fa-download"></i> {{ button_sync_codes }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Pending Submissions -->
    {% if pending_submissions %}
    <div class="row">
      <div class="col-12">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title">
              <i class="fa fa-hourglass-half"></i> {{ text_pending_submissions }}
            </h3>
          </div>
          <div class="panel-body">
            <div class="table-responsive">
              <table class="table table-bordered table-hover">
                <thead>
                  <tr>
                    <th>{{ text_invoice_no }}</th>
                    <th>{{ text_customer_name }}</th>
                    <th>{{ text_total }}</th>
                    <th>{{ text_date_added }}</th>
                    <th>{{ text_status }}</th>
                    <th>{{ text_action }}</th>
                  </tr>
                </thead>
                <tbody>
                  {% for submission in pending_submissions %}
                    <tr>
                      <td>{{ submission.invoice_no }}</td>
                      <td>{{ submission.customer_name }}</td>
                      <td>{{ submission.total }}</td>
                      <td>{{ submission.date_added }}</td>
                      <td>
                        <span class="badge {% if submission.eta_status == 'pending' %}badge-warning{% elseif submission.eta_status == 'failed' %}badge-danger{% else %}badge-secondary{% endif %}">
                          {{ submission.eta_status ?: text_status_not_submitted }}
                        </span>
                      </td>
                      <td>
                        <button type="button" class="btn btn-sm btn-primary" onclick="resubmitInvoice({{ submission.order_id }});">
                          <i class="fa-solid fa-redo"></i> {{ button_resubmit }}
                        </button>
                      </td>
                    </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
    {% endif %}
  </div>
</div>

<script type="text/javascript">
function testConnection() {
    $.ajax({
        url: '{{ test_connection }}',
        type: 'post',
        dataType: 'json',
        beforeSend: function() {
            $('#button-test').button('loading');
        },
        complete: function() {
            $('#button-test').button('reset');
        },
        success: function(json) {
            $('.alert-dismissible').remove();
            
            if (json['error']) {
                $('#content > .container-fluid').prepend('<div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> ' + json['error'] + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
            }
            
            if (json['success']) {
                $('#content > .container-fluid').prepend('<div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> ' + json['success'] + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
                
                setTimeout(function() {
                    location.reload();
                }, 2000);
            }
        },
        error: function(xhr, ajaxOptions, thrownError) {
            console.log(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
    });
}

function submitInvoices() {
    var formData = $('#invoice-submission-form').serialize();
    
    $.ajax({
        url: '{{ submit_invoices }}',
        type: 'post',
        data: formData,
        dataType: 'json',
        beforeSend: function() {
            $('#content > .container-fluid').prepend('<div class="alert alert-info"><i class="fa-solid fa-spinner fa-spin"></i> {{ text_loading_submission }}</div>');
        },
        success: function(json) {
            $('.alert').remove();
            
            if (json['error']) {
                $('#content > .container-fluid').prepend('<div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> ' + json['error'] + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
            }
            
            if (json['success']) {
                $('#content > .container-fluid').prepend('<div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> ' + json['success'] + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
                
                setTimeout(function() {
                    location.reload();
                }, 3000);
            }
        },
        error: function(xhr, ajaxOptions, thrownError) {
            $('.alert').remove();
            $('#content > .container-fluid').prepend('<div class="alert alert-danger"><i class="fa-solid fa-exclamation-circle"></i> Error: ' + thrownError + '</div>');
        }
    });
}

function submitVATReturn() {
    var formData = $('#vat-return-form').serialize();
    
    $.ajax({
        url: '{{ submit_vat_return }}',
        type: 'post',
        data: formData,
        dataType: 'json',
        beforeSend: function() {
            $('#content > .container-fluid').prepend('<div class="alert alert-info"><i class="fa-solid fa-spinner fa-spin"></i> {{ text_loading_submission }}</div>');
        },
        success: function(json) {
            $('.alert').remove();
            
            if (json['error']) {
                $('#content > .container-fluid').prepend('<div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> ' + json['error'] + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
            }
            
            if (json['success']) {
                $('#content > .container-fluid').prepend('<div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> ' + json['success'] + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
                
                setTimeout(function() {
                    location.reload();
                }, 3000);
            }
        },
        error: function(xhr, ajaxOptions, thrownError) {
            $('.alert').remove();
            $('#content > .container-fluid').prepend('<div class="alert alert-danger"><i class="fa-solid fa-exclamation-circle"></i> Error: ' + thrownError + '</div>');
        }
    });
}

function syncCodes() {
    $.ajax({
        url: '{{ sync_codes }}',
        type: 'post',
        dataType: 'json',
        beforeSend: function() {
            $('#content > .container-fluid').prepend('<div class="alert alert-info"><i class="fa-solid fa-spinner fa-spin"></i> {{ text_loading_sync }}</div>');
        },
        success: function(json) {
            $('.alert').remove();
            
            if (json['error']) {
                $('#content > .container-fluid').prepend('<div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> ' + json['error'] + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
            }
            
            if (json['success']) {
                $('#content > .container-fluid').prepend('<div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> ' + json['success'] + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
                
                setTimeout(function() {
                    location.reload();
                }, 2000);
            }
        },
        error: function(xhr, ajaxOptions, thrownError) {
            $('.alert').remove();
            $('#content > .container-fluid').prepend('<div class="alert alert-danger"><i class="fa-solid fa-exclamation-circle"></i> Error: ' + thrownError + '</div>');
        }
    });
}
</script>

{{ footer }}
