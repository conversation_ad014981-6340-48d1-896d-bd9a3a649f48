{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <a href="{{ edit }}" data-toggle="tooltip" title="{{ button_edit }}" class="btn btn-primary"><i class="fa fa-pencil"></i></a>
        <a href="{{ reconcile }}" data-toggle="tooltip" title="{{ button_reconcile }}" class="btn btn-success"><i class="fa fa-balance-scale"></i></a>
        <a href="{{ transfer }}" data-toggle="tooltip" title="{{ button_transfer }}" class="btn btn-warning"><i class="fa fa-exchange"></i></a>
        <a href="{{ analyze }}" data-toggle="tooltip" title="{{ button_analyze }}" class="btn btn-info"><i class="fa fa-line-chart"></i></a>
        <a href="{{ export_excel }}" data-toggle="tooltip" title="{{ button_export_excel }}" class="btn btn-success"><i class="fa fa-file-excel-o"></i></a>
        <a href="{{ print }}" data-toggle="tooltip" title="{{ button_print }}" class="btn btn-default"><i class="fa fa-print"></i></a>
        <a href="{{ back }}" data-toggle="tooltip" title="{{ button_back }}" class="btn btn-default"><i class="fa fa-reply"></i></a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if success %}
    <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    
    <!-- معلومات الحساب الأساسية -->
    <div class="panel panel-primary">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-university"></i> {{ text_account_info }}</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-md-6">
            <table class="table table-striped">
              <tr>
                <td><strong>{{ entry_account_number }}:</strong></td>
                <td>{{ account_number }}</td>
              </tr>
              <tr>
                <td><strong>{{ entry_account_name }}:</strong></td>
                <td>{{ account_name }}</td>
              </tr>
              <tr>
                <td><strong>{{ entry_bank_name }}:</strong></td>
                <td>{{ bank_name_display }}</td>
              </tr>
              <tr>
                <td><strong>{{ entry_branch_name }}:</strong></td>
                <td>{{ branch_name }}</td>
              </tr>
              <tr>
                <td><strong>{{ entry_account_type }}:</strong></td>
                <td>
                  <span class="label label-{% if account_type == 'current' %}primary{% elseif account_type == 'savings' %}success{% elseif account_type == 'credit' %}warning{% else %}info{% endif %}">
                    {{ account_type_display }}
                  </span>
                </td>
              </tr>
            </table>
          </div>
          <div class="col-md-6">
            <table class="table table-striped">
              <tr>
                <td><strong>{{ entry_currency }}:</strong></td>
                <td>{{ currency }}</td>
              </tr>
              <tr>
                <td><strong>{{ entry_swift_code }}:</strong></td>
                <td>{{ swift_code }}</td>
              </tr>
              <tr>
                <td><strong>{{ entry_iban }}:</strong></td>
                <td>{{ iban }}</td>
              </tr>
              <tr>
                <td><strong>{{ column_status }}:</strong></td>
                <td>
                  <span class="label label-{% if status == '1' %}success{% else %}danger{% endif %}">
                    {% if status == '1' %}{{ text_status_active }}{% else %}{{ text_status_inactive }}{% endif %}
                  </span>
                </td>
              </tr>
              <tr>
                <td><strong>{{ text_last_updated }}:</strong></td>
                <td>{{ date_modified }}</td>
              </tr>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- الأرصدة والمعلومات المالية -->
    <div class="panel panel-success">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-money"></i> {{ text_financial_info }}</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-md-3">
            <div class="info-box bg-green">
              <span class="info-box-icon"><i class="fa fa-money"></i></span>
              <div class="info-box-content">
                <span class="info-box-text">{{ text_current_balance }}</span>
                <span class="info-box-number">{{ current_balance }}</span>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="info-box bg-blue">
              <span class="info-box-icon"><i class="fa fa-credit-card"></i></span>
              <div class="info-box-content">
                <span class="info-box-text">{{ text_available_balance }}</span>
                <span class="info-box-number">{{ available_balance }}</span>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="info-box bg-yellow">
              <span class="info-box-icon"><i class="fa fa-line-chart"></i></span>
              <div class="info-box-content">
                <span class="info-box-text">{{ text_average_balance }}</span>
                <span class="info-box-number">{{ average_balance }}</span>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="info-box bg-red">
              <span class="info-box-icon"><i class="fa fa-exclamation-triangle"></i></span>
              <div class="info-box-content">
                <span class="info-box-text">{{ text_minimum_balance }}</span>
                <span class="info-box-number">{{ minimum_balance }}</span>
              </div>
            </div>
          </div>
        </div>
        
        <div class="row">
          <div class="col-md-6">
            <table class="table table-bordered">
              <tr>
                <td><strong>{{ entry_opening_balance }}:</strong></td>
                <td class="text-right">{{ opening_balance }}</td>
              </tr>
              <tr>
                <td><strong>{{ entry_overdraft_limit }}:</strong></td>
                <td class="text-right">{{ overdraft_limit }}</td>
              </tr>
              <tr>
                <td><strong>{{ entry_interest_rate }}:</strong></td>
                <td class="text-right">{{ interest_rate }}%</td>
              </tr>
            </table>
          </div>
          <div class="col-md-6">
            <table class="table table-bordered">
              <tr>
                <td><strong>{{ entry_monthly_charges }}:</strong></td>
                <td class="text-right">{{ monthly_charges }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_total_transactions }}:</strong></td>
                <td class="text-right">{{ total_transactions }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_last_transaction }}:</strong></td>
                <td class="text-right">{{ last_transaction_date }}</td>
              </tr>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- تحليل التدفق النقدي -->
    {% if cash_flow_data %}
    <div class="panel panel-info">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-line-chart"></i> {{ text_cash_flow_analysis }}</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-md-8">
            <canvas id="cash-flow-chart" width="400" height="200"></canvas>
          </div>
          <div class="col-md-4">
            <table class="table table-striped">
              <tr>
                <td>{{ text_cash_inflow }}:</td>
                <td class="text-right text-success">{{ cash_flow_data.inflow }}</td>
              </tr>
              <tr>
                <td>{{ text_cash_outflow }}:</td>
                <td class="text-right text-danger">{{ cash_flow_data.outflow }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_net_cash_flow }}:</strong></td>
                <td class="text-right{% if cash_flow_data.net_flow >= 0 %} text-success{% else %} text-danger{% endif %}">
                  <strong>{{ cash_flow_data.net_flow }}</strong>
                </td>
              </tr>
            </table>
          </div>
        </div>
      </div>
    </div>
    {% endif %}

    <!-- المعاملات الحديثة -->
    {% if recent_transactions %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-list"></i> {{ text_recent_transactions }}</h3>
      </div>
      <div class="panel-body">
        <div class="table-responsive">
          <table class="table table-bordered table-hover">
            <thead>
              <tr>
                <th>{{ text_date }}</th>
                <th>{{ text_description }}</th>
                <th>{{ text_reference }}</th>
                <th class="text-right">{{ text_debit }}</th>
                <th class="text-right">{{ text_credit }}</th>
                <th class="text-right">{{ text_balance }}</th>
              </tr>
            </thead>
            <tbody>
              {% for transaction in recent_transactions %}
              <tr>
                <td>{{ transaction.date }}</td>
                <td>{{ transaction.description }}</td>
                <td>{{ transaction.reference }}</td>
                <td class="text-right{% if transaction.debit > 0 %} text-danger{% endif %}">
                  {% if transaction.debit > 0 %}{{ transaction.debit }}{% endif %}
                </td>
                <td class="text-right{% if transaction.credit > 0 %} text-success{% endif %}">
                  {% if transaction.credit > 0 %}{{ transaction.credit }}{% endif %}
                </td>
                <td class="text-right">{{ transaction.balance }}</td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
        <div class="text-center">
          <a href="{{ view_all_transactions }}" class="btn btn-primary">{{ button_view_all_transactions }}</a>
        </div>
      </div>
    </div>
    {% endif %}

    <!-- معلومات الاتصال -->
    {% if contact_person or phone or email %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-phone"></i> {{ text_contact_info }}</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-md-6">
            {% if contact_person %}
            <p><strong>{{ entry_contact_person }}:</strong> {{ contact_person }}</p>
            {% endif %}
            {% if phone %}
            <p><strong>{{ entry_phone }}:</strong> {{ phone }}</p>
            {% endif %}
          </div>
          <div class="col-md-6">
            {% if email %}
            <p><strong>{{ entry_email }}:</strong> {{ email }}</p>
            {% endif %}
            {% if address %}
            <p><strong>{{ entry_address }}:</strong><br>{{ address }}</p>
            {% endif %}
          </div>
        </div>
      </div>
    </div>
    {% endif %}

    <!-- ملاحظات -->
    {% if notes %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-sticky-note"></i> {{ entry_notes }}</h3>
      </div>
      <div class="panel-body">
        <p>{{ notes }}</p>
      </div>
    </div>
    {% endif %}

    <!-- معلومات الامتثال -->
    <div class="panel panel-success">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-shield"></i> {{ text_compliance_info }}</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-md-3">
            <div class="alert alert-success">
              <i class="fa fa-check"></i> {{ text_cbe_compliant }}
            </div>
          </div>
          <div class="col-md-3">
            <div class="alert alert-info">
              <i class="fa fa-balance-scale"></i> {{ text_banking_law_compliant }}
            </div>
          </div>
          <div class="col-md-3">
            <div class="alert alert-warning">
              <i class="fa fa-user-secret"></i> {{ text_anti_money_laundering }}
            </div>
          </div>
          <div class="col-md-3">
            <div class="alert alert-primary">
              <i class="fa fa-user-check"></i> {{ text_kyc_compliant }}
            </div>
          </div>
        </div>
      </div>
    </div>

  </div>
</div>

<script type="text/javascript"><!--
// Cash Flow Chart
{% if cash_flow_data and cash_flow_data.chart_data %}
var ctx = document.getElementById('cash-flow-chart').getContext('2d');
var cashFlowChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: {{ cash_flow_data.chart_data.labels|json_encode|raw }},
        datasets: [{
            label: '{{ text_balance }}',
            data: {{ cash_flow_data.chart_data.data|json_encode|raw }},
            borderColor: '#3c8dbc',
            backgroundColor: 'rgba(60, 141, 188, 0.1)',
            fill: true
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: false
            }
        }
    }
});
{% endif %}

// Tooltips
$('[data-toggle="tooltip"]').tooltip();

// تحديث البيانات كل 5 دقائق
setInterval(function() {
    location.reload();
}, 300000);
//--></script>

{{ footer }}
