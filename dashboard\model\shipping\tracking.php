<?php
class ModelShippingTracking extends Model {
    
    public function getShipments($data = array()) {
        $sql = "SELECT s.*, c.name as carrier_name, CONCAT(cu.firstname, ' ', cu.lastname) as customer_name 
                FROM " . DB_PREFIX . "shipment_tracking s 
                LEFT JOIN " . DB_PREFIX . "shipping_carrier c ON (s.carrier_id = c.carrier_id) 
                LEFT JOIN " . DB_PREFIX . "customer cu ON (s.customer_id = cu.customer_id)";
        
        $implode = array();
        
        if (!empty($data['filter_tracking_number'])) {
            $implode[] = "s.tracking_number LIKE '%" . $this->db->escape($data['filter_tracking_number']) . "%'";
        }
        
        if (!empty($data['filter_carrier'])) {
            $implode[] = "c.name LIKE '%" . $this->db->escape($data['filter_carrier']) . "%'";
        }
        
        if (!empty($data['filter_status'])) {
            $implode[] = "s.status = '" . $this->db->escape($data['filter_status']) . "'";
        }
        
        if (!empty($data['filter_date_added'])) {
            $implode[] = "DATE(s.date_added) = DATE('" . $this->db->escape($data['filter_date_added']) . "')";
        }
        
        if ($implode) {
            $sql .= " WHERE " . implode(" AND ", $implode);
        }
        
        $sql .= " ORDER BY s.date_added DESC";
        
        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }
            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }
            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }
        
        $query = $this->db->query($sql);
        return $query->rows;
    }
    
    public function getTotalShipments($data = array()) {
        $sql = "SELECT COUNT(*) AS total FROM " . DB_PREFIX . "shipment_tracking s 
                LEFT JOIN " . DB_PREFIX . "shipping_carrier c ON (s.carrier_id = c.carrier_id)";
        
        $implode = array();
        
        if (!empty($data['filter_tracking_number'])) {
            $implode[] = "s.tracking_number LIKE '%" . $this->db->escape($data['filter_tracking_number']) . "%'";
        }
        
        if (!empty($data['filter_carrier'])) {
            $implode[] = "c.name LIKE '%" . $this->db->escape($data['filter_carrier']) . "%'";
        }
        
        if (!empty($data['filter_status'])) {
            $implode[] = "s.status = '" . $this->db->escape($data['filter_status']) . "'";
        }
        
        if (!empty($data['filter_date_added'])) {
            $implode[] = "DATE(s.date_added) = DATE('" . $this->db->escape($data['filter_date_added']) . "')";
        }
        
        if ($implode) {
            $sql .= " WHERE " . implode(" AND ", $implode);
        }
        
        $query = $this->db->query($sql);
        return $query->row['total'];
    }
    
    public function getShipment($shipment_id) {
        $query = $this->db->query("SELECT s.*, c.name as carrier_name, c.code as carrier_code, 
                                   CONCAT(cu.firstname, ' ', cu.lastname) as customer_name,
                                   cu.email as customer_email, cu.telephone as customer_telephone,
                                   CONCAT(ad.firstname, ' ', ad.lastname, ', ', ad.address_1, ', ', ad.city, ', ', ad.postcode) as shipping_address
                                   FROM " . DB_PREFIX . "shipment_tracking s 
                                   LEFT JOIN " . DB_PREFIX . "shipping_carrier c ON (s.carrier_id = c.carrier_id)
                                   LEFT JOIN " . DB_PREFIX . "customer cu ON (s.customer_id = cu.customer_id)
                                   LEFT JOIN " . DB_PREFIX . "address ad ON (s.shipping_address_id = ad.address_id)
                                   WHERE s.shipment_id = '" . (int)$shipment_id . "'");
        return $query->row;
    }
    
    public function getTrackingDetails($shipment_id) {
        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "shipment_tracking_details 
                                   WHERE shipment_id = '" . (int)$shipment_id . "' 
                                   ORDER BY date_added DESC");
        return $query->rows;
    }
    
    public function updateShipmentStatus($shipment_id) {
        $shipment = $this->getShipment($shipment_id);
        
        if (!$shipment) {
            return false;
        }
        
        // محاكاة التحديث من شركة الشحن
        $new_status = $this->simulateCarrierUpdate($shipment['carrier_code'], $shipment['tracking_number']);
        
        if ($new_status && $new_status != $shipment['status']) {
            $this->db->query("UPDATE " . DB_PREFIX . "shipment_tracking SET 
                status = '" . $this->db->escape($new_status) . "',
                last_update = NOW()
                WHERE shipment_id = '" . (int)$shipment_id . "'");
            
            // إضافة تفاصيل التحديث
            $this->addTrackingDetail($shipment_id, $new_status, 'Status updated from carrier');
            
            return true;
        }
        
        return false;
    }
    
    private function simulateCarrierUpdate($carrier_code, $tracking_number) {
        // محاكاة تحديث من شركات الشحن المختلفة
        switch ($carrier_code) {
            case 'aramex':
                return $this->simulateAramexUpdate($tracking_number);
            case 'bosta':
                return $this->simulateBostaUpdate($tracking_number);
            case 'internal':
                return $this->simulateInternalUpdate($tracking_number);
            default:
                return $this->simulateGenericUpdate($tracking_number);
        }
    }
    
    private function simulateAramexUpdate($tracking_number) {
        // محاكاة تحديث أرامكس
        $statuses = array('pending', 'in_transit', 'out_for_delivery', 'delivered');
        $current_time = time();
        $tracking_hash = crc32($tracking_number);
        
        // تحديد الحالة بناءً على الوقت ورقم التتبع
        $status_index = ($current_time + $tracking_hash) % count($statuses);
        return $statuses[$status_index];
    }
    
    private function simulateBostaUpdate($tracking_number) {
        // محاكاة تحديث بوسطة
        $statuses = array('pending', 'in_transit', 'out_for_delivery', 'delivered');
        $current_time = time();
        $tracking_hash = crc32($tracking_number);
        
        $status_index = ($current_time + $tracking_hash) % count($statuses);
        return $statuses[$status_index];
    }
    
    private function simulateInternalUpdate($tracking_number) {
        // محاكاة تحديث المناديب الداخليين
        $statuses = array('pending', 'in_transit', 'out_for_delivery', 'delivered');
        $current_time = time();
        $tracking_hash = crc32($tracking_number);
        
        $status_index = ($current_time + $tracking_hash) % count($statuses);
        return $statuses[$status_index];
    }
    
    private function simulateGenericUpdate($tracking_number) {
        // محاكاة تحديث عام
        $statuses = array('pending', 'in_transit', 'out_for_delivery', 'delivered');
        $current_time = time();
        $tracking_hash = crc32($tracking_number);
        
        $status_index = ($current_time + $tracking_hash) % count($statuses);
        return $statuses[$status_index];
    }
    
    private function addTrackingDetail($shipment_id, $status, $description) {
        $this->db->query("INSERT INTO " . DB_PREFIX . "shipment_tracking_details SET 
            shipment_id = '" . (int)$shipment_id . "',
            status = '" . $this->db->escape($status) . "',
            description = '" . $this->db->escape($description) . "',
            location = '',
            date_added = NOW()");
    }
    
    public function getTrackingStatistics() {
        $stats = array();
        
        // إحصائيات الحالات
        $query = $this->db->query("SELECT status, COUNT(*) as count 
                                   FROM " . DB_PREFIX . "shipment_tracking 
                                   GROUP BY status");
        $stats['status_counts'] = $query->rows;
        
        // إحصائيات شركات الشحن
        $query = $this->db->query("SELECT c.name, COUNT(*) as count 
                                   FROM " . DB_PREFIX . "shipment_tracking s 
                                   LEFT JOIN " . DB_PREFIX . "shipping_carrier c ON (s.carrier_id = c.carrier_id)
                                   GROUP BY s.carrier_id");
        $stats['carrier_counts'] = $query->rows;
        
        // إحصائيات اليومية
        $query = $this->db->query("SELECT DATE(date_added) as date, COUNT(*) as count 
                                   FROM " . DB_PREFIX . "shipment_tracking 
                                   WHERE date_added >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                                   GROUP BY DATE(date_added)
                                   ORDER BY date_added DESC");
        $stats['daily_counts'] = $query->rows;
        
        return $stats;
    }
    
    public function getDelayedShipments() {
        $query = $this->db->query("SELECT s.*, c.name as carrier_name, 
                                   CONCAT(cu.firstname, ' ', cu.lastname) as customer_name
                                   FROM " . DB_PREFIX . "shipment_tracking s 
                                   LEFT JOIN " . DB_PREFIX . "shipping_carrier c ON (s.carrier_id = c.carrier_id)
                                   LEFT JOIN " . DB_PREFIX . "customer cu ON (s.customer_id = cu.customer_id)
                                   WHERE s.estimated_delivery < NOW() 
                                   AND s.status NOT IN ('delivered', 'returned', 'failed')
                                   ORDER BY s.estimated_delivery ASC");
        return $query->rows;
    }
    
    public function getRecentUpdates() {
        $query = $this->db->query("SELECT s.*, c.name as carrier_name, 
                                   CONCAT(cu.firstname, ' ', cu.lastname) as customer_name
                                   FROM " . DB_PREFIX . "shipment_tracking s 
                                   LEFT JOIN " . DB_PREFIX . "shipping_carrier c ON (s.carrier_id = c.carrier_id)
                                   LEFT JOIN " . DB_PREFIX . "customer cu ON (s.customer_id = cu.customer_id)
                                   WHERE s.last_update >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
                                   ORDER BY s.last_update DESC
                                   LIMIT 10");
        return $query->rows;
    }
} 