{{ header }}
{{ column_left }}
<div id="content">
    <div class="page-header">
        <div class="container-fluid">
            <div class="pull-right">
                <button type="submit" form="form-square-checkout" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fa fa-save"></i></button>
                <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a></div>
            <h1>{{ heading_title }}</h1>
            <ul class="breadcrumb">
                {% for breadcrumb in breadcrumbs %}
                    <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
                {% endfor %}
            </ul>
        </div>
    </div>
    <div class="container-fluid">
        {% for alert in alerts %}
            <div class="alert alert-{{ alert.type }}"><i class="fa fa-{{ alert.icon }}"></i>&nbsp;{{ alert.text }}
                <button type="button" class="close" data-dismiss="alert">&times;</button>
            </div>
        {% endfor %}

        <div class="panel panel-default">
            <div class="panel-heading">
                <h3 class="panel-title"><i class="fa fa-pencil"></i>&nbsp;{{ text_edit_heading }}</h3>
            </div>
            <div class="panel-body">
                <form action="{{ action }}" method="post" enctype="multipart/form-data" id="form-square-checkout" class="form-horizontal">
                    <input type="hidden" name="payment_squareup_card" value="1" />
                    <ul class="nav nav-tabs">
                        <li><a href="#tab-setting" data-toggle="tab"><i class="fa fa-gear"></i>&nbsp;{{ tab_setting }}</a></li>
                        <li><a href="#tab-transaction" data-toggle="tab"><i class="fa fa-list"></i>&nbsp;{{ tab_transaction }}</a></li>
                        <li><a href="#tab-cron" data-toggle="tab"><i class="fa fa-clock-o"></i>&nbsp;{{ tab_cron }}</a></li>
                        <li><a href="#tab-recurring" data-toggle="tab"><i class="fa fa-hourglass-half"></i>&nbsp;{{ tab_recurring }}</a></li>
                    </ul>
                    <div class="tab-content">
                        <div class="tab-pane" id="tab-setting">
                            <fieldset>
                                {% if payment_squareup_merchant_id %}
                                    <legend>{{ text_connection_section }} - {{ text_connected }}</legend>
                                    <div class="row">
                                        <div class="col-sm-12">
                                            <span data-toggle="tooltip" title="{{ text_disabled_connect_help_text }}">
                                                <a id="reconnect-button" href="{{ payment_squareup_auth_link }}" class="btn btn-primary btn-lg btn-connect" >{{ button_reconnect }}</a>
                                            </span>
                                            <span data-toggle="tooltip" title="{{ text_disabled_connect_help_text }}">
                                                <a id="refresh-button" href="{{ payment_squareup_refresh_link }}" class="btn btn-primary btn-lg btn-connect" >{{ button_refresh }}</a>
                                            </span>
                                            <p>{{ text_connected_info }}</p>
                                        </div>
                                    </div>
                                {% else %}
                                    <legend>{{ text_connection_section }} - {{ text_not_connected }}</legend>
                                    <div class="row">
                                        <div class="col-sm-12">
                                            <span data-toggle="tooltip" title="{{ text_disabled_connect_help_text }}">
                                                <a id="connect-button" href="{{ payment_squareup_auth_link }}" class="btn btn-primary btn-lg btn-connect">{{ button_connect }}</a>
                                            </span>
                                            <p>{{ text_not_connected_info }}</p>
                                        </div>
                                    </div>
                                {% endif %}
                            </fieldset>
                            <fieldset>
                                <legend>{{ text_settings_section_heading }}</legend>
                                <div class="form-group">
                                    <label class="col-sm-2 control-label" for="dropdown_payment_squareup_status"><span data-toggle="tooltip" title="{{ text_extension_status_help }}">{{ text_extension_status }}</span></label>
                                    <div class="col-sm-10">
                                        <select name="payment_squareup_status" id="dropdown_payment_squareup_status" class="form-control">
                                            <option value="1" {% if payment_squareup_status == 1 %} selected {% endif %}>{{ text_extension_status_enabled }}</option>
                                            <option value="0" {% if payment_squareup_status == 0 %} selected {% endif %}>{{ text_extension_status_disabled }}</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">
                                        <span data-toggle="tooltip" title="{{ text_payment_method_name_help }}">{{ text_payment_method_name_label }}</span>
                                    </label>
                                    <div class="col-sm-10">
                                        {% for language in languages %}
                                            <div class="input-group">
                                                <span class="input-group-addon"><img src="{{ language.image }}" alt="{{ language.name }}" /></span>
                                                <input type="text" name="payment_squareup_display_name[{{ language.language_id }}]" value="{{ payment_squareup_display_name[language.language_id] ?? text_payment_method_name_placeholder }}" placeholder="{{ text_payment_method_name_placeholder }}" class="form-control"/>
                                            </div>
                                        {% endfor %}
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-2 control-label" for="input_payment_squareup_redirect_uri_static">
                                        <span data-toggle="tooltip" title="{{ text_redirect_uri_help }}">{{ text_redirect_uri_label }}</span>
                                    </label>
                                    <div class="col-sm-10">
                                        <input type="text" id="input_payment_squareup_redirect_uri_static" name="payment_squareup_redirect_uri_static" value="{{ payment_squareup_redirect_uri }}" class="form-control" disabled />
                                    </div>
                                </div>
                                <div class="form-group required">
                                    <label class="col-sm-2 control-label" for="input_payment_squareup_client_id">
                                        <span data-toggle="tooltip" title="{{ text_client_id_help }}">{{ text_client_id_label }}</span>
                                    </label>
                                    <div class="col-sm-10">
                                        <input type="text" name="payment_squareup_client_id" value="{{ payment_squareup_client_id }}" placeholder="{{ text_client_id_placeholder }}" id="input_payment_squareup_client_id" class="form-control"/>
                                        {% if error_client_id %}
                                            <div class="text-danger">{{ error_client_id }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="form-group required">
                                    <label class="col-sm-2 control-label" for="input_payment_squareup_client_secret">
                                        <span data-toggle="tooltip" title="{{ text_client_secret_help }}">{{ text_client_secret_label }}</span>
                                    </label>
                                    <div class="col-sm-10">
                                        <input type="text" name="payment_squareup_client_secret" value="{{ payment_squareup_client_secret }}" placeholder="{{ text_client_secret_placeholder }}" id="input_payment_squareup_client_secret" class="form-control"/>
                                        {% if error_client_secret %}
                                            <div class="text-danger">{{ error_client_secret }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-2 control-label" for="dropdown_payment_squareup_delay_capture"><span data-toggle="tooltip" title="{{ text_delay_capture_help }}">{{ text_delay_capture_label }}</span></label>
                                    <div class="col-sm-10">
                                        <select name="payment_squareup_delay_capture" id="dropdown_payment_squareup_delay_capture" class="form-control">
                                            <option value="1" {% if payment_squareup_delay_capture == 1 %} selected {% endif %}>{{ text_authorize_label }}</option>
                                            <option value="0" {% if payment_squareup_delay_capture == 0 %} selected {% endif %}>{{ text_sale_label }}</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-2 control-label" for="input_payment_squareup_total">
                                        <span data-toggle="tooltip" title="{{ help_total }}">{{ entry_total }}</span>
                                    </label>
                                    <div class="col-sm-10">
                                        <input type="text" name="payment_squareup_total" value="{{ payment_squareup_total }}" placeholder="{{ entry_total }}" id="payment_squareup_total" class="form-control"/>
                                    </div>
                                </div>
                                <div class="form-group">
                                  <label class="col-sm-2 control-label" for="input-geo-zone">{{ entry_geo_zone }}</label>
                                  <div class="col-sm-10">
                                    <select name="payment_squareup_geo_zone_id" id="input-geo-zone" class="form-control">
                                        <option value="0">{{ text_all_zones }}</option>
                                        {% for geo_zone in geo_zones %}
                                            <option value="{{ geo_zone.geo_zone_id }}" {% if geo_zone.geo_zone_id == payment_squareup_geo_zone_id %} selected {% endif %}>{{ geo_zone.name }}</option>
                                        {% endfor %}
                                    </select>
                                  </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-2 control-label" for="input_payment_squareup_sort_order">
                                        {{ entry_sort_order }}
                                    </label>
                                    <div class="col-sm-10">
                                        <input type="text" name="payment_squareup_sort_order" value="{{ payment_squareup_sort_order }}" placeholder="{{ entry_sort_order }}" id="input_payment_squareup_sort_order" class="form-control"/>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-2 control-label" for="dropdown_payment_squareup_debug"><span data-toggle="tooltip" title="{{ text_debug_help }}">{{ text_debug_label }}</span></label>
                                    <div class="col-sm-10">
                                        <select name="payment_squareup_debug" id="dropdown_payment_squareup_debug" class="form-control">
                                            <option value="1" {% if payment_squareup_debug == 1 %} selected {% endif %}>{{ text_debug_enabled }}</option>
                                            <option value="0" {% if payment_squareup_debug == 0 %} selected {% endif %}>{{ text_debug_disabled }}</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-2 control-label" for="dropdown_payment_squareup_enable_sandbox"><span data-toggle="tooltip" title="{{ text_enable_sandbox_help }}">{{ text_enable_sandbox_label }}</span></label>
                                    <div class="col-sm-10">
                                        <select name="payment_squareup_enable_sandbox" id="dropdown_payment_squareup_enable_sandbox" class="form-control">
                                            <option value="1" {% if payment_squareup_enable_sandbox == 1 %} selected {% endif %}>{{ text_sandbox_enabled_label }}</option>
                                            <option value="0" {% if payment_squareup_enable_sandbox == 0 %} selected {% endif %}>{{ text_sandbox_disabled_label }}</option>
                                        </select>
                                    </div>
                                </div>
                            </fieldset>
                            <fieldset id="sandbox_settings" {% if not payment_squareup_enable_sandbox %} style="display: none;" {% endif %}> 
                                <legend>{{ text_sandbox_section_heading }}</legend>
                                <div class="form-group required">
                                    <label class="col-sm-2 control-label" for="input_payment_squareup_sandbox_client_id">
                                        <span data-toggle="tooltip" title="{{ text_sandbox_client_id_help }}">{{ text_sandbox_client_id_label }}</span>
                                    </label>
                                    <div class="col-sm-10">
                                        <input type="text" name="payment_squareup_sandbox_client_id" value="{{ payment_squareup_sandbox_client_id }}" placeholder="{{ text_sandbox_client_id_placeholder }}" id="input_payment_squareup_sandbox_client_id" class="form-control" />
                                        {% if error_sandbox_client_id %}
                                            <div class="text-danger">{{ error_sandbox_client_id }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="form-group required">
                                    <label class="col-sm-2 control-label" for="input_payment_squareup_sandbox_token">
                                        <span data-toggle="tooltip" title="{{ text_sandbox_access_token_help }}">{{ text_sandbox_access_token_label }}</span>
                                    </label>
                                    <div class="col-sm-10">
                                        <input type="text" name="payment_squareup_sandbox_token" value="{{ payment_squareup_sandbox_token }}" placeholder="{{ text_sandbox_access_token_placeholder }}" id="input_payment_squareup_sandbox_token" class="form-control"/>
                                        {% if error_sandbox_token %}
                                            <div class="text-danger">{{ error_sandbox_token }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </fieldset>
                            <fieldset>
                                <legend>{{ text_merchant_info_section_heading }}</legend>  
                                <div class="form-group">
                                    <label class="col-sm-2 control-label" for="input_merchant_name">
                                        {{ text_merchant_name_label }}
                                    </label>
                                    <div class="col-sm-10">
                                        <input type="text" name="merchant_name" value="{{ payment_squareup_merchant_name }}" placeholder="{{ text_merchant_name_placeholder }}" id="input_merchant_name" class="form-control" disabled/>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-2 control-label" for="access_token_expires_time">
                                        {{ text_access_token_expires_label }}
                                    </label>
                                    <div class="col-sm-10">
                                        <input type="text" name="access_token_expires" value="{{ access_token_expires_time }}" placeholder="{{ text_access_token_expires_placeholder }}" id="access_token_expires_time" class="form-control" disabled />
                                    </div>
                                </div>
                                <div class="form-group required">
                                    <label class="col-sm-2 control-label" for="dropdown_payment_squareup_sandbox_location_id"><span data-toggle="tooltip" title="{{ text_location_help }}">{{ text_location_label }}</span></label>
                                    <div class="col-sm-10">
                                        <select name="payment_squareup_location_id" id="dropdown_payment_squareup_location_id" class="form-control" {% if not payment_squareup_locations %} disabled {% endif %}>
                                            {% for location in payment_squareup_locations %}
                                                <option value="{{ location.id }}" {% if location.id == payment_squareup_location_id %} selected {% endif %}>{{ location.name }}</option>
                                            {% endfor %}
                                        </select>
                                        <select name="payment_squareup_sandbox_location_id" id="dropdown_payment_squareup_sandbox_location_id" class="form-control" {% if not payment_squareup_sandbox_locations %} disabled {% endif %}>
                                            {% for location in payment_squareup_sandbox_locations %}
                                                <option value="{{ location.id }}" {% if location.id == payment_squareup_sandbox_location_id %} selected {% endif %}>{{ location.name }}</option>
                                            {% endfor %}
                                        </select>
                                        {% if error_location %}
                                            <div class="text-danger">{{ error_location }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </fieldset>
                            <fieldset>
                                <legend>{{ text_transaction_statuses }}</legend>
                                <div class="form-group">
                                    <label class="col-sm-2 control-label" for="dropdown_payment_squareup_status_authorized"><span data-toggle="tooltip" title="{{ payment_squareup_status_comment_authorized }}">{{ entry_status_authorized }}</span></label>
                                    <div class="col-sm-10">
                                        <select name="payment_squareup_status_authorized" id="dropdown_payment_squareup_status_authorized" class="form-control">
                                            {% for order_status in order_statuses %}
                                                <option value="{{ order_status.order_status_id }}" {% if order_status.order_status_id == payment_squareup_status_authorized %} selected {% endif %}>{{ order_status.name }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-2 control-label" for="dropdown_payment_squareup_status_captured"><span data-toggle="tooltip" title="{{ payment_squareup_status_comment_captured }}">{{ entry_status_captured }}</span></label>
                                    <div class="col-sm-10">
                                        <select name="payment_squareup_status_captured" id="dropdown_payment_squareup_status_captured" class="form-control">
                                            {% for order_status in order_statuses %}
                                                <option value="{{ order_status.order_status_id }}" {% if order_status.order_status_id == payment_squareup_status_captured %} selected {% endif %}>{{ order_status.name }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-2 control-label" for="dropdown_payment_squareup_status_voided"><span data-toggle="tooltip" title="{{ payment_squareup_status_comment_voided }}">{{ entry_status_voided }}</span></label>
                                    <div class="col-sm-10">
                                        <select name="payment_squareup_status_voided" id="dropdown_payment_squareup_status_voided" class="form-control">
                                            {% for order_status in order_statuses %}
                                                <option value="{{ order_status.order_status_id }}" {% if order_status.order_status_id == payment_squareup_status_voided %} selected {% endif %}>{{ order_status.name }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-2 control-label" for="dropdown_payment_squareup_status_failed"><span data-toggle="tooltip" title="{{ payment_squareup_status_comment_failed }}">{{ entry_status_failed }}</span></label>
                                    <div class="col-sm-10">
                                        <select name="payment_squareup_status_failed" id="dropdown_payment_squareup_status_failed" class="form-control">
                                            {% for order_status in order_statuses %}
                                                <option value="{{ order_status.order_status_id }}" {% if order_status.order_status_id == payment_squareup_status_failed %} selected {% endif %}>{{ order_status.name }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                </div>
                            </fieldset>
                        </div>
                        <div class="tab-pane" id="tab-transaction">
                            <div id="transaction-alert" data-message="{{ text_loading }}"></div>
                            <div class="text-right margin-bottom">
                            </div>
                            <div class="table-responsive">
                                <table class="table table-bordered table-hover">
                                    <thead>
                                        <tr>
                                            <th class="text-left hidden-xs">{{ column_transaction_id }}</th> 
                                            <th class="text-left">{{ column_customer }}</th>
                                            <th class="text-left hidden-xs">{{ column_order_id }}</th>
                                            <th class="text-left hidden-xs">{{ column_type }}</th>
                                            <th class="text-left hidden-xs">{{ column_amount }}</th>
                                            <th class="text-left hidden-xs">{{ column_refunds }}</th>
                                            <th class="text-left hidden-xs hidden-sm">{{ column_ip }}</th>
                                            <th class="text-left">{{ column_date_created }}</th>
                                            <th class="text-right">{{ column_action }}</th>
                                        </tr>
                                    </thead>
                                    <tbody id="transactions">
                                    </tbody>
                                </table>
                                <div id="transactions_pagination"></div>
                            </div>
                        </div>
                        <div class="tab-pane" id="tab-cron">
                            <fieldset>
                                <legend>{{ text_executables }}</legend>
                                <div class="alert alert-info"><i class="fa fa-info-circle"></i> {{ text_recurring_info }}</div>
                                <div class="form-group">
                                    <label class="col-sm-2 control-label"><span data-toggle="tooltip" data-original-title="{{ help_local_cron }}">{{ text_local_cron }}</span></label>
                                    <div class="col-sm-10">
                                        <input disabled type="text" class="form-control" value="{{ payment_squareup_cron_command }}" />
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-2 control-label"><span data-toggle="tooltip" data-original-title="{{ help_remote_cron }}">{{ text_remote_cron }}</span></label>
                                    <div class="col-sm-10">
                                        <div class="input-group">
                                            <input disabled type="text" name="payment_squareup_cron_url" id="input_payment_squareup_cron_url" class="form-control" value="" />
                                            <div data-toggle="tooltip" data-original-title="{{ text_refresh_token }}" class="input-group-addon btn btn-primary" id="refresh-cron-token">
                                                <i class="fa fa-refresh"></i>
                                            </div>
                                        </div>
                                        <input id="input_payment_squareup_cron_token" type="hidden" name="payment_squareup_cron_token" value="{{ payment_squareup_cron_token }}" />
                                    </div>
                                </div>
                                <div class="form-group required">
                                    <label class="col-sm-2 control-label" for="checkbox_payment_squareup_cron_acknowledge">{{ entry_setup_confirmation }}</label>
                                    <div class="col-sm-10">
                                        <label class="checkbox-inline">
                                            <input id="checkbox_payment_squareup_cron_acknowledge" type="checkbox" value="1" {% if payment_squareup_cron_acknowledge %} checked {% endif %} name="payment_squareup_cron_acknowledge" /> {{ text_acknowledge_cron }}
                                        </label>

                                        {% if error_cron_acknowledge %}
                                            <div class="text-danger">{{ error_cron_acknowledge }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </fieldset>
                            <fieldset>
                                <legend>{{ text_admin_notifications }}</legend>
                                <div class="form-group">
                                    <label class="control-label col-sm-2" for="dropdown_payment_squareup_cron_email_status"><span data-toggle="tooltip" data-original-title="{{ help_cron_email_status }}">{{ text_cron_email_status }}</span></label>
                                    <div class="col-sm-10">
                                        <select id="dropdown_payment_squareup_cron_email_status" name="payment_squareup_cron_email_status" class="form-control">
                                            <option value="1" {% if payment_squareup_cron_email_status == '1' %} selected {% endif %}>{{ text_enabled }}</option>
                                            <option value="0" {% if payment_squareup_cron_email_status == '0' %} selected {% endif %}>{{ text_disabled }}</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group required">
                                    <label class="col-sm-2 control-label" for="input_payment_squareup_cron_email"><span data-toggle="tooltip" data-original-title="{{ help_cron_email }}">{{ text_cron_email }}</span></label>
                                    <div class="col-sm-10">
                                        <input id="input_payment_squareup_cron_email" name="payment_squareup_cron_email" type="text" class="form-control" value="{{ payment_squareup_cron_email }}"/>
                                        {% if error_cron_email %}
                                            <div class="text-danger">{{ error_cron_email }}</div>
                                        {% endif %}
                                    </div>
                                </div>
                            </fieldset>
                        </div>
                        <div class="tab-pane" id="tab-recurring">
                            <div class="form-group">
                                <label class="control-label col-sm-2" for="dropdown_payment_squareup_recurring_status"><span data-toggle="tooltip" data-original-title="{{ help_recurring_status }}">{{ text_recurring_status }}</span></label>
                                <div class="col-sm-10">
                                    <select id="dropdown_payment_squareup_recurring_status" name="payment_squareup_recurring_status" class="form-control">
                                        <option value="1" {% if payment_squareup_recurring_status == '1' %} selected {% endif %}>{{ text_enabled }}</option>
                                        <option value="0" {% if payment_squareup_recurring_status == '0' %} selected {% endif %}>{{ text_disabled }}</option>
                                    </select>
                                </div>
                            </div>
                            <fieldset>
                                <legend>{{ text_customer_notifications }}</legend>
                                <div class="form-group">
                                    <label class="control-label col-sm-2" for="dropdown_payment_squareup_notify_recurring_success"><span data-toggle="tooltip" data-original-title="{{ help_notify_recurring_success }}">{{ text_notify_recurring_success }}</span></label>
                                    <div class="col-sm-10">
                                        <select id="dropdown_payment_squareup_notify_recurring_success" name="payment_squareup_notify_recurring_success" class="form-control">
                                            <option value="1" {% if payment_squareup_notify_recurring_success == '1' %} selected {% endif %}>{{ text_enabled }}</option>
                                            <option value="0" {% if payment_squareup_notify_recurring_success == '0' %} selected {% endif %}>{{ text_disabled }}</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="control-label col-sm-2" for="dropdown_payment_squareup_notify_recurring_fail"><span data-toggle="tooltip" data-original-title="{{ help_notify_recurring_fail }}">{{ text_notify_recurring_fail }}</span></label>
                                    <div class="col-sm-10">
                                        <select id="dropdown_payment_squareup_notify_recurring_fail" name="payment_squareup_notify_recurring_fail" class="form-control">
                                            <option value="1" {% if payment_squareup_notify_recurring_fail == '1' %} selected {% endif %}>{{ text_enabled }}</option>
                                            <option value="0" {% if payment_squareup_notify_recurring_fail == '0' %} selected {% endif %}>{{ text_disabled }}</option>
                                        </select>
                                    </div>
                                </div>
                            </fieldset>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <div class="modal fade" id="squareup-confirm-modal">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                    <h4 class="modal-title">{{ text_confirm_action }}</h4>
                </div>
                <div class="modal-body">
                    <h4 id="squareup-confirm-modal-content"></h4>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">{{ text_close }}</button>
                    <button id="squareup-confirm-ok" type="button" class="btn btn-primary">{{ text_ok }}</button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="squareup-refund-modal">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                    <h4 class="modal-title">{{ text_refund_details }}</h4>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label class="control-label" id="squareup-refund-modal-content-reason"></label>
                        <textarea class="form-control" id="squareup-refund-reason" required></textarea>
                    </div>
                    <div class="form-group">
                        <label class="control-label" id="squareup-refund-modal-content-amount"></label>
                        <input class="form-control" type="text" id="squareup-refund-amount" required />
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">{{ text_close }}</button>
                    <button id="squareup-refund-ok" type="button" class="btn btn-primary">{{ text_ok }}</button>
                </div>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
$(document).ready(function() {
    var triggerConnectButtons = function() {
        if ($('#input_payment_squareup_client_id').val() != '' && $('#input_payment_squareup_client_secret').val() != '') {
            $('.btn-connect').removeClass('disabled');
        } else {
            $('.btn-connect').addClass('disabled');
        }
    }

    var setCronUrl = function() {
        $('#input_payment_squareup_cron_url').val(
            "{{ payment_squareup_cron_url }}".replace('{CRON_TOKEN}', $('#input_payment_squareup_cron_token').val())
        );
    }

    var randomString = function() {
        return (Math.random() * 100).toString(16).replace('.', '');
    }

    var onConnectClick = function(event) {
        if ($('#input_payment_squareup_client_id').val() != '{{ payment_squareup_client_id }}' || $('#input_payment_squareup_client_secret').val() != '{{ payment_squareup_client_secret }}') {
            event.preventDefault();
            event.stopPropagation();

            $('form').attr('action','{{ action_save_auth }}').submit();
        }
    }

    var listTransactions = function(page) {
        $.ajax({
          url : '{{ url_list_transactions }}'.replace('{PAGE}', page ? page : transactionListPage),
          dataType : 'json',
          beforeSend : function() {
            $('#refresh_transactions').button('loading');
            $('#transactions_pagination').empty();
            $('#transactions').html('<tr><td colspan="9" class="text-center"><i class="fa fa-circle-o-notch fa-spin"></i>&nbsp;{{ text_loading }}</td></tr>');
          },
          success : function(data) {
            var html = '';

            if (data.transactions.length) {
              for (var i in data.transactions) {
                var row = data.transactions[i];

                html += '<tr>';
                html += '<td class="text-left hidden-xs">' + row.transaction_id + '</td>';
                html += '<td class="text-left hidden-xs">' + row.customer + '</td>';
                html += '<td class="text-left"><a target="_blank" href="' + row.url_order + '">' + row.order_id + '</td>';
                html += '<td class="text-left hidden-xs">' + row.type + '</td>';
                html += '<td class="text-left hidden-xs">' + row.amount + '</td>';
                html += '<td class="text-left hidden-xs">' + row.num_refunds + '</td>';
                html += '<td class="text-left hidden-xs hidden-sm">' + row.ip + '</td>';
                html += '<td class="text-left">' + row.date_created + '</td>';
                html += '<td class="text-right">';

                switch (row.type) {
                    case "AUTHORIZED" : {
                        html += '<a class="btn btn-success" data-url-transaction-capture="' + row.url_capture + '" data-confirm-capture="' + row.confirm_capture + '">{{ text_capture }}</a> ';
                        html += '<a class="btn btn-warning" data-url-transaction-void="' + row.url_void + '" data-confirm-void="' + row.confirm_void + '">{{ text_void }}</a> ';
                    } break;

                    case "CAPTURED" : {
                        html += '<a class="btn btn-danger" data-url-transaction-refund="' + row.url_refund + '" data-confirm-refund="' + row.confirm_refund + '" data-insert-amount="' + row.insert_amount + '">{{ text_refund }}</a> ';
                    } break;
                }

                html += ' <a class="btn btn-info" href="' + row.url_info + '">{{ text_view }}</a>';
                html += '</td>';
                html += '</tr>';
              }
            } else {
              html += '<tr>';
              html += '<td class="text-center" colspan="9">{{ text_no_transactions }}</td>';
              html += '</tr>';
            }

            $('#transactions').html(html);
            
            $('#transactions_pagination').html(data.pagination).find('a[href]').each(function(index,element) {
              $(this).click(function(e) {
                e.preventDefault();

                transactionListPage = isNaN($(this).attr('href')) ? 1 : $(this).attr('href');

                listTransactions();
              })
            });
          },
          complete : function() {
            $('#refresh_transactions').button('reset');
          }
        });
    }

    var transactionLoading = function() {
        var message = $('#transaction-alert').attr('data-message');

        $('#transaction-alert').html('<div class="text-center alert alert-info"><i class="fa fa-circle-o-notch fa-spin"></i>&nbsp;' + message + '</div>');
    }

    var transactionError = function(message) {
        $('#transaction-alert').html('<div class="alert alert-danger"><button type="button" class="close" data-dismiss="alert" aria-label="X"><span aria-hidden="true">&times;</span></button><i class="fa fa-exclamation-circle"></i>&nbsp;' + message + '</div>');
    }

    var transactionSuccess = function(message) {
        $('#transaction-alert').html('<div class="alert alert-success"><button type="button" class="close" data-dismiss="alert" aria-label="X"><span aria-hidden="true">&times;</span></button><i class="fa fa-exclamation-circle"></i>&nbsp;' + message + '</div>');
    }

    var addOrderHistory = function(data, success_callback) {
        $.ajax({
            url: '{{ catalog }}index.php?route=api/order/history&api_token={{ api_token }}&order_id=' + data.order_id,
            type: 'post',
            dataType: 'json',
            data: data,
            success: function(json) {
                if (json['error']) {
                    transactionError(json['error']);
                    enableTransactionButtons();
                }

                if (json['success']) {
                    success_callback();
                }
            },
            error: function(xhr, ajaxOptions, thrownError) {
                transactionError(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
                enableTransactionButtons();
            }
        });
    }

    var transactionRequest = function(type, url, data) {
        $.ajax({
            url : url,
            dataType : 'json',
            type : type,
            data : data,
            beforeSend : transactionLoading,
            success : function(json) {
                if (json.error) {
                    transactionError(json.error);
                    enableTransactionButtons();
                }

                if (json.success && json.order_history_data) {
                    addOrderHistory(json.order_history_data, function() {
                        transactionSuccess(json.success);
                        listTransactions();
                    });
                }
            },
            error : function(xhr, ajaxSettings, thrownError) {
                transactionError(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
                enableTransactionButtons();
            }
        });
    }

    var disableTransactionButtons = function() {
        $('*[data-url-transaction-capture], *[data-url-transaction-void], *[data-url-transaction-refund]').attr('disabled', true);
    }

    var enableTransactionButtons = function() {
        $('*[data-url-transaction-capture], *[data-url-transaction-void], *[data-url-transaction-refund]').attr('disabled', false);
    }

    var modalConfirm = function(url, text) {
        var modal = '#squareup-confirm-modal';
        var content = '#squareup-confirm-modal-content';
        var button = '#squareup-confirm-ok';

        $(content).html(text);
        $(button).unbind().click(function() {
            disableTransactionButtons();

            $(modal).modal('hide');

            transactionRequest('GET', url);
        });
        
        $(modal).modal('show');
    }

    var refundInputValidate = function(reason_input, amount_input) {
        var result = true;

        if (!$(reason_input)[0].checkValidity()) {
            $(reason_input).closest('.form-group').addClass('has-error');
            result = false;
        } else {
            $(reason_input).closest('.form-group').removeClass('has-error');
        }

        if (!$(amount_input)[0].checkValidity()) {
            $(amount_input).closest('.form-group').addClass('has-error');
            result = false;
        } else {
            $(amount_input).closest('.form-group').removeClass('has-error');
        }

        return result;
    }

    var modalRefund = function(url, text_reason, text_amount) {
        var modal = '#squareup-refund-modal';
        var content_reason = '#squareup-refund-modal-content-reason';
        var content_amount = '#squareup-refund-modal-content-amount';
        var button = '#squareup-refund-ok';
        var reason_input = '#squareup-refund-reason';
        var amount_input = '#squareup-refund-amount';

        $(content_reason).html(text_reason);
        $(content_amount).html(text_amount);

        $(reason_input).val('');
        $(amount_input).val('');

        $(button).unbind().click(function() {
            if (!refundInputValidate(reason_input, amount_input)) {
                return;
            }

            disableTransactionButtons();

            $(modal).modal('hide');

            transactionRequest('POST', url, {
                reason : $(reason_input).val(),
                amount : $(amount_input).val()
            });
        });
        
        $(modal).modal('show');
    }

    var transactionListPage = 1;

    $('.nav-tabs a[href="#{{ tab }}"]').tab('show');

    $('#dropdown_payment_squareup_enable_sandbox')
        .change(function() {
            if ($(this).val() == '0') {
                $('#sandbox_settings').slideUp();
                $('#dropdown_payment_squareup_location_id').show();
                $('#dropdown_payment_squareup_sandbox_location_id').hide();
            } else {
                $('#sandbox_settings').slideDown();
                $('#dropdown_payment_squareup_location_id').hide();
                $('#dropdown_payment_squareup_sandbox_location_id').show();
            }
        })
        .trigger('change');

    $('#input_payment_squareup_client_id, #input_payment_squareup_client_secret')
        .change(triggerConnectButtons)
        .keyup(triggerConnectButtons)
        .trigger('change');

    $('#refresh-cron-token').click(function() {
        $('#input_payment_squareup_cron_token').val(randomString() + randomString());
        setCronUrl();
    });

    $('#connect-button').click(onConnectClick);
    
    $('#reconnect-button').click(onConnectClick);

    $(document).on('click', '*[data-url-transaction-capture]', function() {
        if ($(this).attr('disabled')) return;

        modalConfirm(
            $(this).attr('data-url-transaction-capture'),
            $(this).attr('data-confirm-capture')
        );
    });
        
    $(document).on('click', '*[data-url-transaction-void]', function() {
        if ($(this).attr('disabled')) return;

        modalConfirm(
            $(this).attr('data-url-transaction-void'),
            $(this).attr('data-confirm-void')
        );
    });

    $(document).on('click', '*[data-url-transaction-refund]', function() {
        if ($(this).attr('disabled')) return;

        modalRefund(
            $(this).attr('data-url-transaction-refund'),
            $(this).attr('data-confirm-refund'),
            $(this).attr('data-insert-amount')
        );
    });
    
    setCronUrl();

    listTransactions();
});
</script>
{{ footer }}
