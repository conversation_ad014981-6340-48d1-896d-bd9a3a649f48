<?php
/**
 * English Language File - Task Management
 *
 * @package    AYM ERP
 * <AUTHOR> Development Team
 * @copyright  2025 AYM ERP Systems
 * @license    Commercial License
 * @version    1.0.0
 * @since      2025-07-15
 */

// Main heading
$_['heading_title'] = 'Task Management';

// Text
$_['text_success']           = 'Success: You have modified tasks!';
$_['text_list']              = 'Task List';
$_['text_add']               = 'Add New Task';
$_['text_edit']              = 'Edit Task';
$_['text_delete']            = 'Delete Task';
$_['text_view_task']         = 'View Task';
$_['text_enabled']           = 'Enabled';
$_['text_disabled']          = 'Disabled';
$_['text_priority_low']      = 'Low';
$_['text_priority_medium']   = 'Medium';
$_['text_priority_high']     = 'High';
$_['text_priority_urgent']   = 'Urgent';
$_['text_status_pending']    = 'Pending';
$_['text_status_in_progress'] = 'In Progress';
$_['text_status_review']     = 'Review';
$_['text_status_completed']  = 'Completed';
$_['text_status_cancelled']  = 'Cancelled';
$_['text_no_results']        = 'No results found';
$_['text_confirm']           = 'Are you sure you want to delete the selected tasks?';
$_['text_success_status_update'] = 'Task status updated successfully!';
$_['text_success_comment_added'] = 'Comment added successfully!';

// Entry
$_['entry_title']            = 'Task Title';
$_['entry_description']      = 'Description';
$_['entry_assignee']         = 'Assignee';
$_['entry_priority']         = 'Priority';
$_['entry_status']           = 'Status';
$_['entry_due_date']         = 'Due Date';
$_['entry_estimated_hours']  = 'Estimated Hours';
$_['entry_actual_hours']     = 'Actual Hours';
$_['entry_progress']         = 'Progress';
$_['entry_tags']             = 'Tags';
$_['entry_comment']          = 'Comment';

// Column
$_['column_title']           = 'Task Title';
$_['column_assignee']        = 'Assignee';
$_['column_priority']        = 'Priority';
$_['column_status']          = 'Status';
$_['column_progress']        = 'Progress';
$_['column_due_date']        = 'Due Date';
$_['column_created_date']    = 'Created Date';
$_['column_completed_date']  = 'Completed Date';
$_['column_action']          = 'Action';

// Tab
$_['tab_general']            = 'General';
$_['tab_details']            = 'Details';
$_['tab_history']            = 'History';
$_['tab_comments']           = 'Comments';

// Button
$_['button_add']             = 'Add';
$_['button_edit']            = 'Edit';
$_['button_delete']          = 'Delete';
$_['button_save']            = 'Save';
$_['button_cancel']          = 'Cancel';
$_['button_filter']          = 'Filter';
$_['button_view']            = 'View';
$_['button_update_status']   = 'Update Status';
$_['button_add_comment']     = 'Add Comment';

// Subtasks
$_['text_subtasks'] = 'Subtasks';
$_['text_add_subtask'] = 'Add Subtask';
$_['text_parent_task'] = 'Parent Task';
$_['text_child_tasks'] = 'Child Tasks';
$_['text_task_hierarchy'] = 'Task Hierarchy';

// Comments and notes
$_['text_comments'] = 'Comments';
$_['text_add_comment'] = 'Add Comment';
$_['text_edit_comment'] = 'Edit Comment';
$_['text_delete_comment'] = 'Delete Comment';
$_['text_comment_text'] = 'Comment Text';
$_['text_notes'] = 'Notes';
$_['text_internal_notes'] = 'Internal Notes';

// Activity log
$_['text_activity_log'] = 'Activity Log';
$_['text_task_history'] = 'Task History';
$_['text_status_changes'] = 'Status Changes';
$_['text_assignment_changes'] = 'Assignment Changes';
$_['text_time_tracking'] = 'Time Tracking';

// Time tracking
$_['text_time_entries'] = 'Time Entries';
$_['text_log_time'] = 'Log Time';
$_['text_start_timer'] = 'Start Timer';
$_['text_stop_timer'] = 'Stop Timer';
$_['text_time_spent'] = 'Time Spent';
$_['text_remaining_time'] = 'Remaining Time';
$_['text_overtime'] = 'Overtime';

// Attachments
$_['text_attachments'] = 'Attachments';
$_['text_add_attachment'] = 'Add Attachment';
$_['text_remove_attachment'] = 'Remove Attachment';
$_['text_download_attachment'] = 'Download Attachment';
$_['text_file_name'] = 'File Name';
$_['text_file_size'] = 'File Size';
$_['text_upload_date'] = 'Upload Date';

// Dependencies and relationships
$_['text_dependencies'] = 'Dependencies';
$_['text_blocked_by'] = 'Blocked By';
$_['text_blocking'] = 'Blocking';
$_['text_related_tasks'] = 'Related Tasks';
$_['text_predecessor'] = 'Predecessor';
$_['text_successor'] = 'Successor';

// Reports and statistics
$_['text_reports'] = 'Reports';
$_['text_task_reports'] = 'Task Reports';
$_['text_statistics'] = 'Statistics';
$_['text_performance_metrics'] = 'Performance Metrics';
$_['text_completion_rate'] = 'Completion Rate';
$_['text_overdue_tasks'] = 'Overdue Tasks';
$_['text_tasks_by_status'] = 'Tasks by Status';
$_['text_tasks_by_priority'] = 'Tasks by Priority';
$_['text_tasks_by_assignee'] = 'Tasks by Assignee';

// Search and filter
$_['text_search'] = 'Search';
$_['text_search_tasks'] = 'Search Tasks';
$_['text_advanced_search'] = 'Advanced Search';
$_['text_filter'] = 'Filter';
$_['text_filter_by_status'] = 'Filter by Status';
$_['text_filter_by_priority'] = 'Filter by Priority';
$_['text_filter_by_assignee'] = 'Filter by Assignee';
$_['text_filter_by_project'] = 'Filter by Project';
$_['text_filter_by_due_date'] = 'Filter by Due Date';

// Views and layouts
$_['text_views'] = 'Views';
$_['text_list_view'] = 'List View';
$_['text_board_view'] = 'Board View';
$_['text_calendar_view'] = 'Calendar View';
$_['text_gantt_view'] = 'Gantt Chart';
$_['text_timeline_view'] = 'Timeline View';

// Notifications and alerts
$_['text_notifications'] = 'Notifications';
$_['text_task_notifications'] = 'Task Notifications';
$_['text_due_date_reminders'] = 'Due Date Reminders';
$_['text_overdue_alerts'] = 'Overdue Alerts';
$_['text_status_change_notifications'] = 'Status Change Notifications';

// Templates and forms
$_['text_templates'] = 'Templates';
$_['text_task_templates'] = 'Task Templates';
$_['text_create_template'] = 'Create Template';
$_['text_use_template'] = 'Use Template';
$_['text_template_name'] = 'Template Name';

// Collaboration and teams
$_['text_collaboration'] = 'Collaboration';
$_['text_team_tasks'] = 'Team Tasks';
$_['text_shared_tasks'] = 'Shared Tasks';
$_['text_watchers'] = 'Watchers';
$_['text_add_watcher'] = 'Add Watcher';
$_['text_remove_watcher'] = 'Remove Watcher';

// Integrations
$_['text_integrations'] = 'Integrations';
$_['text_calendar_integration'] = 'Calendar Integration';
$_['text_email_integration'] = 'Email Integration';
$_['text_project_integration'] = 'Project Integration';
$_['text_workflow_integration'] = 'Workflow Integration';

// Error messages
$_['error_permission'] = 'Warning: You do not have permission to access task management!';
$_['error_title'] = 'Task title must be between 3 and 255 characters!';
$_['error_description'] = 'Task description is required!';
$_['error_assignee'] = 'Task assignee must be selected!';
$_['error_due_date'] = 'Due date must be in the future!';
$_['error_invalid_progress'] = 'Progress must be between 0 and 100!';
$_['error_task_not_found'] = 'Task not found!';
$_['error_cannot_delete'] = 'Cannot delete task!';
$_['error_circular_dependency'] = 'Circular dependency not allowed!';

// Confirmation messages
$_['text_confirm_delete'] = 'Are you sure you want to delete this task?';
$_['text_confirm_complete'] = 'Are you sure you want to complete this task?';
$_['text_confirm_cancel'] = 'Are you sure you want to cancel this task?';
$_['text_confirm_reassign'] = 'Are you sure you want to reassign this task?';

// Help and tips
$_['help_title'] = 'Enter a clear and concise task title';
$_['help_description'] = 'Detailed description of what needs to be accomplished';
$_['help_priority'] = 'Choose task priority based on importance';
$_['help_due_date'] = 'Set the deadline for task completion';
$_['help_estimated_hours'] = 'Estimate the time required to complete the task';

// Alerts
$_['alert_task_created'] = 'Task created successfully';
$_['alert_task_updated'] = 'Task updated successfully';
$_['alert_task_deleted'] = 'Task deleted';
$_['alert_task_assigned'] = 'Task assigned';
$_['alert_task_completed'] = 'Task completed';
$_['alert_task_cancelled'] = 'Task cancelled';

// Dates and times
$_['text_created_at'] = 'Created At';
$_['text_updated_at'] = 'Updated At';
$_['text_started_at'] = 'Started At';
$_['text_completed_at'] = 'Completed At';
$_['text_due_in'] = 'Due In';
$_['text_overdue_by'] = 'Overdue By';

// Advanced features
$_['text_recurring_task'] = 'Recurring Task';
$_['text_milestone'] = 'Milestone';
$_['text_critical_path'] = 'Critical Path';
$_['text_effort_estimation'] = 'Effort Estimation';
$_['text_complexity'] = 'Complexity';

// Security and permissions
$_['text_security'] = 'Security & Permissions';
$_['text_task_permissions'] = 'Task Permissions';
$_['text_visibility'] = 'Visibility';
$_['text_confidential'] = 'Confidential';
$_['text_public'] = 'Public';
$_['text_restricted'] = 'Restricted';