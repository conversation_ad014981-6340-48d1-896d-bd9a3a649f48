{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="submit" form="sale\order_modification-form" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fas fa-save"></i></button>
        <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-light"><i class="fas fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    
    <div class="card">
      <div class="card-header"><i class="fas fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}chart-bar{% elif screen_type == 'settings' %}cog{% else %}pencil-alt{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="card-body">
        <form id="sale\order_modification-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-back">{{ text_back }}</label>
            <div class="col-sm-10">
              <input type="text" name="back" value="{{ back }}" placeholder="{{ text_back }}" id="input-back" class="form-control" />
              {% if error_back %}
                <div class="invalid-feedback">{{ error_back }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-eta_enabled">{{ text_eta_enabled }}</label>
            <div class="col-sm-10">
              <input type="text" name="eta_enabled" value="{{ eta_enabled }}" placeholder="{{ text_eta_enabled }}" id="input-eta_enabled" class="form-control" />
              {% if error_eta_enabled %}
                <div class="invalid-feedback">{{ error_eta_enabled }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-eta_invoice_sent">{{ text_eta_invoice_sent }}</label>
            <div class="col-sm-10">
              <input type="text" name="eta_invoice_sent" value="{{ eta_invoice_sent }}" placeholder="{{ text_eta_invoice_sent }}" id="input-eta_invoice_sent" class="form-control" />
              {% if error_eta_invoice_sent %}
                <div class="invalid-feedback">{{ error_eta_invoice_sent }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-eta_notes">{{ text_eta_notes }}</label>
            <div class="col-sm-10">
              <input type="text" name="eta_notes" value="{{ eta_notes }}" placeholder="{{ text_eta_notes }}" id="input-eta_notes" class="form-control" />
              {% if error_eta_notes %}
                <div class="invalid-feedback">{{ error_eta_notes }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_customer">{{ text_filter_customer }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_customer" value="{{ filter_customer }}" placeholder="{{ text_filter_customer }}" id="input-filter_customer" class="form-control" />
              {% if error_filter_customer %}
                <div class="invalid-feedback">{{ error_filter_customer }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_date_from">{{ text_filter_date_from }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_date_from" value="{{ filter_date_from }}" placeholder="{{ text_filter_date_from }}" id="input-filter_date_from" class="form-control" />
              {% if error_filter_date_from %}
                <div class="invalid-feedback">{{ error_filter_date_from }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_date_to">{{ text_filter_date_to }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_date_to" value="{{ filter_date_to }}" placeholder="{{ text_filter_date_to }}" id="input-filter_date_to" class="form-control" />
              {% if error_filter_date_to %}
                <div class="invalid-feedback">{{ error_filter_date_to }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_order_id">{{ text_filter_order_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_order_id" value="{{ filter_order_id }}" placeholder="{{ text_filter_order_id }}" id="input-filter_order_id" class="form-control" />
              {% if error_filter_order_id %}
                <div class="invalid-feedback">{{ error_filter_order_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="invalid-feedback">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-modification_history">{{ text_modification_history }}</label>
            <div class="col-sm-10">
              <input type="text" name="modification_history" value="{{ modification_history }}" placeholder="{{ text_modification_history }}" id="input-modification_history" class="form-control" />
              {% if error_modification_history %}
                <div class="invalid-feedback">{{ error_modification_history }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-modification_info">{{ text_modification_info }}</label>
            <div class="col-sm-10">
              <input type="text" name="modification_info" value="{{ modification_info }}" placeholder="{{ text_modification_info }}" id="input-modification_info" class="form-control" />
              {% if error_modification_info %}
                <div class="invalid-feedback">{{ error_modification_info }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-modification_items">{{ text_modification_items }}</label>
            <div class="col-sm-10">
              <input type="text" name="modification_items" value="{{ modification_items }}" placeholder="{{ text_modification_items }}" id="input-modification_items" class="form-control" />
              {% if error_modification_items %}
                <div class="invalid-feedback">{{ error_modification_items }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-modifications">{{ text_modifications }}</label>
            <div class="col-sm-10">
              <input type="text" name="modifications" value="{{ modifications }}" placeholder="{{ text_modifications }}" id="input-modifications" class="form-control" />
              {% if error_modifications %}
                <div class="invalid-feedback">{{ error_modifications }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-order_id">{{ text_order_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="order_id" value="{{ order_id }}" placeholder="{{ text_order_id }}" id="input-order_id" class="form-control" />
              {% if error_order_id %}
                <div class="invalid-feedback">{{ error_order_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-order_info">{{ text_order_info }}</label>
            <div class="col-sm-10">
              <input type="text" name="order_info" value="{{ order_info }}" placeholder="{{ text_order_info }}" id="input-order_info" class="form-control" />
              {% if error_order_info %}
                <div class="invalid-feedback">{{ error_order_info }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-order_link">{{ text_order_link }}</label>
            <div class="col-sm-10">
              <input type="text" name="order_link" value="{{ order_link }}" placeholder="{{ text_order_link }}" id="input-order_link" class="form-control" />
              {% if error_order_link %}
                <div class="invalid-feedback">{{ error_order_link }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-order_options">{{ text_order_options }}</label>
            <div class="col-sm-10">
              <input type="text" name="order_options" value="{{ order_options }}" placeholder="{{ text_order_options }}" id="input-order_options" class="form-control" />
              {% if error_order_options %}
                <div class="invalid-feedback">{{ error_order_options }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-order_products">{{ text_order_products }}</label>
            <div class="col-sm-10">
              <input type="text" name="order_products" value="{{ order_products }}" placeholder="{{ text_order_products }}" id="input-order_products" class="form-control" />
              {% if error_order_products %}
                <div class="invalid-feedback">{{ error_order_products }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-order_totals">{{ text_order_totals }}</label>
            <div class="col-sm-10">
              <input type="text" name="order_totals" value="{{ order_totals }}" placeholder="{{ text_order_totals }}" id="input-order_totals" class="form-control" />
              {% if error_order_totals %}
                <div class="invalid-feedback">{{ error_order_totals }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-pagination">{{ text_pagination }}</label>
            <div class="col-sm-10">
              <input type="text" name="pagination" value="{{ pagination }}" placeholder="{{ text_pagination }}" id="input-pagination" class="form-control" />
              {% if error_pagination %}
                <div class="invalid-feedback">{{ error_pagination }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-results">{{ text_results }}</label>
            <div class="col-sm-10">
              <input type="text" name="results" value="{{ results }}" placeholder="{{ text_results }}" id="input-results" class="form-control" />
              {% if error_results %}
                <div class="invalid-feedback">{{ error_results }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="invalid-feedback">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}