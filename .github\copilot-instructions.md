# AYM ERP System — AI Agent Instruction File
**الإصدار 2.0 النهائي — بناءً على دراسة شاملة للوثائق المرجعية**

---

## Table of Contents

1. [Project Overview](#project-overview)
2. [Core Rules (الميثاق السيادي v5.1)](#core-rules)
3. [Modules Map (Structure & Routes)](#modules-map)
4. [Database Guide](#database-guide)
5. [Refactoring & Migration Plan](#refactoring--migration-plan)
6. [Development Tasks Checklist](#development-tasks-checklist)
7. [Final Roadmap](#final-roadmap)
8. [Appendices](#appendices)

---

## 1. Project Overview <a name="project-overview"></a>

### 1.1 Market Context & Pain Points

**Fragmented Toolchains:**
- Ecommerce storefronts (Shopify/WooCommerce) disconnected from accounting
- Internal communications via WhatsApp/Excel
- Inventory discrepancies & manual reconciliation

**Regulatory Complexity:**
- Mandatory ETA e‑invoicing in Egypt
- VAT, customs, landed costs compliance
- Social Insurance & payroll government reporting

**Operational Gaps:**
- No built‑in WAC (Weighted Average Cost) updating
- Lack of multi‑branch perpetual inventory
- No visual, low‑code workflow engine for approvals
- Minimal AI forecasting / fraud detection

### 1.2 AYM ERP Value Proposition

**Unified Platform:** Commerce + ERP + Compliance + AI

**Core Strengths:**
1. **Perpetual Inventory & WAC:** Real‑time cost updates with automated journal entries
2. **Multi‑branch Support:** Branch‑level snapshots & consolidated reports
3. **Visual Workflow Engine:** n8n‑style drag-drop approvals with deep ERP integration
4. **Central Services:** Notifications, Logging, Documents, AI Assistant
5. **ETA Compliance:** Native e‑invoice generation and transmission
6. **AI & Analytics:** Demand forecasting, anomaly detection, fraud prevention

### 1.3 High‑Level Architecture

**Base Framework:** OpenCart 3.0.3.x with Twig templating
- `header.twig` for quick actions
- `column_left.php` for menu structure
- `product.twig` for advanced interfaces

**Central Models:**
- `model/communication/unified_notification.php`
- `model/activity_log.php`
- `model/unified_document.php`
- `model/workflow/visual_workflow_engine.php`
- `model/accounts/journal.php`
- `model/ai/ai_assistant.php`

**Database:** Single schema (~5,500 lines in minidb.txt)

**AI Agent:** Trained on this instruction file + full codebase context

---

## 2. Core Rules (الميثاق السيادي v5.1) <a name="core-rules"></a>

### الباب الأول: بروتوكولات التعامل الآمن مع الكود

**تحليل الملف:**
- إذا >200 سطر → إظهار تحذير + استخراج الواجهات العامة + تبعية الملفات (`$this->load->...`)
- فحص الملفات المكررة والوظائف المتداخلة

**عدم الحذف:**
- Files → نقل إلى `/_deprecated/` + `filename.deprecated_YYYYMMDD.php`
- Tables → إعادة التسمية `tablename_deprecated_YYYYMMDD`
- إضافة تعليقات توضيحية للتغييرات

### الباب الثاني: الهندسة المعمارية والخدمات المركزية

**صلاحيات القائمة:**
```php
$this->user->hasPermission('access', 'route/path');
```
- المسارات يجب أن تتطابق مع `column_left.php`
- 212 permission check في النظام الحالي

**صلاحيات الإجراءات:**
```php
$this->user->hasKey('action_key');
```
- مفاتيح واضحة: `order_delete`, `po_approve_lvl1`, `inventory_adjust`
- 47 استخدام لـ `hasKey()` في النظام الحالي

**الخدمات المركزية (Authority):**
- **المحاسبة:** `model/accounts/journal.php`
- **الإشعارات:** `model/communication/unified_notification.php`
- **التدقيق:** `model/activity_log.php`
- **المستندات:** `model/unified_document.php`
- **الذكاء الاصطناعي:** `model/ai/ai_assistant.php`

### الباب الثالث: بنية البيانات وإدارة القاعدة

- راجع `minidb.txt` كاملاً قبل إضافة أو تعديل الجداول
- جمع كل تغييرات DDL (`ALTER`/`CREATE`) في `migrations.sql`
- جميع الجداول تبدأ بـ `cod_`

**الجداول المكررة المحددة:**
- `cod_vendor_payment` vs `cod_supplier_payments`
- `purchase_order` vs `order` modules

### الباب الرابع: الدورة المحاسبية الدقيقة

**WAC Formula:**
```
New_WAC = ((Old_Qty * Old_WAC) + (Received_Qty * Purchase_Price))
          / (Old_Qty + Received_Qty)
```

**فصل المخزون:**
- المخزون الفيزيائي: `cod_product_inventory`
- المخزون المتاح للمتجر: `cod_product_store`

**إنشاء قيود محاسبية فورية:**
- لكل GRN/Invoice/WAC adjustment
- تسجيل تلقائي في `cod_activity_log`

### الباب الخامس: API & Integrations

**تصميم موحد:**
- جميع Endpoints بـ JSON موحد
- دعم Migration Tools: Odoo, WooCommerce, Shopify, Excel
- 18 نقطة تكامل AI محددة

**ETA Integration:**
- `model/eta/invoices.php` للفواتير الإلكترونية
- تكامل مع SDK الضرائب المصرية

### الباب السادس: فصل الصلاحيات والمتجر

**الأدوار المحددة:**
- **مدير المستودع:** التحكم في الكميات والتكلفة
- **مدير المتجر:** التحكم في الأسعار والعرض
- **الكاشير:** عمليات البيع المحدودة

**واجهات مخصصة:**
- `product_form.twig` تُظهر/تخفي الحقول بناءً على الصلاحيات
- `header.twig` للعمليات السريعة

### الباب السابع: خارطة الطريق المنطقية

1. **Foundation:** Settings & Permissions
2. **Master Data:** Products, Accounts, Partners
3. **Core Flows:** Purchase → Sale → Inventory → Accounting
4. **Advanced:** AI, Omnichannel, Reports

### الباب الثامن: منهجية الإجابة النهائية

لكل مهمة، أرفق "مذكرة تصميم" تُجيب على:
1. المهمة & السياق (route في `column_left.php`)
2. تحليل الكود & الجداول + SQL إذا لزم
3. التأثير المتكامل (permissions, WAC, accounting, workflow)
4. خطة التنفيذ (services invoked + MVC sketch)

---

## 3. Modules Map (Structure & Routes) <a name="modules-map"></a>

> **ملاحظة:** الأرقام بين `[###]` تشير إلى رموز الـ route في `column_left.php` (5,300 سطر)

### 3.1 Dashboard & Quick Operations

- **[WEBSTORE]** عرض المتجر الإلكتروني
- **[001]** Main Dashboard
- **[001-1]** AYM ERP Dashboard

**[QUICK OPS]**
- [086] Quick Quote (sale/quote)
- [087] Quick Order (sale/order)
- [065] Quick GRN (purchase/goods_receipt)
- [012] Quick Journal Entry (accounts/journal)
- [273] Quick Account Query (accounts/account_query)

### 3.2 Sales & CRM [SALES]

**POS System:**
- [081] pos/pos
- [082] pos/shift
- [083] pos/cashier_handover
- [084] pos/reports
- [085] pos/settings

**Orders & Quotes:**
- [086] sale/quote
- [087] sale/order
- [088] sale/order_processing
- [089] shipping/prepare_orders
- [090] extension/eta/invoice

**CRM Management:**
- [095] Customers
- [096] Customer Groups
- [097] Loyalty Program
- [098] Credit Limits
- [099] Customer Notes
- [100] Support Tickets
- [101] Feedback
- [107] Pipeline Overview
- [108] Sales Dashboard
- [109] Leads Management
- [110] Opportunities
- [111] Deals
- [112] Campaigns
- [113] Contacts
- [114] Deals
- [115] Activities
- [116] CRM Analytics

### 3.3 Purchase & Procurement [PURCHASE]

**Core Procurement:**
- [117] Purchase Requisition
- [118] Purchase Orders
- [119] Supplier Management
- [120] Goods Receipt Notes
- [121] Supplier Invoices
- [122] Purchase Returns

**Supplier Relations:**
- [123] Supplier Payments
- [124] Supplier Statements
- [125] Supplier Performance
- [126] Purchase Analytics

### 3.4 Inventory & Warehouse [INVENTORY]

**Inventory Management:**
- [127] Product Catalog
- [128] Inventory Movements
- [129] Stock Adjustments
- [130] Warehouse Transfers
- [131] Unit Conversions
- [132] WAC Management

**Warehouse Operations:**
- [133] Multi-Branch Inventory
- [134] Warehouse Locations
- [135] Cycle Counting
- [136] Inventory Reports

### 3.5 Accounting & Finance [ACCOUNTING]

**General Ledger:**
- [137] Chart of Accounts
- [138] Journal Entries
- [139] Trial Balance
- [140] Financial Reports

**Accounts Payable:**
- [141] Vendor Bills
- [142] Payment Processing
- [143] AP Aging
- [144] AP Reports

**Accounts Receivable:**
- [145] Customer Invoices
- [146] Payment Collection
- [147] AR Aging
- [148] AR Reports

### 3.6 AI & Analytics [AI]

**Artificial Intelligence:**
- [149] AI Assistant
- [150] Demand Forecasting
- [151] Fraud Detection
- [152] Anomaly Detection
- [153] Predictive Analytics

**Business Intelligence:**
- [154] Custom Reports
- [155] Dashboard Builder
- [156] KPI Monitoring
- [157] Trend Analysis

### 3.7 Administration [ADMIN]

**System Management:**
- [158] User Management
- [159] Role Permissions
- [160] System Settings
- [161] Backup & Restore
- [162] System Logs

**Workflow & Automation:**
- [163] Visual Workflow Engine
- [164] Approval Workflows
- [165] Automated Tasks
- [166] Process Monitoring

---

## 4. Database Guide <a name="database-guide"></a>

### 4.1 Schema Overview

**Schema Source:** `minidb.txt` (~5,500 lines)

**Key Table Categories:**
- **Products:** `cod_product_inventory`, `cod_product_store`, `cod_product_*`
- **Accounts:** `cod_accounts`, `cod_account_description`, `cod_journal_*`
- **Workflow:** `cod_workflow_*`, `cod_approval_*`
- **Central Services:** `cod_unified_notification`, `cod_unified_document`, `cod_activity_log`
- **AI:** `cod_ai_*`, `cod_prediction_*`

**Naming Convention:** All tables start with `cod_`

**Migration Management:** All DDL changes tracked in `migrations.sql`

### 4.2 Critical Tables

**Inventory Tables:**
```sql
cod_product_inventory (
    product_id, branch_id, warehouse_id,
    quantity, average_cost, last_updated
)

cod_product_store (
    product_id, store_id,
    quantity, price, status
)
```

**Accounting Tables:**
```sql
cod_accounts (
    account_id, account_code, account_name,
    account_type, parent_id
)

cod_journal_entry (
    entry_id, date, reference,
    debit_account, credit_account, amount
)
```

**Workflow Tables:**
```sql
cod_workflow_definition (
    workflow_id, name, description,
    definition_json, status
)

cod_workflow_instance (
    instance_id, workflow_id, entity_id,
    current_step, status, created_date
)
```

---

## 5. Refactoring & Migration Plan <a name="refactoring--migration-plan"></a>

### 5.1 Identified Duplicates (from plan.md analysis)

**Module Duplicates:**
1. `purchase_order.php` ↔ `order.php`
2. `cod_vendor_payment` ↔ `cod_supplier_payments`

**Deprecated Elements:**
- 32 deprecated menu items marked with `// DEPRECATED`
- Old modules in `old/` directory
- Backup file: `column_left.php.backup_20250622_154650`

### 5.2 Consolidation Strategy

**Phase 1: Analysis & Backup**
1. Compare functionality of duplicate modules
2. Identify dependencies using `$this->load->...` patterns
3. Create comprehensive backup before changes

**Phase 2: Merge & Deprecate**
1. Merge duplicate functionalities
2. Move deprecated files to `/_deprecated/`
3. Update all references to use consolidated modules

**Phase 3: Database Cleanup**
1. Rename conflicting tables with `_deprecated_YYYYMMDD` suffix
2. Create migration scripts for data consolidation
3. Update all queries to use new table structure

**Phase 4: Menu & Routing Update**
1. Update `column_left.php` with consolidated structure
2. Remove deprecated menu items
3. Verify all permission checks

### 5.3 Migration Steps

```sql
-- Example Migration Script
-- File: migrations.sql

-- 1. Backup existing data
CREATE TABLE cod_vendor_payment_backup_20250712 AS SELECT * FROM cod_vendor_payment;

-- 2. Merge data into primary table
INSERT INTO cod_supplier_payments (...)
SELECT ... FROM cod_vendor_payment WHERE ...;

-- 3. Rename old table
ALTER TABLE cod_vendor_payment RENAME TO cod_vendor_payment_deprecated_20250712;

-- 4. Update references
UPDATE cod_workflow_definition 
SET definition_json = REPLACE(definition_json, 'cod_vendor_payment', 'cod_supplier_payments');
```

---

## 6. Development Tasks Checklist <a name="development-tasks-checklist"></a>

### 6.1 Central Services Implementation

**Unified Notification System:**
- [ ] Complete `model/communication/unified_notification.php`
- [ ] Implement multi-channel support (email, SMS, in-app)
- [ ] Integration with inventory alerts
- [ ] Integration with workflow approvals
- [ ] Integration with financial thresholds

**Visual Workflow Engine:**
- [ ] Expand node types (API calls, conditional logic, loops)
- [ ] Enhance drag-and-drop editor with snapping guides
- [ ] Deep integration with ERP modules
- [ ] Approval workflow templates
- [ ] Performance optimization for complex workflows

**Document Management System:**
- [ ] Centralized document storage
- [ ] Version control implementation
- [ ] Audit trail logging
- [ ] Integration with sales and HR modules
- [ ] Document search and retrieval

**AI Assistant Integration:**
- [ ] Complete `model/ai/ai_assistant.php`
- [ ] Model Context Protocol (MCP) integration
- [ ] Natural language query processing
- [ ] Contextual help system

### 6.2 Core Module Development

**Sales & CRM:**
- [ ] Order creation and tracking
- [ ] Customer relationship management
- [ ] Sales pipeline management
- [ ] Integration with inventory and accounting
- [ ] Mobile POS functionality

**Purchase & Procurement:**
- [ ] Purchase requisition workflow
- [ ] Purchase order approval system
- [ ] Goods receipt processing
- [ ] Supplier management
- [ ] Integration with accounting

**Inventory & Warehouse:**
- [ ] Perpetual inventory tracking
- [ ] WAC calculation automation
- [ ] Multi-branch inventory management
- [ ] Unit conversion system
- [ ] Warehouse location management

**Accounting & Finance:**
- [ ] General ledger automation
- [ ] Accounts payable/receivable
- [ ] Financial reporting
- [ ] Tax calculation and compliance
- [ ] ETA e-invoicing integration

### 6.3 Advanced Features

**AI & Analytics:**
- [ ] Demand forecasting models
- [ ] Fraud detection algorithms
- [ ] Customer behavior analysis
- [ ] Predictive maintenance
- [ ] Anomaly detection

**E-Commerce Integration:**
- [ ] Multi-channel inventory sync
- [ ] Dynamic pricing engine
- [ ] Order fulfillment automation
- [ ] Customer portal
- [ ] Mobile commerce support

**Compliance & Reporting:**
- [ ] Egyptian tax compliance
- [ ] Social insurance integration
- [ ] Custom report builder
- [ ] Audit trail reporting
- [ ] Regulatory compliance dashboard

### 6.4 Quality Assurance

**Testing Strategy:**
- [ ] Unit tests for all models
- [ ] Integration testing across modules
- [ ] Performance testing under load
- [ ] Security testing and penetration testing
- [ ] User acceptance testing

**Documentation:**
- [ ] API documentation
- [ ] User manuals
- [ ] Technical documentation
- [ ] Training materials
- [ ] System administration guide

### 6.5 Launch Preparation

**Deployment:**
- [ ] Production environment setup
- [ ] Database migration scripts
- [ ] System configuration
- [ ] Performance monitoring setup
- [ ] Backup and recovery procedures

**Training & Support:**
- [ ] User training programs
- [ ] Support documentation
- [ ] Help desk procedures
- [ ] System monitoring setup
- [ ] Performance metrics dashboard

---

## 7. Final Roadmap <a name="final-roadmap"></a>

### Phase 1: Foundation (Months 1-2)
**Objectives:**
- Complete system refactoring
- Implement central services
- Establish core permissions and settings

**Key Deliverables:**
- Consolidated module structure
- Working central services
- Initial setup wizard (`setting.twig`)
- Basic user management

### Phase 2: Core Operations (Months 3-4)
**Objectives:**
- Implement core business processes
- Complete inventory and accounting integration
- Establish workflow automation

**Key Deliverables:**
- Sales order processing
- Purchase order management
- Inventory tracking with WAC
- Basic accounting automation

### Phase 3: Advanced Features (Months 5-6)
**Objectives:**
- Implement AI and analytics
- Complete e-commerce integration
- Advanced workflow engine

**Key Deliverables:**
- AI forecasting and fraud detection
- Visual workflow engine
- Multi-channel e-commerce
- Advanced reporting

### Phase 4: Compliance & Launch (Months 7-8)
**Objectives:**
- Complete regulatory compliance
- Comprehensive testing
- Production deployment

**Key Deliverables:**
- ETA e-invoicing integration
- Full compliance with Egyptian regulations
- Complete documentation
- Production-ready system

---

## 8. Appendices <a name="appendices"></a>

### Appendix A: Permission Keys Catalog

**Menu Access Permissions:**
- Sales: `sale/order`, `sale/quote`, `sale/customer`
- Purchase: `purchase/order`, `purchase/supplier`, `purchase/goods_receipt`
- Inventory: `inventory/product`, `inventory/movement`, `inventory/adjustment`
- Accounting: `accounts/journal`, `accounts/reports`, `accounts/settings`

**Action Permissions:**
- Orders: `order_create`, `order_edit`, `order_delete`, `order_approve`
- Inventory: `inventory_adjust`, `inventory_transfer`, `wac_update`
- Accounting: `journal_create`, `journal_post`, `period_close`

### Appendix B: API Endpoint Reference

**Standard Response Format:**
```json
{
  "success": true,
  "data": { ... },
  "message": "Operation completed successfully",
  "timestamp": "2025-07-12T10:30:00Z"
}
```

**Error Response Format:**
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": { ... }
  },
  "timestamp": "2025-07-12T10:30:00Z"
}
```

### Appendix C: Migration Tools

**Supported Import Formats:**
- Odoo XML exports
- WooCommerce CSV exports
- Shopify CSV exports
- Excel templates
- Generic CSV with mapping

**Migration Process:**
1. Data validation and cleaning
2. Mapping to AYM ERP schema
3. Test import with sample data
4. Full data migration
5. Post-migration validation

### Appendix D: Competitive Analysis

**vs. Odoo:**
- Deeper local compliance (Egyptian regulations)
- Simpler, more intuitive UI
- Better performance for SMEs
- Integrated e-commerce without additional modules

**vs. WooCommerce:**
- Full ERP capabilities beyond e-commerce
- Multi-branch inventory management
- Advanced accounting integration
- Built-in workflow automation

**vs. Shopify:**
- Complete operational customization
- Integrated tax and payment systems
- No transaction fees
- Full data ownership and control

---

**End of Document**

> **Note:** This instruction file serves as the comprehensive guide for AI agents working on the AYM ERP system. All development should align with these guidelines to ensure consistency and quality.