{{ header }}
{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>

  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger">{{ error_warning }}</div>
    {% endif %}
    {% if success %}
    <div class="alert alert-success">{{ success }}</div>
    {% endif %}

    <div class="panel panel-default">
      <div class="panel-heading">
        <div class="panel-title">
          <i class="fa fa-file-text-o"></i> {{ text_list }}
        </div>
      </div>
      <div class="panel-body">

        <!-- Filters -->
        <form id="form-filter" class="form-inline" style="margin-bottom:15px;">
          <label for="filter_status">{{ entry_status }}</label>
          <select id="filter_status" class="form-control" style="margin-right:10px;">
            <option value="">{{ text_all_statuses }}</option>
            <option value="draft"       {{ filter_status=='draft'       ? 'selected':'' }}>{{ text_draft }}</option>
            <option value="active"      {{ filter_status=='active'      ? 'selected':'' }}>{{ text_active }}</option>
            <option value="expired"     {{ filter_status=='expired'     ? 'selected':'' }}>{{ text_expired }}</option>
            <option value="terminated"  {{ filter_status=='terminated'  ? 'selected':'' }}>{{ text_terminated }}</option>
          </select>
          <button type="button" id="btn-filter" class="btn btn-primary">
            <i class="fa fa-filter"></i> {{ button_filter }}
          </button>

          {% if can_add %}
          <button type="button" id="btn-add" class="btn btn-success" style="margin-left:20px;">
            <i class="fa fa-plus"></i> {{ button_add }}
          </button>
          {% endif %}
        </form>

        <!-- Table -->
        <table id="table-contract" class="table table-bordered table-striped">
          <thead>
            <tr>
              <th>{{ column_contract_id }}</th>
              <th>{{ column_contract_type }}</th>
              <th>{{ column_title }}</th>
              <th>{{ column_start_date }}</th>
              <th>{{ column_end_date }}</th>
              <th>{{ column_status }}</th>
              <th>{{ column_value }}</th>
              <th>{{ column_action }}</th>
            </tr>
          </thead>
          <tbody></tbody>
        </table>

      </div>
    </div>
  </div>
</div>

<!-- Modal: Add/Edit Contract -->
<div class="modal fade" id="modalContract" tabindex="-1" role="dialog" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <form id="formContract">
        <div class="modal-header">
          <h4 class="modal-title">{{ text_modal_add }}</h4>
          <button type="button" class="close" data-dismiss="modal">&times;</button>
        </div>

        <div class="modal-body">
          <input type="hidden" name="contract_id" id="contract_id" value="" />

          <div class="form-group">
            <label>{{ entry_contract_type }} <span style="color:red;">*</span></label>
            <input type="text" class="form-control" name="contract_type" id="contract_type" required />
          </div>

          <div class="form-group">
            <label>{{ entry_title }} <span style="color:red;">*</span></label>
            <input type="text" class="form-control" name="title" id="title" required />
          </div>

          <div class="form-group">
            <label>{{ entry_party_id }}</label>
            <input type="number" class="form-control" name="party_id" id="party_id" placeholder="e.g. Branch ID or Supplier ID" />
          </div>

          <div class="form-group">
            <label>{{ entry_start_date }} <span style="color:red;">*</span></label>
            <input type="date" class="form-control" name="start_date" id="start_date" required />
          </div>

          <div class="form-group">
            <label>{{ entry_end_date }}</label>
            <input type="date" class="form-control" name="end_date" id="end_date" />
          </div>

          <div class="form-group">
            <label>{{ entry_status }}</label>
            <select class="form-control" name="status" id="status">
              <option value="draft">{{ text_draft }}</option>
              <option value="active">{{ text_active }}</option>
              <option value="expired">{{ text_expired }}</option>
              <option value="terminated">{{ text_terminated }}</option>
            </select>
          </div>

          <div class="form-group">
            <label>{{ entry_value }}</label>
            <input type="number" step="0.01" class="form-control" name="value" id="value" value="0.00" />
          </div>

          <div class="form-group">
            <label>{{ entry_description }}</label>
            <textarea class="form-control" name="description" id="description" rows="3"></textarea>
          </div>
        </div>

        <div class="modal-footer">
          <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_close }}</button>
          <button type="submit" class="btn btn-primary">{{ button_save }}</button>
        </div>
      </form>
    </div>
  </div>
</div>

<script>
(function($){
  'use strict';
  let tableContract;

  $(document).ready(function(){
    // DataTable
    tableContract = $('#table-contract').DataTable({
      ajax:{
        url: "index.php?route=governance/legal_contract/ajaxList&user_token={{ user_token }}",
        type:"POST",
        data:function(d){
          d.filter_status = $('#filter_status').val();
        },
        dataSrc:function(json){
          if(json.error){
            toastr.error(json.error);
            return [];
          }
          return json.data;
        }
      },
      columns:[
        { data:'contract_id' },
        { data:'contract_type' },
        { data:'title' },
        { data:'start_date' },
        { data:'end_date' },
        { data:'status' },
        { data:'value' },
        {
          data:null,
          orderable:false,
          render:function(data,type,row){
            let btns='';
            {% if can_edit %}
            btns += `<button class="btn btn-info btn-sm btn-edit" data-id="${row.contract_id}">
                      <i class="fa fa-pencil"></i> {{ button_edit }}
                     </button> `;
            {% endif %}
            {% if can_delete %}
            btns += `<button class="btn btn-danger btn-sm btn-delete" data-id="${row.contract_id}">
                      <i class="fa fa-trash"></i> {{ button_delete }}
                     </button>`;
            {% endif %}
            return btns;
          }
        }
      ]
    });

    // Filter
    $('#btn-filter').on('click',function(){
      tableContract.ajax.reload();
    });

    // Add
    $('#btn-add').on('click',function(){
      clearForm();
      $('#contract_id').val('');
      $('#modalContract .modal-title').text('{{ text_modal_add }}');
      $('#modalContract').modal('show');
    });

    // Edit
    $('#table-contract').on('click','.btn-edit',function(){
      let cid = $(this).data('id');
      editContract(cid);
    });

    // Delete
    $('#table-contract').on('click','.btn-delete',function(){
      let cid = $(this).data('id');
      if(confirm('{{ text_confirm_delete }}')){
        $.ajax({
          url:"index.php?route=governance/legal_contract/ajaxDelete&user_token={{ user_token }}",
          type:"POST",
          data:{contract_id:cid},
          dataType:"json",
          success:function(json){
            if(json.error){ toastr.error(json.error); }
            else {
              toastr.success(json.success);
              tableContract.ajax.reload(null,false);
            }
          }
        });
      }
    });

    // Save (Add / Edit)
    $('#formContract').on('submit',function(e){
      e.preventDefault();
      let formData = $(this).serializeArray();
      let cid = $('#contract_id').val();
      let urlReq = '';
      if(cid==''){
        urlReq = "index.php?route=governance/legal_contract/ajaxAdd&user_token={{ user_token }}";
      } else {
        urlReq = "index.php?route=governance/legal_contract/ajaxEdit&user_token={{ user_token }}";
      }

      $.ajax({
        url:urlReq,
        type:'POST',
        data:formData,
        dataType:'json',
        success:function(json){
          if(json.error){
            toastr.error(json.error);
          } else {
            toastr.success(json.success);
            $('#modalContract').modal('hide');
            tableContract.ajax.reload(null,false);
          }
        }
      });
    });

  }); // end doc ready

  function editContract(contract_id){
    $.ajax({
      url:"index.php?route=governance/legal_contract/getOne&user_token={{ user_token }}",
      type:"POST",
      data:{contract_id},
      dataType:"json",
      success:function(json){
        if(json.error){
          toastr.error(json.error);
        } else if(json.success){
          let c = json.data;
          clearForm();
          $('#contract_id').val(c.contract_id);
          $('#contract_type').val(c.contract_type);
          $('#title').val(c.title);
          $('#party_id').val(c.party_id);
          if(c.start_date) $('#start_date').val(c.start_date);
          if(c.end_date) $('#end_date').val(c.end_date);
          $('#status').val(c.status);
          $('#value').val(c.value);
          $('#description').val(c.description);

          $('#modalContract .modal-title').text('{{ text_modal_edit }} #'+c.contract_id);
          $('#modalContract').modal('show');
        }
      }
    });
  }

  function clearForm(){
    $('#formContract')[0].reset();
    $('#contract_id').val('');
  }

})(jQuery);
</script>

{{ footer }}
