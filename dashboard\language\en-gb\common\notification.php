<?php
// Headings
$_['heading_title']          = 'Notifications';
$_['heading_messages']       = 'Messages';

// Text
$_['text_success']          = 'Success!';
$_['text_marked_read']      = 'Notification marked as read';
$_['text_all_marked_read']  = 'All notifications marked as read';
$_['text_notification_hidden'] = 'Notification hidden';
$_['text_notification_added'] = 'Notification added successfully';
$_['text_notifications_cleaned'] = 'Notifications older than %s days have been cleaned';
$_['text_no_notifications'] = 'No notifications to display';
$_['text_no_messages']      = 'No messages to display';
$_['text_just_now']        = 'Just now';
$_['text_minutes_ago']     = '%s minutes ago';
$_['text_hours_ago']       = '%s hours ago';
$_['text_days_ago']        = '%s days ago';
$_['text_yesterday']       = 'Yesterday';
$_['text_new_message']     = 'New Message';
$_['text_new_reply']       = 'New Reply';
$_['text_mark_all_read']   = 'Mark all as read';
$_['text_clear_all']       = 'Clear all';
$_['text_view_all']        = 'View all notifications';
$_['text_view_messages']   = 'View all messages';
$_['text_loading']         = 'Loading...';
$_['text_refresh']         = 'Refresh';

// Error
$_['error_permission']      = 'Warning: You do not have permission to modify notifications!';
$_['error_notification']    = 'Warning: Notification not found!';
$_['error_required_fields'] = 'Warning: Please check all required fields!';
$_['error_adding_notification'] = 'Warning: Error adding notification!';
$_['error_invalid_request'] = 'Warning: Invalid request!';

// Filter
$_['filter_all']           = 'All';
$_['filter_unread']        = 'Unread';
$_['filter_system']        = 'System';
$_['filter_alerts']        = 'Alerts';
$_['filter_messages']      = 'Messages';

// Categories
$_['category_system']      = 'System';
$_['category_order']       = 'Orders';
$_['category_customer']    = 'Customers';
$_['category_product']     = 'Products';
$_['category_security']    = 'Security';
$_['category_backup']      = 'Backup';
$_['category_update']      = 'Updates';

// Button
$_['button_mark_read']     = 'Mark as Read';
$_['button_delete']        = 'Delete';
$_['button_hide']          = 'Hide';
$_['button_view']          = 'View';
$_['button_reply']         = 'Reply';
$_['button_send']          = 'Send';
$_['button_close']         = 'Close';
$_['button_refresh']       = 'Refresh';
$_['button_clear_all']     = 'Clear All';
$_['button_load_more']     = 'Load More';

// Datetime format
$_['datetime_format']      = 'Y-m-d H:i:s';