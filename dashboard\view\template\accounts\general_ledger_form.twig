{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="submit" form="form-general-ledger" data-toggle="tooltip" title="{{ button_generate }}" class="btn btn-primary"><i class="fa fa-play"></i></button>
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-book"></i> {{ text_form }}</h3>
      </div>
      <div class="panel-body">
        <form action="{{ action }}" method="post" enctype="multipart/form-data" id="form-general-ledger" class="form-horizontal">
          
          <!-- فترة التقرير -->
          <div class="form-group">
            <label class="col-sm-2 control-label">{{ text_period }}</label>
            <div class="col-sm-10">
              <div class="row">
                <div class="col-sm-6">
                  <div class="input-group date">
                    <input type="text" name="date_start" value="{{ date_start }}" placeholder="{{ entry_date_start }}" data-date-format="YYYY-MM-DD" id="input-date-start" class="form-control" />
                    <span class="input-group-btn">
                      <button class="btn btn-default" type="button"><i class="fa fa-calendar"></i></button>
                    </span>
                  </div>
                  {% if error_date_start %}
                  <div class="text-danger">{{ error_date_start }}</div>
                  {% endif %}
                </div>
                <div class="col-sm-6">
                  <div class="input-group date">
                    <input type="text" name="date_end" value="{{ date_end }}" placeholder="{{ entry_date_end }}" data-date-format="YYYY-MM-DD" id="input-date-end" class="form-control" />
                    <span class="input-group-btn">
                      <button class="btn btn-default" type="button"><i class="fa fa-calendar"></i></button>
                    </span>
                  </div>
                  {% if error_date_end %}
                  <div class="text-danger">{{ error_date_end }}</div>
                  {% endif %}
                </div>
              </div>
            </div>
          </div>

          <!-- فلترة الحسابات -->
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-account">{{ entry_account }}</label>
            <div class="col-sm-10">
              <select name="account_id" id="input-account" class="form-control select2">
                <option value="">{{ text_all_accounts }}</option>
                {% for account in accounts %}
                <option value="{{ account.account_id }}"{% if account.account_id == account_id %} selected="selected"{% endif %}>{{ account.code }} - {{ account.name }}</option>
                {% endfor %}
              </select>
            </div>
          </div>

          <!-- نوع الحساب -->
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-account-type">{{ entry_account_type }}</label>
            <div class="col-sm-10">
              <select name="account_type" id="input-account-type" class="form-control">
                <option value="">{{ text_all_types }}</option>
                <option value="asset"{% if account_type == 'asset' %} selected="selected"{% endif %}>{{ text_asset }}</option>
                <option value="liability"{% if account_type == 'liability' %} selected="selected"{% endif %}>{{ text_liability }}</option>
                <option value="equity"{% if account_type == 'equity' %} selected="selected"{% endif %}>{{ text_equity }}</option>
                <option value="revenue"{% if account_type == 'revenue' %} selected="selected"{% endif %}>{{ text_revenue }}</option>
                <option value="expense"{% if account_type == 'expense' %} selected="selected"{% endif %}>{{ text_expense }}</option>
              </select>
            </div>
          </div>

          <!-- مركز التكلفة -->
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-cost-center">{{ entry_cost_center }}</label>
            <div class="col-sm-10">
              <select name="cost_center_id" id="input-cost-center" class="form-control select2">
                <option value="">{{ text_all_cost_centers }}</option>
                {% for cost_center in cost_centers %}
                <option value="{{ cost_center.cost_center_id }}"{% if cost_center.cost_center_id == cost_center_id %} selected="selected"{% endif %}>{{ cost_center.name }}</option>
                {% endfor %}
              </select>
            </div>
          </div>

          <!-- الفرع -->
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-branch">{{ entry_branch }}</label>
            <div class="col-sm-10">
              <select name="branch_id" id="input-branch" class="form-control select2">
                <option value="">{{ text_all_branches }}</option>
                {% for branch in branches %}
                <option value="{{ branch.branch_id }}"{% if branch.branch_id == branch_id %} selected="selected"{% endif %}>{{ branch.name }}</option>
                {% endfor %}
              </select>
            </div>
          </div>

          <!-- خيارات العرض -->
          <div class="form-group">
            <label class="col-sm-2 control-label">{{ text_display_options }}</label>
            <div class="col-sm-10">
              <div class="checkbox">
                <label>
                  <input type="checkbox" name="show_zero_balances" value="1"{% if show_zero_balances %} checked="checked"{% endif %} />
                  {{ text_show_zero_balances }}
                </label>
              </div>
              <div class="checkbox">
                <label>
                  <input type="checkbox" name="show_transactions" value="1"{% if show_transactions %} checked="checked"{% endif %} />
                  {{ text_show_transactions }}
                </label>
              </div>
              <div class="checkbox">
                <label>
                  <input type="checkbox" name="group_by_type" value="1"{% if group_by_type %} checked="checked"{% endif %} />
                  {{ text_group_by_type }}
                </label>
              </div>
            </div>
          </div>

          <!-- خيارات التصدير -->
          <div class="form-group">
            <label class="col-sm-2 control-label">{{ text_export_options }}</label>
            <div class="col-sm-10">
              <div class="btn-group" data-toggle="buttons">
                <label class="btn btn-default active">
                  <input type="radio" name="action_type" value="view" checked> {{ text_view_report }}
                </label>
                <label class="btn btn-default">
                  <input type="radio" name="action_type" value="export_excel"> {{ text_export_excel }}
                </label>
                <label class="btn btn-default">
                  <input type="radio" name="action_type" value="export_pdf"> {{ text_export_pdf }}
                </label>
                <label class="btn btn-default">
                  <input type="radio" name="action_type" value="export_csv"> {{ text_export_csv }}
                </label>
              </div>
            </div>
          </div>

        </form>
      </div>
    </div>

    <!-- معلومات إضافية -->
    <div class="panel panel-info">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-info-circle"></i> {{ text_about_general_ledger }}</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-md-6">
            <h4>{{ text_what_is_general_ledger }}</h4>
            <p>{{ help_general_ledger }}</p>
            
            <h4>{{ text_features }}</h4>
            <ul>
              <li>{{ text_feature_1 }}</li>
              <li>{{ text_feature_2 }}</li>
              <li>{{ text_feature_3 }}</li>
              <li>{{ text_feature_4 }}</li>
            </ul>
          </div>
          <div class="col-md-6">
            <h4>{{ text_compliance }}</h4>
            <div class="alert alert-success">
              <i class="fa fa-check-circle"></i> {{ text_eas_compliant }}
            </div>
            <div class="alert alert-info">
              <i class="fa fa-globe"></i> {{ text_eta_ready }}
            </div>
            <div class="alert alert-warning">
              <i class="fa fa-balance-scale"></i> {{ text_egyptian_gaap }}
            </div>
          </div>
        </div>
      </div>
    </div>

  </div>
</div>

<script type="text/javascript"><!--
$('#input-date-start, #input-date-end').datetimepicker({
    language: '{{ datepicker }}',
    pickTime: false
});

$('.select2').select2({
    placeholder: '{{ text_select }}',
    allowClear: true
});

// تحديث الفلاتر تلقائياً
$('#input-account-type').on('change', function() {
    var accountType = $(this).val();
    if (accountType) {
        // يمكن إضافة AJAX لتحديث قائمة الحسابات حسب النوع
    }
});

// التحقق من صحة النموذج
$('#form-general-ledger').on('submit', function(e) {
    var dateStart = $('#input-date-start').val();
    var dateEnd = $('#input-date-end').val();
    
    if (dateStart && dateEnd) {
        if (new Date(dateStart) > new Date(dateEnd)) {
            alert('{{ error_date_range }}');
            e.preventDefault();
            return false;
        }
    }
});
//--></script>

{{ footer }}
