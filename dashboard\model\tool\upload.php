<?php
class ModelToolUpload extends Model {
	public function addUpload($name, $filename) {
		try {
			// التحقق من صحة اسم الملف وتنظيفه
			$filename = $this->sanitizeFilename($filename);
			
			// إنشاء اسم فريد للملف لمنع التعارض
			$unique_filename = substr(md5(mt_rand()), 0, 8) . '_' . $filename;
			
			// إنشاء رمز فريد للملف
			$code = sha1(uniqid(mt_rand(), true));

			// التأكد من أن مجلد الرفع موجود وقابل للكتابة
			if (!is_dir(DIR_UPLOAD) || !is_writable(DIR_UPLOAD)) {
				throw new \Exception('مجلد الرفع غير موجود أو غير قابل للكتابة');
			}

			// حفظ معلومات الملف في قاعدة البيانات
			$this->db->query("INSERT INTO `" . DB_PREFIX . "upload` SET 
				`name` = '" . $this->db->escape($name) . "', 
				`filename` = '" . $this->db->escape($unique_filename) . "', 
				`code` = '" . $this->db->escape($code) . "', 
				`date_added` = NOW()");

			return $code;
		} catch (\Exception $e) {
			if ($this->config->get('config_error_log')) {
				$this->log->write('خطأ في إضافة ملف: ' . $e->getMessage());
			}
			throw $e;
		}
	}
		
	/**
	 * حذف ملف محمل مع التأكد من وجوده أولاً
	 */
	public function deleteUpload($upload_id) {
		// الحصول على معلومات الملف قبل الحذف
		$upload_info = $this->getUpload($upload_id);
		
		if ($upload_info) {
			// حذف الملف الفعلي إذا كان موجوداً
			$file_path = DIR_UPLOAD . $upload_info['filename'];
			if (file_exists($file_path)) {
				unlink($file_path);
			}
			
			// حذف السجل من قاعدة البيانات
			$this->db->query("DELETE FROM " . DB_PREFIX . "upload WHERE upload_id = '" . (int)$upload_id . "'");
			return true;
		}
		
		return false;
	}

	public function getUpload($upload_id) {
		$query = $this->db->query("SELECT * FROM `" . DB_PREFIX . "upload` WHERE upload_id = '" . (int)$upload_id . "'");

		return $query->row;
	}

	public function getUploadByCode($code) {
		$query = $this->db->query("SELECT * FROM " . DB_PREFIX . "upload WHERE code = '" . $this->db->escape($code) . "'");

		return $query->row;
	}

	public function getUploads($data = array()) {
		$sql = "SELECT * FROM " . DB_PREFIX . "upload";

		$implode = array();

		if (!empty($data['filter_name'])) {
			$implode[] = "name LIKE '" . $this->db->escape($data['filter_name']) . "%'";
		}

		if (!empty($data['filter_filename'])) {
			$implode[] = "filename LIKE '" . $this->db->escape($data['filter_filename']) . "%'";
		}

		if (!empty($data['filter_date_added'])) {
			$implode[] = "DATE(date_added) = DATE('" . $this->db->escape($data['filter_date_added']) . "')";
		}

		if ($implode) {
			$sql .= " WHERE " . implode(" AND ", $implode);
		}

		$sort_data = array(
			'name',
			'filename',
			'date_added'
		);

		if (isset($data['sort']) && in_array($data['sort'], $sort_data)) {
			$sql .= " ORDER BY " . $data['sort'];
		} else {
			$sql .= " ORDER BY date_added";
		}

		if (isset($data['order']) && ($data['order'] == 'DESC')) {
			$sql .= " DESC";
		} else {
			$sql .= " ASC";
		}

		if (isset($data['start']) || isset($data['limit'])) {
			if ($data['start'] < 0) {
				$data['start'] = 0;
			}

			if ($data['limit'] < 1) {
				$data['limit'] = 20;
			}

			$sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
		}

		$query = $this->db->query($sql);

		return $query->rows;
	}

	public function getTotalUploads($data = array()) {
		$sql = "SELECT COUNT(*) AS total FROM " . DB_PREFIX . "upload";

		$implode = array();

		if (!empty($data['filter_name'])) {
			$implode[] = "name LIKE '" . $this->db->escape($data['filter_name']) . "%'";
		}

		if (!empty($data['filter_filename'])) {
			$implode[] = "filename LIKE '" . $this->db->escape($data['filter_filename']) . "%'";
		}

		if (!empty($data['filter_date_added'])) {
			$implode[] = "DATE(date_added) = DATE('" . $this->db->escape($data['filter_date_added']) . "')";
		}

		if ($implode) {
			$sql .= " WHERE " . implode(" AND ", $implode);
		}

		$query = $this->db->query($sql);

		return $query->row['total'];
	}

	/**
	 * تنظيف اسم الملف وإزالة الأحرف غير الآمنة
	 */
	private function sanitizeFilename($filename) {
		// إزالة المسارات
		$filename = basename($filename);
		
		// إزالة الأحرف الخاصة والمسافات الزائدة
		$filename = preg_replace('/[^\w\-\.]+/', '_', $filename);
		
		// التأكد من امتداد الملف آمن
		$extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
		$allowed_extensions = ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx', 'xls', 'xlsx', 'txt', 'zip', 'rar'];
		
		if (!in_array($extension, $allowed_extensions)) {
			// تحويل امتداد غير آمن إلى txt
			$filename = pathinfo($filename, PATHINFO_FILENAME) . '.txt';
		}
		
		return $filename;
	}
	
	/**
	 * التحقق من الملف قبل الرفع
	 */
	public function validateUpload($file) {
		// التحقق من حجم الملف (10 ميجا كحد أقصى)
		$max_size = 10 * 1024 * 1024; // 10MB
		if ($file['size'] > $max_size) {
			throw new Exception('حجم الملف كبير جداً. الحد الأقصى هو 10 ميجابايت.');
		}
		
		// التحقق من نوع الملف
		$allowed_mime_types = [
			'image/jpeg', 'image/png', 'image/gif', 
			'application/pdf', 
			'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
			'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
			'text/plain',
			'application/zip', 'application/x-rar-compressed'
		];
		
		$finfo = new finfo(FILEINFO_MIME_TYPE);
		$mime_type = $finfo->file($file['tmp_name']);
		
		if (!in_array($mime_type, $allowed_mime_types)) {
			throw new Exception('نوع الملف غير مسموح به.');
		}
		
		// التحقق من امتداد الملف
		$extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
		$allowed_extensions = ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx', 'xls', 'xlsx', 'txt', 'zip', 'rar'];
		
		if (!in_array($extension, $allowed_extensions)) {
			throw new Exception('امتداد الملف غير مسموح به.');
		}
		
		return true;
	}
}
