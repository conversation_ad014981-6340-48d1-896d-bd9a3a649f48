{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="submit" form="workflow\task-form" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fa fa-save"></i></button>
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="close" data-dismiss="alert">&times;</button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="close" data-dismiss="alert">&times;</button>
      </div>
    {{% endif %}}
    
    <div class="panel panel-default">
      <div class="panel-heading"><i class="fa fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}bar-chart{% elif screen_type == 'settings' %}cog{% else %}pencil{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="panel-body">
        <form id="workflow\task-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-add">{{ text_add }}</label>
            <div class="col-sm-10">
              <input type="text" name="add" value="{{ add }}" placeholder="{{ text_add }}" id="input-add" class="form-control" />
              {% if error_add %}
                <div class="text-danger">{{ error_add }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-assignee_id">{{ text_assignee_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="assignee_id" value="{{ assignee_id }}" placeholder="{{ text_assignee_id }}" id="input-assignee_id" class="form-control" />
              {% if error_assignee_id %}
                <div class="text-danger">{{ error_assignee_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-delete">{{ text_delete }}</label>
            <div class="col-sm-10">
              <input type="text" name="delete" value="{{ delete }}" placeholder="{{ text_delete }}" id="input-delete" class="form-control" />
              {% if error_delete %}
                <div class="text-danger">{{ error_delete }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-description">{{ text_description }}</label>
            <div class="col-sm-10">
              <input type="text" name="description" value="{{ description }}" placeholder="{{ text_description }}" id="input-description" class="form-control" />
              {% if error_description %}
                <div class="invalid-feedback">{{ error_description }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-due_date">{{ text_due_date }}</label>
            <div class="col-sm-10">
              <input type="text" name="due_date" value="{{ due_date }}" placeholder="{{ text_due_date }}" id="input-due_date" class="form-control" />
              {% if error_due_date %}
                <div class="invalid-feedback">{{ error_due_date }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-estimated_hours">{{ text_estimated_hours }}</label>
            <div class="col-sm-10">
              <input type="text" name="estimated_hours" value="{{ estimated_hours }}" placeholder="{{ text_estimated_hours }}" id="input-estimated_hours" class="form-control" />
              {% if error_estimated_hours %}
                <div class="invalid-feedback">{{ error_estimated_hours }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="invalid-feedback">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-priority">{{ text_priority }}</label>
            <div class="col-sm-10">
              <input type="text" name="priority" value="{{ priority }}" placeholder="{{ text_priority }}" id="input-priority" class="form-control" />
              {% if error_priority %}
                <div class="invalid-feedback">{{ error_priority }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-status">{{ text_status }}</label>
            <div class="col-sm-10">
              <input type="text" name="status" value="{{ status }}" placeholder="{{ text_status }}" id="input-status" class="form-control" />
              {% if error_status %}
                <div class="invalid-feedback">{{ error_status }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-tags">{{ text_tags }}</label>
            <div class="col-sm-10">
              <input type="text" name="tags" value="{{ tags }}" placeholder="{{ text_tags }}" id="input-tags" class="form-control" />
              {% if error_tags %}
                <div class="invalid-feedback">{{ error_tags }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-task">{{ text_task }}</label>
            <div class="col-sm-10">
              <input type="text" name="task" value="{{ task }}" placeholder="{{ text_task }}" id="input-task" class="form-control" />
              {% if error_task %}
                <div class="invalid-feedback">{{ error_task }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-task_comments">{{ text_task_comments }}</label>
            <div class="col-sm-10">
              <input type="text" name="task_comments" value="{{ task_comments }}" placeholder="{{ text_task_comments }}" id="input-task_comments" class="form-control" />
              {% if error_task_comments %}
                <div class="invalid-feedback">{{ error_task_comments }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-task_history">{{ text_task_history }}</label>
            <div class="col-sm-10">
              <input type="text" name="task_history" value="{{ task_history }}" placeholder="{{ text_task_history }}" id="input-task_history" class="form-control" />
              {% if error_task_history %}
                <div class="invalid-feedback">{{ error_task_history }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-tasks">{{ text_tasks }}</label>
            <div class="col-sm-10">
              <input type="text" name="tasks" value="{{ tasks }}" placeholder="{{ text_tasks }}" id="input-tasks" class="form-control" />
              {% if error_tasks %}
                <div class="invalid-feedback">{{ error_tasks }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_form">{{ text_text_form }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_form" value="{{ text_form }}" placeholder="{{ text_text_form }}" id="input-text_form" class="form-control" />
              {% if error_text_form %}
                <div class="invalid-feedback">{{ error_text_form }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-title">{{ text_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="title" value="{{ title }}" placeholder="{{ text_title }}" id="input-title" class="form-control" />
              {% if error_title %}
                <div class="invalid-feedback">{{ error_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="invalid-feedback">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}