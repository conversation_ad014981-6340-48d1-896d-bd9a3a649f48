{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <a href="{{ start_shift }}" data-toggle="tooltip" title="{{ button_start_shift }}" class="btn btn-primary"><i class="fa fa-plus"></i></a>
      </div>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if success %}
    <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    {% if error %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    
    {% if active_shift %}
    <div class="alert alert-info"><i class="fa fa-info-circle"></i> {{ text_active_shift }} - {{ active_shift.terminal_name }} ({{ active_shift.branch_name }})
      <div class="pull-right">
        <a href="{{ end_shift }}?shift_id={{ active_shift.shift_id }}" class="btn btn-warning btn-sm">{{ button_end_shift }}</a>
      </div>
    </div>
    {% endif %}
    
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-list"></i> {{ text_list }}</h3>
      </div>
      <div class="panel-body">
        <div class="table-responsive">
          <table class="table table-bordered table-hover">
            <thead>
              <tr>
                <td class="text-left">{{ column_user }}</td>
                <td class="text-left">{{ column_terminal }}</td>
                <td class="text-left">{{ column_branch }}</td>
                <td class="text-left">{{ column_start_time }}</td>
                <td class="text-left">{{ column_end_time }}</td>
                <td class="text-right">{{ column_starting_cash }}</td>
                <td class="text-right">{{ column_ending_cash }}</td>
                <td class="text-center">{{ column_status }}</td>
                <td class="text-right">{{ column_action }}</td>
              </tr>
            </thead>
            <tbody>
              {% if shifts %}
              {% for shift in shifts %}
              <tr>
                <td class="text-left">{{ shift.user_name }}</td>
                <td class="text-left">{{ shift.terminal_name }}</td>
                <td class="text-left">{{ shift.branch_name }}</td>
                <td class="text-left">{{ shift.start_time }}</td>
                <td class="text-left">{{ shift.end_time }}</td>
                <td class="text-right">{{ shift.starting_cash }}</td>
                <td class="text-right">{{ shift.ending_cash }}</td>
                <td class="text-center">
                  {% if shift.status == text_status_active %}
                  <span class="label label-success">{{ shift.status }}</span>
                  {% elseif shift.status == text_status_closed %}
                  <span class="label label-warning">{{ shift.status }}</span>
                  {% else %}
                  <span class="label label-info">{{ shift.status }}</span>
                  {% endif %}
                </td>
                <td class="text-right">
                  <a href="{{ shift.view }}" data-toggle="tooltip" title="{{ button_view }}" class="btn btn-info"><i class="fa fa-eye"></i></a>
                  {% if shift.status == text_status_active %}
                  <a href="{{ shift.end }}" data-toggle="tooltip" title="{{ button_end }}" class="btn btn-warning"><i class="fa fa-stop"></i></a>
                  {% endif %}
                </td>
              </tr>
              {% endfor %}
              {% else %}
              <tr>
                <td class="text-center" colspan="9">{{ text_no_results }}</td>
              </tr>
              {% endif %}
            </tbody>
          </table>
        </div>
        <div class="row">
          <div class="col-sm-6 text-left">{{ pagination }}</div>
          <div class="col-sm-6 text-right">{{ results }}</div>
        </div>
      </div>
    </div>
  </div>
</div>
{{ footer }}