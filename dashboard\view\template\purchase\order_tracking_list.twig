{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="button" data-toggle="tooltip" title="{{ button_filter }}" onclick="$('#filter-order').toggleClass('hidden-sm hidden-xs');" class="btn btn-default hidden-md hidden-lg"><i class="fa fa-filter"></i></button>
        <a href="{{ refresh }}" data-toggle="tooltip" title="{{ button_refresh }}" class="btn btn-default"><i class="fa fa-refresh"></i></a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    {% if success %}
    <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-list"></i> {{ text_list }}</h3>
      </div>
      <div class="panel-body">
        <div class="well hidden-sm hidden-xs" id="filter-order">
          <div class="row">
            <div class="col-sm-6">
              <div class="form-group">
                <label class="control-label" for="input-po-number">{{ entry_po_number }}</label>
                <input type="text" name="filter_po_number" value="{{ filter_po_number }}" placeholder="{{ entry_po_number }}" id="input-po-number" class="form-control" />
              </div>
              <div class="form-group">
                <label class="control-label" for="input-supplier">{{ entry_supplier }}</label>
                <select name="filter_supplier_id" id="input-supplier" class="form-control">
                  <option value="">{{ text_select }}</option>
                  {% for supplier in suppliers %}
                  {% if supplier.supplier_id == filter_supplier_id %}
                  <option value="{{ supplier.supplier_id }}" selected="selected">{{ supplier.name }}</option>
                  {% else %}
                  <option value="{{ supplier.supplier_id }}">{{ supplier.name }}</option>
                  {% endif %}
                  {% endfor %}
                </select>
              </div>
            </div>
            <div class="col-sm-6">
              <div class="form-group">
                <label class="control-label" for="input-status">{{ entry_status }}</label>
                <select name="filter_status" id="input-status" class="form-control">
                  <option value="">{{ text_select }}</option>
                  {% for status in statuses %}
                  {% if status.value == filter_status %}
                  <option value="{{ status.value }}" selected="selected">{{ status.text }}</option>
                  {% else %}
                  <option value="{{ status.value }}">{{ status.text }}</option>
                  {% endif %}
                  {% endfor %}
                </select>
              </div>
              <div class="form-group">
                <label class="control-label" for="input-date-start">{{ entry_date_start }}</label>
                <div class="input-group date">
                  <input type="text" name="filter_date_start" value="{{ filter_date_start }}" placeholder="{{ entry_date_start }}" data-date-format="YYYY-MM-DD" id="input-date-start" class="form-control" />
                  <span class="input-group-btn">
                  <button type="button" class="btn btn-default"><i class="fa fa-calendar"></i></button>
                  </span>
                </div>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-sm-6">
              <div class="form-group">
                <label class="control-label" for="input-date-end">{{ entry_date_end }}</label>
                <div class="input-group date">
                  <input type="text" name="filter_date_end" value="{{ filter_date_end }}" placeholder="{{ entry_date_end }}" data-date-format="YYYY-MM-DD" id="input-date-end" class="form-control" />
                  <span class="input-group-btn">
                  <button type="button" class="btn btn-default"><i class="fa fa-calendar"></i></button>
                  </span>
                </div>
              </div>
            </div>
            <div class="col-sm-6">
              <div class="form-group">
                <label class="control-label">&nbsp;</label>
                <div>
                  <button type="button" id="button-filter" class="btn btn-primary pull-right"><i class="fa fa-search"></i> {{ button_filter }}</button>
                </div>
              </div>
            </div>
          </div>
        </div>
        <form method="post" enctype="multipart/form-data" id="form-order">
          <div class="table-responsive">
            <table class="table table-bordered table-hover">
              <thead>
                <tr>
                  <td style="width: 1px;" class="text-center"><input type="checkbox" onclick="$('input[name*=\'selected\']').prop('checked', this.checked);" /></td>
                  <td class="text-left">{% if sort == 'po.po_number' %}<a href="{{ sort_po_number }}" class="{% if order == 'ASC' %}asc{% else %}desc{% endif %}">{{ column_po_number }}</a>{% else %}<a href="{{ sort_po_number }}">{{ column_po_number }}</a>{% endif %}</td>
                  <td class="text-left">{% if sort == 'supplier_name' %}<a href="{{ sort_supplier }}" class="{% if order == 'ASC' %}asc{% else %}desc{% endif %}">{{ column_supplier }}</a>{% else %}<a href="{{ sort_supplier }}">{{ column_supplier }}</a>{% endif %}</td>
                  <td class="text-left">{% if sort == 'po.order_date' %}<a href="{{ sort_order_date }}" class="{% if order == 'ASC' %}asc{% else %}desc{% endif %}">{{ column_order_date }}</a>{% else %}<a href="{{ sort_order_date }}">{{ column_order_date }}</a>{% endif %}</td>
                  <td class="text-left">{{ column_expected_delivery }}</td>
                  <td class="text-left">{{ column_actual_delivery }}</td>
                  <td class="text-left">{{ column_status }}</td>
                  <td class="text-left">{{ column_current_status }}</td>
                  <td class="text-right">{{ column_total }}</td>
                  <td class="text-right">{{ column_action }}</td>
                </tr>
              </thead>
              <tbody>
                {% if orders %}
                {% for order in orders %}
                <tr class="{% if order.status == 'overdue' %}danger{% elseif order.status == 'upcoming' %}warning{% endif %}">
                  <td class="text-center">{% if order.selected %}<input type="checkbox" name="selected[]" value="{{ order.po_id }}" checked="checked" />{% else %}<input type="checkbox" name="selected[]" value="{{ order.po_id }}" />{% endif %}</td>
                  <td class="text-left">{{ order.po_number }}</td>
                  <td class="text-left">{{ order.supplier }}</td>
                  <td class="text-left">{{ order.order_date }}</td>
                  <td class="text-left">{{ order.expected_delivery }}</td>
                  <td class="text-left">{{ order.actual_delivery }}</td>
                  <td class="text-left">
                    <span class="label label-{% if order.status == 'approved' %}success{% elseif order.status == 'pending' %}warning{% elseif order.status == 'cancelled' %}danger{% else %}default{% endif %}">{{ order.status }}</span>
                  </td>
                  <td class="text-left">
                    <span class="label label-{% if order.current_status == 'fully_received' %}success{% elseif order.current_status == 'partially_received' %}info{% elseif order.current_status == 'cancelled' %}danger{% else %}default{% endif %}">{{ order.current_status }}</span>
                  </td>
                  <td class="text-right">{{ order.total_amount }}</td>
                  <td class="text-right">
                    <div class="btn-group">
                      <a href="{{ order.view }}" data-toggle="tooltip" title="{{ button_view }}" class="btn btn-primary btn-sm"><i class="fa fa-eye"></i></a>
                      <a href="{{ order.update }}" data-toggle="tooltip" title="{{ button_update }}" class="btn btn-info btn-sm"><i class="fa fa-pencil"></i></a>
                    </div>
                  </td>
                </tr>
                {% endfor %}
                {% else %}
                <tr>
                  <td class="text-center" colspan="10">{{ text_no_results }}</td>
                </tr>
                {% endif %}
              </tbody>
            </table>
          </div>
        </form>
        <div class="row">
          <div class="col-sm-6 text-left">{{ pagination }}</div>
          <div class="col-sm-6 text-right">{{ results }}</div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Quick Status Update Modal -->
<div id="modal-status-update" class="modal fade" tabindex="-1" role="dialog">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
        <h4 class="modal-title">{{ button_update_status }}</h4>
      </div>
      <div class="modal-body">
        <form id="form-status-update">
          <input type="hidden" name="po_id" id="status-po-id" value="" />
          <div class="form-group">
            <label class="control-label" for="status-change">{{ entry_status_change }}</label>
            <select name="status_change" id="status-change" class="form-control">
              {% for status in statuses %}
              <option value="{{ status.value }}">{{ status.text }}</option>
              {% endfor %}
            </select>
          </div>
          <div class="form-group">
            <label class="control-label" for="status-notes">{{ entry_notes }}</label>
            <textarea name="notes" id="status-notes" rows="3" class="form-control"></textarea>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_cancel }}</button>
        <button type="button" class="btn btn-primary" onclick="updateStatus()">{{ button_update }}</button>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript"><!--
$('#button-filter').on('click', function() {
    var url = 'index.php?route=purchase/order_tracking&user_token={{ user_token }}';
    
    var filter_po_number = $('input[name=\'filter_po_number\']').val();
    
    if (filter_po_number) {
        url += '&filter_po_number=' + encodeURIComponent(filter_po_number);
    }

    var filter_supplier_id = $('select[name=\'filter_supplier_id\']').val();
    
    if (filter_supplier_id !== '') {
        url += '&filter_supplier_id=' + filter_supplier_id;
    }
    
    var filter_status = $('select[name=\'filter_status\']').val();
    
    if (filter_status !== '') {
        url += '&filter_status=' + filter_status;
    }

    var filter_date_start = $('input[name=\'filter_date_start\']').val();
    
    if (filter_date_start) {
        url += '&filter_date_start=' + encodeURIComponent(filter_date_start);
    }

    var filter_date_end = $('input[name=\'filter_date_end\']').val();
    
    if (filter_date_end) {
        url += '&filter_date_end=' + encodeURIComponent(filter_date_end);
    }
    
    location = url;
});

$('input[name=\'filter_po_number\']').autocomplete({
    'source': function(request, response) {
        $.ajax({
            url: 'index.php?route=purchase/order_tracking/autocomplete&user_token={{ user_token }}&filter_po_number=' + encodeURIComponent(request),
            dataType: 'json',
            success: function(json) {
                response($.map(json, function(item) {
                    return {
                        label: item['po_number'],
                        value: item['po_id']
                    }
                }));
            }
        });
    },
    'select': function(item) {
        $('input[name=\'filter_po_number\']').val(item['label']);
    }
});

$('.date').datetimepicker({
    language: '{{ datepicker }}',
    pickTime: false
});

function updateStatus() {
    $.ajax({
        url: 'index.php?route=purchase/order_tracking/ajaxUpdateStatus&user_token={{ user_token }}',
        type: 'post',
        data: $('#form-status-update').serialize(),
        dataType: 'json',
        beforeSend: function() {
            $('#modal-status-update .btn-primary').button('loading');
        },
        complete: function() {
            $('#modal-status-update .btn-primary').button('reset');
        },
        success: function(json) {
            $('.alert-dismissible').remove();
            
            if (json['error']) {
                $('#content > .container-fluid').prepend('<div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> ' + json['error'] + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
            }
            
            if (json['success']) {
                $('#content > .container-fluid').prepend('<div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> ' + json['success'] + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
                
                $('#modal-status-update').modal('hide');
                
                setTimeout(function() {
                    location.reload();
                }, 1000);
            }
        },
        error: function(xhr, ajaxOptions, thrownError) {
            alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
    });
}

$('input[name=\'filter_po_number\'], input[name=\'filter_date_start\'], input[name=\'filter_date_end\']').on('keydown', function(e) {
    if (e.keyCode == 13) {
        $('#button-filter').trigger('click');
    }
});
//--></script>
{{ footer }}
