<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/cloud/translate/v3/translation_service.proto

namespace Google\Cloud\Translate\V3;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * State metadata for the batch translation operation.
 *
 * Generated from protobuf message <code>google.cloud.translation.v3.BatchTranslateMetadata</code>
 */
class BatchTranslateMetadata extends \Google\Protobuf\Internal\Message
{
    /**
     * The state of the operation.
     *
     * Generated from protobuf field <code>.google.cloud.translation.v3.BatchTranslateMetadata.State state = 1;</code>
     */
    private $state = 0;
    /**
     * Number of successfully translated characters so far (Unicode codepoints).
     *
     * Generated from protobuf field <code>int64 translated_characters = 2;</code>
     */
    private $translated_characters = 0;
    /**
     * Number of characters that have failed to process so far (Unicode
     * codepoints).
     *
     * Generated from protobuf field <code>int64 failed_characters = 3;</code>
     */
    private $failed_characters = 0;
    /**
     * Total number of characters (Unicode codepoints).
     * This is the total number of codepoints from input files times the number of
     * target languages and appears here shortly after the call is submitted.
     *
     * Generated from protobuf field <code>int64 total_characters = 4;</code>
     */
    private $total_characters = 0;
    /**
     * Time when the operation was submitted.
     *
     * Generated from protobuf field <code>.google.protobuf.Timestamp submit_time = 5;</code>
     */
    private $submit_time = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $state
     *           The state of the operation.
     *     @type int|string $translated_characters
     *           Number of successfully translated characters so far (Unicode codepoints).
     *     @type int|string $failed_characters
     *           Number of characters that have failed to process so far (Unicode
     *           codepoints).
     *     @type int|string $total_characters
     *           Total number of characters (Unicode codepoints).
     *           This is the total number of codepoints from input files times the number of
     *           target languages and appears here shortly after the call is submitted.
     *     @type \Google\Protobuf\Timestamp $submit_time
     *           Time when the operation was submitted.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Cloud\Translate\V3\TranslationService::initOnce();
        parent::__construct($data);
    }

    /**
     * The state of the operation.
     *
     * Generated from protobuf field <code>.google.cloud.translation.v3.BatchTranslateMetadata.State state = 1;</code>
     * @return int
     */
    public function getState()
    {
        return $this->state;
    }

    /**
     * The state of the operation.
     *
     * Generated from protobuf field <code>.google.cloud.translation.v3.BatchTranslateMetadata.State state = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setState($var)
    {
        GPBUtil::checkEnum($var, \Google\Cloud\Translate\V3\BatchTranslateMetadata\State::class);
        $this->state = $var;

        return $this;
    }

    /**
     * Number of successfully translated characters so far (Unicode codepoints).
     *
     * Generated from protobuf field <code>int64 translated_characters = 2;</code>
     * @return int|string
     */
    public function getTranslatedCharacters()
    {
        return $this->translated_characters;
    }

    /**
     * Number of successfully translated characters so far (Unicode codepoints).
     *
     * Generated from protobuf field <code>int64 translated_characters = 2;</code>
     * @param int|string $var
     * @return $this
     */
    public function setTranslatedCharacters($var)
    {
        GPBUtil::checkInt64($var);
        $this->translated_characters = $var;

        return $this;
    }

    /**
     * Number of characters that have failed to process so far (Unicode
     * codepoints).
     *
     * Generated from protobuf field <code>int64 failed_characters = 3;</code>
     * @return int|string
     */
    public function getFailedCharacters()
    {
        return $this->failed_characters;
    }

    /**
     * Number of characters that have failed to process so far (Unicode
     * codepoints).
     *
     * Generated from protobuf field <code>int64 failed_characters = 3;</code>
     * @param int|string $var
     * @return $this
     */
    public function setFailedCharacters($var)
    {
        GPBUtil::checkInt64($var);
        $this->failed_characters = $var;

        return $this;
    }

    /**
     * Total number of characters (Unicode codepoints).
     * This is the total number of codepoints from input files times the number of
     * target languages and appears here shortly after the call is submitted.
     *
     * Generated from protobuf field <code>int64 total_characters = 4;</code>
     * @return int|string
     */
    public function getTotalCharacters()
    {
        return $this->total_characters;
    }

    /**
     * Total number of characters (Unicode codepoints).
     * This is the total number of codepoints from input files times the number of
     * target languages and appears here shortly after the call is submitted.
     *
     * Generated from protobuf field <code>int64 total_characters = 4;</code>
     * @param int|string $var
     * @return $this
     */
    public function setTotalCharacters($var)
    {
        GPBUtil::checkInt64($var);
        $this->total_characters = $var;

        return $this;
    }

    /**
     * Time when the operation was submitted.
     *
     * Generated from protobuf field <code>.google.protobuf.Timestamp submit_time = 5;</code>
     * @return \Google\Protobuf\Timestamp
     */
    public function getSubmitTime()
    {
        return isset($this->submit_time) ? $this->submit_time : null;
    }

    public function hasSubmitTime()
    {
        return isset($this->submit_time);
    }

    public function clearSubmitTime()
    {
        unset($this->submit_time);
    }

    /**
     * Time when the operation was submitted.
     *
     * Generated from protobuf field <code>.google.protobuf.Timestamp submit_time = 5;</code>
     * @param \Google\Protobuf\Timestamp $var
     * @return $this
     */
    public function setSubmitTime($var)
    {
        GPBUtil::checkMessage($var, \Google\Protobuf\Timestamp::class);
        $this->submit_time = $var;

        return $this;
    }

}

