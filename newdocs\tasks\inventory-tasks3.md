# المهام المتخصصة - الباقات والوحدات المتعددة
## Inventory Tasks 3 - Bundles & Multi-Units

### 📋 **معلومات المهام:**
- **الملف:** tasks3.md
- **المدة:** 5 أيام
- **الأولوية:** عالية
- **الاعتمادية:** يتطلب إكمال tasks1.md و tasks2.md

---

## 🎯 **الهدف الأساسي**
تطوير نظام الباقات المعقد والوحدات المتعددة مع التحويل التلقائي والتسعير الديناميكي.

---

## 📋 **المهمة الأولى: إدارة الباقات (Product Bundles)**
### **الملف:** `dashboard/controller/inventory/product_bundle.php`

#### **اليوم الأول: التحليل والتخطيط**
- [ ] **1.1** قراءة وتحليل جدول `cod_product_bundle` في minidb.txt
- [ ] **1.2** مراجعة العلاقات مع `cod_product` و `cod_product_inventory`
- [ ] **1.3** دراسة منطق الباقات في التجارة الإلكترونية
- [ ] **1.4** تحديد أنواع الباقات:
  - باقات ثابتة (Fixed Bundles)
  - باقات ديناميكية (Dynamic Bundles)
  - باقات اختيارية (Optional Bundles)
- [ ] **1.5** تحديد منطق التسعير والخصومات

#### **اليوم الثاني: تطوير الكونترولر**
- [ ] **2.1** إنشاء `controller/inventory/product_bundle.php`
- [ ] **2.2** تطبيق الدستور الشامل (20 قاعدة)
- [ ] **2.3** ربط الخدمات المركزية:
  - `model/activity_log.php` للتدقيق
  - `model/unified_document.php` للمستندات
  - `model/communication/unified_notification.php` للإشعارات
- [ ] **2.4** تطوير دوال إدارة الباقات:
  - عرض قائمة الباقات
  - إضافة باقة جديدة
  - تعديل باقة موجودة
  - حذف باقة
- [ ] **2.5** تطوير منطق التحقق من توفر المكونات

#### **اليوم الثالث: تطوير الموديل**
- [ ] **3.1** إنشاء `model/inventory/product_bundle.php`
- [ ] **3.2** تطوير دوال قاعدة البيانات:
  - `getBundles($filters)` - قائمة الباقات
  - `getBundle($bundle_id)` - تفاصيل باقة
  - `addBundle($data)` - إضافة باقة
  - `editBundle($bundle_id, $data)` - تعديل باقة
  - `deleteBundle($bundle_id)` - حذف باقة
  - `getBundleComponents($bundle_id)` - مكونات الباقة
  - `checkBundleAvailability($bundle_id)` - فحص التوفر
- [ ] **3.3** تطوير منطق حساب سعر الباقة
- [ ] **3.4** تطوير منطق تحديث المخزون عند البيع

#### **اليوم الرابع: تطوير التيمبليت**
- [ ] **4.1** إنشاء `view/template/inventory/bundle_list.twig`
- [ ] **4.2** إنشاء `view/template/inventory/bundle_form.twig`
- [ ] **4.3** إضافة واجهة إدارة مكونات الباقة
- [ ] **4.4** إضافة معاينة الباقة للعملاء
- [ ] **4.5** ربط مع نظام التسعير الديناميكي

#### **اليوم الخامس: ملفات اللغة والاختبار**
- [ ] **5.1** إنشاء ملفات اللغة (عربي/إنجليزي)
- [ ] **5.2** اختبار إنشاء وتعديل الباقات
- [ ] **5.3** اختبار منطق التسعير
- [ ] **5.4** اختبار تحديث المخزون
- [ ] **5.5** توثيق نظام الباقات

---

## 📋 **المهمة الثانية: الوحدات المتعددة والتحويل**
### **الملف:** `dashboard/controller/inventory/product_unit.php`

#### **اليوم السادس: التحليل والتخطيط**
- [ ] **6.1** قراءة وتحليل جدول `cod_product_unit` في minidb.txt
- [ ] **6.2** مراجعة العلاقات مع `cod_product` و `cod_unit_conversion`
- [ ] **6.3** دراسة منطق التحويل بين الوحدات
- [ ] **6.4** تحديد أنواع الوحدات:
  - وحدة أساسية (Base Unit)
  - وحدات فرعية (Sub Units)
  - وحدات مركبة (Compound Units)
- [ ] **6.5** تحديد قواعد التحويل والحسابات

#### **اليوم السابع: تطوير الكونترولر**
- [ ] **7.1** إنشاء `controller/inventory/product_unit.php`
- [ ] **7.2** تطبيق الدستور الشامل
- [ ] **7.3** ربط الخدمات المركزية
- [ ] **7.4** تطوير دوال إدارة الوحدات:
  - عرض وحدات المنتج
  - إضافة وحدة جديدة
  - تعديل معاملات التحويل
  - حذف وحدة
- [ ] **7.5** تطوير منطق التحويل التلقائي

#### **اليوم الثامن: تطوير الموديل**
- [ ] **8.1** إنشاء `model/inventory/product_unit.php`
- [ ] **8.2** تطوير دوال قاعدة البيانات:
  - `getProductUnits($product_id)` - وحدات المنتج
  - `getUnit($unit_id)` - تفاصيل وحدة
  - `addUnit($data)` - إضافة وحدة
  - `editUnit($unit_id, $data)` - تعديل وحدة
  - `deleteUnit($unit_id)` - حذف وحدة
  - `convertQuantity($from_unit, $to_unit, $quantity)` - تحويل كمية
  - `getBaseQuantity($product_id, $unit_id, $quantity)` - الكمية الأساسية
- [ ] **8.3** تطوير منطق حساب التكلفة حسب الوحدة
- [ ] **8.4** تطوير منطق التحديث التلقائي للمخزون

#### **اليوم التاسع: تطوير التيمبليت**
- [ ] **9.1** إنشاء `view/template/inventory/unit_list.twig`
- [ ] **9.2** إنشاء `view/template/inventory/unit_form.twig`
- [ ] **9.3** إضافة حاسبة التحويل التفاعلية
- [ ] **9.4** إضافة واجهة إدارة معاملات التحويل
- [ ] **9.5** ربط مع نظام التسعير حسب الوحدة

#### **اليوم العاشر: ملفات اللغة والاختبار**
- [ ] **10.1** إنشاء ملفات اللغة
- [ ] **10.2** اختبار التحويل بين الوحدات
- [ ] **10.3** اختبار دقة الحسابات
- [ ] **10.4** اختبار التكامل مع المخزون
- [ ] **10.5** توثيق نظام الوحدات

---

## 🔗 **الاعتمادية والترابط**

### **يعتمد على:**
- إكمال tasks1.md (المستودعات وحركات المخزون)
- إكمال tasks2.md (التسوية والنقل)
- نظام التسعير الأساسي

### **يؤثر على:**
- tasks4.md (المخزون الوهمي)
- tasks5.md (التسعير المتقدم)
- tasks6.md (التجارة الإلكترونية)

---

## 📊 **مؤشرات النجاح**

### **المؤشرات التقنية:**
- [ ] **100% دقة** في حسابات الباقات
- [ ] **تحويل صحيح** بين جميع الوحدات
- [ ] **تحديث تلقائي** للمخزون والأسعار
- [ ] **أداء محسن** للاستعلامات المعقدة

### **المؤشرات الوظيفية:**
- [ ] **إدارة سهلة** للباقات المعقدة
- [ ] **تحويل مرن** بين الوحدات
- [ ] **تسعير دقيق** للباقات والوحدات
- [ ] **تقارير شاملة** للمبيعات والمخزون

---

## 🚨 **تحذيرات مهمة**

### **نقاط حرجة:**
1. **اختبر حسابات الباقات** بعناية فائقة
2. **تأكد من دقة التحويل** بين الوحدات
3. **راجع منطق التسعير** للباقات المعقدة
4. **اختبر تحديث المخزون** عند البيع

### **متطلبات إلزامية:**
- دقة 100% في جميع الحسابات
- تحديث تلقائي للمخزون
- تسعير ديناميكي للباقات
- تقارير مفصلة للتحليل

---

## 📈 **التحسينات المتقدمة**

### **ميزات الباقات:**
- [ ] **باقات موسمية** بتواريخ محددة
- [ ] **خصومات متدرجة** حسب الكمية
- [ ] **باقات شخصية** للعملاء المميزين
- [ ] **توصيات ذكية** للباقات

### **ميزات الوحدات:**
- [ ] **تحويل تلقائي** عند النقص
- [ ] **وحدات مركبة** معقدة
- [ ] **حسابات دقيقة** للكسور
- [ ] **تقارير تحليلية** للوحدات

---

## 🔧 **التكامل مع الأنظمة الأخرى**

### **التكامل المحاسبي:**
- قيود تلقائية لمبيعات الباقات
- حسابات دقيقة للتكلفة
- تقارير مالية مفصلة

### **التكامل مع المتجر:**
- عرض الباقات بشكل جذاب
- حاسبة الوحدات للعملاء
- خصومات تلقائية للباقات

---

**🎯 الهدف:** إنشاء نظام متقدم ومرن للباقات والوحدات المتعددة يدعم جميع احتياجات التجارة الحديثة.
