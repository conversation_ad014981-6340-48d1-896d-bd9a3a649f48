{{ header }}{{ column_left }}
<div id="content">
<style>
/* تحسينات متقدمة للأصول الثابتة - Enterprise Grade */
.panel-default {
    border: none;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.panel-default:hover {
    box-shadow: 0 0.25rem 2rem 0 rgba(58, 59, 69, 0.2);
}

.panel-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    border-radius: 8px 8px 0 0;
    padding: 15px;
}

/* تحسين الجدول */
.table {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 0.125rem 0.25rem rgba(0,0,0,0.075);
}

.table thead th {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    border: none;
    font-weight: 600;
    text-align: center;
}

.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
    transform: translateX(2px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* تحسين القيم */
.asset-cost {
    color: #28a745;
    font-weight: 600;
}

.depreciation-amount {
    color: #dc3545;
    font-weight: 600;
}

.net-value {
    color: #007bff;
    font-weight: 700;
    font-size: 1.1em;
}

/* تحسين الفلاتر */
.form-inline {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.form-group {
    flex-grow: 1;
    margin-bottom: 10px;
    margin-right: 10px;
}

.form-control {
    border-radius: 6px;
    border: 1px solid #ced4da;
    transition: all 0.2s ease;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
    transform: scale(1.02);
}

/* تحسين الأزرار */
.btn {
    transition: all 0.2s ease;
    border-radius: 6px;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8 0%, #117a8b 100%);
    border: none;
}

/* تحسين ملخص الأصول */
.assets-summary {
    background: linear-gradient(135deg, #f1f8ff 0%, #e6f3ff 100%);
    border-left: 4px solid #007bff;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.assets-summary h4 {
    color: #0056b3;
    margin-top: 0;
    font-weight: 600;
}

/* تحسين بطاقات الأصول */
.asset-card {
    background: white;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.asset-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.asset-name {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 10px;
}

.asset-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.depreciation-progress {
    width: 100%;
    height: 8px;
    background-color: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin: 10px 0;
}

.depreciation-bar {
    height: 100%;
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    transition: width 0.3s ease;
}
</style>

<div class="page-header">
    <div class="container-fluid">
        <div class="pull-right">
            <button type="button" data-toggle="tooltip" title="{{ button_filter }}" onclick="$('#filter-fixed-assets').toggle();" class="btn btn-default"><i class="fa fa-filter"></i></button>
            <a href="{{ add }}" data-toggle="tooltip" title="{{ button_add }}" class="btn btn-primary"><i class="fa fa-plus"></i></a>
            <button type="button" data-toggle="tooltip" title="{{ button_delete }}" onclick="confirm('{{ text_confirm }}') ? $('#form-fixed-assets').submit() : false;" class="btn btn-danger"><i class="fa fa-trash-o"></i></button>
        </div>
        <h1>{{ heading_title }}</h1>
        <ul class="breadcrumb">
            {% for breadcrumb in breadcrumbs %}
            <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
            {% endfor %}
        </ul>
    </div>
</div>

<div class="container-fluid">
    <div class="panel panel-default">
        <div class="panel-header">
            <h3 class="panel-title"><i class="fa fa-building"></i> {{ text_list }}</h3>
        </div>
        
        <!-- ملخص الأصول الثابتة -->
        <div class="assets-summary">
            <h4><i class="fa fa-chart-bar"></i> ملخص الأصول الثابتة</h4>
            <div class="row">
                <div class="col-md-3">
                    <div class="text-center">
                        <h5>إجمالي التكلفة</h5>
                        <span class="asset-cost">{{ total_cost }}</span>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h5>مجمع الإهلاك</h5>
                        <span class="depreciation-amount">{{ total_depreciation }}</span>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h5>صافي القيمة</h5>
                        <span class="net-value">{{ net_book_value }}</span>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h5>عدد الأصول</h5>
                        <span class="badge badge-info">{{ total_assets }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- فلاتر البحث -->
        <div id="filter-fixed-assets" class="well" style="display: none;">
            <div class="row">
                <div class="col-sm-6">
                    <div class="form-group">
                        <label class="control-label" for="input-name">{{ entry_name }}</label>
                        <input type="text" name="filter_name" value="{{ filter_name }}" placeholder="{{ entry_name }}" id="input-name" class="form-control" />
                    </div>
                    <div class="form-group">
                        <label class="control-label" for="input-category">{{ entry_category }}</label>
                        <select name="filter_category" id="input-category" class="form-control">
                            <option value="">{{ text_select }}</option>
                            {% for category in categories %}
                            {% if category.category_id == filter_category %}
                            <option value="{{ category.category_id }}" selected="selected">{{ category.name }}</option>
                            {% else %}
                            <option value="{{ category.category_id }}">{{ category.name }}</option>
                            {% endif %}
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="form-group">
                        <label class="control-label" for="input-status">{{ entry_status }}</label>
                        <select name="filter_status" id="input-status" class="form-control">
                            <option value="">{{ text_select }}</option>
                            {% if filter_status == '1' %}
                            <option value="1" selected="selected">{{ text_enabled }}</option>
                            {% else %}
                            <option value="1">{{ text_enabled }}</option>
                            {% endif %}
                            {% if filter_status == '0' %}
                            <option value="0" selected="selected">{{ text_disabled }}</option>
                            {% else %}
                            <option value="0">{{ text_disabled }}</option>
                            {% endif %}
                        </select>
                    </div>
                    <button type="button" id="button-filter" class="btn btn-primary pull-right"><i class="fa fa-search"></i> {{ button_filter }}</button>
                </div>
            </div>
        </div>

        <form action="{{ delete }}" method="post" enctype="multipart/form-data" id="form-fixed-assets">
            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead>
                        <tr>
                            <td style="width: 1px;" class="text-center"><input type="checkbox" onclick="$('input[name*=\'selected\']').prop('checked', this.checked);" /></td>
                            <td class="text-left">{% if sort == 'name' %}<a href="{{ sort_name }}" class="{{ order|lower }}">{{ column_name }}</a>{% else %}<a href="{{ sort_name }}">{{ column_name }}</a>{% endif %}</td>
                            <td class="text-left">{{ column_category }}</td>
                            <td class="text-right">{{ column_cost }}</td>
                            <td class="text-right">{{ column_depreciation }}</td>
                            <td class="text-right">{{ column_net_value }}</td>
                            <td class="text-center">{{ column_status }}</td>
                            <td class="text-right">{{ column_action }}</td>
                        </tr>
                    </thead>
                    <tbody>
                        {% if assets %}
                        {% for asset in assets %}
                        <tr>
                            <td class="text-center">{% if asset.selected %}<input type="checkbox" name="selected[]" value="{{ asset.asset_id }}" checked="checked" />{% else %}<input type="checkbox" name="selected[]" value="{{ asset.asset_id }}" />{% endif %}</td>
                            <td class="text-left">{{ asset.name }}</td>
                            <td class="text-left">{{ asset.category }}</td>
                            <td class="text-right asset-cost">{{ asset.cost }}</td>
                            <td class="text-right depreciation-amount">{{ asset.accumulated_depreciation }}</td>
                            <td class="text-right net-value">{{ asset.net_book_value }}</td>
                            <td class="text-center">{% if asset.status %}<span class="label label-success">{{ text_enabled }}</span>{% else %}<span class="label label-danger">{{ text_disabled }}</span>{% endif %}</td>
                            <td class="text-right">
                                <a href="{{ asset.edit }}" data-toggle="tooltip" title="{{ button_edit }}" class="btn btn-primary"><i class="fa fa-pencil"></i></a>
                            </td>
                        </tr>
                        {% endfor %}
                        {% else %}
                        <tr>
                            <td class="text-center" colspan="8">{{ text_no_results }}</td>
                        </tr>
                        {% endif %}
                    </tbody>
                </table>
            </div>
        </form>
        
        <div class="row">
            <div class="col-sm-6 text-left">{{ pagination }}</div>
            <div class="col-sm-6 text-right">{{ results }}</div>
        </div>
    </div>
</div>
<script type="text/javascript"><!--
// تحسينات متقدمة للأصول الثابتة
$(document).ready(function() {
    // إضافة تأثيرات متقدمة للجدول
    $('.table tbody tr').hover(function() {
        $(this).find('td').css('background-color', '#f8f9fa');
    }, function() {
        $(this).find('td').css('background-color', '');
    });
    
    // تحسين عرض القيم
    $('.asset-cost').each(function() {
        const value = parseFloat($(this).text().replace(/[^0-9.-]+/g, ''));
        if (value > 0) {
            $(this).addClass('font-weight-bold');
        }
    });
    
    $('.depreciation-amount').each(function() {
        const value = parseFloat($(this).text().replace(/[^0-9.-]+/g, ''));
        if (value > 0) {
            $(this).addClass('font-weight-bold');
        }
    });
    
    $('.net-value').each(function() {
        const value = parseFloat($(this).text().replace(/[^0-9.-]+/g, ''));
        if (value > 0) {
            $(this).addClass('font-weight-bold');
        }
    });
    
    // إضافة مؤشر تحميل للعمليات
    $(document).ajaxStart(function() {
        $('body').addClass('loading');
    }).ajaxStop(function() {
        $('body').removeClass('loading');
    });
    
    // تحسين البحث مع debounce
    let searchTimeout;
    $('#input-name').off('keyup').on('keyup', function() {
        clearTimeout(searchTimeout);
        const value = $(this).val().toLowerCase();
        
        searchTimeout = setTimeout(function() {
            $('.table tbody tr').each(function() {
                const text = $(this).text().toLowerCase();
                const shouldShow = text.indexOf(value) > -1;
                $(this).toggle(shouldShow);
                
                // إضافة تأثير highlight للنتائج
                if (shouldShow && value.length > 0) {
                    $(this).addClass('search-highlight');
                } else {
                    $(this).removeClass('search-highlight');
                }
            });
        }, 300);
    });
    
    // تحسين فلتر الفئات
    $('#input-category').on('change', function() {
        const selectedCategory = $(this).val();
        $('.table tbody tr').each(function() {
            if (selectedCategory === '') {
                $(this).show();
            } else {
                const categoryText = $(this).find('td:nth-child(3)').text();
                $(this).toggle(categoryText.includes(selectedCategory));
            }
        });
    });
    
    // إضافة تأثيرات للبطاقات
    $('.asset-card').each(function() {
        $(this).on('click', function() {
            $(this).toggleClass('selected');
        });
    });
});

// دالة تصدير البيانات
function exportAssets(format) {
    const url = 'index.php?route=accounts/fixed_assets/export&format=' + format;
    window.open(url, '_blank');
}

// دالة طباعة التقرير
function printReport() {
    window.print();
}

// دالة تحديث البيانات
function refreshData() {
    location.reload();
}
//--></script>
{{ footer }}
