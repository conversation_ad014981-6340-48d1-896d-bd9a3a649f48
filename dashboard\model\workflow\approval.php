<?php
/**
 * نموذج الموافقات - AYM ERP
 * Approval Model
 * 
 * @package    AYM ERP
 * <AUTHOR> Team
 * @copyright  2025 AYM ERP
 * @license    Commercial
 * @version    2.0.0
 */

class ModelWorkflowApproval extends Model {
    
    /**
     * إنشاء طلب موافقة جديد
     */
    public function createApprovalRequest($data) {
        $sql = "INSERT INTO " . DB_PREFIX . "approval_request SET
                title = '" . $this->db->escape($data['title']) . "',
                description = '" . $this->db->escape($data['description']) . "',
                request_type = '" . $this->db->escape($data['request_type'] ?? 'general') . "',
                requester_id = '" . (int)($data['requester_id'] ?? $this->user->getId()) . "',
                department_id = '" . (int)($data['department_id'] ?? 0) . "',
                priority = '" . $this->db->escape($data['priority'] ?? 'normal') . "',
                amount = '" . (float)($data['amount'] ?? 0) . "',
                currency = '" . $this->db->escape($data['currency'] ?? 'EGP') . "',
                request_data = '" . $this->db->escape(json_encode($data['request_data'] ?? array())) . "',
                workflow_step_id = '" . (int)($data['workflow_step_id'] ?? 0) . "',
                status = 'pending',
                created_at = NOW()";
        
        $this->db->query($sql);
        $request_id = $this->db->getLastId();
        
        // إضافة المعتمدين
        if (!empty($data['approvers'])) {
            $this->addApprovers($request_id, $data['approvers']);
        }
        
        // إرسال إشعارات للمعتمدين
        $this->notifyApprovers($request_id);
        
        return $request_id;
    }
    
    /**
     * إضافة المعتمدين لطلب الموافقة
     */
    private function addApprovers($request_id, $approvers) {
        foreach ($approvers as $index => $approver) {
            $sql = "INSERT INTO " . DB_PREFIX . "approval_approver SET
                    request_id = '" . (int)$request_id . "',
                    approver_id = '" . (int)$approver['user_id'] . "',
                    approver_type = '" . $this->db->escape($approver['type'] ?? 'user') . "',
                    approval_order = '" . (int)($index + 1) . "',
                    is_required = '" . (int)($approver['required'] ?? 1) . "',
                    status = 'pending',
                    created_at = NOW()";
            
            $this->db->query($sql);
        }
    }
    
    /**
     * إرسال إشعارات للمعتمدين (محدث للخدمات المركزية)
     */
    private function notifyApprovers($request_id) {
        try {
            $this->load->model('core/central_service_manager');

            $request = $this->getApprovalRequest($request_id);
            $approvers = $this->getRequestApprovers($request_id);

            if (!empty($approvers)) {
                $approver_ids = array_column($approvers, 'approver_id');

                // استخدام الخدمة المركزية لإرسال إشعار جماعي
                $this->model_core_central_service_manager->sendBulkNotification(
                    $approver_ids,
                    'طلب موافقة جديد',
                    'لديك طلب موافقة جديد: ' . $request['title'],
                    $request['priority'] ?? 'medium',
                    [
                        'request_id' => $request_id,
                        'request_title' => $request['title'],
                        'action_url' => 'index.php?route=workflow/approval/view&request_id=' . $request_id
                    ]
                );

                // تسجيل النشاط
                $this->model_core_central_service_manager->logActivity(
                    'approval_request_sent',
                    'workflow',
                    "تم إرسال طلب موافقة للمعتمدين: {$request['title']}",
                    [
                        'request_id' => $request_id,
                        'approvers_count' => count($approver_ids)
                    ]
                );
            }

        } catch (Exception $e) {
            // في حالة فشل الخدمة المركزية، استخدم النظام القديم
            error_log("Failed to notify approvers via central service: " . $e->getMessage());

            try {
                $this->load->model('communication/unified_notification');

                $request = $this->getApprovalRequest($request_id);
                $approvers = $this->getRequestApprovers($request_id);

                foreach ($approvers as $approver) {
                    $notification_data = array(
                        'type' => 'approval_request',
                        'title' => 'طلب موافقة جديد',
                        'message' => 'لديك طلب موافقة جديد: ' . $request['title'],
                        'recipient_id' => $approver['approver_id'],
                        'related_id' => $request_id,
                        'related_type' => 'approval_request',
                        'priority' => $request['priority'],
                        'action_url' => 'index.php?route=workflow/approval/view&request_id=' . $request_id
                    );

                    $this->model_communication_unified_notification->createNotification($notification_data);
                }
            } catch (Exception $e2) {
                error_log("CRITICAL: Both notification systems failed: " . $e2->getMessage());
            }
        }
    }
    
    /**
     * الموافقة على طلب
     */
    public function approveRequest($request_id, $approver_id, $comments = '') {
        // تحديث حالة المعتمد
        $sql = "UPDATE " . DB_PREFIX . "approval_approver SET
                status = 'approved',
                comments = '" . $this->db->escape($comments) . "',
                approved_at = NOW()
                WHERE request_id = '" . (int)$request_id . "'
                AND approver_id = '" . (int)$approver_id . "'";
        
        $this->db->query($sql);
        
        // تسجيل النشاط
        $this->logApprovalActivity($request_id, $approver_id, 'approved', $comments);
        
        // فحص إذا كانت جميع الموافقات مكتملة
        if ($this->areAllApprovalsComplete($request_id)) {
            $this->completeApprovalRequest($request_id);
        }
        
        return true;
    }
    
    /**
     * رفض طلب
     */
    public function rejectRequest($request_id, $approver_id, $comments = '') {
        // تحديث حالة المعتمد
        $sql = "UPDATE " . DB_PREFIX . "approval_approver SET
                status = 'rejected',
                comments = '" . $this->db->escape($comments) . "',
                approved_at = NOW()
                WHERE request_id = '" . (int)$request_id . "'
                AND approver_id = '" . (int)$approver_id . "'";
        
        $this->db->query($sql);
        
        // تحديث حالة الطلب
        $sql = "UPDATE " . DB_PREFIX . "approval_request SET
                status = 'rejected',
                completed_at = NOW()
                WHERE request_id = '" . (int)$request_id . "'";
        
        $this->db->query($sql);
        
        // تسجيل النشاط
        $this->logApprovalActivity($request_id, $approver_id, 'rejected', $comments);
        
        // إشعار مقدم الطلب
        $this->notifyRequester($request_id, 'rejected');
        
        return true;
    }
    
    /**
     * فحص إذا كانت جميع الموافقات مكتملة
     */
    private function areAllApprovalsComplete($request_id) {
        $sql = "SELECT COUNT(*) as pending_count
                FROM " . DB_PREFIX . "approval_approver
                WHERE request_id = '" . (int)$request_id . "'
                AND is_required = 1
                AND status = 'pending'";
        
        $query = $this->db->query($sql);
        
        return $query->row['pending_count'] == 0;
    }
    
    /**
     * إكمال طلب الموافقة
     */
    private function completeApprovalRequest($request_id) {
        $sql = "UPDATE " . DB_PREFIX . "approval_request SET
                status = 'approved',
                completed_at = NOW()
                WHERE request_id = '" . (int)$request_id . "'";
        
        $this->db->query($sql);
        
        // إشعار مقدم الطلب
        $this->notifyRequester($request_id, 'approved');
        
        // تنفيذ الإجراءات التالية في سير العمل
        $this->executePostApprovalActions($request_id);
    }
    
    /**
     * تنفيذ الإجراءات التالية بعد الموافقة
     */
    private function executePostApprovalActions($request_id) {
        $request = $this->getApprovalRequest($request_id);
        
        if ($request['workflow_step_id']) {
            $this->load->model('workflow/visual_workflow_engine');
            $this->model_workflow_visual_workflow_engine->continueWorkflow($request['workflow_step_id']);
        }
    }
    
    /**
     * إشعار مقدم الطلب
     */
    private function notifyRequester($request_id, $status) {
        $this->load->model('communication/unified_notification');
        
        $request = $this->getApprovalRequest($request_id);
        
        $status_text = $status == 'approved' ? 'تمت الموافقة على' : 'تم رفض';
        
        $notification_data = array(
            'type' => 'approval_result',
            'title' => $status_text . ' طلبك',
            'message' => $status_text . ' طلب الموافقة: ' . $request['title'],
            'recipient_id' => $request['requester_id'],
            'related_id' => $request_id,
            'related_type' => 'approval_request',
            'priority' => 'high'
        );
        
        $this->model_communication_unified_notification->createNotification($notification_data);
    }
    
    /**
     * تسجيل نشاط الموافقة (محدث للخدمات المركزية)
     */
    private function logApprovalActivity($request_id, $approver_id, $action, $comments) {
        try {
            $this->load->model('core/central_service_manager');

            $description = 'تم ' . ($action == 'approved' ? 'الموافقة على' : 'رفض') . ' طلب الموافقة #' . $request_id;

            // استخدام الخدمة المركزية لتسجيل النشاط
            $this->model_core_central_service_manager->logApproval(
                'workflow',
                'approval_request',
                $request_id,
                ($action == 'approved'),
                $description
            );

            // تسجيل تفاصيل إضافية
            $this->model_core_central_service_manager->logActivity(
                'approval_' . $action,
                'workflow',
                $description,
                [
                    'request_id' => $request_id,
                    'approver_id' => $approver_id,
                    'action' => $action,
                    'comments' => $comments
                ]
            );

        } catch (Exception $e) {
            // في حالة فشل الخدمة المركزية، استخدم النظام القديم
            error_log("Failed to log approval activity via central service: " . $e->getMessage());

            try {
                $this->load->model('activity_log');

                $activity_data = array(
                    'action_type' => 'approval_' . $action,
                    'module' => 'workflow',
                    'description' => 'تم ' . ($action == 'approved' ? 'الموافقة على' : 'رفض') . ' طلب الموافقة #' . $request_id,
                    'user_id' => $approver_id,
                    'related_id' => $request_id,
                    'related_type' => 'approval_request',
                    'additional_data' => json_encode(array('comments' => $comments))
                );

                $this->model_activity_log->addActivity($activity_data);
            } catch (Exception $e2) {
                error_log("CRITICAL: Both logging systems failed: " . $e2->getMessage());
            }
        }
    }
    
    /**
     * الحصول على طلب موافقة
     */
    public function getApprovalRequest($request_id) {
        $sql = "SELECT ar.*, u.firstname, u.lastname
                FROM " . DB_PREFIX . "approval_request ar
                LEFT JOIN " . DB_PREFIX . "user u ON (ar.requester_id = u.user_id)
                WHERE ar.request_id = '" . (int)$request_id . "'";
        
        $query = $this->db->query($sql);
        
        return $query->row;
    }
    
    /**
     * الحصول على معتمدي الطلب
     */
    public function getRequestApprovers($request_id) {
        $sql = "SELECT aa.*, u.firstname, u.lastname
                FROM " . DB_PREFIX . "approval_approver aa
                LEFT JOIN " . DB_PREFIX . "user u ON (aa.approver_id = u.user_id)
                WHERE aa.request_id = '" . (int)$request_id . "'
                ORDER BY aa.approval_order";
        
        $query = $this->db->query($sql);
        
        return $query->rows;
    }
    
    /**
     * الحصول على طلبات الموافقة المعلقة للمستخدم
     */
    public function getPendingApprovals($user_id) {
        $sql = "SELECT ar.*, aa.approval_order, aa.is_required,
                       u.firstname as requester_firstname, u.lastname as requester_lastname
                FROM " . DB_PREFIX . "approval_request ar
                INNER JOIN " . DB_PREFIX . "approval_approver aa ON (ar.request_id = aa.request_id)
                LEFT JOIN " . DB_PREFIX . "user u ON (ar.requester_id = u.user_id)
                WHERE aa.approver_id = '" . (int)$user_id . "'
                AND aa.status = 'pending'
                AND ar.status = 'pending'
                ORDER BY ar.priority DESC, ar.created_at ASC
                LIMIT 20";
        
        $query = $this->db->query($sql);
        
        return $query->rows;
    }
    
    /**
     * الحصول على إحصائيات الموافقات
     */
    public function getStatistics() {
        $stats = array();
        
        // إجمالي الطلبات
        $sql = "SELECT COUNT(*) as total FROM " . DB_PREFIX . "approval_request";
        $query = $this->db->query($sql);
        $stats['total_requests'] = $query->row['total'];
        
        // الطلبات المعلقة
        $sql = "SELECT COUNT(*) as pending FROM " . DB_PREFIX . "approval_request 
                WHERE status = 'pending'";
        $query = $this->db->query($sql);
        $stats['pending_requests'] = $query->row['pending'];
        
        // الطلبات المعتمدة اليوم
        $sql = "SELECT COUNT(*) as approved FROM " . DB_PREFIX . "approval_request 
                WHERE status = 'approved' AND DATE(completed_at) = CURDATE()";
        $query = $this->db->query($sql);
        $stats['approved_today'] = $query->row['approved'];
        
        return $stats;
    }
    
    /**
     * تنظيف الطلبات القديمة
     */
    public function cleanupOldRequests($days = 90) {
        $sql = "DELETE FROM " . DB_PREFIX . "approval_request 
                WHERE status IN ('approved', 'rejected') 
                AND completed_at < DATE_SUB(NOW(), INTERVAL " . (int)$days . " DAY)";
        
        $this->db->query($sql);
        
        return $this->db->countAffected();
    }
}
?>
