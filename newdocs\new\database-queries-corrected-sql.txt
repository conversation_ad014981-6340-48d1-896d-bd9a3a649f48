-- Database Queries Corrected - AYM ERP
-- الاستعلامات المُصححة لقاعدة البيانات - نظام AYM ERP
-- Date: 20/7/2025 - Based on Real Understanding

-- =====================================================
-- CORRECTED DATABASE UPDATES
-- التحديثات المُصححة لقاعدة البيانات
-- =====================================================

-- =====================================================
-- 1. UNAVAILABLE INVENTORY SYSTEM
-- نظام المخزون غير المتاح
-- =====================================================

-- إضافة حقول المخزون غير المتاح لجدول المخزون الموجود
ALTER TABLE `cod_product_inventory` 
ADD COLUMN `quantity_maintenance` DECIMAL(15,4) NOT NULL DEFAULT 0.0000 COMMENT 'مخزون الصيانة',
ADD COLUMN `quantity_quality_check` DECIMAL(15,4) NOT NULL DEFAULT 0.0000 COMMENT 'قيد الفحص',
ADD COLUMN `quantity_damaged` DECIMAL(15,4) NOT NULL DEFAULT 0.0000 COMMENT 'تالف',
ADD COLUMN `quantity_expired` DECIMAL(15,4) NOT NULL DEFAULT 0.0000 COMMENT 'منتهي الصلاحية',
ADD COLUMN `quantity_quarantine` DECIMAL(15,4) NOT NULL DEFAULT 0.0000 COMMENT 'حجر صحي',
ADD COLUMN `last_status_update` DATETIME DEFAULT NULL COMMENT 'آخر تحديث للحالة',
ADD COLUMN `status_updated_by` INT(11) DEFAULT NULL COMMENT 'محدث الحالة';

-- إضافة فهارس للأداء
ALTER TABLE `cod_product_inventory`
ADD INDEX `idx_maintenance_qty` (`quantity_maintenance`),
ADD INDEX `idx_quality_check_qty` (`quantity_quality_check`),
ADD INDEX `idx_damaged_qty` (`quantity_damaged`),
ADD INDEX `idx_expired_qty` (`quantity_expired`),
ADD INDEX `idx_quarantine_qty` (`quantity_quarantine`),
ADD INDEX `idx_status_update` (`last_status_update`);

-- =====================================================
-- 2. INVENTORY STATUS TRACKING
-- تتبع حالات المخزون
-- =====================================================

CREATE TABLE IF NOT EXISTS `cod_inventory_status_log` (
  `log_id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL COMMENT 'معرف المنتج',
  `warehouse_id` int(11) NOT NULL COMMENT 'معرف المستودع',
  `unit_id` int(11) NOT NULL COMMENT 'معرف الوحدة',
  `batch_id` int(11) DEFAULT NULL COMMENT 'معرف الدفعة',
  `status_from` enum('available','reserved','maintenance','quality_check','damaged','expired','quarantine') NOT NULL COMMENT 'الحالة السابقة',
  `status_to` enum('available','reserved','maintenance','quality_check','damaged','expired','quarantine') NOT NULL COMMENT 'الحالة الجديدة',
  `quantity` decimal(15,4) NOT NULL COMMENT 'الكمية المنقولة',
  `reason_id` int(11) DEFAULT NULL COMMENT 'معرف السبب',
  `reason_notes` text DEFAULT NULL COMMENT 'ملاحظات السبب',
  `reference_type` varchar(50) DEFAULT NULL COMMENT 'نوع المرجع',
  `reference_id` int(11) DEFAULT NULL COMMENT 'معرف المرجع',
  `reference_number` varchar(100) DEFAULT NULL COMMENT 'رقم المرجع',
  `created_by` int(11) NOT NULL COMMENT 'المنشئ',
  `created_at` datetime NOT NULL DEFAULT current_timestamp() COMMENT 'تاريخ الإنشاء',
  PRIMARY KEY (`log_id`),
  KEY `idx_product_warehouse` (`product_id`,`warehouse_id`),
  KEY `idx_status_change` (`status_from`,`status_to`),
  KEY `idx_created_date` (`created_at`),
  KEY `idx_batch` (`batch_id`),
  KEY `idx_reason` (`reason_id`),
  KEY `idx_reference` (`reference_type`,`reference_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- =====================================================
-- 3. UNAVAILABILITY REASONS
-- أسباب عدم التوفر
-- =====================================================

CREATE TABLE IF NOT EXISTS `cod_unavailability_reasons` (
  `reason_id` int(11) NOT NULL AUTO_INCREMENT,
  `reason_code` varchar(20) NOT NULL COMMENT 'كود السبب',
  `reason_name_ar` varchar(100) NOT NULL COMMENT 'اسم السبب بالعربية',
  `reason_name_en` varchar(100) NOT NULL COMMENT 'اسم السبب بالإنجليزية',
  `status_type` enum('maintenance','quality_check','damaged','expired','quarantine') NOT NULL COMMENT 'نوع الحالة',
  `description_ar` text DEFAULT NULL COMMENT 'وصف بالعربية',
  `description_en` text DEFAULT NULL COMMENT 'وصف بالإنجليزية',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'نشط',
  `requires_approval` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'يحتاج موافقة',
  `auto_expire_days` int(11) DEFAULT NULL COMMENT 'انتهاء تلقائي بالأيام',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT 'ترتيب العرض',
  `created_by` int(11) NOT NULL COMMENT 'المنشئ',
  `created_at` datetime NOT NULL DEFAULT current_timestamp() COMMENT 'تاريخ الإنشاء',
  `updated_by` int(11) DEFAULT NULL COMMENT 'المحدث',
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp() COMMENT 'تاريخ التحديث',
  PRIMARY KEY (`reason_id`),
  UNIQUE KEY `unique_reason_code` (`reason_code`),
  KEY `idx_status_type` (`status_type`),
  KEY `idx_active` (`is_active`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- =====================================================
-- 4. DEFAULT UNAVAILABILITY REASONS
-- أسباب عدم التوفر الافتراضية
-- =====================================================

INSERT INTO `cod_unavailability_reasons` 
(`reason_code`, `reason_name_ar`, `reason_name_en`, `status_type`, `description_ar`, `description_en`, `requires_approval`, `auto_expire_days`, `sort_order`, `created_by`) VALUES

-- أسباب الصيانة
('MAINT_REPAIR', 'قيد الإصلاح', 'Under Repair', 'maintenance', 'منتج يحتاج إصلاح أو صيانة', 'Product needs repair or maintenance', 1, NULL, 1, 1),
('MAINT_SERVICE', 'صيانة دورية', 'Routine Maintenance', 'maintenance', 'صيانة دورية مجدولة', 'Scheduled routine maintenance', 0, 7, 2, 1),
('MAINT_UPGRADE', 'ترقية/تحديث', 'Upgrade/Update', 'maintenance', 'ترقية أو تحديث المنتج', 'Product upgrade or update', 1, NULL, 3, 1),

-- أسباب فحص الجودة
('QC_PENDING', 'في انتظار الفحص', 'Pending Quality Check', 'quality_check', 'منتج في انتظار فحص الجودة', 'Product awaiting quality inspection', 0, 3, 4, 1),
('QC_TESTING', 'قيد الاختبار', 'Under Testing', 'quality_check', 'منتج قيد الاختبار والفحص', 'Product under testing and inspection', 0, 5, 5, 1),
('QC_RETEST', 'إعادة اختبار', 'Re-testing', 'quality_check', 'منتج يحتاج إعادة اختبار', 'Product needs re-testing', 1, 7, 6, 1),

-- أسباب التلف
('DMG_PHYSICAL', 'تلف مادي', 'Physical Damage', 'damaged', 'تلف مادي في المنتج', 'Physical damage to product', 1, NULL, 7, 1),
('DMG_WATER', 'تلف بالمياه', 'Water Damage', 'damaged', 'تلف بسبب المياه', 'Water damage', 1, NULL, 8, 1),
('DMG_TRANSPORT', 'تلف أثناء النقل', 'Transport Damage', 'damaged', 'تلف أثناء عملية النقل', 'Damage during transportation', 1, NULL, 9, 1),
('DMG_HANDLING', 'تلف أثناء التداول', 'Handling Damage', 'damaged', 'تلف أثناء التداول والتخزين', 'Damage during handling and storage', 0, NULL, 10, 1),

-- أسباب انتهاء الصلاحية
('EXP_DATE', 'منتهي الصلاحية', 'Expired', 'expired', 'منتج منتهي الصلاحية', 'Product has expired', 0, NULL, 11, 1),
('EXP_SOON', 'قارب على الانتهاء', 'Near Expiry', 'expired', 'منتج قارب على انتهاء الصلاحية', 'Product near expiry date', 0, 30, 12, 1),
('EXP_BATCH', 'دفعة منتهية', 'Batch Expired', 'expired', 'دفعة كاملة منتهية الصلاحية', 'Entire batch expired', 1, NULL, 13, 1),

-- أسباب الحجر الصحي
('QUAR_HEALTH', 'حجر صحي', 'Health Quarantine', 'quarantine', 'حجر صحي لأسباب طبية', 'Health quarantine for medical reasons', 1, 14, 14, 1),
('QUAR_SAFETY', 'حجر أمني', 'Safety Quarantine', 'quarantine', 'حجر أمني لأسباب السلامة', 'Safety quarantine for security reasons', 1, 7, 15, 1),
('QUAR_RECALL', 'سحب من السوق', 'Product Recall', 'quarantine', 'سحب المنتج من السوق', 'Product recall from market', 1, NULL, 16, 1),
('QUAR_INVESTIGATION', 'قيد التحقيق', 'Under Investigation', 'quarantine', 'منتج قيد التحقيق', 'Product under investigation', 1, NULL, 17, 1);

-- =====================================================
-- 5. AUTOMATIC AVAILABLE QUANTITY CALCULATION
-- حساب الكمية المتاحة تلقائياً
-- =====================================================

DELIMITER $$

CREATE TRIGGER `update_available_quantity_insert` 
BEFORE INSERT ON `cod_product_inventory`
FOR EACH ROW 
BEGIN
    SET NEW.available_quantity = NEW.quantity - (
        NEW.reserved_quantity + 
        IFNULL(NEW.quantity_maintenance, 0) + 
        IFNULL(NEW.quantity_quality_check, 0) + 
        IFNULL(NEW.quantity_damaged, 0) + 
        IFNULL(NEW.quantity_expired, 0) + 
        IFNULL(NEW.quantity_quarantine, 0)
    );
    
    -- تأكد من عدم كون الكمية المتاحة سالبة
    IF NEW.available_quantity < 0 THEN
        SET NEW.available_quantity = 0;
    END IF;
END$$

CREATE TRIGGER `update_available_quantity_update` 
BEFORE UPDATE ON `cod_product_inventory`
FOR EACH ROW 
BEGIN
    SET NEW.available_quantity = NEW.quantity - (
        NEW.reserved_quantity + 
        IFNULL(NEW.quantity_maintenance, 0) + 
        IFNULL(NEW.quantity_quality_check, 0) + 
        IFNULL(NEW.quantity_damaged, 0) + 
        IFNULL(NEW.quantity_expired, 0) + 
        IFNULL(NEW.quantity_quarantine, 0)
    );
    
    -- تأكد من عدم كون الكمية المتاحة سالبة
    IF NEW.available_quantity < 0 THEN
        SET NEW.available_quantity = 0;
    END IF;
    
    -- تسجيل تغيير الحالة إذا تغيرت الكميات غير المتاحة
    IF (OLD.quantity_maintenance != NEW.quantity_maintenance OR
        OLD.quantity_quality_check != NEW.quantity_quality_check OR
        OLD.quantity_damaged != NEW.quantity_damaged OR
        OLD.quantity_expired != NEW.quantity_expired OR
        OLD.quantity_quarantine != NEW.quantity_quarantine) THEN
        
        SET NEW.last_status_update = NOW();
    END IF;
END$$

DELIMITER ;

-- =====================================================
-- 6. ENHANCED BRANCH DISTANCE SYSTEM UPDATES
-- تحديثات نظام المسافات المحسن
-- =====================================================

-- تحديث جدول الفروع لدعم المخزون غير المتاح
ALTER TABLE `cod_branch` 
ADD COLUMN `allow_unavailable_sales` TINYINT(1) NOT NULL DEFAULT 0 COMMENT 'السماح ببيع المخزون غير المتاح',
ADD COLUMN `auto_transfer_unavailable` TINYINT(1) NOT NULL DEFAULT 1 COMMENT 'نقل تلقائي للمخزون غير المتاح',
ADD COLUMN `unavailable_alert_threshold` DECIMAL(5,2) NOT NULL DEFAULT 10.00 COMMENT 'حد تنبيه المخزون غير المتاح %';

-- =====================================================
-- 7. INVENTORY ALERTS ENHANCEMENT
-- تحسين تنبيهات المخزون
-- =====================================================

-- إضافة أنواع تنبيهات جديدة لجدول التنبيهات الموجود
ALTER TABLE `cod_inventory_alert` 
ADD COLUMN `alert_subtype` VARCHAR(50) DEFAULT NULL COMMENT 'نوع فرعي للتنبيه',
ADD COLUMN `unavailable_quantity` DECIMAL(15,4) DEFAULT 0.0000 COMMENT 'الكمية غير المتاحة',
ADD COLUMN `affected_orders` INT(11) DEFAULT 0 COMMENT 'الطلبات المتأثرة',
ADD COLUMN `estimated_loss` DECIMAL(15,4) DEFAULT 0.0000 COMMENT 'الخسارة المقدرة';

-- =====================================================
-- 8. VIEWS FOR EASY QUERYING
-- Views للاستعلام السهل
-- =====================================================

-- عرض شامل لحالة المخزون
CREATE OR REPLACE VIEW `view_inventory_status_summary` AS
SELECT 
    pi.product_id,
    p.model,
    pd.name as product_name,
    pi.warehouse_id,
    w.name as warehouse_name,
    pi.unit_id,
    u.name as unit_name,
    pi.quantity as total_quantity,
    pi.available_quantity,
    pi.reserved_quantity,
    pi.quantity_maintenance,
    pi.quantity_quality_check,
    pi.quantity_damaged,
    pi.quantity_expired,
    pi.quantity_quarantine,
    (pi.quantity_maintenance + pi.quantity_quality_check + pi.quantity_damaged + pi.quantity_expired + pi.quantity_quarantine) as total_unavailable,
    ROUND(((pi.quantity_maintenance + pi.quantity_quality_check + pi.quantity_damaged + pi.quantity_expired + pi.quantity_quarantine) / pi.quantity * 100), 2) as unavailable_percentage,
    pi.last_status_update,
    pi.updated_at
FROM cod_product_inventory pi
LEFT JOIN cod_product p ON pi.product_id = p.product_id
LEFT JOIN cod_product_description pd ON p.product_id = pd.product_id AND pd.language_id = 1
LEFT JOIN cod_warehouse w ON pi.warehouse_id = w.warehouse_id
LEFT JOIN cod_unit u ON pi.unit_id = u.unit_id
WHERE pi.quantity > 0;

-- عرض المنتجات التي تحتاج انتباه
CREATE OR REPLACE VIEW `view_products_need_attention` AS
SELECT 
    pi.product_id,
    p.model,
    pd.name as product_name,
    pi.warehouse_id,
    w.name as warehouse_name,
    CASE 
        WHEN pi.quantity_damaged > 0 THEN 'damaged'
        WHEN pi.quantity_expired > 0 THEN 'expired'
        WHEN pi.quantity_quarantine > 0 THEN 'quarantine'
        WHEN pi.quantity_quality_check > 0 THEN 'quality_check'
        WHEN pi.quantity_maintenance > 0 THEN 'maintenance'
    END as priority_issue,
    (pi.quantity_maintenance + pi.quantity_quality_check + pi.quantity_damaged + pi.quantity_expired + pi.quantity_quarantine) as total_unavailable,
    pi.available_quantity,
    pi.last_status_update
FROM cod_product_inventory pi
LEFT JOIN cod_product p ON pi.product_id = p.product_id
LEFT JOIN cod_product_description pd ON p.product_id = pd.product_id AND pd.language_id = 1
LEFT JOIN cod_warehouse w ON pi.warehouse_id = w.warehouse_id
WHERE (pi.quantity_maintenance + pi.quantity_quality_check + pi.quantity_damaged + pi.quantity_expired + pi.quantity_quarantine) > 0
ORDER BY total_unavailable DESC, pi.last_status_update DESC;

-- =====================================================
-- 9. STORED PROCEDURES FOR INVENTORY STATUS MANAGEMENT
-- إجراءات مخزنة لإدارة حالات المخزون
-- =====================================================

DELIMITER $$

-- إجراء لتغيير حالة المخزون
CREATE PROCEDURE `sp_change_inventory_status`(
    IN p_product_id INT,
    IN p_warehouse_id INT,
    IN p_unit_id INT,
    IN p_quantity DECIMAL(15,4),
    IN p_from_status VARCHAR(20),
    IN p_to_status VARCHAR(20),
    IN p_reason_id INT,
    IN p_reason_notes TEXT,
    IN p_user_id INT
)
BEGIN
    DECLARE v_current_qty DECIMAL(15,4) DEFAULT 0;
    DECLARE v_error_msg VARCHAR(255);
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        GET DIAGNOSTICS CONDITION 1 v_error_msg = MESSAGE_TEXT;
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = v_error_msg;
    END;
    
    START TRANSACTION;
    
    -- التحقق من وجود المخزون
    SELECT quantity INTO v_current_qty 
    FROM cod_product_inventory 
    WHERE product_id = p_product_id 
      AND warehouse_id = p_warehouse_id 
      AND unit_id = p_unit_id;
    
    IF v_current_qty IS NULL THEN
        SIGNAL SQLSTATE '45000' SET MESSAGE_TEXT = 'المنتج غير موجود في المخزون';
    END IF;
    
    -- تحديث الكميات حسب الحالة
    CASE p_to_status
        WHEN 'maintenance' THEN
            UPDATE cod_product_inventory 
            SET quantity_maintenance = quantity_maintenance + p_quantity,
                last_status_update = NOW(),
                status_updated_by = p_user_id
            WHERE product_id = p_product_id AND warehouse_id = p_warehouse_id AND unit_id = p_unit_id;
            
        WHEN 'quality_check' THEN
            UPDATE cod_product_inventory 
            SET quantity_quality_check = quantity_quality_check + p_quantity,
                last_status_update = NOW(),
                status_updated_by = p_user_id
            WHERE product_id = p_product_id AND warehouse_id = p_warehouse_id AND unit_id = p_unit_id;
            
        WHEN 'damaged' THEN
            UPDATE cod_product_inventory 
            SET quantity_damaged = quantity_damaged + p_quantity,
                last_status_update = NOW(),
                status_updated_by = p_user_id
            WHERE product_id = p_product_id AND warehouse_id = p_warehouse_id AND unit_id = p_unit_id;
            
        WHEN 'expired' THEN
            UPDATE cod_product_inventory 
            SET quantity_expired = quantity_expired + p_quantity,
                last_status_update = NOW(),
                status_updated_by = p_user_id
            WHERE product_id = p_product_id AND warehouse_id = p_warehouse_id AND unit_id = p_unit_id;
            
        WHEN 'quarantine' THEN
            UPDATE cod_product_inventory 
            SET quantity_quarantine = quantity_quarantine + p_quantity,
                last_status_update = NOW(),
                status_updated_by = p_user_id
            WHERE product_id = p_product_id AND warehouse_id = p_warehouse_id AND unit_id = p_unit_id;
    END CASE;
    
    -- تسجيل التغيير في السجل
    INSERT INTO cod_inventory_status_log 
    (product_id, warehouse_id, unit_id, status_from, status_to, quantity, reason_id, reason_notes, created_by)
    VALUES 
    (p_product_id, p_warehouse_id, p_unit_id, p_from_status, p_to_status, p_quantity, p_reason_id, p_reason_notes, p_user_id);
    
    COMMIT;
END$$

DELIMITER ;

-- =====================================================
-- End of Corrected Database Queries
-- =====================================================
