-- إنشاء الجداول المفقودة الفعلية فقط - AYM ERP
-- تاريخ الإنشاء: 2025-07-28
-- الهدف: إنشاء الجداول المفقودة الفعلية من error.txt بعد الحصر الشامل

-- ===================================
-- 1. إضافة أعمدة مفقودة للجداول الموجودة
-- ===================================

-- إضافة أعمدة مفقودة لجدول cod_crm_campaign الموجود
ALTER TABLE `cod_crm_campaign`
ADD COLUMN IF NOT EXISTS `conversion_rate` decimal(5,2) NOT NULL DEFAULT 0.00 COMMENT 'معدل التحويل',
ADD COLUMN IF NOT EXISTS `roi` decimal(8,2) NOT NULL DEFAULT 0.00 COMMENT 'العائد على الاستثمار',
ADD COLUMN IF NOT EXISTS `total_recipients` int(11) NOT NULL DEFAULT 0 COMMENT 'إجمالي المستلمين',
ADD COLUMN IF NOT EXISTS `emails_sent` int(11) NOT NULL DEFAULT 0 COMMENT 'الرسائل المرسلة',
ADD COLUMN IF NOT EXISTS `emails_delivered` int(11) NOT NULL DEFAULT 0 COMMENT 'الرسائل المسلمة',
ADD COLUMN IF NOT EXISTS `emails_opened` int(11) NOT NULL DEFAULT 0 COMMENT 'الرسائل المفتوحة',
ADD COLUMN IF NOT EXISTS `emails_clicked` int(11) NOT NULL DEFAULT 0 COMMENT 'النقرات',
ADD COLUMN IF NOT EXISTS `conversions` int(11) NOT NULL DEFAULT 0 COMMENT 'التحويلات',
ADD COLUMN IF NOT EXISTS `revenue_generated` decimal(15,4) NOT NULL DEFAULT 0.0000 COMMENT 'الإيرادات المحققة';

-- ===================================
-- 2. جداول الذكاء الاصطناعي
-- ===================================

-- جدول كشف الاحتيال بالذكاء الاصطناعي
CREATE TABLE IF NOT EXISTS `cod_ai_fraud_detection` (
  `detection_id` int(11) NOT NULL AUTO_INCREMENT,
  `transaction_type` varchar(50) NOT NULL,
  `transaction_id` int(11) NOT NULL,
  `risk_score` decimal(5,2) NOT NULL DEFAULT 0.00,
  `fraud_probability` decimal(5,2) NOT NULL DEFAULT 0.00,
  `detection_date` datetime NOT NULL DEFAULT current_timestamp(),
  `status` enum('pending','reviewed','confirmed','false_positive') NOT NULL DEFAULT 'pending',
  `reviewed_by` int(11) DEFAULT NULL,
  `reviewed_at` datetime DEFAULT NULL,
  `notes` text DEFAULT NULL,
  PRIMARY KEY (`detection_id`),
  KEY `idx_transaction` (`transaction_type`, `transaction_id`),
  KEY `idx_risk_score` (`risk_score`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- جدول تحليل المشاعر بالذكاء الاصطناعي
CREATE TABLE IF NOT EXISTS `cod_ai_sentiment_analysis` (
  `analysis_id` int(11) NOT NULL AUTO_INCREMENT,
  `content_type` varchar(50) NOT NULL COMMENT 'review, feedback, comment',
  `content_id` int(11) NOT NULL,
  `sentiment_score` decimal(5,3) NOT NULL DEFAULT 0.000 COMMENT '-1 to 1',
  `sentiment_label` enum('negative','neutral','positive') NOT NULL,
  `confidence_score` decimal(5,3) NOT NULL DEFAULT 0.000,
  `analyzed_at` datetime NOT NULL DEFAULT current_timestamp(),
  `model_version` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`analysis_id`),
  KEY `idx_content` (`content_type`, `content_id`),
  KEY `idx_sentiment` (`sentiment_label`),
  KEY `idx_analyzed_at` (`analyzed_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- جدول تحسين الأسعار بالذكاء الاصطناعي
CREATE TABLE IF NOT EXISTS `cod_ai_price_optimization` (
  `optimization_id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL,
  `current_price` decimal(15,4) NOT NULL,
  `suggested_price` decimal(15,4) NOT NULL,
  `confidence_score` decimal(5,2) NOT NULL DEFAULT 0.00,
  `expected_demand_change` decimal(8,2) DEFAULT NULL COMMENT 'نسبة مئوية',
  `expected_revenue_change` decimal(8,2) DEFAULT NULL COMMENT 'نسبة مئوية',
  `optimization_date` datetime NOT NULL DEFAULT current_timestamp(),
  `status` enum('pending','applied','rejected') NOT NULL DEFAULT 'pending',
  `applied_by` int(11) DEFAULT NULL,
  `applied_at` datetime DEFAULT NULL,
  PRIMARY KEY (`optimization_id`),
  KEY `idx_product` (`product_id`),
  KEY `idx_status` (`status`),
  KEY `idx_optimization_date` (`optimization_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- جدول توقع الطلب بالذكاء الاصطناعي
CREATE TABLE IF NOT EXISTS `cod_ai_demand_forecast` (
  `forecast_id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL,
  `branch_id` int(11) DEFAULT NULL,
  `forecast_period` varchar(20) NOT NULL COMMENT 'daily, weekly, monthly',
  `forecast_date` date NOT NULL,
  `predicted_demand` decimal(15,4) NOT NULL,
  `confidence_interval_low` decimal(15,4) DEFAULT NULL,
  `confidence_interval_high` decimal(15,4) DEFAULT NULL,
  `actual_demand` decimal(15,4) DEFAULT NULL,
  `accuracy_score` decimal(5,2) DEFAULT NULL,
  `model_version` varchar(20) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`forecast_id`),
  KEY `idx_product_date` (`product_id`, `forecast_date`),
  KEY `idx_branch` (`branch_id`),
  KEY `idx_forecast_period` (`forecast_period`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- جدول تفاعلات الشات بوت
CREATE TABLE IF NOT EXISTS `cod_ai_chatbot_interactions` (
  `interaction_id` int(11) NOT NULL AUTO_INCREMENT,
  `session_id` varchar(100) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `user_message` text NOT NULL,
  `bot_response` text NOT NULL,
  `intent_detected` varchar(100) DEFAULT NULL,
  `confidence_score` decimal(5,2) DEFAULT NULL,
  `response_time_ms` int(11) DEFAULT NULL,
  `satisfaction_rating` tinyint(1) DEFAULT NULL COMMENT '1-5',
  `interaction_date` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`interaction_id`),
  KEY `idx_session` (`session_id`),
  KEY `idx_user` (`user_id`),
  KEY `idx_intent` (`intent_detected`),
  KEY `idx_interaction_date` (`interaction_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- جدول تحليل سلوك العملاء بالذكاء الاصطناعي
CREATE TABLE IF NOT EXISTS `cod_ai_customer_behavior` (
  `behavior_id` int(11) NOT NULL AUTO_INCREMENT,
  `customer_id` int(11) NOT NULL,
  `behavior_type` varchar(50) NOT NULL COMMENT 'purchase_pattern, browsing, engagement',
  `behavior_score` decimal(5,2) NOT NULL DEFAULT 0.00,
  `churn_probability` decimal(5,2) DEFAULT NULL,
  `lifetime_value_prediction` decimal(15,4) DEFAULT NULL,
  `next_purchase_probability` decimal(5,2) DEFAULT NULL,
  `preferred_categories` text DEFAULT NULL COMMENT 'JSON array',
  `analysis_date` datetime NOT NULL DEFAULT current_timestamp(),
  `model_version` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`behavior_id`),
  KEY `idx_customer` (`customer_id`),
  KEY `idx_behavior_type` (`behavior_type`),
  KEY `idx_analysis_date` (`analysis_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- جدول تحسين سلسلة التوريد بالذكاء الاصطناعي
CREATE TABLE IF NOT EXISTS `cod_ai_supply_chain_optimization` (
  `optimization_id` int(11) NOT NULL AUTO_INCREMENT,
  `optimization_type` varchar(50) NOT NULL COMMENT 'inventory, routing, supplier',
  `target_id` int(11) NOT NULL COMMENT 'product_id, route_id, supplier_id',
  `current_cost` decimal(15,4) DEFAULT NULL,
  `optimized_cost` decimal(15,4) DEFAULT NULL,
  `cost_savings` decimal(15,4) DEFAULT NULL,
  `efficiency_improvement` decimal(5,2) DEFAULT NULL COMMENT 'نسبة مئوية',
  `recommendation` text NOT NULL,
  `confidence_score` decimal(5,2) NOT NULL DEFAULT 0.00,
  `optimization_date` datetime NOT NULL DEFAULT current_timestamp(),
  `status` enum('pending','implemented','rejected') NOT NULL DEFAULT 'pending',
  PRIMARY KEY (`optimization_id`),
  KEY `idx_type_target` (`optimization_type`, `target_id`),
  KEY `idx_status` (`status`),
  KEY `idx_optimization_date` (`optimization_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- جدول تصنيف البيانات بالذكاء الاصطناعي
CREATE TABLE IF NOT EXISTS `cod_ai_classification` (
  `classification_id` int(11) NOT NULL AUTO_INCREMENT,
  `data_type` varchar(50) NOT NULL COMMENT 'product, customer, transaction',
  `data_id` int(11) NOT NULL,
  `classification_category` varchar(100) NOT NULL,
  `probability_score` decimal(5,3) NOT NULL DEFAULT 0.000,
  `confidence_level` enum('low','medium','high') NOT NULL DEFAULT 'medium',
  `classified_at` datetime NOT NULL DEFAULT current_timestamp(),
  `model_version` varchar(20) DEFAULT NULL,
  `verified` tinyint(1) NOT NULL DEFAULT 0,
  `verified_by` int(11) DEFAULT NULL,
  `verified_at` datetime DEFAULT NULL,
  PRIMARY KEY (`classification_id`),
  KEY `idx_data` (`data_type`, `data_id`),
  KEY `idx_category` (`classification_category`),
  KEY `idx_probability` (`probability_score`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ===================================
-- 3. إدراج بيانات تجريبية
-- ===================================

-- تحديث حملة CRM موجودة بالأعمدة الجديدة
UPDATE `cod_crm_campaign`
SET `conversion_rate` = 2.5, `roi` = 150.00, `total_recipients` = 1000, `emails_sent` = 950
WHERE `campaign_id` = 1 AND `type` = 'email';

-- إدراج حملة بريد إلكتروني تجريبية إذا لم تكن موجودة
INSERT IGNORE INTO `cod_crm_campaign`
(`campaign_id`, `name`, `type`, `start_date`, `end_date`, `budget`, `status`, `conversion_rate`, `roi`, `assigned_to_user_id`)
VALUES
(1, 'حملة ترحيبية', 'email', CURDATE(), DATE_ADD(CURDATE(), INTERVAL 30 DAY), 5000.00, 'active', 2.5, 150.00, 1);

-- إدراج بيانات تجريبية للذكاء الاصطناعي
INSERT IGNORE INTO `cod_ai_fraud_detection` 
(`detection_id`, `transaction_type`, `transaction_id`, `risk_score`, `fraud_probability`, `status`) 
VALUES 
(1, 'order', 1, 15.50, 0.15, 'reviewed');

INSERT IGNORE INTO `cod_ai_sentiment_analysis` 
(`analysis_id`, `content_type`, `content_id`, `sentiment_score`, `sentiment_label`, `confidence_score`) 
VALUES 
(1, 'review', 1, 0.750, 'positive', 0.850);

INSERT IGNORE INTO `cod_ai_demand_forecast` 
(`forecast_id`, `product_id`, `forecast_period`, `forecast_date`, `predicted_demand`) 
VALUES 
(1, 1, 'weekly', CURDATE(), 100.0000);

COMMIT;

-- ===================================
-- ملاحظات مهمة
-- ===================================
-- 1. هذا الملف ينشئ الجداول المفقودة الفعلية فقط
-- 2. تم حذف الجداول الموجودة مسبقاً (cod_product_movement, etc)
-- 3. يحتوي على بيانات تجريبية لتجنب الأخطاء
-- 4. جداول التحليلات المتقدمة ستُضاف في ملف منفصل
-- 5. جميع الجداول تستخدم البادئة cod_ الصحيحة
