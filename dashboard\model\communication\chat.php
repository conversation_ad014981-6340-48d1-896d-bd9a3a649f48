<?php
/**
 * نموذج الدردشة المباشرة المتقدم
 * Real-time Chat Model
 * 
 * نموذج البيانات لنظام الدردشة المباشرة مع تكامل catalog/inventory
 * مطور بمستوى عالمي لتفوق على Odoo
 * 
 * @package    AYM ERP
 * <AUTHOR> ERP Development Team
 * @copyright  2024 AYM ERP
 * @license    Proprietary
 * @version    1.0.0
 * @link       https://aym-erp.com
 * @since      2024-12-19
 */

class ModelCommunicationChat extends Model {
    
    /**
     * الحصول على المحادثات الحديثة للمستخدم
     */
    public function getRecentChats($user_id) {
        $query = $this->db->query("SELECT c.*,
            (SELECT message_text FROM " . DB_PREFIX . "internal_message im
             WHERE im.conversation_id = c.conversation_id
             ORDER BY im.sent_at DESC LIMIT 1) as last_message,
            (SELECT sent_at FROM " . DB_PREFIX . "internal_message im
             WHERE im.conversation_id = c.conversation_id
             ORDER BY im.sent_at DESC LIMIT 1) as last_message_time,
            (SELECT COUNT(*) FROM " . DB_PREFIX . "internal_message im
             WHERE im.conversation_id = c.conversation_id
             AND im.sender_id != '" . (int)$user_id . "') as unread_count
            FROM " . DB_PREFIX . "internal_conversation c
            INNER JOIN " . DB_PREFIX . "internal_participant cp ON (c.conversation_id = cp.conversation_id)
            WHERE cp.user_id = '" . (int)$user_id . "'
            AND c.status = 'active'
            ORDER BY last_message_time DESC
            LIMIT 10");

        return $query->rows;
    }
    
    /**
     * الحصول على المحادثات النشطة
     */
    public function getActiveChats($user_id) {
        $query = $this->db->query("SELECT c.*, 
            COUNT(DISTINCT cp.user_id) as participants_count,
            (SELECT COUNT(*) FROM " . DB_PREFIX . "chat_message cm 
             WHERE cm.chat_id = c.chat_id 
             AND cm.created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)) as recent_activity
            FROM " . DB_PREFIX . "chat c
            INNER JOIN " . DB_PREFIX . "chat_participant cp ON (c.chat_id = cp.chat_id)
            WHERE cp.user_id = '" . (int)$user_id . "'
            AND c.status = 'active'
            AND recent_activity > 0
            GROUP BY c.chat_id
            ORDER BY recent_activity DESC");
        
        return $query->rows;
    }
    
    /**
     * الحصول على المحادثات الجماعية
     */
    public function getGroupChats($user_id) {
        $query = $this->db->query("SELECT c.*, 
            COUNT(DISTINCT cp.user_id) as participants_count
            FROM " . DB_PREFIX . "chat c
            INNER JOIN " . DB_PREFIX . "chat_participant cp ON (c.chat_id = cp.chat_id)
            WHERE cp.user_id = '" . (int)$user_id . "'
            AND c.chat_type = 'group'
            AND c.status = 'active'
            GROUP BY c.chat_id
            ORDER BY c.created_at DESC");
        
        return $query->rows;
    }
    
    /**
     * الحصول على المستخدمين المتصلين
     */
    public function getOnlineUsers() {
        $query = $this->db->query("SELECT u.user_id, u.username, 
            CONCAT(u.firstname, ' ', u.lastname) as name,
            u.image,
            us.last_activity,
            CASE 
                WHEN us.last_activity >= DATE_SUB(NOW(), INTERVAL 5 MINUTE) THEN 'online'
                WHEN us.last_activity >= DATE_SUB(NOW(), INTERVAL 30 MINUTE) THEN 'away'
                ELSE 'offline'
            END as status
            FROM " . DB_PREFIX . "user u
            LEFT JOIN " . DB_PREFIX . "user_session us ON (u.user_id = us.user_id)
            WHERE u.status = 1
            AND us.last_activity >= DATE_SUB(NOW(), INTERVAL 2 HOUR)
            ORDER BY us.last_activity DESC");
        
        return $query->rows;
    }
    
    /**
     * الحصول على عدد الرسائل غير المقروءة
     */
    public function getUnreadCount($user_id) {
        $query = $this->db->query("SELECT COUNT(*) as total
            FROM " . DB_PREFIX . "chat_message cm
            INNER JOIN " . DB_PREFIX . "chat_participant cp ON (cm.chat_id = cp.chat_id)
            WHERE cp.user_id = '" . (int)$user_id . "'
            AND cm.sender_id != '" . (int)$user_id . "'
            AND cm.is_read = 0");
        
        return $query->row['total'];
    }
    
    /**
     * الحصول على عدد المحادثات النشطة
     */
    public function getActiveConversationsCount($user_id) {
        $query = $this->db->query("SELECT COUNT(DISTINCT c.chat_id) as total
            FROM " . DB_PREFIX . "chat c
            INNER JOIN " . DB_PREFIX . "chat_participant cp ON (c.chat_id = cp.chat_id)
            WHERE cp.user_id = '" . (int)$user_id . "'
            AND c.status = 'active'
            AND EXISTS (
                SELECT 1 FROM " . DB_PREFIX . "chat_message cm 
                WHERE cm.chat_id = c.chat_id 
                AND cm.created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
            )");
        
        return $query->row['total'];
    }
    
    /**
     * الحصول على عدد الرسائل اليوم
     */
    public function getTodayMessagesCount($user_id) {
        $query = $this->db->query("SELECT COUNT(*) as total
            FROM " . DB_PREFIX . "chat_message cm
            INNER JOIN " . DB_PREFIX . "chat_participant cp ON (cm.chat_id = cp.chat_id)
            WHERE cp.user_id = '" . (int)$user_id . "'
            AND DATE(cm.created_at) = CURDATE()");
        
        return $query->row['total'];
    }
    
    /**
     * إنشاء محادثة جديدة
     */
    public function createChat($data) {
        $this->db->query("INSERT INTO " . DB_PREFIX . "internal_conversation SET
            title = '" . $this->db->escape($data['title']) . "',
            type = '" . $this->db->escape($data['type'] ?? 'group') . "',
            creator_id = '" . (int)$this->user->getId() . "',
            status = 'active',
            associated_module = '" . $this->db->escape($data['associated_module'] ?? '') . "',
            reference_id = '" . (int)($data['reference_id'] ?? 0) . "',
            created_at = NOW()");

        $conversation_id = $this->db->getLastId();

        // إضافة المشاركين
        if (!empty($data['participants'])) {
            foreach ($data['participants'] as $user_id) {
                $this->addChatParticipant($conversation_id, $user_id);
            }
        }

        // إضافة المنشئ كمشارك
        $this->addChatParticipant($conversation_id, $this->user->getId(), 'admin');

        // تسجيل النشاط
        $this->load->model('activity_log');
        $this->model_activity_log->logCreate('communication', 'conversation', $conversation_id, $data);

        return $conversation_id;
    }
    
    /**
     * إضافة مشارك للمحادثة
     */
    public function addChatParticipant($chat_id, $user_id, $role = 'member') {
        $check = $this->db->query("SELECT participant_id FROM " . DB_PREFIX . "chat_participant 
            WHERE chat_id = '" . (int)$chat_id . "' AND user_id = '" . (int)$user_id . "'");
        
        if (!$check->num_rows) {
            $this->db->query("INSERT INTO " . DB_PREFIX . "chat_participant SET
                chat_id = '" . (int)$chat_id . "',
                user_id = '" . (int)$user_id . "',
                role = '" . $this->db->escape($role) . "',
                joined_at = NOW()");
        }
    }
    
    /**
     * إرسال رسالة
     */
    public function sendMessage($chat_id, $content, $message_type = 'text') {
        $this->db->query("INSERT INTO " . DB_PREFIX . "chat_message SET
            chat_id = '" . (int)$chat_id . "',
            sender_id = '" . (int)$this->user->getId() . "',
            content = '" . $this->db->escape($content) . "',
            message_type = '" . $this->db->escape($message_type) . "',
            created_at = NOW()");
        
        $message_id = $this->db->getLastId();
        
        // تحديث آخر نشاط في المحادثة
        $this->db->query("UPDATE " . DB_PREFIX . "chat SET 
            last_activity = NOW() 
            WHERE chat_id = '" . (int)$chat_id . "'");
        
        return $message_id;
    }
    
    /**
     * الحصول على رسائل المحادثة
     */
    public function getChatMessages($chat_id, $limit = 50, $offset = 0) {
        $query = $this->db->query("SELECT cm.*, 
            CONCAT(u.firstname, ' ', u.lastname) as sender_name,
            u.image as sender_image
            FROM " . DB_PREFIX . "chat_message cm
            LEFT JOIN " . DB_PREFIX . "user u ON (cm.sender_id = u.user_id)
            WHERE cm.chat_id = '" . (int)$chat_id . "'
            ORDER BY cm.created_at DESC
            LIMIT " . (int)$offset . ", " . (int)$limit);
        
        return array_reverse($query->rows);
    }
    
    /**
     * تحديد الرسائل كمقروءة
     */
    public function markMessagesAsRead($chat_id, $user_id) {
        $this->db->query("UPDATE " . DB_PREFIX . "chat_message SET
            is_read = 1,
            read_at = NOW()
            WHERE chat_id = '" . (int)$chat_id . "'
            AND sender_id != '" . (int)$user_id . "'
            AND is_read = 0");
    }
    
    /**
     * الحصول على معلومات المحادثة
     */
    public function getChat($chat_id) {
        $query = $this->db->query("SELECT c.*, 
            CONCAT(u.firstname, ' ', u.lastname) as creator_name
            FROM " . DB_PREFIX . "chat c
            LEFT JOIN " . DB_PREFIX . "user u ON (c.created_by = u.user_id)
            WHERE c.chat_id = '" . (int)$chat_id . "'");
        
        return $query->num_rows ? $query->row : false;
    }
    
    /**
     * الحصول على مشاركي المحادثة
     */
    public function getChatParticipants($chat_id) {
        $query = $this->db->query("SELECT cp.*, 
            CONCAT(u.firstname, ' ', u.lastname) as name,
            u.image,
            u.email
            FROM " . DB_PREFIX . "chat_participant cp
            LEFT JOIN " . DB_PREFIX . "user u ON (cp.user_id = u.user_id)
            WHERE cp.chat_id = '" . (int)$chat_id . "'
            ORDER BY cp.joined_at ASC");
        
        return $query->rows;
    }
    
    /**
     * البحث في المحادثات
     */
    public function searchChats($user_id, $search_term) {
        $query = $this->db->query("SELECT DISTINCT c.*, 
            CONCAT(u.firstname, ' ', u.lastname) as creator_name
            FROM " . DB_PREFIX . "chat c
            LEFT JOIN " . DB_PREFIX . "user u ON (c.created_by = u.user_id)
            INNER JOIN " . DB_PREFIX . "chat_participant cp ON (c.chat_id = cp.chat_id)
            LEFT JOIN " . DB_PREFIX . "chat_message cm ON (c.chat_id = cm.chat_id)
            WHERE cp.user_id = '" . (int)$user_id . "'
            AND (c.title LIKE '%" . $this->db->escape($search_term) . "%'
                 OR cm.content LIKE '%" . $this->db->escape($search_term) . "%')
            ORDER BY c.last_activity DESC
            LIMIT 20");
        
        return $query->rows;
    }
}
