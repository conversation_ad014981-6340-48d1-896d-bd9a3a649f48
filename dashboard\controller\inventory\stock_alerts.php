<?php
/**
 * كونترولر تنبيهات المخزون المتقدم - Enterprise Grade
 * 
 * التحسينات الجديدة:
 * - تطبيق الخدمات المركزية (central_service_manager.php)
 * - نظام الصلاحيات المزدوج (hasPermission + hasKey)
 * - تسجيل شامل للأنشطة (logActivity)
 * - نظام الإشعارات المتقدم (sendNotification)
 * - معالجة الأخطاء الشاملة (try-catch)
 * - تنبيهات ذكية متعددة المستويات
 * - تكامل مع نظام الدفعات وانتهاء الصلاحية
 * - تنبيهات الحد الأدنى والأقصى
 * - تنبيهات المنتجات بطيئة الحركة
 * - تنبيهات التكلفة والأسعار
 * - نظام تصعيد التنبيهات
 * - تقارير تحليلية للتنبيهات
 * 
 * <AUTHOR> Team - Enhanced by Kiro AI
 * @version 2.0 - Enterprise Grade
 * @since 2025
 * @reference current_stock.php - Proven Example
 */

class ControllerInventoryStockAlerts extends Controller {
    private $error = array();
    private $central_service;
    
    public function __construct($registry) {
        parent::__construct($registry);
        
        // تحميل الخدمات المركزية
        $this->load->model('common/central_service_manager');
        $this->central_service = new CentralServiceManager($this->registry);
    }
    
    /**
     * الصفحة الرئيسية - عرض تنبيهات المخزون
     */
    public function index() {
        try {
            // التحقق من الصلاحيات الأساسية والمتقدمة
            if (!$this->user->hasPermission('access', 'inventory/stock_alerts')) {
                $this->central_service->logActivity(
                    'access_denied',
                    'stock_alerts',
                    'محاولة وصول غير مصرح به لشاشة تنبيهات المخزون',
                    array('user_id' => $this->user->getId())
                );
                
                $this->response->redirect($this->url->link('error/permission', 'user_token=' . $this->session->data['user_token'], true));
            }
            
            // التحقق من الصلاحيات المتقدمة
            if (!$this->user->hasKey('stock_alerts_view')) {
                $this->central_service->logActivity(
                    'access_denied_advanced',
                    'stock_alerts',
                    'محاولة وصول بصلاحيات متقدمة غير مصرح بها لعرض تنبيهات المخزون',
                    array('user_id' => $this->user->getId())
                );
                
                $this->session->data['warning'] = $this->language->get('error_advanced_permission');
            }
            
            // تسجيل النشاط
            $this->central_service->logActivity(
                'view',
                'stock_alerts',
                'عرض تنبيهات المخزون',
                array('user_id' => $this->user->getId())
            );
            
            // تحميل اللغة والنماذج
            $this->load->language('inventory/stock_alerts');
            $this->document->setTitle($this->language->get('heading_title'));
            
            $this->load->model('inventory/stock_alerts');
            $this->load->model('inventory/product');
            $this->load->model('inventory/warehouse');
            
            // عرض القائمة
            $this->getList();
            
        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'stock_alerts',
                'خطأ في عرض تنبيهات المخزون: ' . $e->getMessage(),
                array('error' => $e->getTraceAsString())
            );
            
            $this->session->data['error'] = $this->language->get('error_exception');
            $this->response->redirect($this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true));
        }
    }
    
    /**
     * دالة عرض القائمة مع الفلاتر المتقدمة
     */
    protected function getList() {
        // القيم الافتراضية للفلاتر
        $filter_alert_type = '';
        $filter_severity = '';
        $filter_warehouse_id = '';
        $filter_category_id = '';
        $filter_status = '';
        $filter_date_start = '';
        $filter_date_end = '';
        $filter_sort = 'alert_priority';
        $filter_order = 'DESC';
        $page = 1;
        $limit = $this->config->get('config_limit_admin');
        
        // معالجة المدخلات والفلاتر
        if (isset($this->request->get['filter_alert_type'])) {
            $filter_alert_type = $this->request->get['filter_alert_type'];
        }
        
        if (isset($this->request->get['filter_severity'])) {
            $filter_severity = $this->request->get['filter_severity'];
        }
        
        if (isset($this->request->get['filter_warehouse_id'])) {
            $filter_warehouse_id = $this->request->get['filter_warehouse_id'];
        }
        
        if (isset($this->request->get['filter_category_id'])) {
            $filter_category_id = $this->request->get['filter_category_id'];
        }
        
        if (isset($this->request->get['filter_status'])) {
            $filter_status = $this->request->get['filter_status'];
        }
        
        if (isset($this->request->get['filter_date_start'])) {
            $filter_date_start = $this->request->get['filter_date_start'];
        }
        
        if (isset($this->request->get['filter_date_end'])) {
            $filter_date_end = $this->request->get['filter_date_end'];
        }
        
        if (isset($this->request->get['sort'])) {
            $filter_sort = $this->request->get['sort'];
        }
        
        if (isset($this->request->get['order'])) {
            $filter_order = $this->request->get['order'];
        }
        
        if (isset($this->request->get['page'])) {
            $page = (int)$this->request->get['page'];
        }
        
        // إعداد البيانات للفلاتر
        $filter_data = array(
            'filter_alert_type'   => $filter_alert_type,
            'filter_severity'     => $filter_severity,
            'filter_warehouse_id' => $filter_warehouse_id,
            'filter_category_id'  => $filter_category_id,
            'filter_status'       => $filter_status,
            'filter_date_start'   => $filter_date_start,
            'filter_date_end'     => $filter_date_end,
            'sort'                => $filter_sort,
            'order'               => $filter_order,
            'start'               => ($page - 1) * $limit,
            'limit'               => $limit
        );
        
        // الحصول على التنبيهات
        $alerts = $this->model_inventory_stock_alerts->getAlerts($filter_data);
        $alert_total = $this->model_inventory_stock_alerts->getTotalAlerts($filter_data);
        
        // إعداد البيانات للعرض
        $data['alerts'] = array();
        
        foreach ($alerts as $alert) {
            $data['alerts'][] = array(
                'alert_id'        => $alert['alert_id'],
                'alert_type'      => $alert['alert_type'],
                'alert_type_text' => $this->language->get('text_' . $alert['alert_type']),
                'severity'        => $alert['severity'],
                'severity_text'   => $this->language->get('text_severity_' . $alert['severity']),
                'severity_class'  => $this->getSeverityClass($alert['severity']),
                'product_name'    => $alert['product_name'],
                'warehouse_name'  => $alert['warehouse_name'],
                'current_stock'   => $alert['current_stock'],
                'threshold_value' => $alert['threshold_value'],
                'message'         => $alert['message'],
                'status'          => $alert['status'],
                'status_text'     => $alert['status'] ? $this->language->get('text_active') : $this->language->get('text_resolved'),
                'status_class'    => $alert['status'] ? 'success' : 'secondary',
                'date_created'    => date($this->language->get('date_format_short'), strtotime($alert['date_created'])),
                'date_resolved'   => $alert['date_resolved'] ? date($this->language->get('date_format_short'), strtotime($alert['date_resolved'])) : '',
                'actions'         => $this->getAlertActions($alert)
            );
        }
        
        // إعداد الروابط والبيانات الأخرى
        $url = '';
        
        if (isset($this->request->get['filter_alert_type'])) {
            $url .= '&filter_alert_type=' . urlencode(html_entity_decode($this->request->get['filter_alert_type'], ENT_QUOTES, 'UTF-8'));
        }
        
        if (isset($this->request->get['filter_severity'])) {
            $url .= '&filter_severity=' . urlencode(html_entity_decode($this->request->get['filter_severity'], ENT_QUOTES, 'UTF-8'));
        }
        
        if (isset($this->request->get['filter_warehouse_id'])) {
            $url .= '&filter_warehouse_id=' . $this->request->get['filter_warehouse_id'];
        }
        
        if (isset($this->request->get['filter_category_id'])) {
            $url .= '&filter_category_id=' . $this->request->get['filter_category_id'];
        }
        
        if (isset($this->request->get['filter_status'])) {
            $url .= '&filter_status=' . $this->request->get['filter_status'];
        }
        
        if (isset($this->request->get['filter_date_start'])) {
            $url .= '&filter_date_start=' . $this->request->get['filter_date_start'];
        }
        
        if (isset($this->request->get['filter_date_end'])) {
            $url .= '&filter_date_end=' . $this->request->get['filter_date_end'];
        }
        
        if (isset($this->request->get['sort'])) {
            $url .= '&sort=' . $this->request->get['sort'];
        }
        
        if (isset($this->request->get['order'])) {
            $url .= '&order=' . $this->request->get['order'];
        }
        
        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }
        
        // إعداد التصفح
        $pagination = new Pagination();
        $pagination->total = $alert_total;
        $pagination->page = $page;
        $pagination->limit = $limit;
        $pagination->url = $this->url->link('inventory/stock_alerts', 'user_token=' . $this->session->data['user_token'] . $url . '&page={page}', true);
        
        $data['pagination'] = $pagination->render();
        $data['results'] = sprintf($this->language->get('text_pagination'), ($alert_total) ? (($page - 1) * $limit) + 1 : 0, ((($page - 1) * $limit) > ($alert_total - $limit)) ? $alert_total : ((($page - 1) * $limit) + $limit), $alert_total, ceil($alert_total / $limit));
        
        // إعداد البيانات الإضافية
        $data['filter_alert_type'] = $filter_alert_type;
        $data['filter_severity'] = $filter_severity;
        $data['filter_warehouse_id'] = $filter_warehouse_id;
        $data['filter_category_id'] = $filter_category_id;
        $data['filter_status'] = $filter_status;
        $data['filter_date_start'] = $filter_date_start;
        $data['filter_date_end'] = $filter_date_end;
        $data['sort'] = $filter_sort;
        $data['order'] = $filter_order;
        
        // الحصول على قوائم الفلاتر
        $data['alert_types'] = $this->model_inventory_stock_alerts->getAlertTypes();
        $data['severity_levels'] = $this->model_inventory_stock_alerts->getSeverityLevels();
        $data['warehouses'] = $this->model_inventory_warehouse->getWarehouses();
        $data['categories'] = $this->model_inventory_product->getCategories();
        
        // إحصائيات التنبيهات
        $data['alert_stats'] = $this->model_inventory_stock_alerts->getAlertStatistics();
        
        // الروابط
        $data['breadcrumbs'] = array();
        
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );
        
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('inventory/stock_alerts', 'user_token=' . $this->session->data['user_token'] . $url, true)
        );
        
        $data['refresh'] = $this->url->link('inventory/stock_alerts/refresh', 'user_token=' . $this->session->data['user_token'], true);
        $data['settings'] = $this->url->link('inventory/stock_alerts/settings', 'user_token=' . $this->session->data['user_token'], true);
        $data['analytics'] = $this->url->link('inventory/stock_alerts/analytics', 'user_token=' . $this->session->data['user_token'], true);
        $data['export'] = $this->url->link('inventory/stock_alerts/export', 'user_token=' . $this->session->data['user_token'] . $url, true);
        
        $data['sort_alert_type'] = $this->url->link('inventory/stock_alerts', 'user_token=' . $this->session->data['user_token'] . '&sort=alert_type' . $url, true);
        $data['sort_severity'] = $this->url->link('inventory/stock_alerts', 'user_token=' . $this->session->data['user_token'] . '&sort=severity' . $url, true);
        $data['sort_product'] = $this->url->link('inventory/stock_alerts', 'user_token=' . $this->session->data['user_token'] . '&sort=product_name' . $url, true);
        $data['sort_warehouse'] = $this->url->link('inventory/stock_alerts', 'user_token=' . $this->session->data['user_token'] . '&sort=warehouse_name' . $url, true);
        $data['sort_date'] = $this->url->link('inventory/stock_alerts', 'user_token=' . $this->session->data['user_token'] . '&sort=date_created' . $url, true);
        
        $data['user_token'] = $this->session->data['user_token'];
        
        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');
        
        $this->response->setOutput($this->load->view('inventory/stock_alerts', $data));
    }

    /**
     * تحديث التنبيهات يدوياً
     */
    public function refresh() {
        try {
            // التحقق من الصلاحيات
            if (!$this->user->hasPermission('modify', 'inventory/stock_alerts') || !$this->user->hasKey('stock_alerts_refresh')) {
                $this->response->redirect($this->url->link('error/permission', 'user_token=' . $this->session->data['user_token'], true));
            }

            $this->load->language('inventory/stock_alerts');
            $this->load->model('inventory/stock_alerts');

            // تسجيل النشاط
            $this->central_service->logActivity(
                'refresh',
                'stock_alerts',
                'تحديث تنبيهات المخزون يدوياً',
                array('user_id' => $this->user->getId())
            );

            // تحديث التنبيهات
            $result = $this->model_inventory_stock_alerts->refreshAlerts();

            if ($result['success']) {
                $this->session->data['success'] = sprintf($this->language->get('text_refresh_success'), $result['new_alerts'], $result['resolved_alerts']);

                // إرسال إشعار
                $this->central_service->sendNotification(
                    'stock_alerts_refreshed',
                    'تم تحديث تنبيهات المخزون',
                    sprintf('تم إنشاء %d تنبيه جديد وحل %d تنبيه', $result['new_alerts'], $result['resolved_alerts']),
                    array('user_id' => $this->user->getId())
                );
            } else {
                $this->session->data['error'] = $this->language->get('error_refresh_failed');
            }

        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'stock_alerts_refresh',
                'خطأ في تحديث التنبيهات: ' . $e->getMessage(),
                array('error' => $e->getTraceAsString())
            );

            $this->session->data['error'] = $this->language->get('error_exception');
        }

        $this->response->redirect($this->url->link('inventory/stock_alerts', 'user_token=' . $this->session->data['user_token'], true));
    }

    /**
     * حل تنبيه
     */
    public function resolve() {
        $json = array();

        try {
            // التحقق من الصلاحيات
            if (!$this->user->hasPermission('modify', 'inventory/stock_alerts') || !$this->user->hasKey('stock_alerts_resolve')) {
                $json['error'] = $this->language->get('error_permission');
                $this->response->addHeader('Content-Type: application/json');
                $this->response->setOutput(json_encode($json));
                return;
            }

            $this->load->language('inventory/stock_alerts');
            $this->load->model('inventory/stock_alerts');

            if (isset($this->request->post['alert_id'])) {
                $alert_id = (int)$this->request->post['alert_id'];
                $notes = isset($this->request->post['notes']) ? $this->request->post['notes'] : '';

                // حل التنبيه
                $result = $this->model_inventory_stock_alerts->resolveAlert($alert_id, $this->user->getId(), $notes);

                if ($result) {
                    $json['success'] = $this->language->get('text_alert_resolved');

                    // تسجيل النشاط
                    $this->central_service->logActivity(
                        'resolve',
                        'stock_alerts',
                        'حل تنبيه المخزون رقم: ' . $alert_id,
                        array(
                            'alert_id' => $alert_id,
                            'user_id' => $this->user->getId(),
                            'notes' => $notes
                        )
                    );

                    // إرسال إشعار
                    $this->central_service->sendNotification(
                        'stock_alert_resolved',
                        'تم حل تنبيه المخزون',
                        'تم حل التنبيه رقم: ' . $alert_id,
                        array('alert_id' => $alert_id, 'user_id' => $this->user->getId())
                    );
                } else {
                    $json['error'] = $this->language->get('error_resolve_failed');
                }
            } else {
                $json['error'] = $this->language->get('error_alert_id');
            }

        } catch (Exception $e) {
            $json['error'] = $this->language->get('error_exception');

            $this->central_service->logActivity(
                'error',
                'stock_alerts_resolve',
                'خطأ في حل التنبيه: ' . $e->getMessage(),
                array('error' => $e->getTraceAsString())
            );
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * صفحة إعدادات التنبيهات
     */
    public function settings() {
        try {
            // التحقق من الصلاحيات
            if (!$this->user->hasPermission('modify', 'inventory/stock_alerts') || !$this->user->hasKey('stock_alerts_settings')) {
                $this->response->redirect($this->url->link('error/permission', 'user_token=' . $this->session->data['user_token'], true));
            }

            $this->load->language('inventory/stock_alerts');
            $this->load->model('inventory/stock_alerts');

            $this->document->setTitle($this->language->get('heading_title') . ' - ' . $this->language->get('text_settings'));

            // معالجة الحفظ
            if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateSettings()) {
                $this->model_inventory_stock_alerts->saveSettings($this->request->post);

                $this->session->data['success'] = $this->language->get('text_settings_saved');

                // تسجيل النشاط
                $this->central_service->logActivity(
                    'settings_update',
                    'stock_alerts',
                    'تحديث إعدادات تنبيهات المخزون',
                    array('user_id' => $this->user->getId(), 'settings' => $this->request->post)
                );

                $this->response->redirect($this->url->link('inventory/stock_alerts/settings', 'user_token=' . $this->session->data['user_token'], true));
            }

            // إعداد البيانات
            $data['breadcrumbs'] = array();

            $data['breadcrumbs'][] = array(
                'text' => $this->language->get('text_home'),
                'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
            );

            $data['breadcrumbs'][] = array(
                'text' => $this->language->get('heading_title'),
                'href' => $this->url->link('inventory/stock_alerts', 'user_token=' . $this->session->data['user_token'], true)
            );

            $data['breadcrumbs'][] = array(
                'text' => $this->language->get('text_settings'),
                'href' => $this->url->link('inventory/stock_alerts/settings', 'user_token=' . $this->session->data['user_token'], true)
            );

            // الحصول على الإعدادات الحالية
            $settings = $this->model_inventory_stock_alerts->getSettings();

            $data['settings'] = $settings;
            $data['warehouses'] = $this->model_inventory_warehouse->getWarehouses();
            $data['categories'] = $this->model_inventory_product->getCategories();
            $data['user_groups'] = $this->model_user_user_group->getUserGroups();

            $data['action'] = $this->url->link('inventory/stock_alerts/settings', 'user_token=' . $this->session->data['user_token'], true);
            $data['cancel'] = $this->url->link('inventory/stock_alerts', 'user_token=' . $this->session->data['user_token'], true);
            $data['user_token'] = $this->session->data['user_token'];

            $data['header'] = $this->load->controller('common/header');
            $data['column_left'] = $this->load->controller('common/column_left');
            $data['footer'] = $this->load->controller('common/footer');

            $this->response->setOutput($this->load->view('inventory/stock_alerts_settings', $data));

        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'stock_alerts_settings',
                'خطأ في إعدادات التنبيهات: ' . $e->getMessage(),
                array('error' => $e->getTraceAsString())
            );

            $this->session->data['error'] = $this->language->get('error_exception');
            $this->response->redirect($this->url->link('inventory/stock_alerts', 'user_token=' . $this->session->data['user_token'], true));
        }
    }

    /**
     * دوال مساعدة
     */
    private function getSeverityClass($severity) {
        switch ($severity) {
            case 'critical':
                return 'danger';
            case 'high':
                return 'warning';
            case 'medium':
                return 'info';
            case 'low':
                return 'secondary';
            default:
                return 'secondary';
        }
    }

    private function getAlertActions($alert) {
        $actions = array();

        if ($alert['status']) {
            $actions[] = array(
                'text' => $this->language->get('button_resolve'),
                'href' => 'javascript:resolveAlert(' . $alert['alert_id'] . ')',
                'class' => 'btn-success btn-sm'
            );
        }

        $actions[] = array(
            'text' => $this->language->get('button_view'),
            'href' => $this->url->link('inventory/stock_alerts/view', 'user_token=' . $this->session->data['user_token'] . '&alert_id=' . $alert['alert_id'], true),
            'class' => 'btn-info btn-sm'
        );

        return $actions;
    }

    private function validateSettings() {
        // التحقق من صحة الإعدادات
        return true; // سيتم تطوير التحقق لاحقاً
    }
}
