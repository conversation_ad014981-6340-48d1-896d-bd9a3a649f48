<?php
// Heading
$_['heading_title']                    = 'Cash Flow Statement';

// Text
$_['text_success']                     = 'Success: Cash Flow Statement has been generated successfully!';
$_['text_list']                        = 'Cash Flow Statement List';
$_['text_form']                        = 'Cash Flow Statement Form';
$_['text_view']                        = 'View Cash Flow Statement';
$_['text_generate']                    = 'Generate Cash Flow Statement';
$_['text_export']                      = 'Export Cash Flow Statement';
$_['text_compare']                     = 'Compare Cash Flow Statement';
$_['text_print']                       = 'Print Cash Flow Statement';
$_['text_cash_flow']                   = 'Cash Flow Statement';
$_['text_period']                      = 'Period';
$_['text_from']                        = 'From';
$_['text_to']                          = 'To';
$_['text_loading']                     = 'Loading...';
$_['text_processing']                  = 'Processing...';
$_['text_success_generate']            = 'Cash Flow Statement generated successfully!';
$_['text_success_export']              = 'Cash Flow Statement exported successfully!';
$_['text_success_compare']             = 'Comparison completed successfully!';

// Cash Flow Activities
$_['text_operating_activities']        = 'Operating Activities';
$_['text_investing_activities']        = 'Investing Activities';
$_['text_financing_activities']        = 'Financing Activities';

// Operating Activities
$_['text_net_income']                  = 'Net Income (Loss)';
$_['text_depreciation']                = 'Depreciation and Depletion';
$_['text_amortization']                = 'Amortization of Intangible Assets';
$_['text_bad_debt_expense']            = 'Bad Debt Expense';
$_['text_gain_loss_disposal']          = 'Gain (Loss) on Disposal of Assets';
$_['text_accounts_receivable_change']  = 'Change in Accounts Receivable';
$_['text_inventory_change']            = 'Change in Inventory';
$_['text_prepaid_expenses_change']     = 'Change in Prepaid Expenses';
$_['text_accounts_payable_change']     = 'Change in Accounts Payable';
$_['text_accrued_expenses_change']     = 'Change in Accrued Expenses';
$_['text_taxes_payable_change']        = 'Change in Taxes Payable';

// Investing Activities
$_['text_purchase_ppe']                = 'Purchase of Property, Plant and Equipment';
$_['text_sale_ppe']                    = 'Sale of Property, Plant and Equipment';
$_['text_purchase_investments']        = 'Purchase of Investments';
$_['text_sale_investments']            = 'Sale of Investments';
$_['text_acquisition_subsidiaries']    = 'Acquisition of Subsidiaries';
$_['text_disposal_subsidiaries']       = 'Disposal of Subsidiaries';
$_['text_loans_to_others']             = 'Loans to Others';
$_['text_collection_loans']            = 'Collection of Loans from Others';

// Financing Activities
$_['text_proceeds_debt']               = 'Proceeds from Debt';
$_['text_repayment_debt']              = 'Repayment of Debt';
$_['text_proceeds_equity']             = 'Proceeds from Equity Issuance';
$_['text_repurchase_shares']           = 'Repurchase of Shares';
$_['text_dividends_paid']              = 'Dividends Paid';
$_['text_interest_paid']               = 'Interest Paid';
$_['text_lease_payments']              = 'Lease Payments';

// Totals
$_['text_total_operating']             = 'Net Cash from Operating Activities';
$_['text_total_investing']             = 'Net Cash from Investing Activities';
$_['text_total_financing']             = 'Net Cash from Financing Activities';
$_['text_net_change']                  = 'Net Increase (Decrease) in Cash and Cash Equivalents';
$_['text_opening_balance']             = 'Cash and Cash Equivalents at Beginning of Period';
$_['text_closing_balance']             = 'Cash and Cash Equivalents at End of Period';

// Methods
$_['text_direct_method']               = 'Direct Method';
$_['text_indirect_method']             = 'Indirect Method';
$_['text_method']                      = 'Preparation Method';

// Column
$_['column_activity']                  = 'Activity';
$_['column_description']               = 'Description';
$_['column_amount']                    = 'Amount';
$_['column_current_period']            = 'Current Period';
$_['column_previous_period']           = 'Previous Period';
$_['column_variance']                  = 'Variance';
$_['column_percentage']                = 'Percentage';

// Entry
$_['entry_date_start']                 = 'Period Start Date';
$_['entry_date_end']                   = 'Period End Date';
$_['entry_comparison_start']           = 'Comparison Period Start Date';
$_['entry_comparison_end']             = 'Comparison Period End Date';
$_['entry_method']                     = 'Preparation Method';
$_['entry_branch']                     = 'Branch';
$_['entry_include_zero_flows']         = 'Include Zero Cash Flows';
$_['entry_show_comparative']           = 'Show Comparative';
$_['entry_cash_accounts']              = 'Cash Accounts';
$_['entry_export_format']              = 'Export Format';

// Button
$_['button_generate']                  = 'Generate';
$_['button_export']                    = 'Export';
$_['button_compare']                   = 'Compare';
$_['button_print']                     = 'Print';
$_['button_filter']                    = 'Filter';
$_['button_reset']                     = 'Reset';
$_['button_close']                     = 'Close';

// Tab
$_['tab_general']                      = 'General';
$_['tab_filters']                      = 'Filters';
$_['tab_options']                      = 'Options';
$_['tab_comparison']                   = 'Comparison';
$_['tab_operating']                    = 'Operating Activities';
$_['tab_investing']                    = 'Investing Activities';
$_['tab_financing']                    = 'Financing Activities';

// Help
$_['help_date_start']                  = 'Select the start date for cash flow period';
$_['help_date_end']                    = 'Select the end date for cash flow period';
$_['help_method']                      = 'Select the method for preparing cash flow statement';
$_['help_comparison']                  = 'Select period to compare with current period';

// Error
$_['error_warning']                    = 'Warning: Please check the form carefully for errors!';
$_['error_permission']                 = 'Warning: You do not have permission to access Cash Flow Statement!';
$_['error_date_start']                 = 'Period start date is required!';
$_['error_date_end']                   = 'Period end date is required!';
$_['error_date_range']                 = 'Start date must be before end date!';
$_['error_no_data']                    = 'No data for selected period!';

// Generate Options
$_['button_generate_and_new']          = 'Generate and New';
$_['button_generate_and_export']       = 'Generate and Export';

// Advanced Analytics
$_['text_cash_flow_analytics']         = 'Cash Flow Analytics';
$_['text_free_cash_flow']              = 'Free Cash Flow';
$_['text_operating_ratio']             = 'Operating Activities Ratio';
$_['text_investing_ratio']             = 'Investing Activities Ratio';
$_['text_financing_ratio']             = 'Financing Activities Ratio';

// Activity Classification
$_['text_activity_classification']     = 'Activity Classification';
$_['text_operating_activities']        = 'Operating Activities';
$_['text_investing_activities']        = 'Investing Activities';
$_['text_financing_activities']        = 'Financing Activities';

// Errors
$_['error_date_required']              = 'Date is required';
$_['error_account_required']           = 'Account is required';
?>
$_['error_export']                     = 'Error exporting data!';
$_['error_form']                       = 'Form data error!';
$_['error_missing_data']               = 'Missing data!';

// Export Formats
$_['text_excel']                       = 'Excel';
$_['text_pdf']                         = 'PDF';
$_['text_csv']                         = 'CSV';

// Print Options
$_['text_print_title']                 = 'Cash Flow Statement';
$_['print_title']                      = 'Print Cash Flow Statement';
$_['text_print_date']                  = 'Print Date';
$_['text_print_user']                  = 'Printed By';

// Status Messages
$_['text_generating']                  = 'Generating cash flow statement...';
$_['text_exporting']                   = 'Exporting data...';
$_['text_comparing']                   = 'Performing comparison...';
$_['text_completed']                   = 'Completed successfully!';

// Cash Flow Analysis
$_['text_cash_flow_analysis']          = 'Cash Flow Analysis';
$_['text_liquidity_ratios']            = 'Liquidity Ratios';
$_['text_operating_cash_ratio']        = 'Operating Cash Ratio';
$_['text_free_cash_flow']              = 'Free Cash Flow';
$_['text_cash_coverage_ratio']         = 'Cash Coverage Ratio';

// Egyptian Accounting Standards
$_['text_eas_compliant']               = 'Compliant with Egyptian Accounting Standards';
$_['text_eta_ready']                   = 'Ready for ETA Integration';
$_['text_egyptian_gaap']               = 'According to Egyptian Generally Accepted Accounting Principles';

// Additional Fields
$_['text_account_name']                = 'Account Name';
$_['text_amount']                      = 'Amount';
$_['text_activity']                    = 'Activity';

// New language variables for cash flow controller fixes
$_['log_unauthorized_access_cash_flow'] = 'Unauthorized access attempt to cash flow list';
$_['log_view_cash_flow_screen']        = 'View cash flow list screen';
$_['log_unauthorized_generate_cash_flow'] = 'Unauthorized cash flow generation attempt';
$_['log_generate_cash_flow_period']    = 'Generate cash flow statement for period';
$_['text_generate_cash_flow']          = 'Generate cash flow statement';
$_['text_cash_flow_generated_notification'] = 'Cash flow statement generated for period';
$_['log_unauthorized_export_cash_flow'] = 'Unauthorized cash flow export attempt';
$_['log_export_cash_flow']             = 'Export cash flow statement';
$_['text_export_cash_flow']            = 'Export cash flow statement';
$_['text_cash_flow_exported_notification'] = 'Cash flow statement exported in format';
$_['log_advanced_cash_flow_analysis']  = 'Advanced cash flow analysis';
$_['log_unauthorized_access_advanced_cash_flow'] = 'Unauthorized access attempt to advanced cash flow analysis';
$_['log_generate_advanced_cash_flow_period'] = 'Generate advanced cash flow analysis for period';
$_['text_advanced_cash_flow_analysis'] = 'Advanced cash flow analysis';
$_['text_advanced_cash_flow_generated_notification'] = 'Advanced AI-powered cash flow analysis generated for period';
$_['log_view_advanced_cash_flow']      = 'View advanced cash flow analysis';
$_['log_unauthorized_access_comprehensive_cash_flow'] = 'Unauthorized access attempt to comprehensive cash flow analysis';
$_['log_generate_comprehensive_cash_flow_period'] = 'Generate comprehensive cash flow analysis for period';
$_['text_comprehensive_cash_flow_analysis'] = 'Comprehensive cash flow analysis';
$_['text_comprehensive_cash_flow_generated_notification'] = 'Comprehensive cash flow analysis generated for period';
$_['log_view_comprehensive_cash_flow'] = 'View comprehensive cash flow analysis';

// Enhanced performance and analytics variables
$_['text_optimized_cash_flow']         = 'Optimized Cash Flow';
$_['text_cash_flow_trends']            = 'Cash Flow Trends';
$_['text_cash_flow_quality']           = 'Cash Flow Quality';
$_['text_cache_enabled']               = 'Cache Enabled';
$_['text_report_cached']               = 'Report Cached';
$_['text_operating_cash_flow']         = 'Operating Cash Flow';
$_['text_investing_cash_flow']         = 'Investing Cash Flow';
$_['text_financing_cash_flow']         = 'Financing Cash Flow';
$_['text_net_cash_flow']               = 'Net Cash Flow';
$_['text_cash_conversion_cycle']       = 'Cash Conversion Cycle';
$_['text_quality_ratio']               = 'Quality Ratio';
$_['text_sustainability_score']        = 'Sustainability Score';
$_['text_volatility_index']            = 'Volatility Index';
$_['text_quality_excellent']           = 'Excellent';
$_['text_quality_good']                = 'Good';
$_['text_quality_fair']                = 'Fair';
$_['text_quality_poor']                = 'Poor';
$_['text_trend_analysis']              = 'Trend Analysis';
$_['text_monthly_trends']              = 'Monthly Trends';
$_['button_trend_analysis']            = 'Trend Analysis';
$_['button_quality_analysis']          = 'Quality Analysis';
$_['text_loading_trends']              = 'Loading trends...';
$_['text_trends_ready']                = 'Trends ready';
?>
