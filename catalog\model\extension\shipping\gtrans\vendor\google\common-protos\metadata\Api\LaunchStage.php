<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/launch_stage.proto

namespace GPBMetadata\Google\Api;

class LaunchStage
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(hex2bin(
            "0afb010a1d676f6f676c652f6170692f6c61756e63685f73746167652e70" .
            "726f746f120a676f6f676c652e6170692a6a0a0b4c61756e636853746167" .
            "65121c0a184c41554e43485f53544147455f554e53504543494649454410" .
            "0012100a0c4541524c595f414343455353100112090a05414c5048411002" .
            "12080a0442455441100312060a0247411004120e0a0a4445505245434154" .
            "45441005425a0a0e636f6d2e676f6f676c652e61706942104c61756e6368" .
            "537461676550726f746f50015a2d676f6f676c652e676f6c616e672e6f72" .
            "672f67656e70726f746f2f676f6f676c65617069732f6170693b617069a2" .
            "020447415049620670726f746f33"
        ), true);

        static::$is_initialized = true;
    }
}

