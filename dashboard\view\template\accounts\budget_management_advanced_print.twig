<!DOCTYPE html>
<html dir="{{ direction }}" lang="{{ lang }}" class="no-js">
<head>
<meta charset="UTF-8" />
<title>{{ heading_title }}</title>
<base href="{{ base }}" />
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
<link href="view/stylesheet/bootstrap.css" rel="stylesheet" media="screen" />
<style>
@media print {
  body { margin: 0; }
  .no-print { display: none !important; }
  .page-break { page-break-before: always; }
}

body {
  font-family: Arial, sans-serif;
  font-size: 11px;
  line-height: 1.3;
}

.header {
  text-align: center;
  margin-bottom: 30px;
  border-bottom: 2px solid #333;
  padding-bottom: 20px;
}

.company-logo {
  max-height: 80px;
  margin-bottom: 10px;
}

.company-name {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}

.report-title {
  font-size: 18px;
  font-weight: bold;
  color: #666;
  margin-bottom: 10px;
}

.report-info {
  font-size: 10px;
  color: #888;
}

.summary-box {
  background-color: #f8f9fa;
  padding: 15px;
  margin-bottom: 20px;
  border: 1px solid #dee2e6;
}

.budget-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 20px;
  font-size: 9px;
}

.budget-table th,
.budget-table td {
  border: 1px solid #dee2e6;
  padding: 4px;
  text-align: left;
}

.budget-table th {
  background-color: #f8f9fa;
  font-weight: bold;
}

.text-right {
  text-align: right;
}

.text-center {
  text-align: center;
}

.text-success {
  color: #28a745;
}

.text-warning {
  color: #ffc107;
}

.text-danger {
  color: #dc3545;
}

.text-info {
  color: #17a2b8;
}

.footer {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #dee2e6;
  font-size: 9px;
  color: #888;
}

.signature-section {
  margin-top: 40px;
  display: flex;
  justify-content: space-between;
}

.signature-box {
  width: 200px;
  text-align: center;
}

.signature-line {
  border-top: 1px solid #333;
  margin-top: 50px;
  padding-top: 5px;
}

.watermark {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(-45deg);
  font-size: 72px;
  color: rgba(0,0,0,0.05);
  z-index: -1;
  pointer-events: none;
}

.status-badge {
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 9px;
  font-weight: bold;
}

.status-approved {
  background-color: #28a745;
  color: white;
}

.status-draft {
  background-color: #ffc107;
  color: black;
}

.status-rejected {
  background-color: #dc3545;
  color: white;
}
</style>
</head>
<body>

<!-- Watermark -->
<div class="watermark">{{ text_confidential }}</div>

<!-- Header -->
<div class="header">
  {% if company_logo %}
  <img src="{{ company_logo }}" alt="{{ company_name }}" class="company-logo">
  {% endif %}
  <div class="company-name">{{ company_name }}</div>
  <div class="report-title">{{ heading_title }}</div>
  <div class="report-info">
    {{ text_generated_on }}: {{ generated_date }} | {{ text_generated_by }}: {{ generated_by }}
  </div>
</div>

<!-- Budget Information -->
<div class="summary-box">
  <h3 style="margin-top: 0;">{{ text_budget_info }}</h3>
  <div style="display: flex; justify-content: space-between;">
    <div>
      <strong>{{ text_budget_name }}:</strong> {{ budget.budget_name }}<br>
      <strong>{{ text_budget_type }}:</strong> {{ budget.budget_type_name }}<br>
      <strong>{{ text_budget_year }}:</strong> {{ budget.budget_year }}<br>
      <strong>{{ text_currency }}:</strong> {{ budget.currency }}
    </div>
    <div>
      <strong>{{ text_start_date }}:</strong> {{ budget.start_date }}<br>
      <strong>{{ text_end_date }}:</strong> {{ budget.end_date }}<br>
      <strong>{{ text_status }}:</strong> 
      <span class="status-badge status-{{ budget.status }}">{{ budget.status_name }}</span><br>
      <strong>{{ text_created_by }}:</strong> {{ budget.created_by_name }}
    </div>
  </div>
  {% if budget.budget_description %}
  <div style="margin-top: 15px;">
    <strong>{{ text_description }}:</strong><br>
    {{ budget.budget_description }}
  </div>
  {% endif %}
</div>

<!-- Budget Summary -->
<h3>{{ text_budget_summary }}</h3>
<table class="budget-table">
  <thead>
    <tr>
      <th>{{ text_metric }}</th>
      <th class="text-right">{{ text_amount }}</th>
      <th class="text-right">{{ text_percentage }}</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>{{ text_total_revenue }}</td>
      <td class="text-right">{{ budget_summary.total_revenue }}</td>
      <td class="text-right">{{ budget_summary.revenue_percentage }}%</td>
    </tr>
    <tr>
      <td>{{ text_total_expenses }}</td>
      <td class="text-right">{{ budget_summary.total_expenses }}</td>
      <td class="text-right">{{ budget_summary.expense_percentage }}%</td>
    </tr>
    <tr style="background-color: #e2e3e5; font-weight: bold;">
      <td>{{ text_net_income }}</td>
      <td class="text-right">{{ budget_summary.net_income }}</td>
      <td class="text-right">{{ budget_summary.profit_margin }}%</td>
    </tr>
  </tbody>
</table>

<!-- Budget Lines -->
<h3>{{ text_budget_lines }}</h3>
<table class="budget-table">
  <thead>
    <tr>
      <th style="width: 8%;">{{ column_account }}</th>
      <th style="width: 15%;">{{ column_account_name }}</th>
      <th style="width: 6%;" class="text-right">{{ column_jan }}</th>
      <th style="width: 6%;" class="text-right">{{ column_feb }}</th>
      <th style="width: 6%;" class="text-right">{{ column_mar }}</th>
      <th style="width: 6%;" class="text-right">{{ column_apr }}</th>
      <th style="width: 6%;" class="text-right">{{ column_may }}</th>
      <th style="width: 6%;" class="text-right">{{ column_jun }}</th>
      <th style="width: 6%;" class="text-right">{{ column_jul }}</th>
      <th style="width: 6%;" class="text-right">{{ column_aug }}</th>
      <th style="width: 6%;" class="text-right">{{ column_sep }}</th>
      <th style="width: 6%;" class="text-right">{{ column_oct }}</th>
      <th style="width: 6%;" class="text-right">{{ column_nov }}</th>
      <th style="width: 6%;" class="text-right">{{ column_dec }}</th>
      <th style="width: 9%;" class="text-right">{{ column_total }}</th>
    </tr>
  </thead>
  <tbody>
    {% for line in budget_lines %}
    <tr>
      <td>{{ line.account_code }}</td>
      <td>{{ line.account_name }}</td>
      <td class="text-right">{{ line.jan_value }}</td>
      <td class="text-right">{{ line.feb_value }}</td>
      <td class="text-right">{{ line.mar_value }}</td>
      <td class="text-right">{{ line.apr_value }}</td>
      <td class="text-right">{{ line.may_value }}</td>
      <td class="text-right">{{ line.jun_value }}</td>
      <td class="text-right">{{ line.jul_value }}</td>
      <td class="text-right">{{ line.aug_value }}</td>
      <td class="text-right">{{ line.sep_value }}</td>
      <td class="text-right">{{ line.oct_value }}</td>
      <td class="text-right">{{ line.nov_value }}</td>
      <td class="text-right">{{ line.dec_value }}</td>
      <td class="text-right"><strong>{{ line.total_value }}</strong></td>
    </tr>
    {% endfor %}
  </tbody>
  <tfoot>
    <tr style="background-color: #e2e3e5; font-weight: bold;">
      <td colspan="2">{{ text_total }}</td>
      <td class="text-right">{{ budget_totals.jan }}</td>
      <td class="text-right">{{ budget_totals.feb }}</td>
      <td class="text-right">{{ budget_totals.mar }}</td>
      <td class="text-right">{{ budget_totals.apr }}</td>
      <td class="text-right">{{ budget_totals.may }}</td>
      <td class="text-right">{{ budget_totals.jun }}</td>
      <td class="text-right">{{ budget_totals.jul }}</td>
      <td class="text-right">{{ budget_totals.aug }}</td>
      <td class="text-right">{{ budget_totals.sep }}</td>
      <td class="text-right">{{ budget_totals.oct }}</td>
      <td class="text-right">{{ budget_totals.nov }}</td>
      <td class="text-right">{{ budget_totals.dec }}</td>
      <td class="text-right">{{ budget_totals.total }}</td>
    </tr>
  </tfoot>
</table>

<!-- Variance Analysis -->
{% if variance_analysis %}
<div class="page-break"></div>
<h3>{{ text_variance_analysis }}</h3>
<table class="budget-table">
  <thead>
    <tr>
      <th>{{ text_metric }}</th>
      <th class="text-right">{{ text_budget }}</th>
      <th class="text-right">{{ text_actual }}</th>
      <th class="text-right">{{ text_variance }}</th>
      <th class="text-right">{{ text_variance_percentage }}</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>{{ text_revenue }}</td>
      <td class="text-right">{{ variance_analysis.budget_revenue }}</td>
      <td class="text-right">{{ variance_analysis.actual_revenue }}</td>
      <td class="text-right {% if variance_analysis.revenue_variance >= 0 %}text-success{% else %}text-danger{% endif %}">
        {{ variance_analysis.revenue_variance }}
      </td>
      <td class="text-right {% if variance_analysis.revenue_variance_percentage >= 0 %}text-success{% else %}text-danger{% endif %}">
        {{ variance_analysis.revenue_variance_percentage }}%
      </td>
    </tr>
    <tr>
      <td>{{ text_expenses }}</td>
      <td class="text-right">{{ variance_analysis.budget_expenses }}</td>
      <td class="text-right">{{ variance_analysis.actual_expenses }}</td>
      <td class="text-right {% if variance_analysis.expense_variance <= 0 %}text-success{% else %}text-danger{% endif %}">
        {{ variance_analysis.expense_variance }}
      </td>
      <td class="text-right {% if variance_analysis.expense_variance_percentage <= 0 %}text-success{% else %}text-danger{% endif %}">
        {{ variance_analysis.expense_variance_percentage }}%
      </td>
    </tr>
    <tr style="background-color: #e2e3e5; font-weight: bold;">
      <td>{{ text_net_income }}</td>
      <td class="text-right">{{ variance_analysis.budget_net }}</td>
      <td class="text-right">{{ variance_analysis.actual_net }}</td>
      <td class="text-right {% if variance_analysis.net_variance >= 0 %}text-success{% else %}text-danger{% endif %}">
        {{ variance_analysis.net_variance }}
      </td>
      <td class="text-right {% if variance_analysis.net_variance_percentage >= 0 %}text-success{% else %}text-danger{% endif %}">
        {{ variance_analysis.net_variance_percentage }}%
      </td>
    </tr>
  </tbody>
</table>
{% endif %}

<!-- Performance Analysis -->
{% if performance_analysis %}
<h3>{{ text_performance_analysis }}</h3>
<table class="budget-table">
  <thead>
    <tr>
      <th>{{ text_performance_metric }}</th>
      <th class="text-right">{{ text_score }}</th>
      <th>{{ text_status }}</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>{{ text_achievement_rate }}</td>
      <td class="text-right">{{ performance_analysis.achievement_rate }}%</td>
      <td>
        {% if performance_analysis.achievement_rate >= 90 %}
          <span class="status-badge status-approved">{{ text_excellent }}</span>
        {% elseif performance_analysis.achievement_rate >= 70 %}
          <span class="status-badge status-draft">{{ text_good }}</span>
        {% else %}
          <span class="status-badge status-rejected">{{ text_needs_improvement }}</span>
        {% endif %}
      </td>
    </tr>
    <tr>
      <td>{{ text_efficiency_score }}</td>
      <td class="text-right">{{ performance_analysis.efficiency_score }}%</td>
      <td>
        {% if performance_analysis.efficiency_score >= 90 %}
          <span class="status-badge status-approved">{{ text_excellent }}</span>
        {% elseif performance_analysis.efficiency_score >= 70 %}
          <span class="status-badge status-draft">{{ text_good }}</span>
        {% else %}
          <span class="status-badge status-rejected">{{ text_needs_improvement }}</span>
        {% endif %}
      </td>
    </tr>
  </tbody>
</table>
{% endif %}

<!-- Signatures -->
<div class="signature-section">
  <div class="signature-box">
    <div class="signature-line">{{ text_prepared_by }}</div>
  </div>
  <div class="signature-box">
    <div class="signature-line">{{ text_reviewed_by }}</div>
  </div>
  <div class="signature-box">
    <div class="signature-line">{{ text_approved_by }}</div>
  </div>
</div>

<!-- Footer -->
<div class="footer">
  <div style="display: flex; justify-content: space-between;">
    <div>
      {{ text_confidential }} - {{ text_internal_use }}
    </div>
    <div>
      {{ text_eas_compliant }} | {{ text_eta_ready }} | {{ text_budget_control }}
    </div>
    <div>
      {{ text_page }} 1 {{ text_of }} 1
    </div>
  </div>
</div>

<script>
// Auto print when page loads
window.onload = function() {
  window.print();
};
</script>

</body>
</html>
