<?php
/**
 * كونترولر التحقق من المصادقة الثنائية - AYM ERP
 * 
 * @package    AYM ERP
 * <AUTHOR> Team
 * @copyright  2025 AYM ERP
 * @license    Commercial
 * @version    1.0.0
 */

class ControllerCommonTwoFactorVerify extends Controller {
    private $error = array();

    public function index() {
        $this->load->language('common/two_factor_verify');
        $this->load->model('user/two_factor_auth');

        $this->document->setTitle($this->language->get('heading_title'));

        // التحقق من وجود جلسة مصادقة ثنائية معلقة
        if (!isset($this->session->data['pending_2fa_user_id'])) {
            $this->response->redirect($this->url->link('common/login', '', true));
        }

        $user_id = $this->session->data['pending_2fa_user_id'];
        $user_info = $this->model_user_two_factor_auth->getUserTwoFactorInfo($user_id);

        if (!$user_info || !$user_info['two_factor_enabled']) {
            $this->response->redirect($this->url->link('common/login', '', true));
        }

        // معالجة التحقق
        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateTwoFactor()) {
            // نجح التحقق، إكمال تسجيل الدخول
            $this->completeLoginAfter2FA($user_id);
        }

        // إعداد البيانات للعرض
        $data['action'] = $this->url->link('common/two_factor_verify', '', true);
        $data['cancel'] = $this->url->link('common/login', '', true);

        // رسائل الخطأ
        if (isset($this->error['warning'])) {
            $data['error_warning'] = $this->error['warning'];
        } else {
            $data['error_warning'] = '';
        }

        // رسائل النجاح
        if (isset($this->session->data['success'])) {
            $data['success'] = $this->session->data['success'];
            unset($this->session->data['success']);
        } else {
            $data['success'] = '';
        }

        // معلومات المستخدم
        $data['username'] = $user_info['username'] ?? '';
        $data['phone_verified'] = $user_info['phone_verified'] ?? false;
        $data['email'] = $user_info['email'] ?? '';

        // خيارات التحقق المتاحة
        $data['totp_enabled'] = true; // TOTP متاح دائماً
        $data['sms_enabled'] = $user_info['phone_verified'] && $this->getTwoFactorSetting('2fa_sms_enabled', '1');
        $data['email_enabled'] = !empty($user_info['email']) && $this->getTwoFactorSetting('2fa_email_enabled', '1');

        // متغيرات اللغة
        $data['heading_title'] = $this->language->get('heading_title');
        $data['text_verification'] = $this->language->get('text_verification');
        $data['text_totp'] = $this->language->get('text_totp');
        $data['text_sms'] = $this->language->get('text_sms');
        $data['text_email'] = $this->language->get('text_email');
        $data['text_backup'] = $this->language->get('text_backup');
        $data['entry_code'] = $this->language->get('entry_code');
        $data['entry_backup_code'] = $this->language->get('entry_backup_code');
        $data['button_verify'] = $this->language->get('button_verify');
        $data['button_send_sms'] = $this->language->get('button_send_sms');
        $data['button_send_email'] = $this->language->get('button_send_email');
        $data['button_cancel'] = $this->language->get('button_cancel');
        $data['text_trust_device'] = $this->language->get('text_trust_device');

        $data['header'] = $this->load->controller('common/header');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('common/two_factor_verify', $data));
    }

    /**
     * إرسال رمز SMS
     */
    public function sendSMS() {
        $this->load->language('common/two_factor_verify');
        $this->load->model('user/two_factor_auth');

        $json = array();

        if (!isset($this->session->data['pending_2fa_user_id'])) {
            $json['error'] = $this->language->get('error_session');
        } else {
            $user_id = $this->session->data['pending_2fa_user_id'];
            
            // التحقق من الحد الأقصى للمحاولات
            if ($this->checkRateLimit($user_id, 'sms')) {
                if ($this->model_user_two_factor_auth->sendSMSCode($user_id)) {
                    $json['success'] = $this->language->get('success_sms_sent');
                } else {
                    $json['error'] = $this->language->get('error_sms_failed');
                }
            } else {
                $json['error'] = $this->language->get('error_rate_limit');
            }
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * إرسال رمز البريد الإلكتروني
     */
    public function sendEmail() {
        $this->load->language('common/two_factor_verify');
        $this->load->model('user/two_factor_auth');

        $json = array();

        if (!isset($this->session->data['pending_2fa_user_id'])) {
            $json['error'] = $this->language->get('error_session');
        } else {
            $user_id = $this->session->data['pending_2fa_user_id'];
            
            // التحقق من الحد الأقصى للمحاولات
            if ($this->checkRateLimit($user_id, 'email')) {
                if ($this->model_user_two_factor_auth->sendEmailCode($user_id)) {
                    $json['success'] = $this->language->get('success_email_sent');
                } else {
                    $json['error'] = $this->language->get('error_email_failed');
                }
            } else {
                $json['error'] = $this->language->get('error_rate_limit');
            }
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * التحقق من صحة المصادقة الثنائية
     */
    protected function validateTwoFactor() {
        $user_id = $this->session->data['pending_2fa_user_id'];
        
        if (!isset($this->request->post['verification_method']) || !$this->request->post['verification_method']) {
            $this->error['warning'] = $this->language->get('error_method_required');
            return false;
        }

        $method = $this->request->post['verification_method'];
        $is_valid = false;

        switch ($method) {
            case 'totp':
                if (!isset($this->request->post['totp_code']) || !$this->request->post['totp_code']) {
                    $this->error['warning'] = $this->language->get('error_totp_required');
                } else {
                    $is_valid = $this->model_user_two_factor_auth->verifyTOTP($user_id, $this->request->post['totp_code']);
                    if (!$is_valid) {
                        $this->error['warning'] = $this->language->get('error_totp_invalid');
                    }
                }
                break;

            case 'sms':
                if (!isset($this->request->post['sms_code']) || !$this->request->post['sms_code']) {
                    $this->error['warning'] = $this->language->get('error_sms_required');
                } else {
                    $is_valid = $this->model_user_two_factor_auth->verifyCode($user_id, $this->request->post['sms_code'], 'sms');
                    if (!$is_valid) {
                        $this->error['warning'] = $this->language->get('error_sms_invalid');
                    }
                }
                break;

            case 'email':
                if (!isset($this->request->post['email_code']) || !$this->request->post['email_code']) {
                    $this->error['warning'] = $this->language->get('error_email_required');
                } else {
                    $is_valid = $this->model_user_two_factor_auth->verifyCode($user_id, $this->request->post['email_code'], 'email');
                    if (!$is_valid) {
                        $this->error['warning'] = $this->language->get('error_email_invalid');
                    }
                }
                break;

            case 'backup':
                if (!isset($this->request->post['backup_code']) || !$this->request->post['backup_code']) {
                    $this->error['warning'] = $this->language->get('error_backup_required');
                } else {
                    $is_valid = $this->model_user_two_factor_auth->verifyBackupCode($user_id, $this->request->post['backup_code']);
                    if (!$is_valid) {
                        $this->error['warning'] = $this->language->get('error_backup_invalid');
                    }
                }
                break;

            default:
                $this->error['warning'] = $this->language->get('error_method_invalid');
        }

        return $is_valid;
    }

    /**
     * إكمال تسجيل الدخول بعد المصادقة الثنائية
     */
    private function completeLoginAfter2FA($user_id) {
        // تسجيل المستخدم
        $this->load->model('user/user');
        $user_info = $this->model_user_user->getUser($user_id);
        
        if ($user_info) {
            $this->user->login($user_info['username'], '', true); // تسجيل دخول مباشر
            $this->session->data['user_token'] = token(32);

            // إضافة الجهاز كموثوق إذا طلب المستخدم ذلك
            if (isset($this->request->post['trust_device']) && $this->request->post['trust_device']) {
                $device_fingerprint = $this->generateDeviceFingerprint();
                $device_name = $this->getDeviceName();
                $this->model_user_two_factor_auth->addTrustedDevice($user_id, $device_fingerprint, $device_name);
            }

            // تسجيل نشاط تسجيل الدخول
            $this->load->model('activity_log');
            $this->model_activity_log->addActivity('user_login_2fa', 'تسجيل دخول ناجح مع المصادقة الثنائية', $user_id);

            // حذف بيانات المصادقة الثنائية من الجلسة
            unset($this->session->data['pending_2fa_user_id']);
            
            $redirect = $this->session->data['pending_2fa_redirect'] ?? '';
            unset($this->session->data['pending_2fa_redirect']);

            // إعادة التوجيه
            if ($redirect && (strpos($redirect, HTTP_SERVER) === 0 || strpos($redirect, HTTPS_SERVER) === 0)) {
                $this->response->redirect($redirect . '&user_token=' . $this->session->data['user_token']);
            } else {
                $this->response->redirect($this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true));
            }
        }
    }

    /**
     * التحقق من حد المعدل
     */
    private function checkRateLimit($user_id, $method) {
        $max_attempts = (int)$this->getTwoFactorSetting('2fa_max_attempts_per_hour', 5);
        
        $sql = "SELECT COUNT(*) as attempts FROM " . DB_PREFIX . "user_2fa_attempts 
                WHERE user_id = '" . (int)$user_id . "' 
                AND attempt_type = '" . $this->db->escape($method) . "' 
                AND attempt_time > DATE_SUB(NOW(), INTERVAL 1 HOUR)";
        
        $query = $this->db->query($sql);
        
        return $query->row['attempts'] < $max_attempts;
    }

    /**
     * توليد بصمة الجهاز
     */
    private function generateDeviceFingerprint() {
        $components = [
            $this->request->server['HTTP_USER_AGENT'] ?? '',
            $this->request->server['HTTP_ACCEPT_LANGUAGE'] ?? '',
            $this->request->server['HTTP_ACCEPT_ENCODING'] ?? '',
            $this->request->server['REMOTE_ADDR'] ?? ''
        ];
        
        return hash('sha256', implode('|', $components));
    }

    /**
     * الحصول على اسم الجهاز
     */
    private function getDeviceName() {
        $user_agent = $this->request->server['HTTP_USER_AGENT'] ?? '';
        
        // تحليل بسيط لنوع الجهاز
        if (strpos($user_agent, 'Mobile') !== false) {
            return 'Mobile Device';
        } elseif (strpos($user_agent, 'Tablet') !== false) {
            return 'Tablet';
        } else {
            return 'Desktop Computer';
        }
    }

    /**
     * الحصول على إعداد المصادقة الثنائية
     */
    private function getTwoFactorSetting($key, $default = '') {
        $sql = "SELECT setting_value FROM " . DB_PREFIX . "2fa_settings 
                WHERE setting_key = '" . $this->db->escape($key) . "' 
                AND is_active = 1";
        
        $query = $this->db->query($sql);
        
        return $query->num_rows ? $query->row['setting_value'] : $default;
    }
}
?>
