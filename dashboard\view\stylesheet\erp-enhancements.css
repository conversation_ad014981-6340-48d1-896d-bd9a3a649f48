/**
 * ERP Enhancements CSS
 * تحسينات CSS لنظام ERP+eCommerce
 * إصدار: 1.0.0
 */

/* ========================================================
   1. تنسيقات عامة وواجهة المستخدم
   ======================================================== */

/* تحسينات عامة للقالب */
body {
    background-color: #f5f5f5;
    color: #333;
}

html[dir="rtl"] body {
    font-family: 'Cairo', sans-serif;
}

html[dir="ltr"] body {
    font-family: 'Roboto', 'Helvetica Neue', Arial, sans-serif;
}

.container-fluid {
    padding-right: 20px;
    padding-left: 20px;
}

/* رسالة التحميل */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
}

.loading-spinner {
    margin-bottom: 10px;
}

/* تحسينات للبطاقات */
.panel, .card {
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: none;
    margin-bottom: 20px;
}

.panel-heading, .card-header {
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #eee;
    padding: 12px 15px;
}

.panel-title, .card-title {
    font-size: 16px;
    font-weight: 600;
    margin: 0;
}

.panel-body, .card-body {
    padding: 15px;
}

.panel-footer, .card-footer {
    background-color: #f8f9fa;
    border-top: 1px solid #eee;
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
    padding: 10px 15px;
}

/* تحسينات للازرار */
.btn {
    border-radius: 3px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: #3c8dbc;
    border-color: #367fa9;
}

.btn-primary:hover,
.btn-primary:focus,
.btn-primary:active {
    background-color: #367fa9;
    border-color: #2d6a8e;
}

.btn-success {
    background-color: #00a65a;
    border-color: #008d4c;
}

.btn-success:hover,
.btn-success:focus,
.btn-success:active {
    background-color: #008d4c;
    border-color: #007541;
}

.btn-warning {
    background-color: #f39c12;
    border-color: #e08e0b;
}

.btn-warning:hover,
.btn-warning:focus,
.btn-warning:active {
    background-color: #e08e0b;
    border-color: #c87f0a;
}

.btn-danger {
    background-color: #dd4b39;
    border-color: #d73925;
}

.btn-danger:hover,
.btn-danger:focus,
.btn-danger:active {
    background-color: #d73925;
    border-color: #c23321;
}

.btn-default {
    background-color: #f4f4f4;
    border-color: #ddd;
    color: #444;
}

.btn-default:hover,
.btn-default:focus,
.btn-default:active {
    background-color: #e7e7e7;
    border-color: #ccc;
}

.btn-xs {
    padding: 1px 5px;
    font-size: 12px;
}

.btn-sm {
    padding: 5px 10px;
    font-size: 12px;
}

.btn-lg {
    padding: 10px 16px;
    font-size: 18px;
}

.btn-icon {
    width: 34px;
    height: 34px;
    padding: 0;
    line-height: 34px;
    text-align: center;
}

.btn-icon i {
    font-size: 16px;
}

/* تحسينات لصناديق الإدخال */
.form-control {
    border-radius: 3px;
    box-shadow: none;
    border-color: #d2d6de;
}

.form-control:focus {
    border-color: #3c8dbc;
    box-shadow: none;
}

.form-group label {
    font-weight: 600;
}

.form-group.has-error .form-control {
    border-color: #dd4b39;
    box-shadow: none;
}

.form-group.has-success .form-control {
    border-color: #00a65a;
    box-shadow: none;
}

.input-group-addon {
    border-radius: 3px;
    background-color: #f4f4f4;
    border-color: #d2d6de;
    color: #555;
}

/* ========================================================
   2. لوحة التحكم وإحصائيات النظام
   ======================================================== */

/* بطاقات المؤشرات */
.info-box {
    display: block;
    min-height: 90px;
    background: #fff;
    width: 100%;
    border-radius: 2px;
    margin-bottom: 15px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.info-box-icon {
    border-top-left-radius: 2px;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 2px;
    display: block;
    float: left;
    height: 90px;
    width: 90px;
    text-align: center;
    font-size: 45px;
    line-height: 90px;
    background: rgba(0, 0, 0, 0.2);
}

html[dir="rtl"] .info-box-icon {
    float: right;
    border-top-right-radius: 2px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 2px;
}

.info-box-content {
    padding: 5px 10px;
    margin-left: 90px;
}

html[dir="rtl"] .info-box-content {
    margin-left: 0;
    margin-right: 90px;
}

.info-box-text {
    text-transform: uppercase;
    font-size: 12px;
    font-weight: 600;
}

.info-box-number {
    display: block;
    font-weight: bold;
    font-size: 18px;
    margin-top: 5px;
}

.info-box .progress {
    margin: 5px -10px 5px -10px;
    height: 5px;
    background: rgba(0, 0, 0, 0.125);
}

.info-box .progress-bar {
    background-color: #fff;
}

/* الألوان للمؤشرات */
.bg-aqua {
    background-color: #00c0ef !important;
    color: #fff !important;
}

.bg-green {
    background-color: #00a65a !important;
    color: #fff !important;
}

.bg-yellow {
    background-color: #f39c12 !important;
    color: #fff !important;
}

.bg-red {
    background-color: #dd4b39 !important;
    color: #fff !important;
}

.bg-blue {
    background-color: #0073b7 !important;
    color: #fff !important;
}

.bg-navy {
    background-color: #001f3f !important;
    color: #fff !important;
}

.bg-purple {
    background-color: #605ca8 !important;
    color: #fff !important;
}

.bg-maroon {
    background-color: #d81b60 !important;
    color: #fff !important;
}

/* مؤشرات صغيرة */
.small-box {
    border-radius: 2px;
    position: relative;
    display: block;
    margin-bottom: 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.small-box > .inner {
    padding: 10px;
}

.small-box h3 {
    font-size: 38px;
    font-weight: bold;
    margin: 0 0 10px 0;
    white-space: nowrap;
    padding: 0;
}

.small-box p {
    font-size: 15px;
    margin-bottom: 0;
}

.small-box .icon {
    position: absolute;
    top: 5px;
    right: 10px;
    font-size: 70px;
    color: rgba(0, 0, 0, 0.15);
}

html[dir="rtl"] .small-box .icon {
    right: auto;
    left: 10px;
}

.small-box .small-box-footer {
    position: relative;
    text-align: center;
    padding: 3px 0;
    color: #fff;
    color: rgba(255, 255, 255, 0.8);
    display: block;
    z-index: 10;
    background: rgba(0, 0, 0, 0.1);
    text-decoration: none;
}

.small-box .small-box-footer:hover {
    color: #fff;
    background: rgba(0, 0, 0, 0.15);
}

/* مؤشرات الاتجاهات */
.trend-arrow {
    margin-right: 5px;
}

html[dir="rtl"] .trend-arrow {
    margin-right: 0;
    margin-left: 5px;
}

.trend-up {
    color: #00a65a;
}

.trend-down {
    color: #dd4b39;
}

.trend-neutral {
    color: #999;
}

/* ========================================================
   3. تخصيصات نظام ERP (تكلفة المخزون، الجرد المستمر)
   ======================================================== */

/* جدول معلومات المخزون */
.inventory-info-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 15px;
}

.inventory-info-table th {
    background-color: #f4f4f4;
    padding: 8px;
    font-weight: 600;
    text-align: start;
    border: 1px solid #ddd;
}

.inventory-info-table td {
    padding: 8px;
    border: 1px solid #ddd;
}

/* مؤشرات حالة المخزون */
.stock-status {
    display: inline-block;
    padding: 2px 5px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: bold;
    text-align: center;
    min-width: 60px;
}

.stock-status-in-stock {
    background-color: #dff0d8;
    color: #3c763d;
}

.stock-status-low-stock {
    background-color: #fcf8e3;
    color: #8a6d3b;
}

.stock-status-out-of-stock {
    background-color: #f2dede;
    color: #a94442;
}

.stock-status-over-stock {
    background-color: #d9edf7;
    color: #31708f;
}

/* مؤشرات تكلفة المخزون */
.cost-badge {
    display: inline-block;
    padding: 2px 5px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: bold;
}

.cost-increasing {
    background-color: #f2dede;
    color: #a94442;
}

.cost-decreasing {
    background-color: #dff0d8;
    color: #3c763d;
}

.cost-stable {
    background-color: #d9edf7;
    color: #31708f;
}

/* بطاقة معلومات المنتج */
.product-info-card {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.product-info-header {
    padding: 10px 15px;
    border-bottom: 1px solid #f4f4f4;
    background-color: #f7f7f7;
    border-top-right-radius: 4px;
    border-top-left-radius: 4px;
}

.product-info-body {
    padding: 15px;
}

.product-info-item {
    margin-bottom: 8px;
    padding-bottom: 8px;
    border-bottom: 1px solid #f4f4f4;
}

.product-info-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.product-info-label {
    display: inline-block;
    width: 40%;
    font-weight: 600;
    color: #555;
}

.product-info-value {
    display: inline-block;
    width: 60%;
}

/* تصميم الحركات التاريخية */
.history-timeline {
    position: relative;
    margin: 20px 0;
    padding: 0;
    list-style: none;
}

.history-timeline:before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    width: 3px;
    background: #e4e8f0;
    left: 31px;
    margin: 0;
    border-radius: 2px;
}

html[dir="rtl"] .history-timeline:before {
    left: auto;
    right: 31px;
}

.history-item {
    position: relative;
    margin-bottom: 15px;
}

.history-item:last-child {
    margin-bottom: 0;
}

.history-badge {
    position: absolute;
    top: 0;
    left: 15px;
    width: 35px;
    height: 35px;
    border-radius: 50%;
    text-align: center;
    line-height: 35px;
    background: #f4f4f4;
    color: #555;
}

html[dir="rtl"] .history-badge {
    left: auto;
    right: 15px;
}

.history-badge i {
    font-size: 16px;
}

.history-body {
    margin-left: 65px;
    background: #fff;
    border-radius: 4px;
    padding: 10px;
    position: relative;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

html[dir="rtl"] .history-body {
    margin-left: 0;
    margin-right: 65px;
}

.history-body:before {
    content: '';
    position: absolute;
    left: -10px;
    top: 12px;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 10px 10px 10px 0;
    border-color: transparent #fff transparent transparent;
}

html[dir="rtl"] .history-body:before {
    left: auto;
    right: -10px;
    border-width: 10px 0 10px 10px;
    border-color: transparent transparent transparent #fff;
}

.history-title {
    margin-top: 0;
    color: #555;
    font-size: 14px;
    font-weight: 600;
}

.history-date {
    color: #999;
    font-size: 12px;
    margin-bottom: 5px;
}

.history-description {
    margin-bottom: 0;
}

/* ========================================================
   4. تنسيقات قوائم وجداول البيانات
   ======================================================== */

/* تحسينات DataTables */
div.dataTables_wrapper div.dataTables_filter {
    text-align: end;
}

div.dataTables_wrapper div.dataTables_info {
    padding-top: 10px;
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
    padding: 0.2em 0.8em;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current,
.dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
    background: #3c8dbc;
    color: #fff !important;
    border-color: #3c8dbc;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    background: #f4f4f4;
    color: #333 !important;
    border-color: #ddd;
}

.dataTables_wrapper .dataTables_length {
    margin-bottom: 10px;
}

.dataTables_wrapper .dataTables_length select {
    border: 1px solid #d2d6de;
    border-radius: 3px;
    padding: 5px;
    background-color: #fff;
}

.dataTables_wrapper .dt-buttons {
    margin-bottom: 10px;
}

.dataTables_wrapper .dt-buttons .btn {
    margin-right: 5px;
    margin-bottom: 5px;
}

html[dir="rtl"] .dataTables_wrapper .dt-buttons .btn {
    margin-right: 0;
    margin-left: 5px;
}

/* تنسيقات جداول البيانات العامة */
.table-responsive {
    border: none;
    margin-bottom: 0;
}

.table-striped > tbody > tr:nth-of-type(odd) {
    background-color: #f9f9f9;
}

.table-hover > tbody > tr:hover {
    background-color: #f5f5f5;
}

.table-bordered {
    border: 1px solid #ddd;
}

.table-bordered > thead > tr > th,
.table-bordered > tbody > tr > th,
.table-bordered > tfoot > tr > th,
.table-bordered > thead > tr > td,
.table-bordered > tbody > tr > td,
.table-bordered > tfoot > tr > td {
    border: 1px solid #ddd;
}

/* تحسينات Select2 */
.select2-container--bootstrap .select2-selection {
    border-radius: 3px;
    box-shadow: none;
    border-color: #d2d6de;
}

.select2-container--bootstrap .select2-selection--single {
    height: 34px;
    padding: 6px 12px;
}

.select2-container--bootstrap .select2-selection--multiple {
    min-height: 34px;
}

.select2-container--bootstrap .select2-results__option--highlighted[aria-selected] {
    background-color: #3c8dbc;
}

.select2-container--bootstrap.select2-container--focus .select2-selection,
.select2-container--bootstrap.select2-container--open .select2-selection {
    border-color: #3c8dbc;
    box-shadow: none;
}

.select2-container--bootstrap .select2-selection--multiple .select2-selection__choice {
    background-color: #3c8dbc;
    border-color: #367fa9;
    color: #fff;
}

.select2-container--bootstrap .select2-selection--multiple .select2-selection__choice__remove {
    color: #fff;
    margin-right: 5px;
}

html[dir="rtl"] .select2-container--bootstrap .select2-selection--multiple .select2-selection__choice__remove {
    margin-right: 0;
    margin-left: 5px;
}

/* ========================================================
   5. تنسيقات خاصة بـ RTL للعربية
   ======================================================== */

/* تنسيقات خاصة بالعربية */
html[dir="rtl"] .text-left {
    text-align: right;
}

html[dir="rtl"] .text-right {
    text-align: left;
}

html[dir="rtl"] .pull-left {
    float: right !important;
}

html[dir="rtl"] .pull-right {
    float: left !important;
}

html[dir="rtl"] .list-unstyled {
    padding-right: 0;
}

html[dir="rtl"] .input-group .form-control:first-child,
html[dir="rtl"] .input-group-addon:first-child,
html[dir="rtl"] .input-group-btn:first-child > .btn,
html[dir="rtl"] .input-group-btn:first-child > .btn-group > .btn,
html[dir="rtl"] .input-group-btn:first-child > .dropdown-toggle,
html[dir="rtl"] .input-group-btn:last-child > .btn:not(:last-child):not(.dropdown-toggle),
html[dir="rtl"] .input-group-btn:last-child > .btn-group:not(:last-child) > .btn {
    border-top-right-radius: 3px;
    border-bottom-right-radius: 3px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

html[dir="rtl"] .input-group .form-control:last-child,
html[dir="rtl"] .input-group-addon:last-child,
html[dir="rtl"] .input-group-btn:last-child > .btn,
html[dir="rtl"] .input-group-btn:last-child > .btn-group > .btn,
html[dir="rtl"] .input-group-btn:last-child > .dropdown-toggle,
html[dir="rtl"] .input-group-btn:first-child > .btn:not(:first-child),
html[dir="rtl"] .input-group-btn:first-child > .btn-group:not(:first-child) > .btn {
    border-top-left-radius: 3px;
    border-bottom-left-radius: 3px;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

/* تصحيح مؤشر الفرز في DataTables للعربية */
html[dir="rtl"] table.dataTable thead .sorting:after,
html[dir="rtl"] table.dataTable thead .sorting_asc:after,
html[dir="rtl"] table.dataTable thead .sorting_desc:after {
    right: auto;
    left: 8px;
}

/* ========================================================
   6. تخصيصات للشاشات المختلفة
   ======================================================== */

/* تنسيقات شاشة المبيعات */
.sales-summary {
    background-color: #fff;
    padding: 15px;
    border-radius: 3px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.sales-total {
    font-size: 24px;
    font-weight: bold;
    color: #3c8dbc;
}

.order-status {
    display: inline-block;
    padding: 3px 6px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: bold;
    text-align: center;
    min-width: 80px;
}

.order-pending {
    background-color: #fcf8e3;
    color: #8a6d3b;
}

.order-processing {
    background-color: #d9edf7;
    color: #31708f;
}

.order-shipped {
    background-color: #dff0d8;
    color: #3c763d;
}

.order-complete {
    background-color: #dff0d8;
    color: #3c763d;
}

.order-cancelled {
    background-color: #f2dede;
    color: #a94442;
}

/* تنسيقات شاشة المشتريات */
.purchase-process-steps {
    display: flex;
    margin-bottom: 20px;
    background-color: #fff;
    border-radius: 3px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.purchase-step {
    flex: 1;
    padding: 10px;
    text-align: center;
    background-color: #f4f4f4;
    border-right: 1px solid #ddd;
    position: relative;
    cursor: pointer;
}

html[dir="rtl"] .purchase-step {
    border-right: none;
    border-left: 1px solid #ddd;
}

.purchase-step:last-child {
    border-right: none;
}

html[dir="rtl"] .purchase-step:last-child {
    border-left: none;
}

.purchase-step.active {
    background-color: #3c8dbc;
    color: #fff;
}

.purchase-step.completed {
    background-color: #00a65a;
    color: #fff;
}

.purchase-step:after {
    content: '';
    position: absolute;
    top: 50%;
    right: -10px;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 10px 0 10px 10px;
    border-color: transparent transparent transparent #f4f4f4;
    transform: translateY(-50%);
    z-index: 1;
}

html[dir="rtl"] .purchase-step:after {
    right: auto;
    left: -10px;
    border-width: 10px 10px 10px 0;
    border-color: transparent #f4f4f4 transparent transparent;
}

.purchase-step.active:after {
    border-color: transparent transparent transparent #3c8dbc;
}

html[dir="rtl"] .purchase-step.active:after {
    border-color: transparent #3c8dbc transparent transparent;
}

.purchase-step.completed:after {
    border-color: transparent transparent transparent #00a65a;
}

html[dir="rtl"] .purchase-step.completed:after {
    border-color: transparent #00a65a transparent transparent;
}

.purchase-step:last-child:after {
    display: none;
}

.purchase-step-number {
    display: inline-block;
    width: 24px;
    height: 24px;
    line-height: 24px;
    text-align: center;
    background-color: #fff;
    color: #555;
    border-radius: 50%;
    margin-right: 5px;
}

html[dir="rtl"] .purchase-step-number {
    margin-right: 0;
    margin-left: 5px;
}

.purchase-step.active .purchase-step-number {
    background-color: #fff;
    color: #3c8dbc;
}

.purchase-step.completed .purchase-step-number {
    background-color: #fff;
    color: #00a65a;
}

/* تنسيقات شاشة الحسابات */
.account-balance {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 10px;
}

.account-balance.positive {
    color: #00a65a;
}

.account-balance.negative {
    color: #dd4b39;
}

.transaction-type {
    display: inline-block;
    padding: 2px 5px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: bold;
    text-align: center;
    min-width: 80px;
}

.transaction-debit {
    background-color: #f2dede;
    color: #a94442;
}

.transaction-credit {
    background-color: #dff0d8;
    color: #3c763d;
}

/* ========================================================
   7. الإعلامات والتنبيهات
   ======================================================== */

/* زر العودة لأعلى الصفحة */
.back-to-top {
    position: fixed;
    bottom: 20px;
    right: 20px;
    display: none;
    z-index: 999;
    width: 40px;
    height: 40px;
    text-align: center;
    line-height: 40px;
    padding: 0;
}

html[dir="rtl"] .back-to-top {
    right: auto;
    left: 20px;
}

/* تنبيهات النظام */
.system-alert {
    position: fixed;
    top: 70px;
    right: 20px;
    max-width: 350px;
    z-index: 9999;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    border-radius: 3px;
    overflow: hidden;
}

html[dir="rtl"] .system-alert {
    right: auto;
    left: 20px;
}

.system-alert .alert {
    margin-bottom: 10px;
    position: relative;
    padding-right: 30px;
}

html[dir="rtl"] .system-alert .alert {
    padding-right: 15px;
    padding-left: 30px;
}

.system-alert .close {
    position: absolute;
    top: 10px;
    right: 10px;
}

html[dir="rtl"] .system-alert .close {
    right: auto;
    left: 10px;
}

.alert-dismissible .close {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: 10px;
}

html[dir="rtl"] .alert-dismissible .close {
    right: auto;
    left: 10px;
}

/* تنبيهات toastr محسنة */
#toast-container > div {
    opacity: 0.9;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    border-radius: 3px;
}

#toast-container > .toast-success {
    background-color: #00a65a;
}

#toast-container > .toast-error {
    background-color: #dd4b39;
}

#toast-container > .toast-info {
    background-color: #00c0ef;
}

#toast-container > .toast-warning {
    background-color: #f39c12;
}

/* مؤشر التحميل الشامل */
.global-spinner {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.7);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
}

.spinner-wrapper {
    text-align: center;
}

.spinner-text {
    margin-top: 10px;
    font-weight: 600;
    color: #555;
}

/* ========================================================
   8. الرسوم البيانية والإحصائيات
   ======================================================== */

/* حاويات الرسوم البيانية */
.chart-container {
    position: relative;
    margin: 0 auto 20px auto;
    height: 300px;
}

.chart-container.small {
    height: 200px;
}

.chart-container.large {
    height: 400px;
}

/* بطاقات للرسوم البيانية */
.chart-card {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    padding: 15px;
}

.chart-card-header {
    border-bottom: 1px solid #f4f4f4;
    margin-bottom: 15px;
    padding-bottom: 10px;
}

.chart-card-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.chart-card-subtitle {
    color: #777;
    font-size: 12px;
    margin-top: 5px;
}

.chart-card-body {
    position: relative;
    min-height: 200px;
}

/* أرقام المؤشرات الإحصائية */
.stat-number {
    font-size: 36px;
    font-weight: bold;
    margin-bottom: 5px;
    line-height: 1.2;
}

.stat-label {
    font-size: 14px;
    color: #777;
    margin-bottom: 10px;
}

.stat-trend {
    font-size: 12px;
    margin-top: 5px;
}

.stat-trend.up {
    color: #00a65a;
}

.stat-trend.down {
    color: #dd4b39;
}

.stat-trend.neutral {
    color: #777;
}

/* تحسينات لـ Chart.js */
canvas.chart-render {
    width: 100% !important;
    height: 100% !important;
}

/* مجموعة مؤشرات KPI */
.kpi-container {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -10px 20px -10px;
}

.kpi-item {
    flex: 1;
    margin: 0 10px 20px 10px;
    min-width: calc(25% - 20px);
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 15px;
    text-align: center;
}

@media (max-width: 992px) {
    .kpi-item {
        min-width: calc(50% - 20px);
    }
}

@media (max-width: 576px) {
    .kpi-item {
        min-width: calc(100% - 20px);
    }
}

.kpi-icon {
    font-size: 28px;
    margin-bottom: 10px;
    color: #3c8dbc;
}

.kpi-number {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 5px;
}

.kpi-label {
    font-size: 12px;
    color: #777;
    margin-bottom: 10px;
}

.kpi-trend {
    font-size: 12px;
}

.kpi-trend.up {
    color: #00a65a;
}

.kpi-trend.down {
    color: #dd4b39;
}

.kpi-trend.neutral {
    color: #777;
}

/* ========================================================
   9. تنسيقات لوحدة الجرد المستمر والتكلفة المتوسطة
   ======================================================== */

/* بطاقة معلومات التكلفة */
.cost-info-card {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.cost-info-header {
    padding: 10px 15px;
    border-bottom: 1px solid #f4f4f4;
    background-color: #f8f9fa;
}

.cost-info-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.cost-info-body {
    padding: 15px;
}

.cost-info-value {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 5px;
}

.cost-info-label {
    font-size: 12px;
    color: #777;
}

.cost-info-footer {
    padding: 10px 15px;
    border-top: 1px solid #f4f4f4;
    background-color: #f8f9fa;
    font-size: 12px;
    color: #777;
}

/* جدول تاريخ التكلفة */
.cost-history-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

.cost-history-table th {
    background-color: #f4f4f4;
    padding: 8px;
    font-weight: 600;
    text-align: start;
    border: 1px solid #ddd;
}

.cost-history-table td {
    padding: 8px;
    border: 1px solid #ddd;
}

.cost-history-table .cost-increase {
    color: #dd4b39;
    font-weight: bold;
}

.cost-history-table .cost-decrease {
    color: #00a65a;
    font-weight: bold;
}

/* مؤشرات حالة المخزون وتقادم المنتجات */
.stock-aging {
    display: inline-block;
    padding: 2px 5px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: bold;
}

.stock-aging-fresh {
    background-color: #dff0d8;
    color: #3c763d;
}

.stock-aging-medium {
    background-color: #fcf8e3;
    color: #8a6d3b;
}

.stock-aging-old {
    background-color: #f2dede;
    color: #a94442;
}

.stock-expiry {
    display: inline-block;
    padding: 2px 5px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: bold;
}

.stock-expiry-safe {
    background-color: #dff0d8;
    color: #3c763d;
}

.stock-expiry-warning {
    background-color: #fcf8e3;
    color: #8a6d3b;
}

.stock-expiry-critical {
    background-color: #f2dede;
    color: #a94442;
}

.stock-expiry-expired {
    background-color: #333;
    color: #fff;
}

/* تنسيقات للحركة بين المخازن */
.transfer-direction {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 10px 0;
}

.transfer-location {
    flex: 1;
    text-align: center;
    padding: 10px;
    background-color: #f4f4f4;
    border-radius: 3px;
}

.transfer-arrow {
    margin: 0 15px;
    font-size: 20px;
    color: #3c8dbc;
}

/* ========================================================
   10. تنسيقات لوحدة المحاسبة والمالية
   ======================================================== */

/* بطاقة الحساب المالي */
.account-card {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    overflow: hidden;
}

.account-card-header {
    padding: 15px;
    background-color: #3c8dbc;
    color: #fff;
}

.account-card-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.account-card-subtitle {
    font-size: 12px;
    opacity: 0.8;
    margin-top: 5px;
}

.account-card-body {
    padding: 15px;
}

.account-balance-large {
    font-size: 32px;
    font-weight: bold;
    margin-bottom: 10px;
}

.account-balance-large.positive {
    color: #00a65a;
}

.account-balance-large.negative {
    color: #dd4b39;
}

.account-info-item {
    margin-bottom: 8px;
    padding-bottom: 8px;
    border-bottom: 1px solid #f4f4f4;
}

.account-info-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.account-info-label {
    display: inline-block;
    width: 40%;
    font-weight: 600;
    color: #555;
}

.account-info-value {
    display: inline-block;
    width: 60%;
}

/* جدول حركات الحساب */
.account-transaction-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

.account-transaction-table th {
    background-color: #f4f4f4;
    padding: 8px;
    font-weight: 600;
    text-align: start;
    border: 1px solid #ddd;
}

.account-transaction-table td {
    padding: 8px;
    border: 1px solid #ddd;
}

.transaction-amount {
    font-weight: bold;
    text-align: end;
}

.transaction-amount.positive {
    color: #00a65a;
}

.transaction-amount.negative {
    color: #dd4b39;
}

/* تنسيقات القيود المحاسبية */
.journal-entry {
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-bottom: 20px;
    padding: 15px;
}

.journal-entry-header {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #f4f4f4;
}

.journal-entry-title {
    font-size: 16px;
    font-weight: 600;
    margin-top: 0;
    margin-bottom: 5px;
}

.journal-entry-date {
    font-size: 12px;
    color: #777;
}

.journal-entry-table {
    width: 100%;
    border-collapse: collapse;
}

.journal-entry-table th {
    background-color: #f4f4f4;
    padding: 8px;
    font-weight: 600;
    text-align: start;
    border: 1px solid #ddd;
}

.journal-entry-table td {
    padding: 8px;
    border: 1px solid #ddd;
}

.debit-amount {
    color: #dd4b39;
    text-align: end;
    font-weight: bold;
}

.credit-amount {
    color: #00a65a;
    text-align: end;
    font-weight: bold;
}

.journal-entry-footer {
    margin-top: 15px;
    padding-top: 10px;
    border-top: 1px solid #f4f4f4;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.journal-totals {
    font-weight: bold;
}

.journal-status {
    display: inline-block;
    padding: 3px 6px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: bold;
}

.journal-status-draft {
    background-color: #fcf8e3;
    color: #8a6d3b;
}

.journal-status-posted {
    background-color: #dff0d8;
    color: #3c763d;
}

.journal-status-void {
    background-color: #f2dede;
    color: #a94442;
}