<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/iam/admin/v1/iam.proto

namespace Google\Iam\Admin\V1;

use UnexpectedValueException;

/**
 * A view for Role objects.
 *
 * Protobuf type <code>google.iam.admin.v1.RoleView</code>
 */
class RoleView
{
    /**
     * Omits the `included_permissions` field.
     * This is the default value.
     *
     * Generated from protobuf enum <code>BASIC = 0;</code>
     */
    const BASIC = 0;
    /**
     * Returns all fields.
     *
     * Generated from protobuf enum <code>FULL = 1;</code>
     */
    const FULL = 1;

    private static $valueToName = [
        self::BASIC => 'BASIC',
        self::FULL => 'FULL',
    ];

    public static function name($value)
    {
        if (!isset(self::$valueToName[$value])) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no name defined for value %s', __CLASS__, $value));
        }
        return self::$valueToName[$value];
    }


    public static function value($name)
    {
        $const = __CLASS__ . '::' . strtoupper($name);
        if (!defined($const)) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no value defined for name %s', __CLASS__, $name));
        }
        return constant($const);
    }
}

