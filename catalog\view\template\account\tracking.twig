{{ header }}
<div id="account-tracking" class="container-fluid">
  <div class="row">{{ column_left }}
    <div id="content" class="col" style="padding-left:10px;padding-right:10px;padding-top:10px">{{ content_top }}
      <p>{{ text_description }}</p>
      <form>
        <div class="row mb-3">
          <label for="input-code" class="col-md-2 col-form-label">{{ entry_code }}</label>
          <div class="col-md-10">
            <textarea cols="40" rows="5" placeholder="{{ entry_code }}" id="input-code" class="form-control">{{ code }}</textarea>
          </div>
        </div>
        <div class="row mb-3">
          <label for="input-generator" class="col-md-2 col-form-label">{{ entry_generator }}</label>
          <div class="col-md-10">
            <input type="text" name="product" value="" placeholder="{{ entry_generator }}" id="input-generator" data-oc-target="autocomplete-generator" class="form-control" autocomplete="off"/>
            <ul id="autocomplete-generator" class="dropdown-menu"></ul>
            <div class="text-muted">{{ help_generator }}</div>
          </div>
        </div>
        <div class="row mb-3">
          <label for="input-link" class="col-md-2 col-form-label">{{ entry_link }}</label>
          <div class="col-md-10">
            <textarea name="link" cols="40" rows="5" placeholder="{{ entry_link }}" id="input-link" class="form-control"></textarea>
          </div>
        </div>
      </form>
      <div class="text-end"><a href="{{ continue }}" class="btn btn-primary">{{ button_continue }}</a></div>
      {{ content_bottom }}</div>
    {{ column_right }}</div>
</div>
<script type="text/javascript"><!--
$('#input-generator').autocomplete({
    'source': function(request, response) {
        return $.ajax({
            url: 'index.php?route=account/tracking.autocomplete&customer_token={{ customer_token }}&filter_name=' + encodeURIComponent(request) + '&tracking=' + encodeURIComponent($('#input-code').val()) + '&language={{ language }}',
            dataType: 'json',
            success: function(json) {
                response($.map(json, function(item) {
                    return {
                        label: item['name'],
                        value: item['link']
                    }
                }));
            }
        });
    },
    'select': function(item) {
        $('#input-link').val(item['value']);
    }
});
//--></script>
{{ footer }}
