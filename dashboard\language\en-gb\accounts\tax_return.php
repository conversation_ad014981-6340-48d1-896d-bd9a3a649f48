<?php
$_['heading_title']        = 'Tax Return Report';
$_['text_form']            = 'Filter Tax Return Report';
$_['entry_date_start']     = 'Start Date';
$_['entry_date_end']       = 'End Date';
$_['button_filter']        = 'Filter';

$_['print_title']          = 'Print Tax Return Report';
$_['text_tax_return']      = 'Tax Return Report';
$_['text_period']          = 'Period';
$_['text_from']            = 'From';
$_['text_to']              = 'To';
$_['text_accounting_profit']= 'Accounting Profit';
$_['text_non_deductible']  = 'Non-Deductible Expenses (Add Back)';
$_['text_exempt_income']   = 'Exempt Income (Subtract)';
$_['text_taxable_profit']  = 'Taxable Profit';
$_['text_tax_rate']        = 'Tax Rate';
$_['text_tax_due']         = 'Tax Due';

$_['error_no_data']        = 'No data for selected period!';

// Additional Text
$_['text_success_generate']            = 'Tax return generated successfully!';
$_['text_no_results']                  = 'No results found';
$_['text_home']                        = 'Home';

// Advanced Features
$_['text_tax_provision']               = 'Tax Provision';
$_['text_deferred_tax']                = 'Deferred Tax';
$_['text_carry_forward_losses']        = 'Carry Forward Losses';
$_['text_book_tax_differences']        = 'Book-Tax Differences';
$_['text_tax_brackets']                = 'Tax Brackets';

// Generate Options
$_['button_generate']                  = 'Generate Return';
$_['button_generate_and_new']          = 'Generate and New';
$_['button_generate_and_export']       = 'Generate and Export';
$_['button_generate_and_eta']          = 'Generate and Submit to ETA';

// ETA Integration
$_['text_eta_submission']              = 'Submit to ETA';
$_['text_eta_status']                  = 'ETA Status';
$_['text_eta_reference']               = 'ETA Reference';
$_['text_success_eta_submit']          = 'Tax return submitted to ETA successfully!';

// Tax Provision
$_['text_calculate_provision']         = 'Calculate Tax Provision';
$_['text_provision_amount']            = 'Provision Amount';
$_['text_provision_journal']           = 'Provision Journal Entry';

// Financial Year
$_['entry_financial_year']             = 'Financial Year';
$_['text_financial_year']              = 'Financial Year';

// Tax Rates
$_['entry_tax_rate']                   = 'Tax Rate';
$_['text_tax_rate_22_5']               = '22.5% - Regular Companies';
$_['text_tax_rate_20']                 = '20% - Small Companies';
$_['text_tax_rate_10']                 = '10% - Startup Companies';
$_['text_tax_rate_0']                  = '0% - Tax Exempt';

// Options
$_['text_include_deferred_tax']        = 'Include Deferred Tax';
$_['text_include_carry_forward']       = 'Include Carry Forward Losses';
$_['text_detailed_calculation']        = 'Detailed Calculation';

// Errors
$_['error_financial_year_required']    = 'Financial year is required!';
$_['error_date_range']                 = 'Start date must be before end date!';
$_['error_no_data_submit']             = 'No data to submit! Please generate return first.';
$_['error_eta_connection']             = 'Error connecting to ETA system!';
$_['error_eta_authentication']         = 'Error authenticating with ETA system!';

// Success Messages
$_['text_success_export']              = 'Tax return exported successfully!';
$_['text_success_provision']           = 'Tax provision calculated successfully!';
$_['text_success_print']               = 'Tax return sent to printer!';

// Additional Fields
$_['text_current_assets']              = 'Current Assets';
$_['text_fixed_assets']                = 'Fixed Assets';
$_['text_total_assets']                = 'Total Assets';
$_['text_current_liabilities']         = 'Current Liabilities';
$_['text_long_term_liabilities']       = 'Long-term Liabilities';
$_['text_total_liabilities']           = 'Total Liabilities';
$_['text_equity']                      = 'Equity';

// Help Text
$_['help_tax_provision']               = 'Tax provision is the estimated amount of tax liability';
$_['help_deferred_tax']                = 'Deferred tax arises from timing differences between book and tax';
$_['help_carry_forward']               = 'Carry forward losses can be deducted from future profits';
$_['help_eta_submission']              = 'Submit tax return directly to ETA system';

// Controller language variables - Direct Arabic texts
$_['log_unauthorized_access'] = 'Unauthorized access attempt to tax return';
$_['log_view_screen'] = 'View tax return screen';
$_['log_unauthorized_export'] = 'Unauthorized export attempt for tax return';
$_['log_export'] = 'Export tax return';
$_['excel_title'] = 'Tax Return';
$_['excel_period'] = 'Period: from';
$_['excel_to'] = 'to';
$_['excel_income'] = 'Income';
$_['excel_expenses'] = 'Expenses';
$_['excel_taxable_income'] = 'Taxable Income';
$_['excel_tax_due'] = 'Tax Due';

// Additional template variables
$_['text_actions'] = 'Actions';
$_['text_tax_return_filters'] = 'Tax Return Filters';
$_['text_select_year'] = 'Select Year';
$_['text_annual_return'] = 'Annual Return';
$_['text_quarterly_return'] = 'Quarterly Return';
$_['text_monthly_return'] = 'Monthly Return';
$_['button_generate_return'] = 'Generate Return';
$_['text_export_options'] = 'Export Options';
$_['text_export'] = 'Export';
$_['text_print'] = 'Print';
$_['button_submit_eta'] = 'Submit to ETA';
$_['text_submit_eta'] = 'Submit to ETA';
$_['button_calculate_provision'] = 'Calculate Provision';
$_['text_total_income'] = 'Total Income';
$_['text_total_income_description'] = 'Total income for the period';
$_['text_total_expenses'] = 'Total Expenses';
$_['text_total_expenses_description'] = 'Total expenses for the period';
$_['text_taxable_income'] = 'Taxable Income';
$_['text_taxable_income_description'] = 'Taxable income';
$_['text_tax_due'] = 'Tax Due';
$_['text_tax_due_description'] = 'Tax due';
$_['text_tax_return_details'] = 'Tax Return Details';
$_['text_income_details'] = 'Income Details';
$_['text_expenses_details'] = 'Expenses Details';
$_['column_account_name'] = 'Account Name';
$_['column_account_code'] = 'Account Code';
$_['column_amount'] = 'Amount';
$_['column_percentage'] = 'Percentage';
$_['column_deductible'] = 'Deductible';
$_['text_yes'] = 'Yes';
$_['text_no'] = 'No';
$_['text_tax_calculation'] = 'Tax Calculation';
$_['text_gross_income'] = 'Gross Income';
$_['text_deductible_expenses'] = 'Deductible Expenses';
$_['text_tax_paid'] = 'Tax Paid';
$_['text_balance_due'] = 'Balance Due';
$_['text_refund_due'] = 'Refund Due';
$_['text_no_tax_return'] = 'No Tax Return';
$_['error_financial_year'] = 'Financial year is required';
$_['error_tax_rate'] = 'Tax rate is required';
$_['error_complete_form'] = 'Please complete all fields';
$_['text_exporting'] = 'Exporting';
$_['confirm_submit_eta'] = 'Are you sure you want to submit the return to ETA?';
$_['text_submitting_eta'] = 'Submitting to ETA';
$_['success_eta_submission'] = 'Return submitted to ETA successfully';
$_['error_eta_submission'] = 'Failed to submit return to ETA';
$_['text_calculating'] = 'Calculating';
$_['success_provision_calculated'] = 'Provision calculated successfully';
$_['error_provision_calculation'] = 'Failed to calculate provision';
$_['text_eta_connected'] = 'Connected to ETA';
$_['text_eta_disconnected'] = 'Disconnected from ETA';

// Enhanced performance and analytics variables
$_['text_optimized_tax']               = 'Optimized Tax Return';
$_['text_tax_analysis']                = 'Tax Analysis';
$_['text_cache_enabled']               = 'Cache Enabled';
$_['text_advanced_analysis']           = 'Advanced Analysis';
$_['text_monthly_breakdown']           = 'Monthly Breakdown';
$_['text_effective_tax_rate']          = 'Effective Tax Rate';
$_['text_estimated_tax']               = 'Estimated Tax';
$_['text_eta_submission']              = 'ETA Submission';
$_['text_submission_reference']        = 'Submission Reference';
$_['text_submission_status']           = 'Submission Status';
$_['text_tax_year']                    = 'Tax Year';
$_['text_corporate_tax_rate']          = 'Corporate Tax Rate';
$_['button_tax_analysis']              = 'Tax Analysis';
$_['text_loading_analysis']            = 'Loading tax analysis...';
$_['text_analysis_ready']              = 'Analysis ready';
?>
