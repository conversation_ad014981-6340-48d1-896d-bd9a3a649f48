<?php
/**
 * Enhanced Queue Controller for AYM ERP
 * Advanced queue management with monitoring and control
 */
class ControllerQueueQueue extends Controller {
    private $error = array();
    
    public function index() {
        $this->load->language('queue/queue');
        $this->document->setTitle($this->language->get('heading_title'));
        
        // التحقق من الصلاحيات
        if (!$this->user->hasPermission('access', 'queue/queue')) {
            $this->session->data['error'] = $this->language->get('error_permission');
            $this->response->redirect($this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true));
        }
        
        $this->load->model('queue/queue');
        
        $data['breadcrumbs'] = array();
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('queue/queue', 'user_token=' . $this->session->data['user_token'], true)
        );
        
        // الحصول على إحصائيات Queue
        $data['stats'] = $this->model_queue_queue->getQueueStats();
        $data['pending_count'] = $this->model_queue_queue->getPendingJobsCount();
        $data['processing_count'] = $this->model_queue_queue->getProcessingJobsCount();
        $data['failed_count'] = $this->model_queue_queue->getFailedJobsCount();
        
        // URLs للعمليات
        $data['process_url'] = $this->url->link('queue/queue/process', 'user_token=' . $this->session->data['user_token'], true);
        $data['cleanup_url'] = $this->url->link('queue/queue/cleanup', 'user_token=' . $this->session->data['user_token'], true);
        $data['reset_stuck_url'] = $this->url->link('queue/queue/resetStuck', 'user_token=' . $this->session->data['user_token'], true);
        $data['jobs_url'] = $this->url->link('queue/queue/getJobs', 'user_token=' . $this->session->data['user_token'], true);
        
        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');
        
        $this->response->setOutput($this->load->view('queue/queue', $data));
    }
    
    /**
     * معالجة المهام المعلقة
     */
    public function process() {
        $this->load->language('queue/queue');
        
        $json = array();
        
        if (!$this->user->hasPermission('modify', 'queue/queue')) {
            $json['error'] = $this->language->get('error_permission');
        } else {
            try {
                // إنشاء معالج Queue
                require_once(DIR_SYSTEM . 'library/enhanced_queue.php');
                require_once(DIR_SYSTEM . 'library/queue_processor.php');
                
                $processor = new QueueProcessor($this->registry);
                
                // معالجة المهام
                $max_jobs = isset($this->request->post['max_jobs']) ? (int)$this->request->post['max_jobs'] : 50;
                $result = $processor->processJobs($max_jobs);
                
                $json['success'] = sprintf(
                    $this->language->get('text_process_success'),
                    $result['processed'],
                    $result['success'],
                    $result['failed'],
                    round($result['execution_time'], 2)
                );
                
                $json['stats'] = $result;
                
            } catch (Exception $e) {
                $json['error'] = 'Processing error: ' . $e->getMessage();
            }
        }
        
        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }
    
    /**
     * تنظيف المهام القديمة
     */
    public function cleanup() {
        $this->load->language('queue/queue');
        
        $json = array();
        
        if (!$this->user->hasPermission('modify', 'queue/queue')) {
            $json['error'] = $this->language->get('error_permission');
        } else {
            try {
                require_once(DIR_SYSTEM . 'library/enhanced_queue.php');
                require_once(DIR_SYSTEM . 'library/queue_processor.php');
                
                $processor = new QueueProcessor($this->registry);
                $deleted_count = $processor->cleanup();
                
                $json['success'] = sprintf($this->language->get('text_cleanup_success'), $deleted_count);
                
            } catch (Exception $e) {
                $json['error'] = 'Cleanup error: ' . $e->getMessage();
            }
        }
        
        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }
    
    /**
     * إعادة تعيين المهام المعلقة
     */
    public function resetStuck() {
        $this->load->language('queue/queue');
        
        $json = array();
        
        if (!$this->user->hasPermission('modify', 'queue/queue')) {
            $json['error'] = $this->language->get('error_permission');
        } else {
            try {
                require_once(DIR_SYSTEM . 'library/enhanced_queue.php');
                
                $queue = new EnhancedQueue($this->db);
                $timeout_minutes = isset($this->request->post['timeout']) ? (int)$this->request->post['timeout'] : 30;
                $reset_count = $queue->resetStuckJobs($timeout_minutes);
                
                $json['success'] = sprintf($this->language->get('text_reset_success'), $reset_count);
                
            } catch (Exception $e) {
                $json['error'] = 'Reset error: ' . $e->getMessage();
            }
        }
        
        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }
    
    /**
     * الحصول على قائمة المهام
     */
    public function getJobs() {
        $this->load->language('queue/queue');
        
        $json = array();
        
        if (!$this->user->hasPermission('access', 'queue/queue')) {
            $json['error'] = $this->language->get('error_permission');
        } else {
            try {
                $this->load->model('queue/queue');
                
                $filter_data = array(
                    'status' => isset($this->request->get['status']) ? $this->request->get['status'] : '',
                    'job_type' => isset($this->request->get['job_type']) ? $this->request->get['job_type'] : '',
                    'start' => isset($this->request->get['start']) ? (int)$this->request->get['start'] : 0,
                    'limit' => isset($this->request->get['limit']) ? (int)$this->request->get['limit'] : 20
                );
                
                $jobs = $this->model_queue_queue->getJobs($filter_data);
                $total = $this->model_queue_queue->getTotalJobs($filter_data);
                
                foreach ($jobs as &$job) {
                    $job['job_data'] = json_decode($job['job_data'], true);
                    $job['created_at_formatted'] = date('Y-m-d H:i:s', strtotime($job['created_at']));
                    $job['updated_at_formatted'] = date('Y-m-d H:i:s', strtotime($job['updated_at']));
                    
                    if ($job['started_at']) {
                        $job['started_at_formatted'] = date('Y-m-d H:i:s', strtotime($job['started_at']));
                    }
                    
                    if ($job['completed_at']) {
                        $job['completed_at_formatted'] = date('Y-m-d H:i:s', strtotime($job['completed_at']));
                    }
                    
                    // إضافة أزرار العمليات
                    $job['cancel_url'] = $this->url->link('queue/queue/cancel', 'user_token=' . $this->session->data['user_token'] . '&job_id=' . $job['id'], true);
                    $job['retry_url'] = $this->url->link('queue/queue/retry', 'user_token=' . $this->session->data['user_token'] . '&job_id=' . $job['id'], true);
                }
                
                $json['jobs'] = $jobs;
                $json['total'] = $total;
                
            } catch (Exception $e) {
                $json['error'] = 'Error fetching jobs: ' . $e->getMessage();
            }
        }
        
        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }
    
    /**
     * إلغاء مهمة
     */
    public function cancel() {
        $this->load->language('queue/queue');
        
        $json = array();
        
        if (!$this->user->hasPermission('modify', 'queue/queue')) {
            $json['error'] = $this->language->get('error_permission');
        } else {
            $job_id = isset($this->request->get['job_id']) ? (int)$this->request->get['job_id'] : 0;
            
            if (!$job_id) {
                $json['error'] = 'Invalid job ID';
            } else {
                try {
                    require_once(DIR_SYSTEM . 'library/enhanced_queue.php');
                    
                    $queue = new EnhancedQueue($this->db);
                    $result = $queue->cancelJob($job_id);
                    
                    if ($result) {
                        $json['success'] = $this->language->get('text_cancel_success');
                    } else {
                        $json['error'] = 'Failed to cancel job';
                    }
                    
                } catch (Exception $e) {
                    $json['error'] = 'Cancel error: ' . $e->getMessage();
                }
            }
        }
        
        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }
    
    /**
     * إعادة تشغيل مهمة فاشلة
     */
    public function retry() {
        $this->load->language('queue/queue');
        
        $json = array();
        
        if (!$this->user->hasPermission('modify', 'queue/queue')) {
            $json['error'] = $this->language->get('error_permission');
        } else {
            $job_id = isset($this->request->get['job_id']) ? (int)$this->request->get['job_id'] : 0;
            
            if (!$job_id) {
                $json['error'] = 'Invalid job ID';
            } else {
                try {
                    $this->load->model('queue/queue');
                    $result = $this->model_queue_queue->retryJob($job_id);
                    
                    if ($result) {
                        $json['success'] = $this->language->get('text_retry_success');
                    } else {
                        $json['error'] = 'Failed to retry job';
                    }
                    
                } catch (Exception $e) {
                    $json['error'] = 'Retry error: ' . $e->getMessage();
                }
            }
        }
        
        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }
    
    /**
     * الحصول على إحصائيات مفصلة
     */
    public function getDetailedStats() {
        $json = array();
        
        if (!$this->user->hasPermission('access', 'queue/queue')) {
            $json['error'] = $this->language->get('error_permission');
        } else {
            try {
                require_once(DIR_SYSTEM . 'library/enhanced_queue.php');
                require_once(DIR_SYSTEM . 'library/queue_processor.php');
                
                $processor = new QueueProcessor($this->registry);
                $json['stats'] = $processor->getStats();
                
                $this->load->model('queue/queue');
                $json['counts'] = array(
                    'pending' => $this->model_queue_queue->getPendingJobsCount(),
                    'processing' => $this->model_queue_queue->getProcessingJobsCount(),
                    'failed' => $this->model_queue_queue->getFailedJobsCount(),
                    'completed' => $this->model_queue_queue->getCompletedJobsCount()
                );
                
            } catch (Exception $e) {
                $json['error'] = 'Error fetching stats: ' . $e->getMessage();
            }
        }
        
        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }
}
?>
