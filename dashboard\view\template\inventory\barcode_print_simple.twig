{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="submit" form="inventory\barcode-form" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fas fa-save"></i></button>
        <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-light"><i class="fas fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    
    <div class="card">
      <div class="card-header"><i class="fas fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}chart-bar{% elif screen_type == 'settings' %}cog{% else %}pencil-alt{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="card-body">
        <form id="inventory\barcode-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-add">{{ text_add }}</label>
            <div class="col-sm-10">
              <input type="text" name="add" value="{{ add }}" placeholder="{{ text_add }}" id="input-add" class="form-control" />
              {% if error_add %}
                <div class="invalid-feedback">{{ error_add }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-barcode">{{ text_barcode }}</label>
            <div class="col-sm-10">
              <input type="text" name="barcode" value="{{ barcode }}" placeholder="{{ text_barcode }}" id="input-barcode" class="form-control" />
              {% if error_barcode %}
                <div class="invalid-feedback">{{ error_barcode }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-barcode_image">{{ text_barcode_image }}</label>
            <div class="col-sm-10">
              <input type="text" name="barcode_image" value="{{ barcode_image }}" placeholder="{{ text_barcode_image }}" id="input-barcode_image" class="form-control" />
              {% if error_barcode_image %}
                <div class="invalid-feedback">{{ error_barcode_image }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-barcode_info">{{ text_barcode_info }}</label>
            <div class="col-sm-10">
              <input type="text" name="barcode_info" value="{{ barcode_info }}" placeholder="{{ text_barcode_info }}" id="input-barcode_info" class="form-control" />
              {% if error_barcode_info %}
                <div class="invalid-feedback">{{ error_barcode_info }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-barcode_type">{{ text_barcode_type }}</label>
            <div class="col-sm-10">
              <input type="text" name="barcode_type" value="{{ barcode_type }}" placeholder="{{ text_barcode_type }}" id="input-barcode_type" class="form-control" />
              {% if error_barcode_type %}
                <div class="invalid-feedback">{{ error_barcode_type }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-barcode_types">{{ text_barcode_types }}</label>
            <div class="col-sm-10">
              <input type="text" name="barcode_types" value="{{ barcode_types }}" placeholder="{{ text_barcode_types }}" id="input-barcode_types" class="form-control" />
              {% if error_barcode_types %}
                <div class="invalid-feedback">{{ error_barcode_types }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-barcodes">{{ text_barcodes }}</label>
            <div class="col-sm-10">
              <input type="text" name="barcodes" value="{{ barcodes }}" placeholder="{{ text_barcodes }}" id="input-barcodes" class="form-control" />
              {% if error_barcodes %}
                <div class="invalid-feedback">{{ error_barcodes }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-delete">{{ text_delete }}</label>
            <div class="col-sm-10">
              <input type="text" name="delete" value="{{ delete }}" placeholder="{{ text_delete }}" id="input-delete" class="form-control" />
              {% if error_delete %}
                <div class="invalid-feedback">{{ error_delete }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_barcode">{{ text_error_barcode }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_barcode" value="{{ error_barcode }}" placeholder="{{ text_error_barcode }}" id="input-error_barcode" class="form-control" />
              {% if error_error_barcode %}
                <div class="invalid-feedback">{{ error_error_barcode }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_product">{{ text_error_product }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_product" value="{{ error_product }}" placeholder="{{ text_error_product }}" id="input-error_product" class="form-control" />
              {% if error_error_product %}
                <div class="invalid-feedback">{{ error_error_product }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_barcode">{{ text_filter_barcode }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_barcode" value="{{ filter_barcode }}" placeholder="{{ text_filter_barcode }}" id="input-filter_barcode" class="form-control" />
              {% if error_filter_barcode %}
                <div class="invalid-feedback">{{ error_filter_barcode }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_product">{{ text_filter_product }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_product" value="{{ filter_product }}" placeholder="{{ text_filter_product }}" id="input-filter_product" class="form-control" />
              {% if error_filter_product %}
                <div class="invalid-feedback">{{ error_filter_product }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_status">{{ text_filter_status }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_status" value="{{ filter_status }}" placeholder="{{ text_filter_status }}" id="input-filter_status" class="form-control" />
              {% if error_filter_status %}
                <div class="invalid-feedback">{{ error_filter_status }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_type">{{ text_filter_type }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_type" value="{{ filter_type }}" placeholder="{{ text_filter_type }}" id="input-filter_type" class="form-control" />
              {% if error_filter_type %}
                <div class="invalid-feedback">{{ error_filter_type }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-generate">{{ text_generate }}</label>
            <div class="col-sm-10">
              <input type="text" name="generate" value="{{ generate }}" placeholder="{{ text_generate }}" id="input-generate" class="form-control" />
              {% if error_generate %}
                <div class="invalid-feedback">{{ error_generate }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="invalid-feedback">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-options">{{ text_options }}</label>
            <div class="col-sm-10">
              <input type="text" name="options" value="{{ options }}" placeholder="{{ text_options }}" id="input-options" class="form-control" />
              {% if error_options %}
                <div class="invalid-feedback">{{ error_options }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-order">{{ text_order }}</label>
            <div class="col-sm-10">
              <input type="text" name="order" value="{{ order }}" placeholder="{{ text_order }}" id="input-order" class="form-control" />
              {% if error_order %}
                <div class="invalid-feedback">{{ error_order }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-print">{{ text_print }}</label>
            <div class="col-sm-10">
              <input type="text" name="print" value="{{ print }}" placeholder="{{ text_print }}" id="input-print" class="form-control" />
              {% if error_print %}
                <div class="invalid-feedback">{{ error_print }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-product">{{ text_product }}</label>
            <div class="col-sm-10">
              <input type="text" name="product" value="{{ product }}" placeholder="{{ text_product }}" id="input-product" class="form-control" />
              {% if error_product %}
                <div class="invalid-feedback">{{ error_product }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-selected">{{ text_selected }}</label>
            <div class="col-sm-10">
              <input type="text" name="selected" value="{{ selected }}" placeholder="{{ text_selected }}" id="input-selected" class="form-control" />
              {% if error_selected %}
                <div class="invalid-feedback">{{ error_selected }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort">{{ text_sort }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort" value="{{ sort }}" placeholder="{{ text_sort }}" id="input-sort" class="form-control" />
              {% if error_sort %}
                <div class="invalid-feedback">{{ error_sort }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-status">{{ text_status }}</label>
            <div class="col-sm-10">
              <input type="text" name="status" value="{{ status }}" placeholder="{{ text_status }}" id="input-status" class="form-control" />
              {% if error_status %}
                <div class="invalid-feedback">{{ error_status }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_form">{{ text_text_form }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_form" value="{{ text_form }}" placeholder="{{ text_text_form }}" id="input-text_form" class="form-control" />
              {% if error_text_form %}
                <div class="invalid-feedback">{{ error_text_form }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-units">{{ text_units }}</label>
            <div class="col-sm-10">
              <input type="text" name="units" value="{{ units }}" placeholder="{{ text_units }}" id="input-units" class="form-control" />
              {% if error_units %}
                <div class="invalid-feedback">{{ error_units }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="invalid-feedback">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}