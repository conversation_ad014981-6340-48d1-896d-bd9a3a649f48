{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="submit" form="accounts\bank_accounts_advanced-form" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fas fa-save"></i></button>
        <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-light"><i class="fas fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    
    <div class="card">
      <div class="card-header"><i class="fas fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}chart-bar{% elif screen_type == 'settings' %}cog{% else %}pencil-alt{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="card-body">
        <form id="accounts\bank_accounts_advanced-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-accounts">{{ text_accounts }}</label>
            <div class="col-sm-10">
              <input type="text" name="accounts" value="{{ accounts }}" placeholder="{{ text_accounts }}" id="input-accounts" class="form-control" />
              {% if error_accounts %}
                <div class="invalid-feedback">{{ error_accounts }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-add">{{ text_add }}</label>
            <div class="col-sm-10">
              <input type="text" name="add" value="{{ add }}" placeholder="{{ text_add }}" id="input-add" class="form-control" />
              {% if error_add %}
                <div class="invalid-feedback">{{ error_add }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-analysis_url">{{ text_analysis_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="analysis_url" value="{{ analysis_url }}" placeholder="{{ text_analysis_url }}" id="input-analysis_url" class="form-control" />
              {% if error_analysis_url %}
                <div class="invalid-feedback">{{ error_analysis_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-balance_history_url">{{ text_balance_history_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="balance_history_url" value="{{ balance_history_url }}" placeholder="{{ text_balance_history_url }}" id="input-balance_history_url" class="form-control" />
              {% if error_balance_history_url %}
                <div class="invalid-feedback">{{ error_balance_history_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-cash_flow_url">{{ text_cash_flow_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="cash_flow_url" value="{{ cash_flow_url }}" placeholder="{{ text_cash_flow_url }}" id="input-cash_flow_url" class="form-control" />
              {% if error_cash_flow_url %}
                <div class="invalid-feedback">{{ error_cash_flow_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-delete">{{ text_delete }}</label>
            <div class="col-sm-10">
              <input type="text" name="delete" value="{{ delete }}" placeholder="{{ text_delete }}" id="input-delete" class="form-control" />
              {% if error_delete %}
                <div class="invalid-feedback">{{ error_delete }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="invalid-feedback">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-order">{{ text_order }}</label>
            <div class="col-sm-10">
              <input type="text" name="order" value="{{ order }}" placeholder="{{ text_order }}" id="input-order" class="form-control" />
              {% if error_order %}
                <div class="invalid-feedback">{{ error_order }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-pagination">{{ text_pagination }}</label>
            <div class="col-sm-10">
              <input type="text" name="pagination" value="{{ pagination }}" placeholder="{{ text_pagination }}" id="input-pagination" class="form-control" />
              {% if error_pagination %}
                <div class="invalid-feedback">{{ error_pagination }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-reconcile">{{ text_reconcile }}</label>
            <div class="col-sm-10">
              <input type="text" name="reconcile" value="{{ reconcile }}" placeholder="{{ text_reconcile }}" id="input-reconcile" class="form-control" />
              {% if error_reconcile %}
                <div class="invalid-feedback">{{ error_reconcile }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-results">{{ text_results }}</label>
            <div class="col-sm-10">
              <input type="text" name="results" value="{{ results }}" placeholder="{{ text_results }}" id="input-results" class="form-control" />
              {% if error_results %}
                <div class="invalid-feedback">{{ error_results }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-selected">{{ text_selected }}</label>
            <div class="col-sm-10">
              <input type="text" name="selected" value="{{ selected }}" placeholder="{{ text_selected }}" id="input-selected" class="form-control" />
              {% if error_selected %}
                <div class="invalid-feedback">{{ error_selected }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort">{{ text_sort }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort" value="{{ sort }}" placeholder="{{ text_sort }}" id="input-sort" class="form-control" />
              {% if error_sort %}
                <div class="invalid-feedback">{{ error_sort }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_account_name">{{ text_sort_account_name }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_account_name" value="{{ sort_account_name }}" placeholder="{{ text_sort_account_name }}" id="input-sort_account_name" class="form-control" />
              {% if error_sort_account_name %}
                <div class="invalid-feedback">{{ error_sort_account_name }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_bank_name">{{ text_sort_bank_name }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_bank_name" value="{{ sort_bank_name }}" placeholder="{{ text_sort_bank_name }}" id="input-sort_bank_name" class="form-control" />
              {% if error_sort_bank_name %}
                <div class="invalid-feedback">{{ error_sort_bank_name }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_current_balance">{{ text_sort_current_balance }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_current_balance" value="{{ sort_current_balance }}" placeholder="{{ text_sort_current_balance }}" id="input-sort_current_balance" class="form-control" />
              {% if error_sort_current_balance %}
                <div class="invalid-feedback">{{ error_sort_current_balance }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_status">{{ text_sort_status }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_status" value="{{ sort_status }}" placeholder="{{ text_sort_status }}" id="input-sort_status" class="form-control" />
              {% if error_sort_status %}
                <div class="invalid-feedback">{{ error_sort_status }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-transactions_url">{{ text_transactions_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="transactions_url" value="{{ transactions_url }}" placeholder="{{ text_transactions_url }}" id="input-transactions_url" class="form-control" />
              {% if error_transactions_url %}
                <div class="invalid-feedback">{{ error_transactions_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-transfer">{{ text_transfer }}</label>
            <div class="col-sm-10">
              <input type="text" name="transfer" value="{{ transfer }}" placeholder="{{ text_transfer }}" id="input-transfer" class="form-control" />
              {% if error_transfer %}
                <div class="invalid-feedback">{{ error_transfer }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="invalid-feedback">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}