Filter.ExtractStyleBlocks.Escaping
TYPE: bool
VERSION: 3.0.0
DEFAULT: true
ALIASES: Filter.ExtractStyleBlocksEscaping, FilterParam.ExtractStyleBlocksEscaping
--DESCRIPTION--

<p>
  Whether or not to escape the dangerous characters &lt;, &gt; and &amp;
  as \3C, \3E and \26, respectively. This is can be safely set to false
  if the contents of StyleBlocks will be placed in an external stylesheet,
  where there is no risk of it being interpreted as HTML.
</p>
--# vim: et sw=4 sts=4
