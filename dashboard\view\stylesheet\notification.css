/**
 * Dashboard Notification Center Styles
 * Provides styling for the notification and message centers
 */

/* Notification Badge */
.notification-badge,
.message-badge {
    position: absolute;
    top: 5px;
    right: 5px;
    display: inline-block;
    min-width: 18px;
    height: 18px;
    padding: 0 5px;
    font-size: 11px;
    font-weight: 700;
    line-height: 18px;
    color: #fff;
    text-align: center;
    background-color: #dc3545;
    border-radius: 10px;
    transition: all 0.3s ease;
}

/* Notification Center Container */
.notification-center,
.message-center {
    position: absolute;
    top: 60px;
    right: 15px;
    width: 350px;
    max-height: 500px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    z-index: 1050;
    overflow: hidden;
    visibility: hidden;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.notification-center.show,
.message-center.show {
    visibility: visible;
    opacity: 1;
    transform: translateY(0);
}

/* Notification Header */
.notification-header,
.message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.notification-header h5,
.message-header h5 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.notification-actions,
.message-actions {
    display: flex;
    gap: 10px;
}

.notification-actions a,
.message-actions a {
    color: #6c757d;
    font-size: 13px;
    text-decoration: none;
}

.notification-actions a:hover,
.message-actions a:hover {
    color: #343a40;
}

/* Notification Tabs */
.notification-tabs {
    display: flex;
    border-bottom: 1px solid #e9ecef;
}

.notification-tab {
    flex: 1;
    padding: 10px 15px;
    text-align: center;
    font-size: 13px;
    color: #6c757d;
    cursor: pointer;
    transition: all 0.2s ease;
}

.notification-tab:hover {
    color: #343a40;
    background-color: #f8f9fa;
}

.notification-tab.active {
    color: #007bff;
    border-bottom: 2px solid #007bff;
}

/* Notification List */
.notification-list,
.message-list {
    max-height: 350px;
    overflow-y: auto;
    padding: 0;
    margin: 0;
    list-style: none;
}

.notification-item,
.message-item {
    display: flex;
    padding: 12px 15px;
    border-bottom: 1px solid #e9ecef;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.notification-item:hover,
.message-item:hover {
    background-color: #f8f9fa;
}

.notification-item.unread,
.message-item.unread {
    background-color: #f0f7ff;
}

.notification-item.unread:hover,
.message-item.unread:hover {
    background-color: #e6f2ff;
}

.notification-item.empty,
.message-item.empty {
    justify-content: center;
    padding: 20px;
    color: #6c757d;
    cursor: default;
}

/* Notification Icon */
.notification-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    margin-right: 15px;
    border-radius: 50%;
    flex-shrink: 0;
}

.notification-icon i {
    font-size: 16px;
}

/* Notification Details */
.notification-details,
.message-details {
    flex: 1;
    min-width: 0;
}

.notification-title,
.message-sender {
    margin: 0 0 5px;
    font-size: 14px;
    font-weight: 600;
    color: #343a40;
}

.notification-message,
.message-preview {
    margin: 0 0 5px;
    font-size: 13px;
    color: #6c757d;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.notification-time,
.message-time {
    display: block;
    font-size: 11px;
    color: #adb5bd;
}

.message-sender .message-time {
    display: inline-block;
    margin-left: 5px;
}

/* Message Avatar */
.message-avatar {
    width: 40px;
    height: 40px;
    margin-right: 15px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;
}

.message-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Notification Footer */
.notification-footer,
.message-footer {
    padding: 10px 15px;
    text-align: center;
    border-top: 1px solid #e9ecef;
}

.notification-footer a,
.message-footer a {
    display: block;
    color: #007bff;
    font-size: 13px;
    text-decoration: none;
}

/* Animation for new notifications */
@keyframes notification-pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.2);
    }
    100% {
        transform: scale(1);
    }
}

.notification-badge.pulse,
.message-badge.pulse {
    animation: notification-pulse 0.5s ease;
}

/* Responsive adjustments */
@media (max-width: 576px) {
    .notification-center,
    .message-center {
        width: calc(100% - 30px);
        right: 15px;
    }
}