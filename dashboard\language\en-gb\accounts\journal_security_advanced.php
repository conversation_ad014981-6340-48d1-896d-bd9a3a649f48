<?php
// Heading
$_['heading_title']                    = 'Advanced Journal Security System';

// Text
$_['text_success']                     = 'Success: Security settings have been saved successfully!';
$_['text_list']                        = 'Journal Security List';
$_['text_form']                        = 'Journal Security Form';
$_['text_add']                         = 'Add Security';
$_['text_edit']                        = 'Edit Security';
$_['text_view']                        = 'View Security';
$_['text_delete']                      = 'Delete Security';
$_['text_journal_security_advanced']   = 'Advanced Journal Security System';
$_['text_loading']                     = 'Loading...';
$_['text_processing']                  = 'Processing...';

// Security Status
$_['text_secured']                     = 'Secured';
$_['text_unsecured']                   = 'Unsecured';
$_['text_locked']                      = 'Locked';
$_['text_unlocked']                    = 'Unlocked';
$_['text_protected']                   = 'Protected';
$_['text_unprotected']                 = 'Unprotected';

// Security Actions
$_['text_secure']                      = 'Secure';
$_['text_unsecure']                    = 'Unsecure';
$_['text_lock']                        = 'Lock';
$_['text_unlock']                      = 'Unlock';
$_['text_protect']                     = 'Protect';
$_['text_unprotect']                   = 'Unprotect';

// Security Levels
$_['text_basic_security']              = 'Basic Security';
$_['text_advanced_security']           = 'Advanced Security';
$_['text_enterprise_security']         = 'Enterprise Security';
$_['text_maximum_security']            = 'Maximum Security';

// Protection Types
$_['text_edit_protection']             = 'Edit Protection';
$_['text_delete_protection']           = 'Delete Protection';
$_['text_view_protection']             = 'View Protection';
$_['text_full_protection']             = 'Full Protection';

// Controller language variables - Direct Arabic texts
$_['log_view_journal_security_screen'] = 'View journal security screen';
$_['error_edit_after_review_post'] = 'Cannot edit journal after review or posting - advanced security system';
$_['log_edit_attempt_blocked'] = 'Protected journal edit attempt - blocked';
$_['success_can_edit'] = 'Journal can be edited';
$_['error_check_journal_status'] = 'Error checking journal status';
$_['error_journal_id_required'] = 'Journal ID required';
$_['error_no_delete_permission'] = 'No permission to delete journal entries - advanced security system';
$_['log_delete_attempt_no_permission'] = 'Journal delete attempt without permission - blocked';
$_['error_delete_after_review_post'] = 'Cannot delete journal after review or posting - even with advanced permissions';
$_['log_delete_attempt_reviewed'] = 'Reviewed/posted journal delete attempt - blocked';
$_['warning_related_entries'] = 'Related entries exist for this journal - continue?';
$_['success_can_delete'] = 'Journal can be deleted';
$_['error_check_delete_ability'] = 'Error checking delete ability';
$_['success_journal_secured'] = 'Journal secured successfully - cannot be edited or deleted';
$_['log_journal_secured'] = 'Journal secured after review';
$_['error_secure_journal_failed'] = 'Failed to secure journal';
$_['error_secure_journal'] = 'Error securing journal';
$_['error_no_unsecure_permission'] = 'No permission to unsecure journals - general manager permission only';
$_['log_unsecure_attempt_no_permission'] = 'Journal unsecure attempt without permission - blocked';
$_['success_journal_unsecured'] = 'Journal unsecured - can be edited now';
$_['log_journal_unsecured'] = 'Journal unsecured - reason';
$_['error_unsecure_journal_failed'] = 'Failed to unsecure journal';
$_['error_unsecure_journal'] = 'Error unsecuring journal';
$_['error_journal_id_reason_required'] = 'Journal ID and reason required';

// Column
$_['column_journal_number']            = 'Journal Number';
$_['column_journal_date']              = 'Journal Date';
$_['column_description']               = 'Description';
$_['column_amount']                    = 'Amount';
$_['column_security_level']            = 'Security Level';
$_['column_protection_type']           = 'Protection Type';
$_['column_secured_by']                = 'Secured By';
$_['column_secured_date']              = 'Secured Date';
$_['column_status']                    = 'Status';
$_['column_action']                    = 'Action';

// Entry
$_['entry_journal_id']                 = 'Journal ID';
$_['entry_security_level']             = 'Security Level';
$_['entry_protection_type']            = 'Protection Type';
$_['entry_reason']                     = 'Reason';
$_['entry_notes']                      = 'Notes';
$_['entry_effective_date']             = 'Effective Date';
$_['entry_expiry_date']                = 'Expiry Date';
$_['entry_auto_secure']                = 'Auto Secure';
$_['entry_notification']               = 'Notification';

// Button
$_['button_secure']                    = 'Secure';
$_['button_unsecure']                  = 'Unsecure';
$_['button_lock']                      = 'Lock';
$_['button_unlock']                    = 'Unlock';
$_['button_protect']                   = 'Protect';
$_['button_unprotect']                 = 'Unprotect';
$_['button_check_security']            = 'Check Security';
$_['button_security_report']           = 'Security Report';
$_['button_audit_trail']               = 'Audit Trail';
$_['button_bulk_secure']               = 'Bulk Secure';
$_['button_bulk_unsecure']             = 'Bulk Unsecure';

// Tab
$_['tab_general']                      = 'General';
$_['tab_security_settings']            = 'Security Settings';
$_['tab_protection_rules']             = 'Protection Rules';
$_['tab_audit_log']                    = 'Audit Log';
$_['tab_notifications']                = 'Notifications';
$_['tab_reports']                      = 'Reports';

// Help
$_['help_security_level']              = 'Security level required for the journal entry';
$_['help_protection_type']             = 'Type of protection applied to the journal';
$_['help_auto_secure']                 = 'Automatically secure journal after review';
$_['help_notification']                = 'Send notification when protected journal is accessed';

// Error
$_['error_warning']                    = 'Warning: Please check the form carefully for errors!';
$_['error_permission']                 = 'Warning: You do not have permission to access advanced journal security!';
$_['error_journal_not_found']          = 'Journal entry not found!';
$_['error_already_secured']            = 'Journal is already secured!';
$_['error_not_secured']                = 'Journal is not secured!';
$_['error_cannot_unsecure']            = 'Cannot unsecure this journal!';
$_['error_security_level_required']    = 'Security level is required!';
$_['error_protection_type_required']   = 'Protection type is required!';
$_['error_reason_required']            = 'Reason is required!';

// Success
$_['success_security_saved']           = 'Security settings saved successfully!';
$_['success_journal_secured']          = 'Journal secured successfully!';
$_['success_journal_unsecured']        = 'Journal unsecured successfully!';
$_['success_bulk_secured']             = 'Selected journals secured successfully!';
$_['success_bulk_unsecured']           = 'Selected journals unsecured successfully!';

// Security Messages
$_['text_security_warning']            = 'Security Warning: Attempt to access protected journal';
$_['text_security_alert']              = 'Security Alert: Suspicious activity detected';
$_['text_access_denied']               = 'Access Denied';
$_['text_unauthorized_access']         = 'Unauthorized Access';
$_['text_security_breach']             = 'Security Breach';
$_['text_audit_required']              = 'Audit Required';

// Additional variables for template
$_['text_actions'] = 'Actions';
$_['text_add_security'] = 'Add Security';
$_['text_export_options'] = 'Export Options';
$_['text_security_filters'] = 'Security Filters';
$_['text_all_levels'] = 'All Levels';
$_['text_all_types'] = 'All Types';
$_['text_all_statuses'] = 'All Statuses';
$_['text_active'] = 'Active';
$_['text_inactive'] = 'Inactive';
$_['text_total_secured'] = 'Total Secured';
$_['text_total_unsecured'] = 'Total Unsecured';
$_['text_security_violations'] = 'Security Violations';
$_['text_security_list'] = 'Security List';
$_['text_no_security'] = 'No security settings found';
$_['text_exporting'] = 'Exporting';
$_['text_loading'] = 'Loading';
$_['button_filter'] = 'Filter';

// Risk Levels
$_['text_low_risk'] = 'Low Risk';
$_['text_medium_risk'] = 'Medium Risk';
$_['text_high_risk'] = 'High Risk';
$_['text_critical_risk'] = 'Critical Risk';

// Compliance Status
$_['text_compliant'] = 'Compliant';
$_['text_non_compliant'] = 'Non-Compliant';
$_['text_partially_compliant'] = 'Partially Compliant';
$_['text_under_review'] = 'Under Review';

// Security Protocols
$_['text_encryption_enabled'] = 'Encryption Enabled';
$_['text_backup_secured'] = 'Backup Secured';
$_['text_access_logged'] = 'Access Logged';
$_['text_integrity_verified'] = 'Integrity Verified';

// Monitoring
$_['text_real_time_monitoring'] = 'Real-time Monitoring';
$_['text_automated_alerts'] = 'Automated Alerts';
$_['text_security_dashboard'] = 'Security Dashboard';
$_['text_threat_detection'] = 'Threat Detection';
?>
