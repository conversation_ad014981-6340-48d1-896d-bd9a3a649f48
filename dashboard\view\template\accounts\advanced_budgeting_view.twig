{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        {% if has_permission %}
        <button type="button" id="button-edit" data-toggle="tooltip" title="{{ button_edit }}" class="btn btn-primary">
          <i class="fa fa-pencil"></i> {{ button_edit }}
        </button>
        {% if budget.status == 'draft' %}
        <button type="button" id="button-submit" data-toggle="tooltip" title="{{ button_submit }}" class="btn btn-warning">
          <i class="fa fa-paper-plane"></i> {{ button_submit }}
        </button>
        {% endif %}
        {% if can_approve %}
        <button type="button" id="button-approve" data-toggle="tooltip" title="{{ button_approve }}" class="btn btn-success">
          <i class="fa fa-check"></i> {{ button_approve }}
        </button>
        <button type="button" id="button-reject" data-toggle="tooltip" title="{{ button_reject }}" class="btn btn-danger">
          <i class="fa fa-times"></i> {{ button_reject }}
        </button>
        {% endif %}
        <button type="button" id="button-export" data-toggle="tooltip" title="{{ button_export }}" class="btn btn-info">
          <i class="fa fa-download"></i> {{ button_export }}
        </button>
        {% endif %}
        <a href="{{ back }}" data-toggle="tooltip" title="{{ button_back }}" class="btn btn-default">
          <i class="fa fa-reply"></i> {{ button_back }}
        </a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    {% if success %}
    <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    
    <!-- معلومات الموازنة الأساسية -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-info-circle"></i> {{ text_budget_info }}</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-md-6">
            <table class="table table-bordered">
              <tr>
                <td><strong>{{ text_budget_name }}:</strong></td>
                <td>{{ budget.budget_name }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_budget_year }}:</strong></td>
                <td>{{ budget.budget_year }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_department }}:</strong></td>
                <td>{{ budget.department_name }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_budget_type }}:</strong></td>
                <td>{{ budget.budget_type }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_status }}:</strong></td>
                <td>
                  {% if budget.status == 'draft' %}
                    <span class="label label-default">{{ text_status_draft }}</span>
                  {% elseif budget.status == 'submitted' %}
                    <span class="label label-warning">{{ text_status_submitted }}</span>
                  {% elseif budget.status == 'approved' %}
                    <span class="label label-success">{{ text_status_approved }}</span>
                  {% elseif budget.status == 'rejected' %}
                    <span class="label label-danger">{{ text_status_rejected }}</span>
                  {% elseif budget.status == 'active' %}
                    <span class="label label-info">{{ text_status_active }}</span>
                  {% endif %}
                </td>
              </tr>
            </table>
          </div>
          <div class="col-md-6">
            <table class="table table-bordered">
              <tr>
                <td><strong>{{ text_total_amount }}:</strong></td>
                <td>{{ budget.total_amount }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_currency }}:</strong></td>
                <td>{{ budget.currency }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_created_by }}:</strong></td>
                <td>{{ budget.created_by_name }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_date_created }}:</strong></td>
                <td>{{ budget.date_created }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_last_updated }}:</strong></td>
                <td>{{ budget.date_modified }}</td>
              </tr>
            </table>
          </div>
        </div>
        {% if budget.description %}
        <div class="row">
          <div class="col-md-12">
            <h4>{{ text_description }}</h4>
            <p>{{ budget.description|nl2br }}</p>
          </div>
        </div>
        {% endif %}
      </div>
    </div>

    <!-- مسار الموافقة -->
    {% if approval_workflow %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-sitemap"></i> {{ text_approval_workflow }}</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          {% for level in approval_workflow %}
          <div class="col-md-3">
            <div class="panel {% if level.status == 'approved' %}panel-success{% elseif level.status == 'rejected' %}panel-danger{% elseif level.status == 'pending' %}panel-warning{% else %}panel-default{% endif %}">
              <div class="panel-heading text-center">
                <h4>{{ text_level }} {{ level.approval_level }}</h4>
              </div>
              <div class="panel-body text-center">
                <p><strong>{{ level.approver_name }}</strong></p>
                <p>{{ level.role_name }}</p>
                {% if level.status == 'approved' %}
                  <i class="fa fa-check-circle fa-2x text-success"></i>
                  <p class="text-success">{{ text_approved_on }}<br>{{ level.approval_date }}</p>
                {% elseif level.status == 'rejected' %}
                  <i class="fa fa-times-circle fa-2x text-danger"></i>
                  <p class="text-danger">{{ text_rejected_on }}<br>{{ level.rejection_date }}</p>
                {% elseif level.status == 'pending' %}
                  <i class="fa fa-clock-o fa-2x text-warning"></i>
                  <p class="text-warning">{{ text_pending_approval }}</p>
                {% else %}
                  <i class="fa fa-circle-o fa-2x text-muted"></i>
                  <p class="text-muted">{{ text_waiting }}</p>
                {% endif %}
              </div>
            </div>
          </div>
          {% endfor %}
        </div>
      </div>
    </div>
    {% endif %}

    <!-- تفاصيل الموازنة -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-list"></i> {{ text_budget_details }}</h3>
      </div>
      <div class="panel-body">
        <ul class="nav nav-tabs" role="tablist">
          <li role="presentation" class="active">
            <a href="#quarterly" aria-controls="quarterly" role="tab" data-toggle="tab">
              <i class="fa fa-calendar"></i> {{ tab_quarterly_breakdown }}
            </a>
          </li>
          <li role="presentation">
            <a href="#accounts" aria-controls="accounts" role="tab" data-toggle="tab">
              <i class="fa fa-list-alt"></i> {{ tab_account_breakdown }}
            </a>
          </li>
          <li role="presentation">
            <a href="#variance" aria-controls="variance" role="tab" data-toggle="tab">
              <i class="fa fa-line-chart"></i> {{ tab_variance_analysis }}
            </a>
          </li>
          <li role="presentation">
            <a href="#scenarios" aria-controls="scenarios" role="tab" data-toggle="tab">
              <i class="fa fa-sitemap"></i> {{ tab_scenarios }}
            </a>
          </li>
        </ul>

        <div class="tab-content">
          <!-- التوزيع الربعي -->
          <div role="tabpanel" class="tab-pane active" id="quarterly">
            <div class="table-responsive" style="margin-top: 15px;">
              <table class="table table-bordered">
                <thead>
                  <tr class="info">
                    <th>{{ column_account }}</th>
                    <th class="text-right">{{ column_q1 }}</th>
                    <th class="text-right">{{ column_q2 }}</th>
                    <th class="text-right">{{ column_q3 }}</th>
                    <th class="text-right">{{ column_q4 }}</th>
                    <th class="text-right">{{ column_total }}</th>
                  </tr>
                </thead>
                <tbody>
                  {% for item in quarterly_breakdown %}
                  <tr>
                    <td>{{ item.account_name }}</td>
                    <td class="text-right">{{ item.q1_amount }}</td>
                    <td class="text-right">{{ item.q2_amount }}</td>
                    <td class="text-right">{{ item.q3_amount }}</td>
                    <td class="text-right">{{ item.q4_amount }}</td>
                    <td class="text-right"><strong>{{ item.total_amount }}</strong></td>
                  </tr>
                  {% endfor %}
                </tbody>
                <tfoot>
                  <tr class="success">
                    <td><strong>{{ text_total }}</strong></td>
                    <td class="text-right"><strong>{{ quarterly_totals.q1_total }}</strong></td>
                    <td class="text-right"><strong>{{ quarterly_totals.q2_total }}</strong></td>
                    <td class="text-right"><strong>{{ quarterly_totals.q3_total }}</strong></td>
                    <td class="text-right"><strong>{{ quarterly_totals.q4_total }}</strong></td>
                    <td class="text-right"><strong>{{ quarterly_totals.grand_total }}</strong></td>
                  </tr>
                </tfoot>
              </table>
            </div>
          </div>

          <!-- تفصيل الحسابات -->
          <div role="tabpanel" class="tab-pane" id="accounts">
            <div class="table-responsive" style="margin-top: 15px;">
              <table class="table table-bordered table-hover">
                <thead>
                  <tr>
                    <th>{{ column_account_code }}</th>
                    <th>{{ column_account_name }}</th>
                    <th class="text-right">{{ column_budgeted_amount }}</th>
                    <th class="text-right">{{ column_actual_amount }}</th>
                    <th class="text-right">{{ column_variance }}</th>
                    <th class="text-right">{{ column_variance_percentage }}</th>
                  </tr>
                </thead>
                <tbody>
                  {% for account in account_breakdown %}
                  <tr>
                    <td>{{ account.account_code }}</td>
                    <td>{{ account.account_name }}</td>
                    <td class="text-right">{{ account.budgeted_amount }}</td>
                    <td class="text-right">{{ account.actual_amount }}</td>
                    <td class="text-right {% if account.variance < 0 %}text-danger{% else %}text-success{% endif %}">
                      {{ account.variance }}
                    </td>
                    <td class="text-right">
                      <span class="label {% if account.variance_percentage > 10 %}label-danger{% elseif account.variance_percentage > 5 %}label-warning{% else %}label-success{% endif %}">
                        {{ account.variance_percentage }}%
                      </span>
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>

          <!-- تحليل الانحراف -->
          <div role="tabpanel" class="tab-pane" id="variance">
            <div style="margin-top: 15px;">
              <div class="row">
                <div class="col-md-6">
                  <canvas id="varianceChart" width="400" height="200"></canvas>
                </div>
                <div class="col-md-6">
                  <div class="table-responsive">
                    <table class="table table-bordered">
                      <thead>
                        <tr class="info">
                          <th>{{ column_variance_type }}</th>
                          <th class="text-right">{{ column_amount }}</th>
                          <th class="text-right">{{ column_percentage }}</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr class="success">
                          <td>{{ text_favorable_variance }}</td>
                          <td class="text-right">{{ variance_analysis.favorable_amount }}</td>
                          <td class="text-right">{{ variance_analysis.favorable_percentage }}%</td>
                        </tr>
                        <tr class="danger">
                          <td>{{ text_unfavorable_variance }}</td>
                          <td class="text-right">{{ variance_analysis.unfavorable_amount }}</td>
                          <td class="text-right">{{ variance_analysis.unfavorable_percentage }}%</td>
                        </tr>
                        <tr class="info">
                          <td><strong>{{ text_net_variance }}</strong></td>
                          <td class="text-right"><strong>{{ variance_analysis.net_variance }}</strong></td>
                          <td class="text-right"><strong>{{ variance_analysis.net_percentage }}%</strong></td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- السيناريوهات -->
          <div role="tabpanel" class="tab-pane" id="scenarios">
            <div class="table-responsive" style="margin-top: 15px;">
              <table class="table table-bordered table-hover">
                <thead>
                  <tr>
                    <th>{{ column_scenario_name }}</th>
                    <th>{{ column_scenario_type }}</th>
                    <th class="text-right">{{ column_total_amount }}</th>
                    <th class="text-right">{{ column_variance_from_base }}</th>
                    <th>{{ column_status }}</th>
                    <th>{{ column_action }}</th>
                  </tr>
                </thead>
                <tbody>
                  {% for scenario in scenarios %}
                  <tr>
                    <td>{{ scenario.scenario_name }}</td>
                    <td>
                      {% if scenario.scenario_type == 'optimistic' %}
                        <span class="label label-success">{{ text_optimistic }}</span>
                      {% elseif scenario.scenario_type == 'pessimistic' %}
                        <span class="label label-danger">{{ text_pessimistic }}</span>
                      {% else %}
                        <span class="label label-info">{{ text_realistic }}</span>
                      {% endif %}
                    </td>
                    <td class="text-right">{{ scenario.total_amount }}</td>
                    <td class="text-right {% if scenario.variance_from_base < 0 %}text-danger{% else %}text-success{% endif %}">
                      {{ scenario.variance_from_base }}
                    </td>
                    <td>
                      {% if scenario.status == 'active' %}
                        <span class="label label-success">{{ text_active }}</span>
                      {% else %}
                        <span class="label label-default">{{ text_inactive }}</span>
                      {% endif %}
                    </td>
                    <td>
                      <a href="{{ scenario.view }}" class="btn btn-xs btn-info">
                        <i class="fa fa-eye"></i> {{ button_view }}
                      </a>
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- تعليقات الموافقة -->
    {% if approval_comments %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-comments"></i> {{ text_approval_comments }}</h3>
      </div>
      <div class="panel-body">
        {% for comment in approval_comments %}
        <div class="media">
          <div class="media-left">
            <img src="{{ comment.user_avatar }}" class="media-object" style="width: 50px; height: 50px;" alt="{{ comment.user_name }}">
          </div>
          <div class="media-body">
            <h5 class="media-heading">
              {{ comment.user_name }} 
              <small class="text-muted">{{ comment.comment_date }}</small>
              {% if comment.action == 'approved' %}
                <span class="label label-success">{{ text_approved }}</span>
              {% elseif comment.action == 'rejected' %}
                <span class="label label-danger">{{ text_rejected }}</span>
              {% else %}
                <span class="label label-info">{{ text_comment }}</span>
              {% endif %}
            </h5>
            <p>{{ comment.comment|nl2br }}</p>
          </div>
        </div>
        <hr>
        {% endfor %}
      </div>
    </div>
    {% endif %}

  </div>
</div>

<script type="text/javascript">
$(document).ready(function() {
    // رسم بياني لتحليل الانحراف
    {% if variance_chart_data %}
    var ctx = document.getElementById('varianceChart').getContext('2d');
    var varianceChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['{{ text_favorable }}', '{{ text_unfavorable }}'],
            datasets: [{
                data: [{{ variance_chart_data.favorable }}, {{ variance_chart_data.unfavorable }}],
                backgroundColor: ['#5cb85c', '#d9534f']
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
    {% endif %}

    // تقديم الموازنة للموافقة
    $('#button-submit').on('click', function() {
        if (confirm('{{ text_confirm_submit }}')) {
            $.ajax({
                url: '{{ submit }}',
                type: 'post',
                dataType: 'json',
                success: function(json) {
                    if (json['success']) {
                        location.reload();
                    } else {
                        alert(json['error']);
                    }
                }
            });
        }
    });

    // موافقة على الموازنة
    $('#button-approve').on('click', function() {
        var comment = prompt('{{ text_approval_comment }}');
        if (comment !== null) {
            $.ajax({
                url: '{{ approve }}',
                type: 'post',
                data: {comment: comment},
                dataType: 'json',
                success: function(json) {
                    if (json['success']) {
                        location.reload();
                    } else {
                        alert(json['error']);
                    }
                }
            });
        }
    });

    // رفض الموازنة
    $('#button-reject').on('click', function() {
        var comment = prompt('{{ text_rejection_reason }}');
        if (comment !== null && comment !== '') {
            $.ajax({
                url: '{{ reject }}',
                type: 'post',
                data: {comment: comment},
                dataType: 'json',
                success: function(json) {
                    if (json['success']) {
                        location.reload();
                    } else {
                        alert(json['error']);
                    }
                }
            });
        }
    });

    // تصدير الموازنة
    $('#button-export').on('click', function() {
        window.open('{{ export }}', '_blank');
    });

    // تحرير الموازنة
    $('#button-edit').on('click', function() {
        window.location = '{{ edit }}';
    });
});
</script>

{{ footer }}
