{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <a href="{{ add }}" data-toggle="tooltip" title="{{ button_add }}" class="btn btn-primary">
          <i class="fa fa-plus"></i>
        </a>
        <a href="{{ generate_bulk }}" data-toggle="tooltip" title="{{ button_generate_bulk }}" class="btn btn-success">
          <i class="fa fa-magic"></i>
        </a>
        <a href="{{ scan_barcode }}" data-toggle="tooltip" title="{{ button_scan }}" class="btn btn-info">
          <i class="fa fa-qrcode"></i>
        </a>
        <div class="btn-group">
          <button type="button" class="btn btn-warning dropdown-toggle" data-toggle="dropdown">
            <i class="fa fa-print"></i> {{ button_print_labels }}
            <span class="caret"></span>
          </button>
          <ul class="dropdown-menu dropdown-menu-right">
            <li><a href="{{ print_labels }}"><i class="fa fa-print"></i> طباعة الملصقات</a></li>
            <li><a href="{{ export_excel }}"><i class="fa fa-file-excel-o"></i> {{ button_export_excel }}</a></li>
          </ul>
        </div>
        <button type="button" data-toggle="tooltip" title="{{ button_delete }}" class="btn btn-danger" onclick="confirm('{{ text_confirm }}') ? $('#form-barcode').submit() : false;">
          <i class="fa fa-trash-o"></i>
        </button>
        <a href="{{ refresh }}" data-toggle="tooltip" title="{{ button_refresh }}" class="btn btn-default">
          <i class="fa fa-refresh"></i>
        </a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible">
      <i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    
    {% if success %}
    <div class="alert alert-success alert-dismissible">
      <i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    
    <!-- إحصائيات الباركود -->
    <div class="row">
      <div class="col-lg-2 col-md-3 col-sm-6">
        <div class="panel panel-primary">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-qrcode fa-2x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ statistics.total_barcodes }}</div>
                <div>{{ text_total_barcodes }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-lg-2 col-md-3 col-sm-6">
        <div class="panel panel-success">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-check-circle fa-2x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ statistics.active_barcodes }}</div>
                <div>{{ text_active_barcodes }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-lg-2 col-md-3 col-sm-6">
        <div class="panel panel-info">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-star fa-2x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ statistics.primary_barcodes }}</div>
                <div>{{ text_primary_barcodes }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-lg-2 col-md-3 col-sm-6">
        <div class="panel panel-warning">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-magic fa-2x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ statistics.auto_generated_barcodes }}</div>
                <div>{{ text_auto_generated_barcodes }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-lg-2 col-md-3 col-sm-6">
        <div class="panel panel-green">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-search fa-2x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ statistics.total_scans }}</div>
                <div>{{ text_total_scans }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-lg-2 col-md-3 col-sm-6">
        <div class="panel panel-purple">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-print fa-2x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ statistics.total_prints }}</div>
                <div>{{ text_total_prints }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- إحصائيات حسب النوع -->
    {% if type_statistics %}
    <div class="row">
      <div class="col-md-6">
        <div class="panel panel-info">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-pie-chart"></i> {{ text_type_statistics }}</h3>
          </div>
          <div class="panel-body">
            <div class="table-responsive">
              <table class="table table-condensed">
                <thead>
                  <tr>
                    <th>النوع</th>
                    <th class="text-center">العدد</th>
                    <th class="text-center">المسح</th>
                    <th class="text-center">الطباعة</th>
                  </tr>
                </thead>
                <tbody>
                  {% for type in type_statistics %}
                  <tr>
                    <td>{{ type.barcode_type_text }}</td>
                    <td class="text-center"><span class="badge badge-primary">{{ type.count }}</span></td>
                    <td class="text-center"><span class="badge badge-success">{{ type.total_scans }}</span></td>
                    <td class="text-center"><span class="badge badge-warning">{{ type.total_prints }}</span></td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-md-6">
        <div class="panel panel-success">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-trophy"></i> {{ text_most_used_barcodes }}</h3>
          </div>
          <div class="panel-body">
            <div class="table-responsive">
              <table class="table table-condensed">
                <thead>
                  <tr>
                    <th>المنتج</th>
                    <th>الباركود</th>
                    <th class="text-center">الاستخدام</th>
                  </tr>
                </thead>
                <tbody>
                  {% for barcode in most_used_barcodes %}
                  <tr>
                    <td>
                      <strong>{{ barcode.product_name }}</strong>
                      {% if barcode.unit_name %}
                      <br><small class="text-muted">{{ barcode.unit_name }}</small>
                      {% endif %}
                    </td>
                    <td>
                      <code>{{ barcode.barcode_value }}</code>
                      <br><small class="text-muted">{{ barcode.barcode_type }}</small>
                    </td>
                    <td class="text-center">
                      <span class="badge badge-info">{{ barcode.total_usage }}</span>
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
    {% endif %}
    
    <!-- الفلاتر المتقدمة -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title">
          <i class="fa fa-filter"></i> فلاتر البحث
          <button type="button" class="btn btn-xs btn-default pull-right" data-toggle="collapse" data-target="#filter-panel">
            <i class="fa fa-chevron-down"></i>
          </button>
        </h3>
      </div>
      <div id="filter-panel" class="panel-collapse collapse">
        <div class="panel-body">
          <form method="get" id="filter-form">
            <input type="hidden" name="route" value="inventory/barcode_management" />
            <input type="hidden" name="user_token" value="{{ user_token }}" />
            
            <div class="row">
              <div class="col-md-3">
                <div class="form-group">
                  <label for="filter_product_name">{{ entry_filter_product_name }}</label>
                  <input type="text" name="filter_product_name" value="{{ filter_product_name }}" placeholder="{{ entry_filter_product_name }}" id="filter_product_name" class="form-control" />
                </div>
              </div>
              
              <div class="col-md-3">
                <div class="form-group">
                  <label for="filter_barcode_value">{{ entry_filter_barcode_value }}</label>
                  <input type="text" name="filter_barcode_value" value="{{ filter_barcode_value }}" placeholder="{{ entry_filter_barcode_value }}" id="filter_barcode_value" class="form-control" />
                </div>
              </div>
              
              <div class="col-md-3">
                <div class="form-group">
                  <label for="filter_barcode_type">{{ entry_filter_barcode_type }}</label>
                  <select name="filter_barcode_type" id="filter_barcode_type" class="form-control">
                    <option value="">{{ text_all }}</option>
                    {% for type in barcode_types %}
                    <option value="{{ type.value }}"{% if type.value == filter_barcode_type %} selected="selected"{% endif %}>{{ type.text }}</option>
                    {% endfor %}
                  </select>
                </div>
              </div>
              
              <div class="col-md-3">
                <div class="form-group">
                  <label for="filter_is_active">{{ entry_filter_is_active }}</label>
                  <select name="filter_is_active" id="filter_is_active" class="form-control">
                    {% for option in status_options %}
                    <option value="{{ option.value }}"{% if option.value == filter_is_active %} selected="selected"{% endif %}>{{ option.text }}</option>
                    {% endfor %}
                  </select>
                </div>
              </div>
            </div>
            
            <div class="row">
              <div class="col-md-3">
                <div class="form-group">
                  <label for="filter_is_primary">{{ entry_filter_is_primary }}</label>
                  <select name="filter_is_primary" id="filter_is_primary" class="form-control">
                    {% for option in primary_options %}
                    <option value="{{ option.value }}"{% if option.value == filter_is_primary %} selected="selected"{% endif %}>{{ option.text }}</option>
                    {% endfor %}
                  </select>
                </div>
              </div>
              
              <div class="col-md-3">
                <div class="form-group">
                  <label for="filter_date_from">{{ entry_filter_date_from }}</label>
                  <input type="date" name="filter_date_from" value="{{ filter_date_from }}" id="filter_date_from" class="form-control" />
                </div>
              </div>
              
              <div class="col-md-3">
                <div class="form-group">
                  <label for="filter_date_to">{{ entry_filter_date_to }}</label>
                  <input type="date" name="filter_date_to" value="{{ filter_date_to }}" id="filter_date_to" class="form-control" />
                </div>
              </div>
              
              <div class="col-md-3">
                <div class="form-group">
                  <label>&nbsp;</label>
                  <div>
                    <button type="submit" class="btn btn-primary"><i class="fa fa-filter"></i> {{ button_filter }}</button>
                    <a href="{{ refresh }}" class="btn btn-default"><i class="fa fa-refresh"></i> {{ button_clear }}</a>
                  </div>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
    
    <!-- جدول الباركودات -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-list"></i> {{ text_list }}</h3>
      </div>
      <div class="panel-body">
        <form action="{{ delete }}" method="post" enctype="multipart/form-data" id="form-barcode">
          <div class="table-responsive">
            <table class="table table-bordered table-hover">
              <thead>
                <tr>
                  <td style="width: 1px;" class="text-center">
                    <input type="checkbox" onclick="$('input[name*=\'selected\']').prop('checked', this.checked);" />
                  </td>
                  <td>{{ column_product_name }}</td>
                  <td>{{ column_barcode_value }}</td>
                  <td>{{ column_barcode_type }}</td>
                  <td class="text-center">{{ column_barcode_category }}</td>
                  <td class="text-center">{{ column_is_active }}</td>
                  <td class="text-center">{{ column_scan_count }}</td>
                  <td class="text-center">{{ column_print_count }}</td>
                  <td>{{ column_date_added }}</td>
                  <td class="text-center">{{ column_action }}</td>
                </tr>
              </thead>
              <tbody>
                {% if barcodes %}
                {% for barcode in barcodes %}
                <tr class="barcode-{{ barcode.status_class }}">
                  <td class="text-center">
                    <input type="checkbox" name="selected[]" value="{{ barcode.barcode_id }}" />
                  </td>
                  <td>
                    <strong>{{ barcode.product_name }}</strong>
                    {% if barcode.model %}
                    <br><small class="text-muted">{{ barcode.model }}</small>
                    {% endif %}
                    {% if barcode.unit_name %}
                    <br><small class="text-info">{{ barcode.unit_name }}</small>
                    {% endif %}
                    {% if barcode.option_name %}
                    <br><small class="text-warning">{{ barcode.option_name }}</small>
                    {% endif %}
                  </td>
                  <td>
                    <code class="barcode-value">{{ barcode.barcode_value }}</code>
                    {% if barcode.is_primary %}
                    <br><span class="label label-success">أساسي</span>
                    {% endif %}
                  </td>
                  <td>
                    <span class="label label-info">{{ barcode.barcode_type_text }}</span>
                  </td>
                  <td class="text-center">
                    <span class="label label-{{ barcode.category_class }}">{{ barcode.barcode_category }}</span>
                  </td>
                  <td class="text-center">
                    <span class="label label-{{ barcode.is_active ? 'success' : 'danger' }}">{{ barcode.is_active_text }}</span>
                  </td>
                  <td class="text-center">
                    <span class="badge badge-{{ barcode.usage_class }}">{{ barcode.scan_count }}</span>
                    {% if barcode.today_scans > 0 %}
                    <br><small class="text-success">+{{ barcode.today_scans }} اليوم</small>
                    {% endif %}
                  </td>
                  <td class="text-center">
                    <span class="badge badge-{{ barcode.usage_class }}">{{ barcode.print_count }}</span>
                    {% if barcode.today_prints > 0 %}
                    <br><small class="text-warning">+{{ barcode.today_prints }} اليوم</small>
                    {% endif %}
                  </td>
                  <td>
                    {{ barcode.date_added }}
                    {% if barcode.last_scanned != 'أبداً' %}
                    <br><small class="text-muted">آخر مسح: {{ barcode.last_scanned }}</small>
                    {% endif %}
                  </td>
                  <td class="text-center">
                    <div class="btn-group">
                      <a href="{{ barcode.view }}" data-toggle="tooltip" title="{{ button_view }}" class="btn btn-info btn-xs">
                        <i class="fa fa-eye"></i>
                      </a>
                      <a href="{{ barcode.edit }}" data-toggle="tooltip" title="{{ button_edit }}" class="btn btn-primary btn-xs">
                        <i class="fa fa-pencil"></i>
                      </a>
                      <a href="{{ barcode.print }}" data-toggle="tooltip" title="{{ button_print }}" class="btn btn-warning btn-xs">
                        <i class="fa fa-print"></i>
                      </a>
                      <a href="{{ barcode.duplicate }}" data-toggle="tooltip" title="{{ button_duplicate }}" class="btn btn-success btn-xs">
                        <i class="fa fa-copy"></i>
                      </a>
                    </div>
                  </td>
                </tr>
                {% endfor %}
                {% else %}
                <tr>
                  <td class="text-center" colspan="10">{{ text_no_results }}</td>
                </tr>
                {% endif %}
              </tbody>
            </table>
          </div>
        </form>
        
        <div class="row">
          <div class="col-sm-6 text-left">{{ pagination }}</div>
          <div class="col-sm-6 text-right">{{ results }}</div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
.huge { font-size: 28px; }
.panel-green { border-color: #5cb85c; }
.panel-green > .panel-heading { border-color: #5cb85c; color: white; background-color: #5cb85c; }
.panel-purple { border-color: #9b59b6; }
.panel-purple > .panel-heading { border-color: #9b59b6; color: white; background-color: #9b59b6; }
.barcode-value { font-family: 'Courier New', monospace; font-size: 12px; }
.badge-primary { background-color: #337ab7; }
.badge-success { background-color: #5cb85c; }
.badge-warning { background-color: #f0ad4e; }
.badge-info { background-color: #5bc0de; }
</style>

<script type="text/javascript">
$(document).ready(function() {
    $('[data-toggle="tooltip"]').tooltip();
    
    // مسح سريع للباركود
    $('#filter_barcode_value').on('keyup', function(e) {
        if (e.keyCode === 13) { // Enter key
            $('#filter-form').submit();
        }
    });
    
    // تحديث تلقائي للإحصائيات
    setInterval(function() {
        if ($('.badge').length > 0) {
            // تحديث الإحصائيات كل 5 دقائق
            location.reload();
        }
    }, 300000);
});
</script>

{{ footer }}
