{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="submit" form="logging\audit_trail-form" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fas fa-save"></i></button>
        <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-light"><i class="fas fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    
    <div class="card">
      <div class="card-header"><i class="fas fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}chart-bar{% elif screen_type == 'settings' %}cog{% else %}pencil-alt{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="card-body">
        <form id="logging\audit_trail-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-audit_info">{{ text_audit_info }}</label>
            <div class="col-sm-10">
              <input type="text" name="audit_info" value="{{ audit_info }}" placeholder="{{ text_audit_info }}" id="input-audit_info" class="form-control" />
              {% if error_audit_info %}
                <div class="invalid-feedback">{{ error_audit_info }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-audit_logs">{{ text_audit_logs }}</label>
            <div class="col-sm-10">
              <input type="text" name="audit_logs" value="{{ audit_logs }}" placeholder="{{ text_audit_logs }}" id="input-audit_logs" class="form-control" />
              {% if error_audit_logs %}
                <div class="invalid-feedback">{{ error_audit_logs }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-audit_stats">{{ text_audit_stats }}</label>
            <div class="col-sm-10">
              <input type="text" name="audit_stats" value="{{ audit_stats }}" placeholder="{{ text_audit_stats }}" id="input-audit_stats" class="form-control" />
              {% if error_audit_stats %}
                <div class="invalid-feedback">{{ error_audit_stats }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-back">{{ text_back }}</label>
            <div class="col-sm-10">
              <input type="text" name="back" value="{{ back }}" placeholder="{{ text_back }}" id="input-back" class="form-control" />
              {% if error_back %}
                <div class="invalid-feedback">{{ error_back }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-catalog_integrity">{{ text_catalog_integrity }}</label>
            <div class="col-sm-10">
              <input type="text" name="catalog_integrity" value="{{ catalog_integrity }}" placeholder="{{ text_catalog_integrity }}" id="input-catalog_integrity" class="form-control" />
              {% if error_catalog_integrity %}
                <div class="invalid-feedback">{{ error_catalog_integrity }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-change_analysis">{{ text_change_analysis }}</label>
            <div class="col-sm-10">
              <input type="text" name="change_analysis" value="{{ change_analysis }}" placeholder="{{ text_change_analysis }}" id="input-change_analysis" class="form-control" />
              {% if error_change_analysis %}
                <div class="invalid-feedback">{{ error_change_analysis }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-change_impact">{{ text_change_impact }}</label>
            <div class="col-sm-10">
              <input type="text" name="change_impact" value="{{ change_impact }}" placeholder="{{ text_change_impact }}" id="input-change_impact" class="form-control" />
              {% if error_change_impact %}
                <div class="invalid-feedback">{{ error_change_impact }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-compliance_analysis">{{ text_compliance_analysis }}</label>
            <div class="col-sm-10">
              <input type="text" name="compliance_analysis" value="{{ compliance_analysis }}" placeholder="{{ text_compliance_analysis }}" id="input-compliance_analysis" class="form-control" />
              {% if error_compliance_analysis %}
                <div class="invalid-feedback">{{ error_compliance_analysis }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-compliance_report">{{ text_compliance_report }}</label>
            <div class="col-sm-10">
              <input type="text" name="compliance_report" value="{{ compliance_report }}" placeholder="{{ text_compliance_report }}" id="input-compliance_report" class="form-control" />
              {% if error_compliance_report %}
                <div class="invalid-feedback">{{ error_compliance_report }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-compliance_reports">{{ text_compliance_reports }}</label>
            <div class="col-sm-10">
              <input type="text" name="compliance_reports" value="{{ compliance_reports }}" placeholder="{{ text_compliance_reports }}" id="input-compliance_reports" class="form-control" />
              {% if error_compliance_reports %}
                <div class="invalid-feedback">{{ error_compliance_reports }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-critical_changes">{{ text_critical_changes }}</label>
            <div class="col-sm-10">
              <input type="text" name="critical_changes" value="{{ critical_changes }}" placeholder="{{ text_critical_changes }}" id="input-critical_changes" class="form-control" />
              {% if error_critical_changes %}
                <div class="invalid-feedback">{{ error_critical_changes }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-export">{{ text_export }}</label>
            <div class="col-sm-10">
              <input type="text" name="export" value="{{ export }}" placeholder="{{ text_export }}" id="input-export" class="form-control" />
              {% if error_export %}
                <div class="invalid-feedback">{{ error_export }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-export_compliance">{{ text_export_compliance }}</label>
            <div class="col-sm-10">
              <input type="text" name="export_compliance" value="{{ export_compliance }}" placeholder="{{ text_export_compliance }}" id="input-export_compliance" class="form-control" />
              {% if error_export_compliance %}
                <div class="invalid-feedback">{{ error_export_compliance }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-export_report">{{ text_export_report }}</label>
            <div class="col-sm-10">
              <input type="text" name="export_report" value="{{ export_report }}" placeholder="{{ text_export_report }}" id="input-export_report" class="form-control" />
              {% if error_export_report %}
                <div class="invalid-feedback">{{ error_export_report }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="invalid-feedback">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-improvement_recommendations">{{ text_improvement_recommendations }}</label>
            <div class="col-sm-10">
              <input type="text" name="improvement_recommendations" value="{{ improvement_recommendations }}" placeholder="{{ text_improvement_recommendations }}" id="input-improvement_recommendations" class="form-control" />
              {% if error_improvement_recommendations %}
                <div class="invalid-feedback">{{ error_improvement_recommendations }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-integrity_check">{{ text_integrity_check }}</label>
            <div class="col-sm-10">
              <input type="text" name="integrity_check" value="{{ integrity_check }}" placeholder="{{ text_integrity_check }}" id="input-integrity_check" class="form-control" />
              {% if error_integrity_check %}
                <div class="invalid-feedback">{{ error_integrity_check }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-integrity_results">{{ text_integrity_results }}</label>
            <div class="col-sm-10">
              <input type="text" name="integrity_results" value="{{ integrity_results }}" placeholder="{{ text_integrity_results }}" id="input-integrity_results" class="form-control" />
              {% if error_integrity_results %}
                <div class="invalid-feedback">{{ error_integrity_results }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-inventory_integrity">{{ text_inventory_integrity }}</label>
            <div class="col-sm-10">
              <input type="text" name="inventory_integrity" value="{{ inventory_integrity }}" placeholder="{{ text_inventory_integrity }}" id="input-inventory_integrity" class="form-control" />
              {% if error_inventory_integrity %}
                <div class="invalid-feedback">{{ error_inventory_integrity }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-monitored_tables">{{ text_monitored_tables }}</label>
            <div class="col-sm-10">
              <input type="text" name="monitored_tables" value="{{ monitored_tables }}" placeholder="{{ text_monitored_tables }}" id="input-monitored_tables" class="form-control" />
              {% if error_monitored_tables %}
                <div class="invalid-feedback">{{ error_monitored_tables }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-operation_types">{{ text_operation_types }}</label>
            <div class="col-sm-10">
              <input type="text" name="operation_types" value="{{ operation_types }}" placeholder="{{ text_operation_types }}" id="input-operation_types" class="form-control" />
              {% if error_operation_types %}
                <div class="invalid-feedback">{{ error_operation_types }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-related_changes">{{ text_related_changes }}</label>
            <div class="col-sm-10">
              <input type="text" name="related_changes" value="{{ related_changes }}" placeholder="{{ text_related_changes }}" id="input-related_changes" class="form-control" />
              {% if error_related_changes %}
                <div class="invalid-feedback">{{ error_related_changes }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-revert">{{ text_revert }}</label>
            <div class="col-sm-10">
              <input type="text" name="revert" value="{{ revert }}" placeholder="{{ text_revert }}" id="input-revert" class="form-control" />
              {% if error_revert %}
                <div class="invalid-feedback">{{ error_revert }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-risk_analysis">{{ text_risk_analysis }}</label>
            <div class="col-sm-10">
              <input type="text" name="risk_analysis" value="{{ risk_analysis }}" placeholder="{{ text_risk_analysis }}" id="input-risk_analysis" class="form-control" />
              {% if error_risk_analysis %}
                <div class="invalid-feedback">{{ error_risk_analysis }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-risk_assessment">{{ text_risk_assessment }}</label>
            <div class="col-sm-10">
              <input type="text" name="risk_assessment" value="{{ risk_assessment }}" placeholder="{{ text_risk_assessment }}" id="input-risk_assessment" class="form-control" />
              {% if error_risk_assessment %}
                <div class="invalid-feedback">{{ error_risk_assessment }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-settings">{{ text_settings }}</label>
            <div class="col-sm-10">
              <input type="text" name="settings" value="{{ settings }}" placeholder="{{ text_settings }}" id="input-settings" class="form-control" />
              {% if error_settings %}
                <div class="invalid-feedback">{{ error_settings }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-total">{{ text_total }}</label>
            <div class="col-sm-10">
              <input type="text" name="total" value="{{ total }}" placeholder="{{ text_total }}" id="input-total" class="form-control" />
              {% if error_total %}
                <div class="invalid-feedback">{{ error_total }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="invalid-feedback">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-users">{{ text_users }}</label>
            <div class="col-sm-10">
              <input type="text" name="users" value="{{ users }}" placeholder="{{ text_users }}" id="input-users" class="form-control" />
              {% if error_users %}
                <div class="invalid-feedback">{{ error_users }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}