{{ header }}
{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="button" id="refresh-data" class="btn btn-info"><i class="fa fa-refresh"></i> {{ button_refresh }}</button>
        {% if can_create_coupon %}
        <button type="button" data-toggle="modal" data-target="#modal-bulk-coupon" class="btn btn-success"><i class="fa fa-gift"></i> {{ button_create_coupons }}</button>
        {% endif %}
        {% if can_send_email %}
        <button type="button" data-toggle="modal" data-target="#modal-bulk-email" class="btn btn-primary"><i class="fa fa-envelope"></i> {{ button_send_emails }}</button>
        {% endif %}
        {% if can_delete %}
        <button type="button" id="button-delete" class="btn btn-danger"><i class="fa fa-trash"></i> {{ button_delete }}</button>
        {% endif %}
      </div>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    {% if success %}
    <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    
    <!-- Statistics Panels -->
    <div class="row">
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-primary">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-shopping-cart fa-5x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ stats.total_abandoned_carts }}</div>
                <div>{{ text_total_abandoned_carts }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-green">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-money fa-5x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ stats.total_value }}</div>
                <div>{{ text_potential_revenue }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-yellow">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-refresh fa-5x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ stats.recovery_rate }}</div>
                <div>{{ text_recovery_rate }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-red">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-credit-card fa-5x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ stats.average_value }}</div>
                <div>{{ text_average_cart_value }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Chart Section -->
    <div class="row">
      <div class="col-md-12">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-bar-chart"></i> {{ text_abandoned_carts_over_time }}</h3>
          </div>
          <div class="panel-body">
            <canvas id="abandoned-carts-chart" height="100"></canvas>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Filter Panel -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-filter"></i> {{ text_filter }}</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-sm-4">
            <div class="form-group">
              <label class="control-label" for="input-customer">{{ entry_customer }}</label>
              <input type="text" name="filter_customer" value="{{ filter_customer }}" placeholder="{{ entry_customer }}" id="input-customer" class="form-control" />
            </div>
          </div>
          <div class="col-sm-4">
            <div class="form-group">
              <label class="control-label" for="input-date-start">{{ entry_date_start }}</label>
              <div class="input-group date">
                <input type="text" name="filter_date_start" value="{{ filter_date_start }}" placeholder="{{ entry_date_start }}" data-date-format="YYYY-MM-DD" id="input-date-start" class="form-control" />
                <span class="input-group-btn">
                  <button type="button" class="btn btn-default"><i class="fa fa-calendar"></i></button>
                </span>
              </div>
            </div>
          </div>
          <div class="col-sm-4">
            <div class="form-group">
              <label class="control-label" for="input-date-end">{{ entry_date_end }}</label>
              <div class="input-group date">
                <input type="text" name="filter_date_end" value="{{ filter_date_end }}" placeholder="{{ entry_date_end }}" data-date-format="YYYY-MM-DD" id="input-date-end" class="form-control" />
                <span class="input-group-btn">
                  <button type="button" class="btn btn-default"><i class="fa fa-calendar"></i></button>
                </span>
              </div>
            </div>
          </div>
        </div>
        <div class="row">
<div class="col-sm-3">
            <div class="form-group">
              <label class="control-label" for="input-status">{{ entry_status }}</label>
              <select name="filter_status" id="input-status" class="form-control">
                {% for key, value in statuses %}
                  {% if key == filter_status %}
                    <option value="{{ key }}" selected="selected">{{ value }}</option>
                  {% else %}
                    <option value="{{ key }}">{{ value }}</option>
                  {% endif %}
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="col-sm-3">
            <div class="form-group">
              <label class="control-label" for="input-total-min">{{ entry_total_min }}</label>
              <input type="text" name="filter_total_min" value="{{ filter_total_min }}" placeholder="{{ entry_total_min }}" id="input-total-min" class="form-control" />
            </div>
          </div>
          <div class="col-sm-3">
            <div class="form-group">
              <label class="control-label" for="input-total-max">{{ entry_total_max }}</label>
              <input type="text" name="filter_total_max" value="{{ filter_total_max }}" placeholder="{{ entry_total_max }}" id="input-total-max" class="form-control" />
            </div>
          </div>
          <div class="col-sm-3">
            <div class="form-group">
              <label class="control-label" for="input-email-sent">{{ entry_email_sent }}</label>
              <select name="filter_email_sent" id="input-email-sent" class="form-control">
                {% for key, value in email_sent_options %}
                  {% if key == filter_email_sent %}
                    <option value="{{ key }}" selected="selected">{{ value }}</option>
                  {% else %}
                    <option value="{{ key }}">{{ value }}</option>
                  {% endif %}
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="col-sm-12 text-right">
            <button type="button" id="button-filter" class="btn btn-primary"><i class="fa fa-search"></i> {{ button_filter }}</button>
            <button type="button" id="button-clear-filter" class="btn btn-default"><i class="fa fa-eraser"></i> {{ button_clear }}</button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Abandoned Carts List -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-list"></i> {{ text_list }}</h3>
      </div>
      <div class="panel-body">
        <form method="post" action="{{ delete }}" enctype="multipart/form-data" id="form-abandoned-cart">
          <div class="table-responsive">
            <table class="table table-bordered table-hover">
              <thead>
                <tr>
                  <td style="width: 1px;" class="text-center"><input type="checkbox" onclick="$('input[name*=\'selected\']').prop('checked', this.checked);" /></td>
                  <td class="text-left">{% if sort == 'customer_id' %}<a href="{{ sort_customer }}" class="{{ order|lower }}">{{ column_customer }}</a>{% else %}<a href="{{ sort_customer }}">{{ column_customer }}</a>{% endif %}</td>
                  <td class="text-left">{{ column_contact }}</td>
                  <td class="text-left">{% if sort == 'date_created' %}<a href="{{ sort_date }}" class="{{ order|lower }}">{{ column_date_created }}</a>{% else %}<a href="{{ sort_date }}">{{ column_date_created }}</a>{% endif %}</td>
                  <td class="text-left">{% if sort == 'last_activity' %}<a href="{{ sort_activity }}" class="{{ order|lower }}">{{ column_last_activity }}</a>{% else %}<a href="{{ sort_activity }}">{{ column_last_activity }}</a>{% endif %}</td>
                  <td class="text-center">{% if sort == 'items_count' %}<a href="{{ sort_items }}" class="{{ order|lower }}">{{ column_items }}</a>{% else %}<a href="{{ sort_items }}">{{ column_items }}</a>{% endif %}</td>
                  <td class="text-right">{% if sort == 'total_value' %}<a href="{{ sort_total }}" class="{{ order|lower }}">{{ column_total }}</a>{% else %}<a href="{{ sort_total }}">{{ column_total }}</a>{% endif %}</td>
                  <td class="text-center">{{ column_email_sent }}</td>
                  <td class="text-center">{% if sort == 'status' %}<a href="{{ sort_status }}" class="{{ order|lower }}">{{ column_status }}</a>{% else %}<a href="{{ sort_status }}">{{ column_status }}</a>{% endif %}</td>
                  <td class="text-right">{{ column_action }}</td>
                </tr>
              </thead>
              <tbody>
                {% if abandoned_carts %}
                {% for cart in abandoned_carts %}
                <tr>
                  <td class="text-center">
                    {% if cart.cart_id in selected %}
                    <input type="checkbox" name="selected[]" value="{{ cart.cart_id }}" checked="checked" />
                    {% else %}
                    <input type="checkbox" name="selected[]" value="{{ cart.cart_id }}" />
                    {% endif %}
                  </td>
                  <td class="text-left">
                    {% if cart.customer_id %}
                    <a href="{{ cart.view }}" data-toggle="tooltip" title="{{ button_view }}">{{ cart.customer_name }}</a>
                    {% else %}
                    {{ text_guest }}
                    {% endif %}
                  </td>
                  <td class="text-left">
                    {% if cart.email %}
                    <span data-toggle="tooltip" title="{{ text_email }}"><i class="fa fa-envelope-o"></i> {{ cart.email }}</span><br />
                    {% endif %}
                    {% if cart.telephone %}
                    <span data-toggle="tooltip" title="{{ text_telephone }}"><i class="fa fa-phone"></i> {{ cart.telephone }}</span>
                    {% endif %}
                  </td>
                  <td class="text-left">{{ cart.date_created }}</td>
                  <td class="text-left">{{ cart.last_activity }}</td>
                  <td class="text-center">{{ cart.items_count }}</td>
                  <td class="text-right">{{ cart.total_value }}</td>
                  <td class="text-center">
                    {% if cart.recovery_email_sent %}
                    <span class="label label-success"><i class="fa fa-check"></i></span>
                    {% else %}
                    <span class="label label-danger"><i class="fa fa-times"></i></span>
                    {% endif %}
                  </td>
                  <td class="text-center">
                    {% if cart.status == 'active' %}
                    <span class="label label-warning">{{ cart.status_text }}</span>
                    {% elseif cart.status == 'recovered' %}
                    <span class="label label-success">{{ cart.status_text }}</span>
                    {% else %}
                    <span class="label label-default">{{ cart.status_text }}</span>
                    {% endif %}
                  </td>
                  <td class="text-right">
                    <div class="btn-group" role="group">
                      <button type="button" class="btn btn-primary btn-sm dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        {{ text_actions }} <span class="caret"></span>
                      </button>
                      <ul class="dropdown-menu dropdown-menu-right">
                        <li><a href="{{ cart.view }}" data-toggle="tooltip" title="{{ button_view }}"><i class="fa fa-eye"></i> {{ button_view }}</a></li>
                        {% if can_send_email %}
                        <li><a href="javascript:void(0);" onclick="sendEmail('{{ cart.cart_id }}')"><i class="fa fa-envelope"></i> {{ button_send_email }}</a></li>
                        {% endif %}
                        {% if can_send_sms %}
                        <li><a href="javascript:void(0);" onclick="sendSMS('{{ cart.cart_id }}')"><i class="fa fa-mobile"></i> {{ button_send_sms }}</a></li>
                        {% endif %}
                        {% if can_create_coupon %}
                        <li><a href="javascript:void(0);" onclick="createCoupon('{{ cart.cart_id }}')"><i class="fa fa-gift"></i> {{ button_create_coupon }}</a></li>
                        {% endif %}
                        {% if cart.telephone %}
                        <li><a href="javascript:void(0);" onclick="sendWhatsApp('{{ cart.cart_id }}')"><i class="fa fa-whatsapp"></i> {{ button_send_whatsapp }}</a></li>
                        <li><a href="javascript:void(0);" onclick="sendTelegram('{{ cart.cart_id }}')"><i class="fa fa-telegram"></i> {{ button_send_telegram }}</a></li>
                        {% endif %}
                        {% if can_delete %}
                        <li role="separator" class="divider"></li>
                        <li><a href="{{ cart.delete }}" class="text-danger" onclick="return confirm('{{ text_confirm_delete }}');"><i class="fa fa-trash-o"></i> {{ button_delete }}</a></li>
                        {% endif %}
                      </ul>
                    </div>
                  </td>
                </tr>
                {% endfor %}
                {% else %}
                <tr>
                  <td class="text-center" colspan="10">{{ text_no_results }}</td>
                </tr>
                {% endif %}
              </tbody>
            </table>
          </div>
        </form>
        <div class="row">
          <div class="col-sm-6 text-left">{{ pagination }}</div>
          <div class="col-sm-6 text-right">{{ results }}</div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Modal: Send Email -->
<div class="modal fade" id="modal-email" tabindex="-1" role="dialog" aria-labelledby="modal-email-label">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
        <h4 class="modal-title" id="modal-email-label">{{ text_send_email }}</h4>
      </div>
      <div class="modal-body">
        <form id="form-email">
          <input type="hidden" name="cart_id" id="email-cart-id" value="" />
          
          <div class="form-group">
            <label for="email-template">{{ entry_template }}</label>
            <select name="email_template" id="email-template" class="form-control">
              <option value="default">{{ text_default_template }}</option>
              <option value="custom">{{ text_custom_message }}</option>
              <!-- Templates will be loaded dynamically -->
            </select>
          </div>
          
          <div class="form-group" id="custom-email-container" style="display: none;">
            <label for="email-custom-message">{{ entry_custom_message }}</label>
            <textarea name="custom_message" id="email-custom-message" rows="10" class="form-control"></textarea>
            <p class="help-block">{{ help_placeholders }}</p>
          </div>
          
          <div class="form-group">
            <div class="checkbox">
              <label>
                <input type="checkbox" name="include_coupon" value="1" />
                {{ entry_include_coupon }}
              </label>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_cancel }}</button>
        <button type="button" class="btn btn-primary" id="button-send-email">{{ button_send }}</button>
      </div>
    </div>
  </div>
</div>

<!-- Modal: Send SMS -->
<div class="modal fade" id="modal-sms" tabindex="-1" role="dialog" aria-labelledby="modal-sms-label">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
        <h4 class="modal-title" id="modal-sms-label">{{ text_send_sms }}</h4>
      </div>
      <div class="modal-body">
        <form id="form-sms">
          <input type="hidden" name="cart_id" id="sms-cart-id" value="" />
          
          <div class="form-group">
            <label for="sms-template">{{ entry_template }}</label>
            <select name="sms_template" id="sms-template" class="form-control">
              <option value="default">{{ text_default_template }}</option>
              <option value="custom">{{ text_custom_message }}</option>
              <!-- Templates will be loaded dynamically -->
            </select>
          </div>
          
          <div class="form-group" id="custom-sms-container" style="display: none;">
            <label for="sms-custom-message">{{ entry_custom_message }}</label>
            <textarea name="custom_message" id="sms-custom-message" rows="5" class="form-control"></textarea>
            <p class="help-block">{{ help_placeholders }}</p>
          </div>
          
          <div class="form-group">
            <div class="checkbox">
              <label>
                <input type="checkbox" name="include_coupon" value="1" />
                {{ entry_include_coupon }}
              </label>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_cancel }}</button>
        <button type="button" class="btn btn-primary" id="button-send-sms">{{ button_send }}</button>
      </div>
    </div>
  </div>
</div>

<!-- Modal: Send WhatsApp -->
<div class="modal fade" id="modal-whatsapp" tabindex="-1" role="dialog" aria-labelledby="modal-whatsapp-label">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
        <h4 class="modal-title" id="modal-whatsapp-label">{{ text_send_whatsapp }}</h4>
      </div>
      <div class="modal-body">
        <form id="form-whatsapp">
          <input type="hidden" name="cart_id" id="whatsapp-cart-id" value="" />
          
          <div class="form-group">
            <label for="whatsapp-template">{{ entry_template }}</label>
            <select name="whatsapp_template" id="whatsapp-template" class="form-control">
              <option value="default">{{ text_default_template }}</option>
              <option value="custom">{{ text_custom_message }}</option>
              <!-- Templates will be loaded dynamically -->
            </select>
          </div>
          
          <div class="form-group" id="custom-whatsapp-container" style="display: none;">
            <label for="whatsapp-custom-message">{{ entry_custom_message }}</label>
            <textarea name="custom_message" id="whatsapp-custom-message" rows="5" class="form-control"></textarea>
            <p class="help-block">{{ help_placeholders }}</p>
          </div>
          
          <div class="form-group">
            <div class="checkbox">
              <label>
                <input type="checkbox" name="include_coupon" value="1" />
                {{ entry_include_coupon }}
              </label>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_cancel }}</button>
        <button type="button" class="btn btn-primary" id="button-send-whatsapp">{{ button_send }}</button>
      </div>
    </div>
  </div>
</div>

<!-- Modal: Send Telegram -->
<div class="modal fade" id="modal-telegram" tabindex="-1" role="dialog" aria-labelledby="modal-telegram-label">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
        <h4 class="modal-title" id="modal-telegram-label">{{ text_send_telegram }}</h4>
      </div>
      <div class="modal-body">
        <form id="form-telegram">
          <input type="hidden" name="cart_id" id="telegram-cart-id" value="" />
          
          <div class="form-group">
            <label for="telegram-template">{{ entry_template }}</label>
            <select name="telegram_template" id="telegram-template" class="form-control">
              <option value="default">{{ text_default_template }}</option>
              <option value="custom">{{ text_custom_message }}</option>
              <!-- Templates will be loaded dynamically -->
            </select>
          </div>
          
          <div class="form-group" id="custom-telegram-container" style="display: none;">
            <label for="telegram-custom-message">{{ entry_custom_message }}</label>
            <textarea name="custom_message" id="telegram-custom-message" rows="5" class="form-control"></textarea>
            <p class="help-block">{{ help_placeholders }}</p>
          </div>
          
          <div class="form-group">
            <div class="checkbox">
              <label>
                <input type="checkbox" name="include_coupon" value="1" />
                {{ entry_include_coupon }}
              </label>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_cancel }}</button>
        <button type="button" class="btn btn-primary" id="button-send-telegram">{{ button_send }}</button>
      </div>
    </div>
  </div>
</div>

<!-- Modal: Bulk Email -->
<div class="modal fade" id="modal-bulk-email" tabindex="-1" role="dialog" aria-labelledby="modal-bulk-email-label">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
        <h4 class="modal-title" id="modal-bulk-email-label">{{ text_bulk_email }}</h4>
      </div>
      <div class="modal-body">
        <form id="form-bulk-email">
          <div class="form-group">
            <label for="bulk-email-template">{{ entry_template }}</label>
            <select name="email_template" id="bulk-email-template" class="form-control">
              <option value="default">{{ text_default_template }}</option>
              <option value="custom">{{ text_custom_message }}</option>
              <!-- Templates will be loaded dynamically -->
            </select>
          </div>
          
          <div class="form-group" id="bulk-custom-email-container" style="display: none;">
            <label for="bulk-email-custom-message">{{ entry_custom_message }}</label>
            <textarea name="custom_message" id="bulk-email-custom-message" rows="10" class="form-control"></textarea>
            <p class="help-block">{{ help_placeholders }}</p>
          </div>
          
          <div class="form-group">
            <div class="checkbox">
              <label>
                <input type="checkbox" name="include_coupon" value="1" />
                {{ entry_include_coupon }}
              </label>
            </div>
          </div>
          
          <div class="alert alert-info">
            <i class="fa fa-info-circle"></i> {{ text_bulk_email_info }}
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_cancel }}</button>
        <button type="button" class="btn btn-primary" id="button-bulk-email">{{ button_send }}</button>
      </div>
    </div>
  </div>
</div>

<!-- Modal: Bulk Coupon -->
<div class="modal fade" id="modal-bulk-coupon" tabindex="-1" role="dialog" aria-labelledby="modal-bulk-coupon-label">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
        <h4 class="modal-title" id="modal-bulk-coupon-label">{{ text_bulk_coupon }}</h4>
      </div>
      <div class="modal-body">
        <div class="alert alert-info">
          <i class="fa fa-info-circle"></i> {{ text_bulk_coupon_info }}
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_cancel }}</button>
        <button type="button" class="btn btn-primary" id="button-bulk-coupon">{{ button_generate }}</button>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript"><!--
// Load chart
var ctx = document.getElementById('abandoned-carts-chart').getContext('2d');
var chartData = {{ stats.chart_data|json_encode }};

var labels = [];
var counts = [];
var values = [];

for (var i = 0; i < chartData.length; i++) {
  labels.push(chartData[i].day);
  counts.push(chartData[i].total);
  values.push(chartData[i].value);
}

var myChart = new Chart(ctx, {
  type: 'line',
  data: {
    labels: labels,
    datasets: [
      {
        label: '{{ text_cart_count }}',
        data: counts,
        backgroundColor: 'rgba(54, 162, 235, 0.2)',
        borderColor: 'rgba(54, 162, 235, 1)',
        borderWidth: 1,
        yAxisID: 'y-axis-count'
      },
      {
        label: '{{ text_cart_value }}',
        data: values,
        backgroundColor: 'rgba(255, 99, 132, 0.2)',
        borderColor: 'rgba(255, 99, 132, 1)',
        borderWidth: 1,
        yAxisID: 'y-axis-value'
      }
    ]
  },
  options: {
    responsive: true,
    scales: {
      yAxes: [
        {
          id: 'y-axis-count',
          type: 'linear',
          position: 'left',
          ticks: {
            beginAtZero: true
          },
          scaleLabel: {
            display: true,
            labelString: '{{ text_cart_count }}'
          }
        },
        {
          id: 'y-axis-value',
          type: 'linear',
          position: 'right',
          ticks: {
            beginAtZero: true,
            callback: function(value) {
              return value.toFixed(2);
            }
          },
          scaleLabel: {
            display: true,
            labelString: '{{ text_cart_value }}'
          },
          gridLines: {
            drawOnChartArea: false
          }
        }
      ]
    }
  }
});

// Date picker
$('.date').datetimepicker({
  pickTime: false
});

// Filter functionality
$('#button-filter').on('click', function() {
  var url = 'index.php?route=sale/abandoned_cart&user_token={{ user_token }}';

  var filter_customer = $('input[name=\'filter_customer\']').val();
  if (filter_customer) {
    url += '&filter_customer=' + encodeURIComponent(filter_customer);
  }

  var filter_date_start = $('input[name=\'filter_date_start\']').val();
  if (filter_date_start) {
    url += '&filter_date_start=' + encodeURIComponent(filter_date_start);
  }

  var filter_date_end = $('input[name=\'filter_date_end\']').val();
  if (filter_date_end) {
    url += '&filter_date_end=' + encodeURIComponent(filter_date_end);
  }

  var filter_status = $('select[name=\'filter_status\']').val();
  if (filter_status) {
    url += '&filter_status=' + encodeURIComponent(filter_status);
  }

  var filter_total_min = $('input[name=\'filter_total_min\']').val();
  if (filter_total_min) {
    url += '&filter_total_min=' + encodeURIComponent(filter_total_min);
  }

  var filter_total_max = $('input[name=\'filter_total_max\']').val();
  if (filter_total_max) {
    url += '&filter_total_max=' + encodeURIComponent(filter_total_max);
  }

  var filter_email_sent = $('select[name=\'filter_email_sent\']').val();
  if (filter_email_sent !== '') {
    url += '&filter_email_sent=' + encodeURIComponent(filter_email_sent);
  }

  location = url;
});

$('#button-clear-filter').on('click', function() {
  location = 'index.php?route=sale/abandoned_cart&user_token={{ user_token }}';
});

// Refresh data
$('#refresh-data').on('click', function() {
  location.reload();
});

// Delete selected carts
$('#button-delete').on('click', function() {
  if ($('input[name=\'selected[]\']:checked').length) {
    if (confirm('{{ text_confirm_delete }}')) {
      $('#form-abandoned-cart').submit();
    }
  } else {
    alert('{{ error_no_selection }}');
  }
});

// Send email to specific cart
function sendEmail(cartId) {
  $('#email-cart-id').val(cartId);
  
  // Load email templates
  $.ajax({
    url: 'index.php?route=sale/abandoned_cart/templates&user_token={{ user_token }}&type=email',
    dataType: 'json',
    beforeSend: function() {
      $('#email-template').html('<option value="default">{{ text_default_template }}</option><option value="custom">{{ text_custom_message }}</option>');
    },
    success: function(json) {
      if (json.templates && json.templates.length) {
        for (var i = 0; i < json.templates.length; i++) {
          $('#email-template').append('<option value="' + json.templates[i].template_id + '">' + json.templates[i].name + '</option>');
        }
      }
      
      $('#modal-email').modal('show');
    },
    error: function(xhr, ajaxOptions, thrownError) {
      alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
    }
  });
}

// Email template change
$('#email-template').on('change', function() {
  if ($(this).val() === 'custom') {
    $('#custom-email-container').show();
  } else {
    $('#custom-email-container').hide();
  }
});

// Send email
$('#button-send-email').on('click', function() {
  $.ajax({
    url: 'index.php?route=sale/abandoned_cart/send_email&user_token={{ user_token }}',
    type: 'post',
    data: $('#form-email').serialize(),
    dataType: 'json',
    beforeSend: function() {
      $('#button-send-email').button('loading');
    },
    complete: function() {
      $('#button-send-email').button('reset');
    },
    success: function(json) {
      $('.alert-dismissible').remove();
      
      if (json.error) {
        $('#modal-email .modal-body').prepend('<div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> ' + json.error + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
      }
      
      if (json.success) {
        $('#modal-email').modal('hide');
        
        $('#content > .container-fluid').prepend('<div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> ' + json.success + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
        
        setTimeout(function() {
          location.reload();
        }, 1000);
      }
    },
    error: function(xhr, ajaxOptions, thrownError) {
      alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
    }
  });
});

// Send SMS to specific cart
function sendSMS(cartId) {
  $('#sms-cart-id').val(cartId);
  
  // Load SMS templates
  $.ajax({
    url: 'index.php?route=sale/abandoned_cart/templates&user_token={{ user_token }}&type=sms',
    dataType: 'json',
    beforeSend: function() {
      $('#sms-template').html('<option value="default">{{ text_default_template }}</option><option value="custom">{{ text_custom_message }}</option>');
    },
    success: function(json) {
      if (json.templates && json.templates.length) {
        for (var i = 0; i < json.templates.length; i++) {
$('#sms-template').append('<option value="' + json.templates[i].template_id + '">' + json.templates[i].name + '</option>');
       }
     }
     
     $('#modal-sms').modal('show');
   },
   error: function(xhr, ajaxOptions, thrownError) {
     alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
   }
 });
}

// SMS template change
$('#sms-template').on('change', function() {
 if ($(this).val() === 'custom') {
   $('#custom-sms-container').show();
 } else {
   $('#custom-sms-container').hide();
 }
});

// Send SMS
$('#button-send-sms').on('click', function() {
 $.ajax({
   url: 'index.php?route=sale/abandoned_cart/send_sms&user_token={{ user_token }}',
   type: 'post',
   data: $('#form-sms').serialize(),
   dataType: 'json',
   beforeSend: function() {
     $('#button-send-sms').button('loading');
   },
   complete: function() {
     $('#button-send-sms').button('reset');
   },
   success: function(json) {
     $('.alert-dismissible').remove();
     
     if (json.error) {
       $('#modal-sms .modal-body').prepend('<div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> ' + json.error + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
     }
     
     if (json.success) {
       $('#modal-sms').modal('hide');
       
       $('#content > .container-fluid').prepend('<div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> ' + json.success + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
     }
   },
   error: function(xhr, ajaxOptions, thrownError) {
     alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
   }
 });
});

// Send WhatsApp
function sendWhatsApp(cartId) {
 $('#whatsapp-cart-id').val(cartId);
 
 // Load WhatsApp templates
 $.ajax({
   url: 'index.php?route=sale/abandoned_cart/templates&user_token={{ user_token }}&type=whatsapp',
   dataType: 'json',
   beforeSend: function() {
     $('#whatsapp-template').html('<option value="default">{{ text_default_template }}</option><option value="custom">{{ text_custom_message }}</option>');
   },
   success: function(json) {
     if (json.templates && json.templates.length) {
       for (var i = 0; i < json.templates.length; i++) {
         $('#whatsapp-template').append('<option value="' + json.templates[i].template_id + '">' + json.templates[i].name + '</option>');
       }
     }
     
     $('#modal-whatsapp').modal('show');
   },
   error: function(xhr, ajaxOptions, thrownError) {
     alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
   }
 });
}

// WhatsApp template change
$('#whatsapp-template').on('change', function() {
 if ($(this).val() === 'custom') {
   $('#custom-whatsapp-container').show();
 } else {
   $('#custom-whatsapp-container').hide();
 }
});

// Send WhatsApp
$('#button-send-whatsapp').on('click', function() {
 $.ajax({
   url: 'index.php?route=sale/abandoned_cart/send_whatsapp&user_token={{ user_token }}',
   type: 'post',
   data: $('#form-whatsapp').serialize(),
   dataType: 'json',
   beforeSend: function() {
     $('#button-send-whatsapp').button('loading');
   },
   complete: function() {
     $('#button-send-whatsapp').button('reset');
   },
   success: function(json) {
     $('.alert-dismissible').remove();
     
     if (json.error) {
       $('#modal-whatsapp .modal-body').prepend('<div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> ' + json.error + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
     }
     
     if (json.success) {
       $('#modal-whatsapp').modal('hide');
       
       $('#content > .container-fluid').prepend('<div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> ' + json.success + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
     }
   },
   error: function(xhr, ajaxOptions, thrownError) {
     alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
   }
 });
});

// Send Telegram
function sendTelegram(cartId) {
 $('#telegram-cart-id').val(cartId);
 
 // Load Telegram templates
 $.ajax({
   url: 'index.php?route=sale/abandoned_cart/templates&user_token={{ user_token }}&type=telegram',
   dataType: 'json',
   beforeSend: function() {
     $('#telegram-template').html('<option value="default">{{ text_default_template }}</option><option value="custom">{{ text_custom_message }}</option>');
   },
   success: function(json) {
     if (json.templates && json.templates.length) {
       for (var i = 0; i < json.templates.length; i++) {
         $('#telegram-template').append('<option value="' + json.templates[i].template_id + '">' + json.templates[i].name + '</option>');
       }
     }
     
     $('#modal-telegram').modal('show');
   },
   error: function(xhr, ajaxOptions, thrownError) {
     alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
   }
 });
}

// Telegram template change
$('#telegram-template').on('change', function() {
 if ($(this).val() === 'custom') {
   $('#custom-telegram-container').show();
 } else {
   $('#custom-telegram-container').hide();
 }
});

// Send Telegram
$('#button-send-telegram').on('click', function() {
 $.ajax({
   url: 'index.php?route=sale/abandoned_cart/send_telegram&user_token={{ user_token }}',
   type: 'post',
   data: $('#form-telegram').serialize(),
   dataType: 'json',
   beforeSend: function() {
     $('#button-send-telegram').button('loading');
   },
   complete: function() {
     $('#button-send-telegram').button('reset');
   },
   success: function(json) {
     $('.alert-dismissible').remove();
     
     if (json.error) {
       $('#modal-telegram .modal-body').prepend('<div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> ' + json.error + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
     }
     
     if (json.success) {
       $('#modal-telegram').modal('hide');
       
       $('#content > .container-fluid').prepend('<div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> ' + json.success + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
     }
   },
   error: function(xhr, ajaxOptions, thrownError) {
     alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
   }
 });
});

// Create coupon for specific cart
function createCoupon(cartId) {
 $.ajax({
   url: 'index.php?route=sale/abandoned_cart/create_coupon&user_token={{ user_token }}',
   type: 'post',
   data: { cart_id: cartId },
   dataType: 'json',
   beforeSend: function() {
     // Show loading animation
   },
   complete: function() {
     // Hide loading animation
   },
   success: function(json) {
     $('.alert-dismissible').remove();
     
     if (json.error) {
       $('#content > .container-fluid').prepend('<div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> ' + json.error + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
     }
     
     if (json.success) {
       $('#content > .container-fluid').prepend('<div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> ' + json.success + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
     }
   },
   error: function(xhr, ajaxOptions, thrownError) {
     alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
   }
 });
}

// Bulk email
$('#bulk-email-template').on('change', function() {
 if ($(this).val() === 'custom') {
   $('#bulk-custom-email-container').show();
 } else {
   $('#bulk-custom-email-container').hide();
 }
});

$('#button-bulk-email').on('click', function() {
 if ($('input[name=\'selected[]\']:checked').length == 0) {
   alert('{{ error_no_selection }}');
   return;
 }
 
 if (confirm('{{ text_confirm_bulk_email }}')) {
   var template = $('#bulk-email-template').val();
   var custom_message = $('#bulk-email-custom-message').val();
   var include_coupon = $('#modal-bulk-email input[name=\'include_coupon\']').is(':checked') ? 1 : 0;
   var selected = [];
   
   $('input[name=\'selected[]\']:checked').each(function() {
     selected.push($(this).val());
   });
   
   $.ajax({
     url: 'index.php?route=sale/abandoned_cart/bulk_email&user_token={{ user_token }}',
     type: 'post',
     data: {
       selected: selected,
       email_template: template,
       custom_message: custom_message,
       include_coupon: include_coupon
     },
     dataType: 'json',
     beforeSend: function() {
       $('#button-bulk-email').button('loading');
     },
     complete: function() {
       $('#button-bulk-email').button('reset');
     },
     success: function(json) {
       $('.alert-dismissible').remove();
       
       if (json.error) {
         $('#modal-bulk-email .modal-body').prepend('<div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> ' + json.error + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
       }
       
       if (json.success) {
         $('#modal-bulk-email').modal('hide');
         
         $('#content > .container-fluid').prepend('<div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> ' + json.success + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
         
         setTimeout(function() {
           location.reload();
         }, 1000);
       }
     },
     error: function(xhr, ajaxOptions, thrownError) {
       alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
     }
   });
 }
});

// Bulk coupon
$('#button-bulk-coupon').on('click', function() {
 if ($('input[name=\'selected[]\']:checked').length == 0) {
   alert('{{ error_no_selection }}');
   return;
 }
 
 if (confirm('{{ text_confirm_bulk_coupon }}')) {
   var selected = [];
   
   $('input[name=\'selected[]\']:checked').each(function() {
     selected.push($(this).val());
   });
   
   $.ajax({
     url: 'index.php?route=sale/abandoned_cart/bulk_coupon&user_token={{ user_token }}',
     type: 'post',
     data: {
       selected: selected
     },
     dataType: 'json',
     beforeSend: function() {
       $('#button-bulk-coupon').button('loading');
     },
     complete: function() {
       $('#button-bulk-coupon').button('reset');
     },
     success: function(json) {
       $('.alert-dismissible').remove();
       
       if (json.error) {
         $('#modal-bulk-coupon .modal-body').prepend('<div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> ' + json.error + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
       }
       
       if (json.success) {
         $('#modal-bulk-coupon').modal('hide');
         
         $('#content > .container-fluid').prepend('<div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> ' + json.success + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
       }
     },
     error: function(xhr, ajaxOptions, thrownError) {
       alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
     }
   });
 }
});

// Load email templates initially
$.ajax({
 url: 'index.php?route=sale/abandoned_cart/templates&user_token={{ user_token }}&type=email',
 dataType: 'json',
 success: function(json) {
   if (json.templates && json.templates.length) {
     for (var i = 0; i < json.templates.length; i++) {
       $('#email-template').append('<option value="' + json.templates[i].template_id + '">' + json.templates[i].name + '</option>');
       $('#bulk-email-template').append('<option value="' + json.templates[i].template_id + '">' + json.templates[i].name + '</option>');
     }
   }
 }
});

// Autocomplete for customer filter
$('input[name=\'filter_customer\']').autocomplete({
 source: function(request, response) {
   $.ajax({
     url: 'index.php?route=sale/abandoned_cart/ajax_search_customer&user_token={{ user_token }}&filter_name=' + encodeURIComponent(request.term),
     dataType: 'json',
     success: function(json) {
       response($.map(json, function(item) {
         return {
           label: item.name + ' (' + item.email + ')',
           value: item.name
         }
       }));
     }
   });
 },
 minLength: 2,
 select: function(event, ui) {
   $('input[name=\'filter_customer\']').val(ui.item.value);
   return false;
 }
});
//--></script>
{{ footer }}