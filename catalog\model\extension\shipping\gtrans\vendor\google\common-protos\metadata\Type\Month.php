<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/type/month.proto

namespace GPBMetadata\Google\Type;

class Month
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(hex2bin(
            "0ac0020a17676f6f676c652f747970652f6d6f6e74682e70726f746f120b" .
            "676f6f676c652e747970652ab0010a054d6f6e746812150a114d4f4e5448" .
            "5f554e5350454349464945441000120b0a074a414e554152591001120c0a" .
            "084645425255415259100212090a054d41524348100312090a0541505249" .
            "4c100412070a034d4159100512080a044a554e45100612080a044a554c59" .
            "1007120a0a064155475553541008120d0a0953455054454d424552100912" .
            "0b0a074f43544f424552100a120c0a084e4f56454d424552100b120c0a08" .
            "444543454d424552100c425d0a0f636f6d2e676f6f676c652e7479706542" .
            "0a4d6f6e746850726f746f50015a36676f6f676c652e676f6c616e672e6f" .
            "72672f67656e70726f746f2f676f6f676c65617069732f747970652f6d6f" .
            "6e74683b6d6f6e7468a20203475450620670726f746f33"
        ), true);

        static::$is_initialized = true;
    }
}

