<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/expr/v1alpha1/syntax.proto

namespace GPBMetadata\Google\Api\Expr\V1Alpha1;

class Syntax
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        \GPBMetadata\Google\Protobuf\Duration::initOnce();
        \GPBMetadata\Google\Protobuf\Struct::initOnce();
        \GPBMetadata\Google\Protobuf\Timestamp::initOnce();
        $pool->internalAddGeneratedFile(hex2bin(
            "0ac0130a25676f6f676c652f6170692f657870722f7631616c706861312f" .
            "73796e7461782e70726f746f1218676f6f676c652e6170692e657870722e" .
            "7631616c706861311a1c676f6f676c652f70726f746f6275662f73747275" .
            "63742e70726f746f1a1f676f6f676c652f70726f746f6275662f74696d65" .
            "7374616d702e70726f746f22750a0a50617273656445787072122c0a0465" .
            "78707218022001280b321e2e676f6f676c652e6170692e657870722e7631" .
            "616c706861312e4578707212390a0b736f757263655f696e666f18032001" .
            "280b32242e676f6f676c652e6170692e657870722e7631616c706861312e" .
            "536f75726365496e666f22c50a0a0445787072120a0a0269641802200128" .
            "0312380a0a636f6e73745f6578707218032001280b32222e676f6f676c65" .
            "2e6170692e657870722e7631616c706861312e436f6e7374616e74480012" .
            "3a0a0a6964656e745f6578707218042001280b32242e676f6f676c652e61" .
            "70692e657870722e7631616c706861312e457870722e4964656e74480012" .
            "3c0a0b73656c6563745f6578707218052001280b32252e676f6f676c652e" .
            "6170692e657870722e7631616c706861312e457870722e53656c65637448" .
            "0012380a0963616c6c5f6578707218062001280b32232e676f6f676c652e" .
            "6170692e657870722e7631616c706861312e457870722e43616c6c480012" .
            "3e0a096c6973745f6578707218072001280b32292e676f6f676c652e6170" .
            "692e657870722e7631616c706861312e457870722e4372656174654c6973" .
            "74480012420a0b7374727563745f6578707218082001280b322b2e676f6f" .
            "676c652e6170692e657870722e7631616c706861312e457870722e437265" .
            "6174655374727563744800124a0a12636f6d70726568656e73696f6e5f65" .
            "78707218092001280b322c2e676f6f676c652e6170692e657870722e7631" .
            "616c706861312e457870722e436f6d70726568656e73696f6e48001a150a" .
            "054964656e74120c0a046e616d651801200128091a5b0a0653656c656374" .
            "122f0a076f706572616e6418012001280b321e2e676f6f676c652e617069" .
            "2e657870722e7631616c706861312e45787072120d0a056669656c641802" .
            "2001280912110a09746573745f6f6e6c791803200128081a760a0443616c" .
            "6c122e0a0674617267657418012001280b321e2e676f6f676c652e617069" .
            "2e657870722e7631616c706861312e4578707212100a0866756e6374696f" .
            "6e180220012809122c0a046172677318032003280b321e2e676f6f676c65" .
            "2e6170692e657870722e7631616c706861312e457870721a3e0a0a437265" .
            "6174654c69737412300a08656c656d656e747318012003280b321e2e676f" .
            "6f676c652e6170692e657870722e7631616c706861312e457870721a8102" .
            "0a0c43726561746553747275637412140a0c6d6573736167655f6e616d65" .
            "18012001280912420a07656e747269657318022003280b32312e676f6f67" .
            "6c652e6170692e657870722e7631616c706861312e457870722e43726561" .
            "74655374727563742e456e7472791a96010a05456e747279120a0a026964" .
            "18012001280312130a096669656c645f6b6579180220012809480012310a" .
            "076d61705f6b657918032001280b321e2e676f6f676c652e6170692e6578" .
            "70722e7631616c706861312e457870724800122d0a0576616c7565180420" .
            "01280b321e2e676f6f676c652e6170692e657870722e7631616c70686131" .
            "2e45787072420a0a086b65795f6b696e641ab5020a0d436f6d7072656865" .
            "6e73696f6e12100a08697465725f76617218012001280912320a0a697465" .
            "725f72616e676518022001280b321e2e676f6f676c652e6170692e657870" .
            "722e7631616c706861312e4578707212100a08616363755f766172180320" .
            "01280912310a09616363755f696e697418042001280b321e2e676f6f676c" .
            "652e6170692e657870722e7631616c706861312e4578707212360a0e6c6f" .
            "6f705f636f6e646974696f6e18052001280b321e2e676f6f676c652e6170" .
            "692e657870722e7631616c706861312e4578707212310a096c6f6f705f73" .
            "74657018062001280b321e2e676f6f676c652e6170692e657870722e7631" .
            "616c706861312e45787072122e0a06726573756c7418072001280b321e2e" .
            "676f6f676c652e6170692e657870722e7631616c706861312e4578707242" .
            "0b0a09657870725f6b696e6422cd020a08436f6e7374616e7412300a0a6e" .
            "756c6c5f76616c756518012001280e321a2e676f6f676c652e70726f746f" .
            "6275662e4e756c6c56616c7565480012140a0a626f6f6c5f76616c756518" .
            "0220012808480012150a0b696e7436345f76616c75651803200128034800" .
            "12160a0c75696e7436345f76616c7565180420012804480012160a0c646f" .
            "75626c655f76616c7565180520012801480012160a0c737472696e675f76" .
            "616c7565180620012809480012150a0b62797465735f76616c7565180720" .
            "01280c480012370a0e6475726174696f6e5f76616c756518082001280b32" .
            "192e676f6f676c652e70726f746f6275662e4475726174696f6e42021801" .
            "480012390a0f74696d657374616d705f76616c756518092001280b321a2e" .
            "676f6f676c652e70726f746f6275662e54696d657374616d704202180148" .
            "00420f0a0d636f6e7374616e745f6b696e6422e4020a0a536f7572636549" .
            "6e666f12160a0e73796e7461785f76657273696f6e18012001280912100a" .
            "086c6f636174696f6e18022001280912140a0c6c696e655f6f6666736574" .
            "7318032003280512460a09706f736974696f6e7318042003280b32332e67" .
            "6f6f676c652e6170692e657870722e7631616c706861312e536f75726365" .
            "496e666f2e506f736974696f6e73456e74727912490a0b6d6163726f5f63" .
            "616c6c7318052003280b32342e676f6f676c652e6170692e657870722e76" .
            "31616c706861312e536f75726365496e666f2e4d6163726f43616c6c7345" .
            "6e7472791a300a0e506f736974696f6e73456e747279120b0a036b657918" .
            "0120012803120d0a0576616c75651802200128053a0238011a510a0f4d61" .
            "63726f43616c6c73456e747279120b0a036b6579180120012803122d0a05" .
            "76616c756518022001280b321e2e676f6f676c652e6170692e657870722e" .
            "7631616c706861312e457870723a02380122500a0e536f75726365506f73" .
            "6974696f6e12100a086c6f636174696f6e180120012809120e0a066f6666" .
            "736574180220012805120c0a046c696e65180320012805120e0a06636f6c" .
            "756d6e180420012805426e0a1c636f6d2e676f6f676c652e6170692e6578" .
            "70722e7631616c70686131420b53796e74617850726f746f50015a3c676f" .
            "6f676c652e676f6c616e672e6f72672f67656e70726f746f2f676f6f676c" .
            "65617069732f6170692f657870722f7631616c706861313b65787072f801" .
            "01620670726f746f33"
        ), true);

        static::$is_initialized = true;
    }
}

