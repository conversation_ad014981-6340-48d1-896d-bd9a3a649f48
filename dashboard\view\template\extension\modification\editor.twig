{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="button" id="button-erase-theme" data-toggle="tooltip" title="{{ button_erase_theme }}" class="btn btn-success" data-loading-text="{{ text_erasing }}"><i class="fa fa-eraser"></i></button>
        <button type="button" id="button-erase-data" data-toggle="tooltip" title="{{ button_erase_data }}" class="btn btn-warning" data-loading-text="{{ text_erasing }}"><i class="fa fa-eraser"></i></button>
        <button type="button" id="button-erase-image" data-toggle="tooltip" title="{{ button_erase_image }}" class="btn btn-danger" data-loading-text="{{ text_erasing }}"><i class="fa fa-eraser"></i></button>
        <button type="button" id="button-save" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary" data-loading-text="{{ text_loading }}"><i class="fa fa-save"></i></button>
        <a href="{{ return }}" data-toggle="tooltip" title="{{ button_return }}" class="btn btn-default"><i class="fa fa-reply"></i></a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pencil"></i> {{ name }}</h3>
      </div>
      <div class="panel-body">
        <div class="alert alert-info"><i class="fa fa-info-circle"></i> {{ text_help_ocmod }}
          <button type="button" class="close" data-dismiss="alert">&times;</button>
        </div>
        <form action="" method="post" enctype="multipart/form-data" id="form-modification" class="form-horizontal">
          <fieldset>
            <legend>{{ text_xml_code }}</legend>
            <div class="form-group">
              <div class="col-sm-12">
                <pre id="code" style="width:100% !important; height:480px; position:relative; font-size:1.1em;">{{ xml }}</pre>
                <input type="hidden" name="modification_id" value="{{ modification_id }}" />
              </div>
            </div>
          </fieldset>
        </form>
      </div>
    </div>
  </div>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/ace/1.2.9/ace.js" type="text/javascript" charset="utf-8"></script>
  <script type="text/javascript">
    var editor = ace.edit("code");

    editor.setTheme("ace/theme/cobalt");
    editor.setOptions({
        wrap: true,
        useSoftTabs: true,
    });
    editor.getSession().setMode("ace/mode/xml");

    $('#button-erase-theme').on('click', function() {
      $('.alert').remove();
      $.ajax({
        url: 'index.php?route=extension/modification/editor/erase_cache_theme&user_token={{ user_token }}',
        dataType: 'json',
        cache: false,
        beforeSend: function() {
          $('#button-erase-theme').button('loading');
        },
        complete: function() {
          $('#button-erase-theme').button('reset');
        },
        success: function(json) {
          if (json['error']) {
            $('.panel-default').before('<div class="alert alert-danger" role="alert">' + json['error'] + '</div>');
          } else {
            $('.panel-default').before('<div class="alert alert-success" role="alert">' + json['success'] + '</div>');
          }
        },
        error: function(xhr, ajaxOptions, thrownError) {
          alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
      });
    });

    $('#button-erase-data').on('click', function() {
      $('.alert').remove();
      $.ajax({
        url: 'index.php?route=extension/modification/editor/erase_cache_data&user_token={{ user_token }}',
        dataType: 'json',
        cache: false,
        beforeSend: function() {
          $('#button-erase-data').button('loading');
        },
        complete: function() {
          $('#button-erase-data').button('reset');
        },
        success: function(json) {
          if (json['error']) {
            $('.panel-default').before('<div class="alert alert-danger" role="alert">' + json['error'] + '</div>');
          } else {
            $('.panel-default').before('<div class="alert alert-success" role="alert">' + json['success'] + '</div>');
          }
        },
        error: function(xhr, ajaxOptions, thrownError) {
          alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
      });
    });

    $('#button-erase-image').on('click', function() {
      $('.alert').remove();
      $.ajax({
        url: 'index.php?route=extension/modification/editor/erase_cache_image&user_token={{ user_token }}',
        dataType: 'json',
        cache: false,
        beforeSend: function() {
          $('#button-erase-image').button('loading');
        },
        complete: function() {
          $('#button-erase-image').button('reset');
        },
        success: function(json) {
          if (json['error']) {
            $('.panel-default').before('<div class="alert alert-danger" role="alert">' + json['error'] + '</div>');
          } else {
            $('.panel-default').before('<div class="alert alert-success" role="alert">' + json['success'] + '</div>');
          }
        },
        error: function(xhr, ajaxOptions, thrownError) {
          alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
      });
    });

    $('#button-save').on('click', function(){
      $('.alert').remove();
      var id = $('input[name="modification_id"]').val();
      var xml_code = editor.getValue();
      $.ajax({
        url: 'index.php?route=extension/modification/editor/save&user_token={{ user_token }}',
        type: 'post',
        dataType: 'json',
        data: { modification_id: id, xml: xml_code },
        cache: false,
        beforeSend: function() {
          $('#button-save').button('loading');
        },
        complete: function() {
          $('#button-save').button('reset');
        },
        success: function(json) {
          if (json['error']) {
            $('.panel-default').before('<div class="alert alert-danger" role="alert">' + json['error'] + '</div>');
          } else {
            $('.panel-default').before('<div class="alert alert-success" role="alert">' + json['success'] + '</div>');

            if (id == 0) { location.href = 'index.php?route=marketplace/modification&user_token={{ user_token }}'; }
          }
        },
        error: function(xhr, ajaxOptions, thrownError) {
          alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
      });
    });

    $(document).ready(function() {
      $('#code').height(Math.max($(window).height() - 120, 800)).css('resize', 'vertical'); // minus 400 is to stay in the window
    });
  </script>
</div>
{{ footer }}