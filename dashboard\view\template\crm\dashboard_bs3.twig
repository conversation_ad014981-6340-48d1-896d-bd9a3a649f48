{{ header }}{{ column_left }}

{# لوحة التحكم التفاعلية - CRM Dashboard #}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="button" data-toggle="tooltip" title="{{ button_refresh }}" onclick="refreshDashboard();" class="btn btn-default">
          <i class="fa fa-refresh"></i>
        </button>
        <button type="button" data-toggle="tooltip" title="{{ button_settings }}" onclick="$('#modal-settings').modal('show');" class="btn btn-default">
          <i class="fa fa-cog"></i>
        </button>
        <button type="button" data-toggle="tooltip" title="{{ button_export }}" onclick="exportDashboard();" class="btn btn-success">
          <i class="fa fa-download"></i>
        </button>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
          <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>

  <div class="container-fluid">
    {# مؤشرات الأداء الرئيسية #}
    <div class="row">
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-primary">
          <div class="panel-body">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-users fa-5x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ kpi.total_leads }}</div>
                <div>{{ text_total_leads }}</div>
                <div class="text-success">
                  <i class="fa fa-arrow-{{ kpi.leads_trend > 0 ? 'up' : 'down' }}"></i>
                  {{ kpi.leads_trend }}% {{ text_from_last_month }}
                </div>
              </div>
            </div>
          </div>
          <a href="{{ link_leads }}">
            <div class="panel-footer">
              <span class="pull-left">{{ text_view_details }}</span>
              <span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
              <div class="clearfix"></div>
            </div>
          </a>
        </div>
      </div>
      
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-success">
          <div class="panel-body">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-exchange fa-5x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ kpi.conversion_rate }}%</div>
                <div>{{ text_conversion_rate }}</div>
                <div class="text-success">
                  <i class="fa fa-arrow-{{ kpi.conversion_trend > 0 ? 'up' : 'down' }}"></i>
                  {{ kpi.conversion_trend }}% {{ text_from_last_month }}
                </div>
              </div>
            </div>
          </div>
          <a href="{{ link_conversions }}">
            <div class="panel-footer">
              <span class="pull-left">{{ text_view_details }}</span>
              <span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
              <div class="clearfix"></div>
            </div>
          </a>
        </div>
      </div>
      
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-warning">
          <div class="panel-body">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-dollar fa-5x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ kpi.revenue }}</div>
                <div>{{ text_total_revenue }}</div>
                <div class="text-success">
                  <i class="fa fa-arrow-{{ kpi.revenue_trend > 0 ? 'up' : 'down' }}"></i>
                  {{ kpi.revenue_trend }}% {{ text_from_last_month }}
                </div>
              </div>
            </div>
          </div>
          <a href="{{ link_revenue }}">
            <div class="panel-footer">
              <span class="pull-left">{{ text_view_details }}</span>
              <span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
              <div class="clearfix"></div>
            </div>
          </a>
        </div>
      </div>
      
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-info">
          <div class="panel-body">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-bullhorn fa-5x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ kpi.active_campaigns }}</div>
                <div>{{ text_active_campaigns }}</div>
                <div class="text-success">
                  <i class="fa fa-arrow-{{ kpi.campaigns_trend > 0 ? 'up' : 'down' }}"></i>
                  {{ kpi.campaigns_trend }}% {{ text_from_last_month }}
                </div>
              </div>
            </div>
          </div>
          <a href="{{ link_campaigns }}">
            <div class="panel-footer">
              <span class="pull-left">{{ text_view_details }}</span>
              <span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
              <div class="clearfix"></div>
            </div>
          </a>
        </div>
      </div>
    </div>

    {# الرسوم البيانية الرئيسية #}
    <div class="row">
      <div class="col-lg-8">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title">
              <i class="fa fa-line-chart"></i>
              {{ text_sales_performance }}
            </h3>
            <div class="pull-right">
              <div class="btn-group" data-toggle="buttons">
                <label class="btn btn-default btn-sm">
                  <input type="radio" name="sales-period" value="7" autocomplete="off"> 7{{ text_days }}
                </label>
                <label class="btn btn-default btn-sm active">
                  <input type="radio" name="sales-period" value="30" autocomplete="off" checked> 30{{ text_days }}
                </label>
                <label class="btn btn-default btn-sm">
                  <input type="radio" name="sales-period" value="90" autocomplete="off"> 90{{ text_days }}
                </label>
              </div>
            </div>
          </div>
          <div class="panel-body">
            <canvas id="salesPerformanceChart" height="400"></canvas>
          </div>
        </div>
      </div>
      
      <div class="col-lg-4">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title">
              <i class="fa fa-pie-chart"></i>
              {{ text_lead_sources }}
            </h3>
          </div>
          <div class="panel-body">
            <canvas id="leadSourcesChart" height="400"></canvas>
          </div>
        </div>
      </div>
    </div>

    {# الرسوم البيانية الثانوية #}
    <div class="row">
      <div class="col-lg-6">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title">
              <i class="fa fa-filter"></i>
              {{ text_sales_funnel }}
            </h3>
          </div>
          <div class="panel-body">
            <canvas id="salesFunnelChart" height="300"></canvas>
          </div>
        </div>
      </div>
      
      <div class="col-lg-6">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title">
              <i class="fa fa-bar-chart"></i>
              {{ text_campaign_performance }}
            </h3>
          </div>
          <div class="panel-body">
            <canvas id="campaignPerformanceChart" height="300"></canvas>
          </div>
        </div>
      </div>
    </div>

    {# الجداول والقوائم #}
    <div class="row">
      <div class="col-lg-6">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title">
              <i class="fa fa-star"></i>
              {{ text_top_leads }}
            </h3>
            <div class="pull-right">
              <a href="{{ link_leads }}" class="btn btn-xs btn-primary">{{ text_view_all }}</a>
            </div>
          </div>
          <div class="panel-body" style="padding: 0;">
            <div class="table-responsive">
              <table class="table table-striped">
                <thead>
                  <tr>
                    <th>{{ text_name }}</th>
                    <th>{{ text_company }}</th>
                    <th>{{ text_score }}</th>
                    <th>{{ text_status }}</th>
                  </tr>
                </thead>
                <tbody>
                  {% for lead in top_leads %}
                    <tr>
                      <td>
                        <div>
                          <strong>{{ lead.customer_name }}</strong>
                          <br><small class="text-muted">{{ lead.email }}</small>
                        </div>
                      </td>
                      <td>{{ lead.company }}</td>
                      <td>
                        <div class="progress" style="margin-bottom: 0;">
                          <div class="progress-bar progress-bar-{{ lead.score_color }}" style="width: {{ lead.total_score }}%">
                            {{ lead.total_score }}%
                          </div>
                        </div>
                      </td>
                      <td>
                        <span class="label label-{{ lead.status_color }}">{{ lead.status }}</span>
                      </td>
                    </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-lg-6">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title">
              <i class="fa fa-tasks"></i>
              {{ text_recent_activities }}
            </h3>
            <div class="pull-right">
              <a href="{{ link_activities }}" class="btn btn-xs btn-primary">{{ text_view_all }}</a>
            </div>
          </div>
          <div class="panel-body">
            <ul class="timeline">
              {% for activity in recent_activities %}
                <li class="timeline-item">
                  <div class="timeline-badge bg-{{ activity.type_color }}">
                    <i class="fa fa-{{ activity.icon }}"></i>
                  </div>
                  <div class="timeline-panel">
                    <div class="timeline-heading">
                      <h6 class="timeline-title">{{ activity.title }}</h6>
                      <p><small class="text-muted"><i class="fa fa-clock-o"></i> {{ activity.time_ago }}</small></p>
                    </div>
                    <div class="timeline-body">
                      <p>{{ activity.description }}</p>
                      <small class="text-muted">
                        <i class="fa fa-user"></i> {{ activity.user_name }}
                      </small>
                    </div>
                  </div>
                </li>
              {% endfor %}
            </ul>
          </div>
        </div>
      </div>
    </div>

    {# التنبيهات والإشعارات #}
    <div class="row">
      <div class="col-lg-12">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title">
              <i class="fa fa-bell"></i>
              {{ text_alerts_notifications }}
            </h3>
            <div class="pull-right">
              <button type="button" class="btn btn-xs btn-default" onclick="markAllAsRead();">
                <i class="fa fa-check-double"></i> {{ text_mark_all_read }}
              </button>
            </div>
          </div>
          <div class="panel-body">
            {% if alerts %}
              <div class="row">
                {% for alert in alerts %}
                  <div class="col-lg-4">
                    <div class="alert alert-{{ alert.type }} alert-dismissible">
                      <button type="button" class="close" data-dismiss="alert">&times;</button>
                      <h6><i class="fa fa-{{ alert.icon }}"></i> {{ alert.title }}</h6>
                      <p>{{ alert.message }}</p>
                      <small class="text-muted">{{ alert.time_ago }}</small>
                      {% if alert.action_url %}
                        <div style="margin-top: 10px;">
                          <a href="{{ alert.action_url }}" class="btn btn-xs btn-{{ alert.type }}">{{ alert.action_text }}</a>
                        </div>
                      {% endif %}
                    </div>
                  </div>
                {% endfor %}
              </div>
            {% else %}
              <div class="text-center text-muted" style="padding: 40px;">
                <i class="fa fa-bell-slash fa-3x" style="margin-bottom: 15px;"></i>
                <p>{{ text_no_alerts }}</p>
              </div>
            {% endif %}
          </div>
        </div>
      </div>
    </div>

    {# الأهداف والتقدم #}
    <div class="row">
      <div class="col-lg-12">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title">
              <i class="fa fa-target"></i>
              {{ text_goals_progress }}
            </h3>
            <div class="pull-right">
              <a href="{{ link_goals }}" class="btn btn-xs btn-primary">{{ text_manage_goals }}</a>
            </div>
          </div>
          <div class="panel-body">
            <div class="row">
              {% for goal in goals %}
                <div class="col-lg-3 col-md-6">
                  <div class="panel panel-{{ goal.status_color }}">
                    <div class="panel-body text-center">
                      <h5>{{ goal.title }}</h5>
                      <div class="progress" style="margin-bottom: 15px;">
                        <div class="progress-bar progress-bar-{{ goal.status_color }}" style="width: {{ goal.progress }}%"></div>
                      </div>
                      <p>
                        <strong>{{ goal.current_value }}</strong> / {{ goal.target_value }}
                        <br><small class="text-muted">{{ goal.progress }}% {{ text_completed }}</small>
                      </p>
                      <small class="text-muted">
                        <i class="fa fa-calendar"></i> {{ text_deadline }}: {{ goal.deadline }}
                      </small>
                    </div>
                  </div>
                </div>
              {% endfor %}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
