<?php
/**
 * تحكم الميزانية العمومية الشاملة والمتكاملة
 * يجمع بين القوة والبساطة - يتفوق على SAP وOracle في سهولة الاستخدام
 * مع الاحتفاظ بالقوة والمرونة المطلوبة للشركات الكبيرة
 * متوافق مع معايير المحاسبة المصرية ومتطلبات ETA
 */
class ControllerAccountsBalanceSheet extends Controller {
    private $error = array();
    private $central_service;

    public function __construct($registry) {
        parent::__construct($registry);

        // تحميل الخدمات المركزية
        $this->load->model('core/central_service_manager');
        $this->central_service = $this->model_core_central_service_manager;
    }

    public function index() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/balance_sheet') ||
            !$this->user->hasKey('accounting_balance_sheet_view')) {

            $this->central_service->logActivity('unauthorized_access', 'accounts',
                $this->language->get('log_unauthorized_access_balance_sheet'), [
                'user_id' => $this->user->getId(),
                'ip_address' => $this->request->server['REMOTE_ADDR']
            ]);

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/balance_sheet');
        $this->document->setTitle($this->language->get('heading_title'));

        // إضافة CSS و JavaScript المتقدم (من advanced)
        $this->document->addStyle('view/stylesheet/accounts/balance_sheet.css');
        $this->document->addScript('view/javascript/accounts/balance_sheet.js');
        $this->document->addScript('view/javascript/jquery/accounting.min.js');
        $this->document->addScript('view/javascript/jquery/select2.min.js');
        $this->document->addStyle('view/javascript/jquery/select2.min.css');
        $this->document->addScript('view/javascript/jquery/daterangepicker.min.js');
        $this->document->addStyle('view/javascript/jquery/daterangepicker.css');
        $this->document->addScript('view/javascript/jquery/chart.min.js');

        // تسجيل الوصول للشاشة
        $this->central_service->logActivity('view', 'accounts',
            $this->language->get('log_view_balance_sheet_screen'), [
            'user_id' => $this->user->getId(),
            'screen' => 'accounts/balance_sheet'
        ]);

        $data['action'] = $this->url->link('accounts/balance_sheet/print', 'user_token=' . $this->session->data['user_token'], true);
        
        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/balance_sheet_print_form', $data));
    }

    /**
     * توليد الميزانية العمومية المتقدمة (مدمج من advanced)
     */
    public function generate() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/balance_sheet') ||
            !$this->user->hasKey('accounting_balance_sheet_generate')) {

            $this->central_service->logActivity('unauthorized_generate', 'accounts',
                $this->language->get('log_unauthorized_generate_balance_sheet'), [
                'user_id' => $this->user->getId(),
                'action' => 'generate_balance_sheet'
            ]);

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/balance_sheet');
        $this->load->model('accounts/balance_sheet');

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm()) {
            try {
                $filter_data = $this->prepareFilterData();

                // تسجيل توليد التقرير
                $this->central_service->logActivity('generate_report', 'accounts',
                    $this->language->get('log_generate_balance_sheet_date') . ': ' . $filter_data['date_end'], [
                    'user_id' => $this->user->getId(),
                    'date_end' => $filter_data['date_end'],
                    'comparison_date' => $filter_data['comparison_date'] ?? null
                ]);

                $balance_sheet_data = $this->model_accounts_balance_sheet->generateBalanceSheet($filter_data);

                // إرسال إشعار للمحاسب الرئيسي
                $this->central_service->sendNotification(
                    'balance_sheet_generated',
                    $this->language->get('text_generate_balance_sheet'),
                    $this->language->get('text_balance_sheet_generated_notification') . ' ' . $this->language->get('text_for_date') . ' ' . $filter_data['date_end'] . ' ' . $this->language->get('text_by') . ' ' . $this->user->getFirstName(),
                    [$this->config->get('config_chief_accountant_id')],
                    [
                        'date_end' => $filter_data['date_end'],
                        'user_name' => $this->user->getFirstName() . ' ' . $this->user->getLastName(),
                        'total_assets' => $balance_sheet_data['totals']['total_assets'] ?? 0
                    ]
                );

                $this->session->data['success'] = $this->language->get('text_success_generate');
                $this->session->data['balance_sheet_data'] = $balance_sheet_data;
                $this->session->data['balance_sheet_filter'] = $filter_data;

                // إعادة توجيه حسب الإجراء المطلوب
                if (isset($this->request->post['generate_and_new'])) {
                    $this->response->redirect($this->url->link('accounts/balance_sheet', 'user_token=' . $this->session->data['user_token'], true));
                } elseif (isset($this->request->post['generate_and_export'])) {
                    $this->response->redirect($this->url->link('accounts/balance_sheet/export', 'format=excel&user_token=' . $this->session->data['user_token'], true));
                } else {
                    $this->response->redirect($this->url->link('accounts/balance_sheet/view', 'user_token=' . $this->session->data['user_token'], true));
                }
            } catch (Exception $e) {
                $this->error['warning'] = $e->getMessage();
            }
        }

        $this->getForm();
    }

    /**
     * عرض الميزانية العمومية
     */
    public function view() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/balance_sheet') ||
            !$this->user->hasKey('accounting_balance_sheet_view')) {

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/balance_sheet');
        $this->document->setTitle($this->language->get('heading_title'));

        if (!isset($this->session->data['balance_sheet_data'])) {
            $this->session->data['error'] = $this->language->get('error_no_data');
            $this->response->redirect($this->url->link('accounts/balance_sheet', 'user_token=' . $this->session->data['user_token'], true));
            return;
        }

        $data = $this->session->data['balance_sheet_data'];

        // تسجيل عرض التقرير
        $this->central_service->logActivity('view_report', 'accounts',
            $this->language->get('log_view_balance_sheet'), [
            'user_id' => $this->user->getId(),
            'action' => 'view_balance_sheet'
        ]);

        $data['breadcrumbs'] = array();
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('accounts/balance_sheet', 'user_token=' . $this->session->data['user_token'], true)
        );

        $data['export_excel'] = $this->url->link('accounts/balance_sheet/export', 'format=excel&user_token=' . $this->session->data['user_token'], true);
        $data['export_pdf'] = $this->url->link('accounts/balance_sheet/export', 'format=pdf&user_token=' . $this->session->data['user_token'], true);
        $data['export_csv'] = $this->url->link('accounts/balance_sheet/export', 'format=csv&user_token=' . $this->session->data['user_token'], true);

        $data['user_token'] = $this->session->data['user_token'];
        $data['heading_title'] = $this->language->get('heading_title');

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/balance_sheet_view', $data));
    }

    /**
     * تصدير الميزانية العمومية (مدمج من advanced)
     */
    public function export() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/balance_sheet') ||
            !$this->user->hasKey('accounting_balance_sheet_export')) {

            $this->central_service->logActivity('unauthorized_export', 'accounts',
                $this->language->get('log_unauthorized_export_balance_sheet'), [
                'user_id' => $this->user->getId(),
                'action' => 'export_balance_sheet'
            ]);

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/balance_sheet');
        $this->load->model('accounts/balance_sheet');

        if (!isset($this->session->data['balance_sheet_data'])) {
            $this->session->data['error'] = $this->language->get('error_no_data');
            $this->response->redirect($this->url->link('accounts/balance_sheet', 'user_token=' . $this->session->data['user_token'], true));
            return;
        }

        $format = isset($this->request->get['format']) ? $this->db->escape($this->request->get['format']) : 'excel';
        $balance_sheet_data = $this->session->data['balance_sheet_data'];
        $filter_data = $this->session->data['balance_sheet_filter'] ?? array();

        // تسجيل عملية التصدير
        $this->central_service->logActivity('export', 'accounts',
            $this->language->get('log_export_balance_sheet') . ' - ' . strtoupper($format), [
            'user_id' => $this->user->getId(),
            'format' => $format,
            'date_end' => $filter_data['date_end'] ?? ''
        ]);

        // إرسال إشعار للمحاسب الرئيسي
        $this->central_service->sendNotification(
            'balance_sheet_exported',
            $this->language->get('text_export_balance_sheet'),
            $this->language->get('text_balance_sheet_exported_notification') . ' ' . $this->language->get('text_format') . ' ' . strtoupper($format) . ' ' . $this->language->get('text_by') . ' ' . $this->user->getFirstName(),
            [$this->config->get('config_chief_accountant_id')],
            [
                'format' => $format,
                'date_end' => $filter_data['date_end'] ?? '',
                'user_name' => $this->user->getFirstName() . ' ' . $this->user->getLastName()
            ]
        );

        switch ($format) {
            case 'excel':
                $this->exportToExcel($balance_sheet_data, $filter_data);
                break;
            case 'pdf':
                $this->exportToPdf($balance_sheet_data, $filter_data);
                break;
            case 'csv':
                $this->exportToCsv($balance_sheet_data, $filter_data);
                break;
            default:
                $this->exportToExcel($balance_sheet_data, $filter_data);
        }
    }

    public function print() {
        $this->load->language('accounts/balance_sheet');
        $this->load->model('accounts/trial_balance');
    
        $data['title'] = $this->language->get('print_title');
        $data['printdate'] = date('Y-m-d H:i:s');
        $data['user_token'] = $this->session->data['user_token'];
        $data['lang'] = $this->language->get('code');
        $data['direction'] = $this->language->get('direction');  
        $data['whoprint'] = $this->user->getUserName();

        // الحصول على التواريخ ونطاق الحسابات
        $date_start = $this->request->post['start_date'] ?: date('Y-01-01');
        $date_end = $this->request->post['end_date'] ?: date('Y-m-d');
        $account_start = $this->request->post['account_start'] ?: $this->model_accounts_trial_balance->getMinAccountCode();
        $account_end = $this->request->post['account_end'] ?: $this->model_accounts_trial_balance->getMaxAccountCode();
    
        $data['start_date'] = date($this->language->get('date_format_short'), strtotime($date_start));
        $data['end_date'] = date($this->language->get('date_format_short'), strtotime($date_end));
    
        // استرجاع بيانات ميزان المراجعة
        $trial_balance = $this->model_accounts_trial_balance->getAccountRangeData($date_start, $date_end, $account_start, $account_end);
        $accounts = $this->formatAccounts($trial_balance['accounts']);
        $data['accounts'] = $accounts;
        
        // تسجيل بيانات ميزان المراجعة
        //error_log("Trial Balance Data: " . print_r($trial_balance, true));

        // تصفية الحسابات حسب النوع
        $data['assets_non_current'] = $this->filterAccounts($trial_balance['accounts'], 'non_current_assets');
        $data['assets_current'] = $this->filterAccounts($trial_balance['accounts'], 'current_assets');
        $data['liabilities_non_current'] = $this->filterAccounts($trial_balance['accounts'], 'non_current_liabilities');
        $data['liabilities_current'] = $this->filterAccounts($trial_balance['accounts'], 'current_liabilities');
        $data['equity'] = $this->filterAccounts($trial_balance['accounts'], 'equity');

        // التحقق من إقفال حساب 25 (صافي أرباح (خسائر) العام)
        $profit_loss_balance = $this->calculateProfitLossAccount($trial_balance['accounts']);
        
        $profit_loss_account_closed = false;
        foreach ($trial_balance['accounts'] as $account) {
            if ($account['account_code'] == 25 && $account['closing_balance'] != 0) {
                $profit_loss_account_closed = true;
                $data['equity'][] = $account; // تضمين حساب 25 إذا كان مغلقًا
                break;
            }
        }

        if (!$profit_loss_account_closed) {
            // حساب صافي الربح أو الخسارة يدويًا وإضافته إلى الملكية
            $profit_loss_account = [
                'account_code' => 25,
                'name' => $this->language->get('text_net_profit_loss_year'),
                'closing_balance' => $profit_loss_balance,
                'closing_balance_formatted' => $this->currency->format($profit_loss_balance, $this->config->get('config_currency'))
            ];
            
            $data['equity'][] = $profit_loss_account;

            if ($profit_loss_balance < 0) {
                $data['total_equity'] -= abs($profit_loss_balance); // الخسارة تُطرح
            } else {
                $data['total_equity'] += $profit_loss_balance; // الربح يُضاف
            }

            // تسجيل حساب صافي الربح أو الخسارة
            //error_log("Profit/Loss Account (Manually Added): " . print_r($profit_loss_account, true));
        }

        // حساب الإجماليات
        $data['total_assets_non_current'] = $this->calculateTotal($data['assets_non_current']);
        $data['total_assets_current'] = $this->calculateTotal($data['assets_current']);
        $data['total_liabilities_non_current'] = $this->calculateTotal($data['liabilities_non_current']);
        $data['total_liabilities_current'] = $this->calculateTotal($data['liabilities_current']);
        $data['total_equity'] = $this->calculateTotal($data['equity']);

        $data['total_assets'] = $data['total_assets_non_current'] + $data['total_assets_current'];
        $data['total_liabilities'] = $data['total_liabilities_non_current'] + $data['total_liabilities_current'];
        $data['total_equity_liabilities'] = $data['total_liabilities'] + $data['total_equity'];

        // تنسيق الإجماليات
        $data['total_assets_formatted'] = $this->currency->format($data['total_assets'], $this->config->get('config_currency'));
        $data['total_liabilities_formatted'] = $this->currency->format($data['total_liabilities'], $this->config->get('config_currency'));
        $data['total_equity_formatted'] = $this->currency->format($data['total_equity'], $this->config->get('config_currency'));
        $data['total_equity_liabilities_formatted'] = $this->currency->format($data['total_equity_liabilities'], $this->config->get('config_currency'));

        // تسجيل الإجماليات
        //error_log("Total Assets: " . print_r($data['total_assets'], true));
        //error_log("Total Liabilities: " . print_r($data['total_liabilities'], true));
        //error_log("Total Equity: " . print_r($data['total_equity'], true));
        //error_log("Total Equity and Liabilities: " . print_r($data['total_equity_liabilities'], true));

        $this->response->setOutput($this->load->view('accounts/balance_sheet_print', $data));
    }

    /**
     * استثناء الحساب 25 من حسابات الملكية الأساسية
     * والتعامل معه بشكل منفصل بناءً على ما إذا كان مغلقًا أم لا
     */
    private function getAccountCodesByType($type) {
        $account_codes = [];

        switch ($type) {
            case 'non_current_assets':
                $account_codes = [111, 112, 113, 114, 115, 116, 117, 118, 119]; // إضافة المزيد حسب الحاجة
                break;
            case 'current_assets':
                $account_codes = [121, 122, 123, 124, 125, 126, 127, 128];
                break;
            case 'non_current_liabilities':
                $account_codes = [311, 312, 313, 314, 315, 316, 317, 318, 319];
                break;
            case 'current_liabilities':
                $account_codes = [321, 322, 323, 324, 325, 326, 327, 328, 329];
                break;
            case 'equity':
                $account_codes = [21, 22, 23, 24, 26, 27]; // تم إزالة الحساب 25
                break;
        }

        return $account_codes;
    }

    /**
     * تصفية الحسابات بناءً على النوع
     */
    private function filterAccounts($accounts, $type) {
        $filtered_accounts = [];
        $account_codes = $this->getAccountCodesByType($type);

        foreach ($accounts as $account) {
            if (in_array($account['account_code'], $account_codes)) {
                // حساب الأرصدة بشكل صحيح بناءً على نوع الحساب
                if ($type == 'non_current_assets' || $type == 'current_assets') {
                    $account['closing_balance'] = $account['closing_balance_debit'] - $account['closing_balance_credit'];
                } else if ($type == 'non_current_liabilities' || $type == 'current_liabilities' || $type == 'equity') {
                    $account['closing_balance'] = $account['closing_balance_credit'] - $account['closing_balance_debit'];
                }
                $account['closing_balance_formatted'] = $this->currency->format($account['closing_balance'], $this->config->get('config_currency'));
                $filtered_accounts[] = $account;
            }
        }

        // تسجيل الحسابات المفلترة
        //error_log("Filtered Accounts for type {$type}: " . print_r($filtered_accounts, true));

        return $filtered_accounts;
    }

    /**
     * تنسيق الحسابات
     */
    private function formatAccounts($accounts) {
        $formatted_accounts = [];
        foreach ($accounts as $account) {
            $account['closing_balance'] = $account['closing_balance_debit'] - $account['closing_balance_credit'];
            $account['closing_balance_formatted'] = $this->currency->format($account['closing_balance'], $this->config->get('config_currency'));
            $formatted_accounts[] = $account;
        }
        return $formatted_accounts;
    }
    
    /**
     * حساب إجمالي الحسابات
     */
    private function calculateTotal($accounts) {
        $total = 0;
        foreach ($accounts as $account) {
            $total += $account['closing_balance'];
        }

        // تسجيل إجمالي الحسابات
        //error_log("Calculated Total: " . print_r($total, true));

        return $total;
    }

    /**
     * حساب صافي الربح أو الخسارة بناءً على حسابات معينة
     */
    private function calculateProfitLossAccount($accounts) {
        $profit_loss_balance = 0;
        $balance_code_4 = 0;
        $balance_code_5 = 0;
    
        foreach ($accounts as $account) {
            if ($account['account_code'] == 4) {
                $balance_code_4 = $account['closing_balance'];
            } elseif ($account['account_code'] == 5) {
                $balance_code_5 = $account['closing_balance'];
            }
        }

        // قارن بين رصيد حساب 4 ورصيد حساب 5 لتحديد إذا كان ينبغي جمعهما أو خصم أحدهما
        if ($balance_code_5 > $balance_code_4) {
            $profit_loss_balance = ($balance_code_5 + $balance_code_4);
        } elseif ($balance_code_5 < $balance_code_4) {
            $profit_loss_balance = -1 * ($balance_code_5 + $balance_code_4);
        } else { // عندما يكونا متساويين
            $profit_loss_balance = 0;
        }
        // error_log("Profit/Loss Balance: " . print_r($profit_loss_balance, true));

        return $profit_loss_balance * 1;
    }

    /**
     * التحقق من صحة البيانات (من advanced)
     */
    protected function validateForm() {
        if (!$this->user->hasPermission('access', 'accounts/balance_sheet')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }

        if (empty($this->request->post['date_end'])) {
            $this->error['date_end'] = $this->language->get('error_date_end');
        }

        if (isset($this->request->post['show_comparative']) && $this->request->post['show_comparative'] && empty($this->request->post['comparative_date'])) {
            $this->error['comparative_date'] = $this->language->get('error_comparative_date');
        }

        return !$this->error;
    }

    /**
     * إعداد بيانات الفلترة (من advanced)
     */
    protected function prepareFilterData() {
        return array(
            'date_end' => isset($this->request->post['date_end']) ? $this->db->escape($this->request->post['date_end']) : date('Y-m-d'),
            'include_zero_balances' => isset($this->request->post['include_zero_balances']) ? 1 : 0,
            'show_comparative' => isset($this->request->post['show_comparative']) ? 1 : 0,
            'comparative_date' => isset($this->request->post['comparative_date']) ? $this->db->escape($this->request->post['comparative_date']) : '',
            'group_by_type' => isset($this->request->post['group_by_type']) ? 1 : 0,
            'show_percentages' => isset($this->request->post['show_percentages']) ? 1 : 0,
            'currency' => isset($this->request->post['currency']) ? $this->db->escape($this->request->post['currency']) : $this->config->get('config_currency')
        );
    }

    /**
     * عرض النموذج (من advanced)
     */
    protected function getForm() {
        $data['breadcrumbs'] = array();
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('accounts/balance_sheet', 'user_token=' . $this->session->data['user_token'], true)
        );

        $data['action'] = $this->url->link('accounts/balance_sheet/generate', 'user_token=' . $this->session->data['user_token'], true);

        // URLs للدوال المتقدمة
        $data['financial_ratios_url'] = $this->url->link('accounts/balance_sheet/getFinancialRatios', 'user_token=' . $this->session->data['user_token'], true);
        $data['balance_check_url'] = $this->url->link('accounts/balance_sheet/checkBalance', 'user_token=' . $this->session->data['user_token'], true);
        $data['export_url'] = $this->url->link('accounts/balance_sheet/export', 'user_token=' . $this->session->data['user_token'], true);

        // تحميل قوائم البيانات
        $this->load->model('branch/branch');
        $data['branches'] = $this->model_branch_branch->getBranches();

        // القيم الافتراضية
        $data['date_end'] = $this->request->post['date_end'] ?? date('Y-m-d');
        $data['include_zero_balances'] = $this->request->post['include_zero_balances'] ?? false;
        $data['show_comparative'] = $this->request->post['show_comparative'] ?? false;
        $data['comparative_date'] = $this->request->post['comparative_date'] ?? '';

        $data['user_token'] = $this->session->data['user_token'];
        $data['heading_title'] = $this->language->get('heading_title');
        $data['text_no_results'] = $this->language->get('text_no_results');

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/balance_sheet_form', $data));
    }

    /**
     * حساب النسب المالية
     */
    public function getFinancialRatios() {
        $this->load->model('accounts/balance_sheet');

        $json = array();

        if (isset($this->request->get['date_end'])) {
            try {
                $date_end = $this->request->get['date_end'];
                $balance_sheet_data = $this->model_accounts_balance_sheet->getBalanceSheetData(['date_end' => $date_end]);

                // حساب النسب المالية
                $current_assets = $balance_sheet_data['current_assets'] ?? 0;
                $current_liabilities = $balance_sheet_data['current_liabilities'] ?? 0;
                $total_assets = $balance_sheet_data['totals']['total_assets'] ?? 0;
                $total_liabilities = $balance_sheet_data['totals']['total_liabilities'] ?? 0;
                $total_equity = $balance_sheet_data['totals']['total_equity'] ?? 0;
                $quick_assets = $current_assets - ($balance_sheet_data['inventory'] ?? 0);

                $ratios = array(
                    'current_ratio' => ($current_liabilities > 0) ? round($current_assets / $current_liabilities, 2) : 0,
                    'quick_ratio' => ($current_liabilities > 0) ? round($quick_assets / $current_liabilities, 2) : 0,
                    'debt_to_equity' => ($total_equity > 0) ? round($total_liabilities / $total_equity, 2) : 0,
                    'debt_to_assets' => ($total_assets > 0) ? round($total_liabilities / $total_assets, 2) : 0,
                    'equity_ratio' => ($total_assets > 0) ? round($total_equity / $total_assets, 2) : 0
                );

                $json['success'] = true;
                $json['ratios'] = $ratios;

                // تسجيل العملية
                $this->central_service->logActivity('financial_ratios', 'accounts',
                    $this->language->get('log_calculate_financial_ratios_balance_sheet'), [
                    'user_id' => $this->user->getId(),
                    'date_end' => $date_end,
                    'ratios' => $ratios
                ]);

            } catch (Exception $e) {
                $json['error'] = $e->getMessage();
            }
        } else {
            $json['error'] = $this->language->get('error_date_required');
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * فحص التوازن المحاسبي
     */
    public function checkBalance() {
        $this->load->model('accounts/balance_sheet');

        $json = array();

        if (isset($this->request->get['date_end'])) {
            try {
                $date_end = $this->request->get['date_end'];
                $balance_sheet_data = $this->model_accounts_balance_sheet->getBalanceSheetData(['date_end' => $date_end]);

                $total_assets = $balance_sheet_data['totals']['total_assets'] ?? 0;
                $total_liabilities = $balance_sheet_data['totals']['total_liabilities'] ?? 0;
                $total_equity = $balance_sheet_data['totals']['total_equity'] ?? 0;
                $total_liabilities_equity = $total_liabilities + $total_equity;

                $difference = abs($total_assets - $total_liabilities_equity);
                $is_balanced = $difference < 0.01; // تسامح بسيط للأخطاء العشرية

                $json['success'] = true;
                $json['is_balanced'] = $is_balanced;
                $json['total_assets'] = $total_assets;
                $json['total_liabilities_equity'] = $total_liabilities_equity;
                $json['difference'] = $difference;

                if ($is_balanced) {
                    $json['message'] = $this->language->get('text_balance_correct');
                } else {
                    $json['message'] = sprintf($this->language->get('text_balance_error'), number_format($difference, 2));
                }

                // تسجيل العملية
                $this->central_service->logActivity('balance_check', 'accounts',
                    $this->language->get('log_check_accounting_balance_balance_sheet'), [
                    'user_id' => $this->user->getId(),
                    'date_end' => $date_end,
                    'is_balanced' => $is_balanced,
                    'difference' => $difference
                ]);

            } catch (Exception $e) {
                $json['error'] = $e->getMessage();
            }
        } else {
            $json['error'] = $this->language->get('error_date_required');
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * تصدير إلى Excel
     */
    private function exportToExcel($data, $filter_data) {
        $filename = 'balance_sheet_' . date('Y-m-d') . '.xls';

        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        echo '<table border="1">';
        echo '<tr><th colspan="2">' . $this->language->get('heading_title') . '</th></tr>';
        echo '<tr><th>' . $this->language->get('text_as_of') . '</th><td>' . ($filter_data['date_end'] ?? date('Y-m-d')) . '</td></tr>';

        // الأصول
        echo '<tr><th colspan="2">' . $this->language->get('text_assets') . '</th></tr>';
        if (isset($data['assets'])) {
            foreach ($data['assets'] as $asset) {
                echo '<tr><td>' . $asset['account_name'] . '</td><td>' . number_format($asset['balance'], 2) . '</td></tr>';
            }
        }
        echo '<tr><th>' . $this->language->get('text_total_assets') . '</th><td>' . number_format($data['totals']['total_assets'] ?? 0, 2) . '</td></tr>';

        // الخصوم
        echo '<tr><th colspan="2">' . $this->language->get('text_liabilities') . '</th></tr>';
        if (isset($data['liabilities'])) {
            foreach ($data['liabilities'] as $liability) {
                echo '<tr><td>' . $liability['account_name'] . '</td><td>' . number_format($liability['balance'], 2) . '</td></tr>';
            }
        }
        echo '<tr><th>' . $this->language->get('text_total_liabilities') . '</th><td>' . number_format($data['totals']['total_liabilities'] ?? 0, 2) . '</td></tr>';

        // حقوق الملكية
        echo '<tr><th colspan="2">' . $this->language->get('text_equity') . '</th></tr>';
        if (isset($data['equity'])) {
            foreach ($data['equity'] as $equity) {
                echo '<tr><td>' . $equity['account_name'] . '</td><td>' . number_format($equity['balance'], 2) . '</td></tr>';
            }
        }
        echo '<tr><th>' . $this->language->get('text_total_equity') . '</th><td>' . number_format($data['totals']['total_equity'] ?? 0, 2) . '</td></tr>';

        echo '</table>';
        exit;
    }

    /**
     * تصدير إلى PDF
     */
    private function exportToPdf($data, $filter_data) {
        require_once(DIR_SYSTEM . 'library/tcpdf/tcpdf.php');

        $pdf = new TCPDF('P', 'mm', 'A4', true, 'UTF-8');
        $pdf->SetCreator('AYM ERP System');
        $pdf->SetAuthor($this->config->get('config_name'));
        $pdf->SetTitle($this->language->get('heading_title'));

        $pdf->AddPage();
        $pdf->SetFont('dejavusans', 'B', 16);
        $pdf->Cell(0, 10, $this->language->get('heading_title'), 0, 1, 'C');

        $pdf->SetFont('dejavusans', '', 10);
        $html = '<table border="1" cellpadding="4">';
        $html .= '<tr><th colspan="2">' . $this->language->get('text_as_of') . ': ' . ($filter_data['date_end'] ?? date('Y-m-d')) . '</th></tr>';

        // الأصول
        $html .= '<tr style="background-color:#f0f0f0;"><th colspan="2">' . $this->language->get('text_assets') . '</th></tr>';
        if (isset($data['assets'])) {
            foreach ($data['assets'] as $asset) {
                $html .= '<tr><td>' . $asset['account_name'] . '</td><td>' . number_format($asset['balance'], 2) . '</td></tr>';
            }
        }
        $html .= '<tr style="background-color:#e0e0e0;"><th>' . $this->language->get('text_total_assets') . '</th><td>' . number_format($data['totals']['total_assets'] ?? 0, 2) . '</td></tr>';

        // الخصوم
        $html .= '<tr style="background-color:#f0f0f0;"><th colspan="2">' . $this->language->get('text_liabilities') . '</th></tr>';
        if (isset($data['liabilities'])) {
            foreach ($data['liabilities'] as $liability) {
                $html .= '<tr><td>' . $liability['account_name'] . '</td><td>' . number_format($liability['balance'], 2) . '</td></tr>';
            }
        }
        $html .= '<tr style="background-color:#e0e0e0;"><th>' . $this->language->get('text_total_liabilities') . '</th><td>' . number_format($data['totals']['total_liabilities'] ?? 0, 2) . '</td></tr>';

        // حقوق الملكية
        $html .= '<tr style="background-color:#f0f0f0;"><th colspan="2">' . $this->language->get('text_equity') . '</th></tr>';
        if (isset($data['equity'])) {
            foreach ($data['equity'] as $equity) {
                $html .= '<tr><td>' . $equity['account_name'] . '</td><td>' . number_format($equity['balance'], 2) . '</td></tr>';
            }
        }
        $html .= '<tr style="background-color:#e0e0e0;"><th>' . $this->language->get('text_total_equity') . '</th><td>' . number_format($data['totals']['total_equity'] ?? 0, 2) . '</td></tr>';

        $html .= '</table>';

        $pdf->writeHTML($html, true, false, true, false, '');
        $pdf->Output('balance_sheet_' . date('Y-m-d') . '.pdf', 'D');
        exit;
    }

    /**
     * تصدير إلى CSV
     */
    private function exportToCsv($data, $filter_data) {
        $filename = 'balance_sheet_' . $filter_data['date_end'] . '.csv';

        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $output = fopen('php://output', 'w');
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

        fputcsv($output, array(
            $this->language->get('text_account'),
            $this->language->get('text_amount')
        ));

        // إضافة بيانات الأصول
        foreach ($data['assets'] as $asset) {
            fputcsv($output, array(
                $asset['name'],
                $asset['amount']
            ));
        }

        // إضافة بيانات الخصوم
        foreach ($data['liabilities'] as $liability) {
            fputcsv($output, array(
                $liability['name'],
                $liability['amount']
            ));
        }

        fclose($output);
        exit;
    }

    /**
     * التحليل المتقدم للميزانية العمومية مع النسب المالية والمقارنات
     */
    public function advancedAnalysis() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/balance_sheet') ||
            !$this->user->hasKey('accounting_balance_sheet_analysis')) {

            $this->central_service->logActivity('unauthorized_access', 'accounts',
                $this->language->get('log_unauthorized_access_advanced_balance_sheet'), [
                'user_id' => $this->user->getId(),
                'ip_address' => $this->request->server['REMOTE_ADDR']
            ]);

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/balance_sheet');
        $this->document->setTitle($this->language->get('heading_title') . ' - ' . $this->language->get('text_advanced_analysis'));
        $this->load->model('accounts/balance_sheet');

        // إضافة CSS و JavaScript المتقدم للتحليل
        $this->document->addStyle('view/stylesheet/accounts/balance_sheet_analysis.css');
        $this->document->addScript('view/javascript/accounts/balance_sheet_analysis.js');
        $this->document->addScript('view/javascript/jquery/chart.min.js');

        if ($this->request->server['REQUEST_METHOD'] == 'POST') {
            try {
                $date_end = $this->request->post['date_end'];
                $branch_id = $this->request->post['branch_id'] ?? null;

                // إعداد تاريخ المقارنة إذا طُلب
                $comparison_date = null;
                if (!empty($this->request->post['comparison_analysis'])) {
                    $comparison_date = $this->request->post['comparison_date'];
                }

                // تسجيل إنشاء التحليل المتقدم
                $this->central_service->logActivity('generate_advanced_balance_sheet_analysis', 'accounts',
                    $this->language->get('log_generate_advanced_balance_sheet_analysis') . ' ' . $this->language->get('text_as_of') . ': ' . $date_end, [
                    'user_id' => $this->user->getId(),
                    'date_end' => $date_end,
                    'branch_id' => $branch_id,
                    'has_comparison' => !empty($comparison_date)
                ]);

                // الحصول على التحليل المتقدم
                $advanced_analysis = $this->model_accounts_balance_sheet->getAdvancedBalanceSheetAnalysis(
                    $date_end, $comparison_date, $branch_id
                );

                // إرسال إشعار للإدارة المالية
                $this->central_service->sendNotification(
                    'advanced_balance_sheet_analysis_generated',
                    $this->language->get('text_advanced_balance_sheet_analysis'),
                    $this->language->get('text_advanced_balance_sheet_analysis_generated') . ' ' . $this->language->get('text_as_of') . ' ' . $date_end,
                    [$this->config->get('config_cfo_id'), $this->config->get('config_chief_accountant_id')],
                    [
                        'date_end' => $date_end,
                        'total_assets' => $advanced_analysis['balance_verification']['total_assets'],
                        'is_balanced' => $advanced_analysis['balance_verification']['is_balanced'],
                        'liquidity_grade' => $advanced_analysis['liquidity_analysis']['liquidity_grade'],
                        'leverage_grade' => $advanced_analysis['leverage_analysis']['leverage_grade'],
                        'generated_by' => $this->user->getFirstName() . ' ' . $this->user->getLastName()
                    ]
                );

                $this->session->data['success'] = $this->language->get('text_success_analysis');
                $this->session->data['advanced_balance_sheet_analysis'] = $advanced_analysis;

                $this->response->redirect($this->url->link('accounts/balance_sheet/analysisView', 'user_token=' . $this->session->data['user_token'], true));
            } catch (Exception $e) {
                $this->error['warning'] = $e->getMessage();
            }
        }

        // عرض نموذج التحليل المتقدم
        $data['breadcrumbs'] = array();
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('accounts/balance_sheet', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_advanced_analysis'),
            'href' => $this->url->link('accounts/balance_sheet/advancedAnalysis', 'user_token=' . $this->session->data['user_token'], true)
        );

        $data['action'] = $this->url->link('accounts/balance_sheet/advancedAnalysis', 'user_token=' . $this->session->data['user_token'], true);
        $data['cancel'] = $this->url->link('accounts/balance_sheet', 'user_token=' . $this->session->data['user_token'], true);

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/balance_sheet_advanced_analysis_form', $data));
    }

    /**
     * عرض التحليل المتقدم
     */
    public function analysisView() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/balance_sheet') ||
            !$this->user->hasKey('accounting_balance_sheet_view')) {

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/balance_sheet');

        if (!isset($this->session->data['advanced_balance_sheet_analysis'])) {
            $this->session->data['error'] = $this->language->get('error_no_analysis_data');
            $this->response->redirect($this->url->link('accounts/balance_sheet', 'user_token=' . $this->session->data['user_token'], true));
            return;
        }

        // تسجيل عرض التحليل
        $this->central_service->logActivity('view_advanced_balance_sheet_analysis', 'accounts',
            $this->language->get('log_view_advanced_balance_sheet_analysis'), [
            'user_id' => $this->user->getId()
        ]);

        $data = $this->session->data['advanced_balance_sheet_analysis'];

        $data['breadcrumbs'] = array();
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('accounts/balance_sheet', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_analysis_view'),
            'href' => $this->url->link('accounts/balance_sheet/analysisView', 'user_token=' . $this->session->data['user_token'], true)
        );

        $data['export_excel'] = $this->url->link('accounts/balance_sheet/export', 'user_token=' . $this->session->data['user_token'] . '&format=excel', true);
        $data['export_pdf'] = $this->url->link('accounts/balance_sheet/export', 'user_token=' . $this->session->data['user_token'] . '&format=pdf', true);
        $data['print'] = $this->url->link('accounts/balance_sheet/analysisPrint', 'user_token=' . $this->session->data['user_token'], true);
        $data['back'] = $this->url->link('accounts/balance_sheet', 'user_token=' . $this->session->data['user_token'], true);

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/balance_sheet_advanced_analysis_view', $data));
    }

    /**
     * طباعة التحليل المتقدم
     */
    public function analysisPrint() {
        $this->load->language('accounts/balance_sheet');

        if (!isset($this->session->data['advanced_balance_sheet_analysis'])) {
            $this->response->redirect($this->url->link('accounts/balance_sheet', 'user_token=' . $this->session->data['user_token'], true));
            return;
        }

        $data = $this->session->data['advanced_balance_sheet_analysis'];
        $data['company_name'] = $this->config->get('config_name');
        $data['generated_date'] = date('Y-m-d H:i:s');
        $data['generated_by'] = $this->user->getFirstName() . ' ' . $this->user->getLastName();

        $this->response->setOutput($this->load->view('accounts/balance_sheet_advanced_analysis_print', $data));
    }

    /**
     * فحص التوازن المحاسبي السريع (AJAX)
     */
    public function quickBalanceCheck() {
        $this->load->language('accounts/balance_sheet');
        $this->load->model('accounts/balance_sheet');

        $json = array();

        if ($this->request->server['REQUEST_METHOD'] == 'POST') {
            $date_end = $this->request->post['date_end'] ?? date('Y-m-d');
            $branch_id = $this->request->post['branch_id'] ?? null;

            try {
                $balance_sheet_data = $this->model_accounts_balance_sheet->getBalanceSheetData($date_end, $branch_id);
                $balance_check = $this->model_accounts_balance_sheet->verifyBalanceSheetBalance($balance_sheet_data);

                $json['success'] = true;
                $json['balance_check'] = $balance_check;
                $json['message'] = $balance_check['is_balanced'] ?
                    $this->language->get('text_balance_verified') :
                    $this->language->get('text_balance_unverified');

            } catch (Exception $e) {
                $json['success'] = false;
                $json['error'] = $e->getMessage();
            }
        } else {
            $json['success'] = false;
            $json['error'] = $this->language->get('error_invalid_request');
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * دالة تنظيف المخرجات (CONSTITUTIONAL REQUIREMENT)
     * Sanitize all output data to prevent XSS attacks
     */
    private function sanitizeOutputData($data) {
        if (is_array($data)) {
            foreach ($data as $key => $value) {
                $data[$key] = $this->sanitizeOutputData($value);
            }
        } elseif (is_string($data)) {
            $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
        }
        return $data;
    }
}
