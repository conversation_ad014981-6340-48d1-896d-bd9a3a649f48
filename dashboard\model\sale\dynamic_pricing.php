<?php
class ModelSaleDynamicPricing extends Model {
    
    public function addPricingRule($data) {
        $this->db->query("INSERT INTO " . DB_PREFIX . "dynamic_pricing_rules SET 
            name = '" . $this->db->escape($data['name']) . "',
            rule_type = '" . $this->db->escape($data['rule_type']) . "',
            condition_type = '" . $this->db->escape($data['condition_type']) . "',
            condition_value = '" . $this->db->escape($data['condition_value']) . "',
            action_type = '" . $this->db->escape($data['action_type']) . "',
            action_value = '" . (float)$data['action_value'] . "',
            priority = '" . (int)$data['priority'] . "',
            status = '" . (int)$data['status'] . "',
            date_added = NOW()");
        
        return $this->db->getLastId();
    }
    
    public function updatePricingRule($rule_id, $data) {
        $this->db->query("UPDATE " . DB_PREFIX . "dynamic_pricing_rules SET 
            name = '" . $this->db->escape($data['name']) . "',
            rule_type = '" . $this->db->escape($data['rule_type']) . "',
            condition_type = '" . $this->db->escape($data['condition_type']) . "',
            condition_value = '" . $this->db->escape($data['condition_value']) . "',
            action_type = '" . $this->db->escape($data['action_type']) . "',
            action_value = '" . (float)$data['action_value'] . "',
            priority = '" . (int)$data['priority'] . "',
            status = '" . (int)$data['status'] . "',
            date_modified = NOW()
            WHERE rule_id = '" . (int)$rule_id . "'");
    }
    
    public function deletePricingRule($rule_id) {
        $this->db->query("DELETE FROM " . DB_PREFIX . "dynamic_pricing_rules WHERE rule_id = '" . (int)$rule_id . "'");
    }
    
    public function getPricingRule($rule_id) {
        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "dynamic_pricing_rules WHERE rule_id = '" . (int)$rule_id . "'");
        return $query->row;
    }
    
    public function getPricingRules($data = array()) {
        $sql = "SELECT * FROM " . DB_PREFIX . "dynamic_pricing_rules";
        
        $implode = array();
        
        if (!empty($data['filter_name'])) {
            $implode[] = "name LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }
        
        if (!empty($data['filter_rule_type'])) {
            $implode[] = "rule_type = '" . $this->db->escape($data['filter_rule_type']) . "'";
        }
        
        if (isset($data['filter_status']) && $data['filter_status'] !== '') {
            $implode[] = "status = '" . (int)$data['filter_status'] . "'";
        }
        
        if ($implode) {
            $sql .= " WHERE " . implode(" AND ", $implode);
        }
        
        $sql .= " ORDER BY priority ASC, date_added DESC";
        
        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }
            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }
            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }
        
        $query = $this->db->query($sql);
        return $query->rows;
    }
    
    public function getTotalPricingRules($data = array()) {
        $sql = "SELECT COUNT(*) AS total FROM " . DB_PREFIX . "dynamic_pricing_rules";
        
        $implode = array();
        
        if (!empty($data['filter_name'])) {
            $implode[] = "name LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }
        
        if (!empty($data['filter_rule_type'])) {
            $implode[] = "rule_type = '" . $this->db->escape($data['filter_rule_type']) . "'";
        }
        
        if (isset($data['filter_status']) && $data['filter_status'] !== '') {
            $implode[] = "status = '" . (int)$data['filter_status'] . "'";
        }
        
        if ($implode) {
            $sql .= " WHERE " . implode(" AND ", $implode);
        }
        
        $query = $this->db->query($sql);
        return $query->row['total'];
    }
    
    public function getProductPrice($product_id) {
        $query = $this->db->query("SELECT price FROM " . DB_PREFIX . "product WHERE product_id = '" . (int)$product_id . "'");
        return $query->row ? $query->row['price'] : 0;
    }
    
    public function calculateDynamicPrice($product_id, $customer_id, $quantity = 1) {
        // الحصول على السعر الأساسي للمنتج
        $base_price = $this->getProductPrice($product_id);
        $final_price = $base_price;
        
        // الحصول على قواعد التسعير النشطة
        $rules = $this->getActivePricingRules();
        
        foreach ($rules as $rule) {
            if ($this->checkRuleCondition($rule, $product_id, $customer_id, $quantity)) {
                $final_price = $this->applyRuleAction($rule, $final_price);
            }
        }
        
        return max(0, $final_price); // التأكد من أن السعر لا يكون سالب
    }
    
    private function getActivePricingRules() {
        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "dynamic_pricing_rules 
                                   WHERE status = 1 
                                   ORDER BY priority ASC, date_added DESC");
        return $query->rows;
    }
    
    private function checkRuleCondition($rule, $product_id, $customer_id, $quantity) {
        switch ($rule['condition_type']) {
            case 'quantity':
                return $quantity >= (int)$rule['condition_value'];
                
            case 'customer_group':
                $customer_group = $this->getCustomerGroup($customer_id);
                return $customer_group == $rule['condition_value'];
                
            case 'product_category':
                $product_categories = $this->getProductCategories($product_id);
                return in_array($rule['condition_value'], $product_categories);
                
            case 'total_amount':
                $cart_total = $this->getCartTotal($customer_id);
                return $cart_total >= (float)$rule['condition_value'];
                
            case 'time_of_day':
                $current_hour = (int)date('H');
                $condition_hours = explode('-', $rule['condition_value']);
                return $current_hour >= (int)$condition_hours[0] && $current_hour <= (int)$condition_hours[1];
                
            case 'day_of_week':
                $current_day = date('N'); // 1 (Monday) to 7 (Sunday)
                return $current_day == (int)$rule['condition_value'];
                
            default:
                return false;
        }
    }
    
    private function applyRuleAction($rule, $current_price) {
        switch ($rule['action_type']) {
            case 'percentage':
                $discount = $current_price * ((float)$rule['action_value'] / 100);
                return $current_price - $discount;
                
            case 'fixed_amount':
                return $current_price - (float)$rule['action_value'];
                
            case 'multiply':
                return $current_price * (float)$rule['action_value'];
                
            case 'set_price':
                return (float)$rule['action_value'];
                
            default:
                return $current_price;
        }
    }
    
    private function getCustomerGroup($customer_id) {
        $query = $this->db->query("SELECT customer_group_id FROM " . DB_PREFIX . "customer 
                                   WHERE customer_id = '" . (int)$customer_id . "'");
        return $query->row ? $query->row['customer_group_id'] : 0;
    }
    
    private function getProductCategories($product_id) {
        $query = $this->db->query("SELECT category_id FROM " . DB_PREFIX . "product_to_category 
                                   WHERE product_id = '" . (int)$product_id . "'");
        $categories = array();
        foreach ($query->rows as $row) {
            $categories[] = $row['category_id'];
        }
        return $categories;
    }
    
    private function getCartTotal($customer_id) {
        // يمكن تطوير هذه الدالة لتجلب إجمالي سلة المشتريات للعميل
        return 0; // مؤقتاً
    }
    
    public function getPricingAnalytics() {
        $analytics = array();
        
        // إحصائيات قواعد التسعير
        $query = $this->db->query("SELECT 
            COUNT(*) as total_rules,
            SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as active_rules,
            SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as inactive_rules
            FROM " . DB_PREFIX . "dynamic_pricing_rules");
        
        $analytics['rules_stats'] = $query->row;
        
        // توزيع أنواع القواعد
        $query = $this->db->query("SELECT rule_type, COUNT(*) as count 
                                   FROM " . DB_PREFIX . "dynamic_pricing_rules 
                                   GROUP BY rule_type");
        $analytics['rule_types'] = $query->rows;
        
        // توزيع أنواع الإجراءات
        $query = $this->db->query("SELECT action_type, COUNT(*) as count 
                                   FROM " . DB_PREFIX . "dynamic_pricing_rules 
                                   GROUP BY action_type");
        $analytics['action_types'] = $query->rows;
        
        return $analytics;
    }
    
    public function bulkUpdatePrices($data) {
        $updated = 0;
        
        foreach ($data['products'] as $product_id) {
            $new_price = $this->calculateDynamicPrice($product_id, $data['customer_id'], $data['quantity']);
            
            $this->db->query("UPDATE " . DB_PREFIX . "product SET 
                price = '" . (float)$new_price . "',
                date_modified = NOW()
                WHERE product_id = '" . (int)$product_id . "'");
            
            $updated++;
        }
        
        return $updated;
    }
} 