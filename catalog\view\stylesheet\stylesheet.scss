body {
  font-family: 'Open Sans', sans-serif;
  font-weight: 400;
  color: #666;
  font-size: 12px;
  line-height: 20px;
  width: 100%;
}

h1, h2, h3, h4, h5, h6 {
  color: #444;
}

/* Override the bootstrap defaults */
h1 {
  font-size: 33px;
}

h2 {
  font-size: 27px;
}

h3 {
  font-size: 21px;
}

h4 {
  font-size: 15px;
}

footer h5 {
  font-size: 12px;
}

h6 {
  font-size: 10.2px;
}

a {
  color: #23a1d1;
  text-decoration: none;

  &:hover {
    text-decoration: none;
  }
}

/* Chrome border line */
button:focus {
  outline: none !important;
}

legend {
  font-size: 18px;
  padding: 7px 0;
  margin-bottom: 20px;
  border-bottom: 1px solid #e5e5e5;
}

label {
  font-size: 12px;
  font-weight: normal;
}

.list-unstyled a {
  text-decoration: none;
}

.nav-tabs {
  margin-bottom: 15px;
}

div.required .col-form-label:before, div.required .form-label:before {
  content: '* ';
  color: #F00;
  font-weight: bold;
}

.form-switch-lg {
  font-size: 20px;
  min-height: 30px;
  line-height: 30px;
}

@media (min-width: 768px) {
  .col-form-label {
    text-align: right;
  }
}

#alert {
  z-index: 9999;
  pointer-events: all;
}

#alert .alert {
  min-width: 400px;
  position: relative;
  margin-bottom: 15px;
}

@media (min-width: 1300px) {
  #alert .alert {
    right: 50px;
  }
}

@media (min-width: 1400px) {
  #alert .alert {
    right: 0px;
  }
}

@media (min-width: 1600px) {
  #alert .alert {
    right: 100px;
  }
}

@media (min-width: 1800px) {
  #alert .alert {
    right: 200px;
  }
}

@media (min-width: 2000px) {
  #alert .alert {
    right: 300px;
  }
}

@media (min-width: 2200px) {
  #alert .alert {
    right: 400px;
  }
}

@media (min-width: 2400px) {
  #alert .alert {
    right: 500px;
  }
}

@media (min-width: 2600px) {
  #alert .alert {
    right: 600px;
  }
}

@media (min-width: 2800px) {
  #alert .alert {
    right: 700px;
  }
}

@media (min-width: 3000px) {
  #alert .alert {
    right: 800px;
  }
}

@media (min-width: 3200px) {
  #alert .alert {
    right: 900px;
  }
}

@media (min-width: 3400px) {
  #alert .alert {
    right: 1000px;
  }
}

@media (min-width: 3600px) {
  #alert .alert {
    right: 1100px;
  }
}

@media (min-width: 3800px) {
  #alert .alert {
    right: 1200px;
  }
}

@media (min-width: 4000px) {
  #alert .alert {
    right: 1300px;
  }
}

/* top */
#top {
  background-color: #EEEEEE;
  border-bottom: 1px solid #e2e2e2;
  padding: 10px 0;
  margin: 0 0 20px 0;
  min-height: 44px;
  position: relative;

  .nav {
    > .list-inline {
      > .list-inline-item, .list-inline-item > a, .list-inline-item .dropdown > a {
        color: #888;
        text-shadow: 0 1px 0 #FFF;
      }
    }
  }

  .btn-link {
    color: #888;
    text-shadow: 0 1px 0 #FFF;
    text-decoration: none;

    &:hover {
      color: #444;
    }
  }
}

#top a {
  font-size: 1.1em;
  text-decoration: none;
}

footer a {
  font-size: 1.1em;
  text-decoration: none;
}

/* logo */
#logo {
  margin: 0 0 10px 0;

  img {
    max-width: 200px;
  }
}

/* search */
#search {
  margin-bottom: 10px;

  .form-control-lg {
    height: 40px;
    font-size: 12px;
    line-height: 20px;
    padding: 0 10px;
  }

  .btn-lg {
    font-size: 15px;
    line-height: 18px;
    padding: 0.57rem 35px;
    text-shadow: 0 1px 0 #FFF;
  }
}

/* cart */
#header-cart {
  margin-bottom: 10px;

  .btn-lg {
    color: #FFF;
    height: 40px;
    padding: 0 1rem;
  }

  .btn:hover {
    color: #FFF;
  }

  &.open {
    > .btn {
      background-image: none;
      background-color: #FFFFFF;
      border: 1px solid #E6E6E6;
      color: #666;
      box-shadow: none;
      text-shadow: none;

      &:hover {
        color: #444;
      }
    }
  }

  .dropdown-menu {
    background: #eee;
    z-index: 1001;
    min-width: 100%;

    table {
      margin-bottom: 10px;

    }

    li {
      min-width: 427px;
      padding: 0 10px;

      p {
        margin: 20px 0;
      }
    }

    @media (max-width: 478px) {
      width: 100%;
      li {
        > div {
          min-width: 100%;
        }
      }
    }
  }

  .table-striped {
    > tbody {
      > tr {
        &:nth-of-type(odd) {
          background-color: #f9f9f9;
        }
      }
    }
  }
}

/* menu */
#menu {
  background-color: #229ac8;
  background-image: linear-gradient(to bottom, #23a1d1, #1f90bb);
  background-repeat: repeat-x;
  border: 1px solid #1f90bb;
  border-color: #1f90bb #1f90bb #145e7a;
  min-height: 40px;
  border-radius: 4px;

  &.navbar {
    padding: 0 1rem;
    margin-bottom: 20px;
  }

  .dropdown-menu {
    padding-bottom: 0;
    margin-top: 0;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
  }

  .dropdown-inner {
    display: table;

    ul {
      display: table-cell;

      li {
        a {
          &:hover {
            color: #ffffff;
            background-image: linear-gradient(to bottom, #23a1d1, #1f90bb);
          }
        }
      }
    }

    a {
      min-width: 160px;
      display: block;
      padding: 3px 20px;
      clear: both;
      line-height: 20px;
      color: #333333;
      font-size: 12px;
    }
  }

  .see-all {
    display: block;
    margin-top: 0.5em;
    border-top: 1px solid #DDD;
    padding: 3px 20px;
    -webkit-border-radius: 0 0 4px 4px;
    -moz-border-radius: 0 0 4px 4px;
    border-radius: 0 0 3px 3px;
    font-size: 12px;

    &:hover,
    &:focus {
      text-decoration: none;
      color: #ffffff;
      background-color: #229ac8;
      background-image: linear-gradient(to bottom, #23a1d1, #1f90bb);
      background-repeat: repeat-x;
    }
  }

  #category {
    float: left;
    font-size: 16px;
    font-weight: 700;
    line-height: 40px;
    color: #fff;
    text-shadow: 0 1px 0 rgba(0, 0, 0, 0.2);
  }

  .navbar-toggler {
    i {
      color: #fff;
      border-color: #fff;
      font-size: 0.9em;
    }
  }

  .navbar-nav {
    > li {
      > a {
        color: #fff;
        text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
        padding: 10px 15px 10px 15px;
        min-height: 15px;
        background-color: transparent;

        &:hover {
          background-color: rgba(0, 0, 0, 0.1);
        }
      }
    }
  }

  @media (min-width: 768px) {
    .dropdown {
      &:hover {
        .dropdown-menu {
          display: block;
        }
      }
    }
  }
  @media (max-width: 767px) {
    border-radius: 4px;
    div {
      &.dropdown-inner {
        > ul {
          .list-unstyled {
            display: block;
          }
        }
      }

      &.dropdown-menu {
        margin-left: 0 !important;
        padding-bottom: 10px;
        background-color: rgba(0, 0, 0, 0.1);
      }
    }
    .dropdown-inner {
      display: block;

      a {
        width: 100%;
        color: #fff;
      }
    }
    .dropdown-menu {
      a {
        &:hover {
          background: rgba(0, 0, 0, 0.1);
        }
      }

      ul {
        li {
          a {
            :hover {
              background: rgba(0, 0, 0, 0.1);
            }
          }
        }
      }
    }
    .see-all {
      margin-top: 0;
      border: none;
      border-radius: 0;
      color: #fff;
    }
  }
}

/* content */
#content {
  min-height: 600px;
}

/* footer */
footer {
  margin-top: 30px;
  padding-top: 30px;
  padding-bottom: 1px;
  background-color: #303030;
  border-top: 1px solid #ddd;
  color: #e2e2e2;

  hr {
    border-top: none;
    border-bottom: 1px solid #666;
  }

  a {
    color: #ccc;

    &:hover {
      color: #fff;
    }
  }

  h5 {
    font-family: 'Open Sans', sans-serif;
    font-size: 13px;
    font-weight: bold;
    color: #fff;
    margin-top: 10px;
    margin-bottom: 10px;
  }
}

/* breadcrumb */
.breadcrumb {
  margin: 0 0 20px 0;
  padding: 8px 0;
  border: 1px solid #ddd;
  background-color: #f5f5f5;

  i {
    font-size: 15px;
  }

  > li.breadcrumb-item {
    text-shadow: 0 1px 0 #FFF;
    padding: 0 20px;
    position: relative;
    white-space: nowrap;

    > a {
      text-decoration: none;
    }

    &:after {
      content: '';
      display: block;
      position: absolute;
      top: -3px;
      right: -5px;
      width: 26px;
      height: 26px;
      border-right: 1px solid #DDD;
      border-bottom: 1px solid #DDD;
      -webkit-transform: rotate(-45deg);
      -moz-transform: rotate(-45deg);
      -o-transform: rotate(-45deg);
      transform: rotate(-45deg);
    }

    + li {
      &:before {
        content: '';
        padding: 0;
      }
    }
  }
}

.pagination {
  margin: 0;
}

/* buttons */
.btn-light {
  color: #777;
  text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);
  background-image: linear-gradient(to bottom, #eeeeee, #dddddd);
  background-repeat: repeat-x;
  border-color: #dddddd #dddddd #b3b3b3 #b7b7b7;

  &:hover,
  &:focus,
  &:active,
  &.active,
  &.disabled,
  &[disabled] {
    color: #333;
    background-color: #e6e6e6;
    border-color: #adadad;
    background-position: 0;
  }
}

.btn-primary {
  color: #ffffff;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
  background-image: linear-gradient(to bottom, #23a1d1, #1f90bb);
  background-repeat: repeat-x;
  border-color: #1f90bb #1f90bb #145e7a;

  &:hover,
  &:focus,
  &:active,
  &.active,
  &.disabled,
  &[disabled] {
    background-position: 0;
  }
}

.btn-warning {
  color: #ffffff;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
  background-image: linear-gradient(to bottom, #fbb450, #f89406);
  background-repeat: repeat-x;
  border-color: #f89406 #f89406 #ad6704;

  &:hover,
  &:focus,
  &:active,
  &.active,
  &.disabled,
  &[disabled] {
    box-shadow: inset 0 1000px 0 rgba(0, 0, 0, 0.1);
  }
}

.btn-danger {
  color: #ffffff;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
  background-image: linear-gradient(to bottom, #ee5f5b, #bd362f);
  background-repeat: repeat-x;
  border-color: #bd362f #bd362f #802420;

  &:hover,
  &:focus,
  &:active,
  &.active,
  &.disabled,
  &[disabled] {
    box-shadow: inset 0 1000px 0 rgba(0, 0, 0, 0.1);
  }
}

.btn-success {
  color: #ffffff;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
  background-image: linear-gradient(to bottom, #62c462, #51a351);
  background-repeat: repeat-x;
  border-color: #51a351 #51a351 #387038;

  &:hover,
  &:focus,
  &:active,
  &.active,
  &.disabled,
  &[disabled] {
    box-shadow: inset 0 1000px 0 rgba(0, 0, 0, 0.1);
  }
}

.btn-info {
  color: #ffffff;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
  background-image: linear-gradient(to bottom, #e06342, #dc512c);
  background-repeat: repeat-x;
  border-color: #dc512c #dc512c #a2371a;

  &:hover,
  &:focus,
  &:active,
  &.active,
  &.disabled,
  &[disabled] {
    background-image: none;
    background-color: #df5c39;
  }
}

.btn-link {
  border-color: rgba(0, 0, 0, 0);
  cursor: pointer;
  color: #23A1D1;
  border-radius: 0;

  &,
  &:active,
  &[disabled] {
    background-color: rgba(0, 0, 0, 0);
    background-image: none;
    box-shadow: none;
  }
}

.btn-inverse {
  color: #ffffff;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
  background-color: #363636;
  background-image: linear-gradient(to bottom, #444444, #222222);
  background-repeat: repeat-x;
  border-color: #222222 #222222 #000000;

  &:hover,
  &:focus,
  &:active,
  &.active,
  &.disabled,
  &[disabled] {
    background-color: #222222;
    background-image: linear-gradient(to bottom, #333333, #111111);
  }
}

.product-thumb {
  border: 1px solid #ddd;
  margin-bottom: 15px;

  h4 {
    font-weight: bold;
  }

  .image {
    text-align: center;
    margin-bottom: 15px;

    a {
      &:hover {
        opacity: 0.8;
      }
    }
  }

  .description {
    padding: 15px;
  }

  .button-group {
    display: flex;
    border-top: 1px solid #ddd;
    background-color: #eee;

    button {
      flex: 33%;
      border-radius: 0;
      display: inline-block;
      border: none;
      background-color: #eee;
      color: #888;
      line-height: 38px;
      font-weight: bold;
      text-align: center;
      text-transform: uppercase;

      &:hover {
        color: #444;
        background-color: #ddd;
        text-decoration: none;
        cursor: pointer;
      }

      + button {
        border-left: 1px solid #ddd;
      }
    }
  }
}

@media (min-width: 960px) {
  .product-list .product-thumb {
    display: flex;

    .image {
      flex-direction: column;
      margin-bottom: 0px;
    }

    .content {
      flex-direction: column;
      flex: 75%;
      position: relative;
    }

    .button-group {
      position: absolute;
      bottom: 0px;
      width: 100%;
      border-left: 1px solid #ddd;
    }
  }
}

.rating {
  padding-bottom: 10px;

  .fa-stack {
    width: 20px;
  }

  &.fa-star {
    color: #999;
    font-size: 15px;
  }

  .fa-star {
    color: #FC0;
    font-size: 15px;

    + .fa-star {
      color: #E69500;
    }
  }
}

/* product list */
.price {
  color: #444;
}

.price-new {
  font-weight: 600;
}

.price-old {
  color: #dc512c;
  text-decoration: line-through;
}

.price-tax {
  color: #999;
  font-size: 12px;
  display: block;
}

/* BS4 Changes */
.navbar-light {
  .navbar-toggler {
    font-size: 15px;
    font-stretch: expanded;
    color: #FFF;
    padding: 6px 12px;
    background-color: #229ac8;
    background-image: linear-gradient(to bottom, #23a1d1, #1f90bb);
    background-repeat: repeat-x;
    border-color: #1f90bb #1f90bb #145e7a;
  }
}

.form-check {
  .form-check-input {
    margin-top: 0.25rem;
  }
}

/* Theme Custom CSS */
#display-control {
  #compare-total {
    &.a {
      margin-top: -2px;
      padding: 0.35rem 0.565rem;
    }
  }
}

#product-product {
  h1 {
    margin-top: 20px;
    margin-bottom: 10px;
  }
}

#information-contact {
  .card {
    margin-bottom: 20px;
  }
}

#cookie {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 150px;
  z-index: 9999;
  opacity: 0.95;
  color: #ecf0f1;
  background: #343a40;
}

#cookie div {
  font-size: 16px;
  color: #FFFFFF;
}
