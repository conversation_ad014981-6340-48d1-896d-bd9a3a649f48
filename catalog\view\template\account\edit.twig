{{ header }}
<div id="account-edit" class="container">
  <div class="row">{{ column_left }}
    <div id="content" class="col" style="margin-top:10px">{{ content_top }}
      <form id="form-customer" action="{{ save }}" method="post" data-oc-toggle="ajax">
        <fieldset>
          <div class="row mb-3 required">
            <div class="col-sm-12">
              <input type="text" name="firstname" value="{{ firstname }}" placeholder="{{ entry_firstname }}" id="input-firstname" class="form-control" />
              <div id="error-firstname" class="invalid-feedback"></div>
            </div>
          </div>
          <div class="row mb-3 required">
            <div class="col-sm-12">
              <input type="text" name="lastname" value="{{ lastname }}" placeholder="{{ entry_lastname }}" id="input-lastname" class="form-control" autocomplete="new-password"/>
              <div id="error-lastname" class="invalid-feedback"></div>
            </div>
          </div>
          <div class="row mb-3 required">
            <div class="col-sm-12">
              <input type="email" name="email" value="{{ email }}" placeholder="{{ entry_email }}" id="input-email" class="form-control" autocomplete="new-password"/>
              <div id="error-email" class="invalid-feedback"></div>
            </div>
          </div>


          {% for custom_field in custom_fields %}

            {% if custom_field.type == 'select' %}
              <div class="row mb-3{% if custom_field.required %} required{% endif %} custom-field">
                <label for="input-custom-field-{{ custom_field.custom_field_id }}" class="col-sm-2 col-form-label">{{ custom_field.name }}</label>
                <div class="col-sm-10">
                  <select name="custom_field[{{ custom_field.custom_field_id }}]" id="input-custom-field-{{ custom_field.custom_field_id }}" class="form-select">
                    <option value="">{{ text_select }}</option>
                    {% for custom_field_value in custom_field.custom_field_value %}
                      <option value="{{ custom_field_value.custom_field_value_id }}"{% if account_custom_field[custom_field.custom_field_id] and custom_field_value.custom_field_value_id == account_custom_field[custom_field.custom_field_id] %} selected{% endif %}>{{ custom_field_value.name }}</option>
                    {% endfor %}
                  </select>
                  <div id="error-custom-field-{{ custom_field.custom_field_id }}" class="invalid-feedback"></div>
                </div>
              </div>
            {% endif %}

            {% if custom_field.type == 'radio' %}
              <div class="row mb-3{% if custom_field.required %} required{% endif %} custom-field">
                <label class="col-sm-2 col-form-label">{{ custom_field.name }}</label>
                <div class="col-sm-10">
                  <div id="input-custom-field-{{ custom_field.custom_field_id }}">
                    {% for custom_field_value in custom_field.custom_field_value %}
                      <div class="form-check">
                        <input type="radio" name="custom_field[{{ custom_field.custom_field_id }}]" value="{{ custom_field_value.custom_field_value_id }}" id="input-custom-value-{{ custom_field_value.custom_field_value_id }}" class="form-check-input"{% if account_custom_field[custom_field.custom_field_id] and custom_field_value.custom_field_value_id == account_custom_field[custom_field.custom_field_id] %} checked{% endif %}/> <label for="input-custom-value-{{ custom_field_value.custom_field_value_id }}" class="form-check-label">{{ custom_field_value.name }}</label>
                      </div>
                    {% endfor %}
                  </div>
                  <div id="error-custom-field-{{ custom_field.custom_field_id }}" class="invalid-feedback"></div>
                </div>
              </div>
            {% endif %}

            {% if custom_field.type == 'checkbox' %}
              <div class="row mb-3{% if custom_field.required %} required{% endif %} custom-field">
                <label class="col-sm-2 col-form-label">{{ custom_field.name }}</label>
                <div class="col-sm-10">
                  <div id="input-custom-field-{{ custom_field.custom_field_id }}">
                    {% for custom_field_value in custom_field.custom_field_value %}
                      <div class="form-check">
                        <input type="checkbox" name="custom_field[{{ custom_field.custom_field_id }}][]" value="{{ custom_field_value.custom_field_value_id }}" id="input-custom-value-{{ custom_field_value.custom_field_value_id }}" class="form-check-input"{% if account_custom_field[custom_field.custom_field_id] and custom_field_value.custom_field_value_id in account_custom_field[custom_field.custom_field_id] %} checked{% endif %}/> <label for="input-custom-value-{{ custom_field_value.custom_field_value_id }}" class="form-check-label">{{ custom_field_value.name }}</label>
                      </div>
                    {% endfor %}
                  </div>
                  <div id="error-custom-field-{{ custom_field.custom_field_id }}" class="invalid-feedback"></div>
                </div>
              </div>
            {% endif %}

            {% if custom_field.type == 'text' %}
              <div class="row mb-3{% if custom_field.required %} required{% endif %} custom-field">
                <label for="input-custom-field-{{ custom_field.custom_field_id }}" class="col-sm-2 col-form-label">{{ custom_field.name }}</label>
                <div class="col-sm-10">
                  <input type="text" name="custom_field[{{ custom_field.custom_field_id }}]" value="{% if account_custom_field[custom_field.custom_field_id] %}{{ account_custom_field[custom_field.custom_field_id] }}{% else %}{{ custom_field.value }}{% endif %}" placeholder="{{ custom_field.name }}" id="input-custom-field-{{ custom_field.custom_field_id }}" class="form-control"/>
                  <div id="error-custom-field-{{ custom_field.custom_field_id }}" class="invalid-feedback"></div>
                </div>
              </div>
            {% endif %}

            {% if custom_field.type == 'textarea' %}
              <div class="row mb-3{% if custom_field.required %} required{% endif %} custom-field">
                <label for="input-custom-field-{{ custom_field.custom_field_id }}" class="col-sm-2 col-form-label">{{ custom_field.name }}</label>
                <div class="col-sm-10">
                  <textarea name="custom_field[{{ custom_field.custom_field_id }}]" rows="5" placeholder="{{ custom_field.name }}" id="input-custom-field-{{ custom_field.custom_field_id }}" class="form-control">{% if account_custom_field[custom_field.custom_field_id] %}{{ account_custom_field[custom_field.custom_field_id] }}{% else %}{{ custom_field.value }}{% endif %}</textarea>
                  <div id="error-custom-field-{{ custom_field.custom_field_id }}" class="invalid-feedback"></div>
                </div>
              </div>
            {% endif %}

            {% if custom_field.type == 'file' %}
              <div class="row mb-3{% if custom_field.required %} required{% endif %} custom-field">
                <label class="col-sm-2 col-form-label">{{ custom_field.name }}</label>
                <div class="col-sm-10">
                  <div>
                    <button type="button" data-oc-toggle="upload" data-oc-url="{{ upload }}" data-oc-size-max="{{ config_file_max_size }}" data-oc-size-error="{{ error_upload_size }}" data-oc-target="#input-custom-field-{{ custom_field.custom_field_id }}" class="btn btn-light"><i class="fa-solid fa-upload"></i> {{ button_upload }}</button>
                    <input type="hidden" name="custom_field[{{ custom_field.custom_field_id }}]" value="{% if account_custom_field[custom_field.custom_field_id] %}{{ account_custom_field[custom_field.custom_field_id] }}{% endif %}" id="input-custom-field-{{ custom_field.custom_field_id }}"/>
                  </div>
                  <div id="error-custom-field-{{ custom_field.custom_field_id }}" class="invalid-feedback"></div>
                </div>
              </div>
            {% endif %}

            {% if custom_field.type == 'date' %}
              <div class="row mb-3{% if custom_field.required %} required{% endif %} custom-field">
                <label for="input-custom-field-{{ custom_field.custom_field_id }}" class="col-sm-2 col-form-label">{{ custom_field.name }}</label>
                <div class="col-sm-10">
                  <div class="input-group">
                    <input type="text" name="custom_field[{{ custom_field.custom_field_id }}]" value="{% if account_custom_field[custom_field.custom_field_id] %}{{ account_custom_field[custom_field.custom_field_id] }}{% else %}{{ custom_field.value }}{% endif %}" placeholder="{{ custom_field.name }}" id="input-custom-field-{{ custom_field.custom_field_id }}" class="form-control date"/>
                    <div class="input-group-text"><i class="fa-regular fa-calendar"></i></div>
                  </div>
                  <div id="error-custom-field-{{ custom_field.custom_field_id }}" class="invalid-feedback"></div>
                </div>
              </div>
            {% endif %}

            {% if custom_field.type == 'time' %}
              <div class="row mb-3{% if custom_field.required %} required{% endif %} custom-field">
                <label for="input-custom-field-{{ custom_field.custom_field_id }}" class="col-sm-2 col-form-label">{{ custom_field.name }}</label>
                <div class="col-sm-10">
                  <div class="input-group">
                    <input type="text" name="custom_field[{{ custom_field.custom_field_id }}]" value="{% if account_custom_field[custom_field.custom_field_id] %}{{ account_custom_field[custom_field.custom_field_id] }}{% else %}{{ custom_field.value }}{% endif %}" placeholder="{{ custom_field.name }}" id="input-custom-field-{{ custom_field.custom_field_id }}" class="form-control time"/>
                    <div class="input-group-text"><i class="fa-regular fa-calendar"></i></div>
                  </div>
                  <div id="error-custom-field-{{ custom_field.custom_field_id }}" class="invalid-feedback"></div>
                </div>
              </div>
            {% endif %}

            {% if custom_field.type == 'datetime' %}
              <div class="row mb-3{% if custom_field.required %} required{% endif %} custom-field">
                <label for="input-custom-field-{{ custom_field.custom_field_id }}" class="col-sm-2 col-form-label">{{ custom_field.name }}</label>
                <div class="col-sm-10">
                  <div class="input-group">
                    <input type="text" name="custom_field[{{ custom_field.custom_field_id }}]" value="{% if account_custom_field[custom_field.custom_field_id] %}{{ account_custom_field[custom_field.custom_field_id] }}{% else %}{{ custom_field.value }}{% endif %}" placeholder="{{ custom_field.name }}" id="input-custom-field-{{ custom_field.custom_field_id }}" class="form-control datetime"/>
                    <div class="input-group-text"><i class="fa-regular fa-calendar"></i></div>
                  </div>
                  <div id="error-custom-field-{{ custom_field.custom_field_id }}" class="invalid-feedback"></div>
                </div>
              </div>
            {% endif %}

          {% endfor %}
        </fieldset>
        <div class="row">
          <div class="col">
            <a href="{{ back }}" class="btn btn-light">{{ button_back }}</a>
          </div>
          <div class="col text-end">
            <button type="submit" class="btn btn-primary">{{ button_continue }}</button>
          </div>
        </div>
      </form>
      {{ content_bottom }}</div>
    {{ column_right }}</div>
</div>
{{ footer }}
