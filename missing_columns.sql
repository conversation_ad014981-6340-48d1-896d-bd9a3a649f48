-- ملف إضافة الأعمدة المفقودة في الجداول الموجودة
-- تاريخ الإنشاء: 2025-07-28
-- الهدف: إصلاح أخطاء الأعمدة المفقودة في dashboard.php

-- ========================================
-- إضافة أعمدة مفقودة لجدول cod_order
-- ========================================

-- إضافة عمود order_source إذا لم يكن موجوداً
ALTER TABLE `cod_order` 
ADD COLUMN IF NOT EXISTS `order_source` enum('website','mobile','phone','store','api') DEFAULT 'website' COMMENT 'مصدر الطلب' 
AFTER `store_id`;

-- إضافة عمود shipped_date إذا لم يكن موجوداً
ALTER TABLE `cod_order` 
ADD COLUMN IF NOT EXISTS `shipped_date` datetime DEFAULT NULL COMMENT 'تاريخ الشحن' 
AFTER `date_modified`;

-- إضافة عمود delivered_date إذا لم يكن موجوداً
ALTER TABLE `cod_order` 
ADD COLUMN IF NOT EXISTS `delivered_date` datetime DEFAULT NULL COMMENT 'تاريخ التسليم' 
AFTER `shipped_date`;

-- إضافة عمود shipping_cost إذا لم يكن موجوداً
ALTER TABLE `cod_order` 
ADD COLUMN IF NOT EXISTS `shipping_cost` decimal(10,4) DEFAULT 0.0000 COMMENT 'تكلفة الشحن' 
AFTER `delivered_date`;

-- ========================================
-- إضافة أعمدة مفقودة لجدول cod_cart
-- ========================================

-- إضافة عمود abandoned_date إذا لم يكن موجوداً
ALTER TABLE `cod_cart` 
ADD COLUMN IF NOT EXISTS `abandoned_date` datetime DEFAULT NULL COMMENT 'تاريخ هجر السلة' 
AFTER `date_added`;

-- إضافة عمود session_id إذا لم يكن موجوداً
ALTER TABLE `cod_cart` 
ADD COLUMN IF NOT EXISTS `session_id` varchar(32) DEFAULT NULL COMMENT 'معرف الجلسة' 
AFTER `customer_id`;

-- ========================================
-- إضافة أعمدة مفقودة لجدول cod_product
-- ========================================

-- إضافة عمود affiliate_id إذا لم يكن موجوداً
ALTER TABLE `cod_product` 
ADD COLUMN IF NOT EXISTS `affiliate_id` int(11) DEFAULT NULL COMMENT 'معرف الشريك التسويقي' 
AFTER `manufacturer_id`;

-- إضافة عمود expiry_date إذا لم يكن موجوداً
ALTER TABLE `cod_product` 
ADD COLUMN IF NOT EXISTS `expiry_date` date DEFAULT NULL COMMENT 'تاريخ انتهاء الصلاحية' 
AFTER `date_available`;

-- ========================================
-- إضافة أعمدة مفقودة لجدول cod_supplier
-- ========================================

-- إضافة عمود name إذا لم يكن موجوداً (كبديل لـ firstname + lastname)
ALTER TABLE `cod_supplier` 
ADD COLUMN IF NOT EXISTS `name` varchar(200) DEFAULT NULL COMMENT 'اسم المورد الكامل' 
AFTER `supplier_id`;

-- إضافة عمود country_id إذا لم يكن موجوداً
ALTER TABLE `cod_supplier` 
ADD COLUMN IF NOT EXISTS `country_id` int(11) DEFAULT NULL COMMENT 'معرف البلد' 
AFTER `zone_id`;

-- ========================================
-- إضافة أعمدة مفقودة لجدول cod_purchase_order
-- ========================================

-- إضافة عمود purchase_order_id إذا لم يكن موجوداً (alias لـ po_id)
ALTER TABLE `cod_purchase_order` 
ADD COLUMN IF NOT EXISTS `purchase_order_id` int(11) DEFAULT NULL COMMENT 'معرف أمر الشراء (مرادف)' 
AFTER `po_id`;

-- تحديث القيم لتكون متطابقة
UPDATE `cod_purchase_order` SET `purchase_order_id` = `po_id` WHERE `purchase_order_id` IS NULL;

-- ========================================
-- إضافة أعمدة مفقودة لجدول cod_risk_register
-- ========================================

-- إضافة عمود risk_score إذا لم يكن موجوداً (محسوب من probability_score * impact_score)
ALTER TABLE `cod_risk_register` 
ADD COLUMN IF NOT EXISTS `risk_score` decimal(5,2) DEFAULT NULL COMMENT 'درجة المخاطر المحسوبة' 
AFTER `impact_score`;

-- تحديث القيم المحسوبة
UPDATE `cod_risk_register` 
SET `risk_score` = `probability_score` * `impact_score` 
WHERE `risk_score` IS NULL AND `probability_score` IS NOT NULL AND `impact_score` IS NOT NULL;

-- ========================================
-- إضافة أعمدة مفقودة لجدول cod_audit_task
-- ========================================

-- إضافة عمود scheduled_date إذا لم يكن موجوداً
ALTER TABLE `cod_audit_task` 
ADD COLUMN IF NOT EXISTS `scheduled_date` date DEFAULT NULL COMMENT 'التاريخ المجدول' 
AFTER `due_date`;

-- ========================================
-- إضافة أعمدة مفقودة لجدول cod_employee_payroll
-- ========================================

-- إضافة عمود overtime_hours إذا لم يكن موجوداً
ALTER TABLE `cod_employee_payroll` 
ADD COLUMN IF NOT EXISTS `overtime_hours` decimal(5,2) DEFAULT 0.00 COMMENT 'ساعات العمل الإضافي' 
AFTER `basic_salary`;

-- إضافة عمود termination_date إذا لم يكن موجوداً
ALTER TABLE `cod_employee_payroll` 
ADD COLUMN IF NOT EXISTS `termination_date` date DEFAULT NULL COMMENT 'تاريخ إنهاء الخدمة' 
AFTER `hire_date`;

-- إضافة عمود termination_reason إذا لم يكن موجوداً
ALTER TABLE `cod_employee_payroll` 
ADD COLUMN IF NOT EXISTS `termination_reason` text DEFAULT NULL COMMENT 'سبب إنهاء الخدمة' 
AFTER `termination_date`;

-- ========================================
-- إضافة أعمدة مفقودة لجدول cod_leave_request
-- ========================================

-- إضافة عمود leave_id إذا لم يكن موجوداً (alias لـ request_id)
ALTER TABLE `cod_leave_request` 
ADD COLUMN IF NOT EXISTS `leave_id` int(11) DEFAULT NULL COMMENT 'معرف الإجازة (مرادف)' 
AFTER `request_id`;

-- تحديث القيم لتكون متطابقة
UPDATE `cod_leave_request` SET `leave_id` = `request_id` WHERE `leave_id` IS NULL;

-- ========================================
-- إضافة أعمدة مفقودة لجدول cod_inventory_alert
-- ========================================

-- إضافة عمود min_quantity إذا لم يكن موجوداً
ALTER TABLE `cod_inventory_alert` 
ADD COLUMN IF NOT EXISTS `min_quantity` decimal(10,4) DEFAULT 0.0000 COMMENT 'الحد الأدنى للكمية' 
AFTER `current_quantity`;

-- ========================================
-- إضافة أعمدة مفقودة لجدول cod_fixed_assets
-- ========================================

-- إضافة عمود asset_name إذا لم يكن موجوداً
ALTER TABLE `cod_fixed_assets` 
ADD COLUMN IF NOT EXISTS `asset_name` varchar(200) DEFAULT NULL COMMENT 'اسم الأصل' 
AFTER `asset_id`;

-- ========================================
-- إضافة أعمدة مفقودة لجدول cod_accounts_payable
-- ========================================

-- إضافة عمود payment_date إذا لم يكن موجوداً
ALTER TABLE `cod_accounts_payable` 
ADD COLUMN IF NOT EXISTS `payment_date` date DEFAULT NULL COMMENT 'تاريخ الدفع' 
AFTER `due_date`;

-- ========================================
-- إضافة أعمدة مفقودة لجدول cod_attendance
-- ========================================

-- إضافة عمود check_in_time إذا لم يكن موجوداً
ALTER TABLE `cod_attendance` 
ADD COLUMN IF NOT EXISTS `check_in_time` time DEFAULT NULL COMMENT 'وقت الحضور' 
AFTER `attendance_date`;

-- ========================================
-- إضافة أعمدة مفقودة لجدول cod_payment_terminal
-- ========================================

-- إضافة عمود terminal_name إذا لم يكن موجوداً
ALTER TABLE `cod_payment_terminal` 
ADD COLUMN IF NOT EXISTS `terminal_name` varchar(100) DEFAULT NULL COMMENT 'اسم المحطة' 
AFTER `terminal_id`;

-- ========================================
-- إضافة فهارس للأعمدة الجديدة
-- ========================================

-- فهارس جدول cod_order
ALTER TABLE `cod_order` ADD INDEX IF NOT EXISTS `idx_order_source` (`order_source`);
ALTER TABLE `cod_order` ADD INDEX IF NOT EXISTS `idx_shipped_date` (`shipped_date`);
ALTER TABLE `cod_order` ADD INDEX IF NOT EXISTS `idx_delivered_date` (`delivered_date`);

-- فهارس جدول cod_cart
ALTER TABLE `cod_cart` ADD INDEX IF NOT EXISTS `idx_abandoned_date` (`abandoned_date`);
ALTER TABLE `cod_cart` ADD INDEX IF NOT EXISTS `idx_session_id` (`session_id`);

-- فهارس جدول cod_product
ALTER TABLE `cod_product` ADD INDEX IF NOT EXISTS `idx_affiliate_id` (`affiliate_id`);
ALTER TABLE `cod_product` ADD INDEX IF NOT EXISTS `idx_expiry_date` (`expiry_date`);

-- فهارس جدول cod_supplier
ALTER TABLE `cod_supplier` ADD INDEX IF NOT EXISTS `idx_name` (`name`);
ALTER TABLE `cod_supplier` ADD INDEX IF NOT EXISTS `idx_country_id` (`country_id`);

-- فهارس جدول cod_risk_register
ALTER TABLE `cod_risk_register` ADD INDEX IF NOT EXISTS `idx_risk_score` (`risk_score`);

-- فهارس جدول cod_audit_task
ALTER TABLE `cod_audit_task` ADD INDEX IF NOT EXISTS `idx_scheduled_date` (`scheduled_date`);

-- ========================================
-- تحديث البيانات الموجودة
-- ========================================

-- تحديث أسماء الموردين من firstname + lastname
UPDATE `cod_supplier` 
SET `name` = CONCAT(COALESCE(`firstname`, ''), ' ', COALESCE(`lastname`, ''))
WHERE `name` IS NULL AND (`firstname` IS NOT NULL OR `lastname` IS NOT NULL);

-- تحديث تواريخ هجر السلة (افتراضياً بعد 24 ساعة من آخر تعديل)
UPDATE `cod_cart` 
SET `abandoned_date` = DATE_ADD(`date_added`, INTERVAL 1 DAY)
WHERE `abandoned_date` IS NULL AND `date_added` < DATE_SUB(NOW(), INTERVAL 1 DAY);

-- تحديث مصدر الطلبات (افتراضياً website)
UPDATE `cod_order` 
SET `order_source` = 'website'
WHERE `order_source` IS NULL;

-- ========================================
-- إضافة قيود المفاتيح الخارجية (اختيارية)
-- ========================================

-- يمكن إضافة قيود المفاتيح الخارجية حسب الحاجة
-- ALTER TABLE `cod_order` ADD CONSTRAINT `fk_order_affiliate` FOREIGN KEY (`affiliate_id`) REFERENCES `cod_affiliate` (`affiliate_id`);
-- ALTER TABLE `cod_product` ADD CONSTRAINT `fk_product_affiliate` FOREIGN KEY (`affiliate_id`) REFERENCES `cod_affiliate` (`affiliate_id`);
-- ALTER TABLE `cod_supplier` ADD CONSTRAINT `fk_supplier_country` FOREIGN KEY (`country_id`) REFERENCES `cod_country` (`country_id`);
