<?php
class ControllerWorkflowTask extends Controller {
    private $error = array();

    public function index() {
        // Load central services (CONSTITUTIONAL REQUIREMENT)
        $this->load->model('core/central_service_manager');

        $this->load->language('workflow/task');
        $this->document->setTitle($this->language->get('heading_title'));

        // التحقق من الصلاحيات
        if (!$this->user->hasPermission('access', 'workflow/task')) {
            $this->response->redirect($this->url->link('error/permission', 'user_token=' . $this->session->data['user_token'], true));
        }

        $this->load->model('workflow/task');
        
        $data['breadcrumbs'] = array();
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_workflow'),
            'href' => $this->url->link('workflow/workflow', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('workflow/task', 'user_token=' . $this->session->data['user_token'], true)
        );
        
        $data['add'] = $this->url->link('workflow/task/add', 'user_token=' . $this->session->data['user_token'], true);
        $data['delete'] = $this->url->link('workflow/task/delete', 'user_token=' . $this->session->data['user_token'], true);
        $data['user_token'] = $this->session->data['user_token'];
        
        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');
        
        $this->response->setOutput($this->load->view('workflow/task', $data));
    }
    
    public function getList() {
        $this->load->language('workflow/task');
        $this->load->model('workflow/task');
        
        $data['tasks'] = array();
        
        $filter_data = array(
            'filter_title' => isset($this->request->get['filter_title']) ? $this->request->get['filter_title'] : '',
            'filter_assignee' => isset($this->request->get['filter_assignee']) ? $this->request->get['filter_assignee'] : '',
            'filter_priority' => isset($this->request->get['filter_priority']) ? $this->request->get['filter_priority'] : '',
            'filter_status' => isset($this->request->get['filter_status']) ? $this->request->get['filter_status'] : '',
            'filter_due_date' => isset($this->request->get['filter_due_date']) ? $this->request->get['filter_due_date'] : '',
            'start' => isset($this->request->get['start']) ? $this->request->get['start'] : 0,
            'limit' => isset($this->request->get['limit']) ? $this->request->get['limit'] : 20
        );
        
        $results = $this->model_workflow_task->getTasks($filter_data);
        
        foreach ($results as $result) {
            $data['tasks'][] = array(
                'task_id' => $result['task_id'],
                'title' => $result['title'],
                'description' => $result['description'],
                'assignee_name' => $result['assignee_name'],
                'priority' => $result['priority'],
                'priority_text' => $this->getPriorityText($result['priority']),
                'status' => $result['status'],
                'status_text' => $this->getStatusText($result['status']),
                'due_date' => $result['due_date'] ? date($this->language->get('date_format_short'), strtotime($result['due_date'])) : '-',
                'created_date' => date($this->language->get('date_format_short'), strtotime($result['created_date'])),
                'completed_date' => $result['completed_date'] ? date($this->language->get('date_format_short'), strtotime($result['completed_date'])) : '-',
                'progress' => $result['progress'],
                'view' => $this->url->link('workflow/task/view', 'user_token=' . $this->session->data['user_token'] . '&task_id=' . $result['task_id'], true),
                'edit' => $this->url->link('workflow/task/edit', 'user_token=' . $this->session->data['user_token'] . '&task_id=' . $result['task_id'], true)
            );
        }
        
        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($data));
    }
    
    public function add() {
        $this->load->language('workflow/task');
        $this->load->model('workflow/task');
        
        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validate()) {
            $task_id = $this->model_workflow_task->addTask($this->request->post);
            
            $this->session->data['success'] = $this->language->get('text_success');
            
            $this->response->redirect($this->url->link('workflow/task', 'user_token=' . $this->session->data['user_token'], true));
        }
        
        $this->getForm();
    }
    
    public function edit() {
        $this->load->language('workflow/task');
        $this->load->model('workflow/task');
        
        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validate()) {
            $this->model_workflow_task->updateTask($this->request->get['task_id'], $this->request->post);
            
            $this->session->data['success'] = $this->language->get('text_success');
            
            $this->response->redirect($this->url->link('workflow/task', 'user_token=' . $this->session->data['user_token'], true));
        }
        
        $this->getForm();
    }
    
    public function delete() {
        $this->load->language('workflow/task');
        $this->load->model('workflow/task');
        
        if (isset($this->request->post['selected']) && $this->validateDelete()) {
            foreach ($this->request->post['selected'] as $task_id) {
                $this->model_workflow_task->deleteTask($task_id);
            }
            
            $this->session->data['success'] = $this->language->get('text_success');
            
            $this->response->redirect($this->url->link('workflow/task', 'user_token=' . $this->session->data['user_token'], true));
        }
        
        $this->getList();
    }
    
    public function view() {
        $this->load->language('workflow/task');
        $this->load->model('workflow/task');
        
        if (isset($this->request->get['task_id'])) {
            $task_id = $this->request->get['task_id'];
        } else {
            $task_id = 0;
        }
        
        $task_info = $this->model_workflow_task->getTask($task_id);
        
        if ($task_info) {
            $data['task'] = array(
                'task_id' => $task_info['task_id'],
                'title' => $task_info['title'],
                'description' => $task_info['description'],
                'assignee_name' => $task_info['assignee_name'],
                'assignee_email' => $task_info['assignee_email'],
                'priority' => $task_info['priority'],
                'priority_text' => $this->getPriorityText($task_info['priority']),
                'status' => $task_info['status'],
                'status_text' => $this->getStatusText($task_info['status']),
                'due_date' => $task_info['due_date'] ? date($this->language->get('date_format_short'), strtotime($task_info['due_date'])) : '-',
                'created_date' => date($this->language->get('date_format_short'), strtotime($task_info['created_date'])),
                'completed_date' => $task_info['completed_date'] ? date($this->language->get('date_format_short'), strtotime($task_info['completed_date'])) : '-',
                'progress' => $task_info['progress'],
                'estimated_hours' => $task_info['estimated_hours'],
                'actual_hours' => $task_info['actual_hours'],
                'tags' => $task_info['tags'],
                'attachments' => $task_info['attachments']
            );
            
            // الحصول على سجل المهمة
            $data['task_history'] = $this->model_workflow_task->getTaskHistory($task_id);
            
            // الحصول على التعليقات
            $data['task_comments'] = $this->model_workflow_task->getTaskComments($task_id);
            
            $data['breadcrumbs'] = array();
            $data['breadcrumbs'][] = array(
                'text' => $this->language->get('text_home'),
                'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
            );
            $data['breadcrumbs'][] = array(
                'text' => $this->language->get('text_workflow'),
                'href' => $this->url->link('workflow/workflow', 'user_token=' . $this->session->data['user_token'], true)
            );
            $data['breadcrumbs'][] = array(
                'text' => $this->language->get('heading_title'),
                'href' => $this->url->link('workflow/task', 'user_token=' . $this->session->data['user_token'], true)
            );
            $data['breadcrumbs'][] = array(
                'text' => $this->language->get('text_view_task'),
                'href' => $this->url->link('workflow/task/view', 'user_token=' . $this->session->data['user_token'] . '&task_id=' . $task_id, true)
            );
            
            $data['user_token'] = $this->session->data['user_token'];
            $data['header'] = $this->load->controller('common/header');
            $data['column_left'] = $this->load->controller('common/column_left');
            $data['footer'] = $this->load->controller('common/footer');
            
            $this->response->setOutput($this->load->view('workflow/task_view', $data));
        } else {
            $this->response->redirect($this->url->link('workflow/task', 'user_token=' . $this->session->data['user_token'], true));
        }
    }
    
    public function updateStatus() {
        $this->load->language('workflow/task');
        $this->load->model('workflow/task');
        
        $json = array();
        
        if (isset($this->request->get['task_id']) && isset($this->request->post['status'])) {
            $task_id = $this->request->get['task_id'];
            $status = $this->request->post['status'];
            $progress = isset($this->request->post['progress']) ? $this->request->post['progress'] : 0;
            
            if ($this->user->hasPermission('modify', 'workflow/task')) {
                $this->model_workflow_task->updateTaskStatus($task_id, $status, $progress);
                
                $json['success'] = $this->language->get('text_success_status_update');
            } else {
                $json['error'] = $this->language->get('error_permission');
            }
        } else {
            $json['error'] = $this->language->get('error_invalid_data');
        }
        
        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }
    
    public function addComment() {
        $this->load->language('workflow/task');
        $this->load->model('workflow/task');
        
        $json = array();
        
        if (isset($this->request->get['task_id']) && isset($this->request->post['comment'])) {
            $task_id = $this->request->get['task_id'];
            $comment = $this->request->post['comment'];
            
            if ($this->user->hasPermission('modify', 'workflow/task')) {
                $this->model_workflow_task->addTaskComment($task_id, $comment);
                
                $json['success'] = $this->language->get('text_success_comment_added');
            } else {
                $json['error'] = $this->language->get('error_permission');
            }
        } else {
            $json['error'] = $this->language->get('error_invalid_data');
        }
        
        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }
    
    protected function getForm() {
        $this->load->language('workflow/task');
        $this->load->model('workflow/task');
        
        $data['text_form'] = !isset($this->request->get['task_id']) ? $this->language->get('text_add') : $this->language->get('text_edit');
        
        if (isset($this->request->get['task_id'])) {
            $task_info = $this->model_workflow_task->getTask($this->request->get['task_id']);
        }
        
        if (isset($this->request->post['title'])) {
            $data['title'] = $this->request->post['title'];
        } elseif (!empty($task_info)) {
            $data['title'] = $task_info['title'];
        } else {
            $data['title'] = '';
        }
        
        if (isset($this->request->post['description'])) {
            $data['description'] = $this->request->post['description'];
        } elseif (!empty($task_info)) {
            $data['description'] = $task_info['description'];
        } else {
            $data['description'] = '';
        }
        
        if (isset($this->request->post['assignee_id'])) {
            $data['assignee_id'] = $this->request->post['assignee_id'];
        } elseif (!empty($task_info)) {
            $data['assignee_id'] = $task_info['assignee_id'];
        } else {
            $data['assignee_id'] = '';
        }
        
        if (isset($this->request->post['priority'])) {
            $data['priority'] = $this->request->post['priority'];
        } elseif (!empty($task_info)) {
            $data['priority'] = $task_info['priority'];
        } else {
            $data['priority'] = 'medium';
        }
        
        if (isset($this->request->post['status'])) {
            $data['status'] = $this->request->post['status'];
        } elseif (!empty($task_info)) {
            $data['status'] = $task_info['status'];
        } else {
            $data['status'] = 'pending';
        }
        
        if (isset($this->request->post['due_date'])) {
            $data['due_date'] = $this->request->post['due_date'];
        } elseif (!empty($task_info)) {
            $data['due_date'] = date('Y-m-d', strtotime($task_info['due_date']));
        } else {
            $data['due_date'] = '';
        }
        
        if (isset($this->request->post['estimated_hours'])) {
            $data['estimated_hours'] = $this->request->post['estimated_hours'];
        } elseif (!empty($task_info)) {
            $data['estimated_hours'] = $task_info['estimated_hours'];
        } else {
            $data['estimated_hours'] = '';
        }
        
        if (isset($this->request->post['tags'])) {
            $data['tags'] = $this->request->post['tags'];
        } elseif (!empty($task_info)) {
            $data['tags'] = $task_info['tags'];
        } else {
            $data['tags'] = '';
        }
        
        $data['user_token'] = $this->session->data['user_token'];
        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');
        
        $this->response->setOutput($this->load->view('workflow/task_form', $data));
    }
    
    private function getPriorityText($priority) {
        switch ($priority) {
            case 'low':
                return $this->language->get('text_priority_low');
            case 'medium':
                return $this->language->get('text_priority_medium');
            case 'high':
                return $this->language->get('text_priority_high');
            case 'urgent':
                return $this->language->get('text_priority_urgent');
            default:
                return $priority;
        }
    }
    
    private function getStatusText($status) {
        switch ($status) {
            case 'pending':
                return $this->language->get('text_status_pending');
            case 'in_progress':
                return $this->language->get('text_status_in_progress');
            case 'review':
                return $this->language->get('text_status_review');
            case 'completed':
                return $this->language->get('text_status_completed');
            case 'cancelled':
                return $this->language->get('text_status_cancelled');
            default:
                return $status;
        }
    }
    
    protected function validate() {
        if (!$this->user->hasPermission('modify', 'workflow/task')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }
        
        if (empty($this->request->post['title'])) {
            $this->error['title'] = $this->language->get('error_title');
        }
        
        if (empty($this->request->post['assignee_id'])) {
            $this->error['assignee'] = $this->language->get('error_assignee');
        }
        
        return !$this->error;
    }
    
    protected function validateDelete() {
        if (!$this->user->hasPermission('modify', 'workflow/task')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }
        
        return !$this->error;
    }
} 