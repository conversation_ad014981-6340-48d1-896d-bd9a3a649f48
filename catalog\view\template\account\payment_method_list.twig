{% if payment_methods %}
  <div class="table-responsive">
    <table class="table table-bordered table-hover">
      <thead>
        <tr>
          <th class="text-start">{{ column_payment_method }}</th>
          <th></th>
          <th class="text-start">{{ column_type }}</th>
          <th class="text-start">{{ column_date_expire }}</th>
          <th class="text-end">{{ column_action }}</th>
        </tr>
      </thead>
      <tbody>
      <tbody>
        {% for payment_method in payment_methods %}
          <tr>
            <td class="text-start">{{ payment_method.name }}</td>
            <td class="text-start">{{ payment_method.image }}</td>
            <td class="text-start">{{ payment_method.type }}</td>
            <td class="text-start">{{ payment_method.date_expire }}</td>
            <td class="text-end"><a href="{{ payment_method.delete }}" class="btn btn-danger">{{ button_delete }}</a></td>
          </tr>
        {% endfor %}
      </tbody>
    </table>
  </div>
{% else %}
  <p>{{ text_no_results }}</p>
{% endif %}