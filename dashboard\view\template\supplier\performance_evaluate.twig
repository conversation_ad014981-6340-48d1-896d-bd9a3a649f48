{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="submit" form="supplier\performance-form" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fas fa-save"></i></button>
        <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-light"><i class="fas fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    
    <div class="card">
      <div class="card-header"><i class="fas fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}chart-bar{% elif screen_type == 'settings' %}cog{% else %}pencil-alt{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="card-body">
        <form id="supplier\performance-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-cost_analysis">{{ text_cost_analysis }}</label>
            <div class="col-sm-10">
              <input type="text" name="cost_analysis" value="{{ cost_analysis }}" placeholder="{{ text_cost_analysis }}" id="input-cost_analysis" class="form-control" />
              {% if error_cost_analysis %}
                <div class="invalid-feedback">{{ error_cost_analysis }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-dashboard">{{ text_dashboard }}</label>
            <div class="col-sm-10">
              <input type="text" name="dashboard" value="{{ dashboard }}" placeholder="{{ text_dashboard }}" id="input-dashboard" class="form-control" />
              {% if error_dashboard %}
                <div class="invalid-feedback">{{ error_dashboard }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-delivery_performance">{{ text_delivery_performance }}</label>
            <div class="col-sm-10">
              <input type="text" name="delivery_performance" value="{{ delivery_performance }}" placeholder="{{ text_delivery_performance }}" id="input-delivery_performance" class="form-control" />
              {% if error_delivery_performance %}
                <div class="invalid-feedback">{{ error_delivery_performance }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-evaluation_criteria">{{ text_evaluation_criteria }}</label>
            <div class="col-sm-10">
              <input type="text" name="evaluation_criteria" value="{{ evaluation_criteria }}" placeholder="{{ text_evaluation_criteria }}" id="input-evaluation_criteria" class="form-control" />
              {% if error_evaluation_criteria %}
                <div class="invalid-feedback">{{ error_evaluation_criteria }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="invalid-feedback">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-order">{{ text_order }}</label>
            <div class="col-sm-10">
              <input type="text" name="order" value="{{ order }}" placeholder="{{ text_order }}" id="input-order" class="form-control" />
              {% if error_order %}
                <div class="invalid-feedback">{{ error_order }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-pagination">{{ text_pagination }}</label>
            <div class="col-sm-10">
              <input type="text" name="pagination" value="{{ pagination }}" placeholder="{{ text_pagination }}" id="input-pagination" class="form-control" />
              {% if error_pagination %}
                <div class="invalid-feedback">{{ error_pagination }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-performance_alerts">{{ text_performance_alerts }}</label>
            <div class="col-sm-10">
              <input type="text" name="performance_alerts" value="{{ performance_alerts }}" placeholder="{{ text_performance_alerts }}" id="input-performance_alerts" class="form-control" />
              {% if error_performance_alerts %}
                <div class="invalid-feedback">{{ error_performance_alerts }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-performance_history">{{ text_performance_history }}</label>
            <div class="col-sm-10">
              <input type="text" name="performance_history" value="{{ performance_history }}" placeholder="{{ text_performance_history }}" id="input-performance_history" class="form-control" />
              {% if error_performance_history %}
                <div class="invalid-feedback">{{ error_performance_history }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-performance_metrics">{{ text_performance_metrics }}</label>
            <div class="col-sm-10">
              <input type="text" name="performance_metrics" value="{{ performance_metrics }}" placeholder="{{ text_performance_metrics }}" id="input-performance_metrics" class="form-control" />
              {% if error_performance_metrics %}
                <div class="invalid-feedback">{{ error_performance_metrics }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-performance_overview">{{ text_performance_overview }}</label>
            <div class="col-sm-10">
              <input type="text" name="performance_overview" value="{{ performance_overview }}" placeholder="{{ text_performance_overview }}" id="input-performance_overview" class="form-control" />
              {% if error_performance_overview %}
                <div class="invalid-feedback">{{ error_performance_overview }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-performance_report">{{ text_performance_report }}</label>
            <div class="col-sm-10">
              <input type="text" name="performance_report" value="{{ performance_report }}" placeholder="{{ text_performance_report }}" id="input-performance_report" class="form-control" />
              {% if error_performance_report %}
                <div class="invalid-feedback">{{ error_performance_report }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-performance_trends">{{ text_performance_trends }}</label>
            <div class="col-sm-10">
              <input type="text" name="performance_trends" value="{{ performance_trends }}" placeholder="{{ text_performance_trends }}" id="input-performance_trends" class="form-control" />
              {% if error_performance_trends %}
                <div class="invalid-feedback">{{ error_performance_trends }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-quality_metrics">{{ text_quality_metrics }}</label>
            <div class="col-sm-10">
              <input type="text" name="quality_metrics" value="{{ quality_metrics }}" placeholder="{{ text_quality_metrics }}" id="input-quality_metrics" class="form-control" />
              {% if error_quality_metrics %}
                <div class="invalid-feedback">{{ error_quality_metrics }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-recent_orders">{{ text_recent_orders }}</label>
            <div class="col-sm-10">
              <input type="text" name="recent_orders" value="{{ recent_orders }}" placeholder="{{ text_recent_orders }}" id="input-recent_orders" class="form-control" />
              {% if error_recent_orders %}
                <div class="invalid-feedback">{{ error_recent_orders }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-results">{{ text_results }}</label>
            <div class="col-sm-10">
              <input type="text" name="results" value="{{ results }}" placeholder="{{ text_results }}" id="input-results" class="form-control" />
              {% if error_results %}
                <div class="invalid-feedback">{{ error_results }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort">{{ text_sort }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort" value="{{ sort }}" placeholder="{{ text_sort }}" id="input-sort" class="form-control" />
              {% if error_sort %}
                <div class="invalid-feedback">{{ error_sort }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_cost_score">{{ text_sort_cost_score }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_cost_score" value="{{ sort_cost_score }}" placeholder="{{ text_sort_cost_score }}" id="input-sort_cost_score" class="form-control" />
              {% if error_sort_cost_score %}
                <div class="invalid-feedback">{{ error_sort_cost_score }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_delivery_score">{{ text_sort_delivery_score }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_delivery_score" value="{{ sort_delivery_score }}" placeholder="{{ text_sort_delivery_score }}" id="input-sort_delivery_score" class="form-control" />
              {% if error_sort_delivery_score %}
                <div class="invalid-feedback">{{ error_sort_delivery_score }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_name">{{ text_sort_name }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_name" value="{{ sort_name }}" placeholder="{{ text_sort_name }}" id="input-sort_name" class="form-control" />
              {% if error_sort_name %}
                <div class="invalid-feedback">{{ error_sort_name }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_overall_score">{{ text_sort_overall_score }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_overall_score" value="{{ sort_overall_score }}" placeholder="{{ text_sort_overall_score }}" id="input-sort_overall_score" class="form-control" />
              {% if error_sort_overall_score %}
                <div class="invalid-feedback">{{ error_sort_overall_score }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_quality_score">{{ text_sort_quality_score }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_quality_score" value="{{ sort_quality_score }}" placeholder="{{ text_sort_quality_score }}" id="input-sort_quality_score" class="form-control" />
              {% if error_sort_quality_score %}
                <div class="invalid-feedback">{{ error_sort_quality_score }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-supplier_info">{{ text_supplier_info }}</label>
            <div class="col-sm-10">
              <input type="text" name="supplier_info" value="{{ supplier_info }}" placeholder="{{ text_supplier_info }}" id="input-supplier_info" class="form-control" />
              {% if error_supplier_info %}
                <div class="invalid-feedback">{{ error_supplier_info }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-suppliers">{{ text_suppliers }}</label>
            <div class="col-sm-10">
              <input type="text" name="suppliers" value="{{ suppliers }}" placeholder="{{ text_suppliers }}" id="input-suppliers" class="form-control" />
              {% if error_suppliers %}
                <div class="invalid-feedback">{{ error_suppliers }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_form">{{ text_text_form }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_form" value="{{ text_form }}" placeholder="{{ text_text_form }}" id="input-text_form" class="form-control" />
              {% if error_text_form %}
                <div class="invalid-feedback">{{ error_text_form }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-top_suppliers">{{ text_top_suppliers }}</label>
            <div class="col-sm-10">
              <input type="text" name="top_suppliers" value="{{ top_suppliers }}" placeholder="{{ text_top_suppliers }}" id="input-top_suppliers" class="form-control" />
              {% if error_top_suppliers %}
                <div class="invalid-feedback">{{ error_top_suppliers }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="invalid-feedback">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

<script type="text/javascript"><!--
$('#supplier\performance-form').on('submit', function(e) {
    e.preventDefault();
    
    var element = this;
    
    $.ajax({
        url: $(element).attr('action'),
        type: 'post',
        data: $(element).serialize(),
        dataType: 'json',
        beforeSend: function() {
            $(element).find('button[type=\'submit\']').prop('disabled', true).addClass('loading');
        },
        complete: function() {
            $(element).find('button[type=\'submit\']').prop('disabled', false).removeClass('loading');
        },
        success: function(json) {
            $('.alert-dismissible').remove();
            $(element).find('.is-invalid').removeClass('is-invalid');
            $(element).find('.invalid-feedback').removeClass('d-block');
            
            if (json['error']) {
                if (json['error']['warning']) {
                    $('#alert').prepend('<div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> ' + json['error']['warning'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
                }
                
                for (key in json['error']) {
                    $('#input-' + key.replaceAll('_', '-')).addClass('is-invalid').next().html(json['error'][key]).addClass('d-block');
                }
            }
            
            if (json['success']) {
                $('#alert').prepend('<div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> ' + json['success'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
                
                // Refresh or redirect
                if (json['redirect']) {
                    location = json['redirect'];
                }
            }
        },
        error: function(xhr, ajaxOptions, thrownError) {
            console.error(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
    });
});

// AJAX action for dashboard
$('body').on('click', '.btn-dashboard', function(e) {
    e.preventDefault();
    
    var element = this;
    var id = $(element).data('id');
    
    $.ajax({
        url: 'index.php?route=supplier\performance/dashboard&user_token={{ user_token }}',
        type: 'post',
        data: {
            'id': id
        },
        dataType: 'json',
        beforeSend: function() {
            $(element).prop('disabled', true);
        },
        complete: function() {
            $(element).prop('disabled', false);
        },
        success: function(json) {
            $('.alert-dismissible').remove();
            
            if (json['error']) {
                $('#alert').prepend('<div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> ' + json['error'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
            }
            
            if (json['success']) {
                $('#alert').prepend('<div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> ' + json['success'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
                
                // Refresh the table
                $('#button-refresh').trigger('click');
            }
        },
        error: function(xhr, ajaxOptions, thrownError) {
            console.error(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
    });
});

// AJAX action for view
$('body').on('click', '.btn-view', function(e) {
    e.preventDefault();
    
    var element = this;
    var id = $(element).data('id');
    
    $.ajax({
        url: 'index.php?route=supplier\performance/view&user_token={{ user_token }}',
        type: 'post',
        data: {
            'id': id
        },
        dataType: 'json',
        beforeSend: function() {
            $(element).prop('disabled', true);
        },
        complete: function() {
            $(element).prop('disabled', false);
        },
        success: function(json) {
            $('.alert-dismissible').remove();
            
            if (json['error']) {
                $('#alert').prepend('<div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> ' + json['error'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
            }
            
            if (json['success']) {
                $('#alert').prepend('<div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> ' + json['success'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
                
                // Refresh the table
                $('#button-refresh').trigger('click');
            }
        },
        error: function(xhr, ajaxOptions, thrownError) {
            console.error(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
    });
});

// AJAX action for evaluate
$('body').on('click', '.btn-evaluate', function(e) {
    e.preventDefault();
    
    var element = this;
    var id = $(element).data('id');
    
    $.ajax({
        url: 'index.php?route=supplier\performance/evaluate&user_token={{ user_token }}',
        type: 'post',
        data: {
            'id': id
        },
        dataType: 'json',
        beforeSend: function() {
            $(element).prop('disabled', true);
        },
        complete: function() {
            $(element).prop('disabled', false);
        },
        success: function(json) {
            $('.alert-dismissible').remove();
            
            if (json['error']) {
                $('#alert').prepend('<div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> ' + json['error'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
            }
            
            if (json['success']) {
                $('#alert').prepend('<div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> ' + json['success'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
                
                // Refresh the table
                $('#button-refresh').trigger('click');
            }
        },
        error: function(xhr, ajaxOptions, thrownError) {
            console.error(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
    });
});

// AJAX action for report
$('body').on('click', '.btn-report', function(e) {
    e.preventDefault();
    
    var element = this;
    var id = $(element).data('id');
    
    $.ajax({
        url: 'index.php?route=supplier\performance/report&user_token={{ user_token }}',
        type: 'post',
        data: {
            'id': id
        },
        dataType: 'json',
        beforeSend: function() {
            $(element).prop('disabled', true);
        },
        complete: function() {
            $(element).prop('disabled', false);
        },
        success: function(json) {
            $('.alert-dismissible').remove();
            
            if (json['error']) {
                $('#alert').prepend('<div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> ' + json['error'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
            }
            
            if (json['success']) {
                $('#alert').prepend('<div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> ' + json['success'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
                
                // Refresh the table
                $('#button-refresh').trigger('click');
            }
        },
        error: function(xhr, ajaxOptions, thrownError) {
            console.error(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
    });
});
//--></script>
      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}