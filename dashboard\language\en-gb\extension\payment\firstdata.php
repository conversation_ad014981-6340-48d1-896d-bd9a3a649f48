<?php
// Heading
$_['heading_title']					 = 'First Data EMEA Connect (3DSecure enabled)';

// Text
$_['text_extension']				 = 'Extensions';
$_['text_success']					 = 'Success: You have modified First Data account details!';
$_['text_edit']                      = 'Edit First Data EMEA Connect (3DSecure enabled)';
$_['text_notification_url']			 = 'Notification URL';
$_['text_live']						 = 'Live';
$_['text_demo']						 = 'Demo';
$_['text_enabled']					 = 'Enabled';
$_['text_merchant_id']				 = 'Store ID';
$_['text_secret']					 = 'Shared secret';
$_['text_capture_ok']				 = 'Capture was successful';
$_['text_capture_ok_order']			 = 'Capture was successful, order status updated to success - settled';
$_['text_void_ok']					 = 'Void was successful, order status updated to voided';
$_['text_settle_auto']				 = 'Sale';
$_['text_settle_delayed']			 = 'Pre auth';
$_['text_success_void']				 = 'Transaction has been voided';
$_['text_success_capture']			 = 'Transaction has been captured';
$_['text_firstdata']				 = '<img src="view/image/payment/firstdata.png" alt="First Data" title="First Data" style="border: 1px solid #EEEEEE;" />';
$_['text_payment_info']				 = 'Payment information';
$_['text_capture_status']			 = 'Payment captured';
$_['text_void_status']				 = 'Payment voided';
$_['text_order_ref']				 = 'Order ref';
$_['text_order_total']				 = 'Total authorised';
$_['text_total_captured']			 = 'Total captured';
$_['text_transactions']				 = 'Transactions';
$_['text_column_amount']			 = 'Amount';
$_['text_column_type']				 = 'Type';
$_['text_column_date_added']		 = 'Created';
$_['text_confirm_void']				 = 'Are you sure you want to void the payment?';
$_['text_confirm_capture']			 = 'Are you sure you want to capture the payment?';

// Entry
$_['entry_merchant_id']				 = 'Store ID';
$_['entry_secret']					 = 'Shared secret';
$_['entry_total']					 = 'Total';
$_['entry_sort_order']				 = 'Sort order';
$_['entry_geo_zone']				 = 'Geo zone';
$_['entry_status']					 = 'Status';
$_['entry_debug']					 = 'Debug logging';
$_['entry_live_demo']				 = 'Live / Demo';
$_['entry_auto_settle']			  	 = 'Settlement type';
$_['entry_card_select']				 = 'Select card';
$_['entry_tss_check']				 = 'TSS checks';
$_['entry_live_url']				 = 'Live connection URL';
$_['entry_demo_url']				 = 'Demo connection URL';
$_['entry_status_success_settled']	 = 'Success - settled';
$_['entry_status_success_unsettled'] = 'Success - not settled';
$_['entry_status_decline']		 	 = 'Decline';
$_['entry_status_void']				 = 'Voided';
$_['entry_enable_card_store']		 = 'Enable card storage tokens';

// Help
$_['help_total']					 = 'The checkout total the order must reach before this payment method becomes active';
$_['help_notification']				 = 'You need to supply this URL to First Data to get payment notifications';
$_['help_debug']					 = 'Enabling debug will write sensitive data to a log file. You should always disable unless instructed otherwise';
$_['help_settle']					 = 'If you use pre-auth you must complete a post-auth action within 3-5 days otherwise your transaction will be dropped'; 

// Tab
$_['tab_account']					 = 'API info';
$_['tab_order_status']				 = 'Order status';
$_['tab_payment']					 = 'Payment settings';
$_['tab_advanced']					 = 'Advanced';

// Button
$_['button_capture']				 = 'Capture';
$_['button_void']					 = 'Void';

// Error
$_['error_merchant_id']				 = 'Store ID is required';
$_['error_secret']					 = 'Shared secret is required';
$_['error_live_url']				 = 'Live URL is required';
$_['error_demo_url']				 = 'Demo URL is required';
$_['error_data_missing']			 = 'Data missing';
$_['error_void_error']				 = 'Unable to void transaction';
$_['error_capture_error']			 = 'Unable to capture transaction';