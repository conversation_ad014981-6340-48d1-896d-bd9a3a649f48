<form id="form-review">
  <div id="review">{{ list }}</div>
  {% if review_guest %}
    <div class="mb-3 required">
      <label for="input-name" class="form-label">{{ entry_name }}</label> <input type="text" name="name" value="{{ customer_name }}" id="input-name" class="form-control"/>
      <div id="error-name" class="invalid-feedback"></div>
    </div>
    <div class="mb-3 required">
      <label for="input-text" class="form-label">{{ entry_review }}</label> <textarea name="text" rows="5" id="input-text" class="form-control"></textarea>
      <div id="error-text" class="invalid-feedback"></div>
      <div class="invalid-feedback">{{ text_note }}</div>
    </div>
    <div class="row mb-3 required">
      <label class="form-label">{{ entry_rating }}</label>
      <div id="input-rating">
        {{ entry_bad }}&nbsp;
        <input type="radio" name="rating" value="1" class="form-check-input"/>
        &nbsp;
        <input type="radio" name="rating" value="2" class="form-check-input"/>
        &nbsp;
        <input type="radio" name="rating" value="3" class="form-check-input"/>
        &nbsp;
        <input type="radio" name="rating" value="4" class="form-check-input"/>
        &nbsp;
        <input type="radio" name="rating" value="5" class="form-check-input"/>
        &nbsp;{{ entry_good }}
      </div>
      <div id="error-rating" class="invalid-feedback"></div>
    </div>
    {{ captcha }}
    <div class="row">
      <div class="col">
        <a href="{{ back }}" class="btn btn-light">{{ button_back }}</a>
      </div>
      <div class="col text-end">
        <button type="submit" id="button-review" class="btn btn-primary">{{ button_continue }}</button>
      </div>
    </div>
  {% else %}
    {{ text_login }}
  {% endif %}
</form>
<script type="text/javascript"><!--
$('#review').on('click', '.pagination a', function(e) {
    e.preventDefault();

    $('#review').load(this.href);
});

// Forms
$('#form-review').on('submit', function(e) {
    e.preventDefault();

    var element = this;

    $.ajax({
        url: 'index.php?route=product/review.write&language={{ language }}&review_token={{ review_token }}&product_id={{ product_id }}',
        type: 'post',
        data: $('#form-review').serialize(),
        dataType: 'json',
        cache: false,
        contentType: 'application/x-www-form-urlencoded',
        processData: false,
        beforeSend: function() {
            $('#button-review').button('loading');
        },
        complete: function() {
            $('#button-review').button('reset');
        },
        success: function(json) {
            $('.alert-dismissible').remove();
            $('#form-review').find('.is-invalid').removeClass('is-invalid');
            $('#form-review').find('.invalid-feedback').removeClass('d-block');

            if (json['error']) {
                if (json['error']['warning']) {
                    $('#alert').prepend('<div class="alert alert-danger alert-dismissible"><i class="fa-solid fa-circle-exclamation"></i> ' + json['error']['warning'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
                }

                for (key in json['error']) {
                    $('#input-' + key.replaceAll('_', '-')).addClass('is-invalid').find('.form-control, .form-select, .form-check-input, .form-check-label').addClass('is-invalid');
                    $('#error-' + key.replaceAll('_', '-')).html(json['error'][key]).addClass('d-block');
                }
            }

            if (json['success']) {
                $('#alert').prepend('<div class="alert alert-success alert-dismissible"><i class="fa-solid fa-circle-exclamation"></i> ' + json['success'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');

                $('#input-text').val('');
                $('#input-rating input[type=\'radio\']').prop('checked', false);
            }
        },
        error: function(xhr, ajaxOptions, thrownError) {
            console.log(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
    });
});
//--></script>