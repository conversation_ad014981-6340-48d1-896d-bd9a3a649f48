{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="button" data-toggle="tooltip" title="{{ button_filter }}" onclick="$('#filter-pricing').toggleClass('hidden-sm hidden-xs')" class="btn btn-default hidden-md hidden-lg"><i class="fa fa-filter"></i></button>
        <a href="{{ add }}" data-toggle="tooltip" title="{{ button_add }}" class="btn btn-primary"><i class="fa fa-plus"></i></a>
        <button type="button" data-toggle="tooltip" title="{{ button_delete }}" class="btn btn-danger" onclick="confirm('{{ text_confirm }}') ? $('#form-pricing').submit() : false;"><i class="fa fa-trash-o"></i></button>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    {% if success %}
    <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    <div class="row">
      <div id="filter-pricing" class="col-md-3 col-md-push-9 col-sm-12 hidden-sm hidden-xs">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-filter"></i> {{ text_filter }}</h3>
          </div>
          <div class="panel-body">
            <div class="form-group">
              <label class="control-label" for="input-name">{{ entry_name }}</label>
              <input type="text" name="filter_name" value="{{ filter_name }}" placeholder="{{ entry_name }}" id="input-name" class="form-control" />
            </div>
            <div class="form-group">
              <label class="control-label" for="input-rule-type">{{ entry_rule_type }}</label>
              <select name="filter_rule_type" id="input-rule-type" class="form-control">
                <option value=""></option>
                <option value="product"{% if filter_rule_type == 'product' %} selected="selected"{% endif %}>{{ text_product }}</option>
                <option value="category"{% if filter_rule_type == 'category' %} selected="selected"{% endif %}>{{ text_category }}</option>
                <option value="customer"{% if filter_rule_type == 'customer' %} selected="selected"{% endif %}>{{ text_customer }}</option>
                <option value="global"{% if filter_rule_type == 'global' %} selected="selected"{% endif %}>{{ text_global }}</option>
              </select>
            </div>
            <div class="form-group">
              <label class="control-label" for="input-status">{{ entry_status }}</label>
              <select name="filter_status" id="input-status" class="form-control">
                <option value=""></option>
                <option value="1"{% if filter_status == '1' %} selected="selected"{% endif %}>{{ text_enabled }}</option>
                <option value="0"{% if filter_status == '0' %} selected="selected"{% endif %}>{{ text_disabled }}</option>
              </select>
            </div>
            <div class="form-group text-right">
              <button type="button" id="button-filter" class="btn btn-default"><i class="fa fa-filter"></i> {{ button_filter }}</button>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-9 col-md-pull-3 col-sm-12">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-list"></i> {{ text_list }}</h3>
          </div>
          <div class="panel-body">
            <form method="post" action="{{ delete }}" enctype="multipart/form-data" id="form-pricing">
              <div class="table-responsive">
                <table class="table table-bordered table-hover">
                  <thead>
                    <tr>
                      <td style="width: 1px;" class="text-center"><input type="checkbox" onclick="$('input[name*=\'selected\']').prop('checked', this.checked);" /></td>
                      <td class="text-left">{% if sort == 'name' %}
                        <a href="{{ sort_name }}" class="{{ order|lower }}">{{ column_name }}</a>
                        {% else %}
                        <a href="{{ sort_name }}">{{ column_name }}</a>
                        {% endif %}</td>
                      <td class="text-left">{% if sort == 'rule_type' %}
                        <a href="{{ sort_rule_type }}" class="{{ order|lower }}">{{ column_rule_type }}</a>
                        {% else %}
                        <a href="{{ sort_rule_type }}">{{ column_rule_type }}</a>
                        {% endif %}</td>
                      <td class="text-left">{{ column_condition }}</td>
                      <td class="text-left">{{ column_action }}</td>
                      <td class="text-left">{% if sort == 'priority' %}
                        <a href="{{ sort_priority }}" class="{{ order|lower }}">{{ column_priority }}</a>
                        {% else %}
                        <a href="{{ sort_priority }}">{{ column_priority }}</a>
                        {% endif %}</td>
                      <td class="text-left">{% if sort == 'status' %}
                        <a href="{{ sort_status }}" class="{{ order|lower }}">{{ column_status }}</a>
                        {% else %}
                        <a href="{{ sort_status }}">{{ column_status }}</a>
                        {% endif %}</td>
                      <td class="text-left">{% if sort == 'date_added' %}
                        <a href="{{ sort_date_added }}" class="{{ order|lower }}">{{ column_date_added }}</a>
                        {% else %}
                        <a href="{{ sort_date_added }}">{{ column_date_added }}</a>
                        {% endif %}</td>
                      <td class="text-right">{{ column_action }}</td>
                    </tr>
                  </thead>
                  <tbody>
                    {% if pricing_rules %}
                    {% for rule in pricing_rules %}
                    <tr>
                      <td class="text-center">{% if rule.rule_id in selected %}
                        <input type="checkbox" name="selected[]" value="{{ rule.rule_id }}" checked="checked" />
                        {% else %}
                        <input type="checkbox" name="selected[]" value="{{ rule.rule_id }}" />
                        {% endif %}</td>
                      <td class="text-left">{{ rule.name }}</td>
                      <td class="text-left">{{ rule.rule_type }}</td>
                      <td class="text-left">{{ rule.condition_type }}: {{ rule.condition_value }}</td>
                      <td class="text-left">{{ rule.action_type }}: {{ rule.action_value }}</td>
                      <td class="text-left">{{ rule.priority }}</td>
                      <td class="text-left">{{ rule.status ? text_enabled : text_disabled }}</td>
                      <td class="text-left">{{ rule.date_added }}</td>
                      <td class="text-right">
                        <a href="{{ rule.edit }}" data-toggle="tooltip" title="{{ button_edit }}" class="btn btn-primary"><i class="fa fa-pencil"></i></a>
                        <button type="button" class="btn btn-info" onclick="testRule({{ rule.rule_id }})" data-toggle="tooltip" title="{{ button_test }}"><i class="fa fa-flask"></i></button>
                      </td>
                    </tr>
                    {% endfor %}
                    {% else %}
                    <tr>
                      <td class="text-center" colspan="9">{{ text_no_results }}</td>
                    </tr>
                    {% endif %}
                  </tbody>
                </table>
              </div>
            </form>
            <div class="row">
              <div class="col-sm-6 text-left">{{ pagination }}</div>
              <div class="col-sm-6 text-right">{{ results }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Test Rule Modal -->
<div class="modal fade" id="testRuleModal" tabindex="-1" role="dialog">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
        <h4 class="modal-title">{{ text_test_rule }}</h4>
      </div>
      <div class="modal-body">
        <form id="testRuleForm">
          <div class="form-group">
            <label for="test_product_id">{{ entry_product }}</label>
            <select name="product_id" id="test_product_id" class="form-control">
              <option value="">{{ text_select }}</option>
            </select>
          </div>
          <div class="form-group">
            <label for="test_customer_id">{{ entry_customer }}</label>
            <select name="customer_id" id="test_customer_id" class="form-control">
              <option value="">{{ text_select }}</option>
            </select>
          </div>
          <div class="form-group">
            <label for="test_quantity">{{ entry_quantity }}</label>
            <input type="number" name="quantity" id="test_quantity" value="1" min="1" class="form-control" />
          </div>
        </form>
        <div id="testResults" style="display: none;">
          <hr>
          <h5>{{ text_test_results }}</h5>
          <div class="row">
            <div class="col-sm-6">
              <strong>{{ text_original_price }}:</strong> <span id="originalPrice">0</span>
            </div>
            <div class="col-sm-6">
              <strong>{{ text_final_price }}:</strong> <span id="finalPrice">0</span>
            </div>
          </div>
          <div class="row">
            <div class="col-sm-6">
              <strong>{{ text_discount }}:</strong> <span id="discount">0</span>
            </div>
            <div class="col-sm-6">
              <strong>{{ text_discount_percentage }}:</strong> <span id="discountPercentage">0%</span>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_close }}</button>
        <button type="button" class="btn btn-primary" onclick="runTest()">{{ button_test }}</button>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript">
$('#button-filter').on('click', function() {
    url = 'index.php?route=sale/dynamic_pricing&user_token={{ user_token }}';
    
    var filter_name = $('input[name=\'filter_name\']').val();
    if (filter_name) {
        url += '&filter_name=' + encodeURIComponent(filter_name);
    }
    
    var filter_rule_type = $('select[name=\'filter_rule_type\']').val();
    if (filter_rule_type !== '') {
        url += '&filter_rule_type=' + encodeURIComponent(filter_rule_type);
    }
    
    var filter_status = $('select[name=\'filter_status\']').val();
    if (filter_status !== '') {
        url += '&filter_status=' + encodeURIComponent(filter_status);
    }
    
    location = url;
});

function testRule(ruleId) {
    $('#testRuleModal').modal('show');
    loadProducts();
    loadCustomers();
}

function loadProducts() {
    $.ajax({
        url: 'index.php?route=catalog/product/autocomplete&user_token={{ user_token }}&filter_name=',
        type: 'GET',
        dataType: 'json',
        success: function(json) {
            var html = '<option value="">{{ text_select }}</option>';
            $.each(json, function(i, item) {
                html += '<option value="' + item.product_id + '">' + item.name + '</option>';
            });
            $('#test_product_id').html(html);
        }
    });
}

function loadCustomers() {
    $.ajax({
        url: 'index.php?route=customer/customer/autocomplete&user_token={{ user_token }}&filter_name=',
        type: 'GET',
        dataType: 'json',
        success: function(json) {
            var html = '<option value="">{{ text_select }}</option>';
            $.each(json, function(i, item) {
                html += '<option value="' + item.customer_id + '">' + item.name + '</option>';
            });
            $('#test_customer_id').html(html);
        }
    });
}

function runTest() {
    var formData = $('#testRuleForm').serialize();
    
    $.ajax({
        url: 'index.php?route=sale/dynamic_pricing/test&user_token={{ user_token }}',
        type: 'POST',
        data: formData,
        dataType: 'json',
        success: function(json) {
            if (json.success) {
                $('#originalPrice').text(json.original_price);
                $('#finalPrice').text(json.final_price);
                $('#discount').text(json.discount);
                $('#discountPercentage').text(json.discount_percentage.toFixed(2) + '%');
                $('#testResults').show();
            } else {
                alert(json.error);
            }
        },
        error: function() {
            alert('{{ error_test_failed }}');
        }
    });
}
</script>
{{ footer }} 