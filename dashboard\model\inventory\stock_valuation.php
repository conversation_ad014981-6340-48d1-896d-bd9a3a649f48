<?php
/**
 * نموذج تقييم المخزون المتقدم - Enterprise Grade
 * 
 * التحسينات الجديدة:
 * - تطبيق الخدمات المركزية (central_service_manager.php)
 * - تقييم بطرق متعددة (FIFO, LIFO, WAC, Standard Cost)
 * - تكامل مع النظام المحاسبي المتقدم
 * - تقارير تقييم تفصيلية ومقارنة
 * - تحليلات ربحية المخزون
 * - تقييم حسب الفروع والمخازن
 * - تقييم حسب التصنيفات والموردين
 * - تقارير تاريخية للتقييم
 * - معالجة الأخطاء الشاملة
 * - تحسين الأداء مع الفهرسة
 * - حسابات متقدمة للربحية ومعدل الدوران
 * 
 * <AUTHOR> Team - Enhanced by Kiro AI
 * @version 2.0 - Enterprise Grade
 * @since 2025
 * @reference current_stock.php - Proven Example
 */

class ModelInventoryStockValuation extends Model {
    
    private $central_service;
    
    public function __construct($registry) {
        parent::__construct($registry);
        
        // تحميل الخدمات المركزية
        $this->load->model('common/central_service_manager');
        $this->central_service = new CentralServiceManager($this->registry);
    }
    
    /**
     * الحصول على تقييمات المخزون مع فلاتر متقدمة
     */
    public function getStockValuations($data = array()) {
        try {
            $sql = "SELECT 
                        cs.product_id,
                        pd.name as product_name,
                        p.model,
                        p.sku,
                        cs.warehouse_id,
                        w.name as warehouse_name,
                        cd.name as category_name,
                        m.name as manufacturer_name,
                        cs.quantity,
                        
                        -- حساب التكلفة حسب الطريقة المختارة
                        CASE '" . $this->db->escape($data['filter_valuation_method']) . "'
                            WHEN 'fifo' THEN " . $this->getFIFOCostQuery() . "
                            WHEN 'lifo' THEN " . $this->getLIFOCostQuery() . "
                            WHEN 'wac' THEN " . $this->getWACCostQuery() . "
                            WHEN 'standard' THEN p.cost
                            WHEN 'latest' THEN " . $this->getLatestCostQuery() . "
                            WHEN 'average' THEN " . $this->getAverageCostQuery() . "
                            ELSE " . $this->getWACCostQuery() . "
                        END as unit_cost,
                        
                        -- حساب القيمة الإجمالية
                        cs.quantity * (
                            CASE '" . $this->db->escape($data['filter_valuation_method']) . "'
                                WHEN 'fifo' THEN " . $this->getFIFOCostQuery() . "
                                WHEN 'lifo' THEN " . $this->getLIFOCostQuery() . "
                                WHEN 'wac' THEN " . $this->getWACCostQuery() . "
                                WHEN 'standard' THEN p.cost
                                WHEN 'latest' THEN " . $this->getLatestCostQuery() . "
                                WHEN 'average' THEN " . $this->getAverageCostQuery() . "
                                ELSE " . $this->getWACCostQuery() . "
                            END
                        ) as total_value,
                        
                        '" . $this->db->escape($data['filter_valuation_method']) . "' as valuation_method,
                        
                        -- حساب هامش الربح
                        CASE 
                            WHEN p.price > 0 THEN 
                                ROUND(((p.price - (
                                    CASE '" . $this->db->escape($data['filter_valuation_method']) . "'
                                        WHEN 'fifo' THEN " . $this->getFIFOCostQuery() . "
                                        WHEN 'lifo' THEN " . $this->getLIFOCostQuery() . "
                                        WHEN 'wac' THEN " . $this->getWACCostQuery() . "
                                        WHEN 'standard' THEN p.cost
                                        WHEN 'latest' THEN " . $this->getLatestCostQuery() . "
                                        WHEN 'average' THEN " . $this->getAverageCostQuery() . "
                                        ELSE " . $this->getWACCostQuery() . "
                                    END
                                )) / p.price) * 100, 2)
                            ELSE 0
                        END as profit_margin,
                        
                        -- حساب معدل الدوران (تقريبي)
                        COALESCE((
                            SELECT 
                                CASE 
                                    WHEN AVG(cs2.quantity) > 0 THEN 
                                        SUM(CASE WHEN sm.movement_type IN ('out', 'adjustment_out', 'transfer_out') THEN sm.quantity ELSE 0 END) / AVG(cs2.quantity)
                                    ELSE 0
                                END
                            FROM " . DB_PREFIX . "stock_movement sm
                            LEFT JOIN " . DB_PREFIX . "current_stock cs2 ON (sm.product_id = cs2.product_id AND sm.warehouse_id = cs2.warehouse_id)
                            WHERE sm.product_id = cs.product_id 
                            AND sm.warehouse_id = cs.warehouse_id
                            AND DATE(sm.date_added) >= DATE_SUB('" . $this->db->escape($data['filter_date']) . "', INTERVAL 12 MONTH)
                        ), 0) as turnover_ratio,
                        
                        NOW() as last_updated
                        
                    FROM " . DB_PREFIX . "current_stock cs
                    LEFT JOIN " . DB_PREFIX . "product p ON (cs.product_id = p.product_id)
                    LEFT JOIN " . DB_PREFIX . "product_description pd ON (p.product_id = pd.product_id AND pd.language_id = '" . (int)$this->config->get('config_language_id') . "')
                    LEFT JOIN " . DB_PREFIX . "warehouse w ON (cs.warehouse_id = w.warehouse_id)
                    LEFT JOIN " . DB_PREFIX . "product_to_category p2c ON (p.product_id = p2c.product_id)
                    LEFT JOIN " . DB_PREFIX . "category_description cd ON (p2c.category_id = cd.category_id AND cd.language_id = '" . (int)$this->config->get('config_language_id') . "')
                    LEFT JOIN " . DB_PREFIX . "manufacturer m ON (p.manufacturer_id = m.manufacturer_id)
                    WHERE 1=1";
            
            // تطبيق الفلاتر
            if (!empty($data['filter_warehouse_id'])) {
                $sql .= " AND cs.warehouse_id = '" . (int)$data['filter_warehouse_id'] . "'";
            }
            
            if (!empty($data['filter_category_id'])) {
                $sql .= " AND p2c.category_id = '" . (int)$data['filter_category_id'] . "'";
            }
            
            if (!empty($data['filter_manufacturer_id'])) {
                $sql .= " AND p.manufacturer_id = '" . (int)$data['filter_manufacturer_id'] . "'";
            }
            
            if (!$data['filter_include_zero_stock']) {
                $sql .= " AND cs.quantity > 0";
            }
            
            $sql .= " AND p.status = 1";
            
            // ترتيب النتائج
            $sort_data = array(
                'pd.name',
                'p.model',
                'p.sku',
                'w.name',
                'cd.name',
                'm.name',
                'cs.quantity',
                'unit_cost',
                'total_value',
                'profit_margin',
                'turnover_ratio'
            );
            
            if (isset($data['sort']) && in_array($data['sort'], $sort_data)) {
                $sql .= " ORDER BY " . $data['sort'];
            } else {
                $sql .= " ORDER BY pd.name";
            }
            
            if (isset($data['order']) && ($data['order'] == 'DESC')) {
                $sql .= " DESC";
            } else {
                $sql .= " ASC";
            }
            
            // تحديد النطاق
            if (isset($data['start']) || isset($data['limit'])) {
                if ($data['start'] < 0) {
                    $data['start'] = 0;
                }
                
                if ($data['limit'] < 1) {
                    $data['limit'] = 20;
                }
                
                $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
            }
            
            $query = $this->db->query($sql);
            
            return $query->rows;
            
        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'stock_valuation_model',
                'خطأ في الحصول على تقييمات المخزون: ' . $e->getMessage(),
                array('error' => $e->getTraceAsString())
            );
            
            return array();
        }
    }
    
    /**
     * الحصول على إجمالي عدد التقييمات
     */
    public function getTotalStockValuations($data = array()) {
        try {
            $sql = "SELECT COUNT(*) AS total 
                    FROM " . DB_PREFIX . "current_stock cs
                    LEFT JOIN " . DB_PREFIX . "product p ON (cs.product_id = p.product_id)
                    LEFT JOIN " . DB_PREFIX . "product_to_category p2c ON (p.product_id = p2c.product_id)
                    WHERE 1=1";
            
            // تطبيق نفس الفلاتر
            if (!empty($data['filter_warehouse_id'])) {
                $sql .= " AND cs.warehouse_id = '" . (int)$data['filter_warehouse_id'] . "'";
            }
            
            if (!empty($data['filter_category_id'])) {
                $sql .= " AND p2c.category_id = '" . (int)$data['filter_category_id'] . "'";
            }
            
            if (!empty($data['filter_manufacturer_id'])) {
                $sql .= " AND p.manufacturer_id = '" . (int)$data['filter_manufacturer_id'] . "'";
            }
            
            if (!$data['filter_include_zero_stock']) {
                $sql .= " AND cs.quantity > 0";
            }
            
            $sql .= " AND p.status = 1";
            
            $query = $this->db->query($sql);
            
            return $query->row['total'];
            
        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'stock_valuation_model',
                'خطأ في حساب إجمالي التقييمات: ' . $e->getMessage(),
                array('error' => $e->getTraceAsString())
            );
            
            return 0;
        }
    }
    
    /**
     * الحصول على ملخص التقييم
     */
    public function getValuationSummary($data = array()) {
        try {
            $sql = "SELECT 
                        COUNT(*) as total_products,
                        SUM(cs.quantity) as total_quantity,
                        SUM(cs.quantity * (
                            CASE '" . $this->db->escape($data['filter_valuation_method']) . "'
                                WHEN 'fifo' THEN " . $this->getFIFOCostQuery() . "
                                WHEN 'lifo' THEN " . $this->getLIFOCostQuery() . "
                                WHEN 'wac' THEN " . $this->getWACCostQuery() . "
                                WHEN 'standard' THEN p.cost
                                WHEN 'latest' THEN " . $this->getLatestCostQuery() . "
                                WHEN 'average' THEN " . $this->getAverageCostQuery() . "
                                ELSE " . $this->getWACCostQuery() . "
                            END
                        )) as total_value,
                        AVG(cs.quantity * (
                            CASE '" . $this->db->escape($data['filter_valuation_method']) . "'
                                WHEN 'fifo' THEN " . $this->getFIFOCostQuery() . "
                                WHEN 'lifo' THEN " . $this->getLIFOCostQuery() . "
                                WHEN 'wac' THEN " . $this->getWACCostQuery() . "
                                WHEN 'standard' THEN p.cost
                                WHEN 'latest' THEN " . $this->getLatestCostQuery() . "
                                WHEN 'average' THEN " . $this->getAverageCostQuery() . "
                                ELSE " . $this->getWACCostQuery() . "
                            END
                        )) as avg_value_per_product,
                        COUNT(DISTINCT cs.warehouse_id) as total_warehouses
                    FROM " . DB_PREFIX . "current_stock cs
                    LEFT JOIN " . DB_PREFIX . "product p ON (cs.product_id = p.product_id)
                    LEFT JOIN " . DB_PREFIX . "product_to_category p2c ON (p.product_id = p2c.product_id)
                    WHERE 1=1";
            
            // تطبيق نفس الفلاتر
            if (!empty($data['filter_warehouse_id'])) {
                $sql .= " AND cs.warehouse_id = '" . (int)$data['filter_warehouse_id'] . "'";
            }
            
            if (!empty($data['filter_category_id'])) {
                $sql .= " AND p2c.category_id = '" . (int)$data['filter_category_id'] . "'";
            }
            
            if (!empty($data['filter_manufacturer_id'])) {
                $sql .= " AND p.manufacturer_id = '" . (int)$data['filter_manufacturer_id'] . "'";
            }
            
            if (!$data['filter_include_zero_stock']) {
                $sql .= " AND cs.quantity > 0";
            }
            
            $sql .= " AND p.status = 1";
            
            $query = $this->db->query($sql);
            
            return $query->row;
            
        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'stock_valuation_summary',
                'خطأ في الحصول على ملخص التقييم: ' . $e->getMessage(),
                array('error' => $e->getTraceAsString())
            );
            
            return array();
        }
    }
    
    /**
     * إعادة حساب التقييم
     */
    public function recalculateValuation() {
        try {
            $updated_products = 0;
            
            // إعادة حساب التكلفة المتوسطة المرجحة لجميع المنتجات
            $sql = "UPDATE " . DB_PREFIX . "product p SET 
                        cost = (
                            SELECT 
                                CASE 
                                    WHEN SUM(CASE WHEN sm.movement_type IN ('in', 'adjustment_in', 'transfer_in', 'return_in') THEN sm.quantity ELSE 0 END) > 0
                                    THEN SUM(CASE WHEN sm.movement_type IN ('in', 'adjustment_in', 'transfer_in', 'return_in') THEN sm.total_cost ELSE 0 END) / 
                                         SUM(CASE WHEN sm.movement_type IN ('in', 'adjustment_in', 'transfer_in', 'return_in') THEN sm.quantity ELSE 0 END)
                                    ELSE p.cost
                                END
                            FROM " . DB_PREFIX . "stock_movement sm 
                            WHERE sm.product_id = p.product_id
                        ),
                        date_modified = NOW()
                    WHERE p.status = 1";
            
            $this->db->query($sql);
            $updated_products = $this->db->countAffected();
            
            // تسجيل النشاط
            $this->central_service->logActivity(
                'recalculate_valuation',
                'stock_valuation',
                'إعادة حساب تقييم المخزون لـ ' . $updated_products . ' منتج',
                array('updated_products' => $updated_products)
            );
            
            return array(
                'success' => true,
                'updated_products' => $updated_products
            );
            
        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'recalculate_valuation',
                'خطأ في إعادة حساب التقييم: ' . $e->getMessage(),
                array('error' => $e->getTraceAsString())
            );
            
            return array(
                'success' => false,
                'error' => $e->getMessage()
            );
        }
    }
    
    /**
     * استعلام تكلفة FIFO
     */
    private function getFIFOCostQuery() {
        return "(
            SELECT COALESCE(sm.unit_cost, p.cost)
            FROM " . DB_PREFIX . "stock_movement sm 
            WHERE sm.product_id = cs.product_id 
            AND sm.warehouse_id = cs.warehouse_id
            AND sm.movement_type IN ('in', 'adjustment_in', 'transfer_in', 'return_in')
            ORDER BY sm.date_added ASC
            LIMIT 1
        )";
    }
    
    /**
     * استعلام تكلفة LIFO
     */
    private function getLIFOCostQuery() {
        return "(
            SELECT COALESCE(sm.unit_cost, p.cost)
            FROM " . DB_PREFIX . "stock_movement sm 
            WHERE sm.product_id = cs.product_id 
            AND sm.warehouse_id = cs.warehouse_id
            AND sm.movement_type IN ('in', 'adjustment_in', 'transfer_in', 'return_in')
            ORDER BY sm.date_added DESC
            LIMIT 1
        )";
    }
    
    /**
     * استعلام التكلفة المتوسطة المرجحة
     */
    private function getWACCostQuery() {
        return "(
            SELECT 
                CASE 
                    WHEN SUM(CASE WHEN sm.movement_type IN ('in', 'adjustment_in', 'transfer_in', 'return_in') THEN sm.quantity ELSE 0 END) > 0
                    THEN SUM(CASE WHEN sm.movement_type IN ('in', 'adjustment_in', 'transfer_in', 'return_in') THEN sm.total_cost ELSE 0 END) / 
                         SUM(CASE WHEN sm.movement_type IN ('in', 'adjustment_in', 'transfer_in', 'return_in') THEN sm.quantity ELSE 0 END)
                    ELSE p.cost
                END
            FROM " . DB_PREFIX . "stock_movement sm 
            WHERE sm.product_id = cs.product_id 
            AND sm.warehouse_id = cs.warehouse_id
        )";
    }
    
    /**
     * استعلام آخر تكلفة
     */
    private function getLatestCostQuery() {
        return "(
            SELECT COALESCE(sm.unit_cost, p.cost)
            FROM " . DB_PREFIX . "stock_movement sm 
            WHERE sm.product_id = cs.product_id 
            AND sm.warehouse_id = cs.warehouse_id
            AND sm.movement_type IN ('in', 'adjustment_in', 'transfer_in', 'return_in')
            ORDER BY sm.date_added DESC
            LIMIT 1
        )";
    }
    
    /**
     * استعلام متوسط التكلفة
     */
    private function getAverageCostQuery() {
        return "(
            SELECT COALESCE(AVG(sm.unit_cost), p.cost)
            FROM " . DB_PREFIX . "stock_movement sm 
            WHERE sm.product_id = cs.product_id 
            AND sm.warehouse_id = cs.warehouse_id
            AND sm.movement_type IN ('in', 'adjustment_in', 'transfer_in', 'return_in')
        )";
    }
}
