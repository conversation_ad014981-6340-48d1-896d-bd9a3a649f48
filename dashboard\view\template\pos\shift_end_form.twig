{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="submit" form="form-shift-end" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fa fa-save"></i></button>
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a>
      </div>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pencil"></i> {{ text_form }}</h3>
      </div>
      <div class="panel-body">
        <form action="{{ action }}" method="post" enctype="multipart/form-data" id="form-shift-end" class="form-horizontal">
          <div class="form-group">
            <label class="col-sm-2 control-label">{{ entry_shift_info }}</label>
            <div class="col-sm-10">
              <div class="well well-sm">
                <p><strong>{{ entry_user }}:</strong> {{ shift.user_name }}</p>
                <p><strong>{{ entry_terminal }}:</strong> {{ shift.terminal_name }}</p>
                <p><strong>{{ entry_branch }}:</strong> {{ shift.branch_name }}</p>
                <p><strong>{{ entry_start_time }}:</strong> {{ shift.start_time }}</p>
                <p><strong>{{ entry_starting_cash }}:</strong> {{ starting_cash }}</p>
                <p><strong>{{ entry_sales_total }}:</strong> {{ sales_total }}</p>
                <p><strong>{{ entry_expected_cash }}:</strong> {{ expected_cash }}</p>
              </div>
            </div>
          </div>
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-ending-cash">{{ entry_ending_cash }}</label>
            <div class="col-sm-10">
              <input type="text" name="ending_cash" value="" placeholder="{{ entry_ending_cash }}" id="input-ending-cash" class="form-control" />
              {% if error_ending_cash %}
              <div class="text-danger">{{ error_ending_cash }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-notes">{{ entry_notes }}</label>
            <div class="col-sm-10">
              <textarea name="notes" rows="5" placeholder="{{ entry_notes }}" id="input-notes" class="form-control"></textarea>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
{{ footer }}