{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <a href="{{ back }}" class="btn btn-default"><i class="fa fa-arrow-left"></i> {{ button_back }}</a>
        <a href="{{ print }}" target="_blank" class="btn btn-info"><i class="fa fa-print"></i> {{ button_print }}</a>
        <a href="{{ export_excel }}" class="btn btn-success"><i class="fa fa-file-excel-o"></i> {{ button_export_excel }}</a>
        <a href="{{ refresh }}" class="btn btn-primary"><i class="fa fa-refresh"></i> {{ button_refresh }}</a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible">
      <i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    
    <!-- فلاتر التقرير -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-filter"></i> {{ text_filters }}</h3>
      </div>
      <div class="panel-body">
        <form method="get" id="filter-form">
          <input type="hidden" name="route" value="inventory/stock_movement/lotReport" />
          <input type="hidden" name="user_token" value="{{ user_token }}" />
          
          <div class="row">
            <div class="col-md-3">
              <div class="form-group">
                <label>{{ entry_product_name }}</label>
                <input type="text" name="filter_product_name" value="{{ filter_product_name }}" class="form-control" placeholder="{{ entry_product_name }}" />
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <label>{{ entry_branch }}</label>
                <select name="filter_branch_id" class="form-control">
                  <option value="">{{ text_all }}</option>
                  {% for branch in branches %}
                  <option value="{{ branch.branch_id }}"{% if branch.branch_id == filter_branch_id %} selected{% endif %}>{{ branch.name }}</option>
                  {% endfor %}
                </select>
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <label>{{ entry_lot_number }}</label>
                <input type="text" name="filter_lot_number" value="{{ filter_lot_number }}" class="form-control" placeholder="{{ entry_lot_number }}" />
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <label>&nbsp;</label>
                <div>
                  <button type="submit" class="btn btn-primary"><i class="fa fa-search"></i> {{ button_filter }}</button>
                  <button type="button" class="btn btn-default" onclick="clearFilters()"><i class="fa fa-times"></i> {{ button_clear }}</button>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
    
    <!-- ملخص التقرير -->
    <div class="row">
      <div class="col-md-3">
        <div class="panel panel-primary">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-tags fa-2x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ summary.total_lots }}</div>
                <div>{{ text_total_lots }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-md-3">
        <div class="panel panel-success">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-check-circle fa-2x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ summary.active_lots }}</div>
                <div>{{ text_active_lots }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-md-3">
        <div class="panel panel-warning">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-times-circle fa-2x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ summary.depleted_lots }}</div>
                <div>{{ text_depleted_lots }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-md-3">
        <div class="panel panel-info">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-cubes fa-2x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ summary.total_products }}</div>
                <div>{{ text_total_products }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- جدول تقرير الدفعات -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-list"></i> {{ text_lot_report_details }}</h3>
      </div>
      <div class="panel-body">
        <div class="table-responsive">
          <table class="table table-bordered table-hover" id="lot-report-table">
            <thead>
              <tr>
                <th>{{ column_product_name }}</th>
                <th>{{ column_branch }}</th>
                <th>{{ column_lot_number }}</th>
                <th>{{ column_expiry_date }}</th>
                <th class="text-right">{{ column_total_in }}</th>
                <th class="text-right">{{ column_total_out }}</th>
                <th class="text-right">{{ column_balance }}</th>
                <th>{{ column_unit }}</th>
                <th class="text-center">{{ column_movements_count }}</th>
                <th>{{ column_first_movement }}</th>
                <th>{{ column_last_movement }}</th>
                <th class="text-center">{{ column_status }}</th>
                <th class="text-center">{{ column_action }}</th>
              </tr>
            </thead>
            <tbody>
              {% for lot in lot_report %}
              <tr class="{% if lot.status == 'depleted' %}warning{% endif %}">
                <td>
                  <strong>{{ lot.product_name }}</strong>
                  {% if lot.model %}
                    <br><small class="text-muted">{{ lot.model }}</small>
                  {% endif %}
                </td>
                <td>{{ lot.branch_name }}</td>
                <td>
                  <span class="label label-info">{{ lot.lot_number }}</span>
                </td>
                <td>
                  {% if lot.expiry_date %}
                    {{ lot.expiry_date }}
                    {% set days_to_expiry = (lot.expiry_date|date('U') - 'now'|date('U')) / 86400 %}
                    {% if days_to_expiry < 0 %}
                      <br><span class="label label-danger">منتهية</span>
                    {% elseif days_to_expiry <= 30 %}
                      <br><span class="label label-warning">قريبة</span>
                    {% endif %}
                  {% else %}
                    <span class="text-muted">---</span>
                  {% endif %}
                </td>
                <td class="text-right">
                  <span class="text-success">{{ lot.total_in }}</span>
                </td>
                <td class="text-right">
                  <span class="text-danger">{{ lot.total_out }}</span>
                </td>
                <td class="text-right">
                  <strong class="{% if lot.balance > 0 %}text-success{% else %}text-muted{% endif %}">
                    {{ lot.balance }}
                  </strong>
                </td>
                <td>{{ lot.unit_name }}</td>
                <td class="text-center">
                  <span class="badge badge-primary">{{ lot.movements_count }}</span>
                </td>
                <td>
                  <small>{{ lot.first_movement }}</small>
                </td>
                <td>
                  <small>{{ lot.last_movement }}</small>
                </td>
                <td class="text-center">
                  {% if lot.status == 'active' %}
                    <span class="label label-success">{{ text_active }}</span>
                  {% else %}
                    <span class="label label-default">{{ text_depleted }}</span>
                  {% endif %}
                </td>
                <td class="text-center">
                  <div class="btn-group">
                    <a href="{{ lot.product_card }}" class="btn btn-xs btn-info" title="{{ button_product_card }}">
                      <i class="fa fa-list-alt"></i>
                    </a>
                    <a href="{{ lot.lot_movements }}" class="btn btn-xs btn-primary" title="{{ button_lot_movements }}">
                      <i class="fa fa-history"></i>
                    </a>
                  </div>
                </td>
              </tr>
              {% else %}
              <tr>
                <td class="text-center" colspan="13">{{ text_no_results }}</td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    </div>
    
    <!-- تحليل الدفعات -->
    {% if lot_analysis %}
    <div class="row">
      <div class="col-md-6">
        <div class="panel panel-info">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-pie-chart"></i> {{ text_lot_status_distribution }}</h3>
          </div>
          <div class="panel-body">
            <canvas id="lotStatusChart" width="400" height="200"></canvas>
          </div>
        </div>
      </div>
      
      <div class="col-md-6">
        <div class="panel panel-success">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-bar-chart"></i> {{ text_lot_movement_trends }}</h3>
          </div>
          <div class="panel-body">
            <canvas id="lotTrendsChart" width="400" height="200"></canvas>
          </div>
        </div>
      </div>
    </div>
    {% endif %}
  </div>
</div>

<style>
.huge {
    font-size: 30px;
}

.panel-primary > .panel-heading {
    color: white;
    background-color: #337ab7;
    border-color: #337ab7;
}

.panel-success > .panel-heading {
    color: white;
    background-color: #5cb85c;
    border-color: #5cb85c;
}

.panel-warning > .panel-heading {
    color: white;
    background-color: #f0ad4e;
    border-color: #f0ad4e;
}

.panel-info > .panel-heading {
    color: white;
    background-color: #5bc0de;
    border-color: #5bc0de;
}

#lot-report-table {
    font-size: 13px;
}

#lot-report-table th {
    background-color: #f5f5f5;
    font-weight: 600;
}

.badge-primary {
    background-color: #337ab7;
}

.table > tbody > tr.warning > td {
    background-color: #fcf8e3;
}
</style>

<script>
$(document).ready(function() {
    // تهيئة DataTable
    $('#lot-report-table').DataTable({
        "paging": true,
        "searching": true,
        "ordering": true,
        "info": true,
        "responsive": true,
        "pageLength": 25,
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Arabic.json"
        },
        "order": [[ 3, "asc" ]], // ترتيب حسب تاريخ انتهاء الصلاحية
        "columnDefs": [
            {
                "targets": [4, 5, 6], // أعمدة الكميات
                "className": "text-right"
            },
            {
                "targets": [8], // عمود عدد الحركات
                "className": "text-center"
            }
        ]
    });
    
    // رسم بياني لحالة الدفعات
    {% if lot_analysis %}
    var ctx1 = document.getElementById('lotStatusChart').getContext('2d');
    var lotStatusChart = new Chart(ctx1, {
        type: 'doughnut',
        data: {
            labels: ['{{ text_active }}', '{{ text_depleted }}'],
            datasets: [{
                data: [{{ lot_analysis.active_lots }}, {{ lot_analysis.depleted_lots }}],
                backgroundColor: ['#5cb85c', '#f0ad4e']
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
    
    // رسم بياني لاتجاهات الحركة
    var ctx2 = document.getElementById('lotTrendsChart').getContext('2d');
    var lotTrendsChart = new Chart(ctx2, {
        type: 'line',
        data: {
            labels: {{ lot_analysis.months|json_encode }},
            datasets: [{
                label: '{{ text_new_lots }}',
                data: {{ lot_analysis.new_lots_trend|json_encode }},
                borderColor: '#337ab7',
                backgroundColor: 'rgba(51, 122, 183, 0.1)'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
    {% endif %}
});

function clearFilters() {
    $('input[name^="filter_"]').val('');
    $('select[name^="filter_"]').val('');
    $('#filter-form').submit();
}
</script>

{{ footer }}