{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="submit" form="form-feedback" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fa fa-save"></i></button>
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pencil"></i> {{ text_form }}</h3>
      </div>
      <div class="panel-body">
        <form action="{{ action }}" method="post" enctype="multipart/form-data" id="form-feedback" class="form-horizontal">
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-customer">{{ entry_customer }}</label>
            <div class="col-sm-10">
              <select name="customer_id" id="input-customer" class="form-control">
                <option value="">{{ text_select }}</option>
                {% for customer in customers %}
                <option value="{{ customer.customer_id }}"{% if customer.customer_id == customer_id %} selected="selected"{% endif %}>{{ customer.firstname }} {{ customer.lastname }} ({{ customer.email }})</option>
                {% endfor %}
              </select>
              {% if error_customer %}
              <div class="text-danger">{{ error_customer }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-subject">{{ entry_subject }}</label>
            <div class="col-sm-10">
              <input type="text" name="subject" value="{{ subject }}" placeholder="{{ entry_subject }}" id="input-subject" class="form-control" />
              {% if error_subject %}
              <div class="text-danger">{{ error_subject }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-description">{{ entry_description }}</label>
            <div class="col-sm-10">
              <textarea name="description" rows="5" placeholder="{{ entry_description }}" id="input-description" class="form-control">{{ description }}</textarea>
              {% if error_description %}
              <div class="text-danger">{{ error_description }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-feedback-type">{{ entry_feedback_type }}</label>
            <div class="col-sm-10">
              <select name="feedback_type" id="input-feedback-type" class="form-control">
                <option value="inquiry"{% if feedback_type == 'inquiry' %} selected="selected"{% endif %}>{{ text_inquiry }}</option>
                <option value="complaint"{% if feedback_type == 'complaint' %} selected="selected"{% endif %}>{{ text_complaint }}</option>
                <option value="suggestion"{% if feedback_type == 'suggestion' %} selected="selected"{% endif %}>{{ text_suggestion }}</option>
                <option value="appreciation"{% if feedback_type == 'appreciation' %} selected="selected"{% endif %}>{{ text_appreciation }}</option>
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-priority">{{ entry_priority }}</label>
            <div class="col-sm-10">
              <select name="priority" id="input-priority" class="form-control">
                <option value="low"{% if priority == 'low' %} selected="selected"{% endif %}>{{ text_low }}</option>
                <option value="medium"{% if priority == 'medium' %} selected="selected"{% endif %}>{{ text_medium }}</option>
                <option value="high"{% if priority == 'high' %} selected="selected"{% endif %}>{{ text_high }}</option>
                <option value="critical"{% if priority == 'critical' %} selected="selected"{% endif %}>{{ text_critical }}</option>
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-status">{{ entry_status }}</label>
            <div class="col-sm-10">
              <select name="status" id="input-status" class="form-control">
                <option value="new"{% if status == 'new' %} selected="selected"{% endif %}>{{ text_new }}</option>
                <option value="in_progress"{% if status == 'in_progress' %} selected="selected"{% endif %}>{{ text_in_progress }}</option>
                <option value="resolved"{% if status == 'resolved' %} selected="selected"{% endif %}>{{ text_resolved }}</option>
                <option value="closed"{% if status == 'closed' %} selected="selected"{% endif %}>{{ text_closed }}</option>
                <option value="cancelled"{% if status == 'cancelled' %} selected="selected"{% endif %}>{{ text_cancelled }}</option>
              </select>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

{{ footer }} 