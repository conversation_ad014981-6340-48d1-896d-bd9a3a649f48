/**
 * AYM ERP - Login Page JavaScript
 * Advanced Login Interface Interactions
 * 
 * @package    AYM ERP
 * <AUTHOR> Team
 * @copyright  2025 AYM ERP
 * @license    Commercial
 * @version    1.0.0
 */

var LoginPage = {
    
    /**
     * Initialize login page functionality
     */
    init: function() {
        this.bindEvents();
        this.initFloatingLabels();
        this.initPasswordToggle();
        this.initFormValidation();
        this.initKeyboardShortcuts();
        this.initAnimations();
    },
    
    /**
     * Bind event handlers
     */
    bindEvents: function() {
        var self = this;
        
        // Form submission
        $('#login-form').on('submit', function(e) {
            e.preventDefault();
            self.handleLogin();
        });
        
        // Input focus effects
        $('.form-control').on('focus blur', function() {
            self.handleInputFocus($(this));
        });
        
        // Real-time validation
        $('.form-control').on('input', function() {
            self.validateField($(this));
        });
        
        // Language switcher
        $('.lang-link').on('click', function(e) {
            e.preventDefault();
            self.switchLanguage($(this).attr('href'));
        });
        
        // Remember me tooltip
        $('.checkbox-container').on('mouseenter', function() {
            self.showTooltip($(this), 'سيتم تذكر بيانات الدخول لمدة 30 يوماً');
        });
    },
    
    /**
     * Initialize floating labels
     */
    initFloatingLabels: function() {
        $('.form-control').each(function() {
            var $input = $(this);
            var $label = $input.siblings('.floating-label');
            
            if ($input.val()) {
                $label.addClass('active');
            }
            
            $input.on('focus', function() {
                $label.addClass('active');
            });
            
            $input.on('blur', function() {
                if (!$input.val()) {
                    $label.removeClass('active');
                }
            });
        });
    },
    
    /**
     * Initialize password toggle functionality
     */
    initPasswordToggle: function() {
        $('#password-toggle').on('click', function() {
            var $password = $('#input-password');
            var $icon = $(this).find('i');
            
            if ($password.attr('type') === 'password') {
                $password.attr('type', 'text');
                $icon.removeClass('fa-eye').addClass('fa-eye-slash');
            } else {
                $password.attr('type', 'password');
                $icon.removeClass('fa-eye-slash').addClass('fa-eye');
            }
        });
    },
    
    /**
     * Initialize form validation
     */
    initFormValidation: function() {
        var self = this;
        
        // Custom validation rules
        $.validator.addMethod('strongPassword', function(value, element) {
            return this.optional(element) || /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$/.test(value);
        }, 'كلمة المرور يجب أن تحتوي على 8 أحرف على الأقل مع أحرف كبيرة وصغيرة وأرقام');
        
        // Initialize validation
        $('#login-form').validate({
            rules: {
                username: {
                    required: true,
                    minlength: 3
                },
                password: {
                    required: true,
                    minlength: 6
                }
            },
            messages: {
                username: {
                    required: 'يرجى إدخال اسم المستخدم',
                    minlength: 'اسم المستخدم يجب أن يكون 3 أحرف على الأقل'
                },
                password: {
                    required: 'يرجى إدخال كلمة المرور',
                    minlength: 'كلمة المرور يجب أن تكون 6 أحرف على الأقل'
                }
            },
            errorElement: 'div',
            errorClass: 'error-message',
            errorPlacement: function(error, element) {
                error.insertAfter(element.closest('.input-container'));
            },
            highlight: function(element) {
                $(element).addClass('error').closest('.input-container').addClass('has-error');
            },
            unhighlight: function(element) {
                $(element).removeClass('error').closest('.input-container').removeClass('has-error');
            }
        });
    },
    
    /**
     * Initialize keyboard shortcuts
     */
    initKeyboardShortcuts: function() {
        $(document).on('keydown', function(e) {
            // Ctrl + Enter to submit form
            if (e.ctrlKey && e.keyCode === 13) {
                $('#login-form').submit();
            }
            
            // Escape to clear form
            if (e.keyCode === 27) {
                $('#login-form')[0].reset();
                $('.floating-label').removeClass('active');
            }
        });
    },
    
    /**
     * Initialize animations
     */
    initAnimations: function() {
        // Stagger animation for form elements
        $('.form-group').each(function(index) {
            $(this).css('animation-delay', (index * 0.1) + 's');
            $(this).addClass('animated fadeInUp');
        });
        
        // Parallax effect for floating shapes
        $(window).on('mousemove', function(e) {
            var mouseX = e.clientX / window.innerWidth;
            var mouseY = e.clientY / window.innerHeight;
            
            $('.shape').each(function(index) {
                var speed = (index + 1) * 0.5;
                var x = mouseX * speed;
                var y = mouseY * speed;
                
                $(this).css('transform', 'translate(' + x + 'px, ' + y + 'px)');
            });
        });
    },
    
    /**
     * Handle login form submission
     */
    handleLogin: function() {
        var self = this;
        var $form = $('#login-form');
        var $button = $('#login-btn');
        var $overlay = $('#loading-overlay');
        
        // Validate form
        if (!$form.valid()) {
            self.showError('يرجى تصحيح الأخطاء في النموذج');
            return;
        }
        
        // Show loading state
        $button.prop('disabled', true);
        $button.find('.btn-text').hide();
        $button.find('.btn-loading').show();
        $overlay.fadeIn(300);
        
        // Simulate network delay for better UX
        setTimeout(function() {
            // Submit form
            $form.off('submit').submit();
        }, 1000);
    },
    
    /**
     * Handle input focus effects
     */
    handleInputFocus: function($input) {
        var $container = $input.closest('.input-container');
        var $icon = $container.find('.input-icon');
        
        if ($input.is(':focus')) {
            $container.addClass('focused');
            $icon.addClass('active');
        } else {
            $container.removeClass('focused');
            $icon.removeClass('active');
        }
    },
    
    /**
     * Validate individual field
     */
    validateField: function($input) {
        var value = $input.val();
        var $container = $input.closest('.input-container');
        
        // Remove previous validation classes
        $container.removeClass('valid invalid');
        
        if (value.length > 0) {
            if ($input.attr('name') === 'username' && value.length >= 3) {
                $container.addClass('valid');
            } else if ($input.attr('name') === 'password' && value.length >= 6) {
                $container.addClass('valid');
            } else if (value.length < 3) {
                $container.addClass('invalid');
            }
        }
    },
    
    /**
     * Switch language
     */
    switchLanguage: function(url) {
        var self = this;
        
        // Show loading
        self.showLoading();
        
        // Redirect to new language
        setTimeout(function() {
            window.location.href = url;
        }, 500);
    },
    
    /**
     * Show error message
     */
    showError: function(message) {
        var $alert = $('<div class="alert alert-danger animated fadeInDown">' +
                      '<i class="fa fa-exclamation-triangle"></i>' +
                      '<span>' + message + '</span>' +
                      '</div>');
        
        $('.login-form').prepend($alert);
        
        setTimeout(function() {
            $alert.fadeOut(300, function() {
                $alert.remove();
            });
        }, 5000);
    },
    
    /**
     * Show success message
     */
    showSuccess: function(message) {
        var $alert = $('<div class="alert alert-success animated fadeInDown">' +
                      '<i class="fa fa-check-circle"></i>' +
                      '<span>' + message + '</span>' +
                      '</div>');
        
        $('.login-form').prepend($alert);
        
        setTimeout(function() {
            $alert.fadeOut(300, function() {
                $alert.remove();
            });
        }, 3000);
    },
    
    /**
     * Show loading overlay
     */
    showLoading: function() {
        $('#loading-overlay').fadeIn(300);
    },
    
    /**
     * Hide loading overlay
     */
    hideLoading: function() {
        $('#loading-overlay').fadeOut(300);
    },
    
    /**
     * Show tooltip
     */
    showTooltip: function($element, text) {
        var $tooltip = $('<div class="tooltip-custom">' + text + '</div>');
        
        $('body').append($tooltip);
        
        var offset = $element.offset();
        $tooltip.css({
            top: offset.top - $tooltip.outerHeight() - 10,
            left: offset.left + ($element.outerWidth() / 2) - ($tooltip.outerWidth() / 2)
        });
        
        $tooltip.fadeIn(200);
        
        $element.on('mouseleave', function() {
            $tooltip.fadeOut(200, function() {
                $tooltip.remove();
            });
        });
    },
    
    /**
     * Auto-fill demo credentials
     */
    fillDemoCredentials: function() {
        $('#input-username').val('admin').trigger('input');
        $('#input-password').val('admin').trigger('input');
        $('.floating-label').addClass('active');
    },
    
    /**
     * Clear form
     */
    clearForm: function() {
        $('#login-form')[0].reset();
        $('.floating-label').removeClass('active');
        $('.input-container').removeClass('valid invalid focused');
        $('.error-message').remove();
    },
    
    /**
     * Check caps lock
     */
    checkCapsLock: function(e) {
        var capsLock = e.getModifierState && e.getModifierState('CapsLock');
        var $warning = $('.caps-lock-warning');
        
        if (capsLock) {
            if (!$warning.length) {
                var warning = '<div class="caps-lock-warning">' +
                             '<i class="fa fa-exclamation-triangle"></i> ' +
                             'تحذير: مفتاح Caps Lock مفعل' +
                             '</div>';
                $('#input-password').closest('.input-container').after(warning);
            }
        } else {
            $warning.remove();
        }
    }
};

// Additional CSS for validation and tooltips
$(document).ready(function() {
    $('<style>')
        .prop('type', 'text/css')
        .html(`
            .input-container.has-error .form-control {
                border-color: #dc3545;
                box-shadow: 0 0 0 4px rgba(220, 53, 69, 0.1);
            }
            
            .input-container.valid .form-control {
                border-color: #28a745;
                box-shadow: 0 0 0 4px rgba(40, 167, 69, 0.1);
            }
            
            .input-container.valid .input-icon {
                color: #28a745;
            }
            
            .error-message {
                color: #dc3545;
                font-size: 12px;
                margin-top: 5px;
                display: block;
            }
            
            .tooltip-custom {
                position: absolute;
                background: #333;
                color: white;
                padding: 8px 12px;
                border-radius: 6px;
                font-size: 12px;
                z-index: 1000;
                display: none;
            }
            
            .tooltip-custom:after {
                content: '';
                position: absolute;
                top: 100%;
                left: 50%;
                margin-left: -5px;
                border: 5px solid transparent;
                border-top-color: #333;
            }
            
            .caps-lock-warning {
                color: #ffc107;
                font-size: 12px;
                margin-top: 5px;
                display: flex;
                align-items: center;
                gap: 5px;
            }
            
            .fadeInUp {
                animation-name: fadeInUp;
            }
            
            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translate3d(0, 20px, 0);
                }
                to {
                    opacity: 1;
                    transform: translate3d(0, 0, 0);
                }
            }
        `)
        .appendTo('head');
});

// Check for caps lock on password field
$(document).on('keydown', '#input-password', function(e) {
    LoginPage.checkCapsLock(e);
});
