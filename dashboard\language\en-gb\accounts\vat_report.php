<?php
$_['heading_title']      = 'VAT Report';
$_['text_form']          = 'Filter VAT Report';
$_['entry_date_start']   = 'Start Date';
$_['entry_date_end']     = 'End Date';
$_['button_filter']      = 'Filter';

$_['print_title']        = 'Print VAT Report';
$_['text_vat_report']    = 'VAT Report';
$_['text_period']        = 'Period';
$_['text_from']          = 'From';
$_['text_to']            = 'To';
$_['text_vat_sales']     = 'VAT on Sales';
$_['text_vat_purchases'] = 'VAT on Purchases';
$_['text_net_vat']       = 'Net VAT';
$_['error_no_data']      = 'No data for selected period!';

// Additional Text
$_['text_success_generate']            = 'Report generated successfully!';
$_['text_no_results']                  = 'No results found';
$_['text_home']                        = 'Home';

// VAT Sections
$_['text_total_vat_sales']             = 'Total VAT on Sales';
$_['text_total_vat_purchases']         = 'Total VAT on Purchases';
$_['text_vat_payable']                 = 'VAT Payable';
$_['text_vat_refundable']              = 'VAT Refundable';

// VAT Rates
$_['text_vat_rate_14']                 = '14% - Standard Rate';
$_['text_vat_rate_10']                 = '10% - Reduced Rate';
$_['text_vat_rate_5']                  = '5% - Special Reduced Rate';
$_['text_vat_rate_0']                  = '0% - Tax Exempt';

// Generate Options
$_['button_generate']                  = 'Generate Report';
$_['button_generate_and_new']          = 'Generate and New';
$_['button_generate_and_export']       = 'Generate and Export';

// Export Options
$_['text_export_excel']                = 'Export Excel';
$_['text_export_pdf']                  = 'Export PDF';
$_['text_export_csv']                  = 'Export CSV';
$_['text_export_eta']                  = 'Export ETA';

// ETA Integration
$_['text_eta_submission']              = 'Submit to ETA';
$_['text_eta_status']                  = 'ETA Status';
$_['text_eta_submitted']               = 'Submitted';
$_['text_eta_pending']                 = 'Pending';
$_['text_eta_error']                   = 'Submission Error';

// Errors
$_['error_date_start_required']        = 'Start date is required!';
$_['error_date_end_required']          = 'End date is required!';
$_['error_date_range']                 = 'Start date must be before end date!';
$_['error_no_data_export']             = 'No data to export! Please generate report first.';
$_['error_eta_connection']             = 'Error connecting to ETA system!';

// Success Messages
$_['text_success_export']              = 'Report exported successfully!';
$_['text_success_eta_submit']          = 'Report submitted to ETA successfully!';

// Advanced Analysis
$_['text_advanced_analysis']           = 'Advanced Analysis';
$_['text_vat_breakdown_by_rates']      = 'VAT Breakdown by Rates';
$_['text_eta_compliance_status']       = 'ETA Compliance Status';
$_['text_vat_reconciliation']          = 'VAT Reconciliation';
$_['text_comparative_analysis']        = 'Comparative Analysis';
$_['text_recommendations']             = 'Recommendations';

// ETA Compliance
$_['text_total_invoices']              = 'Total Invoices';
$_['text_submitted_invoices']          = 'Submitted Invoices';
$_['text_approved_invoices']           = 'Approved Invoices';
$_['text_compliance_rate']             = 'Compliance Rate';
$_['text_eta_compliant']               = 'ETA Compliant';
$_['text_egyptian_tax_authority']      = 'Egyptian Tax Authority';

// VAT Return Form
$_['text_vat_return']                  = 'VAT Return';
$_['text_vat_return_form']             = 'VAT Return Form';
$_['entry_tax_period']                 = 'Tax Period';
$_['text_select_period']               = 'Select Period';
$_['text_monthly']                     = 'Monthly';
$_['text_quarterly']                   = 'Quarterly';
$_['text_annual']                      = 'Annual';
$_['entry_year']                       = 'Year';
$_['text_select_month']                = 'Select Month';

// Company Information
$_['text_company_information']         = 'Company Information';
$_['entry_company_name']               = 'Company Name';
$_['entry_tax_number']                 = 'Tax Number';
$_['entry_commercial_register']        = 'Commercial Register';

// VAT Calculations
$_['text_vat_calculations']            = 'VAT Calculations';
$_['text_sales_vat']                   = 'Sales VAT';
$_['text_purchases_vat']               = 'Purchases VAT';
$_['column_vat_rate']                  = 'VAT Rate';
$_['column_taxable_amount']            = 'Taxable Amount';
$_['column_vat_amount']                = 'VAT Amount';
$_['text_standard_rate']               = 'Standard Rate';
$_['text_reduced_rate']                = 'Reduced Rate';
$_['text_special_rate']                = 'Special Rate';
$_['text_exempt']                      = 'Exempt';

// Net VAT
$_['text_net_vat_calculation']         = 'Net VAT Calculation';
$_['text_total_sales_vat']             = 'Total Sales VAT';
$_['text_total_purchases_vat']         = 'Total Purchases VAT';
$_['text_net_vat_payable']             = 'Net VAT Payable';

// Additional Information
$_['text_additional_information']      = 'Additional Information';
$_['entry_previous_period_credit']     = 'Previous Period Credit';
$_['entry_adjustments']                = 'Adjustments';
$_['entry_payment_method']             = 'Payment Method';
$_['text_select_payment_method']       = 'Select Payment Method';
$_['text_bank_transfer']               = 'Bank Transfer';
$_['text_check']                       = 'Check';
$_['text_cash']                        = 'Cash';
$_['text_offset_credit']               = 'Offset Credit';

// ETA Submission
$_['text_eta_submission']              = 'ETA Submission';
$_['entry_submission_method']          = 'Submission Method';
$_['text_electronic_submission']       = 'Electronic Submission';
$_['text_manual_submission']           = 'Manual Submission';
$_['entry_due_date']                   = 'Due Date';

// Declaration
$_['text_declaration']                 = 'Declaration';
$_['text_declaration_accuracy']        = 'I declare that the information provided is accurate and complete';
$_['text_declaration_completeness']    = 'I confirm that all VAT transactions are included';
$_['text_declaration_compliance']      = 'I certify compliance with Egyptian VAT regulations';

// Print Report
$_['text_vat_summary']                 = 'VAT Summary';
$_['text_detailed_vat_report']         = 'Detailed VAT Report';
$_['column_description']               = 'Description';
$_['column_sales_vat']                 = 'Sales VAT';
$_['column_purchases_vat']             = 'Purchases VAT';
$_['column_net_vat']                   = 'Net VAT';
$_['column_transactions']              = 'Transactions';
$_['column_source']                    = 'Source';
$_['column_status']                    = 'Status';
$_['column_current_period']            = 'Current Period';
$_['column_previous_period']           = 'Previous Period';
$_['column_change']                    = 'Change';
$_['text_journal_entries_vat']         = 'Journal Entries VAT';
$_['text_electronic_invoices_vat']     = 'Electronic Invoices VAT';
$_['text_manual_adjustments']          = 'Manual Adjustments';
$_['text_variance']                    = 'Variance';
$_['text_reconciled']                  = 'Reconciled';
$_['text_needs_review']                = 'Needs Review';
$_['text_vat_report_control']          = 'VAT Report Control';
$_['text_success_analysis']            = 'Advanced VAT analysis generated successfully!';
$_['text_vat_return_submitted']        = 'VAT return submitted successfully!';
$_['error_no_analysis_data']           = 'No analysis data available!';

// Controller language variables - Direct Arabic texts
$_['log_unauthorized_access'] = 'Unauthorized access attempt to VAT report';
$_['log_view_screen'] = 'View VAT report screen';
$_['log_unauthorized_generate'] = 'Unauthorized VAT report generation attempt';
$_['log_generate_report'] = 'Generate VAT report for period';
$_['notification_title'] = 'VAT Report Generation';
$_['notification_message'] = 'VAT report generated for period';
$_['log_export'] = 'Export VAT report in format';
$_['vat_rate_14'] = '14% - Standard Rate';
$_['vat_rate_10'] = '10% - Reduced Rate';
$_['vat_rate_5'] = '5% - Special Reduced Rate';
$_['vat_rate_0'] = '0% - Exempt';
$_['log_unauthorized_advanced'] = 'Unauthorized access attempt to advanced VAT analysis';
$_['log_generate_advanced'] = 'Generate advanced VAT analysis for period';
$_['notification_advanced_title'] = 'Advanced VAT Analysis';
$_['notification_advanced_message'] = 'Advanced VAT analysis generated for period';
$_['log_view_advanced'] = 'View advanced VAT analysis';
$_['log_submit_return'] = 'Submit VAT return';

// Additional template variables
$_['text_actions'] = 'Actions';
$_['text_vat_report_filters'] = 'VAT Report Filters';
$_['entry_date_start'] = 'Start Date';
$_['entry_date_end'] = 'End Date';
$_['entry_vat_rate'] = 'VAT Rate';
$_['text_all_rates'] = 'All Rates';
$_['button_generate'] = 'Generate';
$_['button_generate_report'] = 'Generate Report';
$_['text_export_options'] = 'Export Options';
$_['text_export'] = 'Export';
$_['text_print'] = 'Print';
$_['button_submit_eta'] = 'Submit to ETA';
$_['text_submit_eta'] = 'Submit to ETA';
$_['button_advanced_analysis'] = 'Advanced Analysis';
$_['text_input_vat'] = 'Input VAT';
$_['text_input_vat_description'] = 'Total VAT on purchases';
$_['text_output_vat'] = 'Output VAT';
$_['text_output_vat_description'] = 'Total VAT on sales';
$_['text_net_vat'] = 'Net VAT';
$_['text_net_vat_description'] = 'Difference between output and input';
$_['text_vat_payable'] = 'VAT Payable';
$_['text_vat_payable_description'] = 'Amount due to tax authority';
$_['text_vat_refund'] = 'VAT Refund';
$_['text_vat_refund_description'] = 'Amount due from tax authority';
$_['text_vat_report_details'] = 'VAT Report Details';
$_['text_period'] = 'Period';
$_['text_to'] = 'to';
$_['text_input_vat_details'] = 'Input VAT Details';
$_['text_output_vat_details'] = 'Output VAT Details';
$_['column_transaction_date'] = 'Transaction Date';
$_['column_supplier_name'] = 'Supplier Name';
$_['column_customer_name'] = 'Customer Name';
$_['column_invoice_number'] = 'Invoice Number';
$_['column_net_amount'] = 'Net Amount';
$_['column_total_amount'] = 'Total Amount';
$_['text_total_input_vat'] = 'Total Input VAT';
$_['text_total_output_vat'] = 'Total Output VAT';
$_['text_vat_calculation_summary'] = 'VAT Calculation Summary';
$_['text_net_vat_position'] = 'Net VAT Position';
$_['text_previous_period_balance'] = 'Previous Period Balance';
$_['text_amount_payable'] = 'Amount Payable';
$_['text_amount_refundable'] = 'Amount Refundable';
$_['text_vat_rate_analysis'] = 'VAT Rate Analysis';
$_['column_input_net'] = 'Input Net';
$_['column_input_vat'] = 'Input VAT';
$_['column_output_net'] = 'Output Net';
$_['column_output_vat'] = 'Output VAT';
$_['column_net_position'] = 'Net Position';
$_['text_no_vat_report'] = 'No VAT Report';
$_['error_date_start'] = 'Start date is required';
$_['error_date_end'] = 'End date is required';
$_['error_invalid_date_range'] = 'Invalid date range';
$_['error_complete_form'] = 'Please complete all fields';
$_['text_exporting'] = 'Exporting';
$_['confirm_submit_eta'] = 'Are you sure you want to submit the report to ETA?';
$_['text_submitting_eta'] = 'Submitting to ETA';
$_['success_eta_submission'] = 'Report submitted to ETA successfully';
$_['error_eta_submission'] = 'Failed to submit report to ETA';
$_['text_generating_analysis'] = 'Generating analysis';
$_['success_analysis_generated'] = 'Analysis generated successfully';
$_['error_analysis_generation'] = 'Failed to generate analysis';
$_['text_eta_connected'] = 'Connected to ETA';
$_['text_eta_disconnected'] = 'Disconnected from ETA';

// Enhanced performance and analytics variables
$_['text_optimized_vat']               = 'Optimized VAT Report';
$_['text_vat_analysis']                = 'VAT Analysis';
$_['text_cache_enabled']               = 'Cache Enabled';
$_['text_enhanced_analysis']           = 'Enhanced Analysis';
$_['text_monthly_trends']              = 'Monthly Trends';
$_['text_vat_rate']                    = 'VAT Rate';
$_['text_estimated_sales_base']        = 'Estimated Sales Base';
$_['text_estimated_purchases_base']    = 'Estimated Purchases Base';
$_['text_eta_submission']              = 'ETA Submission';
$_['text_submission_reference']        = 'Submission Reference';
$_['text_submission_status']           = 'Submission Status';
$_['text_vat_period']                  = 'VAT Period';
$_['text_net_vat_payable']             = 'Net VAT Payable';
$_['button_vat_analysis']              = 'VAT Analysis';
$_['text_loading_analysis']            = 'Loading VAT analysis...';
$_['text_analysis_ready']              = 'Analysis ready';
?>
