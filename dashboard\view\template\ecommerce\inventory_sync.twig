{{ header }}
{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="button" data-bs-toggle="tooltip" title="{{ button_filter }}" onclick="$('#filter-sync').toggleClass('d-none');" class="btn btn-light d-md-none d-lg-none"><i class="fas fa-filter"></i></button>
        <button type="button" data-bs-toggle="tooltip" title="{{ button_sync_all }}" onclick="syncAllProducts()" class="btn btn-primary"><i class="fas fa-sync"></i></button>
        <button type="button" data-bs-toggle="tooltip" title="{{ button_resolve_conflicts }}" onclick="resolveAllConflicts()" class="btn btn-warning"><i class="fas fa-exclamation-triangle"></i></button>
        <a href="{{ settings }}" data-bs-toggle="tooltip" title="{{ button_settings }}" class="btn btn-info"><i class="fas fa-cog"></i></a>
        <a href="{{ export }}" data-bs-toggle="tooltip" title="{{ button_export }}" class="btn btn-success"><i class="fas fa-file-excel"></i></a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ol class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
          <li class="breadcrumb-item"><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    
    <!-- إحصائيات المزامنة -->
    <div class="row mb-3">
      <div class="col-lg-3 col-md-6">
        <div class="card bg-primary text-white">
          <div class="card-body">
            <div class="d-flex justify-content-between">
              <div>
                <h4>{{ summary.total_items|number_format }}</h4>
                <p class="mb-0">{{ text_total_items }}</p>
              </div>
              <div class="align-self-center">
                <i class="fas fa-database fa-2x"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-md-6">
        <div class="card bg-success text-white">
          <div class="card-body">
            <div class="d-flex justify-content-between">
              <div>
                <h4>{{ summary.synced_items|number_format }}</h4>
                <p class="mb-0">{{ text_synced_items }}</p>
              </div>
              <div class="align-self-center">
                <i class="fas fa-check-circle fa-2x"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-md-6">
        <div class="card bg-warning text-white">
          <div class="card-body">
            <div class="d-flex justify-content-between">
              <div>
                <h4>{{ summary.pending_items|number_format }}</h4>
                <p class="mb-0">{{ text_pending_items }}</p>
              </div>
              <div class="align-self-center">
                <i class="fas fa-clock fa-2x"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-md-6">
        <div class="card bg-danger text-white">
          <div class="card-body">
            <div class="d-flex justify-content-between">
              <div>
                <h4>{{ summary.conflict_items|number_format }}</h4>
                <p class="mb-0">{{ text_conflict_items }}</p>
              </div>
              <div class="align-self-center">
                <i class="fas fa-exclamation-triangle fa-2x"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- معدل نجاح المزامنة -->
    <div class="row mb-3">
      <div class="col-12">
        <div class="card">
          <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
              <div>
                <h5 class="mb-1">{{ text_success_rate }}</h5>
                <p class="text-muted mb-0">{{ text_last_sync }}: {{ summary.last_sync ? summary.last_sync|date('Y-m-d H:i:s') : text_never }}</p>
              </div>
              <div class="text-end">
                <h3 class="mb-0 {% if summary.success_rate >= 90 %}text-success{% elseif summary.success_rate >= 70 %}text-warning{% else %}text-danger{% endif %}">
                  {{ summary.success_rate }}%
                </h3>
              </div>
            </div>
            <div class="progress mt-2" style="height: 10px;">
              <div class="progress-bar {% if summary.success_rate >= 90 %}bg-success{% elseif summary.success_rate >= 70 %}bg-warning{% else %}bg-danger{% endif %}" 
                   style="width: {{ summary.success_rate }}%"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="row">
      <!-- فلاتر المزامنة -->
      <div id="filter-sync" class="col-lg-3 col-md-12 order-lg-last d-none d-lg-block mb-3">
        <div class="card">
          <div class="card-header"><i class="fas fa-filter"></i> {{ text_filter }}</div>
          <div class="card-body">
            <div class="mb-3">
              <label for="input-product-name" class="form-label">{{ entry_product_name }}</label>
              <input type="text" name="filter_product_name" value="{{ filter_product_name }}" placeholder="{{ entry_product_name }}" id="input-product-name" class="form-control"/>
            </div>
            <div class="mb-3">
              <label for="input-sku" class="form-label">{{ entry_sku }}</label>
              <input type="text" name="filter_sku" value="{{ filter_sku }}" placeholder="{{ entry_sku }}" id="input-sku" class="form-control"/>
            </div>
            <div class="mb-3">
              <label for="input-warehouse" class="form-label">{{ entry_warehouse }}</label>
              <select name="filter_warehouse_id" id="input-warehouse" class="form-select">
                <option value="">{{ text_all_warehouses }}</option>
                {% for warehouse in warehouses %}
                  <option value="{{ warehouse.warehouse_id }}" {% if warehouse.warehouse_id == filter_warehouse_id %}selected="selected"{% endif %}>{{ warehouse.name }}</option>
                {% endfor %}
              </select>
            </div>
            <div class="mb-3">
              <label for="input-sync-status" class="form-label">{{ entry_sync_status }}</label>
              <select name="filter_sync_status" id="input-sync-status" class="form-select">
                <option value="">{{ text_all_statuses }}</option>
                {% for key, value in sync_statuses %}
                  <option value="{{ key }}" {% if key == filter_sync_status %}selected="selected"{% endif %}>{{ value }}</option>
                {% endfor %}
              </select>
            </div>
            <div class="mb-3">
              <label for="input-conflict-status" class="form-label">{{ entry_conflict_status }}</label>
              <select name="filter_conflict_status" id="input-conflict-status" class="form-select">
                <option value="">{{ text_all_conflicts }}</option>
                {% for key, value in conflict_statuses %}
                  <option value="{{ key }}" {% if key == filter_conflict_status %}selected="selected"{% endif %}>{{ value }}</option>
                {% endfor %}
              </select>
            </div>
            <div class="mb-3">
              <label for="input-date-start" class="form-label">{{ entry_date_start }}</label>
              <input type="date" name="filter_date_start" value="{{ filter_date_start }}" id="input-date-start" class="form-control"/>
            </div>
            <div class="mb-3">
              <label for="input-date-end" class="form-label">{{ entry_date_end }}</label>
              <input type="date" name="filter_date_end" value="{{ filter_date_end }}" id="input-date-end" class="form-control"/>
            </div>
            <div class="text-end">
              <button type="button" id="button-filter" class="btn btn-light"><i class="fas fa-filter"></i> {{ button_filter }}</button>
            </div>
          </div>
        </div>
      </div>
      
      <!-- قائمة المزامنة -->
      <div class="col-lg-9 col-md-12">
        <div class="card">
          <div class="card-header">
            <i class="fas fa-sync"></i> {{ text_list }}
            <div class="card-tools">
              <button type="button" class="btn btn-sm btn-outline-secondary" onclick="location.reload();">
                <i class="fas fa-sync"></i> {{ button_refresh }}
              </button>
            </div>
          </div>
          <div class="card-body">
            {% if sync_items %}
            <div class="table-responsive">
              <table class="table table-bordered table-hover">
                <thead>
                  <tr>
                    <td><a href="{{ sort_product }}" {% if sort == 'product_name' %}class="{% if order == 'ASC' %}asc{% else %}desc{% endif %}"{% endif %}>{{ column_product }}</a></td>
                    <td><a href="{{ sort_sku }}" {% if sort == 'sku' %}class="{% if order == 'ASC' %}asc{% else %}desc{% endif %}"{% endif %}>{{ column_sku }}</a></td>
                    <td><a href="{{ sort_warehouse }}" {% if sort == 'warehouse_name' %}class="{% if order == 'ASC' %}asc{% else %}desc{% endif %}"{% endif %}>{{ column_warehouse }}</a></td>
                    <td class="text-center">{{ column_erp_quantity }}</td>
                    <td class="text-center">{{ column_ecommerce_quantity }}</td>
                    <td class="text-center">{{ column_quantity_diff }}</td>
                    <td class="text-center"><a href="{{ sort_sync_status }}" {% if sort == 'sync_status' %}class="{% if order == 'ASC' %}asc{% else %}desc{% endif %}"{% endif %}>{{ column_sync_status }}</a></td>
                    <td class="text-center">{{ column_conflict_status }}</td>
                    <td class="text-center"><a href="{{ sort_last_sync }}" {% if sort == 'last_sync' %}class="{% if order == 'ASC' %}asc{% else %}desc{% endif %}"{% endif %}>{{ column_last_sync }}</a></td>
                    <td class="text-center">{{ column_action }}</td>
                  </tr>
                </thead>
                <tbody>
                  {% for item in sync_items %}
                  <tr id="sync-row-{{ item.sync_id }}">
                    <td>{{ item.product_name }}</td>
                    <td>{{ item.sku }}</td>
                    <td>{{ item.warehouse_name }}</td>
                    <td class="text-center">
                      <span class="badge bg-info">{{ item.erp_quantity }}</span>
                    </td>
                    <td class="text-center">
                      <span class="badge bg-secondary">{{ item.ecommerce_quantity }}</span>
                    </td>
                    <td class="text-center">
                      {% if item.quantity_diff > 0 %}
                        <span class="text-success">+{{ item.quantity_diff }}</span>
                      {% elseif item.quantity_diff < 0 %}
                        <span class="text-danger">{{ item.quantity_diff }}</span>
                      {% else %}
                        <span class="text-muted">0</span>
                      {% endif %}
                    </td>
                    <td class="text-center">
                      <span class="badge bg-{{ item.sync_status_class }}">{{ item.sync_status_text }}</span>
                    </td>
                    <td class="text-center">
                      <span class="badge bg-{{ item.conflict_class }}">{{ item.conflict_text }}</span>
                    </td>
                    <td class="text-center">{{ item.last_sync }}</td>
                    <td class="text-center">
                      <div class="btn-group btn-group-sm">
                        {% for action in item.actions %}
                          {% if action.href starts with 'javascript:' %}
                            <button type="button" class="{{ action.class }}" onclick="{{ action.href|replace({'javascript:': ''}) }}" data-bs-toggle="tooltip" title="{{ action.text }}">
                              <i class="fas fa-{% if 'sync' in action.text %}sync{% elseif 'resolve' in action.text %}exclamation-triangle{% else %}eye{% endif %}"></i>
                            </button>
                          {% else %}
                            <a href="{{ action.href }}" class="{{ action.class }}" data-bs-toggle="tooltip" title="{{ action.text }}">
                              <i class="fas fa-eye"></i>
                            </a>
                          {% endif %}
                        {% endfor %}
                      </div>
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
            <div class="row">
              <div class="col-sm-6 text-start">{{ pagination }}</div>
              <div class="col-sm-6 text-end">{{ results }}</div>
            </div>
            {% else %}
            <div class="text-center">
              <i class="fas fa-sync fa-3x text-muted mb-3"></i>
              <h4>{{ text_no_results }}</h4>
              <p class="text-muted">{{ text_no_sync_items_message }}</p>
              <button type="button" class="btn btn-primary" onclick="syncAllProducts()">
                <i class="fas fa-sync"></i> {{ button_sync_all_now }}
              </button>
            </div>
            {% endif %}
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Modal لحل التعارض -->
<div class="modal fade" id="modal-resolve-conflict" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">{{ text_resolve_conflict }}</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <form id="form-resolve-conflict">
          <input type="hidden" name="sync_id" id="resolve-sync-id">
          <div class="mb-3">
            <label class="form-label">{{ text_resolution_strategy }}</label>
            <div class="form-check">
              <input type="radio" name="strategy" value="erp_wins" id="strategy-erp" class="form-check-input" checked>
              <label for="strategy-erp" class="form-check-label">{{ text_erp_wins }}</label>
            </div>
            <div class="form-check">
              <input type="radio" name="strategy" value="ecommerce_wins" id="strategy-ecommerce" class="form-check-input">
              <label for="strategy-ecommerce" class="form-check-label">{{ text_ecommerce_wins }}</label>
            </div>
            <div class="form-check">
              <input type="radio" name="strategy" value="latest_wins" id="strategy-latest" class="form-check-input">
              <label for="strategy-latest" class="form-check-label">{{ text_latest_wins }}</label>
            </div>
          </div>
          <div class="mb-3">
            <label for="resolve-notes" class="form-label">{{ entry_resolution_notes }}</label>
            <textarea name="notes" id="resolve-notes" class="form-control" rows="3" placeholder="{{ placeholder_resolution_notes }}"></textarea>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ button_cancel }}</button>
        <button type="button" class="btn btn-warning" onclick="confirmResolveConflict()">{{ button_resolve }}</button>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript">
// تطبيق الفلاتر
$('#button-filter').on('click', function() {
    var url = 'index.php?route=ecommerce/inventory_sync&user_token={{ user_token }}';
    
    var filter_product_name = $('input[name=\'filter_product_name\']').val();
    if (filter_product_name) {
        url += '&filter_product_name=' + encodeURIComponent(filter_product_name);
    }
    
    var filter_sku = $('input[name=\'filter_sku\']').val();
    if (filter_sku) {
        url += '&filter_sku=' + encodeURIComponent(filter_sku);
    }
    
    var filter_warehouse_id = $('select[name=\'filter_warehouse_id\']').val();
    if (filter_warehouse_id) {
        url += '&filter_warehouse_id=' + filter_warehouse_id;
    }
    
    var filter_sync_status = $('select[name=\'filter_sync_status\']').val();
    if (filter_sync_status) {
        url += '&filter_sync_status=' + filter_sync_status;
    }
    
    var filter_conflict_status = $('select[name=\'filter_conflict_status\']').val();
    if (filter_conflict_status !== '') {
        url += '&filter_conflict_status=' + filter_conflict_status;
    }
    
    var filter_date_start = $('input[name=\'filter_date_start\']').val();
    if (filter_date_start) {
        url += '&filter_date_start=' + filter_date_start;
    }
    
    var filter_date_end = $('input[name=\'filter_date_end\']').val();
    if (filter_date_end) {
        url += '&filter_date_end=' + filter_date_end;
    }
    
    location = url;
});

// مزامنة جميع المنتجات
function syncAllProducts() {
    if (confirm('{{ text_confirm_sync_all }}')) {
        $.ajax({
            url: '{{ sync_all }}',
            type: 'GET',
            dataType: 'json',
            beforeSend: function() {
                $('button[onclick="syncAllProducts()"]').html('<i class="fas fa-spinner fa-spin"></i> {{ text_syncing }}');
            },
            success: function(json) {
                if (json.success) {
                    alert(json.success);
                    location.reload();
                } else {
                    alert(json.error);
                }
            },
            complete: function() {
                $('button[onclick="syncAllProducts()"]').html('<i class="fas fa-sync"></i> {{ button_sync_all }}');
            }
        });
    }
}

// مزامنة منتج واحد
function syncItem(syncId) {
    $.ajax({
        url: 'index.php?route=ecommerce/inventory_sync/syncItem&user_token={{ user_token }}',
        type: 'POST',
        data: {sync_id: syncId},
        dataType: 'json',
        success: function(json) {
            if (json.success) {
                location.reload();
            } else {
                alert(json.error);
            }
        }
    });
}

// حل التعارض
function resolveConflict(syncId) {
    $('#resolve-sync-id').val(syncId);
    $('#modal-resolve-conflict').modal('show');
}

function confirmResolveConflict() {
    $.ajax({
        url: 'index.php?route=ecommerce/inventory_sync/resolveConflict&user_token={{ user_token }}',
        type: 'POST',
        data: $('#form-resolve-conflict').serialize(),
        dataType: 'json',
        success: function(json) {
            if (json.success) {
                $('#modal-resolve-conflict').modal('hide');
                location.reload();
            } else {
                alert(json.error);
            }
        }
    });
}

// حل جميع التعارضات
function resolveAllConflicts() {
    if (confirm('{{ text_confirm_resolve_all }}')) {
        $.ajax({
            url: '{{ resolve_conflicts }}',
            type: 'POST',
            data: {strategy: 'erp_wins'},
            dataType: 'json',
            beforeSend: function() {
                $('button[onclick="resolveAllConflicts()"]').html('<i class="fas fa-spinner fa-spin"></i> {{ text_resolving }}');
            },
            success: function(json) {
                if (json.success) {
                    alert(json.success);
                    location.reload();
                } else {
                    alert(json.error);
                }
            },
            complete: function() {
                $('button[onclick="resolveAllConflicts()"]').html('<i class="fas fa-exclamation-triangle"></i> {{ button_resolve_conflicts }}');
            }
        });
    }
}
</script>

{{ footer }}
