{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="submit" form="logging\user_activity-form" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fa fa-save"></i></button>
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="close" data-dismiss="alert">&times;</button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="close" data-dismiss="alert">&times;</button>
      </div>
    {{% endif %}}

    <div class="panel panel-default">
      <div class="panel-heading"><i class="fa fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}bar-chart{% elif screen_type == 'settings' %}cog{% else %}pencil{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="panel-body">
        <form id="logging\user_activity-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-activities">{{ text_activities }}</label>
            <div class="col-sm-10">
              <input type="text" name="activities" value="{{ activities }}" placeholder="{{ text_activities }}" id="input-activities" class="form-control" />
              {% if error_activities %}
                <div class="text-danger">{{ error_activities }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-activity_stats">{{ text_activity_stats }}</label>
            <div class="col-sm-10">
              <input type="text" name="activity_stats" value="{{ activity_stats }}" placeholder="{{ text_activity_stats }}" id="input-activity_stats" class="form-control" />
              {% if error_activity_stats %}
                <div class="text-danger">{{ error_activity_stats }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-activity_types">{{ text_activity_types }}</label>
            <div class="col-sm-10">
              <input type="text" name="activity_types" value="{{ activity_types }}" placeholder="{{ text_activity_types }}" id="input-activity_types" class="form-control" />
              {% if error_activity_types %}
                <div class="text-danger">{{ error_activity_types }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-back">{{ text_back }}</label>
            <div class="col-sm-10">
              <input type="text" name="back" value="{{ back }}" placeholder="{{ text_back }}" id="input-back" class="form-control" />
              {% if error_back %}
                <div class="text-danger">{{ error_back }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-export">{{ text_export }}</label>
            <div class="col-sm-10">
              <input type="text" name="export" value="{{ export }}" placeholder="{{ text_export }}" id="input-export" class="form-control" />
              {% if error_export %}
                <div class="text-danger">{{ error_export }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-export_user">{{ text_export_user }}</label>
            <div class="col-sm-10">
              <input type="text" name="export_user" value="{{ export_user }}" placeholder="{{ text_export_user }}" id="input-export_user" class="form-control" />
              {% if error_export_user %}
                <div class="text-danger">{{ error_export_user }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-get_latest">{{ text_get_latest }}</label>
            <div class="col-sm-10">
              <input type="text" name="get_latest" value="{{ get_latest }}" placeholder="{{ text_get_latest }}" id="input-get_latest" class="form-control" />
              {% if error_get_latest %}
                <div class="text-danger">{{ error_get_latest }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="text-danger">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-modules">{{ text_modules }}</label>
            <div class="col-sm-10">
              <input type="text" name="modules" value="{{ modules }}" placeholder="{{ text_modules }}" id="input-modules" class="form-control" />
              {% if error_modules %}
                <div class="text-danger">{{ error_modules }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-online_users">{{ text_online_users }}</label>
            <div class="col-sm-10">
              <input type="text" name="online_users" value="{{ online_users }}" placeholder="{{ text_online_users }}" id="input-online_users" class="form-control" />
              {% if error_online_users %}
                <div class="text-danger">{{ error_online_users }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-real_time">{{ text_real_time }}</label>
            <div class="col-sm-10">
              <input type="text" name="real_time" value="{{ real_time }}" placeholder="{{ text_real_time }}" id="input-real_time" class="form-control" />
              {% if error_real_time %}
                <div class="text-danger">{{ error_real_time }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-realtime_config">{{ text_realtime_config }}</label>
            <div class="col-sm-10">
              <input type="text" name="realtime_config" value="{{ realtime_config }}" placeholder="{{ text_realtime_config }}" id="input-realtime_config" class="form-control" />
              {% if error_realtime_config %}
                <div class="text-danger">{{ error_realtime_config }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-reports">{{ text_reports }}</label>
            <div class="col-sm-10">
              <input type="text" name="reports" value="{{ reports }}" placeholder="{{ text_reports }}" id="input-reports" class="form-control" />
              {% if error_reports %}
                <div class="text-danger">{{ error_reports }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-security_analysis">{{ text_security_analysis }}</label>
            <div class="col-sm-10">
              <input type="text" name="security_analysis" value="{{ security_analysis }}" placeholder="{{ text_security_analysis }}" id="input-security_analysis" class="form-control" />
              {% if error_security_analysis %}
                <div class="text-danger">{{ error_security_analysis }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-settings">{{ text_settings }}</label>
            <div class="col-sm-10">
              <input type="text" name="settings" value="{{ settings }}" placeholder="{{ text_settings }}" id="input-settings" class="form-control" />
              {% if error_settings %}
                <div class="text-danger">{{ error_settings }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-specialized_activities">{{ text_specialized_activities }}</label>
            <div class="col-sm-10">
              <input type="text" name="specialized_activities" value="{{ specialized_activities }}" placeholder="{{ text_specialized_activities }}" id="input-specialized_activities" class="form-control" />
              {% if error_specialized_activities %}
                <div class="text-danger">{{ error_specialized_activities }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-specialized_user_activity">{{ text_specialized_user_activity }}</label>
            <div class="col-sm-10">
              <input type="text" name="specialized_user_activity" value="{{ specialized_user_activity }}" placeholder="{{ text_specialized_user_activity }}" id="input-specialized_user_activity" class="form-control" />
              {% if error_specialized_user_activity %}
                <div class="text-danger">{{ error_specialized_user_activity }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-total">{{ text_total }}</label>
            <div class="col-sm-10">
              <input type="text" name="total" value="{{ total }}" placeholder="{{ text_total }}" id="input-total" class="form-control" />
              {% if error_total %}
                <div class="text-danger">{{ error_total }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-usage_patterns">{{ text_usage_patterns }}</label>
            <div class="col-sm-10">
              <input type="text" name="usage_patterns" value="{{ usage_patterns }}" placeholder="{{ text_usage_patterns }}" id="input-usage_patterns" class="form-control" />
              {% if error_usage_patterns %}
                <div class="text-danger">{{ error_usage_patterns }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-user_activities">{{ text_user_activities }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_activities" value="{{ user_activities }}" placeholder="{{ text_user_activities }}" id="input-user_activities" class="form-control" />
              {% if error_user_activities %}
                <div class="text-danger">{{ error_user_activities }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-user_info">{{ text_user_info }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_info" value="{{ user_info }}" placeholder="{{ text_user_info }}" id="input-user_info" class="form-control" />
              {% if error_user_info %}
                <div class="text-danger">{{ error_user_info }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-user_patterns">{{ text_user_patterns }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_patterns" value="{{ user_patterns }}" placeholder="{{ text_user_patterns }}" id="input-user_patterns" class="form-control" />
              {% if error_user_patterns %}
                <div class="text-danger">{{ error_user_patterns }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-user_stats">{{ text_user_stats }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_stats" value="{{ user_stats }}" placeholder="{{ text_user_stats }}" id="input-user_stats" class="form-control" />
              {% if error_user_stats %}
                <div class="text-danger">{{ error_user_stats }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="text-danger">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-users">{{ text_users }}</label>
            <div class="col-sm-10">
              <input type="text" name="users" value="{{ users }}" placeholder="{{ text_users }}" id="input-users" class="form-control" />
              {% if error_users %}
                <div class="text-danger">{{ error_users }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-websocket_config">{{ text_websocket_config }}</label>
            <div class="col-sm-10">
              <input type="text" name="websocket_config" value="{{ websocket_config }}" placeholder="{{ text_websocket_config }}" id="input-websocket_config" class="form-control" />
              {% if error_websocket_config %}
                <div class="text-danger">{{ error_websocket_config }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}