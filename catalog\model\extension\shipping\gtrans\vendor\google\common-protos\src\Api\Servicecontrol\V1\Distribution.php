<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/servicecontrol/v1/distribution.proto

namespace Google\Api\Servicecontrol\V1;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Distribution represents a frequency distribution of double-valued sample
 * points. It contains the size of the population of sample points plus
 * additional optional information:
 *   - the arithmetic mean of the samples
 *   - the minimum and maximum of the samples
 *   - the sum-squared-deviation of the samples, used to compute variance
 *   - a histogram of the values of the sample points
 *
 * Generated from protobuf message <code>google.api.servicecontrol.v1.Distribution</code>
 */
class Distribution extends \Google\Protobuf\Internal\Message
{
    /**
     * The total number of samples in the distribution. Must be >= 0.
     *
     * Generated from protobuf field <code>int64 count = 1;</code>
     */
    private $count = 0;
    /**
     * The arithmetic mean of the samples in the distribution. If `count` is
     * zero then this field must be zero.
     *
     * Generated from protobuf field <code>double mean = 2;</code>
     */
    private $mean = 0.0;
    /**
     * The minimum of the population of values. Ignored if `count` is zero.
     *
     * Generated from protobuf field <code>double minimum = 3;</code>
     */
    private $minimum = 0.0;
    /**
     * The maximum of the population of values. Ignored if `count` is zero.
     *
     * Generated from protobuf field <code>double maximum = 4;</code>
     */
    private $maximum = 0.0;
    /**
     * The sum of squared deviations from the mean:
     *   Sum[i=1..count]((x_i - mean)^2)
     * where each x_i is a sample values. If `count` is zero then this field
     * must be zero, otherwise validation of the request fails.
     *
     * Generated from protobuf field <code>double sum_of_squared_deviation = 5;</code>
     */
    private $sum_of_squared_deviation = 0.0;
    /**
     * The number of samples in each histogram bucket. `bucket_counts` are
     * optional. If present, they must sum to the `count` value.
     * The buckets are defined below in `bucket_option`. There are N buckets.
     * `bucket_counts[0]` is the number of samples in the underflow bucket.
     * `bucket_counts[1]` to `bucket_counts[N-1]` are the numbers of samples
     * in each of the finite buckets. And `bucket_counts[N] is the number
     * of samples in the overflow bucket. See the comments of `bucket_option`
     * below for more details.
     * Any suffix of trailing zeros may be omitted.
     *
     * Generated from protobuf field <code>repeated int64 bucket_counts = 6;</code>
     */
    private $bucket_counts;
    protected $bucket_option;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int|string $count
     *           The total number of samples in the distribution. Must be >= 0.
     *     @type float $mean
     *           The arithmetic mean of the samples in the distribution. If `count` is
     *           zero then this field must be zero.
     *     @type float $minimum
     *           The minimum of the population of values. Ignored if `count` is zero.
     *     @type float $maximum
     *           The maximum of the population of values. Ignored if `count` is zero.
     *     @type float $sum_of_squared_deviation
     *           The sum of squared deviations from the mean:
     *             Sum[i=1..count]((x_i - mean)^2)
     *           where each x_i is a sample values. If `count` is zero then this field
     *           must be zero, otherwise validation of the request fails.
     *     @type int[]|string[]|\Google\Protobuf\Internal\RepeatedField $bucket_counts
     *           The number of samples in each histogram bucket. `bucket_counts` are
     *           optional. If present, they must sum to the `count` value.
     *           The buckets are defined below in `bucket_option`. There are N buckets.
     *           `bucket_counts[0]` is the number of samples in the underflow bucket.
     *           `bucket_counts[1]` to `bucket_counts[N-1]` are the numbers of samples
     *           in each of the finite buckets. And `bucket_counts[N] is the number
     *           of samples in the overflow bucket. See the comments of `bucket_option`
     *           below for more details.
     *           Any suffix of trailing zeros may be omitted.
     *     @type \Google\Api\Servicecontrol\V1\Distribution\LinearBuckets $linear_buckets
     *           Buckets with constant width.
     *     @type \Google\Api\Servicecontrol\V1\Distribution\ExponentialBuckets $exponential_buckets
     *           Buckets with exponentially growing width.
     *     @type \Google\Api\Servicecontrol\V1\Distribution\ExplicitBuckets $explicit_buckets
     *           Buckets with arbitrary user-provided width.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Api\Servicecontrol\V1\Distribution::initOnce();
        parent::__construct($data);
    }

    /**
     * The total number of samples in the distribution. Must be >= 0.
     *
     * Generated from protobuf field <code>int64 count = 1;</code>
     * @return int|string
     */
    public function getCount()
    {
        return $this->count;
    }

    /**
     * The total number of samples in the distribution. Must be >= 0.
     *
     * Generated from protobuf field <code>int64 count = 1;</code>
     * @param int|string $var
     * @return $this
     */
    public function setCount($var)
    {
        GPBUtil::checkInt64($var);
        $this->count = $var;

        return $this;
    }

    /**
     * The arithmetic mean of the samples in the distribution. If `count` is
     * zero then this field must be zero.
     *
     * Generated from protobuf field <code>double mean = 2;</code>
     * @return float
     */
    public function getMean()
    {
        return $this->mean;
    }

    /**
     * The arithmetic mean of the samples in the distribution. If `count` is
     * zero then this field must be zero.
     *
     * Generated from protobuf field <code>double mean = 2;</code>
     * @param float $var
     * @return $this
     */
    public function setMean($var)
    {
        GPBUtil::checkDouble($var);
        $this->mean = $var;

        return $this;
    }

    /**
     * The minimum of the population of values. Ignored if `count` is zero.
     *
     * Generated from protobuf field <code>double minimum = 3;</code>
     * @return float
     */
    public function getMinimum()
    {
        return $this->minimum;
    }

    /**
     * The minimum of the population of values. Ignored if `count` is zero.
     *
     * Generated from protobuf field <code>double minimum = 3;</code>
     * @param float $var
     * @return $this
     */
    public function setMinimum($var)
    {
        GPBUtil::checkDouble($var);
        $this->minimum = $var;

        return $this;
    }

    /**
     * The maximum of the population of values. Ignored if `count` is zero.
     *
     * Generated from protobuf field <code>double maximum = 4;</code>
     * @return float
     */
    public function getMaximum()
    {
        return $this->maximum;
    }

    /**
     * The maximum of the population of values. Ignored if `count` is zero.
     *
     * Generated from protobuf field <code>double maximum = 4;</code>
     * @param float $var
     * @return $this
     */
    public function setMaximum($var)
    {
        GPBUtil::checkDouble($var);
        $this->maximum = $var;

        return $this;
    }

    /**
     * The sum of squared deviations from the mean:
     *   Sum[i=1..count]((x_i - mean)^2)
     * where each x_i is a sample values. If `count` is zero then this field
     * must be zero, otherwise validation of the request fails.
     *
     * Generated from protobuf field <code>double sum_of_squared_deviation = 5;</code>
     * @return float
     */
    public function getSumOfSquaredDeviation()
    {
        return $this->sum_of_squared_deviation;
    }

    /**
     * The sum of squared deviations from the mean:
     *   Sum[i=1..count]((x_i - mean)^2)
     * where each x_i is a sample values. If `count` is zero then this field
     * must be zero, otherwise validation of the request fails.
     *
     * Generated from protobuf field <code>double sum_of_squared_deviation = 5;</code>
     * @param float $var
     * @return $this
     */
    public function setSumOfSquaredDeviation($var)
    {
        GPBUtil::checkDouble($var);
        $this->sum_of_squared_deviation = $var;

        return $this;
    }

    /**
     * The number of samples in each histogram bucket. `bucket_counts` are
     * optional. If present, they must sum to the `count` value.
     * The buckets are defined below in `bucket_option`. There are N buckets.
     * `bucket_counts[0]` is the number of samples in the underflow bucket.
     * `bucket_counts[1]` to `bucket_counts[N-1]` are the numbers of samples
     * in each of the finite buckets. And `bucket_counts[N] is the number
     * of samples in the overflow bucket. See the comments of `bucket_option`
     * below for more details.
     * Any suffix of trailing zeros may be omitted.
     *
     * Generated from protobuf field <code>repeated int64 bucket_counts = 6;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getBucketCounts()
    {
        return $this->bucket_counts;
    }

    /**
     * The number of samples in each histogram bucket. `bucket_counts` are
     * optional. If present, they must sum to the `count` value.
     * The buckets are defined below in `bucket_option`. There are N buckets.
     * `bucket_counts[0]` is the number of samples in the underflow bucket.
     * `bucket_counts[1]` to `bucket_counts[N-1]` are the numbers of samples
     * in each of the finite buckets. And `bucket_counts[N] is the number
     * of samples in the overflow bucket. See the comments of `bucket_option`
     * below for more details.
     * Any suffix of trailing zeros may be omitted.
     *
     * Generated from protobuf field <code>repeated int64 bucket_counts = 6;</code>
     * @param int[]|string[]|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setBucketCounts($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::INT64);
        $this->bucket_counts = $arr;

        return $this;
    }

    /**
     * Buckets with constant width.
     *
     * Generated from protobuf field <code>.google.api.servicecontrol.v1.Distribution.LinearBuckets linear_buckets = 7;</code>
     * @return \Google\Api\Servicecontrol\V1\Distribution\LinearBuckets
     */
    public function getLinearBuckets()
    {
        return $this->readOneof(7);
    }

    /**
     * Buckets with constant width.
     *
     * Generated from protobuf field <code>.google.api.servicecontrol.v1.Distribution.LinearBuckets linear_buckets = 7;</code>
     * @param \Google\Api\Servicecontrol\V1\Distribution\LinearBuckets $var
     * @return $this
     */
    public function setLinearBuckets($var)
    {
        GPBUtil::checkMessage($var, \Google\Api\Servicecontrol\V1\Distribution_LinearBuckets::class);
        $this->writeOneof(7, $var);

        return $this;
    }

    /**
     * Buckets with exponentially growing width.
     *
     * Generated from protobuf field <code>.google.api.servicecontrol.v1.Distribution.ExponentialBuckets exponential_buckets = 8;</code>
     * @return \Google\Api\Servicecontrol\V1\Distribution\ExponentialBuckets
     */
    public function getExponentialBuckets()
    {
        return $this->readOneof(8);
    }

    /**
     * Buckets with exponentially growing width.
     *
     * Generated from protobuf field <code>.google.api.servicecontrol.v1.Distribution.ExponentialBuckets exponential_buckets = 8;</code>
     * @param \Google\Api\Servicecontrol\V1\Distribution\ExponentialBuckets $var
     * @return $this
     */
    public function setExponentialBuckets($var)
    {
        GPBUtil::checkMessage($var, \Google\Api\Servicecontrol\V1\Distribution_ExponentialBuckets::class);
        $this->writeOneof(8, $var);

        return $this;
    }

    /**
     * Buckets with arbitrary user-provided width.
     *
     * Generated from protobuf field <code>.google.api.servicecontrol.v1.Distribution.ExplicitBuckets explicit_buckets = 9;</code>
     * @return \Google\Api\Servicecontrol\V1\Distribution\ExplicitBuckets
     */
    public function getExplicitBuckets()
    {
        return $this->readOneof(9);
    }

    /**
     * Buckets with arbitrary user-provided width.
     *
     * Generated from protobuf field <code>.google.api.servicecontrol.v1.Distribution.ExplicitBuckets explicit_buckets = 9;</code>
     * @param \Google\Api\Servicecontrol\V1\Distribution\ExplicitBuckets $var
     * @return $this
     */
    public function setExplicitBuckets($var)
    {
        GPBUtil::checkMessage($var, \Google\Api\Servicecontrol\V1\Distribution_ExplicitBuckets::class);
        $this->writeOneof(9, $var);

        return $this;
    }

    /**
     * @return string
     */
    public function getBucketOption()
    {
        return $this->whichOneof("bucket_option");
    }

}

