<table class="table table-bordered">
  <tr>
    <td style="text-align:center; background-color:#ab1b1c; border:1px solid #ab1b1c;" colspan="2"><img src="https://www.fraudlabspro.com/images/logo_200.png" alt="FraudLabs Pro" /></td>
  </tr>
  <tr>
    <td><span data-toggle="tooltip" data-container="#tab-general" title="{{ help_transaction_id }}">{{ text_transaction_id }}</span></td>
    <td><a href="https://www.fraudlabspro.com/merchant/transaction-details/{{ flp_id }}/" target="_blank">{{ flp_id }}</a></td>
  </tr>
  <tr>
    <td><span data-toggle="tooltip" data-container="#tab-general" title="{{ help_score }}">{{ text_score }}</span></td>
    <td><img class="img-responsive" alt="" src="//fraudlabspro.hexa-soft.com/images/fraudscore/fraudlabsproscore{{ flp_score }}.png" /></td>
  </tr>
  <tr>
    <td><span data-toggle="tooltip" data-container="#tab-general" title="{{ help_status }}">{{ text_status }}</span></td>
    <td id="flp_status"><span style="font-weight:bold; color: {% if flp_status|lower == 'approve' %} #5cb85c {% elseif flp_status|lower == 'review' %} #f0ad4e {% else %} #d9534f {% endif %};">{{ flp_status }}</span></td>
  </tr>
  <tr>
    <td><span data-toggle="tooltip" data-container="#tab-general" title="{{ help_ip_address }}">{{ text_ip_address }}</span></td>
    <td>{{ flp_ip_address }}</td>
  </tr>
  <tr>
    <td><span data-toggle="tooltip" data-container="#tab-general" title="{{ help_ip_net_speed }}">{{ text_ip_net_speed }}</span></td>
    <td>{{ flp_ip_net_speed }}</td>
  </tr>
  <tr>
    <td><span data-toggle="tooltip" data-container="#tab-general" title="{{ help_ip_isp_name }}">{{ text_ip_isp_name }}</span></td>
    <td>{{ flp_ip_isp_name }}</td>
  </tr>
  <tr>
    <td><span data-toggle="tooltip" data-container="#tab-general" title="{{ help_ip_usage_type }}">{{ text_ip_usage_type }}</span></td>
    <td>{{ flp_ip_usage_type }}</td>
  </tr>
  <tr>
    <td><span data-toggle="tooltip" data-container="#tab-general" title="{{ help_ip_domain }}">{{ text_ip_domain }}</span></td>
    <td>{{ flp_ip_domain }}</td>
  </tr>
  <tr>
    <td><span data-toggle="tooltip" data-container="#tab-general" title="{{ help_ip_time_zone }}">{{ text_ip_time_zone }}</span></td>
    <td>{{ flp_ip_time_zone }}</td>
  </tr>
  <tr>
    <td><span data-toggle="tooltip" data-container="#tab-general" title="{{ help_ip_location }}">{{ text_ip_location }}</span></td>
    <td>{{ flp_ip_location }}</td>
  </tr>
  <tr>
    <td><span data-toggle="tooltip" data-container="#tab-general" title="{{ help_ip_distance }}">{{ text_ip_distance }}</span></td>
    <td>{{ flp_ip_distance }}</td>
  </tr>
  <tr>
    <td><span data-toggle="tooltip" data-container="#tab-general" title="{{ help_ip_latitude }}">{{ text_ip_latitude }}</span></td>
    <td>{{ flp_ip_latitude }}</td>
  </tr>
  <tr>
    <td><span data-toggle="tooltip" data-container="#tab-general" title="{{ help_ip_longitude }}">{{ text_ip_longitude }}</span></td>
    <td>{{ flp_ip_longitude }}</td>
  </tr>
  <tr>
    <td><span data-toggle="tooltip" data-container="#tab-general" title="{{ help_risk_country }}">{{ text_risk_country }}</span></td>
    <td>{{ flp_risk_country }}</td>
  </tr>
  <tr>
    <td><span data-toggle="tooltip" data-container="#tab-general" title="{{ help_free_email }}">{{ text_free_email }}</span></td>
    <td>{{ flp_free_email }}</td>
  </tr>
  <tr>
    <td><span data-toggle="tooltip" data-container="#tab-general" title="{{ help_ship_forward }}">{{ text_ship_forward }}</span></td>
    <td>{{ flp_ship_forward }}</td>
  </tr>
  <tr>
    <td><span data-toggle="tooltip" data-container="#tab-general" title="{{ help_using_proxy }}">{{ text_using_proxy }}</span></td>
    <td>{{ flp_using_proxy }}</td>
  </tr>
  <tr>
    <td><span data-toggle="tooltip" data-container="#tab-general" title="{{ help_bin_found }}">{{ text_bin_found }}</span></td>
    <td>{{ flp_bin_found }}</td>
  </tr>
  <tr>
    <td><span data-toggle="tooltip" data-container="#tab-general" title="{{ help_email_blacklist }}">{{ text_email_blacklist }}</span></td>
    <td>{{ flp_email_blacklist }}</td>
  </tr>
  <tr>
    <td><span data-toggle="tooltip" data-container="#tab-general" title="{{ help_credit_card_blacklist }}">{{ text_credit_card_blacklist }}</span></td>
    <td>{{ flp_credit_card_blacklist }}</td>
  </tr>
  <tr>
    <td><span data-toggle="tooltip" data-container="#tab-general" title="{{ help_credits }}">{{ text_credits }}</span></td>
    <td>{{ flp_credits }} {{ text_flp_upgrade }}</td>
  </tr>
  <tr>
    <td><span data-toggle="tooltip" data-container="#tab-general" title="{{ help_message }}">{{ text_message }}</span></td>
    <td>{{ flp_message }}</td>
  </tr>
  
  
  {% if flp_status == 'review' %}
  <tr style="background-color: #eee;">
    <td id="flp_action" colspan="2">
      <form id="review-action" method="post">
	<div align="center">
	  <button type="button" id="button-flp-approve" class="btn btn-primary"><i class="fa fa-check"></i> Approve</button>
	  <button type="button" id="button-flp-reject" class="btn btn-danger"><i class="fa fa-remove"></i> Reject</button>
	</div>
	<input type="hidden" id="flp_id" name="flp_id" value="{{ flp_id }}" />
	<input type="hidden" id="new_status" name="new_status" value="" />
      </form>
      
      <script>
	$(document).ready(function(){
		$("#button-flp-approve").click(function(){
			$("#new_status").val("APPROVE");
			$("#review-action").submit();
		});
	});
	
	$(document).ready(function(){
		$("#button-flp-reject").click(function(){
			$("#new_status").val("REJECT");
			$("#review-action").submit();
		});
	});
      </script>
    </td>
  </tr>
  {% endif %}
</table>
<div>
	{{ text_flp_merchant_area }}
</div>