<div id="message-center" class="message-center">
  <div class="message-header">
    <h3><i class="fa fa-envelope"></i> {{ text_messages }}</h3>
    <div class="message-actions">
      <button type="button" class="btn btn-link" id="mark-all-read-messages" title="{{ text_mark_all_read }}"><i class="fa fa-check-circle"></i></button>
      <button type="button" class="btn btn-link" id="refresh-messages" title="{{ text_refresh }}"><i class="fa fa-refresh"></i></button>
      <button type="button" class="btn btn-link" id="compose-new-message" title="{{ text_compose }}"><i class="fa fa-pencil"></i></button>
      <button type="button" class="btn btn-link" id="message-settings" title="{{ text_notification_settings }}"><i class="fa fa-cog"></i></button>
    </div>
  </div>
  
  <div class="message-tabs">
    <ul class="nav nav-tabs">
      <li class="active"><a href="#all-messages" data-toggle="tab">{{ text_all }}</a></li>
      <li><a href="#unread-messages" data-toggle="tab">{{ text_unread }} <span class="badge unread-count">0</span></a></li>
      <li><a href="#starred-messages" data-toggle="tab">{{ text_starred }}</a></li>
      <li><a href="#archived-messages" data-toggle="tab">{{ text_archived }}</a></li>
    </ul>
  </div>
  
  <div class="tab-content">
    <div class="tab-pane active" id="all-messages">
      <div class="message-list" id="all-messages-list">
        <!-- سيتم تحميل الرسائل هنا عبر AJAX -->
      </div>
      <div class="message-empty text-center" style="display: none;">
        <i class="fa fa-envelope-o fa-4x text-muted"></i>
        <p>{{ text_no_messages }}</p>
      </div>
    </div>
    
    <div class="tab-pane" id="unread-messages">
      <div class="message-list" id="unread-messages-list">
        <!-- سيتم تحميل الرسائل غير المقروءة هنا -->
      </div>
      <div class="message-empty text-center" style="display: none;">
        <i class="fa fa-check-circle fa-4x text-muted"></i>
        <p>{{ text_no_unread_messages }}</p>
      </div>
    </div>
    
    <div class="tab-pane" id="starred-messages">
      <div class="message-list" id="starred-messages-list">
        <!-- سيتم تحميل الرسائل المميزة بنجمة هنا -->
      </div>
      <div class="message-empty text-center" style="display: none;">
        <i class="fa fa-star-o fa-4x text-muted"></i>
        <p>{{ text_no_starred_messages }}</p>
      </div>
    </div>
    
    <div class="tab-pane" id="archived-messages">
      <div class="message-list" id="archived-messages-list">
        <!-- سيتم تحميل الرسائل المؤرشفة هنا -->
      </div>
      <div class="message-empty text-center" style="display: none;">
        <i class="fa fa-archive fa-4x text-muted"></i>
        <p>{{ text_no_archived_messages }}</p>
      </div>
    </div>
  </div>
  
  <div class="message-footer">
    <a href="{{ message_history }}" class="btn btn-link btn-block">{{ text_view_all_messages }}</a>
  </div>
</div>

<!-- قالب لعنصر الرسالة (سيتم استخدامه مع JS) -->
<script id="message-template" type="text/template">
  <div class="message-item {message_class}" data-message-id="{message_id}">
    <div class="message-avatar">
      {avatar}
    </div>
    <div class="message-content">
      <div class="message-header">
        <div class="message-sender">{sender_name}</div>
        <div class="message-meta">
          <span class="message-time">{message_time}</span>
          {workflow_badge}
        </div>
      </div>
      <div class="message-title">{message_title}</div>
      <div class="message-text">{message_text}</div>
      {attachment_preview}
    </div>
    <div class="message-actions">
      <button type="button" class="btn btn-link btn-sm star-message" title="{star_title}">
        <i class="fa {star_icon}"></i>
      </button>
      <button type="button" class="btn btn-link btn-sm mark-read" title="{read_title}">
        <i class="fa {read_icon}"></i>
      </button>
      <button type="button" class="btn btn-link btn-sm reply-message" title="{{ text_reply }}">
        <i class="fa fa-reply"></i>
      </button>
      <div class="message-dropdown">
        <button type="button" class="btn btn-link btn-sm dropdown-toggle" data-toggle="dropdown">
          <i class="fa fa-ellipsis-v"></i>
        </button>
        <ul class="dropdown-menu dropdown-menu-right">
          <li><a href="#" class="forward-message"><i class="fa fa-share"></i> {{ text_forward }}</a></li>
          <li><a href="#" class="archive-message"><i class="fa fa-archive"></i> {{ text_archive }}</a></li>
          <li class="divider"></li>
          <li><a href="#" class="delete-message"><i class="fa fa-trash text-danger"></i> {{ text_delete }}</a></li>
        </ul>
      </div>
    </div>
  </div>
</script>

<!-- Modal لإعدادات الرسائل -->
<div class="modal fade" id="modal-message-settings" tabindex="-1" role="dialog">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
        <h4 class="modal-title">{{ text_message_settings }}</h4>
      </div>
      <div class="modal-body">
        <form id="form-message-settings">
          <div class="form-group">
            <label class="control-label">{{ text_message_preferences }}</label>
            
            <div class="checkbox">
              <label>
                <input type="checkbox" name="message_sales" value="1" checked> {{ text_sales_messages }}
              </label>
            </div>
            
            <div class="checkbox">
              <label>
                <input type="checkbox" name="message_support" value="1" checked> {{ text_support_messages }}
              </label>
            </div>
            
            <div class="checkbox">
              <label>
                <input type="checkbox" name="message_system" value="1" checked> {{ text_system_messages }}
              </label>
            </div>
            
            <div class="checkbox">
              <label>
                <input type="checkbox" name="message_workflow" value="1" checked> {{ text_workflow_messages }}
              </label>
            </div>
          </div>
          
          <div class="form-group">
            <label class="control-label">{{ text_message_delivery }}</label>
            
            <div class="checkbox">
              <label>
                <input type="checkbox" name="message_browser" value="1" checked> {{ text_browser_messages }}
              </label>
            </div>
            
            <div class="checkbox">
              <label>
                <input type="checkbox" name="message_email" value="1"> {{ text_email_messages }}
              </label>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_cancel }}</button>
        <button type="button" class="btn btn-primary" id="save-message-settings">{{ button_save }}</button>
      </div>
    </div>
  </div>
</div>

<!-- تضمين نافذة إنشاء رسالة جديدة -->
{% include 'common/message_compose.twig' %}

<style type="text/css">
.message-center {
  width: 380px;
  max-height: 520px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(0,0,0,.15);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.message-header {
  padding: 16px;
  background: linear-gradient(135deg, #2c3e50, #34495e);
  color: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(255,255,255,0.1);
}

.message-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.message-header h3 i {
  margin-right: 8px;
  font-size: 18px;
}

.message-actions .btn-link {
  color: rgba(255,255,255,0.9);
  padding: 6px;
  margin-left: 2px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.message-actions .btn-link:hover {
  color: #fff;
  background-color: rgba(255,255,255,0.1);
}

.message-tabs .nav-tabs {
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  flex-wrap: nowrap;
  overflow-x: auto;
}

.message-tabs .nav-tabs > li {
  flex: 1;
  text-align: center;
  min-width: 80px;
}

.message-tabs .nav-tabs > li > a {
  margin-right: 0;
  border-radius: 0;
  padding: 12px 15px;
  color: #495057;
  font-weight: 500;
  font-size: 13px;
  transition: all 0.2s ease;
  border: none;
  border-bottom: 2px solid transparent;
}

.message-tabs .nav-tabs > li > a:hover {
  background-color: rgba(0,0,0,0.02);
  border-bottom: 2px solid #ddd;
}

.message-tabs .nav-tabs > li.active > a {
  background-color: #fff;
  color: #2c3e50;
  border: none;
  border-bottom: 2px solid #3498db;
}

.message-tabs .badge {
  background-color: #e74c3c;
  margin-left: 5px;
}

.tab-content {
  flex: 1;
  overflow-y: auto;
  max-height: 380px;
  padding: 0;
}

.message-list {
  padding: 0;
}

.message-item {
  display: flex;
  padding: 12px 16px;
  border-bottom: 1px solid #eaedf0;
  transition: background-color 0.2s ease;
  position: relative;
}

.message-item:hover {
  background-color: #f8f9fa;
}

.message-item.unread {
  background-color: rgba(52, 152, 219, 0.05);
}

.message-item.unread:before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background-color: #3498db;
}

.message-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 12px;
  flex-shrink: 0;
}

.message-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.message-avatar .avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #3498db;
  color: white;
  font-weight: 500;
  font-size: 16px;
}

.message-content {
  flex: 1;
  min-width: 0;
  padding-right: 8px;
}

.message-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.message-sender {
  font-weight: 500;
  color: #2c3e50;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 150px;
}

.message-meta {
  display: flex;
  align-items: center;
  color: #6c757d;
  font-size: 12px;
}

.message-time {
  white-space: nowrap;
}

.workflow-badge {
  display: inline-block;
  margin-left: 8px;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 11px;
  font-weight: 500;
  background-color: #f1c40f;
  color: #000;
}

.message-title {
  font-weight: 500;
  margin-bottom: 4px;
  color: #2c3e50;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.message-text {
  color: #495057;
  font-size: 13px;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.attachment-preview {
  margin-top: 8px;
  display: flex;
  gap: 8px;
}

.attachment-item {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  background-color: #f1f3f5;
  border-radius: 4px;
  font-size: 12px;
  color: #495057;
}

.attachment-item i {
  margin-right: 4px;
  color: #6c757d;
}

.message-actions {
  display: flex;
  align-items: flex-start;
  margin-left: 8px;
}

.message-actions .btn-link {
  color: #6c757d;
  padding: 4px;
  font-size: 14px;
  border-radius: 4px;
  transition: all 0.2s ease;
  opacity: 0.7;
}

.message-item:hover .message-actions .btn-link {
  opacity: 1;
}

.message-actions .btn-link:hover {
  color: #2c3e50;
  background-color: rgba(0,0,0,0.05);
}

.message-dropdown .dropdown-menu {
  min-width: 160px;
  padding: 8px 0;
  margin-top: 2px;
  font-size: 13px;
}

.message-dropdown .dropdown-menu > li > a {
  padding: 8px 16px;
  color: #495057;
  display: flex;
  align-items: center;
}

.message-dropdown .dropdown-menu > li > a i {
  margin-right: 8px;
  width: 16px;
  text-align: center;
}

.message-dropdown .dropdown-menu > li > a:hover {
  background-color: #f8f9fa;
  color: #2c3e50;
}

.message-empty {
  padding: 40px 20px;
  color: #6c757d;
}

.message-empty i {
  margin-bottom: 16px;
  opacity: 0.5;
}

.message-empty p {
  font-size: 14px;
  margin: 0;
}

.message-footer {
  padding: 12px;
  border-top: 1px solid #eaedf0;
  text-align: center;
}

.message-footer .btn-link {
  color: #3498db;
  font-weight: 500;
  font-size: 14px;
  text-decoration: none;
}

.message-footer .btn-link:hover {
  text-decoration: underline;
}

/* Modal styles */
#modal-message-settings .modal-content {
  border-radius: 8px;
  border: none;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

#modal-message-settings .modal-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #eaedf0;
  padding: 16px 20px;
  border-radius: 8px 8px 0 0;
}

#modal-message-settings .modal-title {
  font-weight: 500;
  color: #2c3e50;
}

#modal-message-settings .modal-body {
  padding: 20px;
}

#modal-message-settings .control-label {
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 12px;
}

#modal-message-settings .checkbox {
  margin-bottom: 10px;
}

#modal-message-settings .checkbox label {
  display: flex;
  align-items: center;
  color: #495057;
}

#modal-message-settings .checkbox input[type="checkbox"] {
  margin-top: 0;
  margin-right: 8px;
}

#modal-message-settings .modal-footer {
  border-top: 1px solid #eaedf0;
  padding: 16px 20px;
  border-radius: 0 0 8px 8px;
}

/* Modal إنشاء رسالة جديدة */
#modal-compose-message {
  display: none;
}

#modal-compose-message .modal-content {
  border-radius: 8px;
  border: none;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

#modal-compose-message .modal-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #eaedf0;
  padding: 16px 20px;
  border-radius: 8px 8px 0 0;
}

#modal-compose-message .modal-title {
  font-weight: 500;
  color: #2c3e50;
}

#modal-compose-message .modal-body {
  padding: 20px;
}

#modal-compose-message .form-group {
  margin-bottom: 16px;
}

#modal-compose-message .control-label {
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 8px;
}

#modal-compose-message .form-control {
  border-radius: 4px;
  border: 1px solid #dce0e6;
  padding: 8px 12px;
  box-shadow: none;
  transition: border-color 0.2s ease;
}

#modal-compose-message .form-control:focus {
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

#modal-compose-message .modal-footer {
  border-top: 1px solid #eaedf0;
  padding: 16px 20px;
  border-radius: 0 0 8px 8px;
  display: flex;
  justify-content: space-between;
}

#modal-compose-message .attachment-list {
  margin-top: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

#modal-compose-message .attachment-item {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  background-color: #f1f3f5;
  border-radius: 4px;
  font-size: 12px;
  color: #495057;
}

#modal-compose-message .attachment-item i {
  margin-right: 4px;
  color: #6c757d;
}

#modal-compose-message .attachment-item .remove-attachment {
  margin-left: 6px;
  color: #6c757d;
  cursor: pointer;
}

#modal-compose-message .attachment-item .remove-attachment:hover {
  color: #e74c3c;
}
  display: flex;
  align-items: flex-start;
  transition: background-color 0.2s;
}

.message-item:hover {
  background-color: #f9f9f9;
}

.message-item.unread {
  background-color: #f0f7fd;
}

.message-icon {
  margin-right: 10px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #eee;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.message-icon i {
  font-size: 18px;
  color: #555;
}

.message-content {
  flex: 1;
}

.message-title {
  font-weight: 600;
  margin-bottom: 5px;
}

.message-text {
  color: #666;
  font-size: 13px;
  margin-bottom: 5px;
}

.message-time {
  color: #999;
  font-size: 12px;
}

.message-actions {
  display: flex;
  align-items: center;
}

.message-actions .btn-link {
  color: #999;
  padding: 0 5px;
}

.message-actions .btn-link:hover {
  color: #333;
}

.message-empty {
  padding: 30px 15px;
  color: #999;
}

.message-empty i {
  margin-bottom: 10px;
}

.message-footer {
  padding: 10px 15px;
  background: #f5f5f5;
  border-top: 1px solid #ddd;
}

.message-footer .btn-block {
  text-align: center;
  color: #555;
}

/* Message types */
.message-item.message-info .message-icon {
  background-color: #d9edf7;
}

.message-item.message-info .message-icon i {
  color: #31708f;
}

.message-item.message-success .message-icon {
  background-color: #dff0d8;
}

.message-item.message-success .message-icon i {
  color: #3c763d;
}

.message-item.message-warning .message-icon {
  background-color: #fcf8e3;
}

.message-item.message-warning .message-icon i {
  color: #8a6d3b;
}

.message-item.message-danger .message-icon {
  background-color: #f2dede;
}

.message-item.message-danger .message-icon i {
  color: #a94442;
}
</style>

<script type="text/javascript">
$(document).ready(function() {
  // تهيئة مركز الرسائل
  initMessageCenter();
  
  // تحميل الرسائل عند بدء التشغيل
  loadMessages();
  
  // تحديث عدد الرسائل غير المقروءة
  updateUnreadCount();
  
  // معالجة النقر على زر تحديد الكل كمقروء
  $('#mark-all-read-messages').on('click', function() {
    markAllAsRead();
  });
  
  // معالجة النقر على زر تحديث الرسائل
  $('#refresh-messages').on('click', function() {
    loadMessages();
  });
  
  // معالجة النقر على زر إنشاء رسالة جديدة
  $('#compose-new-message').on('click', function() {
    $('#modal-compose-message').modal('show');
  });
  
  // معالجة النقر على زر الإعدادات
  $('#message-settings').on('click', function() {
    $('#modal-message-settings').modal('show');
  });
  
  // معالجة حفظ إعدادات الرسائل
  $('#save-message-settings').on('click', function() {
    saveMessageSettings();
  });
  
  // تحديث الرسائل كل دقيقة
  setInterval(function() {
    loadMessages();
  }, 60000); // 60 ثانية
});

// تهيئة مركز الرسائل
function initMessageCenter() {
  // إضافة معالجات الأحداث للرسائل الديناميكية
  $(document).on('click', '.mark-read', function() {
    var messageId = $(this).closest('.message-item').data('message-id');
    markAsRead(messageId);
  });
  
  $(document).on('click', '.star-message', function() {
    var messageId = $(this).closest('.message-item').data('message-id');
    toggleStarMessage(messageId);
  });
  
  $(document).on('click', '.reply-message', function() {
    var messageId = $(this).closest('.message-item').data('message-id');
    replyToMessage(messageId);
  });
  
  $(document).on('click', '.forward-message', function() {
    var messageId = $(this).closest('.message-item').data('message-id');
    forwardMessage(messageId);
  });
  
  $(document).on('click', '.archive-message', function() {
    var messageId = $(this).closest('.message-item').data('message-id');
    archiveMessage(messageId);
  });
  
  $(document).on('click', '.delete-message', function() {
    var messageId = $(this).closest('.message-item').data('message-id');
    deleteMessage(messageId);
  });
  
  // معالجة النقر على الرسالة نفسها
  $(document).on('click', '.message-content', function() {
    var messageId = $(this).closest('.message-item').data('message-id');
    var messageUrl = $(this).closest('.message-item').data('message-url');
    
    // تحديد الرسالة كمقروءة
    markAsRead(messageId);
    
    // الانتقال إلى الرابط إذا كان موجودًا
    if (messageUrl) {
      window.location.href = messageUrl;
    }
  });
}

// تحميل الرسائل من الخادم
function loadMessages() {
  $.ajax({
    url: 'index.php?route=common/message/getMessages&user_token=' + getURLVar('user_token'),
    type: 'GET',
    dataType: 'json',
    success: function(json) {
      if (json.success) {
        renderMessages(json.messages);
        updateUnreadCount(json.unread_count);
      }
    },
    error: function(xhr, ajaxOptions, thrownError) {
      console.error('Error loading messages:', thrownError);
    }
  });
}

// عرض الرسائل في القائمة
function renderMessages(messages) {
  // تفريغ قوائم الرسائل
  $('#all-messages-list').empty();
  $('#unread-messages-list').empty();
  $('#starred-messages-list').empty();
  $('#archived-messages-list').empty();
  
  // إخفاء رسائل "لا توجد رسائل"
  $('.message-empty').hide();
  
  // التحقق من وجود رسائل
  if (!messages || messages.length === 0) {
    $('#all-messages .message-empty').show();
    $('#unread-messages .message-empty').show();
    $('#starred-messages .message-empty').show();
    $('#archived-messages .message-empty').show();
    return;
  }
  
  // تجميع الرسائل حسب النوع
  var allMessages = [];
  var unreadMessages = [];
  var starredMessages = [];
  var archivedMessages = [];
  
  // معالجة كل رسالة
  for (var i = 0; i < messages.length; i++) {
    var message = messages[i];
    var messageHtml = formatMessage(message);
    
    // إضافة إلى القائمة المناسبة
    if (message.is_archived == '1' || message.is_archived === true) {
      archivedMessages.push(messageHtml);
    } else {
      allMessages.push(messageHtml);
      
      if (message.is_unread == '1' || message.is_read == '0' || message.is_unread === true) {
        unreadMessages.push(messageHtml);
      }
      
      if (message.is_starred == '1' || message.is_starred === true) {
        starredMessages.push(messageHtml);
      }
    }
  }
  
  // إضافة الرسائل إلى القوائم
  if (allMessages.length > 0) {
    $('#all-messages-list').html(allMessages.join(''));
  } else {
    $('#all-messages .message-empty').show();
  }
  
  if (unreadMessages.length > 0) {
    $('#unread-messages-list').html(unreadMessages.join(''));
  } else {
    $('#unread-messages .message-empty').show();
  }
  
  if (starredMessages.length > 0) {
    $('#starred-messages-list').html(starredMessages.join(''));
  } else {
    $('#starred-messages .message-empty').show();
  }
  
  if (archivedMessages.length > 0) {
    $('#archived-messages-list').html(archivedMessages.join(''));
  } else {
    $('#archived-messages .message-empty').show();
  }
  
  // تحديث عدد الرسائل غير المقروءة
  updateUnreadCount(unreadMessages.length);
}

// تحديث عدد الرسائل غير المقروءة
function updateUnreadCount(count) {
  // إذا لم يتم تمرير العدد، قم بحسابه من الرسائل الموجودة
  if (count === undefined) {
    count = $('#unread-messages-list .message-item').length;
  }
  
  // تحديث العداد في علامة التبويب
  $('.unread-count').text(count);
  
  // تحديث عداد الإشعارات في القائمة الرئيسية إذا كان موجودًا
  if ($('#message-notification-count').length > 0) {
    if (count > 0) {
      $('#message-notification-count').text(count).show();
    } else {
      $('#message-notification-count').text('0').hide();
    }
  }
  
  // تحديث عرض علامة التبويب غير المقروءة
  if (count > 0) {
    $('a[href="#unread-messages"]').find('.unread-count').show();
  } else {
    $('a[href="#unread-messages"]').find('.unread-count').hide();
  }
}

// تحديد أيقونة الملف بناءً على نوع الملف
function getFileIcon(fileType) {
  var icon = 'fa-file-o';
  
  switch(fileType) {
    case 'pdf':
      icon = 'fa-file-pdf-o';
      break;
    case 'doc':
    case 'docx':
      icon = 'fa-file-word-o';
      break;
    case 'xls':
    case 'xlsx':
    case 'csv':
      icon = 'fa-file-excel-o';
      break;
    case 'ppt':
    case 'pptx':
      icon = 'fa-file-powerpoint-o';
      break;
    case 'jpg':
    case 'jpeg':
    case 'png':
    case 'gif':
    case 'bmp':
    case 'svg':
      icon = 'fa-file-image-o';
      break;
    case 'zip':
    case 'rar':
    case '7z':
    case 'tar':
    case 'gz':
      icon = 'fa-file-archive-o';
      break;
    case 'mp3':
    case 'wav':
    case 'ogg':
      icon = 'fa-file-audio-o';
      break;
    case 'mp4':
    case 'avi':
    case 'mov':
    case 'wmv':
      icon = 'fa-file-video-o';
      break;
    case 'html':
    case 'htm':
    case 'xml':
    case 'css':
    case 'js':
    case 'php':
    case 'json':
      icon = 'fa-file-code-o';
      break;
    case 'txt':
      icon = 'fa-file-text-o';
      break;
  }
  
  return icon;
}

// تنسيق رسالة واحدة
function formatMessage(message) {
  // الحصول على قالب الرسالة
  var template = $('#message-template').html();
  
  // تحديد فئة الرسالة
  var messageClass = '';
  if (message.is_unread == '1' || message.is_read == '0' || message.is_unread === true) {
    messageClass += ' unread';
  }
  
  // إضافة فئة نوع الرسالة إذا كانت موجودة
  if (message.type) {
    messageClass += ' message-' + message.type;
  }
  
  // تحديد أيقونة النجمة
  var isStarred = message.is_starred == '1' || message.is_starred === true;
  var starIcon = isStarred ? 'fa-star' : 'fa-star-o';
  var starTitle = isStarred ? '{{ text_unstar }}' : '{{ text_star }}';
  
  // تحديد أيقونة القراءة
  var isUnread = message.is_unread == '1' || message.is_read == '0' || message.is_unread === true;
  var readIcon = isUnread ? 'fa-envelope' : 'fa-envelope-open-o';
  var readTitle = isUnread ? '{{ text_mark_read }}' : '{{ text_mark_unread }}';
  
  // إنشاء الصورة الرمزية
  var avatar = '';
  if (message.sender_image) {
    avatar = '<img src="' + message.sender_image + '" alt="' + message.sender_name + '">';
  } else {
    var senderName = message.sender_name || 'User';
    var initials = senderName.split(' ').map(function(name) { return name.charAt(0); }).join('').toUpperCase();
    avatar = '<div class="avatar-placeholder">' + initials + '</div>';
  }
  
  // إنشاء شارة سير العمل إذا كانت موجودة
  var workflowBadge = '';
  if (message.workflow_status) {
    workflowBadge = '<span class="workflow-badge">' + message.workflow_status + '</span>';
  }
  
  // إنشاء معاينة المرفقات إذا كانت موجودة
  var attachmentPreview = '';
  if (message.attachments && message.attachments.length > 0) {
    attachmentPreview = '<div class="attachment-preview">';
    
    for (var i = 0; i < Math.min(message.attachments.length, 2); i++) {
      var attachment = message.attachments[i];
      var fileType = attachment.filename.split('.').pop().toLowerCase();
      var fileIcon = getFileIcon(fileType);
      
      attachmentPreview += '<div class="attachment-item"><i class="fa ' + fileIcon + '"></i> ' + attachment.filename + '</div>';
    }
    
    if (message.attachments.length > 2) {
      attachmentPreview += '<div class="attachment-item">+' + (message.attachments.length - 2) + ' {{ text_more }}</div>';
    }
    
    attachmentPreview += '</div>';
  }
  
  // استخدام القيم الصحيحة من الرسالة
  var messageTitle = message.title || message.subject || '';
  var messageText = message.text || message.content || message.message || '';
  var messageTime = message.time_ago || message.date_added || '';
  var messageId = message.message_id || message.id || '';
  
  // استبدال العناصر في القالب
  return template
    .replace(/{message_class}/g, messageClass)
    .replace(/{message_id}/g, messageId)
    .replace(/{avatar}/g, avatar)
    .replace(/{sender_name}/g, message.sender_name || 'System')
    .replace(/{message_time}/g, messageTime)
    .replace(/{workflow_badge}/g, workflowBadge)
    .replace(/{message_title}/g, messageTitle)
    .replace(/{message_text}/g, messageText)
    .replace(/{attachment_preview}/g, attachmentPreview)
    .replace(/{star_icon}/g, starIcon)
    .replace(/{star_title}/g, starTitle)
    .replace(/{read_icon}/g, readIcon)
    .replace(/{read_title}/g, readTitle);
}
}
  
  if (messages.length > 0) {
    // إخفاء رسائل "لا توجد رسائل"
    $('.message-empty').hide();
    
    // إضافة الرسائل إلى القوائم المناسبة
    $.each(messages, function(index, message) {
      var messageHtml = createMessageHtml(message);
      
      // إضافة إلى قائمة كل الرسائل
      $('#all-messages-list').append(messageHtml);
      
      // إضافة إلى قائمة الرسائل غير المقروءة إذا كانت غير مقروءة
      if (message.is_read == '0') {
        $('#unread-messages-list').append(messageHtml);
      }
      
      // إضافة إلى قائمة الرسائل المؤرشفة إذا كانت مؤرشفة
      if (message.is_archived == '1') {
        $('#archived-messages-list').append(messageHtml);
      }
    });
  } else {
    // إظهار رسائل "لا توجد رسائل"
    $('.message-empty').show();
  }
  
  // التحقق من وجود رسائل غير مقروءة
  if ($('#unread-messages-list').children().length == 0) {
    $('#unread-messages .message-empty').show();
  }
  
  // التحقق من وجود رسائل مؤرشفة
  if ($('#archived-messages-list').children().length == 0) {
    $('#archived-messages .message-empty').show();
  }
}

// إنشاء HTML لرسالة واحدة
function createMessageHtml(message) {
  var template = $('#message-template').html();
  
  // تحديد فئة الرسالة (مقروء/غير مقروء)
  var messageClass = message.is_read == '0' ? 'unread' : '';
  
  // إضافة فئة نوع الرسالة
  messageClass += ' message-' + message.type;
  
  // تحديد أيقونة الرسالة بناءً على النوع
  var messageIcon = 'fa-envelope';
  switch(message.type) {
    case 'info':
      messageIcon = 'fa-info-circle';
      break;
    case 'success':
      messageIcon = 'fa-check-circle';
      break;
    case 'warning':
      messageIcon = 'fa-exclamation-triangle';
      break;
    case 'danger':
      messageIcon = 'fa-exclamation-circle';
      break;
  }
  
  // استبدال المتغيرات في القالب
  var html = template
    .replace('{message_id}', message.message_id)
    .replace('{message_class}', messageClass)
    .replace('{message_icon}', messageIcon)
    .replace('{message_title}', message.title)
    .replace('{message_text}', message.text)
    .replace('{message_time}', message.date_added);
  
  return html;
}

// تحديث عدد الرسائل غير المقروءة
function updateUnreadCount(count) {
  if (count === undefined) {
    // إذا لم يتم تمرير العدد، احسبه من القائمة
    count = $('#unread-messages-list').children().length;
  }
  
  // تحديث العداد في علامة التبويب
  $('.unread-count').text(count);
  
  // تحديث العداد في أيقونة الرسائل في الشريط العلوي
  $('#message-total').text(count);
  
  // إخفاء العداد إذا كان صفرًا
  if (count == 0) {
    $('#message-total').hide();
  } else {
    $('#message-total').show();
  }
}

// حفظ إعدادات الرسائل
function saveMessageSettings() {
  $.ajax({
    url: 'index.php?route=common/message/saveSettings&user_token=' + getURLVar('user_token'),
    type: 'POST',
    data: $('#form-message-settings').serialize(),
    dataType: 'json',
    beforeSend: function() {
      $('#save-message-settings').button('loading');
    },
    complete: function() {
      $('#save-message-settings').button('reset');
    },
    success: function(json) {
      if (json.success) {
        $('#modal-message-settings').modal('hide');
        
        // إظهار رسالة النجاح
        $('#content > .container-fluid').prepend('<div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> ' + json.success + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
      }
    },
    error: function(xhr, ajaxOptions, thrownError) {
      alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
    }
  });
}

// تحديد رسالة كمقروءة
function markAsRead(messageId) {
  $.ajax({
    url: 'index.php?route=common/message/markAsRead&user_token=' + getURLVar('user_token'),
    type: 'POST',
    data: { message_id: messageId },
    dataType: 'json',
    success: function(json) {
      if (json.success) {
        // تحديث واجهة المستخدم
        var $message = $('.message-item[data-message-id="' + messageId + '"]');
        $message.removeClass('unread');
        
        // إزالة من قائمة غير المقروءة
        $('#unread-messages-list .message-item[data-message-id="' + messageId + '"]').remove();
        
        // التحقق من وجود رسائل غير مقروءة
        if ($('#unread-messages-list').children().length == 0) {
          $('#unread-messages .message-empty').show();
        }
        
        // تحديث العداد
        updateUnreadCount();
      }
    }
  });
}

// تحديد جميع الرسائل كمقروءة
function markAllAsRead() {
  $.ajax({
    url: 'index.php?route=common/message/markAllAsRead&user_token=' + getURLVar('user_token'),
    type: 'POST',
    dataType: 'json',
    success: function(json) {
      if (json.success) {
        // تحديث واجهة المستخدم
        $('.message-item').removeClass('unread');
        
        // تفريغ قائمة غير المقروءة
        $('#unread-messages-list').empty();
        $('#unread-messages .message-empty').show();
        
        // تحديث العداد
        updateUnreadCount(0);
      }
    }
  });
}

// تمييز رسالة بنجمة أو إزالة النجمة
function toggleStarMessage(messageId) {
  $.ajax({
    url: 'index.php?route=common/message/toggleStar&user_token=' + getURLVar('user_token'),
    type: 'POST',
    data: { message_id: messageId },
    dataType: 'json',
    success: function(json) {
      if (json.success) {
        // تحديث واجهة المستخدم
        var $message = $('.message-item[data-message-id="' + messageId + '"]');
        var $starButton = $message.find('.star-message');
        var $starIcon = $starButton.find('i');
        
        if (json.is_starred) {
          // تم تمييز الرسالة بنجمة
          $starIcon.removeClass('fa-star-o').addClass('fa-star');
          $starButton.attr('title', '{{ text_unstar }}');
          
          // إضافة نسخة من الرسالة إلى قائمة المميزة بنجمة إذا لم تكن موجودة
          if ($('#starred-messages-list .message-item[data-message-id="' + messageId + '"]').length === 0) {
            var messageClone = $message.clone();
            $('#starred-messages-list').prepend(messageClone);
            $('#starred-messages .message-empty').hide();
          }
        } else {
          // تمت إزالة النجمة
          $starIcon.removeClass('fa-star').addClass('fa-star-o');
          $starButton.attr('title', '{{ text_star }}');
          
          // إزالة من قائمة المميزة بنجمة
          $('#starred-messages-list .message-item[data-message-id="' + messageId + '"]').remove();
          
          // التحقق من وجود رسائل مميزة بنجمة
          if ($('#starred-messages-list').children().length === 0) {
            $('#starred-messages .message-empty').show();
          }
        }
      }
    }
  });
}

// الرد على رسالة
function replyToMessage(messageId) {
  // الحصول على بيانات الرسالة الأصلية
  $.ajax({
    url: 'index.php?route=common/message/getMessage&user_token=' + getURLVar('user_token'),
    type: 'GET',
    data: { message_id: messageId },
    dataType: 'json',
    success: function(json) {
      if (json.message) {
        // فتح نافذة إنشاء رسالة جديدة
        $('#modal-compose-message').modal('show');
        
        // تعبئة بيانات الرسالة
        $('#recipient-select').val(json.message.sender_id);
        $('input[name="subject"]').val('{{ text_re }}: ' + json.message.title);
        
        // إضافة اقتباس من الرسالة الأصلية
        var quoteText = '\n\n> ' + json.message.text.replace(/\n/g, '\n> ');
        $('textarea[name="message"]').val(quoteText);
        
        // التركيز على حقل الرسالة
        $('textarea[name="message"]').focus();
      }
    }
  });
}

// إعادة توجيه رسالة
function forwardMessage(messageId) {
  // الحصول على بيانات الرسالة الأصلية
  $.ajax({
    url: 'index.php?route=common/message/getMessage&user_token=' + getURLVar('user_token'),
    type: 'GET',
    data: { message_id: messageId },
    dataType: 'json',
    success: function(json) {
      if (json.message) {
        // فتح نافذة إنشاء رسالة جديدة
        $('#modal-compose-message').modal('show');
        
        // تعبئة بيانات الرسالة
        $('input[name="subject"]').val('{{ text_fwd }}: ' + json.message.title);
        
        // إضافة نص الرسالة الأصلية
        var forwardText = '\n\n---------- {{ text_forwarded_message }} ----------\n' +
                         '{{ text_from }}: ' + json.message.sender_name + '\n' +
                         '{{ text_date }}: ' + json.message.date_added + '\n' +
                         '{{ text_subject }}: ' + json.message.title + '\n\n' +
                         json.message.text;
        
        $('textarea[name="message"]').val(forwardText);
        
        // نقل المرفقات إذا كانت موجودة
        if (json.message.attachments && json.message.attachments.length > 0) {
          for (var i = 0; i < json.message.attachments.length; i++) {
            var attachment = json.message.attachments[i];
            var fileType = attachment.filename.split('.').pop().toLowerCase();
            var fileIcon = getFileIcon(fileType);
            
            var attachmentHtml = '<div class="attachment-item" data-file="' + attachment.filename + '">' +
              '<i class="fa ' + fileIcon + '"></i> ' + attachment.filename +
              '<span class="remove-attachment"><i class="fa fa-times"></i></span>' +
              '<input type="hidden" name="attachments[]" value="' + attachment.filename + '">' +
              '</div>';
            
            $('#attachment-list').append(attachmentHtml);
          }
        }
        
        // التركيز على حقل المستلم
        $('#recipient-select').focus();
      }
    }
  });
}

// الحصول على أيقونة الملف بناءً على نوعه
function getFileIcon(fileType) {
  var fileIcon = 'fa-file-o';
  
  if (fileType == 'pdf') {
    fileIcon = 'fa-file-pdf-o';
  } else if (['doc', 'docx'].indexOf(fileType) !== -1) {
    fileIcon = 'fa-file-word-o';
  } else if (['xls', 'xlsx'].indexOf(fileType) !== -1) {
    fileIcon = 'fa-file-excel-o';
  } else if (['jpg', 'jpeg', 'png', 'gif'].indexOf(fileType) !== -1) {
    fileIcon = 'fa-file-image-o';
  }
  
  return fileIcon;
}

// أرشفة رسالة
function archiveMessage(messageId) {
  $.ajax({
    url: 'index.php?route=common/message/archiveMessage&user_token=' + getURLVar('user_token'),
    type: 'POST',
    data: { message_id: messageId },
    dataType: 'json',
    success: function(json) {
      if (json.success) {
        // تحديث واجهة المستخدم
        var $message = $('.message-item[data-message-id="' + messageId + '"]');
        
        // إزالة من قائمة كل الرسائل
        $('#all-messages-list .message-item[data-message-id="' + messageId + '"]').remove();
        
        // إزالة من قائمة غير المقروءة إذا كانت موجودة هناك
        $('#unread-messages-list .message-item[data-message-id="' + messageId + '"]').remove();
        
        // إضافة إلى قائمة المؤرشفة
        if ($('#archived-messages-list .message-item[data-message-id="' + messageId + '"]').length == 0) {
          $('#archived-messages-list').append($message.clone());
          $('#archived-messages .message-empty').hide();
        }
        
        // التحقق من وجود رسائل في كل قائمة
        if ($('#all-messages-list').children().length == 0) {
          $('#all-messages .message-empty').show();
        }
        
        if ($('#unread-messages-list').children().length == 0) {
          $('#unread-messages .message-empty').show();
        }
        
        // تحديث العداد
        updateUnreadCount();
      }
    }
  });
}

// حذف رسالة
function deleteMessage(messageId) {
  $.ajax({
    url: 'index.php?route=common/message/delete&user_token=' + getURLVar('user_token'),
    type: 'POST',
    data: { message_id: messageId },
    dataType: 'json',
    success: function(json) {
      if (json.success) {
        // إزالة الرسالة من جميع القوائم
        $('.message-item[data-message-id="' + messageId + '"]').remove();
        
        // التحقق من وجود رسائل في كل قائمة
        if ($('#all-messages-list').children().length == 0) {
          $('#all-messages .message-empty').show();
        }
        
        if ($('#unread-messages-list').children().length == 0) {
          $('#unread-messages .message-empty').show();
        }
        
        if ($('#archived-messages-list').children().length == 0) {
          $('#archived-messages .message-empty').show();
        }
        
        // تحديث العداد
        updateUnreadCount();
      }
    }
  });
}

// حفظ إعدادات الرسائل
function saveMessageSettings() {
  $.ajax({
    url: 'index.php?route=common/message/saveSettings&user_token=' + getURLVar('user_token'),
    type: 'POST',
    data: $('#form-message-settings').serialize(),
    dataType: 'json',
    success: function(json) {
      if (json.success) {
        $('#modal-message-settings').modal('hide');
        
        // عرض رسالة نجاح
        $('#content > .container-fluid').prepend('<div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> ' + json.success + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
      }
    }
  });
}
</script>