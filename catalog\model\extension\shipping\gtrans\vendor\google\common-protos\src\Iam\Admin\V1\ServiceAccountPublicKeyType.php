<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/iam/admin/v1/iam.proto

namespace Google\Iam\Admin\V1;

use UnexpectedValueException;

/**
 * Supported public key output formats.
 *
 * Protobuf type <code>google.iam.admin.v1.ServiceAccountPublicKeyType</code>
 */
class ServiceAccountPublicKeyType
{
    /**
     * Unspecified. Returns nothing here.
     *
     * Generated from protobuf enum <code>TYPE_NONE = 0;</code>
     */
    const TYPE_NONE = 0;
    /**
     * X509 PEM format.
     *
     * Generated from protobuf enum <code>TYPE_X509_PEM_FILE = 1;</code>
     */
    const TYPE_X509_PEM_FILE = 1;
    /**
     * Raw public key.
     *
     * Generated from protobuf enum <code>TYPE_RAW_PUBLIC_KEY = 2;</code>
     */
    const TYPE_RAW_PUBLIC_KEY = 2;

    private static $valueToName = [
        self::TYPE_NONE => 'TYPE_NONE',
        self::TYPE_X509_PEM_FILE => 'TYPE_X509_PEM_FILE',
        self::TYPE_RAW_PUBLIC_KEY => 'TYPE_RAW_PUBLIC_KEY',
    ];

    public static function name($value)
    {
        if (!isset(self::$valueToName[$value])) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no name defined for value %s', __CLASS__, $value));
        }
        return self::$valueToName[$value];
    }


    public static function value($name)
    {
        $const = __CLASS__ . '::' . strtoupper($name);
        if (!defined($const)) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no value defined for name %s', __CLASS__, $name));
        }
        return constant($const);
    }
}

