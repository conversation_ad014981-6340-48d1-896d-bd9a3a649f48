{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <a href="{{ add }}" data-toggle="tooltip" title="إضافة قالب جديد" class="btn btn-primary">
          <i class="fa fa-plus"></i>
        </a>
        <a href="{{ import }}" data-toggle="tooltip" title="استيراد قوالب" class="btn btn-success">
          <i class="fa fa-upload"></i>
        </a>
        <a href="{{ export }}" data-toggle="tooltip" title="تصدير القوالب" class="btn btn-info">
          <i class="fa fa-download"></i>
        </a>
        <button type="button" data-toggle="tooltip" title="حذف المحدد" class="btn btn-danger" onclick="confirm('هل أنت متأكد من الحذف؟') ? $('#form-template').submit() : false;">
          <i class="fa fa-trash-o"></i>
        </button>
      </div>
      <h1>قوالب خطط التقسيط</h1>
      <ul class="breadcrumb">
        <li><a href="{{ home }}">الرئيسية</a></li>
        <li><a href="{{ templates }}">قوالب التقسيط</a></li>
      </ul>
    </div>
  </div>
  
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible">
      <i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    
    {% if success %}
    <div class="alert alert-success alert-dismissible">
      <i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    
    <!-- إحصائيات سريعة -->
    <div class="row">
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-primary">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-file-text-o fa-2x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ statistics.total_templates }}</div>
                <div>إجمالي القوالب</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-success">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-check-circle fa-2x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ statistics.active_templates }}</div>
                <div>القوالب النشطة</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-info">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-star fa-2x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ statistics.most_used_template.usage_count }}</div>
                <div>الأكثر استخداماً</div>
              </div>
            </div>
          </div>
          <div class="panel-footer">
            <span class="pull-left">{{ statistics.most_used_template.name }}</span>
            <div class="clearfix"></div>
          </div>
        </div>
      </div>
      
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-warning">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-percent fa-2x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ statistics.avg_interest_rate }}%</div>
                <div>متوسط الفائدة</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- فلاتر البحث -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title">
          <i class="fa fa-filter"></i> فلاتر البحث
          <button type="button" class="btn btn-xs btn-default pull-right" data-toggle="collapse" data-target="#filter-panel">
            <i class="fa fa-chevron-down"></i>
          </button>
        </h3>
      </div>
      <div id="filter-panel" class="panel-collapse collapse">
        <div class="panel-body">
          <form method="get" id="filter-form">
            <input type="hidden" name="route" value="sale/installment_template" />
            <input type="hidden" name="user_token" value="{{ user_token }}" />
            
            <div class="row">
              <div class="col-md-4">
                <div class="form-group">
                  <label for="filter_name">اسم القالب</label>
                  <input type="text" name="filter_name" value="{{ filter_name }}" placeholder="اسم القالب" id="filter_name" class="form-control" />
                </div>
              </div>
              
              <div class="col-md-4">
                <div class="form-group">
                  <label for="filter_status">الحالة</label>
                  <select name="filter_status" id="filter_status" class="form-control">
                    <option value="">الكل</option>
                    <option value="1"{% if filter_status == '1' %} selected="selected"{% endif %}>نشط</option>
                    <option value="0"{% if filter_status == '0' %} selected="selected"{% endif %}>غير نشط</option>
                  </select>
                </div>
              </div>
              
              <div class="col-md-4">
                <div class="form-group">
                  <label for="filter_interest_type">نوع الفائدة</label>
                  <select name="filter_interest_type" id="filter_interest_type" class="form-control">
                    <option value="">الكل</option>
                    <option value="none"{% if filter_interest_type == 'none' %} selected="selected"{% endif %}>بدون فوائد</option>
                    <option value="fixed"{% if filter_interest_type == 'fixed' %} selected="selected"{% endif %}>فائدة ثابتة</option>
                    <option value="reducing"{% if filter_interest_type == 'reducing' %} selected="selected"{% endif %}>فائدة متناقصة</option>
                    <option value="simple"{% if filter_interest_type == 'simple' %} selected="selected"{% endif %}>فائدة بسيطة</option>
                  </select>
                </div>
              </div>
            </div>
            
            <div class="row">
              <div class="col-md-12">
                <button type="submit" class="btn btn-primary"><i class="fa fa-filter"></i> فلترة</button>
                <a href="{{ refresh }}" class="btn btn-default"><i class="fa fa-refresh"></i> مسح</a>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
    
    <!-- جدول القوالب -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-list"></i> قائمة قوالب التقسيط</h3>
      </div>
      <div class="panel-body">
        <form action="{{ delete }}" method="post" enctype="multipart/form-data" id="form-template">
          <div class="table-responsive">
            <table class="table table-bordered table-hover">
              <thead>
                <tr>
                  <td style="width: 1px;" class="text-center">
                    <input type="checkbox" onclick="$('input[name*=\'selected\']').prop('checked', this.checked);" />
                  </td>
                  <td>اسم القالب</td>
                  <td class="text-center">عدد الأقساط</td>
                  <td class="text-center">معدل الفائدة</td>
                  <td class="text-center">نوع الفائدة</td>
                  <td class="text-right">الحد الأدنى</td>
                  <td class="text-right">الحد الأقصى</td>
                  <td class="text-center">الدفعة المقدمة</td>
                  <td class="text-center">الحالة</td>
                  <td class="text-center">تاريخ الإضافة</td>
                  <td class="text-center">الإجراءات</td>
                </tr>
              </thead>
              <tbody>
                {% if templates %}
                {% for template in templates %}
                <tr>
                  <td class="text-center">
                    <input type="checkbox" name="selected[]" value="{{ template.template_id }}" />
                  </td>
                  <td>
                    <strong>{{ template.name }}</strong>
                    {% if template.description %}
                    <br><small class="text-muted">{{ template.description }}</small>
                    {% endif %}
                  </td>
                  <td class="text-center">
                    <span class="badge badge-primary">{{ template.installments_count }}</span>
                  </td>
                  <td class="text-center">
                    <span class="text-info">{{ template.interest_rate }}</span>
                  </td>
                  <td class="text-center">
                    <span class="label label-default">{{ template.interest_type }}</span>
                  </td>
                  <td class="text-right">{{ template.min_amount }}</td>
                  <td class="text-right">{{ template.max_amount }}</td>
                  <td class="text-center">{{ template.down_payment_percentage }}</td>
                  <td class="text-center">
                    <span class="label label-{{ template.status_class }}">{{ template.status_text }}</span>
                  </td>
                  <td class="text-center">{{ template.date_added }}</td>
                  <td class="text-center">
                    <div class="btn-group">
                      <button type="button" class="btn btn-default btn-sm dropdown-toggle" data-toggle="dropdown">
                        <i class="fa fa-cog"></i> <span class="caret"></span>
                      </button>
                      <ul class="dropdown-menu dropdown-menu-right">
                        <li><a href="{{ template.edit }}"><i class="fa fa-pencil"></i> تعديل</a></li>
                        <li><a href="{{ template.copy }}"><i class="fa fa-copy"></i> نسخ</a></li>
                        <li><a href="{{ template.preview }}" target="_blank"><i class="fa fa-eye"></i> معاينة</a></li>
                        <li class="divider"></li>
                        <li><a href="#" onclick="if(confirm('هل أنت متأكد؟')) { location='{{ template.delete }}'; }"><i class="fa fa-trash"></i> حذف</a></li>
                      </ul>
                    </div>
                  </td>
                </tr>
                {% endfor %}
                {% else %}
                <tr>
                  <td colspan="11" class="text-center">لا توجد قوالب تقسيط</td>
                </tr>
                {% endif %}
              </tbody>
            </table>
          </div>
        </form>
        
        <div class="row">
          <div class="col-sm-6 text-left">{{ pagination }}</div>
          <div class="col-sm-6 text-right">{{ results }}</div>
        </div>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript">
$(document).ready(function() {
    // تفعيل tooltips
    $('[data-toggle="tooltip"]').tooltip();
    
    // تفعيل/إلغاء تفعيل القوالب
    $('.toggle-status').on('click', function(e) {
        e.preventDefault();
        var templateId = $(this).data('template-id');
        var status = $(this).data('status');
        
        $.ajax({
            url: 'index.php?route=sale/installment_template/toggleStatus&user_token={{ user_token }}',
            type: 'POST',
            data: {
                template_id: templateId,
                status: status
            },
            success: function(response) {
                location.reload();
            }
        });
    });
    
    // تأكيد الحذف
    $('.delete-template').on('click', function(e) {
        e.preventDefault();
        if (confirm('هل أنت متأكد من حذف هذا القالب؟ هذا الإجراء لا يمكن التراجع عنه.')) {
            window.location.href = $(this).attr('href');
        }
    });
});
</script>

{{ footer }}
