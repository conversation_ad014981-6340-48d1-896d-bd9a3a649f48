<div class="table-responsive" style="margin-top:10px">
  <table class="table table-bordered">
    <thead>
      <tr>
        <td class="text-center">{{ column_image }}</td>
        <td class="text-center">{{ column_name }}</td>
        <td class="text-center">{{ column_quantity }}</td>
        <td class="text-center">{{ column_price }}</td>
        <td class="text-center">{{ column_total }}</td>
      </tr>
    </thead>
    <tbody>
      {% for product in products %}
        <tr>
          <td class="text-center">{% if product.thumb %} <a href="{{ product.href }}"><img height="60" width="60" src="{{ product.thumb }}" alt="{{ product.name }}" title="{{ product.name }}" class="img-thumbnail"/></a> {% endif %}</td>
          <td class="text-start text-wrap"><a href="{{ product.href }}">{{ product.name }}</a>{% if not product.stock %} <span class="text-danger">***</span>{% endif %}
            {% for option in product.option %}
              <br/>
              <small> - {{ option.name }}: {{ option.value }}</small>
            {% endfor %}
            {% if product.reward %}
              <br/>
              <small> - {{ text_points }}: {{ product.reward }}</small>
            {% endif %}
          </td>
          <td class="text-center">
            <form method="post" data-oc-target="#shopping-cart">
              <div class="input-group">
                <input type="text" name="quantity" value="{{ product.quantity }}" size="1" class="form-control"> <input type="hidden" name="key" value="{{ product.cart_id }}">
                <button type="submit" formaction="{{ product_edit }}" data-bs-toggle="tooltip" title="{{ button_update }}" class="btn btn-primary"><i class="fa-solid fa-rotate"></i></button>
                <button type="submit" formaction="{{ product_remove }}" data-bs-toggle="tooltip" title="{{ button_remove }}" class="btn btn-danger"><i class="fa-solid fa-circle-xmark"></i></button>
              </div>
            </form>
          </td>
          <td class="text-center">{{ product.price }}</td>
          <td class="text-center">{{ product.total }}</td>
        </tr>
      {% endfor %}
      {% for voucher in vouchers %}
        <tr>
          <td></td>
          <td class="text-center text-wrap">{{ voucher.description }}</td>
          <td class="text-center"></td>
          <td class="text-center">
            <form method="post" data-oc-target="#shopping-cart">
              <div class="input-group">
                <input type="text" name="quantity" value="1" size="1" class="form-control" disabled/>
                <button type="submit" formaction="{{ voucher_remove }}" data-bs-toggle="tooltip" title="{{ button_remove }}" class="btn btn-danger"><i class="fa-solid fa-circle-xmark"></i></button>
              </div>
              <input type="hidden" name="key" value="{{ voucher.key }}"/>
            </form>
          </td>
          <td class="text-center">{{ voucher.amount }}</td>
          <td class="text-center">{{ voucher.amount }}</td>
        </tr>
      {% endfor %}
    </tbody>
    <tfoot id="checkout-total">
      {% for total in totals %}
        <tr>
          <td colspan="4" class="text-end"><strong>{{ total.title }}</strong></td>
          <td class="text-center">{{ total.text }}</td>
        </tr>
      {% endfor %}
    </tfoot>
  </table>
</div>
