{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="button" data-toggle="tooltip" title="{{ button_filter }}" onclick="$('#filter-warranty').toggleClass('hidden-sm hidden-xs')" class="btn btn-default hidden-md hidden-lg"><i class="fa fa-filter"></i></button>
        <a href="{{ add }}" data-toggle="tooltip" title="{{ button_add }}" class="btn btn-primary"><i class="fa fa-plus"></i></a>
        <button type="button" data-toggle="tooltip" title="{{ button_delete }}" class="btn btn-danger" onclick="confirm('{{ text_confirm }}') ? $('#form-warranty').submit() : false;"><i class="fa fa-trash-o"></i></button>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    {% if success %}
    <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    <div class="row">
      <div id="filter-warranty" class="col-md-3 col-md-push-9 col-sm-12 hidden-sm hidden-xs">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-filter"></i> {{ text_filter }}</h3>
          </div>
          <div class="panel-body">
            <div class="form-group">
              <label class="control-label" for="input-warranty-number">{{ entry_warranty_number }}</label>
              <input type="text" name="filter_warranty_number" value="{{ filter_warranty_number }}" placeholder="{{ entry_warranty_number }}" id="input-warranty-number" class="form-control" />
            </div>
            <div class="form-group">
              <label class="control-label" for="input-customer">{{ entry_customer }}</label>
              <input type="text" name="filter_customer" value="{{ filter_customer }}" placeholder="{{ entry_customer }}" id="input-customer" class="form-control" />
            </div>
            <div class="form-group">
              <label class="control-label" for="input-product">{{ entry_product }}</label>
              <input type="text" name="filter_product" value="{{ filter_product }}" placeholder="{{ entry_product }}" id="input-product" class="form-control" />
            </div>
            <div class="form-group">
              <label class="control-label" for="input-status">{{ entry_status }}</label>
              <select name="filter_status" id="input-status" class="form-control">
                <option value=""></option>
                <option value="active"{% if filter_status == 'active' %} selected="selected"{% endif %}>{{ text_status_active }}</option>
                <option value="expired"{% if filter_status == 'expired' %} selected="selected"{% endif %}>{{ text_status_expired }}</option>
                <option value="cancelled"{% if filter_status == 'cancelled' %} selected="selected"{% endif %}>{{ text_status_cancelled }}</option>
                <option value="suspended"{% if filter_status == 'suspended' %} selected="selected"{% endif %}>{{ text_status_suspended }}</option>
              </select>
            </div>
            <div class="form-group">
              <label class="control-label" for="input-date-added">{{ entry_date_added }}</label>
              <div class="input-group date">
                <input type="text" name="filter_date_added" value="{{ filter_date_added }}" placeholder="{{ entry_date_added }}" data-date-format="YYYY-MM-DD" id="input-date-added" class="form-control" />
                <span class="input-group-btn">
                <button class="btn btn-default" type="button"><i class="fa fa-calendar"></i></button>
                </span></div>
            </div>
            <div class="form-group text-right">
              <button type="button" id="button-filter" class="btn btn-default"><i class="fa fa-filter"></i> {{ button_filter }}</button>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-9 col-md-pull-3 col-sm-12">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-list"></i> {{ text_list }}</h3>
          </div>
          <div class="panel-body">
            <form method="post" action="{{ delete }}" enctype="multipart/form-data" id="form-warranty">
              <div class="table-responsive">
                <table class="table table-bordered table-hover">
                  <thead>
                    <tr>
                      <td style="width: 1px;" class="text-center"><input type="checkbox" onclick="$('input[name*=\'selected\']').prop('checked', this.checked);" /></td>
                      <td class="text-left">{% if sort == 'warranty_number' %}
                        <a href="{{ sort_warranty_number }}" class="{{ order|lower }}">{{ column_warranty_number }}</a>
                        {% else %}
                        <a href="{{ sort_warranty_number }}">{{ column_warranty_number }}</a>
                        {% endif %}</td>
                      <td class="text-left">{% if sort == 'customer' %}
                        <a href="{{ sort_customer }}" class="{{ order|lower }}">{{ column_customer }}</a>
                        {% else %}
                        <a href="{{ sort_customer }}">{{ column_customer }}</a>
                        {% endif %}</td>
                      <td class="text-left">{% if sort == 'product' %}
                        <a href="{{ sort_product }}" class="{{ order|lower }}">{{ column_product }}</a>
                        {% else %}
                        <a href="{{ sort_product }}">{{ column_product }}</a>
                        {% endif %}</td>
                      <td class="text-left">{% if sort == 'order_id' %}
                        <a href="{{ sort_order_id }}" class="{{ order|lower }}">{{ column_order_id }}</a>
                        {% else %}
                        <a href="{{ sort_order_id }}">{{ column_order_id }}</a>
                        {% endif %}</td>
                      <td class="text-left">{{ column_warranty_type }}</td>
                      <td class="text-left">{{ column_warranty_period }}</td>
                      <td class="text-left">{% if sort == 'start_date' %}
                        <a href="{{ sort_start_date }}" class="{{ order|lower }}">{{ column_start_date }}</a>
                        {% else %}
                        <a href="{{ sort_start_date }}">{{ column_start_date }}</a>
                        {% endif %}</td>
                      <td class="text-left">{% if sort == 'end_date' %}
                        <a href="{{ sort_end_date }}" class="{{ order|lower }}">{{ column_end_date }}</a>
                        {% else %}
                        <a href="{{ sort_end_date }}">{{ column_end_date }}</a>
                        {% endif %}</td>
                      <td class="text-left">{% if sort == 'status' %}
                        <a href="{{ sort_status }}" class="{{ order|lower }}">{{ column_status }}</a>
                        {% else %}
                        <a href="{{ sort_status }}">{{ column_status }}</a>
                        {% endif %}</td>
                      <td class="text-right">{{ column_action }}</td>
                    </tr>
                  </thead>
                  <tbody id="warranties-tbody">
                    <!-- سيتم تحميل البيانات عبر AJAX -->
                  </tbody>
                </table>
              </div>
            </form>
            <div class="row">
              <div class="col-sm-6 text-left" id="pagination">
                <!-- سيتم تحميل الترقيم عبر AJAX -->
              </div>
              <div class="col-sm-6 text-right" id="results">
                <!-- سيتم تحميل النتائج عبر AJAX -->
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript">
$(document).ready(function() {
    loadWarranties();
});

$('#button-filter').on('click', function() {
    loadWarranties();
});

function loadWarranties(page = 1) {
    var filter_data = {
        filter_warranty_number: $('input[name=\'filter_warranty_number\']').val(),
        filter_customer: $('input[name=\'filter_customer\']').val(),
        filter_product: $('input[name=\'filter_product\']').val(),
        filter_status: $('select[name=\'filter_status\']').val(),
        filter_date_added: $('input[name=\'filter_date_added\']').val(),
        start: (page - 1) * 20,
        limit: 20
    };
    
    $.ajax({
        url: 'index.php?route=service/warranty/getList&user_token={{ user_token }}',
        type: 'GET',
        data: filter_data,
        dataType: 'json',
        success: function(json) {
            var html = '';
            
            if (json.warranties && json.warranties.length > 0) {
                $.each(json.warranties, function(i, warranty) {
                    html += '<tr>';
                    html += '<td class="text-center"><input type="checkbox" name="selected[]" value="' + warranty.warranty_id + '" /></td>';
                    html += '<td class="text-left">' + warranty.warranty_number + '</td>';
                    html += '<td class="text-left">' + warranty.customer_name + '</td>';
                    html += '<td class="text-left">' + warranty.product_name + '</td>';
                    html += '<td class="text-left">' + warranty.order_id + '</td>';
                    html += '<td class="text-left">' + warranty.warranty_type + '</td>';
                    html += '<td class="text-left">' + warranty.warranty_period + '</td>';
                    html += '<td class="text-left">' + warranty.start_date + '</td>';
                    html += '<td class="text-left">' + warranty.end_date + '</td>';
                    html += '<td class="text-left"><span class="label label-' + getStatusClass(warranty.status) + '">' + warranty.status_text + '</span></td>';
                    html += '<td class="text-right">';
                    html += '<a href="' + warranty.view + '" data-toggle="tooltip" title="{{ button_view }}" class="btn btn-primary"><i class="fa fa-eye"></i></a> ';
                    html += '<a href="' + warranty.edit + '" data-toggle="tooltip" title="{{ button_edit }}" class="btn btn-info"><i class="fa fa-pencil"></i></a>';
                    html += '</td>';
                    html += '</tr>';
                });
            } else {
                html = '<tr><td class="text-center" colspan="11">{{ text_no_results }}</td></tr>';
            }
            
            $('#warranties-tbody').html(html);
        },
        error: function() {
            alert('{{ error_load_failed }}');
        }
    });
}

function getStatusClass(status) {
    switch(status) {
        case 'active':
            return 'success';
        case 'expired':
            return 'warning';
        case 'cancelled':
            return 'danger';
        case 'suspended':
            return 'default';
        default:
            return 'default';
    }
}

$('.date').datetimepicker({
    pickTime: false
});
</script>
{{ footer }} 