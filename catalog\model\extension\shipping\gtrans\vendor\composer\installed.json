[{"name": "firebase/php-jwt", "version": "v5.2.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/firebase/php-jwt.git", "reference": "feb0e820b8436873675fd3aca04f3728eb2185cb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/firebase/php-jwt/zipball/feb0e820b8436873675fd3aca04f3728eb2185cb", "reference": "feb0e820b8436873675fd3aca04f3728eb2185cb", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": ">=4.8 <=9"}, "time": "2020-03-25T18:49:23+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Firebase\\JWT\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A simple library to encode and decode JSON Web Tokens (JWT) in PHP. Should conform to the current spec.", "homepage": "https://github.com/firebase/php-jwt", "keywords": ["jwt", "php"]}, {"name": "google/auth", "version": "v1.13.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/googleapis/google-auth-library-php.git", "reference": "173191f5defd1d9ae8bdfc28da31b63eb73dd34e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/googleapis/google-auth-library-php/zipball/173191f5defd1d9ae8bdfc28da31b63eb73dd34e", "reference": "173191f5defd1d9ae8bdfc28da31b63eb73dd34e", "shasum": ""}, "require": {"firebase/php-jwt": "~2.0|~3.0|~4.0|~5.0", "guzzlehttp/guzzle": "^5.3.1|^6.2.1|^7.0", "guzzlehttp/psr7": "^1.2", "php": ">=5.4", "psr/cache": "^1.0", "psr/http-message": "^1.0"}, "require-dev": {"guzzlehttp/promises": "0.1.1|^1.3", "kelvinmo/simplejwt": "^0.2.5", "phpseclib/phpseclib": "^2", "phpunit/phpunit": "^4.8.36|^5.7", "sebastian/comparator": ">=1.2.3", "squizlabs/php_codesniffer": "^3.5"}, "suggest": {"phpseclib/phpseclib": "May be used in place of OpenSSL for signing strings or for token management. Please require version ^2."}, "time": "2020-09-18T20:03:05+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Google\\Auth\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "Google Auth Library for PHP", "homepage": "http://github.com/google/google-auth-library-php", "keywords": ["Authentication", "google", "oauth2"]}, {"name": "google/cloud-core", "version": "v1.39.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/googleapis/google-cloud-php-core.git", "reference": "f9e7421beac89fd7d9006a13a6b39b89dd86c92e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/googleapis/google-cloud-php-core/zipball/f9e7421beac89fd7d9006a13a6b39b89dd86c92e", "reference": "f9e7421beac89fd7d9006a13a6b39b89dd86c92e", "shasum": ""}, "require": {"google/auth": "^1.6", "guzzlehttp/guzzle": "^5.3|^6.0|^7.0", "guzzlehttp/promises": "^1.3", "guzzlehttp/psr7": "^1.2", "monolog/monolog": "^1.1|^2.0", "php": ">=5.5", "psr/http-message": "1.0.*", "rize/uri-template": "~0.3"}, "require-dev": {"erusev/parsedown": "^1.6", "google/common-protos": "^1.0", "google/gax": "^1.1", "opis/closure": "^3", "phpdocumentor/reflection": "^3.0", "phpunit/phpunit": "^4.8|^5.0", "squizlabs/php_codesniffer": "2.*"}, "suggest": {"opis/closure": "May be used to serialize closures to process jobs in the batch daemon. Please require version ^3.", "symfony/lock": "Required for the Spanner cached based session pool. Please require the following commit: 3.3.x-dev#1ba6ac9"}, "time": "2020-09-08T20:52:20+00:00", "bin": ["bin/google-cloud-batch"], "type": "library", "extra": {"component": {"id": "cloud-core", "target": "googleapis/google-cloud-php-core.git", "path": "Core", "entry": "src/ServiceBuilder.php"}}, "installation-source": "dist", "autoload": {"psr-4": {"Google\\Cloud\\Core\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "Google Cloud PHP shared dependency, providing functionality useful to all components."}, {"name": "google/cloud-translate", "version": "v1.9.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/googleapis/google-cloud-php-translate.git", "reference": "a64e1beef71d71b7082ca6f23a93a5c532ceea9c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/googleapis/google-cloud-php-translate/zipball/a64e1beef71d71b7082ca6f23a93a5c532ceea9c", "reference": "a64e1beef71d71b7082ca6f23a93a5c532ceea9c", "shasum": ""}, "require": {"google/cloud-core": "^1.39", "google/gax": "^1.1"}, "require-dev": {"erusev/parsedown": "^1.6", "phpdocumentor/reflection": "^3.0", "phpunit/phpunit": "^4.8|^5.0", "squizlabs/php_codesniffer": "2.*"}, "suggest": {"ext-grpc": "The gRPC extension enables use of the performant gRPC transport", "ext-protobuf": "Provides a significant increase in throughput over the pure PHP protobuf implementation. See https://cloud.google.com/php/grpc for installation instructions."}, "time": "2020-09-08T20:52:20+00:00", "type": "library", "extra": {"component": {"id": "cloud-translate", "target": "googleapis/google-cloud-php-translate.git", "path": "Translate", "entry": "src/TranslateClient.php"}}, "installation-source": "dist", "autoload": {"psr-4": {"Google\\Cloud\\Translate\\": "src", "GPBMetadata\\Google\\Cloud\\Translate\\": "metadata"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "Cloud Translation Client for PHP"}, {"name": "google/common-protos", "version": "1.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/googleapis/common-protos-php.git", "reference": "535f489ff1c3433c0ea64cd5aa0560f926949ac5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/googleapis/common-protos-php/zipball/535f489ff1c3433c0ea64cd5aa0560f926949ac5", "reference": "535f489ff1c3433c0ea64cd5aa0560f926949ac5", "shasum": ""}, "require": {"google/protobuf": "^3.6.1"}, "require-dev": {"phpunit/phpunit": "^4.8.36", "sami/sami": "*"}, "time": "2020-08-26T16:05:09+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Google\\": "src", "GPBMetadata\\Google\\": "metadata"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "Google API Common Protos for PHP", "homepage": "https://github.com/googleapis/common-protos-php", "keywords": ["google"]}, {"name": "google/gax", "version": "1.4.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/googleapis/gax-php.git", "reference": "63ea951e70557bb851bf6b487d65698410a6aa96"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/googleapis/gax-php/zipball/63ea951e70557bb851bf6b487d65698410a6aa96", "reference": "63ea951e70557bb851bf6b487d65698410a6aa96", "shasum": ""}, "require": {"google/auth": "^1.2.0", "google/common-protos": "^1.0", "google/grpc-gcp": "^0.1.0", "google/protobuf": "^3.12.2", "grpc/grpc": "^1.13", "guzzlehttp/promises": "^1.3", "guzzlehttp/psr7": "^1.2", "php": ">=5.5"}, "conflict": {"ext-protobuf": "<3.7.0"}, "require-dev": {"phpunit/phpunit": "^4.8.36", "sami/sami": "*", "squizlabs/php_codesniffer": "3.*"}, "time": "2020-09-16T17:29:29+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Google\\ApiCore\\": "src", "GPBMetadata\\Google\\": "metadata"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Google API Core for PHP", "homepage": "https://github.com/googleapis/gax-php", "keywords": ["google"]}, {"name": "google/grpc-gcp", "version": "0.1.5", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/GoogleCloudPlatform/grpc-gcp-php.git", "reference": "bb9bdbf62f6ae4e73d5209d85b1d0a0b9855ff36"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/GoogleCloudPlatform/grpc-gcp-php/zipball/bb9bdbf62f6ae4e73d5209d85b1d0a0b9855ff36", "reference": "bb9bdbf62f6ae4e73d5209d85b1d0a0b9855ff36", "shasum": ""}, "require": {"google/auth": "^1.3", "google/protobuf": "^v3.3.0", "grpc/grpc": "^v1.13.0", "php": ">=5.5.0", "psr/cache": "^1.0.1"}, "require-dev": {"google/cloud-spanner": "^1.7", "phpunit/phpunit": "4.8.36"}, "time": "2020-05-26T17:21:09+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Grpc\\Gcp\\": "src/"}, "classmap": ["src/generated/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "gRPC GCP library for channel management"}, {"name": "google/protobuf", "version": "v3.13.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/protocolbuffers/protobuf-php.git", "reference": "fddc6c2439b190284f207143f6d37bf5b651cea6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/protocolbuffers/protobuf-php/zipball/fddc6c2439b190284f207143f6d37bf5b651cea6", "reference": "fddc6c2439b190284f207143f6d37bf5b651cea6", "shasum": ""}, "require": {"php": ">=5.5.0"}, "require-dev": {"phpunit/phpunit": ">=4.8.0"}, "suggest": {"ext-bcmath": "Need to support JSON deserialization"}, "time": "2020-08-15T00:44:58+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Google\\Protobuf\\": "src/Google/Protobuf", "GPBMetadata\\Google\\Protobuf\\": "src/GPBMetadata/Google/Protobuf"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "proto library for PHP", "homepage": "https://developers.google.com/protocol-buffers/", "keywords": ["proto"]}, {"name": "grpc/grpc", "version": "1.30.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/grpc/grpc-php.git", "reference": "31952d18884d91c674b73f8b4da831f708706f20"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/grpc/grpc-php/zipball/31952d18884d91c674b73f8b4da831f708706f20", "reference": "31952d18884d91c674b73f8b4da831f708706f20", "shasum": ""}, "require": {"php": ">=5.5.0"}, "require-dev": {"google/auth": "^v1.3.0"}, "suggest": {"ext-protobuf": "For better performance, install the protobuf C extension.", "google/protobuf": "To get started using grpc quickly, install the native protobuf library."}, "time": "2020-06-23T01:49:02+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Grpc\\": "src/lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "gRPC library for PHP", "homepage": "https://grpc.io", "keywords": ["rpc"]}, {"name": "guzzlehttp/guzzle", "version": "7.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "2d9d3c186a6637a43193e66b097c50e4451eaab2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/2d9d3c186a6637a43193e66b097c50e4451eaab2", "reference": "2d9d3c186a6637a43193e66b097c50e4451eaab2", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.0", "guzzlehttp/psr7": "^1.6.1", "php": "^7.2.5", "psr/http-client": "^1.0"}, "provide": {"psr/http-client-implementation": "1.0"}, "require-dev": {"ergebnis/composer-normalize": "^2.0", "ext-curl": "*", "php-http/client-integration-tests": "dev-phpunit8", "phpunit/phpunit": "^8.5.5", "psr/log": "^1.1"}, "suggest": {"ext-curl": "Required for CURL handler support", "ext-intl": "Required for Internationalized Domain Name (IDN) support", "psr/log": "Required for using the Log middleware"}, "time": "2020-06-27T10:33:25+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "7.0-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"GuzzleHttp\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "Guzzle is a PHP HTTP client library", "homepage": "http://guzzlephp.org/", "keywords": ["client", "curl", "framework", "http", "http client", "psr-18", "psr-7", "rest", "web service"]}, {"name": "guzzlehttp/promises", "version": "v1.3.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "a59da6cf61d80060647ff4d3eb2c03a2bc694646"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/a59da6cf61d80060647ff4d3eb2c03a2bc694646", "reference": "a59da6cf61d80060647ff4d3eb2c03a2bc694646", "shasum": ""}, "require": {"php": ">=5.5.0"}, "require-dev": {"phpunit/phpunit": "^4.0"}, "time": "2016-12-20T10:07:11+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle promises library", "keywords": ["promise"]}, {"name": "guzzlehttp/psr7", "version": "1.6.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "239400de7a173fe9901b9ac7c06497751f00727a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/239400de7a173fe9901b9ac7c06497751f00727a", "reference": "239400de7a173fe9901b9ac7c06497751f00727a", "shasum": ""}, "require": {"php": ">=5.4.0", "psr/http-message": "~1.0", "ralouphie/getallheaders": "^2.0.5 || ^3.0.0"}, "provide": {"psr/http-message-implementation": "1.0"}, "require-dev": {"ext-zlib": "*", "phpunit/phpunit": "~4.8.36 || ^5.7.27 || ^6.5.8"}, "suggest": {"zendframework/zend-httphandlerrunner": "Emit PSR-7 responses"}, "time": "2019-07-01T23:21:34+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.6-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "homepage": "https://github.com/Tobion"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"]}, {"name": "monolog/monolog", "version": "2.1.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "f9eee5cec93dfb313a38b6b288741e84e53f02d5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/f9eee5cec93dfb313a38b6b288741e84e53f02d5", "reference": "f9eee5cec93dfb313a38b6b288741e84e53f02d5", "shasum": ""}, "require": {"php": ">=7.2", "psr/log": "^1.0.1"}, "provide": {"psr/log-implementation": "1.0.0"}, "require-dev": {"aws/aws-sdk-php": "^2.4.9 || ^3.0", "doctrine/couchdb": "~1.0@dev", "elasticsearch/elasticsearch": "^6.0", "graylog2/gelf-php": "^1.4.2", "php-amqplib/php-amqplib": "~2.4", "php-console/php-console": "^3.1.3", "php-parallel-lint/php-parallel-lint": "^1.0", "phpspec/prophecy": "^1.6.1", "phpunit/phpunit": "^8.5", "predis/predis": "^1.1", "rollbar/rollbar": "^1.3", "ruflin/elastica": ">=0.90 <3.0", "swiftmailer/swiftmailer": "^5.3|^6.0"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "elasticsearch/elasticsearch": "Allow sending log messages to an Elasticsearch server via official client", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-mbstring": "Allow to work properly with unicode symbols", "ext-mongodb": "Allow sending log messages to a MongoDB server (via driver)", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server (via library)", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "php-console/php-console": "Allow sending log messages to Google Chrome", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server"}, "time": "2020-07-23T08:41:23+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "http://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "funding": [{"url": "https://github.com/Seldaek", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/monolog/monolog", "type": "tidelift"}]}, {"name": "psr/cache", "version": "1.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/d11b50ad223250cf17b86e38383413f5a6764bf8", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8", "shasum": ""}, "require": {"php": ">=5.3.0"}, "time": "2016-08-06T20:24:11+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"]}, {"name": "psr/http-client", "version": "1.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "2dfb5f6c5eff0e91e20e913f8c5452ed95b86621"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/2dfb5f6c5eff0e91e20e913f8c5452ed95b86621", "reference": "2dfb5f6c5eff0e91e20e913f8c5452ed95b86621", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0"}, "time": "2020-06-29T06:28:15+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"]}, {"name": "psr/http-message", "version": "1.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/f6561bf28d520154e4b0ec72be95418abe6d9363", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363", "shasum": ""}, "require": {"php": ">=5.3.0"}, "time": "2016-08-06T14:39:51+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"]}, {"name": "psr/log", "version": "1.1.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "0f73288fd15629204f9d42b7055f72dacbe811fc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/0f73288fd15629204f9d42b7055f72dacbe811fc", "reference": "0f73288fd15629204f9d42b7055f72dacbe811fc", "shasum": ""}, "require": {"php": ">=5.3.0"}, "time": "2020-03-23T09:12:05+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"]}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "time": "2019-03-08T08:55:37+00:00", "type": "library", "installation-source": "dist", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders."}, {"name": "rize/uri-template", "version": "0.3.2", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/rize/UriTemplate.git", "reference": "9e5fdd5c47147aa5adf7f760002ee591ed37b9ca"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/rize/UriTemplate/zipball/9e5fdd5c47147aa5adf7f760002ee591ed37b9ca", "reference": "9e5fdd5c47147aa5adf7f760002ee591ed37b9ca", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "~4.0.0"}, "time": "2017-06-14T03:57:53+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-0": {"Rize\\UriTemplate": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>ut <PERSON>", "homepage": "http://twitter.com/rezigned"}], "description": "PHP URI Template (RFC 6570) supports both expansion & extraction", "keywords": ["RFC 6570", "template", "uri"]}]