### **الدستور الشامل النهائي لنظام AYM ERP - الإصدار 5.0**

**التاريخ:** 19/7/2025
**الغرض:** الدليل المرجعي الكامل والنهائي للمطورين، لضمان بناء نظام Enterprise Grade Plus يتفوق على المنافسين العالميين.
**الحالة:** ✅ نسخة نهائية شاملة ووافية.

---

### **🏛️ مقدمة: ما هو نظام AYM ERP؟**

**AYM ERP** ليس مجرد نظام تخطيط موارد مؤسسات عادي، بل هو **منصة متكاملة تهدف إلى منافسة كبرى الأنظمة العالمية** مثل SAP و Oracle، مع **تخصيص عميق لتلبية احتياجات السوق المصري** والشركات التجارية على وجه الخصوص.

*   **المعمارية التقنية:** مبني على أساس OpenCart 3.x المتقدمة (MVC + AJAX + Twig)، لكن مع تعديلات جوهرية وتحسينات عميقة تجعله نظامًا من فئة **Enterprise Grade Plus**.
*   **الرؤية النهائية:** جعل AYM ERP النظام الأول في مصر والشرق الأوسط، والمنافس الأقوى عالميًا في قطاع الشركات التجارية.

---

### **الجزء الأول: الفلسفة الحاكمة (الدستور الشامل)**

هذه هي المنهجية الإلزامية التي تضمن الجودة والأمان وقابلية التوسع في كل شاشة يتم تطويرها.

#### **🎯 الهدف الأسمى لكل شاشة:**

الوصول إلى تقييم **⭐⭐⭐⭐⭐ Enterprise Grade Plus**، مما يعني التفوق على المنافسين العالميين في سهولة الاستخدام، القوة التحليلية، والتوافق المحلي.

#### **📊 المنهجية الإلزامية - الخطوات السبع:**

1.  **الفهم الوظيفي العميق:** ما وظيفة الشاشة؟ وما علاقتها بالأنظمة المعقدة الأخرى (المخزون، المحاسبة، ETA)؟
2.  **فحص الترابطات (MVC) الكامل:** تحليل الـ Controller، Model، View، و Language وفق معايير صارمة تشمل استخدام الخدمات المركزية والصلاحيات المزدوجة.
3.  **اكتشاف التكرار والتداخل:** البحث عن أي كود أو وظائف مكررة لتوحيدها.
4.  **التحسين التقني المتقدم:** التأكد من تطبيق الركائز المعمارية الأساسية للنظام.
5.  **التوافق مع السوق المصري:** التأكد من تلبية المتطلبات القانونية والمحاسبية المحلية.
6.  **تقييم التعقيد والمخاطر:** تحديد مستوى تعقيد الشاشة والمخاطر المحتملة عند تعديلها.
7.  **خطة التطوير والتنفيذ:** وضع خطة عمل واضحة ومحددة الأولويات.

---

### **الجزء الثاني: الركائز المعمارية الحرجة (ما يجب فهمه قبل كتابة سطر كود واحد)**

⚠️ **تحذير مهم:** تجاهل هذه الركائز يؤدي حتمًا إلى فشل النظام أو خلق ثغرات أمنية خطيرة!

#### **1️⃣ الخدمات المركزية الخمس (Central Services)**

*   **المشكلة المكتشفة:** كان هناك 157 دالة متخصصة في ملف `central_service_manager.php` لكنها كانت غير مستخدمة.
*   **الحل الإلزامي:** **يجب** على كل Controller استدعاء واستخدام هذا المدير المركزي. إنه الواجهة الموحدة الإجبارية للعمليات الأساسية.
*   **الخدمات الخمس هي:**
    1.  **📝 التسجيل والتدقيق (Logging):** تسجيل كل حركة في النظام.
    2.  **🔔 الإشعارات (Notifications):** إرسال تنبيهات تلقائية للمستخدمين.
    3.  **💬 التواصل الداخلي (Communication):** نظام رسائل وإعلانات.
    4.  **📎 المستندات والمرفقات (Documents):** نظام موحد لإدارة الملفات بـ 7 جداول مرتبطة.
    5.  **⚙️ محرر سير العمل (Workflow Engine):** نظام مرئي شبيه بـ n8n لأتمتة العمليات.
*   **مثال للتطبيق:**
    ```php
    // كل controller يجب أن يبدأ بهذا
    $this->load->model('core/central_service_manager');
    $this.central_service = $this->model_core_central_service_manager;

    // مثال للتسجيل
    $this->central_service->logActivity(
        'update', 
        'product', 
        'تم تحديث المنتج', 
        ['id' => $product_id]
    );
    ```
-- مفترض يتسجل رسالة واضحة مين قام بايه بوقت ايه 
#### **2️⃣ نظام الصلاحيات المزدوج (Dual Permissions)**

*   **المفهوم:** النظام لا يعتمد على صلاحيات عامة فقط، بل هناك مستويان متكاملان:
    *   **`$this->user->hasPermission()`:** للصلاحيات العامة على الشاشات (عرض، تعديل، حذف).
    *   **`$this->user->hasKey()`:** للصلاحيات المتقدمة والمخصصة لعمليات دقيقة (مثل: الموافقة على طلبات تتجاوز 10,000 جنيه، تعديل أسعار التكلفة).
*   **الأهمية الحرجة:** هذا النظام يوفر حماية وأمان على مستوى Enterprise. **تجاهل `hasKey` يعني ترك ثغرات أمنية خطيرة!**

#### **3️⃣ نظام المخزون المعقد (The Complex Inventory)**

*   **أكبر تحدي وتعقيد في النظام.** يجب فهمه بعمق قبل أي تعديل.
*   **👻 المخزون الوهمي والفعلي:** النظام يفصل بذكاء بين المخزون المتاح للبيع أونلاين (`quantity_available` وهو يمكن أن يكون أكبر من الفعلي للسماح بالبيع قبل الشراء) والمخزون الموجود فعليًا في المستودعات (`quantity`).
*   **⚖️ المتوسط المرجح للتكلفة (WAC):** كل العمليات المخزنية والمحاسبية يجب أن تطبق هذا المبدأ بدقة.
*   **📦 وحدات متعددة (Multi-unit):** يدعم النظام التحويل التلقائي بين الوحدات (قطعة، دستة، كرتونة) مع أسعار مختلفة لكل وحدة. ويمكن ان تكون الوحدات مستقله .
*   **🏗️ الهيكل الشجري للمستودعات:** المستودعات منظمة بشكل هرمي (مستودع رئيسي > منطقة > ممر > رف).
*   **🏢 الربط بالفروع:** كل موظف (خاصة الكاشير في نقاط البيع) يصل ويبيع فقط من مخزون فرعه المحدد.

#### **4️⃣ ميزات التجارة الإلكترونية المتقدمة (Competitive Edge)**

*   **🚀 نظام الطلب السريع (`header.twig`):** ميزة تنافسية استثنائية تسمح للعميل بإتمام الطلب من أي مكان في النظام. شديدة التعقيد ويجب الحذر الشديد عند التعامل معها.
*   **🎯 نظام المنتجات المتقدم (`ProductsPro`):** يسمح بإنشاء مودلات منتجات معقدة، وباقات ديناميكية، وإعدادات متقدمة للأسعار والوحدات.
وهي واضحة بدقة ايضا في صفحة المنتج product.twig فراجعها
فهمك لصفحة المنتج سيوضح لك حاجتنا لفصل وحدة ادارة التجارة الالكترونية المرتبطة بالمتجر عن ادارة المخزون من مدير المخزن او امين المخزن  مع العلم السيستم يدعم مخازن متعددة وفروع متعددة من cod_branch وتوضيح اين هو المخزون بدقة ايضا وهناك جداول كثيرة

#### **5️⃣ الفجوة التقنية المكتشفة (The Technical Gap)**

*   **المشكلة الحرجة:** النظام يعاني من انقسام تقني خطير. الواجهات الأمامية (Front-end) حديثة ومتطورة جدًا، بينما بعض الأنظمة الخلفية (Back-end) و الـ API متخلفة ولا تدعم الميزات المتقدمة (مثل الوحدات المتعددة والباقات).
*   **الحل الإلزامي:** الأولوية القصوى هي تطوير وتأمين الـ API ليدعم كل الميزات المتقدمة.

---

### **الجزء الثالث: الدستور المحاسبي المتكامل**

*   **مبدأ القيد المزدوج الإلزامي:** كل عملية مالية يجب أن تولد قيدًا محاسبيًا مزدوجًا ومتوازنًا.
*   **مبدأ عدم القابلية للتعديل:** القيود المعتمدة لا تُعدل، بل يتم عكسها بقيد جديد لضمان سلامة السجلات المحاسبية ونراعي عند الحذف أن يكون للمسودات فقط وقبل الترحيل والموافقة.
*   **هيكل الحسابات المعياري:** النظام يتبع هيكلًا خماسيًا (أصول، خصوم، حقوق ملكية، إيرادات، مصروفات) يضاهي أنظمة SAP.

---

### **الجزء الرابع: الإنجازات والوضع الحالي**

*   **🧮 النظام المحاسبي (مكتمل 100%):** تم تطوير 14 شاشة محاسبية أساسية لتصل إلى جودة ⭐⭐⭐⭐⭐ Enterprise Grade Plus وتتفوق على المنافسين.
*   **📦 نظام المخزون (تقدم كبير):** تم تطوير أهم شاشاته (إدارة المستودعات، التسويات، التحويلات) لتصل لجودة ⭐⭐⭐⭐⭐.
*   **📊 لوحة المؤشرات (تصحيح جذري):** تم تحويلها من 134 مؤشر غير مناسب إلى 213 مؤشر جديد، منها 140 مصمم خصيصًا للشركات التجارية في مصر.

---

### **الجزء الخامس: خطة العمل والأولويات للمطور الجديد**

مهمتك هي استكمال المسيرة بنفس المنهجية والجودة. إليك خريطة الطريق:

#### **📚 الخطوة (0): الإعداد الإلزامي:**

1.  **قراءة هذا الدليل بالكامل** وفهمه بعمق.
2.  **استيعاب الركائز المعمارية الحرجة جيدًا.**
3.  **مراجعة الأمثلة المكتملة** (`chartaccount.php`, `warehouse.php`) كمرجع للجودة المطلوبة.

#### **🔥 الأولويات الحرجة (يجب العمل عليها فورًا):**

1.  **🔴 تأمين وتطوير الـ API:**
    *   **المهمة:** تطوير طبقة أمان شاملة للـ API (باستخدام OAuth 2.0/JWT) وتحديثه ليدعم كل تعقيدات النظام (الوحدات المتعددة، الباقات، ...). **هذه هي الأولوية القصوى لتجنب المخاطر الأمنية والتشغيلية.**

2.  **🔴 التكامل مع الفاتورة الإلكترونية المصرية (ETA):**
    *   **المهمة:** إتمام التكامل مع ETA SDK. **هذا التزام قانوني حتمي.**

3.  **🟡 استكمال تطوير باقي وحدات النظام:**
    *   **المهمة:** تطبيق "الدستور الشامل" على باقي الشاشات في وحدات **(المخزون، المبيعات، المشتريات، نقاط البيع).**

4.  **🟡 حل التكرارات وتوحيد الأكواد:**
    *   **المهمة:** دمج الملفات المكررة (مثال: `statement_account.php` و `statementaccount.php`).
لكن فهم الاكواد يجعلنا نخرج باصدار فائق الجودة
5. منفتحين على تحسين قاعدة البيانات لكن بشرط ان لا تكفي الجداول .. سنقوم بعمل ملف .sql يوضح الاستعلامات اللازمة للتطوير
---

### **الجزء السادس: أخطاء قاتلة يجب تجنبها**

*   **❌ تجاهل الخدمات المركزية:** سيؤدي إلى كسر نظام التدقيق والإشعارات.
*   **❌ استخدام أرقام ثابتة (Hardcoding):** استخدم دائمًا `$this->config->get()`.
*   **❌ تجاهل نظام الصلاحيات المزدوج:** سيخلق ثغرات أمنية فادحة.
*   **❌ التعامل بسطحية مع نظام المخزون:** سيؤدي إلى كسر العمليات التجارية للعميل.
*   **❌ تعديل `header.twig` دون فهم كامل:** قد يؤدي إلى كسر أهم ميزة تنافسية في النظام.
* ** ضروري تفهم product.twig وكيفية اتمام الطلب بالواجهة (catalog) 
---

### **الجزء السابع: الرؤية المستقبلية والميزات المتقدمة**

#### **🤖 الذكاء الاصطناعي المحاسبي:**

*   **اقتراح القيود:** تلقائيًا حسب نوع العملية.
*   **كشف الأخطاء:** تحليل ذكي للانحرافات.
*   **التنبؤ المالي:** توقع التدفقات النقدية.
*   **التحليل الذكي:** استخراج رؤى من البيانات.

#### **📱 التطبيق المحمول:**

*   **الموافقات الفورية:** للقيود وطلبات الشراء من أي مكان.
*   **استعلام الأرصدة:** فوري ومحدث.
*   **التقارير السريعة:** على الهاتف.
*   **الإشعارات:** تنبيهات فورية بالعمليات الهامة.

#### **🌐 التكامل الخارجي:**

*   **البنوك:** ربط مباشر مع كشوف الحساب للتسويات الآلية.
*   **الضرائب:** تقارير تلقائية لمصلحة الضرائب.
*   **التأمينات:** تقارير شهرية تلقائية.

---

### **الجزء الثامن: ملحق الشاشات والميزات**

هذا القسم هو مرجع سريع للشاشات الرئيسية المؤكد وجودها في النظام، مرتبة حسب الوحدات المنطقية النهائية التي نطمح للوصول إليها.

#### **1. المبيعات وإدارة علاقات العملاء (Sales & CRM):**

*   **نقاط البيع (POS):** واجهة الكاشير، إدارة المناوبات والتسويات، إدارة الأجهزة، تقارير POS.
*   **عمليات المبيعات:** أوامر البيع، المرتجعات، الكوبونات، التسعير الديناميكي، البيع بالتقسيط، تحليل السلات المهجورة.
*   **العملاء (CRM):** إدارة العملاء، المجموعات، الحقول المخصصة، برامج الولاء، العملاء المحتملون (Leads)، الصفقات (Deals)، والفرص (Opportunities).

#### **2. المشتريات والموردين (Purchasing & Suppliers):**

*   **دورة الشراء الكاملة:** طلبات الشراء، أوامر الشراء، استلام البضائع، فواتير الموردين، مرتجعات المشتريات.
*   **إدارة الموردين:** بيانات الموردين، تقييمهم، كشوف حساباتهم.
*   **شاشات متقدمة:** مقارنة عروض الأسعار، تخطيط المشتريات، إدارة الجودة، عقود الموردين.

#### **3. المخزون والمستودعات (Inventory & Warehouse):**

*   **عمليات المخزون:** الجرد، التسويات، والتحويلات المخزنية.
*   **التكاليف والتتبع:** سجل تكلفة الصنف (WAC)، تتبع تاريخ الصلاحية والدفعات (Lots).
*   **تقارير وتحليلات:** لوحة معلومات المخزون، سجل حركة المخزون، تقرير تقييم المخزون.

#### **4. المحاسبة والمالية (Accounting & Finance):**

*   **المحاسبة الأساسية:** دليل الحسابات، قيود اليومية، كشوف الحسابات، وإغلاق الفترة.
*   **النقدية والبنوك:** التسوية البنكية وإدارة الشيكات والمحافظ الإلكترونية.
*   **الأصول الثابتة والموازنات:** سجل الأصول وحساب الإهلاك، وإعداد الموازنات.
*   **التقارير المالية والضريبية:** قائمة الدخل، الميزانية العمومية، التدفقات النقدية، تقرير ضريبة القيمة المضافة.

#### **5. الفوترة الإلكترونية (مصر - ETA):**

*   **إدارة الفواتير والإشعارات والإيصالات الإلكترونية.**
*   **إدارة أكواد الأصناف وإعدادات الربط مع مصلحة الضرائب.**

#### **6. إدارة المتجر الإلكتروني:**

*   **كتالوج المتجر:** إدارة المنتجات، الفئات، والتقييمات.
*   **التسويق الذكي:** نظام توصيات، حملات ذكية، وتسعير ديناميكي.
*   **إدارة المحتوى (CMS):** الصفحات التعريفية وإدارة المدونة.
*   **شرح خاص بـ product.twig:** هذا الملف هو الواجهة النهائية لعرض المنتج (`View`) للمستخدم. بساطته المرئية تخفي وراءها تعقيدًا هائلاً. فهو نقطة التقاء لعدة أنظمة معقدة:
    *   **يتلقى البيانات من `ProductsPro`** لعرض الوحدات المتعددة، الباقات، والأسعار الديناميكية.
    *   **يتفاعل مع `header.twig`** لإضافة المنتجات للسلة أو الطلب السريع.
    *   **يستعلم من نظام المخزون المعقد** لعرض الكمية المتاحة للبيع (`quantity_available`) وليس الكمية الفعلية.
    *   أي تعديل في هذا الملف يجب أن يراعي جميع هذه الترابطات لضمان عدم كسر أي من هذه الميزات التنافسية.

#### **7. الأنظمة المساندة الأخرى:**

*   **نظام الشحن والتوصيل، الموارد البشرية، إدارة المشاريع، إدارة المستندات، التواصل، الذكاء الاصطناعي، الحوكمة والمخاطر، التقارير، والإعدادات العامة.**

---

### **🎉 الخلاصة النهائية**

*   **ما يجعل AYM ERP مميزاً:**
    1.  **فلسفة تطوير متكاملة** - الدستور الشامل.
    2.  **معمارية متقدمة** - الخدمات المركزية والصلاحيات المزدوجة.
    3.  **نظام محاسبي متفوق** - يتفوق على SAP في السهولة.
    4.  **تخصيص للسوق المصري** - متوافق 100% مع القوانين المحلية.
    5.  **جودة Enterprise Grade Pluse** - معايير عالمية في كل شاشة.