{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="submit" form="form-payment" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fa fa-save"></i></button>
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a></div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    <div class="alert alert-warning"><i class="fa fa-exclamation-circle"></i> {{ text_url_message }}</div>
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pencil"></i> {{ text_edit }}</h3>
      </div>
      <div class="panel-body">
        <form action="{{ action }}" method="post" enctype="multipart/form-data" id="form-payment" class="form-horizontal">
          <ul class="nav nav-tabs" id="tabs">
            <li class="active"><a href="#tab-api" data-toggle="tab">{{ tab_api }}</a></li>
            <li><a href="#tab-account" data-toggle="tab">{{ tab_account }}</a></li>
            <li><a href="#tab-order-status" data-toggle="tab">{{ tab_order_status }}</a></li>
            <li><a href="#tab-payment" data-toggle="tab">{{ tab_payment }}</a></li>
            <li><a href="#tab-advanced" data-toggle="tab">{{ tab_advanced }}</a></li>
          </ul>
          <div class="tab-content">
            <div class="tab-pane active" id="tab-api">
              <div class="form-group required">
                <label class="col-sm-2 control-label" for="input-merchant-id">{{ entry_merchant_id }}</label>
                <div class="col-sm-10">
                  <input type="text" name="payment_realex_merchant_id" value="{{ payment_realex_merchant_id }}" placeholder="{{ entry_merchant_id }}" id="input-merchant-id" class="form-control" />
                  {% if error_merchant_id %}
                    <div class="text-danger">{{ error_merchant_id }}</div>
                  {% endif %}
                </div>
              </div>
              <div class="form-group required">
                <label class="col-sm-2 control-label" for="input-secret">{{ entry_secret }}</label>
                <div class="col-sm-10">
                  <input type="password" name="payment_realex_secret" value="{{ payment_realex_secret }}" placeholder="{{ entry_secret }}" id="input-secret" class="form-control" />
                  {% if error_secret %}
                    <div class="text-danger">{{ error_secret }}</div>
                  {% endif %}
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-secret">{{ entry_rebate_password }}</label>
                <div class="col-sm-10">
                  <input type="password" name="payment_realex_rebate_password" value="{{ payment_realex_rebate_password }}" placeholder="{{ entry_rebate_password }}" id="input-rebate-password" class="form-control" />
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-live-demo">{{ entry_live_demo }}</label>
                <div class="col-sm-10">
                  <select name="payment_realex_live_demo" id="input-live-demo" class="form-control">
                    {% if payment_realex_live_demo %}
                      <option value="1" selected="selected">{{ text_live }}</option>
                      <option value="0">{{ text_demo }}</option>
                    {% else %}
                      <option value="1">{{ text_live }}</option>
                      <option value="0" selected="selected">{{ text_demo }}</option>
                    {% endif %}
                  </select>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-geo-zone">{{ entry_geo_zone }}</label>
                <div class="col-sm-10">
                  <select name="payment_realex_geo_zone_id" id="input-geo-zone" class="form-control">
                    <option value="0">{{ text_all_zones }}</option>
                    {% for geo_zone in geo_zones %}
                      {% if geo_zone.geo_zone_id == payment_realex_geo_zone_id %}
                        <option value="{{ geo_zone.geo_zone_id }}" selected="selected">{{ geo_zone.name }}</option>
                      {% else %}
                        <option value="{{ geo_zone.geo_zone_id }}">{{ geo_zone.name }}</option>
                      {% endif %}
                    {% endfor %}
                  </select>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-debug"><span data-toggle="tooltip" title="{{ help_debug }}">{{ entry_debug }}</span></label>
                <div class="col-sm-10">
                  <select name="payment_realex_debug" id="input-debug" class="form-control">
                    {% if payment_realex_debug %}
                      <option value="1" selected="selected">{{ text_enabled }}</option>
                      <option value="0">{{ text_disabled }}</option>
                    {% else %}
                      <option value="1">{{ text_enabled }}</option>
                      <option value="0" selected="selected">{{ text_disabled }}</option>
                    {% endif %}
                  </select>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-status">{{ entry_status }}</label>
                <div class="col-sm-10">
                  <select name="payment_realex_status" id="input-status" class="form-control">
                    {% if payment_realex_status %}
                      <option value="1" selected="selected">{{ text_enabled }}</option>
                      <option value="0">{{ text_disabled }}</option>
                    {% else %}
                      <option value="1">{{ text_enabled }}</option>
                      <option value="0" selected="selected">{{ text_disabled }}</option>
                    {% endif %}
                  </select>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-total"><span data-toggle="tooltip" title="{{ help_total }}">{{ entry_total }}</span></label>
                <div class="col-sm-10">
                  <input type="text" name="payment_realex_total" value="{{ payment_realex_total }}" placeholder="{{ entry_total }}" id="input-total" class="form-control" />
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-sort-order">{{ entry_sort_order }}</label>
                <div class="col-sm-10">
                  <input type="text" name="payment_realex_sort_order" value="{{ payment_realex_sort_order }}" placeholder="{{ entry_sort_order }}" id="input-sort-order" class="form-control" />
                </div>
              </div>
            </div>
            <div class="tab-pane" id="tab-account">
              <div class="alert alert-warning"><i class="fa fa-exclamation-circle"></i> {{ error_use_select_card }}
                <button type="button" class="close" data-dismiss="alert">&times;</button>
              </div>
              <div class="table-responsive">
                <table class="table table-striped table-bordered table-hover">
                  <thead>
                    <tr>
                      <td class="text-left">{{ text_card_type }}</td>
                      <td class="text-center">{{ text_enabled }}</td>
                      <td class="text-center">{{ text_use_default }}</td>
                      <td class="text-left">{{ text_subaccount }}</td>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td class="text-left">{{ text_card_visa }}</td>
                      <td class="text-center"><input type="checkbox" name="payment_realex_account[visa][enabled]" value="1" {{ payment_realex_account.visa.enabled is defined and payment_realex_account.visa.enabled == 1 ? 'checked="checked"' : '' }}/></td>
                      <td class="text-center"><input type="checkbox" name="payment_realex_account[visa][default]" value="1" {{ payment_realex_account.visa.default is defined and payment_realex_account.visa.default == 1 ? 'checked="checked"' : '' }}/></td>
                      <td class="text-right"><input type="text" name="payment_realex_account[visa][merchant_id]" value="{{ payment_realex_account.visa.merchant_id is defined ? payment_realex_account.visa.merchant_id : '' }}" placeholder="{{ text_subaccount }}" class="form-control" /></td>
                    </tr>
                    <tr>
                      <td class="text-left">{{ text_card_master }}</td>
                      <td class="text-center"><input type="checkbox" name="payment_realex_account[mc][enabled]" value="1" {{ payment_realex_account.mc.enabled is defined and payment_realex_account.mc.enabled == 1 ? 'checked="checked"' : '' }}/></td>
                      <td class="text-center"><input type="checkbox" name="payment_realex_account[mc][default]" value="1" {{ payment_realex_account.mc.default is defined and payment_realex_account.mc.default == 1 ? 'checked="checked"' : '' }}/></td>
                      <td class="text-right"><input type="text" name="payment_realex_account[mc][merchant_id]" value="{{ payment_realex_account.mc.merchant_id is defined ? payment_realex_account.mc.merchant_id : '' }}" placeholder="{{ text_subaccount }}" class="form-control" /></td>
                    </tr>
                    <tr>
                      <td class="text-left">{{ text_card_amex }}</td>
                      <td class="text-center"><input type="checkbox" name="payment_realex_account[amex][enabled]" value="1" {{ payment_realex_account.amex.enabled is defined and payment_realex_account.amex.enabled == 1 ? 'checked="checked"' : '' }}/></td>
                      <td class="text-center"><input type="checkbox" name="payment_realex_account[amex][default]" value="1" {{ payment_realex_account.amex.default is defined and payment_realex_account.amex.default == 1 ? 'checked="checked"' : '' }}/></td>
                      <td class="text-right"><input type="text" name="payment_realex_account[amex][merchant_id]" value="{{ payment_realex_account.amex.merchant_id is defined ? payment_realex_account.amex.merchant_id : '' }}" placeholder="{{ text_subaccount }}" class="form-control" /></td>
                    </tr>
                    <tr>
                      <td class="text-left">{{ text_card_switch }}</td>
                      <td class="text-center"><input type="checkbox" name="payment_realex_account[switch][enabled]" value="1" {{ payment_realex_account.switch.enabled is defined and payment_realex_account.switch.enabled == 1 ? 'checked="checked"' : '' }}/></td>
                      <td class="text-center"><input type="checkbox" name="payment_realex_account[switch][default]" value="1" {{ payment_realex_account.switch.default is defined and payment_realex_account.switch.default == 1 ? 'checked="checked"' : '' }}/></td>
                      <td class="text-right"><input type="text" name="payment_realex_account[switch][merchant_id]" value="{{ payment_realex_account.switch.merchant_id is defined ? payment_realex_account.switch.merchant_id : '' }}" placeholder="{{ text_subaccount }}" class="form-control" /></td>
                    </tr>
                    <tr>
                      <td class="text-left">{{ text_card_laser }}</td>
                      <td class="text-center"><input type="checkbox" name="payment_realex_account[laser][enabled]" value="1" {{ payment_realex_account.laser.enabled is defined and payment_realex_account.laser.enabled == 1 ? 'checked="checked"' : '' }}/></td>
                      <td class="text-center"><input type="checkbox" name="payment_realex_account[laser][default]" value="1" {{ payment_realex_account.laser.default is defined and payment_realex_account.laser.default == 1 ? 'checked="checked"' : '' }}/></td>
                      <td class="text-right"><input type="text" name="payment_realex_account[laser][merchant_id]" value="{{ payment_realex_account.laser.merchant_id is defined ? payment_realex_account.laser.merchant_id : '' }}" placeholder="{{ text_subaccount }}" class="form-control" /></td>
                    </tr>
                    <tr>
                      <td class="text-left">{{ text_card_diners }}</td>
                      <td class="text-center"><input type="checkbox" name="payment_realex_account[diners][enabled]" value="1" {{ payment_realex_account.diners.enabled is defined and payment_realex_account.diners.enabled == 1 ? 'checked="checked"' : '' }}/></td>
                      <td class="text-center"><input type="checkbox" name="payment_realex_account[diners][default]" value="1" {{ payment_realex_account.diners.default is defined and payment_realex_account.diners.default == 1 ? 'checked="checked"' : '' }}/></td>
                      <td class="text-right"><input type="text" name="payment_realex_account[diners][merchant_id]" value="{{ payment_realex_account.diners.merchant_id is defined ? payment_realex_account.diners.merchant_id : '' }}" placeholder="{{ text_subaccount }}" class="form-control" /></td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
            <div class="tab-pane" id="tab-order-status">
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-order-status-success-settled">{{ entry_status_success_settled }}</label>
                <div class="col-sm-10">
                  <select name="payment_realex_order_status_success_settled_id" id="input-order-status-success-settled" class="form-control">
                    {% for order_status in order_statuses %}
                      {% if order_status.order_status_id == payment_realex_order_status_success_settled_id %}
                        <option value="{{ order_status.order_status_id }}" selected="selected">{{ order_status.name }}</option>
                      {% else %}
                        <option value="{{ order_status.order_status_id }}">{{ order_status.name }}</option>
                      {% endif %}
                    {% endfor %}
                  </select>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-order-status-success-unsettled">{{ entry_status_success_unsettled }}</label>
                <div class="col-sm-10">
                  <select name="payment_realex_order_status_success_unsettled_id" id="input-order-status-success-unsettled" class="form-control">
                    {% for order_status in order_statuses %}
                      {% if order_status.order_status_id == payment_realex_order_status_success_unsettled_id %}
                        <option value="{{ order_status.order_status_id }}" selected="selected">{{ order_status.name }}</option>
                      {% else %}
                        <option value="{{ order_status.order_status_id }}">{{ order_status.name }}</option>
                      {% endif %}
                    {% endfor %}
                  </select>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-order-status-decline">{{ entry_status_decline }}</label>
                <div class="col-sm-10">
                  <select name="payment_realex_order_status_decline_id" id="input-order-status-decline" class="form-control">
                    {% for order_status in order_statuses %}
                      {% if order_status.order_status_id == payment_realex_order_status_decline_id %}
                        <option value="{{ order_status.order_status_id }}" selected="selected">{{ order_status.name }}</option>
                      {% else %}
                        <option value="{{ order_status.order_status_id }}">{{ order_status.name }}</option>
                      {% endif %}
                    {% endfor %}
                  </select>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-order-status-decline-pending">{{ entry_status_decline_pending }}</label>
                <div class="col-sm-10">
                  <select name="payment_realex_order_status_decline_pending_id" id="input-order-status-decline-pending" class="form-control">
                    {% for order_status in order_statuses %}
                      {% if order_status.order_status_id == payment_realex_order_status_decline_pending_id %}
                        <option value="{{ order_status.order_status_id }}" selected="selected">{{ order_status.name }}</option>
                      {% else %}
                        <option value="{{ order_status.order_status_id }}">{{ order_status.name }}</option>
                      {% endif %}
                    {% endfor %}
                  </select>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-order-status-decline-stolen">{{ entry_status_decline_stolen }}</label>
                <div class="col-sm-10">
                  <select name="payment_realex_order_status_decline_stolen_id" id="input-order-status-decline-stolen" class="form-control">
                    {% for order_status in order_statuses %}
                      {% if order_status.order_status_id == payment_realex_order_status_decline_stolen_id %}
                        <option value="{{ order_status.order_status_id }}" selected="selected">{{ order_status.name }}</option>
                      {% else %}
                        <option value="{{ order_status.order_status_id }}">{{ order_status.name }}</option>
                     {% endif %}
                   {% endfor %}
                  </select>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-order-status-decline-bank">{{ entry_status_decline_bank }}</label>
                <div class="col-sm-10">
                  <select name="payment_realex_order_status_decline_bank_id" id="input-order-status-decline-bank" class="form-control">
                    {% for order_status in order_statuses %}
                      {% if order_status.order_status_id == payment_realex_order_status_decline_bank_id %}
                        <option value="{{ order_status.order_status_id }}" selected="selected">{{ order_status.name }}</option>
                      {% else %}
                        <option value="{{ order_status.order_status_id }}">{{ order_status.name }}</option>
                      {% endif %}
                    {% endfor %}
                  </select>
                </div>
              </div>
            </div>
            <div class="tab-pane" id="tab-payment">
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-auto-settle"><span data-toggle="tooltip" title="{{ help_dcc_settle }}">{{ entry_auto_settle }}</span></label>
                <div class="col-sm-10">
                  <select name="payment_realex_auto_settle" id="input-auto-settle" class="form-control">
                    <option value="0"{{ payment_realex_auto_settle == 0 ? ' selected="selected"' : '' }}>{{ text_settle_delayed }}</option>
                    <option value="1"{{ payment_realex_auto_settle == 1 ? ' selected="selected"' : '' }}>{{ text_settle_auto }}</option>
                    <option value="2"{{ payment_realex_auto_settle == 2 ? ' selected="selected"' : '' }}>{{ text_settle_multi }}</option>
                  </select>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-card-select"><span data-toggle="tooltip" title="{{ help_card_select }}">{{ entry_card_select }}</span></label>
                <div class="col-sm-10">
                  <select name="payment_realex_card_select" id="input-card-select" class="form-control">
                    {% if payment_realex_card_select %}
                      <option value="1" selected="selected">{{ text_yes }}</option>
                      <option value="0">{{ text_no }}</option>
                    {% else %}
                      <option value="1">{{ text_yes }}</option>
                      <option value="0" selected="selected">{{ text_no }}</option>
                    {% endif %}
                  </select>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-tss-check">{{ entry_tss_check }}</label>
                <div class="col-sm-10">
                  <select name="payment_realex_tss_check" id="input-tss-check" class="form-control">
                    {% if payment_realex_tss_check %}
                      <option value="1" selected="selected">{{ text_yes }}</option>
                      <option value="0">{{ text_no }}</option>
                    {% else %}
                      <option value="1">{{ text_yes }}</option>
                      <option value="0" selected="selected">{{ text_no }}</option>
                    {% endif %}
                  </select>
                </div>
              </div>
            </div>
            <div class="tab-pane" id="tab-advanced">
              <div class="form-group required">
                <label class="col-sm-2 control-label" for="input-live-url">{{ entry_live_url }}</label>
                <div class="col-sm-10">
                  <input type="text" name="payment_realex_live_url" value="{{ payment_realex_live_url }}" placeholder="{{ entry_live_url }}" id="input-live-url" class="form-control" />
                  {% if error_live_url %}
                    <div class="text-danger">{{ error_live_url }}</div>
                  {% endif %}
                </div>
              </div>
              <div class="form-group required">
                <label class="col-sm-2 control-label" for="input-demo-url">{{ entry_demo_url }}</label>
                <div class="col-sm-10">
                  <input type="text" name="payment_realex_demo_url" value="{{ payment_realex_demo_url }}" placeholder="{{ entry_demo_url }}" id="input-demo-url" class="form-control" />
                  {% if error_demo_url %}
                    <div class="text-danger">{{ error_demo_url }}</div>
                  {% endif %}
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-sort-order"><span data-toggle="tooltip" title="{{ help_notification }}">{{ entry_notification_url }}</span></label>
                <div class="col-sm-10">
                  <div class="input-group"><span class="input-group-addon"><i class="fa fa-link"></i></span>
                    <input type="text" value="{{ notify_url }}" class="form-control" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
{{ footer }}