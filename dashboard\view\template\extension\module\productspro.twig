{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="submit" form="form-module" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fa fa-save"></i></button>
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a></div>
    </div>
  </div>
  
  <div class="container-fluid">
    <div class="card">
      <div class="card-body">
        <form action="{{ action }}" method="post" enctype="multipart/form-data" id="form-module" class="form-horizontal">

          
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-name">{{ entry_name }}</label>
            <div class="col-sm-10">
              <input type="text" name="name" value="{{ name }}" placeholder="{{ entry_name }}" id="input-name" class="form-control" />
            </div>
          </div>
          
    <div class="form-group">
        <div class="col-sm-12">
            <ul class="nav nav-tabs">
                {% for language in languages %}
                <li{% if loop.first %} class="active"{% endif %}><a href="#language-{{ language.language_id }}" data-toggle="tab">{{ language.name }}</a></li>
                {% endfor %}
            </ul>
            <div class="tab-content">
                {% for language in languages %}
                <div class="tab-pane{% if loop.first %} active{% endif %}" id="language-{{ language.language_id }}">
                    <div class="form-group">
                        <label for="input-title-{{ language.language_id }}" class="col-sm-3 control-label">Title - {{ language.name }}</label>
                        <div class="col-sm-9">
                            <input type="text" name="title[{{ language.language_id }}]" value="{{ title[language.language_id] }}" placeholder="{{ entry_name }}" id="input-name-{{ language.language_id }}" class="form-control"/>
                            <div id="error-title-{{ language.language_id }}" class="help-block"></div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>    
    
   <div class="form-group">
    <label class="col-sm-2 control-label" for="input-type">{{ entry_type }}</label>
    <div class="col-sm-10">
      <select name="type" id="input-type" class="form-control">
      <option value="slider"{% if type == 'slider' %} selected{% endif %}>{{ text_slider }}</option>
      <option value="static"{% if type == 'static' %} selected{% endif %}>{{ text_static }}</option>
      <option value="images"{% if type == 'images' %} selected{% endif %}>Images</option>
      <option value="simages"{% if type == 'simages' %} selected{% endif %}>Images Static</option>
      <option value="modern"{% if type == 'modern' %} selected{% endif %}>Modern</option>
      </select>
    </div>
  </div>
          
 
    <div class="form-group">
    <label class="col-sm-2 control-label" for="input-device">{{ text_device }}</label>
    <div class="col-sm-10">
      <select name="device" id="input-device" class="form-control">
          <option value="mobile"{% if device == 'mobile' %} selected{% endif %}>Mobile</option>
          <option value="pc"{% if device == 'pc' %} selected{% endif %}>PC</option>
          <option value="all"{% if device == 'all' %} selected{% endif %}>ALL</option>              

      </select>
    </div>
  </div>

     <div class="form-group">
    <label class="col-sm-2 control-label" for="input-product-type">{{ source }}</label>
    <div class="col-sm-10">
      <select name="product_type" id="input-product-type" class="form-control">
              <option value="random"{% if product_type == 'random' %} selected{% endif %}>Random</option>
              <option value="bestseller"{% if product_type == 'bestseller' %} selected{% endif %}>Bestseller</option>
              <option value="specials"{% if product_type == 'specials' %} selected{% endif %}>Specials</option>
              <option value="latest"{% if product_type == 'latest' %} selected{% endif %}>Latest</option>
              <option value="bycategories"{% if product_type == 'bycategories' %} selected{% endif %}>By Categories</option>
              <option value="custom"{% if product_type == 'custom' %} selected{% endif %}>Custom</option>
              <option value="bybrands"{% if product_type == 'bybrands' %} selected{% endif %}>By Brands</option>
              <option value="mostviews"{% if product_type == 'mostviews' %} selected{% endif %}>Mostviews</option>
              <option value="bytags"{% if product_type == 'bytags' %} selected{% endif %}>By Tags</option>
              <option value="byfilters"{% if product_type == 'byfilters' %} selected{% endif %}>By Filters</option>
              <option value="byoptions"{% if product_type == 'byoptions' %} selected{% endif %}>By Options</option>
      </select>
    </div>
  </div>

        
<!-- Brands Section -->
<div class="form-group" style="display: none;" id="divbrands">
    <label for="input-manufacturer" class="col-sm-2 control-label">Select Brands</label>
    <div class="col-sm-10">
        <input type="text" class="form-control" id="input-manufacturer" name="manufacturer" placeholder="Select Brands" autocomplete="off">
        <ul class="dropdown-menu" id="autocomplete-manufacturer"></ul>
        <div class="form-control" style="height: 150px; overflow: auto; padding: 0;">
            <table id="product-manufacturer" class="table table-sm">
                <tbody>
                    {% for product_manufacturer in product_manufacturers %}
                    <tr id="product-manufacturer-{{ product_manufacturer.manufacturer_id }}">
                        <td>{{ product_manufacturer.name }}
                            <input type="hidden" name="product_manufacturer[]" value="{{ product_manufacturer.manufacturer_id }}">
                        </td>
                        <td class="text-right"><button type="button" class="btn btn-danger btn-sm"><i class="fa fa-trash-o"></i></button></td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        <span class="help-block">{{ help_manufacturer }}</span>
    </div>
</div>

<!-- Categories Section -->
<div class="form-group" style="display: none;" id="divcategories">
    <label for="input-category" class="col-sm-2 control-label">{{ entry_category }}</label>
    <div class="col-sm-10">
        <input type="text" class="form-control" id="input-category" name="category" placeholder="{{ entry_category }}" autocomplete="off">
        <ul class="dropdown-menu" id="autocomplete-category"></ul>
        <div class="form-control" style="height: 150px; overflow: auto; padding: 0;">
            <table id="product-category" class="table table-sm">
                <tbody>
                    
                    {% for product_category in product_categories %}
                    <tr id="product-category-{{ product_category.category_id }}">
                        <td>{{ product_category.name }}
                            <input type="hidden" name="product_category[]" value="{{ product_category.category_id }}">
                        </td>
                        <td class="text-right"><button type="button" class="btn btn-danger btn-sm"><i class="fa fa-trash-o"></i></button></td>
                    </tr>
                    {% endfor %}
                    
                </tbody>
            </table>
        </div>
        <span class="help-block">{{ help_category }}</span>
    </div>
</div>

          <div class="form-group" id="divcustom" style="display:none">
            <label class="col-sm-2 control-label">{{ entry_product }}</label>
            <div class="col-sm-10">
              <input type="text" name="product" value="" placeholder="{{ entry_product }}" id="input-product" data-oc-target="autocomplete-product" class="form-control" autocomplete="off"/>
              <ul id="autocomplete-product" class="dropdown-menu"></ul>
              <div class="form-control p-0" style="height: 150px; overflow: auto;">
                <table  id="proegproo-product" class="table table-sm m-0">
                  <tbody>
                    {% for product in ccproducts %}
                      <tr id="proegproo-product-{{ product.product_id }}">
                        <td>{{ product.name }}<input type="hidden" name="product[]" value="{{ product.product_id }}"/></td>
                        <td class="text-end"><button type="button" class="btn btn-danger btn-sm"><i class="fa fa-trash-o"></i></button></td>
                      </tr>
                    {% endfor %}
                  </tbody>
                </table>
              </div>
              <div class="form-text text-muted">{{ help_product }}</div>
          </div>

      
              </div>
<div class="form-group" style="display:none" id="divtags">
    <label for="input-tag" class="col-sm-2 control-label">Select Tags</label>
    <div class="col-sm-10">
        <input type="text" class="form-control" id="input-tag" name="tag" placeholder="Select Tags" autocomplete="off"/>
        <ul class="dropdown-menu" id="autocomplete-tag"></ul>
        <div class="form-control p-0" style="height: 150px; overflow: auto;">
            <table class="table table-sm m-0" id="product-tag">
                <tbody>
                    {% for product_tag in product_tags %}
                    <tr id="product-tag-{{ product_tag.tag }}">
                        <td>{{ product_tag.name }}
                            <input type="hidden" name="product_tag[]" value="{{ product_tag.tag }}">
                        </td>
                        <td class="text-right"><button type="button" class="btn btn-danger btn-sm"><i class="fa fa-trash-o"></i></button></td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        <span class="help-block">{{ help_category }}</span>
    </div>
</div>

<div class="form-group" style="display:none" id="divfilters">
    <label for="input-filter" class="col-sm-2 control-label">{{ entry_filter }}</label>
    <div class="col-sm-10">
        <input type="text" class="form-control" id="input-filter" name="filter" placeholder="{{ entry_filter }}" autocomplete="off"/>
        <ul class="dropdown-menu" id="autocomplete-filter"></ul>
        <div class="form-control p-0" style="height: 150px; overflow: auto;">
            <table class="table table-sm m-0" id="product-filter">
                <tbody>
                    {% for product_filter in product_filters %}
                    <tr id="product-filter-{{ product_filter.filter_id }}">
                        <td>{{ product_filter.name }}
                            <input type="hidden" name="product_filter[]" value="{{ product_filter.filter_id }}">
                        </td>
                        <td class="text-right"><button type="button" class="btn btn-danger btn-sm"><i class="fa fa-trash-o"></i></button></td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        <span class="help-block">{{ help_filter }}</span>
    </div>
</div>

<div class="form-group" style="display:none" id="divoptions">
    <label for="input-option" class="col-sm-2 control-label">{{ entry_option }}</label>
    <div class="col-sm-10">
        <input type="text" class="form-control" id="input-option" name="option" placeholder="{{ entry_option }}" autocomplete="off"/>
        <ul class="dropdown-menu" id="autocomplete-option"></ul>
        <div class="form-control p-0" style="height: 150px; overflow: auto;">
            <table class="table table-sm m-0" id="product-option">
                <tbody>
                    {% for product_option in product_options %}
                    <tr id="product-option-{{ product_option.option_id }}">
                        <td>{{ product_option.name }}
                            <input type="hidden" name="product_option[]" value="{{ product_option.option_id }}">
                        </td>
                        <td class="text-right"><button type="button" class="btn btn-danger btn-sm"><i class="fa fa-trash-o"></i></button></td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        <span class="help-block">{{ help_option }}</span>
    </div>
</div>


    <div class="form-group">
    <label class="col-sm-2 control-label" for="input-axis">{{ entry_type }}</label>
    <div class="col-sm-10">
      <select name="axis" id="input-axis" class="form-control">
                <option value="horizontal"{% if axis == 'horizontal' %} selected{% endif %}>{{ text_horizontal }}</option>
                <option value="vertical"{% if axis == 'vertical' %} selected{% endif %}>{{ text_vertical }}</option>
      </select>
    </div>
  </div>
          
 

           <div class="form-group">
            <label class="col-sm-2 control-label" for="input-width">{{ entry_width }}</label>
            <div class="col-sm-10">
              <input type="text" name="width" value="{{ width }}" placeholder="{{ entry_width }}" id="input-width" class="form-control" />
            </div>
          </div>
          
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-height">{{ entry_height }}</label>
            <div class="col-sm-10">
              <input type="text" name="height" value="{{ height }}" placeholder="{{ entry_height }}" id="input-height" class="form-control" />
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-product_count">Limit</label>
            <div class="col-sm-10">
              <input type="text" name="product_count" value="{{ product_count }}" placeholder="{{ product_count }}" id="input-product_count" class="form-control" />
            </div>
          </div>          
          
          
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-status">{{ entry_status }}</label>
            <div class="col-sm-10">
              <select name="status" id="input-status" class="form-control">
                {% if status %}
                <option value="1" selected="selected">{{ text_enabled }}</option>
                <option value="0">{{ text_disabled }}</option>
                {% else %}
                <option value="1">{{ text_enabled }}</option>
                <option value="0" selected="selected">{{ text_disabled }}</option>
                {% endif %}
              </select>
            </div>
          </div>

          <input type="hidden" name="module_id" value="{{ module_id }}" id="input-module-id"/>
        </form>
      </div>
    </div>
  </div>
</div>
<script type="text/javascript"><!--
$('#input-product').autocomplete({
    source: function (request, response) {
        $.ajax({
            url: 'index.php?route=catalog/product/autocomplete&user_token={{ user_token }}&filter_name=' + encodeURIComponent(request),
            dataType: 'json',
            success: function (json) {
                response($.map(json, function (item) {
                    return {
                        label: item['name'],
                        value: item['product_id']
                    }
                }));
            }
        });
    },
    select: function (item) {
        $('#input-product').val('');

        $('#proegproo-product-' + item['value']).remove();

        html  = '<tr id="proegproo-product-' + item['value'] + '">';
        html += '  <td>' + item['label'] + '<input type="hidden" name="product[]" value="' + item['value'] + '"/></td>';
        html += '  <td class="text-end"><button type="button" class="btn btn-danger btn-sm"><i class="fa fa-trash-o"></i></button></td>';
        html += '</tr>';

        $('#proegproo-product tbody').append(html);
    }
});

$('#proegproo-product').on('click', '.btn', function () {
    $(this).parent().parent().remove();
});
$('#input-manufacturer').autocomplete({
    'source': function (request, response) {
        $.ajax({
            url: 'index.php?route=catalog/manufacturer/autocomplete&user_token={{ user_token }}&filter_name=' + encodeURIComponent(request),
            dataType: 'json',
            success: function (json) {
                response($.map(json, function (item) {
                    return {
                        label: item['name'],
                        value: item['manufacturer_id']
                    }
                }));
            }
        });
    },
    'select': function (item) {
        $('#input-manufacturer').val('');

        $('#product-manufacturer-' + item['value']).remove();

        html = '<tr id="product-manufacturer-' + item['value'] + '">';
        html += '  <td>' + item['label'] + '<input type="hidden" name="product_manufacturer[]" value="' + item['value'] + '"/></td>';
        html += '  <td class="text-end"><button type="button" class="btn btn-danger btn-sm"><i class="fa fa-trash-o"></i></button></td>';
        html += '</tr>';

        $('#product-manufacturer tbody').append(html);
    }
});

$('#product-manufacturer').on('click', '.btn', function () {
    $(this).parent().parent().remove();
});
// Filter
$('#input-filter').autocomplete({
  'source': function (request, response) {
    $.ajax({
      url: 'index.php?route=catalog/filter/autocomplete&user_token={{ user_token }}&filter_name=' + encodeURIComponent(request),
      dataType: 'json',
      success: function (json) {
        response($.map(json, function (item) {
          return {
            label: item['name'],
            value: item['filter_id']
          }
        }));
      }
    });
  },
  'select': function (item) {
    $('#input-filter').val('');

    $('#product-filter-' + item['value']).remove();

    var html = '<tr id="product-filter-' + item['value'] + '">';
    html += '  <td>' + item['label'] + '<input type="hidden" name="product_filter[]" value="' + item['value'] + '"/></td>';
    html += '  <td class="text-end"><button type="button" class="btn btn-danger btn-sm btn-remove-filter"><i class="fa fa-trash-o"></i></button></td>';
    html += '</tr>';

    $('#product-filter tbody').append(html);
  }
});

// Remove filter
$('#product-filter').on('click', '.btn-remove-filter', function () {
  $(this).parent().parent().remove();
});

$('#input-tag').autocomplete({
  'source': function (request, response) {
    $.ajax({
      url: 'index.php?route=catalog/product/autocompletetag&user_token={{ user_token }}&filter_name=' + encodeURIComponent(request),
      dataType: 'json',
      success: function (json) {
        response($.map(json, function (item) {
          return {
            label: item['name'],
            value: item['tag']
          }
        }));
      }
    });
  },
  'select': function (item) {
    $('#input-tag').val('');

    $('#product-tag-' + item['value']).remove();

    var html = '<tr id="product-tag-' + item['value'] + '">';
    html += '  <td>' + item['label'] + '<input type="hidden" name="product_tag[]" value="' + item['value'] + '"/></td>';
    html += '  <td class="text-end"><button type="button" class="btn btn-danger btn-sm btn-remove-filter"><i class="fa fa-trash-o"></i></button></td>';
    html += '</tr>';

    $('#product-tag tbody').append(html);
  }
});

// Remove filter
$('#product-tag').on('click', '.btn-remove-filter', function () {
  $(this).parent().parent().remove();
});
// Option
$('#input-option').autocomplete({
  'source': function (request, response) {
    $.ajax({
      url: 'index.php?route=catalog/option/autocompleteq&user_token={{ user_token }}&filter_name=' + encodeURIComponent(request),
      dataType: 'json',
      success: function (json) {
        response($.map(json, function (item) {
          return {
            label: item['name'],
            value: item['option_id']
          }
        }));
      }
    });
  },
  'select': function (item) {
    $('#input-option').val('');

    $('#product-option-' + item['value']).remove();

    var html = '<tr id="product-option-' + item['value'] + '">';
    html += '  <td>' + item['label'] + '<input type="hidden" name="product_option[]" value="' + item['value'] + '"/></td>';
    html += '  <td class="text-end"><button type="button" class="btn btn-danger btn-sm btn-remove-option"><i class="fa fa-trash-o"></i></button></td>';
    html += '</tr>';

    $('#product-option tbody').append(html);
  }
});

// Remove option
$('#product-option').on('click', '.btn-remove-option', function () {
  $(this).parent().parent().remove();
});

// Category
$('#input-category').autocomplete({
    'source': function (request, response) {
        $.ajax({
            url: 'index.php?route=catalog/category/autocomplete&user_token={{ user_token }}&filter_name=' + encodeURIComponent(request),
            dataType: 'json',
            success: function (json) {
                response($.map(json, function (item) {
                    return {
                        label: item['name'],
                        value: item['category_id']
                    }
                }));
            }
        });
    },
    'select': function (item) {
        $('#input-category').val('');

        $('#product-category-' + item['value']).remove();

        html = '<tr id="product-category-' + item['value'] + '">';
        html += '  <td>' + item['label'] + '<input type="hidden" name="product_category[]" value="' + item['value'] + '"/></td>';
        html += '  <td class="text-end"><button type="button" class="btn btn-danger btn-sm"><i class="fa fa-trash-o"></i></button></td>';
        html += '</tr>';

        $('#product-category tbody').append(html);
    }
});

$('#product-category').on('click', '.btn', function () {
    $(this).parent().parent().remove();
});

// Filter
$('#input-filter').autocomplete({
    'source': function (request, response) {
        $.ajax({
            url: 'index.php?route=catalog/filter/autocomplete&user_token={{ user_token }}&filter_name=' + encodeURIComponent(request),
            dataType: 'json',
            success: function (json) {
                response($.map(json, function (item) {
                    return {
                        label: item['name'],
                        value: item['filter_id']
                    }
                }));
            }
        });
    },
    'select': function (item) {
        $('#input-filter').val('');

        $('#product-filter-' + item['value']).remove();

        html = '<tr id="product-filter-' + item['value'] + '">';
        html += '  <td>' + item['label'] + '<input type="hidden" name="product_filter[]" value="' + item['value'] + '"/></td>';
        html += '  <td class="text-end"><button type="button" class="btn btn-danger btn-sm"><i class="fa fa-trash-o"></i></button></td>';
        html += '</tr>';

        $('#product-filter tbody').append(html);
    }
});

$('#product-filter').on('click', '.btn', function () {
    $(this).parent().parent().remove();
});
//  bycategories custom bybrand bytags byoptions byfilters
$('#input-product-type').change(function() {
  var productType = $(this).val();
  
  // Hide all divs
  $('#divbrands, #divcategories, #divtags, #divfilters, #divoptions, #divcustom').hide();

  // Show the corresponding div based on the selected product type
  switch (productType) {
    case 'custom':
      $('#divcustom').show();
      $('#product-tag tbody').html('');
      $('#product-category tbody').html('');
      $('#product-filter tbody').html('');
      $('#product-option tbody').html('');
      $('#product-manufacturer tbody').html('');      
      break;
    case 'bycategories':
      $('#divcategories').show();
      $('#product-tag tbody').html('');
      $('#product-filter tbody').html('');
      $('#product-option tbody').html('');
      $('#product-manufacturer tbody').html('');      
      $('#proegproo-product tbody').html('');      
      break;
    case 'bybrands':
      $('#divbrands').show();
      $('#product-tag tbody').html('');
      $('#product-category tbody').html('');
      $('#product-filter tbody').html('');
      $('#product-option tbody').html('');
      $('#proegproo-product tbody').html('');      
      break;
    case 'bytags':
      $('#divtags').show();
      $('#product-category tbody').html('');
      $('#product-filter tbody').html('');
      $('#product-option tbody').html('');
      $('#product-manufacturer tbody').html('');      
      $('#proegproo-product tbody').html('');      
      break;
    case 'byoptions':
      $('#divoptions').show();
      $('#product-tag tbody').html('');
      $('#product-category tbody').html('');
      $('#product-filter tbody').html('');
      $('#product-manufacturer tbody').html('');      
      $('#proegproo-product tbody').html('');      
      break;
    case 'byfilters':
      $('#divfilters').show();
      $('#product-tag tbody').html('');
      $('#product-category tbody').html('');
      $('#product-option tbody').html('');
      $('#product-manufacturer tbody').html('');      
      $('#proegproo-product tbody').html('');      
      break;
    default:
      $('#product-tag tbody').html('');
      $('#product-category tbody').html('');
      $('#product-filter tbody').html('');
      $('#product-option tbody').html('');
      $('#product-manufacturer tbody').html(''); 
      $('#proegproo-product tbody').html('');      
      break;
  }
});

// Trigger the change event on page load
$('#input-product-type').trigger('change');

  var oldproductType = '{{product_type}}';
  $('#divbrands, #divcategories, #divtags, #divfilters, #divoptions, #divcustom').hide();

  // Show the corresponding div based on the selected product type
 switch (oldproductType) {
    case 'custom':
      $('#divcustom').show();
      $('#product-tag tbody').html('');
      $('#product-category tbody').html('');
      $('#product-filter tbody').html('');
      $('#product-option tbody').html('');
      $('#product-manufacturer tbody').html('');      
      break;
    case 'bycategories':
      $('#divcategories').show();
      $('#product-tag tbody').html('');
      $('#product-filter tbody').html('');
      $('#product-option tbody').html('');
      $('#product-manufacturer tbody').html('');      
      $('#proegproo-product tbody').html('');      
      break;
    case 'bybrands':
      $('#divbrands').show();
      $('#product-tag tbody').html('');
      $('#product-category tbody').html('');
      $('#product-filter tbody').html('');
      $('#product-option tbody').html('');
      $('#proegproo-product tbody').html('');      
      break;
    case 'bytags':
      $('#divtags').show();
      $('#product-category tbody').html('');
      $('#product-filter tbody').html('');
      $('#product-option tbody').html('');
      $('#product-manufacturer tbody').html('');      
      $('#proegproo-product tbody').html('');      
      break;
    case 'byoptions':
      $('#divoptions').show();
      $('#product-tag tbody').html('');
      $('#product-category tbody').html('');
      $('#product-filter tbody').html('');
      $('#product-manufacturer tbody').html('');      
      $('#proegproo-product tbody').html('');      
      break;
    case 'byfilters':
      $('#divfilters').show();
      $('#product-tag tbody').html('');
      $('#product-category tbody').html('');
      $('#product-option tbody').html('');
      $('#product-manufacturer tbody').html('');      
      $('#proegproo-product tbody').html('');      
      break;
    default:
      $('#product-tag tbody').html('');
      $('#product-category tbody').html('');
      $('#product-filter tbody').html('');
      $('#product-option tbody').html('');
      $('#product-manufacturer tbody').html(''); 
      $('#proegproo-product tbody').html('');      
      break;
  }
//--></script>
{{ footer }}
