{{ header }}{{ column_left }}
<div id="content">
<style>
/* تحسينات متقدمة لتحليل الربحية - Enterprise Grade */
.panel-default {
    border: none;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.panel-default:hover {
    box-shadow: 0 0.25rem 2rem 0 rgba(58, 59, 69, 0.2);
}

/* تحسين بطاقات المؤشرات */
.kpi-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 15px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.kpi-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.kpi-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #28a745, #20c997);
}

.kpi-card-icon {
    font-size: 2.5em;
    margin-bottom: 10px;
    opacity: 0.8;
}

.kpi-card-value {
    font-size: 1.8em;
    font-weight: 700;
    margin-bottom: 5px;
}

.kpi-card-label {
    color: #6c757d;
    font-size: 0.9em;
    font-weight: 500;
}

/* تحسين القيم */
.revenue-amount {
    color: #28a745;
    font-weight: 600;
}

.profit-amount {
    color: #007bff;
    font-weight: 700;
}

.margin-percentage {
    color: #6f42c1;
    font-weight: 600;
}

.roi-percentage {
    color: #fd7e14;
    font-weight: 700;
}

/* تحسين الأزرار */
.btn {
    transition: all 0.2s ease;
    border-radius: 6px;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
    border: none;
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8 0%, #117a8b 100%);
    border: none;
}

/* تحسين الرسوم البيانية */
.chart-container {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 0.125rem 0.25rem rgba(0,0,0,0.075);
    margin-bottom: 20px;
}

.chart-title {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 15px;
    text-align: center;
    font-size: 1.1em;
}
</style>

<div class="page-header">
    <div class="container-fluid">
        <div class="pull-right">
            <button type="button" data-toggle="tooltip" title="فلترة" onclick="$('#filter-profitability').toggle();" class="btn btn-default"><i class="fa fa-filter"></i></button>
            <button type="button" data-toggle="tooltip" title="تصدير Excel" onclick="exportProfitabilityAnalysis('excel')" class="btn btn-success"><i class="fa fa-download"></i></button>
            <button type="button" data-toggle="tooltip" title="طباعة" onclick="printProfitabilityAnalysis()" class="btn btn-info"><i class="fa fa-print"></i></button>
        </div>
        <h1>{{ heading_title }}</h1>
        <ul class="breadcrumb">
            {% for breadcrumb in breadcrumbs %}
            <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
            {% endfor %}
        </ul>
    </div>
</div>

<div class="container-fluid">
    <!-- ملخص الربحية -->
    <div class="panel panel-default">
        <div class="panel-header">
            <h3 class="panel-title"><i class="fa fa-chart-line"></i> ملخص تحليل الربحية المتقدم</h3>
        </div>
        
        <div class="panel-body">
            <div class="row">
                <div class="col-md-3">
                    <div class="kpi-card">
                        <div class="kpi-card-icon">
                            <i class="fa fa-dollar-sign text-success"></i>
                        </div>
                        <div class="kpi-card-value revenue-amount">{{ revenue|default('0.00') }}</div>
                        <div class="kpi-card-label">إجمالي الإيرادات</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="kpi-card">
                        <div class="kpi-card-icon">
                            <i class="fa fa-chart-line text-primary"></i>
                        </div>
                        <div class="kpi-card-value profit-amount">{{ net_profit|default('0.00') }}</div>
                        <div class="kpi-card-label">صافي الربح</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="kpi-card">
                        <div class="kpi-card-icon">
                            <i class="fa fa-percentage text-purple"></i>
                        </div>
                        <div class="kpi-card-value margin-percentage">{{ net_margin|default('0.00') }}%</div>
                        <div class="kpi-card-label">هامش صافي الربح</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="kpi-card">
                        <div class="kpi-card-icon">
                            <i class="fa fa-trophy text-warning"></i>
                        </div>
                        <div class="kpi-card-value roi-percentage">{{ roi|default('0.00') }}%</div>
                        <div class="kpi-card-label">العائد على الاستثمار</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الرسوم البيانية -->
    <div class="row">
        <div class="col-md-8">
            <div class="chart-container">
                <div class="chart-title">تحليل الربحية الشامل</div>
                <canvas id="profitabilityChart" width="400" height="250"></canvas>
            </div>
        </div>
        <div class="col-md-4">
            <div class="chart-container">
                <div class="chart-title">توزيع مكونات الربح</div>
                <canvas id="profitBreakdownChart" width="300" height="300"></canvas>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript"><!--
// تحسينات متقدمة لتحليل الربحية
$(document).ready(function() {
    // إنشاء الرسوم البيانية
    initializeProfitabilityCharts();
});

// دالة تهيئة الرسوم البيانية
function initializeProfitabilityCharts() {
    // رسم تحليل الربحية الشامل
    if ($('#profitabilityChart').length) {
        const ctx = document.getElementById('profitabilityChart').getContext('2d');
        const profitabilityChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: ['الإيرادات', 'تكلفة البضائع', 'المصروفات التشغيلية', 'صافي الربح'],
                datasets: [{
                    label: 'المبالغ',
                    data: [100000, 60000, 25000, 15000],
                    backgroundColor: ['#28a745', '#dc3545', '#ffc107', '#007bff'],
                    borderColor: ['#1e7e34', '#c82333', '#e0a800', '#0056b3'],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }
    
    // رسم توزيع مكونات الربح
    if ($('#profitBreakdownChart').length) {
        const ctx2 = document.getElementById('profitBreakdownChart').getContext('2d');
        const profitBreakdownChart = new Chart(ctx2, {
            type: 'doughnut',
            data: {
                labels: ['الربح الإجمالي', 'المصروفات التشغيلية', 'صافي الربح'],
                datasets: [{
                    data: [40000, 25000, 15000],
                    backgroundColor: ['#28a745', '#ffc107', '#007bff'],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                legend: {
                    position: 'bottom'
                }
            }
        });
    }
}

// دالة تصدير تحليل الربحية
function exportProfitabilityAnalysis(format) {
    const url = 'index.php?route=accounts/profitability_analysis/export&format=' + format;
    window.open(url, '_blank');
}

// دالة طباعة تحليل الربحية
function printProfitabilityAnalysis() {
    window.print();
}
//--></script>
{{ footer }}