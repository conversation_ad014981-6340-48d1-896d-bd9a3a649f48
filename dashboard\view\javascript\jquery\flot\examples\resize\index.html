<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>
<head>
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
	<title>Flot Examples: Resizing</title>
	<link href="../examples.css" rel="stylesheet" type="text/css">
	<link href="../shared/jquery-ui/jquery-ui.min.css" rel="stylesheet" type="text/css">
	<!--[if lte IE 8]><script language="javascript" type="text/javascript" src="../../excanvas.min.js"></script><![endif]-->
	<script language="javascript" type="text/javascript" src="../../jquery.js"></script>
	<script language="javascript" type="text/javascript" src="../shared/jquery-ui/jquery-ui.min.js"></script>
	<script language="javascript" type="text/javascript" src="../../jquery.flot.js"></script>
	<script language="javascript" type="text/javascript" src="../../jquery.flot.resize.js"></script>
	<script type="text/javascript">

	$(function() {

		var d1 = [];
		for (var i = 0; i < 14; i += 0.5) {
			d1.push([i, Math.sin(i)]);
		}

		var d2 = [[0, 3], [4, 8], [8, 5], [9, 13]];
		var d3 = [[0, 12], [7, 12], null, [7, 2.5], [12, 2.5]];

		var placeholder = $("#placeholder");
		var plot = $.plot(placeholder, [d1, d2, d3]);

		// The plugin includes a jQuery plugin for adding resize events to any
		// element.  Add a callback so we can display the placeholder size.

		placeholder.resize(function () {
			$(".message").text("Placeholder is now "
				+ $(this).width() + "x" + $(this).height()
				+ " pixels");
		});

		$(".demo-container").resizable({
			maxWidth: 900,
			maxHeight: 500,
			minWidth: 450,
			minHeight: 250,
		});

		// Add the Flot version string to the footer

		$("#footer").prepend("Flot " + $.plot.version + " &ndash; ");
	});

	</script>
</head>
<body>

	<div id="header">
		<h2>Resizing</h2>
	</div>

	<div id="content">

		<div class="demo-container">
			<div id="placeholder" class="demo-placeholder"></div>
		</div>

		<p class="message"></p>

		<p>Sometimes it makes more sense to just let the plot take up the available space. In that case, we need to redraw the plot each time the placeholder changes its size. If you include the resize plugin, this is handled automatically.</p>

		<p>Drag the bottom and right sides of the plot to resize it.</p>

	</div>

	<div id="footer">
		Copyright &copy; 2007 - 2013 IOLA and Ole Laursen
	</div>

</body>
</html>
