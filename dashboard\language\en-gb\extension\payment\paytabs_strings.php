<?php
// Heading
$_['all_heading_title']             = 'PayTabs - All';
$_['creditcard_heading_title']      = 'PayTabs - CreditCard';
$_['mada_heading_title']            = 'PayTabs - mada';
$_['amex_heading_title']            = 'PayTabs - Amex';
$_['applepay_heading_title']        = 'PayTabs - ApplePay';
$_['fawry_heading_title']           = 'PayTabs - @Fawry';
$_['knet_heading_title']            = 'PayTabs - KnPay';
$_['omannet_heading_title']         = 'PayTabs - OmanNet';
$_['sadad_heading_title']           = 'PayTabs - Sadad';
$_['stcpay_heading_title']          = 'PayTabs - StcPay';
$_['valu_heading_title']            = 'PayTabs - valU';
$_['meeza_heading_title']           = 'PayTabs - Meeza';
$_['meezaqr_heading_title']         = 'PayTabs - Meeza QR';
$_['unionpay_heading_title']        = 'PayTabs - UnionPay';
$_['touchpoints_heading_title']     = 'PayTabs - TouchPoints';
$_['paypal_heading_title']          = 'PayTabs - PayPal';
$_['installment_heading_title']     = 'PayTabs - NBE Installment';
$_['urpay_heading_title']           = 'PayTabs - UrPay';
$_['aman_heading_title']            = 'PayTabs - Aman';
$_['forsa_heading_title']           = 'PayTabs - Forsa';
$_['tabby_heading_title']           = 'PayTabs - Tabby';
$_['souhoola_heading_title']        = 'PayTabs - Souhoola';

// Text
$_['text_extension']                = 'Extensions';
$_['text_success']                  = 'Success: You have modified PayTabs configurations';
$_['text_edit']                     = 'Edit Payment method settings';
$_['text_paytabsexpress']           = '<a target="_BLANK" href="https://www.paytabs.com"><img style="width:100px" src="https://www.paytabs.com/theme/express_checkout/img/checkout.png" alt="PayTabs Express Checkout" title="PayTabs Express Checkout" style="border: 1px solid #EEEEEE;" /></a>';
$_['text_live']                     = 'Live';
$_['text_sandbox']                  = 'Sandbox';

// Entry
$_['entry_endpoint']                = 'Endpoint';
$_['entry_profile_id']              = 'Profile ID';
$_['entry_merchant_id']             = 'Merchant ID';
$_['entry_server_key']              = 'Server Key';
$_['entry_secure_sign']             = 'Secure Sign';
$_['entry_total']                   = 'Total';
$_['entry_order_status']            = 'Completed Status';
$_['entry_order_failed_status']     = 'Failed Status';
$_['entry_order_fraud_status']      = 'Fraud Status';
$_['entry_geo_zone']                = 'Geo Zone';
$_['entry_status']                  = 'Status';
$_['entry_sort_order']              = 'Sort Order';
$_['entry_hide_shipping']           = 'Hide Shipping';
$_['entry_iframe']                  = 'iFrame mode';
$_['entry_allow_associated_methods'] = 'Allow associated methods';
$_['entry_config_id']               = 'Theme config id';
$_['entry_alt_currency']            = 'Alternative Currency';

// Help
$_['help_total']                    = 'The checkout total the order must reach before this payment method becomes active';
$_['help_paytabs_account_setup']    = '<a target="_blank" href="http://www.paytabs.com">Click here</a> to learn how to set up PayTabs account.';

// Error
$_['error_permission']              = 'Warning: You do not have permission to modify payment PayTabs!';
$_['error_merchant_id']             = 'Merchant ID required!';
$_['error_server_key']              = 'Merchant Server Key required!';
$_['error_secure_sign']             = 'Secure sign required!';
$_['error_endpoint']                = 'Endpoint required!';
$_['error_profile_id']              = 'Profile ID required!';
$_['error_valu_product_id']         = 'valU product ID required!';
