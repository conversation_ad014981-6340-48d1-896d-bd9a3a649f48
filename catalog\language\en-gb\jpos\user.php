<?php
// Heading
$_['heading_title']  = 'POS User Login';

// Text

$_['text_success']  = 'Success: Information updated Successfully!';

// General Settings starts
$_['text_general_success']  = 'Success: General Settings Updated Successfully!';
// General Settings ends
// Entry
$_['entry_username']       = 'Username';
$_['entry_password']       = 'Password';

$_['entry_firstname']       = 'First Name';
$_['entry_lastname']       = 'Last Name';
$_['entry_email']       = 'Email';

$_['entry_password']       = 'New Password';
$_['entry_password_old']       = 'Previous Password';
$_['entry_password_confirm']       = 'Confirm Password';

// General Settings starts
$_['entry_location']       = 'Select Default Location';
$_['entry_language']       = 'Select Default Language';
$_['entry_currency']       = 'Select Default Currency';
// General Settings ends

// Button
$_['button_login']       = 'Login';

// Error
$_['error_login']    = 'No match for Username and/or Password.';
$_['error_firstname']       = 'First Name must be between 1 and 32 characters!';
$_['error_lastname']        = 'Last Name must be between 1 and 32 characters!';
$_['error_email']           = 'E-Mail Address does not appear to be valid!';
$_['error_exists_email']    = 'Warning: Can not use this E-Mail!';
$_['error_password']        = 'Password must be between 4 and 20 characters!';
$_['error_confirm']         = 'Password and password confirmation do not match!';
$_['error_password_previous']         = 'Previous Password does not match!';
// General Settings starts
$_['error_default_location']       = 'Warning: Must Default Location!';
$_['error_default_language']       = 'Warning: Must Default Language!';
$_['error_default_currency']       = 'Warning: Must Default Currency!';
// General Settings ends
