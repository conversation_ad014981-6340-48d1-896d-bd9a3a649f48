{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="submit" form="form-credit-limit" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fa fa-save"></i></button>
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pencil"></i> {{ text_form }}</h3>
      </div>
      <div class="panel-body">
        <form action="{{ action }}" method="post" enctype="multipart/form-data" id="form-credit-limit" class="form-horizontal">
          <div class="form-group">
            <label class="col-sm-2 control-label">{{ entry_customer }}</label>
            <div class="col-sm-10">
              <input type="text" value="{{ customer_name }}" class="form-control" readonly />
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label">{{ entry_email }}</label>
            <div class="col-sm-10">
              <input type="text" value="{{ customer_email }}" class="form-control" readonly />
            </div>
          </div>
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-credit-limit">{{ entry_credit_limit }}</label>
            <div class="col-sm-10">
              <input type="number" name="credit_limit" value="{{ credit_limit }}" placeholder="{{ entry_credit_limit }}" id="input-credit-limit" class="form-control" step="0.01" min="0" />
              {% if error_credit_limit %}
              <div class="text-danger">{{ error_credit_limit }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-payment-terms">{{ entry_payment_terms }}</label>
            <div class="col-sm-10">
              <input type="number" name="payment_terms" value="{{ payment_terms }}" placeholder="{{ entry_payment_terms }}" id="input-payment-terms" class="form-control" min="0" />
              <small class="form-text text-muted">{{ help_payment_terms }}</small>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-status">{{ entry_status }}</label>
            <div class="col-sm-10">
              <select name="status" id="input-status" class="form-control">
                <option value="active"{% if status == 'active' %} selected="selected"{% endif %}>{{ text_active }}</option>
                <option value="suspended"{% if status == 'suspended' %} selected="selected"{% endif %}>{{ text_suspended }}</option>
                <option value="pending_approval"{% if status == 'pending_approval' %} selected="selected"{% endif %}>{{ text_pending_approval }}</option>
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-notes">{{ entry_notes }}</label>
            <div class="col-sm-10">
              <textarea name="notes" rows="5" placeholder="{{ entry_notes }}" id="input-notes" class="form-control">{{ notes }}</textarea>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

{{ footer }} 