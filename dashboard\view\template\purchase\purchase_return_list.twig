{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-right">
        <button type="button" data-toggle="tooltip" title="{{ button_filter }}" onclick="$('#filter-return').toggleClass('d-none');" class="btn btn-light d-md-none"><i class="fas fa-filter"></i></button>
        <a href="{{ add }}" data-toggle="tooltip" title="{{ button_add }}" class="btn btn-primary"><i class="fas fa-plus"></i></a>
        <button type="button" data-toggle="tooltip" title="{{ button_delete }}" class="btn btn-danger" onclick="confirm('{{ text_confirm }}') ? $('#form-return').submit() : false;"><i class="fas fa-trash-alt"></i></button>
      </div>
      <h1>{{ heading_title }}</h1>
      <ol class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
          <li class="breadcrumb-item"><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{ error_warning }}
        <button type="button" class="close" data-dismiss="alert">&times;</button>
      </div>
    {% endif %}
    {% if success %}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{ success }}
        <button type="button" class="close" data-dismiss="alert">&times;</button>
      </div>
    {% endif %}
    <div class="row">
      <div id="filter-return" class="col-md-3 col-sm-12 order-md-9 d-none d-md-block mb-3">
        <div class="card">
          <div class="card-header"><i class="fas fa-filter"></i> {{ text_filter }}</div>
          <div class="card-body">
            <div class="form-group">
              <label for="input-return-number">{{ entry_return_number }}</label>
              <input type="text" name="filter_return_number" value="{{ filter_return_number }}" placeholder="{{ entry_return_number }}" id="input-return-number" class="form-control"/>
            </div>
            <div class="form-group">
              <label for="input-order-number">{{ entry_order_number }}</label>
              <input type="text" name="filter_order_number" value="{{ filter_order_number }}" placeholder="{{ entry_order_number }}" id="input-order-number" class="form-control"/>
            </div>
            <div class="form-group">
              <label for="input-receipt-number">{{ entry_receipt_number }}</label>
              <input type="text" name="filter_receipt_number" value="{{ filter_receipt_number }}" placeholder="{{ entry_receipt_number }}" id="input-receipt-number" class="form-control"/>
            </div>
            <div class="form-group">
              <label for="input-supplier">{{ entry_supplier }}</label>
              <input type="text" name="filter_supplier" value="{{ filter_supplier }}" placeholder="{{ entry_supplier }}" id="input-supplier" class="form-control"/>
            </div>
            <div class="form-group">
              <label for="input-status">{{ entry_status }}</label>
              <select name="filter_status" id="input-status" class="form-control">
                <option value=""></option>
                <option value="active"{% if filter_status == 'active' %} selected{% endif %}>{{ text_active }}</option>
                <option value="pending"{% if filter_status == 'pending' %} selected{% endif %}>{{ text_pending }}</option>
                <option value="approved"{% if filter_status == 'approved' %} selected{% endif %}>{{ text_approved }}</option>
                <option value="rejected"{% if filter_status == 'rejected' %} selected{% endif %}>{{ text_rejected }}</option>
                <option value="completed"{% if filter_status == 'completed' %} selected{% endif %}>{{ text_completed }}</option>
                <option value="canceled"{% if filter_status == 'canceled' %} selected{% endif %}>{{ text_canceled }}</option>
              </select>
            </div>
            <div class="form-group">
              <label for="input-date-added">{{ entry_date_added }}</label>
              <div class="input-group date">
                <input type="text" name="filter_date_added" value="{{ filter_date_added }}" placeholder="{{ entry_date_added }}" id="input-date-added" class="form-control"/>
                <div class="input-group-append">
                  <div class="input-group-text"><i class="fas fa-calendar"></i></div>
                </div>
              </div>
            </div>
            <div class="form-group text-right">
              <button type="button" id="button-filter" class="btn btn-primary"><i class="fas fa-filter"></i> {{ button_filter }}</button>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-9 col-sm-12">
        <div class="card">
          <div class="card-header"><i class="fas fa-list"></i> {{ text_list }}</div>
          <div class="card-body">
            <form action="{{ delete }}" method="post" enctype="multipart/form-data" id="form-return">
              <div class="table-responsive">
                <table class="table table-bordered table-hover">
                  <thead>
                    <tr>
                      <td style="width: 1px;" class="text-center"><input type="checkbox" onclick="$('input[name*=\'selected\']').trigger('click');"/></td>
                      <td class="text-center">{% if sort == 'r.return_number' %}
                          <a href="{{ sort_return_number }}" class="{{ order|lower }}">{{ column_return_number }}</a>
                        {% else %}
                          <a href="{{ sort_return_number }}">{{ column_return_number }}</a>
                        {% endif %}</td>
                      <td class="text-center">{% if sort == 'r.order_number' %}
                          <a href="{{ sort_order_number }}" class="{{ order|lower }}">{{ column_order_number }}</a>
                        {% else %}
                          <a href="{{ sort_order_number }}">{{ column_order_number }}</a>
                        {% endif %}</td>
                      <td class="text-center">{% if sort == 'r.receipt_number' %}
                          <a href="{{ sort_receipt_number }}" class="{{ order|lower }}">{{ column_receipt_number }}</a>
                        {% else %}
                          <a href="{{ sort_receipt_number }}">{{ column_receipt_number }}</a>
                        {% endif %}</td>
                      <td class="text-center">{% if sort == 's.name' %}
                          <a href="{{ sort_supplier }}" class="{{ order|lower }}">{{ column_supplier }}</a>
                        {% else %}
                          <a href="{{ sort_supplier }}">{{ column_supplier }}</a>
                        {% endif %}</td>
                      <td class="text-right">{% if sort == 'r.total_amount' %}
                          <a href="{{ sort_total_amount }}" class="{{ order|lower }}">{{ column_total_amount }}</a>
                        {% else %}
                          <a href="{{ sort_total_amount }}">{{ column_total_amount }}</a>
                        {% endif %}</td>
                      <td class="text-center">{% if sort == 'r.status' %}
                          <a href="{{ sort_status }}" class="{{ order|lower }}">{{ column_status }}</a>
                        {% else %}
                          <a href="{{ sort_status }}">{{ column_status }}</a>
                        {% endif %}</td>
                      <td class="text-center">{% if sort == 'r.date_added' %}
                          <a href="{{ sort_date_added }}" class="{{ order|lower }}">{{ column_date_added }}</a>
                        {% else %}
                          <a href="{{ sort_date_added }}">{{ column_date_added }}</a>
                        {% endif %}</td>
                      <td class="text-right">{{ column_action }}</td>
                    </tr>
                  </thead>
                  <tbody>
                    {% if returns %}
                      {% for return in returns %}
                        <tr>
                          <td class="text-center">{% if return.return_id in selected %}
                              <input type="checkbox" name="selected[]" value="{{ return.return_id }}" checked="checked"/>
                            {% else %}
                              <input type="checkbox" name="selected[]" value="{{ return.return_id }}"/>
                            {% endif %}</td>
                          <td class="text-center">{{ return.return_number }}</td>
                          <td class="text-center">{{ return.order_number }}</td>
                          <td class="text-center">{{ return.receipt_number }}</td>
                          <td class="text-center">{{ return.supplier }}</td>
                          <td class="text-right">{{ return.total_amount }}</td>
                          <td class="text-center">{{ return.status }}</td>
                          <td class="text-center">{{ return.date_added }}</td>
                          <td class="text-right">
                            <div class="btn-group">
                              <a href="{{ return.view }}" data-toggle="tooltip" title="{{ button_view }}" class="btn btn-info"><i class="fas fa-eye"></i></a>
                              {% if return.edit %}
                                <a href="{{ return.edit }}" data-toggle="tooltip" title="{{ button_edit }}" class="btn btn-primary"><i class="fas fa-pencil-alt"></i></a>
                              {% endif %}
                              {% if return.status == 'approved' %}
                                <a href="{{ return.credit_note }}" data-toggle="tooltip" title="{{ button_create_credit_note }}" class="btn btn-success"><i class="fas fa-file-invoice-dollar"></i></a>
                              {% endif %}
                              <button type="button" data-toggle="dropdown" class="btn btn-outline-primary dropdown-toggle dropdown-toggle-split"><span class="fas fa-caret-down"></span></button>
                              <div class="dropdown-menu dropdown-menu-right">
                                <a href="{{ return.print }}" class="dropdown-item"><i class="fas fa-print fa-fw"></i> {{ button_print }}</a>
                                <a href="{{ return.download }}" class="dropdown-item"><i class="fas fa-download fa-fw"></i> {{ button_download }}</a>
                                {% if return.status == 'pending' %}
                                  <a href="{{ return.approve }}" onclick="return confirm('{{ text_confirm }}');" class="dropdown-item"><i class="fas fa-check fa-fw"></i> {{ button_approve }}</a>
                                  <a href="{{ return.reject }}" onclick="return confirm('{{ text_confirm }}');" class="dropdown-item"><i class="fas fa-times fa-fw"></i> {{ button_reject }}</a>
                                {% endif %}
                                {% if return.status == 'active' or return.status == 'pending' %}
                                  <a href="{{ return.cancel }}" onclick="return confirm('{{ text_confirm }}');" class="dropdown-item"><i class="fas fa-ban fa-fw"></i> {{ button_cancel }}</a>
                                {% endif %}
                              </div>
                            </div>
                          </td>
                        </tr>
                      {% endfor %}
                    {% else %}
                      <tr>
                        <td class="text-center" colspan="9">{{ text_no_results }}</td>
                      </tr>
                    {% endif %}
                  </tbody>
                </table>
              </div>
            </form>
            <div class="row">
              <div class="col-sm-6 text-left">{{ pagination }}</div>
              <div class="col-sm-6 text-right">{{ results }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<script type="text/javascript">
  $('#button-filter').on('click', function() {
    var url = 'index.php?route=purchase/purchase_return&user_token={{ user_token }}';

    var filter_return_number = $('input[name=\'filter_return_number\']').val();
    if (filter_return_number) {
      url += '&filter_return_id=' + encodeURIComponent(filter_return_number);
    }

    var filter_order_number = $('input[name=\'filter_order_number\']').val();
    if (filter_order_number) {
      url += '&filter_po_id=' + encodeURIComponent(filter_order_number);
    }

    var filter_receipt_number = $('input[name=\'filter_receipt_number\']').val();
    if (filter_receipt_number) {
      url += '&filter_receipt_number=' + encodeURIComponent(filter_receipt_number);
    }

    var filter_supplier = $('input[name=\'filter_supplier\']').val();
    if (filter_supplier) {
      url += '&filter_supplier=' + encodeURIComponent(filter_supplier);
    }

    var filter_status = $('select[name=\'filter_status\']').val();
    if (filter_status) {
      url += '&filter_status=' + encodeURIComponent(filter_status);
    }

    var filter_date_added = $('input[name=\'filter_date_added\']').val();
    if (filter_date_added) {
      url += '&filter_date_start=' + encodeURIComponent(filter_date_added);
    }

    location = url;
  });

  // Date picker
  $('.date').datetimepicker({
    'format': 'YYYY-MM-DD',
    'locale': 'ar',
    'allowInputToggle': true
  });
</script>
{{ footer }} 