{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="submit" form="form-supplier-group" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fa fa-save"></i></button>
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pencil"></i> {{ text_form }}</h3>
      </div>
      <div class="panel-body">
        <form action="{{ action }}" method="post" enctype="multipart/form-data" id="form-supplier-group" class="form-horizontal">
          <ul class="nav nav-tabs">
            <li class="active"><a href="#tab-general" data-toggle="tab">{{ tab_general }}</a></li>
            <li><a href="#tab-data" data-toggle="tab">{{ tab_data }}</a></li>
          </ul>
          <div class="tab-content">
            <div class="tab-pane active" id="tab-general">
              <ul class="nav nav-tabs" id="language">
                {% for language in languages %}
                <li{% if loop.first %} class="active"{% endif %}><a href="#language{{ language.language_id }}" data-toggle="tab"><img src="language/{{ language.code }}/{{ language.code }}.png" title="{{ language.name }}" /> {{ language.name }}</a></li>
                {% endfor %}
              </ul>
              <div class="tab-content">
                {% for language in languages %}
                <div class="tab-pane{% if loop.first %} active{% endif %}" id="language{{ language.language_id }}">
                  <div class="form-group required">
                    <label class="col-sm-2 control-label" for="input-name{{ language.language_id }}">{{ entry_name }}</label>
                    <div class="col-sm-10">
                      <input type="text" name="supplier_group_description[{{ language.language_id }}][name]" value="{{ supplier_group_description[language.language_id] ? supplier_group_description[language.language_id].name }}" placeholder="{{ entry_name }}" id="input-name{{ language.language_id }}" class="form-control" />
                      {% if error_name[language.language_id] %}
                      <div class="text-danger">{{ error_name[language.language_id] }}</div>
                      {% endif %}
                    </div>
                  </div>
                  <div class="form-group">
                    <label class="col-sm-2 control-label" for="input-description{{ language.language_id }}">{{ entry_description }}</label>
                    <div class="col-sm-10">
                      <textarea name="supplier_group_description[{{ language.language_id }}][description]" rows="5" placeholder="{{ entry_description }}" id="input-description{{ language.language_id }}" class="form-control">{{ supplier_group_description[language.language_id] ? supplier_group_description[language.language_id].description }}</textarea>
                    </div>
                  </div>
                </div>
                {% endfor %}
              </div>
            </div>
            <div class="tab-pane" id="tab-data">
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-approval"><span data-toggle="tooltip" title="{{ help_approval }}">{{ entry_approval }}</span></label>
                <div class="col-sm-10">
                  <select name="approval" id="input-approval" class="form-control">
                    {% if approval %}
                    <option value="1" selected="selected">{{ text_yes }}</option>
                    <option value="0">{{ text_no }}</option>
                    {% else %}
                    <option value="1">{{ text_yes }}</option>
                    <option value="0" selected="selected">{{ text_no }}</option>
                    {% endif %}
                  </select>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-sort-order"><span data-toggle="tooltip" title="{{ help_sort_order }}">{{ entry_sort_order }}</span></label>
                <div class="col-sm-10">
                  <input type="text" name="sort_order" value="{{ sort_order }}" placeholder="{{ entry_sort_order }}" id="input-sort-order" class="form-control" />
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript"><!--
$('#language a:first').tab('show');
//--></script>
{{ footer }}
