<?php
// Text
$_['text_title']				= 'Credit Card / Debit Card (Opayo)';
$_['text_credit_card']			= 'Card Details';
$_['text_card_type']			= 'Card Type: ';
$_['text_card_name']			= 'Card Name: ';
$_['text_card_digits']			= 'Last Digits: ';
$_['text_card_expiry']			= 'Expiry: ';
$_['text_trial']				= '%s every %s %s for %s payments then ';
$_['text_recurring']			= '%s every %s %s';
$_['text_length']				= ' for %s payments';
$_['text_fail_card']			= 'There was an issue removing your Opayo card, Please contact the shop administrator for help.';
$_['text_confirm_delete']		= 'Are you sure you want to delete the card?';

// Entry
$_['entry_card']				= 'New or Existing Card: ';
$_['entry_card_existing']		= 'Existing';
$_['entry_card_new']			= 'New';
$_['entry_card_save']			= 'Remember Card Details';
$_['entry_card_owner']			= 'Card Owner';
$_['entry_card_type']			= 'Card Type';
$_['entry_card_number']			= 'Card Number';
$_['entry_card_expire_date']	= 'Card Expiry Date';
$_['entry_card_cvv2']			= 'Card Security Code (CVV2)';
$_['entry_card_choice']			= 'Choose an Existing Card';

// Button
$_['button_delete_card']		= 'Delete selected card';