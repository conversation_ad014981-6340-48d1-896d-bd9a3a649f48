<?php
/**
 * نموذج المصادقة الثنائية (2FA) - AYM ERP
 * 
 * @package    AYM ERP
 * <AUTHOR> Team
 * @copyright  2025 AYM ERP
 * @license    Commercial
 * @version    1.0.0
 * @since      File available since Release 1.0.0
 */

class ModelUserTwoFactorAuth extends Model {
    
    /**
     * تفعيل المصادقة الثنائية للمستخدم
     */
    public function enableTwoFactor($user_id, $secret_key, $backup_codes = []) {
        try {
            // تشفير المفتاح السري
            $encrypted_secret = $this->encryption->encrypt($secret_key);
            
            // تشفير رموز النسخ الاحتياطي
            $encrypted_backup_codes = $this->encryption->encrypt(json_encode($backup_codes));
            
            $sql = "UPDATE " . DB_PREFIX . "user SET 
                    two_factor_enabled = 1,
                    two_factor_secret = '" . $this->db->escape($encrypted_secret) . "',
                    two_factor_backup_codes = '" . $this->db->escape($encrypted_backup_codes) . "'
                    WHERE user_id = '" . (int)$user_id . "'";
            
            $this->db->query($sql);
            
            // تسجيل النشاط
            $this->load->model('activity_log');
            $this->model_activity_log->addActivity('2fa_enabled', 'تم تفعيل المصادقة الثنائية', $user_id);
            
            return true;
        } catch (Exception $e) {
            $this->log->write('2FA Enable Error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * إلغاء تفعيل المصادقة الثنائية
     */
    public function disableTwoFactor($user_id) {
        try {
            $sql = "UPDATE " . DB_PREFIX . "user SET 
                    two_factor_enabled = 0,
                    two_factor_secret = NULL,
                    two_factor_backup_codes = NULL,
                    two_factor_last_used = NULL
                    WHERE user_id = '" . (int)$user_id . "'";
            
            $this->db->query($sql);
            
            // حذف الأجهزة الموثوقة
            $this->db->query("DELETE FROM " . DB_PREFIX . "user_trusted_devices WHERE user_id = '" . (int)$user_id . "'");
            
            // تسجيل النشاط
            $this->load->model('activity_log');
            $this->model_activity_log->addActivity('2fa_disabled', 'تم إلغاء تفعيل المصادقة الثنائية', $user_id);
            
            return true;
        } catch (Exception $e) {
            $this->log->write('2FA Disable Error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * التحقق من رمز TOTP
     */
    public function verifyTOTP($user_id, $code) {
        try {
            $user_info = $this->getUserTwoFactorInfo($user_id);
            
            if (!$user_info || !$user_info['two_factor_enabled']) {
                return false;
            }
            
            // فك تشفير المفتاح السري
            $secret_key = $this->encryption->decrypt($user_info['two_factor_secret']);
            
            // التحقق من الرمز باستخدام مكتبة TOTP
            require_once(DIR_SYSTEM . 'library/totp.php');
            $totp = new TOTP($secret_key);
            
            $is_valid = $totp->verify($code, 2); // السماح بـ 2 نافذة زمنية
            
            // تسجيل المحاولة
            $this->logTwoFactorAttempt($user_id, 'totp', $code, $is_valid);
            
            if ($is_valid) {
                // تحديث آخر استخدام
                $this->db->query("UPDATE " . DB_PREFIX . "user SET two_factor_last_used = NOW() WHERE user_id = '" . (int)$user_id . "'");
            }
            
            return $is_valid;
        } catch (Exception $e) {
            $this->log->write('TOTP Verification Error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * التحقق من رمز النسخ الاحتياطي
     */
    public function verifyBackupCode($user_id, $code) {
        try {
            $user_info = $this->getUserTwoFactorInfo($user_id);
            
            if (!$user_info || !$user_info['two_factor_backup_codes']) {
                return false;
            }
            
            // فك تشفير رموز النسخ الاحتياطي
            $backup_codes = json_decode($this->encryption->decrypt($user_info['two_factor_backup_codes']), true);
            
            if (!is_array($backup_codes)) {
                return false;
            }
            
            // البحث عن الرمز
            $code_index = array_search($code, $backup_codes);
            
            if ($code_index !== false) {
                // حذف الرمز المستخدم
                unset($backup_codes[$code_index]);
                
                // تحديث الرموز في قاعدة البيانات
                $encrypted_codes = $this->encryption->encrypt(json_encode(array_values($backup_codes)));
                $this->db->query("UPDATE " . DB_PREFIX . "user SET two_factor_backup_codes = '" . $this->db->escape($encrypted_codes) . "' WHERE user_id = '" . (int)$user_id . "'");
                
                // تسجيل المحاولة
                $this->logTwoFactorAttempt($user_id, 'backup', $code, true);
                
                // تحديث آخر استخدام
                $this->db->query("UPDATE " . DB_PREFIX . "user SET two_factor_last_used = NOW() WHERE user_id = '" . (int)$user_id . "'");
                
                return true;
            }
            
            // تسجيل المحاولة الفاشلة
            $this->logTwoFactorAttempt($user_id, 'backup', $code, false);
            
            return false;
        } catch (Exception $e) {
            $this->log->write('Backup Code Verification Error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * إرسال رمز التحقق عبر الرسائل النصية
     */
    public function sendSMSCode($user_id) {
        try {
            $user_info = $this->getUserInfo($user_id);
            
            if (!$user_info || !$user_info['phone_number'] || !$user_info['phone_verified']) {
                return false;
            }
            
            // توليد رمز عشوائي
            $code = sprintf('%06d', mt_rand(100000, 999999));
            
            // حفظ الرمز في قاعدة البيانات
            $this->saveVerificationCode($user_id, $code, 'sms', '2fa');
            
            // إرسال الرسالة النصية
            $message = $this->getMessageTemplate('sms', 'ar', '2fa_code');
            $message_body = str_replace(['{code}', '{expiry_minutes}', '{user_name}'], 
                                     [$code, '5', $user_info['firstname']], 
                                     $message['message_body']);
            
            // استخدام خدمة الرسائل النصية
            $this->load->model('communication/sms');
            $sent = $this->model_communication_sms->sendSMS($user_info['phone_number'], $message_body);
            
            if ($sent) {
                $this->logTwoFactorAttempt($user_id, 'sms', $code, true, 'SMS sent successfully');
                return true;
            }
            
            return false;
        } catch (Exception $e) {
            $this->log->write('SMS Code Error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * إرسال رمز التحقق عبر البريد الإلكتروني
     */
    public function sendEmailCode($user_id) {
        try {
            $user_info = $this->getUserInfo($user_id);
            
            if (!$user_info || !$user_info['email']) {
                return false;
            }
            
            // توليد رمز عشوائي
            $code = sprintf('%06d', mt_rand(100000, 999999));
            
            // حفظ الرمز في قاعدة البيانات
            $this->saveVerificationCode($user_id, $code, 'email', '2fa');
            
            // إرسال البريد الإلكتروني
            $template = $this->getMessageTemplate('email', 'ar', '2fa_code');
            $subject = str_replace(['{user_name}'], [$user_info['firstname']], $template['subject']);
            $message_body = str_replace(['{code}', '{expiry_minutes}', '{user_name}'], 
                                     [$code, '5', $user_info['firstname']], 
                                     $template['message_body']);
            
            // استخدام خدمة البريد الإلكتروني
            $this->load->model('communication/email');
            $sent = $this->model_communication_email->sendEmail($user_info['email'], $subject, $message_body);
            
            if ($sent) {
                $this->logTwoFactorAttempt($user_id, 'email', $code, true, 'Email sent successfully');
                return true;
            }
            
            return false;
        } catch (Exception $e) {
            $this->log->write('Email Code Error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * التحقق من رمز التحقق (SMS/Email)
     */
    public function verifyCode($user_id, $code, $code_type) {
        try {
            $sql = "SELECT * FROM " . DB_PREFIX . "user_verification_codes 
                    WHERE user_id = '" . (int)$user_id . "' 
                    AND code = '" . $this->db->escape($code) . "' 
                    AND code_type = '" . $this->db->escape($code_type) . "' 
                    AND purpose = '2fa'
                    AND is_used = 0 
                    AND expires_at > NOW() 
                    AND attempts_count < max_attempts
                    ORDER BY created_at DESC 
                    LIMIT 1";
            
            $query = $this->db->query($sql);
            
            if ($query->num_rows) {
                // تحديث الرمز كمستخدم
                $code_id = $query->row['code_id'];
                $this->db->query("UPDATE " . DB_PREFIX . "user_verification_codes SET 
                                is_used = 1, 
                                used_at = NOW() 
                                WHERE code_id = '" . (int)$code_id . "'");
                
                // تسجيل المحاولة الناجحة
                $this->logTwoFactorAttempt($user_id, $code_type, $code, true);
                
                return true;
            } else {
                // زيادة عدد المحاولات
                $this->db->query("UPDATE " . DB_PREFIX . "user_verification_codes SET 
                                attempts_count = attempts_count + 1 
                                WHERE user_id = '" . (int)$user_id . "' 
                                AND code_type = '" . $this->db->escape($code_type) . "' 
                                AND purpose = '2fa'
                                AND is_used = 0");
                
                // تسجيل المحاولة الفاشلة
                $this->logTwoFactorAttempt($user_id, $code_type, $code, false, 'Invalid or expired code');
                
                return false;
            }
        } catch (Exception $e) {
            $this->log->write('Code Verification Error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * إضافة جهاز موثوق
     */
    public function addTrustedDevice($user_id, $device_fingerprint, $device_name, $location = '') {
        try {
            $expires_at = date('Y-m-d H:i:s', strtotime('+' . $this->getTwoFactorSetting('2fa_trusted_device_days', 30) . ' days'));
            
            $sql = "INSERT INTO " . DB_PREFIX . "user_trusted_devices SET
                    user_id = '" . (int)$user_id . "',
                    device_fingerprint = '" . $this->db->escape($device_fingerprint) . "',
                    device_name = '" . $this->db->escape($device_name) . "',
                    ip_address = '" . $this->db->escape($this->request->server['REMOTE_ADDR']) . "',
                    user_agent = '" . $this->db->escape($this->request->server['HTTP_USER_AGENT']) . "',
                    location = '" . $this->db->escape($location) . "',
                    expires_at = '" . $expires_at . "'
                    ON DUPLICATE KEY UPDATE
                    last_used_at = NOW(),
                    expires_at = '" . $expires_at . "'";
            
            $this->db->query($sql);
            
            return true;
        } catch (Exception $e) {
            $this->log->write('Add Trusted Device Error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * التحقق من الجهاز الموثوق
     */
    public function isTrustedDevice($user_id, $device_fingerprint) {
        $sql = "SELECT device_id FROM " . DB_PREFIX . "user_trusted_devices 
                WHERE user_id = '" . (int)$user_id . "' 
                AND device_fingerprint = '" . $this->db->escape($device_fingerprint) . "' 
                AND is_active = 1 
                AND expires_at > NOW()";
        
        $query = $this->db->query($sql);
        
        if ($query->num_rows) {
            // تحديث آخر استخدام
            $this->db->query("UPDATE " . DB_PREFIX . "user_trusted_devices SET last_used_at = NOW() WHERE device_id = '" . (int)$query->row['device_id'] . "'");
            return true;
        }
        
        return false;
    }
    
    /**
     * الحصول على معلومات المصادقة الثنائية للمستخدم
     */
    public function getUserTwoFactorInfo($user_id) {
        $sql = "SELECT user_id, two_factor_enabled, two_factor_secret, two_factor_backup_codes, two_factor_last_used, phone_number, phone_verified 
                FROM " . DB_PREFIX . "user 
                WHERE user_id = '" . (int)$user_id . "'";
        
        $query = $this->db->query($sql);
        
        return $query->num_rows ? $query->row : false;
    }
    
    /**
     * الحصول على معلومات المستخدم
     */
    private function getUserInfo($user_id) {
        $sql = "SELECT user_id, username, firstname, lastname, email, phone_number, phone_verified 
                FROM " . DB_PREFIX . "user 
                WHERE user_id = '" . (int)$user_id . "'";
        
        $query = $this->db->query($sql);
        
        return $query->num_rows ? $query->row : false;
    }
    
    /**
     * حفظ رمز التحقق
     */
    private function saveVerificationCode($user_id, $code, $code_type, $purpose) {
        $expires_at = date('Y-m-d H:i:s', strtotime('+' . $this->getTwoFactorSetting('2fa_code_expiry_minutes', 5) . ' minutes'));
        
        $sql = "INSERT INTO " . DB_PREFIX . "user_verification_codes SET
                user_id = '" . (int)$user_id . "',
                code = '" . $this->db->escape($code) . "',
                code_type = '" . $this->db->escape($code_type) . "',
                purpose = '" . $this->db->escape($purpose) . "',
                expires_at = '" . $expires_at . "',
                ip_address = '" . $this->db->escape($this->request->server['REMOTE_ADDR']) . "'";
        
        $this->db->query($sql);
    }
    
    /**
     * تسجيل محاولة المصادقة الثنائية
     */
    private function logTwoFactorAttempt($user_id, $attempt_type, $code, $is_successful, $failure_reason = '') {
        $sql = "INSERT INTO " . DB_PREFIX . "user_2fa_attempts SET
                user_id = '" . (int)$user_id . "',
                attempt_type = '" . $this->db->escape($attempt_type) . "',
                code_entered = '" . $this->db->escape($code) . "',
                is_successful = '" . (int)$is_successful . "',
                ip_address = '" . $this->db->escape($this->request->server['REMOTE_ADDR']) . "',
                user_agent = '" . $this->db->escape($this->request->server['HTTP_USER_AGENT']) . "',
                failure_reason = '" . $this->db->escape($failure_reason) . "'";
        
        $this->db->query($sql);
    }
    
    /**
     * الحصول على قالب الرسالة
     */
    private function getMessageTemplate($template_type, $language_code, $purpose) {
        $sql = "SELECT * FROM " . DB_PREFIX . "2fa_message_templates 
                WHERE template_type = '" . $this->db->escape($template_type) . "' 
                AND language_code = '" . $this->db->escape($language_code) . "' 
                AND purpose = '" . $this->db->escape($purpose) . "' 
                AND is_active = 1";
        
        $query = $this->db->query($sql);
        
        return $query->num_rows ? $query->row : false;
    }
    
    /**
     * الحصول على إعداد المصادقة الثنائية
     */
    private function getTwoFactorSetting($key, $default = '') {
        $sql = "SELECT setting_value FROM " . DB_PREFIX . "2fa_settings 
                WHERE setting_key = '" . $this->db->escape($key) . "' 
                AND is_active = 1";
        
        $query = $this->db->query($sql);
        
        return $query->num_rows ? $query->row['setting_value'] : $default;
    }
    
    /**
     * توليد مفتاح سري جديد
     */
    public function generateSecretKey() {
        require_once(DIR_SYSTEM . 'library/totp.php');
        return TOTP::generateSecret();
    }
    
    /**
     * توليد رموز النسخ الاحتياطي
     */
    public function generateBackupCodes($count = 10) {
        $codes = [];
        for ($i = 0; $i < $count; $i++) {
            $codes[] = sprintf('%08d', mt_rand(10000000, 99999999));
        }
        return $codes;
    }
    
    /**
     * الحصول على QR Code للمصادقة الثنائية
     */
    public function getQRCodeURL($user_id, $secret_key) {
        $user_info = $this->getUserInfo($user_id);
        $issuer = $this->getTwoFactorSetting('2fa_totp_issuer', 'AYM ERP');
        
        $label = $issuer . ':' . $user_info['username'];
        $qr_url = 'otpauth://totp/' . urlencode($label) . '?secret=' . $secret_key . '&issuer=' . urlencode($issuer);
        
        return 'https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=' . urlencode($qr_url);
    }
}
?>
