<?php
/**
 * كونترولر إعداد المصادقة الثنائية - AYM ERP
 * 
 * @package    AYM ERP
 * <AUTHOR> Team
 * @copyright  2025 AYM ERP
 * @license    Commercial
 * @version    1.0.0
 */

class ControllerUserTwoFactorSetup extends Controller {
    private $error = array();

    public function index() {
        $this->load->language('user/two_factor_setup');
        $this->load->model('user/two_factor_auth');
        $this->load->model('user/user');

        $this->document->setTitle($this->language->get('heading_title'));

        // التحقق من الصلاحيات
        if (!$this->user->hasPermission('modify', 'user/two_factor_setup')) {
            $this->session->data['error'] = $this->language->get('error_permission');
            $this->response->redirect($this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true));
        }

        $user_id = $this->user->getId();
        $user_info = $this->model_user_two_factor_auth->getUserTwoFactorInfo($user_id);

        // معالجة تفعيل المصادقة الثنائية
        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateSetup()) {
            $secret_key = $this->session->data['temp_2fa_secret'];
            $backup_codes = $this->model_user_two_factor_auth->generateBackupCodes(10);

            if ($this->model_user_two_factor_auth->enableTwoFactor($user_id, $secret_key, $backup_codes)) {
                // حذف المفتاح المؤقت
                unset($this->session->data['temp_2fa_secret']);

                $this->session->data['success'] = $this->language->get('success_enabled');
                $this->session->data['backup_codes'] = $backup_codes;
                
                $this->response->redirect($this->url->link('user/two_factor_setup/success', 'user_token=' . $this->session->data['user_token'], true));
            } else {
                $this->error['warning'] = $this->language->get('error_enable_failed');
            }
        }

        // إعداد البيانات للعرض
        $data['action'] = $this->url->link('user/two_factor_setup', 'user_token=' . $this->session->data['user_token'], true);
        $data['cancel'] = $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true);

        // رسائل الخطأ
        if (isset($this->error['warning'])) {
            $data['error_warning'] = $this->error['warning'];
        } else {
            $data['error_warning'] = '';
        }

        // رسائل النجاح
        if (isset($this->session->data['success'])) {
            $data['success'] = $this->session->data['success'];
            unset($this->session->data['success']);
        } else {
            $data['success'] = '';
        }

        // حالة المصادقة الثنائية
        $data['two_factor_enabled'] = $user_info['two_factor_enabled'] ?? false;

        if (!$data['two_factor_enabled']) {
            // توليد مفتاح سري جديد إذا لم يكن موجوداً
            if (!isset($this->session->data['temp_2fa_secret'])) {
                $this->session->data['temp_2fa_secret'] = $this->model_user_two_factor_auth->generateSecretKey();
            }

            $secret_key = $this->session->data['temp_2fa_secret'];
            $user_details = $this->model_user_user->getUser($user_id);
            
            // إنشاء QR Code
            require_once(DIR_SYSTEM . 'library/totp.php');
            $totp = new TOTP($secret_key);
            $label = 'AYM ERP:' . $user_details['username'];
            
            $data['secret_key'] = $secret_key;
            $data['qr_code_url'] = $totp->getQRCodeURL($label, 'AYM ERP');
            $data['manual_entry_key'] = chunk_split($secret_key, 4, ' ');
        }

        // معلومات المستخدم
        $data['username'] = $this->user->getUserName();
        $data['phone_number'] = $user_info['phone_number'] ?? '';
        $data['phone_verified'] = $user_info['phone_verified'] ?? false;

        // متغيرات اللغة
        $data['heading_title'] = $this->language->get('heading_title');
        $data['text_setup'] = $this->language->get('text_setup');
        $data['text_enabled'] = $this->language->get('text_enabled');
        $data['text_disabled'] = $this->language->get('text_disabled');
        $data['text_step1'] = $this->language->get('text_step1');
        $data['text_step2'] = $this->language->get('text_step2');
        $data['text_step3'] = $this->language->get('text_step3');
        $data['entry_verification_code'] = $this->language->get('entry_verification_code');
        $data['button_enable'] = $this->language->get('button_enable');
        $data['button_disable'] = $this->language->get('button_disable');
        $data['button_cancel'] = $this->language->get('button_cancel');

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('user/two_factor_setup', $data));
    }

    /**
     * صفحة النجاح مع رموز النسخ الاحتياطي
     */
    public function success() {
        $this->load->language('user/two_factor_setup');

        $this->document->setTitle($this->language->get('heading_title'));

        if (!isset($this->session->data['backup_codes'])) {
            $this->response->redirect($this->url->link('user/two_factor_setup', 'user_token=' . $this->session->data['user_token'], true));
        }

        $data['backup_codes'] = $this->session->data['backup_codes'];
        unset($this->session->data['backup_codes']);

        $data['continue'] = $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true);

        // متغيرات اللغة
        $data['heading_title'] = $this->language->get('heading_title');
        $data['text_success'] = $this->language->get('text_success');
        $data['text_backup_codes'] = $this->language->get('text_backup_codes');
        $data['text_backup_warning'] = $this->language->get('text_backup_warning');
        $data['button_continue'] = $this->language->get('button_continue');
        $data['button_download'] = $this->language->get('button_download');
        $data['button_print'] = $this->language->get('button_print');

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('user/two_factor_success', $data));
    }

    /**
     * إلغاء تفعيل المصادقة الثنائية
     */
    public function disable() {
        $this->load->language('user/two_factor_setup');
        $this->load->model('user/two_factor_auth');

        $json = array();

        if (!$this->user->hasPermission('modify', 'user/two_factor_setup')) {
            $json['error'] = $this->language->get('error_permission');
        } else {
            $user_id = $this->user->getId();
            
            if ($this->model_user_two_factor_auth->disableTwoFactor($user_id)) {
                $json['success'] = $this->language->get('success_disabled');
            } else {
                $json['error'] = $this->language->get('error_disable_failed');
            }
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * تحديث رقم الهاتف
     */
    public function updatePhone() {
        $this->load->language('user/two_factor_setup');
        $this->load->model('user/user');

        $json = array();

        if (!$this->user->hasPermission('modify', 'user/two_factor_setup')) {
            $json['error'] = $this->language->get('error_permission');
        } elseif (!isset($this->request->post['phone_number']) || !$this->request->post['phone_number']) {
            $json['error'] = $this->language->get('error_phone_required');
        } else {
            $phone_number = $this->request->post['phone_number'];
            
            // تحديث رقم الهاتف
            $this->db->query("UPDATE " . DB_PREFIX . "user SET 
                            phone_number = '" . $this->db->escape($phone_number) . "',
                            phone_verified = 0
                            WHERE user_id = '" . (int)$this->user->getId() . "'");
            
            $json['success'] = $this->language->get('success_phone_updated');
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * التحقق من رقم الهاتف
     */
    public function verifyPhone() {
        $this->load->language('user/two_factor_setup');
        $this->load->model('user/two_factor_auth');

        $json = array();

        if (!$this->user->hasPermission('modify', 'user/two_factor_setup')) {
            $json['error'] = $this->language->get('error_permission');
        } else {
            $user_id = $this->user->getId();
            
            if ($this->model_user_two_factor_auth->sendSMSCode($user_id)) {
                $json['success'] = $this->language->get('success_verification_sent');
            } else {
                $json['error'] = $this->language->get('error_verification_failed');
            }
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * التحقق من صحة الإعداد
     */
    protected function validateSetup() {
        if (!isset($this->request->post['verification_code']) || !$this->request->post['verification_code']) {
            $this->error['warning'] = $this->language->get('error_verification_required');
            return false;
        }

        if (!isset($this->session->data['temp_2fa_secret'])) {
            $this->error['warning'] = $this->language->get('error_session_expired');
            return false;
        }

        // التحقق من الرمز
        $secret_key = $this->session->data['temp_2fa_secret'];
        $code = $this->request->post['verification_code'];

        require_once(DIR_SYSTEM . 'library/totp.php');
        $totp = new TOTP($secret_key);

        if (!$totp->verify($code, 2)) {
            $this->error['warning'] = $this->language->get('error_verification_invalid');
            return false;
        }

        return true;
    }
}
?>
