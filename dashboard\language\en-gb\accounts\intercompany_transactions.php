<?php
// Heading
$_['heading_title']                    = 'Intercompany Transactions';

// Text
$_['text_success']                     = 'Success: You have modified intercompany transactions!';
$_['text_list']                        = 'Intercompany Transactions List';
$_['text_add']                         = 'Add Transaction';
$_['text_edit']                        = 'Edit Transaction';
$_['text_view']                        = 'View Transaction';
$_['text_default']                     = 'Default';
$_['text_enabled']                     = 'Enabled';
$_['text_disabled']                    = 'Disabled';
$_['text_yes']                         = 'Yes';
$_['text_no']                          = 'No';
$_['text_none']                        = 'None';
$_['text_select']                      = 'Select';
$_['text_loading']                     = 'Loading...';
$_['text_no_results']                  = 'No results!';
$_['text_confirm']                     = 'Are you sure?';

// Transaction specific
$_['text_transaction_info']            = 'Transaction Information';
$_['text_transaction_reference']       = 'Transaction Reference';
$_['text_transaction_type']            = 'Transaction Type';
$_['text_transaction_date']            = 'Transaction Date';
$_['text_from_company']                = 'From Company';
$_['text_to_company']                  = 'To Company';
$_['text_transaction_amount']          = 'Transaction Amount';
$_['text_transaction_currency']        = 'Transaction Currency';
$_['text_exchange_rate']               = 'Exchange Rate';
$_['text_base_amount']                 = 'Base Amount';
$_['text_account']                     = 'Account';
$_['text_contra_account']              = 'Contra Account';
$_['text_description']                 = 'Description';
$_['text_notes']                       = 'Notes';

// Transaction Types
$_['text_type_sale']                   = 'Sale';
$_['text_type_purchase']               = 'Purchase';
$_['text_type_loan']                   = 'Loan';
$_['text_type_dividend']               = 'Dividend';
$_['text_type_management_fee']         = 'Management Fee';
$_['text_type_royalty']                = 'Royalty';
$_['text_type_interest']               = 'Interest';
$_['text_type_rent']                   = 'Rent';
$_['text_type_other']                  = 'Other';

// Status
$_['text_status_pending']              = 'Pending';
$_['text_status_matched']              = 'Matched';
$_['text_status_eliminated']           = 'Eliminated';
$_['text_status_reconciled']           = 'Reconciled';
$_['text_status_cancelled']            = 'Cancelled';

// Matching
$_['text_matching_info']               = 'Matching Information';
$_['text_match_type']                  = 'Match Type';
$_['text_tolerance_amount']            = 'Tolerance Amount';
$_['text_matched_transaction']         = 'Matched Transaction';
$_['text_match_date']                  = 'Match Date';
$_['text_matched_by']                  = 'Matched By';
$_['text_auto_match']                  = 'Auto Match';
$_['text_manual_match']                = 'Manual Match';

// Elimination
$_['text_elimination_info']            = 'Elimination Information';
$_['text_elimination_type']            = 'Elimination Type';
$_['text_elimination_method']          = 'Elimination Method';
$_['text_elimination_amount']          = 'Elimination Amount';
$_['text_elimination_date']            = 'Elimination Date';
$_['text_eliminated_by']               = 'Eliminated By';
$_['text_full_elimination']            = 'Full Elimination';
$_['text_partial_elimination']         = 'Partial Elimination';

// Statistics
$_['text_total_transactions']          = 'Total Transactions';
$_['text_pending_transactions']        = 'Pending Transactions';
$_['text_matched_transactions']        = 'Matched Transactions';
$_['text_eliminated_transactions']     = 'Eliminated Transactions';
$_['text_total_amount']                = 'Total Amount';
$_['text_elimination_impact']          = 'Elimination Impact';

// Buttons
$_['button_add']                       = 'Add';
$_['button_edit']                      = 'Edit';
$_['button_delete']                    = 'Delete';
$_['button_save']                      = 'Save';
$_['button_cancel']                    = 'Cancel';
$_['button_close']                     = 'Close';
$_['button_back']                      = 'Back';
$_['button_view']                      = 'View';
$_['button_print']                     = 'Print';
$_['button_export']                    = 'Export';
$_['button_match']                     = 'Match';
$_['button_eliminate']                 = 'Eliminate';
$_['button_reconcile']                 = 'Reconcile';
$_['button_auto_match']                = 'Auto Match';
$_['button_generate_report']           = 'Generate Report';

// Columns
$_['column_reference']                 = 'Reference';
$_['column_transaction_type']          = 'Type';
$_['column_transaction_date']          = 'Date';
$_['column_from_company']              = 'From Company';
$_['column_to_company']                = 'To Company';
$_['column_amount']                    = 'Amount';
$_['column_currency']                  = 'Currency';
$_['column_status']                    = 'Status';
$_['column_match_status']              = 'Match Status';
$_['column_created_by']                = 'Created By';
$_['column_date_created']              = 'Date Created';
$_['column_action']                    = 'Action';

// Entry fields
$_['entry_reference']                  = 'Transaction Reference';
$_['entry_transaction_type']           = 'Transaction Type';
$_['entry_transaction_date']           = 'Transaction Date';
$_['entry_from_company']               = 'From Company';
$_['entry_to_company']                 = 'To Company';
$_['entry_amount']                     = 'Amount';
$_['entry_currency']                   = 'Currency';
$_['entry_exchange_rate']              = 'Exchange Rate';
$_['entry_account']                    = 'Account';
$_['entry_description']                = 'Description';
$_['entry_notes']                      = 'Notes';
$_['entry_tolerance_amount']           = 'Tolerance Amount';

// Help text
$_['help_reference']                   = 'Enter a unique reference for the transaction';
$_['help_transaction_type']            = 'Select the appropriate transaction type';
$_['help_from_to_company']             = 'Select the companies involved in the transaction';
$_['help_tolerance_amount']            = 'Tolerance amount allowed for matching';

// Error messages
$_['error_permission']                 = 'Warning: You do not have permission to access intercompany transactions!';
$_['error_reference']                  = 'Transaction reference must be between 3 and 64 characters!';
$_['error_transaction_type']           = 'Please select transaction type!';
$_['error_transaction_date']           = 'Please enter transaction date!';
$_['error_from_company']               = 'Please select from company!';
$_['error_to_company']                 = 'Please select to company!';
$_['error_same_company']               = 'From and to companies cannot be the same!';
$_['error_amount']                     = 'Please enter a valid amount!';
$_['error_currency']                   = 'Please select currency!';
$_['error_account']                    = 'Please select account!';
$_['error_duplicate_reference']        = 'Transaction reference already exists!';
$_['error_invalid_date']               = 'Invalid date!';
$_['error_future_date']                = 'Date cannot be in the future!';
$_['error_no_match_found']             = 'No matching transaction found!';
$_['error_already_matched']            = 'Transaction is already matched!';
$_['error_already_eliminated']         = 'Transaction is already eliminated!';

// Success messages
$_['success_transaction_added']        = 'Transaction added successfully!';
$_['success_transaction_updated']      = 'Transaction updated successfully!';
$_['success_transaction_deleted']      = 'Transaction deleted successfully!';
$_['success_transaction_matched']      = 'Transaction matched successfully!';
$_['success_transaction_eliminated']   = 'Transaction eliminated successfully!';
$_['success_auto_match_completed']     = 'Auto matching completed successfully!';

// Confirmation messages
$_['confirm_delete']                   = 'Are you sure you want to delete this transaction?';
$_['confirm_match']                    = 'Are you sure you want to match this transaction?';
$_['confirm_eliminate']                = 'Are you sure you want to eliminate this transaction?';
$_['confirm_auto_match']               = 'Are you sure you want to run auto matching?';

// Tabs
$_['tab_general']                      = 'General';
$_['tab_matching']                     = 'Matching';
$_['tab_elimination']                  = 'Elimination';
$_['tab_journal_entries']              = 'Journal Entries';
$_['tab_history']                      = 'History';

// Reports
$_['text_transaction_report']          = 'Transaction Report';
$_['text_matching_report']             = 'Matching Report';
$_['text_elimination_report']          = 'Elimination Report';
$_['text_reconciliation_report']       = 'Reconciliation Report';
$_['text_company_analysis']            = 'Company Analysis';

// Filters
$_['text_filter_company']              = 'Filter by Company';
$_['text_filter_type']                 = 'Filter by Type';
$_['text_filter_status']               = 'Filter by Status';
$_['text_filter_date_range']           = 'Filter by Date Range';
$_['text_filter_amount_range']         = 'Filter by Amount Range';

// Processing status
$_['text_processing']                  = 'Processing...';
$_['text_matching']                    = 'Matching...';
$_['text_eliminating']                 = 'Eliminating...';
$_['text_completed']                   = 'Completed';
$_['text_failed']                      = 'Failed';

// Additional features
$_['text_bulk_operations']             = 'Bulk Operations';
$_['text_bulk_match']                  = 'Bulk Match';
$_['text_bulk_eliminate']              = 'Bulk Eliminate';
$_['text_import_transactions']         = 'Import Transactions';
$_['text_export_transactions']         = 'Export Transactions';
?>
