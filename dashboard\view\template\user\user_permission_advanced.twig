{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="submit" form="user\user_permission_advanced-form" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fas fa-save"></i></button>
        <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-light"><i class="fas fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    
    <div class="card">
      <div class="card-header"><i class="fas fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}chart-bar{% elif screen_type == 'settings' %}cog{% else %}pencil-alt{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="card-body">
        <form id="user\user_permission_advanced-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-access_log_url">{{ text_access_log_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="access_log_url" value="{{ access_log_url }}" placeholder="{{ text_access_log_url }}" id="input-access_log_url" class="form-control" />
              {% if error_access_log_url %}
                <div class="invalid-feedback">{{ error_access_log_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-assign_role_url">{{ text_assign_role_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="assign_role_url" value="{{ assign_role_url }}" placeholder="{{ text_assign_role_url }}" id="input-assign_role_url" class="form-control" />
              {% if error_assign_role_url %}
                <div class="invalid-feedback">{{ error_assign_role_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-can_assign_roles">{{ text_can_assign_roles }}</label>
            <div class="col-sm-10">
              <input type="text" name="can_assign_roles" value="{{ can_assign_roles }}" placeholder="{{ text_can_assign_roles }}" id="input-can_assign_roles" class="form-control" />
              {% if error_can_assign_roles %}
                <div class="invalid-feedback">{{ error_can_assign_roles }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-can_create_roles">{{ text_can_create_roles }}</label>
            <div class="col-sm-10">
              <input type="text" name="can_create_roles" value="{{ can_create_roles }}" placeholder="{{ text_can_create_roles }}" id="input-can_create_roles" class="form-control" />
              {% if error_can_create_roles %}
                <div class="invalid-feedback">{{ error_can_create_roles }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-can_modify_permissions">{{ text_can_modify_permissions }}</label>
            <div class="col-sm-10">
              <input type="text" name="can_modify_permissions" value="{{ can_modify_permissions }}" placeholder="{{ text_can_modify_permissions }}" id="input-can_modify_permissions" class="form-control" />
              {% if error_can_modify_permissions %}
                <div class="invalid-feedback">{{ error_can_modify_permissions }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-create_role_url">{{ text_create_role_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="create_role_url" value="{{ create_role_url }}" placeholder="{{ text_create_role_url }}" id="input-create_role_url" class="form-control" />
              {% if error_create_role_url %}
                <div class="invalid-feedback">{{ error_create_role_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-export_url">{{ text_export_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="export_url" value="{{ export_url }}" placeholder="{{ text_export_url }}" id="input-export_url" class="form-control" />
              {% if error_export_url %}
                <div class="invalid-feedback">{{ error_export_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-get_permissions_url">{{ text_get_permissions_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="get_permissions_url" value="{{ get_permissions_url }}" placeholder="{{ text_get_permissions_url }}" id="input-get_permissions_url" class="form-control" />
              {% if error_get_permissions_url %}
                <div class="invalid-feedback">{{ error_get_permissions_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="invalid-feedback">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-permission_matrix_url">{{ text_permission_matrix_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="permission_matrix_url" value="{{ permission_matrix_url }}" placeholder="{{ text_permission_matrix_url }}" id="input-permission_matrix_url" class="form-control" />
              {% if error_permission_matrix_url %}
                <div class="invalid-feedback">{{ error_permission_matrix_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-roles">{{ text_roles }}</label>
            <div class="col-sm-10">
              <input type="text" name="roles" value="{{ roles }}" placeholder="{{ text_roles }}" id="input-roles" class="form-control" />
              {% if error_roles %}
                <div class="invalid-feedback">{{ error_roles }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-security_report_url">{{ text_security_report_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="security_report_url" value="{{ security_report_url }}" placeholder="{{ text_security_report_url }}" id="input-security_report_url" class="form-control" />
              {% if error_security_report_url %}
                <div class="invalid-feedback">{{ error_security_report_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-update_permissions_url">{{ text_update_permissions_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="update_permissions_url" value="{{ update_permissions_url }}" placeholder="{{ text_update_permissions_url }}" id="input-update_permissions_url" class="form-control" />
              {% if error_update_permissions_url %}
                <div class="invalid-feedback">{{ error_update_permissions_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="invalid-feedback">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-users">{{ text_users }}</label>
            <div class="col-sm-10">
              <input type="text" name="users" value="{{ users }}" placeholder="{{ text_users }}" id="input-users" class="form-control" />
              {% if error_users %}
                <div class="invalid-feedback">{{ error_users }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-validate_permissions_url">{{ text_validate_permissions_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="validate_permissions_url" value="{{ validate_permissions_url }}" placeholder="{{ text_validate_permissions_url }}" id="input-validate_permissions_url" class="form-control" />
              {% if error_validate_permissions_url %}
                <div class="invalid-feedback">{{ error_validate_permissions_url }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}