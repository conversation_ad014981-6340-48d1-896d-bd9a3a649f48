{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        {% if has_permission %}
        <button type="button" id="button-edit" data-toggle="tooltip" title="{{ button_edit }}" class="btn btn-primary">
          <i class="fa fa-pencil"></i> {{ button_edit }}
        </button>
        <button type="button" id="button-regenerate" data-toggle="tooltip" title="{{ button_regenerate }}" class="btn btn-warning">
          <i class="fa fa-refresh"></i> {{ button_regenerate }}
        </button>
        <button type="button" id="button-scenario" data-toggle="tooltip" title="{{ button_scenario }}" class="btn btn-info">
          <i class="fa fa-sitemap"></i> {{ button_scenario }}
        </button>
        <button type="button" id="button-export" data-toggle="tooltip" title="{{ button_export }}" class="btn btn-success">
          <i class="fa fa-download"></i> {{ button_export }}
        </button>
        {% endif %}
        <a href="{{ back }}" data-toggle="tooltip" title="{{ button_back }}" class="btn btn-default">
          <i class="fa fa-reply"></i> {{ button_back }}
        </a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    {% if success %}
    <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    
    <!-- معلومات التنبؤ الأساسية -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-info-circle"></i> {{ text_forecast_info }}</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-md-6">
            <table class="table table-bordered">
              <tr>
                <td><strong>{{ text_forecast_name }}:</strong></td>
                <td>{{ forecast.forecast_name }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_forecast_period }}:</strong></td>
                <td>{{ forecast.forecast_period }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_forecast_method }}:</strong></td>
                <td>
                  {% if forecast.forecast_method == 'ai_prediction' %}
                    <span class="label label-warning">{{ text_ai_prediction }}</span>
                  {% else %}
                    {{ forecast.forecast_method }}
                  {% endif %}
                </td>
              </tr>
              <tr>
                <td><strong>{{ text_start_date }}:</strong></td>
                <td>{{ forecast.start_date }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_end_date }}:</strong></td>
                <td>{{ forecast.end_date }}</td>
              </tr>
            </table>
          </div>
          <div class="col-md-6">
            <table class="table table-bordered">
              <tr>
                <td><strong>{{ text_accuracy_score }}:</strong></td>
                <td>
                  {% if forecast.accuracy_score %}
                    <span class="label {% if forecast.accuracy_score >= 90 %}label-success{% elseif forecast.accuracy_score >= 70 %}label-warning{% else %}label-danger{% endif %}">
                      {{ forecast.accuracy_score }}%
                    </span>
                  {% else %}
                    <span class="label label-default">{{ text_no_data }}</span>
                  {% endif %}
                </td>
              </tr>
              <tr>
                <td><strong>{{ text_base_scenario }}:</strong></td>
                <td>{{ forecast.base_scenario }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_status }}:</strong></td>
                <td>
                  {% if forecast.status == 'draft' %}
                    <span class="label label-default">{{ text_status_draft }}</span>
                  {% elseif forecast.status == 'active' %}
                    <span class="label label-success">{{ text_status_active }}</span>
                  {% elseif forecast.status == 'archived' %}
                    <span class="label label-info">{{ text_status_archived }}</span>
                  {% endif %}
                </td>
              </tr>
              <tr>
                <td><strong>{{ text_created_by }}:</strong></td>
                <td>{{ forecast.created_by_name }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_date_created }}:</strong></td>
                <td>{{ forecast.date_created }}</td>
              </tr>
            </table>
          </div>
        </div>
        {% if forecast.description %}
        <div class="row">
          <div class="col-md-12">
            <h4>{{ text_description }}</h4>
            <p>{{ forecast.description|nl2br }}</p>
          </div>
        </div>
        {% endif %}
      </div>
    </div>

    <!-- مؤشرات الأداء الرئيسية -->
    <div class="row">
      <div class="col-lg-3 col-xs-6">
        <div class="small-box bg-aqua">
          <div class="inner">
            <h3>{{ kpi.opening_balance }}</h3>
            <p>{{ text_opening_balance }}</p>
          </div>
          <div class="icon">
            <i class="fa fa-money"></i>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-xs-6">
        <div class="small-box bg-green">
          <div class="inner">
            <h3>{{ kpi.total_inflows }}</h3>
            <p>{{ text_total_inflows }}</p>
          </div>
          <div class="icon">
            <i class="fa fa-arrow-down"></i>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-xs-6">
        <div class="small-box bg-yellow">
          <div class="inner">
            <h3>{{ kpi.total_outflows }}</h3>
            <p>{{ text_total_outflows }}</p>
          </div>
          <div class="icon">
            <i class="fa fa-arrow-up"></i>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-xs-6">
        <div class="small-box bg-red">
          <div class="inner">
            <h3>{{ kpi.closing_balance }}</h3>
            <p>{{ text_closing_balance }}</p>
          </div>
          <div class="icon">
            <i class="fa fa-line-chart"></i>
          </div>
        </div>
      </div>
    </div>

    <!-- الرسم البياني للتنبؤ -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-line-chart"></i> {{ text_forecast_chart }}</h3>
      </div>
      <div class="panel-body">
        <canvas id="forecastChart" width="400" height="100"></canvas>
      </div>
    </div>

    <!-- تفاصيل التنبؤ -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-table"></i> {{ text_forecast_details }}</h3>
      </div>
      <div class="panel-body">
        <ul class="nav nav-tabs" role="tablist">
          <li role="presentation" class="active">
            <a href="#cash-flows" aria-controls="cash-flows" role="tab" data-toggle="tab">
              <i class="fa fa-money"></i> {{ tab_cash_flows }}
            </a>
          </li>
          <li role="presentation">
            <a href="#scenarios" aria-controls="scenarios" role="tab" data-toggle="tab">
              <i class="fa fa-sitemap"></i> {{ tab_scenarios }}
            </a>
          </li>
          <li role="presentation">
            <a href="#analysis" aria-controls="analysis" role="tab" data-toggle="tab">
              <i class="fa fa-bar-chart"></i> {{ tab_analysis }}
            </a>
          </li>
          <li role="presentation">
            <a href="#alerts" aria-controls="alerts" role="tab" data-toggle="tab">
              <i class="fa fa-exclamation-triangle"></i> {{ tab_alerts }}
            </a>
          </li>
        </ul>

        <div class="tab-content">
          <!-- التدفقات النقدية -->
          <div role="tabpanel" class="tab-pane active" id="cash-flows">
            <div class="table-responsive" style="margin-top: 15px;">
              <table class="table table-bordered table-hover">
                <thead>
                  <tr>
                    <th>{{ column_period }}</th>
                    <th class="text-right">{{ column_opening_balance }}</th>
                    <th class="text-right">{{ column_inflows }}</th>
                    <th class="text-right">{{ column_outflows }}</th>
                    <th class="text-right">{{ column_net_flow }}</th>
                    <th class="text-right">{{ column_closing_balance }}</th>
                    <th>{{ column_department }}</th>
                    <th>{{ column_cost_center }}</th>
                  </tr>
                </thead>
                <tbody>
                  {% for line in forecast_lines %}
                  <tr>
                    <td>{{ line.period_name }}</td>
                    <td class="text-right">{{ line.opening_balance }}</td>
                    <td class="text-right text-success">{{ line.inflows }}</td>
                    <td class="text-right text-danger">{{ line.outflows }}</td>
                    <td class="text-right {% if line.net_flow < 0 %}text-danger{% else %}text-success{% endif %}">
                      {{ line.net_flow }}
                    </td>
                    <td class="text-right {% if line.closing_balance < 0 %}text-danger{% else %}text-success{% endif %}">
                      {{ line.closing_balance }}
                    </td>
                    <td>{{ line.department_name }}</td>
                    <td>{{ line.cost_center_name }}</td>
                  </tr>
                  {% endfor %}
                </tbody>
                <tfoot>
                  <tr class="info">
                    <td><strong>{{ text_total }}</strong></td>
                    <td class="text-right"><strong>{{ totals.opening_balance }}</strong></td>
                    <td class="text-right"><strong>{{ totals.total_inflows }}</strong></td>
                    <td class="text-right"><strong>{{ totals.total_outflows }}</strong></td>
                    <td class="text-right"><strong>{{ totals.net_flow }}</strong></td>
                    <td class="text-right"><strong>{{ totals.closing_balance }}</strong></td>
                    <td colspan="2"></td>
                  </tr>
                </tfoot>
              </table>
            </div>
          </div>

          <!-- السيناريوهات -->
          <div role="tabpanel" class="tab-pane" id="scenarios">
            <div class="table-responsive" style="margin-top: 15px;">
              <table class="table table-bordered table-hover">
                <thead>
                  <tr>
                    <th>{{ column_scenario_name }}</th>
                    <th>{{ column_scenario_type }}</th>
                    <th class="text-right">{{ column_projected_balance }}</th>
                    <th class="text-right">{{ column_variance_from_base }}</th>
                    <th class="text-right">{{ column_probability }}</th>
                    <th>{{ column_status }}</th>
                    <th>{{ column_action }}</th>
                  </tr>
                </thead>
                <tbody>
                  {% for scenario in scenarios %}
                  <tr>
                    <td>{{ scenario.scenario_name }}</td>
                    <td>
                      {% if scenario.scenario_type == 'optimistic' %}
                        <span class="label label-success">{{ text_optimistic }}</span>
                      {% elseif scenario.scenario_type == 'pessimistic' %}
                        <span class="label label-danger">{{ text_pessimistic }}</span>
                      {% else %}
                        <span class="label label-info">{{ text_realistic }}</span>
                      {% endif %}
                    </td>
                    <td class="text-right">{{ scenario.projected_balance }}</td>
                    <td class="text-right {% if scenario.variance_from_base < 0 %}text-danger{% else %}text-success{% endif %}">
                      {{ scenario.variance_from_base }}
                    </td>
                    <td class="text-right">{{ scenario.probability }}%</td>
                    <td>
                      {% if scenario.status == 'active' %}
                        <span class="label label-success">{{ text_active }}</span>
                      {% else %}
                        <span class="label label-default">{{ text_inactive }}</span>
                      {% endif %}
                    </td>
                    <td>
                      <a href="{{ scenario.view }}" class="btn btn-xs btn-info">
                        <i class="fa fa-eye"></i> {{ button_view }}
                      </a>
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>

          <!-- التحليل -->
          <div role="tabpanel" class="tab-pane" id="analysis">
            <div style="margin-top: 15px;">
              <div class="row">
                <div class="col-md-6">
                  <canvas id="inflowOutflowChart" width="400" height="200"></canvas>
                </div>
                <div class="col-md-6">
                  <div class="table-responsive">
                    <table class="table table-bordered">
                      <thead>
                        <tr class="info">
                          <th>{{ column_analysis_metric }}</th>
                          <th class="text-right">{{ column_value }}</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr>
                          <td>{{ text_average_monthly_inflow }}</td>
                          <td class="text-right">{{ analysis.average_monthly_inflow }}</td>
                        </tr>
                        <tr>
                          <td>{{ text_average_monthly_outflow }}</td>
                          <td class="text-right">{{ analysis.average_monthly_outflow }}</td>
                        </tr>
                        <tr>
                          <td>{{ text_cash_conversion_cycle }}</td>
                          <td class="text-right">{{ analysis.cash_conversion_cycle }} {{ text_days }}</td>
                        </tr>
                        <tr>
                          <td>{{ text_liquidity_ratio }}</td>
                          <td class="text-right">{{ analysis.liquidity_ratio }}</td>
                        </tr>
                        <tr>
                          <td>{{ text_volatility_index }}</td>
                          <td class="text-right">{{ analysis.volatility_index }}%</td>
                        </tr>
                        <tr class="{% if analysis.risk_score >= 70 %}danger{% elseif analysis.risk_score >= 40 %}warning{% else %}success{% endif %}">
                          <td><strong>{{ text_risk_score }}</strong></td>
                          <td class="text-right"><strong>{{ analysis.risk_score }}%</strong></td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- التنبيهات -->
          <div role="tabpanel" class="tab-pane" id="alerts">
            <div style="margin-top: 15px;">
              {% if forecast_alerts %}
              {% for alert in forecast_alerts %}
              <div class="alert alert-{% if alert.severity == 'high' %}danger{% elseif alert.severity == 'medium' %}warning{% else %}info{% endif %} alert-dismissible">
                <button type="button" class="close" data-dismiss="alert">&times;</button>
                <h4>
                  <i class="fa fa-{% if alert.severity == 'high' %}exclamation-triangle{% elseif alert.severity == 'medium' %}warning{% else %}info-circle{% endif %}"></i>
                  {{ alert.alert_type }}
                </h4>
                <p>{{ alert.message }}</p>
                <small class="text-muted">{{ text_triggered_on }}: {{ alert.alert_date }}</small>
              </div>
              {% endfor %}
              {% else %}
              <div class="alert alert-success">
                <i class="fa fa-check-circle"></i> {{ text_no_alerts }}
              </div>
              {% endif %}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- ملاحظات -->
    {% if forecast.notes %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-sticky-note"></i> {{ text_notes }}</h3>
      </div>
      <div class="panel-body">
        <p>{{ forecast.notes|nl2br }}</p>
      </div>
    </div>
    {% endif %}

  </div>
</div>

<script type="text/javascript">
$(document).ready(function() {
    // رسم بياني للتنبؤ
    {% if chart_data %}
    var ctx = document.getElementById('forecastChart').getContext('2d');
    var forecastChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: {{ chart_data.labels|json_encode|raw }},
            datasets: [{
                label: '{{ text_opening_balance }}',
                data: {{ chart_data.opening_balance|json_encode|raw }},
                borderColor: '#3c8dbc',
                backgroundColor: 'rgba(60, 141, 188, 0.1)',
                fill: false
            }, {
                label: '{{ text_closing_balance }}',
                data: {{ chart_data.closing_balance|json_encode|raw }},
                borderColor: '#00a65a',
                backgroundColor: 'rgba(0, 166, 90, 0.1)',
                fill: false
            }, {
                label: '{{ text_net_flow }}',
                data: {{ chart_data.net_flow|json_encode|raw }},
                borderColor: '#f39c12',
                backgroundColor: 'rgba(243, 156, 18, 0.1)',
                fill: false
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
    {% endif %}

    // رسم بياني للتدفقات الداخلة والخارجة
    {% if inflow_outflow_data %}
    var ctx2 = document.getElementById('inflowOutflowChart').getContext('2d');
    var inflowOutflowChart = new Chart(ctx2, {
        type: 'bar',
        data: {
            labels: {{ inflow_outflow_data.labels|json_encode|raw }},
            datasets: [{
                label: '{{ text_inflows }}',
                data: {{ inflow_outflow_data.inflows|json_encode|raw }},
                backgroundColor: '#5cb85c'
            }, {
                label: '{{ text_outflows }}',
                data: {{ inflow_outflow_data.outflows|json_encode|raw }},
                backgroundColor: '#d9534f'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
    {% endif %}

    // إعادة توليد التنبؤ
    $('#button-regenerate').on('click', function() {
        if (confirm('{{ text_confirm_regenerate }}')) {
            $.ajax({
                url: '{{ regenerate }}',
                type: 'post',
                dataType: 'json',
                beforeSend: function() {
                    $('#button-regenerate').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> {{ text_processing }}');
                },
                success: function(json) {
                    if (json['success']) {
                        location.reload();
                    } else {
                        alert(json['error']);
                    }
                },
                complete: function() {
                    $('#button-regenerate').prop('disabled', false).html('<i class="fa fa-refresh"></i> {{ button_regenerate }}');
                }
            });
        }
    });

    // إدارة السيناريوهات
    $('#button-scenario').on('click', function() {
        window.location = '{{ scenario }}';
    });

    // تصدير البيانات
    $('#button-export').on('click', function() {
        window.open('{{ export }}', '_blank');
    });

    // تحرير التنبؤ
    $('#button-edit').on('click', function() {
        window.location = '{{ edit }}';
    });
});
</script>

{{ footer }}
