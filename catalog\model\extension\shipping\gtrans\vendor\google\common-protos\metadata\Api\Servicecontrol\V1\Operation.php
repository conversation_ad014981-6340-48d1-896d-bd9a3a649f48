<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/servicecontrol/v1/operation.proto

namespace GPBMetadata\Google\Api\Servicecontrol\V1;

class Operation
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        \GPBMetadata\Google\Api\Annotations::initOnce();
        \GPBMetadata\Google\Api\Servicecontrol\V1\LogEntry::initOnce();
        \GPBMetadata\Google\Api\Servicecontrol\V1\MetricValue::initOnce();
        \GPBMetadata\Google\Protobuf\Timestamp::initOnce();
        $pool->internalAddGeneratedFile(hex2bin(
            "0aec060a2c676f6f676c652f6170692f73657276696365636f6e74726f6c" .
            "2f76312f6f7065726174696f6e2e70726f746f121c676f6f676c652e6170" .
            "692e73657276696365636f6e74726f6c2e76311a2c676f6f676c652f6170" .
            "692f73657276696365636f6e74726f6c2f76312f6c6f675f656e7472792e" .
            "70726f746f1a2f676f6f676c652f6170692f73657276696365636f6e7472" .
            "6f6c2f76312f6d65747269635f76616c75652e70726f746f1a1f676f6f67" .
            "6c652f70726f746f6275662f74696d657374616d702e70726f746f228f04" .
            "0a094f7065726174696f6e12140a0c6f7065726174696f6e5f6964180120" .
            "01280912160a0e6f7065726174696f6e5f6e616d6518022001280912130a" .
            "0b636f6e73756d65725f6964180320012809122e0a0a73746172745f7469" .
            "6d6518042001280b321a2e676f6f676c652e70726f746f6275662e54696d" .
            "657374616d70122c0a08656e645f74696d6518052001280b321a2e676f6f" .
            "676c652e70726f746f6275662e54696d657374616d7012430a066c616265" .
            "6c7318062003280b32332e676f6f676c652e6170692e7365727669636563" .
            "6f6e74726f6c2e76312e4f7065726174696f6e2e4c6162656c73456e7472" .
            "7912470a116d65747269635f76616c75655f7365747318072003280b322c" .
            "2e676f6f676c652e6170692e73657276696365636f6e74726f6c2e76312e" .
            "4d657472696356616c7565536574123b0a0b6c6f675f656e747269657318" .
            "082003280b32262e676f6f676c652e6170692e73657276696365636f6e74" .
            "726f6c2e76312e4c6f67456e74727912460a0a696d706f7274616e636518" .
            "0b2001280e32322e676f6f676c652e6170692e73657276696365636f6e74" .
            "726f6c2e76312e4f7065726174696f6e2e496d706f7274616e63651a2d0a" .
            "0b4c6162656c73456e747279120b0a036b6579180120012809120d0a0576" .
            "616c75651802200128093a023801221f0a0a496d706f7274616e63651207" .
            "0a034c4f57100012080a044849474810014283010a20636f6d2e676f6f67" .
            "6c652e6170692e73657276696365636f6e74726f6c2e7631420e4f706572" .
            "6174696f6e50726f746f50015a4a676f6f676c652e676f6c616e672e6f72" .
            "672f67656e70726f746f2f676f6f676c65617069732f6170692f73657276" .
            "696365636f6e74726f6c2f76313b73657276696365636f6e74726f6cf801" .
            "01620670726f746f33"
        ), true);

        static::$is_initialized = true;
    }
}

