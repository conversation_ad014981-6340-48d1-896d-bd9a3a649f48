<h2>{{ text_payment_info }}</h2>
<div class="success" id="securetrading_pp_transaction_msg" style="display:none;"></div>
<table class="table table-bordered">
	<tr>
		<td>{{ text_order_ref }}</td>
		<td>{{ securetrading_pp_order.transaction_reference }}</td>
	</tr>
	<tr>
		<td>{{ text_order_total }}</td>
		<td>{{ securetrading_pp_order.total_formatted }}</td>
	</tr>
	<tr>
		<td>{{ text_total_released }}</td>
		<td id="securetrading_pp_total_released">{{ securetrading_pp_order.total_released_formatted }}</td>
	</tr>
	<tr>
		<td>{{ text_release_status }}</td>
		<td id="release_status">
			{% if securetrading_pp_order.release_status == 1 %}
				<span class="release_text">{{ text_yes }}</span>
			{% else %}
				<span class="release_text">{{ text_no }}</span>&nbsp;&nbsp;
				{% if securetrading_pp_order.void_status == 0 %}
					<input type="text" width="10" id="release_amount" value="{{ securetrading_pp_order.total }}"/>
					<a class="button btn btn-primary" id="btn_release">{{ button_release }}</a>
					<span class="btn btn-primary" id="img_loading_release" style="display:none;"><i class="fa fa-cog fa-spin fa-lg"></i></span>
				{% endif %}
			{% endif %}
		</td>
	</tr>
	<tr>
		<td>{{ text_void_status }}</td>
		<td id="void_status">
			{% if securetrading_pp_order.void_status == 1 %}
				<span class="void_text">{{ text_yes }}</span>
			{% elseif securetrading_pp_order.void_status == 0 and securetrading_pp_order.release_status != 1 and securetrading_pp_order.rebate_status != 1 %}
				<span class="void_text">{{ text_no }}</span>&nbsp;&nbsp;
				<a class="button btn btn-primary" id="btn_void">{{ button_void }}</a>
				<span class="btn btn-primary" id="img_loading_void" style="display:none;"><i class="fa fa-cog fa-spin fa-lg"></i></span>
			{% else %}
				<span class="void_text">{{ text_no }}</span>
			{% endif %}
		</td>
	</tr>
	<tr>
		<td>{{ text_rebate_status }}</td>
		<td id="rebate_status">
			{% if securetrading_pp_order.rebate_status == 1 %}
				<span class="rebate_text">{{ text_yes }}</span>
			{% else %}
				<span class="rebate_text">{{ text_no }}</span>&nbsp;&nbsp;

				{% if securetrading_pp_order.total_released > 0 and securetrading_pp_order.void_status == 0 %}
					<input type="text" width="10" id="rebate_amount" />
					<a class="button btn btn-primary" id="btn_rebate">{{ button_rebate }}</a>
					<span class="btn btn-primary" id="img_loading_rebate" style="display:none;"><i class="fa fa-cog fa-spin fa-lg"></i></span>
				{% endif %}
			{% endif %}
		</td>
	</tr>
	<tr>
		<td>{{ text_transactions }}:</td>
		<td>
			<table class="table table-bordered" id="securetrading_pp_transactions">
				<thead>
					<tr>
						<td class="text-left"><strong>{{ text_column_created }}</strong></td>
						<td class="text-left"><strong>{{ text_column_type }}</strong></td>
						<td class="text-left"><strong>{{ text_column_amount }}</strong></td>
					</tr>
				</thead>
				<tbody>
					{% for transaction in securetrading_pp_order.transactions %}
						<tr>
							<td class="text-left">{{ transaction.created }}</td>
							<td class="text-left">{{ transaction.type }}</td>
							<td class="text-left">{{ transaction.amount }}</td>
						</tr>
					{% endfor %}
				</tbody>
			</table>
		</td>
	</tr>
</table>
<script type="text/javascript"><!--
  $("#btn_void").click(function() {
		if (confirm('{{ text_confirm_void }}')) {
			$.ajax({
				type: 'POST',
				dataType: 'json',
				data: {'order_id': {{ order_id }}},
				url: 'index.php?route=extension/payment/securetrading_pp/void&user_token={{ user_token }}',
				beforeSend: function() {
					$('#btn_void').hide();
					$('#img_loading_void').show();
					$('#securetrading_pp_transaction_msg').hide();
				},
				success: function(data) {
					if (data.error == false) {
						html = '';
						html += '<tr>';
						html += '<td class="text-left">' + data.data.created + '</td>';
						html += '<td class="text-left">{{ text_reversed }}</td>';
						html += '<td class="text-left">0.00</td>';
						html += '</tr>';

						$('.void_text').text('{{ text_yes }}');
						$('#securetrading_pp_transactions').append(html);
						$('#btn_release').hide();
						$('#release_amount').hide();

						if (data.msg != '') {
							$('#securetrading_pp_transaction_msg').empty().html(data.msg).fadeIn();
						}
					}
					if (data.error == true) {
						alert(data.msg);
						$('#btn_void').show();
					}

					$('#img_loading_void').hide();
				}
			});
		}
	});
	$("#btn_release").click(function() {
		if (confirm('{{ text_confirm_release }}')) {
			$.ajax({
				type: 'POST',
				dataType: 'json',
				data: {'order_id': {{ order_id }}, 'amount': $('#release_amount').val()},
				url: 'index.php?route=extension/payment/securetrading_pp/release&user_token={{ user_token }}',
				beforeSend: function() {
					$('#btn_release').hide();
					$('#release_amount').hide();
					$('#img_loading_release').show();
					$('#securetrading_pp_transaction_msg').hide();
				},
				success: function(data) {
					if (data.error == false) {
						html = '';
						html += '<tr>';
						html += '<td class="text-left">' + data.data.created + '</td>';
						html += '<td class="text-left">{{ text_payment }}</td>';
						html += '<td class="text-left">' + data.data.amount + '</td>';
						html += '</tr>';

						$('#securetrading_pp_transactions').append(html);
						$('#securetrading_pp_total_released').text(data.data.total);

						if (data.data.release_status == 1) {
							$('#btn_void').hide();
							$('.release_text').text('{{ text_yes }}');
						} else {
							$('#btn_release').show();
							$('#release_amount').val(0.00);

{% if auto_settle == 2 %}
								$('#release_amount').show();
{% endif %}
						}

						if (data.msg != '') {
							$('#securetrading_pp_transaction_msg').empty().html(data.msg).fadeIn();
						}

						$('#btn_rebate').show();
						$('#rebate_amount').val(0.00).show();
					}
					if (data.error == true) {
						alert(data.msg);
						$('#btn_release').show();
						$('#release_amount').show();
					}

					$('#img_loading_release').hide();
				}
			});
		}
	});
	$("#btn_rebate").click(function() {
		if (confirm('{{ text_confirm_rebate }}')) {
			$.ajax({
				type: 'POST',
				dataType: 'json',
				data: {'order_id': {{ order_id }}, 'amount': $('#rebate_amount').val()},
				url: 'index.php?route=extension/payment/securetrading_pp/rebate&user_token={{ user_token }}',
				beforeSend: function() {
					$('#btn_rebate').hide();
					$('#rebate_amount').hide();
					$('#img_loading_rebate').show();
					$('#securetrading_pp_transaction_msg').hide();
				},
				success: function(data) {
					if (data.error == false) {
						html = '';
						html += '<tr>';
						html += '<td class="text-left">' + data.data.created + '</td>';
						html += '<td class="text-left">{{ text_rebate }}</td>';
						html += '<td class="text-left">' + data.data.amount + '</td>';
						html += '</tr>';

						$('#securetrading_pp_transactions').append(html);
						$('#securetrading_pp_total_released').text(data.data.total_released);

						if (data.data.rebate_status == 1) {
							$('.rebate_text').text('{{ text_yes }}');
						} else {
							$('#btn_rebate').show();
							$('#rebate_amount').val(0.00).show();
						}

						if (data.msg != '') {
							$('#securetrading_pp_transaction_msg').empty().html(data.msg).fadeIn();
						}
					}
					if (data.error == true) {
						alert(data.msg);
						$('#btn_rebate').show();
					}

					$('#img_loading_rebate').hide();
				}
			});
		}
	});
//--></script>