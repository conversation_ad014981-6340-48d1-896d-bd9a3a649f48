<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/servicecontrol/v1/check_error.proto

namespace GPBMetadata\Google\Api\Servicecontrol\V1;

class CheckError
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        \GPBMetadata\Google\Api\Annotations::initOnce();
        $pool->internalAddGeneratedFile(hex2bin(
            "0a9c080a2e676f6f676c652f6170692f73657276696365636f6e74726f6c" .
            "2f76312f636865636b5f6572726f722e70726f746f121c676f6f676c652e" .
            "6170692e73657276696365636f6e74726f6c2e763122bc060a0a43686563" .
            "6b4572726f72123b0a04636f646518012001280e322d2e676f6f676c652e" .
            "6170692e73657276696365636f6e74726f6c2e76312e436865636b457272" .
            "6f722e436f6465120e0a0664657461696c18022001280922e0050a04436f" .
            "6465121a0a164552524f525f434f44455f554e5350454349464945441000" .
            "120d0a094e4f545f464f554e44100512150a115045524d495353494f4e5f" .
            "44454e494544100712160a125245534f555243455f455848415553544544" .
            "100812130a0f4142555345525f4445544543544544106712190a15534552" .
            "564943455f4e4f545f414354495641544544106812140a1042494c4c494e" .
            "475f44495341424c4544106b12130a0f50524f4a4543545f44454c455445" .
            "44106c12130a0f50524f4a4543545f494e56414c4944107212160a124950" .
            "5f414444524553535f424c4f434b4544106d12130a0f524546455245525f" .
            "424c4f434b4544106e12160a12434c49454e545f4150505f424c4f434b45" .
            "44106f12160a124150495f5441524745545f424c4f434b4544107a12130a" .
            "0f4150495f4b45595f494e56414c4944106912130a0f4150495f4b45595f" .
            "45585049524544107012150a114150495f4b45595f4e4f545f464f554e44" .
            "1071121c0a1853454355524954595f504f4c4943595f56494f4c41544544" .
            "107912160a12494e56414c49445f43524544454e5449414c107b121c0a18" .
            "4c4f434154494f4e5f504f4c4943595f56494f4c41544544107c12140a10" .
            "434f4e53554d45525f494e56414c4944107d12210a1c4e414d4553504143" .
            "455f4c4f4f4b55505f554e415641494c41424c4510ac02121f0a1a534552" .
            "564943455f5354415455535f554e415641494c41424c4510ad02121f0a1a" .
            "42494c4c494e475f5354415455535f554e415641494c41424c4510ae0212" .
            "1c0a1751554f54415f434845434b5f554e415641494c41424c4510af0212" .
            "2f0a2a434c4f55445f5245534f555243455f4d414e414745525f4241434b" .
            "454e445f554e415641494c41424c4510b10212280a235345435552495459" .
            "5f504f4c4943595f4241434b454e445f554e415641494c41424c4510b202" .
            "12280a234c4f434154494f4e5f504f4c4943595f4241434b454e445f554e" .
            "415641494c41424c4510b3024284010a20636f6d2e676f6f676c652e6170" .
            "692e73657276696365636f6e74726f6c2e7631420f436865636b4572726f" .
            "7250726f746f50015a4a676f6f676c652e676f6c616e672e6f72672f6765" .
            "6e70726f746f2f676f6f676c65617069732f6170692f7365727669636563" .
            "6f6e74726f6c2f76313b73657276696365636f6e74726f6cf80101620670" .
            "726f746f33"
        ), true);

        static::$is_initialized = true;
    }
}

