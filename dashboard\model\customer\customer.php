<?php
class ModelCustomerCustomer extends Model {
	public function addCustomer($data) {
		try {
			// بدء المعاملة لضمان سلامة البيانات
			$this->db->query("START TRANSACTION");
			
			// التحقق من وجود البريد الإلكتروني مسبقاً
			$email_check = $this->db->query("SELECT COUNT(*) AS total FROM " . DB_PREFIX . "customer 
				WHERE LOWER(email) = '" . $this->db->escape(utf8_strtolower($data['email'])) . "'");
			
			if ($email_check->row['total'] > 0) {
				throw new \Exception('البريد الإلكتروني مستخدم بالفعل');
			}
			
			// إنشاء salt عشوائي آمن
			$salt = token(9);
			
			// تشفير كلمة المرور
			$password = sha1($salt . sha1($salt . sha1($data['password'])));
			
			// إدخال بيانات العميل الأساسية
			$this->db->query("INSERT INTO " . DB_PREFIX . "customer SET 
				customer_group_id = '" . (int)$data['customer_group_id'] . "', 
				firstname = '" . $this->db->escape($data['firstname']) . "', 
				lastname = '" . $this->db->escape($data['lastname']) . "', 
				email = '" . $this->db->escape($data['email']) . "', 
				telephone = '" . $this->db->escape($data['telephone']) . "', 
				custom_field = '" . $this->db->escape(isset($data['custom_field']) ? json_encode($data['custom_field']) : json_encode(array())) . "', 
				newsletter = '" . (int)$data['newsletter'] . "', 
				salt = '" . $this->db->escape($salt) . "', 
				password = '" . $this->db->escape($password) . "', 
				status = '" . (int)$data['status'] . "', 
				safe = '" . (int)$data['safe'] . "', 
				date_added = NOW()");

			$customer_id = $this->db->getLastId();

			// إدخال عناوين العميل إذا وجدت
			if (isset($data['address']) && is_array($data['address'])) {
				foreach ($data['address'] as $address) {
					$this->db->query("INSERT INTO " . DB_PREFIX . "address SET 
						customer_id = '" . (int)$customer_id . "', 
						firstname = '" . $this->db->escape($address['firstname']) . "', 
						lastname = '" . $this->db->escape($address['lastname']) . "', 
						company = '" . $this->db->escape($address['company'] ?? '') . "', 
						address_1 = '" . $this->db->escape($address['address_1']) . "', 
						address_2 = '" . $this->db->escape($address['address_2'] ?? '') . "', 
						city = '" . $this->db->escape($address['city']) . "', 
						postcode = '" . $this->db->escape($address['postcode'] ?? '') . "', 
						country_id = '" . (int)$address['country_id'] . "', 
						zone_id = '" . (int)($address['zone_id'] ?? 0) . "', 
						custom_field = '" . $this->db->escape(isset($address['custom_field']) ? json_encode($address['custom_field']) : json_encode(array())) . "'");

					if (isset($address['default']) && $address['default']) {
						$address_id = $this->db->getLastId();

						$this->db->query("UPDATE " . DB_PREFIX . "customer SET 
							address_id = '" . (int)$address_id . "' 
							WHERE customer_id = '" . (int)$customer_id . "'");
					}
				}
			}
			
			// تأكيد المعاملة في حالة عدم وجود أخطاء
			$this->db->query("COMMIT");
			
			// تسجيل النشاط
			if ($this->config->get('config_customer_activity')) {
				$this->load->model('customer/activity');
				$activity_data = array(
					'customer_id' => $customer_id,
					'name'        => $data['firstname'] . ' ' . $data['lastname']
				);
				$this->model_customer_activity->addActivity('register', $activity_data);
			}
			
			return $customer_id;
			
		} catch (\Exception $e) {
			// التراجع عن المعاملة في حالة وجود خطأ
			$this->db->query("ROLLBACK");
			
			// تسجيل الخطأ
			if ($this->config->get('config_error_log')) {
				$this->log->write('خطأ في إضافة العميل: ' . $e->getMessage());
			}
			
			throw $e;
		}
	}
	
    private function addAccount($data) {
        $this->db->query("INSERT INTO " . DB_PREFIX . "accounts SET account_code = '" . $this->db->escape($data['account_code']) . "', parent_id = '" . (int)$data['parent_id'] . "', status = '" . (int)$data['status'] . "', date_modified = NOW(), date_added = NOW()");

        $account_id = $this->db->getLastId();

        foreach ($data['account_description'] as $language_id => $value) {
            $this->db->query("INSERT INTO " . DB_PREFIX . "account_description SET account_id = '" . (int)$account_id . "', language_id = '" . (int)$language_id . "', name = '" . $this->db->escape($value['name']) . "'");
        }

        $this->cache->delete('account');

        return $account_id;
    }	
   public function getTotalCustomersByTelephone($telephone) {
        $query = $this->db->query("SELECT COUNT(*) AS total FROM " . DB_PREFIX . "customer WHERE telephone = '" . $this->db->escape($telephone) . "'");
        return $query->row['total'];
    }
public function getCustomerByEmailOrPhone($identifier) {
    try {
        if (empty($identifier)) {
            return [];
        }
        
        // تنظيف القيمة المدخلة
        $clean_identifier = trim($identifier);
        
        // التحقق مما إذا كان المدخل هو بريد إلكتروني أو رقم هاتف
        if (filter_var($clean_identifier, FILTER_VALIDATE_EMAIL)) {
            // البحث عن طريق البريد الإلكتروني (مع مراعاة حالة الأحرف)
            $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "customer 
                WHERE LOWER(email) = '" . $this->db->escape(utf8_strtolower($clean_identifier)) . "'");
        } else {
            // تنظيف رقم الهاتف من الأحرف غير الرقمية
            $phone_digits = preg_replace('/[^0-9]/', '', $clean_identifier);
            
            // إذا كان طول الرقم صغيرًا جدًا، قد يكون مدخلًا غير صالح
            if (strlen($phone_digits) < 5) {
                return [];
            }
            
            // البحث عن طريق آخر أرقام من رقم الهاتف
            $length = min(strlen($phone_digits), 10);
            $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "customer 
                WHERE RIGHT(REPLACE(REPLACE(REPLACE(telephone, ' ', ''), '-', ''), '+', ''), " . (int)$length . ") = 
                RIGHT('" . $this->db->escape($phone_digits) . "', " . (int)$length . ")");
        }
        
        return $query->row ?? [];
    } catch (\Exception $e) {
        // تسجيل الخطأ
        if ($this->config->get('config_error_log')) {
            $this->log->write('Error in getCustomerByEmailOrPhone: ' . $e->getMessage());
        }
        
        // إرجاع مصفوفة فارغة في حالة الخطأ
        return [];
    }
}    
	public function editCustomer($customer_id, $data) {
		$this->db->query("UPDATE " . DB_PREFIX . "customer SET customer_group_id = '" . (int)$data['customer_group_id'] . "', firstname = '" . $this->db->escape($data['firstname']) . "', lastname = '" . $this->db->escape($data['lastname']) . "', email = '" . $this->db->escape($data['email']) . "', telephone = '" . $this->db->escape($data['telephone']) . "', custom_field = '" . $this->db->escape(isset($data['custom_field']) ? json_encode($data['custom_field']) : json_encode(array())) . "', newsletter = '" . (int)$data['newsletter'] . "', status = '" . (int)$data['status'] . "', safe = '" . (int)$data['safe'] . "' WHERE customer_id = '" . (int)$customer_id . "'");

		if ($data['password']) {
			$this->db->query("UPDATE " . DB_PREFIX . "customer SET salt = '" . $this->db->escape($salt = token(9)) . "', password = '" . $this->db->escape(sha1($salt . sha1($salt . sha1($data['password'])))) . "' WHERE customer_id = '" . (int)$customer_id . "'");
		}

		$this->db->query("DELETE FROM " . DB_PREFIX . "address WHERE customer_id = '" . (int)$customer_id . "'");

		if (isset($data['address'])) {
			foreach ($data['address'] as $address) {
				$this->db->query("INSERT INTO " . DB_PREFIX . "address SET address_id = '" . (int)$address['address_id'] . "', customer_id = '" . (int)$customer_id . "', firstname = '" . $this->db->escape($address['firstname']) . "', lastname = '" . $this->db->escape($address['lastname']) . "', company = '" . $this->db->escape($address['company']) . "', address_1 = '" . $this->db->escape($address['address_1']) . "', address_2 = '" . $this->db->escape($address['address_2']) . "', city = '" . $this->db->escape($address['city']) . "', postcode = '" . $this->db->escape($address['postcode']) . "', country_id = '" . (int)$address['country_id'] . "', zone_id = '" . (int)$address['zone_id'] . "', custom_field = '" . $this->db->escape(isset($address['custom_field']) ? json_encode($address['custom_field']) : json_encode(array())) . "'");

				if (isset($address['default'])) {
					$address_id = $this->db->getLastId();

					$this->db->query("UPDATE " . DB_PREFIX . "customer SET address_id = '" . (int)$address_id . "' WHERE customer_id = '" . (int)$customer_id . "'");
				}
			}
		}
		
		if ($data['affiliate']) {
			$this->db->query("REPLACE INTO " . DB_PREFIX . "customer_affiliate SET customer_id = '" . (int)$customer_id . "', company = '" . $this->db->escape($data['company']) . "', website = '" . $this->db->escape($data['website']) . "', tracking = '" . $this->db->escape($data['tracking']) . "', commission = '" . (float)$data['commission'] . "', tax = '" . $this->db->escape($data['tax']) . "', payment = '" . $this->db->escape($data['payment']) . "', cheque = '" . $this->db->escape($data['cheque']) . "', paypal = '" . $this->db->escape($data['paypal']) . "', bank_name = '" . $this->db->escape($data['bank_name']) . "', bank_branch_number = '" . $this->db->escape($data['bank_branch_number']) . "', bank_swift_code = '" . $this->db->escape($data['bank_swift_code']) . "', bank_account_name = '" . $this->db->escape($data['bank_account_name']) . "', bank_account_number = '" . $this->db->escape($data['bank_account_number']) . "', custom_field = '" . $this->db->escape(isset($data['custom_field']['affiliate']) ? json_encode($data['custom_field']['affiliate']) : json_encode(array())) . "', status = '" . (int)$data['affiliate'] . "', date_added = NOW()");
		}		
	}

	public function editToken($customer_id, $token) {
		$this->db->query("UPDATE " . DB_PREFIX . "customer SET token = '" . $this->db->escape($token) . "' WHERE customer_id = '" . (int)$customer_id . "'");
	}

	public function deleteCustomer($customer_id) {
		$this->db->query("DELETE FROM " . DB_PREFIX . "customer WHERE customer_id = '" . (int)$customer_id . "'");
		$this->db->query("DELETE FROM " . DB_PREFIX . "customer_activity WHERE customer_id = '" . (int)$customer_id . "'");
		$this->db->query("DELETE FROM " . DB_PREFIX . "customer_affiliate WHERE customer_id = '" . (int)$customer_id . "'");
		$this->db->query("DELETE FROM " . DB_PREFIX . "customer_approval WHERE customer_id = '" . (int)$customer_id . "'");
 		$this->db->query("DELETE FROM " . DB_PREFIX . "customer_history WHERE customer_id = '" . (int)$customer_id . "'");
		$this->db->query("DELETE FROM " . DB_PREFIX . "customer_reward WHERE customer_id = '" . (int)$customer_id . "'");
		$this->db->query("DELETE FROM " . DB_PREFIX . "customer_transaction WHERE customer_id = '" . (int)$customer_id . "'");
		$this->db->query("DELETE FROM " . DB_PREFIX . "customer_ip WHERE customer_id = '" . (int)$customer_id . "'");
		$this->db->query("DELETE FROM " . DB_PREFIX . "address WHERE customer_id = '" . (int)$customer_id . "'");
		
	}

	public function getCustomer($customer_id) {
		$query = $this->db->query("SELECT DISTINCT * FROM " . DB_PREFIX . "customer WHERE customer_id = '" . (int)$customer_id . "'");

		return $query->row;
	}

	public function getCustomerByEmail($email) {
		$query = $this->db->query("SELECT DISTINCT * FROM " . DB_PREFIX . "customer WHERE LCASE(email) = '" . $this->db->escape(utf8_strtolower($email)) . "'");

		return $query->row;
	}
	
	public function getCustomers($data = array()) {
		$sql = "SELECT *, CONCAT(c.firstname, ' ', c.lastname) AS name, cgd.name AS customer_group FROM " . DB_PREFIX . "customer c LEFT JOIN " . DB_PREFIX . "customer_group_description cgd ON (c.customer_group_id = cgd.customer_group_id)";
		
		if (!empty($data['filter_affiliate'])) {
			$sql .= " LEFT JOIN " . DB_PREFIX . "customer_affiliate ca ON (c.customer_id = ca.customer_id)";
		}		
		
		$sql .= " WHERE cgd.language_id = '" . (int)$this->config->get('config_language_id') . "'";
		
		$implode = array();

		if (!empty($data['filter_name'])) {
			$implode[] = "CONCAT(c.firstname, ' ', c.lastname) LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
		}

		if (!empty($data['filter_email'])) {
			$implode[] = "c.email LIKE '" . $this->db->escape($data['filter_email']) . "%'";
		}

		if (isset($data['filter_newsletter']) && !is_null($data['filter_newsletter'])) {
			$implode[] = "c.newsletter = '" . (int)$data['filter_newsletter'] . "'";
		}

		if (!empty($data['filter_customer_group_id'])) {
			$implode[] = "c.customer_group_id = '" . (int)$data['filter_customer_group_id'] . "'";
		}

		if (!empty($data['filter_affiliate'])) {
			$implode[] = "ca.status = '" . (int)$data['filter_affiliate'] . "'";
		}
		
		if (!empty($data['filter_ip'])) {
			$implode[] = "c.customer_id IN (SELECT customer_id FROM " . DB_PREFIX . "customer_ip WHERE ip = '" . $this->db->escape($data['filter_ip']) . "')";
		}

		if (isset($data['filter_status']) && $data['filter_status'] !== '') {
			$implode[] = "c.status = '" . (int)$data['filter_status'] . "'";
		}

		if (!empty($data['filter_date_added'])) {
			$implode[] = "DATE(c.date_added) = DATE('" . $this->db->escape($data['filter_date_added']) . "')";
		}

		if ($implode) {
			$sql .= " AND " . implode(" AND ", $implode);
		}

		$sort_data = array(
			'name',
			'c.email',
			'customer_group',
			'c.status',
			'c.ip',
			'c.date_added'
		);

		if (isset($data['sort']) && in_array($data['sort'], $sort_data)) {
			$sql .= " ORDER BY " . $data['sort'];
		} else {
			$sql .= " ORDER BY name";
		}

		if (isset($data['order']) && ($data['order'] == 'DESC')) {
			$sql .= " DESC";
		} else {
			$sql .= " ASC";
		}

		if (isset($data['start']) || isset($data['limit'])) {
			if ($data['start'] < 0) {
				$data['start'] = 0;
			}

			if ($data['limit'] < 1) {
				$data['limit'] = 20;
			}

			$sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
		}

		$query = $this->db->query($sql);

		return $query->rows;
	}

	public function getAddress($address_id) {
		$address_query = $this->db->query("SELECT * FROM " . DB_PREFIX . "address WHERE address_id = '" . (int)$address_id . "'");

		if ($address_query->num_rows) {
			$country_query = $this->db->query("SELECT * FROM `" . DB_PREFIX . "country` WHERE country_id = '" . (int)$address_query->row['country_id'] . "'");

			if ($country_query->num_rows) {
				$country = $country_query->row['name'];
				$iso_code_2 = $country_query->row['iso_code_2'];
				$iso_code_3 = $country_query->row['iso_code_3'];
				$address_format = $country_query->row['address_format'];
			} else {
				$country = '';
				$iso_code_2 = '';
				$iso_code_3 = '';
				$address_format = '';
			}

			$zone_query = $this->db->query("SELECT * FROM `" . DB_PREFIX . "zone` WHERE zone_id = '" . (int)$address_query->row['zone_id'] . "'");

			if ($zone_query->num_rows) {
				$zone = $zone_query->row['name'];
				$zone_code = $zone_query->row['code'];
			} else {
				$zone = '';
				$zone_code = '';
			}

			return array(
				'address_id'     => $address_query->row['address_id'],
				'customer_id'    => $address_query->row['customer_id'],
				'firstname'      => $address_query->row['firstname'],
				'lastname'       => $address_query->row['lastname'],
				'company'        => $address_query->row['company'],
				'address_1'      => $address_query->row['address_1'],
				'address_2'      => $address_query->row['address_2'],
				'postcode'       => $address_query->row['postcode'],
				'city'           => $address_query->row['city'],
				'zone_id'        => $address_query->row['zone_id'],
				'zone'           => $zone,
				'zone_code'      => $zone_code,
				'country_id'     => $address_query->row['country_id'],
				'country'        => $country,
				'iso_code_2'     => $iso_code_2,
				'iso_code_3'     => $iso_code_3,
				'address_format' => $address_format,
				'custom_field'   => json_decode($address_query->row['custom_field'], true)
			);
		}
	}

	public function getAddresses($customer_id) {
		$address_data = array();

		$query = $this->db->query("SELECT address_id FROM " . DB_PREFIX . "address WHERE customer_id = '" . (int)$customer_id . "'");

		foreach ($query->rows as $result) {
			$address_info = $this->getAddress($result['address_id']);

			if ($address_info) {
				$address_data[$result['address_id']] = $address_info;
			}
		}

		return $address_data;
	}

	public function getTotalCustomers($data = array()) {
		$sql = "SELECT COUNT(*) AS total FROM " . DB_PREFIX . "customer c";

		$implode = array();

		if (!empty($data['filter_name'])) {
			$implode[] = "CONCAT(firstname, ' ', lastname) LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
		}

		if (!empty($data['filter_email'])) {
			$implode[] = "email LIKE '" . $this->db->escape($data['filter_email']) . "%'";
		}

		if (isset($data['filter_newsletter']) && !is_null($data['filter_newsletter'])) {
			$implode[] = "newsletter = '" . (int)$data['filter_newsletter'] . "'";
		}

		if (!empty($data['filter_customer_group_id'])) {
			$implode[] = "customer_group_id = '" . (int)$data['filter_customer_group_id'] . "'";
		}

		if (!empty($data['filter_ip'])) {
			$implode[] = "customer_id IN (SELECT customer_id FROM " . DB_PREFIX . "customer_ip WHERE ip = '" . $this->db->escape($data['filter_ip']) . "')";
		}

		if (isset($data['filter_status']) && $data['filter_status'] !== '') {
			$implode[] = "status = '" . (int)$data['filter_status'] . "'";
		}

		if (!empty($data['filter_date_added'])) {
			$implode[] = "DATE(date_added) = DATE('" . $this->db->escape($data['filter_date_added']) . "')";
		}

		if ($implode) {
			$sql .= " WHERE " . implode(" AND ", $implode);
		}

		$query = $this->db->query($sql);

		return $query->row['total'];
	}
        
    public function getAffiliateByTracking($tracking) {
        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "customer_affiliate WHERE tracking = '" . $this->db->escape($tracking) . "'");
                
        return $query->row;
    }
	
	public function getAffiliate($customer_id) {
		$query = $this->db->query("SELECT * FROM " . DB_PREFIX . "customer_affiliate WHERE customer_id = '" . (int)$customer_id . "'");

		return $query->row;
	}
	
	public function getAffiliates($data = array()) {
		$sql = "SELECT DISTINCT *, CONCAT(c.firstname, ' ', c.lastname) AS name FROM " . DB_PREFIX . "customer_affiliate ca LEFT JOIN " . DB_PREFIX . "customer c ON (ca.customer_id = c.customer_id)";
		
		$implode = array();

		if (!empty($data['filter_name'])) {
			$implode[] = "CONCAT(c.firstname, ' ', c.lastname) LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
		}		
		
		if ($implode) {
			$sql .= " WHERE " . implode(" AND ", $implode);
		}
		
		if (isset($data['start']) || isset($data['limit'])) {
			if ($data['start'] < 0) {
				$data['start'] = 0;
			}

			if ($data['limit'] < 1) {
				$data['limit'] = 20;
			}

			$sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
		}
						
		$query = $this->db->query($sql . "ORDER BY name");

		return $query->rows;
	}
	
	public function getTotalAffiliates($data = array()) {
		$sql = "SELECT DISTINCT COUNT(*) AS total FROM " . DB_PREFIX . "customer_affiliate ca LEFT JOIN " . DB_PREFIX . "customer c ON (ca.customer_id = c.customer_id)";
		
		$implode = array();

		if (!empty($data['filter_name'])) {
			$implode[] = "CONCAT(c.firstname, ' ', c.lastname) LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
		}		
		
		if ($implode) {
			$sql .= " WHERE " . implode(" AND ", $implode);
		}

		$query = $this->db->query($sql);

		return $query->row['total'];
	}

	public function getTotalAddressesByCustomerId($customer_id) {
		$query = $this->db->query("SELECT COUNT(*) AS total FROM " . DB_PREFIX . "address WHERE customer_id = '" . (int)$customer_id . "'");

		return $query->row['total'];
	}

	public function getTotalAddressesByCountryId($country_id) {
		$query = $this->db->query("SELECT COUNT(*) AS total FROM " . DB_PREFIX . "address WHERE country_id = '" . (int)$country_id . "'");

		return $query->row['total'];
	}

	public function getTotalAddressesByZoneId($zone_id) {
		$query = $this->db->query("SELECT COUNT(*) AS total FROM " . DB_PREFIX . "address WHERE zone_id = '" . (int)$zone_id . "'");

		return $query->row['total'];
	}

	public function getTotalCustomersByCustomerGroupId($customer_group_id) {
		$query = $this->db->query("SELECT COUNT(*) AS total FROM " . DB_PREFIX . "customer WHERE customer_group_id = '" . (int)$customer_group_id . "'");

		return $query->row['total'];
	}

	public function addHistory($customer_id, $comment) {
		$this->db->query("INSERT INTO " . DB_PREFIX . "customer_history SET customer_id = '" . (int)$customer_id . "', comment = '" . $this->db->escape(strip_tags($comment)) . "', date_added = NOW()");
	}

	public function getHistories($customer_id, $start = 0, $limit = 10) {
		if ($start < 0) {
			$start = 0;
		}

		if ($limit < 1) {
			$limit = 10;
		}

		$query = $this->db->query("SELECT comment, date_added FROM " . DB_PREFIX . "customer_history WHERE customer_id = '" . (int)$customer_id . "' ORDER BY date_added DESC LIMIT " . (int)$start . "," . (int)$limit);

		return $query->rows;
	}

	public function getTotalHistories($customer_id) {
		$query = $this->db->query("SELECT COUNT(*) AS total FROM " . DB_PREFIX . "customer_history WHERE customer_id = '" . (int)$customer_id . "'");

		return $query->row['total'];
	}

	public function addTransaction($customer_id, $description = '', $amount = '', $order_id = 0) {
		$this->db->query("INSERT INTO " . DB_PREFIX . "customer_transaction SET customer_id = '" . (int)$customer_id . "', order_id = '" . (int)$order_id . "', description = '" . $this->db->escape($description) . "', amount = '" . (float)$amount . "', date_added = NOW()");
	}

	public function deleteTransactionByOrderId($order_id) {
		$this->db->query("DELETE FROM " . DB_PREFIX . "customer_transaction WHERE order_id = '" . (int)$order_id . "'");
	}

	public function getTransactions($customer_id, $start = 0, $limit = 10) {
		if ($start < 0) {
			$start = 0;
		}

		if ($limit < 1) {
			$limit = 10;
		}

		$query = $this->db->query("SELECT * FROM " . DB_PREFIX . "customer_transaction WHERE customer_id = '" . (int)$customer_id . "' ORDER BY date_added DESC LIMIT " . (int)$start . "," . (int)$limit);

		return $query->rows;
	}

	public function getTotalTransactions($customer_id) {
		$query = $this->db->query("SELECT COUNT(*) AS total  FROM " . DB_PREFIX . "customer_transaction WHERE customer_id = '" . (int)$customer_id . "'");

		return $query->row['total'];
	}

	public function getTransactionTotal($customer_id) {
		$query = $this->db->query("SELECT SUM(amount) AS total FROM " . DB_PREFIX . "customer_transaction WHERE customer_id = '" . (int)$customer_id . "'");

		return $query->row['total'];
	}

	public function getTotalTransactionsByOrderId($order_id) {
		$query = $this->db->query("SELECT COUNT(*) AS total FROM " . DB_PREFIX . "customer_transaction WHERE order_id = '" . (int)$order_id . "'");

		return $query->row['total'];
	}

	public function addReward($customer_id, $description = '', $points = '', $order_id = 0) {
		$this->db->query("INSERT INTO " . DB_PREFIX . "customer_reward SET customer_id = '" . (int)$customer_id . "', order_id = '" . (int)$order_id . "', points = '" . (int)$points . "', description = '" . $this->db->escape($description) . "', date_added = NOW()");
	}

	public function deleteReward($order_id) {
		$this->db->query("DELETE FROM " . DB_PREFIX . "customer_reward WHERE order_id = '" . (int)$order_id . "' AND points > 0");
	}

	public function getRewards($customer_id, $start = 0, $limit = 10) {
		$query = $this->db->query("SELECT * FROM " . DB_PREFIX . "customer_reward WHERE customer_id = '" . (int)$customer_id . "' ORDER BY date_added DESC LIMIT " . (int)$start . "," . (int)$limit);

		return $query->rows;
	}

	public function getTotalRewards($customer_id) {
		$query = $this->db->query("SELECT COUNT(*) AS total FROM " . DB_PREFIX . "customer_reward WHERE customer_id = '" . (int)$customer_id . "'");

		return $query->row['total'];
	}

	public function getRewardTotal($customer_id) {
		$query = $this->db->query("SELECT SUM(points) AS total FROM " . DB_PREFIX . "customer_reward WHERE customer_id = '" . (int)$customer_id . "'");

		return $query->row['total'];
	}

	public function getTotalCustomerRewardsByOrderId($order_id) {
		$query = $this->db->query("SELECT COUNT(*) AS total FROM " . DB_PREFIX . "customer_reward WHERE order_id = '" . (int)$order_id . "' AND points > 0");

		return $query->row['total'];
	}

	public function getIps($customer_id, $start = 0, $limit = 10) {
		if ($start < 0) {
			$start = 0;
		}
		if ($limit < 1) {
			$limit = 10;
		}

		$query = $this->db->query("SELECT * FROM " . DB_PREFIX . "customer_ip WHERE customer_id = '" . (int)$customer_id . "' ORDER BY date_added DESC LIMIT " . (int)$start . "," . (int)$limit);
		
		return $query->rows;
	}

	public function getTotalIps($customer_id) {
		$query = $this->db->query("SELECT COUNT(*) AS total FROM " . DB_PREFIX . "customer_ip WHERE customer_id = '" . (int)$customer_id . "'");

		return $query->row['total'];
	}

	public function getTotalCustomersByIp($ip) {
		$query = $this->db->query("SELECT COUNT(*) AS total FROM " . DB_PREFIX . "customer_ip WHERE ip = '" . $this->db->escape($ip) . "'");

		return $query->row['total'];
	}

	public function getTotalLoginAttempts($email) {
		$query = $this->db->query("SELECT * FROM `" . DB_PREFIX . "customer_login` WHERE `email` = '" . $this->db->escape($email) . "'");

		return $query->row;
	}

	public function deleteLoginAttempts($email) {
		$this->db->query("DELETE FROM " . DB_PREFIX . "customer_login WHERE email = '" . $this->db->escape(utf8_strtolower($email)) . "'");
	}

	// =======================================================================
	// دوال حدود ائتمان العملاء
	// =======================================================================
	
	public function getCustomerCreditLimit($customer_id) {
		$query = $this->db->query("SELECT * FROM " . DB_PREFIX . "customer_credit_limit 
								   WHERE customer_id = '" . (int)$customer_id . "'");
		return $query->row;
	}

	public function updateCustomerCreditLimit($customer_id, $data) {
		$this->db->query("INSERT INTO " . DB_PREFIX . "customer_credit_limit 
						  SET customer_id = '" . (int)$customer_id . "',
							  credit_limit = '" . (float)$data['credit_limit'] . "',
							  current_balance = '" . (float)$data['current_balance'] . "',
							  payment_terms = '" . (int)$data['payment_terms'] . "',
							  status = '" . $this->db->escape($data['status']) . "',
							  notes = '" . $this->db->escape($data['notes']) . "',
							  updated_at = NOW()
						  ON DUPLICATE KEY UPDATE
							  credit_limit = VALUES(credit_limit),
							  current_balance = VALUES(current_balance),
							  payment_terms = VALUES(payment_terms),
							  status = VALUES(status),
							  notes = VALUES(notes),
							  updated_at = NOW()");
	}

	public function getAllCustomerCreditLimits($data = array()) {
		$sql = "SELECT ccl.*, c.firstname, c.lastname, c.email, c.telephone 
				FROM " . DB_PREFIX . "customer_credit_limit ccl
				LEFT JOIN " . DB_PREFIX . "customer c ON (ccl.customer_id = c.customer_id)";
		
		$implode = array();
		
		if (!empty($data['filter_customer'])) {
			$implode[] = "CONCAT(c.firstname, ' ', c.lastname) LIKE '%" . $this->db->escape($data['filter_customer']) . "%'";
		}
		
		if (!empty($data['filter_email'])) {
			$implode[] = "c.email LIKE '" . $this->db->escape($data['filter_email']) . "%'";
		}
		
		if (isset($data['filter_status']) && $data['filter_status'] !== '') {
			$implode[] = "ccl.status = '" . $this->db->escape($data['filter_status']) . "'";
		}
		
		if ($implode) {
			$sql .= " WHERE " . implode(" AND ", $implode);
		}
		
		$sql .= " ORDER BY c.firstname, c.lastname";
		
		if (isset($data['start']) || isset($data['limit'])) {
			if ($data['start'] < 0) {
				$data['start'] = 0;
			}
			if ($data['limit'] < 1) {
				$data['limit'] = 20;
			}
			$sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
		}
		
		$query = $this->db->query($sql);
		return $query->rows;
	}

	public function getTotalCustomerCreditLimits($data = array()) {
		$sql = "SELECT COUNT(*) AS total FROM " . DB_PREFIX . "customer_credit_limit ccl
				LEFT JOIN " . DB_PREFIX . "customer c ON (ccl.customer_id = c.customer_id)";
		
		$implode = array();
		
		if (!empty($data['filter_customer'])) {
			$implode[] = "CONCAT(c.firstname, ' ', c.lastname) LIKE '%" . $this->db->escape($data['filter_customer']) . "%'";
		}
		
		if (!empty($data['filter_email'])) {
			$implode[] = "c.email LIKE '" . $this->db->escape($data['filter_email']) . "%'";
		}
		
		if (isset($data['filter_status']) && $data['filter_status'] !== '') {
			$implode[] = "ccl.status = '" . $this->db->escape($data['filter_status']) . "'";
		}
		
		if ($implode) {
			$sql .= " WHERE " . implode(" AND ", $implode);
		}
		
		$query = $this->db->query($sql);
		return $query->row['total'];
	}

	// =======================================================================
	// دوال ملاحظات العملاء
	// =======================================================================
	
	public function getCustomerFeedbacks($customer_id = null, $data = array()) {
		$sql = "SELECT cf.*, c.firstname, c.lastname, c.email 
				FROM " . DB_PREFIX . "customer_feedback cf
				LEFT JOIN " . DB_PREFIX . "customer c ON (cf.customer_id = c.customer_id)";
		
		if ($customer_id) {
			$sql .= " WHERE cf.customer_id = '" . (int)$customer_id . "'";
		}
		
		$implode = array();
		
		if (!$customer_id && !empty($data['filter_customer'])) {
			$implode[] = "CONCAT(c.firstname, ' ', c.lastname) LIKE '%" . $this->db->escape($data['filter_customer']) . "%'";
		}
		
		if (!empty($data['filter_subject'])) {
			$implode[] = "cf.subject LIKE '%" . $this->db->escape($data['filter_subject']) . "%'";
		}
		
		if (!empty($data['filter_feedback_type'])) {
			$implode[] = "cf.feedback_type = '" . $this->db->escape($data['filter_feedback_type']) . "'";
		}
		
		if (isset($data['filter_status']) && $data['filter_status'] !== '') {
			$implode[] = "cf.status = '" . $this->db->escape($data['filter_status']) . "'";
		}
		
		if (isset($data['filter_priority']) && $data['filter_priority'] !== '') {
			$implode[] = "cf.priority = '" . $this->db->escape($data['filter_priority']) . "'";
		}
		
		if ($implode) {
			$sql .= ($customer_id ? " AND " : " WHERE ") . implode(" AND ", $implode);
		}
		
		$sql .= " ORDER BY cf.created_at DESC";
		
		if (isset($data['start']) || isset($data['limit'])) {
			if ($data['start'] < 0) {
				$data['start'] = 0;
			}
			if ($data['limit'] < 1) {
				$data['limit'] = 20;
			}
			$sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
		}
		
		$query = $this->db->query($sql);
		return $query->rows;
	}

	public function getCustomerFeedback($feedback_id) {
		$query = $this->db->query("SELECT cf.*, c.firstname, c.lastname, c.email 
								   FROM " . DB_PREFIX . "customer_feedback cf
								   LEFT JOIN " . DB_PREFIX . "customer c ON (cf.customer_id = c.customer_id)
								   WHERE cf.feedback_id = '" . (int)$feedback_id . "'");
		return $query->row;
	}

	public function addCustomerFeedback($data) {
		$this->db->query("INSERT INTO " . DB_PREFIX . "customer_feedback 
						  SET customer_id = '" . (int)$data['customer_id'] . "',
							  feedback_type = '" . $this->db->escape($data['feedback_type']) . "',
							  subject = '" . $this->db->escape($data['subject']) . "',
							  description = '" . $this->db->escape($data['description']) . "',
							  status = '" . $this->db->escape($data['status']) . "',
							  priority = '" . $this->db->escape($data['priority']) . "',
							  source = '" . $this->db->escape($data['source']) . "',
							  created_at = NOW()");
		
		return $this->db->getLastId();
	}

	public function updateCustomerFeedback($feedback_id, $data) {
		$this->db->query("UPDATE " . DB_PREFIX . "customer_feedback 
						  SET customer_id = '" . (int)$data['customer_id'] . "',
							  feedback_type = '" . $this->db->escape($data['feedback_type']) . "',
							  subject = '" . $this->db->escape($data['subject']) . "',
							  description = '" . $this->db->escape($data['description']) . "',
							  status = '" . $this->db->escape($data['status']) . "',
							  priority = '" . $this->db->escape($data['priority']) . "',
							  updated_at = NOW()
						  WHERE feedback_id = '" . (int)$feedback_id . "'");
	}

	public function deleteCustomerFeedback($feedback_id) {
		$this->db->query("DELETE FROM " . DB_PREFIX . "customer_feedback WHERE feedback_id = '" . (int)$feedback_id . "'");
	}

	public function getTotalCustomerFeedbacks($data = array()) {
		$sql = "SELECT COUNT(*) AS total FROM " . DB_PREFIX . "customer_feedback cf
				LEFT JOIN " . DB_PREFIX . "customer c ON (cf.customer_id = c.customer_id)";
		
		$implode = array();
		
		if (!empty($data['filter_customer'])) {
			$implode[] = "CONCAT(c.firstname, ' ', c.lastname) LIKE '%" . $this->db->escape($data['filter_customer']) . "%'";
		}
		
		if (!empty($data['filter_subject'])) {
			$implode[] = "cf.subject LIKE '%" . $this->db->escape($data['filter_subject']) . "%'";
		}
		
		if (!empty($data['filter_feedback_type'])) {
			$implode[] = "cf.feedback_type = '" . $this->db->escape($data['filter_feedback_type']) . "'";
		}
		
		if (isset($data['filter_status']) && $data['filter_status'] !== '') {
			$implode[] = "cf.status = '" . $this->db->escape($data['filter_status']) . "'";
		}
		
		if (isset($data['filter_priority']) && $data['filter_priority'] !== '') {
			$implode[] = "cf.priority = '" . $this->db->escape($data['filter_priority']) . "'";
		}
		
		if ($implode) {
			$sql .= " WHERE " . implode(" AND ", $implode);
		}
		
		$query = $this->db->query($sql);
		return $query->row['total'];
	}
}
