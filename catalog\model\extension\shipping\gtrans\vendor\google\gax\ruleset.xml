<?xml version="1.0"?>
<ruleset name="Google-Cloud-PHP-PSR2">
  <rule ref="PSR2">
    <!--
      Exclude this sniff because we override methods with underscores
      in the \Grpc\BaseStub class
    -->
    <exclude name="PSR2.Methods.MethodDeclaration.Underscore"/>
  </rule>
  <exclude-pattern>src/LongRunning</exclude-pattern>
  <exclude-pattern>src/Testing</exclude-pattern>
  <file>src</file>
</ruleset>
