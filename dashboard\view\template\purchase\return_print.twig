{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="submit" form="purchase\return-form" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fas fa-save"></i></button>
        <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-light"><i class="fas fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    
    <div class="card">
      <div class="card-header"><i class="fas fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}chart-bar{% elif screen_type == 'settings' %}cog{% else %}pencil-alt{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="card-body">
        <form id="purchase\return-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-add">{{ text_add }}</label>
            <div class="col-sm-10">
              <input type="text" name="add" value="{{ add }}" placeholder="{{ text_add }}" id="input-add" class="form-control" />
              {% if error_add %}
                <div class="invalid-feedback">{{ error_add }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-button_add">{{ text_button_add }}</label>
            <div class="col-sm-10">
              <input type="text" name="button_add" value="{{ button_add }}" placeholder="{{ text_button_add }}" id="input-button_add" class="form-control" />
              {% if error_button_add %}
                <div class="invalid-feedback">{{ error_button_add }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-button_cancel">{{ text_button_cancel }}</label>
            <div class="col-sm-10">
              <input type="text" name="button_cancel" value="{{ button_cancel }}" placeholder="{{ text_button_cancel }}" id="input-button_cancel" class="form-control" />
              {% if error_button_cancel %}
                <div class="invalid-feedback">{{ error_button_cancel }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-button_close">{{ text_button_close }}</label>
            <div class="col-sm-10">
              <input type="text" name="button_close" value="{{ button_close }}" placeholder="{{ text_button_close }}" id="input-button_close" class="form-control" />
              {% if error_button_close %}
                <div class="invalid-feedback">{{ error_button_close }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-button_delete">{{ text_button_delete }}</label>
            <div class="col-sm-10">
              <input type="text" name="button_delete" value="{{ button_delete }}" placeholder="{{ text_button_delete }}" id="input-button_delete" class="form-control" />
              {% if error_button_delete %}
                <div class="invalid-feedback">{{ error_button_delete }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-button_export_excel">{{ text_button_export_excel }}</label>
            <div class="col-sm-10">
              <input type="text" name="button_export_excel" value="{{ button_export_excel }}" placeholder="{{ text_button_export_excel }}" id="input-button_export_excel" class="form-control" />
              {% if error_button_export_excel %}
                <div class="invalid-feedback">{{ error_button_export_excel }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-button_export_pdf">{{ text_button_export_pdf }}</label>
            <div class="col-sm-10">
              <input type="text" name="button_export_pdf" value="{{ button_export_pdf }}" placeholder="{{ text_button_export_pdf }}" id="input-button_export_pdf" class="form-control" />
              {% if error_button_export_pdf %}
                <div class="invalid-feedback">{{ error_button_export_pdf }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-button_save">{{ text_button_save }}</label>
            <div class="col-sm-10">
              <input type="text" name="button_save" value="{{ button_save }}" placeholder="{{ text_button_save }}" id="input-button_save" class="form-control" />
              {% if error_button_save %}
                <div class="invalid-feedback">{{ error_button_save }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-can_add">{{ text_can_add }}</label>
            <div class="col-sm-10">
              <input type="text" name="can_add" value="{{ can_add }}" placeholder="{{ text_can_add }}" id="input-can_add" class="form-control" />
              {% if error_can_add %}
                <div class="invalid-feedback">{{ error_can_add }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-can_approve">{{ text_can_approve }}</label>
            <div class="col-sm-10">
              <input type="text" name="can_approve" value="{{ can_approve }}" placeholder="{{ text_can_approve }}" id="input-can_approve" class="form-control" />
              {% if error_can_approve %}
                <div class="invalid-feedback">{{ error_can_approve }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-can_delete">{{ text_can_delete }}</label>
            <div class="col-sm-10">
              <input type="text" name="can_delete" value="{{ can_delete }}" placeholder="{{ text_can_delete }}" id="input-can_delete" class="form-control" />
              {% if error_can_delete %}
                <div class="invalid-feedback">{{ error_can_delete }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-can_export">{{ text_can_export }}</label>
            <div class="col-sm-10">
              <input type="text" name="can_export" value="{{ can_export }}" placeholder="{{ text_can_export }}" id="input-can_export" class="form-control" />
              {% if error_can_export %}
                <div class="invalid-feedback">{{ error_can_export }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_product">{{ text_column_product }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_product" value="{{ column_product }}" placeholder="{{ text_column_product }}" id="input-column_product" class="form-control" />
              {% if error_column_product %}
                <div class="invalid-feedback">{{ error_column_product }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_quantity">{{ text_column_quantity }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_quantity" value="{{ column_quantity }}" placeholder="{{ text_column_quantity }}" id="input-column_quantity" class="form-control" />
              {% if error_column_quantity %}
                <div class="invalid-feedback">{{ error_column_quantity }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_received_qty">{{ text_column_received_qty }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_received_qty" value="{{ column_received_qty }}" placeholder="{{ text_column_received_qty }}" id="input-column_received_qty" class="form-control" />
              {% if error_column_received_qty %}
                <div class="invalid-feedback">{{ error_column_received_qty }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_return_qty">{{ text_column_return_qty }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_return_qty" value="{{ column_return_qty }}" placeholder="{{ text_column_return_qty }}" id="input-column_return_qty" class="form-control" />
              {% if error_column_return_qty %}
                <div class="invalid-feedback">{{ error_column_return_qty }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_total">{{ text_column_total }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_total" value="{{ column_total }}" placeholder="{{ text_column_total }}" id="input-column_total" class="form-control" />
              {% if error_column_total %}
                <div class="invalid-feedback">{{ error_column_total }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_unit_price">{{ text_column_unit_price }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_unit_price" value="{{ column_unit_price }}" placeholder="{{ text_column_unit_price }}" id="input-column_unit_price" class="form-control" />
              {% if error_column_unit_price %}
                <div class="invalid-feedback">{{ error_column_unit_price }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-comment">{{ text_comment }}</label>
            <div class="col-sm-10">
              <input type="text" name="comment" value="{{ comment }}" placeholder="{{ text_comment }}" id="input-comment" class="form-control" />
              {% if error_comment %}
                <div class="invalid-feedback">{{ error_comment }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-entry_comment">{{ text_entry_comment }}</label>
            <div class="col-sm-10">
              <input type="text" name="entry_comment" value="{{ entry_comment }}" placeholder="{{ text_entry_comment }}" id="input-entry_comment" class="form-control" />
              {% if error_entry_comment %}
                <div class="invalid-feedback">{{ error_entry_comment }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-entry_goods_receipt">{{ text_entry_goods_receipt }}</label>
            <div class="col-sm-10">
              <input type="text" name="entry_goods_receipt" value="{{ entry_goods_receipt }}" placeholder="{{ text_entry_goods_receipt }}" id="input-entry_goods_receipt" class="form-control" />
              {% if error_entry_goods_receipt %}
                <div class="invalid-feedback">{{ error_entry_goods_receipt }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-entry_purchase_order">{{ text_entry_purchase_order }}</label>
            <div class="col-sm-10">
              <input type="text" name="entry_purchase_order" value="{{ entry_purchase_order }}" placeholder="{{ text_entry_purchase_order }}" id="input-entry_purchase_order" class="form-control" />
              {% if error_entry_purchase_order %}
                <div class="invalid-feedback">{{ error_entry_purchase_order }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-entry_return_action">{{ text_entry_return_action }}</label>
            <div class="col-sm-10">
              <input type="text" name="entry_return_action" value="{{ entry_return_action }}" placeholder="{{ text_entry_return_action }}" id="input-entry_return_action" class="form-control" />
              {% if error_entry_return_action %}
                <div class="invalid-feedback">{{ error_entry_return_action }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-entry_return_date">{{ text_entry_return_date }}</label>
            <div class="col-sm-10">
              <input type="text" name="entry_return_date" value="{{ entry_return_date }}" placeholder="{{ text_entry_return_date }}" id="input-entry_return_date" class="form-control" />
              {% if error_entry_return_date %}
                <div class="invalid-feedback">{{ error_entry_return_date }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-entry_return_reason">{{ text_entry_return_reason }}</label>
            <div class="col-sm-10">
              <input type="text" name="entry_return_reason" value="{{ entry_return_reason }}" placeholder="{{ text_entry_return_reason }}" id="input-entry_return_reason" class="form-control" />
              {% if error_entry_return_reason %}
                <div class="invalid-feedback">{{ error_entry_return_reason }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-entry_supplier">{{ text_entry_supplier }}</label>
            <div class="col-sm-10">
              <input type="text" name="entry_supplier" value="{{ entry_supplier }}" placeholder="{{ text_entry_supplier }}" id="input-entry_supplier" class="form-control" />
              {% if error_entry_supplier %}
                <div class="invalid-feedback">{{ error_entry_supplier }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_ajax">{{ text_error_ajax }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_ajax" value="{{ error_ajax }}" placeholder="{{ text_error_ajax }}" id="input-error_ajax" class="form-control" />
              {% if error_error_ajax %}
                <div class="invalid-feedback">{{ error_error_ajax }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_payment_exceeds_due">{{ text_error_payment_exceeds_due }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_payment_exceeds_due" value="{{ error_payment_exceeds_due }}" placeholder="{{ text_error_payment_exceeds_due }}" id="input-error_payment_exceeds_due" class="form-control" />
              {% if error_error_payment_exceeds_due %}
                <div class="invalid-feedback">{{ error_error_payment_exceeds_due }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-export_excel">{{ text_export_excel }}</label>
            <div class="col-sm-10">
              <input type="text" name="export_excel" value="{{ export_excel }}" placeholder="{{ text_export_excel }}" id="input-export_excel" class="form-control" />
              {% if error_export_excel %}
                <div class="invalid-feedback">{{ error_export_excel }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-export_pdf">{{ text_export_pdf }}</label>
            <div class="col-sm-10">
              <input type="text" name="export_pdf" value="{{ export_pdf }}" placeholder="{{ text_export_pdf }}" id="input-export_pdf" class="form-control" />
              {% if error_export_pdf %}
                <div class="invalid-feedback">{{ error_export_pdf }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-goods_receipt_id">{{ text_goods_receipt_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="goods_receipt_id" value="{{ goods_receipt_id }}" placeholder="{{ text_goods_receipt_id }}" id="input-goods_receipt_id" class="form-control" />
              {% if error_goods_receipt_id %}
                <div class="invalid-feedback">{{ error_goods_receipt_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="invalid-feedback">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-items">{{ text_items }}</label>
            <div class="col-sm-10">
              <input type="text" name="items" value="{{ items }}" placeholder="{{ text_items }}" id="input-items" class="form-control" />
              {% if error_items %}
                <div class="invalid-feedback">{{ error_items }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-mode">{{ text_mode }}</label>
            <div class="col-sm-10">
              <input type="text" name="mode" value="{{ mode }}" placeholder="{{ text_mode }}" id="input-mode" class="form-control" />
              {% if error_mode %}
                <div class="invalid-feedback">{{ error_mode }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-pagination">{{ text_pagination }}</label>
            <div class="col-sm-10">
              <input type="text" name="pagination" value="{{ pagination }}" placeholder="{{ text_pagination }}" id="input-pagination" class="form-control" />
              {% if error_pagination %}
                <div class="invalid-feedback">{{ error_pagination }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-po_id">{{ text_po_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="po_id" value="{{ po_id }}" placeholder="{{ text_po_id }}" id="input-po_id" class="form-control" />
              {% if error_po_id %}
                <div class="invalid-feedback">{{ error_po_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-po_number">{{ text_po_number }}</label>
            <div class="col-sm-10">
              <input type="text" name="po_number" value="{{ po_number }}" placeholder="{{ text_po_number }}" id="input-po_number" class="form-control" />
              {% if error_po_number %}
                <div class="invalid-feedback">{{ error_po_number }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-receipt_number">{{ text_receipt_number }}</label>
            <div class="col-sm-10">
              <input type="text" name="receipt_number" value="{{ receipt_number }}" placeholder="{{ text_receipt_number }}" id="input-receipt_number" class="form-control" />
              {% if error_receipt_number %}
                <div class="invalid-feedback">{{ error_receipt_number }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-results">{{ text_results }}</label>
            <div class="col-sm-10">
              <input type="text" name="results" value="{{ results }}" placeholder="{{ text_results }}" id="input-results" class="form-control" />
              {% if error_results %}
                <div class="invalid-feedback">{{ error_results }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-return_action_id">{{ text_return_action_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="return_action_id" value="{{ return_action_id }}" placeholder="{{ text_return_action_id }}" id="input-return_action_id" class="form-control" />
              {% if error_return_action_id %}
                <div class="invalid-feedback">{{ error_return_action_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-return_actions">{{ text_return_actions }}</label>
            <div class="col-sm-10">
              <input type="text" name="return_actions" value="{{ return_actions }}" placeholder="{{ text_return_actions }}" id="input-return_actions" class="form-control" />
              {% if error_return_actions %}
                <div class="invalid-feedback">{{ error_return_actions }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-return_date">{{ text_return_date }}</label>
            <div class="col-sm-10">
              <input type="text" name="return_date" value="{{ return_date }}" placeholder="{{ text_return_date }}" id="input-return_date" class="form-control" />
              {% if error_return_date %}
                <div class="invalid-feedback">{{ error_return_date }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-return_history">{{ text_return_history }}</label>
            <div class="col-sm-10">
              <input type="text" name="return_history" value="{{ return_history }}" placeholder="{{ text_return_history }}" id="input-return_history" class="form-control" />
              {% if error_return_history %}
                <div class="invalid-feedback">{{ error_return_history }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-return_id">{{ text_return_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="return_id" value="{{ return_id }}" placeholder="{{ text_return_id }}" id="input-return_id" class="form-control" />
              {% if error_return_id %}
                <div class="invalid-feedback">{{ error_return_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-return_info">{{ text_return_info }}</label>
            <div class="col-sm-10">
              <input type="text" name="return_info" value="{{ return_info }}" placeholder="{{ text_return_info }}" id="input-return_info" class="form-control" />
              {% if error_return_info %}
                <div class="invalid-feedback">{{ error_return_info }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-return_items">{{ text_return_items }}</label>
            <div class="col-sm-10">
              <input type="text" name="return_items" value="{{ return_items }}" placeholder="{{ text_return_items }}" id="input-return_items" class="form-control" />
              {% if error_return_items %}
                <div class="invalid-feedback">{{ error_return_items }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-return_reason_id">{{ text_return_reason_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="return_reason_id" value="{{ return_reason_id }}" placeholder="{{ text_return_reason_id }}" id="input-return_reason_id" class="form-control" />
              {% if error_return_reason_id %}
                <div class="invalid-feedback">{{ error_return_reason_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-return_reasons">{{ text_return_reasons }}</label>
            <div class="col-sm-10">
              <input type="text" name="return_reasons" value="{{ return_reasons }}" placeholder="{{ text_return_reasons }}" id="input-return_reasons" class="form-control" />
              {% if error_return_reasons %}
                <div class="invalid-feedback">{{ error_return_reasons }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-return_status_id">{{ text_return_status_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="return_status_id" value="{{ return_status_id }}" placeholder="{{ text_return_status_id }}" id="input-return_status_id" class="form-control" />
              {% if error_return_status_id %}
                <div class="invalid-feedback">{{ error_return_status_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-return_statuses">{{ text_return_statuses }}</label>
            <div class="col-sm-10">
              <input type="text" name="return_statuses" value="{{ return_statuses }}" placeholder="{{ text_return_statuses }}" id="input-return_statuses" class="form-control" />
              {% if error_return_statuses %}
                <div class="invalid-feedback">{{ error_return_statuses }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-returns">{{ text_returns }}</label>
            <div class="col-sm-10">
              <input type="text" name="returns" value="{{ returns }}" placeholder="{{ text_returns }}" id="input-returns" class="form-control" />
              {% if error_returns %}
                <div class="invalid-feedback">{{ error_returns }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-supplier_id">{{ text_supplier_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="supplier_id" value="{{ supplier_id }}" placeholder="{{ text_supplier_id }}" id="input-supplier_id" class="form-control" />
              {% if error_supplier_id %}
                <div class="invalid-feedback">{{ error_supplier_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-suppliers">{{ text_suppliers }}</label>
            <div class="col-sm-10">
              <input type="text" name="suppliers" value="{{ suppliers }}" placeholder="{{ text_suppliers }}" id="input-suppliers" class="form-control" />
              {% if error_suppliers %}
                <div class="invalid-feedback">{{ error_suppliers }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_date_added">{{ text_text_date_added }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_date_added" value="{{ text_date_added }}" placeholder="{{ text_text_date_added }}" id="input-text_date_added" class="form-control" />
              {% if error_text_date_added %}
                <div class="invalid-feedback">{{ error_text_date_added }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_form_title">{{ text_text_form_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_form_title" value="{{ text_form_title }}" placeholder="{{ text_text_form_title }}" id="input-text_form_title" class="form-control" />
              {% if error_text_form_title %}
                <div class="invalid-feedback">{{ error_text_form_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_history">{{ text_text_history }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_history" value="{{ text_history }}" placeholder="{{ text_text_history }}" id="input-text_history" class="form-control" />
              {% if error_text_history %}
                <div class="invalid-feedback">{{ error_text_history }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_items_to_return">{{ text_text_items_to_return }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_items_to_return" value="{{ text_items_to_return }}" placeholder="{{ text_text_items_to_return }}" id="input-text_items_to_return" class="form-control" />
              {% if error_text_items_to_return %}
                <div class="invalid-feedback">{{ error_text_items_to_return }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_list">{{ text_text_list }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_list" value="{{ text_list }}" placeholder="{{ text_text_list }}" id="input-text_list" class="form-control" />
              {% if error_text_list %}
                <div class="invalid-feedback">{{ error_text_list }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_loading">{{ text_text_loading }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_loading" value="{{ text_loading }}" placeholder="{{ text_text_loading }}" id="input-text_loading" class="form-control" />
              {% if error_text_loading %}
                <div class="invalid-feedback">{{ error_text_loading }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_no_returnable_items">{{ text_text_no_returnable_items }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_no_returnable_items" value="{{ text_no_returnable_items }}" placeholder="{{ text_text_no_returnable_items }}" id="input-text_no_returnable_items" class="form-control" />
              {% if error_text_no_returnable_items %}
                <div class="invalid-feedback">{{ error_text_no_returnable_items }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_reason">{{ text_text_reason }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_reason" value="{{ text_reason }}" placeholder="{{ text_text_reason }}" id="input-text_reason" class="form-control" />
              {% if error_text_reason %}
                <div class="invalid-feedback">{{ error_text_reason }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_return_details">{{ text_text_return_details }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_return_details" value="{{ text_return_details }}" placeholder="{{ text_text_return_details }}" id="input-text_return_details" class="form-control" />
              {% if error_text_return_details %}
                <div class="invalid-feedback">{{ error_text_return_details }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_return_items">{{ text_text_return_items }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_return_items" value="{{ text_return_items }}" placeholder="{{ text_text_return_items }}" id="input-text_return_items" class="form-control" />
              {% if error_text_return_items %}
                <div class="invalid-feedback">{{ error_text_return_items }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_return_number">{{ text_text_return_number }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_return_number" value="{{ text_return_number }}" placeholder="{{ text_text_return_number }}" id="input-text_return_number" class="form-control" />
              {% if error_text_return_number %}
                <div class="invalid-feedback">{{ error_text_return_number }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_select">{{ text_text_select }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_select" value="{{ text_select }}" placeholder="{{ text_text_select }}" id="input-text_select" class="form-control" />
              {% if error_text_select %}
                <div class="invalid-feedback">{{ error_text_select }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_select_grn">{{ text_text_select_grn }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_select_grn" value="{{ text_select_grn }}" placeholder="{{ text_text_select_grn }}" id="input-text_select_grn" class="form-control" />
              {% if error_text_select_grn %}
                <div class="invalid-feedback">{{ error_text_select_grn }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_select_grn_or_po">{{ text_text_select_grn_or_po }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_select_grn_or_po" value="{{ text_select_grn_or_po }}" placeholder="{{ text_text_select_grn_or_po }}" id="input-text_select_grn_or_po" class="form-control" />
              {% if error_text_select_grn_or_po %}
                <div class="invalid-feedback">{{ error_text_select_grn_or_po }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_select_po">{{ text_text_select_po }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_select_po" value="{{ text_select_po }}" placeholder="{{ text_text_select_po }}" id="input-text_select_po" class="form-control" />
              {% if error_text_select_po %}
                <div class="invalid-feedback">{{ error_text_select_po }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_select_supplier">{{ text_text_select_supplier }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_select_supplier" value="{{ text_select_supplier }}" placeholder="{{ text_text_select_supplier }}" id="input-text_select_supplier" class="form-control" />
              {% if error_text_select_supplier %}
                <div class="invalid-feedback">{{ error_text_select_supplier }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_status">{{ text_text_status }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_status" value="{{ text_status }}" placeholder="{{ text_text_status }}" id="input-text_status" class="form-control" />
              {% if error_text_status %}
                <div class="invalid-feedback">{{ error_text_status }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_supplier">{{ text_text_supplier }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_supplier" value="{{ text_supplier }}" placeholder="{{ text_text_supplier }}" id="input-text_supplier" class="form-control" />
              {% if error_text_supplier %}
                <div class="invalid-feedback">{{ error_text_supplier }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="invalid-feedback">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}