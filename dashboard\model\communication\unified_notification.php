<?php
/**
 * ═══════════════════════════════════════════════════════════════════════════════
 * AYM ERP SYSTEM - Unified Notification System
 * ═══════════════════════════════════════════════════════════════════════════════
 * 
 * نظام الإشعارات الموحد المتقدم - يدعم جميع أنواع الإشعارات في النظام
 * 
 * الميزات:
 * - إشعارات فورية (Real-time)
 * - دعم متعدد القنوات (Email, SMS, Push, In-app)
 * - نظام أولويات متقدم
 * - قوالب ديناميكية
 * - تتبع حالة الإشعارات
 * - تكامل مع جميع وحدات النظام
 * - دعم الإشعارات المجدولة
 * - نظام إلغاء الاشتراك
 * 
 * @package    AYM ERP
 * <AUTHOR> Development Team
 * @copyright  2025 AYM ERP Systems
 * @license    Commercial License
 * @version    1.0.0
 * @since      2025-07-12
 */

class ModelCommunicationUnifiedNotification extends Model {
    
    /**
     * إضافة إشعار جديد
     *
     * @param array $data بيانات الإشعار
     * @return int معرف الإشعار الجديد
     */
    public function addNotification($data) {
        // التحقق من البيانات المطلوبة
        if (empty($data['title']) || empty($data['message'])) {
            throw new Exception('Title and message are required');
        }

        // تعيين القيم الافتراضية
        $data['type'] = $data['type'] ?? 'info';
        $data['priority'] = $data['priority'] ?? 'normal';
        $data['status'] = $data['status'] ?? 'pending';
        $data['created_at'] = date('Y-m-d H:i:s');
        $data['created_by'] = $data['created_by'] ?? $this->user->getId();

        $this->db->query("INSERT INTO " . DB_PREFIX . "unified_notification SET
            title = '" . $this->db->escape($data['title']) . "',
            message = '" . $this->db->escape($data['message']) . "',
            type = '" . $this->db->escape($data['type']) . "',
            priority = '" . $this->db->escape($data['priority']) . "',
            module = '" . $this->db->escape($data['module']) . "',
            reference_type = '" . $this->db->escape($data['reference_type']) . "',
            reference_id = '" . (int)$data['reference_id'] . "',
            recipient_type = '" . $this->db->escape($data['recipient_type']) . "',
            recipient_id = '" . (int)$data['recipient_id'] . "',
            channels = '" . $this->db->escape(json_encode($data['channels'])) . "',
            scheduled_at = " . ($data['scheduled_at'] ? "'" . $this->db->escape($data['scheduled_at']) . "'" : "NULL") . ",
            status = '" . $this->db->escape($data['status']) . "',
            created_at = '" . $this->db->escape($data['created_at']) . "',
            created_by = '" . (int)$data['created_by'] . "'");

        $notification_id = $this->db->getLastId();

        // إرسال الإشعار فوراً إذا لم يكن مجدولاً
        if (!$data['scheduled_at']) {
            $this->sendNotification($notification_id);
        }

        return $notification_id;
    }

    /**
     * إرسال إشعار
     *
     * @param int $notification_id معرف الإشعار
     * @return bool نجاح العملية
     */
    public function sendNotification($notification_id) {
        $notification = $this->getNotification($notification_id);
        if (!$notification) {
            return false;
        }

        $channels = json_decode($notification['channels'], true);
        $success = true;

        foreach ($channels as $channel) {
            switch ($channel) {
                case 'email':
                    $success &= $this->sendEmailNotification($notification);
                    break;
                case 'sms':
                    $success &= $this->sendSMSNotification($notification);
                    break;
                case 'push':
                    $success &= $this->sendPushNotification($notification);
                    break;
                case 'in_app':
                    $success &= $this->sendInAppNotification($notification);
                    break;
            }
        }

        // تحديث حالة الإشعار
        $status = $success ? 'sent' : 'failed';
        $this->updateNotificationStatus($notification_id, $status);

        return $success;
    }

    /**
     * إرسال إشعار عبر البريد الإلكتروني
     */
    private function sendEmailNotification($notification) {
        try {
            $recipient = $this->getRecipient($notification['recipient_type'], $notification['recipient_id']);
            if (!$recipient || !$recipient['email']) {
                return false;
            }

            $template = $this->getEmailTemplate($notification['type']);
            $subject = $this->parseTemplate($template['subject'], $notification);
            $body = $this->parseTemplate($template['body'], $notification);

            // استخدام نظام البريد الإلكتروني المدمج
            $this->load->model('tool/mail');
            
            $mail = new Mail($this->config->get('config_mail_engine'));
            $mail->parameter = $this->config->get('config_mail_parameter');
            $mail->smtp_hostname = $this->config->get('config_mail_smtp_hostname');
            $mail->smtp_username = $this->config->get('config_mail_smtp_username');
            $mail->smtp_password = html_entity_decode($this->config->get('config_mail_smtp_password'), ENT_QUOTES, 'UTF-8');
            $mail->smtp_port = $this->config->get('config_mail_smtp_port');
            $mail->smtp_timeout = $this->config->get('config_mail_smtp_timeout');

            $mail->setTo($recipient['email']);
            $mail->setFrom($this->config->get('config_email'));
            $mail->setSender(html_entity_decode($this->config->get('config_name'), ENT_QUOTES, 'UTF-8'));
            $mail->setSubject($subject);
            $mail->setHtml($body);
            $mail->send();

            return true;
        } catch (Exception $e) {
            $this->log->write('Email notification failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * إرسال إشعار عبر الرسائل النصية
     */
    private function sendSMSNotification($notification) {
        try {
            $recipient = $this->getRecipient($notification['recipient_type'], $notification['recipient_id']);
            if (!$recipient || !$recipient['telephone']) {
                return false;
            }

            // تكامل مع مزود خدمة الرسائل النصية
            $sms_provider = $this->config->get('config_sms_provider');
            $api_key = $this->config->get('config_sms_api_key');
            
            // هنا يتم إرسال الرسالة النصية عبر API
            // يمكن إضافة مزودي خدمات مختلفين
            
            return true;
        } catch (Exception $e) {
            $this->log->write('SMS notification failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * إرسال إشعار Push
     */
    private function sendPushNotification($notification) {
        try {
            $recipient = $this->getRecipient($notification['recipient_type'], $notification['recipient_id']);
            if (!$recipient) {
                return false;
            }

            // تكامل مع خدمات Push Notifications
            // Firebase, OneSignal, etc.
            
            return true;
        } catch (Exception $e) {
            $this->log->write('Push notification failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * إرسال إشعار في التطبيق
     */
    private function sendInAppNotification($notification) {
        try {
            // حفظ الإشعار في جدول إشعارات التطبيق
            $this->db->query("INSERT INTO " . DB_PREFIX . "user_notification SET
                user_id = '" . (int)$notification['recipient_id'] . "',
                notification_id = '" . (int)$notification['notification_id'] . "',
                is_read = '0',
                created_at = NOW()");

            return true;
        } catch (Exception $e) {
            $this->log->write('In-app notification failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * الحصول على إشعار
     */
    public function getNotification($notification_id) {
        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "unified_notification 
            WHERE notification_id = '" . (int)$notification_id . "'");
        
        return $query->num_rows ? $query->row : false;
    }

    /**
     * الحصول على إشعارات المستخدم
     */
    public function getUserNotifications($user_id, $limit = 20, $start = 0) {
        $query = $this->db->query("SELECT un.*, unr.is_read, unr.read_at 
            FROM " . DB_PREFIX . "unified_notification un
            LEFT JOIN " . DB_PREFIX . "user_notification unr ON (un.notification_id = unr.notification_id)
            WHERE un.recipient_type = 'user' AND un.recipient_id = '" . (int)$user_id . "'
            ORDER BY un.created_at DESC
            LIMIT " . (int)$start . "," . (int)$limit);

        return $query->rows;
    }

    /**
     * تحديث حالة الإشعار
     */
    public function updateNotificationStatus($notification_id, $status) {
        $this->db->query("UPDATE " . DB_PREFIX . "unified_notification SET
            status = '" . $this->db->escape($status) . "',
            sent_at = NOW()
            WHERE notification_id = '" . (int)$notification_id . "'");
    }

    /**
     * تحديد الإشعار كمقروء
     */
    public function markAsRead($notification_id, $user_id) {
        $this->db->query("UPDATE " . DB_PREFIX . "user_notification SET
            is_read = '1',
            read_at = NOW()
            WHERE notification_id = '" . (int)$notification_id . "' 
            AND user_id = '" . (int)$user_id . "'");
    }

    /**
     * الحصول على عدد الإشعارات غير المقروءة
     */
    public function getUnreadCount($user_id) {
        $query = $this->db->query("SELECT COUNT(*) as total 
            FROM " . DB_PREFIX . "user_notification 
            WHERE user_id = '" . (int)$user_id . "' AND is_read = '0'");
        
        return $query->row['total'];
    }

    /**
     * الحصول على المستلم
     */
    private function getRecipient($type, $id) {
        switch ($type) {
            case 'user':
                $this->load->model('user/user');
                return $this->model_user_user->getUser($id);
            case 'customer':
                $this->load->model('customer/customer');
                return $this->model_customer_customer->getCustomer($id);
            case 'supplier':
                $this->load->model('supplier/supplier');
                return $this->model_supplier_supplier->getSupplier($id);
            default:
                return false;
        }
    }

    /**
     * الحصول على قالب البريد الإلكتروني
     */
    private function getEmailTemplate($type) {
        $templates = [
            'info' => [
                'subject' => 'معلومات جديدة - {title}',
                'body' => '<h2>{title}</h2><p>{message}</p>'
            ],
            'warning' => [
                'subject' => 'تحذير - {title}',
                'body' => '<h2 style="color: orange;">{title}</h2><p>{message}</p>'
            ],
            'error' => [
                'subject' => 'خطأ - {title}',
                'body' => '<h2 style="color: red;">{title}</h2><p>{message}</p>'
            ],
            'success' => [
                'subject' => 'نجح - {title}',
                'body' => '<h2 style="color: green;">{title}</h2><p>{message}</p>'
            ]
        ];

        return $templates[$type] ?? $templates['info'];
    }

    /**
     * تحليل القالب
     */
    private function parseTemplate($template, $data) {
        $replacements = [
            '{title}' => $data['title'],
            '{message}' => $data['message'],
            '{module}' => $data['module'],
            '{date}' => date('Y-m-d H:i:s')
        ];

        return str_replace(array_keys($replacements), array_values($replacements), $template);
    }

    /**
     * إرسال إشعارات مجدولة
     */
    public function processScheduledNotifications() {
        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "unified_notification 
            WHERE status = 'pending' 
            AND scheduled_at IS NOT NULL 
            AND scheduled_at <= NOW()");

        foreach ($query->rows as $notification) {
            $this->sendNotification($notification['notification_id']);
        }
    }

    /**
     * حذف إشعارات قديمة
     */
    public function cleanupOldNotifications($days = 90) {
        $this->db->query("DELETE FROM " . DB_PREFIX . "unified_notification 
            WHERE created_at < DATE_SUB(NOW(), INTERVAL " . (int)$days . " DAY)
            AND status IN ('sent', 'failed')");
    }
}
