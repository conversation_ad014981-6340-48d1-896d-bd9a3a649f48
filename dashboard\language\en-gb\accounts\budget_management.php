<?php
// Heading
$_['heading_title']                    = 'Advanced Budget Management';

// Text
$_['text_list']                        = 'Budget List';
$_['text_add']                         = 'Add New Budget';
$_['text_edit']                        = 'Edit Budget';
$_['text_default']                     = 'Default';
$_['text_success_add']                 = 'Budget added successfully!';
$_['text_success_edit']                = 'Budget updated successfully!';
$_['text_success_delete']              = 'Budget deleted successfully!';
$_['text_success_copy']                = 'Budget copied successfully!';
$_['text_success_approve']             = 'Budget approved successfully!';
$_['text_confirm_delete']              = 'Are you sure you want to delete this budget?';
$_['text_confirm_approve']             = 'Are you sure you want to approve this budget?';
$_['text_no_results']                  = 'No budgets found';
$_['text_home']                        = 'Home';

// Budget Types
$_['text_budget_type_annual']          = 'Annual Budget';
$_['text_budget_type_quarterly']       = 'Quarterly Budget';
$_['text_budget_type_monthly']         = 'Monthly Budget';
$_['text_budget_type_project']         = 'Project Budget';
$_['text_budget_type_department']      = 'Department Budget';
$_['text_budget_type_capital']         = 'Capital Budget';
$_['text_budget_type_operational']     = 'Operational Budget';

// Budget Status
$_['text_status_draft']                = 'Draft';
$_['text_status_pending']              = 'Pending Approval';
$_['text_status_approved']             = 'Approved';
$_['text_status_active']               = 'Active';
$_['text_status_closed']               = 'Closed';
$_['text_status_cancelled']            = 'Cancelled';

// Analysis Types
$_['text_variance_analysis']           = 'Variance Analysis';
$_['text_performance_analysis']        = 'Performance Analysis';
$_['text_forecast_analysis']           = 'Forecast Analysis';
$_['text_scenario_analysis']           = 'Scenario Analysis';
$_['text_budget_analysis']             = 'Budget Analysis';

// Scenarios
$_['text_scenario_optimistic']         = 'Optimistic';
$_['text_scenario_realistic']          = 'Realistic';
$_['text_scenario_pessimistic']        = 'Pessimistic';

// Financial Terms
$_['text_revenue']                     = 'Revenue';
$_['text_expenses']                    = 'Expenses';
$_['text_net_income']                  = 'Net Income';
$_['text_gross_profit']                = 'Gross Profit';
$_['text_operating_expenses']          = 'Operating Expenses';
$_['text_capital_expenditure']         = 'Capital Expenditure';
$_['text_cash_flow']                   = 'Cash Flow';

// Variance Analysis
$_['text_favorable_variance']          = 'Favorable Variance';
$_['text_unfavorable_variance']        = 'Unfavorable Variance';
$_['text_budget_vs_actual']            = 'Budget vs Actual';
$_['text_variance_percentage']         = 'Variance Percentage';
$_['text_variance_amount']             = 'Variance Amount';

// Performance Indicators
$_['text_kpi']                         = 'Key Performance Indicators';
$_['text_roi']                         = 'Return on Investment';
$_['text_profit_margin']               = 'Profit Margin';
$_['text_cost_efficiency']             = 'Cost Efficiency';
$_['text_budget_utilization']          = 'Budget Utilization';

// Column
$_['column_budget_id']                 = 'Budget ID';
$_['column_name']                      = 'Budget Name';
$_['column_type']                      = 'Type';
$_['column_period']                    = 'Period';
$_['column_start_date']                = 'Start Date';
$_['column_end_date']                  = 'End Date';
$_['column_total_budget']              = 'Total Budget';
$_['column_actual_amount']             = 'Actual Amount';
$_['column_variance']                  = 'Variance';
$_['column_variance_percentage']       = 'Variance %';
$_['column_status']                    = 'Status';
$_['column_created_by']                = 'Created By';
$_['column_created_date']              = 'Created Date';
$_['column_approved_by']               = 'Approved By';
$_['column_approved_date']             = 'Approved Date';
$_['column_action']                    = 'Action';

// Entry
$_['entry_name']                       = 'Budget Name';
$_['entry_description']                = 'Description';
$_['entry_type']                       = 'Budget Type';
$_['entry_period']                     = 'Period';
$_['entry_start_date']                 = 'Start Date';
$_['entry_end_date']                   = 'End Date';
$_['entry_department']                 = 'Department';
$_['entry_project']                    = 'Project';
$_['entry_currency']                   = 'Currency';
$_['entry_notes']                      = 'Notes';

// Budget Items
$_['text_budget_items']                = 'Budget Items';
$_['text_add_item']                    = 'Add Item';
$_['text_edit_item']                   = 'Edit Item';
$_['text_delete_item']                 = 'Delete Item';
$_['entry_account']                    = 'Account';
$_['entry_amount']                     = 'Amount';
$_['entry_quantity']                   = 'Quantity';
$_['entry_unit_price']                 = 'Unit Price';
$_['entry_item_notes']                 = 'Item Notes';

// Button
$_['button_add_budget']                = 'Add Budget';
$_['button_edit_budget']               = 'Edit Budget';
$_['button_delete_budget']             = 'Delete Budget';
$_['button_copy_budget']               = 'Copy Budget';
$_['button_approve_budget']            = 'Approve Budget';
$_['button_view_analysis']             = 'View Analysis';
$_['button_export_excel']              = 'Export Excel';
$_['button_export_pdf']                = 'Export PDF';
$_['button_export_csv']                = 'Export CSV';
$_['button_generate_forecast']         = 'Generate Forecast';
$_['button_scenario_analysis']         = 'Scenario Analysis';

// Error
$_['error_permission']                 = 'Warning: You do not have permission to access budget management!';
$_['error_name_required']              = 'Budget name is required!';
$_['error_type_required']              = 'Budget type is required!';
$_['error_start_date_required']        = 'Start date is required!';
$_['error_end_date_required']          = 'End date is required!';
$_['error_date_range']                 = 'End date must be after start date!';
$_['error_budget_exists']              = 'A budget with the same name and period already exists!';
$_['error_budget_not_found']           = 'Budget not found!';
$_['error_budget_approved']            = 'Cannot modify an approved budget!';
$_['error_no_items']                   = 'At least one budget item must be added!';
$_['error_invalid_amount']             = 'Invalid amount!';
$_['error_account_required']           = 'Account is required!';
$_['error_amount_required']            = 'Amount is required!';

// Success
$_['text_success_export']              = 'Budget exported successfully!';
$_['text_success_forecast']            = 'Forecast generated successfully!';
$_['text_success_analysis']            = 'Analysis created successfully!';

// Help
$_['help_budget_type']                 = 'Select budget type based on purpose and time period';
$_['help_period']                      = 'Time period covered by the budget';
$_['help_variance_analysis']           = 'Compare planned figures with actual figures';
$_['help_scenario_analysis']           = 'Analyze different scenarios for financial planning';

// Additional
$_['text_total']                       = 'Total';
$_['text_subtotal']                    = 'Subtotal';
$_['text_grand_total']                 = 'Grand Total';
$_['text_percentage']                  = 'Percentage';
$_['text_currency_egp']                = 'Egyptian Pound';
$_['text_generated_by']                = 'Generated By';
$_['text_generated_on']                = 'Generated On';
$_['text_page']                        = 'Page';
$_['text_of']                          = 'of';

// Dashboard
$_['text_budget_dashboard']            = 'Budget Dashboard';
$_['text_total_budgets']               = 'Total Budgets';
$_['text_active_budgets']              = 'Active Budgets';
$_['text_pending_approval']            = 'Pending Approval';
$_['text_budget_utilization_rate']     = 'Budget Utilization Rate';
$_['text_top_variances']               = 'Top Variances';
$_['text_budget_alerts']               = 'Budget Alerts';

// Workflow
$_['text_workflow']                    = 'Workflow';
$_['text_approval_workflow']           = 'Approval Workflow';
$_['text_submit_for_approval']         = 'Submit for Approval';
$_['text_approve']                     = 'Approve';
$_['text_reject']                      = 'Reject';
$_['text_revision_required']           = 'Revision Required';
$_['text_approved']                    = 'Approved';
$_['text_rejected']                    = 'Rejected';

// Forecasting
$_['text_forecasting']                 = 'Financial Forecasting';
$_['text_forecast_method']             = 'Forecast Method';
$_['text_linear_regression']           = 'Linear Regression';
$_['text_moving_average']              = 'Moving Average';
$_['text_exponential_smoothing']       = 'Exponential Smoothing';
$_['text_forecast_period']             = 'Forecast Period';
$_['text_forecast_accuracy']           = 'Forecast Accuracy';

// Reports
$_['text_budget_reports']              = 'Budget Reports';
$_['text_budget_summary']              = 'Budget Summary';
$_['text_detailed_budget']             = 'Detailed Budget';
$_['text_variance_report']             = 'Variance Report';
$_['text_performance_report']          = 'Performance Report';
$_['text_forecast_report']             = 'Forecast Report';
?>
