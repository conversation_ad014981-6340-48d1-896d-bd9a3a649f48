<style>
/* ═══════════════════════════════════════════════════════════════════════════════
   AYM ERP FOOTER STYLES - Enterprise Grade Plus
   ═══════════════════════════════════════════════════════════════════════════════ */

:root {
  --footer-bg: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f0f23 100%);
  --footer-text: rgba(255, 255, 255, 0.9);
  --footer-text-muted: rgba(255, 255, 255, 0.6);
  --footer-border: rgba(255, 255, 255, 0.1);
  --footer-accent: #667eea;
  --footer-success: #00b894;
  --footer-warning: #fdcb6e;
  --footer-danger: #e17055;
}

#footer {
  background: var(--footer-bg);
  border-top: 2px solid var(--footer-border);
  padding: 20px 0;
  margin-top: auto;
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

#footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 50%, rgba(102, 126, 234, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 50%, rgba(0, 184, 148, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.footer-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
  z-index: 2;
}

.footer-grid {
  display: grid;
  grid-template-columns: 1fr auto auto;
  gap: 30px;
  align-items: center;
}

.footer-brand {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.footer-logo {
  color: var(--footer-text);
  font-size: 18px;
  font-weight: 700;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 10px;
  transition: all 0.3s ease;
}

.footer-logo:hover {
  color: var(--footer-accent);
  text-shadow: 0 0 10px rgba(102, 126, 234, 0.5);
}

.footer-logo i {
  font-size: 24px;
  background: linear-gradient(135deg, var(--footer-accent), #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.footer-version {
  color: var(--footer-text-muted);
  font-size: 12px;
  font-weight: 500;
}

.footer-links {
  display: flex;
  gap: 20px;
  align-items: center;
}

.footer-link {
  color: var(--footer-text-muted);
  text-decoration: none;
  font-size: 13px;
  font-weight: 500;
  padding: 8px 12px;
  border-radius: 6px;
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.footer-link:hover {
  color: var(--footer-text);
  background: rgba(255, 255, 255, 0.05);
  border-color: var(--footer-border);
  transform: translateY(-1px);
}

.footer-system-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 200px;
}

.system-status {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  font-weight: 500;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.status-operational { background: var(--footer-success); }
.status-warning { background: var(--footer-warning); }
.status-error { background: var(--footer-danger); }

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.system-metrics {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
  font-size: 11px;
  color: var(--footer-text-muted);
}

.metric-item {
  display: flex;
  justify-content: space-between;
  padding: 2px 0;
}

.metric-value {
  color: var(--footer-text);
  font-weight: 600;
}

/* ═══════════════════════════════════════════════════════════════════════════════
   RESPONSIVE DESIGN
   ═══════════════════════════════════════════════════════════════════════════════ */

@media (max-width: 768px) {
  .footer-grid {
    grid-template-columns: 1fr;
    gap: 20px;
    text-align: center;
  }

  .footer-links {
    justify-content: center;
    flex-wrap: wrap;
  }

  .footer-system-info {
    align-items: center;
  }

  .system-metrics {
    grid-template-columns: 1fr;
    max-width: 250px;
  }
}

/* ═══════════════════════════════════════════════════════════════════════════════
   ACCESSIBILITY ENHANCEMENTS
   ═══════════════════════════════════════════════════════════════════════════════ */

.footer-link:focus {
  outline: 2px solid var(--footer-accent);
  outline-offset: 2px;
}

@media (prefers-reduced-motion: reduce) {
  .status-indicator {
    animation: none;
  }

  .footer-logo,
  .footer-link {
    transition: none;
  }
}

@media (prefers-contrast: high) {
  #footer {
    border-top-width: 3px;
  }

  .footer-link {
    border-width: 2px;
  }
}

/* ═══════════════════════════════════════════════════════════════════════════════
   SECURITY ENHANCEMENTS
   ═══════════════════════════════════════════════════════════════════════════════ */

.footer-link[href^="http"]:not([href*="aym-erp.com"])::after {
  content: " ↗";
  font-size: 10px;
  opacity: 0.7;
}

/* ═══════════════════════════════════════════════════════════════════════════════
   PERFORMANCE INDICATORS
   ═══════════════════════════════════════════════════════════════════════════════ */

.metric-value[data-metric="response_time"] {
  color: var(--footer-success);
}

.metric-value[data-metric="response_time"][data-slow="true"] {
  color: var(--footer-warning);
}

.metric-value[data-metric="response_time"][data-very-slow="true"] {
  color: var(--footer-danger);
}

.metric-value[data-metric="memory_usage"][data-high="true"] {
  color: var(--footer-warning);
}

.metric-value[data-metric="memory_usage"][data-critical="true"] {
  color: var(--footer-danger);
}

/* ═══════════════════════════════════════════════════════════════════════════════
   RTL SUPPORT
   ═══════════════════════════════════════════════════════════════════════════════ */

[dir="rtl"] .footer-grid {
  direction: rtl;
}

[dir="rtl"] .footer-logo {
  flex-direction: row-reverse;
}

[dir="rtl"] .system-status {
  flex-direction: row-reverse;
}

[dir="rtl"] .footer-links {
  flex-direction: row-reverse;
}

[dir="rtl"] .metric-item {
  text-align: right;
}
</style>

<footer id="footer" role="contentinfo" aria-label="Site footer">
  <div class="footer-container">
    <div class="footer-grid">

      <!-- Brand Section -->
      <div class="footer-brand">
        <a href="{{ company_url }}" class="footer-logo" target="_blank" rel="noopener" aria-label="AYM ERP Homepage">
          <i class="fa fa-brain" aria-hidden="true"></i>
          <span>{{ text_footer|raw }}</span>
        </a>
        <div class="footer-version">{{ text_version }}</div>
      </div>

      <!-- Links Section -->
      {% if show_system_info %}
      <nav class="footer-links" aria-label="Footer navigation">
        <a href="{{ support_url }}" class="footer-link" target="_blank" rel="noopener">
          <i class="fa fa-life-ring" aria-hidden="true"></i>
          {{ text_support }}
        </a>
        <a href="{{ documentation_url }}" class="footer-link" target="_blank" rel="noopener">
          <i class="fa fa-book" aria-hidden="true"></i>
          {{ text_documentation }}
        </a>
      </nav>
      {% endif %}

      <!-- System Information -->
      {% if show_performance %}
      <div class="footer-system-info" aria-label="System information">
        <div class="system-status">
          <span class="status-indicator status-{{ system_status }}" aria-hidden="true"></span>
          <span>{{ text_system_status }}:
            {% if system_status == 'operational' %}
              <span class="metric-value">{{ 'Operational'|default('Operational') }}</span>
            {% elseif system_status == 'warning' %}
              <span class="metric-value">{{ 'Warning'|default('Warning') }}</span>
            {% else %}
              <span class="metric-value">{{ 'Error'|default('Error') }}</span>
            {% endif %}
          </span>
        </div>

        <div class="system-metrics">
          <div class="metric-item">
            <span>{{ text_memory_usage }}:</span>
            <span class="metric-value" data-metric="memory_usage">{{ memory_usage }}</span>
          </div>
          <div class="metric-item">
            <span>{{ text_response_time }}:</span>
            <span class="metric-value" data-metric="response_time">{{ response_time }}ms</span>
          </div>
          <div class="metric-item">
            <span>{{ text_session_time }}:</span>
            <span class="metric-value" data-metric="session_duration">{{ session_duration }}</span>
          </div>
          <div class="metric-item">
            <span>{{ text_database_queries }}:</span>
            <span class="metric-value" data-metric="database_queries">{{ database_queries }}</span>
          </div>
        </div>
      </div>
      {% endif %}

    </div>
  </div>
</footer>

</div>

<!-- Performance Analytics -->
<script type="text/javascript">
// Enhanced Performance monitoring with error handling
(function() {
  'use strict';

  try {
    // Track page load performance
    window.addEventListener('load', function() {
      try {
        if ('performance' in window && performance.getEntriesByType) {
          const perfData = performance.getEntriesByType('navigation')[0];
          if (perfData && perfData.loadEventEnd && perfData.loadEventStart) {
            const loadTime = perfData.loadEventEnd - perfData.loadEventStart;

            // Send performance data to analytics
            if (typeof gtag !== 'undefined') {
              gtag('event', 'page_load_time', {
                event_category: 'Performance',
                event_label: 'Footer Load',
                value: Math.round(loadTime),
                custom_parameters: {
                  page_type: 'footer',
                  user_agent: navigator.userAgent.substring(0, 100)
                }
              });
            }

            // Store performance metrics for system monitoring
            if (window.localStorage) {
              const perfMetrics = {
                loadTime: loadTime,
                timestamp: Date.now(),
                url: window.location.pathname
              };
              localStorage.setItem('aym_perf_metrics', JSON.stringify(perfMetrics));
            }
          }
        }
      } catch (perfError) {
        console.warn('Performance tracking error:', perfError);
      }
    });

    // Track user session duration with enhanced data
    let sessionStart = Date.now();
    let interactionCount = 0;

    // Track user interactions
    ['click', 'keydown', 'scroll'].forEach(eventType => {
      document.addEventListener(eventType, function() {
        interactionCount++;
      }, { passive: true });
    });

    window.addEventListener('beforeunload', function() {
      try {
        const sessionDuration = Date.now() - sessionStart;
        if (typeof gtag !== 'undefined') {
          gtag('event', 'session_duration', {
            event_category: 'Engagement',
            event_label: 'Footer Session',
            value: Math.round(sessionDuration / 1000),
            custom_parameters: {
              interaction_count: interactionCount,
              page_type: 'footer'
            }
          });
        }
      } catch (sessionError) {
        console.warn('Session tracking error:', sessionError);
      }
    });

    // Enhanced error tracking with context
    window.addEventListener('error', function(e) {
      try {
        if (typeof gtag !== 'undefined') {
          gtag('event', 'javascript_error', {
            event_category: 'Error',
            event_label: e.message ? e.message.substring(0, 100) : 'Unknown error',
            value: 1,
            custom_parameters: {
              filename: e.filename || 'unknown',
              line_number: e.lineno || 0,
              column_number: e.colno || 0,
              stack: e.error && e.error.stack ? e.error.stack.substring(0, 500) : 'No stack trace'
            }
          });
        }
      } catch (errorTrackingError) {
        console.warn('Error tracking failed:', errorTrackingError);
      }
    });

    // Track unhandled promise rejections
    window.addEventListener('unhandledrejection', function(e) {
      try {
        if (typeof gtag !== 'undefined') {
          gtag('event', 'promise_rejection', {
            event_category: 'Error',
            event_label: e.reason ? String(e.reason).substring(0, 100) : 'Unknown rejection',
            value: 1
          });
        }
      } catch (rejectionError) {
        console.warn('Promise rejection tracking error:', rejectionError);
      }
    });

  } catch (initError) {
    console.warn('Footer analytics initialization failed:', initError);
  }
})();

// Microsoft Clarity Analytics
(function(c,l,a,r,i,t,y){
    c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
    t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
    y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
})(window, document, "clarity", "script", "q9pabj480y");

// Enhanced Accessibility and UX improvements
document.addEventListener('DOMContentLoaded', function() {
  try {
    // Add keyboard navigation for footer links
    const footerLinks = document.querySelectorAll('.footer-link');
    footerLinks.forEach(link => {
      link.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          this.click();
        }
      });

      // Add focus indicators
      link.addEventListener('focus', function() {
        this.setAttribute('aria-expanded', 'true');
      });

      link.addEventListener('blur', function() {
        this.removeAttribute('aria-expanded');
      });
    });

    // Add tooltips for system metrics
    const metricItems = document.querySelectorAll('.metric-item');
    metricItems.forEach(item => {
      const label = item.querySelector('span:first-child').textContent;
      const value = item.querySelector('.metric-value');

      if (value) {
        value.setAttribute('title', `Current ${label}: ${value.textContent}`);
        value.setAttribute('aria-label', `${label} is currently ${value.textContent}`);
      }
    });

    // Add system status announcements for screen readers
    const statusIndicator = document.querySelector('.status-indicator');
    if (statusIndicator) {
      const statusText = document.querySelector('.system-status span:last-child .metric-value');
      if (statusText) {
        statusIndicator.setAttribute('aria-label', `System status: ${statusText.textContent}`);
        statusIndicator.setAttribute('role', 'status');
        statusIndicator.setAttribute('aria-live', 'polite');
      }
    }

  // Update system status in real-time
  setInterval(function() {
    fetch(window.location.origin + window.location.pathname + '?route=common/footer/status', {
      method: 'GET',
      headers: {
        'X-Requested-With': 'XMLHttpRequest'
      }
    })
    .then(response => response.json())
    .then(data => {
      const statusIndicator = document.querySelector('.status-indicator');
      const statusText = document.querySelector('.system-status span:last-child .metric-value');

      if (statusIndicator && statusText) {
        statusIndicator.className = `status-indicator status-${data.status}`;
        statusText.textContent = data.status_text;
      }

      // Update metrics with performance indicators
      if (data.metrics) {
        Object.keys(data.metrics).forEach(key => {
          const element = document.querySelector(`[data-metric="${key}"]`);
          if (element) {
            element.textContent = data.metrics[key];

            // Add performance indicators
            if (key === 'response_time') {
              const responseTime = parseFloat(data.metrics[key]);
              element.removeAttribute('data-slow');
              element.removeAttribute('data-very-slow');

              if (responseTime > 1000) {
                element.setAttribute('data-very-slow', 'true');
              } else if (responseTime > 500) {
                element.setAttribute('data-slow', 'true');
              }
            }

            if (key === 'memory_usage') {
              const memoryMB = parseFloat(data.metrics[key]);
              element.removeAttribute('data-high');
              element.removeAttribute('data-critical');

              if (memoryMB > 512) {
                element.setAttribute('data-critical', 'true');
              } else if (memoryMB > 256) {
                element.setAttribute('data-high', 'true');
              }
            }
          }
        });
      }
    })
    .catch(error => {
      console.warn('Failed to update system status:', error);

      // Show user-friendly error indicator
      const statusIndicator = document.querySelector('.status-indicator');
      if (statusIndicator) {
        statusIndicator.className = 'status-indicator status-warning';
        statusIndicator.setAttribute('aria-label', 'System status: Connection error');
      }
    });
  }, 30000); // Update every 30 seconds

  } catch (domError) {
    console.warn('Footer DOM enhancements failed:', domError);
  }
});
</script>

</body></html>
