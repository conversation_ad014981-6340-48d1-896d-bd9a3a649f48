<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>{{ text_quotation_comparison }}</title>
  <style type="text/css">
    body {
      font-family: Arial, sans-serif;
      font-size: 10pt;
      color: #333;
    }
    
    h1, h2, h3 {
      margin: 10px 0;
    }
    
    table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 10px;
    }
    
    table th, table td {
      border: 1px solid #ddd;
      padding: 0.5em;
      text-align: left;
    }
    
    table th {
      background-color: #f5f5f5;
      font-weight: bold;
    }
    
    .bg-success {
      background-color: #dff0d8;
    }
    
    .text-right {
      text-align: right;
    }
    
    .text-center {
      text-align: center;
    }
    
    .label {
      display: inline;
      padding: .2em .6em .3em;
      font-size: 75%;
      font-weight: bold;
      line-height: 1;
      color: #fff;
      text-align: center;
      white-space: nowrap;
      vertical-align: baseline;
      border-radius: .25em;
    }
    
    .label-success {
      background-color: #5cb85c;
    }
    
    .requisition-info {
      margin-bottom: 20px;
      border: 1px solid #ddd;
      padding: 10px;
      background-color: #f9f9f9;
    }
    
    .info-row {
      margin-bottom: 5px;
    }
    
    .company-info {
      margin-bottom: 20px;
    }
    
    .company-logo {
      text-align: right;
    }
    
    .company-logo img {
      max-height: 60px;
    }
    
    .clearfix:after {
      content: "";
      display: table;
      clear: both;
    }
    
    .col-4 {
      float: left;
      width: 33%;
    }
    
    .best-price {
      background-color: #dff0d8;
    }
  </style>
</head>
<body>
  <div class="company-info clearfix">
    <div style="float: left; width: 60%;">
      <h1>{{ text_quotation_comparison }}</h1>
      <div><strong>{{ company_name }}</strong></div>
      <div>{{ text_comparison_date }}: {{ text_date }}</div>
    </div>
    <div class="company-logo" style="float: right; width: 40%;">
      {% if company_logo %}
      <img src="{{ company_logo }}" alt="{{ company_name }}">
      {% endif %}
    </div>
  </div>
  
  <div class="requisition-info">
    <h3>{{ text_requisition_details }}</h3>
    <div class="clearfix">
      <div class="col-4">
        <div class="info-row"><strong>{{ text_requisition_number }}:</strong> {{ requisition.req_number }}</div>
      </div>
      <div class="col-4">
        <div class="info-row"><strong>{{ text_branch }}:</strong> {{ requisition.branch_name }}</div>
      </div>
      <div class="col-4">
        <div class="info-row"><strong>{{ text_department }}:</strong> {{ requisition.user_group_name }}</div>
      </div>
    </div>
  </div>
  
  <table>
    <thead>
      <tr>
        <th rowspan="2">{{ column_product }}</th>
        <th rowspan="2" class="text-right">{{ column_quantity }}</th>
        <th rowspan="2">{{ column_unit }}</th>
        
        {% for quotation in quotations %}
        <th class="text-center {% if quotation.has_lowest_total %}best-price{% endif %}">
          {{ quotation.supplier_name }}<br>
          <small>{{ quotation.quotation_number }}</small>
          {% if quotation.has_lowest_total %}
          <br><span class="label label-success">{{ text_lowest_total }}</span>
          {% endif %}
        </th>
        {% endfor %}
      </tr>
    </thead>
    
    <tbody>
      {% for item in comparison %}
      <tr>
        <td>{{ item.product_name }}</td>
        <td class="text-right">{{ item.quantity }}</td>
        <td>{{ item.unit_name }}</td>
        
        {% for quotation_id, quotation in quotations %}
          {% if item.supplier_prices[quotation_id] is defined %}
            <td class="text-right {% if item.supplier_prices[quotation_id].is_best_price %}best-price{% endif %}">
              {{ item.supplier_prices[quotation_id].unit_price_formatted }}
              {% if item.supplier_prices[quotation_id].is_best_price %}
              <span class="label label-success">{{ text_best_price }}</span>
              {% endif %}
            </td>
          {% else %}
            <td class="text-center">-</td>
          {% endif %}
        {% endfor %}
      </tr>
      {% endfor %}
    </tbody>
    
    <tfoot>
      <tr>
        <th colspan="3" class="text-right">{{ text_total }}:</th>
        
        {% for quotation in quotations %}
        <th class="text-right {% if quotation.has_lowest_total %}best-price{% endif %}">
          {{ quotation.total_formatted }}
        </th>
        {% endfor %}
      </tr>
    </tfoot>
  </table>
</body>
</html>