# تحليل شامل لمعمارية OpenCart 3.x والتفاعل بـ AJAX

## نظرة عامة على المعمارية

OpenCart 3.x يستخدم معمارية **MVC (Model-View-Controller)** متقدمة مع نظام **Registry Pattern** و **Event-Driven Architecture**.

## 1. نمط MVC في OpenCart

### البنية الأساسية:
```
dashboard/
├── controller/     # Controllers - منطق التحكم
├── model/         # Models - منطق البيانات
└── view/template/ # Views - قوالب العرض (Twig)
```

### تدفق البيانات:
```
Request → Router → Action → Controller → Model → Database
                     ↓
Response ← View ← Controller ← Model ← Database
```

## 2. نظام الـ Routing المتقدم

### كيفية عمل الـ Router:

#### في `system/engine/router.php`:
```php
class Router {
    private $registry;
    private $pre_action = array();
    private $error;
    
    public function dispatch(Action $action, Action $error) {
        // تنفيذ Pre-actions أولاً
        foreach ($this->pre_action as $pre_action) {
            $result = $this->execute($pre_action);
            if ($result instanceof Action) {
                $action = $result;
                break;
            }
        }
        
        // تنفيذ Action الرئيسي
        while ($action instanceof Action) {
            $action = $this->execute($action);
        }
    }
}
```

### نظام Action:

#### في `system/engine/action.php`:
```php
class Action {
    private $route;
    private $method = 'index';
    
    public function __construct($route) {
        // تحليل الـ route: "catalog/product/edit"
        $parts = explode('/', $route);
        
        // البحث عن الملف المناسب
        while ($parts) {
            $file = DIR_APPLICATION . 'controller/' . implode('/', $parts) . '.php';
            if (is_file($file)) {
                $this->route = implode('/', $parts);
                break;
            } else {
                $this->method = array_pop($parts);
            }
        }
    }
}
```

### مثال عملي للـ Routing:
- URL: `index.php?route=catalog/product/edit&product_id=1`
- يتم تحويله إلى: `dashboard/controller/catalog/product.php` → `edit()` method

## 3. نظام Registry Pattern

### الهدف:
Registry يعمل كـ **Service Container** يحتوي على جميع الكائنات المشتركة.

#### في `system/engine/registry.php`:
```php
class Registry {
    private $data = array();
    
    public function get($key) {
        return (isset($this->data[$key]) ? $this->data[$key] : null);
    }
    
    public function set($key, $value) {
        $this->data[$key] = $value;
    }
}
```

### الكائنات المتاحة في Registry:
```php
// في أي Controller أو Model
$this->db          // قاعدة البيانات
$this->config      // الإعدادات
$this->session     // الجلسة
$this->request     // طلب HTTP
$this->response    // استجابة HTTP
$this->load        // محمل الملفات
$this->user        // المستخدم الحالي
$this->language    // اللغة
$this->url         // مولد الروابط
$this->cache       // التخزين المؤقت
$this->event       // نظام الأحداث
```

## 4. نظام Loader المتقدم

### في `system/engine/loader.php`:

#### تحميل Models:
```php
public function model($route) {
    if (!$this->registry->has('model_' . str_replace('/', '_', $route))) {
        $file = DIR_APPLICATION . 'model/' . $route . '.php';
        $class = 'Model' . preg_replace('/[^a-zA-Z0-9]/', '', $route);
        
        // استخدام Proxy Pattern للـ Models
        $proxy = new Proxy();
        foreach (get_class_methods($class) as $method) {
            $proxy->{$method} = $this->callback($this->registry, $route . '/' . $method);
        }
        
        $this->registry->set('model_' . str_replace('/', '_', $route), $proxy);
    }
}
```

#### تحميل Views:
```php
public function view($route, $data = array()) {
    // تشغيل Events قبل العرض
    $result = $this->registry->get('event')->trigger('view/' . $route . '/before', array(&$route, &$data));
    
    // استخدام Twig Template Engine
    $template = new Template($this->registry->get('config')->get('template_engine'));
    foreach ($data as $key => $value) {
        $template->set($key, $value);
    }
    
    $output = $template->render($this->registry->get('config')->get('template_directory') . $route);
    
    // تشغيل Events بعد العرض
    $result = $this->registry->get('event')->trigger('view/' . $route . '/after', array(&$route, &$data, &$output));
    
    return $output;
}
```

## 5. نظام Events المتقدم

### في `system/engine/event.php`:

```php
class Event {
    protected $data = array();
    
    public function register($trigger, Action $action, $priority = 0) {
        $this->data[] = array(
            'trigger'  => $trigger,
            'action'   => $action,
            'priority' => $priority
        );
        
        // ترتيب حسب الأولوية
        array_multisort($sort_order, SORT_ASC, $this->data);
    }
    
    public function trigger($event, array $args = array()) {
        foreach ($this->data as $value) {
            if (preg_match('/^' . str_replace(array('\*', '\?'), array('.*', '.'), preg_quote($value['trigger'], '/')) . '/', $event)) {
                $result = $value['action']->execute($this->registry, $args);
                if (!is_null($result) && !($result instanceof Exception)) {
                    return $result;
                }
            }
        }
    }
}
```

### أمثلة Events المتاحة:
- `controller/*/before` - قبل تنفيذ أي Controller
- `controller/*/after` - بعد تنفيذ أي Controller
- `model/*/before` - قبل تنفيذ أي Model method
- `model/*/after` - بعد تنفيذ أي Model method
- `view/*/before` - قبل عرض أي View
- `view/*/after` - بعد عرض أي View

## 6. التفاعل بـ AJAX

### النمط المعياري للـ AJAX في AYM ERP:

#### في Controller:
```php
public function updateStatus() {
    $this->load->language('module/name');
    $this->load->model('module/name');
    
    $json = array();
    
    // 1. التحقق من الصلاحيات
    if (!$this->user->hasPermission('modify', 'module/name')) {
        $json['error'] = $this->language->get('error_permission');
    }
    
    // 2. التحقق من البيانات
    if (!isset($this->request->post['required_field'])) {
        $json['error'] = $this->language->get('error_required_field');
    }
    
    // 3. تنفيذ العملية
    if (!isset($json['error'])) {
        try {
            // استخدام الخدمات المركزية
            $this->load->model('core/central_service_manager');
            
            // تنفيذ العملية
            $result = $this->model_module_name->updateStatus($this->request->post);
            
            // تسجيل النشاط
            $this->model_core_central_service_manager->logActivity(
                'update', 
                'module_name', 
                'تم تحديث الحالة',
                ['id' => $result]
            );
            
            // إرسال إشعار
            $this->model_core_central_service_manager->sendNotification(
                'info',
                'تم التحديث',
                'تم تحديث الحالة بنجاح',
                [$this->user->getId()]
            );
            
            $json['success'] = $this->language->get('text_success');
            $json['data'] = $result;
            
        } catch (Exception $e) {
            $json['error'] = $e->getMessage();
        }
    }
    
    // 4. إرجاع JSON Response
    $this->response->addHeader('Content-Type: application/json');
    $this->response->setOutput(json_encode($json));
}
```

### في JavaScript (Frontend):
```javascript
// النمط المعياري للـ AJAX calls
function updateStatus(id, status) {
    $.ajax({
        url: 'index.php?route=module/name/updateStatus&user_token=' + getToken(),
        type: 'POST',
        data: {
            id: id,
            status: status
        },
        dataType: 'json',
        beforeSend: function() {
            // إظهار loading
            $('#loading').show();
        },
        success: function(json) {
            $('#loading').hide();
            
            if (json['error']) {
                // إظهار رسالة خطأ
                toastr.error(json['error']);
            }
            
            if (json['success']) {
                // إظهار رسالة نجاح
                toastr.success(json['success']);
                
                // تحديث الواجهة
                if (json['data']) {
                    updateUI(json['data']);
                }
                
                // إعادة تحميل الجدول
                $('#table').DataTable().ajax.reload();
            }
        },
        error: function(xhr, ajaxOptions, thrownError) {
            $('#loading').hide();
            toastr.error('حدث خطأ في الاتصال: ' + thrownError);
        }
    });
}
```

## 7. نظام Template Engine (Twig)

### الميزات المستخدمة:

#### المتغيرات:
```twig
{{ title }}                    <!-- عرض متغير -->
{{ user.name }}               <!-- عرض خاصية كائن -->
{{ products|length }}         <!-- استخدام فلتر -->
```

#### الشروط:
```twig
{% if logged %}
    <p>مرحباً {{ username }}</p>
{% else %}
    <p>يرجى تسجيل الدخول</p>
{% endif %}
```

#### الحلقات:
```twig
{% for product in products %}
    <div class="product">
        <h3>{{ product.name }}</h3>
        <p>{{ product.price }}</p>
    </div>
{% endfor %}
```

#### الوراثة:
```twig
{% extends "common/header.twig" %}

{% block content %}
    <h1>{{ heading_title }}</h1>
    <!-- محتوى الصفحة -->
{% endblock %}
```

#### التضمين:
```twig
{% include 'common/pagination.twig' %}
{{ include('module/item_row.twig', {'item': product}) }}
```

## 8. أفضل الممارسات في AYM ERP

### في Controllers:
1. **دائماً تحقق من الصلاحيات أولاً**
2. **استخدم الخدمات المركزية للعمليات المشتركة**
3. **أرجع JSON responses موحدة**
4. **سجل جميع الأنشطة المهمة**
5. **استخدم try-catch لمعالجة الأخطاء**

### في Models:
1. **استخدم prepared statements دائماً**
2. **تحقق من صحة البيانات**
3. **استخدم transactions للعمليات المعقدة**
4. **أرجع نتائج متسقة**

### في Views:
1. **استخدم Twig بدلاً من PHP المباشر**
2. **فصل المنطق عن العرض**
3. **استخدم التخزين المؤقت للقوالب**
4. **تأكد من الاستجابة (Responsive)**

## 9. نظام الأمان المدمج

### في كل Controller:
```php
// التحقق من token للحماية من CSRF
if (!isset($this->request->post['user_token']) || !isset($this->session->data['user_token']) || ($this->request->post['user_token'] != $this->session->data['user_token'])) {
    $json['error'] = 'Invalid token';
}

// التحقق من الصلاحيات
if (!$this->user->hasPermission('modify', 'module/name')) {
    $json['error'] = $this->language->get('error_permission');
}

// تنظيف البيانات
$data = array_map('trim', $this->request->post);
$data = array_map('htmlspecialchars', $data);
```

## 10. التكامل مع الخدمات المركزية

### النمط المطلوب في كل Controller:
```php
public function index() {
    // 1. تحميل الخدمة المركزية
    $this->load->model('core/central_service_manager');
    
    // 2. تسجيل النشاط
    $this->model_core_central_service_manager->logActivity(
        'view', 
        'module_name', 
        'تم عرض الشاشة الرئيسية'
    );
    
    // 3. جلب الإشعارات للهيدر
    $data['notifications'] = $this->model_core_central_service_manager->getUserNotifications(
        $this->user->getId(), 
        0, 
        10
    );
    
    // 4. عرض الصفحة
    $this->response->setOutput($this->load->view('module/template', $data));
}
```

## الخلاصة

معمارية OpenCart 3.x في AYM ERP تتميز بـ:

1. **MVC Pattern** محكم مع فصل واضح للمسؤوليات
2. **Registry Pattern** للوصول المركزي للخدمات
3. **Event-Driven Architecture** للمرونة والتوسع
4. **AJAX-First Approach** للتفاعل السلس
5. **Twig Template Engine** للقوالب الآمنة
6. **Security-First Design** مع حماية شاملة
7. **Central Services Integration** للوظائف المشتركة

هذه المعمارية تضمن بناء نظام ERP قوي وقابل للصيانة والتطوير، يتفوق على المنافسين الأقوياء.