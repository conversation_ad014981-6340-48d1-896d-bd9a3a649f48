<?php
// Heading
$_['heading_title']                    = 'Financial Analytics Dashboard';

// Text
$_['text_success']                     = 'Success: You have modified financial analytics dashboard!';
$_['text_list']                        = 'Financial Analytics Dashboard List';
$_['text_add']                         = 'Add Dashboard';
$_['text_edit']                        = 'Edit Dashboard';
$_['text_view']                        = 'View Dashboard';
$_['text_default']                     = 'Default';
$_['text_enabled']                     = 'Enabled';
$_['text_disabled']                    = 'Disabled';
$_['text_yes']                         = 'Yes';
$_['text_no']                          = 'No';
$_['text_none']                        = 'None';
$_['text_select']                      = 'Select';
$_['text_loading']                     = 'Loading...';
$_['text_no_results']                  = 'No results!';
$_['text_confirm']                     = 'Are you sure?';

// Dashboard specific
$_['text_dashboard_info']              = 'Dashboard Information';
$_['text_dashboard_name']              = 'Dashboard Name';
$_['text_dashboard_type']              = 'Dashboard Type';
$_['text_dashboard_layout']            = 'Dashboard Layout';
$_['text_refresh_interval']            = 'Refresh Interval';
$_['text_auto_refresh']                = 'Auto Refresh';
$_['text_last_updated']                = 'Last Updated';
$_['text_data_source']                 = 'Data Source';

// Dashboard Types
$_['text_type_executive']              = 'Executive';
$_['text_type_operational']            = 'Operational';
$_['text_type_financial']              = 'Financial';
$_['text_type_analytical']             = 'Analytical';
$_['text_type_custom']                 = 'Custom';

// Widget Types
$_['text_widget_kpi']                  = 'KPI Widget';
$_['text_widget_chart']                = 'Chart Widget';
$_['text_widget_table']                = 'Table Widget';
$_['text_widget_gauge']                = 'Gauge Widget';
$_['text_widget_trend']                = 'Trend Widget';
$_['text_widget_comparison']           = 'Comparison Widget';

// KPI Metrics
$_['text_revenue']                     = 'Revenue';
$_['text_profit_margin']               = 'Profit Margin';
$_['text_gross_profit']                = 'Gross Profit';
$_['text_net_profit']                  = 'Net Profit';
$_['text_operating_income']            = 'Operating Income';
$_['text_ebitda']                      = 'EBITDA';
$_['text_cash_flow']                   = 'Cash Flow';
$_['text_working_capital']             = 'Working Capital';
$_['text_current_ratio']               = 'Current Ratio';
$_['text_quick_ratio']                 = 'Quick Ratio';
$_['text_debt_ratio']                  = 'Debt Ratio';
$_['text_roe']                         = 'Return on Equity';
$_['text_roa']                         = 'Return on Assets';
$_['text_roi']                         = 'Return on Investment';

// Chart Types
$_['text_chart_line']                  = 'Line';
$_['text_chart_bar']                   = 'Bar';
$_['text_chart_pie']                   = 'Pie';
$_['text_chart_area']                  = 'Area';
$_['text_chart_scatter']               = 'Scatter';
$_['text_chart_combo']                 = 'Combo';

// Time Periods
$_['text_period_daily']                = 'Daily';
$_['text_period_weekly']               = 'Weekly';
$_['text_period_monthly']              = 'Monthly';
$_['text_period_quarterly']            = 'Quarterly';
$_['text_period_yearly']               = 'Yearly';
$_['text_period_custom']               = 'Custom';

// Filters
$_['text_filter_date_range']           = 'Date Range';
$_['text_filter_department']           = 'Department';
$_['text_filter_branch']               = 'Branch';
$_['text_filter_currency']             = 'Currency';
$_['text_filter_account_type']         = 'Account Type';

// Widget Configuration
$_['text_widget_config']               = 'Widget Configuration';
$_['text_widget_title']                = 'Widget Title';
$_['text_widget_size']                 = 'Widget Size';
$_['text_widget_position']             = 'Widget Position';
$_['text_widget_color']                = 'Widget Color';
$_['text_widget_data_source']          = 'Widget Data Source';

// Real-time Features
$_['text_real_time']                   = 'Real-time';
$_['text_live_data']                   = 'Live Data';
$_['text_auto_update']                 = 'Auto Update';
$_['text_manual_refresh']              = 'Manual Refresh';
$_['text_data_freshness']              = 'Data Freshness';

// Alerts and Notifications
$_['text_alerts']                      = 'Alerts';
$_['text_threshold_alerts']            = 'Threshold Alerts';
$_['text_trend_alerts']                = 'Trend Alerts';
$_['text_anomaly_alerts']              = 'Anomaly Alerts';
$_['text_alert_conditions']            = 'Alert Conditions';

// Export Options
$_['text_export_pdf']                  = 'Export PDF';
$_['text_export_excel']                = 'Export Excel';
$_['text_export_image']                = 'Export Image';
$_['text_export_data']                 = 'Export Data';

// Buttons
$_['button_add']                       = 'Add';
$_['button_edit']                      = 'Edit';
$_['button_delete']                    = 'Delete';
$_['button_save']                      = 'Save';
$_['button_cancel']                    = 'Cancel';
$_['button_close']                     = 'Close';
$_['button_back']                      = 'Back';
$_['button_view']                      = 'View';
$_['button_print']                     = 'Print';
$_['button_export']                    = 'Export';
$_['button_refresh']                   = 'Refresh';
$_['button_configure']                 = 'Configure';
$_['button_customize']                 = 'Customize';
$_['button_add_widget']                = 'Add Widget';
$_['button_fullscreen']                = 'Fullscreen';
$_['button_share']                     = 'Share';

// Columns
$_['column_dashboard_name']            = 'Dashboard Name';
$_['column_dashboard_type']            = 'Type';
$_['column_widgets_count']             = 'Widgets Count';
$_['column_last_updated']              = 'Last Updated';
$_['column_auto_refresh']              = 'Auto Refresh';
$_['column_status']                    = 'Status';
$_['column_created_by']                = 'Created By';
$_['column_date_created']              = 'Date Created';
$_['column_action']                    = 'Action';

// Widget columns
$_['column_widget_title']              = 'Widget Title';
$_['column_widget_type']               = 'Type';
$_['column_widget_size']               = 'Size';
$_['column_widget_position']           = 'Position';
$_['column_widget_data_source']        = 'Data Source';

// Entry fields
$_['entry_dashboard_name']             = 'Dashboard Name';
$_['entry_dashboard_type']             = 'Dashboard Type';
$_['entry_dashboard_layout']           = 'Dashboard Layout';
$_['entry_refresh_interval']           = 'Refresh Interval (seconds)';
$_['entry_description']                = 'Description';
$_['entry_notes']                      = 'Notes';
$_['entry_widget_title']               = 'Widget Title';
$_['entry_widget_type']                = 'Widget Type';
$_['entry_chart_type']                 = 'Chart Type';
$_['entry_data_source']                = 'Data Source';

// Help text
$_['help_dashboard_name']              = 'Enter a descriptive name for the analytics dashboard';
$_['help_refresh_interval']            = 'Auto refresh interval in seconds (0 = no auto refresh)';
$_['help_widget_configuration']        = 'Configure the widget according to your needs';
$_['help_real_time_data']              = 'Real-time data requires continuous connection';

// Error messages
$_['error_permission']                 = 'Warning: You do not have permission to access financial analytics dashboard!';
$_['error_dashboard_name']             = 'Dashboard name must be between 3 and 64 characters!';
$_['error_dashboard_type']             = 'Please select dashboard type!';
$_['error_refresh_interval']           = 'Refresh interval must be a valid number!';
$_['error_widget_title']               = 'Widget title must be between 3 and 64 characters!';
$_['error_widget_type']                = 'Please select widget type!';
$_['error_data_source']                = 'Please select data source!';
$_['error_no_data']                    = 'No data available for display!';
$_['error_data_loading']               = 'Error loading data!';
$_['error_chart_rendering']            = 'Error rendering chart!';

// Success messages
$_['success_dashboard_added']          = 'Dashboard added successfully!';
$_['success_dashboard_updated']        = 'Dashboard updated successfully!';
$_['success_dashboard_deleted']        = 'Dashboard deleted successfully!';
$_['success_widget_added']             = 'Widget added successfully!';
$_['success_widget_updated']           = 'Widget updated successfully!';
$_['success_widget_deleted']           = 'Widget deleted successfully!';
$_['success_data_refreshed']           = 'Data refreshed successfully!';
$_['success_dashboard_exported']       = 'Dashboard exported successfully!';

// Confirmation messages
$_['confirm_delete_dashboard']         = 'Are you sure you want to delete this dashboard?';
$_['confirm_delete_widget']            = 'Are you sure you want to delete this widget?';
$_['confirm_refresh_data']             = 'Are you sure you want to refresh the data?';

// Tabs
$_['tab_general']                      = 'General';
$_['tab_widgets']                      = 'Widgets';
$_['tab_layout']                       = 'Layout';
$_['tab_filters']                      = 'Filters';
$_['tab_alerts']                       = 'Alerts';
$_['tab_permissions']                  = 'Permissions';

// Status
$_['text_status_active']               = 'Active';
$_['text_status_inactive']             = 'Inactive';
$_['text_status_draft']                = 'Draft';
$_['text_status_published']            = 'Published';

// Performance Indicators
$_['text_performance_good']            = 'Good Performance';
$_['text_performance_average']         = 'Average Performance';
$_['text_performance_poor']            = 'Poor Performance';
$_['text_trend_up']                    = 'Upward Trend';
$_['text_trend_down']                  = 'Downward Trend';
$_['text_trend_stable']                = 'Stable Trend';

// Data Visualization
$_['text_visualization']               = 'Visualization';
$_['text_interactive_charts']          = 'Interactive Charts';
$_['text_drill_down']                  = 'Drill Down';
$_['text_zoom_in']                     = 'Zoom In';
$_['text_zoom_out']                    = 'Zoom Out';
$_['text_pan']                         = 'Pan';

// Comparison Features
$_['text_compare_periods']             = 'Compare Periods';
$_['text_year_over_year']              = 'Year over Year';
$_['text_month_over_month']            = 'Month over Month';
$_['text_quarter_over_quarter']        = 'Quarter over Quarter';
$_['text_benchmark']                   = 'Benchmark';

// Advanced Analytics
$_['text_predictive_analytics']        = 'Predictive Analytics';
$_['text_statistical_analysis']        = 'Statistical Analysis';
$_['text_correlation_analysis']        = 'Correlation Analysis';
$_['text_regression_analysis']         = 'Regression Analysis';
$_['text_variance_analysis']           = 'Variance Analysis';

// Mobile Features
$_['text_mobile_responsive']           = 'Mobile Responsive';
$_['text_mobile_app']                  = 'Mobile App';
$_['text_offline_access']              = 'Offline Access';

// Collaboration
$_['text_share_dashboard']             = 'Share Dashboard';
$_['text_collaborate']                 = 'Collaborate';
$_['text_comments']                    = 'Comments';
$_['text_annotations']                 = 'Annotations';

// Security
$_['text_access_control']              = 'Access Control';
$_['text_user_permissions']            = 'User Permissions';
$_['text_data_security']               = 'Data Security';
$_['text_audit_trail']                 = 'Audit Trail';
?>
