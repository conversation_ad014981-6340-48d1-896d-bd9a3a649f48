# Complete Inventory & E-commerce Specification - AYM ERP
## المواصفات الشاملة للمخزون والتجارة الإلكترونية - نظام AYM ERP

---

## 🎯 **الفهم الصحيح للهيكل الحقيقي**

### 📊 **مراجعة tree.txt الفعلية - الأرقام الحقيقية:**

#### **1️⃣ وحدة المخزون (dashboard/controller/inventory/) - 32 ملف:**
```
abc_analysis.php, adjustment.php, barcode.php, barcode_management.php, barcode_print.php,
batch_tracking.php, category.php, current_stock.php, dashboard.php, goods_receipt.php,
interactive_dashboard.php, inventory.php, inventory_management_advanced.php, inventory_valuation.php,
location_management.php, manufacturer.php, movement_history.php, product.php, product_management.php,
purchase_order.php, stock_adjustment.php, stock_count.php, stock_counting.php, stock_level.php,
stock_levels.php, stock_movement.php, stock_transfer.php, stocktake.php, transfer.php,
unit_management.php, units.php, warehouse.php
```

#### **2️⃣ وحدة الكتالوج (dashboard/controller/catalog/) - 16 ملف:**
```
attribute.php, attribute_group.php, blog.php, blog_category.php, blog_comment.php, blog_tag.php,
category.php, dynamic_pricing.php, filter.php, information.php, manufacturer.php, option.php,
product.php, review.php, seo.php, unit.php
```

#### **3️⃣ نظام نقطة البيع (dashboard/controller/pos/) - 6 ملفات:**
```
cashier_handover.php, pos.php, reports.php, settings.php, shift.php, terminal.php
```

#### **4️⃣ ملفات View الحقيقية:**
- **inventory/ views:** 80+ ملف twig
- **catalog/ views:** 40+ ملف twig  
- **pos/ views:** 15+ ملف twig

---

## 🔍 **توضيح الالتباس - الفروقات الحقيقية:**

### **المنتجات - 3 واجهات مختلفة:**

#### **1. إدارة المنتجات للمخزون:**
- **المسار:** `dashboard/controller/inventory/product.php`
- **View:** `dashboard/view/template/inventory/product_form.twig`
- **الهدف:** إدارة المخزون الفعلي والحركات
- **التركيز:** الكميات، التكاليف، المواقع، الدفعات

#### **2. إدارة المنتجات للكتالوج:**
- **المسار:** `dashboard/controller/catalog/product.php`
- **View:** `dashboard/view/template/catalog/product_form.twig` (الأعقد - 12 تبويب)
- **الهدف:** إدارة محتوى المتجر الإلكتروني
- **التركيز:** الأوصاف، الصور، SEO، التسويق

#### **3. عرض المنتج للعملاء:**
- **المسار:** `catalog/controller/product/product.php`
- **View:** `catalog/view/template/product/product.twig`
- **الهدف:** واجهة التسوق للعملاء
- **التركيز:** العرض، السلة، المراجعات

### **نقطة البيع - واجهة واحدة متكاملة:**
- **المسار:** `dashboard/controller/pos/pos.php`
- **View:** `dashboard/view/template/pos/pos.twig` (1925 سطر تفاعلي)
- **الهدف:** البيع المباشر في الفروع
- **الميزة:** 4 مستويات أسعار + واجهة تفاعلية كاملة

---

## 💰 **نظام التسعير المعقد - الفهم الصحيح:**

### **في نقطة البيع (POS):**
```
4 مستويات أسعار متاحة:
├── السعر الأساسي (Basic Price)
├── سعر العرض (Special Price)  
├── سعر الجملة (Wholesale Price)
├── سعر نصف الجملة (Semi-wholesale Price)
└── السعر الخاص (Custom Price) - حسب العميل
```

### **في المتجر الإلكتروني:**
```
مستويين أساسيين:
├── السعر الأساسي (Basic Price)
└── سعر العرض (Special Price)

مع تأثيرات إضافية:
├── خصومات الكمية (Quantity Discounts)
├── أسعار الباقات (Bundle Pricing)
├── أسعار الخيارات (Option Pricing)
└── كوبونات الخصم (Coupon Discounts)
```

---

## 📊 **الإحصاء الدقيق المُصحح:**

### **Controllers (54 ملف):**
- **المخزون:** 32 ملف
- **الكتالوج:** 16 ملف  
- **نقطة البيع:** 6 ملفات

### **Views (135+ ملف twig):**
- **المخزون:** 80+ ملف
- **الكتالوج:** 40+ ملف
- **نقطة البيع:** 15+ ملف

### **Models (54 ملف متوقع):**
- **المخزون:** 32 ملف
- **الكتالوج:** 16 ملف
- **نقطة البيع:** 6 ملفات

### **Language Files (108 ملف متوقع):**
- **العربية:** 54 ملف
- **الإنجليزية:** 54 ملف

---

## 🎯 **خطة التنفيذ الواقعية المُصححة:**

### **المرحلة الأولى: الأساسيات الحرجة (4 أسابيع)**

#### **الأسبوع 1: أساسيات المخزون (8 شاشات)**
1. ✅ **warehouse.php** - مكتمل
2. ✅ **stock_movement.php** - مكتمل  
3. ✅ **stock_adjustment.php** - مكتمل
4. **stock_transfer.php** - تحويلات المخزون
5. **current_stock.php** - المخزون الحالي
6. **inventory.php** - إدارة المخزون العامة
7. **barcode.php** - إدارة الباركود الأساسية
8. **units.php** - وحدات القياس

#### **الأسبوع 2: إدارة المنتجات (4 شاشات معقدة)**
9. **inventory/product.php** - إدارة المنتجات للمخزون (3 أيام)
10. **catalog/product.php** - إدارة المنتجات للكتالوج (4 أيام) ⭐ الأعقد

#### **الأسبوع 3: نقطة البيع (6 شاشات)**
11. **pos.php** - الشاشة الرئيسية (3 أيام) ⭐
12. **cashier_handover.php** - تسليم الكاش (1 يوم)
13. **shift.php** - إدارة الورديات (1 يوم)
14. **terminal.php** - إدارة الطرفيات (1 يوم)
15. **pos/reports.php** - تقارير نقطة البيع (1 يوم)
16. **pos/settings.php** - إعدادات نقطة البيع (1 يوم)

#### **الأسبوع 4: الكتالوج الأساسي (8 شاشات)**
17. **catalog/category.php** - فئات المنتجات
18. **catalog/manufacturer.php** - العلامات التجارية
19. **catalog/attribute.php** - خصائص المنتجات
20. **catalog/option.php** - خيارات المنتجات
21. **catalog/review.php** - مراجعات المنتجات
22. **catalog/filter.php** - فلاتر البحث
23. **catalog/unit.php** - وحدات الكتالوج
24. **catalog/information.php** - صفحات المعلومات

### **المرحلة الثانية: الميزات المتقدمة (6 أسابيع)**

#### **الأسبوع 5-6: المخزون المتقدم (12 شاشة)**
25. **abc_analysis.php** - تحليل ABC
26. **batch_tracking.php** - تتبع الدفعات
27. **inventory_management_advanced.php** - الإدارة المتقدمة
28. **inventory_valuation.php** - تقييم المخزون
29. **location_management.php** - إدارة المواقع
30. **barcode_management.php** - إدارة الباركود المتقدمة
31. **unit_management.php** - إدارة الوحدات المتقدمة
32. **stock_count.php** - عد المخزون
33. **stock_counting.php** - الجرد
34. **stocktake.php** - الجرد المتقدم
35. **movement_history.php** - تاريخ الحركات
36. **interactive_dashboard.php** - لوحة التحكم التفاعلية

#### **الأسبوع 7-8: الكتالوج المتقدم (8 شاشات)**
37. **dynamic_pricing.php** - التسعير الديناميكي ⭐
38. **seo.php** - تحسين محركات البحث
39. **blog.php** - إدارة المدونة
40. **blog_category.php** - فئات المدونة
41. **blog_comment.php** - تعليقات المدونة
42. **blog_tag.php** - علامات المدونة
43. **attribute_group.php** - مجموعات الخصائص
44. **catalog/category.php** - فئات متقدمة

#### **الأسبوع 9-10: المخزون المتخصص (12 شاشة)**
45. **goods_receipt.php** - استلام البضائع
46. **purchase_order.php** - أوامر الشراء
47. **stock_level.php** - مستويات المخزون
48. **stock_levels.php** - مستويات متعددة
49. **transfer.php** - التحويلات
50. **adjustment.php** - التسويات العامة
51. **barcode_print.php** - طباعة الباركود
52. **product_management.php** - إدارة المنتجات المتقدمة
53. **inventory/category.php** - فئات المخزون
54. **inventory/manufacturer.php** - مصنعي المخزون
55. **inventory/dashboard.php** - لوحة تحكم المخزون
56. **current_stock.php** - المخزون الحالي المتقدم

---

## 🗄️ **قاعدة البيانات المحسنة:**

### **الجداول الأساسية الموجودة:**
- cod_product, cod_product_inventory, cod_product_movement
- cod_warehouse, cod_stock_adjustment, cod_stock_transfer
- cod_category, cod_manufacturer, cod_attribute

### **الجداول الناقصة المطلوبة (12 جدول):**
1. **cod_warehouse_location** - مواقع المستودعات المتقدمة
2. **cod_batch_tracking** - تتبع الدفعات والصلاحية
3. **cod_inventory_reservation** - حجز المخزون
4. **cod_inventory_count** - الجرد المتقدم
5. **cod_inventory_count_item** - بنود الجرد
6. **cod_inventory_alert** - تنبيهات المخزون الذكية
7. **cod_inventory_abc_analysis** - تحليل ABC
8. **cod_inventory_turnover_analysis** - تحليل معدل الدوران
9. **cod_inventory_valuation** - التقييم المتقدم
10. **cod_inventory_accounting_reconciliation** - المطابقة المحاسبية
11. **cod_inventory_accounting_reconciliation_item** - بنود المطابقة
12. **cod_pos_session** - جلسات نقطة البيع

---

## 🎯 **الهدف النهائي:**

### **نظام متكامل يتفوق على:**
- **SAP MM** في سهولة الاستخدام
- **Oracle WMS** في التكامل والأداء  
- **Shopify Plus** في التكامل مع ERP
- **Square POS** في الميزات المتقدمة

### **الميزات التنافسية الفريدة:**
1. **تكامل كامل 100%** بين المخزون والتجارة الإلكترونية ونقطة البيع
2. **نظام تسعير معقد** - 4 مستويات في POS، ديناميكي في المتجر
3. **مزامنة فورية** للبيانات عبر جميع القنوات
4. **دعم عربي كامل** مع مصطلحات مصرية
5. **تكلفة اقتصادية** أقل بـ 90% من المنافسين

---

## 🔄 **التكامل بين الوحدات - الفهم الصحيح:**

### **المنتج الواحد = 3 واجهات إدارية + 1 واجهة عملاء:**

```
المنتج في قاعدة البيانات (cod_product)
├── 1. إدارة المخزون (inventory/product.php)
│   ├── الكميات والمواقع
│   ├── تتبع الحركات والدفعات
│   ├── حساب التكاليف (WAC)
│   └── تنبيهات الحد الأدنى
│
├── 2. إدارة الكتالوج (catalog/product.php) ⭐ الأعقد
│   ├── 12 تبويب معقد
│   ├── الأوصاف والصور
│   ├── التسعير والعروض
│   ├── SEO وتحسين المحتوى
│   └── إدارة المراجعات
│
├── 3. نقطة البيع (pos.php)
│   ├── بيع مباشر تفاعلي
│   ├── 4 مستويات أسعار
│   ├── خصومات فورية
│   └── طباعة فاتورة
│
└── 4. عرض للعملاء (catalog/product/product.php)
    ├── عرض للعملاء
    ├── إضافة للسلة
    ├── مراجعات العملاء
    └── معلومات الشحن
```

### **مزامنة البيانات الفورية:**

```
عند تحديث المنتج في أي وحدة:

المخزون → الكتالوج:
├── تحديث حالة التوفر
├── تحديث الكمية المتاحة
├── تنبيهات نفاد المخزون
└── تحديث التكلفة

الكتالوج → المتجر:
├── تحديث الأسعار
├── تحديث الأوصاف والصور
├── تفعيل/إلغاء تفعيل المنتج
└── تحديث العروض

POS → المخزون:
├── خصم الكميات المباعة
├── تحديث التكلفة (WAC)
├── إنشاء حركة مخزون
└── تحديث الأرصدة

المحاسبة ← جميع الوحدات:
├── إنشاء القيود التلقائية
├── تحديث حسابات المخزون
├── تسجيل الإيرادات والتكاليف
└── تحديث الميزانية
```

---

## 📋 **تحليل الشاشات المعقدة:**

### **⭐ catalog/product.php - الأعقد في النظام:**

#### **التبويبات الـ12 المكتشفة:**
```
1. tab-general: المعلومات العامة (متعدد اللغات)
2. tab-data: البيانات الأساسية والتصنيف
3. tab-image: إدارة الصور المتقدمة
4. tab-units: الوحدات المتعددة والتحويل ⭐
5. tab-inventory: المخزون المعقد (فعلي + وهمي) ⭐
6. tab-pricing: التسعير المتقدم (عملاء + كميات) ⭐
7. tab-barcode: إدارة الباركود والطباعة
8. tab-option: الخيارات والمتغيرات
9. tab-bundle: الباقات الديناميكية ⭐
10. tab-recommendation: التوصيات الذكية (AI)
11. tab-movement: تتبع حركات المخزون
12. tab-orders: الطلبات والمبيعات المرتبطة
```

#### **الملفات المرتبطة:**
```
Controllers: catalog/product.php (4000+ سطر متوقع)
Models: catalog/product.php (3000+ سطر متوقع)
Views:
├── product_form.twig (2667 سطر - الرئيسي)
├── product_tab_general.twig
├── product_tab_data.twig
├── product_tab_image.twig
├── product_tab_units.twig
├── product_tab_inventory.twig
├── product_tab_pricing.twig
├── product_tab_barcode.twig
├── product_tab_option.twig
├── product_tab_bundle.twig
├── product_tab_recommend.twig
├── product_tab_movement.twig
└── product_tab_orders.twig
```

### **⭐ pos.php - نقطة البيع المتطورة:**

#### **الميزات المكتشفة:**
```
1. واجهة تفاعلية كاملة (AJAX متقدم)
2. بحث ذكي فوري (للمنتجات والعملاء)
3. نظام خصومات معقد (متعدد المستويات)
4. إدارة مدفوعات متقدمة (طرق متعددة)
5. طباعة فواتير ذكية (قوالب متعددة)
6. تقارير فورية (مبيعات وأرباح لحظية)
7. إدارة ورديات (تسليم واستلام متقدم)
8. مزامنة فورية (مع المخزون الرئيسي)
```

#### **الملفات المرتبطة:**
```
Controllers: pos/pos.php (2000+ سطر متوقع)
Models: pos/pos.php (1500+ سطر متوقع)
Views:
├── pos.twig (1925 سطر - الرئيسي)
├── receipt.twig
├── cashier_handover_form.twig
├── shift_form.twig
├── terminal_form.twig
└── settings.twig
```

---

## 🗄️ **قاعدة البيانات المحسنة - البديل الشامل:**

### **بديل للملفات الثلاثة (db.txt, minidb.txt, dbindex.txt):**

#### **الجداول الأساسية المحسنة:**
```sql
-- المنتجات والكتالوج
cod_product (محسن مع حقول إضافية)
cod_product_description (متعدد اللغات)
cod_product_image (إدارة متقدمة)
cod_product_option (خيارات معقدة)
cod_product_bundle (باقات ديناميكية)
cod_product_unit (وحدات متعددة)
cod_product_price (تسعير متقدم)

-- المخزون المتقدم
cod_product_inventory (محسن مع WAC)
cod_product_movement (تتبع شامل)
cod_warehouse (هيكل شجري)
cod_warehouse_location (مواقع ثلاثية الأبعاد)
cod_batch_tracking (دفعات وصلاحية)
cod_inventory_reservation (حجز ذكي)

-- الجرد والتحليلات
cod_inventory_count (جرد متقدم)
cod_inventory_count_item (بنود الجرد)
cod_inventory_alert (تنبيهات ذكية)
cod_inventory_abc_analysis (تحليل ABC)
cod_inventory_turnover_analysis (معدل الدوران)
cod_inventory_valuation (تقييم متقدم)

-- نقطة البيع
cod_pos_session (جلسات POS)
cod_pos_transaction (معاملات مفصلة)
cod_pos_payment (مدفوعات متعددة)
cod_cashier_handover (تسليم الكاش)
cod_terminal (إدارة الطرفيات)

-- التجارة الإلكترونية
cod_cart (سلة متقدمة)
cod_order (طلبات شاملة)
cod_customer (عملاء متقدم)
cod_abandoned_cart (سلات مهجورة)
cod_coupon (كوبونات ذكية)
```

#### **الفهارس المحسنة:**
```sql
-- فهارس الأداء
CREATE INDEX idx_product_inventory_performance ON cod_product_inventory
(warehouse_id, product_id, quantity, reserved_quantity);

CREATE INDEX idx_movement_analysis ON cod_product_movement
(movement_date, product_id, movement_type, warehouse_id);

CREATE INDEX idx_pos_performance ON cod_pos_transaction
(session_id, product_id, transaction_date);

-- فهارس البحث
CREATE FULLTEXT INDEX idx_product_search ON cod_product_description
(name, description, meta_title, meta_description);

-- فهارس التقارير
CREATE INDEX idx_inventory_reports ON cod_product_inventory
(warehouse_id, quantity, average_cost, updated_at);
```

---

## 📊 **التقدير الزمني الواقعي المُصحح:**

### **التوزيع الزمني بناءً على التعقيد:**

#### **الشاشات الحرجة (تحتاج وقت إضافي):**
```
1. catalog/product.php: 5 أيام (الأعقد - 12 تبويب)
2. pos.php: 3 أيام (تفاعلية معقدة)
3. inventory/product.php: 2 أيام (مخزون متقدم)
4. dynamic_pricing.php: 2 أيام (تسعير معقد)
5. abc_analysis.php: 2 أيام (تحليلات متقدمة)
6. batch_tracking.php: 2 أيام (تتبع دفعات)
7. inventory_management_advanced.php: 2 أيام
```

#### **الشاشات المتوسطة (يوم واحد لكل شاشة):**
```
8-32. باقي شاشات المخزون (25 شاشة × 1 يوم = 25 يوم)
33-48. باقي شاشات الكتالوج (16 شاشة × 1 يوم = 16 يوم)
49-54. باقي شاشات POS (6 شاشات × 1 يوم = 6 أيام)
```

#### **إجمالي الوقت المطلوب:**
```
الشاشات الحرجة: 18 يوم
الشاشات المتوسطة: 47 يوم
إجمالي التطوير: 65 يوم عمل
الاختبار والتحسين: 15 يوم
المجموع الكلي: 80 يوم عمل (16 أسبوع)
```

---

**📅 آخر تحديث:** 20/7/2025 - 07:45
**👨‍💻 المعد:** AI Agent - Enterprise Grade Development
**📋 الحالة:** Spec شاملة مكتملة بناءً على الهيكل الحقيقي
**🎯 المرحلة التالية:** إنشاء ملف SQL محسن وتحديث taskmemory.md
