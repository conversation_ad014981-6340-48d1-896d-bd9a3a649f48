<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/metric.proto

namespace Google\Api;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Defines a metric type and its schema. Once a metric descriptor is created,
 * deleting or altering it stops data collection and makes the metric type's
 * existing data unusable.
 *
 * Generated from protobuf message <code>google.api.MetricDescriptor</code>
 */
class MetricDescriptor extends \Google\Protobuf\Internal\Message
{
    /**
     * The resource name of the metric descriptor.
     *
     * Generated from protobuf field <code>string name = 1;</code>
     */
    private $name = '';
    /**
     * The metric type, including its DNS name prefix. The type is not
     * URL-encoded.  All user-defined metric types have the DNS name
     * `custom.googleapis.com` or `external.googleapis.com`.  Metric types should
     * use a natural hierarchical grouping. For example:
     *     "custom.googleapis.com/invoice/paid/amount"
     *     "external.googleapis.com/prometheus/up"
     *     "appengine.googleapis.com/http/server/response_latencies"
     *
     * Generated from protobuf field <code>string type = 8;</code>
     */
    private $type = '';
    /**
     * The set of labels that can be used to describe a specific
     * instance of this metric type. For example, the
     * `appengine.googleapis.com/http/server/response_latencies` metric
     * type has a label for the HTTP response code, `response_code`, so
     * you can look at latencies for successful responses or just
     * for responses that failed.
     *
     * Generated from protobuf field <code>repeated .google.api.LabelDescriptor labels = 2;</code>
     */
    private $labels;
    /**
     * Whether the metric records instantaneous values, changes to a value, etc.
     * Some combinations of `metric_kind` and `value_type` might not be supported.
     *
     * Generated from protobuf field <code>.google.api.MetricDescriptor.MetricKind metric_kind = 3;</code>
     */
    private $metric_kind = 0;
    /**
     * Whether the measurement is an integer, a floating-point number, etc.
     * Some combinations of `metric_kind` and `value_type` might not be supported.
     *
     * Generated from protobuf field <code>.google.api.MetricDescriptor.ValueType value_type = 4;</code>
     */
    private $value_type = 0;
    /**
     * The units in which the metric value is reported. It is only applicable
     * if the `value_type` is `INT64`, `DOUBLE`, or `DISTRIBUTION`. The `unit`
     * defines the representation of the stored metric values.
     * Different systems may scale the values to be more easily displayed (so a
     * value of `0.02KBy` _might_ be displayed as `20By`, and a value of
     * `3523KBy` _might_ be displayed as `3.5MBy`). However, if the `unit` is
     * `KBy`, then the value of the metric is always in thousands of bytes, no
     * matter how it may be displayed..
     * If you want a custom metric to record the exact number of CPU-seconds used
     * by a job, you can create an `INT64 CUMULATIVE` metric whose `unit` is
     * `s{CPU}` (or equivalently `1s{CPU}` or just `s`). If the job uses 12,005
     * CPU-seconds, then the value is written as `12005`.
     * Alternatively, if you want a custome metric to record data in a more
     * granular way, you can create a `DOUBLE CUMULATIVE` metric whose `unit` is
     * `ks{CPU}`, and then write the value `12.005` (which is `12005/1000`),
     * or use `Kis{CPU}` and write `11.723` (which is `12005/1024`).
     * The supported units are a subset of [The Unified Code for Units of
     * Measure](http://unitsofmeasure.org/ucum.html) standard:
     * **Basic units (UNIT)**
     * * `bit`   bit
     * * `By`    byte
     * * `s`     second
     * * `min`   minute
     * * `h`     hour
     * * `d`     day
     * **Prefixes (PREFIX)**
     * * `k`     kilo    (10^3)
     * * `M`     mega    (10^6)
     * * `G`     giga    (10^9)
     * * `T`     tera    (10^12)
     * * `P`     peta    (10^15)
     * * `E`     exa     (10^18)
     * * `Z`     zetta   (10^21)
     * * `Y`     yotta   (10^24)
     * * `m`     milli   (10^-3)
     * * `u`     micro   (10^-6)
     * * `n`     nano    (10^-9)
     * * `p`     pico    (10^-12)
     * * `f`     femto   (10^-15)
     * * `a`     atto    (10^-18)
     * * `z`     zepto   (10^-21)
     * * `y`     yocto   (10^-24)
     * * `Ki`    kibi    (2^10)
     * * `Mi`    mebi    (2^20)
     * * `Gi`    gibi    (2^30)
     * * `Ti`    tebi    (2^40)
     * * `Pi`    pebi    (2^50)
     * **Grammar**
     * The grammar also includes these connectors:
     * * `/`    division or ratio (as an infix operator). For examples,
     *          `kBy/{email}` or `MiBy/10ms` (although you should almost never
     *          have `/s` in a metric `unit`; rates should always be computed at
     *          query time from the underlying cumulative or delta value).
     * * `.`    multiplication or composition (as an infix operator). For
     *          examples, `GBy.d` or `k{watt}.h`.
     * The grammar for a unit is as follows:
     *     Expression = Component { "." Component } { "/" Component } ;
     *     Component = ( [ PREFIX ] UNIT | "%" ) [ Annotation ]
     *               | Annotation
     *               | "1"
     *               ;
     *     Annotation = "{" NAME "}" ;
     * Notes:
     * * `Annotation` is just a comment if it follows a `UNIT`. If the annotation
     *    is used alone, then the unit is equivalent to `1`. For examples,
     *    `{request}/s == 1/s`, `By{transmitted}/s == By/s`.
     * * `NAME` is a sequence of non-blank printable ASCII characters not
     *    containing `{` or `}`.
     * * `1` represents a unitary [dimensionless
     *    unit](https://en.wikipedia.org/wiki/Dimensionless_quantity) of 1, such
     *    as in `1/s`. It is typically used when none of the basic units are
     *    appropriate. For example, "new users per day" can be represented as
     *    `1/d` or `{new-users}/d` (and a metric value `5` would mean "5 new
     *    users). Alternatively, "thousands of page views per day" would be
     *    represented as `1000/d` or `k1/d` or `k{page_views}/d` (and a metric
     *    value of `5.3` would mean "5300 page views per day").
     * * `%` represents dimensionless value of 1/100, and annotates values giving
     *    a percentage (so the metric values are typically in the range of 0..100,
     *    and a metric value `3` means "3 percent").
     * * `10^2.%` indicates a metric contains a ratio, typically in the range
     *    0..1, that will be multiplied by 100 and displayed as a percentage
     *    (so a metric value `0.03` means "3 percent").
     *
     * Generated from protobuf field <code>string unit = 5;</code>
     */
    private $unit = '';
    /**
     * A detailed description of the metric, which can be used in documentation.
     *
     * Generated from protobuf field <code>string description = 6;</code>
     */
    private $description = '';
    /**
     * A concise name for the metric, which can be displayed in user interfaces.
     * Use sentence case without an ending period, for example "Request count".
     * This field is optional but it is recommended to be set for any metrics
     * associated with user-visible concepts, such as Quota.
     *
     * Generated from protobuf field <code>string display_name = 7;</code>
     */
    private $display_name = '';
    /**
     * Optional. Metadata which can be used to guide usage of the metric.
     *
     * Generated from protobuf field <code>.google.api.MetricDescriptor.MetricDescriptorMetadata metadata = 10;</code>
     */
    private $metadata = null;
    /**
     * Optional. The launch stage of the metric definition.
     *
     * Generated from protobuf field <code>.google.api.LaunchStage launch_stage = 12;</code>
     */
    private $launch_stage = 0;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $name
     *           The resource name of the metric descriptor.
     *     @type string $type
     *           The metric type, including its DNS name prefix. The type is not
     *           URL-encoded.  All user-defined metric types have the DNS name
     *           `custom.googleapis.com` or `external.googleapis.com`.  Metric types should
     *           use a natural hierarchical grouping. For example:
     *               "custom.googleapis.com/invoice/paid/amount"
     *               "external.googleapis.com/prometheus/up"
     *               "appengine.googleapis.com/http/server/response_latencies"
     *     @type \Google\Api\LabelDescriptor[]|\Google\Protobuf\Internal\RepeatedField $labels
     *           The set of labels that can be used to describe a specific
     *           instance of this metric type. For example, the
     *           `appengine.googleapis.com/http/server/response_latencies` metric
     *           type has a label for the HTTP response code, `response_code`, so
     *           you can look at latencies for successful responses or just
     *           for responses that failed.
     *     @type int $metric_kind
     *           Whether the metric records instantaneous values, changes to a value, etc.
     *           Some combinations of `metric_kind` and `value_type` might not be supported.
     *     @type int $value_type
     *           Whether the measurement is an integer, a floating-point number, etc.
     *           Some combinations of `metric_kind` and `value_type` might not be supported.
     *     @type string $unit
     *           The units in which the metric value is reported. It is only applicable
     *           if the `value_type` is `INT64`, `DOUBLE`, or `DISTRIBUTION`. The `unit`
     *           defines the representation of the stored metric values.
     *           Different systems may scale the values to be more easily displayed (so a
     *           value of `0.02KBy` _might_ be displayed as `20By`, and a value of
     *           `3523KBy` _might_ be displayed as `3.5MBy`). However, if the `unit` is
     *           `KBy`, then the value of the metric is always in thousands of bytes, no
     *           matter how it may be displayed..
     *           If you want a custom metric to record the exact number of CPU-seconds used
     *           by a job, you can create an `INT64 CUMULATIVE` metric whose `unit` is
     *           `s{CPU}` (or equivalently `1s{CPU}` or just `s`). If the job uses 12,005
     *           CPU-seconds, then the value is written as `12005`.
     *           Alternatively, if you want a custome metric to record data in a more
     *           granular way, you can create a `DOUBLE CUMULATIVE` metric whose `unit` is
     *           `ks{CPU}`, and then write the value `12.005` (which is `12005/1000`),
     *           or use `Kis{CPU}` and write `11.723` (which is `12005/1024`).
     *           The supported units are a subset of [The Unified Code for Units of
     *           Measure](http://unitsofmeasure.org/ucum.html) standard:
     *           **Basic units (UNIT)**
     *           * `bit`   bit
     *           * `By`    byte
     *           * `s`     second
     *           * `min`   minute
     *           * `h`     hour
     *           * `d`     day
     *           **Prefixes (PREFIX)**
     *           * `k`     kilo    (10^3)
     *           * `M`     mega    (10^6)
     *           * `G`     giga    (10^9)
     *           * `T`     tera    (10^12)
     *           * `P`     peta    (10^15)
     *           * `E`     exa     (10^18)
     *           * `Z`     zetta   (10^21)
     *           * `Y`     yotta   (10^24)
     *           * `m`     milli   (10^-3)
     *           * `u`     micro   (10^-6)
     *           * `n`     nano    (10^-9)
     *           * `p`     pico    (10^-12)
     *           * `f`     femto   (10^-15)
     *           * `a`     atto    (10^-18)
     *           * `z`     zepto   (10^-21)
     *           * `y`     yocto   (10^-24)
     *           * `Ki`    kibi    (2^10)
     *           * `Mi`    mebi    (2^20)
     *           * `Gi`    gibi    (2^30)
     *           * `Ti`    tebi    (2^40)
     *           * `Pi`    pebi    (2^50)
     *           **Grammar**
     *           The grammar also includes these connectors:
     *           * `/`    division or ratio (as an infix operator). For examples,
     *                    `kBy/{email}` or `MiBy/10ms` (although you should almost never
     *                    have `/s` in a metric `unit`; rates should always be computed at
     *                    query time from the underlying cumulative or delta value).
     *           * `.`    multiplication or composition (as an infix operator). For
     *                    examples, `GBy.d` or `k{watt}.h`.
     *           The grammar for a unit is as follows:
     *               Expression = Component { "." Component } { "/" Component } ;
     *               Component = ( [ PREFIX ] UNIT | "%" ) [ Annotation ]
     *                         | Annotation
     *                         | "1"
     *                         ;
     *               Annotation = "{" NAME "}" ;
     *           Notes:
     *           * `Annotation` is just a comment if it follows a `UNIT`. If the annotation
     *              is used alone, then the unit is equivalent to `1`. For examples,
     *              `{request}/s == 1/s`, `By{transmitted}/s == By/s`.
     *           * `NAME` is a sequence of non-blank printable ASCII characters not
     *              containing `{` or `}`.
     *           * `1` represents a unitary [dimensionless
     *              unit](https://en.wikipedia.org/wiki/Dimensionless_quantity) of 1, such
     *              as in `1/s`. It is typically used when none of the basic units are
     *              appropriate. For example, "new users per day" can be represented as
     *              `1/d` or `{new-users}/d` (and a metric value `5` would mean "5 new
     *              users). Alternatively, "thousands of page views per day" would be
     *              represented as `1000/d` or `k1/d` or `k{page_views}/d` (and a metric
     *              value of `5.3` would mean "5300 page views per day").
     *           * `%` represents dimensionless value of 1/100, and annotates values giving
     *              a percentage (so the metric values are typically in the range of 0..100,
     *              and a metric value `3` means "3 percent").
     *           * `10^2.%` indicates a metric contains a ratio, typically in the range
     *              0..1, that will be multiplied by 100 and displayed as a percentage
     *              (so a metric value `0.03` means "3 percent").
     *     @type string $description
     *           A detailed description of the metric, which can be used in documentation.
     *     @type string $display_name
     *           A concise name for the metric, which can be displayed in user interfaces.
     *           Use sentence case without an ending period, for example "Request count".
     *           This field is optional but it is recommended to be set for any metrics
     *           associated with user-visible concepts, such as Quota.
     *     @type \Google\Api\MetricDescriptor\MetricDescriptorMetadata $metadata
     *           Optional. Metadata which can be used to guide usage of the metric.
     *     @type int $launch_stage
     *           Optional. The launch stage of the metric definition.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Api\Metric::initOnce();
        parent::__construct($data);
    }

    /**
     * The resource name of the metric descriptor.
     *
     * Generated from protobuf field <code>string name = 1;</code>
     * @return string
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * The resource name of the metric descriptor.
     *
     * Generated from protobuf field <code>string name = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setName($var)
    {
        GPBUtil::checkString($var, True);
        $this->name = $var;

        return $this;
    }

    /**
     * The metric type, including its DNS name prefix. The type is not
     * URL-encoded.  All user-defined metric types have the DNS name
     * `custom.googleapis.com` or `external.googleapis.com`.  Metric types should
     * use a natural hierarchical grouping. For example:
     *     "custom.googleapis.com/invoice/paid/amount"
     *     "external.googleapis.com/prometheus/up"
     *     "appengine.googleapis.com/http/server/response_latencies"
     *
     * Generated from protobuf field <code>string type = 8;</code>
     * @return string
     */
    public function getType()
    {
        return $this->type;
    }

    /**
     * The metric type, including its DNS name prefix. The type is not
     * URL-encoded.  All user-defined metric types have the DNS name
     * `custom.googleapis.com` or `external.googleapis.com`.  Metric types should
     * use a natural hierarchical grouping. For example:
     *     "custom.googleapis.com/invoice/paid/amount"
     *     "external.googleapis.com/prometheus/up"
     *     "appengine.googleapis.com/http/server/response_latencies"
     *
     * Generated from protobuf field <code>string type = 8;</code>
     * @param string $var
     * @return $this
     */
    public function setType($var)
    {
        GPBUtil::checkString($var, True);
        $this->type = $var;

        return $this;
    }

    /**
     * The set of labels that can be used to describe a specific
     * instance of this metric type. For example, the
     * `appengine.googleapis.com/http/server/response_latencies` metric
     * type has a label for the HTTP response code, `response_code`, so
     * you can look at latencies for successful responses or just
     * for responses that failed.
     *
     * Generated from protobuf field <code>repeated .google.api.LabelDescriptor labels = 2;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getLabels()
    {
        return $this->labels;
    }

    /**
     * The set of labels that can be used to describe a specific
     * instance of this metric type. For example, the
     * `appengine.googleapis.com/http/server/response_latencies` metric
     * type has a label for the HTTP response code, `response_code`, so
     * you can look at latencies for successful responses or just
     * for responses that failed.
     *
     * Generated from protobuf field <code>repeated .google.api.LabelDescriptor labels = 2;</code>
     * @param \Google\Api\LabelDescriptor[]|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setLabels($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::MESSAGE, \Google\Api\LabelDescriptor::class);
        $this->labels = $arr;

        return $this;
    }

    /**
     * Whether the metric records instantaneous values, changes to a value, etc.
     * Some combinations of `metric_kind` and `value_type` might not be supported.
     *
     * Generated from protobuf field <code>.google.api.MetricDescriptor.MetricKind metric_kind = 3;</code>
     * @return int
     */
    public function getMetricKind()
    {
        return $this->metric_kind;
    }

    /**
     * Whether the metric records instantaneous values, changes to a value, etc.
     * Some combinations of `metric_kind` and `value_type` might not be supported.
     *
     * Generated from protobuf field <code>.google.api.MetricDescriptor.MetricKind metric_kind = 3;</code>
     * @param int $var
     * @return $this
     */
    public function setMetricKind($var)
    {
        GPBUtil::checkEnum($var, \Google\Api\MetricDescriptor_MetricKind::class);
        $this->metric_kind = $var;

        return $this;
    }

    /**
     * Whether the measurement is an integer, a floating-point number, etc.
     * Some combinations of `metric_kind` and `value_type` might not be supported.
     *
     * Generated from protobuf field <code>.google.api.MetricDescriptor.ValueType value_type = 4;</code>
     * @return int
     */
    public function getValueType()
    {
        return $this->value_type;
    }

    /**
     * Whether the measurement is an integer, a floating-point number, etc.
     * Some combinations of `metric_kind` and `value_type` might not be supported.
     *
     * Generated from protobuf field <code>.google.api.MetricDescriptor.ValueType value_type = 4;</code>
     * @param int $var
     * @return $this
     */
    public function setValueType($var)
    {
        GPBUtil::checkEnum($var, \Google\Api\MetricDescriptor_ValueType::class);
        $this->value_type = $var;

        return $this;
    }

    /**
     * The units in which the metric value is reported. It is only applicable
     * if the `value_type` is `INT64`, `DOUBLE`, or `DISTRIBUTION`. The `unit`
     * defines the representation of the stored metric values.
     * Different systems may scale the values to be more easily displayed (so a
     * value of `0.02KBy` _might_ be displayed as `20By`, and a value of
     * `3523KBy` _might_ be displayed as `3.5MBy`). However, if the `unit` is
     * `KBy`, then the value of the metric is always in thousands of bytes, no
     * matter how it may be displayed..
     * If you want a custom metric to record the exact number of CPU-seconds used
     * by a job, you can create an `INT64 CUMULATIVE` metric whose `unit` is
     * `s{CPU}` (or equivalently `1s{CPU}` or just `s`). If the job uses 12,005
     * CPU-seconds, then the value is written as `12005`.
     * Alternatively, if you want a custome metric to record data in a more
     * granular way, you can create a `DOUBLE CUMULATIVE` metric whose `unit` is
     * `ks{CPU}`, and then write the value `12.005` (which is `12005/1000`),
     * or use `Kis{CPU}` and write `11.723` (which is `12005/1024`).
     * The supported units are a subset of [The Unified Code for Units of
     * Measure](http://unitsofmeasure.org/ucum.html) standard:
     * **Basic units (UNIT)**
     * * `bit`   bit
     * * `By`    byte
     * * `s`     second
     * * `min`   minute
     * * `h`     hour
     * * `d`     day
     * **Prefixes (PREFIX)**
     * * `k`     kilo    (10^3)
     * * `M`     mega    (10^6)
     * * `G`     giga    (10^9)
     * * `T`     tera    (10^12)
     * * `P`     peta    (10^15)
     * * `E`     exa     (10^18)
     * * `Z`     zetta   (10^21)
     * * `Y`     yotta   (10^24)
     * * `m`     milli   (10^-3)
     * * `u`     micro   (10^-6)
     * * `n`     nano    (10^-9)
     * * `p`     pico    (10^-12)
     * * `f`     femto   (10^-15)
     * * `a`     atto    (10^-18)
     * * `z`     zepto   (10^-21)
     * * `y`     yocto   (10^-24)
     * * `Ki`    kibi    (2^10)
     * * `Mi`    mebi    (2^20)
     * * `Gi`    gibi    (2^30)
     * * `Ti`    tebi    (2^40)
     * * `Pi`    pebi    (2^50)
     * **Grammar**
     * The grammar also includes these connectors:
     * * `/`    division or ratio (as an infix operator). For examples,
     *          `kBy/{email}` or `MiBy/10ms` (although you should almost never
     *          have `/s` in a metric `unit`; rates should always be computed at
     *          query time from the underlying cumulative or delta value).
     * * `.`    multiplication or composition (as an infix operator). For
     *          examples, `GBy.d` or `k{watt}.h`.
     * The grammar for a unit is as follows:
     *     Expression = Component { "." Component } { "/" Component } ;
     *     Component = ( [ PREFIX ] UNIT | "%" ) [ Annotation ]
     *               | Annotation
     *               | "1"
     *               ;
     *     Annotation = "{" NAME "}" ;
     * Notes:
     * * `Annotation` is just a comment if it follows a `UNIT`. If the annotation
     *    is used alone, then the unit is equivalent to `1`. For examples,
     *    `{request}/s == 1/s`, `By{transmitted}/s == By/s`.
     * * `NAME` is a sequence of non-blank printable ASCII characters not
     *    containing `{` or `}`.
     * * `1` represents a unitary [dimensionless
     *    unit](https://en.wikipedia.org/wiki/Dimensionless_quantity) of 1, such
     *    as in `1/s`. It is typically used when none of the basic units are
     *    appropriate. For example, "new users per day" can be represented as
     *    `1/d` or `{new-users}/d` (and a metric value `5` would mean "5 new
     *    users). Alternatively, "thousands of page views per day" would be
     *    represented as `1000/d` or `k1/d` or `k{page_views}/d` (and a metric
     *    value of `5.3` would mean "5300 page views per day").
     * * `%` represents dimensionless value of 1/100, and annotates values giving
     *    a percentage (so the metric values are typically in the range of 0..100,
     *    and a metric value `3` means "3 percent").
     * * `10^2.%` indicates a metric contains a ratio, typically in the range
     *    0..1, that will be multiplied by 100 and displayed as a percentage
     *    (so a metric value `0.03` means "3 percent").
     *
     * Generated from protobuf field <code>string unit = 5;</code>
     * @return string
     */
    public function getUnit()
    {
        return $this->unit;
    }

    /**
     * The units in which the metric value is reported. It is only applicable
     * if the `value_type` is `INT64`, `DOUBLE`, or `DISTRIBUTION`. The `unit`
     * defines the representation of the stored metric values.
     * Different systems may scale the values to be more easily displayed (so a
     * value of `0.02KBy` _might_ be displayed as `20By`, and a value of
     * `3523KBy` _might_ be displayed as `3.5MBy`). However, if the `unit` is
     * `KBy`, then the value of the metric is always in thousands of bytes, no
     * matter how it may be displayed..
     * If you want a custom metric to record the exact number of CPU-seconds used
     * by a job, you can create an `INT64 CUMULATIVE` metric whose `unit` is
     * `s{CPU}` (or equivalently `1s{CPU}` or just `s`). If the job uses 12,005
     * CPU-seconds, then the value is written as `12005`.
     * Alternatively, if you want a custome metric to record data in a more
     * granular way, you can create a `DOUBLE CUMULATIVE` metric whose `unit` is
     * `ks{CPU}`, and then write the value `12.005` (which is `12005/1000`),
     * or use `Kis{CPU}` and write `11.723` (which is `12005/1024`).
     * The supported units are a subset of [The Unified Code for Units of
     * Measure](http://unitsofmeasure.org/ucum.html) standard:
     * **Basic units (UNIT)**
     * * `bit`   bit
     * * `By`    byte
     * * `s`     second
     * * `min`   minute
     * * `h`     hour
     * * `d`     day
     * **Prefixes (PREFIX)**
     * * `k`     kilo    (10^3)
     * * `M`     mega    (10^6)
     * * `G`     giga    (10^9)
     * * `T`     tera    (10^12)
     * * `P`     peta    (10^15)
     * * `E`     exa     (10^18)
     * * `Z`     zetta   (10^21)
     * * `Y`     yotta   (10^24)
     * * `m`     milli   (10^-3)
     * * `u`     micro   (10^-6)
     * * `n`     nano    (10^-9)
     * * `p`     pico    (10^-12)
     * * `f`     femto   (10^-15)
     * * `a`     atto    (10^-18)
     * * `z`     zepto   (10^-21)
     * * `y`     yocto   (10^-24)
     * * `Ki`    kibi    (2^10)
     * * `Mi`    mebi    (2^20)
     * * `Gi`    gibi    (2^30)
     * * `Ti`    tebi    (2^40)
     * * `Pi`    pebi    (2^50)
     * **Grammar**
     * The grammar also includes these connectors:
     * * `/`    division or ratio (as an infix operator). For examples,
     *          `kBy/{email}` or `MiBy/10ms` (although you should almost never
     *          have `/s` in a metric `unit`; rates should always be computed at
     *          query time from the underlying cumulative or delta value).
     * * `.`    multiplication or composition (as an infix operator). For
     *          examples, `GBy.d` or `k{watt}.h`.
     * The grammar for a unit is as follows:
     *     Expression = Component { "." Component } { "/" Component } ;
     *     Component = ( [ PREFIX ] UNIT | "%" ) [ Annotation ]
     *               | Annotation
     *               | "1"
     *               ;
     *     Annotation = "{" NAME "}" ;
     * Notes:
     * * `Annotation` is just a comment if it follows a `UNIT`. If the annotation
     *    is used alone, then the unit is equivalent to `1`. For examples,
     *    `{request}/s == 1/s`, `By{transmitted}/s == By/s`.
     * * `NAME` is a sequence of non-blank printable ASCII characters not
     *    containing `{` or `}`.
     * * `1` represents a unitary [dimensionless
     *    unit](https://en.wikipedia.org/wiki/Dimensionless_quantity) of 1, such
     *    as in `1/s`. It is typically used when none of the basic units are
     *    appropriate. For example, "new users per day" can be represented as
     *    `1/d` or `{new-users}/d` (and a metric value `5` would mean "5 new
     *    users). Alternatively, "thousands of page views per day" would be
     *    represented as `1000/d` or `k1/d` or `k{page_views}/d` (and a metric
     *    value of `5.3` would mean "5300 page views per day").
     * * `%` represents dimensionless value of 1/100, and annotates values giving
     *    a percentage (so the metric values are typically in the range of 0..100,
     *    and a metric value `3` means "3 percent").
     * * `10^2.%` indicates a metric contains a ratio, typically in the range
     *    0..1, that will be multiplied by 100 and displayed as a percentage
     *    (so a metric value `0.03` means "3 percent").
     *
     * Generated from protobuf field <code>string unit = 5;</code>
     * @param string $var
     * @return $this
     */
    public function setUnit($var)
    {
        GPBUtil::checkString($var, True);
        $this->unit = $var;

        return $this;
    }

    /**
     * A detailed description of the metric, which can be used in documentation.
     *
     * Generated from protobuf field <code>string description = 6;</code>
     * @return string
     */
    public function getDescription()
    {
        return $this->description;
    }

    /**
     * A detailed description of the metric, which can be used in documentation.
     *
     * Generated from protobuf field <code>string description = 6;</code>
     * @param string $var
     * @return $this
     */
    public function setDescription($var)
    {
        GPBUtil::checkString($var, True);
        $this->description = $var;

        return $this;
    }

    /**
     * A concise name for the metric, which can be displayed in user interfaces.
     * Use sentence case without an ending period, for example "Request count".
     * This field is optional but it is recommended to be set for any metrics
     * associated with user-visible concepts, such as Quota.
     *
     * Generated from protobuf field <code>string display_name = 7;</code>
     * @return string
     */
    public function getDisplayName()
    {
        return $this->display_name;
    }

    /**
     * A concise name for the metric, which can be displayed in user interfaces.
     * Use sentence case without an ending period, for example "Request count".
     * This field is optional but it is recommended to be set for any metrics
     * associated with user-visible concepts, such as Quota.
     *
     * Generated from protobuf field <code>string display_name = 7;</code>
     * @param string $var
     * @return $this
     */
    public function setDisplayName($var)
    {
        GPBUtil::checkString($var, True);
        $this->display_name = $var;

        return $this;
    }

    /**
     * Optional. Metadata which can be used to guide usage of the metric.
     *
     * Generated from protobuf field <code>.google.api.MetricDescriptor.MetricDescriptorMetadata metadata = 10;</code>
     * @return \Google\Api\MetricDescriptor\MetricDescriptorMetadata
     */
    public function getMetadata()
    {
        return $this->metadata;
    }

    /**
     * Optional. Metadata which can be used to guide usage of the metric.
     *
     * Generated from protobuf field <code>.google.api.MetricDescriptor.MetricDescriptorMetadata metadata = 10;</code>
     * @param \Google\Api\MetricDescriptor\MetricDescriptorMetadata $var
     * @return $this
     */
    public function setMetadata($var)
    {
        GPBUtil::checkMessage($var, \Google\Api\MetricDescriptor_MetricDescriptorMetadata::class);
        $this->metadata = $var;

        return $this;
    }

    /**
     * Optional. The launch stage of the metric definition.
     *
     * Generated from protobuf field <code>.google.api.LaunchStage launch_stage = 12;</code>
     * @return int
     */
    public function getLaunchStage()
    {
        return $this->launch_stage;
    }

    /**
     * Optional. The launch stage of the metric definition.
     *
     * Generated from protobuf field <code>.google.api.LaunchStage launch_stage = 12;</code>
     * @param int $var
     * @return $this
     */
    public function setLaunchStage($var)
    {
        GPBUtil::checkEnum($var, \Google\Api\LaunchStage::class);
        $this->launch_stage = $var;

        return $this;
    }

}

