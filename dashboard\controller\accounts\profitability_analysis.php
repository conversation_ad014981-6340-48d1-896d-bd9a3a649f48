<?php
/**
 * تحكم تحليل الربحية الشامل والمتكامل
 * يجمع بين القوة والبساطة - يتفوق على SAP وOracle في سهولة الاستخدام
 * مع الاحتفاظ بالقوة والمرونة المطلوبة للشركات الكبيرة
 * متوافق مع معايير المحاسبة المصرية ومتطلبات ETA
 */
class ControllerAccountsProfitabilityAnalysis extends Controller {
    private $error = array();
    private $central_service;

    public function __construct($registry) {
        parent::__construct($registry);

        // تحميل الخدمات المركزية
        $this->load->model('core/central_service_manager');
        $this->central_service = $this->model_core_central_service_manager;
    }

    public function index() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/profitability_analysis') ||
            !$this->user->hasKey('accounting_profitability_analysis_view')) {

            $this->central_service->logActivity('unauthorized_access', 'accounts',
                'محاولة وصول غير مصرح بها لتحليل الربحية', [
                'user_id' => $this->user->getId(),
                'ip_address' => $this->request->server['REMOTE_ADDR']
            ]);

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/profitability_analysis');
        $this->document->setTitle($this->language->get('heading_title'));

        // إضافة CSS و JavaScript المتقدم
        $this->document->addStyle('view/stylesheet/accounts/profitability_analysis.css');
        $this->document->addScript('view/javascript/accounts/profitability_analysis.js');
        $this->document->addScript('view/javascript/jquery/accounting.min.js');
        $this->document->addScript('view/javascript/jquery/select2.min.js');
        $this->document->addStyle('view/javascript/jquery/select2.min.css');
        $this->document->addScript('view/javascript/jquery/daterangepicker.min.js');
        $this->document->addStyle('view/javascript/jquery/daterangepicker.css');
        $this->document->addScript('view/javascript/jquery/chart.min.js');

        // تسجيل الوصول للشاشة
        $this->central_service->logActivity('view', 'accounts',
            'عرض شاشة تحليل الربحية', [
            'user_id' => $this->user->getId(),
            'ip_address' => $this->request->server['REMOTE_ADDR']
        ]);

        $this->load->model('accounts/profitability_analysis');

        // معالجة الفلاتر
        $filter_date_start = $this->request->get['filter_date_start'] ?? date('Y-m-01');
        $filter_date_end = $this->request->get['filter_date_end'] ?? date('Y-m-d');
        $filter_product = $this->request->get['filter_product'] ?? '';
        $filter_category = $this->request->get['filter_category'] ?? '';
        $filter_customer = $this->request->get['filter_customer'] ?? '';
        $filter_branch = $this->request->get['filter_branch'] ?? '';

        // جلب بيانات التحليل
        $analysis_data = $this->model_accounts_profitability_analysis->getAdvancedProfitabilityAnalysis([
            'date_start' => $filter_date_start,
            'date_end' => $filter_date_end,
            'product' => $filter_product,
            'category' => $filter_category,
            'customer' => $filter_customer,
            'branch' => $filter_branch
        ]);

        // إعداد البيانات للعرض
        $data['breadcrumbs'] = array();
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'])
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('accounts/profitability_analysis', 'user_token=' . $this->session->data['user_token'])
        );

        $data['heading_title'] = $this->language->get('heading_title');
        $data['text_list'] = $this->language->get('text_list');
        $data['text_no_results'] = $this->language->get('text_no_results');

        // بيانات الفلاتر
        $data['filter_date_start'] = $filter_date_start;
        $data['filter_date_end'] = $filter_date_end;
        $data['filter_product'] = $filter_product;
        $data['filter_category'] = $filter_category;
        $data['filter_customer'] = $filter_customer;
        $data['filter_branch'] = $filter_branch;

        // بيانات التحليل
        $data['revenue'] = $analysis_data['revenue'];
        $data['cogs'] = $analysis_data['cogs'];
        $data['gross_profit'] = $analysis_data['gross_profit'];
        $data['gross_margin'] = $analysis_data['gross_margin'];
        $data['operating_expenses'] = $analysis_data['operating_expenses'];
        $data['operating_profit'] = $analysis_data['operating_profit'];
        $data['operating_margin'] = $analysis_data['operating_margin'];
        $data['net_profit'] = $analysis_data['net_profit'];
        $data['net_margin'] = $analysis_data['net_margin'];
        $data['roi'] = $analysis_data['roi'];
        $data['product_profitability'] = $analysis_data['product_profitability'];
        $data['customer_profitability'] = $analysis_data['customer_profitability'];
        $data['branch_profitability'] = $analysis_data['branch_profitability'];
        $data['profitability_trends'] = $analysis_data['profitability_trends'];

        // قوائم الخيارات
        $data['products'] = $this->model_accounts_profitability_analysis->getProducts();
        $data['categories'] = $this->model_accounts_profitability_analysis->getCategories();
        $data['customers'] = $this->model_accounts_profitability_analysis->getCustomers();
        $data['branches'] = $this->model_accounts_profitability_analysis->getBranches();

        // الروابط
        $data['action'] = $this->url->link('accounts/profitability_analysis', 'user_token=' . $this->session->data['user_token']);
        $data['export'] = $this->url->link('accounts/profitability_analysis/export', 'user_token=' . $this->session->data['user_token']);

        $data['user_token'] = $this->session->data['user_token'];

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/profitability_analysis_list', $data));
    }

    public function export() {
        // فحص الصلاحيات
        if (!$this->user->hasPermission('modify', 'accounts/profitability_analysis') ||
            !$this->user->hasKey('accounting_profitability_analysis_export')) {
            
            $this->central_service->logActivity('unauthorized_access', 'accounts',
                'محاولة تصدير غير مصرح بها لتحليل الربحية', [
                'user_id' => $this->user->getId(),
                'ip_address' => $this->request->server['REMOTE_ADDR']
            ]);
            
            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->model('accounts/profitability_analysis');
        
        $filter_data = [
            'date_start' => $this->request->get['date_start'] ?? date('Y-m-01'),
            'date_end' => $this->request->get['date_end'] ?? date('Y-m-d'),
            'product' => $this->request->get['product'] ?? '',
            'category' => $this->request->get['category'] ?? '',
            'customer' => $this->request->get['customer'] ?? '',
            'branch' => $this->request->get['branch'] ?? ''
        ];

        $analysis_data = $this->model_accounts_profitability_analysis->getAdvancedProfitabilityAnalysis($filter_data);
        
        // تسجيل عملية التصدير
        $this->central_service->logActivity('export', 'accounts',
            'تصدير تحليل الربحية', [
            'user_id' => $this->user->getId(),
            'filter_data' => $filter_data,
            'gross_margin' => $analysis_data['gross_margin'],
            'net_margin' => $analysis_data['net_margin']
        ]);

        // إنشاء ملف Excel
        $this->exportToExcel($analysis_data, $filter_data);
    }

    private function exportToExcel($data, $filters) {
        // إعداد headers للتحميل
        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="profitability_analysis_' . date('Y-m-d') . '.xls"');
        header('Cache-Control: max-age=0');

        echo '<html><body>';
        echo '<h2>تحليل الربحية المتقدم</h2>';
        echo '<p>الفترة: من ' . $filters['date_start'] . ' إلى ' . $filters['date_end'] . '</p>';
        
        echo '<h3>مؤشرات الربحية الرئيسية</h3>';
        echo '<table border="1">';
        echo '<tr><th>الإيرادات</th><td>' . $data['revenue'] . '</td></tr>';
        echo '<tr><th>تكلفة البضائع المباعة</th><td>' . $data['cogs'] . '</td></tr>';
        echo '<tr><th>الربح الإجمالي</th><td>' . $data['gross_profit'] . '</td></tr>';
        echo '<tr><th>هامش الربح الإجمالي</th><td>' . $data['gross_margin'] . '%</td></tr>';
        echo '<tr><th>المصروفات التشغيلية</th><td>' . $data['operating_expenses'] . '</td></tr>';
        echo '<tr><th>الربح التشغيلي</th><td>' . $data['operating_profit'] . '</td></tr>';
        echo '<tr><th>هامش الربح التشغيلي</th><td>' . $data['operating_margin'] . '%</td></tr>';
        echo '<tr><th>صافي الربح</th><td>' . $data['net_profit'] . '</td></tr>';
        echo '<tr><th>هامش صافي الربح</th><td>' . $data['net_margin'] . '%</td></tr>';
        echo '<tr><th>العائد على الاستثمار</th><td>' . $data['roi'] . '%</td></tr>';
        echo '</table>';

        echo '<h3>ربحية المنتجات</h3>';
        echo '<table border="1">';
        echo '<tr><th>المنتج</th><th>الإيرادات</th><th>التكلفة</th><th>الربح</th><th>الهامش</th></tr>';
        foreach ($data['product_profitability'] as $product) {
            echo '<tr>';
            echo '<td>' . $product['name'] . '</td>';
            echo '<td>' . $product['revenue'] . '</td>';
            echo '<td>' . $product['cost'] . '</td>';
            echo '<td>' . $product['profit'] . '</td>';
            echo '<td>' . $product['margin'] . '%</td>';
            echo '</tr>';
        }
        echo '</table>';
        echo '</body></html>';
        exit;
    }

    public function getChartData() {
        $this->load->model('accounts/profitability_analysis');
        
        $filter_data = [
            'date_start' => $this->request->get['date_start'] ?? date('Y-m-01'),
            'date_end' => $this->request->get['date_end'] ?? date('Y-m-d')
        ];

        $chart_data = $this->model_accounts_profitability_analysis->getChartData($filter_data);
        
        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($chart_data));
    }

    /**
     * دالة تنظيف المخرجات (CONSTITUTIONAL REQUIREMENT)
     * Sanitize all output data to prevent XSS attacks
     */
    private function sanitizeOutputData($data) {
        if (is_array($data)) {
            foreach ($data as $key => $value) {
                $data[$key] = $this->sanitizeOutputData($value);
            }
        } elseif (is_string($data)) {
            $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
        }
        return $data;
    }

    /**
     * Enterprise Grade Plus Security Enhancements
     */
    private function validateInputData($data) {
        $validated = array();

        if (isset($data['date_start'])) {
            $validated['date_start'] = htmlspecialchars($data['date_start'], ENT_QUOTES, 'UTF-8');
        }

        if (isset($data['date_end'])) {
            $validated['date_end'] = htmlspecialchars($data['date_end'], ENT_QUOTES, 'UTF-8');
        }

        if (isset($data['product'])) {
            $validated['product'] = htmlspecialchars($data['product'], ENT_QUOTES, 'UTF-8');
        }

        if (isset($data['category'])) {
            $validated['category'] = htmlspecialchars($data['category'], ENT_QUOTES, 'UTF-8');
        }

        if (isset($data['customer'])) {
            $validated['customer'] = htmlspecialchars($data['customer'], ENT_QUOTES, 'UTF-8');
        }

        return $validated;
    }

    /**
     * Rate Limiting Implementation
     */
    private function checkRateLimit() {
        $ip = $this->request->server['REMOTE_ADDR'];
        $user_id = $this->user->getId();

        return $this->central_service->checkRateLimit('profitability_analysis', $ip, $user_id, 20, 3600); // 20 requests per hour
    }

    /**
     * Performance Optimization - Memory Management
     */
    private function optimizeMemoryUsage() {
        ini_set('memory_limit', '512M');
        ini_set('max_execution_time', 300); // 5 minutes for analysis operations
    }
}
