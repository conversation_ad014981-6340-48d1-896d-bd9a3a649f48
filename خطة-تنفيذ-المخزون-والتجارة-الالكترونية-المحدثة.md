# خطة تنفيذ وحدة المخزون والتجارة الإلكترونية المتشابكة - AYM ERP (محدثة)

**التاريخ:** 18/7/2025 - 19:30  
**المشروع:** AYM ERP - خطة تنفيذ دقيقة ومحكمة  
**الحالة:** 🚀 **محدثة مع مراعاة الفروق بين المخزون الوهمي/الفعلي وأدوار المستخدمين**

---

## 🎯 **منهجية التنفيذ المحدثة**

### **المبادئ الأساسية:**
1. **🔍 فهم الفروق الجوهرية** - المخزون الوهمي vs الفعلي
2. **👥 احترام أدوار المستخدمين** - كل دور له شاشاته المخصصة
3. **🔗 التشابك المعقد** - header.twig + ProductsPro + الباقات
4. **📊 قاعدة البيانات أولاً** - إنشاء ملف SQL للتعديلات المطلوبة
5. **⚡ الميزات التنافسية** - الحفاظ على التفوق الحالي

---

## 📊 **المرحلة الأولى: إعداد قاعدة البيانات (أسبوع واحد)**

### **🗄️ المهمة الأولية: إنشاء ملف SQL للتعديلات**

- [ ] 1.0 إنشاء ملف inventory-ecommerce-database-updates.sql
  - **📝 الهدف:** تجميع جميع الاستعلامات المطلوبة لتحديث قاعدة البيانات
  - **📋 المحتوى:**
    - إضافة جداول جديدة للمخزون الوهمي راعي انه موجود quantity_available
    - تحديث جداول المخزون الفعلي
    - إضافة جداول الباقات المعقدة
    - تحديث جداول الأدوار والصلاحيات
    - إضافة فهارس محسنة للأداء
  - **🔄 التكامل:** مع db.txt و minidb.txt الحاليين
  - **⏱️ المدة:** 2-3 أيام
  - **👤 المسؤول:** مطور قاعدة البيانات

### **🗃️ الجداول الجديدة المطلوبة:**

- [ ] 1.1 جداول المخزون الوهمي (Virtual Inventory)
  - `cod_virtual_inventory` - المخزون المتاح للبيع عبر الإنترنت
  - `cod_virtual_inventory_rules` - قواعد المخزون الوهمي
  - `cod_virtual_inventory_reservations` - حجوزات المخزون المؤقتة
  - **⏱️ المدة:** 1 يوم

- [ ] 1.2 جداول الباقات المتقدمة (Advanced Bundles)
  - `cod_product_bundles` - تعريف الباقات
  - `cod_bundle_items` - عناصر الباقات
  - `cod_bundle_pricing_rules` - قواعد تسعير الباقات
  - `cod_bundle_inventory_mapping` - ربط الباقات بالمخزون
  - **⏱️ المدة:** 1 يوم

- [ ] 1.3 جداول الأدوار المتخصصة (Specialized Roles)
  - `cod_user_branch_permissions` - صلاحيات المستخدمين حسب الفرع
  - `cod_inventory_access_matrix` - مصفوفة الوصول للمخزون
  - `cod_ecommerce_manager_settings` - إعدادات مدير المتجر
  - **⏱️ المدة:** 1 يوم

---

## 🏭 **المرحلة الثانية: المخزون الفعلي - أمين المخزن (أسبوعان)**

### **🎯 الهدف:** تطوير شاشات إدارة المخزون الفعلي للمستودعات

### **الأسبوع الأول: الشاشات الأساسية**

- [ ] 2.1 تحليل وتطوير stock_movement.php
  - **🔍 التحليل الشامل MVC:**
    - Controller: فحص الدوال والعمليات
    - Model: فحص التكامل مع قاعدة البيانات
    - View: فحص واجهة المستخدم
    - Language: فحص ملفات اللغة العربية/الإنجليزية
  - **⭐ التقييم:** من 1-5 نجوم (مثل المحاسبة)
  - **🔄 التطبيق:** تكامل مع الخدمات المركزية + WAC + قيود محاسبية
  - **👤 المستخدم:** أمين المخزن فقط
  - **📊 البيانات:** quantity (الكمية الفعلية)
  - **⏱️ المدة:** 2-3 أيام

- [ ] 2.2 تحليل وتطوير stock_adjustment.php
  - **🎯 الهدف:** تسويات المخزون الفعلي مع موافقات متدرجة
  - **🔄 التكامل:** نظام الموافقات + تسجيل الأنشطة + إشعارات
  - **👤 المستخدم:** أمين المخزن + مدير المستودع
  - **⏱️ المدة:** 2-3 أيام

- [ ] 2.3 تحليل وتطوير goods_receipt.php
  - **🎯 الهدف:** استلام البضائع مع تطبيق WAC التلقائي
  - **🔄 التكامل:** حساب WAC + قيود محاسبية + تحديث التكلفة
  - **👤 المستخدم:** أمين المخزن
  - **⏱️ المدة:** 2-3 أيام

### **الأسبوع الثاني: الشاشات المتقدمة**

- [ ] 2.4 تحليل وتطوير warehouse.php
  - **🎯 الهدف:** إدارة المستودعات والمواقع
  - **🔄 التكامل:** ربط بالفروع + صلاحيات متقدمة
  - **👤 المستخدم:** مدير المستودع
  - **⏱️ المدة:** 2-3 أيام

- [ ] 2.5 تحليل وتطوير stocktake.php
  - **🎯 الهدف:** الجرد الفعلي مع مطابقة النظام
  - **🔄 التكامل:** مقارنة مع النظام + تسويات تلقائية
  - **👤 المستخدم:** أمين المخزن + مراجع الجرد
  - **⏱️ المدة:** 2-3 أيام

---

## 🛍️ **المرحلة الثالثة: المخزون الوهمي - مدير المتجر (أسبوعان)**

### **🎯 الهدف:** تطوير شاشات إدارة المخزون المتاح للبيع عبر الإنترنت

### **الأسبوع الثالث: إدارة المخزون الوهمي**

- [ ] 3.1 تطوير virtual_inventory_management.php (جديد)
  - **🎯 الهدف:** إدارة quantity_available للمتجر الإلكتروني
  - **📊 البيانات:** quantity_available (المتاح للبيع)
  - **🔄 الميزات:**
    - تحديد المتاح للبيع (يمكن أن يكون أكبر من الفعلي)
    - إدارة العروض والخصومات
    - حجز مؤقت للطلبات
    - ربط مع المخزون الفعلي
  - **👤 المستخدم:** مدير المتجر فقط
  - **⏱️ المدة:** 3-4 أيام

- [ ] 3.2 تحسين وتطوير ProductsPro المحسن
  - **🔍 مراجعة التحليل الموجود:** productspro-advanced-analysis.md
  - **🎯 التحسينات:**
    - تقسيم الكود المعقد (300+ سطر) إلى وحدات
    - تحسين الأداء والاستجابة
    - دعم أفضل للوحدات المتعددة
    - تكامل محسن مع المخزون الوهمي
  - **👤 المستخدم:** مدير المتجر
  - **⏱️ المدة:** 3-4 أيام

### **الأسبوع الرابع: الباقات والعروض**

- [ ] 3.3 تطوير bundle_management.php (جديد)
  - **🎯 الهدف:** إدارة الباقات المعقدة للمتجر الإلكتروني
  - **🔄 الميزات:**
    - باقات ثابتة ومتغيرة
    - خصومات خاصة للباقات
    - ربط مع المخزون الوهمي
    - تسعير ديناميكي للباقات
  - **👤 المستخدم:** مدير المتجر
  - **⏱️ المدة:** 3-4 أيام

- [ ] 3.4 تحسين header.twig للطلب السريع
  - **🔍 مراجعة التحليل الموجود:** header-quick-order-analysis.md
  - **🎯 التحسينات:**
    - تحسين الكود المعقد (500+ سطر JavaScript)
    - دعم أفضل للباقات
    - تكامل محسن مع المخزون الوهمي
    - تحسين تجربة المستخدم
  - **👤 المستخدم:** العملاء (واجهة المتجر)
  - **⏱️ المدة:** 3-4 أيام

---

## 🏪 **المرحلة الرابعة: نظام الفروع - مدير الفرع (أسبوع واحد)**

### **🎯 الهدف:** تطوير شاشات إدارة مخزون الفرع والمبيعات

- [ ] 4.1 تحليل وتطوير branch_inventory_management.php
  - **🎯 الهدف:** إدارة مخزون الفرع الواحد
  - **📊 البيانات:** quantity مقيدة بالفرع
  - **🔄 الميزات:**
    - عرض مخزون الفرع فقط
    - تحويلات بين الفروع
    - تقارير خاصة بالفرع
  - **👤 المستخدم:** مدير الفرع
  - **⏱️ المدة:** 2-3 أيام

- [ ] 4.2 تحسين نظام POS المعقد
  - **🎯 الهدف:** ربط POS بمخزون الفرع
  - **🔄 الميزات:**
    - Multi-user Sessions
    - Branch-specific Inventory
    - Real-time Sync
  - **👤 المستخدم:** مدير الفرع + الكاشير
  - **⏱️ المدة:** 2-3 أيام

---

## 💻 **المرحلة الخامسة: واجهات الموظفين - موظف المتجر (أسبوع واحد)**

### **🎯 الهدف:** تطوير شاشات معالجة الطلبات والمبيعات

- [ ] 5.1 تطوير order_processing_enhanced.php
  - **🎯 الهدف:** معالجة طلبات المتجر الإلكتروني
  - **🔄 الميزات:**
    - معالجة الطلبات العادية والباقات
    - تحديث المخزون الوهمي والفعلي
    - إنشاء قيود محاسبية
  - **👤 المستخدم:** موظف المتجر
  - **⏱️ المدة:** 3-4 أيام

- [ ] 5.2 تطوير customer_service_portal.php
  - **🎯 الهدف:** خدمة العملاء والإرجاعات
  - **🔄 الميزات:**
    - إدارة الإرجاعات
    - تحديث المخزون عند الإرجاع
    - خدمة العملاء
  - **👤 المستخدم:** موظف المتجر
  - **⏱️ المدة:** 2-3 أيام

---

## 🔧 **المرحلة السادسة: التكامل والاختبار (أسبوعان)**

### **الأسبوع السادس: التكامل**

- [ ] 6.1 تكامل نظام WAC مع جميع الشاشات
  - **🎯 الهدف:** تطبيق WAC في جميع عمليات المخزون
  - **🔄 التكامل:** مع النظام المحاسبي المطور
  - **⏱️ المدة:** 3-4 أيام

- [ ] 6.2 تكامل الخدمات المركزية
  - **🎯 الهدف:** ربط جميع الشاشات بـ central_service_manager
  - **🔄 التكامل:** تسجيل الأنشطة + الإشعارات + الصلاحيات
  - **⏱️ المدة:** 3-4 أيام

### **الأسبوع السابع: الاختبار والتحسين**

- [ ] 6.3 اختبار التكامل الشامل
  - **🎯 الهدف:** اختبار جميع السيناريوهات
  - **🔄 الاختبارات:**
    - اختبار الأدوار والصلاحيات
    - اختبار التشابك بين المخزون الوهمي والفعلي
    - اختبار الباقات والعروض
    - اختبار نظام WAC والقيود المحاسبية
  - **⏱️ المدة:** 4-5 أيام

- [ ] 6.4 تحسين الأداء والاستجابة
  - **🎯 الهدف:** ضمان الأداء المطلوب
  - **🔄 التحسينات:**
    - تحسين استعلامات قاعدة البيانات
    - تحسين واجهات المستخدم
    - تحسين الكود JavaScript
  - **⏱️ المدة:** 2-3 أيام

---

## 📊 **توزيع المهام حسب الأدوار**

### **👤 أمين المخزن (Warehouse Manager):**
- stock_movement.php
- stock_adjustment.php  
- goods_receipt.php
- warehouse.php
- stocktake.php
- **المجموع:** 5 شاشات متخصصة

### **👤 مدير المتجر (E-commerce Manager):**
- virtual_inventory_management.php
- ProductsPro المحسن
- bundle_management.php
- header.twig المحسن
- **المجموع:** 4 شاشات متخصصة

### **👤 مدير الفرع (Branch Manager):**
- branch_inventory_management.php
- نظام POS المحسن
- **المجموع:** 2 شاشات متخصصة

### **👤 موظف المتجر (Store Staff):**
- order_processing_enhanced.php
- customer_service_portal.php
- **المجموع:** 2 شاشات متخصصة

### **👤 الكاشير (Cashier):**
- نظام POS (مقيد بالفرع)
- **المجموع:** 1 شاشة مقيدة

---

## ⏱️ **الجدول الزمني التفصيلي**

### **📅 الأسبوع 1: قاعدة البيانات**
- **الأيام 1-3:** إنشاء ملف SQL وتحديث الجداول
- **الأيام 4-7:** اختبار التحديثات وضمان التوافق

### **📅 الأسابيع 2-3: المخزون الفعلي**
- **الأسبوع 2:** الشاشات الأساسية (أمين المخزن)
- **الأسبوع 3:** الشاشات المتقدمة (أمين المخزن)

### **📅 الأسابيع 4-5: المخزون الوهمي**
- **الأسبوع 4:** إدارة المخزون الوهمي (مدير المتجر)
- **الأسبوع 5:** الباقات والعروض (مدير المتجر)

### **📅 الأسبوع 6: نظام الفروع**
- **الأيام 1-4:** إدارة مخزون الفرع (مدير الفرع)
- **الأيام 5-7:** تحسين نظام POS

### **📅 الأسبوع 7: واجهات الموظفين**
- **الأيام 1-4:** معالجة الطلبات (موظف المتجر)
- **الأيام 5-7:** خدمة العملاء

### **📅 الأسابيع 8-9: التكامل والاختبار**
- **الأسبوع 8:** التكامل الشامل
- **الأسبوع 9:** الاختبار والتحسين

---

## 🎯 **معايير النجاح المحدثة**

### **✅ معايير تقنية:**
1. **🗄️ قاعدة البيانات:** جميع التحديثات مطبقة بنجاح
2. **🔗 التكامل:** 100% من الشاشات متكاملة مع الخدمات المركزية
3. **⚡ الأداء:** تحميل الشاشات في أقل من 2 ثانية
4. **🔒 الأمان:** تطبيق الصلاحيات المزدوجة في جميع الشاشات

### **✅ معايير وظيفية:**
1. **📊 المخزون الوهمي:** يعمل بشكل منفصل عن المخزون الفعلي
2. **🏭 المخزون الفعلي:** نظام WAC مطبق في جميع العمليات
3. **📦 الباقات:** تعمل بشكل صحيح مع المخزون الوهمي
4. **🏪 الفروع:** كل مستخدم يصل لمخزون فرعه فقط

### **✅ معايير تجربة المستخدم:**
1. **👤 الأدوار:** كل دور له شاشاته المخصصة
2. **🎨 الواجهات:** سهلة الاستخدام ومتجاوبة
3. **📱 الاستجابة:** تعمل على جميع الأجهزة
4. **🔔 الإشعارات:** تعمل في الوقت الفعلي

---

## 🚨 **المخاطر والتحديات**

### **⚠️ مخاطر تقنية:**
1. **تعقيد التشابك** بين المخزون الوهمي والفعلي
2. **صعوبة اختبار** جميع السيناريوهات
3. **تحديات الأداء** مع البيانات الكثيرة

### **⚠️ مخاطر تشغيلية:**
1. **تدريب المستخدمين** على النظام المعقد
2. **مقاومة التغيير** من المستخدمين الحاليين
3. **صعوبة الصيانة** للنظام المعقد

### **🛡️ خطط التخفيف:**
1. **توثيق شامل** لجميع العمليات
2. **تدريب مكثف** للمستخدمين
3. **دعم فني متخصص** لفترة انتقالية
4. **اختبار مكثف** قبل التشغيل

---

## 📋 **قائمة التحقق النهائية**

### **🗄️ قاعدة البيانات:**
- [ ] ملف SQL مكتمل ومختبر
- [ ] جميع الجداول الجديدة منشأة
- [ ] الفهارس محسنة للأداء
- [ ] النسخ الاحتياطية جاهزة

### **🏭 المخزون الفعلي:**
- [ ] جميع شاشات أمين المخزن مكتملة
- [ ] نظام WAC يعمل بشكل صحيح
- [ ] القيود المحاسبية تلقائية
- [ ] التكامل مع الخدمات المركزية

### **🛍️ المخزون الوهمي:**
- [ ] شاشات مدير المتجر مكتملة
- [ ] نظام الباقات يعمل بشكل صحيح
- [ ] header.twig محسن ومختبر
- [ ] ProductsPro محسن ومقسم

### **🏪 نظام الفروع:**
- [ ] شاشات مدير الفرع مكتملة
- [ ] نظام POS محسن ومختبر
- [ ] الصلاحيات تعمل بشكل صحيح
- [ ] التقارير خاصة بكل فرع

### **💻 واجهات الموظفين:**
- [ ] شاشات موظف المتجر مكتملة
- [ ] معالجة الطلبات تعمل بشكل صحيح
- [ ] خدمة العملاء متكاملة
- [ ] الإرجاعات تحدث المخزون

### **🔧 التكامل والاختبار:**
- [ ] جميع الشاشات متكاملة
- [ ] الاختبارات مكتملة ومجتازة
- [ ] الأداء محسن ومقبول
- [ ] التوثيق مكتمل وشامل

---

## 🏆 **النتيجة المتوقعة**

### **🎯 نظام متكامل يحقق:**
1. **فصل واضح** بين المخزون الوهمي والفعلي
2. **أدوار محددة** لكل نوع من المستخدمين
3. **تشابك ذكي** بين جميع المكونات
4. **أداء عالي** وتجربة مستخدم ممتازة
5. **تكامل كامل** مع النظام المحاسبي

### **🚀 ميزة تنافسية استثنائية:**
- **أول نظام ERP** يجمع بين المخزون الوهمي والفعلي بهذا التطور
- **أقوى نظام باقات** في المنطقة
- **أسرع نظام طلب سريع** مع header.twig المحسن
- **أدق نظام WAC** متكامل مع المحاسبة

---
**آخر تحديث:** 18/7/2025 - 19:30  
**الحالة:** ✅ خطة محدثة ودقيقة - جاهزة للتنفيذ  
**المنهجية:** مراعاة الفروق الجوهرية والأدوار المتخصصة  
**المخطط:** Kiro AI Assistant

---

## 📊 **ملحق: تفاصيل ملف SQL المطلوب**

### **🗄️ محتويات inventory-ecommerce-database-updates.sql:**

```sql
-- =====================================================
-- AYM ERP - تحديثات قاعدة البيانات للمخزون والتجارة الإلكترونية
-- التاريخ: 18/7/2025
-- الهدف: دعم المخزون الوهمي والفعلي والباقات المعقدة
-- =====================================================

-- 1. جداول المخزون الوهمي
-- =====================================================

-- جدول المخزون المتاح للبيع عبر الإنترنت
CREATE TABLE IF NOT EXISTS `cod_virtual_inventory` (
  `virtual_inventory_id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL,
  `unit_id` int(11) NOT NULL,
  `quantity_available` decimal(15,4) NOT NULL DEFAULT '0.0000',
  `quantity_reserved` decimal(15,4) NOT NULL DEFAULT '0.0000',
  `allow_oversell` tinyint(1) NOT NULL DEFAULT '0',
  `oversell_limit` decimal(15,4) NOT NULL DEFAULT '0.0000',
  `safety_stock` decimal(15,4) NOT NULL DEFAULT '0.0000',
  `reorder_point` decimal(15,4) NOT NULL DEFAULT '0.0000',
  `last_updated` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `updated_by` int(11) NOT NULL,
  PRIMARY KEY (`virtual_inventory_id`),
  UNIQUE KEY `product_unit` (`product_id`, `unit_id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_unit_id` (`unit_id`),
  KEY `idx_quantity_available` (`quantity_available`),
  FOREIGN KEY (`product_id`) REFERENCES `cod_product` (`product_id`) ON DELETE CASCADE,
  FOREIGN KEY (`unit_id`) REFERENCES `cod_unit` (`unit_id`) ON DELETE CASCADE,
  FOREIGN KEY (`updated_by`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- قواعد المخزون الوهمي
CREATE TABLE IF NOT EXISTS `cod_virtual_inventory_rules` (
  `rule_id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL,
  `rule_type` enum('oversell','safety_stock','reorder','seasonal') NOT NULL,
  `rule_value` decimal(15,4) NOT NULL,
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` int(11) NOT NULL,
  PRIMARY KEY (`rule_id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_rule_type` (`rule_type`),
  KEY `idx_is_active` (`is_active`),
  FOREIGN KEY (`product_id`) REFERENCES `cod_product` (`product_id`) ON DELETE CASCADE,
  FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- حجوزات المخزون المؤقتة (للطلب السريع)
CREATE TABLE IF NOT EXISTS `cod_virtual_inventory_reservations` (
  `reservation_id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL,
  `unit_id` int(11) NOT NULL,
  `quantity_reserved` decimal(15,4) NOT NULL,
  `session_id` varchar(32) NOT NULL,
  `customer_id` int(11) DEFAULT NULL,
  `reservation_type` enum('cart','quick_order','quote') NOT NULL DEFAULT 'cart',
  `expires_at` timestamp NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`reservation_id`),
  KEY `idx_product_unit` (`product_id`, `unit_id`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_customer_id` (`customer_id`),
  KEY `idx_expires_at` (`expires_at`),
  FOREIGN KEY (`product_id`) REFERENCES `cod_product` (`product_id`) ON DELETE CASCADE,
  FOREIGN KEY (`unit_id`) REFERENCES `cod_unit` (`unit_id`) ON DELETE CASCADE,
  FOREIGN KEY (`customer_id`) REFERENCES `cod_customer` (`customer_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- 2. جداول الباقات المتقدمة
-- =====================================================

-- تعريف الباقات
CREATE TABLE IF NOT EXISTS `cod_product_bundles` (
  `bundle_id` int(11) NOT NULL AUTO_INCREMENT,
  `bundle_name` varchar(255) NOT NULL,
  `bundle_description` text,
  `bundle_type` enum('fixed','dynamic','conditional','tiered') NOT NULL DEFAULT 'fixed',
  `discount_type` enum('percentage','fixed_amount','special_price') NOT NULL DEFAULT 'percentage',
  `discount_value` decimal(15,4) NOT NULL DEFAULT '0.0000',
  `min_quantity` int(11) NOT NULL DEFAULT '1',
  `max_quantity` int(11) DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `created_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` int(11) NOT NULL,
  PRIMARY KEY (`bundle_id`),
  KEY `idx_bundle_type` (`bundle_type`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_dates` (`start_date`, `end_date`),
  FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- عناصر الباقات
CREATE TABLE IF NOT EXISTS `cod_bundle_items` (
  `bundle_item_id` int(11) NOT NULL AUTO_INCREMENT,
  `bundle_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `unit_id` int(11) NOT NULL,
  `quantity` decimal(15,4) NOT NULL DEFAULT '1.0000',
  `is_required` tinyint(1) NOT NULL DEFAULT '1',
  `is_default` tinyint(1) NOT NULL DEFAULT '0',
  `sort_order` int(3) NOT NULL DEFAULT '0',
  PRIMARY KEY (`bundle_item_id`),
  UNIQUE KEY `bundle_product_unit` (`bundle_id`, `product_id`, `unit_id`),
  KEY `idx_bundle_id` (`bundle_id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_sort_order` (`sort_order`),
  FOREIGN KEY (`bundle_id`) REFERENCES `cod_product_bundles` (`bundle_id`) ON DELETE CASCADE,
  FOREIGN KEY (`product_id`) REFERENCES `cod_product` (`product_id`) ON DELETE CASCADE,
  FOREIGN KEY (`unit_id`) REFERENCES `cod_unit` (`unit_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- قواعد تسعير الباقات
CREATE TABLE IF NOT EXISTS `cod_bundle_pricing_rules` (
  `pricing_rule_id` int(11) NOT NULL AUTO_INCREMENT,
  `bundle_id` int(11) NOT NULL,
  `customer_group_id` int(11) DEFAULT NULL,
  `min_quantity` int(11) NOT NULL DEFAULT '1',
  `max_quantity` int(11) DEFAULT NULL,
  `discount_type` enum('percentage','fixed_amount','special_price') NOT NULL,
  `discount_value` decimal(15,4) NOT NULL,
  `priority` int(3) NOT NULL DEFAULT '0',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  PRIMARY KEY (`pricing_rule_id`),
  KEY `idx_bundle_id` (`bundle_id`),
  KEY `idx_customer_group_id` (`customer_group_id`),
  KEY `idx_priority` (`priority`),
  FOREIGN KEY (`bundle_id`) REFERENCES `cod_product_bundles` (`bundle_id`) ON DELETE CASCADE,
  FOREIGN KEY (`customer_group_id`) REFERENCES `cod_customer_group` (`customer_group_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- ربط الباقات بالمخزون
CREATE TABLE IF NOT EXISTS `cod_bundle_inventory_mapping` (
  `mapping_id` int(11) NOT NULL AUTO_INCREMENT,
  `bundle_id` int(11) NOT NULL,
  `inventory_type` enum('virtual','physical','both') NOT NULL DEFAULT 'virtual',
  `check_availability` tinyint(1) NOT NULL DEFAULT '1',
  `reserve_inventory` tinyint(1) NOT NULL DEFAULT '1',
  `allocation_method` enum('fifo','lifo','specific') NOT NULL DEFAULT 'fifo',
  PRIMARY KEY (`mapping_id`),
  UNIQUE KEY `bundle_inventory_type` (`bundle_id`, `inventory_type`),
  FOREIGN KEY (`bundle_id`) REFERENCES `cod_product_bundles` (`bundle_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- 3. جداول الأدوار المتخصصة
-- =====================================================

-- صلاحيات المستخدمين حسب الفرع
CREATE TABLE IF NOT EXISTS `cod_user_branch_permissions` (
  `permission_id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `branch_id` int(11) NOT NULL,
  `permission_type` enum('inventory_view','inventory_edit','sales','reports','management') NOT NULL,
  `can_access` tinyint(1) NOT NULL DEFAULT '0',
  `restrictions` text,
  `granted_by` int(11) NOT NULL,
  `granted_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`permission_id`),
  UNIQUE KEY `user_branch_permission` (`user_id`, `branch_id`, `permission_type`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_branch_id` (`branch_id`),
  FOREIGN KEY (`user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE,
  FOREIGN KEY (`branch_id`) REFERENCES `cod_branch` (`branch_id`) ON DELETE CASCADE,
  FOREIGN KEY (`granted_by`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- مصفوفة الوصول للمخزون
CREATE TABLE IF NOT EXISTS `cod_inventory_access_matrix` (
  `access_id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `inventory_type` enum('physical','virtual','both') NOT NULL,
  `access_level` enum('view','edit','approve','admin') NOT NULL,
  `branch_restriction` tinyint(1) NOT NULL DEFAULT '1',
  `product_category_restriction` text,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_by` int(11) NOT NULL,
  PRIMARY KEY (`access_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_inventory_type` (`inventory_type`),
  KEY `idx_access_level` (`access_level`),
  FOREIGN KEY (`user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE,
  FOREIGN KEY (`created_by`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- إعدادات مدير المتجر
CREATE TABLE IF NOT EXISTS `cod_ecommerce_manager_settings` (
  `setting_id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text,
  `setting_type` enum('boolean','integer','decimal','string','json') NOT NULL DEFAULT 'string',
  `is_encrypted` tinyint(1) NOT NULL DEFAULT '0',
  `last_updated` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`setting_id`),
  UNIQUE KEY `user_setting` (`user_id`, `setting_key`),
  KEY `idx_user_id` (`user_id`),
  FOREIGN KEY (`user_id`) REFERENCES `cod_user` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;

-- 4. تحديثات الجداول الموجودة
-- =====================================================

-- إضافة حقول جديدة لجدول المخزون الأساسي
ALTER TABLE `cod_product_inventory` 
ADD COLUMN IF NOT EXISTS `virtual_quantity_available` decimal(15,4) NOT NULL DEFAULT '0.0000' AFTER `quantity`,
ADD COLUMN IF NOT EXISTS `reserved_quantity` decimal(15,4) NOT NULL DEFAULT '0.0000' AFTER `virtual_quantity_available`,
ADD COLUMN IF NOT EXISTS `last_wac_update` timestamp NULL DEFAULT NULL AFTER `average_cost`,
ADD COLUMN IF NOT EXISTS `wac_calculation_method` enum('standard','moving','weighted') NOT NULL DEFAULT 'weighted' AFTER `last_wac_update`;

-- إضافة فهارس محسنة للأداء
ALTER TABLE `cod_product_inventory` 
ADD INDEX IF NOT EXISTS `idx_virtual_quantity` (`virtual_quantity_available`),
ADD INDEX IF NOT EXISTS `idx_reserved_quantity` (`reserved_quantity`),
ADD INDEX IF NOT EXISTS `idx_wac_update` (`last_wac_update`);

-- تحديث جدول المنتجات لدعم الباقات
ALTER TABLE `cod_product` 
ADD COLUMN IF NOT EXISTS `is_bundle` tinyint(1) NOT NULL DEFAULT '0' AFTER `status`,
ADD COLUMN IF NOT EXISTS `bundle_type` enum('simple','configurable','grouped') DEFAULT NULL AFTER `is_bundle`,
ADD COLUMN IF NOT EXISTS `allow_virtual_inventory` tinyint(1) NOT NULL DEFAULT '1' AFTER `bundle_type`;

-- 5. البيانات الأولية
-- =====================================================

-- إدراج الأدوار الأساسية
INSERT IGNORE INTO `cod_user_group` (`user_group_id`, `name`, `permission`) VALUES
(10, 'Warehouse Manager', '{"inventory": {"physical": ["view", "edit", "approve"]}}'),
(11, 'E-commerce Manager', '{"inventory": {"virtual": ["view", "edit", "approve"]}, "catalog": ["view", "edit"]}'),
(12, 'Branch Manager', '{"inventory": {"branch_specific": ["view", "edit"]}, "pos": ["view", "edit"]}'),
(13, 'Store Staff', '{"orders": ["view", "edit"], "customers": ["view", "edit"]}'),
(14, 'Cashier', '{"pos": ["view", "limited_edit"]}}');

-- إدراج إعدادات افتراضية لمدير المتجر
INSERT IGNORE INTO `cod_ecommerce_manager_settings` (`user_id`, `setting_key`, `setting_value`, `setting_type`) VALUES
(1, 'allow_oversell', '0', 'boolean'),
(1, 'oversell_limit_percentage', '10.00', 'decimal'),
(1, 'auto_reserve_inventory', '1', 'boolean'),
(1, 'reservation_timeout_minutes', '30', 'integer'),
(1, 'bundle_discount_stacking', '0', 'boolean');

-- 6. إجراءات مخزنة للعمليات المعقدة
-- =====================================================

DELIMITER //

-- إجراء حساب المخزون المتاح للبيع
CREATE PROCEDURE IF NOT EXISTS `sp_calculate_available_quantity`(
    IN p_product_id INT,
    IN p_unit_id INT,
    OUT p_available_quantity DECIMAL(15,4)
)
BEGIN
    DECLARE v_physical_qty DECIMAL(15,4) DEFAULT 0;
    DECLARE v_virtual_qty DECIMAL(15,4) DEFAULT 0;
    DECLARE v_reserved_qty DECIMAL(15,4) DEFAULT 0;
    DECLARE v_convertible_qty DECIMAL(15,4) DEFAULT 0;
    
    -- الحصول على الكمية الفعلية
    SELECT COALESCE(SUM(quantity), 0) INTO v_physical_qty
    FROM cod_product_inventory 
    WHERE product_id = p_product_id AND unit_id = p_unit_id;
    
    -- الحصول على الكمية الوهمية
    SELECT COALESCE(quantity_available, 0) INTO v_virtual_qty
    FROM cod_virtual_inventory 
    WHERE product_id = p_product_id AND unit_id = p_unit_id;
    
    -- الحصول على الكمية المحجوزة
    SELECT COALESCE(SUM(quantity_reserved), 0) INTO v_reserved_qty
    FROM cod_virtual_inventory_reservations 
    WHERE product_id = p_product_id AND unit_id = p_unit_id 
    AND expires_at > NOW();
    
    -- حساب الكمية القابلة للتحويل من الوحدات الأخرى
    -- (هذا يحتاج تطوير أكثر تعقيداً)
    SET v_convertible_qty = 0;
    
    -- الكمية المتاحة = الوهمية + القابلة للتحويل - المحجوزة
    SET p_available_quantity = GREATEST(0, v_virtual_qty + v_convertible_qty - v_reserved_qty);
END //

-- إجراء تحديث WAC
CREATE PROCEDURE IF NOT EXISTS `sp_update_wac`(
    IN p_product_id INT,
    IN p_unit_id INT,
    IN p_new_quantity DECIMAL(15,4),
    IN p_new_cost DECIMAL(15,4),
    IN p_movement_type ENUM('receipt', 'issue')
)
BEGIN
    DECLARE v_current_qty DECIMAL(15,4) DEFAULT 0;
    DECLARE v_current_cost DECIMAL(15,4) DEFAULT 0;
    DECLARE v_new_wac DECIMAL(15,4) DEFAULT 0;
    
    -- الحصول على الكمية والتكلفة الحالية
    SELECT quantity, average_cost INTO v_current_qty, v_current_cost
    FROM cod_product_inventory 
    WHERE product_id = p_product_id AND unit_id = p_unit_id;
    
    IF p_movement_type = 'receipt' THEN
        -- حساب WAC الجديد عند الاستلام
        IF (v_current_qty + p_new_quantity) > 0 THEN
            SET v_new_wac = ((v_current_qty * v_current_cost) + (p_new_quantity * p_new_cost)) / (v_current_qty + p_new_quantity);
        ELSE
            SET v_new_wac = p_new_cost;
        END IF;
        
        -- تحديث المخزون
        UPDATE cod_product_inventory 
        SET quantity = quantity + p_new_quantity,
            average_cost = v_new_wac,
            last_wac_update = NOW()
        WHERE product_id = p_product_id AND unit_id = p_unit_id;
        
    ELSEIF p_movement_type = 'issue' THEN
        -- عند الصرف، WAC يبقى كما هو
        UPDATE cod_product_inventory 
        SET quantity = GREATEST(0, quantity - p_new_quantity)
        WHERE product_id = p_product_id AND unit_id = p_unit_id;
    END IF;
END //

DELIMITER ;

-- 7. مشاهد (Views) للاستعلامات المعقدة
-- =====================================================

-- مشهد المخزون الشامل
CREATE OR REPLACE VIEW `v_comprehensive_inventory` AS
SELECT 
    p.product_id,
    p.name as product_name,
    p.model,
    pi.branch_id,
    b.name as branch_name,
    pi.unit_id,
    u.desc_en as unit_name,
    pi.quantity as physical_quantity,
    COALESCE(vi.quantity_available, 0) as virtual_quantity,
    COALESCE(vi.quantity_reserved, 0) as reserved_quantity,
    pi.average_cost,
    (pi.quantity * pi.average_cost) as inventory_value,
    CASE 
        WHEN pi.quantity <= 0 THEN 'out_of_stock'
        WHEN pi.quantity <= p.minimum THEN 'low_stock'
        WHEN pi.quantity >= p.maximum THEN 'overstock'
        ELSE 'in_stock'
    END as stock_status
FROM cod_product p
LEFT JOIN cod_product_inventory pi ON p.product_id = pi.product_id
LEFT JOIN cod_virtual_inventory vi ON p.product_id = vi.product_id AND pi.unit_id = vi.unit_id
LEFT JOIN cod_branch b ON pi.branch_id = b.branch_id
LEFT JOIN cod_unit u ON pi.unit_id = u.unit_id
WHERE p.status = 1;

-- مشهد الباقات النشطة
CREATE OR REPLACE VIEW `v_active_bundles` AS
SELECT 
    pb.bundle_id,
    pb.bundle_name,
    pb.bundle_type,
    pb.discount_type,
    pb.discount_value,
    COUNT(bi.bundle_item_id) as item_count,
    SUM(CASE WHEN bi.is_required = 1 THEN 1 ELSE 0 END) as required_items,
    pb.start_date,
    pb.end_date
FROM cod_product_bundles pb
LEFT JOIN cod_bundle_items bi ON pb.bundle_id = bi.bundle_id
WHERE pb.is_active = 1 
AND (pb.start_date IS NULL OR pb.start_date <= CURDATE())
AND (pb.end_date IS NULL OR pb.end_date >= CURDATE())
GROUP BY pb.bundle_id;

-- 8. مشغلات (Triggers) للتحديث التلقائي
-- =====================================================

-- مشغل تحديث المخزون الوهمي عند تغيير المخزون الفعلي
DELIMITER //

CREATE TRIGGER IF NOT EXISTS `tr_update_virtual_inventory_after_physical_change`
AFTER UPDATE ON `cod_product_inventory`
FOR EACH ROW
BEGIN
    -- تحديث المخزون الوهمي بناءً على التغيير في المخزون الفعلي
    IF OLD.quantity != NEW.quantity THEN
        UPDATE cod_virtual_inventory 
        SET quantity_available = GREATEST(0, quantity_available + (NEW.quantity - OLD.quantity))
        WHERE product_id = NEW.product_id AND unit_id = NEW.unit_id;
        
        -- تسجيل في سجل التغييرات
        INSERT INTO cod_inventory_change_log (
            product_id, unit_id, branch_id, 
            change_type, old_quantity, new_quantity, 
            change_reason, changed_by, change_date
        ) VALUES (
            NEW.product_id, NEW.unit_id, NEW.branch_id,
            'physical_inventory_update', OLD.quantity, NEW.quantity,
            'Automatic update from physical inventory change', 
            @current_user_id, NOW()
        );
    END IF;
END //

DELIMITER ;

-- 9. فهارس إضافية للأداء
-- =====================================================

-- فهارس مركبة للاستعلامات المعقدة
CREATE INDEX IF NOT EXISTS `idx_product_branch_unit` ON `cod_product_inventory` (`product_id`, `branch_id`, `unit_id`);
CREATE INDEX IF NOT EXISTS `idx_virtual_product_unit` ON `cod_virtual_inventory` (`product_id`, `unit_id`);
CREATE INDEX IF NOT EXISTS `idx_bundle_active_dates` ON `cod_product_bundles` (`is_active`, `start_date`, `end_date`);
CREATE INDEX IF NOT EXISTS `idx_reservation_expires` ON `cod_virtual_inventory_reservations` (`expires_at`);

-- فهارس للبحث النصي
CREATE FULLTEXT INDEX IF NOT EXISTS `idx_product_search` ON `cod_product` (`name`, `description`, `model`);
CREATE FULLTEXT INDEX IF NOT EXISTS `idx_bundle_search` ON `cod_product_bundles` (`bundle_name`, `bundle_description`);

-- 10. إعدادات الأداء والتحسين
-- =====================================================

-- تحسين إعدادات الجداول
ALTER TABLE `cod_product_inventory` ENGINE=InnoDB ROW_FORMAT=COMPRESSED;
ALTER TABLE `cod_virtual_inventory` ENGINE=InnoDB ROW_FORMAT=COMPRESSED;
ALTER TABLE `cod_product_bundles` ENGINE=InnoDB ROW_FORMAT=COMPRESSED;

-- إعدادات التخزين المؤقت
SET GLOBAL query_cache_size = 268435456; -- 256MB
SET GLOBAL query_cache_type = ON;

-- =====================================================
-- نهاية ملف التحديثات
-- =====================================================

-- تسجيل إكمال التحديثات
INSERT INTO cod_system_updates (
    update_name, 
    update_description, 
    update_version, 
    update_date, 
    update_status
) VALUES (
    'Inventory E-commerce Integration', 
    'Database updates for virtual/physical inventory separation and advanced bundles system',
    '1.0.0',
    NOW(),
    'completed'
);

COMMIT;
```

---

## 📋 **تعليمات تطبيق ملف SQL**

### **🔧 خطوات التطبيق:**

1. **📂 إنشاء نسخة احتياطية:**
   ```bash
   mysqldump -u username -p database_name > backup_before_inventory_update.sql
   ```

2. **🔍 مراجعة الملف:**
   - فحص التوافق مع الجداول الحالية
   - التأكد من عدم تضارب الأسماء
   - مراجعة المفاتيح الخارجية

3. **⚡ تطبيق التحديثات:**
   ```bash
   mysql -u username -p database_name < inventory-ecommerce-database-updates.sql
   ```

4. **✅ التحقق من النجاح:**
   ```sql
   SHOW TABLES LIKE 'cod_virtual_%';
   SHOW TABLES LIKE 'cod_bundle_%';
   SELECT * FROM cod_system_updates WHERE update_name = 'Inventory E-commerce Integration';
   ```

### **⚠️ احتياطات مهمة:**

1. **🔒 تطبيق في بيئة اختبار أولاً**
2. **📊 مراقبة الأداء بعد التطبيق**
3. **🔄 إعداد مهام صيانة دورية للجداول الجديدة**
4. **📝 توثيق أي تغييرات إضافية مطلوبة**

---

## 🎯 **الخلاصة النهائية**

هذه الخطة التنفيذية المحدثة تراعي:

### **✅ الفروق الجوهرية:**
- **المخزون الوهمي** (quantity_available) للمتجر الإلكتروني
- **المخزون الفعلي** (quantity) للمستودعات
- **الباقات المعقدة** للتجارة الإلكترونية
- **أدوار المستخدمين** المتخصصة

### **✅ التشابك الذكي:**
- **header.twig** المحسن للطلب السريع
- **ProductsPro** المطور للمنتجات المعقدة
- **نظام WAC** المتكامل مع المحاسبة
- **قاعدة البيانات** المحسنة للأداء

### **✅ الجودة Enterprise Grade:**
- **تكامل كامل** مع الخدمات المركزية
- **صلاحيات متقدمة** لكل دور
- **أداء محسن** للعمليات المعقدة
- **توثيق شامل** لجميع المكونات

**النتيجة:** أقوى نظام مخزون وتجارة إلكترونية متشابك في المنطقة! 🚀