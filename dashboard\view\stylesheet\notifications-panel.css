/**
 * AYM ERP - مركز الإشعارات المتطور
 * "عينك على النظام" - Notifications Panel CSS
 * 
 * @package    AYM ERP
 * <AUTHOR> Team
 * @copyright  2025 AYM ERP
 * @license    Commercial
 * @version    2.0.0
 */

/* ===================================================================
   أيقونة الإشعارات المحسنة
   =================================================================== */

.notification-bell {
  font-size: 18px;
  transition: all 0.3s ease;
  position: relative;
}

.notification-bell:hover {
  transform: rotate(15deg);
  color: #667eea;
}

.notification-badge {
  position: absolute;
  top: 3px;
  right: 3px;
  font-size: 10px;
  border-radius: 12px;
  padding: 2px 6px;
  min-width: 18px;
  text-align: center;
  background: linear-gradient(135deg, #ff6b6b, #ee5a52);
  color: white;
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(255, 107, 107, 0.3);
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-3px); }
  60% { transform: translateY(-2px); }
}

/* مؤشر الحالة الحرجة */
.critical-pulse {
  position: absolute;
  top: 1px;
  right: 1px;
  width: 10px;
  height: 10px;
  background: radial-gradient(circle, #ff4444, #cc0000);
  border-radius: 50%;
  animation: criticalPulse 1s infinite;
  box-shadow: 0 0 0 0 rgba(255, 68, 68, 0.7);
}

/* مؤشر حالة النظام */
.system-health-dot {
  position: absolute;
  top: 8px;
  left: 8px;
  width: 6px;
  height: 6px;
  background: #28a745;
  border-radius: 50%;
  animation: healthPulse 2s infinite;
}

@keyframes criticalPulse {
  0% { 
    transform: scale(1); 
    box-shadow: 0 0 0 0 rgba(255, 68, 68, 0.7); 
  }
  70% { 
    transform: scale(1.1); 
    box-shadow: 0 0 0 8px rgba(255, 68, 68, 0); 
  }
  100% { 
    transform: scale(1); 
    box-shadow: 0 0 0 0 rgba(255, 68, 68, 0); 
  }
}

@keyframes healthPulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

/* ===================================================================
   البانل الرئيسي المحسن
   =================================================================== */

.unified-notifications-panel {
  width: 420px;
  max-height: 600px;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0,0,0,.12);
  background: #ffffff;
  overflow: hidden;
  position: absolute;
  top: 100%;
  z-index: 9999;
  margin-top: 8px;
}

/* ===================================================================
   هيدر البانل المحسن
   =================================================================== */

.panel-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.panel-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.header-left, .header-right {
  display: flex;
  flex-direction: column;
  gap: 4px;
  position: relative;
  z-index: 1;
}

.header-title {
  font-size: 16px;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 2px;
}

.system-status {
  font-size: 12px;
  opacity: 0.9;
  display: flex;
  align-items: center;
  gap: 5px;
}

.header-stats {
  font-size: 14px;
  font-weight: 600;
  text-align: right;
}

.header-actions {
  display: flex;
  gap: 8px;
  margin-top: 4px;
}

.header-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  padding: 6px 8px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 12px;
}

.header-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

/* ===================================================================
   المؤشرات السريعة
   =================================================================== */

.quick-indicators {
  display: flex;
  padding: 12px 16px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #dee2e6;
  gap: 8px;
}

.indicator-item {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 10px;
  background: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.indicator-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.indicator-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: white;
}

.indicator-item[data-type="performance"] .indicator-icon {
  background: linear-gradient(135deg, #28a745, #20c997);
}

.indicator-item[data-type="users"] .indicator-icon {
  background: linear-gradient(135deg, #007bff, #6610f2);
}

.indicator-item[data-type="sales"] .indicator-icon {
  background: linear-gradient(135deg, #ffc107, #fd7e14);
}

.indicator-item[data-type="tasks"] .indicator-icon {
  background: linear-gradient(135deg, #dc3545, #e83e8c);
}

.indicator-info {
  display: flex;
  flex-direction: column;
}

.indicator-value {
  font-size: 16px;
  font-weight: 700;
  color: #333;
  line-height: 1;
}

.indicator-label {
  font-size: 11px;
  color: #666;
  margin-top: 2px;
}

/* ===================================================================
   التبويبات المحسنة
   =================================================================== */

.panel-tabs {
  background: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  padding: 0 8px;
}

.panel-tabs .nav-tabs {
  border: none;
  margin: 0;
  display: flex;
  flex-wrap: wrap;
}

.panel-tabs .nav-tabs > li {
  margin-bottom: 0;
  flex: 1;
  min-width: 0;
}

.panel-tabs .nav-tabs > li > a {
  border: none;
  border-radius: 8px 8px 0 0;
  padding: 10px 8px;
  color: #666;
  font-size: 12px;
  font-weight: 600;
  transition: all 0.3s ease;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  position: relative;
  overflow: hidden;
}

.panel-tabs .nav-tabs > li > a .tab-text {
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.panel-tabs .nav-tabs > li > a:hover {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  color: #667eea;
  transform: translateY(-2px);
}

.panel-tabs .nav-tabs > li.active > a {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.panel-tabs .nav-tabs > li.active > a::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #5a6fd8, #6a4c93);
}

/* شارات العدد في التبويبات */
.tab-badge {
  font-size: 9px;
  padding: 2px 5px;
  border-radius: 12px;
  margin-top: 2px;
  min-width: 16px;
  text-align: center;
  display: inline-block;
  font-weight: 700;
  box-shadow: 0 1px 3px rgba(0,0,0,0.2);
}

.tab-badge:not(.critical):not(.warning):not(.info):not(.danger) {
  background: linear-gradient(135deg, #dc3545, #c82333);
  color: white;
}

.tab-badge.critical {
  background: linear-gradient(135deg, #ff4444, #cc0000);
  color: white;
  animation: badgePulse 2s infinite;
}

.tab-badge.warning {
  background: linear-gradient(135deg, #ffc107, #e0a800);
  color: #212529;
}

.tab-badge.info {
  background: linear-gradient(135deg, #17a2b8, #138496);
  color: white;
}

.tab-badge.danger {
  background: linear-gradient(135deg, #dc3545, #c82333);
  color: white;
}

@keyframes badgePulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

/* ===================================================================
   محتوى التبويبات
   =================================================================== */

.tab-header {
  padding: 12px 20px;
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-bottom: 1px solid #dee2e6;
}

.tab-header h6 {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 8px;
}

.tab-header .tab-description {
  margin: 0;
  font-size: 12px;
  color: #6c757d;
  font-style: italic;
}

/* حالة فارغة */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #6c757d;
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-state p {
  margin: 0;
  font-size: 14px;
}

/* ===================================================================
   فوتر البانل المحسن
   =================================================================== */

.panel-footer {
  background: linear-gradient(135deg, #f8f9fa, #e9ecef);
  border-top: 1px solid #dee2e6;
  padding: 16px 20px;
}

.footer-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-size: 11px;
  color: #6c757d;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.footer-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.action-group {
  display: flex;
  gap: 8px;
}

.footer-btn {
  background: white;
  border: 1px solid #dee2e6;
  color: #495057;
  padding: 6px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.footer-btn:hover {
  background: #f8f9fa;
  border-color: #adb5bd;
  transform: translateY(-1px);
}

.footer-btn.primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-color: #667eea;
}

.footer-btn.primary:hover {
  background: linear-gradient(135deg, #5a6fd8, #6a4c93);
  transform: translateY(-1px);
}

/* ===================================================================
   التحكم السريع
   =================================================================== */

.quick-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 12px;
  border-top: 1px solid #dee2e6;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.control-label {
  font-size: 11px;
  color: #6c757d;
  font-weight: 500;
}

/* مفتاح التبديل */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 20px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-label {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 20px;
}

.toggle-label:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .toggle-label {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

input:checked + .toggle-label:before {
  transform: translateX(20px);
}

/* ===================================================================
   دعم RTL/LTR المحسن - إصلاح المشاكل
   =================================================================== */

/* الموضع الأساسي للبانل */
.unified-notifications-panel {
  position: absolute !important;
  top: 100% !important;
  z-index: 9999 !important;
  margin-top: 8px !important;
}

/* عرض البانل في الوسط - إصلاح شامل */
.unified-notifications-panel {
  left: 50% !important;
  right: auto !important;
  transform: translateX(-50%) !important;
  position: fixed !important;
  top: 60px !important;
  z-index: 10000 !important;
}

/* توحيد العرض للـ RTL/LTR */
[dir="rtl"] .unified-notifications-panel,
[dir="ltr"] .unified-notifications-panel {
  left: 50% !important;
  right: auto !important;
  transform: translateX(-50%) !important;
}

/* إصلاح للعناصر الفرعية في RTL */
[dir="rtl"] .notification-badge {
  right: auto;
  left: 3px;
}

[dir="rtl"] .critical-pulse {
  right: auto;
  left: 1px;
}

[dir="rtl"] .system-health-dot {
  left: auto;
  right: 8px;
}

/* ===================================================================
   تحسينات الموبايل
   =================================================================== */

@media (max-width: 768px) {
  .unified-notifications-panel {
    width: 95vw;
    max-width: 95vw;
    max-height: 70vh;
    left: 2.5vw !important;
    right: 2.5vw !important;
    position: fixed !important;
    top: 60px !important;
    margin-top: 8px;
    border-radius: 8px;
  }

  [dir="rtl"] .unified-notifications-panel,
  [dir="ltr"] .unified-notifications-panel {
    left: 2.5vw !important;
    right: 2.5vw !important;
    transform: none !important;
  }
  
  .quick-indicators {
    flex-wrap: wrap;
    gap: 6px;
  }
  
  .indicator-item {
    flex: 1 1 calc(50% - 3px);
    min-width: 0;
  }
  
  .panel-tabs .nav-tabs > li > a {
    padding: 8px 4px;
    font-size: 10px;
  }
  
  .panel-tabs .nav-tabs > li > a .tab-text {
    display: none;
  }
  
  .footer-actions {
    flex-direction: column;
    gap: 8px;
  }
  
  .action-group {
    width: 100%;
    justify-content: center;
  }
  
  .quick-controls {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }
  
  .control-group {
    justify-content: space-between;
  }
}
