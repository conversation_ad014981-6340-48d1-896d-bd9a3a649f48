<?php
/**
 * English Language File - Two Factor Authentication Verification
 * 
 * @package    AYM ERP
 * <AUTHOR> Team
 * @copyright  2025 AYM ERP
 * @license    Commercial
 * @version    1.0.0
 */

// Main heading
$_['heading_title'] = 'Identity Verification';

// General texts
$_['text_verification'] = 'Please enter the verification code to complete login';
$_['text_totp'] = 'Authenticator App';
$_['text_sms'] = 'Text Message';
$_['text_email'] = 'Email';
$_['text_backup'] = 'Backup Code';
$_['text_trust_device'] = 'Trust this device for 30 days';

// Input fields
$_['entry_code'] = 'Verification Code:';
$_['entry_backup_code'] = 'Backup Code:';

// Buttons
$_['button_verify'] = 'Verify';
$_['button_send_sms'] = 'Send SMS';
$_['button_send_email'] = 'Send Email';
$_['button_cancel'] = 'Cancel';

// Success messages
$_['success_sms_sent'] = 'Verification code has been sent to your phone number successfully';
$_['success_email_sent'] = 'Verification code has been sent to your email address successfully';

// Error messages
$_['error_session'] = 'Session expired, please login again';
$_['error_method_required'] = 'Please select a verification method';
$_['error_method_invalid'] = 'Invalid verification method';
$_['error_totp_required'] = 'Please enter the verification code from your authenticator app';
$_['error_totp_invalid'] = 'Invalid or expired verification code';
$_['error_sms_required'] = 'Please enter the verification code sent via SMS';
$_['error_sms_invalid'] = 'Invalid or expired verification code';
$_['error_sms_failed'] = 'Failed to send SMS, please try again';
$_['error_email_required'] = 'Please enter the verification code sent via email';
$_['error_email_invalid'] = 'Invalid or expired verification code';
$_['error_email_failed'] = 'Failed to send email, please try again';
$_['error_backup_required'] = 'Please enter a backup code';
$_['error_backup_invalid'] = 'Invalid or already used backup code';
$_['error_rate_limit'] = 'Maximum attempts exceeded, please try again later';

// Help messages
$_['help_totp'] = 'Use an authenticator app like Google Authenticator or Authy to generate the verification code';
$_['help_sms'] = 'A verification code will be sent to your registered phone number';
$_['help_email'] = 'A verification code will be sent to your registered email address';
$_['help_backup'] = 'Use one of the backup codes you saved when enabling two-factor authentication';
$_['help_trust_device'] = 'When enabled, you won\'t be asked for verification codes on this device for 30 days';

// Security messages
$_['security_warning'] = 'Security Warning: Never share your verification codes with anyone';
$_['security_info'] = 'Two-factor authentication protects your account from unauthorized access';
?>
