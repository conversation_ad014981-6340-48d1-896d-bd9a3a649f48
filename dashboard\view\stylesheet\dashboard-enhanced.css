/* AYM ERP Dashboard Enhanced Styles - دقة 100% */

/* Main Dashboard Styles */
.huge {
    font-size: 40px;
    font-weight: bold;
}

.panel-primary .huge {
    color: #fff;
}

.panel-success .huge {
    color: #fff;
}

.panel-warning .huge {
    color: #fff;
}

.panel-info .huge {
    color: #fff;
}

/* Progress Bars */
.progress-mini {
    height: 10px;
    margin-bottom: 0;
}

.progress-mini .progress-bar {
    line-height: 10px;
}

/* Panel Enhancements */
.panel-heading {
    border-bottom: 1px solid rgba(255,255,255,0.2);
}

.panel-footer {
    background-color: rgba(255,255,255,0.1);
    border-top: 1px solid rgba(255,255,255,0.2);
}

.panel-primary .panel-footer {
    color: #fff;
}

.panel-success .panel-footer {
    color: #fff;
}

.panel-warning .panel-footer {
    color: #fff;
}

.panel-info .panel-footer {
    color: #fff;
}

/* Table Enhancements */
.table-hover tbody tr:hover {
    background-color: #f5f5f5;
}

.table th {
    background-color: #f8f9fa;
    font-weight: bold;
    border-bottom: 2px solid #dee2e6;
}

/* Badge Styles */
.badge {
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 12px;
}

/* Filter Panel */
.panel-default .panel-heading {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.form-group label {
    font-weight: bold;
    color: #495057;
}

/* Button Enhancements */
.btn-group-justified .btn {
    border-radius: 0;
}

.btn-group-justified .btn:first-child {
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
}

.btn-group-justified .btn:last-child {
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
}

/* Responsive Enhancements */
@media (max-width: 768px) {
    .huge {
        font-size: 24px;
    }
    
    .col-xs-3 i {
        font-size: 2em !important;
    }
    
    .panel-footer .row {
        text-align: center;
    }
    
    .table-responsive {
        font-size: 12px;
    }
}

/* Animation Effects */
.panel {
    transition: all 0.3s ease;
}

.panel:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.btn {
    transition: all 0.2s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

/* Color Scheme */
.text-success {
    color: #28a745 !important;
}

.text-warning {
    color: #ffc107 !important;
}

.text-danger {
    color: #dc3545 !important;
}

.text-info {
    color: #17a2b8 !important;
}

/* Custom Progress Colors */
.progress-bar-success {
    background-color: #28a745;
}

.progress-bar-warning {
    background-color: #ffc107;
}

.progress-bar-danger {
    background-color: #dc3545;
}

.progress-bar-info {
    background-color: #17a2b8;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 1000;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Real-time Updates */
.updating {
    background-color: #fff3cd;
    border-color: #ffeaa7;
    transition: all 0.3s ease;
}

.updated {
    background-color: #d4edda;
    border-color: #c3e6cb;
    animation: pulse 0.5s ease-in-out;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); }
    100% { transform: scale(1); }
}

/* Notification Styles */
.alert {
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.alert-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.alert-danger {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

/* Auto-refresh indicator */
.refresh-indicator {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: #007bff;
    color: white;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 12px;
    z-index: 1000;
    opacity: 0.8;
}

.refresh-indicator.active {
    background: #28a745;
    animation: blink 1s infinite;
}

@keyframes blink {
    0%, 50% { opacity: 0.8; }
    51%, 100% { opacity: 0.4; }
}

/* Print Styles */
@media print {
    .page-header,
    .panel-heading .btn,
    .btn-group-justified {
        display: none !important;
    }
    
    .panel {
        border: 1px solid #000 !important;
        break-inside: avoid;
    }
    
    .huge {
        font-size: 24px !important;
    }
}

/* RTL Support */
[dir="rtl"] .pull-right {
    float: left !important;
}

[dir="rtl"] .pull-left {
    float: right !important;
}

[dir="rtl"] .text-right {
    text-align: left !important;
}

[dir="rtl"] .text-left {
    text-align: right !important;
}

/* Dashboard Specific */
.dashboard-container {
    padding: 15px 0;
}

.metric-card {
    margin-bottom: 20px;
    padding: 15px;
    text-align: center;
    border-radius: 8px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
}

.metric-value {
    font-size: 2.5em;
    font-weight: bold;
    line-height: 1;
    margin-bottom: 5px;
}

.metric-label {
    font-size: 0.9em;
    color: #6c757d;
    margin-top: 5px;
    font-weight: 500;
}

/* E-commerce Metrics */
.metric-card .metric-value.text-primary {
    color: #007bff !important;
}

.metric-card .metric-value.text-success {
    color: #28a745 !important;
}

.metric-card .metric-value.text-warning {
    color: #ffc107 !important;
}

.metric-card .metric-value.text-info {
    color: #17a2b8 !important;
}

.metric-card .metric-value.text-danger {
    color: #dc3545 !important;
}

.metric-change {
    font-size: 0.8em;
    margin-top: 5px;
}

.metric-change.positive {
    color: #28a745;
}

.metric-change.negative {
    color: #dc3545;
}

/* Status Indicators */
.status-indicator {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 5px;
}

.status-indicator.success {
    background-color: #28a745;
}

.status-indicator.warning {
    background-color: #ffc107;
}

.status-indicator.danger {
    background-color: #dc3545;
}

.status-indicator.info {
    background-color: #17a2b8;
}

/* Tooltip Enhancements */
.tooltip-inner {
    background-color: #333;
    color: #fff;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 12px;
}

/* Custom Scrollbar */
.table-responsive::-webkit-scrollbar {
    height: 8px;
}

.table-responsive::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
    background: #555;
}
