{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-bar-chart"></i> {{ text_financial_report }}</h3>
      </div>
      <div class="panel-body">
        <table class="table table-bordered">
          <tr>
            <td>{{ text_partner }}:</td>
            <td>{{ partner_name }}</td>
          </tr>
          <tr>
            <td>{{ text_type }}:</td>
            <td>{{ partner_type }}</td>
          </tr>
          <tr>
            <td>{{ text_percentage }}:</td>
<td>{{ partner_percentage }}%</td>
          </tr>
          <tr>
            <td>{{ text_profit_percentage }}:</td>
            <td>{{ partner_profit_percentage }}%</td>
          </tr>
          <tr>
            <td>{{ text_initial_investment }}:</td>
            <td>{{ partner_initial_investment }}</td>
          </tr>
          <tr>
            <td>{{ text_current_balance }}:</td>
            <td>{{ partner_current_balance }}</td>
          </tr>
        </table>
        
        <h2>{{ text_transactions }}</h2>
        <table class="table table-bordered table-hover">
          <thead>
            <tr>
              <td class="text-left">{{ column_date }}</td>
              <td class="text-left">{{ column_description }}</td>
              <td class="text-right">{{ column_amount }}</td>
              <td class="text-right">{{ column_balance }}</td>
            </tr>
          </thead>
          <tbody>
            {% if transactions %}
              {% for transaction in transactions %}
                <tr>
                  <td class="text-left">{{ transaction.date }}</td>
                  <td class="text-left">{{ transaction.description }}</td>
                  <td class="text-right">{{ transaction.amount }}</td>
                  <td class="text-right">{{ transaction.balance }}</td>
                </tr>
              {% endfor %}
            {% else %}
              <tr>
                <td class="text-center" colspan="4">{{ text_no_results }}</td>
              </tr>
            {% endif %}
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>
{{ footer }}            