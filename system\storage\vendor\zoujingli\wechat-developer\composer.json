{"type": "library", "name": "zoujingli/wechat-developer", "homepage": "https://thinkadmin.top", "description": "WeChat and Alipay Platform Development", "license": "MIT", "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://thinkadmin.top"}], "keywords": ["AliPay", "WeMini", "WeChat", "WeChatPay", "WeChatDeveloper"], "require": {"php": ">=5.4", "ext-xml": "*", "ext-json": "*", "ext-curl": "*", "ext-bcmath": "*", "ext-libxml": "*", "ext-openssl": "*", "ext-mbstring": "*", "ext-simplexml": "*"}, "autoload": {"classmap": ["We.php"], "psr-4": {"WePay\\": "WePay", "WeChat\\": "WeChat", "WeMini\\": "WeMini", "AliPay\\": "AliPay", "WePayV3\\": "WePayV3"}}}