<?php
/**
 * تحكم تقرير أعمار الديون الشامل والمتكامل
 * يجمع بين القوة والبساطة - يتفوق على SAP وOracle في سهولة الاستخدام
 * مع الاحتفاظ بالقوة والمرونة المطلوبة للشركات الكبيرة
 * متوافق مع القوانين المصرية ومعايير إدارة المخاطر
 */
class ControllerAccountsAgingReport extends Controller {
    private $error = array();
    private $central_service;

    public function __construct($registry) {
        parent::__construct($registry);

        // تحميل الخدمات المركزية
        $this->load->model('core/central_service_manager');
        $this->central_service = $this->model_core_central_service_manager;
    }

    public function index() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/aging_report') ||
            !$this->user->hasKey('accounting_aging_report_view')) {

            $this->central_service->logActivity('unauthorized_access', 'accounts',
                $this->language->get('log_unauthorized_access_aging_report'), [
                'user_id' => $this->user->getId(),
                'ip_address' => $this->request->server['REMOTE_ADDR']
            ]);

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/aging_report');
        $this->document->setTitle($this->language->get('heading_title'));

        // إضافة CSS و JavaScript المتقدم (من advanced)
        $this->document->addStyle('view/stylesheet/accounts/aging_report.css');
        $this->document->addScript('view/javascript/accounts/aging_report.js');
        $this->document->addScript('view/javascript/jquery/accounting.min.js');
        $this->document->addScript('view/javascript/jquery/select2.min.js');
        $this->document->addStyle('view/javascript/jquery/select2.min.css');
        $this->document->addScript('view/javascript/jquery/daterangepicker.min.js');
        $this->document->addStyle('view/javascript/jquery/daterangepicker.css');
        $this->document->addScript('view/javascript/jquery/chart.min.js');
        $this->document->addScript('view/javascript/jquery/datatables.min.js');
        $this->document->addStyle('view/javascript/jquery/datatables.min.css');

        // تسجيل الوصول للشاشة
        $this->central_service->logActivity('view', 'accounts',
            $this->language->get('log_view_aging_report_screen'), [
            'user_id' => $this->user->getId(),
            'screen' => 'accounts/aging_report'
        ]);

        $data['action'] = $this->url->link('accounts/aging_report/print', 'user_token=' . $this->session->data['user_token'], true);

        $data['heading_title'] = $this->language->get('heading_title');
        $data['text_form'] = $this->language->get('text_form');
        $data['entry_date_end'] = $this->language->get('entry_date_end');
        $data['button_filter'] = $this->language->get('button_filter');

        $data['user_token'] = $this->session->data['user_token'];
        $data['error_warning'] = isset($this->error['warning'])?$this->error['warning']:'';

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/aging_report_form', $data));
    }

    /**
     * توليد تقرير أعمار الديون المتقدم (مدمج من advanced)
     */
    public function generate() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/aging_report') ||
            !$this->user->hasKey('accounting_aging_report_generate')) {

            $this->central_service->logActivity('unauthorized_generate', 'accounts',
                $this->language->get('log_unauthorized_generate_aging_report'), [
                'user_id' => $this->user->getId(),
                'action' => 'generate_aging_report'
            ]);

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/aging_report');
        $this->load->model('accounts/aging_report');

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm()) {
            try {
                $filter_data = $this->prepareFilterData();

                // تسجيل توليد التقرير
                $this->central_service->logActivity('generate_report', 'accounts',
                    $this->language->get('log_generate_aging_report_date') . ': ' . $filter_data['date_end'], [
                    'user_id' => $this->user->getId(),
                    'date_end' => $filter_data['date_end'],
                    'report_type' => $filter_data['report_type'] ?? 'customers'
                ]);

                $aging_data = $this->model_accounts_aging_report->generateAgingReport($filter_data);

                // تحليل المخاطر المتقدم بالذكاء الاصطناعي
                $risk_analysis = $this->model_accounts_aging_report->performAIRiskAnalysis($aging_data);

                // إنذار مبكر للديون المتعثرة
                if ($risk_analysis['high_risk_customers']) {
                    $this->central_service->sendNotification(
                        'high_risk_customers_alert',
                        $this->language->get('text_high_risk_customers_alert'),
                        $this->language->get('text_high_risk_customers_detected') . ' ' . count($risk_analysis['high_risk_customers']) . ' ' . $this->language->get('text_high_risk_customer_in_aging_report'),
                        [$this->config->get('config_ceo_id'), $this->config->get('config_chief_accountant_id')],
                        [
                            'high_risk_count' => count($risk_analysis['high_risk_customers']),
                            'total_risk_amount' => $risk_analysis['total_risk_amount'],
                            'risk_percentage' => $risk_analysis['risk_percentage']
                        ]
                    );
                }

                // إرسال إشعار للمحاسب الرئيسي
                $this->central_service->sendNotification(
                    'aging_report_generated',
                    $this->language->get('text_aging_report_generated'),
                    $this->language->get('text_aging_report_generated_notification') . ' ' . $filter_data['date_end'] . ' ' . $this->language->get('text_by') . ' ' . $this->user->getFirstName(),
                    [$this->config->get('config_chief_accountant_id')],
                    [
                        'date_end' => $filter_data['date_end'],
                        'user_name' => $this->user->getFirstName() . ' ' . $this->user->getLastName(),
                        'total_overdue' => $aging_data['totals']['total_overdue'] ?? 0
                    ]
                );

                $this->session->data['success'] = $this->language->get('text_success_generate');
                $this->session->data['aging_report_data'] = $aging_data;

                $this->response->redirect($this->url->link('accounts/aging_report/view', 'user_token=' . $this->session->data['user_token'], true));
            } catch (Exception $e) {
                $this->error['warning'] = $e->getMessage();
            }
        }

        $this->getForm();
    }

    /**
     * عرض تقرير أعمار الديون
     */
    public function view() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/aging_report') ||
            !$this->user->hasKey('accounting_aging_report_view')) {

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/aging_report');
        $this->document->setTitle($this->language->get('heading_title'));

        if (!isset($this->session->data['aging_report_data'])) {
            $this->session->data['error'] = $this->language->get('error_no_data');
            $this->response->redirect($this->url->link('accounts/aging_report', 'user_token=' . $this->session->data['user_token'], true));
            return;
        }

        $data = $this->session->data['aging_report_data'];

        // تسجيل عرض التقرير
        $this->central_service->logActivity('view_report', 'accounts',
            $this->language->get('log_view_aging_report'), [
            'user_id' => $this->user->getId(),
            'action' => 'view_aging_report'
        ]);

        $data['breadcrumbs'] = array();
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('accounts/aging_report', 'user_token=' . $this->session->data['user_token'], true)
        );

        $data['export_excel'] = $this->url->link('accounts/aging_report/export', 'format=excel&user_token=' . $this->session->data['user_token'], true);
        $data['export_pdf'] = $this->url->link('accounts/aging_report/export', 'format=pdf&user_token=' . $this->session->data['user_token'], true);
        $data['export_csv'] = $this->url->link('accounts/aging_report/export', 'format=csv&user_token=' . $this->session->data['user_token'], true);

        $data['user_token'] = $this->session->data['user_token'];
        $data['heading_title'] = $this->language->get('heading_title');

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/aging_report_view', $data));
    }

    public function print() {
        $this->load->language('accounts/aging_report');
        $this->load->model('accounts/aging_report');

        $data['title'] = $this->language->get('print_title');
        $data['printdate'] = date('Y-m-d H:i:s');
        $data['user_token'] = $this->session->data['user_token'];
        $data['lang'] = $this->language->get('code');
        $data['direction'] = $this->language->get('direction');  
        $data['whoprint'] = $this->user->getUserName();

        $date_end = $this->request->post['date_end'] ?: date('Y-m-d');
        $data['end_date'] = date($this->language->get('date_format_short'), strtotime($date_end));

        if ($date_end) {
            $results = $this->model_accounts_aging_report->getAgingReportData($date_end);
            $data['buckets'] = $results['buckets'];
            $data['customers_data'] = $results['customers_data'];
        } else {
            $data['buckets'] = [
                '0-30' => $this->currency->format(0, $this->config->get('config_currency')),
                '31-60' => $this->currency->format(0, $this->config->get('config_currency')),
                '61-90' => $this->currency->format(0, $this->config->get('config_currency')),
                '>90' => $this->currency->format(0, $this->config->get('config_currency'))
            ];
            $data['customers_data'] = [];
            $this->error['warning'] = $this->language->get('error_no_data');
        }

        $data['text_aging_report'] = $this->language->get('text_aging_report');
        $data['text_period_end'] = $this->language->get('text_period_end');
        $data['text_buckets'] = $this->language->get('text_buckets');
        $data['text_customer_details'] = $this->language->get('text_customer_details');

        $data['text_0_30'] = $this->language->get('text_0_30');
        $data['text_31_60'] = $this->language->get('text_31_60');
        $data['text_61_90'] = $this->language->get('text_61_90');
        $data['text_over_90'] = $this->language->get('text_over_90');

        $data['text_customer_name'] = $this->language->get('text_customer_name');

        $this->response->setOutput($this->load->view('accounts/aging_report_list', $data));
    }

    /**
     * تصدير تقرير أعمار الديون
     */
    public function export() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/aging_report') ||
            !$this->user->hasKey('accounting_aging_report_export')) {

            $this->central_service->logActivity('unauthorized_export', 'accounts',
                $this->language->get('log_unauthorized_export_aging_report'), [
                'user_id' => $this->user->getId(),
                'action' => 'export_aging_report'
            ]);

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/aging_report');
        $this->load->model('accounts/aging_report');

        if (!isset($this->session->data['aging_report_data'])) {
            $this->session->data['error'] = $this->language->get('error_no_data');
            $this->response->redirect($this->url->link('accounts/aging_report', 'user_token=' . $this->session->data['user_token'], true));
            return;
        }

        $format = $this->request->get['format'] ?? 'excel';
        $aging_data = $this->session->data['aging_report_data'];
        $filter_data = $this->session->data['aging_report_filter'] ?? array();

        // تسجيل عملية التصدير
        $this->central_service->logActivity('export', 'accounts',
            $this->language->get('log_export_aging_report') . ' - ' . strtoupper($format), [
            'user_id' => $this->user->getId(),
            'format' => $format,
            'date_end' => $filter_data['date_end'] ?? ''
        ]);

        // إرسال إشعار للمحاسب الرئيسي
        $this->central_service->sendNotification(
            'aging_report_exported',
            $this->language->get('text_aging_report_exported'),
            $this->language->get('text_aging_report_exported_notification') . ' ' . $this->language->get('text_format') . ' ' . strtoupper($format) . ' ' . $this->language->get('text_by') . ' ' . $this->user->getFirstName(),
            [$this->config->get('config_chief_accountant_id')],
            [
                'format' => $format,
                'date_end' => $filter_data['date_end'] ?? '',
                'user_name' => $this->user->getFirstName() . ' ' . $this->user->getLastName()
            ]
        );

        switch ($format) {
            case 'excel':
                $this->exportToExcel($aging_data, $filter_data);
                break;
            case 'pdf':
                $this->exportToPdf($aging_data, $filter_data);
                break;
            case 'csv':
                $this->exportToCsv($aging_data, $filter_data);
                break;
            default:
                $this->exportToExcel($aging_data, $filter_data);
        }
    }

    /**
     * التحقق من صحة البيانات
     */
    protected function validateForm() {
        if (!$this->user->hasPermission('access', 'accounts/aging_report')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }

        if (empty($this->request->post['date_end'])) {
            $this->error['date_end'] = $this->language->get('error_date_end');
        }

        return !$this->error;
    }

    /**
     * إعداد بيانات الفلترة
     */
    protected function prepareFilterData() {
        return array(
            'date_end' => $this->request->post['date_end'] ?: date('Y-m-d'),
            'report_type' => $this->request->post['report_type'] ?? 'customers',
            'customer_id' => $this->request->post['customer_id'] ?? '',
            'supplier_id' => $this->request->post['supplier_id'] ?? '',
            'include_zero_balances' => isset($this->request->post['include_zero_balances']) ? 1 : 0,
            'aging_periods' => $this->request->post['aging_periods'] ?? array('0-30', '31-60', '61-90', '>90'),
            'currency' => $this->request->post['currency'] ?? $this->config->get('config_currency'),
            'branch_id' => $this->request->post['branch_id'] ?? ''
        );
    }

    /**
     * عرض النموذج
     */
    protected function getForm() {
        $data['breadcrumbs'] = array();
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('accounts/aging_report', 'user_token=' . $this->session->data['user_token'], true)
        );

        $data['action'] = $this->url->link('accounts/aging_report/generate', 'user_token=' . $this->session->data['user_token'], true);

        // تحميل قوائم البيانات
        $this->load->model('customer/customer');
        $this->load->model('supplier/supplier');
        $this->load->model('branch/branch');

        $data['customers'] = $this->model_customer_customer->getCustomers();
        $data['suppliers'] = $this->model_supplier_supplier->getSuppliers();
        $data['branches'] = $this->model_branch_branch->getBranches();

        // القيم الافتراضية
        $data['date_end'] = $this->request->post['date_end'] ?? date('Y-m-d');
        $data['report_type'] = $this->request->post['report_type'] ?? 'customers';
        $data['include_zero_balances'] = $this->request->post['include_zero_balances'] ?? false;

        $data['user_token'] = $this->session->data['user_token'];
        $data['heading_title'] = $this->language->get('heading_title');
        $data['text_no_results'] = $this->language->get('text_no_results');

        if (isset($this->error['warning'])) {
            $data['error_warning'] = $this->error['warning'];
        } else {
            $data['error_warning'] = '';
        }

        if (isset($this->session->data['success'])) {
            $data['success'] = $this->session->data['success'];
            unset($this->session->data['success']);
        } else {
            $data['success'] = '';
        }

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/aging_report_form', $data));
    }

    /**
     * تصدير إلى Excel
     */
    private function exportToExcel($data, $filter_data) {
        $filename = 'aging_report_' . date('Y-m-d') . '.xls';

        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        echo '<table border="1">';
        echo '<tr><th colspan="6">' . $this->language->get('heading_title') . '</th></tr>';
        echo '<tr><th>' . $this->language->get('text_customer') . '</th>';
        echo '<th>' . $this->language->get('text_0_30_days') . '</th>';
        echo '<th>' . $this->language->get('text_31_60_days') . '</th>';
        echo '<th>' . $this->language->get('text_61_90_days') . '</th>';
        echo '<th>' . $this->language->get('text_over_90_days') . '</th>';
        echo '<th>' . $this->language->get('text_total') . '</th></tr>';

        echo '</table>';
        exit;
    }

    /**
     * تصدير إلى PDF
     */
    private function exportToPdf($data, $filter_data) {
        require_once(DIR_SYSTEM . 'library/tcpdf/tcpdf.php');

        $pdf = new TCPDF('P', 'mm', 'A4', true, 'UTF-8');
        $pdf->SetCreator('AYM ERP System');
        $pdf->SetAuthor($this->config->get('config_name'));
        $pdf->SetTitle($this->language->get('heading_title'));

        $pdf->AddPage();
        $pdf->SetFont('dejavusans', 'B', 16);
        $pdf->Cell(0, 10, $this->language->get('heading_title'), 0, 1, 'C');

        $pdf->Output('aging_report_' . date('Y-m-d') . '.pdf', 'D');
        exit;
    }

    /**
     * تصدير إلى CSV
     */
    private function exportToCsv($data, $filter_data) {
        $filename = 'aging_report_' . date('Y-m-d') . '.csv';

        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $output = fopen('php://output', 'w');
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

        fputcsv($output, array(
            $this->language->get('text_customer'),
            $this->language->get('text_0_30_days'),
            $this->language->get('text_31_60_days'),
            $this->language->get('text_61_90_days'),
            $this->language->get('text_over_90_days'),
            $this->language->get('text_total')
        ));

        fclose($output);
        exit;
    }

    /**
     * دالة تنظيف المخرجات (CONSTITUTIONAL REQUIREMENT)
     * Sanitize all output data to prevent XSS attacks
     */
    private function sanitizeOutputData($data) {
        if (is_array($data)) {
            foreach ($data as $key => $value) {
                $data[$key] = $this->sanitizeOutputData($value);
            }
        } elseif (is_string($data)) {
            $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
        }
        return $data;
    }

    /**
     * Enterprise Grade Plus Security Enhancements
     */
    private function validateInputData($data) {
        $validated = array();

        if (isset($data['date_end'])) {
            $validated['date_end'] = date('Y-m-d', strtotime($data['date_end']));
        }

        if (isset($data['report_type'])) {
            $validated['report_type'] = in_array($data['report_type'], ['customers', 'suppliers']) ? $data['report_type'] : 'customers';
        }

        if (isset($data['branch_id'])) {
            $validated['branch_id'] = (int)$data['branch_id'];
        }

        return $validated;
    }

    /**
     * Rate Limiting Implementation
     */
    private function checkRateLimit() {
        $ip = $this->request->server['REMOTE_ADDR'];
        $user_id = $this->user->getId();

        return $this->central_service->checkRateLimit('aging_report_generation', $ip, $user_id, 20, 3600); // 20 requests per hour
    }

    /**
     * Performance Optimization - Memory Management
     */
    private function optimizeMemoryUsage() {
        ini_set('memory_limit', '256M');
        ini_set('max_execution_time', 180); // 3 minutes for aging calculations
    }
}
