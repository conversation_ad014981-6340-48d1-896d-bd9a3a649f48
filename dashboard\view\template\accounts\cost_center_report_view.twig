{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <a href="{{ export_excel }}" data-toggle="tooltip" title="{{ button_export_excel }}" class="btn btn-success"><i class="fa fa-file-excel-o"></i></a>
        <a href="{{ export_pdf }}" data-toggle="tooltip" title="{{ button_export_pdf }}" class="btn btn-danger"><i class="fa fa-file-pdf-o"></i></a>
        <a href="{{ print }}" data-toggle="tooltip" title="{{ button_print }}" class="btn btn-info"><i class="fa fa-print"></i></a>
        <a href="{{ profitability_analysis }}" data-toggle="tooltip" title="{{ button_profitability_analysis }}" class="btn btn-warning"><i class="fa fa-line-chart"></i></a>
        <a href="{{ back }}" data-toggle="tooltip" title="{{ button_back }}" class="btn btn-default"><i class="fa fa-reply"></i></a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if success %}
    <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    
    <!-- Report Header -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-info-circle"></i> {{ text_report_info }}</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-md-6">
            <strong>{{ text_period }}:</strong> {{ filter.date_start }} {{ text_to }} {{ filter.date_end }}<br>
            <strong>{{ text_generated_on }}:</strong> {{ generated_date }}<br>
            <strong>{{ text_generated_by }}:</strong> {{ generated_by }}
          </div>
          <div class="col-md-6">
            {% if filter.cost_center_name %}
            <strong>{{ text_cost_center }}:</strong> {{ filter.cost_center_name }}<br>
            {% endif %}
            {% if filter.department_name %}
            <strong>{{ text_department }}:</strong> {{ filter.department_name }}<br>
            {% endif %}
            <strong>{{ text_cost_type }}:</strong> {{ filter.cost_type_name }}
          </div>
        </div>
      </div>
    </div>

    <!-- Cost Centers Summary -->
    <div class="panel panel-primary">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-building"></i> {{ text_cost_centers_summary }}</h3>
      </div>
      <div class="panel-body">
        <div class="table-responsive">
          <table class="table table-bordered table-hover" id="cost-centers-table">
            <thead>
              <tr>
                <th>{{ column_cost_center }}</th>
                <th>{{ column_department }}</th>
                <th class="text-right">{{ column_revenues }}</th>
                <th class="text-right">{{ column_direct_costs }}</th>
                <th class="text-right">{{ column_indirect_costs }}</th>
                <th class="text-right">{{ column_total_costs }}</th>
                <th class="text-right">{{ column_profit_loss }}</th>
                <th class="text-right">{{ column_profit_margin }}</th>
                <th>{{ column_action }}</th>
              </tr>
            </thead>
            <tbody>
              {% for cost_center in cost_centers %}
              <tr{% if cost_center.profit_loss < 0 %} class="danger"{% elseif cost_center.profit_loss > 0 %} class="success"{% endif %}>
                <td>{{ cost_center.name }}</td>
                <td>{{ cost_center.department_name }}</td>
                <td class="text-right">{{ cost_center.revenues }}</td>
                <td class="text-right">{{ cost_center.direct_costs }}</td>
                <td class="text-right">{{ cost_center.indirect_costs }}</td>
                <td class="text-right">{{ cost_center.total_costs }}</td>
                <td class="text-right{% if cost_center.profit_loss < 0 %} text-danger{% elseif cost_center.profit_loss > 0 %} text-success{% endif %}">{{ cost_center.profit_loss }}</td>
                <td class="text-right">{{ cost_center.profit_margin }}%</td>
                <td>
                  <a href="{{ cost_center.drill_down }}" class="btn btn-xs btn-info" data-toggle="tooltip" title="{{ button_drill_down }}"><i class="fa fa-search"></i></a>
                </td>
              </tr>
              {% endfor %}
            </tbody>
            <tfoot>
              <tr class="info">
                <th colspan="2">{{ text_total }}</th>
                <th class="text-right">{{ totals.revenues }}</th>
                <th class="text-right">{{ totals.direct_costs }}</th>
                <th class="text-right">{{ totals.indirect_costs }}</th>
                <th class="text-right">{{ totals.total_costs }}</th>
                <th class="text-right{% if totals.profit_loss < 0 %} text-danger{% elseif totals.profit_loss > 0 %} text-success{% endif %}">{{ totals.profit_loss }}</th>
                <th class="text-right">{{ totals.profit_margin }}%</th>
                <th></th>
              </tr>
            </tfoot>
          </table>
        </div>
      </div>
    </div>

    <!-- Profitability Chart -->
    {% if show_profitability %}
    <div class="panel panel-info">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pie-chart"></i> {{ text_profitability_chart }}</h3>
      </div>
      <div class="panel-body">
        <canvas id="profitability-chart" width="400" height="200"></canvas>
      </div>
    </div>
    {% endif %}

    <!-- Variance Analysis -->
    {% if variance_analysis %}
    <div class="panel panel-warning">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-exclamation-triangle"></i> {{ text_variance_analysis }}</h3>
      </div>
      <div class="panel-body">
        <div class="table-responsive">
          <table class="table table-bordered">
            <thead>
              <tr>
                <th>{{ column_cost_center }}</th>
                <th class="text-right">{{ column_budget }}</th>
                <th class="text-right">{{ column_actual }}</th>
                <th class="text-right">{{ column_variance }}</th>
                <th class="text-right">{{ column_variance_percent }}</th>
                <th>{{ column_status }}</th>
              </tr>
            </thead>
            <tbody>
              {% for variance in variance_analysis %}
              <tr{% if variance.variance_percent > 10 %} class="danger"{% elseif variance.variance_percent < -10 %} class="warning"{% endif %}>
                <td>{{ variance.cost_center_name }}</td>
                <td class="text-right">{{ variance.budget }}</td>
                <td class="text-right">{{ variance.actual }}</td>
                <td class="text-right{% if variance.variance > 0 %} text-danger{% elseif variance.variance < 0 %} text-success{% endif %}">{{ variance.variance }}</td>
                <td class="text-right">{{ variance.variance_percent }}%</td>
                <td>
                  {% if variance.variance_percent > 10 %}
                  <span class="label label-danger">{{ text_over_budget }}</span>
                  {% elseif variance.variance_percent < -10 %}
                  <span class="label label-success">{{ text_under_budget }}</span>
                  {% else %}
                  <span class="label label-default">{{ text_on_budget }}</span>
                  {% endif %}
                </td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    </div>
    {% endif %}
  </div>
</div>

<script type="text/javascript"><!--
// DataTables initialization
$('#cost-centers-table').DataTable({
    "language": {
        "url": "{{ datatable_language }}"
    },
    "order": [[ 6, "desc" ]],
    "columnDefs": [
        { "orderable": false, "targets": 8 }
    ]
});

// Profitability Chart
{% if show_profitability %}
var ctx = document.getElementById('profitability-chart').getContext('2d');
var profitabilityChart = new Chart(ctx, {
    type: 'doughnut',
    data: {
        labels: [{% for cost_center in cost_centers %}'{{ cost_center.name }}'{% if not loop.last %},{% endif %}{% endfor %}],
        datasets: [{
            data: [{% for cost_center in cost_centers %}{{ cost_center.profit_loss }}{% if not loop.last %},{% endif %}{% endfor %}],
            backgroundColor: [
                '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF', '#FF9F40'
            ]
        }]
    },
    options: {
        responsive: true,
        legend: {
            position: 'bottom'
        }
    }
});
{% endif %}
//--></script>

{{ footer }}
