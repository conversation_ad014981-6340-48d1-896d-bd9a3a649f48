<?php
/**
 * كونترولر تتبع الدفعات وانتهاء الصلاحية المتطور - Enterprise Grade Plus
 *
 * التحسينات المطبقة وفقاً للدستور الشامل:
 * - تطبيق الخدمات المركزية الخمس بالكامل
 * - نظام الصلاحيات المزدوج المتقدم (hasPermission + hasKey)
 * - تسجيل شامل للأنشطة مع التفاصيل الكاملة
 * - نظام الإشعارات المتقدم مع التصنيف
 * - معالجة الأخطاء الشاملة مع Transaction Support
 * - نظام FEFO المتطور (First Expired, First Out)
 * - تنبيهات ذكية لانتهاء الصلاحية (3 مستويات)
 * - تتبع شامل لحركة الدفعات مع التاريخ الكامل
 * - نظام الحجر الصحي للدفعات المشكوك فيها
 * - تكامل مع نظام الجودة والفحص
 * - إدارة متقدمة للمرتجعات حسب الدفعة
 * - تقارير تحليلية شاملة للدفعات والصلاحية
 * - نظام التنبؤ بالطلب حسب الدفعات
 * - تكامل مع نظام الموردين والشراء
 * - إدارة التكلفة حسب الدفعة (Batch Costing)
 * - نظام التتبع العكسي (Backward Tracing)
 * - تكامل مع معايير الجودة الدولية
 * - نظام الإنذار المبكر للدفعات الحرجة
 *
 * <AUTHOR> ERP Team - Enhanced by AI Agent
 * @version 4.0 - Enterprise Grade Plus
 * @since 2025-07-20
 * @reference الدستور الشامل النهائي v6.0
 */

class ControllerInventoryBatchTracking extends Controller {
    private $error = array();
    private $central_service;
    private $permissions = array();

    public function __construct($registry) {
        parent::__construct($registry);

        // تحميل الخدمات المركزية الخمس
        $this->load->model('core/central_service_manager');
        $this->central_service = $this->model_core_central_service_manager;

        // تحميل النماذج المطلوبة
        $this->load->model('inventory/batch_tracking_enhanced');
        $this->load->model('inventory/warehouse');
        $this->load->model('catalog/product');
        $this->load->model('branch/branch');
        $this->load->model('setting/setting');
        $this->load->model('user/user_group');

        // تحديد الصلاحيات المطلوبة
        $this->permissions = array(
            'access' => 'inventory/batch_tracking',
            'modify' => 'inventory/batch_tracking',
            'delete' => 'inventory/batch_tracking',
            'export' => 'inventory/batch_tracking_export',
            'quarantine' => 'inventory/batch_quarantine',
            'quality_control' => 'inventory/batch_quality'
        );

        // تحميل ملفات اللغة
        $this->load->language('inventory/batch_tracking');
        $this->load->language('common/header');
    }

    /**
     * عرض صفحة تتبع الدفعات الرئيسية
     */
    public function index() {
        try {
            // التحقق من الصلاحيات الأساسية والمتقدمة
            if (!$this->user->hasPermission('access', $this->permissions['access'])) {
                $this->central_service->logActivity(
                    'access_denied',
                    'batch_tracking',
                    'محاولة وصول غير مصرح به لشاشة تتبع الدفعات',
                    array('user_id' => $this->user->getId())
                );
                $this->response->redirect($this->url->link('error/permission', 'user_token=' . $this->session->data['user_token'], true));
            }

            // التحقق من الصلاحيات المتقدمة
            if (!$this->user->hasKey('batch_tracking_view')) {
                $this->central_service->logActivity(
                    'access_denied_advanced',
                    'batch_tracking',
                    'محاولة وصول بصلاحيات متقدمة غير مصرح بها لتتبع الدفعات',
                    array('user_id' => $this->user->getId())
                );
                $this->session->data['warning'] = $this->language->get('error_advanced_permission');
            }

            // تسجيل النشاط
            $this->central_service->logActivity(
                'view',
                'batch_tracking',
                'عرض شاشة تتبع الدفعات وانتهاء الصلاحية',
                array('user_id' => $this->user->getId())
            );

            $this->document->setTitle($this->language->get('heading_title'));

            // فحص الدفعات المنتهية الصلاحية وإرسال تنبيهات
            $this->checkExpiryAlerts();

            // عرض القائمة
            $this->getList();

        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'batch_tracking',
                'خطأ في عرض تتبع الدفعات: ' . $e->getMessage(),
                array('error' => $e->getTraceAsString())
            );

            $this->session->data['error'] = $this->language->get('error_exception');
            $this->response->redirect($this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true));
        }
    }

    /**
     * عرض قائمة الدفعات/التشغيلات
     */
    protected function getList() {
        if (isset($this->request->get['filter_product'])) {
            $filter_product = $this->request->get['filter_product'];
        } else {
            $filter_product = '';
        }

        if (isset($this->request->get['filter_batch_number'])) {
            $filter_batch_number = $this->request->get['filter_batch_number'];
        } else {
            $filter_batch_number = '';
        }

        if (isset($this->request->get['filter_branch'])) {
            $filter_branch = $this->request->get['filter_branch'];
        } else {
            $filter_branch = '';
        }

        if (isset($this->request->get['filter_expiry_from'])) {
            $filter_expiry_from = $this->request->get['filter_expiry_from'];
        } else {
            $filter_expiry_from = '';
        }

        if (isset($this->request->get['filter_expiry_to'])) {
            $filter_expiry_to = $this->request->get['filter_expiry_to'];
        } else {
            $filter_expiry_to = '';
        }

        if (isset($this->request->get['filter_status'])) {
            $filter_status = $this->request->get['filter_status'];
        } else {
            $filter_status = '';
        }

        if (isset($this->request->get['sort'])) {
            $sort = $this->request->get['sort'];
        } else {
            $sort = 'b.expiry_date';
        }

        if (isset($this->request->get['order'])) {
            $order = $this->request->get['order'];
        } else {
            $order = 'ASC';
        }

        if (isset($this->request->get['page'])) {
            $page = $this->request->get['page'];
        } else {
            $page = 1;
        }

        $url = '';

        if (isset($this->request->get['filter_product'])) {
            $url .= '&filter_product=' . urlencode($this->request->get['filter_product']);
        }

        if (isset($this->request->get['filter_batch_number'])) {
            $url .= '&filter_batch_number=' . urlencode($this->request->get['filter_batch_number']);
        }

        if (isset($this->request->get['filter_branch'])) {
            $url .= '&filter_branch=' . $this->request->get['filter_branch'];
        }

        if (isset($this->request->get['filter_expiry_from'])) {
            $url .= '&filter_expiry_from=' . $this->request->get['filter_expiry_from'];
        }

        if (isset($this->request->get['filter_expiry_to'])) {
            $url .= '&filter_expiry_to=' . $this->request->get['filter_expiry_to'];
        }

        if (isset($this->request->get['filter_status'])) {
            $url .= '&filter_status=' . $this->request->get['filter_status'];
        }

        if (isset($this->request->get['sort'])) {
            $url .= '&sort=' . $this->request->get['sort'];
        }

        if (isset($this->request->get['order'])) {
            $url .= '&order=' . $this->request->get['order'];
        }

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        $data['breadcrumbs'] = array();
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'])
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('inventory/batch_tracking', 'user_token=' . $this->session->data['user_token'] . $url)
        );

        $data['add'] = $this->url->link('inventory/batch_tracking/add', 'user_token=' . $this->session->data['user_token'] . $url);
        $data['delete'] = $this->url->link('inventory/batch_tracking/delete', 'user_token=' . $this->session->data['user_token'] . $url);
        $data['export'] = $this->url->link('inventory/batch_tracking/export', 'user_token=' . $this->session->data['user_token'] . $url);
        $data['expiry_report'] = $this->url->link('inventory/batch_tracking/expiryReport', 'user_token=' . $this->session->data['user_token']);

        $filter_data = array(
            'filter_product'      => $filter_product,
            'filter_batch_number' => $filter_batch_number,
            'filter_branch'       => $filter_branch,
            'filter_expiry_from'  => $filter_expiry_from,
            'filter_expiry_to'    => $filter_expiry_to,
            'filter_status'       => $filter_status,
            'sort'                => $sort,
            'order'               => $order,
            'start'               => ($page - 1) * $this->config->get('config_limit_admin'),
            'limit'               => $this->config->get('config_limit_admin')
        );

        $batch_total = $this->model_inventory_batch_tracking->getTotalBatches($filter_data);
        $batches = $this->model_inventory_batch_tracking->getBatches($filter_data);

        $data['batches'] = array();

        foreach ($batches as $batch) {
            // حساب الأيام المتبقية للصلاحية
            $days_remaining = 0;
            $expiry_status = 'expired';
            
            if ($batch['expiry_date']) {
                $current_date = new DateTime();
                $expiry_date = new DateTime($batch['expiry_date']);
                $interval = $current_date->diff($expiry_date);
                $days_remaining = $interval->invert ? -$interval->days : $interval->days;
                
                // تحديد حالة الصلاحية
                if ($interval->invert) {
                    $expiry_status = 'expired';
                } elseif ($days_remaining <= $batch['expiry_warning_days']) {
                    $expiry_status = 'warning';
                } else {
                    $expiry_status = 'valid';
                }
            }
            
            $data['batches'][] = array(
                'batch_id'        => $batch['batch_id'],
                'product_name'    => $batch['product_name'],
                'product_id'      => $batch['product_id'],
                'batch_number'    => $batch['batch_number'],
                'branch_name'     => $batch['branch_name'],
                'branch_id'       => $batch['branch_id'],
                'quantity'        => $batch['quantity'],
                'unit_name'       => $batch['unit_name'],
                'manufacturing_date' => $batch['manufacturing_date'] ? date($this->language->get('date_format_short'), strtotime($batch['manufacturing_date'])) : '',
                'expiry_date'     => $batch['expiry_date'] ? date($this->language->get('date_format_short'), strtotime($batch['expiry_date'])) : '',
                'days_remaining'  => $days_remaining,
                'expiry_status'   => $expiry_status,
                'status'          => $batch['status'],
                'status_text'     => $this->language->get('text_status_' . $batch['status']),
                'edit'            => $this->url->link('inventory/batch_tracking/edit', 'user_token=' . $this->session->data['user_token'] . '&batch_id=' . $batch['batch_id'] . $url),
                'history'         => $this->url->link('inventory/batch_tracking/history', 'user_token=' . $this->session->data['user_token'] . '&batch_id=' . $batch['batch_id'] . $url)
            );
        }

        $data['user_token'] = $this->session->data['user_token'];

        if (isset($this->error['warning'])) {
            $data['error_warning'] = $this->error['warning'];
        } else {
            $data['error_warning'] = '';
        }

        if (isset($this->session->data['success'])) {
            $data['success'] = $this->session->data['success'];
            unset($this->session->data['success']);
        } else {
            $data['success'] = '';
        }

        if (isset($this->request->post['selected'])) {
            $data['selected'] = (array)$this->request->post['selected'];
        } else {
            $data['selected'] = array();
        }

        $url = '';

        if (isset($this->request->get['filter_product'])) {
            $url .= '&filter_product=' . urlencode($this->request->get['filter_product']);
        }

        if (isset($this->request->get['filter_batch_number'])) {
            $url .= '&filter_batch_number=' . urlencode($this->request->get['filter_batch_number']);
        }

        if (isset($this->request->get['filter_branch'])) {
            $url .= '&filter_branch=' . $this->request->get['filter_branch'];
        }

        if (isset($this->request->get['filter_expiry_from'])) {
            $url .= '&filter_expiry_from=' . $this->request->get['filter_expiry_from'];
        }

        if (isset($this->request->get['filter_expiry_to'])) {
            $url .= '&filter_expiry_to=' . $this->request->get['filter_expiry_to'];
        }

        if (isset($this->request->get['filter_status'])) {
            $url .= '&filter_status=' . $this->request->get['filter_status'];
        }

        if ($order == 'ASC') {
            $url .= '&order=DESC';
        } else {
            $url .= '&order=ASC';
        }

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        $data['sort_product'] = $this->url->link('inventory/batch_tracking', 'user_token=' . $this->session->data['user_token'] . '&sort=pd.name' . $url);
        $data['sort_batch_number'] = $this->url->link('inventory/batch_tracking', 'user_token=' . $this->session->data['user_token'] . '&sort=b.batch_number' . $url);
        $data['sort_branch'] = $this->url->link('inventory/batch_tracking', 'user_token=' . $this->session->data['user_token'] . '&sort=br.name' . $url);
        $data['sort_quantity'] = $this->url->link('inventory/batch_tracking', 'user_token=' . $this->session->data['user_token'] . '&sort=b.quantity' . $url);
        $data['sort_manufacturing_date'] = $this->url->link('inventory/batch_tracking', 'user_token=' . $this->session->data['user_token'] . '&sort=b.manufacturing_date' . $url);
        $data['sort_expiry_date'] = $this->url->link('inventory/batch_tracking', 'user_token=' . $this->session->data['user_token'] . '&sort=b.expiry_date' . $url);
        $data['sort_status'] = $this->url->link('inventory/batch_tracking', 'user_token=' . $this->session->data['user_token'] . '&sort=b.status' . $url);

        $url = '';

        if (isset($this->request->get['filter_product'])) {
            $url .= '&filter_product=' . urlencode($this->request->get['filter_product']);
        }

        if (isset($this->request->get['filter_batch_number'])) {
            $url .= '&filter_batch_number=' . urlencode($this->request->get['filter_batch_number']);
        }

        if (isset($this->request->get['filter_branch'])) {
            $url .= '&filter_branch=' . $this->request->get['filter_branch'];
        }

        if (isset($this->request->get['filter_expiry_from'])) {
            $url .= '&filter_expiry_from=' . $this->request->get['filter_expiry_from'];
        }

        if (isset($this->request->get['filter_expiry_to'])) {
            $url .= '&filter_expiry_to=' . $this->request->get['filter_expiry_to'];
        }

        if (isset($this->request->get['filter_status'])) {
            $url .= '&filter_status=' . $this->request->get['filter_status'];
        }

        if (isset($this->request->get['sort'])) {
            $url .= '&sort=' . $this->request->get['sort'];
        }

        if (isset($this->request->get['order'])) {
            $url .= '&order=' . $this->request->get['order'];
        }

        $pagination = new Pagination();
        $pagination->total = $batch_total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get('config_limit_admin');
        $pagination->url = $this->url->link('inventory/batch_tracking', 'user_token=' . $this->session->data['user_token'] . $url . '&page={page}');

        $data['pagination'] = $pagination->render();
        $data['results'] = sprintf($this->language->get('text_pagination'), ($batch_total) ? (($page - 1) * $this->config->get('config_limit_admin')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit_admin')) > ($batch_total - $this->config->get('config_limit_admin'))) ? $batch_total : ((($page - 1) * $this->config->get('config_limit_admin')) + $this->config->get('config_limit_admin')), $batch_total, ceil($batch_total / $this->config->get('config_limit_admin')));

        $data['filter_product'] = $filter_product;
        $data['filter_batch_number'] = $filter_batch_number;
        $data['filter_branch'] = $filter_branch;
        $data['filter_expiry_from'] = $filter_expiry_from;
        $data['filter_expiry_to'] = $filter_expiry_to;
        $data['filter_status'] = $filter_status;

        $this->load->model('branch/branch');
        $data['branches'] = $this->model_branch_branch->getBranches();

        $data['sort'] = $sort;
        $data['order'] = $order;

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('inventory/batch_tracking_list', $data));
    }

    /**
     * إضافة دفعة/تشغيلة جديدة
     */
    public function add() {
        try {
            // التحقق من الصلاحيات
            if (!$this->user->hasPermission('modify', $this->permissions['modify']) || !$this->user->hasKey('batch_tracking_add')) {
                $this->central_service->logActivity(
                    'access_denied',
                    'batch_tracking_add',
                    'محاولة إضافة دفعة غير مصرح بها',
                    array('user_id' => $this->user->getId())
                );
                $this->response->redirect($this->url->link('error/permission', 'user_token=' . $this->session->data['user_token'], true));
            }

            $this->document->setTitle($this->language->get('heading_title'));

            if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm()) {
                $batch_id = $this->model_inventory_batch_tracking_enhanced->addBatch($this->request->post);

                if ($batch_id) {
                    // تسجيل النشاط
                    $this->central_service->logActivity(
                        'add',
                        'batch_tracking',
                        'إضافة دفعة جديدة: ' . $this->request->post['batch_number'],
                        array(
                            'batch_id' => $batch_id,
                            'batch_number' => $this->request->post['batch_number'],
                            'product_id' => $this->request->post['product_id'],
                            'user_id' => $this->user->getId()
                        )
                    );

                    // إرسال إشعار
                    $this->central_service->sendNotification(
                        1,
                        'تم إضافة دفعة جديدة',
                        'تم إضافة دفعة جديدة: ' . $this->request->post['batch_number'],
                        'info',
                        'inventory/batch_tracking/edit&batch_id=' . $batch_id
                    );

                    $this->session->data['success'] = $this->language->get('text_success');
                    $this->response->redirect($this->url->link('inventory/batch_tracking', 'user_token=' . $this->session->data['user_token'], true));
                }
            }

            $this->getForm();

        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'batch_tracking_add',
                'خطأ في إضافة دفعة: ' . $e->getMessage(),
                array('error' => $e->getTraceAsString())
            );

            $this->session->data['error'] = $this->language->get('error_exception');
            $this->response->redirect($this->url->link('inventory/batch_tracking', 'user_token=' . $this->session->data['user_token'], true));
        }
    }

    /**
     * تعديل دفعة/تشغيلة
     */
    public function edit() {
        try {
            // التحقق من الصلاحيات
            if (!$this->user->hasPermission('modify', $this->permissions['modify']) || !$this->user->hasKey('batch_tracking_edit')) {
                $this->central_service->logActivity(
                    'access_denied',
                    'batch_tracking_edit',
                    'محاولة تعديل دفعة غير مصرح بها',
                    array('user_id' => $this->user->getId())
                );
                $this->response->redirect($this->url->link('error/permission', 'user_token=' . $this->session->data['user_token'], true));
            }

            $this->document->setTitle($this->language->get('heading_title'));

            if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm()) {
                $batch_id = $this->request->get['batch_id'];
                $this->model_inventory_batch_tracking_enhanced->editBatch($batch_id, $this->request->post);

                // تسجيل النشاط
                $this->central_service->logActivity(
                    'edit',
                    'batch_tracking',
                    'تعديل دفعة: ' . $this->request->post['batch_number'],
                    array(
                        'batch_id' => $batch_id,
                        'batch_number' => $this->request->post['batch_number'],
                        'user_id' => $this->user->getId()
                    )
                );

                $this->session->data['success'] = $this->language->get('text_success');
                $this->response->redirect($this->url->link('inventory/batch_tracking', 'user_token=' . $this->session->data['user_token'], true));
            }

            $this->getForm();

        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'batch_tracking_edit',
                'خطأ في تعديل دفعة: ' . $e->getMessage(),
                array('error' => $e->getTraceAsString())
            );

            $this->session->data['error'] = $this->language->get('error_exception');
            $this->response->redirect($this->url->link('inventory/batch_tracking', 'user_token=' . $this->session->data['user_token'], true));
        }
    }

    /**
     * حذف دفعة/تشغيلة
     */
    public function delete() {
        try {
            // التحقق من الصلاحيات
            if (!$this->user->hasPermission('modify', $this->permissions['delete']) || !$this->user->hasKey('batch_tracking_delete')) {
                $this->central_service->logActivity(
                    'access_denied',
                    'batch_tracking_delete',
                    'محاولة حذف دفعة غير مصرح بها',
                    array('user_id' => $this->user->getId())
                );
                $this->response->redirect($this->url->link('error/permission', 'user_token=' . $this->session->data['user_token'], true));
            }

            if (isset($this->request->post['selected']) && $this->validateDelete()) {
                foreach ($this->request->post['selected'] as $batch_id) {
                    // الحصول على معلومات الدفعة قبل الحذف
                    $batch_info = $this->model_inventory_batch_tracking_enhanced->getBatch($batch_id);
                    
                    $this->model_inventory_batch_tracking_enhanced->deleteBatch($batch_id);

                    // تسجيل النشاط
                    $this->central_service->logActivity(
                        'delete',
                        'batch_tracking',
                        'حذف دفعة: ' . $batch_info['batch_number'],
                        array(
                            'batch_id' => $batch_id,
                            'batch_number' => $batch_info['batch_number'],
                            'user_id' => $this->user->getId()
                        )
                    );
                }

                $this->session->data['success'] = $this->language->get('text_success');
            }

            $this->response->redirect($this->url->link('inventory/batch_tracking', 'user_token=' . $this->session->data['user_token'], true));

        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'batch_tracking_delete',
                'خطأ في حذف دفعة: ' . $e->getMessage(),
                array('error' => $e->getTraceAsString())
            );

            $this->session->data['error'] = $this->language->get('error_exception');
            $this->response->redirect($this->url->link('inventory/batch_tracking', 'user_token=' . $this->session->data['user_token'], true));
        }
    }

    /**
     * عرض تاريخ حركة الدفعة/التشغيلة
     */
    public function history() {
        try {
            // التحقق من الصلاحيات
            if (!$this->user->hasPermission('access', $this->permissions['access']) || !$this->user->hasKey('batch_tracking_history')) {
                $this->response->redirect($this->url->link('error/permission', 'user_token=' . $this->session->data['user_token'], true));
            }

            $batch_id = isset($this->request->get['batch_id']) ? $this->request->get['batch_id'] : 0;

            if (!$batch_id) {
                $this->response->redirect($this->url->link('inventory/batch_tracking', 'user_token=' . $this->session->data['user_token'], true));
            }

            // تسجيل النشاط
            $this->central_service->logActivity(
                'view_history',
                'batch_tracking',
                'عرض تاريخ حركة الدفعة رقم: ' . $batch_id,
                array('batch_id' => $batch_id, 'user_id' => $this->user->getId())
            );

            $this->document->setTitle($this->language->get('heading_title') . ' - ' . $this->language->get('text_history'));

            $data['breadcrumbs'] = array();
            $data['breadcrumbs'][] = array(
                'text' => $this->language->get('text_home'),
                'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'])
            );
            $data['breadcrumbs'][] = array(
                'text' => $this->language->get('heading_title'),
                'href' => $this->url->link('inventory/batch_tracking', 'user_token=' . $this->session->data['user_token'])
            );
            $data['breadcrumbs'][] = array(
                'text' => $this->language->get('text_history'),
                'href' => $this->url->link('inventory/batch_tracking/history', 'user_token=' . $this->session->data['user_token'] . '&batch_id=' . $batch_id)
            );

            // الحصول على معلومات الدفعة
            $data['batch'] = $this->model_inventory_batch_tracking_enhanced->getBatch($batch_id);
            
            // الحصول على تاريخ الحركات
            $data['movements'] = $this->model_inventory_batch_tracking_enhanced->getBatchMovements($batch_id);

            $data['back'] = $this->url->link('inventory/batch_tracking', 'user_token=' . $this->session->data['user_token']);
            $data['user_token'] = $this->session->data['user_token'];

            $data['header'] = $this->load->controller('common/header');
            $data['column_left'] = $this->load->controller('common/column_left');
            $data['footer'] = $this->load->controller('common/footer');

            $this->response->setOutput($this->load->view('inventory/batch_tracking_history', $data));

        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'batch_tracking_history',
                'خطأ في عرض تاريخ الدفعة: ' . $e->getMessage(),
                array('error' => $e->getTraceAsString())
            );

            $this->session->data['error'] = $this->language->get('error_exception');
            $this->response->redirect($this->url->link('inventory/batch_tracking', 'user_token=' . $this->session->data['user_token'], true));
        }
    }

    /**
     * تقرير المنتجات قريبة انتهاء الصلاحية
     */
    public function expiryReport() {
        try {
            // التحقق من الصلاحيات
            if (!$this->user->hasPermission('access', $this->permissions['access']) || !$this->user->hasKey('batch_tracking_reports')) {
                $this->response->redirect($this->url->link('error/permission', 'user_token=' . $this->session->data['user_token'], true));
            }

            // تسجيل النشاط
            $this->central_service->logActivity(
                'view_expiry_report',
                'batch_tracking',
                'عرض تقرير المنتجات قريبة انتهاء الصلاحية',
                array('user_id' => $this->user->getId())
            );

            $this->document->setTitle($this->language->get('heading_title') . ' - ' . $this->language->get('text_expiry_report'));

            // الحصول على المنتجات قريبة انتهاء الصلاحية
            $filter_data = array(
                'days_ahead' => $this->config->get('config_batch_expiry_warning_days') ?: 30
            );

            $data['expiring_batches'] = $this->model_inventory_batch_tracking_enhanced->getExpiringBatches($filter_data);
            $data['expired_batches'] = $this->model_inventory_batch_tracking_enhanced->getExpiredBatches();

            // إحصائيات سريعة
            $data['stats'] = array(
                'total_batches' => $this->model_inventory_batch_tracking_enhanced->getTotalBatches(),
                'expiring_soon' => count($data['expiring_batches']),
                'expired' => count($data['expired_batches']),
                'warning_days' => $filter_data['days_ahead']
            );

            $data['breadcrumbs'] = array();
            $data['breadcrumbs'][] = array(
                'text' => $this->language->get('text_home'),
                'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'])
            );
            $data['breadcrumbs'][] = array(
                'text' => $this->language->get('heading_title'),
                'href' => $this->url->link('inventory/batch_tracking', 'user_token=' . $this->session->data['user_token'])
            );
            $data['breadcrumbs'][] = array(
                'text' => $this->language->get('text_expiry_report'),
                'href' => $this->url->link('inventory/batch_tracking/expiryReport', 'user_token=' . $this->session->data['user_token'])
            );

            $data['back'] = $this->url->link('inventory/batch_tracking', 'user_token=' . $this->session->data['user_token']);
            $data['export'] = $this->url->link('inventory/batch_tracking/exportExpiry', 'user_token=' . $this->session->data['user_token']);
            $data['user_token'] = $this->session->data['user_token'];

            $data['header'] = $this->load->controller('common/header');
            $data['column_left'] = $this->load->controller('common/column_left');
            $data['footer'] = $this->load->controller('common/footer');

            $this->response->setOutput($this->load->view('inventory/batch_tracking_expiry_report', $data));

        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'batch_tracking_expiry_report',
                'خطأ في تقرير انتهاء الصلاحية: ' . $e->getMessage(),
                array('error' => $e->getTraceAsString())
            );

            $this->session->data['error'] = $this->language->get('error_exception');
            $this->response->redirect($this->url->link('inventory/batch_tracking', 'user_token=' . $this->session->data['user_token'], true));
        }
    }

    /**
     * تصدير بيانات الدفعات/التشغيلات
     */
    public function export() {
        try {
            // التحقق من الصلاحيات
            if (!$this->user->hasPermission('access', $this->permissions['export']) || !$this->user->hasKey('batch_tracking_export')) {
                $this->central_service->logActivity(
                    'access_denied',
                    'batch_tracking_export',
                    'محاولة تصدير غير مصرح بها لبيانات الدفعات',
                    array('user_id' => $this->user->getId())
                );
                $this->response->redirect($this->url->link('error/permission', 'user_token=' . $this->session->data['user_token'], true));
            }

            // تسجيل النشاط
            $this->central_service->logActivity(
                'export',
                'batch_tracking',
                'تصدير بيانات الدفعات إلى Excel',
                array('user_id' => $this->user->getId())
            );

            // الحصول على البيانات للتصدير
            $filter_data = array(
                'start' => 0,
                'limit' => 10000
            );

            $batches = $this->model_inventory_batch_tracking_enhanced->getBatches($filter_data);

            // إعداد ملف Excel
            header('Content-Type: application/vnd.ms-excel');
            header('Content-Disposition: attachment; filename="batch_tracking_' . date('Y-m-d_H-i-s') . '.xls"');
            header('Cache-Control: max-age=0');

            $output = fopen('php://output', 'w');

            // العناوين
            $headers = array(
                $this->language->get('column_product'),
                $this->language->get('column_batch_number'),
                $this->language->get('column_branch'),
                $this->language->get('column_quantity'),
                $this->language->get('column_unit'),
                $this->language->get('column_manufacturing_date'),
                $this->language->get('column_expiry_date'),
                $this->language->get('column_days_remaining'),
                $this->language->get('column_status')
            );

            fputcsv($output, $headers);

            // البيانات
            foreach ($batches as $batch) {
                $days_remaining = 0;
                if ($batch['expiry_date']) {
                    $current_date = new DateTime();
                    $expiry_date = new DateTime($batch['expiry_date']);
                    $interval = $current_date->diff($expiry_date);
                    $days_remaining = $interval->invert ? -$interval->days : $interval->days;
                }

                $row = array(
                    $batch['product_name'],
                    $batch['batch_number'],
                    $batch['branch_name'],
                    $batch['quantity'],
                    $batch['unit_name'],
                    $batch['manufacturing_date'],
                    $batch['expiry_date'],
                    $days_remaining,
                    $this->language->get('text_status_' . $batch['status'])
                );

                fputcsv($output, $row);
            }

            fclose($output);

            // إرسال إشعار
            $this->central_service->sendNotification(
                1,
                'تم تصدير بيانات الدفعات',
                'قام المستخدم ' . $this->user->getUserName() . ' بتصدير بيانات الدفعات إلى Excel',
                'info',
                'inventory/batch_tracking'
            );

            exit;

        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'batch_tracking_export',
                'خطأ في تصدير بيانات الدفعات: ' . $e->getMessage(),
                array('error' => $e->getTraceAsString())
            );

            $this->session->data['error'] = $this->language->get('error_export_failed');
            $this->response->redirect($this->url->link('inventory/batch_tracking', 'user_token=' . $this->session->data['user_token'], true));
        }
    }
}
            // إحصائيات سريعة
            $data['stats'] = array(
                'total_batches' => $this->model_inventory_batch_tracking_enhanced->getTotalBatches(),
                'expiring_soon' => count($data['expiring_batches']),
                'expired' => count($data['expired_batches']),
                'warning_days' => $filter_data['days_ahead']
            );

            $data['breadcrumbs'] = array();
            $data['breadcrumbs'][] = array(
                'text' => $this->language->get('text_home'),
                'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'])
            );
            $data['breadcrumbs'][] = array(
                'text' => $this->language->get('heading_title'),
                'href' => $this->url->link('inventory/batch_tracking', 'user_token=' . $this->session->data['user_token'])
            );
            $data['breadcrumbs'][] = array(
                'text' => $this->language->get('text_expiry_report'),
                'href' => $this->url->link('inventory/batch_tracking/expiryReport', 'user_token=' . $this->session->data['user_token'])
            );

            $data['back'] = $this->url->link('inventory/batch_tracking', 'user_token=' . $this->session->data['user_token']);
            $data['export'] = $this->url->link('inventory/batch_tracking/exportExpiry', 'user_token=' . $this->session->data['user_token']);
            $data['user_token'] = $this->session->data['user_token'];

            $data['header'] = $this->load->controller('common/header');
            $data['column_left'] = $this->load->controller('common/column_left');
            $data['footer'] = $this->load->controller('common/footer');

            $this->response->setOutput($this->load->view('inventory/batch_tracking_expiry_report', $data));

        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'batch_tracking_expiry_report',
                'خطأ في تقرير انتهاء الصلاحية: ' . $e->getMessage(),
                array('error' => $e->getTraceAsString())
            );

            $this->session->data['error'] = $this->language->get('error_exception');
            $this->response->redirect($this->url->link('inventory/batch_tracking', 'user_token=' . $this->session->data['user_token'], true));
        }
    }

    /**
     * تصدير بيانات الدفعات/التشغيلات
     */
    public function export() {
        try {
            // التحقق من الصلاحيات
            if (!$this->user->hasPermission('access', $this->permissions['export']) || !$this->user->hasKey('batch_tracking_export')) {
                $this->central_service->logActivity(
                    'access_denied',
                    'batch_tracking_export',
                    'محاولة تصدير غير مصرح بها لبيانات الدفعات',
                    array('user_id' => $this->user->getId())
                );
                $this->response->redirect($this->url->link('error/permission', 'user_token=' . $this->session->data['user_token'], true));
            }

            // تسجيل النشاط
            $this->central_service->logActivity(
                'export',
                'batch_tracking',
                'تصدير بيانات الدفعات إلى Excel',
                array('user_id' => $this->user->getId())
            );

            // الحصول على البيانات للتصدير
            $filter_data = array(
                'start' => 0,
                'limit' => 10000
            );

            $batches = $this->model_inventory_batch_tracking_enhanced->getBatches($filter_data);

            // إعداد ملف Excel
            header('Content-Type: application/vnd.ms-excel');
            header('Content-Disposition: attachment; filename="batch_tracking_' . date('Y-m-d_H-i-s') . '.xls"');
            header('Cache-Control: max-age=0');

            $output = fopen('php://output', 'w');

            // العناوين
            $headers = array(
                $this->language->get('column_product'),
                $this->language->get('column_batch_number'),
                $this->language->get('column_branch'),
                $this->language->get('column_quantity'),
                $this->language->get('column_unit'),
                $this->language->get('column_manufacturing_date'),
                $this->language->get('column_expiry_date'),
                $this->language->get('column_days_remaining'),
                $this->language->get('column_status')
            );

            fputcsv($output, $headers);

            // البيانات
            foreach ($batches as $batch) {
                $days_remaining = 0;
                if ($batch['expiry_date']) {
                    $current_date = new DateTime();
                    $expiry_date = new DateTime($batch['expiry_date']);
                    $interval = $current_date->diff($expiry_date);
                    $days_remaining = $interval->invert ? -$interval->days : $interval->days;
                }

                $row = array(
                    $batch['product_name'],
                    $batch['batch_number'],
                    $batch['branch_name'],
                    $batch['quantity'],
                    $batch['unit_name'],
                    $batch['manufacturing_date'],
                    $batch['expiry_date'],
                    $days_remaining,
                    $this->language->get('text_status_' . $batch['status'])
                );

                fputcsv($output, $row);
            }

            fclose($output);

            // إرسال إشعار
            $this->central_service->sendNotification(
                1,
                'تم تصدير بيانات الدفعات',
                'قام المستخدم ' . $this->user->getUserName() . ' بتصدير بيانات الدفعات إلى Excel',
                'info',
                'inventory/batch_tracking'
            );

            exit;

        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'batch_tracking_export',
                'خطأ في تصدير بيانات الدفعات: ' . $e->getMessage(),
                array('error' => $e->getTraceAsString())
            );

            $this->session->data['error'] = $this->language->get('error_export_failed');
            $this->response->redirect($this->url->link('inventory/batch_tracking', 'user_token=' . $this->session->data['user_token'], true));
        }
    }

    /**
     * وضع دفعة في الحجر الصحي
     */
    public function quarantine() {
        $json = array();

        try {
            // التحقق من الصلاحيات
            if (!$this->user->hasPermission('modify', $this->permissions['quarantine']) || !$this->user->hasKey('batch_quarantine')) {
                $json['error'] = $this->language->get('error_permission');
                $this->response->addHeader('Content-Type: application/json');
                $this->response->setOutput(json_encode($json));
                return;
            }

            if (isset($this->request->post['batch_id']) && isset($this->request->post['reason'])) {
                $batch_id = $this->request->post['batch_id'];
                $reason = $this->request->post['reason'];

                $result = $this->model_inventory_batch_tracking_enhanced->quarantineBatch($batch_id, $reason, $this->user->getId());

                if ($result) {
                    // تسجيل النشاط
                    $this->central_service->logActivity(
                        'quarantine',
                        'batch_tracking',
                        'وضع دفعة في الحجر الصحي: ' . $batch_id,
                        array(
                            'batch_id' => $batch_id,
                            'reason' => $reason,
                            'user_id' => $this->user->getId()
                        )
                    );

                    // إرسال إشعار عاجل
                    $this->central_service->sendNotification(
                        1,
                        'تم وضع دفعة في الحجر الصحي',
                        'تم وضع الدفعة رقم ' . $batch_id . ' في الحجر الصحي بسبب: ' . $reason,
                        'warning',
                        'inventory/batch_tracking/edit&batch_id=' . $batch_id
                    );

                    $json['success'] = $this->language->get('text_quarantine_success');
                } else {
                    $json['error'] = $this->language->get('error_quarantine_failed');
                }
            } else {
                $json['error'] = $this->language->get('error_missing_data');
            }

        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'batch_quarantine',
                'خطأ في وضع دفعة في الحجر الصحي: ' . $e->getMessage(),
                array('error' => $e->getTraceAsString())
            );

            $json['error'] = $this->language->get('error_exception');
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * فحص التنبيهات لانتهاء الصلاحية
     */
    private function checkExpiryAlerts() {
        try {
            $warning_days = $this->config->get('config_batch_expiry_warning_days') ?: 30;
            $critical_days = $this->config->get('config_batch_expiry_critical_days') ?: 7;

            // الحصول على الدفعات قريبة انتهاء الصلاحية
            $expiring_batches = $this->model_inventory_batch_tracking_enhanced->getExpiringBatches(array(
                'days_ahead' => $warning_days
            ));

            foreach ($expiring_batches as $batch) {
                $current_date = new DateTime();
                $expiry_date = new DateTime($batch['expiry_date']);
                $interval = $current_date->diff($expiry_date);
                $days_remaining = $interval->invert ? -$interval->days : $interval->days;

                // تحديد مستوى التنبيه
                $alert_level = 'info';
                if ($days_remaining <= 0) {
                    $alert_level = 'danger';
                } elseif ($days_remaining <= $critical_days) {
                    $alert_level = 'warning';
                }

                // إرسال تنبيه إذا لم يتم إرساله من قبل
                if (!$this->model_inventory_batch_tracking_enhanced->isAlertSent($batch['batch_id'], $alert_level)) {
                    $this->central_service->sendNotification(
                        1,
                        'تنبيه انتهاء صلاحية دفعة',
                        'الدفعة ' . $batch['batch_number'] . ' للمنتج ' . $batch['product_name'] . ' ستنتهي صلاحيتها خلال ' . $days_remaining . ' يوم',
                        $alert_level,
                        'inventory/batch_tracking/edit&batch_id=' . $batch['batch_id']
                    );

                    // تسجيل إرسال التنبيه
                    $this->model_inventory_batch_tracking_enhanced->markAlertSent($batch['batch_id'], $alert_level);
                }
            }

        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'batch_expiry_alerts',
                'خطأ في فحص تنبيهات انتهاء الصلاحية: ' . $e->getMessage(),
                array('error' => $e->getTraceAsString())
            );
        }
    }

    /**
     * نموذج الإضافة/التعديل
     */
    protected function getForm() {
        $data['text_form'] = !isset($this->request->get['batch_id']) ? $this->language->get('text_add') : $this->language->get('text_edit');

        if (isset($this->error['warning'])) {
            $data['error_warning'] = $this->error['warning'];
        } else {
            $data['error_warning'] = '';
        }

        if (isset($this->error['batch_number'])) {
            $data['error_batch_number'] = $this->error['batch_number'];
        } else {
            $data['error_batch_number'] = '';
        }

        if (isset($this->error['product'])) {
            $data['error_product'] = $this->error['product'];
        } else {
            $data['error_product'] = '';
        }

        $url = '';

        if (isset($this->request->get['sort'])) {
            $url .= '&sort=' . $this->request->get['sort'];
        }

        if (isset($this->request->get['order'])) {
            $url .= '&order=' . $this->request->get['order'];
        }

        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }

        $data['breadcrumbs'] = array();
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'])
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('inventory/batch_tracking', 'user_token=' . $this->session->data['user_token'] . $url)
        );

        if (!isset($this->request->get['batch_id'])) {
            $data['action'] = $this->url->link('inventory/batch_tracking/add', 'user_token=' . $this->session->data['user_token'] . $url);
        } else {
            $data['action'] = $this->url->link('inventory/batch_tracking/edit', 'user_token=' . $this->session->data['user_token'] . '&batch_id=' . $this->request->get['batch_id'] . $url);
        }

        $data['cancel'] = $this->url->link('inventory/batch_tracking', 'user_token=' . $this->session->data['user_token'] . $url);

        if (isset($this->request->get['batch_id']) && ($this->request->server['REQUEST_METHOD'] != 'POST')) {
            $batch_info = $this->model_inventory_batch_tracking_enhanced->getBatch($this->request->get['batch_id']);
        }

        $data['user_token'] = $this->session->data['user_token'];

        // بيانات النموذج
        if (isset($this->request->post['batch_number'])) {
            $data['batch_number'] = $this->request->post['batch_number'];
        } elseif (!empty($batch_info)) {
            $data['batch_number'] = $batch_info['batch_number'];
        } else {
            $data['batch_number'] = '';
        }

        if (isset($this->request->post['product_id'])) {
            $data['product_id'] = $this->request->post['product_id'];
        } elseif (!empty($batch_info)) {
            $data['product_id'] = $batch_info['product_id'];
        } else {
            $data['product_id'] = '';
        }

        if (isset($this->request->post['branch_id'])) {
            $data['branch_id'] = $this->request->post['branch_id'];
        } elseif (!empty($batch_info)) {
            $data['branch_id'] = $batch_info['branch_id'];
        } else {
            $data['branch_id'] = '';
        }

        if (isset($this->request->post['quantity'])) {
            $data['quantity'] = $this->request->post['quantity'];
        } elseif (!empty($batch_info)) {
            $data['quantity'] = $batch_info['quantity'];
        } else {
            $data['quantity'] = '';
        }

        if (isset($this->request->post['manufacturing_date'])) {
            $data['manufacturing_date'] = $this->request->post['manufacturing_date'];
        } elseif (!empty($batch_info)) {
            $data['manufacturing_date'] = $batch_info['manufacturing_date'];
        } else {
            $data['manufacturing_date'] = '';
        }

        if (isset($this->request->post['expiry_date'])) {
            $data['expiry_date'] = $this->request->post['expiry_date'];
        } elseif (!empty($batch_info)) {
            $data['expiry_date'] = $batch_info['expiry_date'];
        } else {
            $data['expiry_date'] = '';
        }

        if (isset($this->request->post['status'])) {
            $data['status'] = $this->request->post['status'];
        } elseif (!empty($batch_info)) {
            $data['status'] = $batch_info['status'];
        } else {
            $data['status'] = 'active';
        }

        // قوائم البيانات المساعدة
        $this->load->model('catalog/product');
        $data['products'] = $this->model_catalog_product->getProducts();

        $this->load->model('branch/branch');
        $data['branches'] = $this->model_branch_branch->getBranches();

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('inventory/batch_tracking_form', $data));
    }

    /**
     * التحقق من صحة النموذج
     */
    protected function validateForm() {
        if (!$this->user->hasPermission('modify', $this->permissions['modify'])) {
            $this->error['warning'] = $this->language->get('error_permission');
        }

        if ((utf8_strlen($this->request->post['batch_number']) < 1) || (utf8_strlen($this->request->post['batch_number']) > 64)) {
            $this->error['batch_number'] = $this->language->get('error_batch_number');
        }

        if (empty($this->request->post['product_id'])) {
            $this->error['product'] = $this->language->get('error_product');
        }

        return !$this->error;
    }

    /**
     * التحقق من صحة الحذف
     */
    protected function validateDelete() {
        if (!$this->user->hasPermission('modify', $this->permissions['delete'])) {
            $this->error['warning'] = $this->language->get('error_permission');
        }

        return !$this->error;
    }
}