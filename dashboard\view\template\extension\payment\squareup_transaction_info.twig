{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        {% if is_authorized %}
            <a id="transaction_capture" data-url-transaction-capture="{{ url_capture }}" data-confirm-capture="{{ confirm_capture }}" class="btn btn-success">{{ button_capture }}</a>
            <a id="transaction_void" data-url-transaction-void="{{ url_void }}" data-confirm-void="{{ confirm_void }}" class="btn btn-warning">{{ button_void }}</a>
        {% endif %}
        
        {% if is_captured %}
            <a id="transaction_refund" data-url-transaction-refund="{{ url_refund }}" data-confirm-refund="{{ confirm_refund }}" data-insert-amount="{{ insert_amount }}" class="btn btn-danger">{{ button_refund }}</a>
        {% endif %}
        
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a></div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    <div id="transaction-alert" data-message="{{ text_loading }}">
        {% for alert in alerts %}
            <div class="alert alert-{{ alert.type }}"><i class="fa fa-{{ alert.icon }}"></i>&nbsp;{{ alert.text }}
                <button type="button" class="close" data-dismiss="alert">&times;</button>
            </div>
        {% endfor %}
    </div>
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-info-circle"></i>&nbsp;{{ text_edit }}</h3>
      </div>
      <div class="panel-body">
        <form class="form-horizontal">
            <div class="form-group">
                <label class="col-sm-2 control-label">{{ entry_transaction_id }}</label>
                <div class="col-sm-10">
                    <div class="form-control-static"><a href="{{ url_transaction }}" target="_blank">{{ transaction_id }}</a></div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">{{ entry_merchant }}</label>
                <div class="col-sm-10">
                    <div class="form-control-static">{{ merchant }}</div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">{{ entry_order_id }}</label>
                <div class="col-sm-10">
                    <div class="form-control-static"><a href="{{ url_order }}" target="_blank">{{ order_id }}</a></div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">{{ entry_type }}</label>
                <div class="col-sm-10">
                    <div class="form-control-static">{{ type }}</div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">{{ entry_amount }}</label>
                <div class="col-sm-10">
                    <div class="form-control-static">{{ amount }}</div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">{{ entry_billing_address_company }}</label>
                <div class="col-sm-10">
                    <div class="form-control-static">{{ billing_address_company }}</div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">{{ entry_billing_address_street }}</label>
                <div class="col-sm-10">
                    <div class="form-control-static">{{ billing_address_street }}</div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">{{ entry_billing_address_city }}</label>
                <div class="col-sm-10">
                    <div class="form-control-static">{{ billing_address_city }}</div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">{{ entry_billing_address_postcode }}</label>
                <div class="col-sm-10">
                    <div class="form-control-static">{{ billing_address_postcode }}</div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">{{ entry_billing_address_province }}</label>
                <div class="col-sm-10">
                    <div class="form-control-static">{{ billing_address_province }}</div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">{{ entry_billing_address_country }}</label>
                <div class="col-sm-10">
                    <div class="form-control-static">{{ billing_address_country }}</div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">{{ entry_browser }}</label>
                <div class="col-sm-10">
                    <div class="form-control-static">{{ browser }}</div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">{{ entry_ip }}</label>
                <div class="col-sm-10">
                    <div class="form-control-static">{{ ip }}</div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">{{ entry_date_created }}</label>
                <div class="col-sm-10">
                    <div class="form-control-static">{{ date_created }}</div>
                </div>
            </div>
            {% if has_refunds %}
                <hr />
                <h3>{{ text_refunds }}</h3>
                <table class="table table-bordered table-striped">
                    <thead>
                        <th>{{ column_date_created }}</th>
                        <th>{{ column_reason }}</th>
                        <th>{{ column_status }}</th>
                        <th>{{ column_amount }}</th>
                        <th>{{ column_fee }}</th>
                    </thead>
                    <tbody>
                        {% for refund in refunds %}
                            <tr>
                                <td>{{ refund.date_created }}</td>
                                <td>{{ refund.reason }}</td>
                                <td>{{ refund.status }}</td>
                                <td>{{ refund.amount }}</td>
                                <td>{{ refund.fee }}</td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            {% endif %}
        </form>
      </div>
    </div>
  </div>
</div>
<div class="modal fade" id="squareup-confirm-modal">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title">{{ text_confirm_action }}</h4>
            </div>
            <div class="modal-body">
                <h4 id="squareup-confirm-modal-content"></h4>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">{{ text_close }}</button>
                <button id="squareup-confirm-ok" type="button" class="btn btn-primary">{{ text_ok }}</button>
            </div>
        </div>
    </div>
</div>
<div class="modal fade" id="squareup-refund-modal">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title">{{ text_refund_details }}</h4>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="control-label" id="squareup-refund-modal-content-reason"></label>
                    <textarea class="form-control" id="squareup-refund-reason" required></textarea>
                </div>
                <div class="form-group">
                    <label class="control-label" id="squareup-refund-modal-content-amount"></label>
                    <input class="form-control" type="text" id="squareup-refund-amount" required />
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">{{ text_close }}</button>
                <button id="squareup-refund-ok" type="button" class="btn btn-primary">{{ text_ok }}</button>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
$(document).ready(function() {
    var transactionLoading = function() {
        var message = $('#transaction-alert').attr('data-message');

        $('#transaction-alert').html('<div class="text-center alert alert-info"><i class="fa fa-circle-o-notch fa-spin"></i>&nbsp;' + message + '</div>');
    }

    var refreshPage = function() {
        document.location = document.location;
    }

    var transactionError = function(message) {
        $('#transaction-alert').html('<div class="alert alert-danger"><button type="button" class="close" data-dismiss="alert" aria-label="X"><span aria-hidden="true">&times;</span></button><i class="fa fa-exclamation-circle"></i>&nbsp;' + message + '</div>');
    }

    var addOrderHistory = function(data, success_callback) {
        $.ajax({
            url: '{{ catalog }}index.php?route=api/order/history&api_token={{ api_token }}&order_id=' + data.order_id,
            type: 'post',
            dataType: 'json',
            data: data,
            success: function(json) {
                if (json['error']) {
                    refreshPage();
                }

                if (json['success']) {
                    success_callback();
                }
            },
            error: function(xhr, ajaxOptions, thrownError) {
                transactionError(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
                enableTransactionButtons();
            }
        });
    }

    var transactionRequest = function(type, url, data) {
        $.ajax({
            url : url,
            dataType : 'json',
            type : type,
            data : data,
            beforeSend : transactionLoading,
            success : function(json) {
                if (json.error) {
                    refreshPage();
                }

                if (json.success && json.order_history_data) {
                    addOrderHistory(json.order_history_data, function() {
                        refreshPage();
                    });
                }
            },
            error : function(xhr, ajaxSettings, thrownError) {
                transactionError(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
                enableTransactionButtons();
            }
        });
    }

    var disableTransactionButtons = function() {
        $('*[data-url-transaction-capture], *[data-url-transaction-void], *[data-url-transaction-refund]').attr('disabled', true);
    }

    var enableTransactionButtons = function() {
        $('*[data-url-transaction-capture], *[data-url-transaction-void], *[data-url-transaction-refund]').attr('disabled', false);
    }

    var modalConfirm = function(url, text) {
        var modal = '#squareup-confirm-modal';
        var content = '#squareup-confirm-modal-content';
        var button = '#squareup-confirm-ok';

        $(content).html(text);
        $(button).unbind().click(function() {
            disableTransactionButtons();

            $(modal).modal('hide');

            transactionRequest('GET', url);
        });
        
        $(modal).modal('show');
    }

    var refundInputValidate = function(reason_input, amount_input) {
        var result = true;

        if (!$(reason_input)[0].checkValidity()) {
            $(reason_input).closest('.form-group').addClass('has-error');
            result = false;
        } else {
            $(reason_input).closest('.form-group').removeClass('has-error');
        }

        if (!$(amount_input)[0].checkValidity()) {
            $(amount_input).closest('.form-group').addClass('has-error');
            result = false;
        } else {
            $(amount_input).closest('.form-group').removeClass('has-error');
        }

        return result;
    }

    var modalRefund = function(url, text_reason, text_amount) {
        var modal = '#squareup-refund-modal';
        var content_reason = '#squareup-refund-modal-content-reason';
        var content_amount = '#squareup-refund-modal-content-amount';
        var button = '#squareup-refund-ok';
        var reason_input = '#squareup-refund-reason';
        var amount_input = '#squareup-refund-amount';

        $(content_reason).html(text_reason);
        $(content_amount).html(text_amount);

        $(reason_input).val('');
        $(amount_input).val('');

        $(button).unbind().click(function() {
            if (!refundInputValidate(reason_input, amount_input)) {
                return;
            }

            disableTransactionButtons();

            $(modal).modal('hide');

            transactionRequest('POST', url, {
                reason : $(reason_input).val(),
                amount : $(amount_input).val()
            });
        });
        
        $(modal).modal('show');
    }

    $(document).on('click', '*[data-url-transaction-capture]', function() {
        if ($(this).attr('disabled')) return;

        modalConfirm(
            $(this).attr('data-url-transaction-capture'),
            $(this).attr('data-confirm-capture')
        );
    });
        
    $(document).on('click', '*[data-url-transaction-void]', function() {
        if ($(this).attr('disabled')) return;

        modalConfirm(
            $(this).attr('data-url-transaction-void'),
            $(this).attr('data-confirm-void')
        );
    });

    $(document).on('click', '*[data-url-transaction-refund]', function() {
        if ($(this).attr('disabled')) return;

        modalRefund(
            $(this).attr('data-url-transaction-refund'),
            $(this).attr('data-confirm-refund'),
            $(this).attr('data-insert-amount')
        );
    });
});
</script>
{{ footer }} 