<?php
/**
 * نموذج إعادة تقييم العملات المتقدم
 * يدعم معايير المحاسبة المصرية والدولية IAS 21
 * مع تكامل كامل مع نظام المحاسبة والقيود التلقائية
 */
class ModelAccountsMultiCurrencyRevaluation extends Model {
    private $central_service;

    public function __construct($registry) {
        parent::__construct($registry);
        
        // تحميل الخدمات المركزية
        $this->load->model('core/central_service_manager');
        $this->central_service = $this->model_core_central_service_manager;
    }

    /**
     * معالجة إعادة تقييم العملات
     */
    public function processRevaluation($data) {
        $this->db->query("START TRANSACTION");

        try {
            // إنشاء سجل إعادة التقييم
            $reference = $this->generateRevaluationReference();
            
            $this->db->query("INSERT INTO cod_currency_revaluation SET 
                reference = '" . $this->db->escape($reference) . "',
                revaluation_date = '" . $this->db->escape($data['revaluation_date']) . "',
                status = 'pending',
                auto_post = '" . (int)$data['auto_post'] . "',
                created_by = '" . (int)$this->user->getId() . "',
                date_created = NOW()
            ");

            $revaluation_id = $this->db->getLastId();

            // معالجة كل عملة
            $total_adjustment = 0;
            foreach ($data['currencies'] as $currency_code) {
                if (isset($data['exchange_rates'][$currency_code])) {
                    $new_rate = (float)$data['exchange_rates'][$currency_code];
                    $adjustment = $this->processRevaluationForCurrency($revaluation_id, $currency_code, $new_rate, $data['revaluation_date'], $data['account_types']);
                    $total_adjustment += $adjustment;
                }
            }

            // تحديث إجمالي التعديل
            $this->db->query("UPDATE cod_currency_revaluation SET 
                total_adjustment = '" . (float)$total_adjustment . "',
                status = '" . ($data['auto_post'] ? 'posted' : 'pending') . "'
                WHERE revaluation_id = '" . (int)$revaluation_id . "'
            ");

            // إنشاء القيود المحاسبية إذا كان الترحيل تلقائي
            if ($data['auto_post']) {
                $this->createRevaluationJournalEntries($revaluation_id);
            }

            $this->db->query("COMMIT");
            return $revaluation_id;

        } catch (Exception $e) {
            $this->db->query("ROLLBACK");
            throw $e;
        }
    }

    /**
     * معالجة إعادة التقييم لعملة واحدة
     */
    private function processRevaluationForCurrency($revaluation_id, $currency_code, $new_rate, $revaluation_date, $account_types) {
        // جلب سعر الصرف الحالي
        $current_rate_query = $this->db->query("SELECT value FROM cod_currency WHERE code = '" . $this->db->escape($currency_code) . "'");
        $current_rate = $current_rate_query->num_rows ? (float)$current_rate_query->row['value'] : 1;

        // جلب الحسابات المتأثرة
        $accounts = $this->getForeignCurrencyAccountsForCurrency($currency_code, $account_types);

        $total_adjustment = 0;

        foreach ($accounts as $account) {
            // حساب الرصيد بالعملة الأجنبية
            $foreign_balance = $this->getAccountForeignCurrencyBalance($account['account_id'], $currency_code, $revaluation_date);
            
            if ($foreign_balance != 0) {
                // حساب الرصيد بالعملة المحلية بالسعر الحالي والجديد
                $current_local_balance = $foreign_balance * $current_rate;
                $new_local_balance = $foreign_balance * $new_rate;
                $adjustment = $new_local_balance - $current_local_balance;

                if (abs($adjustment) > 0.01) { // تجنب التعديلات الصغيرة جداً
                    // حفظ تفاصيل التعديل
                    $this->db->query("INSERT INTO cod_currency_revaluation_details SET 
                        revaluation_id = '" . (int)$revaluation_id . "',
                        account_id = '" . (int)$account['account_id'] . "',
                        currency_code = '" . $this->db->escape($currency_code) . "',
                        foreign_balance = '" . (float)$foreign_balance . "',
                        old_rate = '" . (float)$current_rate . "',
                        new_rate = '" . (float)$new_rate . "',
                        old_local_balance = '" . (float)$current_local_balance . "',
                        new_local_balance = '" . (float)$new_local_balance . "',
                        adjustment_amount = '" . (float)$adjustment . "',
                        date_added = NOW()
                    ");

                    $total_adjustment += $adjustment;
                }
            }
        }

        // تحديث سعر الصرف في النظام
        $this->db->query("UPDATE cod_currency SET 
            value = '" . (float)$new_rate . "',
            date_modified = NOW()
            WHERE code = '" . $this->db->escape($currency_code) . "'
        ");

        return $total_adjustment;
    }

    /**
     * إنشاء القيود المحاسبية لإعادة التقييم
     */
    private function createRevaluationJournalEntries($revaluation_id) {
        // جلب تفاصيل إعادة التقييم
        $details_query = $this->db->query("SELECT * FROM cod_currency_revaluation_details WHERE revaluation_id = '" . (int)$revaluation_id . "'");

        if ($details_query->num_rows) {
            // إنشاء قيد محاسبي رئيسي
            $this->load->model('accounts/journal');
            
            $journal_data = array(
                'reference' => 'REV-' . $revaluation_id,
                'description' => 'Currency Revaluation - ' . date('Y-m-d'),
                'journal_date' => date('Y-m-d'),
                'entries' => array()
            );

            $revaluation_gain_account = $this->config->get('config_currency_revaluation_gain_account');
            $revaluation_loss_account = $this->config->get('config_currency_revaluation_loss_account');

            foreach ($details_query->rows as $detail) {
                $adjustment = (float)$detail['adjustment_amount'];
                
                if ($adjustment != 0) {
                    // قيد الحساب المتأثر
                    $journal_data['entries'][] = array(
                        'account_id' => $detail['account_id'],
                        'debit' => $adjustment > 0 ? $adjustment : 0,
                        'credit' => $adjustment < 0 ? abs($adjustment) : 0,
                        'description' => 'Currency revaluation adjustment - ' . $detail['currency_code']
                    );

                    // قيد حساب أرباح/خسائر إعادة التقييم
                    $journal_data['entries'][] = array(
                        'account_id' => $adjustment > 0 ? $revaluation_gain_account : $revaluation_loss_account,
                        'debit' => $adjustment < 0 ? abs($adjustment) : 0,
                        'credit' => $adjustment > 0 ? $adjustment : 0,
                        'description' => 'Currency revaluation ' . ($adjustment > 0 ? 'gain' : 'loss') . ' - ' . $detail['currency_code']
                    );
                }
            }

            if (!empty($journal_data['entries'])) {
                $journal_id = $this->model_accounts_journal->addJournalEntry($journal_data);
                
                // ربط القيد بإعادة التقييم
                $this->db->query("UPDATE cod_currency_revaluation SET 
                    journal_id = '" . (int)$journal_id . "'
                    WHERE revaluation_id = '" . (int)$revaluation_id . "'
                ");
            }
        }
    }

    /**
     * معاينة إعادة التقييم
     */
    public function previewRevaluation($data) {
        $preview = array();

        foreach ($data['currencies'] as $currency_code) {
            if (isset($data['exchange_rates'][$currency_code])) {
                $new_rate = (float)$data['exchange_rates'][$currency_code];
                
                // جلب سعر الصرف الحالي
                $current_rate_query = $this->db->query("SELECT value FROM cod_currency WHERE code = '" . $this->db->escape($currency_code) . "'");
                $current_rate = $current_rate_query->num_rows ? (float)$current_rate_query->row['value'] : 1;

                // جلب الحسابات المتأثرة
                $accounts = $this->getForeignCurrencyAccountsForCurrency($currency_code, $data['account_types']);

                $currency_preview = array(
                    'currency_code' => $currency_code,
                    'current_rate' => $current_rate,
                    'new_rate' => $new_rate,
                    'rate_change' => (($new_rate - $current_rate) / $current_rate) * 100,
                    'accounts' => array(),
                    'total_adjustment' => 0
                );

                foreach ($accounts as $account) {
                    $foreign_balance = $this->getAccountForeignCurrencyBalance($account['account_id'], $currency_code, $data['revaluation_date']);
                    
                    if ($foreign_balance != 0) {
                        $current_local_balance = $foreign_balance * $current_rate;
                        $new_local_balance = $foreign_balance * $new_rate;
                        $adjustment = $new_local_balance - $current_local_balance;

                        if (abs($adjustment) > 0.01) {
                            $currency_preview['accounts'][] = array(
                                'account_code' => $account['code'],
                                'account_name' => $account['name'],
                                'foreign_balance' => $foreign_balance,
                                'current_local_balance' => $current_local_balance,
                                'new_local_balance' => $new_local_balance,
                                'adjustment' => $adjustment
                            );

                            $currency_preview['total_adjustment'] += $adjustment;
                        }
                    }
                }

                $preview[] = $currency_preview;
            }
        }

        return $preview;
    }

    /**
     * اعتماد إعادة التقييم
     */
    public function approveRevaluation($revaluation_id, $approval_notes = '') {
        $this->db->query("START TRANSACTION");

        try {
            // تحديث حالة إعادة التقييم
            $this->db->query("UPDATE cod_currency_revaluation SET 
                status = 'approved',
                approved_by = '" . (int)$this->user->getId() . "',
                approval_notes = '" . $this->db->escape($approval_notes) . "',
                date_approved = NOW()
                WHERE revaluation_id = '" . (int)$revaluation_id . "' AND status = 'pending'
            ");

            if ($this->db->countAffected() > 0) {
                // إنشاء القيود المحاسبية
                $this->createRevaluationJournalEntries($revaluation_id);
                
                $this->db->query("COMMIT");
                return true;
            } else {
                $this->db->query("ROLLBACK");
                return false;
            }

        } catch (Exception $e) {
            $this->db->query("ROLLBACK");
            throw $e;
        }
    }

    /**
     * عكس إعادة التقييم
     */
    public function reverseRevaluation($revaluation_id, $reverse_reason) {
        $this->db->query("START TRANSACTION");

        try {
            // جلب بيانات إعادة التقييم
            $revaluation_query = $this->db->query("SELECT * FROM cod_currency_revaluation WHERE revaluation_id = '" . (int)$revaluation_id . "'");
            
            if (!$revaluation_query->num_rows) {
                throw new Exception('Revaluation not found');
            }

            $revaluation = $revaluation_query->row;

            // عكس القيود المحاسبية إذا كانت موجودة
            if ($revaluation['journal_id']) {
                $this->load->model('accounts/journal');
                $this->model_accounts_journal->reverseJournalEntry($revaluation['journal_id'], 'Currency revaluation reversal: ' . $reverse_reason);
            }

            // إعادة أسعار الصرف للقيم السابقة
            $details_query = $this->db->query("SELECT DISTINCT currency_code, old_rate FROM cod_currency_revaluation_details WHERE revaluation_id = '" . (int)$revaluation_id . "'");
            
            foreach ($details_query->rows as $detail) {
                $this->db->query("UPDATE cod_currency SET 
                    value = '" . (float)$detail['old_rate'] . "',
                    date_modified = NOW()
                    WHERE code = '" . $this->db->escape($detail['currency_code']) . "'
                ");
            }

            // تحديث حالة إعادة التقييم
            $this->db->query("UPDATE cod_currency_revaluation SET 
                status = 'reversed',
                reversed_by = '" . (int)$this->user->getId() . "',
                reverse_reason = '" . $this->db->escape($reverse_reason) . "',
                date_reversed = NOW()
                WHERE revaluation_id = '" . (int)$revaluation_id . "'
            ");

            $this->db->query("COMMIT");
            return true;

        } catch (Exception $e) {
            $this->db->query("ROLLBACK");
            throw $e;
        }
    }

    /**
     * جلب العملات النشطة
     */
    public function getActiveCurrencies() {
        $query = $this->db->query("SELECT c.*, cd.title 
            FROM cod_currency c 
            LEFT JOIN cod_currency_description cd ON (c.currency_id = cd.currency_id AND cd.language_id = '" . (int)$this->config->get('config_language_id') . "')
            WHERE c.status = '1' AND c.code != '" . $this->db->escape($this->config->get('config_currency')) . "'
            ORDER BY cd.title ASC
        ");

        return $query->rows;
    }

    /**
     * جلب آخر عمليات إعادة التقييم
     */
    public function getRecentRevaluations($limit = 10) {
        $query = $this->db->query("SELECT cr.*, u.username as created_by_name, u2.username as approved_by_name
            FROM cod_currency_revaluation cr
            LEFT JOIN cod_user u ON (cr.created_by = u.user_id)
            LEFT JOIN cod_user u2 ON (cr.approved_by = u2.user_id)
            ORDER BY cr.date_created DESC
            LIMIT " . (int)$limit
        );

        return $query->rows;
    }

    /**
     * جلب أسعار الصرف الحالية
     */
    public function getCurrentExchangeRates() {
        $query = $this->db->query("SELECT c.code, c.value, cd.title, c.date_modified
            FROM cod_currency c 
            LEFT JOIN cod_currency_description cd ON (c.currency_id = cd.currency_id AND cd.language_id = '" . (int)$this->config->get('config_language_id') . "')
            WHERE c.status = '1'
            ORDER BY cd.title ASC
        ");

        return $query->rows;
    }

    /**
     * جلب الحسابات المتأثرة بالعملات الأجنبية
     */
    public function getForeignCurrencyAccounts() {
        $query = $this->db->query("SELECT DISTINCT a.account_id, a.code, ad.name, a.account_type
            FROM cod_accounts a
            LEFT JOIN cod_account_description ad ON (a.account_id = ad.account_id AND ad.language_id = '" . (int)$this->config->get('config_language_id') . "')
            INNER JOIN cod_journal_entries jel ON (a.account_id = jel.account_id)
            WHERE jel.currency != '" . $this->db->escape($this->config->get('config_currency')) . "'
            AND a.status = '1'
            ORDER BY a.code ASC
        ");

        return $query->rows;
    }

    /**
     * جلب الحسابات المتأثرة بعملة معينة
     */
    private function getForeignCurrencyAccountsForCurrency($currency_code, $account_types = array()) {
        $type_filter = '';
        if (!empty($account_types)) {
            $type_filter = " AND a.account_type IN ('" . implode("','", array_map(array($this->db, 'escape'), $account_types)) . "')";
        }

        $query = $this->db->query("SELECT DISTINCT a.account_id, a.code, ad.name, a.account_type
            FROM cod_accounts a
            LEFT JOIN cod_account_description ad ON (a.account_id = ad.account_id AND ad.language_id = '" . (int)$this->config->get('config_language_id') . "')
            INNER JOIN cod_journal_entries jel ON (a.account_id = jel.account_id)
            WHERE jel.currency = '" . $this->db->escape($currency_code) . "'
            AND a.status = '1'
            " . $type_filter . "
            ORDER BY a.code ASC
        ");

        return $query->rows;
    }

    /**
     * جلب رصيد الحساب بالعملة الأجنبية
     */
    private function getAccountForeignCurrencyBalance($account_id, $currency_code, $as_of_date) {
        $query = $this->db->query("SELECT
            SUM(jel.debit - jel.credit) as balance
            FROM cod_journal_entries jel
            INNER JOIN cod_journals je ON (jel.journal_id = je.journal_id)
            WHERE jel.account_id = '" . (int)$account_id . "'
            AND jel.currency = '" . $this->db->escape($currency_code) . "'
            AND je.journal_date <= '" . $this->db->escape($as_of_date) . "'
            AND je.status = 'posted'
        ");

        return $query->num_rows ? (float)$query->row['balance'] : 0;
    }

    /**
     * جلب أسعار الصرف لتاريخ معين
     */
    public function getExchangeRatesForDate($currency_code, $date) {
        // يمكن تطوير هذه الدالة للحصول على أسعار الصرف من مصادر خارجية
        $query = $this->db->query("SELECT value FROM cod_currency WHERE code = '" . $this->db->escape($currency_code) . "'");
        
        return $query->num_rows ? array(
            'current_rate' => (float)$query->row['value'],
            'suggested_rate' => (float)$query->row['value'], // يمكن تحديثها من API خارجي
            'last_updated' => date('Y-m-d H:i:s')
        ) : null;
    }

    /**
     * جلب بيانات إعادة التقييم
     */
    public function getRevaluation($revaluation_id) {
        $query = $this->db->query("SELECT cr.*, u.username as created_by_name, u2.username as approved_by_name
            FROM cod_currency_revaluation cr
            LEFT JOIN cod_user u ON (cr.created_by = u.user_id)
            LEFT JOIN cod_user u2 ON (cr.approved_by = u2.user_id)
            WHERE cr.revaluation_id = '" . (int)$revaluation_id . "'
        ");

        return $query->num_rows ? $query->row : null;
    }

    /**
     * جلب تفاصيل إعادة التقييم
     */
    public function getRevaluationDetails($revaluation_id) {
        $query = $this->db->query("SELECT crd.*, a.code as account_code, ad.name as account_name
            FROM cod_currency_revaluation_details crd
            LEFT JOIN cod_accounts a ON (crd.account_id = a.account_id)
            LEFT JOIN cod_account_description ad ON (a.account_id = ad.account_id AND ad.language_id = '" . (int)$this->config->get('config_language_id') . "')
            WHERE crd.revaluation_id = '" . (int)$revaluation_id . "'
            ORDER BY a.code ASC
        ");

        return $query->rows;
    }

    /**
     * جلب القيود المحاسبية لإعادة التقييم
     */
    public function getRevaluationJournalEntries($revaluation_id) {
        $query = $this->db->query("SELECT cr.journal_id FROM cod_currency_revaluation cr WHERE cr.revaluation_id = '" . (int)$revaluation_id . "'");
        
        if ($query->num_rows && $query->row['journal_id']) {
            $this->load->model('accounts/journal');
            return $this->model_accounts_journal->getJournalEntry($query->row['journal_id']);
        }

        return null;
    }

    /**
     * إنشاء مرجع إعادة التقييم
     */
    private function generateRevaluationReference() {
        $prefix = 'REV';
        $year = date('Y');
        $month = date('m');
        
        // جلب آخر رقم
        $query = $this->db->query("SELECT reference FROM cod_currency_revaluation 
            WHERE reference LIKE '" . $prefix . "-" . $year . $month . "%' 
            ORDER BY reference DESC LIMIT 1
        ");

        if ($query->num_rows) {
            $last_number = (int)substr($query->row['reference'], -4);
            $new_number = $last_number + 1;
        } else {
            $new_number = 1;
        }

        return $prefix . '-' . $year . $month . '-' . str_pad($new_number, 4, '0', STR_PAD_LEFT);
    }
}
