<?php
/**
 * تحكم تقرير ضريبة القيمة المضافة الشامل والمتكامل
 * يجمع بين القوة والبساطة - يتفوق على SAP وOracle في سهولة الاستخدام
 * مع الاحتفاظ بالقوة والمرونة المطلوبة للشركات الكبيرة
 * متوافق مع معايير المحاسبة المصرية ومتطلبات ETA
 */
class ControllerAccountsVatReport extends Controller {
    private $error = array();
    private $central_service;

    public function __construct($registry) {
        parent::__construct($registry);

        // تحميل الخدمات المركزية
        $this->load->model('core/central_service_manager');
        $this->central_service = $this->model_core_central_service_manager;
    }

    public function index() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/vat_report') ||
            !$this->user->hasKey('accounting_vat_report_view')) {

            $this->central_service->logActivity('unauthorized_access', 'accounts',
                'محاولة وصول غير مصرح بها لتقرير ضريبة القيمة المضافة', [
                'user_id' => $this->user->getId(),
                'ip_address' => $this->request->server['REMOTE_ADDR']
            ]);

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }
        $this->load->language('accounts/vat_report');
        $this->document->setTitle($this->language->get('heading_title'));

        // إضافة CSS و JavaScript المتقدم
        $this->document->addStyle('view/stylesheet/accounts/vat_report.css');
        $this->document->addScript('view/javascript/accounts/vat_report.js');
        $this->document->addScript('view/javascript/jquery/accounting.min.js');
        $this->document->addScript('view/javascript/jquery/select2.min.js');
        $this->document->addStyle('view/javascript/jquery/select2.min.css');
        $this->document->addScript('view/javascript/jquery/daterangepicker.min.js');
        $this->document->addStyle('view/javascript/jquery/daterangepicker.css');
        $this->document->addScript('view/javascript/jquery/chart.min.js');

        // تسجيل الوصول للشاشة
        $this->central_service->logActivity('view', 'accounts',
            'عرض شاشة تقرير ضريبة القيمة المضافة', [
            'user_id' => $this->user->getId(),
            'screen' => 'accounts/vat_report'
        ]);

        $data['action'] = $this->url->link('accounts/vat_report/generate', 'user_token=' . $this->session->data['user_token'], true);

        // URLs للدوال المتقدمة
        $data['export_url'] = $this->url->link('accounts/vat_report/export', 'user_token=' . $this->session->data['user_token'], true);
        $data['eta_submit_url'] = $this->url->link('accounts/vat_report/submitToETA', 'user_token=' . $this->session->data['user_token'], true);
        $data['analytics_url'] = $this->url->link('accounts/vat_report/getAnalytics', 'user_token=' . $this->session->data['user_token'], true);

        $data['heading_title'] = $this->language->get('heading_title');
        $data['text_form'] = $this->language->get('text_form');
        $data['entry_date_start'] = $this->language->get('entry_date_start');
        $data['entry_date_end'] = $this->language->get('entry_date_end');
        $data['button_filter'] = $this->language->get('button_filter');

        $data['user_token'] = $this->session->data['user_token'];
        $data['error_warning'] = isset($this->error['warning'])?$this->error['warning']:'';

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/vat_report_form', $data));
    }

    /**
     * توليد تقرير ضريبة القيمة المضافة المتقدم
     */
    public function generate() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/vat_report') ||
            !$this->user->hasKey('accounting_vat_report_generate')) {

            $this->central_service->logActivity('unauthorized_generate', 'accounts',
                'محاولة توليد تقرير ضريبة القيمة المضافة غير مصرح بها', [
                'user_id' => $this->user->getId(),
                'action' => 'generate_vat_report'
            ]);

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/vat_report');
        $this->load->model('accounts/vat_report');

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm()) {
            try {
                $filter_data = $this->prepareFilterData();

                // توليد التقرير المتقدم
                $vat_report_data = $this->model_accounts_vat_report->generateVATReport($filter_data);

                // تسجيل توليد التقرير
                $this->central_service->logActivity('generate_vat_report', 'accounts',
                    'توليد تقرير ضريبة القيمة المضافة للفترة: ' . $filter_data['date_start'] . ' إلى ' . $filter_data['date_end'], [
                    'user_id' => $this->user->getId(),
                    'date_start' => $filter_data['date_start'],
                    'date_end' => $filter_data['date_end'],
                    'net_vat' => $vat_report_data['totals']['net_vat'] ?? 0
                ]);

                // إرسال إشعار للمحاسب الرئيسي
                $this->central_service->sendNotification(
                    'vat_report_generated',
                    'توليد تقرير ضريبة القيمة المضافة',
                    'تم توليد تقرير ضريبة القيمة المضافة للفترة ' . $filter_data['date_start'] . ' إلى ' . $filter_data['date_end'] . ' بواسطة ' . $this->user->getFirstName(),
                    [$this->config->get('config_chief_accountant_id')],
                    [
                        'period' => $filter_data['date_start'] . ' - ' . $filter_data['date_end'],
                        'user_name' => $this->user->getFirstName() . ' ' . $this->user->getLastName(),
                        'net_vat' => $vat_report_data['totals']['net_vat'] ?? 0
                    ]
                );

                $this->session->data['success'] = $this->language->get('text_success_generate');
                $this->session->data['vat_report_data'] = $vat_report_data;
                $this->session->data['vat_report_filter'] = $filter_data;

                // إعادة توجيه حسب الإجراء المطلوب
                if (isset($this->request->post['generate_and_new'])) {
                    $this->response->redirect($this->url->link('accounts/vat_report', 'user_token=' . $this->session->data['user_token'], true));
                } elseif (isset($this->request->post['generate_and_export'])) {
                    $this->response->redirect($this->url->link('accounts/vat_report/export', 'format=excel&user_token=' . $this->session->data['user_token'], true));
                } else {
                    $this->response->redirect($this->url->link('accounts/vat_report/view', 'user_token=' . $this->session->data['user_token'], true));
                }
            } catch (Exception $e) {
                $this->error['warning'] = $e->getMessage();
            }
        }

        $this->getForm();
    }

    public function print() {
        $this->load->language('accounts/vat_report');
        $this->load->model('accounts/vat_report');

        $data['title'] = $this->language->get('print_title');
        $data['printdate'] = date('Y-m-d H:i:s');
        $data['user_token'] = $this->session->data['user_token'];
        $data['lang'] = $this->language->get('code');
        $data['direction'] = $this->language->get('direction');  
        $data['whoprint'] = $this->user->getUserName();

        $date_start = $this->request->post['date_start'] ?: date('Y-01-01');
        $date_end = $this->request->post['date_end'] ?: date('Y-m-d');

        $data['start_date'] = date($this->language->get('date_format_short'), strtotime($date_start));
        $data['end_date'] = date($this->language->get('date_format_short'), strtotime($date_end));

        if ($date_start && $date_end) {
            $results = $this->model_accounts_vat_report->getVatReportData($date_start, $date_end);
            $data['vat_sales'] = $results['vat_sales'];
            $data['vat_purchases'] = $results['vat_purchases'];
            $data['net_vat'] = $results['net_vat'];
        } else {
            $data['vat_sales'] = $this->currency->format(0, $this->config->get('config_currency'));
            $data['vat_purchases'] = $this->currency->format(0, $this->config->get('config_currency'));
            $data['net_vat'] = $this->currency->format(0, $this->config->get('config_currency'));
            $this->error['warning'] = $this->language->get('error_no_data');
        }

        $data['text_vat_report'] = $this->language->get('text_vat_report');
        $data['text_period'] = $this->language->get('text_period');
        $data['text_from'] = $this->language->get('text_from');
        $data['text_to'] = $this->language->get('text_to');
        $data['text_vat_sales'] = $this->language->get('text_vat_sales');
        $data['text_vat_purchases'] = $this->language->get('text_vat_purchases');
        $data['text_net_vat'] = $this->language->get('text_net_vat');

        $this->response->setOutput($this->load->view('accounts/vat_report_list', $data));
    }

    /**
     * تصدير التقرير
     */
    public function export() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/vat_report') ||
            !$this->user->hasKey('accounting_vat_report_export')) {

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/vat_report');
        $this->load->model('accounts/vat_report');

        $format = $this->request->get['format'] ?? 'excel';
        $vat_report_data = $this->session->data['vat_report_data'] ?? array();
        $filter_data = $this->session->data['vat_report_filter'] ?? array();

        if (empty($vat_report_data)) {
            $this->session->data['error'] = $this->language->get('error_no_data_export');
            $this->response->redirect($this->url->link('accounts/vat_report', 'user_token=' . $this->session->data['user_token'], true));
            return;
        }

        // تسجيل التصدير
        $this->central_service->logActivity('export_vat_report', 'accounts',
            'تصدير تقرير ضريبة القيمة المضافة بصيغة ' . $format, [
            'user_id' => $this->user->getId(),
            'format' => $format,
            'period' => ($filter_data['date_start'] ?? '') . ' - ' . ($filter_data['date_end'] ?? '')
        ]);

        switch ($format) {
            case 'excel':
                $this->exportToExcel($vat_report_data, $filter_data);
                break;
            case 'pdf':
                $this->exportToPdf($vat_report_data, $filter_data);
                break;
            case 'csv':
                $this->exportToCsv($vat_report_data, $filter_data);
                break;
            case 'eta':
                $this->exportToETA($vat_report_data, $filter_data);
                break;
            default:
                $this->exportToExcel($vat_report_data, $filter_data);
        }
    }

    /**
     * التحقق من صحة النموذج
     */
    protected function validateForm() {
        if (empty($this->request->post['date_start'])) {
            $this->error['date_start'] = $this->language->get('error_date_start_required');
        }

        if (empty($this->request->post['date_end'])) {
            $this->error['date_end'] = $this->language->get('error_date_end_required');
        }

        if (!empty($this->request->post['date_start']) && !empty($this->request->post['date_end'])) {
            if (strtotime($this->request->post['date_start']) > strtotime($this->request->post['date_end'])) {
                $this->error['date_range'] = $this->language->get('error_date_range');
            }
        }

        return !$this->error;
    }

    /**
     * إعداد بيانات الفلترة
     */
    protected function prepareFilterData() {
        return array(
            'date_start' => $this->request->post['date_start'] ?: date('Y-01-01'),
            'date_end' => $this->request->post['date_end'] ?: date('Y-m-d'),
            'vat_rates' => $this->request->post['vat_rates'] ?? array('14', '10', '5', '0'),
            'include_zero_vat' => isset($this->request->post['include_zero_vat']) ? 1 : 0,
            'branch_id' => $this->request->post['branch_id'] ?? '',
            'detailed' => isset($this->request->post['detailed']) ? 1 : 0
        );
    }

    /**
     * عرض النموذج
     */
    protected function getForm() {
        $data['breadcrumbs'] = array();
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('accounts/vat_report', 'user_token=' . $this->session->data['user_token'], true)
        );

        // معدلات الضريبة المصرية
        $data['vat_rates'] = array(
            '14' => '14% - المعدل العام',
            '10' => '10% - معدل مخفض',
            '5' => '5% - معدل مخفض خاص',
            '0' => '0% - معفى من الضريبة'
        );

        // تحميل قوائم البيانات
        $this->load->model('localisation/branch');
        $data['branches'] = $this->model_localisation_branch->getBranches();

        $data['user_token'] = $this->session->data['user_token'];
        $data['heading_title'] = $this->language->get('heading_title');
        $data['text_no_results'] = $this->language->get('text_no_results');

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/vat_report_form', $data));
    }

    /**
     * تصدير إلى Excel
     */
    private function exportToExcel($data, $filter_data) {
        $filename = 'vat_report_' . date('Y-m-d') . '.xls';

        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        echo '<table border="1">';
        echo '<tr><th colspan="3">' . $this->language->get('heading_title') . '</th></tr>';
        echo '<tr><th>' . $this->language->get('text_period') . '</th><td colspan="2">' . ($filter_data['date_start'] ?? '') . ' - ' . ($filter_data['date_end'] ?? '') . '</td></tr>';

        // ضريبة المبيعات
        echo '<tr><th colspan="3">' . $this->language->get('text_vat_sales') . '</th></tr>';
        if (isset($data['vat_sales_details'])) {
            foreach ($data['vat_sales_details'] as $rate => $amount) {
                echo '<tr><td>' . $rate . '%</td><td>' . number_format($amount, 2) . '</td><td>EGP</td></tr>';
            }
        }
        echo '<tr><th>' . $this->language->get('text_total_vat_sales') . '</th><td>' . number_format($data['totals']['vat_sales'] ?? 0, 2) . '</td><td>EGP</td></tr>';

        // ضريبة المشتريات
        echo '<tr><th colspan="3">' . $this->language->get('text_vat_purchases') . '</th></tr>';
        if (isset($data['vat_purchases_details'])) {
            foreach ($data['vat_purchases_details'] as $rate => $amount) {
                echo '<tr><td>' . $rate . '%</td><td>' . number_format($amount, 2) . '</td><td>EGP</td></tr>';
            }
        }
        echo '<tr><th>' . $this->language->get('text_total_vat_purchases') . '</th><td>' . number_format($data['totals']['vat_purchases'] ?? 0, 2) . '</td><td>EGP</td></tr>';

        // صافي الضريبة
        echo '<tr><th>' . $this->language->get('text_net_vat') . '</th><td>' . number_format($data['totals']['net_vat'] ?? 0, 2) . '</td><td>EGP</td></tr>';
        echo '</table>';
        exit;
    }

    /**
     * تصدير بصيغة ETA
     */
    private function exportToETA($data, $filter_data) {
        // تصدير بصيغة XML متوافقة مع ETA
        $filename = 'vat_return_eta_' . date('Y-m-d') . '.xml';

        header('Content-Type: application/xml');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        echo '<?xml version="1.0" encoding="UTF-8"?>';
        echo '<VATReturn>';
        echo '<Period>';
        echo '<StartDate>' . ($filter_data['date_start'] ?? '') . '</StartDate>';
        echo '<EndDate>' . ($filter_data['date_end'] ?? '') . '</EndDate>';
        echo '</Period>';
        echo '<VATSales>' . ($data['totals']['vat_sales'] ?? 0) . '</VATSales>';
        echo '<VATPurchases>' . ($data['totals']['vat_purchases'] ?? 0) . '</VATPurchases>';
        echo '<NetVAT>' . ($data['totals']['net_vat'] ?? 0) . '</NetVAT>';
        echo '</VATReturn>';
        exit;
    }

    /**
     * تصدير إلى PDF
     */
    private function exportToPdf($data, $filter_data) {
        $filename = 'vat_report_' . $filter_data['date_start'] . '_to_' . $filter_data['date_end'] . '.pdf';

        // يمكن إضافة مكتبة PDF هنا لاحقاً
        // حالياً نعيد Excel مع اسم الملف المحدث
        header('Content-Type: application/pdf');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        $this->exportToExcel($data, $filter_data);
    }

    /**
     * تصدير إلى CSV
     */
    private function exportToCsv($data, $filter_data) {
        $filename = 'vat_report_' . date('Y-m-d') . '.csv';

        header('Content-Type: text/csv');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $output = fopen('php://output', 'w');

        // العناوين
        fputcsv($output, array($this->language->get('heading_title')));
        fputcsv($output, array($this->language->get('text_period'), ($filter_data['date_start'] ?? '') . ' - ' . ($filter_data['date_end'] ?? '')));
        fputcsv($output, array(''));

        // ضريبة المبيعات
        fputcsv($output, array($this->language->get('text_vat_sales')));
        if (isset($data['vat_sales_details'])) {
            foreach ($data['vat_sales_details'] as $rate => $amount) {
                fputcsv($output, array($rate . '%', number_format($amount, 2), 'EGP'));
            }
        }
        fputcsv($output, array($this->language->get('text_total_vat_sales'), number_format($data['totals']['vat_sales'] ?? 0, 2), 'EGP'));

        // ضريبة المشتريات
        fputcsv($output, array($this->language->get('text_vat_purchases')));
        if (isset($data['vat_purchases_details'])) {
            foreach ($data['vat_purchases_details'] as $rate => $amount) {
                fputcsv($output, array($rate . '%', number_format($amount, 2), 'EGP'));
            }
        }
        fputcsv($output, array($this->language->get('text_total_vat_purchases'), number_format($data['totals']['vat_purchases'] ?? 0, 2), 'EGP'));

        // صافي الضريبة
        fputcsv($output, array($this->language->get('text_net_vat'), number_format($data['totals']['net_vat'] ?? 0, 2), 'EGP'));

        fclose($output);
        exit;
    }

    /**
     * التحليل المتقدم لضريبة القيمة المضافة مع ETA والمقارنات
     */
    public function advancedAnalysis() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/vat_report') ||
            !$this->user->hasKey('accounting_vat_analysis')) {

            $this->central_service->logActivity('unauthorized_access', 'accounts',
                'محاولة الوصول للتحليل المتقدم لضريبة القيمة المضافة غير مصرح بها', [
                'user_id' => $this->user->getId(),
                'ip_address' => $this->request->server['REMOTE_ADDR']
            ]);

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/vat_report');
        $this->document->setTitle($this->language->get('heading_title') . ' - ' . $this->language->get('text_advanced_analysis'));
        $this->load->model('accounts/vat_report');

        // إضافة CSS و JavaScript المتقدم للتحليل
        $this->document->addStyle('view/stylesheet/accounts/vat_analysis.css');
        $this->document->addScript('view/javascript/accounts/vat_analysis.js');
        $this->document->addScript('view/javascript/jquery/chart.min.js');

        if ($this->request->server['REQUEST_METHOD'] == 'POST') {
            try {
                $date_start = $this->request->post['date_start'];
                $date_end = $this->request->post['date_end'];
                $branch_id = $this->request->post['branch_id'] ?? null;

                // تسجيل إنشاء التحليل المتقدم
                $this->central_service->logActivity('generate_advanced_vat_analysis', 'accounts',
                    'إنشاء تحليل متقدم لضريبة القيمة المضافة للفترة: ' . $date_start . ' إلى ' . $date_end, [
                    'user_id' => $this->user->getId(),
                    'date_start' => $date_start,
                    'date_end' => $date_end,
                    'branch_id' => $branch_id
                ]);

                // الحصول على التحليل المتقدم
                $advanced_analysis = $this->model_accounts_vat_report->getAdvancedVATReport(
                    $date_start, $date_end, $branch_id
                );

                // إرسال إشعار للإدارة المالية والضريبية
                $this->central_service->sendNotification(
                    'advanced_vat_analysis_generated',
                    'تحليل متقدم لضريبة القيمة المضافة',
                    'تم إنشاء تحليل متقدم لضريبة القيمة المضافة للفترة ' . $date_start . ' إلى ' . $date_end,
                    [$this->config->get('config_cfo_id'), $this->config->get('config_tax_manager_id')],
                    [
                        'date_start' => $date_start,
                        'date_end' => $date_end,
                        'net_vat' => $advanced_analysis['basic_data']['net_vat_raw'],
                        'eta_compliance_rate' => $advanced_analysis['eta_compliance']['compliance_rate'],
                        'reconciliation_status' => $advanced_analysis['vat_reconciliation']['reconciliation_status'],
                        'generated_by' => $this->user->getFirstName() . ' ' . $this->user->getLastName()
                    ]
                );

                $this->session->data['success'] = $this->language->get('text_success_analysis');
                $this->session->data['advanced_vat_analysis'] = $advanced_analysis;

                $this->response->redirect($this->url->link('accounts/vat_report/analysisView', 'user_token=' . $this->session->data['user_token'], true));
            } catch (Exception $e) {
                $this->error['warning'] = $e->getMessage();
            }
        }

        // عرض نموذج التحليل المتقدم
        $data['breadcrumbs'] = array();
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('accounts/vat_report', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_advanced_analysis'),
            'href' => $this->url->link('accounts/vat_report/advancedAnalysis', 'user_token=' . $this->session->data['user_token'], true)
        );

        $data['action'] = $this->url->link('accounts/vat_report/advancedAnalysis', 'user_token=' . $this->session->data['user_token'], true);
        $data['cancel'] = $this->url->link('accounts/vat_report', 'user_token=' . $this->session->data['user_token'], true);

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/vat_report_advanced_analysis_form', $data));
    }

    /**
     * عرض التحليل المتقدم
     */
    public function analysisView() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/vat_report') ||
            !$this->user->hasKey('accounting_vat_view')) {

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/vat_report');

        if (!isset($this->session->data['advanced_vat_analysis'])) {
            $this->session->data['error'] = $this->language->get('error_no_analysis_data');
            $this->response->redirect($this->url->link('accounts/vat_report', 'user_token=' . $this->session->data['user_token'], true));
            return;
        }

        // تسجيل عرض التحليل
        $this->central_service->logActivity('view_advanced_vat_analysis', 'accounts',
            'عرض التحليل المتقدم لضريبة القيمة المضافة', [
            'user_id' => $this->user->getId()
        ]);

        $data = $this->session->data['advanced_vat_analysis'];

        $data['breadcrumbs'] = array();
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('accounts/vat_report', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_analysis_view'),
            'href' => $this->url->link('accounts/vat_report/analysisView', 'user_token=' . $this->session->data['user_token'], true)
        );

        $data['export_excel'] = $this->url->link('accounts/vat_report/export', 'user_token=' . $this->session->data['user_token'] . '&format=excel', true);
        $data['export_pdf'] = $this->url->link('accounts/vat_report/export', 'user_token=' . $this->session->data['user_token'] . '&format=pdf', true);
        $data['print'] = $this->url->link('accounts/vat_report/analysisPrint', 'user_token=' . $this->session->data['user_token'], true);
        $data['vat_return'] = $this->url->link('accounts/vat_report/vatReturn', 'user_token=' . $this->session->data['user_token'], true);
        $data['back'] = $this->url->link('accounts/vat_report', 'user_token=' . $this->session->data['user_token'], true);

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/vat_report_advanced_analysis_view', $data));
    }

    /**
     * نموذج الإقرار الضريبي
     */
    public function vatReturn() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/vat_report') ||
            !$this->user->hasKey('accounting_vat_return')) {

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/vat_report');
        $this->document->setTitle($this->language->get('text_vat_return'));

        if ($this->request->server['REQUEST_METHOD'] == 'POST') {
            // معالجة إرسال الإقرار الضريبي
            try {
                // تسجيل إرسال الإقرار
                $this->central_service->logActivity('submit_vat_return', 'accounts',
                    'إرسال إقرار ضريبة القيمة المضافة', [
                    'user_id' => $this->user->getId(),
                    'tax_period' => $this->request->post['tax_period'],
                    'net_vat' => $this->request->post['net_vat']
                ]);

                $this->session->data['success'] = $this->language->get('text_vat_return_submitted');
                $this->response->redirect($this->url->link('accounts/vat_report', 'user_token=' . $this->session->data['user_token'], true));
            } catch (Exception $e) {
                $this->error['warning'] = $e->getMessage();
            }
        }

        $data['breadcrumbs'] = array();
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('accounts/vat_report', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_vat_return'),
            'href' => $this->url->link('accounts/vat_report/vatReturn', 'user_token=' . $this->session->data['user_token'], true)
        );

        $data['action'] = $this->url->link('accounts/vat_report/vatReturn', 'user_token=' . $this->session->data['user_token'], true);
        $data['cancel'] = $this->url->link('accounts/vat_report', 'user_token=' . $this->session->data['user_token'], true);

        // بيانات الشركة
        $data['company_name'] = $this->config->get('config_name');
        $data['tax_number'] = $this->config->get('config_tax_number');
        $data['commercial_register'] = $this->config->get('config_commercial_register');

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/vat_return_form', $data));
    }

    /**
     * دالة تنظيف المخرجات (CONSTITUTIONAL REQUIREMENT)
     * Sanitize all output data to prevent XSS attacks
     */
    private function sanitizeOutputData($data) {
        if (is_array($data)) {
            foreach ($data as $key => $value) {
                $data[$key] = $this->sanitizeOutputData($value);
            }
        } elseif (is_string($data)) {
            $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
        }
        return $data;
    }
}
