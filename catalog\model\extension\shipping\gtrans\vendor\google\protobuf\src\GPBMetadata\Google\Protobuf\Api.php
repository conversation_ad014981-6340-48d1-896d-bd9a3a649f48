<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/protobuf/api.proto

namespace GPBMetadata\Google\Protobuf;

class Api
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        \GPBMetadata\Google\Protobuf\SourceContext::initOnce();
        \GPBMetadata\Google\Protobuf\Type::initOnce();
        $pool->internalAddGeneratedFile(hex2bin(
            "0ac8050a19676f6f676c652f70726f746f6275662f6170692e70726f746f" .
            "120f676f6f676c652e70726f746f6275661a1a676f6f676c652f70726f74" .
            "6f6275662f747970652e70726f746f2281020a03417069120c0a046e616d" .
            "6518012001280912280a076d6574686f647318022003280b32172e676f6f" .
            "676c652e70726f746f6275662e4d6574686f6412280a076f7074696f6e73" .
            "18032003280b32172e676f6f676c652e70726f746f6275662e4f7074696f" .
            "6e120f0a0776657273696f6e18042001280912360a0e736f757263655f63" .
            "6f6e7465787418052001280b321e2e676f6f676c652e70726f746f627566" .
            "2e536f75726365436f6e7465787412260a066d6978696e7318062003280b" .
            "32162e676f6f676c652e70726f746f6275662e4d6978696e12270a067379" .
            "6e74617818072001280e32172e676f6f676c652e70726f746f6275662e53" .
            "796e74617822d5010a064d6574686f64120c0a046e616d65180120012809" .
            "12180a10726571756573745f747970655f75726c18022001280912190a11" .
            "726571756573745f73747265616d696e6718032001280812190a11726573" .
            "706f6e73655f747970655f75726c180420012809121a0a12726573706f6e" .
            "73655f73747265616d696e6718052001280812280a076f7074696f6e7318" .
            "062003280b32172e676f6f676c652e70726f746f6275662e4f7074696f6e" .
            "12270a0673796e74617818072001280e32172e676f6f676c652e70726f74" .
            "6f6275662e53796e74617822230a054d6978696e120c0a046e616d651801" .
            "20012809120c0a04726f6f7418022001280942750a13636f6d2e676f6f67" .
            "6c652e70726f746f627566420841706950726f746f50015a2b676f6f676c" .
            "652e676f6c616e672e6f72672f67656e70726f746f2f70726f746f627566" .
            "2f6170693b617069a20203475042aa021e476f6f676c652e50726f746f62" .
            "75662e57656c6c4b6e6f776e5479706573620670726f746f33"
        ));

        static::$is_initialized = true;
    }
}

