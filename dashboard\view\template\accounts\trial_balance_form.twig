{{ header }}{{ column_left }}

<div id="content">
<link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css" rel="stylesheet" />
<script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>  
<style>
.select2-container--default .select2-selection--single {
  height: 36px;
  width:100%;
} 

.select2-container{width:100% !important;}
</style>

<div class="container-fluid">
    <div class="row no-print">
        <div class="col-md-12">
            <h2>{{ title }}</h2>
            <form action="{{ action }}" method="post" id="form-trial-balance" class="form-horizontal">
                <div class="form-group">
                    <label for="input-account-start" class="col-sm-2 control-label">{{ text_account_start }}</label>
                    <div class="col-sm-4">
                        <select name="account_start" id="input-account-start" class="form-control select2">
                            <option value=""></option>
                            {% for account in accounts %}
                            <option value="{{ account.account_code }}">{{ account.name }} ({{ account.account_code }})</option>
                            {% endfor %}
                        </select>
                    </div>
                    <label for="input-account-end" class="col-sm-2 control-label">{{ text_account_end }}</label>
                    <div class="col-sm-4">
                        <select name="account_end" id="input-account-end" class="form-control select2">
                            <option value=""></option>
                            {% for account in accounts %}
                                <option value="{{ account.account_code }}">{{ account.name }} ({{ account.account_code }})</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label for="input-date-start" class="col-sm-2 control-label">{{ text_date_start }}</label>
                    <div class="col-sm-4">
                        <input type="date" name="date_start" id="input-date-start" class="form-control">
                    </div>
                    <label for="input-date-end" class="col-sm-2 control-label">{{ text_date_end }}</label>
                    <div class="col-sm-4">
                        <input type="date" name="date_end" id="input-date-end" class="form-control">
                    </div>
                </div>
                <div class="text-right">
                    <button type="submit" class="btn btn-primary">{{ button_submit }}</button>
                </div>
            </form>
        </div>
    </div>
</div>
<script>
    $(document).ready(function() {
        $('.select2').select2();
    });
</script>
{{footer}}
