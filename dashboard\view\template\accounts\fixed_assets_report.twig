{{ header }}{{ column_left }}

<!-- Enterprise Grade Plus CSS for Fixed Assets Report -->
<style>
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --danger-color: #e74c3c;
    --warning-color: #f39c12;
    --info-color: #17a2b8;
    --assets-report-color: #16a085;
    --light-bg: #f8f9fa;
    --border-color: #dee2e6;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

.assets-report-container {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: var(--shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.assets-report-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--assets-report-color), var(--primary-color), var(--secondary-color));
}

.assets-report-header {
    text-align: center;
    border-bottom: 3px solid var(--assets-report-color);
    padding-bottom: 20px;
    margin-bottom: 30px;
    position: relative;
}

.assets-report-header h2 {
    color: var(--assets-report-color);
    font-weight: 700;
    font-size: 1.8rem;
    margin: 0;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.assets-report-summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.assets-report-summary-card {
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    text-align: center;
    box-shadow: var(--shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.assets-report-summary-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0,0,0,0.1);
}

.assets-report-summary-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
}

.assets-report-summary-card.total::before { background: var(--assets-report-color); }
.assets-report-summary-card.cost::before { background: var(--info-color); }
.assets-report-summary-card.depreciation::before { background: var(--warning-color); }
.assets-report-summary-card.net::before { background: var(--success-color); }

.assets-report-summary-card h4 {
    color: var(--primary-color);
    margin-bottom: 10px;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.assets-report-summary-card .amount {
    font-size: 1.4rem;
    font-weight: 700;
    font-family: 'Courier New', monospace;
    margin-bottom: 5px;
}

.assets-report-summary-card .description {
    font-size: 0.8rem;
    color: #6c757d;
    font-weight: 500;
}

.card-total .amount { color: var(--assets-report-color); }
.card-cost .amount { color: var(--info-color); }
.card-depreciation .amount { color: var(--warning-color); }
.card-net .amount { color: var(--success-color); }

.assets-report-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    background: #ffffff;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.assets-report-table th {
    background: linear-gradient(135deg, var(--assets-report-color), #138d75);
    color: white;
    padding: 15px 12px;
    text-align: center;
    font-weight: 600;
    font-size: 0.9rem;
    border-bottom: 2px solid var(--border-color);
}

.assets-report-table td {
    padding: 12px;
    border-bottom: 1px solid var(--border-color);
    text-align: center;
    transition: var(--transition);
}

.assets-report-table tbody tr:hover {
    background: var(--light-bg);
    transform: scale(1.005);
}

.assets-report-table tbody tr:nth-child(even) {
    background: #fafbfc;
}

.amount-cell {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    text-align: {{ direction == 'rtl' ? 'right' : 'left' }};
}

.amount-positive { 
    color: var(--success-color); 
    font-weight: 600;
}

.amount-negative { 
    color: var(--danger-color); 
    font-weight: 600;
}

.amount-neutral { 
    color: var(--secondary-color); 
    font-weight: 600;
}

/* Chart Container */
.chart-container {
    background: #ffffff;
    border-radius: var(--border-radius);
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: var(--shadow);
}

.chart-container canvas {
    max-height: 400px;
}

/* Filter Panel */
.filter-panel {
    background: linear-gradient(135deg, #f8f9fa, #ffffff);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: var(--shadow);
}

/* RTL Support */
[dir="rtl"] .assets-report-table {
    direction: rtl;
}

[dir="rtl"] .amount-cell {
    text-align: right;
}

/* Print Styles */
@media print {
    .assets-report-container {
        box-shadow: none;
        border: 1px solid #000;
    }
    
    .assets-report-table th {
        background: #f0f0f0 !important;
        color: #000 !important;
    }
    
    .btn, .card-header, .filter-panel {
        display: none !important;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .assets-report-table {
        font-size: 0.8rem;
    }
    
    .assets-report-table th,
    .assets-report-table td {
        padding: 8px 6px;
    }
    
    .assets-report-summary-cards {
        grid-template-columns: repeat(2, 1fr);
    }
}
</style>

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-{{ direction == 'rtl' ? 'start' : 'end' }}">
        <div class="btn-group" role="group" aria-label="{{ text_actions }}">
          <button type="button" class="btn btn-success btn-lg" onclick="generateReport()" 
                  data-bs-toggle="tooltip" title="{{ text_generate_report }}">
            <i class="fas fa-chart-line me-2"></i> {{ button_generate }}
          </button>
          <div class="btn-group" role="group">
            <button type="button" class="btn btn-info dropdown-toggle" data-bs-toggle="dropdown" 
                    aria-expanded="false" data-bs-toggle="tooltip" title="{{ text_export_options }}">
              <i class="fas fa-download me-2"></i> {{ text_export }}
            </button>
            <ul class="dropdown-menu">
              <li><a class="dropdown-item" href="#" onclick="exportReport('excel')">
                <i class="fas fa-file-excel text-success me-2"></i> Excel
              </a></li>
              <li><a class="dropdown-item" href="#" onclick="exportReport('pdf')">
                <i class="fas fa-file-pdf text-danger me-2"></i> PDF
              </a></li>
              <li><a class="dropdown-item" href="#" onclick="exportReport('csv')">
                <i class="fas fa-file-csv text-info me-2"></i> CSV
              </a></li>
              <li><hr class="dropdown-divider"></li>
              <li><a class="dropdown-item" href="#" onclick="printReport()">
                <i class="fas fa-print text-secondary me-2"></i> {{ text_print }}
              </a></li>
            </ul>
          </div>
          <button type="button" class="btn btn-outline-primary" onclick="showAnalysis()"
                  data-bs-toggle="tooltip" title="{{ text_assets_analysis }}">
            <i class="fas fa-analytics"></i>
          </button>
        </div>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li class="breadcrumb-item"><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>

  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible fade show">
      <i class="fas fa-exclamation-triangle"></i>
      {{ error_warning }}
      <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    {% endif %}

    {% if success %}
    <div class="alert alert-success alert-dismissible fade show">
      <i class="fas fa-check-circle"></i>
      {{ success }}
      <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    {% endif %}

    <!-- Filter Panel -->
    <div class="filter-panel">
      <h4>{{ text_report_filters }}</h4>
      <form id="assets-report-form" method="post" action="{{ action }}">
        <div class="row">
          <div class="col-md-3">
            <div class="form-group">
              <label for="date_start" class="form-label">{{ entry_date_start }}</label>
              <input type="date" name="date_start" id="date_start" value="{{ date_start }}" class="form-control" required>
            </div>
          </div>
          <div class="col-md-3">
            <div class="form-group">
              <label for="date_end" class="form-label">{{ entry_date_end }}</label>
              <input type="date" name="date_end" id="date_end" value="{{ date_end }}" class="form-control" required>
            </div>
          </div>
          <div class="col-md-2">
            <div class="form-group">
              <label for="category_id" class="form-label">{{ entry_category }}</label>
              <select name="category_id" id="category_id" class="form-control">
                <option value="">{{ text_all_categories }}</option>
                {% for category in categories %}
                <option value="{{ category.category_id }}"{% if category.category_id == category_id %} selected{% endif %}>{{ category.name }}</option>
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="col-md-2">
            <div class="form-group">
              <label for="status" class="form-label">{{ entry_status }}</label>
              <select name="status" id="status" class="form-control">
                <option value="">{{ text_all_statuses }}</option>
                <option value="active"{% if status == 'active' %} selected{% endif %}>{{ text_active }}</option>
                <option value="disposed"{% if status == 'disposed' %} selected{% endif %}>{{ text_disposed }}</option>
              </select>
            </div>
          </div>
          <div class="col-md-2">
            <div class="form-group">
              <label class="form-label">&nbsp;</label>
              <div class="d-grid">
                <button type="submit" class="btn btn-primary">
                  <i class="fas fa-search me-2"></i>{{ button_filter }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>

    <!-- Fixed Assets Report Content -->
    {% if assets %}
    <!-- Summary Cards -->
    <div class="assets-report-summary-cards">
      <div class="assets-report-summary-card card-total total">
        <h4>{{ text_total_assets }}</h4>
        <div class="amount">{{ summary.total_assets }}</div>
        <div class="description">{{ text_assets }}</div>
      </div>
      <div class="assets-report-summary-card card-cost cost">
        <h4>{{ text_total_cost }}</h4>
        <div class="amount">{{ summary.total_cost_formatted }}</div>
        <div class="description">{{ text_original_cost }}</div>
      </div>
      <div class="assets-report-summary-card card-depreciation depreciation">
        <h4>{{ text_total_depreciation }}</h4>
        <div class="amount">{{ summary.total_depreciation_formatted }}</div>
        <div class="description">{{ text_accumulated_depreciation }}</div>
      </div>
      <div class="assets-report-summary-card card-net net">
        <h4>{{ text_net_book_value }}</h4>
        <div class="amount">{{ summary.net_book_value_formatted }}</div>
        <div class="description">{{ text_current_value }}</div>
      </div>
    </div>

    <!-- Fixed Assets Report Table -->
    <div class="assets-report-container">
      <div class="assets-report-header">
        <h2>{{ text_fixed_assets_report_details }}</h2>
      </div>

      <div class="table-responsive">
        <table class="assets-report-table" id="assets-report-table">
          <thead>
            <tr>
              <th>{{ column_asset_name }}</th>
              <th>{{ column_asset_code }}</th>
              <th>{{ column_category }}</th>
              <th>{{ column_purchase_date }}</th>
              <th>{{ column_original_cost }}</th>
              <th>{{ column_depreciation_method }}</th>
              <th>{{ column_useful_life }}</th>
              <th>{{ column_accumulated_depreciation }}</th>
              <th>{{ column_net_book_value }}</th>
              <th>{{ column_status }}</th>
            </tr>
          </thead>
          <tbody>
            {% for asset in assets %}
            <tr data-asset-id="{{ asset.asset_id }}">
              <td>
                <strong>{{ asset.asset_name }}</strong>
                <br>
                <small class="text-muted">{{ asset.serial_number }}</small>
              </td>
              <td>{{ asset.asset_code }}</td>
              <td>{{ asset.category_name }}</td>
              <td>{{ asset.purchase_date_formatted }}</td>
              <td class="amount-cell">
                <strong class="amount-neutral">{{ asset.original_cost_formatted }}</strong>
              </td>
              <td>{{ asset.depreciation_method_text }}</td>
              <td>{{ asset.useful_life }} {{ text_years }}</td>
              <td class="amount-cell">
                <span class="amount-negative">{{ asset.accumulated_depreciation_formatted }}</span>
              </td>
              <td class="amount-cell">
                <strong class="amount-positive">{{ asset.net_book_value_formatted }}</strong>
              </td>
              <td>
                <span class="badge bg-{% if asset.status == 'active' %}success{% elseif asset.status == 'disposed' %}danger{% else %}secondary{% endif %}">
                  {{ asset.status_text }}
                </span>
              </td>
            </tr>
            {% endfor %}
          </tbody>
          <tfoot>
            <tr class="table-info">
              <th colspan="4">{{ text_totals }}</th>
              <th class="amount-cell">
                <strong class="amount-neutral">{{ summary.total_cost_formatted }}</strong>
              </th>
              <th colspan="2"></th>
              <th class="amount-cell">
                <strong class="amount-negative">{{ summary.total_depreciation_formatted }}</strong>
              </th>
              <th class="amount-cell">
                <strong class="amount-positive">{{ summary.net_book_value_formatted }}</strong>
              </th>
              <th></th>
            </tr>
          </tfoot>
        </table>
      </div>
    </div>

    <!-- Charts Section -->
    <div class="row">
      <div class="col-md-6">
        <div class="chart-container">
          <h4>{{ text_assets_by_category_chart }}</h4>
          <canvas id="assetsByCategoryChart"></canvas>
        </div>
      </div>
      <div class="col-md-6">
        <div class="chart-container">
          <h4>{{ text_depreciation_analysis_chart }}</h4>
          <canvas id="depreciationAnalysisChart"></canvas>
        </div>
      </div>
    </div>

    <!-- Asset Age Analysis -->
    <div class="row">
      <div class="col-md-12">
        <div class="chart-container">
          <h4>{{ text_asset_age_analysis_chart }}</h4>
          <canvas id="assetAgeAnalysisChart"></canvas>
        </div>
      </div>
    </div>
    {% else %}
    <div class="alert alert-info">
      <i class="fas fa-info-circle me-2"></i>
      {{ text_no_assets_found }}
    </div>
    {% endif %}
  </div>
</div>

<script>
// Enterprise Grade Plus JavaScript for Fixed Assets Report
class FixedAssetsReportManager {
    constructor() {
        this.initializeTooltips();
        this.initializeDataTable();
        this.initializeKeyboardShortcuts();
        this.initializeCharts();
    }

    initializeTooltips() {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }

    initializeDataTable() {
        const table = document.getElementById('assets-report-table');
        if (table && typeof $ !== 'undefined' && $.fn.DataTable) {
            $(table).DataTable({
                responsive: true,
                pageLength: 25,
                order: [[0, 'asc']], // Sort by asset name asc
                columnDefs: [
                    { targets: [4, 7, 8], className: 'text-end' },
                    { targets: [6], className: 'text-center' }
                ],
                language: {
                    url: '{{ direction == "rtl" ? "/view/javascript/jquery/datatables-ar.json" : "/view/javascript/jquery/datatables-en.json" }}'
                },
                footerCallback: function (row, data, start, end, display) {
                    var api = this.api();

                    // Calculate totals for visible rows
                    var totalCost = api.column(4, { page: 'current' }).data().reduce(function (a, b) {
                        return parseFloat(a) + parseFloat($(b).text().replace(/[^\d.-]/g, ''));
                    }, 0);

                    var totalDepreciation = api.column(7, { page: 'current' }).data().reduce(function (a, b) {
                        return parseFloat(a) + parseFloat($(b).text().replace(/[^\d.-]/g, ''));
                    }, 0);

                    var netBookValue = api.column(8, { page: 'current' }).data().reduce(function (a, b) {
                        return parseFloat(a) + parseFloat($(b).text().replace(/[^\d.-]/g, ''));
                    }, 0);
                }
            });
        }
    }

    initializeKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case 'g':
                        e.preventDefault();
                        this.generateReport();
                        break;
                    case 'e':
                        e.preventDefault();
                        this.showExportMenu();
                        break;
                    case 'p':
                        e.preventDefault();
                        this.printReport();
                        break;
                    case 'a':
                        e.preventDefault();
                        this.showAnalysis();
                        break;
                }
            }
        });
    }

    initializeCharts() {
        if (typeof Chart !== 'undefined') {
            this.createAssetsByCategoryChart();
            this.createDepreciationAnalysisChart();
            this.createAssetAgeAnalysisChart();
        }
    }

    generateReport() {
        const form = document.getElementById('assets-report-form');
        const formData = new FormData(form);

        this.showLoadingState(true);

        fetch('{{ url_link('accounts/fixed_assets_report', 'generate') }}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            this.showLoadingState(false);
            if (data.success) {
                this.showAlert('{{ text_report_generated }}', 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                this.showAlert(data.error || '{{ error_generate_report }}', 'danger');
            }
        })
        .catch(error => {
            this.showLoadingState(false);
            this.showAlert('{{ error_generate_report }}: ' + error.message, 'danger');
        });
    }

    exportReport(format) {
        const params = new URLSearchParams({
            format: format,
            date_start: document.getElementById('date_start').value,
            date_end: document.getElementById('date_end').value,
            category_id: document.getElementById('category_id').value,
            status: document.getElementById('status').value
        });

        this.showAlert('{{ text_exporting }}...', 'info');
        window.open('{{ export_url }}&' + params.toString(), '_blank');
    }

    printReport() {
        window.print();
    }

    showAnalysis() {
        window.open('{{ analysis_url }}', '_blank');
    }

    createAssetsByCategoryChart() {
        const ctx = document.getElementById('assetsByCategoryChart');
        if (!ctx) return;

        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: {{ category_names|json_encode|raw }},
                datasets: [{
                    data: {{ category_values|json_encode|raw }},
                    backgroundColor: ['#16a085', '#3498db', '#e74c3c', '#f39c12', '#9b59b6'],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: '{{ text_assets_by_category_chart }}'
                    },
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    createDepreciationAnalysisChart() {
        const ctx = document.getElementById('depreciationAnalysisChart');
        if (!ctx) return;

        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: {{ asset_names|json_encode|raw }},
                datasets: [{
                    label: '{{ text_original_cost }}',
                    data: {{ original_costs|json_encode|raw }},
                    backgroundColor: '#17a2b8',
                    borderColor: '#138496',
                    borderWidth: 1
                }, {
                    label: '{{ text_accumulated_depreciation }}',
                    data: {{ accumulated_depreciation|json_encode|raw }},
                    backgroundColor: '#f39c12',
                    borderColor: '#e67e22',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: '{{ text_depreciation_analysis_chart }}'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    createAssetAgeAnalysisChart() {
        const ctx = document.getElementById('assetAgeAnalysisChart');
        if (!ctx) return;

        new Chart(ctx, {
            type: 'line',
            data: {
                labels: {{ age_ranges|json_encode|raw }},
                datasets: [{
                    label: '{{ text_number_of_assets }}',
                    data: {{ age_counts|json_encode|raw }},
                    borderColor: '#16a085',
                    backgroundColor: 'rgba(22, 160, 133, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: '{{ text_asset_age_analysis_chart }}'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    showExportMenu() {
        const exportButton = document.querySelector('.dropdown-toggle');
        if (exportButton) {
            exportButton.click();
        }
    }

    showLoadingState(show) {
        const generateBtn = document.querySelector('button[onclick="generateReport()"]');
        if (generateBtn) {
            if (show) {
                generateBtn.disabled = true;
                generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> {{ text_loading }}';
            } else {
                generateBtn.disabled = false;
                generateBtn.innerHTML = '<i class="fas fa-chart-line me-2"></i> {{ button_generate }}';
            }
        }
    }

    showAlert(message, type) {
        const alertContainer = document.createElement('div');
        alertContainer.className = `alert alert-${type} alert-dismissible fade show`;
        alertContainer.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'}"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        const container = document.querySelector('#content .container-fluid');
        container.insertBefore(alertContainer, container.firstChild);

        setTimeout(() => {
            alertContainer.remove();
        }, 5000);
    }
}

// Global functions for backward compatibility
function generateReport() {
    fixedAssetsReportManager.generateReport();
}

function exportReport(format) {
    fixedAssetsReportManager.exportReport(format);
}

function printReport() {
    fixedAssetsReportManager.printReport();
}

function showAnalysis() {
    fixedAssetsReportManager.showAnalysis();
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.fixedAssetsReportManager = new FixedAssetsReportManager();
});
</script>

{{ footer }}
