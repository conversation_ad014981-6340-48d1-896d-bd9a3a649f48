{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="submit" form="form-store" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fa fa-save"></i></button>
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a></div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    {% if success %}
    <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pencil"></i> {{ text_form }}</h3>
      </div>
      <div class="panel-body">
        <form action="{{ action }}" method="post" enctype="multipart/form-data" id="form-store" class="form-horizontal">
          <ul class="nav nav-tabs">
            <li class="active"><a href="#tab-general" data-toggle="tab">{{ tab_general }}</a></li>
            <li><a href="#tab-store" data-toggle="tab">{{ tab_store }}</a></li>
            <li><a href="#tab-local" data-toggle="tab">{{ tab_local }}</a></li>
            <li><a href="#tab-option" data-toggle="tab">{{ tab_option }}</a></li>
            <li><a href="#tab-image" data-toggle="tab">{{ tab_image }}</a></li>
            <li><a href="#tab-server" data-toggle="tab">{{ tab_server }}</a></li>
          </ul>
          <div class="tab-content">
            <div class="tab-pane active" id="tab-general">
              <div class="form-group required">
                <label class="col-sm-2 control-label" for="input-url"><span data-toggle="tooltip" data-html="true" title="{{ help_url|escape('html') }}">{{ entry_url }}</span></label>
                <div class="col-sm-10">
                  <input type="text" name="config_url" value="{{ config_url }}" placeholder="{{ entry_url }}" id="input-url" class="form-control" />
                  {% if error_url %}
                  <div class="text-danger">{{ error_url }}</div>
                  {% endif %}
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-ssl"><span data-toggle="tooltip" data-html="true" title="{{ help_ssl }}">{{ entry_ssl }}</span></label>
                <div class="col-sm-10">
                  <input type="text" name="config_ssl" value="{{ config_ssl }}" placeholder="{{ entry_ssl }}" id="input-ssl" class="form-control" />
                </div>
              </div>
              <div class="form-group required">
                <label class="col-sm-2 control-label" for="input-meta-title">{{ entry_meta_title }}</label>
                <div class="col-sm-10">
                  <input type="text" name="config_meta_title" value="{{ config_meta_title }}" placeholder="{{ entry_meta_title }}" id="input-meta-title" class="form-control" />
                  {% if error_meta_title %}
                  <div class="text-danger">{{ error_meta_title }}</div>
                  {% endif %}
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-meta-description">{{ entry_meta_description }}</label>
                <div class="col-sm-10">
                  <textarea name="config_meta_description" rows="5" placeholder="{{ entry_meta_description }}" id="input-meta-description" class="form-control">{{ config_meta_description }}</textarea>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-meta-keyword">{{ entry_meta_keyword }}</label>
                <div class="col-sm-10">
                  <textarea name="config_meta_keyword" rows="5" placeholder="{{ entry_meta_keyword }}" id="input-meta-keyword" class="form-control">{{ config_meta_keyword }}</textarea>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-theme">{{ entry_theme }}</label>
                <div class="col-sm-10">
                  <select name="config_theme" id="input-theme" class="form-control">
                    {% for theme in themes %}
                    {% if theme.value == config_theme %}
                    <option value="{{ theme.value }}" selected="selected">{{ theme.text }}</option>
                    {% else %}
                    <option value="{{ theme.value }}">{{ theme.text }}</option>
                    {% endif %}
                    {% endfor %}
                  </select>
                  <br/>
                  <img src="" alt="" id="theme" class="img-thumbnail" /></div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-layout">{{ entry_layout }}</label>
                <div class="col-sm-10">
                  <select name="config_layout_id" id="input-layout" class="form-control">
                    {% for layout in layouts %}
                    {% if layout.layout_id == config_layout_id %}
                    <option value="{{ layout.layout_id }}" selected="selected">{{ layout.name }}</option>
                    {% else %}
                    <option value="{{ layout.layout_id }}">{{ layout.name }}</option>
                    {% endif %}
                    {% endfor %}
                  </select>
                </div>
              </div>
            </div>
            <div class="tab-pane" id="tab-store">
              <div class="form-group required">
                <label class="col-sm-2 control-label" for="input-name">{{ entry_name }}</label>
                <div class="col-sm-10">
                  <input type="text" name="config_name" value="{{ config_name }}" placeholder="{{ entry_name }}" id="input-name" class="form-control" />
                  {% if error_name %}
                  <div class="text-danger">{{ error_name }}</div>
                  {% endif %}
                </div>
              </div>
              <div class="form-group required">
                <label class="col-sm-2 control-label" for="input-owner">{{ entry_owner }}</label>
                <div class="col-sm-10">
                  <input type="text" name="config_owner" value="{{ config_owner }}" placeholder="{{ entry_owner }}" id="input-owner" class="form-control" />
                  {% if error_owner %}
                  <div class="text-danger">{{ error_owner }}</div>
                  {% endif %}
                </div>
              </div>
              <div class="form-group required">
                <label class="col-sm-2 control-label" for="input-address">{{ entry_address }}</label>
                <div class="col-sm-10">
                  <textarea name="config_address" rows="5" placeholder="{{ entry_address }}" id="input-address" class="form-control">{{ config_address }}</textarea>
                  {% if error_address %}
                  <div class="text-danger">{{ error_address }}</div>
                  {% endif %}
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-geocode"><span data-toggle="tooltip" data-container="#tab-general" title="{{ help_geocode }}">{{ entry_geocode }}</span></label>
                <div class="col-sm-10">
                  <input type="text" name="config_geocode" value="{{ config_geocode }}" placeholder="{{ entry_geocode }}" id="input-geocode" class="form-control" />
                </div>
              </div>
              <div class="form-group required">
                <label class="col-sm-2 control-label" for="input-email">{{ entry_email }}</label>
                <div class="col-sm-10">
                  <input type="text" name="config_email" value="{{ config_email }}" placeholder="{{ entry_email }}" id="input-email" class="form-control" />
                  {% if error_email %}
                  <div class="text-danger">{{ error_email }}</div>
                  {% endif %}
                </div>
              </div>
              <div class="form-group required">
                <label class="col-sm-2 control-label" for="input-telephone">{{ entry_telephone }}</label>
                <div class="col-sm-10">
                  <input type="text" name="config_telephone" value="{{ config_telephone }}" placeholder="{{ entry_telephone }}" id="input-telephone" class="form-control" />
                  {% if error_telephone %}
                  <div class="text-danger">{{ error_telephone }}</div>
                  {% endif %}
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-fax">{{ entry_fax }}</label>
                <div class="col-sm-10">
                  <input type="text" name="config_fax" value="{{ config_fax }}" placeholder="{{ entry_fax }}" id="input-fax" class="form-control" />
                </div>
              </div>              
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-image">{{ entry_image }}</label>
                <div class="col-sm-10"><a href="" id="thumb-image" data-toggle="image" class="img-thumbnail"><img src="{{ thumb }}" alt="" title="" data-placeholder="{{ placeholder }}" /></a>
                  <input type="hidden" name="config_image" value="{{ config_image }}" id="input-image" />
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-open"><span data-toggle="tooltip" title="{{ help_open }}">{{ entry_open }}</span></label>
                <div class="col-sm-10">
                  <textarea name="config_open" rows="5" placeholder="{{ entry_open }}" id="input-open" class="form-control">{{ config_open }}</textarea>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-comment"><span data-toggle="tooltip" title="{{ help_comment }}">{{ entry_comment }}</span></label>
                <div class="col-sm-10">
                  <textarea name="config_comment" rows="5" placeholder="{{ entry_comment }}" id="input-comment" class="form-control">{{ config_comment }}</textarea>
                </div>
              </div>
              {% if locations %}
              <div class="form-group">
                <label class="col-sm-2 control-label"><span data-toggle="tooltip" title="{{ help_location }}">{{ entry_location }}</span></label>
                <div class="col-sm-10">
                  {% for location in locations %}
                  <div class="checkbox">
                    <label>
                      {% if location.location_id in config_location %}
                      <input type="checkbox" name="config_location[]" value="{{ location.location_id }}" checked="checked" />
                      {{ location.name }}
                      {% else %}
                      <input type="checkbox" name="config_location[]" value="{{ location.location_id }}" />
                      {{ location.name }}
                      {% endif %}
                    </label>
                  </div>
                  {% endfor %}
                </div>
              </div>
              {% endif %}
            </div>
            <div class="tab-pane" id="tab-local">
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-country">{{ entry_country }}</label>
                <div class="col-sm-10">
                  <select name="config_country_id" id="input-country" class="form-control">
                    {% for country in countries %}
                    {% if country.country_id == config_country_id %}
                    <option value="{{ country.country_id }}" selected="selected">{{ country.name }}</option>
                    {% else %}
                    <option value="{{ country.country_id }}">{{ country.name }}</option>
                    {% endif %}
                    {% endfor %}
                  </select>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-zone">{{ entry_zone }}</label>
                <div class="col-sm-10">
                  <select name="config_zone_id" id="input-zone" class="form-control">
                  </select>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-language">{{ entry_language }}</label>
                <div class="col-sm-10">
                  <select name="config_language" id="input-language" class="form-control">
                    {% for language in languages %}
                    {% if language.code == config_language %}
                    <option value="{{ language.code }}" selected="selected">{{ language.name }}</option>
                    {% else %}
                    <option value="{{ language.code }}">{{ language.name }}</option>
                    {% endif %}
                    {% endfor %}
                  </select>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-currency"><span data-toggle="tooltip" title="{{ help_currency }}">{{ entry_currency }}</span></label>
                <div class="col-sm-10">
                  <select name="config_currency" id="input-currency" class="form-control">
                    {% for currency in currencies %}
                    {% if currency.code == config_currency %}
                    <option value="{{ currency.code }}" selected="selected">{{ currency.title }}</option>
                    {% else %}
                    <option value="{{ currency.code }}">{{ currency.title }}</option>
                    {% endif %}
                    {% endfor %}
                  </select>
                </div>
              </div>
            </div>
            <div class="tab-pane" id="tab-option">
              <fieldset>
                <legend>{{ text_tax }}</legend>
                <div class="form-group">
                  <label class="col-sm-2 control-label">{{ entry_tax }}</label>
                  <div class="col-sm-10">
                    <label class="radio-inline">
                      {% if config_tax %}
                      <input type="radio" name="config_tax" value="1" checked="checked" />
                      {{ text_yes }}
                      {% else %}
                      <input type="radio" name="config_tax" value="1" />
                      {{ text_yes }}
                      {% endif %}
                    </label>
                    <label class="radio-inline">
                      {% if not config_tax %}
                      <input type="radio" name="config_tax" value="0" checked="checked" />
                      {{ text_no }}
                      {% else %}
                      <input type="radio" name="config_tax" value="0" />
                      {{ text_no }}
                      {% endif %}
                    </label>
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-tax-default"><span data-toggle="tooltip" title="{{ help_tax_default }}">{{ entry_tax_default }}</span></label>
                  <div class="col-sm-10">
                    <select name="config_tax_default" id="input-tax-default" class="form-control">
                      <option value="">{{ text_none }}</option>
                      {% if config_tax_default == 'shipping' %}
                      <option value="shipping" selected="selected">{{ text_shipping }}</option>
                      {% else %}
                      <option value="shipping">{{ text_shipping }}</option>
                      {% endif %}
                      {% if config_tax_default == 'payment' %}
                      <option value="payment" selected="selected">{{ text_payment }}</option>
                      {% else %}
                      <option value="payment">{{ text_payment }}</option>
                      {% endif %}
                    </select>
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-tax-customer"><span data-toggle="tooltip" title="{{ help_tax_customer }}">{{ entry_tax_customer }}</span></label>
                  <div class="col-sm-10">
                    <select name="config_tax_customer" id="input-tax-customer" class="form-control">
                      <option value="">{{ text_none }}</option>
                      {% if config_tax_customer == 'shipping' %}
                      <option value="shipping" selected="selected">{{ text_shipping }}</option>
                      {% else %}
                      <option value="shipping">{{ text_shipping }}</option>
                      {% endif %}
                      {% if config_tax_customer == 'payment' %}
                      <option value="payment" selected="selected">{{ text_payment }}</option>
                      {% else %}
                      <option value="payment">{{ text_payment }}</option>
                      {% endif %}
                    </select>
                  </div>
                </div>
              </fieldset>
              <fieldset>
                <legend>{{ text_account }}</legend>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-customer-group"><span data-toggle="tooltip" title="{{ help_customer_group }}">{{ entry_customer_group }}</span></label>
                  <div class="col-sm-10">
                    <select name="config_customer_group_id" id="input-customer-group" class="form-control">
                      {% for customer_group in customer_groups %}
                      {% if customer_group.customer_group_id == config_customer_group_id %}
                      <option value="{{ customer_group.customer_group_id }}" selected="selected">{{ customer_group.name }}</option>
                      {% else %}
                      <option value="{{ customer_group.customer_group_id }}">{{ customer_group.name }}</option>
                      {% endif %}
                      {% endfor %}
                    </select>
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label"><span data-toggle="tooltip" title="{{ help_customer_group_display }}">{{ entry_customer_group_display }}</span></label>
                  <div class="col-sm-10">
                    {% for customer_group in customer_groups %}
                    <div class="checkbox">
                      <label>
                        {% if customer_group.customer_group_id in config_customer_group_display %}
                        <input type="checkbox" name="config_customer_group_display[]" value="{{ customer_group.customer_group_id }}" checked="checked" />
                        {{ customer_group.name }}
                        {% else %}
                        <input type="checkbox" name="config_customer_group_display[]" value="{{ customer_group.customer_group_id }}" />
                        {{ customer_group.name }}
                        {% endif %}
                      </label>
                    </div>
                    {% endfor %}
                    {% if error_customer_group_display %}
                    <div class="text-danger">{{ error_customer_group_display }}</div>
                    {% endif %}
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label"><span data-toggle="tooltip" title="{{ help_customer_price }}">{{ entry_customer_price }}</span></label>
                  <div class="col-sm-10">
                    <label class="radio-inline">
                      {% if config_customer_price %}
                      <input type="radio" name="config_customer_price" value="1" checked="checked" />
                      {{ text_yes }}
                      {% else %}
                      <input type="radio" name="config_customer_price" value="1" />
                      {{ text_yes }}
                      {% endif %}
                    </label>
                    <label class="radio-inline">
                      {% if not config_customer_price %}
                      <input type="radio" name="config_customer_price" value="0" checked="checked" />
                      {{ text_no }}
                      {% else %}
                      <input type="radio" name="config_customer_price" value="0" />
                      {{ text_no }}
                      {% endif %}
                    </label>
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-account"><span data-toggle="tooltip" title="{{ help_account }}">{{ entry_account }}</span></label>
                  <div class="col-sm-10">
                    <select name="config_account_id" id="input-account" class="form-control">
                      <option value="0">{{ text_none }}</option>
                      {% for information in informations %}
                      {% if information.information_id == config_account_id %}
                      <option value="{{ information.information_id }}" selected="selected">{{ information.title }}</option>
                      {% else %}
                      <option value="{{ information.information_id }}">{{ information.title }}</option>
                      {% endif %}
                      {% endfor %}
                    </select>
                  </div>
                </div>
              </fieldset>
              <fieldset>
                <legend>{{ text_checkout }}</legend>
                <div class="form-group">
                  <label class="col-sm-2 control-label">{{ entry_cart_weight }}</label>
                  <div class="col-sm-10">
                    <label class="radio-inline">
                      {% if config_cart_weight %}
                      <input type="radio" name="config_cart_weight" value="1" checked="checked" />
                      {{ text_yes }}
                      {% else %}
                      <input type="radio" name="config_cart_weight" value="1" />
                      {{ text_yes }}
                      {% endif %}
                    </label>
                    <label class="radio-inline">
                      {% if not config_cart_weight %}
                      <input type="radio" name="config_cart_weight" value="0" checked="checked" />
                      {{ text_no }}
                      {% else %}
                      <input type="radio" name="config_cart_weight" value="0" />
                      {{ text_no }}
                      {% endif %}
                    </label>
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label"><span data-toggle="tooltip" title="{{ help_checkout_guest }}">{{ entry_checkout_guest }}</span></label>
                  <div class="col-sm-10">
                    <label class="radio-inline">
                      {% if config_checkout_guest %}
                      <input type="radio" name="config_checkout_guest" value="1" checked="checked" />
                      {{ text_yes }}
                      {% else %}
                      <input type="radio" name="config_checkout_guest" value="1" />
                      {{ text_yes }}
                      {% endif %}
                    </label>
                    <label class="radio-inline">
                      {% if not config_checkout_guest %}
                      <input type="radio" name="config_checkout_guest" value="0" checked="checked" />
                      {{ text_no }}
                      {% else %}
                      <input type="radio" name="config_checkout_guest" value="0" />
                      {{ text_no }}
                      {% endif %}
                    </label>
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-checkout"><span data-toggle="tooltip" title="{{ help_checkout }}">{{ entry_checkout }}</span></label>
                  <div class="col-sm-10">
                    <select name="config_checkout_id" id="input-checkout" class="form-control">
                      <option value="0">{{ text_none }}</option>
                      {% for information in informations %}
                      {% if information.information_id == config_checkout_id %}
                      <option value="{{ information.information_id }}" selected="selected">{{ information.title }}</option>
                      {% else %}
                      <option value="{{ information.information_id }}">{{ information.title }}</option>
                      {% endif %}
                      {% endfor %}
                    </select>
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-order-status"><span data-toggle="tooltip" title="{{ help_order_status }}">{{ entry_order_status }}</span></label>
                  <div class="col-sm-10">
                    <select name="config_order_status_id" id="input-order-status" class="form-control">
                      {% for order_status in order_statuses %}
                      {% if order_status.order_status_id == config_order_status_id %}
                      <option value="{{ order_status.order_status_id }}" selected="selected">{{ order_status.name }}</option>
                      {% else %}
                      <option value="{{ order_status.order_status_id }}">{{ order_status.name }}</option>
                      {% endif %}
                      {% endfor %}
                    </select>
                  </div>
                </div>
              </fieldset>
              <fieldset>
                <legend>{{ text_stock }}</legend>
                <div class="form-group">
                  <label class="col-sm-2 control-label"><span data-toggle="tooltip" title="{{ help_stock_display }}">{{ entry_stock_display }}</span></label>
                  <div class="col-sm-10">
                    <label class="radio-inline">
                      {% if config_stock_display %}
                      <input type="radio" name="config_stock_display" value="1" checked="checked" />
                      {{ text_yes }}
                      {% else %}
                      <input type="radio" name="config_stock_display" value="1" />
                      {{ text_yes }}
                      {% endif %}
                    </label>
                    <label class="radio-inline">
                      {% if not config_stock_display %}
                      <input type="radio" name="config_stock_display" value="0" checked="checked" />
                      {{ text_no }}
                      {% else %}
                      <input type="radio" name="config_stock_display" value="0" />
                      {{ text_no }}
                      {% endif %}
                    </label>
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label"><span data-toggle="tooltip" title="{{ help_stock_checkout }}">{{ entry_stock_checkout }}</span></label>
                  <div class="col-sm-10">
                    <label class="radio-inline">
                      {% if config_stock_checkout %}
                      <input type="radio" name="config_stock_checkout" value="1" checked="checked" />
                      {{ text_yes }}
                      {% else %}
                      <input type="radio" name="config_stock_checkout" value="1" />
                      {{ text_yes }}
                      {% endif %}
                    </label>
                    <label class="radio-inline">
                      {% if not config_stock_checkout %}
                      <input type="radio" name="config_stock_checkout" value="0" checked="checked" />
                      {{ text_no }}
                      {% else %}
                      <input type="radio" name="config_stock_checkout" value="0" />
                      {{ text_no }}
                      {% endif %}
                    </label>
                  </div>
                </div>
              </fieldset>
            </div>
            <div class="tab-pane" id="tab-image">
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-logo">{{ entry_logo }}</label>
                <div class="col-sm-10"><a href="" id="thumb-logo" data-toggle="image" class="img-thumbnail"><img src="{{ logo }}" alt="" title="" data-placeholder="{{ placeholder }}" /></a>
                  <input type="hidden" name="config_logo" value="{{ config_logo }}" id="input-logo" />
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-icon"><span data-toggle="tooltip" title="{{ help_icon }}">{{ entry_icon }}</span></label>
                <div class="col-sm-10"><a href="" id="thumb-icon" data-toggle="image" class="img-thumbnail"><img src="{{ icon }}" alt="" title="" data-placeholder="{{ placeholder }}" /></a>
                  <input type="hidden" name="config_icon" value="{{ config_icon }}" id="input-icon" />
                </div>
              </div>
            </div>
            <div class="tab-pane" id="tab-server">
              <div class="form-group">
                <label class="col-sm-2 control-label"><span data-toggle="tooltip" title="{{ help_secure }}">{{ entry_secure }}</span></label>
                <div class="col-sm-10">
                  <label class="radio-inline">
                    {% if config_secure %}
                    <input type="radio" name="config_secure" value="1" checked="checked" />
                    {{ text_yes }}
                    {% else %}
                    <input type="radio" name="config_secure" value="1" />
                    {{ text_yes }}
                    {% endif %}
                  </label>
                  <label class="radio-inline">
                    {% if not config_secure %}
                    <input type="radio" name="config_secure" value="0" checked="checked" />
                    {{ text_no }}
                    {% else %}
                    <input type="radio" name="config_secure" value="0" />
                    {{ text_no }}
                    {% endif %}
                  </label>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
  <script type="text/javascript"><!--
$('select[name=\'config_theme\']').on('change', function() {
	$.ajax({
		url: 'index.php?route=setting/setting/theme&user_token={{ user_token }}&theme=' + this.value,
		dataType: 'html',
		beforeSend: function() {
			$('select[name=\'config_theme\']').prop('disabled', true);
		},
		complete: function() {
			$('select[name=\'config_theme\']').prop('disabled', false);
		},
		success: function(html) {
			$('#theme').attr('src', html);
		},
		error: function(xhr, ajaxOptions, thrownError) {
			alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
		}
	});
});

$('select[name=\'config_theme\']').trigger('change');
//--></script>   <script type="text/javascript"><!--
$('select[name=\'config_country_id\']').on('change', function() {
	$.ajax({
		url: 'index.php?route=localisation/country/country&user_token={{ user_token }}&country_id=' + this.value,
		dataType: 'json',
		beforeSend: function() {
			$('select[name=\'config_country_id\']').prop('disabled', true);
		},
		complete: function() {
			$('select[name=\'config_country_id\']').prop('disabled', false);
		},
		success: function(json) {
			html = '<option value="">{{ text_select }}</option>';

			if (json['zone'] && json['zone'] != '') {
				for (i = 0; i < json['zone'].length; i++) {
					html += '<option value="' + json['zone'][i]['zone_id'] + '"';

					if (json['zone'][i]['zone_id'] == '{{ config_zone_id }}') {
						html += ' selected="selected"';
					}

					html += '>' + json['zone'][i]['name'] + '</option>';
				}
			} else {
				html += '<option value="0" selected="selected">{{ text_none }}</option>';
			}

			$('select[name=\'config_zone_id\']').html(html);
		},
		error: function(xhr, ajaxOptions, thrownError) {
			alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
		}
	});
});

$('select[name=\'config_country_id\']').trigger('change');
//--></script></div>
{{ footer }} 
