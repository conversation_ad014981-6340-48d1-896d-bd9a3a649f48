{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="submit" form="purchase\smart_approval_system-form" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fas fa-save"></i></button>
        <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-light"><i class="fas fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    
    <div class="card">
      <div class="card-header"><i class="fas fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}chart-bar{% elif screen_type == 'settings' %}cog{% else %}pencil-alt{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="card-body">
        <form id="purchase\smart_approval_system-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-analytics_url">{{ text_analytics_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="analytics_url" value="{{ analytics_url }}" placeholder="{{ text_analytics_url }}" id="input-analytics_url" class="form-control" />
              {% if error_analytics_url %}
                <div class="invalid-feedback">{{ error_analytics_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-bulk_approval_url">{{ text_bulk_approval_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="bulk_approval_url" value="{{ bulk_approval_url }}" placeholder="{{ text_bulk_approval_url }}" id="input-bulk_approval_url" class="form-control" />
              {% if error_bulk_approval_url %}
                <div class="invalid-feedback">{{ error_bulk_approval_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-can_bulk_approval">{{ text_can_bulk_approval }}</label>
            <div class="col-sm-10">
              <input type="text" name="can_bulk_approval" value="{{ can_bulk_approval }}" placeholder="{{ text_can_bulk_approval }}" id="input-can_bulk_approval" class="form-control" />
              {% if error_can_bulk_approval %}
                <div class="invalid-feedback">{{ error_can_bulk_approval }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-can_process_approval">{{ text_can_process_approval }}</label>
            <div class="col-sm-10">
              <input type="text" name="can_process_approval" value="{{ can_process_approval }}" placeholder="{{ text_can_process_approval }}" id="input-can_process_approval" class="form-control" />
              {% if error_can_process_approval %}
                <div class="invalid-feedback">{{ error_can_process_approval }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-can_update_rules">{{ text_can_update_rules }}</label>
            <div class="col-sm-10">
              <input type="text" name="can_update_rules" value="{{ can_update_rules }}" placeholder="{{ text_can_update_rules }}" id="input-can_update_rules" class="form-control" />
              {% if error_can_update_rules %}
                <div class="invalid-feedback">{{ error_can_update_rules }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-departments">{{ text_departments }}</label>
            <div class="col-sm-10">
              <input type="text" name="departments" value="{{ departments }}" placeholder="{{ text_departments }}" id="input-departments" class="form-control" />
              {% if error_departments %}
                <div class="invalid-feedback">{{ error_departments }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-escalate_url">{{ text_escalate_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="escalate_url" value="{{ escalate_url }}" placeholder="{{ text_escalate_url }}" id="input-escalate_url" class="form-control" />
              {% if error_escalate_url %}
                <div class="invalid-feedback">{{ error_escalate_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="invalid-feedback">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-pending_approvals_url">{{ text_pending_approvals_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="pending_approvals_url" value="{{ pending_approvals_url }}" placeholder="{{ text_pending_approvals_url }}" id="input-pending_approvals_url" class="form-control" />
              {% if error_pending_approvals_url %}
                <div class="invalid-feedback">{{ error_pending_approvals_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-process_approval_url">{{ text_process_approval_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="process_approval_url" value="{{ process_approval_url }}" placeholder="{{ text_process_approval_url }}" id="input-process_approval_url" class="form-control" />
              {% if error_process_approval_url %}
                <div class="invalid-feedback">{{ error_process_approval_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-rules_url">{{ text_rules_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="rules_url" value="{{ rules_url }}" placeholder="{{ text_rules_url }}" id="input-rules_url" class="form-control" />
              {% if error_rules_url %}
                <div class="invalid-feedback">{{ error_rules_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-update_rules_url">{{ text_update_rules_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="update_rules_url" value="{{ update_rules_url }}" placeholder="{{ text_update_rules_url }}" id="input-update_rules_url" class="form-control" />
              {% if error_update_rules_url %}
                <div class="invalid-feedback">{{ error_update_rules_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="invalid-feedback">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-users">{{ text_users }}</label>
            <div class="col-sm-10">
              <input type="text" name="users" value="{{ users }}" placeholder="{{ text_users }}" id="input-users" class="form-control" />
              {% if error_users %}
                <div class="invalid-feedback">{{ error_users }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-workflow_url">{{ text_workflow_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="workflow_url" value="{{ workflow_url }}" placeholder="{{ text_workflow_url }}" id="input-workflow_url" class="form-control" />
              {% if error_workflow_url %}
                <div class="invalid-feedback">{{ error_workflow_url }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}