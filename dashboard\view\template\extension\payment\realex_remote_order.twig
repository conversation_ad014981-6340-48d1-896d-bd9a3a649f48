<h2>{{ text_payment_info }}</h2>
<div class="alert alert-success" id="realex_transaction_msg" style="display:none;"></div>
<table class="table table-striped table-bordered">
  <tr>
    <td>{{ text_order_ref }}</td>
    <td>{{ realex_order.order_ref }}</td>
  </tr>
  <tr>
    <td>{{ text_order_total }}</td>
    <td>{{ realex_order.total_formatted }}</td>
  </tr>
  <tr>
    <td>{{ text_total_captured }}</td>
    <td id="payment_realex_total_captured">{{ realex_order.total_captured_formatted }}</td>
  </tr>
  <tr>
    <td>{{ text_capture_status }}</td>
    <td id="capture_status">{% if realex_order.capture_status == 1 %}
      <span class="capture_text">{{ text_yes }}</span>
      {% else %}
      <span class="capture_text">{{ text_no }}</span>&nbsp;&nbsp;
      {% if realex_order.void_status == 0 %}
      <input type="text" width="10" id="capture_amount" value="{{ realex_order.total }}"/>
      <a class="button btn btn-primary" id="button_capture">{{ button_capture }}</a> <span class="btn btn-primary" id="img_loading_capture" style="display:none;"><i class="fa fa-circle-o-notch fa-spin fa-lg"></i></span>
      {% endif %}
      {% endif %}</td>
  </tr>
  <tr>
    <td>{{ text_void_status }}</td>
    <td id="void_status">{% if realex_order.void_status == 1 %}
      <span class="void_text">{{ text_yes }}</span>
      {% else %}
      <span class="void_text">{{ text_no }}</span>&nbsp;&nbsp; <a class="button btn btn-primary" id="button-void">{{ button_void }}</a> <span class="btn btn-primary" id="img_loading_void" style="display:none;"><i class="fa fa-circle-o-notch fa-spin fa-lg"></i></span>
      {% endif %}</td>
  </tr>
  <tr>
    <td>{{ text_rebate_status }}</td>
    <td id="rebate_status">{% if realex_order.rebate_status == 1 %}
      <span class="rebate_text">{{ text_yes }}</span>
      {% else %}
      <span class="rebate_text">{{ text_no }}</span>&nbsp;&nbsp;
      {%  if realex_order.total_captured > 0 and realex_order.void_status == 0 %}
      <input type="text" width="10" id="rebate_amount" />
      <a class="button btn btn-primary" id="button-rebate">{{ button_rebate }}</a> <span class="btn btn-primary" id="img_loading_rebate" style="display:none;"><i class="fa fa-circle-o-notch fa-spin fa-lg"></i></span>
      {% endif %}
      {% endif %}</td>
  </tr>
  <tr>
    <td>{{ text_transactions }}:</td>
    <td><table class="table table-striped table-bordered" id="realex_transactions">
        <thead>
          <tr>
            <td class="text-left"><strong>{{ text_column_date_added }}</strong></td>
            <td class="text-left"><strong>{{ text_column_type }}</strong></td>
            <td class="text-left"><strong>{{ text_column_amount }}</strong></td>
          </tr>
        </thead>
        <tbody>
          {% for transaction in realex_order.transactions %}
          <tr>
            <td class="text-left">{{ transaction.date_added }}</td>
            <td class="text-left">{{ transaction.type }}</td>
            <td class="text-left">{{ transaction.amount }}</td>
          </tr>
          {% endfor %}
        </tbody>
      </table></td>
  </tr>
</table>
<script type="text/javascript"><!--
  $("#button-void").click(function () {
    if (confirm('{{ text_confirm_void }}')) {
      $.ajax({
        type:'POST',
        dataType: 'json',
        data: {'order_id': {{ order_id }} },
        url: 'index.php?route=extension/payment/realex_remote/void&user_token={{ user_token }}',
        beforeSend: function() {
          $('#button-void').hide();
          $('#img_loading_void').show();
          $('#realex_transaction_msg').hide();
        },
        success: function(data) {
          if (data.error == false) {
            html = '';
            html += '<tr>';
            html += '<td class="text-left">' + data.data.date_added + '</td>';
            html += '<td class="text-left">{{ text_void }}</td>';
            html += '<td class="text-left">0.00</td>';
            html += '</tr>';

            $('.void_text').text('{{ text_yes }}');
            $('#realex_transactions').append(html);
            $('#button_capture').hide();
            $('#capture_amount').hide();

            if (data.msg != '') {
              $('#realex_transaction_msg').empty().html('<i class="fa fa-check-circle"></i> ' + data.msg).fadeIn();
            }
          }
          if (data.error == true) {
            alert(data.msg);
            $('#button-void').show();
          }

          $('#img_loading_void').hide();
        }
      });
    }
  });
  $("#button_capture").click(function () {
    if (confirm('{{ text_confirm_capture }}')) {
      $.ajax({
        type:'POST',
        dataType: 'json',
        data: {'order_id': {{ order_id }}, 'amount' : $('#capture_amount').val() },
        url: 'index.php?route=extension/payment/realex_remote/capture&user_token={{ user_token }}',
        beforeSend: function() {
          $('#button_capture').hide();
          $('#capture_amount').hide();
          $('#img_loading_capture').show();
          $('#realex_transaction_msg').hide();
        },
        success: function(data) {
          if (data.error == false) {
            html = '';
            html += '<tr>';
            html += '<td class="text-left">' + data.data.date_added + '</td>';
            html += '<td class="text-left">{{ text_payment }}</td>';
            html += '<td class="text-left">' + data.data.amount + '</td>';
            html += '</tr>';

            $('#realex_transactions').append(html);
            $('#payment_realex_total_captured').text(data.data.total_formatted);

            if (data.data.capture_status == 1) {
              $('#button-void').hide();
              $('.capture_text').text('{{ text_yes }}');
            } else {
              $('#button_capture').show();
              $('#capture_amount').val(0.00);
            }

            {% if auto_settle == 2 %}
              $('#capture_amount').show();
            {% endif %}

            if (data.msg != '') {
              $('#realex_transaction_msg').empty().html('<i class="fa fa-check-circle"></i> ' + data.msg).fadeIn();
            }

            $('#button-rebate').show();
            $('#rebate_amount').val(0.00).show();
          }
          if (data.error == true) {
            alert(data.msg);
            $('#button_capture').show();
            $('#capture_amount').show();
          }

          $('#img_loading_capture').hide();
        }
      });
    }
  });
  $("#button-rebate").click(function () {
    if (confirm('{{ text_confirm_rebate }}')) {
      $.ajax({
        type:'POST',
        dataType: 'json',
        data: {'order_id': {{ order_id }}, 'amount' : $('#rebate_amount').val() },
        url: 'index.php?route=extension/payment/realex_remote/rebate&user_token={{ user_token }}',
        beforeSend: function() {
          $('#button-rebate').hide();
          $('#rebate_amount').hide();
          $('#img_loading_rebate').show();
          $('#realex_transaction_msg').hide();
        },
        success: function(data) {
          if (data.error == false) {
            html = '';
            html += '<tr>';
            html += '<td class="text-left">' + data.data.date_added + '</td>';
            html += '<td class="text-left">{{ text_rebate }}</td>';
            html += '<td class="text-left">' + data.data.amount + '</td>';
            html += '</tr>';

            $('#realex_transactions').append(html);
            $('#payment_realex_total_captured').text(data.data.total_captured);

            if (data.data.rebate_status == 1) {
              $('.rebate_text').text('{{ text_yes }}');
            } else {
              $('#button-rebate').show();
              $('#rebate_amount').val(0.00).show();
            }

            if (data.msg != '') {
              $('#realex_transaction_msg').empty().html('<i class="fa fa-check-circle"></i> ' + data.msg).fadeIn();
            }
          }
          if (data.error == true) {
            alert(data.msg);
            $('#button-rebate').show();
          }

          $('#img_loading_rebate').hide();
        }
      });
    }
  });
//--></script>
