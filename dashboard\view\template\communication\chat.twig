{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="submit" form="communication\chat-form" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fa fa-save"></i></button>
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="close" data-dismiss="alert">&times;</button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="close" data-dismiss="alert">&times;</button>
      </div>
    {{% endif %}}

    <div class="panel panel-default">
      <div class="panel-heading"><i class="fa fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}bar-chart{% elif screen_type == 'settings' %}cog{% else %}pencil{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="panel-body">
        <form id="communication\chat-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-active_chats">{{ text_active_chats }}</label>
            <div class="col-sm-10">
              <input type="text" name="active_chats" value="{{ active_chats }}" placeholder="{{ text_active_chats }}" id="input-active_chats" class="form-control" />
              {% if error_active_chats %}
                <div class="text-danger">{{ error_active_chats }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-back_to_chat">{{ text_back_to_chat }}</label>
            <div class="col-sm-10">
              <input type="text" name="back_to_chat" value="{{ back_to_chat }}" placeholder="{{ text_back_to_chat }}" id="input-back_to_chat" class="form-control" />
              {% if error_back_to_chat %}
                <div class="text-danger">{{ error_back_to_chat }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-chat_id">{{ text_chat_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="chat_id" value="{{ chat_id }}" placeholder="{{ text_chat_id }}" id="input-chat_id" class="form-control" />
              {% if error_chat_id %}
                <div class="text-danger">{{ error_chat_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-chat_info">{{ text_chat_info }}</label>
            <div class="col-sm-10">
              <input type="text" name="chat_info" value="{{ chat_info }}" placeholder="{{ text_chat_info }}" id="input-chat_info" class="form-control" />
              {% if error_chat_info %}
                <div class="text-danger">{{ error_chat_info }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-chat_settings">{{ text_chat_settings }}</label>
            <div class="col-sm-10">
              <input type="text" name="chat_settings" value="{{ chat_settings }}" placeholder="{{ text_chat_settings }}" id="input-chat_settings" class="form-control" />
              {% if error_chat_settings %}
                <div class="text-danger">{{ error_chat_settings }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-chat_settings_url">{{ text_chat_settings_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="chat_settings_url" value="{{ chat_settings_url }}" placeholder="{{ text_chat_settings_url }}" id="input-chat_settings_url" class="form-control" />
              {% if error_chat_settings_url %}
                <div class="text-danger">{{ error_chat_settings_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-chat_stats">{{ text_chat_stats }}</label>
            <div class="col-sm-10">
              <input type="text" name="chat_stats" value="{{ chat_stats }}" placeholder="{{ text_chat_stats }}" id="input-chat_stats" class="form-control" />
              {% if error_chat_stats %}
                <div class="text-danger">{{ error_chat_stats }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-current_user_id">{{ text_current_user_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="current_user_id" value="{{ current_user_id }}" placeholder="{{ text_current_user_id }}" id="input-current_user_id" class="form-control" />
              {% if error_current_user_id %}
                <div class="text-danger">{{ error_current_user_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-current_user_name">{{ text_current_user_name }}</label>
            <div class="col-sm-10">
              <input type="text" name="current_user_name" value="{{ current_user_name }}" placeholder="{{ text_current_user_name }}" id="input-current_user_name" class="form-control" />
              {% if error_current_user_name %}
                <div class="text-danger">{{ error_current_user_name }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-group_chats">{{ text_group_chats }}</label>
            <div class="col-sm-10">
              <input type="text" name="group_chats" value="{{ group_chats }}" placeholder="{{ text_group_chats }}" id="input-group_chats" class="form-control" />
              {% if error_group_chats %}
                <div class="text-danger">{{ error_group_chats }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="text-danger">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-messages">{{ text_messages }}</label>
            <div class="col-sm-10">
              <input type="text" name="messages" value="{{ messages }}" placeholder="{{ text_messages }}" id="input-messages" class="form-control" />
              {% if error_messages %}
                <div class="text-danger">{{ error_messages }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-new_chat">{{ text_new_chat }}</label>
            <div class="col-sm-10">
              <input type="text" name="new_chat" value="{{ new_chat }}" placeholder="{{ text_new_chat }}" id="input-new_chat" class="form-control" />
              {% if error_new_chat %}
                <div class="text-danger">{{ error_new_chat }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-online_users">{{ text_online_users }}</label>
            <div class="col-sm-10">
              <input type="text" name="online_users" value="{{ online_users }}" placeholder="{{ text_online_users }}" id="input-online_users" class="form-control" />
              {% if error_online_users %}
                <div class="text-danger">{{ error_online_users }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-participants">{{ text_participants }}</label>
            <div class="col-sm-10">
              <input type="text" name="participants" value="{{ participants }}" placeholder="{{ text_participants }}" id="input-participants" class="form-control" />
              {% if error_participants %}
                <div class="text-danger">{{ error_participants }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-quick_actions">{{ text_quick_actions }}</label>
            <div class="col-sm-10">
              <input type="text" name="quick_actions" value="{{ quick_actions }}" placeholder="{{ text_quick_actions }}" id="input-quick_actions" class="form-control" />
              {% if error_quick_actions %}
                <div class="text-danger">{{ error_quick_actions }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-recent_chats">{{ text_recent_chats }}</label>
            <div class="col-sm-10">
              <input type="text" name="recent_chats" value="{{ recent_chats }}" placeholder="{{ text_recent_chats }}" id="input-recent_chats" class="form-control" />
              {% if error_recent_chats %}
                <div class="text-danger">{{ error_recent_chats }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-send_message_url">{{ text_send_message_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="send_message_url" value="{{ send_message_url }}" placeholder="{{ text_send_message_url }}" id="input-send_message_url" class="form-control" />
              {% if error_send_message_url %}
                <div class="text-danger">{{ error_send_message_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-specialized_chats">{{ text_specialized_chats }}</label>
            <div class="col-sm-10">
              <input type="text" name="specialized_chats" value="{{ specialized_chats }}" placeholder="{{ text_specialized_chats }}" id="input-specialized_chats" class="form-control" />
              {% if error_specialized_chats %}
                <div class="text-danger">{{ error_specialized_chats }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-upload_file_url">{{ text_upload_file_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="upload_file_url" value="{{ upload_file_url }}" placeholder="{{ text_upload_file_url }}" id="input-upload_file_url" class="form-control" />
              {% if error_upload_file_url %}
                <div class="text-danger">{{ error_upload_file_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="text-danger">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-websocket_config">{{ text_websocket_config }}</label>
            <div class="col-sm-10">
              <input type="text" name="websocket_config" value="{{ websocket_config }}" placeholder="{{ text_websocket_config }}" id="input-websocket_config" class="form-control" />
              {% if error_websocket_config %}
                <div class="text-danger">{{ error_websocket_config }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}