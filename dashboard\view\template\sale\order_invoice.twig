<!DOCTYPE html>
<html dir="{{ direction }}" lang="{{ lang }}">
<head>
<meta charset="UTF-8" />
<title>{{ title }}</title>
<base href="{{ base }}" />
<link href="view/javascript/bootstrap/css/bootstrap.css" rel="stylesheet" media="all" />
<script type="text/javascript" src="view/javascript/jquery/jquery-3.7.0.min.js"></script>
<script type="text/javascript" src="view/javascript/bootstrap/js/bootstrap.min.js"></script>
<link href="view/javascript/font-awesome/css/font-awesome.min.css" type="text/css" rel="stylesheet" />
<link type="text/css" href="view/stylesheet/stylesheet.css" rel="stylesheet" media="all" />
</head>
<body>
<div class="container">
  {% for order in orders %}
  <div style="page-break-after: always;">
    <h1>{% if order.invoice_no %}{{ text_invoice }} #{{ order.invoice_no }}{% else %}{{ order.text_order }}{% endif %}</h1>
    <table class="table table-bordered">
      <thead>
        <tr>
          <td colspan="2">{{ text_order_detail }}</td>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td style="width: 50%;"><address>
            <strong>{{ order.store_name }}</strong><br/>
            {{ order.store_address }}
            </address>
            <b>{{ text_telephone }}</b> {{ order.store_telephone }}<br/>
            {% if order.store_fax %}
            <b>{{ text_fax }}</b> {{ order.store_fax }}<br/>
            {% endif %}
            <b>{{ text_email }}</b> {{ order.store_email }}<br/>
            <b>{{ text_website }}</b> <a href="{{ order.store_url }}">{{ order.store_url }}</a></td>
          <td style="width: 50%;"><b>{{ text_date_added }}</b> {{ order.date_added }}<br/>
            {% if order.invoice_no %}
            <b>{{ text_invoice_no }}</b> {{ order.invoice_no }}<br/>
            {% endif %}
            <b>{{ text_order_id }}</b> {{ order.order_id }}<br/>
            <b>{{ text_payment_method }}</b> {{ order.payment_method }}<br/>
            {% if order.shipping_method %}
            <b>{{ text_shipping_method }}</b> {{ order.shipping_method }}<br/>
            {% endif %}</td>
        </tr>
      </tbody>
    </table>
    <table class="table table-bordered">
      <thead>
        <tr>
          <td style="width: 50%;"><b>{{ text_payment_address }}</b></td>
          <td style="width: 50%;"><b>{{ text_shipping_address }}</b></td>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td><address>
            {{ order.payment_address }}
            </address></td>
          <td><address>
            {{ order.shipping_address }}
            </address></td>
        </tr>
      </tbody>
    </table>
    <table class="table table-bordered">
      <thead>
        <tr>
          <td><b>{{ column_product }}</b></td>
          <td><b>{{ column_model }}</b></td>
          <td class="text-right"><b>{{ column_quantity }}</b></td>
          <td class="text-right"><b>{{ column_price }}</b></td>
          <td class="text-right"><b>{{ column_total }}</b></td>
        </tr>
      </thead>
      <tbody>
        {% for product in order.product %}
        <tr>
          <td>{{ product.name }}
            {% for option in product.option %}
            <br/>
            &nbsp;<small> - {{ option.name }}: {{ option.value }}</small>
            {% endfor %}</td>
          <td>{{ product.model }}</td>
          <td class="text-right">{{ product.quantity }}</td>
          <td class="text-right">{{ product.price }}</td>
          <td class="text-right">{{ product.total }}</td>
        </tr>
        {% endfor %}
        {% for voucher in order.voucher %}
        <tr>
          <td>{{ voucher.description }}</td>
          <td></td>
          <td class="text-right">1</td>
          <td class="text-right">{{ voucher.amount }}</td>
          <td class="text-right">{{ voucher.amount }}</td>
        </tr>
        {% endfor %}
        {% for total in order.total %}
        <tr>
          <td class="text-right" colspan="4"><b>{{ total.title }}</b></td>
          <td class="text-right">{{ total.text }}</td>
        </tr>
        {% endfor %}
      </tbody>
    </table>
    {% if order.comment %}
    <table class="table table-bordered">
      <thead>
        <tr>
          <td><b>{{ text_comment }}</b></td>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>{{ order.comment }}</td>
        </tr>
      </tbody>
    </table>
    {% endif %}
  </div>
  {% endfor %}
</div>
</body>
</html>
