{{ header }}{{ column_left }}

<!-- Enterprise Grade Plus CSS for Sales Analysis -->
<style>
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --danger-color: #e74c3c;
    --warning-color: #f39c12;
    --info-color: #17a2b8;
    --sales-color: #28a745;
    --light-bg: #f8f9fa;
    --border-color: #dee2e6;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

.sales-analysis-container {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: var(--shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.sales-analysis-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--sales-color), var(--primary-color), var(--secondary-color));
}

.sales-analysis-header {
    text-align: center;
    border-bottom: 3px solid var(--sales-color);
    padding-bottom: 20px;
    margin-bottom: 30px;
    position: relative;
}

.sales-analysis-header h2 {
    color: var(--sales-color);
    font-weight: 700;
    font-size: 1.8rem;
    margin: 0;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.sales-kpis {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.sales-kpi {
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    text-align: center;
    box-shadow: var(--shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.sales-kpi:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0,0,0,0.1);
}

.sales-kpi::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
}

.sales-kpi.total-sales::before { background: var(--sales-color); }
.sales-kpi.total-orders::before { background: var(--info-color); }
.sales-kpi.average-order::before { background: var(--success-color); }
.sales-kpi.top-customer::before { background: var(--warning-color); }
.sales-kpi.growth-rate::before { background: var(--secondary-color); }

.sales-kpi h4 {
    color: var(--primary-color);
    margin-bottom: 10px;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.sales-kpi .amount {
    font-size: 1.4rem;
    font-weight: 700;
    font-family: 'Courier New', monospace;
    margin-bottom: 5px;
}

.sales-kpi .description {
    font-size: 0.8rem;
    color: #6c757d;
    font-weight: 500;
}

.kpi-total-sales .amount { color: var(--sales-color); }
.kpi-total-orders .amount { color: var(--info-color); }
.kpi-average-order .amount { color: var(--success-color); }
.kpi-top-customer .amount { color: var(--warning-color); }
.kpi-growth-rate .amount { color: var(--secondary-color); }

.sales-charts {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.sales-chart {
    background: #ffffff;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: var(--shadow);
}

.sales-chart h4 {
    color: var(--primary-color);
    margin-bottom: 15px;
    font-weight: 600;
    text-align: center;
}

.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
}

.sales-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    background: #ffffff;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.sales-table th {
    background: linear-gradient(135deg, var(--sales-color), #1e7e34);
    color: white;
    padding: 15px 12px;
    text-align: center;
    font-weight: 600;
    font-size: 0.9rem;
    border-bottom: 2px solid var(--border-color);
}

.sales-table td {
    padding: 12px;
    border-bottom: 1px solid var(--border-color);
    text-align: center;
    transition: var(--transition);
}

.sales-table tbody tr:hover {
    background: var(--light-bg);
    transform: scale(1.005);
}

.sales-table tbody tr:nth-child(even) {
    background: #fafbfc;
}

.amount-cell {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    text-align: {{ direction == 'rtl' ? 'right' : 'left' }};
}

.amount-positive { 
    color: var(--sales-color); 
    font-weight: 600;
}

.amount-negative { 
    color: var(--danger-color); 
    font-weight: 600;
}

.amount-neutral { 
    color: var(--secondary-color); 
    font-weight: 600;
}

/* Filter Panel */
.filter-panel {
    background: linear-gradient(135deg, #f8f9fa, #ffffff);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: var(--shadow);
}

.filter-panel h4 {
    color: var(--sales-color);
    margin-bottom: 15px;
    font-weight: 600;
}

.form-group {
    margin-bottom: 15px;
}

.form-label {
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 5px;
    display: block;
}

.form-control {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 8px 12px;
    transition: var(--transition);
    width: 100%;
}

.form-control:focus {
    border-color: var(--sales-color);
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    outline: none;
}

/* Trend Indicators */
.trend-indicator {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    font-size: 0.8rem;
    font-weight: 600;
}

.trend-up {
    color: var(--sales-color);
}

.trend-down {
    color: var(--danger-color);
}

.trend-neutral {
    color: #6c757d;
}

/* RTL Support */
[dir="rtl"] .sales-table {
    direction: rtl;
}

[dir="rtl"] .amount-cell {
    text-align: right;
}

/* Print Styles */
@media print {
    .sales-analysis-container {
        box-shadow: none;
        border: 1px solid #000;
    }
    
    .sales-table th {
        background: #f0f0f0 !important;
        color: #000 !important;
    }
    
    .btn, .panel-heading, .filter-panel {
        display: none !important;
    }
    
    .sales-charts {
        page-break-inside: avoid;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .sales-table {
        font-size: 0.8rem;
    }
    
    .sales-table th,
    .sales-table td {
        padding: 8px 6px;
    }
    
    .sales-kpis {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .sales-charts {
        grid-template-columns: 1fr;
    }
}
</style>

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-{{ direction == 'rtl' ? 'start' : 'end' }}">
        <div class="btn-group" role="group" aria-label="{{ text_actions }}">
          <button type="button" class="btn btn-success btn-lg" onclick="generateReport()"
                  data-toggle="tooltip" title="{{ button_generate_report }}">
            <i class="fa fa-line-chart"></i> {{ button_generate }}
          </button>
          <div class="btn-group" role="group">
            <button type="button" class="btn btn-info dropdown-toggle" data-toggle="dropdown"
                    aria-expanded="false" data-toggle="tooltip" title="{{ text_export_options }}">
              <i class="fa fa-download"></i> {{ text_export }}
            </button>
            <ul class="dropdown-menu">
              <li><a class="dropdown-item" href="#" onclick="exportAnalysis('excel')">
                <i class="fa fa-file-excel text-success"></i> Excel
              </a></li>
              <li><a class="dropdown-item" href="#" onclick="exportAnalysis('pdf')">
                <i class="fa fa-file-pdf text-danger"></i> PDF
              </a></li>
              <li><a class="dropdown-item" href="#" onclick="exportAnalysis('csv')">
                <i class="fa fa-file-csv text-info"></i> CSV
              </a></li>
              <li><hr class="dropdown-divider"></li>
              <li><a class="dropdown-item" href="#" onclick="printAnalysis()">
                <i class="fa fa-print text-muted"></i> {{ text_print }}
              </a></li>
            </ul>
          </div>
          <button type="button" class="btn btn-primary" onclick="compareAnalysis()"
                  data-toggle="tooltip" title="{{ button_compare_periods }}">
            <i class="fa fa-balance-scale"></i>
          </button>
          <button type="button" class="btn btn-warning" onclick="customerAnalysis()"
                  data-toggle="tooltip" title="{{ button_customer_analysis }}">
            <i class="fa fa-users"></i>
          </button>
        </div>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li class="breadcrumb-item"><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>

  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible">
      <i class="fa fa-exclamation-triangle"></i>
      {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}

    {% if success %}
    <div class="alert alert-success alert-dismissible">
      <i class="fa fa-check-circle"></i>
      {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}

    <!-- Filter Panel -->
    <div class="filter-panel">
      <h4>{{ text_analysis_filters }}</h4>
      <form id="sales-analysis-filter-form" method="post">
        <div class="row">
          <div class="col-md-3">
            <div class="form-group">
              <label for="date_start" class="form-label">{{ entry_date_start }}</label>
              <input type="date" name="date_start" id="date_start" value="{{ date_start }}" class="form-control" required>
            </div>
          </div>
          <div class="col-md-3">
            <div class="form-group">
              <label for="date_end" class="form-label">{{ entry_date_end }}</label>
              <input type="date" name="date_end" id="date_end" value="{{ date_end }}" class="form-control" required>
            </div>
          </div>
          <div class="col-md-2">
            <div class="form-group">
              <label for="customer_id" class="form-label">{{ entry_customer }}</label>
              <select name="customer_id" id="customer_id" class="form-control">
                <option value="">{{ text_all_customers }}</option>
                {% for customer in customers %}
                <option value="{{ customer.customer_id }}"{% if customer.customer_id == customer_id %} selected{% endif %}>{{ customer.name }}</option>
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="col-md-2">
            <div class="form-group">
              <label for="category_id" class="form-label">{{ entry_category }}</label>
              <select name="category_id" id="category_id" class="form-control">
                <option value="">{{ text_all_categories }}</option>
                {% for category in categories %}
                <option value="{{ category.category_id }}"{% if category.category_id == category_id %} selected{% endif %}>{{ category.name }}</option>
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="col-md-2">
            <div class="form-group">
              <label class="form-label">&nbsp;</label>
              <div>
                <button type="submit" class="btn btn-primary btn-block">
                  <i class="fa fa-search"></i> {{ button_analyze }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>

    <!-- Sales Analysis Content -->
    {% if analysis_data %}
    <!-- Key Performance Indicators -->
    <div class="sales-kpis">
      <div class="sales-kpi kpi-total-sales total-sales">
        <h4>{{ text_total_sales }}</h4>
        <div class="amount">{{ analysis_data.total_sales_formatted }}</div>
        <div class="description">{{ text_total_sales_description }}</div>
      </div>

      <div class="sales-kpi kpi-total-orders total-orders">
        <h4>{{ text_total_orders }}</h4>
        <div class="amount">{{ analysis_data.total_orders }}</div>
        <div class="description">{{ text_total_orders_description }}</div>
      </div>

      <div class="sales-kpi kpi-average-order average-order">
        <h4>{{ text_average_order }}</h4>
        <div class="amount">{{ analysis_data.average_order_formatted }}</div>
        <div class="description">{{ text_average_order_description }}</div>
      </div>

      <div class="sales-kpi kpi-top-customer top-customer">
        <h4>{{ text_top_customer }}</h4>
        <div class="amount">{{ analysis_data.top_customer_name }}</div>
        <div class="description">{{ analysis_data.top_customer_amount_formatted }}</div>
      </div>

      <div class="sales-kpi kpi-growth-rate growth-rate">
        <h4>{{ text_growth_rate }}</h4>
        <div class="amount">{{ analysis_data.growth_rate }}%</div>
        <div class="description">{{ text_growth_rate_description }}</div>
      </div>
    </div>

    <!-- Charts Section -->
    <div class="sales-charts">
      <div class="sales-chart">
        <h4>{{ text_sales_trend_chart }}</h4>
        <div class="chart-container">
          <canvas id="salesTrendChart"></canvas>
        </div>
      </div>

      <div class="sales-chart">
        <h4>{{ text_customer_breakdown_chart }}</h4>
        <div class="chart-container">
          <canvas id="customerBreakdownChart"></canvas>
        </div>
      </div>

      <div class="sales-chart">
        <h4>{{ text_category_analysis_chart }}</h4>
        <div class="chart-container">
          <canvas id="categoryAnalysisChart"></canvas>
        </div>
      </div>

      <div class="sales-chart">
        <h4>{{ text_monthly_comparison_chart }}</h4>
        <div class="chart-container">
          <canvas id="monthlyComparisonChart"></canvas>
        </div>
      </div>
    </div>

    <!-- Top Products Table -->
    <div class="sales-analysis-container">
      <div class="sales-analysis-header">
        <h2>{{ text_top_products }}</h2>
      </div>

      <div class="table-responsive">
        <table class="sales-table" id="top-products-table">
          <thead>
            <tr>
              <th>{{ column_product_name }}</th>
              <th>{{ column_quantity_sold }}</th>
              <th>{{ column_total_sales }}</th>
              <th>{{ column_average_price }}</th>
              <th>{{ column_orders_count }}</th>
              <th>{{ column_last_sale_date }}</th>
              <th>{{ column_trend }}</th>
            </tr>
          </thead>
          <tbody>
            {% for product in analysis_data.top_products %}
            <tr>
              <td>{{ product.name }}</td>
              <td>{{ product.quantity_sold }}</td>
              <td class="amount-cell amount-positive">{{ product.total_sales_formatted }}</td>
              <td class="amount-cell amount-neutral">{{ product.average_price_formatted }}</td>
              <td>{{ product.orders_count }}</td>
              <td>{{ product.last_sale_date_formatted }}</td>
              <td>
                <div class="trend-indicator trend-{{ product.trend }}">
                  <i class="fa fa-arrow-{{ product.trend == 'up' ? 'up' : product.trend == 'down' ? 'down' : 'right' }}"></i>
                  {{ product.trend_percentage }}%
                </div>
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>

    <!-- Top Customers Table -->
    <div class="sales-analysis-container">
      <div class="sales-analysis-header">
        <h2>{{ text_top_customers }}</h2>
      </div>

      <div class="table-responsive">
        <table class="sales-table" id="top-customers-table">
          <thead>
            <tr>
              <th>{{ column_customer_name }}</th>
              <th>{{ column_total_sales }}</th>
              <th>{{ column_orders_count }}</th>
              <th>{{ column_average_order_value }}</th>
              <th>{{ column_last_order_date }}</th>
              <th>{{ column_customer_segment }}</th>
              <th>{{ column_loyalty_score }}</th>
            </tr>
          </thead>
          <tbody>
            {% for customer in analysis_data.top_customers %}
            <tr>
              <td>{{ customer.name }}</td>
              <td class="amount-cell amount-positive">{{ customer.total_sales_formatted }}</td>
              <td>{{ customer.orders_count }}</td>
              <td class="amount-cell amount-neutral">{{ customer.average_order_value_formatted }}</td>
              <td>{{ customer.last_order_date_formatted }}</td>
              <td>
                <span class="badge bg-{{ customer.segment_color }}">{{ customer.segment }}</span>
              </td>
              <td>
                <div class="loyalty-score score-{{ customer.loyalty_level }}">
                  {{ customer.loyalty_score }}/100
                  <div class="score-bar">
                    <div class="score-fill" style="width: {{ customer.loyalty_score }}%"></div>
                  </div>
                </div>
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>
    {% else %}
    <div class="alert alert-info">
      <i class="fa fa-info-circle"></i>
      {{ text_no_data }}
    </div>
    {% endif %}
  </div>
</div>

<script>
// Enterprise Grade Plus JavaScript for Sales Analysis
class SalesAnalysisManager {
    constructor() {
        this.charts = {};
        this.initializeTooltips();
        this.initializeDataTables();
        this.initializeKeyboardShortcuts();
        this.initializeCharts();
        this.initializeFormValidation();
    }

    initializeTooltips() {
        $('[data-toggle="tooltip"]').tooltip();
    }

    initializeDataTables() {
        const tables = ['top-products-table', 'top-customers-table'];

        tables.forEach(tableId => {
            const table = document.getElementById(tableId);
            if (table && typeof $ !== 'undefined' && $.fn.DataTable) {
                $(table).DataTable({
                    responsive: true,
                    pageLength: 25,
                    order: [[2, 'desc']], // Sort by total sales desc
                    columnDefs: [
                        { targets: [2, 3], className: 'text-end' },
                        { targets: [6], className: 'text-center' }
                    ],
                    language: {
                        url: '{{ direction == "rtl" ? "/view/javascript/jquery/datatables-ar.json" : "/view/javascript/jquery/datatables-en.json" }}'
                    }
                });
            }
        });
    }

    initializeKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case 'g':
                        e.preventDefault();
                        this.generateReport();
                        break;
                    case 'e':
                        e.preventDefault();
                        this.showExportMenu();
                        break;
                    case 'p':
                        e.preventDefault();
                        this.printAnalysis();
                        break;
                    case 'c':
                        e.preventDefault();
                        this.compareAnalysis();
                        break;
                    case 'u':
                        e.preventDefault();
                        this.customerAnalysis();
                        break;
                }
            }
        });
    }

    initializeCharts() {
        // Chart initialization would go here
        console.log('Charts initialized');
    }

    initializeFormValidation() {
        const form = document.getElementById('sales-analysis-filter-form');
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.validateAndSubmitForm();
            });
        }
    }

    validateAndSubmitForm() {
        const form = document.getElementById('sales-analysis-filter-form');
        const formData = new FormData(form);

        // Validate date range
        const startDate = new Date(formData.get('date_start'));
        const endDate = new Date(formData.get('date_end'));

        if (startDate >= endDate) {
            this.showAlert('{{ error_invalid_date_range }}', 'danger');
            return;
        }

        // Submit form
        this.showLoadingState(true);
        form.submit();
    }

    generateReport() {
        this.showLoadingState(true);

        const formData = this.getFormData();

        fetch('{{ url_link('accounts/sales_analysis', 'generate') }}', {
            method: 'POST',
            body: JSON.stringify(formData),
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            this.showLoadingState(false);
            if (data.success) {
                this.showAlert('{{ success_report_generated }}', 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                this.showAlert(data.error || '{{ error_report_generation }}', 'danger');
            }
        })
        .catch(error => {
            this.showLoadingState(false);
            this.showAlert('{{ error_report_generation }}: ' + error.message, 'danger');
        });
    }

    exportAnalysis(format) {
        const params = new URLSearchParams({
            format: format,
            date_start: document.getElementById('date_start').value,
            date_end: document.getElementById('date_end').value,
            customer_id: document.getElementById('customer_id').value,
            category_id: document.getElementById('category_id').value
        });

        this.showAlert('{{ text_exporting }}...', 'info');
        window.open('{{ export_url }}&' + params.toString(), '_blank');
    }

    printAnalysis() {
        window.print();
    }

    compareAnalysis() {
        window.open('{{ url_link('accounts/sales_analysis', 'compare') }}', '_blank');
    }

    customerAnalysis() {
        window.open('{{ url_link('accounts/sales_analysis', 'customers') }}', '_blank');
    }

    getFormData() {
        const form = document.getElementById('sales-analysis-filter-form');
        const formData = new FormData(form);
        const data = {};

        for (let [key, value] of formData.entries()) {
            data[key] = value;
        }

        return data;
    }

    showExportMenu() {
        const exportButton = document.querySelector('.dropdown-toggle');
        if (exportButton) {
            exportButton.click();
        }
    }

    showLoadingState(show) {
        const buttons = document.querySelectorAll('.btn');
        buttons.forEach(btn => {
            if (show) {
                btn.disabled = true;
                if (btn.querySelector('i')) {
                    btn.querySelector('i').className = 'fa fa-spinner fa-spin';
                }
            } else {
                btn.disabled = false;
                location.reload();
            }
        });
    }

    showAlert(message, type) {
        const alertContainer = document.createElement('div');
        alertContainer.className = `alert alert-${type} alert-dismissible fade show`;
        alertContainer.innerHTML = `
            <i class="fa fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'}"></i>
            ${message}
            <button type="button" class="close" data-dismiss="alert">&times;</button>
        `;

        const container = document.querySelector('#content .container-fluid');
        container.insertBefore(alertContainer, container.firstChild);

        setTimeout(() => {
            alertContainer.remove();
        }, 5000);
    }
}

// Global functions for backward compatibility
function generateReport() {
    salesAnalysisManager.generateReport();
}

function exportAnalysis(format) {
    salesAnalysisManager.exportAnalysis(format);
}

function printAnalysis() {
    salesAnalysisManager.printAnalysis();
}

function compareAnalysis() {
    salesAnalysisManager.compareAnalysis();
}

function customerAnalysis() {
    salesAnalysisManager.customerAnalysis();
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.salesAnalysisManager = new SalesAnalysisManager();
});
</script>

{{ footer }}
