<?php
class ControllerCustomerCreditLimit extends Controller {
    public function index() {
        $this->load->language('customer/credit_limit');
        $this->document->setTitle($this->language->get('heading_title'));
        
        $this->load->model('customer/customer');
        
        $data['breadcrumbs'] = array();
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_customer'),
            'href' => $this->url->link('customer/customer', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('customer/credit_limit', 'user_token=' . $this->session->data['user_token'], true)
        );
        
        $data['user_token'] = $this->session->data['user_token'];
        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');
        
        $this->response->setOutput($this->load->view('customer/credit_limit', $data));
    }
    
    public function getList() {
        $this->load->language('customer/credit_limit');
        $this->load->model('customer/customer');
        
        $data['customers'] = array();
        
        $results = $this->model_customer_customer->getCustomers();
        
        foreach ($results as $result) {
            $credit_limit = $this->model_customer_customer->getCustomerCreditLimit($result['customer_id']);
            
            $data['customers'][] = array(
                'customer_id' => $result['customer_id'],
                'name' => $result['name'],
                'email' => $result['email'],
                'telephone' => $result['telephone'],
                'credit_limit' => $credit_limit ? $credit_limit['credit_limit'] : 0,
                'current_balance' => $credit_limit ? $credit_limit['current_balance'] : 0,
                'available_credit' => $credit_limit ? ($credit_limit['credit_limit'] - $credit_limit['current_balance']) : 0,
                'status' => $credit_limit ? $credit_limit['status'] : 'active',
                'edit' => $this->url->link('customer/credit_limit/edit', 'user_token=' . $this->session->data['user_token'] . '&customer_id=' . $result['customer_id'], true)
            );
        }
        
        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($data));
    }
    
    public function edit() {
        $this->load->language('customer/credit_limit');
        $this->load->model('customer/customer');
        
        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validate()) {
            $this->model_customer_customer->updateCustomerCreditLimit($this->request->get['customer_id'], $this->request->post);
            
            $this->session->data['success'] = $this->language->get('text_success');
            
            $this->response->redirect($this->url->link('customer/credit_limit', 'user_token=' . $this->session->data['user_token'], true));
        }
        
        $this->getForm();
    }
    
    protected function getForm() {
        $this->load->language('customer/credit_limit');
        $this->load->model('customer/customer');
        
        $data['text_form'] = !isset($this->request->get['customer_id']) ? $this->language->get('text_add') : $this->language->get('text_edit');
        
        if (isset($this->request->get['customer_id'])) {
            $customer_info = $this->model_customer_customer->getCustomer($this->request->get['customer_id']);
            $credit_limit_info = $this->model_customer_customer->getCustomerCreditLimit($this->request->get['customer_id']);
        }
        
        if (isset($this->request->post['credit_limit'])) {
            $data['credit_limit'] = $this->request->post['credit_limit'];
        } elseif (!empty($credit_limit_info)) {
            $data['credit_limit'] = $credit_limit_info['credit_limit'];
        } else {
            $data['credit_limit'] = 0;
        }
        
        if (isset($this->request->post['payment_terms'])) {
            $data['payment_terms'] = $this->request->post['payment_terms'];
        } elseif (!empty($credit_limit_info)) {
            $data['payment_terms'] = $credit_limit_info['payment_terms'];
        } else {
            $data['payment_terms'] = 30;
        }
        
        if (isset($this->request->post['status'])) {
            $data['status'] = $this->request->post['status'];
        } elseif (!empty($credit_limit_info)) {
            $data['status'] = $credit_limit_info['status'];
        } else {
            $data['status'] = 'active';
        }
        
        if (isset($this->request->post['notes'])) {
            $data['notes'] = $this->request->post['notes'];
        } elseif (!empty($credit_limit_info)) {
            $data['notes'] = $credit_limit_info['notes'];
        } else {
            $data['notes'] = '';
        }
        
        $data['customer_name'] = $customer_info['firstname'] . ' ' . $customer_info['lastname'];
        $data['customer_email'] = $customer_info['email'];
        
        $data['user_token'] = $this->session->data['user_token'];
        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');
        
        $this->response->setOutput($this->load->view('customer/credit_limit_form', $data));
    }
    
    protected function validate() {
        if (!$this->user->hasPermission('modify', 'customer/credit_limit')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }
        
        if (isset($this->request->post['credit_limit']) && $this->request->post['credit_limit'] < 0) {
            $this->error['credit_limit'] = $this->language->get('error_credit_limit');
        }
        
        return !$this->error;
    }
} 