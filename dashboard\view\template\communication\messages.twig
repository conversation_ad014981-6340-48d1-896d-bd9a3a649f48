{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="submit" form="communication\messages-form" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fa fa-save"></i></button>
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="close" data-dismiss="alert">&times;</button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="close" data-dismiss="alert">&times;</button>
      </div>
    {{% endif %}}

    <div class="panel panel-default">
      <div class="panel-heading"><i class="fa fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}bar-chart{% elif screen_type == 'settings' %}cog{% else %}pencil{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="panel-body">
        <form id="communication\messages-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-attachments">{{ text_attachments }}</label>
            <div class="col-sm-10">
              <input type="text" name="attachments" value="{{ attachments }}" placeholder="{{ text_attachments }}" id="input-attachments" class="form-control" />
              {% if error_attachments %}
                <div class="text-danger">{{ error_attachments }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-back">{{ text_back }}</label>
            <div class="col-sm-10">
              <input type="text" name="back" value="{{ back }}" placeholder="{{ text_back }}" id="input-back" class="form-control" />
              {% if error_back %}
                <div class="text-danger">{{ error_back }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-compose">{{ text_compose }}</label>
            <div class="col-sm-10">
              <input type="text" name="compose" value="{{ compose }}" placeholder="{{ text_compose }}" id="input-compose" class="form-control" />
              {% if error_compose %}
                <div class="text-danger">{{ error_compose }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-delete">{{ text_delete }}</label>
            <div class="col-sm-10">
              <input type="text" name="delete" value="{{ delete }}" placeholder="{{ text_delete }}" id="input-delete" class="form-control" />
              {% if error_delete %}
                <div class="text-danger">{{ error_delete }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-deleted_messages">{{ text_deleted_messages }}</label>
            <div class="col-sm-10">
              <input type="text" name="deleted_messages" value="{{ deleted_messages }}" placeholder="{{ text_deleted_messages }}" id="input-deleted_messages" class="form-control" />
              {% if error_deleted_messages %}
                <div class="text-danger">{{ error_deleted_messages }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-deleted_total">{{ text_deleted_total }}</label>
            <div class="col-sm-10">
              <input type="text" name="deleted_total" value="{{ deleted_total }}" placeholder="{{ text_deleted_total }}" id="input-deleted_total" class="form-control" />
              {% if error_deleted_total %}
                <div class="text-danger">{{ error_deleted_total }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-forward">{{ text_forward }}</label>
            <div class="col-sm-10">
              <input type="text" name="forward" value="{{ forward }}" placeholder="{{ text_forward }}" id="input-forward" class="form-control" />
              {% if error_forward %}
                <div class="text-danger">{{ error_forward }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="text-danger">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-inbox_messages">{{ text_inbox_messages }}</label>
            <div class="col-sm-10">
              <input type="text" name="inbox_messages" value="{{ inbox_messages }}" placeholder="{{ text_inbox_messages }}" id="input-inbox_messages" class="form-control" />
              {% if error_inbox_messages %}
                <div class="text-danger">{{ error_inbox_messages }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-inbox_total">{{ text_inbox_total }}</label>
            <div class="col-sm-10">
              <input type="text" name="inbox_total" value="{{ inbox_total }}" placeholder="{{ text_inbox_total }}" id="input-inbox_total" class="form-control" />
              {% if error_inbox_total %}
                <div class="text-danger">{{ error_inbox_total }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-inbox_unread">{{ text_inbox_unread }}</label>
            <div class="col-sm-10">
              <input type="text" name="inbox_unread" value="{{ inbox_unread }}" placeholder="{{ text_inbox_unread }}" id="input-inbox_unread" class="form-control" />
              {% if error_inbox_unread %}
                <div class="text-danger">{{ error_inbox_unread }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-message">{{ text_message }}</label>
            <div class="col-sm-10">
              <input type="text" name="message" value="{{ message }}" placeholder="{{ text_message }}" id="input-message" class="form-control" />
              {% if error_message %}
                <div class="text-danger">{{ error_message }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-message_types">{{ text_message_types }}</label>
            <div class="col-sm-10">
              <input type="text" name="message_types" value="{{ message_types }}" placeholder="{{ text_message_types }}" id="input-message_types" class="form-control" />
              {% if error_message_types %}
                <div class="text-danger">{{ error_message_types }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-priorities">{{ text_priorities }}</label>
            <div class="col-sm-10">
              <input type="text" name="priorities" value="{{ priorities }}" placeholder="{{ text_priorities }}" id="input-priorities" class="form-control" />
              {% if error_priorities %}
                <div class="text-danger">{{ error_priorities }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-refresh">{{ text_refresh }}</label>
            <div class="col-sm-10">
              <input type="text" name="refresh" value="{{ refresh }}" placeholder="{{ text_refresh }}" id="input-refresh" class="form-control" />
              {% if error_refresh %}
                <div class="text-danger">{{ error_refresh }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-replies">{{ text_replies }}</label>
            <div class="col-sm-10">
              <input type="text" name="replies" value="{{ replies }}" placeholder="{{ text_replies }}" id="input-replies" class="form-control" />
              {% if error_replies %}
                <div class="text-danger">{{ error_replies }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-reply">{{ text_reply }}</label>
            <div class="col-sm-10">
              <input type="text" name="reply" value="{{ reply }}" placeholder="{{ text_reply }}" id="input-reply" class="form-control" />
              {% if error_reply %}
                <div class="text-danger">{{ error_reply }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-sent_messages">{{ text_sent_messages }}</label>
            <div class="col-sm-10">
              <input type="text" name="sent_messages" value="{{ sent_messages }}" placeholder="{{ text_sent_messages }}" id="input-sent_messages" class="form-control" />
              {% if error_sent_messages %}
                <div class="text-danger">{{ error_sent_messages }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-sent_total">{{ text_sent_total }}</label>
            <div class="col-sm-10">
              <input type="text" name="sent_total" value="{{ sent_total }}" placeholder="{{ text_sent_total }}" id="input-sent_total" class="form-control" />
              {% if error_sent_total %}
                <div class="text-danger">{{ error_sent_total }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-teams">{{ text_teams }}</label>
            <div class="col-sm-10">
              <input type="text" name="teams" value="{{ teams }}" placeholder="{{ text_teams }}" id="input-teams" class="form-control" />
              {% if error_teams %}
                <div class="text-danger">{{ error_teams }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="text-danger">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-users">{{ text_users }}</label>
            <div class="col-sm-10">
              <input type="text" name="users" value="{{ users }}" placeholder="{{ text_users }}" id="input-users" class="form-control" />
              {% if error_users %}
                <div class="text-danger">{{ error_users }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}