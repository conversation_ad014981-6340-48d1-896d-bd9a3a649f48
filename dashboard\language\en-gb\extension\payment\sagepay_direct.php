<?php
// Heading
$_['heading_title']			  = 'SagePay Direct';

// Text
$_['text_extension']		  = 'Extensions';
$_['text_success']			  = 'Success: You have modified SagePay account details!';
$_['text_edit']               = 'Edit SagePay Direct';
$_['text_test']				  = 'Test';
$_['text_live']				  = 'Live';
$_['text_defered']			  = 'Defered';
$_['text_authenticate']		  = 'Authenticate';
$_['text_payment']			  = 'Payment';
$_['text_release_ok']		  = 'Release was successful';
$_['text_release_ok_order']	  = 'Release was successful, order status updated to success - settled';
$_['text_rebate_ok']		  = 'Rebate was successful';
$_['text_rebate_ok_order']	  = 'Rebate was successful, order status updated to rebated';
$_['text_void_ok']			  = 'Void was successful, order status updated to voided';
$_['text_payment_info']		  = 'Payment information';
$_['text_release_status']	  = 'Payment released';
$_['text_void_status']		  = 'Payment voided';
$_['text_rebate_status']	  = 'Payment rebated';
$_['text_order_ref']		  = 'Order ref';
$_['text_order_total']		  = 'Total authorised';
$_['text_total_released']	  = 'Total released';
$_['text_transactions']		  = 'Transactions';
$_['text_column_amount']	  = 'Amount';
$_['text_column_type']		  = 'Type';
$_['text_column_date_added']  = 'Created';
$_['text_confirm_void']		  = 'Are you sure you want to void the payment?';
$_['text_confirm_release']	  = 'Are you sure you want to release the payment?';
$_['text_confirm_rebate']	  = 'Are you sure you want to rebate the payment?';

// Entry
$_['entry_vendor']			  = 'Vendor';
$_['entry_test']			  = 'Test Mode';
$_['entry_transaction']		  = 'Transaction Method';
$_['entry_total']			  = 'Total';
$_['entry_order_status']	  = 'Order Status';
$_['entry_geo_zone']		  = 'Geo Zone';
$_['entry_status']			  = 'Status';
$_['entry_sort_order']		  = 'Sort Order';
$_['entry_debug']			  = 'Debug logging';
$_['entry_card']			  = 'Store Cards';
$_['entry_cron_job_token']	  = 'Secret Token';
$_['entry_cron_job_url']	  = 'Cron Job\'s URL';
$_['entry_last_cron_job_run'] = 'Last cron job\'s run time:';

// Help
$_['help_total']			  = 'The checkout total the order must reach before this payment method becomes active.';
$_['help_debug']			  = 'Enabling debug will write sensitive data to a log file. You should always disable unless instructed otherwise';
$_['help_transaction']		  = 'Transaction method MUST be set to Payment to allow subscription payments';
$_['help_cron_job_token']	  = 'Make this long and hard to guess';
$_['help_cron_job_url']		  = 'Set a cron job to call this URL';
$_['text_void']               = 'Void';
$_['text_payment']            = "Payment";
$_['text_rebate']             = 'Rebate';

// Button
$_['button_release']		  = 'Release';
$_['button_rebate']			  = 'Rebate / refund';
$_['button_void']			  = 'Void';

// Error
$_['error_permission']		  = 'Warning: You do not have permission to modify payment SagePay!';
$_['error_vendor']			  = 'Vendor ID Required!';
