# المهام الأساسية - المخزون والمستودعات
## Inventory Tasks 1 - Core Foundation

### 📋 **معلومات المهام:**
- **الملف:** tasks1.md
- **المدة:** 5 أيام
- **الأولوية:** حرجة (يجب إكمالها أولاً)
- **الاعتمادية:** لا توجد (نقطة البداية)

---

## 🎯 **الهدف الأساسي**
إنشاء الأساس الصلب لنظام المخزون مع التركيز على المستودعات وحركات المخزون الأساسية.

---

## 📋 **المهمة الأولى: إدارة المستودعات**
### **الملف:** `dashboard/controller/inventory/warehouse.php`

#### **اليوم الأول: التحليل والتخطيط**
- [ ] **1.1** قراءة وتحليل جدول `cod_warehouse` في minidb.txt
- [ ] **1.2** مراجعة العلاقات مع `cod_branch` و `cod_product_inventory`
- [ ] **1.3** دراسة نموذج الحسابات `chartaccount.php` كمرجع
- [ ] **1.4** تحديد الصلاحيات المطلوبة (hasPermission)
- [ ] **1.5** تحديد الإعدادات المطلوبة (config)

#### **اليوم الثاني: تطوير الكونترولر**
- [ ] **2.1** إنشاء `controller/inventory/warehouse.php`
- [ ] **2.2** تطبيق الدستور الشامل (20 قاعدة)
- [ ] **2.3** ربط الخدمات المركزية:
  - `model/activity_log.php` للتدقيق
  - `model/unified_document.php` للمستندات
  - `model/communication/unified_notification.php` للإشعارات
- [ ] **2.4** تطبيق نظام الصلاحيات المزدوج
- [ ] **2.5** إضافة دوال CRUD أساسية

#### **اليوم الثالث: تطوير الموديل**
- [ ] **3.1** إنشاء `model/inventory/warehouse.php`
- [ ] **3.2** تطوير دوال قاعدة البيانات:
  - `getWarehouses()` - قائمة المستودعات
  - `getWarehouse($warehouse_id)` - تفاصيل مستودع
  - `addWarehouse($data)` - إضافة مستودع
  - `editWarehouse($warehouse_id, $data)` - تعديل مستودع
  - `deleteWarehouse($warehouse_id)` - حذف مستودع
- [ ] **3.3** ربط مع جداول الفروع والمخزون
- [ ] **3.4** تطبيق نظام WAC في الحسابات

#### **اليوم الرابع: تطوير التيمبليت**
- [ ] **4.1** إنشاء `view/template/inventory/warehouse_list.twig`
- [ ] **4.2** إنشاء `view/template/inventory/warehouse_form.twig`
- [ ] **4.3** تطبيق التصميم المتجاوب
- [ ] **4.4** إضافة JavaScript للتفاعل
- [ ] **4.5** ربط مع نظام الإشعارات

#### **اليوم الخامس: ملفات اللغة والاختبار**
- [ ] **5.1** إنشاء ملفات اللغة (عربي/إنجليزي)
- [ ] **5.2** تطبيق المنهجية الصحيحة للترجمة
- [ ] **5.3** اختبار شامل للوظائف
- [ ] **5.4** اختبار التكامل مع الخدمات المركزية
- [ ] **5.5** توثيق الشاشة في newdocs

---

## 📋 **المهمة الثانية: حركات المخزون الأساسية**
### **الملف:** `dashboard/controller/inventory/stock_movement.php`

#### **اليوم السادس: التحليل والتخطيط**
- [ ] **6.1** قراءة وتحليل جدول `cod_product_movement` في minidb.txt
- [ ] **6.2** مراجعة العلاقات مع `cod_product_inventory` و `cod_journal_entry`
- [ ] **6.3** دراسة نموذج المحاسبة `journal.php` كمرجع
- [ ] **6.4** تحديد أنواع الحركات المطلوبة
- [ ] **6.5** تحديد القيود المحاسبية المرتبطة

#### **اليوم السابع: تطوير الكونترولر**
- [ ] **7.1** إنشاء `controller/inventory/stock_movement.php`
- [ ] **7.2** تطبيق الدستور الشامل
- [ ] **7.3** ربط الخدمات المركزية
- [ ] **7.4** تطبيق نظام الموافقات للحركات الكبيرة
- [ ] **7.5** إضافة دوال عرض وإضافة الحركات

#### **اليوم الثامن: تطوير الموديل**
- [ ] **8.1** إنشاء `model/inventory/stock_movement.php`
- [ ] **8.2** تطوير دوال قاعدة البيانات:
  - `getMovements($filters)` - قائمة الحركات
  - `getMovement($movement_id)` - تفاصيل حركة
  - `addMovement($data)` - إضافة حركة
  - `updateInventory($product_id, $quantity, $type)` - تحديث المخزون
  - `calculateWAC($product_id, $new_cost, $quantity)` - حساب WAC
- [ ] **8.3** ربط مع النظام المحاسبي
- [ ] **8.4** تطبيق قواعد العمل المعقدة

#### **اليوم التاسع: تطوير التيمبليت**
- [ ] **9.1** إنشاء `view/template/inventory/movement_list.twig`
- [ ] **9.2** إنشاء `view/template/inventory/movement_form.twig`
- [ ] **9.3** إضافة فلاتر متقدمة للبحث
- [ ] **9.4** إضافة تقارير سريعة
- [ ] **9.5** ربط مع نظام الطباعة

#### **اليوم العاشر: ملفات اللغة والاختبار**
- [ ] **10.1** إنشاء ملفات اللغة
- [ ] **10.2** اختبار العمليات المحاسبية
- [ ] **10.3** اختبار حسابات WAC
- [ ] **10.4** اختبار التكامل مع المستودعات
- [ ] **10.5** توثيق النظام

---

## 🔗 **الاعتمادية والترابط**

### **يعتمد على:**
- قاعدة البيانات الأساسية (minidb.txt)
- الخدمات المركزية الموجودة
- نظام الصلاحيات الأساسي

### **يؤثر على:**
- جميع المهام التالية (tasks2-6)
- النظام المحاسبي
- تقارير المخزون

---

## 📊 **مؤشرات النجاح**

### **المؤشرات التقنية:**
- [ ] **100% تطابق** مع جداول قاعدة البيانات
- [ ] **صفر أخطاء** في العمليات الأساسية
- [ ] **تكامل كامل** مع الخدمات المركزية
- [ ] **أداء أقل من 2 ثانية** للاستعلامات

### **المؤشرات الوظيفية:**
- [ ] **إدارة مستودعات** كاملة وسهلة
- [ ] **تتبع حركات** دقيق ومفصل
- [ ] **حسابات WAC** صحيحة 100%
- [ ] **قيود محاسبية** تلقائية ودقيقة

---

## 🚨 **تحذيرات مهمة**

### **نقاط حرجة:**
1. **لا تبدأ tasks2** قبل إكمال warehouse.php بالكامل
2. **اختبر WAC** بعناية قبل المتابعة
3. **تأكد من القيود المحاسبية** في كل حركة
4. **راجع الصلاحيات** مع كل دالة

### **متطلبات إلزامية:**
- تطبيق الدستور الشامل (20 قاعدة)
- ربط الخدمات المركزية في كل شاشة
- اختبار شامل قبل الانتقال للمهام التالية
- توثيق كامل في newdocs

---

**🎯 الهدف:** إنشاء أساس صلب وموثوق لنظام المخزون يدعم جميع العمليات المتقدمة في المهام التالية.
