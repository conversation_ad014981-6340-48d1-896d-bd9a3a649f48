<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/type/calendar_period.proto

namespace GPBMetadata\Google\Type;

class CalendarPeriod
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(hex2bin(
            "0ab3020a21676f6f676c652f747970652f63616c656e6461725f70657269" .
            "6f642e70726f746f120b676f6f676c652e747970652a7f0a0e43616c656e" .
            "646172506572696f64121f0a1b43414c454e4441525f504552494f445f55" .
            "4e535045434946494544100012070a03444159100112080a045745454b10" .
            "02120d0a09464f52544e49474854100312090a054d4f4e54481004120b0a" .
            "0751554152544552100512080a0448414c46100612080a04594541521007" .
            "42780a0f636f6d2e676f6f676c652e74797065421343616c656e64617250" .
            "6572696f6450726f746f50015a48676f6f676c652e676f6c616e672e6f72" .
            "672f67656e70726f746f2f676f6f676c65617069732f747970652f63616c" .
            "656e646172706572696f643b63616c656e646172706572696f64a2020347" .
            "5450620670726f746f33"
        ), true);

        static::$is_initialized = true;
    }
}

