<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/iam/v1/options.proto

namespace Google\Cloud\Iam\V1;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Encapsulates settings provided to GetIamPolicy.
 *
 * Generated from protobuf message <code>google.iam.v1.GetPolicyOptions</code>
 */
class GetPolicyOptions extends \Google\Protobuf\Internal\Message
{
    /**
     * Optional. The policy format version to be returned.
     * Valid values are 0, 1, and 3. Requests specifying an invalid value will be
     * rejected.
     * Requests for policies with any conditional bindings must specify version 3.
     * Policies without any conditional bindings may specify any valid value or
     * leave the field unset.
     *
     * Generated from protobuf field <code>int32 requested_policy_version = 1;</code>
     */
    private $requested_policy_version = 0;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $requested_policy_version
     *           Optional. The policy format version to be returned.
     *           Valid values are 0, 1, and 3. Requests specifying an invalid value will be
     *           rejected.
     *           Requests for policies with any conditional bindings must specify version 3.
     *           Policies without any conditional bindings may specify any valid value or
     *           leave the field unset.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Iam\V1\Options::initOnce();
        parent::__construct($data);
    }

    /**
     * Optional. The policy format version to be returned.
     * Valid values are 0, 1, and 3. Requests specifying an invalid value will be
     * rejected.
     * Requests for policies with any conditional bindings must specify version 3.
     * Policies without any conditional bindings may specify any valid value or
     * leave the field unset.
     *
     * Generated from protobuf field <code>int32 requested_policy_version = 1;</code>
     * @return int
     */
    public function getRequestedPolicyVersion()
    {
        return $this->requested_policy_version;
    }

    /**
     * Optional. The policy format version to be returned.
     * Valid values are 0, 1, and 3. Requests specifying an invalid value will be
     * rejected.
     * Requests for policies with any conditional bindings must specify version 3.
     * Policies without any conditional bindings may specify any valid value or
     * leave the field unset.
     *
     * Generated from protobuf field <code>int32 requested_policy_version = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setRequestedPolicyVersion($var)
    {
        GPBUtil::checkInt32($var);
        $this->requested_policy_version = $var;

        return $this;
    }

}

