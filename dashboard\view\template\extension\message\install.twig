{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="submit" form="form-install" data-toggle="tooltip" title="{{ button_install }}" class="btn btn-primary"><i class="fa fa-check"></i> {{ button_install }}</button>
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a>
      </div>
      <h1><i class="fa fa-envelope"></i> {{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-cogs"></i> {{ text_installation }}</h3>
      </div>
      <div class="panel-body">
        <form action="{{ action }}" method="post" enctype="multipart/form-data" id="form-install" class="form-horizontal">
          <div class="alert alert-info">
            <i class="fa fa-info-circle"></i> {{ text_install_description }}
          </div>
          
          <h4><i class="fa fa-check-square-o"></i> {{ text_prerequisites }}</h4>
          <div class="well">
            <ul>
              <li>AYM CMS version 1.0.0 or higher</li>
              <li>PHP version 7.0 or higher</li>
              <li>Write permission to upload directory</li>
            </ul>
          </div>
          
          <h4><i class="fa fa-list-ol"></i> {{ text_steps }}</h4>
          <div class="well">
            <ol>
              <li>Click the install button to set up the messaging system</li>
              <li>The system will:
                <ul>
                  <li>Create database tables: message, message_history, message_attachment</li>
                  <li>Create upload directory for attachments</li>
                  <li>Set up appropriate permissions</li>
                  <li>Add menu item for easy access</li>
                </ul>
              </li>
              <li>After installation, you'll be redirected to the messaging system</li>
            </ol>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
{{ footer }} 