<?php
/**
 * موديل تقرير الموازنة المتقدم
 * Advanced Budget Report Model
 * 
 * @package    AYM ERP
 * <AUTHOR> ERP Development Team
 * @copyright  2025 AYM ERP. All Rights Reserved.
 * @license    Proprietary - AYM ERP Commercial License
 * @version    1.0.0
 * @link       https://aym-erp.com
 * @since      File available since AYM ERP 1.0.0
 */

class ModelAccountsBudgetReport extends Model {
    
    /**
     * إنشاء تقرير الموازنة الشامل
     * Generate comprehensive budget report
     */
    public function generateBudgetReport($filter_data) {
        $sql = "SELECT 
                    b.budget_id,
                    b.budget_name,
                    b.budget_code,
                    b.budget_year,
                    b.start_date,
                    b.end_date,
                    b.total_amount as budgeted_amount,
                    b.status,
                    d.name as department_name,
                    cc.name as cost_center_name,
                    c.code as currency_code,
                    c.symbol_left,
                    c.symbol_right,
                    c.decimal_place,
                    COALESCE(SUM(bl.budgeted_amount), 0) as total_budgeted,
                    COALESCE(SUM(bl.actual_amount), 0) as total_actual,
                    (COALESCE(SUM(bl.actual_amount), 0) - COALESCE(SUM(bl.budgeted_amount), 0)) as variance,
                    CASE 
                        WHEN COALESCE(SUM(bl.budgeted_amount), 0) = 0 THEN 0
                        ELSE ((COALESCE(SUM(bl.actual_amount), 0) - COALESCE(SUM(bl.budgeted_amount), 0)) / COALESCE(SUM(bl.budgeted_amount), 0)) * 100
                    END as variance_percentage
                FROM " . DB_PREFIX . "budget b
                LEFT JOIN " . DB_PREFIX . "budget_line bl ON (b.budget_id = bl.budget_id)
                LEFT JOIN " . DB_PREFIX . "department d ON (b.department_id = d.department_id)
                LEFT JOIN " . DB_PREFIX . "cost_center cc ON (b.cost_center_id = cc.cost_center_id)
                LEFT JOIN " . DB_PREFIX . "currency c ON (b.currency_id = c.currency_id)
                WHERE 1=1";

        $params = array();

        // تصفية حسب الموازنة
        if (!empty($filter_data['budget_id'])) {
            $sql .= " AND b.budget_id = ?";
            $params[] = (int)$filter_data['budget_id'];
        }

        // تصفية حسب السنة المالية
        if (!empty($filter_data['budget_year'])) {
            $sql .= " AND b.budget_year = ?";
            $params[] = (int)$filter_data['budget_year'];
        }

        // تصفية حسب الإدارة
        if (!empty($filter_data['department_id'])) {
            $sql .= " AND b.department_id = ?";
            $params[] = (int)$filter_data['department_id'];
        }

        // تصفية حسب مركز التكلفة
        if (!empty($filter_data['cost_center_id'])) {
            $sql .= " AND b.cost_center_id = ?";
            $params[] = (int)$filter_data['cost_center_id'];
        }

        // تصفية حسب الفترة
        if (!empty($filter_data['date_start'])) {
            $sql .= " AND b.start_date >= ?";
            $params[] = $filter_data['date_start'];
        }

        if (!empty($filter_data['date_end'])) {
            $sql .= " AND b.end_date <= ?";
            $params[] = $filter_data['date_end'];
        }

        // تصفية حسب الحالة
        if (!empty($filter_data['status'])) {
            $sql .= " AND b.status = ?";
            $params[] = $filter_data['status'];
        }

        $sql .= " GROUP BY b.budget_id ORDER BY b.budget_year DESC, b.budget_name ASC";

        $query = $this->db->query($sql, $params);

        $budgets = array();
        foreach ($query->rows as $row) {
            $budgets[] = array(
                'budget_id' => $row['budget_id'],
                'budget_name' => $row['budget_name'],
                'budget_code' => $row['budget_code'],
                'budget_year' => $row['budget_year'],
                'start_date' => $row['start_date'],
                'end_date' => $row['end_date'],
                'department_name' => $row['department_name'],
                'cost_center_name' => $row['cost_center_name'],
                'currency_code' => $row['currency_code'],
                'budgeted_amount' => (float)$row['total_budgeted'],
                'actual_amount' => (float)$row['total_actual'],
                'variance' => (float)$row['variance'],
                'variance_percentage' => (float)$row['variance_percentage'],
                'budgeted_amount_formatted' => $this->currency->format($row['total_budgeted'], $row['currency_code']),
                'actual_amount_formatted' => $this->currency->format($row['total_actual'], $row['currency_code']),
                'variance_formatted' => $this->currency->format($row['variance'], $row['currency_code']),
                'status' => $row['status']
            );
        }

        // إضافة تفاصيل الحسابات لكل موازنة
        foreach ($budgets as &$budget) {
            $budget['lines'] = $this->getBudgetLines($budget['budget_id']);
        }

        return array(
            'budgets' => $budgets,
            'summary' => $this->getBudgetSummary($budgets),
            'filter_data' => $filter_data
        );
    }

    /**
     * الحصول على تفاصيل بنود الموازنة
     * Get budget line details
     */
    public function getBudgetLines($budget_id) {
        $sql = "SELECT 
                    bl.budget_line_id,
                    bl.account_id,
                    bl.budgeted_amount,
                    bl.actual_amount,
                    bl.variance,
                    bl.variance_percentage,
                    bl.notes,
                    a.account_code,
                    a.account_name,
                    at.name as account_type_name
                FROM " . DB_PREFIX . "budget_line bl
                LEFT JOIN " . DB_PREFIX . "account a ON (bl.account_id = a.account_id)
                LEFT JOIN " . DB_PREFIX . "account_type at ON (a.account_type_id = at.account_type_id)
                WHERE bl.budget_id = ?
                ORDER BY a.account_code ASC";

        $query = $this->db->query($sql, array((int)$budget_id));

        $lines = array();
        foreach ($query->rows as $row) {
            $lines[] = array(
                'budget_line_id' => $row['budget_line_id'],
                'account_id' => $row['account_id'],
                'account_code' => $row['account_code'],
                'account_name' => $row['account_name'],
                'account_type_name' => $row['account_type_name'],
                'budgeted_amount' => (float)$row['budgeted_amount'],
                'actual_amount' => (float)$row['actual_amount'],
                'variance' => (float)$row['variance'],
                'variance_percentage' => (float)$row['variance_percentage'],
                'notes' => $row['notes']
            );
        }

        return $lines;
    }

    /**
     * الحصول على ملخص الموازنة
     * Get budget summary
     */
    public function getBudgetSummary($budgets) {
        $total_budgeted = 0;
        $total_actual = 0;
        $total_variance = 0;
        $budget_count = count($budgets);

        foreach ($budgets as $budget) {
            $total_budgeted += $budget['budgeted_amount'];
            $total_actual += $budget['actual_amount'];
            $total_variance += $budget['variance'];
        }

        $variance_percentage = $total_budgeted > 0 ? (($total_variance / $total_budgeted) * 100) : 0;

        return array(
            'budget_count' => $budget_count,
            'total_budgeted' => $total_budgeted,
            'total_actual' => $total_actual,
            'total_variance' => $total_variance,
            'variance_percentage' => $variance_percentage,
            'total_budgeted_formatted' => $this->currency->format($total_budgeted),
            'total_actual_formatted' => $this->currency->format($total_actual),
            'total_variance_formatted' => $this->currency->format($total_variance)
        );
    }

    /**
     * تحليل انحرافات الموازنة
     * Budget variance analysis
     */
    public function getVarianceAnalysis($filter_data) {
        $sql = "SELECT 
                    a.account_code,
                    a.account_name,
                    at.name as account_type_name,
                    SUM(bl.budgeted_amount) as total_budgeted,
                    SUM(bl.actual_amount) as total_actual,
                    SUM(bl.variance) as total_variance,
                    AVG(bl.variance_percentage) as avg_variance_percentage
                FROM " . DB_PREFIX . "budget_line bl
                LEFT JOIN " . DB_PREFIX . "account a ON (bl.account_id = a.account_id)
                LEFT JOIN " . DB_PREFIX . "account_type at ON (a.account_type_id = at.account_type_id)
                LEFT JOIN " . DB_PREFIX . "budget b ON (bl.budget_id = b.budget_id)
                WHERE 1=1";

        $params = array();

        // تطبيق المرشحات
        if (!empty($filter_data['budget_year'])) {
            $sql .= " AND b.budget_year = ?";
            $params[] = (int)$filter_data['budget_year'];
        }

        if (!empty($filter_data['department_id'])) {
            $sql .= " AND b.department_id = ?";
            $params[] = (int)$filter_data['department_id'];
        }

        $sql .= " GROUP BY a.account_id 
                  HAVING ABS(SUM(bl.variance)) > 0
                  ORDER BY ABS(SUM(bl.variance)) DESC";

        $query = $this->db->query($sql, $params);

        $variances = array();
        foreach ($query->rows as $row) {
            $variances[] = array(
                'account_code' => $row['account_code'],
                'account_name' => $row['account_name'],
                'account_type_name' => $row['account_type_name'],
                'budgeted_amount' => (float)$row['total_budgeted'],
                'actual_amount' => (float)$row['total_actual'],
                'variance' => (float)$row['total_variance'],
                'variance_percentage' => (float)$row['avg_variance_percentage'],
                'budgeted_amount_formatted' => $this->currency->format($row['total_budgeted']),
                'actual_amount_formatted' => $this->currency->format($row['total_actual']),
                'variance_formatted' => $this->currency->format($row['total_variance'])
            );
        }

        return $variances;
    }

    /**
     * الحصول على قائمة الموازنات للتصفية
     * Get budgets list for filtering
     */
    public function getBudgets() {
        $sql = "SELECT budget_id, budget_name, budget_code, budget_year, status
                FROM " . DB_PREFIX . "budget
                ORDER BY budget_year DESC, budget_name ASC";

        $query = $this->db->query($sql);

        return $query->rows;
    }

    /**
     * الحصول على قائمة الإدارات
     * Get departments list
     */
    public function getDepartments() {
        $sql = "SELECT department_id, name
                FROM " . DB_PREFIX . "department
                WHERE status = 1
                ORDER BY name ASC";

        $query = $this->db->query($sql);

        return $query->rows;
    }

    /**
     * الحصول على قائمة مراكز التكلفة
     * Get cost centers list
     */
    public function getCostCenters() {
        $sql = "SELECT cost_center_id, name
                FROM " . DB_PREFIX . "cost_center
                WHERE status = 1
                ORDER BY name ASC";

        $query = $this->db->query($sql);

        return $query->rows;
    }

    /**
     * الحصول على السنوات المالية المتاحة
     * Get available financial years
     */
    public function getFinancialYears() {
        $sql = "SELECT DISTINCT budget_year
                FROM " . DB_PREFIX . "budget
                ORDER BY budget_year DESC";

        $query = $this->db->query($sql);

        $years = array();
        foreach ($query->rows as $row) {
            $years[] = $row['budget_year'];
        }

        return $years;
    }
}
