{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="submit" form="form-total" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fa fa-save"></i></button>
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a></div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pencil"></i> {{ text_edit }}</h3>
      </div>
      <div class="panel-body">
        <form action="{{ action }}" method="post" enctype="multipart/form-data" id="form-total" class="form-horizontal">
          <ul class="nav nav-tabs" id="country">
            {% for country in countries %}
            <li><a href="#tab-{{ country.code }}" data-toggle="tab">{{ country.name }}</a></li>
            {% endfor %}
          </ul>
          <div class="tab-content">
            {% for country in countries %}
            <div class="tab-pane" id="tab-{{ country.code }}">
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-total{{ country.code }}">{{ entry_total }}</label>
                <div class="col-sm-10">
                  <input type="text" name="total_klarna_fee[{{ country.code }}][total]" value="{{ total_klarna_fee[country.code] ? total_klarna_fee[country.code].total }}" placeholder="{{ entry_total }}" id="input-total{{ country.code }}" class="form-control" />
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-fee{{ country.code }}">{{ entry_fee }}</label>
                <div class="col-sm-10">
                  <input type="text" name="total_klarna_fee[{{ country.code }}][fee]" value="{{ total_klarna_fee[country.code] ? total_klarna_fee[country.code].fee }}" placeholder="{{ entry_fee }}" id="input-fee{{ country.code }}" class="form-control" />
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-tax-class{{ country.code }}">{{ entry_tax_class }}</label>
                <div class="col-sm-10">
                  <select name="total_klarna_fee[{{ country.code }}][tax_class_id]" id="input-tax-class{{ country.code }}" class="form-control">
                    <option value="0">{{ text_none }}</option>
                    {% for tax_class in tax_classes %}
                    {% if total_klarna_fee[country.code] and total_klarna_fee[country.code].tax_class_id == tax_class.tax_class_id %}
                    <option value="{{ tax_class.tax_class_id }}" selected="selected">{{ tax_class.title }}</option>
                    {% else %}
                    <option value="{{ tax_class.tax_class_id }}">{{ tax_class.title }}</option>
                    {% endif %}
                    {% endfor %}
                  </select>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-status{{ country.code }}">{{ entry_status }}</label>
                <div class="col-sm-10">
                  <select name="total_klarna_fee[{{ country.code }}][status]" id="input-status{{ country.code }}" class="form-control">
                    {% if total_klarna_fee[country.code] and total_klarna_fee[country.code].status %}
                    <option value="1" selected="selected">{{ text_enabled }}</option>
                    <option value="0">{{ text_disabled }}</option>
                    {% else %}
                    <option value="1">{{ text_enabled }}</option>
                    <option value="0" selected="selected">{{ text_disabled }}</option>
                    {% endif %}
                  </select>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-sort-order{{ country.code }}">{{ entry_sort_order }}</label>
                <div class="col-sm-10">
                  <input type="text" name="total_klarna_fee[{{ country.code }}][sort_order]" value="{{ total_klarna_fee[country.code] ? total_klarna_fee[country.code].sort_order }}" placeholder="{{ entry_sort_order }}" id="input-sort-order{{ country.code }}" class="form-control" />
                </div>
              </div>
            </div>
            {% endfor %}
          </div>
        </form>
      </div>
    </div>
  </div>
  <script type="text/javascript"><!--
$('#country a:first').tab('show');
//--></script></div>
{{ footer }}
