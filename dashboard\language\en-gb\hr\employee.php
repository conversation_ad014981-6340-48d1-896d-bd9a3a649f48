<?php
// Heading
$_['heading_title']                = 'Employee Profiles Management';

// Text
$_['text_filter']                  = 'Filter';
$_['text_enter_employee_name']     = 'Enter employee name';
$_['text_all_statuses']            = 'All Statuses';
$_['text_active']                  = 'Active';
$_['text_inactive']                = 'Inactive';
$_['text_terminated']              = 'Terminated';
$_['button_filter']                = 'Filter';
$_['button_reset']                 = 'Reset';
$_['button_add_employee']          = 'Add Employee';
$_['text_employee_list']           = 'Employee List';
$_['text_add_employee']            = 'Add Employee';
$_['text_edit_employee']           = 'Edit Employee';
$_['text_ajax_error']              = 'An error occurred while communicating with the server';
$_['text_confirm_delete']          = 'Are you sure you want to delete?';
$_['text_user_id']                 = 'User (Employee)';
$_['text_select_user']             = 'Select User';
$_['text_job_title']               = 'Job Title';
$_['text_hiring_date']             = 'Hiring Date';
$_['text_salary']                  = 'Salary';
$_['text_status']                  = 'Status';
$_['text_documents']               = 'Documents';
$_['button_add_document']          = 'Add Document';
$_['column_document_name']         = 'Document Name';
$_['column_document_description']  = 'Description';
$_['column_document_actions']      = 'Actions';
$_['text_add_document']            = 'Add Document';
$_['text_document_name']           = 'Document Name';
$_['text_document_description']    = 'Document Description';
$_['text_file']                    = 'File';
$_['text_save_employee_first']     = 'Please save the employee data first before adding documents.';
$_['text_employee_name']           = 'Employee Name';

// Columns
$_['column_employee_name']         = 'Employee Name';
$_['column_job_title']             = 'Job Title';
$_['column_status']                = 'Status';
$_['column_salary']                = 'Salary';
$_['column_hiring_date']           = 'Hiring Date';
$_['column_actions']               = 'Actions';

// Errors/Success
$_['error_not_found']              = 'Record not found!';
$_['error_invalid_request']        = 'Invalid request!';
$_['error_permission']             = 'Warning: You do not have permission to modify employees!';
$_['error_required']               = 'Warning: Please fill in the required fields!';
$_['error_upload']                 = 'An error occurred while uploading the file!';
$_['text_success_add']             = 'Employee added successfully!';
$_['text_success_edit']            = 'Employee data updated successfully!';
$_['text_success_delete']          = 'Employee deleted successfully!';
$_['text_success_document_add']    = 'Document added successfully!';
$_['text_success_document_delete'] = 'Document deleted successfully!';
