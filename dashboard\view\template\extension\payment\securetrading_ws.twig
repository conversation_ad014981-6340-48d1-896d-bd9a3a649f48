{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="submit" form="form-payment" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fa fa-save"></i></button>
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a></div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid"> {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pencil"></i> {{ heading_title }}</h3>
      </div>
      <div class="panel-body">
        <form action="{{ action }}" method="post" enctype="multipart/form-data" id="form-payment" class="form-horizontal">
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="securetrading_ws_site_reference">{{ entry_site_reference }}</label>
            <div class="col-sm-10">
              <input type="text" name="payment_securetrading_ws_site_reference" value="{{ payment_securetrading_ws_site_reference }}" placeholder="{{ entry_site_reference }}" id="securetrading_ws_site_reference" class="form-control" />
              {% if error_site_reference %}
              <div class="text-danger">{{ error_site_reference }}</div>
              {% endif %} </div>
          </div>
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="securetrading_ws_username">{{ entry_username }}</label>
            <div class="col-sm-10">
              <input type="text" name="payment_securetrading_ws_username" value="{{ payment_securetrading_ws_username }}" placeholder="{{ entry_username }}" id="securetrading_ws_username" class="form-control" />
              {% if error_username %}
              <div class="text-danger">{{ error_username }}</div>
              {% endif %} </div>
          </div>
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="securetrading_ws_password">{{ entry_password }}</label>
            <div class="col-sm-10">
              <input type="text" name="payment_securetrading_ws_password" value="{{ payment_securetrading_ws_password }}" placeholder="{{ entry_password }}" id="securetrading_ws_password" class="form-control" />
              {% if error_password %}
              <div class="text-danger">{{ error_password }}</div>
              {% endif %} </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="securetrading_ws_csv_username">{{ entry_csv_username }}</label>
            <div class="col-sm-10">
              <input type="text" name="payment_securetrading_ws_csv_username" value="{{ payment_securetrading_ws_csv_username }}" placeholder="{{ entry_csv_username }}" id="securetrading_ws_csv_username" class="form-control" />
              <span class="help-block">{{ help_csv_username }}</span> </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="securetrading_ws_csv_password">{{ entry_csv_password }}</label>
            <div class="col-sm-10">
              <input type="text" name="payment_securetrading_ws_csv_password" value="{{ payment_securetrading_ws_csv_password }}" placeholder="{{ entry_csv_password }}" id="securetrading_ws_csv_password" class="form-control" />
              <span class="help-block">{{ help_csv_password }}</span> </div>
          </div>
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="securetrading_ws_cards_accepted">{{ entry_cards_accepted }}</label>
            <div class="col-sm-10"> {% for key, value in cards %}
              <div class="checkbox">
                <label> {% if key in payment_securetrading_ws_cards_accepted %}
                  <input type="checkbox" checked="checked" name="payment_securetrading_ws_cards_accepted[]" value="{{ key }}" />
                  {% else %}
                  <input type="checkbox" name="payment_securetrading_ws_cards_accepted[]" value="{{ key }}" />
                  {% endif %}
                  {{ value }} </label>
              </div>
              {% endfor %}
              {% if error_cards_accepted %}
              <div class="text-danger">{{ error_cards_accepted }}</div>
              {% endif %} </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="securetrading_ws_settle_status">{{ entry_settle_status }}</label>
            <div class="col-sm-10">
              <select name="payment_securetrading_ws_settle_status" id="payment_securetrading_ws_settle_status" class="form-control">
                
								{% for key, value in settlement_statuses %}
									{% if key == payment_securetrading_ws_settle_status %}
										
                <option value="{{ key }}" selected="selected">{{ value }}</option>
                
									{% else %}
										
                <option value="{{ key }}">{{ value }}</option>
                
									{% endif %}
								{% endfor %}
							
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="securetrading_ws_settle_due_date">{{ entry_settle_due_date }}</label>
            <div class="col-sm-10">
              <select name="payment_securetrading_ws_settle_due_date" id="securetrading_ws_settle_due_date" class="form-control">
                {%if payment_securetrading_ws_settle_due_date == 0 %}
                <option value="0" selected="selected">{{ text_process_immediately }}</option>
                
								{% else %}
									
                <option value="0">{{ text_process_immediately }}</option>
                
								{% endif %}
								
                {% for i in 0..7 %}
                
									{% if i == payment_securetrading_ws_settle_due_date %}
										
                <option value="{{ i }}" selected="selected">{{ text_wait_x_days|format(i) }}</option>
                
									{% else %}
										
                <option value="{{ i }}">{{ text_wait_x_days|format(i) }}</option>
                
									{% endif %}
								{% endfor %}
							
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="securetrading_ws_total">{{ entry_total }}</label>
            <div class="col-sm-10">
              <input type="text" name="payment_securetrading_ws_total" value="{{ payment_securetrading_ws_total }}" placeholder="{{ entry_total }}" id="securetrading_ws_total" class="form-control" />
              <span class="help-block">{{ help_total }}</span> </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="securetrading_ws_order_status_id">{{ entry_order_status }}</label>
            <div class="col-sm-10">
              <select name="payment_securetrading_ws_order_status_id" id="securetrading_ws_order_status_id" class="form-control">
                
								{% for order_status in order_statuses %}
									{% if order_status.order_status_id == payment_securetrading_ws_order_status_id %}
										
                <option value="{{ order_status.order_status_id }}" selected="selected">{{ order_status.name }}</option>
                
									{% else %}
										
                <option value="{{ order_status.order_status_id }}">{{ order_status.name }}</option>
                
									{% endif %}
								{% endfor %}
							
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="securetrading_ws_failed_order_status_id">{{ entry_failed_order_status }}</label>
            <div class="col-sm-10">
              <select name="payment_securetrading_ws_failed_order_status_id" id="securetrading_ws_failed_order_status_id" class="form-control">
                
								{% for order_status in order_statuses %}
									{% if order_status.order_status_id == payment_securetrading_ws_failed_order_status_id %}
										
                <option value="{{ order_status.order_status_id }}" selected="selected">{{ order_status.name }}</option>
                
									{% else %}
										
                <option value="{{ order_status.order_status_id }}">{{ order_status.name }}</option>
                
									{% endif %}
								{% endfor %}
							
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="securetrading_ws_declined_order_status_id">{{ entry_declined_order_status }}</label>
            <div class="col-sm-10">
              <select name="payment_securetrading_ws_declined_order_status_id" id="securetrading_ws_declined_order_status_id" class="form-control">
                
								{% for order_status in order_statuses %}
									{% if order_status.order_status_id == payment_securetrading_ws_declined_order_status_id %}
										
                <option value="{{ order_status.order_status_id }}" selected="selected">{{ order_status.name }}</option>
                
									{% else %}
										
                <option value="{{ order_status.order_status_id }}">{{ order_status.name }}</option>
                
									{% endif %}
								{% endfor %}
							
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="securetrading_ws_refunded_order_status_id">{{ entry_refunded_order_status }}</label>
            <div class="col-sm-10">
              <select name="payment_securetrading_ws_refunded_order_status_id" id="securetrading_ws_refunded_order_status_id" class="form-control">
                
								{% for order_status in order_statuses %}
									{% if order_status.order_status_id == payment_securetrading_ws_refunded_order_status_id %}
										
                <option value="{{ order_status.order_status_id }}" selected="selected">{{ order_status.name }}</option>
                
									{% else %}
										
                <option value="{{ order_status.order_status_id }}">{{ order_status.name }}</option>
                
									{% endif %}
								{% endfor %}
							
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="securetrading_ws_authorisation_reversed_order_status_id">{{ entry_authorisation_reversed_order_status }}</label>
            <div class="col-sm-10">
              <select name="payment_securetrading_ws_authorisation_reversed_order_status_id" id="securetrading_ws_authorisation_reversed_order_status_id" class="form-control">
                
								{% for order_status in order_statuses %}
									{% if order_status.order_status_id == payment_securetrading_ws_authorisation_reversed_order_status_id %}
										
                <option value="{{ order_status.order_status_id }}" selected="selected">{{ order_status.name }}</option>
                
									{% else %}
										
                <option value="{{ order_status.order_status_id }}">{{ order_status.name }}</option>
                
									{% endif %}
								{% endfor %}
							
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="securetrading_ws_geo_zone_id">{{ entry_geo_zone }}</label>
            <div class="col-sm-10">
              <select name="payment_securetrading_ws_geo_zone_id" id="securetrading_ws_geo_zone_id" class="form-control">
                {% if payment_securetrading_ws_geo_zone_id == 0 %}
                <option value="0" selected="selected">{{ text_all_geo_zones }}</option>
                
								{% else %}
									
                <option value="0">{{ text_all_geo_zones }}</option>
                
								{% endif %}
								{% for geo_zone in geo_zones %}
									
                {% if payment_securetrading_ws_geo_zone_id == geo_zone.geo_zone_id %}
                <option value="{{ geo_zone.geo_zone_id }}" selected="selected">{{ geo_zone.name }}</option>
                
									{% else %}
										
                <option value="{{ geo_zone.geo_zone_id }}">{{ geo_zone.name }}</option>
                
									{% endif %}
								{% endfor %}
							
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="securetrading_ws_status">{{ entry_status }}</label>
            <div class="col-sm-10">
              <select name="payment_securetrading_ws_status" id="securetrading_ws_status" class="form-control">
                {% if payment_securetrading_ws_status == 1 %}
                <option value="1" selected="selected">{{ text_enabled }}</option>
                
								{% else %}
									
                <option value="1">{{ text_enabled }}</option>
                
								{% endif %}
								
                {% if payment_securetrading_ws_status == 0 %}
                <option value="0" selected="selected">{{ text_disabled }}</option>
                
								{% else %}
									
                <option value="0">{{ text_disabled }}</option>
                
								{% endif %}
							
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="securetrading_ws_sort_order">{{ entry_sort_order }}</label>
            <div class="col-sm-10">
              <input type="text" name="payment_securetrading_ws_sort_order" value="{{ payment_securetrading_ws_sort_order }}" placeholder="{{ entry_sort_order }}" id="securetrading_ws_sort_order" class="form-control" />
            </div>
          </div>
        </form>
      </div>
      {% if myst_status %}
      <div class="tab-pane" id="tab-myst">
        <div class="well">
          <form id="transaction-form">
            <div class="row">
              <div class="col-sm-1">
                <div class="form-group">
                  <label class="control-label" for="hour-from">{{ entry_hour }}</label>
                  <div class="input-group">
                    <select name="hour_from" id="hour-from" class="form-control">
                      
												{% for hour in hours %}
													
                      <option value="{{ hour }}">{{ hour }}</option>
                      
												{% endfor %}
											
                    </select>
                  </div>
                </div>
              </div>
              <div class="col-sm-1">
                <div class="form-group">
                  <label class="control-label" for="minute-from">{{ entry_minute }}</label>
                  <div class="input-group">
                    <select name="minute_from" id="minute-from" class="form-control">
                      
												{% for minute in minutes %}
													
                      <option value="{{ minute }}">{{ minute }}</option>
                      
												{% endfor %}
											
                    </select>
                  </div>
                </div>
              </div>
              <div class="col-sm-3">
                <div class="form-group">
                  <label class="control-label" for="date-from">{{ entry_date_from }}</label>
                  <div class="input-group date">
                    <input type="text" name="date_from" value="<?php echo date('Y-m-d'); ?>" placeholder="{{ entry_date_from }}" data-date-format="YYYY-MM-DD" id="date-from" class="form-control" />
                    <span class="input-group-btn">
                    <button type="button" class="btn btn-default"><i class="fa fa-calendar"></i></button>
                    </span> </div>
                </div>
              </div>
              <div class="col-sm-1 col-sm-offset-2">
                <div class="form-group">
                  <label class="control-label" for="hour-to">{{ entry_hour }}</label>
                  <div class="input-group">
                    <select name="hour_to" id="hour-to" class="form-control">
                      
												{% for hour in hours %}
													
                      <option value="{{ hour }}">{{ hour }}</option>
                      
												{% endfor %}
											
                    </select>
                  </div>
                </div>
              </div>
              <div class="col-sm-1">
                <div class="form-group">
                  <label class="control-label" for="minute-to">{{ entry_minute }}</label>
                  <div class="input-group">
                    <select name="minute_to" id="minute-to" class="form-control">
                      
												{% for minute in minutes %}
													
                      <option value="{{ minute }}">{{ minute }}</option>
                      
												{% endfor %}
											
                    </select>
                  </div>
                </div>
              </div>
              <div class="col-sm-3">
                <div class="form-group">
                  <label class="control-label" for="date-to">{{ entry_date_to }}</label>
                  <div class="input-group date">
                    <input type="text" name="date_to" value="<?php echo date('Y-m-d'); ?>" placeholder="{{ entry_date_to }}" data-date-format="YYYY-MM-DD" id="date-to" class="form-control" />
                    <span class="input-group-btn">
                    <button type="button" class="btn btn-default"><i class="fa fa-calendar"></i></button>
                    </span> </div>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-sm-4">
                <div class="form-group">
                  <label class="control-label" for="request">{{ entry_request }}</label>
                  <select name="request[]" id="request" multiple class="form-control" style="height: 150px">
                    <option selected="selected">ACCOUNTCHECK</option>
                    <option selected="selected">AUTH</option>
                    <option selected="selected">FRAUDSCORE</option>
                    <option selected="selected">ORDER</option>
                    <option selected="selected">ORDERDETAILS</option>
                    <option selected="selected">REFUND</option>
                    <option selected="selected">THREEDQUERY</option>
                  </select>
                </div>
              </div>
              <div class="col-sm-4">
                <div class="form-group">
                  <label class="control-label" for="currency">{{ entry_currency }}</label>
                  <select name="currency[]" id="currency" multiple class="form-control" style="height: 150px">
                    
											{% for currency in currencies %}
												
                    <option selected="selected" value="{{ currency.code }}">{{ currency.title }}</option>
                    
											{% endfor %}
										
                  </select>
                </div>
              </div>
              <div class="col-sm-4">
                <div class="form-group">
                  <label class="control-label" for="payment-type">{{ entry_payment_type }}</label>
                  <select name="payment_type[]" id="payment-type" multiple class="form-control" style="height: 150px">
                    <option selected="selected">AMEX</option>
                    <option selected="selected">DELTA</option>
                    <option selected="selected">ELECTRON</option>
                    <option selected="selected">MAESTRO</option>
                    <option selected="selected">MASTERCARD</option>
                    <option selected="selected">MASTERCARDDEBIT</option>
                    <option selected="selected">PAYPAL</option>
                    <option selected="selected">PURCHASING</option>
                    <option selected="selected">VISA</option>
                    <option selected="selected">VPAY</option>
                  </select>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-sm-4">
                <div class="form-group">
                  <label class="control-label" for="status">{{ entry_status_code }}</label>
                  <select name="status[]" id="status" multiple class="form-control" style="height: 150px">
                    <option selected="selected" value="0">0 - Ok</option>
                    <option selected="selected" value="70000">70000 - Decline</option>
                  </select>
                </div>
              </div>
              <div class="col-sm-4">
                <div class="form-group">
                  <label class="control-label" for="settle-status">{{ entry_settle_status }}</label>
                  <select name="settle_status[]" id="settle-status" multiple class="form-control" style="height: 150px">
                    <option selected="selected" value="0">0 - {{ text_pending_settlement }}</option>
                    <option selected="selected" value="1">1 - {{ text_manual_settlement }}</option>
                    <option selected="selected" value="2">2 - {{ text_suspended }}</option>
                    <option selected="selected" value="3">3 - {{ text_cancelled }}</option>
                    <option selected="selected" value="10">10 - {{ text_settling }}</option>
                    <option selected="selected" value="100">100 - {{ text_settled }}</option>
                  </select>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-sm-12"> <a class="btn btn-primary" onclick="showTransactions()">{{ button_show }}</a> <a class="btn btn-primary" onclick="downloadTransactions()">{{ button_download }}</a> </div>
            </div>
          </form>
        </div>
      </div>
      {% endif %}
    </div>
  </div>
</div>
<script type="text/javascript">
	function downloadTransactions() {
		$('#download-iframe').remove();
		$('#transaction-form').after('<iframe name="download-iframe" id="download-iframe" style="display: none;" src="" />');

		$('#transaction-form').attr('method', 'POST');
		$('#transaction-form').attr('target', 'download-iframe');
		$('#transaction-form').attr('action', 'index.php?route=extension/payment/securetrading_ws/downloadTransactions&user_token={{ user_token }}');

		$('#transaction-form').submit();

		$('#transaction-form').removeAttr('method');
		$('#transaction-form').removeAttr('target');
		$('#transaction-form').removeAttr('action');
	}

	function showTransactions() {
		$.ajax({
			url: 'index.php?route=extension/payment/securetrading_ws/showTransactions&user_token={{ user_token }}',
			type: 'post',
			data: $('#transaction-form').serialize(),
			dataType: 'html',
			beforeSend: function() {
				$('.transactions').remove();
			},
			success: function(html) {
				$('.well').after(html);
			}
		});
	}

	$('.date').datetimepicker({
		language: '{{ datepicker }}',
		pickTime: false
	});
</script> 
{{ footer }} 
