{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="submit" form="governance\meetings-form" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fas fa-save"></i></button>
        <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-light"><i class="fas fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    
    <div class="card">
      <div class="card-header"><i class="fas fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}chart-bar{% elif screen_type == 'settings' %}cog{% else %}pencil-alt{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="card-body">
        <form id="governance\meetings-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-can_add">{{ text_can_add }}</label>
            <div class="col-sm-10">
              <input type="text" name="can_add" value="{{ can_add }}" placeholder="{{ text_can_add }}" id="input-can_add" class="form-control" />
              {% if error_can_add %}
                <div class="invalid-feedback">{{ error_can_add }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-can_add_attendee">{{ text_can_add_attendee }}</label>
            <div class="col-sm-10">
              <input type="text" name="can_add_attendee" value="{{ can_add_attendee }}" placeholder="{{ text_can_add_attendee }}" id="input-can_add_attendee" class="form-control" />
              {% if error_can_add_attendee %}
                <div class="invalid-feedback">{{ error_can_add_attendee }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-can_delete">{{ text_can_delete }}</label>
            <div class="col-sm-10">
              <input type="text" name="can_delete" value="{{ can_delete }}" placeholder="{{ text_can_delete }}" id="input-can_delete" class="form-control" />
              {% if error_can_delete %}
                <div class="invalid-feedback">{{ error_can_delete }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-can_delete_attendee">{{ text_can_delete_attendee }}</label>
            <div class="col-sm-10">
              <input type="text" name="can_delete_attendee" value="{{ can_delete_attendee }}" placeholder="{{ text_can_delete_attendee }}" id="input-can_delete_attendee" class="form-control" />
              {% if error_can_delete_attendee %}
                <div class="invalid-feedback">{{ error_can_delete_attendee }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-can_edit">{{ text_can_edit }}</label>
            <div class="col-sm-10">
              <input type="text" name="can_edit" value="{{ can_edit }}" placeholder="{{ text_can_edit }}" id="input-can_edit" class="form-control" />
              {% if error_can_edit %}
                <div class="invalid-feedback">{{ error_can_edit }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_type">{{ text_filter_type }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_type" value="{{ filter_type }}" placeholder="{{ text_filter_type }}" id="input-filter_type" class="form-control" />
              {% if error_filter_type %}
                <div class="invalid-feedback">{{ error_filter_type }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="invalid-feedback">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_list">{{ text_text_list }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_list" value="{{ text_list }}" placeholder="{{ text_text_list }}" id="input-text_list" class="form-control" />
              {% if error_text_list %}
                <div class="invalid-feedback">{{ error_text_list }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="invalid-feedback">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}