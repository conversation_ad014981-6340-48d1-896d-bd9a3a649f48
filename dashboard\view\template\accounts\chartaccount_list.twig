{{ header }}{{ column_left }}

<!-- CSS مخصص لدليل الحسابات - Enterprise Grade Enhanced -->
<style>
/* تحسينات متقدمة للعرض الشجري */
.account-tree {
    background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid #e3e6f0;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 25px;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    transition: all 0.3s ease;
}

.account-tree:hover {
    box-shadow: 0 0.25rem 2rem 0 rgba(58, 59, 69, 0.2);
    transform: translateY(-2px);
}

.account-item {
    padding: 8px 12px;
    border-bottom: 1px solid #f0f0f0;
    transition: all 0.3s ease;
    position: relative;
}

.account-item:hover {
    background-color: #f8f9fa;
    transform: translateX(3px);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.account-item:hover .account-code {
    color: #c0392b;
    transform: scale(1.05);
}

.account-item:hover .account-balance {
    font-size: 1.1em;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.account-level-0 { padding-right: 0px; font-weight: bold; color: #2c3e50; }
.account-level-1 { padding-right: 20px; font-weight: 600; color: #34495e; }
.account-level-2 { padding-right: 40px; color: #5a6c7d; }
.account-level-3 { padding-right: 60px; color: #7f8c8d; }
.account-level-4 { padding-right: 80px; color: #95a5a6; }

.account-code {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    color: #e74c3c;
    margin-left: 10px;
}

.account-balance {
    font-weight: bold;
    color: #27ae60;
}

.account-balance.negative {
    color: #e74c3c;
}

.account-type-badge {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
}

.account-type-asset { background-color: #3498db; color: white; }
.account-type-liability { background-color: #e74c3c; color: white; }
.account-type-equity { background-color: #9b59b6; color: white; }
.account-type-revenue { background-color: #27ae60; color: white; }
.account-type-expense { background-color: #f39c12; color: white; }

.toolbar {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 20px;
    border: 1px solid #dee2e6;
}

.view-toggle {
    margin-left: 10px;
}

.export-dropdown {
    margin-right: 10px;
}

.search-box {
    max-width: 300px;
    margin-right: 15px;
}

.stats-cards {
    margin-bottom: 20px;
}

.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 12px;
    text-align: center;
    margin-bottom: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.stat-card:hover::before {
    left: 100%;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.stat-card h3 {
    margin: 0;
    font-size: 2.2em;
    font-weight: bold;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.stat-card p {
    margin: 8px 0 0 0;
    opacity: 0.9;
    font-size: 0.95em;
    font-weight: 500;
}

/* تأثيرات البحث والتحميل المتقدمة */
.search-highlight {
    background-color: #fff3cd !important;
    border-left: 4px solid #ffc107;
    animation: highlightPulse 1s ease-in-out;
}

@keyframes highlightPulse {
    0% { background-color: #fff3cd; }
    50% { background-color: #ffeaa7; }
    100% { background-color: #fff3cd; }
}

.loading {
    cursor: wait;
}

.loading::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.1);
    z-index: 9999;
    pointer-events: none;
}

/* تحسينات إضافية للأزرار */
.btn-group .btn {
    transition: all 0.2s ease;
}

.btn-group .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* تحسين شريط الأدوات */
.toolbar {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

/* تحسين مربع البحث */
.search-box .form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
    transform: scale(1.02);
}

/* تحسين الفلاتر */
.form-select:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

/* تحسين البطاقات */
.card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0,0,0,0.075);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0,0,0,0.15);
}

.card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
}

/* مؤشر التحميل المتقدم */
.loading-indicator {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
}

.loading-spinner {
    background: white;
    padding: 30px;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
}

.loading-spinner i {
    color: #007bff;
    margin-bottom: 15px;
}

.loading-spinner p {
    margin: 0;
    color: #333;
    font-weight: 500;
}

/* تحسينات إضافية للجدول */
.table thead th {
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
}

.table-striped > tbody > tr:nth-of-type(odd) > td {
    background-color: rgba(0,0,0,0.02);
}

/* تحسين الأيقونات */
.fa {
    transition: all 0.2s ease;
}

.btn:hover .fa {
    transform: scale(1.1);
}

/* تحسين التنبيهات */
.alert {
    border: none;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    border-left: 4px solid #28a745;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    border-left: 4px solid #dc3545;
}

@media print {
    .toolbar, .pagination, .btn, .checkbox {
        display: none !important;
    }
    
    .account-item {
        page-break-inside: avoid;
    }
}
</style>

<div id="content">
    <div class="page-header">
        <div class="container-fluid">
            <div class="float-end">
                <div class="btn-group">
                    <a href="{{ add }}" data-toggle="tooltip" title="{{ button_add }}" class="btn btn-primary">
                        <i class="fa fa-plus"></i> {{ button_add }}
                    </a>
                    <button type="button" data-toggle="dropdown" class="btn btn-primary dropdown-toggle dropdown-toggle-split">
                        <span class="sr-only">Toggle Dropdown</span>
                    </button>
                    <div class="dropdown-menu dropdown-menu-end">
                        <a class="dropdown-item" href="{{ tree_view }}">
                            <i class="fa fa-sitemap"></i> {{ text_tree_view }}
                        </a>
                        <a class="dropdown-item" href="{{ import }}">
                            <i class="fa fa-upload"></i> {{ text_import }}
                        </a>
                        <div class="dropdown-divider"></div>
                        <a class="dropdown-item" href="{{ print }}">
                            <i class="fa fa-print"></i> {{ text_print }}
                        </a>
                        <div class="dropdown-submenu">
                            <a class="dropdown-item dropdown-toggle" href="#">
                                <i class="fa fa-download"></i> {{ text_export }}
                            </a>
                            <div class="dropdown-menu">
                                <a class="dropdown-item" href="{{ export }}&format=excel">
                                    <i class="fa fa-file-excel-o"></i> Excel
                                </a>
                                <a class="dropdown-item" href="{{ export }}&format=pdf">
                                    <i class="fa fa-file-pdf-o"></i> PDF
                                </a>
                                <a class="dropdown-item" href="{{ export }}&format=csv">
                                    <i class="fa fa-file-text-o"></i> CSV
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <button type="button" data-toggle="tooltip" title="{{ button_archive }}" class="btn btn-warning" onclick="archiveSelected()">
                    <i class="fa fa-archive"></i>
                </button>
                <button type="button" data-toggle="tooltip" title="{{ button_delete }}" class="btn btn-danger" onclick="confirm('{{ text_confirm }}') ? $('#form-account').submit() : false;">
                    <i class="fa fa-trash-o"></i>
                </button>
                <a href="{{ backup }}" data-toggle="tooltip" title="{{ text_backup_management }}" class="btn btn-info">
                    <i class="fa fa-history"></i>
                </a>
            </div>
            <h1>{{ heading_title }}</h1>
            <ul class="breadcrumb">
                {% for breadcrumb in breadcrumbs %}
                <li class="breadcrumb-item">
                    <a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a>
                </li>
                {% endfor %}
            </ul>
        </div>
    </div>

    <div class="container-fluid">
        <!-- إحصائيات سريعة -->
        <div class="row stats-cards">
            <div class="col-lg-2 col-md-4 col-sm-6">
                <div class="stat-card" style="background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);">
                    <h3>{{ total_assets }}</h3>
                    <p>{{ text_total_assets }}</p>
                </div>
            </div>
            <div class="col-lg-2 col-md-4 col-sm-6">
                <div class="stat-card" style="background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);">
                    <h3>{{ total_liabilities }}</h3>
                    <p>{{ text_total_liabilities }}</p>
                </div>
            </div>
            <div class="col-lg-2 col-md-4 col-sm-6">
                <div class="stat-card" style="background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);">
                    <h3>{{ total_equity }}</h3>
                    <p>{{ text_total_equity }}</p>
                </div>
            </div>
            <div class="col-lg-2 col-md-4 col-sm-6">
                <div class="stat-card" style="background: linear-gradient(135deg, #27ae60 0%, #229954 100%);">
                    <h3>{{ total_revenue }}</h3>
                    <p>{{ text_total_revenue }}</p>
                </div>
            </div>
            <div class="col-lg-2 col-md-4 col-sm-6">
                <div class="stat-card" style="background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);">
                    <h3>{{ total_expenses }}</h3>
                    <p>{{ text_total_expenses }}</p>
                </div>
            </div>
            <div class="col-lg-2 col-md-4 col-sm-6">
                <div class="stat-card" style="background: linear-gradient(135deg, #1abc9c 0%, #16a085 100%);">
                    <h3>{{ total_accounts }}</h3>
                    <p>{{ text_total_accounts }}</p>
                </div>
            </div>
        </div>

        <!-- شريط الأدوات -->
        <div class="toolbar">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="input-group search-box">
                        <input type="text" class="form-control" placeholder="{{ text_search_accounts }}" id="account-search">
                        <div class="input-group-btn">
                            <button class="btn btn-default" type="button" id="search-btn">
                                <i class="fa fa-search"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 text-right">
                    <div class="btn-group" data-toggle="buttons">
                        <label class="btn btn-primary active">
                            <input type="radio" name="view-type" id="list-view" value="list" checked> <i class="fa fa-list"></i> {{ text_list_view }}
                        </label>
                        <label class="btn btn-primary">
                            <input type="radio" name="view-type" id="tree-view" value="tree"> <i class="fa fa-sitemap"></i> {{ text_tree_view }}
                        </label>
                        <label class="btn btn-primary">
                            <input type="radio" name="view-type" id="card-view" value="card"> <i class="fa fa-th-large"></i> {{ text_card_view }}
                        </label>
                    </div>

                    <div class="btn-group" style="margin-left: 10px;">
                        <select class="form-control" id="account-type-filter">
                            <option value="">{{ text_all_types }}</option>
                            <option value="asset">{{ text_assets }}</option>
                            <option value="liability">{{ text_liabilities }}</option>
                            <option value="equity">{{ text_equity }}</option>
                            <option value="revenue">{{ text_revenue }}</option>
                            <option value="expense">{{ text_expenses }}</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        {% if error_warning %}
        <div class="alert alert-danger alert-dismissible">
            <i class="fa fa-exclamation-circle"></i> {{ error_warning }}
            <button type="button" class="close" data-dismiss="alert">&times;</button>
        </div>
        {% endif %}

        {% if success %}
        <div class="alert alert-success alert-dismissible">
            <i class="fa fa-check-circle"></i> {{ success }}
            <button type="button" class="close" data-dismiss="alert">&times;</button>
        </div>
        {% endif %}

        <!-- جدول الحسابات -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fa fa-list"></i> {{ text_list }}
                </h3>
                <div class="card-tools">
                    <button type="button" class="btn btn-default btn-sm" data-toggle="collapse" data-target="#accounts-table">
                        <i class="fa fa-minus"></i>
                    </button>
                </div>
            </div>
            <div class="panel-body collapse in" id="accounts-table">
                <form action="{{ delete }}" method="post" enctype="multipart/form-data" id="form-account">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover table-striped">
                            <thead class="table-dark" style="background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);">
                                <tr>
                                    <td style="width: 1px;" class="text-center">
                                        <input type="checkbox" onclick="$('input[name*=\'selected\']').prop('checked', this.checked);" />
                                    </td>
                                    <td class="text-center">
                                        <a href="{{ sort_account_code }}" class="text-white text-decoration-none">
                                            {{ column_account_code }}
                                            {% if sort == 'account_code' %}
                                                <i class="fa fa-sort-{{ order|lower == 'asc' ? 'up' : 'down' }}"></i>
                                            {% endif %}
                                        </a>
                                    </td>
                                    <td>
                                        <a href="{{ sort_name }}" class="text-white text-decoration-none">
                                            {{ column_account_name }}
                                            {% if sort == 'name' %}
                                                <i class="fa fa-sort-{{ order|lower == 'asc' ? 'up' : 'down' }}"></i>
                                            {% endif %}
                                        </a>
                                    </td>
                                    <td class="text-center">{{ column_account_type }}</td>
                                    <td class="text-center">{{ column_current_balance }}</td>
                                    <td class="text-center">{{ column_status }}</td>
                                    <td class="text-center">{{ column_action }}</td>
                                </tr>
                            </thead>
                            <tbody>
                                {% for account in accounts %}
                                <tr class="account-item account-level-{{ account.level|default(0) }}" data-account-type="{{ account.account_type }}">
                                    <td class="text-center">
                                        {% if account.selected %}
                                        <input type="checkbox" name="selected[]" value="{{ account.account_id }}" checked="checked" />
                                        {% else %}
                                        <input type="checkbox" name="selected[]" value="{{ account.account_id }}" />
                                        {% endif %}
                                    </td>
                                    <td class="text-center">
                                        <span class="account-code">{{ account.account_code }}</span>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            {% if account.is_parent %}
                                                <i class="fa fa-folder text-warning me-2"></i>
                                            {% else %}
                                                <i class="fa fa-file-text-o text-info me-2"></i>
                                            {% endif %}
                                            <span>{{ account.name }}</span>
                                            {% if account.description %}
                                                <small class="text-muted ms-2">({{ account.description }})</small>
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td class="text-center">
                                        <span class="account-type-badge account-type-{{ account.account_type }}">
                                            {{ account.account_type_text }}
                                        </span>
                                    </td>
                                    <td class="text-end">
                                        <span class="account-balance {{ account.current_balance < 0 ? 'negative' : '' }}">
                                            {{ account.current_balance_formatted }}
                                        </span>
                                    </td>
                                    <td class="text-center">
                                        {% if account.is_active %}
                                            <span class="badge bg-success">{{ text_enabled }}</span>
                                        {% else %}
                                            <span class="badge bg-danger">{{ text_disabled }}</span>
                                        {% endif %}
                                    </td>
                                    <td class="text-center">
                                        <div class="btn-group">
                                            <a href="{{ account.edit }}" data-bs-toggle="tooltip" title="{{ button_edit }}" class="btn btn-primary btn-sm">
                                                <i class="fa fa-pencil"></i>
                                            </a>
                                            <a href="{{ account.statement }}" data-bs-toggle="tooltip" title="{{ button_statement }}" class="btn btn-info btn-sm">
                                                <i class="fa fa-file-text"></i>
                                            </a>
                                            <button type="button" class="btn btn-danger btn-sm" onclick="deleteAccount({{ account.account_id }})" data-bs-toggle="tooltip" title="{{ button_delete }}">
                                                <i class="fa fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td class="text-center" colspan="7">{{ text_no_results }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </form>
                
                <div class="row">
                    <div class="col-sm-6 text-start">{{ pagination }}</div>
                    <div class="col-sm-6 text-end">{{ results }}</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript متقدم -->
<script>
$(document).ready(function() {
    // البحث المباشر
    $('#account-search').on('keyup', function() {
        var value = $(this).val().toLowerCase();
        $('.account-item').filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
        });
    });

    // فلترة حسب نوع الحساب
    $('#account-type-filter').on('change', function() {
        var selectedType = $(this).val();
        if (selectedType === '') {
            $('.account-item').show();
        } else {
            $('.account-item').hide();
            $('.account-item[data-account-type="' + selectedType + '"]').show();
        }
    });

    // تبديل طرق العرض
    $('input[name="view-type"]').on('change', function() {
        var viewType = $(this).val();
        switch(viewType) {
            case 'tree':
                window.location.href = '{{ tree_view }}';
                break;
            case 'card':
                toggleCardView();
                break;
            default:
                toggleListView();
        }
    });

    // تفعيل التلميحات
    $('[data-bs-toggle="tooltip"]').tooltip();

    // تحديث الأرصدة كل 30 ثانية مع مؤشر تحميل
    setInterval(function() {
        updateAccountBalances();
    }, 30000);

    // إضافة مؤشر تحميل للعمليات
    $(document).ajaxStart(function() {
        $('body').addClass('loading');
    }).ajaxStop(function() {
        $('body').removeClass('loading');
    });

    // تحسين البحث مع debounce
    let searchTimeout;
    $('#account-search').off('keyup').on('keyup', function() {
        clearTimeout(searchTimeout);
        const value = $(this).val().toLowerCase();
        
        searchTimeout = setTimeout(function() {
            $('.account-item').each(function() {
                const text = $(this).text().toLowerCase();
                const shouldShow = text.indexOf(value) > -1;
                $(this).toggle(shouldShow);
                
                // إضافة تأثير highlight للنتائج
                if (shouldShow && value.length > 0) {
                    $(this).addClass('search-highlight');
                } else {
                    $(this).removeClass('search-highlight');
                }
            });
        }, 300);
    });

    // تحسين الفلترة مع عداد النتائج
    $('#account-type-filter').on('change', function() {
        const selectedType = $(this).val();
        let visibleCount = 0;
        
        if (selectedType === '') {
            $('.account-item').show();
            visibleCount = $('.account-item').length;
        } else {
            $('.account-item').hide();
            $('.account-item[data-account-type="' + selectedType + '"]').show();
            visibleCount = $('.account-item[data-account-type="' + selectedType + '"]').length;
        }
        
        // تحديث عداد النتائج
        updateResultsCounter(visibleCount);
    });
});

function deleteAccount(accountId) {
    if (confirm('{{ text_confirm_delete }}')) {
        $.ajax({
            url: '{{ delete }}',
            type: 'POST',
            data: {selected: [accountId]},
            success: function(response) {
                location.reload();
            }
        });
    }
}

function toggleCardView() {
    // تنفيذ عرض البطاقات
    $('.table-responsive').hide();
    // إضافة عرض البطاقات هنا
}

function toggleListView() {
    $('.table-responsive').show();
    // إخفاء عرض البطاقات
}

function updateAccountBalances() {
    $.ajax({
        url: '{{ update_balances }}',
        type: 'GET',
        dataType: 'json',
        success: function(data) {
            $.each(data, function(accountId, balance) {
                $('.account-balance[data-account-id="' + accountId + '"]').text(balance);
            });
        }
    });
}

// طباعة الصفحة
function printPage() {
    window.print();
}

// تصدير البيانات
function exportData(format) {
    window.location.href = '{{ export }}&format=' + format;
}

// تحديث عداد النتائج
function updateResultsCounter(count) {
    const resultsText = count === 1 ? 'نتيجة واحدة' : count + ' نتائج';
    $('.results-counter').text('عرض ' + resultsText);
}

// إضافة مؤشر تحميل للعمليات الطويلة
function showLoadingIndicator(message = 'جاري التحميل...') {
    if (!$('.loading-indicator').length) {
        $('body').append(`
            <div class="loading-indicator">
                <div class="loading-spinner">
                    <i class="fa fa-spinner fa-spin fa-2x"></i>
                    <p>${message}</p>
                </div>
            </div>
        `);
    }
}

function hideLoadingIndicator() {
    $('.loading-indicator').remove();
}

// Archive selected accounts
function archiveSelected() {
    var selected = [];
    $('input[name*="selected"]:checked').each(function() {
        selected.push($(this).val());
    });

    if (selected.length === 0) {
        alert('{{ error_select_accounts }}');
        return;
    }

    if (confirm('{{ text_confirm_archive }}')) {
        // Create form for archive action
        var form = $('<form>', {
            'method': 'POST',
            'action': '{{ archive }}'
        });

        // Add selected accounts
        $.each(selected, function(index, value) {
            form.append($('<input>', {
                'type': 'hidden',
                'name': 'selected[]',
                'value': value
            }));
        });

        // Add user token
        form.append($('<input>', {
            'type': 'hidden',
            'name': 'user_token',
            'value': '{{ user_token }}'
        }));

        // Submit form
        $('body').append(form);
        form.submit();
    }
}
</script>

{{ footer }}
