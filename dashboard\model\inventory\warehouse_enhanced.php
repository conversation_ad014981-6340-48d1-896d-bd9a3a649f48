<?php
/**
 * نموذج إدارة المستودعات المحسن - Enterprise Grade
 * 
 * التحسينات الجديدة:
 * - هيكل شجري متطور (parent-child)
 * - تقسيم د		
	
	return $this->db->getLastId();
	}
	
	/**
	 * تعديل مستودع موجود مع دعم الهيكل الشجري
	 */
	public function editWarehouse($warehouse_id, $data) {
		try {
			// بدء المعاملة
			$this->db->query("START TRANSACTION");
			
			// الحصول على معلومات المستودع الحالية
			$warehouse_info = $this->getWarehouse($warehouse_id);
			
			// التحقق من تغيير الأب
			if (isset($data['parent_id']) && $warehouse_info['parent_id'] != $data['parent_id']) {
				// التحقق من عدم اختيار المستودع نفسه أو أحد أبنائه كأب
				if ($data['parent_id'] == $warehouse_id) {
					throw new Exception('لا يمكن اختيار المستودع نفسه كأب');
				}
				
				// التحقق من عدم اختيار أحد الأبناء كأب
				$children = $this->getWarehouseChildren($warehouse_id);
				foreach ($children as $child) {
					if ($child['warehouse_id'] == $data['parent_id']) {
						throw new Exception('لا يمكن اختيار أحد الأبناء كأب');
					}
				}
				
				// تحديث المستوى والمسار
				$level = 0;
				$path = '';
				
				if (!empty($data['parent_id'])) {
					$parent_info = $this->getWarehouse($data['parent_id']);
					$level = $parent_info['level'] + 1;
					$path = $parent_info['path'];
				}
				
				// تحديث المستودع
				$this->db->query("
					UPDATE " . DB_PREFIX . "warehouse SET
					parent_id = '" . (int)$data['parent_id'] . "',
					level = '" . (int)$level . "',
					path = '" . $this->db->escape($path . $warehouse_id . '_') . "'
					WHERE warehouse_id = '" . (int)$warehouse_id . "'
				");
				
				// تحديث مستوى ومسار جميع الأبناء
				$this->updateWarehouseChildrenLevel($warehouse_id);
			}
			
			// تحديث بيانات المستودع
			$this->db->query("
				UPDATE " . DB_PREFIX . "warehouse SET
				name = '" . $this->db->escape($data['name']) . "',
				code = '" . $this->db->escape($data['code']) . "',
				address = '" . $this->db->escape($data['address']) . "',
				telephone = '" . $this->db->escape($data['telephone']) . "',
				email = '" . $this->db->escape($data['email']) . "',
				manager = '" . $this->db->escape($data['manager']) . "',
				total_capacity = '" . (float)($data['total_capacity'] ?? $warehouse_info['total_capacity']) . "',
				capacity_unit = '" . $this->db->escape($data['capacity_unit'] ?? $warehouse_info['capacity_unit']) . "',
				location_type = '" . $this->db->escape($data['location_type'] ?? $warehouse_info['location_type']) . "',
				account_id = '" . (int)($data['account_id'] ?? $warehouse_info['account_id']) . "',
				status = '" . (int)$data['status'] . "',
				sort_order = '" . (int)($data['sort_order'] ?? $warehouse_info['sort_order']) . "',
				date_modified = NOW()
				WHERE warehouse_id = '" . (int)$warehouse_id . "'
			");
			
			// تحديث المناطق
			if (isset($data['zones'])) {
				// حذف المناطق الحالية
				$this->db->query("DELETE FROM " . DB_PREFIX . "warehouse_zone WHERE warehouse_id = '" . (int)$warehouse_id . "'");
				
				// إضافة المناطق الجديدة
				foreach ($data['zones'] as $zone) {
					$this->addWarehouseZone($warehouse_id, $zone);
				}
			}
			
			// تأكيد المعاملة
			$this->db->query("COMMIT");
			
			return true;
			
		} catch (Exception $e) {
			// إلغاء المعاملة في حالة الخطأ
			$this->db->query("ROLLBACK");
			throw $e;
		}
	}
	
	/**
	 * حذف مستودع مع التحقق من الأبناء
	 */
	public function deleteWarehouse($warehouse_id) {
		try {
			// بدء المعاملة
			$this->db->query("START TRANSACTION");
			
			// التحقق من وجود أبناء
			$children = $this->getWarehouseChildren($warehouse_id);
			if (!empty($children)) {
				throw new Exception('لا يمكن حذف المستودع لأنه يحتوي على مستودعات فرعية');
			}
			
			// التحقق من وجود منتجات
			if ($this->getWarehouseProductCount($warehouse_id) > 0) {
				throw new Exception('لا يمكن حذف المستودع لأنه يحتوي على منتجات');
			}
			
			// حذف المناطق
			$this->db->query("DELETE FROM " . DB_PREFIX . "warehouse_zone WHERE warehouse_id = '" . (int)$warehouse_id . "'");
			
			// حذف الأرفف
			$this->db->query("DELETE FROM " . DB_PREFIX . "warehouse_rack WHERE warehouse_id = '" . (int)$warehouse_id . "'");
			
			// حذف المستودع
			$this->db->query("DELETE FROM " . DB_PREFIX . "warehouse WHERE warehouse_id = '" . (int)$warehouse_id . "'");
			
			// تأكيد المعاملة
			$this->db->query("COMMIT");
			
			return true;
			
		} catch (Exception $e) {
			// إلغاء المعاملة في حالة الخطأ
			$this->db->query("ROLLBACK");
			throw $e;
		}
	}
	
	/**
	 * الحصول على مستودع محدد
	 */
	public function getWarehouse($warehouse_id) {
		$query = $this->db->query("
			SELECT w.*, 
				   pw.name as parent_name,
				   (SELECT COUNT(*) FROM " . DB_PREFIX . "warehouse cw WHERE cw.parent_id = w.warehouse_id) as children_count,
				   (SELECT COUNT(*) FROM " . DB_PREFIX . "product_to_warehouse ptw WHERE ptw.warehouse_id = w.warehouse_id) as product_count,
				   (SELECT SUM(ptw.quantity * p.price) FROM " . DB_PREFIX . "product_to_warehouse ptw 
				    LEFT JOIN " . DB_PREFIX . "product p ON ptw.product_id = p.product_id 
				    WHERE ptw.warehouse_id = w.warehouse_id) as total_value
			FROM " . DB_PREFIX . "warehouse w
			LEFT JOIN " . DB_PREFIX . "warehouse pw ON w.parent_id = pw.warehouse_id
			WHERE w.warehouse_id = '" . (int)$warehouse_id . "'
		");
		
		return $query->row;
	}
	
	/**
	 * الحصول على قائمة المستودعات مع دعم الهيكل الشجري
	 */
	public function getWarehouses($data = array()) {
		$sql = "
			SELECT w.*, 
				   pw.name as parent_name,
				   (SELECT COUNT(*) FROM " . DB_PREFIX . "warehouse cw WHERE cw.parent_id = w.warehouse_id) as children_count,
				   (SELECT COUNT(*) FROM " . DB_PREFIX . "product_to_warehouse ptw WHERE ptw.warehouse_id = w.warehouse_id) as product_count,
				   (SELECT SUM(ptw.quantity * p.price) FROM " . DB_PREFIX . "product_to_warehouse ptw 
				    LEFT JOIN " . DB_PREFIX . "product p ON ptw.product_id = p.product_id 
				    WHERE ptw.warehouse_id = w.warehouse_id) as total_value
			FROM " . DB_PREFIX . "warehouse w
			LEFT JOIN " . DB_PREFIX . "warehouse pw ON w.parent_id = pw.warehouse_id
		";
		
		// إضافة فلاتر البحث
		$where = array();
		
		if (!empty($data['filter_name'])) {
			$where[] = "w.name LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
		}
		
		if (!empty($data['filter_code'])) {
			$where[] = "w.code LIKE '%" . $this->db->escape($data['filter_code']) . "%'";
		}
		
		if (isset($data['filter_status']) && $data['filter_status'] !== '') {
			$where[] = "w.status = '" . (int)$data['filter_status'] . "'";
		}
		
		if (!empty($data['filter_parent_id'])) {
			$where[] = "w.parent_id = '" . (int)$data['filter_parent_id'] . "'";
		}
		
		if (!empty($where)) {
			$sql .= " WHERE " . implode(" AND ", $where);
		}
		
		// ترتيب النتائج
		$sort_data = array(
			'name',
			'code',
			'status',
			'level',
			'sort_order',
			'date_added'
		);
		
		if (isset($data['sort']) && in_array($data['sort'], $sort_data)) {
			$sql .= " ORDER BY w." . $data['sort'];
		} else {
			$sql .= " ORDER BY w.level, w.sort_order, w.name";
		}
		
		if (isset($data['order']) && ($data['order'] == 'DESC')) {
			$sql .= " DESC";
		} else {
			$sql .= " ASC";
		}
		
		// تحديد عدد النتائج
		if (isset($data['start']) || isset($data['limit'])) {
			if ($data['start'] < 0) {
				$data['start'] = 0;
			}
			
			if ($data['limit'] < 1) {
				$data['limit'] = 20;
			}
			
			$sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
		}
		
		$query = $this->db->query($sql);
		
		return $query->rows;
	}
	
	/**
	 * الحصول على المستودعات في شكل شجري
	 */
	public function getWarehousesTree($parent_id = 0) {
		$warehouses = array();
		
		$query = $this->db->query("
			SELECT w.*, 
				   (SELECT COUNT(*) FROM " . DB_PREFIX . "warehouse cw WHERE cw.parent_id = w.warehouse_id) as children_count
			FROM " . DB_PREFIX . "warehouse w
			WHERE w.parent_id = '" . (int)$parent_id . "'
			ORDER BY w.sort_order, w.name
		");
		
		foreach ($query->rows as $warehouse) {
			$warehouse['children'] = $this->getWarehousesTree($warehouse['warehouse_id']);
			$warehouses[] = $warehouse;
		}
		
		return $warehouses;
	}
	
	/**
	 * الحصول على أبناء مستودع محدد
	 */
	public function getWarehouseChildren($warehouse_id) {
		$query = $this->db->query("
			SELECT * FROM " . DB_PREFIX . "warehouse 
			WHERE parent_id = '" . (int)$warehouse_id . "'
			ORDER BY sort_order, name
		");
		
		return $query->rows;
	}
	
	/**
	 * تحديث مستوى ومسار الأبناء
	 */
	private function updateWarehouseChildrenLevel($warehouse_id) {
		$warehouse_info = $this->getWarehouse($warehouse_id);
		$children = $this->getWarehouseChildren($warehouse_id);
		
		foreach ($children as $child) {
			$new_level = $warehouse_info['level'] + 1;
			$new_path = $warehouse_info['path'] . $child['warehouse_id'] . '_';
			
			$this->db->query("
				UPDATE " . DB_PREFIX . "warehouse SET
				level = '" . (int)$new_level . "',
				path = '" . $this->db->escape($new_path) . "'
				WHERE warehouse_id = '" . (int)$child['warehouse_id'] . "'
			");
			
			// تحديث أبناء الأبناء بشكل تكراري
			$this->updateWarehouseChildrenLevel($child['warehouse_id']);
		}
	}
	
	/**
	 * الحصول على إجمالي عدد المستودعات
	 */
	public function getTotalWarehouses($data = array()) {
		$sql = "SELECT COUNT(*) AS total FROM " . DB_PREFIX . "warehouse w";
		
		// إضافة فلاتر البحث
		$where = array();
		
		if (!empty($data['filter_name'])) {
			$where[] = "w.name LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
		}
		
		if (!empty($data['filter_code'])) {
			$where[] = "w.code LIKE '%" . $this->db->escape($data['filter_code']) . "%'";
		}
		
		if (isset($data['filter_status']) && $data['filter_status'] !== '') {
			$where[] = "w.status = '" . (int)$data['filter_status'] . "'";
		}
		
		if (!empty($where)) {
			$sql .= " WHERE " . implode(" AND ", $where);
		}
		
		$query = $this->db->query($sql);
		
		return $query->row['total'];
	}
	
	/**
	 * الحصول على عدد المنتجات في مستودع
	 */
	public function getWarehouseProductCount($warehouse_id) {
		$query = $this->db->query("
			SELECT COUNT(*) AS total 
			FROM " . DB_PREFIX . "product_to_warehouse 
			WHERE warehouse_id = '" . (int)$warehouse_id . "'
		");
		
		return $query->row['total'];
	}
	
	/**
	 * الحصول على إحصائيات المستودع
	 */
	public function getWarehouseStatistics() {
		$data = array();
		
		// إجمالي المستودعات
		$query = $this->db->query("SELECT COUNT(*) as total FROM " . DB_PREFIX . "warehouse WHERE status = 1");
		$data['total_warehouses'] = $query->row['total'];
		
		// إجمالي المنتجات
		$query = $this->db->query("SELECT SUM(quantity) as total FROM " . DB_PREFIX . "product_to_warehouse");
		$data['total_products'] = $query->row['total'] ?? 0;
		
		// إجمالي القيمة
		$query = $this->db->query("
			SELECT SUM(ptw.quantity * p.price) as total 
			FROM " . DB_PREFIX . "product_to_warehouse ptw
			LEFT JOIN " . DB_PREFIX . "product p ON ptw.product_id = p.product_id
		");
		$data['total_value'] = $query->row['total'] ?? 0;
		
		// المنتجات منخفضة المخزون
		$query = $this->db->query("
			SELECT COUNT(*) as total 
			FROM " . DB_PREFIX . "product_to_warehouse ptw
			LEFT JOIN " . DB_PREFIX . "product p ON ptw.product_id = p.product_id
			WHERE ptw.quantity <= p.minimum
		");
		$data['low_stock_products'] = $query->row['total'];
		
		return $data;
	}
	
	/**
	 * الحصول على تنبيهات المخزون المنخفض
	 */
	public function getLowStockAlerts($limit = 10) {
		$query = $this->db->query("
			SELECT ptw.*, p.name as product_name, p.minimum, w.name as warehouse_name
			FROM " . DB_PREFIX . "product_to_warehouse ptw
			LEFT JOIN " . DB_PREFIX . "product p ON ptw.product_id = p.product_id
			LEFT JOIN " . DB_PREFIX . "warehouse w ON ptw.warehouse_id = w.warehouse_id
			WHERE ptw.quantity <= p.minimum AND p.minimum > 0
			ORDER BY (ptw.quantity / p.minimum) ASC
			LIMIT " . (int)$limit
		);
		
		return $query->rows;
	}
	
	/**
	 * الحصول على الحركات الأخيرة
	 */
	public function getRecentMovements($limit = 10) {
		$query = $this->db->query("
			SELECT sm.*, p.name as product_name, w.name as warehouse_name
			FROM " . DB_PREFIX . "stock_movement sm
			LEFT JOIN " . DB_PREFIX . "product p ON sm.product_id = p.product_id
			LEFT JOIN " . DB_PREFIX . "warehouse w ON sm.warehouse_id = w.warehouse_id
			ORDER BY sm.date_added DESC
			LIMIT " . (int)$limit
		);
		
		return $query->rows;
	}
	
	/**
	 * الحصول على تنبيهات انتهاء الصلاحية
	 */
	public function getExpiryAlerts($days = 30) {
		$query = $this->db->query("
			SELECT ptw.*, p.name as product_name, w.name as warehouse_name
			FROM " . DB_PREFIX . "product_to_warehouse ptw
			LEFT JOIN " . DB_PREFIX . "product p ON ptw.product_id = p.product_id
			LEFT JOIN " . DB_PREFIX . "warehouse w ON ptw.warehouse_id = w.warehouse_id
			WHERE ptw.expiry_date IS NOT NULL 
			AND ptw.expiry_date <= DATE_ADD(NOW(), INTERVAL " . (int)$days . " DAY)
			AND ptw.expiry_date >= NOW()
			ORDER BY ptw.expiry_date ASC
		");
		
		return $query->rows;
	}
	
	/**
	 * الحصول على طلبات النقل المعلقة
	 */
	public function getPendingTransfers($limit = 10) {
		$query = $this->db->query("
			SELECT wt.*, 
				   wf.name as from_warehouse_name,
				   wt_to.name as to_warehouse_name
			FROM " . DB_PREFIX . "warehouse_transfer wt
			LEFT JOIN " . DB_PREFIX . "warehouse wf ON wt.from_warehouse_id = wf.warehouse_id
			LEFT JOIN " . DB_PREFIX . "warehouse wt_to ON wt.to_warehouse_id = wt_to.warehouse_id
			WHERE wt.status = 'pending'
			ORDER BY wt.date_added DESC
			LIMIT " . (int)$limit
		);
		
		return $query->rows;
	}
	
	/**
	 * الحصول على استخدام المستودعات
	 */
	public function getWarehouseUtilization() {
		$query = $this->db->query("
			SELECT w.warehouse_id, w.name, w.total_capacity, w.used_capacity, w.capacity_unit,
				   CASE 
					   WHEN w.total_capacity > 0 THEN (w.used_capacity / w.total_capacity * 100)
					   ELSE 0
				   END as utilization_percentage
			FROM " . DB_PREFIX . "warehouse w
			WHERE w.status = 1 AND w.total_capacity > 0
			ORDER BY utilization_percentage DESC
		");
		
		return $query->rows;
	}
}
?>"	
	
		return $this->db->getLastId();
	}
	
	/**
	 * الحصول على مستودع محدد مع معلومات الهيكل الشجري
	 */
	public function getWarehouse($warehouse_id) {
		$query = $this->db->query("
			SELECT w.*, 
				   parent.name as parent_name,
				   (SELECT COUNT(*) FROM " . DB_PREFIX . "warehouse child WHERE child.parent_id = w.warehouse_id) as children_count,
				   (SELECT COUNT(*) FROM " . DB_PREFIX . "product_to_warehouse ptw WHERE ptw.warehouse_id = w.warehouse_id) as product_count,
				   (SELECT SUM(ptw.quantity) FROM " . DB_PREFIX . "product_to_warehouse ptw WHERE ptw.warehouse_id = w.warehouse_id) as total_products,
				   ROUND((w.used_capacity / w.total_capacity) * 100, 2) as capacity_percentage
			FROM " . DB_PREFIX . "warehouse w
			LEFT JOIN " . DB_PREFIX . "warehouse parent ON (w.parent_id = parent.warehouse_id)
			WHERE w.warehouse_id = '" . (int)$warehouse_id . "'
		");
		
		if ($query->num_rows) {
			$warehouse = $query->row;
			
			// إضافة معلومات المناطق
			$warehouse['zones'] = $this->getWarehouseZones($warehouse_id);
			
			// إضافة معلومات الأرفف
			$warehouse['racks'] = $this->getWarehouseRacks($warehouse_id);
			
			return $warehouse;
		}
		
		return array();
	}
	
	/**
	 * الحصول على قائمة المستودعات مع دعم الهيكل الشجري
	 */
	public function getWarehouses($data = array()) {
		$sql = "
			SELECT w.*, 
				   parent.name as parent_name,
				   (SELECT COUNT(*) FROM " . DB_PREFIX . "warehouse child WHERE child.parent_id = w.warehouse_id) as children_count,
				   (SELECT COUNT(*) FROM " . DB_PREFIX . "product_to_warehouse ptw WHERE ptw.warehouse_id = w.warehouse_id) as product_count,
				   ROUND((w.used_capacity / NULLIF(w.total_capacity, 0)) * 100, 2) as capacity_percentage
			FROM " . DB_PREFIX . "warehouse w
			LEFT JOIN " . DB_PREFIX . "warehouse parent ON (w.parent_id = parent.warehouse_id)
			WHERE 1=1
		";
		
		// فلترة حسب الأب
		if (!empty($data['parent_id'])) {
			$sql .= " AND w.parent_id = '" . (int)$data['parent_id'] . "'";
		}
		
		// فلترة حسب المستوى
		if (isset($data['level'])) {
			$sql .= " AND w.level = '" . (int)$data['level'] . "'";
		}
		
		// فلترة حسب النوع
		if (!empty($data['location_type'])) {
			$sql .= " AND w.location_type = '" . $this->db->escape($data['location_type']) . "'";
		}
		
		// فلترة حسب الحالة
		if (isset($data['status'])) {
			$sql .= " AND w.status = '" . (int)$data['status'] . "'";
		}
		
		// البحث
		if (!empty($data['filter_name'])) {
			$sql .= " AND (w.name LIKE '%" . $this->db->escape($data['filter_name']) . "%' OR w.code LIKE '%" . $this->db->escape($data['filter_name']) . "%')";
		}
		
		// الترتيب
		$sort_data = array(
			'name',
			'code',
			'level',
			'location_type',
			'total_capacity',
			'used_capacity',
			'capacity_percentage',
			'status',
			'sort_order'
		);
		
		if (isset($data['sort']) && in_array($data['sort'], $sort_data)) {
			$sql .= " ORDER BY " . $data['sort'];
		} else {
			$sql .= " ORDER BY w.level, w.sort_order, w.name";
		}
		
		if (isset($data['order']) && ($data['order'] == 'DESC')) {
			$sql .= " DESC";
		} else {
			$sql .= " ASC";
		}
		
		// التصفح
		if (isset($data['start']) || isset($data['limit'])) {
			if ($data['start'] < 0) {
				$data['start'] = 0;
			}
			
			if ($data['limit'] < 1) {
				$data['limit'] = 20;
			}
			
			$sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
		}
		
		$query = $this->db->query($sql);
		
		return $query->rows;
	}
	
	/**
	 * الحصول على المستودعات في شكل شجري
	 */
	public function getWarehousesTree($parent_id = 0, $level = 0) {
		$warehouses = array();
		
		$query = $this->db->query("
			SELECT w.*, 
				   (SELECT COUNT(*) FROM " . DB_PREFIX . "warehouse child WHERE child.parent_id = w.warehouse_id) as children_count,
				   (SELECT COUNT(*) FROM " . DB_PREFIX . "product_to_warehouse ptw WHERE ptw.warehouse_id = w.warehouse_id) as product_count,
				   ROUND((w.used_capacity / NULLIF(w.total_capacity, 0)) * 100, 2) as capacity_percentage
			FROM " . DB_PREFIX . "warehouse w
			WHERE w.parent_id = '" . (int)$parent_id . "' AND w.status = '1'
			ORDER BY w.sort_order, w.name
		");
		
		foreach ($query->rows as $warehouse) {
			$warehouse['level'] = $level;
			$warehouse['children'] = $this->getWarehousesTree($warehouse['warehouse_id'], $level + 1);
			$warehouses[] = $warehouse;
		}
		
		return $warehouses;
	}
	
	/**
	 * الحصول على أبناء المستودع
	 */
	public function getWarehouseChildren($warehouse_id) {
		$query = $this->db->query("
			SELECT * FROM " . DB_PREFIX . "warehouse 
			WHERE parent_id = '" . (int)$warehouse_id . "'
			ORDER BY sort_order, name
		");
		
		return $query->rows;
	}
	
	/**
	 * تحديث مستوى ومسار الأبناء
	 */
	private function updateWarehouseChildrenLevel($warehouse_id) {
		$warehouse_info = $this->getWarehouse($warehouse_id);
		$children = $this->getWarehouseChildren($warehouse_id);
		
		foreach ($children as $child) {
			$new_level = $warehouse_info['level'] + 1;
			$new_path = $warehouse_info['path'] . $child['warehouse_id'] . '_';
			
			$this->db->query("
				UPDATE " . DB_PREFIX . "warehouse SET
				level = '" . (int)$new_level . "',
				path = '" . $this->db->escape($new_path) . "'
				WHERE warehouse_id = '" . (int)$child['warehouse_id'] . "'
			");
			
			// تحديث أبناء الأبناء بشكل تكراري
			$this->updateWarehouseChildrenLevel($child['warehouse_id']);
		}
	}
	
	/**
	 * الحصول على مناطق المستودع
	 */
	public function getWarehouseZones($warehouse_id) {
		$query = $this->db->query("
			SELECT wz.*,
				   (SELECT COUNT(*) FROM " . DB_PREFIX . "warehouse_rack wr WHERE wr.zone_id = wz.zone_id) as rack_count,
				   ROUND((wz.used_capacity / NULLIF(wz.total_capacity, 0)) * 100, 2) as capacity_percentage
			FROM " . DB_PREFIX . "warehouse_zone wz
			WHERE wz.warehouse_id = '" . (int)$warehouse_id . "'
			ORDER BY wz.sort_order, wz.name
		");
		
		return $query->rows;
	}
	
	/**
	 * الحصول على أرفف المستودع
	 */
	public function getWarehouseRacks($warehouse_id) {
		$query = $this->db->query("
			SELECT wr.*, wz.name as zone_name,
				   (SELECT COUNT(*) FROM " . DB_PREFIX . "product_to_warehouse ptw WHERE ptw.rack_id = wr.rack_id) as product_count,
				   ROUND((wr.used_capacity / NULLIF(wr.total_capacity, 0)) * 100, 2) as capacity_percentage
			FROM " . DB_PREFIX . "warehouse_rack wr
			LEFT JOIN " . DB_PREFIX . "warehouse_zone wz ON (wr.zone_id = wz.zone_id)
			WHERE wr.warehouse_id = '" . (int)$warehouse_id . "'
			ORDER BY wz.sort_order, wr.sort_order, wr.name
		");
		
		return $query->rows;
	}
	
	/**
	 * الحصول على إجمالي عدد المستودعات
	 */
	public function getTotalWarehouses($data = array()) {
		$sql = "SELECT COUNT(*) AS total FROM " . DB_PREFIX . "warehouse w WHERE 1=1";
		
		// فلترة حسب الأب
		if (!empty($data['parent_id'])) {
			$sql .= " AND w.parent_id = '" . (int)$data['parent_id'] . "'";
		}
		
		// فلترة حسب المستوى
		if (isset($data['level'])) {
			$sql .= " AND w.level = '" . (int)$data['level'] . "'";
		}
		
		// فلترة حسب النوع
		if (!empty($data['location_type'])) {
			$sql .= " AND w.location_type = '" . $this->db->escape($data['location_type']) . "'";
		}
		
		// فلترة حسب الحالة
		if (isset($data['status'])) {
			$sql .= " AND w.status = '" . (int)$data['status'] . "'";
		}
		
		// البحث
		if (!empty($data['filter_name'])) {
			$sql .= " AND (w.name LIKE '%" . $this->db->escape($data['filter_name']) . "%' OR w.code LIKE '%" . $this->db->escape($data['filter_name']) . "%')";
		}
		
		$query = $this->db->query($sql);
		
		return $query->row['total'];
	}
	
	/**
	 * الحصول على عدد المنتجات في المستودع
	 */
	public function getWarehouseProductCount($warehouse_id) {
		$query = $this->db->query("
			SELECT COUNT(*) AS total 
			FROM " . DB_PREFIX . "product_to_warehouse 
			WHERE warehouse_id = '" . (int)$warehouse_id . "'
		");
		
		return $query->row['total'];
	}
	
	/**
	 * الحصول على إحصائيات المستودع
	 */
	public function getWarehouseStatistics($warehouse_id = null) {
		$where = '';
		if ($warehouse_id) {
			$where = " WHERE w.warehouse_id = '" . (int)$warehouse_id . "'";
		}
		
		$query = $this->db->query("
			SELECT 
				COUNT(*) as total_warehouses,
				SUM(w.total_capacity) as total_capacity,
				SUM(w.used_capacity) as used_capacity,
				ROUND((SUM(w.used_capacity) / NULLIF(SUM(w.total_capacity), 0)) * 100, 2) as overall_capacity_percentage,
				COUNT(CASE WHEN w.status = 1 THEN 1 END) as active_warehouses,
				COUNT(CASE WHEN w.status = 0 THEN 1 END) as inactive_warehouses,
				(SELECT COUNT(*) FROM " . DB_PREFIX . "warehouse_zone wz JOIN " . DB_PREFIX . "warehouse w2 ON wz.warehouse_id = w2.warehouse_id" . str_replace('w.', 'w2.', $where) . ") as total_zones,
				(SELECT COUNT(*) FROM " . DB_PREFIX . "warehouse_rack wr JOIN " . DB_PREFIX . "warehouse w3 ON wr.warehouse_id = w3.warehouse_id" . str_replace('w.', 'w3.', $where) . ") as total_racks,
				(SELECT COUNT(*) FROM " . DB_PREFIX . "product_to_warehouse ptw JOIN " . DB_PREFIX . "warehouse w4 ON ptw.warehouse_id = w4.warehouse_id" . str_replace('w.', 'w4.', $where) . ") as total_products
			FROM " . DB_PREFIX . "warehouse w" . $where
		);
		
		return $query->row;
	}
	
	/**
	 * تحديث السعة المستخدمة للمستودع
	 */
	public function updateWarehouseCapacity($warehouse_id) {
		// حساب السعة المستخدمة من المنتجات
		$query = $this->db->query("
			SELECT SUM(ptw.quantity * p.weight) as used_capacity
			FROM " . DB_PREFIX . "product_to_warehouse ptw
			LEFT JOIN " . DB_PREFIX . "product p ON (ptw.product_id = p.product_id)
			WHERE ptw.warehouse_id = '" . (int)$warehouse_id . "'
		");
		
		$used_capacity = $query->row['used_capacity'] ?? 0;
		
		// تحديث السعة المستخدمة
		$this->db->query("
			UPDATE " . DB_PREFIX . "warehouse 
			SET used_capacity = '" . (float)$used_capacity . "',
				date_modified = NOW()
			WHERE warehouse_id = '" . (int)$warehouse_id . "'
		");
		
		return true;
	}
	
	/**
	 * نقل المنتجات بين المستودعات
	 */
	public function transferProducts($from_warehouse_id, $to_warehouse_id, $products) {
		try {
			// بدء المعاملة
			$this->db->query("START TRANSACTION");
			
			foreach ($products as $product) {
				$product_id = $product['product_id'];
				$quantity = $product['quantity'];
				
				// التحقق من توفر الكمية في المستودع المصدر
				$source_query = $this->db->query("
					SELECT quantity FROM " . DB_PREFIX . "product_to_warehouse 
					WHERE warehouse_id = '" . (int)$from_warehouse_id . "' 
					AND product_id = '" . (int)$product_id . "'
				");
				
				if (!$source_query->num_rows || $source_query->row['quantity'] < $quantity) {
					throw new Exception('الكمية المطلوبة غير متوفرة في المستودع المصدر');
				}
				
				// تقليل الكمية من المستودع المصدر
				$this->db->query("
					UPDATE " . DB_PREFIX . "product_to_warehouse 
					SET quantity = quantity - '" . (float)$quantity . "'
					WHERE warehouse_id = '" . (int)$from_warehouse_id . "' 
					AND product_id = '" . (int)$product_id . "'
				");
				
				// إضافة الكمية للمستودع الهدف
				$target_query = $this->db->query("
					SELECT * FROM " . DB_PREFIX . "product_to_warehouse 
					WHERE warehouse_id = '" . (int)$to_warehouse_id . "' 
					AND product_id = '" . (int)$product_id . "'
				");
				
				if ($target_query->num_rows) {
					// تحديث الكمية الموجودة
					$this->db->query("
						UPDATE " . DB_PREFIX . "product_to_warehouse 
						SET quantity = quantity + '" . (float)$quantity . "'
						WHERE warehouse_id = '" . (int)$to_warehouse_id . "' 
						AND product_id = '" . (int)$product_id . "'
					");
				} else {
					// إضافة سجل جديد
					$this->db->query("
						INSERT INTO " . DB_PREFIX . "product_to_warehouse 
						SET warehouse_id = '" . (int)$to_warehouse_id . "',
							product_id = '" . (int)$product_id . "',
							quantity = '" . (float)$quantity . "'
					");
				}
			}
			
			// تحديث السعة المستخدمة للمستودعين
			$this->updateWarehouseCapacity($from_warehouse_id);
			$this->updateWarehouseCapacity($to_warehouse_id);
			
			// تأكيد المعاملة
			$this->db->query("COMMIT");
			
			return true;
			
		} catch (Exception $e) {
			// إلغاء المعاملة في حالة الخطأ
			$this->db->query("ROLLBACK");
			throw $e;
		}
	}
	
	/**
	 * البحث المتقدم في المستودعات
	 */
	public function searchWarehouses($search_data) {
		$sql = "
			SELECT w.*, 
				   parent.name as parent_name,
				   (SELECT COUNT(*) FROM " . DB_PREFIX . "warehouse child WHERE child.parent_id = w.warehouse_id) as children_count,
				   (SELECT COUNT(*) FROM " . DB_PREFIX . "product_to_warehouse ptw WHERE ptw.warehouse_id = w.warehouse_id) as product_count,
				   ROUND((w.used_capacity / NULLIF(w.total_capacity, 0)) * 100, 2) as capacity_percentage
			FROM " . DB_PREFIX . "warehouse w
			LEFT JOIN " . DB_PREFIX . "warehouse parent ON (w.parent_id = parent.warehouse_id)
			WHERE 1=1
		";
		
		// البحث في الاسم والكود
		if (!empty($search_data['keyword'])) {
			$sql .= " AND (w.name LIKE '%" . $this->db->escape($search_data['keyword']) . "%' 
					  OR w.code LIKE '%" . $this->db->escape($search_data['keyword']) . "%'
					  OR w.address LIKE '%" . $this->db->escape($search_data['keyword']) . "%'
					  OR w.manager LIKE '%" . $this->db->escape($search_data['keyword']) . "%')";
		}
		
		// فلترة حسب النوع
		if (!empty($search_data['location_type'])) {
			$sql .= " AND w.location_type = '" . $this->db->escape($search_data['location_type']) . "'";
		}
		
		// فلترة حسب السعة
		if (!empty($search_data['min_capacity'])) {
			$sql .= " AND w.total_capacity >= '" . (float)$search_data['min_capacity'] . "'";
		}
		
		if (!empty($search_data['max_capacity'])) {
			$sql .= " AND w.total_capacity <= '" . (float)$search_data['max_capacity'] . "'";
		}
		
		// فلترة حسب نسبة الاستخدام
		if (!empty($search_data['min_usage'])) {
			$sql .= " AND (w.used_capacity / NULLIF(w.total_capacity, 0)) * 100 >= '" . (float)$search_data['min_usage'] . "'";
		}
		
		if (!empty($search_data['max_usage'])) {
			$sql .= " AND (w.used_capacity / NULLIF(w.total_capacity, 0)) * 100 <= '" . (float)$search_data['max_usage'] . "'";
		}
		
		// فلترة حسب الحالة
		if (isset($search_data['status'])) {
			$sql .= " AND w.status = '" . (int)$search_data['status'] . "'";
		}
		
		$sql .= " ORDER BY w.level, w.sort_order, w.name";
		
		$query = $this->db->query($sql);
		
		return $query->rows;
	}
}
?>"		

		return $this->db->getLastId();
	}
	
	/**
	 * الحصول على مستودع محدد مع معلومات الهيكل الشجري
	 */
	public function getWarehouse($warehouse_id) {
		$query = $this->db->query("
			SELECT w.*, 
				   parent.name as parent_name,
				   (SELECT COUNT(*) FROM " . DB_PREFIX . "warehouse child WHERE child.parent_id = w.warehouse_id) as children_count,
				   (SELECT COUNT(*) FROM " . DB_PREFIX . "product_to_warehouse ptw WHERE ptw.warehouse_id = w.warehouse_id) as product_count,
				   (SELECT SUM(ptw.quantity) FROM " . DB_PREFIX . "product_to_warehouse ptw WHERE ptw.warehouse_id = w.warehouse_id) as total_products,
				   ROUND((w.used_capacity / w.total_capacity) * 100, 2) as capacity_percentage
			FROM " . DB_PREFIX . "warehouse w
			LEFT JOIN " . DB_PREFIX . "warehouse parent ON (w.parent_id = parent.warehouse_id)
			WHERE w.warehouse_id = '" . (int)$warehouse_id . "'
		");
		
		return $query->row;
	}
	
	/**
	 * الحصول على قائمة المستودعات مع دعم الهيكل الشجري
	 */
	public function getWarehouses($data = array()) {
		$sql = "
			SELECT w.*, 
				   parent.name as parent_name,
				   (SELECT COUNT(*) FROM " . DB_PREFIX . "warehouse child WHERE child.parent_id = w.warehouse_id) as children_count,
				   (SELECT COUNT(*) FROM " . DB_PREFIX . "product_to_warehouse ptw WHERE ptw.warehouse_id = w.warehouse_id) as product_count,
				   ROUND((w.used_capacity / NULLIF(w.total_capacity, 0)) * 100, 2) as capacity_percentage
			FROM " . DB_PREFIX . "warehouse w
			LEFT JOIN " . DB_PREFIX . "warehouse parent ON (w.parent_id = parent.warehouse_id)
			WHERE 1=1
		";
		
		// فلترة حسب الأب
		if (!empty($data['filter_parent_id'])) {
			$sql .= " AND w.parent_id = '" . (int)$data['filter_parent_id'] . "'";
		}
		
		// فلترة حسب المستوى
		if (isset($data['filter_level'])) {
			$sql .= " AND w.level = '" . (int)$data['filter_level'] . "'";
		}
		
		// فلترة حسب النوع
		if (!empty($data['filter_location_type'])) {
			$sql .= " AND w.location_type = '" . $this->db->escape($data['filter_location_type']) . "'";
		}
		
		// فلترة حسب الحالة
		if (isset($data['filter_status'])) {
			$sql .= " AND w.status = '" . (int)$data['filter_status'] . "'";
		}
		
		// البحث
		if (!empty($data['filter_search'])) {
			$sql .= " AND (w.name LIKE '%" . $this->db->escape($data['filter_search']) . "%' 
						  OR w.code LIKE '%" . $this->db->escape($data['filter_search']) . "%'
						  OR w.address LIKE '%" . $this->db->escape($data['filter_search']) . "%')";
		}
		
		// الترتيب
		$sort_data = array(
			'name',
			'code', 
			'level',
			'location_type',
			'total_capacity',
			'used_capacity',
			'capacity_percentage',
			'status',
			'sort_order'
		);
		
		if (isset($data['sort']) && in_array($data['sort'], $sort_data)) {
			$sql .= " ORDER BY " . $data['sort'];
		} else {
			$sql .= " ORDER BY w.level, w.sort_order, w.name";
		}
		
		if (isset($data['order']) && ($data['order'] == 'DESC')) {
			$sql .= " DESC";
		} else {
			$sql .= " ASC";
		}
		
		// التصفح
		if (isset($data['start']) || isset($data['limit'])) {
			if ($data['start'] < 0) {
				$data['start'] = 0;
			}
			
			if ($data['limit'] < 1) {
				$data['limit'] = 20;
			}
			
			$sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
		}
		
		$query = $this->db->query($sql);
		
		return $query->rows;
	}
	
	/**
	 * الحصول على المستودعات في شكل شجري
	 */
	public function getWarehousesTree($parent_id = 0, $level = 0) {
		$warehouses = array();
		
		$query = $this->db->query("
			SELECT w.*, 
				   (SELECT COUNT(*) FROM " . DB_PREFIX . "warehouse child WHERE child.parent_id = w.warehouse_id) as children_count,
				   (SELECT COUNT(*) FROM " . DB_PREFIX . "product_to_warehouse ptw WHERE ptw.warehouse_id = w.warehouse_id) as product_count,
				   ROUND((w.used_capacity / NULLIF(w.total_capacity, 0)) * 100, 2) as capacity_percentage
			FROM " . DB_PREFIX . "warehouse w
			WHERE w.parent_id = '" . (int)$parent_id . "' 
			AND w.status = '1'
			ORDER BY w.sort_order, w.name
		");
		
		foreach ($query->rows as $warehouse) {
			$warehouse['level'] = $level;
			$warehouse['children'] = $this->getWarehousesTree($warehouse['warehouse_id'], $level + 1);
			$warehouses[] = $warehouse;
		}
		
		return $warehouses;
	}
	
	/**
	 * الحصول على أبناء المستودع
	 */
	public function getWarehouseChildren($warehouse_id) {
		$query = $this->db->query("
			SELECT w.*, 
				   (SELECT COUNT(*) FROM " . DB_PREFIX . "warehouse child WHERE child.parent_id = w.warehouse_id) as children_count
			FROM " . DB_PREFIX . "warehouse w
			WHERE w.parent_id = '" . (int)$warehouse_id . "'
			ORDER BY w.sort_order, w.name
		");
		
		return $query->rows;
	}
	
	/**
	 * تحديث مستوى ومسار الأبناء
	 */
	private function updateWarehouseChildrenLevel($warehouse_id) {
		$warehouse_info = $this->getWarehouse($warehouse_id);
		$children = $this->getWarehouseChildren($warehouse_id);
		
		foreach ($children as $child) {
			$new_level = $warehouse_info['level'] + 1;
			$new_path = $warehouse_info['path'] . $child['warehouse_id'] . '_';
			
			$this->db->query("
				UPDATE " . DB_PREFIX . "warehouse SET
				level = '" . (int)$new_level . "',
				path = '" . $this->db->escape($new_path) . "'
				WHERE warehouse_id = '" . (int)$child['warehouse_id'] . "'
			");
			
			// تحديث الأبناء بشكل تكراري
			$this->updateWarehouseChildrenLevel($child['warehouse_id']);
		}
	}
	
	/**
	 * الحصول على إجمالي عدد المستودعات
	 */
	public function getTotalWarehouses($data = array()) {
		$sql = "SELECT COUNT(*) AS total FROM " . DB_PREFIX . "warehouse w WHERE 1=1";
		
		// تطبيق نفس الفلاتر
		if (!empty($data['filter_parent_id'])) {
			$sql .= " AND w.parent_id = '" . (int)$data['filter_parent_id'] . "'";
		}
		
		if (isset($data['filter_level'])) {
			$sql .= " AND w.level = '" . (int)$data['filter_level'] . "'";
		}
		
		if (!empty($data['filter_location_type'])) {
			$sql .= " AND w.location_type = '" . $this->db->escape($data['filter_location_type']) . "'";
		}
		
		if (isset($data['filter_status'])) {
			$sql .= " AND w.status = '" . (int)$data['filter_status'] . "'";
		}
		
		if (!empty($data['filter_search'])) {
			$sql .= " AND (w.name LIKE '%" . $this->db->escape($data['filter_search']) . "%' 
						  OR w.code LIKE '%" . $this->db->escape($data['filter_search']) . "%'
						  OR w.address LIKE '%" . $this->db->escape($data['filter_search']) . "%')";
		}
		
		$query = $this->db->query($sql);
		
		return $query->row['total'];
	}
	
	/**
	 * الحصول على عدد المنتجات في المستودع
	 */
	public function getWarehouseProductCount($warehouse_id) {
		$query = $this->db->query("
			SELECT COUNT(*) AS total 
			FROM " . DB_PREFIX . "product_to_warehouse 
			WHERE warehouse_id = '" . (int)$warehouse_id . "'
		");
		
		return $query->row['total'];
	}
	
	/**
	 * الحصول على إحصائيات المستودع
	 */
	public function getWarehouseStatistics($warehouse_id = null) {
		$where = '';
		if ($warehouse_id) {
			$where = "WHERE w.warehouse_id = '" . (int)$warehouse_id . "'";
		}
		
		$query = $this->db->query("
			SELECT 
				COUNT(*) as total_warehouses,
				COUNT(CASE WHEN w.status = 1 THEN 1 END) as active_warehouses,
				COUNT(CASE WHEN w.status = 0 THEN 1 END) as inactive_warehouses,
				SUM(w.total_capacity) as total_capacity,
				SUM(w.used_capacity) as used_capacity,
				ROUND((SUM(w.used_capacity) / NULLIF(SUM(w.total_capacity), 0)) * 100, 2) as overall_utilization,
				COUNT(CASE WHEN w.level = 0 THEN 1 END) as main_warehouses,
				COUNT(CASE WHEN w.level > 0 THEN 1 END) as sub_warehouses
			FROM " . DB_PREFIX . "warehouse w
			$where
		");
		
		return $query->row;
	}
	
	/**
	 * الحصول على مناطق المستودع
	 */
	public function getWarehouseZones($warehouse_id) {
		$query = $this->db->query("
			SELECT wz.*,
				   (SELECT COUNT(*) FROM " . DB_PREFIX . "warehouse_rack wr WHERE wr.zone_id = wz.zone_id) as rack_count,
				   ROUND((wz.used_capacity / NULLIF(wz.total_capacity, 0)) * 100, 2) as capacity_percentage
			FROM " . DB_PREFIX . "warehouse_zone wz
			WHERE wz.warehouse_id = '" . (int)$warehouse_id . "'
			ORDER BY wz.sort_order, wz.name
		");
		
		return $query->rows;
	}
	
	/**
	 * تحديث السعة المستخدمة للمستودع
	 */
	public function updateWarehouseCapacity($warehouse_id) {
		// حساب السعة المستخدمة من المناطق
		$query = $this->db->query("
			SELECT SUM(used_capacity) as total_used
			FROM " . DB_PREFIX . "warehouse_zone
			WHERE warehouse_id = '" . (int)$warehouse_id . "'
		");
		
		$used_capacity = $query->row['total_used'] ?? 0;
		
		// تحديث السعة المستخدمة
		$this->db->query("
			UPDATE " . DB_PREFIX . "warehouse 
			SET used_capacity = '" . (float)$used_capacity . "'
			WHERE warehouse_id = '" . (int)$warehouse_id . "'
		");
		
		return true;
	}
	
	/**
	 * نقل المنتجات بين المستودعات
	 */
	public function transferProducts($from_warehouse_id, $to_warehouse_id, $products, $notes = '') {
		try {
			// بدء المعاملة
			$this->db->query("START TRANSACTION");
			
			foreach ($products as $product) {
				$product_id = $product['product_id'];
				$quantity = $product['quantity'];
				
				// التحقق من توفر الكمية في المستودع المصدر
				$source_query = $this->db->query("
					SELECT quantity 
					FROM " . DB_PREFIX . "product_to_warehouse 
					WHERE product_id = '" . (int)$product_id . "' 
					AND warehouse_id = '" . (int)$from_warehouse_id . "'
				");
				
				if (!$source_query->num_rows || $source_query->row['quantity'] < $quantity) {
					throw new Exception('الكمية المطلوبة غير متوفرة في المستودع المصدر');
				}
				
				// تقليل الكمية من المستودع المصدر
				$this->db->query("
					UPDATE " . DB_PREFIX . "product_to_warehouse 
					SET quantity = quantity - '" . (float)$quantity . "'
					WHERE product_id = '" . (int)$product_id . "' 
					AND warehouse_id = '" . (int)$from_warehouse_id . "'
				");
				
				// إضافة الكمية للمستودع الهدف
				$target_query = $this->db->query("
					SELECT quantity 
					FROM " . DB_PREFIX . "product_to_warehouse 
					WHERE product_id = '" . (int)$product_id . "' 
					AND warehouse_id = '" . (int)$to_warehouse_id . "'
				");
				
				if ($target_query->num_rows) {
					// تحديث الكمية الموجودة
					$this->db->query("
						UPDATE " . DB_PREFIX . "product_to_warehouse 
						SET quantity = quantity + '" . (float)$quantity . "'
						WHERE product_id = '" . (int)$product_id . "' 
						AND warehouse_id = '" . (int)$to_warehouse_id . "'
					");
				} else {
					// إدراج سجل جديد
					$this->db->query("
						INSERT INTO " . DB_PREFIX . "product_to_warehouse 
						SET product_id = '" . (int)$product_id . "',
							warehouse_id = '" . (int)$to_warehouse_id . "',
							quantity = '" . (float)$quantity . "'
					");
				}
				
				// تسجيل حركة المخزون
				$this->db->query("
					INSERT INTO " . DB_PREFIX . "stock_movement 
					SET product_id = '" . (int)$product_id . "',
						warehouse_id = '" . (int)$from_warehouse_id . "',
						movement_type = 'transfer_out',
						quantity = '-" . (float)$quantity . "',
						reference_type = 'warehouse_transfer',
						reference_id = '" . (int)$to_warehouse_id . "',
						notes = '" . $this->db->escape($notes) . "',
						date_added = NOW()
				");
				
				$this->db->query("
					INSERT INTO " . DB_PREFIX . "stock_movement 
					SET product_id = '" . (int)$product_id . "',
						warehouse_id = '" . (int)$to_warehouse_id . "',
						movement_type = 'transfer_in',
						quantity = '" . (float)$quantity . "',
						reference_type = 'warehouse_transfer',
						reference_id = '" . (int)$from_warehouse_id . "',
						notes = '" . $this->db->escape($notes) . "',
						date_added = NOW()
				");
			}
			
			// تأكيد المعاملة
			$this->db->query("COMMIT");
			
			return true;
			
		} catch (Exception $e) {
			// إلغاء المعاملة في حالة الخطأ
			$this->db->query("ROLLBACK");
			throw $e;
		}
	}
	
	/**
	 * البحث المتقدم في المستودعات
	 */
	public function searchWarehouses($search_data) {
		$sql = "
			SELECT w.*, 
				   parent.name as parent_name,
				   (SELECT COUNT(*) FROM " . DB_PREFIX . "warehouse child WHERE child.parent_id = w.warehouse_id) as children_count,
				   (SELECT COUNT(*) FROM " . DB_PREFIX . "product_to_warehouse ptw WHERE ptw.warehouse_id = w.warehouse_id) as product_count,
				   ROUND((w.used_capacity / NULLIF(w.total_capacity, 0)) * 100, 2) as capacity_percentage
			FROM " . DB_PREFIX . "warehouse w
			LEFT JOIN " . DB_PREFIX . "warehouse parent ON (w.parent_id = parent.warehouse_id)
			WHERE 1=1
		";
		
		// البحث في النص
		if (!empty($search_data['text'])) {
			$sql .= " AND (w.name LIKE '%" . $this->db->escape($search_data['text']) . "%' 
						  OR w.code LIKE '%" . $this->db->escape($search_data['text']) . "%'
						  OR w.address LIKE '%" . $this->db->escape($search_data['text']) . "%'
						  OR w.manager LIKE '%" . $this->db->escape($search_data['text']) . "%')";
		}
		
		// البحث حسب النوع
		if (!empty($search_data['location_type'])) {
			$sql .= " AND w.location_type = '" . $this->db->escape($search_data['location_type']) . "'";
		}
		
		// البحث حسب السعة
		if (!empty($search_data['min_capacity'])) {
			$sql .= " AND w.total_capacity >= '" . (float)$search_data['min_capacity'] . "'";
		}
		
		if (!empty($search_data['max_capacity'])) {
			$sql .= " AND w.total_capacity <= '" . (float)$search_data['max_capacity'] . "'";
		}
		
		// البحث حسب نسبة الاستخدام
		if (!empty($search_data['min_utilization'])) {
			$sql .= " AND (w.used_capacity / NULLIF(w.total_capacity, 0)) * 100 >= '" . (float)$search_data['min_utilization'] . "'";
		}
		
		if (!empty($search_data['max_utilization'])) {
			$sql .= " AND (w.used_capacity / NULLIF(w.total_capacity, 0)) * 100 <= '" . (float)$search_data['max_utilization'] . "'";
		}
		
		$sql .= " ORDER BY w.level, w.sort_order, w.name";
		
		$query = $this->db->query($sql);
		
		return $query->rows;
	}
}