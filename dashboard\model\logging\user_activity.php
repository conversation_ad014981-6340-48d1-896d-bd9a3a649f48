<?php
/**
 * نشاط المستخدمين - AYM ERP
 * User Activity Model
 * 
 * @package    AYM ERP
 * <AUTHOR> Team
 * @copyright  2025 AYM ERP
 * @license    Commercial
 * @version    2.0.0
 */

class ModelLoggingUserActivity extends Model {
    
    /**
     * تسجيل نشاط المستخدم
     */
    public function logUserActivity($activity_type, $description, $additional_data = array()) {
        $sql = "INSERT INTO " . DB_PREFIX . "user_activity SET
                user_id = '" . (int)$this->user->getId() . "',
                activity_type = '" . $this->db->escape($activity_type) . "',
                description = '" . $this->db->escape($description) . "',
                additional_data = '" . $this->db->escape(json_encode($additional_data)) . "',
                ip_address = '" . $this->db->escape(isset($this->request->server['REMOTE_ADDR']) ? $this->request->server['REMOTE_ADDR'] : '') . "',
                user_agent = '" . $this->db->escape(isset($this->request->server['HTTP_USER_AGENT']) ? $this->request->server['HTTP_USER_AGENT'] : '') . "',
                session_id = '" . $this->db->escape($this->session->getId()) . "',
                created_at = NOW()";
        
        $this->db->query($sql);
        
        return $this->db->getLastId();
    }
    
    /**
     * تسجيل دخول المستخدم
     */
    public function logLogin() {
        return $this->logUserActivity('login', 'تسجيل دخول ناجح');
    }
    
    /**
     * تسجيل خروج المستخدم
     */
    public function logLogout() {
        return $this->logUserActivity('logout', 'تسجيل خروج');
    }
    
    /**
     * تسجيل زيارة صفحة
     */
    public function logPageView($route, $page_title = '') {
        $additional_data = array(
            'route' => $route,
            'page_title' => $page_title,
            'referer' => isset($this->request->server['HTTP_REFERER']) ? $this->request->server['HTTP_REFERER'] : ''
        );
        
        return $this->logUserActivity('page_view', 'زيارة صفحة: ' . $route, $additional_data);
    }
    
    /**
     * تسجيل إجراء المستخدم
     */
    public function logUserAction($action, $target, $details = array()) {
        $description = "تم تنفيذ إجراء: {$action} على {$target}";
        
        return $this->logUserActivity('action', $description, $details);
    }
    
    /**
     * الحصول على نشاط المستخدم
     */
    public function getUserActivity($user_id = null, $limit = 50, $offset = 0) {
        $user_id = $user_id ?: $this->user->getId();
        
        $sql = "SELECT ua.*, u.firstname, u.lastname
                FROM " . DB_PREFIX . "user_activity ua
                LEFT JOIN " . DB_PREFIX . "user u ON (ua.user_id = u.user_id)
                WHERE ua.user_id = '" . (int)$user_id . "'
                ORDER BY ua.created_at DESC";
        
        if ($limit > 0) {
            $sql .= " LIMIT " . (int)$offset . ", " . (int)$limit;
        }
        
        $query = $this->db->query($sql);
        
        return $query->rows;
    }
    
    /**
     * الحصول على المستخدمين النشطين
     */
    public function getActiveUsers($minutes = 30) {
        $sql = "SELECT DISTINCT ua.user_id, u.firstname, u.lastname, u.email,
                       MAX(ua.created_at) as last_activity
                FROM " . DB_PREFIX . "user_activity ua
                LEFT JOIN " . DB_PREFIX . "user u ON (ua.user_id = u.user_id)
                WHERE ua.created_at >= DATE_SUB(NOW(), INTERVAL " . (int)$minutes . " MINUTE)
                GROUP BY ua.user_id
                ORDER BY last_activity DESC";
        
        $query = $this->db->query($sql);
        
        return $query->rows;
    }
    
    /**
     * الحصول على عدد المستخدمين النشطين
     */
    public function getActiveUsersCount($minutes = 30) {
        $sql = "SELECT COUNT(DISTINCT user_id) as count
                FROM " . DB_PREFIX . "user_activity
                WHERE created_at >= DATE_SUB(NOW(), INTERVAL " . (int)$minutes . " MINUTE)";
        
        $query = $this->db->query($sql);
        
        return $query->row['count'];
    }
    
    /**
     * الحصول على إحصائيات نشاط المستخدم
     */
    public function getUserActivityStats($user_id = null, $period = 'today') {
        $user_id = $user_id ?: $this->user->getId();
        $stats = array();
        
        $date_condition = '';
        switch ($period) {
            case 'today':
                $date_condition = "DATE(created_at) = CURDATE()";
                break;
            case 'week':
                $date_condition = "created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)";
                break;
            case 'month':
                $date_condition = "created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)";
                break;
        }
        
        // إجمالي الأنشطة
        $sql = "SELECT COUNT(*) as total FROM " . DB_PREFIX . "user_activity 
                WHERE user_id = '" . (int)$user_id . "' AND " . $date_condition;
        $query = $this->db->query($sql);
        $stats['total_activities'] = $query->row['total'];
        
        // زيارات الصفحات
        $sql = "SELECT COUNT(*) as page_views FROM " . DB_PREFIX . "user_activity 
                WHERE user_id = '" . (int)$user_id . "' 
                AND activity_type = 'page_view' AND " . $date_condition;
        $query = $this->db->query($sql);
        $stats['page_views'] = $query->row['page_views'];
        
        // الإجراءات
        $sql = "SELECT COUNT(*) as actions FROM " . DB_PREFIX . "user_activity 
                WHERE user_id = '" . (int)$user_id . "' 
                AND activity_type = 'action' AND " . $date_condition;
        $query = $this->db->query($sql);
        $stats['actions'] = $query->row['actions'];
        
        return $stats;
    }
    
    /**
     * الحصول على الصفحات الأكثر زيارة
     */
    public function getMostVisitedPages($limit = 10) {
        $sql = "SELECT JSON_EXTRACT(additional_data, '$.route') as route,
                       JSON_EXTRACT(additional_data, '$.page_title') as page_title,
                       COUNT(*) as visits
                FROM " . DB_PREFIX . "user_activity
                WHERE activity_type = 'page_view'
                AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                GROUP BY JSON_EXTRACT(additional_data, '$.route')
                ORDER BY visits DESC
                LIMIT " . (int)$limit;
        
        $query = $this->db->query($sql);
        
        return $query->rows;
    }
    
    /**
     * تنظيف أنشطة المستخدمين القديمة
     */
    public function cleanupOldActivity($days = 90) {
        $sql = "DELETE FROM " . DB_PREFIX . "user_activity 
                WHERE created_at < DATE_SUB(NOW(), INTERVAL " . (int)$days . " DAY)";
        
        $this->db->query($sql);
        
        return $this->db->countAffected();
    }
    
    /**
     * الحصول على تقرير نشاط المستخدمين
     */
    public function getUserActivityReport($date_from, $date_to, $user_id = null) {
        $sql = "SELECT ua.user_id, u.firstname, u.lastname,
                       COUNT(*) as total_activities,
                       COUNT(CASE WHEN ua.activity_type = 'page_view' THEN 1 END) as page_views,
                       COUNT(CASE WHEN ua.activity_type = 'action' THEN 1 END) as actions,
                       MIN(ua.created_at) as first_activity,
                       MAX(ua.created_at) as last_activity
                FROM " . DB_PREFIX . "user_activity ua
                LEFT JOIN " . DB_PREFIX . "user u ON (ua.user_id = u.user_id)
                WHERE DATE(ua.created_at) BETWEEN '" . $this->db->escape($date_from) . "' 
                AND '" . $this->db->escape($date_to) . "'";
        
        if ($user_id) {
            $sql .= " AND ua.user_id = '" . (int)$user_id . "'";
        }
        
        $sql .= " GROUP BY ua.user_id
                  ORDER BY total_activities DESC";
        
        $query = $this->db->query($sql);
        
        return $query->rows;
    }
    
    /**
     * الحصول على إشعارات نشاط المستخدم
     */
    public function getUserSecurityNotifications($user_id) {
        // محاولات دخول من أماكن جديدة
        $sql = "SELECT DISTINCT ip_address, created_at
                FROM " . DB_PREFIX . "user_activity
                WHERE user_id = '" . (int)$user_id . "'
                AND activity_type = 'login'
                AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
                ORDER BY created_at DESC";
        
        $query = $this->db->query($sql);
        
        $notifications = array();
        
        foreach ($query->rows as $row) {
            $notifications[] = array(
                'type' => 'login_location',
                'title' => 'تسجيل دخول من موقع جديد',
                'message' => 'تم تسجيل دخول من IP: ' . $row['ip_address'],
                'time' => $row['created_at'],
                'priority' => 'medium'
            );
        }
        
        return $notifications;
    }

    // =================== تحسينات المخزون والتجارة الإلكترونية v10.0 ===================

    /**
     * تسجيل عمليات WAC
     */
    public function logWACActivity($action, $product_id, $details = array()) {
        $descriptions = array(
            'calculate_single' => 'حساب WAC لمنتج واحد',
            'calculate_category' => 'حساب WAC لفئة كاملة',
            'calculate_all' => 'حساب WAC لجميع المنتجات',
            'recalculate' => 'إعادة حساب WAC',
            'update_cost' => 'تحديث تكلفة المنتج'
        );

        $description = (isset($descriptions[$action]) ? $descriptions[$action] : 'عملية WAC') . " - المنتج #{$product_id}";

        if (!empty($details['old_wac']) && !empty($details['new_wac'])) {
            $description .= " (من {$details['old_wac']} إلى {$details['new_wac']})";
        }

        $additional_data = array_merge($details, array(
            'product_id' => $product_id,
            'wac_action' => $action,
            'calculation_method' => isset($details['method']) ? $details['method'] : 'real_time'
        ));

        return $this->logUserActivity('wac_' . $action, $description, $additional_data);
    }

    /**
     * تسجيل عمليات مزامنة المخزون
     */
    public function logInventorySyncActivity($action, $details = array()) {
        $descriptions = array(
            'sync_branches' => 'مزامنة المخزون بين الفروع',
            'sync_product' => 'مزامنة منتج واحد',
            'sync_realtime' => 'مزامنة فورية',
            'resolve_conflict' => 'حل تعارض المزامنة',
            'sync_failed' => 'فشل في المزامنة'
        );

        $description = isset($descriptions[$action]) ? $descriptions[$action] : 'عملية مزامنة مخزون';

        if (!empty($details['source_branch_id']) && !empty($details['target_branch_ids'])) {
            $target_count = is_array($details['target_branch_ids']) ? count($details['target_branch_ids']) : 1;
            $description .= " (من فرع {$details['source_branch_id']} إلى {$target_count} فرع)";
        }

        if (!empty($details['synced_products'])) {
            $description .= " - {$details['synced_products']} منتج";
        }

        return $this->logUserActivity('inventory_sync_' . $action, $description, $details);
    }

    /**
     * تسجيل عمليات معالجة الطلبات
     */
    public function logOrderProcessingActivity($action, $order_id, $details = array()) {
        $descriptions = array(
            'process_order' => 'معالجة طلب',
            'process_bundle' => 'معالجة طلب باقات',
            'process_subscription' => 'معالجة طلب اشتراك',
            'process_preorder' => 'معالجة طلب مسبق',
            'process_backorder' => 'معالجة طلب متأخر',
            'apply_discount' => 'تطبيق خصم',
            'update_inventory' => 'تحديث المخزون للطلب',
            'create_accounting' => 'إنشاء قيود محاسبية',
            'order_failed' => 'فشل في معالجة الطلب'
        );

        $description = (isset($descriptions[$action]) ? $descriptions[$action] : 'عملية طلب') . " #{$order_id}";

        if (!empty($details['complexity_score'])) {
            $description .= " (تعقيد: {$details['complexity_score']})";
        }

        if (!empty($details['total_amount'])) {
            $description .= " - {$details['total_amount']} " . (isset($details['currency']) ? $details['currency'] : 'ج.م');
        }

        $additional_data = array_merge($details, array(
            'order_id' => $order_id,
            'order_action' => $action
        ));

        return $this->logUserActivity('order_' . $action, $description, $additional_data);
    }

    /**
     * تسجيل عمليات التسعير الديناميكي
     */
    public function logDynamicPricingActivity($action, $details = array()) {
        $descriptions = array(
            'update_pricing' => 'تحديث الأسعار الديناميكية',
            'calculate_level' => 'حساب مستوى سعر',
            'apply_customer_pricing' => 'تطبيق تسعير العملاء',
            'apply_quantity_pricing' => 'تطبيق تسعير الكمية',
            'apply_time_pricing' => 'تطبيق التسعير الزمني',
            'margin_protection' => 'تطبيق حماية الهامش',
            'pricing_failed' => 'فشل في تحديث التسعير'
        );

        $description = isset($descriptions[$action]) ? $descriptions[$action] : 'عملية تسعير ديناميكي';

        if (!empty($details['updated_products'])) {
            $description .= " - {$details['updated_products']} منتج";
        }

        if (!empty($details['product_id'])) {
            $description .= " - المنتج #{$details['product_id']}";
        }

        return $this->logUserActivity('pricing_' . $action, $description, $details);
    }

    /**
     * تسجيل عمليات الطوابير
     */
    public function logQueueActivity($action, $details = array()) {
        $descriptions = array(
            'job_added' => 'إضافة مهمة للطابور',
            'job_started' => 'بدء تنفيذ مهمة',
            'job_completed' => 'اكتمال مهمة',
            'job_failed' => 'فشل مهمة',
            'job_cancelled' => 'إلغاء مهمة',
            'job_retry' => 'إعادة محاولة مهمة',
            'queue_cleared' => 'تفريغ الطابور'
        );

        $description = isset($descriptions[$action]) ? $descriptions[$action] : 'عملية طابور';

        if (!empty($details['job_id'])) {
            $description .= " #{$details['job_id']}";
        }

        if (!empty($details['job_type'])) {
            $description .= " ({$details['job_type']})";
        }

        if (!empty($details['priority'])) {
            $description .= " - أولوية: {$details['priority']}";
        }

        return $this->logUserActivity('queue_' . $action, $description, $details);
    }

    /**
     * تسجيل عمليات الباقات
     */
    public function logBundleActivity($action, $bundle_id, $details = array()) {
        $descriptions = array(
            'create_bundle' => 'إنشاء باقة',
            'update_bundle' => 'تحديث باقة',
            'delete_bundle' => 'حذف باقة',
            'add_component' => 'إضافة مكون للباقة',
            'remove_component' => 'إزالة مكون من الباقة',
            'calculate_price' => 'حساب سعر الباقة',
            'check_availability' => 'فحص توفر الباقة',
            'assemble_bundle' => 'تجميع باقة',
            'disassemble_bundle' => 'فك باقة'
        );

        $description = (isset($descriptions[$action]) ? $descriptions[$action] : 'عملية باقة') . " #{$bundle_id}";

        if (!empty($details['components_count'])) {
            $description .= " ({$details['components_count']} مكون)";
        }

        if (!empty($details['bundle_price'])) {
            $description .= " - {$details['bundle_price']} " . (isset($details['currency']) ? $details['currency'] : 'ج.م');
        }

        $additional_data = array_merge($details, array(
            'bundle_id' => $bundle_id,
            'bundle_action' => $action
        ));

        return $this->logUserActivity('bundle_' . $action, $description, $additional_data);
    }

    /**
     * تسجيل عمليات المحاسبة التلقائية
     */
    public function logAccountingActivity($action, $details = array()) {
        $descriptions = array(
            'create_journal_entry' => 'إنشاء قيد محاسبي',
            'sales_entry' => 'قيد المبيعات',
            'cogs_entry' => 'قيد تكلفة البضاعة المباعة',
            'inventory_entry' => 'قيد المخزون',
            'tax_entry' => 'قيد الضرائب',
            'discount_entry' => 'قيد الخصومات',
            'wac_adjustment' => 'تسوية WAC',
            'accounting_failed' => 'فشل في العملية المحاسبية'
        );

        $description = isset($descriptions[$action]) ? $descriptions[$action] : 'عملية محاسبية';

        if (!empty($details['journal_entry_id'])) {
            $description .= " #{$details['journal_entry_id']}";
        }

        if (!empty($details['amount'])) {
            $description .= " - {$details['amount']} " . (isset($details['currency']) ? $details['currency'] : 'ج.م');
        }

        if (!empty($details['reference_type']) && !empty($details['reference_id'])) {
            $description .= " (مرجع: {$details['reference_type']} #{$details['reference_id']})";
        }

        return $this->logUserActivity('accounting_' . $action, $description, $details);
    }

    /**
     * البحث المتقدم في سجل الأنشطة
     */
    public function searchActivities($filters = array(), $limit = 50, $offset = 0) {
        $sql = "SELECT ua.*, u.firstname, u.lastname
                FROM " . DB_PREFIX . "user_activity ua
                LEFT JOIN " . DB_PREFIX . "user u ON (ua.user_id = u.user_id)
                WHERE 1=1";

        // فلترة حسب نوع النشاط
        if (!empty($filters['activity_types'])) {
            $activity_types = array_map(array($this->db, 'escape'), $filters['activity_types']);
            $sql .= " AND ua.activity_type IN ('" . implode("','", $activity_types) . "')";
        }

        // فلترة حسب المستخدم
        if (!empty($filters['user_ids'])) {
            $user_ids = array_map('intval', $filters['user_ids']);
            $sql .= " AND ua.user_id IN (" . implode(',', $user_ids) . ")";
        }

        // فلترة حسب التاريخ
        if (!empty($filters['date_from'])) {
            $sql .= " AND DATE(ua.created_at) >= '" . $this->db->escape($filters['date_from']) . "'";
        }

        if (!empty($filters['date_to'])) {
            $sql .= " AND DATE(ua.created_at) <= '" . $this->db->escape($filters['date_to']) . "'";
        }

        // فلترة حسب الوصف
        if (!empty($filters['description'])) {
            $sql .= " AND ua.description LIKE '%" . $this->db->escape($filters['description']) . "%'";
        }

        // فلترة حسب عنوان IP
        if (!empty($filters['ip_address'])) {
            $sql .= " AND ua.ip_address = '" . $this->db->escape($filters['ip_address']) . "'";
        }

        // فلترة حسب الوحدة (من البيانات الإضافية)
        if (!empty($filters['modules'])) {
            $modules = array_map(array($this->db, 'escape'), $filters['modules']);
            $sql .= " AND (";
            foreach ($modules as $i => $module) {
                if ($i > 0) $sql .= " OR ";
                $sql .= "ua.additional_data LIKE '%\"module\":\"" . $module . "\"%'";
            }
            $sql .= ")";
        }

        $sql .= " ORDER BY ua.created_at DESC";

        if ($limit > 0) {
            $sql .= " LIMIT " . (int)$offset . ", " . (int)$limit;
        }

        $query = $this->db->query($sql);

        return $query->rows;
    }

    /**
     * إحصائيات الأنشطة المتقدمة
     */
    public function getAdvancedActivityStatistics($days = 30) {
        $date_filter = "DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL " . (int)$days . " DAY)";

        // إحصائيات عامة
        $total_activities = $this->db->query("
            SELECT COUNT(*) as total FROM " . DB_PREFIX . "user_activity
            WHERE " . $date_filter
        )->row['total'];

        // أنشطة حسب النوع
        $activities_by_type = $this->db->query("
            SELECT
                CASE
                    WHEN activity_type LIKE 'wac_%' THEN 'WAC Operations'
                    WHEN activity_type LIKE 'inventory_sync_%' THEN 'Inventory Sync'
                    WHEN activity_type LIKE 'order_%' THEN 'Order Processing'
                    WHEN activity_type LIKE 'pricing_%' THEN 'Dynamic Pricing'
                    WHEN activity_type LIKE 'queue_%' THEN 'Queue Operations'
                    WHEN activity_type LIKE 'bundle_%' THEN 'Bundle Management'
                    WHEN activity_type LIKE 'accounting_%' THEN 'Accounting'
                    ELSE 'Other'
                END as category,
                COUNT(*) as count
            FROM " . DB_PREFIX . "user_activity
            WHERE " . $date_filter . "
            GROUP BY category
            ORDER BY count DESC
        ")->rows;

        // المستخدمين الأكثر نشاطاً
        $most_active_users = $this->db->query("
            SELECT ua.user_id, u.firstname, u.lastname, COUNT(*) as activity_count
            FROM " . DB_PREFIX . "user_activity ua
            LEFT JOIN " . DB_PREFIX . "user u ON (ua.user_id = u.user_id)
            WHERE " . $date_filter . "
            GROUP BY ua.user_id
            ORDER BY activity_count DESC
            LIMIT 10
        ")->rows;

        // الأنشطة حسب الساعة
        $activities_by_hour = $this->db->query("
            SELECT HOUR(created_at) as hour, COUNT(*) as count
            FROM " . DB_PREFIX . "user_activity
            WHERE " . $date_filter . "
            GROUP BY HOUR(created_at)
            ORDER BY hour
        ")->rows;

        // أخطاء العمليات
        $error_activities = $this->db->query("
            SELECT COUNT(*) as error_count
            FROM " . DB_PREFIX . "user_activity
            WHERE " . $date_filter . "
            AND (activity_type LIKE '%_failed' OR description LIKE '%فشل%' OR description LIKE '%خطأ%')
        ")->row['error_count'];

        return array(
            'total_activities' => $total_activities,
            'activities_by_type' => $activities_by_type,
            'most_active_users' => $most_active_users,
            'activities_by_hour' => $activities_by_hour,
            'error_count' => $error_activities,
            'success_rate' => $total_activities > 0 ? round((($total_activities - $error_activities) / $total_activities) * 100, 2) : 100,
            'period_days' => $days
        );
    }

    /**
     * تنظيف سجل الأنشطة القديمة - مبسط
     */
    public function cleanupOldActivities($days = 90) {
        // TODO: تنفيذ لاحقاً
        return 0;
    }

    /**
     * تصدير سجل الأنشطة
     */
    public function exportActivities($filters = array(), $format = 'csv') {
        $activities = $this->searchActivities($filters, 0); // بدون حد

        if ($format === 'csv') {
            return $this->exportToCSV($activities);
        } elseif ($format === 'json') {
            return json_encode($activities, JSON_UNESCAPED_UNICODE);
        }

        return $activities;
    }

    /**
     * تصدير إلى CSV - مبسط
     */
    private function exportToCSV($activities) {
        // TODO: تنفيذ لاحقاً
        return '';
    }
}

/*
 * =================== تحسينات Activity Log للمخزون والتجارة الإلكترونية v10.0 ===================
 *
 * الميزات الجديدة المضافة:
 *
 * 1. تسجيل عمليات WAC:
 *    - حساب WAC للمنتج الواحد والفئات وجميع المنتجات
 *    - تتبع التغييرات في التكلفة
 *    - تسجيل طرق الحساب المختلفة
 *
 * 2. تسجيل مزامنة المخزون:
 *    - مزامنة بين الفروع
 *    - حل التعارضات
 *    - المزامنة الفورية والمجدولة
 *
 * 3. تسجيل معالجة الطلبات:
 *    - جميع أنواع الطلبات (عادية، باقات، اشتراك، مسبقة، متأخرة)
 *    - تطبيق الخصومات والتسعير
 *    - التكامل المحاسبي
 *
 * 4. تسجيل التسعير الديناميكي:
 *    - تحديث مستويات الأسعار
 *    - تسعير العملاء والكمية والوقت
 *    - حماية الهامش
 *
 * 5. تسجيل عمليات الطوابير:
 *    - إضافة وتنفيذ المهام
 *    - حالات النجاح والفشل
 *    - إدارة الأولويات
 *
 * 6. تسجيل إدارة الباقات:
 *    - إنشاء وتحديث الباقات
 *    - إدارة المكونات
 *    - تجميع وفك الباقات
 *
 * 7. تسجيل العمليات المحاسبية:
 *    - القيود التلقائية
 *    - تسويات WAC
 *    - ربط المراجع
 *
 * 8. البحث والتصفية المتقدمة:
 *    - بحث متعدد المعايير
 *    - فلترة حسب النوع والتاريخ والمستخدم
 *    - بحث في الوصف والبيانات الإضافية
 *
 * 9. إحصائيات متقدمة:
 *    - تحليل الأنشطة حسب النوع
 *    - المستخدمين الأكثر نشاطاً
 *    - توزيع الأنشطة حسب الوقت
 *    - معدل نجاح العمليات
 *
 * 10. إدارة البيانات:
 *     - تنظيف السجلات القديمة
 *     - تصدير البيانات (CSV/JSON)
 *     - أرشفة الأنشطة
 *
 * تاريخ التحديث: 2025-07-27
 * المطور: AYM ERP Development Team
 * الإصدار: v10.0 Enhanced
 */
