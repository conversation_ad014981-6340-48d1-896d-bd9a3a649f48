<?php
/**
 * نموذج لوحة التحليلات المالية المتقدمة
 * لوحة معلومات تفاعلية متقدمة مع مؤشرات الأداء الرئيسية والتحليلات المالية
 * تدعم التخصيص الكامل والتحديث في الوقت الفعلي
 */
class ModelAccountsFinancialAnalyticsDashboard extends Model {
    private $central_service;

    public function __construct($registry) {
        parent::__construct($registry);
        
        // تحميل الخدمات المركزية
        $this->load->model('core/central_service_manager');
        $this->central_service = $this->model_core_central_service_manager;
    }

    /**
     * جلب لوحة المعلومات الافتراضية للمستخدم
     */
    public function getUserDefaultDashboard($user_id) {
        $query = $this->db->query("SELECT * FROM cod_financial_analytics_dashboard 
                                   WHERE user_id = '" . (int)$user_id . "' 
                                   AND is_default = '1' 
                                   LIMIT 1");

        return $query->num_rows ? $query->row : null;
    }

    /**
     * إنشاء لوحة معلومات افتراضية
     */
    public function createDefaultDashboard($user_id) {
        $this->db->query("START TRANSACTION");

        try {
            // إنشاء لوحة المعلومات
            $this->db->query("INSERT INTO cod_financial_analytics_dashboard SET 
                dashboard_name = 'لوحة المعلومات الافتراضية',
                user_id = '" . (int)$user_id . "',
                layout_config = '" . $this->db->escape(json_encode($this->getDefaultLayout())) . "',
                refresh_interval = '300',
                is_default = '1',
                is_active = '1',
                date_created = NOW(),
                date_modified = NOW()
            ");

            $dashboard_id = $this->db->getLastId();

            // إضافة الويدجت الافتراضية
            $default_widgets = $this->getDefaultWidgets();
            foreach ($default_widgets as $widget) {
                $widget['dashboard_id'] = $dashboard_id;
                $this->addWidget($widget);
            }

            $this->db->query("COMMIT");

            // جلب لوحة المعلومات المنشأة
            return $this->getDashboard($dashboard_id);

        } catch (Exception $e) {
            $this->db->query("ROLLBACK");
            throw $e;
        }
    }

    /**
     * حفظ لوحة المعلومات
     */
    public function saveDashboard($data) {
        try {
            if (isset($data['dashboard_id']) && $data['dashboard_id']) {
                // تحديث لوحة موجودة
                $this->db->query("UPDATE cod_financial_analytics_dashboard SET 
                    dashboard_name = '" . $this->db->escape($data['dashboard_name']) . "',
                    layout_config = '" . $this->db->escape($data['layout_config']) . "',
                    refresh_interval = '" . (int)$data['refresh_interval'] . "',
                    is_default = '" . (isset($data['is_default']) ? 1 : 0) . "',
                    date_modified = NOW()
                    WHERE dashboard_id = '" . (int)$data['dashboard_id'] . "'
                ");
            } else {
                // إنشاء لوحة جديدة
                $this->db->query("INSERT INTO cod_financial_analytics_dashboard SET 
                    dashboard_name = '" . $this->db->escape($data['dashboard_name']) . "',
                    user_id = '" . (int)$this->user->getId() . "',
                    layout_config = '" . $this->db->escape($data['layout_config']) . "',
                    refresh_interval = '" . (int)$data['refresh_interval'] . "',
                    is_default = '" . (isset($data['is_default']) ? 1 : 0) . "',
                    is_active = '1',
                    date_created = NOW(),
                    date_modified = NOW()
                ");
            }

            return true;

        } catch (Exception $e) {
            throw $e;
        }
    }

    /**
     * إضافة ويدجت جديد
     */
    public function addWidget($data) {
        try {
            $this->db->query("INSERT INTO cod_financial_analytics_widgets SET 
                dashboard_id = '" . (int)$data['dashboard_id'] . "',
                widget_type = '" . $this->db->escape($data['widget_type']) . "',
                widget_title = '" . $this->db->escape($data['widget_title']) . "',
                data_source = '" . $this->db->escape($data['data_source']) . "',
                chart_type = '" . $this->db->escape($data['chart_type']) . "',
                position_x = '" . (int)$data['position_x'] . "',
                position_y = '" . (int)$data['position_y'] . "',
                width = '" . (int)$data['width'] . "',
                height = '" . (int)$data['height'] . "',
                config = '" . $this->db->escape(json_encode($data['config'])) . "',
                filters = '" . $this->db->escape(json_encode($data['filters'])) . "',
                is_active = '1',
                date_created = NOW()
            ");

            return $this->db->getLastId();

        } catch (Exception $e) {
            throw $e;
        }
    }

    /**
     * تحديث ويدجت
     */
    public function updateWidget($data) {
        try {
            $this->db->query("UPDATE cod_financial_analytics_widgets SET 
                widget_title = '" . $this->db->escape($data['widget_title']) . "',
                position_x = '" . (int)$data['position_x'] . "',
                position_y = '" . (int)$data['position_y'] . "',
                width = '" . (int)$data['width'] . "',
                height = '" . (int)$data['height'] . "',
                config = '" . $this->db->escape(json_encode($data['config'])) . "',
                filters = '" . $this->db->escape(json_encode($data['filters'])) . "',
                date_modified = NOW()
                WHERE widget_id = '" . (int)$data['widget_id'] . "'
            ");

            return true;

        } catch (Exception $e) {
            throw $e;
        }
    }

    /**
     * حذف ويدجت
     */
    public function deleteWidget($widget_id) {
        try {
            $this->db->query("DELETE FROM cod_financial_analytics_widgets WHERE widget_id = '" . (int)$widget_id . "'");
            return true;

        } catch (Exception $e) {
            throw $e;
        }
    }

    /**
     * جلب ويدجت محدد
     */
    public function getWidget($widget_id) {
        $query = $this->db->query("SELECT * FROM cod_financial_analytics_widgets WHERE widget_id = '" . (int)$widget_id . "'");
        return $query->num_rows ? $query->row : null;
    }

    /**
     * جلب ويدجت لوحة المعلومات
     */
    public function getDashboardWidgets($dashboard_id) {
        $query = $this->db->query("SELECT * FROM cod_financial_analytics_widgets 
                                   WHERE dashboard_id = '" . (int)$dashboard_id . "' 
                                   AND is_active = '1'
                                   ORDER BY position_y ASC, position_x ASC");

        return $query->rows;
    }

    /**
     * جلب مؤشرات الأداء الرئيسية
     */
    public function getKPIs() {
        $kpis = array();

        // إجمالي الإيرادات للشهر الحالي
        $current_month = date('Y-m');
        $query = $this->db->query("SELECT SUM(jel.credit - jel.debit) as total_revenue
                                   FROM cod_journal_entry_line jel
                                   INNER JOIN cod_journal_entry je ON (jel.entry_id = je.entry_id)
                                   INNER JOIN cod_accounts a ON (jel.account_id = a.account_id)
                                   WHERE a.account_type = 'revenue'
                                   AND DATE_FORMAT(je.journal_date, '%Y-%m') = '" . $current_month . "'
                                   AND je.status = 'posted'
        ");
        $kpis['monthly_revenue'] = $query->row['total_revenue'] ?? 0;

        // إجمالي المصروفات للشهر الحالي
        $query = $this->db->query("SELECT SUM(jel.debit - jel.credit) as total_expenses
                                   FROM cod_journal_entry_line jel
                                   INNER JOIN cod_journal_entry je ON (jel.entry_id = je.entry_id)
                                   INNER JOIN cod_accounts a ON (jel.account_id = a.account_id)
                                   WHERE a.account_type = 'expense'
                                   AND DATE_FORMAT(je.journal_date, '%Y-%m') = '" . $current_month . "'
                                   AND je.status = 'posted'
        ");
        $kpis['monthly_expenses'] = $query->row['total_expenses'] ?? 0;

        // صافي الربح للشهر الحالي
        $kpis['monthly_profit'] = $kpis['monthly_revenue'] - $kpis['monthly_expenses'];

        // هامش الربح
        $kpis['profit_margin'] = $kpis['monthly_revenue'] > 0 ? 
            ($kpis['monthly_profit'] / $kpis['monthly_revenue']) * 100 : 0;

        // إجمالي الأصول
        $query = $this->db->query("SELECT SUM(jel.debit - jel.credit) as total_assets
                                   FROM cod_journal_entry_line jel
                                   INNER JOIN cod_accounts a ON (jel.account_id = a.account_id)
                                   WHERE a.account_type = 'asset'
        ");
        $kpis['total_assets'] = $query->row['total_assets'] ?? 0;

        // إجمالي الخصوم
        $query = $this->db->query("SELECT SUM(jel.credit - jel.debit) as total_liabilities
                                   FROM cod_journal_entry_line jel
                                   INNER JOIN cod_accounts a ON (jel.account_id = a.account_id)
                                   WHERE a.account_type = 'liability'
        ");
        $kpis['total_liabilities'] = $query->row['total_liabilities'] ?? 0;

        // حقوق الملكية
        $kpis['equity'] = $kpis['total_assets'] - $kpis['total_liabilities'];

        // نسبة الدين إلى حقوق الملكية
        $kpis['debt_to_equity'] = $kpis['equity'] > 0 ? 
            ($kpis['total_liabilities'] / $kpis['equity']) : 0;

        // الرصيد النقدي الحالي
        $query = $this->db->query("SELECT SUM(jel.debit - jel.credit) as cash_balance
                                   FROM cod_journal_entry_line jel
                                   INNER JOIN cod_accounts a ON (jel.account_id = a.account_id)
                                   WHERE a.account_type = 'asset' 
                                   AND a.sub_type = 'cash'
        ");
        $kpis['cash_balance'] = $query->row['cash_balance'] ?? 0;

        return $kpis;
    }

    /**
     * جلب التحليلات السريعة
     */
    public function getQuickInsights() {
        $insights = array();

        // مقارنة الأداء مع الشهر السابق
        $current_month = date('Y-m');
        $previous_month = date('Y-m', strtotime('-1 month'));

        // نمو الإيرادات
        $current_revenue = $this->getMonthlyRevenue($current_month);
        $previous_revenue = $this->getMonthlyRevenue($previous_month);
        
        $revenue_growth = $previous_revenue > 0 ? 
            (($current_revenue - $previous_revenue) / $previous_revenue) * 100 : 0;

        $insights[] = array(
            'type' => 'revenue_growth',
            'title' => 'نمو الإيرادات',
            'value' => $revenue_growth,
            'trend' => $revenue_growth > 0 ? 'up' : 'down',
            'description' => sprintf('نمو الإيرادات بنسبة %.2f%% مقارنة بالشهر السابق', $revenue_growth)
        );

        // تحليل التدفق النقدي
        $cash_flow_trend = $this->getCashFlowTrend();
        $insights[] = array(
            'type' => 'cash_flow',
            'title' => 'اتجاه التدفق النقدي',
            'value' => $cash_flow_trend['trend'],
            'trend' => $cash_flow_trend['direction'],
            'description' => $cash_flow_trend['description']
        );

        // تحليل المصروفات
        $expense_analysis = $this->getExpenseAnalysis();
        $insights[] = array(
            'type' => 'expense_control',
            'title' => 'تحكم في المصروفات',
            'value' => $expense_analysis['control_score'],
            'trend' => $expense_analysis['trend'],
            'description' => $expense_analysis['description']
        );

        return $insights;
    }

    /**
     * جلب التنبيهات المالية
     */
    public function getFinancialAlerts() {
        $alerts = array();

        // تنبيه انخفاض الرصيد النقدي
        $cash_balance = $this->getCashBalance();
        $min_cash_threshold = $this->config->get('min_cash_threshold') ?? 10000;
        
        if ($cash_balance < $min_cash_threshold) {
            $alerts[] = array(
                'type' => 'warning',
                'title' => 'انخفاض الرصيد النقدي',
                'message' => sprintf('الرصيد النقدي الحالي %.2f أقل من الحد الأدنى المطلوب %.2f', 
                    $cash_balance, $min_cash_threshold),
                'priority' => 'high'
            );
        }

        // تنبيه تجاوز الموازنة
        $budget_variance = $this->getBudgetVarianceAlert();
        if ($budget_variance['exceeded']) {
            $alerts[] = array(
                'type' => 'danger',
                'title' => 'تجاوز الموازنة',
                'message' => sprintf('تم تجاوز الموازنة بنسبة %.2f%% في قسم %s', 
                    $budget_variance['percentage'], $budget_variance['department']),
                'priority' => 'critical'
            );
        }

        // تنبيه الذمم المدينة المتأخرة
        $overdue_receivables = $this->getOverdueReceivables();
        if ($overdue_receivables['count'] > 0) {
            $alerts[] = array(
                'type' => 'info',
                'title' => 'ذمم مدينة متأخرة',
                'message' => sprintf('يوجد %d فاتورة متأخرة بإجمالي %.2f', 
                    $overdue_receivables['count'], $overdue_receivables['amount']),
                'priority' => 'medium'
            );
        }

        return $alerts;
    }

    /**
     * تحليل الإيرادات
     */
    public function getRevenueAnalysis($widget) {
        $filters = json_decode($widget['filters'], true);
        $period = $filters['period'] ?? 'monthly';
        
        $sql = "SELECT 
                    DATE_FORMAT(je.journal_date, '" . $this->getPeriodFormat($period) . "') as period,
                    SUM(jel.credit - jel.debit) as revenue
                FROM cod_journal_entry_line jel
                INNER JOIN cod_journal_entry je ON (jel.entry_id = je.entry_id)
                INNER JOIN cod_accounts a ON (jel.account_id = a.account_id)
                WHERE a.account_type = 'revenue'
                AND je.status = 'posted'";

        if (isset($filters['date_from'])) {
            $sql .= " AND je.journal_date >= '" . $this->db->escape($filters['date_from']) . "'";
        }

        if (isset($filters['date_to'])) {
            $sql .= " AND je.journal_date <= '" . $this->db->escape($filters['date_to']) . "'";
        }

        $sql .= " GROUP BY period ORDER BY period ASC";

        $query = $this->db->query($sql);
        return $query->rows;
    }

    /**
     * تحليل المصروفات
     */
    public function getExpenseAnalysis($widget = null) {
        if ($widget) {
            $filters = json_decode($widget['filters'], true);
            $period = $filters['period'] ?? 'monthly';
            
            $sql = "SELECT 
                        DATE_FORMAT(je.journal_date, '" . $this->getPeriodFormat($period) . "') as period,
                        SUM(jel.debit - jel.credit) as expense
                    FROM cod_journal_entry_line jel
                    INNER JOIN cod_journal_entry je ON (jel.entry_id = je.entry_id)
                    INNER JOIN cod_accounts a ON (jel.account_id = a.account_id)
                    WHERE a.account_type = 'expense'
                    AND je.status = 'posted'";

            if (isset($filters['date_from'])) {
                $sql .= " AND je.journal_date >= '" . $this->db->escape($filters['date_from']) . "'";
            }

            if (isset($filters['date_to'])) {
                $sql .= " AND je.journal_date <= '" . $this->db->escape($filters['date_to']) . "'";
            }

            $sql .= " GROUP BY period ORDER BY period ASC";

            $query = $this->db->query($sql);
            return $query->rows;
        } else {
            // تحليل عام للمصروفات
            $current_month = date('Y-m');
            $previous_month = date('Y-m', strtotime('-1 month'));

            $current_expenses = $this->getMonthlyExpenses($current_month);
            $previous_expenses = $this->getMonthlyExpenses($previous_month);

            $expense_change = $previous_expenses > 0 ? 
                (($current_expenses - $previous_expenses) / $previous_expenses) * 100 : 0;

            $control_score = 100 - abs($expense_change);
            
            return array(
                'control_score' => $control_score,
                'trend' => $expense_change < 0 ? 'down' : 'up',
                'description' => sprintf('المصروفات %s بنسبة %.2f%% مقارنة بالشهر السابق', 
                    $expense_change < 0 ? 'انخفضت' : 'ارتفعت', abs($expense_change))
            );
        }
    }

    /**
     * دوال مساعدة
     */
    private function getDefaultLayout() {
        return array(
            'columns' => 12,
            'row_height' => 150,
            'margin' => 10
        );
    }

    private function getDefaultWidgets() {
        return array(
            array(
                'widget_type' => 'chart',
                'widget_title' => 'تحليل الإيرادات',
                'data_source' => 'revenue_analysis',
                'chart_type' => 'line',
                'position_x' => 0,
                'position_y' => 0,
                'width' => 6,
                'height' => 2,
                'config' => array('show_legend' => true),
                'filters' => array('period' => 'monthly')
            ),
            array(
                'widget_type' => 'chart',
                'widget_title' => 'تحليل المصروفات',
                'data_source' => 'expense_analysis',
                'chart_type' => 'bar',
                'position_x' => 6,
                'position_y' => 0,
                'width' => 6,
                'height' => 2,
                'config' => array('show_legend' => true),
                'filters' => array('period' => 'monthly')
            ),
            array(
                'widget_type' => 'kpi',
                'widget_title' => 'مؤشرات الأداء',
                'data_source' => 'kpi_metrics',
                'chart_type' => 'metric',
                'position_x' => 0,
                'position_y' => 2,
                'width' => 12,
                'height' => 1,
                'config' => array('layout' => 'horizontal'),
                'filters' => array()
            )
        );
    }

    private function getPeriodFormat($period) {
        switch ($period) {
            case 'daily':
                return '%Y-%m-%d';
            case 'weekly':
                return '%Y-%u';
            case 'monthly':
                return '%Y-%m';
            case 'quarterly':
                return '%Y-Q%q';
            case 'yearly':
                return '%Y';
            default:
                return '%Y-%m';
        }
    }

    private function getMonthlyRevenue($month) {
        $query = $this->db->query("SELECT SUM(jel.credit - jel.debit) as revenue
                                   FROM cod_journal_entry_line jel
                                   INNER JOIN cod_journal_entry je ON (jel.entry_id = je.entry_id)
                                   INNER JOIN cod_accounts a ON (jel.account_id = a.account_id)
                                   WHERE a.account_type = 'revenue'
                                   AND DATE_FORMAT(je.journal_date, '%Y-%m') = '" . $month . "'
                                   AND je.status = 'posted'
        ");

        return $query->row['revenue'] ?? 0;
    }

    private function getMonthlyExpenses($month) {
        $query = $this->db->query("SELECT SUM(jel.debit - jel.credit) as expenses
                                   FROM cod_journal_entry_line jel
                                   INNER JOIN cod_journal_entry je ON (jel.entry_id = je.entry_id)
                                   INNER JOIN cod_accounts a ON (jel.account_id = a.account_id)
                                   WHERE a.account_type = 'expense'
                                   AND DATE_FORMAT(je.journal_date, '%Y-%m') = '" . $month . "'
                                   AND je.status = 'posted'
        ");

        return $query->row['expenses'] ?? 0;
    }

    private function getCashBalance() {
        $query = $this->db->query("SELECT SUM(jel.debit - jel.credit) as balance
                                   FROM cod_journal_entry_line jel
                                   INNER JOIN cod_accounts a ON (jel.account_id = a.account_id)
                                   WHERE a.account_type = 'asset' 
                                   AND a.sub_type = 'cash'
        ");

        return $query->row['balance'] ?? 0;
    }

    private function getCashFlowTrend() {
        // تحليل اتجاه التدفق النقدي للأشهر الثلاثة الماضية
        $months = array();
        for ($i = 2; $i >= 0; $i--) {
            $months[] = date('Y-m', strtotime('-' . $i . ' months'));
        }

        $cash_flows = array();
        foreach ($months as $month) {
            $revenue = $this->getMonthlyRevenue($month);
            $expenses = $this->getMonthlyExpenses($month);
            $cash_flows[] = $revenue - $expenses;
        }

        // حساب الاتجاه
        $trend_value = 0;
        if (count($cash_flows) >= 2) {
            $trend_value = $cash_flows[2] - $cash_flows[0]; // مقارنة الشهر الحالي بالشهر قبل الماضي
        }

        return array(
            'trend' => $trend_value,
            'direction' => $trend_value > 0 ? 'up' : 'down',
            'description' => sprintf('التدفق النقدي %s بمقدار %.2f خلال الأشهر الثلاثة الماضية', 
                $trend_value > 0 ? 'تحسن' : 'انخفض', abs($trend_value))
        );
    }

    private function getBudgetVarianceAlert() {
        // فحص تجاوز الموازنة - نسخة مبسطة
        return array('exceeded' => false);
    }

    private function getOverdueReceivables() {
        // فحص الذمم المدينة المتأخرة - نسخة مبسطة
        return array('count' => 0, 'amount' => 0);
    }

    // باقي الدوال للويدجت المختلفة
    public function getProfitLossData($widget) { return array(); }
    public function getCashFlowData($widget) { return array(); }
    public function getBalanceSheetData($widget) { return array(); }
    public function getKPIMetrics($widget) { return $this->getKPIs(); }
    public function getBudgetVariance($widget) { return array(); }
    public function getAccountsReceivable($widget) { return array(); }
    public function getAccountsPayable($widget) { return array(); }
    public function getInventoryAnalysis($widget) { return array(); }
    public function getDashboard($dashboard_id) { 
        $query = $this->db->query("SELECT * FROM cod_financial_analytics_dashboard WHERE dashboard_id = '" . (int)$dashboard_id . "'");
        return $query->num_rows ? $query->row : null;
    }
    public function exportDashboard($dashboard_id, $format) { return array('download_url' => '#'); }
}
