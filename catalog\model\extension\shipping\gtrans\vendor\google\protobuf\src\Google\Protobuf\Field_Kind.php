<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/protobuf/type.proto

namespace Google\Protobuf;

if (false) {
    /**
     * This class is deprecated. Use Google\Protobuf\Field\Kind instead.
     * @deprecated
     */
    class Field_Kind {}
}
class_exists(Field\Kind::class);
@trigger_error('Google\Protobuf\Field_Kind is deprecated and will be removed in the next major release. Use Google\Protobuf\Field\Kind instead', E_USER_DEPRECATED);

