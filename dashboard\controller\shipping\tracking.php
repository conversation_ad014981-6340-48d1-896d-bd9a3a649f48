<?php
class ControllerShippingTracking extends Controller {
    public function index() {
        $this->load->language('shipping/tracking');
        $this->document->setTitle($this->language->get('heading_title'));
        
        $this->load->model('shipping/tracking');
        
        $data['breadcrumbs'] = array();
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_shipping'),
            'href' => $this->url->link('shipping/shipping', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('shipping/tracking', 'user_token=' . $this->session->data['user_token'], true)
        );
        
        $data['user_token'] = $this->session->data['user_token'];
        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');
        
        $this->response->setOutput($this->load->view('shipping/tracking', $data));
    }
    
    public function getShipments() {
        $this->load->language('shipping/tracking');
        $this->load->model('shipping/tracking');
        
        $data['shipments'] = array();
        
        $filter_data = array(
            'filter_tracking_number' => isset($this->request->get['filter_tracking_number']) ? $this->request->get['filter_tracking_number'] : '',
            'filter_carrier' => isset($this->request->get['filter_carrier']) ? $this->request->get['filter_carrier'] : '',
            'filter_status' => isset($this->request->get['filter_status']) ? $this->request->get['filter_status'] : '',
            'filter_date_added' => isset($this->request->get['filter_date_added']) ? $this->request->get['filter_date_added'] : '',
            'start' => isset($this->request->get['start']) ? $this->request->get['start'] : 0,
            'limit' => isset($this->request->get['limit']) ? $this->request->get['limit'] : 20
        );
        
        $results = $this->model_shipping_tracking->getShipments($filter_data);
        
        foreach ($results as $result) {
            $data['shipments'][] = array(
                'shipment_id' => $result['shipment_id'],
                'tracking_number' => $result['tracking_number'],
                'carrier_name' => $result['carrier_name'],
                'order_id' => $result['order_id'],
                'customer_name' => $result['customer_name'],
                'status' => $result['status'],
                'status_text' => $this->getStatusText($result['status']),
                'date_added' => date($this->language->get('date_format_short'), strtotime($result['date_added'])),
                'last_update' => $result['last_update'] ? date($this->language->get('date_format_short'), strtotime($result['last_update'])) : '-',
                'view' => $this->url->link('shipping/tracking/view', 'user_token=' . $this->session->data['user_token'] . '&shipment_id=' . $result['shipment_id'], true),
                'update' => $this->url->link('shipping/tracking/update', 'user_token=' . $this->session->data['user_token'] . '&shipment_id=' . $result['shipment_id'], true)
            );
        }
        
        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($data));
    }
    
    public function view() {
        $this->load->language('shipping/tracking');
        $this->load->model('shipping/tracking');
        
        if (isset($this->request->get['shipment_id'])) {
            $shipment_id = $this->request->get['shipment_id'];
        } else {
            $shipment_id = 0;
        }
        
        $shipment_info = $this->model_shipping_tracking->getShipment($shipment_id);
        
        if ($shipment_info) {
            $data['shipment'] = array(
                'shipment_id' => $shipment_info['shipment_id'],
                'tracking_number' => $shipment_info['tracking_number'],
                'carrier_name' => $shipment_info['carrier_name'],
                'carrier_code' => $shipment_info['carrier_code'],
                'order_id' => $shipment_info['order_id'],
                'customer_name' => $shipment_info['customer_name'],
                'customer_email' => $shipment_info['customer_email'],
                'customer_telephone' => $shipment_info['customer_telephone'],
                'shipping_address' => $shipment_info['shipping_address'],
                'status' => $shipment_info['status'],
                'status_text' => $this->getStatusText($shipment_info['status']),
                'date_added' => date($this->language->get('date_format_short'), strtotime($shipment_info['date_added'])),
                'estimated_delivery' => $shipment_info['estimated_delivery'] ? date($this->language->get('date_format_short'), strtotime($shipment_info['estimated_delivery'])) : '-',
                'actual_delivery' => $shipment_info['actual_delivery'] ? date($this->language->get('date_format_short'), strtotime($shipment_info['actual_delivery'])) : '-'
            );
            
            // الحصول على تفاصيل التتبع
            $data['tracking_details'] = $this->model_shipping_tracking->getTrackingDetails($shipment_id);
            
            $data['breadcrumbs'] = array();
            $data['breadcrumbs'][] = array(
                'text' => $this->language->get('text_home'),
                'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
            );
            $data['breadcrumbs'][] = array(
                'text' => $this->language->get('text_shipping'),
                'href' => $this->url->link('shipping/shipping', 'user_token=' . $this->session->data['user_token'], true)
            );
            $data['breadcrumbs'][] = array(
                'text' => $this->language->get('heading_title'),
                'href' => $this->url->link('shipping/tracking', 'user_token=' . $this->session->data['user_token'], true)
            );
            $data['breadcrumbs'][] = array(
                'text' => $this->language->get('text_view_shipment'),
                'href' => $this->url->link('shipping/tracking/view', 'user_token=' . $this->session->data['user_token'] . '&shipment_id=' . $shipment_id, true)
            );
            
            $data['user_token'] = $this->session->data['user_token'];
            $data['header'] = $this->load->controller('common/header');
            $data['column_left'] = $this->load->controller('common/column_left');
            $data['footer'] = $this->load->controller('common/footer');
            
            $this->response->setOutput($this->load->view('shipping/tracking_view', $data));
        } else {
            $this->response->redirect($this->url->link('shipping/tracking', 'user_token=' . $this->session->data['user_token'], true));
        }
    }
    
    public function update() {
        $this->load->language('shipping/tracking');
        $this->load->model('shipping/tracking');
        
        $json = array();
        
        if (isset($this->request->get['shipment_id'])) {
            $shipment_id = $this->request->get['shipment_id'];
        } else {
            $shipment_id = 0;
        }
        
        if ($this->user->hasPermission('modify', 'shipping/tracking')) {
            // تحديث حالة الشحنة من شركة الشحن
            $result = $this->model_shipping_tracking->updateShipmentStatus($shipment_id);
            
            if ($result) {
                $json['success'] = $this->language->get('text_success_update');
                $json['shipment'] = $this->model_shipping_tracking->getShipment($shipment_id);
            } else {
                $json['error'] = $this->language->get('error_update_failed');
            }
        } else {
            $json['error'] = $this->language->get('error_permission');
        }
        
        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }
    
    public function bulkUpdate() {
        $this->load->language('shipping/tracking');
        $this->load->model('shipping/tracking');
        
        $json = array();
        
        if (isset($this->request->post['selected']) && $this->user->hasPermission('modify', 'shipping/tracking')) {
            $updated = 0;
            $failed = 0;
            
            foreach ($this->request->post['selected'] as $shipment_id) {
                if ($this->model_shipping_tracking->updateShipmentStatus($shipment_id)) {
                    $updated++;
                } else {
                    $failed++;
                }
            }
            
            $json['success'] = sprintf($this->language->get('text_bulk_update_success'), $updated, $failed);
        } else {
            $json['error'] = $this->language->get('error_permission');
        }
        
        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }
    
    public function export() {
        $this->load->language('shipping/tracking');
        $this->load->model('shipping/tracking');
        
        $filter_data = array(
            'filter_tracking_number' => isset($this->request->get['filter_tracking_number']) ? $this->request->get['filter_tracking_number'] : '',
            'filter_carrier' => isset($this->request->get['filter_carrier']) ? $this->request->get['filter_carrier'] : '',
            'filter_status' => isset($this->request->get['filter_status']) ? $this->request->get['filter_status'] : '',
            'filter_date_added' => isset($this->request->get['filter_date_added']) ? $this->request->get['filter_date_added'] : ''
        );
        
        $results = $this->model_shipping_tracking->getShipments($filter_data);
        
        $filename = 'shipment_tracking_' . date('Y-m-d_H-i-s') . '.csv';
        
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        $output = fopen('php://output', 'w');
        
        // Header
        fputcsv($output, array(
            'Shipment ID',
            'Tracking Number',
            'Carrier',
            'Order ID',
            'Customer Name',
            'Status',
            'Date Added',
            'Last Update',
            'Estimated Delivery',
            'Actual Delivery'
        ));
        
        // Data
        foreach ($results as $result) {
            fputcsv($output, array(
                $result['shipment_id'],
                $result['tracking_number'],
                $result['carrier_name'],
                $result['order_id'],
                $result['customer_name'],
                $this->getStatusText($result['status']),
                date($this->language->get('date_format_short'), strtotime($result['date_added'])),
                $result['last_update'] ? date($this->language->get('date_format_short'), strtotime($result['last_update'])) : '-',
                $result['estimated_delivery'] ? date($this->language->get('date_format_short'), strtotime($result['estimated_delivery'])) : '-',
                $result['actual_delivery'] ? date($this->language->get('date_format_short'), strtotime($result['actual_delivery'])) : '-'
            ));
        }
        
        fclose($output);
        exit();
    }
    
    private function getStatusText($status) {
        switch ($status) {
            case 'pending':
                return $this->language->get('text_status_pending');
            case 'in_transit':
                return $this->language->get('text_status_in_transit');
            case 'out_for_delivery':
                return $this->language->get('text_status_out_for_delivery');
            case 'delivered':
                return $this->language->get('text_status_delivered');
            case 'failed':
                return $this->language->get('text_status_failed');
            case 'returned':
                return $this->language->get('text_status_returned');
            default:
                return $status;
        }
    }
} 