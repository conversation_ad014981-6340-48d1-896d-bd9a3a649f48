<div class="row">
  <div id="filter-report" class="col-md-3 col-md-push-9 col-sm-12 hidden-sm hidden-xs">
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-filter"></i> {{ text_filter }}</h3>
      </div>
      <div class="panel-body">
        <div class="form-group">
          <label class="control-label" for="input-date-start">{{ entry_date_start }}</label>
          <div class="input-group date">
            <input type="text" name="filter_date_start" value="{{ filter_date_start }}" placeholder="{{ entry_date_start }}" data-date-format="YYYY-MM-DD" id="input-date-start" class="form-control" />
            <span class="input-group-btn">
            <button type="button" class="btn btn-default"><i class="fa fa-calendar"></i></button>
            </span></div>
        </div>
        <div class="form-group">
          <label class="control-label" for="input-customer">{{ entry_customer }}</label>
          <input type="text" name="filter_customer" value="{{ filter_customer }}" placeholder="{{ entry_customer }}" id="input-customer" class="form-control" />
        </div>
        <div class="form-group">
          <label class="control-label" for="input-date-end">{{ entry_date_end }}</label>
          <div class="input-group date">
            <input type="text" name="filter_date_end" value="{{ filter_date_end }}" placeholder="{{ entry_date_end }}" data-date-format="YYYY-MM-DD" id="input-date-end" class="form-control" />
            <span class="input-group-btn">
            <button type="button" class="btn btn-default"><i class="fa fa-calendar"></i></button>
            </span></div>
        </div>
        <div class="form-group">
          <label class="control-label" for="input-ip">{{ entry_ip }}</label>
          <input type="text" name="filter_ip" value="{{ filter_ip }}" placeholder="{{ entry_ip }}" id="input-ip" class="form-control" />
        </div>
        <div class="form-group">
          <label class="control-label" for="input-keyword">{{ entry_keyword }}</label>
          <input type="text" name="filter_keyword" value="{{ filter_keyword }}" placeholder="{{ entry_keyword }}" id="input-keyword" class="form-control" />
        </div>
        <div class="form-group text-right">
          <button type="button" id="button-filter" class="btn btn-default"><i class="fa fa-filter"></i> {{ button_filter }}</button>
        </div>
      </div>
    </div>
  </div>
  <div class="col-md-9 col-md-pull-3 col-sm-12">
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-bar-chart"></i> {{ heading_title }}</h3>
      </div>
      <div class="panel-body">
        <div class="table-responsive">
          <table class="table table-bordered">
            <thead>
              <tr>
                <td class="text-left">{{ column_keyword }}</td>
                <td class="text-left">{{ column_products }}</td>
                <td class="text-left">{{ column_category }}</td>
                <td class="text-left">{{ column_customer }}</td>
                <td class="text-left">{{ column_ip }}</td>
                <td class="text-left">{{ column_date_added }}</td>
              </tr>
            </thead>
            <tbody>
            
            {% if searches %}
            {% for search in searches %}
            <tr>
              <td class="text-left">{{ search.keyword }}</td>
              <td class="text-left">{{ search.products }}</td>
              <td class="text-left">{{ search.category }}</td>
              <td class="text-left">{{ search.customer }}</td>
              <td class="text-left">{{ search.ip }}</td>
              <td class="text-left">{{ search.date_added }}</td>
            </tr>
            {% endfor %}
            {% else %}
            <tr>
              <td class="text-center" colspan="6">{{ text_no_results }}</td>
            </tr>
            {% endif %}
            </tbody>
            
          </table>
        </div>
        <div class="row">
          <div class="col-sm-6 text-left">{{ pagination }}</div>
          <div class="col-sm-6 text-right">{{ results }}</div>
        </div>
      </div>
    </div>
  </div>
</div>
<script type="text/javascript"><!--
$('#button-filter').on('click', function() {
	var url = '';
	
	var filter_date_start = $('input[name=\'filter_date_start\']').val();
	
	if (filter_date_start) {
		url += '&filter_date_start=' + encodeURIComponent(filter_date_start);
	}
	
	var filter_date_end = $('input[name=\'filter_date_end\']').val();
	
	if (filter_date_end) {
		url += '&filter_date_end=' + encodeURIComponent(filter_date_end);
	}
	
	var filter_keyword = $('input[name=\'filter_keyword\']').val();
	
	if (filter_keyword) {
		url += '&filter_keyword=' + encodeURIComponent(filter_keyword);
	}
	
	var filter_customer = $('input[name=\'filter_customer\']').val();
	
	if (filter_customer) {
		url += '&filter_customer=' + encodeURIComponent(filter_customer);
	}
	
	var filter_ip = $('input[name=\'filter_ip\']').val();
	
	if (filter_ip) {
		url += '&filter_ip=' + encodeURIComponent(filter_ip);
	}
	
	location = 'index.php?route=report/report&code=customer_search&user_token={{ user_token }}' + url;
});
//--></script> 
<script type="text/javascript"><!--
$('.date').datetimepicker({
	language: '{{ datepicker }}',
	pickTime: false
});
//--></script> 
<script type="text/javascript"><!--
$('input[name=\'filter_customer\']').autocomplete({
	'source': function(request, response) {
		$.ajax({
			url: 'index.php?route=customer/customer/autocomplete&user_token={{ user_token }}&filter_name=' +  encodeURIComponent(request),
			dataType: 'json',
			success: function(json) {
				response($.map(json, function(item) {
					return {
						label: item['name'],
						value: item['customer_id']
					}
				}));
			}
		});
	},
	'select': function(item) {
		$('input[name=\'filter_customer\']').val(item['label']);
	}
});
//--></script>