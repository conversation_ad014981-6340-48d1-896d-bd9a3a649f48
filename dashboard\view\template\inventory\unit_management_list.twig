{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <a href="{{ add }}" data-toggle="tooltip" title="{{ button_add }}" class="btn btn-primary">
          <i class="fa fa-plus"></i>
        </a>
        <a href="{{ create_defaults }}" data-toggle="tooltip" title="{{ button_create_defaults }}" class="btn btn-success">
          <i class="fa fa-magic"></i>
        </a>
        <a href="{{ usage_report }}" data-toggle="tooltip" title="{{ button_usage_report }}" class="btn btn-info">
          <i class="fa fa-bar-chart"></i>
        </a>
        <a href="{{ conversion_calculator }}" data-toggle="tooltip" title="{{ button_conversion_calculator }}" class="btn btn-warning">
          <i class="fa fa-calculator"></i>
        </a>
        <button type="button" data-toggle="tooltip" title="{{ button_delete }}" class="btn btn-danger" onclick="confirm('{{ text_confirm }}') ? $('#form-unit').submit() : false;">
          <i class="fa fa-trash-o"></i>
        </button>
        <a href="{{ refresh }}" data-toggle="tooltip" title="{{ button_refresh }}" class="btn btn-default">
          <i class="fa fa-refresh"></i>
        </a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible">
      <i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    
    {% if success %}
    <div class="alert alert-success alert-dismissible">
      <i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    
    <!-- إحصائيات الوحدات -->
    <div class="row">
      <div class="col-lg-2 col-md-3 col-sm-6">
        <div class="panel panel-primary">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-balance-scale fa-2x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ statistics.total_units }}</div>
                <div>{{ text_total_units }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-lg-2 col-md-3 col-sm-6">
        <div class="panel panel-success">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-check-circle fa-2x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ statistics.active_units }}</div>
                <div>{{ text_active_units }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-lg-2 col-md-3 col-sm-6">
        <div class="panel panel-info">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-star fa-2x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ statistics.base_units }}</div>
                <div>{{ text_base_units }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-lg-2 col-md-3 col-sm-6">
        <div class="panel panel-warning">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-tags fa-2x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ statistics.unit_types }}</div>
                <div>{{ text_unit_types }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-lg-2 col-md-3 col-sm-6">
        <div class="panel panel-green">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-cubes fa-2x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ statistics.products_with_units }}</div>
                <div>{{ text_products_with_units }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-lg-2 col-md-3 col-sm-6">
        <div class="panel panel-purple">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-trophy fa-2x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge-text">{{ statistics.most_used_unit }}</div>
                <div>{{ text_most_used_unit }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- الفلاتر المتقدمة -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title">
          <i class="fa fa-filter"></i> فلاتر البحث المتقدم
          <button type="button" class="btn btn-xs btn-default pull-right" data-toggle="collapse" data-target="#filter-panel">
            <i class="fa fa-chevron-down"></i>
          </button>
        </h3>
      </div>
      <div id="filter-panel" class="panel-collapse collapse">
        <div class="panel-body">
          <form method="get" id="filter-form">
            <input type="hidden" name="route" value="inventory/unit_management" />
            <input type="hidden" name="user_token" value="{{ user_token }}" />
            
            <div class="row">
              <div class="col-md-3">
                <div class="form-group">
                  <label for="filter_name">{{ entry_filter_name }}</label>
                  <input type="text" name="filter_name" value="{{ filter_name }}" placeholder="{{ entry_filter_name }}" id="filter_name" class="form-control" />
                </div>
              </div>
              
              <div class="col-md-3">
                <div class="form-group">
                  <label for="filter_symbol">{{ entry_filter_symbol }}</label>
                  <input type="text" name="filter_symbol" value="{{ filter_symbol }}" placeholder="{{ entry_filter_symbol }}" id="filter_symbol" class="form-control" />
                </div>
              </div>
              
              <div class="col-md-3">
                <div class="form-group">
                  <label for="filter_unit_type">{{ entry_filter_unit_type }}</label>
                  <select name="filter_unit_type" id="filter_unit_type" class="form-control">
                    <option value="">{{ text_all }}</option>
                    {% for type_key, type_name in unit_types %}
                    <option value="{{ type_key }}"{% if type_key == filter_unit_type %} selected="selected"{% endif %}>{{ type_name }}</option>
                    {% endfor %}
                  </select>
                </div>
              </div>
              
              <div class="col-md-3">
                <div class="form-group">
                  <label for="filter_base_unit_id">{{ entry_filter_base_unit }}</label>
                  <select name="filter_base_unit_id" id="filter_base_unit_id" class="form-control">
                    <option value="">{{ text_all }}</option>
                    {% for base_unit in base_units %}
                    <option value="{{ base_unit.unit_id }}"{% if base_unit.unit_id == filter_base_unit_id %} selected="selected"{% endif %}>{{ base_unit.name }} ({{ base_unit.symbol }})</option>
                    {% endfor %}
                  </select>
                </div>
              </div>
            </div>
            
            <div class="row">
              <div class="col-md-3">
                <div class="form-group">
                  <label for="filter_is_base_unit">{{ entry_filter_is_base_unit }}</label>
                  <select name="filter_is_base_unit" id="filter_is_base_unit" class="form-control">
                    {% for option in base_unit_options %}
                    <option value="{{ option.value }}"{% if option.value == filter_is_base_unit %} selected="selected"{% endif %}>{{ option.text }}</option>
                    {% endfor %}
                  </select>
                </div>
              </div>
              
              <div class="col-md-3">
                <div class="form-group">
                  <label for="filter_is_active">{{ entry_filter_is_active }}</label>
                  <select name="filter_is_active" id="filter_is_active" class="form-control">
                    {% for option in status_options %}
                    <option value="{{ option.value }}"{% if option.value == filter_is_active %} selected="selected"{% endif %}>{{ option.text }}</option>
                    {% endfor %}
                  </select>
                </div>
              </div>
              
              <div class="col-md-6">
                <div class="form-group">
                  <label>&nbsp;</label>
                  <div>
                    <button type="submit" class="btn btn-primary"><i class="fa fa-filter"></i> {{ button_filter }}</button>
                    <a href="{{ refresh }}" class="btn btn-default"><i class="fa fa-refresh"></i> {{ button_clear }}</a>
                  </div>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
    
    <!-- جدول الوحدات -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-list"></i> {{ text_list }}</h3>
      </div>
      <div class="panel-body">
        <form action="{{ delete }}" method="post" enctype="multipart/form-data" id="form-unit">
          <div class="table-responsive">
            <table class="table table-bordered table-hover">
              <thead>
                <tr>
                  <td style="width: 1px;" class="text-center">
                    <input type="checkbox" onclick="$('input[name*=\'selected\']').prop('checked', this.checked);" />
                  </td>
                  <td>{{ column_name }}</td>
                  <td class="text-center">{{ column_symbol }}</td>
                  <td>{{ column_unit_type }}</td>
                  <td>{{ column_base_unit }}</td>
                  <td class="text-center">{{ column_conversion_factor }}</td>
                  <td class="text-center">{{ column_is_base_unit }}</td>
                  <td class="text-center">{{ column_products_count }}</td>
                  <td class="text-center">{{ column_movements_30_days }}</td>
                  <td class="text-center">{{ column_status }}</td>
                  <td class="text-center">{{ column_action }}</td>
                </tr>
              </thead>
              <tbody>
                {% if units %}
                {% for unit in units %}
                <tr>
                  <td class="text-center">
                    <input type="checkbox" name="selected[]" value="{{ unit.unit_id }}" />
                  </td>
                  <td>
                    <strong>{{ unit.name }}</strong>
                    {% if unit.description %}
                    <br><small class="text-muted">{{ unit.description|slice(0, 50) }}{% if unit.description|length > 50 %}...{% endif %}</small>
                    {% endif %}
                  </td>
                  <td class="text-center">
                    <span class="label label-info">{{ unit.symbol }}</span>
                  </td>
                  <td>
                    <span class="label label-primary">{{ unit.unit_type_text }}</span>
                  </td>
                  <td>
                    {% if unit.base_unit_name %}
                    {{ unit.base_unit_name }} ({{ unit.base_unit_symbol }})
                    {% else %}
                    <span class="text-muted">{{ text_none }}</span>
                    {% endif %}
                  </td>
                  <td class="text-center">
                    {% if unit.conversion_factor != '1.0000' %}
                    <span class="badge badge-warning">{{ unit.conversion_factor }}</span>
                    {% else %}
                    <span class="badge badge-success">1</span>
                    {% endif %}
                  </td>
                  <td class="text-center">
                    {% if unit.is_base_unit %}
                    <span class="label label-success"><i class="fa fa-star"></i> {{ text_yes }}</span>
                    {% else %}
                    <span class="label label-default">{{ text_no }}</span>
                    {% endif %}
                  </td>
                  <td class="text-center">
                    <span class="badge badge-primary">{{ unit.products_count }}</span>
                  </td>
                  <td class="text-center">
                    <span class="badge badge-info">{{ unit.movements_30_days }}</span>
                  </td>
                  <td class="text-center">
                    <span class="label label-{{ unit.is_active_class }}">{{ unit.is_active_text }}</span>
                  </td>
                  <td class="text-center">
                    <div class="btn-group">
                      <a href="{{ unit.view }}" data-toggle="tooltip" title="{{ button_view }}" class="btn btn-info btn-xs">
                        <i class="fa fa-eye"></i>
                      </a>
                      <a href="{{ unit.edit }}" data-toggle="tooltip" title="{{ button_edit }}" class="btn btn-primary btn-xs">
                        <i class="fa fa-pencil"></i>
                      </a>
                      <a href="{{ unit.copy }}" data-toggle="tooltip" title="{{ button_copy }}" class="btn btn-success btn-xs">
                        <i class="fa fa-copy"></i>
                      </a>
                      <button type="button" data-toggle="tooltip" title="{{ button_conversion_table }}" class="btn btn-warning btn-xs" onclick="showConversionTable({{ unit.unit_id }})">
                        <i class="fa fa-exchange"></i>
                      </button>
                    </div>
                  </td>
                </tr>
                {% endfor %}
                {% else %}
                <tr>
                  <td class="text-center" colspan="11">{{ text_no_results }}</td>
                </tr>
                {% endif %}
              </tbody>
            </table>
          </div>
        </form>
        
        <div class="row">
          <div class="col-sm-6 text-left">{{ pagination }}</div>
          <div class="col-sm-6 text-right">{{ results }}</div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- نافذة جدول التحويلات -->
<div class="modal fade" id="conversion-table-modal" tabindex="-1" role="dialog">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title"><i class="fa fa-exchange"></i> {{ text_conversion_table }}</h4>
      </div>
      <div class="modal-body">
        <div id="conversion-table-content">
          <div class="text-center">
            <i class="fa fa-spinner fa-spin fa-2x"></i>
            <p>{{ text_loading }}</p>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">إغلاق</button>
      </div>
    </div>
  </div>
</div>

<style>
.huge { font-size: 28px; font-weight: bold; }
.huge-text { font-size: 14px; font-weight: bold; }
.panel-green { border-color: #5cb85c; }
.panel-green > .panel-heading { border-color: #5cb85c; color: white; background-color: #5cb85c; }
.panel-purple { border-color: #9b59b6; }
.panel-purple > .panel-heading { border-color: #9b59b6; color: white; background-color: #9b59b6; }
.badge-primary { background-color: #337ab7; }
.badge-success { background-color: #5cb85c; }
.badge-warning { background-color: #f0ad4e; }
.badge-info { background-color: #5bc0de; }
.badge-danger { background-color: #d9534f; }
</style>

<script type="text/javascript">
$(document).ready(function() {
    $('[data-toggle="tooltip"]').tooltip();
    
    // تأثيرات بصرية للإحصائيات
    $('.huge').each(function() {
        var $this = $(this);
        var finalValue = parseInt($this.text().replace(/,/g, ''));
        if (!isNaN(finalValue)) {
            $this.text('0');
            
            $({ counter: 0 }).animate({ counter: finalValue }, {
                duration: 1500,
                easing: 'swing',
                step: function() {
                    $this.text(Math.ceil(this.counter).toLocaleString());
                }
            });
        }
    });
});

function showConversionTable(unitId) {
    $('#conversion-table-modal').modal('show');
    
    $.ajax({
        url: 'index.php?route=inventory/unit_management/conversionTable&user_token={{ user_token }}&unit_id=' + unitId,
        type: 'GET',
        dataType: 'json',
        success: function(data) {
            var html = '<div class="row">';
            html += '<div class="col-md-6">';
            html += '<h4>معلومات الوحدة</h4>';
            html += '<table class="table table-condensed">';
            html += '<tr><td><strong>الاسم:</strong></td><td>' + data.unit_info.name + '</td></tr>';
            html += '<tr><td><strong>الرمز:</strong></td><td>' + data.unit_info.symbol + '</td></tr>';
            html += '<tr><td><strong>النوع:</strong></td><td>' + data.unit_info.unit_type + '</td></tr>';
            html += '</table>';
            html += '</div>';
            
            html += '<div class="col-md-6">';
            html += '<h4>جدول التحويلات</h4>';
            if (data.conversion_table.length > 0) {
                html += '<table class="table table-condensed table-striped">';
                html += '<thead><tr><th>إلى الوحدة</th><th class="text-center">معامل التحويل</th></tr></thead>';
                html += '<tbody>';
                data.conversion_table.forEach(function(conversion) {
                    html += '<tr>';
                    html += '<td>' + conversion.to_unit_name + ' (' + conversion.to_unit_symbol + ')</td>';
                    html += '<td class="text-center"><span class="badge badge-info">' + parseFloat(conversion.conversion_factor).toFixed(4) + '</span></td>';
                    html += '</tr>';
                });
                html += '</tbody></table>';
            } else {
                html += '<p class="text-muted">{{ text_no_conversions }}</p>';
            }
            html += '</div>';
            html += '</div>';
            
            $('#conversion-table-content').html(html);
        },
        error: function() {
            $('#conversion-table-content').html('<div class="alert alert-danger">خطأ في تحميل البيانات</div>');
        }
    });
}
</script>

{{ footer }}
