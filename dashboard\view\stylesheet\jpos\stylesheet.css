/*Jade Core CSS Do Not Modify, Override Instead. Ends*/
fieldset legend {
  color: #dab310;
  font-weight: bold;
  margin-top: 30px;
  padding-bottom: 5px;
  text-transform: uppercase;
}
.notopmagin {
  margin-top: 0;
}
.form-horizontal .control-label {
  text-align: right;
  margin-bottom: 5px;
}
/* Support Tab */
.card-header h2{
  font-weight: 700;
}
.card-title{
  margin: 25px 0;
}
.card-title .text-muted{
  font-weight: 600;
}
/* Support Tab End */
.form-horizontal .control-label.text-left{
  text-align: left; 
}
.panel-default .panel-heading{
  color: #fff;
  border-color: #dab310;
  background: #dab310;
}
.panel-default .panel-heading select{
  color: #000;
}
.panel-default{
  border: 1px solid #dab310;
  border-top: 2px solid #dab310;
}
.nav-tabs > li > a, .nav-pills > li > a {
  font-size: 16px;
  padding: 15px 20px;
}

.btn-group .btn.btn-default:hover {
    background-color: #dab310;
    border-color: #dab310;
    color: #fff;
}
.btn-group .btn.btn-default.active, .btn-primary {
    background-color: #dab310;
    border-color: #dab310;
    color: #fff;
}
.btn-primary:hover {
    color: #fff;
    background-color: #dab310;
    border-color: #dab310;
}
.fa-md {
  font-size: 0.80em;
}
.help:before {
  font-family: FontAwesome;
  content: "\f059";
  margin-right: 4px;
  font-size: 14px;
}
.nav-tabs > li.active > a, .nav-tabs > li.active > a:hover, .nav-tabs > li.active > a:focus {
  border-top: 3px solid #dab310;
}
.jps-tab-content{
  padding-left: 45px;
}
/*Jade Core CSS Do Not Modify, Override Instead. Ends*/