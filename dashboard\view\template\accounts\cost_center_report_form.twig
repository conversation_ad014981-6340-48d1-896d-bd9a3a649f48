{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="submit" form="form-cost-center-report" data-toggle="tooltip" title="{{ button_generate }}" class="btn btn-primary"><i class="fa fa-play"></i></button>
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pencil"></i> {{ text_form }}</h3>
      </div>
      <div class="panel-body">
        <form action="{{ action }}" method="post" enctype="multipart/form-data" id="form-cost-center-report" class="form-horizontal">
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-date-start">{{ entry_date_start }}</label>
            <div class="col-sm-10">
              <div class="input-group date">
                <input type="text" name="date_start" value="{{ date_start }}" placeholder="{{ entry_date_start }}" data-date-format="YYYY-MM-DD" id="input-date-start" class="form-control" />
                <span class="input-group-btn">
                <button class="btn btn-default" type="button"><i class="fa fa-calendar"></i></button>
                </span>
              </div>
              {% if error_date_start %}
              <div class="text-danger">{{ error_date_start }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-date-end">{{ entry_date_end }}</label>
            <div class="col-sm-10">
              <div class="input-group date">
                <input type="text" name="date_end" value="{{ date_end }}" placeholder="{{ entry_date_end }}" data-date-format="YYYY-MM-DD" id="input-date-end" class="form-control" />
                <span class="input-group-btn">
                <button class="btn btn-default" type="button"><i class="fa fa-calendar"></i></button>
                </span>
              </div>
              {% if error_date_end %}
              <div class="text-danger">{{ error_date_end }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-cost-center">{{ entry_cost_center }}</label>
            <div class="col-sm-10">
              <select name="cost_center_id" id="input-cost-center" class="form-control select2">
                <option value="">{{ text_all_cost_centers }}</option>
                {% for cost_center in cost_centers %}
                <option value="{{ cost_center.cost_center_id }}"{% if cost_center.cost_center_id == cost_center_id %} selected="selected"{% endif %}>{{ cost_center.name }}</option>
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-department">{{ entry_department }}</label>
            <div class="col-sm-10">
              <select name="department_id" id="input-department" class="form-control select2">
                <option value="">{{ text_all_departments }}</option>
                {% for department in departments %}
                <option value="{{ department.department_id }}"{% if department.department_id == department_id %} selected="selected"{% endif %}>{{ department.name }}</option>
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-cost-type">{{ entry_cost_type }}</label>
            <div class="col-sm-10">
              <select name="cost_type" id="input-cost-type" class="form-control">
                <option value="all"{% if cost_type == 'all' %} selected="selected"{% endif %}>{{ text_all_costs }}</option>
                <option value="direct"{% if cost_type == 'direct' %} selected="selected"{% endif %}>{{ text_direct_costs }}</option>
                <option value="indirect"{% if cost_type == 'indirect' %} selected="selected"{% endif %}>{{ text_indirect_costs }}</option>
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label">{{ text_options }}</label>
            <div class="col-sm-10">
              <div class="checkbox">
                <label>
                  <input type="checkbox" name="include_indirect_costs" value="1"{% if include_indirect_costs %} checked="checked"{% endif %} />
                  {{ text_include_indirect_costs }}
                </label>
              </div>
              <div class="checkbox">
                <label>
                  <input type="checkbox" name="show_profitability" value="1"{% if show_profitability %} checked="checked"{% endif %} />
                  {{ text_show_profitability }}
                </label>
              </div>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label">{{ text_generate_options }}</label>
            <div class="col-sm-10">
              <div class="btn-group" data-toggle="buttons">
                <label class="btn btn-default">
                  <input type="radio" name="action_type" value="view" checked> {{ text_view_report }}
                </label>
                <label class="btn btn-default">
                  <input type="radio" name="action_type" value="export"> {{ text_export_report }}
                </label>
                <label class="btn btn-default">
                  <input type="radio" name="action_type" value="profitability"> {{ text_profitability_analysis }}
                </label>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript"><!--
$('#input-date-start, #input-date-end').datetimepicker({
    language: '{{ datepicker }}',
    pickTime: false
});

$('.select2').select2({
    placeholder: '{{ text_select }}',
    allowClear: true
});
//--></script>

{{ footer }}
