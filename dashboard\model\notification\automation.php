<?php
/**
 * نموذج أتمتة الإشعارات
 * Notification Automation Model
 * 
 * نموذج البيانات لأتمتة الإشعارات والتشغيل التلقائي
 * 
 * @package    AYM ERP
 * <AUTHOR> ERP Development Team
 * @copyright  2024 AYM ERP
 * @license    Proprietary
 * @version    1.0.0
 * @since      2024-12-19
 */

class ModelNotificationAutomation extends Model {
    
    /**
     * إنشاء قاعدة أتمتة جديدة
     */
    public function addAutomationRule($data) {
        $this->db->query("INSERT INTO " . DB_PREFIX . "notification_automation SET
            name = '" . $this->db->escape($data['name']) . "',
            description = '" . $this->db->escape($data['description']) . "',
            trigger_event = '" . $this->db->escape($data['trigger_event']) . "',
            trigger_conditions = '" . $this->db->escape(json_encode($data['trigger_conditions'])) . "',
            notification_template_id = '" . (int)$data['notification_template_id'] . "',
            target_type = '" . $this->db->escape($data['target_type']) . "',
            target_config = '" . $this->db->escape(json_encode($data['target_config'])) . "',
            delay_minutes = '" . (int)($data['delay_minutes'] ?? 0) . "',
            status = '" . $this->db->escape($data['status']) . "',
            created_by = '" . (int)$this->user->getId() . "',
            created_at = NOW()");
        
        $rule_id = $this->db->getLastId();
        
        // تسجيل النشاط
        $this->load->model('activity_log');
        $this->model_activity_log->logCreate('notification', 'automation_rule', $rule_id, $data);
        
        return $rule_id;
    }
    
    /**
     * تحديث قاعدة أتمتة
     */
    public function editAutomationRule($rule_id, $data) {
        $this->db->query("UPDATE " . DB_PREFIX . "notification_automation SET
            name = '" . $this->db->escape($data['name']) . "',
            description = '" . $this->db->escape($data['description']) . "',
            trigger_event = '" . $this->db->escape($data['trigger_event']) . "',
            trigger_conditions = '" . $this->db->escape(json_encode($data['trigger_conditions'])) . "',
            notification_template_id = '" . (int)$data['notification_template_id'] . "',
            target_type = '" . $this->db->escape($data['target_type']) . "',
            target_config = '" . $this->db->escape(json_encode($data['target_config'])) . "',
            delay_minutes = '" . (int)($data['delay_minutes'] ?? 0) . "',
            status = '" . $this->db->escape($data['status']) . "',
            updated_at = NOW()
            WHERE rule_id = '" . (int)$rule_id . "'");
        
        // تسجيل النشاط
        $this->load->model('activity_log');
        $this->model_activity_log->logUpdate('notification', 'automation_rule', $rule_id, [], $data);
    }
    
    /**
     * حذف قاعدة أتمتة
     */
    public function deleteAutomationRule($rule_id) {
        $rule = $this->getAutomationRule($rule_id);
        
        $this->db->query("DELETE FROM " . DB_PREFIX . "notification_automation WHERE rule_id = '" . (int)$rule_id . "'");
        $this->db->query("DELETE FROM " . DB_PREFIX . "notification_automation_log WHERE rule_id = '" . (int)$rule_id . "'");
        
        // تسجيل النشاط
        $this->load->model('activity_log');
        $this->model_activity_log->logDelete('notification', 'automation_rule', $rule_id, $rule);
    }
    
    /**
     * الحصول على قاعدة أتمتة
     */
    public function getAutomationRule($rule_id) {
        $query = $this->db->query("SELECT na.*, 
            CONCAT(u.firstname, ' ', u.lastname) as creator_name,
            nt.name as template_name
            FROM " . DB_PREFIX . "notification_automation na
            LEFT JOIN " . DB_PREFIX . "user u ON (na.created_by = u.user_id)
            LEFT JOIN " . DB_PREFIX . "notification_template nt ON (na.notification_template_id = nt.template_id)
            WHERE na.rule_id = '" . (int)$rule_id . "'");
        
        if ($query->num_rows) {
            $rule = $query->row;
            $rule['trigger_conditions'] = json_decode($rule['trigger_conditions'], true);
            $rule['target_config'] = json_decode($rule['target_config'], true);
            return $rule;
        }
        
        return false;
    }
    
    /**
     * الحصول على قواعد الأتمتة
     */
    public function getAutomationRules($data = []) {
        $sql = "SELECT na.*, 
            CONCAT(u.firstname, ' ', u.lastname) as creator_name,
            nt.name as template_name,
            (SELECT COUNT(*) FROM " . DB_PREFIX . "notification_automation_log nal 
             WHERE nal.rule_id = na.rule_id) as execution_count
            FROM " . DB_PREFIX . "notification_automation na
            LEFT JOIN " . DB_PREFIX . "user u ON (na.created_by = u.user_id)
            LEFT JOIN " . DB_PREFIX . "notification_template nt ON (na.notification_template_id = nt.template_id)
            WHERE 1=1";
        
        if (!empty($data['filter_status'])) {
            $sql .= " AND na.status = '" . $this->db->escape($data['filter_status']) . "'";
        }
        
        if (!empty($data['filter_event'])) {
            $sql .= " AND na.trigger_event = '" . $this->db->escape($data['filter_event']) . "'";
        }
        
        $sql .= " ORDER BY na.created_at DESC";
        
        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }
            
            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }
            
            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }
        
        $query = $this->db->query($sql);
        
        return $query->rows;
    }
    
    /**
     * تشغيل قواعد الأتمتة للحدث المحدد
     */
    public function triggerAutomation($event, $data = []) {
        // البحث عن القواعد النشطة للحدث
        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "notification_automation 
            WHERE trigger_event = '" . $this->db->escape($event) . "' 
            AND status = 'active'");
        
        foreach ($query->rows as $rule) {
            $this->executeRule($rule, $data);
        }
    }
    
    /**
     * تنفيذ قاعدة أتمتة محددة
     */
    private function executeRule($rule, $event_data) {
        // فحص الشروط
        $conditions = json_decode($rule['trigger_conditions'], true);
        if (!$this->checkConditions($conditions, $event_data)) {
            return false;
        }
        
        // تحديد المستهدفين
        $targets = $this->getTargets($rule, $event_data);
        if (empty($targets)) {
            return false;
        }
        
        // تحميل قالب الإشعار
        $this->load->model('notification/templates');
        $template = $this->model_notification_templates->getTemplate($rule['notification_template_id']);
        if (!$template) {
            return false;
        }
        
        // إرسال الإشعارات
        $this->load->model('communication/unified_notification');
        $sent_count = 0;
        
        foreach ($targets as $target) {
            // معالجة القالب بالبيانات
            $processed_template = $this->processTemplate($template, $event_data);
            
            $notification_data = [
                'title' => $processed_template['title'],
                'message' => $processed_template['content'],
                'type' => $template['type'],
                'priority' => $template['priority'],
                'module' => 'automation',
                'reference_type' => 'automation_rule',
                'reference_id' => $rule['rule_id'],
                'recipient_type' => 'user',
                'recipient_id' => $target['user_id'],
                'channels' => json_decode($template['channels'], true)
            ];
            
            // تأخير الإرسال إذا كان مطلوباً
            if ($rule['delay_minutes'] > 0) {
                $notification_data['scheduled_at'] = date('Y-m-d H:i:s', strtotime('+' . $rule['delay_minutes'] . ' minutes'));
            }
            
            $notification_id = $this->model_communication_unified_notification->addNotification($notification_data);
            if ($notification_id) {
                $sent_count++;
            }
        }
        
        // تسجيل تنفيذ القاعدة
        $this->logRuleExecution($rule['rule_id'], $event_data, $sent_count);
        
        return $sent_count;
    }
    
    /**
     * فحص شروط التشغيل
     */
    private function checkConditions($conditions, $event_data) {
        if (empty($conditions)) {
            return true;
        }
        
        foreach ($conditions as $condition) {
            $field = $condition['field'];
            $operator = $condition['operator'];
            $value = $condition['value'];
            
            $event_value = isset($event_data[$field]) ? $event_data[$field] : null;
            
            switch ($operator) {
                case 'equals':
                    if ($event_value != $value) return false;
                    break;
                case 'not_equals':
                    if ($event_value == $value) return false;
                    break;
                case 'greater_than':
                    if ($event_value <= $value) return false;
                    break;
                case 'less_than':
                    if ($event_value >= $value) return false;
                    break;
                case 'contains':
                    if (strpos($event_value, $value) === false) return false;
                    break;
            }
        }
        
        return true;
    }
    
    /**
     * تحديد المستهدفين للإشعار
     */
    private function getTargets($rule, $event_data) {
        $target_config = json_decode($rule['target_config'], true);
        $targets = [];
        
        switch ($rule['target_type']) {
            case 'specific_users':
                foreach ($target_config['user_ids'] as $user_id) {
                    $targets[] = ['user_id' => $user_id];
                }
                break;
                
            case 'user_groups':
                foreach ($target_config['group_ids'] as $group_id) {
                    $query = $this->db->query("SELECT user_id FROM " . DB_PREFIX . "user_to_group 
                        WHERE user_group_id = '" . (int)$group_id . "'");
                    foreach ($query->rows as $row) {
                        $targets[] = ['user_id' => $row['user_id']];
                    }
                }
                break;
                
            case 'dynamic':
                // تحديد ديناميكي بناءً على بيانات الحدث
                if (isset($event_data['user_id'])) {
                    $targets[] = ['user_id' => $event_data['user_id']];
                }
                if (isset($event_data['assigned_to'])) {
                    $targets[] = ['user_id' => $event_data['assigned_to']];
                }
                break;
        }
        
        return array_unique($targets, SORT_REGULAR);
    }
    
    /**
     * معالجة قالب الإشعار بالبيانات
     */
    private function processTemplate($template, $data) {
        $title = $template['title'];
        $content = $template['content'];
        
        // استبدال المتغيرات
        foreach ($data as $key => $value) {
            $placeholder = '{' . $key . '}';
            $title = str_replace($placeholder, $value, $title);
            $content = str_replace($placeholder, $value, $content);
        }
        
        return [
            'title' => $title,
            'content' => $content
        ];
    }
    
    /**
     * تسجيل تنفيذ القاعدة
     */
    private function logRuleExecution($rule_id, $event_data, $sent_count) {
        $this->db->query("INSERT INTO " . DB_PREFIX . "notification_automation_log SET
            rule_id = '" . (int)$rule_id . "',
            event_data = '" . $this->db->escape(json_encode($event_data)) . "',
            notifications_sent = '" . (int)$sent_count . "',
            executed_at = NOW()");
    }
    
    /**
     * الحصول على سجل تنفيذ القواعد
     */
    public function getExecutionLog($rule_id = null, $limit = 50) {
        $sql = "SELECT nal.*, na.name as rule_name
            FROM " . DB_PREFIX . "notification_automation_log nal
            LEFT JOIN " . DB_PREFIX . "notification_automation na ON (nal.rule_id = na.rule_id)";
        
        if ($rule_id) {
            $sql .= " WHERE nal.rule_id = '" . (int)$rule_id . "'";
        }
        
        $sql .= " ORDER BY nal.executed_at DESC LIMIT " . (int)$limit;
        
        $query = $this->db->query($sql);
        
        return $query->rows;
    }
    
    /**
     * الحصول على إحصائيات الأتمتة
     */
    public function getAutomationStats() {
        $stats = [];
        
        // إجمالي القواعد
        $query = $this->db->query("SELECT COUNT(*) as total FROM " . DB_PREFIX . "notification_automation");
        $stats['total_rules'] = $query->row['total'];
        
        // القواعد النشطة
        $query = $this->db->query("SELECT COUNT(*) as total FROM " . DB_PREFIX . "notification_automation WHERE status = 'active'");
        $stats['active_rules'] = $query->row['total'];
        
        // التنفيذات اليوم
        $query = $this->db->query("SELECT COUNT(*) as total FROM " . DB_PREFIX . "notification_automation_log WHERE DATE(executed_at) = CURDATE()");
        $stats['executions_today'] = $query->row['total'];
        
        // الإشعارات المرسلة اليوم
        $query = $this->db->query("SELECT SUM(notifications_sent) as total FROM " . DB_PREFIX . "notification_automation_log WHERE DATE(executed_at) = CURDATE()");
        $stats['notifications_sent_today'] = $query->row['total'] ?? 0;
        
        return $stats;
    }
}
