{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <a href="{{ generate_form }}" data-toggle="tooltip" title="{{ button_generate }}" class="btn btn-primary"><i class="fa fa-cogs"></i></a>
        <a href="{{ export_excel }}" data-toggle="tooltip" title="{{ button_export_excel }}" class="btn btn-success"><i class="fa fa-file-excel-o"></i></a>
        <a href="{{ export_pdf }}" data-toggle="tooltip" title="{{ button_export_pdf }}" class="btn btn-danger"><i class="fa fa-file-pdf-o"></i></a>
        <a href="{{ print }}" data-toggle="tooltip" title="{{ button_print }}" class="btn btn-warning" target="_blank"><i class="fa fa-print"></i></a>
        <a href="{{ back }}" data-toggle="tooltip" title="{{ button_back }}" class="btn btn-default"><i class="fa fa-reply"></i></a>
      </div>
      <h1>{{ heading_title }} - {{ text_dashboard }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if success %}
    <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}

    <!-- Financial KPI Cards -->
    <div class="row">
      <div class="col-md-3">
        <div class="info-box bg-blue">
          <span class="info-box-icon"><i class="fa fa-money"></i></span>
          <div class="info-box-content">
            <span class="info-box-text">{{ text_total_assets }}</span>
            <span class="info-box-number">{{ financial_summary.total_assets }}</span>
            <div class="progress">
              <div class="progress-bar" style="width: {{ financial_summary.asset_growth }}%"></div>
            </div>
            <span class="progress-description">{{ financial_summary.asset_growth }}% {{ text_growth }}</span>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="info-box bg-green">
          <span class="info-box-icon"><i class="fa fa-line-chart"></i></span>
          <div class="info-box-content">
            <span class="info-box-text">{{ text_net_income }}</span>
            <span class="info-box-number">{{ financial_summary.net_income }}</span>
            <div class="progress">
              <div class="progress-bar" style="width: {{ financial_summary.profit_margin }}%"></div>
            </div>
            <span class="progress-description">{{ financial_summary.profit_margin }}% {{ text_margin }}</span>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="info-box bg-yellow">
          <span class="info-box-icon"><i class="fa fa-tint"></i></span>
          <div class="info-box-content">
            <span class="info-box-text">{{ text_current_ratio }}</span>
            <span class="info-box-number">{{ financial_ratios.current_ratio }}</span>
            <div class="progress">
              <div class="progress-bar" style="width: {{ financial_ratios.liquidity_score }}%"></div>
            </div>
            <span class="progress-description">{{ text_liquidity_health }}</span>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="info-box bg-red">
          <span class="info-box-icon"><i class="fa fa-percent"></i></span>
          <div class="info-box-content">
            <span class="info-box-text">{{ text_return_on_equity }}</span>
            <span class="info-box-number">{{ financial_ratios.return_on_equity }}%</span>
            <div class="progress">
              <div class="progress-bar" style="width: {{ financial_ratios.roe_score }}%"></div>
            </div>
            <span class="progress-description">{{ text_profitability_health }}</span>
          </div>
        </div>
      </div>
    </div>
    
    <div class="card">
      <div class="card-header"><i class="fas fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}chart-bar{% elif screen_type == 'settings' %}cog{% else %}pencil-alt{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="card-body">
        <div id="filter-accounts\financial_reports_advanced" class="filter-container collapse">
          <div class="filter-card card mb-3">
            <div class="card-header">
              <h5 class="card-title">
                <i class="fas fa-filter"></i> {{ text_filter }}
              </h5>
            </div>
            <div class="card-body p-0">
              <div class="filter-form-container p-3">
                <form id="filter-form">
                  <div class="row">
                    <div class="col-sm-4">
                      <div class="mb-3">
                        <label for="input-name">{{ entry_name }}</label>
                        <input type="text" name="filter_name" value="{{ filter_name }}" placeholder="{{ entry_name }}" id="input-name" class="form-control" />
                      </div>
                    </div>
                    <div class="col-sm-4">
                      <div class="mb-3">
                        <label for="input-status">{{ entry_status }}</label>
                        <select name="filter_status" id="input-status" class="form-select">
                          <option value="">{{ text_all }}</option>
                          <option value="1" {% if filter_status == '1' %}selected{% endif %}>{{ text_enabled }}</option>
                          <option value="0" {% if filter_status == '0' %}selected{% endif %}>{{ text_disabled }}</option>
                        </select>
                      </div>
                    </div>
                    <div class="col-sm-4">
                      <div class="mb-3">
                        <label for="input-date">{{ entry_date_added }}</label>
                        <div class="input-group date">
                          <input type="text" name="filter_date_added" value="{{ filter_date_added }}" placeholder="{{ entry_date_added }}" id="input-date" class="form-control" />
                          <div class="input-group-text"><i class="fas fa-calendar"></i></div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="text-end">
                    <button type="button" id="button-filter" class="btn btn-light"><i class="fas fa-filter"></i> {{ button_filter }}</button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
        <form id="form-accounts\financial_reports_advanced" method="post" data-oc-toggle="ajax" data-oc-load="{{ action }}" data-oc-target="#main-container">
          <div class="table-responsive">
            <table class="table table-bordered table-hover">
              <thead>
                <tr>
                  <td class="text-center" style="width: 1px;"><input type="checkbox" onclick="$('input[name*=\'selected\']').prop('checked', $(this).prop('checked'));" class="form-check-input" /></td>
                  <td class="text-start">{{ column_name }}{% if sort == 'name' %} <i class="fas fa-sort-{{ order|lower }}"></i>{% endif %}</td>
                  <td class="text-start">{{ column_status }}{% if sort == 'status' %} <i class="fas fa-sort-{{ order|lower }}"></i>{% endif %}</td>
                  <td class="text-start">{{ column_date_added }}{% if sort == 'date_added' %} <i class="fas fa-sort-{{ order|lower }}"></i>{% endif %}</td>
                  <td class="text-end">{{ column_action }}</td>
                </tr>
              </thead>
              <tbody>
                {% if accounts\financial_reports_advanceds %}
                  {% for accounts\financial_reports_advanced in accounts\financial_reports_advanceds %}
                    <tr>
                      <td class="text-center"><input type="checkbox" name="selected[]" value="{{ accounts\financial_reports_advanced.accounts\financial_reports_advanced_id }}" class="form-check-input" /></td>
                      <td class="text-start">{{ accounts\financial_reports_advanced.name }}</td>
                      <td class="text-start">{{ accounts\financial_reports_advanced.status }}</td>
                      <td class="text-start">{{ accounts\financial_reports_advanced.date_added }}</td>
                      <td class="text-end">
                        <div class="d-inline-block dropdown">
                          <button type="button" data-bs-toggle="dropdown" class="btn btn-sm btn-text dropdown-toggle"><i class="fas fa-cog"></i></button>
                          <div class="dropdown-menu">
                            <a href="{{ accounts\financial_reports_advanced.edit }}" class="dropdown-item"><i class="fas fa-pencil-alt fa-fw"></i> {{ button_edit }}</a>
                            <button type="button" data-bs-toggle="modal" data-bs-target="#modal-accounts\financial_reports_advanced{{ accounts\financial_reports_advanced.accounts\financial_reports_advanced_id }}" class="dropdown-item"><i class="fas fa-trash-alt fa-fw"></i> {{ button_delete }}</button>
                          </div>
                        </div>
                      </td>
                    </tr>
                  {% endfor %}
                {% else %}
                  <tr>
                    <td class="text-center" colspan="{{ columns_count + 2 }}">{{ text_no_results }}</td>
                  </tr>
                {% endif %}
              </tbody>
            </table>
          </div>
          <div class="row">
            <div class="col-sm-6 text-start">{{ pagination }}</div>
            <div class="col-sm-6 text-end">{{ results }}</div>
          </div>
        </form>

<script type="text/javascript"><!--
$('#accounts\financial_reports_advanced-form').on('submit', function(e) {
    e.preventDefault();
    
    var element = this;
    
    $.ajax({
        url: $(element).attr('action'),
        type: 'post',
        data: $(element).serialize(),
        dataType: 'json',
        beforeSend: function() {
            $(element).find('button[type=\'submit\']').prop('disabled', true).addClass('loading');
        },
        complete: function() {
            $(element).find('button[type=\'submit\']').prop('disabled', false).removeClass('loading');
        },
        success: function(json) {
            $('.alert-dismissible').remove();
            $(element).find('.is-invalid').removeClass('is-invalid');
            $(element).find('.invalid-feedback').removeClass('d-block');
            
            if (json['error']) {
                if (json['error']['warning']) {
                    $('#alert').prepend('<div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> ' + json['error']['warning'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
                }
                
                for (key in json['error']) {
                    $('#input-' + key.replaceAll('_', '-')).addClass('is-invalid').next().html(json['error'][key]).addClass('d-block');
                }
            }
            
            if (json['success']) {
                $('#alert').prepend('<div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> ' + json['success'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
                
                // Refresh or redirect
                if (json['redirect']) {
                    location = json['redirect'];
                }
            }
        },
        error: function(xhr, ajaxOptions, thrownError) {
            console.error(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
    });
});

// AJAX action for generateReport
$('body').on('click', '.btn-generateReport', function(e) {
    e.preventDefault();
    
    var element = this;
    var id = $(element).data('id');
    
    $.ajax({
        url: 'index.php?route=accounts\financial_reports_advanced/generateReport&user_token={{ user_token }}',
        type: 'post',
        data: {
            'id': id
        },
        dataType: 'json',
        beforeSend: function() {
            $(element).prop('disabled', true);
        },
        complete: function() {
            $(element).prop('disabled', false);
        },
        success: function(json) {
            $('.alert-dismissible').remove();
            
            if (json['error']) {
                $('#alert').prepend('<div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> ' + json['error'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
            }
            
            if (json['success']) {
                $('#alert').prepend('<div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> ' + json['success'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
                
                // Refresh the table
                $('#button-refresh').trigger('click');
            }
        },
        error: function(xhr, ajaxOptions, thrownError) {
            console.error(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
    });
});

// AJAX action for view
$('body').on('click', '.btn-view', function(e) {
    e.preventDefault();
    
    var element = this;
    var id = $(element).data('id');
    
    $.ajax({
        url: 'index.php?route=accounts\financial_reports_advanced/view&user_token={{ user_token }}',
        type: 'post',
        data: {
            'id': id
        },
        dataType: 'json',
        beforeSend: function() {
            $(element).prop('disabled', true);
        },
        complete: function() {
            $(element).prop('disabled', false);
        },
        success: function(json) {
            $('.alert-dismissible').remove();
            
            if (json['error']) {
                $('#alert').prepend('<div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> ' + json['error'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
            }
            
            if (json['success']) {
                $('#alert').prepend('<div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> ' + json['success'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
                
                // Refresh the table
                $('#button-refresh').trigger('click');
            }
        },
        error: function(xhr, ajaxOptions, thrownError) {
            console.error(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
    });
});

// AJAX action for export
$('body').on('click', '.btn-export', function(e) {
    e.preventDefault();
    
    var element = this;
    var id = $(element).data('id');
    
    $.ajax({
        url: 'index.php?route=accounts\financial_reports_advanced/export&user_token={{ user_token }}',
        type: 'post',
        data: {
            'id': id
        },
        dataType: 'json',
        beforeSend: function() {
            $(element).prop('disabled', true);
        },
        complete: function() {
            $(element).prop('disabled', false);
        },
        success: function(json) {
            $('.alert-dismissible').remove();
            
            if (json['error']) {
                $('#alert').prepend('<div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> ' + json['error'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
            }
            
            if (json['success']) {
                $('#alert').prepend('<div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> ' + json['success'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
                
                // Refresh the table
                $('#button-refresh').trigger('click');
            }
        },
        error: function(xhr, ajaxOptions, thrownError) {
            console.error(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
    });
});

// AJAX action for getFinancialRatios
$('body').on('click', '.btn-getFinancialRatios', function(e) {
    e.preventDefault();
    
    var element = this;
    var id = $(element).data('id');
    
    $.ajax({
        url: 'index.php?route=accounts\financial_reports_advanced/getFinancialRatios&user_token={{ user_token }}',
        type: 'post',
        data: {
            'id': id
        },
        dataType: 'json',
        beforeSend: function() {
            $(element).prop('disabled', true);
        },
        complete: function() {
            $(element).prop('disabled', false);
        },
        success: function(json) {
            $('.alert-dismissible').remove();
            
            if (json['error']) {
                $('#alert').prepend('<div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> ' + json['error'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
            }
            
            if (json['success']) {
                $('#alert').prepend('<div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> ' + json['success'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
                
                // Refresh the table
                $('#button-refresh').trigger('click');
            }
        },
        error: function(xhr, ajaxOptions, thrownError) {
            console.error(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
    });
});

// AJAX action for getFinancialAnalysis
$('body').on('click', '.btn-getFinancialAnalysis', function(e) {
    e.preventDefault();
    
    var element = this;
    var id = $(element).data('id');
    
    $.ajax({
        url: 'index.php?route=accounts\financial_reports_advanced/getFinancialAnalysis&user_token={{ user_token }}',
        type: 'post',
        data: {
            'id': id
        },
        dataType: 'json',
        beforeSend: function() {
            $(element).prop('disabled', true);
        },
        complete: function() {
            $(element).prop('disabled', false);
        },
        success: function(json) {
            $('.alert-dismissible').remove();
            
            if (json['error']) {
                $('#alert').prepend('<div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> ' + json['error'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
            }
            
            if (json['success']) {
                $('#alert').prepend('<div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> ' + json['success'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
                
                // Refresh the table
                $('#button-refresh').trigger('click');
            }
        },
        error: function(xhr, ajaxOptions, thrownError) {
            console.error(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
    });
});

// AJAX action for getPerformanceIndicators
$('body').on('click', '.btn-getPerformanceIndicators', function(e) {
    e.preventDefault();
    
    var element = this;
    var id = $(element).data('id');
    
    $.ajax({
        url: 'index.php?route=accounts\financial_reports_advanced/getPerformanceIndicators&user_token={{ user_token }}',
        type: 'post',
        data: {
            'id': id
        },
        dataType: 'json',
        beforeSend: function() {
            $(element).prop('disabled', true);
        },
        complete: function() {
            $(element).prop('disabled', false);
        },
        success: function(json) {
            $('.alert-dismissible').remove();
            
            if (json['error']) {
                $('#alert').prepend('<div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> ' + json['error'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
            }
            
            if (json['success']) {
                $('#alert').prepend('<div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> ' + json['success'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
                
                // Refresh the table
                $('#button-refresh').trigger('click');
            }
        },
        error: function(xhr, ajaxOptions, thrownError) {
            console.error(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
    });
});

// AJAX action for getTrendAnalysis
$('body').on('click', '.btn-getTrendAnalysis', function(e) {
    e.preventDefault();
    
    var element = this;
    var id = $(element).data('id');
    
    $.ajax({
        url: 'index.php?route=accounts\financial_reports_advanced/getTrendAnalysis&user_token={{ user_token }}',
        type: 'post',
        data: {
            'id': id
        },
        dataType: 'json',
        beforeSend: function() {
            $(element).prop('disabled', true);
        },
        complete: function() {
            $(element).prop('disabled', false);
        },
        success: function(json) {
            $('.alert-dismissible').remove();
            
            if (json['error']) {
                $('#alert').prepend('<div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> ' + json['error'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
            }
            
            if (json['success']) {
                $('#alert').prepend('<div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> ' + json['success'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
                
                // Refresh the table
                $('#button-refresh').trigger('click');
            }
        },
        error: function(xhr, ajaxOptions, thrownError) {
            console.error(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
    });
});

// AJAX action for getBenchmarkAnalysis
$('body').on('click', '.btn-getBenchmarkAnalysis', function(e) {
    e.preventDefault();
    
    var element = this;
    var id = $(element).data('id');
    
    $.ajax({
        url: 'index.php?route=accounts\financial_reports_advanced/getBenchmarkAnalysis&user_token={{ user_token }}',
        type: 'post',
        data: {
            'id': id
        },
        dataType: 'json',
        beforeSend: function() {
            $(element).prop('disabled', true);
        },
        complete: function() {
            $(element).prop('disabled', false);
        },
        success: function(json) {
            $('.alert-dismissible').remove();
            
            if (json['error']) {
                $('#alert').prepend('<div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> ' + json['error'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
            }
            
            if (json['success']) {
                $('#alert').prepend('<div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> ' + json['success'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
                
                // Refresh the table
                $('#button-refresh').trigger('click');
            }
        },
        error: function(xhr, ajaxOptions, thrownError) {
            console.error(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
    });
});

// AJAX action for getVarianceAnalysis
$('body').on('click', '.btn-getVarianceAnalysis', function(e) {
    e.preventDefault();
    
    var element = this;
    var id = $(element).data('id');
    
    $.ajax({
        url: 'index.php?route=accounts\financial_reports_advanced/getVarianceAnalysis&user_token={{ user_token }}',
        type: 'post',
        data: {
            'id': id
        },
        dataType: 'json',
        beforeSend: function() {
            $(element).prop('disabled', true);
        },
        complete: function() {
            $(element).prop('disabled', false);
        },
        success: function(json) {
            $('.alert-dismissible').remove();
            
            if (json['error']) {
                $('#alert').prepend('<div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> ' + json['error'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
            }
            
            if (json['success']) {
                $('#alert').prepend('<div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> ' + json['success'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
                
                // Refresh the table
                $('#button-refresh').trigger('click');
            }
        },
        error: function(xhr, ajaxOptions, thrownError) {
            console.error(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
    });
});

// AJAX action for getSegmentAnalysis
$('body').on('click', '.btn-getSegmentAnalysis', function(e) {
    e.preventDefault();
    
    var element = this;
    var id = $(element).data('id');
    
    $.ajax({
        url: 'index.php?route=accounts\financial_reports_advanced/getSegmentAnalysis&user_token={{ user_token }}',
        type: 'post',
        data: {
            'id': id
        },
        dataType: 'json',
        beforeSend: function() {
            $(element).prop('disabled', true);
        },
        complete: function() {
            $(element).prop('disabled', false);
        },
        success: function(json) {
            $('.alert-dismissible').remove();
            
            if (json['error']) {
                $('#alert').prepend('<div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> ' + json['error'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
            }
            
            if (json['success']) {
                $('#alert').prepend('<div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> ' + json['success'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
                
                // Refresh the table
                $('#button-refresh').trigger('click');
            }
        },
        error: function(xhr, ajaxOptions, thrownError) {
            console.error(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
    });
});
//--></script>
      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}