{{ header }}{{ column_left }}
{#
/**
 * AYM ERP System: Cash Flow Report View
 *
 * عرض تقرير التدفقات النقدية - مطور بجودة عالمية
 *
 * الميزات المتقدمة:
 * - تقرير تدفقات نقدية شامل
 * - عرض احترافي للبيانات المالية
 * - تصدير متعدد الصيغ
 * - تحليل مالي متقدم
 * - واجهة مستخدم متجاوبة
 * - تكامل مع Bootstrap 3.3
 *
 * @package    AYM ERP
 * <AUTHOR> Development Team
 * @copyright  2024 AYM ERP Systems
 * @license    Commercial License
 * @version    1.0.0
 * @since      2024-01-15
 */
#}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <div class="btn-group">
          <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown">
            <i class="fa fa-download"></i> {{ button_export_pdf }} <span class="caret"></span>
          </button>
          <ul class="dropdown-menu dropdown-menu-right">
            <li><a href="{{ export_pdf }}" target="_blank"><i class="fa fa-file-pdf-o"></i> {{ button_export_pdf }}</a></li>
            <li><a href="{{ export_excel }}" target="_blank"><i class="fa fa-file-excel-o"></i> {{ button_export_excel }}</a></li>
            <li class="divider"></li>
            <li><a href="#" onclick="window.print()"><i class="fa fa-print"></i> {{ button_print }}</a></li>
          </ul>
        </div>
        <a href="{{ generate }}" class="btn btn-default">
          <i class="fa fa-cog"></i> إعداد التقرير
        </a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>

  <div class="container-fluid">
    {# رسائل التنبيه #}
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible">
      <i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}

    {% if success %}
    <div class="alert alert-success alert-dismissible">
      <i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}

    {# معلومات التقرير #}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title">
          <i class="fa fa-line-chart"></i> {{ heading_title_report }}
        </h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-md-6">
            <h4 class="text-primary">{{ heading_title }}</h4>
            <p class="text-muted">
              <strong>الفترة:</strong> من {{ date_start_formatted }} إلى {{ date_end_formatted }}<br>
              <strong>الطريقة:</strong> {{ method == 'direct' ? text_method_direct : text_method_indirect }}<br>
              <strong>تاريخ الإنشاء:</strong> {{ "now"|date("d/m/Y H:i") }}
            </p>
          </div>
          <div class="col-md-6 text-right">
            {% if cash_flow.net_change >= 0 %}
            <div class="alert alert-success">
              <i class="fa fa-arrow-up"></i>
              <strong>تدفق نقدي إيجابي</strong><br>
              {{ cash_flow.net_change|number_format(2) }} {{ config_currency }}
            </div>
            {% else %}
            <div class="alert alert-danger">
              <i class="fa fa-arrow-down"></i>
              <strong>تدفق نقدي سلبي</strong><br>
              {{ cash_flow.net_change|number_format(2) }} {{ config_currency }}
            </div>
            {% endif %}
          </div>
        </div>
      </div>
    </div>

    {# التدفقات النقدية من الأنشطة التشغيلية #}
    <div class="panel panel-info">
      <div class="panel-heading">
        <h4 class="panel-title">
          <i class="fa fa-cogs"></i> {{ text_operating_activities }}
        </h4>
      </div>
      <div class="panel-body">
        <div class="table-responsive">
          <table class="table table-striped table-hover">
            <thead>
              <tr>
                <th width="70%">{{ column_description }}</th>
                <th width="30%" class="text-right">{{ column_amount }}</th>
              </tr>
            </thead>
            <tbody>
              {% for item in cash_flow.operating %}
              <tr>
                <td>{{ item.description }}</td>
                <td class="text-right">
                  <span class="{% if item.amount >= 0 %}text-success{% else %}text-danger{% endif %}">
                    {{ item.amount|number_format(2) }}
                  </span>
                </td>
              </tr>
              {% endfor %}
            </tbody>
            <tfoot>
              <tr class="info">
                <th>{{ text_net_operating_cash }}</th>
                <th class="text-right">
                  <span class="{% if cash_flow.operating_total >= 0 %}text-success{% else %}text-danger{% endif %}">
                    <strong>{{ cash_flow.operating_total|number_format(2) }}</strong>
                  </span>
                </th>
              </tr>
            </tfoot>
          </table>
        </div>
      </div>
    </div>

    {# التدفقات النقدية من الأنشطة الاستثمارية #}
    <div class="panel panel-warning">
      <div class="panel-heading">
        <h4 class="panel-title">
          <i class="fa fa-building"></i> {{ text_investing_activities }}
        </h4>
      </div>
      <div class="panel-body">
        <div class="table-responsive">
          <table class="table table-striped table-hover">
            <thead>
              <tr>
                <th width="70%">{{ column_description }}</th>
                <th width="30%" class="text-right">{{ column_amount }}</th>
              </tr>
            </thead>
            <tbody>
              {% for item in cash_flow.investing %}
              <tr>
                <td>{{ item.description }}</td>
                <td class="text-right">
                  <span class="{% if item.amount >= 0 %}text-success{% else %}text-danger{% endif %}">
                    {{ item.amount|number_format(2) }}
                  </span>
                </td>
              </tr>
              {% endfor %}
            </tbody>
            <tfoot>
              <tr class="warning">
                <th>{{ text_net_investing_cash }}</th>
                <th class="text-right">
                  <span class="{% if cash_flow.investing_total >= 0 %}text-success{% else %}text-danger{% endif %}">
                    <strong>{{ cash_flow.investing_total|number_format(2) }}</strong>
                  </span>
                </th>
              </tr>
            </tfoot>
          </table>
        </div>
      </div>
    </div>

    {# التدفقات النقدية من الأنشطة التمويلية #}
    <div class="panel panel-success">
      <div class="panel-heading">
        <h4 class="panel-title">
          <i class="fa fa-bank"></i> {{ text_financing_activities }}
        </h4>
      </div>
      <div class="panel-body">
        <div class="table-responsive">
          <table class="table table-striped table-hover">
            <thead>
              <tr>
                <th width="70%">{{ column_description }}</th>
                <th width="30%" class="text-right">{{ column_amount }}</th>
              </tr>
            </thead>
            <tbody>
              {% for item in cash_flow.financing %}
              <tr>
                <td>{{ item.description }}</td>
                <td class="text-right">
                  <span class="{% if item.amount >= 0 %}text-success{% else %}text-danger{% endif %}">
                    {{ item.amount|number_format(2) }}
                  </span>
                </td>
              </tr>
              {% endfor %}
            </tbody>
            <tfoot>
              <tr class="success">
                <th>{{ text_net_financing_cash }}</th>
                <th class="text-right">
                  <span class="{% if cash_flow.financing_total >= 0 %}text-success{% else %}text-danger{% endif %}">
                    <strong>{{ cash_flow.financing_total|number_format(2) }}</strong>
                  </span>
                </th>
              </tr>
            </tfoot>
          </table>
        </div>
      </div>
    </div>

    {# ملخص التدفقات النقدية #}
    <div class="panel panel-primary">
      <div class="panel-heading">
        <h4 class="panel-title">
          <i class="fa fa-calculator"></i> {{ tab_summary }}
        </h4>
      </div>
      <div class="panel-body">
        <div class="table-responsive">
          <table class="table table-bordered">
            <tbody>
              <tr>
                <td width="70%"><strong>{{ text_net_operating_cash }}</strong></td>
                <td width="30%" class="text-right">
                  <span class="{% if cash_flow.operating_total >= 0 %}text-success{% else %}text-danger{% endif %}">
                    <strong>{{ cash_flow.operating_total|number_format(2) }}</strong>
                  </span>
                </td>
              </tr>
              <tr>
                <td><strong>{{ text_net_investing_cash }}</strong></td>
                <td class="text-right">
                  <span class="{% if cash_flow.investing_total >= 0 %}text-success{% else %}text-danger{% endif %}">
                    <strong>{{ cash_flow.investing_total|number_format(2) }}</strong>
                  </span>
                </td>
              </tr>
              <tr>
                <td><strong>{{ text_net_financing_cash }}</strong></td>
                <td class="text-right">
                  <span class="{% if cash_flow.financing_total >= 0 %}text-success{% else %}text-danger{% endif %}">
                    <strong>{{ cash_flow.financing_total|number_format(2) }}</strong>
                  </span>
                </td>
              </tr>
              <tr class="active">
                <td><strong>{{ text_net_change_cash }}</strong></td>
                <td class="text-right">
                  <span class="{% if cash_flow.net_change >= 0 %}text-success{% else %}text-danger{% endif %}">
                    <strong>{{ cash_flow.net_change|number_format(2) }}</strong>
                  </span>
                </td>
              </tr>
              <tr>
                <td><strong>{{ text_opening_cash }}</strong></td>
                <td class="text-right">
                  <strong>{{ cash_flow.opening_cash|number_format(2) }}</strong>
                </td>
              </tr>
              <tr class="info">
                <td><strong>{{ text_closing_cash }}</strong></td>
                <td class="text-right">
                  <strong>{{ cash_flow.closing_cash|number_format(2) }}</strong>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    {# التحليل المالي #}
    {% if cash_flow.analysis %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h4 class="panel-title">
          <i class="fa fa-pie-chart"></i> {{ tab_analysis }}
        </h4>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-md-4">
            <div class="info-box bg-aqua">
              <span class="info-box-icon"><i class="fa fa-tint"></i></span>
              <div class="info-box-content">
                <span class="info-box-text">{{ text_cash_ratio }}</span>
                <span class="info-box-number">{{ cash_flow.analysis.cash_ratio|number_format(2) }}%</span>
              </div>
            </div>
          </div>
          <div class="col-md-4">
            <div class="info-box bg-green">
              <span class="info-box-icon"><i class="fa fa-line-chart"></i></span>
              <div class="info-box-content">
                <span class="info-box-text">{{ text_operating_cash_ratio }}</span>
                <span class="info-box-number">{{ cash_flow.analysis.operating_ratio|number_format(2) }}%</span>
              </div>
            </div>
          </div>
          <div class="col-md-4">
            <div class="info-box bg-yellow">
              <span class="info-box-icon"><i class="fa fa-money"></i></span>
              <div class="info-box-content">
                <span class="info-box-text">{{ text_free_cash_flow }}</span>
                <span class="info-box-number">{{ cash_flow.analysis.free_cash_flow|number_format(0) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    {% endif %}
  </div>
</div>

<style>
@media print {
  .page-header .pull-right,
  .breadcrumb,
  .btn,
  .alert {
    display: none !important;
  }
  
  .panel {
    border: 1px solid #ddd !important;
    box-shadow: none !important;
  }
  
  .panel-heading {
    background-color: #f5f5f5 !important;
    color: #333 !important;
  }
  
  .table {
    font-size: 12px;
  }
  
  .info-box {
    border: 1px solid #ddd;
    background: #fff !important;
  }
}

.info-box {
  display: block;
  min-height: 90px;
  background: #fff;
  width: 100%;
  box-shadow: 0 1px 1px rgba(0,0,0,0.1);
  border-radius: 2px;
  margin-bottom: 15px;
}

.info-box-icon {
  border-top-left-radius: 2px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 2px;
  display: block;
  float: left;
  height: 90px;
  width: 90px;
  text-align: center;
  font-size: 45px;
  line-height: 90px;
  background: rgba(0,0,0,0.2);
}

.info-box-content {
  padding: 5px 10px;
  margin-left: 90px;
}

.info-box-text {
  text-transform: uppercase;
  font-weight: bold;
  font-size: 13px;
}

.info-box-number {
  display: block;
  font-weight: bold;
  font-size: 18px;
}

.bg-aqua { background-color: #00c0ef !important; }
.bg-green { background-color: #00a65a !important; }
.bg-yellow { background-color: #f39c12 !important; }
</style>

{{ footer }}
