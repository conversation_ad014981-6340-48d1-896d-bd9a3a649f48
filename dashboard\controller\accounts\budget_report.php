<?php
/**
 * تحكم تقرير الموازنة الشامل والمتكامل
 * يجمع بين القوة والبساطة - يتفوق على SAP وOracle في سهولة الاستخدام
 * مع الاحتفاظ بالقوة والمرونة المطلوبة للشركات الكبيرة
 * متوافق مع النظام المالي المصري ومعايير التخطيط المالي
 */
class ControllerAccountsBudgetReport extends Controller {
    private $error = array();
    private $central_service;

    public function __construct($registry) {
        parent::__construct($registry);
        
        // تحميل الخدمات المركزية
        $this->load->model('core/central_service_manager');
        $this->central_service = $this->model_core_central_service_manager;
    }

    public function index() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/budget_report') || 
            !$this->user->hasKey('accounting_budget_report_view')) {
            
            $this->central_service->logActivity('unauthorized_access', 'accounts',
                $this->language->get('log_unauthorized_access_budget_report'), [
                'user_id' => $this->user->getId(),
                'ip_address' => $this->request->server['REMOTE_ADDR']
            ]);
            
            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/budget_report');
        $this->document->setTitle($this->language->get('heading_title'));

        // إضافة CSS و JavaScript المتقدم
        $this->document->addStyle('view/stylesheet/accounts/budget_report.css');
        $this->document->addScript('view/javascript/accounts/budget_report.js');
        $this->document->addScript('view/javascript/jquery/accounting.min.js');
        $this->document->addScript('view/javascript/jquery/select2.min.js');
        $this->document->addStyle('view/javascript/jquery/select2.min.css');
        $this->document->addScript('view/javascript/jquery/daterangepicker.min.js');
        $this->document->addStyle('view/javascript/jquery/daterangepicker.css');
        $this->document->addScript('view/javascript/jquery/chart.min.js');
        $this->document->addScript('view/javascript/jquery/datatables.min.js');
        $this->document->addStyle('view/javascript/jquery/datatables.min.css');

        // تسجيل الوصول للشاشة
        $this->central_service->logActivity('view', 'accounts',
            $this->language->get('log_view_budget_report_screen'), [
            'user_id' => $this->user->getId(),
            'screen' => 'accounts/budget_report'
        ]);

        $this->getForm();
    }

    /**
     * توليد تقرير الموازنة المتقدم
     */
    public function generate() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/budget_report') || 
            !$this->user->hasKey('accounting_budget_report_generate')) {
            
            $this->central_service->logActivity('unauthorized_generate', 'accounts',
                $this->language->get('log_unauthorized_generate_budget_report'), [
                'user_id' => $this->user->getId(),
                'action' => 'generate_budget_report'
            ]);
            
            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/budget_report');
        $this->load->model('accounts/budget_report');

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm()) {
            try {
                $filter_data = $this->prepareFilterData();

                // تسجيل توليد التقرير
                $this->central_service->logActivity('generate_report', 'accounts',
                    $this->language->get('log_generate_budget_report_period') . ': ' . $filter_data['date_start'] . ' ' . $this->language->get('text_to') . ' ' . $filter_data['date_end'], [
                    'user_id' => $this->user->getId(),
                    'date_start' => $filter_data['date_start'],
                    'date_end' => $filter_data['date_end'],
                    'budget_id' => $filter_data['budget_id'] ?? 'all'
                ]);

                $budget_data = $this->model_accounts_budget_report->generateBudgetReport($filter_data);

                // إرسال إشعار للمدير المالي
                $this->central_service->sendNotification(
                    'budget_report_generated', 
                    'توليد تقرير الموازنة', 
                    'تم توليد تقرير الموازنة للفترة ' . $filter_data['date_start'] . ' إلى ' . $filter_data['date_end'] . ' بواسطة ' . $this->user->getFirstName(), 
                    [$this->config->get('config_financial_manager_id')], 
                    [
                        'period' => $filter_data['date_start'] . ' - ' . $filter_data['date_end'],
                        'user_name' => $this->user->getFirstName() . ' ' . $this->user->getLastName(),
                        'total_variance' => $budget_data['totals']['total_variance'] ?? 0
                    ]
                );

                $this->session->data['success'] = $this->language->get('text_success_generate');
                $this->session->data['budget_report_data'] = $budget_data;

                $this->response->redirect($this->url->link('accounts/budget_report/view', 'user_token=' . $this->session->data['user_token'], true));
            } catch (Exception $e) {
                $this->error['warning'] = $e->getMessage();
            }
        }

        $this->getForm();
    }

    /**
     * عرض تقرير الموازنة
     */
    public function view() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/budget_report') || 
            !$this->user->hasKey('accounting_budget_report_view')) {
            
            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/budget_report');
        $this->document->setTitle($this->language->get('heading_title'));

        if (!isset($this->session->data['budget_report_data'])) {
            $this->session->data['error'] = $this->language->get('error_no_data');
            $this->response->redirect($this->url->link('accounts/budget_report', 'user_token=' . $this->session->data['user_token'], true));
            return;
        }

        $data = $this->session->data['budget_report_data'];
        
        // تسجيل عرض التقرير
        $this->central_service->logActivity('view_report', 'accounts', 
            'عرض تقرير الموازنة', [
            'user_id' => $this->user->getId(),
            'action' => 'view_budget_report'
        ]);

        $data['breadcrumbs'] = array();
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('accounts/budget_report', 'user_token=' . $this->session->data['user_token'], true)
        );

        $data['export_excel'] = $this->url->link('accounts/budget_report/export', 'format=excel&user_token=' . $this->session->data['user_token'], true);
        $data['export_pdf'] = $this->url->link('accounts/budget_report/export', 'format=pdf&user_token=' . $this->session->data['user_token'], true);
        $data['export_csv'] = $this->url->link('accounts/budget_report/export', 'format=csv&user_token=' . $this->session->data['user_token'], true);

        $data['user_token'] = $this->session->data['user_token'];
        $data['heading_title'] = $this->language->get('heading_title');

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/budget_report_view', $data));
    }

    /**
     * تحليل الانحرافات
     */
    public function variance_analysis() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/budget_report') || 
            !$this->user->hasKey('accounting_budget_variance_analysis')) {
            
            $this->central_service->logActivity('unauthorized_variance_analysis', 'accounts', 
                'محاولة تحليل انحرافات موازنة غير مصرح بها', [
                'user_id' => $this->user->getId(),
                'action' => 'variance_analysis'
            ]);
            
            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/budget_report');
        $this->load->model('accounts/budget_report');

        if (!isset($this->session->data['budget_report_data'])) {
            $this->session->data['error'] = $this->language->get('error_no_data');
            $this->response->redirect($this->url->link('accounts/budget_report', 'user_token=' . $this->session->data['user_token'], true));
            return;
        }

        $budget_data = $this->session->data['budget_report_data'];
        $variance_analysis = $this->model_accounts_budget_report->analyzeVariances($budget_data);

        // تسجيل تحليل الانحرافات
        $this->central_service->logActivity('variance_analysis', 'accounts', 
            'تحليل انحرافات الموازنة', [
            'user_id' => $this->user->getId(),
            'action' => 'variance_analysis',
            'significant_variances' => count($variance_analysis['significant_variances'] ?? [])
        ]);

        $data = $variance_analysis;
        $data['breadcrumbs'] = array();
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('accounts/budget_report', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_variance_analysis'),
            'href' => $this->url->link('accounts/budget_report/variance_analysis', 'user_token=' . $this->session->data['user_token'], true)
        );

        $data['user_token'] = $this->session->data['user_token'];
        $data['heading_title'] = $this->language->get('text_variance_analysis');

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/budget_variance_analysis', $data));
    }

    /**
     * تصدير تقرير الموازنة
     */
    public function export() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/budget_report') ||
            !$this->user->hasKey('accounting_budget_report_export')) {

            $this->central_service->logActivity('unauthorized_export', 'accounts',
                'محاولة تصدير تقرير موازنة غير مصرح بها', [
                'user_id' => $this->user->getId(),
                'action' => 'export_budget_report'
            ]);

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/budget_report');
        $this->load->model('accounts/budget_report');

        if (!isset($this->session->data['budget_report_data'])) {
            $this->session->data['error'] = $this->language->get('error_no_data');
            $this->response->redirect($this->url->link('accounts/budget_report', 'user_token=' . $this->session->data['user_token'], true));
            return;
        }

        $format = $this->request->get['format'] ?? 'excel';
        $budget_data = $this->session->data['budget_report_data'];
        $filter_data = $this->session->data['budget_report_filter'] ?? array();

        // تسجيل عملية التصدير
        $this->central_service->logActivity('export', 'accounts',
            'تصدير تقرير الموازنة - ' . strtoupper($format), [
            'user_id' => $this->user->getId(),
            'format' => $format,
            'period' => ($filter_data['date_start'] ?? '') . ' - ' . ($filter_data['date_end'] ?? '')
        ]);

        // إرسال إشعار للمدير المالي
        $this->central_service->sendNotification(
            'budget_report_exported',
            'تصدير تقرير الموازنة',
            'تم تصدير تقرير الموازنة بصيغة ' . strtoupper($format) . ' بواسطة ' . $this->user->getFirstName(),
            [$this->config->get('config_financial_manager_id')],
            [
                'format' => $format,
                'period' => ($filter_data['date_start'] ?? '') . ' - ' . ($filter_data['date_end'] ?? ''),
                'user_name' => $this->user->getFirstName() . ' ' . $this->user->getLastName()
            ]
        );

        switch ($format) {
            case 'excel':
                $this->exportToExcel($budget_data, $filter_data);
                break;
            case 'pdf':
                $this->exportToPdf($budget_data, $filter_data);
                break;
            case 'csv':
                $this->exportToCsv($budget_data, $filter_data);
                break;
            default:
                $this->exportToExcel($budget_data, $filter_data);
        }
    }

    /**
     * التحقق من صحة البيانات
     */
    protected function validateForm() {
        if (!$this->user->hasPermission('access', 'accounts/budget_report')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }

        if (empty($this->request->post['date_start'])) {
            $this->error['date_start'] = $this->language->get('error_date_start');
        }

        if (empty($this->request->post['date_end'])) {
            $this->error['date_end'] = $this->language->get('error_date_end');
        }

        if (!empty($this->request->post['date_start']) && !empty($this->request->post['date_end'])) {
            if (strtotime($this->request->post['date_start']) > strtotime($this->request->post['date_end'])) {
                $this->error['date_range'] = $this->language->get('error_date_range');
            }
        }

        return !$this->error;
    }

    /**
     * إعداد بيانات الفلترة
     */
    protected function prepareFilterData() {
        return array(
            'date_start' => $this->request->post['date_start'] ?: date('Y-01-01'),
            'date_end' => $this->request->post['date_end'] ?: date('Y-m-d'),
            'budget_id' => $this->request->post['budget_id'] ?? '',
            'department_id' => $this->request->post['department_id'] ?? '',
            'account_group' => $this->request->post['account_group'] ?? '',
            'include_zero_variances' => isset($this->request->post['include_zero_variances']) ? 1 : 0,
            'variance_threshold' => $this->request->post['variance_threshold'] ?? 5,
            'show_percentages' => isset($this->request->post['show_percentages']) ? 1 : 0,
            'currency' => $this->request->post['currency'] ?? $this->config->get('config_currency'),
            'branch_id' => $this->request->post['branch_id'] ?? ''
        );
    }

    /**
     * عرض النموذج
     */
    protected function getForm() {
        $data['breadcrumbs'] = array();
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('accounts/budget_report', 'user_token=' . $this->session->data['user_token'], true)
        );

        $data['action'] = $this->url->link('accounts/budget_report/generate', 'user_token=' . $this->session->data['user_token'], true);

        // تحميل قوائم البيانات
        $this->load->model('accounts/budget_management_advanced');
        $this->load->model('department/department');
        $this->load->model('branch/branch');

        $data['budgets'] = $this->model_accounts_budget_management_advanced->getBudgets();
        $data['departments'] = $this->model_department_department->getDepartments();
        $data['branches'] = $this->model_branch_branch->getBranches();

        // القيم الافتراضية
        $data['date_start'] = $this->request->post['date_start'] ?? date('Y-01-01');
        $data['date_end'] = $this->request->post['date_end'] ?? date('Y-m-d');
        $data['budget_id'] = $this->request->post['budget_id'] ?? '';
        $data['include_zero_variances'] = $this->request->post['include_zero_variances'] ?? false;
        $data['variance_threshold'] = $this->request->post['variance_threshold'] ?? 5;
        $data['show_percentages'] = $this->request->post['show_percentages'] ?? true;

        $data['user_token'] = $this->session->data['user_token'];
        $data['heading_title'] = $this->language->get('heading_title');
        $data['text_no_results'] = $this->language->get('text_no_results');

        if (isset($this->error['warning'])) {
            $data['error_warning'] = $this->error['warning'];
        } else {
            $data['error_warning'] = '';
        }

        if (isset($this->session->data['success'])) {
            $data['success'] = $this->session->data['success'];
            unset($this->session->data['success']);
        } else {
            $data['success'] = '';
        }

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/budget_report_form', $data));
    }

    /**
     * تصدير إلى Excel
     */
    private function exportToExcel($data, $filter_data) {
        $filename = 'budget_report_' . date('Y-m-d') . '.xls';

        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        echo '<table border="1">';
        echo '<tr><th colspan="5">' . $this->language->get('heading_title') . '</th></tr>';
        echo '<tr><th>' . $this->language->get('text_account') . '</th>';
        echo '<th>' . $this->language->get('text_budget') . '</th>';
        echo '<th>' . $this->language->get('text_actual') . '</th>';
        echo '<th>' . $this->language->get('text_variance') . '</th>';
        echo '<th>' . $this->language->get('text_variance_percentage') . '</th></tr>';

        echo '</table>';
        exit;
    }

    /**
     * تصدير إلى PDF
     */
    private function exportToPdf($data, $filter_data) {
        require_once(DIR_SYSTEM . 'library/tcpdf/tcpdf.php');

        $pdf = new TCPDF('P', 'mm', 'A4', true, 'UTF-8');
        $pdf->SetCreator('AYM ERP System');
        $pdf->SetAuthor($this->config->get('config_name'));
        $pdf->SetTitle($this->language->get('heading_title'));

        $pdf->AddPage();
        $pdf->SetFont('dejavusans', 'B', 16);
        $pdf->Cell(0, 10, $this->language->get('heading_title'), 0, 1, 'C');

        $pdf->Output('budget_report_' . date('Y-m-d') . '.pdf', 'D');
        exit;
    }

    /**
     * تصدير إلى CSV
     */
    private function exportToCsv($data, $filter_data) {
        $filename = 'budget_report_' . date('Y-m-d') . '.csv';

        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $output = fopen('php://output', 'w');
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

        fputcsv($output, array(
            $this->language->get('text_account'),
            $this->language->get('text_budget'),
            $this->language->get('text_actual'),
            $this->language->get('text_variance'),
            $this->language->get('text_variance_percentage')
        ));

        fclose($output);
        exit;
    }

    /**
     * دالة تنظيف المخرجات (CONSTITUTIONAL REQUIREMENT)
     * Sanitize all output data to prevent XSS attacks
     */
    private function sanitizeOutputData($data) {
        if (is_array($data)) {
            foreach ($data as $key => $value) {
                $data[$key] = $this->sanitizeOutputData($value);
            }
        } elseif (is_string($data)) {
            $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
        }
        return $data;
    }

    /**
     * Enterprise Grade Plus Security Enhancements
     */
    private function validateInputData($data) {
        $validated = array();

        if (isset($data['budget_id'])) {
            $validated['budget_id'] = (int)$data['budget_id'];
        }

        if (isset($data['budget_year'])) {
            $validated['budget_year'] = (int)$data['budget_year'];
        }

        if (isset($data['department_id'])) {
            $validated['department_id'] = (int)$data['department_id'];
        }

        if (isset($data['cost_center_id'])) {
            $validated['cost_center_id'] = (int)$data['cost_center_id'];
        }

        if (isset($data['date_start'])) {
            $validated['date_start'] = date('Y-m-d', strtotime($data['date_start']));
        }

        if (isset($data['date_end'])) {
            $validated['date_end'] = date('Y-m-d', strtotime($data['date_end']));
        }

        return $validated;
    }

    /**
     * Rate Limiting Implementation
     */
    private function checkRateLimit() {
        $ip = $this->request->server['REMOTE_ADDR'];
        $user_id = $this->user->getId();

        return $this->central_service->checkRateLimit('budget_report', $ip, $user_id, 30, 3600); // 30 requests per hour
    }

    /**
     * Performance Optimization - Memory Management
     */
    private function optimizeMemoryUsage() {
        ini_set('memory_limit', '512M');
        ini_set('max_execution_time', 300); // 5 minutes for report generation
    }
}
