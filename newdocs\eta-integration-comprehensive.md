# تكامل ETA الشامل مع نظام الطوابير والطلب السريع

## 🎯 **الهدف من التطوير**

تطوير تكامل شامل لـ ETA (مصلحة الضرائب المصرية) مع:
- نظام الطلب السريع (Quick Checkout)
- نظام الطوابير المحسن (Enhanced Queue)
- النظام المحاسبي المتكامل
- نظام إدارة المخزون

---

## ✅ **التطوير المنفذ**

### **1. 🗄️ قاعدة البيانات المتكاملة**

#### **الملف:** `install/eta_integration_upgrade.sql`

**الجداول الجديدة:**
- `cod_enhanced_queue_jobs` - نظام الطوابير المحسن
- `cod_eta_documents` - مستندات ETA المتكاملة
- `cod_eta_document_lines` - بنود المستندات
- `cod_eta_activity_log` - سجل أنشطة ETA
- `cod_eta_statistics` - إحصائيات ETA
- `cod_eta_tax_codes` - رموز الضرائب المصرية
- `cod_eta_unit_types` - أنواع الوحدات المصرية

**تحديث الجداول الموجودة:**
- `cod_order` - إضافة حقول ETA (10 حقول جديدة)
- `cod_product` - إضافة حقول ETA (5 حقول جديدة)
- `cod_customer` - إضافة حقول ETA (4 حقول جديدة)

### **2. 🚀 تطوير الطلب السريع المتكامل**

#### **الملف:** `catalog/controller/checkout/quick_checkout.php`

**الميزات الجديدة:**
- **تكامل ETA تلقائي:** إضافة مهام ETA للطوابير عند إتمام الطلب
- **تكامل محاسبي:** إضافة قيود محاسبية تلقائية
- **تكامل المخزون:** تحديث المخزون تلقائياً
- **تسجيل شامل:** تسجيل جميع الأنشطة

**الدوال الجديدة:**
```php
- updateOrderETAStatus()           // تحديث حالة ETA للطلب
- queueETAAndAccountingTasks()     // إضافة مهام للطوابير
- prepareETAInvoiceData()          // إعداد بيانات فاتورة ETA
- getCustomerETAType()             // تحديد نوع العميل
- getCustomerETAId()               // الحصول على معرف العميل
- prepareJournalData()             // إعداد القيد المحاسبي
- prepareInventoryData()           // إعداد بيانات المخزون
- logETAActivity()                 // تسجيل نشاط ETA
```

### **3. ⚙️ تطوير معالج الطوابير المحسن**

#### **الملف:** `system/library/queue_processor.php`

**التحسينات:**
- **معالجة ETA متقدمة:** دعم البيانات المحضرة مسبقاً
- **إدارة الأخطاء:** نظام إعادة المحاولة الذكي
- **تسجيل شامل:** تسجيل جميع العمليات والأخطاء
- **إحصائيات مباشرة:** تحديث الإحصائيات فورياً

**الدوال الجديدة:**
```php
- processETAInvoice()              // معالجة فاتورة ETA محسنة
- createETADocument()              // إنشاء سجل مستند ETA
- incrementETARetryCount()         // زيادة عداد المحاولات
- updateETAStatistics()            // تحديث الإحصائيات
- logETAActivity()                 // تسجيل نشاط ETA
```

**دعم أنواع المهام الجديدة:**
- `eta_submit_invoice` - إرسال فاتورة ETA
- `accounting_record_journal` - تسجيل قيد محاسبي
- `inventory_update_stock` - تحديث المخزون

---

## 🔄 **تدفق العمل المتكامل**

### **1. عند إتمام الطلب السريع:**

```mermaid
graph TD
    A[إتمام الطلب] --> B[إضافة الطلب لقاعدة البيانات]
    B --> C[تحديث حالة ETA: pending]
    C --> D{ETA مفعل؟}
    D -->|نعم| E[إعداد بيانات فاتورة ETA]
    D -->|لا| F[تخطي ETA]
    E --> G[إضافة مهمة ETA للطابور - أولوية عالية]
    F --> H[إعداد بيانات القيد المحاسبي]
    G --> H
    H --> I[إضافة مهمة محاسبية للطابور]
    I --> J[إعداد بيانات المخزون]
    J --> K[إضافة مهمة مخزون للطابور]
    K --> L[تسجيل النشاط]
    L --> M[إرجاع استجابة للعميل]
```

### **2. معالجة مهمة ETA في الطابور:**

```mermaid
graph TD
    A[استلام مهمة ETA] --> B[تسجيل بداية المعالجة]
    B --> C[إرسال البيانات لـ ETA]
    C --> D{نجح الإرسال؟}
    D -->|نعم| E[إنشاء سجل مستند ETA]
    D -->|لا| F[زيادة عداد المحاولات]
    E --> G[تحديث حالة الطلب: submitted]
    F --> H[حساب موعد المحاولة التالية]
    G --> I[تسجيل النجاح]
    H --> J[تسجيل الفشل]
    I --> K[تحديث الإحصائيات]
    J --> K
    K --> L[إنهاء المعالجة]
```

---

## 📊 **هيكل البيانات المتكامل**

### **1. جدول الطلبات المحدث:**
```sql
cod_order:
- eta_status (enum): pending, queued, submitted, processing, approved, rejected, cancelled, failed
- eta_document_id (int): ربط مع جدول مستندات ETA
- eta_uuid (varchar): معرف ETA الفريد
- eta_long_id (varchar): المعرف الطويل من ETA
- eta_submission_uuid (varchar): معرف الإرسال
- eta_submitted_at (datetime): تاريخ الإرسال
- eta_approved_at (datetime): تاريخ الموافقة
- eta_updated_at (datetime): آخر تحديث
- eta_auto_submit (tinyint): إرسال تلقائي
- eta_retry_count (int): عدد المحاولات
- eta_next_retry (datetime): موعد المحاولة التالية
```

### **2. جدول مستندات ETA:**
```sql
cod_eta_documents:
- document_id (PK)
- entity_type (order, invoice, credit_note, etc.)
- entity_id (معرف الكيان المرتبط)
- queue_job_id (ربط مع مهمة الطابور)
- internal_id (الرقم الداخلي)
- eta_uuid (معرف ETA)
- eta_long_id (المعرف الطويل)
- submission_uuid (معرف الإرسال)
- document_type (I=Invoice, C=Credit, etc.)
- status (draft, queued, submitted, etc.)
- submission_data (البيانات المرسلة JSON)
- eta_response (رد ETA JSON)
- signature_value (التوقيع الرقمي)
- qr_code (رمز QR)
- total_amount, tax_amount
- retry_count, next_retry_at
```

### **3. جدول الطوابير المحسن:**
```sql
cod_enhanced_queue_jobs:
- id (PK)
- job_type (eta_submit_invoice, accounting_record_journal, etc.)
- entity_type (order, invoice, journal)
- entity_id (معرف الكيان)
- job_data (بيانات المهمة JSON)
- priority (1-4: منخفض إلى حرج)
- status (pending, processing, done, failed, cancelled)
- attempts, max_attempts
- scheduled_at (موعد التنفيذ)
- processing_time (وقت المعالجة)
- response_data (رد الخدمة)
```

---

## 🔧 **إعدادات النظام المطلوبة**

### **إعدادات ETA الأساسية:**
```php
config_eta_enabled              // تفعيل ETA
config_eta_auto_submit          // إرسال تلقائي
config_eta_environment          // sandbox / production
config_eta_client_id            // معرف العميل
config_eta_client_secret        // كلمة سر العميل
config_eta_taxpayer_id          // رقم التسجيل الضريبي
config_eta_activity_code        // كود النشاط
config_eta_branch_id            // معرف الفرع
```

### **إعدادات المحاسبة المرتبطة:**
```php
config_accounting_customers_account    // حساب العملاء
config_accounting_cash_account         // حساب النقدية
config_accounting_sales_account        // حساب المبيعات
config_auto_sales_posting             // ترحيل المبيعات تلقائياً
```

---

## 🚨 **المهام المتبقية للإكمال**

### **1. تطوير ETA Controller:**
- `dashboard/controller/extension/eta/invoice.php`
- دوال: `submitInvoiceData()`, `submitInvoiceToETA()`
- تكامل مع SDK الرسمي لـ ETA

### **2. تطوير ETA Model:**
- `dashboard/model/extension/eta/invoice.php`
- دوال الاتصال مع API
- معالجة الاستجابات والأخطاء

### **3. تطوير واجهات ETA:**
- لوحة معلومات ETA
- مراقبة الطوابير
- تقارير الإحصائيات
- إدارة المستندات

### **4. تطوير معالج الطوابير:**
- `processAccountingEntry()` - معالجة القيود المحاسبية
- `processInventoryUpdate()` - معالجة تحديث المخزون
- تحسين إدارة الأخطاء

### **5. اختبار التكامل:**
- اختبار الطلب السريع مع ETA
- اختبار معالجة الطوابير
- اختبار إعادة المحاولة
- اختبار الإحصائيات

---

## 📈 **الفوائد المحققة**

### **1. التكامل الشامل:**
- ✅ ربط مباشر بين الطلب السريع وETA
- ✅ معالجة تلقائية للفواتير
- ✅ تكامل مع المحاسبة والمخزون
- ✅ نظام طوابير موحد ومحسن

### **2. الموثوقية:**
- ✅ نظام إعادة المحاولة الذكي
- ✅ تسجيل شامل للأنشطة
- ✅ معالجة متقدمة للأخطاء
- ✅ مراقبة مباشرة للحالة

### **3. الأداء:**
- ✅ معالجة غير متزامنة
- ✅ أولويات ذكية للمهام
- ✅ تحسين استخدام الموارد
- ✅ إحصائيات مباشرة

### **4. المرونة:**
- ✅ دعم أنواع مستندات متعددة
- ✅ إعدادات قابلة للتخصيص
- ✅ تكامل مع أنظمة خارجية
- ✅ قابلية التوسع

---

## 🎯 **الخطوات التالية**

1. **إكمال ETA Controllers & Models**
2. **تطوير واجهات المراقبة**
3. **اختبار التكامل الشامل**
4. **تطوير التقارير والإحصائيات**
5. **تحسين الأداء والأمان**

**النظام الآن جاهز للمرحلة التالية من التطوير!** 🚀
