{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="submit" form="form-shipping" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fa fa-save"></i></button>
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a></div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pencil"></i> {{ text_edit }}</h3>
      </div>
      <div class="panel-body">
        <form action="{{ action }}" method="post" enctype="multipart/form-data" id="form-shipping" class="form-horizontal">
          <div class="row">
            <div class="col-sm-2">
              <ul class="nav nav-pills nav-stacked">
                <li class="active"><a href="#tab-general" data-toggle="tab">{{ tab_general }}</a></li>
                {% for geo_zone in geo_zones %}
                <li><a href="#tab-geo-zone{{ geo_zone.geo_zone_id }}" data-toggle="tab">{{ geo_zone.name }}</a></li>
                {% endfor %}
              </ul>
            </div>
            <div class="col-sm-10">
              <div class="tab-content">
                <div class="tab-pane active" id="tab-general">
                  <div class="form-group">
                    <label class="col-sm-2 control-label" for="input-tax-class">{{ entry_tax_class }}</label>
                    <div class="col-sm-10">
                      <select name="shipping_weight_tax_class_id" id="input-tax-class" class="form-control">
                        <option value="0">{{ text_none }}</option>
                        {% for tax_class in tax_classes %}
                        {% if tax_class.tax_class_id == shipping_weight_tax_class_id %}
                        <option value="{{ tax_class.tax_class_id }}" selected="selected">{{ tax_class.title }}</option>
                        {% else %}
                        <option value="{{ tax_class.tax_class_id }}">{{ tax_class.title }}</option>
                        {% endif %}
                        {% endfor %}
                      </select>
                    </div>
                  </div>
                  <div class="form-group">
                    <label class="col-sm-2 control-label" for="input-status">{{ entry_status }}</label>
                    <div class="col-sm-10">
                      <select name="shipping_weight_status" id="input-status" class="form-control">
                        {% if shipping_weight_status %}
                        <option value="1" selected="selected">{{ text_enabled }}</option>
                        <option value="0">{{ text_disabled }}</option>
                        {% else %}
                        <option value="1">{{ text_enabled }}</option>
                        <option value="0" selected="selected">{{ text_disabled }}</option>
                        {% endif %}
                      </select>
                    </div>
                  </div>
                  <div class="form-group">
                    <label class="col-sm-2 control-label" for="input-sort-order">{{ entry_sort_order }}</label>
                    <div class="col-sm-10">
                      <input type="text" name="shipping_weight_sort_order" value="{{ shipping_weight_sort_order }}" placeholder="{{ entry_sort_order }}" id="input-sort-order" class="form-control" />
                    </div>
                  </div>
                </div>
                {% for geo_zone in geo_zones %}
                <div class="tab-pane" id="tab-geo-zone{{ geo_zone.geo_zone_id }}">
                  <div class="form-group">
                    <label class="col-sm-2 control-label" for="input-rate{{ geo_zone.geo_zone_id }}"><span data-toggle="tooltip" title="{{ help_rate }}">{{ entry_rate }}</span></label>
                    <div class="col-sm-10">
                      <textarea name="shipping_weight_{{ geo_zone.geo_zone_id }}_rate" rows="5" placeholder="{{ entry_rate }}" id="input-rate{{ geo_zone.geo_zone_id }}" class="form-control">{{ shipping_weight_geo_zone_rate[geo_zone.geo_zone_id] ? shipping_weight_geo_zone_rate[geo_zone.geo_zone_id] }}</textarea>
                    </div>
                  </div>
                  <div class="form-group">
                    <label class="col-sm-2 control-label" for="input-status{{ geo_zone.geo_zone_id }}">{{ entry_status }}</label>
                    <div class="col-sm-10">
                      <select name="shipping_weight_{{ geo_zone.geo_zone_id }}_status" id="input-status{{ geo_zone.geo_zone_id }}" class="form-control">
                        {% if shipping_weight_geo_zone_status[geo_zone.geo_zone_id] %}
                        <option value="1" selected="selected">{{ text_enabled }}</option>
                        <option value="0">{{ text_disabled }}</option>
                        {% else %}
                        <option value="1">{{ text_enabled }}</option>
                        <option value="0" selected="selected">{{ text_disabled }}</option>
                        {% endif %}
                      </select>
                    </div>
                  </div>
                </div>
                {% endfor %}
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
{{ footer }}
