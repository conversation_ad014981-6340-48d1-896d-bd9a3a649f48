<?php
// Heading
$_['heading_title']          = 'Attendance Management';

// Text
$_['text_filter']            = 'Filter';
$_['text_select_employee']   = 'Select Employee';
$_['text_date']              = 'Date';
$_['text_add_attendance']    = 'Add Attendance Record';
$_['text_edit_attendance']   = 'Edit Attendance Record';
$_['text_present']           = 'Present';
$_['text_absent']            = 'Absent';
$_['text_late']              = 'Late';
$_['text_on_leave']          = 'On Leave';
$_['text_attendance_list']   = 'Attendance Records List';
$_['text_ajax_error']        = 'An error occurred while communicating with the server';
$_['text_confirm_delete']    = 'Are you sure you want to delete?';
$_['text_notes']             = 'Notes';
$_['text_employee']          = 'Employee';
$_['text_status']            = 'Status';
$_['text_checkin']           = 'Check-In Time';
$_['text_checkout']          = 'Check-Out Time';
$_['text_date_start']        = 'Start Date';
$_['text_date_end']          = 'End Date';

// Button
$_['button_filter']          = 'Filter';
$_['button_reset']           = 'Reset';
$_['button_add_attendance']  = 'Add Attendance';
$_['button_close']           = 'Close';
$_['button_save']            = 'Save';

// Column
$_['column_employee']        = 'Employee';
$_['column_date']            = 'Date';
$_['column_checkin']         = 'Check-In';
$_['column_checkout']        = 'Check-Out';
$_['column_status']          = 'Status';
$_['column_actions']         = 'Actions';

// Error / Success
$_['error_not_found']        = 'Record not found!';
$_['error_invalid_request']  = 'Invalid request!';
$_['error_permission']       = 'Warning: You do not have permission to modify attendance!';
$_['error_required']         = 'Warning: Please fill in the required fields!';
$_['text_success_add']       = 'Attendance record added successfully!';
$_['text_success_edit']      = 'Attendance record updated successfully!';
$_['text_success_delete']    = 'Attendance record deleted successfully!';
