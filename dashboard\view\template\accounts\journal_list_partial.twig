          {% if journals %}
            {% for journal in journals %}
              <tr href="{{ journal.edit }}" {% if journal.is_cancelled=='1' %} style="background-color:#ffeaea;" {% endif %}>
                <td class="text-center"><input type="checkbox" name="selected[]" value="{{ journal.journal_id }}" /></td>                  
                <td class="text-center">{{ journal.thedate }}</td>
                <td class="text-center">{{ journal.journal_id }}</td>
                <td class="text-center">{{ journal.description }}</td>
                <td class="text-center">{{ journal.total_debit }}</td>
                <td class="text-center">{{ journal.total_credit }}</td>
                <td class="text-center">
                  <i class="fa fa-circle" style="color: {{ journal.is_balanced ? 'green' : 'red' }};"></i>
                  {{ journal.is_balanced ? text_balanced : text_unbalanced }} {% if journal.is_cancelled=='1' %} <br> (<span style="color:red">{{text_is_cancelled}}</span>) {% endif %}
                </td>
              </tr>
            
            {% endfor %}
          {% else %}
            <tr><td class="text-center" colspan="7">{{ text_no_results }}</td></tr>
          {% endif %}