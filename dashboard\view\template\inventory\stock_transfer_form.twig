{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="submit" form="form-stock-transfer" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary">
          <i class="fa fa-save"></i>
        </button>
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default">
          <i class="fa fa-reply"></i>
        </a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible">
      <i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pencil"></i> {{ text_form }}</h3>
      </div>
      <div class="panel-body">
        <form action="{{ action }}" method="post" enctype="multipart/form-data" id="form-stock-transfer" class="form-horizontal">
          
          <!-- معلومات أساسية -->
          <fieldset>
            <legend>المعلومات الأساسية</legend>
            
            <div class="form-group required">
              <label class="col-sm-2 control-label" for="input-transfer-number">{{ entry_transfer_number }}</label>
              <div class="col-sm-10">
                <input type="text" name="transfer_number" value="{{ transfer_number }}" placeholder="{{ entry_transfer_number }}" id="input-transfer-number" class="form-control" readonly />
              </div>
            </div>
            
            <div class="form-group required">
              <label class="col-sm-2 control-label" for="input-transfer-name">{{ entry_transfer_name }}</label>
              <div class="col-sm-10">
                <input type="text" name="transfer_name" value="{{ transfer_name }}" placeholder="{{ entry_transfer_name }}" id="input-transfer-name" class="form-control" />
                {% if error_transfer_name %}
                <div class="text-danger">{{ error_transfer_name }}</div>
                {% endif %}
              </div>
            </div>
            
            <div class="form-group required">
              <label class="col-sm-2 control-label" for="input-transfer-type">{{ entry_transfer_type }}</label>
              <div class="col-sm-4">
                <select name="transfer_type" id="input-transfer-type" class="form-control">
                  {% for type in transfer_types %}
                  <option value="{{ type.value }}"{% if type.value == transfer_type %} selected="selected"{% endif %}>{{ type.text }}</option>
                  {% endfor %}
                </select>
              </div>
              <label class="col-sm-2 control-label" for="input-priority">{{ entry_priority }}</label>
              <div class="col-sm-4">
                <select name="priority" id="input-priority" class="form-control">
                  {% for priority in priorities %}
                  <option value="{{ priority.value }}"{% if priority.value == priority %} selected="selected"{% endif %}>{{ priority.text }}</option>
                  {% endfor %}
                </select>
              </div>
            </div>
            
            <div class="form-group required">
              <label class="col-sm-2 control-label" for="input-from-branch">{{ entry_from_branch }}</label>
              <div class="col-sm-4">
                <select name="from_branch_id" id="input-from-branch" class="form-control">
                  <option value="">{{ text_select }}</option>
                  {% for branch in branches %}
                  <option value="{{ branch.branch_id }}"{% if branch.branch_id == from_branch_id %} selected="selected"{% endif %}>{{ branch.name }}</option>
                  {% endfor %}
                </select>
                {% if error_from_branch_id %}
                <div class="text-danger">{{ error_from_branch_id }}</div>
                {% endif %}
              </div>
              <label class="col-sm-2 control-label" for="input-to-branch">{{ entry_to_branch }}</label>
              <div class="col-sm-4">
                <select name="to_branch_id" id="input-to-branch" class="form-control">
                  <option value="">{{ text_select }}</option>
                  {% for branch in branches %}
                  <option value="{{ branch.branch_id }}"{% if branch.branch_id == to_branch_id %} selected="selected"{% endif %}>{{ branch.name }}</option>
                  {% endfor %}
                </select>
                {% if error_to_branch_id %}
                <div class="text-danger">{{ error_to_branch_id }}</div>
                {% endif %}
              </div>
            </div>
            
            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-reason">{{ entry_reason }}</label>
              <div class="col-sm-10">
                <select name="reason_id" id="input-reason" class="form-control">
                  <option value="">{{ text_select }}</option>
                  {% for reason in transfer_reasons %}
                  <option value="{{ reason.reason_id }}"{% if reason.reason_id == reason_id %} selected="selected"{% endif %}>{{ reason.name }}</option>
                  {% endfor %}
                </select>
              </div>
            </div>
            
            <div class="form-group required">
              <label class="col-sm-2 control-label" for="input-request-date">{{ entry_request_date }}</label>
              <div class="col-sm-4">
                <input type="date" name="request_date" value="{{ request_date }}" id="input-request-date" class="form-control" />
                {% if error_request_date %}
                <div class="text-danger">{{ error_request_date }}</div>
                {% endif %}
              </div>
              <label class="col-sm-2 control-label" for="input-expected-delivery-date">{{ entry_expected_delivery_date }}</label>
              <div class="col-sm-4">
                <input type="date" name="expected_delivery_date" value="{{ expected_delivery_date }}" id="input-expected-delivery-date" class="form-control" />
              </div>
            </div>
            
            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-notes">{{ entry_notes }}</label>
              <div class="col-sm-10">
                <textarea name="notes" rows="3" placeholder="{{ entry_notes }}" id="input-notes" class="form-control">{{ notes }}</textarea>
              </div>
            </div>
          </fieldset>
          
          <!-- عناصر النقل -->
          <fieldset>
            <legend>
              عناصر النقل
              <button type="button" class="btn btn-success btn-sm pull-right" id="add-item">
                <i class="fa fa-plus"></i> {{ button_add_item }}
              </button>
            </legend>
            
            {% if error_transfer_items %}
            <div class="alert alert-danger">
              <i class="fa fa-exclamation-circle"></i> {{ error_transfer_items }}
            </div>
            {% endif %}
            
            <div class="table-responsive">
              <table class="table table-striped table-bordered" id="transfer-items-table">
                <thead>
                  <tr>
                    <th style="width: 30%;">{{ entry_product }}</th>
                    <th style="width: 10%;">{{ entry_quantity }}</th>
                    <th style="width: 12%;">{{ entry_unit_cost }}</th>
                    <th style="width: 12%;">{{ entry_total_cost }}</th>
                    <th style="width: 10%;">{{ entry_lot_number }}</th>
                    <th style="width: 10%;">{{ entry_expiry_date }}</th>
                    <th style="width: 13%;">{{ entry_item_notes }}</th>
                    <th style="width: 3%;">{{ button_remove_item }}</th>
                  </tr>
                </thead>
                <tbody id="transfer-items">
                  {% set item_row = 0 %}
                  {% if transfer_items %}
                  {% for item in transfer_items %}
                  <tr id="item-row-{{ item_row }}">
                    <td>
                      <input type="text" name="transfer_items[{{ item_row }}][product_name]" value="{{ item.product_name }}" placeholder="{{ entry_product }}" class="form-control product-autocomplete" data-row="{{ item_row }}" />
                      <input type="hidden" name="transfer_items[{{ item_row }}][product_id]" value="{{ item.product_id }}" />
                    </td>
                    <td>
                      <input type="number" name="transfer_items[{{ item_row }}][quantity]" value="{{ item.quantity }}" placeholder="{{ entry_quantity }}" class="form-control quantity-input" data-row="{{ item_row }}" step="0.01" />
                    </td>
                    <td>
                      <input type="number" name="transfer_items[{{ item_row }}][unit_cost]" value="{{ item.unit_cost }}" placeholder="{{ entry_unit_cost }}" class="form-control unit-cost-input" data-row="{{ item_row }}" step="0.01" />
                    </td>
                    <td>
                      <input type="text" name="transfer_items[{{ item_row }}][total_cost]" value="{{ item.total_cost }}" class="form-control total-cost-display" data-row="{{ item_row }}" readonly />
                    </td>
                    <td>
                      <input type="text" name="transfer_items[{{ item_row }}][lot_number]" value="{{ item.lot_number }}" placeholder="{{ entry_lot_number }}" class="form-control" />
                    </td>
                    <td>
                      <input type="date" name="transfer_items[{{ item_row }}][expiry_date]" value="{{ item.expiry_date }}" class="form-control" />
                    </td>
                    <td>
                      <input type="text" name="transfer_items[{{ item_row }}][notes]" value="{{ item.notes }}" placeholder="{{ entry_item_notes }}" class="form-control" />
                    </td>
                    <td class="text-center">
                      <button type="button" class="btn btn-danger btn-sm remove-item" data-row="{{ item_row }}">
                        <i class="fa fa-minus"></i>
                      </button>
                    </td>
                  </tr>
                  {% set item_row = item_row + 1 %}
                  {% endfor %}
                  {% endif %}
                </tbody>
                <tfoot>
                  <tr>
                    <td colspan="3" class="text-right"><strong>الإجمالي:</strong></td>
                    <td><strong id="grand-total">0.00</strong></td>
                    <td colspan="4"></td>
                  </tr>
                </tfoot>
              </table>
            </div>
          </fieldset>
          
        </form>
      </div>
    </div>
  </div>
</div>

<script type="text/template" id="item-row-template">
<tr id="item-row-{row}">
  <td>
    <input type="text" name="transfer_items[{row}][product_name]" value="" placeholder="{{ entry_product }}" class="form-control product-autocomplete" data-row="{row}" />
    <input type="hidden" name="transfer_items[{row}][product_id]" value="" />
  </td>
  <td>
    <input type="number" name="transfer_items[{row}][quantity]" value="" placeholder="{{ entry_quantity }}" class="form-control quantity-input" data-row="{row}" step="0.01" />
  </td>
  <td>
    <input type="number" name="transfer_items[{row}][unit_cost]" value="" placeholder="{{ entry_unit_cost }}" class="form-control unit-cost-input" data-row="{row}" step="0.01" />
  </td>
  <td>
    <input type="text" name="transfer_items[{row}][total_cost]" value="0.00" class="form-control total-cost-display" data-row="{row}" readonly />
  </td>
  <td>
    <input type="text" name="transfer_items[{row}][lot_number]" value="" placeholder="{{ entry_lot_number }}" class="form-control" />
  </td>
  <td>
    <input type="date" name="transfer_items[{row}][expiry_date]" value="" class="form-control" />
  </td>
  <td>
    <input type="text" name="transfer_items[{row}][notes]" value="" placeholder="{{ entry_item_notes }}" class="form-control" />
  </td>
  <td class="text-center">
    <button type="button" class="btn btn-danger btn-sm remove-item" data-row="{row}">
      <i class="fa fa-minus"></i>
    </button>
  </td>
</tr>
</script>

<script type="text/javascript">
var item_row = {{ transfer_items|length > 0 ? transfer_items|length : 0 }};

$(document).ready(function() {
    $('#add-item').on('click', function() {
        var html = $('#item-row-template').html();
        html = html.replace(/{row}/g, item_row);
        $('#transfer-items').append(html);
        item_row++;
    });
    
    $(document).on('click', '.remove-item', function() {
        var row = $(this).data('row');
        $('#item-row-' + row).remove();
        calculateGrandTotal();
    });
    
    $(document).on('input', '.quantity-input, .unit-cost-input', function() {
        var row = $(this).data('row');
        calculateItemTotal(row);
        calculateGrandTotal();
    });
    
    if ($('#transfer-items tr').length === 0) {
        $('#add-item').click();
    }
    
    calculateGrandTotal();
});

function calculateItemTotal(row) {
    var quantity = parseFloat($('input[name="transfer_items[' + row + '][quantity]"]').val()) || 0;
    var unitCost = parseFloat($('input[name="transfer_items[' + row + '][unit_cost]"]').val()) || 0;
    var total = quantity * unitCost;
    $('input[name="transfer_items[' + row + '][total_cost]"]').val(total.toFixed(2));
}

function calculateGrandTotal() {
    var grandTotal = 0;
    $('.total-cost-display').each(function() {
        var value = parseFloat($(this).val()) || 0;
        grandTotal += value;
    });
    $('#grand-total').text(grandTotal.toFixed(2));
}
</script>

{{ footer }}
