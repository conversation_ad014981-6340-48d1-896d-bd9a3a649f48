{{ header }}
{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="button" data-bs-toggle="tooltip" title="{{ button_filter }}" onclick="$('#filter-transfer').toggleClass('d-none');" class="btn btn-light d-md-none d-lg-none"><i class="fas fa-filter"></i></button>
        <a href="{{ add }}" data-bs-toggle="tooltip" title="{{ button_add }}" class="btn btn-primary"><i class="fas fa-plus"></i></a>
        <button type="button" data-bs-toggle="tooltip" title="{{ button_bulk_actions }}" onclick="$('#bulk-actions').toggleClass('d-none');" class="btn btn-secondary"><i class="fas fa-tasks"></i></button>
        <a href="{{ export }}" data-bs-toggle="tooltip" title="{{ button_export }}" class="btn btn-success"><i class="fas fa-file-excel"></i></a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ol class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
          <li class="breadcrumb-item"><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    
    <!-- إحصائيات التحويلات -->
    <div class="row mb-3">
      <div class="col-lg-3 col-md-6">
        <div class="card bg-primary text-white">
          <div class="card-body">
            <div class="d-flex justify-content-between">
              <div>
                <h4>{{ summary.total_transfers }}</h4>
                <p class="mb-0">{{ text_total_transfers }}</p>
              </div>
              <div class="align-self-center">
                <i class="fas fa-exchange-alt fa-2x"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-md-6">
        <div class="card bg-warning text-white">
          <div class="card-body">
            <div class="d-flex justify-content-between">
              <div>
                <h4>{{ summary.pending_transfers }}</h4>
                <p class="mb-0">{{ text_pending_transfers }}</p>
              </div>
              <div class="align-self-center">
                <i class="fas fa-clock fa-2x"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-md-6">
        <div class="card bg-info text-white">
          <div class="card-body">
            <div class="d-flex justify-content-between">
              <div>
                <h4>{{ summary.in_transit_transfers }}</h4>
                <p class="mb-0">{{ text_in_transit_transfers }}</p>
              </div>
              <div class="align-self-center">
                <i class="fas fa-truck fa-2x"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-md-6">
        <div class="card bg-success text-white">
          <div class="card-body">
            <div class="d-flex justify-content-between">
              <div>
                <h4>{{ summary.completed_transfers }}</h4>
                <p class="mb-0">{{ text_completed_transfers }}</p>
              </div>
              <div class="align-self-center">
                <i class="fas fa-check-circle fa-2x"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="row">
      <!-- فلاتر التحويلات -->
      <div id="filter-transfer" class="col-lg-3 col-md-12 order-lg-last d-none d-lg-block mb-3">
        <div class="card">
          <div class="card-header"><i class="fas fa-filter"></i> {{ text_filter }}</div>
          <div class="card-body">
            <div class="mb-3">
              <label for="input-transfer-number" class="form-label">{{ entry_transfer_number }}</label>
              <input type="text" name="filter_transfer_number" value="{{ filter_transfer_number }}" placeholder="{{ entry_transfer_number }}" id="input-transfer-number" class="form-control"/>
            </div>
            <div class="mb-3">
              <label for="input-from-warehouse" class="form-label">{{ entry_from_warehouse }}</label>
              <select name="filter_from_warehouse_id" id="input-from-warehouse" class="form-select">
                <option value="">{{ text_all_warehouses }}</option>
                {% for warehouse in warehouses %}
                  <option value="{{ warehouse.warehouse_id }}" {% if warehouse.warehouse_id == filter_from_warehouse_id %}selected="selected"{% endif %}>{{ warehouse.name }}</option>
                {% endfor %}
              </select>
            </div>
            <div class="mb-3">
              <label for="input-to-warehouse" class="form-label">{{ entry_to_warehouse }}</label>
              <select name="filter_to_warehouse_id" id="input-to-warehouse" class="form-select">
                <option value="">{{ text_all_warehouses }}</option>
                {% for warehouse in warehouses %}
                  <option value="{{ warehouse.warehouse_id }}" {% if warehouse.warehouse_id == filter_to_warehouse_id %}selected="selected"{% endif %}>{{ warehouse.name }}</option>
                {% endfor %}
              </select>
            </div>
            <div class="mb-3">
              <label for="input-status" class="form-label">{{ entry_status }}</label>
              <select name="filter_status" id="input-status" class="form-select">
                <option value="">{{ text_all_statuses }}</option>
                {% for key, value in transfer_statuses %}
                  <option value="{{ key }}" {% if key == filter_status %}selected="selected"{% endif %}>{{ value }}</option>
                {% endfor %}
              </select>
            </div>
            <div class="mb-3">
              <label for="input-date-start" class="form-label">{{ entry_date_start }}</label>
              <input type="date" name="filter_date_start" value="{{ filter_date_start }}" id="input-date-start" class="form-control"/>
            </div>
            <div class="mb-3">
              <label for="input-date-end" class="form-label">{{ entry_date_end }}</label>
              <input type="date" name="filter_date_end" value="{{ filter_date_end }}" id="input-date-end" class="form-control"/>
            </div>
            <div class="text-end">
              <button type="button" id="button-filter" class="btn btn-light"><i class="fas fa-filter"></i> {{ button_filter }}</button>
            </div>
          </div>
        </div>
      </div>
      
      <!-- قائمة التحويلات -->
      <div class="col-lg-9 col-md-12">
        <!-- الإجراءات المجمعة -->
        <div id="bulk-actions" class="card mb-3 d-none">
          <div class="card-header"><i class="fas fa-tasks"></i> {{ text_bulk_actions }}</div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-8">
                <select id="bulk-action-select" class="form-select">
                  <option value="">{{ text_select_action }}</option>
                  <option value="approve">{{ button_bulk_approve }}</option>
                  <option value="ship">{{ button_bulk_ship }}</option>
                  <option value="cancel">{{ button_bulk_cancel }}</option>
                  <option value="print">{{ button_bulk_print }}</option>
                </select>
              </div>
              <div class="col-md-4">
                <button type="button" id="button-bulk-execute" class="btn btn-primary w-100">{{ button_execute }}</button>
              </div>
            </div>
          </div>
        </div>
        
        <div class="card">
          <div class="card-header">
            <i class="fas fa-exchange-alt"></i> {{ text_list }}
            <div class="card-tools">
              <button type="button" class="btn btn-sm btn-outline-secondary" onclick="location.reload();">
                <i class="fas fa-sync"></i> {{ button_refresh }}
              </button>
            </div>
          </div>
          <div class="card-body">
            {% if transfers %}
            <div class="table-responsive">
              <table class="table table-bordered table-hover">
                <thead>
                  <tr>
                    <td class="text-center" width="1">
                      <input type="checkbox" onclick="$('input[name*=\'selected\']').prop('checked', this.checked);"/>
                    </td>
                    <td><a href="{{ sort_transfer_number }}" {% if sort == 'transfer_number' %}class="{% if order == 'ASC' %}asc{% else %}desc{% endif %}"{% endif %}>{{ column_transfer_number }}</a></td>
                    <td><a href="{{ sort_from_warehouse }}" {% if sort == 'from_warehouse' %}class="{% if order == 'ASC' %}asc{% else %}desc{% endif %}"{% endif %}>{{ column_from_warehouse }}</a></td>
                    <td><a href="{{ sort_to_warehouse }}" {% if sort == 'to_warehouse' %}class="{% if order == 'ASC' %}asc{% else %}desc{% endif %}"{% endif %}>{{ column_to_warehouse }}</a></td>
                    <td class="text-center">{{ column_items_count }}</td>
                    <td class="text-center">{{ column_total_quantity }}</td>
                    <td class="text-center">{{ column_status }}</td>
                    <td class="text-center"><a href="{{ sort_date_created }}" {% if sort == 'date_created' %}class="{% if order == 'ASC' %}asc{% else %}desc{% endif %}"{% endif %}>{{ column_date_created }}</a></td>
                    <td class="text-center">{{ column_action }}</td>
                  </tr>
                </thead>
                <tbody>
                  {% for transfer in transfers %}
                  <tr>
                    <td class="text-center">
                      <input type="checkbox" name="selected[]" value="{{ transfer.transfer_id }}"/>
                    </td>
                    <td>
                      <a href="{{ transfer.view }}" class="fw-bold">{{ transfer.transfer_number }}</a>
                      {% if transfer.priority == 'high' %}
                        <span class="badge bg-danger ms-1">{{ text_high_priority }}</span>
                      {% endif %}
                    </td>
                    <td>{{ transfer.from_warehouse_name }}</td>
                    <td>{{ transfer.to_warehouse_name }}</td>
                    <td class="text-center">
                      <span class="badge bg-info">{{ transfer.items_count }}</span>
                    </td>
                    <td class="text-center">{{ transfer.total_quantity }}</td>
                    <td class="text-center">
                      <span class="badge bg-{{ transfer.status_class }}">{{ transfer.status_text }}</span>
                    </td>
                    <td class="text-center">{{ transfer.date_created }}</td>
                    <td class="text-center">
                      <div class="btn-group btn-group-sm">
                        <button type="button" class="btn btn-info" onclick="viewTransfer({{ transfer.transfer_id }})" data-bs-toggle="tooltip" title="{{ button_view }}">
                          <i class="fas fa-eye"></i>
                        </button>
                        {% if transfer.can_edit %}
                        <a href="{{ transfer.edit }}" class="btn btn-primary" data-bs-toggle="tooltip" title="{{ button_edit }}">
                          <i class="fas fa-pencil-alt"></i>
                        </a>
                        {% endif %}
                        {% if transfer.can_approve %}
                        <button type="button" class="btn btn-success" onclick="approveTransfer({{ transfer.transfer_id }})" data-bs-toggle="tooltip" title="{{ button_approve }}">
                          <i class="fas fa-check"></i>
                        </button>
                        {% endif %}
                        {% if transfer.can_ship %}
                        <button type="button" class="btn btn-warning" onclick="shipTransfer({{ transfer.transfer_id }})" data-bs-toggle="tooltip" title="{{ button_ship }}">
                          <i class="fas fa-truck"></i>
                        </button>
                        {% endif %}
                        {% if transfer.can_receive %}
                        <button type="button" class="btn btn-info" onclick="receiveTransfer({{ transfer.transfer_id }})" data-bs-toggle="tooltip" title="{{ button_receive }}">
                          <i class="fas fa-inbox"></i>
                        </button>
                        {% endif %}
                        {% if transfer.can_delete %}
                        <button type="button" class="btn btn-danger" onclick="deleteTransfer({{ transfer.transfer_id }})" data-bs-toggle="tooltip" title="{{ button_delete }}">
                          <i class="fas fa-trash"></i>
                        </button>
                        {% endif %}
                      </div>
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
            <div class="row">
              <div class="col-sm-6 text-start">{{ pagination }}</div>
              <div class="col-sm-6 text-end">{{ results }}</div>
            </div>
            {% else %}
            <div class="text-center">
              <i class="fas fa-exchange-alt fa-3x text-muted mb-3"></i>
              <h4>{{ text_no_results }}</h4>
              <p class="text-muted">{{ text_no_transfers_message }}</p>
              <a href="{{ add }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> {{ button_add_transfer }}
              </a>
            </div>
            {% endif %}
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Modal لعرض تفاصيل التحويل -->
<div class="modal fade" id="modal-transfer-details" tabindex="-1">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">{{ text_transfer_details }}</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body" id="transfer-details-content">
        <!-- سيتم تحميل المحتوى عبر AJAX -->
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ button_close }}</button>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript">
// تطبيق الفلاتر
$('#button-filter').on('click', function() {
    var url = 'index.php?route=inventory/stock_transfer&user_token={{ user_token }}';
    
    var filter_transfer_number = $('input[name=\'filter_transfer_number\']').val();
    if (filter_transfer_number) {
        url += '&filter_transfer_number=' + encodeURIComponent(filter_transfer_number);
    }
    
    var filter_from_warehouse_id = $('select[name=\'filter_from_warehouse_id\']').val();
    if (filter_from_warehouse_id) {
        url += '&filter_from_warehouse_id=' + filter_from_warehouse_id;
    }
    
    var filter_to_warehouse_id = $('select[name=\'filter_to_warehouse_id\']').val();
    if (filter_to_warehouse_id) {
        url += '&filter_to_warehouse_id=' + filter_to_warehouse_id;
    }
    
    var filter_status = $('select[name=\'filter_status\']').val();
    if (filter_status) {
        url += '&filter_status=' + filter_status;
    }
    
    var filter_date_start = $('input[name=\'filter_date_start\']').val();
    if (filter_date_start) {
        url += '&filter_date_start=' + filter_date_start;
    }
    
    var filter_date_end = $('input[name=\'filter_date_end\']').val();
    if (filter_date_end) {
        url += '&filter_date_end=' + filter_date_end;
    }
    
    location = url;
});

// عرض تفاصيل التحويل
function viewTransfer(transferId) {
    $.ajax({
        url: 'index.php?route=inventory/stock_transfer/view&user_token={{ user_token }}&transfer_id=' + transferId,
        type: 'GET',
        success: function(data) {
            $('#transfer-details-content').html(data);
            $('#modal-transfer-details').modal('show');
        }
    });
}

// الموافقة على التحويل
function approveTransfer(transferId) {
    if (confirm('{{ text_confirm_approve }}')) {
        $.ajax({
            url: 'index.php?route=inventory/stock_transfer/approve&user_token={{ user_token }}',
            type: 'POST',
            data: {transfer_id: transferId},
            dataType: 'json',
            success: function(json) {
                if (json.success) {
                    location.reload();
                } else {
                    alert(json.error);
                }
            }
        });
    }
}

// شحن التحويل
function shipTransfer(transferId) {
    if (confirm('{{ text_confirm_ship }}')) {
        $.ajax({
            url: 'index.php?route=inventory/stock_transfer/ship&user_token={{ user_token }}',
            type: 'POST',
            data: {transfer_id: transferId},
            dataType: 'json',
            success: function(json) {
                if (json.success) {
                    location.reload();
                } else {
                    alert(json.error);
                }
            }
        });
    }
}

// استلام التحويل
function receiveTransfer(transferId) {
    window.open('index.php?route=inventory/stock_transfer/receive&user_token={{ user_token }}&transfer_id=' + transferId, '_blank');
}

// حذف التحويل
function deleteTransfer(transferId) {
    if (confirm('{{ text_confirm_delete }}')) {
        $.ajax({
            url: 'index.php?route=inventory/stock_transfer/delete&user_token={{ user_token }}',
            type: 'POST',
            data: {transfer_id: transferId},
            dataType: 'json',
            success: function(json) {
                if (json.success) {
                    location.reload();
                } else {
                    alert(json.error);
                }
            }
        });
    }
}

// تنفيذ الإجراءات المجمعة
$('#button-bulk-execute').on('click', function() {
    var action = $('#bulk-action-select').val();
    var selected = $('input[name*=\'selected\']:checked').map(function() {
        return this.value;
    }).get();
    
    if (!action) {
        alert('{{ error_no_action_selected }}');
        return;
    }
    
    if (selected.length === 0) {
        alert('{{ error_no_transfers_selected }}');
        return;
    }
    
    if (confirm('{{ text_confirm_bulk_action }}')) {
        $.ajax({
            url: 'index.php?route=inventory/stock_transfer/bulk' + action.charAt(0).toUpperCase() + action.slice(1) + '&user_token={{ user_token }}',
            type: 'POST',
            data: {selected: selected},
            dataType: 'json',
            success: function(json) {
                if (json.success) {
                    location.reload();
                } else {
                    alert(json.error);
                }
            }
        });
    }
});
</script>

{{ footer }}
