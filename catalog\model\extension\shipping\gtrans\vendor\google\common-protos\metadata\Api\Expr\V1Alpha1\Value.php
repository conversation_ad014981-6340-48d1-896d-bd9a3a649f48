<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/expr/v1alpha1/value.proto

namespace GPBMetadata\Google\Api\Expr\V1Alpha1;

class Value
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        \GPBMetadata\Google\Protobuf\Any::initOnce();
        \GPBMetadata\Google\Protobuf\Struct::initOnce();
        $pool->internalAddGeneratedFile(hex2bin(
            "0aaf070a24676f6f676c652f6170692f657870722f7631616c706861312f" .
            "76616c75652e70726f746f1218676f6f676c652e6170692e657870722e76" .
            "31616c706861311a1c676f6f676c652f70726f746f6275662f7374727563" .
            "742e70726f746f22c0030a0556616c756512300a0a6e756c6c5f76616c75" .
            "6518012001280e321a2e676f6f676c652e70726f746f6275662e4e756c6c" .
            "56616c7565480012140a0a626f6f6c5f76616c7565180220012808480012" .
            "150a0b696e7436345f76616c7565180320012803480012160a0c75696e74" .
            "36345f76616c7565180420012804480012160a0c646f75626c655f76616c" .
            "7565180520012801480012160a0c737472696e675f76616c756518062001" .
            "2809480012150a0b62797465735f76616c756518072001280c480012390a" .
            "0a656e756d5f76616c756518092001280b32232e676f6f676c652e617069" .
            "2e657870722e7631616c706861312e456e756d56616c75654800122c0a0c" .
            "6f626a6563745f76616c7565180a2001280b32142e676f6f676c652e7072" .
            "6f746f6275662e416e79480012370a096d61705f76616c7565180b200128" .
            "0b32222e676f6f676c652e6170692e657870722e7631616c706861312e4d" .
            "617056616c7565480012390a0a6c6973745f76616c7565180c2001280b32" .
            "232e676f6f676c652e6170692e657870722e7631616c706861312e4c6973" .
            "7456616c7565480012140a0a747970655f76616c7565180f200128094800" .
            "42060a046b696e6422280a09456e756d56616c7565120c0a047479706518" .
            "0120012809120d0a0576616c7565180220012805223c0a094c6973745661" .
            "6c7565122f0a0676616c75657318012003280b321f2e676f6f676c652e61" .
            "70692e657870722e7631616c706861312e56616c756522ac010a084d6170" .
            "56616c756512390a07656e747269657318012003280b32282e676f6f676c" .
            "652e6170692e657870722e7631616c706861312e4d617056616c75652e45" .
            "6e7472791a650a05456e747279122c0a036b657918012001280b321f2e67" .
            "6f6f676c652e6170692e657870722e7631616c706861312e56616c756512" .
            "2e0a0576616c756518022001280b321f2e676f6f676c652e6170692e6578" .
            "70722e7631616c706861312e56616c7565426d0a1c636f6d2e676f6f676c" .
            "652e6170692e657870722e7631616c70686131420a56616c756550726f74" .
            "6f50015a3c676f6f676c652e676f6c616e672e6f72672f67656e70726f74" .
            "6f2f676f6f676c65617069732f6170692f657870722f7631616c70686131" .
            "3b65787072f80101620670726f746f33"
        ), true);

        static::$is_initialized = true;
    }
}

