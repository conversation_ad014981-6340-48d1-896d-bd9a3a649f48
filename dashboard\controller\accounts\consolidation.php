<?php
/**
 * تحكم توحيد القوائم المالية المتقدم
 * يجمع بين القوة والبساطة - يتفوق على SAP وOracle في سهولة الاستخدام
 * مع الاحتفاظ بالقوة والمرونة المطلوبة للشركات الكبيرة
 * متوافق مع معايير المحاسبة المصرية والدولية IFRS
 */
class ControllerAccountsConsolidation extends Controller {
    private $error = array();
    private $central_service;

    public function __construct($registry) {
        parent::__construct($registry);

        // تحميل الخدمات المركزية
        $this->load->model('core/central_service_manager');
        $this->central_service = $this->model_core_central_service_manager;
    }

    public function index() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/consolidation') ||
            !$this->user->hasKey('accounting_consolidation_view')) {

            $this->central_service->logActivity('unauthorized_access', 'accounts',
                $this->language->get('log_unauthorized_access_consolidation'), [
                'user_id' => $this->user->getId(),
                'ip_address' => $this->request->server['REMOTE_ADDR']
            ]);

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/consolidation');
        $this->document->setTitle($this->language->get('heading_title'));
        $this->load->model('accounts/consolidation');

        // إضافة CSS و JavaScript المتقدم
        $this->document->addStyle('view/stylesheet/accounts/consolidation.css');
        $this->document->addScript('view/javascript/accounts/consolidation.js');
        $this->document->addScript('view/javascript/jquery/accounting.min.js');
        $this->document->addScript('view/javascript/jquery/select2.min.js');
        $this->document->addStyle('view/javascript/jquery/select2.min.css');
        $this->document->addScript('view/javascript/jquery/daterangepicker.min.js');
        $this->document->addStyle('view/javascript/jquery/daterangepicker.css');
        $this->document->addScript('view/javascript/jquery/chart.min.js');
        $this->document->addScript('view/javascript/jquery/datatables.min.js');
        $this->document->addStyle('view/javascript/jquery/datatables.min.css');

        // تسجيل الوصول للشاشة
        $this->central_service->logActivity('view', 'accounts',
            $this->language->get('log_view_consolidation_screen'), [
            'user_id' => $this->user->getId(),
            'screen' => 'consolidation'
        ]);

        // معالجة الطلبات
        if ($this->request->server['REQUEST_METHOD'] == 'POST') {
            if (isset($this->request->post['action'])) {
                switch ($this->request->post['action']) {
                    case 'generate_consolidation':
                        $this->generateConsolidation();
                        break;
                    case 'save_adjustments':
                        $this->saveAdjustments();
                        break;
                    case 'export_report':
                        $this->exportReport();
                        break;
                }
            }
        }

        $data = $this->getCommonData();

        // جلب بيانات الشركات التابعة
        $data['subsidiaries'] = $this->model_accounts_consolidation->getSubsidiaries();
        
        // جلب آخر عمليات التوحيد
        $data['recent_consolidations'] = $this->model_accounts_consolidation->getRecentConsolidations(10);

        // جلب إعدادات التوحيد
        $data['consolidation_settings'] = $this->model_accounts_consolidation->getConsolidationSettings();

        // روابط Ajax
        $data['ajax_generate_url'] = $this->url->link('accounts/consolidation/generate', 'user_token=' . $this->session->data['user_token'], true);
        $data['ajax_subsidiaries_url'] = $this->url->link('accounts/consolidation/getSubsidiaries', 'user_token=' . $this->session->data['user_token'], true);
        $data['ajax_adjustments_url'] = $this->url->link('accounts/consolidation/getAdjustments', 'user_token=' . $this->session->data['user_token'], true);
        $data['ajax_export_url'] = $this->url->link('accounts/consolidation/export', 'user_token=' . $this->session->data['user_token'], true);

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/consolidation', $data));
    }

    /**
     * توليد تقرير التوحيد
     */
    public function generate() {
        // فحص الصلاحيات المتقدمة
        if (!$this->user->hasKey('accounting_consolidation_generate')) {
            $json['error'] = $this->language->get('error_permission_generate');
            $this->response->addHeader('Content-Type: application/json');
            $this->response->setOutput(json_encode($json));
            return;
        }

        $this->load->model('accounts/consolidation');
        $json = array();

        try {
            $consolidation_data = array(
                'period_start' => $this->request->post['period_start'],
                'period_end' => $this->request->post['period_end'],
                'subsidiaries' => $this->request->post['subsidiaries'],
                'consolidation_method' => $this->request->post['consolidation_method'],
                'currency' => $this->request->post['currency'],
                'include_adjustments' => isset($this->request->post['include_adjustments'])
            );

            $consolidation_id = $this->model_accounts_consolidation->generateConsolidation($consolidation_data);

            if ($consolidation_id) {
                $json['success'] = $this->language->get('text_consolidation_generated');
                $json['consolidation_id'] = $consolidation_id;
                
                // تسجيل العملية
                $this->central_service->logActivity('generate', 'accounts',
                    $this->language->get('log_consolidation_generated'), [
                    'user_id' => $this->user->getId(),
                    'consolidation_id' => $consolidation_id,
                    'period' => $consolidation_data['period_start'] . ' - ' . $consolidation_data['period_end']
                ]);

                // إرسال إشعار
                $this->central_service->sendNotification([
                    'type' => 'consolidation_generated',
                    'title' => $this->language->get('notification_consolidation_title'),
                    'message' => sprintf($this->language->get('notification_consolidation_message'), $consolidation_id),
                    'user_id' => $this->user->getId(),
                    'url' => $this->url->link('accounts/consolidation/view', 'consolidation_id=' . $consolidation_id)
                ]);

            } else {
                $json['error'] = $this->language->get('error_consolidation_failed');
            }

        } catch (Exception $e) {
            $json['error'] = $e->getMessage();
            
            // تسجيل الخطأ
            $this->central_service->logActivity('error', 'accounts',
                'Consolidation generation failed: ' . $e->getMessage(), [
                'user_id' => $this->user->getId(),
                'error_details' => $e->getTraceAsString()
            ]);
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * عرض تفاصيل التوحيد
     */
    public function view() {
        if (!$this->user->hasPermission('access', 'accounts/consolidation') ||
            !$this->user->hasKey('accounting_consolidation_view')) {
            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $consolidation_id = isset($this->request->get['consolidation_id']) ? (int)$this->request->get['consolidation_id'] : 0;

        if (!$consolidation_id) {
            $this->response->redirect($this->url->link('accounts/consolidation'));
            return;
        }

        $this->load->language('accounts/consolidation');
        $this->load->model('accounts/consolidation');

        $data = $this->getCommonData();
        
        // جلب بيانات التوحيد
        $consolidation = $this->model_accounts_consolidation->getConsolidation($consolidation_id);
        
        if (!$consolidation) {
            $this->response->redirect($this->url->link('accounts/consolidation'));
            return;
        }

        $data['consolidation'] = $consolidation;
        $data['consolidation_details'] = $this->model_accounts_consolidation->getConsolidationDetails($consolidation_id);
        $data['adjustments'] = $this->model_accounts_consolidation->getConsolidationAdjustments($consolidation_id);

        $this->document->setTitle($this->language->get('heading_title_view') . ' - ' . $consolidation['reference']);

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/consolidation_view', $data));
    }

    /**
     * حفظ تعديلات التوحيد
     */
    public function saveAdjustments() {
        if (!$this->user->hasKey('accounting_consolidation_adjust')) {
            $json['error'] = $this->language->get('error_permission_adjust');
            $this->response->addHeader('Content-Type: application/json');
            $this->response->setOutput(json_encode($json));
            return;
        }

        $this->load->model('accounts/consolidation');
        $json = array();

        try {
            $adjustments_data = array(
                'consolidation_id' => $this->request->post['consolidation_id'],
                'adjustments' => $this->request->post['adjustments'],
                'notes' => $this->request->post['notes']
            );

            $result = $this->model_accounts_consolidation->saveAdjustments($adjustments_data);

            if ($result) {
                $json['success'] = $this->language->get('text_adjustments_saved');
                
                // تسجيل العملية
                $this->central_service->logActivity('adjust', 'accounts',
                    $this->language->get('log_consolidation_adjusted'), [
                    'user_id' => $this->user->getId(),
                    'consolidation_id' => $adjustments_data['consolidation_id']
                ]);

            } else {
                $json['error'] = $this->language->get('error_adjustments_failed');
            }

        } catch (Exception $e) {
            $json['error'] = $e->getMessage();
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * تصدير التقرير
     */
    public function export() {
        if (!$this->user->hasKey('accounting_consolidation_export')) {
            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $consolidation_id = isset($this->request->get['consolidation_id']) ? (int)$this->request->get['consolidation_id'] : 0;
        $format = isset($this->request->get['format']) ? $this->request->get['format'] : 'pdf';

        $this->load->model('accounts/consolidation');
        
        try {
            $export_data = $this->model_accounts_consolidation->exportConsolidation($consolidation_id, $format);
            
            if ($export_data) {
                // تسجيل العملية
                $this->central_service->logActivity('export', 'accounts',
                    $this->language->get('log_consolidation_exported'), [
                    'user_id' => $this->user->getId(),
                    'consolidation_id' => $consolidation_id,
                    'format' => $format
                ]);

                // إعداد headers للتحميل
                $this->response->addHeader('Content-Type: ' . $export_data['mime_type']);
                $this->response->addHeader('Content-Disposition: attachment; filename="' . $export_data['filename'] . '"');
                $this->response->setOutput($export_data['content']);
            }

        } catch (Exception $e) {
            $this->response->redirect($this->url->link('accounts/consolidation'));
        }
    }

    /**
     * جلب البيانات المشتركة
     */
    private function getCommonData() {
        $data = array();

        // النصوص
        $data['heading_title'] = $this->language->get('heading_title');
        $data['text_list'] = $this->language->get('text_list');
        $data['text_no_results'] = $this->language->get('text_no_results');
        $data['text_confirm'] = $this->language->get('text_confirm');

        // الأزرار
        $data['button_generate'] = $this->language->get('button_generate');
        $data['button_view'] = $this->language->get('button_view');
        $data['button_export'] = $this->language->get('button_export');
        $data['button_cancel'] = $this->language->get('button_cancel');

        // الروابط
        $data['cancel'] = $this->url->link('accounts/consolidation', 'user_token=' . $this->session->data['user_token'], true);
        $data['user_token'] = $this->session->data['user_token'];

        return $data;
    }
}
