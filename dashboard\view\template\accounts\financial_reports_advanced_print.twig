<!DOCTYPE html>
<html dir="{{ direction }}" lang="{{ lang }}" class="no-js">
<head>
<meta charset="UTF-8" />
<title>{{ heading_title }}</title>
<base href="{{ base }}" />
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
<link href="view/stylesheet/bootstrap.css" rel="stylesheet" media="screen" />
<style>
@media print {
  body { margin: 0; }
  .no-print { display: none !important; }
  .page-break { page-break-before: always; }
}

body {
  font-family: Arial, sans-serif;
  font-size: 11px;
  line-height: 1.3;
}

.header {
  text-align: center;
  margin-bottom: 30px;
  border-bottom: 2px solid #333;
  padding-bottom: 20px;
}

.company-logo {
  max-height: 80px;
  margin-bottom: 10px;
}

.company-name {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}

.report-title {
  font-size: 18px;
  font-weight: bold;
  color: #666;
  margin-bottom: 10px;
}

.report-info {
  font-size: 10px;
  color: #888;
}

.summary-box {
  background-color: #f8f9fa;
  padding: 15px;
  margin-bottom: 20px;
  border: 1px solid #dee2e6;
}

.financial-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 20px;
  font-size: 10px;
}

.financial-table th,
.financial-table td {
  border: 1px solid #dee2e6;
  padding: 6px;
  text-align: left;
}

.financial-table th {
  background-color: #f8f9fa;
  font-weight: bold;
}

.text-right {
  text-align: right;
}

.text-center {
  text-align: center;
}

.text-success {
  color: #28a745;
}

.text-warning {
  color: #ffc107;
}

.text-danger {
  color: #dc3545;
}

.text-info {
  color: #17a2b8;
}

.footer {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #dee2e6;
  font-size: 9px;
  color: #888;
}

.signature-section {
  margin-top: 40px;
  display: flex;
  justify-content: space-between;
}

.signature-box {
  width: 200px;
  text-align: center;
}

.signature-line {
  border-top: 1px solid #333;
  margin-top: 50px;
  padding-top: 5px;
}

.watermark {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(-45deg);
  font-size: 72px;
  color: rgba(0,0,0,0.05);
  z-index: -1;
  pointer-events: none;
}

.ratio-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 15px;
  font-size: 9px;
}

.ratio-table th,
.ratio-table td {
  border: 1px solid #dee2e6;
  padding: 4px;
  text-align: left;
}

.ratio-table th {
  background-color: #f8f9fa;
  font-weight: bold;
}

.chart-placeholder {
  width: 100%;
  height: 200px;
  border: 1px solid #dee2e6;
  background-color: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
}
</style>
</head>
<body>

<!-- Watermark -->
{% if include_watermark %}
<div class="watermark">{{ text_confidential }}</div>
{% endif %}

<!-- Header -->
<div class="header">
  {% if company_logo %}
  <img src="{{ company_logo }}" alt="{{ company_name }}" class="company-logo">
  {% endif %}
  <div class="company-name">{{ company_name }}</div>
  <div class="report-title">{{ report_title }}</div>
  <div class="report-info">
    {{ text_period }}: {{ date_start }} {{ text_to }} {{ date_end }} | {{ text_generated_on }}: {{ generated_date }}
  </div>
</div>

<!-- Executive Summary -->
<div class="summary-box">
  <h3 style="margin-top: 0;">{{ text_executive_summary }}</h3>
  <div style="display: flex; justify-content: space-between;">
    <div>
      <strong>{{ text_report_type }}:</strong> {{ report_type_name }}<br>
      <strong>{{ text_period }}:</strong> {{ date_start }} {{ text_to }} {{ date_end }}<br>
      <strong>{{ text_currency }}:</strong> {{ currency }}
    </div>
    <div>
      <strong>{{ text_total_assets }}:</strong> {{ summary.total_assets }}<br>
      <strong>{{ text_total_liabilities }}:</strong> {{ summary.total_liabilities }}<br>
      <strong>{{ text_total_equity }}:</strong> {{ summary.total_equity }}
    </div>
    <div>
      <strong>{{ text_total_revenues }}:</strong> {{ summary.total_revenues }}<br>
      <strong>{{ text_total_expenses }}:</strong> {{ summary.total_expenses }}<br>
      <strong>{{ text_net_income }}:</strong> {{ summary.net_income }}
    </div>
  </div>
</div>

<!-- Income Statement -->
{% if report_type == 'comprehensive' or report_type == 'income_statement' %}
<h3>{{ text_income_statement }}</h3>
<table class="financial-table">
  <thead>
    <tr>
      <th style="width: 60%;">{{ column_account }}</th>
      <th style="width: 20%;" class="text-right">{{ column_current_period }}</th>
      {% if comparison %}
      <th style="width: 20%;" class="text-right">{{ column_comparison_period }}</th>
      {% endif %}
    </tr>
  </thead>
  <tbody>
    <!-- Revenues -->
    <tr style="background-color: #e2e3e5; font-weight: bold;">
      <td>{{ text_revenues }}</td>
      <td class="text-right">{{ income_statement.total_revenues }}</td>
      {% if comparison %}
      <td class="text-right">{{ comparison.income_statement.total_revenues }}</td>
      {% endif %}
    </tr>
    {% for revenue in income_statement.revenues %}
    <tr>
      <td style="padding-left: 20px;">{{ revenue.account_name }}</td>
      <td class="text-right">{{ revenue.amount }}</td>
      {% if comparison %}
      <td class="text-right">{{ revenue.comparison_amount }}</td>
      {% endif %}
    </tr>
    {% endfor %}
    
    <!-- Expenses -->
    <tr style="background-color: #e2e3e5; font-weight: bold;">
      <td>{{ text_expenses }}</td>
      <td class="text-right">{{ income_statement.total_expenses }}</td>
      {% if comparison %}
      <td class="text-right">{{ comparison.income_statement.total_expenses }}</td>
      {% endif %}
    </tr>
    {% for expense in income_statement.expenses %}
    <tr>
      <td style="padding-left: 20px;">{{ expense.account_name }}</td>
      <td class="text-right">{{ expense.amount }}</td>
      {% if comparison %}
      <td class="text-right">{{ expense.comparison_amount }}</td>
      {% endif %}
    </tr>
    {% endfor %}
  </tbody>
  <tfoot>
    <tr style="background-color: #d1ecf1; font-weight: bold;">
      <td>{{ text_net_income }}</td>
      <td class="text-right {% if income_statement.net_income >= 0 %}text-success{% else %}text-danger{% endif %}">
        {{ income_statement.net_income }}
      </td>
      {% if comparison %}
      <td class="text-right {% if comparison.income_statement.net_income >= 0 %}text-success{% else %}text-danger{% endif %}">
        {{ comparison.income_statement.net_income }}
      </td>
      {% endif %}
    </tr>
  </tfoot>
</table>
{% endif %}

<!-- Balance Sheet -->
{% if report_type == 'comprehensive' or report_type == 'balance_sheet' %}
<div class="page-break"></div>
<h3>{{ text_balance_sheet }}</h3>
<table class="financial-table">
  <thead>
    <tr>
      <th style="width: 60%;">{{ column_account }}</th>
      <th style="width: 20%;" class="text-right">{{ column_current_period }}</th>
      {% if comparison %}
      <th style="width: 20%;" class="text-right">{{ column_comparison_period }}</th>
      {% endif %}
    </tr>
  </thead>
  <tbody>
    <!-- Assets -->
    <tr style="background-color: #e2e3e5; font-weight: bold;">
      <td>{{ text_assets }}</td>
      <td class="text-right">{{ balance_sheet.total_assets }}</td>
      {% if comparison %}
      <td class="text-right">{{ comparison.balance_sheet.total_assets }}</td>
      {% endif %}
    </tr>
    {% for asset in balance_sheet.assets %}
    <tr>
      <td style="padding-left: 20px;">{{ asset.account_name }}</td>
      <td class="text-right">{{ asset.amount }}</td>
      {% if comparison %}
      <td class="text-right">{{ asset.comparison_amount }}</td>
      {% endif %}
    </tr>
    {% endfor %}
    
    <!-- Liabilities -->
    <tr style="background-color: #e2e3e5; font-weight: bold;">
      <td>{{ text_liabilities }}</td>
      <td class="text-right">{{ balance_sheet.total_liabilities }}</td>
      {% if comparison %}
      <td class="text-right">{{ comparison.balance_sheet.total_liabilities }}</td>
      {% endif %}
    </tr>
    {% for liability in balance_sheet.liabilities %}
    <tr>
      <td style="padding-left: 20px;">{{ liability.account_name }}</td>
      <td class="text-right">{{ liability.amount }}</td>
      {% if comparison %}
      <td class="text-right">{{ liability.comparison_amount }}</td>
      {% endif %}
    </tr>
    {% endfor %}
    
    <!-- Equity -->
    <tr style="background-color: #e2e3e5; font-weight: bold;">
      <td>{{ text_equity }}</td>
      <td class="text-right">{{ balance_sheet.total_equity }}</td>
      {% if comparison %}
      <td class="text-right">{{ comparison.balance_sheet.total_equity }}</td>
      {% endif %}
    </tr>
    {% for equity in balance_sheet.equity %}
    <tr>
      <td style="padding-left: 20px;">{{ equity.account_name }}</td>
      <td class="text-right">{{ equity.amount }}</td>
      {% if comparison %}
      <td class="text-right">{{ equity.comparison_amount }}</td>
      {% endif %}
    </tr>
    {% endfor %}
  </tbody>
  <tfoot>
    <tr style="background-color: #d1ecf1; font-weight: bold;">
      <td>{{ text_total_liabilities_equity }}</td>
      <td class="text-right">{{ balance_sheet.total_liabilities_equity }}</td>
      {% if comparison %}
      <td class="text-right">{{ comparison.balance_sheet.total_liabilities_equity }}</td>
      {% endif %}
    </tr>
  </tfoot>
</table>
{% endif %}

<!-- Financial Ratios -->
{% if include_ratios and financial_ratios %}
<div class="page-break"></div>
<h3>{{ text_financial_ratios }}</h3>

<!-- Liquidity Ratios -->
<h4>{{ text_liquidity_ratios }}</h4>
<table class="ratio-table">
  <thead>
    <tr>
      <th>{{ column_ratio_name }}</th>
      <th class="text-right">{{ column_value }}</th>
      <th class="text-center">{{ column_rating }}</th>
      <th>{{ column_interpretation }}</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>{{ text_current_ratio }}</td>
      <td class="text-right">{{ financial_ratios.liquidity.current_ratio }}</td>
      <td class="text-center">
        {% if financial_ratios.liquidity.current_ratio >= 2 %}{{ text_excellent }}
        {% elseif financial_ratios.liquidity.current_ratio >= 1 %}{{ text_good }}
        {% else %}{{ text_poor }}{% endif %}
      </td>
      <td>{{ text_current_ratio_interpretation }}</td>
    </tr>
    <tr>
      <td>{{ text_quick_ratio }}</td>
      <td class="text-right">{{ financial_ratios.liquidity.quick_ratio }}</td>
      <td class="text-center">
        {% if financial_ratios.liquidity.quick_ratio >= 1 %}{{ text_excellent }}
        {% elseif financial_ratios.liquidity.quick_ratio >= 0.5 %}{{ text_good }}
        {% else %}{{ text_poor }}{% endif %}
      </td>
      <td>{{ text_quick_ratio_interpretation }}</td>
    </tr>
    <tr>
      <td>{{ text_cash_ratio }}</td>
      <td class="text-right">{{ financial_ratios.liquidity.cash_ratio }}</td>
      <td class="text-center">
        {% if financial_ratios.liquidity.cash_ratio >= 0.2 %}{{ text_excellent }}
        {% elseif financial_ratios.liquidity.cash_ratio >= 0.1 %}{{ text_good }}
        {% else %}{{ text_poor }}{% endif %}
      </td>
      <td>{{ text_cash_ratio_interpretation }}</td>
    </tr>
  </tbody>
</table>

<!-- Profitability Ratios -->
<h4>{{ text_profitability_ratios }}</h4>
<table class="ratio-table">
  <thead>
    <tr>
      <th>{{ column_ratio_name }}</th>
      <th class="text-right">{{ column_value }}</th>
      <th class="text-center">{{ column_rating }}</th>
      <th>{{ column_interpretation }}</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>{{ text_gross_profit_margin }}</td>
      <td class="text-right">{{ financial_ratios.profitability.gross_profit_margin }}%</td>
      <td class="text-center">
        {% if financial_ratios.profitability.gross_profit_margin >= 30 %}{{ text_excellent }}
        {% elseif financial_ratios.profitability.gross_profit_margin >= 20 %}{{ text_good }}
        {% else %}{{ text_poor }}{% endif %}
      </td>
      <td>{{ text_gross_profit_margin_interpretation }}</td>
    </tr>
    <tr>
      <td>{{ text_net_profit_margin }}</td>
      <td class="text-right">{{ financial_ratios.profitability.net_profit_margin }}%</td>
      <td class="text-center">
        {% if financial_ratios.profitability.net_profit_margin >= 15 %}{{ text_excellent }}
        {% elseif financial_ratios.profitability.net_profit_margin >= 5 %}{{ text_good }}
        {% else %}{{ text_poor }}{% endif %}
      </td>
      <td>{{ text_net_profit_margin_interpretation }}</td>
    </tr>
    <tr>
      <td>{{ text_return_on_assets }}</td>
      <td class="text-right">{{ financial_ratios.profitability.return_on_assets }}%</td>
      <td class="text-center">
        {% if financial_ratios.profitability.return_on_assets >= 10 %}{{ text_excellent }}
        {% elseif financial_ratios.profitability.return_on_assets >= 5 %}{{ text_good }}
        {% else %}{{ text_poor }}{% endif %}
      </td>
      <td>{{ text_return_on_assets_interpretation }}</td>
    </tr>
    <tr>
      <td>{{ text_return_on_equity }}</td>
      <td class="text-right">{{ financial_ratios.profitability.return_on_equity }}%</td>
      <td class="text-center">
        {% if financial_ratios.profitability.return_on_equity >= 15 %}{{ text_excellent }}
        {% elseif financial_ratios.profitability.return_on_equity >= 10 %}{{ text_good }}
        {% else %}{{ text_poor }}{% endif %}
      </td>
      <td>{{ text_return_on_equity_interpretation }}</td>
    </tr>
  </tbody>
</table>
{% endif %}

<!-- Performance Indicators -->
{% if performance_indicators %}
<h3>{{ text_key_performance_indicators }}</h3>
<table class="ratio-table">
  <thead>
    <tr>
      <th>{{ column_kpi_name }}</th>
      <th class="text-right">{{ column_value }}</th>
      <th class="text-right">{{ column_target }}</th>
      <th class="text-center">{{ column_achievement }}</th>
    </tr>
  </thead>
  <tbody>
    {% for kpi in performance_indicators %}
    <tr>
      <td>{{ kpi.name }}</td>
      <td class="text-right">{{ kpi.value }}</td>
      <td class="text-right">{{ kpi.target }}</td>
      <td class="text-center">
        {% if kpi.achievement >= 100 %}{{ text_achieved }}
        {% elseif kpi.achievement >= 80 %}{{ text_near_target }}
        {% else %}{{ text_below_target }}{% endif %}
      </td>
    </tr>
    {% endfor %}
  </tbody>
</table>
{% endif %}

<!-- Charts Placeholder -->
{% if include_charts %}
<div class="page-break"></div>
<h3>{{ text_financial_charts }}</h3>
<div class="chart-placeholder">
  {{ text_chart_placeholder }}
</div>
{% endif %}

<!-- Notes -->
{% if include_notes and notes %}
<h3>{{ text_notes }}</h3>
<div style="background-color: #f8f9fa; padding: 15px; border: 1px solid #dee2e6;">
  {% for note in notes %}
  <p><strong>{{ note.title }}:</strong> {{ note.content }}</p>
  {% endfor %}
</div>
{% endif %}

<!-- Signatures -->
<div class="signature-section">
  <div class="signature-box">
    <div class="signature-line">{{ text_prepared_by }}</div>
  </div>
  <div class="signature-box">
    <div class="signature-line">{{ text_reviewed_by }}</div>
  </div>
  <div class="signature-box">
    <div class="signature-line">{{ text_approved_by }}</div>
  </div>
</div>

<!-- Footer -->
<div class="footer">
  <div style="display: flex; justify-content: space-between;">
    <div>
      {{ text_confidential }} - {{ text_internal_use }}
    </div>
    <div>
      {{ text_eas_compliant }} | {{ text_eta_ready }} | {{ text_financial_control }}
    </div>
    <div>
      {{ text_page }} 1 {{ text_of }} 1
    </div>
  </div>
</div>

<script>
// Auto print when page loads
window.onload = function() {
  window.print();
};
</script>

</body>
</html>
