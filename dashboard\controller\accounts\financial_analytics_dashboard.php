<?php
/**
 * تحكم لوحة التحليلات المالية المتقدمة
 * لوحة معلومات تفاعلية متقدمة مع مؤشرات الأداء الرئيسية والتحليلات المالية
 * تدعم التخصيص الكامل والتحديث في الوقت الفعلي
 */
class ControllerAccountsFinancialAnalyticsDashboard extends Controller {
    private $error = array();
    private $central_service;

    public function __construct($registry) {
        parent::__construct($registry);

        // تحميل الخدمات المركزية
        $this->load->model('core/central_service_manager');
        $this->central_service = $this->model_core_central_service_manager;
    }

    public function index() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/financial_analytics_dashboard') ||
            !$this->user->hasKey('accounting_financial_analytics_view')) {

            $this->central_service->logActivity('unauthorized_access', 'accounts',
                $this->language->get('log_unauthorized_access_analytics'), [
                'user_id' => $this->user->getId(),
                'ip_address' => $this->request->server['REMOTE_ADDR']
            ]);

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/financial_analytics_dashboard');
        $this->document->setTitle($this->language->get('heading_title'));
        $this->load->model('accounts/financial_analytics_dashboard');

        // إضافة CSS و JavaScript المتقدم
        $this->document->addStyle('view/stylesheet/accounts/financial_analytics_dashboard.css');
        $this->document->addScript('view/javascript/accounts/financial_analytics_dashboard.js');
        $this->document->addScript('view/javascript/jquery/chart.min.js');
        $this->document->addScript('view/javascript/jquery/chartjs-adapter-date-fns.min.js');
        $this->document->addScript('view/javascript/jquery/accounting.min.js');
        $this->document->addScript('view/javascript/jquery/select2.min.js');
        $this->document->addStyle('view/javascript/jquery/select2.min.css');
        $this->document->addScript('view/javascript/jquery/daterangepicker.min.js');
        $this->document->addStyle('view/javascript/jquery/daterangepicker.css');
        $this->document->addScript('view/javascript/jquery/gridstack.min.js');
        $this->document->addStyle('view/javascript/jquery/gridstack.min.css');

        // تسجيل الوصول للشاشة
        $this->central_service->logActivity('view', 'accounts',
            $this->language->get('log_view_analytics_screen'), [
            'user_id' => $this->user->getId(),
            'screen' => 'financial_analytics_dashboard'
        ]);

        // معالجة الطلبات
        if ($this->request->server['REQUEST_METHOD'] == 'POST') {
            if (isset($this->request->post['action'])) {
                switch ($this->request->post['action']) {
                    case 'save_dashboard':
                        $this->saveDashboard();
                        break;
                    case 'add_widget':
                        $this->addWidget();
                        break;
                    case 'update_widget':
                        $this->updateWidget();
                        break;
                    case 'delete_widget':
                        $this->deleteWidget();
                        break;
                }
            }
        }

        $data = $this->getCommonData();

        // جلب لوحة المعلومات الافتراضية للمستخدم
        $dashboard = $this->model_accounts_financial_analytics_dashboard->getUserDefaultDashboard($this->user->getId());
        
        if (!$dashboard) {
            // إنشاء لوحة معلومات افتراضية
            $dashboard = $this->model_accounts_financial_analytics_dashboard->createDefaultDashboard($this->user->getId());
        }

        $data['dashboard'] = $dashboard;
        $data['widgets'] = $this->model_accounts_financial_analytics_dashboard->getDashboardWidgets($dashboard['dashboard_id']);

        // جلب البيانات لكل ويدجت
        foreach ($data['widgets'] as &$widget) {
            $widget['data'] = $this->getWidgetData($widget);
        }

        // جلب مؤشرات الأداء الرئيسية
        $data['kpis'] = $this->model_accounts_financial_analytics_dashboard->getKPIs();

        // جلب التحليلات السريعة
        $data['quick_insights'] = $this->model_accounts_financial_analytics_dashboard->getQuickInsights();

        // جلب التنبيهات المالية
        $data['financial_alerts'] = $this->model_accounts_financial_analytics_dashboard->getFinancialAlerts();

        // روابط Ajax
        $data['ajax_save_url'] = $this->url->link('accounts/financial_analytics_dashboard/save', 'user_token=' . $this->session->data['user_token'], true);
        $data['ajax_widget_url'] = $this->url->link('accounts/financial_analytics_dashboard/widget', 'user_token=' . $this->session->data['user_token'], true);
        $data['ajax_data_url'] = $this->url->link('accounts/financial_analytics_dashboard/data', 'user_token=' . $this->session->data['user_token'], true);
        $data['ajax_export_url'] = $this->url->link('accounts/financial_analytics_dashboard/export', 'user_token=' . $this->session->data['user_token'], true);

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/financial_analytics_dashboard', $data));
    }

    /**
     * حفظ لوحة المعلومات
     */
    public function save() {
        // فحص الصلاحيات المتقدمة
        if (!$this->user->hasKey('accounting_financial_analytics_create')) {
            $json['error'] = $this->language->get('error_permission_save');
            $this->response->addHeader('Content-Type: application/json');
            $this->response->setOutput(json_encode($json));
            return;
        }

        $this->load->model('accounts/financial_analytics_dashboard');
        $json = array();

        try {
            $dashboard_data = array(
                'dashboard_id' => $this->request->post['dashboard_id'],
                'dashboard_name' => $this->request->post['dashboard_name'],
                'layout_config' => $this->request->post['layout_config'],
                'refresh_interval' => $this->request->post['refresh_interval'],
                'is_default' => isset($this->request->post['is_default'])
            );

            $result = $this->model_accounts_financial_analytics_dashboard->saveDashboard($dashboard_data);

            if ($result) {
                $json['success'] = $this->language->get('text_dashboard_saved');
                
                // تسجيل العملية
                $this->central_service->logActivity('save', 'accounts',
                    $this->language->get('log_dashboard_saved'), [
                    'user_id' => $this->user->getId(),
                    'dashboard_id' => $dashboard_data['dashboard_id']
                ]);

            } else {
                $json['error'] = $this->language->get('error_save_failed');
            }

        } catch (Exception $e) {
            $json['error'] = $e->getMessage();
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * إدارة الويدجت
     */
    public function widget() {
        if (!$this->user->hasKey('accounting_financial_analytics_create')) {
            $json['error'] = $this->language->get('error_permission_widget');
            $this->response->addHeader('Content-Type: application/json');
            $this->response->setOutput(json_encode($json));
            return;
        }

        $this->load->model('accounts/financial_analytics_dashboard');
        $json = array();

        try {
            $action = $this->request->post['widget_action'];
            
            switch ($action) {
                case 'add':
                    $widget_data = array(
                        'dashboard_id' => $this->request->post['dashboard_id'],
                        'widget_type' => $this->request->post['widget_type'],
                        'widget_title' => $this->request->post['widget_title'],
                        'data_source' => $this->request->post['data_source'],
                        'chart_type' => $this->request->post['chart_type'],
                        'position_x' => $this->request->post['position_x'],
                        'position_y' => $this->request->post['position_y'],
                        'width' => $this->request->post['width'],
                        'height' => $this->request->post['height'],
                        'config' => $this->request->post['config'],
                        'filters' => $this->request->post['filters']
                    );

                    $widget_id = $this->model_accounts_financial_analytics_dashboard->addWidget($widget_data);
                    
                    if ($widget_id) {
                        $json['success'] = $this->language->get('text_widget_added');
                        $json['widget_id'] = $widget_id;
                    } else {
                        $json['error'] = $this->language->get('error_widget_add_failed');
                    }
                    break;

                case 'update':
                    $widget_data = array(
                        'widget_id' => $this->request->post['widget_id'],
                        'widget_title' => $this->request->post['widget_title'],
                        'position_x' => $this->request->post['position_x'],
                        'position_y' => $this->request->post['position_y'],
                        'width' => $this->request->post['width'],
                        'height' => $this->request->post['height'],
                        'config' => $this->request->post['config'],
                        'filters' => $this->request->post['filters']
                    );

                    $result = $this->model_accounts_financial_analytics_dashboard->updateWidget($widget_data);
                    
                    if ($result) {
                        $json['success'] = $this->language->get('text_widget_updated');
                    } else {
                        $json['error'] = $this->language->get('error_widget_update_failed');
                    }
                    break;

                case 'delete':
                    $widget_id = $this->request->post['widget_id'];
                    $result = $this->model_accounts_financial_analytics_dashboard->deleteWidget($widget_id);
                    
                    if ($result) {
                        $json['success'] = $this->language->get('text_widget_deleted');
                    } else {
                        $json['error'] = $this->language->get('error_widget_delete_failed');
                    }
                    break;

                default:
                    $json['error'] = $this->language->get('error_invalid_action');
                    break;
            }

        } catch (Exception $e) {
            $json['error'] = $e->getMessage();
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * جلب بيانات الويدجت
     */
    public function data() {
        $this->load->model('accounts/financial_analytics_dashboard');
        $json = array();

        try {
            $widget_id = $this->request->get['widget_id'];
            $widget = $this->model_accounts_financial_analytics_dashboard->getWidget($widget_id);
            
            if ($widget) {
                $widget_data = $this->getWidgetData($widget);
                $json['success'] = true;
                $json['data'] = $widget_data;
            } else {
                $json['error'] = $this->language->get('error_widget_not_found');
            }

        } catch (Exception $e) {
            $json['error'] = $e->getMessage();
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * تصدير لوحة المعلومات
     */
    public function export() {
        if (!$this->user->hasKey('accounting_financial_analytics_view')) {
            $json['error'] = $this->language->get('error_permission_export');
            $this->response->addHeader('Content-Type: application/json');
            $this->response->setOutput(json_encode($json));
            return;
        }

        $this->load->model('accounts/financial_analytics_dashboard');
        $json = array();

        try {
            $dashboard_id = $this->request->post['dashboard_id'];
            $export_format = $this->request->post['export_format']; // pdf, excel, image

            $export_result = $this->model_accounts_financial_analytics_dashboard->exportDashboard($dashboard_id, $export_format);

            if ($export_result) {
                $json['success'] = $this->language->get('text_dashboard_exported');
                $json['download_url'] = $export_result['download_url'];
                
                // تسجيل العملية
                $this->central_service->logActivity('export', 'accounts',
                    $this->language->get('log_dashboard_exported'), [
                    'user_id' => $this->user->getId(),
                    'dashboard_id' => $dashboard_id,
                    'format' => $export_format
                ]);

            } else {
                $json['error'] = $this->language->get('error_export_failed');
            }

        } catch (Exception $e) {
            $json['error'] = $e->getMessage();
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * جلب بيانات الويدجت حسب النوع
     */
    private function getWidgetData($widget) {
        $this->load->model('accounts/financial_analytics_dashboard');
        
        switch ($widget['data_source']) {
            case 'revenue_analysis':
                return $this->model_accounts_financial_analytics_dashboard->getRevenueAnalysis($widget);
            
            case 'expense_analysis':
                return $this->model_accounts_financial_analytics_dashboard->getExpenseAnalysis($widget);
            
            case 'profit_loss':
                return $this->model_accounts_financial_analytics_dashboard->getProfitLossData($widget);
            
            case 'cash_flow':
                return $this->model_accounts_financial_analytics_dashboard->getCashFlowData($widget);
            
            case 'balance_sheet':
                return $this->model_accounts_financial_analytics_dashboard->getBalanceSheetData($widget);
            
            case 'kpi_metrics':
                return $this->model_accounts_financial_analytics_dashboard->getKPIMetrics($widget);
            
            case 'budget_variance':
                return $this->model_accounts_financial_analytics_dashboard->getBudgetVariance($widget);
            
            case 'accounts_receivable':
                return $this->model_accounts_financial_analytics_dashboard->getAccountsReceivable($widget);
            
            case 'accounts_payable':
                return $this->model_accounts_financial_analytics_dashboard->getAccountsPayable($widget);
            
            case 'inventory_analysis':
                return $this->model_accounts_financial_analytics_dashboard->getInventoryAnalysis($widget);
            
            default:
                return array();
        }
    }

    /**
     * جلب البيانات المشتركة
     */
    private function getCommonData() {
        $data = array();

        // النصوص
        $data['heading_title'] = $this->language->get('heading_title');
        $data['text_dashboard'] = $this->language->get('text_dashboard');
        $data['text_widgets'] = $this->language->get('text_widgets');
        $data['text_no_data'] = $this->language->get('text_no_data');

        // الأزرار
        $data['button_save'] = $this->language->get('button_save');
        $data['button_add_widget'] = $this->language->get('button_add_widget');
        $data['button_edit'] = $this->language->get('button_edit');
        $data['button_delete'] = $this->language->get('button_delete');
        $data['button_export'] = $this->language->get('button_export');
        $data['button_refresh'] = $this->language->get('button_refresh');

        // الروابط
        $data['user_token'] = $this->session->data['user_token'];

        return $data;
    }
}
