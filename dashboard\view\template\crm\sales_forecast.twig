{{ header }}{{ column_left }}

{# توقعات المبيعات - Sales Forecast #}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="button" data-toggle="tooltip" title="{{ button_filter }}" onclick="$('#filter-forecast').toggleClass('hidden');" class="btn btn-default visible-xs">
          <i class="fa fa-filter"></i>
        </button>
        <a href="{{ add }}" data-toggle="tooltip" title="{{ button_add }}" class="btn btn-primary">
          <i class="fa fa-plus"></i>
        </a>
        <button type="button" data-toggle="tooltip" title="{{ button_compare }}" onclick="$('#modal-compare').modal('show');" class="btn btn-info">
          <i class="fa fa-line-chart"></i>
        </button>
        <button type="button" data-toggle="tooltip" title="{{ button_export }}" onclick="exportForecasts();" class="btn btn-success">
          <i class="fa fa-download"></i>
        </button>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
          <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>

  <div class="container-fluid">
    {# إحصائيات سريعة #}
    <div class="row mb-4">
      <div class="col-lg-3 col-md-6">
        <div class="card bg-primary text-white">
          <div class="card-body">
            <div class="d-flex justify-content-between">
              <div>
                <h4 class="mb-0">{{ statistics.total_forecasts }}</h4>
                <p class="mb-0">{{ text_total_forecasts }}</p>
              </div>
              <div class="align-self-center">
                <i class="fas fa-chart-line fa-2x"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-md-6">
        <div class="card bg-success text-white">
          <div class="card-body">
            <div class="d-flex justify-content-between">
              <div>
                <h4 class="mb-0">{{ statistics.avg_accuracy }}%</h4>
                <p class="mb-0">{{ text_avg_accuracy }}</p>
              </div>
              <div class="align-self-center">
                <i class="fas fa-bullseye fa-2x"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-md-6">
        <div class="card bg-warning text-white">
          <div class="card-body">
            <div class="d-flex justify-content-between">
              <div>
                <h4 class="mb-0">{{ statistics.best_method }}</h4>
                <p class="mb-0">{{ text_best_method }}</p>
              </div>
              <div class="align-self-center">
                <i class="fas fa-trophy fa-2x"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-md-6">
        <div class="card bg-info text-white">
          <div class="card-body">
            <div class="d-flex justify-content-between">
              <div>
                <h4 class="mb-0">{{ statistics.active_forecasts }}</h4>
                <p class="mb-0">{{ text_active_forecasts }}</p>
              </div>
              <div class="align-self-center">
                <i class="fas fa-clock fa-2x"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    {# الرسوم البيانية المتقدمة #}
    <div class="row mb-4">
      <div class="col-lg-8">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">
              <i class="fas fa-chart-area"></i>
              {{ text_forecast_vs_actual }}
            </h3>
            <div class="card-tools">
              <select id="chart-period" class="form-select form-select-sm" onchange="updateForecastChart();">
                <option value="7">{{ text_last_7_days }}</option>
                <option value="30" selected>{{ text_last_30_days }}</option>
                <option value="90">{{ text_last_90_days }}</option>
                <option value="365">{{ text_last_year }}</option>
              </select>
            </div>
          </div>
          <div class="card-body">
            <canvas id="forecastVsActualChart" height="400"></canvas>
          </div>
        </div>
      </div>
      <div class="col-lg-4">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">
              <i class="fas fa-chart-pie"></i>
              {{ text_method_accuracy }}
            </h3>
          </div>
          <div class="card-body">
            <canvas id="methodAccuracyChart" height="400"></canvas>
          </div>
        </div>
      </div>
    </div>

    {# اتجاه الدقة #}
    <div class="row mb-4">
      <div class="col-lg-6">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">
              <i class="fas fa-chart-line"></i>
              {{ text_accuracy_trend }}
            </h3>
          </div>
          <div class="card-body">
            <canvas id="accuracyTrendChart" height="300"></canvas>
          </div>
        </div>
      </div>
      <div class="col-lg-6">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">
              <i class="fas fa-chart-bar"></i>
              {{ text_confidence_intervals }}
            </h3>
          </div>
          <div class="card-body">
            <canvas id="confidenceIntervalsChart" height="300"></canvas>
          </div>
        </div>
      </div>
    </div>

    {# الفلاتر المتقدمة #}
    <div class="card d-none d-lg-block mb-3" id="filter-forecast">
      <div class="card-header">
        <h3 class="card-title">
          <i class="fas fa-filter"></i>
          {{ text_filter }}
        </h3>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-lg-3">
            <div class="mb-3">
              <label for="input-period" class="form-label">{{ entry_period }}</label>
              <select name="filter_period" id="input-period" class="form-select">
                <option value="">{{ text_all_periods }}</option>
                {% for period in periods %}
                  <option value="{{ period.value }}"{% if period.value == filter_period %} selected{% endif %}>{{ period.text }}</option>
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="col-lg-3">
            <div class="mb-3">
              <label for="input-type" class="form-label">{{ entry_type }}</label>
              <select name="filter_type" id="input-type" class="form-select">
                <option value="">{{ text_all_types }}</option>
                {% for type in types %}
                  <option value="{{ type.value }}"{% if type.value == filter_type %} selected{% endif %}>{{ type.text }}</option>
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="col-lg-3">
            <div class="mb-3">
              <label for="input-method" class="form-label">{{ entry_method }}</label>
              <select name="filter_method" id="input-method" class="form-select">
                <option value="">{{ text_all_methods }}</option>
                {% for method in methods %}
                  <option value="{{ method.value }}"{% if method.value == filter_method %} selected{% endif %}>{{ method.text }}</option>
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="col-lg-3">
            <div class="mb-3">
              <label for="input-status" class="form-label">{{ entry_status }}</label>
              <select name="filter_status" id="input-status" class="form-select">
                <option value="">{{ text_all_statuses }}</option>
                {% for status in statuses %}
                  <option value="{{ status.value }}"{% if status.value == filter_status %} selected{% endif %}>{{ status.text }}</option>
                {% endfor %}
              </select>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-lg-3">
            <div class="mb-3">
              <label for="input-accuracy" class="form-label">{{ entry_accuracy }}</label>
              <select name="filter_accuracy" id="input-accuracy" class="form-select">
                <option value="">{{ text_all_accuracy }}</option>
                <option value="high"{% if filter_accuracy == 'high' %} selected{% endif %}>{{ text_high_accuracy }} (>90%)</option>
                <option value="medium"{% if filter_accuracy == 'medium' %} selected{% endif %}>{{ text_medium_accuracy }} (70-90%)</option>
                <option value="low"{% if filter_accuracy == 'low' %} selected{% endif %}>{{ text_low_accuracy }} (<70%)</option>
              </select>
            </div>
          </div>
          <div class="col-lg-3">
            <div class="mb-3">
              <label for="input-date-from" class="form-label">{{ entry_date_from }}</label>
              <input type="date" name="filter_date_from" value="{{ filter_date_from }}" id="input-date-from" class="form-control">
            </div>
          </div>
          <div class="col-lg-3">
            <div class="mb-3">
              <label for="input-date-to" class="form-label">{{ entry_date_to }}</label>
              <input type="date" name="filter_date_to" value="{{ filter_date_to }}" id="input-date-to" class="form-control">
            </div>
          </div>
          <div class="col-lg-3">
            <div class="mb-3">
              <label class="form-label">&nbsp;</label>
              <div class="d-grid gap-2">
                <button type="button" onclick="filter();" class="btn btn-primary">
                  <i class="fas fa-search"></i> {{ button_search }}
                </button>
              </div>
            </div>
          </div>
        </div>
        <div class="text-end">
          <button type="button" id="button-filter" class="btn btn-light">
            <i class="fas fa-eraser"></i> {{ button_clear }}
          </button>
        </div>
      </div>
    </div>

    {# جدول التوقعات #}
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">
          <i class="fas fa-list"></i>
          {{ text_list }}
        </h3>
        <div class="card-tools">
          <div class="input-group input-group-sm" style="width: 200px;">
            <input type="text" name="search" class="form-control float-right" placeholder="{{ text_search }}" value="{{ search }}">
            <div class="input-group-append">
              <button type="submit" class="btn btn-default">
                <i class="fas fa-search"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
      <div class="card-body table-responsive p-0">
        <form id="form-forecast" method="post" data-oc-toggle="ajax" data-oc-load="{{ action }}" data-oc-target="#content">
          <table class="table table-hover text-nowrap">
            <thead>
              <tr>
                <th style="width: 1px;" class="text-center">
                  <input type="checkbox" onclick="$('input[name*=\'selected\']').prop('checked', $(this).prop('checked'));">
                </th>
                <th>
                  <a href="{{ sort_period }}" class="text-decoration-none">
                    {{ column_period }}
                    {% if sort == 'period' %}
                      <i class="fas fa-sort-{{ order|lower == 'desc' ? 'down' : 'up' }}"></i>
                    {% endif %}
                  </a>
                </th>
                <th>{{ column_type }}</th>
                <th>{{ column_method }}</th>
                <th>
                  <a href="{{ sort_predicted }}" class="text-decoration-none">
                    {{ column_predicted_amount }}
                    {% if sort == 'predicted_amount' %}
                      <i class="fas fa-sort-{{ order|lower == 'desc' ? 'down' : 'up' }}"></i>
                    {% endif %}
                  </a>
                </th>
                <th>{{ column_actual_amount }}</th>
                <th>
                  <a href="{{ sort_accuracy }}" class="text-decoration-none">
                    {{ column_accuracy }}
                    {% if sort == 'accuracy' %}
                      <i class="fas fa-sort-{{ order|lower == 'desc' ? 'down' : 'up' }}"></i>
                    {% endif %}
                  </a>
                </th>
                <th>{{ column_confidence }}</th>
                <th>{{ column_status }}</th>
                <th>
                  <a href="{{ sort_date }}" class="text-decoration-none">
                    {{ column_date_created }}
                    {% if sort == 'date_created' %}
                      <i class="fas fa-sort-{{ order|lower == 'desc' ? 'down' : 'up' }}"></i>
                    {% endif %}
                  </a>
                </th>
                <th class="text-end">{{ column_action }}</th>
              </tr>
            </thead>
            <tbody>
              {% if forecasts %}
                {% for forecast in forecasts %}
                  <tr>
                    <td class="text-center">
                      <input type="checkbox" name="selected[]" value="{{ forecast.forecast_id }}">
                    </td>
                    <td>
                      <div>
                        <strong>{{ forecast.period }}</strong>
                        <br><small class="text-muted">{{ forecast.start_date }} - {{ forecast.end_date }}</small>
                      </div>
                    </td>
                    <td>
                      <span class="badge bg-{{ forecast.type_color }}">{{ forecast.forecast_type }}</span>
                    </td>
                    <td>
                      <span class="badge bg-{{ forecast.method_color }}">{{ forecast.method }}</span>
                    </td>
                    <td>
                      <strong>{{ forecast.predicted_amount }}</strong>
                      {% if forecast.confidence_interval_lower and forecast.confidence_interval_upper %}
                        <br><small class="text-muted">{{ forecast.confidence_interval_lower }} - {{ forecast.confidence_interval_upper }}</small>
                      {% endif %}
                    </td>
                    <td>
                      {% if forecast.actual_amount %}
                        <strong>{{ forecast.actual_amount }}</strong>
                        {% if forecast.variance %}
                          <br><small class="text-{{ forecast.variance > 0 ? 'success' : 'danger' }}">
                            {{ forecast.variance > 0 ? '+' : '' }}{{ forecast.variance }}%
                          </small>
                        {% endif %}
                      {% else %}
                        <span class="text-muted">{{ text_pending }}</span>
                      {% endif %}
                    </td>
                    <td>
                      {% if forecast.accuracy %}
                        <div class="d-flex align-items-center">
                          <div class="progress me-2" style="width: 60px; height: 8px;">
                            <div class="progress-bar bg-{{ forecast.accuracy_color }}" style="width: {{ forecast.accuracy }}%"></div>
                          </div>
                          <span class="fw-bold">{{ forecast.accuracy }}%</span>
                        </div>
                      {% else %}
                        <span class="text-muted">{{ text_not_validated }}</span>
                      {% endif %}
                    </td>
                    <td>
                      <span class="badge bg-secondary">{{ forecast.confidence_level }}%</span>
                    </td>
                    <td>
                      <span class="badge bg-{{ forecast.status_color }}">{{ forecast.status }}</span>
                    </td>
                    <td>{{ forecast.date_created }}</td>
                    <td class="text-end">
                      <div class="btn-group">
                        <button type="button" class="btn btn-sm btn-light dropdown-toggle" data-bs-toggle="dropdown">
                          <i class="fas fa-cog"></i>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                          <li><a class="dropdown-item" href="{{ forecast.view }}"><i class="fas fa-eye"></i> {{ text_view }}</a></li>
                          <li><a class="dropdown-item" href="{{ forecast.edit }}"><i class="fas fa-edit"></i> {{ text_edit }}</a></li>
                          <li><hr class="dropdown-divider"></li>
                          {% if not forecast.actual_amount %}
                            <li><a class="dropdown-item" href="javascript:void(0);" onclick="validateForecast({{ forecast.forecast_id }});"><i class="fas fa-check"></i> {{ text_validate }}</a></li>
                          {% endif %}
                          <li><a class="dropdown-item" href="javascript:void(0);" onclick="duplicateForecast({{ forecast.forecast_id }});"><i class="fas fa-copy"></i> {{ text_duplicate }}</a></li>
                          <li><hr class="dropdown-divider"></li>
                          <li><a class="dropdown-item text-danger" href="javascript:void(0);" onclick="deleteForecast({{ forecast.forecast_id }});"><i class="fas fa-trash"></i> {{ text_delete }}</a></li>
                        </ul>
                      </div>
                    </td>
                  </tr>
                {% endfor %}
              {% else %}
                <tr>
                  <td class="text-center" colspan="11">{{ text_no_results }}</td>
                </tr>
              {% endif %}
            </tbody>
          </table>
        </form>
      </div>
      <div class="card-footer">
        <div class="row">
          <div class="col-sm-6 text-start">{{ pagination }}</div>
          <div class="col-sm-6 text-end">{{ results }}</div>
        </div>
      </div>
    </div>
  </div>
</div>

{# مودال مقارنة الخوارزميات #}
<div class="modal fade" id="modal-compare" tabindex="-1">
  <div class="modal-dialog modal-xl">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title">{{ text_compare_algorithms }}</h4>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <form id="form-compare">
          <div class="row mb-3">
            <div class="col-md-4">
              <label for="compare-period" class="form-label">{{ text_period }}</label>
              <select name="period" id="compare-period" class="form-select">
                <option value="daily">{{ text_daily }}</option>
                <option value="weekly">{{ text_weekly }}</option>
                <option value="monthly" selected>{{ text_monthly }}</option>
                <option value="quarterly">{{ text_quarterly }}</option>
              </select>
            </div>
            <div class="col-md-4">
              <label for="compare-horizon" class="form-label">{{ text_horizon }}</label>
              <select name="horizon" id="compare-horizon" class="form-select">
                <option value="7">7 {{ text_days }}</option>
                <option value="30" selected>30 {{ text_days }}</option>
                <option value="60">60 {{ text_days }}</option>
                <option value="90">90 {{ text_days }}</option>
              </select>
            </div>
            <div class="col-md-4">
              <label for="compare-confidence" class="form-label">{{ text_confidence_level }}</label>
              <select name="confidence_level" id="compare-confidence" class="form-select">
                <option value="90">90%</option>
                <option value="95" selected>95%</option>
                <option value="99">99%</option>
              </select>
            </div>
          </div>
          <div class="mb-3">
            <label class="form-label">{{ text_algorithms }}</label>
            <div class="row">
              <div class="col-md-6">
                <div class="form-check">
                  <input type="checkbox" name="algorithms[]" value="linear" id="alg-linear" class="form-check-input" checked>
                  <label for="alg-linear" class="form-check-label">{{ text_linear_regression }}</label>
                </div>
                <div class="form-check">
                  <input type="checkbox" name="algorithms[]" value="moving_average" id="alg-moving" class="form-check-input" checked>
                  <label for="alg-moving" class="form-check-label">{{ text_moving_average }}</label>
                </div>
                <div class="form-check">
                  <input type="checkbox" name="algorithms[]" value="exponential" id="alg-exponential" class="form-check-input" checked>
                  <label for="alg-exponential" class="form-check-label">{{ text_exponential_smoothing }}</label>
                </div>
              </div>
              <div class="col-md-6">
                <div class="form-check">
                  <input type="checkbox" name="algorithms[]" value="seasonal" id="alg-seasonal" class="form-check-input" checked>
                  <label for="alg-seasonal" class="form-check-label">{{ text_seasonal_decomposition }}</label>
                </div>
                <div class="form-check">
                  <input type="checkbox" name="algorithms[]" value="arima" id="alg-arima" class="form-check-input" checked>
                  <label for="alg-arima" class="form-check-label">{{ text_arima_model }}</label>
                </div>
                <div class="form-check">
                  <input type="checkbox" name="algorithms[]" value="neural" id="alg-neural" class="form-check-input" checked>
                  <label for="alg-neural" class="form-check-label">{{ text_neural_network }}</label>
                </div>
              </div>
            </div>
          </div>
        </form>
        <div id="comparison-results" class="d-none">
          <hr>
          <h5>{{ text_comparison_results }}</h5>
          <div class="table-responsive">
            <table class="table table-striped">
              <thead>
                <tr>
                  <th>{{ text_algorithm }}</th>
                  <th>{{ text_predicted_amount }}</th>
                  <th>{{ text_accuracy }}</th>
                  <th>{{ text_execution_time }}</th>
                  <th>{{ text_complexity }}</th>
                </tr>
              </thead>
              <tbody id="comparison-table-body">
              </tbody>
            </table>
          </div>
          <div class="mt-3">
            <canvas id="comparisonChart" height="300"></canvas>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ button_close }}</button>
        <button type="button" class="btn btn-primary" onclick="compareAlgorithms();">
          <i class="fas fa-chart-line"></i> {{ button_compare }}
        </button>
      </div>
    </div>
  </div>
</div>

{# مودال التحقق من صحة التوقع #}
<div class="modal fade" id="modal-validate" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title">{{ text_validate_forecast }}</h4>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <form id="form-validate">
          <input type="hidden" name="forecast_id" id="validate-forecast-id">
          <div class="mb-3">
            <label for="actual-amount" class="form-label">{{ text_actual_amount }}</label>
            <input type="number" name="actual_amount" id="actual-amount" class="form-control" step="0.01" placeholder="{{ text_enter_actual_amount }}">
            <div class="form-text">{{ text_actual_amount_help }}</div>
          </div>
          <div class="mb-3">
            <label for="validation-notes" class="form-label">{{ text_notes }}</label>
            <textarea name="notes" id="validation-notes" class="form-control" rows="3" placeholder="{{ text_validation_notes_placeholder }}"></textarea>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ button_cancel }}</button>
        <button type="button" class="btn btn-success" onclick="validateForecastConfirm();">
          <i class="fas fa-check"></i> {{ button_validate }}
        </button>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript">
// تهيئة الرسوم البيانية
$(document).ready(function() {
    // رسم بياني للتوقع مقابل الفعلي
    var forecastVsActualCtx = document.getElementById('forecastVsActualChart').getContext('2d');
    var forecastVsActualChart = new Chart(forecastVsActualCtx, {
        type: 'line',
        data: {
            labels: [
                {% for data in forecast_vs_actual %}
                    '{{ data.date }}'{% if not loop.last %},{% endif %}
                {% endfor %}
            ],
            datasets: [{
                label: '{{ text_predicted }}',
                data: [
                    {% for data in forecast_vs_actual %}
                        {{ data.predicted }}{% if not loop.last %},{% endif %}
                    {% endfor %}
                ],
                borderColor: '#007bff',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                borderWidth: 2,
                fill: false
            }, {
                label: '{{ text_actual }}',
                data: [
                    {% for data in forecast_vs_actual %}
                        {{ data.actual }}{% if not loop.last %},{% endif %}
                    {% endfor %}
                ],
                borderColor: '#28a745',
                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                borderWidth: 2,
                fill: false
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            },
            plugins: {
                legend: {
                    position: 'top'
                }
            }
        }
    });

    // رسم بياني لدقة الطرق
    var methodAccuracyCtx = document.getElementById('methodAccuracyChart').getContext('2d');
    var methodAccuracyChart = new Chart(methodAccuracyCtx, {
        type: 'doughnut',
        data: {
            labels: [
                {% for method in method_accuracy %}
                    '{{ method.name }}'{% if not loop.last %},{% endif %}
                {% endfor %}
            ],
            datasets: [{
                data: [
                    {% for method in method_accuracy %}
                        {{ method.accuracy }}{% if not loop.last %},{% endif %}
                    {% endfor %}
                ],
                backgroundColor: [
                    '#007bff', '#28a745', '#ffc107', '#dc3545', '#17a2b8', '#6f42c1'
                ],
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // رسم بياني لاتجاه الدقة
    var accuracyTrendCtx = document.getElementById('accuracyTrendChart').getContext('2d');
    var accuracyTrendChart = new Chart(accuracyTrendCtx, {
        type: 'line',
        data: {
            labels: [
                {% for trend in accuracy_trend %}
                    '{{ trend.date }}'{% if not loop.last %},{% endif %}
                {% endfor %}
            ],
            datasets: [{
                label: '{{ text_accuracy }}',
                data: [
                    {% for trend in accuracy_trend %}
                        {{ trend.accuracy }}{% if not loop.last %},{% endif %}
                    {% endfor %}
                ],
                borderColor: '#28a745',
                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });

    // رسم بياني لفترات الثقة
    var confidenceIntervalsCtx = document.getElementById('confidenceIntervalsChart').getContext('2d');
    var confidenceIntervalsChart = new Chart(confidenceIntervalsCtx, {
        type: 'bar',
        data: {
            labels: [
                {% for interval in confidence_intervals %}
                    '{{ interval.forecast_name }}'{% if not loop.last %},{% endif %}
                {% endfor %}
            ],
            datasets: [{
                label: '{{ text_lower_bound }}',
                data: [
                    {% for interval in confidence_intervals %}
                        {{ interval.lower }}{% if not loop.last %},{% endif %}
                    {% endfor %}
                ],
                backgroundColor: 'rgba(220, 53, 69, 0.5)',
                borderColor: '#dc3545',
                borderWidth: 1
            }, {
                label: '{{ text_predicted }}',
                data: [
                    {% for interval in confidence_intervals %}
                        {{ interval.predicted }}{% if not loop.last %},{% endif %}
                    {% endfor %}
                ],
                backgroundColor: 'rgba(0, 123, 255, 0.5)',
                borderColor: '#007bff',
                borderWidth: 1
            }, {
                label: '{{ text_upper_bound }}',
                data: [
                    {% for interval in confidence_intervals %}
                        {{ interval.upper }}{% if not loop.last %},{% endif %}
                    {% endfor %}
                ],
                backgroundColor: 'rgba(40, 167, 69, 0.5)',
                borderColor: '#28a745',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            },
            plugins: {
                legend: {
                    position: 'top'
                }
            }
        }
    });

    // تهيئة التلميحات
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// تحديث الرسم البياني للتوقع مقابل الفعلي
function updateForecastChart() {
    var period = $('#chart-period').val();

    $.ajax({
        url: 'index.php?route=crm/sales_forecast/getForecastData&user_token={{ user_token }}',
        type: 'post',
        data: {period: period},
        dataType: 'json',
        success: function(json) {
            if (json['success']) {
                // تحديث البيانات
                // سيتم تنفيذ هذا عند الحاجة
            }
        },
        error: function(xhr, ajaxOptions, thrownError) {
            console.log(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
    });
}

// وظائف الفلترة والبحث
function filter() {
    var url = 'index.php?route=crm/sales_forecast&user_token={{ user_token }}';

    var filter_period = $('select[name=\'filter_period\']').val();
    if (filter_period) {
        url += '&filter_period=' + encodeURIComponent(filter_period);
    }

    var filter_type = $('select[name=\'filter_type\']').val();
    if (filter_type) {
        url += '&filter_type=' + encodeURIComponent(filter_type);
    }

    var filter_method = $('select[name=\'filter_method\']').val();
    if (filter_method) {
        url += '&filter_method=' + encodeURIComponent(filter_method);
    }

    var filter_status = $('select[name=\'filter_status\']').val();
    if (filter_status) {
        url += '&filter_status=' + encodeURIComponent(filter_status);
    }

    var filter_accuracy = $('select[name=\'filter_accuracy\']').val();
    if (filter_accuracy) {
        url += '&filter_accuracy=' + encodeURIComponent(filter_accuracy);
    }

    var filter_date_from = $('input[name=\'filter_date_from\']').val();
    if (filter_date_from) {
        url += '&filter_date_from=' + encodeURIComponent(filter_date_from);
    }

    var filter_date_to = $('input[name=\'filter_date_to\']').val();
    if (filter_date_to) {
        url += '&filter_date_to=' + encodeURIComponent(filter_date_to);
    }

    location = url;
}

// مقارنة الخوارزميات
function compareAlgorithms() {
    var formData = $('#form-compare').serialize();

    $.ajax({
        url: 'index.php?route=crm/sales_forecast/compareAlgorithms&user_token={{ user_token }}',
        type: 'post',
        data: formData,
        dataType: 'json',
        beforeSend: function() {
            $('#modal-compare .btn-primary').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> {{ text_comparing }}');
        },
        complete: function() {
            $('#modal-compare .btn-primary').prop('disabled', false).html('<i class="fas fa-chart-line"></i> {{ button_compare }}');
        },
        success: function(json) {
            if (json['success']) {
                displayComparisonResults(json['data']);
                $('#comparison-results').removeClass('d-none');
            }

            if (json['error']) {
                alert(json['error']);
            }
        },
        error: function(xhr, ajaxOptions, thrownError) {
            console.log(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
    });
}

// عرض نتائج المقارنة
function displayComparisonResults(data) {
    var tbody = $('#comparison-table-body');
    tbody.empty();

    var chartLabels = [];
    var chartData = [];
    var chartColors = ['#007bff', '#28a745', '#ffc107', '#dc3545', '#17a2b8', '#6f42c1'];

    $.each(data.comparison_results, function(algorithm, result) {
        var row = '<tr>' +
            '<td><strong>' + result.name + '</strong><br><small class="text-muted">' + result.description + '</small></td>' +
            '<td>' + result.predicted_amount + '</td>' +
            '<td><span class="badge bg-' + getAccuracyColor(result.performance_metrics.accuracy) + '">' + result.performance_metrics.accuracy + '%</span></td>' +
            '<td>' + result.execution_time + 'ms</td>' +
            '<td><span class="badge bg-secondary">' + result.complexity + '</span></td>' +
            '</tr>';
        tbody.append(row);

        chartLabels.push(result.name);
        chartData.push(result.performance_metrics.accuracy);
    });

    // رسم بياني للمقارنة
    var comparisonCtx = document.getElementById('comparisonChart').getContext('2d');
    new Chart(comparisonCtx, {
        type: 'bar',
        data: {
            labels: chartLabels,
            datasets: [{
                label: '{{ text_accuracy }}',
                data: chartData,
                backgroundColor: chartColors.slice(0, chartData.length),
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
}

// الحصول على لون الدقة
function getAccuracyColor(accuracy) {
    if (accuracy >= 90) return 'success';
    if (accuracy >= 70) return 'warning';
    return 'danger';
}

// التحقق من صحة التوقع
function validateForecast(forecast_id) {
    $('#validate-forecast-id').val(forecast_id);
    $('#modal-validate').modal('show');
}

function validateForecastConfirm() {
    var forecast_id = $('#validate-forecast-id').val();
    var actual_amount = $('#actual-amount').val();
    var notes = $('#validation-notes').val();

    if (!actual_amount) {
        alert('{{ error_actual_amount_required }}');
        return;
    }

    $.ajax({
        url: 'index.php?route=crm/sales_forecast/validate&user_token={{ user_token }}',
        type: 'post',
        data: {
            forecast_id: forecast_id,
            actual_amount: actual_amount,
            notes: notes
        },
        dataType: 'json',
        beforeSend: function() {
            $('#modal-validate .btn-success').prop('disabled', true);
        },
        complete: function() {
            $('#modal-validate .btn-success').prop('disabled', false);
        },
        success: function(json) {
            if (json['success']) {
                $('#alert').prepend('<div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> ' + json['success'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
                $('#modal-validate').modal('hide');
                location.reload();
            }

            if (json['error']) {
                $('#alert').prepend('<div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> ' + json['error'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
            }
        },
        error: function(xhr, ajaxOptions, thrownError) {
            console.log(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
    });
}

// نسخ التوقع
function duplicateForecast(forecast_id) {
    if (confirm('{{ text_confirm_duplicate }}')) {
        $.ajax({
            url: 'index.php?route=crm/sales_forecast/duplicate&user_token={{ user_token }}',
            type: 'post',
            data: {forecast_id: forecast_id},
            dataType: 'json',
            success: function(json) {
                if (json['success']) {
                    $('#alert').prepend('<div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> ' + json['success'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
                    location.reload();
                }

                if (json['error']) {
                    $('#alert').prepend('<div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> ' + json['error'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
                }
            },
            error: function(xhr, ajaxOptions, thrownError) {
                console.log(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
            }
        });
    }
}

// حذف التوقع
function deleteForecast(forecast_id) {
    if (confirm('{{ text_confirm_delete }}')) {
        $.ajax({
            url: 'index.php?route=crm/sales_forecast/delete&user_token={{ user_token }}',
            type: 'post',
            data: {forecast_id: forecast_id},
            dataType: 'json',
            success: function(json) {
                if (json['success']) {
                    $('#alert').prepend('<div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> ' + json['success'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
                    location.reload();
                }

                if (json['error']) {
                    $('#alert').prepend('<div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> ' + json['error'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
                }
            },
            error: function(xhr, ajaxOptions, thrownError) {
                console.log(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
            }
        });
    }
}

// تصدير البيانات
function exportForecasts() {
    var url = 'index.php?route=crm/sales_forecast/export&user_token={{ user_token }}';
    window.open(url, '_blank');
}

// إعادة تعيين الفلاتر
$('#button-filter').click(function() {
    $('select[name=\'filter_period\']').val('');
    $('select[name=\'filter_type\']').val('');
    $('select[name=\'filter_method\']').val('');
    $('select[name=\'filter_status\']').val('');
    $('select[name=\'filter_accuracy\']').val('');
    $('input[name=\'filter_date_from\']').val('');
    $('input[name=\'filter_date_to\']').val('');

    filter();
});
</script>

{{ footer }}
