# 📚 دليل نظام AYM ERP الشامل
## أول نظام ERP بالذكاء الاصطناعي + التجارة الإلكترونية

---

## 🎯 نظرة عامة على النظام

### ما هو AYM ERP؟
**AYM ERP** هو أول نظام تخطيط موارد المؤسسات (ERP) مدعوم بالذكاء الاصطناعي مع تكامل كامل للتجارة الإلكترونية، مصمم خصيصاً للشركات العربية لمنافسة عمالقة الصناعة مثل SAP وMicrosoft وOracle.

### الهدف الاستراتيجي
- **منافسة Odoo + WooCommerce/Shopify** مجتمعين
- **التفوق على SAP/Microsoft/Oracle** في السوق العربي
- **تقديم حل متكامل** بتكلفة أقل وأداء أفضل
- **دعم كامل للغة العربية** والمتطلبات المحلية

---

## 🏗️ البنية التقنية

### المعمارية الأساسية
- **الأساس:** OpenCart 3.0.3.x مع تعديلات جذرية
- **النمط:** MVC (Model-View-Controller)
- **قاعدة البيانات:** MySQL مع بادئة `cod_` بدلاً من `oc_`
- **الهيكل:** مجلد `dashboard` بدلاً من `admin`

### المكونات الرئيسية
```
dashboard/
├── controller/     # منطق التحكم
├── model/         # طبقة البيانات
├── view/          # واجهة المستخدم
└── language/      # ملفات اللغة (عربي/إنجليزي)
```

### قاعدة البيانات
- **340+ جدول متخصص** في `db.txt`
- **12 جدول إضافي** في `new.sql`
- **نظام محاسبي متقدم:** الجرد المستمر + المتوسط المرجح للتكلفة (WAC)
- **دعم متعدد:** فروع، عملات، لغات

---

## 🔐 نظام الصلاحيات المتقدم

### آلية العمل
```php
// فحص الصلاحيات الأساسية
$this->user->hasPermission('access', 'module/controller')

// فحص الصلاحيات المتقدمة
$this->user->hasKey('advanced_feature_name')
```

### الصلاحيات المتقدمة (33 صلاحية)

#### 🎛️ إدارة النظام (6)
- `advanced_notifications` - إعدادات الإشعارات المتقدمة
- `system_monitoring` - مراقبة النظام
- `export_data` - تصدير البيانات
- `advanced_settings` - الإعدادات المتقدمة
- `user_management` - إدارة المستخدمين المتقدمة
- `system_administration` - إدارة النظام

#### 💬 التواصل الداخلي (10)
- `create_teams` - إنشاء الفرق
- `manage_all_teams` - إدارة جميع الفرق
- `approve_team_requests` - الموافقة على طلبات الفرق
- `view_team_analytics` - عرض تحليلات الفرق
- `manage_meetings` - إدارة الاجتماعات المتقدمة
- `record_meetings` - تسجيل الاجتماعات
- `manage_announcements` - إدارة الإعلانات العامة
- `send_urgent_announcements` - إرسال إعلانات عاجلة
- `manage_group_chats` - إدارة المحادثات الجماعية
- `access_archived_chats` - الوصول للمحادثات المؤرشفة

#### ⚙️ سير العمل (10)
- `create_workflows` - إنشاء سير العمل
- `edit_all_workflows` - تحرير جميع سير العمل
- `delete_workflows` - حذف سير العمل
- `manage_workflow_templates` - إدارة قوالب سير العمل
- `view_workflow_analytics` - عرض تحليلات سير العمل
- `advanced_workflow_editor` - المحرر المرئي المتقدم
- `execute_workflows_manually` - تنفيذ سير العمل يدوياً
- `pause_workflows` - إيقاف سير العمل
- `export_workflows` - تصدير سير العمل
- `import_workflows` - استيراد سير العمل

#### 📄 إدارة المستندات (6)
- `approve_documents` - موافقة المستندات
- `archive_documents` - أرشفة المستندات
- `manage_document_templates` - إدارة قوالب المستندات
- `manage_document_versions` - إدارة إصدارات المستندات
- `view_deleted_documents` - عرض المستندات المحذوفة
- `restore_documents` - استعادة المستندات

#### 📊 نظام التدقيق (7)
- `view_audit_trail` - عرض مسار المراجعة
- `view_performance_logs` - عرض سجلات الأداء
- `view_system_logs` - عرض سجلات النظام
- `view_user_activity` - عرض نشاط المستخدمين
- `export_logs` - تصدير السجلات
- `purge_old_logs` - حذف السجلات القديمة
- `manage_audit_settings` - إدارة إعدادات التدقيق

---

## 🔧 الخدمات المركزية

### Central Service Manager
**الموقع:** `dashboard/model/core/central_service_manager.php`

**الوظائف الأساسية:**
- إدارة الإشعارات الموحدة
- تسجيل الأنشطة
- إدارة المستندات
- التكامل مع الذكاء الاصطناعي

**الاستخدام:**
```php
// تحميل الخدمة (إجباري في كل كونترولر)
$this->load->model('core/central_service_manager');

// إرسال إشعار
$this->model_core_central_service_manager->sendNotification($data);

// تسجيل نشاط
$this->model_core_central_service_manager->logActivity($action, $data);
```

### خدمات أخرى مهمة
- **المحاسبة:** `model/accounts/journal.php`
- **الإشعارات:** `model/communication/unified_notification.php`
- **التدقيق:** `model/activity_log.php`
- **المستندات:** `model/unified_document.php`
- **الذكاء الاصطناعي:** `model/ai/ai_assistant.php`

---

## 💬 نظام التواصل الداخلي المتقدم

### الميزات الرئيسية
- **رسائل مشفرة** مع إيصالات قراءة
- **محادثات جماعية** مع أدوار متقدمة
- **اجتماعات افتراضية** مع تسجيل
- **إدارة فرق هرمية** مع تقييم أداء
- **إعلانات ذكية** مع استهداف دقيق

### الجداول الأساسية

#### 📨 الرسائل
```sql
cod_message              -- الرسائل الأساسية
cod_message_recipient    -- مستقبلي الرسائل
```

#### 💬 المحادثات
```sql
cod_chat                 -- المحادثات والقنوات
cod_chat_message         -- رسائل المحادثات
cod_chat_member          -- أعضاء المحادثات
```

#### 👥 الفرق
```sql
cod_team                 -- الفرق والمجموعات
cod_team_member          -- أعضاء الفرق
```

#### 🤝 الاجتماعات
```sql
cod_meeting              -- الاجتماعات المتقدمة
cod_meeting_attendee     -- حضور الاجتماعات
```

#### 📢 الإعلانات
```sql
cod_announcement         -- الإعلانات العامة
cod_announcement_read    -- قراءة الإعلانات
cod_announcement_target  -- استهداف الإعلانات
cod_announcement_analytics -- تحليلات الإعلانات
cod_announcement_schedule  -- جدولة الإعلانات
```

### الملفات الرئيسية
```
dashboard/controller/communication/
├── messages.php         -- إدارة الرسائل
├── chat.php            -- المحادثات المباشرة
├── teams.php           -- إدارة الفرق
└── announcements.php   -- الإعلانات

dashboard/model/communication/
├── messages.php
├── chat.php
├── teams.php
├── announcements.php
├── notifications.php
├── unified_notification.php
└── advanced_internal_communication.php
```

---

## ⚙️ نظام سير العمل المرئي

### المحرر المرئي (شبيه n8n)
- **سحب وإفلات العقد** لبناء سير العمل
- **أنواع عقد متنوعة:** موافقة، إشعار، تأخير، API، إلخ
- **روابط شرطية** ومتقدمة
- **معاينة مباشرة** للتنفيذ

### أنواع العقد المدعومة
- `start` - نقطة البداية
- `end` - نقطة النهاية
- `task` - مهمة
- `decision` - قرار
- `approval` - موافقة
- `notification` - إشعار
- `delay` - تأخير
- `script` - سكريبت
- `api_call` - استدعاء API
- `email` - بريد إلكتروني
- `sms` - رسالة نصية

### الجداول المتقدمة
```sql
cod_unified_workflow     -- سير العمل الأساسي
cod_workflow_request     -- طلبات سير العمل
cod_workflow_step        -- خطوات سير العمل
cod_workflow_approval    -- موافقات سير العمل
cod_workflow_execution   -- تنفيذ سير العمل
cod_workflow_node        -- عقد سير العمل المرئية
cod_workflow_connection  -- روابط العقد
cod_workflow_template    -- قوالب سير العمل
```

### الملفات الرئيسية
```
dashboard/controller/workflow/
├── visual_editor.php         -- المحرر الأساسي
├── advanced_visual_editor.php -- المحرر المتقدم
├── workflow.php              -- إدارة سير العمل
├── task.php                  -- إدارة المهام
├── designer.php              -- مصمم سير العمل
├── actions.php               -- إجراءات سير العمل
├── conditions.php            -- شروط سير العمل
└── triggers.php              -- محفزات سير العمل
```

---

## 📄 نظام إدارة المستندات

### الميزات المتقدمة
- **موافقات متعددة المستويات**
- **إدارة إصدارات متقدمة**
- **أرشفة ذكية**
- **قوالب قابلة للتخصيص**
- **تتبع شامل للتغييرات**

### الملفات الرئيسية
```
dashboard/controller/documents/
├── approval.php      -- موافقة المستندات
├── archive.php       -- أرشيف المستندات
├── templates.php     -- قوالب المستندات
└── versioning.php    -- إدارة الإصدارات
```

---

## 📊 نظام التدقيق والسجلات

### أنواع السجلات
- **مسار المراجعة** - تتبع جميع التغييرات
- **سجلات الأداء** - مراقبة أداء النظام
- **سجلات النظام** - أخطاء وتحذيرات
- **نشاط المستخدمين** - تتبع أنشطة المستخدمين

### الملفات الرئيسية
```
dashboard/controller/logging/
├── audit_trail.php    -- مسار المراجعة
├── performance.php    -- سجلات الأداء
├── system_logs.php    -- سجلات النظام
└── user_activity.php  -- نشاط المستخدمين
```

---

## 🎨 واجهة المستخدم

### الهيدر المتطور
**الملف:** `dashboard/view/template/common/header.twig`

**الميزات:**
- **مركز إشعارات تفاعلي** مع عدادات مباشرة
- **لوحات تنبيهات سريعة** للمؤشرات المهمة
- **أزرار متقدمة** للمستخدمين المصرح لهم
- **بحث ذكي** عبر النظام
- **إعدادات شخصية** متقدمة

### العمود الجانبي
**الملف:** `dashboard/controller/common/column_left.php`

**التنظيم:**
- **مجموعات منطقية** للوحدات
- **ترتيب حسب الأولوية** والاستخدام
- **فحص صلاحيات** لكل عنصر
- **أيقونات واضحة** ومعبرة

---

## 🌐 دعم اللغات

### اللغات المدعومة
- **العربية** (الأساسية)
- **الإنجليزية** (الثانوية)

### هيكل ملفات اللغة
```
dashboard/language/
├── ar/              -- العربية
│   ├── common/
│   ├── communication/
│   ├── workflow/
│   ├── documents/
│   └── logging/
└── en-gb/           -- الإنجليزية
    ├── common/
    ├── communication/
    ├── workflow/
    ├── documents/
    └── logging/
```

---

## 🚀 التفوق على المنافسين

### مقارنة مع Microsoft Teams + Power Automate
✅ **تكامل أعمق** مع العمليات التجارية  
✅ **أمان متقدم** ومشفر محلياً  
✅ **تحكم كامل** في البيانات  
✅ **تكلفة أقل** بكثير  

### مقارنة مع Slack + Zapier
✅ **لا يحتاج اتصال خارجي**  
✅ **أداء أسرع** وأكثر استقراراً  
✅ **خصوصية أكبر** للبيانات  
✅ **واجهة عربية كاملة**  

### مقارنة مع Odoo + n8n
✅ **ميزات أكثر تطوراً**  
✅ **واجهة أفضل وأسهل**  
✅ **تكامل أعمق** مع المحاسبة والمخزون  
✅ **دعم فني محلي**  

---

## 📈 إحصائيات النظام

### الملفات والمكونات
- **340+ جدول** في قاعدة البيانات الأساسية
- **12 جدول إضافي** للميزات المتقدمة
- **20+ كونترولر** محدث ومحسن
- **33 صلاحية متقدمة** للتحكم الدقيق
- **157 دالة** في الخدمات المركزية

### الوحدات المكتملة
✅ **نظام التواصل الداخلي** - مكتمل 100%  
✅ **محرر سير العمل المرئي** - مكتمل 100%  
✅ **إدارة المستندات** - مكتمل 100%  
✅ **نظام التدقيق والسجلات** - مكتمل 100%  
✅ **الهيدر والواجهة** - مكتمل 100%  

### الوحدات قيد التطوير
🔄 **شاشات الحسابات** - 7 شاشات متبقية  
🔄 **الذكاء الاصطناعي** - 18 نقطة تكامل  
🔄 **التجارة الإلكترونية** - تكامل متقدم  

---

## 🔧 دليل التطبيق

### متطلبات النظام
- **PHP 7.4+** مع امتدادات MySQL
- **MySQL 5.7+** أو MariaDB 10.2+
- **Apache/Nginx** مع mod_rewrite
- **SSL Certificate** للأمان

### خطوات التثبيت
1. **رفع الملفات** إلى الخادم
2. **تطبيق db.txt** لإنشاء قاعدة البيانات الأساسية
3. **تطبيق new.sql** للجداول والصلاحيات المتقدمة
4. **ضبط الصلاحيات** للمجلدات
5. **تكوين الإعدادات** الأساسية

### الصيانة الدورية
- **نسخ احتياطية** يومية لقاعدة البيانات
- **تنظيف السجلات** القديمة شهرياً
- **تحديث الصلاحيات** حسب الحاجة
- **مراقبة الأداء** مستمرة

---

## 📞 الدعم والمساعدة

### الموارد المتاحة
- **دليل المستخدم** التفصيلي (هذا الملف)
- **وثائق API** للمطورين
- **فيديوهات تدريبية** للمستخدمين
- **منتدى المجتمع** للأسئلة والحلول

### التواصل
- **البريد الإلكتروني:** <EMAIL>
- **الهاتف:** +20-xxx-xxx-xxxx
- **الموقع:** www.aym-erp.com
- **المنتدى:** forum.aym-erp.com

---

## 🎯 الخطوات التالية

### المرحلة القادمة
1. **إكمال شاشات الحسابات** (7 شاشات متبقية)
2. **تطوير نقاط تكامل الذكاء الاصطناعي** (18 نقطة)
3. **تحسين التجارة الإلكترونية** المتكاملة
4. **إضافة تحليلات متقدمة** ولوحات معلومات

### الرؤية المستقبلية
- **التوسع إقليمياً** في الشرق الأوسط
- **إضافة وحدات متخصصة** حسب القطاعات
- **تطوير تطبيق موبايل** متكامل
- **تكامل مع أنظمة حكومية** محلية

---

## 🔍 فهرس سريع للمطورين

### الملفات الحرجة
```
dashboard/model/core/central_service_manager.php    -- الخدمة المركزية
dashboard/controller/common/column_left.php         -- القائمة الجانبية
dashboard/view/template/common/header.twig          -- الهيدر
new.sql                                             -- التحديثات الجديدة
db.txt                                              -- قاعدة البيانات الأساسية
```

### أكواد مهمة للمطورين

#### تحميل الخدمة المركزية (إجباري)
```php
// في بداية كل دالة index()
$this->load->model('core/central_service_manager');
```

#### فحص الصلاحيات
```php
// الصلاحيات الأساسية
if (!$this->user->hasPermission('access', 'module/controller')) {
    $this->response->redirect($this->url->link('error/permission', 'user_token=' . $this->session->data['user_token'], true));
}

// الصلاحيات المتقدمة
$data['can_advanced_feature'] = $this->user->hasKey('advanced_feature_name');
```

#### إرسال إشعار
```php
$notification_data = array(
    'user_id' => $user_id,
    'title' => 'عنوان الإشعار',
    'message' => 'نص الإشعار',
    'type' => 'info', // info, success, warning, error
    'url' => $this->url->link('target/page')
);
$this->model_core_central_service_manager->sendNotification($notification_data);
```

#### تسجيل نشاط
```php
$activity_data = array(
    'action' => 'create',
    'resource' => 'document',
    'resource_id' => $document_id,
    'details' => 'تم إنشاء مستند جديد'
);
$this->model_core_central_service_manager->logActivity($activity_data);
```

### قواعد التطوير
1. **لا تحذف** أي شيء قد نحتاجه لاحقاً
2. **اقرأ كل ملف** سطراً بسطر قبل التعديل
3. **تحقق من الترابطات** (controller/model/view)
4. **اختبر التكاملات** مع الخدمات المركزية
5. **وثق كل تغيير** مع السبب والهدف

### نصائح للأداء
- استخدم **التخزين المؤقت** للبيانات المتكررة
- **فهرس الجداول** المستخدمة بكثرة
- **ضغط الصور** والملفات الثابتة
- **تحسين الاستعلامات** المعقدة
- **مراقبة الذاكرة** والمعالج

---

## 📋 قائمة المراجعة للتطبيق

### قبل التطبيق
- [ ] نسخة احتياطية من قاعدة البيانات الحالية
- [ ] نسخة احتياطية من ملفات النظام
- [ ] اختبار البيئة التجريبية
- [ ] التأكد من صلاحيات الملفات

### أثناء التطبيق
- [ ] تطبيق new.sql خطوة بخطوة
- [ ] التحقق من إنشاء الجداول الجديدة
- [ ] اختبار الصلاحيات الجديدة
- [ ] التأكد من عمل الخدمات المركزية

### بعد التطبيق
- [ ] اختبار جميع الوحدات المحدثة
- [ ] التحقق من الإشعارات والسجلات
- [ ] اختبار سير العمل المرئي
- [ ] التأكد من أمان النظام

---

## 🎓 دليل التدريب السريع

### للمديرين
1. **لوحة المعلومات** - نظرة شاملة على النشاط
2. **إدارة الفرق** - تنظيم المجموعات والأدوار
3. **موافقة المستندات** - سير عمل الموافقات
4. **مراقبة الأداء** - تتبع مؤشرات الأداء

### للموظفين
1. **الرسائل والمحادثات** - التواصل اليومي
2. **المهام والمشاريع** - إدارة العمل
3. **رفع المستندات** - مشاركة الملفات
4. **الإشعارات** - متابعة التحديثات

### للمطورين
1. **واجهات البرمجة** - التكامل مع أنظمة أخرى
2. **سير العمل المرئي** - أتمتة العمليات
3. **السجلات والتدقيق** - مراقبة النظام
4. **الصلاحيات المتقدمة** - التحكم الدقيق

---

## 🔮 الميزات القادمة

### الإصدار 1.1 (Q2 2025)
- **تطبيق موبايل** أصلي لـ iOS و Android
- **تكامل WhatsApp Business** للتواصل الخارجي
- **ذكاء اصطناعي متقدم** للتنبؤ والتحليل
- **تقارير تفاعلية** مع Power BI

### الإصدار 1.2 (Q3 2025)
- **التجارة الإلكترونية المتقدمة** مع Shopify
- **نظام CRM متكامل** لإدارة العملاء
- **محاسبة ضريبية** متوافقة مع ETA
- **تكامل بنكي** مباشر

### الإصدار 2.0 (Q4 2025)
- **نسخة SaaS** متعددة المستأجرين
- **ذكاء اصطناعي توليدي** للمحتوى
- **واقع معزز** لإدارة المخزون
- **بلوك تشين** للأمان المتقدم

---

**© 2025 AYM ERP - أول نظام ERP بالذكاء الاصطناعي**
**النسخة:** 1.0 Enterprise Grade Plus
**تاريخ التحديث:** يناير 2025
**المطور:** فريق AYM للتطوير المتقدم
