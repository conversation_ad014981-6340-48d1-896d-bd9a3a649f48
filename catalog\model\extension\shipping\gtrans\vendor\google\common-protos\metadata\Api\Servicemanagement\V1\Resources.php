<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/servicemanagement/v1/resources.proto

namespace GPBMetadata\Google\Api\Servicemanagement\V1;

class Resources
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        \GPBMetadata\Google\Api\Annotations::initOnce();
        \GPBMetadata\Google\Api\ConfigChange::initOnce();
        \GPBMetadata\Google\Api\FieldBehavior::initOnce();
        \GPBMetadata\Google\Api\Metric::initOnce();
        \GPBMetadata\Google\Api\Quota::initOnce();
        \GPBMetadata\Google\Api\Service::initOnce();
        \GPBMetadata\Google\Longrunning\Operations::initOnce();
        \GPBMetadata\Google\Protobuf\Any::initOnce();
        \GPBMetadata\Google\Protobuf\FieldMask::initOnce();
        \GPBMetadata\Google\Protobuf\Timestamp::initOnce();
        \GPBMetadata\Google\Rpc\Status::initOnce();
        $pool->internalAddGeneratedFile(hex2bin(
            "0a91130a2f676f6f676c652f6170692f736572766963656d616e6167656d" .
            "656e742f76312f7265736f75726365732e70726f746f121f676f6f676c65" .
            "2e6170692e736572766963656d616e6167656d656e742e76311a1e676f6f" .
            "676c652f6170692f636f6e6669675f6368616e67652e70726f746f1a1f67" .
            "6f6f676c652f6170692f6669656c645f6265686176696f722e70726f746f" .
            "1a17676f6f676c652f6170692f6d65747269632e70726f746f1a16676f6f" .
            "676c652f6170692f71756f74612e70726f746f1a18676f6f676c652f6170" .
            "692f736572766963652e70726f746f1a23676f6f676c652f6c6f6e677275" .
            "6e6e696e672f6f7065726174696f6e732e70726f746f1a19676f6f676c65" .
            "2f70726f746f6275662f616e792e70726f746f1a20676f6f676c652f7072" .
            "6f746f6275662f6669656c645f6d61736b2e70726f746f1a1f676f6f676c" .
            "652f70726f746f6275662f74696d657374616d702e70726f746f1a17676f" .
            "6f676c652f7270632f7374617475732e70726f746f22430a0e4d616e6167" .
            "65645365727669636512140a0c736572766963655f6e616d651802200128" .
            "09121b0a1370726f64756365725f70726f6a6563745f6964180320012809" .
            "2291030a114f7065726174696f6e4d6574616461746112160a0e7265736f" .
            "757263655f6e616d657318012003280912460a0573746570731802200328" .
            "0b32372e676f6f676c652e6170692e736572766963656d616e6167656d65" .
            "6e742e76312e4f7065726174696f6e4d657461646174612e53746570121b" .
            "0a1370726f67726573735f70657263656e74616765180320012805122e0a" .
            "0a73746172745f74696d6518042001280b321a2e676f6f676c652e70726f" .
            "746f6275662e54696d657374616d701a660a045374657012130a0b646573" .
            "6372697074696f6e18022001280912490a0673746174757318042001280e" .
            "32392e676f6f676c652e6170692e736572766963656d616e6167656d656e" .
            "742e76312e4f7065726174696f6e4d657461646174612e53746174757322" .
            "670a0653746174757312160a125354415455535f554e5350454349464945" .
            "44100012080a04444f4e451001120f0a0b4e4f545f535441525445441002" .
            "120f0a0b494e5f50524f47524553531003120a0a064641494c4544100412" .
            "0d0a0943414e43454c4c45441005228f010a0a446961676e6f7374696312" .
            "100a086c6f636174696f6e180120012809123e0a046b696e641802200128" .
            "0e32302e676f6f676c652e6170692e736572766963656d616e6167656d65" .
            "6e742e76312e446961676e6f737469632e4b696e64120f0a076d65737361" .
            "6765180320012809221e0a044b696e64120b0a075741524e494e47100012" .
            "090a054552524f52100122560a0c436f6e666967536f75726365120a0a02" .
            "6964180520012809123a0a0566696c657318022003280b322b2e676f6f67" .
            "6c652e6170692e736572766963656d616e6167656d656e742e76312e436f" .
            "6e66696746696c652295020a0a436f6e66696746696c6512110a0966696c" .
            "655f7061746818012001280912150a0d66696c655f636f6e74656e747318" .
            "032001280c12470a0966696c655f7479706518042001280e32342e676f6f" .
            "676c652e6170692e736572766963656d616e6167656d656e742e76312e43" .
            "6f6e66696746696c652e46696c65547970652293010a0846696c65547970" .
            "6512190a1546494c455f545950455f554e53504543494649454410001217" .
            "0a13534552564943455f434f4e4649475f59414d4c100112110a0d4f5045" .
            "4e5f4150495f4a534f4e100212110a0d4f50454e5f4150495f59414d4c10" .
            "03121d0a1946494c455f44455343524950544f525f5345545f50524f544f" .
            "1004120e0a0a50524f544f5f46494c45100622190a09436f6e6669675265" .
            "66120c0a046e616d6518012001280922400a0c4368616e67655265706f72" .
            "7412300a0e636f6e6669675f6368616e67657318012003280b32182e676f" .
            "6f676c652e6170692e436f6e6669674368616e676522f9050a07526f6c6c" .
            "6f757412170a0a726f6c6c6f75745f69641801200128094203e04101122f" .
            "0a0b6372656174655f74696d6518022001280b321a2e676f6f676c652e70" .
            "726f746f6275662e54696d657374616d7012120a0a637265617465645f62" .
            "7918032001280912460a0673746174757318042001280e32362e676f6f67" .
            "6c652e6170692e736572766963656d616e6167656d656e742e76312e526f" .
            "6c6c6f75742e526f6c6c6f757453746174757312630a1874726166666963" .
            "5f70657263656e745f737472617465677918052001280b323f2e676f6f67" .
            "6c652e6170692e736572766963656d616e6167656d656e742e76312e526f" .
            "6c6c6f75742e5472616666696350657263656e7453747261746567794800" .
            "12620a1764656c6574655f736572766963655f737472617465677918c801" .
            "2001280b323e2e676f6f676c652e6170692e736572766963656d616e6167" .
            "656d656e742e76312e526f6c6c6f75742e44656c65746553657276696365" .
            "5374726174656779480012140a0c736572766963655f6e616d6518082001" .
            "28091ab3010a165472616666696350657263656e74537472617465677912" .
            "650a0b70657263656e746167657318012003280b32502e676f6f676c652e" .
            "6170692e736572766963656d616e6167656d656e742e76312e526f6c6c6f" .
            "75742e5472616666696350657263656e7453747261746567792e50657263" .
            "656e7461676573456e7472791a320a1050657263656e7461676573456e74" .
            "7279120b0a036b6579180120012809120d0a0576616c7565180220012801" .
            "3a0238011a170a1544656c65746553657276696365537472617465677922" .
            "8d010a0d526f6c6c6f7574537461747573121e0a1a524f4c4c4f55545f53" .
            "54415455535f554e5350454349464945441000120f0a0b494e5f50524f47" .
            "524553531001120b0a07535543434553531002120d0a0943414e43454c4c" .
            "45441003120a0a064641494c45441004120b0a0750454e44494e47100512" .
            "160a124641494c45445f524f4c4c45445f4241434b1006420a0a08737472" .
            "617465677942d8010a23636f6d2e676f6f676c652e6170692e7365727669" .
            "63656d616e6167656d656e742e7631420e5265736f757263657350726f74" .
            "6f50015a50676f6f676c652e676f6c616e672e6f72672f67656e70726f74" .
            "6f2f676f6f676c65617069732f6170692f736572766963656d616e616765" .
            "6d656e742f76313b736572766963656d616e6167656d656e74a202044741" .
            "534daa0221476f6f676c652e436c6f75642e536572766963654d616e6167" .
            "656d656e742e5631ca0221476f6f676c655c436c6f75645c536572766963" .
            "654d616e6167656d656e745c5631620670726f746f33"
        ), true);

        static::$is_initialized = true;
    }
}

