{{ header }}
<div id="account-order" class="container">
  <div class="row">{{ column_left }}
    <div id="content" class="col" style="padding-left:10px;padding-right:10px;padding-top:10px">{{ content_top }}
      <div class="row row-cols-md-2">
        <div class="col">
          <table class="table table-bordered table-hover">
            <tr>
              <td><strong>{{ text_order_id }}</strong></td>
              <td>#{{ order_id }}</td>
            </tr>
            <tr>
              <td><strong>{{ text_order_status }}</strong></td>
              <td>{{ order_status }}</td>
            </tr>
          </table>
        </div>
        <div class="col">
          <table class="table table-bordered table-hover">
            <tr>
              <td><strong>{{ text_payment_method }}</strong></td>
              <td>{{ payment_method }}</td>
            </tr>
            <tr>
              <td><strong>{{ text_date_added }}</strong></td>
              <td>{{ date_added }}</td>
            </tr>
          </table>
        </div>
      </div>
      {% if payment_address or shipping_address %}
        <table class="table table-bordered table-hover">
          <thead>
            <tr>
              {% if payment_address %}
                <td class="text-start align-top"><strong>{{ text_payment_address }}</strong></td>
              {% endif %}
              {% if shipping_address %}
                <td class="text-start align-top"><strong>{{ text_shipping_address }}</strong></td>
              {% endif %}
            </tr>
          </thead>
          <tbody>
            <tr>
              {% if payment_address %}
                <td class="text-start align-top">{{ payment_address }}</td>
              {% endif %}
              {% if shipping_address %}
                <td class="text-start align-top">{{ shipping_address }}</td>
              {% endif %}</tr>
          </tbody>
        </table>
      {% endif %}
      <div class="table-responsive">
        <table class="table table-bordered table-hover">
          <thead>
            <tr>
              <td class="text-start"><strong>{{ column_name }}</strong></td>
              <td class="text-end"><strong>{{ column_quantity }}</strong></td>
              <td class="text-end"><strong>{{ column_price }}</strong></td>
              <td class="text-end"><strong>{{ column_total }}</strong></td>
              {% if products %}
                <td class="text-end"><strong>{{ column_action }}</strong></td>
              {% endif %}
            </tr>
          </thead>
          <tbody>
            {% for product in products %}
              <tr>
                <td class="text-start"><a href="{{ product.href }}">{{ product.name }}</a>
                  {% for option in product.option %}
                    <br/>
                    <small> - {{ option.name }}: {{ option.value }}</small>
                  {% endfor %}
                  {% if product.reward %}
                    <br/>
                    <small> - {{ text_points }}: {{ product.reward }}</small>
                  {% endif %}
                  {% if product.subscription %}
                    <br/>
                    <small> - {{ text_subscription }}: <a href="{{ product.subscription }}" target="_blank">{{ product.subscription_description }}</a></small>
                  {% endif %}
                </td>
                <td class="text-end">{{ product.quantity }}</td>
                <td class="text-end">{{ product.price }}</td>
                <td class="text-end">{{ product.total }}</td>
                <td class="text-end text-nowrap">{% if product.reorder %}<a href="{{ product.reorder }}" data-bs-toggle="tooltip" title="{{ button_reorder }}" class="btn btn-primary"><i class="fa-solid fa-cart-shopping"></i></a>{% endif %}
                  <a href="{{ product.return }}" data-bs-toggle="tooltip" title="{{ button_return }}" class="btn btn-danger"><i class="fa-solid fa-reply"></i></a></td>
              </tr>
            {% endfor %}

            {% for voucher in vouchers %}
              <tr>
                <td class="text-start">{{ voucher.description }}</td>
                <td class="text-start"></td>
                <td class="text-end">1</td>
                <td class="text-end">{{ voucher.amount }}</td>
                <td class="text-end">{{ voucher.amount }}</td>
                {% if products %}
                  <td></td>
                {% endif %}
              </tr>
            {% endfor %}
          </tbody>
          <tfoot>
            {% for total in totals %}
              <tr>
                <td colspan="3"></td>
                <td class="text-end"><b>{{ total.title }}</b></td>
                <td class="text-end">{{ total.text }}</td>
                {% if products %}
                  <td></td>
                {% endif %}
              </tr>
            {% endfor %}
          </tfoot>
        </table>
      </div>
      {% if comment %}
        <table class="table table-bordered table-hover">
          <thead>
            <tr>
              <td class="text-start"><strong>{{ text_comment }}</strong></td>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td class="text-start">{{ comment }}</td>
            </tr>
          </tbody>
        </table>
      {% endif %}
      <h2>{{ text_history }}</h2>
      <div id="history">{{ history }}</div>
      <div class="text-end mt-3"><a href="{{ continue }}" class="btn btn-primary">{{ button_continue }}</a></div>
      {{ content_bottom }}</div>
    {{ column_right }}</div>
</div>
<script type="text/javascript"><!--
$('#history').on('click', '.pagination a', function(e) {
    e.preventDefault();

    $('#history').load(this.href);
});
//--></script>
{{ footer }}