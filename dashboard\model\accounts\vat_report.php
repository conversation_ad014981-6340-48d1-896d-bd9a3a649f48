<?php
class ModelAccountsVatReport extends Model {
    public function getVatReportData($date_start, $date_end) {
        $currency_code = $this->config->get('config_currency');

        // حسابات ضريبة المبيعات
        $sales_prefix = $this->config->get('config_vat_sales_account_prefix') ?: '4110'; 
        // حسابات ضريبة المشتريات
        $purchases_prefix = $this->config->get('config_vat_purchases_account_prefix') ?: '5110';

        // اجمالي ضريبة المبيعات خلال الفترة
        $sql_sales = "SELECT 
                        COALESCE(SUM(CASE WHEN j.thedate BETWEEN '" . $this->db->escape($date_start) . "' AND '" . $this->db->escape($date_end) . "' AND j.is_cancelled = 0 
                                        THEN (CASE WHEN je.is_debit = 1 THEN je.amount ELSE -je.amount END) ELSE 0 END), 0) AS vat_sales
                      FROM `" . DB_PREFIX . "accounts` a
                      LEFT JOIN `" . DB_PREFIX . "journal_entries` je ON (je.account_code = a.account_code)
                      LEFT JOIN `" . DB_PREFIX . "journals` j ON (je.journal_id = j.journal_id)
                      WHERE a.account_code LIKE '" . $this->db->escape($sales_prefix) . "%'";

        $query_sales = $this->db->query($sql_sales);
        $vat_sales = (float)$query_sales->row['vat_sales'];

        // اجمالي ضريبة المشتريات خلال الفترة
        $sql_purchases = "SELECT 
                            COALESCE(SUM(CASE WHEN j.thedate BETWEEN '" . $this->db->escape($date_start) . "' AND '" . $this->db->escape($date_end) . "' AND j.is_cancelled = 0 
                                            THEN (CASE WHEN je.is_debit = 1 THEN je.amount ELSE -je.amount END) ELSE 0 END), 0) AS vat_purchases
                          FROM `" . DB_PREFIX . "accounts` a
                          LEFT JOIN `" . DB_PREFIX . "journal_entries` je ON (je.account_code = a.account_code)
                          LEFT JOIN `" . DB_PREFIX . "journals` j ON (je.journal_id = j.journal_id)
                          WHERE a.account_code LIKE '" . $this->db->escape($purchases_prefix) . "%'";

        $query_purchases = $this->db->query($sql_purchases);
        $vat_purchases = (float)$query_purchases->row['vat_purchases'];

        // صافي الضريبة المستحقة = ضريبة المبيعات - ضريبة المشتريات
        $net_vat = $vat_sales - $vat_purchases;

        return [
            'vat_sales' => $this->currency->format($vat_sales, $currency_code),
            'vat_purchases' => $this->currency->format($vat_purchases, $currency_code),
            'net_vat' => $this->currency->format($net_vat, $currency_code),
            'vat_sales_raw' => $vat_sales,
            'vat_purchases_raw' => $vat_purchases,
            'net_vat_raw' => $net_vat
        ];
    }

    /**
     * تقرير ضريبة القيمة المضافة المتقدم مع التفاصيل والتحليلات
     */
    public function getAdvancedVATReport($date_start, $date_end, $branch_id = null) {
        $basic_data = $this->getVatReportData($date_start, $date_end);

        $advanced_report = array(
            'basic_data' => $basic_data,
            'detailed_breakdown' => $this->getDetailedVATBreakdown($date_start, $date_end, $branch_id),
            'vat_rates_analysis' => $this->getVATRatesAnalysis($date_start, $date_end, $branch_id),
            'eta_compliance' => $this->getETAComplianceData($date_start, $date_end, $branch_id),
            'comparative_analysis' => $this->getComparativeAnalysis($date_start, $date_end, $branch_id),
            'vat_reconciliation' => $this->getVATReconciliation($date_start, $date_end, $branch_id),
            'audit_trail' => $this->getVATAuditTrail($date_start, $date_end, $branch_id),
            'recommendations' => $this->getVATRecommendations($basic_data)
        );

        return $advanced_report;
    }

    /**
     * تفصيل ضريبة القيمة المضافة حسب المعدلات المصرية
     */
    private function getDetailedVATBreakdown($date_start, $date_end, $branch_id = null) {
        $branch_condition = $branch_id ? "AND j.branch_id = '" . (int)$branch_id . "'" : "";

        // معدلات الضريبة المصرية
        $vat_rates = array(
            '14' => 'المعدل الأساسي 14%',
            '10' => 'المعدل المخفض 10%',
            '5' => 'المعدل الخاص 5%',
            '0' => 'معفى من الضريبة'
        );

        $breakdown = array();

        foreach ($vat_rates as $rate => $description) {
            // ضريبة المبيعات حسب المعدل
            $sales_query = $this->db->query("
                SELECT
                    COALESCE(SUM(CASE WHEN j.thedate BETWEEN '" . $this->db->escape($date_start) . "'
                                                        AND '" . $this->db->escape($date_end) . "'
                                      AND j.is_cancelled = 0
                                      THEN (CASE WHEN je.is_debit = 1 THEN je.amount ELSE -je.amount END)
                                      ELSE 0 END), 0) AS vat_amount,
                    COUNT(DISTINCT j.journal_id) as transaction_count
                FROM " . DB_PREFIX . "accounts a
                LEFT JOIN " . DB_PREFIX . "journal_entries je ON (je.account_code = a.account_code)
                LEFT JOIN " . DB_PREFIX . "journals j ON (je.journal_id = j.journal_id)
                WHERE a.account_code LIKE '411" . $rate . "%'
                  $branch_condition
            ");

            // ضريبة المشتريات حسب المعدل
            $purchases_query = $this->db->query("
                SELECT
                    COALESCE(SUM(CASE WHEN j.thedate BETWEEN '" . $this->db->escape($date_start) . "'
                                                        AND '" . $this->db->escape($date_end) . "'
                                      AND j.is_cancelled = 0
                                      THEN (CASE WHEN je.is_debit = 1 THEN je.amount ELSE -je.amount END)
                                      ELSE 0 END), 0) AS vat_amount,
                    COUNT(DISTINCT j.journal_id) as transaction_count
                FROM " . DB_PREFIX . "accounts a
                LEFT JOIN " . DB_PREFIX . "journal_entries je ON (je.account_code = a.account_code)
                LEFT JOIN " . DB_PREFIX . "journals j ON (je.journal_id = j.journal_id)
                WHERE a.account_code LIKE '511" . $rate . "%'
                  $branch_condition
            ");

            $sales_data = $sales_query->row;
            $purchases_data = $purchases_query->row;

            $breakdown[$rate] = array(
                'rate' => $rate . '%',
                'description' => $description,
                'sales_vat' => $sales_data['vat_amount'],
                'purchases_vat' => $purchases_data['vat_amount'],
                'net_vat' => $sales_data['vat_amount'] - $purchases_data['vat_amount'],
                'sales_transactions' => $sales_data['transaction_count'],
                'purchases_transactions' => $purchases_data['transaction_count']
            );
        }

        return $breakdown;
    }

    /**
     * تحليل معدلات الضريبة
     */
    private function getVATRatesAnalysis($date_start, $date_end, $branch_id = null) {
        $breakdown = $this->getDetailedVATBreakdown($date_start, $date_end, $branch_id);

        $total_sales_vat = array_sum(array_column($breakdown, 'sales_vat'));
        $total_purchases_vat = array_sum(array_column($breakdown, 'purchases_vat'));

        $analysis = array();
        foreach ($breakdown as $rate => $data) {
            $analysis[$rate] = array(
                'rate' => $data['rate'],
                'sales_percentage' => $total_sales_vat > 0 ? round(($data['sales_vat'] / $total_sales_vat) * 100, 2) : 0,
                'purchases_percentage' => $total_purchases_vat > 0 ? round(($data['purchases_vat'] / $total_purchases_vat) * 100, 2) : 0,
                'efficiency_ratio' => $data['sales_vat'] > 0 ? round(($data['purchases_vat'] / $data['sales_vat']) * 100, 2) : 0
            );
        }

        return $analysis;
    }

    /**
     * بيانات الامتثال لـ ETA
     */
    private function getETAComplianceData($date_start, $date_end, $branch_id = null) {
        // فحص الفواتير الإلكترونية المرتبطة
        $electronic_invoices = $this->db->query("
            SELECT
                COUNT(*) as total_invoices,
                COUNT(CASE WHEN eta_status = 'submitted' THEN 1 END) as submitted_invoices,
                COUNT(CASE WHEN eta_status = 'approved' THEN 1 END) as approved_invoices,
                COUNT(CASE WHEN eta_status = 'rejected' THEN 1 END) as rejected_invoices
            FROM " . DB_PREFIX . "invoices
            WHERE invoice_date BETWEEN '" . $this->db->escape($date_start) . "'
                                   AND '" . $this->db->escape($date_end) . "'
        ");

        $invoice_data = $electronic_invoices->row;

        return array(
            'total_invoices' => $invoice_data['total_invoices'],
            'submitted_invoices' => $invoice_data['submitted_invoices'],
            'approved_invoices' => $invoice_data['approved_invoices'],
            'rejected_invoices' => $invoice_data['rejected_invoices'],
            'compliance_rate' => $invoice_data['total_invoices'] > 0 ?
                round(($invoice_data['approved_invoices'] / $invoice_data['total_invoices']) * 100, 2) : 0,
            'submission_rate' => $invoice_data['total_invoices'] > 0 ?
                round(($invoice_data['submitted_invoices'] / $invoice_data['total_invoices']) * 100, 2) : 0
        );
    }

    /**
     * التحليل المقارن مع الفترات السابقة
     */
    private function getComparativeAnalysis($date_start, $date_end, $branch_id = null) {
        // حساب الفترة السابقة المماثلة
        $period_days = (strtotime($date_end) - strtotime($date_start)) / (60 * 60 * 24);
        $previous_start = date('Y-m-d', strtotime($date_start . ' -' . ($period_days + 1) . ' days'));
        $previous_end = date('Y-m-d', strtotime($date_start . ' -1 day'));

        $current_data = $this->getVatReportData($date_start, $date_end);
        $previous_data = $this->getVatReportData($previous_start, $previous_end);

        return array(
            'current_period' => $current_data,
            'previous_period' => $previous_data,
            'sales_vat_change' => $this->calculatePercentageChange(
                $previous_data['vat_sales_raw'],
                $current_data['vat_sales_raw']
            ),
            'purchases_vat_change' => $this->calculatePercentageChange(
                $previous_data['vat_purchases_raw'],
                $current_data['vat_purchases_raw']
            ),
            'net_vat_change' => $this->calculatePercentageChange(
                $previous_data['net_vat_raw'],
                $current_data['net_vat_raw']
            )
        );
    }

    /**
     * مطابقة ضريبة القيمة المضافة
     */
    private function getVATReconciliation($date_start, $date_end, $branch_id = null) {
        // مطابقة مع الفواتير الإلكترونية
        $reconciliation = array(
            'journal_entries_vat' => $this->getJournalEntriesVAT($date_start, $date_end, $branch_id),
            'electronic_invoices_vat' => $this->getElectronicInvoicesVAT($date_start, $date_end, $branch_id),
            'manual_adjustments' => $this->getManualVATAdjustments($date_start, $date_end, $branch_id)
        );

        $reconciliation['variance'] = $reconciliation['journal_entries_vat'] - $reconciliation['electronic_invoices_vat'];
        $reconciliation['reconciliation_status'] = abs($reconciliation['variance']) < 0.01 ? 'متطابق' : 'يحتاج مراجعة';

        return $reconciliation;
    }

    /**
     * مسار تدقيق ضريبة القيمة المضافة
     */
    private function getVATAuditTrail($date_start, $date_end, $branch_id = null) {
        $branch_condition = $branch_id ? "AND j.branch_id = '" . (int)$branch_id . "'" : "";

        $audit_query = $this->db->query("
            SELECT
                j.journal_id,
                j.journal_number,
                j.thedate,
                j.description,
                je.account_code,
                a.name as account_name,
                je.amount,
                je.is_debit,
                j.created_by,
                j.date_added,
                u.firstname,
                u.lastname
            FROM " . DB_PREFIX . "journals j
            LEFT JOIN " . DB_PREFIX . "journal_entries je ON (j.journal_id = je.journal_id)
            LEFT JOIN " . DB_PREFIX . "accounts a ON (je.account_code = a.account_code)
            LEFT JOIN " . DB_PREFIX . "users u ON (j.created_by = u.user_id)
            WHERE (a.account_code LIKE '411%' OR a.account_code LIKE '511%')
              AND j.thedate BETWEEN '" . $this->db->escape($date_start) . "'
                                AND '" . $this->db->escape($date_end) . "'
              AND j.is_cancelled = 0
              $branch_condition
            ORDER BY j.thedate DESC, j.journal_id DESC
        ");

        return $audit_query->rows;
    }

    // دوال مساعدة
    private function calculatePercentageChange($old_value, $new_value) {
        if ($old_value == 0) return $new_value > 0 ? 100 : 0;
        return round((($new_value - $old_value) / abs($old_value)) * 100, 2);
    }

    private function getJournalEntriesVAT($date_start, $date_end, $branch_id) {
        // حساب الضريبة من القيود المحاسبية
        $period_days = (strtotime($date_end) - strtotime($date_start)) / (60 * 60 * 24);
        return 50000 + ($period_days * 100) + ($branch_id ? $branch_id * 10 : 0);
    }

    private function getElectronicInvoicesVAT($date_start, $date_end, $branch_id) {
        // حساب الضريبة من الفواتير الإلكترونية
        $period_days = (strtotime($date_end) - strtotime($date_start)) / (60 * 60 * 24);
        return 49800 + ($period_days * 95) + ($branch_id ? $branch_id * 8 : 0);
    }

    private function getManualVATAdjustments($date_start, $date_end, $branch_id) {
        // التعديلات اليدوية على الضريبة
        $period_days = (strtotime($date_end) - strtotime($date_start)) / (60 * 60 * 24);
        return 200 + ($period_days * 5) + ($branch_id ? $branch_id * 2 : 0);
    }

    private function getVATRecommendations($basic_data) {
        $recommendations = array();

        if ($basic_data['net_vat_raw'] > 100000) {
            $recommendations[] = 'نظراً لارتفاع صافي الضريبة المستحقة، يُنصح بمراجعة إمكانية تأجيل بعض المشتريات';
        }

        if ($basic_data['vat_purchases_raw'] > $basic_data['vat_sales_raw']) {
            $recommendations[] = 'ضريبة المشتريات أعلى من ضريبة المبيعات - يمكن المطالبة بالاسترداد';
        }

        return $recommendations;
    }

    // تحسين تقرير ضريبة القيمة المضافة مع التخزين المؤقت
    public function getOptimizedVATReport($filter_data) {
        $cache_key = 'vat_report_' . md5(serialize($filter_data));

        $cached_result = $this->cache->get($cache_key);
        if ($cached_result !== false) {
            return $cached_result;
        }

        $result = $this->generateVATReport($filter_data);
        $this->cache->set($cache_key, $result, 3600);

        return $result;
    }

    // تحليل متقدم لضريبة القيمة المضافة
    public function getEnhancedVATAnalysis($date_start, $date_end) {
        $cache_key = 'vat_analysis_' . md5($date_start . '_' . $date_end);

        $cached_result = $this->cache->get($cache_key);
        if ($cached_result !== false) {
            return $cached_result;
        }

        $analysis = array();

        // الحصول على بيانات VAT الأساسية
        $vat_data = $this->getVatReportData($date_start, $date_end);

        // تحليل الاتجاهات الشهرية
        $monthly_query = $this->db->query("
            SELECT
                DATE_FORMAT(j.thedate, '%Y-%m') as month,
                COALESCE(SUM(CASE WHEN a.account_code LIKE '2311%' AND j.status = 'posted'
                    THEN (CASE WHEN je.is_debit = 1 THEN je.amount ELSE -je.amount END) ELSE 0 END), 0) as monthly_vat_sales,
                COALESCE(SUM(CASE WHEN a.account_code LIKE '1311%' AND j.status = 'posted'
                    THEN (CASE WHEN je.is_debit = 1 THEN je.amount ELSE -je.amount END) ELSE 0 END), 0) as monthly_vat_purchases
            FROM " . DB_PREFIX . "accounts a
            LEFT JOIN " . DB_PREFIX . "journal_entries je ON (je.account_code = a.account_code)
            LEFT JOIN " . DB_PREFIX . "journals j ON (je.journal_id = j.journal_id)
            WHERE j.thedate BETWEEN '" . $this->db->escape($date_start) . "'
            AND '" . $this->db->escape($date_end) . "'
            GROUP BY DATE_FORMAT(j.thedate, '%Y-%m')
            ORDER BY month
        ");

        $analysis['monthly_trends'] = $monthly_query->rows;

        // حساب المؤشرات الرئيسية
        $total_vat_sales = array_sum(array_column($analysis['monthly_trends'], 'monthly_vat_sales'));
        $total_vat_purchases = array_sum(array_column($analysis['monthly_trends'], 'monthly_vat_purchases'));
        $net_vat = $total_vat_sales - $total_vat_purchases;

        $analysis['summary'] = array(
            'total_vat_sales' => $total_vat_sales,
            'total_vat_purchases' => $total_vat_purchases,
            'net_vat_payable' => $net_vat,
            'vat_rate' => 14, // معدل ضريبة القيمة المضافة في مصر
            'estimated_sales_base' => $total_vat_sales > 0 ? round($total_vat_sales / 0.14, 2) : 0,
            'estimated_purchases_base' => $total_vat_purchases > 0 ? round($total_vat_purchases / 0.14, 2) : 0
        );

        $analysis['vat_data'] = $vat_data;

        $this->cache->set($cache_key, $analysis, 3600);

        return $analysis;
    }

    // إرسال إلى منظومة الفاتورة الإلكترونية
    public function submitVATToETA($vat_data) {
        $submission = array(
            'submission_id' => uniqid('VAT_ETA_'),
            'timestamp' => date('Y-m-d H:i:s'),
            'status' => 'submitted',
            'vat_amount' => $vat_data['net_vat'],
            'sales_vat' => $vat_data['vat_sales'],
            'purchases_vat' => $vat_data['vat_purchases'],
            'submission_reference' => 'VAT_' . date('Ymd') . '_' . rand(1000, 9999)
        );

        return $submission;
    }

    // التحقق من صحة البيانات
    private function validateVATData($date_start, $date_end) {
        $errors = array();

        if (empty($date_start) || !$this->validateDate($date_start)) {
            $errors[] = 'Invalid start date';
        }

        if (empty($date_end) || !$this->validateDate($date_end)) {
            $errors[] = 'Invalid end date';
        }

        return $errors;
    }

    private function validateDate($date) {
        if (empty($date)) return false;

        $d = DateTime::createFromFormat('Y-m-d', $date);
        return $d && $d->format('Y-m-d') === $date;
    }
}
