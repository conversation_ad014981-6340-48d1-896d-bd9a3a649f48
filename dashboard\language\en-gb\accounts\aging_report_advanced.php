<?php
// Heading
$_['heading_title']                = 'Advanced Aging Report';

// Text
$_['text_list']                    = 'Advanced Aging Reports List';
$_['text_form']                    = 'Advanced Aging Report Form';
$_['text_view']                    = 'View Advanced Aging Report';
$_['text_generate']                = 'Generate Advanced Aging Report';
$_['text_success_generate']        = 'Advanced aging report generated successfully!';
$_['text_loading']                 = 'Loading...';
$_['text_processing']              = 'Processing...';
$_['text_no_results']              = 'No results found';
$_['text_home']                    = 'Home';

// Report Types
$_['text_receivables']             = 'Receivables (Customers)';
$_['text_payables']                = 'Payables (Suppliers)';
$_['text_both']                    = 'Both';

// Aging Periods
$_['text_standard_periods']        = 'Standard Periods';
$_['text_custom_periods']          = 'Custom Periods';
$_['text_period_current']          = 'Current';
$_['text_period_1_30']             = '1-30 Days';
$_['text_period_31_60']            = '31-60 Days';
$_['text_period_61_90']            = '61-90 Days';
$_['text_period_over_90']          = 'Over 90 Days';

// Advanced Analysis
$_['text_risk_analysis']           = 'Risk Analysis';
$_['text_advanced_analysis']       = 'Advanced Analysis';
$_['text_ai_analysis']             = 'AI Analysis';
$_['text_predictive_analysis']     = 'Predictive Analysis';
$_['text_trend_analysis']          = 'Trend Analysis';
$_['text_collection_analysis']     = 'Collection Analysis';
$_['text_credit_analysis']         = 'Credit Analysis';

// Risk Levels
$_['text_risk_low']                = 'Low Risk';
$_['text_risk_medium']             = 'Medium Risk';
$_['text_risk_high']               = 'High Risk';
$_['text_risk_critical']           = 'Critical Risk';
$_['text_risk_score']              = 'Risk Score';
$_['text_overall_risk']            = 'Overall Risk';

// Collection Rates
$_['text_collection_rate']         = 'Collection Rate';
$_['text_expected_collection']     = 'Expected Collection';
$_['text_collection_probability']  = 'Collection Probability';
$_['text_collection_forecast']     = 'Collection Forecast';

// Customer/Supplier Information
$_['text_customer_info']           = 'Customer Information';
$_['text_supplier_info']           = 'Supplier Information';
$_['text_entity_name']             = 'Entity Name';
$_['text_entity_code']             = 'Entity Code';
$_['text_credit_limit']            = 'Credit Limit';
$_['text_payment_terms']           = 'Payment Terms';
$_['text_last_payment']            = 'Last Payment';
$_['text_contact_info']            = 'Contact Information';

// Financial Information
$_['text_total_balance']           = 'Total Balance';
$_['text_overdue_amount']          = 'Overdue Amount';
$_['text_current_amount']          = 'Current Amount';
$_['text_percentage_overdue']      = 'Percentage Overdue';
$_['text_days_outstanding']        = 'Days Outstanding';
$_['text_average_days']            = 'Average Days';

// Summary Information
$_['text_summary']                 = 'Summary';
$_['text_detailed_summary']        = 'Detailed Summary';
$_['text_total_entities']          = 'Total Entities';
$_['text_total_amount']            = 'Total Amount';
$_['text_total_overdue']           = 'Total Overdue';
$_['text_percentage_distribution'] = 'Percentage Distribution';

// Charts and Visualization
$_['text_charts']                  = 'Charts';
$_['text_aging_chart']             = 'Aging Chart';
$_['text_risk_chart']              = 'Risk Chart';
$_['text_trend_chart']             = 'Trend Chart';
$_['text_distribution_chart']      = 'Distribution Chart';
$_['text_collection_chart']        = 'Collection Chart';

// Export Options
$_['text_export_options']          = 'Export Options';
$_['text_export_excel']            = 'Export to Excel';
$_['text_export_pdf']              = 'Export to PDF';
$_['text_export_csv']              = 'Export to CSV';
$_['text_export_xml']              = 'Export to XML';
$_['text_include_charts']          = 'Include Charts';
$_['text_include_analysis']        = 'Include Analysis';

// Print Options
$_['text_print_options']           = 'Print Options';
$_['text_print_preview']           = 'Print Preview';
$_['text_print_detailed']          = 'Print Detailed';
$_['text_print_summary']           = 'Print Summary';

// Entry Fields
$_['entry_date_end']               = 'Report Date';
$_['entry_report_type']            = 'Report Type';
$_['entry_aging_periods']          = 'Aging Periods';
$_['entry_currency']               = 'Currency';
$_['entry_customer']               = 'Customer';
$_['entry_supplier']               = 'Supplier';
$_['entry_include_zero_balances']  = 'Include Zero Balances';
$_['entry_sort_by']                = 'Sort By';
$_['entry_group_by']               = 'Group By';
$_['entry_filter_amount']          = 'Filter Amount';
$_['entry_risk_threshold']         = 'Risk Threshold';

// Column Headers
$_['column_entity']                = 'Entity';
$_['column_current']               = 'Current';
$_['column_1_30']                  = '1-30 Days';
$_['column_31_60']                 = '31-60 Days';
$_['column_61_90']                 = '61-90 Days';
$_['column_over_90']               = '90+ Days';
$_['column_total']                 = 'Total';
$_['column_risk_score']            = 'Risk Score';
$_['column_collection_rate']       = 'Collection Rate';
$_['column_last_payment']          = 'Last Payment';
$_['column_contact']               = 'Contact';

// Buttons
$_['button_generate']              = 'Generate';
$_['button_view']                  = 'View';
$_['button_export']                = 'Export';
$_['button_print']                 = 'Print';
$_['button_analyze']               = 'Analyze';
$_['button_forecast']              = 'Forecast';
$_['button_clear']                 = 'Clear';
$_['button_reset']                 = 'Reset';
$_['button_refresh']               = 'Refresh';
$_['button_save']                  = 'Save';
$_['button_cancel']                = 'Cancel';

// Error Messages
$_['error_permission']             = 'Warning: You do not have permission to access advanced aging report!';
$_['error_date_end']               = 'Report date is required!';
$_['error_report_type']            = 'Report type is required!';
$_['error_currency']               = 'Currency is required!';
$_['error_no_data']                = 'No data to display!';
$_['error_generation_failed']      = 'Report generation failed!';
$_['error_export_failed']          = 'Report export failed!';
$_['error_print_failed']           = 'Report printing failed!';
$_['error_analysis_failed']        = 'Data analysis failed!';

// Success Messages
$_['success_generated']            = 'Report generated successfully!';
$_['success_exported']             = 'Report exported successfully!';
$_['success_printed']              = 'Report sent to printer successfully!';
$_['success_analyzed']             = 'Data analyzed successfully!';

// Help Text
$_['help_aging_report_advanced']   = 'Advanced aging report provides comprehensive analysis of receivables and payables with risk assessment and forecasting.';
$_['help_risk_analysis']           = 'Risk analysis helps identify customers or suppliers who may face payment difficulties.';

// Missing variables from audit report - Critical fixes
$_['accounts/aging_report_advanced']   = '';
$_['code']                             = 'Code';
$_['date_format_short']                = 'm/d/Y';
$_['direction']                        = 'ltr';
$_['lang']                             = 'en';

// Controller language variables
$_['log_unauthorized_access_aging_report_advanced'] = 'Unauthorized access attempt to advanced aging report';
$_['log_view_aging_report_advanced_screen'] = 'View advanced aging report screen';
$_['log_unauthorized_generate_aging_report_advanced'] = 'Unauthorized advanced aging report generation attempt';
$_['log_generate_aging_report_advanced_date'] = 'Generate advanced aging report for date';
$_['text_critical_risk_entities_alert'] = 'Critical Warning: High Risk Entities';
$_['text_critical_risk_entities_detected'] = 'Detected';
$_['text_critical_risk_entity_in_advanced_report'] = 'high risk entity in advanced report';
$_['text_aging_report_advanced_generated'] = 'Advanced Aging Report Generated';
$_['text_aging_report_advanced_generated_notification'] = 'Advanced aging report generated for date';
$_['log_view_aging_report_advanced']   = 'View advanced aging report';
$_['log_export_aging_report_advanced_format'] = 'Export advanced aging report in format';

// Enterprise Grade Plus template variables
$_['text_actions']                     = 'Actions';
$_['text_generate_tooltip']            = 'Generate advanced aging report for selected date';
$_['text_export_tooltip']              = 'Export advanced aging report in various formats';
$_['text_print']                       = 'Print';
$_['text_generating']                  = 'Generating...';
$_['text_exporting']                   = 'Exporting...';
$_['error_generate']                   = 'Error generating advanced aging report';
$_['text_advanced_filters']            = 'Advanced Filters';
$_['entry_date_end']                   = 'End Date';
$_['entry_report_type']                = 'Report Type';
$_['entry_risk_level']                 = 'Risk Level';
$_['entry_sort_by']                    = 'Sort By';
$_['entry_branch']                     = 'Branch';
$_['text_customers']                   = 'Customers';
$_['text_suppliers']                   = 'Suppliers';
$_['text_both']                        = 'Both';
$_['text_all_risks']                   = 'All Risks';
$_['text_critical_risk']               = 'Critical Risk';
$_['text_high_risk']                   = 'High Risk';
$_['text_medium_risk']                 = 'Medium Risk';
$_['text_low_risk']                    = 'Low Risk';
$_['text_sort_by_name']                = 'Name';
$_['text_sort_by_amount']              = 'Amount';
$_['text_sort_by_risk']                = 'Risk';
$_['text_sort_by_overdue_days']        = 'Overdue Days';
$_['text_all_branches']                = 'All Branches';
$_['button_filter']                    = 'Filter';
$_['text_as_of']                       = 'As of';
$_['text_report_type']                 = 'Report Type';
$_['text_entities']                    = 'Entities';
$_['text_total_exposure']              = 'Total Exposure';
$_['text_entity_name']                 = 'Entity Name';
$_['text_contact_info']                = 'Contact Info';
$_['text_credit_limit']                = 'Credit Limit';
$_['text_current']                     = 'Current';
$_['text_days']                        = 'Days';
$_['text_total_outstanding']           = 'Total Outstanding';
$_['text_risk_score']                  = 'Risk Score';
$_['text_trend']                       = 'Trend';
$_['text_customer']                    = 'Customer';
$_['text_supplier']                    = 'Supplier';
$_['text_view_details']                = 'View Details';
$_['text_send_statement']              = 'Send Statement';
$_['text_escalate_risk']               = 'Escalate Risk';
$_['text_critical_risk_alert']         = 'Critical Risk Alert';
$_['text_critical_entities_found']     = 'Critical entities found';
$_['text_total_critical_exposure']     = 'Total Critical Exposure';
$_['text_view_critical_details']       = 'View Critical Details';
$_['text_advanced_analytics']          = 'Advanced Analytics';
$_['text_no_data']                     = 'No data to display. Please select date and click generate.';
$_['error_date_required']              = 'End date is required';
$_['warning_future_date']              = 'Warning: Selected date is in the future';
$_['text_success_generation']          = 'Advanced aging report generated successfully';
$_['text_high_risk_entities_found']    = 'High risk entities found';
$_['text_no_critical_entities']        = 'No critical entities found';
$_['text_trend_analysis_loading']      = 'Loading trend analysis...';
$_['text_statement_sent']              = 'Statement sent successfully';
$_['text_risk_escalated']              = 'Risk escalated successfully';
$_['text_critical_risk_analysis']      = 'Critical Risk Analysis';
$_['text_trend_analysis']              = 'Trend Analysis';
$_['text_risk_distribution']           = 'Risk Distribution';
$_['text_aging_trend']                 = 'Aging Trend';
$_['text_amount']                      = 'Amount';
$_['help_collection_forecast']     = 'Collection forecasting helps in financial planning and cash flow management.';
$_['help_ai_analysis']             = 'AI analysis uses advanced algorithms to analyze patterns and predict risks.';

// Recommendations
$_['text_recommendations']         = 'Recommendations';
$_['text_action_required']         = 'Action Required';
$_['text_follow_up']               = 'Follow Up';
$_['text_credit_review']           = 'Credit Review';
$_['text_collection_action']       = 'Collection Action';
$_['text_legal_action']            = 'Legal Action';

// Status
$_['text_status_current']          = 'Current';
$_['text_status_overdue']          = 'Overdue';
$_['text_status_critical']         = 'Critical';
$_['text_status_collection']       = 'Under Collection';
$_['text_status_legal']            = 'Legal Action';

// Sorting Options
$_['text_sort_by_name']            = 'Sort by Name';
$_['text_sort_by_amount']          = 'Sort by Amount';
$_['text_sort_by_risk']            = 'Sort by Risk';
$_['text_sort_by_days']            = 'Sort by Days';
$_['text_sort_ascending']          = 'Ascending';
$_['text_sort_descending']         = 'Descending';

// Grouping Options
$_['text_group_by_type']           = 'Group by Type';
$_['text_group_by_risk']           = 'Group by Risk';
$_['text_group_by_period']         = 'Group by Period';
$_['text_group_by_currency']       = 'Group by Currency';

// Additional
$_['text_total']                   = 'Total';
$_['text_subtotal']                = 'Subtotal';
$_['text_average']                 = 'Average';
$_['text_percentage']              = 'Percentage';
$_['text_count']                   = 'Count';
$_['text_ratio']                   = 'Ratio';
$_['text_index']                   = 'Index';
$_['text_score']                   = 'Score';

// Compliance
$_['text_eas_compliant']           = 'Egyptian Accounting Standards Compliant';
$_['text_eta_ready']               = 'ETA Integration Ready';
$_['text_egyptian_gaap']           = 'Egyptian GAAP Compliant';
$_['text_risk_management']         = 'Risk Management';

// Generated Information
$_['text_generated_by']            = 'Generated By';
$_['text_generated_on']            = 'Generated On';
$_['text_report_date']             = 'Report Date';
$_['text_page']                    = 'Page';
$_['text_of']                      = 'of';
$_['text_confidential']            = 'Confidential';
$_['text_internal_use']            = 'Internal Use Only';

// AI Features
$_['text_machine_learning']        = 'Machine Learning';
$_['text_pattern_recognition']     = 'Pattern Recognition';
$_['text_anomaly_detection']       = 'Anomaly Detection';
$_['text_predictive_modeling']     = 'Predictive Modeling';
$_['text_neural_network']          = 'Neural Network';
$_['text_deep_learning']           = 'Deep Learning';

// Performance Metrics
$_['text_performance_metrics']     = 'Performance Metrics';
$_['text_collection_efficiency']   = 'Collection Efficiency';
$_['text_recovery_rate']           = 'Recovery Rate';
$_['text_bad_debt_ratio']          = 'Bad Debt Ratio';
$_['text_dso']                     = 'Days Sales Outstanding';
$_['text_turnover_ratio']          = 'Turnover Ratio';

// Alerts and Notifications
$_['text_alerts']                  = 'Alerts';
$_['text_critical_alerts']         = 'Critical Alerts';
$_['text_overdue_alerts']          = 'Overdue Alerts';
$_['text_risk_alerts']             = 'Risk Alerts';
$_['text_threshold_alerts']        = 'Threshold Alerts';

// Integration
$_['text_bank_integration']        = 'Bank Integration';
$_['text_credit_bureau']           = 'Credit Bureau';
$_['text_external_data']           = 'External Data';
$_['text_api_integration']         = 'API Integration';
?>
