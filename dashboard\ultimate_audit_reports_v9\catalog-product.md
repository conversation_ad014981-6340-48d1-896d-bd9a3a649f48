# 🏆 AYM ERP ULTIMATE AUDIT REPORT V9.0
## 📄 Route: `catalog/product`
## 🆔 Analysis ID: `23bea125`

---

### 📊 EXECUTIVE SUMMARY

| Metric | Value | Status |
|--------|-------|--------|
| **Health Score** | 💀 **18%** | SYSTEM FAILURE |
| **Critical Issues** | 🔴 3 | ❌ IMMEDIATE ACTION REQUIRED |
| **High Priority** | 🟡 1 | ✅ GOOD |
| **Medium Priority** | 🟠 0 | ✅ GOOD |
| **Analysis Date** | 📅 2025-07-24 06:50:57 | ✅ CURRENT |
| **Global Progress** | 📈 56/446 | 🚀 IN PROGRESS |

---

### 🏗️ COMPREHENSIVE MVC ARCHITECTURE ANALYSIS


#### 📂 Controller Analysis
- **File:** `controller\catalog\product.php`
- **Status:** ✅ EXISTS
- **Complexity:** 82679
- **Lines of Code:** 1893
- **Functions:** 41

#### 🧱 Models Analysis (29)
- ✅ `core/central_service_manager` (56 functions, complexity: 40995)
- ✅ `logging/user_activity` (11 functions, complexity: 8849)
- ✅ `communication/unified_notification` (16 functions, complexity: 12609)
- ❌ `communication/internal_communication` (0 functions, complexity: 0)
- ✅ `unified_document` (16 functions, complexity: 18298)
- ✅ `workflow/visual_workflow_engine` (18 functions, complexity: 18447)
- ✅ `catalog/product` (112 functions, complexity: 197928)
- ✅ `catalog/category` (14 functions, complexity: 16509)
- ✅ `catalog/manufacturer` (8 functions, complexity: 5747)
- ✅ `catalog/filter` (9 functions, complexity: 7939)
- ✅ `catalog/option` (9 functions, complexity: 10390)
- ✅ `catalog/attribute` (7 functions, complexity: 4292)
- ✅ `inventory/warehouse` (44 functions, complexity: 54045)
- ❌ `inventory/inventory_manager` (0 functions, complexity: 0)
- ❌ `catalog/pricing_manager` (0 functions, complexity: 0)
- ❌ `catalog/unit_manager` (0 functions, complexity: 0)
- ✅ `accounts/journal` (16 functions, complexity: 27549)
- ❌ `accounts/account` (0 functions, complexity: 0)
- ✅ `setting/setting` (5 functions, complexity: 5728)
- ✅ `design/layout` (7 functions, complexity: 4371)
- ✅ `localisation/stock_status` (6 functions, complexity: 3591)
- ✅ `localisation/tax_class` (7 functions, complexity: 3645)
- ✅ `localisation/weight_class` (7 functions, complexity: 4713)
- ✅ `localisation/length_class` (7 functions, complexity: 4713)
- ✅ `tool/image` (1 functions, complexity: 1658)
- ✅ `localisation/language` (6 functions, complexity: 17397)
- ❌ `tool/export` (0 functions, complexity: 0)
- ❌ `tool/import` (0 functions, complexity: 0)
- ✅ `ai/ai_assistant` (19 functions, complexity: 16118)

#### 🎨 Views Analysis (0)

#### 🌐 Language Files Analysis
- **Arabic Files:** 1/1
- **English Files:** 1/1

#### 📊 MVC Quality Metrics
- **Architecture Score:** 93%
- **Completeness Score:** 78%
- **Coupling Score:** 0%
- **Cohesion Score:** 0%


---

### 📜 CONSTITUTIONAL COMPLIANCE ANALYSIS

#### 📊 Overall Compliance
- **Compliance Score:** 85%
- **Compliance Level:** GOOD
- **Rules Passed:** 17/20
- **Critical Violations:** 2

#### 🔍 Rule-by-Rule Analysis

##### ✅ Central Services
- **Status:** COMPLIANT
- **Severity:** CRITICAL
- **Score:** 100%
- **Description:** Must call central services in every controller
- **Impact:** Loss of audit, notifications, and system integration

##### ✅ Permissions Basic
- **Status:** COMPLIANT
- **Severity:** CRITICAL
- **Score:** 100%
- **Description:** Must use basic permission system
- **Impact:** Security vulnerabilities in access control

##### ✅ Permissions Advanced
- **Status:** COMPLIANT
- **Severity:** HIGH
- **Score:** 100%
- **Description:** Must use advanced permission system
- **Impact:** Missing advanced security features

##### ✅ Language Files
- **Status:** COMPLIANT
- **Severity:** HIGH
- **Score:** 100%
- **Description:** Must have matching Arabic and English language files
- **Impact:** Broken internationalization and user experience

##### ❌ Database Prefix
- **Status:** VIOLATION
- **Severity:** CRITICAL
- **Score:** 0%
- **Description:** All tables must start with cod_ prefix
- **Impact:** Database conflicts and system instability
- **Violations:**
  - Non-compliant table: Excel
  - Non-compliant table: product
- **Recommendations:**
  - Use cod_ prefix for all custom tables

##### ❌ Mvc Structure
- **Status:** VIOLATION
- **Severity:** HIGH
- **Score:** 80%
- **Description:** Must follow complete MVC structure
- **Impact:** Broken architecture and maintainability issues
- **Violations:**
  - Missing view
- **Recommendations:**
  - Create view file

##### ✅ Config Usage
- **Status:** COMPLIANT
- **Severity:** MEDIUM
- **Score:** 100%
- **Description:** Use centralized settings instead of hardcoded values
- **Impact:** Difficult maintenance and configuration management

##### ✅ Ajax Security
- **Status:** COMPLIANT
- **Severity:** CRITICAL
- **Score:** 100%
- **Description:** Must secure AJAX requests
- **Impact:** CSRF attacks and security breaches

##### ✅ Error Handling
- **Status:** COMPLIANT
- **Severity:** HIGH
- **Score:** 100%
- **Description:** Must handle errors and log them
- **Impact:** Unhandled errors and difficult debugging

##### ✅ Input Validation
- **Status:** COMPLIANT
- **Severity:** CRITICAL
- **Score:** 100%
- **Description:** Must validate all user inputs
- **Impact:** SQL injection and XSS vulnerabilities

##### ❌ Output Sanitization
- **Status:** VIOLATION
- **Severity:** CRITICAL
- **Score:** 0%
- **Description:** Must sanitize all outputs
- **Impact:** XSS attacks and data corruption
- **Violations:**
  - Direct output without sanitization
- **Recommendations:**
  - Add output sanitization using htmlspecialchars()

##### ✅ Session Management
- **Status:** COMPLIANT
- **Severity:** HIGH
- **Score:** 100%
- **Description:** Must implement secure session management
- **Impact:** Session hijacking and unauthorized access

##### ✅ Sql Injection Prevention
- **Status:** COMPLIANT
- **Severity:** CRITICAL
- **Score:** 100%
- **Description:** Must prevent SQL injection attacks
- **Impact:** Database compromise and data theft

##### ✅ File Upload Security
- **Status:** COMPLIANT
- **Severity:** HIGH
- **Score:** 100%
- **Description:** Must validate file uploads securely
- **Impact:** Malicious file uploads and server compromise

##### ✅ Rate Limiting
- **Status:** COMPLIANT
- **Severity:** MEDIUM
- **Score:** 100%
- **Description:** Must implement rate limiting for sensitive operations
- **Impact:** Brute force attacks and resource exhaustion

##### ✅ Logging Audit
- **Status:** COMPLIANT
- **Severity:** HIGH
- **Score:** 100%
- **Description:** Must log all important activities
- **Impact:** No audit trail and compliance issues

##### ✅ Data Encryption
- **Status:** COMPLIANT
- **Severity:** HIGH
- **Score:** 100%
- **Description:** Must encrypt sensitive data
- **Impact:** Data breaches and privacy violations

##### ✅ Api Versioning
- **Status:** COMPLIANT
- **Severity:** MEDIUM
- **Score:** 100%
- **Description:** Must implement API versioning
- **Impact:** Breaking changes and integration issues

##### ✅ Performance Optimization
- **Status:** COMPLIANT
- **Severity:** MEDIUM
- **Score:** 100%
- **Description:** Must optimize performance
- **Impact:** Slow response times and poor user experience

##### ✅ Memory Management
- **Status:** COMPLIANT
- **Severity:** MEDIUM
- **Score:** 100%
- **Description:** Must manage memory efficiently
- **Impact:** Memory leaks and server instability

---

### 🌐 ULTIMATE LANGUAGE ANALYSIS (Enhanced from lang_comparison_script.py)

#### 📊 Coverage Statistics
- **Arabic Coverage:** 76.7% (46/60)
- **English Coverage:** 76.7% (46/60)
- **Total Used Variables:** 60 variables
- **Arabic Defined:** 1014 variables
- **English Defined:** 1533 variables

#### 🔍 Analysis Scope
- **Models Analyzed:** 22 models
- **Views Analyzed:** 0 views
- **Arabic Files Found:** 1 files
- **English Files Found:** 1 files

#### ⚠️ Issues Detected
- **Missing Arabic:** ❌ 14 variables
- **Missing English:** ❌ 14 variables
- **Unused Arabic:** 🧹 968 variables
- **Unused English:** 🧹 1487 variables
- **Hardcoded Text:** ⚠️ 149 instances

#### 📈 Quality Metrics
- **Compliance Score:** 0.0%
- **Maintenance Score:** 0%
- **I18n Readiness:** 50%
- **Translation Quality:** 0%

#### ✅ Used Variables (Top 200000)
   - `accounts/accounting` (AR: ❌, EN: ❌, Used: 1x)
   - `audit_duplicate_models` (AR: ✅, EN: ✅, Used: 1x)
   - `audit_negative_stock` (AR: ✅, EN: ✅, Used: 1x)
   - `audit_products_without_categories` (AR: ✅, EN: ✅, Used: 1x)
   - `audit_products_without_images` (AR: ✅, EN: ✅, Used: 1x)
   - `audit_products_without_prices` (AR: ✅, EN: ✅, Used: 1x)
   - `catalog/product` (AR: ❌, EN: ❌, Used: 46x)
   - `common/header` (AR: ❌, EN: ❌, Used: 7x)
   - `error_add_failed` (AR: ✅, EN: ✅, Used: 1x)
   - `error_edit_failed` (AR: ✅, EN: ✅, Used: 1x)
   - `error_empty_file` (AR: ✅, EN: ✅, Used: 1x)
   - `error_import_row` (AR: ✅, EN: ✅, Used: 1x)
   - `error_insufficient_stock_for_product` (AR: ❌, EN: ❌, Used: 1x)
   - `error_insufficient_stock_for_transfer` (AR: ❌, EN: ❌, Used: 1x)
   - `error_insufficient_stock_for_transfer_item` (AR: ❌, EN: ❌, Used: 1x)
   - `error_invalid_field` (AR: ✅, EN: ✅, Used: 1x)
   - `error_invalid_file_type` (AR: ✅, EN: ✅, Used: 1x)
   - `error_invalid_item` (AR: ❌, EN: ❌, Used: 1x)
   - `error_items_required` (AR: ✅, EN: ✅, Used: 1x)
   - `error_meta_title` (AR: ✅, EN: ✅, Used: 1x)
   - `error_missing_data` (AR: ✅, EN: ✅, Used: 1x)
   - `error_missing_required_data` (AR: ✅, EN: ✅, Used: 1x)
   - `error_model` (AR: ✅, EN: ✅, Used: 1x)
   - `error_model_exists` (AR: ✅, EN: ✅, Used: 1x)
   - `error_movement_failed_for_product` (AR: ❌, EN: ❌, Used: 1x)
   - `error_name` (AR: ✅, EN: ✅, Used: 1x)
   - `error_permission` (AR: ✅, EN: ✅, Used: 9x)
   - `error_permission_advanced` (AR: ✅, EN: ✅, Used: 9x)
   - `error_product_has_dependencies` (AR: ✅, EN: ✅, Used: 1x)
   - `error_product_id_required` (AR: ✅, EN: ✅, Used: 3x)
   - `error_product_not_found` (AR: ✅, EN: ✅, Used: 1x)
   - `error_quantity_must_be_positive` (AR: ✅, EN: ✅, Used: 1x)
   - `error_same_branch` (AR: ✅, EN: ✅, Used: 1x)
   - `error_transfer_already_completed` (AR: ❌, EN: ❌, Used: 1x)
   - `error_transfer_no_items` (AR: ❌, EN: ❌, Used: 1x)
   - `error_transfer_not_found` (AR: ❌, EN: ❌, Used: 1x)
   - `heading_title` (AR: ✅, EN: ✅, Used: 2x)
   - `inventory/inventory` (AR: ❌, EN: ❌, Used: 1x)
   - `text_abc_recommendation_a` (AR: ✅, EN: ✅, Used: 1x)
   - `text_abc_recommendation_b` (AR: ✅, EN: ✅, Used: 1x)
   - `text_abc_recommendation_c` (AR: ✅, EN: ✅, Used: 1x)
   - `text_add` (AR: ✅, EN: ✅, Used: 2x)
   - `text_auto_saved` (AR: ✅, EN: ✅, Used: 1x)
   - `text_disabled` (AR: ✅, EN: ✅, Used: 1x)
   - `text_edit` (AR: ✅, EN: ✅, Used: 2x)
   - `text_enabled` (AR: ✅, EN: ✅, Used: 1x)
   - `text_field_updated` (AR: ✅, EN: ✅, Used: 1x)
   - `text_home` (AR: ❌, EN: ❌, Used: 1x)
   - `text_import_success` (AR: ✅, EN: ✅, Used: 1x)
   - `text_initial_inventory` (AR: ✅, EN: ✅, Used: 1x)
   - `text_inventory_adjustment` (AR: ✅, EN: ✅, Used: 1x)
   - `text_low_stock_alert` (AR: ✅, EN: ✅, Used: 1x)
   - `text_pagination` (AR: ❌, EN: ❌, Used: 1x)
   - `text_price_adjustment` (AR: ✅, EN: ✅, Used: 1x)
   - `text_product_added_notification` (AR: ✅, EN: ✅, Used: 1x)
   - `text_product_critical_changes` (AR: ✅, EN: ✅, Used: 1x)
   - `text_success_add` (AR: ✅, EN: ✅, Used: 1x)
   - `text_success_copy` (AR: ✅, EN: ✅, Used: 1x)
   - `text_success_delete` (AR: ✅, EN: ✅, Used: 1x)
   - `text_success_edit` (AR: ✅, EN: ✅, Used: 1x)

#### 💡 Suggested Code for Arabic File (Ready to Copy)
```php
$_['accounts/accounting'] = '';  // TODO: Arabic translation
$_['catalog/product'] = '';  // TODO: Arabic translation
$_['common/header'] = '';  // TODO: Arabic translation
$_['error_insufficient_stock_for_product'] = '';  // TODO: Arabic translation
$_['error_insufficient_stock_for_transfer'] = '';  // TODO: Arabic translation
$_['error_insufficient_stock_for_transfer_item'] = '';  // TODO: Arabic translation
$_['error_invalid_item'] = '';  // TODO: Arabic translation
$_['error_movement_failed_for_product'] = '';  // TODO: Arabic translation
$_['error_transfer_already_completed'] = '';  // TODO: Arabic translation
$_['error_transfer_no_items'] = '';  // TODO: Arabic translation
$_['error_transfer_not_found'] = '';  // TODO: Arabic translation
$_['inventory/inventory'] = '';  // TODO: Arabic translation
$_['text_home'] = '';  // TODO: Arabic translation
$_['text_pagination'] = '';  // TODO: Arabic translation
```

#### 💡 Suggested Code for English File (Ready to Copy)
```php
$_['accounts/accounting'] = '';  // TODO: English translation
$_['catalog/product'] = '';  // TODO: English translation
$_['common/header'] = '';  // TODO: English translation
$_['error_insufficient_stock_for_product'] = '';  // TODO: English translation
$_['error_insufficient_stock_for_transfer'] = '';  // TODO: English translation
$_['error_insufficient_stock_for_transfer_item'] = '';  // TODO: English translation
$_['error_invalid_item'] = '';  // TODO: English translation
$_['error_movement_failed_for_product'] = '';  // TODO: English translation
$_['error_transfer_already_completed'] = '';  // TODO: English translation
$_['error_transfer_no_items'] = '';  // TODO: English translation
$_['error_transfer_not_found'] = '';  // TODO: English translation
$_['inventory/inventory'] = '';  // TODO: English translation
$_['text_home'] = '';  // TODO: English translation
$_['text_pagination'] = '';  // TODO: English translation
```

#### 🧹 Unused in Arabic (968)
   - `adjustment_modal_description`, `adjustment_modal_quantity`, `adjustment_modal_reason`, `adjustment_modal_title`, `barcode_modal_preview`, `barcode_modal_title`, `barcode_modal_type`, `barcode_modal_value`, `button_add_dynamic_rule`, `button_add_movement`, `button_add_option`, `button_add_product`, `button_add_supplier_price`, `button_add_unit`, `button_apply`, `button_apply_adjustment`, `button_apply_price`, `button_barcode_add`, `button_bundle_add`, `button_calculate`, `button_calculate_margin`, `button_calculate_price`, `button_cancel`, `button_cancel_adjustment`, `button_cancel_count_sheet`, `button_cancel_dynamic_rule`, `button_cancel_supplier_price`, `button_cancel_transfer`, `button_clear`, `button_close`, `button_close_barcode_modal`, `button_create_count_sheet`, `button_create_transfer`, `button_cross_sell_add`, `button_discount_add`, `button_filter`, `button_generate`, `button_generate_barcode`, `button_image_add`, `button_manage_columns`, `button_option_value_add`, `button_remove`, `button_reset`, `button_rule_add`, `button_save`, `button_save_supplier_price`, `button_unit_add`, `button_upsell_add`, `button_view`, `column_action`, `column_available`, `column_average_cost`, `column_base_price`, `column_batch`, `column_branch`, `column_change_type`, `column_cost`, `column_cost_impact`, `column_currency`, `column_custom_price`, `column_customer`, `column_date`, `column_date_added`, `column_date_end`, `column_date_start`, `column_egs_code`, `column_expiry_date`, `column_half_wholesale_price`, `column_id`, `column_image`, `column_is_default`, `column_last_purchase_date`, `column_min_quantity`, `column_min_stock`, `column_model`, `column_name`, `column_new_cost`, `column_new_price`, `column_notes`, `column_old_cost`, `column_old_price`, `column_order_id`, `column_po_number`, `column_price`, `column_price_type`, `column_quantity`, `column_receipt_date`, `column_receipt_number`, `column_reference`, `column_reserved`, `column_rule_condition`, `column_rule_name`, `column_rule_priority`, `column_rule_type`, `column_rule_value`, `column_special_price`, `column_status`, `column_stock`, `column_stock_online`, `column_supplier`, `column_total`, `column_total_value`, `column_transfer_quantity`, `column_type`, `column_unit`, `column_unit_price`, `column_user`, `column_wholesale_price`, `count_sheet_modal_branch`, `count_sheet_modal_category`, `count_sheet_modal_count_type`, `count_sheet_modal_filter_name`, `count_sheet_modal_notes`, `count_sheet_modal_sheet_format`, `count_sheet_modal_title`, `dynamic_pricing_modal_condition`, `dynamic_pricing_modal_formula`, `dynamic_pricing_modal_priority`, `dynamic_pricing_modal_rule_name`, `dynamic_pricing_modal_rule_value`, `dynamic_pricing_modal_title`, `entry_action`, `entry_additional_image`, `entry_adjustment_movement_type`, `entry_adjustment_reason`, `entry_adjustment_type`, `entry_attribute`, `entry_attribute_group`, `entry_average_cost`, `entry_barcode`, `entry_barcode_type`, `entry_base_price`, `entry_branch`, `entry_bundle_discount_type`, `entry_bundle_discount_value`, `entry_bundle_name`, `entry_bundle_products`, `entry_buy_quantity`, `entry_category`, `entry_condition_value`, `entry_consignment`, `entry_conversion_factor`, `entry_cost_max`, `entry_cost_min`, `entry_cost_value`, `entry_custom_price`, `entry_custom_reason`, `entry_customer_group`, `entry_date_added`, `entry_date_available`, `entry_date_end`, `entry_date_start`, `entry_description`, `entry_dimensions`, `entry_direct_cost`, `entry_discount_name`, `entry_discount_type`, `entry_discount_value`, `entry_document_reference`, `entry_download`, `entry_ean`, `entry_filter`, `entry_get_quantity`, `entry_half_wholesale_price`, `entry_has_image`, `entry_height`, `entry_image`, `entry_isbn`, `entry_jan`, `entry_keyword`, `entry_layout`, `entry_length`, `entry_location`, `entry_low_stock`, `entry_manufacturer`, `entry_meta_description`, `entry_meta_keyword`, `entry_meta_title`, `entry_minimum`, `entry_model`, `entry_movement_type`, `entry_mpn`, `entry_name`, `entry_notes`, `entry_option`, `entry_option_points`, `entry_option_value`, `entry_points`, `entry_price`, `entry_price_change_reason`, `entry_priority`, `entry_product`, `entry_profit_margin`, `entry_quantity`, `entry_quantity_available`, `entry_quantity_max`, `entry_quantity_min`, `entry_reason`, `entry_recurring`, `entry_related`, `entry_required`, `entry_reward`, `entry_rule_formula`, `entry_rule_name`, `entry_rule_value`, `entry_shipping`, `entry_sku`, `entry_sort_order`, `entry_special_price`, `entry_status`, `entry_stock_status`, `entry_store`, `entry_subtract`, `entry_supplier`, `entry_tag`, `entry_tax_class`, `entry_text`, `entry_total_value`, `entry_transfer_notes`, `entry_unit`, `entry_unit_type`, `entry_upc`, `entry_update_type`, `entry_value`, `entry_weight`, `entry_weight_class`, `entry_wholesale_price`, `entry_width`, `error_adjustment_failed`, `error_adjustment_type_required`, `error_alert_id_required`, `error_base_price_invalid`, `error_base_unit`, `error_base_unit_required`, `error_branch_required`, `error_bundle_name`, `error_completing_stock_count`, `error_confirmation_required`, `error_conversion_factor`, `error_cross_sell_product`, `error_cross_sell_unit`, `error_custom_reason_required`, `error_destination_branch_required`, `error_fixed_range`, `error_insufficient_stock`, `error_invalid_cost`, `error_invalid_margin`, `error_invalid_price_field`, `error_keyword`, `error_margin_range`, `error_meta_description_length`, `error_meta_title_length`, `error_minimum_level_required`, `error_missing_movement_data`, `error_model_required`, `error_movement_failed`, `error_name_required`, `error_no_expiring_products`, `error_no_inventory_data`, `error_no_low_stock`, `error_no_products_selected`, `error_no_products_to_export`, `error_no_recent_movements`, `error_percentage_range`, `error_positive_quantity`, `error_price_id`, `error_price_required`, `error_pricing_required`, `error_product`, `error_product_id`, `error_quantity`, `error_quantity_exceeded`, `error_quantity_exceeds_available`, `error_quantity_invalid`, `error_quantity_required`, `error_reason_required`, `error_required_data`, `error_select_branch_first`, `error_select_destination_branch_first`, `error_select_source_branch_first`, `error_sheet_creation`, `error_source_branch_required`, `error_status_required`, `error_stock_count_creation`, `error_stock_count_id_required`, `error_supplier_required`, `error_transfer_failed`, `error_unique`, `error_unit`, `error_unit_id_required`, `error_unit_required`, `error_units`, `error_update_failed`, `error_updating_alert`, `error_upsell_product`, `error_upsell_unit`, `error_warning`, `help_bundle_and_discount`, `help_category`, `help_condition_value`, `help_direct_cost`, `help_download`, `help_ean`, `help_filter`, `help_isbn`, `help_jan`, `help_manufacturer`, `help_meta_description`, `help_meta_keyword`, `help_meta_title`, `help_minimum`, `help_mpn`, `help_points`, `help_priority`, `help_recommendation`, `help_related`, `help_rule_formula`, `help_rule_value`, `help_seo_permalink`, `help_sku`, `help_stock_status`, `help_tag`, `help_upc`, `supplier_price_modal_currency`, `supplier_price_modal_default`, `supplier_price_modal_min_quantity`, `supplier_price_modal_price`, `supplier_price_modal_supplier`, `supplier_price_modal_title`, `supplier_price_modal_unit`, `tab_bundle`, `tab_cross_sell`, `tab_data`, `tab_dynamic_pricing`, `tab_general`, `tab_image`, `tab_inventory`, `tab_movement`, `tab_option`, `tab_orders`, `tab_pricing`, `tab_purchase`, `tab_recommendation`, `tab_units`, `tab_upsell`, `text_abc_analysis`, `text_account`, `text_accounting_integration`, `text_action`, `text_actual_quantity`, `text_add_inventory_movement`, `text_add_stock`, `text_add_transfer_items_first`, `text_additional_images`, `text_additional_images_details`, `text_additional_unit`, `text_additional_unit_help`, `text_adjust_by_percentage`, `text_adjustment`, `text_adjustment_added`, `text_adjustment_applied`, `text_adjustment_decrease`, `text_adjustment_details`, `text_adjustment_fields_required`, `text_adjustment_increase`, `text_adjustment_saved`, `text_advanced_pricing`, `text_advanced_search`, `text_ai_recommendations`, `text_alerts_notifications`, `text_all`, `text_all_branches`, `text_all_categories`, `text_all_customers`, `text_all_types`, `text_all_units`, `text_amount`, `text_apply_count`, `text_auto_backup`, `text_auto_save_failed`, `text_automated_testing`, `text_available`, `text_available_quantity`, `text_available_quantity_help`, `text_average_cost`, `text_average_cost_help`, `text_average_price`, `text_backup_recovery`, `text_barcode_exists`, `text_barcode_format`, `text_barcode_generated`, `text_barcode_help`, `text_barcode_help_intro`, `text_barcode_help_tip`, `text_barcode_info`, `text_barcode_management`, `text_barcode_option`, `text_barcode_option_help`, `text_barcode_preview`, `text_barcode_preview_help`, `text_barcode_preview_placeholder`, `text_barcode_type`, `text_barcode_types`, `text_barcode_types_help`, `text_barcode_unit`, `text_barcode_unit_help`, `text_base_price`, `text_base_price_help`, `text_base_unit`, `text_base_unit_help`, `text_base_unit_info`, `text_base_unit_required`, `text_base_unit_warning`, `text_basic_data`, `text_below_min_stock`, `text_branch`, `text_bulk_cost_update`, `text_bulk_delete`, `text_bulk_disable`, `text_bulk_enable`, `text_bulk_operations`, `text_bulk_price_update`, `text_bulk_status_change`, `text_bulk_stock_update`, `text_bulk_update_category`, `text_bulk_update_price`, `text_bundle_discount`, `text_bundle_discount_types`, `text_bundle_discount_types_explanation`, `text_bundle_management`, `text_bundle_pricing`, `text_bundle_products`, `text_bundle_products_explanation`, `text_bundles`, `text_bundles_discounts_help`, `text_bundles_discounts_help_intro`, `text_bundles_discounts_help_tip`, `text_bundles_explanation`, `text_buy_x_get_discount`, `text_buy_x_get_y`, `text_cache_status`, `text_calculated_new_price`, `text_cannot_remove_base_unit`, `text_categories_filters`, `text_categories_filters_help`, `text_categorization`, `text_central_services`, `text_clustering`, `text_code_quality`, `text_competitor_price`, `text_complete_transfer`, `text_compliance_standards`, `text_confirm`, `text_confirm_action`, `text_confirm_adjustment`, `text_confirm_cost_change`, `text_confirm_delete`, `text_consignment`, `text_constitution_compliant`, `text_contra_account`, `text_conversion_example`, `text_conversion_factor`, `text_conversion_factor_help`, `text_conversion_result`, `text_cost`, `text_cost_based_pricing`, `text_cost_change_reason`, `text_cost_details`, `text_cost_help`, `text_cost_history`, `text_cost_history_help`, `text_cost_history_section`, `text_cost_impact`, `text_cost_must_be_positive`, `text_cost_of_goods_sold`, `text_cost_updated`, `text_cost_value`, `text_count`, `text_count_applied`, `text_count_notes`, `text_count_pending`, `text_count_sheet`, `text_count_status`, `text_count_type`, `text_counted_quantity`, `text_create_count_sheet`, `text_create_transfer`, `text_credit`, `text_cross_sell`, `text_cross_sell_help`, `text_currency`, `text_current_base_price`, `text_current_cost`, `text_current_quantity`, `text_current_total_value`, `text_current_unit_cost`, `text_custom_price`, `text_custom_reason`, `text_customer_group`, `text_customer_preferences`, `text_customers_also_viewed`, `text_dashboard_integration`, `text_data_correction`, `text_data_help`, `text_data_help_intro`, `text_data_help_tip`, `text_data_integrity`, `text_data_protection`, `text_data_validation`, `text_date_added`, `text_date_modified`, `text_days_left`, `text_debit`, `text_decrease`, `text_decrease_by`, `text_default`, `text_demand_prediction`, `text_destination_branch`, `text_detailed_notes`, `text_difference`, `text_dimensions_weight`, `text_dimensions_weight_details`, `text_dimensions_weight_help`, `text_discount_period`, `text_discount_period_explanation`, `text_discount_types`, `text_discount_types_explanation`, `text_discounts`, `text_discounts_explanation`, `text_document_reference`, `text_dynamic_pricing`, `text_edit_price`, `text_enter_margin_percentage`, `text_enter_positive_value`, `text_enter_quantity`, `text_enter_valid_cost`, `text_enter_valid_margin`, `text_enterprise_features`, `text_enterprise_grade_plus`, `text_equity_account`, `text_error_deleting`, `text_error_loading`, `text_error_saving`, `text_excel_sheet`, `text_expired`, `text_expiring_products`, `text_export`, `text_export_excel`, `text_export_pdf`, `text_export_success`, `text_field_validation`, `text_filter`, `text_filter_applied`, `text_filter_by_branch`, `text_filter_by_date`, `text_filter_by_type`, `text_filter_reset`, `text_financial_impact`, `text_fixed`, `text_fixed_help`, `text_fixed_value`, `text_formula`, `text_free`, `text_free_product`, `text_frequently_bought_together`, `text_from_date`, `text_from_unit`, `text_full_count`, `text_fully_integrated`, `text_gdpr_compliance`, `text_general_help_intro`, `text_general_help_tip`, `text_general_info_help`, `text_general_settings`, `text_generate_barcode`, `text_generated_successfully`, `text_gl_account_impact`, `text_half_wholesale_price`, `text_has_expired`, `text_height`, `text_high_availability`, `text_id`, `text_identifiers`, `text_identifiers_help`, `text_images_help`, `text_images_help_intro`, `text_images_help_tip`, `text_import`, `text_include_price`, `text_increase`, `text_increase_by`, `text_individual_labels`, `text_insufficient_stock`, `text_insufficient_stock_warning`, `text_integration_apis`, `text_invalid_cost`, `text_inventory`, `text_inventory_account`, `text_inventory_adjustment_account`, `text_inventory_count`, `text_inventory_details`, `text_inventory_help`, `text_inventory_help_intro`, `text_inventory_help_tip`, `text_inventory_info`, `text_inventory_levels`, `text_inventory_loss_account`, `text_inventory_operations`, `text_inventory_quantity`, `text_inventory_quantity_help`, `text_inventory_report`, `text_inventory_settings`, `text_inventory_suggestions`, `text_inventory_transfer`, `text_inventory_valuation`, `text_inventory_valuation_account`, `text_iso_standards`, `text_keyword`, `text_length`, `text_list`, `text_load_balancing`, `text_load_time`, `text_loading`, `text_locale_settings`, `text_low_stock`, `text_low_stock_products`, `text_main_image`, `text_main_image_details`, `text_main_image_help`, `text_manage_columns`, `text_manual_cost_update`, `text_margin`, `text_margin_calculation_help`, `text_margin_help`, `text_market_price_change`, `text_marketing_suggestions`, `text_meta_data`, `text_meta_data_help`, `text_min`, `text_minus`, `text_mission_critical`, `text_mobile_optimized`, `text_mobile_sheet`, `text_modal_cancel`, `text_modal_close`, `text_modal_save`, `text_modal_title_adjustment`, `text_modal_title_barcode`, `text_modal_title_count_sheet`, `text_modal_title_dynamic_pricing`, `text_modal_title_inventory`, `text_modal_title_supplier_price`, `text_modal_title_transfer`, `text_movement_added`, `text_movement_filter`, `text_movement_filters`, `text_movement_filters_help`, `text_movement_frequency`, `text_movement_help`, `text_movement_help_intro`, `text_movement_help_tip`, `text_movement_history`, `text_movement_info`, `text_movement_reason`, `text_movement_stats`, `text_movement_stats_help`, `text_movement_success`, `text_movement_tracking`, `text_movement_type`, `text_movement_types`, `text_movement_types_help`, `text_movements`, `text_multi_language`, `text_multi_unit_system`, `text_na`, `text_new_cost`, `text_new_quantity`, `text_new_total_value`, `text_new_unit_cost`, `text_no`, `text_no_barcode`, `text_no_branches_available`, `text_no_cost_history`, `text_no_expiring_products`, `text_no_inventory`, `text_no_inventory_data`, `text_no_low_stock`, `text_no_movements`, `text_no_option`, `text_no_orders`, `text_no_price_history`, `text_no_purchase_history`, `text_no_recent_movements`, `text_no_results`, `text_no_supplier_pricing`, `text_no_units`, `text_no_units_selected`, `text_none`, `text_note`, `text_notes`, `text_on_hand`, `text_operations`, `text_option`, `text_option_inventory`, `text_option_inventory_help`, `text_option_pricing`, `text_option_pricing_help`, `text_option_types`, `text_option_types_help`, `text_option_unit`, `text_option_unit_help`, `text_option_value`, `text_option_values`, `text_option_values_help`, `text_optional`, `text_options_help`, `text_options_help_intro`, `text_options_help_tip`, `text_order_analysis`, `text_order_frequency`, `text_order_statistics`, `text_order_statistics_help`, `text_order_statistics_section`, `text_orders`, `text_orders_help`, `text_orders_help_intro`, `text_orders_help_tip`, `text_orders_info`, `text_orders_list_help`, `text_orders_list_section`, `text_other_reason`, `text_page_size`, `text_partial_count`, `text_percent`, `text_percentage`, `text_percentage_help`, `text_percentage_value`, `text_performance_metrics`, `text_performance_report`, `text_performance_testing`, `text_periodic_count`, `text_plus`, `text_preview_journal_entry`, `text_price`, `text_price_added`, `text_price_applied`, `text_price_calculator`, `text_price_calculator_help`, `text_price_deleted`, `text_price_field`, `text_price_history`, `text_price_updated`, `text_prices_updated`, `text_pricing_help`, `text_pricing_help_intro`, `text_pricing_help_tip`, `text_pricing_info`, `text_pricing_list`, `text_pricing_suggestions`, `text_print_barcodes`, `text_printed_sheet`, `text_product_description`, `text_product_description_help`, `text_product_info`, `text_product_name`, `text_product_name_help`, `text_production_account`, `text_production_ready`, `text_profit_margin`, `text_profit_margin_help`, `text_profit_margin_percentage`, `text_promotional_pricing`, `text_purchase`, `text_purchase_help`, `text_purchase_help_intro`, `text_purchase_help_tip`, `text_purchase_history`, `text_purchase_history_help`, `text_purchase_history_section`, `text_purchase_info`, `text_quality_assurance`, `text_quantity`, `text_quantity_change`, `text_quantity_exceeds_available`, `text_quantity_must_be_positive`, `text_quantity_note`, `text_quantity_on_hand`, `text_quantity_per_product`, `text_quantity_to_add`, `text_quantity_to_remove`, `text_quick_actions`, `text_quick_adjustment`, `text_quick_adjustment_help`, `text_quick_info`, `text_quick_stats`, `text_rating`, `text_reason`, `text_reason_adjustment`, `text_reason_correction`, `text_reason_customer_returns`, `text_reason_damaged`, `text_reason_damaged_goods`, `text_reason_expired`, `text_reason_expired_goods`, `text_reason_initial`, `text_reason_initial_stock`, `text_reason_inventory_correction`, `text_reason_manual`, `text_reason_other`, `text_reason_production`, `text_reason_purchase`, `text_reason_stock_count`, `text_reason_transfer`, `text_recent_activities`, `text_recent_movements`, `text_recommendation_discount`, `text_recommendation_discount_help`, `text_recommendation_help`, `text_recommendation_help_intro`, `text_recommendation_help_tip`, `text_recommendation_priority`, `text_recommendation_priority_help`, `text_recommendation_unit`, `text_recommendation_unit_help`, `text_recommended_products`, `text_recovery_point`, `text_related_products`, `text_remove_stock`, `text_reports`, `text_required_fields`, `text_response_time`, `text_responsive_design`, `text_rest_api`, `text_rollback_support`, `text_rtl_support`, `text_sale`, `text_sales_history`, `text_sales_trend`, `text_sales_trend_help`, `text_sales_trend_section`, `text_same_branch_error`, `text_save_and_close`, `text_save_and_copy`, `text_save_and_new`, `text_saving`, `text_scalability`, `text_search`, `text_search_by_barcode`, `text_search_by_category`, `text_search_by_manufacturer`, `text_search_by_price_range`, `text_search_by_stock_level`, `text_search_products`, `text_seasonal_trends`, `text_security_audit`, `text_security_standards`, `text_select`, `text_select_branch`, `text_select_branch_first`, `text_select_destination_branch_first`, `text_select_option_first`, `text_select_products_first`, `text_select_reason_first`, `text_select_source_branch_first`, `text_select_status`, `text_select_unit`, `text_select_unit_first`, `text_seo_permalink`, `text_seo_url`, `text_seo_url_help`, `text_set_cost`, `text_set_quantity`, `text_sheet_created`, `text_sheet_format`, `text_sheet_labels`, `text_sort_order`, `text_sort_order_details`, `text_source_branch`, `text_special_price`, `text_special_price_help`, `text_specify_other_reason`, `text_specify_reason`, `text_stock`, `text_stock_count`, `text_stock_level`, `text_stock_movements`, `text_stock_options`, `text_stock_options_help`, `text_stock_value_change`, `text_success`, `text_success_operation`, `text_success_update`, `text_supplier_price_change`, `text_supplier_price_modal`, `text_supplier_pricing_help`, `text_supplier_pricing_section`, `text_system_quantity`, `text_third_party_integration`, `text_tier_pricing`, `text_time_period`, `text_to_date`, `text_to_unit`, `text_total_incoming`, `text_total_outgoing`, `text_total_revenue`, `text_total_sold`, `text_total_spent`, `text_totals`, `text_touch_friendly`, `text_transaction_support`, `text_transfer`, `text_transfer_added`, `text_transfer_completed`, `text_transfer_in`, `text_transfer_items`, `text_transfer_notes`, `text_transfer_out`, `text_transfer_pending`, `text_transfer_quantity`, `text_transfer_status`, `text_translation_management`, `text_type_to_search`, `text_unit`, `text_unit_conversion`, `text_unit_conversion_info`, `text_unit_converter`, `text_unit_converter_help`, `text_unit_inventory`, `text_unit_pricing`, `text_units_help`, `text_units_help_intro`, `text_units_info`, `text_units_list`, `text_unknown`, `text_unknown_error`, `text_unsaved_changes`, `text_unsaved_changes_warning`, `text_update_based_on_new_cost`, `text_update_cost`, `text_update_prices_confirm`, `text_update_sales_prices`, `text_update_selling_prices`, `text_update_type`, `text_upsell`, `text_upsell_help`, `text_valuation_report`, `text_value`, `text_value_change`, `text_view_all`, `text_view_all_movements`, `text_view_inventory`, `text_views`, `text_wac_enabled`, `text_wac_explanation`, `text_webhook_support`, `text_weighted_average_cost`, `text_weighted_average_cost_calculator`, `text_what_are_bundles`, `text_what_are_discounts`, `text_wholesale_price`, `text_wholesale_price_help`, `text_width`, `text_yes`, `transfer_modal_destination_branch`, `transfer_modal_items`, `transfer_modal_notes`, `transfer_modal_source_branch`, `transfer_modal_title`

#### 🧹 Unused in English (1487)
   - `button_add_dynamic_rule`, `button_add_movement`, `button_add_option`, `button_add_product`, `button_add_supplier_price`, `button_add_unit`, `button_adjust`, `button_apply`, `button_apply_adjustment`, `button_apply_price`, `button_barcode_add`, `button_bundle_add`, `button_calculate`, `button_calculate_margin`, `button_calculate_price`, `button_cancel`, `button_cancel_adjustment`, `button_cancel_count_sheet`, `button_cancel_dynamic_rule`, `button_cancel_supplier_price`, `button_cancel_transfer`, `button_clear`, `button_close`, `button_close_barcode_modal`, `button_create_count_sheet`, `button_create_transfer`, `button_cross_sell_add`, `button_discount_add`, `button_edit_cost`, `button_filter`, `button_generate`, `button_generate_barcode`, `button_image_add`, `button_manage_columns`, `button_option_value_add`, `button_remove`, `button_reset`, `button_rule_add`, `button_save`, `button_save_supplier_price`, `button_unit_add`, `button_upsell_add`, `button_view`, `column_action`, `column_available`, `column_average_cost`, `column_base_price`, `column_branch`, `column_change_type`, `column_cost`, `column_cost_impact`, `column_currency`, `column_custom_price`, `column_customer`, `column_date`, `column_date_added`, `column_date_end`, `column_date_start`, `column_egs_code`, `column_half_wholesale_price`, `column_id`, `column_image`, `column_is_default`, `column_last_purchase_date`, `column_min_quantity`, `column_model`, `column_name`, `column_new_cost`, `column_new_price`, `column_notes`, `column_old_cost`, `column_old_price`, `column_order_id`, `column_po_number`, `column_price`, `column_price_type`, `column_quantity`, `column_receipt_date`, `column_receipt_number`, `column_reference`, `column_reserved`, `column_rule_condition`, `column_rule_name`, `column_rule_priority`, `column_rule_type`, `column_rule_value`, `column_special_price`, `column_status`, `column_stock`, `column_stock_online`, `column_supplier`, `column_total`, `column_total_value`, `column_transfer_quantity`, `column_type`, `column_unit`, `column_unit_price`, `column_user`, `column_wholesale_price`, `entry_action`, `entry_additional_image`, `entry_adjustment_movement_type`, `entry_adjustment_reason`, `entry_adjustment_type`, `entry_attribute`, `entry_attribute_group`, `entry_average_cost`, `entry_barcode`, `entry_barcode_type`, `entry_base_price`, `entry_branch`, `entry_bundle_discount_type`, `entry_bundle_discount_value`, `entry_bundle_name`, `entry_bundle_products`, `entry_buy_quantity`, `entry_category`, `entry_condition_value`, `entry_consignment`, `entry_conversion_factor`, `entry_cost`, `entry_cost_max`, `entry_cost_min`, `entry_cost_value`, `entry_custom_price`, `entry_custom_reason`, `entry_customer_group`, `entry_date_added`, `entry_date_available`, `entry_date_end`, `entry_date_start`, `entry_description`, `entry_dimensions`, `entry_direct_cost`, `entry_discount_name`, `entry_discount_type`, `entry_discount_value`, `entry_document_reference`, `entry_download`, `entry_ean`, `entry_filter`, `entry_get_quantity`, `entry_half_wholesale_price`, `entry_has_image`, `entry_height`, `entry_image`, `entry_isbn`, `entry_jan`, `entry_keyword`, `entry_layout`, `entry_length`, `entry_location`, `entry_low_stock`, `entry_manufacturer`, `entry_meta_description`, `entry_meta_keyword`, `entry_meta_title`, `entry_minimum`, `entry_model`, `entry_movement_type`, `entry_mpn`, `entry_name`, `entry_notes`, `entry_option`, `entry_option_points`, `entry_option_value`, `entry_points`, `entry_price`, `entry_price_change_reason`, `entry_priority`, `entry_product`, `entry_profit_margin`, `entry_quantity`, `entry_quantity_available`, `entry_quantity_max`, `entry_quantity_min`, `entry_reason`, `entry_recurring`, `entry_related`, `entry_required`, `entry_reward`, `entry_rule_formula`, `entry_rule_name`, `entry_rule_value`, `entry_shipping`, `entry_sku`, `entry_sort_order`, `entry_special_price`, `entry_status`, `entry_stock_status`, `entry_store`, `entry_subtract`, `entry_supplier`, `entry_tag`, `entry_tax_class`, `entry_text`, `entry_total_value`, `entry_transfer_notes`, `entry_unit`, `entry_unit_type`, `entry_upc`, `entry_update_type`, `entry_value`, `entry_weight`, `entry_weight_class`, `entry_wholesale_price`, `entry_width`, `error_adjustment_failed`, `error_adjustment_type_required`, `error_base_price_invalid`, `error_base_unit`, `error_base_unit_required`, `error_branch_required`, `error_bundle_name`, `error_confirmation_required`, `error_cost`, `error_cross_sell_product`, `error_cross_sell_unit`, `error_custom_reason`, `error_custom_reason_required`, `error_destination_branch_required`, `error_insufficient_stock`, `error_invalid_cost`, `error_invalid_margin`, `error_keyword`, `error_meta_description_length`, `error_meta_title_length`, `error_missing_movement_data`, `error_model_required`, `error_movement`, `error_movement_failed`, `error_name_required`, `error_notes`, `error_price_id`, `error_price_required`, `error_pricing_required`, `error_product`, `error_product_adjustment_failed`, `error_product_id`, `error_product_insufficient_stock`, `error_product_quantity_invalid`, `error_quantity`, `error_quantity_exceeded`, `error_quantity_invalid`, `error_quantity_required`, `error_reason_required`, `error_required_data`, `error_save_failed`, `error_sheet_creation`, `error_source_branch_required`, `error_supplier_required`, `error_transfer_failed`, `error_unique`, `error_unit`, `error_unit_required`, `error_upsell_product`, `error_upsell_unit`, `error_warning`, `help_bundle_and_discount`, `help_category`, `help_condition_value`, `help_direct_cost`, `help_download`, `help_ean`, `help_filter`, `help_isbn`, `help_jan`, `help_manufacturer`, `help_meta_description`, `help_meta_keyword`, `help_meta_title`, `help_minimum`, `help_mpn`, `help_points`, `help_priority`, `help_recommendation`, `help_related`, `help_rule_formula`, `help_rule_value`, `help_seo_permalink`, `help_sku`, `help_stock_status`, `help_tag`, `help_upc`, `tab_bundle`, `tab_cross_sell`, `tab_data`, `tab_dynamic_pricing`, `tab_general`, `tab_image`, `tab_inventory`, `tab_movement`, `tab_option`, `tab_orders`, `tab_pricing`, `tab_purchase`, `tab_recommendation`, `tab_units`, `tab_upsell`, `text_abc_analysis`, `text_access_level`, `text_accessibility_testing`, `text_accomplishment`, `text_account`, `text_accountability`, `text_accounting_impact`, `text_accounting_integration`, `text_accounts_payable`, `text_accounts_receivable`, `text_accuracy`, `text_achievement`, `text_action`, `text_activity_completion`, `text_actual_quantity`, `text_actualization`, `text_adaptability`, `text_add_stock`, `text_add_to_batch`, `text_add_transfer_items_first`, `text_additional_images`, `text_additional_images_details`, `text_additional_notes`, `text_additional_statistics`, `text_additional_unit`, `text_additional_unit_help`, `text_adjust_by_percentage`, `text_adjustment`, `text_adjustment_added`, `text_adjustment_applied`, `text_adjustment_decrease`, `text_adjustment_details`, `text_adjustment_fields_required`, `text_adjustment_help`, `text_adjustment_increase`, `text_adjustment_information`, `text_adjustment_preview`, `text_adjustment_reason`, `text_adjustment_saved`, `text_adjustment_saved_memory`, `text_adjustment_type`, `text_advanced_pricing`, `text_advanced_search`, `text_advancement`, `text_aesthetics`, `text_affiliate_program`, `text_agility`, `text_ai_recommendations`, `text_alerts_notifications`, `text_all`, `text_all_branches`, `text_all_categories`, `text_all_customers`, `text_all_types`, `text_all_units`, `text_amount`, `text_analytics_settings`, `text_anomaly_detection`, `text_api_management`, `text_apply_count`, `text_apply_filters`, `text_arabic_specific_1`, `text_arabic_specific_10`, `text_arabic_specific_100`, `text_arabic_specific_101`, `text_arabic_specific_102`, `text_arabic_specific_103`, `text_arabic_specific_104`, `text_arabic_specific_105`, `text_arabic_specific_106`, `text_arabic_specific_107`, `text_arabic_specific_108`, `text_arabic_specific_109`, `text_arabic_specific_11`, `text_arabic_specific_110`, `text_arabic_specific_111`, `text_arabic_specific_112`, `text_arabic_specific_113`, `text_arabic_specific_114`, `text_arabic_specific_115`, `text_arabic_specific_116`, `text_arabic_specific_117`, `text_arabic_specific_118`, `text_arabic_specific_119`, `text_arabic_specific_12`, `text_arabic_specific_120`, `text_arabic_specific_121`, `text_arabic_specific_122`, `text_arabic_specific_123`, `text_arabic_specific_124`, `text_arabic_specific_125`, `text_arabic_specific_126`, `text_arabic_specific_127`, `text_arabic_specific_128`, `text_arabic_specific_129`, `text_arabic_specific_13`, `text_arabic_specific_130`, `text_arabic_specific_131`, `text_arabic_specific_132`, `text_arabic_specific_133`, `text_arabic_specific_134`, `text_arabic_specific_135`, `text_arabic_specific_136`, `text_arabic_specific_137`, `text_arabic_specific_138`, `text_arabic_specific_139`, `text_arabic_specific_14`, `text_arabic_specific_140`, `text_arabic_specific_141`, `text_arabic_specific_142`, `text_arabic_specific_143`, `text_arabic_specific_144`, `text_arabic_specific_145`, `text_arabic_specific_146`, `text_arabic_specific_147`, `text_arabic_specific_148`, `text_arabic_specific_149`, `text_arabic_specific_15`, `text_arabic_specific_150`, `text_arabic_specific_151`, `text_arabic_specific_152`, `text_arabic_specific_153`, `text_arabic_specific_154`, `text_arabic_specific_155`, `text_arabic_specific_156`, `text_arabic_specific_157`, `text_arabic_specific_158`, `text_arabic_specific_159`, `text_arabic_specific_16`, `text_arabic_specific_160`, `text_arabic_specific_161`, `text_arabic_specific_162`, `text_arabic_specific_163`, `text_arabic_specific_164`, `text_arabic_specific_165`, `text_arabic_specific_166`, `text_arabic_specific_167`, `text_arabic_specific_168`, `text_arabic_specific_169`, `text_arabic_specific_17`, `text_arabic_specific_170`, `text_arabic_specific_171`, `text_arabic_specific_172`, `text_arabic_specific_173`, `text_arabic_specific_174`, `text_arabic_specific_175`, `text_arabic_specific_176`, `text_arabic_specific_177`, `text_arabic_specific_178`, `text_arabic_specific_179`, `text_arabic_specific_18`, `text_arabic_specific_180`, `text_arabic_specific_181`, `text_arabic_specific_182`, `text_arabic_specific_183`, `text_arabic_specific_184`, `text_arabic_specific_185`, `text_arabic_specific_186`, `text_arabic_specific_187`, `text_arabic_specific_188`, `text_arabic_specific_189`, `text_arabic_specific_19`, `text_arabic_specific_190`, `text_arabic_specific_2`, `text_arabic_specific_20`, `text_arabic_specific_21`, `text_arabic_specific_22`, `text_arabic_specific_23`, `text_arabic_specific_24`, `text_arabic_specific_25`, `text_arabic_specific_26`, `text_arabic_specific_27`, `text_arabic_specific_28`, `text_arabic_specific_29`, `text_arabic_specific_3`, `text_arabic_specific_30`, `text_arabic_specific_31`, `text_arabic_specific_32`, `text_arabic_specific_33`, `text_arabic_specific_34`, `text_arabic_specific_35`, `text_arabic_specific_36`, `text_arabic_specific_37`, `text_arabic_specific_38`, `text_arabic_specific_39`, `text_arabic_specific_4`, `text_arabic_specific_40`, `text_arabic_specific_41`, `text_arabic_specific_42`, `text_arabic_specific_43`, `text_arabic_specific_44`, `text_arabic_specific_45`, `text_arabic_specific_46`, `text_arabic_specific_47`, `text_arabic_specific_48`, `text_arabic_specific_49`, `text_arabic_specific_5`, `text_arabic_specific_50`, `text_arabic_specific_51`, `text_arabic_specific_52`, `text_arabic_specific_53`, `text_arabic_specific_54`, `text_arabic_specific_55`, `text_arabic_specific_56`, `text_arabic_specific_57`, `text_arabic_specific_58`, `text_arabic_specific_59`, `text_arabic_specific_6`, `text_arabic_specific_60`, `text_arabic_specific_61`, `text_arabic_specific_62`, `text_arabic_specific_63`, `text_arabic_specific_64`, `text_arabic_specific_65`, `text_arabic_specific_66`, `text_arabic_specific_67`, `text_arabic_specific_68`, `text_arabic_specific_69`, `text_arabic_specific_7`, `text_arabic_specific_70`, `text_arabic_specific_71`, `text_arabic_specific_72`, `text_arabic_specific_73`, `text_arabic_specific_74`, `text_arabic_specific_75`, `text_arabic_specific_76`, `text_arabic_specific_77`, `text_arabic_specific_78`, `text_arabic_specific_79`, `text_arabic_specific_8`, `text_arabic_specific_80`, `text_arabic_specific_81`, `text_arabic_specific_82`, `text_arabic_specific_83`, `text_arabic_specific_84`, `text_arabic_specific_85`, `text_arabic_specific_86`, `text_arabic_specific_87`, `text_arabic_specific_88`, `text_arabic_specific_89`, `text_arabic_specific_9`, `text_arabic_specific_90`, `text_arabic_specific_91`, `text_arabic_specific_92`, `text_arabic_specific_93`, `text_arabic_specific_94`, `text_arabic_specific_95`, `text_arabic_specific_96`, `text_arabic_specific_97`, `text_arabic_specific_98`, `text_arabic_specific_99`, `text_architecture`, `text_artificial_intelligence`, `text_assistance`, `text_audience_targeting`, `text_audit_trail`, `text_auto_backup`, `text_auto_refresh`, `text_auto_save_failed`, `text_automated_testing`, `text_automation`, `text_automation_opportunities`, `text_availability_testing`, `text_available`, `text_available_quantity`, `text_available_quantity_help`, `text_available_units`, `text_average_cost`, `text_average_cost_help`, `text_average_price`, `text_backup_recovery`, `text_balance_sheet`, `text_bandwidth_optimization`, `text_barcode_exists`, `text_barcode_format`, `text_barcode_generated`, `text_barcode_help`, `text_barcode_help_intro`, `text_barcode_help_tip`, `text_barcode_info`, `text_barcode_management`, `text_barcode_option`, `text_barcode_option_help`, `text_barcode_preview`, `text_barcode_preview_help`, `text_barcode_preview_placeholder`, `text_barcode_type`, `text_barcode_types`, `text_barcode_types_help`, `text_barcode_unit`, `text_barcode_unit_help`, `text_base_price`, `text_base_price_help`, `text_base_unit`, `text_base_unit_help`, `text_base_unit_info`, `text_base_unit_required`, `text_base_unit_warning`, `text_basic_data`, `text_basic_information`, `text_batch_adjustment`, `text_batch_adjustment_partial`, `text_batch_adjustment_success`, `text_batch_adjustment_title`, `text_batch_empty`, `text_beauty`, `text_behavioral_data`, `text_below_min_stock`, `text_best_practices`, `text_blog_integration`, `text_branch`, `text_branch_selection`, `text_browsing_history`, `text_budget_planning`, `text_bulk_cost_update`, `text_bulk_delete`, `text_bulk_disable`, `text_bulk_enable`, `text_bulk_operations`, `text_bulk_status_change`, `text_bulk_stock_update`, `text_bulk_update_category`, `text_bulk_update_price`, `text_bundle_discount`, `text_bundle_discount_types`, `text_bundle_discount_types_explanation`, `text_bundle_management`, `text_bundle_pricing`, `text_bundle_products`, `text_bundle_products_explanation`, `text_bundles`, `text_bundles_discounts_help`, `text_bundles_discounts_help_intro`, `text_bundles_discounts_help_tip`, `text_bundles_explanation`, `text_business_continuity`, `text_business_intelligence`, `text_business_ready`, `text_business_value`, `text_buy_x_get_discount`, `text_buy_x_get_y`, `text_cache_optimization`, `text_cache_status`, `text_cached_data`, `text_calculated_new_price`, `text_cannot_remove_base_unit`, `text_capability`, `text_capacity`, `text_capacity_planning`, `text_carbon_footprint`, `text_cart_abandonment`, `text_cash_flow`, `text_cashback_system`, `text_categories_filters`, `text_categories_filters_help`, `text_categorization`, `text_central_services`, `text_change_management`, `text_change_reason`, `text_chart_view`, `text_circular_economy`, `text_clear_filters`, `text_cloud_integration`, `text_clustering`, `text_code_quality`, `text_compatibility_testing`, `text_competency`, `text_competitive_advantage`, `text_competitor_price`, `text_complete_transfer`, `text_completion`, `text_complexity`, `text_compliance_standards`, `text_component_completion`, `text_configuration_completion`, `text_configuration_management`, `text_confirm_adjustment`, `text_confirm_cost_change`, `text_confirm_delete`, `text_connection_status`, `text_consignment`, `text_consistency`, `text_constitution_compliant`, `text_consumption`, `text_containerization`, `text_content_marketing`, `text_continuous_deployment`, `text_continuous_integration`, `text_contra_account`, `text_control`, `text_conversion_factor`, `text_conversion_factor_help`, `text_conversion_result`, `text_conversion_tracking`, `text_corporate_governance`, `text_cost`, `text_cost_accounting`, `text_cost_adjustment`, `text_cost_based_pricing`, `text_cost_change`, `text_cost_change_reason`, `text_cost_details`, `text_cost_help`, `text_cost_history`, `text_cost_history_help`, `text_cost_history_section`, `text_cost_impact`, `text_cost_must_be_positive`, `text_cost_of_goods_sold`, `text_cost_optimization`, `text_cost_trend`, `text_cost_updated`, `text_count`, `text_count_applied`, `text_count_notes`, `text_count_pending`, `text_count_sheet`, `text_count_status`, `text_count_type`, `text_counted_quantity`, `text_cpu_optimization`, `text_create_count_sheet`, `text_create_transfer`, `text_creativity`, `text_credit`, `text_cross_sell`, `text_cross_sell_help`, `text_cultural_adaptation`, `text_currency`, `text_currency_position`, `text_currency_settings`, `text_currency_symbol`, `text_current_base_price`, `text_current_cost`, `text_current_quantity`, `text_current_stock`, `text_current_total_value`, `text_current_unit_cost`, `text_custom_dimensions`, `text_custom_metrics`, `text_custom_price`, `text_custom_reason`, `text_customer_acquisition`, `text_customer_excellence`, `text_customer_group`, `text_customer_lifetime_value`, `text_customer_preferences`, `text_customer_ready`, `text_customer_retention`, `text_customer_satisfaction`, `text_customers_also_viewed`, `text_dashboard_integration`, `text_data_correction`, `text_data_help`, `text_data_help_intro`, `text_data_help_tip`, `text_data_integration`, `text_data_integrity`, `text_data_layer`, `text_data_mining`, `text_data_protection`, `text_data_source`, `text_data_validation`, `text_data_warehousing`, `text_database_optimization`, `text_date_added`, `text_date_format`, `text_date_modified`, `text_date_range`, `text_debit`, `text_decimal_places`, `text_decimal_separator`, `text_decrease`, `text_decrease_by`, `text_default`, `text_deliverable_completion`, `text_delivery`, `text_delivery_completion`, `text_demand_prediction`, `text_demographic_data`, `text_dependability`, `text_deployment`, `text_deployment_completion`, `text_deployment_ready`, `text_design`, `text_destination_branch`, `text_detailed_notes`, `text_detailed_view`, `text_development`, `text_devops_integration`, `text_difference`, `text_digital_transformation`, `text_digitalization`, `text_dimensions_weight`, `text_dimensions_weight_details`, `text_dimensions_weight_help`, `text_direction`, `text_disaster_recovery`, `text_discount_period`, `text_discount_period_explanation`, `text_discount_types`, `text_discount_types_explanation`, `text_discounts`, `text_discounts_explanation`, `text_dynamic_pricing`, `text_edit_cost`, `text_education`, `text_effectiveness`, `text_efficiency`, `text_elegance`, `text_email_marketing`, `text_email_report`, `text_employee_excellence`, `text_energy_efficiency`, `text_enhanced_ecommerce`, `text_enhancement`, `text_enhancement_ready`, `text_enter_margin_percentage`, `text_enter_positive_value`, `text_enter_valid_cost`, `text_enter_valid_margin`, `text_enterprise_features`, `text_enterprise_grade_plus`, `text_enterprise_ready`, `text_environment_management`, `text_environmental_impact`, `text_equity_account`, `text_error_deleting`, `text_error_loading`, `text_error_loading_product`, `text_error_logs`, `text_error_saving`, `text_ethical_business`, `text_etl_processes`, `text_event_tracking`, `text_evolution`, `text_excel_sheet`, `text_excellence`, `text_execution`, `text_expansion_ready`, `text_experience`, `text_expertise`, `text_export`, `text_export_excel`, `text_export_filtered`, `text_export_pdf`, `text_export_success`, `text_feature_completion`, `text_feedback_system`, `text_field_validation`, `text_filter`, `text_filter_applied`, `text_filter_by_branch`, `text_filter_by_category`, `text_filter_by_date`, `text_filter_by_manufacturer`, `text_filter_by_status`, `text_filter_by_type`, `text_filter_by_unit`, `text_filter_reset`, `text_financial_forecasting`, `text_financial_impact`, `text_financial_reporting`, `text_fixed`, `text_flexibility`, `text_formula`, `text_fraud_detection`, `text_free`, `text_free_product`, `text_frequently_bought_together`, `text_from_date`, `text_from_unit`, `text_fulfillment`, `text_full_count`, `text_fully_integrated`, `text_function_completion`, `text_future`, `text_future_ready`, `text_gdpr_compliance`, `text_general_help_intro`, `text_general_help_tip`, `text_general_info_help`, `text_general_ledger`, `text_general_settings`, `text_generate_barcode`, `text_generated_successfully`, `text_gl_account_impact`, `text_globalization_testing`, `text_go_live_completion`, `text_goal`, `text_goal_tracking`, `text_goods_receipt`, `text_governance`, `text_green_technology`, `text_growth_ready`, `text_guidance`, `text_guidance_support`, `text_half_wholesale_price`, `text_has_expired`, `text_height`, `text_help`, `text_high_availability`, `text_hybrid_deployment`, `text_id`, `text_identifiers`, `text_identifiers_help`, `text_image_quality`, `text_image_size`, `text_images_help`, `text_images_help_intro`, `text_images_help_tip`, `text_implementation`, `text_implementation_ready`, `text_implementation_success`, `text_import`, `text_improvement`, `text_incident_response`, `text_include_price`, `text_income_statement`, `text_increase`, `text_increase_by`, `text_index_optimization`, `text_individual_labels`, `text_industry_standards`, `text_initial_stock`, `text_initialization_completion`, `text_innovation`, `text_innovation_management`, `text_installation_completion`, `text_insufficient_stock`, `text_insufficient_stock_warning`, `text_integration_apis`, `text_integration_ready`, `text_integrity`, `text_intelligence`, `text_internal_controls`, `text_internationalization`, `text_inventory`, `text_inventory_account`, `text_inventory_adjustment_account`, `text_inventory_count`, `text_inventory_details`, `text_inventory_help`, `text_inventory_help_intro`, `text_inventory_help_tip`, `text_inventory_info`, `text_inventory_levels`, `text_inventory_loss_account`, `text_inventory_operations`, `text_inventory_quantity`, `text_inventory_quantity_help`, `text_inventory_report`, `text_inventory_settings`, `text_inventory_statistics`, `text_inventory_suggestions`, `text_inventory_transfer`, `text_inventory_valuation`, `text_inventory_valuation_account`, `text_invoice_matching`, `text_iso_standards`, `text_items_to_adjust`, `text_keyword`, `text_knowledge`, `text_language_settings`, `text_last_updated`, `text_latency_optimization`, `text_launch_completion`, `text_leadership`, `text_learning`, `text_length`, `text_list`, `text_load_balancing`, `text_load_testing`, `text_load_time`, `text_loading`, `text_local_regulations`, `text_locale_settings`, `text_localization_testing`, `text_low_stock`, `text_loyalty_program`, `text_machine_learning`, `text_main_image`, `text_main_image_details`, `text_main_image_help`, `text_maintainability_testing`, `text_maintenance`, `text_manage_columns`, `text_management`, `text_management_accounting`, `text_manifestation`, `text_manual_cost_update`, `text_margin`, `text_margin_calculation_help`, `text_market_positioning`, `text_market_price_change`, `text_market_ready`, `text_marketing_suggestions`, `text_materialization`, `text_memory_optimization`, `text_meta_data`, `text_meta_data_help`, `text_meta_settings`, `text_methodology_compliance`, `text_microservices`, `text_migration_ready`, `text_milestone_achievement`, `text_min`, `text_minus`, `text_mission`, `text_mission_critical`, `text_mobile_optimized`, `text_mobile_sheet`, `text_modal_title_adjustment`, `text_modal_title_barcode`, `text_modal_title_count_sheet`, `text_modal_title_dynamic_pricing`, `text_modal_title_inventory`, `text_modal_title_supplier_price`, `text_modal_title_transfer`, `text_modernization`, `text_module_completion`, `text_monitoring`, `text_movement_added`, `text_movement_by_type`, `text_movement_details`, `text_movement_filter`, `text_movement_filters`, `text_movement_filters_help`, `text_movement_frequency`, `text_movement_help`, `text_movement_help_intro`, `text_movement_help_tip`, `text_movement_history`, `text_movement_info`, `text_movement_reason`, `text_movement_stats`, `text_movement_stats_help`, `text_movement_success`, `text_movement_tracking`, `text_movement_type`, `text_movement_types`, `text_movement_types_help`, `text_movements`, `text_multi_language`, `text_multi_unit_system`, `text_na`, `text_net_change`, `text_network_optimization`, `text_new_cost`, `text_new_quantity`, `text_new_total_value`, `text_new_unit_cost`, `text_news_integration`, `text_no`, `text_no_accounting_impact`, `text_no_barcode`, `text_no_branches_available`, `text_no_change_needed`, `text_no_cost_history`, `text_no_inventory`, `text_no_inventory_data`, `text_no_movements`, `text_no_option`, `text_no_orders`, `text_no_price_history`, `text_no_purchase_history`, `text_no_recent_movements`, `text_no_results`, `text_no_supplier_pricing`, `text_no_units`, `text_no_units_selected`, `text_none`, `text_note`, `text_notes`, `text_notification_settings`, `text_number_format`, `text_objective`, `text_operation`, `text_operation_completion`, `text_operational_excellence`, `text_opportunity`, `text_optimization`, `text_optimization_ready`, `text_option`, `text_option_inventory`, `text_option_inventory_help`, `text_option_pricing`, `text_option_pricing_help`, `text_option_types`, `text_option_types_help`, `text_option_unit`, `text_option_unit_help`, `text_option_value`, `text_option_values`, `text_option_values_help`, `text_optional`, `text_options_help`, `text_options_help_intro`, `text_options_help_tip`, `text_orchestration`, `text_order_analysis`, `text_order_frequency`, `text_order_statistics`, `text_order_statistics_help`, `text_order_statistics_section`, `text_orders`, `text_orders_help`, `text_orders_help_intro`, `text_orders_help_tip`, `text_orders_info`, `text_orders_list_help`, `text_orders_list_section`, `text_organization`, `text_other_reason`, `text_oversight`, `text_page_size`, `text_partial_count`, `text_partner_excellence`, `text_partner_program`, `text_pattern_recognition`, `text_payment_processing`, `text_penetration_testing`, `text_percent`, `text_percentage`, `text_performance`, `text_performance_metrics`, `text_performance_optimization`, `text_performance_report`, `text_performance_testing`, `text_periodic_count`, `text_permission_matrix`, `text_phase_completion`, `text_pixel_tracking`, `text_planning`, `text_plus`, `text_poll_system`, `text_possibility`, `text_potential`, `text_precision`, `text_predictability`, `text_predictive_analytics`, `text_preview_journal_entry`, `text_price`, `text_price_added`, `text_price_applied`, `text_price_calculator`, `text_price_calculator_help`, `text_price_deleted`, `text_price_display`, `text_price_history`, `text_price_updated`, `text_prices_updated`, `text_pricing_help`, `text_pricing_help_intro`, `text_pricing_help_tip`, `text_pricing_info`, `text_pricing_suggestions`, `text_print_barcodes`, `text_print_report`, `text_printed_sheet`, `text_procedure_completion`, `text_process_adjustments`, `text_process_completion`, `text_process_improvement`, `text_procurement_system`, `text_product_added_to_batch`, `text_product_completion`, `text_product_description`, `text_product_description_help`, `text_product_excellence`, `text_product_info`, `text_product_name`, `text_product_name_help`, `text_production`, `text_production_account`, `text_production_ready`, `text_productivity`, `text_profit_margin`, `text_profit_margin_help`, `text_profit_margin_percentage`, `text_progress`, `text_project_completion`, `text_promotional_pricing`, `text_prospect`, `text_purchase`, `text_purchase_help`, `text_purchase_help_intro`, `text_purchase_help_tip`, `text_purchase_history`, `text_purchase_history_help`, `text_purchase_history_section`, `text_purchase_info`, `text_purchase_orders`, `text_purpose`, `text_push_notifications`, `text_quality`, `text_quality_assurance`, `text_quantity`, `text_quantity_change`, `text_quantity_exceeds_available`, `text_quantity_must_be_positive`, `text_quantity_note`, `text_quantity_on_hand`, `text_quantity_per_product`, `text_quantity_to_add`, `text_quantity_to_remove`, `text_query_optimization`, `text_quick_actions`, `text_quick_adjustment`, `text_quick_adjustment_help`, `text_quick_count`, `text_quick_decrease`, `text_quick_increase`, `text_quick_info`, `text_quick_stats`, `text_quick_stock_adjustment`, `text_quiz_system`, `text_rating`, `text_rating_system`, `text_real_time`, `text_realization`, `text_reason_adjustment`, `text_reason_correction`, `text_reason_customer_returns`, `text_reason_damaged`, `text_reason_damaged_goods`, `text_reason_expired`, `text_reason_expired_goods`, `text_reason_initial`, `text_reason_initial_stock`, `text_reason_inventory_correction`, `text_reason_manual`, `text_reason_other`, `text_reason_production`, `text_reason_purchase`, `text_reason_stock_count`, `text_reason_transfer`, `text_recent_activities`, `text_recent_movements`, `text_recommendation_discount`, `text_recommendation_discount_help`, `text_recommendation_help`, `text_recommendation_help_intro`, `text_recommendation_help_tip`, `text_recommendation_priority`, `text_recommendation_priority_help`, `text_recommendation_unit`, `text_recommendation_unit_help`, `text_recommended_products`, `text_recovery_point`, `text_referral_program`, `text_refresh_data`, `text_regional_compliance`, `text_regulatory_compliance`, `text_related_products`, `text_release_completion`, `text_release_management`, `text_reliability`, `text_reliability_testing`, `text_remarketing`, `text_remove_stock`, `text_report_settings`, `text_reports`, `text_required_fields`, `text_resilience`, `text_resource_conservation`, `text_resource_optimization`, `text_response_time`, `text_responsive_design`, `text_responsiveness`, `text_rest_api`, `text_return_in`, `text_return_on_investment`, `text_return_out`, `text_review_system`, `text_reward_points`, `text_risk_management`, `text_robots_settings`, `text_robustness`, `text_role_management`, `text_rollback_support`, `text_rollout_completion`, `text_rtl_support`, `text_sale`, `text_sales_history`, `text_sales_trend`, `text_sales_trend_help`, `text_sales_trend_section`, `text_same_branch_error`, `text_save_and_close`, `text_save_and_copy`, `text_save_and_new`, `text_saving`, `text_scalability`, `text_scalability_testing`, `text_scaling_ready`, `text_schedule_report`, `text_scrap`, `text_search_by_barcode`, `text_search_by_category`, `text_search_by_manufacturer`, `text_search_by_price_range`, `text_search_by_stock_level`, `text_search_history`, `text_search_products`, `text_seasonal_trends`, `text_security_audit`, `text_security_level`, `text_security_monitoring`, `text_security_standards`, `text_security_testing`, `text_select`, `text_select_branch`, `text_select_branch_first`, `text_select_destination_branch_first`, `text_select_one_product`, `text_select_product`, `text_select_products_first`, `text_select_reason_first`, `text_select_source_branch_first`, `text_select_status`, `text_select_unit`, `text_select_unit_first`, `text_seo_permalink`, `text_seo_settings`, `text_seo_url`, `text_seo_url_help`, `text_service`, `text_service_completion`, `text_service_excellence`, `text_session_info`, `text_set_cost`, `text_set_quantity`, `text_setup_completion`, `text_shareholder_value`, `text_sheet_created`, `text_sheet_format`, `text_sheet_labels`, `text_simplicity`, `text_sitemap_settings`, `text_skill`, `text_sms_marketing`, `text_social_media`, `text_social_responsibility`, `text_solution_completion`, `text_sophistication`, `text_sort_order`, `text_sort_order_details`, `text_source_branch`, `text_special_price`, `text_special_price_help`, `text_specify_reason`, `text_stability`, `text_stage_completion`, `text_stakeholder_value`, `text_startup_completion`, `text_step_completion`, `text_stock`, `text_stock_by_branch`, `text_stock_count`, `text_stock_display`, `text_stock_level`, `text_stock_movements`, `text_stock_options`, `text_stock_options_help`, `text_stock_trend`, `text_stock_value_change`, `text_storage_optimization`, `text_strategic_alignment`, `text_strategy`, `text_stress_testing`, `text_structure`, `text_success`, `text_success_operation`, `text_success_update`, `text_summary_view`, `text_supervision`, `text_supplier_portal`, `text_supplier_price_change`, `text_supplier_price_modal`, `text_supplier_pricing_help`, `text_supplier_pricing_section`, `text_support`, `text_survey_system`, `text_sustainability`, `text_sustainable_development`, `text_sync_status`, `text_system_completion`, `text_system_health`, `text_system_quantity`, `text_table_view`, `text_tag_manager`, `text_target`, `text_task_completion`, `text_tax_display`, `text_tax_reporting`, `text_technology_adoption`, `text_theme_settings`, `text_third_party_integration`, `text_thousand_separator`, `text_throughput_optimization`, `text_thumbnail_size`, `text_tier_pricing`, `text_time_format`, `text_time_period`, `text_timezone_settings`, `text_to_date`, `text_to_unit`, `text_total_cost_ownership`, `text_total_incoming`, `text_total_outgoing`, `text_total_revenue`, `text_total_sold`, `text_total_spent`, `text_totals`, `text_touch_friendly`, `text_tracking_code`, `text_training`, `text_transaction_support`, `text_transfer`, `text_transfer_added`, `text_transfer_completed`, `text_transfer_in`, `text_transfer_items`, `text_transfer_notes`, `text_transfer_out`, `text_transfer_pending`, `text_transfer_status`, `text_transformation`, `text_translation_management`, `text_transparency`, `text_trial_balance`, `text_triumph`, `text_trust`, `text_type_to_search`, `text_unit`, `text_unit_conversion`, `text_unit_conversion_info`, `text_unit_converter`, `text_unit_converter_help`, `text_unit_inventory`, `text_unit_pricing`, `text_units_help`, `text_units_help_intro`, `text_units_info`, `text_unknown`, `text_unsaved_changes`, `text_unsaved_changes_warning`, `text_update_based_on_new_cost`, `text_update_cost`, `text_update_prices_confirm`, `text_update_sales_prices`, `text_update_selling_prices`, `text_upgrade_ready`, `text_upsell`, `text_upsell_help`, `text_usability_testing`, `text_user_activity`, `text_user_preferences`, `text_user_ready`, `text_valuation_report`, `text_value_change`, `text_value_proposition`, `text_vendor_management`, `text_version_control`, `text_victory`, `text_view_all_movements`, `text_view_inventory`, `text_views`, `text_vision`, `text_vulnerability_scanning`, `text_wac_enabled`, `text_wac_explanation`, `text_warning_insufficient_stock`, `text_waste_reduction`, `text_watermark_settings`, `text_webhook_support`, `text_weighted_average_cost`, `text_weighted_average_cost_calculator`, `text_what_are_bundles`, `text_what_are_discounts`, `text_wholesale_price`, `text_wholesale_price_help`, `text_width`, `text_wishlist_data`, `text_workflow_completion`, `text_workflow_optimization`, `text_yes`

---

### 🛡️ COMPREHENSIVE SECURITY ANALYSIS

#### 📊 Security Overview
- **Overall Score:** 91%
- **Security Level:** EXCELLENT
- **Total Vulnerabilities:** 1
- **Critical Vulnerabilities:** 1
- **High Risk Vulnerabilities:** 0

#### 🔍 Security Categories Analysis

##### ❌ Sql Injection
- **Status:** VULNERABLE
- **Risk Score:** 25%
- **Vulnerabilities:** 1
- **Issues Found:**
  - Potential SQL injection vulnerability detected

##### ✅ Xss Protection
- **Status:** SAFE
- **Risk Score:** 15%
- **Vulnerabilities:** 0

##### ✅ Csrf Protection
- **Status:** SAFE
- **Risk Score:** 0%
- **Vulnerabilities:** 0

##### ✅ File Inclusion
- **Status:** SAFE
- **Risk Score:** 0%
- **Vulnerabilities:** 0

##### ✅ Command Injection
- **Status:** SAFE
- **Risk Score:** 0%
- **Vulnerabilities:** 0

##### ❌ Authentication
- **Status:** NONE
- **Risk Score:** 50%
- **Vulnerabilities:** 0

##### ❌ Authorization
- **Status:** IMPLEMENTED
- **Risk Score:** 15%
- **Vulnerabilities:** 0

##### ✅ Session Security
- **Status:** SAFE
- **Risk Score:** 0%
- **Vulnerabilities:** 0

##### ✅ Input Validation
- **Status:** SAFE
- **Risk Score:** 0%
- **Vulnerabilities:** 0

##### ✅ Output Encoding
- **Status:** SAFE
- **Risk Score:** 0%
- **Vulnerabilities:** 0

##### ✅ File Upload
- **Status:** SAFE
- **Risk Score:** 0%
- **Vulnerabilities:** 0

##### ✅ Cryptography
- **Status:** SAFE
- **Risk Score:** 0%
- **Vulnerabilities:** 0

##### ✅ Error Handling
- **Status:** SAFE
- **Risk Score:** 0%
- **Vulnerabilities:** 0

##### ✅ Configuration
- **Status:** SAFE
- **Risk Score:** 0%
- **Vulnerabilities:** 0

##### ✅ Api Security
- **Status:** SAFE
- **Risk Score:** 0%
- **Vulnerabilities:** 0

##### ✅ Data Protection
- **Status:** SAFE
- **Risk Score:** 0%
- **Vulnerabilities:** 0

#### 🎯 Threat Assessment
- **Threat Level:** CRITICAL
- **Business Impact:** SEVERE
- **Attack Vectors:** 1

---

### ⚡ COMPREHENSIVE PERFORMANCE ANALYSIS

#### 📊 Performance Overview
- **Overall Score:** 5%
- **Bottlenecks Detected:** 2
- **Optimization Opportunities:** 0

#### 🔍 Performance Categories

##### 💾 Database Performance
- **Query Count:** 16
- **Optimization Score:** 80%
- **N+1 Query Risks:** 0

##### 🧠 Memory Performance
- **Memory-Intensive Operations:** 6
- **Optimization Score:** 10%

##### 🚀 Caching Analysis
- **Cacheable Operations:** 0
- **Existing Caching:** 0
- **Potential Improvement:** 0%

---

### 🚨 CRITICAL ISSUES REQUIRING IMMEDIATE ACTION (4)


#### 1. 🔴 Constitutional Compliance
- **Type:** CONSTITUTIONAL_VIOLATION
- **Severity:** CRITICAL
- **Description:** All tables must start with cod_ prefix
- **Impact:** Database conflicts and system instability
- **Fix Priority:** 1


#### 2. 🔴 Constitutional Compliance
- **Type:** CONSTITUTIONAL_VIOLATION
- **Severity:** CRITICAL
- **Description:** Must sanitize all outputs
- **Impact:** XSS attacks and data corruption
- **Fix Priority:** 1


#### 3. 🔴 Security
- **Type:** SECURITY_VULNERABILITY
- **Severity:** CRITICAL
- **Description:** Potential SQL injection vulnerability detected
- **Impact:** Database compromise, data theft, unauthorized access
- **Fix Priority:** 1


#### 4. 🟡 Internationalization
- **Type:** LANGUAGE_MISMATCH
- **Severity:** HIGH
- **Description:** Significant language variable mismatches
- **Impact:** Broken user interface and poor user experience
- **Fix Priority:** 2


---

### 💡 COMPREHENSIVE RECOMMENDATIONS


#### Constitutional Compliance
- **MEDIUM:** Use cod_ prefix for all custom tables
- **MEDIUM:** Add output sanitization using htmlspecialchars()
- **MEDIUM:** Create view file

#### Security Analysis
- **MEDIUM:** Use role-based access control (RBAC)
- **MEDIUM:** Consider taking system offline until fixes are applied
- **MEDIUM:** Avoid hardcoded permissions
- **MEDIUM:** Implement input validation and sanitization
- **MEDIUM:** Apply the principle of least privilege for database access
- **MEDIUM:** Conduct thorough security audit
- **MEDIUM:** Implement emergency incident response procedures
- **MEDIUM:** Use parameterized queries instead of string concatenation
- **MEDIUM:** Implement principle of least privilege
- **MEDIUM:** Use prepared statements for all database queries

#### Performance Analysis
- **MEDIUM:** Reduce the number of database queries
- **MEDIUM:** Implement query batching where possible
- **MEDIUM:** Optimize memory-intensive operations
- **MEDIUM:** Consider streaming for large data processing
- **MEDIUM:** Profile application performance regularly
- **MEDIUM:** Monitor database query performance
- **MEDIUM:** Implement proper error handling and logging
- **MEDIUM:** Use appropriate data structures and algorithms

---

### 🔧 DETAILED FIX INSTRUCTIONS

#### 🚨 Immediate Actions (Do First)
- **Issue:** All tables must start with cod_ prefix
  **Fix:** Fix CONSTITUTIONAL_VIOLATION immediately
  **Time:** 1-2 hours

- **Issue:** Must sanitize all outputs
  **Fix:** Fix CONSTITUTIONAL_VIOLATION immediately
  **Time:** 1-2 hours

- **Issue:** Potential SQL injection vulnerability detected
  **Fix:** Fix SECURITY_VULNERABILITY immediately
  **Time:** 1-2 hours

#### ⏰ Short-term Fixes (This Week)
- **Issue:** All tables must start with cod_ prefix
  **Fix:** Use: DB_PREFIX . "table_name" or ensure cod_ prefix
  **Time:** 30 minutes

- **Issue:** Must follow complete MVC structure
  **Fix:** Create missing files: model, view, language
  **Time:** 30 minutes

- **Issue:** Must sanitize all outputs
  **Fix:** Add: htmlspecialchars() for all outputs
  **Time:** 30 minutes


---

### 💻 CODE EXAMPLES FOR FIXES

#### Fix Database Prefix

**Before (Problematic Code):**
```php
// Current problematic code
// All tables must start with cod_ prefix
```

**After (Fixed Code):**
```php
// Fixed code
Use: DB_PREFIX . "table_name" or ensure cod_ prefix
```

#### Fix Mvc Structure

**Before (Problematic Code):**
```php
// Current problematic code
// Must follow complete MVC structure
```

**After (Fixed Code):**
```php
// Fixed code
Create missing files: model, view, language
```

#### Fix Output Sanitization

**Before (Problematic Code):**
```php
// Current problematic code
// Must sanitize all outputs
```

**After (Fixed Code):**
```php
// Fixed code
Add: htmlspecialchars() for all outputs
```

#### Fix Missing Language Variables

**Before (Problematic Code):**
```php
// Missing language variables cause errors
```

**After (Fixed Code):**
```php
$_['accounts/accounting'] = '';  // TODO: Arabic translation
$_['catalog/product'] = '';  // TODO: Arabic translation
$_['common/header'] = '';  // TODO: Arabic translation
$_['error_insufficient_stock_for_product'] = '';  // TODO: Arabic translation
$_['error_insufficient_stock_for_transfer'] = '';  // TODO: Arabic translation
```


---

### 📋 STEP-BY-STEP IMPLEMENTATION GUIDE

#### Step 1: 🔴 Fix Critical Issues
- **Description:** Address 4 critical issues immediately
- **Estimated Time:** 120 minutes
- **Priority:** CRITICAL

#### Step 3: 🟡 Synchronize Language Files
- **Description:** Add 28 missing language variables
- **Estimated Time:** 56 minutes
- **Priority:** HIGH


---

### 🎯 FINAL SUMMARY AND NEXT STEPS

#### Immediate Actions Required
1. **Fix 3 Critical Issues** - These can break the system
2. **Address 1 High Priority Items** - Important for stability
3. **Review 0 Medium Priority Items** - Good for optimization

#### Success Criteria
- Health Score should reach 95%+
- Zero critical security vulnerabilities
- Complete constitutional compliance
- Full language file synchronization

#### Quality Metrics Summary

| Category | Score | Status |
|----------|-------|--------|
| Constitutional Compliance | 85% | PASS |
| Security | 91% | PASS |
| Language Integration | 0.0% | FAIL |
| Performance | 5% | FAIL |
| MVC Architecture | 93% | PASS |
| **OVERALL HEALTH** | **18%** | **NEEDS WORK** |

#### Global Progress
- **Screens Analyzed:** 56/446
- **Total Critical Issues:** 89
- **Total Security Vulnerabilities:** 43
- **Total Language Mismatches:** 39

---

### 📈 ANALYSIS STATISTICS

- **Total Lines Analyzed:** 1,893
- **Functions Analyzed:** 41
- **Variables Analyzed:** 60
- **Security Checks:** 16
- **Constitutional Rules:** 20
- **Performance Metrics:** 2

---

*Generated by AYM ERP Ultimate Auditor V9.0 - 2025-07-24 06:50:57*
*Analysis ID: 23bea125*
*This report contains 5000+ lines of comprehensive analysis and guidance*
*Enhanced with advanced language analysis from lang_comparison_script.py*
*Complete constitutional compliance checking with 20+ rules*
*Comprehensive security analysis with threat assessment*
*Performance optimization with bottleneck detection*
*Enterprise-grade quality metrics and recommendations*

**🎉 Thank you for using AYM ERP Ultimate Auditor V9.0!**
