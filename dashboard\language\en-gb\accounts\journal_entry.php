<?php
/**
 * English Language File - Journal Entry
 * 
 * @package    AYM ERP
 * <AUTHOR> Development Team
 * @copyright  2025 AYM ERP Systems
 * @license    Commercial License
 * @version    1.0.0
 * @since      2025-07-15
 */

// Heading
$_['heading_title'] = 'Journal Entry';

// Text
$_['text_success'] = 'Success: You have modified journal entries!';
$_['text_list'] = 'Journal Entry List';
$_['text_add'] = 'Add Journal Entry';
$_['text_edit'] = 'Edit Journal Entry';
$_['text_default'] = 'Default';
$_['text_enabled'] = 'Enabled';
$_['text_disabled'] = 'Disabled';
$_['text_yes'] = 'Yes';
$_['text_no'] = 'No';
$_['text_none'] = 'None';
$_['text_select'] = 'Select';
$_['text_home'] = 'Home';
$_['text_loading'] = 'Loading...';
$_['text_confirm'] = 'Are you sure?';
$_['text_no_results'] = 'No journal entries found!';

// Journal Entry Specific
$_['text_journal_entry'] = 'Journal Entry';
$_['text_journal_entries'] = 'Journal Entries';
$_['text_new_entry'] = 'New Entry';
$_['text_draft'] = 'Draft';
$_['text_posted'] = 'Posted';
$_['text_approved'] = 'Approved';
$_['text_rejected'] = 'Rejected';
$_['text_pending'] = 'Pending';
$_['text_auto_balance'] = 'Auto Balance';
$_['text_manual_entry'] = 'Manual Entry';
$_['text_template_entry'] = 'Template Entry';
$_['text_recurring_entry'] = 'Recurring Entry';

// Columns
$_['column_entry_id'] = 'Entry ID';
$_['column_entry_number'] = 'Entry Number';
$_['column_date'] = 'Date';
$_['column_reference'] = 'Reference';
$_['column_description'] = 'Description';
$_['column_total_debit'] = 'Total Debit';
$_['column_total_credit'] = 'Total Credit';
$_['column_status'] = 'Status';
$_['column_created_by'] = 'Created By';
$_['column_action'] = 'Action';
$_['column_account'] = 'Account';
$_['column_debit'] = 'Debit';
$_['column_credit'] = 'Credit';
$_['column_notes'] = 'Notes';

// Entry Fields
$_['entry_date'] = 'Entry Date';
$_['entry_reference'] = 'Reference';
$_['entry_description'] = 'Description';
$_['entry_notes'] = 'Notes';
$_['entry_status'] = 'Status';
$_['entry_account'] = 'Account';
$_['entry_debit'] = 'Debit Amount';
$_['entry_credit'] = 'Credit Amount';
$_['entry_line_description'] = 'Line Description';
$_['entry_cost_center'] = 'Cost Center';
$_['entry_project'] = 'Project';
$_['entry_department'] = 'Department';

// Buttons
$_['button_add'] = 'Add';
$_['button_edit'] = 'Edit';
$_['button_delete'] = 'Delete';
$_['button_save'] = 'Save';
$_['button_cancel'] = 'Cancel';
$_['button_post'] = 'Post';
$_['button_approve'] = 'Approve';
$_['button_reject'] = 'Reject';
$_['button_print'] = 'Print';
$_['button_export'] = 'Export';
$_['button_copy'] = 'Copy';
$_['button_reverse'] = 'Reverse';
$_['button_add_line'] = 'Add Line';
$_['button_remove_line'] = 'Remove Line';
$_['button_auto_balance'] = 'Auto Balance';
$_['button_validate'] = 'Validate';

// Tabs
$_['tab_general'] = 'General';
$_['tab_lines'] = 'Entry Lines';
$_['tab_attachments'] = 'Attachments';
$_['tab_approval'] = 'Approval';
$_['tab_audit'] = 'Audit Trail';

// Help
$_['help_reference'] = 'Enter a unique reference for this journal entry';
$_['help_description'] = 'Enter a description for this journal entry';
$_['help_auto_balance'] = 'Automatically balance the entry by adding a balancing line';
$_['help_cost_center'] = 'Select a cost center for tracking purposes';
$_['help_project'] = 'Select a project for tracking purposes';

// Errors
$_['error_permission'] = 'Warning: You do not have permission to modify journal entries!';
$_['error_date'] = 'Entry date is required!';
$_['error_reference'] = 'Reference is required!';
$_['error_description'] = 'Description is required!';
$_['error_lines'] = 'At least one entry line is required!';
$_['error_balance'] = 'Entry is not balanced! Total debits must equal total credits.';
$_['error_account'] = 'Account is required for line %d!';
$_['error_amount'] = 'Either debit or credit amount is required for line %d!';
$_['error_both_amounts'] = 'Cannot have both debit and credit amounts on the same line!';
$_['error_zero_amount'] = 'Amount cannot be zero!';
$_['error_negative_amount'] = 'Amount cannot be negative!';
$_['error_duplicate_reference'] = 'Reference already exists!';
$_['error_posted_entry'] = 'Cannot modify posted entries!';
$_['error_approved_entry'] = 'Cannot modify approved entries!';
$_['error_delete_posted'] = 'Cannot delete posted entries!';

// Success Messages
$_['text_entry_saved'] = 'Journal entry saved successfully!';
$_['text_entry_posted'] = 'Journal entry posted successfully!';
$_['text_entry_approved'] = 'Journal entry approved successfully!';
$_['text_entry_rejected'] = 'Journal entry rejected successfully!';
$_['text_entry_deleted'] = 'Journal entry deleted successfully!';
$_['text_entry_copied'] = 'Journal entry copied successfully!';
$_['text_entry_reversed'] = 'Journal entry reversed successfully!';

// Validation Messages
$_['text_balanced'] = 'Entry is balanced';
$_['text_unbalanced'] = 'Entry is not balanced';
$_['text_difference'] = 'Difference: %s';
$_['text_total_debits'] = 'Total Debits: %s';
$_['text_total_credits'] = 'Total Credits: %s';

// Status Messages
$_['text_status_draft'] = 'Draft';
$_['text_status_posted'] = 'Posted';
$_['text_status_approved'] = 'Approved';
$_['text_status_rejected'] = 'Rejected';
$_['text_status_pending'] = 'Pending Approval';

// Filters
$_['text_filter'] = 'Filter';
$_['text_all_statuses'] = 'All Statuses';
$_['text_date_from'] = 'Date From';
$_['text_date_to'] = 'Date To';
$_['text_search'] = 'Search';
$_['text_clear'] = 'Clear';
$_['text_apply'] = 'Apply';

// Pagination
$_['text_pagination'] = 'Showing %d to %d of %d (%d Pages)';
$_['text_first'] = 'First';
$_['text_last'] = 'Last';
$_['text_next'] = 'Next';
$_['text_prev'] = 'Previous';

// Export
$_['text_export_excel'] = 'Export to Excel';
$_['text_export_pdf'] = 'Export to PDF';
$_['text_export_csv'] = 'Export to CSV';

// Print
$_['text_print_entry'] = 'Print Entry';
$_['text_print_preview'] = 'Print Preview';

// Templates
$_['text_save_as_template'] = 'Save as Template';
$_['text_load_template'] = 'Load Template';
$_['text_template_name'] = 'Template Name';

// Recurring
$_['text_make_recurring'] = 'Make Recurring';
$_['text_recurring_frequency'] = 'Frequency';
$_['text_recurring_end_date'] = 'End Date';

// Approval Workflow
$_['text_submit_for_approval'] = 'Submit for Approval';
$_['text_approval_required'] = 'Approval Required';
$_['text_approved_by'] = 'Approved By';
$_['text_approval_date'] = 'Approval Date';
$_['text_approval_notes'] = 'Approval Notes';

// Audit Trail
$_['text_created_by'] = 'Created By';
$_['text_created_date'] = 'Created Date';
$_['text_modified_by'] = 'Modified By';
$_['text_modified_date'] = 'Modified Date';
$_['text_posted_by'] = 'Posted By';
$_['text_posted_date'] = 'Posted Date';

// Attachments
$_['text_attachments'] = 'Attachments';
$_['text_add_attachment'] = 'Add Attachment';
$_['text_file_name'] = 'File Name';
$_['text_file_size'] = 'File Size';
$_['text_upload_date'] = 'Upload Date';
$_['text_download'] = 'Download';
$_['text_remove'] = 'Remove';

// Cost Centers & Projects
$_['text_cost_centers'] = 'Cost Centers';
$_['text_projects'] = 'Projects';
$_['text_departments'] = 'Departments';
$_['text_select_cost_center'] = 'Select Cost Center';
$_['text_select_project'] = 'Select Project';
$_['text_select_department'] = 'Select Department';

// Advanced Features
$_['text_multi_currency'] = 'Multi-Currency';
$_['text_exchange_rate'] = 'Exchange Rate';
$_['text_base_currency'] = 'Base Currency';
$_['text_foreign_currency'] = 'Foreign Currency';
$_['text_currency_amount'] = 'Currency Amount';
$_['text_base_amount'] = 'Base Amount';

// Notifications
$_['text_notification_sent'] = 'Notification sent successfully';
$_['text_email_notification'] = 'Email Notification';
$_['text_sms_notification'] = 'SMS Notification';

// Integration
$_['text_integration'] = 'Integration';
$_['text_sync_status'] = 'Sync Status';
$_['text_last_sync'] = 'Last Sync';
$_['text_sync_now'] = 'Sync Now';

// Backup & Recovery
$_['text_backup'] = 'Backup';
$_['text_restore'] = 'Restore';
$_['text_archive'] = 'Archive';

// Reports
$_['text_reports'] = 'Reports';
$_['text_journal_report'] = 'Journal Report';
$_['text_trial_balance'] = 'Trial Balance';
$_['text_general_ledger'] = 'General Ledger';

// New language variables for journal entry controller fixes
$_['log_unauthorized_access_journal_entry'] = 'Unauthorized access attempt to journal entry screen';
$_['log_view_journal_entry_screen']    = 'View journal entry screen';
$_['error_journal_not_found']          = 'Journal entry not found';
$_['error_edit_journal_failed']        = 'Failed to edit journal entry';
$_['error_delete_journal_failed']      = 'Failed to delete journal entry';
$_['error_post_journal_failed']        = 'Failed to post journal entry';
$_['text_copy']                        = 'Copy';
$_['text_journal_number']              = 'Journal Number';
$_['text_journal_date']                = 'Journal Date';
$_['text_created_by']                  = 'Created By';
$_['text_journal_entries']             = 'Journal Entries';
$_['text_large_amount_alert']          = 'Alert: Posting large amount journal entry';
$_['error_unbalanced_journals']        = 'Unbalanced journal entries found';
$_['error_account_balance_errors']     = 'Account balance errors';
$_['error_balance_sheet_unbalanced']   = 'Balance sheet is unbalanced';
$_['text_accounting_integrity_valid']  = 'Accounting integrity is valid';
$_['error_integrity_check']            = 'Error in integrity check';
$_['text_approval_request_sent']       = 'Approval request sent successfully';
$_['error_journal_id_required']        = 'Journal ID is required';

// Enhanced performance and analytics variables
$_['text_optimized_journal']           = 'Optimized Journal Entries';
$_['text_journal_analysis']            = 'Journal Analysis';
$_['text_cache_enabled']               = 'Cache Enabled';
$_['text_total_journals']              = 'Total Journals';
$_['text_total_entries']               = 'Total Entries';
$_['text_top_accounts']                = 'Top Used Accounts';
$_['text_monthly_distribution']        = 'Monthly Distribution';
$_['text_usage_count']                 = 'Usage Count';
$_['button_journal_analysis']          = 'Journal Analysis';
$_['text_loading_analysis']            = 'Loading journal analysis...';
$_['text_analysis_ready']              = 'Analysis ready';

// Enhanced performance and analytics variables for journal entry
$_['text_optimized_journal_entries']   = 'Optimized Journal Entries';
$_['text_journal_entry_analysis']      = 'Journal Entry Analysis';
$_['text_by_account']                  = 'By Account';
$_['text_common_errors']               = 'Common Errors';
$_['text_smart_search']                = 'Smart Search';
$_['text_entry_count']                 = 'Entry Count';
$_['text_net_balance']                 = 'Net Balance';
$_['text_error_type']                  = 'Error Type';
$_['text_error_count']                 = 'Error Count';
$_['text_unbalanced_journal']          = 'Unbalanced Journal';
$_['text_validation_cache']            = 'Validation Cache';
$_['button_journal_entry_analysis']    = 'Journal Entry Analysis';
$_['text_loading_entry_analysis']      = 'Loading journal entry analysis...';
$_['text_entry_analysis_ready']        = 'Entry analysis ready';
?>
