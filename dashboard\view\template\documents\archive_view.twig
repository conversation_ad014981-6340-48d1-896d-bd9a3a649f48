{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="submit" form="documents\archive-form" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fas fa-save"></i></button>
        <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-light"><i class="fas fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    
    <div class="card">
      <div class="card-header"><i class="fas fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}chart-bar{% elif screen_type == 'settings' %}cog{% else %}pencil-alt{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="card-body">
        <form id="documents\archive-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-advanced_search">{{ text_advanced_search }}</label>
            <div class="col-sm-10">
              <input type="text" name="advanced_search" value="{{ advanced_search }}" placeholder="{{ text_advanced_search }}" id="input-advanced_search" class="form-control" />
              {% if error_advanced_search %}
                <div class="invalid-feedback">{{ error_advanced_search }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-analytics">{{ text_analytics }}</label>
            <div class="col-sm-10">
              <input type="text" name="analytics" value="{{ analytics }}" placeholder="{{ text_analytics }}" id="input-analytics" class="form-control" />
              {% if error_analytics %}
                <div class="invalid-feedback">{{ error_analytics }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-archive_stats">{{ text_archive_stats }}</label>
            <div class="col-sm-10">
              <input type="text" name="archive_stats" value="{{ archive_stats }}" placeholder="{{ text_archive_stats }}" id="input-archive_stats" class="form-control" />
              {% if error_archive_stats %}
                <div class="invalid-feedback">{{ error_archive_stats }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-back">{{ text_back }}</label>
            <div class="col-sm-10">
              <input type="text" name="back" value="{{ back }}" placeholder="{{ text_back }}" id="input-back" class="form-control" />
              {% if error_back %}
                <div class="invalid-feedback">{{ error_back }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-bulk_upload">{{ text_bulk_upload }}</label>
            <div class="col-sm-10">
              <input type="text" name="bulk_upload" value="{{ bulk_upload }}" placeholder="{{ text_bulk_upload }}" id="input-bulk_upload" class="form-control" />
              {% if error_bulk_upload %}
                <div class="invalid-feedback">{{ error_bulk_upload }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-categories">{{ text_categories }}</label>
            <div class="col-sm-10">
              <input type="text" name="categories" value="{{ categories }}" placeholder="{{ text_categories }}" id="input-categories" class="form-control" />
              {% if error_categories %}
                <div class="invalid-feedback">{{ error_categories }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-content_preview">{{ text_content_preview }}</label>
            <div class="col-sm-10">
              <input type="text" name="content_preview" value="{{ content_preview }}" placeholder="{{ text_content_preview }}" id="input-content_preview" class="form-control" />
              {% if error_content_preview %}
                <div class="invalid-feedback">{{ error_content_preview }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-document">{{ text_document }}</label>
            <div class="col-sm-10">
              <input type="text" name="document" value="{{ document }}" placeholder="{{ text_document }}" id="input-document" class="form-control" />
              {% if error_document %}
                <div class="invalid-feedback">{{ error_document }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-document_categories">{{ text_document_categories }}</label>
            <div class="col-sm-10">
              <input type="text" name="document_categories" value="{{ document_categories }}" placeholder="{{ text_document_categories }}" id="input-document_categories" class="form-control" />
              {% if error_document_categories %}
                <div class="invalid-feedback">{{ error_document_categories }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-document_history">{{ text_document_history }}</label>
            <div class="col-sm-10">
              <input type="text" name="document_history" value="{{ document_history }}" placeholder="{{ text_document_history }}" id="input-document_history" class="form-control" />
              {% if error_document_history %}
                <div class="invalid-feedback">{{ error_document_history }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-documents">{{ text_documents }}</label>
            <div class="col-sm-10">
              <input type="text" name="documents" value="{{ documents }}" placeholder="{{ text_documents }}" id="input-documents" class="form-control" />
              {% if error_documents %}
                <div class="invalid-feedback">{{ error_documents }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-download">{{ text_download }}</label>
            <div class="col-sm-10">
              <input type="text" name="download" value="{{ download }}" placeholder="{{ text_download }}" id="input-download" class="form-control" />
              {% if error_download %}
                <div class="invalid-feedback">{{ error_download }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-edit">{{ text_edit }}</label>
            <div class="col-sm-10">
              <input type="text" name="edit" value="{{ edit }}" placeholder="{{ text_edit }}" id="input-edit" class="form-control" />
              {% if error_edit %}
                <div class="invalid-feedback">{{ error_edit }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="invalid-feedback">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-manage_categories">{{ text_manage_categories }}</label>
            <div class="col-sm-10">
              <input type="text" name="manage_categories" value="{{ manage_categories }}" placeholder="{{ text_manage_categories }}" id="input-manage_categories" class="form-control" />
              {% if error_manage_categories %}
                <div class="invalid-feedback">{{ error_manage_categories }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-metadata_templates">{{ text_metadata_templates }}</label>
            <div class="col-sm-10">
              <input type="text" name="metadata_templates" value="{{ metadata_templates }}" placeholder="{{ text_metadata_templates }}" id="input-metadata_templates" class="form-control" />
              {% if error_metadata_templates %}
                <div class="invalid-feedback">{{ error_metadata_templates }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-popular_documents">{{ text_popular_documents }}</label>
            <div class="col-sm-10">
              <input type="text" name="popular_documents" value="{{ popular_documents }}" placeholder="{{ text_popular_documents }}" id="input-popular_documents" class="form-control" />
              {% if error_popular_documents %}
                <div class="invalid-feedback">{{ error_popular_documents }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-products">{{ text_products }}</label>
            <div class="col-sm-10">
              <input type="text" name="products" value="{{ products }}" placeholder="{{ text_products }}" id="input-products" class="form-control" />
              {% if error_products %}
                <div class="invalid-feedback">{{ error_products }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-recent_documents">{{ text_recent_documents }}</label>
            <div class="col-sm-10">
              <input type="text" name="recent_documents" value="{{ recent_documents }}" placeholder="{{ text_recent_documents }}" id="input-recent_documents" class="form-control" />
              {% if error_recent_documents %}
                <div class="invalid-feedback">{{ error_recent_documents }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-related_documents">{{ text_related_documents }}</label>
            <div class="col-sm-10">
              <input type="text" name="related_documents" value="{{ related_documents }}" placeholder="{{ text_related_documents }}" id="input-related_documents" class="form-control" />
              {% if error_related_documents %}
                <div class="invalid-feedback">{{ error_related_documents }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-search_features">{{ text_search_features }}</label>
            <div class="col-sm-10">
              <input type="text" name="search_features" value="{{ search_features }}" placeholder="{{ text_search_features }}" id="input-search_features" class="form-control" />
              {% if error_search_features %}
                <div class="invalid-feedback">{{ error_search_features }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-share">{{ text_share }}</label>
            <div class="col-sm-10">
              <input type="text" name="share" value="{{ share }}" placeholder="{{ text_share }}" id="input-share" class="form-control" />
              {% if error_share %}
                <div class="invalid-feedback">{{ error_share }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-storage_management">{{ text_storage_management }}</label>
            <div class="col-sm-10">
              <input type="text" name="storage_management" value="{{ storage_management }}" placeholder="{{ text_storage_management }}" id="input-storage_management" class="form-control" />
              {% if error_storage_management %}
                <div class="invalid-feedback">{{ error_storage_management }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-suppliers">{{ text_suppliers }}</label>
            <div class="col-sm-10">
              <input type="text" name="suppliers" value="{{ suppliers }}" placeholder="{{ text_suppliers }}" id="input-suppliers" class="form-control" />
              {% if error_suppliers %}
                <div class="invalid-feedback">{{ error_suppliers }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-supported_file_types">{{ text_supported_file_types }}</label>
            <div class="col-sm-10">
              <input type="text" name="supported_file_types" value="{{ supported_file_types }}" placeholder="{{ text_supported_file_types }}" id="input-supported_file_types" class="form-control" />
              {% if error_supported_file_types %}
                <div class="invalid-feedback">{{ error_supported_file_types }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-total">{{ text_total }}</label>
            <div class="col-sm-10">
              <input type="text" name="total" value="{{ total }}" placeholder="{{ text_total }}" id="input-total" class="form-control" />
              {% if error_total %}
                <div class="invalid-feedback">{{ error_total }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-upload">{{ text_upload }}</label>
            <div class="col-sm-10">
              <input type="text" name="upload" value="{{ upload }}" placeholder="{{ text_upload }}" id="input-upload" class="form-control" />
              {% if error_upload %}
                <div class="invalid-feedback">{{ error_upload }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-upload_options">{{ text_upload_options }}</label>
            <div class="col-sm-10">
              <input type="text" name="upload_options" value="{{ upload_options }}" placeholder="{{ text_upload_options }}" id="input-upload_options" class="form-control" />
              {% if error_upload_options %}
                <div class="invalid-feedback">{{ error_upload_options }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-usage_analytics">{{ text_usage_analytics }}</label>
            <div class="col-sm-10">
              <input type="text" name="usage_analytics" value="{{ usage_analytics }}" placeholder="{{ text_usage_analytics }}" id="input-usage_analytics" class="form-control" />
              {% if error_usage_analytics %}
                <div class="invalid-feedback">{{ error_usage_analytics }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="invalid-feedback">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-versions">{{ text_versions }}</label>
            <div class="col-sm-10">
              <input type="text" name="versions" value="{{ versions }}" placeholder="{{ text_versions }}" id="input-versions" class="form-control" />
              {% if error_versions %}
                <div class="invalid-feedback">{{ error_versions }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-view_stats">{{ text_view_stats }}</label>
            <div class="col-sm-10">
              <input type="text" name="view_stats" value="{{ view_stats }}" placeholder="{{ text_view_stats }}" id="input-view_stats" class="form-control" />
              {% if error_view_stats %}
                <div class="invalid-feedback">{{ error_view_stats }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}