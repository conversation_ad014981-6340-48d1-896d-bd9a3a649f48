<!DOCTYPE html>
<html dir="{{ direction }}" lang="{{ lang }}" class="no-js">
<head>
<meta charset="UTF-8" />
<title>{{ heading_title }}</title>
<base href="{{ base }}" />
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
<link href="view/stylesheet/bootstrap.css" rel="stylesheet" media="screen" />
<style>
@media print {
  body { margin: 0; }
  .no-print { display: none !important; }
  .page-break { page-break-before: always; }
}

body {
  font-family: Arial, sans-serif;
  font-size: 11px;
  line-height: 1.3;
}

.header {
  text-align: center;
  margin-bottom: 30px;
  border-bottom: 2px solid #333;
  padding-bottom: 20px;
}

.company-logo {
  max-height: 80px;
  margin-bottom: 10px;
}

.company-name {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}

.report-title {
  font-size: 18px;
  font-weight: bold;
  color: #666;
  margin-bottom: 10px;
}

.report-info {
  font-size: 10px;
  color: #888;
}

.summary-box {
  background-color: #f8f9fa;
  padding: 15px;
  margin-bottom: 20px;
  border: 1px solid #dee2e6;
}

.cost-center-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 20px;
  font-size: 9px;
}

.cost-center-table th,
.cost-center-table td {
  border: 1px solid #dee2e6;
  padding: 4px;
  text-align: left;
}

.cost-center-table th {
  background-color: #f8f9fa;
  font-weight: bold;
}

.text-right {
  text-align: right;
}

.text-center {
  text-align: center;
}

.text-success {
  color: #28a745;
}

.text-warning {
  color: #ffc107;
}

.text-danger {
  color: #dc3545;
}

.text-info {
  color: #17a2b8;
}

.footer {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #dee2e6;
  font-size: 9px;
  color: #888;
}

.signature-section {
  margin-top: 40px;
  display: flex;
  justify-content: space-between;
}

.signature-box {
  width: 200px;
  text-align: center;
}

.signature-line {
  border-top: 1px solid #333;
  margin-top: 50px;
  padding-top: 5px;
}

.watermark {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(-45deg);
  font-size: 72px;
  color: rgba(0,0,0,0.05);
  z-index: -1;
  pointer-events: none;
}

.performance-badge {
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 8px;
  font-weight: bold;
}

.performance-excellent {
  background-color: #28a745;
  color: white;
}

.performance-good {
  background-color: #17a2b8;
  color: white;
}

.performance-fair {
  background-color: #ffc107;
  color: black;
}

.performance-poor {
  background-color: #dc3545;
  color: white;
}
</style>
</head>
<body>

<!-- Watermark -->
<div class="watermark">{{ text_confidential }}</div>

<!-- Header -->
<div class="header">
  {% if company_logo %}
  <img src="{{ company_logo }}" alt="{{ company_name }}" class="company-logo">
  {% endif %}
  <div class="company-name">{{ company_name }}</div>
  <div class="report-title">{{ heading_title }}</div>
  <div class="report-info">
    {{ text_period }}: {{ date_start }} {{ text_to }} {{ date_end }} | {{ text_generated_on }}: {{ generated_date }}
  </div>
</div>

<!-- Report Information -->
<div class="summary-box">
  <h3 style="margin-top: 0;">{{ text_report_summary }}</h3>
  <div style="display: flex; justify-content: space-between;">
    <div>
      <strong>{{ text_period }}:</strong> {{ date_start }} {{ text_to }} {{ date_end }}<br>
      <strong>{{ text_currency }}:</strong> {{ currency }}<br>
      <strong>{{ text_total_cost_centers }}:</strong> {{ summary.total_cost_centers }}
    </div>
    <div>
      <strong>{{ text_total_revenues }}:</strong> {{ summary.total_revenues }}<br>
      <strong>{{ text_total_expenses }}:</strong> {{ summary.total_expenses }}<br>
      <strong>{{ text_overall_profit_margin }}:</strong> {{ summary.overall_profit_margin }}%
    </div>
    <div>
      <strong>{{ text_profitable_centers }}:</strong> {{ summary.profitable_centers }}<br>
      <strong>{{ text_loss_making_centers }}:</strong> {{ summary.loss_making_centers }}<br>
      <strong>{{ text_generated_by }}:</strong> {{ generated_by }}
    </div>
  </div>
</div>

<!-- Cost Centers Summary -->
<h3>{{ text_cost_centers_summary }}</h3>
<table class="cost-center-table">
  <thead>
    <tr>
      <th style="width: 8%;">{{ column_code }}</th>
      <th style="width: 20%;">{{ column_name }}</th>
      <th style="width: 15%;">{{ column_department }}</th>
      <th style="width: 12%;" class="text-right">{{ column_revenues }}</th>
      <th style="width: 12%;" class="text-right">{{ column_expenses }}</th>
      <th style="width: 12%;" class="text-right">{{ column_net_profit }}</th>
      <th style="width: 8%;" class="text-right">{{ column_margin }}</th>
      <th style="width: 13%;" class="text-center">{{ column_performance }}</th>
    </tr>
  </thead>
  <tbody>
    {% for center in cost_centers %}
    <tr>
      <td>{{ center.cost_center_code }}</td>
      <td>{{ center.cost_center_name }}</td>
      <td>{{ center.department }}</td>
      <td class="text-right">{{ center.total_revenues }}</td>
      <td class="text-right">{{ center.total_expenses }}</td>
      <td class="text-right {% if center.net_profit >= 0 %}text-success{% else %}text-danger{% endif %}">
        {{ center.net_profit }}
      </td>
      <td class="text-right {% if center.profit_margin >= 0 %}text-success{% else %}text-danger{% endif %}">
        {{ center.profit_margin }}%
      </td>
      <td class="text-center">
        <span class="performance-badge performance-{{ center.performance_rating }}">
          {% if center.performance_rating == 'excellent' %}{{ text_excellent }}
          {% elseif center.performance_rating == 'good' %}{{ text_good }}
          {% elseif center.performance_rating == 'fair' %}{{ text_fair }}
          {% else %}{{ text_poor }}{% endif %}
        </span>
      </td>
    </tr>
    {% endfor %}
  </tbody>
  <tfoot>
    <tr style="background-color: #e2e3e5; font-weight: bold;">
      <td colspan="3">{{ text_total }}</td>
      <td class="text-right">{{ summary.total_revenues }}</td>
      <td class="text-right">{{ summary.total_expenses }}</td>
      <td class="text-right {% if summary.total_profit >= 0 %}text-success{% else %}text-danger{% endif %}">
        {{ summary.total_profit }}
      </td>
      <td class="text-right">{{ summary.overall_profit_margin }}%</td>
      <td class="text-center">-</td>
    </tr>
  </tfoot>
</table>

<!-- Detailed Cost Center Analysis -->
{% for center in cost_centers %}
<div class="page-break"></div>
<h3>{{ text_detailed_analysis }}: {{ center.cost_center_name }} ({{ center.cost_center_code }})</h3>

<!-- Revenue Breakdown -->
<h4>{{ text_revenue_breakdown }}</h4>
<table class="cost-center-table">
  <thead>
    <tr>
      <th>{{ column_account_code }}</th>
      <th>{{ column_account_name }}</th>
      <th class="text-right">{{ column_amount }}</th>
      <th class="text-right">{{ column_transactions }}</th>
    </tr>
  </thead>
  <tbody>
    {% for revenue in center.revenues %}
    <tr>
      <td>{{ revenue.account_code }}</td>
      <td>{{ revenue.account_name }}</td>
      <td class="text-right">{{ revenue.amount }}</td>
      <td class="text-right">{{ revenue.transaction_count }}</td>
    </tr>
    {% endfor %}
  </tbody>
  <tfoot>
    <tr style="background-color: #e2e3e5; font-weight: bold;">
      <td colspan="2">{{ text_total_revenues }}</td>
      <td class="text-right">{{ center.total_revenues }}</td>
      <td class="text-right">-</td>
    </tr>
  </tfoot>
</table>

<!-- Expense Breakdown -->
<h4>{{ text_expense_breakdown }}</h4>
<table class="cost-center-table">
  <thead>
    <tr>
      <th>{{ column_account_code }}</th>
      <th>{{ column_account_name }}</th>
      <th>{{ column_category }}</th>
      <th class="text-right">{{ column_amount }}</th>
      <th class="text-right">{{ column_transactions }}</th>
    </tr>
  </thead>
  <tbody>
    {% for expense in center.direct_expenses %}
    <tr>
      <td>{{ expense.account_code }}</td>
      <td>{{ expense.account_name }}</td>
      <td>{{ expense.expense_category }}</td>
      <td class="text-right">{{ expense.amount }}</td>
      <td class="text-right">{{ expense.transaction_count }}</td>
    </tr>
    {% endfor %}
  </tbody>
  <tfoot>
    <tr style="background-color: #e2e3e5;">
      <td colspan="3">{{ text_direct_expenses }}</td>
      <td class="text-right">{{ center.total_direct_expenses }}</td>
      <td class="text-right">-</td>
    </tr>
    <tr style="background-color: #e2e3e5;">
      <td colspan="3">{{ text_indirect_costs }}</td>
      <td class="text-right">{{ center.total_indirect_costs }}</td>
      <td class="text-right">-</td>
    </tr>
    <tr style="background-color: #e2e3e5; font-weight: bold;">
      <td colspan="3">{{ text_total_expenses }}</td>
      <td class="text-right">{{ center.total_expenses }}</td>
      <td class="text-right">-</td>
    </tr>
  </tfoot>
</table>

<!-- Budget Comparison -->
{% if center.budget_comparison %}
<h4>{{ text_budget_comparison }}</h4>
<table class="cost-center-table">
  <thead>
    <tr>
      <th>{{ column_item }}</th>
      <th class="text-right">{{ column_budget }}</th>
      <th class="text-right">{{ column_actual }}</th>
      <th class="text-right">{{ column_variance }}</th>
      <th class="text-right">{{ column_variance_percentage }}</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>{{ text_revenues }}</td>
      <td class="text-right">{{ center.budget_comparison.budget_revenues }}</td>
      <td class="text-right">{{ center.budget_comparison.actual_revenues }}</td>
      <td class="text-right {% if center.budget_comparison.revenue_variance >= 0 %}text-success{% else %}text-danger{% endif %}">
        {{ center.budget_comparison.revenue_variance }}
      </td>
      <td class="text-right {% if center.budget_comparison.revenue_variance_percentage >= 0 %}text-success{% else %}text-danger{% endif %}">
        {{ center.budget_comparison.revenue_variance_percentage }}%
      </td>
    </tr>
    <tr>
      <td>{{ text_expenses }}</td>
      <td class="text-right">{{ center.budget_comparison.budget_expenses }}</td>
      <td class="text-right">{{ center.budget_comparison.actual_expenses }}</td>
      <td class="text-right {% if center.budget_comparison.expense_variance <= 0 %}text-success{% else %}text-danger{% endif %}">
        {{ center.budget_comparison.expense_variance }}
      </td>
      <td class="text-right {% if center.budget_comparison.expense_variance_percentage <= 0 %}text-success{% else %}text-danger{% endif %}">
        {{ center.budget_comparison.expense_variance_percentage }}%
      </td>
    </tr>
    <tr style="background-color: #e2e3e5; font-weight: bold;">
      <td>{{ text_net_profit }}</td>
      <td class="text-right">{{ center.budget_comparison.budget_profit }}</td>
      <td class="text-right">{{ center.budget_comparison.actual_profit }}</td>
      <td class="text-right {% if center.budget_comparison.profit_variance >= 0 %}text-success{% else %}text-danger{% endif %}">
        {{ center.budget_comparison.profit_variance }}
      </td>
      <td class="text-right {% if center.budget_comparison.profit_variance_percentage >= 0 %}text-success{% else %}text-danger{% endif %}">
        {{ center.budget_comparison.profit_variance_percentage }}%
      </td>
    </tr>
  </tbody>
</table>
{% endif %}

<!-- KPIs -->
{% if center.kpis %}
<h4>{{ text_key_performance_indicators }}</h4>
<table class="cost-center-table">
  <thead>
    <tr>
      <th>{{ column_kpi }}</th>
      <th class="text-right">{{ column_value }}</th>
      <th>{{ column_unit }}</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>{{ text_revenue_per_employee }}</td>
      <td class="text-right">{{ center.kpis.revenue_per_employee }}</td>
      <td>{{ currency }}</td>
    </tr>
    <tr>
      <td>{{ text_expense_per_employee }}</td>
      <td class="text-right">{{ center.kpis.expense_per_employee }}</td>
      <td>{{ currency }}</td>
    </tr>
    <tr>
      <td>{{ text_profit_per_employee }}</td>
      <td class="text-right">{{ center.kpis.profit_per_employee }}</td>
      <td>{{ currency }}</td>
    </tr>
    <tr>
      <td>{{ text_asset_turnover }}</td>
      <td class="text-right">{{ center.kpis.asset_turnover }}</td>
      <td>{{ text_times }}</td>
    </tr>
    <tr>
      <td>{{ text_expense_ratio }}</td>
      <td class="text-right">{{ center.kpis.expense_ratio }}</td>
      <td>%</td>
    </tr>
    <tr>
      <td>{{ text_efficiency_score }}</td>
      <td class="text-right">{{ center.kpis.efficiency_score }}</td>
      <td>%</td>
    </tr>
  </tbody>
</table>
{% endif %}
{% endfor %}

<!-- Recommendations -->
{% if recommendations %}
<div class="page-break"></div>
<h3>{{ text_recommendations }}</h3>
<div style="background-color: #f8f9fa; padding: 15px; border: 1px solid #dee2e6;">
  <ol style="margin: 0; padding-left: 20px;">
    {% for recommendation in recommendations %}
    <li style="margin-bottom: 10px;">{{ recommendation }}</li>
    {% endfor %}
  </ol>
</div>
{% endif %}

<!-- Signatures -->
<div class="signature-section">
  <div class="signature-box">
    <div class="signature-line">{{ text_prepared_by }}</div>
  </div>
  <div class="signature-box">
    <div class="signature-line">{{ text_reviewed_by }}</div>
  </div>
  <div class="signature-box">
    <div class="signature-line">{{ text_approved_by }}</div>
  </div>
</div>

<!-- Footer -->
<div class="footer">
  <div style="display: flex; justify-content: space-between;">
    <div>
      {{ text_confidential }} - {{ text_internal_use }}
    </div>
    <div>
      {{ text_eas_compliant }} | {{ text_cost_center_control }}
    </div>
    <div>
      {{ text_page }} 1 {{ text_of }} 1
    </div>
  </div>
</div>

<script>
// Auto print when page loads
window.onload = function() {
  window.print();
};
</script>

</body>
</html>
