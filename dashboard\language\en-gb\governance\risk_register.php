<?php
// Headings
$_['heading_title'] = 'Risk Register';
$_['text_list'] = 'Risk List';
$_['text_home'] = 'Home';

// Table Columns
$_['column_risk_id'] = 'Number';
$_['column_title'] = 'Title';
$_['column_category'] = 'Category';
$_['column_likelihood'] = 'Likelihood';
$_['column_impact'] = 'Impact';
$_['column_score'] = 'Score';
$_['column_owner'] = 'Administrator';
$_['column_status'] = 'Status';
$_['column_date_added'] = 'Date Added';
$_['column_action'] = 'Action';

// Buttons and actions
$_['button_filter'] = 'Filter';
$_['button_add'] = 'Add risk';
$_['button_edit'] = 'Edit';
$_['button_delete'] = 'Delete';
$_['button_save'] = 'Save';
$_['button_close'] = 'Close';

// Text for filters
$_['text_all_categories'] = '- All categories -';
$_['text_all_statuses'] = '- All statuses -';
$_['text_all_owners'] = '- All groups -';
$_['text_all_natures'] = '- All species -';

$_['text_open'] = 'Open';
$_['text_mitigated'] = 'Mitigated';
$_['text_closed'] = 'Closed';
$_['text_ongoing'] = 'Permanent';
$_['text_one_time'] = 'Temporary';

$_['text_confirm_delete'] = 'Are you sure you want to delete this risk?';
$_['text_modal_add'] = 'Add risk';
$_['text_modal_edit'] = 'Edit risk';

// Input fields
$_['entry_title'] = 'Risk title';
$_['entry_description'] = 'Risk description';
$_['entry_category'] = 'Risk classification';
$_['entry_likelihood'] = 'Likelihood of occurrence';
$_['entry_impact'] = 'Risk impact';
$_['entry_nature_of_risk'] = 'Nature of risk';
$_['entry_status'] = 'Status';
$_['entry_owner_group'] = 'Administrator group';
$_['entry_risk_start_date'] = 'Start date';
$_['entry_risk_end_date'] = 'End date';
$_['entry_mitigation_plan'] = 'Mitigation plan';

// Common errors
$_['error_no_permission'] = 'You do not have permission to access this section!';
$_['error_request_failed'] = 'Request failed:';