{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="submit" form="system\integration_advanced-form" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fas fa-save"></i></button>
        <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-light"><i class="fas fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    
    <div class="card">
      <div class="card-header"><i class="fas fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}chart-bar{% elif screen_type == 'settings' %}cog{% else %}pencil-alt{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="card-body">
        <form id="system\integration_advanced-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-check_accounting_url">{{ text_check_accounting_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="check_accounting_url" value="{{ check_accounting_url }}" placeholder="{{ text_check_accounting_url }}" id="input-check_accounting_url" class="form-control" />
              {% if error_check_accounting_url %}
                <div class="invalid-feedback">{{ error_check_accounting_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-check_integrity_url">{{ text_check_integrity_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="check_integrity_url" value="{{ check_integrity_url }}" placeholder="{{ text_check_integrity_url }}" id="input-check_integrity_url" class="form-control" />
              {% if error_check_integrity_url %}
                <div class="invalid-feedback">{{ error_check_integrity_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-fix_issues_url">{{ text_fix_issues_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="fix_issues_url" value="{{ fix_issues_url }}" placeholder="{{ text_fix_issues_url }}" id="input-fix_issues_url" class="form-control" />
              {% if error_fix_issues_url %}
                <div class="invalid-feedback">{{ error_fix_issues_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="invalid-feedback">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-status_url">{{ text_status_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="status_url" value="{{ status_url }}" placeholder="{{ text_status_url }}" id="input-status_url" class="form-control" />
              {% if error_status_url %}
                <div class="invalid-feedback">{{ error_status_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sync_modules_url">{{ text_sync_modules_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="sync_modules_url" value="{{ sync_modules_url }}" placeholder="{{ text_sync_modules_url }}" id="input-sync_modules_url" class="form-control" />
              {% if error_sync_modules_url %}
                <div class="invalid-feedback">{{ error_sync_modules_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-test_connections_url">{{ text_test_connections_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="test_connections_url" value="{{ test_connections_url }}" placeholder="{{ text_test_connections_url }}" id="input-test_connections_url" class="form-control" />
              {% if error_test_connections_url %}
                <div class="invalid-feedback">{{ error_test_connections_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-update_settings_url">{{ text_update_settings_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="update_settings_url" value="{{ update_settings_url }}" placeholder="{{ text_update_settings_url }}" id="input-update_settings_url" class="form-control" />
              {% if error_update_settings_url %}
                <div class="invalid-feedback">{{ error_update_settings_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="invalid-feedback">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}