{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <div class="btn-group">
          <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown">
            <i class="fa fa-cog"></i> إعدادات اللوحة <span class="caret"></span>
          </button>
          <ul class="dropdown-menu dropdown-menu-right">
            <li><a href="#" onclick="refreshDashboard()"><i class="fa fa-refresh"></i> تحديث البيانات</a></li>
            <li><a href="#" onclick="customizeDashboard()"><i class="fa fa-edit"></i> تخصيص اللوحة</a></li>
            <li><a href="#" onclick="exportDashboard()"><i class="fa fa-download"></i> تصدير التقرير</a></li>
          </ul>
        </div>
      </div>
      <h1><i class="fa fa-dashboard"></i> لوحة المعلومات التنفيذية</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>

  <div class="container-fluid">
    
    <!-- الإشعارات العاجلة -->
    {% if urgent_notifications %}
    <div class="alert alert-warning alert-dismissible">
      <button type="button" class="close" data-dismiss="alert">&times;</button>
      <h4><i class="fa fa-exclamation-triangle"></i> تنبيهات عاجلة</h4>
      <ul class="list-unstyled">
        {% for notification in urgent_notifications %}
        <li><strong>{{ notification.title }}:</strong> {{ notification.content }}</li>
        {% endfor %}
      </ul>
    </div>
    {% endif %}

    <!-- الاختصارات السريعة -->
    {% if quick_actions %}
    <div class="row">
      <div class="col-md-12">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-bolt"></i> العمليات السريعة</h3>
          </div>
          <div class="panel-body">
            <div class="row">
              {% for action in quick_actions %}
              <div class="col-md-2 col-sm-4 col-xs-6">
                <a href="{{ action.url }}" class="btn btn-default btn-block quick-action-btn">
                  <i class="fa {{ action.icon }} fa-2x"></i><br>
                  <small>{{ action.name }}</small>
                </a>
              </div>
              {% endfor %}
            </div>
          </div>
        </div>
      </div>
    </div>
    {% endif %}

    <!-- المؤشرات المالية الرئيسية -->
    {% if financial_kpis %}
    <div class="row">
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-primary">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-money fa-5x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ financial_kpis.today.revenue|number_format(2) }}</div>
                <div>إيرادات اليوم</div>
              </div>
            </div>
          </div>
          <div class="panel-footer">
            <span class="pull-left">{{ financial_kpis.today.orders }} طلب</span>
            <span class="pull-right">
              <i class="fa fa-arrow-circle-right"></i>
            </span>
            <div class="clearfix"></div>
          </div>
        </div>
      </div>

      <div class="col-lg-3 col-md-6">
        <div class="panel panel-green">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-shopping-cart fa-5x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ financial_kpis.today.orders }}</div>
                <div>طلبات اليوم</div>
              </div>
            </div>
          </div>
          <div class="panel-footer">
            <span class="pull-left">{{ financial_kpis.today.customers }} عميل جديد</span>
            <span class="pull-right">
              <i class="fa fa-arrow-circle-right"></i>
            </span>
            <div class="clearfix"></div>
          </div>
        </div>
      </div>

      <div class="col-lg-3 col-md-6">
        <div class="panel panel-yellow">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-users fa-5x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ financial_kpis.today.customers }}</div>
                <div>عملاء جدد</div>
              </div>
            </div>
          </div>
          <div class="panel-footer">
            <span class="pull-left">اليوم</span>
            <span class="pull-right">
              <i class="fa fa-arrow-circle-right"></i>
            </span>
            <div class="clearfix"></div>
          </div>
        </div>
      </div>

      <div class="col-lg-3 col-md-6">
        <div class="panel panel-red">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-percent fa-5x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ financial_kpis.today.profit_margin.margin_percentage }}%</div>
                <div>هامش الربح</div>
              </div>
            </div>
          </div>
          <div class="panel-footer">
            <span class="pull-left">{{ financial_kpis.today.profit_margin.gross_profit|number_format(2) }} ربح</span>
            <span class="pull-right">
              <i class="fa fa-arrow-circle-right"></i>
            </span>
            <div class="clearfix"></div>
          </div>
        </div>
      </div>
    </div>
    {% endif %}

    <!-- أداء القنوات -->
    {% if channel_performance %}
    <div class="row">
      <div class="col-md-4">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-globe"></i> المتجر الإلكتروني</h3>
          </div>
          <div class="panel-body">
            <div class="row">
              <div class="col-xs-6">
                <div class="text-center">
                  <div class="huge text-primary">{{ channel_performance.online_store.revenue|number_format(0) }}</div>
                  <div>الإيرادات</div>
                </div>
              </div>
              <div class="col-xs-6">
                <div class="text-center">
                  <div class="huge text-success">{{ channel_performance.online_store.orders }}</div>
                  <div>الطلبات</div>
                </div>
              </div>
            </div>
            <hr>
            <div class="row">
              <div class="col-xs-6">
                <small>معدل التحويل</small><br>
                <strong>{{ channel_performance.online_store.conversion_rate }}%</strong>
              </div>
              <div class="col-xs-6">
                <small>السلات المهجورة</small><br>
                <strong>{{ channel_performance.online_store.abandoned_carts }}</strong>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="col-md-4">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-store"></i> نقاط البيع</h3>
          </div>
          <div class="panel-body">
            <div class="row">
              <div class="col-xs-6">
                <div class="text-center">
                  <div class="huge text-primary">{{ channel_performance.pos_branches.revenue|number_format(0) }}</div>
                  <div>الإيرادات</div>
                </div>
              </div>
              <div class="col-xs-6">
                <div class="text-center">
                  <div class="huge text-success">{{ channel_performance.pos_branches.transactions }}</div>
                  <div>المعاملات</div>
                </div>
              </div>
            </div>
            <hr>
            <div class="row">
              <div class="col-xs-6">
                <small>الأجهزة النشطة</small><br>
                <strong>{{ channel_performance.pos_branches.active_terminals }}</strong>
              </div>
              <div class="col-xs-6">
                <small>متوسط المعاملة</small><br>
                <strong>{{ (channel_performance.pos_branches.revenue / channel_performance.pos_branches.transactions)|number_format(2) }}</strong>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="col-md-4">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-user-tie"></i> المناديب</h3>
          </div>
          <div class="panel-body">
            <div class="row">
              <div class="col-xs-6">
                <div class="text-center">
                  <div class="huge text-primary">{{ channel_performance.sales_reps.revenue|number_format(0) }}</div>
                  <div>الإيرادات</div>
                </div>
              </div>
              <div class="col-xs-6">
                <div class="text-center">
                  <div class="huge text-success">{{ channel_performance.sales_reps.orders }}</div>
                  <div>الطلبات</div>
                </div>
              </div>
            </div>
            <hr>
            <div class="row">
              <div class="col-xs-6">
                <small>المناديب النشطون</small><br>
                <strong>{{ channel_performance.sales_reps.active_reps }}</strong>
              </div>
              <div class="col-xs-6">
                <small>أفضل مندوب</small><br>
                <strong>{{ channel_performance.sales_reps.top_performers[0].name ?? 'غير محدد' }}</strong>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    {% endif %}

    <!-- نظرة عامة على المخزون والموافقات -->
    <div class="row">
      <!-- المخزون -->
      {% if inventory_overview %}
      <div class="col-md-6">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-boxes"></i> نظرة عامة على المخزون</h3>
          </div>
          <div class="panel-body">
            <div class="row">
              <div class="col-md-6">
                <div class="metric-box">
                  <div class="metric-value">{{ inventory_overview.total_value|number_format(0) }}</div>
                  <div class="metric-label">إجمالي قيمة المخزون</div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="metric-box">
                  <div class="metric-value text-warning">{{ inventory_overview.low_stock_count }}</div>
                  <div class="metric-label">منتجات منخفضة المخزون</div>
                </div>
              </div>
            </div>
            <hr>
            <div class="row">
              <div class="col-md-6">
                <div class="metric-box">
                  <div class="metric-value text-danger">{{ inventory_overview.out_of_stock_count }}</div>
                  <div class="metric-label">منتجات نفدت</div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="metric-box">
                  <div class="metric-value text-info">{{ inventory_overview.expiring_soon|length }}</div>
                  <div class="metric-label">منتجات تنتهي صلاحيتها</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      {% endif %}

      <!-- الموافقات المعلقة -->
      {% if pending_approvals %}
      <div class="col-md-6">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-check-circle"></i> الموافقات المعلقة</h3>
          </div>
          <div class="panel-body">
            <div class="list-group">
              {% if pending_approvals.purchase_orders %}
                <div class="list-group-item">
                  <span class="badge">{{ pending_approvals.purchase_orders|length }}</span>
                  <i class="fa fa-shopping-cart"></i> طلبات شراء معلقة
                </div>
              {% endif %}
              {% if pending_approvals.expense_requests %}
                <div class="list-group-item">
                  <span class="badge">{{ pending_approvals.expense_requests|length }}</span>
                  <i class="fa fa-receipt"></i> طلبات مصروفات
                </div>
              {% endif %}
              {% if pending_approvals.discount_requests %}
                <div class="list-group-item">
                  <span class="badge">{{ pending_approvals.discount_requests|length }}</span>
                  <i class="fa fa-percent"></i> طلبات خصم
                </div>
              {% endif %}
              {% if pending_approvals.customer_credit %}
                <div class="list-group-item">
                  <span class="badge">{{ pending_approvals.customer_credit|length }}</span>
                  <i class="fa fa-credit-card"></i> طلبات ائتمان العملاء
                </div>
              {% endif %}
            </div>
            <div class="text-center">
              <a href="#" class="btn btn-primary btn-sm">عرض جميع الموافقات</a>
            </div>
          </div>
        </div>
      </div>
      {% endif %}
    </div>

    <!-- المنتجات الأكثر مبيعاً والعملاء المميزون -->
    <div class="row">
      <!-- المنتجات الأكثر مبيعاً -->
      <div class="col-md-6">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-star"></i> المنتجات الأكثر مبيعاً</h3>
          </div>
          <div class="panel-body">
            <div class="table-responsive">
              <table class="table table-striped table-condensed">
                <thead>
                  <tr>
                    <th>المنتج</th>
                    <th>الكمية المباعة</th>
                    <th>الإيرادات</th>
                  </tr>
                </thead>
                <tbody>
                  {% for product in inventory_overview.fast_moving %}
                  <tr>
                    <td>{{ product.name }}</td>
                    <td><span class="badge">{{ product.total_sold }}</span></td>
                    <td>{{ product.total_revenue|number_format(2) }}</td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      <!-- العملاء المميزون -->
      <div class="col-md-6">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-crown"></i> العملاء المميزون</h3>
          </div>
          <div class="panel-body">
            <div class="table-responsive">
              <table class="table table-striped table-condensed">
                <thead>
                  <tr>
                    <th>العميل</th>
                    <th>الطلبات</th>
                    <th>إجمالي الإنفاق</th>
                  </tr>
                </thead>
                <tbody>
                  {% for customer in customer_analytics.top_customers %}
                  <tr>
                    <td>{{ customer.customer_name }}</td>
                    <td><span class="badge">{{ customer.total_orders }}</span></td>
                    <td>{{ customer.total_spent|number_format(2) }}</td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>

  </div>
</div>

<!-- CSS مخصص للوحة المعلومات -->
<style>
.huge {
  font-size: 40px;
  font-weight: bold;
}

.panel-green {
  border-color: #5cb85c;
}

.panel-green > .panel-heading {
  border-color: #5cb85c;
  color: white;
  background-color: #5cb85c;
}

.panel-yellow {
  border-color: #f0ad4e;
}

.panel-yellow > .panel-heading {
  border-color: #f0ad4e;
  color: white;
  background-color: #f0ad4e;
}

.panel-red {
  border-color: #d9534f;
}

.panel-red > .panel-heading {
  border-color: #d9534f;
  color: white;
  background-color: #d9534f;
}

.quick-action-btn {
  height: 80px;
  margin-bottom: 10px;
  border: 2px solid #ddd;
  transition: all 0.3s ease;
}

.quick-action-btn:hover {
  border-color: #337ab7;
  background-color: #f5f5f5;
  transform: translateY(-2px);
}

.metric-box {
  text-align: center;
  padding: 10px;
}

.metric-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 5px;
}

.metric-label {
  font-size: 12px;
  color: #666;
}

.panel-footer {
  background-color: #f5f5f5;
  border-top: 1px solid #ddd;
}

.text-primary { color: #337ab7 !important; }
.text-success { color: #5cb85c !important; }
.text-warning { color: #f0ad4e !important; }
.text-danger { color: #d9534f !important; }
.text-info { color: #5bc0de !important; }
</style>

<!-- JavaScript للتفاعل -->
<script>
function refreshDashboard() {
  location.reload();
}

function customizeDashboard() {
  alert('ميزة تخصيص اللوحة قيد التطوير');
}

function exportDashboard() {
  window.print();
}

// تحديث البيانات كل 5 دقائق
setInterval(function() {
  // يمكن إضافة AJAX لتحديث البيانات دون إعادة تحميل الصفحة
}, 300000);
</script>

{{ footer }}