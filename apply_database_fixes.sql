-- ملف تطبيق جميع إصلاحات قاعدة البيانات لـ AYM ERP
-- تاريخ الإنشاء: 2025-07-28
-- الهدف: تطبيق جميع الإصلاحات المطلوبة لحل أخطاء dashboard.php

-- ========================================
-- تعطيل فحص المفاتيح الخارجية مؤقتاً
-- ========================================
SET FOREIGN_KEY_CHECKS = 0;
SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;

-- ========================================
-- الخطوة 1: إنشاء الجداول المفقودة
-- ========================================

-- تشغيل ملف الجداول المفقودة
SOURCE missing_tables.sql;

-- ========================================
-- الخطوة 2: إضافة الأعمدة المفقودة
-- ========================================

-- تشغيل ملف الأعمدة المفقودة
SOURCE missing_columns.sql;

-- ========================================
-- الخطوة 3: إصلاحات إضافية مباشرة
-- ========================================

-- إصلاح جدول cod_journal_entry إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS `cod_journal_entry` (
  `entry_id` int(11) NOT NULL AUTO_INCREMENT,
  `journal_id` int(11) DEFAULT NULL,
  `entry_number` varchar(50) NOT NULL,
  `entry_date` date NOT NULL,
  `description` text,
  `reference_type` varchar(50) DEFAULT NULL,
  `reference_id` int(11) DEFAULT NULL,
  `total_debit` decimal(15,4) DEFAULT 0.0000,
  `total_credit` decimal(15,4) DEFAULT 0.0000,
  `status` enum('draft','posted','reversed') DEFAULT 'draft',
  `created_by` int(11) DEFAULT NULL,
  `approved_by` int(11) DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`entry_id`),
  KEY `idx_journal_id` (`journal_id`),
  KEY `idx_entry_date` (`entry_date`),
  KEY `idx_reference` (`reference_type`, `reference_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول قيود اليومية';

-- إصلاح جدول cod_cash_voucher إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS `cod_cash_voucher` (
  `voucher_id` int(11) NOT NULL AUTO_INCREMENT,
  `voucher_number` varchar(50) NOT NULL,
  `voucher_type` enum('receipt','payment') NOT NULL,
  `voucher_date` date NOT NULL,
  `amount` decimal(15,4) NOT NULL,
  `currency_id` int(11) DEFAULT 1,
  `exchange_rate` decimal(10,6) DEFAULT 1.000000,
  `payee_name` varchar(200) DEFAULT NULL,
  `description` text,
  `account_id` int(11) NOT NULL,
  `cash_account_id` int(11) NOT NULL,
  `reference_type` varchar(50) DEFAULT NULL,
  `reference_id` int(11) DEFAULT NULL,
  `status` enum('draft','approved','cancelled') DEFAULT 'draft',
  `created_by` int(11) DEFAULT NULL,
  `approved_by` int(11) DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`voucher_id`),
  UNIQUE KEY `voucher_number` (`voucher_number`),
  KEY `idx_voucher_type` (`voucher_type`),
  KEY `idx_voucher_date` (`voucher_date`),
  KEY `idx_account_id` (`account_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول سندات النقدية';

-- إصلاح جدول cod_cash_flow إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS `cod_cash_flow` (
  `flow_id` int(11) NOT NULL AUTO_INCREMENT,
  `flow_date` date NOT NULL,
  `flow_type` enum('inflow','outflow') NOT NULL,
  `category` varchar(100) NOT NULL,
  `subcategory` varchar(100) DEFAULT NULL,
  `amount` decimal(15,4) NOT NULL,
  `currency_id` int(11) DEFAULT 1,
  `account_id` int(11) NOT NULL,
  `description` text,
  `reference_type` varchar(50) DEFAULT NULL,
  `reference_id` int(11) DEFAULT NULL,
  `project_id` int(11) DEFAULT NULL,
  `department_id` int(11) DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`flow_id`),
  KEY `idx_flow_date` (`flow_date`),
  KEY `idx_flow_type` (`flow_type`),
  KEY `idx_category` (`category`),
  KEY `idx_account_id` (`account_id`),
  KEY `idx_reference` (`reference_type`, `reference_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول التدفق النقدي';

-- إصلاح جدول cod_investment إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS `cod_investment` (
  `investment_id` int(11) NOT NULL AUTO_INCREMENT,
  `investment_name` varchar(200) NOT NULL,
  `investment_type` enum('stocks','bonds','mutual_funds','real_estate','commodities','crypto','other') NOT NULL,
  `investment_amount` decimal(15,4) NOT NULL,
  `currency_id` int(11) DEFAULT 1,
  `purchase_date` date NOT NULL,
  `purchase_price` decimal(15,4) NOT NULL,
  `current_value` decimal(15,4) DEFAULT NULL,
  `maturity_date` date DEFAULT NULL,
  `expected_return` decimal(5,2) DEFAULT NULL,
  `actual_return` decimal(5,2) DEFAULT NULL,
  `risk_level` enum('low','medium','high','very_high') DEFAULT 'medium',
  `portfolio_id` int(11) DEFAULT NULL,
  `broker_name` varchar(200) DEFAULT NULL,
  `notes` text,
  `status` enum('active','sold','matured','cancelled') DEFAULT 'active',
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`investment_id`),
  KEY `idx_investment_type` (`investment_type`),
  KEY `idx_purchase_date` (`purchase_date`),
  KEY `idx_status` (`status`),
  KEY `idx_portfolio_id` (`portfolio_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول الاستثمارات';

-- إصلاح جدول cod_loan إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS `cod_loan` (
  `loan_id` int(11) NOT NULL AUTO_INCREMENT,
  `loan_number` varchar(50) NOT NULL,
  `loan_type` enum('business','personal','mortgage','equipment','working_capital') NOT NULL,
  `lender_name` varchar(200) NOT NULL,
  `principal_amount` decimal(15,4) NOT NULL,
  `interest_rate` decimal(5,4) NOT NULL,
  `loan_term_months` int(11) NOT NULL,
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `monthly_payment` decimal(15,4) NOT NULL,
  `outstanding_balance` decimal(15,4) NOT NULL,
  `total_paid` decimal(15,4) DEFAULT 0.0000,
  `currency_id` int(11) DEFAULT 1,
  `collateral_description` text,
  `purpose` text,
  `status` enum('active','paid_off','defaulted','restructured') DEFAULT 'active',
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`loan_id`),
  UNIQUE KEY `loan_number` (`loan_number`),
  KEY `idx_loan_type` (`loan_type`),
  KEY `idx_start_date` (`start_date`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول القروض';

-- إصلاح جدول cod_loan_transaction إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS `cod_loan_transaction` (
  `transaction_id` int(11) NOT NULL AUTO_INCREMENT,
  `loan_id` int(11) NOT NULL,
  `transaction_date` date NOT NULL,
  `transaction_type` enum('payment','interest','principal','fee','penalty') NOT NULL,
  `amount` decimal(15,4) NOT NULL,
  `principal_portion` decimal(15,4) DEFAULT 0.0000,
  `interest_portion` decimal(15,4) DEFAULT 0.0000,
  `fee_portion` decimal(15,4) DEFAULT 0.0000,
  `balance_after` decimal(15,4) NOT NULL,
  `payment_method` varchar(50) DEFAULT NULL,
  `reference_number` varchar(100) DEFAULT NULL,
  `notes` text,
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`transaction_id`),
  KEY `idx_loan_id` (`loan_id`),
  KEY `idx_transaction_date` (`transaction_date`),
  KEY `idx_transaction_type` (`transaction_type`),
  FOREIGN KEY (`loan_id`) REFERENCES `cod_loan` (`loan_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول معاملات القروض';

-- إصلاح جدول cod_expense_detail إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS `cod_expense_detail` (
  `detail_id` int(11) NOT NULL AUTO_INCREMENT,
  `expense_id` int(11) NOT NULL,
  `item_description` varchar(500) NOT NULL,
  `quantity` decimal(10,4) DEFAULT 1.0000,
  `unit_price` decimal(15,4) NOT NULL,
  `total_amount` decimal(15,4) NOT NULL,
  `tax_amount` decimal(15,4) DEFAULT 0.0000,
  `account_id` int(11) DEFAULT NULL,
  `project_id` int(11) DEFAULT NULL,
  `department_id` int(11) DEFAULT NULL,
  `notes` text,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`detail_id`),
  KEY `idx_expense_id` (`expense_id`),
  KEY `idx_account_id` (`account_id`),
  KEY `idx_project_id` (`project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول تفاصيل المصروفات';

-- إصلاح جدول cod_revenue_detail إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS `cod_revenue_detail` (
  `detail_id` int(11) NOT NULL AUTO_INCREMENT,
  `revenue_id` int(11) NOT NULL,
  `item_description` varchar(500) NOT NULL,
  `quantity` decimal(10,4) DEFAULT 1.0000,
  `unit_price` decimal(15,4) NOT NULL,
  `total_amount` decimal(15,4) NOT NULL,
  `tax_amount` decimal(15,4) DEFAULT 0.0000,
  `account_id` int(11) DEFAULT NULL,
  `project_id` int(11) DEFAULT NULL,
  `department_id` int(11) DEFAULT NULL,
  `notes` text,
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`detail_id`),
  KEY `idx_revenue_id` (`revenue_id`),
  KEY `idx_account_id` (`account_id`),
  KEY `idx_project_id` (`project_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول تفاصيل الإيرادات';

-- ========================================
-- الخطوة 4: إضافة بيانات افتراضية
-- ========================================

-- إضافة بيانات افتراضية لجداول الذكاء الاصطناعي
INSERT IGNORE INTO `cod_ai_predictions` (`prediction_type`, `model_name`, `predicted_value`, `prediction_date`, `target_date`) VALUES
('sales', 'Linear Regression Model', 50000.00, NOW(), DATE_ADD(CURDATE(), INTERVAL 30 DAY)),
('demand', 'ARIMA Model', 1500.00, NOW(), DATE_ADD(CURDATE(), INTERVAL 7 DAY)),
('price', 'Random Forest Model', 25.50, NOW(), DATE_ADD(CURDATE(), INTERVAL 1 DAY));

-- إضافة بيانات افتراضية لجداول التوصيات
INSERT IGNORE INTO `cod_ai_recommendations` (`recommendation_type`, `target_entity`, `target_id`, `recommendation_text`, `confidence_score`, `date_created`) VALUES
('inventory', 'product', 1, 'يُنصح بزيادة مخزون هذا المنتج بنسبة 20%', 0.85, NOW()),
('pricing', 'product', 2, 'يُنصح بتقليل السعر بنسبة 5% لزيادة المبيعات', 0.78, NOW()),
('marketing', 'campaign', 1, 'يُنصح بتوجيه الحملة للفئة العمرية 25-35', 0.92, NOW());

-- ========================================
-- الخطوة 5: تحديث الإحصائيات
-- ========================================

-- تحديث إحصائيات الجداول
ANALYZE TABLE `cod_ai_predictions`;
ANALYZE TABLE `cod_ai_recommendations`;
ANALYZE TABLE `cod_ai_fraud_detection`;
ANALYZE TABLE `cod_employee_training`;
ANALYZE TABLE `cod_email_campaign`;
ANALYZE TABLE `cod_journal_entry`;
ANALYZE TABLE `cod_cash_voucher`;
ANALYZE TABLE `cod_cash_flow`;

-- ========================================
-- الخطوة 6: إعادة تفعيل فحص المفاتيح الخارجية
-- ========================================
SET FOREIGN_KEY_CHECKS = 1;
COMMIT;

-- ========================================
-- رسالة النجاح
-- ========================================
SELECT 'تم تطبيق جميع إصلاحات قاعدة البيانات بنجاح!' as message;
