<?php

/**
 * ConnectionClosedException
 *
 * PHP version 5
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2015 <PERSON>
 * @license   http://www.opensource.org/licenses/mit-license.html  MIT License
 * @link      http://phpseclib.sourceforge.net
 */

declare(strict_types=1);

namespace phpseclib3\Exception;

/**
 * ConnectionClosedException
 *
 * <AUTHOR> <<EMAIL>>
 */
class ConnectionClosedException extends \RuntimeException implements ExceptionInterface
{
}
