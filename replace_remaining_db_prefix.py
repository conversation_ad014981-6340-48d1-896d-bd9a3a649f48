#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت بسيط لاستبدال جميع استخدامات DB_PREFIX المتبقية
يستبدلها بـ cod_ مع تعليق أنها جداول غير موجودة
"""

import re
import os
from datetime import datetime

def main():
    file_path = "dashboard/model/common/dashboard.php"
    
    if not os.path.exists(file_path):
        print(f"❌ الملف غير موجود: {file_path}")
        return
    
    print("🔧 بدء استبدال DB_PREFIX المتبقية...")
    
    # قراءة الملف
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # إنشاء نسخة احتياطية
    backup_file = f"{file_path}.backup_final.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    with open(backup_file, 'w', encoding='utf-8') as f:
        f.write(content)
    print(f"📁 تم إنشاء نسخة احتياطية: {backup_file}")
    
    # عد الاستخدامات قبل التعديل
    original_count = content.count('DB_PREFIX')
    
    # استبدال جميع الأنماط المتبقية
    replacements_made = 0
    
    # النمط 1: DB_PREFIX . "table_name"
    pattern1 = re.compile(r'DB_PREFIX \. "([^"]+)"')
    matches1 = pattern1.findall(content)
    for table_name in set(matches1):
        old_pattern = f'DB_PREFIX . "{table_name}"'
        new_replacement = f'"cod_{table_name}" /* جدول غير موجود - مُعلق مؤقتاً */'
        count = content.count(old_pattern)
        content = content.replace(old_pattern, new_replacement)
        replacements_made += count
        if count > 0:
            print(f"✅ استبدال {count} من: {old_pattern}")
    
    # النمط 2: " . DB_PREFIX . "table_name
    pattern2 = re.compile(r'" \. DB_PREFIX \. "([^"]+)')
    matches2 = pattern2.findall(content)
    for table_name in set(matches2):
        old_pattern = f'" . DB_PREFIX . "{table_name}'
        new_replacement = f'"cod_{table_name} /* جدول غير موجود - مُعلق مؤقتاً */'
        count = content.count(old_pattern)
        content = content.replace(old_pattern, new_replacement)
        replacements_made += count
        if count > 0:
            print(f"✅ استبدال {count} من: {old_pattern}")
    
    # النمط 3: FROM " . DB_PREFIX . "table_name
    pattern3 = re.compile(r'FROM " \. DB_PREFIX \. "([^"]+)')
    matches3 = pattern3.findall(content)
    for table_name in set(matches3):
        old_pattern = f'FROM " . DB_PREFIX . "{table_name}'
        new_replacement = f'FROM "cod_{table_name} /* جدول غير موجود - مُعلق مؤقتاً */'
        count = content.count(old_pattern)
        content = content.replace(old_pattern, new_replacement)
        replacements_made += count
        if count > 0:
            print(f"✅ استبدال {count} من: {old_pattern}")
    
    # النمط 4: JOIN " . DB_PREFIX . "table_name
    pattern4 = re.compile(r'JOIN " \. DB_PREFIX \. "([^"]+)')
    matches4 = pattern4.findall(content)
    for table_name in set(matches4):
        old_pattern = f'JOIN " . DB_PREFIX . "{table_name}'
        new_replacement = f'JOIN "cod_{table_name} /* جدول غير موجود - مُعلق مؤقتاً */'
        count = content.count(old_pattern)
        content = content.replace(old_pattern, new_replacement)
        replacements_made += count
        if count > 0:
            print(f"✅ استبدال {count} من: {old_pattern}")
    
    # حفظ الملف المُعدل
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    # إحصائيات النتائج
    final_count = content.count('DB_PREFIX')
    
    print(f"\n📊 تقرير الاستبدال النهائي:")
    print(f"✅ إجمالي الاستبدالات: {replacements_made}")
    print(f"📉 قبل: {original_count} استخدام DB_PREFIX")
    print(f"📈 بعد: {final_count} استخدام DB_PREFIX")
    print(f"🎯 تم تصحيح: {original_count - final_count} استخدام")
    
    if final_count == 0:
        print("\n🎉 تم تصحيح جميع استخدامات DB_PREFIX بنجاح!")
        print("✅ لوحة التحكم جاهزة للعمل مع الجداول الموجودة فقط")
    else:
        print(f"\n⚠️  لا تزال هناك {final_count} استخدامات تحتاج مراجعة يدوية")
    
    print(f"\n📁 النسخة الاحتياطية: {backup_file}")
    print("🔧 انتهى الاستبدال النهائي!")

if __name__ == "__main__":
    main()
