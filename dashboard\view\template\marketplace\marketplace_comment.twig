{% if comments %}
{% for comment in comments %}
<div id="comment{{ comment.extension_comment_id }}" class="media">
  <div class="media-left"><img src="{{ comment.image }}" alt="{{ comment.member }}" title="{{ comment.member }}" class="media-object" /></div>
  <div class="media-body">
    <p class="media-heading">{{ comment.member }} <span>{{ comment.date_added }}</span></p>
    <p>{{ comment.comment }}</p>
    <div class="reply">
      <div> {% if comment.reply %}
        
        {% for reply in comment.reply %}
        <div class="media">
          <div class="media-left"><img src="{{ reply.image }}" alt="{{ reply.member }}" title="{{ reply.member }}" class="media-object" /></div>
          <div class="media-body">
            <p class="media-heading">{{ reply.member }} <span>{{ reply.date_added }}</span></p>
            <p>{{ reply.comment }}</p>
          </div>
        </div>
        {% endfor %}
        
        {% if comment.next %}
        <div class="text-center"><a href="{{ comment.next }}" class="btn btn-block">{{ text_see_more }}</a></div>
        {% endif %}
        
        {% endif %} <a href="{{ comment.refresh }}" class="reply-refresh hide">{{ button_refresh }}</a> </div>
    </div>
    <p class="text-right">
      <button type="button" class="btn btn-link btn-xs">{{ button_reply }}</button>
    </p>
    <div class="reply-input-box" style="display: none;">
      <div class="media">
        <div class="media-body">
          <div class="form-group">
            <label for="input-comment{{ comment.extension_comment_id }}">{{ text_comment_add }}</label>
            <textarea name="comment" placeholder="Write your comment here..." id="input-comment{{ comment.extension_comment_id }}" class="form-control"></textarea>
          </div>
          <div class="text-right"><a href="{{ comment.add }}" class="btn btn-primary btn-sm">{{ button_comment }}</a></div>
        </div>
      </div>
    </div>
  </div>
</div>
</div>
{% endfor %} <br/>
<div class="text-center">{{ pagination }}</div>
{% endif %} 
