<?php
/**
 * كونترولر تقييم المخزون المتقدم - Enterprise Grade
 * 
 * التحسينات الجديدة:
 * - تطبيق الخدمات المركزية (central_service_manager.php)
 * - نظام الصلاحيات المزدوج (hasPermission + hasKey)
 * - تسجيل شامل للأنشطة (logActivity)
 * - نظام الإشعارات المتقدم (sendNotification)
 * - معالجة الأخطاء الشاملة (try-catch)
 * - تقييم بطرق متعددة (FIFO, LIFO, WAC, Standard Cost)
 * - تكامل مع النظام المحاسبي المتقدم
 * - تقارير تقييم تفصيلية ومقارنة
 * - تحليلات ربحية المخزون
 * - تقييم حسب الفروع والمخازن
 * - تقييم حسب التصنيفات والموردين
 * - تقارير تاريخية للتقييم
 * 
 * <AUTHOR> Team - Enhanced by Kiro AI
 * @version 2.0 - Enterprise Grade
 * @since 2025
 * @reference current_stock.php - Proven Example
 */

class ControllerInventoryStockValuation extends Controller {
    private $error = array();
    private $central_service;
    
    public function __construct($registry) {
        parent::__construct($registry);
        
        // تحميل الخدمات المركزية
        $this->load->model('common/central_service_manager');
        $this->central_service = new CentralServiceManager($this->registry);
    }
    
    /**
     * الصفحة الرئيسية - عرض تقييم المخزون
     */
    public function index() {
        try {
            // التحقق من الصلاحيات الأساسية والمتقدمة
            if (!$this->user->hasPermission('access', 'inventory/stock_valuation')) {
                $this->central_service->logActivity(
                    'access_denied',
                    'stock_valuation',
                    'محاولة وصول غير مصرح به لشاشة تقييم المخزون',
                    array('user_id' => $this->user->getId())
                );
                
                $this->response->redirect($this->url->link('error/permission', 'user_token=' . $this->session->data['user_token'], true));
            }
            
            // التحقق من الصلاحيات المتقدمة
            if (!$this->user->hasKey('stock_valuation_view')) {
                $this->central_service->logActivity(
                    'access_denied_advanced',
                    'stock_valuation',
                    'محاولة وصول بصلاحيات متقدمة غير مصرح بها لعرض تقييم المخزون',
                    array('user_id' => $this->user->getId())
                );
                
                $this->session->data['warning'] = $this->language->get('error_advanced_permission');
            }
            
            // تسجيل النشاط
            $this->central_service->logActivity(
                'view',
                'stock_valuation',
                'عرض تقييم المخزون',
                array('user_id' => $this->user->getId())
            );
            
            // تحميل اللغة والنماذج
            $this->load->language('inventory/stock_valuation');
            $this->document->setTitle($this->language->get('heading_title'));
            
            $this->load->model('inventory/stock_valuation');
            $this->load->model('inventory/product');
            $this->load->model('inventory/warehouse');
            
            // عرض القائمة
            $this->getList();
            
        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'stock_valuation',
                'خطأ في عرض تقييم المخزون: ' . $e->getMessage(),
                array('error' => $e->getTraceAsString())
            );
            
            $this->session->data['error'] = $this->language->get('error_exception');
            $this->response->redirect($this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true));
        }
    }
    
    /**
     * دالة عرض القائمة مع الفلاتر المتقدمة
     */
    protected function getList() {
        // القيم الافتراضية للفلاتر
        $filter_warehouse_id = '';
        $filter_category_id = '';
        $filter_manufacturer_id = '';
        $filter_valuation_method = 'wac'; // الافتراضي: المتوسط المرجح
        $filter_date = date('Y-m-d');
        $filter_include_zero_stock = 0;
        $filter_sort = 'pd.name';
        $filter_order = 'ASC';
        $page = 1;
        $limit = $this->config->get('config_limit_admin');
        
        // معالجة المدخلات والفلاتر
        if (isset($this->request->get['filter_warehouse_id'])) {
            $filter_warehouse_id = $this->request->get['filter_warehouse_id'];
        }
        
        if (isset($this->request->get['filter_category_id'])) {
            $filter_category_id = $this->request->get['filter_category_id'];
        }
        
        if (isset($this->request->get['filter_manufacturer_id'])) {
            $filter_manufacturer_id = $this->request->get['filter_manufacturer_id'];
        }
        
        if (isset($this->request->get['filter_valuation_method'])) {
            $filter_valuation_method = $this->request->get['filter_valuation_method'];
        }
        
        if (isset($this->request->get['filter_date'])) {
            $filter_date = $this->request->get['filter_date'];
        }
        
        if (isset($this->request->get['filter_include_zero_stock'])) {
            $filter_include_zero_stock = $this->request->get['filter_include_zero_stock'];
        }
        
        if (isset($this->request->get['sort'])) {
            $filter_sort = $this->request->get['sort'];
        }
        
        if (isset($this->request->get['order'])) {
            $filter_order = $this->request->get['order'];
        }
        
        if (isset($this->request->get['page'])) {
            $page = (int)$this->request->get['page'];
        }
        
        // إعداد البيانات للفلاتر
        $filter_data = array(
            'filter_warehouse_id'      => $filter_warehouse_id,
            'filter_category_id'       => $filter_category_id,
            'filter_manufacturer_id'   => $filter_manufacturer_id,
            'filter_valuation_method'  => $filter_valuation_method,
            'filter_date'              => $filter_date,
            'filter_include_zero_stock'=> $filter_include_zero_stock,
            'sort'                     => $filter_sort,
            'order'                    => $filter_order,
            'start'                    => ($page - 1) * $limit,
            'limit'                    => $limit
        );
        
        // الحصول على بيانات التقييم
        $valuations = $this->model_inventory_stock_valuation->getStockValuations($filter_data);
        $valuation_total = $this->model_inventory_stock_valuation->getTotalStockValuations($filter_data);
        
        // إعداد البيانات للعرض
        $data['valuations'] = array();
        
        foreach ($valuations as $valuation) {
            $data['valuations'][] = array(
                'product_id'        => $valuation['product_id'],
                'product_name'      => $valuation['product_name'],
                'model'             => $valuation['model'],
                'sku'               => $valuation['sku'],
                'warehouse_name'    => $valuation['warehouse_name'],
                'category_name'     => $valuation['category_name'],
                'manufacturer_name' => $valuation['manufacturer_name'],
                'quantity'          => number_format($valuation['quantity'], 2),
                'unit_cost'         => number_format($valuation['unit_cost'], 4),
                'total_value'       => number_format($valuation['total_value'], 2),
                'valuation_method'  => $valuation['valuation_method'],
                'last_updated'      => date($this->language->get('date_format_short'), strtotime($valuation['last_updated'])),
                'profit_margin'     => isset($valuation['profit_margin']) ? number_format($valuation['profit_margin'], 2) . '%' : 'N/A',
                'turnover_ratio'    => isset($valuation['turnover_ratio']) ? number_format($valuation['turnover_ratio'], 2) : 'N/A'
            );
        }
        
        // حساب الإجماليات
        $summary = $this->model_inventory_stock_valuation->getValuationSummary($filter_data);
        
        // إعداد الروابط والبيانات الأخرى
        $url = '';
        
        if (isset($this->request->get['filter_warehouse_id'])) {
            $url .= '&filter_warehouse_id=' . $this->request->get['filter_warehouse_id'];
        }
        
        if (isset($this->request->get['filter_category_id'])) {
            $url .= '&filter_category_id=' . $this->request->get['filter_category_id'];
        }
        
        if (isset($this->request->get['filter_manufacturer_id'])) {
            $url .= '&filter_manufacturer_id=' . $this->request->get['filter_manufacturer_id'];
        }
        
        if (isset($this->request->get['filter_valuation_method'])) {
            $url .= '&filter_valuation_method=' . $this->request->get['filter_valuation_method'];
        }
        
        if (isset($this->request->get['filter_date'])) {
            $url .= '&filter_date=' . $this->request->get['filter_date'];
        }
        
        if (isset($this->request->get['filter_include_zero_stock'])) {
            $url .= '&filter_include_zero_stock=' . $this->request->get['filter_include_zero_stock'];
        }
        
        if (isset($this->request->get['sort'])) {
            $url .= '&sort=' . $this->request->get['sort'];
        }
        
        if (isset($this->request->get['order'])) {
            $url .= '&order=' . $this->request->get['order'];
        }
        
        if (isset($this->request->get['page'])) {
            $url .= '&page=' . $this->request->get['page'];
        }
        
        // إعداد التصفح
        $pagination = new Pagination();
        $pagination->total = $valuation_total;
        $pagination->page = $page;
        $pagination->limit = $limit;
        $pagination->url = $this->url->link('inventory/stock_valuation', 'user_token=' . $this->session->data['user_token'] . $url . '&page={page}', true);
        
        $data['pagination'] = $pagination->render();
        $data['results'] = sprintf($this->language->get('text_pagination'), ($valuation_total) ? (($page - 1) * $limit) + 1 : 0, ((($page - 1) * $limit) > ($valuation_total - $limit)) ? $valuation_total : ((($page - 1) * $limit) + $limit), $valuation_total, ceil($valuation_total / $limit));
        
        // إعداد البيانات الإضافية
        $data['filter_warehouse_id'] = $filter_warehouse_id;
        $data['filter_category_id'] = $filter_category_id;
        $data['filter_manufacturer_id'] = $filter_manufacturer_id;
        $data['filter_valuation_method'] = $filter_valuation_method;
        $data['filter_date'] = $filter_date;
        $data['filter_include_zero_stock'] = $filter_include_zero_stock;
        $data['sort'] = $filter_sort;
        $data['order'] = $filter_order;
        
        // الحصول على قوائم الفلاتر
        $data['warehouses'] = $this->model_inventory_warehouse->getWarehouses();
        $data['categories'] = $this->model_inventory_product->getCategories();
        $data['manufacturers'] = $this->model_inventory_product->getManufacturers();
        $data['valuation_methods'] = $this->getValuationMethods();
        
        // ملخص التقييم
        $data['summary'] = $summary;
        
        // الروابط
        $data['breadcrumbs'] = array();
        
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );
        
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('inventory/stock_valuation', 'user_token=' . $this->session->data['user_token'] . $url, true)
        );
        
        $data['recalculate'] = $this->url->link('inventory/stock_valuation/recalculate', 'user_token=' . $this->session->data['user_token'], true);
        $data['compare'] = $this->url->link('inventory/stock_valuation/compare', 'user_token=' . $this->session->data['user_token'], true);
        $data['analytics'] = $this->url->link('inventory/stock_valuation/analytics', 'user_token=' . $this->session->data['user_token'], true);
        $data['export'] = $this->url->link('inventory/stock_valuation/export', 'user_token=' . $this->session->data['user_token'] . $url, true);
        
        $data['sort_product'] = $this->url->link('inventory/stock_valuation', 'user_token=' . $this->session->data['user_token'] . '&sort=pd.name' . $url, true);
        $data['sort_warehouse'] = $this->url->link('inventory/stock_valuation', 'user_token=' . $this->session->data['user_token'] . '&sort=w.name' . $url, true);
        $data['sort_quantity'] = $this->url->link('inventory/stock_valuation', 'user_token=' . $this->session->data['user_token'] . '&sort=quantity' . $url, true);
        $data['sort_unit_cost'] = $this->url->link('inventory/stock_valuation', 'user_token=' . $this->session->data['user_token'] . '&sort=unit_cost' . $url, true);
        $data['sort_total_value'] = $this->url->link('inventory/stock_valuation', 'user_token=' . $this->session->data['user_token'] . '&sort=total_value' . $url, true);
        
        $data['user_token'] = $this->session->data['user_token'];
        
        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');
        
        $this->response->setOutput($this->load->view('inventory/stock_valuation', $data));
    }
    
    /**
     * إعادة حساب التقييم
     */
    public function recalculate() {
        try {
            // التحقق من الصلاحيات
            if (!$this->user->hasPermission('modify', 'inventory/stock_valuation') || !$this->user->hasKey('stock_valuation_recalculate')) {
                $this->response->redirect($this->url->link('error/permission', 'user_token=' . $this->session->data['user_token'], true));
            }
            
            $this->load->language('inventory/stock_valuation');
            $this->load->model('inventory/stock_valuation');
            
            // تسجيل النشاط
            $this->central_service->logActivity(
                'recalculate',
                'stock_valuation',
                'إعادة حساب تقييم المخزون',
                array('user_id' => $this->user->getId())
            );
            
            // إعادة حساب التقييم
            $result = $this->model_inventory_stock_valuation->recalculateValuation();
            
            if ($result['success']) {
                $this->session->data['success'] = sprintf($this->language->get('text_recalculate_success'), $result['updated_products']);
                
                // إرسال إشعار
                $this->central_service->sendNotification(
                    'stock_valuation_recalculated',
                    'تم إعادة حساب تقييم المخزون',
                    sprintf('تم تحديث تقييم %d منتج', $result['updated_products']),
                    array('user_id' => $this->user->getId())
                );
            } else {
                $this->session->data['error'] = $this->language->get('error_recalculate_failed');
            }
            
        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'stock_valuation_recalculate',
                'خطأ في إعادة حساب التقييم: ' . $e->getMessage(),
                array('error' => $e->getTraceAsString())
            );
            
            $this->session->data['error'] = $this->language->get('error_exception');
        }
        
        $this->response->redirect($this->url->link('inventory/stock_valuation', 'user_token=' . $this->session->data['user_token'], true));
    }
    
    /**
     * الحصول على طرق التقييم المتاحة
     */
    private function getValuationMethods() {
        return array(
            'fifo'         => $this->language->get('text_fifo'),
            'lifo'         => $this->language->get('text_lifo'),
            'wac'          => $this->language->get('text_wac'),
            'standard'     => $this->language->get('text_standard_cost'),
            'latest'       => $this->language->get('text_latest_cost'),
            'average'      => $this->language->get('text_average_cost')
        );
    }
}
