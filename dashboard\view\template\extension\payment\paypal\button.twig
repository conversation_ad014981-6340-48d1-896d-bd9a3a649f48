{{ header }}{{ column_left }}
<div id="content" class="payment-paypal">
	<div class="page-header">
		<div class="container-fluid">
			<div class="pull-right">
				<button data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary button-save"><i class="fa fa-save"></i></button>
				<a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a>
			</div>
			<h1>{{ heading_title_main }}</h1>
			<ul class="breadcrumb">
				{% for breadcrumb in breadcrumbs %}
				<li><a href="{{ breadcrumb['href'] }}">{{ breadcrumb['text'] }}</a></li>
				{% endfor %}
			</ul>
		</div>
	</div>
	<div class="container-fluid">
		{% if error_warning %}
		<div class="alert alert-danger"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}</div>
		{% endif %}
		{% if text_version %}
		<div class="alert alert-info"><i class="fa fa-info-circle"></i> {{ text_version }}</div>
		{% endif %}
		<div class="panel panel-default">
			<div class="panel-heading">
				<h3 class="panel-title"><i class="fa fa-pencil"></i> {{ text_edit }}</h3>
			</div>
			<div class="panel-body">
				<form action="{{ action }}" method="post" enctype="multipart/form-data" id="form_payment">
					<a href="{{ href_dashboard }}" class="back-dashboard"><i class="icon icon-back-dashboard"></i>{{ text_tab_dashboard }}</a>
					<ul class="nav nav-tabs">
						<li class="nav-tab"><a href="{{ href_general }}" class="tab"><i class="tab-icon tab-icon-general"></i><span class="tab-title">{{ text_tab_general }}</span></a></li>
						<li class="nav-tab active"><a href="{{ href_button }}" class="tab"><i class="tab-icon tab-icon-button"></i><span class="tab-title">{{ text_tab_button }}</span></a></li>
						<li class="nav-tab"><a href="{{ href_googlepay_button }}" class="tab"><i class="tab-icon tab-icon-googlepay-button"></i><span class="tab-title">{{ text_tab_googlepay_button }}</span></a></li>
						<li class="nav-tab"><a href="{{ href_applepay_button }}" class="tab"><i class="tab-icon tab-icon-applepay-button"></i><span class="tab-title">{{ text_tab_applepay_button }}</span></a></li>
						<li class="nav-tab"><a href="{{ href_card }}" class="tab"><i class="tab-icon tab-icon-card"></i><span class="tab-title">{{ text_tab_card }}</span></a></li>
						<li class="nav-tab"><a href="{{ href_message_configurator }}" class="tab"><i class="tab-icon tab-icon-message-configurator"></i><span class="tab-title">{{ text_tab_message_configurator }}</span></a></li>
						<li class="nav-tab"><a href="{{ href_message_setting }}" class="tab"><i class="tab-icon tab-icon-message-setting"></i><span class="tab-title">{{ text_tab_message_setting }}</span></a></li>
						<li class="nav-tab"><a href="{{ href_order_status }}" class="tab"><i class="tab-icon tab-icon-order-status"></i><span class="tab-title">{{ text_tab_order_status }}</span></a></li>
						<li class="nav-tab"><a href="{{ href_contact }}" class="tab"><i class="tab-icon tab-icon-contact"></i><span class="tab-title">{{ text_tab_contact }}</span></a></li>
					</ul>
					<div class="section-content">
						<ul class="nav nav-pills">
							{% for button in setting['button'] %}
							<li class="nav-pill {% if loop.first %}active{% endif %}"><a href="#pill_{{ button['page_code'] }}" class="pill" data-toggle="tab">{{ attribute(_context, button['page_name']) }}</a></li>
							{% endfor %}
						</ul>
						<hr class="hr" />
						<div class="tab-content">
							{% for button in setting['button'] %}
							<div id="pill_{{ button['page_code'] }}" class="tab-pane {% if loop.first %}active{% endif %}">
								<div class="row">
									<div class="col col-lg-6">
										{% if (button['page_code'] == 'checkout') %}
										<div class="section-checkout">
											<div class="section-title">{{ text_checkout }}</div>
											<div class="section-panel">
												<div class="section-panel-title">{{ text_step_payment_method }}<i class="icon icon-section-panel"></i></div>
											</div>
											<div class="section-panel">
												<div class="section-panel-title">{{ text_step_confirm_order }}<i class="icon icon-section-panel"></i></div>
											</div>
											<div class="table-totals">
												<div class="row">
													<div class="col col-md-offset-6 col-md-6">
														<div class="row row-total">
															<div class="col col-xs-6 col-title">{{ text_cart_sub_total }}</div>
															<div class="col col-xs-6 col-price">{{ text_cart_product_total_value }}</div>
														</div>
														<div class="row row-total">
															<div class="col col-xs-6 col-title">{{ text_cart_total }}</div>
															<div class="col col-xs-6 col-price">{{ text_cart_product_total_value }}</div>
														</div>
													</div>
												</div>
											</div>
											<div id="paypal_button_checkout" class="paypal-button">
												<div id="paypal_button_checkout_container" class="paypal-button-container paypal-spinner"></div>
											</div>
										</div>
										{% endif %}
										{% if (button['page_code'] == 'cart') %}
										<div class="section-cart">
											<div class="section-title">{{ text_cart }}</div>
											<div class="table-cart">
												<div class="table-row table-row-header">
													<div class="table-col table-col-product-image">{{ text_cart_product_image }}</div>
													<div class="table-col table-col-product-name">{{ text_cart_product_name }}</div>
													<div class="table-col table-col-product-model">{{ text_cart_product_model }}</div>
													<div class="table-col table-col-product-quantity">{{ text_cart_product_quantity }}</div>
													<div class="table-col table-col-product-price">{{ text_cart_product_price }}</div>
													<div class="table-col table-col-product-total">{{ text_cart_product_total }}</div>
												</div>
												<div class="table-row">
													<div class="table-col table-col-product-image"><div class="product-image"></div></div>
													<div class="table-col table-col-product-name">{{ text_cart_product_name_value }}</div>
													<div class="table-col table-col-product-model">{{ text_cart_product_model_value }}</div>
													<div class="table-col table-col-product-quantity">{{ text_cart_product_quantity_value }}</div>
													<div class="table-col table-col-product-price">{{ text_cart_product_price_value }}</div>
													<div class="table-col table-col-product-total">{{ text_cart_product_total_value }}</div>
												</div>
											</div>	
											<div class="section-panel">
												<div class="section-panel-title">{{ text_step_coupon }}<i class="icon icon-section-panel"></i></div>
											</div>
											<div class="section-panel">
												<div class="section-panel-title">{{ text_step_shipping }}<i class="icon icon-section-panel"></i></div>
											</div>
											<div class="table-totals">
												<div class="row">
													<div class="col col-md-offset-6 col-md-6">
														<div class="row row-total">
															<div class="col col-xs-6 col-title">{{ text_cart_sub_total }}</div>
															<div class="col col-xs-6 col-price">{{ text_cart_product_total_value }}</div>
														</div>
														<div class="row row-total">
															<div class="col col-xs-6 col-title">{{ text_cart_total }}</div>
															<div class="col col-xs-6 col-price">{{ text_cart_product_total_value }}</div>
														</div>
													</div>
												</div>
											</div>
											<div class="row">
												<div class="col col-md-offset-7 col-md-5">
													<button type="button" class="btn button-cart">{{ button_checkout }}</button>
												</div>
											</div>
											<div id="paypal_button_cart" class="paypal-button">
												<div id="paypal_button_cart_container" class="paypal-button-container paypal-spinner"></div>
											</div>
										</div>
										{% endif %}
										{% if (button['page_code'] == 'product') %}
										<div class="section-product">
											<div class="row">
												<div class="col col-sm-6">
													<div class="product-image"></div>
												</div>
												<div class="col col-sm-6">
													<div class="product-name">{{ text_product_name }}</div>
													<div class="product-price">{{ text_product_price }}</div>
													<div class="product-manufacturer">{{ text_product_manufacturer }}</div>
													<div class="product-model">{{ text_product_model }}</div>
													<div class="product-stock">{{ text_product_stock }}</div>
													<button type="button" class="btn button-cart">{{ button_cart }}</button>
													<div id="paypal_button_product" class="paypal-button">
														<div id="paypal_button_product_container" class="paypal-button-container paypal-spinner"></div>
													</div>
												</div>
											</div>
										</div>
										{% endif %}
									</div>
									<div class="col col-lg-6">
										<div class="section-button-setting">
											<div class="row">
												<div class="col col-md-6">
													<legend class="legend">{{ text_button_settings }}</legend>
												</div>
												<div class="col col-md-6">
													<div class="form-group-status">
														<label class="control-label" for="input_button_{{ button['page_code'] }}_status"><span data-toggle="tooltip" title="{{ help_button_status }}">{{ entry_status }}</span></label>
														<input type="hidden" name="payment_paypal_setting[button][{{ button['page_code'] }}][status]" value="0" />
														<input type="checkbox" name="payment_paypal_setting[button][{{ button['page_code'] }}][status]" value="1" class="switch" {% if button['status'] %}checked="checked"{% endif %} />
													</div>
												</div>
											</div>
											{% if (button['page_code'] != 'checkout') %}
											<div class="row">
												<div class="col col-md-6">
													<div class="form-group">
														<label class="control-label" for="input_button_{{ button['page_code'] }}_insert_tag">{{ entry_button_insert_tag }}</label>
														<input type="text" name="payment_paypal_setting[button][{{ button['page_code'] }}][insert_tag]" value="{{ button['insert_tag'] }}" id="input_button_{{ button['page_code'] }}_insert_tag" class="form-control" />
													</div>
												</div>
												<div class="col col-md-6">
													<div class="form-group">
														<label class="control-label" for="input_button_{{ button['page_code'] }}_insert_type">{{ entry_button_insert_type }}</label>
														<select name="payment_paypal_setting[button][{{ button['page_code'] }}][insert_type]" id="input_button_{{ button['page_code'] }}_insert_type" class="form-control">
															{% for button_insert_type in setting['button_insert_type'] %}
															{% if (button_insert_type['code'] == button['insert_type']) %}
															<option value="{{ button_insert_type['code'] }}" selected="selected">{{ attribute(_context, button_insert_type['name']) }}</option>
															{% else %}
															<option value="{{ button_insert_type['code'] }}">{{ attribute(_context, button_insert_type['name']) }}</option>
															{% endif %}
															{% endfor %}
														</select>
													</div>
												</div>
											</div>
											{% endif %}
											<div class="row">
												<div class="col col-md-6">
													<div class="form-group">
														<label class="control-label" for="input_button_{{ button['page_code'] }}_align">{{ entry_button_align }}</label>
														<select name="payment_paypal_setting[button][{{ button['page_code'] }}][align]" id="input_button_{{ button['page_code'] }}_align" class="form-control control-paypal-button">
															{% for button_align in setting['button_align'] %}
															{% if (button_align['code'] == button['align']) %}
															<option value="{{ button_align['code'] }}" selected="selected">{{ attribute(_context, button_align['name']) }}</option>
															{% else %}
															<option value="{{ button_align['code'] }}">{{ attribute(_context, button_align['name']) }}</option>
															{% endif %}
															{% endfor %}
														</select>
													</div>
													<div class="form-group">
														<label class="control-label" for="input_button_{{ button['page_code'] }}_size">{{ entry_button_size }}</label>
														<select name="payment_paypal_setting[button][{{ button['page_code'] }}][size]" id="input_button_{{ button['page_code'] }}_size" class="form-control control-paypal-button">
															{% for button_size in setting['button_size'] %}
															{% if (button_size['code'] == button['size']) %}
															<option value="{{ button_size['code'] }}" selected="selected">{{ attribute(_context, button_size['name']) }}</option>
															{% else %}
															<option value="{{ button_size['code'] }}">{{ attribute(_context, button_size['name']) }}</option>
															{% endif %}
															{% endfor %}
														</select>
													</div>
													<div class="form-group">
														<label class="control-label" for="input_button_{{ button['page_code'] }}_color">{{ entry_button_color }}</label>
														<select name="payment_paypal_setting[button][{{ button['page_code'] }}][color]" id="input_button_{{ button['page_code'] }}_color" class="form-control control-paypal-button">
															{% for button_color in setting['button_color'] %}
															{% if (button_color['code'] == button['color']) %}
															<option value="{{ button_color['code'] }}" selected="selected">{{ attribute(_context, button_color['name']) }}</option>
															{% else %}
															<option value="{{ button_color['code'] }}">{{ attribute(_context, button_color['name']) }}</option>
															{% endif %}
															{% endfor %}
														</select>
													</div>
												</div>
												<div class="col col-md-6">
													<div class="form-group">
														<label class="control-label" for="input_button_{{ button['page_code'] }}_shape">{{ entry_button_shape }}</label>
														<select name="payment_paypal_setting[button][{{ button['page_code'] }}][shape]" id="input_button_{{ button['page_code'] }}_shape" class="form-control control-paypal-button">
															{% for button_shape in setting['button_shape'] %}
															{% if (button_shape['code'] == button['shape']) %}
															<option value="{{ button_shape['code'] }}" selected="selected">{{ attribute(_context, button_shape['name']) }}</option>
															{% else %}
															<option value="{{ button_shape['code'] }}">{{ attribute(_context, button_shape['name']) }}</option>
															{% endif %}
															{% endfor %}
														</select>
													</div>
													<div class="form-group">
														<label class="control-label" for="input_button_{{ button['page_code'] }}_label">{{ entry_button_label }}</label>
														<select name="payment_paypal_setting[button][{{ button['page_code'] }}][label]" id="input_button_{{ button['page_code'] }}_label" class="form-control control-paypal-button">
															{% for button_label in setting['button_label'] %}
															{% if (button_label['code'] == button['label']) %}
															<option value="{{ button_label['code'] }}" selected="selected">{{ attribute(_context, button_label['name']) }}</option>
															{% else %}
															<option value="{{ button_label['code'] }}">{{ attribute(_context, button_label['name']) }}</option>
															{% endif %}
															{% endfor %}
														</select>
													</div>
												</div>
											</div>
											<hr class="hr" />
											<button type="button" href="#all_settings_{{ button['page_code'] }}" class="btn btn-default button-all-settings collapsed" data-toggle="collapse" role="button">{{ button_all_settings }}<i class="icon icon-all-settings"></i></button>	
											<div id="all_settings_{{ button['page_code'] }}" class="all-settings collapse">
												<div class="row">
													{% for column_button_funding in setting['button_funding']|batch(setting['button_funding']|length / 2|round(1, 'ceil')) %}
													<div class="col col-md-6">
														{% for button_funding in column_button_funding %}
														<div class="form-group">
															<label class="control-label" for="input_button_{{ button['page_code'] }}_funding_{{ button_funding['code'] }}">{{ attribute(_context, button_funding['name']) }}</label>
															<select name="payment_paypal_setting[button][{{ button['page_code'] }}][funding][{{ button_funding['code'] }}]" id="input_button_{{ button['page_code'] }}_funding_{{ button_funding['code'] }}" class="form-control control-paypal-button" funding_code="{{ button_funding['code'] }}">
																<option value="0" {% if (button['funding'][button_funding['code']] == 0) %}selected="selected"{% endif %}>{{ text_auto }}</option>
																<option value="1" {% if (button['funding'][button_funding['code']] == 1) %}selected="selected"{% endif %}>{{ text_enabled }}</option>
																<option value="2" {% if (button['funding'][button_funding['code']] == 2) %}selected="selected"{% endif %}>{{ text_disabled }}</option>	
															</select>
														</div>
														{% endfor %}
													</div>
													{% endfor %}
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
							{% endfor %}
						</div>
					</div>
				</form>
			</div>
		</div>
	</div>
</div>
<script type="text/javascript">

var button_width = JSON.parse('{{ setting['button_width']|json_encode() }}');

updatePayPalButton('checkout', function() {
	updatePayPalButton('cart', function() {
		updatePayPalButton('product');
	});
});

$('.payment-paypal .switch').bootstrapSwitch({
    'onColor': 'success',
    'onText': '{{ text_on }}',
    'offText': '{{ text_off }}'
});

$('.payment-paypal').on('change', '.control-paypal-button', function() {
	var page_code = $(this).parents('.tab-pane').attr('id').replace('pill_', '');
	
	updatePayPalButton(page_code);
});

$('.payment-paypal').on('click', '.button-save', function() {
    $.ajax({
		type: 'post',
		url: $('#form_payment').attr('action'),
		data: $('#form_payment').serialize(),
		dataType: 'json',
		success: function(json) {
			$('.payment-paypal .alert-success').remove();
			
			if (json['success']) {
				$('.payment-paypal > .container-fluid').prepend('<div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i><button type="button" class="close" data-dismiss="alert">&times;</button> ' + json['success'] + '</div>');
				
				$('html, body').animate({scrollTop: $('.payment-paypal > .container-fluid .alert-success').offset().top}, 'slow');
			}
		},
		error: function(xhr, ajaxOptions, thrownError) {
			console.log(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
		}
    });  
});

$('.payment-paypal').on('click', '.button-agree', function() {
	$.ajax({
		type: 'post',
		url: '{{ agree_url }}',
		data: '',
		dataType: 'json',
		success: function(json) {
			$('.payment-paypal .alert').remove();
			
			if (json['success']) {
				$('.payment-paypal > .container-fluid').prepend('<div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i><button type="button" class="close" data-dismiss="alert">&times;</button> ' + json['success'] + '</div>');
			}
		},
		error: function(xhr, ajaxOptions, thrownError) {
			console.log(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
		}
	});
});

function updatePayPalButton(button_page_code, paypal_callback = '') {
	var button_enable_funding = [];
	var button_disable_funding = [];

	$('.payment-paypal [id^=input_button_' + button_page_code + '_funding').each(function() {
		if ($(this).val() == 1) {
			button_enable_funding.push($(this).attr('funding_code'));
		}
	
		if ($(this).val() == 2) {
			button_disable_funding.push($(this).attr('funding_code'));
		}
	});
								
	var paypal_data = {};

	paypal_data['page_code'] = button_page_code;
	paypal_data['client_id'] = '{{ client_id }}';
	paypal_data['secret'] = '{{ secret }}';
	paypal_data['merchant_id'] = '{{ merchant_id }}';
	paypal_data['environment'] = '{{ environment }}';
	paypal_data['partner_attribution_id'] = '{{ partner_attribution_id }}';
	paypal_data['locale'] = '{{ locale }}';
	paypal_data['currency_code'] = '{{ currency_code }}';
	paypal_data['currency_value'] = '{{ currency_value }}';
	paypal_data['decimal_place'] = '{{ decimal_place }}';
	paypal_data['client_token'] = '{{ client_token }}';
	paypal_data['transaction_method'] = '{{ setting['general']['transaction_method'] }}';
	paypal_data['components'] = ['buttons'];
	paypal_data['button_align'] = $('.payment-paypal #input_button_' + button_page_code + '_align').val();
	paypal_data['button_size'] = $('.payment-paypal #input_button_' + button_page_code + '_size').val();
	paypal_data['button_color'] = $('.payment-paypal #input_button_' + button_page_code + '_color').val();
	paypal_data['button_shape'] = $('.payment-paypal #input_button_' + button_page_code + '_shape').val();
	paypal_data['button_label'] = $('.payment-paypal #input_button_' + button_page_code + '_label').val();
	
	if (button_page_code != 'checkout') {
		paypal_data['button_tagline'] = $('.payment-paypal #input_button_' + button_page_code + '_tagline').val();
	}
	
	paypal_data['button_width'] = button_width[paypal_data['button_size']];
	
	paypal_data['button_enable_funding'] = button_enable_funding;
	paypal_data['button_disable_funding'] = button_disable_funding;
		
	PayPalAPI.init(paypal_data, paypal_callback);
}

</script>
{{ footer }}