<?php
/**
 * مثال عملي لاستخدام نظام الصلاحيات المزدوج في AYM ERP
 * 
 * هذا المثال يوضح كيفية استخدام النظامين معاً للحصول على أقصى مرونة وأمان
 */

class ExampleAdvancedPermissionController extends Controller {
    
    /**
     * مثال على استخدام hasPermission للصلاحيات الأساسية
     */
    public function basicPermissionExample() {
        // التحقق من صلاحية الوصول للصفحة (النظام التقليدي)
        if (!$this->user->hasPermission('access', 'sale/order')) {
            return new Action('error/permission');
        }
        
        // التحقق من صلاحية التعديل (النظام التقليدي)
        if (!$this->user->hasPermission('modify', 'sale/order')) {
            $json['error'] = 'ليس لديك صلاحية تعديل الطلبات';
            return;
        }
        
        // المتابعة مع العملية...
        echo "تم السماح بالوصول والتعديل";
    }
    
    /**
     * مثال على استخدام hasKey للصلاحيات المتقدمة
     */
    public function advancedPermissionExample() {
        // التحقق من صلاحية متقدمة مخصصة
        if (!$this->user->hasKey('approve_large_orders')) {
            $json['error'] = 'ليس لديك صلاحية الموافقة على الطلبات الكبيرة';
            return;
        }
        
        // التحقق من صلاحية حذف السجلات المالية
        if (!$this->user->hasKey('delete_financial_records')) {
            $json['error'] = 'ليس لديك صلاحية حذف السجلات المالية';
            return;
        }
        
        // التحقق من صلاحية الوصول للتقارير الحساسة
        if (!$this->user->hasKey('view_sensitive_reports')) {
            $json['error'] = 'ليس لديك صلاحية عرض التقارير الحساسة';
            return;
        }
        
        // المتابعة مع العملية المتقدمة...
        echo "تم السماح بالعمليات المتقدمة";
    }
    
    /**
     * مثال على دمج النظامين للحصول على أقصى مرونة
     */
    public function hybridPermissionExample() {
        // التحقق الأساسي من الوصول للوحدة
        if (!$this->user->hasPermission('access', 'financial/reports')) {
            return new Action('error/permission');
        }
        
        // التحقق المتقدم من الصلاحيات الحساسة
        $can_view_sensitive = $this->user->hasKey('view_sensitive_financial_data');
        $can_export_data = $this->user->hasKey('export_financial_reports');
        $can_modify_reports = $this->user->hasPermission('modify', 'financial/reports');
        
        // بناء البيانات حسب الصلاحيات
        $data = [];
        
        if ($can_view_sensitive) {
            $data['sensitive_data'] = $this->getSensitiveFinancialData();
        } else {
            $data['sensitive_data'] = 'محجوب - ليس لديك صلاحية';
        }
        
        if ($can_export_data) {
            $data['export_button'] = true;
        }
        
        if ($can_modify_reports) {
            $data['edit_button'] = true;
        }
        
        // عرض التقرير مع الصلاحيات المناسبة
        $this->response->setOutput($this->load->view('financial/report', $data));
    }
    
    /**
     * مثال على التحقق من الصلاحيات المتدرجة
     */
    public function hierarchicalPermissionExample() {
        $order_amount = 50000; // مبلغ الطلب
        
        // صلاحيات متدرجة حسب المبلغ
        if ($order_amount > 100000) {
            // طلبات كبيرة جداً - تحتاج موافقة الإدارة العليا
            if (!$this->user->hasKey('approve_orders_above_100k')) {
                $json['error'] = 'هذا الطلب يحتاج موافقة الإدارة العليا';
                return;
            }
        } elseif ($order_amount > 50000) {
            // طلبات كبيرة - تحتاج موافقة المدير
            if (!$this->user->hasKey('approve_orders_above_50k')) {
                $json['error'] = 'هذا الطلب يحتاج موافقة المدير';
                return;
            }
        } elseif ($order_amount > 10000) {
            // طلبات متوسطة - تحتاج موافقة المشرف
            if (!$this->user->hasKey('approve_orders_above_10k')) {
                $json['error'] = 'هذا الطلب يحتاج موافقة المشرف';
                return;
            }
        }
        
        // التحقق من الصلاحية الأساسية للموافقة
        if (!$this->user->hasPermission('modify', 'sale/order')) {
            $json['error'] = 'ليس لديك صلاحية الموافقة على الطلبات';
            return;
        }
        
        echo "تمت الموافقة على الطلب بمبلغ: " . $order_amount;
    }
    
    /**
     * مثال على إدارة الصلاحيات الديناميكية
     */
    public function dynamicPermissionExample() {
        $user_branch = $this->user->getBranchId();
        $target_branch = $this->request->get['branch_id'];
        
        // التحقق من الصلاحية الأساسية
        if (!$this->user->hasPermission('access', 'inventory/stock')) {
            return new Action('error/permission');
        }
        
        // التحقق من صلاحية الوصول للفروع الأخرى
        if ($user_branch != $target_branch) {
            if (!$this->user->hasKey('access_other_branches')) {
                $json['error'] = 'ليس لديك صلاحية الوصول لفروع أخرى';
                return;
            }
        }
        
        // التحقق من صلاحية تعديل المخزون
        $can_modify = $this->user->hasPermission('modify', 'inventory/stock');
        
        // التحقق من صلاحية تعديل المخزون في فروع أخرى
        if ($user_branch != $target_branch && $can_modify) {
            if (!$this->user->hasKey('modify_other_branches_stock')) {
                $can_modify = false;
            }
        }
        
        $data['can_modify'] = $can_modify;
        $data['stock_data'] = $this->getStockData($target_branch);
        
        $this->response->setOutput($this->load->view('inventory/stock', $data));
    }
    
    /**
     * مثال على التحقق من الصلاحيات مع التخزين المؤقت
     */
    public function cachedPermissionExample() {
        // تخزين مؤقت للصلاحيات المتكررة لتحسين الأداء
        static $permission_cache = [];
        
        $permission_key = 'advanced_financial_operations';
        
        if (!isset($permission_cache[$permission_key])) {
            $permission_cache[$permission_key] = $this->user->hasKey($permission_key);
        }
        
        if (!$permission_cache[$permission_key]) {
            $json['error'] = 'ليس لديك صلاحية العمليات المالية المتقدمة';
            return;
        }
        
        // المتابعة مع العملية...
        echo "تم السماح بالعمليات المالية المتقدمة";
    }
    
    /**
     * مثال على إنشاء صلاحيات جديدة برمجياً
     */
    public function createNewPermissionExample() {
        // التحقق من صلاحية إدارة الصلاحيات
        if (!$this->user->hasPermission('modify', 'user/permission')) {
            return new Action('error/permission');
        }
        
        $this->load->model('user/permission');
        
        // إنشاء صلاحية جديدة
        $permission_data = [
            'name' => 'الموافقة على الطلبات فوق 500 ألف',
            'key' => 'approve_orders_above_500k',
            'type' => 'other'
        ];
        
        $permission_id = $this->model_user_permission->addPermission($permission_data);
        
        // ربط الصلاحية بمجموعة الإدارة العليا
        $this->model_user_permission->setUserGroupPermissions($permission_id, [1, 11]); // مجموعة الإدارة والرئيس التنفيذي
        
        // ربط الصلاحية بمستخدمين محددين
        $this->model_user_permission->setUserPermissions($permission_id, [1, 5]); // مستخدمين محددين
        
        echo "تم إنشاء الصلاحية الجديدة بنجاح";
    }
    
    /**
     * مثال على التحقق من الصلاحيات مع السجلات
     */
    public function auditedPermissionExample() {
        $sensitive_operation = 'delete_customer_data';
        
        // التحقق من الصلاحية
        if (!$this->user->hasKey($sensitive_operation)) {
            // تسجيل محاولة الوصول غير المصرح بها
            $this->user->logActivity(
                'unauthorized_access_attempt',
                'security',
                'محاولة وصول غير مصرح بها للعملية: ' . $sensitive_operation,
                'permission',
                null
            );
            
            $json['error'] = 'ليس لديك صلاحية هذه العملية';
            return;
        }
        
        // تسجيل العملية الحساسة
        $this->user->logActivity(
            'sensitive_operation',
            'data_management',
            'تم تنفيذ عملية حساسة: ' . $sensitive_operation,
            'permission',
            null
        );
        
        // تنفيذ العملية...
        echo "تم تنفيذ العملية الحساسة مع التسجيل";
    }
    
    /**
     * دوال مساعدة للمثال
     */
    private function getSensitiveFinancialData() {
        return [
            'profit_margin' => '25%',
            'cash_flow' => '1,500,000',
            'debt_ratio' => '0.3'
        ];
    }
    
    private function getStockData($branch_id) {
        return [
            'branch_id' => $branch_id,
            'total_items' => 1500,
            'total_value' => 750000
        ];
    }
}

/**
 * مثال على كيفية إعداد الصلاحيات في قاعدة البيانات
 */

/*
-- إدراج صلاحيات جديدة في جدول cod_permission
INSERT INTO cod_permission (name, `key`, type, date_added, date_modified) VALUES
('الموافقة على الطلبات فوق 10 آلاف', 'approve_orders_above_10k', 'other', NOW(), NOW()),
('الموافقة على الطلبات فوق 50 ألف', 'approve_orders_above_50k', 'other', NOW(), NOW()),
('الموافقة على الطلبات فوق 100 ألف', 'approve_orders_above_100k', 'other', NOW(), NOW()),
('عرض البيانات المالية الحساسة', 'view_sensitive_financial_data', 'access', NOW(), NOW()),
('تصدير التقارير المالية', 'export_financial_reports', 'other', NOW(), NOW()),
('حذف السجلات المالية', 'delete_financial_records', 'modify', NOW(), NOW()),
('الوصول للفروع الأخرى', 'access_other_branches', 'access', NOW(), NOW()),
('تعديل مخزون الفروع الأخرى', 'modify_other_branches_stock', 'modify', NOW(), NOW());

-- ربط الصلاحيات بمجموعة الإدارة (المجموعة 1)
INSERT INTO cod_user_group_permission (user_group_id, permission_id) 
SELECT 1, permission_id FROM cod_permission WHERE `key` IN (
    'approve_orders_above_100k',
    'view_sensitive_financial_data',
    'export_financial_reports',
    'delete_financial_records',
    'access_other_branches',
    'modify_other_branches_stock'
);

-- ربط صلاحيات محدودة بمجموعة المدراء (المجموعة 2)
INSERT INTO cod_user_group_permission (user_group_id, permission_id) 
SELECT 2, permission_id FROM cod_permission WHERE `key` IN (
    'approve_orders_above_50k',
    'access_other_branches'
);

-- ربط صلاحيات أساسية بمجموعة المشرفين (المجموعة 3)
INSERT INTO cod_user_group_permission (user_group_id, permission_id) 
SELECT 3, permission_id FROM cod_permission WHERE `key` IN (
    'approve_orders_above_10k'
);

-- منح صلاحية فردية لمستخدم محدد
INSERT INTO cod_user_permission (user_id, permission_id) 
SELECT 5, permission_id FROM cod_permission WHERE `key` = 'export_financial_reports';
*/

/**
 * ملاحظات مهمة للمطورين:
 * 
 * 1. استخدم hasPermission للصلاحيات الأساسية والسريعة
 * 2. استخدم hasKey للصلاحيات المتقدمة والحساسة
 * 3. المجموعة 1 لها صلاحيات كاملة تلقائياً
 * 4. يمكن دمج النظامين للحصول على مرونة أكبر
 * 5. استخدم التخزين المؤقت للصلاحيات المتكررة
 * 6. سجل العمليات الحساسة دائماً
 * 7. اختبر الصلاحيات في بداية كل دالة
 * 8. استخدم أسماء واضحة للصلاحيات المخصصة
 */