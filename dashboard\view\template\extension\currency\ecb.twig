{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        {% if currency_ecb_status %}
        <a href="{{ refresh }}" data-toggle="tooltip" title="{{ button_currency }}" class="btn btn-warning"><i class="fa fa fa-refresh"></i></a>
        {% endif %}
        <button type="submit" form="form-currency" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fa fa-save"></i></button>
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a></div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pencil"></i> {{ text_edit }}</h3>
      </div>
      <div class="panel-body">
        <form action="{{ action }}" method="post" enctype="multipart/form-data" id="form-currency" class="form-horizontal">
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-status">{{ entry_status }}</label>
            <div class="col-sm-10">
              <select name="currency_ecb_status" id="input-status" class="form-control">
                {% if currency_ecb_status %}
                <option value="1" selected="selected">{{ text_enabled }}</option>
                <option value="0">{{ text_disabled }}</option>
                {% else %}
                <option value="1">{{ text_enabled }}</option>
                <option value="0" selected="selected">{{ text_disabled }}</option>
                {% endif %}
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-ip"><span data-toggle="tooltip" title="{{ help_ip }}">{{ entry_ip }}</span></label>
            <div class="col-sm-10">
              <input type="text" name="currency_ecb_ip" value="{{ currency_ecb_ip }}" placeholder="{{ entry_ip }}" id="input-ip" class="form-control" />
              {% if (error_ip) %}
              <div class="text-danger">{{ error_ip }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-cron"><span data-toggle="tooltip" title="{{ help_cron }}">{{ entry_cron }}</span></label>
            <div class="col-sm-10">
              <input type="text" name="currency_ecb_cron" value="{{ currency_ecb_cron }}" placeholder="{{ entry_cron }}" id="input-cron" class="form-control" readonly="readonly" />
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
{{ footer }}