#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت Python لتصحيح جميع استخدامات DB_PREFIX المتبقية في dashboard.php
يطبق البروتوكول الجديد بصرامة - لا افتراضات، فقط الجداول الموجودة في db.txt
"""

import re
import os
from datetime import datetime

def main():
    file_path = "dashboard/model/common/dashboard.php"
    
    if not os.path.exists(file_path):
        print(f"❌ الملف غير موجود: {file_path}")
        return
    
    print("🔧 بدء تصحيح DB_PREFIX المتبقية...")
    
    # قراءة الملف
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # إنشاء نسخة احتياطية
    backup_file = f"{file_path}.backup.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    with open(backup_file, 'w', encoding='utf-8') as f:
        f.write(content)
    print(f"📁 تم إنشاء نسخة احتياطية: {backup_file}")
    
    # الجداول الموجودة فعلاً في db.txt (من الحصر الشامل)
    existing_tables = {
        # الجداول الأساسية
        'order': 'cod_order',
        'order_product': 'cod_order_product', 
        'order_total': 'cod_order_total',
        'order_history': 'cod_order_history',
        'order_option': 'cod_order_option',
        'order_shipment': 'cod_order_shipment',
        'customer': 'cod_customer',
        'customer_group': 'cod_customer_group',
        'customer_activity': 'cod_customer_activity',
        'customer_feedback': 'cod_customer_feedback',
        'customer_online': 'cod_customer_online',
        'product': 'cod_product',
        'product_description': 'cod_product_description',
        'product_inventory': 'cod_product_inventory',
        'product_movement': 'cod_product_movement',
        'product_batch': 'cod_product_batch',
        'category': 'cod_category',
        'category_description': 'cod_category_description',
        'category_path': 'cod_category_path',
        'supplier': 'cod_supplier',
        'supplier_address': 'cod_supplier_address',
        'supplier_evaluation': 'cod_supplier_evaluation',
        'supplier_invoice': 'cod_supplier_invoice',
        'purchase_order': 'cod_purchase_order',
        'purchase_order_item': 'cod_purchase_order_item',
        'purchase_order_history': 'cod_purchase_order_history',
        'branch': 'cod_branch',
        'branch_address': 'cod_branch_address',
        'user': 'cod_user',
        'user_group': 'cod_user_group',
        'user_session': 'cod_user_session',
        'user_activity_log': 'cod_user_activity_log',
        'cart': 'cod_cart',
        'cart_product': 'cod_cart_product',
        'abandoned_cart': 'cod_abandoned_cart',
        'return': 'cod_return',
        'review': 'cod_review',
        
        # الجداول المحاسبية
        'accounts': 'cod_accounts',
        'journal_entries': 'cod_journal_entries',
        'journals': 'cod_journals',
        'general_ledger_analysis': 'cod_general_ledger_analysis',
        'cash': 'cod_cash',
        'cash_flow_analysis': 'cod_cash_flow_analysis',
        'bank_account': 'cod_bank_account',
        'budget': 'cod_budget',
        
        # جداول CRM
        'crm_campaign': 'cod_crm_campaign',
        'crm_lead': 'cod_crm_lead',
        'crm_deal': 'cod_crm_deal',
        'crm_contact': 'cod_crm_contact',
        
        # جداول الشحن
        'shipping_order': 'cod_shipping_order',
        'shipping_company': 'cod_shipping_company',
        'shipping_rate': 'cod_shipping_rate',
        
        # جداول المخزون
        'inventory_alert': 'cod_inventory_alert',
        'inventory_transfer': 'cod_inventory_transfer',
        'inventory_count': 'cod_inventory_count',
        'stock_adjustment': 'cod_stock_adjustment',
        'stock_transfer': 'cod_stock_transfer',
        'stock_count': 'cod_stock_count',
        
        # جداول أخرى موجودة
        'currency': 'cod_currency',
        'language': 'cod_language',
        'setting': 'cod_setting',
        'statistics': 'cod_statistics',
        'notification_user': 'cod_notification_user',
        'unified_notification': 'cod_unified_notification',
        'task': 'cod_task',
        'project': 'cod_project',
        'meeting': 'cod_meeting',
        'eta_documents': 'cod_eta_documents',
        'eta_submissions': 'cod_eta_submissions',
        'attendance': 'cod_attendance',
        'leave_request': 'cod_leave_request',
        'performance_review': 'cod_performance_review',
        'employee_profile': 'cod_employee_profile',
        'goods_receipt': 'cod_goods_receipt',
        'product_to_category': 'cod_product_to_category'
    }
    
    # الجداول غير الموجودة - استبدالها بالبدائل
    non_existing_replacements = {
        'employee': 'cod_user',
        'hr_attendance': 'cod_attendance',
        'hr_leave_request': 'cod_leave_request',
        'hr_evaluation': 'cod_performance_review',
        'hr_job_position': 'cod_task',
        'invoice': 'cod_supplier_invoice',
        'shipment': 'cod_shipping_order',
        'project_task': 'cod_task',
        'calendar_event': 'cod_meeting',
        'marketing_campaign': 'cod_crm_campaign',
        'customer_complaint': 'cod_customer_feedback',
        'system_event': 'cod_activity_log',
        'user_activity': 'cod_user_activity_log',
        'branch_inventory_snapshot': 'cod_product_inventory',
        'order_item': 'cod_order_product',
        'email_campaign': 'cod_crm_campaign'
    }
    
    # دمج القوائم
    all_replacements = {**existing_tables, **non_existing_replacements}
    
    # تطبيق الاستبدالات
    original_count = content.count('DB_PREFIX')
    replacements_made = 0
    
    for old_table, new_table in all_replacements.items():
        # النمط الأول: DB_PREFIX . "table_name"
        pattern1 = f'DB_PREFIX . "{old_table}"'
        if pattern1 in content:
            count = content.count(pattern1)
            content = content.replace(pattern1, f'"{new_table}"')
            replacements_made += count
            print(f"✅ استبدال {count} من: {pattern1}")
        
        # النمط الثاني: " . DB_PREFIX . "table_name
        pattern2 = f'" . DB_PREFIX . "{old_table}'
        if pattern2 in content:
            count = content.count(pattern2)
            content = content.replace(pattern2, f'"{new_table}')
            replacements_made += count
            print(f"✅ استبدال {count} من: {pattern2}")
    
    # حفظ الملف المُصحح
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    # إحصائيات النتائج
    final_count = content.count('DB_PREFIX')
    
    print(f"\n📊 تقرير التصحيح:")
    print(f"✅ إجمالي الاستبدالات: {replacements_made}")
    print(f"📉 قبل: {original_count} استخدام DB_PREFIX")
    print(f"📈 بعد: {final_count} استخدام DB_PREFIX")
    print(f"🎯 تم تصحيح: {original_count - final_count} استخدام")
    
    if final_count == 0:
        print("\n🎉 تم تصحيح جميع استخدامات DB_PREFIX بنجاح!")
    else:
        print(f"\n⚠️  تحذير: لا تزال هناك {final_count} استخدامات تحتاج مراجعة يدوية")
        
        # عرض الاستخدامات المتبقية
        lines = content.split('\n')
        remaining_lines = []
        for i, line in enumerate(lines, 1):
            if 'DB_PREFIX' in line:
                remaining_lines.append(f"السطر {i}: {line.strip()}")
        
        if remaining_lines:
            print("\n🔍 الاستخدامات المتبقية:")
            for line in remaining_lines[:10]:  # أول 10 فقط
                print(f"   {line}")
            if len(remaining_lines) > 10:
                print(f"   ... و {len(remaining_lines) - 10} استخدام آخر")
    
    print(f"\n📁 النسخة الاحتياطية: {backup_file}")
    print("🔧 انتهى التصحيح!")

if __name__ == "__main__":
    main()
