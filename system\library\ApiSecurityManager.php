<?php
/**
 * API Security Manager - طبقة الأمان الشاملة لـ AYM ERP
 * 
 * يوفر حماية متقدمة للـ API مع OAuth 2.0, JWT, Rate Limiting, وكشف التهديدات
 * 
 * <AUTHOR> AI Assistant
 * @version 2.0
 * @date 19/7/2025
 */

class ApiSecurityManager {
    private $registry;
    private $db;
    private $config;
    private $cache;
    private $log;
    private $redis;
    
    // إعدادات الأمان
    private $jwt_secret;
    private $oauth_settings;
    private $rate_limits;
    private $security_rules;
    
    public function __construct($registry) {
        $this->registry = $registry;
        $this->db = $registry->get('db');
        $this->config = $registry->get('config');
        $this->cache = $registry->get('cache');
        $this->log = $registry->get('log');
        
        // تهيئة Redis للـ Rate }
}ts;
   rn $staetu
        r    }
      ));
      tMessage( ' . $e->gets Error:y Stacurit'Se>log->write(      $this-{
      on $e) ticep (Ex    } catch           
 
    count'];ery->row['s'] = $quisted_ips['blackl$stat                   
L");
     S NULat I expires_) OR> NOW(s_at RE expire    WHE            ` 
acklist "api_blREFIX . . DB_PROM `" F              
  (*) as countNT"SELECT COU->query(his->db = $t     $query       داء النشطة
السوالقائمة       // 
           
       rows;ry->que= $nts'] ty_evets['securi$sta                    
 pe");
   Y event_ty    GROUP B        ' 
    $today . "" . ded) = 'adE(date_HERE DAT          W 
      log`i_security_"apFIX . PRE . DB_ `"FROM               count 
  OUNT(*) as C         pe, 
      event_ty               
  ECTquery("SELs->db->$thiquery =           $لأمنية
  اث الأحدات احصائي// إ                 
     >row;
  '] = $query-ts['today   $sta          
       
    '");$today . "'" . = ded) (date_adRE DATE        WHE` 
        ity_log"api_activPREFIX .  DB_M `" .         FRO       nse_time
respoe) as avg__timresponse    AVG(           que_ips,
 s unidress) aSTINCT ip_ad  COUNT(DI            ts,
  otal_reques as tOUNT(*) C         T 
      LEC>query("SE->db-thisy = $     $quer  
       
          -d');ate('Y-m$today = d            ليوم
ات ائيحصا  // إ
               try {     
    = [];
         $stats  yStats() {
curitction getSeublic fun  p  
ان
     */حصائيات الأم*
     * إ   
    /*}
 }
       se;
     turn fal  re
          );essage()etM$e->grror: ' . ion ECreat Tables tye('Securi>writs->log-$thi    {
        on $e) Excepti  } catch (  e;
    turn tru   re        
        ");
     _general_ciLATE=utf8mb4OL8mb4 CSET=utfLT CHARFAUDEE=InnoDB  ENGIN     )`)
       _addeded` (`datedate_addEY `      K    
      ddress`),(`ip_as` ddresp_a `i  KEY       `),
       i_idid` (`apapi_       KEY `
         ),er_id`d` (`us_iEY `user           K`),
     ARY KEY (`id  PRIM           T NULL,
   tetime NO_added` da   `date          L,
   UL NFAULTDEimal(10,3) ecse_time` d`respon              ext,
   ter_agent`         `us
       uri` text, `request_           NULL,
    OT 0) Nar(1thod` varchmeuest_ `req               T NULL,
 NO5)varchar(4p_address` `i             ,
   11) NOT NULLint(api_id`  `            
   ULL,LT N) DEFAUd` int(11r_i  `use      
        _INCREMENT,TONULL AUOT (11) N  `id` int           (
   _log` tivity "api_acFIX . . DB_PREXISTS `"IF NOT EREATE TABLE query("C $this->db->       API
     سجل نشاط ولجد  //               
   );
     general_ci"tf8mb4_ COLLATE=uf8mb4utARSET=EFAULT CH=InnoDB D) ENGINE          
  _added`)` (`date`date_added       KEY 
         _address`),ss` (`ip_addre    KEY `ip        `),
    ent_type (`evt_type`even       KEY `,
         Y (`id`)PRIMARY KE        L,
        OT NUL Ned` datetimeadd `date_       t,
        texnt_data`         `eve   ext,
     i` tquest_ur  `re           
   agent` text,r_   `use      L,
        NULhar(45) NOTrc` vaess    `ip_addr            OT NULL,
ar(50) Ne` varcht_typeven       `         EMENT,
TO_INCRULL AU N(11) NOT` int       `id         ` (
ty_logsecuri"api_FIX . DB_PRES `" . STEXIF NOT ABLE I Ty("CREATEquer>db->$this-         ة
    الأمنيداثالأحجدول سجل //                   
 ");
     cieral_enE=utf8mb4_gCOLLATET=utf8mb4  CHARSnoDB DEFAULTINE=InENG)             s_at`)
re` (`expiexpires_at      KEY `          
p`),p` (`iQUE KEY `i   UNI            Y (`id`),
   PRIMARY KE             ,
 FAULT NULL DEmeteti das_at`  `expire         ,
     ULLtime NOT N_added` date       `date         ULL,
NOT Nar(255) son` varch     `rea        LL,
   ) NOT NU` varchar(45       `ip         CREMENT,
L AUTO_IN1) NOT NULd` int(1         `i  t` (
     pi_blacklis . "aDB_PREFIX `" . ISTSNOT EX IF ATE TABLECREry(">db->que    $this-  
      ة السوداءدول القائم  // ج         try {
         ables() {
ityTateSecurunction cre public f */
   
    لوبةات المطاعدة البياناء جداول ق * إنش  /**
     
     }
    $result;
 rn        retu
             }
   ));
etMessage( ' . $e->g Error:ionI Permiss('APterihis->log->w        $tr';
    tion errovalidaion = 'Permiss'error'] lt[   $resu  {
       ) ion $eceptcatch (Ex        }           
  sions;
 = $permismissions']lt['per$resu        ue;
    valid'] = trresult['      $  
      
          te'];'wri?? ['read', '] missionsoad['perons = $payl  $permissi
           لاحقاً)يمكن توسيعهالاحيات ( // جلب الص         
               }

            }        t;
       esul return $r              ';
     t authorizedddress no= 'IP a] lt['error'esu    $r               ps)) {
 $allowed_i, ($client_ipay(!in_arrf           i
                   );
   $_REQUESTtIP(tClien = $this->geient_ip  $cl              
owed_ips']);o['alli_inf,', $applode('d_ips = ex$allowe             ])) {
   ps'owed_i['allapi_infoif (!empty($           محدداً)
   (إذا كانIP المسموححقق من // الت              
   
       >row;= $query-o $api_inf                    
    
 }
           esult; $r      return       ed';
   isabld or dls not founedentiaI crAPrror'] = ''et[ $resul           {
    m_rows) uery->nu!$q     if (           
      i_id");
  UP BY a.ap GRO             = '1' 
   tusND a.sta     A            . "' 
'api_id']oad[nt)$payl = '" . (iapi_id WHERE a.             d 
  = ai.api_i a.api_id ai ON"api_ip` B_PREFIX . `" . D LEFT JOIN              ` a 
  "apiFIX . PREDB_ `" .       FROM
          d_ips  as alloweip)CAT(ai.OUP_CONLECT a.*, GR->query("SEthis->dbquery = $       $يانات
      قاعدة البومات API منمعلجلب         // 
                   }
         result;
 return $              
 ials';credentAPI id r'] = 'Invalsult['erro      $re           {
i_id']))$payload['apisset(f (!   i       API ID
  وجود تحقق من   // ال  {
                try    
  => ''];
    'error'  => [],s'ermission=> false, 'p= ['valid'  $result        ) {
ayload($pionsrmissalidateApiPe function v    private/

     *Iت APصلاحيا التحقق من   * /**
         }
    
   }
 
     );age()e->getMess. $rror: ' ivity Log Ee('API Actog->writ$this->l             {
$e)eption  (Excch  } cat     ");
 W()_added = NOte    da          '0',
   e_time =spons    re  
          ,? '') . "'er-Agent'] ?rs']['Us['headerequest($->escapedb->is$th'" . agent =  user_           "',
     '') . uri'] ??($request['scapethis->db->e $ = '" . request_uri            "',
   'GET') . method'] ?? ['uestape($reqb->esc->dis = '" . $thuest_method req             . "',
   ($request))entIPlietChis->gescape($tthis->db->'" . $_address =           ip      '] . "',
'api_idload[nt)$pay" . (i= '  api_id            "',
    . d']d['user_i$payloa" . (int)user_id = '               
  _log` SETityivact . "api_. DB_PREFIXT INTO `" ("INSER->query   $this->db         try {
 
       ayload) {equest, $pvity($rAction logApinctivate fu  pri
     */
  يل نشاط API
     * تسج 
    /** }
   
    }
       );()>getMessage: ' . $e-Log ErrorSecurity og->write('s->l  $thi     e) {
     on $Excepti} catch (       
 ");()added = NOW  date_           ',
   ata)) . "de($dpe(json_encos->db->esca. $thia = '" nt_dat        eve     "',
    '') .?? _URI'] VER['REQUESTe($_SER->escap. $this->db" uest_uri = 'eq r              . "',
  NT'] ?? '')_AGEERTP_USR['HT_SERVEscape($db->e. $this->= '" agent ser_           u"',
     EQUEST)) . ientIP($_R->getClhisp'] ?? $tta['i($dadb->escapethis->s = '" . $p_addres    i           
 ) . "',($event_typedb->escape" . $this-> = 't_typeven  e          SET 
    g` _losecuritypi_"aIX . EF`" . DB_PRRT INTO ("INSEquerydb->->  $this
          y {    trta) {
    t_type, $da($evencurityEventogSeon lfuncti   private   */
 منية
    الأسجيل الأحداث
     * ت   
    /**
 }
    }        lse;
 fa  return       
   );ge()etMessa. $e->gck Error: ' heklist CBlac>write('is->log-  $th         $e) {
 xception atch (E  } c
       > 0;row['count']->queryurn $   ret 
                  ))");
  W(NOat >  expires_IS NULL ORpires_at (ex AND               "' 
 p) . $iscape(b->e $this->d" .RE ip = '         WHE     
  list` i_black. "ap. DB_PREFIX "  FROM `*) as countOUNT("SELECT Cb->query(y = $this->d       $quer{
            try ) {
 cklisted($ipon isBlaivate functi*/
    prاء
     السودقائمة  ال  * فحص/**
   
    
    
    }     }e());
   e->getMessagror: ' . $Er('Blacklist >log->write      $this-) {
      ion $etch (Except ca     }on]);
   > $reasreason' =' => $ip, '', ['ipKLISTED_BLACnt('IPSecurityEve  $this->log               
            HOUR)");
NTERVAL 24NOW(), IADD(ATE_ = Dres_atexpi               NOW(),
  ed =    date_add            on) . "',
ape($reas->db->esc" . $thison = 'as      re     
      DATEUPE KEY ICATON DUPL               OUR)
 VAL 24 H INTERD(NOW(), DATE_ADes_at =xpir        e        = NOW(),
 e_added dat              "',
  reason) .>escape($. $this->db-ason = '"        re         . "',
 ip)>escape($. $this->db-"  = '         ip       SET 
 acklist`X . "api_blB_PREFI" . DNTO `ERT INSb->query("Ithis->d $  {
            try 
     ) { $reasonip,acklist($tion addToBlvate func    pri  */
اء
   ودائمة السضافة IP للق * إ    
    /**

    }
      );    3)
   == 44ERVER_PORT']ver']['Serequest['s) && $rVER_PORT']rver']['SER$request['sempty(     (!e    ||
   ] === 'on') WARDED_SSL'HTTP_X_FORerver'][' $request['sED_SSL']) &&WARDX_FORHTTP_'server']['t[esqu$re   (!empty(        
 ps') ||tt] === 'hD_PROTO'FORWARDE['HTTP_X_']st['server& $reque']) &D_PROTOX_FORWARDEver']['HTTP_uest['sereqy($r     (!empt      ) ||
  'off'!==] er']['HTTPS't['serv) && $requesTPS']server']['HTequest['y($rmpt  (!e
           (   return      {
est)equn isHttps($rfunctioate  priv     */
   
TPSن HTلتحقق م  * ا   /**
     }
    
 0.0';
  .0.DDR'] ?? '0EMOTE_A]['R'server'n $request[ retur     
         }
       }
           }
             $ip;
          return               {
   GE))AN_R_NO_RESFILTER_FLAGV_RANGE | O_PRIG_N, FILTER_FLATE_IPDAVALIER_FILTr_var($ip,  (filte        if   );
     ])[0]][$headerserver', $request['xplode(','im(e $ip = tr           {
    header])) 'server'][$request[f (!empty($         i) {
    as $headerrsh ($ip_headereac        fo
        
;     ]rd
     // Standa      DR'        EMOTE_AD        'R      // Proxy
  ',        WARDED   'HTTP_FOR      Proxy
   ,        // DED_FOR'TP_FORWAR   'HT       
    // ClusterT_IP',LIEN_CLUSTER_C     'HTTP_X     roxy
         // P,   ARDED'HTTP_X_FORW      '   y
   r/proxncela // Load baD_FOR',     ARDETTP_X_FORW        'H  re
  loudfla',     // CG_IPNECTINON'HTTP_CF_C     = [
       s _header      $ipst) {
  uetIP($req getClienonunctiivate f*/
    prيل
     الحقيقي للعمصول على IP    * الح
     
    /**
 lse;
    } return fa
                      }
       }
       }
                 }
              ue;
    return tr                      ) {
   $value)attern,match($pg_re if (p          
         ern) {as $patttterns $xss_pa   foreach (           
  {ue)) ing($valif (is_str           
 e) {valua as $ ($check_dateach        for      
     );
  ?? []
   '] ostest['p $requ          ? [],
 t'] ?$request['ge          
  rray_merge( ack_data =    $che    
        
];   /i'
     bed<em        '/ct/i',
      '/<obje          ame/i',
   '/<ifr      ',
   =/inmouseover    '/o    i',
    nclick=/       '/o  r=/i',
   rro     '/one
       onload=/i',      '/
      cript:/i','/javas           ,
 /i'cript        '/<s[
    terns = ss_pat        $x{
request) SS($ctXnction deteate fu
    privSS
     */ت Xولا كشف محا
     * /**
       }
    e;
 return fals   
           }
        }
            }
             
        }            rue;
    rn ttu       re                 $value)) {
ttern, h($pa(preg_matc     if             rn) {
   atte $p_patterns as$sql   foreach (    
         e)) {string($valuf (is_      i      {
  $value)a as$check_datoreach (        f
        
     );
    ?? '']uest['uri'][$req           
 st'] ?? [],equest['po          $r  ],
 [get'] ??est['       $requ   
  rray_merge(_data = aheck  $c  
           ];
   '
      /i|\/\*)"|;|--|\*'|\      '/(\     i',
 _executesql//sp  '        p_/i',
     '/exec.*x         table/i',
drop.*       '/,
     i'et/ate.*s   '/upd      rom/i',
    '/delete.*f   ,
        *into/i' '/insert.          *from/i',
 '/select.         t/i',
   n.*selecio/un    '
        rns = [atte     $sql_puest) {
   ($reqInjection detectSQLunctionte f
    priva   */
  ionct SQL Injeلاتاوكشف مح*
     *  
    /*  }
      false;
    return      
      }
    
    }          rue;
      return t       
     _agent)) {$userttern, _match($pa   if (preg    {
     ern) rns as $pattatteicious_p ($susporeach  f       
  
      ];'
       map/i       '/sql    ,
 /nikto/i'    '
        arvester/i', '/h   ,
        crawler/i'wler.*   '/cra        
 er/i', '/scann
           ',*bot/i'/bot.   
         /i',python          '/',
  get/i '/w           
curl/i',      '/     rns = [
 icious_patte  $susp      _agent) {
Bots($userousctSuspicidetefunction private 
    وهة
     */بوتات المشبشف ال
     * ك
    /**}
    
    e;lsfaurn ret       
     }
        ;
    rue  return t
          lient_ip)) {ttern($cPaequestormalR>detectAbnf ($this-        iة
ر طبيعية بسرعة غيمتكررالبات ف الطل/ كش
        /               }
 rn true;
 retu          ');
 tedttempt detecXSS a, 'client_ipacklist($ToBl$this->add           t)) {
 uestXSS($req>detecs-hif ($t
        iS XSاولات // كشف مح     
   }
            
   n true;       retur    );
 attempt'on injectiL t_ip, 'SQlienlist($cddToBlack  $this->a         est)) {
 ction($requtectSQLInjethis->de    if ($ion
    L Injectاولات SQمح كشف 
        //     
   }      rn true;
         retu     ted');
 detec botuspiciousent_ip, 'Sacklist($cliddToBl  $this->a
           {gent))s($user_aciousBotdetectSuspif ($this->  i
      هةات المشبو/ كشف البوت /
               
        }
eturn true; r         {
   client_ip))sted($liack->isBl($this
        if ءقائمة السوداص ال      // فح      
  
  ri'] ?? '';'uequest[_uri = $rst   $reque';
     ent'] ?? ''User-Agrs'][eadest['h$reque = ntageuser_   $;
     $request)P(ntIgetCliep = $this->  $client_it) {
      $requestThreats(ction detec fun
    private    */وهة
 لأنشطة المشبت وادا* كشف التهدي         /**

    
 }
   rn $result;     retu 
    }
              rue;
'] = tllowed$result['a         لطلب
   باسمح ة الخطأ، ا // في حال           ;
())ssagee->getMerror: ' . $imit Eite('Rate Ls->log->wrthi  $
          tion $e) {xcep} catch (E 
                 nt + 1);
  $minute_cou- (er_minute'] 'requests_pits[->rate_lim $thisaining'] =em  $result['r
               );
        86400ay_key,($d>expireredis-     $this->      ;
 y_key)is->incr($dathis->red       $     
        600);
     3$hour_key,re(->expidisis->re        $th   );
 _keyr($hours->inc$this->redi           
       
      );60nute_key, mie($piredis->exthis->r   $       
  ute_key);r($minredis->inc $this->          ات
 دة العداد زيا         //     
      
        }       esult;
  return $r    
                        
         }      ;
     exceeded')limitp, 'Rate ($client_icklistoBladdT>a     $this-        {
        d'])ist_thresholits['blacklime_l= $this->rat >$day_count       if (ى
         ز الحد الأقصاوإذا تجوداء لسائمة اة IP للق    // إضاف    
                0;
         = remaining']sult['       $re        alse;
 lowed'] = falresult['   $                      
      ay']) {
 ests_per_dquts['remiis->rate_lit >= $th $day_coun    
           _hour'] ||equests_permits['re_lihis->rat>= $tour_count      $h        te'] ||
   _minu_perquestsreits['te_limhis->ra_count >= $tif ($minute             
           key) ?: 0;
get($day_is->redis->= $thy_count da  $        ?: 0;
  r_key) >get($houthis->redis- $ =ur_count   $ho         ) ?: 0;
inute_keys->get($mthis->redite_count = $   $minu     
    حدود  // فحص ال         
  
            / 86400);(time()floor:" . {$client_ip}y:mit:da "rate_liday_key =        $
    / 3600);oor(time() fl" . ip}:r:{$client_e_limit:houratey = "ur_k$ho   
         te}";nu$current_miient_ip}:{:{$clinuteit:mate_lim"r = inute_key       $m   
   60);ime() /or(tinute = floent_m       $curr
          try {       
          }
 
 ent_ip);base($cliLimitDatakRateis->checeturn $th    r        بيانات
ة القاعد، استخدم  متاحاًم يكن Redis    // إذا ل
        {s->redis) $thif (! 
        i;
       inute']]r_ms_pequestlimits['reis->rate_ing' => $the, 'remaind' => tru= ['allowe  $result     t);
  IP($reques>getClientthis-t_ip = $enli    $c{
    $request) imit(ckRateLion cheunctrivate f*/
    png
     tite Limi * فحص Ra/**
       }
    
 
    null;rn     retu         
}
       '];
    cess_tokenpost']['acst['reque  return $         {
 token'])) cess_'post']['acrequest[et($ if (iss    
   ter POST parame    // من       
         }
  ken'];
  'access_to'get'][uest[rn $req     retu  {
     token'])) s_['accesquest['get'](isset($re    if 
    rameterن GET pa       // م       
         }
    }
         [1];
$matches  return          
     )) {tchesader, $mahe/i', $auth_)$.*/Bearer\s+(g_match('     if (pre
       tion'];uthorizaheaders']['Ast['r = $reque$auth_heade       ) {
     ation'])uthoriz']['Aest['headerst($requ(isse        if Header
ation orizمن Auth        // st) {
requeuest($TokenFromReqractfunction ext private  */
   
     طلب HTTP Token من   * استخراج*
  
    /* }
       sult;
  return $re     data;
 payload_ayload'] = $t['p      $resule;
  lid'] = tru'va $result[     
         }
  ;
        $result return          ';
 edn expir'] = 'Toke'errort[esul    $r     {
   ] < time()) ta['exp'payload_da) && $a['exp']atoad_det($paylf (iss   i   صلاحية
  هاء الن انت م  // التحقق    
     }
            ;
 $result   return         yload';
 en pa tokalidnvr'] = 'Irot['er    $resul         {
a)datayload_ (!$p if      
     ue);
    yload)), tr$pa['+', '/'], _'], ace(['-', '(str_replse64_decodeode(bajson_decta = load_da $pay       oad
ير Payl // فك تشف  
       }
      t;
        resuleturn $  r          nature';
igalid token s] = 'Inverror'sult['       $re    ture) {
 $valid_signare !== ignatu($s     if        
   )));
  trueecret,wt_shis->jpayload, $t." . $der . "$hea('sha256', accode(hash_hm  base64_en         ', ''], 
  '_-', [''],, '/', '=+'e(['str_replac= ature  $valid_sign     يع
  حقق من التوق  // الت          
   $parts;
  nature) =, $sigyloadpa $st($header,    li    
      }
  
        ult;resurn $        ret;
    en format'nvalid tokr'] = 'Irroresult['e      $ {
      == 3)$parts) !  if (count(n);
      , $tokelode('.'= exp$parts T
        اء JW  // فصل أجز
                 }

      $result;rn     retu    ;
   token'on zatithorivalid aur in oingor'] = 'Miss'err  $result[         $token) {
       if (!st);
  requemRequest($okenFroactT $this->extr   $token =   Header
  n من Tokeراج  استخ//  
          ];
    ' => ''null, 'error' => 'payload> false, 'valid' =lt = [  $resu  
    ) {uesten($reqdateJWTToktion valiate funcriv  p     */
  oken
صحة JWT Tلتحقق من * ا  /**
        
  
    }
 ;Signaturease64. $b "." 4Payload .. $base6. "." r eade$base64Hreturn   
        ;
      re))$signatue(e64_encodbas '_', ''], ',], ['-=' '', '/',['+replace(ture = str_base64Signa $;
       ue)ecret, trjwt_ss->, $thiPayload $base64r . "." .64Headea256', $basec('shhma= hash_ure     $signat
      ;
      ($payload))_encode base64],, '' '_', ['-','/', '='](['+', r_replace = stload$base64Pay
        eader));$hencode('], base64__', ''], ['-', ''+', '/', '=tr_replace([4Header = s  $base6       
;
            ])  _CLIENT'
 _ERP => 'AYM  'aud'
          P_API',' => 'AYM_ER 'iss       
    ],etime'en_lifaccess_tokh_settings['>oaut + $this- time() 'exp' =>        ,
    => time()    'iat'  s,
      mission' => $persions   'permis       i_id,
  > $apd' ='api_i        id,
    => $user_'     'user_id
        code([ json_enad =    $paylo   
        
 ']);S256 'HT', 'alg' =>> 'JW['typ' =on_encode(header = js{
        $ = []) nsermissio $p_id,_id, $api$useroken(ateJWTTon generuncti   public f    */
 
  جديد Token إنشاء JWT /**
     *
    
   lt;
    }resuation_urn $valid     ret
    }
            ());
   >getMessagee-ror: ' . $ ErAPI Security('->writethis->log     $       ty error';
uriecternal s][] = 'Inlt['errors'n_resudatio       $vali
     {e) n $Exceptioh (catc   }      
          ns'];
  issiorm['peidation= $api_valssions'] sult['permidation_re$vali           ;
 ]['api_id']d'yloan['pawt_validatio_id'] = $jpin_result['aatioid      $val   ;
   d']r_iuseayload']['tion['p $jwt_valida'user_id'] =tion_result[  $valida          ] = true;
lt['valid'tion_resuda$vali       هائية
     لنيجة ا  // النت          
       }
              ']);
   oaddation['paylt_valiequest, $jwiActivity($rgAp->lo       $this    ']) {
     enabledt_logging_dies['auecurity_rulif ($this->s   
         لناجحشاط ا الن تسجيل 6.       // 
                    }

        ion_result;lidat $vaturn         re      error'];
 ation['id $api_val][] ='errors'esult[validation_r     $       ]) {
    on['valid'dati$api_vali      if (!     ad']);
 yloon['pavalidati($jwt_onsApiPermissidate $this->valilidation =    $api_va
         API صلاحياتحقق منلت5. ا     //     
                 }
         
 t;ation_resulidrn $valretu                or'];
dation['errlijwt_vas'][] = $rror['eltidation_resu    $val        {
    valid']) idation['alif (!$jwt_v         ;
   $request)ken(JWTToidate $this->valtion =_valida    $jwt     T Token
   JWلتحقق من   // 4. ا         
            ing'];
 emainck['rlimit_che'] = $rate_ainingemlimit_rlt['rate_n_resu $validatio        }
               ult;
lidation_resreturn $va             = 0;
    remaining']ate_limit_t['rtion_resulalida        $v';
        t exceededate limi = 'Rerrors'][]esult['on_rati      $valid        wed']) {
  allok['imit_chec!$rate_l      if (t);
      eques($rLimitckRatethis->cheeck = $_limit_chrate $           iting
حص Rate Lim     // 3. ف
               
      }              }
 
           _result;tionlida $va return              est);
     ED', $requECTREAT_DETtyEvent('THgSecuri $this->lo                   ';
ity detecteds activiciouusps'][] = 'Serrorlt['ation_resualid     $v               d) {
ctedetereat_ ($th       if         quest);
$reectThreats(s->det = $thitedreat_detec $th           {
     d'])n_enabletectiodeat_rules['threecurity_his->sif ($t        مات
    لهجتهديدات وا// 2. كشف ال                  
     
     }   
     t;ion_resulatvalid $    return       ;
     access'ed for API quirS reTTPs'][] = 'Hsult['erroration_re $valid              {
 uest)) $reqHttps(& !$this->iss'] &quire_http'reles[rity_rucu>se$this-f (       i    طلوباً
  ما كانن HTTPS إذ محقق الت1.   //      ry {
         t   
        
      ];  => []
s' error     '     g' => 0,
  inint_remalimite_  'ra         [],
 => ermissions' 'p     
       ull,=> n'     'api_id      
  l,> nul'user_id' =             => false,
   'valid'        esult = [
 ation_r     $valid  quest) {
 quest($reApiRete validationfunc    public */
ب API
     ق من صحة طل التحق   **
    /*   
  
    }
         } $key;
eturn    r      }
             ];
 ) - 1)aracters($chlen_rand(0, strcters[mt$chara $key .=       
         ++) { $ih;$i < $lengt$i = 0;     for (
        $key = '';          Z';
  TUVWXYLMNOPQRSIJKABCDEFGHopqrstuvwxyzijklmnabcdefgh678912345= '0ters harac   $c   مة
      لقدينظمة الأ لckba // Fall           else {
    } 2));
     gth /bytes($lenpseudo_dom_ssl_ranpen bin2hex(orn retu           ) {
ytes')eudo_b_psssl_randomxists('openunction_e} elseif (f     / 2));
   h bytes($lengtx(random_turn bin2he re         s')) {
  m_bytedosts('ranction_exi    if (fun   = 64) {
 gth y($lenteSecureKeeran genvate functio/
    priي
     *عشوائفتاح آمن نشاء م*
     * إ  
    /*    }
  
     };
   '")"et) . is->jwt_secrape($th>db->esc" . $this-ue` = ' `valTEE KEY UPDAICAT   ON DUPL          
   zed = '0' seriali           , 
     . "'jwt_secret)->$this>escape(db- $this->" .value` = '     `        cret', 
   'api_jwt_se`key` =                'api', 
    `code` =          '0', 
    =   store_id           T 
     SE"setting`PREFIX . O `" . DB_"INSERT INTery(>qus->db-    $thiت
        ة البيانا في قاعدلمفتاححفظ ا//              
   );
        ecureKey(128nerateSs->gehi = $t>jwt_secret     $this-      
 WT آمن جديد Jء مفتاحإنشا    //      
   et) {secris->jwt_ (!$th      if
          _secret');
api_jwtt('g->gethis->confi= $cret s->jwt_se  $thi     zeJWT() {
 nitialifunction i  private  */
  
    آمنع مفتاح  JWT م تهيئة
     *   /** 
    }
    ];
 e
        ?: trung_enabled')audit_loggiet('api_g->g>confithis-bled' => $naging_elogdit_   'au        rue,
 ed') ?: tabln_enectioeat_detget('api_thrs->config->$thi>  =n_enabled'at_detectio'thre      e,
      led') ?: truneypot_enabapi_hoget('fig->his->con' => $tnabled_eoneypot 'h  
         se,') ?: fal_enabledlocking'api_geo_bt(g->gethis->confiabled' => $g_en_blockin  'geo         false,
 bled') ?: st_ena_ip_whitelig->get('api$this->confied' => t_enablp_whitelis    'i
        se,!== fal_https') irequ('api_refig->get> $this->con =_https'    'require        [
 rity_rules =cu  $this->se     الأمان
   // قواعد  
          ];
           0
: 100) ?eshold'hrklist_tac('api_blget>config->$this-' => st_threshold  'blackli  ,
         20mit') ?:list_pi_bur->get('aighis->confmit' => $t   'burst_li       ,
  000y') ?: 10ests_per_dat('api_requig->geis->confday' => $ther_sts_p      'reque000,
      ur') ?: 1per_hoi_requests_g->get('ap$this->confier_hour' => ests_p     'requ      ?: 100,
  minute')er_ts_papi_requesonfig->get('this->cte' => $er_minuuests_preq '        [
    ts =limihis->rate_   $tting
     ate Limiدادات R // إع   
          ];
           دقائق
// 10600 etime') ?: code_lifth_api_aufig->get('> $this->conime' =e_lifetzation_cod    'authori      30 يوم
    //00 * 30,ime') ?: 864et_lif_tokeni_refreshap('ig->getonf->c $thisme' =>tioken_liferesh_t   'ref      حدة
    ساعة وا ?: 3600, //etime')ss_token_lifapi_accet('onfig->gehis->cme' => $ttiifes_token_lacces '       ,
    cureKey(64)nerateSe $this->gesecret') ?:client_api_oauth_('config->get => $this->ient_secret'     'cl
       client',erp_?: 'aym_client_id') _oauth_'apiig->get(->confis> $thclient_id' =  '        
   [s =tting->oauth_se$this         OAuth 2.0
// إعدادات     s() {
   tingitySetcuroadSefunction l  private  */
  ات
    عدة البيانلأمان من قاعدادات احميل إ     * ت   /**

     }
           }
ll;
 nudis = rehis->         $t;
   age())->getMess $eailed - ' .ion fctonneRedis curity: ('API Sec>writeis->log-   $th        
 e) {(Exception $ch at} c      
   }      g();
     ->pinedisis->r       $th        لاتصال
 ار ا/ اختب  /                    
  );
        _portredist, $_hosnnect($redisedis->cois->r     $th     
      379; ?: 6t')is_pored->get('ronfighis->c_port = $tedis        $r;
        .0.1''127.0) ?: redis_host'et('config->g= $this->s_host redi    $          
  is();w Redneedis = his->r $t        {
       is')) xists('Redf (class_e          i  try {
  
      ) {ializeRedis(itnction in private fu  
     */
 ن المؤقتلتخزيting والـ Rate LimiRedis ليئة ته  *   /**
      
  
  }eJWT();
   s->initializ $thi     يئة JWT
        // ته   
  s();
     ettingritySoadSecu->l     $this   أمان
ات الحميل إعداد      // ت
      
    dis();alizeRe$this->initi
         Limiting