<?php
// Heading
$_['heading_title']                    = 'Income Statement';

// Text
$_['text_success']                     = 'Success: Income Statement has been generated successfully!';
$_['text_list']                        = 'Income Statement List';
$_['text_form']                        = 'Income Statement Form';
$_['text_view']                        = 'View Income Statement';
$_['text_generate']                    = 'Generate Income Statement';
$_['text_export']                      = 'Export Income Statement';
$_['text_compare']                     = 'Compare Income Statement';
$_['text_print']                       = 'Print Income Statement';
$_['text_income_statement']            = 'Income Statement';
$_['text_period']                      = 'Period';
$_['text_from']                        = 'From';
$_['text_to']                          = 'To';
$_['text_loading']                     = 'Loading...';
$_['text_processing']                  = 'Processing...';
$_['text_success_generate']            = 'Income Statement generated successfully!';
$_['text_success_export']              = 'Income Statement exported successfully!';
$_['text_success_compare']             = 'Comparison completed successfully!';

// Income Statement Sections
$_['text_revenues']                    = 'Revenues';
$_['text_sales_revenue']               = 'Sales Revenue';
$_['text_service_revenue']             = 'Service Revenue';
$_['text_other_revenue']               = 'Other Revenue';
$_['text_total_revenues']              = 'Total Revenues';

$_['text_cost_of_goods_sold']          = 'Cost of Goods Sold';
$_['text_gross_profit']                = 'Gross Profit';
$_['text_gross_loss']                  = 'Gross Loss';

$_['text_operating_expenses']          = 'Operating Expenses';
$_['text_selling_expenses']            = 'Selling Expenses';
$_['text_administrative_expenses']     = 'Administrative Expenses';
$_['text_general_expenses']            = 'General Expenses';
$_['text_total_operating_expenses']    = 'Total Operating Expenses';

$_['text_operating_income']            = 'Operating Income';
$_['text_operating_loss']              = 'Operating Loss';

$_['text_other_income']                = 'Other Income';
$_['text_financial_income']            = 'Financial Income';
$_['text_investment_income']           = 'Investment Income';

$_['text_other_expenses']              = 'Other Expenses';
$_['text_financial_expenses']          = 'Financial Expenses';
$_['text_interest_expenses']           = 'Interest Expenses';

$_['text_income_before_tax']           = 'Income Before Tax';
$_['text_tax_expenses']                = 'Tax Expenses';
$_['text_net_income']                  = 'Net Income';
$_['text_net_loss']                    = 'Net Loss';

// Column
$_['column_account_code']              = 'Account Code';
$_['column_account_name']              = 'Account Name';
$_['text_account_name']                = 'Account Name';
$_['column_amount']                    = 'Amount';
$_['text_amount']                      = 'Amount';
$_['column_percentage']                = 'Percentage';
$_['column_current_period']            = 'Current Period';
$_['column_previous_period']           = 'Previous Period';
$_['column_variance']                  = 'Variance';
$_['column_variance_percentage']       = 'Variance %';

// Entry
$_['entry_date_start']                 = 'Start Date';
$_['entry_date_end']                   = 'End Date';
$_['entry_branch']                     = 'Branch';
$_['entry_cost_center']                = 'Cost Center';
$_['entry_comparison_period']          = 'Comparison Period';
$_['entry_export_format']              = 'Export Format';
$_['entry_include_zero_amounts']       = 'Include Zero Amounts';
$_['entry_show_percentages']           = 'Show Percentages';

// Button
$_['button_generate']                  = 'Generate';
$_['button_export']                    = 'Export';
$_['button_compare']                   = 'Compare';
$_['button_print']                     = 'Print';
$_['button_drill_down']                = 'Drill Down';
$_['button_filter']                    = 'Filter';
$_['button_reset']                     = 'Reset';
$_['button_close']                     = 'Close';

// Tab
$_['tab_general']                      = 'General';
$_['tab_filters']                      = 'Filters';
$_['tab_options']                      = 'Options';
$_['tab_comparison']                   = 'Comparison';

// Help
$_['help_date_range']                  = 'Select the time period for income statement';
$_['help_branch']                      = 'Select specific branch or all branches';
$_['help_comparison']                  = 'Select period to compare with current period';

// Error
$_['error_warning']                    = 'Warning: Please check the form carefully for errors!';
$_['error_permission']                 = 'Warning: You do not have permission to access Income Statement!';
$_['error_date_start']                 = 'Start date is required!';
$_['error_date_end']                   = 'End date is required!';
$_['error_date_range']                 = 'Start date must be before end date!';
$_['error_no_data']                    = 'No data for selected period!';
$_['error_export']                     = 'Error exporting data!';
$_['error_form']                       = 'Form data error!';
$_['error_missing_data']               = 'Missing data!';

// Export Formats
$_['text_excel']                       = 'Excel';
$_['text_pdf']                         = 'PDF';
$_['text_csv']                         = 'CSV';

// Print Options
$_['text_print_title']                 = 'Income Statement';
$_['print_title']                      = 'Print Income Statement';
$_['text_print_date']                  = 'Print Date';
$_['text_print_user']                  = 'Printed By';
$_['text_print_period']                = 'Period';

// Comparison
$_['text_period_1']                    = 'Period 1';
$_['text_period_2']                    = 'Period 2';
$_['text_variance_amount']             = 'Amount Variance';
$_['text_variance_percentage']         = 'Percentage Variance';
$_['text_increase']                    = 'Increase';
$_['text_decrease']                    = 'Decrease';
$_['text_favorable']                   = 'Favorable';
$_['text_unfavorable']                 = 'Unfavorable';

// Analysis
$_['text_profitability_analysis']      = 'Profitability Analysis';
$_['text_margin_analysis']             = 'Margin Analysis';
$_['text_trend_analysis']              = 'Trend Analysis';
$_['text_ratio_analysis']              = 'Ratio Analysis';

// Status Messages
$_['text_generating']                  = 'Generating income statement...';
$_['text_exporting']                   = 'Exporting data...';
$_['text_comparing']                   = 'Performing comparison...';
$_['text_completed']                   = 'Completed successfully!';

// Additional Fields
$_['text_total']                       = 'Total';
$_['text_no_results']                  = 'No results found';

// Missing variables from audit report - Critical fixes
$_['accounts/income_statement']        = '';
$_['column_left']                      = '';
$_['date_end']                         = 'End Date';
$_['date_start']                       = 'Start Date';
$_['error_no_analysis_data']           = 'No data available for analysis';
$_['export_url']                       = '';
$_['footer']                           = '';
$_['generate_url']                     = '';
$_['header']                           = '';
$_['lang']                             = '';
$_['success']                          = 'Success';
$_['text_advanced_analysis']           = 'Advanced Analysis';
$_['text_analysis_view']               = 'Analysis View';
$_['text_expenses']                    = 'Expenses';
$_['text_success_analysis']            = 'Analysis generated successfully';
$_['text_total_expenses']              = 'Total Expenses';
$_['title']                            = 'Title';

// Additional controller language variables
$_['log_unauthorized_access_income_statement'] = 'Unauthorized access attempt to income statement';
$_['log_view_income_statement_screen'] = 'View income statement screen';

// Enterprise Grade Plus template variables
$_['text_actions']                     = 'Actions';
$_['text_generate_tooltip']            = 'Generate income statement for selected period';
$_['text_export_tooltip']              = 'Export income statement in various formats';
$_['text_print']                       = 'Print';
$_['text_generating']                  = 'Generating...';
$_['text_exporting']                   = 'Exporting...';
$_['error_generate']                   = 'Error generating income statement';

// Error Messages
$_['error_generate']                   = 'Error generating income statement';
$_['error_invalid_data']               = 'Invalid data';
$_['error_missing_parameters']         = 'Required parameters missing';
$_['error_date_from_required']         = 'Start date is required';
$_['error_date_to_required']           = 'End date is required';
$_['error_date_range']                 = 'Start date must be before end date';
$_['error_period1_required']           = 'First period is required';
$_['error_period2_required']           = 'Second period is required';
$_['error_no_data']                    = 'No data found';
$_['error_permission']                 = 'Warning: You do not have permission to access this page!';
$_['error_form']                       = 'Form error';

// Print Title
$_['print_title']                      = 'Income Statement - Print';

// Date Format
$_['date_format_short']                = 'd/m/Y';

// Additional Text
$_['text_home']                        = 'Home';
// Direction
$_['direction']                        = 'ltr';
$_['code']                             = 'en';

// Enhanced performance and analytics variables
$_['text_optimized_income']            = 'Optimized Income Statement';
$_['text_profitability_analysis']      = 'Profitability Analysis';
$_['text_cache_enabled']               = 'Cache Enabled';
$_['text_profit_margin']               = 'Profit Margin';
$_['text_expense_ratio']               = 'Expense Ratio';
$_['text_net_income']                  = 'Net Income';
$_['button_profitability_analysis']    = 'Profitability Analysis';
$_['text_loading_analysis']            = 'Loading profitability analysis...';
$_['text_analysis_ready']              = 'Analysis ready';

// Generate Options
$_['button_generate_and_new']          = 'Generate and New';
$_['button_generate_and_export']       = 'Generate and Export';

// Advanced Filters
$_['text_all_branches']                = 'All Branches';
$_['text_all_cost_centers']            = 'All Cost Centers';

// KPIs
$_['text_gross_profit_margin']         = 'Gross Profit Margin';
$_['text_net_profit_margin']           = 'Net Profit Margin';
$_['text_operating_margin']            = 'Operating Margin';
$_['text_expense_ratio']               = 'Expense Ratio';
?>