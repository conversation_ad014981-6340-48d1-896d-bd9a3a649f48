<?php
/**
 * English Language File - Notification Templates
 * 
 * @package    AYM ERP
 * <AUTHOR> Development Team
 * @copyright  2025 AYM ERP Systems
 * @license    Commercial License
 * @version    1.0.0
 * @since      2025-07-15
 */

// Main heading
$_['heading_title'] = 'Notification Templates';

// General texts
$_['text_success'] = 'Success: You have modified notification templates!';
$_['text_list'] = 'Template List';
$_['text_add'] = 'Add Template';
$_['text_edit'] = 'Edit Template';
$_['text_view'] = 'View Template';
$_['text_delete'] = 'Delete';
$_['text_confirm'] = 'Are you sure?';
$_['text_no_results'] = 'No templates found!';
$_['text_loading'] = 'Loading...';

// Template types
$_['text_template_types'] = 'Template Types';
$_['text_email_template'] = 'Email Template';
$_['text_sms_template'] = 'SMS Template';
$_['text_push_template'] = 'Push Notification Template';
$_['text_in_app_template'] = 'In-App Notification Template';
$_['text_webhook_template'] = 'Webhook Template';

// Template categories
$_['text_template_categories'] = 'Template Categories';
$_['text_system_templates'] = 'System Templates';
$_['text_user_templates'] = 'User Templates';
$_['text_workflow_templates'] = 'Workflow Templates';
$_['text_marketing_templates'] = 'Marketing Templates';
$_['text_security_templates'] = 'Security Templates';
$_['text_reminder_templates'] = 'Reminder Templates';

// Template status
$_['text_status_active'] = 'Active';
$_['text_status_inactive'] = 'Inactive';
$_['text_status_draft'] = 'Draft';
$_['text_status_archived'] = 'Archived';
$_['text_status_testing'] = 'Testing';

// Entry fields
$_['entry_name'] = 'Template Name';
$_['entry_description'] = 'Description';
$_['entry_type'] = 'Template Type';
$_['entry_category'] = 'Category';
$_['entry_status'] = 'Status';
$_['entry_subject'] = 'Subject';
$_['entry_content'] = 'Content';
$_['entry_html_content'] = 'HTML Content';
$_['entry_text_content'] = 'Text Content';
$_['entry_variables'] = 'Variables';
$_['entry_conditions'] = 'Conditions';
$_['entry_priority'] = 'Priority';
$_['entry_language'] = 'Language';

// Columns
$_['column_name'] = 'Name';
$_['column_type'] = 'Type';
$_['column_category'] = 'Category';
$_['column_status'] = 'Status';
$_['column_language'] = 'Language';
$_['column_created'] = 'Created';
$_['column_modified'] = 'Modified';
$_['column_action'] = 'Action';

// Buttons
$_['button_add'] = 'Add Template';
$_['button_edit'] = 'Edit';
$_['button_delete'] = 'Delete';
$_['button_view'] = 'View';
$_['button_copy'] = 'Copy';
$_['button_test'] = 'Test';
$_['button_preview'] = 'Preview';
$_['button_export'] = 'Export';
$_['button_import'] = 'Import';
$_['button_save'] = 'Save';
$_['button_cancel'] = 'Cancel';

// Variables
$_['text_variables'] = 'Available Variables';
$_['text_user_variables'] = 'User Variables';
$_['text_system_variables'] = 'System Variables';
$_['text_custom_variables'] = 'Custom Variables';
$_['text_variable_name'] = 'Variable Name';
$_['text_variable_description'] = 'Variable Description';
$_['text_variable_example'] = 'Variable Example';

// Common variables
$_['text_common_variables'] = 'Common Variables';
$_['variable_user_name'] = '{user_name} - User Name';
$_['variable_user_email'] = '{user_email} - User Email';
$_['variable_company_name'] = '{company_name} - Company Name';
$_['variable_current_date'] = '{current_date} - Current Date';
$_['variable_current_time'] = '{current_time} - Current Time';
$_['variable_notification_title'] = '{notification_title} - Notification Title';
$_['variable_notification_content'] = '{notification_content} - Notification Content';
$_['variable_action_url'] = '{action_url} - Action URL';

// Formatting and design
$_['text_formatting'] = 'Formatting and Design';
$_['text_html_editor'] = 'HTML Editor';
$_['text_text_editor'] = 'Text Editor';
$_['text_template_design'] = 'Template Design';
$_['text_color_scheme'] = 'Color Scheme';
$_['text_font_settings'] = 'Font Settings';
$_['text_layout_options'] = 'Layout Options';

// Preview and testing
$_['text_preview'] = 'Preview';
$_['text_test_template'] = 'Test Template';
$_['text_send_test'] = 'Send Test';
$_['text_test_recipient'] = 'Test Recipient';
$_['text_test_data'] = 'Test Data';
$_['text_preview_email'] = 'Preview Email';
$_['text_preview_sms'] = 'Preview SMS';
$_['text_preview_push'] = 'Preview Push Notification';

// Conditions and rules
$_['text_conditions'] = 'Conditions and Rules';
$_['text_condition_rules'] = 'Condition Rules';
$_['text_trigger_conditions'] = 'Trigger Conditions';
$_['text_recipient_conditions'] = 'Recipient Conditions';
$_['text_content_conditions'] = 'Content Conditions';

// Customization
$_['text_customization'] = 'Customization';
$_['text_personalization'] = 'Personalization';
$_['text_dynamic_content'] = 'Dynamic Content';
$_['text_conditional_content'] = 'Conditional Content';
$_['text_localization'] = 'Localization';

// Predefined templates
$_['text_predefined_templates'] = 'Predefined Templates';
$_['text_welcome_template'] = 'Welcome Template';
$_['text_password_reset_template'] = 'Password Reset Template';
$_['text_order_confirmation_template'] = 'Order Confirmation Template';
$_['text_payment_reminder_template'] = 'Payment Reminder Template';
$_['text_task_assignment_template'] = 'Task Assignment Template';
$_['text_approval_request_template'] = 'Approval Request Template';
$_['text_system_alert_template'] = 'System Alert Template';

// Organization and classification
$_['text_organization'] = 'Organization and Classification';
$_['text_tags'] = 'Tags';
$_['text_folders'] = 'Folders';
$_['text_search'] = 'Search';
$_['text_filter'] = 'Filter';
$_['text_sort'] = 'Sort';

// Statistics and reports
$_['text_statistics'] = 'Statistics';
$_['text_usage_statistics'] = 'Usage Statistics';
$_['text_performance_metrics'] = 'Performance Metrics';
$_['text_template_analytics'] = 'Template Analytics';
$_['text_delivery_rates'] = 'Delivery Rates';
$_['text_open_rates'] = 'Open Rates';
$_['text_click_rates'] = 'Click Rates';

// Version control
$_['text_version_control'] = 'Version Control';
$_['text_version_history'] = 'Version History';
$_['text_current_version'] = 'Current Version';
$_['text_previous_versions'] = 'Previous Versions';
$_['text_restore_version'] = 'Restore Version';
$_['text_compare_versions'] = 'Compare Versions';

// Collaboration and sharing
$_['text_collaboration'] = 'Collaboration and Sharing';
$_['text_shared_templates'] = 'Shared Templates';
$_['text_template_permissions'] = 'Template Permissions';
$_['text_team_templates'] = 'Team Templates';
$_['text_public_templates'] = 'Public Templates';
$_['text_private_templates'] = 'Private Templates';

// Export and import
$_['text_export_import'] = 'Export and Import';
$_['text_export_template'] = 'Export Template';
$_['text_import_template'] = 'Import Template';
$_['text_export_format'] = 'Export Format';
$_['text_import_format'] = 'Import Format';
$_['text_bulk_export'] = 'Bulk Export';
$_['text_bulk_import'] = 'Bulk Import';

// Error messages
$_['error_permission'] = 'Warning: You do not have permission to access notification templates!';
$_['error_name'] = 'Template name must be between 3 and 255 characters!';
$_['error_type'] = 'Template type must be selected!';
$_['error_content'] = 'Template content is required!';
$_['error_subject'] = 'Template subject is required!';
$_['error_invalid_variable'] = 'Invalid variable!';
$_['error_template_not_found'] = 'Template not found!';
$_['error_template_in_use'] = 'Template is in use and cannot be deleted!';
$_['error_invalid_format'] = 'Invalid format!';

// Confirmation messages
$_['text_confirm_delete'] = 'Are you sure you want to delete this template?';
$_['text_confirm_archive'] = 'Are you sure you want to archive this template?';
$_['text_confirm_restore'] = 'Are you sure you want to restore this version?';
$_['text_unsaved_changes'] = 'You have unsaved changes. Do you want to continue?';

// Help and tips
$_['help_template_name'] = 'Enter a clear and distinctive name for the template';
$_['help_variables'] = 'Use variables to make the template dynamic';
$_['help_html_content'] = 'You can use HTML to format the content';
$_['help_conditions'] = 'Set conditions to customize content display';
$_['help_testing'] = 'Test the template before activating it';

// Alerts
$_['alert_template_saved'] = 'Template saved successfully';
$_['alert_template_deleted'] = 'Template deleted';
$_['alert_test_sent'] = 'Test sent successfully';
$_['alert_template_activated'] = 'Template activated';
$_['alert_template_deactivated'] = 'Template deactivated';

// Dates and times
$_['text_created_at'] = 'Created At';
$_['text_updated_at'] = 'Updated At';
$_['text_last_used'] = 'Last Used';
$_['text_usage_count'] = 'Usage Count';

// Security and permissions
$_['text_security'] = 'Security and Permissions';
$_['text_access_control'] = 'Access Control';
$_['text_template_security'] = 'Template Security';
$_['text_content_validation'] = 'Content Validation';
$_['text_malicious_content'] = 'Malicious Content';

// Integrations
$_['text_integrations'] = 'Integrations';
$_['text_external_templates'] = 'External Templates';
$_['text_api_integration'] = 'API Integration';
$_['text_third_party_services'] = 'Third Party Services';

// Maintenance and optimization
$_['text_maintenance'] = 'Maintenance and Optimization';
$_['text_cleanup_unused'] = 'Cleanup Unused Templates';
$_['text_optimize_performance'] = 'Optimize Performance';
$_['text_cache_templates'] = 'Cache Templates';
$_['text_template_backup'] = 'Template Backup';
?>
