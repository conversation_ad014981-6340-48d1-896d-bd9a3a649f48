{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="submit" form="documents\templates-form" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fas fa-save"></i></button>
        <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-light"><i class="fas fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    
    <div class="card">
      <div class="card-header"><i class="fas fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}chart-bar{% elif screen_type == 'settings' %}cog{% else %}pencil-alt{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="card-body">
        <form id="documents\templates-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-add">{{ text_add }}</label>
            <div class="col-sm-10">
              <input type="text" name="add" value="{{ add }}" placeholder="{{ text_add }}" id="input-add" class="form-control" />
              {% if error_add %}
                <div class="invalid-feedback">{{ error_add }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-analytics">{{ text_analytics }}</label>
            <div class="col-sm-10">
              <input type="text" name="analytics" value="{{ analytics }}" placeholder="{{ text_analytics }}" id="input-analytics" class="form-control" />
              {% if error_analytics %}
                <div class="invalid-feedback">{{ error_analytics }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-back">{{ text_back }}</label>
            <div class="col-sm-10">
              <input type="text" name="back" value="{{ back }}" placeholder="{{ text_back }}" id="input-back" class="form-control" />
              {% if error_back %}
                <div class="invalid-feedback">{{ error_back }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-builder_elements">{{ text_builder_elements }}</label>
            <div class="col-sm-10">
              <input type="text" name="builder_elements" value="{{ builder_elements }}" placeholder="{{ text_builder_elements }}" id="input-builder_elements" class="form-control" />
              {% if error_builder_elements %}
                <div class="invalid-feedback">{{ error_builder_elements }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-data_sources">{{ text_data_sources }}</label>
            <div class="col-sm-10">
              <input type="text" name="data_sources" value="{{ data_sources }}" placeholder="{{ text_data_sources }}" id="input-data_sources" class="form-control" />
              {% if error_data_sources %}
                <div class="invalid-feedback">{{ error_data_sources }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-export">{{ text_export }}</label>
            <div class="col-sm-10">
              <input type="text" name="export" value="{{ export }}" placeholder="{{ text_export }}" id="input-export" class="form-control" />
              {% if error_export %}
                <div class="invalid-feedback">{{ error_export }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="invalid-feedback">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-import">{{ text_import }}</label>
            <div class="col-sm-10">
              <input type="text" name="import" value="{{ import }}" placeholder="{{ text_import }}" id="input-import" class="form-control" />
              {% if error_import %}
                <div class="invalid-feedback">{{ error_import }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-popular_templates">{{ text_popular_templates }}</label>
            <div class="col-sm-10">
              <input type="text" name="popular_templates" value="{{ popular_templates }}" placeholder="{{ text_popular_templates }}" id="input-popular_templates" class="form-control" />
              {% if error_popular_templates %}
                <div class="invalid-feedback">{{ error_popular_templates }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-preview">{{ text_preview }}</label>
            <div class="col-sm-10">
              <input type="text" name="preview" value="{{ preview }}" placeholder="{{ text_preview }}" id="input-preview" class="form-control" />
              {% if error_preview %}
                <div class="invalid-feedback">{{ error_preview }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-recent_templates">{{ text_recent_templates }}</label>
            <div class="col-sm-10">
              <input type="text" name="recent_templates" value="{{ recent_templates }}" placeholder="{{ text_recent_templates }}" id="input-recent_templates" class="form-control" />
              {% if error_recent_templates %}
                <div class="invalid-feedback">{{ error_recent_templates }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-save_template">{{ text_save_template }}</label>
            <div class="col-sm-10">
              <input type="text" name="save_template" value="{{ save_template }}" placeholder="{{ text_save_template }}" id="input-save_template" class="form-control" />
              {% if error_save_template %}
                <div class="invalid-feedback">{{ error_save_template }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-specialized_templates">{{ text_specialized_templates }}</label>
            <div class="col-sm-10">
              <input type="text" name="specialized_templates" value="{{ specialized_templates }}" placeholder="{{ text_specialized_templates }}" id="input-specialized_templates" class="form-control" />
              {% if error_specialized_templates %}
                <div class="invalid-feedback">{{ error_specialized_templates }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-template_builder">{{ text_template_builder }}</label>
            <div class="col-sm-10">
              <input type="text" name="template_builder" value="{{ template_builder }}" placeholder="{{ text_template_builder }}" id="input-template_builder" class="form-control" />
              {% if error_template_builder %}
                <div class="invalid-feedback">{{ error_template_builder }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-template_engine_features">{{ text_template_engine_features }}</label>
            <div class="col-sm-10">
              <input type="text" name="template_engine_features" value="{{ template_engine_features }}" placeholder="{{ text_template_engine_features }}" id="input-template_engine_features" class="form-control" />
              {% if error_template_engine_features %}
                <div class="invalid-feedback">{{ error_template_engine_features }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-template_stats">{{ text_template_stats }}</label>
            <div class="col-sm-10">
              <input type="text" name="template_stats" value="{{ template_stats }}" placeholder="{{ text_template_stats }}" id="input-template_stats" class="form-control" />
              {% if error_template_stats %}
                <div class="invalid-feedback">{{ error_template_stats }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-template_types">{{ text_template_types }}</label>
            <div class="col-sm-10">
              <input type="text" name="template_types" value="{{ template_types }}" placeholder="{{ text_template_types }}" id="input-template_types" class="form-control" />
              {% if error_template_types %}
                <div class="invalid-feedback">{{ error_template_types }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-templates">{{ text_templates }}</label>
            <div class="col-sm-10">
              <input type="text" name="templates" value="{{ templates }}" placeholder="{{ text_templates }}" id="input-templates" class="form-control" />
              {% if error_templates %}
                <div class="invalid-feedback">{{ error_templates }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-total">{{ text_total }}</label>
            <div class="col-sm-10">
              <input type="text" name="total" value="{{ total }}" placeholder="{{ text_total }}" id="input-total" class="form-control" />
              {% if error_total %}
                <div class="invalid-feedback">{{ error_total }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="invalid-feedback">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}