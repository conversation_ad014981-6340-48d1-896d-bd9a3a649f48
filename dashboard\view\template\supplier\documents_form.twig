{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="submit" form="supplier\documents-form" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fas fa-save"></i></button>
        <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-light"><i class="fas fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    
    <div class="card">
      <div class="card-header"><i class="fas fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}chart-bar{% elif screen_type == 'settings' %}cog{% else %}pencil-alt{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="card-body">
        <form id="supplier\documents-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-add">{{ text_add }}</label>
            <div class="col-sm-10">
              <input type="text" name="add" value="{{ add }}" placeholder="{{ text_add }}" id="input-add" class="form-control" />
              {% if error_add %}
                <div class="invalid-feedback">{{ error_add }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-back">{{ text_back }}</label>
            <div class="col-sm-10">
              <input type="text" name="back" value="{{ back }}" placeholder="{{ text_back }}" id="input-back" class="form-control" />
              {% if error_back %}
                <div class="invalid-feedback">{{ error_back }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-current_file">{{ text_current_file }}</label>
            <div class="col-sm-10">
              <input type="text" name="current_file" value="{{ current_file }}" placeholder="{{ text_current_file }}" id="input-current_file" class="form-control" />
              {% if error_current_file %}
                <div class="invalid-feedback">{{ error_current_file }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-delete">{{ text_delete }}</label>
            <div class="col-sm-10">
              <input type="text" name="delete" value="{{ delete }}" placeholder="{{ text_delete }}" id="input-delete" class="form-control" />
              {% if error_delete %}
                <div class="invalid-feedback">{{ error_delete }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-description">{{ text_description }}</label>
            <div class="col-sm-10">
              <input type="text" name="description" value="{{ description }}" placeholder="{{ text_description }}" id="input-description" class="form-control" />
              {% if error_description %}
                <div class="invalid-feedback">{{ error_description }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-document">{{ text_document }}</label>
            <div class="col-sm-10">
              <input type="text" name="document" value="{{ document }}" placeholder="{{ text_document }}" id="input-document" class="form-control" />
              {% if error_document %}
                <div class="invalid-feedback">{{ error_document }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-document_history">{{ text_document_history }}</label>
            <div class="col-sm-10">
              <input type="text" name="document_history" value="{{ document_history }}" placeholder="{{ text_document_history }}" id="input-document_history" class="form-control" />
              {% if error_document_history %}
                <div class="invalid-feedback">{{ error_document_history }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-document_type">{{ text_document_type }}</label>
            <div class="col-sm-10">
              <input type="text" name="document_type" value="{{ document_type }}" placeholder="{{ text_document_type }}" id="input-document_type" class="form-control" />
              {% if error_document_type %}
                <div class="invalid-feedback">{{ error_document_type }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-document_types">{{ text_document_types }}</label>
            <div class="col-sm-10">
              <input type="text" name="document_types" value="{{ document_types }}" placeholder="{{ text_document_types }}" id="input-document_types" class="form-control" />
              {% if error_document_types %}
                <div class="invalid-feedback">{{ error_document_types }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-document_versions">{{ text_document_versions }}</label>
            <div class="col-sm-10">
              <input type="text" name="document_versions" value="{{ document_versions }}" placeholder="{{ text_document_versions }}" id="input-document_versions" class="form-control" />
              {% if error_document_versions %}
                <div class="invalid-feedback">{{ error_document_versions }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-documents">{{ text_documents }}</label>
            <div class="col-sm-10">
              <input type="text" name="documents" value="{{ documents }}" placeholder="{{ text_documents }}" id="input-documents" class="form-control" />
              {% if error_documents %}
                <div class="invalid-feedback">{{ error_documents }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-download">{{ text_download }}</label>
            <div class="col-sm-10">
              <input type="text" name="download" value="{{ download }}" placeholder="{{ text_download }}" id="input-download" class="form-control" />
              {% if error_download %}
                <div class="invalid-feedback">{{ error_download }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-edit">{{ text_edit }}</label>
            <div class="col-sm-10">
              <input type="text" name="edit" value="{{ edit }}" placeholder="{{ text_edit }}" id="input-edit" class="form-control" />
              {% if error_edit %}
                <div class="invalid-feedback">{{ error_edit }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_document_type">{{ text_error_document_type }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_document_type" value="{{ error_document_type }}" placeholder="{{ text_error_document_type }}" id="input-error_document_type" class="form-control" />
              {% if error_error_document_type %}
                <div class="invalid-feedback">{{ error_error_document_type }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_supplier">{{ text_error_supplier }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_supplier" value="{{ error_supplier }}" placeholder="{{ text_error_supplier }}" id="input-error_supplier" class="form-control" />
              {% if error_error_supplier %}
                <div class="invalid-feedback">{{ error_error_supplier }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_title">{{ text_error_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_title" value="{{ error_title }}" placeholder="{{ text_error_title }}" id="input-error_title" class="form-control" />
              {% if error_error_title %}
                <div class="invalid-feedback">{{ error_error_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-expiry_date">{{ text_expiry_date }}</label>
            <div class="col-sm-10">
              <input type="text" name="expiry_date" value="{{ expiry_date }}" placeholder="{{ text_expiry_date }}" id="input-expiry_date" class="form-control" />
              {% if error_expiry_date %}
                <div class="invalid-feedback">{{ error_expiry_date }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_document_type">{{ text_filter_document_type }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_document_type" value="{{ filter_document_type }}" placeholder="{{ text_filter_document_type }}" id="input-filter_document_type" class="form-control" />
              {% if error_filter_document_type %}
                <div class="invalid-feedback">{{ error_filter_document_type }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_status">{{ text_filter_status }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_status" value="{{ filter_status }}" placeholder="{{ text_filter_status }}" id="input-filter_status" class="form-control" />
              {% if error_filter_status %}
                <div class="invalid-feedback">{{ error_filter_status }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_supplier_id">{{ text_filter_supplier_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_supplier_id" value="{{ filter_supplier_id }}" placeholder="{{ text_filter_supplier_id }}" id="input-filter_supplier_id" class="form-control" />
              {% if error_filter_supplier_id %}
                <div class="invalid-feedback">{{ error_filter_supplier_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_title">{{ text_filter_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_title" value="{{ filter_title }}" placeholder="{{ text_filter_title }}" id="input-filter_title" class="form-control" />
              {% if error_filter_title %}
                <div class="invalid-feedback">{{ error_filter_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="invalid-feedback">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-order">{{ text_order }}</label>
            <div class="col-sm-10">
              <input type="text" name="order" value="{{ order }}" placeholder="{{ text_order }}" id="input-order" class="form-control" />
              {% if error_order %}
                <div class="invalid-feedback">{{ error_order }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-pagination">{{ text_pagination }}</label>
            <div class="col-sm-10">
              <input type="text" name="pagination" value="{{ pagination }}" placeholder="{{ text_pagination }}" id="input-pagination" class="form-control" />
              {% if error_pagination %}
                <div class="invalid-feedback">{{ error_pagination }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-results">{{ text_results }}</label>
            <div class="col-sm-10">
              <input type="text" name="results" value="{{ results }}" placeholder="{{ text_results }}" id="input-results" class="form-control" />
              {% if error_results %}
                <div class="invalid-feedback">{{ error_results }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-selected">{{ text_selected }}</label>
            <div class="col-sm-10">
              <input type="text" name="selected" value="{{ selected }}" placeholder="{{ text_selected }}" id="input-selected" class="form-control" />
              {% if error_selected %}
                <div class="invalid-feedback">{{ error_selected }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort">{{ text_sort }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort" value="{{ sort }}" placeholder="{{ text_sort }}" id="input-sort" class="form-control" />
              {% if error_sort %}
                <div class="invalid-feedback">{{ error_sort }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_date_added">{{ text_sort_date_added }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_date_added" value="{{ sort_date_added }}" placeholder="{{ text_sort_date_added }}" id="input-sort_date_added" class="form-control" />
              {% if error_sort_date_added %}
                <div class="invalid-feedback">{{ error_sort_date_added }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_expiry_date">{{ text_sort_expiry_date }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_expiry_date" value="{{ sort_expiry_date }}" placeholder="{{ text_sort_expiry_date }}" id="input-sort_expiry_date" class="form-control" />
              {% if error_sort_expiry_date %}
                <div class="invalid-feedback">{{ error_sort_expiry_date }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_supplier">{{ text_sort_supplier }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_supplier" value="{{ sort_supplier }}" placeholder="{{ text_sort_supplier }}" id="input-sort_supplier" class="form-control" />
              {% if error_sort_supplier %}
                <div class="invalid-feedback">{{ error_sort_supplier }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_title">{{ text_sort_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_title" value="{{ sort_title }}" placeholder="{{ text_sort_title }}" id="input-sort_title" class="form-control" />
              {% if error_sort_title %}
                <div class="invalid-feedback">{{ error_sort_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_type">{{ text_sort_type }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_type" value="{{ sort_type }}" placeholder="{{ text_sort_type }}" id="input-sort_type" class="form-control" />
              {% if error_sort_type %}
                <div class="invalid-feedback">{{ error_sort_type }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-status">{{ text_status }}</label>
            <div class="col-sm-10">
              <input type="text" name="status" value="{{ status }}" placeholder="{{ text_status }}" id="input-status" class="form-control" />
              {% if error_status %}
                <div class="invalid-feedback">{{ error_status }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-supplier_id">{{ text_supplier_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="supplier_id" value="{{ supplier_id }}" placeholder="{{ text_supplier_id }}" id="input-supplier_id" class="form-control" />
              {% if error_supplier_id %}
                <div class="invalid-feedback">{{ error_supplier_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-suppliers">{{ text_suppliers }}</label>
            <div class="col-sm-10">
              <input type="text" name="suppliers" value="{{ suppliers }}" placeholder="{{ text_suppliers }}" id="input-suppliers" class="form-control" />
              {% if error_suppliers %}
                <div class="invalid-feedback">{{ error_suppliers }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-tags">{{ text_tags }}</label>
            <div class="col-sm-10">
              <input type="text" name="tags" value="{{ tags }}" placeholder="{{ text_tags }}" id="input-tags" class="form-control" />
              {% if error_tags %}
                <div class="invalid-feedback">{{ error_tags }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_form">{{ text_text_form }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_form" value="{{ text_form }}" placeholder="{{ text_text_form }}" id="input-text_form" class="form-control" />
              {% if error_text_form %}
                <div class="invalid-feedback">{{ error_text_form }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-title">{{ text_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="title" value="{{ title }}" placeholder="{{ text_title }}" id="input-title" class="form-control" />
              {% if error_title %}
                <div class="invalid-feedback">{{ error_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="invalid-feedback">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}