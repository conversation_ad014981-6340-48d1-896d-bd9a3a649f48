<?php
// Heading
$_['heading_title']                    = 'Trial Balance';

// Text
$_['text_success']                     = 'Success: Trial Balance has been generated successfully!';
$_['text_list']                        = 'Trial Balance List';
$_['text_form']                        = 'Trial Balance Form';
$_['text_view']                        = 'View Trial Balance';
$_['text_generate']                    = 'Generate Trial Balance';
$_['text_export']                      = 'Export Trial Balance';
$_['text_compare']                     = 'Compare Trial Balance';
$_['text_print']                       = 'Print Trial Balance';
$_['text_account_start']               = 'Account Start';
$_['text_account_end']                 = 'Account End';
$_['text_date_start']                  = 'Date Start';
$_['text_date_end']                    = 'Date End';
$_['text_no_results']                  = 'No results found!';
$_['text_loading']                     = 'Loading...';
$_['text_processing']                  = 'Processing...';
$_['text_success_generate']            = 'Trial Balance generated successfully!';
$_['text_success_export']              = 'Trial Balance exported successfully!';
$_['text_success_compare']             = 'Comparison completed successfully!';

// Column
$_['column_account_code']              = 'Account Code';
$_['column_account_name']              = 'Account Name';
$_['column_debit_balance']             = 'Debit Balance';
$_['column_credit_balance']            = 'Credit Balance';
$_['column_opening_balance']           = 'Opening Balance';
$_['column_closing_balance']           = 'Closing Balance';
$_['column_movement']                  = 'Movement';
$_['column_variance']                  = 'Variance';
$_['column_percentage']                = 'Percentage';
$_['column_action']                    = 'Action';

// Entry
$_['entry_date_start']                 = 'Start Date';
$_['entry_date_end']                   = 'End Date';
$_['entry_account_start']              = 'Start Account';
$_['entry_account_end']                = 'End Account';
$_['entry_branch']                     = 'Branch';
$_['entry_cost_center']                = 'Cost Center';
$_['entry_include_zero_balance']       = 'Include Zero Balance Accounts';
$_['entry_comparison_period']          = 'Comparison Period';
$_['entry_export_format']              = 'Export Format';

// Button
$_['button_generate']                  = 'Generate';
$_['button_export']                    = 'Export';
$_['button_compare']                   = 'Compare';
$_['button_print']                     = 'Print';
$_['button_drill_down']                = 'Drill Down';
$_['button_submit']                    = 'Submit';
$_['button_reset']                     = 'Reset';
$_['button_close']                     = 'Close';

// Tab
$_['tab_general']                      = 'General';
$_['tab_filters']                      = 'Filters';
$_['tab_options']                      = 'Options';
$_['tab_comparison']                   = 'Comparison';

// Help
$_['help_date_range']                  = 'Select the time period for trial balance';
$_['help_account_range']               = 'Select account range (optional)';
$_['help_branch']                      = 'Select specific branch or all branches';
$_['help_include_zero']                = 'Include accounts with zero balance';

// Error
$_['error_warning']                    = 'Warning: Please check the form carefully for errors!';
$_['error_permission']                 = 'Warning: You do not have permission to access Trial Balance!';
$_['error_date_start']                 = 'Start date is required!';
$_['error_date_end']                   = 'End date is required!';
$_['error_date_range']                 = 'Start date must be before end date!';
$_['error_account']                    = 'At least one account must be selected!';
$_['error_no_data']                    = 'No data to display!';
$_['error_export']                     = 'Error exporting data!';
$_['error_form']                       = 'Form data error!';
$_['error_missing_data']               = 'Missing data!';
$_['error_period1']                    = 'First period is required for comparison!';
$_['error_period2']                    = 'Second period is required for comparison!';

// Export Formats
$_['text_excel']                       = 'Excel';
$_['text_pdf']                         = 'PDF';
$_['text_csv']                         = 'CSV';

// Print Options
$_['text_print_title']                 = 'Trial Balance';
$_['text_print_date']                  = 'Print Date';
$_['text_print_user']                  = 'Printed By';
$_['text_print_period']                = 'Period';
$_['text_total_debit']                 = 'Total Debit';
$_['text_total_credit']                = 'Total Credit';

// Comparison
$_['text_period_1']                    = 'Period 1';
$_['text_period_2']                    = 'Period 2';
$_['text_variance_amount']             = 'Amount Variance';
$_['text_variance_percentage']         = 'Percentage Variance';
$_['text_increase']                    = 'Increase';
$_['text_decrease']                    = 'Decrease';

// Drill Down
$_['text_drill_down_title']            = 'Account Drill Down';
$_['text_transaction_details']         = 'Transaction Details';
$_['text_journal_entries']             = 'Journal Entries';

// Status Messages
$_['text_generating']                  = 'Generating trial balance...';
$_['text_exporting']                   = 'Exporting data...';
$_['text_comparing']                   = 'Performing comparison...';
$_['text_completed']                   = 'Completed successfully!';

// Generate Options
$_['button_generate_and_new']          = 'Generate and New';
$_['button_generate_and_export']       = 'Generate and Export';

// Advanced Filters
$_['text_all_accounts']                = 'All Accounts';
$_['text_assets']                      = 'Assets';
$_['text_liabilities']                 = 'Liabilities';
$_['text_equity']                      = 'Equity';
$_['text_revenue']                     = 'Revenue';
$_['text_expense']                     = 'Expenses';
$_['text_all_balances']                = 'All Balances';
$_['text_debit_balances_only']         = 'Debit Balances Only';
$_['text_credit_balances_only']        = 'Credit Balances Only';
$_['text_zero_balances']               = 'Zero Balances';
$_['text_non_zero_balances']           = 'Non-Zero Balances';

// Missing Variables from Audit Report
$_['accounts/trial_balance'] = '';
$_['code'] = '';
$_['date_format_short'] = '';
$_['direction'] = '';
$_['error_invalid_request'] = '';
$_['error_no_analysis_data'] = '';
$_['lang'] = '';
$_['print_title'] = '';
$_['text_advanced_analysis'] = '';
$_['text_analysis_view'] = '';
$_['text_balance_unverified'] = '';
$_['text_balance_verified'] = '';
$_['text_home'] = '';
$_['text_success_analysis'] = '';
$_['title'] = '';

// New language variables for trial balance controller fixes
$_['log_unauthorized_access_trial_balance'] = 'Unauthorized access attempt to trial balance';
$_['log_view_trial_balance_screen'] = 'View trial balance screen';
$_['log_unauthorized_generate_trial_balance'] = 'Unauthorized trial balance generation attempt';
$_['log_generate_trial_balance_period'] = 'Generate trial balance for period';
$_['text_to'] = 'to';
$_['text_generate_trial_balance'] = 'Generate trial balance';
$_['text_trial_balance_generated_notification'] = 'Trial balance generated';
$_['text_for_period'] = 'for period';
$_['log_view_trial_balance'] = 'View trial balance';
$_['log_unauthorized_export_trial_balance'] = 'Unauthorized trial balance export attempt';
$_['log_export_trial_balance'] = 'Export trial balance';
$_['text_export_trial_balance'] = 'Export trial balance';
$_['text_trial_balance_exported_notification'] = 'Trial balance exported';
$_['text_format'] = 'in format';
$_['log_unauthorized_compare_trial_balance'] = 'Unauthorized trial balance comparison attempt';
$_['log_compare_trial_balance_periods'] = 'Compare trial balance between periods';
$_['log_drill_down_account_trial_balance'] = 'Drill down account in trial balance';
$_['log_unauthorized_access_advanced_trial_balance'] = 'Unauthorized access attempt to advanced trial balance analysis';
$_['log_generate_advanced_trial_balance_analysis'] = 'Generate advanced trial balance analysis';
$_['text_advanced_trial_balance_analysis'] = 'Advanced trial balance analysis';
$_['text_advanced_trial_balance_analysis_generated'] = 'Advanced trial balance analysis generated';
$_['log_view_advanced_trial_balance_analysis'] = 'View advanced trial balance analysis';

// Enhanced performance and analytics variables
$_['text_optimized_trial_balance']     = 'Optimized Trial Balance';
$_['text_trial_balance_analysis']      = 'Trial Balance Analysis';
$_['text_cache_enabled']               = 'Cache Enabled';
$_['text_enhanced_analysis']           = 'Enhanced Analysis';
$_['text_balance_summary']             = 'Balance Summary';
$_['text_is_balanced']                 = 'Balanced';
$_['text_difference']                  = 'Difference';
$_['text_account_types']               = 'Account Types';
$_['text_account_count']               = 'Account Count';
$_['text_total_balance']               = 'Total Balance';
$_['text_balance_verification']        = 'Balance Verification';
$_['button_trial_balance_analysis']    = 'Trial Balance Analysis';
$_['text_loading_analysis']            = 'Loading trial balance analysis...';
$_['text_analysis_ready']              = 'Analysis ready';
?>
