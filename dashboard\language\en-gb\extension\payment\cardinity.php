<?php
// Heading
$_['heading_title']			= 'Cardinity';

// Text
$_['text_extension']		= 'Extensions';
$_['text_success']			= 'Success: You have modified Cardinity payment module!';
$_['text_edit']             = 'Edit Cardinity';
$_['text_cardinity']		= '<a href="http://cardinity.com/?crdp=opencart" target="_blank"><img src="view/image/payment/cardinity.png" alt="Cardinity" title="Cardinity" style="border: 1px solid #EEEEEE;" /></a>';
$_['text_production']		= 'Production';
$_['text_sandbox']			= 'Sandbox';
$_['text_payment_info']		= 'Refund information';
$_['text_no_refund']		= 'No refund history';
$_['text_confirm_refund']	= 'Are you sure you want to refund';
$_['text_na']				= 'N/A';
$_['text_success_action']	= 'Success';
$_['text_error_generic']	= 'Error: There was an error with your request. Please check the logs.';
$_['text_amount']			= 'Amount';
$_['text_description']	    = 'Description';

// Column
$_['column_refund']			= 'Refund';
$_['column_date']			= 'Date';
$_['column_refund_history'] = 'Refund History';
$_['column_action']			= 'Action';
$_['column_status']			= 'Status';
$_['column_amount']			= 'Amount';
$_['column_description']	= 'Description';

// Entry
$_['entry_total']			= 'Total';
$_['entry_order_status']	= 'Order Status';
$_['entry_geo_zone']		= 'Geo Zone';
$_['entry_status']			= 'Status';
$_['entry_sort_order']		= 'Sort Order';
$_['entry_key']				= 'Key';
$_['entry_secret']			= 'Secret';
$_['entry_debug']			= 'Debug';

// Help
$_['help_debug']			= 'Enabling debug will write sensitive data to a log file. You should always disable unless instructed otherwise.';
$_['help_total']			= 'The checkout total the order must reach before this payment method becomes active.';

// Button
$_['button_refund']			= 'Refund';

// Error
$_['error_key']				= 'Key Required!';
$_['error_secret']			= 'Secret Required!';
$_['error_composer']		= 'Unable to load Cardinity SDK. Please download a compiled vendor folder or run composer.';
$_['error_php_version']		= 'Minimum version of PHP 5.4.0 is required!';
$_['error_permission']		= 'Warning: You do not have permission to modify payment Cardinity!';
$_['error_connection']		= 'There was a problem establishing a connection to the Cardinity API. Please check your Key and Secret settings.';
$_['error_transaction']     = 'Unable to find transaction for this order.';
$_['error_warning']			= 'Warning: Please check the form carefully for errors!';