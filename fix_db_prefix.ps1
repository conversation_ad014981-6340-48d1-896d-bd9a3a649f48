# سكريبت PowerShell لتصحيح DB_PREFIX في dashboard.php
# يستبدل جميع استخدامات DB_PREFIX بـ cod_ مباشرة

$filePath = "dashboard\model\common\dashboard.php"

if (-not (Test-Path $filePath)) {
    Write-Host "الملف غير موجود: $filePath" -ForegroundColor Red
    exit 1
}

Write-Host "🔧 بدء تصحيح DB_PREFIX في $filePath" -ForegroundColor Yellow

# قراءة محتوى الملف
$content = Get-Content $filePath -Raw -Encoding UTF8

# إنشاء نسخة احتياطية
$backupFile = "$filePath.backup.$(Get-Date -Format 'yyyy-MM-dd-HH-mm-ss')"
Copy-Item $filePath $backupFile
Write-Host "📁 تم إنشاء نسخة احتياطية: $backupFile" -ForegroundColor Green

# قائمة الاستبدالات الأساسية للجداول الموجودة
$replacements = @{
    'DB_PREFIX . "order"' = '"cod_order"'
    'DB_PREFIX . "order_product"' = '"cod_order_product"'
    'DB_PREFIX . "order_total"' = '"cod_order_total"'
    'DB_PREFIX . "customer"' = '"cod_customer"'
    'DB_PREFIX . "product"' = '"cod_product"'
    'DB_PREFIX . "product_description"' = '"cod_product_description"'
    'DB_PREFIX . "category"' = '"cod_category"'
    'DB_PREFIX . "category_description"' = '"cod_category_description"'
    'DB_PREFIX . "supplier"' = '"cod_supplier"'
    'DB_PREFIX . "purchase_order"' = '"cod_purchase_order"'
    'DB_PREFIX . "branch"' = '"cod_branch"'
    'DB_PREFIX . "branch_address"' = '"cod_branch_address"'
    'DB_PREFIX . "user"' = '"cod_user"'
    'DB_PREFIX . "user_session"' = '"cod_user_session"'
    'DB_PREFIX . "cart"' = '"cod_cart"'
    'DB_PREFIX . "cart_product"' = '"cod_cart_product"'
    'DB_PREFIX . "return"' = '"cod_return"'
    'DB_PREFIX . "crm_campaign"' = '"cod_crm_campaign"'
    'DB_PREFIX . "crm_lead"' = '"cod_crm_lead"'
    'DB_PREFIX . "accounts"' = '"cod_accounts"'
    'DB_PREFIX . "journal_entries"' = '"cod_journal_entries"'
    'DB_PREFIX . "bank_account"' = '"cod_bank_account"'
    'DB_PREFIX . "cash"' = '"cod_cash"'
    'DB_PREFIX . "currency"' = '"cod_currency"'
    'DB_PREFIX . "language"' = '"cod_language"'
    'DB_PREFIX . "setting"' = '"cod_setting"'
    
    # النمط الثاني
    '" . DB_PREFIX . "order' = '"cod_order'
    '" . DB_PREFIX . "order_product' = '"cod_order_product'
    '" . DB_PREFIX . "order_total' = '"cod_order_total'
    '" . DB_PREFIX . "customer' = '"cod_customer'
    '" . DB_PREFIX . "product' = '"cod_product'
    '" . DB_PREFIX . "product_description' = '"cod_product_description'
    '" . DB_PREFIX . "category' = '"cod_category'
    '" . DB_PREFIX . "category_description' = '"cod_category_description'
    '" . DB_PREFIX . "supplier' = '"cod_supplier'
    '" . DB_PREFIX . "purchase_order' = '"cod_purchase_order'
    '" . DB_PREFIX . "branch' = '"cod_branch'
    '" . DB_PREFIX . "branch_address' = '"cod_branch_address'
    '" . DB_PREFIX . "user' = '"cod_user'
    '" . DB_PREFIX . "user_session' = '"cod_user_session'
    '" . DB_PREFIX . "cart' = '"cod_cart'
    '" . DB_PREFIX . "cart_product' = '"cod_cart_product'
    '" . DB_PREFIX . "return' = '"cod_return'
    '" . DB_PREFIX . "crm_campaign' = '"cod_crm_campaign'
    '" . DB_PREFIX . "crm_lead' = '"cod_crm_lead'
    '" . DB_PREFIX . "accounts' = '"cod_accounts'
    '" . DB_PREFIX . "journal_entries' = '"cod_journal_entries'
    '" . DB_PREFIX . "bank_account' = '"cod_bank_account'
    '" . DB_PREFIX . "cash' = '"cod_cash'
    '" . DB_PREFIX . "currency' = '"cod_currency'
    '" . DB_PREFIX . "language' = '"cod_language'
    '" . DB_PREFIX . "setting' = '"cod_setting'
}

# استبدالات الجداول غير الموجودة بالبدائل
$nonExistingReplacements = @{
    'DB_PREFIX . "employee"' = '"cod_user"'
    'DB_PREFIX . "invoice"' = '"cod_supplier_invoice"'
    'DB_PREFIX . "shipment"' = '"cod_shipping_order"'
    'DB_PREFIX . "project_task"' = '"cod_task"'
    'DB_PREFIX . "calendar_event"' = '"cod_meeting"'
    'DB_PREFIX . "marketing_campaign"' = '"cod_crm_campaign"'
    'DB_PREFIX . "customer_complaint"' = '"cod_customer_feedback"'
    'DB_PREFIX . "system_event"' = '"cod_activity_log"'
    'DB_PREFIX . "user_activity"' = '"cod_user_activity_log"'
    'DB_PREFIX . "branch_inventory_snapshot"' = '"cod_product_inventory"'
    'DB_PREFIX . "order_item"' = '"cod_order_product"'
    'DB_PREFIX . "product_to_category"' = '"cod_product_to_category"'
    'DB_PREFIX . "customer_online"' = '"cod_customer_online"'
    'DB_PREFIX . "eta_documents"' = '"cod_eta_documents"'
    
    # النمط الثاني
    '" . DB_PREFIX . "employee' = '"cod_user'
    '" . DB_PREFIX . "invoice' = '"cod_supplier_invoice'
    '" . DB_PREFIX . "shipment' = '"cod_shipping_order'
    '" . DB_PREFIX . "project_task' = '"cod_task'
    '" . DB_PREFIX . "calendar_event' = '"cod_meeting'
    '" . DB_PREFIX . "marketing_campaign' = '"cod_crm_campaign'
    '" . DB_PREFIX . "customer_complaint' = '"cod_customer_feedback'
    '" . DB_PREFIX . "system_event' = '"cod_activity_log'
    '" . DB_PREFIX . "user_activity' = '"cod_user_activity_log'
    '" . DB_PREFIX . "branch_inventory_snapshot' = '"cod_product_inventory'
    '" . DB_PREFIX . "order_item' = '"cod_order_product'
    '" . DB_PREFIX . "product_to_category' = '"cod_product_to_category'
    '" . DB_PREFIX . "customer_online' = '"cod_customer_online'
    '" . DB_PREFIX . "eta_documents' = '"cod_eta_documents'
}

# تطبيق الاستبدالات الأساسية
$count = 0
foreach ($old in $replacements.Keys) {
    $new = $replacements[$old]
    $oldCount = ($content | Select-String $old -AllMatches).Matches.Count
    if ($oldCount -gt 0) {
        $content = $content -replace [regex]::Escape($old), $new
        $count += $oldCount
        Write-Host "✅ استبدال $oldCount من: $old" -ForegroundColor Green
    }
}

# تطبيق استبدالات الجداول غير الموجودة
foreach ($old in $nonExistingReplacements.Keys) {
    $new = $nonExistingReplacements[$old]
    $oldCount = ($content | Select-String $old -AllMatches).Matches.Count
    if ($oldCount -gt 0) {
        $content = $content -replace [regex]::Escape($old), $new
        $count += $oldCount
        Write-Host "🔄 استبدال جدول غير موجود $oldCount من: $old -> $new" -ForegroundColor Yellow
    }
}

# حفظ الملف المُصحح
$content | Set-Content $filePath -Encoding UTF8

# إحصائيات النتائج
$remainingDbPrefix = ($content | Select-String "DB_PREFIX" -AllMatches).Matches.Count

Write-Host "`n📊 تقرير التصحيح:" -ForegroundColor Cyan
Write-Host "✅ إجمالي الاستبدالات: $count" -ForegroundColor Green
Write-Host "📁 النسخة الاحتياطية: $backupFile" -ForegroundColor Blue
Write-Host "⚠️  استخدامات DB_PREFIX المتبقية: $remainingDbPrefix" -ForegroundColor $(if ($remainingDbPrefix -eq 0) { "Green" } else { "Yellow" })

if ($remainingDbPrefix -eq 0) {
    Write-Host "`n🎉 تم تصحيح جميع استخدامات DB_PREFIX بنجاح!" -ForegroundColor Green
} else {
    Write-Host "`n⚠️  تحذير: لا تزال هناك استخدامات لـ DB_PREFIX تحتاج مراجعة يدوية" -ForegroundColor Yellow
}

Write-Host "`n🔧 انتهى التصحيح!" -ForegroundColor Cyan
