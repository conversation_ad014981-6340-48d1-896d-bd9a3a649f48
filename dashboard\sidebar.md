<?xml version="1.0" encoding="utf-8"?>
<modification>
    <code>como_admin_tools</code>
    <name>Como Admin tools - Admin menu toggle button (Collapsed by Default)</name>
    <version>1.0.2</version>
    <author>esoft.cmstory.com</author>
    <link><![CDATA[https://www.opencart.com/index.php?route=marketplace/extension/info&extension_id=36078]]></link>
    <!-- Admin menu toggle button -->
    <file path="admin/view/template/common/header.twig">
        <operation error="skip">
        <search><![CDATA[id="header-logo"]]></search>
        <add position="before"><![CDATA[
    <a href="#" id="button-menu-toggle" class="hidden-xs"><span class="fa fa-bars"></span></a>
{% if direction=="rtl" %}    
<style>
#button-menu-toggle {
    font-size: 24px;
    float: right;
    padding: 10px 16px;
    color: #6D6D6D;
    border-right: 1px solid #eee;
}
#column-left {
    right: -280px;
}
#content {
    width: 100%;
}
</style>
<script>
   $(document).ready(function() {
        $('#button-menu-toggle').on('click', function(e) {
            e.preventDefault();
            var currentRight = $('#column-left').css('right') == '0px' ? '-280px' : '0px';
            $('#column-left').css('right', currentRight);
            var contentWidth = currentRight == '0px' ? 'calc(100% - 280px)' : '100%';
            var rightWidth = currentRight == '0px' ?  '280px' : '0px';
            
            $('#content').css({'width':contentWidth,right:rightWidth});
        });
        $(window).on('resize', function() {
            if ($(window).width() <= 768) {
                $('#column-left, #content').css({right: '', width: ''});
            }
        });
    });
</script>
{% else %}
<style>
#button-menu-toggle {
    font-size: 24px;
    float: left;
    padding: 10px 16px;
    color: #6D6D6D;
    border-right: 1px solid #eee;
}
#column-left {
    left: -280px;
}
#content {
    width: 100%;
}
</style>
<script>
    $(document).ready(function() {
    	$('#button-menu-toggle').on('click', function(e) {
    		e.preventDefault();
    		if ($('#column-left').offset().left >= 0) {
    		    $('#column-left').offset({left: -280});
    		    $('#content').offset({left: 0}).css('width', '100%');
    		} else {
    		    $('#column-left').offset({left: 0});
    		    $('#content').offset({left: 280}).css('width', 'calc(100% - 280px)');
            }
    	});
        $(window).on('resize', function() {
            if ($(window).width() <= 768) {
                $('#column-left,#content').css({left: '', width: ''});
            }
        });
    });
</script>
{% endif %}
        ]]></add>
        </operation>
    </file>
</modification>