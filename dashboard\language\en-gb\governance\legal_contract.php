<?php
// Heading
$_['heading_title'] = 'Legal Contracts';

// Text
$_['text_list'] = 'Contract List';
$_['text_modal_add'] = 'Add Contract';
$_['text_modal_edit'] = 'Modify Contract';
$_['text_confirm_delete'] = 'Are you sure you want to delete?';
$_['text_home'] = 'Home';

// Statuses
$_['text_all_statuses'] = '- All Statuses -';
$_['text_draft'] = 'Draft';
$_['text_active'] = 'Active';
$_['text_expired'] = 'Expired';
$_['text_terminated'] = 'Terminated/Expired';

// Entry
$_['entry_status'] = 'Status';
$_['entry_contract_type'] = 'Contract Type';
$_['entry_title'] = 'Contract Title';
$_['entry_party_id'] = 'Party/Branch Number';
$_['entry_start_date'] = 'Start Date';
$_['entry_end_date'] = 'End Date';
$_['entry_value'] = 'Financial Value';
$_['entry_description'] = 'Description';

// Columns
$_['column_contract_id'] = 'Number';
$_['column_contract_type']= 'Contract Type';
$_['column_title'] = 'Title';
$_['column_start_date'] = 'Start Date';
$_['column_end_date'] = 'Ending';
$_['column_status'] = 'Status';
$_['column_value'] = 'Value';
$_['column_action'] = 'Action';

// Buttons
$_['button_filter'] = 'Filter';
$_['button_add'] = 'Add';
$_['button_edit'] = 'Edit';
$_['button_delete'] = 'Delete';
$_['button_save'] = 'Save';
$_['button_close'] = 'Close';