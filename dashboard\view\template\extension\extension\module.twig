{{ promotion }}
<fieldset>
  <legend>{{ heading_title }}</legend>
  {% if error_warning %}
  <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
    <button type="button" class="close" data-dismiss="alert">&times;</button>
  </div>
  {% endif %}
  {% if success %}
  <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{ success }}
    <button type="button" class="close" data-dismiss="alert">&times;</button>
  </div>
  {% endif %}
  <div class="alert alert-info"><i class="fa fa-info-circle"></i> {{ text_layout }}</div>
  <div class="table-responsive">
    <table class="table table-bordered table-hover">
      <thead>
        <tr>
          <td class="text-left">{{ column_name }}</td>
          <td class="text-left">{{ column_status }}</td>
          <td class="text-right">{{ column_action }}</td>
        </tr>
      </thead>
      <tbody>
      
      {% if extensions %}
      {% for extension in extensions %}
      <tr>
        <td><b>{{ extension.name }}</b></td>
        <td>{{ extension.status }}</td>
        <td class="text-right">{% if extension.installed %}
          {% if extension.module %} <a href="{{ extension.edit }}" data-toggle="tooltip" title="{{ button_add }}" class="btn btn-primary"><i class="fa fa-plus-circle"></i></a> {% else %} <a href="{{ extension.edit }}" data-toggle="tooltip" title="{{ button_edit }}" class="btn btn-primary"><i class="fa fa-pencil"></i></a> {% endif %}
          {% else %}
          <button type="button" class="btn btn-primary" disabled="disabled"><i class="fa fa-pencil"></i></button>
          {% endif %}
          {% if not extension.installed %}<a href="{{ extension.install }}" data-toggle="tooltip" title="{{ button_install }}" class="btn btn-success"><i class="fa fa-plus-circle"></i></a> {% else %} <a href="{{ extension.uninstall }}" data-toggle="tooltip" title="{{ button_uninstall }}" class="btn btn-danger"><i class="fa fa-minus-circle"></i></a>{% endif %}</td>
      </tr>
      {% for module in extension.module %}
      <tr>
        <td class="text-left">&nbsp;&nbsp;&nbsp;<i class="fa fa-folder-open"></i>&nbsp;&nbsp;&nbsp;{{ module.name }}</td>
        <td class="text-left">{{ module.status }}</td>
        <td class="text-right"><a href="{{ module.edit }}" data-toggle="tooltip" title="{{ button_edit }}" class="btn btn-info"><i class="fa fa-pencil"></i></a> <a href="{{ module.delete }}" data-toggle="tooltip" title="{{ button_delete }}" class="btn btn-warning"><i class="fa fa-trash-o"></i></a></td>
      </tr>
      {% endfor %}
      {% endfor %}
      {% else %}
      <tr>
        <td class="text-center" colspan="3">{{ text_no_results }}</td>
      </tr>
      {% endif %}
      </tbody>
      
    </table>
  </div>
</fieldset>
