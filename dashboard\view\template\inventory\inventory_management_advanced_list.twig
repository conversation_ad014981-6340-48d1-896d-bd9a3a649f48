{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="submit" form="inventory\inventory_management_advanced-form" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fas fa-save"></i></button>
        <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-light"><i class="fas fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    
    <div class="card">
      <div class="card-header"><i class="fas fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}chart-bar{% elif screen_type == 'settings' %}cog{% else %}pencil-alt{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="card-body">
        <form id="inventory\inventory_management_advanced-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-analysis_url">{{ text_analysis_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="analysis_url" value="{{ analysis_url }}" placeholder="{{ text_analysis_url }}" id="input-analysis_url" class="form-control" />
              {% if error_analysis_url %}
                <div class="invalid-feedback">{{ error_analysis_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="invalid-feedback">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-inventory_items">{{ text_inventory_items }}</label>
            <div class="col-sm-10">
              <input type="text" name="inventory_items" value="{{ inventory_items }}" placeholder="{{ text_inventory_items }}" id="input-inventory_items" class="form-control" />
              {% if error_inventory_items %}
                <div class="invalid-feedback">{{ error_inventory_items }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-movement_history_url">{{ text_movement_history_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="movement_history_url" value="{{ movement_history_url }}" placeholder="{{ text_movement_history_url }}" id="input-movement_history_url" class="form-control" />
              {% if error_movement_history_url %}
                <div class="invalid-feedback">{{ error_movement_history_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-order">{{ text_order }}</label>
            <div class="col-sm-10">
              <input type="text" name="order" value="{{ order }}" placeholder="{{ text_order }}" id="input-order" class="form-control" />
              {% if error_order %}
                <div class="invalid-feedback">{{ error_order }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-pagination">{{ text_pagination }}</label>
            <div class="col-sm-10">
              <input type="text" name="pagination" value="{{ pagination }}" placeholder="{{ text_pagination }}" id="input-pagination" class="form-control" />
              {% if error_pagination %}
                <div class="invalid-feedback">{{ error_pagination }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-results">{{ text_results }}</label>
            <div class="col-sm-10">
              <input type="text" name="results" value="{{ results }}" placeholder="{{ text_results }}" id="input-results" class="form-control" />
              {% if error_results %}
                <div class="invalid-feedback">{{ error_results }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-revaluation">{{ text_revaluation }}</label>
            <div class="col-sm-10">
              <input type="text" name="revaluation" value="{{ revaluation }}" placeholder="{{ text_revaluation }}" id="input-revaluation" class="form-control" />
              {% if error_revaluation %}
                <div class="invalid-feedback">{{ error_revaluation }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort">{{ text_sort }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort" value="{{ sort }}" placeholder="{{ text_sort }}" id="input-sort" class="form-control" />
              {% if error_sort %}
                <div class="invalid-feedback">{{ error_sort }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_category">{{ text_sort_category }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_category" value="{{ sort_category }}" placeholder="{{ text_sort_category }}" id="input-sort_category" class="form-control" />
              {% if error_sort_category %}
                <div class="invalid-feedback">{{ error_sort_category }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_current_stock">{{ text_sort_current_stock }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_current_stock" value="{{ sort_current_stock }}" placeholder="{{ text_sort_current_stock }}" id="input-sort_current_stock" class="form-control" />
              {% if error_sort_current_stock %}
                <div class="invalid-feedback">{{ error_sort_current_stock }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_product_name">{{ text_sort_product_name }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_product_name" value="{{ sort_product_name }}" placeholder="{{ text_sort_product_name }}" id="input-sort_product_name" class="form-control" />
              {% if error_sort_product_name %}
                <div class="invalid-feedback">{{ error_sort_product_name }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_unit_cost">{{ text_sort_unit_cost }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_unit_cost" value="{{ sort_unit_cost }}" placeholder="{{ text_sort_unit_cost }}" id="input-sort_unit_cost" class="form-control" />
              {% if error_sort_unit_cost %}
                <div class="invalid-feedback">{{ error_sort_unit_cost }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-stock_adjustment">{{ text_stock_adjustment }}</label>
            <div class="col-sm-10">
              <input type="text" name="stock_adjustment" value="{{ stock_adjustment }}" placeholder="{{ text_stock_adjustment }}" id="input-stock_adjustment" class="form-control" />
              {% if error_stock_adjustment %}
                <div class="invalid-feedback">{{ error_stock_adjustment }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-stock_count">{{ text_stock_count }}</label>
            <div class="col-sm-10">
              <input type="text" name="stock_count" value="{{ stock_count }}" placeholder="{{ text_stock_count }}" id="input-stock_count" class="form-control" />
              {% if error_stock_count %}
                <div class="invalid-feedback">{{ error_stock_count }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-stock_levels_url">{{ text_stock_levels_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="stock_levels_url" value="{{ stock_levels_url }}" placeholder="{{ text_stock_levels_url }}" id="input-stock_levels_url" class="form-control" />
              {% if error_stock_levels_url %}
                <div class="invalid-feedback">{{ error_stock_levels_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-stock_movement">{{ text_stock_movement }}</label>
            <div class="col-sm-10">
              <input type="text" name="stock_movement" value="{{ stock_movement }}" placeholder="{{ text_stock_movement }}" id="input-stock_movement" class="form-control" />
              {% if error_stock_movement %}
                <div class="invalid-feedback">{{ error_stock_movement }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-stock_transfer">{{ text_stock_transfer }}</label>
            <div class="col-sm-10">
              <input type="text" name="stock_transfer" value="{{ stock_transfer }}" placeholder="{{ text_stock_transfer }}" id="input-stock_transfer" class="form-control" />
              {% if error_stock_transfer %}
                <div class="invalid-feedback">{{ error_stock_transfer }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="invalid-feedback">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-valuation_url">{{ text_valuation_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="valuation_url" value="{{ valuation_url }}" placeholder="{{ text_valuation_url }}" id="input-valuation_url" class="form-control" />
              {% if error_valuation_url %}
                <div class="invalid-feedback">{{ error_valuation_url }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}