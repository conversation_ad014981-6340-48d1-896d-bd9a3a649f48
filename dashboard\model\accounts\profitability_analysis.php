<?php
class ModelAccountsProfitabilityAnalysis extends Model {
    public function getProfitabilityData($date_start, $date_end) {
        $currency_code = $this->config->get('config_currency');

        // إيرادات المبيعات (نفترض جميع الإيرادات 4xxx)
        $revenue = $this->getSumForAccounts('4', $date_start, $date_end);

        // تكلفة المبيعات (مثلا 41xx أو 42xx حسب تصميم الحسابات، نفترض 42xx)
        $cogs = $this->getSumForAccounts('42', $date_start, $date_end);

        // المصاريف التشغيلية (نفترض 43xx و44xx)
        $operating_expenses = $this->getSumForAccounts('43', $date_start, $date_end) + $this->getSumForAccounts('44', $date_start, $date_end);

        // الربح الإجمالي = المبيعات - تكلفة المبيعات
        $gross_profit = $revenue - $cogs;
        // الربح التشغيلي = الربح الإجمالي - المصاريف التشغيلية
        $operating_profit = $gross_profit - $operating_expenses;

        // المصاريف الأخرى مثل الفوائد والضرائب (نفترض 45xx)
        $other_expenses = $this->getSumForAccounts('45', $date_start, $date_end);

        // صافي الربح = الربح التشغيلي - المصاريف الأخرى
        $net_profit = $operating_profit - $other_expenses;

        // حساب الهوامش
        $gross_margin = ($revenue != 0) ? ($gross_profit / $revenue) * 100 : 0;
        $operating_margin = ($revenue != 0) ? ($operating_profit / $revenue) * 100 : 0;
        $net_margin = ($revenue != 0) ? ($net_profit / $revenue) * 100 : 0;

        return [
            'revenue' => $this->currency->format($revenue, $currency_code),
            'cogs' => $this->currency->format($cogs, $currency_code),
            'operating_expenses' => $this->currency->format($operating_expenses, $currency_code),
            'gross_profit' => $this->currency->format($gross_profit, $currency_code),
            'operating_profit' => $this->currency->format($operating_profit, $currency_code),
            'other_expenses' => $this->currency->format($other_expenses, $currency_code),
            'net_profit' => $this->currency->format($net_profit, $currency_code),
            'gross_margin' => number_format($gross_margin, 2) . '%',
            'operating_margin' => number_format($operating_margin, 2) . '%',
            'net_margin' => number_format($net_margin, 2) . '%',
        ];
    }

    private function getSumForAccounts($prefix, $date_start, $date_end) {
        $sql = "SELECT COALESCE(SUM(CASE WHEN je.is_debit=1 THEN je.amount ELSE -je.amount END),0) AS sum_amount
                FROM " . DB_PREFIX . "journal_entries je
                LEFT JOIN " . DB_PREFIX . "journals j ON (je.journal_id=j.journal_id)
                LEFT JOIN " . DB_PREFIX . "accounts a ON (je.account_code=a.account_code)
                WHERE a.account_code LIKE '".$this->db->escape($prefix)."%'
                AND j.thedate BETWEEN '".$this->db->escape($date_start)."' AND '".$this->db->escape($date_end)."'
                AND j.is_cancelled=0";
        $q = $this->db->query($sql);
        return (float)$q->row['sum_amount'];
    }

    // تحسين تحليل الربحية مع التخزين المؤقت
    public function getOptimizedProfitabilityAnalysis($filter_data) {
        $cache_key = 'profitability_analysis_' . md5(serialize($filter_data));

        $cached_result = $this->cache->get($cache_key);
        if ($cached_result !== false) {
            return $cached_result;
        }

        $result = $this->getAdvancedProfitabilityAnalysis($filter_data);
        $this->cache->set($cache_key, $result, 1800);

        return $result;
    }

    // تحليل متقدم للربحية حسب المنتج
    public function getProductProfitabilityAnalysis($filter_data) {
        $cache_key = 'product_profitability_' . md5(serialize($filter_data));

        $cached_result = $this->cache->get($cache_key);
        if ($cached_result !== false) {
            return $cached_result;
        }

        $analysis = array();

        // تحليل الربحية حسب المنتج
        $product_query = $this->db->query("
            SELECT
                p.product_id,
                pd.name as product_name,
                COALESCE(SUM(CASE WHEN je.account_code LIKE '4%' THEN je.amount ELSE 0 END), 0) as revenue,
                COALESCE(SUM(CASE WHEN je.account_code LIKE '5%' THEN je.amount ELSE 0 END), 0) as cost
            FROM " . DB_PREFIX . "product p
            LEFT JOIN " . DB_PREFIX . "product_description pd ON p.product_id = pd.product_id
            LEFT JOIN " . DB_PREFIX . "journal_entries je ON je.reference_id = p.product_id
            LEFT JOIN " . DB_PREFIX . "journals j ON je.journal_id = j.journal_id
            WHERE j.status = 'posted'
            AND j.thedate BETWEEN '" . $this->db->escape($filter_data['date_start']) . "'
            AND '" . $this->db->escape($filter_data['date_end']) . "'
            GROUP BY p.product_id, pd.name
            ORDER BY revenue DESC
            LIMIT 50
        ");

        $analysis['products'] = $product_query->rows;

        $this->cache->set($cache_key, $analysis, 2400);

        return $analysis;
    }

    // التحقق من صحة البيانات
    private function validateAnalysisData($filter_data) {
        $errors = array();

        if (empty($filter_data['date_start']) || !$this->validateDate($filter_data['date_start'])) {
            $errors[] = 'Invalid start date';
        }

        if (empty($filter_data['date_end']) || !$this->validateDate($filter_data['date_end'])) {
            $errors[] = 'Invalid end date';
        }

        return $errors;
    }

    private function validateDate($date) {
        if (empty($date)) return false;

        $d = DateTime::createFromFormat('Y-m-d', $date);
        return $d && $d->format('Y-m-d') === $date;
    }
}
