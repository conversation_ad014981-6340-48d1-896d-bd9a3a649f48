<?php
// Heading
$_['heading_title']          = 'Blog Comments';

// Text
$_['text_success_add']       = 'Comment added successfully!';
$_['text_success_edit']      = 'Comment updated successfully!';
$_['text_success_delete']    = 'Comment(s) deleted successfully!';
$_['text_success_approve']   = 'Comment approved successfully!';
$_['text_success_disapprove']= 'Comment disapproved successfully!';
$_['text_list']              = 'Comment List';
$_['text_add']               = 'Add Comment';
$_['text_edit']              = 'Edit Comment';
$_['text_default']           = 'Default';
$_['text_enabled']           = 'Approved';
$_['text_disabled']          = 'Disapproved';
$_['text_all_statuses']      = '-- All Statuses --';
$_['text_all_posts']         = '-- All Posts --';
$_['text_no_results']        = 'No results';
$_['text_form']              = 'Comment Form';
$_['text_filter']            = 'Filter';
$_['text_view_comment']      = 'View Comment';
$_['text_comment_details']   = 'Comment Details';
$_['text_comment_content']   = 'Comment Content';
$_['text_parent_comment']    = 'Parent Comment';
$_['text_replies']           = 'Replies';
$_['text_confirm']           = 'Are you sure?';

// Column
$_['column_post']            = 'Post';
$_['column_author']          = 'Author';
$_['column_comment']         = 'Comment';
$_['column_status']          = 'Status';
$_['column_date_added']      = 'Date Added';
$_['column_replies']         = 'Replies';
$_['column_action']          = 'Action';

// Entry
$_['entry_post']             = 'Post';
$_['entry_author']           = 'Author';
$_['entry_email']            = 'Email';
$_['entry_website']          = 'Website';
$_['entry_content']          = 'Content';
$_['entry_status']           = 'Status';
$_['entry_date_added']       = 'Date Added';
$_['entry_notify']           = 'Notify of Replies';
$_['entry_ip']               = 'IP Address';

// Button
$_['button_save']            = 'Save';
$_['button_cancel']          = 'Cancel';
$_['button_add']             = 'Add Comment';
$_['button_edit']            = 'Edit';
$_['button_delete']          = 'Delete';
$_['button_filter']          = 'Filter';
$_['button_clear']           = 'Clear';
$_['button_view']            = 'View';
$_['button_approve']         = 'Approve';
$_['button_disapprove']      = 'Disapprove';
$_['button_reply']           = 'Reply';
$_['button_back']            = 'Back';
$_['button_view_parent']     = 'View Parent Comment';

// Error
$_['error_permission']       = 'Warning: You do not have permission to modify comments!';
$_['error_author']           = 'Author name must be between 3 and 64 characters!';
$_['error_email']            = 'Invalid email address!';
$_['error_content']          = 'Comment content must be at least 5 characters!';
$_['error_comment']          = 'Comment does not exist!';