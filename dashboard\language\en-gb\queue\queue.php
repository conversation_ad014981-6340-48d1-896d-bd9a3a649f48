<?php
/**
 * Language file for Enhanced Queue Management
 * English translations for AYM ERP Queue system
 */

// Heading
$_['heading_title']          = 'Queue Management';

// Text
$_['text_success']           = 'Success: Queue settings have been updated successfully!';
$_['text_list']              = 'Job List';
$_['text_add']               = 'Add Job';
$_['text_edit']              = 'Edit Job';
$_['text_default']           = 'Default';
$_['text_filter']            = 'Filter';
$_['text_all']               = 'All';
$_['text_pending']           = 'Pending';
$_['text_processing']        = 'Processing';
$_['text_completed']         = 'Completed';
$_['text_failed']            = 'Failed';
$_['text_cancelled']         = 'Cancelled';
$_['text_low']               = 'Low';
$_['text_normal']            = 'Normal';
$_['text_high']              = 'High';
$_['text_critical']          = 'Critical';
$_['text_job_details']       = 'Job Details';
$_['text_no_results']        = 'No results found!';
$_['text_confirm_cleanup']   = 'Are you sure you want to delete old completed jobs?';
$_['text_confirm_reset']     = 'Are you sure you want to reset stuck jobs?';
$_['text_confirm_retry']     = 'Are you sure you want to retry this job?';
$_['text_confirm_cancel']    = 'Are you sure you want to cancel this job?';

// Column
$_['column_id']              = 'Job ID';
$_['column_job_type']        = 'Job Type';
$_['column_priority']        = 'Priority';
$_['column_status']          = 'Status';
$_['column_attempts']        = 'Attempts';
$_['column_created']         = 'Created';
$_['column_updated']         = 'Updated';
$_['column_action']          = 'Action';

// Entry
$_['entry_status']           = 'Status:';
$_['entry_job_type']         = 'Job Type:';
$_['entry_priority']         = 'Priority:';
$_['entry_date_from']        = 'Date From:';
$_['entry_date_to']          = 'Date To:';

// Button
$_['button_process']         = 'Process Jobs';
$_['button_cleanup']         = 'Cleanup Old Jobs';
$_['button_reset_stuck']     = 'Reset Stuck Jobs';
$_['button_filter']          = 'Filter';
$_['button_view']            = 'View';
$_['button_retry']           = 'Retry';
$_['button_cancel']          = 'Cancel';
$_['button_close']           = 'Close';

// Success Messages
$_['success_process']        = 'Successfully processed %s jobs!';
$_['success_cleanup']        = 'Successfully cleaned up %s old jobs!';
$_['success_reset_stuck']    = 'Successfully reset %s stuck jobs!';
$_['success_retry']          = 'Job retried successfully!';
$_['success_cancel']         = 'Job cancelled successfully!';

// Error Messages
$_['error_permission']       = 'Warning: You do not have permission to access queue management!';
$_['error_job_not_found']    = 'Error: Job not found!';
$_['error_job_not_failed']   = 'Error: Cannot retry a job that is not failed!';
$_['error_job_not_pending']  = 'Error: Cannot cancel a job that is not pending or processing!';
$_['error_processing']       = 'Error: An error occurred while processing jobs!';
$_['error_cleanup']          = 'Error: An error occurred while cleaning up old jobs!';
$_['error_reset']            = 'Error: An error occurred while resetting stuck jobs!';

// Job Types
$_['job_type_eta_invoice']     = 'ETA Invoice';
$_['job_type_eta_receipt']     = 'ETA Receipt';
$_['job_type_eta_credit_note'] = 'ETA Credit Note';
$_['job_type_eta_debit_note']  = 'ETA Debit Note';
$_['job_type_inventory_update'] = 'Inventory Update';
$_['job_type_accounting_entry'] = 'Accounting Entry';
$_['job_type_email_notification'] = 'Email Notification';
$_['job_type_report_generation'] = 'Report Generation';

// Statistics
$_['stat_total_jobs']        = 'Total Jobs';
$_['stat_pending_jobs']      = 'Pending Jobs';
$_['stat_processing_jobs']   = 'Processing Jobs';
$_['stat_completed_jobs']    = 'Completed Jobs';
$_['stat_failed_jobs']       = 'Failed Jobs';
$_['stat_avg_processing_time'] = 'Avg Processing Time';
$_['stat_table_size']        = 'Table Size';

// Help
$_['help_priority']          = 'Priority determines the order of job processing. Critical priority (4) has the highest precedence.';
$_['help_cleanup']           = 'Cleanup old jobs removes completed and cancelled jobs older than 7 days.';
$_['help_reset_stuck']       = 'Reset stuck jobs resets jobs that have been stuck in "processing" status for more than 30 minutes.';

// Job Details
$_['detail_job_id']          = 'Job ID:';
$_['detail_job_type']        = 'Job Type:';
$_['detail_priority']        = 'Priority:';
$_['detail_status']          = 'Status:';
$_['detail_attempts']        = 'Attempts:';
$_['detail_max_attempts']    = 'Max Attempts:';
$_['detail_created_at']      = 'Created At:';
$_['detail_updated_at']      = 'Updated At:';
$_['detail_started_at']      = 'Started At:';
$_['detail_completed_at']    = 'Completed At:';
$_['detail_scheduled_at']    = 'Scheduled At:';
$_['detail_processing_time'] = 'Processing Time:';
$_['detail_error_message']   = 'Error Message:';
$_['detail_job_data']        = 'Job Data:';

// Time units
$_['time_seconds']           = 'seconds';
$_['time_minutes']           = 'minutes';
$_['time_hours']             = 'hours';
$_['time_days']              = 'days';

// Queue Status Messages
$_['queue_empty']            = 'Queue is empty';
$_['queue_processing']       = 'Processing jobs...';
$_['queue_paused']           = 'Queue is paused';
$_['queue_error']            = 'Queue error';

// Performance Messages
$_['performance_good']       = 'Good performance';
$_['performance_warning']    = 'Average performance';
$_['performance_critical']   = 'Poor performance';

// Monitoring
$_['monitor_memory_usage']   = 'Memory Usage:';
$_['monitor_cpu_usage']      = 'CPU Usage:';
$_['monitor_disk_usage']     = 'Disk Usage:';
$_['monitor_queue_health']   = 'Queue Health:';

// Notifications
$_['notification_high_load'] = 'Warning: High load on queue system';
$_['notification_stuck_jobs'] = 'Warning: There are stuck jobs that need to be reset';
$_['notification_failed_jobs'] = 'Warning: There are failed jobs that need review';

// Dashboard Widget
$_['widget_title']           = 'Job Queue';
$_['widget_pending']         = 'Pending';
$_['widget_processing']      = 'Processing';
$_['widget_failed']          = 'Failed';
$_['widget_view_all']        = 'View All';

// Export
$_['export_csv']             = 'Export CSV';
$_['export_excel']           = 'Export Excel';
$_['export_pdf']             = 'Export PDF';

// Import
$_['import_jobs']            = 'Import Jobs';
$_['import_success']         = 'Successfully imported %s jobs!';
$_['import_error']           = 'Error importing jobs!';

// Batch Operations
$_['batch_retry_selected']   = 'Retry Selected';
$_['batch_cancel_selected']  = 'Cancel Selected';
$_['batch_delete_selected']  = 'Delete Selected';
$_['batch_success']          = 'Successfully performed operation on %s jobs!';
$_['batch_error']            = 'Error performing batch operation!';

// Advanced Settings
$_['setting_max_attempts']   = 'Max Attempts:';
$_['setting_timeout']        = 'Timeout (minutes):';
$_['setting_batch_size']     = 'Batch Size:';
$_['setting_cleanup_days']   = 'Cleanup Days:';
$_['setting_auto_process']   = 'Auto Process:';
$_['setting_notifications']  = 'Notifications:';

// API Messages
$_['api_job_created']        = 'Job created successfully';
$_['api_job_updated']        = 'Job updated successfully';
$_['api_job_deleted']        = 'Job deleted successfully';
$_['api_invalid_request']    = 'Invalid request';
$_['api_unauthorized']       = 'Unauthorized';
$_['api_not_found']          = 'Not found';
$_['api_server_error']       = 'Server error';

// Cron Job Messages
$_['cron_started']           = 'Queue processing cron job started';
$_['cron_completed']         = 'Cron job completed successfully';
$_['cron_error']             = 'Error executing cron job';
$_['cron_no_jobs']           = 'No jobs to process';

// Log Messages
$_['log_job_started']        = 'Started processing job #%s';
$_['log_job_completed']      = 'Job #%s completed successfully';
$_['log_job_failed']         = 'Job #%s failed: %s';
$_['log_job_retried']        = 'Job #%s retried';
$_['log_job_cancelled']      = 'Job #%s cancelled';
$_['log_queue_processed']    = 'Processed %s jobs from queue';
$_['log_queue_cleaned']      = 'Cleaned up %s old jobs from queue';
$_['log_stuck_jobs_reset']   = 'Reset %s stuck jobs';

// Email Templates
$_['email_job_failed_subject'] = 'Job Failed - %s';
$_['email_job_failed_body']    = 'Job #%s of type %s failed after %s attempts.\n\nError message: %s\n\nPlease review the system.';
$_['email_queue_stuck_subject'] = 'Warning: Stuck Jobs in Queue';
$_['email_queue_stuck_body']    = 'There are %s jobs stuck in the queue for more than %s minutes.\n\nPlease review the system and reset stuck jobs.';

// Validation Messages
$_['validation_job_type_required'] = 'Job type is required';
$_['validation_job_data_required']  = 'Job data is required';
$_['validation_priority_invalid']   = 'Invalid priority';
$_['validation_status_invalid']     = 'Invalid status';
$_['validation_date_invalid']       = 'Invalid date';

// Tooltips
$_['tooltip_process']        = 'Process all pending jobs';
$_['tooltip_cleanup']        = 'Delete completed and cancelled jobs older than 7 days';
$_['tooltip_reset_stuck']    = 'Reset jobs stuck in "processing" status for more than 30 minutes';
$_['tooltip_view']           = 'View job details';
$_['tooltip_retry']          = 'Retry failed job';
$_['tooltip_cancel']         = 'Cancel pending or processing job';

// Breadcrumbs
$_['breadcrumb_home']        = 'Home';
$_['breadcrumb_queue']       = 'Job Queue';
$_['breadcrumb_statistics']  = 'Statistics';
$_['breadcrumb_settings']    = 'Settings';

// Pagination
$_['pagination_showing']     = 'Showing %s to %s of %s (%s Pages)';
$_['pagination_first']       = 'First';
$_['pagination_last']        = 'Last';
$_['pagination_next']        = 'Next';
$_['pagination_prev']        = 'Previous';

// Search
$_['search_placeholder']     = 'Search jobs...';
$_['search_no_results']      = 'No search results found';
$_['search_results']         = 'Search results: %s jobs';

// Sorting
$_['sort_id_asc']           = 'Job ID (Ascending)';
$_['sort_id_desc']          = 'Job ID (Descending)';
$_['sort_created_asc']      = 'Created Date (Ascending)';
$_['sort_created_desc']     = 'Created Date (Descending)';
$_['sort_priority_asc']     = 'Priority (Ascending)';
$_['sort_priority_desc']    = 'Priority (Descending)';
$_['sort_status_asc']       = 'Status (Ascending)';
$_['sort_status_desc']      = 'Status (Descending)';

// Auto-refresh
$_['auto_refresh_enabled']  = 'Auto-refresh enabled';
$_['auto_refresh_disabled'] = 'Auto-refresh disabled';
$_['auto_refresh_interval'] = 'Refresh interval: %s seconds';

// Mobile
$_['mobile_view']           = 'Mobile View';
$_['mobile_actions']        = 'Actions';
$_['mobile_details']        = 'Details';

// Accessibility
$_['accessibility_skip']    = 'Skip to main content';
$_['accessibility_menu']    = 'Navigation menu';
$_['accessibility_search']  = 'Search';
$_['accessibility_filter']  = 'Filter';

// Print
$_['print_title']           = 'Queue Report';
$_['print_date']            = 'Print Date: %s';
$_['print_total']           = 'Total Jobs: %s';

// Keyboard Shortcuts
$_['shortcut_process']      = 'Ctrl+P: Process Jobs';
$_['shortcut_cleanup']      = 'Ctrl+C: Cleanup Jobs';
$_['shortcut_refresh']      = 'F5: Refresh Page';
$_['shortcut_filter']       = 'Ctrl+F: Open Filter';
$_['shortcut_help']         = 'F1: Help';

// Theme
$_['theme_light']           = 'Light Theme';
$_['theme_dark']            = 'Dark Theme';
$_['theme_auto']            = 'Auto';

// Language
$_['language_arabic']       = 'العربية';
$_['language_english']      = 'English';
$_['language_auto']         = 'Auto';

// Footer
$_['footer_copyright']      = '© %s AYM ERP. All rights reserved.';
$_['footer_version']        = 'Version %s';
$_['footer_support']        = 'Support';
$_['footer_documentation']  = 'Documentation';

// Debug
$_['debug_mode']            = 'Debug Mode';
$_['debug_query_count']     = 'Query Count: %s';
$_['debug_memory_usage']    = 'Memory Usage: %s MB';
$_['debug_execution_time']  = 'Execution Time: %s seconds';

// Cache
$_['cache_enabled']         = 'Cache Enabled';
$_['cache_disabled']        = 'Cache Disabled';
$_['cache_clear']           = 'Clear Cache';
$_['cache_cleared']         = 'Cache cleared successfully';

// Security
$_['security_token_invalid'] = 'Invalid security token';
$_['security_session_expired'] = 'Session expired';
$_['security_access_denied'] = 'Access denied';
$_['security_ip_blocked']   = 'IP address blocked';

// Maintenance
$_['maintenance_mode']      = 'Maintenance Mode';
$_['maintenance_message']   = 'System is under maintenance. Please try again later.';
$_['maintenance_eta']       = 'Estimated completion time: %s';

// Backup
$_['backup_queue']          = 'Backup Queue';
$_['backup_created']        = 'Backup created successfully';
$_['backup_restored']       = 'Backup restored successfully';
$_['backup_failed']         = 'Backup failed';

// Integration
$_['integration_webhook']   = 'Webhook';
$_['integration_api']       = 'API';
$_['integration_email']     = 'Email';
$_['integration_sms']       = 'SMS';
$_['integration_slack']     = 'Slack';
$_['integration_discord']   = 'Discord';

// Analytics
$_['analytics_performance'] = 'Performance Analytics';
$_['analytics_trends']      = 'Trends';
$_['analytics_reports']     = 'Reports';
$_['analytics_insights']    = 'Insights';

// Alerts
$_['alert_info']            = 'Info';
$_['alert_success']         = 'Success';
$_['alert_warning']         = 'Warning';
$_['alert_danger']          = 'Danger';
$_['alert_dismiss']         = 'Dismiss Alert';

// Loading
$_['loading']               = 'Loading...';
$_['loading_jobs']          = 'Loading jobs...';
$_['loading_stats']         = 'Loading statistics...';
$_['loading_details']       = 'Loading details...';

// Empty States
$_['empty_queue']           = 'Queue is empty';
$_['empty_failed']          = 'No failed jobs';
$_['empty_completed']       = 'No completed jobs';
$_['empty_search']          = 'No search results';

// Actions
$_['action_view']           = 'View';
$_['action_edit']           = 'Edit';
$_['action_delete']         = 'Delete';
$_['action_retry']          = 'Retry';
$_['action_cancel']         = 'Cancel';
$_['action_duplicate']      = 'Duplicate';
$_['action_export']         = 'Export';
$_['action_import']         = 'Import';
$_['action_refresh']        = 'Refresh';
$_['action_clear']          = 'Clear';
$_['action_reset']          = 'Reset';
$_['action_save']           = 'Save';
$_['action_cancel_action']  = 'Cancel';
$_['action_submit']         = 'Submit';
$_['action_close']          = 'Close';
$_['action_back']           = 'Back';
$_['action_next']           = 'Next';
$_['action_previous']       = 'Previous';
$_['action_first']          = 'First';
$_['action_last']           = 'Last';

// Units
$_['unit_bytes']            = 'bytes';
$_['unit_kb']               = 'KB';
$_['unit_mb']               = 'MB';
$_['unit_gb']               = 'GB';
$_['unit_seconds']          = 'seconds';
$_['unit_minutes']          = 'minutes';
$_['unit_hours']            = 'hours';
$_['unit_days']             = 'days';
$_['unit_weeks']            = 'weeks';
$_['unit_months']           = 'months';
$_['unit_years']            = 'years';

// Status Indicators
$_['status_online']         = 'Online';
$_['status_offline']        = 'Offline';
$_['status_active']         = 'Active';
$_['status_inactive']       = 'Inactive';
$_['status_enabled']        = 'Enabled';
$_['status_disabled']       = 'Disabled';
$_['status_running']        = 'Running';
$_['status_stopped']        = 'Stopped';
$_['status_paused']         = 'Paused';
$_['status_error']          = 'Error';
$_['status_warning']        = 'Warning';
$_['status_success']        = 'Success';
$_['status_info']           = 'Info';

// Permissions
$_['permission_view']       = 'View';
$_['permission_add']        = 'Add';
$_['permission_edit']       = 'Edit';
$_['permission_delete']     = 'Delete';
$_['permission_process']    = 'Process';
$_['permission_manage']     = 'Manage';
$_['permission_admin']      = 'Admin';
$_['permission_denied']     = 'Permission denied';

// Logs
$_['log_level_debug']       = 'Debug';
$_['log_level_info']        = 'Info';
$_['log_level_warning']     = 'Warning';
$_['log_level_error']       = 'Error';
$_['log_level_critical']    = 'Critical';

// Configuration
$_['config_general']        = 'General';
$_['config_performance']    = 'Performance';
$_['config_security']       = 'Security';
$_['config_notifications']  = 'Notifications';
$_['config_advanced']       = 'Advanced';
$_['config_saved']          = 'Configuration saved successfully';
$_['config_error']          = 'Error saving configuration';

// System Info
$_['system_info']           = 'System Information';
$_['system_version']        = 'System Version';
$_['system_php_version']    = 'PHP Version';
$_['system_mysql_version']  = 'MySQL Version';
$_['system_server']         = 'Server';
$_['system_os']             = 'Operating System';
$_['system_memory']         = 'Memory';
$_['system_disk']           = 'Disk';
$_['system_uptime']         = 'Uptime';
$_['system_load']           = 'System Load';

// Health Check
$_['health_check']          = 'Health Check';
$_['health_good']           = 'Good';
$_['health_warning']        = 'Warning';
$_['health_critical']       = 'Critical';
$_['health_unknown']        = 'Unknown';
$_['health_database']       = 'Database';
$_['health_queue']          = 'Queue';
$_['health_storage']        = 'Storage';
$_['health_network']        = 'Network';

// Recommendations
$_['recommendation_title']  = 'Recommendations';
$_['recommendation_performance'] = 'Performance Optimization';
$_['recommendation_security'] = 'Security Enhancement';
$_['recommendation_maintenance'] = 'Maintenance';
$_['recommendation_upgrade'] = 'Upgrade';

// Documentation
$_['doc_title']             = 'Documentation';
$_['doc_getting_started']   = 'Getting Started';
$_['doc_user_guide']        = 'User Guide';
$_['doc_admin_guide']       = 'Admin Guide';
$_['doc_api_reference']     = 'API Reference';
$_['doc_troubleshooting']   = 'Troubleshooting';
$_['doc_faq']               = 'FAQ';
$_['doc_changelog']         = 'Changelog';
$_['doc_license']           = 'License';

// Support
$_['support_title']         = 'Support';
$_['support_contact']       = 'Contact Us';
$_['support_ticket']        = 'Support Ticket';
$_['support_forum']         = 'Forum';
$_['support_chat']          = 'Chat';
$_['support_email']         = 'Email';
$_['support_phone']         = 'Phone';

// About
$_['about_title']           = 'About';
$_['about_description']     = 'Advanced Queue Management System for AYM ERP';
$_['about_version']         = 'Version';
$_['about_author']          = 'Author';
$_['about_license']         = 'License';
$_['about_website']         = 'Website';
$_['about_support']         = 'Support';

// Footer Links
$_['footer_privacy']        = 'Privacy Policy';
$_['footer_terms']          = 'Terms of Service';
$_['footer_contact']        = 'Contact';
$_['footer_about']          = 'About';
$_['footer_help']           = 'Help';

// Meta
$_['meta_title']            = 'Queue Management - AYM ERP';
$_['meta_description']      = 'Advanced queue management system with performance monitoring and detailed statistics';
$_['meta_keywords']         = 'job queue, task management, AYM ERP, job processing, performance monitoring';

// Social
$_['social_share']          = 'Share';
$_['social_facebook']       = 'Facebook';
$_['social_twitter']        = 'Twitter';
$_['social_linkedin']       = 'LinkedIn';
$_['social_whatsapp']       = 'WhatsApp';
$_['social_telegram']       = 'Telegram';

// Cookies
$_['cookie_consent']        = 'We use cookies to improve your experience';
$_['cookie_accept']         = 'Accept';
$_['cookie_decline']        = 'Decline';
$_['cookie_settings']       = 'Cookie Settings';

// GDPR
$_['gdpr_title']            = 'Data Protection';
$_['gdpr_description']      = 'We are committed to protecting your personal data';
$_['gdpr_accept']           = 'I agree to data processing';
$_['gdpr_decline']          = 'I do not agree';
$_['gdpr_policy']           = 'Privacy Policy';

// Accessibility
$_['accessibility_title']   = 'Accessibility';
$_['accessibility_skip_nav'] = 'Skip Navigation';
$_['accessibility_skip_content'] = 'Skip to Content';
$_['accessibility_high_contrast'] = 'High Contrast';
$_['accessibility_large_text'] = 'Large Text';
$_['accessibility_screen_reader'] = 'Screen Reader';

// PWA
$_['pwa_install']           = 'Install App';
$_['pwa_offline']           = 'Offline';
$_['pwa_update']            = 'Update Available';
$_['pwa_reload']            = 'Reload';

// Dark Mode
$_['dark_mode']             = 'Dark Mode';
$_['light_mode']            = 'Light Mode';
$_['auto_mode']             = 'Auto';

// Responsive
$_['responsive_mobile']     = 'Mobile';
$_['responsive_tablet']     = 'Tablet';
$_['responsive_desktop']    = 'Desktop';

// Print
$_['print_page']            = 'Print Page';
$_['print_selection']       = 'Print Selection';
$_['print_preview']         = 'Print Preview';

// Keyboard Navigation
$_['keyboard_navigation']   = 'Keyboard Navigation';
$_['keyboard_shortcuts']    = 'Keyboard Shortcuts';
$_['keyboard_help']         = 'Keyboard Help';

// Screen Reader
$_['screen_reader_only']    = 'Screen Reader Only';
$_['screen_reader_skip']    = 'Skip to Main Content';
$_['screen_reader_menu']    = 'Main Navigation Menu';

// Focus Management
$_['focus_skip']            = 'Skip to Content';
$_['focus_menu']            = 'Focus Menu';
$_['focus_search']          = 'Focus Search';
$_['focus_content']         = 'Focus Content';

// Error Handling
$_['error_404']             = 'Page Not Found';
$_['error_500']             = 'Server Error';
$_['error_403']             = 'Forbidden';
$_['error_401']             = 'Unauthorized';
$_['error_timeout']         = 'Request Timeout';
$_['error_network']         = 'Network Error';
$_['error_unknown']         = 'Unknown Error';
$_['error_retry']           = 'Retry';
$_['error_contact_support'] = 'Contact Support';

// Success Messages
$_['success_general']       = 'Operation completed successfully';
$_['success_saved']         = 'Saved successfully';
$_['success_updated']       = 'Updated successfully';
$_['success_deleted']       = 'Deleted successfully';
$_['success_created']       = 'Created successfully';
$_['success_uploaded']      = 'Uploaded successfully';
$_['success_downloaded']    = 'Downloaded successfully';
$_['success_sent']          = 'Sent successfully';
$_['success_received']      = 'Received successfully';
$_['success_processed']     = 'Processed successfully';
$_['success_completed']     = 'Completed successfully';

// Warning Messages
$_['warning_general']       = 'General warning';
$_['warning_unsaved']       = 'You have unsaved changes';
$_['warning_delete']        = 'Are you sure you want to delete?';
$_['warning_overwrite']     = 'Do you want to overwrite the existing file?';
$_['warning_irreversible']  = 'This action cannot be undone';
$_['warning_data_loss']     = 'You may lose some data';
$_['warning_connection']    = 'Connection issue';
$_['warning_performance']   = 'May affect performance';
$_['warning_security']      = 'Security warning';
$_['warning_compatibility'] = 'Compatibility issue';

// Info Messages
$_['info_general']          = 'General information';
$_['info_loading']          = 'Loading...';
$_['info_processing']       = 'Processing...';
$_['info_saving']           = 'Saving...';
$_['info_uploading']        = 'Uploading...';
$_['info_downloading']      = 'Downloading...';
$_['info_connecting']       = 'Connecting...';
$_['info_searching']        = 'Searching...';
$_['info_filtering']        = 'Filtering...';
$_['info_sorting']          = 'Sorting...';

// Confirmation Messages
$_['confirm_general']       = 'Are you sure?';
$_['confirm_delete']        = 'Are you sure you want to delete?';
$_['confirm_save']          = 'Do you want to save changes?';
$_['confirm_cancel']        = 'Do you want to cancel the operation?';
$_['confirm_exit']          = 'Do you want to exit?';
$_['confirm_reset']         = 'Do you want to reset?';
$_['confirm_clear']         = 'Do you want to clear data?';
$_['confirm_overwrite']     = 'Do you want to overwrite the file?';
$_['confirm_proceed']       = 'Do you want to proceed?';
$_['confirm_submit']        = 'Do you want to submit data?';

// Navigation
$_['nav_home']              = 'Home';
$_['nav_dashboard']         = 'Dashboard';
$_['nav_queue']             = 'Job Queue';
$_['nav_statistics']        = 'Statistics';
$_['nav_settings']          = 'Settings';
$_['nav_help']              = 'Help';
$_['nav_logout']            = 'Logout';
$_['nav_profile']           = 'Profile';
$_['nav_admin']             = 'Admin';
$_['nav_reports']           = 'Reports';
$_['nav_tools']             = 'Tools';

// End of file
?>