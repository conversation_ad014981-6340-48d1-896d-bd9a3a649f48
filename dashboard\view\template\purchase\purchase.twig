{# filename: purchase.twig #}

{{ header }}
{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        {% if user_purchase_requisition_add %}
        <button type="button" class="btn btn-primary" id="btn-open-add-requisition" style="margin-left:5px;">
          <i class="fa fa-plus"></i> {{ text_add_requisition }}
        </button>
        {% endif %}

        {% if user_purchase_order_add %}
        <button type="button" class="btn btn-success" id="btn-add-po">
          <i class="fa fa-shopping-cart"></i> {{ text_add_po }}
        </button>
        {% endif %}

        {% if user_goods_receipt_add %}
        <button type="button" class="btn btn-warning" id="btn-add-gr">
          <i class="fa fa-truck"></i> {{ text_add_goods_receipt }}
        </button>
        {% endif %}

        {% if user_supplier_invoice_add %}
        <button type="button" class="btn btn-danger" id="btn-add-invoice">
          <i class="fa fa-file-text"></i> {{ text_add_invoice }}
        </button>
        {% endif %}

        {% if user_vendor_payment_add %}
        <button type="button" class="btn btn-default" id="btn-add-payment">
          <i class="fa fa-money"></i> {{ text_add_payment }}
        </button>
        {% endif %}

        {% if user_purchase_return_add %}
        <button type="button" class="btn btn-default" id="btn-add-purchase-return">
          <i class="fa fa-reply"></i> {{ text_add_purchase_return }}
        </button>
        {% endif %}

        {% if user_stock_adjustment_add %}
        <button type="button" class="btn btn-default" id="btn-add-stock-adjustment">
          <i class="fa fa-sliders"></i> {{ text_add_stock_adjustment }}
        </button>
        {% endif %}

        {% if user_stock_transfer_add %}
        <button type="button" class="btn btn-default" id="btn-add-stock-transfer">
          <i class="fa fa-exchange"></i> {{ text_add_stock_transfer }}
        </button>
        {% endif %}

        {% if user_quality_inspection_add %}
        <button type="button" class="btn btn-default" id="btn-add-quality-inspection">
          <i class="fa fa-check-square-o"></i> {{ text_add_quality_inspection }}
        </button>
        {% endif %}

        {% if user_accounting_integration %}
        <button type="button" class="btn btn-info" id="btn-open-accounting-ledger">
          <i class="fa fa-book"></i> {{ text_open_ledger }}
        </button>
        {% endif %}

        <button type="button" class="btn btn-default" id="btn-print-report-dashboard">
          <i class="fa fa-print"></i> {{ text_print_report }}
        </button>
      </div>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
          <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>

  <div class="container-fluid">
    {% if error_warning %}
      <div class="alert alert-danger alert-dismissible">
        <i class="fa fa-exclamation-circle"></i> {{ error_warning }}
        <button type="button" class="close" data-dismiss="alert">&times;</button>
      </div>
    {% endif %}
    {% if success %}
      <div class="alert alert-success alert-dismissible">
        <i class="fa fa-check-circle"></i> {{ success }}
        <button type="button" class="close" data-dismiss="alert">&times;</button>
      </div>
    {% endif %}

    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-list"></i> {{ text_purchase_dashboard }}</h3>
      </div>
      <div class="panel-body">

        <ul class="nav nav-tabs">
          <li class="active"><a href="#tab-dashboard" data-toggle="tab">{{ tab_dashboard }}</a></li>
          {% if user_purchase_requisition_view %}
          <li><a href="#tab-purchase-requisition" data-toggle="tab">{{ tab_purchase_requisition }}</a></li>
          {% endif %}
          {% if user_purchase_quotation_view %}
          <li><a href="#tab-quotation" data-toggle="tab">{{ tab_quotation }}</a></li>
          {% endif %}
          {% if user_purchase_order_view %}
          <li><a href="#tab-purchase-order" data-toggle="tab">{{ tab_purchase_order }}</a></li>
          {% endif %}
          {% if user_goods_receipt_view %}
          <li><a href="#tab-goods-receipt" data-toggle="tab">{{ tab_goods_receipt }}</a></li>
          {% endif %}
          {% if user_supplier_invoice_view %}
          <li><a href="#tab-supplier-invoice" data-toggle="tab">{{ tab_supplier_invoice }}</a></li>
          {% endif %}
          {% if user_vendor_payment_view %}
          <li><a href="#tab-vendor-payment" data-toggle="tab">{{ tab_vendor_payment }}</a></li>
          {% endif %}
          {% if user_inventory_view %}
          <li><a href="#tab-inventory" data-toggle="tab">{{ tab_inventory }}</a></li>
          {% endif %}
          {% if user_purchase_return_view %}
          <li><a href="#tab-purchase-return" data-toggle="tab">{{ tab_purchase_return }}</a></li>
          {% endif %}
          {% if user_stock_adjustment_view %}
          <li><a href="#tab-stock-adjustment" data-toggle="tab">{{ tab_stock_adjustment }}</a></li>
          {% endif %}
          {% if user_stock_transfer_view %}
          <li><a href="#tab-stock-transfer" data-toggle="tab">{{ tab_stock_transfer }}</a></li>
          {% endif %}
          {% if user_quality_inspection_view %}
          <li><a href="#tab-quality-inspection" data-toggle="tab">{{ tab_quality_inspection }}</a></li>
          {% endif %}
        </ul>

        <div class="tab-content">
          <!-- TAB 1: Dashboard -->
          <div class="tab-pane active" id="tab-dashboard">
            <div class="row filter-container">
              <div class="col-md-3">
                <label>{{ text_filter_branch }}</label>
                <select id="filter-dashboard-branch" class="form-control select2">
                  <option value="">{{ text_all_branches }}</option>
                  {% for branch in branches %}
                    <option value="{{ branch.branch_id }}">{{ branch.name }}</option>
                  {% endfor %}
                </select>
              </div>
              <div class="col-md-3">
                <label>{{ text_filter_period }}</label>
                <select id="filter-dashboard-period" class="form-control select2">
                  <option value="today">{{ text_today }}</option>
                  <option value="week">{{ text_this_week }}</option>
                  <option value="month" selected>{{ text_this_month }}</option>
                  <option value="quarter">{{ text_this_quarter }}</option>
                  <option value="year">{{ text_this_year }}</option>
                  <option value="all">{{ text_all_years }}</option>
                </select>
              </div>
              <div class="col-md-3">
                <label>&nbsp;</label><br>
                <button id="btn-filter-dashboard" class="btn btn-primary">
                  <i class="fa fa-filter"></i> {{ button_filter }}
                </button>
              </div>
            </div>
            <hr>

            <div class="row" id="dashboard-stats">
              <div class="col-md-3 col-sm-6">
                <div class="panel panel-primary">
                  <div class="panel-heading">
                    <h3 class="panel-title">{{ text_total_requisitions }}</h3>
                  </div>
                  <div class="panel-body text-center">
                    <h3 id="dashboard-total-requisitions">0</h3>
                  </div>
                </div>
              </div>
              <div class="col-md-3 col-sm-6">
                <div class="panel panel-info">
                  <div class="panel-heading">
                    <h3 class="panel-title">{{ text_total_quotations }}</h3>
                  </div>
                  <div class="panel-body text-center">
                    <h3 id="dashboard-total-quotations">0</h3>
                  </div>
                </div>
              </div>
              <div class="col-md-3 col-sm-6">
                <div class="panel panel-success">
                  <div class="panel-heading">
                    <h3 class="panel-title">{{ text_total_pos }}</h3>
                  </div>
                  <div class="panel-body text-center">
                    <h3 id="dashboard-total-pos">0</h3>
                  </div>
                </div>
              </div>
              <div class="col-md-3 col-sm-6">
                <div class="panel panel-warning">
                  <div class="panel-heading">
                    <h3 class="panel-title">{{ text_pending_approvals }}</h3>
                  </div>
                  <div class="panel-body text-center">
                    <h3 id="dashboard-pending-approvals">0</h3>
                  </div>
                </div>
              </div>
            </div>

            <div class="row">
              <div class="col-md-6">
                <div class="panel panel-default">
                  <div class="panel-heading">
                    <h3 class="panel-title">{{ text_purchase_overview }}</h3>
                  </div>
                  <div class="panel-body">
                    <canvas id="purchaseOverviewChart" height="200"></canvas>
                  </div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="panel panel-default">
                  <div class="panel-heading">
                    <h3 class="panel-title">{{ text_top_suppliers }}</h3>
                  </div>
                  <div class="panel-body">
                    <canvas id="topSuppliersChart" height="200"></canvas>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {% if user_purchase_requisition_view %}
          <!-- TAB 2: Purchase Requisition -->
          <div class="tab-pane" id="tab-purchase-requisition">
            <div class="filter-container">
              <div class="row">
                <div class="col-sm-3">
                  <label>{{ entry_requisition_id }}</label>
                  <input type="text" id="filter-requisition-id" class="form-control" placeholder="{{ entry_requisition_id }}">
                </div>
                <div class="col-sm-3">
                  <label>{{ entry_branch }}</label>
                  <select id="filter-requisition-branch" class="form-control select2">
                    <option value="">{{ text_select }}</option>
                    {% for branch in branches %}
                      <option value="{{ branch.branch_id }}">{{ branch.name }}</option>
                    {% endfor %}
                  </select>
                </div>
                <div class="col-sm-3">
                  <label>{{ entry_department }}</label>
                  <select id="filter-requisition-department" class="form-control select2">
                    <option value="">{{ text_select }}</option>
                    {% for dept in user_groups %}
                      <option value="{{ dept.user_group_id }}">{{ dept.name }}</option>
                    {% endfor %}
                  </select>
                </div>
                <div class="col-sm-3">
                  <label>{{ entry_status }}</label>
                  <select id="filter-requisition-status" class="form-control select2">
                    <option value="">{{ text_all }}</option>
                    {% for st in requisition_statuses %}
                      <option value="{{ st.value }}">{{ st.text }}</option>
                    {% endfor %}
                  </select>
                </div>
              </div>
              <br>
              <div class="row">
                <div class="col-sm-3">
                  <label>{{ entry_date_start }}</label>
                  <input type="text" id="filter-requisition-date-start" class="form-control date" placeholder="{{ entry_date_start }}">
                </div>
                <div class="col-sm-3">
                  <label>{{ entry_date_end }}</label>
                  <input type="text" id="filter-requisition-date-end" class="form-control date" placeholder="{{ entry_date_end }}">
                </div>
                <div class="col-sm-3">
                  <label>{{ entry_user }}</label>
                  <select id="filter-requisition-user" class="form-control select2">
                    <option value="">{{ text_select }}</option>
                    {% for usr in users %}
                      <option value="{{ usr.user_id }}">{{ usr.firstname }} {{ usr.lastname }}</option>
                    {% endfor %}
                  </select>
                </div>
                <div class="col-sm-3">
                  <br>
                  <button type="button" id="button-filter-requisition" class="btn btn-primary btn-block">
                    <i class="fa fa-filter"></i> {{ button_filter }}
                  </button>
                </div>
              </div>
            </div>

            <div class="table-responsive" style="margin-top:20px;">
              <table id="table-requisition" class="table table-bordered table-hover">
                <thead>
                  <tr>
                    <th style="width:1px;" class="text-center"><input type="checkbox" id="check-all-requisition"></th>
                    <th class="text-left">{{ column_requisition_id }}</th>
                    <th class="text-left">{{ column_department }}</th>
                    <th class="text-left">{{ column_status }}</th>
                    <th class="text-left">{{ column_date_added }}</th>
                    <th class="text-right">{{ column_action }}</th>
                  </tr>
                </thead>
                <tbody>
                  <tr><td class="text-center" colspan="6">{{ text_no_results }}</td></tr>
                </tbody>
              </table>
            </div>

            <div class="pagination-container">
              <div class="row">
                <div class="col-sm-6 text-left" id="requisition-pagination"></div>
                <div class="col-sm-6 text-right" id="requisition-results"></div>
              </div>
            </div>
          </div>
          {% endif %}

          <!-- مودال المقارنة -->
          <div id="modal-requisition-quotations" class="modal fade" tabindex="-1" role="dialog">
            <div class="modal-dialog modal-lg">
              <div class="modal-content">
                <div class="modal-header">
                  <button class="close" data-dismiss="modal">&times;</button>
                  <h4>{{ text_compare_quotations }}</h4>
                </div>
                <div class="modal-body" id="modal-requisition-quotations-body"></div>
                <div class="modal-footer">
                  <button class="btn btn-default" data-dismiss="modal">{{ button_close }}</button>
                </div>
              </div>
            </div>
          </div>

          <!-- مودال إضافة طلب شراء -->
          <div id="modal-add-requisition" class="modal fade" tabindex="-1" role="dialog">
            <div class="modal-dialog modal-xl">
              <div class="modal-content">
                <form id="form-add-requisition">
                  <div class="modal-header" style="background:#f7f9fb;border-bottom:1px solid #ddd;">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">{{ text_add_requisition }}</h4>
                  </div>
                  <div class="modal-body">
                    <div class="row">
                      <div class="col-sm-3">
                        <label>{{ entry_branch }}</label>
                        <select name="branch_id" class="form-control select2">
                          {% for br in branches %}
                            <option value="{{ br.branch_id }}">{{ br.name }}</option>
                          {% endfor %}
                        </select>
                      </div>
                      <div class="col-sm-3">
                        <label>{{ entry_department }}</label>
                        <select name="department_id" class="form-control select2">
                          {% for dept in user_groups %}
                            <option value="{{ dept.user_group_id }}">{{ dept.name }}</option>
                          {% endfor %}
                        </select>
                      </div>
                      <div class="col-sm-3">
                        <label>{{ entry_required_date }}</label>
                        <input type="date" name="required_date" class="form-control">
                      </div>
                      <div class="col-sm-3">
                        <label>{{ entry_priority }}</label>
                        <select name="priority" class="form-control select2">
                          <option value="low">{{ text_priority_low }}</option>
                          <option value="medium" selected>{{ text_priority_medium }}</option>
                          <option value="high">{{ text_priority_high }}</option>
                          <option value="urgent">{{ text_priority_urgent }}</option>
                        </select>
                      </div>
                    </div>
                    <br>
                    <div class="form-group">
                      <label>{{ entry_notes }}</label>
                      <textarea name="notes" rows="2" class="form-control"></textarea>
                    </div>
                    <hr>
                    <h4>{{ text_items }}</h4>
                    <table class="table table-bordered" id="table-add-requisition-items">
                      <thead>
                        <tr>
                          <th>{{ column_product }}</th>
                          <th>{{ column_quantity }}</th>
                          <th>{{ column_unit }}</th>
                          <th>{{ column_description }}</th>
                          <th style="width:50px;">{{ column_action }}</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr><td colspan="5">{{ text_no_results }}</td></tr>
                      </tbody>
                      <tfoot>
                        <tr>
                          <td colspan="4"></td>
                          <td><button type="button" class="btn btn-primary" id="btn-add-row-add-requisition"><i class="fa fa-plus"></i></button></td>
                        </tr>
                      </tfoot>
                    </table>
                  </div>
                  <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_close }}</button>
                    <button type="button" class="btn btn-primary" id="btn-save-add-requisition">{{ button_save }}</button>
                  </div>
                </form>
              </div>
            </div>
          </div>

          <!-- مودال تعديل طلب شراء -->
          <div id="modal-edit-requisition" class="modal fade" tabindex="-1" role="dialog">
            <div class="modal-dialog modal-xl">
              <div class="modal-content">
                <form id="form-edit-requisition">
                  <div class="modal-header" style="background:#f7f9fb;border-bottom:1px solid #ddd;">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">{{ text_edit_requisition }}</h4>
                  </div>
                  <div class="modal-body">
                    <input type="hidden" name="requisition_id" value="">
                    <div class="row">
                      <div class="col-sm-3">
                        <label>{{ entry_branch }}</label>
                        <select name="branch_id" class="form-control select2">
                          {% for br in branches %}
                            <option value="{{ br.branch_id }}">{{ br.name }}</option>
                          {% endfor %}
                        </select>
                      </div>
                      <div class="col-sm-3">
                        <label>{{ entry_department }}</label>
                        <select name="department_id" class="form-control select2">
                          {% for dept in user_groups %}
                            <option value="{{ dept.user_group_id }}">{{ dept.name }}</option>
                          {% endfor %}
                        </select>
                      </div>
                      <div class="col-sm-3">
                        <label>{{ entry_required_date }}</label>
                        <input type="date" name="required_date" class="form-control">
                      </div>
                      <div class="col-sm-3">
                        <label>{{ entry_priority }}</label>
                        <select name="priority" class="form-control select2">
                          <option value="low">{{ text_priority_low }}</option>
                          <option value="medium">{{ text_priority_medium }}</option>
                          <option value="high">{{ text_priority_high }}</option>
                          <option value="urgent">{{ text_priority_urgent }}</option>
                        </select>
                      </div>
                    </div>
                    <br>
                    <div class="form-group">
                      <label>{{ entry_notes }}</label>
                      <textarea name="notes" rows="2" class="form-control"></textarea>
                    </div>
                    <hr>
                    <h4>{{ text_items }}</h4>
                    <table class="table table-bordered" id="table-edit-requisition-items">
                      <thead>
                        <tr>
                          <th>{{ column_product }}</th>
                          <th>{{ column_quantity }}</th>
                          <th>{{ column_unit }}</th>
                          <th>{{ column_description }}</th>
                          <th style="width:50px;">{{ column_action }}</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr><td colspan="5">{{ text_no_results }}</td></tr>
                      </tbody>
                      <tfoot>
                        <tr>
                          <td colspan="4"></td>
                          <td><button type="button" class="btn btn-primary" id="btn-add-row-edit-requisition"><i class="fa fa-plus"></i></button></td>
                        </tr>
                      </tfoot>
                    </table>
                  </div>
                  <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_close }}</button>
                    <button type="button" class="btn btn-primary" id="btn-save-edit-requisition">{{ button_save }}</button>
                  </div>
                </form>
              </div>
            </div>
          </div>

          <!-- مودال عرض تفاصيل طلب -->
          <div id="modal-view-requisition" class="modal fade" tabindex="-1" role="dialog">
            <div class="modal-dialog modal-lg">
              <div class="modal-content">
                <div class="modal-header" style="background:#f7f9fb;border-bottom:1px solid #ddd;">
                  <button type="button" class="close" data-dismiss="modal">&times;</button>
                  <h4 class="modal-title">{{ text_view_requisition }}</h4>
                </div>
                <div class="modal-body" id="view-requisition-details">
                  <!-- تعبئة عبر AJAX -->
                </div>
                <div class="modal-footer">
                  <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_close }}</button>
                </div>
              </div>
            </div>
          </div>

          <!-- TAB 3: Quotations (RFQ) -->
          <div class="tab-pane" id="tab-quotation">
            <div class="filter-container">
              <div class="row">
                <div class="col-sm-4">
                  <label>{{ entry_quotation_number }}</label>
                  <input type="text" id="filter-quotation-number" class="form-control" placeholder="{{ entry_quotation_number }}">
                </div>
                <div class="col-sm-4">
                  <label>{{ entry_vendor }}</label>
                  <select id="filter-quotation-vendor" class="form-control select2">
                    <option value="">{{ text_all_vendors }}</option>
                    {% for v in vendors %}
                      <option value="{{ v.supplier_id }}">{{ v.firstname }} {{ v.lastname }}</option>
                    {% endfor %}
                  </select>
                </div>
                <div class="col-sm-4">
                  <label>{{ entry_status }}</label>
                  <select id="filter-quotation-status" class="form-control select2">
                    <option value="">{{ text_all }}</option>
                    {% for st in quotation_statuses %}
                      <option value="{{ st.value }}">{{ st.text }}</option>
                    {% endfor %}
                  </select>
                </div>
              </div>
              <br>
              <div class="row">
                <div class="col-sm-5">
                  <label>{{ entry_date_start }}</label>
                  <input type="text" id="filter-quotation-date-start" class="form-control date" placeholder="{{ entry_date_start }}">
                </div>
                <div class="col-sm-5">
                  <label>{{ entry_date_end }}</label>
                  <input type="text" id="filter-quotation-date-end" class="form-control date" placeholder="{{ entry_date_end }}">
                </div>
                <div class="col-sm-2">
                  <br>
                  <button type="button" id="button-filter-quotation" class="btn btn-primary btn-block">
                    <i class="fa fa-filter"></i> {{ button_filter }}
                  </button>
                </div>
              </div>
            </div>

            <div class="table-responsive" style="margin-top:20px;">
              <table id="table-quotation" class="table table-bordered table-hover">
                <thead>
                  <tr>
                    <th style="width:1px;" class="text-center"><input type="checkbox" id="check-all-quotation"></th>
                    <th class="text-left">{{ column_quotation_number }}</th>
                    <th class="text-left">{{ column_vendor }}</th>
                    <th class="text-right">{{ column_total_amount }}</th>
                    <th class="text-left">{{ column_status }}</th>
                    <th class="text-left">{{ column_date_added }}</th>
                    <th class="text-right">{{ column_action }}</th>
                  </tr>
                </thead>
                <tbody>
                  <tr><td class="text-center" colspan="7">{{ text_no_results }}</td></tr>
                </tbody>
              </table>
            </div>

            <div class="pagination-container">
              <div class="row">
                <div class="col-sm-6 text-left" id="quotation-pagination"></div>
                <div class="col-sm-6 text-right" id="quotation-results"></div>
              </div>
            </div>
          </div>

          <!-- مودال إضافة عرض سعر -->
          <div id="modal-add-quotation" class="modal fade" tabindex="-1" role="dialog">
            <div class="modal-dialog modal-xl">
              <div class="modal-content">
                <form id="form-add-quotation">
                  <div class="modal-header" style="background:#f7f9fb;border-bottom:1px solid #ddd;">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">{{ text_add_quotation }}</h4>
                  </div>
                  <div class="modal-body">
                    <input type="hidden" name="requisition_id" value="">

                    <div class="row">
                      <div class="col-sm-4">
                        <label>{{ entry_quotation_number }}</label>
                        <input type="text" name="quotation_number" class="form-control">
                      </div>
                      <div class="col-sm-4">
                        <label>{{ entry_vendor }}</label>
                        <select name="vendor_id" class="form-control select2"></select>
                      </div>
                      <div class="col-sm-4">
                        <label>{{ entry_validity_date }}</label>
                        <input type="date" name="validity_date" class="form-control">
                      </div>
                    </div>
                    <br>
                    <div class="form-group">
                      <label>{{ entry_notes }}</label>
                      <textarea name="notes" class="form-control" rows="2"></textarea>
                    </div>
                    <hr>
                    <h4>{{ text_items }}</h4>
                    <table class="table table-bordered" id="table-add-quotation-items">
                      <thead>
                        <tr>
                          <th>{{ column_product }}</th>
                          <th>{{ column_quantity }}</th>
                          <th>{{ column_unit_price }}</th>
                          <th>{{ column_total }}</th>
                          <th>{{ column_description }}</th>
                          <th style="width:50px;">{{ column_action }}</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr><td colspan="6">{{ text_no_results }}</td></tr>
                      </tbody>
                      <tfoot>
                        <tr>
                          <td colspan="5"></td>
                          <td><button type="button" class="btn btn-primary" id="btn-add-row-add-quotation"><i class="fa fa-plus"></i></button></td>
                        </tr>
                      </tfoot>
                    </table>
                  </div>
                  <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_close }}</button>
                    <button type="button" class="btn btn-primary" id="btn-save-add-quotation">{{ button_save }}</button>
                  </div>
                </form>
              </div>
            </div>
          </div>

          <!-- مودال تعديل عرض سعر -->
          <div id="modal-edit-quotation" class="modal fade" tabindex="-1" role="dialog">
            <div class="modal-dialog modal-xl">
              <div class="modal-content">
                <form id="form-edit-quotation">
                  <div class="modal-header" style="background:#f7f9fb;border-bottom:1px solid #ddd;">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">{{ text_edit_quotation }}</h4>
                  </div>
                  <div class="modal-body">
                    <input type="hidden" name="quotation_id" value="">

                    <div class="row">
                      <div class="col-sm-4">
                        <label>{{ entry_quotation_number }}</label>
                        <input type="text" name="quotation_number" class="form-control">
                      </div>
                      <div class="col-sm-4">
                        <label>{{ entry_vendor }}</label>
                        <select name="vendor_id" class="form-control select2"></select>
                      </div>
                      <div class="col-sm-4">
                        <label>{{ entry_validity_date }}</label>
                        <input type="date" name="validity_date" class="form-control">
                      </div>
                    </div>
                    <br>
                    <div class="form-group">
                      <label>{{ entry_notes }}</label>
                      <textarea name="notes" class="form-control" rows="2"></textarea>
                    </div>
                    <hr>
                    <h4>{{ text_items }}</h4>
                    <table class="table table-bordered" id="table-edit-quotation-items">
                      <thead>
                        <tr>
                          <th>{{ column_product }}</th>
                          <th>{{ column_quantity }}</th>
                          <th>{{ column_unit_price }}</th>
                          <th>{{ column_total }}</th>
                          <th>{{ column_description }}</th>
                          <th style="width:50px;">{{ column_action }}</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr><td colspan="6">{{ text_no_results }}</td></tr>
                      </tbody>
                      <tfoot>
                        <tr>
                          <td colspan="5"></td>
                          <td><button type="button" class="btn btn-primary" id="btn-add-row-edit-quotation"><i class="fa fa-plus"></i></button></td>
                        </tr>
                      </tfoot>
                    </table>
                  </div>
                  <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_close }}</button>
                    <button type="button" class="btn btn-primary" id="btn-save-edit-quotation">{{ button_save }}</button>
                  </div>
                </form>
              </div>
            </div>
          </div>

          <!-- مودال العرض -->
          <div id="modal-view-quotation" class="modal fade" tabindex="-1" role="dialog">
            <div class="modal-dialog modal-lg">
              <div class="modal-content">
                <div class="modal-header" style="background:#f7f9fb;border-bottom:1px solid #ddd;">
                  <button type="button" class="close" data-dismiss="modal">&times;</button>
                  <h4 class="modal-title">{{ text_view_quotation }}</h4>
                </div>
                <div class="modal-body" id="view-quotation-details"></div>
                <div class="modal-footer">
                  <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_close }}</button>
                </div>
              </div>
            </div>
          </div>

          <!-- Approve Quotation -->
          <div id="modal-approve-quotation" class="modal fade" tabindex="-1" role="dialog">
            <div class="modal-dialog">
              <div class="modal-content">
                <form id="form-approve-quotation">
                  <div class="modal-header" style="background:#f7f9fb;border-bottom:1px solid #ddd;">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">{{ text_approve_quotation }}</h4>
                  </div>
                  <div class="modal-body">
                    <input type="hidden" name="quotation_id" value="">
                    <div class="form-group">
                      <label>{{ entry_approval_comment }}</label>
                      <textarea name="comment" class="form-control" rows="3"></textarea>
                    </div>
                  </div>
                  <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_cancel }}</button>
                    <button type="button" class="btn btn-success" id="btn-confirm-approve-quotation">{{ button_approve }}</button>
                  </div>
                </form>
              </div>
            </div>
          </div>

          <!-- Reject Quotation -->
          <div id="modal-reject-quotation" class="modal fade" tabindex="-1" role="dialog">
            <div class="modal-dialog">
              <div class="modal-content">
                <form id="form-reject-quotation">
                  <div class="modal-header" style="background:#f7f9fb;border-bottom:1px solid #ddd;">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">{{ text_reject_quotation }}</h4>
                  </div>
                  <div class="modal-body">
                    <input type="hidden" name="quotation_id" value="">
                    <div class="form-group">
                      <label>{{ entry_reject_reason }}</label>
                      <textarea name="reject_reason" class="form-control" rows="3"></textarea>
                    </div>
                  </div>
                  <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_cancel }}</button>
                    <button type="button" class="btn btn-danger" id="btn-confirm-reject-quotation">{{ button_reject }}</button>
                  </div>
                </form>
              </div>
            </div>
          </div>

          {# -----------------------------------------------------------------
             TAB 4: Purchase Orders
          ----------------------------------------------------------------- #}
          <div class="tab-pane" id="tab-purchase-order">
            <div class="filter-container">
              <div class="row">
                <div class="col-sm-4">
                  <label>{{ entry_po_number }}</label>
                  <input type="text" id="filter-po-number" class="form-control" placeholder="{{ entry_po_number }}">
                </div>
                <div class="col-sm-4">
                  <label>{{ entry_status }}</label>
                  <select id="filter-po-status" class="form-control select2">
                    <option value="">{{ text_all }}</option>
                    {% for st in po_statuses %}
                      <option value="{{ st.value }}">{{ st.text }}</option>
                    {% endfor %}
                  </select>
                </div>
                <div class="col-sm-4">
                  <label>{{ entry_vendor }}</label>
                  <select id="filter-po-vendor" class="form-control select2">
                    <option value="">{{ text_all_vendors }}</option>
                    {% for v in vendors %}
                      <option value="{{ v.supplier_id }}">{{ v.firstname }} {{ v.lastname }}</option>
                    {% endfor %}
                  </select>
                </div>
              </div>
              <br>
              <div class="row">
                <div class="col-sm-5">
                  <label>{{ entry_date_start }}</label>
                  <input type="text" id="filter-po-date-start" class="form-control date" placeholder="{{ entry_date_start }}">
                </div>
                <div class="col-sm-5">
                  <label>{{ entry_date_end }}</label>
                  <input type="text" id="filter-po-date-end" class="form-control date" placeholder="{{ entry_date_end }}">
                </div>
                <div class="col-sm-2">
                  <br>
                  <button type="button" id="button-filter-po" class="btn btn-primary btn-block">
                    <i class="fa fa-filter"></i> {{ button_filter }}
                  </button>
                </div>
              </div>
            </div>

            <div class="table-responsive" style="margin-top:20px;">
              <table id="table-po" class="table table-bordered table-hover">
                <thead>
                  <tr>
                    <th style="width:1px;" class="text-center"><input type="checkbox" id="check-all-po"></th>
                    <th class="text-left">{{ column_po_number }}</th>
                    <th class="text-left">{{ column_status }}</th>
                    <th class="text-left">{{ column_vendor }}</th>
                    <th class="text-right">{{ column_total_amount }}</th>
                    <th class="text-left">{{ column_date_added }}</th>
                    <th class="text-right">{{ column_action }}</th>
                  </tr>
                </thead>
                <tbody>
                  <tr><td class="text-center" colspan="7">{{ text_no_results }}</td></tr>
                </tbody>
              </table>
            </div>

            <div class="pagination-container">
              <div class="row">
                <div class="col-sm-6 text-left" id="po-pagination"></div>
                <div class="col-sm-6 text-right" id="po-results"></div>
              </div>
            </div>
          </div>

          <!-- مودال إضافة PO -->
          <div id="modal-add-po" class="modal fade" tabindex="-1" role="dialog">
            <div class="modal-dialog modal-xl" role="document">
              <div class="modal-content">
                <form id="form-add-po">
                  <div class="modal-header" style="background:#f7f9fb;border-bottom:1px solid #ddd;">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">{{ text_add_po }}</h4>
                  </div>
                  <div class="modal-body">
                    <input type="hidden" name="requisition_id" value="">
                    <input type="hidden" name="quotation_id" value="">

                    <div class="row">
                      <div class="col-sm-4">
                        <label>{{ entry_po_number }}</label>
                        <input type="text" name="po_number" class="form-control" placeholder="{{ entry_po_number }}">
                      </div>
                      <div class="col-sm-4">
                        <label>{{ entry_vendor }}</label>
                        <select name="vendor_id" class="form-control select2 po-vendor-select"></select>
                      </div>
                      <div class="col-sm-4">
                        <label>{{ entry_order_date }}</label>
                        <input type="date" name="order_date" class="form-control">
                      </div>
                    </div>
                    <br>
                    <div class="row">
                      <div class="col-sm-4">
                        <label>{{ entry_expected_delivery_date }}</label>
                        <input type="date" name="expected_delivery_date" class="form-control">
                      </div>
                      <div class="col-sm-4">
                        <label>{{ entry_status }}</label>
                        <select name="status" class="form-control select2">
                          {% for st in po_statuses %}
                            <option value="{{ st.value }}">{{ st.text }}</option>
                          {% endfor %}
                        </select>
                      </div>
                      <div class="col-sm-4">
                        <label>{{ entry_terms_conditions }}</label>
                        <input type="text" name="terms_conditions" class="form-control">
                      </div>
                    </div>
                    <br>
                    <div class="form-group">
                      <label>{{ entry_notes }}</label>
                      <textarea name="notes" class="form-control" rows="2"></textarea>
                    </div>
                    <hr>
                    <h4>{{ text_items }}</h4>
                    <table class="table table-bordered" id="table-add-po-items">
                      <thead>
                        <tr>
                          <th>{{ column_product }}</th>
                          <th>{{ column_quantity }}</th>
                          <th>{{ column_unit }}</th>
                          <th>{{ column_price }}</th>
                          <th>{{ column_total }}</th>
                          <th style="width:50px;">{{ column_action }}</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr><td colspan="6">{{ text_no_results }}</td></tr>
                      </tbody>
                      <tfoot>
                        <tr>
                          <td colspan="5"></td>
                          <td>
                            <button type="button" class="btn btn-primary" id="btn-add-row-add-po">
                              <i class="fa fa-plus"></i>
                            </button>
                          </td>
                        </tr>
                      </tfoot>
                    </table>
                  </div>
                  <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_close }}</button>
                    <button type="button" class="btn btn-primary" id="btn-save-add-po">{{ button_save }}</button>
                  </div>
                </form>
              </div>
            </div>
          </div>

          <!-- مودال تعديل PO -->
          <div id="modal-edit-po" class="modal fade" tabindex="-1" role="dialog">
            <div class="modal-dialog modal-xl" role="document">
              <div class="modal-content">
                <form id="form-edit-po">
                  <div class="modal-header" style="background:#f7f9fb;border-bottom:1px solid #ddd;">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">{{ text_edit_po }}</h4>
                  </div>
                  <div class="modal-body">
                    <input type="hidden" name="po_id" value="">
                    <div class="row">
                      <div class="col-sm-4">
                        <label>{{ entry_po_number }}</label>
                        <input type="text" name="po_number" class="form-control">
                      </div>
                      <div class="col-sm-4">
                        <label>{{ entry_vendor }}</label>
                        <select name="vendor_id" class="form-control select2 po-vendor-select-edit"></select>
                      </div>
                      <div class="col-sm-4">
                        <label>{{ entry_order_date }}</label>
                        <input type="date" name="order_date" class="form-control">
                      </div>
                    </div>
                    <br>
                    <div class="row">
                      <div class="col-sm-4">
                        <label>{{ entry_expected_delivery_date }}</label>
                        <input type="date" name="expected_delivery_date" class="form-control">
                      </div>
                      <div class="col-sm-4">
                        <label>{{ entry_status }}</label>
                        <select name="status" class="form-control select2">
                          {% for st in po_statuses %}
                            <option value="{{ st.value }}">{{ st.text }}</option>
                          {% endfor %}
                        </select>
                      </div>
                      <div class="col-sm-4">
                        <label>{{ entry_terms_conditions }}</label>
                        <input type="text" name="terms_conditions" class="form-control">
                      </div>
                    </div>
                    <br>
                    <div class="form-group">
                      <label>{{ entry_notes }}</label>
                      <textarea name="notes" class="form-control" rows="2"></textarea>
                    </div>
                    <hr>
                    <h4>{{ text_items }}</h4>
                    <table class="table table-bordered" id="table-edit-po-items">
                      <thead>
                        <tr>
                          <th>{{ column_product }}</th>
                          <th>{{ column_quantity }}</th>
                          <th>{{ column_unit }}</th>
                          <th>{{ column_price }}</th>
                          <th>{{ column_total }}</th>
                          <th style="width:50px;">{{ column_action }}</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr><td colspan="6">{{ text_no_results }}</td></tr>
                      </tbody>
                      <tfoot>
                        <tr>
                          <td colspan="5"></td>
                          <td>
                            <button type="button" class="btn btn-primary" id="btn-add-row-edit-po">
                              <i class="fa fa-plus"></i>
                            </button>
                          </td>
                        </tr>
                      </tfoot>
                    </table>
                  </div>
                  <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_close }}</button>
                    <button type="button" class="btn btn-primary" id="btn-save-edit-po">{{ button_save }}</button>
                  </div>
                </form>
              </div>
            </div>
          </div>

          <!-- مودال عرض PO -->
          <div id="modal-view-po" class="modal fade" tabindex="-1" role="dialog">
            <div class="modal-dialog modal-lg" role="document">
              <div class="modal-content">
                <div class="modal-header" style="background:#f7f9fb;border-bottom:1px solid #ddd;">
                  <button type="button" class="close" data-dismiss="modal">&times;</button>
                  <h4 class="modal-title">{{ text_view_po }}</h4>
                </div>
                <div class="modal-body" id="view-po-details">
                  <!-- يتم تعبئة عبر AJAX -->
                </div>
                <div class="modal-footer">
                  <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_close }}</button>
                </div>
              </div>
            </div>
          </div>

          <!-- Approve Purchase Order -->
          <div id="modal-approve-po" class="modal fade" tabindex="-1" role="dialog">
            <div class="modal-dialog" role="document">
              <div class="modal-content">
                <form id="form-approve-po">
                  <div class="modal-header" style="background:#f7f9fb;border-bottom:1px solid #ddd;">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">{{ text_approve_po }}</h4>
                  </div>
                  <div class="modal-body">
                    <input type="hidden" name="po_id" value="">
                    <div class="form-group">
                      <label>{{ entry_comment }}</label>
                      <textarea name="comment" class="form-control" rows="3"></textarea>
                    </div>
                  </div>
                  <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_cancel }}</button>
                    <button type="button" class="btn btn-success" id="btn-confirm-approve-po">{{ button_approve }}</button>
                  </div>
                </form>
              </div>
            </div>
          </div>

          <!-- Reject Purchase Order -->
          <div id="modal-reject-po" class="modal fade" tabindex="-1" role="dialog">
            <div class="modal-dialog" role="document">
              <div class="modal-content">
                <form id="form-reject-po">
                  <div class="modal-header" style="background:#f7f9fb;border-bottom:1px solid #ddd;">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">{{ text_reject_po }}</h4>
                  </div>
                  <div class="modal-body">
                    <input type="hidden" name="po_id" value="">
                    <div class="form-group">
                      <label>{{ entry_reject_reason }}</label>
                      <textarea name="reject_reason" class="form-control" rows="3"></textarea>
                    </div>
                  </div>
                  <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_cancel }}</button>
                    <button type="button" class="btn btn-danger" id="btn-confirm-reject-po">{{ button_reject }}</button>
                  </div>
                </form>
              </div>
            </div>
          </div>

          {# -----------------------------------------------------------------
             TAB 5: Goods Receipts
          ----------------------------------------------------------------- #}
          <div class="tab-pane" id="tab-goods-receipt">
            <div class="filter-container">
              <div class="row">
                <div class="col-sm-4">
                  <label>{{ entry_gr_number }}</label>
                  <input type="text" id="filter-gr-number" class="form-control" placeholder="{{ entry_gr_number }}">
                </div>
                <div class="col-sm-4">
                  <label>{{ entry_status }}</label>
                  <select id="filter-gr-status" class="form-control select2">
                    <option value="">{{ text_all }}</option>
                    {% for st in gr_statuses %}
                      <option value="{{ st.value }}">{{ st.text }}</option>
                    {% endfor %}
                  </select>
                </div>
                <div class="col-sm-4">
                  <label>{{ entry_po_number }}</label>
                  <input type="text" id="filter-gr-po-number" class="form-control" placeholder="{{ entry_po_number }}">
                </div>
              </div>
              <br>
              <div class="row">
                <div class="col-sm-5">
                  <label>{{ entry_date_start }}</label>
                  <input type="text" id="filter-gr-date-start" class="form-control date" placeholder="{{ entry_date_start }}">
                </div>
                <div class="col-sm-5">
                  <label>{{ entry_date_end }}</label>
                  <input type="text" id="filter-gr-date-end" class="form-control date" placeholder="{{ entry_date_end }}">
                </div>
                <div class="col-sm-2">
                  <br>
                  <button type="button" id="button-filter-gr" class="btn btn-primary btn-block">
                    <i class="fa fa-filter"></i> {{ button_filter }}
                  </button>
                </div>
              </div>
            </div>

            <div class="table-responsive" style="margin-top:20px;">
              <table id="table-gr" class="table table-bordered table-hover">
                <thead>
                  <tr>
                    <th style="width:1px;" class="text-center"><input type="checkbox" id="check-all-gr"></th>
                    <th class="text-left">{{ column_gr_number }}</th>
                    <th class="text-left">{{ column_po_number }}</th>
                    <th class="text-left">{{ column_status }}</th>
                    <th class="text-left">{{ column_date_added }}</th>
                    <th class="text-right">{{ column_action }}</th>
                  </tr>
                </thead>
                <tbody>
                  <tr><td class="text-center" colspan="6">{{ text_no_results }}</td></tr>
                </tbody>
              </table>
            </div>

            <div class="pagination-container">
              <div class="row">
                <div class="col-sm-6 text-left" id="gr-pagination"></div>
                <div class="col-sm-6 text-right" id="gr-results"></div>
              </div>
            </div>
          </div>

          <!-- مودال إضافة GR -->
          <div id="modal-add-gr" class="modal fade" tabindex="-1" role="dialog">
            <div class="modal-dialog modal-xl" role="document">
              <div class="modal-content">
                <form id="form-add-gr">
                  <div class="modal-header" style="background:#f7f9fb;border-bottom:1px solid #ddd;">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">{{ text_add_goods_receipt }}</h4>
                  </div>
                  <div class="modal-body">
                    <input type="hidden" name="po_id" value="">

                    <div class="row">
                      <div class="col-sm-4">
                        <label>{{ entry_gr_number }}</label>
                        <input type="text" name="gr_number" class="form-control" placeholder="{{ entry_gr_number }}">
                      </div>
                      <div class="col-sm-4">
                        <label>{{ entry_po_number }}</label>
                        <select name="po_id_select" class="form-control select2 gr-po-select"></select>
                      </div>
                      <div class="col-sm-4">
                        <label>{{ entry_receipt_date }}</label>
                        <input type="date" name="receipt_date" class="form-control">
                      </div>
                    </div>
                    <br>
                    <div class="row">
                      <div class="col-sm-4">
                        <label>{{ entry_status }}</label>
                        <select name="status" class="form-control select2">
                          {% for st in gr_statuses %}
                            <option value="{{ st.value }}">{{ st.text }}</option>
                          {% endfor %}
                        </select>
                      </div>
                      <div class="col-sm-8">
                        <label>{{ entry_notes }}</label>
                        <textarea name="notes" class="form-control" rows="2"></textarea>
                      </div>
                    </div>
                    <hr>
                    <h4>{{ text_items }}</h4>
                    <table class="table table-bordered" id="table-add-gr-items">
                      <thead>
                        <tr>
                          <th>{{ column_product }}</th>
                          <th>{{ column_quantity_received }}</th>
                          <th>{{ column_unit }}</th>
                          <th>{{ column_quality_result }}</th>
                          <th>{{ column_remarks }}</th>
                          <th style="width:50px;">{{ column_action }}</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr><td colspan="6">{{ text_no_results }}</td></tr>
                      </tbody>
                      <tfoot>
                        <tr>
                          <td colspan="5"></td>
                          <td>
                            <button type="button" class="btn btn-primary" id="btn-add-row-add-gr">
                              <i class="fa fa-plus"></i>
                            </button>
                          </td>
                        </tr>
                      </tfoot>
                    </table>
                  </div>
                  <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_close }}</button>
                    <button type="button" class="btn btn-primary" id="btn-save-add-gr">{{ button_save }}</button>
                  </div>
                </form>
              </div>
            </div>
          </div>

          <!-- مودال تعديل GR -->
          <div id="modal-edit-gr" class="modal fade" tabindex="-1" role="dialog">
            <div class="modal-dialog modal-xl" role="document">
              <div class="modal-content">
                <form id="form-edit-gr">
                  <div class="modal-header" style="background:#f7f9fb;border-bottom:1px solid #ddd;">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">{{ text_edit_goods_receipt }}</h4>
                  </div>
                  <div class="modal-body">
                    <input type="hidden" name="goods_receipt_id" value="">
                    <div class="row">
                      <div class="col-sm-4">
                        <label>{{ entry_gr_number }}</label>
                        <input type="text" name="gr_number" class="form-control">
                      </div>
                      <div class="col-sm-4">
                        <label>{{ entry_po_number }}</label>
                        <select name="po_id_select" class="form-control select2 gr-po-select-edit"></select>
                      </div>
                      <div class="col-sm-4">
                        <label>{{ entry_receipt_date }}</label>
                        <input type="date" name="receipt_date" class="form-control">
                      </div>
                    </div>
                    <br>
                    <div class="row">
                      <div class="col-sm-4">
                        <label>{{ entry_status }}</label>
                        <select name="status" class="form-control select2">
                          {% for st in gr_statuses %}
                            <option value="{{ st.value }}">{{ st.text }}</option>
                          {% endfor %}
                        </select>
                      </div>
                      <div class="col-sm-8">
                        <label>{{ entry_notes }}</label>
                        <textarea name="notes" class="form-control" rows="2"></textarea>
                      </div>
                    </div>
                    <hr>
                    <h4>{{ text_items }}</h4>
                    <table class="table table-bordered" id="table-edit-gr-items">
                      <thead>
                        <tr>
                          <th>{{ column_product }}</th>
                          <th>{{ column_quantity_received }}</th>
                          <th>{{ column_unit }}</th>
                          <th>{{ column_quality_result }}</th>
                          <th>{{ column_remarks }}</th>
                          <th style="width:50px;">{{ column_action }}</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr><td colspan="6">{{ text_no_results }}</td></tr>
                      </tbody>
                      <tfoot>
                        <tr>
                          <td colspan="5"></td>
                          <td>
                            <button type="button" class="btn btn-primary" id="btn-add-row-edit-gr">
                              <i class="fa fa-plus"></i>
                            </button>
                          </td>
                        </tr>
                      </tfoot>
                    </table>
                  </div>
                  <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_close }}</button>
                    <button type="button" class="btn btn-primary" id="btn-save-edit-gr">{{ button_save }}</button>
                  </div>
                </form>
              </div>
            </div>
          </div>

          <!-- مودال عرض GR -->
          <div id="modal-view-gr" class="modal fade" tabindex="-1" role="dialog">
            <div class="modal-dialog modal-lg" role="document">
              <div class="modal-content">
                <div class="modal-header" style="background:#f7f9fb;border-bottom:1px solid #ddd;">
                  <button type="button" class="close" data-dismiss="modal">&times;</button>
                  <h4 class="modal-title">{{ text_view_goods_receipt }}</h4>
                </div>
                <div class="modal-body" id="view-gr-details">
                  <!-- يتم تعبئة التفاصيل عبر الـJS -->
                </div>
                <div class="modal-footer">
                  <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_close }}</button>
                </div>
              </div>
            </div>
          </div>

          <!-- Approve / Reject GR -->
          <div id="modal-approve-gr" class="modal fade" tabindex="-1" role="dialog">
            <div class="modal-dialog" role="document">
              <div class="modal-content">
                <form id="form-approve-gr">
                  <div class="modal-header" style="background:#f7f9fb;border-bottom:1px solid #ddd;">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">{{ text_approve_goods_receipt }}</h4>
                  </div>
                  <div class="modal-body">
                    <input type="hidden" name="goods_receipt_id" value="">
                    <div class="form-group">
                      <label>{{ entry_approval_comment }}</label>
                      <textarea name="comment" class="form-control" rows="3"></textarea>
                    </div>
                  </div>
                  <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_cancel }}</button>
                    <button type="button" class="btn btn-success" id="btn-confirm-approve-gr">{{ button_approve }}</button>
                  </div>
                </form>
              </div>
            </div>
          </div>

          <div id="modal-reject-gr" class="modal fade" tabindex="-1" role="dialog">
            <div class="modal-dialog" role="document">
              <div class="modal-content">
                <form id="form-reject-gr">
                  <div class="modal-header" style="background:#f7f9fb;border-bottom:1px solid #ddd;">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">{{ text_reject_goods_receipt }}</h4>
                  </div>
                  <div class="modal-body">
                    <input type="hidden" name="goods_receipt_id" value="">
                    <div class="form-group">
                      <label>{{ entry_reject_reason }}</label>
                      <textarea name="reject_reason" class="form-control" rows="3"></textarea>
                    </div>
                  </div>
                  <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_cancel }}</button>
                    <button type="button" class="btn btn-danger" id="btn-confirm-reject-gr">{{ button_reject }}</button>
                  </div>
                </form>
              </div>
            </div>
          </div>


          {# -----------------------------------------------------------------
             TAB 6: Supplier Invoices
          ----------------------------------------------------------------- #}
          <div class="tab-pane" id="tab-supplier-invoice">
            <div class="filter-container">
              <div class="row">
                <div class="col-sm-4">
                  <label>{{ entry_invoice_number }}</label>
                  <input type="text" id="filter-invoice-number" class="form-control" placeholder="{{ entry_invoice_number }}">
                </div>
                <div class="col-sm-4">
                  <label>{{ entry_status }}</label>
                  <select id="filter-invoice-status" class="form-control select2">
                    <option value="">{{ text_all }}</option>
                    {% for st in invoice_statuses %}
                      <option value="{{ st.value }}">{{ st.text }}</option>
                    {% endfor %}
                  </select>
                </div>
                <div class="col-sm-4">
                  <label>{{ entry_vendor }}</label>
                  <select id="filter-invoice-vendor" class="form-control select2">
                    <option value="">{{ text_all_vendors }}</option>
                    {% for v in vendors %}
                      <option value="{{ v.supplier_id }}">{{ v.firstname }} {{ v.lastname }}</option>
                    {% endfor %}
                  </select>
                </div>
              </div>
              <br>
              <div class="row">
                <div class="col-sm-5">
                  <label>{{ entry_date_start }}</label>
                  <input type="text" id="filter-invoice-date-start" class="form-control date" placeholder="{{ entry_date_start }}">
                </div>
                <div class="col-sm-5">
                  <label>{{ entry_date_end }}</label>
                  <input type="text" id="filter-invoice-date-end" class="form-control date" placeholder="{{ entry_date_end }}">
                </div>
                <div class="col-sm-2">
                  <br>
                  <button type="button" id="button-filter-invoice" class="btn btn-primary btn-block">
                    <i class="fa fa-filter"></i> {{ button_filter }}
                  </button>
                </div>
              </div>
            </div>

            <div class="table-responsive" style="margin-top:20px;">
              <table id="table-invoice" class="table table-bordered table-hover">
                <thead>
                  <tr>
                    <th style="width:1px;" class="text-center"><input type="checkbox" id="check-all-invoice"></th>
                    <th class="text-left">{{ column_invoice_number }}</th>
                    <th class="text-left">{{ column_vendor }}</th>
                    <th class="text-right">{{ column_total_amount }}</th>
                    <th class="text-left">{{ column_status }}</th>
                    <th class="text-left">{{ column_date_added }}</th>
                    <th class="text-right">{{ column_action }}</th>
                  </tr>
                </thead>
                <tbody>
                  <tr><td class="text-center" colspan="7">{{ text_no_results }}</td></tr>
                </tbody>
              </table>
            </div>

            <div class="pagination-container">
              <div class="row">
                <div class="col-sm-6 text-left" id="invoice-pagination"></div>
                <div class="col-sm-6 text-right" id="invoice-results"></div>
              </div>
            </div>
          </div>

          <!-- مودال إضافة فاتورة -->
          <div id="modal-add-invoice" class="modal fade" tabindex="-1" role="dialog">
            <div class="modal-dialog modal-xl" role="document">
              <div class="modal-content">
                <form id="form-add-invoice">
                  <div class="modal-header" style="background:#f7f9fb;border-bottom:1px solid #ddd;">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">{{ text_add_invoice }}</h4>
                  </div>
                  <div class="modal-body">
                    <input type="hidden" name="invoice_id" value="">
                    <div class="row">
                      <div class="col-sm-4">
                        <label>{{ entry_invoice_number }}</label>
                        <input type="text" name="invoice_number" class="form-control" placeholder="{{ entry_invoice_number }}">
                      </div>
                      <div class="col-sm-4">
                        <label>{{ entry_vendor }}</label>
                        <select name="vendor_id" class="form-control select2 invoice-vendor-select"></select>
                      </div>
                      <div class="col-sm-4">
                        <label>{{ entry_invoice_date }}</label>
                        <input type="date" name="invoice_date" class="form-control">
                      </div>
                    </div>
                    <br>
                    <div class="row">
                      <div class="col-sm-4">
                        <label>{{ entry_due_date }}</label>
                        <input type="date" name="due_date" class="form-control">
                      </div>
                      <div class="col-sm-4">
                        <label>{{ entry_status }}</label>
                        <select name="status" class="form-control select2">
                          {% for st in invoice_statuses %}
                            <option value="{{ st.value }}">{{ st.text }}</option>
                          {% endfor %}
                        </select>
                      </div>
                      <div class="col-sm-4">
                        <label>{{ entry_po_number }}</label>
                        <select name="po_id" class="form-control select2 invoice-po-select"></select>
                      </div>
                    </div>
                    <br>
                    <div class="form-group">
                      <label>{{ entry_notes }}</label>
                      <textarea name="notes" class="form-control" rows="2"></textarea>
                    </div>
                    <hr>
                    <h4>{{ text_items }}</h4>
                    <table class="table table-bordered" id="table-add-invoice-items">
                      <thead>
                        <tr>
                          <th>{{ column_product }}</th>
                          <th>{{ column_quantity }}</th>
                          <th>{{ column_unit }}</th>
                          <th>{{ column_unit_price }}</th>
                          <th>{{ column_total_price }}</th>
                          <th style="width:50px;">{{ column_action }}</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr><td colspan="6">{{ text_no_results }}</td></tr>
                      </tbody>
                      <tfoot>
                        <tr>
                          <td colspan="5"></td>
                          <td><button type="button" class="btn btn-primary" id="btn-add-row-add-invoice">
                              <i class="fa fa-plus"></i>
                            </button>
                          </td>
                        </tr>
                      </tfoot>
                    </table>

                    <hr>
                    <div class="row">
                      <div class="col-sm-3">
                        <label>{{ text_subtotal }}</label>
                        <input type="number" name="subtotal" class="form-control" step="0.01" value="0">
                      </div>
                      <div class="col-sm-3">
                        <label>{{ text_tax_amount }}</label>
                        <input type="number" name="tax_amount" class="form-control" step="0.01" value="0">
                      </div>
                      <div class="col-sm-3">
                        <label>{{ text_discount_amount }}</label>
                        <input type="number" name="discount_amount" class="form-control" step="0.01" value="0">
                      </div>
                      <div class="col-sm-3">
                        <label>{{ text_total_amount }}</label>
                        <input type="number" name="total_amount" class="form-control" step="0.01" value="0" readonly>
                      </div>
                    </div>

                  </div>
                  <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_close }}</button>
                    <button type="button" class="btn btn-primary" id="btn-save-add-invoice">{{ button_save }}</button>
                  </div>
                </form>
              </div>
            </div>
          </div>

          <!-- مودال تعديل فاتورة -->
          <div id="modal-edit-invoice" class="modal fade" tabindex="-1" role="dialog">
            <div class="modal-dialog modal-xl" role="document">
              <div class="modal-content">
                <form id="form-edit-invoice">
                  <div class="modal-header" style="background:#f7f9fb;border-bottom:1px solid #ddd;">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">{{ text_edit_invoice }}</h4>
                  </div>
                  <div class="modal-body">
                    <input type="hidden" name="invoice_id" value="">
                    <div class="row">
                      <div class="col-sm-4">
                        <label>{{ entry_invoice_number }}</label>
                        <input type="text" name="invoice_number" class="form-control">
                      </div>
                      <div class="col-sm-4">
                        <label>{{ entry_vendor }}</label>
                        <select name="vendor_id" class="form-control select2 invoice-vendor-select-edit"></select>
                      </div>
                      <div class="col-sm-4">
                        <label>{{ entry_invoice_date }}</label>
                        <input type="date" name="invoice_date" class="form-control">
                      </div>
                    </div>
                    <br>
                    <div class="row">
                      <div class="col-sm-4">
                        <label>{{ entry_due_date }}</label>
                        <input type="date" name="due_date" class="form-control">
                      </div>
                      <div class="col-sm-4">
                        <label>{{ entry_status }}</label>
                        <select name="status" class="form-control select2">
                          {% for st in invoice_statuses %}
                            <option value="{{ st.value }}">{{ st.text }}</option>
                          {% endfor %}
                        </select>
                      </div>
                      <div class="col-sm-4">
                        <label>{{ entry_po_number }}</label>
                        <select name="po_id" class="form-control select2 invoice-po-select-edit"></select>
                      </div>
                    </div>
                    <br>
                    <div class="form-group">
                      <label>{{ entry_notes }}</label>
                      <textarea name="notes" class="form-control" rows="2"></textarea>
                    </div>
                    <hr>
                    <h4>{{ text_items }}</h4>
                    <table class="table table-bordered" id="table-edit-invoice-items">
                      <thead>
                        <tr>
                          <th>{{ column_product }}</th>
                          <th>{{ column_quantity }}</th>
                          <th>{{ column_unit }}</th>
                          <th>{{ column_unit_price }}</th>
                          <th>{{ column_total_price }}</th>
                          <th style="width:50px;">{{ column_action }}</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr><td colspan="6">{{ text_no_results }}</td></tr>
                      </tbody>
                      <tfoot>
                        <tr>
                          <td colspan="5"></td>
                          <td><button type="button" class="btn btn-primary" id="btn-add-row-edit-invoice"><i class="fa fa-plus"></i></button></td>
                        </tr>
                      </tfoot>
                    </table>

                    <hr>
                    <div class="row">
                      <div class="col-sm-3">
                        <label>{{ text_subtotal }}</label>
                        <input type="number" name="subtotal" class="form-control" step="0.01" value="0">
                      </div>
                      <div class="col-sm-3">
                        <label>{{ text_tax_amount }}</label>
                        <input type="number" name="tax_amount" class="form-control" step="0.01" value="0">
                      </div>
                      <div class="col-sm-3">
                        <label>{{ text_discount_amount }}</label>
                        <input type="number" name="discount_amount" class="form-control" step="0.01" value="0">
                      </div>
                      <div class="col-sm-3">
                        <label>{{ text_total_amount }}</label>
                        <input type="number" name="total_amount" class="form-control" step="0.01" value="0" readonly>
                      </div>
                    </div>

                  </div>
                  <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_close }}</button>
                    <button type="button" class="btn btn-primary" id="btn-save-edit-invoice">{{ button_save }}</button>
                  </div>
                </form>
              </div>
            </div>
          </div>

          <!-- مودال العرض -->
          <div id="modal-view-invoice" class="modal fade" tabindex="-1" role="dialog">
            <div class="modal-dialog modal-lg" role="document">
              <div class="modal-content">
                <div class="modal-header" style="background:#f7f9fb;border-bottom:1px solid #ddd;">
                  <button type="button" class="close" data-dismiss="modal">&times;</button>
                  <h4 class="modal-title">{{ text_view_invoice }}</h4>
                </div>
                <div class="modal-body" id="view-invoice-details"></div>
                <div class="modal-footer">
                  <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_close }}</button>
                </div>
              </div>
            </div>
          </div>

          <!-- Approve / Reject Invoice -->
          <div id="modal-approve-invoice" class="modal fade" tabindex="-1" role="dialog">
            <div class="modal-dialog" role="document">
              <div class="modal-content">
                <form id="form-approve-invoice">
                  <div class="modal-header" style="background:#f7f9fb;border-bottom:1px solid #ddd;">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">{{ text_approve_invoice }}</h4>
                  </div>
                  <div class="modal-body">
                    <input type="hidden" name="invoice_id" value="">
                    <div class="form-group">
                      <label>{{ entry_approval_comment }}</label>
                      <textarea name="comment" class="form-control" rows="3"></textarea>
                    </div>
                  </div>
                  <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_cancel }}</button>
                    <button type="button" class="btn btn-success" id="btn-confirm-approve-invoice">{{ button_approve }}</button>
                  </div>
                </form>
              </div>
            </div>
          </div>

          <div id="modal-reject-invoice" class="modal fade" tabindex="-1" role="dialog">
            <div class="modal-dialog" role="document">
              <div class="modal-content">
                <form id="form-reject-invoice">
                  <div class="modal-header" style="background:#f7f9fb;border-bottom:1px solid #ddd;">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">{{ text_reject_invoice }}</h4>
                  </div>
                  <div class="modal-body">
                    <input type="hidden" name="invoice_id" value="">
                    <div class="form-group">
                      <label>{{ entry_reject_reason }}</label>
                      <textarea name="reject_reason" class="form-control" rows="3"></textarea>
                    </div>
                  </div>
                  <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_cancel }}</button>
                    <button type="button" class="btn btn-danger" id="btn-confirm-reject-invoice">{{ button_reject }}</button>
                  </div>
                </form>
              </div>
            </div>
          </div>


          {# -----------------------------------------------------------------
             TAB 7: Vendor Payments
          ----------------------------------------------------------------- #}
          <div class="tab-pane" id="tab-vendor-payment">
            <div class="filter-container">
              <div class="row">
                <div class="col-sm-4">
                  <label>{{ entry_payment_number }}</label>
                  <input type="text" id="filter-payment-number" class="form-control" placeholder="{{ entry_payment_number }}">
                </div>
                <div class="col-sm-4">
                  <label>{{ entry_status }}</label>
                  <select id="filter-payment-status" class="form-control select2">
                    <option value="">{{ text_all }}</option>
                    {% for st in payment_statuses %}
                      <option value="{{ st.value }}">{{ st.text }}</option>
                    {% endfor %}
                  </select>
                </div>
                <div class="col-sm-4">
                  <label>{{ entry_vendor }}</label>
                  <select id="filter-payment-vendor" class="form-control select2">
                    <option value="">{{ text_all_vendors }}</option>
                    {% for v in vendors %}
                      <option value="{{ v.supplier_id }}">{{ v.firstname }} {{ v.lastname }}</option>
                    {% endfor %}
                  </select>
                </div>
              </div>
              <br>
              <div class="row">
                <div class="col-sm-5">
                  <label>{{ entry_date_start }}</label>
                  <input type="text" id="filter-payment-date-start" class="form-control date" placeholder="{{ entry_date_start }}">
                </div>
                <div class="col-sm-5">
                  <label>{{ entry_date_end }}</label>
                  <input type="text" id="filter-payment-date-end" class="form-control date" placeholder="{{ entry_date_end }}">
                </div>
                <div class="col-sm-2">
                  <br>
                  <button type="button" id="button-filter-payment" class="btn btn-primary btn-block">
                    <i class="fa fa-filter"></i> {{ button_filter }}
                  </button>
                </div>
              </div>
            </div>

            <div class="table-responsive" style="margin-top:20px;">
              <table id="table-payment" class="table table-bordered table-hover">
                <thead>
                  <tr>
                    <th style="width:1px;" class="text-center"><input type="checkbox" id="check-all-payment"></th>
                    <th class="text-left">{{ column_payment_number }}</th>
                    <th class="text-left">{{ column_vendor }}</th>
                    <th class="text-right">{{ column_total_amount }}</th>
                    <th class="text-left">{{ column_status }}</th>
                    <th class="text-left">{{ column_date_added }}</th>
                    <th class="text-right">{{ column_action }}</th>
                  </tr>
                </thead>
                <tbody>
                  <tr><td class="text-center" colspan="7">{{ text_no_results }}</td></tr>
                </tbody>
              </table>
            </div>

            <div class="pagination-container">
              <div class="row">
                <div class="col-sm-6 text-left" id="payment-pagination"></div>
                <div class="col-sm-6 text-right" id="payment-results"></div>
              </div>
            </div>
          </div>

          <!-- مودال إضافة دفعة -->
          <div id="modal-add-payment" class="modal fade" tabindex="-1" role="dialog">
            <div class="modal-dialog modal-xl" role="document">
              <div class="modal-content">
                <form id="form-add-payment">
                  <div class="modal-header" style="background:#f7f9fb;border-bottom:1px solid #ddd;">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">{{ text_add_payment }}</h4>
                  </div>
                  <div class="modal-body">
                    <input type="hidden" name="payment_id" value="">

                    <div class="row">
                      <div class="col-sm-4">
                        <label>{{ entry_payment_number }}</label>
                        <input type="text" name="payment_number" class="form-control" placeholder="{{ entry_payment_number }}">
                      </div>
                      <div class="col-sm-4">
                        <label>{{ entry_vendor }}</label>
                        <select name="vendor_id" class="form-control select2 payment-vendor-select"></select>
                      </div>
                      <div class="col-sm-4">
                        <label>{{ entry_payment_date }}</label>
                        <input type="date" name="payment_date" class="form-control">
                      </div>
                    </div>
                    <br>
                    <div class="row">
                      <div class="col-sm-4">
                        <label>{{ entry_payment_method }}</label>
                        <select name="payment_method" class="form-control select2">
                          <option value="cash">{{ text_payment_cash }}</option>
                          <option value="check">{{ text_payment_check }}</option>
                          <option value="transfer">{{ text_payment_transfer }}</option>
                        </select>
                      </div>
                      <div class="col-sm-4">
                        <label>{{ entry_reference_number }}</label>
                        <input type="text" name="reference_number" class="form-control">
                      </div>
                      <div class="col-sm-4">
                        <label>{{ entry_amount }}</label>
                        <input type="number" name="amount" class="form-control" step="0.01" value="0">
                      </div>
                    </div>
                    <br>
                    <div class="form-group">
                      <label>{{ entry_notes }}</label>
                      <textarea name="notes" class="form-control" rows="2"></textarea>
                    </div>
                    <hr>
                    <h4>{{ text_allocated_invoices }}</h4>
                    <table class="table table-bordered" id="table-add-payment-invoices">
                      <thead>
                        <tr>
                          <th>{{ column_invoice_number }}</th>
                          <th>{{ column_amount_due }}</th>
                          <th>{{ column_amount_pay }}</th>
                          <th style="width:50px;">{{ column_action }}</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr><td colspan="4">{{ text_no_results }}</td></tr>
                      </tbody>
                      <tfoot>
                        <tr>
                          <td colspan="3"></td>
                          <td>
                            <button type="button" class="btn btn-primary" id="btn-add-invoice-payment-row">
                              <i class="fa fa-plus"></i>
                            </button>
                          </td>
                        </tr>
                      </tfoot>
                    </table>
                  </div>
                  <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_close }}</button>
                    <button type="button" class="btn btn-primary" id="btn-save-add-payment">{{ button_save }}</button>
                  </div>
                </form>
              </div>
            </div>
          </div>

          <!-- مودال عرض الدفعة -->
          <div id="modal-view-payment" class="modal fade" tabindex="-1" role="dialog">
            <div class="modal-dialog modal-lg" role="document">
              <div class="modal-content">
                <div class="modal-header" style="background:#f7f9fb;border-bottom:1px solid #ddd;">
                  <button type="button" class="close" data-dismiss="modal">&times;</button>
                  <h4 class="modal-title">{{ text_view_payment }}</h4>
                </div>
                <div class="modal-body" id="view-payment-details">
                  <!-- يتم تعبئة بيانات الدفعة عبر AJAX -->
                </div>
                <div class="modal-footer">
                  <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_close }}</button>
                </div>
              </div>
            </div>
          </div>

          <!-- مودال تعديل الدفعة -->
          <div id="modal-edit-payment" class="modal fade" tabindex="-1" role="dialog">
            <div class="modal-dialog modal-xl" role="document">
              <div class="modal-content">
                <form id="form-edit-payment">
                  <div class="modal-header" style="background:#f7f9fb;border-bottom:1px solid #ddd;">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">{{ text_edit_payment }}</h4>
                  </div>
                  <div class="modal-body">
                    <input type="hidden" name="payment_id" value="">
                    <!-- مشابه للمودال السابق -->
                  </div>
                  <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_close }}</button>
                    <button type="button" class="btn btn-primary" id="btn-save-edit-payment">{{ button_save }}</button>
                  </div>
                </form>
              </div>
            </div>
          </div>

          <!-- Approve Payment -->
          <div id="modal-approve-payment" class="modal fade" tabindex="-1" role="dialog">
            <div class="modal-dialog" role="document">
              <div class="modal-content">
                <form id="form-approve-payment">
                  <div class="modal-header" style="background:#f7f9fb;border-bottom:1px solid #ddd;">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">{{ text_approve_payment }}</h4>
                  </div>
                  <div class="modal-body">
                    <input type="hidden" name="payment_id" value="">
                    <div class="form-group">
                      <label>{{ entry_comment }}</label>
                      <textarea name="comment" class="form-control" rows="3"></textarea>
                    </div>
                  </div>
                  <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_cancel }}</button>
                    <button type="button" class="btn btn-success" id="btn-confirm-approve-payment">
                      {{ button_approve }}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>

          <!-- Reject Payment -->
          <div id="modal-reject-payment" class="modal fade" tabindex="-1" role="dialog">
            <div class="modal-dialog" role="document">
              <div class="modal-content">
                <form id="form-reject-payment">
                  <div class="modal-header" style="background:#f7f9fb;border-bottom:1px solid #ddd;">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">{{ text_reject_payment }}</h4>
                  </div>
                  <div class="modal-body">
                    <input type="hidden" name="payment_id" value="">
                    <div class="form-group">
                      <label>{{ entry_reject_reason }}</label>
                      <textarea name="reject_reason" class="form-control" rows="3"></textarea>
                    </div>
                  </div>
                  <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_cancel }}</button>
                    <button type="button" class="btn btn-danger" id="btn-confirm-reject-payment">
                      {{ button_reject }}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>

          {# -----------------------------------------------------------------
             TAB 8: Inventory
          ----------------------------------------------------------------- #}
          <div class="tab-pane" id="tab-inventory">
            <div class="filter-container">
              <div class="row">
                <div class="col-sm-3">
                  <label>{{ entry_branch }}</label>
                  <select id="filter-inv-branch" class="form-control select2">
                    <option value="">{{ text_all_branches }}</option>
                    {% for branch in branches %}
                      <option value="{{ branch.branch_id }}">{{ branch.name }}</option>
                    {% endfor %}
                  </select>
                </div>
                <div class="col-sm-3">
                  <label>{{ entry_product }}</label>
                  <input type="text" id="filter-inv-product" class="form-control" placeholder="{{ entry_product }}">
                </div>
                <div class="col-sm-3">
                  <label>{{ entry_stock_movement }}</label>
                  <select id="filter-inv-movement" class="form-control select2">
                    <option value="">{{ text_all }}</option>
                    <option value="increase">{{ text_increase }}</option>
                    <option value="decrease">{{ text_decrease }}</option>
                    <option value="transfer">{{ text_transfer }}</option>
                    <option value="purchase">{{ text_purchase }}</option>
                    <option value="sale">{{ text_sale }}</option>
                  </select>
                </div>
                <div class="col-sm-3">
                  <label>&nbsp;</label><br>
                  <button type="button" id="button-filter-inventory" class="btn btn-primary btn-block">
                    <i class="fa fa-filter"></i> {{ button_filter }}
                  </button>
                </div>
              </div>
            </div>
            <div class="table-responsive" style="margin-top:20px;">
              <table id="table-inventory" class="table table-bordered table-hover">
                <thead>
                  <tr>
                    <th>{{ column_branch }}</th>
                    <th>{{ column_product }}</th>
                    <th>{{ column_quantity }}</th>
                    <th>{{ column_unit }}</th>
                    <th>{{ column_is_consignment }}</th>
                    <th>{{ column_consignment_supplier }}</th>
                    <th>{{ column_actions }}</th>
                  </tr>
                </thead>
                <tbody>
                  <tr><td class="text-center" colspan="7">{{ text_no_results }}</td></tr>
                </tbody>
              </table>
            </div>
            <div class="pagination-container">
              <div class="row">
                <div class="col-sm-6 text-left" id="inventory-pagination"></div>
                <div class="col-sm-6 text-right" id="inventory-results"></div>
              </div>
            </div>
          </div>

          <!-- مودال عرض تفاصيل الـInventory -->
          <div id="modal-view-inventory-details" class="modal fade" tabindex="-1" role="dialog">
            <div class="modal-dialog modal-lg" role="document">
              <div class="modal-content">
                <div class="modal-header" style="background:#f7f9fb;border-bottom:1px solid #ddd;">
                  <button type="button" class="close" data-dismiss="modal">&times;</button>
                  <h4 class="modal-title">{{ text_view_inventory_details }}</h4>
                </div>
                <div class="modal-body" id="inventory-details-body">
                  <!-- تعبئة عبر الـ AJAX -->
                </div>
                <div class="modal-footer">
                  <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_close }}</button>
                </div>
              </div>
            </div>
          </div>

          {# -----------------------------------------------------------------
             TAB 9: Purchase Returns
          ----------------------------------------------------------------- #}
          <div class="tab-pane" id="tab-purchase-return">
            <div class="filter-container">
              <div class="row">
                <div class="col-sm-3">
                  <label>{{ entry_return_number }}</label>
                  <input type="text" id="filter-return-number" class="form-control" placeholder="{{ entry_return_number }}">
                </div>
                <div class="col-sm-3">
                  <label>{{ entry_vendor }}</label>
                  <select id="filter-return-vendor" class="form-control select2">
                    <option value="">{{ text_all_vendors }}</option>
                    {% for v in vendors %}
                      <option value="{{ v.supplier_id }}">{{ v.firstname }} {{ v.lastname }}</option>
                    {% endfor %}
                  </select>
                </div>
                <div class="col-sm-3">
                  <label>{{ entry_status }}</label>
                  <select id="filter-return-status" class="form-control select2">
                    <option value="">{{ text_all }}</option>
                    {% for st in return_statuses %}
                      <option value="{{ st.value }}">{{ st.text }}</option>
                    {% endfor %}
                  </select>
                </div>
                <div class="col-sm-3">
                  <br>
                  <button type="button" id="button-filter-return" class="btn btn-primary btn-block">
                    <i class="fa fa-filter"></i> {{ button_filter }}
                  </button>
                </div>
              </div>
            </div>

            <div class="table-responsive" style="margin-top:20px;">
              <table id="table-purchase-return" class="table table-bordered table-hover">
                <thead>
                  <tr>
                    <th style="width:1px;" class="text-center"><input type="checkbox" id="check-all-purchase-return"></th>
                    <th class="text-left">{{ column_return_number }}</th>
                    <th class="text-left">{{ column_vendor }}</th>
                    <th class="text-left">{{ column_status }}</th>
                    <th class="text-left">{{ column_date_added }}</th>
                    <th class="text-right">{{ column_action }}</th>
                  </tr>
                </thead>
                <tbody>
                  <tr><td class="text-center" colspan="6">{{ text_no_results }}</td></tr>
                </tbody>
              </table>
            </div>

            <div class="pagination-container">
              <div class="row">
                <div class="col-sm-6 text-left" id="purchase-return-pagination"></div>
                <div class="col-sm-6 text-right" id="purchase-return-results"></div>
              </div>
            </div>
          </div>

          <!-- مودال تعديل أو إضافةPurchase Return -->
          <div id="modal-edit-purchase-return" class="modal fade" tabindex="-1" role="dialog">
            <div class="modal-dialog modal-xl" role="document">
              <div class="modal-content">
                <div class="modal-header" style="background:#f7f9fb;border-bottom:1px solid #ddd;">
                  <button type="button" class="close" data-dismiss="modal">&times;</button>
                  <h4 class="modal-title">{{ text_edit_purchase_return }}</h4>
                </div>
                <div class="modal-body">
                  <form id="form-purchase-return">
                    <input type="hidden" name="return_id" value="">

                    <div class="row">
                      <div class="col-sm-4">
                        <label>{{ entry_return_number }}</label>
                        <input type="text" name="return_number" class="form-control" placeholder="{{ entry_return_number }}">
                      </div>
                      <div class="col-sm-4">
                        <label>{{ entry_vendor }}</label>
                        <select name="supplier_id" class="form-control select2">
                          <option value="">{{ text_select }}</option>
                          {% for sup in suppliers %}
                            <option value="{{ sup.supplier_id }}">{{ sup.name }}</option>
                          {% endfor %}
                        </select>
                      </div>
                      <div class="col-sm-4">
                        <label>{{ entry_return_date }}</label>
                        <input type="date" name="return_date" class="form-control">
                      </div>
                    </div>
                    <br>
                    <div class="row">
                      <div class="col-sm-6">
                        <label>{{ entry_purchase_order }}</label>
                        <select name="purchase_order_id" class="form-control select2">
                          <option value="">{{ text_none }}</option>
                          {% for po in purchase_orders %}
                            <option value="{{ po.po_id }}">{{ po.po_number }}</option>
                          {% endfor %}
                        </select>
                      </div>
                      <div class="col-sm-6">
                        <label>{{ entry_goods_receipt }}</label>
                        <select name="goods_receipt_id" class="form-control select2">
                          <option value="">{{ text_none }}</option>
                          {% for gr in goods_receipts %}
                            <option value="{{ gr.goods_receipt_id }}">{{ gr.gr_number }}</option>
                          {% endfor %}
                        </select>
                      </div>
                    </div>
                    <br>
                    <div class="form-group">
                      <label>{{ entry_notes }}</label>
                      <textarea name="notes" class="form-control" rows="3"></textarea>
                    </div>

                    <hr>
                    <h3>{{ text_items }}</h3>
                    <table class="table table-bordered table-hover" id="table-purchase-return-items">
                      <thead>
                        <tr>
                          <th>{{ column_product }}</th>
                          <th>{{ column_quantity }}</th>
                          <th>{{ column_unit }}</th>
                          <th>{{ column_price }}</th>
                          <th>{{ column_total }}</th>
                          <th>{{ column_reason }}</th>
                          <th>{{ column_action }}</th>
                        </tr>
                      </thead>
                      <tbody></tbody>
                      <tfoot>
                        <tr>
                          <td colspan="6"></td>
                          <td>
                            <button type="button" class="btn btn-primary" id="btn-add-purchase-return-item">
                              <i class="fa fa-plus-circle"></i>
                            </button>
                          </td>
                        </tr>
                      </tfoot>
                    </table>
                  </form>
                </div>
                <div class="modal-footer">
                  <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_cancel }}</button>
                  <button type="button" class="btn btn-primary" id="btn-save-purchase-return">{{ button_save }}</button>
                </div>
              </div>
            </div>
          </div>

          <!-- Approve Purchase Return -->
          <div id="modal-approve-purchase-return" class="modal fade" tabindex="-1" role="dialog">
            <div class="modal-dialog" role="document">
              <div class="modal-content">
                <form id="form-approve-purchase-return">
                  <div class="modal-header" style="background:#f7f9fb;border-bottom:1px solid #ddd;">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">{{ text_approve_return }}</h4>
                  </div>
                  <div class="modal-body">
                    <input type="hidden" name="return_id" value="">
                    <div class="form-group">
                      <label>{{ entry_approval_comment }}</label>
                      <textarea name="approval_comment" class="form-control" rows="3"></textarea>
                    </div>
                  </div>
                  <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_cancel }}</button>
                    <button type="button" class="btn btn-success" id="btn-confirm-approve-purchase-return">
                      {{ button_approve }}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>

          <!-- Reject Purchase Return -->
          <div id="modal-reject-purchase-return" class="modal fade" tabindex="-1" role="dialog">
            <div class="modal-dialog" role="document">
              <div class="modal-content">
                <form id="form-reject-purchase-return">
                  <div class="modal-header" style="background:#f7f9fb;border-bottom:1px solid #ddd;">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">{{ text_reject_return }}</h4>
                  </div>
                  <div class="modal-body">
                    <input type="hidden" name="return_id" value="">
                    <div class="form-group">
                      <label>{{ entry_reject_reason }}</label>
                      <textarea name="reject_reason" class="form-control" rows="3"></textarea>
                    </div>
                  </div>
                  <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_cancel }}</button>
                    <button type="button" class="btn btn-danger" id="btn-confirm-reject-purchase-return">
                      {{ button_reject }}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>

          {# -----------------------------------------------------------------
             TAB 10: Stock Adjustments
          ----------------------------------------------------------------- #}
          <div class="tab-pane" id="tab-stock-adjustment">
            <div class="filter-container">
              <div class="row">
                <div class="col-sm-4">
                  <label>{{ entry_adjustment_number }}</label>
                  <input type="text" id="filter-adjustment-number" class="form-control" placeholder="{{ entry_adjustment_number }}">
                </div>
                <div class="col-sm-4">
                  <label>{{ entry_status }}</label>
                  <select id="filter-adjustment-status" class="form-control select2">
                    <option value="">{{ text_all }}</option>
                    {% for st in adjustment_statuses %}
                      <option value="{{ st.value }}">{{ st.text }}</option>
                    {% endfor %}
                  </select>
                </div>
                <div class="col-sm-4">
                  <br>
                  <button type="button" id="button-filter-adjustment" class="btn btn-primary btn-block">
                    <i class="fa fa-filter"></i> {{ button_filter }}
                  </button>
                </div>
              </div>
            </div>

            <div class="table-responsive" style="margin-top:20px;">
              <table id="table-adjustment" class="table table-bordered table-hover">
                <thead>
                  <tr>
                    <th style="width:1px;" class="text-center"><input type="checkbox" id="check-all-adjustment"></th>
                    <th class="text-left">{{ column_adjustment_number }}</th>
                    <th class="text-left">{{ column_branch }}</th>
                    <th class="text-left">{{ column_type }}</th>
                    <th class="text-left">{{ column_status }}</th>
                    <th class="text-left">{{ column_date_added }}</th>
                    <th class="text-right">{{ column_action }}</th>
                  </tr>
                </thead>
                <tbody>
                  <tr><td class="text-center" colspan="7">{{ text_no_results }}</td></tr>
                </tbody>
              </table>
            </div>

            <div class="pagination-container">
              <div class="row">
                <div class="col-sm-6 text-left" id="adjustment-pagination"></div>
                <div class="col-sm-6 text-right" id="adjustment-results"></div>
              </div>
            </div>
          </div>

          <!-- مودال تعديل التسوية -->
          <div id="modal-edit-adjustment" class="modal fade" tabindex="-1" role="dialog">
            <div class="modal-dialog modal-xl" role="document">
              <div class="modal-content">
                <div class="modal-header" style="background:#f7f9fb;border-bottom:1px solid #ddd;">
                  <button type="button" class="close" data-dismiss="modal">&times;</button>
                  <h4 class="modal-title">{{ text_edit_stock_adjustment }}</h4>
                </div>
                <div class="modal-body">
                  <form id="form-adjustment">
                    <input type="hidden" name="adjustment_id" value="">
                    <div class="row">
                      <div class="col-sm-4">
                        <label>{{ entry_adjustment_number }}</label>
                        <input type="text" name="adjustment_number" class="form-control" placeholder="{{ entry_adjustment_number }}">
                      </div>
                      <div class="col-sm-4">
                        <label>{{ entry_branch }}</label>
                        <select name="branch_id" class="form-control select2">
                          {% for br in branches %}
                            <option value="{{ br.branch_id }}">{{ br.name }}</option>
                          {% endfor %}
                        </select>
                      </div>
                      <div class="col-sm-4">
                        <label>{{ entry_adjustment_type }}</label>
                        <select name="type" class="form-control select2">
                          <option value="increase">{{ text_increase }}</option>
                          <option value="decrease">{{ text_decrease }}</option>
                        </select>
                      </div>
                    </div>
                    <br>
                    <div class="form-group">
                      <label>{{ entry_notes }}</label>
                      <textarea name="notes" class="form-control" rows="2"></textarea>
                    </div>
                    <hr>
                    <h3>{{ text_items }}</h3>
                    <table class="table table-bordered table-hover" id="table-adjustment-items">
                      <thead>
                        <tr>
                          <th>{{ column_product }}</th>
                          <th>{{ column_quantity }}</th>
                          <th>{{ column_unit }}</th>
                          <th>{{ column_reason }}</th>
                          <th>{{ column_action }}</th>
                        </tr>
                      </thead>
                      <tbody></tbody>
                      <tfoot>
                        <tr>
                          <td colspan="4"></td>
                          <td>
                            <button type="button" class="btn btn-primary" id="btn-add-adjustment-item">
                              <i class="fa fa-plus-circle"></i>
                            </button>
                          </td>
                        </tr>
                      </tfoot>
                    </table>
                  </form>
                </div>
                <div class="modal-footer">
                  <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_cancel }}</button>
                  <button type="button" class="btn btn-primary" id="btn-save-adjustment">{{ button_save }}</button>
                </div>
              </div>
            </div>
          </div>

          <!-- Approve Adjustment -->
          <div id="modal-approve-adjustment" class="modal fade" tabindex="-1" role="dialog">
            <div class="modal-dialog" role="document">
              <div class="modal-content">
                <form id="form-approve-adjustment">
                  <div class="modal-header" style="background:#f7f9fb;border-bottom:1px solid #ddd;">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">{{ text_approve_adjustment }}</h4>
                  </div>
                  <div class="modal-body">
                    <input type="hidden" name="adjustment_id" value="">
                    <div class="form-group">
                      <label>{{ entry_comment }}</label>
                      <textarea name="comment" class="form-control" rows="3"></textarea>
                    </div>
                  </div>
                  <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_cancel }}</button>
                    <button type="button" class="btn btn-success" id="btn-confirm-approve-adjustment">{{ button_approve }}</button>
                  </div>
                </form>
              </div>
            </div>
          </div>

          <!-- Cancel Stock Adjustment -->
          <div id="modal-cancel-adjustment" class="modal fade" tabindex="-1" role="dialog">
            <div class="modal-dialog" role="document">
              <div class="modal-content">
                <form id="form-cancel-adjustment">
                  <div class="modal-header" style="background:#f7f9fb;border-bottom:1px solid #ddd;">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">{{ text_cancel_adjustment }}</h4>
                  </div>
                  <div class="modal-body">
                    <input type="hidden" name="adjustment_id" value="">
                    <div class="form-group">
                      <label>{{ entry_comment }}</label>
                      <textarea name="comment" class="form-control" rows="3"></textarea>
                    </div>
                  </div>
                  <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_cancel }}</button>
                    <button type="button" class="btn btn-danger" id="btn-confirm-cancel-adjustment">
                      {{ button_cancel_adjustment }}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>


          {# -----------------------------------------------------------------
             TAB 11: Stock Transfers
          ----------------------------------------------------------------- #}
          <!-- ======================= TAB 11: Stock Transfers ======================= -->
          <div class="tab-pane" id="tab-stock-transfer">
            <div class="filter-container">
              <div class="row">
                <div class="col-sm-3">
                  <label>{{ entry_transfer_number }}</label>
                  <input type="text" id="filter-transfer-number" class="form-control" 
                         placeholder="{{ entry_transfer_number }}">
                </div>
                <div class="col-sm-3">
                  <label>{{ entry_from_branch }}</label>
                  <select id="filter-transfer-from-branch" class="form-control select2">
                    <option value="">{{ text_all_branches }}</option>
                    {% for branch in branches %}
                      <option value="{{ branch.branch_id }}">{{ branch.name }}</option>
                    {% endfor %}
                  </select>
                </div>
                <div class="col-sm-3">
                  <label>{{ entry_to_branch }}</label>
                  <select id="filter-transfer-to-branch" class="form-control select2">
                    <option value="">{{ text_all_branches }}</option>
                    {% for branch in branches %}
                      <option value="{{ branch.branch_id }}">{{ branch.name }}</option>
                    {% endfor %}
                  </select>
                </div>
                <div class="col-sm-3">
                  <br>
                  <button type="button" id="button-filter-transfer" class="btn btn-primary btn-block">
                    <i class="fa fa-filter"></i> {{ button_filter }}
                  </button>
                </div>
              </div>
            </div>

            <div class="table-responsive" style="margin-top:20px;">
              <table id="table-transfer" class="table table-bordered table-hover">
                <thead>
                  <tr>
                    <th style="width:1px;" class="text-center">
                      <input type="checkbox" id="check-all-transfer">
                    </th>
                    <th class="text-left">{{ column_transfer_number }}</th>
                    <th class="text-left">{{ column_from_branch }}</th>
                    <th class="text-left">{{ column_to_branch }}</th>
                    <th class="text-left">{{ column_status }}</th>
                    <th class="text-left">{{ column_date_added }}</th>
                    <th class="text-right">{{ column_action }}</th>
                  </tr>
                </thead>
                <tbody>
                  <tr><td class="text-center" colspan="7">{{ text_no_results }}</td></tr>
                </tbody>
              </table>
            </div>

            <div class="pagination-container">
              <div class="row">
                <div class="col-sm-6 text-left" id="transfer-pagination"></div>
                <div class="col-sm-6 text-right" id="transfer-results"></div>
              </div>
            </div>
          </div>

          <!-- Modal: Stock Transfer -->
          <div id="modal-edit-transfer" class="modal fade" tabindex="-1" role="dialog">
            <div class="modal-dialog modal-xl" role="document">
              <div class="modal-content">
                <div class="modal-header" style="background:#f7f9fb;border-bottom:1px solid #ddd;">
                  <button type="button" class="close" data-dismiss="modal">&times;</button>
                  <h4 class="modal-title">{{ text_edit_stock_transfer }}</h4>
                </div>
                <div class="modal-body">
                  <form id="form-transfer">
                    <input type="hidden" name="transfer_id" value="">
                    <div class="row">
                      <div class="col-sm-4">
                        <label>{{ entry_transfer_number }}</label>
                        <input type="text" name="transfer_number" class="form-control" 
                               placeholder="{{ entry_transfer_number }}">
                      </div>
                      <div class="col-sm-4">
                        <label>{{ entry_from_branch }}</label>
                        <select name="from_branch_id" class="form-control select2">
                          {% for br in branches %}
                            <option value="{{ br.branch_id }}">{{ br.name }}</option>
                          {% endfor %}
                        </select>
                      </div>
                      <div class="col-sm-4">
                        <label>{{ entry_to_branch }}</label>
                        <select name="to_branch_id" class="form-control select2">
                          {% for br in branches %}
                            <option value="{{ br.branch_id }}">{{ br.name }}</option>
                          {% endfor %}
                        </select>
                      </div>
                    </div>
                    <br>
                    <div class="form-group">
                      <label>{{ entry_notes }}</label>
                      <textarea name="notes" class="form-control" rows="2"></textarea>
                    </div>
                    <hr>
                    <h3>{{ text_items }}</h3>
                    <table class="table table-bordered table-hover" id="table-transfer-items">
                      <thead>
                        <tr>
                          <th>{{ column_product }}</th>
                          <th>{{ column_quantity }}</th>
                          <th>{{ column_unit }}</th>
                          <th>{{ column_notes }}</th>
                          <th>{{ column_action }}</th>
                        </tr>
                      </thead>
                      <tbody></tbody>
                      <tfoot>
                        <tr>
                          <td colspan="4"></td>
                          <td>
                            <button type="button" class="btn btn-primary" id="btn-add-transfer-item">
                              <i class="fa fa-plus-circle"></i>
                            </button>
                          </td>
                        </tr>
                      </tfoot>
                    </table>
                  </form>
                </div>
                <div class="modal-footer">
                  <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_cancel }}</button>
                  <button type="button" class="btn btn-primary" id="btn-save-transfer">{{ button_save }}</button>
                </div>
              </div>
            </div>
          </div>

          <!-- Approve Transfer -->
          <div id="modal-approve-transfer" class="modal fade" tabindex="-1" role="dialog">
            <div class="modal-dialog" role="document">
              <div class="modal-content">
                <form id="form-approve-transfer">
                  <div class="modal-header" style="background:#f7f9fb;border-bottom:1px solid #ddd;">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">{{ text_approve_transfer }}</h4>
                  </div>
                  <div class="modal-body">
                    <input type="hidden" name="transfer_id" value="">
                    <div class="form-group">
                      <label>{{ entry_comment }}</label>
                      <textarea name="comment" class="form-control" rows="3"></textarea>
                    </div>
                  </div>
                  <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_cancel }}</button>
                    <button type="button" class="btn btn-success" id="btn-confirm-approve-transfer">
                      {{ button_approve }}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>

          <!-- Reject Transfer -->
          <div id="modal-reject-transfer" class="modal fade" tabindex="-1" role="dialog">
            <div class="modal-dialog" role="document">
              <div class="modal-content">
                <form id="form-reject-transfer">
                  <div class="modal-header" style="background:#f7f9fb;border-bottom:1px solid #ddd;">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">{{ text_reject_transfer }}</h4>
                  </div>
                  <div class="modal-body">
                    <input type="hidden" name="transfer_id" value="">
                    <div class="form-group">
                      <label>{{ entry_reject_reason }}</label>
                      <textarea name="reject_reason" class="form-control" rows="3"></textarea>
                    </div>
                  </div>
                  <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_cancel }}</button>
                    <button type="button" class="btn btn-danger" id="btn-confirm-reject-transfer">{{ button_reject }}</button>
                  </div>
                </form>
              </div>
            </div>
          </div>


          {# -----------------------------------------------------------------
             TAB 12: Quality Inspection
          ----------------------------------------------------------------- #}
          <!-- ======================= TAB 12: Quality Inspection ======================= -->
          <div class="tab-pane" id="tab-quality-inspection">
            <div class="filter-container">
              <div class="row">
                <div class="col-sm-4">
                  <label>{{ entry_inspection_number }}</label>
                  <input type="text" id="filter-inspection-number" class="form-control" 
                         placeholder="{{ entry_inspection_number }}">
                </div>
                <div class="col-sm-4">
                  <label>{{ entry_status }}</label>
                  <select id="filter-inspection-status" class="form-control select2">
                    <option value="">{{ text_all }}</option>
                    {% for st in inspection_statuses %}
                      <option value="{{ st.value }}">{{ st.text }}</option>
                    {% endfor %}
                  </select>
                </div>
                <div class="col-sm-4">
                  <br>
                  <button type="button" id="button-filter-inspection" class="btn btn-primary btn-block">
                    <i class="fa fa-filter"></i> {{ button_filter }}
                  </button>
                </div>
              </div>
            </div>

            <div class="table-responsive" style="margin-top:20px;">
              <table id="table-inspection" class="table table-bordered table-hover">
                <thead>
                  <tr>
                    <th style="width:1px;" class="text-center">
                      <input type="checkbox" id="check-all-inspection">
                    </th>
                    <th class="text-left">{{ column_inspection_number }}</th>
                    <th class="text-left">{{ column_goods_receipt_id }}</th>
                    <th class="text-left">{{ column_inspector }}</th>
                    <th class="text-left">{{ column_status }}</th>
                    <th class="text-left">{{ column_date_added }}</th>
                    <th class="text-right">{{ column_action }}</th>
                  </tr>
                </thead>
                <tbody>
                  <tr><td class="text-center" colspan="7">{{ text_no_results }}</td></tr>
                </tbody>
              </table>
            </div>

            <div class="pagination-container">
              <div class="row">
                <div class="col-sm-6 text-left" id="inspection-pagination"></div>
                <div class="col-sm-6 text-right" id="inspection-results"></div>
              </div>
            </div>
          </div>

          <!-- Modal: Quality Inspection -->
          <div id="modal-edit-inspection" class="modal fade" tabindex="-1" role="dialog">
            <div class="modal-dialog modal-xl" role="document">
              <div class="modal-content">
                <div class="modal-header" style="background:#f7f9fb;border-bottom:1px solid #ddd;">
                  <button type="button" class="close" data-dismiss="modal">&times;</button>
                  <h4 class="modal-title">{{ text_edit_quality_inspection }}</h4>
                </div>
                <div class="modal-body">
                  <form id="form-inspection">
                    <input type="hidden" name="inspection_id" value="">
                    <div class="row">
                      <div class="col-sm-4">
                        <label>{{ entry_inspection_number }}</label>
                        <input type="text" name="inspection_number" class="form-control"
                               placeholder="{{ entry_inspection_number }}">
                      </div>
                      <div class="col-sm-4">
                        <label>{{ entry_goods_receipt_id }}</label>
                        <select name="goods_receipt_id" class="form-control select2">
                          <option value="">{{ text_select }}</option>
                          {% for gr in goods_receipts %}
                            <option value="{{ gr.goods_receipt_id }}">{{ gr.gr_number }}</option>
                          {% endfor %}
                        </select>
                      </div>
                      <div class="col-sm-4">
                        <label>{{ entry_inspection_date }}</label>
                        <input type="date" name="inspection_date" class="form-control">
                      </div>
                    </div>
                    <br>
                    <div class="form-group">
                      <label>{{ entry_notes }}</label>
                      <textarea name="notes" class="form-control" rows="2"></textarea>
                    </div>
                    <hr>
                    <h3>{{ text_inspection_items }}</h3>
                    <table class="table table-bordered table-hover" id="table-inspection-items">
                      <thead>
                        <tr>
                          <th>{{ column_product }}</th>
                          <th>{{ column_quantity }}</th>
                          <th>{{ column_unit }}</th>
                          <th>{{ column_quality_result }}</th>
                          <th>{{ column_remarks }}</th>
                          <th style="width:50px;">{{ column_action }}</th>
                        </tr>
                      </thead>
                      <tbody>
                      </tbody>
                      <tfoot>
                        <tr>
                          <td colspan="5"></td>
                          <td>
                            <button type="button" class="btn btn-primary" id="btn-add-inspection-item">
                              <i class="fa fa-plus"></i>
                            </button>
                          </td>
                        </tr>
                      </tfoot>
                    </table>
                  </form>
                </div>
                <div class="modal-footer">
                  <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_cancel }}</button>
                  <button type="button" class="btn btn-primary" id="btn-save-inspection">{{ button_save }}</button>
                </div>
              </div>
            </div>
          </div>

          <!-- Example: Approve / Reject Inspection -->
          <div id="modal-approve-inspection" class="modal fade" tabindex="-1" role="dialog">
            <div class="modal-dialog" role="document">
              <div class="modal-content">
                <form id="form-approve-inspection">
                  <div class="modal-header" style="background:#f7f9fb;border-bottom:1px solid #ddd;">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">{{ text_approve_inspection }}</h4>
                  </div>
                  <div class="modal-body">
                    <input type="hidden" name="inspection_id" value="">
                    <div class="form-group">
                      <label>{{ entry_comment }}</label>
                      <textarea name="comment" class="form-control" rows="3"></textarea>
                    </div>
                  </div>
                  <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_cancel }}</button>
                    <button type="button" class="btn btn-success" id="btn-confirm-approve-inspection">
                      {{ button_approve }}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>

          <div id="modal-reject-inspection" class="modal fade" tabindex="-1" role="dialog">
            <div class="modal-dialog" role="document">
              <div class="modal-content">
                <form id="form-reject-inspection">
                  <div class="modal-header" style="background:#f7f9fb;border-bottom:1px solid #ddd;">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">{{ text_reject_inspection }}</h4>
                  </div>
                  <div class="modal-body">
                    <input type="hidden" name="inspection_id" value="">
                    <div class="form-group">
                      <label>{{ entry_reject_reason }}</label>
                      <textarea name="reject_reason" class="form-control" rows="3"></textarea>
                    </div>
                  </div>
                  <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_cancel }}</button>
                    <button type="button" class="btn btn-danger" id="btn-confirm-reject-inspection">
                      {{ button_reject }}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>

        </div> <!-- /tab-content -->
      </div> <!-- /panel-body -->
    </div> <!-- /panel -->
  </div> <!-- /container-fluid -->

  <!-- شريط تحميل/انتظار -->
  <div class="loading-overlay" id="loading-overlay">
    {{ text_loading }}
  </div>
</div> <!-- /#content -->
</div>
</div>

<!-- الكود التالي يوضع إما داخل وسم <style> في رأس الصفحة، أو في ملف CSS خارجي -->

<style>
  /* ================[ الجزء الأول: تنسيق select2 وبعض الجداول والمودال ]================ */

  .select2-container--default .select2-selection--single {
    height: 36px;
  }
  .select2-container {
    width: 100% !important;
  }
  .loading-overlay {
    position: absolute;
    top:0; 
    left:0; 
    right:0; 
    bottom:0;
    background: rgba(255,255,255,0.7);
    z-index:9999;
    display:none;
    justify-content:center;
    align-items:center;
    font-size:24px;
    font-weight:bold;
    color:#333;
  }
  .modal-lg, .modal-xl {
    width:90% !important;
    max-width:1200px;
  }
  .table thead th {
    vertical-align:middle !important;
  }
  .form-inline .form-group {
    margin-right:10px;
  }
  .dataTables_wrapper .dataTables_filter {
    float:right;
    text-align:right;
  }
  .dataTables_wrapper .dataTables_length {
    float:left;
  }
  .filter-container {
    margin-bottom:20px;
  }
  .tab-content {
    margin-top:20px;
  }
  #toast-container {
    z-index:99999;
  }
  .pagination-container {
    margin-top:15px;
  }


  /* ================[ الجزء الثاني: التنسيقات الختامية للـUI/UX ]================ */

  /* خلفية الصفحة */
  body {
    background-color: #f9fafc;
  }

  /* رأس الصفحة (Navbar أو Header) */
  .page-header {
    background-color: #f1f3f6;
    border-bottom: 1px solid #dfe3e8;
    padding: 15px;
  }
  .page-header .btn {
    margin-left: 5px;
  }
  .page-header .breadcrumb {
    margin-bottom: 0;
  }

  /* Panel */
  .panel-default {
    border-color: #ddd;
    border-radius: 4px;
  }
  .panel-heading {
    background-color: #f7f9fb;
    border-bottom: 1px solid #ddd;
    padding: 10px 15px;
  }
  .panel-title {
    font-weight: 600;
    font-size: 16px;
    color: #333;
  }

  /* الجداول */
  .table {
    background-color: #fff;
    margin-bottom: 0;
  }
  .table th {
    background-color: #f1f3f6;
    color: #333;
    font-weight: 600;
    border-bottom: 2px solid #ddd;
  }
  .table td {
    vertical-align: middle;
  }
  .table-hover tbody tr:hover {
    background-color: #f7f8fa;
  }

  /* Dropdown Actions */
  .table .btn-group .btn {
    margin-right: 0;
  }
  .table .btn-default {
    color: #333;
    background-color: #f1f1f1;
    border: 1px solid #ccc;
  }
  .dropdown-menu > li > a {
    padding: 5px 15px;
    font-size: 14px;
    color: #333;
  }
  .dropdown-menu > li > a:hover {
    background-color: #eaeaea;
    color: #000;
  }

  /* Filter Panel */
  .filter-container {
    background-color: #fff;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
  }
  .filter-container .form-control {
    margin-bottom: 10px;
  }

  /* Tabs */
  .nav-tabs > li.active > a {
    background-color: #fff;
    border-color: #ddd #ddd #fff;
    color: #333;
    font-weight: 600;
  }
  .nav-tabs > li > a {
    color: #555;
    padding: 10px 15px;
  }
  .nav-tabs > li > a:hover {
    background-color: #f9f9f9;
    border-color: #ddd;
    color: #333;
  }

  /* Panels in Dashboard (الألوان) */
  .panel.panel-primary .panel-heading {
    background-color: #337ab7;
    border-color: #337ab7;
  }
  .panel.panel-info .panel-heading {
    background-color: #5bc0de;
    border-color: #5bc0de;
  }
  .panel.panel-success .panel-heading {
    background-color: #5cb85c;
    border-color: #5cb85c;
  }
  .panel.panel-warning .panel-heading {
    background-color: #f0ad4e;
    border-color: #f0ad4e;
  }

  /* Loading overlay (خط إضافي) */
  .loading-overlay {
    font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
    letter-spacing: 1px;
  }

  /* Modals */
  .modal-content {
    border-radius: 4px;
  }
  .modal-header {
    background-color: #f7f9fb;
    border-bottom: 1px solid #ddd;
  }
  .modal-title {
    font-weight: 600;
    font-size: 16px;
  }
  .modal-footer .btn {
    min-width: 80px;
  }

  /* Select2 */
  .select2-selection {
    border: 1px solid #ccc !important;
    background-color: #fff !important;
  }
  .select2-selection__rendered {
    line-height: 34px !important;
  }
  .select2-selection__arrow {
    height: 34px !important;
  }

  /* Toastr */
  #toast-container > .toast {
    opacity: 0.95;
    border-radius: 4px;
  }
  #toast-container > .toast-success {
    background-color: #5cb85c;
  }
  #toast-container > .toast-error {
    background-color: #d9534f;
  }
  #toast-container > .toast-info {
    background-color: #5bc0de;
  }
  #toast-container > .toast-warning {
    background-color: #f0ad4e;
  }

  /* Footer (إن وجدت) */
  .footer {
    padding: 15px;
    text-align: center;
    color: #999;
    background-color: #f7f9fb;
    border-top: 1px solid #e7e7e7;
    margin-top: 20px;
  }
</style>

<script type="text/javascript">
(function($) {
    "use strict";

    //=========================[ Helper Functions ]=========================//
    function showLoading() {
        $('#loading-overlay').show();
    }
    function hideLoading() {
        $('#loading-overlay').hide();
    }
    function handleAjaxError(xhr, status, error) {
        hideLoading();
        toastr.error("{{ text_error }}: " + error);
    }

    //=========================[ On Tab Shown (autoload) ]=========================//
    // ملاحظة: هذه الأحداث تعمل عند الانتقال بين التبويبات.
    // إذا أردت تحميل بيانات جدول معيّن مباشرةً عند فتح التبويب:

    $('a[href="#tab-purchase-requisition"]').on('shown.bs.tab', function() {
        loadRequisitions(1);
    });
    $('a[href="#tab-quotation"]').on('shown.bs.tab', function() {
        loadQuotations(1);
    });
    $('a[href="#tab-purchase-order"]').on('shown.bs.tab', function() {
        loadPurchaseOrders(1);
    });
    $('a[href="#tab-goods-receipt"]').on('shown.bs.tab', function() {
        loadGoodsReceipts(1);
    });
    $('a[href="#tab-supplier-invoice"]').on('shown.bs.tab', function() {
        loadSupplierInvoices(1);
    });
    $('a[href="#tab-vendor-payment"]').on('shown.bs.tab', function() {
        loadVendorPayments(1);
    });
    $('a[href="#tab-inventory"]').on('shown.bs.tab', function() {
        loadInventory(1);
    });
    $('a[href="#tab-purchase-return"]').on('shown.bs.tab', function() {
        loadPurchaseReturns(1);
    });
    $('a[href="#tab-stock-adjustment"]').on('shown.bs.tab', function() {
        loadAdjustments(1);
    });
    $('a[href="#tab-stock-transfer"]').on('shown.bs.tab', function() {
        loadTransfers(1);
    });
    $('a[href="#tab-quality-inspection"]').on('shown.bs.tab', function() {
        loadInspections(1);
    });

    //=========================[ Dashboard Example ]=========================//
    function loadDashboard() {
        showLoading();
        let url = 'index.php?route=purchase/purchase/getDashboardData&user_token={{ user_token }}';
        let branch = $('#filter-dashboard-branch').val();
        let period = $('#filter-dashboard-period').val();
        if (branch) url += '&filter_branch=' + encodeURIComponent(branch);
        if (period) url += '&filter_period=' + encodeURIComponent(period);

        $.ajax({
            url: url,
            type: 'GET',
            dataType: 'json',
            success: function(json) {
                hideLoading();
                // ملء بعض الإحصائيات
                $('#dashboard-total-requisitions').text(json.total_requisitions || 0);
                $('#dashboard-total-quotations').text(json.total_quotations || 0);
                $('#dashboard-total-pos').text(json.total_pos || 0);
                $('#dashboard-pending-approvals').text(json.pending_approvals || 0);

                // رسم بياني باستخدام Chart.js
                if (json.chart_purchase_overview) {
                    let ctx1 = document.getElementById('purchaseOverviewChart').getContext('2d');
                    new Chart(ctx1, {
                        type: 'line',
                        data: json.chart_purchase_overview,
                        options: {}
                    });
                }
                if (json.chart_top_suppliers) {
                    let ctx2 = document.getElementById('topSuppliersChart').getContext('2d');
                    new Chart(ctx2, {
                        type: 'bar',
                        data: json.chart_top_suppliers,
                        options: {}
                    });
                }
            },
            error: handleAjaxError
        });
    }
    $('#btn-filter-dashboard').on('click', loadDashboard);
    // تحميله مباشرةً:
    loadDashboard();
    //=========================[ Requisition Tab ]=========================//

    function loadRequisitions(page = 1) {
        if(!page) page = 1;
        showLoading();

        let url = 'index.php?route=purchase/purchase/getRequisitionList&user_token={{ user_token }}&page=' + page;

        // جمع الفلاتر من عناصر الإدخال/التحديد:
        let filterId        = $('#filter-requisition-id').val()        || '';
        let filterBranch    = $('#filter-requisition-branch').val()    || '';
        let filterDept      = $('#filter-requisition-department').val()|| '';
        let filterStatus    = $('#filter-requisition-status').val()    || '';
        let filterDateStart = $('#filter-requisition-date-start').val()|| '';
        let filterDateEnd   = $('#filter-requisition-date-end').val()  || '';
        let filterUser      = $('#filter-requisition-user').val()      || '';

        // إلحاق القيم بالـURL (أو يمكنك إرسالها عبر POST بدلًا من GET):
        if (filterId)        url += '&filter_requisition_id=' + encodeURIComponent(filterId);
        if (filterBranch)    url += '&filter_branch='         + encodeURIComponent(filterBranch);
        if (filterDept)      url += '&filter_department='     + encodeURIComponent(filterDept);
        if (filterStatus)    url += '&filter_status='         + encodeURIComponent(filterStatus);
        if (filterDateStart) url += '&filter_date_start='     + encodeURIComponent(filterDateStart);
        if (filterDateEnd)   url += '&filter_date_end='       + encodeURIComponent(filterDateEnd);
        if (filterUser)      url += '&filter_user='           + encodeURIComponent(filterUser);

        // ثم نستمر بإرسال الطلب AJAX:
        $.ajax({
            url: url,
            type: 'GET',
            dataType: 'json',
            success: function(json) {
                hideLoading();

                let html = '';
                if (json.requisitions && json.requisitions.length) {
                    $.each(json.requisitions, function(i, item) {
                        html += '<tr>';
                        html += '  <td class="text-center"><input type="checkbox" value="'+ (item.requisition_id || '') +'"></td>';
                        html += '  <td>' + (item.requisition_id || '') + '</td>';
                        html += '  <td>' + (item.department_name || '') + '</td>';
                        html += '  <td>' + (item.status || '') + '</td>';
                        html += '  <td>' + (item.date_added || '') + '</td>';
                        html += '  <td class="text-right">';
                        html += '    <div class="btn-group">';
                        html += '      <button type="button" class="btn btn-sm btn-default dropdown-toggle" data-toggle="dropdown">';
                        html += '        <i class="fa fa-cog"></i> <span class="caret"></span>';
                        html += '      </button>';
                        html += '      <ul class="dropdown-menu dropdown-menu-right">';

                        // زر عرض التفاصيل
                        html += '       <li><a href="#" onclick="viewRequisition('+ item.requisition_id +')">';
                        html += '         <i class="fa fa-eye"></i> {{ button_view }}</a></li>';

                        // زر تعديل (إن كان user_purchase_requisition_edit متاح)
                        {% if user_purchase_requisition_edit %}
                        html += '       <li><a href="#" onclick="openEditRequisition('+ item.requisition_id +')">';
                        html += '         <i class="fa fa-pencil"></i> {{ button_edit }}</a></li>';
                        {% endif %}

                        // زر حذف (إن كان user_purchase_requisition_delete متاح)
                        {% if user_purchase_requisition_delete %}
                        html += '       <li><a href="#" onclick="deleteRequisition('+ item.requisition_id +')">';
                        html += '         <i class="fa fa-trash-o"></i> {{ button_delete }}</a></li>';
                        {% endif %}

                        // زر الموافقة (Approve) - إذا كانت حالته pending
                        {% if user_purchase_requisition_approve %}
                        if (item.status === 'pending') {
                            html += ' <li><a href="#" onclick="approveRequisition('+ item.requisition_id +')">';
                            html += '   <i class="fa fa-check"></i> {{ text_approve }}</a></li>';

                            // زر الرفض (Reject)
                            html += ' <li><a href="#" onclick="rejectRequisition('+ item.requisition_id +')">';
                            html += '   <i class="fa fa-ban"></i> {{ text_reject }}</a></li>';
                        }
                        {% endif %}

                        // إدارة عروض السعر لطلب الشراء
                        html += '       <li><a href="#" onclick="gotoQuotationTab('+ item.requisition_id +')">';
                        html += '         <i class="fa fa-file-text-o"></i> {{ text_manage_quotations }}</a></li>';

                        // مقارنة عروض السعر
                        html += '        <li><a href="#" onclick="openRequisitionQuotations('+ item.requisition_id +')">';
                        html += '          <i class="fa fa-balance-scale"></i> {{ text_compare_quotations }}</a></li>';

                        html += '      </ul>';
                        html += '    </div>';
                        html += '  </td>';
                        html += '</tr>';
                    });
                } else {
                    html = '<tr><td class="text-center" colspan="6">{{ text_no_results }}</td></tr>';
                }

                // تحديث الجدول والعناصر التابعة
                $('#table-requisition tbody').html(html);
                $('#requisition-pagination').html(json.pagination || '');
                $('#requisition-results').html(json.results || '');
            },
            error: handleAjaxError
        });
    }

    $('#button-filter-requisition').on('click', function(){
        loadRequisitions(1);
    });

    window.viewRequisition = function(reqId){
        showLoading();
        $.ajax({
            url: 'index.php?route=purchase/purchase/viewRequisition&user_token={{ user_token }}&requisition_id='+reqId,
            type: 'GET',
            dataType: 'html',
            success: function(html){
                hideLoading();
                $('#view-requisition-details').html(html);
                $('#modal-view-requisition').modal('show');
            },
            error: handleAjaxError
        });
    };

    // فتح مودال الإضافة
    $('#btn-open-add-requisition').on('click', function(){
        // reset form
        $('#form-add-requisition')[0].reset();
        // تفريغ الجدول
        $('#table-add-requisition-items tbody').html('<tr><td colspan="5">{{ text_no_results }}</td></tr>');
        // تهيئة الـSelect2
        $('.select2').select2();
        // عرض المودال
        $('#modal-add-requisition').modal('show');
    });

// (1) دالة تهيئة الـSelect2 للمنتجات
function initSelect2ProductAddOne($elem) {
    // هذه تهيئة لعناصر <select> محددة (بدل استهداف الكل دفعة واحدة)
    $elem.select2({
        placeholder: '{{ text_select_product }}',
        ajax: {
            url: 'index.php?route=purchase/purchase/select2Products&user_token={{ user_token }}',
            dataType: 'json',
            data: function(params) {
                return { q: params.term };
            },
            processResults: function(data) {
                return { results: data };
            }
        }
    }).on('select2:select', function(e) {
        let data = e.params.data;
        let units = data.units || [];
        let $tr = $(this).closest('tr');
        let unitSelect = $tr.find('.unit-select-add');

        unitSelect.empty();
        $.each(units, function(i, u){
            unitSelect.append('<option value="'+u.unit_id+'">'+u.name+'</option>');
        });
    });
}
// لتعقّب عدد الأسطر المضافة
let add_req_index = 0;

/**
 * دالة تهيئة الـSelect2 للمنتج الواحد.
 * - لديك مثلها سابقًا باسم initSelect2ProductAddOne أو نحو ذلك.
 */
function initSelect2ProductAddOne($elem) {
    $elem.select2({
        placeholder: '{{ text_select_product }}',
        tags: true,
        dropdownParent: $elem.parent(),
        ajax: {
            url: 'index.php?route=purchase/purchase/select2Products&user_token={{ user_token }}',
            dataType: 'json',
            delay: 300,
            data: function(params) {
                return { q: params.term };
            },
            processResults: function(data) {
                return { results: data };
            }
        }
    }).on('select2:select', function(e) {
        let data = e.params.data;
        let units = data.units || [];
        let $tr   = $(this).closest('tr');
        let unitSelect = $tr.find('.unit-select-add');

        unitSelect.empty();
        $.each(units, function(i, u) {
            unitSelect.append(`<option value="${u.unit_id}">${u.name}</option>`);
        });
    });
}

/**
 * عند الضغط على زر (إضافة سطر جديد) في المودال:
 */
$('#btn-add-row-add-requisition').on('click', function() {
    // إن وُجد سطر "لا يوجد نتائج" فنمسحه
    let hasNoRes = $('#table-add-requisition-items tbody td[colspan="5"]').length > 0;
    if (hasNoRes) {
        $('#table-add-requisition-items tbody').html('');
    }

    // ابني صفًا جديدًا يدويًا مع إدراج الفهرس الحالي (add_req_index) في أسماء الحقول
    let newRow = `
      <tr>
        <td>
          <select name="items[${add_req_index}][product_id]" 
                  class="form-control select2 product-select-add">
          </select>
        </td>
        <td>
          <input type="number" min="1" 
                 name="items[${add_req_index}][quantity]" 
                 class="form-control" value="1">
        </td>
        <td>
          <select name="items[${add_req_index}][unit_id]"
                  class="form-control select2 unit-select-add">
          </select>
        </td>
        <td>
          <input type="text" 
                 name="items[${add_req_index}][description]" 
                 class="form-control">
        </td>
        <td>
          <button type="button" class="btn btn-danger" 
                  onclick="$(this).closest('tr').remove();">
            <i class="fa fa-minus-circle"></i>
          </button>
        </td>
      </tr>
    `;

    // أضف الصف للجدول
    $('#table-add-requisition-items tbody').append(newRow);

    // 1) انتقاء الصف الجديد
    let $lastRow = $('#table-add-requisition-items tbody tr:last');

    // 2) تهيئة Select2 على المنتج بالصف الجديد
    initSelect2ProductAddOne($lastRow.find('.product-select-add'));

    // 3) لو لديك وحدات أخرى تستعمل فيها الـselect2:
    $lastRow.find('.select2').select2();

    // 4) زيادة الفهرس للصف التالي
    add_req_index++;
});




    // الموافقة على طلب الشراء
    window.approveRequisition = function(reqId){
        // يمكن فتح مودال لإدخال سبب/تعليق الموافقة إن أردت
        // أو تنفيذ موافقة مباشرة
        if(!confirm("{{ text_confirm_approve }}")) return;
        showLoading();
        $.ajax({
            url: 'index.php?route=purchase/purchase/approveRequisition&user_token={{ user_token }}&requisition_id='+reqId,
            type: 'POST',
            dataType: 'json',
            success: function(json){
                hideLoading();
                if(json.error){
                    toastr.error(json.error);
                }
                if(json.success){
                    toastr.success(json.success);
                    // إعادة تحميل القائمة
                    loadRequisitions(1);
                }
            },
            error: handleAjaxError
        });
    };

    // رفض طلب الشراء
    window.rejectRequisition = function(reqId){
        // يمكن فتح مودال لإدخال سبب الرفض
        let reason = prompt("{{ text_enter_reject_reason }}"); 
        if(reason === null) return; // user canceled
        showLoading();
        $.ajax({
            url: 'index.php?route=purchase/purchase/rejectRequisition&user_token={{ user_token }}',
            type: 'POST',
            data: { requisition_id: reqId, reason: reason },
            dataType: 'json',
            success: function(json){
                hideLoading();
                if(json.error){
                    toastr.error(json.error);
                }
                if(json.success){
                    toastr.success(json.success);
                    loadRequisitions(1);
                }
            },
            error: handleAjaxError
        });
    };

    // حفظ طلب الشراء (إضافة جديد)
    $('#btn-save-add-requisition').on('click', function(){
        showLoading();
        $.ajax({
            url: 'index.php?route=purchase/purchase/saveRequisition&user_token={{ user_token }}',
            type:'POST',
            data: $('#form-add-requisition').serialize() + '&requisition_id=0',
            dataType:'json',
            success: function(json){
                hideLoading();
                if(json.error){ 
                    toastr.error(json.error); 
                }
                if(json.success){
                    toastr.success(json.success);
                    $('#modal-add-requisition').modal('hide');
                    loadRequisitions(1);
                }
            },
            error: handleAjaxError
        });
    });
    // فتح مودال التعديل (Edit Requisition)
    window.openEditRequisition = function(reqId){
        showLoading();
        $.ajax({
            url: 'index.php?route=purchase/purchase/getRequisitionForm&user_token={{ user_token }}&requisition_id='+reqId,
            type: 'GET',
            dataType: 'json',
            success: function(json){
                hideLoading();
                // تفريغ وإعادة ملء form-edit
                $('#form-edit-requisition')[0].reset();
                $('input[name="requisition_id"]').val(json.requisition_id || '');
                $('select[name="branch_id"]').val(json.branch_id || '').trigger('change');
                $('select[name="department_id"]').val(json.department_id || '').trigger('change');
                $('input[name="required_date"]').val(json.required_date || '');
                $('select[name="priority"]').val(json.priority || 'medium').trigger('change');
                $('textarea[name="notes"]').val(json.notes || '');

                // تعبئة الأصناف:
                let html = '';
                if(json.items && json.items.length){
                    $.each(json.items, function(i, item){
                        html += '<tr>';
                        html += ' <td><select name="item_product_id[]" class="form-control select2 product-select-edit" data-selected="'+(item.product_id || '')+'"></select></td>';
                        html += ' <td><input type="number" min="1" name="item_quantity[]" class="form-control" value="'+(item.quantity || 1)+'"></td>';
                        html += ' <td><select name="item_unit_id[]" class="form-control select2 unit-select-edit"></select></td>';
                        html += ' <td><input type="text" name="item_description[]" class="form-control" value="'+(item.description || '')+'"></td>';
                        html += ' <td><button type="button" class="btn btn-danger" onclick="$(this).closest(\'tr\').remove();">';
                        html += '  <i class="fa fa-minus-circle"></i></button></td>';
                        html += '</tr>';
                    });
                } else {
                    html = '<tr><td colspan="5">{{ text_no_results }}</td></tr>';
                }

                $('#table-edit-requisition-items tbody').html(html);
                initSelect2ProductEdit();
                $('.select2').select2();

                $('#modal-edit-requisition').modal('show');
            },
            error: handleAjaxError
        });
    };

    // إضافة سطر صنف جديد في وضع التعديل (Edit Requisition)
    $('#btn-add-row-edit-requisition').on('click', function(){
        let hasNoRes = $('#table-edit-requisition-items tbody td[colspan="5"]').length > 0;
        if(hasNoRes) {
            $('#table-edit-requisition-items tbody').html('');
        }

        let tr = '';
        tr += '<tr>';
        tr += '  <td><select name="item_product_id[]" class="form-control select2 product-select-edit"></select></td>';
        tr += '  <td><input type="number" min="1" name="item_quantity[]" class="form-control" value="1"></td>';
        tr += '  <td><select name="item_unit_id[]" class="form-control select2 unit-select-edit"></select></td>';
        tr += '  <td><input type="text" name="item_description[]" class="form-control"></td>';
        tr += '  <td><button type="button" class="btn btn-danger" onclick="$(this).closest(\'tr\').remove();">';
        tr += '    <i class="fa fa-minus-circle"></i></button></td>';
        tr += '</tr>';

        $('#table-edit-requisition-items tbody').append(tr);
        initSelect2ProductEdit();
        $('.select2').select2();
    });

    // تهيئة الـSelect2 للمنتجات في وضع التعديل (Edit Requisition)
    function initSelect2ProductEdit(){
        $('.product-select-edit').select2({
            placeholder: '{{ text_select_product }}',
            ajax: {
                url: 'index.php?route=purchase/purchase/select2Products&user_token={{ user_token }}',
                dataType: 'json',
                delay: 300,
                data: function(params){
                    return { q: params.term };
                },
                processResults: function(data){
                    return { results: data };
                }
            }
        }).on('select2:select', function(e){
            let data = e.params.data;
            let units = data.units || [];
            let unitSelect = $(this).closest('tr').find('.unit-select-edit');
            unitSelect.empty();
            $.each(units, function(i, u){
                unitSelect.append('<option value="'+u.unit_id+'">'+u.name+'</option>');
            });
        }).each(function(){
            // تعبئة المنتج مسبقًا إن وجد
            let preselected = $(this).attr('data-selected') || '';
            if(preselected){
                let $this = $(this);
                $.ajax({
                    url: 'index.php?route=purchase/purchase/select2Products&user_token={{ user_token }}&product_id='+preselected,
                    dataType: 'json',
                    success: function(res){
                        if(res && res.length){
                            let item = res[0];
                            let option = new Option(item.text, item.id, true, true);
                            $this.append(option).trigger('change');
                            // ملء الوحدات
                            let units = item.units || [];
                            let unitSelect = $this.closest('tr').find('.unit-select-edit');
                            unitSelect.empty();
                            $.each(units, function(i, u){
                                unitSelect.append('<option value="'+u.unit_id+'">'+u.name+'</option>');
                            });
                        }
                    }
                });
            }
        });
    }

    // حفظ تعديلات طلب الشراء
    $('#btn-save-edit-requisition').on('click', function(){
        showLoading();
        $.ajax({
            url: 'index.php?route=purchase/purchase/saveRequisition&user_token={{ user_token }}',
            type: 'POST',
            data: $('#form-edit-requisition').serialize(),
            dataType: 'json',
            success: function(json){
                hideLoading();
                if(json.error) {
                    toastr.error(json.error);
                }
                if(json.success){
                    toastr.success(json.success);
                    $('#modal-edit-requisition').modal('hide');
                    loadRequisitions(1);
                }
            },
            error: handleAjaxError
        });
    });

    // حذف طلب الشراء
    window.deleteRequisition = function(reqId){
        if(!confirm("{{ text_confirm_delete }}")) return;
        showLoading();
        $.ajax({
            url: 'index.php?route=purchase/purchase/deleteRequisition&user_token={{ user_token }}&requisition_id='+reqId,
            type: 'POST',
            dataType: 'json',
            success: function(json){
                hideLoading();
                if(json.error){
                    toastr.error(json.error);
                }
                if(json.success){
                    toastr.success(json.success);
                    loadRequisitions(1);
                }
            },
            error: handleAjaxError
        });
    };

    // فتح مودال "مقارنة عروض السعر" الخاصة بطلب شراء معيّن
    window.openRequisitionQuotations = function(reqId) {
        showLoading();
        $.ajax({
            url: 'index.php?route=purchase/purchase/getRequisitionQuotations&user_token={{ user_token }}',
            type: 'POST',
            data: { requisition_id: reqId },
            dataType: 'json',
            success: function(json){
                hideLoading();
                if(json.error){
                    toastr.error(json.error);
                    return;
                }
                // بناء جدول المقارنة
                let html = '';
                if(json.quotations && json.quotations.length){
                    html += '<table class="table table-bordered table-hover">';
                    html += ' <thead><tr>';
                    html += '   <th>{{ column_quotation_number }}</th>';
                    html += '   <th>{{ column_vendor }}</th>';
                    html += '   <th>{{ column_total_amount }}</th>';
                    html += '   <th>{{ column_status }}</th>';
                    html += '   <th>{{ column_action }}</th>';
                    html += ' </tr></thead>';
                    html += ' <tbody>';
                    $.each(json.quotations, function(i, quo){
                        html += '<tr>';
                        html += '  <td>'+(quo.quotation_number || '')+'</td>';
                        html += '  <td>'+(quo.vendor_name || '')+'</td>';
                        html += '  <td>'+(quo.total_amount || '0')+'</td>';
                        html += '  <td>'+(quo.status || '')+'</td>';
                        html += '  <td>';
                        html += '    <button class="btn btn-sm btn-info" onclick="viewQuotation('+quo.quotation_id+')">';
                        html += '      <i class="fa fa-eye"></i> {{ button_view }}';
                        html += '    </button> ';
                        html += '    <button class="btn btn-sm btn-success" onclick="approveQuotationForRequisition('+reqId+','+quo.quotation_id+')">';
                        html += '      <i class="fa fa-check"></i> {{ text_approve }}';
                        html += '    </button>';
                        html += '  </td>';
                        html += '</tr>';
                    });
                    html += ' </tbody></table>';
                } else {
                    html = '<p>{{ text_no_quotations_found }}</p>';
                }
                $('#modal-requisition-quotations-body').html(html);
                $('#modal-requisition-quotations').modal('show');
            },
            error: handleAjaxError
        });
    };

    // اعتماد عرض سعر معيّن لطلب الشراء
    window.approveQuotationForRequisition = function(reqId, quotationId){
        if(!confirm("{{ text_confirm_approve_quotation_for_req }}")) return;
        showLoading();
        $.ajax({
            url: 'index.php?route=purchase/purchase/approveQuotationForRequisition&user_token={{ user_token }}',
            type: 'POST',
            dataType: 'json',
            data: { requisition_id: reqId, quotation_id: quotationId },
            success: function(json){
                hideLoading();
                if(json.error){
                    toastr.error(json.error);
                }
                if(json.success){
                    toastr.success(json.success);
                    // إعادة تحميل جدول المقارنة
                    openRequisitionQuotations(reqId);
                }
            },
            error: handleAjaxError
        });
    };

    // الانتقال إلى تبويب العروض (Quotations) مع وضع الـreqId في الاعتبار
    window.gotoQuotationTab = function(reqId){
        // 1) تفعيل تبويب عروض السعر
        $('a[href="#tab-quotation"]').tab('show');
        
        // 2) وضع رقم requisition_id كـ Filter (إن أردت تصفية العروض بناءً على طلب الشراء)
        $('#filter-quotation-req-id').val(reqId);

        // 3) استدعاء الدالة loadQuotations(1) بعد قليل
        setTimeout(function(){
            loadQuotations(1);
        }, 500);
    };
    // =======================[ Quotations Tab ]=======================//
    // دالة تحميل قائمة عروض السعر مع تطبيق الفلاتر والصفحة
    function loadQuotations(page = 1) {
        if (!page) page = 1;
        showLoading();

        let url = 'index.php?route=purchase/purchase/getQuotationList&user_token={{ user_token }}&page=' + page;

        // جمع الفلاتر من حقول الإدخال
        let qNumber = $('#filter-quotation-number').val() || '';
        let vendor  = $('#filter-quotation-vendor').val() || '';
        let status  = $('#filter-quotation-status').val() || '';
        let dStart  = $('#filter-quotation-date-start').val() || '';
        let dEnd    = $('#filter-quotation-date-end').val() || '';

        // ملاحظة: إذا لدينا reqId من طلب الشراء لعرض عروضه:
        //   $('#filter-quotation-req-id') حقل افتراضي لوضعه داخلياً
        //   ليس موجوداً بواجهة المستخدم لكن نستخدمه داخلياً
        let reqId   = $('#filter-quotation-req-id').val() || '';

        // إلحاق القيم بالـURL
        if (qNumber) url += '&filter_quotation_number=' + encodeURIComponent(qNumber);
        if (vendor)  url += '&filter_vendor='          + encodeURIComponent(vendor);
        if (status)  url += '&filter_status='          + encodeURIComponent(status);
        if (dStart)  url += '&filter_date_start='      + encodeURIComponent(dStart);
        if (dEnd)    url += '&filter_date_end='        + encodeURIComponent(dEnd);
        if (reqId)   url += '&filter_requisition_id='  + encodeURIComponent(reqId);

        $.ajax({
            url: url,
            type: 'GET',
            dataType: 'json',
            success: function(json) {
                hideLoading();
                let html = '';

                if (json.quotations && json.quotations.length) {
                    $.each(json.quotations, function(i, item) {
                        html += '<tr>';
                        html += '  <td class="text-center"><input type="checkbox" value="' + (item.quotation_id || '') + '"></td>';
                        html += '  <td>' + (item.quotation_number || '') + '</td>';
                        html += '  <td>' + (item.vendor_name || '') + '</td>';
                        html += '  <td class="text-right">' + (item.total_amount || '0') + '</td>';
                        html += '  <td>' + (item.status || '') + '</td>';
                        html += '  <td>' + (item.date_added || '') + '</td>';
                        html += '  <td class="text-right">';
                        html += '    <div class="btn-group">';
                        html += '      <button type="button" class="btn btn-sm btn-default dropdown-toggle" data-toggle="dropdown">';
                        html += '        <i class="fa fa-cog"></i> <span class="caret"></span>';
                        html += '      </button>';
                        html += '      <ul class="dropdown-menu dropdown-menu-right">';
                        // زر العرض View
                        html += '        <li><a href="#" onclick="viewQuotation(' + item.quotation_id + ')">';
                        html += '          <i class="fa fa-eye"></i> {{ button_view }}</a></li>';

                        // زر التعديل Edit (وفق صلاحية user_quotation_edit)
                        {% if user_quotation_edit %}
                        html += '        <li><a href="#" onclick="openEditQuotation(' + item.quotation_id + ')">';
                        html += '          <i class="fa fa-pencil"></i> {{ button_edit }}</a></li>';
                        {% endif %}

                        // زر الحذف Delete (وفق صلاحية user_quotation_delete)
                        {% if user_quotation_delete %}
                        html += '        <li><a href="#" onclick="deleteQuotation(' + item.quotation_id + ')">';
                        html += '          <i class="fa fa-trash"></i> {{ button_delete }}</a></li>';
                        {% endif %}

                        // زر الاعتماد Approve
                        html += '        <li><a href="#" onclick="approveQuotation(' + item.quotation_id + ')">';
                        html += '          <i class="fa fa-check"></i> {{ text_approve }}</a></li>';

                        // زر الرفض Reject
                        html += '        <li><a href="#" onclick="rejectQuotation(' + item.quotation_id + ')">';
                        html += '          <i class="fa fa-ban"></i> {{ text_reject }}</a></li>';

                        // زر التحويل لأمر شراء
                        html += '        <li><a href="#" onclick="convertToPO(' + item.quotation_id + ')">';
                        html += '          <i class="fa fa-shopping-cart"></i> {{ text_convert_to_po }}</a></li>';

                        html += '      </ul>';
                        html += '    </div>';
                        html += '  </td>';
                        html += '</tr>';
                    });
                } else {
                    html = '<tr><td class="text-center" colspan="7">{{ text_no_results }}</td></tr>';
                }

                $('#table-quotation tbody').html(html);
                $('#quotation-pagination').html(json.pagination || '');
                $('#quotation-results').html(json.results || '');
            },
            error: handleAjaxError
        });
    }

    // عند النقر على زر الفلترة
    $('#button-filter-quotation').on('click', function() {
        loadQuotations(1);
    });

    // لعرض بيانات عرض السعر (View Quotation)
    window.viewQuotation = function(qId) {
        showLoading();
        $.ajax({
            url: 'index.php?route=purchase/purchase/viewQuotation&user_token={{ user_token }}&quotation_id=' + qId,
            type: 'GET',
            dataType: 'html',
            success: function(html) {
                hideLoading();
                $('#view-quotation-details').html(html);
                $('#modal-view-quotation').modal('show');
            },
            error: handleAjaxError
        });
    };

    // زر فتح إضافة عرض السعر (Add Quotation)
    $('#btn-add-quotation').on('click', function() {
        // تفريغ النموذج
        $('#form-add-quotation')[0].reset();
        $('#table-add-quotation-items tbody').html('<tr><td colspan="6">{{ text_no_results }}</td></tr>');
        $('.select2').select2();

        // في حال كنا مرتبطين بطلب شراء معيّن
        if(window.currentRequisitionId) {
            $('input[name="requisition_id"]').val(window.currentRequisitionId);
        }

        $('#modal-add-quotation').modal('show');
    });

    // زر إضافة صف صنف جديد في مودال إضافة عرض السعر
    $('#btn-add-row-add-quotation').on('click', function() {
        let hasNoRes = $('#table-add-quotation-items tbody td[colspan="6"]').length > 0;
        if(hasNoRes) {
            $('#table-add-quotation-items tbody').html('');
        }
        let tr = '';
        tr += '<tr>';
        tr += ' <td><select name="item_product_id[]" class="form-control select2 product-select-q-add"></select></td>';
        tr += ' <td><input type="number" name="item_quantity[]" class="form-control" min="1" value="1"></td>';
        tr += ' <td><input type="number" name="item_unit_price[]" class="form-control" step="0.01" value="0"></td>';
        tr += ' <td><input type="number" name="item_total[]" class="form-control" step="0.01" value="0" readonly></td>';
        tr += ' <td><input type="text" name="item_description[]" class="form-control"></td>';
        tr += ' <td><button type="button" class="btn btn-danger" onclick="$(this).closest(\'tr\').remove();">';
        tr += '  <i class="fa fa-minus-circle"></i></button></td>';
        tr += '</tr>';

        $('#table-add-quotation-items tbody').append(tr);
        initSelect2ProductQuotationAdd();
        $('.select2').select2();
    });

    // تهيئة الـSelect2 للمنتجات في مودال (Add Quotation)
    function initSelect2ProductQuotationAdd() {
        $('.product-select-q-add').select2({
            placeholder: '{{ text_select_product }}',
            ajax: {
                url: 'index.php?route=purchase/purchase/select2Products&user_token={{ user_token }}',
                dataType: 'json',
                delay: 300,
                data: function(params) {
                    return { q: params.term };
                },
                processResults: function(data) {
                    return { results: data };
                }
            }
        }).on('select2:select', function(e) {
            let productData = e.params.data;
            let $tr = $(this).closest('tr');
            // تعبئة الوحدات إن أردت
            let units = productData.units || [];
            let $unitSelect = $tr.find('select[name="item_unit_id[]"]');
            if($unitSelect.length) {
                $unitSelect.empty();
                $.each(units, function(i, un) {
                    $unitSelect.append('<option value="'+un.unit_id+'">'+un.name+'</option>');
                });
            }
            // تعبئة سعر افتراضي...الخ
        });
    }

    // حفظ عرض السعر الجديد
    $('#btn-save-add-quotation').on('click', function() {
        showLoading();
        $.ajax({
            url: 'index.php?route=purchase/purchase/saveQuotation&user_token={{ user_token }}',
            type: 'POST',
            data: $('#form-add-quotation').serialize() + '&quotation_id=0',
            dataType: 'json',
            success: function(json) {
                hideLoading();
                if(json.error) {
                    toastr.error(json.error);
                }
                if(json.success){
                    toastr.success(json.success);
                    $('#modal-add-quotation').modal('hide');
                    loadQuotations(1);
                }
            },
            error: handleAjaxError
        });
    });

    // زر فتح مودال التعديل (Edit Quotation)
    window.openEditQuotation = function(qId) {
        showLoading();
        $.ajax({
            url: 'index.php?route=purchase/purchase/getQuotationForm&user_token={{ user_token }}&quotation_id=' + qId,
            type: 'GET',
            dataType: 'json',
            success: function(json) {
                hideLoading();
                $('#form-edit-quotation')[0].reset();
                $('input[name="quotation_id"]').val(json.quotation_id || '');
                $('input[name="quotation_number"]').val(json.quotation_number || '');
                $('select[name="vendor_id"]').val(json.vendor_id || '').trigger('change');
                $('input[name="validity_date"]').val(json.validity_date || '');
                $('textarea[name="notes"]').val(json.notes || '');

                // تعبئة أصناف العرض
                let html = '';
                if(json.items && json.items.length){
                    $.each(json.items, function(i, item){
                        html += '<tr>';
                        html += ' <td><select name="item_product_id[]" class="form-control select2 product-select-q-edit" data-prod="'+ (item.product_id || '') +'"></select></td>';
                        html += ' <td><input type="number" name="item_quantity[]" class="form-control" value="'+ (item.quantity || 1) +'"></td>';
                        html += ' <td><input type="number" name="item_unit_price[]" class="form-control" step="0.01" value="'+ (item.unit_price || 0) +'"></td>';
                        html += ' <td><input type="number" name="item_total[]" class="form-control" step="0.01" value="'+ (item.total || 0) +'" readonly></td>';
                        html += ' <td><input type="text" name="item_description[]" class="form-control" value="'+ (item.description || '') +'"></td>';
                        html += ' <td><button type="button" class="btn btn-danger" onclick="$(this).closest(\'tr\').remove();">';
                        html += '   <i class="fa fa-minus-circle"></i></button></td>';
                        html += '</tr>';
                    });
                } else {
                    html = '<tr><td colspan="6">{{ text_no_results }}</td></tr>';
                }

                $('#table-edit-quotation-items tbody').html(html);
                initSelect2ProductQuotationEdit();
                $('.select2').select2();

                $('#modal-edit-quotation').modal('show');
            },
            error: handleAjaxError
        });
    };
    // زر إضافة صف جديد في التعديل (Edit Quotation)
    $('#btn-add-row-edit-quotation').on('click', function() {
        let hasNoRes = $('#table-edit-quotation-items tbody td[colspan="6"]').length > 0;
        if(hasNoRes) {
            $('#table-edit-quotation-items tbody').html('');
        }
        let tr = '';
        tr += '<tr>';
        tr += ' <td><select name="item_product_id[]" class="form-control select2 product-select-q-edit"></select></td>';
        tr += ' <td><input type="number" name="item_quantity[]" class="form-control" min="1" value="1"></td>';
        tr += ' <td><input type="number" name="item_unit_price[]" class="form-control" step="0.01" value="0"></td>';
        tr += ' <td><input type="number" name="item_total[]" class="form-control" step="0.01" value="0" readonly></td>';
        tr += ' <td><input type="text" name="item_description[]" class="form-control" value=""></td>';
        tr += ' <td><button type="button" class="btn btn-danger" onclick="$(this).closest(\'tr\').remove();">';
        tr += '   <i class="fa fa-minus-circle"></i></button></td>';
        tr += '</tr>';

        $('#table-edit-quotation-items tbody').append(tr);
        initSelect2ProductQuotationEdit();
        $('.select2').select2();
    });

    // تهيئة الـSelect2 في وضع تعديل (Edit Quotation)
    function initSelect2ProductQuotationEdit() {
        $('.product-select-q-edit').select2({
            placeholder: '{{ text_select_product }}',
            ajax: {
                url: 'index.php?route=purchase/purchase/select2Products&user_token={{ user_token }}',
                dataType: 'json',
                delay: 300,
                data: function(params) {
                    return { q: params.term };
                },
                processResults: function(data) {
                    return { results: data };
                }
            }
        })
        .on('select2:select', function(e) {
            let data = e.params.data;
            let units = data.units || [];
            let $tr = $(this).closest('tr');
            let $unitSelect = $tr.find('select[name="item_unit_id[]"]');
            $unitSelect.empty();
            $.each(units, function(i, un) {
                $unitSelect.append('<option value="' + un.unit_id + '">' + un.name + '</option>');
            });
        })
        .each(function() {
            // في حال كانت هناك قيمة محددة مسبقًا:
            let preselected = $(this).attr('data-prod') || '';
            if(preselected) {
                let $this = $(this);
                $.ajax({
                    url: 'index.php?route=purchase/purchase/select2Products&user_token={{ user_token }}&product_id=' + preselected,
                    dataType: 'json',
                    success: function(res) {
                        if(res && res.length) {
                            let item = res[0];
                            let option = new Option(item.text, item.id, true, true);
                            $this.append(option).trigger('change');

                            // ملء الوحدات:
                            let units = item.units || [];
                            let $unitSelect = $this.closest('tr').find('select[name="item_unit_id[]"]');
                            $unitSelect.empty();
                            $.each(units, function(i, un) {
                                $unitSelect.append('<option value="' + un.unit_id + '">' + un.name + '</option>');
                            });
                        }
                    }
                });
            }
        });
    }

    // حفظ التعديل على عرض السعر
    $('#btn-save-edit-quotation').on('click', function() {
        showLoading();
        $.ajax({
            url: 'index.php?route=purchase/purchase/saveQuotation&user_token={{ user_token }}',
            type: 'POST',
            data: $('#form-edit-quotation').serialize(),
            dataType: 'json',
            success: function(json) {
                hideLoading();
                if(json.error) {
                    toastr.error(json.error);
                }
                if(json.success) {
                    toastr.success(json.success);
                    $('#modal-edit-quotation').modal('hide');
                    loadQuotations(1);
                }
            },
            error: handleAjaxError
        });
    });

    // حذف عرض السعر
    window.deleteQuotation = function(qId) {
        if(!confirm("{{ text_confirm_delete }}")) return;
        showLoading();
        $.ajax({
            url: 'index.php?route=purchase/purchase/deleteQuotation&user_token={{ user_token }}&quotation_id=' + qId,
            type: 'POST',
            dataType: 'json',
            success: function(json) {
                hideLoading();
                if(json.error) {
                    toastr.error(json.error);
                }
                if(json.success) {
                    toastr.success(json.success);
                    loadQuotations(1);
                }
            },
            error: handleAjaxError
        });
    };

    // زر اعتماد عرض السعر (Approve Quotation)
    window.approveQuotation = function(qId) {
        // فتح مودال التعليق إن أردنا
        $('#modal-approve-quotation input[name="quotation_id"]').val(qId);
        $('#modal-approve-quotation').modal('show');
    };

    // زر تأكيد الموافقة بعـد تعبئة الـComment
    $('#btn-confirm-approve-quotation').on('click', function() {
        showLoading();
        $.ajax({
            url: 'index.php?route=purchase/purchase/approveQuotation&user_token={{ user_token }}',
            type: 'POST',
            data: $('#form-approve-quotation').serialize(),
            dataType: 'json',
            success: function(json) {
                hideLoading();
                if(json.error) {
                    toastr.error(json.error);
                }
                if(json.success) {
                    toastr.success(json.success);
                    $('#modal-approve-quotation').modal('hide');
                    loadQuotations(1);
                }
            },
            error: handleAjaxError
        });
    });

    // زر رفض عرض السعر (Reject Quotation)
    window.rejectQuotation = function(qId) {
        $('#modal-reject-quotation input[name="quotation_id"]').val(qId);
        $('#modal-reject-quotation').modal('show');
    };

    // تأكيد رفض عرض السعر
    $('#btn-confirm-reject-quotation').on('click', function() {
        showLoading();
        $.ajax({
            url: 'index.php?route=purchase/purchase/rejectQuotation&user_token={{ user_token }}',
            type: 'POST',
            data: $('#form-reject-quotation').serialize(),
            dataType: 'json',
            success: function(json) {
                hideLoading();
                if(json.error) {
                    toastr.error(json.error);
                }
                if(json.success) {
                    toastr.success(json.success);
                    $('#modal-reject-quotation').modal('hide');
                    loadQuotations(1);
                }
            },
            error: handleAjaxError
        });
    });

    // تحويل عرض السعر إلى أمر شراء
    window.convertToPO = function(qId) {
        if(!confirm("{{ text_confirm_convert_to_po }}")) return;
        showLoading();
        $.ajax({
            url: 'index.php?route=purchase/purchase/convertQuotationToPO&user_token={{ user_token }}&quotation_id=' + qId,
            type: 'POST',
            dataType: 'json',
            success: function(json) {
                hideLoading();
                if(json.error) {
                    toastr.error(json.error);
                }
                if(json.success) {
                    toastr.success(json.success);
                    // الانتقال إلى تبويب أوامر الشراء
                    $('a[href="#tab-purchase-order"]').tab('show');
                    loadPurchaseOrders(1);
                }
            },
            error: handleAjaxError
        });
    };

    // زر "مقارنة عروض السعر" لو أردنا مقارنة يدوية (في حال احتجنا تحديد أكثر من عرض)
    $('#btn-compare-quotations').on('click', function() {
        // جلب العروض المختارة
        let selected = [];
        $('#table-quotation tbody input[type="checkbox"]:checked').each(function(){
            selected.push($(this).val());
        });
        if(selected.length < 2) {
            toastr.warning("{{ text_select_at_least_two_to_compare }}");
            return;
        }
        // هنا يمكن فتح مودال خاص بالمقارنة أو إرسال IDs للسيرفر
        toastr.info('Comparison feature is under construction...');
    });
    //=========================[ Purchase Orders ]=========================//
    function loadPurchaseOrders(page=1) {
        showLoading();
        let url = 'index.php?route=purchase/purchase/getPOList&user_token={{ user_token }}&page=' + page;

        let poNumber  = $('#filter-po-number').val()   || '';
        let status    = $('#filter-po-status').val()   || '';
        let vendor    = $('#filter-po-vendor').val()   || '';
        let dateStart = $('#filter-po-date-start').val()|| '';
        let dateEnd   = $('#filter-po-date-end').val()  || '';

        if(poNumber)  url += '&filter_po_number='   + encodeURIComponent(poNumber);
        if(status)    url += '&filter_status='      + encodeURIComponent(status);
        if(vendor)    url += '&filter_vendor='      + encodeURIComponent(vendor);
        if(dateStart) url += '&filter_date_start='  + encodeURIComponent(dateStart);
        if(dateEnd)   url += '&filter_date_end='    + encodeURIComponent(dateEnd);

        $.ajax({
            url: url,
            dataType: 'json',
            success: function(json) {
                hideLoading();
                let html = '';
                if(json.pos && json.pos.length) {
                    $.each(json.pos, function(i, po) {
                        html += '<tr>';
                        html += '  <td class="text-center"><input type="checkbox" value="' + po.po_id + '"></td>';
                        html += '  <td>' + (po.po_number || '') + '</td>';
                        html += '  <td>' + (po.status || '') + '</td>';
                        html += '  <td>' + (po.vendor_name || '') + '</td>';
                        html += '  <td class="text-right">' + (po.total_amount || '0') + '</td>';
                        html += '  <td>' + (po.date_added || '') + '</td>';
                        html += '  <td class="text-right">';
                        html += '    <div class="btn-group">';
                        html += '      <button type="button" class="btn btn-sm btn-default dropdown-toggle" data-toggle="dropdown">';
                        html += '        <i class="fa fa-cog"></i> <span class="caret"></span>';
                        html += '      </button>';
                        html += '      <ul class="dropdown-menu dropdown-menu-right">';
                        // زر العرض View
                        html += '        <li><a href="#" onclick="viewPO(' + po.po_id + ')">';
                        html += '          <i class="fa fa-eye"></i> {{ button_view }}</a></li>';
                        // زر التعديل Edit (إن كانت الصلاحيات متوفرة)
                        {% if user_purchase_order_edit %}
                        html += '        <li><a href="#" onclick="openEditPO(' + po.po_id + ')">';
                        html += '          <i class="fa fa-pencil"></i> {{ button_edit }}</a></li>';
                        {% endif %}
                        // زر الحذف Delete
                        {% if user_purchase_order_delete %}
                        html += '        <li><a href="#" onclick="deletePO(' + po.po_id + ')">';
                        html += '          <i class="fa fa-trash"></i> {{ button_delete }}</a></li>';
                        {% endif %}
                        // زر الاعتماد Approve
                        html += '        <li><a href="#" onclick="approvePO(' + po.po_id + ')">';
                        html += '          <i class="fa fa-check"></i> {{ text_approve }}</a></li>';
                        // زر الرفض Reject
                        html += '        <li><a href="#" onclick="rejectPO(' + po.po_id + ')">';
                        html += '          <i class="fa fa-ban"></i> {{ text_reject }}</a></li>';
                        // زر إنشاء مستندات تالية مثل GR أو Invoice
                        html += '        <li><a href="#" onclick="createGR(' + po.po_id + ')">';
                        html += '          <i class="fa fa-truck"></i> {{ text_goods_receipt }}</a></li>';
                        html += '        <li><a href="#" onclick="createInvoice(' + po.po_id + ')">';
                        html += '          <i class="fa fa-file-text"></i> {{ text_invoice }}</a></li>';
                        html += '      </ul>';
                        html += '    </div>';
                        html += '  </td>';
                        html += '</tr>';
                    });
                } else {
                    html = '<tr><td class="text-center" colspan="7">{{ text_no_results }}</td></tr>';
                }
                $('#table-po tbody').html(html);
                $('#po-pagination').html(json.pagination || '');
                $('#po-results').html(json.results || '');
            },
            error: handleAjaxError
        });
    }

    // تشغيل الفلترة
    $('#button-filter-po').on('click', function() {
        loadPurchaseOrders(1);
    });

    // زر إضافة أمر شراء جديد
    $('#btn-add-po').on('click', function() {
        // إعادة ضبط الفورم
        $('#form-add-po')[0].reset();
        $('#table-add-po-items tbody').html('<tr><td colspan="6">{{ text_no_results }}</td></tr>');
        $('.select2').select2();
        initSelect2Vendor('.po-vendor-select');
        $('#modal-add-po').modal('show');
    });

    // إضافة سطر item جديد في إضافة أمر الشراء
    $('#btn-add-row-add-po').on('click', function() {
        let hasNoRes = $('#table-add-po-items tbody td[colspan="6"]').length > 0;
        if(hasNoRes) {
            $('#table-add-po-items tbody').html('');
        }
        let row = '';
        row += '<tr>';
        row += ' <td><select name="item_product_id[]" class="form-control select2 product-select-po-add" style="width:100%;"></select></td>';
        row += ' <td><input type="number" name="item_quantity[]" class="form-control" min="1" value="1"></td>';
        row += ' <td><select name="item_unit_id[]" class="form-control select2 product-unit-po-add"></select></td>';
        row += ' <td><input type="number" name="item_price[]" class="form-control" step="0.01" value="0"></td>';
        row += ' <td><input type="number" name="item_total[]" class="form-control" step="0.01" value="0" readonly></td>';
        row += ' <td><button type="button" class="btn btn-danger" onclick="$(this).closest(\'tr\').remove();">';
        row += '   <i class="fa fa-minus-circle"></i></button></td>';
        row += '</tr>';
        $('#table-add-po-items tbody').append(row);
        initSelect2ProductAddPO();
        $('.select2').select2();
    });


    // تهيئة select2 للمنتجات في نموذج إضافة PO
    function initSelect2ProductAddPO() {
        $('.product-select-po-add').select2({
            placeholder: '{{ text_select_product }}',
            ajax: {
                url: 'index.php?route=purchase/purchase/select2Products&user_token={{ user_token }}',
                dataType: 'json',
                delay: 300,
                data: function(params) {
                    return { q: params.term };
                },
                processResults: function(data) {
                    return { results: data };
                }
            }
        }).on('select2:select', function(e) {
            // عند اختيار المنتج املأ قائمة الوحدات لو لزم
            let data = e.params.data;
            let units = data.units || [];
            let $tr = $(this).closest('tr');
            let unitSelect = $tr.find('.product-unit-po-add');
            unitSelect.empty();
            $.each(units, function(i, un) {
                unitSelect.append('<option value="' + un.unit_id + '">' + un.name + '</option>');
            });
        });
    }

    // حفظ عملية إضافة أمر الشراء
    $('#btn-save-add-po').on('click', function() {
        showLoading();
        $.ajax({
            url: 'index.php?route=purchase/purchase/savePurchaseOrder&user_token={{ user_token }}',
            type: 'POST',
            data: $('#form-add-po').serialize() + '&po_id=0',
            dataType: 'json',
            success: function(json) {
                hideLoading();
                if(json.error) {
                    toastr.error(json.error);
                }
                if(json.success) {
                    toastr.success(json.success);
                    $('#modal-add-po').modal('hide');
                    loadPurchaseOrders(1);
                }
            },
            error: handleAjaxError
        });
    });

    // فتح نافذة التعديل لأمر الشراء
    window.openEditPO = function(po_id) {
        showLoading();
        $.ajax({
            url: 'index.php?route=purchase/purchase/getPurchaseOrderForm&user_token={{ user_token }}&po_id=' + po_id,
            type: 'GET',
            dataType: 'json',
            success: function(json) {
                hideLoading();
                $('#form-edit-po')[0].reset();
                $('input[name="po_id"]').val(json.po_id || '');
                $('input[name="po_number"]').val(json.po_number || '');
                $('input[name="order_date"]').val(json.order_date || '');
                $('input[name="expected_delivery_date"]').val(json.expected_delivery_date || '');
                $('select[name="status"]').val(json.status || 'draft').trigger('change');
                $('input[name="terms_conditions"]').val(json.terms_conditions || '');
                $('textarea[name="notes"]').val(json.notes || '');

                // تهيئة الـVendor:
                initSelect2Vendor('.po-vendor-select-edit');
                let $vendor = $('.po-vendor-select-edit');
                if(json.vendor_id) {
                    $.ajax({
                        url: 'index.php?route=purchase/purchase/select2Vendors&user_token={{ user_token }}&vendor_id=' + json.vendor_id,
                        dataType: 'json',
                        success: function(res) {
                            if(res && res.length) {
                                let vend = res[0];
                                let option = new Option(vend.text, vend.id, true, true);
                                $vendor.append(option).trigger('change');
                            }
                        }
                    });
                }

                // تعبئة سطور الـitems
                let html = '';
                if(json.items && json.items.length) {
                    $.each(json.items, function(i, item) {
                        html += '<tr>';
                        html += ' <td><select name="item_product_id[]" class="form-control select2 product-select-po-edit" ';
                        html += '     style="width:100%;" data-prod="' + (item.product_id || '') + '"></select></td>';
                        html += ' <td><input type="number" name="item_quantity[]" class="form-control" ';
                        html += '     value="' + (item.quantity || 1) + '"></td>';
                        html += ' <td><select name="item_unit_id[]" class="form-control select2 product-unit-po-edit"></select></td>';
                        html += ' <td><input type="number" name="item_price[]" class="form-control" step="0.01" ';
                        html += '     value="' + (item.price || 0) + '"></td>';
                        html += ' <td><input type="number" name="item_total[]" class="form-control" step="0.01" ';
                        html += '     value="' + (item.total || 0) + '" readonly></td>';
                        html += ' <td><button type="button" class="btn btn-danger" ';
                        html += '     onclick="$(this).closest(\'tr\').remove();"><i class="fa fa-minus-circle"></i></button></td>';
                        html += '</tr>';
                    });
                } else {
                    html = '<tr><td colspan="6">{{ text_no_results }}</td></tr>';
                }
                $('#table-edit-po-items tbody').html(html);
                initSelect2ProductEditPO();
                $('.select2').select2();

                // عرض المودال
                $('#modal-edit-po').modal('show');
            },
            error: handleAjaxError
        });
    };

    // زر إضافة صف جديد في تعديل PO
    $('#btn-add-row-edit-po').on('click', function() {
        let hasNoRes = $('#table-edit-po-items tbody td[colspan="6"]').length > 0;
        if(hasNoRes) {
            $('#table-edit-po-items tbody').html('');
        }
        let row = '';
        row += '<tr>';
        row += ' <td><select name="item_product_id[]" class="form-control select2 product-select-po-edit" ';
        row += '     data-prod="" style="width:100%;"></select></td>';
        row += ' <td><input type="number" name="item_quantity[]" class="form-control" min="1" value="1"></td>';
        row += ' <td><select name="item_unit_id[]" class="form-control select2 product-unit-po-edit"></select></td>';
        row += ' <td><input type="number" name="item_price[]" class="form-control" step="0.01" value="0"></td>';
        row += ' <td><input type="number" name="item_total[]" class="form-control" step="0.01" value="0" readonly></td>';
        row += ' <td><button type="button" class="btn btn-danger" onclick="$(this).closest(\'tr\').remove();">';
        row += '   <i class="fa fa-minus-circle"></i></button></td>';
        row += '</tr>';

        $('#table-edit-po-items tbody').append(row);
        initSelect2ProductEditPO();
        $('.select2').select2();
    });

    // تهيئة المنتجات في تعديل PO
    function initSelect2ProductEditPO() {
        $('.product-select-po-edit').select2({
            placeholder: '{{ text_select_product }}',
            ajax: {
                url: 'index.php?route=purchase/purchase/select2Products&user_token={{ user_token }}',
                dataType: 'json',
                delay: 300,
                data: function(params) {
                    return { q: params.term };
                },
                processResults: function(data) {
                    return { results: data };
                }
            }
        }).on('select2:select', function(e) {
            let data = e.params.data;
            let units = data.units || [];
            let $tr = $(this).closest('tr');
            let unitSelect = $tr.find('.product-unit-po-edit');
            unitSelect.empty();
            $.each(units, function(i, un) {
                unitSelect.append('<option value="' + un.unit_id + '">' + un.name + '</option>');
            });
        }).each(function() {
            // تعبئة المنتج إن كان موجود
            let preProd = $(this).attr('data-prod') || '';
            if(preProd) {
                let $this = $(this);
                $.ajax({
                    url: 'index.php?route=purchase/purchase/select2Products&user_token={{ user_token }}&product_id=' + preProd,
                    dataType: 'json',
                    success: function(res) {
                        if(res && res.length) {
                            let item = res[0];
                            let option = new Option(item.text, item.id, true, true);
                            $this.append(option).trigger('change');
                            let units = item.units || [];
                            let unitSelect = $this.closest('tr').find('.product-unit-po-edit');
                            unitSelect.empty();
                            $.each(units, function(i, un) {
                                unitSelect.append('<option value="' + un.unit_id + '">' + un.name + '</option>');
                            });
                        }
                    }
                });
            }
        });
    }

    // حفظ التعديل
    $('#btn-save-edit-po').on('click', function() {
        showLoading();
        $.ajax({
            url: 'index.php?route=purchase/purchase/savePurchaseOrder&user_token={{ user_token }}',
            type: 'POST',
            data: $('#form-edit-po').serialize(),
            dataType: 'json',
            success: function(json) {
                hideLoading();
                if(json.error) {
                    toastr.error(json.error);
                }
                if(json.success) {
                    toastr.success(json.success);
                    $('#modal-edit-po').modal('hide');
                    loadPurchaseOrders(1);
                }
            },
            error: handleAjaxError
        });
    });

    // عرض تفاصيل أمر الشراء
    window.viewPO = function(po_id) {
        showLoading();
        $.ajax({
            url: 'index.php?route=purchase/purchase/viewPurchaseOrder&user_token={{ user_token }}&po_id=' + po_id,
            type: 'GET',
            dataType: 'html',
            success: function(html) {
                hideLoading();
                $('#view-po-details').html(html);
                $('#modal-view-po').modal('show');
            },
            error: handleAjaxError
        });
    };

    // حذف أمر الشراء
    window.deletePO = function(po_id) {
        if(!confirm("{{ text_confirm_delete }}")) return;
        showLoading();
        $.ajax({
            url: 'index.php?route=purchase/purchase/deletePurchaseOrder&user_token={{ user_token }}&po_id=' + po_id,
            type: 'POST',
            dataType: 'json',
            success: function(json) {
                hideLoading();
                if(json.error) {
                    toastr.error(json.error);
                }
                if(json.success) {
                    toastr.success(json.success);
                    loadPurchaseOrders(1);
                }
            },
            error: handleAjaxError
        });
    };

    // اعتماد أمر الشراء
    window.approvePO = function(po_id) {
        $('#modal-approve-po input[name="po_id"]').val(po_id);
        $('#modal-approve-po').modal('show');
    };

    $('#btn-confirm-approve-po').on('click', function() {
        showLoading();
        $.ajax({
            url: 'index.php?route=purchase/purchase/approvePurchaseOrder&user_token={{ user_token }}',
            type: 'POST',
            data: $('#form-approve-po').serialize(),
            dataType: 'json',
            success: function(json) {
                hideLoading();
                if(json.error) {
                    toastr.error(json.error);
                }
                if(json.success) {
                    toastr.success(json.success);
                    $('#modal-approve-po').modal('hide');
                    loadPurchaseOrders(1);
                }
            },
            error: handleAjaxError
        });
    });

    // رفض أمر الشراء
    window.rejectPO = function(po_id) {
        $('#modal-reject-po input[name="po_id"]').val(po_id);
        $('#modal-reject-po').modal('show');
    };

    $('#btn-confirm-reject-po').on('click', function() {
        showLoading();
        $.ajax({
            url: 'index.php?route=purchase/purchase/rejectPurchaseOrder&user_token={{ user_token }}',
            type: 'POST',
            data: $('#form-reject-po').serialize(),
            dataType: 'json',
            success: function(json) {
                hideLoading();
                if(json.error) {
                    toastr.error(json.error);
                }
                if(json.success) {
                    toastr.success(json.success);
                    $('#modal-reject-po').modal('hide');
                    loadPurchaseOrders(1);
                }
            },
            error: handleAjaxError
        });
    });

    // إنشاء Goods Receipt من أمر الشراء
    window.createGR = function(po_id) {
        // مثال: ننتقل لتبويب GR أو نفتح مودال إضافة GR ونحقن فيه الـpo_id
        $('a[href="#tab-goods-receipt"]').tab('show');
        // أو نسمي دالة openAddGoodsReceipt(po_id) .. إلخ
    };

    // إنشاء Invoice من أمر الشراء
    window.createInvoice = function(po_id) {
        // مثال: ننتقل إلى تبويب الفواتير
        $('a[href="#tab-supplier-invoice"]').tab('show');
        // أو openAddInvoice(po_id) ...
    };


$('#button-filter-po').on('click', function() {
    loadPurchaseOrders(1);
});

// عند الضغط على زر إضافة أمر شراء
$('#btn-add-po').on('click', function() {
    $('#form-add-po')[0].reset();
    $('#table-add-po-items tbody').html('<tr><td colspan="6">{{ text_no_results }}</td></tr>');
    $('.select2').select2();
    initSelect2Vendor('.po-vendor-select'); // استدعاء الدالة لتهيئة select2 للبايع/المورد
    $('#modal-add-po').modal('show');
});

// إضافة صف جديد في مودال الإضافة
$('#btn-add-row-add-po').on('click', function() {
    let hasNoRes = $('#table-add-po-items tbody td[colspan="6"]').length > 0;
    if(hasNoRes) {
        $('#table-add-po-items tbody').html('');
    }
    let row = '';
    row += '<tr>';
    row += ' <td><select name="item_product_id[]" class="form-control select2 product-select-po-add" style="width:100%;"></select></td>';
    row += ' <td><input type="number" name="item_quantity[]" class="form-control" min="1" value="1"></td>';
    row += ' <td><select name="item_unit_id[]" class="form-control select2 product-unit-po-add"></select></td>';
    row += ' <td><input type="number" name="item_price[]" class="form-control" step="0.01" value="0"></td>';
    row += ' <td><input type="number" name="item_total[]" class="form-control" step="0.01" value="0" readonly></td>';
    row += ' <td><button type="button" class="btn btn-danger" onclick="$(this).closest(\'tr\').remove();">';
    row += '   <i class="fa fa-minus-circle"></i></button></td>';
    row += '</tr>';

    $('#table-add-po-items tbody').append(row);
    initSelect2ProductAddPO(); // استدعاء الدالة لتهيئة السلكت2 للمنتجات
    $('.select2').select2();
});

// دالة تهيئة الـSelect2 للمنتجات في وضع الإضافة (Add PO)
function initSelect2ProductAddPO() {
    $('.product-select-po-add').select2({
        placeholder: '{{ text_select_product }}',
        ajax: {
            url: 'index.php?route=purchase/purchase/select2Products&user_token={{ user_token }}',
            dataType: 'json',
            delay: 300,
            data: function(params) {
                return { q: params.term };
            },
            processResults: function(data) {
                return { results: data };
            }
        }
    }).on('select2:select', function(e) {
        let data = e.params.data;
        let units = data.units || [];
        let $tr   = $(this).closest('tr');
        let unitSelect = $tr.find('.product-unit-po-add');
        unitSelect.empty();
        $.each(units, function(i, un) {
            unitSelect.append('<option value="' + un.unit_id + '">' + un.name + '</option>');
        });
    });
}

// حفظ أمر شراء جديد
$('#btn-save-add-po').on('click', function() {
    showLoading();
    $.ajax({
        url: 'index.php?route=purchase/purchase/savePurchaseOrder&user_token={{ user_token }}',
        type: 'POST',
        data: $('#form-add-po').serialize() + '&po_id=0',
        dataType: 'json',
        success: function(json) {
            hideLoading();
            if(json.error) {
                toastr.error(json.error);
            }
            if(json.success) {
                toastr.success(json.success);
                $('#modal-add-po').modal('hide');
                loadPurchaseOrders(1);
            }
        },
        error: handleAjaxError
    });
});

// فتح مودال التعديل (Edit PO)
window.openEditPO = function(po_id) {
    showLoading();
    $.ajax({
        url: 'index.php?route=purchase/purchase/getPurchaseOrderForm&user_token={{ user_token }}&po_id=' + po_id,
        type: 'GET',
        dataType: 'json',
        success: function(json) {
            hideLoading();
            // تعبئة الحقول
            $('#form-edit-po')[0].reset();
            $('input[name="po_id"]').val(json.po_id || '');
            $('input[name="po_number"]').val(json.po_number || '');
            $('input[name="order_date"]').val(json.order_date || '');
            $('input[name="expected_delivery_date"]').val(json.expected_delivery_date || '');
            $('select[name="status"]').val(json.status || 'draft').trigger('change');
            $('input[name="terms_conditions"]').val(json.terms_conditions || '');
            $('textarea[name="notes"]').val(json.notes || '');

            // تهيئة الـVendor في وضع التعديل
            initSelect2Vendor('.po-vendor-select-edit');
            let $vendor = $('.po-vendor-select-edit');
            if(json.vendor_id) {
                $.ajax({
                    url: 'index.php?route=purchase/purchase/select2Vendors&user_token={{ user_token }}&vendor_id=' + json.vendor_id,
                    dataType: 'json',
                    success: function(res) {
                        if(res && res.length) {
                            let vend = res[0];
                            let option = new Option(vend.text, vend.id, true, true);
                            $vendor.append(option).trigger('change');
                        }
                    }
                });
            }

            // تعبئة سطور الأصناف
            let html = '';
            if(json.items && json.items.length) {
                $.each(json.items, function(i, item) {
                    html += '<tr>';
                    html += ' <td><select name="item_product_id[]" class="form-control select2 product-select-po-edit" style="width:100%;" data-prod="' + (item.product_id || '') + '"></select></td>';
                    html += ' <td><input type="number" name="item_quantity[]" class="form-control" value="' + (item.quantity || 1) + '"></td>';
                    html += ' <td><select name="item_unit_id[]" class="form-control select2 product-unit-po-edit"></select></td>';
                    html += ' <td><input type="number" name="item_price[]" class="form-control" step="0.01" value="' + (item.price || 0) + '"></td>';
                    html += ' <td><input type="number" name="item_total[]" class="form-control" step="0.01" value="' + (item.total || 0) + '" readonly></td>';
                    html += ' <td><button type="button" class="btn btn-danger" onclick="$(this).closest(\'tr\').remove();">';
                    html += '   <i class="fa fa-minus-circle"></i></button></td>';
                    html += '</tr>';
                });
            } else {
                html = '<tr><td colspan="6">{{ text_no_results }}</td></tr>';
            }
            $('#table-edit-po-items tbody').html(html);
            initSelect2ProductEditPO();
            $('.select2').select2();

            $('#modal-edit-po').modal('show');
        },
        error: handleAjaxError
    });
};

// إضافة صف جديد في التعديل
$('#btn-add-row-edit-po').on('click', function() {
    let hasNoRes = $('#table-edit-po-items tbody td[colspan="6"]').length > 0;
    if(hasNoRes) {
        $('#table-edit-po-items tbody').html('');
    }
    let row = '';
    row += '<tr>';
    row += ' <td><select name="item_product_id[]" class="form-control select2 product-select-po-edit" data-prod="" style="width:100%;"></select></td>';
    row += ' <td><input type="number" name="item_quantity[]" class="form-control" min="1" value="1"></td>';
    row += ' <td><select name="item_unit_id[]" class="form-control select2 product-unit-po-edit"></select></td>';
    row += ' <td><input type="number" name="item_price[]" class="form-control" step="0.01" value="0"></td>';
    row += ' <td><input type="number" name="item_total[]" class="form-control" step="0.01" value="0" readonly></td>';
    row += ' <td><button type="button" class="btn btn-danger" onclick="$(this).closest(\'tr\').remove();">';
    row += '   <i class="fa fa-minus-circle"></i></button></td>';
    row += '</tr>';

    $('#table-edit-po-items tbody').append(row);
    initSelect2ProductEditPO();
    $('.select2').select2();
});

// تهيئة المنتجات في تعديل الـPO
function initSelect2ProductEditPO() {
    $('.product-select-po-edit').select2({
        placeholder: '{{ text_select_product }}',
        ajax: {
            url: 'index.php?route=purchase/purchase/select2Products&user_token={{ user_token }}',
            dataType: 'json',
            delay: 300,
            data: function(params) {
                return { q: params.term };
            },
            processResults: function(data) {
                return { results: data };
            }
        }
    })
    .on('select2:select', function(e) {
        let data = e.params.data;
        let units = data.units || [];
        let $tr = $(this).closest('tr');
        let unitSelect = $tr.find('.product-unit-po-edit');
        unitSelect.empty();
        $.each(units, function(i, un) {
            unitSelect.append('<option value="' + un.unit_id + '">' + un.name + '</option>');
        });
    })
    .each(function() {
        // تعبئة المنتج مسبقًا إن كان موجودًا
        let preProd = $(this).attr('data-prod') || '';
        if(preProd) {
            let $this = $(this);
            $.ajax({
                url: 'index.php?route=purchase/purchase/select2Products&user_token={{ user_token }}&product_id=' + preProd,
                dataType: 'json',
                success: function(res) {
                    if(res && res.length) {
                        let item = res[0];
                        let option = new Option(item.text, item.id, true, true);
                        $this.append(option).trigger('change');
                        let units = item.units || [];
                        let unitSelect = $this.closest('tr').find('.product-unit-po-edit');
                        unitSelect.empty();
                        $.each(units, function(i, un) {
                            unitSelect.append('<option value="' + un.unit_id + '">' + un.name + '</option>');
                        });
                    }
                }
            });
        }
    });
}

// حفظ التعديل
$('#btn-save-edit-po').on('click', function() {
    showLoading();
    $.ajax({
        url: 'index.php?route=purchase/purchase/savePurchaseOrder&user_token={{ user_token }}',
        type: 'POST',
        data: $('#form-edit-po').serialize(),
        dataType: 'json',
        success: function(json) {
            hideLoading();
            if(json.error) {
                toastr.error(json.error);
            }
            if(json.success) {
                toastr.success(json.success);
                $('#modal-edit-po').modal('hide');
                loadPurchaseOrders(1);
            }
        },
        error: handleAjaxError
    });
});

// عرض تفاصيل PO
window.viewPO = function(po_id) {
    showLoading();
    $.ajax({
        url: 'index.php?route=purchase/purchase/viewPurchaseOrder&user_token={{ user_token }}&po_id=' + po_id,
        type: 'GET',
        dataType: 'html',
        success: function(html) {
            hideLoading();
            $('#view-po-details').html(html);
            $('#modal-view-po').modal('show');
        },
        error: handleAjaxError
    });
};

// حذف أمر الشراء
window.deletePO = function(po_id) {
    if(!confirm("{{ text_confirm_delete }}")) return;
    showLoading();
    $.ajax({
        url: 'index.php?route=purchase/purchase/deletePurchaseOrder&user_token={{ user_token }}&po_id=' + po_id,
        type: 'POST',
        dataType: 'json',
        success: function(json) {
            hideLoading();
            if(json.error) {
                toastr.error(json.error);
            }
            if(json.success) {
                toastr.success(json.success);
                loadPurchaseOrders(1);
            }
        },
        error: handleAjaxError
    });
};

// اعتماد أمر الشراء (Approve)
window.approvePO = function(po_id) {
    $('#modal-approve-po input[name="po_id"]').val(po_id);
    $('#modal-approve-po').modal('show');
};

$('#btn-confirm-approve-po').on('click', function() {
    showLoading();
    $.ajax({
        url: 'index.php?route=purchase/purchase/approvePurchaseOrder&user_token={{ user_token }}',
        type: 'POST',
        data: $('#form-approve-po').serialize(),
        dataType: 'json',
        success: function(json) {
            hideLoading();
            if(json.error) {
                toastr.error(json.error);
            }
            if(json.success) {
                toastr.success(json.success);
                $('#modal-approve-po').modal('hide');
                loadPurchaseOrders(1);
            }
        },
        error: handleAjaxError
    });
});

// رفض أمر الشراء (Reject)
window.rejectPO = function(po_id) {
    $('#modal-reject-po input[name="po_id"]').val(po_id);
    $('#modal-reject-po').modal('show');
};

$('#btn-confirm-reject-po').on('click', function() {
    showLoading();
    $.ajax({
        url: 'index.php?route=purchase/purchase/rejectPurchaseOrder&user_token={{ user_token }}',
        type: 'POST',
        data: $('#form-reject-po').serialize(),
        dataType: 'json',
        success: function(json) {
            hideLoading();
            if(json.error) {
                toastr.error(json.error);
            }
            if(json.success) {
                toastr.success(json.success);
                $('#modal-reject-po').modal('hide');
                loadPurchaseOrders(1);
            }
        },
        error: handleAjaxError
    });
});




// دالة فتح مودال إضافة Goods Receipt وتمرير po_id
window.openAddGoodsReceipt = function(po_id) {
    // نفرغ النموذج
    $('#form-add-gr')[0].reset();
    // وضع الـpo_id في حقل مخفي أو حسب بنية النموذج
    $('#form-add-gr input[name="po_id"]').val(po_id);

    // إعادة تعيين جدول الأصناف
    $('#table-add-gr-items tbody').html('<tr><td colspan="6">{{ text_no_results }}</td></tr>');

    // عرض المودال
    $('#modal-add-gr').modal('show');
};




// من أمر الشراء نستطيع إنشاء Goods Receipt
window.createGR = function(po_id) {
    // الانتقال لتبويب Goods Receipt (اختياري)
    $('a[href="#tab-goods-receipt"]').tab('show');
    // ثم فتح مودال إضافة استلام بضائع وتمرير po_id
    openAddGoodsReceipt(po_id);
};

// إنشاء فاتورة من PO
window.createInvoice = function(po_id) {
    // هنا يمكننا مباشرةً استدعاء فتح مودال الفاتورة وتمرير الـpo_id
    openAddInvoice({ po_id: po_id });
};


/************************************************************************************
 * جزء (8/16)
 * استكمال الأكواد الخاصة بـ Goods Receipt (GR)،
 * مع الدوال والـAJAX المرتبطة بعمليات إضافة/عرض/تعديل/اعتماد/رفض وغيرها.
 ************************************************************************************/

//=========================[ Purchase GoodsReceipts ]=========================//

// تحميل قائمة Goods Receipts بالفلترة والصفحة
function loadGoodsReceipts(page=1){
    showLoading();
    let url='index.php?route=purchase/purchase/getGoodsReceiptList&user_token={{ user_token }}&page='+page;

    // جمع الفلاتر من الحقول
    let grNumber = $('#filter-gr-number').val() || '';
    let status   = $('#filter-gr-status').val() || '';
    let poNumber = $('#filter-gr-po-number').val() || '';
    let dateStart= $('#filter-gr-date-start').val() || '';
    let dateEnd  = $('#filter-gr-date-end').val() || '';

    if(grNumber) url += '&filter_gr_number=' + encodeURIComponent(grNumber);
    if(status)   url += '&filter_status=' + encodeURIComponent(status);
    if(poNumber) url += '&filter_po_number=' + encodeURIComponent(poNumber);
    if(dateStart)url += '&filter_date_start=' + encodeURIComponent(dateStart);
    if(dateEnd)  url += '&filter_date_end=' + encodeURIComponent(dateEnd);

    $.ajax({
        url: url,
        type:'GET',
        dataType:'json',
        success:function(json){
            hideLoading();
            let html='';
            if(json.receipts && json.receipts.length){
                $.each(json.receipts, function(i,gr){
                    html += '<tr>';
                    html += '  <td class="text-center"><input type="checkbox" value="'+gr.goods_receipt_id+'"></td>';
                    html += '  <td>'+(gr.gr_number || '')+'</td>';
                    html += '  <td>'+(gr.po_number || '')+'</td>';
                    html += '  <td>'+(gr.status || '')+'</td>';
                    html += '  <td>'+(gr.date_added || '')+'</td>';
                    html += '  <td class="text-right">';
                    html += '    <div class="btn-group">';
                    html += '      <button type="button" class="btn btn-sm btn-default dropdown-toggle" data-toggle="dropdown">';
                    html += '        <i class="fa fa-cog"></i> <span class="caret"></span>';
                    html += '      </button>';
                    html += '      <ul class="dropdown-menu dropdown-menu-right">';
                    // زر العرض
                    html += '        <li><a href="#" onclick="viewGR('+gr.goods_receipt_id+')"><i class="fa fa-eye"></i> {{ button_view }}</a></li>';
                    // زر التعديل (لو متاح)
                    {% if user_goods_receipt_edit %}
                    html += '        <li><a href="#" onclick="openEditGR('+gr.goods_receipt_id+')"><i class="fa fa-pencil"></i> {{ button_edit }}</a></li>';
                    {% endif %}
                    // زر الحذف (لو متاح)
                    {% if user_goods_receipt_delete %}
                    html += '        <li><a href="#" onclick="deleteGR('+gr.goods_receipt_id+')"><i class="fa fa-trash"></i> {{ button_delete }}</a></li>';
                    {% endif %}
                    // زر الاعتماد (Approve)
                    html += '        <li><a href="#" onclick="approveGR('+gr.goods_receipt_id+')"><i class="fa fa-check"></i> {{ text_approve }}</a></li>';
                    // زر الرفض
                    html += '        <li><a href="#" onclick="rejectGR('+gr.goods_receipt_id+')"><i class="fa fa-ban"></i> {{ text_reject }}</a></li>';
                    // خيار إنشاء فاتورة
                    html += '        <li><a href="#" onclick="createInvoiceFromGR('+gr.goods_receipt_id+')"><i class="fa fa-file-text"></i> {{ text_invoice }}</a></li>';
                    html += '      </ul>';
                    html += '    </div>';
                    html += '  </td>';
                    html += '</tr>';
                });
            } else {
                html='<tr><td class="text-center" colspan="6">{{ text_no_results }}</td></tr>';
            }
            $('#table-gr tbody').html(html);
            $('#gr-pagination').html(json.pagination || '');
            $('#gr-results').html(json.results || '');
        },
        error: handleAjaxError
    });
}

// عند الضغط على زر الفلترة
$('#button-filter-gr').on('click',function(){
    loadGoodsReceipts(1);
});

// الدالة التي تُفتح عند النقر على إضافة استلام (أو من زر أعلى الصفحة)
$('#btn-add-gr').on('click', function(){
    // Reset form
    $('#form-add-gr')[0].reset();
    // حذف/إعادة تعيين جدول الأصناف
    $('#table-add-gr-items tbody').html('<tr><td colspan="6">{{ text_no_results }}</td></tr>');
    // تهيئة الـ Select2
    $('.select2').select2();
    // تهيئة Select2 لاختيار PO
    initSelect2PO('.gr-po-select');
    // فتح المودال
    $('#modal-add-gr').modal('show');
});

// تهيئة الـSelect2 الخاصة باختيار PO
function initSelect2PO(selector){
    $(selector).select2({
        placeholder:'{{ text_select_po }}',
        ajax:{
            url:'index.php?route=purchase/purchase/select2PO&user_token={{ user_token }}',
            dataType:'json',
            delay:300,
            data:function(params){ 
                return { q: params.term }; 
            },
            processResults:function(data){ 
                return { results:data }; 
            }
        }
    });
}

// إضافة صف جديد في جدول إضافة GR
$('#btn-add-row-add-gr').on('click', function(){
    let hasNoRes = $('#table-add-gr-items tbody td[colspan="6"]').length>0;
    if(hasNoRes) $('#table-add-gr-items tbody').html('');

    let tr='';
    tr+='<tr>';
    tr+=' <td><select name="item_product_id[]" class="form-control select2 gr-product-select-add" style="width:100%;"></select></td>';
    tr+=' <td><input type="number" min="1" name="item_quantity_received[]" class="form-control" value="1"></td>';
    tr+=' <td><select name="item_unit_id[]" class="form-control select2 gr-unit-select-add"></select></td>';
    tr+=' <td><select name="item_quality_result[]" class="form-control select2">';
    tr+='    <option value="pending">{{ text_pending }}</option>';
    tr+='    <option value="passed">{{ text_passed }}</option>';
    tr+='    <option value="failed">{{ text_failed }}</option>';
    tr+=' </select></td>';
    tr+=' <td><input type="text" name="item_remarks[]" class="form-control" value=""></td>';
    tr+=' <td><button type="button" class="btn btn-danger" onclick="$(this).closest(\'tr\').remove();">';
    tr+='    <i class="fa fa-minus-circle"></i></button></td>';
    tr+='</tr>';

    $('#table-add-gr-items tbody').append(tr);
    initSelect2ProductForGRAdd();
    $('.select2').select2();
});

// تهيئة Select2 للمنتجات بسطر إضافة جديد
function initSelect2ProductForGRAdd(){
    $('.gr-product-select-add').select2({
        placeholder:'{{ text_select_product }}',
        ajax:{
            url:'index.php?route=purchase/purchase/select2Products&user_token={{ user_token }}',
            dataType:'json',
            delay:300,
            data:function(params){ return { q:params.term }; },
            processResults:function(data){ return { results:data }; }
        }
    }).on('select2:select', function(e){
        let data = e.params.data;
        let units = data.units || [];
        let $tr   = $(this).closest('tr');
        let unitSelect = $tr.find('.gr-unit-select-add');
        unitSelect.empty();
        $.each(units, function(i,un){
            unitSelect.append('<option value="'+un.unit_id+'">'+un.name+'</option>');
        });
    });
}

// حفظ المودال الجديد (Add GR)
$('#btn-save-add-gr').on('click', function(){
    showLoading();
    $.ajax({
        url:'index.php?route=purchase/purchase/saveGoodsReceipt&user_token={{ user_token }}',
        type:'POST',
        data:$('#form-add-gr').serialize() + '&goods_receipt_id=0',
        dataType:'json',
        success:function(json){
            hideLoading();
            if(json.error){
                toastr.error(json.error);
            }
            if(json.success){
                toastr.success(json.success);
                $('#modal-add-gr').modal('hide');
                loadGoodsReceipts(1);
            }
        },
        error: handleAjaxError
    });
});

// فتح مودال التعديل
window.openEditGR = function(gr_id){
    showLoading();
    $.ajax({
        url:'index.php?route=purchase/purchase/getGoodsReceiptForm&user_token={{ user_token }}&goods_receipt_id='+gr_id,
        type:'GET',
        dataType:'json',
        success:function(json){
            hideLoading();
            // Reset
            $('#form-edit-gr')[0].reset();
            $('input[name="goods_receipt_id"]').val(json.goods_receipt_id || '');
            $('input[name="gr_number"]').val(json.gr_number || '');
            $('input[name="receipt_date"]').val(json.receipt_date || '');
            $('select[name="status"]').val(json.status || 'pending').trigger('change');
            $('textarea[name="notes"]').val(json.notes || '');

            // تهيئة PO select
            initSelect2PO('.gr-po-select-edit');
            let $poSel = $('.gr-po-select-edit');
            if(json.po_id){
                $.ajax({
                    url:'index.php?route=purchase/purchase/select2PO&user_token={{ user_token }}&po_id='+json.po_id,
                    dataType:'json',
                    success:function(res){
                        if(res && res.length){
                            let po = res[0];
                            let option = new Option(po.text, po.id, true, true);
                            $poSel.append(option).trigger('change');
                        }
                    }
                });
            }

            // تعبئة الأصناف
            let html='';
            if(json.items && json.items.length){
                $.each(json.items, function(i,item){
                    html+='<tr>';
                    html+=' <td><select name="item_product_id[]" class="form-control select2 gr-product-select-edit" data-prod="'+(item.product_id||'')+'" style="width:100%;"></select></td>';
                    html+=' <td><input type="number" name="item_quantity_received[]" class="form-control" min="1" value="'+(item.quantity_received||1)+'"></td>';
                    html+=' <td><select name="item_unit_id[]" class="form-control select2 gr-unit-select-edit"></select></td>';
                    html+=' <td><select name="item_quality_result[]" class="form-control select2">';
                    html+='   <option value="pending" '+(item.quality_result=='pending'?'selected':'')+'>{{ text_pending }}</option>';
                    html+='   <option value="passed"  '+(item.quality_result=='passed' ?'selected':'')+'>{{ text_passed }}</option>';
                    html+='   <option value="failed"  '+(item.quality_result=='failed' ?'selected':'')+'>{{ text_failed }}</option>';
                    html+=' </select></td>';
                    html+=' <td><input type="text" name="item_remarks[]" class="form-control" value="'+(item.remarks||'')+'"></td>';
                    html+=' <td><button type="button" class="btn btn-danger" onclick="$(this).closest(\'tr\').remove();">';
                    html+='    <i class="fa fa-minus-circle"></i></button></td>';
                    html+='</tr>';
                });
            } else {
                html='<tr><td colspan="6">{{ text_no_results }}</td></tr>';
            }
            $('#table-edit-gr-items tbody').html(html);
            initSelect2ProductForGREdit();
            $('.select2').select2();

            $('#modal-edit-gr').modal('show');
        },
        error: handleAjaxError
    });
};

// إضافة صف بالتعديل
$('#btn-add-row-edit-gr').on('click', function(){
    let hasNoRes=$('#table-edit-gr-items tbody td[colspan="6"]').length>0;
    if(hasNoRes) $('#table-edit-gr-items tbody').html('');

    let tr='';
    tr+='<tr>';
    tr+=' <td><select name="item_product_id[]" class="form-control select2 gr-product-select-edit" data-prod="" style="width:100%;"></select></td>';
    tr+=' <td><input type="number" min="1" name="item_quantity_received[]" class="form-control" value="1"></td>';
    tr+=' <td><select name="item_unit_id[]" class="form-control select2 gr-unit-select-edit"></select></td>';
    tr+=' <td><select name="item_quality_result[]" class="form-control select2">';
    tr+='   <option value="pending">{{ text_pending }}</option>';
    tr+='   <option value="passed">{{ text_passed }}</option>';
    tr+='   <option value="failed">{{ text_failed }}</option>';
    tr+=' </select></td>';
    tr+=' <td><input type="text" name="item_remarks[]" class="form-control" value=""></td>';
    tr+=' <td><button type="button" class="btn btn-danger" onclick="$(this).closest(\'tr\').remove();">';
    tr+='    <i class="fa fa-minus-circle"></i></button></td>';
    tr+='</tr>';

    $('#table-edit-gr-items tbody').append(tr);
    initSelect2ProductForGREdit();
    $('.select2').select2();
});

// تهيئة المنتج + الوحدات عند التعديل
function initSelect2ProductForGREdit(){
    $('.gr-product-select-edit').select2({
        placeholder:'{{ text_select_product }}',
        ajax:{
            url:'index.php?route=purchase/purchase/select2Products&user_token={{ user_token }}',
            dataType:'json',
            delay:300,
            data:function(params){ return { q:params.term }; },
            processResults:function(data){ return { results:data }; }
        }
    }).on('select2:select', function(e){
        let data = e.params.data;
        let units= data.units || [];
        let $tr  = $(this).closest('tr');
        let unitSel = $tr.find('.gr-unit-select-edit');
        unitSel.empty();
        $.each(units, function(i,un){
            unitSel.append('<option value="'+un.unit_id+'">'+un.name+'</option>');
        });
    }).each(function(){
        // في حال وجود منتج محدد مسبقاً
        let preProd = $(this).attr('data-prod') || '';
        if(preProd){
            let $this = $(this);
            $.ajax({
                url:'index.php?route=purchase/purchase/select2Products&user_token={{ user_token }}&product_id='+preProd,
                dataType:'json',
                success:function(res){
                    if(res && res.length){
                        let item=res[0];
                        let option=new Option(item.text, item.id, true, true);
                        $this.append(option).trigger('change');
                        let units=item.units || [];
                        let unitSel=$this.closest('tr').find('.gr-unit-select-edit');
                        unitSel.empty();
                        $.each(units,function(i,un){
                            unitSel.append('<option value="'+un.unit_id+'">'+un.name+'</option>');
                        });
                    }
                }
            });
        }
    });
}

// حفظ التعديل
$('#btn-save-edit-gr').on('click',function(){
    showLoading();
    $.ajax({
        url:'index.php?route=purchase/purchase/saveGoodsReceipt&user_token={{ user_token }}',
        type:'POST',
        data:$('#form-edit-gr').serialize(),
        dataType:'json',
        success:function(json){
            hideLoading();
            if(json.error){
                toastr.error(json.error);
            }
            if(json.success){
                toastr.success(json.success);
                $('#modal-edit-gr').modal('hide');
                loadGoodsReceipts(1);
            }
        },
        error: handleAjaxError
    });
});

// عرض التفاصيل في مودال
window.viewGR = function(gr_id){
    showLoading();
    $.ajax({
        url:'index.php?route=purchase/purchase/viewGoodsReceipt&user_token={{ user_token }}&goods_receipt_id='+gr_id,
        type:'GET',
        dataType:'html',
        success:function(html){
            hideLoading();
            $('#view-gr-details').html(html);
            $('#modal-view-gr').modal('show');
        },
        error: handleAjaxError
    });
};

// حذف GR
window.deleteGR = function(gr_id){
    if(!confirm("{{ text_confirm_delete }}")) return;
    showLoading();
    $.ajax({
        url:'index.php?route=purchase/purchase/deleteGoodsReceipt&user_token={{ user_token }}&goods_receipt_id='+gr_id,
        type:'POST',
        dataType:'json',
        success:function(json){
            hideLoading();
            if(json.error){
                toastr.error(json.error);
            }
            if(json.success){
                toastr.success(json.success);
                loadGoodsReceipts(1);
            }
        },
        error: handleAjaxError
    });
};

// اعتماد GR
window.approveGR = function(gr_id){
    $('#modal-approve-gr input[name="goods_receipt_id"]').val(gr_id);
    $('#modal-approve-gr').modal('show');
};

$('#btn-confirm-approve-gr').on('click', function(){
    showLoading();
    $.ajax({
        url:'index.php?route=purchase/purchase/approveGoodsReceipt&user_token={{ user_token }}',
        type:'POST',
        data: $('#form-approve-gr').serialize(),
        dataType:'json',
        success:function(json){
            hideLoading();
            if(json.error){
                toastr.error(json.error);
            }
            if(json.success){
                toastr.success(json.success);
                $('#modal-approve-gr').modal('hide');
                loadGoodsReceipts(1);
            }
        },
        error: handleAjaxError
    });
});

// رفض GR
window.rejectGR = function(gr_id){
    $('#modal-reject-gr input[name="goods_receipt_id"]').val(gr_id);
    $('#modal-reject-gr').modal('show');
};

$('#btn-confirm-reject-gr').on('click', function(){
    showLoading();
    $.ajax({
        url:'index.php?route=purchase/purchase/rejectGoodsReceipt&user_token={{ user_token }}',
        type:'POST',
        data: $('#form-reject-gr').serialize(),
        dataType:'json',
        success:function(json){
            hideLoading();
            if(json.error){
                toastr.error(json.error);
            }
            if(json.success){
                toastr.success(json.success);
                $('#modal-reject-gr').modal('hide');
                loadGoodsReceipts(1);
            }
        },
        error: handleAjaxError
    });
});
/**********************************************************************
 * مثال: فتح مودال إضافة فاتورة وربطها بـ Purchase Order أو Goods Receipt
 **********************************************************************/


// دالة إنشاء فاتورة مباشرة من Goods Receipt
window.createInvoiceFromGR = function(gr_id){
    // مثلاً ننتقل لتبويب "Supplier Invoice"
    $('a[href="#tab-supplier-invoice"]').tab('show');
    
    // بعد تفعيل التبويب، يمكننا فتح مودال "إضافة فاتورة" وتمرير goods_receipt_id:
    setTimeout(function(){
        openAddInvoice({ gr_id: gr_id });
    }, 400); 
    // تأخير بسيط لضمان انتقال التبويب
};


/***********************************************************
 * الجزء التاسع (9): دوال إدارة فواتير المورد (Supplier Invoices)
 ***********************************************************/

//=========================[ الدالة: فتح مودال إضافة فاتورة ]=========================//
window.openAddInvoice = function(options) {
    // options قد تحتوي على { po_id: X } أو { gr_id: Y } أو غير ذلك
    if(!options) options = {};

    // 1) إعادة ضبط/تفريغ النموذج
    $('#form-add-invoice')[0].reset();
    $('#table-add-invoice-items tbody').html('<tr><td colspan="6">{{ text_no_results }}</td></tr>');
    
    // إزالة حقل مخفي goods_receipt_id إذا كان موجودًا من قبل
    $('#form-add-invoice input[name="goods_receipt_id"]').remove();

    // 2) تهيئة الـSelect2 الضرورية (مثل vendor, po, products)
    $('.select2').select2();
    initSelect2Vendor('.invoice-vendor-select');
    initSelect2PO('.invoice-po-select');
    // ملاحظة: من المفترض وجود دالتين باسم initSelect2Vendor و initSelect2PO
    // تم شرح أو تعريفهما في الأجزاء السابقة.

    // 3) لو وصلنا po_id أو gr_id من options
    if(options.po_id) {
        // تعبئة الـ select الخاص بالـ PO أو حقل مخفي 
        $('select[name="po_id"]').val(options.po_id).trigger('change');
    }
    if(options.gr_id) {
        // مثلاً نضيف حقل مخفي لتخزين رقم الـ GR:
        $('#form-add-invoice').append('<input type="hidden" name="goods_receipt_id" value="'+ options.gr_id +'">');
    }

    // 4) فتح المودال
    $('#modal-add-invoice').modal('show');
};

//=========================[ الدالة: إنشاء فاتورة انطلاقًا من Goods Receipt ]=========================//
window.createInvoiceFromGR = function(gr_id){
    // 1) الانتقال لتبويب "Supplier Invoice"
    $('a[href="#tab-supplier-invoice"]').tab('show');
    
    // 2) بعد الانتقال، نفتح مودال إضافة الفاتورة ونمرّر goods_receipt_id:
    setTimeout(function(){
        openAddInvoice({ gr_id: gr_id });
    }, 400); 
    // استخدمنا setTimeout لإعطاء مهلة وجيزة كي يتم تفعيل تبويب Supplier Invoice.
};

//=========================[ تحميل فواتير المورد (قائمة) مع الفلترة ]=========================//
function loadSupplierInvoices(page = 1){
    showLoading();
    let url = 'index.php?route=purchase/purchase/getSupplierInvoiceList&user_token={{ user_token }}&page=' + page;

    // جمع فلاتر البحث من الواجهة
    let invNum    = $('#filter-invoice-number').val() || '';
    let status    = $('#filter-invoice-status').val() || '';
    let vendor    = $('#filter-invoice-vendor').val() || '';
    let dateStart = $('#filter-invoice-date-start').val() || '';
    let dateEnd   = $('#filter-invoice-date-end').val() || '';

    // إلحاقها بالـURL
    if(invNum)    url += '&filter_invoice_number=' + encodeURIComponent(invNum);
    if(status)    url += '&filter_status=' + encodeURIComponent(status);
    if(vendor)    url += '&filter_vendor=' + encodeURIComponent(vendor);
    if(dateStart) url += '&filter_date_start=' + encodeURIComponent(dateStart);
    if(dateEnd)   url += '&filter_date_end=' + encodeURIComponent(dateEnd);

    // إرسال الطلب عبر AJAX
    $.ajax({
        url: url,
        type: 'GET',
        dataType: 'json',
        success: function(json){
            hideLoading();
            let html = '';
            if(json.invoices && json.invoices.length) {
                $.each(json.invoices, function(i, inv){
                    html += '<tr>';
                    html += ' <td class="text-center"><input type="checkbox" value="'+ (inv.invoice_id || '') +'"></td>';
                    html += ' <td>' + (inv.invoice_number || '') + '</td>';
                    html += ' <td>' + (inv.vendor_name || '') + '</td>';
                    html += ' <td class="text-right">' + (inv.total_amount || '0') + '</td>';
                    html += ' <td>' + (inv.status || '') + '</td>';
                    html += ' <td>' + (inv.date_added || '') + '</td>';
                    html += ' <td class="text-right">';
                    html += '   <div class="btn-group">';
                    html += '     <button type="button" class="btn btn-sm btn-default dropdown-toggle" data-toggle="dropdown">';
                    html += '       <i class="fa fa-cog"></i> <span class="caret"></span>';
                    html += '     </button>';
                    html += '     <ul class="dropdown-menu dropdown-menu-right">';
                    
                    // زر عرض الفاتورة
                    html += '       <li><a href="#" onclick="viewInvoice(' + inv.invoice_id + ')">';
                    html += '         <i class="fa fa-eye"></i> {{ button_view }}</a></li>';
                    
                    // زر تعديل (إن كانت الصلاحية موجودة)
                    {% if user_supplier_invoice_edit %}
                    html += '       <li><a href="#" onclick="openEditInvoice(' + inv.invoice_id + ')">';
                    html += '         <i class="fa fa-pencil"></i> {{ button_edit }}</a></li>';
                    {% endif %}

                    // زر حذف (إن كانت الصلاحية موجودة)
                    {% if user_supplier_invoice_delete %}
                    html += '       <li><a href="#" onclick="deleteInvoice(' + inv.invoice_id + ')">';
                    html += '         <i class="fa fa-trash"></i> {{ button_delete }}</a></li>';
                    {% endif %}

                    // زر اعتماد الفاتورة
                    html += '       <li><a href="#" onclick="approveInvoice(' + inv.invoice_id + ')">';
                    html += '         <i class="fa fa-check"></i> {{ text_approve }}</a></li>';
                    
                    // زر رفض الفاتورة
                    html += '       <li><a href="#" onclick="rejectInvoice(' + inv.invoice_id + ')">';
                    html += '         <i class="fa fa-ban"></i> {{ text_reject }}</a></li>';
                    
                    html += '     </ul>';
                    html += '   </div>';
                    html += ' </td>';
                    html += '</tr>';
                });
            } else {
                html = '<tr><td class="text-center" colspan="7">{{ text_no_results }}</td></tr>';
            }

            $('#table-invoice tbody').html(html);
            $('#invoice-pagination').html(json.pagination || '');
            $('#invoice-results').html(json.results || '');
        },
        error: handleAjaxError
    });
}

// الربط مع زر الفلترة
$('#button-filter-invoice').on('click', function(){
    loadSupplierInvoices(1);
});

//=========================[ فتح مودال إضافة فاتورة (زر أعلى الصفحة) ]=========================//
{% if user_supplier_invoice_add %}
$('#btn-add-invoice').on('click', function(){
    // إعادة ضبط النموذج وفتح المودال
    openAddInvoice({});
});
{% endif %}

//=========================[ إضافة صف جديد في مودال إضافة فاتورة ]=========================//
$('#btn-add-row-add-invoice').on('click', function(){
    let hasNoRes = $('#table-add-invoice-items tbody td[colspan="6"]').length > 0;
    if(hasNoRes) {
        $('#table-add-invoice-items tbody').html('');
    }
    let row = '';
    row += '<tr>';
    row += '  <td><select name="item_product_id[]" class="form-control select2 invoice-product-select-add" style="width:100%;"></select></td>';
    row += '  <td><input type="number" name="item_quantity[]" class="form-control" min="1" value="1"></td>';
    row += '  <td><select name="item_unit_id[]" class="form-control select2 invoice-unit-select-add"></select></td>';
    row += '  <td><input type="number" name="item_unit_price[]" class="form-control" step="0.01" value="0"></td>';
    row += '  <td><input type="number" name="item_total_price[]" class="form-control" step="0.01" value="0" readonly></td>';
    row += '  <td><button type="button" class="btn btn-danger" onclick="$(this).closest(\'tr\').remove();">';
    row += '    <i class="fa fa-minus-circle"></i></button></td>';
    row += '</tr>';

    $('#table-add-invoice-items tbody').append(row);
    initSelect2ProductAddInvoice();
    $('.select2').select2();
});

//=========================[ حفظ فاتورة (إضافة) ]=========================//
$('#btn-save-add-invoice').on('click', function(){
    showLoading();
    $.ajax({
        url: 'index.php?route=purchase/purchase/saveSupplierInvoice&user_token={{ user_token }}',
        type: 'POST',
        data: $('#form-add-invoice').serialize() + '&invoice_id=0',
        dataType: 'json',
        success: function(json){
            hideLoading();
            if(json.error) {
                toastr.error(json.error);
            }
            if(json.success) {
                toastr.success(json.success);
                $('#modal-add-invoice').modal('hide');
                loadSupplierInvoices(1);
            }
        },
        error: handleAjaxError
    });
});

//=========================[ دوال تهيئة Select2 لمنتجات الفاتورة ]=========================//
function initSelect2ProductAddInvoice(){
    // ربط select2 بالمنتجات
    $('.invoice-product-select-add').select2({
        placeholder: '{{ text_select_product }}',
        ajax: {
            url: 'index.php?route=purchase/purchase/select2Products&user_token={{ user_token }}',
            dataType: 'json',
            delay: 300,
            data: function(params){
                return { q: params.term };
            },
            processResults: function(data){
                return { results: data };
            }
        }
    }).on('select2:select', function(e){
        // عند اختيار المنتج نملأ الوحدات
        let data = e.params.data;
        let units = data.units || [];
        let $tr = $(this).closest('tr');
        let unitSelect = $tr.find('.invoice-unit-select-add');
        unitSelect.empty();
        $.each(units, function(i, un){
            unitSelect.append('<option value="'+un.unit_id+'">'+un.name+'</option>');
        });
    });
}

//=========================[ عرض فاتورة (عرض تفصيلي) ]=========================//
window.viewInvoice = function(id){
    showLoading();
    $.ajax({
        url: 'index.php?route=purchase/purchase/viewSupplierInvoice&user_token={{ user_token }}&invoice_id='+id,
        type: 'GET',
        dataType: 'html',
        success: function(html){
            hideLoading();
            $('#view-invoice-details').html(html);
            $('#modal-view-invoice').modal('show');
        },
        error: handleAjaxError
    });
};

//=========================[ حذف فاتورة ]=========================//
window.deleteInvoice = function(id){
    if(!confirm("{{ text_confirm_delete }}")) return;
    showLoading();
    $.ajax({
        url: 'index.php?route=purchase/purchase/deleteSupplierInvoice&user_token={{ user_token }}&invoice_id='+id,
        type: 'POST',
        dataType: 'json',
        success: function(json){
            hideLoading();
            if(json.error){
                toastr.error(json.error);
            }
            if(json.success){
                toastr.success(json.success);
                loadSupplierInvoices(1);
            }
        },
        error: handleAjaxError
    });
};

//=========================[ اعتماد/رفض فاتورة ]=========================//
window.approveInvoice = function(id){
    $('#modal-approve-invoice input[name="invoice_id"]').val(id);
    $('#modal-approve-invoice').modal('show');
};
$('#btn-confirm-approve-invoice').on('click', function(){
    showLoading();
    $.ajax({
        url: 'index.php?route=purchase/purchase/approveSupplierInvoice&user_token={{ user_token }}',
        type: 'POST',
        data: $('#form-approve-invoice').serialize(),
        dataType: 'json',
        success: function(json){
            hideLoading();
            if(json.error){ toastr.error(json.error); }
            if(json.success){
                toastr.success(json.success);
                $('#modal-approve-invoice').modal('hide');
                loadSupplierInvoices(1);
            }
        },
        error: handleAjaxError
    });
});

window.rejectInvoice = function(id){
    $('#modal-reject-invoice input[name="invoice_id"]').val(id);
    $('#modal-reject-invoice').modal('show');
};
$('#btn-confirm-reject-invoice').on('click', function(){
    showLoading();
    $.ajax({
        url: 'index.php?route=purchase/purchase/rejectSupplierInvoice&user_token={{ user_token }}',
        type: 'POST',
        data: $('#form-reject-invoice').serialize(),
        dataType: 'json',
        success: function(json){
            hideLoading();
            if(json.error){ toastr.error(json.error); }
            if(json.success){
                toastr.success(json.success);
                $('#modal-reject-invoice').modal('hide');
                loadSupplierInvoices(1);
            }
        },
        error: handleAjaxError
    });
});

/*******************************
 * نهاية الجزء التاسع (9)
 *******************************/

/***********************************************************
 * الجزء العاشر (10): دوال إدارة دفعات المورد (Vendor Payments)
 ***********************************************************/

//=========================[ تحميل قائمة الدفعات مع الفلترة ]=========================//
function loadVendorPayments(page = 1){
    showLoading();
    let url = 'index.php?route=purchase/purchase/getVendorPaymentList&user_token={{ user_token }}&page=' + page;

    // جمع الفلاتر من واجهة المستخدم
    let payNum   = $('#filter-payment-number').val() || '';
    let vendor   = $('#filter-payment-vendor').val() || '';
    let status   = $('#filter-payment-status').val() || '';
    let dStart   = $('#filter-payment-date-start').val() || '';
    let dEnd     = $('#filter-payment-date-end').val() || '';

    if(payNum) url += '&filter_payment_number=' + encodeURIComponent(payNum);
    if(vendor) url += '&filter_vendor=' + encodeURIComponent(vendor);
    if(status) url += '&filter_status=' + encodeURIComponent(status);
    if(dStart) url += '&filter_date_start=' + encodeURIComponent(dStart);
    if(dEnd)   url += '&filter_date_end=' + encodeURIComponent(dEnd);

    $.ajax({
        url: url,
        type: 'GET',
        dataType: 'json',
        success: function(json){
            hideLoading();
            let html = '';
            if(json.payments && json.payments.length){
                $.each(json.payments, function(i, p){
                    html += '<tr>';
                    html += '  <td class="text-center"><input type="checkbox" value="'+ (p.payment_id || '') +'"></td>';
                    html += '  <td>' + (p.payment_number || '') + '</td>';
                    html += '  <td>' + (p.vendor_name || '') + '</td>';
                    html += '  <td class="text-right">' + (p.amount || '0') + '</td>';
                    html += '  <td>' + (p.status || '') + '</td>';
                    html += '  <td>' + (p.date_added || '') + '</td>';
                    html += '  <td class="text-right">';
                    html += '    <div class="btn-group">';
                    html += '      <button type="button" class="btn btn-sm btn-default dropdown-toggle" data-toggle="dropdown">';
                    html += '        <i class="fa fa-cog"></i> <span class="caret"></span>';
                    html += '      </button>';
                    html += '      <ul class="dropdown-menu dropdown-menu-right">';
                    
                    // زر عرض التفاصيل
                    html += '        <li><a href="#" onclick="viewVendorPayment(' + p.payment_id + ')">';
                    html += '          <i class="fa fa-eye"></i> {{ button_view }}</a></li>';
                    
                    // زر التعديل (إن وُجدت الصلاحية)
                    {% if user_vendor_payment_edit %}
                    html += '        <li><a href="#" onclick="openEditPayment(' + p.payment_id + ')">';
                    html += '          <i class="fa fa-pencil"></i> {{ button_edit }}</a></li>';
                    {% endif %}

                    // زر الحذف (إن وُجدت الصلاحية)
                    {% if user_vendor_payment_delete %}
                    html += '        <li><a href="#" onclick="deleteVendorPayment(' + p.payment_id + ')">';
                    html += '          <i class="fa fa-trash"></i> {{ button_delete }}</a></li>';
                    {% endif %}

                    // زر الاعتماد
                    {% if user_vendor_payment_approve %}
                    html += '        <li><a href="#" onclick="approveVendorPayment(' + p.payment_id + ')">';
                    html += '          <i class="fa fa-check"></i> {{ text_approve }}</a></li>';
                    {% endif %}

                    // زر الرفض
                    {% if user_vendor_payment_reject %}
                    html += '        <li><a href="#" onclick="rejectVendorPayment(' + p.payment_id + ')">';
                    html += '          <i class="fa fa-ban"></i> {{ text_reject }}</a></li>';
                    {% endif %}

                    html += '      </ul>';
                    html += '    </div>';
                    html += '  </td>';
                    html += '</tr>';
                });
            } else {
                html = '<tr><td class="text-center" colspan="7">{{ text_no_results }}</td></tr>';
            }

            // تعبئة الجدول
            $('#table-payment tbody').html(html);
            // تعبئة الـPagination والـResults
            $('#payment-pagination').html(json.pagination || '');
            $('#payment-results').html(json.results || '');
        },
        error: handleAjaxError
    });
}

// ربط زر الفلترة بتحميل القائمة
$('#button-filter-payment').on('click', function(){
    loadVendorPayments(1);
});

//=========================[ فتح مودال إضافة دفعة (زر أعلى الصفحة) ]=========================//
{% if user_vendor_payment_add %}
$('#btn-add-payment').on('click', function(){
    // 1) إعادة ضبط النموذج
    $('#form-add-payment')[0].reset();
    $('#table-add-payment-invoices tbody').html('<tr><td colspan="4">{{ text_no_results }}</td></tr>');
    // 2) تهيئة الـ select2
    $('.select2').select2();
    initSelect2Vendor('.payment-vendor-select');
    // 3) فتح المودال
    $('#modal-add-payment').modal('show');
});
{% endif %}

//=========================[ إضافة سطر فاتورة في مودال إضافة الدفعة ]=========================//
$('#btn-add-invoice-payment-row').on('click', function(){
    let hasNoRes = $('#table-add-payment-invoices tbody td[colspan="4"]').length > 0;
    if(hasNoRes) {
        $('#table-add-payment-invoices tbody').html('');
    }
    let row = '';
    row += '<tr>';
    row += '  <td><select name="allocated_invoice_id[]" class="form-control select2 invoice-select-payment-row" style="width:100%;"></select></td>';
    row += '  <td><input type="number" name="invoice_amount_due[]" class="form-control" step="0.01" value="0" readonly></td>';
    row += '  <td><input type="number" name="invoice_amount_pay[]" class="form-control" step="0.01" value="0"></td>';
    row += '  <td><button type="button" class="btn btn-danger" onclick="$(this).closest(\'tr\').remove();">';
    row += '    <i class="fa fa-minus-circle"></i></button></td>';
    row += '</tr>';

    $('#table-add-payment-invoices tbody').append(row);
    initSelect2InvoicesPayment();
    $('.select2').select2();
});

//=========================[ حفظ الدفعة (إضافة) ]=========================//
$('#btn-save-add-payment').on('click', function(){
    showLoading();
    $.ajax({
        url: 'index.php?route=purchase/purchase/saveVendorPayment&user_token={{ user_token }}',
        type: 'POST',
        data: $('#form-add-payment').serialize() + '&payment_id=0',
        dataType: 'json',
        success: function(json){
            hideLoading();
            if(json.error) {
                toastr.error(json.error);
            }
            if(json.success) {
                toastr.success(json.success);
                $('#modal-add-payment').modal('hide');
                loadVendorPayments(1);
            }
        },
        error: handleAjaxError
    });
});

//=========================[ عرض تفاصيل الدفعة في مودال منفصل ]=========================//
window.viewVendorPayment = function(id){
    showLoading();
    $.ajax({
        url: 'index.php?route=purchase/purchase/viewVendorPayment&user_token={{ user_token }}&payment_id=' + id,
        type: 'GET',
        dataType: 'html',
        success: function(html){
            hideLoading();
            $('#view-payment-details').html(html);
            $('#modal-view-payment').modal('show');
        },
        error: handleAjaxError
    });
};

//=========================[ فتح مودال التعديل (دفعة) ]=========================//
{% if user_vendor_payment_edit %}
window.openEditPayment = function(id){
    showLoading();
    $.ajax({
        url: 'index.php?route=purchase/purchase/getVendorPaymentForm&user_token={{ user_token }}&payment_id='+id,
        type: 'GET',
        dataType: 'json',
        success: function(json){
            hideLoading();
            // 1) إعادة ضبط النموذج
            $('#form-edit-payment')[0].reset();
            // 2) تعبئة الحقول الأساسية
            $('input[name="payment_id"]').val(json.payment_id || '');
            $('input[name="payment_number"]').val(json.payment_number || '');
            $('input[name="payment_date"]').val(json.payment_date || '');
            $('select[name="payment_method"]').val(json.payment_method || 'cash').trigger('change');
            $('input[name="reference_number"]').val(json.reference_number || '');
            $('input[name="amount"]').val(json.amount || 0);
            $('textarea[name="notes"]').val(json.notes || '');

            // Vendor
            initSelect2Vendor('.payment-vendor-select-edit');
            let $vendSel = $('.payment-vendor-select-edit');
            if(json.vendor_id){
                $.ajax({
                    url: 'index.php?route=purchase/purchase/select2Vendors&user_token={{ user_token }}&vendor_id=' + json.vendor_id,
                    dataType: 'json',
                    success: function(res){
                        if(res && res.length){
                            let vend = res[0];
                            let option = new Option(vend.text, vend.id, true, true);
                            $vendSel.append(option).trigger('change');
                        }
                    }
                });
            }

            // تعبئة الفواتير المخصصة لهذه الدفعة
            let html = '';
            if(json.invoices && json.invoices.length){
                $.each(json.invoices, function(i, inv){
                    html += '<tr>';
                    html += '  <td><select name="allocated_invoice_id[]" class="form-control select2 invoice-select-payment-row-edit" data-invoice="' + (inv.invoice_id || '') + '" style="width:100%;"></select></td>';
                    html += '  <td><input type="number" name="invoice_amount_due[]" class="form-control" step="0.01" value="' + (inv.amount_due || 0) + '" readonly></td>';
                    html += '  <td><input type="number" name="invoice_amount_pay[]" class="form-control" step="0.01" value="' + (inv.amount_pay || 0) + '"></td>';
                    html += '  <td><button type="button" class="btn btn-danger" onclick="$(this).closest(\'tr\').remove();">';
                    html += '    <i class="fa fa-minus-circle"></i></button></td>';
                    html += '</tr>';
                });
            } else {
                html = '<tr><td colspan="4">{{ text_no_results }}</td></tr>';
            }
            $('#table-edit-payment-invoices tbody').html(html);

            // تهيئة الـSelect2
            initSelect2InvoicesPaymentEdit();
            $('.select2').select2();

            // 3) عرض المودال
            $('#modal-edit-payment').modal('show');
        },
        error: handleAjaxError
    });
};

// حفظ التعديل
$('#btn-save-edit-payment').on('click', function(){
    showLoading();
    $.ajax({
        url: 'index.php?route=purchase/purchase/saveVendorPayment&user_token={{ user_token }}',
        type: 'POST',
        data: $('#form-edit-payment').serialize(),
        dataType: 'json',
        success: function(json){
            hideLoading();
            if(json.error) {
                toastr.error(json.error);
            }
            if(json.success) {
                toastr.success(json.success);
                $('#modal-edit-payment').modal('hide');
                loadVendorPayments(1);
            }
        },
        error: handleAjaxError
    });
});
{% endif %}

//=========================[ دالة حذف الدفعة ]=========================//
window.deleteVendorPayment = function(id){
    if(!confirm("{{ text_confirm_delete }}")) return;
    showLoading();
    $.ajax({
        url: 'index.php?route=purchase/purchase/deleteVendorPayment&user_token={{ user_token }}&payment_id=' + id,
        type: 'POST',
        dataType: 'json',
        success: function(json){
            hideLoading();
            if(json.error){
                toastr.error(json.error);
            }
            if(json.success){
                toastr.success(json.success);
                loadVendorPayments(1);
            }
        },
        error: handleAjaxError
    });
};

//=========================[ اعتماد الدفعة (Approve) ]=========================//
{% if user_vendor_payment_approve %}
window.approveVendorPayment = function(id){
    $('#modal-approve-payment input[name="payment_id"]').val(id);
    $('#modal-approve-payment').modal('show');
};

$('#btn-confirm-approve-payment').on('click', function(){
    showLoading();
    $.ajax({
        url: 'index.php?route=purchase/purchase/approveVendorPayment&user_token={{ user_token }}',
        type: 'POST',
        data: $('#form-approve-payment').serialize(),
        dataType: 'json',
        success: function(json){
            hideLoading();
            if(json.error){ toastr.error(json.error); }
            if(json.success){
                toastr.success(json.success);
                $('#modal-approve-payment').modal('hide');
                loadVendorPayments(1);
            }
        },
        error: handleAjaxError
    });
});
{% endif %}

//=========================[ رفض الدفعة (Reject) ]=========================//
{% if user_vendor_payment_reject %}
window.rejectVendorPayment = function(id){
    $('#modal-reject-payment input[name="payment_id"]').val(id);
    $('#modal-reject-payment').modal('show');
};

$('#btn-confirm-reject-payment').on('click', function(){
    showLoading();
    $.ajax({
        url: 'index.php?route=purchase/purchase/rejectVendorPayment&user_token={{ user_token }}',
        type: 'POST',
        data: $('#form-reject-payment').serialize(),
        dataType: 'json',
        success: function(json){
            hideLoading();
            if(json.error){ toastr.error(json.error); }
            if(json.success){
                toastr.success(json.success);
                $('#modal-reject-payment').modal('hide');
                loadVendorPayments(1);
            }
        },
        error: handleAjaxError
    });
});
{% endif %}

//=========================[ تهيئة Select2 للمورد Vendor في الدفعات ]=========================//
function initSelect2Vendor(selector){
    $(selector).select2({
        placeholder: '{{ text_select_vendor }}',
        ajax:{
            url: 'index.php?route=purchase/purchase/select2Vendors&user_token={{ user_token }}',
            dataType: 'json',
            delay: 300,
            data: function(params){
                return { q: params.term };
            },
            processResults: function(data){
                return { results: data };
            }
        }
    });
}

//=========================[ تهيئة Select2 للفواتير في الدفعات ]=========================//
function initSelect2InvoicesPayment(){
    $('.invoice-select-payment-row').select2({
        placeholder: '{{ text_select_invoice }}',
        ajax:{
            url: 'index.php?route=purchase/purchase/select2VendorInvoices&user_token={{ user_token }}',
            dataType: 'json',
            delay: 300,
            data: function(params){
                return {
                    q: params.term,
                    // بإمكاننا معرفة الـvendor المختار
                    vendor_id: $('select[name="vendor_id"]').val() || 0
                };
            },
            processResults: function(data){
                return { results: data };
            }
        }
    }).on('select2:select', function(e){
        let invoiceData = e.params.data;
        let $tr = $(this).closest('tr');
        $tr.find('input[name="invoice_amount_due[]"]').val(invoiceData.amount_due || 0);
        $tr.find('input[name="invoice_amount_pay[]"]').val(invoiceData.amount_due || 0);
    });
}

//=========================[ تهيئة Select2 للفواتير في الدفعات (تحرير) ]=========================//
function initSelect2InvoicesPaymentEdit(){
    $('.invoice-select-payment-row-edit').select2({
        placeholder: '{{ text_select_invoice }}',
        ajax:{
            url: 'index.php?route=purchase/purchase/select2VendorInvoices&user_token={{ user_token }}',
            dataType: 'json',
            delay: 300,
            data: function(params){
                return {
                    q: params.term,
                    vendor_id: $('select[name="vendor_id"]').val() || 0
                };
            },
            processResults: function(data){
                return { results: data };
            }
        }
    })
    .on('select2:select', function(e){
        let invoiceData = e.params.data;
        let $tr = $(this).closest('tr');
        $tr.find('input[name="invoice_amount_due[]"]').val(invoiceData.amount_due || 0);
        $tr.find('input[name="invoice_amount_pay[]"]').val(invoiceData.amount_due || 0);
    })
    .each(function(){
        // تعبئة الفاتورة إن كانت موجودة مسبقًا في data-invoice
        let preInv = $(this).attr('data-invoice') || '';
        if(preInv){
            let $this = $(this);
            $.ajax({
                url: 'index.php?route=purchase/purchase/select2VendorInvoices&user_token={{ user_token }}&invoice_id=' + preInv,
                dataType: 'json',
                success: function(res){
                    if(res && res.length){
                        let inv = res[0];
                        let option = new Option(inv.text, inv.id, true, true);
                        $this.append(option).trigger('change');
                        
                        // تعبئة حقول due/pay
                        let $tr = $this.closest('tr');
                        $tr.find('input[name="invoice_amount_due[]"]').val(inv.amount_due || 0);
                        $tr.find('input[name="invoice_amount_pay[]"]').val(inv.amount_pay || inv.amount_due || 0);
                    }
                }
            });
        }
    });
}

/*******************************
 * نهاية الجزء العاشر (10)
 *******************************/
/***********************************************************
 * الجزء الحادي عشر (11): دوال تبويب المخزون (Inventory)
 ***********************************************************/

//=========================[ تحميل قائمة المخزون مع الفلترة ]=========================//
function loadInventory(page = 1) {
    showLoading();
    let url = 'index.php?route=purchase/purchase/getInventoryList&user_token={{ user_token }}&page=' + page;

    // قراءة الفلاتر من حقول الإدخال
    let filterBranch = $('#filter-inv-branch').val() || '';
    let filterProd   = $('#filter-inv-product').val() || '';
    let filterMove   = $('#filter-inv-movement').val() || '';

    if(filterBranch) url += '&filter_branch=' + encodeURIComponent(filterBranch);
    if(filterProd)   url += '&filter_product=' + encodeURIComponent(filterProd);
    if(filterMove)   url += '&filter_movement=' + encodeURIComponent(filterMove);

    $.ajax({
        url: url,
        type: 'GET',
        dataType: 'json',
        success: function(json) {
            hideLoading();
            let html = '';
            if (json.inventory && json.inventory.length) {
                $.each(json.inventory, function(i, inv) {
                    html += '<tr>';
                    html += '  <td>' + (inv.branch_name || '') + '</td>';
                    html += '  <td>' + (inv.product_name || '') + '</td>';
                    html += '  <td>' + (inv.quantity || '0') + '</td>';
                    html += '  <td>' + (inv.unit_name || '') + '</td>';
                    html += '  <td>' + (inv.is_consignment ? '{{ text_yes }}' : '{{ text_no }}') + '</td>';
                    html += '  <td>' + (inv.consignment_supplier || '') + '</td>';
                    html += '  <td class="text-right">';
                    html += '    <div class="btn-group">';
                    html += '      <button type="button" class="btn btn-sm btn-default dropdown-toggle" data-toggle="dropdown">';
                    html += '        <i class="fa fa-cog"></i> <span class="caret"></span>';
                    html += '      </button>';
                    html += '      <ul class="dropdown-menu dropdown-menu-right">';
                    
                    // زر عرض التفاصيل
                    html += '        <li><a href="#" onclick="viewInventoryDetails(\'' + (inv.product_name || '') + '\', ' 
                                                          + (inv.product_id || 0) + ', ' + (inv.branch_id || 0) + ')">';
                    html += '          <i class="fa fa-eye"></i> {{ text_view_details }}</a></li>';

                    // زر فتح التحويل المخزني
                    {% if user_stock_transfer_add %}
                    html += '        <li><a href="#" onclick="openStockTransfer(' + (inv.product_id || 0) + ', ' + (inv.branch_id || 0) + ')">';
                    html += '          <i class="fa fa-exchange"></i> {{ text_stock_transfer }}</a></li>';
                    {% endif %}

                    // زر فتح التسوية المخزنية
                    {% if user_stock_adjustment_add %}
                    html += '        <li><a href="#" onclick="openStockAdjustment(' + (inv.product_id || 0) + ', ' + (inv.branch_id || 0) + ')">';
                    html += '          <i class="fa fa-sliders"></i> {{ text_stock_adjustment }}</a></li>';
                    {% endif %}

                    html += '      </ul>';
                    html += '    </div>';
                    html += '  </td>';
                    html += '</tr>';
                });
            } else {
                html = '<tr><td class="text-center" colspan="7">{{ text_no_results }}</td></tr>';
            }

            // تعبئة الجدول
            $('#table-inventory tbody').html(html);
            // تعبئة الـPagination والـResults
            $('#inventory-pagination').html(json.pagination || '');
            $('#inventory-results').html(json.results || '');
        },
        error: handleAjaxError
    });
}

// ربط زر الفلترة
$('#button-filter-inventory').on('click', function() {
    loadInventory(1);
});

//=========================[ عرض تفاصيل المخزون في مودال منفصل ]=========================//
window.viewInventoryDetails = function(prodName, prodId, branchId) {
    showLoading();
    $.ajax({
        url: 'index.php?route=purchase/purchase/getInventoryDetails&user_token={{ user_token }}&product_id=' 
              + prodId + '&branch_id=' + branchId,
        type: 'GET',
        dataType: 'html',
        success: function(html) {
            hideLoading();
            $('#inventory-details-body').html(html);
            // تغيير عنوان المودال ليحمل اسم المنتج
            $('#modal-view-inventory-details .modal-title').text('{{ text_view_inventory_details }} - ' + prodName);
            $('#modal-view-inventory-details').modal('show');
        },
        error: handleAjaxError
    });
};

{% if user_stock_transfer_add %}
window.openStockTransfer = function(productId, fromBranchId) {
    // في هذا المثال سنفترض أننا سننتقل مباشرة إلى تاب "Stock Transfer"
    $('a[href="#tab-stock-transfer"]').tab('show');

    // نهيئ الفورم وكأنه تحويل مخزني جديد
    $('#form-transfer')[0].reset();
    $('input[name="transfer_id"]').val('0'); // لإضافة تحويل جديد
    $('select[name="from_branch_id"]').val(fromBranchId).trigger('change');
    $('select[name="to_branch_id"]').val('').trigger('change'); // يختار المستخدم الفرع الوجهة
    $('textarea[name="notes"]').val('');

    // تفريغ سطور الأصناف في جدول التحويل
    $('#table-transfer-items tbody').html('');

    // إضافة سطر واحد بشكل افتراضي للمنتج المعطى:
    let row = '';
    row += '<tr>';
    row += '  <td>';
    row += '    <select name="item_product_id[]" ';
    row += '            class="form-control select2 transfer-product-select" ';
    row += '            data-prod="' + productId + '" ';
    row += '            style="width:100%;"></select>';
    row += '  </td>';
    row += '  <td><input type="number" name="item_quantity[]" class="form-control" value="1"></td>';
    row += '  <td><select name="item_unit_id[]" class="form-control select2 transfer-unit-select"></select></td>';
    row += '  <td><input type="text" name="item_notes[]" class="form-control" value=""></td>';
    row += '  <td>';
    row += '    <button type="button" class="btn btn-danger" onclick="$(this).closest(\'tr\').remove();">';
    row += '      <i class="fa fa-minus-circle"></i>';
    row += '    </button>';
    row += '  </td>';
    row += '</tr>';

    $('#table-transfer-items tbody').append(row);

    // تهيئة الـSelect2 (يفترض دالة initSelect2TransferProducts() موجودة سابقًا)
    initSelect2TransferProducts();

    // إعادة تهيئة أي Select2 آخر بالمودال/التبويب:
    $('.select2').select2();
};
{% endif %}


//=========================[ فتح مودال/تاب التسوية المخزنية من المخزون ]=========================//
{% if user_stock_adjustment_add %}
window.openStockAdjustment = function(productId, branchId) {
    // هنا سنفترض الانتقال لتبويب "Stock Adjustment"
    $('a[href="#tab-stock-adjustment"]').tab('show');

    // تهيئة النموذج كأننا بصدد إضافة تسوية جديدة
    $('#form-adjustment')[0].reset();
    $('input[name="adjustment_id"]').val('0'); // جديدة
    $('select[name="branch_id"]').val(branchId).trigger('change');
    $('select[name="type"]').val('increase').trigger('change'); // افتراضيًا زيادة، أو حسب الحاجة
    $('textarea[name="notes"]').val('');

    // تفريغ سطور الأصناف في جدول التسويات
    $('#table-adjustment-items tbody').html('');

    // إضافة صف افتراضي للمنتج الذي أرسلناه:
    let row = '';
    row += '<tr>';
    row += '  <td>';
    row += '    <select name="item_product_id[]" ';
    row += '            class="form-control select2 adj-product-select" ';
    row += '            data-prod="' + productId + '" ';
    row += '            style="width:100%;"></select>';
    row += '  </td>';
    row += '  <td><input type="number" name="item_quantity[]" class="form-control" value="1"></td>';
    row += '  <td><select name="item_unit_id[]" class="form-control select2 adj-unit-select"></select></td>';
    row += '  <td><input type="text" name="item_reason[]" class="form-control" value=""></td>';
    row += '  <td>';
    row += '    <button type="button" class="btn btn-danger" onclick="$(this).closest(\'tr\').remove();">';
    row += '      <i class="fa fa-minus-circle"></i>';
    row += '    </button>';
    row += '  </td>';
    row += '</tr>';

    $('#table-adjustment-items tbody').append(row);

    // تهيئة الـSelect2 (يفترض وجود دالة initSelect2AdjustmentProducts() تعرف كيف تتعامل مع .adj-product-select)
    initSelect2AdjustmentProducts();

    // إعادة تهيئة أي Select2 آخر في نفس المودال/التبويب:
    $('.select2').select2();
};
{% endif %}

/*******************************
 * نهاية الجزء الحادي عشر (11)
 *******************************/
/****************************************************************************
 * =======================[ TAB 9: Purchase Returns ]========================
 ****************************************************************************/
function loadPurchaseReturns(page = 1) {
    showLoading();
    let url = 'index.php?route=purchase/purchase/getPurchaseReturnList&user_token={{ user_token }}&page=' + page;

    // جمع فلاتر البحث
    let retNumber = $('#filter-return-number').val() || '';
    let vendor    = $('#filter-return-vendor').val()  || '';
    let status    = $('#filter-return-status').val()  || '';

    if(retNumber) url += '&filter_return_number=' + encodeURIComponent(retNumber);
    if(vendor)    url += '&filter_vendor='        + encodeURIComponent(vendor);
    if(status)    url += '&filter_status='        + encodeURIComponent(status);

    $.ajax({
        url: url,
        type: 'GET',
        dataType: 'json',
        success: function(json) {
            hideLoading();
            let html = '';
            if(json.returns && json.returns.length) {
                $.each(json.returns, function(i, ret){
                    html += '<tr>';
                    html += '  <td class="text-center"><input type="checkbox" value="'+ (ret.return_id || '') +'"></td>';
                    html += '  <td>' + (ret.return_number || '') + '</td>';
                    html += '  <td>' + (ret.vendor_name || '') + '</td>';
                    html += '  <td>' + (ret.status || '') + '</td>';
                    html += '  <td>' + (ret.date_added || '') + '</td>';
                    html += '  <td class="text-right">';
                    html += '    <div class="btn-group">';
                    html += '      <button type="button" class="btn btn-sm btn-default dropdown-toggle" data-toggle="dropdown">';
                    html += '        <i class="fa fa-cog"></i> <span class="caret"></span>';
                    html += '      </button>';
                    html += '      <ul class="dropdown-menu dropdown-menu-right">';

                    // زر فتح المودال (تعديل)
                    {% if user_purchase_return_edit %}
                    html += '        <li><a href="#" onclick="openEditPurchaseReturn(' + ret.return_id + ')">';
                    html += '          <i class="fa fa-pencil"></i> {{ button_edit }}</a></li>';
                    {% endif %}

                    // زر الحذف
                    {% if user_purchase_return_delete %}
                    html += '        <li><a href="#" onclick="deletePurchaseReturn(' + ret.return_id + ')">';
                    html += '          <i class="fa fa-trash"></i> {{ button_delete }}</a></li>';
                    {% endif %}

                    // زر الموافقة (Approve)
                    html += '        <li><a href="#" onclick="approvePurchaseReturn(' + ret.return_id + ')">';
                    html += '          <i class="fa fa-check"></i> {{ text_approve }}</a></li>';

                    // زر الرفض (Reject)
                    html += '        <li><a href="#" onclick="rejectPurchaseReturn(' + ret.return_id + ')">';
                    html += '          <i class="fa fa-ban"></i> {{ text_reject }}</a></li>';

                    html += '      </ul>';
                    html += '    </div>';
                    html += '  </td>';
                    html += '</tr>';
                });
            } else {
                html = '<tr><td class="text-center" colspan="6">{{ text_no_results }}</td></tr>';
            }
            $('#table-purchase-return tbody').html(html);
            $('#purchase-return-pagination').html(json.pagination || '');
            $('#purchase-return-results').html(json.results || '');
        },
        error: handleAjaxError
    });
}

// عند الضغط على زر الفلترة
$('#button-filter-return').on('click', function(){
    loadPurchaseReturns(1);
});

// فتح مودال إضافة/تعديل Purchase Return
window.openEditPurchaseReturn = function(return_id) {
    // مبدئيًا نحمّل البيانات إن كانت تعديل (return_id != 0)
    if(!return_id) return_id = 0;
    showLoading();
    $.ajax({
        url: 'index.php?route=purchase/purchase/getPurchaseReturnForm&user_token={{ user_token }}&return_id=' + return_id,
        type: 'GET',
        dataType: 'json',
        success: function(json){
            hideLoading();
            // تفريغ النموذج
            $('#form-purchase-return')[0].reset();
            $('input[name="return_id"]').val(json.return_id || 0);
            $('input[name="return_number"]').val(json.return_number || '');
            $('select[name="supplier_id"]').val(json.supplier_id || '').trigger('change');
            $('input[name="return_date"]').val(json.return_date || '');
            $('select[name="purchase_order_id"]').val(json.purchase_order_id || '').trigger('change');
            $('select[name="goods_receipt_id"]').val(json.goods_receipt_id || '').trigger('change');
            $('textarea[name="notes"]').val(json.notes || '');

            // تعبئة الأصناف
            let html = '';
            if(json.items && json.items.length){
                $.each(json.items, function(i, item){
                    html += '<tr>';
                    html += '  <td>' + (item.product_name || '') + '</td>';
                    html += '  <td><input type="number" name="item_quantity[]" class="form-control" value="' + (item.quantity||1) + '"></td>';
                    html += '  <td><input type="text" name="item_unit[]" class="form-control" value="' + (item.unit||'') + '" readonly></td>';
                    html += '  <td><input type="number" name="item_price[]" class="form-control" step="0.01" value="' + (item.price||0) + '"></td>';
                    html += '  <td><input type="number" name="item_total[]" class="form-control" step="0.01" value="' + (item.total||0) + '" readonly></td>';
                    html += '  <td><input type="text" name="item_reason[]" class="form-control" value="' + (item.reason||'') + '"></td>';
                    html += '  <td><button type="button" class="btn btn-danger" onclick="$(this).closest(\'tr\').remove();">';
                    html += '    <i class="fa fa-minus-circle"></i></button></td>';
                    html += '</tr>';
                });
            } else {
                html = '<tr><td colspan="7">{{ text_no_results }}</td></tr>';
            }
            $('#table-purchase-return-items tbody').html(html);

            // عرض المودال
            $('#modal-edit-purchase-return').modal('show');
            $('.select2').select2();
        },
        error: handleAjaxError
    });
};

// حفظ الـPurchase Return (سواء إضافة أو تعديل)
$('#btn-save-purchase-return').on('click', function(){
    showLoading();
    $.ajax({
        url: 'index.php?route=purchase/purchase/savePurchaseReturn&user_token={{ user_token }}',
        type: 'POST',
        data: $('#form-purchase-return').serialize(),
        dataType: 'json',
        success: function(json){
            hideLoading();
            if(json.error){ toastr.error(json.error); }
            if(json.success){
                toastr.success(json.success);
                $('#modal-edit-purchase-return').modal('hide');
                loadPurchaseReturns(1);
            }
        },
        error: handleAjaxError
    });
});

// حذف الـPurchase Return
window.deletePurchaseReturn = function(return_id){
    if(!confirm("{{ text_confirm_delete }}")) return;
    showLoading();
    $.ajax({
        url:'index.php?route=purchase/purchase/deletePurchaseReturn&user_token={{ user_token }}&return_id='+return_id,
        type:'POST',
        dataType:'json',
        success:function(json){
            hideLoading();
            if(json.error){ toastr.error(json.error); }
            if(json.success){
                toastr.success(json.success);
                loadPurchaseReturns(1);
            }
        },
        error: handleAjaxError
    });
};

// الموافقة على إرجاع الشراء
window.approvePurchaseReturn = function(return_id){
    $('#modal-approve-purchase-return input[name="return_id"]').val(return_id);
    $('#modal-approve-purchase-return').modal('show');
};
$('#btn-confirm-approve-purchase-return').on('click', function(){
    showLoading();
    $.ajax({
        url:'index.php?route=purchase/purchase/approvePurchaseReturn&user_token={{ user_token }}',
        type:'POST',
        data: $('#form-approve-purchase-return').serialize(),
        dataType:'json',
        success:function(json){
            hideLoading();
            if(json.error){ toastr.error(json.error); }
            if(json.success){
                toastr.success(json.success);
                $('#modal-approve-purchase-return').modal('hide');
                loadPurchaseReturns(1);
            }
        },
        error: handleAjaxError
    });
});

// رفض إرجاع الشراء
window.rejectPurchaseReturn = function(return_id){
    $('#modal-reject-purchase-return input[name="return_id"]').val(return_id);
    $('#modal-reject-purchase-return').modal('show');
};
$('#btn-confirm-reject-purchase-return').on('click', function(){
    showLoading();
    $.ajax({
        url:'index.php?route=purchase/purchase/rejectPurchaseReturn&user_token={{ user_token }}',
        type:'POST',
        data: $('#form-reject-purchase-return').serialize(),
        dataType:'json',
        success:function(json){
            hideLoading();
            if(json.error){ toastr.error(json.error); }
            if(json.success){
                toastr.success(json.success);
                $('#modal-reject-purchase-return').modal('hide');
                loadPurchaseReturns(1);
            }
        },
        error: handleAjaxError
    });
});

/****************************************************************************
 * =======================[ TAB 10: Stock Adjustments ]=======================
 ****************************************************************************/
function loadAdjustments(page = 1){
    showLoading();
    let url = 'index.php?route=purchase/purchase/getStockAdjustmentList&user_token={{ user_token }}&page=' + page;

    let adjNumber = $('#filter-adjustment-number').val() || '';
    let status    = $('#filter-adjustment-status').val() || '';

    if(adjNumber) url += '&filter_adjustment_number=' + encodeURIComponent(adjNumber);
    if(status)    url += '&filter_status=' + encodeURIComponent(status);

    $.ajax({
        url: url,
        type: 'GET',
        dataType: 'json',
        success: function(json){
            hideLoading();
            let html = '';
            if(json.adjustments && json.adjustments.length){
                $.each(json.adjustments, function(i, adj){
                    html += '<tr>';
                    html += '  <td class="text-center"><input type="checkbox" value="'+ (adj.adjustment_id || '') +'"></td>';
                    html += '  <td>' + (adj.adjustment_number || '') + '</td>';
                    html += '  <td>' + (adj.branch_name || '') + '</td>';
                    html += '  <td>' + (adj.type || '') + '</td>';
                    html += '  <td>' + (adj.status || '') + '</td>';
                    html += '  <td>' + (adj.date_added || '') + '</td>';
                    html += '  <td class="text-right">';
                    html += '    <div class="btn-group">';
                    html += '      <button type="button" class="btn btn-sm btn-default dropdown-toggle" data-toggle="dropdown">';
                    html += '        <i class="fa fa-cog"></i> <span class="caret"></span>';
                    html += '      </button>';
                    html += '      <ul class="dropdown-menu dropdown-menu-right">';

                    // زر التعديل
                    {% if user_stock_adjustment_edit %}
                    html += '        <li><a href="#" onclick="openEditAdjustment(' + adj.adjustment_id + ')">';
                    html += '          <i class="fa fa-pencil"></i> {{ button_edit }}</a></li>';
                    {% endif %}

                    // زر الحذف
                    {% if user_stock_adjustment_delete %}
                    html += '        <li><a href="#" onclick="deleteAdjustment(' + adj.adjustment_id + ')">';
                    html += '          <i class="fa fa-trash"></i> {{ button_delete }}</a></li>';
                    {% endif %}

                    // زر Approve
                    html += '        <li><a href="#" onclick="approveAdjustment(' + adj.adjustment_id + ')">';
                    html += '          <i class="fa fa-check"></i> {{ text_approve }}</a></li>';

                    // زر Cancel Adjustment
                    html += '        <li><a href="#" onclick="cancelAdjustment(' + adj.adjustment_id + ')">';
                    html += '          <i class="fa fa-ban"></i> {{ text_cancel_adjustment }}</a></li>';

                    html += '      </ul>';
                    html += '    </div>';
                    html += '  </td>';
                    html += '</tr>';
                });
            } else {
                html = '<tr><td class="text-center" colspan="7">{{ text_no_results }}</td></tr>';
            }
            $('#table-adjustment tbody').html(html);
            $('#adjustment-pagination').html(json.pagination || '');
            $('#adjustment-results').html(json.results || '');
        },
        error: handleAjaxError
    });
}

// زر الفلترة
$('#button-filter-adjustment').on('click', function(){
    loadAdjustments(1);
});

// فتح مودال تعديل/إضافة Stock Adjustment
window.openEditAdjustment = function(adjustment_id){
    if(!adjustment_id) adjustment_id = 0;
    showLoading();
    $.ajax({
        url: 'index.php?route=purchase/purchase/getStockAdjustmentForm&user_token={{ user_token }}&adjustment_id=' + adjustment_id,
        type: 'GET',
        dataType: 'json',
        success: function(json){
            hideLoading();
            // تفريغ النموذج
            $('#form-adjustment')[0].reset();
            $('input[name="adjustment_id"]').val(json.adjustment_id || 0);
            $('input[name="adjustment_number"]').val(json.adjustment_number || '');
            $('select[name="branch_id"]').val(json.branch_id || '').trigger('change');
            $('select[name="type"]').val(json.type || 'increase').trigger('change');
            $('textarea[name="notes"]').val(json.notes || '');

            // تعبئة الأصناف
            let html = '';
            if(json.items && json.items.length){
                $.each(json.items, function(i, item){
                    html += '<tr>';
                    html += '  <td>' + (item.product_name || '') + '</td>';
                    html += '  <td><input type="number" name="item_quantity[]" class="form-control" value="' + (item.quantity||1) + '"></td>';
                    html += '  <td><input type="text" class="form-control" value="' + (item.unit||'') + '" readonly></td>';
                    html += '  <td><input type="text" name="item_reason[]" class="form-control" value="' + (item.reason||'') + '"></td>';
                    html += '  <td><button type="button" class="btn btn-danger" onclick="$(this).closest(\'tr\').remove();">';
                    html += '    <i class="fa fa-minus-circle"></i></button></td>';
                    html += '</tr>';
                });
            } else {
                html = '<tr><td colspan="5">{{ text_no_results }}</td></tr>';
            }
            $('#table-adjustment-items tbody').html(html);

            // عرض المودال
            $('#modal-edit-adjustment').modal('show');
            $('.select2').select2();
        },
        error: handleAjaxError
    });
};

// حفظ التسوية
$('#btn-save-adjustment').on('click', function(){
    showLoading();
    $.ajax({
        url: 'index.php?route=purchase/purchase/saveStockAdjustment&user_token={{ user_token }}',
        type: 'POST',
        data: $('#form-adjustment').serialize(),
        dataType: 'json',
        success: function(json){
            hideLoading();
            if(json.error) { toastr.error(json.error); }
            if(json.success){
                toastr.success(json.success);
                $('#modal-edit-adjustment').modal('hide');
                loadAdjustments(1);
            }
        },
        error: handleAjaxError
    });
});

// حذف التسوية
window.deleteAdjustment = function(adjustment_id){
    if(!confirm("{{ text_confirm_delete }}")) return;
    showLoading();
    $.ajax({
        url: 'index.php?route=purchase/purchase/deleteStockAdjustment&user_token={{ user_token }}&adjustment_id='+adjustment_id,
        type: 'POST',
        dataType: 'json',
        success: function(json){
            hideLoading();
            if(json.error){ toastr.error(json.error); }
            if(json.success){
                toastr.success(json.success);
                loadAdjustments(1);
            }
        },
        error: handleAjaxError
    });
};

// اعتماد التسوية
window.approveAdjustment = function(adjustment_id){
    $('#modal-approve-adjustment input[name="adjustment_id"]').val(adjustment_id);
    $('#modal-approve-adjustment').modal('show');
};
$('#btn-confirm-approve-adjustment').on('click', function(){
    showLoading();
    $.ajax({
        url: 'index.php?route=purchase/purchase/approveStockAdjustment&user_token={{ user_token }}',
        type: 'POST',
        data: $('#form-approve-adjustment').serialize(),
        dataType: 'json',
        success: function(json){
            hideLoading();
            if(json.error){ toastr.error(json.error); }
            if(json.success){
                toastr.success(json.success);
                $('#modal-approve-adjustment').modal('hide');
                loadAdjustments(1);
            }
        },
        error: handleAjaxError
    });
});

// إلغاء التسوية (Cancel)
window.cancelAdjustment = function(adjustment_id){
    $('#modal-cancel-adjustment input[name="adjustment_id"]').val(adjustment_id);
    $('#modal-cancel-adjustment').modal('show');
};
$('#btn-confirm-cancel-adjustment').on('click', function(){
    showLoading();
    $.ajax({
        url: 'index.php?route=purchase/purchase/cancelStockAdjustment&user_token={{ user_token }}',
        type: 'POST',
        data: $('#form-cancel-adjustment').serialize(),
        dataType: 'json',
        success: function(json){
            hideLoading();
            if(json.error){ toastr.error(json.error); }
            if(json.success){
                toastr.success(json.success);
                $('#modal-cancel-adjustment').modal('hide');
                loadAdjustments(1);
            }
        },
        error: handleAjaxError
    });
});

/****************************************************************************
 * =======================[ TAB 11: Stock Transfers ]========================
 ****************************************************************************/
function loadTransfers(page = 1) {
    showLoading();
    let url = 'index.php?route=purchase/purchase/getStockTransferList&user_token={{ user_token }}&page=' + page;

    let transferNumber = $('#filter-transfer-number').val() || '';
    let fromBranch     = $('#filter-transfer-from-branch').val() || '';
    let toBranch       = $('#filter-transfer-to-branch').val()   || '';

    if(transferNumber) url += '&filter_transfer_number=' + encodeURIComponent(transferNumber);
    if(fromBranch)     url += '&filter_from_branch='     + encodeURIComponent(fromBranch);
    if(toBranch)       url += '&filter_to_branch='       + encodeURIComponent(toBranch);

    $.ajax({
        url: url,
        type: 'GET',
        dataType: 'json',
        success: function(json){
            hideLoading();
            let html = '';
            if(json.transfers && json.transfers.length){
                $.each(json.transfers, function(i, trn){
                    html += '<tr>';
                    html += '  <td class="text-center"><input type="checkbox" value="'+ (trn.transfer_id || '') +'"></td>';
                    html += '  <td>' + (trn.transfer_number || '') + '</td>';
                    html += '  <td>' + (trn.from_branch_name || '') + '</td>';
                    html += '  <td>' + (trn.to_branch_name || '') + '</td>';
                    html += '  <td>' + (trn.status || '') + '</td>';
                    html += '  <td>' + (trn.date_added || '') + '</td>';
                    html += '  <td class="text-right">';
                    html += '    <div class="btn-group">';
                    html += '      <button type="button" class="btn btn-sm btn-default dropdown-toggle" data-toggle="dropdown">';
                    html += '        <i class="fa fa-cog"></i> <span class="caret"></span>';
                    html += '      </button>';
                    html += '      <ul class="dropdown-menu dropdown-menu-right">';

                    // زر التعديل
                    {% if user_stock_transfer_edit %}
                    html += '        <li><a href="#" onclick="openEditTransfer(' + trn.transfer_id + ')">';
                    html += '          <i class="fa fa-pencil"></i> {{ button_edit }}</a></li>';
                    {% endif %}

                    // زر الحذف
                    {% if user_stock_transfer_delete %}
                    html += '        <li><a href="#" onclick="deleteTransfer(' + trn.transfer_id + ')">';
                    html += '          <i class="fa fa-trash"></i> {{ button_delete }}</a></li>';
                    {% endif %}

                    // زر الموافقة (Approve)
                    html += '        <li><a href="#" onclick="approveTransfer(' + trn.transfer_id + ')">';
                    html += '          <i class="fa fa-check"></i> {{ text_approve }}</a></li>';

                    // زر الرفض (Reject)
                    html += '        <li><a href="#" onclick="rejectTransfer(' + trn.transfer_id + ')">';
                    html += '          <i class="fa fa-ban"></i> {{ text_reject }}</a></li>';

                    html += '      </ul>';
                    html += '    </div>';
                    html += '  </td>';
                    html += '</tr>';
                });
            } else {
                html = '<tr><td class="text-center" colspan="7">{{ text_no_results }}</td></tr>';
            }
            $('#table-transfer tbody').html(html);
            $('#transfer-pagination').html(json.pagination || '');
            $('#transfer-results').html(json.results || '');
        },
        error: handleAjaxError
    });
}

// زر الفلترة
$('#button-filter-transfer').on('click', function(){
    loadTransfers(1);
});

// فتح مودال (أو التبويب) تعديل/إضافة تحويل
window.openEditTransfer = function(transfer_id){
    if(!transfer_id) transfer_id = 0;
    showLoading();
    $.ajax({
        url: 'index.php?route=purchase/purchase/getStockTransferForm&user_token={{ user_token }}&transfer_id=' + transfer_id,
        type: 'GET',
        dataType: 'json',
        success: function(json){
            hideLoading();
            // إعادة ضبط الفورم
            $('#form-transfer')[0].reset();
            $('input[name="transfer_id"]').val(json.transfer_id || 0);
            $('input[name="transfer_number"]').val(json.transfer_number || '');
            $('select[name="from_branch_id"]').val(json.from_branch_id || '').trigger('change');
            $('select[name="to_branch_id"]').val(json.to_branch_id || '').trigger('change');
            $('textarea[name="notes"]').val(json.notes || '');

            // تعبئة الأصناف
            let html = '';
            if(json.items && json.items.length){
                $.each(json.items, function(i, item){
                    html += '<tr>';
                    html += '  <td>' + (item.product_name || '') + '</td>';
                    html += '  <td><input type="number" name="item_quantity[]" class="form-control" value="' + (item.quantity||1) + '"></td>';
                    html += '  <td><input type="text" class="form-control" value="' + (item.unit||'') + '" readonly></td>';
                    html += '  <td><input type="text" name="item_notes[]" class="form-control" value="' + (item.notes||'') + '"></td>';
                    html += '  <td><button type="button" class="btn btn-danger" onclick="$(this).closest(\'tr\').remove();">';
                    html += '    <i class="fa fa-minus-circle"></i></button></td>';
                    html += '</tr>';
                });
            } else {
                html = '<tr><td colspan="5">{{ text_no_results }}</td></tr>';
            }
            $('#table-transfer-items tbody').html(html);

            $('#modal-edit-transfer').modal('show');
            $('.select2').select2();
        },
        error: handleAjaxError
    });
};

// حفظ التحويل
$('#btn-save-transfer').on('click', function(){
    showLoading();
    $.ajax({
        url: 'index.php?route=purchase/purchase/saveStockTransfer&user_token={{ user_token }}',
        type: 'POST',
        data: $('#form-transfer').serialize(),
        dataType: 'json',
        success: function(json){
            hideLoading();
            if(json.error) { toastr.error(json.error); }
            if(json.success){
                toastr.success(json.success);
                $('#modal-edit-transfer').modal('hide');
                loadTransfers(1);
            }
        },
        error: handleAjaxError
    });
});

// حذف التحويل
window.deleteTransfer = function(transfer_id){
    if(!confirm("{{ text_confirm_delete }}")) return;
    showLoading();
    $.ajax({
        url: 'index.php?route=purchase/purchase/deleteStockTransfer&user_token={{ user_token }}&transfer_id=' + transfer_id,
        type: 'POST',
        dataType: 'json',
        success: function(json){
            hideLoading();
            if(json.error){ toastr.error(json.error); }
            if(json.success){
                toastr.success(json.success);
                loadTransfers(1);
            }
        },
        error: handleAjaxError
    });
};

// الموافقة على التحويل
window.approveTransfer = function(transfer_id){
    $('#modal-approve-transfer input[name="transfer_id"]').val(transfer_id);
    $('#modal-approve-transfer').modal('show');
};
$('#btn-confirm-approve-transfer').on('click', function(){
    showLoading();
    $.ajax({
        url:'index.php?route=purchase/purchase/approveStockTransfer&user_token={{ user_token }}',
        type:'POST',
        data: $('#form-approve-transfer').serialize(),
        dataType:'json',
        success:function(json){
            hideLoading();
            if(json.error){ toastr.error(json.error); }
            if(json.success){
                toastr.success(json.success);
                $('#modal-approve-transfer').modal('hide');
                loadTransfers(1);
            }
        },
        error: handleAjaxError
    });
});

// رفض التحويل
window.rejectTransfer = function(transfer_id){
    $('#modal-reject-transfer input[name="transfer_id"]').val(transfer_id);
    $('#modal-reject-transfer').modal('show');
};
$('#btn-confirm-reject-transfer').on('click', function(){
    showLoading();
    $.ajax({
        url:'index.php?route=purchase/purchase/rejectStockTransfer&user_token={{ user_token }}',
        type:'POST',
        data: $('#form-reject-transfer').serialize(),
        dataType:'json',
        success:function(json){
            hideLoading();
            if(json.error){ toastr.error(json.error); }
            if(json.success){
                toastr.success(json.success);
                $('#modal-reject-transfer').modal('hide');
                loadTransfers(1);
            }
        },
        error: handleAjaxError
    });
});

/****************************************************************************
 * =======================[ TAB 12: Quality Inspection ]=====================
 ****************************************************************************/
function loadInspections(page = 1){
    showLoading();
    let url = 'index.php?route=purchase/purchase/getQualityInspectionList&user_token={{ user_token }}&page=' + page;

    let inspectionNumber = $('#filter-inspection-number').val() || '';
    let status           = $('#filter-inspection-status').val()   || '';

    if(inspectionNumber) url += '&filter_inspection_number=' + encodeURIComponent(inspectionNumber);
    if(status)           url += '&filter_status=' + encodeURIComponent(status);

    $.ajax({
        url: url,
        type: 'GET',
        dataType: 'json',
        success: function(json){
            hideLoading();
            let html = '';
            if(json.inspections && json.inspections.length){
                $.each(json.inspections, function(i, insp){
                    html += '<tr>';
                    html += '  <td class="text-center"><input type="checkbox" value="'+ (insp.inspection_id || '') +'"></td>';
                    html += '  <td>' + (insp.inspection_number || '') + '</td>';
                    html += '  <td>' + (insp.gr_number || '') + '</td>';
                    html += '  <td>' + (insp.inspector_name || '') + '</td>';
                    html += '  <td>' + (insp.status || '') + '</td>';
                    html += '  <td>' + (insp.date_added || '') + '</td>';
                    html += '  <td class="text-right">';
                    html += '    <div class="btn-group">';
                    html += '      <button type="button" class="btn btn-sm btn-default dropdown-toggle" data-toggle="dropdown">';
                    html += '        <i class="fa fa-cog"></i> <span class="caret"></span>';
                    html += '      </button>';
                    html += '      <ul class="dropdown-menu dropdown-menu-right">';

                    // زر التعديل
                    {% if user_quality_inspection_edit %}
                    html += '        <li><a href="#" onclick="openEditInspection(' + insp.inspection_id + ')">';
                    html += '          <i class="fa fa-pencil"></i> {{ button_edit }}</a></li>';
                    {% endif %}

                    // زر الحذف
                    {% if user_quality_inspection_delete %}
                    html += '        <li><a href="#" onclick="deleteInspection(' + insp.inspection_id + ')">';
                    html += '          <i class="fa fa-trash"></i> {{ button_delete }}</a></li>';
                    {% endif %}

                    // زر الاعتماد
                    html += '        <li><a href="#" onclick="approveInspection(' + insp.inspection_id + ')">';
                    html += '          <i class="fa fa-check"></i> {{ text_approve }}</a></li>';

                    // زر الرفض
                    html += '        <li><a href="#" onclick="rejectInspection(' + insp.inspection_id + ')">';
                    html += '          <i class="fa fa-ban"></i> {{ text_reject }}</a></li>';

                    html += '      </ul>';
                    html += '    </div>';
                    html += '  </td>';
                    html += '</tr>';
                });
            } else {
                html = '<tr><td class="text-center" colspan="7">{{ text_no_results }}</td></tr>';
            }
            $('#table-inspection tbody').html(html);
            $('#inspection-pagination').html(json.pagination || '');
            $('#inspection-results').html(json.results || '');
        },
        error: handleAjaxError
    });
}

// زر الفلترة
$('#button-filter-inspection').on('click', function(){
    loadInspections(1);
});

// فتح مودال / تبويب تعديل أو إضافة Inspection
window.openEditInspection = function(inspection_id){
    if(!inspection_id) inspection_id = 0;
    showLoading();
    $.ajax({
        url: 'index.php?route=purchase/purchase/getQualityInspectionForm&user_token={{ user_token }}&inspection_id=' + inspection_id,
        type: 'GET',
        dataType: 'json',
        success: function(json){
            hideLoading();
            // إعادة ضبط النموذج
            $('#form-inspection')[0].reset();
            $('input[name="inspection_id"]').val(json.inspection_id || 0);
            $('input[name="inspection_number"]').val(json.inspection_number || '');
            $('select[name="goods_receipt_id"]').val(json.goods_receipt_id || '').trigger('change');
            $('input[name="inspection_date"]').val(json.inspection_date || '');
            $('textarea[name="notes"]').val(json.notes || '');

            // الأصناف
            let html = '';
            if(json.items && json.items.length){
                $.each(json.items, function(i, item){
                    html += '<tr>';
                    html += '  <td>' + (item.product_name || '') + '</td>';
                    html += '  <td><input type="number" name="item_quantity[]" class="form-control" value="'+ (item.quantity||1) +'"></td>';
                    html += '  <td><input type="text" class="form-control" value="'+ (item.unit||'') +'" readonly></td>';
                    html += '  <td><select name="item_quality_result[]" class="form-control">';
                    html += '    <option value="pending" '+ (item.quality_result=='pending'?'selected':'') +'>{{ text_pending }}</option>';
                    html += '    <option value="passed"  '+ (item.quality_result=='passed'?'selected':'')  +'>{{ text_passed }}</option>';
                    html += '    <option value="failed"  '+ (item.quality_result=='failed'?'selected':'')  +'>{{ text_failed }}</option>';
                    html += '  </select></td>';
                    html += '  <td><input type="text" name="item_remarks[]" class="form-control" value="'+ (item.remarks||'') +'"></td>';
                    html += '  <td><button type="button" class="btn btn-danger" onclick="$(this).closest(\'tr\').remove();">';
                    html += '    <i class="fa fa-minus-circle"></i></button></td>';
                    html += '</tr>';
                });
            } else {
                html = '<tr><td colspan="6">{{ text_no_results }}</td></tr>';
            }
            $('#table-inspection-items tbody').html(html);

            $('#modal-edit-inspection').modal('show');
            $('.select2').select2();
        },
        error: handleAjaxError
    });
};

// حفظ الـInspection
$('#btn-save-inspection').on('click', function(){
    showLoading();
    $.ajax({
        url: 'index.php?route=purchase/purchase/saveQualityInspection&user_token={{ user_token }}',
        type: 'POST',
        data: $('#form-inspection').serialize(),
        dataType: 'json',
        success: function(json){
            hideLoading();
            if(json.error) { toastr.error(json.error); }
            if(json.success){
                toastr.success(json.success);
                $('#modal-edit-inspection').modal('hide');
                loadInspections(1);
            }
        },
        error: handleAjaxError
    });
});

// حذف الـInspection
window.deleteInspection = function(inspection_id){
    if(!confirm("{{ text_confirm_delete }}")) return;
    showLoading();
    $.ajax({
        url:'index.php?route=purchase/purchase/deleteQualityInspection&user_token={{ user_token }}&inspection_id='+inspection_id,
        type:'POST',
        dataType:'json',
        success:function(json){
            hideLoading();
            if(json.error){ toastr.error(json.error); }
            if(json.success){
                toastr.success(json.success);
                loadInspections(1);
            }
        },
        error: handleAjaxError
    });
};

// اعتماد الفحص
window.approveInspection = function(inspection_id){
    $('#modal-approve-inspection input[name="inspection_id"]').val(inspection_id);
    $('#modal-approve-inspection').modal('show');
};
$('#btn-confirm-approve-inspection').on('click', function(){
    showLoading();
    $.ajax({
        url:'index.php?route=purchase/purchase/approveQualityInspection&user_token={{ user_token }}',
        type:'POST',
        data: $('#form-approve-inspection').serialize(),
        dataType:'json',
        success:function(json){
            hideLoading();
            if(json.error){ toastr.error(json.error); }
            if(json.success){
                toastr.success(json.success);
                $('#modal-approve-inspection').modal('hide');
                loadInspections(1);
            }
        },
        error: handleAjaxError
    });
});

// رفض الفحص
window.rejectInspection = function(inspection_id){
    $('#modal-reject-inspection input[name="inspection_id"]').val(inspection_id);
    $('#modal-reject-inspection').modal('show');
};
$('#btn-confirm-reject-inspection').on('click', function(){
    showLoading();
    $.ajax({
        url:'index.php?route=purchase/purchase/rejectQualityInspection&user_token={{ user_token }}',
        type:'POST',
        data: $('#form-reject-inspection').serialize(),
        dataType:'json',
        success:function(json){
            hideLoading();
            if(json.error){ toastr.error(json.error); }
            if(json.success){
                toastr.success(json.success);
                $('#modal-reject-inspection').modal('hide');
                loadInspections(1);
            }
        },
        error: handleAjaxError
    });
});

})(jQuery);
</script>
{{ footer }}

