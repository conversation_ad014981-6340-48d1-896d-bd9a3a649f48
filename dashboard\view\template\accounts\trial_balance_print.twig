{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="submit" form="accounts\trial_balance_advanced-form" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fa fa-save"></i></button>
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="close" data-dismiss="alert">&times;</button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="close" data-dismiss="alert">&times;</button>
      </div>
    {{% endif %}}
    
    <div class="panel panel-default">
      <div class="panel-heading"><i class="fa fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}bar-chart{% elif screen_type == 'settings' %}cog{% else %}pencil{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="panel-body">
        <form id="accounts\trial_balance_advanced-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-account_types">{{ text_account_types }}</label>
            <div class="col-sm-10">
              <input type="text" name="account_types" value="{{ account_types }}" placeholder="{{ text_account_types }}" id="input-account_types" class="form-control" />
              {% if error_account_types %}
                <div class="text-danger">{{ error_account_types }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-accounts">{{ text_accounts }}</label>
            <div class="col-sm-10">
              <input type="text" name="accounts" value="{{ accounts }}" placeholder="{{ text_accounts }}" id="input-accounts" class="form-control" />
              {% if error_accounts %}
                <div class="text-danger">{{ error_accounts }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-analysis_url">{{ text_analysis_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="analysis_url" value="{{ analysis_url }}" placeholder="{{ text_analysis_url }}" id="input-analysis_url" class="form-control" />
              {% if error_analysis_url %}
                <div class="text-danger">{{ error_analysis_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-back_url">{{ text_back_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="back_url" value="{{ back_url }}" placeholder="{{ text_back_url }}" id="input-back_url" class="form-control" />
              {% if error_back_url %}
                <div class="text-danger">{{ error_back_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-balance_check">{{ text_balance_check }}</label>
            <div class="col-sm-10">
              <input type="text" name="balance_check" value="{{ balance_check }}" placeholder="{{ text_balance_check }}" id="input-balance_check" class="form-control" />
              {% if error_balance_check %}
                <div class="text-danger">{{ error_balance_check }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-company_address">{{ text_company_address }}</label>
            <div class="col-sm-10">
              <input type="text" name="company_address" value="{{ company_address }}" placeholder="{{ text_company_address }}" id="input-company_address" class="form-control" />
              {% if error_company_address %}
                <div class="text-danger">{{ error_company_address }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-company_email">{{ text_company_email }}</label>
            <div class="col-sm-10">
              <input type="text" name="company_email" value="{{ company_email }}" placeholder="{{ text_company_email }}" id="input-company_email" class="form-control" />
              {% if error_company_email %}
                <div class="text-danger">{{ error_company_email }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-company_name">{{ text_company_name }}</label>
            <div class="col-sm-10">
              <input type="text" name="company_name" value="{{ company_name }}" placeholder="{{ text_company_name }}" id="input-company_name" class="form-control" />
              {% if error_company_name %}
                <div class="text-danger">{{ error_company_name }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-company_telephone">{{ text_company_telephone }}</label>
            <div class="col-sm-10">
              <input type="text" name="company_telephone" value="{{ company_telephone }}" placeholder="{{ text_company_telephone }}" id="input-company_telephone" class="form-control" />
              {% if error_company_telephone %}
                <div class="text-danger">{{ error_company_telephone }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-compare_url">{{ text_compare_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="compare_url" value="{{ compare_url }}" placeholder="{{ text_compare_url }}" id="input-compare_url" class="form-control" />
              {% if error_compare_url %}
                <div class="text-danger">{{ error_compare_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-cost_centers">{{ text_cost_centers }}</label>
            <div class="col-sm-10">
              <input type="text" name="cost_centers" value="{{ cost_centers }}" placeholder="{{ text_cost_centers }}" id="input-cost_centers" class="form-control" />
              {% if error_cost_centers %}
                <div class="text-danger">{{ error_cost_centers }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-currencies">{{ text_currencies }}</label>
            <div class="col-sm-10">
              <input type="text" name="currencies" value="{{ currencies }}" placeholder="{{ text_currencies }}" id="input-currencies" class="form-control" />
              {% if error_currencies %}
                <div class="text-danger">{{ error_currencies }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-departments">{{ text_departments }}</label>
            <div class="col-sm-10">
              <input type="text" name="departments" value="{{ departments }}" placeholder="{{ text_departments }}" id="input-departments" class="form-control" />
              {% if error_departments %}
                <div class="text-danger">{{ error_departments }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-drill_down_url">{{ text_drill_down_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="drill_down_url" value="{{ drill_down_url }}" placeholder="{{ text_drill_down_url }}" id="input-drill_down_url" class="form-control" />
              {% if error_drill_down_url %}
                <div class="text-danger">{{ error_drill_down_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-export_csv">{{ text_export_csv }}</label>
            <div class="col-sm-10">
              <input type="text" name="export_csv" value="{{ export_csv }}" placeholder="{{ text_export_csv }}" id="input-export_csv" class="form-control" />
              {% if error_export_csv %}
                <div class="text-danger">{{ error_export_csv }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-export_excel">{{ text_export_excel }}</label>
            <div class="col-sm-10">
              <input type="text" name="export_excel" value="{{ export_excel }}" placeholder="{{ text_export_excel }}" id="input-export_excel" class="form-control" />
              {% if error_export_excel %}
                <div class="text-danger">{{ error_export_excel }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-export_pdf">{{ text_export_pdf }}</label>
            <div class="col-sm-10">
              <input type="text" name="export_pdf" value="{{ export_pdf }}" placeholder="{{ text_export_pdf }}" id="input-export_pdf" class="form-control" />
              {% if error_export_pdf %}
                <div class="text-danger">{{ error_export_pdf }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-filter_data">{{ text_filter_data }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_data" value="{{ filter_data }}" placeholder="{{ text_filter_data }}" id="input-filter_data" class="form-control" />
              {% if error_filter_data %}
                <div class="text-danger">{{ error_filter_data }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-generated_by">{{ text_generated_by }}</label>
            <div class="col-sm-10">
              <input type="text" name="generated_by" value="{{ generated_by }}" placeholder="{{ text_generated_by }}" id="input-generated_by" class="form-control" />
              {% if error_generated_by %}
                <div class="text-danger">{{ error_generated_by }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="text-danger">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-integrity_check_url">{{ text_integrity_check_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="integrity_check_url" value="{{ integrity_check_url }}" placeholder="{{ text_integrity_check_url }}" id="input-integrity_check_url" class="form-control" />
              {% if error_integrity_check_url %}
                <div class="text-danger">{{ error_integrity_check_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-movements_url">{{ text_movements_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="movements_url" value="{{ movements_url }}" placeholder="{{ text_movements_url }}" id="input-movements_url" class="form-control" />
              {% if error_movements_url %}
                <div class="text-danger">{{ error_movements_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-print_mode">{{ text_print_mode }}</label>
            <div class="col-sm-10">
              <input type="text" name="print_mode" value="{{ print_mode }}" placeholder="{{ text_print_mode }}" id="input-print_mode" class="form-control" />
              {% if error_print_mode %}
                <div class="text-danger">{{ error_print_mode }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-print_url">{{ text_print_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="print_url" value="{{ print_url }}" placeholder="{{ text_print_url }}" id="input-print_url" class="form-control" />
              {% if error_print_url %}
                <div class="text-danger">{{ error_print_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-projects">{{ text_projects }}</label>
            <div class="col-sm-10">
              <input type="text" name="projects" value="{{ projects }}" placeholder="{{ text_projects }}" id="input-projects" class="form-control" />
              {% if error_projects %}
                <div class="text-danger">{{ error_projects }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-report_date">{{ text_report_date }}</label>
            <div class="col-sm-10">
              <input type="text" name="report_date" value="{{ report_date }}" placeholder="{{ text_report_date }}" id="input-report_date" class="form-control" />
              {% if error_report_date %}
                <div class="text-danger">{{ error_report_date }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-report_title">{{ text_report_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="report_title" value="{{ report_title }}" placeholder="{{ text_report_title }}" id="input-report_title" class="form-control" />
              {% if error_report_title %}
                <div class="text-danger">{{ error_report_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-trial_balance_data">{{ text_trial_balance_data }}</label>
            <div class="col-sm-10">
              <input type="text" name="trial_balance_data" value="{{ trial_balance_data }}" placeholder="{{ text_trial_balance_data }}" id="input-trial_balance_data" class="form-control" />
              {% if error_trial_balance_data %}
                <div class="text-danger">{{ error_trial_balance_data }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-warning">{{ text_warning }}</label>
            <div class="col-sm-10">
              <input type="text" name="warning" value="{{ warning }}" placeholder="{{ text_warning }}" id="input-warning" class="form-control" />
              {% if error_warning %}
                <div class="text-danger">{{ error_warning }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}