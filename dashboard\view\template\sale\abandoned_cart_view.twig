<div class="modal-header">
  <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
  <h4 class="modal-title">{{ text_cart_details }}</h4>
</div>
<div class="modal-body">
  <div class="row">
    <!-- Customer Information -->
    <div class="col-md-6">
      <div class="panel panel-default">
        <div class="panel-heading">
          <h3 class="panel-title"><i class="fa fa-user"></i> {{ text_customer_details }}</h3>
        </div>
        <div class="panel-body">
          {% if customer.customer_id %}
          <div class="table-responsive">
            <table class="table table-bordered">
              <tr>
                <td><strong>{{ text_customer_name }}:</strong></td>
                <td>{{ customer.firstname }} {{ customer.lastname }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_customer_email }}:</strong></td>
                <td>{{ customer.email }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_customer_telephone }}:</strong></td>
                <td>{{ customer.telephone }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_customer_group }}:</strong></td>
                <td>{{ customer.customer_group }}</td>
              </tr>
            </table>
          </div>
          <a href="{{ customer_url }}" target="_blank" class="btn btn-info btn-sm"><i class="fa fa-external-link"></i> {{ text_view_customer }}</a>
          {% else %}
          <div class="alert alert-info">
            <i class="fa fa-info-circle"></i> {{ text_guest_customer }}
          </div>
          {% endif %}
        </div>
      </div>
    </div>
    
    <!-- Cart Information -->
    <div class="col-md-6">
      <div class="panel panel-default">
        <div class="panel-heading">
          <h3 class="panel-title"><i class="fa fa-shopping-cart"></i> {{ text_cart_info }}</h3>
        </div>
        <div class="panel-body">
          <div class="table-responsive">
            <table class="table table-bordered">
              <tr>
                <td><strong>{{ text_cart_id }}:</strong></td>
                <td>{{ cart.cart_id }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_date_created }}:</strong></td>
                <td>{{ cart.date_created }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_last_activity }}:</strong></td>
                <td>{{ cart.last_activity }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_items_count }}:</strong></td>
                <td>{{ cart.items_count }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_total_value }}:</strong></td>
                <td>{{ cart.total_value }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_status }}:</strong></td>
                <td>{{ cart.status_text }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_email_sent }}:</strong></td>
                <td>{% if cart.recovery_email_sent %}<span class="label label-success">{{ text_yes }}</span>{% else %}<span class="label label-danger">{{ text_no }}</span>{% endif %}</td>
              </tr>
              {% if cart.recovery_date %}
              <tr>
                <td><strong>{{ text_recovery_date }}:</strong></td>
                <td>{{ cart.recovery_date }}</td>
              </tr>
              {% endif %}
              {% if cart.order_id %}
              <tr>
                <td><strong>{{ text_order_id }}:</strong></td>
                <td><a href="{{ order_url }}" target="_blank">#{{ cart.order_id }}</a></td>
              </tr>
              {% endif %}
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Cart Items -->
  <div class="row">
    <div class="col-md-12">
      <div class="panel panel-default">
        <div class="panel-heading">
          <h3 class="panel-title"><i class="fa fa-list"></i> {{ text_cart_items }}</h3>
        </div>
        <div class="panel-body">
          <div class="table-responsive">
            <table class="table table-bordered table-hover">
              <thead>
                <tr>
                  <th>{{ column_product }}</th>
                  <th>{{ column_model }}</th>
                  <th>{{ column_options }}</th>
                  <th class="text-right">{{ column_quantity }}</th>
                  <th class="text-right">{{ column_price }}</th>
                  <th class="text-right">{{ column_total }}</th>
                </tr>
              </thead>
              <tbody>
                {% if cart_items %}
                {% for item in cart_items %}
                <tr>
                  <td>{{ item.name }}</td>
                  <td>{{ item.model }}</td>
                  <td>
                    {% if item.options %}
                    <ul class="list-unstyled">
                      {% for option in item.options %}
                      <li>{{ option }}</li>
                      {% endfor %}
                    </ul>
                    {% endif %}
                  </td>
                  <td class="text-right">{{ item.quantity }}</td>
                  <td class="text-right">{{ item.price }}</td>
                  <td class="text-right">{{ item.total }}</td>
                </tr>
                {% endfor %}
                {% else %}
                <tr>
                  <td class="text-center" colspan="6">{{ text_no_items }}</td>
                </tr>
                {% endif %}
              </tbody>
              <tfoot>
                <tr>
                  <td colspan="5" class="text-right"><strong>{{ text_total }}:</strong></td>
                  <td class="text-right">{{ cart.total_value }}</td>
                </tr>
              </tfoot>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Recovery Timeline -->
  <div class="row">
    <div class="col-md-12">
      <div class="panel panel-default">
        <div class="panel-heading">
          <h3 class="panel-title"><i class="fa fa-history"></i> {{ text_timeline }}</h3>
        </div>
        <div class="panel-body">
          <ul class="timeline">
            {% for event in timeline %}
            <li class="{{ event.type }}">
              <div class="timeline-badge {{ event.color }}"><i class="fa {{ event.icon }}"></i></div>
              <div class="timeline-panel">
                <div class="timeline-heading">
                  <h4 class="timeline-title">{{ event.title }}</h4>
                  <p><small class="text-muted"><i class="fa fa-clock-o"></i> {{ event.time }}</small></p>
                </div>
                <div class="timeline-body">
                  <p>{{ event.message }}</p>
                </div>
              </div>
            </li>
            {% endfor %}
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>
<div class="modal-footer">
  <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_close }}</button>
</div>

<style>
.timeline {
  list-style: none;
  padding: 20px 0 20px;
  position: relative;
}

.timeline:before {
  top: 0;
  bottom: 0;
  position: absolute;
  content: " ";
  width: 3px;
  background-color: #eeeeee;
  left: 50%;
  margin-left: -1.5px;
}

.timeline > li {
  margin-bottom: 20px;
  position: relative;
}

.timeline > li:before,
.timeline > li:after {
  content: " ";
  display: table;
}

.timeline > li:after {
  clear: both;
}

.timeline > li > .timeline-panel {
  width: 46%;
  float: left;
  border: 1px solid #d4d4d4;
  border-radius: 2px;
  padding: 20px;
  position: relative;
  -webkit-box-shadow: 0 1px 6px rgba(0, 0, 0, 0.175);
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.175);
}

.timeline > li > .timeline-panel:before {
  position: absolute;
  top: 26px;
  right: -15px;
  display: inline-block;
  border-top: 15px solid transparent;
  border-left: 15px solid #ccc;
  border-right: 0 solid #ccc;
  border-bottom: 15px solid transparent;
  content: " ";
}

.timeline > li > .timeline-panel:after {
  position: absolute;
  top: 27px;
  right: -14px;
  display: inline-block;
  border-top: 14px solid transparent;
  border-left: 14px solid #fff;
  border-right: 0 solid #fff;
  border-bottom: 14px solid transparent;
  content: " ";
}

.timeline > li > .timeline-badge {
  color: #fff;
  width: 50px;
  height: 50px;
  line-height: 50px;
  font-size: 1.4em;
  text-align: center;
  position: absolute;
  top: 16px;
  left: 50%;
  margin-left: -25px;
  background-color: #999999;
  z-index: 100;
  border-top-right-radius: 50%;
  border-top-left-radius: 50%;
  border-bottom-right-radius: 50%;
  border-bottom-left-radius: 50%;
}

.timeline > li.timeline-inverted > .timeline-panel {
  float: right;
}

.timeline > li.timeline-inverted > .timeline-panel:before {
  border-left-width: 0;
  border-right-width: 15px;
  left: -15px;
  right: auto;
}

.timeline > li.timeline-inverted > .timeline-panel:after {
  border-left-width: 0;
  border-right-width: 14px;
  left: -14px;
  right: auto;
}

.timeline-badge.primary {
  background-color: #2e6da4 !important;
}

.timeline-badge.success {
  background-color: #3f903f !important;
}

.timeline-badge.warning {
  background-color: #f0ad4e !important;
}

.timeline-badge.danger {
  background-color: #d9534f !important;
}

.timeline-badge.info {
  background-color: #5bc0de !important;
}

.timeline-title {
  margin-top: 0;
  color: inherit;
}

.timeline-body > p,
.timeline-body > ul {
  margin-bottom: 0;
}

.timeline-body > p + p {
  margin-top: 5px;
}

@media (max-width: 767px) {
  ul.timeline:before {
    left: 40px;
  }

  ul.timeline > li > .timeline-panel {
    width: calc(100% - 90px);
    width: -moz-calc(100% - 90px);
    width: -webkit-calc(100% - 90px);
  }

  ul.timeline > li > .timeline-badge {
    left: 15px;
    margin-left: 0;
    top: 16px;
  }

  ul.timeline > li > .timeline-panel {
    float: right;
  }

  ul.timeline > li > .timeline-panel:before {
    border-left-width: 0;
    border-right-width: 15px;
    left: -15px;
    right: auto;
  }

  ul.timeline > li > .timeline-panel:after {
    border-left-width: 0;
    border-right-width: 14px;
    left: -14px;
    right: auto;
  }
}
</style>