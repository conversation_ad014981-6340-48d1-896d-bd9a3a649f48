{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        {% if has_permission %}
        <button type="button" id="button-create" data-toggle="tooltip" title="{{ button_create }}" class="btn btn-primary">
          <i class="fa fa-plus"></i> {{ button_create }}
        </button>
        <button type="button" id="button-process" data-toggle="tooltip" title="{{ button_process }}" class="btn btn-success" disabled>
          <i class="fa fa-cogs"></i> {{ button_process }}
        </button>
        {% endif %}
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    {% if success %}
    <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    
    <!-- Currency Rates Dashboard -->
    <div class="row">
      <div class="col-md-12">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-exchange"></i> {{ text_currency_rates_overview }}</h3>
          </div>
          <div class="panel-body">
            <div class="row">
              {% for currency in currencies %}
              <div class="col-md-3">
                <div class="panel panel-info">
                  <div class="panel-heading text-center">
                    <h4>{{ currency.code }}</h4>
                  </div>
                  <div class="panel-body text-center">
                    <h3>{{ currency.current_rate }}</h3>
                    <p class="text-muted">{{ text_current_rate }}</p>
                    {% if currency.change > 0 %}
                    <span class="label label-success">+{{ currency.change }}%</span>
                    {% elseif currency.change < 0 %}
                    <span class="label label-danger">{{ currency.change }}%</span>
                    {% else %}
                    <span class="label label-default">{{ currency.change }}%</span>
                    {% endif %}
                  </div>
                </div>
              </div>
              {% endfor %}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Create New Revaluation -->
    <div class="row">
      <div class="col-md-8">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-plus"></i> {{ text_create_revaluation }}</h3>
          </div>
          <div class="panel-body">
            <form id="form-revaluation" class="form-horizontal">
              <div class="form-group">
                <label class="col-sm-3 control-label" for="input-date">{{ entry_revaluation_date }}</label>
                <div class="col-sm-9">
                  <div class="input-group date">
                    <input type="text" name="revaluation_date" value="{{ revaluation_date }}" placeholder="{{ entry_revaluation_date }}" id="input-date" class="form-control" />
                    <span class="input-group-btn">
                      <button class="btn btn-default" type="button"><i class="fa fa-calendar"></i></button>
                    </span>
                  </div>
                </div>
              </div>
              
              <div class="form-group">
                <label class="col-sm-3 control-label">{{ entry_currencies }}</label>
                <div class="col-sm-9">
                  <div class="well well-sm">
                    {% for currency in available_currencies %}
                    <div class="checkbox">
                      <label>
                        <input type="checkbox" name="currencies[]" value="{{ currency.code }}" checked="checked" />
                        {{ currency.title }} ({{ currency.code }})
                      </label>
                    </div>
                    {% endfor %}
                  </div>
                </div>
              </div>
              
              <div class="form-group">
                <label class="col-sm-3 control-label">{{ entry_account_types }}</label>
                <div class="col-sm-9">
                  <div class="well well-sm">
                    <div class="checkbox">
                      <label>
                        <input type="checkbox" name="account_types[]" value="receivables" checked="checked" />
                        {{ text_receivables }}
                      </label>
                    </div>
                    <div class="checkbox">
                      <label>
                        <input type="checkbox" name="account_types[]" value="payables" checked="checked" />
                        {{ text_payables }}
                      </label>
                    </div>
                    <div class="checkbox">
                      <label>
                        <input type="checkbox" name="account_types[]" value="cash" checked="checked" />
                        {{ text_cash_accounts }}
                      </label>
                    </div>
                    <div class="checkbox">
                      <label>
                        <input type="checkbox" name="account_types[]" value="bank" checked="checked" />
                        {{ text_bank_accounts }}
                      </label>
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="form-group">
                <label class="col-sm-3 control-label">{{ entry_auto_post }}</label>
                <div class="col-sm-9">
                  <div class="checkbox">
                    <label>
                      <input type="checkbox" name="auto_post" value="1" />
                      {{ text_auto_post_help }}
                    </label>
                  </div>
                </div>
              </div>
            </form>
          </div>
          <div class="panel-footer">
            <button type="button" id="button-preview" class="btn btn-info">
              <i class="fa fa-eye"></i> {{ button_preview }}
            </button>
            <button type="button" id="button-save" class="btn btn-primary">
              <i class="fa fa-save"></i> {{ button_save }}
            </button>
          </div>
        </div>
      </div>
      
      <!-- Exchange Rates Input -->
      <div class="col-md-4">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-calculator"></i> {{ text_exchange_rates }}</h3>
          </div>
          <div class="panel-body">
            <div id="exchange-rates">
              {% for currency in available_currencies %}
              <div class="form-group">
                <label for="rate-{{ currency.code }}">{{ currency.code }}</label>
                <div class="input-group">
                  <input type="number" step="0.0001" name="exchange_rates[{{ currency.code }}]" value="{{ currency.rate }}" id="rate-{{ currency.code }}" class="form-control" />
                  <span class="input-group-btn">
                    <button type="button" class="btn btn-default btn-update-rate" data-currency="{{ currency.code }}">
                      <i class="fa fa-refresh"></i>
                    </button>
                  </span>
                </div>
              </div>
              {% endfor %}
            </div>
            <button type="button" id="button-update-all-rates" class="btn btn-sm btn-warning btn-block">
              <i class="fa fa-refresh"></i> {{ button_update_all_rates }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Recent Revaluations -->
    <div class="row">
      <div class="col-md-12">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-history"></i> {{ text_recent_revaluations }}</h3>
          </div>
          <div class="panel-body">
            <div class="table-responsive">
              <table class="table table-striped table-hover">
                <thead>
                  <tr>
                    <th>{{ column_reference }}</th>
                    <th>{{ column_date }}</th>
                    <th>{{ column_currencies }}</th>
                    <th>{{ column_adjustment }}</th>
                    <th>{{ column_status }}</th>
                    <th>{{ column_action }}</th>
                  </tr>
                </thead>
                <tbody>
                  {% if revaluations %}
                  {% for revaluation in revaluations %}
                  <tr>
                    <td>{{ revaluation.reference }}</td>
                    <td>{{ revaluation.revaluation_date }}</td>
                    <td>
                      {% for currency in revaluation.currencies %}
                      <span class="label label-default">{{ currency }}</span>
                      {% endfor %}
                    </td>
                    <td class="text-right">
                      {% if revaluation.total_adjustment > 0 %}
                      <span class="text-success">{{ revaluation.total_adjustment_formatted }}</span>
                      {% elseif revaluation.total_adjustment < 0 %}
                      <span class="text-danger">{{ revaluation.total_adjustment_formatted }}</span>
                      {% else %}
                      <span class="text-muted">{{ revaluation.total_adjustment_formatted }}</span>
                      {% endif %}
                    </td>
                    <td>
                      {% if revaluation.status == 'posted' %}
                      <span class="label label-success">{{ revaluation.status_text }}</span>
                      {% elseif revaluation.status == 'approved' %}
                      <span class="label label-info">{{ revaluation.status_text }}</span>
                      {% elseif revaluation.status == 'pending' %}
                      <span class="label label-warning">{{ revaluation.status_text }}</span>
                      {% else %}
                      <span class="label label-danger">{{ revaluation.status_text }}</span>
                      {% endif %}
                    </td>
                    <td>
                      <div class="btn-group">
                        <a href="{{ revaluation.view }}" class="btn btn-sm btn-info" title="{{ button_view }}">
                          <i class="fa fa-eye"></i>
                        </a>
                        {% if revaluation.edit %}
                        <a href="{{ revaluation.edit }}" class="btn btn-sm btn-primary" title="{{ button_edit }}">
                          <i class="fa fa-pencil"></i>
                        </a>
                        {% endif %}
                        {% if revaluation.reverse %}
                        <button type="button" class="btn btn-sm btn-warning" title="{{ button_reverse }}" onclick="confirm('{{ text_confirm_reverse }}') ? location = '{{ revaluation.reverse }}' : false;">
                          <i class="fa fa-undo"></i>
                        </button>
                        {% endif %}
                      </div>
                    </td>
                  </tr>
                  {% endfor %}
                  {% else %}
                  <tr>
                    <td colspan="6" class="text-center">{{ text_no_results }}</td>
                  </tr>
                  {% endif %}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Pagination -->
    <div class="row">
      <div class="col-sm-6 text-left">{{ pagination }}</div>
      <div class="col-sm-6 text-right">{{ results }}</div>
    </div>
  </div>
</div>

<script type="text/javascript">
$(document).ready(function() {
    // Initialize date picker
    $('.date').datetimepicker({
        language: '{{ datepicker }}',
        pickTime: false
    });
    
    // Initialize tooltips
    $('[data-toggle="tooltip"]').tooltip();
    
    // Update individual exchange rate
    $('.btn-update-rate').on('click', function() {
        var currency = $(this).data('currency');
        // AJAX call to get latest rate
        $.ajax({
            url: 'index.php?route=accounts/multi_currency_revaluation/getLatestRate&token={{ token }}',
            type: 'post',
            data: {currency: currency},
            dataType: 'json',
            success: function(json) {
                if (json.success) {
                    $('#rate-' + currency).val(json.rate);
                }
            }
        });
    });
    
    // Update all exchange rates
    $('#button-update-all-rates').on('click', function() {
        $('input[name^="currencies"]:checked').each(function() {
            var currency = $(this).val();
            $('.btn-update-rate[data-currency="' + currency + '"]').click();
        });
    });
    
    // Preview revaluation
    $('#button-preview').on('click', function() {
        // Show preview modal
        $('#modal-preview').modal('show');
    });
    
    // Save revaluation
    $('#button-save').on('click', function() {
        $.ajax({
            url: 'index.php?route=accounts/multi_currency_revaluation/create&token={{ token }}',
            type: 'post',
            data: $('#form-revaluation').serialize(),
            dataType: 'json',
            beforeSend: function() {
                $('#button-save').button('loading');
            },
            complete: function() {
                $('#button-save').button('reset');
            },
            success: function(json) {
                if (json.success) {
                    location.reload();
                } else {
                    alert(json.error);
                }
            }
        });
    });
});
</script>

{{ footer }}
