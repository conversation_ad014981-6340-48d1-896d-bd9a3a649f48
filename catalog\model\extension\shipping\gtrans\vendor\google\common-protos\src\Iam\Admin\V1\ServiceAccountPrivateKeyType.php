<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/iam/admin/v1/iam.proto

namespace Google\Iam\Admin\V1;

use UnexpectedValueException;

/**
 * Supported private key output formats.
 *
 * Protobuf type <code>google.iam.admin.v1.ServiceAccountPrivateKeyType</code>
 */
class ServiceAccountPrivateKeyType
{
    /**
     * Unspecified. Equivalent to `TYPE_GOOGLE_CREDENTIALS_FILE`.
     *
     * Generated from protobuf enum <code>TYPE_UNSPECIFIED = 0;</code>
     */
    const TYPE_UNSPECIFIED = 0;
    /**
     * PKCS12 format.
     * The password for the PKCS12 file is `notasecret`.
     * For more information, see https://tools.ietf.org/html/rfc7292.
     *
     * Generated from protobuf enum <code>TYPE_PKCS12_FILE = 1;</code>
     */
    const TYPE_PKCS12_FILE = 1;
    /**
     * Google Credentials File format.
     *
     * Generated from protobuf enum <code>TYPE_GOOGLE_CREDENTIALS_FILE = 2;</code>
     */
    const TYPE_GOOGLE_CREDENTIALS_FILE = 2;

    private static $valueToName = [
        self::TYPE_UNSPECIFIED => 'TYPE_UNSPECIFIED',
        self::TYPE_PKCS12_FILE => 'TYPE_PKCS12_FILE',
        self::TYPE_GOOGLE_CREDENTIALS_FILE => 'TYPE_GOOGLE_CREDENTIALS_FILE',
    ];

    public static function name($value)
    {
        if (!isset(self::$valueToName[$value])) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no name defined for value %s', __CLASS__, $value));
        }
        return self::$valueToName[$value];
    }


    public static function value($name)
    {
        $const = __CLASS__ . '::' . strtoupper($name);
        if (!defined($const)) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no value defined for name %s', __CLASS__, $name));
        }
        return constant($const);
    }
}

