{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="submit" form="crm\campaign_management-form" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fas fa-save"></i></button>
        <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-light"><i class="fas fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    
    <div class="card">
      <div class="card-header"><i class="fas fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}chart-bar{% elif screen_type == 'settings' %}cog{% else %}pencil-alt{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="card-body">
        <form id="crm\campaign_management-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-add">{{ text_add }}</label>
            <div class="col-sm-10">
              <input type="text" name="add" value="{{ add }}" placeholder="{{ text_add }}" id="input-add" class="form-control" />
              {% if error_add %}
                <div class="invalid-feedback">{{ error_add }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-analytics">{{ text_analytics }}</label>
            <div class="col-sm-10">
              <input type="text" name="analytics" value="{{ analytics }}" placeholder="{{ text_analytics }}" id="input-analytics" class="form-control" />
              {% if error_analytics %}
                <div class="invalid-feedback">{{ error_analytics }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-analytics_overview">{{ text_analytics_overview }}</label>
            <div class="col-sm-10">
              <input type="text" name="analytics_overview" value="{{ analytics_overview }}" placeholder="{{ text_analytics_overview }}" id="input-analytics_overview" class="form-control" />
              {% if error_analytics_overview %}
                <div class="invalid-feedback">{{ error_analytics_overview }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-bulk_actions">{{ text_bulk_actions }}</label>
            <div class="col-sm-10">
              <input type="text" name="bulk_actions" value="{{ bulk_actions }}" placeholder="{{ text_bulk_actions }}" id="input-bulk_actions" class="form-control" />
              {% if error_bulk_actions %}
                <div class="invalid-feedback">{{ error_bulk_actions }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-campaign">{{ text_campaign }}</label>
            <div class="col-sm-10">
              <input type="text" name="campaign" value="{{ campaign }}" placeholder="{{ text_campaign }}" id="input-campaign" class="form-control" />
              {% if error_campaign %}
                <div class="invalid-feedback">{{ error_campaign }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-campaign_activities">{{ text_campaign_activities }}</label>
            <div class="col-sm-10">
              <input type="text" name="campaign_activities" value="{{ campaign_activities }}" placeholder="{{ text_campaign_activities }}" id="input-campaign_activities" class="form-control" />
              {% if error_campaign_activities %}
                <div class="invalid-feedback">{{ error_campaign_activities }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-campaign_analytics">{{ text_campaign_analytics }}</label>
            <div class="col-sm-10">
              <input type="text" name="campaign_analytics" value="{{ campaign_analytics }}" placeholder="{{ text_campaign_analytics }}" id="input-campaign_analytics" class="form-control" />
              {% if error_campaign_analytics %}
                <div class="invalid-feedback">{{ error_campaign_analytics }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-campaign_charts">{{ text_campaign_charts }}</label>
            <div class="col-sm-10">
              <input type="text" name="campaign_charts" value="{{ campaign_charts }}" placeholder="{{ text_campaign_charts }}" id="input-campaign_charts" class="form-control" />
              {% if error_campaign_charts %}
                <div class="invalid-feedback">{{ error_campaign_charts }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-campaign_details">{{ text_campaign_details }}</label>
            <div class="col-sm-10">
              <input type="text" name="campaign_details" value="{{ campaign_details }}" placeholder="{{ text_campaign_details }}" id="input-campaign_details" class="form-control" />
              {% if error_campaign_details %}
                <div class="invalid-feedback">{{ error_campaign_details }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-campaign_leads">{{ text_campaign_leads }}</label>
            <div class="col-sm-10">
              <input type="text" name="campaign_leads" value="{{ campaign_leads }}" placeholder="{{ text_campaign_leads }}" id="input-campaign_leads" class="form-control" />
              {% if error_campaign_leads %}
                <div class="invalid-feedback">{{ error_campaign_leads }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-campaign_statistics">{{ text_campaign_statistics }}</label>
            <div class="col-sm-10">
              <input type="text" name="campaign_statistics" value="{{ campaign_statistics }}" placeholder="{{ text_campaign_statistics }}" id="input-campaign_statistics" class="form-control" />
              {% if error_campaign_statistics %}
                <div class="invalid-feedback">{{ error_campaign_statistics }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-campaign_statuses">{{ text_campaign_statuses }}</label>
            <div class="col-sm-10">
              <input type="text" name="campaign_statuses" value="{{ campaign_statuses }}" placeholder="{{ text_campaign_statuses }}" id="input-campaign_statuses" class="form-control" />
              {% if error_campaign_statuses %}
                <div class="invalid-feedback">{{ error_campaign_statuses }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-campaign_types">{{ text_campaign_types }}</label>
            <div class="col-sm-10">
              <input type="text" name="campaign_types" value="{{ campaign_types }}" placeholder="{{ text_campaign_types }}" id="input-campaign_types" class="form-control" />
              {% if error_campaign_types %}
                <div class="invalid-feedback">{{ error_campaign_types }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-campaigns">{{ text_campaigns }}</label>
            <div class="col-sm-10">
              <input type="text" name="campaigns" value="{{ campaigns }}" placeholder="{{ text_campaigns }}" id="input-campaigns" class="form-control" />
              {% if error_campaigns %}
                <div class="invalid-feedback">{{ error_campaigns }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-charts">{{ text_charts }}</label>
            <div class="col-sm-10">
              <input type="text" name="charts" value="{{ charts }}" placeholder="{{ text_charts }}" id="input-charts" class="form-control" />
              {% if error_charts %}
                <div class="invalid-feedback">{{ error_charts }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-export">{{ text_export }}</label>
            <div class="col-sm-10">
              <input type="text" name="export" value="{{ export }}" placeholder="{{ text_export }}" id="input-export" class="form-control" />
              {% if error_export %}
                <div class="invalid-feedback">{{ error_export }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_date_from">{{ text_filter_date_from }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_date_from" value="{{ filter_date_from }}" placeholder="{{ text_filter_date_from }}" id="input-filter_date_from" class="form-control" />
              {% if error_filter_date_from %}
                <div class="invalid-feedback">{{ error_filter_date_from }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_date_to">{{ text_filter_date_to }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_date_to" value="{{ filter_date_to }}" placeholder="{{ text_filter_date_to }}" id="input-filter_date_to" class="form-control" />
              {% if error_filter_date_to %}
                <div class="invalid-feedback">{{ error_filter_date_to }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_name">{{ text_filter_name }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_name" value="{{ filter_name }}" placeholder="{{ text_filter_name }}" id="input-filter_name" class="form-control" />
              {% if error_filter_name %}
                <div class="invalid-feedback">{{ error_filter_name }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_performance">{{ text_filter_performance }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_performance" value="{{ filter_performance }}" placeholder="{{ text_filter_performance }}" id="input-filter_performance" class="form-control" />
              {% if error_filter_performance %}
                <div class="invalid-feedback">{{ error_filter_performance }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_status">{{ text_filter_status }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_status" value="{{ filter_status }}" placeholder="{{ text_filter_status }}" id="input-filter_status" class="form-control" />
              {% if error_filter_status %}
                <div class="invalid-feedback">{{ error_filter_status }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_type">{{ text_filter_type }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_type" value="{{ filter_type }}" placeholder="{{ text_filter_type }}" id="input-filter_type" class="form-control" />
              {% if error_filter_type %}
                <div class="invalid-feedback">{{ error_filter_type }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="invalid-feedback">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-pagination">{{ text_pagination }}</label>
            <div class="col-sm-10">
              <input type="text" name="pagination" value="{{ pagination }}" placeholder="{{ text_pagination }}" id="input-pagination" class="form-control" />
              {% if error_pagination %}
                <div class="invalid-feedback">{{ error_pagination }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-performance_levels">{{ text_performance_levels }}</label>
            <div class="col-sm-10">
              <input type="text" name="performance_levels" value="{{ performance_levels }}" placeholder="{{ text_performance_levels }}" id="input-performance_levels" class="form-control" />
              {% if error_performance_levels %}
                <div class="invalid-feedback">{{ error_performance_levels }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-results">{{ text_results }}</label>
            <div class="col-sm-10">
              <input type="text" name="results" value="{{ results }}" placeholder="{{ text_results }}" id="input-results" class="form-control" />
              {% if error_results %}
                <div class="invalid-feedback">{{ error_results }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-statistics">{{ text_statistics }}</label>
            <div class="col-sm-10">
              <input type="text" name="statistics" value="{{ statistics }}" placeholder="{{ text_statistics }}" id="input-statistics" class="form-control" />
              {% if error_statistics %}
                <div class="invalid-feedback">{{ error_statistics }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-templates">{{ text_templates }}</label>
            <div class="col-sm-10">
              <input type="text" name="templates" value="{{ templates }}" placeholder="{{ text_templates }}" id="input-templates" class="form-control" />
              {% if error_templates %}
                <div class="invalid-feedback">{{ error_templates }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="invalid-feedback">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}