{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <a href="{{ edit }}" data-toggle="tooltip" title="{{ button_edit }}" class="btn btn-primary">
          <i class="fa fa-pencil"></i>
        </a>
        <a href="{{ copy }}" data-toggle="tooltip" title="{{ button_copy }}" class="btn btn-success">
          <i class="fa fa-copy"></i>
        </a>
        <button type="button" data-toggle="tooltip" title="طباعة الصفحة" class="btn btn-default" onclick="window.print();">
          <i class="fa fa-print"></i>
        </button>
        <a href="{{ back }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default">
          <i class="fa fa-reply"></i>
        </a>
      </div>
      <h1>{{ heading_title }} - عرض التفاصيل</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  
  <div class="container-fluid">
    <!-- معلومات المنتج الأساسية -->
    <div class="row">
      <div class="col-md-8">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title">
              <i class="fa fa-info-circle"></i> معلومات المنتج
              <span class="label label-{{ product_info.status ? 'success' : 'danger' }} pull-right">
                {{ product_info.status ? 'مفعل' : 'معطل' }}
              </span>
            </h3>
          </div>
          <div class="panel-body">
            <div class="row">
              <div class="col-md-6">
                <table class="table table-condensed">
                  <tr>
                    <td><strong>اسم المنتج:</strong></td>
                    <td>{{ product_info.name }}</td>
                  </tr>
                  <tr>
                    <td><strong>الموديل:</strong></td>
                    <td>{{ product_info.model }}</td>
                  </tr>
                  <tr>
                    <td><strong>رمز المنتج:</strong></td>
                    <td><code>{{ product_info.sku }}</code></td>
                  </tr>
                  <tr>
                    <td><strong>الشركة المصنعة:</strong></td>
                    <td>{{ product_info.manufacturer ? product_info.manufacturer : '-' }}</td>
                  </tr>
                  <tr>
                    <td><strong>الوحدة الأساسية:</strong></td>
                    <td>{{ product_info.base_unit_name ? product_info.base_unit_name ~ ' (' ~ product_info.base_unit_symbol ~ ')' : 'غير محدد' }}</td>
                  </tr>
                  <tr>
                    <td><strong>الموقع:</strong></td>
                    <td>{{ product_info.location ? product_info.location : '-' }}</td>
                  </tr>
                </table>
              </div>
              <div class="col-md-6">
                <table class="table table-condensed">
                  <tr>
                    <td><strong>تاريخ الإضافة:</strong></td>
                    <td>{{ product_info.date_added }}</td>
                  </tr>
                  <tr>
                    <td><strong>آخر تعديل:</strong></td>
                    <td>{{ product_info.date_modified }}</td>
                  </tr>
                  <tr>
                    <td><strong>تاريخ التوفر:</strong></td>
                    <td>{{ product_info.date_available }}</td>
                  </tr>
                  <tr>
                    <td><strong>الوزن:</strong></td>
                    <td>{{ product_info.weight ? product_info.weight ~ ' ' ~ product_info.weight_class : '-' }}</td>
                  </tr>
                  <tr>
                    <td><strong>الأبعاد:</strong></td>
                    <td>
                      {% if product_info.length or product_info.width or product_info.height %}
                      {{ product_info.length }} × {{ product_info.width }} × {{ product_info.height }} {{ product_info.length_class }}
                      {% else %}
                      -
                      {% endif %}
                    </td>
                  </tr>
                  <tr>
                    <td><strong>ترتيب العرض:</strong></td>
                    <td>{{ product_info.sort_order }}</td>
                  </tr>
                </table>
              </div>
            </div>
            
            {% if product_info.description %}
            <div class="row">
              <div class="col-md-12">
                <div class="well">
                  <strong>وصف المنتج:</strong><br>
                  {{ product_info.description|raw }}
                </div>
              </div>
            </div>
            {% endif %}
          </div>
        </div>
      </div>
      
      <!-- صورة المنتج -->
      <div class="col-md-4">
        <div class="panel panel-info">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-image"></i> صورة المنتج</h3>
          </div>
          <div class="panel-body text-center">
            {% if product_info.image %}
            <img src="{{ product_info.image }}" alt="{{ product_info.name }}" class="img-thumbnail" style="max-width: 100%;" />
            {% else %}
            <div class="image-placeholder" style="padding: 60px 20px; border: 2px dashed #ddd; border-radius: 10px;">
              <i class="fa fa-camera fa-3x text-muted"></i>
              <p class="text-muted">لا توجد صورة</p>
            </div>
            {% endif %}
          </div>
        </div>
      </div>
    </div>
    
    <!-- التسعير والمخزون -->
    <div class="row">
      <div class="col-md-6">
        <div class="panel panel-success">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-money"></i> التسعير المتقدم</h3>
          </div>
          <div class="panel-body">
            <div class="row">
              <div class="col-xs-6">
                <div class="text-center">
                  <h4 class="text-primary">{{ product_info.basic_price ? product_info.basic_price : product_info.price }}</h4>
                  <small>السعر الأساسي</small>
                </div>
              </div>
              <div class="col-xs-6">
                <div class="text-center">
                  <h4 class="text-success">{{ product_info.cost_price ? product_info.cost_price : '0.00' }}</h4>
                  <small>سعر التكلفة</small>
                </div>
              </div>
            </div>
            
            <hr>
            
            <div class="table-responsive">
              <table class="table table-condensed">
                <tr>
                  <td>سعر العرض:</td>
                  <td class="text-right"><strong>{{ product_info.offer_price ? product_info.offer_price : '-' }}</strong></td>
                </tr>
                <tr>
                  <td>سعر الجملة:</td>
                  <td class="text-right"><strong>{{ product_info.wholesale_price ? product_info.wholesale_price : '-' }}</strong></td>
                </tr>
                <tr>
                  <td>سعر نصف الجملة:</td>
                  <td class="text-right"><strong>{{ product_info.semi_wholesale_price ? product_info.semi_wholesale_price : '-' }}</strong></td>
                </tr>
                <tr>
                  <td>سعر نقطة البيع:</td>
                  <td class="text-right"><strong>{{ product_info.pos_price ? product_info.pos_price : '-' }}</strong></td>
                </tr>
                <tr>
                  <td>السعر الإلكتروني:</td>
                  <td class="text-right"><strong>{{ product_info.online_price ? product_info.online_price : '-' }}</strong></td>
                </tr>
              </table>
            </div>
            
            {% if product_info.margin_percentage or product_info.markup_percentage %}
            <hr>
            <div class="row">
              {% if product_info.margin_percentage %}
              <div class="col-xs-6">
                <div class="text-center">
                  <h5 class="text-info">{{ product_info.margin_percentage }}%</h5>
                  <small>نسبة الهامش</small>
                </div>
              </div>
              {% endif %}
              {% if product_info.markup_percentage %}
              <div class="col-xs-6">
                <div class="text-center">
                  <h5 class="text-warning">{{ product_info.markup_percentage }}%</h5>
                  <small>نسبة الربح</small>
                </div>
              </div>
              {% endif %}
            </div>
            {% endif %}
          </div>
        </div>
      </div>
      
      <div class="col-md-6">
        <div class="panel panel-warning">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-cubes"></i> إدارة المخزون</h3>
          </div>
          <div class="panel-body">
            <div class="row">
              <div class="col-xs-4">
                <div class="text-center">
                  <h4 class="text-success">{{ product_info.available_quantity ? product_info.available_quantity : '0' }}</h4>
                  <small>الكمية المتاحة</small>
                </div>
              </div>
              <div class="col-xs-4">
                <div class="text-center">
                  <h4 class="text-warning">{{ product_info.reserved_quantity ? product_info.reserved_quantity : '0' }}</h4>
                  <small>الكمية المحجوزة</small>
                </div>
              </div>
              <div class="col-xs-4">
                <div class="text-center">
                  <h4 class="text-info">{{ product_info.on_order_quantity ? product_info.on_order_quantity : '0' }}</h4>
                  <small>الكمية المطلوبة</small>
                </div>
              </div>
            </div>
            
            <hr>
            
            <div class="table-responsive">
              <table class="table table-condensed">
                <tr>
                  <td>حد إعادة الطلب:</td>
                  <td class="text-right"><span class="badge badge-warning">{{ product_info.reorder_level ? product_info.reorder_level : '0' }}</span></td>
                </tr>
                <tr>
                  <td>الحد الأقصى:</td>
                  <td class="text-right"><span class="badge badge-info">{{ product_info.max_stock_level ? product_info.max_stock_level : '0' }}</span></td>
                </tr>
                <tr>
                  <td>متوسط التكلفة:</td>
                  <td class="text-right"><strong>{{ product_info.avg_cost ? product_info.avg_cost : '0.00' }}</strong></td>
                </tr>
                <tr>
                  <td>آخر تكلفة:</td>
                  <td class="text-right"><strong>{{ product_info.last_cost ? product_info.last_cost : '0.00' }}</strong></td>
                </tr>
                <tr>
                  <td>التكلفة المعيارية:</td>
                  <td class="text-right"><strong>{{ product_info.standard_cost ? product_info.standard_cost : '0.00' }}</strong></td>
                </tr>
              </table>
            </div>
            
            <hr>
            
            <div class="row">
              <div class="col-xs-12">
                <div class="text-center">
                  <h4 class="text-primary">
                    {{ (product_info.available_quantity * product_info.avg_cost)|number_format(2) }}
                  </h4>
                  <small>إجمالي قيمة المخزون</small>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- الأكواد والمعرفات -->
    {% if product_info.upc or product_info.ean or product_info.jan or product_info.isbn or product_info.mpn %}
    <div class="panel panel-info">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-barcode"></i> الأكواد والمعرفات</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          {% if product_info.upc %}
          <div class="col-md-2">
            <strong>UPC:</strong><br>
            <code>{{ product_info.upc }}</code>
          </div>
          {% endif %}
          {% if product_info.ean %}
          <div class="col-md-2">
            <strong>EAN:</strong><br>
            <code>{{ product_info.ean }}</code>
          </div>
          {% endif %}
          {% if product_info.jan %}
          <div class="col-md-2">
            <strong>JAN:</strong><br>
            <code>{{ product_info.jan }}</code>
          </div>
          {% endif %}
          {% if product_info.isbn %}
          <div class="col-md-2">
            <strong>ISBN:</strong><br>
            <code>{{ product_info.isbn }}</code>
          </div>
          {% endif %}
          {% if product_info.mpn %}
          <div class="col-md-2">
            <strong>MPN:</strong><br>
            <code>{{ product_info.mpn }}</code>
          </div>
          {% endif %}
        </div>
      </div>
    </div>
    {% endif %}
    
    <!-- روابط سريعة -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-link"></i> إجراءات سريعة</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-md-3">
            <a href="{{ manage_barcodes }}" class="btn btn-warning btn-block">
              <i class="fa fa-qrcode"></i> إدارة الباركود
            </a>
          </div>
          <div class="col-md-3">
            <a href="{{ stock_movements }}" class="btn btn-info btn-block">
              <i class="fa fa-history"></i> حركات المخزون
            </a>
          </div>
          <div class="col-md-3">
            <a href="{{ edit }}" class="btn btn-primary btn-block">
              <i class="fa fa-pencil"></i> تعديل المنتج
            </a>
          </div>
          <div class="col-md-3">
            <a href="{{ copy }}" class="btn btn-success btn-block">
              <i class="fa fa-copy"></i> نسخ المنتج
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
@media print {
    .page-header, .breadcrumb, .btn {
        display: none !important;
    }
    
    .panel {
        border: 1px solid #ddd !important;
        box-shadow: none !important;
    }
}

.table > tbody > tr > td {
    vertical-align: middle;
}

.panel-heading h3 {
    margin: 0;
}

.text-center h4, .text-center h5 {
    margin: 10px 0;
}

.badge-warning {
    background-color: #f0ad4e;
}

.badge-info {
    background-color: #5bc0de;
}

.image-placeholder {
    background-color: #f9f9f9;
}

code {
    font-size: 12px;
    background-color: #f5f5f5;
    padding: 2px 4px;
    border-radius: 3px;
}
</style>

<script type="text/javascript">
$(document).ready(function() {
    $('[data-toggle="tooltip"]').tooltip();
    
    // تحسين عرض الطباعة
    window.addEventListener('beforeprint', function() {
        $('.table-responsive').removeClass('table-responsive');
    });
    
    window.addEventListener('afterprint', function() {
        $('.table').parent().addClass('table-responsive');
    });
});
</script>

{{ footer }}
