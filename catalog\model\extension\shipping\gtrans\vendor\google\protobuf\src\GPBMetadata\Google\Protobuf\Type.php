<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/protobuf/type.proto

namespace GPBMetadata\Google\Protobuf;

class Type
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        \GPBMetadata\Google\Protobuf\Any::initOnce();
        \GPBMetadata\Google\Protobuf\SourceContext::initOnce();
        $pool->internalAddGeneratedFile(hex2bin(
            "0a9f0c0a1a676f6f676c652f70726f746f6275662f747970652e70726f74" .
            "6f120f676f6f676c652e70726f746f6275661a24676f6f676c652f70726f" .
            "746f6275662f736f757263655f636f6e746578742e70726f746f22d7010a" .
            "0454797065120c0a046e616d6518012001280912260a066669656c647318" .
            "022003280b32162e676f6f676c652e70726f746f6275662e4669656c6412" .
            "0e0a066f6e656f667318032003280912280a076f7074696f6e7318042003" .
            "280b32172e676f6f676c652e70726f746f6275662e4f7074696f6e12360a" .
            "0e736f757263655f636f6e7465787418052001280b321e2e676f6f676c65" .
            "2e70726f746f6275662e536f75726365436f6e7465787412270a0673796e" .
            "74617818062001280e32172e676f6f676c652e70726f746f6275662e5379" .
            "6e74617822d5050a054669656c6412290a046b696e6418012001280e321b" .
            "2e676f6f676c652e70726f746f6275662e4669656c642e4b696e6412370a" .
            "0b63617264696e616c69747918022001280e32222e676f6f676c652e7072" .
            "6f746f6275662e4669656c642e43617264696e616c697479120e0a066e75" .
            "6d626572180320012805120c0a046e616d6518042001280912100a087479" .
            "70655f75726c18062001280912130a0b6f6e656f665f696e646578180720" .
            "012805120e0a067061636b656418082001280812280a076f7074696f6e73" .
            "18092003280b32172e676f6f676c652e70726f746f6275662e4f7074696f" .
            "6e12110a096a736f6e5f6e616d65180a2001280912150a0d64656661756c" .
            "745f76616c7565180b2001280922c8020a044b696e6412100a0c54595045" .
            "5f554e4b4e4f574e1000120f0a0b545950455f444f55424c451001120e0a" .
            "0a545950455f464c4f41541002120e0a0a545950455f494e543634100312" .
            "0f0a0b545950455f55494e5436341004120e0a0a545950455f494e543332" .
            "100512100a0c545950455f46495845443634100612100a0c545950455f46" .
            "4958454433321007120d0a09545950455f424f4f4c1008120f0a0b545950" .
            "455f535452494e471009120e0a0a545950455f47524f5550100a12100a0c" .
            "545950455f4d455353414745100b120e0a0a545950455f4259544553100c" .
            "120f0a0b545950455f55494e543332100d120d0a09545950455f454e554d" .
            "100e12110a0d545950455f5346495845443332100f12110a0d545950455f" .
            "53464958454436341010120f0a0b545950455f53494e5433321011120f0a" .
            "0b545950455f53494e543634101222740a0b43617264696e616c69747912" .
            "170a1343415244494e414c4954595f554e4b4e4f574e100012180a144341" .
            "5244494e414c4954595f4f5054494f4e414c100112180a1443415244494e" .
            "414c4954595f5245515549524544100212180a1443415244494e414c4954" .
            "595f5245504541544544100322ce010a04456e756d120c0a046e616d6518" .
            "0120012809122d0a09656e756d76616c756518022003280b321a2e676f6f" .
            "676c652e70726f746f6275662e456e756d56616c756512280a076f707469" .
            "6f6e7318032003280b32172e676f6f676c652e70726f746f6275662e4f70" .
            "74696f6e12360a0e736f757263655f636f6e7465787418042001280b321e" .
            "2e676f6f676c652e70726f746f6275662e536f75726365436f6e74657874" .
            "12270a0673796e74617818052001280e32172e676f6f676c652e70726f74" .
            "6f6275662e53796e74617822530a09456e756d56616c7565120c0a046e61" .
            "6d65180120012809120e0a066e756d62657218022001280512280a076f70" .
            "74696f6e7318032003280b32172e676f6f676c652e70726f746f6275662e" .
            "4f7074696f6e223b0a064f7074696f6e120c0a046e616d65180120012809" .
            "12230a0576616c756518022001280b32142e676f6f676c652e70726f746f" .
            "6275662e416e792a2e0a0653796e74617812110a0d53594e5441585f5052" .
            "4f544f32100012110a0d53594e5441585f50524f544f331001427d0a1363" .
            "6f6d2e676f6f676c652e70726f746f62756642095479706550726f746f50" .
            "015a2f676f6f676c652e676f6c616e672e6f72672f67656e70726f746f2f" .
            "70726f746f6275662f70747970653b7074797065f80101a20203475042aa" .
            "021e476f6f676c652e50726f746f6275662e57656c6c4b6e6f776e547970" .
            "6573620670726f746f33"
        ));

        static::$is_initialized = true;
    }
}

