<?xml version="1.0"?>
<psalm
    totallyTyped="false"
    resolveFromConfigFile="true"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns="https://getpsalm.org/schema/config"
    xsi:schemaLocation="https://getpsalm.org/schema/config vendor/vimeo/psalm/config.xsd"
>
    <projectFiles>
        <directory name="src" />
        <ignoreFiles>
            <directory name="vendor" />
        </ignoreFiles>
    </projectFiles>

    <issueHandlers>
        <LessSpecificReturnType errorLevel="info" />

        <!-- level 3 issues - slightly lazy code writing, but provably low false-negatives -->

        <DeprecatedMethod errorLevel="info" />
        <DeprecatedProperty errorLevel="info" />
        <DeprecatedClass errorLevel="info" />
        <DeprecatedConstant errorLevel="info" />
        <DeprecatedFunction errorLevel="info" />
        <DeprecatedInterface errorLevel="info" />
        <DeprecatedTrait errorLevel="info" />

        <InternalMethod errorLevel="info" />
        <InternalProperty errorLevel="info" />
        <InternalClass errorLevel="info" />

        <MissingClosureReturnType errorLevel="info" />
        <MissingReturnType errorLevel="info" />
        <MissingPropertyType errorLevel="info" />
        <InvalidDocblock errorLevel="info" />
        <MisplacedRequiredParam errorLevel="info" />

        <PropertyNotSetInConstructor errorLevel="info" />
        <MissingConstructor errorLevel="info" />
        <MissingClosureParamType errorLevel="info" />
        <MissingParamType errorLevel="info" />

        <RedundantCondition errorLevel="info" />

        <DocblockTypeContradiction errorLevel="info" />
        <RedundantConditionGivenDocblockType errorLevel="info" />

        <UnresolvableInclude errorLevel="info" />

        <RawObjectIteration errorLevel="info" />

        <InvalidStringClass errorLevel="info" />
    </issueHandlers>
</psalm>
