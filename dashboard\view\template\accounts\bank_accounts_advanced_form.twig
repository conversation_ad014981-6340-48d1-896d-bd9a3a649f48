{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="submit" form="form-bank-account" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fa fa-save"></i></button>
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-university"></i> {{ text_form }}</h3>
      </div>
      <div class="panel-body">
        <form action="{{ action }}" method="post" enctype="multipart/form-data" id="form-bank-account" class="form-horizontal">
          
          <!-- معلومات الحساب الأساسية -->
          <fieldset>
            <legend>{{ text_basic_info }}</legend>
            
            <div class="form-group required">
              <label class="col-sm-2 control-label" for="input-account-number">{{ entry_account_number }}</label>
              <div class="col-sm-10">
                <input type="text" name="account_number" value="{{ account_number }}" placeholder="{{ entry_account_number }}" id="input-account-number" class="form-control" />
                {% if error_account_number %}
                <div class="text-danger">{{ error_account_number }}</div>
                {% endif %}
              </div>
            </div>

            <div class="form-group required">
              <label class="col-sm-2 control-label" for="input-account-name">{{ entry_account_name }}</label>
              <div class="col-sm-10">
                <input type="text" name="account_name" value="{{ account_name }}" placeholder="{{ entry_account_name }}" id="input-account-name" class="form-control" />
                {% if error_account_name %}
                <div class="text-danger">{{ error_account_name }}</div>
                {% endif %}
              </div>
            </div>

            <div class="form-group required">
              <label class="col-sm-2 control-label" for="input-bank-name">{{ entry_bank_name }}</label>
              <div class="col-sm-10">
                <select name="bank_name" id="input-bank-name" class="form-control select2">
                  <option value="">{{ text_select }}</option>
                  <option value="nbe"{% if bank_name == 'nbe' %} selected="selected"{% endif %}>{{ text_bank_nbe }}</option>
                  <option value="cib"{% if bank_name == 'cib' %} selected="selected"{% endif %}>{{ text_bank_cib }}</option>
                  <option value="aaib"{% if bank_name == 'aaib' %} selected="selected"{% endif %}>{{ text_bank_aaib }}</option>
                  <option value="banque_misr"{% if bank_name == 'banque_misr' %} selected="selected"{% endif %}>{{ text_bank_banque_misr }}</option>
                  <option value="qnb"{% if bank_name == 'qnb' %} selected="selected"{% endif %}>{{ text_bank_qnb }}</option>
                  <option value="hsbc"{% if bank_name == 'hsbc' %} selected="selected"{% endif %}>{{ text_bank_hsbc }}</option>
                  <option value="alex_bank"{% if bank_name == 'alex_bank' %} selected="selected"{% endif %}>{{ text_bank_alex_bank }}</option>
                  <option value="cairo"{% if bank_name == 'cairo' %} selected="selected"{% endif %}>{{ text_bank_cairo }}</option>
                  <option value="other"{% if bank_name == 'other' %} selected="selected"{% endif %}>{{ text_other }}</option>
                </select>
                {% if error_bank_name %}
                <div class="text-danger">{{ error_bank_name }}</div>
                {% endif %}
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-bank-code">{{ entry_bank_code }}</label>
              <div class="col-sm-10">
                <input type="text" name="bank_code" value="{{ bank_code }}" placeholder="{{ entry_bank_code }}" id="input-bank-code" class="form-control" />
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-branch-name">{{ entry_branch_name }}</label>
              <div class="col-sm-10">
                <input type="text" name="branch_name" value="{{ branch_name }}" placeholder="{{ entry_branch_name }}" id="input-branch-name" class="form-control" />
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-branch-code">{{ entry_branch_code }}</label>
              <div class="col-sm-10">
                <input type="text" name="branch_code" value="{{ branch_code }}" placeholder="{{ entry_branch_code }}" id="input-branch-code" class="form-control" />
              </div>
            </div>

          </fieldset>

          <!-- معلومات دولية -->
          <fieldset>
            <legend>{{ text_international_info }}</legend>
            
            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-swift-code">{{ entry_swift_code }}</label>
              <div class="col-sm-10">
                <input type="text" name="swift_code" value="{{ swift_code }}" placeholder="{{ entry_swift_code }}" id="input-swift-code" class="form-control" />
                <span class="help-block">{{ help_swift_code }}</span>
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-iban">{{ entry_iban }}</label>
              <div class="col-sm-10">
                <input type="text" name="iban" value="{{ iban }}" placeholder="{{ entry_iban }}" id="input-iban" class="form-control" />
                <span class="help-block">{{ help_iban }}</span>
              </div>
            </div>

          </fieldset>

          <!-- تفاصيل الحساب -->
          <fieldset>
            <legend>{{ text_account_details }}</legend>
            
            <div class="form-group required">
              <label class="col-sm-2 control-label" for="input-account-type">{{ entry_account_type }}</label>
              <div class="col-sm-10">
                <select name="account_type" id="input-account-type" class="form-control">
                  <option value="">{{ text_select }}</option>
                  <option value="current"{% if account_type == 'current' %} selected="selected"{% endif %}>{{ text_account_type_current }}</option>
                  <option value="savings"{% if account_type == 'savings' %} selected="selected"{% endif %}>{{ text_account_type_savings }}</option>
                  <option value="fixed_deposit"{% if account_type == 'fixed_deposit' %} selected="selected"{% endif %}>{{ text_account_type_fixed_deposit }}</option>
                  <option value="credit"{% if account_type == 'credit' %} selected="selected"{% endif %}>{{ text_account_type_credit }}</option>
                  <option value="loan"{% if account_type == 'loan' %} selected="selected"{% endif %}>{{ text_account_type_loan }}</option>
                  <option value="investment"{% if account_type == 'investment' %} selected="selected"{% endif %}>{{ text_account_type_investment }}</option>
                </select>
                {% if error_account_type %}
                <div class="text-danger">{{ error_account_type }}</div>
                {% endif %}
              </div>
            </div>

            <div class="form-group required">
              <label class="col-sm-2 control-label" for="input-currency">{{ entry_currency }}</label>
              <div class="col-sm-10">
                <select name="currency" id="input-currency" class="form-control">
                  <option value="">{{ text_select }}</option>
                  <option value="EGP"{% if currency == 'EGP' %} selected="selected"{% endif %}>{{ text_currency_egp }}</option>
                  <option value="USD"{% if currency == 'USD' %} selected="selected"{% endif %}>{{ text_currency_usd }}</option>
                  <option value="EUR"{% if currency == 'EUR' %} selected="selected"{% endif %}>{{ text_currency_eur }}</option>
                  <option value="GBP"{% if currency == 'GBP' %} selected="selected"{% endif %}>{{ text_currency_gbp }}</option>
                  <option value="SAR"{% if currency == 'SAR' %} selected="selected"{% endif %}>{{ text_currency_sar }}</option>
                  <option value="AED"{% if currency == 'AED' %} selected="selected"{% endif %}>{{ text_currency_aed }}</option>
                </select>
                {% if error_currency %}
                <div class="text-danger">{{ error_currency }}</div>
                {% endif %}
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-opening-balance">{{ entry_opening_balance }}</label>
              <div class="col-sm-10">
                <input type="text" name="opening_balance" value="{{ opening_balance }}" placeholder="{{ entry_opening_balance }}" id="input-opening-balance" class="form-control text-right" />
                {% if error_opening_balance %}
                <div class="text-danger">{{ error_opening_balance }}</div>
                {% endif %}
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-minimum-balance">{{ entry_minimum_balance }}</label>
              <div class="col-sm-10">
                <input type="text" name="minimum_balance" value="{{ minimum_balance }}" placeholder="{{ entry_minimum_balance }}" id="input-minimum-balance" class="form-control text-right" />
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-overdraft-limit">{{ entry_overdraft_limit }}</label>
              <div class="col-sm-10">
                <input type="text" name="overdraft_limit" value="{{ overdraft_limit }}" placeholder="{{ entry_overdraft_limit }}" id="input-overdraft-limit" class="form-control text-right" />
                <span class="help-block">{{ help_overdraft_limit }}</span>
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-interest-rate">{{ entry_interest_rate }}</label>
              <div class="col-sm-10">
                <div class="input-group">
                  <input type="text" name="interest_rate" value="{{ interest_rate }}" placeholder="{{ entry_interest_rate }}" id="input-interest-rate" class="form-control text-right" />
                  <span class="input-group-addon">%</span>
                </div>
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-monthly-charges">{{ entry_monthly_charges }}</label>
              <div class="col-sm-10">
                <input type="text" name="monthly_charges" value="{{ monthly_charges }}" placeholder="{{ entry_monthly_charges }}" id="input-monthly-charges" class="form-control text-right" />
              </div>
            </div>

          </fieldset>

          <!-- معلومات الاتصال -->
          <fieldset>
            <legend>{{ text_contact_info }}</legend>
            
            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-contact-person">{{ entry_contact_person }}</label>
              <div class="col-sm-10">
                <input type="text" name="contact_person" value="{{ contact_person }}" placeholder="{{ entry_contact_person }}" id="input-contact-person" class="form-control" />
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-phone">{{ entry_phone }}</label>
              <div class="col-sm-10">
                <input type="text" name="phone" value="{{ phone }}" placeholder="{{ entry_phone }}" id="input-phone" class="form-control" />
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-email">{{ entry_email }}</label>
              <div class="col-sm-10">
                <input type="email" name="email" value="{{ email }}" placeholder="{{ entry_email }}" id="input-email" class="form-control" />
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-address">{{ entry_address }}</label>
              <div class="col-sm-10">
                <textarea name="address" rows="3" placeholder="{{ entry_address }}" id="input-address" class="form-control">{{ address }}</textarea>
              </div>
            </div>

          </fieldset>

          <!-- ملاحظات -->
          <fieldset>
            <legend>{{ text_additional_info }}</legend>
            
            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-notes">{{ entry_notes }}</label>
              <div class="col-sm-10">
                <textarea name="notes" rows="3" placeholder="{{ entry_notes }}" id="input-notes" class="form-control">{{ notes }}</textarea>
              </div>
            </div>

            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-status">{{ entry_status }}</label>
              <div class="col-sm-10">
                <select name="status" id="input-status" class="form-control">
                  <option value="1"{% if status == '1' %} selected="selected"{% endif %}>{{ text_status_active }}</option>
                  <option value="0"{% if status == '0' %} selected="selected"{% endif %}>{{ text_status_inactive }}</option>
                </select>
              </div>
            </div>

          </fieldset>

        </form>
      </div>
    </div>

    <!-- معلومات الامتثال -->
    <div class="panel panel-info">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-shield"></i> {{ text_compliance_info }}</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-md-4">
            <div class="alert alert-success">
              <i class="fa fa-check"></i> {{ text_cbe_compliant }}
            </div>
          </div>
          <div class="col-md-4">
            <div class="alert alert-info">
              <i class="fa fa-balance-scale"></i> {{ text_banking_law_compliant }}
            </div>
          </div>
          <div class="col-md-4">
            <div class="alert alert-warning">
              <i class="fa fa-user-secret"></i> {{ text_anti_money_laundering }}
            </div>
          </div>
        </div>
      </div>
    </div>

  </div>
</div>

<script type="text/javascript"><!--
$('.select2').select2({
    placeholder: '{{ text_select }}',
    allowClear: true
});

// تنسيق الأرقام
$('#input-opening-balance, #input-minimum-balance, #input-overdraft-limit, #input-monthly-charges').on('keyup', function() {
    var value = $(this).val().replace(/,/g, '');
    if (!isNaN(value) && value !== '') {
        $(this).val(accounting.formatMoney(value, '', 2));
    }
});

// تحديث كود البنك تلقائياً
$('#input-bank-name').on('change', function() {
    var bankCodes = {
        'nbe': '0003',
        'cib': '0031',
        'aaib': '0013',
        'banque_misr': '0002',
        'qnb': '0097',
        'hsbc': '0135',
        'alex_bank': '0004',
        'cairo': '0006'
    };
    
    var selectedBank = $(this).val();
    if (bankCodes[selectedBank]) {
        $('#input-bank-code').val(bankCodes[selectedBank]);
    }
});

// التحقق من صحة النموذج
$('#form-bank-account').on('submit', function(e) {
    var accountNumber = $('#input-account-number').val();
    var accountName = $('#input-account-name').val();
    var bankName = $('#input-bank-name').val();
    
    if (!accountNumber || !accountName || !bankName) {
        alert('{{ error_required_fields }}');
        e.preventDefault();
        return false;
    }
});
//--></script>

{{ footer }}
