<?php
/**
 * Copyright 2016 Google Inc. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

//@codeCoverageIgnoreStart

namespace Google\Cloud\Translate\Connection;

use Google\Cloud\Translate\V2;

if (false) {
    /**
     * This class is deprecated. Use Google\Cloud\Translate\V2\Connection\Rest instead.
     * @deprecated
     */
    class Rest {}
}

class_exists(V2\Rest::class);
@trigger_error(
    'Google\Cloud\Translate\Rest is deprecated and will be ' .
    'removed in a future release. Use ' .
    'Google\Cloud\Translate\V2\Rest instead',
    E_USER_DEPRECATED
);

//@codeCoverageIgnoreEnd
