<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/iam/v1/options.proto

namespace GPBMetadata\Google\Iam\V1;

class Options
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        \GPBMetadata\Google\Api\Annotations::initOnce();
        $pool->internalAddGeneratedFile(hex2bin(
            "0af1010a1b676f6f676c652f69616d2f76312f6f7074696f6e732e70726f" .
            "746f120d676f6f676c652e69616d2e763122340a10476574506f6c696379" .
            "4f7074696f6e7312200a187265717565737465645f706f6c6963795f7665" .
            "7273696f6e1801200128054284010a11636f6d2e676f6f676c652e69616d" .
            "2e7631420c4f7074696f6e7350726f746f50015a30676f6f676c652e676f" .
            "6c616e672e6f72672f67656e70726f746f2f676f6f676c65617069732f69" .
            "616d2f76313b69616df80101aa0213476f6f676c652e436c6f75642e4961" .
            "6d2e5631ca0213476f6f676c655c436c6f75645c49616d5c563162067072" .
            "6f746f33"
        ), true);

        static::$is_initialized = true;
    }
}

