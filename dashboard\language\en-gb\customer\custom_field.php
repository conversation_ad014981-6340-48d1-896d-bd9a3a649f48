<?php
// Heading
$_['heading_title']        = 'Custom Fields';

// Text
$_['text_success']         = 'Success: You have modified custom fields!';
$_['text_list']            = 'Custom Field List';
$_['text_add']             = 'Add Custom Field';
$_['text_edit']            = 'Edit Custom Field';
$_['text_choose']          = 'Choose';
$_['text_select']          = 'Select';
$_['text_radio']           = 'Radio';
$_['text_checkbox']        = 'Checkbox';
$_['text_input']           = 'Input';
$_['text_text']            = 'Text';
$_['text_textarea']        = 'Textarea';
$_['text_file']            = 'File';
$_['text_date']            = 'Date';
$_['text_datetime']        = 'Date &amp; Time';
$_['text_time']            = 'Time';
$_['text_account']         = 'Account';
$_['text_address']         = 'Address';
$_['text_affiliate']       = 'Affiliate';
$_['text_regex']           = 'Regex';
$_['text_custom_field']    = 'Custom Field';
$_['text_value']           = 'Custom Field Values';

// Column
$_['column_name']          = 'Custom Field Name';
$_['column_location']      = 'Location';
$_['column_type']          = 'Type';
$_['column_sort_order']    = 'Sort Order';
$_['column_action']        = 'Action';

// Entry
$_['entry_name']           = 'Custom Field Name';
$_['entry_location']       = 'Location';
$_['entry_type']           = 'Type';
$_['entry_value']          = 'Value';
$_['entry_validation']     = 'Validation';
$_['entry_custom_value']   = 'Custom Field Value Name';
$_['entry_customer_group'] = 'Customer Group';
$_['entry_required']       = 'Required';
$_['entry_status']         = 'Status';
$_['entry_sort_order']     = 'Sort Order';

// Help
$_['help_regex']           = 'Use regex. E.g: /[a-zA-Z0-9_-]/';
$_['help_sort_order']      = 'Use minus to count backwards from the last field in the set.';

// Error
$_['error_permission']     = 'Warning: You do not have permission to modify custom fields!';
$_['error_name']           = 'Custom Field Name must be between 1 and 128 characters!';
$_['error_type']           = 'Warning: Custom Field Values required!';
$_['error_custom_value']   = 'Custom Value Name must be between 1 and 128 characters!';