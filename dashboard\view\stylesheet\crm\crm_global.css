/**
 * CSS شامل لجميع أنظمة CRM
 * Global CRM CSS Styles
 * 
 * الهدف: توفير تصميم موحد ومتقدم لجميع أنظمة CRM
 * الميزات: تصميم متجاوب، تأثيرات متقدمة، ألوان ذكية
 * 
 * <AUTHOR> Team
 * @version 1.0
 * @since 2024
 */

/* ========== استيراد الخطوط ========== */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css');

/* ========== المتغيرات العامة ========== */
:root {
    /* الألوان الأساسية */
    --primary-color: #007bff;
    --primary-dark: #0056b3;
    --primary-light: #66b3ff;
    
    --secondary-color: #6c757d;
    --secondary-dark: #545b62;
    --secondary-light: #adb5bd;
    
    --success-color: #28a745;
    --success-dark: #1e7e34;
    --success-light: #71dd8a;
    
    --warning-color: #ffc107;
    --warning-dark: #e0a800;
    --warning-light: #ffda6a;
    
    --danger-color: #dc3545;
    --danger-dark: #c82333;
    --danger-light: #f1959b;
    
    --info-color: #17a2b8;
    --info-dark: #138496;
    --info-light: #7dd3fc;
    
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    
    /* الألوان المتدرجة */
    --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    --gradient-success: linear-gradient(135deg, var(--success-color), var(--success-dark));
    --gradient-warning: linear-gradient(135deg, var(--warning-color), var(--warning-dark));
    --gradient-danger: linear-gradient(135deg, var(--danger-color), var(--danger-dark));
    --gradient-info: linear-gradient(135deg, var(--info-color), var(--info-dark));
    
    /* المسافات والأحجام */
    --border-radius: 12px;
    --border-radius-sm: 6px;
    --border-radius-lg: 20px;
    
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --spacing-xxl: 48px;
    
    /* الظلال */
    --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-md: 0 4px 12px rgba(0,0,0,0.15);
    --shadow-lg: 0 8px 25px rgba(0,0,0,0.2);
    --shadow-xl: 0 15px 35px rgba(0,0,0,0.25);
    
    /* التحريك */
    --transition-fast: all 0.2s ease;
    --transition-normal: all 0.3s ease;
    --transition-slow: all 0.5s ease;
    
    /* الخطوط */
    --font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-xxl: 1.5rem;
    
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
}

/* ========== إعدادات عامة ========== */
* {
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--dark-color);
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    direction: rtl;
    text-align: right;
    margin: 0;
    padding: 0;
}

/* ========== حاوي CRM الرئيسي ========== */
.crm-container {
    min-height: 100vh;
    padding: var(--spacing-lg);
    background: transparent;
}

.crm-page-header {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    padding: var(--spacing-xl);
    margin-bottom: var(--spacing-lg);
    position: relative;
    overflow: hidden;
}

.crm-page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
}

.crm-page-title {
    font-size: var(--font-size-xxl);
    font-weight: var(--font-weight-bold);
    color: var(--dark-color);
    margin: 0 0 var(--spacing-sm) 0;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.crm-page-subtitle {
    color: var(--secondary-color);
    font-size: var(--font-size-base);
    margin: 0;
}

/* ========== بطاقات الإحصائيات المتقدمة ========== */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.stat-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    padding: var(--spacing-xl);
    position: relative;
    overflow: hidden;
    transition: var(--transition-normal);
    cursor: pointer;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    transition: var(--transition-normal);
}

.stat-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

.stat-card:hover::before {
    height: 8px;
}

.stat-card-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    margin-bottom: var(--spacing-md);
    background: var(--gradient-primary);
}

.stat-card-number {
    font-size: 2.5rem;
    font-weight: var(--font-weight-bold);
    color: var(--dark-color);
    margin: 0 0 var(--spacing-xs) 0;
    line-height: 1;
}

.stat-card-label {
    color: var(--secondary-color);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin: 0;
}

.stat-card-change {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    margin-top: var(--spacing-sm);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
}

.stat-card-change.positive {
    color: var(--success-color);
}

.stat-card-change.negative {
    color: var(--danger-color);
}

/* ========== بطاقات المحتوى ========== */
.content-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    margin-bottom: var(--spacing-lg);
    overflow: hidden;
    transition: var(--transition-normal);
}

.content-card:hover {
    box-shadow: var(--shadow-lg);
}

.content-card-header {
    background: var(--gradient-primary);
    color: white;
    padding: var(--spacing-lg);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.content-card-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.content-card-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.content-card-body {
    padding: var(--spacing-xl);
}

.content-card-footer {
    background: var(--light-color);
    padding: var(--spacing-md) var(--spacing-xl);
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* ========== الأزرار المتقدمة ========== */
.btn-crm {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: var(--border-radius-sm);
    font-family: var(--font-family);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
    white-space: nowrap;
}

.btn-crm::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255,255,255,0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s, height 0.3s;
}

.btn-crm:hover::before {
    width: 300px;
    height: 300px;
}

.btn-crm:active {
    transform: scale(0.98);
}

.btn-crm-primary {
    background: var(--gradient-primary);
    color: white;
}

.btn-crm-success {
    background: var(--gradient-success);
    color: white;
}

.btn-crm-warning {
    background: var(--gradient-warning);
    color: var(--dark-color);
}

.btn-crm-danger {
    background: var(--gradient-danger);
    color: white;
}

.btn-crm-info {
    background: var(--gradient-info);
    color: white;
}

.btn-crm-outline {
    background: transparent;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
}

.btn-crm-outline:hover {
    background: var(--primary-color);
    color: white;
}

.btn-crm-lg {
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: var(--font-size-base);
}

.btn-crm-sm {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-xs);
}

/* ========== الجداول المتقدمة ========== */
.table-crm {
    width: 100%;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    margin-bottom: var(--spacing-lg);
}

.table-crm-header {
    background: var(--gradient-primary);
    color: white;
    padding: var(--spacing-lg);
}

.table-crm-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    margin: 0;
}

.table-crm-responsive {
    overflow-x: auto;
    max-height: 600px;
    overflow-y: auto;
}

.table-crm table {
    width: 100%;
    border-collapse: collapse;
    margin: 0;
}

.table-crm thead th {
    background: var(--light-color);
    color: var(--dark-color);
    padding: var(--spacing-md);
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-sm);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-bottom: 2px solid #e9ecef;
    position: sticky;
    top: 0;
    z-index: 10;
}

.table-crm tbody tr {
    transition: var(--transition-fast);
    border-bottom: 1px solid #e9ecef;
}

.table-crm tbody tr:hover {
    background: rgba(0, 123, 255, 0.05);
    transform: scale(1.01);
}

.table-crm tbody td {
    padding: var(--spacing-md);
    vertical-align: middle;
    border: none;
}

/* ========== الشارات والتسميات ========== */
.badge-crm {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-lg);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-semibold);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
}

.badge-crm::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

.badge-crm:hover::before {
    left: 100%;
}

.badge-crm-primary {
    background: var(--gradient-primary);
    color: white;
}

.badge-crm-success {
    background: var(--gradient-success);
    color: white;
}

.badge-crm-warning {
    background: var(--gradient-warning);
    color: var(--dark-color);
}

.badge-crm-danger {
    background: var(--gradient-danger);
    color: white;
}

.badge-crm-info {
    background: var(--gradient-info);
    color: white;
}

/* ========== أشرطة التقدم المتقدمة ========== */
.progress-crm {
    height: 20px;
    background: #e9ecef;
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    position: relative;
    margin: var(--spacing-sm) 0;
}

.progress-crm-bar {
    height: 100%;
    border-radius: var(--border-radius-lg);
    position: relative;
    transition: width 1s ease-in-out;
    background: var(--gradient-primary);
}

.progress-crm-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.3) 50%, transparent 70%);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.progress-crm-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-xs);
    color: white;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
    z-index: 1;
}

/* ========== الإشعارات المتقدمة ========== */
.notifications-container {
    position: fixed;
    top: var(--spacing-lg);
    right: var(--spacing-lg);
    z-index: 9999;
    max-width: 400px;
    pointer-events: none;
}

.notification-crm {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-sm);
    border-left: 4px solid var(--primary-color);
    animation: slideInRight 0.3s ease-out;
    pointer-events: auto;
    position: relative;
    overflow: hidden;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.notification-crm::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: var(--primary-color);
    animation: notificationProgress 5s linear;
}

@keyframes notificationProgress {
    from { width: 100%; }
    to { width: 0%; }
}

.notification-crm.success {
    border-left-color: var(--success-color);
}

.notification-crm.success::before {
    background: var(--success-color);
}

.notification-crm.warning {
    border-left-color: var(--warning-color);
}

.notification-crm.warning::before {
    background: var(--warning-color);
}

.notification-crm.error {
    border-left-color: var(--danger-color);
}

.notification-crm.error::before {
    background: var(--danger-color);
}

/* ========== التحميل المتقدم ========== */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.7);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    backdrop-filter: blur(5px);
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255,255,255,0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: var(--spacing-md);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    color: white;
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    text-align: center;
}

/* ========== التجاوب ========== */
@media (max-width: 1200px) {
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
}

@media (max-width: 768px) {
    .crm-container {
        padding: var(--spacing-md);
    }
    
    .crm-page-header {
        padding: var(--spacing-lg);
    }
    
    .crm-page-title {
        font-size: var(--font-size-xl);
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    .stat-card {
        padding: var(--spacing-lg);
    }
    
    .stat-card-number {
        font-size: 2rem;
    }
    
    .content-card-header {
        padding: var(--spacing-md);
        flex-direction: column;
        gap: var(--spacing-sm);
        align-items: flex-start;
    }
    
    .content-card-body {
        padding: var(--spacing-lg);
    }
    
    .table-crm tbody td {
        padding: var(--spacing-sm);
        font-size: var(--font-size-sm);
    }
    
    .notifications-container {
        right: var(--spacing-sm);
        left: var(--spacing-sm);
        max-width: none;
    }
}

@media (max-width: 576px) {
    .crm-container {
        padding: var(--spacing-sm);
    }
    
    .stat-card-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }
    
    .stat-card-number {
        font-size: 1.75rem;
    }
    
    .btn-crm {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: var(--font-size-xs);
    }
}

/* ========== تحسينات الطباعة ========== */
@media print {
    .crm-container {
        background: white;
        padding: 0;
    }
    
    .stat-card,
    .content-card,
    .table-crm {
        box-shadow: none;
        border: 1px solid #ddd;
        break-inside: avoid;
    }
    
    .btn-crm,
    .notifications-container,
    .loading-overlay {
        display: none;
    }
    
    .content-card-header {
        background: #f8f9fa !important;
        color: var(--dark-color) !important;
    }
}

/* ========== تحسينات إمكانية الوصول ========== */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0,0,0,0);
    white-space: nowrap;
    border: 0;
}

.focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* ========== تأثيرات متقدمة ========== */
.glass-effect {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
}

.gradient-text {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.shadow-hover {
    transition: var(--transition-normal);
}

.shadow-hover:hover {
    box-shadow: var(--shadow-xl);
    transform: translateY(-5px);
}

.pulse-animation {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(0, 123, 255, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(0, 123, 255, 0);
    }
}
