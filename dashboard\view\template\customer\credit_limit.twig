{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <a href="{{ add }}" data-toggle="tooltip" title="{{ button_add }}" class="btn btn-primary"><i class="fa fa-plus"></i></a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    {% if success %}
    <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-list"></i> {{ text_list }}</h3>
      </div>
      <div class="panel-body">
        <div class="table-responsive">
          <table class="table table-bordered table-hover">
            <thead>
              <tr>
                <td class="text-left">{{ column_customer }}</td>
                <td class="text-left">{{ column_email }}</td>
                <td class="text-left">{{ column_telephone }}</td>
                <td class="text-right">{{ column_credit_limit }}</td>
                <td class="text-right">{{ column_current_balance }}</td>
                <td class="text-right">{{ column_available_credit }}</td>
                <td class="text-left">{{ column_status }}</td>
                <td class="text-right">{{ column_action }}</td>
              </tr>
            </thead>
            <tbody id="customer-list">
              <!-- سيتم تحميل البيانات عبر AJAX -->
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript">
$(document).ready(function() {
    loadCustomers();
});

function loadCustomers() {
    $.ajax({
        url: 'index.php?route=customer/credit_limit/getList&user_token={{ user_token }}',
        type: 'GET',
        dataType: 'json',
        success: function(data) {
            var html = '';
            
            $.each(data.customers, function(index, customer) {
                html += '<tr>';
                html += '<td class="text-left">' + customer.name + '</td>';
                html += '<td class="text-left">' + customer.email + '</td>';
                html += '<td class="text-left">' + customer.telephone + '</td>';
                html += '<td class="text-right">' + formatCurrency(customer.credit_limit) + '</td>';
                html += '<td class="text-right">' + formatCurrency(customer.current_balance) + '</td>';
                html += '<td class="text-right">' + formatCurrency(customer.available_credit) + '</td>';
                html += '<td class="text-left"><span class="label label-' + getStatusClass(customer.status) + '">' + customer.status + '</span></td>';
                html += '<td class="text-right"><a href="' + customer.edit + '" data-toggle="tooltip" title="{{ button_edit }}" class="btn btn-primary"><i class="fa fa-pencil"></i></a></td>';
                html += '</tr>';
            });
            
            $('#customer-list').html(html);
        }
    });
}

function formatCurrency(amount) {
    return parseFloat(amount).toFixed(2);
}

function getStatusClass(status) {
    switch(status) {
        case 'active': return 'success';
        case 'suspended': return 'danger';
        case 'pending_approval': return 'warning';
        default: return 'default';
    }
}
</script>

{{ footer }} 