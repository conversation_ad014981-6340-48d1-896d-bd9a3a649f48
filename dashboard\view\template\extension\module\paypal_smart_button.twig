{{ header }}{{ column_left }}
<div id="content">
	<div class="page-header">
		<div class="container-fluid">
			<div class="pull-right">
				<button type="submit" form="form_module" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fa fa-save"></i></button>
				<a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a>
			</div>
			<h1>{{ heading_title }}</h1>
			<ul class="breadcrumb">
				{% for breadcrumb in breadcrumbs %}
				<li><a href="{{ breadcrumb['href'] }}">{{ breadcrumb['text'] }}</a></li>
				{% endfor %}
			</ul>
		</div>
	</div>
	<div class="container-fluid">
		{% if error_warning %}
		<div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
			<button type="button" class="close" data-dismiss="alert">&times;</button>
		</div>
		{% endif %}
		<div class="panel panel-default">
			<div class="panel-heading">
				<h3 class="panel-title"><i class="fa fa-pencil"></i> {{ text_edit }}</h3>
			</div>
			<div class="panel-body">   
				<form action="{{ action }}" method="post" enctype="multipart/form-data" id="form_module" class="form-horizontal">
					<ul class="nav nav-tabs">
						<li class="active"><a href="#tab_general" data-toggle="tab">{{ text_general }}</a></li>
						{% for page in setting['page'] %}
						<li><a href="#tab_{{ page['code'] }}" data-toggle="tab">{{ attribute(_context, page['name']) }}</a></li>
						{% endfor %}
					</ul>
					
					<div class="tab-content">
						<div class="tab-pane active" id="tab_general">
							<div class="form-group">
								<label class="col-sm-2 control-label" for="input_status">{{ entry_status }}</label>
								<div class="col-sm-10">
									<select name="module_paypal_smart_button_status" id="input_status" class="form-control">
										{% if status %}
										<option value="1" selected="selected">{{ text_enabled }}</option>
										<option value="0">{{ text_disabled }}</option>
										{% else %}
										<option value="1">{{ text_enabled }}</option>
										<option value="0" selected="selected">{{ text_disabled }}</option>
										{% endif %}
									</select>
								</div>
							</div>
							{% for page in setting['page'] %}
							<div class="form-group">
								<label class="col-sm-2 control-label" for="input_page_{{ page['code'] }}_status">{{ attribute(_context, 'entry_' ~ page['code'] ~ '_page_status') }}</label>
								<div class="col-sm-10">
									<select name="module_paypal_smart_button_setting[page][{{ page['code'] }}][status]" id="input_page_{{ page['code'] }}_status" class="form-control">
										{% if page['status'] %}
										<option value="1" selected="selected">{{ text_enabled }}</option>
										<option value="0">{{ text_disabled }}</option>
										{% else %}
										<option value="1">{{ text_enabled }}</option>
										<option value="0" selected="selected">{{ text_disabled }}</option>
										{% endif %}
									</select>
								</div>
							</div>
							{% endfor %}
						</div>
						{% for page in setting['page'] %}
						<div class="tab-pane" id="tab_{{ page['code'] }}">
							<div class="form-group">
								<label class="col-sm-2 control-label" for="input_page_{{ page['code'] }}_insert_tag">{{ entry_insert_tag }}</label>
								<div class="col-sm-10">
									<input type="text" name="module_paypal_smart_button_setting[page][{{ page['code'] }}][insert_tag]" value="{{ page['insert_tag'] }}" id="input_page_{{ page['code'] }}_insert_tag" class="form-control" />
								</div>
							</div>
							<div class="form-group">
								<label class="col-sm-2 control-label" for="input_page_{{ page['code'] }}_insert_type">{{ entry_insert_type }}</label>
								<div class="col-sm-10">
									<select name="module_paypal_smart_button_setting[page][{{ page['code'] }}][insert_type]" id="input_page_{{ page['code'] }}_insert_type" class="form-control">
										{% for insert_type in setting['insert_type'] %}
										{% if (insert_type['code'] == page['insert_type']) %}
										<option value="{{ insert_type['code'] }}" selected="selected">{{ attribute(_context, insert_type['name']) }}</option>
										{% else %}
										<option value="{{ insert_type['code'] }}">{{ attribute(_context, insert_type['name']) }}</option>
										{% endif %}
										{% endfor %}
									</select>
								</div>
							</div>
							<div class="form-group">
								<label class="col-sm-2 control-label" for="input_page_{{ page['code'] }}_button_align">{{ entry_button_align }}</label>
								<div class="col-sm-10">
									<select name="module_paypal_smart_button_setting[page][{{ page['code'] }}][button_align]" id="input_page_{{ page['code'] }}_button_align" class="form-control">
										{% for button_align in setting['button_align'] %}
										{% if (button_align['code'] == page['button_align']) %}
										<option value="{{ button_align['code'] }}" selected="selected">{{ attribute(_context, button_align['name']) }}</option>
										{% else %}
										<option value="{{ button_align['code'] }}">{{ attribute(_context, button_align['name']) }}</option>
										{% endif %}
										{% endfor %}
									</select>
								</div>
							</div>
							<div class="form-group">
								<label class="col-sm-2 control-label" for="input_page_{{ page['code'] }}_button_size">{{ entry_button_size }}</label>
								<div class="col-sm-10">
									<select name="module_paypal_smart_button_setting[page][{{ page['code'] }}][button_size]" id="input_page_{{ page['code'] }}_button_size" class="form-control">
										{% for button_size in setting['button_size'] %}
										{% if (button_size['code'] == page['button_size']) %}
										<option value="{{ button_size['code'] }}" selected="selected">{{ attribute(_context, button_size['name']) }}</option>
										{% else %}
										<option value="{{ button_size['code'] }}">{{ attribute(_context, button_size['name']) }}</option>
										{% endif %}
										{% endfor %}
									</select>
								</div>
							</div>
							<div class="form-group">
								<label class="col-sm-2 control-label" for="input_page_{{ page['code'] }}_button_color">{{ entry_button_color }}</label>
								<div class="col-sm-10">
									<select name="module_paypal_smart_button_setting[page][{{ page['code'] }}][button_color]" id="input_page_{{ page['code'] }}_button_color" class="form-control">
										{% for button_color in setting['button_color'] %}
										{% if (button_color['code'] == page['button_color']) %}
										<option value="{{ button_color['code'] }}" selected="selected">{{ attribute(_context, button_color['name']) }}</option>
										{% else %}
										<option value="{{ button_color['code'] }}">{{ attribute(_context, button_color['name']) }}</option>
										{% endif %}
										{% endfor %}
									</select>
								</div>
							</div>
							<div class="form-group">
								<label class="col-sm-2 control-label" for="input_page_{{ page['code'] }}_shape">{{ entry_button_shape }}</label>
								<div class="col-sm-10">
									<select name="module_paypal_smart_button_setting[page][{{ page['code'] }}][button_shape]" id="input_page_{{ page['code'] }}_shape" class="form-control">
										{% for button_shape in setting['button_shape'] %}
										{% if (button_shape['code'] == page['button_shape']) %}
										<option value="{{ button_shape['code'] }}" selected="selected">{{ attribute(_context, button_shape['name']) }}</option>
										{% else %}
										<option value="{{ button_shape['code'] }}">{{ attribute(_context, button_shape['name']) }}</option>
										{% endif %}
										{% endfor %}
									</select>
								</div>
							</div>
							<div class="form-group">
								<label class="col-sm-2 control-label" for="input_page_{{ page['code'] }}_button_label">{{ entry_button_label }}</label>
								<div class="col-sm-10">
									<select name="module_paypal_smart_button_setting[page][{{ page['code'] }}][button_label]" id="input_page_{{ page['code'] }}_button_label" class="form-control">
										{% for button_label in setting['button_label'] %}
										{% if (button_label['code'] == page['button_label']) %}
										<option value="{{ button_label['code'] }}" selected="selected">{{ attribute(_context, button_label['name']) }}</option>
										{% else %}
										<option value="{{ button_label['code'] }}">{{ attribute(_context, button_label['name']) }}</option>
										{% endif %}
										{% endfor %}
									</select>
								</div>
							</div>
							<div class="form-group">
								<label class="col-sm-2 control-label" for="input_button_tagline">{{ entry_button_tagline }}</label>
								<div class="col-sm-10">
									<select name="module_paypal_smart_button_tagline" id="input_button_tagline" class="form-control">
										{% for button_tagline in setting['button_tagline'] %}
										{% if (button_tagline['code'] == page['button_tagline']) %}
										<option value="{{ button_tagline['code'] }}" selected="selected">{{ attribute(_context, button_tagline['name']) }}</option>
										{% else %}
										<option value="{{ button_tagline['code'] }}">{{ attribute(_context, button_tagline['name']) }}</option>
										{% endif %}
										{% endfor %}
									</select>
								</div>
							</div>
							<div class="form-group">
								<label class="col-sm-2 control-label" for="input_page_{{ page['code'] }}_message_status"><span data-toggle="tooltip" title="{{ help_message_status }}">{{ entry_message_status }}</span></label>
								<div class="col-sm-10">
									<select name="module_paypal_smart_button_setting[page][{{ page['code'] }}][message_status]" id="input_page_{{ page['code'] }}_message_status" class="form-control">
										{% if page['message_status'] %}
										<option value="1" selected="selected">{{ text_enabled }}</option>
										<option value="0">{{ text_disabled }}</option>
										{% else %}
										<option value="1">{{ text_enabled }}</option>
										<option value="0" selected="selected">{{ text_disabled }}</option>
										{% endif %}
									</select>
								</div>
							</div>
							<div class="form-group">
								<label class="col-sm-2 control-label" for="input_page_{{ page['code'] }}_message_align">{{ entry_message_align }}</label>
								<div class="col-sm-10">
									<select name="module_paypal_smart_button_setting[page][{{ page['code'] }}][message_align]" id="input_page_{{ page['code'] }}_message_align" class="form-control">
										{% for message_align in setting['message_align'] %}
										{% if (message_align['code'] == page['message_align']) %}
										<option value="{{ message_align['code'] }}" selected="selected">{{ attribute(_context, message_align['name']) }}</option>
										{% else %}
										<option value="{{ message_align['code'] }}">{{ attribute(_context, message_align['name']) }}</option>
										{% endif %}
										{% endfor %}
									</select>
								</div>
							</div>
							<div class="form-group">
								<label class="col-sm-2 control-label" for="input_page_{{ page['code'] }}_message_size">{{ entry_message_size }}</label>
								<div class="col-sm-10">
									<select name="module_paypal_smart_button_setting[page][{{ page['code'] }}][message_size]" id="input_page_{{ page['code'] }}_message_size" class="form-control">
										{% for message_size in setting['message_size'] %}
										{% if (message_size['code'] == page['message_size']) %}
										<option value="{{ message_size['code'] }}" selected="selected">{{ attribute(_context, message_size['name']) }}</option>
										{% else %}
										<option value="{{ message_size['code'] }}">{{ attribute(_context, message_size['name']) }}</option>
										{% endif %}
										{% endfor %}
									</select>
								</div>
							</div>
							<div class="form-group">
								<label class="col-sm-2 control-label" for="input_page_{{ page['code'] }}_message_layout">{{ entry_message_layout }}</label>
								<div class="col-sm-10">
									<select name="module_paypal_smart_button_setting[page][{{ page['code'] }}][message_layout]" id="input_page_{{ page['code'] }}_message_layout" class="form-control">
										{% for message_layout in setting['message_layout'] %}
										{% if (message_layout['code'] == page['message_layout']) %}
										<option value="{{ message_layout['code'] }}" selected="selected">{{ attribute(_context, message_layout['name']) }}</option>
										{% else %}
										<option value="{{ message_layout['code'] }}">{{ attribute(_context, message_layout['name']) }}</option>
										{% endif %}
										{% endfor %}
									</select>
								</div>
							</div>
							<div class="form-group {% if (page['message_layout'] == 'flex') %}hidden{% endif %}">
								<label class="col-sm-2 control-label" for="input_page_{{ page['code'] }}_message_text_color">{{ entry_message_text_color }}</label>
								<div class="col-sm-10">
									<select name="module_paypal_smart_button_setting[page][{{ page['code'] }}][message_text_color]" id="input_page_{{ page['code'] }}_message_text_color" class="form-control">
										{% for message_text_color in setting['message_text_color'] %}
										{% if (message_text_color['code'] == page['message_text_color']) %}
										<option value="{{ message_text_color['code'] }}" selected="selected">{{ attribute(_context, message_text_color['name']) }}</option>
										{% else %}
										<option value="{{ message_text_color['code'] }}">{{ attribute(_context, message_text_color['name']) }}</option>
										{% endif %}
										{% endfor %}
									</select>
								</div>
							</div>
							<div class="form-group {% if (page['message_layout'] == 'flex') %}hidden{% endif %}">
								<label class="col-sm-2 control-label" for="input_page_{{ page['code'] }}_message_text_size">{{ entry_message_text_size }}</label>
								<div class="col-sm-10">
									<select name="module_paypal_smart_button_setting[page][{{ page['code'] }}][message_text_size]" id="input_page_{{ page['code'] }}_message_text_size" class="form-control">
										{% for message_text_size in setting['message_text_size'] %}
										{% if (message_text_size == page['message_text_size']) %}
										<option value="{{ message_text_size }}" selected="selected">{{ message_text_size }}</option>
										{% else %}
										<option value="{{ message_text_size }}">{{ message_text_size }}</option>
										{% endif %}
										{% endfor %}
									</select>
								</div>
							</div>
							<div class="form-group {% if (page['message_layout'] == 'text') %}hidden{% endif %}">
								<label class="col-sm-2 control-label" for="input_page_{{ page['code'] }}_message_flex_color">{{ entry_message_flex_color }}</label>
								<div class="col-sm-10">
									<select name="module_paypal_smart_button_setting[page][{{ page['code'] }}][message_flex_color]" id="input_page_{{ page['code'] }}_message_flex_color" class="form-control">
										{% for message_flex_color in setting['message_flex_color'] %}
										{% if (message_flex_color['code'] == page['message_flex_color']) %}
										<option value="{{ message_flex_color['code'] }}" selected="selected">{{ attribute(_context, message_flex_color['name']) }}</option>
										{% else %}
										<option value="{{ message_flex_color['code'] }}">{{ attribute(_context, message_flex_color['name']) }}</option>
										{% endif %}
										{% endfor %}
									</select>
								</div>
							</div>
							<div class="form-group {% if (page['message_layout'] == 'text') %}hidden{% endif %}">
								<label class="col-sm-2 control-label" for="input_page_{{ page['code'] }}_message_flex_ratio">{{ entry_message_flex_ratio }}</label>
								<div class="col-sm-10">
									<select name="module_paypal_smart_button_setting[page][{{ page['code'] }}][message_flex_ratio]" id="input_page_{{ page['code'] }}_message_flex_ratio" class="form-control">
										{% for message_flex_ratio in setting['message_flex_ratio'] %}
										{% if (message_flex_ratio == page['message_flex_ratio']) %}
										<option value="{{ message_flex_ratio }}" selected="selected">{{ message_flex_ratio }}</option>
										{% else %}
										<option value="{{ message_flex_ratio }}">{{ message_flex_ratio }}</option>
										{% endif %}
										{% endfor %}
									</select>
								</div>
							</div>
						</div>
						{% endfor %}
					</div>
				</form>
			</div>
		</div>
	</div>
</div>
<script type="text/javascript">

$('#input_page_product_message_layout').on('change', function() {
	var layout = $(this).val();
	
	if (layout == 'text') {
		$('#input_page_product_message_flex_color').parents('.form-group').addClass('hidden');
		$('#input_page_product_message_flex_ratio').parents('.form-group').addClass('hidden');
		$('#input_page_product_message_text_color').parents('.form-group').removeClass('hidden');
		$('#input_page_product_message_text_size').parents('.form-group').removeClass('hidden');
	} else {
		$('#input_page_product_message_text_color').parents('.form-group').addClass('hidden');
		$('#input_page_product_message_text_size').parents('.form-group').addClass('hidden');
		$('#input_page_product_message_flex_color').parents('.form-group').removeClass('hidden');
		$('#input_page_product_message_flex_ratio').parents('.form-group').removeClass('hidden');
	}
});

$('#input_page_cart_message_layout').on('change', function() {
	var layout = $(this).val();
	
	if (layout == 'text') {
		$('#input_page_cart_message_flex_color').parents('.form-group').addClass('hidden');
		$('#input_page_cart_message_flex_ratio').parents('.form-group').addClass('hidden');
		$('#input_page_cart_message_text_color').parents('.form-group').removeClass('hidden');
		$('#input_page_cart_message_text_size').parents('.form-group').removeClass('hidden');
	} else {
		$('#input_page_cart_message_text_color').parents('.form-group').addClass('hidden');
		$('#input_page_cart_message_text_size').parents('.form-group').addClass('hidden');
		$('#input_page_cart_message_flex_color').parents('.form-group').removeClass('hidden');
		$('#input_page_cart_message_flex_ratio').parents('.form-group').removeClass('hidden');
	}
});

</script>
{{ footer }}