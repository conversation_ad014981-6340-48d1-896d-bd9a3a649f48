<?php
// Heading
$_['heading_title']                            = 'EC-Ship';

// Text
$_['text_extension']                           = 'Extension';
$_['text_success']                             = 'Success: You have modified EC-Ship shipping!';
$_['text_edit']                                = 'Edit EC-Ship Shipping';
$_['text_air_registered_mail']                 = 'Air Registered Mail';
$_['text_air_parcel']                          = 'Air Parcel(Package maxiumn weight 20KG)';
$_['text_e_express_service_to_us']             = 'e-Express Service to US(Package maxiumn weight 2KG)';
$_['text_e_express_service_to_canada']         = 'e-Express Service to Canada(Package maxiumn weight 2KG)';
$_['text_e_express_service_to_united_kingdom'] = 'e-Express Service to United Kingdom(Package maxiumn weight 2KG)';
$_['text_e_express_service_to_russia']         = 'e-Express Service to Russia(Package maxiumn weight 2KG)';
$_['text_e_express_service_one']               = 'e-Express service (including Germany, France and Norway)(Package maxiumn weight 2KG)';
$_['text_e_express_service_two']               = 'e-Express service (including Australia, New Zealand, Korea, Singapore and Vietnam)(Package maxiumn weight 2KG)';
$_['text_speed_post']                          = 'SpeedPost (Standard Service)(Package maxiumn weight 30KG)';
$_['text_smart_post']                          = 'Smart Post(Package maxiumn weight 2KG)';
$_['text_local_courier_post']                  = 'Local Courier Post (Counter Collection)(Package maxiumn weight 2KG)';
$_['text_local_parcel']                        = 'Local Parcel (Package maxiumn weight 20KG)';

// Entry
$_['entry_api_key']                            = 'API Key';
$_['entry_username']                           = 'Integrator Username';
$_['entry_api_username']                       = 'API Username';
$_['entry_test']                               = 'Test Mode';
$_['entry_service']                            = 'Services';
$_['entry_weight_class']                       = 'Weight Class';
$_['entry_tax_class']                          = 'Tax Class';
$_['entry_geo_zone']                           = 'Geo Zone';
$_['entry_status']                             = 'Status';
$_['entry_sort_order']                         = 'Sort Order';

// Help
$_['help_api_key']                             = 'Enter the API key assigned to you by EC-SHIP.';
$_['help_username']                            = 'Enter your EC-SHIP account Integrator username.';
$_['help_api_username']                        = 'Enter your EC-SHIP account API username.';
$_['help_test']                                = 'Use this module in Test (YES) or Production mode (NO)?';
$_['help_service']                             = 'Select the EC-SHIP services to be offered.';
$_['help_weight_class']                        = 'Set to kilograms only.';

// Error
$_['error_permission']                         = 'Warning: You do not have permission to modify EC-SHIP shipping!';
$_['error_api_key']                            = 'Access API Key Required!';
$_['error_username']                           = 'Username Required!';
$_['error_api_username']                       = 'API Username Required!';
