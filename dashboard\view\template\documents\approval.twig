{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="submit" form="documents\approval-form" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fas fa-save"></i></button>
        <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-light"><i class="fas fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    
    <div class="card">
      <div class="card-header"><i class="fas fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}chart-bar{% elif screen_type == 'settings' %}cog{% else %}pencil-alt{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="card-body">
        <form id="documents\approval-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-approval_levels">{{ text_approval_levels }}</label>
            <div class="col-sm-10">
              <input type="text" name="approval_levels" value="{{ approval_levels }}" placeholder="{{ text_approval_levels }}" id="input-approval_levels" class="form-control" />
              {% if error_approval_levels %}
                <div class="invalid-feedback">{{ error_approval_levels }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-approval_stats">{{ text_approval_stats }}</label>
            <div class="col-sm-10">
              <input type="text" name="approval_stats" value="{{ approval_stats }}" placeholder="{{ text_approval_stats }}" id="input-approval_stats" class="form-control" />
              {% if error_approval_stats %}
                <div class="invalid-feedback">{{ error_approval_stats }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-approval_statuses">{{ text_approval_statuses }}</label>
            <div class="col-sm-10">
              <input type="text" name="approval_statuses" value="{{ approval_statuses }}" placeholder="{{ text_approval_statuses }}" id="input-approval_statuses" class="form-control" />
              {% if error_approval_statuses %}
                <div class="invalid-feedback">{{ error_approval_statuses }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-approval_types">{{ text_approval_types }}</label>
            <div class="col-sm-10">
              <input type="text" name="approval_types" value="{{ approval_types }}" placeholder="{{ text_approval_types }}" id="input-approval_types" class="form-control" />
              {% if error_approval_types %}
                <div class="invalid-feedback">{{ error_approval_types }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-bulk_approve">{{ text_bulk_approve }}</label>
            <div class="col-sm-10">
              <input type="text" name="bulk_approve" value="{{ bulk_approve }}" placeholder="{{ text_bulk_approve }}" id="input-bulk_approve" class="form-control" />
              {% if error_bulk_approve %}
                <div class="invalid-feedback">{{ error_bulk_approve }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-completed_approvals">{{ text_completed_approvals }}</label>
            <div class="col-sm-10">
              <input type="text" name="completed_approvals" value="{{ completed_approvals }}" placeholder="{{ text_completed_approvals }}" id="input-completed_approvals" class="form-control" />
              {% if error_completed_approvals %}
                <div class="invalid-feedback">{{ error_completed_approvals }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-delegate">{{ text_delegate }}</label>
            <div class="col-sm-10">
              <input type="text" name="delegate" value="{{ delegate }}" placeholder="{{ text_delegate }}" id="input-delegate" class="form-control" />
              {% if error_delegate %}
                <div class="invalid-feedback">{{ error_delegate }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="invalid-feedback">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-new_request">{{ text_new_request }}</label>
            <div class="col-sm-10">
              <input type="text" name="new_request" value="{{ new_request }}" placeholder="{{ text_new_request }}" id="input-new_request" class="form-control" />
              {% if error_new_request %}
                <div class="invalid-feedback">{{ error_new_request }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-pending_approvals">{{ text_pending_approvals }}</label>
            <div class="col-sm-10">
              <input type="text" name="pending_approvals" value="{{ pending_approvals }}" placeholder="{{ text_pending_approvals }}" id="input-pending_approvals" class="form-control" />
              {% if error_pending_approvals %}
                <div class="invalid-feedback">{{ error_pending_approvals }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-performance_metrics">{{ text_performance_metrics }}</label>
            <div class="col-sm-10">
              <input type="text" name="performance_metrics" value="{{ performance_metrics }}" placeholder="{{ text_performance_metrics }}" id="input-performance_metrics" class="form-control" />
              {% if error_performance_metrics %}
                <div class="invalid-feedback">{{ error_performance_metrics }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-reports">{{ text_reports }}</label>
            <div class="col-sm-10">
              <input type="text" name="reports" value="{{ reports }}" placeholder="{{ text_reports }}" id="input-reports" class="form-control" />
              {% if error_reports %}
                <div class="invalid-feedback">{{ error_reports }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sent_requests">{{ text_sent_requests }}</label>
            <div class="col-sm-10">
              <input type="text" name="sent_requests" value="{{ sent_requests }}" placeholder="{{ text_sent_requests }}" id="input-sent_requests" class="form-control" />
              {% if error_sent_requests %}
                <div class="invalid-feedback">{{ error_sent_requests }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-settings">{{ text_settings }}</label>
            <div class="col-sm-10">
              <input type="text" name="settings" value="{{ settings }}" placeholder="{{ text_settings }}" id="input-settings" class="form-control" />
              {% if error_settings %}
                <div class="invalid-feedback">{{ error_settings }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="invalid-feedback">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}