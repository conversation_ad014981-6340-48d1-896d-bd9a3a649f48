{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="submit" form="inventory\warehouse-form" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fas fa-save"></i></button>
        <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-light"><i class="fas fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    
    <div class="card">
      <div class="card-header"><i class="fas fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}chart-bar{% elif screen_type == 'settings' %}cog{% else %}pencil-alt{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="card-body">
        <form id="inventory\warehouse-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-address">{{ text_address }}</label>
            <div class="col-sm-10">
              <input type="text" name="address" value="{{ address }}" placeholder="{{ text_address }}" id="input-address" class="form-control" />
              {% if error_address %}
                <div class="invalid-feedback">{{ error_address }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-adjustment_reasons">{{ text_adjustment_reasons }}</label>
            <div class="col-sm-10">
              <input type="text" name="adjustment_reasons" value="{{ adjustment_reasons }}" placeholder="{{ text_adjustment_reasons }}" id="input-adjustment_reasons" class="form-control" />
              {% if error_adjustment_reasons %}
                <div class="invalid-feedback">{{ error_adjustment_reasons }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-code">{{ text_code }}</label>
            <div class="col-sm-10">
              <input type="text" name="code" value="{{ code }}" placeholder="{{ text_code }}" id="input-code" class="form-control" />
              {% if error_code %}
                <div class="invalid-feedback">{{ error_code }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_code">{{ text_error_code }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_code" value="{{ error_code }}" placeholder="{{ text_error_code }}" id="input-error_code" class="form-control" />
              {% if error_error_code %}
                <div class="invalid-feedback">{{ error_error_code }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_name">{{ text_error_name }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_name" value="{{ error_name }}" placeholder="{{ text_error_name }}" id="input-error_name" class="form-control" />
              {% if error_error_name %}
                <div class="invalid-feedback">{{ error_error_name }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-expiry_alerts">{{ text_expiry_alerts }}</label>
            <div class="col-sm-10">
              <input type="text" name="expiry_alerts" value="{{ expiry_alerts }}" placeholder="{{ text_expiry_alerts }}" id="input-expiry_alerts" class="form-control" />
              {% if error_expiry_alerts %}
                <div class="invalid-feedback">{{ error_expiry_alerts }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-from_warehouse_id">{{ text_from_warehouse_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="from_warehouse_id" value="{{ from_warehouse_id }}" placeholder="{{ text_from_warehouse_id }}" id="input-from_warehouse_id" class="form-control" />
              {% if error_from_warehouse_id %}
                <div class="invalid-feedback">{{ error_from_warehouse_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="invalid-feedback">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-low_stock_alerts">{{ text_low_stock_alerts }}</label>
            <div class="col-sm-10">
              <input type="text" name="low_stock_alerts" value="{{ low_stock_alerts }}" placeholder="{{ text_low_stock_alerts }}" id="input-low_stock_alerts" class="form-control" />
              {% if error_low_stock_alerts %}
                <div class="invalid-feedback">{{ error_low_stock_alerts }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-manager">{{ text_manager }}</label>
            <div class="col-sm-10">
              <input type="text" name="manager" value="{{ manager }}" placeholder="{{ text_manager }}" id="input-manager" class="form-control" />
              {% if error_manager %}
                <div class="invalid-feedback">{{ error_manager }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-name">{{ text_name }}</label>
            <div class="col-sm-10">
              <input type="text" name="name" value="{{ name }}" placeholder="{{ text_name }}" id="input-name" class="form-control" />
              {% if error_name %}
                <div class="invalid-feedback">{{ error_name }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-order">{{ text_order }}</label>
            <div class="col-sm-10">
              <input type="text" name="order" value="{{ order }}" placeholder="{{ text_order }}" id="input-order" class="form-control" />
              {% if error_order %}
                <div class="invalid-feedback">{{ error_order }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-pagination">{{ text_pagination }}</label>
            <div class="col-sm-10">
              <input type="text" name="pagination" value="{{ pagination }}" placeholder="{{ text_pagination }}" id="input-pagination" class="form-control" />
              {% if error_pagination %}
                <div class="invalid-feedback">{{ error_pagination }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-recent_adjustments">{{ text_recent_adjustments }}</label>
            <div class="col-sm-10">
              <input type="text" name="recent_adjustments" value="{{ recent_adjustments }}" placeholder="{{ text_recent_adjustments }}" id="input-recent_adjustments" class="form-control" />
              {% if error_recent_adjustments %}
                <div class="invalid-feedback">{{ error_recent_adjustments }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-recent_movements">{{ text_recent_movements }}</label>
            <div class="col-sm-10">
              <input type="text" name="recent_movements" value="{{ recent_movements }}" placeholder="{{ text_recent_movements }}" id="input-recent_movements" class="form-control" />
              {% if error_recent_movements %}
                <div class="invalid-feedback">{{ error_recent_movements }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-results">{{ text_results }}</label>
            <div class="col-sm-10">
              <input type="text" name="results" value="{{ results }}" placeholder="{{ text_results }}" id="input-results" class="form-control" />
              {% if error_results %}
                <div class="invalid-feedback">{{ error_results }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-selected">{{ text_selected }}</label>
            <div class="col-sm-10">
              <input type="text" name="selected" value="{{ selected }}" placeholder="{{ text_selected }}" id="input-selected" class="form-control" />
              {% if error_selected %}
                <div class="invalid-feedback">{{ error_selected }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort">{{ text_sort }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort" value="{{ sort }}" placeholder="{{ text_sort }}" id="input-sort" class="form-control" />
              {% if error_sort %}
                <div class="invalid-feedback">{{ error_sort }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_code">{{ text_sort_code }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_code" value="{{ sort_code }}" placeholder="{{ text_sort_code }}" id="input-sort_code" class="form-control" />
              {% if error_sort_code %}
                <div class="invalid-feedback">{{ error_sort_code }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_name">{{ text_sort_name }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_name" value="{{ sort_name }}" placeholder="{{ text_sort_name }}" id="input-sort_name" class="form-control" />
              {% if error_sort_name %}
                <div class="invalid-feedback">{{ error_sort_name }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_status">{{ text_sort_status }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_status" value="{{ sort_status }}" placeholder="{{ text_sort_status }}" id="input-sort_status" class="form-control" />
              {% if error_sort_status %}
                <div class="invalid-feedback">{{ error_sort_status }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-status">{{ text_status }}</label>
            <div class="col-sm-10">
              <input type="text" name="status" value="{{ status }}" placeholder="{{ text_status }}" id="input-status" class="form-control" />
              {% if error_status %}
                <div class="invalid-feedback">{{ error_status }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-telephone">{{ text_telephone }}</label>
            <div class="col-sm-10">
              <input type="text" name="telephone" value="{{ telephone }}" placeholder="{{ text_telephone }}" id="input-telephone" class="form-control" />
              {% if error_telephone %}
                <div class="invalid-feedback">{{ error_telephone }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_form">{{ text_text_form }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_form" value="{{ text_form }}" placeholder="{{ text_text_form }}" id="input-text_form" class="form-control" />
              {% if error_text_form %}
                <div class="invalid-feedback">{{ error_text_form }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-transfer_requests">{{ text_transfer_requests }}</label>
            <div class="col-sm-10">
              <input type="text" name="transfer_requests" value="{{ transfer_requests }}" placeholder="{{ text_transfer_requests }}" id="input-transfer_requests" class="form-control" />
              {% if error_transfer_requests %}
                <div class="invalid-feedback">{{ error_transfer_requests }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="invalid-feedback">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-warehouse_stats">{{ text_warehouse_stats }}</label>
            <div class="col-sm-10">
              <input type="text" name="warehouse_stats" value="{{ warehouse_stats }}" placeholder="{{ text_warehouse_stats }}" id="input-warehouse_stats" class="form-control" />
              {% if error_warehouse_stats %}
                <div class="invalid-feedback">{{ error_warehouse_stats }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-warehouse_utilization">{{ text_warehouse_utilization }}</label>
            <div class="col-sm-10">
              <input type="text" name="warehouse_utilization" value="{{ warehouse_utilization }}" placeholder="{{ text_warehouse_utilization }}" id="input-warehouse_utilization" class="form-control" />
              {% if error_warehouse_utilization %}
                <div class="invalid-feedback">{{ error_warehouse_utilization }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-warehouses">{{ text_warehouses }}</label>
            <div class="col-sm-10">
              <input type="text" name="warehouses" value="{{ warehouses }}" placeholder="{{ text_warehouses }}" id="input-warehouses" class="form-control" />
              {% if error_warehouses %}
                <div class="invalid-feedback">{{ error_warehouses }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}