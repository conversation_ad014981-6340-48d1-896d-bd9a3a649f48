{{ header }}
{{ column_left }}

<div id="content">
    <div class="page-header">
        <div class="container-fluid">
            <div class="pull-right">
                <button type="button" id="button-save" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary">
                    <i class="fa fa-save"></i>
                </button>
                <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default">
                    <i class="fa fa-reply"></i>
                </a>
            </div>
            <h1>{{ heading_title }}</h1>
            <ul class="breadcrumb">
                <li><a href="{{ home }}">{{ text_home }}</a></li>
                <li><a href="{{ hr_dashboard }}">{{ text_hr_dashboard }}</a></li>
                <li class="active">{{ heading_title }}</li>
            </ul>
        </div>
    </div>

    <div class="container-fluid">
        {% if error_warning %}
            <div class="alert alert-danger alert-dismissible">
                <i class="fa fa-exclamation-circle"></i> {{ error_warning }}
                <button type="button" class="close" data-dismiss="alert">&times;</button>
            </div>
        {% endif %}

        <div class="panel panel-default">
            <div class="panel-heading">
                <h3 class="panel-title">
                    <i class="fa fa-user"></i> {{ text_employee_profile }}
                </h3>
            </div>
            <div class="panel-body">
                <form action="" method="post" enctype="multipart/form-data" id="form-employee" class="form-horizontal">
                    
                    <!-- Employee Basic Information -->
                    <div class="form-group required">
                        <label class="col-sm-2 control-label" for="input-user-id">{{ text_user_id }}</label>
                        <div class="col-sm-10">
                            <select name="user_id" id="input-user-id" class="form-control select2">
                                <option value="">{{ text_select_user }}</option>
                                {% for user in users %}
                                    <option value="{{ user.user_id }}" {% if user.user_id == user_id %}selected{% endif %}>
                                        {{ user.firstname }} {{ user.lastname }}
                                    </option>
                                {% endfor %}
                            </select>
                            {% if error_user_id %}
                                <div class="text-danger">{{ error_user_id }}</div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="form-group required">
                        <label class="col-sm-2 control-label" for="input-job-title">{{ text_job_title }}</label>
                        <div class="col-sm-10">
                            <input type="text" name="job_title" value="{{ job_title }}" placeholder="{{ text_job_title }}" 
                                   id="input-job-title" class="form-control" />
                            {% if error_job_title %}
                                <div class="text-danger">{{ error_job_title }}</div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="form-group required">
                        <label class="col-sm-2 control-label" for="input-hiring-date">{{ text_hiring_date }}</label>
                        <div class="col-sm-10">
                            <div class="input-group date">
                                <input type="text" name="hiring_date" value="{{ hiring_date }}" placeholder="{{ text_hiring_date }}" 
                                       id="input-hiring-date" class="form-control" />
                                <span class="input-group-addon">
                                    <i class="fa fa-calendar"></i>
                                </span>
                            </div>
                            {% if error_hiring_date %}
                                <div class="text-danger">{{ error_hiring_date }}</div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="form-group required">
                        <label class="col-sm-2 control-label" for="input-salary">{{ text_salary }}</label>
                        <div class="col-sm-10">
                            <div class="input-group">
                                <input type="number" name="salary" value="{{ salary }}" placeholder="{{ text_salary }}" 
                                       id="input-salary" class="form-control" step="0.01" min="0" />
                                <span class="input-group-addon">{{ currency_code }}</span>
                            </div>
                            {% if error_salary %}
                                <div class="text-danger">{{ error_salary }}</div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-2 control-label" for="input-status">{{ text_status }}</label>
                        <div class="col-sm-10">
                            <select name="status" id="input-status" class="form-control">
                                <option value="1" {% if status == '1' %}selected{% endif %}>{{ text_active }}</option>
                                <option value="0" {% if status == '0' %}selected{% endif %}>{{ text_inactive }}</option>
                                <option value="2" {% if status == '2' %}selected{% endif %}>{{ text_terminated }}</option>
                            </select>
                        </div>
                    </div>

                    <!-- Employee Documents Section -->
                    {% if employee_id %}
                    <div class="form-group">
                        <label class="col-sm-2 control-label">{{ text_documents }}</label>
                        <div class="col-sm-10">
                            <div class="well">
                                <button type="button" id="button-add-document" class="btn btn-success btn-sm">
                                    <i class="fa fa-plus"></i> {{ button_add_document }}
                                </button>
                                <div id="documents-list" class="table-responsive" style="margin-top: 15px;">
                                    <!-- Documents will be loaded here via AJAX -->
                                </div>
                            </div>
                        </div>
                    </div>
                    {% else %}
                    <div class="form-group">
                        <label class="col-sm-2 control-label">{{ text_documents }}</label>
                        <div class="col-sm-10">
                            <div class="alert alert-info">
                                <i class="fa fa-info-circle"></i> {{ text_save_employee_first }}
                            </div>
                        </div>
                    </div>
                    {% endif %}

                </form>
            </div>
        </div>
    </div>
</div>

<!-- Document Upload Modal -->
<div class="modal fade" id="modal-document" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title">{{ text_add_document }}</h4>
            </div>
            <div class="modal-body">
                <form id="form-document" enctype="multipart/form-data">
                    <div class="form-group">
                        <label for="document-name">{{ text_document_name }}</label>
                        <input type="text" id="document-name" name="document_name" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="document-description">{{ text_document_description }}</label>
                        <textarea id="document-description" name="document_description" class="form-control" rows="3"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="document-file">{{ text_file }}</label>
                        <input type="file" id="document-file" name="document_file" class="form-control" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_close }}</button>
                <button type="button" id="button-upload-document" class="btn btn-primary">{{ button_save }}</button>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
$(document).ready(function() {
    // Initialize Select2
    $('.select2').select2({
        placeholder: "{{ text_select_user }}",
        allowClear: true
    });

    // Initialize Date Picker
    $('#input-hiring-date').datetimepicker({
        format: 'YYYY-MM-DD',
        pickTime: false
    });

    // Save Employee
    $('#button-save').on('click', function() {
        var formData = $('#form-employee').serialize();
        
        $.ajax({
            url: '{{ ajax_save_url }}',
            type: 'POST',
            data: formData,
            dataType: 'json',
            beforeSend: function() {
                $('#button-save').button('loading');
            },
            complete: function() {
                $('#button-save').button('reset');
            },
            success: function(json) {
                $('.alert-dismissible').remove();
                
                if (json['error']) {
                    $('#content').prepend('<div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> ' + json['error'] + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
                }
                
                if (json['success']) {
                    $('#content').prepend('<div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> ' + json['success'] + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
                    
                    if (json['employee_id']) {
                        // Redirect to edit mode if new employee was created
                        window.location = '{{ edit_url }}&employee_id=' + json['employee_id'];
                    }
                }
            },
            error: function(xhr, ajaxOptions, thrownError) {
                alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
            }
        });
    });

    // Load Documents
    {% if employee_id %}
    loadDocuments();
    {% endif %}

    // Add Document Button
    $('#button-add-document').on('click', function() {
        $('#modal-document').modal('show');
    });

    // Upload Document
    $('#button-upload-document').on('click', function() {
        var formData = new FormData($('#form-document')[0]);
        formData.append('employee_id', '{{ employee_id }}');
        
        $.ajax({
            url: '{{ ajax_document_upload_url }}',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            dataType: 'json',
            beforeSend: function() {
                $('#button-upload-document').button('loading');
            },
            complete: function() {
                $('#button-upload-document').button('reset');
            },
            success: function(json) {
                if (json['error']) {
                    alert(json['error']);
                }
                
                if (json['success']) {
                    $('#modal-document').modal('hide');
                    $('#form-document')[0].reset();
                    loadDocuments();
                    
                    $('#content').prepend('<div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> ' + json['success'] + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
                }
            },
            error: function(xhr, ajaxOptions, thrownError) {
                alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
            }
        });
    });

    // Load Documents Function
    function loadDocuments() {
        $.ajax({
            url: '{{ ajax_documents_list_url }}',
            type: 'POST',
            data: {employee_id: '{{ employee_id }}'},
            dataType: 'json',
            success: function(json) {
                if (json['html']) {
                    $('#documents-list').html(json['html']);
                }
            }
        });
    }

    // Delete Document
    $(document).on('click', '.btn-delete-document', function() {
        if (confirm('{{ text_confirm_delete }}')) {
            var documentId = $(this).data('document-id');
            
            $.ajax({
                url: '{{ ajax_document_delete_url }}',
                type: 'POST',
                data: {document_id: documentId},
                dataType: 'json',
                success: function(json) {
                    if (json['success']) {
                        loadDocuments();
                        $('#content').prepend('<div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> ' + json['success'] + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
                    }
                    
                    if (json['error']) {
                        alert(json['error']);
                    }
                }
            });
        }
    });
});
</script>

{{ footer }}