@font-face {
  font-family: 'Readex Pro';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url(fonts/SLXYc1bJ7HE5YDoGPuzj_dh8uc7wUy8ZQQyX2Iw1ZEzMhQ.woff2) format('woff2');
  unicode-range: U+0600-06FF, U+200C-200E, U+2010-2011, U+204F, U+2E41, U+FB50-FDFF, U+FE80-FEFC;
}
@font-face {
  font-family: 'Readex Pro';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url(fonts/SLXYc1bJ7HE5YDoGPuzj_dh8uc7wUy8ZQQyX2Iw-ZEzMhQ.woff2) format('woff2');
  unicode-range: U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
@font-face {
  font-family: 'Readex Pro';
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url(fonts/SLXYc1bJ7HE5YDoGPuzj_dh8uc7wUy8ZQQyX2IwwZEw.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
/* arabic */
@font-face {
  font-family: 'Readex Pro';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(fonts/SLXYc1bJ7HE5YDoGPuzj_dh8uc7wUy8ZQQyX2Iw1ZEzMhQ.woff2) format('woff2');
  unicode-range: U+0600-06FF, U+200C-200E, U+2010-2011, U+204F, U+2E41, U+FB50-FDFF, U+FE80-FEFC;
}
@font-face {
  font-family: 'Readex Pro';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(fonts/SLXYc1bJ7HE5YDoGPuzj_dh8uc7wUy8ZQQyX2Iw-ZEzMhQ.woff2) format('woff2');
  unicode-range: U+0100-024F, U+0259, U+1E00-1EFF, U+2020, U+20A0-20AB, U+20AD-20CF, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
@font-face {
  font-family: 'Readex Pro';
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url(fonts/SLXYc1bJ7HE5YDoGPuzj_dh8uc7wUy8ZQQyX2IwwZEw.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
body {
  font-family: "Readex Pro", sans-serif;
  font-weight: 400;
  color: #666;
  font-size: 12px;
  line-height: 20px;
  width: 100%;
}
h1, h2, h3, h4, h5, h6 {
  color: #444;
}
/* Override the bootstrap defaults */
h1 {
  font-size: 33px;
}
h2 {
  font-size: 27px;
}
h3 {
  font-size: 21px;
}
h4 {
  font-size: 15px;
}
footer h5 {
  font-size: 12px;
}
h6 {
  font-size: 10.2px;
}
a {
  color: #23a1d1;
  text-decoration: none;
}
a:hover {
  text-decoration: none;
}
/* Chrome border line */
button:focus {
  outline: none !important;
}
legend {
  font-size: 18px;
  padding: 7px 0;
  margin-bottom: 20px;
  border-bottom: 1px solid #e5e5e5;
}
label {
  font-size: 12px;
  font-weight: normal;
}
.list-unstyled a {
  text-decoration: none;
}
.nav-tabs {
  margin-bottom: 15px;
}
div.required .col-form-label:before, div.required .form-label:before {
  content: "* ";
  color: #F00;
  font-weight: bold;
}
.form-switch-lg {
  font-size: 20px;
  min-height: 30px;
  line-height: 30px;
}
@media (min-width: 768px) {
  .col-form-label {
    text-align: right;
  }
}
#alert {
  z-index: 9999;
  pointer-events: all;
}
#alert .alert {
  min-width: 400px;
  position: relative;
  margin-bottom: 15px;
}
@media (min-width: 1300px) {
  #alert .alert {
    right: 50px;
  }
}
@media (min-width: 1400px) {
  #alert .alert {
    right: 0px;
  }
}
@media (min-width: 1600px) {
  #alert .alert {
    right: 100px;
  }
}
@media (min-width: 1800px) {
  #alert .alert {
    right: 200px;
  }
}
@media (min-width: 2000px) {
  #alert .alert {
    right: 300px;
  }
}
@media (min-width: 2200px) {
  #alert .alert {
    right: 400px;
  }
}
@media (min-width: 2400px) {
  #alert .alert {
    right: 500px;
  }
}
@media (min-width: 2600px) {
  #alert .alert {
    right: 600px;
  }
}
@media (min-width: 2800px) {
  #alert .alert {
    right: 700px;
  }
}
@media (min-width: 3000px) {
  #alert .alert {
    right: 800px;
  }
}
@media (min-width: 3200px) {
  #alert .alert {
    right: 900px;
  }
}
@media (min-width: 3400px) {
  #alert .alert {
    right: 1000px;
  }
}
@media (min-width: 3600px) {
  #alert .alert {
    right: 1100px;
  }
}
@media (min-width: 3800px) {
  #alert .alert {
    right: 1200px;
  }
}
@media (min-width: 4000px) {
  #alert .alert {
    right: 1300px;
  }
}
/* top */
#top {
  background-color: #EEEEEE;
  border-bottom: 1px solid #e2e2e2;
  padding: 10px 0;
  margin: 0 0 20px 0;
  min-height: 44px;
  position: relative;
}
#top .nav > .list-inline > .list-inline-item, #top .nav > .list-inline .list-inline-item > a, #top .nav > .list-inline .list-inline-item .dropdown > a {
  color: #888;
  text-shadow: 0 1px 0 #FFF;
}
#top .btn-link {
  color: #888;
  text-shadow: 0 1px 0 #FFF;
  text-decoration: none;
}
#top .btn-link:hover {
  color: #444;
}
#top a {
  font-size: 1.1em;
  text-decoration: none;
}
footer a {
  font-size: 1.1em;
  text-decoration: none;
}
/* logo */
#logo {
  margin: 0 0 10px 0;
}
#logo img {
  max-width: 200px;
}
/* search */
#search {
  margin-bottom: 10px;
}
#search .form-control-lg {
  height: 40px;
  font-size: 12px;
  line-height: 20px;
  padding: 0 10px;
}
#search .btn-lg {
  font-size: 15px;
  line-height: 18px;
  padding: 0.57rem 35px;
  text-shadow: 0 1px 0 #FFF;
}
/* cart */
#header-cart {
  margin-bottom: 10px;
}
#header-cart .btn-lg {
  color: #FFF;
  height: 40px;
  padding: 0 1rem;
}
#header-cart .btn:hover {
  color: #FFF;
}
#header-cart.open > .btn {
  background-image: none;
  background-color: #FFFFFF;
  border: 1px solid #E6E6E6;
  color: #666;
  box-shadow: none;
  text-shadow: none;
}
#header-cart.open > .btn:hover {
  color: #444;
}
#header-cart .dropdown-menu {
  background: #eee;
  z-index: 1001;
  min-width: 100%;
}
#header-cart .dropdown-menu table {
  margin-bottom: 10px;
}
#header-cart .dropdown-menu li {
  min-width: 427px;
  padding: 0 10px;
}
#header-cart .dropdown-menu li p {
  margin: 20px 0;
}
@media (max-width: 478px) {
  #header-cart .dropdown-menu {
    width: 100%;
  }
  #header-cart .dropdown-menu li > div {
    min-width: 100%;
  }
}
#header-cart .table-striped > tbody > tr:nth-of-type(odd) {
  background-color: #f9f9f9;
}
/* menu */
#menu {
  background-color: #229ac8;
  background-image: linear-gradient(to bottom, #23a1d1, #1f90bb);
  background-repeat: repeat-x;
  border: 1px solid #1f90bb;
  border-color: #1f90bb #1f90bb #145e7a;
  min-height: 40px;
  border-radius: 4px;
}
#menu.navbar {
  padding: 0 1rem;
  margin-bottom: 20px;
}
#menu .dropdown-menu {
  padding-bottom: 0;
  margin-top: 0;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}
#menu .dropdown-inner {
  display: table;
}
#menu .dropdown-inner ul {
  display: table-cell;
}
#menu .dropdown-inner ul li a:hover {
  color: #ffffff;
  background-image: linear-gradient(to bottom, #23a1d1, #1f90bb);
}
#menu .dropdown-inner a {
  min-width: 160px;
  display: block;
  padding: 3px 20px;
  clear: both;
  line-height: 20px;
  color: #333333;
  font-size: 12px;
}
#menu .see-all {
  display: block;
  margin-top: 0.5em;
  border-top: 1px solid #DDD;
  padding: 3px 20px;
  -webkit-border-radius: 0 0 4px 4px;
  -moz-border-radius: 0 0 4px 4px;
  border-radius: 0 0 3px 3px;
  font-size: 12px;
}
#menu .see-all:hover, #menu .see-all:focus {
  text-decoration: none;
  color: #ffffff;
  background-color: #229ac8;
  background-image: linear-gradient(to bottom, #23a1d1, #1f90bb);
  background-repeat: repeat-x;
}
#menu #category {
  float: left;
  font-size: 16px;
  font-weight: 700;
  line-height: 40px;
  color: #fff;
  text-shadow: 0 1px 0 rgba(0, 0, 0, 0.2);
}
#menu .navbar-toggler i {
  color: #fff;
  border-color: #fff;
  font-size: 0.9em;
}
#menu .navbar-nav > li > a {
  color: #fff;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
  padding: 10px 15px 10px 15px;
  min-height: 15px;
  background-color: transparent;
}
#menu .navbar-nav > li > a:hover {
  background-color: rgba(0, 0, 0, 0.1);
}
@media (min-width: 768px) {
  #menu .dropdown:hover .dropdown-menu {
    display: block;
  }
}
@media (max-width: 767px) {
  #menu {
    border-radius: 4px;
  }
  #menu div.dropdown-inner > ul .list-unstyled {
    display: block;
  }
  #menu div.dropdown-menu {
    margin-left: 0 !important;
    padding-bottom: 10px;
    background-color: rgba(0, 0, 0, 0.1);
  }
  #menu .dropdown-inner {
    display: block;
  }
  #menu .dropdown-inner a {
    width: 100%;
    color: #fff;
  }
  #menu .dropdown-menu a:hover {
    background: rgba(0, 0, 0, 0.1);
  }
  #menu .dropdown-menu ul li a :hover {
    background: rgba(0, 0, 0, 0.1);
  }
  #menu .see-all {
    margin-top: 0;
    border: none;
    border-radius: 0;
    color: #fff;
  }
}
/* content */
#content {
  min-height: 600px;
}
/* footer */
footer {
  margin-top: 30px;
  padding-top: 30px;
  padding-bottom: 1px;
  background-color: #303030;
  border-top: 1px solid #ddd;
  color: #e2e2e2;
}
footer hr {
  border-top: none;
  border-bottom: 1px solid #666;
}
footer a {
  color: #ccc;
}
footer a:hover {
  color: #fff;
}
footer h5 {
  font-family: "Readex Pro", sans-serif;
  font-size: 13px;
  font-weight: bold;
  color: #fff;
  margin-top: 10px;
  margin-bottom: 10px;
}
/* breadcrumb */
.breadcrumb {
  margin: 0 0 20px 0;
  padding: 8px 0;
  border: 1px solid #ddd;
  background-color: #f5f5f5;
}
.breadcrumb i {
  font-size: 15px;
}
.breadcrumb > li.breadcrumb-item {
  text-shadow: 0 1px 0 #FFF;
  padding: 0 20px;
  position: relative;
  white-space: nowrap;
}
.breadcrumb > li.breadcrumb-item > a {
  text-decoration: none;
}
.breadcrumb > li.breadcrumb-item:after {
  content: "";
  display: block;
  position: absolute;
  top: -3px;
  right: -5px;
  width: 26px;
  height: 26px;
  border-right: 1px solid #DDD;
  border-bottom: 1px solid #DDD;
  -webkit-transform: rotate(-45deg);
  -moz-transform: rotate(-45deg);
  -o-transform: rotate(-45deg);
  transform: rotate(-45deg);
}
.breadcrumb > li.breadcrumb-item + li:before {
  content: "";
  padding: 0;
}
.pagination {
  margin: 0;
}
/* buttons */
.btn-light {
  color: #777;
  text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);
  background-image: linear-gradient(to bottom, #eeeeee, #dddddd);
  background-repeat: repeat-x;
  border-color: #dddddd #dddddd #b3b3b3 #b7b7b7;
}
.btn-light:hover, .btn-light:focus, .btn-light:active, .btn-light.active, .btn-light.disabled, .btn-light[disabled] {
  color: #333;
  background-color: #e6e6e6;
  border-color: #adadad;
  background-position: 0;
}
.btn-primary {
  color: #ffffff;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
  background-image: linear-gradient(to bottom, #23a1d1, #1f90bb);
  background-repeat: repeat-x;
  border-color: #1f90bb #1f90bb #145e7a;
}
.btn-primary:hover, .btn-primary:focus, .btn-primary:active, .btn-primary.active, .btn-primary.disabled, .btn-primary[disabled] {
  background-position: 0;
}
.btn-warning {
  color: #ffffff;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
  background-image: linear-gradient(to bottom, #fbb450, #f89406);
  background-repeat: repeat-x;
  border-color: #f89406 #f89406 #ad6704;
}
.btn-warning:hover, .btn-warning:focus, .btn-warning:active, .btn-warning.active, .btn-warning.disabled, .btn-warning[disabled] {
  box-shadow: inset 0 1000px 0 rgba(0, 0, 0, 0.1);
}
.btn-danger {
  color: #ffffff;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
  background-image: linear-gradient(to bottom, #ee5f5b, #bd362f);
  background-repeat: repeat-x;
  border-color: #bd362f #bd362f #802420;
}
.btn-danger:hover, .btn-danger:focus, .btn-danger:active, .btn-danger.active, .btn-danger.disabled, .btn-danger[disabled] {
  box-shadow: inset 0 1000px 0 rgba(0, 0, 0, 0.1);
}
.btn-success {
  color: #ffffff;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
  background-image: linear-gradient(to bottom, #62c462, #51a351);
  background-repeat: repeat-x;
  border-color: #51a351 #51a351 #387038;
}
.btn-success:hover, .btn-success:focus, .btn-success:active, .btn-success.active, .btn-success.disabled, .btn-success[disabled] {
  box-shadow: inset 0 1000px 0 rgba(0, 0, 0, 0.1);
}
.btn-info {
  color: #ffffff;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
  background-image: linear-gradient(to bottom, #e06342, #dc512c);
  background-repeat: repeat-x;
  border-color: #dc512c #dc512c #a2371a;
}
.btn-info:hover, .btn-info:focus, .btn-info:active, .btn-info.active, .btn-info.disabled, .btn-info[disabled] {
  background-image: none;
  background-color: #df5c39;
}
.btn-link {
  border-color: rgba(0, 0, 0, 0);
  cursor: pointer;
  color: #23A1D1;
  border-radius: 0;
}
.btn-link, .btn-link:active, .btn-link[disabled] {
  background-color: rgba(0, 0, 0, 0);
  background-image: none;
  box-shadow: none;
}
.btn-inverse {
  color: #ffffff;
  text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
  background-color: #363636;
  background-image: linear-gradient(to bottom, #444444, #222222);
  background-repeat: repeat-x;
  border-color: #222222 #222222 #000000;
}
.btn-inverse:hover, .btn-inverse:focus, .btn-inverse:active, .btn-inverse.active, .btn-inverse.disabled, .btn-inverse[disabled] {
  background-color: #222222;
  background-image: linear-gradient(to bottom, #333333, #111111);
}
.product-thumb {
  border: 1px solid #ddd;
  margin-bottom: 15px;
}
.product-thumb h4 {
  font-weight: bold;
}
.product-thumb .image {
  text-align: center;
  margin-bottom: 15px;
}
.product-thumb .image a:hover {
  opacity: 0.8;
}
.product-thumb .description {
  padding: 15px;
}
.product-thumb .button-group {
  display: flex;
  border-top: 1px solid #ddd;
  background-color: #eee;
}
.product-thumb .button-group button {
  flex: 33%;
  border-radius: 0;
  display: inline-block;
  border: none;
  background-color: #eee;
  color: #888;
  line-height: 38px;
  font-weight: bold;
  text-align: center;
  text-transform: uppercase;
}
.product-thumb .button-group button:hover {
  color: #444;
  background-color: #ddd;
  text-decoration: none;
  cursor: pointer;
}
.product-thumb .button-group button + button {
  border-left: 1px solid #ddd;
}
@media (min-width: 960px) {
  .product-list .product-thumb {
    display: flex;
  }
  .product-list .product-thumb .image {
    flex-direction: column;
    margin-bottom: 0px;
  }
  .product-list .product-thumb .content {
    flex-direction: column;
    flex: 75%;
    position: relative;
  }
  .product-list .product-thumb .button-group {
    position: absolute;
    bottom: 0px;
    width: 100%;
    border-left: 1px solid #ddd;
  }
}
.rating {
  padding-bottom: 10px;
}
.rating .fa-stack {
  width: 20px;
}
.rating.fa-star {
  color: #999;
  font-size: 15px;
}
.rating .fa-star {
  color: #FC0;
  font-size: 15px;
}
.rating .fa-star + .fa-star {
  color: #E69500;
}
/* product list */
.price {
  color: #444;
}
.price-new {
  font-weight: 600;
}
.price-old {
  color: #dc512c;
  text-decoration: line-through;
}
.price-tax {
  color: #999;
  font-size: 12px;
  display: block;
}
/* BS4 Changes */
.navbar-light .navbar-toggler {
  font-size: 15px;
  font-stretch: expanded;
  color: #FFF;
  padding: 6px 12px;
  background-color: #229ac8;
  background-image: linear-gradient(to bottom, #23a1d1, #1f90bb);
  background-repeat: repeat-x;
  border-color: #1f90bb #1f90bb #145e7a;
}
.form-check .form-check-input {
  margin-top: 0.25rem;
}
/* Theme Custom CSS */
#display-control #compare-total.a {
  margin-top: -2px;
  padding: 0.35rem 0.565rem;
}
#product-product h1 {
  margin-top: 20px;
  margin-bottom: 10px;
}
#information-contact .card {
  margin-bottom: 20px;
}
#cookie {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 150px;
  z-index: 9999;
  opacity: 0.95;
  color: #ecf0f1;
  background: #343a40;
}
#cookie div {
  font-size: 16px;
  color: #FFFFFF;
}
