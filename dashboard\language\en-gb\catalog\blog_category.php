<?php
// Heading
$_['heading_title']          = 'Blog Categories';

// Text
$_['text_success_add']       = 'Category added successfully!';
$_['text_success_edit']      = 'Category updated successfully!';
$_['text_success_delete']    = 'Category/Categories deleted successfully!';
$_['text_list']              = 'Blog Category List';
$_['text_add']               = 'Add New Category';
$_['text_edit']              = 'Edit Category';
$_['text_default']           = 'Default';
$_['text_enabled']           = 'Enabled';
$_['text_disabled']          = 'Disabled';
$_['text_all_statuses']      = '-- All Statuses --';
$_['text_no_results']        = 'No results';
$_['text_form']              = 'Category Form';
$_['text_filter']            = 'Filter';
$_['text_none']              = '-- None --';
$_['text_select']            = '-- Select --';
$_['text_parent_category']   = 'Parent Category';

// Column
$_['column_name']            = 'Category Name';
$_['column_parent']          = 'Parent Category';
$_['column_sort_order']      = 'Sort Order';
$_['column_status']          = 'Status';
$_['column_posts']           = 'Number of Posts';
$_['column_action']          = 'Action';

// Entry
$_['entry_name']             = 'Category Name';
$_['entry_slug']             = 'Slug';
$_['entry_description']      = 'Description';
$_['entry_meta_title']       = 'Meta Title';
$_['entry_meta_description'] = 'Meta Description';
$_['entry_meta_keywords']    = 'Meta Keywords';
$_['entry_status']           = 'Status';
$_['entry_sort_order']       = 'Sort Order';
$_['entry_image']            = 'Image';
$_['entry_parent']           = 'Parent Category';
$_['entry_filter']           = 'Filter by Name';

// Help
$_['help_slug']              = 'The slug is the URL-friendly version of the category name. It must be unique.';
$_['help_parent']            = 'Choose a parent category to create a hierarchical structure.';

// Tab
$_['tab_general']            = 'General';
$_['tab_data']               = 'Data';
$_['tab_seo']                = 'SEO';
$_['tab_design']             = 'Design';
$_['tab_posts']              = 'Posts';

// Button
$_['button_save']            = 'Save';
$_['button_cancel']          = 'Cancel';
$_['button_add']             = 'Add New';
$_['button_edit']            = 'Edit';
$_['button_delete']          = 'Delete';
$_['button_filter']          = 'Filter';
$_['button_clear']           = 'Clear';
$_['button_generate']        = 'Generate';
$_['button_back_to_blog']    = 'Back to Blog';

// Error
$_['error_permission']       = 'Warning: You do not have permission to modify blog categories!';
$_['error_name']             = 'Category Name must be between 1 and 255 characters!';
$_['error_parent_self']      = 'Cannot select the category itself as a parent!';
$_['error_slug']             = 'Slug is required!';
$_['error_slug_exists']      = 'This slug already exists!';
$_['error_posts']            = 'Cannot delete this category as it contains %s post(s)!';
$_['error_ajax']             = 'Error in operation! Please try again.';