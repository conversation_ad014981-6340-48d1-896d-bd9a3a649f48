<?php
class ModelAccountsTrialBalance extends Model {
    public function getMinAccountCode() {
        $query = $this->db->query("SELECT MIN(account_code) AS min_code FROM " . DB_PREFIX . "accounts");
        return $query->row['min_code'];
    }

    public function getMaxAccountCode() {
        $query = $this->db->query("SELECT MAX(account_code) AS max_code FROM " . DB_PREFIX . "accounts");
        return $query->row['max_code'];
    }

    public function getAccountRangeData($date_start, $date_end, $account_start, $account_end) {
        $language_id = (int)$this->config->get('config_language_id');
        $currency_code = $this->config->get('config_currency');

        // استخدام الجداول الجديدة المحسنة
        $sql = "SELECT a.account_code, a.parent_id, a.account_type, a.account_nature,
                       a.opening_balance, a.current_balance, ad.name,
                   COALESCE(SUM(CASE WHEN je.journal_date < '" . $this->db->escape($date_start) . "' AND je.status = 'posted'
                                     THEN (jel.debit_amount - jel.credit_amount) ELSE 0 END), 0) AS opening_balance_calculated,
                   COALESCE(SUM(CASE WHEN j.thedate BETWEEN '" . $this->db->escape($date_start) . "' AND '" . $this->db->escape($date_end) . "' AND j.status = 'posted'
                                     THEN (CASE WHEN je.is_debit = 1 THEN je.amount ELSE -je.amount END) ELSE 0 END), 0) AS period_movement
                FROM `" . DB_PREFIX . "accounts` a
                LEFT JOIN `" . DB_PREFIX . "account_description` ad ON (a.account_id = ad.account_id AND ad.language_id = '" . (int)$language_id . "')
                LEFT JOIN `" . DB_PREFIX . "journal_entries` je ON (je.account_code = a.account_code)
                LEFT JOIN `" . DB_PREFIX . "journals` j ON (je.journal_id = j.journal_id)
                WHERE a.account_code BETWEEN '" . (int)$account_start . "' AND '" . (int)$account_end . "'
                AND a.is_active = 1
                GROUP BY a.account_id, a.account_code, a.parent_id, a.account_type, a.account_nature, ad.name
                ORDER BY a.account_code ASC";

        $query = $this->db->query($sql);
        $accounts = $query->rows;

        $accountsHierarchy = [];
        $rootAccounts = [];

        // فهرسة بالحساب code
        foreach ($accounts as $acc) {
            $acc['children'] = [];
            $accountsHierarchy[$acc['account_code']] = $acc;
        }

        // بناء الشجرة
        foreach ($accounts as $acc) {
            $code = $acc['account_code'];
            $pcode = $acc['parent_id'];
            if ($pcode == 0) {
                // حساب جذري
                $rootAccounts[] = &$accountsHierarchy[$code];
            } else {
                // حساب فرعي
                if (isset($accountsHierarchy[$pcode])) {
                    $accountsHierarchy[$pcode]['children'][] = &$accountsHierarchy[$code];
                }
            }
        }

        // تجميع الأرصدة
        $this->aggregateBalances($rootAccounts);

        // حساب الرصيد الختامي بناءً على account_type
        $this->finalizeClosingBalance($rootAccounts);

        // تنسيق البيانات
        $formattedAccounts = [];
        $sums = [
            'opening_balance_debit' => 0, 'opening_balance_credit' => 0,
            'total_debit' => 0, 'total_credit' => 0,
            'closing_balance_debit' => 0, 'closing_balance_credit' => 0
        ];

        foreach ($accountsHierarchy as $acc) {
            $formatted = $this->formatAccountData($acc, $currency_code);
            $formattedAccounts[] = $formatted;
            if ($acc['parent_id'] == 0) {
                $this->updateSums($acc, $sums);
            }
        }

        foreach ($sums as $key => $value) {
            $sums[$key . '_formatted'] = $this->currency->format((float)$value, $currency_code);
        }

        return [
            'accounts' => $formattedAccounts,
            'sums' => $sums
        ];
    }

    private function aggregateBalances(&$accounts) {
        foreach ($accounts as &$acc) {
            if (!empty($acc['children'])) {
                $this->aggregateBalances($acc['children']);
                foreach ($acc['children'] as $child) {
                    $acc['opening_balance'] += $child['opening_balance'];
                    $acc['period_movement'] += $child['period_movement'];
                }
            }
        }
    }

    private function finalizeClosingBalance(&$accounts) {
        foreach ($accounts as &$acc) {
            // استخدام طبيعة الحساب الصحيحة من الجدول الجديد
            if ($acc['account_nature'] == 'debit') {
                // الحسابات المدينة: الأصول والمصروفات
                $acc['closing_balance'] = $acc['opening_balance_calculated'] + $acc['period_movement'];
            } else {
                // الحسابات الدائنة: الخصوم وحقوق الملكية والإيرادات
                $acc['closing_balance'] = $acc['opening_balance_calculated'] + $acc['period_movement'];
            }

            if (!empty($acc['children'])) {
                $this->finalizeClosingBalance($acc['children']);
            }
        }
    }

    private function formatAccountData($acc, $currency_code) {
        $ob_debit = $ob_credit = $cb_debit = $cb_credit = $total_debit = $total_credit = 0.0;

        $opening_balance = (float)$acc['opening_balance_calculated'];
        $period_movement = (float)$acc['period_movement'];
        $closing_balance = (float)$acc['closing_balance'];

        // تحديد الرصيد الافتتاحي حسب طبيعة الحساب
        if ($acc['account_nature'] == 'debit') {
            // الحسابات المدينة
            if ($opening_balance >= 0) {
                $ob_debit = $opening_balance;
            } else {
                $ob_credit = abs($opening_balance);
            }

            // الحركة خلال الفترة
            if ($period_movement >= 0) {
                $total_debit = $period_movement;
            } else {
                $total_credit = abs($period_movement);
            }

            // الرصيد الختامي
            if ($closing_balance >= 0) {
                $cb_debit = $closing_balance;
            } else {
                $cb_credit = abs($closing_balance);
            }
        } else {
            // الحسابات الدائنة
            if ($opening_balance >= 0) {
                $ob_credit = $opening_balance;
            } else {
                $ob_debit = abs($opening_balance);
            }

            // الحركة خلال الفترة
            if ($period_movement >= 0) {
                $total_credit = $period_movement;
            } else {
                $total_debit = abs($period_movement);
            }

            // الرصيد الختامي
            if ($closing_balance >= 0) {
                $cb_credit = $closing_balance;
            } else {
                $cb_debit = abs($closing_balance);
            }
        }

        return [
            'account_code' => $acc['account_code'],
            'name' => $acc['name'],
            'account_type' => $acc['account_type'],
            'account_nature' => $acc['account_nature'],
            'opening_balance_debit_formatted' => $this->currency->format($ob_debit, $currency_code),
            'opening_balance_credit_formatted' => $this->currency->format($ob_credit, $currency_code),
            'total_debit_formatted' => $this->currency->format($total_debit, $currency_code),
            'total_credit_formatted' => $this->currency->format($total_credit, $currency_code),
            'closing_balance_debit_formatted' => $this->currency->format($cb_debit, $currency_code),
            'closing_balance_credit_formatted' => $this->currency->format($cb_credit, $currency_code),
            'opening_balance_debit' => $ob_debit,
            'opening_balance_credit' => $ob_credit,
            'total_debit' => $total_debit,
            'total_credit' => $total_credit,
            'closing_balance_debit' => $cb_debit,
            'closing_balance_credit' => $cb_credit,
            'closing_balance' => $closing_balance
        ];
    }

    private function updateSums($acc, &$sums) {
        $ob = (float)$acc['opening_balance'];
        $pm = (float)$acc['period_movement'];
        $cb = (float)$acc['closing_balance'];

        $sums['opening_balance_debit'] += $ob >= 0 ? $ob : 0;
        $sums['opening_balance_credit'] += $ob < 0 ? abs($ob) : 0;
        $sums['total_debit'] += max(0, $pm);
        $sums['total_credit'] += max(0, -$pm);
        $sums['closing_balance_debit'] += $cb >= 0 ? $cb : 0;
        $sums['closing_balance_credit'] += $cb < 0 ? abs($cb) : 0;
    }

    /**
     * تحليل ميزان المراجعة المتقدم مع فحص التوازن والانحرافات
     */
    public function getAdvancedTrialBalanceAnalysis($date_start, $date_end, $comparison_period = null) {
        $trial_balance_data = $this->getAccountRangeData($date_start, $date_end,
            $this->getMinAccountCode(), $this->getMaxAccountCode());

        $analysis = array(
            'trial_balance' => $trial_balance_data,
            'balance_verification' => $this->verifyAccountingBalance($trial_balance_data),
            'account_analysis' => $this->analyzeAccountMovements($trial_balance_data),
            'materiality_analysis' => $this->performMaterialityAnalysis($trial_balance_data),
            'variance_analysis' => $this->analyzeVariances($trial_balance_data, $date_start, $date_end),
            'summary_statistics' => $this->calculateSummaryStatistics($trial_balance_data),
            'audit_flags' => $this->identifyAuditFlags($trial_balance_data)
        );

        // إضافة المقارنة مع الفترة السابقة إذا طُلبت
        if ($comparison_period) {
            $comparison_data = $this->getAccountRangeData(
                $comparison_period['date_start'],
                $comparison_period['date_end'],
                $this->getMinAccountCode(),
                $this->getMaxAccountCode()
            );
            $analysis['comparison_period'] = $comparison_data;
            $analysis['period_comparison'] = $this->comparePeriods($trial_balance_data, $comparison_data);
        }

        return $analysis;
    }

    /**
     * فحص التوازن المحاسبي
     */
    private function verifyAccountingBalance($trial_balance_data) {
        $total_debits = 0;
        $total_credits = 0;
        $unbalanced_accounts = array();

        foreach ($trial_balance_data as $account) {
            $closing_balance = $account['opening_balance_calculated'] + $account['period_movement'];

            if ($closing_balance > 0) {
                $total_debits += $closing_balance;
            } else {
                $total_credits += abs($closing_balance);
            }

            // فحص الحسابات غير المتوازنة
            if (abs($closing_balance) < 0.01 && $account['period_movement'] != 0) {
                $unbalanced_accounts[] = array(
                    'account_code' => $account['account_code'],
                    'account_name' => $account['name'],
                    'period_movement' => $account['period_movement'],
                    'closing_balance' => $closing_balance
                );
            }
        }

        $difference = $total_debits - $total_credits;
        $is_balanced = abs($difference) < 0.01;

        return array(
            'total_debits' => round($total_debits, 2),
            'total_credits' => round($total_credits, 2),
            'difference' => round($difference, 2),
            'is_balanced' => $is_balanced,
            'balance_percentage' => $total_debits > 0 ? round(($total_credits / $total_debits) * 100, 2) : 0,
            'unbalanced_accounts' => $unbalanced_accounts,
            'balance_quality_score' => $this->calculateBalanceQualityScore($difference, count($unbalanced_accounts))
        );
    }

    /**
     * تحليل حركات الحسابات
     */
    private function analyzeAccountMovements($trial_balance_data) {
        $active_accounts = 0;
        $inactive_accounts = 0;
        $high_activity_accounts = array();
        $dormant_accounts = array();
        $account_types_summary = array();

        foreach ($trial_balance_data as $account) {
            $movement = abs($account['period_movement']);
            $account_type = $account['account_type'];

            // تصنيف النشاط
            if ($movement > 0) {
                $active_accounts++;

                // الحسابات عالية النشاط (أكثر من 100,000)
                if ($movement > 100000) {
                    $high_activity_accounts[] = array(
                        'account_code' => $account['account_code'],
                        'account_name' => $account['name'],
                        'movement' => $movement,
                        'account_type' => $account_type
                    );
                }
            } else {
                $inactive_accounts++;

                // الحسابات الخاملة (لها رصيد افتتاحي لكن بدون حركة)
                if (abs($account['opening_balance_calculated']) > 0) {
                    $dormant_accounts[] = array(
                        'account_code' => $account['account_code'],
                        'account_name' => $account['name'],
                        'opening_balance' => $account['opening_balance_calculated'],
                        'account_type' => $account_type
                    );
                }
            }

            // تجميع حسب نوع الحساب
            if (!isset($account_types_summary[$account_type])) {
                $account_types_summary[$account_type] = array(
                    'count' => 0,
                    'total_movement' => 0,
                    'total_balance' => 0
                );
            }
            $account_types_summary[$account_type]['count']++;
            $account_types_summary[$account_type]['total_movement'] += $movement;
            $account_types_summary[$account_type]['total_balance'] += $account['opening_balance_calculated'] + $account['period_movement'];
        }

        // ترتيب الحسابات عالية النشاط
        usort($high_activity_accounts, function($a, $b) {
            return $b['movement'] <=> $a['movement'];
        });

        return array(
            'total_accounts' => count($trial_balance_data),
            'active_accounts' => $active_accounts,
            'inactive_accounts' => $inactive_accounts,
            'activity_ratio' => count($trial_balance_data) > 0 ? round(($active_accounts / count($trial_balance_data)) * 100, 2) : 0,
            'high_activity_accounts' => array_slice($high_activity_accounts, 0, 10),
            'dormant_accounts' => $dormant_accounts,
            'account_types_summary' => $account_types_summary
        );
    }

    /**
     * تحليل الأهمية النسبية
     */
    private function performMaterialityAnalysis($trial_balance_data) {
        // حساب إجمالي الأصول لتحديد حد الأهمية النسبية
        $total_assets = 0;
        $material_accounts = array();

        foreach ($trial_balance_data as $account) {
            if (strpos($account['account_code'], '1') === 0) { // حسابات الأصول
                $total_assets += abs($account['opening_balance_calculated'] + $account['period_movement']);
            }
        }

        // حد الأهمية النسبية (5% من إجمالي الأصول)
        $materiality_threshold = $total_assets * 0.05;

        foreach ($trial_balance_data as $account) {
            $account_balance = abs($account['opening_balance_calculated'] + $account['period_movement']);
            $movement = abs($account['period_movement']);

            if ($account_balance > $materiality_threshold || $movement > $materiality_threshold) {
                $material_accounts[] = array(
                    'account_code' => $account['account_code'],
                    'account_name' => $account['name'],
                    'balance' => $account_balance,
                    'movement' => $movement,
                    'materiality_ratio' => $total_assets > 0 ? round(($account_balance / $total_assets) * 100, 2) : 0,
                    'risk_level' => $this->assessAccountRisk($account)
                );
            }
        }

        // ترتيب حسب الأهمية النسبية
        usort($material_accounts, function($a, $b) {
            return $b['materiality_ratio'] <=> $a['materiality_ratio'];
        });

        return array(
            'total_assets' => $total_assets,
            'materiality_threshold' => $materiality_threshold,
            'material_accounts_count' => count($material_accounts),
            'material_accounts' => $material_accounts,
            'materiality_coverage' => count($trial_balance_data) > 0 ?
                round((count($material_accounts) / count($trial_balance_data)) * 100, 2) : 0
        );
    }

    // دوال مساعدة أساسية
    private function calculateBalanceQualityScore($difference, $unbalanced_count) {
        $score = 100;
        $score -= min(abs($difference) / 1000, 50);
        $score -= min($unbalanced_count * 5, 30);
        return max(round($score, 2), 0);
    }

    private function assessAccountRisk($account) {
        $balance = abs($account['opening_balance_calculated'] + $account['period_movement']);
        $movement = abs($account['period_movement']);

        if ($balance > 1000000 || $movement > 500000) return 'high';
        if ($balance > 100000 || $movement > 100000) return 'medium';
        return 'low';
    }

    private function analyzeVariances($trial_balance_data, $date_start, $date_end) {
        return array('significant_variances' => array(), 'variance_summary' => array());
    }

    private function calculateSummaryStatistics($trial_balance_data) {
        return array('total_accounts' => count($trial_balance_data));
    }

    private function identifyAuditFlags($trial_balance_data) {
        return array();
    }

    private function comparePeriods($current_data, $comparison_data) {
        return array();
    }

    // تحسين ميزان المراجعة مع التخزين المؤقت
    public function getOptimizedTrialBalance($date_start, $date_end, $account_start = null, $account_end = null) {
        $cache_key = 'trial_balance_' . md5($date_start . '_' . $date_end . '_' . $account_start . '_' . $account_end);

        $cached_result = $this->cache->get($cache_key);
        if ($cached_result !== false) {
            return $cached_result;
        }

        $result = $this->getAccountRangeData($date_start, $date_end, $account_start, $account_end);
        $this->cache->set($cache_key, $result, 1800);

        return $result;
    }

    // تحليل متقدم لميزان المراجعة
    public function getEnhancedTrialBalanceAnalysis($date_start, $date_end) {
        $cache_key = 'trial_balance_analysis_' . md5($date_start . '_' . $date_end);

        $cached_result = $this->cache->get($cache_key);
        if ($cached_result !== false) {
            return $cached_result;
        }

        $trial_balance = $this->getAccountRangeData($date_start, $date_end, $this->getMinAccountCode(), $this->getMaxAccountCode());

        $analysis = array();

        // تحليل التوازن
        $total_debits = 0;
        $total_credits = 0;
        $account_count = 0;

        foreach ($trial_balance as $account) {
            $balance = (float)$account['closing_balance'];
            if ($balance > 0) {
                $total_debits += $balance;
            } else {
                $total_credits += abs($balance);
            }
            $account_count++;
        }

        $analysis['balance_summary'] = array(
            'total_debits' => $total_debits,
            'total_credits' => $total_credits,
            'difference' => $total_debits - $total_credits,
            'is_balanced' => abs($total_debits - $total_credits) < 0.01,
            'account_count' => $account_count
        );

        // تحليل الحسابات حسب النوع
        $account_types = array();
        foreach ($trial_balance as $account) {
            $type = $account['account_type'];
            if (!isset($account_types[$type])) {
                $account_types[$type] = array(
                    'count' => 0,
                    'total_balance' => 0
                );
            }
            $account_types[$type]['count']++;
            $account_types[$type]['total_balance'] += (float)$account['closing_balance'];
        }

        $analysis['account_types'] = $account_types;
        $analysis['trial_balance'] = $trial_balance;

        $this->cache->set($cache_key, $analysis, 2400);

        return $analysis;
    }

    // التحقق من صحة البيانات
    private function validateTrialBalanceData($date_start, $date_end) {
        $errors = array();

        if (empty($date_start) || !$this->validateDate($date_start)) {
            $errors[] = 'Invalid start date';
        }

        if (empty($date_end) || !$this->validateDate($date_end)) {
            $errors[] = 'Invalid end date';
        }

        return $errors;
    }

    private function validateDate($date) {
        if (empty($date)) return false;

        $d = DateTime::createFromFormat('Y-m-d', $date);
        return $d && $d->format('Y-m-d') === $date;
    }
}
