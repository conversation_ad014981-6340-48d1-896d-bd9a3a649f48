{{ header }}
{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <a href="{{ reanalyze }}" data-toggle="tooltip" title="{{ button_reanalyze }}" class="btn btn-warning"><i class="fa fa-refresh"></i></a>
        <a href="{{ back }}" data-toggle="tooltip" title="{{ button_back }}" class="btn btn-default"><i class="fa fa-reply"></i></a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-search"></i> {{ text_view }}</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-md-6">
            <div class="form-group">
              <label class="control-label">{{ entry_page_url }}</label>
              <div class="input-group">
                <span class="input-group-addon"><i class="fa fa-link"></i></span>
                <input type="text" value="{{ page_url }}" class="form-control" readonly />
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-group">
              <label class="control-label">{{ entry_target_keyword }}</label>
              <div class="input-group">
                <span class="input-group-addon"><i class="fa fa-key"></i></span>
                <input type="text" value="{{ target_keyword }}" class="form-control" readonly />
              </div>
            </div>
          </div>
        </div>
        
        <div class="row">
          <div class="col-md-12">
            <div class="well">
              <h4>{{ entry_overall_score }}</h4>
              <div class="progress">
                <div class="progress-bar progress-bar-{{ overall_score_class }}" role="progressbar" aria-valuenow="{{ overall_score }}" aria-valuemin="0" aria-valuemax="100" style="width: {{ overall_score }}%;">
                  {{ overall_score }}%
                </div>
              </div>
              <div class="text-center">
                <strong>{{ text_analysis_date }}: {{ date_analysis }}</strong>
              </div>
            </div>
          </div>
        </div>
        
        <div class="row">
          <!-- Puntuaciones detalladas -->
          <div class="col-md-6">
            <div class="panel panel-default">
              <div class="panel-heading">
                <h3 class="panel-title">{{ text_detailed_scores }}</h3>
              </div>
              <div class="panel-body">
                <div class="form-group">
                  <label class="control-label">{{ entry_title_score }}</label>
                  <div class="progress">
                    <div class="progress-bar progress-bar-{{ title_score_class }}" role="progressbar" aria-valuenow="{{ title_score }}" aria-valuemin="0" aria-valuemax="100" style="width: {{ title_score }}%;">
                      {{ title_score }}%
                    </div>
                  </div>
                </div>
                
                <div class="form-group">
                  <label class="control-label">{{ entry_meta_score }}</label>
                  <div class="progress">
                    <div class="progress-bar progress-bar-{{ meta_score_class }}" role="progressbar" aria-valuenow="{{ meta_score }}" aria-valuemin="0" aria-valuemax="100" style="width: {{ meta_score }}%;">
                      {{ meta_score }}%
                    </div>
                  </div>
                </div>
                
                <div class="form-group">
                  <label class="control-label">{{ entry_content_score }}</label>
                  <div class="progress">
                    <div class="progress-bar progress-bar-{{ content_score_class }}" role="progressbar" aria-valuenow="{{ content_score }}" aria-valuemin="0" aria-valuemax="100" style="width: {{ content_score }}%;">
                      {{ content_score }}%
                    </div>
                  </div>
                </div>
                
                <div class="form-group">
                  <label class="control-label">{{ entry_technical_score }}</label>
                  <div class="progress">
                    <div class="progress-bar progress-bar-{{ technical_score_class }}" role="progressbar" aria-valuenow="{{ technical_score }}" aria-valuemin="0" aria-valuemax="100" style="width: {{ technical_score }}%;">
                      {{ technical_score }}%
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Sugerencias -->
          <div class="col-md-6">
            <div class="panel panel-default">
              <div class="panel-heading">
                <h3 class="panel-title">{{ entry_suggestions }}</h3>
              </div>
              <div class="panel-body">
                <div class="seo-suggestions-container">
                  {% if suggestions %}
                    {% set suggestions_list = suggestions|split('\n') %}
                    <ul class="seo-suggestions-list">
                      {% for suggestion in suggestions_list %}
                        <li><i class="fa fa-check-circle text-info"></i> {{ suggestion }}</li>
                      {% endfor %}
                    </ul>
                  {% else %}
                    <div class="alert alert-info">{{ text_no_suggestions }}</div>
                  {% endif %}
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Enlaces internos relacionados -->
        <div class="row">
          <div class="col-md-12">
            <div class="panel panel-default">
              <div class="panel-heading">
                <h3 class="panel-title">{{ text_related_internal_links }}</h3>
              </div>
              <div class="panel-body">
                {% if internal_links %}
                  <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                      <thead>
                        <tr>
                          <th>{{ column_source_page }}</th>
                          <th>{{ column_target_page }}</th>
                          <th>{{ column_anchor_text }}</th>
                          <th>{{ column_status }}</th>
                        </tr>
                      </thead>
                      <tbody>
                        {% for link in internal_links %}
                          <tr>
                            <td>{{ link.source_page }}</td>
                            <td>{{ link.target_page }}</td>
                            <td>{{ link.anchor_text }}</td>
                            <td>{{ link.status ? text_enabled : text_disabled }}</td>
                          </tr>
                        {% endfor %}
                      </tbody>
                    </table>
                  </div>
                {% else %}
                  <div class="alert alert-info">
                    <i class="fa fa-info-circle"></i> {{ text_no_internal_links }}
                    <a href="{{ internal_links_url }}" class="alert-link">{{ text_add_internal_link }}</a>
                  </div>
                {% endif %}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<style>
/* Estilos generales para el módulo SEO */
.seo-navigation {
    margin-bottom: 30px;
}

.seo-navigation .panel-body {
    padding: 15px;
    transition: all 0.3s ease;
}

.seo-navigation .panel-body:hover {
    background-color: #f8f8f8;
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
}

.seo-navigation .btn-link {
    color: #333;
    text-decoration: none;
    padding: 15px 0;
}

.seo-navigation i.fa {
    margin-bottom: 10px;
    color: #23a1d1;
}

/* Tarjetas de estadísticas */
.panel-primary, .panel-green, .panel-red, .panel-yellow {
    border-color: #ddd;
}

.panel-primary .panel-heading {
    background-color: #337ab7;
    border-color: #337ab7;
    color: #fff;
}

.panel-green .panel-heading {
    background-color: #5cb85c;
    border-color: #5cb85c;
    color: #fff;
}

.panel-red .panel-heading {
    background-color: #d9534f;
    border-color: #d9534f;
    color: #fff;
}

.panel-yellow .panel-heading {
    background-color: #f0ad4e;
    border-color: #f0ad4e;
    color: #fff;
}

.panel-footer {
    color: #333;
}

.panel .huge {
    font-size: 40px;
    line-height: 1;
    margin-bottom: 5px;
}

/* Barras de progreso */
.progress {
    margin-bottom: 10px;
    height: 20px;
}

.progress-bar {
    line-height: 20px;
    font-size: 12px;
}

.progress-bar-success {
    background-color: #5cb85c;
}

.progress-bar-warning {
    background-color: #f0ad4e;
}

.progress-bar-danger {
    background-color: #d9534f;
}

/* Lista de sugerencias */
.seo-suggestions-list {
    padding-left: 15px;
    list-style-type: none;
}

.seo-suggestions-list li {
    padding: 5px 0;
    border-bottom: 1px dotted #eee;
}

.seo-suggestions-list i {
    margin-right: 8px;
}

/* Estilos para estados */
.label-improved {
    background-color: #5cb85c;
}

.label-declined {
    background-color: #d9534f;
}

.label-unchanged {
    background-color: #5bc0de;
}

.label-new {
    background-color: #f0ad4e;
}

/* Indicadores de cambio en posiciones */
.text-success .fa, 
.text-danger .fa {
    margin-right: 3px;
}

/* Formularios de análisis */
.analysis-results-container {
    margin-top: 20px;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #f9f9f9;
}

/* Responsive */
@media (max-width: 768px) {
    .panel .huge {
        font-size: 30px;
    }
    
    .form-group [class*='col-xs-'] {
        margin-bottom: 15px;
    }
}

/* Animaciones */
.fade-in {
    animation: fadeIn ease 0.5s;
    -webkit-animation: fadeIn ease 0.5s;
    -moz-animation: fadeIn ease 0.5s;
    -o-animation: fadeIn ease 0.5s;
    -ms-animation: fadeIn ease 0.5s;
}

@keyframes fadeIn {
    0% {opacity:0;}
    100% {opacity:1;}
}

@-moz-keyframes fadeIn {
    0% {opacity:0;}
    100% {opacity:1;}
}

@-webkit-keyframes fadeIn {
    0% {opacity:0;}
    100% {opacity:1;}
}

@-o-keyframes fadeIn {
    0% {opacity:0;}
    100% {opacity:1;}
}

@-ms-keyframes fadeIn {
    0% {opacity:0;}
    100% {opacity:1;}
}    
</style>
{{ footer }}