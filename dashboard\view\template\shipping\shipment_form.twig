{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="submit" form="shipping\shipment-form" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fas fa-save"></i></button>
        <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-light"><i class="fas fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    
    <div class="card">
      <div class="card-header"><i class="fas fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}chart-bar{% elif screen_type == 'settings' %}cog{% else %}pencil-alt{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="card-body">
        <form id="shipping\shipment-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-add">{{ text_add }}</label>
            <div class="col-sm-10">
              <input type="text" name="add" value="{{ add }}" placeholder="{{ text_add }}" id="input-add" class="form-control" />
              {% if error_add %}
                <div class="invalid-feedback">{{ error_add }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-ajax_shipments_url">{{ text_ajax_shipments_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="ajax_shipments_url" value="{{ ajax_shipments_url }}" placeholder="{{ text_ajax_shipments_url }}" id="input-ajax_shipments_url" class="form-control" />
              {% if error_ajax_shipments_url %}
                <div class="invalid-feedback">{{ error_ajax_shipments_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-ajax_track_url">{{ text_ajax_track_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="ajax_track_url" value="{{ ajax_track_url }}" placeholder="{{ text_ajax_track_url }}" id="input-ajax_track_url" class="form-control" />
              {% if error_ajax_track_url %}
                <div class="invalid-feedback">{{ error_ajax_track_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-ajax_update_status_url">{{ text_ajax_update_status_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="ajax_update_status_url" value="{{ ajax_update_status_url }}" placeholder="{{ text_ajax_update_status_url }}" id="input-ajax_update_status_url" class="form-control" />
              {% if error_ajax_update_status_url %}
                <div class="invalid-feedback">{{ error_ajax_update_status_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-button_add">{{ text_button_add }}</label>
            <div class="col-sm-10">
              <input type="text" name="button_add" value="{{ button_add }}" placeholder="{{ text_button_add }}" id="input-button_add" class="form-control" />
              {% if error_button_add %}
                <div class="invalid-feedback">{{ error_button_add }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-button_delete">{{ text_button_delete }}</label>
            <div class="col-sm-10">
              <input type="text" name="button_delete" value="{{ button_delete }}" placeholder="{{ text_button_delete }}" id="input-button_delete" class="form-control" />
              {% if error_button_delete %}
                <div class="invalid-feedback">{{ error_button_delete }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-button_edit">{{ text_button_edit }}</label>
            <div class="col-sm-10">
              <input type="text" name="button_edit" value="{{ button_edit }}" placeholder="{{ text_button_edit }}" id="input-button_edit" class="form-control" />
              {% if error_button_edit %}
                <div class="invalid-feedback">{{ error_button_edit }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-button_export">{{ text_button_export }}</label>
            <div class="col-sm-10">
              <input type="text" name="button_export" value="{{ button_export }}" placeholder="{{ text_button_export }}" id="input-button_export" class="form-control" />
              {% if error_button_export %}
                <div class="invalid-feedback">{{ error_button_export }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-button_filter">{{ text_button_filter }}</label>
            <div class="col-sm-10">
              <input type="text" name="button_filter" value="{{ button_filter }}" placeholder="{{ text_button_filter }}" id="input-button_filter" class="form-control" />
              {% if error_button_filter %}
                <div class="invalid-feedback">{{ error_button_filter }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-button_print_label">{{ text_button_print_label }}</label>
            <div class="col-sm-10">
              <input type="text" name="button_print_label" value="{{ button_print_label }}" placeholder="{{ text_button_print_label }}" id="input-button_print_label" class="form-control" />
              {% if error_button_print_label %}
                <div class="invalid-feedback">{{ error_button_print_label }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-button_track">{{ text_button_track }}</label>
            <div class="col-sm-10">
              <input type="text" name="button_track" value="{{ button_track }}" placeholder="{{ text_button_track }}" id="input-button_track" class="form-control" />
              {% if error_button_track %}
                <div class="invalid-feedback">{{ error_button_track }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-carrier_analysis">{{ text_carrier_analysis }}</label>
            <div class="col-sm-10">
              <input type="text" name="carrier_analysis" value="{{ carrier_analysis }}" placeholder="{{ text_carrier_analysis }}" id="input-carrier_analysis" class="form-control" />
              {% if error_carrier_analysis %}
                <div class="invalid-feedback">{{ error_carrier_analysis }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-carriers">{{ text_carriers }}</label>
            <div class="col-sm-10">
              <input type="text" name="carriers" value="{{ carriers }}" placeholder="{{ text_carriers }}" id="input-carriers" class="form-control" />
              {% if error_carriers %}
                <div class="invalid-feedback">{{ error_carriers }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_action">{{ text_column_action }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_action" value="{{ column_action }}" placeholder="{{ text_column_action }}" id="input-column_action" class="form-control" />
              {% if error_column_action %}
                <div class="invalid-feedback">{{ error_column_action }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_carrier">{{ text_column_carrier }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_carrier" value="{{ column_carrier }}" placeholder="{{ text_column_carrier }}" id="input-column_carrier" class="form-control" />
              {% if error_column_carrier %}
                <div class="invalid-feedback">{{ error_column_carrier }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_customer">{{ text_column_customer }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_customer" value="{{ column_customer }}" placeholder="{{ text_column_customer }}" id="input-column_customer" class="form-control" />
              {% if error_column_customer %}
                <div class="invalid-feedback">{{ error_column_customer }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_date_shipped">{{ text_column_date_shipped }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_date_shipped" value="{{ column_date_shipped }}" placeholder="{{ text_column_date_shipped }}" id="input-column_date_shipped" class="form-control" />
              {% if error_column_date_shipped %}
                <div class="invalid-feedback">{{ error_column_date_shipped }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_order_number">{{ text_column_order_number }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_order_number" value="{{ column_order_number }}" placeholder="{{ text_column_order_number }}" id="input-column_order_number" class="form-control" />
              {% if error_column_order_number %}
                <div class="invalid-feedback">{{ error_column_order_number }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_shipment_number">{{ text_column_shipment_number }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_shipment_number" value="{{ column_shipment_number }}" placeholder="{{ text_column_shipment_number }}" id="input-column_shipment_number" class="form-control" />
              {% if error_column_shipment_number %}
                <div class="invalid-feedback">{{ error_column_shipment_number }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_status">{{ text_column_status }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_status" value="{{ column_status }}" placeholder="{{ text_column_status }}" id="input-column_status" class="form-control" />
              {% if error_column_status %}
                <div class="invalid-feedback">{{ error_column_status }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_tracking_number">{{ text_column_tracking_number }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_tracking_number" value="{{ column_tracking_number }}" placeholder="{{ text_column_tracking_number }}" id="input-column_tracking_number" class="form-control" />
              {% if error_column_tracking_number %}
                <div class="invalid-feedback">{{ error_column_tracking_number }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-delete">{{ text_delete }}</label>
            <div class="col-sm-10">
              <input type="text" name="delete" value="{{ delete }}" placeholder="{{ text_delete }}" id="input-delete" class="form-control" />
              {% if error_delete %}
                <div class="invalid-feedback">{{ error_delete }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-entry_carrier">{{ text_entry_carrier }}</label>
            <div class="col-sm-10">
              <input type="text" name="entry_carrier" value="{{ entry_carrier }}" placeholder="{{ text_entry_carrier }}" id="input-entry_carrier" class="form-control" />
              {% if error_entry_carrier %}
                <div class="invalid-feedback">{{ error_entry_carrier }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-entry_customer">{{ text_entry_customer }}</label>
            <div class="col-sm-10">
              <input type="text" name="entry_customer" value="{{ entry_customer }}" placeholder="{{ text_entry_customer }}" id="input-entry_customer" class="form-control" />
              {% if error_entry_customer %}
                <div class="invalid-feedback">{{ error_entry_customer }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-entry_date_from">{{ text_entry_date_from }}</label>
            <div class="col-sm-10">
              <input type="text" name="entry_date_from" value="{{ entry_date_from }}" placeholder="{{ text_entry_date_from }}" id="input-entry_date_from" class="form-control" />
              {% if error_entry_date_from %}
                <div class="invalid-feedback">{{ error_entry_date_from }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-entry_date_to">{{ text_entry_date_to }}</label>
            <div class="col-sm-10">
              <input type="text" name="entry_date_to" value="{{ entry_date_to }}" placeholder="{{ text_entry_date_to }}" id="input-entry_date_to" class="form-control" />
              {% if error_entry_date_to %}
                <div class="invalid-feedback">{{ error_entry_date_to }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-entry_order_number">{{ text_entry_order_number }}</label>
            <div class="col-sm-10">
              <input type="text" name="entry_order_number" value="{{ entry_order_number }}" placeholder="{{ text_entry_order_number }}" id="input-entry_order_number" class="form-control" />
              {% if error_entry_order_number %}
                <div class="invalid-feedback">{{ error_entry_order_number }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-entry_shipment_number">{{ text_entry_shipment_number }}</label>
            <div class="col-sm-10">
              <input type="text" name="entry_shipment_number" value="{{ entry_shipment_number }}" placeholder="{{ text_entry_shipment_number }}" id="input-entry_shipment_number" class="form-control" />
              {% if error_entry_shipment_number %}
                <div class="invalid-feedback">{{ error_entry_shipment_number }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-entry_status">{{ text_entry_status }}</label>
            <div class="col-sm-10">
              <input type="text" name="entry_status" value="{{ entry_status }}" placeholder="{{ text_entry_status }}" id="input-entry_status" class="form-control" />
              {% if error_entry_status %}
                <div class="invalid-feedback">{{ error_entry_status }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error">{{ text_error }}</label>
            <div class="col-sm-10">
              <input type="text" name="error" value="{{ error }}" placeholder="{{ text_error }}" id="input-error" class="form-control" />
              {% if error_error %}
                <div class="invalid-feedback">{{ error_error }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_carrier_id">{{ text_error_carrier_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_carrier_id" value="{{ error_carrier_id }}" placeholder="{{ text_error_carrier_id }}" id="input-error_carrier_id" class="form-control" />
              {% if error_error_carrier_id %}
                <div class="invalid-feedback">{{ error_error_carrier_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_order_id">{{ text_error_order_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_order_id" value="{{ error_order_id }}" placeholder="{{ text_error_order_id }}" id="input-error_order_id" class="form-control" />
              {% if error_error_order_id %}
                <div class="invalid-feedback">{{ error_error_order_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="invalid-feedback">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-orders">{{ text_orders }}</label>
            <div class="col-sm-10">
              <input type="text" name="orders" value="{{ orders }}" placeholder="{{ text_orders }}" id="input-orders" class="form-control" />
              {% if error_orders %}
                <div class="invalid-feedback">{{ error_orders }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-performance_data">{{ text_performance_data }}</label>
            <div class="col-sm-10">
              <input type="text" name="performance_data" value="{{ performance_data }}" placeholder="{{ text_performance_data }}" id="input-performance_data" class="form-control" />
              {% if error_performance_data %}
                <div class="invalid-feedback">{{ error_performance_data }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-statistics">{{ text_statistics }}</label>
            <div class="col-sm-10">
              <input type="text" name="statistics" value="{{ statistics }}" placeholder="{{ text_statistics }}" id="input-statistics" class="form-control" />
              {% if error_statistics %}
                <div class="invalid-feedback">{{ error_statistics }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-statuses">{{ text_statuses }}</label>
            <div class="col-sm-10">
              <input type="text" name="statuses" value="{{ statuses }}" placeholder="{{ text_statuses }}" id="input-statuses" class="form-control" />
              {% if error_statuses %}
                <div class="invalid-feedback">{{ error_statuses }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_carrier_analysis">{{ text_text_carrier_analysis }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_carrier_analysis" value="{{ text_carrier_analysis }}" placeholder="{{ text_text_carrier_analysis }}" id="input-text_carrier_analysis" class="form-control" />
              {% if error_text_carrier_analysis %}
                <div class="invalid-feedback">{{ error_text_carrier_analysis }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_confirm">{{ text_text_confirm }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_confirm" value="{{ text_confirm }}" placeholder="{{ text_text_confirm }}" id="input-text_confirm" class="form-control" />
              {% if error_text_confirm %}
                <div class="invalid-feedback">{{ error_text_confirm }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_list">{{ text_text_list }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_list" value="{{ text_list }}" placeholder="{{ text_text_list }}" id="input-text_list" class="form-control" />
              {% if error_text_list %}
                <div class="invalid-feedback">{{ error_text_list }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_loading">{{ text_text_loading }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_loading" value="{{ text_loading }}" placeholder="{{ text_text_loading }}" id="input-text_loading" class="form-control" />
              {% if error_text_loading %}
                <div class="invalid-feedback">{{ error_text_loading }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_no_results">{{ text_text_no_results }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_no_results" value="{{ text_no_results }}" placeholder="{{ text_text_no_results }}" id="input-text_no_results" class="form-control" />
              {% if error_text_no_results %}
                <div class="invalid-feedback">{{ error_text_no_results }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_performance">{{ text_text_performance }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_performance" value="{{ text_performance }}" placeholder="{{ text_text_performance }}" id="input-text_performance" class="form-control" />
              {% if error_text_performance %}
                <div class="invalid-feedback">{{ error_text_performance }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_statistics">{{ text_text_statistics }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_statistics" value="{{ text_statistics }}" placeholder="{{ text_text_statistics }}" id="input-text_statistics" class="form-control" />
              {% if error_text_statistics %}
                <div class="invalid-feedback">{{ error_text_statistics }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="invalid-feedback">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}