{{ header }}{{ column_left }}

<!-- Enterprise Grade Plus CSS for Journal View -->
<style>
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --danger-color: #e74c3c;
    --warning-color: #f39c12;
    --info-color: #17a2b8;
    --journal-color: #6f42c1;
    --light-bg: #f8f9fa;
    --border-color: #dee2e6;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

.journal-view-container {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: var(--shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.journal-view-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--journal-color), var(--primary-color), var(--secondary-color));
}

.journal-header {
    text-align: center;
    border-bottom: 3px solid var(--journal-color);
    padding-bottom: 20px;
    margin-bottom: 30px;
    position: relative;
}

.journal-header h2 {
    color: var(--journal-color);
    font-weight: 700;
    font-size: 1.8rem;
    margin: 0;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.journal-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.info-card {
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    text-align: center;
    box-shadow: var(--shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.info-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0,0,0,0.1);
}

.info-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--journal-color);
}

.info-card h4 {
    color: var(--primary-color);
    margin-bottom: 10px;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.info-card .value {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--journal-color);
    margin-bottom: 5px;
}

.journal-entries-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    background: #ffffff;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.journal-entries-table th {
    background: linear-gradient(135deg, var(--journal-color), #5a32a3);
    color: white;
    padding: 15px 12px;
    text-align: center;
    font-weight: 600;
    font-size: 0.9rem;
    border-bottom: 2px solid var(--border-color);
}

.journal-entries-table td {
    padding: 12px;
    border-bottom: 1px solid var(--border-color);
    text-align: center;
    transition: var(--transition);
}

.journal-entries-table tbody tr:hover {
    background: var(--light-bg);
    transform: scale(1.005);
}

.journal-entries-table tbody tr:nth-child(even) {
    background: #fafbfc;
}

.amount-cell {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    text-align: {{ direction == 'rtl' ? 'right' : 'left' }};
}

.amount-debit { 
    color: var(--danger-color); 
    font-weight: 600;
}

.amount-credit { 
    color: var(--success-color); 
    font-weight: 600;
}

.status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-align: center;
}

.status-posted {
    background: rgba(39, 174, 96, 0.1);
    color: var(--success-color);
}

.status-draft {
    background: rgba(243, 156, 18, 0.1);
    color: var(--warning-color);
}

.status-cancelled {
    background: rgba(231, 76, 60, 0.1);
    color: var(--danger-color);
}

.attachments-section {
    margin-top: 30px;
    padding: 20px;
    background: var(--light-bg);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.attachment-item {
    display: flex;
    align-items: center;
    padding: 10px;
    margin-bottom: 10px;
    background: white;
    border-radius: 6px;
    border: 1px solid var(--border-color);
    transition: var(--transition);
}

.attachment-item:hover {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.attachment-icon {
    width: 40px;
    height: 40px;
    background: var(--journal-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-right: 15px;
}

/* RTL Support */
[dir="rtl"] .journal-entries-table {
    direction: rtl;
}

[dir="rtl"] .amount-cell {
    text-align: right;
}

[dir="rtl"] .attachment-icon {
    margin-right: 0;
    margin-left: 15px;
}

/* Print Styles */
@media print {
    .journal-view-container {
        box-shadow: none;
        border: 1px solid #000;
    }
    
    .journal-entries-table th {
        background: #f0f0f0 !important;
        color: #000 !important;
    }
    
    .btn, .panel-heading {
        display: none !important;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .journal-entries-table {
        font-size: 0.8rem;
    }
    
    .journal-entries-table th,
    .journal-entries-table td {
        padding: 8px 6px;
    }
    
    .journal-info {
        grid-template-columns: repeat(2, 1fr);
    }
}
</style>

<div id="content">
    <div class="page-header">
        <div class="container-fluid">
            <div class="float-{{ direction == 'rtl' ? 'start' : 'end' }}">
                <a href="{{ back }}" data-toggle="tooltip" title="{{ button_back }}" class="btn btn-default">
                    <i class="fa fa-arrow-left"></i>
                </a>
                <a href="{{ edit }}" data-toggle="tooltip" title="{{ button_edit }}" class="btn btn-primary">
                    <i class="fa fa-pencil"></i>
                </a>
                <button type="button" data-toggle="tooltip" title="{{ button_print }}" class="btn btn-info" onclick="window.print()">
                    <i class="fa fa-print"></i>
                </button>
            </div>
            <h1>{{ heading_title }}</h1>
            <ul class="breadcrumb">
                {% for breadcrumb in breadcrumbs %}
                <li class="breadcrumb-item">
                    <a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a>
                </li>
                {% endfor %}
            </ul>
        </div>
    </div>

    <div class="container-fluid">
        {% if error_warning %}
        <div class="alert alert-danger alert-dismissible">
            <i class="fa fa-exclamation-triangle"></i>
            {{ error_warning }}
            <button type="button" class="close" data-dismiss="alert">&times;</button>
        </div>
        {% endif %}

        {% if success %}
        <div class="alert alert-success alert-dismissible">
            <i class="fa fa-check-circle"></i>
            {{ success }}
            <button type="button" class="close" data-dismiss="alert">&times;</button>
        </div>
        {% endif %}

        <!-- Journal Information -->
        <div class="journal-view-container">
            <div class="journal-header">
                <h2>{{ text_journal_entry }} #{{ journal.refnum }}</h2>
                <p><strong>{{ text_date }}:</strong> {{ journal.thedate }}</p>
                <div class="status-badge status-{{ journal.status }}">
                    {{ journal.status_text }}
                </div>
            </div>

            <!-- Journal Info Cards -->
            <div class="journal-info">
                <div class="info-card">
                    <h4>{{ text_reference_number }}</h4>
                    <div class="value">{{ journal.refnum }}</div>
                </div>
                
                <div class="info-card">
                    <h4>{{ text_total_debit }}</h4>
                    <div class="value amount-debit">{{ journal.total_debit_formatted }}</div>
                </div>
                
                <div class="info-card">
                    <h4>{{ text_total_credit }}</h4>
                    <div class="value amount-credit">{{ journal.total_credit_formatted }}</div>
                </div>
                
                <div class="info-card">
                    <h4>{{ text_created_by }}</h4>
                    <div class="value">{{ journal.added_by }}</div>
                </div>
            </div>

            <!-- Description -->
            {% if journal.description %}
            <div class="alert alert-info">
                <h5>{{ text_description }}:</h5>
                <p>{{ journal.description }}</p>
            </div>
            {% endif %}

            <!-- Journal Entries Table -->
            <h4>{{ text_journal_entries }}</h4>
            <div class="table-responsive">
                <table class="journal-entries-table">
                    <thead>
                        <tr>
                            <th>{{ column_account_code }}</th>
                            <th>{{ column_account_name }}</th>
                            <th>{{ column_debit }}</th>
                            <th>{{ column_credit }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for entry in journal.entries %}
                        <tr>
                            <td><strong>{{ entry.account_code }}</strong></td>
                            <td>{{ entry.account_name }}</td>
                            <td class="amount-cell">
                                {% if entry.is_debit %}
                                <span class="amount-debit">{{ entry.amount_formatted }}</span>
                                {% else %}
                                -
                                {% endif %}
                            </td>
                            <td class="amount-cell">
                                {% if not entry.is_debit %}
                                <span class="amount-credit">{{ entry.amount_formatted }}</span>
                                {% else %}
                                -
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                    <tfoot>
                        <tr style="background: #f8f9fa; font-weight: bold;">
                            <td colspan="2"><strong>{{ text_totals }}</strong></td>
                            <td class="amount-cell amount-debit">{{ journal.total_debit_formatted }}</td>
                            <td class="amount-cell amount-credit">{{ journal.total_credit_formatted }}</td>
                        </tr>
                    </tfoot>
                </table>
            </div>

            <!-- Attachments -->
            {% if journal.attachments %}
            <div class="attachments-section">
                <h4>{{ text_attachments }}</h4>
                {% for attachment in journal.attachments %}
                <div class="attachment-item">
                    <div class="attachment-icon">
                        <i class="fa fa-file"></i>
                    </div>
                    <div class="attachment-info">
                        <strong>{{ attachment.file_name }}</strong>
                        <br>
                        <small class="text-muted">{{ attachment.file_size }}</small>
                    </div>
                    <div class="ms-auto">
                        <a href="{{ attachment.download_url }}" class="btn btn-sm btn-outline-primary">
                            <i class="fa fa-download"></i> {{ button_download }}
                        </a>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
// Initialize tooltips
$(document).ready(function() {
    $('[data-toggle="tooltip"]').tooltip();
});
</script>

{{ footer }}
