name: CI

on:
  pull_request:

jobs:
  build:
    name: Build
    runs-on: ubuntu-22.04
    strategy:
      max-parallel: 10
      matrix:
        php: ['5.5', '5.6', '7.0', '7.1', '7.2', '7.3', '7.4', '8.0', '8.1']

    steps:
      - name: Set up PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php }}
          coverage: 'none'
          extensions: mbstring

      - name: Checkout code
        uses: actions/checkout@v3

      - name: Install dependencies
        run: composer update --no-interaction --no-progress

      - name: Run tests
        run: make test
