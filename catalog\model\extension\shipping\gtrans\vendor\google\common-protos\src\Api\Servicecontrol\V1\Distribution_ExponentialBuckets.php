<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/servicecontrol/v1/distribution.proto

namespace Google\Api\Servicecontrol\V1;

if (false) {
    /**
     * This class is deprecated. Use Google\Api\Servicecontrol\V1\Distribution\ExponentialBuckets instead.
     * @deprecated
     */
    class Distribution_ExponentialBuckets {}
}
class_exists(Distribution\ExponentialBuckets::class);
@trigger_error('Google\Api\Servicecontrol\V1\Distribution_ExponentialBuckets is deprecated and will be removed in the next major release. Use Google\Api\Servicecontrol\V1\Distribution\ExponentialBuckets instead', E_USER_DEPRECATED);

