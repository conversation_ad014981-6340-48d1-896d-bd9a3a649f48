<?php
// Heading
$_['heading_title']          = 'Campaign Management';

// Text
$_['text_filter']            = 'Filter';
$_['text_campaign_name']     = 'Campaign Name';
$_['text_enter_campaign_name']= 'Enter campaign name';
$_['text_date_start']        = 'Start Date';
$_['text_date_end']          = 'End Date';
$_['button_filter']          = 'Filter';
$_['button_reset']           = 'Reset';
$_['button_add_campaign']    = 'Add Campaign';
$_['text_campaign_list']     = 'Campaign List';
$_['text_add_campaign']      = 'Add Campaign';
$_['text_edit_campaign']     = 'Edit Campaign';
$_['text_ajax_error']        = 'An error occurred while communicating with the server';
$_['text_confirm_delete']    = 'Are you sure you want to delete?';
$_['text_name']              = 'Name';
$_['text_type']              = 'Type';
$_['text_type_other']        = 'Other';
$_['text_start_date']        = 'Start Date';
$_['text_end_date']          = 'End Date';
$_['text_budget']            = 'Budget';
$_['text_code']              = 'Tracking Code';
$_['text_code_help']         = 'Used to link orders to this campaign';
$_['text_status']            = 'Status';
$_['text_all_statuses']      = 'All Statuses';
$_['text_status_active']     = 'Active';
$_['text_status_inactive']   = 'Inactive';
$_['text_status_completed']  = 'Completed';
$_['text_assigned_to']       = 'Assigned To';
$_['text_select_user']       = 'Select User';
$_['text_actual_spend']      = 'Actual Spend';
$_['text_invoice_reference'] = 'Invoice Reference';
$_['text_invoice_reference_help'] = 'Invoice number or reference';
$_['text_add_expense']       = 'Add as Advertising Expense';
$_['text_add_expense_help']  = 'If checked and amount > 0, a journal entry for advertising expense will be created.';
$_['text_notes']             = 'Notes';
$_['button_close']           = 'Close';
$_['button_save']            = 'Save';

// Columns
$_['column_name']            = 'Name';
$_['column_type']            = 'Type';
$_['column_start_date']      = 'Start Date';
$_['column_end_date']        = 'End Date';
$_['column_budget']          = 'Budget';
$_['column_status']          = 'Status';
$_['column_actions']         = 'Actions';

// Period Stats
$_['text_period_stats']      = 'Period Statistics';
$_['text_visits']            = 'Visits';
$_['text_orders']            = 'Orders';

// Errors/Success
$_['error_not_found']        = 'Record not found!';
$_['error_invalid_request']  = 'Invalid request!';
$_['error_permission']       = 'Warning: You do not have permission to modify campaigns!';
$_['error_required']         = 'Warning: Please fill in the required fields!';
$_['text_success_add']       = 'Campaign added successfully!';
$_['text_success_edit']      = 'Campaign updated successfully!';
$_['text_success_delete']    = 'Campaign deleted successfully!';

// Breadcrumb
$_['text_home']              = 'Home';
$_['text_crm']               = 'CRM';
