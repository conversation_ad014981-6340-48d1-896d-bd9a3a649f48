# 🏗️ قواعد تطوير AYM ERP الشاملة - Enterprise Grade v2.0
*محدث بناءً على التحليل الشامل للنظام والتكامل مع الأنظمة المركزية*

---

## 📋 **القواعد الأساسية للتطوير المتقدم**

### 1. 🔍 قاعدة المراجعة الشاملة المتقدمة (Advanced Comprehensive Review Rule)
- **استخدم aym_ultimate_auditor_v9.py** قبل مراجعة أي ملف لتحليل التكامل مع الأنظمة المركزية
- **اقرأ كل ملف سطرًا بسطر بالكامل** مع التركيز على:
  - التكامل مع Central Service Manager (نسبة التكامل المطلوبة: 95%+)
  - استخدام `$this->config->get()` من setting/setting.php (نسبة التكامل المطلوبة: 95%+)
  - استخدام `hasKey()` و `hasPermission()` (نسبة التطبيق المطلوبة: 100%)
  - تسجيل الأنشطة في Activity Log (نسبة التطبيق المطلوبة: 100%)
  - استخدام Queue Manager للعمليات المعقدة (نسبة التطبيق المطلوبة: 90%+)
- **لا تعدل أو تحذف أي شيء** إلا بعد فهم كامل للترابطات والتأكد من النتيجة 85%+ في aym_ultimate_auditor_v9.py
- **تحرك وفق ترتيب العمود الجانبي** (column_left.php - 3,674 سطر) مع مراجعة كل وحدة مرتبطة

### 2. 🎯 قاعدة التكامل المركزي الإلزامي (Mandatory Central Integration Rule)
- **كل ملف يجب أن يحقق نتيجة 85%+ في aym_ultimate_auditor_v9.py** قبل اعتباره مكتملاً
- **التكامل الإلزامي مع الخدمات المركزية:**
  - Central Service Manager: استدعاء الخدمات المطلوبة في بداية كل كونترولر
  - Activity Log: تسجيل جميع العمليات الحساسة (إضافة، تعديل، حذف، عرض حساس)
  - Unified Notification: إرسال إشعارات للعمليات المهمة
  - Journal Entry Service: إنشاء القيود المحاسبية التلقائية
  - WAC Calculator: حساب التكلفة المتوسطة المرجحة للمخزون
  - Queue Manager: معالجة العمليات المعقدة في الخلفية
- **عدم التكامل = فشل في المراجعة**

### 3. ⚙️ قاعدة الإعدادات المركزية الشاملة (Comprehensive Central Settings Rule)
- **استخدم `$this->config->get()` بدلاً من أي قيم ثابتة** في الكود
- **الإعدادات المطلوبة في setting/setting.php:**
  - حسابات المبيعات/المشتريات/المخزون الافتراضية لكل فرع
  - إعدادات WAC (طريقة الحساب، تكرار إعادة الحساب، معالجة الكميات السالبة)
  - إعدادات الطوابير (Redis، أولويات، إعادة المحاولة)
  - إعدادات التكامل الخارجي (ETA، بوابات الدفع، شركات الشحن)
  - إعدادات الأمان والصلاحيات المتقدمة
- **كل إعداد يجب أن يكون قابل للتخصيص** من واجهة الإعدادات

### 4. 🔄 قاعدة الطوابير والعمليات المعقدة (Queue & Complex Operations Rule)
- **استخدم AYMQueueManager** لجميع العمليات التي تستغرق أكثر من 3 ثوان
- **العمليات التي تتطلب طوابير:**
  - حساب WAC للمنتجات (خاصة للكميات الكبيرة)
  - مزامنة المخزون بين الفروع
  - معالجة الطلبات المعقدة (أكثر من 50 منتج)
  - إنشاء القيود المحاسبية المعقدة
  - إرسال الإشعارات الجماعية (أكثر من 100 مستخدم)
  - تحديث الأسعار الديناميكية
  - إنشاء التقارير الثقيلة
  - النسخ الاحتياطية التلقائية
- **كل مهمة في الطابور يجب أن تُسجل في Activity Log**

### 5. 📊 قاعدة قاعدة البيانات المتقدمة (Advanced Database Rule)
- **راجع minidb.txt (451 جدول)** قبل إنشاء أي جداول جديدة
- **استخدم البادئة `cod_`** لجميع الجداول الجديدة
- **طبق نظام WAC المتطور** مع دعم:
  - الوحدات المتعددة لكل منتج
  - الفروع المتعددة
  - العملات المتعددة
  - المعالجة المتوازية للحسابات
- **أنشئ القيود المحاسبية التلقائية** لكل عملية مع ربطها بالحسابات الافتراضية من الإعدادات

---

## 🔧 **القواعد التقنية المتقدمة والتكامل**

### 6. 🏛️ قاعدة MVC والتكامل المتطور (Advanced MVC Integration Rule)
- **اتبع نمط MVC المحسن** مع التكامل المركزي:
  - **Controller**: يجب أن يستدعي Central Service Manager في البداية
  - **Model**: يجب أن يتكامل مع WAC Calculator و Queue Manager
  - **View**: يجب أن يعرض الإشعارات من Unified Notification
- **استخدم AJAX** لجميع العمليات مع معالجة أخطاء متقدمة
- **أرجع JSON responses موحدة** تتضمن:
  ```json
  {
    "success": true/false,
    "message": "رسالة واضحة",
    "data": {},
    "notifications": [],
    "activity_logged": true,
    "queue_job_id": "job_123" // للعمليات المعقدة
  }
  ```

### 7. 🔐 قاعدة الأمان المتقدم والصلاحيات (Advanced Security & Permissions Rule)
- **نظام الصلاحيات المزدوج الإلزامي:**
  - `hasPermission()` للصلاحيات الأساسية (عرض، إضافة، تعديل، حذف)
  - `hasKey()` للصلاحيات المتقدمة (موافقة، تصدير، إعدادات متقدمة)
  - المجموعة 1 (إدارة الشركة) لها جميع الصلاحيات تلقائياً
- **تسجيل الأمان الإلزامي:**
  - كل محاولة وصول غير مصرح (نجحت أو فشلت)
  - كل عملية حساسة (مالية، مخزون، بيانات عملاء)
  - كل تغيير في الإعدادات أو الصلاحيات
- **حماية البيانات:**
  - تشفير البيانات الحساسة (كلمات المرور، أرقام البطاقات)
  - CSRF Protection لجميع النماذج
  - XSS Protection لجميع المدخلات
  - SQL Injection Prevention باستخدام Prepared Statements

### 8. ⚡ قاعدة الأداء والاستجابة المتقدمة (Advanced Performance Rule)
- **معايير الأداء الإلزامية:**
  - تحميل أي شاشة في أقل من 2 ثانية (محسن من 3 ثوان)
  - استجابة AJAX في أقل من 500ms
  - استجابة الإشعارات في أقل من 200ms (محسن من ثانية)
  - دعم 50,000+ مستخدم متزامن (محسن من 10,000)
- **تحسينات الأداء المطلوبة:**
  - استخدام Redis للتخزين المؤقت
  - فهارس قاعدة البيانات المحسنة
  - ضغط الاستجابات (Gzip)
  - تحميل تدريجي للبيانات (Lazy Loading)
  - معالجة متوازية للعمليات المعقدة

### 9. 🌐 قاعدة التكامل الخارجي المتقدم (Advanced External Integration Rule)
- **التكامل الإلزامي مع الأنظمة المصرية:**
  - منظومة الفواتير الإلكترونية (ETA) - تكامل كامل
  - البنوك المصرية (CIB، NBE، AAIB، QNB) - APIs مباشرة
  - شركات الشحن المحلية (أرامكس، سبيدي، بوستا) - تتبع فوري
- **التكامل مع المنصات العالمية:**
  - بوابات الدفع (PayPal، Stripe، Visa، MasterCard)
  - منصات التجارة الإلكترونية (Shopify، WooCommerce، Magento)
  - خدمات التخزين السحابي (AWS، Azure، Google Cloud)
- **كل تكامل خارجي يجب أن يمر عبر Queue Manager**

---

## 🎯 **قواعد الجودة والتفوق التنافسي**

### 10. 🏆 قاعدة Enterprise Grade Quality المتطورة
- **كل شاشة يجب أن تتفوق على SAP/Oracle/Microsoft/Odoo** في:
  - سهولة الاستخدام (User Experience)
  - سرعة الاستجابة (Performance)
  - ثراء الميزات (Feature Richness)
  - جودة التصميم (Design Quality)
- **دعم متقدم للأعمال المصرية:**
  - البيع عبر الإنترنت والفروع الفعلية (Omnichannel)
  - المنتجات ذات الوحدات المتنوعة (Multi-Unit Products)
  - العملات المتعددة مع أسعار صرف ديناميكية
  - اللغات المتعددة (عربي، إنجليزي، فرنسي)
- **ميزات فريدة لا توجد في المنافسين:**
  - Quick Checkout من أي مكان (تفوق 92% على Shopify)
  - PRODUCTSPRO بـ 10 أنواع عرض حديثة
  - نظام WAC متقدم مع معالجة متوازية
  - تطبيق محمول بميزات AR/VR/AI

### 11. 🤖 قاعدة الذكاء الاصطناعي المتقدم (Advanced AI Integration Rule)
- **كل شاشة يجب أن تقدم ذكاء اصطناعي:**
  - اقتراحات ذكية مبنية على البيانات التاريخية
  - تنبؤات دقيقة للمبيعات والمخزون
  - كشف الأنماط والاتجاهات المخفية
  - تحسين تلقائي للعمليات
- **نماذج AI المطلوبة:**
  - التنبؤ بالطلب (Demand Forecasting)
  - تحليل سلوك العملاء (Customer Behavior Analysis)
  - التسعير الديناميكي (Dynamic Pricing)
  - كشف الاحتيال (Fraud Detection)
  - تحسين المخزون (Inventory Optimization)
- **كل نموذج AI يجب أن يتدرب على بيانات الشركة الفعلية**

### 12. 📱 قاعدة التجارة الإلكترونية المتكاملة (Integrated E-commerce Rule)
- **دمج كامل بين ERP والمتجر الإلكتروني:**
  - مزامنة فورية للمخزون والأسعار
  - إدارة موحدة للعملاء والطلبات
  - تقارير شاملة عبر جميع القنوات
- **دعم البيع عبر قنوات متعددة:**
  - المتجر الإلكتروني الرئيسي
  - تطبيق الهاتف المحمول
  - منصات التواصل الاجتماعي (Facebook، Instagram)
  - الأسواق الإلكترونية (Amazon، Jumia، Noon)
- **واجهة متجر تتفوق على Shopify وMagento:**
  - تحميل أسرع (< 1 ثانية)
  - تصميم متجاوب ومتطور
  - تجربة تسوق سلسة ومبتكرة
  - دعم كامل للغة العربية وRTL

---

## 🔍 **قواعد المراجعة والتدقيق المتقدمة**

### 13. 📋 قاعدة مراجعة الشاشات المتطورة (Advanced Screen Review Rule)
**قبل مراجعة أي شاشة، يجب تشغيل aym_ultimate_auditor_v9.py عليها أولاً**

عند مراجعة أي شاشة، يجب الإجابة على:

1. **التحليل التقني المتقدم:**
   - ما نتيجة aym_ultimate_auditor_v9.py للملف؟ (يجب أن تكون 85%+)
   - هل يتكامل مع Central Service Manager؟ (95%+)
   - هل يستخدم setting/setting.php بشكل صحيح؟ (95%+)
   - هل يطبق hasKey/hasPermission؟ (100%)
   - هل يسجل في Activity Log؟ (100%)

2. **المقارنة التنافسية:**
   - كيف تقارن هذه الشاشة مع SAP/Oracle/Microsoft/Odoo؟
   - ما الميزات المفقودة مقارنة بـ Shopify/Magento/WooCommerce؟
   - هل تقدم تجربة مستخدم أفضل من المنافسين؟

3. **التكامل والترابط:**
   - هل تتكامل مع جميع الشاشات ذات الصلة؟
   - هل تدعم سير العمل الكامل للعملية؟
   - هل تتوافق مع قاعدة البيانات (451 جدول)؟

4. **الجودة والأداء:**
   - هل تحمل في أقل من 2 ثانية؟
   - هل تدعم الأجهزة المحمولة؟
   - هل تدعم اللغة العربية وRTL بالكامل؟

### 14. 🔗 قاعدة التكامل الشامل المحدثة (Updated Full Integration Rule)
- **كل شاشة يجب أن تحقق 85%+ في aym_ultimate_auditor_v9.py**
- **التكامل الإلزامي مع الخدمات المركزية:**
  - Central Service Manager: استدعاء في بداية كل كونترولر
  - Activity Log: تسجيل جميع العمليات الحساسة
  - Unified Notification: إشعارات للعمليات المهمة
  - Journal Entry Service: قيود محاسبية تلقائية
  - WAC Calculator: حساب التكلفة للمخزون
  - Queue Manager: العمليات المعقدة في الخلفية
- **كل عملية مالية يجب أن تنشئ قيود محاسبية فورية**
- **كل تحديث مخزون يجب أن يحدث WAC تلقائياً**
- **كل نشاط حساس يجب أن يُسجل مع تفاصيل كاملة**

### 15. 📱 قاعدة الهيدر المتكامل المحدثة (Updated Integrated Header Rule)
- **كل صفحة يجب أن تعرض header.twig المحدث** مع:
  - Quick Checkout Sidebar (4,510 سطر كود متقدم)
  - نظام الإشعارات المباشر مع 4 تبويبات
  - عداد المنتجات في السلة (تحديث فوري)
  - قائمة التواصل السريع مع المستخدمين المتصلين
- **عداد الإشعارات يجب أن يتحدث كل 5 ثوان**
- **Quick Checkout يجب أن يعمل من أي صفحة** بدون إعادة تحميل
- **دعم كامل للأجهزة المحمولة** مع تصميم متجاوب

### 16. 📄 قاعدة نظام المستندات المتطور (Advanced Documents Rule)
- **استخدم الجداول الـ7 المتخصصة** من minidb.txt:
  - cod_unified_document (المستند الأساسي)
  - cod_document_permission (صلاحيات المستندات)
  - cod_document_version (إدارة الإصدارات)
  - cod_document_approval (نظام الموافقات)
  - cod_document_category (تصنيف المستندات)
  - cod_document_tag (علامات المستندات)
  - cod_document_activity (سجل أنشطة المستندات)
- **طبق نظام الصلاحيات المتقدم** مع hasKey للعمليات الحساسة
- **دعم إدارة الإصدارات التلقائية** مع تتبع التغييرات
- **تكامل مع نظام البحث المتقدم** والفهرسة الذكية

---

## 🚀 **القواعد الاستراتيجية والتنافسية المتقدمة**

### 17. 🏅 قاعدة التفوق على المنافسين المحدثة (Updated Competitive Advantage Rule)
- **قارن كل ميزة مع المنافسين باستخدام معايير محددة:**
  - SAP: التعقيد والشمولية (نتفوق بالبساطة والسهولة)
  - Oracle: القوة والاستقرار (نتفوق بالسرعة والمرونة)
  - Microsoft: التكامل والانتشار (نتفوق بالتخصص والتوطين)
  - Odoo: المرونة والتكلفة (نتفوق بالميزات والأداء)
  - Shopify: سهولة التجارة الإلكترونية (نتفوق بالتكامل مع ERP)
- **طور أدوات نقل البيانات متقدمة:**
  - استيراد من SAP (XML/CSV)
  - استيراد من Oracle (Database Direct)
  - استيراد من Odoo (API Integration)
  - استيراد من Excel/CSV (Smart Mapping)
- **اجعل الانتقال إلى AYM ERP مجاني ومدعوم بالكامل**
- **قدم ميزات فريدة لا توجد في أي منافس**

### 18. ☁️ قاعدة SaaS والاستقلالية المتطورة (Advanced SaaS Independence Rule)
- **صمم النظام ليكون SaaS-Ready بالكامل:**
  - Multi-tenant architecture مع عزل كامل للبيانات
  - Auto-scaling حسب الاستخدام
  - Load balancing متقدم
  - CDN integration للأداء العالمي
- **نظام Subscription Management متقدم:**
  - خطط اشتراك مرنة (شهرية، سنوية، مدى الحياة)
  - فوترة تلقائية مع دعم جميع طرق الدفع
  - تجارب مجانية مع حدود ذكية
  - ترقية/تخفيض الخطط فورياً
- **نشر سحابي متقدم:**
  - AWS/Azure/Google Cloud support
  - Docker containers للنشر السريع
  - Kubernetes orchestration
  - CI/CD pipelines متقدمة

### 19. 📱 قاعدة التطبيق المحمول الثوري (Revolutionary Mobile App Rule)
- **تطبيق AYM Mobile بميزات فريدة:**
  - نظام العمولات الذكي المتعدد المستويات
  - خريطة العملاء الجغرافية مع AI
  - نقطة بيع ذكية بالواقع المعزز
  - الطلبات الصوتية بفهم اللهجات المحلية
  - الجرد بالذكاء الاصطناعي والرؤية الحاسوبية
  - التوقيع البيومتري متعدد العوامل
  - مساعد ذكي شخصي لكل مستخدم
- **تكامل كامل مع النظام الأساسي:**
  - تزامن فوري ثنائي الاتجاه
  - عمل أوفلاين مع مزامنة ذكية
  - أمان Enterprise-grade
- **تفوق على جميع تطبيقات المنافسين بنسبة 85%+**

### 20. 🌍 قاعدة التوسع الإقليمي والعالمي (Regional & Global Expansion Rule)
- **التوطين الكامل للأسواق المستهدفة:**
  - مصر: تكامل ETA، البنوك المحلية، شركات الشحن
  - السعودية: تكامل ZATCA، البنوك السعودية، نظام الفوترة
  - الإمارات: تكامل VAT، البنوك الإماراتية، المناطق الحرة
  - الكويت: تكامل الضرائب الكويتية، البنوك المحلية
- **دعم اللغات والعملات:**
  - العربية (مع  اللهجات المحلية)
  - الإنجليزية (أمريكية وبريطانية)
  - الفرنسية (للأسواق الأفريقية)
  - جميع العملات مع أسعار صرف حية
- **شراكات استراتيجية محلية في كل دولة**

## قواعد التنفيذ والمتابعة

### 19. قاعدة تحديث المهام (Task Update Rule)
- **حدث حالة المهام** في tasks.md عند البدء والانتهاء
- **اتبع الترتيب المنطقي** للمهام في الخطة
- **لا تنتقل للمهمة التالية** حتى اكتمال الحالية
- **وثق أي تغييرات أو اكتشافات** مهمة

### 20. قاعدة الجودة النهائية (Final Quality Rule)
- **اختبر كل وظيفة قبل اعتبارها مكتملة**
- **تأكد من التوافق مع جميع المتصفحات**
- **اختبر الاستجابة على الأجهزة المختلفة**
- **راجع الأمان والأداء** قبل الإطلاق

---

## ملاحظة مهمة

هذه القواعد **إلزامية وغير قابلة للتفاوض**. أي انحراف عنها يعتبر فشلاً في المهمة. الهدف هو إنشاء أقوى نظام ERP في مصر والشرق الأوسط، يتفوق على جميع المنافسين ويوفر تجربة استخدام استثنائية للشركات التجارية المصرية.

**نحن نسعى لـ Enterprise Grade Quality - لا مجال للتساهل أو النقص.**