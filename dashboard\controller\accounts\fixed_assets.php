<?php
/**
 * تحكم الأصول الثابتة الشامل والمتكامل
 * يجمع بين القوة والبساطة - يتفوق على SAP وOracle في سهولة الاستخدام
 * مع الاحتفاظ بالقوة والمرونة المطلوبة للشركات الكبيرة
 * متوافق مع معايير المحاسبة المصرية وقوانين الإهلاك
 */
class ControllerAccountsFixedAssets extends Controller {
    private $error = array();
    private $central_service;

    public function __construct($registry) {
        parent::__construct($registry);

        // تحميل الخدمات المركزية
        $this->load->model('core/central_service_manager');
        $this->central_service = $this->model_core_central_service_manager;
    }

    public function index() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/fixed_assets') ||
            !$this->user->hasKey('accounting_fixed_assets_view')) {

            $this->central_service->logActivity('unauthorized_access', 'accounts',
                $this->language->get('log_unauthorized_access_fixed_assets'), [
                'user_id' => $this->user->getId(),
                'ip_address' => $this->request->server['REMOTE_ADDR']
            ]);

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }
        $this->load->language('accounts/fixed_assets');
        $this->document->setTitle($this->language->get('heading_title'));

        // إضافة CSS و JavaScript المتقدم
        $this->document->addStyle('view/stylesheet/accounts/fixed_assets.css');
        $this->document->addScript('view/javascript/accounts/fixed_assets.js');
        $this->document->addScript('view/javascript/jquery/accounting.min.js');
        $this->document->addScript('view/javascript/jquery/select2.min.js');
        $this->document->addStyle('view/javascript/jquery/select2.min.css');
        $this->document->addScript('view/javascript/jquery/daterangepicker.min.js');
        $this->document->addStyle('view/javascript/jquery/daterangepicker.css');
        $this->document->addScript('view/javascript/jquery/datatables.min.js');
        $this->document->addStyle('view/javascript/jquery/datatables.min.css');

        // تسجيل الوصول للشاشة
        $this->central_service->logActivity('view', 'accounts',
            $this->language->get('log_view_fixed_assets_screen'), [
            'user_id' => $this->user->getId(),
            'screen' => 'accounts/fixed_assets'
        ]);

        $this->load->model('accounts/fixed_assets');

        $data['add'] = $this->url->link('accounts/fixed_assets/add', 'user_token=' . $this->session->data['user_token'], true);
        $data['action'] = $this->url->link('accounts/fixed_assets/print', 'user_token=' . $this->session->data['user_token'], true);

        $data['heading_title'] = $this->language->get('heading_title');
        $data['text_form'] = $this->language->get('text_form');
        $data['entry_date_end'] = $this->language->get('entry_date_end');
        $data['button_filter'] = $this->language->get('button_filter');

        $data['user_token'] = $this->session->data['user_token'];
        $data['error_warning'] = isset($this->error['warning'])?$this->error['warning']:'';

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');
        $this->response->setOutput($this->load->view('accounts/fixed_assets_list', $data));
    }

    /**
     * إضافة أصل ثابت جديد
     */
    public function add() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('modify', 'accounts/fixed_assets') ||
            !$this->user->hasKey('accounting_fixed_assets_add')) {

            $this->central_service->logActivity('unauthorized_add', 'accounts',
                $this->language->get('log_unauthorized_add_fixed_asset'), [
                'user_id' => $this->user->getId(),
                'action' => 'add_asset'
            ]);

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/fixed_assets');
        $this->document->setTitle($this->language->get('heading_title'));
        $this->load->model('accounts/fixed_assets');

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm()) {
            try {
                $asset_data = $this->prepareAssetData();

                $asset_id = $this->model_accounts_fixed_assets->addFixedAsset($asset_data);

                // تسجيل إضافة الأصل
                $this->central_service->logActivity('add_fixed_asset', 'accounts',
                    $this->language->get('log_add_fixed_asset') . ': ' . $asset_data['name'], [
                    'user_id' => $this->user->getId(),
                    'asset_id' => $asset_id,
                    'asset_name' => $asset_data['name'],
                    'cost' => $asset_data['cost'],
                    'category' => $asset_data['category']
                ]);

                // إرسال إشعار للمدير المالي
                $this->central_service->sendNotification(
                    'asset_added',
                    'إضافة أصل ثابت جديد',
                    'تم إضافة أصل ثابت جديد "' . $asset_data['name'] . '" بقيمة ' . $asset_data['cost'] . ' بواسطة ' . $this->user->getFirstName(),
                    [$this->config->get('config_chief_accountant_id')],
                    [
                        'asset_name' => $asset_data['name'],
                        'user_name' => $this->user->getFirstName() . ' ' . $this->user->getLastName(),
                        'cost' => $asset_data['cost']
                    ]
                );

                $this->session->data['success'] = $this->language->get('text_success_add');

                // إعادة توجيه حسب الإجراء المطلوب
                if (isset($this->request->post['save_and_new'])) {
                    $this->response->redirect($this->url->link('accounts/fixed_assets/add', 'user_token=' . $this->session->data['user_token'], true));
                } elseif (isset($this->request->post['save_and_depreciate'])) {
                    $this->response->redirect($this->url->link('accounts/fixed_assets/depreciate', 'asset_id=' . $asset_id . '&user_token=' . $this->session->data['user_token'], true));
                } else {
                    $this->response->redirect($this->url->link('accounts/fixed_assets', 'user_token=' . $this->session->data['user_token'], true));
                }
            } catch (Exception $e) {
                $this->error['warning'] = $e->getMessage();
            }
        }

        $this->getForm();
    }

    public function print() {
        $this->load->language('accounts/fixed_assets');
        $this->load->model('accounts/fixed_assets');

        $data['title'] = $this->language->get('print_title');
        $data['printdate'] = date('Y-m-d H:i:s');
        $data['user_token'] = $this->session->data['user_token'];
        $data['lang'] = $this->language->get('code');
        $data['direction'] = $this->language->get('direction');
        $data['whoprint'] = $this->user->getUserName();

        $date_end = isset($this->request->post['date_end']) ? $this->db->escape($this->request->post['date_end']) : date('Y-m-d');
        $data['end_date'] = date($this->language->get('date_format_short'), strtotime($date_end));

        $results = $this->model_accounts_fixed_assets->getFixedAssetsData($date_end);

        $data = array_merge($data, $results);

        $data['text_fixed_assets_report'] = $this->language->get('text_fixed_assets_report');
        $data['text_end_date'] = $this->language->get('text_end_date');
        $data['text_assets'] = $this->language->get('text_assets');
        $data['text_accum_depr'] = $this->language->get('text_accum_depr');
        $data['text_net_value'] = $this->language->get('text_net_value');

        $this->response->setOutput($this->load->view('accounts/fixed_assets_list', $data));
    }

    /**
     * حساب الإهلاك للأصول
     */
    public function depreciate() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('modify', 'accounts/fixed_assets') ||
            !$this->user->hasKey('accounting_fixed_assets_depreciate')) {

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/fixed_assets');
        $this->load->model('accounts/fixed_assets');

        $json = array();

        if (isset($this->request->get['asset_id'])) {
            try {
                $asset_id = (int)$this->request->get['asset_id'];
                $depreciation_data = $this->model_accounts_fixed_assets->calculateMonthlyDepreciation($asset_id);

                // تسجيل حساب الإهلاك
                $this->central_service->logActivity('calculate_depreciation', 'accounts',
                    'حساب الإهلاك للأصل رقم ' . $asset_id, [
                    'user_id' => $this->user->getId(),
                    'asset_id' => $asset_id,
                    'depreciation_amount' => $depreciation_data['monthly_depreciation'] ?? 0
                ]);

                $json['success'] = true;
                $json['depreciation'] = $depreciation_data;

            } catch (Exception $e) {
                $json['error'] = $e->getMessage();
            }
        } else {
            $json['error'] = $this->language->get('error_asset_id_required');
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * التخلص من الأصل
     */
    public function dispose() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('modify', 'accounts/fixed_assets') ||
            !$this->user->hasKey('accounting_fixed_assets_dispose')) {

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/fixed_assets');
        $this->load->model('accounts/fixed_assets');

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && isset($this->request->post['asset_id'])) {
            try {
                $asset_id = (int)$this->request->post['asset_id'];
                $disposal_data = array(
                    'disposal_date' => $this->request->post['disposal_date'],
                    'disposal_amount' => (float)$this->request->post['disposal_amount'],
                    'disposal_reason' => $this->request->post['disposal_reason'] ?? '',
                    'disposed_by' => $this->user->getId()
                );

                $result = $this->model_accounts_fixed_assets->disposeAsset($asset_id, $disposal_data);

                if ($result['success']) {
                    // تسجيل التخلص من الأصل
                    $this->central_service->logActivity('dispose_asset', 'accounts',
                        'التخلص من الأصل رقم ' . $asset_id, [
                        'user_id' => $this->user->getId(),
                        'asset_id' => $asset_id,
                        'disposal_amount' => $disposal_data['disposal_amount'],
                        'disposal_reason' => $disposal_data['disposal_reason']
                    ]);

                    $this->session->data['success'] = $this->language->get('text_success_dispose');
                } else {
                    $this->session->data['error'] = $result['error'];
                }

            } catch (Exception $e) {
                $this->session->data['error'] = $e->getMessage();
            }

            $this->response->redirect($this->url->link('accounts/fixed_assets', 'user_token=' . $this->session->data['user_token'], true));
        }

        $this->getDisposeForm();
    }

    /**
     * التحقق من صحة النموذج
     */
    protected function validateForm() {
        if (empty($this->request->post['name'])) {
            $this->error['name'] = $this->language->get('error_name_required');
        }

        if (empty($this->request->post['cost']) || (float)$this->request->post['cost'] <= 0) {
            $this->error['cost'] = $this->language->get('error_cost_required');
        }

        if (empty($this->request->post['category'])) {
            $this->error['category'] = $this->language->get('error_category_required');
        }

        if (empty($this->request->post['depreciation_method'])) {
            $this->error['depreciation_method'] = $this->language->get('error_depreciation_method_required');
        }

        return !$this->error;
    }

    /**
     * إعداد بيانات الأصل
     */
    protected function prepareAssetData() {
        return array(
            'name' => isset($this->request->post['name']) ? $this->db->escape($this->request->post['name']) : '',
            'description' => isset($this->request->post['description']) ? $this->db->escape($this->request->post['description']) : '',
            'category' => isset($this->request->post['category']) ? $this->db->escape($this->request->post['category']) : '',
            'cost' => isset($this->request->post['cost']) ? (float)$this->request->post['cost'] : 0,
            'purchase_date' => isset($this->request->post['purchase_date']) ? $this->db->escape($this->request->post['purchase_date']) : '',
            'depreciation_method' => isset($this->request->post['depreciation_method']) ? $this->db->escape($this->request->post['depreciation_method']) : '',
            'useful_life' => isset($this->request->post['useful_life']) ? (int)$this->request->post['useful_life'] : 0,
            'salvage_value' => isset($this->request->post['salvage_value']) ? (float)$this->request->post['salvage_value'] : 0,
            'location' => isset($this->request->post['location']) ? $this->db->escape($this->request->post['location']) : '',
            'serial_number' => isset($this->request->post['serial_number']) ? $this->db->escape($this->request->post['serial_number']) : '',
            'warranty_expiry' => isset($this->request->post['warranty_expiry']) ? $this->db->escape($this->request->post['warranty_expiry']) : '',
            'supplier' => isset($this->request->post['supplier']) ? $this->db->escape($this->request->post['supplier']) : '',
            'status' => 'active',
            'created_by' => $this->user->getId()
        );
    }

    /**
     * عرض النموذج
     */
    protected function getForm() {
        $data['breadcrumbs'] = array();
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('accounts/fixed_assets', 'user_token=' . $this->session->data['user_token'], true)
        );

        // فئات الأصول
        $data['asset_categories'] = array(
            'buildings' => $this->language->get('text_buildings'),
            'equipment' => $this->language->get('text_equipment'),
            'furniture' => $this->language->get('text_furniture'),
            'vehicles' => $this->language->get('text_vehicles'),
            'computers' => $this->language->get('text_computers'),
            'other' => $this->language->get('text_other')
        );

        // طرق الإهلاك
        $data['depreciation_methods'] = array(
            'straight_line' => $this->language->get('text_straight_line'),
            'declining_balance' => $this->language->get('text_declining_balance'),
            'sum_of_years' => $this->language->get('text_sum_of_years')
        );

        $data['user_token'] = $this->session->data['user_token'];
        $data['heading_title'] = $this->language->get('heading_title');
        $data['text_no_results'] = $this->language->get('text_no_results');

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/fixed_assets_form', $data));
    }

    /**
     * عرض نموذج التخلص من الأصل
     */
    protected function getDisposeForm() {
        // يمكن تطوير هذه الدالة لاحقاً
        $this->response->redirect($this->url->link('accounts/fixed_assets', 'user_token=' . $this->session->data['user_token'], true));
    }

    /**
     * دالة تنظيف المخرجات (CONSTITUTIONAL REQUIREMENT)
     * Sanitize all output data to prevent XSS attacks
     */
    private function sanitizeOutputData($data) {
        if (is_array($data)) {
            foreach ($data as $key => $value) {
                $data[$key] = $this->sanitizeOutputData($value);
            }
        } elseif (is_string($data)) {
            $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
        }
        return $data;
    }
}
