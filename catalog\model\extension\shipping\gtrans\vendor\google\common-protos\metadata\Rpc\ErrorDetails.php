<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/rpc/error_details.proto

namespace GPBMetadata\Google\Rpc;

class ErrorDetails
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        \GPBMetadata\Google\Protobuf\Duration::initOnce();
        $pool->internalAddGeneratedFile(hex2bin(
            "0ae8080a1e676f6f676c652f7270632f6572726f725f64657461696c732e" .
            "70726f746f120a676f6f676c652e727063223b0a095265747279496e666f" .
            "122e0a0b72657472795f64656c617918012001280b32192e676f6f676c65" .
            "2e70726f746f6275662e4475726174696f6e22320a094465627567496e66" .
            "6f12150a0d737461636b5f656e7472696573180120032809120e0a066465" .
            "7461696c18022001280922790a0c51756f74614661696c75726512360a0a" .
            "76696f6c6174696f6e7318012003280b32222e676f6f676c652e7270632e" .
            "51756f74614661696c7572652e56696f6c6174696f6e1a310a0956696f6c" .
            "6174696f6e120f0a077375626a65637418012001280912130a0b64657363" .
            "72697074696f6e1802200128092291010a094572726f72496e666f120c0a" .
            "0474797065180120012809120e0a06646f6d61696e18022001280912350a" .
            "086d6574616461746118032003280b32232e676f6f676c652e7270632e45" .
            "72726f72496e666f2e4d65746164617461456e7472791a2f0a0d4d657461" .
            "64617461456e747279120b0a036b6579180120012809120d0a0576616c75" .
            "651802200128093a0238012295010a13507265636f6e646974696f6e4661" .
            "696c757265123d0a0a76696f6c6174696f6e7318012003280b32292e676f" .
            "6f676c652e7270632e507265636f6e646974696f6e4661696c7572652e56" .
            "696f6c6174696f6e1a3f0a0956696f6c6174696f6e120c0a047479706518" .
            "0120012809120f0a077375626a65637418022001280912130a0b64657363" .
            "72697074696f6e1803200128092283010a0a42616452657175657374123f" .
            "0a106669656c645f76696f6c6174696f6e7318012003280b32252e676f6f" .
            "676c652e7270632e426164526571756573742e4669656c6456696f6c6174" .
            "696f6e1a340a0e4669656c6456696f6c6174696f6e120d0a056669656c64" .
            "18012001280912130a0b6465736372697074696f6e18022001280922370a" .
            "0b52657175657374496e666f12120a0a726571756573745f696418012001" .
            "280912140a0c73657276696e675f6461746118022001280922600a0c5265" .
            "736f75726365496e666f12150a0d7265736f757263655f74797065180120" .
            "01280912150a0d7265736f757263655f6e616d65180220012809120d0a05" .
            "6f776e657218032001280912130a0b6465736372697074696f6e18042001" .
            "280922560a0448656c7012240a056c696e6b7318012003280b32152e676f" .
            "6f676c652e7270632e48656c702e4c696e6b1a280a044c696e6b12130a0b" .
            "6465736372697074696f6e180120012809120b0a0375726c180220012809" .
            "22330a104c6f63616c697a65644d657373616765120e0a066c6f63616c65" .
            "180120012809120f0a076d657373616765180220012809426c0a0e636f6d" .
            "2e676f6f676c652e72706342114572726f7244657461696c7350726f746f" .
            "50015a3f676f6f676c652e676f6c616e672e6f72672f67656e70726f746f" .
            "2f676f6f676c65617069732f7270632f65727264657461696c733b657272" .
            "64657461696c73a20203525043620670726f746f33"
        ), true);

        static::$is_initialized = true;
    }
}

