{{ header }}{{ column_left }}
{#
/**
 * AYM ERP System: Advanced Feature Permissions List View
 *
 * عرض قائمة الصلاحيات المتقدمة - مطور بجودة عالمية
 *
 * الميزات المتقدمة:
 * - إدارة شاملة للصلاحيات المتقدمة
 * - تحكم دقيق في الوصول للميزات
 * - ربط الصلاحيات بمجموعات المستخدمين
 * - واجهة مستخدم احترافية
 * - تكامل مع Bootstrap 3.3
 *
 * @package    AYM ERP
 * <AUTHOR> Development Team
 * @copyright  2024 AYM ERP Systems
 * @license    Commercial License
 * @version    1.0.0
 * @since      2024-01-15
 */
#}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <a href="{{ add }}" data-toggle="tooltip" title="{{ button_add }}" class="btn btn-primary">
          <i class="fa fa-plus"></i> {{ button_add }}
        </a>
        <button type="button" data-toggle="tooltip" title="{{ button_delete }}" class="btn btn-danger" onclick="confirm('{{ text_confirm }}') ? $('#form-permission').submit() : false;">
          <i class="fa fa-trash-o"></i> {{ button_delete }}
        </button>
      </div>
      <h1><i class="fa fa-shield"></i> {{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>

  <div class="container-fluid">
    {# رسائل التنبيه #}
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible">
      <i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}

    {% if success %}
    <div class="alert alert-success alert-dismissible">
      <i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}

    {# إحصائيات سريعة #}
    <div class="row">
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-primary">
          <div class="panel-body">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-shield fa-5x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ permissions|length }}</div>
                <div>إجمالي الصلاحيات</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-green">
          <div class="panel-body">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-users fa-5x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge" id="active-permissions">-</div>
                <div>صلاحيات نشطة</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-yellow">
          <div class="panel-body">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-cogs fa-5x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge" id="system-permissions">-</div>
                <div>صلاحيات النظام</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-red">
          <div class="panel-body">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-key fa-5x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge" id="custom-permissions">-</div>
                <div>صلاحيات مخصصة</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    {# جدول الصلاحيات #}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title">
          <i class="fa fa-list"></i> {{ text_list }}
        </h3>
      </div>
      <div class="panel-body">
        <form action="{{ delete }}" method="post" enctype="multipart/form-data" id="form-permission">
          <div class="table-responsive">
            <table class="table table-bordered table-hover">
              <thead>
                <tr>
                  <td style="width: 1px;" class="text-center">
                    <input type="checkbox" onclick="$('input[name*=\'selected\']').prop('checked', this.checked);" />
                  </td>
                  <td class="text-center">{{ column_name }}</td>
                  <td class="text-center">{{ column_key }}</td>
                  <td class="text-center">{{ column_type }}</td>
                  <td class="text-center">{{ column_user_groups }}</td>
                  <td class="text-center">{{ column_status }}</td>
                  <td class="text-center">{{ column_action }}</td>
                </tr>
              </thead>
              <tbody>
                {% if permissions %}
                  {% for permission in permissions %}
                  <tr>
                    <td class="text-center">
                      {% if permission.permission_id %}
                      <input type="checkbox" name="selected[]" value="{{ permission.permission_id }}" />
                      {% endif %}
                    </td>
                    <td class="text-left">
                      <strong>{{ permission.name }}</strong>
                      {% if permission.description %}
                      <br><small class="text-muted">{{ permission.description }}</small>
                      {% endif %}
                    </td>
                    <td class="text-center">
                      <code>{{ permission.key }}</code>
                    </td>
                    <td class="text-center">
                      {% if permission.type == 'access' %}
                        <span class="label label-info">{{ text_type_access }}</span>
                      {% elseif permission.type == 'modify' %}
                        <span class="label label-warning">{{ text_type_modify }}</span>
                      {% elseif permission.type == 'system' %}
                        <span class="label label-danger">{{ text_type_system }}</span>
                      {% else %}
                        <span class="label label-default">{{ text_type_other }}</span>
                      {% endif %}
                    </td>
                    <td class="text-center">
                      {% if permission.user_groups %}
                        {% for group in permission.user_groups %}
                          <span class="label label-primary">{{ group.name }}</span>
                        {% endfor %}
                      {% else %}
                        <span class="text-muted">{{ text_no_groups }}</span>
                      {% endif %}
                    </td>
                    <td class="text-center">
                      {% if permission.status %}
                        <span class="label label-success">{{ text_enabled }}</span>
                      {% else %}
                        <span class="label label-danger">{{ text_disabled }}</span>
                      {% endif %}
                    </td>
                    <td class="text-center">
                      <div class="btn-group">
                        <a href="{{ edit_link(permission.permission_id) }}" data-toggle="tooltip" title="{{ button_edit }}" class="btn btn-primary btn-sm">
                          <i class="fa fa-pencil"></i>
                        </a>
                        <a href="{{ delete_link(permission.permission_id) }}" data-toggle="tooltip" title="{{ button_delete }}" class="btn btn-danger btn-sm" onclick="return confirm('{{ text_confirm }}');">
                          <i class="fa fa-trash-o"></i>
                        </a>
                      </div>
                    </td>
                  </tr>
                  {% endfor %}
                {% else %}
                  <tr>
                    <td colspan="7" class="text-center">{{ text_no_results }}</td>
                  </tr>
                {% endif %}
              </tbody>
            </table>
          </div>
        </form>
      </div>
    </div>

    {# معلومات إضافية #}
    <div class="row">
      <div class="col-md-6">
        <div class="panel panel-info">
          <div class="panel-heading">
            <h3 class="panel-title">
              <i class="fa fa-info-circle"></i> {{ text_permission_info }}
            </h3>
          </div>
          <div class="panel-body">
            <dl class="dl-horizontal">
              <dt>{{ text_access_permissions }}:</dt>
              <dd>{{ text_access_description }}</dd>
              
              <dt>{{ text_modify_permissions }}:</dt>
              <dd>{{ text_modify_description }}</dd>
              
              <dt>{{ text_system_permissions }}:</dt>
              <dd>{{ text_system_description }}</dd>
              
              <dt>{{ text_custom_permissions }}:</dt>
              <dd>{{ text_custom_description }}</dd>
            </dl>
          </div>
        </div>
      </div>
      
      <div class="col-md-6">
        <div class="panel panel-warning">
          <div class="panel-heading">
            <h3 class="panel-title">
              <i class="fa fa-exclamation-triangle"></i> {{ text_security_notice }}
            </h3>
          </div>
          <div class="panel-body">
            <p><strong>{{ text_important }}:</strong></p>
            <ul>
              <li>{{ text_security_tip_1 }}</li>
              <li>{{ text_security_tip_2 }}</li>
              <li>{{ text_security_tip_3 }}</li>
              <li>{{ text_security_tip_4 }}</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript">
$(document).ready(function() {
    // حساب الإحصائيات
    calculateStats();
    
    // تفعيل التلميحات
    $('[data-toggle="tooltip"]').tooltip();
});

function calculateStats() {
    var permissions = {{ permissions|json_encode|raw }};
    var activeCount = 0;
    var systemCount = 0;
    var customCount = 0;
    
    permissions.forEach(function(permission) {
        if (permission.status) {
            activeCount++;
        }
        
        if (permission.type === 'system') {
            systemCount++;
        } else if (permission.type === 'other') {
            customCount++;
        }
    });
    
    $('#active-permissions').text(activeCount);
    $('#system-permissions').text(systemCount);
    $('#custom-permissions').text(customCount);
}
</script>

<style>
.huge {
    font-size: 40px;
}

.panel-green {
    border-color: #5cb85c;
}

.panel-green > .panel-heading {
    border-color: #5cb85c;
    color: white;
    background-color: #5cb85c;
}

.panel-yellow {
    border-color: #f0ad4e;
}

.panel-yellow > .panel-heading {
    border-color: #f0ad4e;
    color: white;
    background-color: #f0ad4e;
}

.panel-red {
    border-color: #d9534f;
}

.panel-red > .panel-heading {
    border-color: #d9534f;
    color: white;
    background-color: #d9534f;
}

.panel-body {
    color: white;
}

.panel-primary .panel-body,
.panel-green .panel-body,
.panel-yellow .panel-body,
.panel-red .panel-body {
    color: white;
}

.label {
    margin-right: 5px;
}

code {
    background-color: #f5f5f5;
    color: #d14;
    padding: 2px 4px;
    border-radius: 3px;
}
</style>

{{ footer }}
