{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="submit" form="extension\eta\invoice-form" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fas fa-save"></i></button>
        <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-light"><i class="fas fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    
    <div class="card">
      <div class="card-header"><i class="fas fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}chart-bar{% elif screen_type == 'settings' %}cog{% else %}pencil-alt{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="card-body">
        <form id="extension\eta\invoice-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-back">{{ text_back }}</label>
            <div class="col-sm-10">
              <input type="text" name="back" value="{{ back }}" placeholder="{{ text_back }}" id="input-back" class="form-control" />
              {% if error_back %}
                <div class="invalid-feedback">{{ error_back }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-environments">{{ text_environments }}</label>
            <div class="col-sm-10">
              <input type="text" name="environments" value="{{ environments }}" placeholder="{{ text_environments }}" id="input-environments" class="form-control" />
              {% if error_environments %}
                <div class="invalid-feedback">{{ error_environments }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-eta_auto_receipt">{{ text_eta_auto_receipt }}</label>
            <div class="col-sm-10">
              <input type="text" name="eta_auto_receipt" value="{{ eta_auto_receipt }}" placeholder="{{ text_eta_auto_receipt }}" id="input-eta_auto_receipt" class="form-control" />
              {% if error_eta_auto_receipt %}
                <div class="invalid-feedback">{{ error_eta_auto_receipt }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-eta_auto_send">{{ text_eta_auto_send }}</label>
            <div class="col-sm-10">
              <input type="text" name="eta_auto_send" value="{{ eta_auto_send }}" placeholder="{{ text_eta_auto_send }}" id="input-eta_auto_send" class="form-control" />
              {% if error_eta_auto_send %}
                <div class="invalid-feedback">{{ error_eta_auto_send }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-eta_client_id">{{ text_eta_client_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="eta_client_id" value="{{ eta_client_id }}" placeholder="{{ text_eta_client_id }}" id="input-eta_client_id" class="form-control" />
              {% if error_eta_client_id %}
                <div class="invalid-feedback">{{ error_eta_client_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-eta_client_secret">{{ text_eta_client_secret }}</label>
            <div class="col-sm-10">
              <input type="text" name="eta_client_secret" value="{{ eta_client_secret }}" placeholder="{{ text_eta_client_secret }}" id="input-eta_client_secret" class="form-control" />
              {% if error_eta_client_secret %}
                <div class="invalid-feedback">{{ error_eta_client_secret }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-eta_environment">{{ text_eta_environment }}</label>
            <div class="col-sm-10">
              <input type="text" name="eta_environment" value="{{ eta_environment }}" placeholder="{{ text_eta_environment }}" id="input-eta_environment" class="form-control" />
              {% if error_eta_environment %}
                <div class="invalid-feedback">{{ error_eta_environment }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="invalid-feedback">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-logs">{{ text_logs }}</label>
            <div class="col-sm-10">
              <input type="text" name="logs" value="{{ logs }}" placeholder="{{ text_logs }}" id="input-logs" class="form-control" />
              {% if error_logs %}
                <div class="invalid-feedback">{{ error_logs }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-pending_orders">{{ text_pending_orders }}</label>
            <div class="col-sm-10">
              <input type="text" name="pending_orders" value="{{ pending_orders }}" placeholder="{{ text_pending_orders }}" id="input-pending_orders" class="form-control" />
              {% if error_pending_orders %}
                <div class="invalid-feedback">{{ error_pending_orders }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-queue_items">{{ text_queue_items }}</label>
            <div class="col-sm-10">
              <input type="text" name="queue_items" value="{{ queue_items }}" placeholder="{{ text_queue_items }}" id="input-queue_items" class="form-control" />
              {% if error_queue_items %}
                <div class="invalid-feedback">{{ error_queue_items }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-statistics">{{ text_statistics }}</label>
            <div class="col-sm-10">
              <input type="text" name="statistics" value="{{ statistics }}" placeholder="{{ text_statistics }}" id="input-statistics" class="form-control" />
              {% if error_statistics %}
                <div class="invalid-feedback">{{ error_statistics }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_list">{{ text_text_list }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_list" value="{{ text_list }}" placeholder="{{ text_text_list }}" id="input-text_list" class="form-control" />
              {% if error_text_list %}
                <div class="invalid-feedback">{{ error_text_list }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="invalid-feedback">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}