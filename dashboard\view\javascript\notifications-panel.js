/**
 * AYM ERP - مركز الإشعارات المتطور
 * "عينك على النظام" - Notifications Panel JavaScript
 * 
 * @package    AYM ERP
 * <AUTHOR> Team
 * @copyright  2025 AYM ERP
 * @license    Commercial
 * @version    2.0.0
 */

(function($) {
    'use strict';

    // متغيرات النظام العامة
    window.AYMNotifications = {
        // إعدادات النظام
        settings: {
            autoRefreshEnabled: true,
            soundNotificationsEnabled: true,
            desktopNotificationsEnabled: false,
            refreshInterval: 30000, // 30 ثانية
            apiEndpoint: 'index.php?route=common/dashboard/',
            userToken: ''
        },
        
        // متغيرات التشغيل
        runtime: {
            refreshTimer: null,
            lastUpdateTime: null,
            isInitialized: false,
            activeTab: 'all'
        },
        
        // ذاكرة التخزين المؤقت
        cache: {
            notifications: {},
            indicators: {},
            systemStatus: {}
        }
    };

    /**
     * تهيئة النظام
     */
    function initializeNotificationSystem() {
        if (AYMNotifications.runtime.isInitialized) {
            return;
        }

        console.log('🔔 تهيئة مركز الإشعارات المتطور...');
        
        // تحميل الإعدادات المحفوظة
        loadSavedSettings();
        
        // تهيئة معالجات الأحداث
        initEventHandlers();
        
        // تحميل البيانات الأولية
        loadHeaderData();
        
        // بدء التحديث التلقائي
        if (AYMNotifications.settings.autoRefreshEnabled) {
            startAutoRefresh();
        }
        
        // طلب إذن الإشعارات
        requestNotificationPermission();
        
        // تحديث الوقت المباشر
        updateLiveTime();
        setInterval(updateLiveTime, 1000);
        
        AYMNotifications.runtime.isInitialized = true;
        console.log('✅ تم تهيئة مركز الإشعارات بنجاح');
    }

    /**
     * تحميل الإعدادات المحفوظة
     */
    function loadSavedSettings() {
        try {
            const savedSettings = localStorage.getItem('aym_notification_settings');
            if (savedSettings) {
                const settings = JSON.parse(savedSettings);
                $.extend(AYMNotifications.settings, settings);
            }
        } catch (error) {
            console.warn('تعذر تحميل الإعدادات المحفوظة:', error);
        }
    }

    /**
     * حفظ الإعدادات
     */
    function saveSettings() {
        try {
            localStorage.setItem('aym_notification_settings', JSON.stringify(AYMNotifications.settings));
        } catch (error) {
            console.warn('تعذر حفظ الإعدادات:', error);
        }
    }

    /**
     * تهيئة معالجات الأحداث
     */
    function initEventHandlers() {
        // النقر على الإشعارات
        $(document).on('click', '.notification-item', function() {
            const notificationId = $(this).data('notification-id');
            if (notificationId) {
                markNotificationAsRead(notificationId);
            }
        });

        // النقر على المؤشرات السريعة
        $(document).on('click', '.indicator-item', function() {
            const type = $(this).data('type');
            handleIndicatorClick(type);
        });

        // أزرار الهيدر
        $('#refresh-notifications').on('click', function() {
            const $icon = $(this).find('i');
            $icon.addClass('fa-spin');
            
            loadHeaderData().finally(() => {
                setTimeout(() => {
                    $icon.removeClass('fa-spin');
                }, 1000);
            });
        });

        $('#notification-settings').on('click', function() {
            openNotificationSettings();
        });

        // أزرار الفوتر
        $('#mark-all-read').on('click', function() {
            markAllNotificationsAsRead();
        });

        $('#clear-read').on('click', function() {
            clearReadNotifications();
        });

        $('#view-all-notifications').on('click', function() {
            const url = AYMNotifications.settings.apiEndpoint.replace('header/', 'notifications') + 
                       '&user_token=' + AYMNotifications.settings.userToken;
            window.open(url, '_blank');
        });

        // مفاتيح التبديل
        $('#auto-refresh-toggle').on('change', function() {
            AYMNotifications.settings.autoRefreshEnabled = $(this).is(':checked');
            
            if (AYMNotifications.settings.autoRefreshEnabled) {
                startAutoRefresh();
                $('#auto-refresh-status').text('مفعل');
            } else {
                stopAutoRefresh();
                $('#auto-refresh-status').text('معطل');
            }
            
            saveSettings();
        });

        $('#sound-notifications-toggle').on('change', function() {
            AYMNotifications.settings.soundNotificationsEnabled = $(this).is(':checked');
            saveSettings();
        });

        $('#desktop-notifications-toggle').on('change', function() {
            AYMNotifications.settings.desktopNotificationsEnabled = $(this).is(':checked');
            saveSettings();
        });

        // تبديل التبويبات
        $('.panel-tabs a[data-toggle="tab"]').on('shown.bs.tab', function(e) {
            const tabType = $(e.target).data('tab');
            AYMNotifications.runtime.activeTab = tabType;
            loadTabData(tabType);
        });

        // إغلاق البانل عند النقر خارجه
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.unified-notifications-menu').length) {
                $('.unified-notifications-menu').removeClass('open');
            }
        });
    }

    /**
     * تحميل بيانات الهيدر
     */
    function loadHeaderData() {
        return new Promise((resolve, reject) => {
            $.ajax({
                url: AYMNotifications.settings.apiEndpoint + 'getHeaderData',
                type: 'GET',
                data: {
                    user_token: AYMNotifications.settings.userToken
                },
                dataType: 'json',
                timeout: 10000,
                success: function(response) {
                    if (response.success) {
                        updateNotifications(response.data.notifications);
                        updateSystemIndicators(response.data.indicators);
                        updateSystemStatus(response.data.system_status);
                        updateLastUpdateTime();
                        
                        // تشغيل صوت التنبيه للإشعارات الجديدة
                        if (AYMNotifications.settings.soundNotificationsEnabled && 
                            response.data.new_notifications > 0) {
                            playNotificationSound();
                        }
                        
                        // إشعارات سطح المكتب
                        if (AYMNotifications.settings.desktopNotificationsEnabled && 
                            response.data.critical_notifications > 0) {
                            showDesktopNotification(response.data.critical_notifications);
                        }
                        
                        // تحديث الذاكرة المؤقتة
                        AYMNotifications.cache = response.data;
                        AYMNotifications.runtime.lastUpdateTime = new Date();
                        
                        resolve(response.data);
                    } else {
                        console.error('خطأ في تحميل بيانات الهيدر:', response.error);
                        reject(response.error);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('خطأ في الاتصال:', error);
                    console.error('Response Text:', xhr.responseText);

                    // محاولة تحليل الاستجابة كـ JSON
                    try {
                        var response = JSON.parse(xhr.responseText);
                        if (response.error) {
                            console.error('خطأ من الخادم:', response.error);
                        }
                    } catch (e) {
                        console.error('استجابة غير صحيحة من الخادم:', xhr.responseText.substring(0, 200));
                    }

                    showConnectionError(error);
                    reject(error);
                }
            });
        });
    }

    /**
     * تحديث الإشعارات
     */
    function updateNotifications(notificationsData) {
        if (!notificationsData) return;
        
        // تحديث العداد الرئيسي
        const totalCount = notificationsData.unread_count || 0;
        $('#unified-notifications-count').text(totalCount);
        $('#total-count-display').text(totalCount);
        
        // إظهار/إخفاء شارة الإشعارات
        if (totalCount > 0) {
            $('#unified-notifications-count').show();
        } else {
            $('#unified-notifications-count').hide();
        }
        
        // تحديث المؤشر الحرج
        const criticalCount = notificationsData.critical_count || 0;
        if (criticalCount > 0) {
            $('#critical-indicator').show();
        } else {
            $('#critical-indicator').hide();
        }
        
        // تحديث عدادات التبويبات
        if (notificationsData.stats) {
            updateTabCounts(notificationsData.stats);
        }
    }

    /**
     * تحديث عدادات التبويبات
     */
    function updateTabCounts(stats) {
        $('#all-count').text(stats.total || 0);
        $('#critical-count').text(stats.critical || 0);
        $('#approvals-count-tab').text(stats.approvals || 0);
        $('#workflow-count-tab').text(stats.workflow || 0);
        $('#documents-count-tab').text(stats.documents || 0);
        $('#security-count-tab').text(stats.security || 0);
    }

    /**
     * تحديث المؤشرات السريعة
     */
    function updateSystemIndicators(indicators) {
        if (!indicators) return;
        
        $('#system-performance').text(indicators.performance + '%');
        $('#active-users-count').text(indicators.active_users);
        $('#today-sales-amount').text(indicators.today_sales);
        $('#pending-tasks-count').text(indicators.pending_tasks);
        
        // تحديث ألوان المؤشرات حسب الحالة
        updateIndicatorColors(indicators);
    }

    /**
     * تحديث ألوان المؤشرات
     */
    function updateIndicatorColors(indicators) {
        // مؤشر الأداء
        const performanceIcon = $('.indicator-item[data-type="performance"] .indicator-icon');
        if (indicators.performance >= 90) {
            performanceIcon.css('background', 'linear-gradient(135deg, #28a745, #20c997)');
        } else if (indicators.performance >= 70) {
            performanceIcon.css('background', 'linear-gradient(135deg, #ffc107, #fd7e14)');
        } else {
            performanceIcon.css('background', 'linear-gradient(135deg, #dc3545, #e83e8c)');
        }
    }

    /**
     * تحديث حالة النظام
     */
    function updateSystemStatus(status) {
        if (!status) return;
        
        const statusIndicator = $('#system-status-indicator');
        const healthDot = $('.system-health-dot');
        
        // إزالة الفئات السابقة
        statusIndicator.find('i').removeClass('text-success text-warning text-danger');
        healthDot.removeClass('text-success text-warning text-danger');
        
        switch(status.level) {
            case 'healthy':
                statusIndicator.find('i').addClass('text-success');
                healthDot.addClass('text-success').css('background', '#28a745');
                statusIndicator.find('span').text('النظام يعمل بكفاءة');
                break;
            case 'warning':
                statusIndicator.find('i').addClass('text-warning');
                healthDot.addClass('text-warning').css('background', '#ffc107');
                statusIndicator.find('span').text('تحذير: ' + status.message);
                break;
            case 'critical':
                statusIndicator.find('i').addClass('text-danger');
                healthDot.addClass('text-danger').css('background', '#dc3545');
                statusIndicator.find('span').text('خطر: ' + status.message);
                break;
        }
    }

    /**
     * تحديث وقت آخر تحديث
     */
    function updateLastUpdateTime() {
        $('#last-update-time').text('الآن');
        
        // تحديث تدريجي للوقت
        setTimeout(() => {
            $('#last-update-time').text('منذ ثانية');
        }, 1000);
    }

    /**
     * تحديث الوقت المباشر
     */
    function updateLiveTime() {
        const now = new Date();
        const timeString = now.toLocaleTimeString('ar-EG', {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
        
        // تحديث أي عنصر يعرض الوقت
        $('.live-time').text(timeString);
    }

    /**
     * بدء التحديث التلقائي
     */
    function startAutoRefresh() {
        stopAutoRefresh(); // إيقاف أي تحديث سابق
        
        AYMNotifications.runtime.refreshTimer = setInterval(() => {
            loadHeaderData().catch(error => {
                console.warn('فشل التحديث التلقائي:', error);
            });
        }, AYMNotifications.settings.refreshInterval);
        
        console.log('🔄 تم بدء التحديث التلقائي');
    }

    /**
     * إيقاف التحديث التلقائي
     */
    function stopAutoRefresh() {
        if (AYMNotifications.runtime.refreshTimer) {
            clearInterval(AYMNotifications.runtime.refreshTimer);
            AYMNotifications.runtime.refreshTimer = null;
        }
        
        console.log('⏸️ تم إيقاف التحديث التلقائي');
    }

    /**
     * عرض خطأ الاتصال
     */
    function showConnectionError(error) {
        const errorMessage = error || 'خطأ في الاتصال بالخادم';

        // إظهار رسالة خطأ في الهيدر
        const errorAlert = `
            <div class="alert alert-danger alert-dismissible connection-error" style="margin: 5px; padding: 8px;">
                <button type="button" class="close" data-dismiss="alert">&times;</button>
                <i class="fa fa-exclamation-triangle"></i>
                <strong>خطأ في الاتصال:</strong> ${errorMessage}
                <button class="btn btn-xs btn-primary" onclick="AYMNotifications.init()" style="margin-left: 10px;">
                    <i class="fa fa-refresh"></i> إعادة المحاولة
                </button>
            </div>
        `;

        // إضافة التنبيه للهيدر
        if ($('.connection-error').length === 0) {
            $('#header-notifications').prepend(errorAlert);
        }

        // إخفاء التنبيه تلقائياً بعد 10 ثوان
        setTimeout(() => {
            $('.connection-error').fadeOut();
        }, 10000);
    }

    // تهيئة النظام عند تحميل الصفحة
    $(document).ready(function() {
        // تعيين رمز المستخدم من المتغير العام
        if (typeof user_token !== 'undefined') {
            AYMNotifications.settings.userToken = user_token;
        }
        
        // بدء التهيئة
        initializeNotificationSystem();
    });

    // تصدير الوظائف للاستخدام العام
    window.AYMNotifications.init = initializeNotificationSystem;
    window.AYMNotifications.loadData = loadHeaderData;
    window.AYMNotifications.startRefresh = startAutoRefresh;
    window.AYMNotifications.stopRefresh = stopAutoRefresh;

})(jQuery);
