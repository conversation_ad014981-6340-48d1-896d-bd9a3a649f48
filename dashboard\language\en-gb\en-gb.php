<?php
// Locale
$_['code']                          = 'en';
$_['direction']                     = 'ltr';
$_['date_format_short']             = 'd/m/Y';
$_['date_format_long']              = 'l dS F Y';
$_['time_format']                   = 'h:i:s A';
$_['datetime_format']               = 'd/m/Y H:i:s';
$_['decimal_point']                 = '.';
$_['thousand_point']                = ',';
$_['text_opening_balance_debit'] = 'Opening Balance Debit';
$_['text_opening_balance_credit'] = 'Opening Balance Credit';
$_['text_closing_balance_debit'] = 'Closing Balance Debit';
$_['text_closing_balance_credit'] = 'Closing Balance Credit';
$_['text_period_debit'] = 'Period Debit';
$_['text_period_credit'] = 'Period Credit';
$_['text_trial_balance'] = 'Trial Balance';
$_['text_income_statement']           		    = 'Income Statement';
$_['text_accounts'] = 'General Accounts';
$_['text_eta'] = 'ETA';
$_['text_eta_codes'] = 'Codes';
$_['text_eta_invoicing'] = 'E-Invoice';
$_['text_eta_receipt'] = 'E-Receipt';
$_['text_store_settingx']           		    = 'Online Store Setting';


$_['text_banks'] = 'Banks';
$_['text_cash'] = 'Cash';
$_['text_partners'] = 'Partners';




$_['text_balance_sheet'] = 'Balance Sheet'; // Select

$_['column_gpc_code'] = 'GPC Code'; // GPC Code
$_['column_product_id'] = 'Product ID'; // Product ID
$_['column_name'] = 'Name'; // Name
$_['column_egs_code'] = 'EGS Code'; // EGS Code
$_['column_eta_status'] = 'ETA Status'; // ETA Status
$_['column_action'] = 'Action'; // Action
$_['entry_name'] = 'Name'; // Name
$_['entry_egs'] = 'EGS Code'; // EGS Code
$_['entry_product_id'] = 'Product ID'; // Product ID
$_['entry_select_gpc'] = 'Select GPC Code'; // Select GPC Code
$_['text_select'] = 'Select'; // Select
$_['button_search'] = 'Search'; // Search
$_['text_no_results'] = 'No results!'; // No results

$_['text_to_eta'] = 'Send to ETA'; // Select

$_['text_all_categories'] = 'All categories'; // Select

$_['text_category'] = 'Category'; // Select

// Wallet
$_['text_wallet'] = 'Wallet';
$_['text_balance'] = 'Available Balance';
$_['text_transactions'] = 'Transaction History';

// Subscriptions
$_['text_subscription'] = 'Subscription';
$_['text_s_balance'] = 'Available Balance';
$_['text_transactions'] = 'Transaction History';

// Marketing
$_['text_google_tag'] = 'Google Tag Settings';
$_['text_whatsapp'] = 'WhatsApp Marketing';
$_['text_telegram'] = 'Telegram Marketing';

// WhatsApp Marketing Items
$_['text_whatsapp_settings'] = 'WhatsApp Settings';
$_['text_whatsapp_order_confirmation'] = 'Order Confirmation Messages';
$_['text_whatsapp_promotional'] = 'Promotional Messages';
$_['text_whatsapp_templates'] = 'Message Templates';

// Telegram Marketing Items
$_['text_telegram_settings'] = 'Telegram Settings';
$_['text_telegram_order_confirmation'] = 'Order Confirmation Messages';
$_['text_telegram_promotional'] = 'Promotional Messages';
$_['text_telegram_templates'] = 'Message Templates';


// Text
$_['text_hr'] = 'Human Resources';
$_['text_employee'] = 'Employees';
$_['text_payroll'] = 'Payroll';
$_['text_attendance'] = 'Attendance';
$_['text_leave'] = 'Leave';

$_['text_crm'] = 'CRM';
$_['text_lead'] = 'Leads';
$_['text_opportunity'] = 'Opportunities';
$_['text_campaign'] = 'Campaigns';

$_['text_project_management'] = 'Project Management';
$_['text_project'] = 'Projects';
$_['text_task'] = 'Tasks';
$_['text_timesheet'] = 'Timesheets';

$_['text_purchasing'] = 'Purchasing';
$_['text_supplier'] = 'Suppliers';
$_['text_purchase_order'] = 'Purchase Orders';
$_['text_rfq'] = 'RFQs';

$_['text_inventory'] = 'Inventory';
$_['text_warehouse'] = 'Warehouses';
$_['text_stock_transfer'] = 'Stock Transfers';
$_['text_stock_adjustment'] = 'Stock Adjustments';
$_['text_stock_count'] = 'Stock Counts';

$_['text_manufacturing'] = 'Manufacturing';
$_['text_bom'] = 'BOM';
$_['text_work_order'] = 'Work Orders';


$_['text_chart_accounts'] = 'Chart of Accounts';
$_['entry_account_code']                = 'Account Code';
$_['text_journal_accounts']                = 'Journals';
$_['column_thedate']                = 'Date';
$_['column_thedescription']                = 'Entry Description';
$_['text_journal_entriesbalance']                = 'Entires Balance';
$_['new_journal_entry']                = 'New Journal entry';
$_['balance_status']                = 'Balance Status';
$_['text_attachment']                = 'Attachment';
$_['text_from']                = 'From';
$_['text_to']                = 'To';
$_['text_refnum'] = 'Reference number';
$_['text_store_text']                = 'Online Store';
$_['text_pages']                = 'Information Pages';
$_['error_date_required']       = 'Date field is required!';
$_['error_description_required'] = 'Description field is required!';
$_['error_entries_required']    = 'Both debit and credit entries must be provided!';
$_['error_unbalanced']          = 'The entries are not balanced!';
$_['error_form']                = 'There are errors in the form. Please check and correct them!';
$_['text_existing_attachments']                       = 'Existing Attachments';
$_['text_new_attachments']                       = 'New Attachments';
$_['text_show_cancelled']                       = 'Cancelled';
$_['text_is_cancelled']                       = 'Cancelled';
$_['confirm_cancelled']                       = 'Are you sure you want to cancel the selected journals?';
$_['cancelled_please_select']                       = 'Please select journals to cancel.';
$_['failed_cancelled']                       = 'Failed to cancel journal ID';
$_['column_print_date']                       = 'Print Date';
$_['text_notes']                       = 'Notes';
$_['column_journal_total']                       = 'Total';
$_['entry_audit_by']                 = 'Audited By';
$_['entry_last_edit_by']             = 'Last Edited By';
$_['entry_audit_date']               = 'Audit Date';
$_['text_journal_id']               = 'Journal ID';
$_['text_journal_type']             = 'Journal Type';
$_['text_created_at']               = 'Created At';
$_['text_updated_at']               = 'Updated At';
$_['text_is_cancelled']             = 'Cancelled';
$_['text_last_edited_by']           = 'Last Edited By';
$_['text_audited_by']               = 'Audited';
$_['text_audit_date']               = 'Audit Date';
$_['text_status_j']               = 'Status';
$_['text_single_account'] = 'Print one account statement';
$_['text_account_range'] = 'Print a range of accounts from to';
$_['text_select_mode'] = 'Select print mode';
$_['text_account']            = 'Select Account';
$_['text_added_by']               = 'Added By';
$_['text_manual']               = 'Manual';
$_['text_automatic']               = 'Automatic';
$_['text_account_statement']       = 'Account Statement';
$_['text_date']                    = 'Date';
$_['text_account_number']          = 'Account Number';
$_['text_statement']               = 'Statement';
$_['text_debit']                   = 'Debit';
$_['text_credit']                  = 'Credit';
$_['text_balance']                 = 'Balance';
$_['text_total']                   = 'Total';
$_['text_printed_by']              = 'Printed by';
$_['text_print_date']              = 'Print Date';
$_['text_period_from_to']          = 'For the period';
$_['text_no_period']               = 'No period specified';

$_['text_account_start']     = 'Account From';
$_['text_account_end']       = 'Account To';
$_['text_date_start']        = 'Date Start';
$_['text_date_end']          = 'Date End';
$_['button_submit']          = 'Submit';
$_['text_no_results']        = 'No results found!';


$_['column_whoprint']               = 'Printed by';
$_['column_print_date']             = 'Print Date';
$_['text_journal_id']               = 'Journal entry ID';
$_['text_account_code']             = 'Account Code';
$_['text_account_name']             = 'Account Name';
$_['entry_dedit']                   = 'Debit';
$_['entry_credit']                  = 'Credit';
$_['text_journal_total']            = 'Total';
$_['text_bbalance']               = 'beginning balance';
$_['text_ebalance']               = 'End balance';
$_['text_entry_j']               = 'Entry ';
$_['text_print_statement']               = 'Print Account Statement';
$_['text_trial_balance']               = 'Trail Balance';

// Text
$_['text_yes']                      = 'Yes';
$_['text_no']                       = 'No';
$_['text_enabled']                  = 'Enabled';
$_['text_disabled']                 = 'Disabled';
$_['text_none']                     = ' --- None --- ';
$_['text_select']                   = ' --- Please Select --- ';
$_['text_select_all']               = 'Select All';
$_['text_unselect_all']             = 'Unselect All';
$_['text_all_zones']                = 'All Zones';
$_['text_default']                  = ' <b>(Default)</b>';
$_['text_close']                    = 'Close';
$_['text_pagination']               = 'Showing %d to %d of %d (%d Pages)';
$_['text_loading']                  = 'Loading...';
$_['text_no_results']               = 'No results!';
$_['text_confirm']                  = 'Are you sure?';
$_['text_home']                     = 'Home';

// Button
$_['button_add']                    = 'Add New';
$_['button_delete']                 = 'Delete';
$_['button_save']                   = 'Save';
$_['button_cancel']                 = 'Cancel';
$_['button_cancel_recurring']       = 'Cancel Recurring Payments';
$_['button_continue']               = 'Continue';
$_['button_clear']                  = 'Clear';
$_['button_close']                  = 'Close';
$_['button_enable']                 = 'Enable';
$_['button_disable']                = 'Disable';
$_['button_filter']                 = 'Filter';
$_['button_send']                   = 'Send';
$_['button_edit']                   = 'Edit';
$_['button_copy']                   = 'Copy';
$_['button_back']                   = 'Back';
$_['button_remove']                 = 'Remove';
$_['button_refresh']                = 'Refresh';
$_['button_export']                 = 'Export';
$_['button_import']                 = 'Import';
$_['button_download']               = 'Download';
$_['button_rebuild']                = 'Rebuild';
$_['button_upload']                 = 'Upload';
$_['button_submit']                 = 'Submit';
$_['button_invoice_print']          = 'Print Invoice';
$_['button_shipping_print']         = 'Print Shipping List';
$_['button_address_add']            = 'Add Address';
$_['button_attribute_add']          = 'Add Attribute';
$_['button_banner_add']             = 'Add Banner';
$_['button_custom_field_value_add'] = 'Add Custom Field';
$_['button_product_add']            = 'Add Product';
$_['button_filter_add']             = 'Add Filter';
$_['button_option_add']             = 'Add Option';
$_['button_option_value_add']       = 'Add Option Value';
$_['button_recurring_add']          = 'Add Recurring';
$_['button_discount_add']           = 'Add Discount';
$_['button_special_add']            = 'Add Special';
$_['button_image_add']              = 'Add Image';
$_['button_geo_zone_add']           = 'Add Geo Zone';
$_['button_history_add']            = 'Add History';
$_['button_translation']            = 'Load Default Translation';
$_['button_translation_add']        = 'Add Translation';
$_['button_transaction_add']        = 'Add Transaction';
$_['button_route_add']              = 'Add Route';
$_['button_rule_add']               = 'Add Rule';
$_['button_module_add']             = 'Add Module';
$_['button_link_add']               = 'Add Link';
$_['button_approve']                = 'Approve';
$_['button_deny']                   = 'Deny';
$_['button_reset']                  = 'Reset';
$_['button_generate']               = 'Generate';
$_['button_voucher_add']            = 'Add Voucher';
$_['button_reward_add']             = 'Add Reward Points';
$_['button_reward_remove']          = 'Remove Reward Points';
$_['button_commission_add']         = 'Add Commission';
$_['button_commission_remove']      = 'Remove Commission';
$_['button_credit_add']             = 'Add Credit';
$_['button_credit_remove']          = 'Remove Credit';
$_['button_ip_add']                 = 'Add IP';
$_['button_parent']                 = 'Parent';
$_['button_folder']                 = 'New Folder';
$_['button_search']                 = 'Search';
$_['button_view']                   = 'View';
$_['button_install']                = 'Install';
$_['button_uninstall']              = 'Uninstall';
$_['button_link']                   = 'Link';
$_['button_currency']               = 'Refresh Currency Values';
$_['button_apply']                  = 'Apply';
$_['button_category_add']           = 'Add Category';
$_['button_order']                  = 'View Order';
$_['button_order_recurring']        = 'View Recurring Order';
$_['button_buy']                    = 'Buy';
$_['button_developer']              = 'Developer Setting'; 

// Tab
$_['tab_affiliate']                 = 'Affiliate';
$_['tab_address']                   = 'Address';
$_['tab_additional']                = 'Additional';
$_['tab_admin']                     = 'Admin';
$_['tab_attribute']                 = 'Attribute';
$_['tab_customer']                  = 'Customer Details';
$_['tab_comment']                   = 'Comment';
$_['tab_data']                      = 'Data';
$_['tab_description']               = 'Description';
$_['tab_design']                    = 'Design';
$_['tab_discount']                  = 'Discount';
$_['tab_documentation']             = 'Documentation';
$_['tab_general']                   = 'General';
$_['tab_history']                   = 'History';
$_['tab_ftp']                       = 'FTP';
$_['tab_ip']                        = 'IP Addresses';
$_['tab_links']                     = 'Links';
$_['tab_log']                       = 'Log';
$_['tab_image']                     = 'Image';
$_['tab_option']                    = 'Option';
$_['tab_server']                    = 'Server';
$_['tab_seo']                       = 'SEO';
$_['tab_store']                     = 'Store';
$_['tab_special']                   = 'Special';
$_['tab_session']                   = 'Session';
$_['tab_local']                     = 'Local';
$_['tab_mail']                      = 'Mail';
$_['tab_module']                    = 'Module';
$_['tab_payment']                   = 'Payment Details';
$_['tab_product']                   = 'Products';
$_['tab_reward']                    = 'Reward Points';
$_['tab_shipping']                  = 'Shipping Details';
$_['tab_total']                     = 'Totals';
$_['tab_transaction']               = 'Transactions';
$_['tab_voucher']                   = 'Vouchers';
$_['tab_sale']                      = 'Sales';
$_['tab_marketing']                 = 'Marketing';
$_['tab_online']                    = 'People Online';
$_['tab_activity']                  = 'Recent Activity';
$_['tab_recurring']                 = 'Recurring';
$_['tab_action']                    = 'Action';
$_['tab_google']                    = 'Google';

// Error
$_['error_exception']               = 'Error Code(%s): %s in %s on line %s';
$_['error_upload_1']                = 'Warning: The uploaded file exceeds the upload_max_filesize directive in php.ini!';
$_['error_upload_2']                = 'Warning: The uploaded file exceeds the MAX_FILE_SIZE directive that was specified in the HTML form!';
$_['error_upload_3']                = 'Warning: The uploaded file was only partially uploaded!';
$_['error_upload_4']                = 'Warning: No file was uploaded!';
$_['error_upload_6']                = 'Warning: Missing a temporary folder!';
$_['error_upload_7']                = 'Warning: Failed to write file to disk!';
$_['error_upload_8']                = 'Warning: File upload stopped by extension!';
$_['error_upload_999']              = 'Warning: No error code available!';
$_['error_curl']                    = 'CURL: Error Code(%s): %s';

/* When doing translations only include the matching language code */

// Summernote
//$_['summernote']                    = 'ar-AR';
//$_['summernote']                    = 'bg-BG';
//$_['summernote']                    = 'ca-ES';
//$_['summernote']                    = 'cs-CZ';
//$_['summernote']                    = 'da-DK';
//$_['summernote']                    = 'de-DE';
//$_['summernote']                    = 'es-ES';
//$_['summernote']                    = 'es-EU';
//$_['summernote']                    = 'fa-IR';
//$_['summernote']                    = 'fi-FI';
//$_['summernote']                    = 'fr-FR';
//$_['summernote']                    = 'he-IL';
//$_['summernote']                    = 'hu-HU';
//$_['summernote']                    = 'hr-HR';
//$_['summernote']                    = 'gl-ES';
//$_['summernote']                    = 'id-ID';
//$_['summernote']                    = 'it-IT';
//$_['summernote']                    = 'ja-JP';
//$_['summernote']                    = 'ko-KR';
//$_['summernote']                    = 'lt-LT';
//$_['summernote']                    = 'lt-LV';
//$_['summernote']                    = 'nb-NO';
//$_['summernote']                    = 'nl-NL';
//$_['summernote']                    = 'pl-PL';
//$_['summernote']                    = 'pt-BR';
//$_['summernote']                    = 'pt-PT';
//$_['summernote']                    = 'ro-RO';
//$_['summernote']                    = 'ru-RU';
//$_['summernote']                    = 'sk-SK';
//$_['summernote']                    = 'sl-SI';
//$_['summernote']                    = 'sr-RS-Latin';
//$_['summernote']                    = 'sr-RS';
//$_['summernote']                    = 'sv-SE';
//$_['summernote']                    = 'th-TH';
//$_['summernote']                    = 'tr-TR';
//$_['summernote']                    = 'uk-UA';
//$_['summernote']                    = 'vi-VN';
//$_['summernote']                    = 'zh-CN';
//$_['summernote']                    = 'zh-TW';

// Datepicker
$_['datepicker']                    = 'en-gb';
//$_['datepicker']                    = 'af';
//$_['datepicker']                    = 'ar-dz';
//$_['datepicker']                    = 'ar-kw';
//$_['datepicker']                    = 'ar-ly';
//$_['datepicker']                    = 'ar-ma';
//$_['datepicker']                    = 'ar-sa';
//$_['datepicker']                    = 'ar-tn';
//$_['datepicker']                    = 'ar';
//$_['datepicker']                    = 'az';
//$_['datepicker']                    = 'be';
//$_['datepicker']                    = 'bg';
//$_['datepicker']                    = 'bn';
//$_['datepicker']                    = 'bo';
//$_['datepicker']                    = 'br';
//$_['datepicker']                    = 'bs';
//$_['datepicker']                    = 'ca';
//$_['datepicker']                    = 'cs';
//$_['datepicker']                    = 'cv';
//$_['datepicker']                    = 'cy';
//$_['datepicker']                    = 'da';
//$_['datepicker']                    = 'de-at';
//$_['datepicker']                    = 'de-ch';
//$_['datepicker']                    = 'de';
//$_['datepicker']                    = 'dv';
//$_['datepicker']                    = 'el';
//$_['datepicker']                    = 'en-au';
//$_['datepicker']                    = 'en-ca';
//$_['datepicker']                    = 'en-gb';
//$_['datepicker']                    = 'en-ie';
//$_['datepicker']                    = 'en-nz';
//$_['datepicker']                    = 'eo';
//$_['datepicker']                    = 'es-do';
//$_['datepicker']                    = 'es';
//$_['datepicker']                    = 'et';
//$_['datepicker']                    = 'eu';
//$_['datepicker']                    = 'fa';
//$_['datepicker']                    = 'fi';
//$_['datepicker']                    = 'fo';
//$_['datepicker']                    = 'fr-ca';
//$_['datepicker']                    = 'fr-ch';
//$_['datepicker']                    = 'fr';
//$_['datepicker']                    = 'fy';
//$_['datepicker']                    = 'gd';
//$_['datepicker']                    = 'gl';
//$_['datepicker']                    = 'gom-latn';
//$_['datepicker']                    = 'he';
//$_['datepicker']                    = 'hi';
//$_['datepicker']                    = 'hr';
//$_['datepicker']                    = 'hu';
//$_['datepicker']                    = 'hy-am';
//$_['datepicker']                    = 'id';
//$_['datepicker']                    = 'is';
//$_['datepicker']                    = 'it';
//$_['datepicker']                    = 'ja';
//$_['datepicker']                    = 'jv';
//$_['datepicker']                    = 'ka';
//$_['datepicker']                    = 'kk';
//$_['datepicker']                    = 'km';
//$_['datepicker']                    = 'kn';
//$_['datepicker']                    = 'ko';
//$_['datepicker']                    = 'ky';
//$_['datepicker']                    = 'lb';
//$_['datepicker']                    = 'lo';
//$_['datepicker']                    = 'lt';
//$_['datepicker']                    = 'lv';
//$_['datepicker']                    = 'me';
//$_['datepicker']                    = 'mi';
//$_['datepicker']                    = 'mk';
//$_['datepicker']                    = 'ml';
//$_['datepicker']                    = 'mr';
//$_['datepicker']                    = 'ms-my';
//$_['datepicker']                    = 'ms';
//$_['datepicker']                    = 'my';
//$_['datepicker']                    = 'nb';
//$_['datepicker']                    = 'ne';
//$_['datepicker']                    = 'nl-be';
//$_['datepicker']                    = 'nl';
//$_['datepicker']                    = 'nn';
//$_['datepicker']                    = 'pa-in';
//$_['datepicker']                    = 'pl';
//$_['datepicker']                    = 'pt-br';
//$_['datepicker']                    = 'pt';
//$_['datepicker']                    = 'ro';
//$_['datepicker']                    = 'ru';
//$_['datepicker']                    = 'sd';
//$_['datepicker']                    = 'se';
//$_['datepicker']                    = 'si';
//$_['datepicker']                    = 'sk';
//$_['datepicker']                    = 'sl';
//$_['datepicker']                    = 'sq';
//$_['datepicker']                    = 'sr-cyrl';
//$_['datepicker']                    = 'sr';
//$_['datepicker']                    = 'ss';
//$_['datepicker']                    = 'sv';
//$_['datepicker']                    = 'sw';
//$_['datepicker']                    = 'ta';
//$_['datepicker']                    = 'te';
//$_['datepicker']                    = 'tet';
//$_['datepicker']                    = 'th';
//$_['datepicker']                    = 'tl-ph';
//$_['datepicker']                    = 'tlh';
//$_['datepicker']                    = 'tr';
//$_['datepicker']                    = 'tzl';
//$_['datepicker']                    = 'tzm-latn';
//$_['datepicker']                    = 'tzm';
//$_['datepicker']                    = 'uk';
//$_['datepicker']                    = 'ur';
//$_['datepicker']                    = 'uz-latn';
//$_['datepicker']                    = 'uz';
//$_['datepicker']                    = 'vi';
//$_['datepicker']                    = 'x-pseudo';
//$_['datepicker']                    = 'yo';
//$_['datepicker']                    = 'zh-cn';
//$_['datepicker']                    = 'zh-hk';
//$_['datepicker']                    = 'zh-tw';