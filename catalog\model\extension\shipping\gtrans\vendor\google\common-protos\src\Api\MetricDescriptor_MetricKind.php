<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/metric.proto

namespace Google\Api;

if (false) {
    /**
     * This class is deprecated. Use Google\Api\MetricDescriptor\MetricKind instead.
     * @deprecated
     */
    class MetricDescriptor_MetricKind {}
}
class_exists(MetricDescriptor\MetricKind::class);
@trigger_error('Google\Api\MetricDescriptor_MetricKind is deprecated and will be removed in the next major release. Use Google\Api\MetricDescriptor\MetricKind instead', E_USER_DEPRECATED);

