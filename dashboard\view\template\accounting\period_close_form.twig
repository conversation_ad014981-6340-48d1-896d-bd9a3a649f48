{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="submit" form="form-period-close" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fa fa-save"></i></button>
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a></div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-lock"></i> {{ text_form }}</h3>
      </div>
      <div class="panel-body">
        <div class="alert alert-info"><i class="fa fa-info-circle"></i> {{ text_close_info }}</div>
        <form action="{{ action }}" method="post" enctype="multipart/form-data" id="form-period-close" class="form-horizontal">
          <div class="form-group">
            <label class="col-sm-2 control-label">{{ entry_name }}</label>
            <div class="col-sm-10">
              <p class="form-control-static">{{ name }}</p>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label">{{ entry_date_range }}</label>
            <div class="col-sm-10">
              <p class="form-control-static">{{ start_date }} - {{ end_date }}</p>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-closing-notes">{{ entry_closing_notes }}</label>
            <div class="col-sm-10">
              <textarea name="closing_notes" rows="5" placeholder="{{ entry_closing_notes }}" id="input-closing-notes" class="form-control">{{ closing_notes }}</textarea>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label">{{ entry_create_closing_entries }}</label>
            <div class="col-sm-10">
              <label class="radio-inline">
                <input type="radio" name="create_closing_entries" value="1" {% if create_closing_entries %}checked="checked"{% endif %} /> {{ text_yes }}
              </label>
              <label class="radio-inline">
                <input type="radio" name="create_closing_entries" value="0" {% if not create_closing_entries %}checked="checked"{% endif %} /> {{ text_no }}
              </label>
              <p class="help-block">{{ help_create_closing_entries }}</p>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
{{ footer }}
