{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="button" data-toggle="tooltip" title="إعادة تعيين" class="btn btn-warning" onclick="resetConverter()">
          <i class="fa fa-refresh"></i>
        </button>
        <button type="button" data-toggle="tooltip" title="سجل التحويلات" class="btn btn-info" data-toggle="modal" data-target="#history-modal">
          <i class="fa fa-history"></i>
        </button>
        <button type="button" data-toggle="tooltip" title="التحويلات المفضلة" class="btn btn-success" data-toggle="modal" data-target="#favorites-modal">
          <i class="fa fa-star"></i>
        </button>
        <a href="{{ back }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default">
          <i class="fa fa-reply"></i>
        </a>
      </div>
      <h1>{{ heading_title }} - {{ text_conversion_calculator }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>

  <div class="container-fluid">
    <div class="row">
      <!-- حاسبة التحويل الرئيسية -->
      <div class="col-md-8">
        <div class="panel panel-primary">
          <div class="panel-heading">
            <h3 class="panel-title">
              <i class="fa fa-calculator"></i> حاسبة التحويل التفاعلية
            </h3>
          </div>
          <div class="panel-body">
            <form id="conversion-form">
              <!-- اختيار نوع الوحدة -->
              <div class="row">
                <div class="col-md-12">
                  <div class="form-group">
                    <label for="unit-type-select">نوع الوحدة:</label>
                    <select id="unit-type-select" class="form-control" onchange="loadUnitsByType()">
                      <option value="">اختر نوع الوحدة</option>
                      {% for type_key, type_name in unit_types %}
                      <option value="{{ type_key }}">{{ type_name }}</option>
                      {% endfor %}
                    </select>
                  </div>
                </div>
              </div>

              <!-- منطقة التحويل -->
              <div class="conversion-area" style="display: none;">
                <div class="row">
                  <div class="col-md-5">
                    <div class="form-group">
                      <label for="from-unit">من الوحدة:</label>
                      <select id="from-unit" class="form-control" onchange="updateConversion()">
                        <option value="">اختر الوحدة</option>
                      </select>
                    </div>

                    <div class="form-group">
                      <label for="from-quantity">الكمية:</label>
                      <div class="input-group input-group-lg">
                        <input type="number" id="from-quantity" class="form-control" placeholder="أدخل الكمية" step="0.000001" oninput="convertFromTo()" />
                        <span class="input-group-addon" id="from-unit-symbol">-</span>
                      </div>
                    </div>
                  </div>

                  <div class="col-md-2 text-center">
                    <div style="margin-top: 60px;">
                      <button type="button" class="btn btn-info btn-lg" onclick="swapUnits()" data-toggle="tooltip" title="تبديل الوحدات">
                        <i class="fa fa-exchange"></i>
                      </button>
                    </div>
                  </div>

                  <div class="col-md-5">
                    <div class="form-group">
                      <label for="to-unit">إلى الوحدة:</label>
                      <select id="to-unit" class="form-control" onchange="updateConversion()">
                        <option value="">اختر الوحدة</option>
                      </select>
                    </div>

                    <div class="form-group">
                      <label for="to-quantity">النتيجة:</label>
                      <div class="input-group input-group-lg">
                        <input type="number" id="to-quantity" class="form-control" placeholder="النتيجة" step="0.000001" oninput="convertToFrom()" />
                        <span class="input-group-addon" id="to-unit-symbol">-</span>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- معلومات التحويل -->
                <div class="row" id="conversion-info" style="display: none;">
                  <div class="col-md-12">
                    <div class="alert alert-info">
                      <h5><i class="fa fa-info-circle"></i> معلومات التحويل:</h5>
                      <p id="conversion-formula">معادلة التحويل: -</p>
                      <p id="conversion-factor">معامل التحويل: -</p>
                      <p id="conversion-accuracy">دقة التحويل: عالية</p>
                    </div>
                  </div>
                </div>

                <!-- أزرار الإجراءات -->
                <div class="row">
                  <div class="col-md-12 text-center">
                    <button type="button" class="btn btn-success btn-lg" onclick="saveToFavorites()">
                      <i class="fa fa-star"></i> حفظ في المفضلة
                    </button>
                    <button type="button" class="btn btn-info btn-lg" onclick="addToHistory()">
                      <i class="fa fa-history"></i> حفظ في السجل
                    </button>
                    <button type="button" class="btn btn-warning btn-lg" onclick="resetConverter()">
                      <i class="fa fa-refresh"></i> إعادة تعيين
                    </button>
                  </div>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>

      <!-- الشريط الجانبي -->
      <div class="col-md-4">
        <!-- التحويلات السريعة -->
        <div class="panel panel-success">
          <div class="panel-heading">
            <h3 class="panel-title">
              <i class="fa fa-bolt"></i> التحويلات السريعة
            </h3>
          </div>
          <div class="panel-body">
            <div id="quick-conversions">
              <p class="text-muted text-center">اختر نوع الوحدة لعرض التحويلات السريعة</p>
            </div>
          </div>
        </div>

        <!-- التحويلات الشائعة -->
        <div class="panel panel-info">
          <div class="panel-heading">
            <h3 class="panel-title">
              <i class="fa fa-fire"></i> التحويلات الشائعة
            </h3>
          </div>
          <div class="panel-body">
            <div class="common-conversions">
              <div class="conversion-item" onclick="setQuickConversion('weight', 'kilogram', 'gram')">
                <strong>كيلوجرام ← جرام</strong>
                <small class="text-muted">1 كجم = 1000 جم</small>
              </div>
              <div class="conversion-item" onclick="setQuickConversion('length', 'meter', 'centimeter')">
                <strong>متر ← سنتيمتر</strong>
                <small class="text-muted">1 م = 100 سم</small>
              </div>
              <div class="conversion-item" onclick="setQuickConversion('volume', 'liter', 'milliliter')">
                <strong>لتر ← مليلتر</strong>
                <small class="text-muted">1 لتر = 1000 مل</small>
              </div>
              <div class="conversion-item" onclick="setQuickConversion('quantity', 'dozen', 'piece')">
                <strong>دستة ← قطعة</strong>
                <small class="text-muted">1 دستة = 12 قطعة</small>
              </div>
            </div>
          </div>
        </div>

        <!-- حاسبة متقدمة -->
        <div class="panel panel-warning">
          <div class="panel-heading">
            <h3 class="panel-title">
              <i class="fa fa-cogs"></i> حاسبة متقدمة
            </h3>
          </div>
          <div class="panel-body">
            <div class="form-group">
              <label>العملية:</label>
              <select id="advanced-operation" class="form-control">
                <option value="multiply">ضرب</option>
                <option value="divide">قسمة</option>
                <option value="add">جمع</option>
                <option value="subtract">طرح</option>
              </select>
            </div>

            <div class="form-group">
              <label>المعامل:</label>
              <input type="number" id="advanced-factor" class="form-control" placeholder="أدخل المعامل" step="0.000001" />
            </div>

            <button type="button" class="btn btn-warning btn-block" onclick="applyAdvancedOperation()">
              <i class="fa fa-calculator"></i> تطبيق العملية
            </button>
          </div>
        </div>

        <!-- إحصائيات الاستخدام -->
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title">
              <i class="fa fa-bar-chart"></i> إحصائيات الجلسة
            </h3>
          </div>
          <div class="panel-body">
            <div class="row">
              <div class="col-xs-6">
                <div class="text-center">
                  <h4 id="total-conversions">0</h4>
                  <small>إجمالي التحويلات</small>
                </div>
              </div>
              <div class="col-xs-6">
                <div class="text-center">
                  <h4 id="favorite-conversions">0</h4>
                  <small>التحويلات المفضلة</small>
                </div>
              </div>
            </div>

            <div class="row" style="margin-top: 10px;">
              <div class="col-xs-6">
                <div class="text-center">
                  <h4 id="most-used-type">-</h4>
                  <small>النوع الأكثر استخداماً</small>
                </div>
              </div>
              <div class="col-xs-6">
                <div class="text-center">
                  <h4 id="session-time">00:00</h4>
                  <small>وقت الجلسة</small>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- نافذة سجل التحويلات -->
<div class="modal fade" id="history-modal" tabindex="-1" role="dialog">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title"><i class="fa fa-history"></i> سجل التحويلات</h4>
      </div>
      <div class="modal-body">
        <div class="table-responsive">
          <table class="table table-striped">
            <thead>
              <tr>
                <th>الوقت</th>
                <th>من</th>
                <th>إلى</th>
                <th>الكمية</th>
                <th>النتيجة</th>
                <th>إجراء</th>
              </tr>
            </thead>
            <tbody id="history-table">
              <tr>
                <td colspan="6" class="text-center text-muted">لا يوجد سجل تحويلات</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-warning" onclick="clearHistory()">
          <i class="fa fa-trash"></i> مسح السجل
        </button>
        <button type="button" class="btn btn-success" onclick="exportHistory()">
          <i class="fa fa-download"></i> تصدير
        </button>
        <button type="button" class="btn btn-default" data-dismiss="modal">إغلاق</button>
      </div>
    </div>
  </div>
</div>

<!-- نافذة التحويلات المفضلة -->
<div class="modal fade" id="favorites-modal" tabindex="-1" role="dialog">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title"><i class="fa fa-star"></i> التحويلات المفضلة</h4>
      </div>
      <div class="modal-body">
        <div id="favorites-list">
          <p class="text-muted text-center">لا توجد تحويلات مفضلة</p>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-warning" onclick="clearFavorites()">
          <i class="fa fa-trash"></i> مسح المفضلة
        </button>
        <button type="button" class="btn btn-default" data-dismiss="modal">إغلاق</button>
      </div>
    </div>
  </div>
</div>

<style>
.conversion-area {
  border: 2px dashed #ddd;
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
  background: #f9f9f9;
}

.conversion-item {
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.conversion-item:hover {
  background-color: #f0f8ff;
  border-color: #337ab7;
}

.conversion-item strong {
  display: block;
  color: #333;
}

.conversion-item small {
  display: block;
  margin-top: 2px;
}

.input-group-lg .form-control {
  font-size: 18px;
  font-weight: bold;
}

.input-group-lg .input-group-addon {
  font-size: 16px;
  font-weight: bold;
  min-width: 60px;
}

#conversion-info {
  margin-top: 20px;
}

#conversion-info p {
  margin-bottom: 5px;
}

.panel-heading h3 {
  margin: 0;
}

.text-center h4 {
  margin: 5px 0;
  font-weight: bold;
}

.text-center small {
  color: #666;
  font-size: 11px;
}
</style>

<script type="text/javascript">
var conversionHistory = [];
var favoriteConversions = [];
var sessionStats = {
    totalConversions: 0,
    favoriteConversions: 0,
    mostUsedType: '',
    sessionStartTime: new Date()
};
var unitsByType = {};

$(document).ready(function() {
    $('[data-toggle="tooltip"]').tooltip();

    // تحميل البيانات المحفوظة
    loadSavedData();

    // بدء عداد الوقت
    startSessionTimer();

    // تحديث الإحصائيات
    updateSessionStats();
});

function loadUnitsByType() {
    var unitType = $('#unit-type-select').val();

    if (!unitType) {
        $('.conversion-area').hide();
        return;
    }

    // إظهار منطقة التحويل
    $('.conversion-area').show();

    // تحميل الوحدات من الخادم أو من البيانات المحفوظة
    if (unitsByType[unitType]) {
        populateUnitSelects(unitsByType[unitType]);
        loadQuickConversions(unitType);
    } else {
        $.ajax({
            url: 'index.php?route=inventory/unit_management/getUnitsByType&user_token={{ user_token }}',
            type: 'GET',
            data: { unit_type: unitType },
            dataType: 'json',
            success: function(data) {
                if (data.success) {
                    unitsByType[unitType] = data.units;
                    populateUnitSelects(data.units);
                    loadQuickConversions(unitType);
                } else {
                    showNotification(data.error || 'خطأ في تحميل الوحدات', 'error');
                }
            },
            error: function() {
                showNotification('خطأ في الاتصال بالخادم', 'error');
            }
        });
    }
}

function populateUnitSelects(units) {
    var fromSelect = $('#from-unit');
    var toSelect = $('#to-unit');

    fromSelect.empty().append('<option value="">اختر الوحدة</option>');
    toSelect.empty().append('<option value="">اختر الوحدة</option>');

    units.forEach(function(unit) {
        var option = '<option value="' + unit.unit_id + '" data-symbol="' + unit.symbol + '" data-name="' + unit.name + '">' + unit.name + ' (' + unit.symbol + ')</option>';
        fromSelect.append(option);
        toSelect.append(option);
    });
}

function loadQuickConversions(unitType) {
    var quickConversionsData = {
        'weight': [
            { from: 'كيلوجرام', to: 'جرام', factor: '1000' },
            { from: 'طن', to: 'كيلوجرام', factor: '1000' },
            { from: 'رطل', to: 'كيلوجرام', factor: '0.453592' }
        ],
        'length': [
            { from: 'متر', to: 'سنتيمتر', factor: '100' },
            { from: 'كيلومتر', to: 'متر', factor: '1000' },
            { from: 'بوصة', to: 'سنتيمتر', factor: '2.54' }
        ],
        'volume': [
            { from: 'لتر', to: 'مليلتر', factor: '1000' },
            { from: 'متر مكعب', to: 'لتر', factor: '1000' },
            { from: 'جالون', to: 'لتر', factor: '3.78541' }
        ],
        'quantity': [
            { from: 'دستة', to: 'قطعة', factor: '12' },
            { from: 'كرتونة', to: 'قطعة', factor: '24' },
            { from: 'صندوق', to: 'قطعة', factor: '100' }
        ]
    };

    var conversions = quickConversionsData[unitType] || [];
    var html = '';

    if (conversions.length > 0) {
        conversions.forEach(function(conversion) {
            html += '<div class="conversion-item" onclick="setQuickConversionByName(\'' + conversion.from + '\', \'' + conversion.to + '\')">';
            html += '<strong>' + conversion.from + ' ← ' + conversion.to + '</strong>';
            html += '<small class="text-muted">معامل التحويل: ' + conversion.factor + '</small>';
            html += '</div>';
        });
    } else {
        html = '<p class="text-muted text-center">لا توجد تحويلات سريعة لهذا النوع</p>';
    }

    $('#quick-conversions').html(html);
}

function updateConversion() {
    var fromUnit = $('#from-unit').val();
    var toUnit = $('#to-unit').val();

    if (fromUnit && toUnit) {
        // تحديث رموز الوحدات
        $('#from-unit-symbol').text($('#from-unit option:selected').data('symbol') || '-');
        $('#to-unit-symbol').text($('#to-unit option:selected').data('symbol') || '-');

        // إظهار معلومات التحويل
        $('#conversion-info').show();

        // تحديث معلومات التحويل
        updateConversionInfo(fromUnit, toUnit);

        // تحويل الكمية إذا كانت موجودة
        if ($('#from-quantity').val()) {
            convertFromTo();
        }
    } else {
        $('#conversion-info').hide();
        $('#from-unit-symbol').text('-');
        $('#to-unit-symbol').text('-');
    }
}

function convertFromTo() {
    var quantity = parseFloat($('#from-quantity').val());
    var fromUnit = $('#from-unit').val();
    var toUnit = $('#to-unit').val();

    if (isNaN(quantity) || !fromUnit || !toUnit) {
        $('#to-quantity').val('');
        return;
    }

    performConversion(quantity, fromUnit, toUnit, function(result) {
        $('#to-quantity').val(result.toFixed(6));
        sessionStats.totalConversions++;
        updateSessionStats();
    });
}

function performConversion(quantity, fromUnitId, toUnitId, callback) {
    $.ajax({
        url: 'index.php?route=inventory/unit_management/convert&user_token={{ user_token }}',
        type: 'POST',
        data: {
            quantity: quantity,
            from_unit_id: fromUnitId,
            to_unit_id: toUnitId
        },
        dataType: 'json',
        success: function(data) {
            if (data.success) {
                callback(data.converted_quantity);
            } else {
                showNotification(data.error || 'خطأ في التحويل', 'error');
            }
        },
        error: function() {
            showNotification('خطأ في الاتصال بالخادم', 'error');
        }
    });
}

function swapUnits() {
    var fromUnit = $('#from-unit').val();
    var toUnit = $('#to-unit').val();
    var fromQuantity = $('#from-quantity').val();
    var toQuantity = $('#to-quantity').val();

    // تبديل الوحدات
    $('#from-unit').val(toUnit);
    $('#to-unit').val(fromUnit);

    // تبديل الكميات
    $('#from-quantity').val(toQuantity);
    $('#to-quantity').val(fromQuantity);

    // تحديث التحويل
    updateConversion();

    showNotification('تم تبديل الوحدات', 'success');
}

function saveToFavorites() {
    var fromUnit = $('#from-unit').val();
    var toUnit = $('#to-unit').val();
    var fromUnitName = $('#from-unit option:selected').text();
    var toUnitName = $('#to-unit option:selected').text();

    if (!fromUnit || !toUnit) {
        showNotification('يرجى اختيار الوحدات أولاً', 'warning');
        return;
    }

    var favorite = {
        id: Date.now(),
        fromUnit: fromUnit,
        toUnit: toUnit,
        fromUnitName: fromUnitName,
        toUnitName: toUnitName,
        unitType: $('#unit-type-select').val(),
        dateAdded: new Date().toLocaleString('ar-EG')
    };

    favoriteConversions.push(favorite);
    sessionStats.favoriteConversions++;

    saveFavorites();
    updateSessionStats();
    updateFavoritesList();

    showNotification('تم حفظ التحويل في المفضلة', 'success');
}

function resetConverter() {
    $('#unit-type-select').val('');
    $('#from-unit').val('');
    $('#to-unit').val('');
    $('#from-quantity').val('');
    $('#to-quantity').val('');
    $('#from-unit-symbol').text('-');
    $('#to-unit-symbol').text('-');
    $('.conversion-area').hide();
    $('#conversion-info').hide();
    $('#quick-conversions').html('<p class="text-muted text-center">اختر نوع الوحدة لعرض التحويلات السريعة</p>');

    showNotification('تم إعادة تعيين الحاسبة', 'info');
}

function updateSessionStats() {
    $('#total-conversions').text(sessionStats.totalConversions);
    $('#favorite-conversions').text(sessionStats.favoriteConversions);
    $('#most-used-type').text(sessionStats.mostUsedType || '-');
}

function startSessionTimer() {
    setInterval(function() {
        var now = new Date();
        var diff = now - sessionStats.sessionStartTime;
        var minutes = Math.floor(diff / 60000);
        var seconds = Math.floor((diff % 60000) / 1000);

        $('#session-time').text(
            (minutes < 10 ? '0' : '') + minutes + ':' +
            (seconds < 10 ? '0' : '') + seconds
        );
    }, 1000);
}

function loadSavedData() {
    // تحميل السجل
    var savedHistory = localStorage.getItem('conversionHistory');
    if (savedHistory) {
        conversionHistory = JSON.parse(savedHistory);
    }

    // تحميل المفضلة
    var savedFavorites = localStorage.getItem('favoriteConversions');
    if (savedFavorites) {
        favoriteConversions = JSON.parse(savedFavorites);
        sessionStats.favoriteConversions = favoriteConversions.length;
    }
}

function showNotification(message, type) {
    var alertClass = type === 'success' ? 'alert-success' : type === 'info' ? 'alert-info' : type === 'warning' ? 'alert-warning' : 'alert-danger';
    var icon = type === 'success' ? 'fa-check-circle' : type === 'info' ? 'fa-info-circle' : type === 'warning' ? 'fa-exclamation-triangle' : 'fa-exclamation-circle';

    var notification = $('<div class="alert ' + alertClass + ' alert-dismissible" style="position: fixed; top: 70px; right: 20px; z-index: 9999; min-width: 300px;"><i class="fa ' + icon + '"></i> ' + message + '<button type="button" class="close" data-dismiss="alert">&times;</button></div>');

    $('body').append(notification);

    setTimeout(function() {
        notification.fadeOut(function() { notification.remove(); });
    }, 4000);
}
</script>

{{ footer }}
