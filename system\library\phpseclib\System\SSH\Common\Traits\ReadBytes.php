<?php

/**
 * ReadBytes trait
 *
 * PHP version 5
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2015 <PERSON>
 * @license   http://www.opensource.org/licenses/mit-license.html  MIT License
 * @link      http://phpseclib.sourceforge.net
 */

declare(strict_types=1);

namespace phpseclib3\System\SSH\Common\Traits;

use phpseclib3\Exception\RuntimeException;

/**
 * ReadBytes trait
 *
 * <AUTHOR> <<EMAIL>>
 */
trait ReadBytes
{
    /**
     * Read data
     *
     * @throws RuntimeException on connection errors
     */
    public function readBytes(int $length): string
    {
        $temp = fread($this->fsock, $length);
        if ($temp === false) {
            throw new RuntimeException('\fread() failed.');
        }
        if (strlen($temp) !== $length) {
            throw new RuntimeException("Expected $length bytes; got " . strlen($temp));
        }
        return $temp;
    }
}
