<!DOCTYPE html>
<html dir="{{ direction }}" lang="{{ lang }}" class="no-js">
<head>
<meta charset="UTF-8" />
<title>{{ heading_title }}</title>
<base href="{{ base }}" />
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
<link href="view/stylesheet/bootstrap.css" rel="stylesheet" media="screen" />
<style>
@media print {
  body { margin: 0; }
  .no-print { display: none !important; }
  .page-break { page-break-before: always; }
}

body {
  font-family: Arial, sans-serif;
  font-size: 11px;
  line-height: 1.3;
}

.header {
  text-align: center;
  margin-bottom: 30px;
  border-bottom: 2px solid #333;
  padding-bottom: 20px;
}

.company-logo {
  max-height: 80px;
  margin-bottom: 10px;
}

.company-name {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}

.report-title {
  font-size: 18px;
  font-weight: bold;
  color: #666;
  margin-bottom: 10px;
}

.report-info {
  font-size: 10px;
  color: #888;
}

.summary-box {
  background-color: #f8f9fa;
  padding: 15px;
  margin-bottom: 20px;
  border: 1px solid #dee2e6;
}

.cash-flow-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 20px;
  font-size: 10px;
}

.cash-flow-table th,
.cash-flow-table td {
  border: 1px solid #dee2e6;
  padding: 6px;
  text-align: left;
}

.cash-flow-table th {
  background-color: #f8f9fa;
  font-weight: bold;
}

.text-right {
  text-align: right;
}

.text-center {
  text-align: center;
}

.text-success {
  color: #28a745;
}

.text-warning {
  color: #ffc107;
}

.text-danger {
  color: #dc3545;
}

.text-info {
  color: #17a2b8;
}

.footer {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #dee2e6;
  font-size: 9px;
  color: #888;
}

.signature-section {
  margin-top: 40px;
  display: flex;
  justify-content: space-between;
}

.signature-box {
  width: 200px;
  text-align: center;
}

.signature-line {
  border-top: 1px solid #333;
  margin-top: 50px;
  padding-top: 5px;
}

.watermark {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(-45deg);
  font-size: 72px;
  color: rgba(0,0,0,0.05);
  z-index: -1;
  pointer-events: none;
}

.ratio-badge {
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 9px;
  font-weight: bold;
}

.ratio-excellent {
  background-color: #28a745;
  color: white;
}

.ratio-good {
  background-color: #17a2b8;
  color: white;
}

.ratio-warning {
  background-color: #ffc107;
  color: black;
}

.ratio-danger {
  background-color: #dc3545;
  color: white;
}
</style>
</head>
<body>

<!-- Watermark -->
<div class="watermark">{{ text_confidential }}</div>

<!-- Header -->
<div class="header">
  {% if company_logo %}
  <img src="{{ company_logo }}" alt="{{ company_name }}" class="company-logo">
  {% endif %}
  <div class="company-name">{{ company_name }}</div>
  <div class="report-title">{{ heading_title }}</div>
  <div class="report-info">
    {{ text_period }}: {{ date_start }} {{ text_to }} {{ date_end }} | {{ text_generated_on }}: {{ generated_date }}
  </div>
</div>

<!-- Report Information -->
<div class="summary-box">
  <h3 style="margin-top: 0;">{{ text_report_info }}</h3>
  <div style="display: flex; justify-content: space-between;">
    <div>
      <strong>{{ text_period }}:</strong> {{ date_start }} {{ text_to }} {{ date_end }}<br>
      <strong>{{ text_currency }}:</strong> {{ currency }}<br>
      <strong>{{ text_analysis_type }}:</strong> {{ text_ai_powered }}
    </div>
    <div>
      <strong>{{ text_generated_on }}:</strong> {{ generated_date }}<br>
      <strong>{{ text_generated_by }}:</strong> {{ generated_by }}<br>
      <strong>{{ text_report_type }}:</strong> {{ text_advanced_cash_flow }}
    </div>
  </div>
</div>

<!-- Cash Flow Summary -->
<h3>{{ text_cash_flow_summary }}</h3>
<table class="cash-flow-table">
  <thead>
    <tr>
      <th>{{ text_activity_type }}</th>
      <th class="text-right">{{ text_amount }}</th>
      <th class="text-right">{{ text_percentage }}</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>{{ text_operating_activities }}</td>
      <td class="text-right">{{ cash_flow_data.operating_total }}</td>
      <td class="text-right">{{ cash_flow_data.operating_percentage }}%</td>
    </tr>
    <tr>
      <td>{{ text_investing_activities }}</td>
      <td class="text-right">{{ cash_flow_data.investing_total }}</td>
      <td class="text-right">{{ cash_flow_data.investing_percentage }}%</td>
    </tr>
    <tr>
      <td>{{ text_financing_activities }}</td>
      <td class="text-right">{{ cash_flow_data.financing_total }}</td>
      <td class="text-right">{{ cash_flow_data.financing_percentage }}%</td>
    </tr>
  </tbody>
  <tfoot>
    <tr style="background-color: #e2e3e5; font-weight: bold;">
      <td>{{ text_net_cash_flow }}</td>
      <td class="text-right">{{ cash_flow_data.net_cash_flow }}</td>
      <td class="text-right">100%</td>
    </tr>
  </tfoot>
</table>

<!-- Liquidity Ratios -->
{% if liquidity_ratios %}
<h3>{{ text_liquidity_ratios }}</h3>
<table class="cash-flow-table">
  <thead>
    <tr>
      <th>{{ text_ratio_name }}</th>
      <th class="text-right">{{ text_value }}</th>
      <th class="text-center">{{ text_rating }}</th>
      <th>{{ text_interpretation }}</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>{{ text_current_ratio }}</td>
      <td class="text-right">{{ liquidity_ratios.current_ratio }}</td>
      <td class="text-center">
        <span class="ratio-badge ratio-{% if liquidity_ratios.current_ratio >= 2 %}excellent{% elseif liquidity_ratios.current_ratio >= 1 %}good{% else %}danger{% endif %}">
          {% if liquidity_ratios.current_ratio >= 2 %}{{ text_excellent }}{% elseif liquidity_ratios.current_ratio >= 1 %}{{ text_good }}{% else %}{{ text_poor }}{% endif %}
        </span>
      </td>
      <td>{{ text_current_ratio_interpretation }}</td>
    </tr>
    <tr>
      <td>{{ text_quick_ratio }}</td>
      <td class="text-right">{{ liquidity_ratios.quick_ratio }}</td>
      <td class="text-center">
        <span class="ratio-badge ratio-{% if liquidity_ratios.quick_ratio >= 1 %}excellent{% elseif liquidity_ratios.quick_ratio >= 0.5 %}good{% else %}danger{% endif %}">
          {% if liquidity_ratios.quick_ratio >= 1 %}{{ text_excellent }}{% elseif liquidity_ratios.quick_ratio >= 0.5 %}{{ text_good }}{% else %}{{ text_poor }}{% endif %}
        </span>
      </td>
      <td>{{ text_quick_ratio_interpretation }}</td>
    </tr>
    <tr>
      <td>{{ text_cash_ratio }}</td>
      <td class="text-right">{{ liquidity_ratios.cash_ratio }}</td>
      <td class="text-center">
        <span class="ratio-badge ratio-{% if liquidity_ratios.cash_ratio >= 0.2 %}excellent{% elseif liquidity_ratios.cash_ratio >= 0.1 %}good{% else %}danger{% endif %}">
          {% if liquidity_ratios.cash_ratio >= 0.2 %}{{ text_excellent }}{% elseif liquidity_ratios.cash_ratio >= 0.1 %}{{ text_good }}{% else %}{{ text_poor }}{% endif %}
        </span>
      </td>
      <td>{{ text_cash_ratio_interpretation }}</td>
    </tr>
    <tr>
      <td>{{ text_cash_coverage_days }}</td>
      <td class="text-right">{{ liquidity_ratios.cash_coverage_days }} {{ text_days }}</td>
      <td class="text-center">
        <span class="ratio-badge ratio-{% if liquidity_ratios.cash_coverage_days >= 90 %}excellent{% elseif liquidity_ratios.cash_coverage_days >= 30 %}good{% else %}danger{% endif %}">
          {% if liquidity_ratios.cash_coverage_days >= 90 %}{{ text_excellent }}{% elseif liquidity_ratios.cash_coverage_days >= 30 %}{{ text_good }}{% else %}{{ text_poor }}{% endif %}
        </span>
      </td>
      <td>{{ text_cash_coverage_interpretation }}</td>
    </tr>
  </tbody>
</table>
{% endif %}

<!-- Cash Conversion Cycle -->
{% if cash_conversion_cycle %}
<h3>{{ text_cash_conversion_cycle }}</h3>
<table class="cash-flow-table">
  <thead>
    <tr>
      <th>{{ text_component }}</th>
      <th class="text-right">{{ text_days }}</th>
      <th>{{ text_description }}</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>{{ text_collection_period }}</td>
      <td class="text-right">{{ cash_conversion_cycle.average_collection_period }}</td>
      <td>{{ text_collection_period_description }}</td>
    </tr>
    <tr>
      <td>{{ text_inventory_period }}</td>
      <td class="text-right">{{ cash_conversion_cycle.average_inventory_period }}</td>
      <td>{{ text_inventory_period_description }}</td>
    </tr>
    <tr>
      <td>{{ text_payment_period }}</td>
      <td class="text-right">{{ cash_conversion_cycle.average_payment_period }}</td>
      <td>{{ text_payment_period_description }}</td>
    </tr>
  </tbody>
  <tfoot>
    <tr style="background-color: #e2e3e5; font-weight: bold;">
      <td>{{ text_cash_conversion_cycle }}</td>
      <td class="text-right">{{ cash_conversion_cycle.cash_conversion_cycle }}</td>
      <td>
        <span class="ratio-badge ratio-{% if cash_conversion_cycle.efficiency_rating == 'excellent' %}excellent{% elseif cash_conversion_cycle.efficiency_rating == 'good' %}good{% else %}warning{% endif %}">
          {{ cash_conversion_cycle.efficiency_rating }}
        </span>
      </td>
    </tr>
  </tfoot>
</table>
{% endif %}

<!-- Trend Analysis -->
{% if trend_analysis %}
<h3>{{ text_trend_analysis }}</h3>
<table class="cash-flow-table">
  <thead>
    <tr>
      <th>{{ text_period }}</th>
      <th class="text-right">{{ text_cash_inflow }}</th>
      <th class="text-right">{{ text_cash_outflow }}</th>
      <th class="text-right">{{ text_net_cash_flow }}</th>
      <th class="text-right">{{ text_growth_rate }}</th>
    </tr>
  </thead>
  <tbody>
    {% for trend in trend_analysis|slice(-12) %}
    <tr>
      <td>{{ trend.period }}</td>
      <td class="text-right">{{ trend.cash_inflow }}</td>
      <td class="text-right">{{ trend.cash_outflow }}</td>
      <td class="text-right {% if trend.net_cash_flow >= 0 %}text-success{% else %}text-danger{% endif %}">
        {{ trend.net_cash_flow }}
      </td>
      <td class="text-right {% if trend.growth_rate >= 0 %}text-success{% else %}text-danger{% endif %}">
        {{ trend.growth_rate }}%
      </td>
    </tr>
    {% endfor %}
  </tbody>
</table>
{% endif %}

<!-- Cash Flow Forecast -->
{% if forecasting %}
<div class="page-break"></div>
<h3>{{ text_cash_flow_forecast }}</h3>
<table class="cash-flow-table">
  <thead>
    <tr>
      <th>{{ text_period }}</th>
      <th class="text-right">{{ text_forecasted_flow }}</th>
      <th class="text-center">{{ text_confidence }}</th>
      <th>{{ text_scenario }}</th>
    </tr>
  </thead>
  <tbody>
    {% for forecast in forecasting %}
    <tr>
      <td>{{ forecast.period }}</td>
      <td class="text-right {% if forecast.forecasted_net_flow >= 0 %}text-success{% else %}text-danger{% endif %}">
        {{ forecast.forecasted_net_flow }}
      </td>
      <td class="text-center">
        <span class="ratio-badge ratio-{% if forecast.confidence_level == 'high' %}excellent{% elseif forecast.confidence_level == 'medium' %}good{% else %}warning{% endif %}">
          {{ forecast.confidence_level }}
        </span>
      </td>
      <td>{{ forecast.scenario }}</td>
    </tr>
    {% endfor %}
  </tbody>
</table>
{% endif %}

<!-- Risk Assessment -->
{% if risk_assessment and risk_assessment|length > 0 %}
<h3>{{ text_risk_assessment }}</h3>
<table class="cash-flow-table">
  <thead>
    <tr>
      <th>{{ text_risk_type }}</th>
      <th class="text-center">{{ text_severity }}</th>
      <th>{{ text_description }}</th>
      <th>{{ text_impact }}</th>
    </tr>
  </thead>
  <tbody>
    {% for risk in risk_assessment %}
    <tr>
      <td>{{ risk.type }}</td>
      <td class="text-center">
        <span class="ratio-badge ratio-{% if risk.severity == 'high' %}danger{% elseif risk.severity == 'medium' %}warning{% else %}good{% endif %}">
          {{ risk.severity }}
        </span>
      </td>
      <td>{{ risk.description }}</td>
      <td>{{ risk.impact }}</td>
    </tr>
    {% endfor %}
  </tbody>
</table>
{% endif %}

<!-- Recommendations -->
{% if recommendations %}
<h3>{{ text_recommendations }}</h3>
<div style="background-color: #f8f9fa; padding: 15px; border: 1px solid #dee2e6;">
  <ol style="margin: 0; padding-left: 20px;">
    {% for recommendation in recommendations %}
    <li style="margin-bottom: 10px;">{{ recommendation }}</li>
    {% endfor %}
  </ol>
</div>
{% endif %}

<!-- Signatures -->
<div class="signature-section">
  <div class="signature-box">
    <div class="signature-line">{{ text_prepared_by }}</div>
  </div>
  <div class="signature-box">
    <div class="signature-line">{{ text_reviewed_by }}</div>
  </div>
  <div class="signature-box">
    <div class="signature-line">{{ text_approved_by }}</div>
  </div>
</div>

<!-- Footer -->
<div class="footer">
  <div style="display: flex; justify-content: space-between;">
    <div>
      {{ text_confidential }} - {{ text_internal_use }}
    </div>
    <div>
      {{ text_eas_compliant }} | {{ text_eta_ready }} | {{ text_cash_flow_control }}
    </div>
    <div>
      {{ text_page }} 1 {{ text_of }} 1
    </div>
  </div>
</div>

<script>
// Auto print when page loads
window.onload = function() {
  window.print();
};
</script>

</body>
</html>
