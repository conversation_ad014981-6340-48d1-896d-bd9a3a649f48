{% extends "common/column_left.twig" %}
{% block content %}
<div class="page-header">
  <div class="container-fluid">
    <h1>{{ heading_title }} - {{ text_form }}</h1>
    <ul class="breadcrumb">
      <li><a href="{{ home }}">{{ text_home }}</a></li>
      <li><a href="{{ cancel }}">{{ heading_title }}</a></li>
    </ul>
  </div>
</div>
<div class="container-fluid">
  <form action="{{ action }}" method="post" enctype="multipart/form-data" id="form-gr" class="form-horizontal">
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title">{{ text_form }}</h3>
      </div>
      <div class="panel-body">
        <div class="form-group">
          <label class="col-sm-2 control-label" for="input-receipt-number">{{ entry_receipt_number }}</label>
          <div class="col-sm-10">
            <input type="text" name="receipt_number" value="{{ receipt_number }}" id="input-receipt-number" class="form-control" />
          </div>
        </div>
        <div class="form-group">
          <label class="col-sm-2 control-label" for="input-po_id">{{ entry_po_id }}</label>
          <div class="col-sm-10">
            <input type="text" name="po_id" value="{{ po_id }}" id="input-po_id" class="form-control" />
          </div>
        </div>
        <!-- Add other fields similarly -->
        <table class="table table-bordered table-hover">
          <thead>
            <tr>
              <th>PO Item ID</th>
              <th>Product</th>
              <th>Qty Received</th>
              <th>Unit</th>
              <th>Quality</th>
            </tr>
          </thead>
          <tbody id="receipt-items">
          {% if receipt_items %}
            {% for item in receipt_items %}
            <tr>
              <td><input type="text" name="receipt_items[{{ loop.index0 }}][po_item_id]" value="{{ item.po_item_id }}" class="form-control" /></td>
              <td><input type="text" name="receipt_items[{{ loop.index0 }}][product_id]" value="{{ item.product_id }}" class="form-control" /></td>
              <td><input type="text" name="receipt_items[{{ loop.index0 }}][quantity_received]" value="{{ item.quantity_received }}" class="form-control" /></td>
              <td><input type="text" name="receipt_items[{{ loop.index0 }}][unit_id]" value="{{ item.unit_id }}" class="form-control" /></td>
              <td><select name="receipt_items[{{ loop.index0 }}][quality_result]" class="form-control">
                <option value="pending" {% if item.quality_result=='pending' %}selected{% endif %}>Pending</option>
                <option value="passed" {% if item.quality_result=='passed' %}selected{% endif %}>Passed</option>
                <option value="failed" {% if item.quality_result=='failed' %}selected{% endif %}>Failed</option>
                <option value="partial" {% if item.quality_result=='partial' %}selected{% endif %}>Partial</option>
              </select></td>
            </tr>
            {% endfor %}
          {% else %}
            <tr>
              <td colspan="5" class="text-center">No Items</td>
            </tr>
          {% endif %}
          </tbody>
        </table>
      </div>
      <div class="panel-footer">
        <button type="submit" class="btn btn-primary">{{ button_save }}</button>
        <a href="{{ cancel }}" class="btn btn-default">{{ button_cancel }}</a>
      </div>
    </div>
  </form>
</div>
{% endblock %}
