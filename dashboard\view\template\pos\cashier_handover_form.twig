{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="submit" form="form-handover" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fa fa-save"></i></button>
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a>
      </div>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pencil"></i> {{ text_form }}</h3>
      </div>
      <div class="panel-body">
        <form action="{{ action }}" method="post" enctype="multipart/form-data" id="form-handover" class="form-horizontal">
          <div class="form-group">
            <label class="col-sm-2 control-label">{{ entry_cash_available }}</label>
            <div class="col-sm-10">
              <div class="input-group">
                <span class="input-group-addon">{{ currency_symbol }}</span>
                <input type="text" value="{{ formatted_available_cash }}" class="form-control" readonly />
              </div>
              <input type="hidden" name="available_cash" value="{{ available_cash }}" />
            </div>
          </div>
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-to-user">{{ entry_to_user }}</label>
            <div class="col-sm-10">
              <select name="to_user_id" id="input-to-user" class="form-control">
                <option value="">{{ text_select }}</option>
                {% for user in users %}
                <option value="{{ user.user_id }}">{{ user.firstname }} {{ user.lastname }}</option>
                {% endfor %}
              </select>
              {% if error_to_user %}
              <div class="text-danger">{{ error_to_user }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-amount">{{ entry_amount }}</label>
            <div class="col-sm-10">
              <div class="input-group">
                <span class="input-group-addon">{{ currency_symbol }}</span>
                <input type="text" name="amount" value="" placeholder="{{ entry_amount }}" id="input-amount" class="form-control" />
              </div>
              {% if error_amount %}
              <div class="text-danger">{{ error_amount }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-notes">{{ entry_notes }}</label>
            <div class="col-sm-10">
              <textarea name="notes" rows="5" placeholder="{{ entry_notes }}" id="input-notes" class="form-control"></textarea>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
{{ footer }}