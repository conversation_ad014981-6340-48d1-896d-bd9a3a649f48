{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="submit" form="form-settings" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fa fa-save"></i></button>
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a>
      </div>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if success %}
    <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pencil"></i> {{ text_settings }}</h3>
      </div>
      <div class="panel-body">
        <form action="{{ action }}" method="post" enctype="multipart/form-data" id="form-settings" class="form-horizontal">
          <ul class="nav nav-tabs">
            {% for tab_id, tab in tabs %}
            <li {% if loop.first %}class="active"{% endif %}><a href="#tab-{{ tab_id }}" data-toggle="tab"><i class="fa {{ tab.icon }}"></i> {{ tab.title }}</a></li>
            {% endfor %}
          </ul>
          <div class="tab-content">
            <!-- General Tab -->
            <div class="tab-pane active" id="tab-general">
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-default-pricing-type">{{ entry_default_pricing_type }}</label>
                <div class="col-sm-10">
                  <select name="pos_default_pricing_type" id="input-default-pricing-type" class="form-control">
                    {% for key, pricing_type in pricing_types %}
                    <option value="{{ key }}" {{ pos_default_pricing_type == key ? 'selected="selected"' }}>{{ pricing_type }}</option>
                    {% endfor %}
                  </select>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-require-customer">{{ entry_require_customer }}</label>
                <div class="col-sm-10">
                  <select name="pos_require_customer" id="input-require-customer" class="form-control">
                    <option value="1" {{ pos_require_customer ? 'selected="selected"' }}>{{ text_yes }}</option>
                    <option value="0" {{ not pos_require_customer ? 'selected="selected"' }}>{{ text_no }}</option>
                  </select>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-default-payment-method">{{ entry_default_payment_method }}</label>
                <div class="col-sm-10">
                  <select name="pos_default_payment_method" id="input-default-payment-method" class="form-control">
                    <option value="">{{ text_select }}</option>
                    {% for payment_method in payment_methods %}
                    <option value="{{ payment_method.code }}" {{ pos_default_payment_method == payment_method.code ? 'selected="selected"' }}>{{ payment_method.title }}</option>
                    {% endfor %}
                  </select>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-default-shipping-method">{{ entry_default_shipping_method }}</label>
                <div class="col-sm-10">
                  <select name="pos_default_shipping_method" id="input-default-shipping-method" class="form-control">
                    <option value="">{{ text_select }}</option>
                    {% for shipping_method in shipping_methods %}
                    <option value="{{ shipping_method.code }}" {{ pos_default_shipping_method == shipping_method.code ? 'selected="selected"' }}>{{ shipping_method.title }}</option>
                    {% endfor %}
                  </select>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-enable-quick-sale">{{ entry_enable_quick_sale }}</label>
                <div class="col-sm-10">
                  <select name="pos_enable_quick_sale" id="input-enable-quick-sale" class="form-control">
                    <option value="1" {{ pos_enable_quick_sale ? 'selected="selected"' }}>{{ text_yes }}</option>
                    <option value="0" {{ not pos_enable_quick_sale ? 'selected="selected"' }}>{{ text_no }}</option>
                  </select>
                </div>
              </div>
            </div>
            
            <!-- Security Tab -->
            <div class="tab-pane" id="tab-security">
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-require-shift">{{ entry_require_shift }}</label>
                <div class="col-sm-10">
                  <select name="pos_require_shift" id="input-require-shift" class="form-control">
                    <option value="1" {{ pos_require_shift ? 'selected="selected"' }}>{{ text_yes }}</option>
                    <option value="0" {{ not pos_require_shift ? 'selected="selected"' }}>{{ text_no }}</option>
                  </select>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-allow-discount">{{ entry_allow_discount }}</label>
                <div class="col-sm-10">
                  <select name="pos_allow_discount" id="input-allow-discount" class="form-control">
                    <option value="1" {{ pos_allow_discount ? 'selected="selected"' }}>{{ text_yes }}</option>
                    <option value="0" {{ not pos_allow_discount ? 'selected="selected"' }}>{{ text_no }}</option>
                  </select>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-max-discount-percentage">{{ entry_max_discount_percentage }}</label>
                <div class="col-sm-10">
                  <div class="input-group">
                    <input type="text" name="pos_max_discount_percentage" value="{{ pos_max_discount_percentage }}" placeholder="{{ entry_max_discount_percentage }}" id="input-max-discount-percentage" class="form-control" />
                    <span class="input-group-addon">%</span>
                  </div>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-allow-price-change">{{ entry_allow_price_change }}</label>
                <div class="col-sm-10">
                  <select name="pos_allow_price_change" id="input-allow-price-change" class="form-control">
                    <option value="1" {{ pos_allow_price_change ? 'selected="selected"' }}>{{ text_yes }}</option>
                    <option value="0" {{ not pos_allow_price_change ? 'selected="selected"' }}>{{ text_no }}</option>
                  </select>
                </div>
              </div>
            </div>
            
            <!-- Display Tab -->
            <div class="tab-pane" id="tab-display">
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-default-category">{{ entry_default_category }}</label>
                <div class="col-sm-10">
                  <select name="pos_default_category" id="input-default-category" class="form-control">
                    <option value="0">{{ text_all_categories }}</option>
                    {% for category in categories %}
                    <option value="{{ category.category_id }}" {{ pos_default_category == category.category_id ? 'selected="selected"' }}>{{ category.name }}</option>
                    {% endfor %}
                  </select>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-items-per-page">{{ entry_items_per_page }}</label>
                <div class="col-sm-10">
                  <input type="text" name="pos_items_per_page" value="{{ pos_items_per_page }}" placeholder="{{ entry_items_per_page }}" id="input-items-per-page" class="form-control" />
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-barcode-mode">{{ entry_barcode_mode }}</label>
                <div class="col-sm-10">
                  <select name="pos_barcode_mode" id="input-barcode-mode" class="form-control">
                    <option value="1" {{ pos_barcode_mode ? 'selected="selected"' }}>{{ text_yes }}</option>
                    <option value="0" {{ not pos_barcode_mode ? 'selected="selected"' }}>{{ text_no }}</option>
                  </select>
                </div>
              </div>
            </div>
            
            <!-- Printing Tab -->
            <div class="tab-pane" id="tab-printing">
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-auto-print-receipt">{{ entry_auto_print_receipt }}</label>
                <div class="col-sm-10">
                  <select name="pos_auto_print_receipt" id="input-auto-print-receipt" class="form-control">
                    <option value="1" {{ pos_auto_print_receipt ? 'selected="selected"' }}>{{ text_yes }}</option>
                    <option value="0" {{ not pos_auto_print_receipt ? 'selected="selected"' }}>{{ text_no }}</option>
                  </select>
                </div>
              </div>
            </div>
            
            <!-- Terminals Tab -->
            <div class="tab-pane" id="tab-terminals">
              <div class="table-responsive">
                <table class="table table-bordered table-hover">
                  <thead>
                    <tr>
                      <td class="text-left">{{ column_name }}</td>
                      <td class="text-left">{{ column_branch }}</td>
                      <td class="text-left">{{ column_printer }}</td>
                      <td class="text-center">{{ column_status }}</td>
                      <td class="text-right">{{ column_action }}</td>
                    </tr>
                  </thead>
                  <tbody>
                    {% if terminals %}
                    {% for terminal in terminals %}
                    <tr>
                      <td class="text-left">{{ terminal.name }}</td>
                      <td class="text-left">{{ terminal.branch_name }}</td>
                      <td class="text-left">{{ terminal.printer_name }}</td>
                      <td class="text-center">{{ terminal.status ? text_enabled : text_disabled }}</td>
                      <td class="text-right">
                        <a href="{{ url_link('pos/settings/terminal', 'user_token=' ~ user_token ~ '&terminal_id=' ~ terminal.terminal_id) }}" data-toggle="tooltip" title="{{ button_edit }}" class="btn btn-primary"><i class="fa fa-pencil"></i></a>
                        <button type="button" data-terminal-id="{{ terminal.terminal_id }}" data-toggle="tooltip" title="{{ button_delete }}" class="btn btn-danger delete-terminal"><i class="fa fa-trash-o"></i></button>
                      </td>
                    </tr>
                    {% endfor %}
                    {% else %}
                    <tr>
                      <td class="text-center" colspan="5">{{ text_no_terminals }}</td>
                    </tr>
                    {% endif %}
                  </tbody>
                  <tfoot>
                    <tr>
                      <td class="text-right" colspan="5"><a href="{{ url_link('pos/settings/terminal', 'user_token=' ~ user_token) }}" data-toggle="tooltip" title="{{ button_add_terminal }}" class="btn btn-primary"><i class="fa fa-plus"></i></a></td>
                    </tr>
                  </tfoot>
                </table>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript">
$(document).ready(function() {
  $('.delete-terminal').on('click', function() {
    if (confirm('{{ text_confirm_delete }}')) {
      var terminal_id = $(this).data('terminal-id');
      
      $.ajax({
        url: 'index.php?route=pos/settings/deleteTerminal&user_token={{ user_token }}',
        type: 'post',
        data: {terminal_id: terminal_id},
        dataType: 'json',
        success: function(json) {
          if (json.success) {
            location.reload();
          } else if (json.error) {
            alert(json.error);
          }
        },
        error: function(xhr, ajaxOptions, thrownError) {
          alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
      });
    }
  });
});
</script>
{{ footer }}