<?php
/**
 * English Language File - Workflow Designer
 *
 * @package    AYM ERP
 * <AUTHOR> Development Team
 * @copyright  2025 AYM ERP Systems
 * @license    Commercial License
 * @version    1.0.0
 * @since      2025-07-15
 */

// Heading and text
$_['heading_title']              = 'Visual Workflow Editor';

// Text
$_['text_success']               = 'Success: You have modified your workflow!';
$_['text_workflow']              = 'Workflows';
$_['text_designer']              = 'Designer';
$_['text_list']                  = 'Workflow List';
$_['text_add']                   = 'Add Workflow';
$_['text_edit']                  = 'Edit Workflow';
$_['text_enabled']               = 'Enabled';
$_['text_disabled']              = 'Disabled';
$_['text_workflow_designer']     = 'Workflow Designer';
$_['text_no_results']            = 'No results!';
$_['text_confirm']               = 'Are you sure?';
$_['text_start']                 = 'Start';
$_['text_task']                  = 'Task';
$_['text_decision']              = 'Decision';
$_['text_email']                 = 'Email';
$_['text_delay']                 = 'Delay';
$_['text_end']                   = 'End';
$_['text_zoom_in']               = 'Zoom In';
$_['text_zoom_out']              = 'Zoom Out';
$_['text_clear']                 = 'Clear';
$_['text_form']                  = 'Workflow Designer';

// Entry
$_['entry_name']                 = 'Workflow Name';
$_['entry_description']          = 'Description';
$_['entry_status']               = 'Status';

// Help
$_['help_workflow']              = 'Design your workflow by dragging nodes onto the canvas and connecting them';

// Error
$_['error_permission']           = 'Warning: You do not have permission to modify workflows!';
$_['error_name']                 = 'Workflow Name must be between 3 and 64 characters!';
$_['error_workflow_data']        = 'Invalid workflow design data!';

// Tabs
$_['tab_general']                = 'General';
$_['tab_designer']               = 'Designer';
$_['tab_settings']               = 'Settings';

// Buttons
$_['button_save']                = 'Save';
$_['button_cancel']              = 'Cancel';
$_['button_design']              = 'Design';
$_['button_delete']              = 'Delete';
$_['button_add']                 = 'Add';
$_['button_edit']                = 'Edit';

// Additional properties
$_['text_form']                 = 'Edit Workflow';
$_['text_saving']               = 'Saving';
$_['text_select']               = '-- Select --';
$_['text_active']               = 'Active';
$_['text_inactive']             = 'Inactive';
$_['text_archived']             = 'Archived';
$_['text_properties']           = 'Properties';
$_['text_no_node_selected']     = 'No node selected';
$_['text_triggers']             = 'Triggers';
$_['text_actions']              = 'Actions';
$_['text_flow']                 = 'Flow';
$_['text_connections']          = 'Connections';
$_['text_approval_properties']  = 'Approval Properties';
$_['text_any_one']              = 'Any one approval';
$_['text_all']                  = 'All approval';
$_['text_percentage']           = 'Approval percentage';
$_['text_sequential']           = 'Sequential approval';
$_['text_select_user']          = 'Select user';
$_['text_select_group']         = 'Select group';
$_['text_document_approval']    = 'Document approval';
$_['text_purchase_approval']    = 'Purchase approval';
$_['text_leave_request']        = 'Leave request';
$_['text_expense_claim']        = 'Expense claim';
$_['text_payment_approval']     = 'Payment approval';
$_['text_other']                = 'Other';
$_['text_confirm_delete']       = 'Are you sure you want to delete this node?';
$_['text_confirm_reset']        = 'Are you sure you want to reset the workflow? All nodes and connections will be deleted.';
$_['text_success_save']         = 'Workflow saved successfully!';
$_['text_error_save']           = 'Error occurred while saving workflow!';

// Additional buttons
$_['button_zoom_in']             = 'Zoom In';
$_['button_zoom_out']            = 'Zoom Out';
$_['button_zoom_reset']          = 'Reset Zoom';
$_['button_undo']                = 'Undo';
$_['button_redo']                = 'Redo';
$_['button_copy']                = 'Copy';
$_['button_paste']               = 'Paste';

// Node types
$_['text_node_start']            = 'Start';
$_['text_node_end']              = 'End';
$_['text_node_task']             = 'Task';
$_['text_node_approval']         = 'Approval';
$_['text_node_condition']        = 'Condition';
$_['text_node_notification']     = 'Notification';
$_['text_node_delay']            = 'Delay';
$_['text_node_script']           = 'Script';
$_['text_node_webhook']          = 'Webhook';
$_['text_node_email']            = 'Email';

// Nodes tooltips
$_['node_tooltip_start']         = 'Start the workflow';
$_['node_tooltip_task']          = 'Assign a task to a user or group';
$_['node_tooltip_decision']      = 'Branch workflow based on a condition';
$_['node_tooltip_email']         = 'Send email notification';
$_['node_tooltip_delay']         = 'Add a delay or wait period';
$_['node_tooltip_end']           = 'End the workflow';