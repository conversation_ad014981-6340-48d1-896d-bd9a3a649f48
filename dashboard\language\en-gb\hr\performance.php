<?php
// Heading
$_['heading_title']          = 'Performance Reviews Management';

// Text
$_['text_filter']            = 'Filter';
$_['text_select_employee']   = 'Select Employee';
$_['text_select_reviewer']   = 'Select Reviewer';
$_['text_all_statuses']      = 'All Statuses';
$_['text_status_pending']    = 'Pending';
$_['text_status_completed']  = 'Completed';
$_['text_performance_list']  = 'Performance Reviews List';
$_['text_add_review']        = 'Add Review';
$_['text_edit_review']       = 'Edit Review';
$_['text_ajax_error']        = 'An error occurred while communicating with the server';
$_['text_confirm_delete']    = 'Are you sure you want to delete?';
$_['text_review_date']       = 'Review Date';
$_['text_overall_score']     = 'Overall Score';
$_['text_comments']          = 'Comments';
$_['text_criteria_scores']   = 'Criteria Scores';

$_['text_review_date_start'] = 'Review Start Date';
$_['text_review_date_end']   = 'Review End Date';
$_['text_employee']          = 'Employee';
$_['text_reviewer']          = 'Reviewer';
$_['text_status']            = 'Status';

// Buttons
$_['button_filter']          = 'Filter';
$_['button_reset']           = 'Reset';
$_['button_add_review']      = 'Add Review';
$_['button_close']           = 'Close';
$_['button_save']            = 'Save';

// Columns
$_['column_employee']        = 'Employee';
$_['column_review_date']     = 'Review Date';
$_['column_reviewer']        = 'Reviewer';
$_['column_overall_score']   = 'Overall Score';
$_['column_status']          = 'Status';
$_['column_actions']         = 'Actions';

$_['column_criteria_name']   = 'Criteria Name';
$_['column_score']           = 'Score';
$_['column_comments']        = 'Comments';

// Errors/Success
$_['error_not_found']        = 'Record not found!';
$_['error_invalid_request']  = 'Invalid request!';
$_['error_permission']       = 'Warning: You do not have permission to modify performance reviews!';
$_['error_required']         = 'Warning: Please fill in the required fields!';
$_['text_success_add']       = 'Review added successfully!';
$_['text_success_edit']      = 'Review updated successfully!';
$_['text_success_delete']    = 'Review deleted successfully!';
