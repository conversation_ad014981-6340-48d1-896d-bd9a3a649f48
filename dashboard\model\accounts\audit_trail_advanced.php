<?php
/**
 * نموذج مسار التدقيق المتقدم
 * نظام تدقيق شامل مع تكامل محرر سير العمل المرئي
 * يدعم التدقيق التلقائي والذكي مع تتبع كامل للعمليات
 */
class ModelAccountsAuditTrailAdvanced extends Model {
    private $central_service;

    public function __construct($registry) {
        parent::__construct($registry);
        
        // تحميل الخدمات المركزية
        $this->load->model('core/central_service_manager');
        $this->central_service = $this->model_core_central_service_manager;
    }

    /**
     * إنشاء تدقيق جديد
     */
    public function createAudit($data) {
        $this->db->query("START TRANSACTION");

        try {
            // إنشاء التدقيق الأساسي
            $this->db->query("INSERT INTO cod_audit_trail_advanced SET 
                audit_name = '" . $this->db->escape($data['audit_name']) . "',
                audit_type = '" . $this->db->escape($data['audit_type']) . "',
                audit_scope = '" . $this->db->escape($data['audit_scope']) . "',
                period_start = '" . $this->db->escape($data['period_start']) . "',
                period_end = '" . $this->db->escape($data['period_end']) . "',
                assigned_auditor = '" . (int)$data['assigned_auditor'] . "',
                audit_objectives = '" . $this->db->escape($data['audit_objectives']) . "',
                risk_level = '" . $this->db->escape($data['risk_level']) . "',
                workflow_template_id = '" . (int)$data['workflow_template_id'] . "',
                automated_checks = '" . (isset($data['automated_checks']) ? 1 : 0) . "',
                ai_assistance = '" . (isset($data['ai_assistance']) ? 1 : 0) . "',
                status = 'planned',
                created_by = '" . (int)$this->user->getId() . "',
                date_created = NOW(),
                date_modified = NOW()
            ");

            $audit_id = $this->db->getLastId();

            // إنشاء خطة التدقيق الافتراضية
            $this->createDefaultAuditPlan($audit_id, $data['audit_type']);

            $this->db->query("COMMIT");
            return $audit_id;

        } catch (Exception $e) {
            $this->db->query("ROLLBACK");
            throw $e;
        }
    }

    /**
     * بدء التدقيق
     */
    public function startAudit($audit_id) {
        try {
            $this->db->query("UPDATE cod_audit_trail_advanced SET 
                status = 'in_progress',
                date_started = NOW(),
                date_modified = NOW()
                WHERE audit_id = '" . (int)$audit_id . "'
            ");

            return true;

        } catch (Exception $e) {
            throw $e;
        }
    }

    /**
     * إكمال التدقيق
     */
    public function completeAudit($data) {
        $this->db->query("START TRANSACTION");

        try {
            $audit_id = $data['audit_id'];

            // تحديث حالة التدقيق
            $this->db->query("UPDATE cod_audit_trail_advanced SET 
                status = 'completed',
                completion_notes = '" . $this->db->escape($data['completion_notes']) . "',
                overall_rating = '" . $this->db->escape($data['overall_rating']) . "',
                recommendations_summary = '" . $this->db->escape($data['recommendations_summary']) . "',
                date_completed = NOW(),
                date_modified = NOW()
                WHERE audit_id = '" . (int)$audit_id . "'
            ");

            // تحديث حالة جميع النتائج المفتوحة
            $this->db->query("UPDATE cod_audit_trail_findings SET 
                status = 'closed'
                WHERE audit_id = '" . (int)$audit_id . "' 
                AND status = 'open'
            ");

            $this->db->query("COMMIT");
            return true;

        } catch (Exception $e) {
            $this->db->query("ROLLBACK");
            throw $e;
        }
    }

    /**
     * إضافة نتيجة تدقيق
     */
    public function addFinding($data) {
        $this->db->query("START TRANSACTION");

        try {
            // إضافة النتيجة
            $this->db->query("INSERT INTO cod_audit_trail_findings SET 
                audit_id = '" . (int)$data['audit_id'] . "',
                finding_type = '" . $this->db->escape($data['finding_type']) . "',
                severity = '" . $this->db->escape($data['severity']) . "',
                finding_title = '" . $this->db->escape($data['finding_title']) . "',
                finding_description = '" . $this->db->escape($data['finding_description']) . "',
                affected_accounts = '" . $this->db->escape(json_encode($data['affected_accounts'])) . "',
                financial_impact = '" . (float)$data['financial_impact'] . "',
                recommendation = '" . $this->db->escape($data['recommendation']) . "',
                responsible_party = '" . (int)$data['responsible_party'] . "',
                due_date = '" . $this->db->escape($data['due_date']) . "',
                is_automated = '" . (isset($data['is_automated']) ? 1 : 0) . "',
                status = 'open',
                identified_by = '" . (int)$this->user->getId() . "',
                date_identified = NOW()
            ");

            $finding_id = $this->db->getLastId();

            $this->db->query("COMMIT");
            return $finding_id;

        } catch (Exception $e) {
            $this->db->query("ROLLBACK");
            throw $e;
        }
    }

    /**
     * إضافة دليل
     */
    public function addEvidence($finding_id, $evidence_data) {
        try {
            $this->db->query("INSERT INTO cod_audit_trail_evidence SET 
                finding_id = '" . (int)$finding_id . "',
                evidence_type = '" . $this->db->escape($evidence_data['type']) . "',
                evidence_title = '" . $this->db->escape($evidence_data['title']) . "',
                evidence_description = '" . $this->db->escape($evidence_data['description']) . "',
                file_path = '" . $this->db->escape($evidence_data['file_path']) . "',
                file_size = '" . (int)$evidence_data['file_size'] . "',
                uploaded_by = '" . (int)$this->user->getId() . "',
                date_uploaded = NOW()
            ");

            return $this->db->getLastId();

        } catch (Exception $e) {
            throw $e;
        }
    }

    /**
     * ربط سير العمل بالتدقيق
     */
    public function linkWorkflow($audit_id, $workflow_id) {
        try {
            $this->db->query("UPDATE cod_audit_trail_advanced SET 
                workflow_id = '" . (int)$workflow_id . "'
                WHERE audit_id = '" . (int)$audit_id . "'
            ");

            return true;

        } catch (Exception $e) {
            throw $e;
        }
    }

    /**
     * جلب التدقيقات
     */
    public function getAudits($filter = array()) {
        $sql = "SELECT ata.*, u1.username as created_by_name, u2.username as assigned_auditor_name
                FROM cod_audit_trail_advanced ata
                LEFT JOIN cod_user u1 ON (ata.created_by = u1.user_id)
                LEFT JOIN cod_user u2 ON (ata.assigned_auditor = u2.user_id)
                WHERE 1=1";

        if (!empty($filter['status'])) {
            $sql .= " AND ata.status = '" . $this->db->escape($filter['status']) . "'";
        }

        if (!empty($filter['audit_type'])) {
            $sql .= " AND ata.audit_type = '" . $this->db->escape($filter['audit_type']) . "'";
        }

        if (!empty($filter['date_from'])) {
            $sql .= " AND ata.period_start >= '" . $this->db->escape($filter['date_from']) . "'";
        }

        if (!empty($filter['date_to'])) {
            $sql .= " AND ata.period_end <= '" . $this->db->escape($filter['date_to']) . "'";
        }

        $sql .= " ORDER BY ata.date_created DESC";

        $query = $this->db->query($sql);
        return $query->rows;
    }

    /**
     * جلب إحصائيات التدقيق
     */
    public function getAuditStatistics() {
        $stats = array();

        // إجمالي التدقيقات
        $query = $this->db->query("SELECT COUNT(*) as total FROM cod_audit_trail_advanced");
        $stats['total_audits'] = $query->row['total'];

        // التدقيقات النشطة
        $query = $this->db->query("SELECT COUNT(*) as active FROM cod_audit_trail_advanced WHERE status = 'in_progress'");
        $stats['active_audits'] = $query->row['active'];

        // التدقيقات المكتملة
        $query = $this->db->query("SELECT COUNT(*) as completed FROM cod_audit_trail_advanced WHERE status = 'completed'");
        $stats['completed_audits'] = $query->row['completed'];

        // النتائج المفتوحة
        $query = $this->db->query("SELECT COUNT(*) as open_findings FROM cod_audit_trail_findings WHERE status = 'open'");
        $stats['open_findings'] = $query->row['open_findings'];

        // النتائج عالية الخطورة
        $query = $this->db->query("SELECT COUNT(*) as critical_findings FROM cod_audit_trail_findings WHERE severity = 'critical' AND status = 'open'");
        $stats['critical_findings'] = $query->row['critical_findings'];

        // متوسط وقت التدقيق
        $query = $this->db->query("SELECT AVG(DATEDIFF(date_completed, date_started)) as avg_duration 
                                   FROM cod_audit_trail_advanced 
                                   WHERE status = 'completed' 
                                   AND date_started IS NOT NULL 
                                   AND date_completed IS NOT NULL");
        $stats['average_duration'] = $query->row['avg_duration'] ?? 0;

        return $stats;
    }

    /**
     * جلب التدقيقات النشطة
     */
    public function getActiveAudits() {
        $query = $this->db->query("SELECT ata.*, u.username as assigned_auditor_name
                                   FROM cod_audit_trail_advanced ata
                                   LEFT JOIN cod_user u ON (ata.assigned_auditor = u.user_id)
                                   WHERE ata.status = 'in_progress'
                                   ORDER BY ata.date_started ASC
        ");

        return $query->rows;
    }

    /**
     * جلب النتائج الحديثة
     */
    public function getRecentFindings() {
        $query = $this->db->query("SELECT atf.*, ata.audit_name, u.username as identified_by_name
                                   FROM cod_audit_trail_findings atf
                                   INNER JOIN cod_audit_trail_advanced ata ON (atf.audit_id = ata.audit_id)
                                   LEFT JOIN cod_user u ON (atf.identified_by = u.user_id)
                                   WHERE atf.status = 'open'
                                   ORDER BY atf.date_identified DESC
                                   LIMIT 10
        ");

        return $query->rows;
    }

    /**
     * جلب قوالب التدقيق
     */
    public function getAuditTemplates() {
        $query = $this->db->query("SELECT * FROM cod_workflow_template WHERE template_type = 'audit' AND is_active = '1' ORDER BY template_name ASC");
        return $query->rows;
    }

    /**
     * جلب تدقيق محدد
     */
    public function getAudit($audit_id) {
        $query = $this->db->query("SELECT ata.*, u1.username as created_by_name, u2.username as assigned_auditor_name
                                   FROM cod_audit_trail_advanced ata
                                   LEFT JOIN cod_user u1 ON (ata.created_by = u1.user_id)
                                   LEFT JOIN cod_user u2 ON (ata.assigned_auditor = u2.user_id)
                                   WHERE ata.audit_id = '" . (int)$audit_id . "'
        ");

        return $query->num_rows ? $query->row : null;
    }

    /**
     * جلب نتائج التدقيق
     */
    public function getAuditFindings($audit_id) {
        $query = $this->db->query("SELECT atf.*, u1.username as identified_by_name, u2.username as responsible_party_name
                                   FROM cod_audit_trail_findings atf
                                   LEFT JOIN cod_user u1 ON (atf.identified_by = u1.user_id)
                                   LEFT JOIN cod_user u2 ON (atf.responsible_party = u2.user_id)
                                   WHERE atf.audit_id = '" . (int)$audit_id . "'
                                   ORDER BY atf.severity DESC, atf.date_identified DESC
        ");

        return $query->rows;
    }

    /**
     * جلب أدلة التدقيق
     */
    public function getAuditEvidence($audit_id) {
        $query = $this->db->query("SELECT ate.*, atf.finding_title, u.username as uploaded_by_name
                                   FROM cod_audit_trail_evidence ate
                                   INNER JOIN cod_audit_trail_findings atf ON (ate.finding_id = atf.finding_id)
                                   LEFT JOIN cod_user u ON (ate.uploaded_by = u.user_id)
                                   WHERE atf.audit_id = '" . (int)$audit_id . "'
                                   ORDER BY ate.date_uploaded DESC
        ");

        return $query->rows;
    }

    /**
     * جلب حالة سير العمل
     */
    public function getWorkflowStatus($audit_id) {
        $audit = $this->getAudit($audit_id);
        
        if (!$audit || !$audit['workflow_id']) {
            return null;
        }

        $this->load->model('workflow/visual_workflow_engine');
        return $this->model_workflow_visual_workflow_engine->getWorkflowStatus($audit['workflow_id']);
    }

    /**
     * جلب تقدم التدقيق
     */
    public function getAuditProgress($audit_id) {
        $progress = array();

        // حساب النسبة المئوية للإكمال
        $total_tasks = $this->getTotalAuditTasks($audit_id);
        $completed_tasks = $this->getCompletedAuditTasks($audit_id);

        $progress['completion_percentage'] = $total_tasks > 0 ? ($completed_tasks / $total_tasks) * 100 : 0;
        $progress['total_tasks'] = $total_tasks;
        $progress['completed_tasks'] = $completed_tasks;
        $progress['remaining_tasks'] = $total_tasks - $completed_tasks;

        // حساب الوقت المتبقي المقدر
        $audit = $this->getAudit($audit_id);
        if ($audit['date_started']) {
            $days_elapsed = (time() - strtotime($audit['date_started'])) / (24 * 60 * 60);
            $estimated_total_days = $progress['completion_percentage'] > 0 ? 
                ($days_elapsed / $progress['completion_percentage']) * 100 : 0;
            $progress['estimated_remaining_days'] = max(0, $estimated_total_days - $days_elapsed);
        } else {
            $progress['estimated_remaining_days'] = 0;
        }

        return $progress;
    }

    /**
     * جلب سير العمل المرتبط بالتدقيق
     */
    public function getAuditWorkflow($audit_id) {
        $query = $this->db->query("SELECT workflow_id FROM cod_audit_trail_advanced WHERE audit_id = '" . (int)$audit_id . "'");
        return $query->num_rows ? $query->row['workflow_id'] : null;
    }

    /**
     * توليد التقرير النهائي
     */
    public function generateFinalReport($audit_id) {
        try {
            // جلب بيانات التدقيق الكاملة
            $audit = $this->getAudit($audit_id);
            $findings = $this->getAuditFindings($audit_id);
            $evidence = $this->getAuditEvidence($audit_id);

            // إنشاء التقرير
            $report_data = array(
                'audit' => $audit,
                'findings' => $findings,
                'evidence' => $evidence,
                'summary' => $this->generateAuditSummary($audit_id)
            );

            // حفظ التقرير في قاعدة البيانات
            $this->db->query("INSERT INTO cod_audit_reports SET 
                audit_id = '" . (int)$audit_id . "',
                report_type = 'final',
                report_data = '" . $this->db->escape(json_encode($report_data)) . "',
                generated_by = '" . (int)$this->user->getId() . "',
                date_generated = NOW()
            ");

            return $this->db->getLastId();

        } catch (Exception $e) {
            throw $e;
        }
    }

    /**
     * دوال مساعدة
     */
    private function createDefaultAuditPlan($audit_id, $audit_type) {
        // إنشاء خطة تدقيق افتراضية حسب نوع التدقيق
        $default_tasks = $this->getDefaultAuditTasks($audit_type);
        
        foreach ($default_tasks as $task) {
            $this->db->query("INSERT INTO cod_audit_tasks SET 
                audit_id = '" . (int)$audit_id . "',
                task_name = '" . $this->db->escape($task['name']) . "',
                task_description = '" . $this->db->escape($task['description']) . "',
                task_order = '" . (int)$task['order'] . "',
                estimated_hours = '" . (int)$task['hours'] . "',
                status = 'pending',
                date_created = NOW()
            ");
        }
    }

    private function getDefaultAuditTasks($audit_type) {
        $tasks = array();
        
        switch ($audit_type) {
            case 'financial':
                $tasks = array(
                    array('name' => 'مراجعة الميزانية العمومية', 'description' => 'فحص دقة وصحة الميزانية العمومية', 'order' => 1, 'hours' => 8),
                    array('name' => 'مراجعة قائمة الدخل', 'description' => 'فحص الإيرادات والمصروفات', 'order' => 2, 'hours' => 6),
                    array('name' => 'مراجعة التدفق النقدي', 'description' => 'تحليل التدفقات النقدية', 'order' => 3, 'hours' => 4),
                    array('name' => 'فحص الحسابات المدينة', 'description' => 'مراجعة الذمم المدينة', 'order' => 4, 'hours' => 5),
                    array('name' => 'فحص الحسابات الدائنة', 'description' => 'مراجعة الذمم الدائنة', 'order' => 5, 'hours' => 5)
                );
                break;
            case 'operational':
                $tasks = array(
                    array('name' => 'مراجعة العمليات التشغيلية', 'description' => 'فحص كفاءة العمليات', 'order' => 1, 'hours' => 10),
                    array('name' => 'مراجعة الضوابط الداخلية', 'description' => 'تقييم نظم الرقابة الداخلية', 'order' => 2, 'hours' => 8),
                    array('name' => 'فحص الامتثال', 'description' => 'التأكد من الامتثال للقوانين', 'order' => 3, 'hours' => 6)
                );
                break;
            case 'compliance':
                $tasks = array(
                    array('name' => 'مراجعة الامتثال التنظيمي', 'description' => 'فحص الامتثال للوائح', 'order' => 1, 'hours' => 12),
                    array('name' => 'مراجعة السياسات والإجراءات', 'description' => 'تقييم السياسات المطبقة', 'order' => 2, 'hours' => 8)
                );
                break;
            default:
                $tasks = array(
                    array('name' => 'مراجعة عامة', 'description' => 'مراجعة شاملة للنظام', 'order' => 1, 'hours' => 16)
                );
                break;
        }

        return $tasks;
    }

    private function getTotalAuditTasks($audit_id) {
        $query = $this->db->query("SELECT COUNT(*) as total FROM cod_audit_tasks WHERE audit_id = '" . (int)$audit_id . "'");
        return $query->row['total'];
    }

    private function getCompletedAuditTasks($audit_id) {
        $query = $this->db->query("SELECT COUNT(*) as completed FROM cod_audit_tasks WHERE audit_id = '" . (int)$audit_id . "' AND status = 'completed'");
        return $query->row['completed'];
    }

    private function generateAuditSummary($audit_id) {
        $summary = array();
        
        // إحصائيات النتائج
        $query = $this->db->query("SELECT 
                                      severity,
                                      COUNT(*) as count,
                                      SUM(financial_impact) as total_impact
                                   FROM cod_audit_trail_findings 
                                   WHERE audit_id = '" . (int)$audit_id . "'
                                   GROUP BY severity
        ");

        $summary['findings_by_severity'] = $query->rows;

        // التوصيات الرئيسية
        $query = $this->db->query("SELECT recommendation 
                                   FROM cod_audit_trail_findings 
                                   WHERE audit_id = '" . (int)$audit_id . "' 
                                   AND severity IN ('critical', 'high')
                                   ORDER BY severity DESC
        ");

        $summary['key_recommendations'] = array_column($query->rows, 'recommendation');

        return $summary;
    }
}
