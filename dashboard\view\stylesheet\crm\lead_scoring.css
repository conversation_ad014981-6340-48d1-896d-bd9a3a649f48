/**
 * CSS متقدم لتقييم العملاء المحتملين
 * Advanced Lead Scoring CSS
 * 
 * الهدف: توفير تصميم احترافي ومتجاوب
 * الميزات: تأثيرات متقدمة، ألوان ذكية، تحريك سلس
 * 
 * <AUTHOR> Team
 * @version 1.0
 * @since 2024
 */

/* ========== المتغيرات العامة ========== */
:root {
    --primary-color: #007bff;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-radius: 8px;
    --box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    --transition: all 0.3s ease;
    --font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* ========== الخطوط والنصوص ========== */
body {
    font-family: var(--font-family);
    direction: rtl;
    text-align: right;
}

.lead-scoring-container {
    padding: 20px;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
}

/* ========== بطاقات الإحصائيات ========== */
.stats-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 25px;
    margin-bottom: 20px;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--info-color));
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.stats-card .icon {
    font-size: 3rem;
    opacity: 0.8;
    margin-bottom: 15px;
}

.stats-card .number {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 5px;
    background: linear-gradient(45deg, var(--primary-color), var(--info-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.stats-card .label {
    color: #6c757d;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.stats-card.primary { border-left: 4px solid var(--primary-color); }
.stats-card.success { border-left: 4px solid var(--success-color); }
.stats-card.warning { border-left: 4px solid var(--warning-color); }
.stats-card.danger { border-left: 4px solid var(--danger-color); }

/* ========== الفلاتر المتقدمة ========== */
.advanced-filters {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 25px;
    margin-bottom: 20px;
}

.filter-group {
    margin-bottom: 20px;
}

.filter-control {
    border: 2px solid #e9ecef;
    border-radius: var(--border-radius);
    padding: 12px 15px;
    transition: var(--transition);
    font-size: 14px;
}

.filter-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
    outline: none;
}

.filter-buttons {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 20px;
}

.btn-filter {
    padding: 10px 20px;
    border-radius: var(--border-radius);
    border: none;
    font-weight: 600;
    transition: var(--transition);
    cursor: pointer;
}

.btn-filter.primary {
    background: linear-gradient(45deg, var(--primary-color), #0056b3);
    color: white;
}

.btn-filter.primary:hover {
    background: linear-gradient(45deg, #0056b3, var(--primary-color));
    transform: translateY(-2px);
}

/* ========== الجدول المتقدم ========== */
.advanced-table {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    overflow: hidden;
}

.table-header {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.table-title {
    font-size: 1.2rem;
    font-weight: bold;
    margin: 0;
}

.table-actions {
    display: flex;
    gap: 10px;
}

.table-responsive {
    max-height: 600px;
    overflow-y: auto;
}

.leads-table {
    margin: 0;
    border: none;
}

.leads-table thead th {
    background: #f8f9fa;
    border: none;
    padding: 15px 12px;
    font-weight: 600;
    color: #495057;
    position: sticky;
    top: 0;
    z-index: 10;
}

.leads-table tbody tr {
    transition: var(--transition);
    border-bottom: 1px solid #e9ecef;
}

.leads-table tbody tr:hover {
    background: #f8f9fa;
    transform: scale(1.01);
}

.leads-table td {
    padding: 15px 12px;
    vertical-align: middle;
    border: none;
}

/* ========== نقاط التقييم ========== */
.score-badge {
    display: inline-block;
    padding: 8px 15px;
    border-radius: 20px;
    font-weight: bold;
    font-size: 0.9rem;
    text-align: center;
    min-width: 60px;
    position: relative;
    overflow: hidden;
}

.score-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

.score-badge:hover::before {
    left: 100%;
}

.score-hot {
    background: linear-gradient(45deg, var(--danger-color), #c82333);
    color: white;
    animation: pulse 2s infinite;
}

.score-warm {
    background: linear-gradient(45deg, var(--warning-color), #e0a800);
    color: #212529;
}

.score-cold {
    background: linear-gradient(45deg, #6c757d, #545b62);
    color: white;
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(220, 53, 69, 0); }
    100% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0); }
}

/* ========== شريط التقدم المتقدم ========== */
.conversion-progress {
    position: relative;
    height: 25px;
    background: #e9ecef;
    border-radius: 15px;
    overflow: hidden;
}

.conversion-progress-bar {
    height: 100%;
    border-radius: 15px;
    position: relative;
    transition: width 1s ease-in-out;
    background: linear-gradient(45deg, var(--success-color), #20c997);
}

.conversion-progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.3) 50%, transparent 70%);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.conversion-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-weight: bold;
    font-size: 0.8rem;
    color: white;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

/* ========== الأزرار المتقدمة ========== */
.action-btn {
    padding: 8px 15px;
    border: none;
    border-radius: var(--border-radius);
    font-size: 0.85rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.action-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255,255,255,0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s, height 0.3s;
}

.action-btn:hover::before {
    width: 300px;
    height: 300px;
}

.btn-view {
    background: linear-gradient(45deg, var(--info-color), #138496);
    color: white;
}

.btn-edit {
    background: linear-gradient(45deg, var(--warning-color), #e0a800);
    color: #212529;
}

.btn-convert {
    background: linear-gradient(45deg, var(--success-color), #1e7e34);
    color: white;
}

.btn-delete {
    background: linear-gradient(45deg, var(--danger-color), #c82333);
    color: white;
}

/* ========== الرسوم البيانية ========== */
.chart-container {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 25px;
    margin-bottom: 20px;
    position: relative;
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e9ecef;
}

.chart-title {
    font-size: 1.1rem;
    font-weight: bold;
    color: #495057;
    margin: 0;
}

.chart-canvas {
    position: relative;
    height: 300px;
}

/* ========== الإشعارات ========== */
.notifications-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    max-width: 400px;
}

.notification {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 15px 20px;
    margin-bottom: 10px;
    border-left: 4px solid var(--primary-color);
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.notification.success { border-left-color: var(--success-color); }
.notification.warning { border-left-color: var(--warning-color); }
.notification.error { border-left-color: var(--danger-color); }

/* ========== التحميل ========== */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ========== النوافذ المنبثقة ========== */
.modal-content {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    color: white;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    border-bottom: none;
}

.modal-title {
    font-weight: bold;
}

.modal-body {
    padding: 30px;
}

.modal-footer {
    border-top: 1px solid #e9ecef;
    padding: 20px 30px;
}

/* ========== التجاوب ========== */
@media (max-width: 768px) {
    .lead-scoring-container {
        padding: 10px;
    }
    
    .stats-card {
        padding: 15px;
        margin-bottom: 15px;
    }
    
    .stats-card .number {
        font-size: 2rem;
    }
    
    .advanced-filters {
        padding: 15px;
    }
    
    .filter-buttons {
        flex-direction: column;
        gap: 5px;
    }
    
    .table-actions {
        flex-direction: column;
        gap: 5px;
    }
    
    .leads-table {
        font-size: 0.85rem;
    }
    
    .leads-table td {
        padding: 10px 8px;
    }
    
    .notifications-container {
        right: 10px;
        left: 10px;
        max-width: none;
    }
}

@media (max-width: 576px) {
    .stats-card .icon {
        font-size: 2rem;
    }
    
    .stats-card .number {
        font-size: 1.5rem;
    }
    
    .chart-canvas {
        height: 250px;
    }
    
    .action-btn {
        padding: 6px 10px;
        font-size: 0.75rem;
    }
}

/* ========== تحسينات الطباعة ========== */
@media print {
    .lead-scoring-container {
        background: white;
        padding: 0;
    }
    
    .stats-card,
    .advanced-table,
    .chart-container {
        box-shadow: none;
        border: 1px solid #ddd;
    }
    
    .action-btn,
    .filter-buttons,
    .table-actions {
        display: none;
    }
    
    .notifications-container {
        display: none;
    }
}

/* ========== تحسينات إمكانية الوصول ========== */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0,0,0,0);
    white-space: nowrap;
    border: 0;
}

.focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* ========== تأثيرات متقدمة ========== */
.glass-effect {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
}

.gradient-text {
    background: linear-gradient(45deg, var(--primary-color), var(--info-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.shadow-hover {
    transition: var(--transition);
}

.shadow-hover:hover {
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    transform: translateY(-3px);
}
