{{ header }}{{ column_left }}

<!-- Enterprise Grade Plus CSS for Advanced Account Statement -->
<style>
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --danger-color: #e74c3c;
    --warning-color: #f39c12;
    --info-color: #17a2b8;
    --statement-color: #6f42c1;
    --light-bg: #f8f9fa;
    --border-color: #dee2e6;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

.statement-container {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: var(--shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.statement-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--statement-color), var(--primary-color), var(--secondary-color));
}

.statement-header {
    text-align: center;
    border-bottom: 3px solid var(--statement-color);
    padding-bottom: 20px;
    margin-bottom: 30px;
    position: relative;
}

.statement-header h2 {
    color: var(--statement-color);
    font-weight: 700;
    font-size: 1.8rem;
    margin: 0;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.account-info-card {
    background: linear-gradient(135deg, #f8f9fa, #ffffff);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: var(--shadow);
}

.statement-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 25px;
}

.summary-card {
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 15px;
    text-align: center;
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.summary-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 12px rgba(0,0,0,0.1);
}

.summary-card h5 {
    color: var(--primary-color);
    margin-bottom: 8px;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.summary-card .amount {
    font-size: 1.2rem;
    font-weight: 700;
    font-family: 'Courier New', monospace;
}

.amount-debit { color: var(--danger-color); }
.amount-credit { color: var(--success-color); }
.amount-balance { color: var(--primary-color); }

.statement-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    background: #ffffff;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.statement-table th {
    background: linear-gradient(135deg, var(--statement-color), #8e44ad);
    color: white;
    padding: 12px 10px;
    text-align: center;
    font-weight: 600;
    font-size: 0.85rem;
    border-bottom: 2px solid var(--border-color);
}

.statement-table td {
    padding: 10px;
    border-bottom: 1px solid var(--border-color);
    text-align: center;
    transition: var(--transition);
    font-size: 0.9rem;
}

.statement-table tbody tr:hover {
    background: var(--light-bg);
    transform: scale(1.002);
}

.statement-table tbody tr:nth-child(even) {
    background: #fafbfc;
}

.amount-cell {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    text-align: {{ direction == 'rtl' ? 'right' : 'left' }};
}

.opening-balance-row {
    background: #e8f5e8 !important;
    font-weight: 600;
}

.closing-balance-row {
    background: #fff3cd !important;
    font-weight: 600;
}

.balance-positive { color: var(--success-color); }
.balance-negative { color: var(--danger-color); }

.transaction-row {
    cursor: pointer;
}

.transaction-row:hover {
    background: #e3f2fd !important;
}

/* RTL Support */
[dir="rtl"] .statement-table {
    direction: rtl;
}

[dir="rtl"] .amount-cell {
    text-align: right;
}

/* Print Styles */
@media print {
    .statement-container {
        box-shadow: none;
        border: 1px solid #000;
    }
    
    .statement-table th {
        background: #f0f0f0 !important;
        color: #000 !important;
    }
    
    .btn, .card-header {
        display: none !important;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .statement-table {
        font-size: 0.75rem;
    }
    
    .statement-table th,
    .statement-table td {
        padding: 6px 4px;
    }
    
    .statement-summary {
        grid-template-columns: repeat(2, 1fr);
    }
}
</style>

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-{{ direction == 'rtl' ? 'start' : 'end' }}">
        <div class="btn-group" role="group" aria-label="{{ text_actions }}">
          <button type="button" class="btn btn-success btn-lg" onclick="generateStatement()" 
                  data-bs-toggle="tooltip" title="{{ text_generate_tooltip }}">
            <i class="fas fa-file-invoice me-2"></i> {{ button_generate }}
          </button>
          <div class="btn-group" role="group">
            <button type="button" class="btn btn-info dropdown-toggle" data-bs-toggle="dropdown" 
                    aria-expanded="false" data-bs-toggle="tooltip" title="{{ text_export_tooltip }}">
              <i class="fas fa-download me-2"></i> {{ text_export }}
            </button>
            <ul class="dropdown-menu">
              <li><a class="dropdown-item" href="#" onclick="exportStatement('excel')">
                <i class="fas fa-file-excel text-success me-2"></i> Excel
              </a></li>
              <li><a class="dropdown-item" href="#" onclick="exportStatement('pdf')">
                <i class="fas fa-file-pdf text-danger me-2"></i> PDF
              </a></li>
              <li><a class="dropdown-item" href="#" onclick="exportStatement('csv')">
                <i class="fas fa-file-csv text-info me-2"></i> CSV
              </a></li>
              <li><hr class="dropdown-divider"></li>
              <li><a class="dropdown-item" href="#" onclick="printStatement()">
                <i class="fas fa-print text-secondary me-2"></i> {{ text_print }}
              </a></li>
            </ul>
          </div>
          <button type="button" class="btn btn-outline-primary" onclick="showComparison()"
                  data-bs-toggle="tooltip" title="{{ text_compare_periods }}">
            <i class="fas fa-chart-line"></i>
          </button>
        </div>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li class="breadcrumb-item"><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>

  <div class="container-fluid">
    <!-- Statement Filter Form -->
    <div class="card mb-4">
      <div class="card-header">
        <h3 class="card-title">
          <i class="fas fa-filter me-2"></i>{{ text_statement_filters }}
        </h3>
      </div>
      <div class="card-body">
        <form id="statement-filter-form" method="post">
          <div class="row">
            <div class="col-md-4">
              <div class="form-group">
                <label for="account_id" class="form-label">{{ entry_account }}</label>
                <select name="account_id" id="account_id" class="form-control select2" required>
                  <option value="">{{ text_please_select }}</option>
                  {% for account in accounts %}
                  <option value="{{ account.account_id }}"{% if account.account_id == account_id %} selected{% endif %}>
                    {{ account.account_code }} - {{ account.account_name }}
                  </option>
                  {% endfor %}
                </select>
              </div>
            </div>
            <div class="col-md-2">
              <div class="form-group">
                <label for="date_start" class="form-label">{{ entry_date_start }}</label>
                <input type="date" name="date_start" id="date_start" value="{{ date_start }}" class="form-control" required>
              </div>
            </div>
            <div class="col-md-2">
              <div class="form-group">
                <label for="date_end" class="form-label">{{ entry_date_end }}</label>
                <input type="date" name="date_end" id="date_end" value="{{ date_end }}" class="form-control" required>
              </div>
            </div>
            <div class="col-md-2">
              <div class="form-group">
                <label for="branch_id" class="form-label">{{ entry_branch }}</label>
                <select name="branch_id" id="branch_id" class="form-control">
                  <option value="">{{ text_all_branches }}</option>
                  {% for branch in branches %}
                  <option value="{{ branch.branch_id }}"{% if branch.branch_id == branch_id %} selected{% endif %}>{{ branch.name }}</option>
                  {% endfor %}
                </select>
              </div>
            </div>
            <div class="col-md-2">
              <div class="form-group">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                  <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search me-2"></i>{{ button_filter }}
                  </button>
                </div>
              </div>
            </div>
          </div>
          <div class="row mt-3">
            <div class="col-md-3">
              <div class="form-check">
                <input class="form-check-input" type="checkbox" name="include_opening_balance" id="include_opening_balance"{% if include_opening_balance %} checked{% endif %}>
                <label class="form-check-label" for="include_opening_balance">
                  {{ entry_include_opening_balance }}
                </label>
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-check">
                <input class="form-check-input" type="checkbox" name="include_closing_balance" id="include_closing_balance"{% if include_closing_balance %} checked{% endif %}>
                <label class="form-check-label" for="include_closing_balance">
                  {{ entry_include_closing_balance }}
                </label>
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-check">
                <input class="form-check-input" type="checkbox" name="detailed_view" id="detailed_view"{% if detailed_view %} checked{% endif %}>
                <label class="form-check-label" for="detailed_view">
                  {{ entry_detailed_view }}
                </label>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>

    <!-- Account Statement Content -->
    {% if statement_data %}
    <div class="statement-container">
      <div class="statement-header">
        <h2>{{ heading_title }}</h2>
        <p class="text-muted">{{ text_account_statement_for }}: {{ statement_data.account.account_name }} ({{ statement_data.account.account_code }})</p>
        <p class="text-muted">{{ text_period_from_to }} {{ date_start_formatted }} {{ text_to }} {{ date_end_formatted }}</p>
      </div>

      <!-- Account Information Card -->
      <div class="account-info-card">
        <div class="row">
          <div class="col-md-6">
            <h5>{{ text_account_info }}</h5>
            <p><strong>{{ entry_account_code }}:</strong> {{ statement_data.account.account_code }}</p>
            <p><strong>{{ entry_account_name }}:</strong> {{ statement_data.account.account_name }}</p>
            <p><strong>{{ text_account_type }}:</strong> {{ statement_data.account.account_type }}</p>
          </div>
          <div class="col-md-6">
            <h5>{{ text_statement_summary }}</h5>
            <p><strong>{{ text_period }}:</strong> {{ date_start_formatted }} {{ text_to }} {{ date_end_formatted }}</p>
            <p><strong>{{ text_transaction_count }}:</strong> {{ statement_data.transaction_count }}</p>
            <p><strong>{{ text_net_movement }}:</strong>
              <span class="{% if statement_data.net_movement >= 0 %}balance-positive{% else %}balance-negative{% endif %}">
                {{ statement_data.net_movement_formatted }}
              </span>
            </p>
          </div>
        </div>
      </div>

      <!-- Statement Summary Cards -->
      <div class="statement-summary">
        <div class="summary-card">
          <h5>{{ text_opening_balance }}</h5>
          <div class="amount amount-balance">{{ statement_data.opening_balance_formatted }}</div>
        </div>
        <div class="summary-card">
          <h5>{{ text_total_debit }}</h5>
          <div class="amount amount-debit">{{ statement_data.total_debit_formatted }}</div>
        </div>
        <div class="summary-card">
          <h5>{{ text_total_credit }}</h5>
          <div class="amount amount-credit">{{ statement_data.total_credit_formatted }}</div>
        </div>
        <div class="summary-card">
          <h5>{{ text_closing_balance }}</h5>
          <div class="amount amount-balance">{{ statement_data.closing_balance_formatted }}</div>
        </div>
      </div>

      <!-- Statement Table -->
      <div class="table-responsive">
        <table class="statement-table" id="statement-table">
          <thead>
            <tr>
              <th>{{ column_date }}</th>
              <th>{{ column_reference }}</th>
              <th>{{ column_description }}</th>
              <th>{{ column_debit }}</th>
              <th>{{ column_credit }}</th>
              <th>{{ column_running_balance }}</th>
              <th>{{ column_source }}</th>
              {% if detailed_view %}
              <th>{{ column_action }}</th>
              {% endif %}
            </tr>
          </thead>
          <tbody>
            {% if include_opening_balance and statement_data.opening_balance != 0 %}
            <tr class="opening-balance-row">
              <td>{{ date_start_formatted }}</td>
              <td>-</td>
              <td><strong>{{ text_opening_balance }}</strong></td>
              <td class="amount-cell">{% if statement_data.opening_balance > 0 %}{{ statement_data.opening_balance_formatted }}{% else %}-{% endif %}</td>
              <td class="amount-cell">{% if statement_data.opening_balance < 0 %}{{ statement_data.opening_balance_formatted|abs }}{% else %}-{% endif %}</td>
              <td class="amount-cell"><strong>{{ statement_data.opening_balance_formatted }}</strong></td>
              <td>{{ text_opening_balance }}</td>
              {% if detailed_view %}
              <td>-</td>
              {% endif %}
            </tr>
            {% endif %}

            {% for transaction in statement_data.transactions %}
            <tr class="transaction-row" data-entry-id="{{ transaction.journal_entry_id }}">
              <td>{{ transaction.date_formatted }}</td>
              <td>{{ transaction.reference }}</td>
              <td>{{ transaction.description }}</td>
              <td class="amount-cell">{% if transaction.debit > 0 %}{{ transaction.debit_formatted }}{% else %}-{% endif %}</td>
              <td class="amount-cell">{% if transaction.credit > 0 %}{{ transaction.credit_formatted }}{% else %}-{% endif %}</td>
              <td class="amount-cell {% if transaction.running_balance >= 0 %}balance-positive{% else %}balance-negative{% endif %}">
                <strong>{{ transaction.running_balance_formatted }}</strong>
              </td>
              <td>{{ transaction.source }}</td>
              {% if detailed_view %}
              <td>
                <button type="button" class="btn btn-outline-primary btn-sm" onclick="viewEntryDetails({{ transaction.journal_entry_id }})"
                        data-bs-toggle="tooltip" title="{{ text_view_details }}">
                  <i class="fas fa-eye"></i>
                </button>
              </td>
              {% endif %}
            </tr>
            {% endfor %}

            {% if include_closing_balance %}
            <tr class="closing-balance-row">
              <td>{{ date_end_formatted }}</td>
              <td>-</td>
              <td><strong>{{ text_closing_balance }}</strong></td>
              <td class="amount-cell">-</td>
              <td class="amount-cell">-</td>
              <td class="amount-cell"><strong>{{ statement_data.closing_balance_formatted }}</strong></td>
              <td>{{ text_closing_balance }}</td>
              {% if detailed_view %}
              <td>-</td>
              {% endif %}
            </tr>
            {% endif %}
          </tbody>
        </table>
      </div>
    </div>
    {% else %}
    <div class="alert alert-info">
      <i class="fas fa-info-circle me-2"></i>
      {{ text_no_data }}
    </div>
    {% endif %}
  </div>
</div>

<script>
// Enterprise Grade Plus JavaScript for Advanced Account Statement
class AdvancedAccountStatementManager {
    constructor() {
        this.initializeTooltips();
        this.initializeFormValidation();
        this.initializeKeyboardShortcuts();
        this.initializeAutoSave();
        this.initializeDataTable();
        this.initializeSelect2();
    }

    initializeTooltips() {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }

    initializeFormValidation() {
        const form = document.getElementById('statement-filter-form');
        if (form) {
            form.addEventListener('submit', this.validateForm.bind(this));
        }
    }

    initializeKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case 'g':
                        e.preventDefault();
                        this.generateStatement();
                        break;
                    case 'e':
                        e.preventDefault();
                        this.showExportMenu();
                        break;
                    case 'p':
                        e.preventDefault();
                        this.printStatement();
                        break;
                    case 'c':
                        e.preventDefault();
                        this.showComparison();
                        break;
                }
            }
        });
    }

    initializeAutoSave() {
        const inputs = document.querySelectorAll('#statement-filter-form input, #statement-filter-form select');
        inputs.forEach(input => {
            input.addEventListener('change', () => {
                this.saveFormState();
            });
        });
        this.loadFormState();
    }

    initializeDataTable() {
        const table = document.getElementById('statement-table');
        if (table && typeof $ !== 'undefined' && $.fn.DataTable) {
            $(table).DataTable({
                responsive: true,
                pageLength: 50,
                order: [[0, 'asc']], // Sort by date
                columnDefs: [
                    { targets: [3, 4, 5], className: 'text-end' },
                    { targets: [7], orderable: false }
                ],
                language: {
                    url: '{{ direction == "rtl" ? "/view/javascript/jquery/datatables-ar.json" : "/view/javascript/jquery/datatables-en.json" }}'
                }
            });
        }
    }

    initializeSelect2() {
        if (typeof $ !== 'undefined' && $.fn.select2) {
            $('.select2').select2({
                theme: 'bootstrap-5',
                width: '100%'
            });
        }
    }

    validateForm(e) {
        e.preventDefault();
        const accountId = document.getElementById('account_id').value;
        const dateStart = document.getElementById('date_start').value;
        const dateEnd = document.getElementById('date_end').value;

        if (!accountId) {
            this.showAlert('{{ error_account_id }}', 'danger');
            return false;
        }

        if (!dateStart) {
            this.showAlert('{{ error_date_start }}', 'danger');
            return false;
        }

        if (!dateEnd) {
            this.showAlert('{{ error_date_end }}', 'danger');
            return false;
        }

        if (new Date(dateStart) > new Date(dateEnd)) {
            this.showAlert('{{ error_date_range }}', 'danger');
            return false;
        }

        this.generateStatement();
        return true;
    }

    generateStatement() {
        const form = document.getElementById('statement-filter-form');
        const formData = new FormData(form);

        // Show loading state
        this.showLoadingState(true);

        fetch('{{ generate_url }}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            this.showLoadingState(false);
            if (data.success) {
                this.showAlert('{{ success_generate }}', 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                this.showAlert(data.error || '{{ error_generate }}', 'danger');
            }
        })
        .catch(error => {
            this.showLoadingState(false);
            this.showAlert('{{ error_generate }}: ' + error.message, 'danger');
        });
    }

    exportStatement(format) {
        const params = new URLSearchParams({
            format: format,
            account_id: document.getElementById('account_id').value,
            date_start: document.getElementById('date_start').value,
            date_end: document.getElementById('date_end').value,
            include_opening_balance: document.getElementById('include_opening_balance').checked ? '1' : '0',
            include_closing_balance: document.getElementById('include_closing_balance').checked ? '1' : '0',
            detailed_view: document.getElementById('detailed_view').checked ? '1' : '0'
        });

        this.showAlert('{{ text_exporting }}...', 'info');
        window.open('{{ export_url }}&' + params.toString(), '_blank');
    }

    printStatement() {
        window.print();
    }

    showComparison() {
        // Period comparison implementation
        this.showAlert('{{ text_compare_periods }} {{ text_loading }}', 'info');
    }

    viewEntryDetails(entryId) {
        // View journal entry details
        window.open('{{ url_link('accounts/journal_entry', 'edit') }}&journal_entry_id=' + entryId, '_blank');
    }

    showExportMenu() {
        const exportButton = document.querySelector('.dropdown-toggle');
        if (exportButton) {
            exportButton.click();
        }
    }

    showLoadingState(show) {
        const generateBtn = document.querySelector('button[onclick="generateStatement()"]');
        if (generateBtn) {
            if (show) {
                generateBtn.disabled = true;
                generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> {{ text_loading }}';
            } else {
                generateBtn.disabled = false;
                generateBtn.innerHTML = '<i class="fas fa-file-invoice me-2"></i> {{ button_generate }}';
            }
        }
    }

    showAlert(message, type) {
        const alertContainer = document.createElement('div');
        alertContainer.className = `alert alert-${type} alert-dismissible fade show`;
        alertContainer.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'}"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        const container = document.querySelector('#content .container-fluid');
        container.insertBefore(alertContainer, container.firstChild);

        setTimeout(() => {
            alertContainer.remove();
        }, 5000);
    }

    saveFormState() {
        const formData = new FormData(document.getElementById('statement-filter-form'));
        const state = Object.fromEntries(formData);
        localStorage.setItem('statement_form_state', JSON.stringify(state));
    }

    loadFormState() {
        const savedState = localStorage.getItem('statement_form_state');
        if (savedState) {
            const state = JSON.parse(savedState);
            Object.keys(state).forEach(key => {
                const element = document.getElementById(key);
                if (element) {
                    if (element.type === 'checkbox') {
                        element.checked = state[key] === 'on';
                    } else {
                        element.value = state[key];
                    }
                }
            });
        }
    }
}

// Global functions for backward compatibility
function generateStatement() {
    statementManager.generateStatement();
}

function exportStatement(format) {
    statementManager.exportStatement(format);
}

function printStatement() {
    statementManager.printStatement();
}

function showComparison() {
    statementManager.showComparison();
}

function viewEntryDetails(entryId) {
    statementManager.viewEntryDetails(entryId);
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.statementManager = new AdvancedAccountStatementManager();
});
</script>

{{ footer }}
