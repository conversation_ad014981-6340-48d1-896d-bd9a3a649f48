<?php
// Heading
$_['heading_title']                = 'Advanced Account Statement';

// Text
$_['text_list']                    = 'Account Statements List';
$_['text_form']                    = 'Account Statement Form';
$_['text_view']                    = 'View Account Statement';
$_['text_generate']                = 'Generate Account Statement';
$_['text_export']                  = 'Export Account Statement';
$_['text_print']                   = 'Print Account Statement';
$_['text_success']                 = 'Success: Account statement generated successfully!';
$_['text_success_export']          = 'Account statement exported successfully!';
$_['text_loading']                 = 'Loading...';
$_['text_processing']              = 'Processing...';
$_['text_no_results']              = 'No results found';

// Account Information
$_['text_account_info']            = 'Account Information';
$_['text_account_code']            = 'Account Code';
$_['text_account_name']            = 'Account Name';
$_['text_account_type']            = 'Account Type';
$_['text_parent_account']          = 'Parent Account';
$_['text_account_status']          = 'Account Status';
$_['text_account_description']     = 'Account Description';

// Statement Information
$_['text_statement_period']        = 'Statement Period';
$_['text_statement_summary']       = 'Statement Summary';
$_['text_statement_details']       = 'Statement Details';
$_['text_opening_balance']         = 'Opening Balance';
$_['text_closing_balance']         = 'Closing Balance';
$_['text_total_debit']             = 'Total Debit';
$_['text_total_credit']            = 'Total Credit';
$_['text_net_movement']            = 'Net Movement';
$_['text_balance_change']          = 'Balance Change';
$_['text_percentage_change']       = 'Percentage Change';

// Transaction Information
$_['text_transaction_date']        = 'Transaction Date';
$_['text_transaction_description'] = 'Transaction Description';
$_['text_transaction_reference']   = 'Transaction Reference';
$_['text_transaction_amount']      = 'Transaction Amount';
$_['text_transaction_type']        = 'Transaction Type';
$_['text_transaction_balance']     = 'Balance After Transaction';
$_['text_transaction_count']       = 'Transaction Count';

// Analysis
$_['text_analysis']                = 'Analysis';
$_['text_advanced_analysis']       = 'Advanced Analysis';
$_['text_trend_analysis']          = 'Trend Analysis';
$_['text_activity_analysis']       = 'Activity Analysis';
$_['text_performance_analysis']    = 'Performance Analysis';
$_['text_comparison_analysis']     = 'Comparison Analysis';
$_['text_statistical_analysis']   = 'Statistical Analysis';

// Statistics
$_['text_statistics']              = 'Statistics';
$_['text_average_balance']         = 'Average Balance';
$_['text_minimum_balance']         = 'Minimum Balance';
$_['text_maximum_balance']         = 'Maximum Balance';
$_['text_average_transaction']     = 'Average Transaction';
$_['text_largest_transaction']     = 'Largest Transaction';
$_['text_smallest_transaction']    = 'Smallest Transaction';
$_['text_active_days']             = 'Active Days';
$_['text_inactive_days']           = 'Inactive Days';
$_['text_activity_rate']           = 'Activity Rate';
$_['text_volatility']              = 'Volatility';

// Comparison
$_['text_compare_periods']         = 'Compare Periods';
$_['text_period_comparison']       = 'Period Comparison';
$_['text_previous_period']         = 'Previous Period';
$_['text_current_period']          = 'Current Period';
$_['text_variance']                = 'Variance';
$_['text_variance_percentage']     = 'Variance Percentage';
$_['text_improvement']             = 'Improvement';
$_['text_deterioration']           = 'Deterioration';

// Charts and Visualization
$_['text_charts']                  = 'Charts';
$_['text_balance_chart']           = 'Balance Chart';
$_['text_activity_chart']          = 'Activity Chart';
$_['text_trend_chart']             = 'Trend Chart';
$_['text_comparison_chart']        = 'Comparison Chart';
$_['text_monthly_activity']        = 'Monthly Activity';
$_['text_daily_activity']          = 'Daily Activity';

// Export Options
$_['text_export_options']          = 'Export Options';
$_['text_export_excel']            = 'Export to Excel';
$_['text_export_pdf']              = 'Export to PDF';
$_['text_export_csv']              = 'Export to CSV';
$_['text_export_format']           = 'Export Format';
$_['text_include_charts']          = 'Include Charts';
$_['text_include_analysis']        = 'Include Analysis';

// Print Options
$_['text_print_options']           = 'Print Options';
$_['text_print_preview']           = 'Print Preview';
$_['text_print_format']            = 'Print Format';
$_['text_page_orientation']        = 'Page Orientation';
$_['text_portrait']                = 'Portrait';
$_['text_landscape']               = 'Landscape';
$_['text_paper_size']              = 'Paper Size';
$_['text_include_header']          = 'Include Header';
$_['text_include_footer']          = 'Include Footer';

// Entry Fields
$_['entry_account']                = 'Account';
$_['entry_date_start']             = 'Start Date';
$_['entry_date_end']               = 'End Date';
$_['entry_include_opening']        = 'Include Opening Balance';
$_['entry_include_closing']        = 'Include Closing Balance';
$_['entry_group_by']               = 'Group By';
$_['entry_sort_by']                = 'Sort By';
$_['entry_filter_amount_from']     = 'Amount From';
$_['entry_filter_amount_to']       = 'Amount To';
$_['entry_filter_description']     = 'Description Contains';

// Column Headers
$_['column_date']                  = 'Date';
$_['column_description']           = 'Description';
$_['column_reference']             = 'Reference';
$_['column_debit']                 = 'Debit';
$_['column_credit']                = 'Credit';
$_['column_balance']               = 'Balance';
$_['column_type']                  = 'Type';
$_['column_amount']                = 'Amount';
$_['column_running_balance']       = 'Running Balance';

// Buttons
$_['button_generate']              = 'Generate';
$_['button_view']                  = 'View';
$_['button_export']                = 'Export';
$_['button_print']                 = 'Print';
$_['button_clear']                 = 'Clear';
$_['button_reset']                 = 'Reset';
$_['button_refresh']               = 'Refresh';
$_['button_analyze']               = 'Analyze';
$_['button_compare']               = 'Compare';
$_['button_save']                  = 'Save';
$_['button_cancel']                = 'Cancel';

// Error Messages
$_['error_permission']             = 'Warning: You do not have permission to access account statement!';
$_['error_account_required']       = 'Account is required!';
$_['error_date_start_required']    = 'Start date is required!';
$_['error_date_end_required']      = 'End date is required!';
$_['error_date_invalid']           = 'Invalid date format!';
$_['error_date_range']             = 'End date must be after start date!';
$_['error_account_not_found']      = 'Account not found!';
$_['error_no_transactions']        = 'No transactions found in the specified period!';
$_['error_export_failed']          = 'Export failed! Please try again.';
$_['error_print_failed']           = 'Print failed! Please try again.';
$_['error_generation_failed']      = 'Account statement generation failed!';

// Success Messages
$_['success_generated']            = 'Account statement generated successfully!';
$_['success_exported']             = 'Account statement exported successfully!';
$_['success_printed']              = 'Account statement sent to printer successfully!';

// Help Text
$_['help_account_statement']       = 'Account statement shows all transactions and balances for a specific account during a specified time period.';
$_['help_date_range']              = 'Select the time period for the account statement. Leave empty to include all transactions.';
$_['help_advanced_analysis']       = 'Advanced analysis provides statistics and charts to better understand account performance.';
$_['help_comparison']              = 'Period comparison helps analyze changes and trends over time.';

// Account Types
$_['text_type_asset']              = 'Asset';
$_['text_type_liability']          = 'Liability';
$_['text_type_equity']             = 'Equity';
$_['text_type_revenue']            = 'Revenue';
$_['text_type_expense']            = 'Expense';

// Transaction Types
$_['text_type_debit']              = 'Debit';
$_['text_type_credit']             = 'Credit';
$_['text_type_transfer']           = 'Transfer';
$_['text_type_adjustment']         = 'Adjustment';
$_['text_type_opening']            = 'Opening Balance';
$_['text_type_closing']            = 'Closing Balance';

// Status
$_['text_status_active']           = 'Active';
$_['text_status_inactive']         = 'Inactive';
$_['text_status_suspended']        = 'Suspended';
$_['text_status_closed']           = 'Closed';

// Periods
$_['text_period_daily']            = 'Daily';
$_['text_period_weekly']           = 'Weekly';
$_['text_period_monthly']          = 'Monthly';
$_['text_period_quarterly']        = 'Quarterly';
$_['text_period_yearly']           = 'Yearly';
$_['text_period_custom']           = 'Custom Period';

// Grouping Options
$_['text_group_by_date']           = 'Group by Date';
$_['text_group_by_type']           = 'Group by Type';
$_['text_group_by_month']          = 'Group by Month';
$_['text_group_by_quarter']        = 'Group by Quarter';

// Sorting Options
$_['text_sort_by_date']            = 'Sort by Date';
$_['text_sort_by_amount']          = 'Sort by Amount';
$_['text_sort_by_description']     = 'Sort by Description';
$_['text_sort_ascending']          = 'Ascending';
$_['text_sort_descending']         = 'Descending';

// Additional
$_['text_total']                   = 'Total';
$_['text_subtotal']                = 'Subtotal';
$_['text_grand_total']             = 'Grand Total';
$_['text_count']                   = 'Count';
$_['text_average']                 = 'Average';
$_['text_percentage']              = 'Percentage';
$_['text_ratio']                   = 'Ratio';
$_['text_index']                   = 'Index';
$_['text_score']                   = 'Score';

// Compliance
$_['text_eas_compliant']           = 'Egyptian Accounting Standards Compliant';
$_['text_eta_ready']               = 'ETA Integration Ready';
$_['text_egyptian_gaap']           = 'Egyptian GAAP Compliant';

// Generated Information
$_['text_generated_by']            = 'Generated By';
$_['text_generated_on']            = 'Generated On';
$_['text_report_date']             = 'Report Date';
$_['text_page']                    = 'Page';
$_['text_of']                      = 'of';
$_['text_confidential']            = 'Confidential';
$_['text_internal_use']            = 'Internal Use Only';
?>
