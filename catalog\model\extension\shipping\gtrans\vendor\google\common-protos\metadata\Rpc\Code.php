<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/rpc/code.proto

namespace GPBMetadata\Google\Rpc;

class Code
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(hex2bin(
            "0abf030a15676f6f676c652f7270632f636f64652e70726f746f120a676f" .
            "6f676c652e7270632ab7020a04436f646512060a024f4b1000120d0a0943" .
            "414e43454c4c45441001120b0a07554e4b4e4f574e100212140a10494e56" .
            "414c49445f415247554d454e54100312150a11444541444c494e455f4558" .
            "4345454445441004120d0a094e4f545f464f554e44100512120a0e414c52" .
            "454144595f455849535453100612150a115045524d495353494f4e5f4445" .
            "4e494544100712130a0f554e41555448454e54494341544544101012160a" .
            "125245534f555243455f455848415553544544100812170a134641494c45" .
            "445f505245434f4e444954494f4e1009120b0a0741424f52544544100a12" .
            "100a0c4f55545f4f465f52414e4745100b12110a0d554e494d504c454d45" .
            "4e544544100c120c0a08494e5445524e414c100d120f0a0b554e41564149" .
            "4c41424c45100e120d0a09444154415f4c4f5353100f42580a0e636f6d2e" .
            "676f6f676c652e7270634209436f646550726f746f50015a33676f6f676c" .
            "652e676f6c616e672e6f72672f67656e70726f746f2f676f6f676c656170" .
            "69732f7270632f636f64653b636f6465a20203525043620670726f746f33"
        ), true);

        static::$is_initialized = true;
    }
}

