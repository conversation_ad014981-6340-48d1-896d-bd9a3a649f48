<?php
// Heading
$_['heading_title']                    = 'General Ledger';

// Text
$_['text_success']                     = 'Success: General Ledger has been generated successfully!';
$_['text_list']                        = 'General Ledger List';
$_['text_form']                        = 'General Ledger Form';
$_['text_view']                        = 'View General Ledger';
$_['text_generate']                    = 'Generate General Ledger';
$_['text_export']                      = 'Export General Ledger';
$_['text_compare']                     = 'Compare General Ledger';
$_['text_print']                       = 'Print General Ledger';
$_['text_general_ledger']              = 'General Ledger';
$_['text_period']                      = 'Period';
$_['text_from']                        = 'From';
$_['text_to']                          = 'To';
$_['text_loading']                     = 'Loading...';
$_['text_processing']                  = 'Processing...';
$_['text_success_generate']            = 'General Ledger generated successfully!';
$_['text_success_export']              = 'General Ledger exported successfully!';
$_['text_success_compare']             = 'Comparison completed successfully!';

// Ledger Components
$_['text_account_code']                = 'Account Code';
$_['text_account_name']                = 'Account Name';
$_['text_date']                        = 'Date';
$_['text_reference']                   = 'Reference';
$_['text_description']                 = 'Description';
$_['text_debit']                       = 'Debit';
$_['text_credit']                      = 'Credit';
$_['text_balance']                     = 'Balance';
$_['text_running_balance']             = 'Running Balance';
$_['text_opening_balance']             = 'Opening Balance';
$_['text_closing_balance']             = 'Closing Balance';
$_['text_total_debit']                 = 'Total Debit';
$_['text_total_credit']                = 'Total Credit';
$_['text_net_movement']                = 'Net Movement';

// Account Types
$_['text_assets']                      = 'Assets';
$_['text_liabilities']                 = 'Liabilities';
$_['text_equity']                      = 'Equity';
$_['text_revenue']                     = 'Revenue';
$_['text_expenses']                    = 'Expenses';
$_['text_current_assets']              = 'Current Assets';
$_['text_non_current_assets']          = 'Non-Current Assets';
$_['text_current_liabilities']         = 'Current Liabilities';
$_['text_non_current_liabilities']     = 'Non-Current Liabilities';

// Transaction Types
$_['text_journal_entry']               = 'Journal Entry';
$_['text_sales_invoice']               = 'Sales Invoice';
$_['text_purchase_invoice']            = 'Purchase Invoice';
$_['text_payment']                     = 'Payment';
$_['text_receipt']                     = 'Receipt';
$_['text_adjustment']                  = 'Adjustment';
$_['text_transfer']                    = 'Transfer';
$_['text_opening_entry']               = 'Opening Entry';
$_['text_closing_entry']               = 'Closing Entry';

// Column
$_['column_date']                      = 'Date';
$_['column_reference']                 = 'Reference';
$_['column_description']               = 'Description';
$_['column_debit']                     = 'Debit';
$_['column_credit']                    = 'Credit';
$_['column_balance']                   = 'Balance';
$_['column_account_code']              = 'Account Code';
$_['column_account_name']              = 'Account Name';
$_['column_transaction_type']          = 'Transaction Type';
$_['column_branch']                    = 'Branch';
$_['column_user']                      = 'User';

// Entry
$_['entry_date_start']                 = 'Period Start Date';
$_['entry_date_end']                   = 'Period End Date';
$_['entry_account_id']                 = 'Account';
$_['entry_account_group']              = 'Account Group';
$_['entry_branch_id']                  = 'Branch';
$_['entry_include_zero_balances']      = 'Include Zero Balance Accounts';
$_['entry_show_opening_balance']       = 'Show Opening Balance';
$_['entry_show_running_balance']       = 'Show Running Balance';
$_['entry_group_by_account']           = 'Group by Account';
$_['entry_export_format']              = 'Export Format';

// Button
$_['button_generate']                  = 'Generate';
$_['button_export']                    = 'Export';
$_['button_compare']                   = 'Compare';
$_['button_print']                     = 'Print';
$_['button_filter']                    = 'Filter';
$_['button_reset']                     = 'Reset';
$_['button_close']                     = 'Close';
$_['button_search']                    = 'Search';
$_['button_clear']                     = 'Clear';

// Tab
$_['tab_general']                      = 'General';
$_['tab_filters']                      = 'Filters';
$_['tab_options']                      = 'Options';
$_['tab_accounts']                     = 'Accounts';
$_['tab_summary']                      = 'Summary';
$_['tab_details']                      = 'Details';

// Help
$_['help_date_start']                  = 'Select the start date for general ledger period';
$_['help_date_end']                    = 'Select the end date for general ledger period';
$_['help_account_id']                  = 'Select specific account or leave empty for all accounts';
$_['help_account_group']               = 'Select specific account group';
$_['help_running_balance']             = 'Show running balance after each transaction';

// Error
$_['error_warning']                    = 'Warning: Please check the form carefully for errors!';
$_['error_permission']                 = 'Warning: You do not have permission to access General Ledger!';
$_['error_date_start']                 = 'Period start date is required!';
$_['error_date_end']                   = 'Period end date is required!';
$_['error_date_range']                 = 'Start date must be before end date!';
$_['error_no_data']                    = 'No data for selected period!';
$_['error_export']                     = 'Error exporting data!';
$_['error_form']                       = 'Form data error!';
$_['error_missing_data']               = 'Missing data!';
$_['error_account_not_found']          = 'Account not found!';

// Export Formats
$_['text_excel']                       = 'Excel';
$_['text_pdf']                         = 'PDF';
$_['text_csv']                         = 'CSV';

// Print Options
$_['text_print_title']                 = 'General Ledger';
$_['print_title']                      = 'Print General Ledger';
$_['text_print_date']                  = 'Print Date';
$_['text_print_user']                  = 'Printed By';

// Status Messages
$_['text_generating']                  = 'Generating general ledger...';
$_['text_exporting']                   = 'Exporting data...';
$_['text_comparing']                   = 'Performing comparison...';
$_['text_completed']                   = 'Completed successfully!';

// Summary Information
$_['text_total_accounts']              = 'Total Accounts';
$_['text_total_transactions']          = 'Total Transactions';
$_['text_total_entries']               = 'Total Entries';
$_['text_period_summary']              = 'Period Summary';
$_['text_account_summary']             = 'Account Summary';

// Search and Filter
$_['text_search_account']              = 'Search Accounts';
$_['text_filter_by_type']              = 'Filter by Type';
$_['text_filter_by_group']             = 'Filter by Group';
$_['text_filter_by_branch']            = 'Filter by Branch';
$_['text_all_accounts']                = 'All Accounts';
$_['text_all_groups']                  = 'All Groups';
$_['text_all_branches']                = 'All Branches';

// Balance Types
$_['text_debit_balance']               = 'Debit Balance';
$_['text_credit_balance']              = 'Credit Balance';
$_['text_zero_balance']                = 'Zero Balance';

// Egyptian Accounting Standards
$_['text_eas_compliant']               = 'Compliant with Egyptian Accounting Standards';
$_['text_eta_ready']                   = 'Ready for ETA Integration';
$_['text_egyptian_gaap']               = 'According to Egyptian Generally Accepted Accounting Principles';

// Additional Options
$_['text_show_details']                = 'Show Details';
$_['text_show_summary']                = 'Show Summary';
$_['text_group_by_month']              = 'Group by Month';
$_['text_group_by_quarter']            = 'Group by Quarter';
$_['text_group_by_year']               = 'Group by Year';

// Navigation
$_['text_first_page']                  = 'First Page';
$_['text_last_page']                   = 'Last Page';
$_['text_next_page']                   = 'Next Page';
$_['text_previous_page']               = 'Previous Page';
$_['text_page']                        = 'Page';
$_['text_of']                          = 'of';
$_['text_showing']                     = 'Showing';
$_['text_entries']                     = 'entries';

// Additional Fields
$_['text_no_results']                  = 'No results found';
$_['text_total']                       = 'Total';

// Missing Variables from Audit Report
$_['accounts/general_ledger'] = '';

// New language variables for general ledger controller fixes
$_['log_unauthorized_access_general_ledger'] = 'Unauthorized access attempt to general ledger';
$_['log_view_general_ledger_screen']   = 'View general ledger screen';
$_['log_unauthorized_generate_general_ledger'] = 'Unauthorized general ledger generation attempt';
$_['log_generate_general_ledger_period'] = 'Generate general ledger for period';
$_['text_generate_general_ledger']     = 'Generate general ledger';
$_['text_general_ledger_generated_notification'] = 'General ledger generated for period';
$_['log_view_general_ledger']          = 'View general ledger';
$_['log_unauthorized_export_general_ledger'] = 'Unauthorized general ledger export attempt';
$_['log_export_general_ledger']        = 'Export general ledger';
$_['text_export_general_ledger']       = 'Export general ledger';
$_['text_general_ledger_exported_notification'] = 'General ledger exported in format';
$_['log_unauthorized_print_general_ledger'] = 'Unauthorized general ledger printing attempt';
$_['log_print_general_ledger_period']  = 'Print general ledger for period';
$_['text_print_general_ledger']        = 'Print general ledger';
$_['text_general_ledger_printed_notification'] = 'General ledger printed for period';
$_['error_print_general_ledger']       = 'Error printing general ledger';
$_['error_analyze_general_ledger']     = 'Error analyzing general ledger';
$_['text_home'] = '';
$_['text_visual_analysis'] = '';

// Enhanced performance and analytics variables
$_['text_optimized_ledger']            = 'Optimized Ledger';
$_['text_advanced_analysis']           = 'Advanced Analysis';
$_['text_ledger_analysis']             = 'Ledger Analysis';
$_['text_cache_enabled']               = 'Cache Enabled';
$_['text_report_cached']               = 'Report Cached';
$_['text_most_active_accounts']        = 'Most Active Accounts';
$_['text_monthly_trends']              = 'Monthly Trends';
$_['text_account_type_distribution']   = 'Account Type Distribution';
$_['text_largest_transactions']        = 'Largest Transactions';
$_['text_balance_analysis']            = 'Balance Analysis';
$_['text_transaction_count']           = 'Transaction Count';
$_['text_total_amount']                = 'Total Amount';
$_['text_avg_amount']                  = 'Average Amount';
$_['text_total_debits']                = 'Total Debits';
$_['text_total_credits']               = 'Total Credits';
$_['text_balance_difference']          = 'Balance Difference';
$_['text_is_balanced']                 = 'Is Balanced';
$_['text_balance_percentage']          = 'Balance Percentage';
$_['text_journal_count']               = 'Journal Count';
$_['text_entry_count']                 = 'Entry Count';
$_['text_yes']                         = 'Yes';
$_['text_no']                          = 'No';
$_['text_account_activity']            = 'Account Activity';
$_['text_monthly_summary']             = 'Monthly Summary';
$_['button_advanced_analysis']         = 'Advanced Analysis';
$_['button_visual_analysis']           = 'Visual Analysis';
$_['text_loading_analysis']            = 'Loading ledger analysis...';
$_['text_analysis_ready']              = 'Analysis ready';
?>
