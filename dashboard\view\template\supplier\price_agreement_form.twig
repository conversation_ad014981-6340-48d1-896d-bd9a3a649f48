{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="submit" form="form-price-agreement" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fa fa-save"></i></button>
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pencil"></i> {{ text_form }}</h3>
      </div>
      <div class="panel-body">
        <form action="{{ action }}" method="post" enctype="multipart/form-data" id="form-price-agreement" class="form-horizontal">
          <ul class="nav nav-tabs">
            <li class="active"><a href="#tab-general" data-toggle="tab">{{ tab_general }}</a></li>
            <li><a href="#tab-items" data-toggle="tab">{{ tab_items }}</a></li>
            <li><a href="#tab-terms" data-toggle="tab">{{ tab_terms }}</a></li>
          </ul>
          <div class="tab-content">
            <div class="tab-pane active" id="tab-general">
              <div class="form-group required">
                <label class="col-sm-2 control-label" for="input-agreement-name">{{ entry_agreement_name }}</label>
                <div class="col-sm-10">
                  <input type="text" name="agreement_name" value="{{ agreement_name }}" placeholder="{{ entry_agreement_name }}" id="input-agreement-name" class="form-control" />
                  {% if error_agreement_name %}
                  <div class="text-danger">{{ error_agreement_name }}</div>
                  {% endif %}
                </div>
              </div>
              <div class="form-group required">
                <label class="col-sm-2 control-label" for="input-supplier">{{ entry_supplier }}</label>
                <div class="col-sm-10">
                  <select name="supplier_id" id="input-supplier" class="form-control">
                    <option value="">{{ text_select }}</option>
                    {% for supplier in suppliers %}
                    {% if supplier.supplier_id == supplier_id %}
                    <option value="{{ supplier.supplier_id }}" selected="selected">{{ supplier.name }}</option>
                    {% else %}
                    <option value="{{ supplier.supplier_id }}">{{ supplier.name }}</option>
                    {% endif %}
                    {% endfor %}
                  </select>
                  {% if error_supplier %}
                  <div class="text-danger">{{ error_supplier }}</div>
                  {% endif %}
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-description">{{ entry_description }}</label>
                <div class="col-sm-10">
                  <textarea name="description" rows="5" placeholder="{{ entry_description }}" id="input-description" class="form-control">{{ description }}</textarea>
                </div>
              </div>
              <div class="form-group required">
                <label class="col-sm-2 control-label" for="input-start-date">{{ entry_start_date }}</label>
                <div class="col-sm-10">
                  <div class="input-group date">
                    <input type="text" name="start_date" value="{{ start_date }}" placeholder="{{ entry_start_date }}" data-date-format="YYYY-MM-DD" id="input-start-date" class="form-control" />
                    <span class="input-group-btn">
                    <button type="button" class="btn btn-default"><i class="fa fa-calendar"></i></button>
                    </span>
                  </div>
                  {% if error_start_date %}
                  <div class="text-danger">{{ error_start_date }}</div>
                  {% endif %}
                </div>
              </div>
              <div class="form-group required">
                <label class="col-sm-2 control-label" for="input-end-date">{{ entry_end_date }}</label>
                <div class="col-sm-10">
                  <div class="input-group date">
                    <input type="text" name="end_date" value="{{ end_date }}" placeholder="{{ entry_end_date }}" data-date-format="YYYY-MM-DD" id="input-end-date" class="form-control" />
                    <span class="input-group-btn">
                    <button type="button" class="btn btn-default"><i class="fa fa-calendar"></i></button>
                    </span>
                  </div>
                  {% if error_end_date %}
                  <div class="text-danger">{{ error_end_date }}</div>
                  {% endif %}
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-status">{{ entry_status }}</label>
                <div class="col-sm-10">
                  <select name="status" id="input-status" class="form-control">
                    {% if status %}
                    <option value="1" selected="selected">{{ text_enabled }}</option>
                    <option value="0">{{ text_disabled }}</option>
                    {% else %}
                    <option value="1">{{ text_enabled }}</option>
                    <option value="0" selected="selected">{{ text_disabled }}</option>
                    {% endif %}
                  </select>
                </div>
              </div>
            </div>
            <div class="tab-pane" id="tab-items">
              <div class="table-responsive">
                <table id="price-agreement-items" class="table table-striped table-bordered table-hover">
                  <thead>
                    <tr>
                      <td class="text-left">{{ column_product }}</td>
                      <td class="text-left">{{ column_model }}</td>
                      <td class="text-right">{{ column_quantity_min }}</td>
                      <td class="text-right">{{ column_quantity_max }}</td>
                      <td class="text-right">{{ column_price }}</td>
                      <td class="text-right">{{ column_discount }}</td>
                      <td class="text-left">{{ column_currency }}</td>
                      <td class="text-left">{{ column_status }}</td>
                      <td></td>
                    </tr>
                  </thead>
                  <tbody>
                    {% set price_agreement_item_row = 0 %}
                    {% for price_agreement_item in price_agreement_items %}
                    <tr id="price-agreement-item-row{{ price_agreement_item_row }}">
                      <td class="text-left">
                        <input type="text" name="price_agreement_items[{{ price_agreement_item_row }}][product]" value="{{ price_agreement_item.product_name }}" placeholder="{{ entry_product }}" class="form-control" />
                        <input type="hidden" name="price_agreement_items[{{ price_agreement_item_row }}][product_id]" value="{{ price_agreement_item.product_id }}" />
                      </td>
                      <td class="text-left">{{ price_agreement_item.product_model }}</td>
                      <td class="text-right">
                        <input type="text" name="price_agreement_items[{{ price_agreement_item_row }}][quantity_min]" value="{{ price_agreement_item.quantity_min }}" placeholder="{{ entry_quantity_min }}" class="form-control text-right" />
                      </td>
                      <td class="text-right">
                        <input type="text" name="price_agreement_items[{{ price_agreement_item_row }}][quantity_max]" value="{{ price_agreement_item.quantity_max }}" placeholder="{{ entry_quantity_max }}" class="form-control text-right" />
                      </td>
                      <td class="text-right">
                        <input type="text" name="price_agreement_items[{{ price_agreement_item_row }}][price]" value="{{ price_agreement_item.price }}" placeholder="{{ entry_price }}" class="form-control text-right" />
                      </td>
                      <td class="text-right">
                        <input type="text" name="price_agreement_items[{{ price_agreement_item_row }}][discount_percentage]" value="{{ price_agreement_item.discount_percentage }}" placeholder="{{ entry_discount }}" class="form-control text-right" />
                      </td>
                      <td class="text-left">
                        <select name="price_agreement_items[{{ price_agreement_item_row }}][currency_id]" class="form-control">
                          {% for currency in currencies %}
                          {% if currency.currency_id == price_agreement_item.currency_id %}
                          <option value="{{ currency.currency_id }}" selected="selected">{{ currency.title }}</option>
                          {% else %}
                          <option value="{{ currency.currency_id }}">{{ currency.title }}</option>
                          {% endif %}
                          {% endfor %}
                        </select>
                      </td>
                      <td class="text-left">
                        <select name="price_agreement_items[{{ price_agreement_item_row }}][status]" class="form-control">
                          {% if price_agreement_item.status %}
                          <option value="1" selected="selected">{{ text_enabled }}</option>
                          <option value="0">{{ text_disabled }}</option>
                          {% else %}
                          <option value="1">{{ text_enabled }}</option>
                          <option value="0" selected="selected">{{ text_disabled }}</option>
                          {% endif %}
                        </select>
                      </td>
                      <td class="text-left"><button type="button" onclick="$('#price-agreement-item-row{{ price_agreement_item_row }}').remove();" data-toggle="tooltip" title="{{ button_remove }}" class="btn btn-danger"><i class="fa fa-minus-circle"></i></button></td>
                    </tr>
                    {% set price_agreement_item_row = price_agreement_item_row + 1 %}
                    {% endfor %}
                  </tbody>
                  <tfoot>
                    <tr>
                      <td colspan="8"></td>
                      <td class="text-left"><button type="button" onclick="addItem();" data-toggle="tooltip" title="{{ button_add_item }}" class="btn btn-primary"><i class="fa fa-plus-circle"></i></button></td>
                    </tr>
                  </tfoot>
                </table>
              </div>
            </div>
            <div class="tab-pane" id="tab-terms">
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-terms">{{ entry_terms }}</label>
                <div class="col-sm-10">
                  <textarea name="terms" rows="10" placeholder="{{ entry_terms }}" id="input-terms" class="form-control">{{ terms }}</textarea>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript"><!--
var price_agreement_item_row = {{ price_agreement_item_row }};

function addItem() {
    html  = '<tr id="price-agreement-item-row' + price_agreement_item_row + '">';
    html += '  <td class="text-left">';
    html += '    <input type="text" name="price_agreement_items[' + price_agreement_item_row + '][product]" value="" placeholder="{{ entry_product }}" class="form-control" />';
    html += '    <input type="hidden" name="price_agreement_items[' + price_agreement_item_row + '][product_id]" value="" />';
    html += '  </td>';
    html += '  <td class="text-left"></td>';
    html += '  <td class="text-right">';
    html += '    <input type="text" name="price_agreement_items[' + price_agreement_item_row + '][quantity_min]" value="" placeholder="{{ entry_quantity_min }}" class="form-control text-right" />';
    html += '  </td>';
    html += '  <td class="text-right">';
    html += '    <input type="text" name="price_agreement_items[' + price_agreement_item_row + '][quantity_max]" value="" placeholder="{{ entry_quantity_max }}" class="form-control text-right" />';
    html += '  </td>';
    html += '  <td class="text-right">';
    html += '    <input type="text" name="price_agreement_items[' + price_agreement_item_row + '][price]" value="" placeholder="{{ entry_price }}" class="form-control text-right" />';
    html += '  </td>';
    html += '  <td class="text-right">';
    html += '    <input type="text" name="price_agreement_items[' + price_agreement_item_row + '][discount_percentage]" value="" placeholder="{{ entry_discount }}" class="form-control text-right" />';
    html += '  </td>';
    html += '  <td class="text-left">';
    html += '    <select name="price_agreement_items[' + price_agreement_item_row + '][currency_id]" class="form-control">';
    {% for currency in currencies %}
    html += '      <option value="{{ currency.currency_id }}">{{ currency.title }}</option>';
    {% endfor %}
    html += '    </select>';
    html += '  </td>';
    html += '  <td class="text-left">';
    html += '    <select name="price_agreement_items[' + price_agreement_item_row + '][status]" class="form-control">';
    html += '      <option value="1" selected="selected">{{ text_enabled }}</option>';
    html += '      <option value="0">{{ text_disabled }}</option>';
    html += '    </select>';
    html += '  </td>';
    html += '  <td class="text-left"><button type="button" onclick="$(\'#price-agreement-item-row' + price_agreement_item_row + '\').remove();" data-toggle="tooltip" title="{{ button_remove }}" class="btn btn-danger"><i class="fa fa-minus-circle"></i></button></td>';
    html += '</tr>';
    
    $('#price-agreement-items tbody').append(html);
    
    price_agreement_item_row++;
}

$('.date').datetimepicker({
    language: '{{ datepicker }}',
    pickTime: false
});

// Product autocomplete
$(document).delegate('input[name$=\'[product]\']', 'keydown', function(e) {
    if (e.keyCode == 13) {
        e.preventDefault();
    }
});

$(document).delegate('input[name$=\'[product]\']', 'keyup', function() {
    var element = this;
    
    $(element).autocomplete({
        'source': function(request, response) {
            $.ajax({
                url: 'index.php?route=catalog/product/autocomplete&user_token={{ user_token }}&filter_name=' +  encodeURIComponent(request),
                dataType: 'json',
                success: function(json) {
                    response($.map(json, function(item) {
                        return {
                            label: item['name'],
                            value: item['product_id']
                        }
                    }));
                }
            });
        },
        'select': function(item) {
            $(element).val(item['label']);
            $(element).next('input[type=\'hidden\']').val(item['value']);
        }
    });
});
//--></script>

{{ footer }}
