{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        {% if has_permission %}
        <button type="button" id="button-edit" data-toggle="tooltip" title="{{ button_edit }}" class="btn btn-primary">
          <i class="fa fa-pencil"></i> {{ button_edit }}
        </button>
        <button type="button" id="button-match" data-toggle="tooltip" title="{{ button_match }}" class="btn btn-warning">
          <i class="fa fa-link"></i> {{ button_match }}
        </button>
        <button type="button" id="button-eliminate" data-toggle="tooltip" title="{{ button_eliminate }}" class="btn btn-danger">
          <i class="fa fa-minus-circle"></i> {{ button_eliminate }}
        </button>
        <button type="button" id="button-export" data-toggle="tooltip" title="{{ button_export }}" class="btn btn-success">
          <i class="fa fa-download"></i> {{ button_export }}
        </button>
        {% endif %}
        <a href="{{ back }}" data-toggle="tooltip" title="{{ button_back }}" class="btn btn-default">
          <i class="fa fa-reply"></i> {{ button_back }}
        </a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    {% if success %}
    <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    
    <!-- معلومات المعاملة الأساسية -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-info-circle"></i> {{ text_transaction_info }}</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-md-6">
            <table class="table table-bordered">
              <tr>
                <td><strong>{{ text_transaction_reference }}:</strong></td>
                <td>{{ transaction.transaction_reference }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_transaction_date }}:</strong></td>
                <td>{{ transaction.transaction_date }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_source_company }}:</strong></td>
                <td>{{ transaction.source_company_name }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_target_company }}:</strong></td>
                <td>{{ transaction.target_company_name }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_transaction_type }}:</strong></td>
                <td>{{ transaction.transaction_type }}</td>
              </tr>
            </table>
          </div>
          <div class="col-md-6">
            <table class="table table-bordered">
              <tr>
                <td><strong>{{ text_amount }}:</strong></td>
                <td>{{ transaction.amount }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_currency }}:</strong></td>
                <td>{{ transaction.currency }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_status }}:</strong></td>
                <td>
                  {% if transaction.status == 'pending' %}
                    <span class="label label-warning">{{ text_status_pending }}</span>
                  {% elseif transaction.status == 'matched' %}
                    <span class="label label-info">{{ text_status_matched }}</span>
                  {% elseif transaction.status == 'eliminated' %}
                    <span class="label label-success">{{ text_status_eliminated }}</span>
                  {% else %}
                    <span class="label label-danger">{{ text_status_error }}</span>
                  {% endif %}
                </td>
              </tr>
              <tr>
                <td><strong>{{ text_created_by }}:</strong></td>
                <td>{{ transaction.created_by_name }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_date_created }}:</strong></td>
                <td>{{ transaction.date_created }}</td>
              </tr>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- تفاصيل المطابقة -->
    {% if matching_details %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-link"></i> {{ text_matching_details }}</h3>
      </div>
      <div class="panel-body">
        <div class="table-responsive">
          <table class="table table-bordered table-hover">
            <thead>
              <tr>
                <th>{{ column_matched_transaction }}</th>
                <th>{{ column_matching_date }}</th>
                <th class="text-right">{{ column_matched_amount }}</th>
                <th class="text-right">{{ column_difference }}</th>
                <th>{{ column_matching_status }}</th>
                <th>{{ column_matched_by }}</th>
              </tr>
            </thead>
            <tbody>
              {% for match in matching_details %}
              <tr>
                <td>{{ match.matched_transaction_reference }}</td>
                <td>{{ match.matching_date }}</td>
                <td class="text-right">{{ match.matched_amount }}</td>
                <td class="text-right {% if match.difference != 0 %}text-danger{% else %}text-success{% endif %}">
                  {{ match.difference }}
                </td>
                <td>
                  {% if match.matching_status == 'exact' %}
                    <span class="label label-success">{{ text_exact_match }}</span>
                  {% elseif match.matching_status == 'partial' %}
                    <span class="label label-warning">{{ text_partial_match }}</span>
                  {% else %}
                    <span class="label label-info">{{ text_approximate_match }}</span>
                  {% endif %}
                </td>
                <td>{{ match.matched_by_name }}</td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    </div>
    {% endif %}

    <!-- تفاصيل الإلغاء -->
    {% if elimination_details %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-minus-circle"></i> {{ text_elimination_details }}</h3>
      </div>
      <div class="panel-body">
        <div class="table-responsive">
          <table class="table table-bordered">
            <thead>
              <tr>
                <th>{{ column_elimination_type }}</th>
                <th>{{ column_description }}</th>
                <th class="text-right">{{ column_elimination_amount }}</th>
                <th>{{ column_journal_entry }}</th>
                <th>{{ column_elimination_date }}</th>
                <th>{{ column_status }}</th>
              </tr>
            </thead>
            <tbody>
              {% for elimination in elimination_details %}
              <tr>
                <td>{{ elimination.elimination_type }}</td>
                <td>{{ elimination.description }}</td>
                <td class="text-right">{{ elimination.elimination_amount }}</td>
                <td>
                  {% if elimination.journal_entry_id %}
                    <a href="{{ elimination.journal_entry_link }}" target="_blank">{{ elimination.journal_entry_id }}</a>
                  {% else %}
                    {{ text_not_posted }}
                  {% endif %}
                </td>
                <td>{{ elimination.elimination_date }}</td>
                <td>
                  {% if elimination.status == 'posted' %}
                    <span class="label label-success">{{ text_posted }}</span>
                  {% else %}
                    <span class="label label-warning">{{ text_pending }}</span>
                  {% endif %}
                </td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    </div>
    {% endif %}

    <!-- المعاملات ذات الصلة -->
    {% if related_transactions %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-sitemap"></i> {{ text_related_transactions }}</h3>
      </div>
      <div class="panel-body">
        <div class="table-responsive">
          <table class="table table-bordered table-hover">
            <thead>
              <tr>
                <th>{{ column_transaction_reference }}</th>
                <th>{{ column_transaction_date }}</th>
                <th>{{ column_company }}</th>
                <th class="text-right">{{ column_amount }}</th>
                <th>{{ column_type }}</th>
                <th>{{ column_status }}</th>
                <th>{{ column_action }}</th>
              </tr>
            </thead>
            <tbody>
              {% for related in related_transactions %}
              <tr>
                <td>{{ related.transaction_reference }}</td>
                <td>{{ related.transaction_date }}</td>
                <td>{{ related.company_name }}</td>
                <td class="text-right">{{ related.amount }}</td>
                <td>{{ related.transaction_type }}</td>
                <td>
                  {% if related.status == 'pending' %}
                    <span class="label label-warning">{{ text_status_pending }}</span>
                  {% elseif related.status == 'matched' %}
                    <span class="label label-info">{{ text_status_matched }}</span>
                  {% elseif related.status == 'eliminated' %}
                    <span class="label label-success">{{ text_status_eliminated }}</span>
                  {% endif %}
                </td>
                <td>
                  <a href="{{ related.view_link }}" class="btn btn-xs btn-info">
                    <i class="fa fa-eye"></i> {{ button_view }}
                  </a>
                </td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    </div>
    {% endif %}

    <!-- سجل التدقيق -->
    {% if audit_trail %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-history"></i> {{ text_audit_trail }}</h3>
      </div>
      <div class="panel-body">
        <div class="table-responsive">
          <table class="table table-bordered table-striped">
            <thead>
              <tr>
                <th>{{ column_action }}</th>
                <th>{{ column_description }}</th>
                <th>{{ column_user }}</th>
                <th>{{ column_date }}</th>
              </tr>
            </thead>
            <tbody>
              {% for audit in audit_trail %}
              <tr>
                <td>
                  {% if audit.action == 'created' %}
                    <span class="label label-info">{{ text_created }}</span>
                  {% elseif audit.action == 'matched' %}
                    <span class="label label-warning">{{ text_matched }}</span>
                  {% elseif audit.action == 'eliminated' %}
                    <span class="label label-success">{{ text_eliminated }}</span>
                  {% elseif audit.action == 'modified' %}
                    <span class="label label-primary">{{ text_modified }}</span>
                  {% endif %}
                </td>
                <td>{{ audit.description }}</td>
                <td>{{ audit.user_name }}</td>
                <td>{{ audit.action_date }}</td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    </div>
    {% endif %}

    <!-- ملاحظات -->
    {% if transaction.notes %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-sticky-note"></i> {{ text_notes }}</h3>
      </div>
      <div class="panel-body">
        <p>{{ transaction.notes|nl2br }}</p>
      </div>
    </div>
    {% endif %}

  </div>
</div>

<script type="text/javascript">
$(document).ready(function() {
    // مطابقة المعاملات
    $('#button-match').on('click', function() {
        if (confirm('{{ text_confirm_match }}')) {
            $.ajax({
                url: '{{ match }}',
                type: 'post',
                dataType: 'json',
                success: function(json) {
                    if (json['success']) {
                        location.reload();
                    } else {
                        alert(json['error']);
                    }
                }
            });
        }
    });

    // إلغاء المعاملات
    $('#button-eliminate').on('click', function() {
        if (confirm('{{ text_confirm_eliminate }}')) {
            $.ajax({
                url: '{{ eliminate }}',
                type: 'post',
                dataType: 'json',
                success: function(json) {
                    if (json['success']) {
                        location.reload();
                    } else {
                        alert(json['error']);
                    }
                }
            });
        }
    });

    // تصدير البيانات
    $('#button-export').on('click', function() {
        window.open('{{ export }}', '_blank');
    });

    // تحرير المعاملة
    $('#button-edit').on('click', function() {
        window.location = '{{ edit }}';
    });
});
</script>

{{ footer }}
