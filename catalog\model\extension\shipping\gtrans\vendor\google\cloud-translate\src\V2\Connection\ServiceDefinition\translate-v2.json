{"baseUrl": "https://translation.googleapis.com/language/translate/", "servicePath": "language/translate/", "kind": "discovery#restDescription", "description": "The Google Cloud Translation API lets websites and programs integrate with\n    Google Translate programmatically.", "basePath": "/language/translate/", "id": "translate:v2", "documentationLink": "https://code.google.com/apis/language/translate/v2/getting_started.html", "revision": "20170525", "discoveryVersion": "v1", "schemas": {"TranslationsListResponse": {"id": "TranslationsListResponse", "description": "The main language translation response message.", "type": "object", "properties": {"translations": {"description": "Translations contains list of translation results of given text", "type": "array", "items": {"$ref": "TranslationsResource"}}}}, "TranslateTextRequest": {"description": "The main translation request message for the Cloud Translation API.", "type": "object", "properties": {"target": {"type": "string", "description": "The language to use for translation of the input text, set to one of the\nlanguage codes listed in Language Support."}, "q": {"description": "The input text to translate. Repeat this parameter to perform translation\noperations on multiple text inputs.", "type": "array", "items": {"type": "string"}}, "format": {"type": "string", "description": "The format of the source text, in either HTML (default) or plain-text. A\nvalue of \"html\" indicates HTML and a value of \"text\" indicates plain-text."}, "source": {"description": "The language of the source text, set to one of the language codes listed in\nLanguage Support. If the source language is not specified, the API will\nattempt to identify the source language automatically and return it within\nthe response.", "type": "string"}, "model": {"description": "The `model` type requested for this translation. Valid values are\nlisted in public documentation.", "type": "string"}}, "id": "TranslateTextRequest"}, "DetectLanguageRequest": {"id": "DetectLanguageRequest", "description": "The request message for language detection.", "type": "object", "properties": {"q": {"description": "The input text upon which to perform language detection. Repeat this\nparameter to perform language detection on multiple text inputs.", "type": "array", "items": {"type": "string"}}}}, "LanguagesResource": {"type": "object", "properties": {"language": {"description": "Supported language code, generally consisting of its ISO 639-1\nidentifier. (E.g. 'en', 'ja'). In certain cases, BCP-47 codes including\nlanguage + region identifiers are returned (e.g. 'zh-TW' and 'zh-CH')", "type": "string"}, "name": {"description": "Human readable name of the language localized to the target language.", "type": "string"}}, "id": "LanguagesResource"}, "DetectionsListResponse": {"type": "object", "properties": {"detections": {"description": "A detections contains detection results of several text", "type": "array", "items": {"$ref": "DetectionsResource"}}}, "id": "DetectionsListResponse"}, "GetSupportedLanguagesRequest": {"description": "The request message for discovering supported languages.", "type": "object", "properties": {"target": {"description": "The language to use to return localized, human readable names of supported\nlanguages.", "type": "string"}}, "id": "GetSupportedLanguagesRequest"}, "LanguagesListResponse": {"type": "object", "properties": {"languages": {"type": "array", "items": {"$ref": "LanguagesResource"}, "description": "List of source/target languages supported by the translation API. If target parameter is unspecified, the list is sorted by the ASCII code point order of the language code. If target parameter is specified, the list is sorted by the collation order of the language name in the target language."}}, "id": "LanguagesListResponse"}, "DetectionsResource": {"description": "An array of languages which we detect for the given text The most likely language list first.", "type": "array", "items": {"type": "object", "properties": {"confidence": {"description": "The confidence of the detection result of this language.", "format": "float", "type": "number"}, "isReliable": {"type": "boolean", "description": "A boolean to indicate is the language detection result reliable."}, "language": {"description": "The language we detected.", "type": "string"}}}, "id": "DetectionsResource"}, "TranslationsResource": {"type": "object", "properties": {"model": {"description": "The `model` type used for this translation. Valid values are\nlisted in public documentation. Can be different from requested `model`.\nPresent only if specific model type was explicitly requested.", "type": "string"}, "translatedText": {"description": "Text translated into the target language.", "type": "string"}, "detectedSourceLanguage": {"description": "The source language of the initial request, detected automatically, if\nno source language was passed within the initial request. If the\nsource language was passed, auto-detection of the language will not\noccur and this field will be empty.", "type": "string"}}, "id": "TranslationsResource"}}, "icons": {"x16": "https://www.google.com/images/icons/product/translate-16.png", "x32": "https://www.google.com/images/icons/product/translate-32.png"}, "protocol": "rest", "canonicalName": "Translate", "auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "View and manage your data across Google Cloud Platform services"}, "https://www.googleapis.com/auth/cloud-translation": {"description": "Translate text from one language to another using Google Translate"}}}}, "rootUrl": "https://translation.googleapis.com/", "ownerDomain": "google.com", "name": "translate", "batchPath": "batch/translate", "features": ["dataWrapper"], "title": "Google Cloud Translation API", "ownerName": "Google", "resources": {"detections": {"methods": {"list": {"id": "language.detections.list", "path": "v2/detect", "description": "Detects the language of text within a request.", "httpMethod": "GET", "response": {"$ref": "DetectionsListResponse"}, "parameterOrder": ["q"], "parameters": {"q": {"description": "The input text upon which to perform language detection. Repeat this\nparameter to perform language detection on multiple text inputs.", "type": "string", "required": true, "repeated": true, "location": "query"}}, "scopes": ["https://www.googleapis.com/auth/cloud-translation", "https://www.googleapis.com/auth/cloud-platform"]}, "detect": {"path": "v2/detect", "id": "language.detections.detect", "description": "Detects the language of text within a request.", "request": {"$ref": "DetectLanguageRequest"}, "response": {"$ref": "DetectionsListResponse"}, "parameterOrder": [], "httpMethod": "POST", "parameters": {}, "scopes": ["https://www.googleapis.com/auth/cloud-translation", "https://www.googleapis.com/auth/cloud-platform"]}}}, "languages": {"methods": {"list": {"id": "language.languages.list", "path": "v2/languages", "description": "Returns a list of supported languages for translation.", "response": {"$ref": "LanguagesListResponse"}, "httpMethod": "GET", "scopes": ["https://www.googleapis.com/auth/cloud-translation", "https://www.googleapis.com/auth/cloud-platform"], "parameters": {"target": {"location": "query", "description": "The language to use to return localized, human readable names of supported\nlanguages.", "type": "string"}, "model": {"location": "query", "description": "The model type for which supported languages should be returned.", "type": "string"}}}}}, "translations": {"methods": {"translate": {"response": {"$ref": "TranslationsListResponse"}, "parameterOrder": [], "httpMethod": "POST", "scopes": ["https://www.googleapis.com/auth/cloud-translation", "https://www.googleapis.com/auth/cloud-platform"], "parameters": {}, "path": "v2", "id": "language.translations.translate", "request": {"$ref": "TranslateTextRequest"}, "description": "Translates input text, returning translated text."}, "list": {"path": "v2", "id": "language.translations.list", "description": "Translates input text, returning translated text.", "response": {"$ref": "TranslationsListResponse"}, "parameterOrder": ["q", "target"], "httpMethod": "GET", "parameters": {"target": {"location": "query", "description": "The language to use for translation of the input text, set to one of the\nlanguage codes listed in Language Support.", "type": "string", "required": true}, "format": {"location": "query", "enum": ["html", "text"], "description": "The format of the source text, in either HTML (default) or plain-text. A\nvalue of \"html\" indicates HTML and a value of \"text\" indicates plain-text.", "type": "string", "enumDescriptions": ["Specifies the input is in HTML", "Specifies the input is in plain textual format"]}, "model": {"location": "query", "description": "The `model` type requested for this translation. Valid values are\nlisted in public documentation.", "type": "string"}, "q": {"description": "The input text to translate. Repeat this parameter to perform translation\noperations on multiple text inputs.", "type": "string", "required": true, "repeated": true, "location": "query"}, "source": {"description": "The language of the source text, set to one of the language codes listed in\nLanguage Support. If the source language is not specified, the API will\nattempt to identify the source language automatically and return it within\nthe response.", "type": "string", "location": "query"}, "cid": {"description": "The customization id for translate", "type": "string", "repeated": true, "location": "query"}}, "scopes": ["https://www.googleapis.com/auth/cloud-translation", "https://www.googleapis.com/auth/cloud-platform"]}}}}, "parameters": {"key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "type": "string", "location": "query"}, "access_token": {"location": "query", "description": "OAuth access token.", "type": "string"}, "quotaUser": {"location": "query", "description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters. Overrides userIp if both are provided.", "type": "string"}, "pp": {"description": "Pretty-print response.", "type": "boolean", "default": "true", "location": "query"}, "oauth_token": {"location": "query", "description": "OAuth 2.0 token for the current user.", "type": "string"}, "bearer_token": {"description": "OAuth bearer token.", "type": "string", "location": "query"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "type": "string", "location": "query"}, "prettyPrint": {"location": "query", "description": "Returns response with indentations and line breaks.", "type": "boolean", "default": "true"}, "fields": {"type": "string", "location": "query", "description": "Selector specifying which fields to include in a partial response."}, "uploadType": {"location": "query", "description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "type": "string"}, "callback": {"type": "string", "location": "query", "description": "JSONP"}, "$.xgafv": {"type": "string", "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "enum": ["1", "2"], "description": "V1 error format."}, "alt": {"enum": ["json", "media", "proto"], "type": "string", "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "description": "Data format for response.", "default": "json"}}, "version": "v2"}