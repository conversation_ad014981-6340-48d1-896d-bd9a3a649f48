{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <a href="{{ add }}" class="btn btn-primary"><i class="fa fa-plus"></i> {{ button_add }}</a>
        <button type="button" data-toggle="tooltip" title="{{ button_delete }}" class="btn btn-danger" onclick="confirm('{{ text_confirm }}') ? $('#form-transfer').submit() : false;"><i class="fa fa-trash-o"></i></button>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    {% if success %}
    <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    
    <!-- ملخص التحويلات -->
    <div class="row">
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-primary">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-exchange fa-5x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ summary.total_transfers }}</div>
                <div>{{ text_total_transfers }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-warning">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-clock-o fa-5x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ summary.pending_count }}</div>
                <div>{{ text_pending_transfers }}</div>
              </div>
            </div>
          </div>
          {% if can_approve and summary.pending_count > 0 %}
          <a href="{{ pending_url }}">
            <div class="panel-footer">
              <span class="pull-left">{{ text_view_pending }}</span>
              <span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
              <div class="clearfix"></div>
            </div>
          </a>
          {% endif %}
        </div>
      </div>
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-info">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-truck fa-5x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ summary.shipped_count }}</div>
                <div>{{ text_shipped_transfers }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-success">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-check-circle fa-5x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ summary.completed_count }}</div>
                <div>{{ text_completed_transfers }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- رسم بياني للتحويلات -->
    <div class="row">
      <div class="col-md-8">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-bar-chart"></i> {{ text_transfer_trends }}</h3>
          </div>
          <div class="panel-body">
            <canvas id="transferTrendsChart" width="400" height="200"></canvas>
          </div>
        </div>
      </div>
      <div class="col-md-4">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-pie-chart"></i> {{ text_transfer_status }}</h3>
          </div>
          <div class="panel-body">
            <canvas id="transferStatusChart" width="200" height="200"></canvas>
          </div>
        </div>
      </div>
    </div>
    
    <!-- فلاتر البحث -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-filter"></i> {{ text_filter }}</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-sm-3">
            <div class="form-group">
              <label class="control-label" for="input-reference">{{ entry_reference }}</label>
              <input type="text" name="filter_reference" value="{{ filter_reference }}" placeholder="{{ entry_reference }}" id="input-reference" class="form-control" />
            </div>
          </div>
          <div class="col-sm-3">
            <div class="form-group">
              <label class="control-label" for="input-from-branch">{{ entry_from_branch }}</label>
              <select name="filter_from_branch_id" id="input-from-branch" class="form-control">
                <option value=""></option>
                {% for branch in branches %}
                {% if branch.branch_id == filter_from_branch_id %}
                <option value="{{ branch.branch_id }}" selected="selected">{{ branch.name }}</option>
                {% else %}
                <option value="{{ branch.branch_id }}">{{ branch.name }}</option>
                {% endif %}
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="col-sm-3">
            <div class="form-group">
              <label class="control-label" for="input-to-branch">{{ entry_to_branch }}</label>
              <select name="filter_to_branch_id" id="input-to-branch" class="form-control">
                <option value=""></option>
                {% for branch in branches %}
                {% if branch.branch_id == filter_to_branch_id %}
                <option value="{{ branch.branch_id }}" selected="selected">{{ branch.name }}</option>
                {% else %}
                <option value="{{ branch.branch_id }}">{{ branch.name }}</option>
                {% endif %}
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="col-sm-3">
            <div class="form-group">
              <label class="control-label" for="input-status">{{ entry_status }}</label>
              <select name="filter_status" id="input-status" class="form-control">
                <option value=""></option>
                <option value="pending" {% if filter_status == 'pending' %}selected="selected"{% endif %}>{{ text_pending }}</option>
                <option value="approved" {% if filter_status == 'approved' %}selected="selected"{% endif %}>{{ text_approved }}</option>
                <option value="shipped" {% if filter_status == 'shipped' %}selected="selected"{% endif %}>{{ text_shipped }}</option>
                <option value="received" {% if filter_status == 'received' %}selected="selected"{% endif %}>{{ text_received }}</option>
                <option value="completed" {% if filter_status == 'completed' %}selected="selected"{% endif %}>{{ text_completed }}</option>
              </select>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-sm-3">
            <div class="form-group">
              <label class="control-label" for="input-date-from">{{ entry_date_from }}</label>
              <div class="input-group date">
                <input type="text" name="filter_date_from" value="{{ filter_date_from }}" placeholder="{{ entry_date_from }}" data-date-format="YYYY-MM-DD" id="input-date-from" class="form-control" />
                <span class="input-group-btn">
                <button type="button" class="btn btn-default"><i class="fa fa-calendar"></i></button>
                </span>
              </div>
            </div>
          </div>
          <div class="col-sm-3">
            <div class="form-group">
              <label class="control-label" for="input-date-to">{{ entry_date_to }}</label>
              <div class="input-group date">
                <input type="text" name="filter_date_to" value="{{ filter_date_to }}" placeholder="{{ entry_date_to }}" data-date-format="YYYY-MM-DD" id="input-date-to" class="form-control" />
                <span class="input-group-btn">
                <button type="button" class="btn btn-default"><i class="fa fa-calendar"></i></button>
                </span>
              </div>
            </div>
          </div>
          <div class="col-sm-3">
            <div class="form-group">
              <label class="control-label" for="input-priority">{{ entry_priority }}</label>
              <select name="filter_priority" id="input-priority" class="form-control">
                <option value=""></option>
                <option value="low" {% if filter_priority == 'low' %}selected="selected"{% endif %}>{{ text_priority_low }}</option>
                <option value="normal" {% if filter_priority == 'normal' %}selected="selected"{% endif %}>{{ text_priority_normal }}</option>
                <option value="high" {% if filter_priority == 'high' %}selected="selected"{% endif %}>{{ text_priority_high }}</option>
                <option value="urgent" {% if filter_priority == 'urgent' %}selected="selected"{% endif %}>{{ text_priority_urgent }}</option>
              </select>
            </div>
          </div>
          <div class="col-sm-3">
            <div class="form-group">
              <label class="control-label">&nbsp;</label>
              <div>
                <button type="button" id="button-filter" class="btn btn-primary"><i class="fa fa-search"></i> {{ button_filter }}</button>
                <button type="button" id="button-clear" class="btn btn-default"><i class="fa fa-eraser"></i> {{ button_clear }}</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- جدول التحويلات -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-list"></i> {{ text_list }}</h3>
      </div>
      <div class="panel-body">
        <form action="{{ delete }}" method="post" enctype="multipart/form-data" id="form-transfer">
          <div class="table-responsive">
            <table class="table table-bordered table-hover" id="transfer-table">
              <thead>
                <tr>
                  <td style="width: 1px;" class="text-center"><input type="checkbox" onclick="$('input[name*=\\'selected\\']').prop('checked', this.checked);\" /></td>
                  <td class="text-left">{{ column_reference }}</td>
                  <td class="text-left">{{ column_from_branch }}</td>
                  <td class="text-left">{{ column_to_branch }}</td>
                  <td class="text-center">{{ column_priority }}</td>
                  <td class="text-right">{{ column_products }}</td>
                  <td class="text-right">{{ column_quantity }}</td>
                  {% if can_view_cost %}
                  <td class="text-right">{{ column_value }}</td>
                  {% endif %}
                  <td class="text-center">{{ column_progress }}</td>
                  <td class="text-left">{{ column_date_added }}</td>
                  <td class="text-center">{{ column_status }}</td>
                  <td class="text-right">{{ column_action }}</td>
                </tr>
              </thead>
              <tbody>
                {% if transfers %}
                {% for transfer in transfers %}
                <tr class="{{ transfer.status_class }}">
                  <td class="text-center">{% if transfer.transfer_id in selected %}
                    <input type="checkbox" name="selected[]" value="{{ transfer.transfer_id }}" checked="checked" />
                    {% else %}
                    <input type="checkbox" name="selected[]" value="{{ transfer.transfer_id }}" />
                    {% endif %}</td>
                  <td class="text-left">{{ transfer.reference }}</td>
                  <td class="text-left">{{ transfer.from_branch_name }}</td>
                  <td class="text-left">{{ transfer.to_branch_name }}</td>
                  <td class="text-center">
                    <span class="label label-{{ transfer.priority_class }}">{{ transfer.priority_text }}</span>
                  </td>
                  <td class="text-right">{{ transfer.total_products }}</td>
                  <td class="text-right">{{ transfer.total_quantity }}</td>
                  {% if can_view_cost %}
                  <td class="text-right">{{ transfer.total_value }}</td>
                  {% endif %}
                  <td class="text-center">
                    <div class="progress">
                      <div class="progress-bar progress-bar-{{ transfer.progress_class }}" role="progressbar" style="width: {{ transfer.progress_percentage }}%">
                        {{ transfer.progress_percentage }}%
                      </div>
                    </div>
                  </td>
                  <td class="text-left">{{ transfer.date_added }}</td>
                  <td class="text-center">
                    <span class="label label-{{ transfer.status_class }}">{{ transfer.status_text }}</span>
                  </td>
                  <td class="text-right">
                    <div class="btn-group">
                      <a href="{{ transfer.view }}" data-toggle="tooltip" title="{{ button_view }}" class="btn btn-info btn-sm"><i class="fa fa-eye"></i></a>
                      {% if transfer.status == 'pending' and can_approve %}
                        <button type="button" data-toggle="tooltip" title="{{ button_approve }}" class="btn btn-success btn-sm" onclick="approveTransfer('{{ transfer.transfer_id }}')"><i class="fa fa-check"></i></button>
                      {% endif %}
                      {% if transfer.status == 'approved' %}
                        <button type="button" data-toggle="tooltip" title="{{ button_ship }}" class="btn btn-primary btn-sm" onclick="shipTransfer('{{ transfer.transfer_id }}')"><i class="fa fa-truck"></i></button>
                      {% endif %}
                      {% if transfer.status == 'shipped' %}
                        <button type="button" data-toggle="tooltip" title="{{ button_receive }}" class="btn btn-warning btn-sm" onclick="receiveTransfer('{{ transfer.transfer_id }}')"><i class="fa fa-download"></i></button>
                      {% endif %}
                      {% if transfer.can_edit %}
                        <a href="{{ transfer.edit }}" data-toggle="tooltip" title="{{ button_edit }}" class="btn btn-default btn-sm"><i class="fa fa-pencil"></i></a>
                      {% endif %}
                    </div>
                  </td>
                </tr>
                {% endfor %}
                {% else %}
                <tr>
                  <td class="text-center" colspan="{% if can_view_cost %}12{% else %}11{% endif %}">{{ text_no_results }}</td>
                </tr>
                {% endif %}
              </tbody>
            </table>
          </div>
        </form>
        <div class="row">
          <div class="col-sm-6 text-left">{{ pagination }}</div>
          <div class="col-sm-6 text-right">{{ results }}</div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
.huge {
  font-size: 40px;
}

.panel-primary > .panel-heading {
  color: white;
}

.label-low {
  background-color: #5cb85c;
}

.label-normal {
  background-color: #5bc0de;
}

.label-high {
  background-color: #f0ad4e;
}

.label-urgent {
  background-color: #d9534f;
}

.label-pending {
  background-color: #f0ad4e;
}

.label-approved {
  background-color: #5bc0de;
}

.label-shipped {
  background-color: #337ab7;
}

.label-received {
  background-color: #5cb85c;
}

.label-completed {
  background-color: #449d44;
}

tr.warning {
  background-color: #fcf8e3 !important;
}

tr.info {
  background-color: #d9edf7 !important;
}

tr.success {
  background-color: #dff0d8 !important;
}

.progress {
  margin-bottom: 0;
}
</style>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script type="text/javascript"><!--
// Initialize DataTables
$(document).ready(function() {
    $('#transfer-table').DataTable({
        "paging": false,
        "info": false,
        "searching": false,
        "order": [[ 9, "desc" ]]
    });
    
    // Initialize Charts
    initializeCharts();
});

// Filter functionality
$('#button-filter').on('click', function() {
    url = 'index.php?route=inventory/stock_transfer&user_token={{ user_token }}';
    
    var filter_reference = $('input[name=\\'filter_reference\\']').val();
    if (filter_reference) {
        url += '&filter_reference=' + encodeURIComponent(filter_reference);
    }
    
    var filter_from_branch_id = $('select[name=\\'filter_from_branch_id\\']').val();
    if (filter_from_branch_id) {
        url += '&filter_from_branch_id=' + encodeURIComponent(filter_from_branch_id);
    }
    
    var filter_to_branch_id = $('select[name=\\'filter_to_branch_id\\']').val();
    if (filter_to_branch_id) {
        url += '&filter_to_branch_id=' + encodeURIComponent(filter_to_branch_id);
    }
    
    var filter_status = $('select[name=\\'filter_status\\']').val();
    if (filter_status) {
        url += '&filter_status=' + encodeURIComponent(filter_status);
    }
    
    var filter_date_from = $('input[name=\\'filter_date_from\\']').val();
    if (filter_date_from) {
        url += '&filter_date_from=' + encodeURIComponent(filter_date_from);
    }
    
    var filter_date_to = $('input[name=\\'filter_date_to\\']').val();
    if (filter_date_to) {
        url += '&filter_date_to=' + encodeURIComponent(filter_date_to);
    }
    
    var filter_priority = $('select[name=\\'filter_priority\\']').val();
    if (filter_priority) {
        url += '&filter_priority=' + encodeURIComponent(filter_priority);
    }

    location = url;
});

$('#button-clear').on('click', function() {
    $('input[name=\\'filter_reference\\']').val('');
    $('select[name=\\'filter_from_branch_id\\']').val('');
    $('select[name=\\'filter_to_branch_id\\']').val('');
    $('select[name=\\'filter_status\\']').val('');
    $('input[name=\\'filter_date_from\\']').val('');
    $('input[name=\\'filter_date_to\\']').val('');
    $('select[name=\\'filter_priority\\']').val('');
    
    location = 'index.php?route=inventory/stock_transfer&user_token={{ user_token }}';
});

// Date picker
$('.date').datetimepicker({
    language: '{{ datepicker }}',
    pickTime: false
});

// Transfer actions
function approveTransfer(transfer_id) {
    if (confirm('{{ text_confirm_approve }}')) {
        $.ajax({
            url: 'index.php?route=inventory/stock_transfer/approve&user_token={{ user_token }}',
            type: 'post',
            data: { transfer_id: transfer_id },
            dataType: 'json',
            success: function(json) {
                if (json.success) {
                    location.reload();
                }
                if (json.error) {
                    alert(json.error);
                }
            }
        });
    }
}

function shipTransfer(transfer_id) {
    if (confirm('{{ text_confirm_ship }}')) {
        $.ajax({
            url: 'index.php?route=inventory/stock_transfer/ship&user_token={{ user_token }}',
            type: 'post',
            data: { transfer_id: transfer_id },
            dataType: 'json',
            success: function(json) {
                if (json.success) {
                    location.reload();
                }
                if (json.error) {
                    alert(json.error);
                }
            }
        });
    }
}

function receiveTransfer(transfer_id) {
    window.location = 'index.php?route=inventory/stock_transfer/receive&user_token={{ user_token }}&transfer_id=' + transfer_id;
}

// Initialize Charts
function initializeCharts() {
    // Transfer Trends Chart
    var ctx1 = document.getElementById('transferTrendsChart').getContext('2d');
    var transferTrendsChart = new Chart(ctx1, {
        type: 'line',
        data: {
            labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
            datasets: [{
                label: 'التحويلات المكتملة',
                data: [12, 19, 3, 5, 2, 3],
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'top',
                }
            }
        }
    });
    
    // Transfer Status Chart
    var ctx2 = document.getElementById('transferStatusChart').getContext('2d');
    var transferStatusChart = new Chart(ctx2, {
        type: 'doughnut',
        data: {
            labels: ['معلق', 'معتمد', 'مشحون', 'مستلم', 'مكتمل'],
            datasets: [{
                data: [{{ summary.pending_count }}, {{ summary.approved_count }}, {{ summary.shipped_count }}, {{ summary.received_count }}, {{ summary.completed_count }}],
                backgroundColor: [
                    '#f0ad4e',
                    '#5bc0de',
                    '#337ab7',
                    '#5cb85c',
                    '#449d44'
                ]
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom',
                }
            }
        }
    });
}
//--></script>

{{ footer }}