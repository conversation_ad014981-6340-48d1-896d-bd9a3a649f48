{{ header }}
<div id="account-newsletter" class="container" style="margin-top:10px">
  <div class="row">{{ column_left }}
    <div id="content" class="col" style="padding-left:10px;padding-right:10px;padding-top:10px">{{ content_top }}
      <form id="form-newsletter" action="{{ save }}" method="post" data-oc-toggle="ajax">
        <fieldset>
          <div class="row mb-3 mb-0">
            <label class="col-md-3 col-form-label">{{ entry_newsletter }}</label>
            <div class="col-md-9">
              <div class="form-check form-switch form-switch-lg">
                <input type="hidden" name="newsletter" value="0"/>
                <input type="checkbox" name="newsletter" value="1" id="input-newsletter" class="form-check-input"{% if newsletter %} checked{% endif %}/>
              </div>
            </div>
          </div>
        </fieldset>
        <div class="row">
          <div class="col">
            <a href="{{ back }}" class="btn btn-light">{{ button_back }}</a>
          </div>
          <div class="col text-end">
            <button type="submit" class="btn btn-primary">{{ button_continue }}</button>
          </div>
        </div>
      </form>
      {{ content_bottom }}
    </div>
    {{ column_right }}</div>
</div>
{{ footer }}
