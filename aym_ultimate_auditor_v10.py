#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AYM Ultimate Auditor v10.0 - Advanced System Analysis Tool
==========================================================

تطوير: Kiro AI Assistant
التاريخ: يناير 2025
الإصدار: 10.0 (محسن من v9)

الميزات الجديدة في v10:
- تحليل Bootstrap versions
- فحص Bundle Management System
- تحليل WAC Calculator integration
- فحص PC Builder compatibility
- تحليل Target Categories support
- تقارير JSON متقدمة مع recommendations
- فحص Database schema consistency
- تحليل Performance bottlenecks
"""

import os
import re
import json
import time
import hashlib
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Tuple, Any
from dataclasses import dataclass, asdict
import argparse

@dataclass
class AuditResult:
    """نتيجة فحص ملف واحد (محسن من v9)"""
    file_path: str
    score: float
    issues: List[str]
    recommendations: List[str]
    dependencies: List[str]
    bootstrap_version: str = ""
    mvc_compliance: bool = False
    central_service_integration: float = 0.0
    bundle_support: bool = False
    wac_integration: bool = False
    target_category_support: str = ""
    language_compliance: float = 0.0  # جديد من v9
    language_analysis: Dict = None  # جديد من v9

@dataclass
class SystemAudit:
    """نتيجة فحص النظام الكامل (محسن من v9)"""
    timestamp: str
    total_files: int
    average_score: float
    critical_issues: List[str]
    recommendations: List[str]
    bootstrap_analysis: Dict[str, Any]
    dependency_graph: Dict[str, List[str]]
    mvc_compliance_rate: float
    language_compliance_rate: float  # جديد من v9
    language_system_status: Dict[str, Any]  # جديد من v9
    bundle_system_status: Dict[str, Any]
    wac_system_status: Dict[str, Any]
    target_categories_status: Dict[str, Any]
    performance_analysis: Dict[str, Any]

class AYMUltimateAuditorV10:
    """
    أداة التحليل الشاملة المتقدمة لنظام AYM ERP v10
    """
    
    def __init__(self, root_path: str = "."):
        self.root_path = Path(root_path)
        self.results: List[AuditResult] = []
        self.bootstrap_files = {
            "dashboard": [],
            "catalog": []
        }
        self.bundle_files = []
        self.wac_files = []
        self.target_category_files = {
            "clothing": [],
            "perfumes": [],
            "electronics": [],
            "supermarket": [],
            "mall": []
        }
        
        # معايير التقييم المتقدمة (محسنة من v9)
        self.evaluation_criteria = {
            "central_service_integration": 20,
            "mvc_compliance": 15,
            "language_compliance": 20,  # جديد من v9
            "bootstrap_consistency": 10,
            "bundle_system_support": 10,
            "wac_integration": 10,
            "target_category_support": 10,
            "performance_optimization": 5
        }
        
        # أنماط البحث المتقدمة (محسنة من v9)
        self.patterns = {
            "central_service_manager": [
                r"load->model\(['\"]core/central_service_manager['\"]\)",
                r"model_core_central_service_manager",
                r"Central Service Manager",
                r"centralServiceManager"
            ],
            "bootstrap_3": [
                r"Bootstrap\s+v?3\.\d+\.\d+",
                r"bootstrap\.min\.css.*3\.",
                r"col-xs-", r"col-sm-", r"col-md-", r"col-lg-",
                r"panel-", r"glyphicon-"
            ],
            "bootstrap_5": [
                r"Bootstrap\s+v?5\.\d+\.\d+",
                r"bootstrap\.min\.css.*5\.",
                r"col-", r"row-cols-", r"g-\d+",
                r"btn-outline-", r"text-bg-"
            ],
            "bundle_system": [
                r"bundle_management",
                r"product_bundle",
                r"getBundles\(",
                r"bundle_id",
                r"bundle_items"
            ],
            "wac_calculator": [
                r"WAC",
                r"weighted.*average.*cost",
                r"calculateWAC",
                r"wac_calculator",
                r"average_cost"
            ],
            "target_categories": {
                "clothing": [r"size", r"color", r"fashion", r"clothing", r"apparel"],
                "perfumes": [r"perfume", r"fragrance", r"concentration", r"ml", r"oz"],
                "electronics": [r"pc.*builder", r"compatibility", r"cpu", r"ram", r"motherboard"],
                "supermarket": [r"barcode", r"expiry", r"batch", r"grocery"],
                "mall": [r"branch", r"loyalty", r"points", r"rewards"]
            },
            # أنماط اللغات المتقدمة (من v9)
            "language_patterns": {
                "controller_patterns": [
                    r'\$this->language->get\(["\']([^"\']+)["\']\)',
                    r'\$this->load->language\(["\']([^"\']+)["\']\)',
                    r'\$language\[["\']([^"\']+)["\']\]',
                    r'\$_\[["\']([^"\']+)["\']\]'
                ],
                "view_patterns": [
                    r'\{\{\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*\}\}',
                    r'\$([a-zA-Z_][a-zA-Z0-9_]*)',
                    r'lang\(["\']([^"\']+)["\']\)',
                    r'trans\(["\']([^"\']+)["\']\)'
                ],
                "model_patterns": [
                    r'\$this->language->get\(["\']([^"\']+)["\']\)',
                    r'language\(["\']([^"\']+)["\']\)'
                ],
                "language_file_patterns": [
                    r'\$_\[["\']([^"\']+)["\']\]',
                    r'["\']([^"\']+)["\']\s*=>',
                    r'define\(["\']([^"\']+)["\']\s*,',
                    r'const\s+([A-Z_]+)\s*=',
                    r'LANG_([A-Z_]+)',
                    r'TEXT_([A-Z_]+)'
                ],
                "hardcoded_patterns": [
                    r'["\'][^"\']*[\u0600-\u06FF][^"\']*["\']',  # Arabic text
                    r'["\'][A-Za-z\s]{15,}["\']',  # Long English text
                    r'echo\s+["\'][^"\']+["\']',  # Direct echo
                    r'print\s+["\'][^"\']+["\']',  # Direct print
                    r'printf\s*\(["\'][^"\']+["\']',  # Printf statements
                    r'sprintf\s*\(["\'][^"\']+["\']',  # Sprintf statements
                    r'die\s*\(["\'][^"\']+["\']',  # Die statements with text
                    r'exit\s*\(["\'][^"\']+["\']'  # Exit statements with text
                ]
            }
        }

    def scan_directory(self, directory: str, extensions: List[str] = None) -> List[Path]:
        """مسح المجلد للملفات المطلوبة"""
        if extensions is None:
            extensions = ['.php', '.twig', '.js', '.css', '.scss']
        
        files = []
        dir_path = self.root_path / directory
        
        if not dir_path.exists():
            return files
            
        for ext in extensions:
            files.extend(dir_path.rglob(f"*{ext}"))
            
        return files

    def analyze_bootstrap_version(self, file_path: Path, content: str) -> str:
        """تحليل إصدار Bootstrap المستخدم"""
        bootstrap_3_score = 0
        bootstrap_5_score = 0
        
        for pattern in self.patterns["bootstrap_3"]:
            bootstrap_3_score += len(re.findall(pattern, content, re.IGNORECASE))
            
        for pattern in self.patterns["bootstrap_5"]:
            bootstrap_5_score += len(re.findall(pattern, content, re.IGNORECASE))
        
        if bootstrap_3_score > bootstrap_5_score:
            return "3.x"
        elif bootstrap_5_score > bootstrap_3_score:
            return "5.x"
        else:
            return "unknown"

    def check_central_service_integration(self, content: str) -> float:
        """فحص التكامل مع Central Service Manager"""
        integration_score = 0
        total_checks = len(self.patterns["central_service_manager"])
        
        for pattern in self.patterns["central_service_manager"]:
            if re.search(pattern, content, re.IGNORECASE):
                integration_score += 1
                
        return (integration_score / total_checks) * 100

    def check_mvc_compliance(self, file_path: Path, content: str) -> bool:
        """فحص الالتزام بنمط MVC"""
        path_str = str(file_path).lower()
        
        # فحص هيكل المجلدات
        if "controller" in path_str:
            return "class" in content and "Controller" in content
        elif "model" in path_str:
            return "class" in content and "Model" in content
        elif "view" in path_str or ".twig" in path_str:
            return True  # Views are generally compliant
        
        return False

    def check_bundle_support(self, content: str) -> bool:
        """فحص دعم نظام الباقات"""
        for pattern in self.patterns["bundle_system"]:
            if re.search(pattern, content, re.IGNORECASE):
                return True
        return False

    def check_wac_integration(self, content: str) -> bool:
        """فحص التكامل مع نظام WAC"""
        for pattern in self.patterns["wac_calculator"]:
            if re.search(pattern, content, re.IGNORECASE):
                return True
        return False

    def check_target_category_support(self, content: str) -> str:
        """فحص دعم الفئات المستهدفة"""
        supported_categories = []

        for category, patterns in self.patterns["target_categories"].items():
            for pattern in patterns:
                if re.search(pattern, content, re.IGNORECASE):
                    supported_categories.append(category)
                    break

        return ",".join(supported_categories) if supported_categories else "none"

    def analyze_language_compliance(self, file_path: Path, content: str) -> Dict[str, Any]:
        """تحليل الامتثال للغات (محسن من v9)"""
        lang_analysis = {
            'used_variables': set(),
            'ar_variables': set(),
            'en_variables': set(),
            'missing_ar': set(),
            'missing_en': set(),
            'unused_ar': set(),
            'unused_en': set(),
            'hardcoded_text': [],
            'compliance_score': 0,
            'language_files_found': {'ar': [], 'en': []},
            'encoding_analysis': {},
            'pattern_analysis': [],
            'rtl_support': False,
            'i18n_readiness': 0
        }

        try:
            # استخراج المتغيرات المستخدمة
            file_type = self.get_file_type(file_path)
            patterns = self.patterns["language_patterns"].get(f"{file_type}_patterns", [])

            for pattern in patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                lang_analysis['used_variables'].update(matches)

            # فحص النصوص المدمجة
            for pattern in self.patterns["language_patterns"]["hardcoded_patterns"]:
                matches = re.findall(pattern, content, re.IGNORECASE)
                lang_analysis['hardcoded_text'].extend(matches)

            # تحليل ملفات اللغة
            route = self.extract_route_from_path(file_path)
            if route:
                lang_analysis.update(self.analyze_language_files(route, lang_analysis['used_variables']))

            # حساب نتيجة الامتثال
            lang_analysis['compliance_score'] = self.calculate_language_compliance_score(lang_analysis)
            lang_analysis['i18n_readiness'] = self.calculate_i18n_readiness(lang_analysis)

        except Exception as e:
            lang_analysis['error'] = str(e)

        return lang_analysis

    def get_file_type(self, file_path: Path) -> str:
        """تحديد نوع الملف"""
        path_str = str(file_path).lower()
        if "controller" in path_str:
            return "controller"
        elif "model" in path_str:
            return "model"
        elif "view" in path_str or ".twig" in path_str:
            return "view"
        return "controller"  # افتراضي

    def extract_route_from_path(self, file_path: Path) -> str:
        """استخراج المسار من مسار الملف"""
        path_parts = file_path.parts
        try:
            if "controller" in path_parts:
                controller_index = path_parts.index("controller")
                route_parts = path_parts[controller_index + 1:]
                route = "/".join(route_parts).replace(".php", "")
                return route
        except:
            pass
        return ""

    def analyze_language_files(self, route: str, used_variables: set) -> Dict[str, Any]:
        """تحليل ملفات اللغة للمسار المحدد"""
        result = {
            'ar_variables': set(),
            'en_variables': set(),
            'missing_ar': set(),
            'missing_en': set(),
            'unused_ar': set(),
            'unused_en': set(),
            'language_files_found': {'ar': [], 'en': []},
            'encoding_analysis': {}
        }

        # مسارات ملفات اللغة
        ar_file = self.root_path / "dashboard" / "language" / "ar" / f"{route}.php"
        en_file = self.root_path / "dashboard" / "language" / "en-gb" / f"{route}.php"

        # تحليل الملف العربي
        if ar_file.exists():
            result['language_files_found']['ar'].append(str(ar_file))
            try:
                ar_content = ar_file.read_text(encoding='utf-8', errors='ignore')
                result['ar_variables'] = self.extract_language_variables(ar_content)
                result['encoding_analysis']['ar'] = {'encoding': 'utf-8', 'file_size': len(ar_content)}
            except Exception as e:
                result['encoding_analysis']['ar'] = {'error': str(e)}

        # تحليل الملف الإنجليزي
        if en_file.exists():
            result['language_files_found']['en'].append(str(en_file))
            try:
                en_content = en_file.read_text(encoding='utf-8', errors='ignore')
                result['en_variables'] = self.extract_language_variables(en_content)
                result['encoding_analysis']['en'] = {'encoding': 'utf-8', 'file_size': len(en_content)}
            except Exception as e:
                result['encoding_analysis']['en'] = {'error': str(e)}

        # حساب المتغيرات المفقودة والغير مستخدمة
        result['missing_ar'] = used_variables - result['ar_variables']
        result['missing_en'] = used_variables - result['en_variables']
        result['unused_ar'] = result['ar_variables'] - used_variables
        result['unused_en'] = result['en_variables'] - used_variables

        return result

    def extract_language_variables(self, content: str) -> set:
        """استخراج متغيرات اللغة من المحتوى"""
        variables = set()

        # إزالة التعليقات
        content = re.sub(r'/\*.*?\*/', '', content, flags=re.DOTALL)
        content = re.sub(r'//.*', '', content)

        # استخراج المتغيرات
        for pattern in self.patterns["language_patterns"]["language_file_patterns"]:
            matches = re.findall(pattern, content)
            variables.update(matches)

        return variables

    def calculate_language_compliance_score(self, lang_analysis: Dict) -> float:
        """حساب نتيجة امتثال اللغة"""
        total_used = len(lang_analysis['used_variables'])
        if total_used == 0:
            return 100.0

        missing_penalty = len(lang_analysis['missing_ar']) + len(lang_analysis['missing_en'])
        hardcoded_penalty = len(lang_analysis['hardcoded_text'])
        unused_penalty = (len(lang_analysis['unused_ar']) + len(lang_analysis['unused_en'])) * 0.5

        score = max(0, min(100, 100 - (missing_penalty * 5) - (hardcoded_penalty * 3) - unused_penalty))
        return score

    def analyze_file(self, file_path: Path) -> AuditResult:
        """تحليل ملف واحد"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
        except Exception as e:
            return AuditResult(
                file_path=str(file_path),
                score=0.0,
                issues=[f"خطأ في قراءة الملف: {str(e)}"],
                recommendations=["تحقق من صحة الملف وترميزه"],
                dependencies=[]
            )
        
        # التحليلات المختلفة
        bootstrap_version = self.analyze_bootstrap_version(file_path, content)
        central_service_score = self.check_central_service_integration(content)
        mvc_compliance = self.check_mvc_compliance(file_path, content)
        bundle_support = self.check_bundle_support(content)
        wac_integration = self.check_wac_integration(content)
        target_category_support = self.check_target_category_support(content)
        language_analysis = self.analyze_language_compliance(file_path, content)

        # حساب النتيجة الإجمالية
        score = self.calculate_file_score(
            central_service_score, mvc_compliance, bootstrap_version,
            bundle_support, wac_integration, target_category_support, language_analysis
        )

        # تحديد المشاكل والتوصيات
        issues, recommendations = self.generate_issues_and_recommendations(
            file_path, central_service_score, mvc_compliance, bootstrap_version,
            bundle_support, wac_integration, target_category_support, language_analysis
        )
        
        # استخراج الاعتماديات
        dependencies = self.extract_dependencies(content)
        
        return AuditResult(
            file_path=str(file_path),
            score=score,
            issues=issues,
            recommendations=recommendations,
            dependencies=dependencies,
            bootstrap_version=bootstrap_version,
            mvc_compliance=mvc_compliance,
            central_service_integration=central_service_score,
            bundle_support=bundle_support,
            wac_integration=wac_integration,
            target_category_support=target_category_support,
            language_compliance=language_analysis['compliance_score'],
            language_analysis=language_analysis
        )

    def calculate_file_score(self, central_service_score: float, mvc_compliance: bool,
                           bootstrap_version: str, bundle_support: bool,
                           wac_integration: bool, target_category_support: str,
                           language_analysis: Dict) -> float:
        """حساب نتيجة الملف (محسن من v9)"""
        score = 0.0

        # Central Service Integration (20%)
        score += (central_service_score / 100) * self.evaluation_criteria["central_service_integration"]

        # MVC Compliance (15%)
        if mvc_compliance:
            score += self.evaluation_criteria["mvc_compliance"]

        # Language Compliance (20%) - جديد من v9
        score += (language_analysis['compliance_score'] / 100) * self.evaluation_criteria["language_compliance"]

        # Bootstrap Consistency (10%)
        if bootstrap_version in ["3.x", "5.x"]:
            score += self.evaluation_criteria["bootstrap_consistency"]

        # Bundle System Support (10%)
        if bundle_support:
            score += self.evaluation_criteria["bundle_system_support"]

        # WAC Integration (10%)
        if wac_integration:
            score += self.evaluation_criteria["wac_integration"]

        # Target Category Support (10%)
        if target_category_support != "none":
            score += self.evaluation_criteria["target_category_support"]

        # Performance Optimization (5%)
        # سيتم إضافة تحليل الأداء لاحقاً

        return min(score, 100.0)  # الحد الأقصى 100

    def generate_issues_and_recommendations(self, file_path: Path, central_service_score: float,
                                          mvc_compliance: bool, bootstrap_version: str,
                                          bundle_support: bool, wac_integration: bool,
                                          target_category_support: str, language_analysis: Dict) -> Tuple[List[str], List[str]]:
        """توليد المشاكل والتوصيات (محسن من v9)"""
        issues = []
        recommendations = []

        # فحص التكامل مع Central Service Manager
        if central_service_score < 50:
            issues.append("ضعف التكامل مع Central Service Manager")
            recommendations.append("إضافة استدعاءات Central Service Manager المطلوبة")

        # فحص MVC Compliance
        if not mvc_compliance:
            issues.append("عدم الالتزام بنمط MVC")
            recommendations.append("إعادة هيكلة الكود وفق نمط MVC")

        # فحص Language Compliance (جديد من v9)
        if language_analysis['compliance_score'] < 70:
            issues.append(f"ضعف امتثال اللغات ({language_analysis['compliance_score']:.1f}%)")
            recommendations.append("تحسين ملفات اللغة وإزالة النصوص المدمجة")

        if language_analysis['missing_ar']:
            issues.append(f"متغيرات عربية مفقودة: {len(language_analysis['missing_ar'])}")
            recommendations.append("إضافة المتغيرات المفقودة في ملف اللغة العربية")

        if language_analysis['missing_en']:
            issues.append(f"متغيرات إنجليزية مفقودة: {len(language_analysis['missing_en'])}")
            recommendations.append("إضافة المتغيرات المفقودة في ملف اللغة الإنجليزية")

        if language_analysis['hardcoded_text']:
            issues.append(f"نصوص مدمجة في الكود: {len(language_analysis['hardcoded_text'])}")
            recommendations.append("نقل النصوص المدمجة إلى ملفات اللغة")

        # فحص Bootstrap Version
        if bootstrap_version == "unknown":
            issues.append("إصدار Bootstrap غير محدد أو مختلط")
            recommendations.append("توحيد إصدار Bootstrap المستخدم")

        # فحص Bundle Support
        if not bundle_support and "catalog" in str(file_path):
            issues.append("عدم دعم نظام الباقات")
            recommendations.append("إضافة دعم نظام الباقات للتجارة الإلكترونية")

        # فحص WAC Integration
        if not wac_integration and "inventory" in str(file_path):
            issues.append("عدم التكامل مع نظام WAC")
            recommendations.append("إضافة التكامل مع WAC Calculator")

        # فحص Target Category Support
        if target_category_support == "none":
            issues.append("عدم دعم الفئات المستهدفة")
            recommendations.append("إضافة دعم للفئات المستهدفة (ملابس، عطور، إلكترونيات، إلخ)")

        return issues, recommendations

    def extract_dependencies(self, content: str) -> List[str]:
        """استخراج الاعتماديات من الملف"""
        dependencies = []
        
        # PHP model loading
        model_pattern = r"load->model\(['\"]([^'\"]+)['\"]\)"
        dependencies.extend(re.findall(model_pattern, content))
        
        # Include/require statements
        include_pattern = r"(?:include|require)(?:_once)?\s*['\"]([^'\"]+)['\"]"
        dependencies.extend(re.findall(include_pattern, content))
        
        return list(set(dependencies))  # إزالة المكررات

    def analyze_bootstrap_consistency(self) -> Dict[str, Any]:
        """تحليل اتساق Bootstrap عبر النظام"""
        dashboard_files = self.scan_directory("dashboard", ['.css', '.scss', '.twig'])
        catalog_files = self.scan_directory("catalog", ['.css', '.scss', '.twig'])

        dashboard_bootstrap = {"3.x": 0, "5.x": 0, "unknown": 0}
        catalog_bootstrap = {"3.x": 0, "5.x": 0, "unknown": 0}

        for file_path in dashboard_files:
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                version = self.analyze_bootstrap_version(file_path, content)
                dashboard_bootstrap[version] += 1
            except:
                continue

        for file_path in catalog_files:
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                version = self.analyze_bootstrap_version(file_path, content)
                catalog_bootstrap[version] += 1
            except:
                continue

        return {
            "dashboard": dashboard_bootstrap,
            "catalog": catalog_bootstrap,
            "consistency_score": self.calculate_bootstrap_consistency_score(dashboard_bootstrap, catalog_bootstrap),
            "recommendation": self.get_bootstrap_recommendation(dashboard_bootstrap, catalog_bootstrap)
        }

    def calculate_bootstrap_consistency_score(self, dashboard: Dict, catalog: Dict) -> float:
        """حساب نتيجة اتساق Bootstrap"""
        dashboard_dominant = max(dashboard, key=dashboard.get)
        catalog_dominant = max(catalog, key=catalog.get)

        if dashboard_dominant == "3.x" and catalog_dominant == "5.x":
            return 100.0  # هذا هو المطلوب
        elif dashboard_dominant == catalog_dominant:
            return 75.0   # متسق لكن قد لا يكون الأمثل
        else:
            return 25.0   # غير متسق

    def get_bootstrap_recommendation(self, dashboard: Dict, catalog: Dict) -> str:
        """توصية لـ Bootstrap"""
        dashboard_dominant = max(dashboard, key=dashboard.get)
        catalog_dominant = max(catalog, key=catalog.get)

        if dashboard_dominant == "3.x" and catalog_dominant == "5.x":
            return "ممتاز! Dashboard يستخدم Bootstrap 3.x والواجهة الأمامية تستخدم Bootstrap 5.x"
        elif dashboard_dominant == catalog_dominant == "3.x":
            return "يُنصح بترقية الواجهة الأمامية إلى Bootstrap 5.x للميزات الحديثة"
        elif dashboard_dominant == catalog_dominant == "5.x":
            return "يُنصح بالحفاظ على Bootstrap 3.x في Dashboard للاستقرار"
        else:
            return "يجب توحيد إصدارات Bootstrap حسب الغرض من كل واجهة"

    def analyze_bundle_system_status(self) -> Dict[str, Any]:
        """تحليل حالة نظام الباقات"""
        bundle_files = []
        catalog_files = self.scan_directory("catalog", ['.php', '.twig'])
        dashboard_files = self.scan_directory("dashboard/controller/catalog", ['.php'])

        bundle_support_count = 0
        total_catalog_files = 0

        for file_path in catalog_files + dashboard_files:
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                total_catalog_files += 1
                if self.check_bundle_support(content):
                    bundle_support_count += 1
                    bundle_files.append(str(file_path))
            except:
                continue

        bundle_coverage = (bundle_support_count / total_catalog_files * 100) if total_catalog_files > 0 else 0

        return {
            "files_with_bundle_support": bundle_files,
            "bundle_coverage_percentage": bundle_coverage,
            "total_catalog_files": total_catalog_files,
            "files_with_support": bundle_support_count,
            "status": "excellent" if bundle_coverage > 80 else "good" if bundle_coverage > 50 else "needs_improvement",
            "recommendation": self.get_bundle_recommendation(bundle_coverage)
        }

    def get_bundle_recommendation(self, coverage: float) -> str:
        """توصية لنظام الباقات"""
        if coverage > 80:
            return "نظام الباقات مطبق بشكل ممتاز عبر معظم الملفات"
        elif coverage > 50:
            return "نظام الباقات مطبق جيداً، يُنصح بتوسيع التغطية"
        else:
            return "يجب تطوير وتطبيق نظام الباقات في المزيد من الملفات"

    def analyze_wac_system_status(self) -> Dict[str, Any]:
        """تحليل حالة نظام WAC"""
        wac_files = []
        inventory_files = self.scan_directory("dashboard/controller/inventory", ['.php'])
        inventory_files.extend(self.scan_directory("dashboard/model/inventory", ['.php']))

        wac_support_count = 0
        total_inventory_files = len(inventory_files)

        for file_path in inventory_files:
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                if self.check_wac_integration(content):
                    wac_support_count += 1
                    wac_files.append(str(file_path))
            except:
                continue

        wac_coverage = (wac_support_count / total_inventory_files * 100) if total_inventory_files > 0 else 0

        return {
            "files_with_wac_support": wac_files,
            "wac_coverage_percentage": wac_coverage,
            "total_inventory_files": total_inventory_files,
            "files_with_support": wac_support_count,
            "status": "excellent" if wac_coverage > 70 else "good" if wac_coverage > 40 else "needs_improvement",
            "recommendation": self.get_wac_recommendation(wac_coverage)
        }

    def get_wac_recommendation(self, coverage: float) -> str:
        """توصية لنظام WAC"""
        if coverage > 70:
            return "نظام WAC مطبق بشكل ممتاز في ملفات المخزون"
        elif coverage > 40:
            return "نظام WAC مطبق جيداً، يُنصح بتوسيع التكامل"
        else:
            return "يجب تطوير وتطبيق نظام WAC في جميع ملفات المخزون"

    def analyze_target_categories_status(self) -> Dict[str, Any]:
        """تحليل دعم الفئات المستهدفة"""
        categories_analysis = {}
        all_files = []

        # جمع جميع الملفات ذات الصلة
        all_files.extend(self.scan_directory("dashboard/controller/catalog", ['.php']))
        all_files.extend(self.scan_directory("dashboard/controller/inventory", ['.php']))
        all_files.extend(self.scan_directory("catalog/controller", ['.php']))

        for category in self.target_category_files.keys():
            category_files = []
            category_support_count = 0

            for file_path in all_files:
                try:
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()

                    # فحص دعم الفئة المحددة
                    for pattern in self.patterns["target_categories"][category]:
                        if re.search(pattern, content, re.IGNORECASE):
                            category_files.append(str(file_path))
                            category_support_count += 1
                            break
                except:
                    continue

            coverage = (category_support_count / len(all_files) * 100) if all_files else 0

            categories_analysis[category] = {
                "files_with_support": category_files,
                "coverage_percentage": coverage,
                "files_count": category_support_count,
                "status": "good" if coverage > 20 else "needs_development"
            }

        return {
            "categories": categories_analysis,
            "overall_recommendation": self.get_target_categories_recommendation(categories_analysis),
            "priority_categories": self.get_priority_categories(categories_analysis)
        }

    def get_target_categories_recommendation(self, analysis: Dict) -> str:
        """توصية للفئات المستهدفة"""
        good_categories = [cat for cat, data in analysis.items() if data["status"] == "good"]

        if len(good_categories) >= 3:
            return "دعم جيد للفئات المستهدفة، يُنصح بتطوير الفئات المتبقية"
        elif len(good_categories) >= 1:
            return "دعم متوسط للفئات المستهدفة، يجب تطوير المزيد من الفئات"
        else:
            return "يجب تطوير دعم شامل لجميع الفئات المستهدفة"

    def get_priority_categories(self, analysis: Dict) -> List[str]:
        """تحديد الفئات ذات الأولوية للتطوير"""
        needs_development = [cat for cat, data in analysis.items() if data["status"] == "needs_development"]
        return sorted(needs_development, key=lambda x: analysis[x]["coverage_percentage"], reverse=True)

    def analyze_performance_bottlenecks(self) -> Dict[str, Any]:
        """تحليل اختناقات الأداء"""
        large_files = []
        complex_files = []

        all_files = []
        all_files.extend(self.scan_directory("dashboard", ['.php', '.twig']))
        all_files.extend(self.scan_directory("catalog", ['.php', '.twig']))

        for file_path in all_files:
            try:
                file_size = file_path.stat().st_size

                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                    line_count = len(content.splitlines())

                # ملفات كبيرة (أكثر من 1000 سطر أو 100KB)
                if line_count > 1000 or file_size > 100000:
                    large_files.append({
                        "file": str(file_path),
                        "lines": line_count,
                        "size_kb": round(file_size / 1024, 2)
                    })

                # ملفات معقدة (تحتوي على استعلامات كثيرة أو حلقات معقدة)
                query_count = len(re.findall(r"db->query|SELECT|INSERT|UPDATE|DELETE", content, re.IGNORECASE))
                loop_count = len(re.findall(r"for\s*\(|while\s*\(|foreach\s*\(", content, re.IGNORECASE))

                if query_count > 20 or loop_count > 15:
                    complex_files.append({
                        "file": str(file_path),
                        "queries": query_count,
                        "loops": loop_count,
                        "complexity_score": query_count + loop_count
                    })
            except:
                continue

        return {
            "large_files": sorted(large_files, key=lambda x: x["lines"], reverse=True)[:10],
            "complex_files": sorted(complex_files, key=lambda x: x["complexity_score"], reverse=True)[:10],
            "performance_recommendation": self.get_performance_recommendation(large_files, complex_files)
        }

    def analyze_language_system_status(self) -> Dict[str, Any]:
        """تحليل حالة نظام اللغات (جديد من v9)"""
        language_files = []
        controller_files = self.scan_directory("dashboard/controller", ['.php'])

        total_language_score = 0
        files_with_language_support = 0
        missing_ar_files = []
        missing_en_files = []
        files_with_hardcoded_text = []

        for file_path in controller_files:
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()

                lang_analysis = self.analyze_language_compliance(file_path, content)
                total_language_score += lang_analysis['compliance_score']

                if lang_analysis['compliance_score'] > 50:
                    files_with_language_support += 1

                if lang_analysis['missing_ar']:
                    missing_ar_files.append({
                        "file": str(file_path),
                        "missing_count": len(lang_analysis['missing_ar'])
                    })

                if lang_analysis['missing_en']:
                    missing_en_files.append({
                        "file": str(file_path),
                        "missing_count": len(lang_analysis['missing_en'])
                    })

                if lang_analysis['hardcoded_text']:
                    files_with_hardcoded_text.append({
                        "file": str(file_path),
                        "hardcoded_count": len(lang_analysis['hardcoded_text'])
                    })

                language_files.append({
                    "file": str(file_path),
                    "score": lang_analysis['compliance_score'],
                    "i18n_readiness": lang_analysis['i18n_readiness']
                })

            except:
                continue

        average_language_score = (total_language_score / len(controller_files)) if controller_files else 0
        language_coverage = (files_with_language_support / len(controller_files) * 100) if controller_files else 0

        return {
            "average_language_score": average_language_score,
            "language_coverage_percentage": language_coverage,
            "total_controller_files": len(controller_files),
            "files_with_language_support": files_with_language_support,
            "missing_arabic_files": sorted(missing_ar_files, key=lambda x: x["missing_count"], reverse=True)[:10],
            "missing_english_files": sorted(missing_en_files, key=lambda x: x["missing_count"], reverse=True)[:10],
            "files_with_hardcoded_text": sorted(files_with_hardcoded_text, key=lambda x: x["hardcoded_count"], reverse=True)[:10],
            "status": "excellent" if average_language_score > 80 else "good" if average_language_score > 60 else "needs_improvement",
            "recommendation": self.get_language_recommendation(average_language_score),
            "detailed_files": language_files
        }

    def get_language_recommendation(self, average_score: float) -> str:
        """توصية لنظام اللغات"""
        if average_score > 80:
            return "نظام اللغات ممتاز، يُنصح بالمحافظة على هذا المستوى"
        elif average_score > 60:
            return "نظام اللغات جيد، يُنصح بتحسين الملفات ذات النتائج المنخفضة"
        elif average_score > 40:
            return "نظام اللغات يحتاج تحسين، يجب إضافة ملفات اللغة المفقودة"
        else:
            return "نظام اللغات يحتاج تطوير شامل، يجب إنشاء ملفات اللغة وإزالة النصوص المدمجة"

    def get_performance_recommendation(self, large_files: List, complex_files: List) -> str:
        """توصية للأداء"""
        if len(large_files) > 5 or len(complex_files) > 5:
            return "يوجد ملفات كبيرة ومعقدة تحتاج تحسين، يُنصح بتقسيمها وتحسين الاستعلامات"
        elif len(large_files) > 2 or len(complex_files) > 2:
            return "بعض الملفات تحتاج تحسين، يُنصح بمراجعة الكود وتحسين الأداء"
        else:
            return "الأداء جيد عموماً، مراجعة دورية للملفات الكبيرة مُنصح بها"

    def run_full_audit(self, target_directories: List[str] = None) -> SystemAudit:
        """تشغيل الفحص الشامل للنظام"""
        if target_directories is None:
            target_directories = ["dashboard", "catalog"]

        print("🚀 بدء الفحص الشامل لنظام AYM ERP...")
        start_time = time.time()

        # جمع جميع الملفات
        all_files = []
        for directory in target_directories:
            all_files.extend(self.scan_directory(directory))

        print(f"📁 تم العثور على {len(all_files)} ملف للفحص...")

        # فحص كل ملف
        for i, file_path in enumerate(all_files, 1):
            if i % 50 == 0:
                print(f"⏳ تم فحص {i}/{len(all_files)} ملف...")

            result = self.analyze_file(file_path)
            self.results.append(result)

        # التحليلات المتقدمة
        print("🔍 تشغيل التحليلات المتقدمة...")
        bootstrap_analysis = self.analyze_bootstrap_consistency()
        language_system_status = self.analyze_language_system_status()  # جديد من v9
        bundle_system_status = self.analyze_bundle_system_status()
        wac_system_status = self.analyze_wac_system_status()
        target_categories_status = self.analyze_target_categories_status()
        performance_analysis = self.analyze_performance_bottlenecks()

        # حساب الإحصائيات العامة
        total_files = len(self.results)
        average_score = sum(r.score for r in self.results) / total_files if total_files > 0 else 0
        mvc_compliance_rate = sum(1 for r in self.results if r.mvc_compliance) / total_files * 100 if total_files > 0 else 0
        language_compliance_rate = sum(r.language_compliance for r in self.results) / total_files if total_files > 0 else 0  # جديد من v9

        # جمع المشاكل الحرجة
        critical_issues = []
        for result in self.results:
            if result.score < 50:
                critical_issues.extend(result.issues)

        # جمع التوصيات
        all_recommendations = []
        for result in self.results:
            all_recommendations.extend(result.recommendations)

        # إزالة المكررات
        critical_issues = list(set(critical_issues))
        all_recommendations = list(set(all_recommendations))

        # بناء خريطة الاعتماديات
        dependency_graph = {}
        for result in self.results:
            dependency_graph[result.file_path] = result.dependencies

        end_time = time.time()
        print(f"✅ تم الانتهاء من الفحص في {end_time - start_time:.2f} ثانية")

        return SystemAudit(
            timestamp=datetime.now().isoformat(),
            total_files=total_files,
            average_score=average_score,
            critical_issues=critical_issues,
            recommendations=all_recommendations,
            bootstrap_analysis=bootstrap_analysis,
            dependency_graph=dependency_graph,
            mvc_compliance_rate=mvc_compliance_rate,
            language_compliance_rate=language_compliance_rate,  # جديد من v9
            language_system_status=language_system_status,  # جديد من v9
            bundle_system_status=bundle_system_status,
            wac_system_status=wac_system_status,
            target_categories_status=target_categories_status,
            performance_analysis=performance_analysis
        )

    def generate_detailed_report(self, audit: SystemAudit, output_file: str = "aym_audit_report_v10.json"):
        """إنشاء تقرير مفصل"""
        report = {
            "audit_info": {
                "version": "10.0",
                "timestamp": audit.timestamp,
                "total_files_analyzed": audit.total_files
            },
            "overall_scores": {
                "average_system_score": round(audit.average_score, 2),
                "mvc_compliance_rate": round(audit.mvc_compliance_rate, 2),
                "language_compliance_rate": round(audit.language_compliance_rate, 2),  # جديد من v9
                "bootstrap_consistency_score": audit.bootstrap_analysis["consistency_score"]
            },
            "critical_issues": audit.critical_issues,
            "system_recommendations": audit.recommendations,
            "detailed_analysis": {
                "bootstrap_analysis": audit.bootstrap_analysis,
                "language_system": audit.language_system_status,  # جديد من v9
                "bundle_system": audit.bundle_system_status,
                "wac_system": audit.wac_system_status,
                "target_categories": audit.target_categories_status,
                "performance": audit.performance_analysis
            },
            "file_results": [asdict(result) for result in self.results],
            "dependency_graph": audit.dependency_graph
        }

        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)

        print(f"📄 تم حفظ التقرير المفصل في: {output_file}")

    def generate_executive_summary(self, audit: SystemAudit, output_file: str = "aym_executive_summary_v10.md"):
        """إنشاء ملخص تنفيذي"""
        summary = f"""# تقرير الفحص الشامل لنظام AYM ERP v10.0

## 📊 الملخص التنفيذي

**تاريخ الفحص:** {audit.timestamp}
**إجمالي الملفات المفحوصة:** {audit.total_files}
**النتيجة العامة:** {audit.average_score:.1f}/100

## 🎯 النتائج الرئيسية

### Bootstrap Analysis
- **حالة Bootstrap:** {audit.bootstrap_analysis['recommendation']}
- **نتيجة الاتساق:** {audit.bootstrap_analysis['consistency_score']:.1f}/100

### Language System Status (جديد من v9)
- **متوسط نتيجة اللغات:** {audit.language_system_status['average_language_score']:.1f}%
- **تغطية نظام اللغات:** {audit.language_system_status['language_coverage_percentage']:.1f}%
- **الحالة:** {audit.language_system_status['status']}
- **التوصية:** {audit.language_system_status['recommendation']}

### Bundle System Status
- **تغطية نظام الباقات:** {audit.bundle_system_status['bundle_coverage_percentage']:.1f}%
- **الحالة:** {audit.bundle_system_status['status']}
- **التوصية:** {audit.bundle_system_status['recommendation']}

### WAC System Status
- **تغطية نظام WAC:** {audit.wac_system_status['wac_coverage_percentage']:.1f}%
- **الحالة:** {audit.wac_system_status['status']}
- **التوصية:** {audit.wac_system_status['recommendation']}

### Target Categories Support
- **التوصية العامة:** {audit.target_categories_status['overall_recommendation']}
- **الفئات ذات الأولوية:** {', '.join(audit.target_categories_status['priority_categories'])}

## 🚨 المشاكل الحرجة

"""

        for i, issue in enumerate(audit.critical_issues[:10], 1):
            summary += f"{i}. {issue}\n"

        summary += f"""
## 💡 التوصيات الرئيسية

"""

        for i, recommendation in enumerate(audit.recommendations[:10], 1):
            summary += f"{i}. {recommendation}\n"

        summary += f"""
## 📈 تحليل الأداء

- **الملفات الكبيرة:** {len(audit.performance_analysis['large_files'])} ملف
- **الملفات المعقدة:** {len(audit.performance_analysis['complex_files'])} ملف
- **توصية الأداء:** {audit.performance_analysis['performance_recommendation']}

## 🎯 الخطوات التالية

1. **إصلاح المشاكل الحرجة** - الملفات ذات النتيجة أقل من 50
2. **تطوير نظام الباقات** - زيادة التغطية إلى 80%+
3. **تحسين تكامل WAC** - زيادة التغطية إلى 70%+
4. **تطوير دعم الفئات المستهدفة** - التركيز على الفئات ذات الأولوية
5. **تحسين الأداء** - تقسيم الملفات الكبيرة وتحسين الاستعلامات

---
*تم إنشاء هذا التقرير بواسطة AYM Ultimate Auditor v10.0*
"""

        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(summary)

        print(f"📋 تم حفظ الملخص التنفيذي في: {output_file}")

def main():
    """الوظيفة الرئيسية"""
    parser = argparse.ArgumentParser(description="AYM Ultimate Auditor v10.0 - Advanced System Analysis Tool")
    parser.add_argument("--path", default=".", help="مسار المشروع (افتراضي: المجلد الحالي)")
    parser.add_argument("--directories", nargs="+", default=["dashboard", "catalog"],
                       help="المجلدات المراد فحصها (افتراضي: dashboard catalog)")
    parser.add_argument("--output", default="aym_audit_report_v10.json", help="ملف التقرير المفصل")
    parser.add_argument("--summary", default="aym_executive_summary_v10.md", help="ملف الملخص التنفيذي")

    args = parser.parse_args()

    print("🔍 AYM Ultimate Auditor v10.0 - Advanced System Analysis Tool")
    print("=" * 60)

    auditor = AYMUltimateAuditorV10(args.path)
    audit_result = auditor.run_full_audit(args.directories)

    # إنشاء التقارير
    auditor.generate_detailed_report(audit_result, args.output)
    auditor.generate_executive_summary(audit_result, args.summary)

    print("\n🎉 تم الانتهاء من الفحص الشامل!")
    print(f"📊 النتيجة العامة: {audit_result.average_score:.1f}/100")
    print(f"🔧 MVC Compliance: {audit_result.mvc_compliance_rate:.1f}%")
    print(f"📁 تم فحص {audit_result.total_files} ملف")

if __name__ == "__main__":
    main()
