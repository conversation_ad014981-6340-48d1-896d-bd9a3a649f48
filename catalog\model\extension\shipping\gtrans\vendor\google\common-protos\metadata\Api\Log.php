<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/log.proto

namespace GPBMetadata\Google\Api;

class Log
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        \GPBMetadata\Google\Api\Label::initOnce();
        $pool->internalAddGeneratedFile(hex2bin(
            "0a8d020a14676f6f676c652f6170692f6c6f672e70726f746f120a676f6f" .
            "676c652e61706922750a0d4c6f6744657363726970746f72120c0a046e61" .
            "6d65180120012809122b0a066c6162656c7318022003280b321b2e676f6f" .
            "676c652e6170692e4c6162656c44657363726970746f7212130a0b646573" .
            "6372697074696f6e18032001280912140a0c646973706c61795f6e616d65" .
            "180420012809426a0a0e636f6d2e676f6f676c652e61706942084c6f6750" .
            "726f746f50015a45676f6f676c652e676f6c616e672e6f72672f67656e70" .
            "726f746f2f676f6f676c65617069732f6170692f73657276696365636f6e" .
            "6669673b73657276696365636f6e666967a2020447415049620670726f74" .
            "6f33"
        ), true);

        static::$is_initialized = true;
    }
}

