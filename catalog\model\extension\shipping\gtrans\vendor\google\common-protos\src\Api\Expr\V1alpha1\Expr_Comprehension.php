<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/expr/v1alpha1/syntax.proto

namespace Google\Api\Expr\V1alpha1;

if (false) {
    /**
     * This class is deprecated. Use Google\Api\Expr\V1alpha1\Expr\Comprehension instead.
     * @deprecated
     */
    class Expr_Comprehension {}
}
class_exists(Expr\Comprehension::class);
@trigger_error('Google\Api\Expr\V1alpha1\Expr_Comprehension is deprecated and will be removed in the next major release. Use Google\Api\Expr\V1alpha1\Expr\Comprehension instead', E_USER_DEPRECATED);

