<?php

namespace Pi<PERSON><PERSON>er\Barcode\Types;

use <PERSON><PERSON><PERSON><PERSON>\Barcode\Barcode;
use <PERSON><PERSON><PERSON><PERSON>\Barcode\BarcodeBar;
use <PERSON><PERSON><PERSON>er\Barcode\Exceptions\InvalidCharacterException;

/*
 * CODABAR barcodes.
 * Older code often used in library systems, sometimes in blood banks
 */

class TypeCodabar implements TypeInterface
{
    protected array $conversionTable = [
        '0' => '********',
        '1' => '********',
        '2' => '********',
        '3' => '********',
        '4' => '********',
        '5' => '********',
        '6' => '********',
        '7' => '********',
        '8' => '********',
        '9' => '********',
        '-' => '********',
        '$' => '********',
        ':' => '********',
        '/' => '********',
        '.' => '********',
        '+' => '********',
        'A' => '********',
        'B' => '********',
        'C' => '********',
        'D' => '********'
    ];

    public function getBarcode(string $code): Barcode
    {
        $barcode = new Barcode($code);

        $code = 'A' . strtoupper($code) . 'A';

        for ($i = 0; $i < strlen($code); ++$i) {
            if (! isset($this->conversionTable[(string)$code[$i]])) {
                throw new InvalidCharacterException('Char ' . $code[$i] . ' is unsupported');
            }

            $seq = $this->conversionTable[(string)$code[$i]];
            for ($j = 0; $j < 8; ++$j) {
                if (($j % 2) == 0) {
                    $drawBar = true;
                } else {
                    $drawBar = false;
                }
                $barWidth = $seq[$j];
                $barcode->addBar(new BarcodeBar($barWidth, 1, $drawBar));
            }
        }

        return $barcode;
    }
}
