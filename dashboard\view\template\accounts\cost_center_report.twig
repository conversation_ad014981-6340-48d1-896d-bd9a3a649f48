{{ header }}{{ column_left }}

<!-- Enterprise Grade Plus CSS for Cost Center Report -->
<style>
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --danger-color: #e74c3c;
    --warning-color: #f39c12;
    --info-color: #17a2b8;
    --cost-center-color: #e67e22;
    --light-bg: #f8f9fa;
    --border-color: #dee2e6;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

.cost-center-container {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: var(--shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.cost-center-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--cost-center-color), var(--primary-color), var(--secondary-color));
}

.cost-center-header {
    text-align: center;
    border-bottom: 3px solid var(--cost-center-color);
    padding-bottom: 20px;
    margin-bottom: 30px;
    position: relative;
}

.cost-center-header h2 {
    color: var(--cost-center-color);
    font-weight: 700;
    font-size: 1.8rem;
    margin: 0;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.cost-center-summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.cost-center-summary-card {
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    text-align: center;
    box-shadow: var(--shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.cost-center-summary-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0,0,0,0.1);
}

.cost-center-summary-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
}

.cost-center-summary-card.total::before { background: var(--cost-center-color); }
.cost-center-summary-card.revenue::before { background: var(--success-color); }
.cost-center-summary-card.expenses::before { background: var(--danger-color); }
.cost-center-summary-card.profit::before { background: var(--info-color); }

.cost-center-summary-card h4 {
    color: var(--primary-color);
    margin-bottom: 10px;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.cost-center-summary-card .amount {
    font-size: 1.4rem;
    font-weight: 700;
    font-family: 'Courier New', monospace;
    margin-bottom: 5px;
}

.cost-center-summary-card .description {
    font-size: 0.8rem;
    color: #6c757d;
    font-weight: 500;
}

.card-total .amount { color: var(--cost-center-color); }
.card-revenue .amount { color: var(--success-color); }
.card-expenses .amount { color: var(--danger-color); }
.card-profit .amount { color: var(--info-color); }

.cost-center-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    background: #ffffff;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.cost-center-table th {
    background: linear-gradient(135deg, var(--cost-center-color), #d35400);
    color: white;
    padding: 15px 12px;
    text-align: center;
    font-weight: 600;
    font-size: 0.9rem;
    border-bottom: 2px solid var(--border-color);
}

.cost-center-table td {
    padding: 12px;
    border-bottom: 1px solid var(--border-color);
    text-align: center;
    transition: var(--transition);
}

.cost-center-table tbody tr:hover {
    background: var(--light-bg);
    transform: scale(1.005);
}

.cost-center-table tbody tr:nth-child(even) {
    background: #fafbfc;
}

.amount-cell {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    text-align: {{ direction == 'rtl' ? 'right' : 'left' }};
}

.amount-positive { 
    color: var(--success-color); 
    font-weight: 600;
}

.amount-negative { 
    color: var(--danger-color); 
    font-weight: 600;
}

.amount-neutral { 
    color: var(--secondary-color); 
    font-weight: 600;
}

.cost-center-actions {
    display: flex;
    gap: 5px;
    justify-content: center;
}

.cost-center-actions .btn {
    padding: 5px 10px;
    font-size: 0.8rem;
}

/* Chart Container */
.chart-container {
    background: #ffffff;
    border-radius: var(--border-radius);
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: var(--shadow);
}

.chart-container canvas {
    max-height: 400px;
}

/* Filter Panel */
.filter-panel {
    background: linear-gradient(135deg, #f8f9fa, #ffffff);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: var(--shadow);
}

/* RTL Support */
[dir="rtl"] .cost-center-table {
    direction: rtl;
}

[dir="rtl"] .amount-cell {
    text-align: right;
}

/* Print Styles */
@media print {
    .cost-center-container {
        box-shadow: none;
        border: 1px solid #000;
    }
    
    .cost-center-table th {
        background: #f0f0f0 !important;
        color: #000 !important;
    }
    
    .btn, .card-header, .filter-panel {
        display: none !important;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .cost-center-table {
        font-size: 0.8rem;
    }
    
    .cost-center-table th,
    .cost-center-table td {
        padding: 8px 6px;
    }
    
    .cost-center-summary-cards {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .cost-center-actions {
        flex-direction: column;
    }
}
</style>

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-{{ direction == 'rtl' ? 'start' : 'end' }}">
        <div class="btn-group" role="group" aria-label="{{ text_actions }}">
          <button type="button" class="btn btn-success btn-lg" onclick="generateReport()" 
                  data-bs-toggle="tooltip" title="{{ text_generate_report }}">
            <i class="fas fa-chart-bar me-2"></i> {{ button_generate }}
          </button>
          <div class="btn-group" role="group">
            <button type="button" class="btn btn-info dropdown-toggle" data-bs-toggle="dropdown" 
                    aria-expanded="false" data-bs-toggle="tooltip" title="{{ text_export_options }}">
              <i class="fas fa-download me-2"></i> {{ text_export }}
            </button>
            <ul class="dropdown-menu">
              <li><a class="dropdown-item" href="#" onclick="exportReport('excel')">
                <i class="fas fa-file-excel text-success me-2"></i> Excel
              </a></li>
              <li><a class="dropdown-item" href="#" onclick="exportReport('pdf')">
                <i class="fas fa-file-pdf text-danger me-2"></i> PDF
              </a></li>
              <li><a class="dropdown-item" href="#" onclick="exportReport('csv')">
                <i class="fas fa-file-csv text-info me-2"></i> CSV
              </a></li>
              <li><hr class="dropdown-divider"></li>
              <li><a class="dropdown-item" href="#" onclick="printReport()">
                <i class="fas fa-print text-secondary me-2"></i> {{ text_print }}
              </a></li>
            </ul>
          </div>
          <button type="button" class="btn btn-outline-primary" onclick="showAnalysis()"
                  data-bs-toggle="tooltip" title="{{ text_cost_center_analysis }}">
            <i class="fas fa-analytics"></i>
          </button>
        </div>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li class="breadcrumb-item"><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>

  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible fade show">
      <i class="fas fa-exclamation-triangle"></i>
      {{ error_warning }}
      <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    {% endif %}

    {% if success %}
    <div class="alert alert-success alert-dismissible fade show">
      <i class="fas fa-check-circle"></i>
      {{ success }}
      <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    {% endif %}

    <!-- Filter Panel -->
    <div class="filter-panel">
      <h4>{{ text_report_filters }}</h4>
      <form id="cost-center-report-form" method="post">
        <div class="row">
          <div class="col-md-3">
            <div class="form-group">
              <label for="cost_center_id" class="form-label">{{ entry_cost_center }}</label>
              <select name="cost_center_id" id="cost_center_id" class="form-control select2">
                <option value="">{{ text_all_cost_centers }}</option>
                {% for cost_center in cost_centers %}
                <option value="{{ cost_center.cost_center_id }}"{% if cost_center.cost_center_id == cost_center_id %} selected{% endif %}>
                  {{ cost_center.name }} ({{ cost_center.code }})
                </option>
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="col-md-2">
            <div class="form-group">
              <label for="date_start" class="form-label">{{ entry_date_start }}</label>
              <input type="date" name="date_start" id="date_start" value="{{ date_start }}" class="form-control" required>
            </div>
          </div>
          <div class="col-md-2">
            <div class="form-group">
              <label for="date_end" class="form-label">{{ entry_date_end }}</label>
              <input type="date" name="date_end" id="date_end" value="{{ date_end }}" class="form-control" required>
            </div>
          </div>
          <div class="col-md-2">
            <div class="form-group">
              <label for="department_id" class="form-label">{{ entry_department }}</label>
              <select name="department_id" id="department_id" class="form-control">
                <option value="">{{ text_all_departments }}</option>
                {% for department in departments %}
                <option value="{{ department.department_id }}"{% if department.department_id == department_id %} selected{% endif %}>{{ department.name }}</option>
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="col-md-3">
            <div class="form-group">
              <label class="form-label">&nbsp;</label>
              <div class="d-grid">
                <button type="submit" class="btn btn-primary">
                  <i class="fas fa-search me-2"></i>{{ button_filter }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>

    <!-- Cost Center Report Content -->
    {% if report_data %}
    <!-- Summary Cards -->
    <div class="cost-center-summary-cards">
      <div class="cost-center-summary-card card-total total">
        <h4>{{ text_total_cost_centers }}</h4>
        <div class="amount">{{ report_data.summary.total_cost_centers }}</div>
        <div class="description">{{ text_cost_centers }}</div>
      </div>
      <div class="cost-center-summary-card card-revenue revenue">
        <h4>{{ text_total_revenue }}</h4>
        <div class="amount">{{ report_data.summary.total_revenue_formatted }}</div>
        <div class="description">{{ text_revenue }}</div>
      </div>
      <div class="cost-center-summary-card card-expenses expenses">
        <h4>{{ text_total_expenses }}</h4>
        <div class="amount">{{ report_data.summary.total_expenses_formatted }}</div>
        <div class="description">{{ text_expenses }}</div>
      </div>
      <div class="cost-center-summary-card card-profit profit">
        <h4>{{ text_net_profit }}</h4>
        <div class="amount">{{ report_data.summary.net_profit_formatted }}</div>
        <div class="description">{{ report_data.summary.profit_margin|number_format(2) }}%</div>
      </div>
    </div>

    <!-- Cost Center Report Table -->
    <div class="cost-center-container">
      <div class="cost-center-header">
        <h2>{{ text_cost_center_report_details }}</h2>
      </div>

      <div class="table-responsive">
        <table class="cost-center-table" id="cost-center-report-table">
          <thead>
            <tr>
              <th>{{ column_cost_center_name }}</th>
              <th>{{ column_cost_center_code }}</th>
              <th>{{ column_department }}</th>
              <th>{{ column_revenue }}</th>
              <th>{{ column_expenses }}</th>
              <th>{{ column_net_profit }}</th>
              <th>{{ column_profit_margin }}</th>
              <th>{{ column_budget_variance }}</th>
              <th>{{ column_action }}</th>
            </tr>
          </thead>
          <tbody>
            {% for cost_center in report_data.cost_centers %}
            <tr data-cost-center-id="{{ cost_center.cost_center_id }}">
              <td>
                <strong>{{ cost_center.cost_center_name }}</strong>
                <br>
                <small class="text-muted">{{ cost_center.description }}</small>
              </td>
              <td>{{ cost_center.cost_center_code }}</td>
              <td>{{ cost_center.department_name }}</td>
              <td class="amount-cell">
                <strong class="amount-positive">{{ cost_center.revenue_formatted }}</strong>
              </td>
              <td class="amount-cell">
                <strong class="amount-negative">{{ cost_center.expenses_formatted }}</strong>
              </td>
              <td class="amount-cell">
                <span class="{% if cost_center.net_profit >= 0 %}amount-positive{% else %}amount-negative{% endif %}">
                  <strong>{{ cost_center.net_profit_formatted }}</strong>
                </span>
              </td>
              <td class="amount-cell">
                <span class="{% if cost_center.profit_margin >= 0 %}amount-positive{% else %}amount-negative{% endif %}">
                  {{ cost_center.profit_margin|number_format(2) }}%
                </span>
              </td>
              <td class="amount-cell">
                <span class="{% if cost_center.budget_variance >= 0 %}amount-positive{% else %}amount-negative{% endif %}">
                  {{ cost_center.budget_variance_formatted }}
                </span>
              </td>
              <td>
                <div class="cost-center-actions">
                  <button type="button" class="btn btn-outline-info btn-sm"
                          onclick="viewCostCenterDetails({{ cost_center.cost_center_id }})"
                          data-bs-toggle="tooltip" title="{{ text_view_details }}">
                    <i class="fas fa-eye"></i>
                  </button>
                  <button type="button" class="btn btn-outline-warning btn-sm"
                          onclick="viewBudgetComparison({{ cost_center.cost_center_id }})"
                          data-bs-toggle="tooltip" title="{{ text_budget_comparison }}">
                    <i class="fas fa-chart-bar"></i>
                  </button>
                </div>
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>

    <!-- Charts Section -->
    <div class="row">
      <div class="col-md-6">
        <div class="chart-container">
          <h4>{{ text_revenue_vs_expenses_chart }}</h4>
          <canvas id="revenueExpensesChart"></canvas>
        </div>
      </div>
      <div class="col-md-6">
        <div class="chart-container">
          <h4>{{ text_profit_margin_chart }}</h4>
          <canvas id="profitMarginChart"></canvas>
        </div>
      </div>
    </div>
    {% else %}
    <div class="alert alert-info">
      <i class="fas fa-info-circle me-2"></i>
      {{ text_no_data }}
    </div>
    {% endif %}
  </div>
</div>

<script>
// Enterprise Grade Plus JavaScript for Cost Center Report
class CostCenterReportManager {
    constructor() {
        this.initializeTooltips();
        this.initializeDataTable();
        this.initializeKeyboardShortcuts();
        this.initializeCharts();
        this.initializeSelect2();
    }

    initializeTooltips() {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }

    initializeDataTable() {
        const table = document.getElementById('cost-center-report-table');
        if (table && typeof $ !== 'undefined' && $.fn.DataTable) {
            $(table).DataTable({
                responsive: true,
                pageLength: 25,
                order: [[5, 'desc']], // Sort by net profit desc
                columnDefs: [
                    { targets: [3, 4, 5, 6, 7], className: 'text-end' },
                    { targets: [8], orderable: false }
                ],
                language: {
                    url: '{{ direction == "rtl" ? "/view/javascript/jquery/datatables-ar.json" : "/view/javascript/jquery/datatables-en.json" }}'
                }
            });
        }
    }

    initializeKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case 'g':
                        e.preventDefault();
                        this.generateReport();
                        break;
                    case 'e':
                        e.preventDefault();
                        this.showExportMenu();
                        break;
                    case 'p':
                        e.preventDefault();
                        this.printReport();
                        break;
                    case 'a':
                        e.preventDefault();
                        this.showAnalysis();
                        break;
                }
            }
        });
    }

    initializeCharts() {
        if (typeof Chart !== 'undefined') {
            this.createRevenueExpensesChart();
            this.createProfitMarginChart();
        }
    }

    initializeSelect2() {
        if (typeof $ !== 'undefined' && $.fn.select2) {
            $('.select2').select2({
                theme: 'bootstrap-5',
                width: '100%'
            });
        }
    }

    generateReport() {
        const form = document.getElementById('cost-center-report-form');
        const formData = new FormData(form);

        this.showLoadingState(true);

        fetch('{{ generate_url }}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            this.showLoadingState(false);
            if (data.success) {
                this.showAlert('{{ text_report_generated }}', 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                this.showAlert(data.error || '{{ error_generate_report }}', 'danger');
            }
        })
        .catch(error => {
            this.showLoadingState(false);
            this.showAlert('{{ error_generate_report }}: ' + error.message, 'danger');
        });
    }

    exportReport(format) {
        const params = new URLSearchParams({
            format: format,
            cost_center_id: document.getElementById('cost_center_id').value,
            date_start: document.getElementById('date_start').value,
            date_end: document.getElementById('date_end').value,
            department_id: document.getElementById('department_id').value
        });

        this.showAlert('{{ text_exporting }}...', 'info');
        window.open('{{ export_url }}&' + params.toString(), '_blank');
    }

    printReport() {
        window.print();
    }

    showAnalysis() {
        window.open('{{ analysis_url }}', '_blank');
    }

    viewCostCenterDetails(costCenterId) {
        window.open('{{ url_link('accounts/cost_center_management', 'view') }}&cost_center_id=' + costCenterId, '_blank');
    }

    viewBudgetComparison(costCenterId) {
        window.open('{{ budget_comparison_url }}&cost_center_id=' + costCenterId, '_blank');
    }

    createRevenueExpensesChart() {
        const ctx = document.getElementById('revenueExpensesChart');
        if (!ctx) return;

        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: {{ cost_center_names|json_encode|raw }},
                datasets: [{
                    label: '{{ text_revenue }}',
                    data: {{ revenue_amounts|json_encode|raw }},
                    backgroundColor: '#27ae60',
                    borderColor: '#229954',
                    borderWidth: 1
                }, {
                    label: '{{ text_expenses }}',
                    data: {{ expense_amounts|json_encode|raw }},
                    backgroundColor: '#e74c3c',
                    borderColor: '#c0392b',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: '{{ text_revenue_vs_expenses_chart }}'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    createProfitMarginChart() {
        const ctx = document.getElementById('profitMarginChart');
        if (!ctx) return;

        new Chart(ctx, {
            type: 'line',
            data: {
                labels: {{ cost_center_names|json_encode|raw }},
                datasets: [{
                    label: '{{ text_profit_margin }}',
                    data: {{ profit_margins|json_encode|raw }},
                    borderColor: '#e67e22',
                    backgroundColor: 'rgba(230, 126, 34, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: '{{ text_profit_margin_chart }}'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    }
                }
            }
        });
    }

    showExportMenu() {
        const exportButton = document.querySelector('.dropdown-toggle');
        if (exportButton) {
            exportButton.click();
        }
    }

    showLoadingState(show) {
        const generateBtn = document.querySelector('button[onclick="generateReport()"]');
        if (generateBtn) {
            if (show) {
                generateBtn.disabled = true;
                generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> {{ text_loading }}';
            } else {
                generateBtn.disabled = false;
                generateBtn.innerHTML = '<i class="fas fa-chart-bar me-2"></i> {{ button_generate }}';
            }
        }
    }

    showAlert(message, type) {
        const alertContainer = document.createElement('div');
        alertContainer.className = `alert alert-${type} alert-dismissible fade show`;
        alertContainer.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'}"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        const container = document.querySelector('#content .container-fluid');
        container.insertBefore(alertContainer, container.firstChild);

        setTimeout(() => {
            alertContainer.remove();
        }, 5000);
    }
}

// Global functions for backward compatibility
function generateReport() {
    costCenterReportManager.generateReport();
}

function exportReport(format) {
    costCenterReportManager.exportReport(format);
}

function printReport() {
    costCenterReportManager.printReport();
}

function showAnalysis() {
    costCenterReportManager.showAnalysis();
}

function viewCostCenterDetails(costCenterId) {
    costCenterReportManager.viewCostCenterDetails(costCenterId);
}

function viewBudgetComparison(costCenterId) {
    costCenterReportManager.viewBudgetComparison(costCenterId);
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.costCenterReportManager = new CostCenterReportManager();
});
</script>

{{ footer }}
