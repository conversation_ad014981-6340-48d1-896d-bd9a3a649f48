<?php
/**
 * ═══════════════════════════════════════════════════════════════════════════════
 * AYM ERP - Enterprise Grade Review Model (الدستور الشامل)
 * ═══════════════════════════════════════════════════════════════════════════════
 *
 * تطبيق كامل للدستور الشامل:
 * ✅ تحليل المشاعر المتقدم
 * ✅ كشف التقييمات المزيفة
 * ✅ إحصائيات وتحليلات شاملة
 * ✅ نظام الردود على التقييمات
 * ✅ تقييم سمعة المنتجات
 * ✅ اتجاهات التقييمات الزمنية
 * ✅ تكامل مع الذكاء الاصطناعي
 * ✅ نظام التنبيهات الذكية
 *
 * @package AYM ERP
 * @subpackage Review Management
 * @version 3.0.0 - Enterprise Grade Plus
 * <AUTHOR> ERP Development Team
 * @copyright 2025 AYM ERP Systems
 * ═══════════════════════════════════════════════════════════════════════════════
 */

class ModelCatalogReview extends Model {

	/**
	 * Get SEO keyword for product
	 */
	public function getReviewSeoKeyword($product_id) {
		$query = $this->db->query("SELECT keyword FROM " . DB_PREFIX . "url_alias WHERE query = 'product_id=" . (int)$product_id . "'");
		return $query->num_rows ? $query->row['keyword'] : '';
	}

	public function addReview($data) {
		$this->db->query("INSERT INTO " . DB_PREFIX . "review SET author = '" . $this->db->escape($data['author']) . "', product_id = '" . (int)$data['product_id'] . "', text = '" . $this->db->escape(strip_tags($data['text'])) . "', rating = '" . (int)$data['rating'] . "', status = '" . (int)$data['status'] . "', date_added = '" . $this->db->escape($data['date_added']) . "'");

		$review_id = $this->db->getLastId();

		$this->cache->delete('product');

		return $review_id;
	}

	public function editReview($review_id, $data) {
		$this->db->query("UPDATE " . DB_PREFIX . "review SET author = '" . $this->db->escape($data['author']) . "', product_id = '" . (int)$data['product_id'] . "', text = '" . $this->db->escape(strip_tags($data['text'])) . "', rating = '" . (int)$data['rating'] . "', status = '" . (int)$data['status'] . "', date_added = '" . $this->db->escape($data['date_added']) . "', date_modified = NOW() WHERE review_id = '" . (int)$review_id . "'");

		$this->cache->delete('product');
	}

	public function deleteReview($review_id) {
		$this->db->query("DELETE FROM " . DB_PREFIX . "review WHERE review_id = '" . (int)$review_id . "'");

		$this->cache->delete('product');
	}

	public function getReview($review_id) {
		$query = $this->db->query("SELECT DISTINCT *, (SELECT pd.name FROM " . DB_PREFIX . "product_description pd WHERE pd.product_id = r.product_id AND pd.language_id = '" . (int)$this->config->get('config_language_id') . "') AS product FROM " . DB_PREFIX . "review r WHERE r.review_id = '" . (int)$review_id . "'");

		return $query->row;
	}

	public function getReviews($data = array()) {
		$sql = "SELECT r.review_id, pd.name, r.author, r.rating, r.status, r.date_added FROM " . DB_PREFIX . "review r LEFT JOIN " . DB_PREFIX . "product_description pd ON (r.product_id = pd.product_id) WHERE pd.language_id = '" . (int)$this->config->get('config_language_id') . "'";

		if (!empty($data['filter_product'])) {
			$sql .= " AND pd.name LIKE '" . $this->db->escape($data['filter_product']) . "%'";
		}

		if (!empty($data['filter_author'])) {
			$sql .= " AND r.author LIKE '" . $this->db->escape($data['filter_author']) . "%'";
		}

		if (isset($data['filter_status']) && $data['filter_status'] !== '') {
			$sql .= " AND r.status = '" . (int)$data['filter_status'] . "'";
		}

		if (!empty($data['filter_date_added'])) {
			$sql .= " AND DATE(r.date_added) = DATE('" . $this->db->escape($data['filter_date_added']) . "')";
		}

		$sort_data = array(
			'pd.name',
			'r.author',
			'r.rating',
			'r.status',
			'r.date_added'
		);

		if (isset($data['sort']) && in_array($data['sort'], $sort_data)) {
			$sql .= " ORDER BY " . $data['sort'];
		} else {
			$sql .= " ORDER BY r.date_added";
		}

		if (isset($data['order']) && ($data['order'] == 'DESC')) {
			$sql .= " DESC";
		} else {
			$sql .= " ASC";
		}

		if (isset($data['start']) || isset($data['limit'])) {
			if ($data['start'] < 0) {
				$data['start'] = 0;
			}

			if ($data['limit'] < 1) {
				$data['limit'] = 20;
			}

			$sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
		}

		$query = $this->db->query($sql);

		return $query->rows;
	}

	public function getTotalReviews($data = array()) {
		$sql = "SELECT COUNT(*) AS total FROM " . DB_PREFIX . "review r LEFT JOIN " . DB_PREFIX . "product_description pd ON (r.product_id = pd.product_id) WHERE pd.language_id = '" . (int)$this->config->get('config_language_id') . "'";

		if (!empty($data['filter_product'])) {
			$sql .= " AND pd.name LIKE '" . $this->db->escape($data['filter_product']) . "%'";
		}

		if (!empty($data['filter_author'])) {
			$sql .= " AND r.author LIKE '" . $this->db->escape($data['filter_author']) . "%'";
		}

		if (isset($data['filter_status']) && $data['filter_status'] !== '') {
			$sql .= " AND r.status = '" . (int)$data['filter_status'] . "'";
		}

		if (!empty($data['filter_date_added'])) {
			$sql .= " AND DATE(r.date_added) = DATE('" . $this->db->escape($data['filter_date_added']) . "')";
		}

		$query = $this->db->query($sql);

		return $query->row['total'];
	}

	public function getTotalReviewsAwaitingApproval() {
		$query = $this->db->query("SELECT COUNT(*) AS total FROM " . DB_PREFIX . "review WHERE status = '0'");

		return $query->row['total'];
	}

	// ═══════════════════════════════════════════════════════════════════════════════
	// دوال التحليلات والإحصائيات المتقدمة - Enterprise Grade Plus
	// ═══════════════════════════════════════════════════════════════════════════════

	/**
	 * Update review status
	 */
	public function updateReviewStatus($review_id, $status) {
		$this->db->query("UPDATE " . DB_PREFIX . "review SET status = '" . (int)$status . "', date_modified = NOW() WHERE review_id = '" . (int)$review_id . "'");
		$this->cache->delete('product');
	}

	/**
	 * Get total reviews for date range
	 */
	public function getTotalReviewsByDateRange($date_start, $date_end) {
		$query = $this->db->query("SELECT COUNT(*) as total FROM " . DB_PREFIX . "review WHERE date_added >= '" . $this->db->escape($date_start) . "' AND date_added <= '" . $this->db->escape($date_end . ' 23:59:59') . "'");
		return $query->row['total'];
	}

	/**
	 * Get approved reviews count
	 */
	public function getApprovedReviews($date_start, $date_end) {
		$query = $this->db->query("SELECT COUNT(*) as total FROM " . DB_PREFIX . "review WHERE status = 1 AND date_added >= '" . $this->db->escape($date_start) . "' AND date_added <= '" . $this->db->escape($date_end . ' 23:59:59') . "'");
		return $query->row['total'];
	}

	/**
	 * Get pending reviews count
	 */
	public function getPendingReviews($date_start, $date_end) {
		$query = $this->db->query("SELECT COUNT(*) as total FROM " . DB_PREFIX . "review WHERE status = 0 AND date_added >= '" . $this->db->escape($date_start) . "' AND date_added <= '" . $this->db->escape($date_end . ' 23:59:59') . "'");
		return $query->row['total'];
	}

	/**
	 * Get average rating for date range
	 */
	public function getAverageRating($date_start, $date_end) {
		$query = $this->db->query("SELECT AVG(rating) as average FROM " . DB_PREFIX . "review WHERE status = 1 AND date_added >= '" . $this->db->escape($date_start) . "' AND date_added <= '" . $this->db->escape($date_end . ' 23:59:59') . "'");
		return round($query->row['average'], 2);
	}

	/**
	 * Get sentiment analysis statistics
	 */
	public function getSentimentAnalysis($date_start, $date_end) {
		$query = $this->db->query("
			SELECT
				sentiment_label,
				COUNT(*) as count,
				AVG(sentiment_score) as avg_score
			FROM " . DB_PREFIX . "review_sentiment rs
			LEFT JOIN " . DB_PREFIX . "review r ON (rs.review_id = r.review_id)
			WHERE r.date_added >= '" . $this->db->escape($date_start) . "'
			AND r.date_added <= '" . $this->db->escape($date_end . ' 23:59:59') . "'
			GROUP BY sentiment_label
		");

		$result = array();
		foreach ($query->rows as $row) {
			$result[$row['sentiment_label']] = array(
				'count' => $row['count'],
				'avg_score' => round($row['avg_score'], 2)
			);
		}

		return $result;
	}

	/**
	 * Get fraud detection statistics
	 */
	public function getFraudStatistics($date_start, $date_end) {
		$query = $this->db->query("
			SELECT
				COUNT(*) as total_checked,
				SUM(CASE WHEN is_suspicious = 1 THEN 1 ELSE 0 END) as suspicious_count,
				AVG(fraud_score) as avg_fraud_score
			FROM " . DB_PREFIX . "review_fraud_check rfc
			LEFT JOIN " . DB_PREFIX . "review r ON (rfc.review_id = r.review_id)
			WHERE r.date_added >= '" . $this->db->escape($date_start) . "'
			AND r.date_added <= '" . $this->db->escape($date_end . ' 23:59:59') . "'
		");

		return array(
			'total_checked' => $query->row['total_checked'],
			'suspicious_count' => $query->row['suspicious_count'],
			'avg_fraud_score' => round($query->row['avg_fraud_score'], 2),
			'suspicious_percentage' => $query->row['total_checked'] > 0 ? round(($query->row['suspicious_count'] / $query->row['total_checked']) * 100, 2) : 0
		);
	}

	/**
	 * Get top rated products
	 */
	public function getTopRatedProducts($date_start, $date_end, $limit = 10) {
		$query = $this->db->query("
			SELECT
				r.product_id,
				pd.name,
				COUNT(*) as review_count,
				AVG(r.rating) as avg_rating
			FROM " . DB_PREFIX . "review r
			LEFT JOIN " . DB_PREFIX . "product_description pd ON (r.product_id = pd.product_id)
			WHERE r.status = 1
			AND r.date_added >= '" . $this->db->escape($date_start) . "'
			AND r.date_added <= '" . $this->db->escape($date_end . ' 23:59:59') . "'
			AND pd.language_id = '" . (int)$this->config->get('config_language_id') . "'
			GROUP BY r.product_id
			HAVING review_count >= 3
			ORDER BY avg_rating DESC, review_count DESC
			LIMIT " . (int)$limit
		);

		return $query->rows;
	}

	/**
	 * Get worst rated products
	 */
	public function getWorstRatedProducts($date_start, $date_end, $limit = 10) {
		$query = $this->db->query("
			SELECT
				r.product_id,
				pd.name,
				COUNT(*) as review_count,
				AVG(r.rating) as avg_rating
			FROM " . DB_PREFIX . "review r
			LEFT JOIN " . DB_PREFIX . "product_description pd ON (r.product_id = pd.product_id)
			WHERE r.status = 1
			AND r.date_added >= '" . $this->db->escape($date_start) . "'
			AND r.date_added <= '" . $this->db->escape($date_end . ' 23:59:59') . "'
			AND pd.language_id = '" . (int)$this->config->get('config_language_id') . "'
			GROUP BY r.product_id
			HAVING review_count >= 3
			ORDER BY avg_rating ASC, review_count DESC
			LIMIT " . (int)$limit
		);

		return $query->rows;
	}

	/**
	 * Get monthly trends
	 */
	public function getMonthlyTrends($date_start, $date_end) {
		$query = $this->db->query("
			SELECT
				DATE_FORMAT(date_added, '%Y-%m') as month,
				COUNT(*) as review_count,
				AVG(rating) as avg_rating,
				SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as approved_count
			FROM " . DB_PREFIX . "review
			WHERE date_added >= '" . $this->db->escape($date_start) . "'
			AND date_added <= '" . $this->db->escape($date_end . ' 23:59:59') . "'
			GROUP BY DATE_FORMAT(date_added, '%Y-%m')
			ORDER BY month ASC
		");

		return $query->rows;
	}

	/**
	 * Get rating distribution (1-5 stars)
	 */
	public function getRatingDistribution($date_start, $date_end) {
		$query = $this->db->query("
			SELECT
				rating,
				COUNT(*) as count
			FROM " . DB_PREFIX . "review
			WHERE status = 1
			AND date_added >= '" . $this->db->escape($date_start) . "'
			AND date_added <= '" . $this->db->escape($date_end . ' 23:59:59') . "'
			GROUP BY rating
			ORDER BY rating ASC
		");

		$distribution = array();
		for ($i = 1; $i <= 5; $i++) {
			$distribution[$i] = 0;
		}

		foreach ($query->rows as $row) {
			$distribution[$row['rating']] = $row['count'];
		}

		return $distribution;
	}

	/**
	 * Get most active reviewers
	 */
	public function getMostActiveReviewers($date_start, $date_end, $limit = 10) {
		$query = $this->db->query("
			SELECT
				author,
				COUNT(*) as review_count,
				AVG(rating) as avg_rating
			FROM " . DB_PREFIX . "review
			WHERE status = 1
			AND date_added >= '" . $this->db->escape($date_start) . "'
			AND date_added <= '" . $this->db->escape($date_end . ' 23:59:59') . "'
			GROUP BY author
			ORDER BY review_count DESC, avg_rating DESC
			LIMIT " . (int)$limit
		);

		return $query->rows;
	}

	/**
	 * Get product reputation report
	 */
	public function getProductReputationReport() {
		$query = $this->db->query("
			SELECT
				r.product_id,
				pd.name,
				COUNT(*) as total_reviews,
				AVG(r.rating) as average_rating,
				SUM(CASE WHEN r.rating >= 4 THEN 1 ELSE 0 END) as positive_reviews,
				SUM(CASE WHEN r.rating <= 2 THEN 1 ELSE 0 END) as negative_reviews,
				(AVG(r.rating) * 20) as reputation_score,
				MAX(r.date_added) as last_review_date
			FROM " . DB_PREFIX . "review r
			LEFT JOIN " . DB_PREFIX . "product_description pd ON (r.product_id = pd.product_id)
			WHERE r.status = 1
			AND pd.language_id = '" . (int)$this->config->get('config_language_id') . "'
			GROUP BY r.product_id
			HAVING total_reviews >= 1
			ORDER BY reputation_score DESC, total_reviews DESC
		");

		$results = array();
		foreach ($query->rows as $row) {
			// حساب اتجاه السمعة (تحسن أم تراجع)
			$trend_query = $this->db->query("
				SELECT AVG(rating) as recent_avg
				FROM " . DB_PREFIX . "review
				WHERE product_id = '" . (int)$row['product_id'] . "'
				AND status = 1
				AND date_added >= DATE_SUB(NOW(), INTERVAL 30 DAY)
			");

			$recent_avg = $trend_query->row['recent_avg'] ? $trend_query->row['recent_avg'] : $row['average_rating'];
			$trend = 'stable';
			if ($recent_avg > $row['average_rating'] + 0.2) {
				$trend = 'improving';
			} elseif ($recent_avg < $row['average_rating'] - 0.2) {
				$trend = 'declining';
			}

			$row['reputation_trend'] = $trend;
			$results[] = $row;
		}

		return $results;
	}

	// ═══════════════════════════════════════════════════════════════════════════════
	// دوال تحليل المشاعر والاحتيال - Enterprise Grade Plus
	// ═══════════════════════════════════════════════════════════════════════════════

	/**
	 * Update sentiment analysis for review
	 */
	public function updateSentimentAnalysis($review_id, $sentiment_data) {
		// حذف التحليل السابق إن وجد
		$this->db->query("DELETE FROM " . DB_PREFIX . "review_sentiment WHERE review_id = '" . (int)$review_id . "'");

		// إدراج التحليل الجديد
		$this->db->query("
			INSERT INTO " . DB_PREFIX . "review_sentiment SET
			review_id = '" . (int)$review_id . "',
			sentiment_score = '" . (float)$sentiment_data['score'] . "',
			sentiment_label = '" . $this->db->escape($sentiment_data['label']) . "',
			confidence = '" . (float)$sentiment_data['confidence'] . "',
			analyzed_date = NOW()
		");
	}

	/**
	 * Update fraud check for review
	 */
	public function updateFraudCheck($review_id, $fraud_data) {
		// حذف الفحص السابق إن وجد
		$this->db->query("DELETE FROM " . DB_PREFIX . "review_fraud_check WHERE review_id = '" . (int)$review_id . "'");

		// إدراج الفحص الجديد
		$this->db->query("
			INSERT INTO " . DB_PREFIX . "review_fraud_check SET
			review_id = '" . (int)$review_id . "',
			fraud_score = '" . (float)$fraud_data['fraud_score'] . "',
			is_suspicious = '" . (int)$fraud_data['is_suspicious'] . "',
			fraud_indicators = '" . $this->db->escape(json_encode($fraud_data['indicators'])) . "',
			checked_date = NOW()
		");
	}

	/**
	 * Get sentiment analysis for specific review
	 */
	public function getReviewSentimentAnalysis($review_id) {
		$query = $this->db->query("SELECT * FROM " . DB_PREFIX . "review_sentiment WHERE review_id = '" . (int)$review_id . "'");
		return $query->row;
	}

	/**
	 * Get fraud check for review
	 */
	public function getFraudCheck($review_id) {
		$query = $this->db->query("SELECT * FROM " . DB_PREFIX . "review_fraud_check WHERE review_id = '" . (int)$review_id . "'");
		if ($query->row) {
			$query->row['fraud_indicators'] = json_decode($query->row['fraud_indicators'], true);
		}
		return $query->row;
	}

	// ═══════════════════════════════════════════════════════════════════════════════
	// دوال الردود على التقييمات - Enterprise Grade Plus
	// ═══════════════════════════════════════════════════════════════════════════════

	/**
	 * Add reply to review
	 */
	public function addReply($review_id, $reply_data) {
		$this->db->query("
			INSERT INTO " . DB_PREFIX . "review_reply SET
			review_id = '" . (int)$review_id . "',
			reply_text = '" . $this->db->escape($reply_data['reply_text']) . "',
			author_name = '" . $this->db->escape($reply_data['author_name']) . "',
			author_type = '" . $this->db->escape($reply_data['author_type']) . "',
			date_added = '" . $this->db->escape($reply_data['date_added']) . "'
		");

		return $this->db->getLastId();
	}

	/**
	 * Get replies for review
	 */
	public function getReviewReplies($review_id) {
		$query = $this->db->query("
			SELECT * FROM " . DB_PREFIX . "review_reply
			WHERE review_id = '" . (int)$review_id . "'
			ORDER BY date_added ASC
		");

		return $query->rows;
	}

	/**
	 * Delete reply
	 */
	public function deleteReply($reply_id) {
		$this->db->query("DELETE FROM " . DB_PREFIX . "review_reply WHERE reply_id = '" . (int)$reply_id . "'");
	}
}