<?php
// Heading
$_['heading_title']                    = 'Cash Flow Forecasting';

// Text
$_['text_success']                     = 'Success: You have modified cash flow forecasting!';
$_['text_list']                        = 'Cash Flow Forecasting List';
$_['text_add']                         = 'Add Forecast';
$_['text_edit']                        = 'Edit Forecast';
$_['text_view']                        = 'View Forecast';
$_['text_default']                     = 'Default';
$_['text_enabled']                     = 'Enabled';
$_['text_disabled']                    = 'Disabled';
$_['text_yes']                         = 'Yes';
$_['text_no']                          = 'No';
$_['text_none']                        = 'None';
$_['text_select']                      = 'Select';
$_['text_loading']                     = 'Loading...';
$_['text_no_results']                  = 'No results!';
$_['text_confirm']                     = 'Are you sure?';

// Forecast specific
$_['text_forecast_info']               = 'Forecast Information';
$_['text_forecast_name']               = 'Forecast Name';
$_['text_forecast_period']             = 'Forecast Period';
$_['text_forecast_type']               = 'Forecast Type';
$_['text_forecast_method']             = 'Forecast Method';
$_['text_start_date']                  = 'Start Date';
$_['text_end_date']                    = 'End Date';
$_['text_base_date']                   = 'Base Date';
$_['text_currency']                    = 'Currency';
$_['text_confidence_level']            = 'Confidence Level';
$_['text_accuracy_rate']               = 'Accuracy Rate';

// Cash Flow Types
$_['text_type_operating']              = 'Operating';
$_['text_type_investing']              = 'Investing';
$_['text_type_financing']              = 'Financing';
$_['text_type_comprehensive']          = 'Comprehensive';

// Forecast Methods
$_['text_method_historical']           = 'Historical';
$_['text_method_regression']           = 'Regression';
$_['text_method_moving_average']       = 'Moving Average';
$_['text_method_exponential']          = 'Exponential';
$_['text_method_ai_prediction']        = 'AI Prediction';
$_['text_method_scenario_based']       = 'Scenario-Based';

// Status
$_['text_status_draft']                = 'Draft';
$_['text_status_processing']           = 'Processing';
$_['text_status_completed']            = 'Completed';
$_['text_status_approved']             = 'Approved';
$_['text_status_archived']             = 'Archived';

// Cash Flow Components
$_['text_cash_inflows']                = 'Cash Inflows';
$_['text_cash_outflows']               = 'Cash Outflows';
$_['text_net_cash_flow']               = 'Net Cash Flow';
$_['text_opening_balance']             = 'Opening Balance';
$_['text_closing_balance']             = 'Closing Balance';
$_['text_cumulative_flow']             = 'Cumulative Flow';

// Inflow Sources
$_['text_sales_receipts']              = 'Sales Receipts';
$_['text_accounts_receivable']         = 'Accounts Receivable';
$_['text_loan_proceeds']               = 'Loan Proceeds';
$_['text_investment_income']           = 'Investment Income';
$_['text_other_income']                = 'Other Income';

// Outflow Sources
$_['text_supplier_payments']           = 'Supplier Payments';
$_['text_payroll_expenses']            = 'Payroll Expenses';
$_['text_operating_expenses']          = 'Operating Expenses';
$_['text_loan_payments']               = 'Loan Payments';
$_['text_tax_payments']                = 'Tax Payments';
$_['text_capital_expenditure']         = 'Capital Expenditure';

// Scenarios
$_['text_scenarios']                   = 'Scenarios';
$_['text_base_scenario']               = 'Base Scenario';
$_['text_optimistic_scenario']         = 'Optimistic Scenario';
$_['text_pessimistic_scenario']        = 'Pessimistic Scenario';
$_['text_stress_test']                 = 'Stress Test';

// AI Features
$_['text_ai_analysis']                 = 'AI Analysis';
$_['text_pattern_recognition']         = 'Pattern Recognition';
$_['text_trend_analysis']              = 'Trend Analysis';
$_['text_anomaly_detection']           = 'Anomaly Detection';
$_['text_predictive_insights']         = 'Predictive Insights';

// Statistics
$_['text_total_forecasts']             = 'Total Forecasts';
$_['text_active_forecasts']            = 'Active Forecasts';
$_['text_accuracy_metrics']            = 'Accuracy Metrics';
$_['text_variance_analysis']           = 'Variance Analysis';

// Buttons
$_['button_add']                       = 'Add';
$_['button_edit']                      = 'Edit';
$_['button_delete']                    = 'Delete';
$_['button_save']                      = 'Save';
$_['button_cancel']                    = 'Cancel';
$_['button_close']                     = 'Close';
$_['button_back']                      = 'Back';
$_['button_view']                      = 'View';
$_['button_print']                     = 'Print';
$_['button_export']                    = 'Export';
$_['button_generate']                  = 'Generate';
$_['button_analyze']                   = 'Analyze';
$_['button_scenario']                  = 'Scenario';
$_['button_ai_predict']                = 'AI Predict';
$_['button_refresh']                   = 'Refresh';

// Columns
$_['column_forecast_name']             = 'Forecast Name';
$_['column_forecast_period']           = 'Period';
$_['column_forecast_type']             = 'Type';
$_['column_method']                    = 'Method';
$_['column_start_date']                = 'Start Date';
$_['column_end_date']                  = 'End Date';
$_['column_accuracy']                  = 'Accuracy';
$_['column_status']                    = 'Status';
$_['column_created_by']                = 'Created By';
$_['column_date_created']              = 'Date Created';
$_['column_action']                    = 'Action';

// Period columns
$_['column_period']                    = 'Period';
$_['column_inflows']                   = 'Inflows';
$_['column_outflows']                  = 'Outflows';
$_['column_net_flow']                  = 'Net Flow';
$_['column_cumulative']                = 'Cumulative';
$_['column_variance']                  = 'Variance';

// Entry fields
$_['entry_forecast_name']              = 'Forecast Name';
$_['entry_forecast_period']            = 'Forecast Period';
$_['entry_forecast_type']              = 'Forecast Type';
$_['entry_forecast_method']            = 'Forecast Method';
$_['entry_start_date']                 = 'Start Date';
$_['entry_end_date']                   = 'End Date';
$_['entry_currency']                   = 'Currency';
$_['entry_confidence_level']           = 'Confidence Level';
$_['entry_description']                = 'Description';
$_['entry_notes']                      = 'Notes';

// Help text
$_['help_forecast_name']               = 'Enter a descriptive name for the forecast';
$_['help_forecast_method']             = 'Select the appropriate forecasting method';
$_['help_confidence_level']            = 'Confidence level in the forecast (50%-99%)';
$_['help_ai_prediction']               = 'Use AI for more accurate predictions';

// Error messages
$_['error_permission']                 = 'Warning: You do not have permission to access cash flow forecasting!';
$_['error_forecast_name']              = 'Forecast name must be between 3 and 64 characters!';
$_['error_forecast_period']            = 'Please select forecast period!';
$_['error_forecast_type']              = 'Please select forecast type!';
$_['error_forecast_method']            = 'Please select forecast method!';
$_['error_start_date']                 = 'Please enter start date!';
$_['error_end_date']                   = 'Please enter end date!';
$_['error_date_range']                 = 'End date must be after start date!';
$_['error_currency']                   = 'Please select currency!';
$_['error_confidence_level']           = 'Confidence level must be between 50% and 99%!';
$_['error_insufficient_data']          = 'Insufficient data for forecasting!';
$_['error_ai_service']                 = 'AI service error!';

// Success messages
$_['success_forecast_added']           = 'Forecast added successfully!';
$_['success_forecast_updated']         = 'Forecast updated successfully!';
$_['success_forecast_deleted']         = 'Forecast deleted successfully!';
$_['success_forecast_generated']       = 'Forecast generated successfully!';
$_['success_ai_analysis_completed']    = 'AI analysis completed successfully!';
$_['success_scenario_created']         = 'Scenario created successfully!';

// Confirmation messages
$_['confirm_delete']                   = 'Are you sure you want to delete this forecast?';
$_['confirm_generate']                 = 'Are you sure you want to generate the forecast?';
$_['confirm_ai_analysis']              = 'Are you sure you want to run AI analysis?';

// Tabs
$_['tab_general']                      = 'General';
$_['tab_parameters']                   = 'Parameters';
$_['tab_cash_flows']                   = 'Cash Flows';
$_['tab_scenarios']                    = 'Scenarios';
$_['tab_analysis']                     = 'Analysis';
$_['tab_ai_insights']                  = 'AI Insights';

// Reports
$_['text_forecast_report']             = 'Forecast Report';
$_['text_cash_flow_statement']         = 'Cash Flow Statement';
$_['text_variance_report']             = 'Variance Report';
$_['text_scenario_comparison']         = 'Scenario Comparison';
$_['text_accuracy_report']             = 'Accuracy Report';

// Filters
$_['text_filter_type']                 = 'Filter by Type';
$_['text_filter_method']               = 'Filter by Method';
$_['text_filter_status']               = 'Filter by Status';
$_['text_filter_date_range']           = 'Filter by Date Range';

// Processing status
$_['text_processing']                  = 'Processing...';
$_['text_generating']                  = 'Generating...';
$_['text_analyzing']                   = 'Analyzing...';
$_['text_ai_processing']               = 'AI Processing...';
$_['text_completed']                   = 'Completed';
$_['text_failed']                      = 'Failed';

// Additional features
$_['text_cash_position']               = 'Cash Position';
$_['text_liquidity_analysis']          = 'Liquidity Analysis';
$_['text_working_capital']             = 'Working Capital';
$_['text_seasonal_patterns']           = 'Seasonal Patterns';
$_['text_risk_assessment']             = 'Risk Assessment';
$_['text_sensitivity_analysis']        = 'Sensitivity Analysis';

// Warnings
$_['warning_low_accuracy']             = 'Warning: Low forecast accuracy!';
$_['warning_negative_flow']            = 'Warning: Negative cash flow expected!';
$_['warning_insufficient_liquidity']   = 'Warning: Insufficient liquidity!';
?>
