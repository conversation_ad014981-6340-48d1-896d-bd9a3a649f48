<?php
// Heading
$_['heading_title']          = 'Measurement Units';

// Text
$_['text_success_add']       = 'Success: You have added a measurement unit!';
$_['text_success_edit']      = 'Success: You have modified a measurement unit!';
$_['text_success_delete']    = 'Success: You have deleted a measurement unit!';
$_['text_list']              = 'Measurement Unit List';
$_['text_add']               = 'Add Measurement Unit';
$_['text_edit']              = 'Edit Measurement Unit';
$_['text_filter']            = 'Filter';
$_['text_search']            = 'Search';
$_['text_no_results']        = 'There are no measurement units!';
$_['text_confirm']           = 'Are you sure?';
$_['text_pagination']        = 'Showing %d to %d of %d (%d Pages)';
$_['text_loading']           = 'Loading...';
$_['text_no_selected']       = 'You have not selected any measurement unit!';

// Column
$_['column_code']            = 'Code';
$_['column_desc_en']         = 'Description (English)';
$_['column_desc_ar']         = 'Description (Arabic)';
$_['column_action']          = 'Action';

// Entry
$_['entry_code']             = 'Code';
$_['entry_desc_en']          = 'Description (English)';
$_['entry_desc_ar']          = 'Description (Arabic)';

// Button
$_['button_add']             = 'Add New';
$_['button_edit']            = 'Edit';
$_['button_delete']          = 'Delete';
$_['button_filter']          = 'Filter';
$_['button_clear']           = 'Clear';
$_['button_save']            = 'Save';
$_['button_cancel']          = 'Cancel';
$_['button_search']          = 'Search';

// Error
$_['error_permission']       = 'Warning: You do not have permission to modify!';
$_['error_code']             = 'Unit Code must be between 1 and 10 characters!';
$_['error_code_exists']      = 'This code is already in use!';
$_['error_desc_en']          = 'English Description must be between 1 and 255 characters!';
$_['error_unit_in_use']      = 'Warning: This unit cannot be deleted as it is currently assigned to %s products!';