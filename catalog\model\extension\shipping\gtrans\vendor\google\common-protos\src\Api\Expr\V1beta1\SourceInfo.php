<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/expr/v1beta1/source.proto

namespace Google\Api\Expr\V1beta1;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Source information collected at parse time.
 *
 * Generated from protobuf message <code>google.api.expr.v1beta1.SourceInfo</code>
 */
class SourceInfo extends \Google\Protobuf\Internal\Message
{
    /**
     * The location name. All position information attached to an expression is
     * relative to this location.
     * The location could be a file, UI element, or similar. For example,
     * `acme/app/AnvilPolicy.cel`.
     *
     * Generated from protobuf field <code>string location = 2;</code>
     */
    private $location = '';
    /**
     * Monotonically increasing list of character offsets where newlines appear.
     * The line number of a given position is the index `i` where for a given
     * `id` the `line_offsets[i] < id_positions[id] < line_offsets[i+1]`. The
     * column may be derivd from `id_positions[id] - line_offsets[i]`.
     *
     * Generated from protobuf field <code>repeated int32 line_offsets = 3;</code>
     */
    private $line_offsets;
    /**
     * A map from the parse node id (e.g. `Expr.id`) to the character offset
     * within source.
     *
     * Generated from protobuf field <code>map<int32, int32> positions = 4;</code>
     */
    private $positions;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $location
     *           The location name. All position information attached to an expression is
     *           relative to this location.
     *           The location could be a file, UI element, or similar. For example,
     *           `acme/app/AnvilPolicy.cel`.
     *     @type int[]|\Google\Protobuf\Internal\RepeatedField $line_offsets
     *           Monotonically increasing list of character offsets where newlines appear.
     *           The line number of a given position is the index `i` where for a given
     *           `id` the `line_offsets[i] < id_positions[id] < line_offsets[i+1]`. The
     *           column may be derivd from `id_positions[id] - line_offsets[i]`.
     *     @type array|\Google\Protobuf\Internal\MapField $positions
     *           A map from the parse node id (e.g. `Expr.id`) to the character offset
     *           within source.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Api\Expr\V1Beta1\Source::initOnce();
        parent::__construct($data);
    }

    /**
     * The location name. All position information attached to an expression is
     * relative to this location.
     * The location could be a file, UI element, or similar. For example,
     * `acme/app/AnvilPolicy.cel`.
     *
     * Generated from protobuf field <code>string location = 2;</code>
     * @return string
     */
    public function getLocation()
    {
        return $this->location;
    }

    /**
     * The location name. All position information attached to an expression is
     * relative to this location.
     * The location could be a file, UI element, or similar. For example,
     * `acme/app/AnvilPolicy.cel`.
     *
     * Generated from protobuf field <code>string location = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setLocation($var)
    {
        GPBUtil::checkString($var, True);
        $this->location = $var;

        return $this;
    }

    /**
     * Monotonically increasing list of character offsets where newlines appear.
     * The line number of a given position is the index `i` where for a given
     * `id` the `line_offsets[i] < id_positions[id] < line_offsets[i+1]`. The
     * column may be derivd from `id_positions[id] - line_offsets[i]`.
     *
     * Generated from protobuf field <code>repeated int32 line_offsets = 3;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getLineOffsets()
    {
        return $this->line_offsets;
    }

    /**
     * Monotonically increasing list of character offsets where newlines appear.
     * The line number of a given position is the index `i` where for a given
     * `id` the `line_offsets[i] < id_positions[id] < line_offsets[i+1]`. The
     * column may be derivd from `id_positions[id] - line_offsets[i]`.
     *
     * Generated from protobuf field <code>repeated int32 line_offsets = 3;</code>
     * @param int[]|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setLineOffsets($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::INT32);
        $this->line_offsets = $arr;

        return $this;
    }

    /**
     * A map from the parse node id (e.g. `Expr.id`) to the character offset
     * within source.
     *
     * Generated from protobuf field <code>map<int32, int32> positions = 4;</code>
     * @return \Google\Protobuf\Internal\MapField
     */
    public function getPositions()
    {
        return $this->positions;
    }

    /**
     * A map from the parse node id (e.g. `Expr.id`) to the character offset
     * within source.
     *
     * Generated from protobuf field <code>map<int32, int32> positions = 4;</code>
     * @param array|\Google\Protobuf\Internal\MapField $var
     * @return $this
     */
    public function setPositions($var)
    {
        $arr = GPBUtil::checkMapField($var, \Google\Protobuf\Internal\GPBType::INT32, \Google\Protobuf\Internal\GPBType::INT32);
        $this->positions = $arr;

        return $this;
    }

}

