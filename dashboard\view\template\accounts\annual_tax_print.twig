<!DOCTYPE html>
<html dir="{{ direction }}" lang="{{ lang }}" class="no-js">
<head>
<meta charset="UTF-8" />
<title>{{ heading_title }}</title>
<base href="{{ base }}" />
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
<link href="view/stylesheet/bootstrap.css" rel="stylesheet" media="screen" />
<style>
@media print {
  body { margin: 0; }
  .no-print { display: none !important; }
  .page-break { page-break-before: always; }
}

body {
  font-family: Arial, sans-serif;
  font-size: 11px;
  line-height: 1.3;
}

.header {
  text-align: center;
  margin-bottom: 30px;
  border-bottom: 2px solid #333;
  padding-bottom: 20px;
}

.company-logo {
  max-height: 80px;
  margin-bottom: 10px;
}

.company-name {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}

.report-title {
  font-size: 18px;
  font-weight: bold;
  color: #666;
  margin-bottom: 10px;
}

.report-info {
  font-size: 10px;
  color: #888;
}

.summary-box {
  background-color: #f8f9fa;
  padding: 15px;
  margin-bottom: 20px;
  border: 1px solid #dee2e6;
}

.compliance-box {
  background-color: #d4edda;
  padding: 15px;
  margin-bottom: 20px;
  border-left: 4px solid #28a745;
}

.violations-box {
  background-color: #f8d7da;
  padding: 15px;
  margin-bottom: 20px;
  border-left: 4px solid #dc3545;
}

.tax-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 20px;
  font-size: 10px;
}

.tax-table th,
.tax-table td {
  border: 1px solid #dee2e6;
  padding: 6px;
  text-align: left;
}

.tax-table th {
  background-color: #f8f9fa;
  font-weight: bold;
}

.text-right {
  text-align: right;
}

.text-center {
  text-align: center;
}

.text-success {
  color: #28a745;
}

.text-warning {
  color: #ffc107;
}

.text-danger {
  color: #dc3545;
}

.text-info {
  color: #17a2b8;
}

.footer {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #dee2e6;
  font-size: 9px;
  color: #888;
}

.signature-section {
  margin-top: 40px;
  display: flex;
  justify-content: space-between;
}

.signature-box {
  width: 200px;
  text-align: center;
}

.signature-line {
  border-top: 1px solid #333;
  margin-top: 50px;
  padding-top: 5px;
}

.watermark {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(-45deg);
  font-size: 72px;
  color: rgba(0,0,0,0.05);
  z-index: -1;
  pointer-events: none;
}

.compliance-badge {
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 9px;
  font-weight: bold;
}

.compliance-excellent {
  background-color: #28a745;
  color: white;
}

.compliance-good {
  background-color: #17a2b8;
  color: white;
}

.compliance-fair {
  background-color: #ffc107;
  color: black;
}

.compliance-poor {
  background-color: #dc3545;
  color: white;
}

.eta-badge {
  background-color: #007bff;
  color: white;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 9px;
  font-weight: bold;
}
</style>
</head>
<body>

<!-- Watermark -->
<div class="watermark">{{ text_confidential }}</div>

<!-- Header -->
<div class="header">
  {% if company_logo %}
  <img src="{{ company_logo }}" alt="{{ company_name }}" class="company-logo">
  {% endif %}
  <div class="company-name">{{ company_name }}</div>
  <div class="report-title">{{ heading_title }}</div>
  <div class="report-info">
    {{ text_generated_on }}: {{ generated_date }} | {{ text_generated_by }}: {{ generated_by }}
  </div>
</div>

<!-- Report Information -->
<div class="summary-box">
  <h3 style="margin-top: 0;">{{ text_report_info }}</h3>
  <div style="display: flex; justify-content: space-between;">
    <div>
      <strong>{{ text_year }}:</strong> {{ tax_data.filter_data.year }}<br>
      <strong>{{ text_report_type }}:</strong> {{ tax_data.filter_data.report_type }}<br>
      <strong>{{ text_tax_type }}:</strong> {{ tax_data.filter_data.tax_type }}
    </div>
    <div>
      <strong>{{ text_total_transactions }}:</strong> {{ tax_data.summary.total_transactions }}<br>
      <strong>{{ text_total_tax_liability }}:</strong> {{ tax_data.summary.total_tax_liability }}<br>
      <strong>{{ text_eta_ready }}:</strong> 
      {% if tax_data.filter_data.include_eta_format %}
        <span class="eta-badge">{{ text_yes }}</span>
      {% else %}
        {{ text_no }}
      {% endif %}
    </div>
  </div>
</div>

<!-- Tax Summary -->
<h3>{{ text_summary }}</h3>
<table class="tax-table">
  <thead>
    <tr>
      <th>{{ text_metric }}</th>
      <th class="text-right">{{ text_amount }}</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>{{ text_total_transactions }}</td>
      <td class="text-right">{{ tax_data.summary.total_transactions }}</td>
    </tr>
    <tr>
      <td>{{ text_total_tax_expense }}</td>
      <td class="text-right">{{ tax_data.summary.total_tax_expense }}</td>
    </tr>
    <tr>
      <td>{{ text_total_tax_liability }}</td>
      <td class="text-right">{{ tax_data.summary.total_tax_liability }}</td>
    </tr>
    <tr>
      <td>{{ text_net_tax_impact }}</td>
      <td class="text-right">{{ tax_data.summary.net_tax_impact }}</td>
    </tr>
    <tr>
      <td>{{ text_average_tax_amount }}</td>
      <td class="text-right">{{ tax_data.summary.average_tax_amount }}</td>
    </tr>
    <tr>
      <td>{{ text_tax_efficiency_ratio }}</td>
      <td class="text-right">{{ tax_data.summary.tax_efficiency_ratio }}%</td>
    </tr>
  </tbody>
</table>

<!-- Tax Types Breakdown -->
{% if tax_data.tax_types_breakdown %}
<h3>{{ text_tax_types_breakdown }}</h3>
<table class="tax-table">
  <thead>
    <tr>
      <th>{{ text_tax_type }}</th>
      <th class="text-right">{{ text_amount }}</th>
      <th class="text-right">{{ text_percentage }}</th>
      <th class="text-right">{{ text_transactions_count }}</th>
    </tr>
  </thead>
  <tbody>
    {% for tax_type in tax_data.tax_types_breakdown.tax_types %}
    <tr>
      <td>{{ tax_type.tax_type }}</td>
      <td class="text-right">{{ tax_type.tax_amount }}</td>
      <td class="text-right">{{ tax_type.percentage }}%</td>
      <td class="text-right">{{ tax_type.transactions_count }}</td>
    </tr>
    {% endfor %}
  </tbody>
  <tfoot>
    <tr style="background-color: #e2e3e5; font-weight: bold;">
      <td>{{ text_total }}</td>
      <td class="text-right">{{ tax_data.tax_types_breakdown.total_amount }}</td>
      <td class="text-right">100%</td>
      <td class="text-right">-</td>
    </tr>
  </tfoot>
</table>
{% endif %}

<!-- Monthly Breakdown -->
{% if tax_data.monthly_breakdown %}
<h3>{{ text_monthly_breakdown }}</h3>
<table class="tax-table">
  <thead>
    <tr>
      <th>{{ text_month }}</th>
      <th class="text-right">{{ text_transactions_count }}</th>
      <th class="text-right">{{ text_tax_expense }}</th>
      <th class="text-right">{{ text_tax_liability }}</th>
      <th class="text-right">{{ text_net_tax_impact }}</th>
    </tr>
  </thead>
  <tbody>
    {% for month in tax_data.monthly_breakdown.monthly_data %}
    <tr>
      <td>{{ month.month_name }}</td>
      <td class="text-right">{{ month.transactions_count }}</td>
      <td class="text-right">{{ month.tax_expense }}</td>
      <td class="text-right">{{ month.tax_liability }}</td>
      <td class="text-right">{{ month.net_tax_impact }}</td>
    </tr>
    {% endfor %}
  </tbody>
  <tfoot>
    <tr style="background-color: #e2e3e5; font-weight: bold;">
      <td>{{ text_total }}</td>
      <td class="text-right">-</td>
      <td class="text-right">{{ tax_data.monthly_breakdown.total_tax_expense }}</td>
      <td class="text-right">{{ tax_data.monthly_breakdown.total_tax_liability }}</td>
      <td class="text-right">-</td>
    </tr>
  </tfoot>
</table>
{% endif %}

<!-- Quarterly Analysis -->
{% if tax_data.quarterly_analysis %}
<h3>{{ text_quarterly_analysis }}</h3>
<table class="tax-table">
  <thead>
    <tr>
      <th>{{ text_quarter }}</th>
      <th class="text-right">{{ text_transactions_count }}</th>
      <th class="text-right">{{ text_tax_expense }}</th>
      <th class="text-right">{{ text_tax_liability }}</th>
    </tr>
  </thead>
  <tbody>
    {% for quarter in tax_data.quarterly_analysis %}
    <tr>
      <td>{{ quarter.quarter }}</td>
      <td class="text-right">{{ quarter.transactions_count }}</td>
      <td class="text-right">{{ quarter.tax_expense }}</td>
      <td class="text-right">{{ quarter.tax_liability }}</td>
    </tr>
    {% endfor %}
  </tbody>
</table>
{% endif %}

<!-- Compliance Status -->
{% if compliance_analysis %}
<div class="compliance-box">
  <h3 style="margin-top: 0;">{{ text_compliance_status }}</h3>
  <div style="display: flex; justify-content: space-between; margin-bottom: 15px;">
    <div>
      <strong>{{ text_compliance_score }}:</strong> 
      <span class="compliance-badge compliance-{% if compliance_analysis.compliance_score >= 90 %}excellent{% elseif compliance_analysis.compliance_score >= 70 %}good{% elseif compliance_analysis.compliance_score >= 50 %}fair{% else %}poor{% endif %}">
        {{ compliance_analysis.compliance_score }}%
      </span>
    </div>
    <div>
      <strong>{{ text_total_tax_liability }}:</strong> {{ compliance_analysis.total_tax_liability }}
    </div>
  </div>
  
  {% if compliance_analysis.violations %}
  <h4>{{ text_violations }}</h4>
  <ul style="margin: 0; padding-left: 20px;">
    {% for violation in compliance_analysis.violations %}
    <li>{{ violation.description }} 
      {% if violation.severity %}
        <span class="compliance-badge compliance-{% if violation.severity == 'critical' %}poor{% elseif violation.severity == 'high' %}fair{% else %}good{% endif %}">
          {{ violation.severity }}
        </span>
      {% endif %}
    </li>
    {% endfor %}
  </ul>
  {% endif %}
  
  {% if compliance_analysis.recommendations %}
  <h4>{{ text_recommendations }}</h4>
  <ul style="margin: 0; padding-left: 20px;">
    {% for recommendation in compliance_analysis.recommendations %}
    <li>{{ recommendation }}</li>
    {% endfor %}
  </ul>
  {% endif %}
</div>
{% endif %}

<!-- Signatures -->
<div class="signature-section">
  <div class="signature-box">
    <div class="signature-line">{{ text_prepared_by }}</div>
  </div>
  <div class="signature-box">
    <div class="signature-line">{{ text_reviewed_by }}</div>
  </div>
  <div class="signature-box">
    <div class="signature-line">{{ text_approved_by }}</div>
  </div>
</div>

<!-- Footer -->
<div class="footer">
  <div style="display: flex; justify-content: space-between;">
    <div>
      {{ text_confidential }} - {{ text_internal_use }}
    </div>
    <div>
      {{ text_egyptian_tax_law }} | {{ text_eta_ready }} | {{ text_audit_trail }}
    </div>
    <div>
      {{ text_page }} 1 {{ text_of }} 1
    </div>
  </div>
</div>

<script>
// Auto print when page loads
window.onload = function() {
  window.print();
};
</script>

</body>
</html>
