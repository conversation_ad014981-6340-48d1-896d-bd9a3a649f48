{{ header }}
{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <a href="{{ back }}" data-bs-toggle="tooltip" title="{{ button_back }}" class="btn btn-light"><i class="fas fa-reply"></i></a>
        <a href="{{ print }}" data-bs-toggle="tooltip" title="{{ button_print }}" class="btn btn-success" target="_blank"><i class="fas fa-print"></i></a>
        <a href="{{ export }}" data-bs-toggle="tooltip" title="{{ button_export }}" class="btn btn-primary"><i class="fas fa-file-excel"></i></a>
        {% if status == 'in_progress' %}
          <a href="{{ complete }}" data-bs-toggle="tooltip" title="{{ button_complete }}" class="btn btn-warning" onclick="return confirm('{{ text_confirm_complete }}');"><i class="fas fa-check"></i></a>
        {% endif %}
        {% if status == 'draft' or status == 'in_progress' %}
          <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-danger" onclick="return confirm('{{ text_confirm_cancel }}');"><i class="fas fa-ban"></i></a>
        {% endif %}
      </div>
      <h1>{{ heading_stocktake_view }}</h1>
      <ol class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
          <li class="breadcrumb-item"><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {% if success %}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{ success }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {% endif %}
    <div class="row">
      <div class="col-md-4">
        <div class="card mb-3">
          <div class="card-header"><i class="fas fa-info-circle"></i> {{ text_stocktake_details }}</div>
          <div class="card-body">
            <table class="table table-bordered">
              <tr>
                <td><strong>{{ entry_reference }}</strong></td>
                <td>{{ reference }}</td>
              </tr>
              <tr>
                <td><strong>{{ entry_branch }}</strong></td>
                <td>{{ branch_name }}</td>
              </tr>
              <tr>
                <td><strong>{{ entry_stocktake_date }}</strong></td>
                <td>{{ stocktake_date }}</td>
              </tr>
              <tr>
                <td><strong>{{ entry_type }}</strong></td>
                <td>{{ type_text }}</td>
              </tr>
              <tr>
                <td><strong>{{ entry_status }}</strong></td>
                <td>
                  {% if status == 'draft' %}
                    <span class="badge bg-secondary">{{ status_text }}</span>
                  {% elseif status == 'in_progress' %}
                    <span class="badge bg-primary">{{ status_text }}</span>
                  {% elseif status == 'completed' %}
                    <span class="badge bg-success">{{ status_text }}</span>
                  {% elseif status == 'cancelled' %}
                    <span class="badge bg-danger">{{ status_text }}</span>
                  {% endif %}
                </td>
              </tr>
              <tr>
                <td><strong>{{ text_created_by }}</strong></td>
                <td>{{ created_by_name }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_date_created }}</strong></td>
                <td>{{ date_added }}</td>
              </tr>
              {% if status == 'completed' %}
                <tr>
                  <td><strong>{{ text_completed_by }}</strong></td>
                  <td>{{ completed_by_name }}</td>
                </tr>
                <tr>
                  <td><strong>{{ text_date_completed }}</strong></td>
                  <td>{{ date_completed }}</td>
                </tr>
              {% endif %}
            </table>
            {% if notes %}
              <div class="mt-3">
                <strong>{{ entry_notes }}</strong>
                <p>{{ notes }}</p>
              </div>
            {% endif %}
          </div>
        </div>
        <div class="card">
          <div class="card-header"><i class="fas fa-chart-pie"></i> {{ text_stocktake_summary }}</div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-6">
                <div class="mb-3">
                  <strong>{{ text_total_products }}</strong>
                  <div class="fs-4">{{ total_products }}</div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="mb-3">
                  <strong>{{ text_total_expected }}</strong>
                  <div class="fs-4">{{ total_expected }}</div>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-md-6">
                <div class="mb-3">
                  <strong>{{ text_total_counted }}</strong>
                  <div class="fs-4">{{ total_counted }}</div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="mb-3">
                  <strong>{{ text_total_variance }}</strong>
                  <div class="fs-4 {% if total_variance < 0 %}text-danger{% elseif total_variance > 0 %}text-success{% endif %}">{{ total_variance }}</div>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-md-6">
                <div class="mb-3">
                  <strong>{{ text_variance_percentage }}</strong>
                  <div class="fs-4 {% if variance_percentage < 0 %}text-danger{% elseif variance_percentage > 0 %}text-success{% endif %}">{{ variance_percentage }}%</div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="mb-3">
                  <strong>{{ text_variance_value }}</strong>
                  <div class="fs-4 {% if variance_value < 0 %}text-danger{% elseif variance_value > 0 %}text-success{% endif %}">{{ variance_value }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-8">
        <div class="card">
          <div class="card-header"><i class="fas fa-list"></i> {{ text_stocktake_products }}</div>
          <div class="card-body">
            <div class="table-responsive">
              <table class="table table-bordered table-hover">
                <thead>
                  <tr>
                    <td class="text-start">{{ column_product }}</td>
                    <td class="text-start">{{ column_model }}</td>
                    <td class="text-start">{{ column_sku }}</td>
                    <td class="text-start">{{ column_unit }}</td>
                    <td class="text-end">{{ column_expected_quantity }}</td>
                    <td class="text-end">{{ column_counted_quantity }}</td>
                    <td class="text-end">{{ column_variance_quantity }}</td>
                    <td class="text-end">{{ column_variance_percentage }}</td>
                    <td class="text-start">{{ column_notes }}</td>
                  </tr>
                </thead>
                <tbody>
                  {% if products %}
                    {% for product in products %}
                      <tr>
                        <td class="text-start">{{ product.product_name }}</td>
                        <td class="text-start">{{ product.model }}</td>
                        <td class="text-start">{{ product.sku }}</td>
                        <td class="text-start">{{ product.unit_name }}</td>
                        <td class="text-end">{{ product.expected_quantity }}</td>
                        <td class="text-end">{{ product.counted_quantity }}</td>
                        <td class="text-end {% if product.variance_quantity < 0 %}text-danger{% elseif product.variance_quantity > 0 %}text-success{% endif %}">{{ product.variance_quantity }}</td>
                        <td class="text-end {% if product.variance_percentage < 0 %}text-danger{% elseif product.variance_percentage > 0 %}text-success{% endif %}">{{ product.variance_percentage }}%</td>
                        <td class="text-start">{{ product.notes }}</td>
                      </tr>
                    {% endfor %}
                  {% else %}
                    <tr>
                      <td class="text-center" colspan="9">{{ text_no_results }}</td>
                    </tr>
                  {% endif %}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{{ footer }}
