<?php
/**
 * تحكم قائمة التدفقات النقدية الشاملة والمتكاملة
 * يجمع بين القوة والبساطة - يتفوق على SAP وOracle في سهولة الاستخدام
 * مع الاحتفاظ بالقوة والمرونة المطلوبة للشركات الكبيرة
 * متوافق مع معايير المحاسبة المصرية ومتطلبات ETA
 */
class ControllerAccountsCashFlow extends Controller {
    private $error = array();
    private $central_service;

    public function __construct($registry) {
        parent::__construct($registry);

        // تحميل الخدمات المركزية
        $this->load->model('core/central_service_manager');
        $this->central_service = $this->model_core_central_service_manager;
    }

    public function index() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/cash_flow') ||
            !$this->user->hasKey('accounting_cash_flow_view')) {

            $this->central_service->logActivity('unauthorized_access', 'accounts',
                $this->language->get('log_unauthorized_access_cash_flow'), [
                'user_id' => $this->user->getId(),
                'ip_address' => $this->request->server['REMOTE_ADDR']
            ]);

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/cash_flow');
        $this->document->setTitle($this->language->get('heading_title'));

        // إضافة CSS و JavaScript المتقدم (من advanced)
        $this->document->addStyle('view/stylesheet/accounts/cash_flow.css');
        $this->document->addScript('view/javascript/accounts/cash_flow.js');
        $this->document->addScript('view/javascript/jquery/accounting.min.js');
        $this->document->addScript('view/javascript/jquery/select2.min.js');
        $this->document->addStyle('view/javascript/jquery/select2.min.css');
        $this->document->addScript('view/javascript/jquery/daterangepicker.min.js');
        $this->document->addStyle('view/javascript/jquery/daterangepicker.css');
        $this->document->addScript('view/javascript/jquery/chart.min.js');

        // تسجيل الوصول للشاشة
        $this->central_service->logActivity('view', 'accounts',
            $this->language->get('log_view_cash_flow_screen'), [
            'user_id' => $this->user->getId(),
            'screen' => 'accounts/cash_flow'
        ]);

        $data['action'] = $this->url->link('accounts/cash_flow/print', 'user_token=' . $this->session->data['user_token'], true);

        $data['heading_title'] = $this->language->get('heading_title');
        // بقية النصوص...

        $data['user_token'] = $this->session->data['user_token'];
        $data['error_warning'] = isset($this->error['warning']) ? $this->error['warning'] : '';

        // اجلب التاريخ الافتراضي أو من GET/POST
        if (isset($this->request->post['date_start'])) {
            $data['date_start'] = $this->request->post['date_start'];
        } else {
            $data['date_start'] = date('Y-01-01');
        }
        if (isset($this->request->post['date_end'])) {
            $data['date_end'] = $this->request->post['date_end'];
        } else {
            $data['date_end'] = date('Y-m-d');
        }

        // تحميل أجزاء التصميم
        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        // حمل الفيو
        $this->response->setOutput($this->load->view('accounts/cash_flow_form', $data));
    }

    /**
     * توليد قائمة التدفقات النقدية المتقدمة (مدمج من advanced)
     */
    public function generate() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/cash_flow') ||
            !$this->user->hasKey('accounting_cash_flow_generate')) {

            $this->central_service->logActivity('unauthorized_generate', 'accounts',
                $this->language->get('log_unauthorized_generate_cash_flow'), [
                'user_id' => $this->user->getId(),
                'action' => 'generate_cash_flow'
            ]);

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/cash_flow');
        $this->load->model('accounts/cash_flow');

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm()) {
            try {
                $filter_data = $this->prepareFilterData();

                // تسجيل توليد التقرير
                $this->central_service->logActivity('generate_report', 'accounts',
                    $this->language->get('log_generate_cash_flow_period') . ': ' . $filter_data['date_start'] . ' ' . $this->language->get('text_to') . ' ' . $filter_data['date_end'], [
                    'user_id' => $this->user->getId(),
                    'date_start' => $filter_data['date_start'],
                    'date_end' => $filter_data['date_end'],
                    'method' => $filter_data['method'] ?? 'indirect'
                ]);

                $cash_flow_data = $this->model_accounts_cash_flow->generateCashFlow($filter_data);

                // إرسال إشعار للمحاسب الرئيسي
                $this->central_service->sendNotification(
                    'cash_flow_generated',
                    $this->language->get('text_generate_cash_flow'),
                    $this->language->get('text_cash_flow_generated_notification') . ' ' . $filter_data['date_start'] . ' ' . $this->language->get('text_to') . ' ' . $filter_data['date_end'] . ' ' . $this->language->get('text_by') . ' ' . $this->user->getFirstName(),
                    [$this->config->get('config_chief_accountant_id')],
                    [
                        'period' => $filter_data['date_start'] . ' - ' . $filter_data['date_end'],
                        'user_name' => $this->user->getFirstName() . ' ' . $this->user->getLastName(),
                        'net_cash_flow' => $cash_flow_data['totals']['net_cash_flow'] ?? 0
                    ]
                );

                $this->session->data['success'] = $this->language->get('text_success_generate');
                $this->session->data['cash_flow_data'] = $cash_flow_data;

                // إعادة توجيه حسب الإجراء المطلوب
                if (isset($this->request->post['generate_and_new'])) {
                    $this->response->redirect($this->url->link('accounts/cash_flow', 'user_token=' . $this->session->data['user_token'], true));
                } elseif (isset($this->request->post['generate_and_export'])) {
                    $this->response->redirect($this->url->link('accounts/cash_flow/export', 'format=excel&user_token=' . $this->session->data['user_token'], true));
                } else {
                    $this->response->redirect($this->url->link('accounts/cash_flow/view', 'user_token=' . $this->session->data['user_token'], true));
                }
            } catch (Exception $e) {
                $this->error['warning'] = $e->getMessage();
            }
        }

        $this->getForm();
    }

    /**
     * تصدير قائمة التدفقات النقدية (مدمج من advanced)
     */
    public function export() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/cash_flow') ||
            !$this->user->hasKey('accounting_cash_flow_export')) {

            $this->central_service->logActivity('unauthorized_export', 'accounts',
                $this->language->get('log_unauthorized_export_cash_flow'), [
                'user_id' => $this->user->getId(),
                'action' => 'export_cash_flow'
            ]);

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/cash_flow');
        $this->load->model('accounts/cash_flow');

        if (!isset($this->session->data['cash_flow_data'])) {
            $this->session->data['error'] = $this->language->get('error_no_data');
            $this->response->redirect($this->url->link('accounts/cash_flow', 'user_token=' . $this->session->data['user_token'], true));
            return;
        }

        $format = $this->request->get['format'] ?? 'excel';
        $cash_flow_data = $this->session->data['cash_flow_data'];
        $filter_data = $this->session->data['cash_flow_filter'] ?? array();

        // تسجيل عملية التصدير
        $this->central_service->logActivity('export', 'accounts',
            $this->language->get('log_export_cash_flow') . ' - ' . strtoupper($format), [
            'user_id' => $this->user->getId(),
            'format' => $format,
            'period' => ($filter_data['date_start'] ?? '') . ' - ' . ($filter_data['date_end'] ?? '')
        ]);

        // إرسال إشعار للمحاسب الرئيسي
        $this->central_service->sendNotification(
            'cash_flow_exported',
            $this->language->get('text_export_cash_flow'),
            $this->language->get('text_cash_flow_exported_notification') . ' ' . $this->language->get('text_format') . ' ' . strtoupper($format) . ' ' . $this->language->get('text_by') . ' ' . $this->user->getFirstName(),
            [$this->config->get('config_chief_accountant_id')],
            [
                'format' => $format,
                'period' => ($filter_data['date_start'] ?? '') . ' - ' . ($filter_data['date_end'] ?? ''),
                'user_name' => $this->user->getFirstName() . ' ' . $this->user->getLastName()
            ]
        );

        switch ($format) {
            case 'excel':
                $this->exportToExcel($cash_flow_data, $filter_data);
                break;
            case 'pdf':
                $this->exportToPdf($cash_flow_data, $filter_data);
                break;
            case 'csv':
                $this->exportToCsv($cash_flow_data, $filter_data);
                break;
            default:
                $this->exportToExcel($cash_flow_data, $filter_data);
        }
    }

    public function print() {
        $this->load->language('accounts/cash_flow');
        $this->load->model('accounts/cash_flow');

        $date_start = $this->request->post['date_start'] ?: date('Y-01-01');
        $date_end   = $this->request->post['date_end'] ?: date('Y-m-d');

        $data['start_date'] = $date_start;
        $data['end_date']   = $date_end;

        // 1) أحضر بيانات التدفقات
        $results = $this->model_accounts_cash_flow->getCashFlowData($date_start, $date_end);

        $data['operating'] = $results['operating'];
        $data['investing'] = $results['investing'];
        $data['financing'] = $results['financing'];

        // 2) رصيد النقدية أول الفترة
        $openingBalance = $this->model_accounts_cash_flow->getOpeningCashBalance($date_start);

        // 3) صافي التغير
        $netChange = $results['net_change'];

        // 4) الرصيد الختامي
        $closingBalance = $openingBalance + $netChange;

        $data['total_operating'] = $results['total_operating'];
        $data['total_investing'] = $results['total_investing'];
        $data['total_financing'] = $results['total_financing'];
        $data['net_change'] = $netChange;
        $data['opening_balance'] = $openingBalance;
        $data['closing_balance'] = $closingBalance;

        // اعداد عرض
        $this->response->setOutput($this->load->view('accounts/cash_flow_list', $data));
    }

    /**
     * التحقق من صحة البيانات (من advanced)
     */
    protected function validateForm() {
        if (!$this->user->hasPermission('access', 'accounts/cash_flow')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }

        if (empty($this->request->post['date_start'])) {
            $this->error['date_start'] = $this->language->get('error_date_start');
        }

        if (empty($this->request->post['date_end'])) {
            $this->error['date_end'] = $this->language->get('error_date_end');
        }

        if (!empty($this->request->post['date_start']) && !empty($this->request->post['date_end'])) {
            if (strtotime($this->request->post['date_start']) > strtotime($this->request->post['date_end'])) {
                $this->error['date_range'] = $this->language->get('error_date_range');
            }
        }

        return !$this->error;
    }

    /**
     * إعداد بيانات الفلترة (من advanced)
     */
    protected function prepareFilterData() {
        return array(
            'date_start' => $this->request->post['date_start'] ?: date('Y-01-01'),
            'date_end' => $this->request->post['date_end'] ?: date('Y-m-d'),
            'method' => $this->request->post['method'] ?? 'indirect',
            'include_zero_flows' => isset($this->request->post['include_zero_flows']) ? 1 : 0,
            'show_comparative' => isset($this->request->post['show_comparative']) ? 1 : 0,
            'comparative_date_start' => $this->request->post['comparative_date_start'] ?? '',
            'comparative_date_end' => $this->request->post['comparative_date_end'] ?? '',
            'currency' => $this->request->post['currency'] ?? $this->config->get('config_currency'),
            'cash_accounts' => $this->request->post['cash_accounts'] ?? array()
        );
    }

    /**
     * عرض النموذج (من advanced)
     */
    protected function getForm() {
        $data['breadcrumbs'] = array();
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('accounts/cash_flow', 'user_token=' . $this->session->data['user_token'], true)
        );

        $data['action'] = $this->url->link('accounts/cash_flow/generate', 'user_token=' . $this->session->data['user_token'], true);

        // URLs للدوال المتقدمة
        $data['analytics_url'] = $this->url->link('accounts/cash_flow/getAnalytics', 'user_token=' . $this->session->data['user_token'], true);
        $data['classify_url'] = $this->url->link('accounts/cash_flow/classifyActivity', 'user_token=' . $this->session->data['user_token'], true);
        $data['export_url'] = $this->url->link('accounts/cash_flow/export', 'user_token=' . $this->session->data['user_token'], true);

        // تحميل قوائم البيانات
        $this->load->model('accounts/chartaccount');
        $this->load->model('branch/branch');

        $data['cash_accounts'] = $this->model_accounts_chartaccount->getCashAccounts();
        $data['branches'] = $this->model_branch_branch->getBranches();

        // القيم الافتراضية
        $data['date_start'] = $this->request->post['date_start'] ?? date('Y-01-01');
        $data['date_end'] = $this->request->post['date_end'] ?? date('Y-m-d');
        $data['method'] = $this->request->post['method'] ?? 'indirect';
        $data['include_zero_flows'] = $this->request->post['include_zero_flows'] ?? false;

        $data['user_token'] = $this->session->data['user_token'];
        $data['heading_title'] = $this->language->get('heading_title');
        $data['text_no_results'] = $this->language->get('text_no_results');

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/cash_flow_form', $data));
    }

    /**
     * تحليل التدفقات النقدية المتقدم
     */
    public function getAnalytics() {
        $this->load->model('accounts/cash_flow');

        $json = array();

        if (isset($this->request->get['date_start']) && isset($this->request->get['date_end'])) {
            try {
                $filter_data = array(
                    'date_start' => $this->request->get['date_start'],
                    'date_end' => $this->request->get['date_end'],
                    'method' => $this->request->get['method'] ?? 'indirect'
                );

                $cash_flow_data = $this->model_accounts_cash_flow->getCashFlowData($filter_data);

                // حساب النسب المالية للسيولة
                $operating_cash_flow = $cash_flow_data['operating']['total'] ?? 0;
                $investing_cash_flow = $cash_flow_data['investing']['total'] ?? 0;
                $financing_cash_flow = $cash_flow_data['financing']['total'] ?? 0;
                $net_cash_flow = $operating_cash_flow + $investing_cash_flow + $financing_cash_flow;

                // حساب التدفق النقدي الحر
                $capital_expenditures = abs($cash_flow_data['investing']['capital_expenditures'] ?? 0);
                $free_cash_flow = $operating_cash_flow - $capital_expenditures;

                $analytics = array(
                    'operating_cash_flow' => $operating_cash_flow,
                    'investing_cash_flow' => $investing_cash_flow,
                    'financing_cash_flow' => $financing_cash_flow,
                    'net_cash_flow' => $net_cash_flow,
                    'free_cash_flow' => $free_cash_flow,
                    'cash_flow_ratios' => array(
                        'operating_ratio' => ($net_cash_flow != 0) ? round(($operating_cash_flow / $net_cash_flow) * 100, 2) : 0,
                        'investing_ratio' => ($net_cash_flow != 0) ? round(($investing_cash_flow / $net_cash_flow) * 100, 2) : 0,
                        'financing_ratio' => ($net_cash_flow != 0) ? round(($financing_cash_flow / $net_cash_flow) * 100, 2) : 0
                    )
                );

                $json['success'] = true;
                $json['analytics'] = $analytics;

                // تسجيل العملية
                $this->central_service->logActivity('cash_flow_analytics', 'accounts',
                    $this->language->get('log_advanced_cash_flow_analysis'), [
                    'user_id' => $this->user->getId(),
                    'period' => $filter_data['date_start'] . ' - ' . $filter_data['date_end'],
                    'method' => $filter_data['method']
                ]);

            } catch (Exception $e) {
                $json['error'] = $e->getMessage();
            }
        } else {
            $json['error'] = $this->language->get('error_date_required');
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * تصنيف الأنشطة التلقائي
     */
    public function classifyActivity() {
        $this->load->model('accounts/cash_flow');

        $json = array();

        if (isset($this->request->get['account_id'])) {
            try {
                $account_id = (int)$this->request->get['account_id'];
                $activity_type = $this->model_accounts_cash_flow->getActivityType($account_id);

                $json['success'] = true;
                $json['activity_type'] = $activity_type;
                $json['activity_name'] = $this->language->get('text_' . $activity_type . '_activities');

            } catch (Exception $e) {
                $json['error'] = $e->getMessage();
            }
        } else {
            $json['error'] = $this->language->get('error_account_required');
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * تصدير إلى Excel
     */
    private function exportToExcel($data, $filter_data) {
        $filename = 'cash_flow_' . $filter_data['date_start'] . '_to_' . $filter_data['date_end'] . '.xls';

        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        echo '<table border="1">';
        echo '<tr><th colspan="2">' . $this->language->get('heading_title') . '</th></tr>';
        echo '<tr><th>' . $this->language->get('text_activity') . '</th>';
        echo '<th>' . $this->language->get('text_amount') . '</th></tr>';

        // إضافة بيانات الأنشطة التشغيلية
        foreach ($data['operating'] as $activity) {
            echo '<tr>';
            echo '<td>' . $activity['description'] . '</td>';
            echo '<td>' . $activity['amount'] . '</td>';
            echo '</tr>';
        }

        echo '</table>';
        exit;
    }

    /**
     * تصدير إلى PDF
     */
    private function exportToPdf($data, $filter_data) {
        require_once(DIR_SYSTEM . 'library/tcpdf/tcpdf.php');

        $pdf = new TCPDF('P', 'mm', 'A4', true, 'UTF-8');
        $pdf->SetCreator('AYM ERP System');
        $pdf->SetAuthor($this->config->get('config_name'));
        $pdf->SetTitle($this->language->get('heading_title'));

        $pdf->AddPage();
        $pdf->SetFont('dejavusans', 'B', 16);
        $pdf->Cell(0, 10, $this->language->get('heading_title'), 0, 1, 'C');

        // إضافة البيانات
        $pdf->SetFont('dejavusans', '', 8);
        foreach ($data['operating'] as $activity) {
            $pdf->Cell(100, 6, $activity['description'], 1);
            $pdf->Cell(50, 6, $activity['amount'], 1);
            $pdf->Ln();
        }

        $pdf->Output('cash_flow_' . $filter_data['date_start'] . '.pdf', 'D');
        exit;
    }

    /**
     * تصدير إلى CSV
     */
    private function exportToCsv($data, $filter_data) {
        $filename = 'cash_flow_' . $filter_data['date_start'] . '_to_' . $filter_data['date_end'] . '.csv';

        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $output = fopen('php://output', 'w');
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

        fputcsv($output, array(
            $this->language->get('text_activity'),
            $this->language->get('text_amount')
        ));

        // إضافة بيانات الأنشطة التشغيلية
        foreach ($data['operating'] as $activity) {
            fputcsv($output, array(
                $activity['description'],
                $activity['amount']
            ));
        }

        fclose($output);
        exit;
    }

    /**
     * التحليل المتقدم للتدفق النقدي بالذكاء الاصطناعي
     */
    public function advancedAnalysis() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/cash_flow') ||
            !$this->user->hasKey('accounting_cash_flow_advanced')) {

            $this->central_service->logActivity('unauthorized_access', 'accounts',
                $this->language->get('log_unauthorized_access_advanced_cash_flow'), [
                'user_id' => $this->user->getId(),
                'ip_address' => $this->request->server['REMOTE_ADDR']
            ]);

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/cash_flow');
        $this->document->setTitle($this->language->get('heading_title'));
        $this->load->model('accounts/cash_flow');

        if ($this->request->server['REQUEST_METHOD'] == 'POST') {
            try {
                $date_start = $this->request->post['date_start'];
                $date_end = $this->request->post['date_end'];

                // تسجيل إنشاء التحليل المتقدم
                $this->central_service->logActivity('generate_advanced_cash_flow', 'accounts',
                    $this->language->get('log_generate_advanced_cash_flow_period') . ': ' . $date_start . ' ' . $this->language->get('text_to') . ' ' . $date_end, [
                    'user_id' => $this->user->getId(),
                    'date_start' => $date_start,
                    'date_end' => $date_end
                ]);

                // الحصول على التحليل المتقدم
                $advanced_analysis = $this->model_accounts_cash_flow->getAdvancedCashFlowAnalysis($date_start, $date_end);

                // إرسال إشعار للإدارة المالية
                $this->central_service->sendNotification(
                    'advanced_cash_flow_generated',
                    $this->language->get('text_advanced_cash_flow_analysis'),
                    $this->language->get('text_advanced_cash_flow_generated_notification') . ' ' . $date_start . ' ' . $this->language->get('text_to') . ' ' . $date_end,
                    [$this->config->get('config_cfo_id'), $this->config->get('config_chief_accountant_id')],
                    [
                        'date_start' => $date_start,
                        'date_end' => $date_end,
                        'net_cash_flow' => $advanced_analysis['basic_data']['net_cash_flow'],
                        'risk_count' => count($advanced_analysis['risk_assessment']),
                        'generated_by' => $this->user->getFirstName() . ' ' . $this->user->getLastName()
                    ]
                );

                $this->session->data['success'] = $this->language->get('text_success_generate');
                $this->session->data['advanced_analysis'] = $advanced_analysis;

                $this->response->redirect($this->url->link('accounts/cash_flow/advancedView', 'user_token=' . $this->session->data['user_token'], true));
            } catch (Exception $e) {
                $this->error['warning'] = $e->getMessage();
            }
        }

        // عرض نموذج التحليل المتقدم
        $data['breadcrumbs'] = array();
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('accounts/cash_flow', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_advanced_analysis'),
            'href' => $this->url->link('accounts/cash_flow/advancedAnalysis', 'user_token=' . $this->session->data['user_token'], true)
        );

        $data['action'] = $this->url->link('accounts/cash_flow/advancedAnalysis', 'user_token=' . $this->session->data['user_token'], true);
        $data['cancel'] = $this->url->link('accounts/cash_flow', 'user_token=' . $this->session->data['user_token'], true);

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/cash_flow_advanced_form', $data));
    }

    /**
     * عرض التحليل المتقدم
     */
    public function advancedView() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/cash_flow') ||
            !$this->user->hasKey('accounting_cash_flow_view')) {

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/cash_flow');

        if (!isset($this->session->data['advanced_analysis'])) {
            $this->session->data['error'] = $this->language->get('error_no_data');
            $this->response->redirect($this->url->link('accounts/cash_flow', 'user_token=' . $this->session->data['user_token'], true));
            return;
        }

        // تسجيل عرض التحليل
        $this->central_service->logActivity('view_advanced_cash_flow', 'accounts',
            $this->language->get('log_view_advanced_cash_flow'), [
            'user_id' => $this->user->getId()
        ]);

        $data = $this->session->data['advanced_analysis'];
        $data['cash_flow_data'] = $data['basic_data'];
        $data['trend_analysis'] = $data['trend_analysis'];
        $data['seasonal_patterns'] = $data['seasonal_patterns'];
        $data['liquidity_ratios'] = $data['liquidity_ratios'];
        $data['cash_conversion_cycle'] = $data['cash_conversion_cycle'];
        $data['forecasting'] = $data['forecasting'];
        $data['risk_assessment'] = $data['risk_assessment'];
        $data['recommendations'] = $data['recommendations'];

        $data['breadcrumbs'] = array();
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('accounts/cash_flow', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_advanced_view'),
            'href' => $this->url->link('accounts/cash_flow/advancedView', 'user_token=' . $this->session->data['user_token'], true)
        );

        $data['export_excel'] = $this->url->link('accounts/cash_flow/export', 'user_token=' . $this->session->data['user_token'] . '&format=excel', true);
        $data['export_pdf'] = $this->url->link('accounts/cash_flow/export', 'user_token=' . $this->session->data['user_token'] . '&format=pdf', true);
        $data['export_csv'] = $this->url->link('accounts/cash_flow/export', 'user_token=' . $this->session->data['user_token'] . '&format=csv', true);
        $data['print'] = $this->url->link('accounts/cash_flow/advancedPrint', 'user_token=' . $this->session->data['user_token'], true);
        $data['back'] = $this->url->link('accounts/cash_flow', 'user_token=' . $this->session->data['user_token'], true);

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/cash_flow_advanced_view', $data));
    }

    /**
     * طباعة التحليل المتقدم
     */
    public function advancedPrint() {
        $this->load->language('accounts/cash_flow');

        if (!isset($this->session->data['advanced_analysis'])) {
            $this->response->redirect($this->url->link('accounts/cash_flow', 'user_token=' . $this->session->data['user_token'], true));
            return;
        }

        $data = $this->session->data['advanced_analysis'];
        $data['cash_flow_data'] = $data['basic_data'];
        $data['trend_analysis'] = $data['trend_analysis'];
        $data['liquidity_ratios'] = $data['liquidity_ratios'];
        $data['cash_conversion_cycle'] = $data['cash_conversion_cycle'];
        $data['forecasting'] = $data['forecasting'];
        $data['risk_assessment'] = $data['risk_assessment'];
        $data['recommendations'] = $data['recommendations'];

        $data['company_name'] = $this->config->get('config_name');
        $data['generated_date'] = date('Y-m-d H:i:s');
        $data['generated_by'] = $this->user->getFirstName() . ' ' . $this->user->getLastName();

        $this->response->setOutput($this->load->view('accounts/cash_flow_advanced_print', $data));
    }

    /**
     * التحليل الشامل للتدفقات النقدية مع الطريقتين والنسب المالية
     */
    public function comprehensiveAnalysis() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/cash_flow') ||
            !$this->user->hasKey('accounting_cash_flow_analysis')) {

            $this->central_service->logActivity('unauthorized_access', 'accounts',
                $this->language->get('log_unauthorized_access_comprehensive_cash_flow'), [
                'user_id' => $this->user->getId(),
                'ip_address' => $this->request->server['REMOTE_ADDR']
            ]);

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/cash_flow');
        $this->document->setTitle($this->language->get('heading_title') . ' - ' . $this->language->get('text_comprehensive_analysis'));
        $this->load->model('accounts/cash_flow');

        // إضافة CSS و JavaScript المتقدم للتحليل
        $this->document->addStyle('view/stylesheet/accounts/cash_flow_comprehensive.css');
        $this->document->addScript('view/javascript/accounts/cash_flow_comprehensive.js');
        $this->document->addScript('view/javascript/jquery/chart.min.js');

        if ($this->request->server['REQUEST_METHOD'] == 'POST') {
            try {
                $date_start = $this->request->post['date_start'];
                $date_end = $this->request->post['date_end'];
                $branch_id = $this->request->post['branch_id'] ?? null;

                // تسجيل إنشاء التحليل الشامل
                $this->central_service->logActivity('generate_comprehensive_cash_flow_analysis', 'accounts',
                    $this->language->get('log_generate_comprehensive_cash_flow_period') . ': ' . $date_start . ' ' . $this->language->get('text_to') . ' ' . $date_end, [
                    'user_id' => $this->user->getId(),
                    'date_start' => $date_start,
                    'date_end' => $date_end,
                    'branch_id' => $branch_id
                ]);

                // الحصول على التحليل الشامل
                $comprehensive_analysis = $this->model_accounts_cash_flow->getComprehensiveCashFlowAnalysis(
                    $date_start, $date_end, $branch_id
                );

                // إرسال إشعار للإدارة المالية
                $this->central_service->sendNotification(
                    'comprehensive_cash_flow_analysis_generated',
                    $this->language->get('text_comprehensive_cash_flow_analysis'),
                    $this->language->get('text_comprehensive_cash_flow_generated_notification') . ' ' . $date_start . ' ' . $this->language->get('text_to') . ' ' . $date_end,
                    [$this->config->get('config_cfo_id'), $this->config->get('config_chief_accountant_id')],
                    [
                        'date_start' => $date_start,
                        'date_end' => $date_end,
                        'operating_cash_flow' => $comprehensive_analysis['direct_method']['operating_total'],
                        'net_change_in_cash' => $comprehensive_analysis['direct_method']['total_cash_flow'],
                        'cash_quality_score' => $comprehensive_analysis['cash_quality_analysis']['quality_score'],
                        'generated_by' => $this->user->getFirstName() . ' ' . $this->user->getLastName()
                    ]
                );

                $this->session->data['success'] = $this->language->get('text_success_analysis');
                $this->session->data['comprehensive_cash_flow_analysis'] = $comprehensive_analysis;

                $this->response->redirect($this->url->link('accounts/cash_flow/analysisView', 'user_token=' . $this->session->data['user_token'], true));
            } catch (Exception $e) {
                $this->error['warning'] = $e->getMessage();
            }
        }

        // عرض نموذج التحليل الشامل
        $data['breadcrumbs'] = array();
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('accounts/cash_flow', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_comprehensive_analysis'),
            'href' => $this->url->link('accounts/cash_flow/comprehensiveAnalysis', 'user_token=' . $this->session->data['user_token'], true)
        );

        $data['action'] = $this->url->link('accounts/cash_flow/comprehensiveAnalysis', 'user_token=' . $this->session->data['user_token'], true);
        $data['cancel'] = $this->url->link('accounts/cash_flow', 'user_token=' . $this->session->data['user_token'], true);

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/cash_flow_comprehensive_analysis_form', $data));
    }

    /**
     * عرض التحليل الشامل
     */
    public function analysisView() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/cash_flow') ||
            !$this->user->hasKey('accounting_cash_flow_view')) {

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/cash_flow');

        if (!isset($this->session->data['comprehensive_cash_flow_analysis'])) {
            $this->session->data['error'] = $this->language->get('error_no_analysis_data');
            $this->response->redirect($this->url->link('accounts/cash_flow', 'user_token=' . $this->session->data['user_token'], true));
            return;
        }

        // تسجيل عرض التحليل
        $this->central_service->logActivity('view_comprehensive_cash_flow_analysis', 'accounts',
            $this->language->get('log_view_comprehensive_cash_flow'), [
            'user_id' => $this->user->getId()
        ]);

        $data = $this->session->data['comprehensive_cash_flow_analysis'];

        $data['breadcrumbs'] = array();
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('accounts/cash_flow', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_analysis_view'),
            'href' => $this->url->link('accounts/cash_flow/analysisView', 'user_token=' . $this->session->data['user_token'], true)
        );

        $data['export_excel'] = $this->url->link('accounts/cash_flow/export', 'user_token=' . $this->session->data['user_token'] . '&format=excel', true);
        $data['export_pdf'] = $this->url->link('accounts/cash_flow/export', 'user_token=' . $this->session->data['user_token'] . '&format=pdf', true);
        $data['print'] = $this->url->link('accounts/cash_flow/analysisPrint', 'user_token=' . $this->session->data['user_token'], true);
        $data['back'] = $this->url->link('accounts/cash_flow', 'user_token=' . $this->session->data['user_token'], true);

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/cash_flow_comprehensive_analysis_view', $data));
    }

    /**
     * طباعة التحليل الشامل
     */
    public function analysisPrint() {
        $this->load->language('accounts/cash_flow');

        if (!isset($this->session->data['comprehensive_cash_flow_analysis'])) {
            $this->response->redirect($this->url->link('accounts/cash_flow', 'user_token=' . $this->session->data['user_token'], true));
            return;
        }

        $data = $this->session->data['comprehensive_cash_flow_analysis'];
        $data['company_name'] = $this->config->get('config_name');
        $data['generated_date'] = date('Y-m-d H:i:s');
        $data['generated_by'] = $this->user->getFirstName() . ' ' . $this->user->getLastName();

        $this->response->setOutput($this->load->view('accounts/cash_flow_comprehensive_analysis_print', $data));
    }

    /**
     * دالة تنظيف المخرجات (CONSTITUTIONAL REQUIREMENT)
     * Sanitize all output data to prevent XSS attacks
     */
    private function sanitizeOutputData($data) {
        if (is_array($data)) {
            foreach ($data as $key => $value) {
                $data[$key] = $this->sanitizeOutputData($value);
            }
        } elseif (is_string($data)) {
            $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
        }
        return $data;
    }
}
