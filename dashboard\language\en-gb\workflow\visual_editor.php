<?php
/**
 * English Language File - Visual Workflow Editor
 *
 * @package    AYM ERP
 * <AUTHOR> Development Team
 * @copyright  2025 AYM ERP Systems
 * @license    Commercial License
 * @version    1.0.0
 * @since      2025-07-15
 */

// Main heading
$_['heading_title'] = 'Visual Workflow Editor';

// General texts
$_['text_success'] = 'Success: You have modified workflows!';
$_['text_list'] = 'Workflow List';
$_['text_add'] = 'Add Workflow';
$_['text_edit'] = 'Edit Workflow';
$_['text_view'] = 'View Workflow';
$_['text_delete'] = 'Delete';
$_['text_confirm'] = 'Are you sure?';
$_['text_no_results'] = 'No results!';
$_['text_loading'] = 'Loading...';

// Visual editor
$_['text_visual_editor'] = 'Visual Editor';
$_['text_canvas'] = 'Canvas';
$_['text_toolbox'] = 'Toolbox';
$_['text_properties'] = 'Properties';
$_['text_preview'] = 'Preview';
$_['text_save'] = 'Save';
$_['text_publish'] = 'Publish';
$_['text_test'] = 'Test';

// Node types
$_['text_nodes'] = 'Nodes';
$_['text_start_nodes'] = 'Start Nodes';
$_['text_process_nodes'] = 'Process Nodes';
$_['text_control_nodes'] = 'Control Nodes';
$_['text_end_nodes'] = 'End Nodes';

// Start nodes
$_['text_manual_start'] = 'Manual Start';
$_['text_timer_start'] = 'Timer Start';
$_['text_event_start'] = 'Event Start';
$_['text_data_start'] = 'Data Start';
$_['text_webhook_start'] = 'Webhook Start';

// Process nodes
$_['text_approval_node'] = 'Approval';
$_['text_condition_node'] = 'Condition';
$_['text_update_node'] = 'Update Data';
$_['text_notification_node'] = 'Notification';
$_['text_task_node'] = 'Task';
$_['text_wait_node'] = 'Wait';
$_['text_email_node'] = 'Email';
$_['text_sms_node'] = 'SMS';

// Control nodes
$_['text_if_node'] = 'If';
$_['text_loop_node'] = 'Loop';
$_['text_merge_node'] = 'Merge';
$_['text_split_node'] = 'Split';
$_['text_switch_node'] = 'Switch';
$_['text_parallel_node'] = 'Parallel';

// End nodes
$_['text_success_end'] = 'Success End';
$_['text_cancel_end'] = 'Cancel';
$_['text_error_end'] = 'Error';
$_['text_redirect_end'] = 'Redirect';

// Legacy compatibility
$_['text_default'] = 'Default';
$_['text_workflow'] = 'Workflows';
$_['text_none'] = '-- None --';
$_['text_yes'] = 'Yes';
$_['text_no'] = 'No';
$_['text_active'] = 'Active';
$_['text_inactive'] = 'Inactive';
$_['text_archived'] = 'Archived';
$_['text_document_approval'] = 'Document Approval';
$_['text_purchase_approval'] = 'Purchase Approval';
$_['text_leave_request'] = 'Leave Request';
$_['text_expense_claim'] = 'Expense Claim';
$_['text_payment_approval'] = 'Payment Approval';
$_['text_other'] = 'Other';
$_['text_trigger'] = 'Trigger';
$_['text_approval'] = 'Approval';
$_['text_condition'] = 'Condition';
$_['text_email'] = 'Email';
$_['text_notification'] = 'Notification';
$_['text_task'] = 'Task';
$_['text_delay'] = 'Delay';
$_['text_webhook'] = 'Webhook';
$_['text_function'] = 'Function';
$_['text_node'] = 'Node';
$_['text_click_to_configure'] = 'Click to configure this node';
$_['text_configure'] = 'Configure';
$_['text_configure_node'] = 'Configure Node';
$_['text_approver'] = 'Approver';
$_['text_recipient'] = 'Recipient';

// Entry
$_['entry_name']                     = 'Workflow Name';
$_['entry_description']              = 'Description';
$_['entry_workflow_type']            = 'Workflow Type';
$_['entry_status']                   = 'Status';
$_['entry_department']               = 'Department';
$_['entry_escalation_enabled']       = 'Enable Escalation';
$_['entry_escalation_after_days']    = 'Escalate After (Days)';
$_['entry_notify_creator']           = 'Notify Creator';

// Tab
$_['tab_general']                    = 'General';
$_['tab_visual_editor']              = 'Visual Editor';

// Button
$_['button_save']                    = 'Save';
$_['button_cancel']                  = 'Cancel';

// Columns
$_['column_name'] = 'Name';
$_['column_category'] = 'Category';
$_['column_status'] = 'Status';
$_['column_created'] = 'Created';
$_['column_modified'] = 'Modified';
$_['column_action'] = 'Action';
$_['column_description'] = 'Description';
$_['column_type'] = 'Type';

// Additional buttons
$_['button_add'] = 'Add Workflow';
$_['button_edit'] = 'Edit';
$_['button_delete'] = 'Delete';
$_['button_view'] = 'View';
$_['button_copy'] = 'Copy';
$_['button_export'] = 'Export';
$_['button_import'] = 'Import';
$_['button_test'] = 'Test';
$_['button_publish'] = 'Publish';
$_['button_unpublish'] = 'Unpublish';

// Editor tools
$_['text_zoom_in'] = 'Zoom In';
$_['text_zoom_out'] = 'Zoom Out';
$_['text_zoom_fit'] = 'Fit to Screen';
$_['text_grid'] = 'Grid';
$_['text_snap'] = 'Snap';
$_['text_undo'] = 'Undo';
$_['text_redo'] = 'Redo';
$_['text_select_all'] = 'Select All';
$_['text_delete_selected'] = 'Delete Selected';

// Templates
$_['text_templates'] = 'Templates';
$_['text_workflow_templates'] = 'Workflow Templates';
$_['text_use_template'] = 'Use Template';
$_['text_save_as_template'] = 'Save as Template';
$_['text_template_name'] = 'Template Name';
$_['text_template_description'] = 'Template Description';

// Testing
$_['text_test_workflow'] = 'Test Workflow';
$_['text_run_workflow'] = 'Run Workflow';
$_['text_test_data'] = 'Test Data';
$_['text_execution_log'] = 'Execution Log';
$_['text_debug_mode'] = 'Debug Mode';

// Variables
$_['text_variables'] = 'Variables';
$_['text_data_mapping'] = 'Data Mapping';
$_['text_input_data'] = 'Input Data';
$_['text_output_data'] = 'Output Data';

// Validation
$_['text_validation'] = 'Validation';
$_['text_validate_workflow'] = 'Validate Workflow';
$_['text_validation_errors'] = 'Validation Errors';
$_['text_validation_warnings'] = 'Validation Warnings';

// Error messages
$_['error_permission'] = 'Warning: You do not have permission to modify workflows!';
$_['error_name'] = 'Workflow Name must be between 3 and 255 characters!';
$_['error_invalid_node'] = 'Invalid node information!';
$_['error_description'] = 'Description is required!';
$_['error_workflow_type'] = 'Please select a workflow type!';
$_['error_department'] = 'Please select a department!';
$_['error_no_start_node'] = 'At least one start node must be added!';
$_['error_no_end_node'] = 'At least one end node must be added!';
$_['error_validation_failed'] = 'Workflow validation failed!';

// Confirmation messages
$_['text_confirm_delete'] = 'Are you sure you want to delete this workflow?';
$_['text_confirm_publish'] = 'Are you sure you want to publish this workflow?';
$_['text_unsaved_changes'] = 'There are unsaved changes. Do you want to continue?';

// Help and tips
$_['help_visual_editor'] = 'Drag nodes from the toolbox and drop them on the canvas';
$_['help_connections'] = 'Connect nodes by dragging from output point to input point';
$_['help_properties'] = 'Click on a node to edit its properties';

// Alerts
$_['alert_workflow_saved'] = 'Workflow saved successfully';
$_['alert_workflow_published'] = 'Workflow published successfully';
$_['alert_validation_complete'] = 'Workflow validation complete';

// Advanced visual editor descriptions
$_['text_manual_start_desc'] = 'Start workflow manually';
$_['text_scheduled_start_desc'] = 'Run workflow on schedule';
$_['text_http_trigger_desc'] = 'Trigger on HTTP request';
$_['text_database_change_desc'] = 'Trigger on data change';
$_['text_create_order_desc'] = 'Create new sales order';
$_['text_send_email_action_desc'] = 'Send email message';
$_['text_order_approval_workflow'] = 'Order approval workflow';
?>