-- إضافة جداول توحيد القوائم المالية
CREATE TABLE `cod_consolidation_subsidiaries` (
  `subsidiary_id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `subsidiary_type` enum('subsidiary','associate','joint_venture') NOT NULL DEFAULT 'subsidiary',
  `functional_currency` varchar(3) NOT NULL DEFAULT 'EGP',
  `ownership_percentage` decimal(5,2) NOT NULL DEFAULT '0.00',
  `acquisition_date` date DEFAULT NULL,
  `acquisition_cost` decimal(15,4) NOT NULL DEFAULT '0.0000',
  `status` tinyint(1) NOT NULL DEFAULT '1',
  `date_added` datetime NOT NULL,
  `date_modified` datetime NOT NULL,
  PRIMARY KEY (`subsidiary_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `cod_consolidation_reports` (
  `consolidation_id` int(11) NOT NULL AUTO_INCREMENT,
  `reference` varchar(64) NOT NULL,
  `period_start` date NOT NULL,
  `period_end` date NOT NULL,
  `consolidation_method` enum('full','proportional','equity') NOT NULL DEFAULT 'full',
  `reporting_currency` varchar(3) NOT NULL DEFAULT 'EGP',
  `include_adjustments` tinyint(1) NOT NULL DEFAULT '1',
  `status` enum('processing','completed','failed') NOT NULL DEFAULT 'processing',
  `created_by` int(11) NOT NULL,
  `date_created` datetime NOT NULL,
  `date_completed` datetime DEFAULT NULL,
  PRIMARY KEY (`consolidation_id`),
  UNIQUE KEY `reference` (`reference`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `cod_consolidation_subsidiaries_included` (
  `consolidation_id` int(11) NOT NULL,
  `subsidiary_id` int(11) NOT NULL,
  `date_added` datetime NOT NULL,
  PRIMARY KEY (`consolidation_id`,`subsidiary_id`),
  KEY `fk_consolidation_subsidiaries_included_subsidiary` (`subsidiary_id`),
  CONSTRAINT `fk_consolidation_subsidiaries_included_consolidation` FOREIGN KEY (`consolidation_id`) REFERENCES `cod_consolidation_reports` (`consolidation_id`) ON DELETE CASCADE,
  CONSTRAINT `fk_consolidation_subsidiaries_included_subsidiary` FOREIGN KEY (`subsidiary_id`) REFERENCES `cod_consolidation_subsidiaries` (`subsidiary_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `cod_consolidation_results` (
  `result_id` int(11) NOT NULL AUTO_INCREMENT,
  `consolidation_id` int(11) NOT NULL,
  `statement_type` enum('balance_sheet','income_statement','cash_flow','equity_changes') NOT NULL,
  `account_code` varchar(20) NOT NULL,
  `account_name` varchar(255) NOT NULL,
  `amount` decimal(15,4) NOT NULL DEFAULT '0.0000',
  `date_added` datetime NOT NULL,
  PRIMARY KEY (`result_id`),
  KEY `fk_consolidation_results_consolidation` (`consolidation_id`),
  KEY `idx_consolidation_results_statement` (`statement_type`),
  CONSTRAINT `fk_consolidation_results_consolidation` FOREIGN KEY (`consolidation_id`) REFERENCES `cod_consolidation_reports` (`consolidation_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `cod_consolidation_adjustments` (
  `adjustment_id` int(11) NOT NULL AUTO_INCREMENT,
  `consolidation_id` int(11) NOT NULL,
  `adjustment_type` enum('intercompany_elimination','fair_value_adjustment','currency_translation','goodwill_calculation') NOT NULL,
  `account_code` varchar(20) NOT NULL,
  `debit_amount` decimal(15,4) NOT NULL DEFAULT '0.0000',
  `credit_amount` decimal(15,4) NOT NULL DEFAULT '0.0000',
  `description` text NOT NULL,
  `notes` text,
  `created_by` int(11) NOT NULL,
  `date_added` datetime NOT NULL,
  PRIMARY KEY (`adjustment_id`),
  KEY `fk_consolidation_adjustments_consolidation` (`consolidation_id`),
  CONSTRAINT `fk_consolidation_adjustments_consolidation` FOREIGN KEY (`consolidation_id`) REFERENCES `cod_consolidation_reports` (`consolidation_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `cod_consolidation_settings` (
  `setting_id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(128) NOT NULL,
  `setting_value` text NOT NULL,
  `status` tinyint(1) NOT NULL DEFAULT '1',
  `sort_order` int(3) NOT NULL DEFAULT '0',
  PRIMARY KEY (`setting_id`),
  UNIQUE KEY `setting_key` (`setting_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `cod_intercompany_transactions` (
  `transaction_id` int(11) NOT NULL AUTO_INCREMENT,
  `company_id` int(11) NOT NULL,
  `related_company_id` int(11) NOT NULL,
  `transaction_type` enum('sale','purchase','loan','dividend','management_fee') NOT NULL,
  `transaction_date` date NOT NULL,
  `amount` decimal(15,4) NOT NULL DEFAULT '0.0000',
  `currency` varchar(3) NOT NULL DEFAULT 'EGP',
  `description` text NOT NULL,
  `status` enum('pending','confirmed','eliminated') NOT NULL DEFAULT 'pending',
  `created_by` int(11) NOT NULL,
  `date_added` datetime NOT NULL,
  `date_modified` datetime NOT NULL,
  PRIMARY KEY (`transaction_id`),
  KEY `idx_intercompany_company` (`company_id`),
  KEY `idx_intercompany_related_company` (`related_company_id`),
  KEY `idx_intercompany_date` (`transaction_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إضافة صلاحيات الشاشات المحاسبية الجديدة السبع
INSERT INTO `cod_user_group_permission` (`user_group_id`, `permission`, `type`) VALUES
-- صلاحيات توحيد القوائم المالية
(1, 'accounting_consolidation_view', 'access'),
(1, 'accounting_consolidation_generate', 'access'),
(1, 'accounting_consolidation_adjust', 'access'),
(1, 'accounting_consolidation_export', 'access'),

-- صلاحيات إعادة تقييم العملات
(1, 'accounting_multi_currency_revaluation_view', 'access'),
(1, 'accounting_multi_currency_revaluation_process', 'access'),
(1, 'accounting_multi_currency_revaluation_approve', 'access'),

-- صلاحيات المعاملات بين الشركات
(1, 'accounting_intercompany_transactions_view', 'access'),
(1, 'accounting_intercompany_transactions_create', 'access'),
(1, 'accounting_intercompany_transactions_eliminate', 'access'),

-- صلاحيات الموازنات المتقدمة
(1, 'accounting_advanced_budgeting_view', 'access'),
(1, 'accounting_advanced_budgeting_create', 'access'),
(1, 'accounting_advanced_budgeting_approve', 'access'),

-- صلاحيات التنبؤ بالتدفق النقدي
(1, 'accounting_cash_flow_forecasting_view', 'access'),
(1, 'accounting_cash_flow_forecasting_generate', 'access'),
(1, 'accounting_cash_flow_forecasting_analyze', 'access'),

-- صلاحيات لوحة التحليلات المالية
(1, 'accounting_financial_analytics_dashboard_view', 'access'),
(1, 'accounting_financial_analytics_dashboard_configure', 'access'),
(1, 'accounting_financial_analytics_dashboard_export', 'access'),

-- صلاحيات مسار التدقيق المتقدم
(1, 'accounting_audit_trail_advanced_view', 'access'),
(1, 'accounting_audit_trail_advanced_configure', 'access'),
(1, 'accounting_audit_trail_advanced_export', 'access');

-- إضافة جداول إعادة تقييم العملات
CREATE TABLE `cod_currency_revaluation` (
  `revaluation_id` int(11) NOT NULL AUTO_INCREMENT,
  `reference` varchar(64) NOT NULL,
  `revaluation_date` date NOT NULL,
  `status` enum('pending','approved','posted','reversed') NOT NULL DEFAULT 'pending',
  `auto_post` tinyint(1) NOT NULL DEFAULT '0',
  `total_adjustment` decimal(15,4) NOT NULL DEFAULT '0.0000',
  `journal_id` int(11) DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `approved_by` int(11) DEFAULT NULL,
  `reversed_by` int(11) DEFAULT NULL,
  `approval_notes` text,
  `reverse_reason` text,
  `date_created` datetime NOT NULL,
  `date_approved` datetime DEFAULT NULL,
  `date_reversed` datetime DEFAULT NULL,
  PRIMARY KEY (`revaluation_id`),
  UNIQUE KEY `reference` (`reference`),
  KEY `idx_revaluation_date` (`revaluation_date`),
  KEY `idx_revaluation_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `cod_currency_revaluation_details` (
  `detail_id` int(11) NOT NULL AUTO_INCREMENT,
  `revaluation_id` int(11) NOT NULL,
  `account_id` int(11) NOT NULL,
  `currency_code` varchar(3) NOT NULL,
  `foreign_balance` decimal(15,4) NOT NULL DEFAULT '0.0000',
  `old_rate` decimal(15,8) NOT NULL DEFAULT '1.********',
  `new_rate` decimal(15,8) NOT NULL DEFAULT '1.********',
  `old_local_balance` decimal(15,4) NOT NULL DEFAULT '0.0000',
  `new_local_balance` decimal(15,4) NOT NULL DEFAULT '0.0000',
  `adjustment_amount` decimal(15,4) NOT NULL DEFAULT '0.0000',
  `date_added` datetime NOT NULL,
  PRIMARY KEY (`detail_id`),
  KEY `fk_currency_revaluation_details_revaluation` (`revaluation_id`),
  KEY `fk_currency_revaluation_details_account` (`account_id`),
  KEY `idx_currency_revaluation_details_currency` (`currency_code`),
  CONSTRAINT `fk_currency_revaluation_details_revaluation` FOREIGN KEY (`revaluation_id`) REFERENCES `cod_currency_revaluation` (`revaluation_id`) ON DELETE CASCADE,
  CONSTRAINT `fk_currency_revaluation_details_account` FOREIGN KEY (`account_id`) REFERENCES `cod_accounts` (`account_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `cod_currency_revaluation_settings` (
  `setting_id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(128) NOT NULL,
  `setting_value` text NOT NULL,
  `status` tinyint(1) NOT NULL DEFAULT '1',
  `sort_order` int(3) NOT NULL DEFAULT '0',
  PRIMARY KEY (`setting_id`),
  UNIQUE KEY `setting_key` (`setting_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إضافة جداول الموازنات المتقدمة
CREATE TABLE `cod_advanced_budget` (
  `budget_id` int(11) NOT NULL AUTO_INCREMENT,
  `budget_name` varchar(255) NOT NULL,
  `budget_type` enum('annual','quarterly','monthly','project','department') NOT NULL DEFAULT 'annual',
  `fiscal_year` int(4) NOT NULL,
  `period_start` date NOT NULL,
  `period_end` date NOT NULL,
  `currency` varchar(3) NOT NULL DEFAULT 'EGP',
  `status` enum('draft','submitted','approved','active','closed') NOT NULL DEFAULT 'draft',
  `approval_workflow_id` int(11) DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `approved_by` int(11) DEFAULT NULL,
  `date_created` datetime NOT NULL,
  `date_approved` datetime DEFAULT NULL,
  `date_modified` datetime NOT NULL,
  PRIMARY KEY (`budget_id`),
  KEY `idx_budget_fiscal_year` (`fiscal_year`),
  KEY `idx_budget_status` (`status`),
  KEY `idx_budget_type` (`budget_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `cod_advanced_budget_lines` (
  `budget_line_id` int(11) NOT NULL AUTO_INCREMENT,
  `budget_id` int(11) NOT NULL,
  `account_id` int(11) NOT NULL,
  `department_id` int(11) DEFAULT NULL,
  `cost_center_id` int(11) DEFAULT NULL,
  `budget_amount` decimal(15,4) NOT NULL DEFAULT '0.0000',
  `q1_amount` decimal(15,4) NOT NULL DEFAULT '0.0000',
  `q2_amount` decimal(15,4) NOT NULL DEFAULT '0.0000',
  `q3_amount` decimal(15,4) NOT NULL DEFAULT '0.0000',
  `q4_amount` decimal(15,4) NOT NULL DEFAULT '0.0000',
  `notes` text,
  `date_added` datetime NOT NULL,
  `date_modified` datetime NOT NULL,
  PRIMARY KEY (`budget_line_id`),
  KEY `fk_budget_lines_budget` (`budget_id`),
  KEY `fk_budget_lines_account` (`account_id`),
  KEY `idx_budget_lines_department` (`department_id`),
  CONSTRAINT `fk_budget_lines_budget` FOREIGN KEY (`budget_id`) REFERENCES `cod_advanced_budget` (`budget_id`) ON DELETE CASCADE,
  CONSTRAINT `fk_budget_lines_account` FOREIGN KEY (`account_id`) REFERENCES `cod_accounts` (`account_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إضافة جداول إضافية للمعاملات بين الشركات
CREATE TABLE `cod_companies` (
  `company_id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `code` varchar(20) NOT NULL,
  `legal_name` varchar(255) NOT NULL,
  `tax_number` varchar(50) DEFAULT NULL,
  `registration_number` varchar(50) DEFAULT NULL,
  `address` text,
  `phone` varchar(32) DEFAULT NULL,
  `email` varchar(96) DEFAULT NULL,
  `website` varchar(255) DEFAULT NULL,
  `currency` varchar(3) NOT NULL DEFAULT 'EGP',
  `status` tinyint(1) NOT NULL DEFAULT '1',
  `date_added` datetime NOT NULL,
  `date_modified` datetime NOT NULL,
  PRIMARY KEY (`company_id`),
  UNIQUE KEY `code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `cod_intercompany_eliminations` (
  `elimination_id` int(11) NOT NULL AUTO_INCREMENT,
  `reference` varchar(64) NOT NULL,
  `elimination_date` date NOT NULL,
  `elimination_method` enum('full','partial','proportional') NOT NULL DEFAULT 'full',
  `total_eliminated` decimal(15,4) NOT NULL DEFAULT '0.0000',
  `notes` text,
  `status` enum('pending','completed','reversed') NOT NULL DEFAULT 'pending',
  `created_by` int(11) NOT NULL,
  `date_created` datetime NOT NULL,
  PRIMARY KEY (`elimination_id`),
  UNIQUE KEY `reference` (`reference`),
  KEY `idx_elimination_date` (`elimination_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `cod_intercompany_elimination_details` (
  `detail_id` int(11) NOT NULL AUTO_INCREMENT,
  `elimination_id` int(11) NOT NULL,
  `transaction_id` int(11) NOT NULL,
  `eliminated_amount` decimal(15,4) NOT NULL DEFAULT '0.0000',
  `date_added` datetime NOT NULL,
  PRIMARY KEY (`detail_id`),
  KEY `fk_elimination_details_elimination` (`elimination_id`),
  KEY `fk_elimination_details_transaction` (`transaction_id`),
  CONSTRAINT `fk_elimination_details_elimination` FOREIGN KEY (`elimination_id`) REFERENCES `cod_intercompany_eliminations` (`elimination_id`) ON DELETE CASCADE,
  CONSTRAINT `fk_elimination_details_transaction` FOREIGN KEY (`transaction_id`) REFERENCES `cod_intercompany_transactions` (`transaction_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `cod_intercompany_matches` (
  `match_id` int(11) NOT NULL AUTO_INCREMENT,
  `transaction_id_1` int(11) NOT NULL,
  `transaction_id_2` int(11) NOT NULL,
  `match_type` enum('exact','tolerance','manual') NOT NULL DEFAULT 'exact',
  `tolerance_amount` decimal(15,4) NOT NULL DEFAULT '0.0000',
  `notes` text,
  `matched_by` int(11) NOT NULL,
  `date_matched` datetime NOT NULL,
  PRIMARY KEY (`match_id`),
  KEY `fk_matches_transaction1` (`transaction_id_1`),
  KEY `fk_matches_transaction2` (`transaction_id_2`),
  CONSTRAINT `fk_matches_transaction1` FOREIGN KEY (`transaction_id_1`) REFERENCES `cod_intercompany_transactions` (`transaction_id`) ON DELETE CASCADE,
  CONSTRAINT `fk_matches_transaction2` FOREIGN KEY (`transaction_id_2`) REFERENCES `cod_intercompany_transactions` (`transaction_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إضافة جداول التنبؤ بالتدفق النقدي
CREATE TABLE `cod_cash_flow_forecast` (
  `forecast_id` int(11) NOT NULL AUTO_INCREMENT,
  `forecast_name` varchar(255) NOT NULL,
  `forecast_type` enum('weekly','monthly','quarterly','annual') NOT NULL DEFAULT 'monthly',
  `period_start` date NOT NULL,
  `period_end` date NOT NULL,
  `currency` varchar(3) NOT NULL DEFAULT 'EGP',
  `status` enum('draft','active','completed','archived') NOT NULL DEFAULT 'draft',
  `created_by` int(11) NOT NULL,
  `date_created` datetime NOT NULL,
  `date_modified` datetime NOT NULL,
  PRIMARY KEY (`forecast_id`),
  KEY `idx_forecast_period` (`period_start`, `period_end`),
  KEY `idx_forecast_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `cod_cash_flow_forecast_lines` (
  `forecast_line_id` int(11) NOT NULL AUTO_INCREMENT,
  `forecast_id` int(11) NOT NULL,
  `account_id` int(11) NOT NULL,
  `forecast_date` date NOT NULL,
  `forecast_type` enum('inflow','outflow') NOT NULL,
  `forecast_amount` decimal(15,4) NOT NULL DEFAULT '0.0000',
  `actual_amount` decimal(15,4) NOT NULL DEFAULT '0.0000',
  `variance` decimal(15,4) NOT NULL DEFAULT '0.0000',
  `confidence_level` enum('high','medium','low') NOT NULL DEFAULT 'medium',
  `notes` text,
  `date_added` datetime NOT NULL,
  PRIMARY KEY (`forecast_line_id`),
  KEY `fk_forecast_lines_forecast` (`forecast_id`),
  KEY `fk_forecast_lines_account` (`account_id`),
  KEY `idx_forecast_lines_date` (`forecast_date`),
  CONSTRAINT `fk_forecast_lines_forecast` FOREIGN KEY (`forecast_id`) REFERENCES `cod_cash_flow_forecast` (`forecast_id`) ON DELETE CASCADE,
  CONSTRAINT `fk_forecast_lines_account` FOREIGN KEY (`account_id`) REFERENCES `cod_accounts` (`account_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إضافة جداول إضافية للموازنات المتقدمة
CREATE TABLE `cod_budget_approval_history` (
  `history_id` int(11) NOT NULL AUTO_INCREMENT,
  `budget_id` int(11) NOT NULL,
  `action` enum('submitted','approved','rejected','changes_requested','activated') NOT NULL,
  `notes` text,
  `user_id` int(11) NOT NULL,
  `date_action` datetime NOT NULL,
  PRIMARY KEY (`history_id`),
  KEY `fk_budget_approval_budget` (`budget_id`),
  KEY `fk_budget_approval_user` (`user_id`),
  CONSTRAINT `fk_budget_approval_budget` FOREIGN KEY (`budget_id`) REFERENCES `cod_advanced_budget` (`budget_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `cod_departments` (
  `department_id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `code` varchar(20) NOT NULL,
  `description` text,
  `manager_id` int(11) DEFAULT NULL,
  `parent_department_id` int(11) DEFAULT NULL,
  `status` tinyint(1) NOT NULL DEFAULT '1',
  `date_added` datetime NOT NULL,
  `date_modified` datetime NOT NULL,
  PRIMARY KEY (`department_id`),
  UNIQUE KEY `code` (`code`),
  KEY `fk_department_parent` (`parent_department_id`),
  CONSTRAINT `fk_department_parent` FOREIGN KEY (`parent_department_id`) REFERENCES `cod_departments` (`department_id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `cod_cost_centers` (
  `cost_center_id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `code` varchar(20) NOT NULL,
  `description` text,
  `department_id` int(11) DEFAULT NULL,
  `manager_id` int(11) DEFAULT NULL,
  `status` tinyint(1) NOT NULL DEFAULT '1',
  `date_added` datetime NOT NULL,
  `date_modified` datetime NOT NULL,
  PRIMARY KEY (`cost_center_id`),
  UNIQUE KEY `code` (`code`),
  KEY `fk_cost_center_department` (`department_id`),
  CONSTRAINT `fk_cost_center_department` FOREIGN KEY (`department_id`) REFERENCES `cod_departments` (`department_id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إضافة جداول لوحة التحليلات المالية
CREATE TABLE `cod_financial_analytics_dashboard` (
  `dashboard_id` int(11) NOT NULL AUTO_INCREMENT,
  `dashboard_name` varchar(255) NOT NULL,
  `dashboard_type` enum('executive','operational','financial','custom') NOT NULL DEFAULT 'financial',
  `user_id` int(11) NOT NULL,
  `is_default` tinyint(1) NOT NULL DEFAULT '0',
  `layout_config` text,
  `refresh_interval` int(11) NOT NULL DEFAULT '300',
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `date_created` datetime NOT NULL,
  `date_modified` datetime NOT NULL,
  PRIMARY KEY (`dashboard_id`),
  KEY `fk_dashboard_user` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `cod_financial_analytics_widgets` (
  `widget_id` int(11) NOT NULL AUTO_INCREMENT,
  `dashboard_id` int(11) NOT NULL,
  `widget_type` enum('chart','table','kpi','gauge','trend') NOT NULL,
  `widget_title` varchar(255) NOT NULL,
  `data_source` varchar(255) NOT NULL,
  `chart_type` enum('line','bar','pie','doughnut','area','scatter') DEFAULT NULL,
  `position_x` int(11) NOT NULL DEFAULT '0',
  `position_y` int(11) NOT NULL DEFAULT '0',
  `width` int(11) NOT NULL DEFAULT '4',
  `height` int(11) NOT NULL DEFAULT '3',
  `config` text,
  `filters` text,
  `date_added` datetime NOT NULL,
  PRIMARY KEY (`widget_id`),
  KEY `fk_widget_dashboard` (`dashboard_id`),
  CONSTRAINT `fk_widget_dashboard` FOREIGN KEY (`dashboard_id`) REFERENCES `cod_financial_analytics_dashboard` (`dashboard_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إضافة جداول مسار التدقيق المتقدم
CREATE TABLE `cod_audit_trail_advanced` (
  `audit_id` int(11) NOT NULL AUTO_INCREMENT,
  `audit_name` varchar(255) NOT NULL,
  `audit_type` enum('financial','operational','compliance','security') NOT NULL,
  `workflow_id` int(11) DEFAULT NULL,
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `auditor_id` int(11) NOT NULL,
  `status` enum('planning','in_progress','review','completed','archived') NOT NULL DEFAULT 'planning',
  `risk_level` enum('low','medium','high','critical') NOT NULL DEFAULT 'medium',
  `scope` text,
  `objectives` text,
  `date_created` datetime NOT NULL,
  `date_modified` datetime NOT NULL,
  PRIMARY KEY (`audit_id`),
  KEY `fk_audit_workflow` (`workflow_id`),
  KEY `fk_audit_auditor` (`auditor_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `cod_audit_trail_findings` (
  `finding_id` int(11) NOT NULL AUTO_INCREMENT,
  `audit_id` int(11) NOT NULL,
  `finding_title` varchar(255) NOT NULL,
  `finding_description` text NOT NULL,
  `risk_level` enum('low','medium','high','critical') NOT NULL,
  `category` varchar(100) NOT NULL,
  `status` enum('open','in_progress','resolved','closed') NOT NULL DEFAULT 'open',
  `responsible_user_id` int(11) DEFAULT NULL,
  `due_date` date DEFAULT NULL,
  `resolution_notes` text,
  `date_identified` datetime NOT NULL,
  `date_resolved` datetime DEFAULT NULL,
  PRIMARY KEY (`finding_id`),
  KEY `fk_finding_audit` (`audit_id`),
  KEY `fk_finding_responsible` (`responsible_user_id`),
  CONSTRAINT `fk_finding_audit` FOREIGN KEY (`audit_id`) REFERENCES `cod_audit_trail_advanced` (`audit_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `cod_audit_trail_evidence` (
  `evidence_id` int(11) NOT NULL AUTO_INCREMENT,
  `audit_id` int(11) NOT NULL,
  `finding_id` int(11) DEFAULT NULL,
  `evidence_type` enum('document','screenshot','data_export','interview','observation') NOT NULL,
  `evidence_title` varchar(255) NOT NULL,
  `evidence_description` text,
  `file_path` varchar(500) DEFAULT NULL,
  `collected_by` int(11) NOT NULL,
  `date_collected` datetime NOT NULL,
  PRIMARY KEY (`evidence_id`),
  KEY `fk_evidence_audit` (`audit_id`),
  KEY `fk_evidence_finding` (`finding_id`),
  KEY `fk_evidence_collector` (`collected_by`),
  CONSTRAINT `fk_evidence_audit` FOREIGN KEY (`audit_id`) REFERENCES `cod_audit_trail_advanced` (`audit_id`) ON DELETE CASCADE,
  CONSTRAINT `fk_evidence_finding` FOREIGN KEY (`finding_id`) REFERENCES `cod_audit_trail_findings` (`finding_id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إضافة جداول إضافية للتنبؤ بالتدفق النقدي
CREATE TABLE `cod_cash_flow_scenarios` (
  `scenario_id` int(11) NOT NULL AUTO_INCREMENT,
  `forecast_id` int(11) NOT NULL,
  `scenario_name` varchar(255) NOT NULL,
  `scenario_type` enum('optimistic','pessimistic','realistic','custom') NOT NULL,
  `scenario_data` text NOT NULL,
  `created_by` int(11) NOT NULL,
  `date_created` datetime NOT NULL,
  PRIMARY KEY (`scenario_id`),
  KEY `fk_scenario_forecast` (`forecast_id`),
  KEY `fk_scenario_creator` (`created_by`),
  CONSTRAINT `fk_scenario_forecast` FOREIGN KEY (`forecast_id`) REFERENCES `cod_cash_flow_forecast` (`forecast_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- تحديث جدول cod_cash_flow_forecast لإضافة حقول جديدة
ALTER TABLE `cod_cash_flow_forecast`
ADD COLUMN `forecast_method` enum('manual','moving_average','linear_regression','seasonal_decomposition','exponential_smoothing','ai_prediction') DEFAULT 'manual' AFTER `currency`,
ADD COLUMN `base_scenario` varchar(100) DEFAULT 'realistic' AFTER `forecast_method`,
ADD COLUMN `include_historical` tinyint(1) DEFAULT '0' AFTER `base_scenario`,
ADD COLUMN `generation_method` varchar(100) DEFAULT NULL AFTER `include_historical`,
ADD COLUMN `accuracy_score` decimal(5,2) DEFAULT NULL AFTER `generation_method`,
ADD COLUMN `date_generated` datetime DEFAULT NULL AFTER `accuracy_score`,
ADD COLUMN `date_activated` datetime DEFAULT NULL AFTER `date_generated`;

-- تحديث جدول cod_cash_flow_forecast_lines لإضافة حقول جديدة
ALTER TABLE `cod_cash_flow_forecast_lines`
ADD COLUMN `department_id` int(11) DEFAULT NULL AFTER `account_id`,
ADD COLUMN `cost_center_id` int(11) DEFAULT NULL AFTER `department_id`,
ADD COLUMN `variance_percentage` decimal(8,4) DEFAULT '0.0000' AFTER `variance`;

-- إضافة مفاتيح خارجية للحقول الجديدة
ALTER TABLE `cod_cash_flow_forecast_lines`
ADD KEY `fk_forecast_lines_department` (`department_id`),
ADD KEY `fk_forecast_lines_cost_center` (`cost_center_id`),
ADD CONSTRAINT `fk_forecast_lines_department` FOREIGN KEY (`department_id`) REFERENCES `cod_departments` (`department_id`) ON DELETE SET NULL,
ADD CONSTRAINT `fk_forecast_lines_cost_center` FOREIGN KEY (`cost_center_id`) REFERENCES `cod_cost_centers` (`cost_center_id`) ON DELETE SET NULL;

-- إضافة الجداول المفقودة التي تم اكتشافها في المراجعة الشاملة

-- جدول الشركات (مطلوب للمعاملات بين الشركات)
CREATE TABLE `cod_companies` (
  `company_id` int(11) NOT NULL AUTO_INCREMENT,
  `company_code` varchar(20) NOT NULL,
  `company_name` varchar(255) NOT NULL,
  `company_type` enum('parent','subsidiary','associate','joint_venture') NOT NULL DEFAULT 'subsidiary',
  `legal_name` varchar(255) NOT NULL,
  `tax_id` varchar(50) DEFAULT NULL,
  `registration_number` varchar(100) DEFAULT NULL,
  `functional_currency` varchar(3) NOT NULL DEFAULT 'EGP',
  `country_id` int(11) DEFAULT NULL,
  `address` text,
  `phone` varchar(32) DEFAULT NULL,
  `email` varchar(96) DEFAULT NULL,
  `website` varchar(255) DEFAULT NULL,
  `status` tinyint(1) NOT NULL DEFAULT '1',
  `date_added` datetime NOT NULL,
  `date_modified` datetime NOT NULL,
  PRIMARY KEY (`company_id`),
  UNIQUE KEY `company_code` (`company_code`),
  KEY `idx_company_type` (`company_type`),
  KEY `idx_company_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول قوالب سير العمل (مطلوب لمحرر سير العمل المرئي)
CREATE TABLE `cod_workflow_template` (
  `template_id` int(11) NOT NULL AUTO_INCREMENT,
  `template_name` varchar(255) NOT NULL,
  `template_category` varchar(100) NOT NULL,
  `template_description` text,
  `template_data` longtext NOT NULL,
  `template_version` varchar(20) NOT NULL DEFAULT '1.0',
  `is_public` tinyint(1) NOT NULL DEFAULT '0',
  `usage_count` int(11) NOT NULL DEFAULT '0',
  `created_by` int(11) NOT NULL,
  `date_created` datetime NOT NULL,
  `date_modified` datetime NOT NULL,
  PRIMARY KEY (`template_id`),
  KEY `idx_template_category` (`template_category`),
  KEY `idx_template_public` (`is_public`),
  KEY `fk_template_creator` (`created_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول تقارير التدقيق (مطلوب لمسار التدقيق المتقدم)
CREATE TABLE `cod_audit_reports` (
  `report_id` int(11) NOT NULL AUTO_INCREMENT,
  `audit_id` int(11) NOT NULL,
  `report_name` varchar(255) NOT NULL,
  `report_type` enum('preliminary','interim','final','follow_up') NOT NULL,
  `report_content` longtext,
  `executive_summary` text,
  `findings_count` int(11) NOT NULL DEFAULT '0',
  `high_risk_findings` int(11) NOT NULL DEFAULT '0',
  `medium_risk_findings` int(11) NOT NULL DEFAULT '0',
  `low_risk_findings` int(11) NOT NULL DEFAULT '0',
  `recommendations_count` int(11) NOT NULL DEFAULT '0',
  `report_status` enum('draft','review','approved','published') NOT NULL DEFAULT 'draft',
  `generated_by` int(11) NOT NULL,
  `reviewed_by` int(11) DEFAULT NULL,
  `approved_by` int(11) DEFAULT NULL,
  `date_generated` datetime NOT NULL,
  `date_reviewed` datetime DEFAULT NULL,
  `date_approved` datetime DEFAULT NULL,
  `date_published` datetime DEFAULT NULL,
  PRIMARY KEY (`report_id`),
  KEY `fk_audit_report_audit` (`audit_id`),
  KEY `fk_audit_report_generator` (`generated_by`),
  KEY `fk_audit_report_reviewer` (`reviewed_by`),
  KEY `fk_audit_report_approver` (`approved_by`),
  KEY `idx_report_status` (`report_status`),
  CONSTRAINT `fk_audit_report_audit` FOREIGN KEY (`audit_id`) REFERENCES `cod_audit_trail_advanced` (`audit_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- جدول مهام التدقيق (مطلوب لمسار التدقيق المتقدم)
CREATE TABLE `cod_audit_tasks` (
  `task_id` int(11) NOT NULL AUTO_INCREMENT,
  `audit_id` int(11) NOT NULL,
  `task_name` varchar(255) NOT NULL,
  `task_description` text,
  `task_type` enum('planning','fieldwork','testing','review','reporting') NOT NULL,
  `assigned_to` int(11) NOT NULL,
  `estimated_hours` decimal(8,2) DEFAULT '0.00',
  `actual_hours` decimal(8,2) DEFAULT '0.00',
  `priority` enum('low','medium','high','critical') NOT NULL DEFAULT 'medium',
  `task_status` enum('not_started','in_progress','completed','on_hold','cancelled') NOT NULL DEFAULT 'not_started',
  `start_date` date DEFAULT NULL,
  `due_date` date DEFAULT NULL,
  `completion_date` datetime DEFAULT NULL,
  `completion_percentage` int(3) NOT NULL DEFAULT '0',
  `dependencies` text,
  `notes` text,
  `created_by` int(11) NOT NULL,
  `date_created` datetime NOT NULL,
  `date_modified` datetime NOT NULL,
  PRIMARY KEY (`task_id`),
  KEY `fk_audit_task_audit` (`audit_id`),
  KEY `fk_audit_task_assignee` (`assigned_to`),
  KEY `fk_audit_task_creator` (`created_by`),
  KEY `idx_task_status` (`task_status`),
  KEY `idx_task_priority` (`priority`),
  CONSTRAINT `fk_audit_task_audit` FOREIGN KEY (`audit_id`) REFERENCES `cod_audit_trail_advanced` (`audit_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;