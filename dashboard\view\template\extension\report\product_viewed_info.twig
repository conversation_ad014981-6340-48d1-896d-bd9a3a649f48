<div class="panel panel-default">
  <div class="panel-heading">
    <div class="pull-right">
      <a href="{{ reset }}" class="btn btn-danger btn-xs"><i class="fa fa-refresh"></i> {{ button_reset }}</a>
    </div>
    <h3 class="panel-title"><i class="fa fa-filter"></i> {{ heading_title }}</h3>
  </div>
  <div class="panel-body">
    <div class="table-responsive">
      <table class="table table-bordered">
        <thead>
          <tr>
            <td class="text-left">{{ column_name }}</td>
            <td class="text-left">{{ column_model }}</td>
            <td class="text-right">{{ column_viewed }}</td>
            <td class="text-right">{{ column_percent }}</td>
          </tr>
        </thead>
        <tbody>
        
        {% if products %}
        {% for product in products %}
        <tr>
          <td class="text-left">{{ product.name }}</td>
          <td class="text-left">{{ product.model }}</td>
          <td class="text-right">{{ product.viewed }}</td>
          <td class="text-right">{{ product.percent }}</td>
        </tr>
        {% endfor %}
        {% else %}
        <tr>
          <td class="text-center" colspan="4">{{ text_no_results }}</td>
        </tr>
        {% endif %}
        </tbody>
        
      </table>
    </div>
    <div class="row">
      <div class="col-sm-6 text-left">{{ pagination }}</div>
      <div class="col-sm-6 text-right">{{ results }}</div>
    </div>
  </div>
</div>