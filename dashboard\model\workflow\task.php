<?php
class ModelWorkflowTask extends Model {
    
    public function addTask($data) {
        $this->db->query("INSERT INTO " . DB_PREFIX . "workflow_task SET 
            title = '" . $this->db->escape($data['title']) . "',
            description = '" . $this->db->escape($data['description']) . "',
            assignee_id = '" . (int)$data['assignee_id'] . "',
            priority = '" . $this->db->escape($data['priority']) . "',
            status = '" . $this->db->escape($data['status']) . "',
            due_date = '" . $this->db->escape($data['due_date']) . "',
            estimated_hours = '" . (float)$data['estimated_hours'] . "',
            tags = '" . $this->db->escape($data['tags']) . "',
            progress = 0,
            created_by = '" . (int)$this->user->getId() . "',
            created_date = NOW()");
        
        $task_id = $this->db->getLastId();
        
        // إضافة سجل في التاريخ
        $this->addTaskHistory($task_id, 'created', 'Task created');
        
        return $task_id;
    }
    
    public function updateTask($task_id, $data) {
        $this->db->query("UPDATE " . DB_PREFIX . "workflow_task SET 
            title = '" . $this->db->escape($data['title']) . "',
            description = '" . $this->db->escape($data['description']) . "',
            assignee_id = '" . (int)$data['assignee_id'] . "',
            priority = '" . $this->db->escape($data['priority']) . "',
            status = '" . $this->db->escape($data['status']) . "',
            due_date = '" . $this->db->escape($data['due_date']) . "',
            estimated_hours = '" . (float)$data['estimated_hours'] . "',
            tags = '" . $this->db->escape($data['tags']) . "',
            modified_by = '" . (int)$this->user->getId() . "',
            modified_date = NOW()
            WHERE task_id = '" . (int)$task_id . "'");
        
        // إضافة سجل في التاريخ
        $this->addTaskHistory($task_id, 'updated', 'Task updated');
    }
    
    public function deleteTask($task_id) {
        $this->db->query("DELETE FROM " . DB_PREFIX . "workflow_task WHERE task_id = '" . (int)$task_id . "'");
        $this->db->query("DELETE FROM " . DB_PREFIX . "workflow_task_history WHERE task_id = '" . (int)$task_id . "'");
        $this->db->query("DELETE FROM " . DB_PREFIX . "workflow_task_comment WHERE task_id = '" . (int)$task_id . "'");
    }
    
    public function getTask($task_id) {
        $query = $this->db->query("SELECT t.*, 
                                   CONCAT(u.firstname, ' ', u.lastname) as assignee_name,
                                   u.email as assignee_email
                                   FROM " . DB_PREFIX . "workflow_task t 
                                   LEFT JOIN " . DB_PREFIX . "user u ON (t.assignee_id = u.user_id)
                                   WHERE t.task_id = '" . (int)$task_id . "'");
        return $query->row;
    }
    
    public function getTasks($data = array()) {
        $sql = "SELECT t.*, 
                CONCAT(u.firstname, ' ', u.lastname) as assignee_name
                FROM " . DB_PREFIX . "workflow_task t 
                LEFT JOIN " . DB_PREFIX . "user u ON (t.assignee_id = u.user_id)";
        
        $implode = array();
        
        if (!empty($data['filter_title'])) {
            $implode[] = "t.title LIKE '%" . $this->db->escape($data['filter_title']) . "%'";
        }
        
        if (!empty($data['filter_assignee'])) {
            $implode[] = "CONCAT(u.firstname, ' ', u.lastname) LIKE '%" . $this->db->escape($data['filter_assignee']) . "%'";
        }
        
        if (!empty($data['filter_priority'])) {
            $implode[] = "t.priority = '" . $this->db->escape($data['filter_priority']) . "'";
        }
        
        if (!empty($data['filter_status'])) {
            $implode[] = "t.status = '" . $this->db->escape($data['filter_status']) . "'";
        }
        
        if (!empty($data['filter_due_date'])) {
            $implode[] = "DATE(t.due_date) = DATE('" . $this->db->escape($data['filter_due_date']) . "')";
        }
        
        if ($implode) {
            $sql .= " WHERE " . implode(" AND ", $implode);
        }
        
        $sql .= " ORDER BY t.priority DESC, t.due_date ASC";
        
        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }
            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }
            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }
        
        $query = $this->db->query($sql);
        return $query->rows;
    }
    
    public function getTotalTasks($data = array()) {
        $sql = "SELECT COUNT(*) AS total FROM " . DB_PREFIX . "workflow_task t 
                LEFT JOIN " . DB_PREFIX . "user u ON (t.assignee_id = u.user_id)";
        
        $implode = array();
        
        if (!empty($data['filter_title'])) {
            $implode[] = "t.title LIKE '%" . $this->db->escape($data['filter_title']) . "%'";
        }
        
        if (!empty($data['filter_assignee'])) {
            $implode[] = "CONCAT(u.firstname, ' ', u.lastname) LIKE '%" . $this->db->escape($data['filter_assignee']) . "%'";
        }
        
        if (!empty($data['filter_priority'])) {
            $implode[] = "t.priority = '" . $this->db->escape($data['filter_priority']) . "'";
        }
        
        if (!empty($data['filter_status'])) {
            $implode[] = "t.status = '" . $this->db->escape($data['filter_status']) . "'";
        }
        
        if (!empty($data['filter_due_date'])) {
            $implode[] = "DATE(t.due_date) = DATE('" . $this->db->escape($data['filter_due_date']) . "')";
        }
        
        if ($implode) {
            $sql .= " WHERE " . implode(" AND ", $implode);
        }
        
        $query = $this->db->query($sql);
        return $query->row['total'];
    }
    
    public function getTaskHistory($task_id) {
        $query = $this->db->query("SELECT th.*, 
                                   CONCAT(u.firstname, ' ', u.lastname) as user_name
                                   FROM " . DB_PREFIX . "workflow_task_history th 
                                   LEFT JOIN " . DB_PREFIX . "user u ON (th.user_id = u.user_id)
                                   WHERE th.task_id = '" . (int)$task_id . "' 
                                   ORDER BY th.date_added DESC");
        return $query->rows;
    }
    
    public function getTaskComments($task_id) {
        $query = $this->db->query("SELECT tc.*, 
                                   CONCAT(u.firstname, ' ', u.lastname) as user_name
                                   FROM " . DB_PREFIX . "workflow_task_comment tc 
                                   LEFT JOIN " . DB_PREFIX . "user u ON (tc.user_id = u.user_id)
                                   WHERE tc.task_id = '" . (int)$task_id . "' 
                                   ORDER BY tc.date_added DESC");
        return $query->rows;
    }
    
    public function addTaskHistory($task_id, $action, $description) {
        $this->db->query("INSERT INTO " . DB_PREFIX . "workflow_task_history SET 
            task_id = '" . (int)$task_id . "',
            action = '" . $this->db->escape($action) . "',
            description = '" . $this->db->escape($description) . "',
            user_id = '" . (int)$this->user->getId() . "',
            date_added = NOW()");
    }
    
    public function addTaskComment($task_id, $comment) {
        $this->db->query("INSERT INTO " . DB_PREFIX . "workflow_task_comment SET 
            task_id = '" . (int)$task_id . "',
            comment = '" . $this->db->escape($comment) . "',
            user_id = '" . (int)$this->user->getId() . "',
            date_added = NOW()");
    }
    
    public function updateTaskStatus($task_id, $status, $progress = 0) {
        $completed_date = ($status == 'completed') ? ', completed_date = NOW()' : '';
        
        $this->db->query("UPDATE " . DB_PREFIX . "workflow_task SET 
            status = '" . $this->db->escape($status) . "',
            progress = '" . (int)$progress . "'" . $completed_date . ",
            modified_by = '" . (int)$this->user->getId() . "',
            modified_date = NOW()
            WHERE task_id = '" . (int)$task_id . "'");
        
        // إضافة سجل في التاريخ
        $this->addTaskHistory($task_id, 'status_changed', 'Status changed to ' . $status . ' (Progress: ' . $progress . '%)');
    }
    
    public function getMyTasks($user_id) {
        $query = $this->db->query("SELECT t.*, 
                                   CONCAT(u.firstname, ' ', u.lastname) as assignee_name
                                   FROM " . DB_PREFIX . "workflow_task t 
                                   LEFT JOIN " . DB_PREFIX . "user u ON (t.assignee_id = u.user_id)
                                   WHERE t.assignee_id = '" . (int)$user_id . "'
                                   ORDER BY t.priority DESC, t.due_date ASC");
        return $query->rows;
    }
    
    public function getOverdueTasks() {
        $query = $this->db->query("SELECT t.*, 
                                   CONCAT(u.firstname, ' ', u.lastname) as assignee_name
                                   FROM " . DB_PREFIX . "workflow_task t 
                                   LEFT JOIN " . DB_PREFIX . "user u ON (t.assignee_id = u.user_id)
                                   WHERE t.status NOT IN ('completed', 'cancelled')
                                   AND t.due_date < CURDATE()
                                   ORDER BY t.due_date ASC");
        return $query->rows;
    }
    
    public function getTaskStatistics() {
        $stats = array();
        
        // إحصائيات الحالات
        $query = $this->db->query("SELECT status, COUNT(*) as count 
                                   FROM " . DB_PREFIX . "workflow_task 
                                   GROUP BY status");
        $stats['status_counts'] = $query->rows;
        
        // إحصائيات الأولويات
        $query = $this->db->query("SELECT priority, COUNT(*) as count 
                                   FROM " . DB_PREFIX . "workflow_task 
                                   GROUP BY priority");
        $stats['priority_counts'] = $query->rows;
        
        // المهام المتأخرة
        $query = $this->db->query("SELECT COUNT(*) as count 
                                   FROM " . DB_PREFIX . "workflow_task 
                                   WHERE status NOT IN ('completed', 'cancelled')
                                   AND due_date < CURDATE()");
        $stats['overdue_count'] = $query->row['count'];
        
        // المهام المستحقة اليوم
        $query = $this->db->query("SELECT COUNT(*) as count 
                                   FROM " . DB_PREFIX . "workflow_task 
                                   WHERE status NOT IN ('completed', 'cancelled')
                                   AND DATE(due_date) = CURDATE()");
        $stats['due_today_count'] = $query->row['count'];
        
        // متوسط التقدم
        $query = $this->db->query("SELECT AVG(progress) as avg_progress 
                                   FROM " . DB_PREFIX . "workflow_task 
                                   WHERE status NOT IN ('completed', 'cancelled')");
        $stats['avg_progress'] = round($query->row['avg_progress'], 2);
        
        return $stats;
    }
    
    public function searchTasks($keyword) {
        $query = $this->db->query("SELECT t.*, 
                                   CONCAT(u.firstname, ' ', u.lastname) as assignee_name
                                   FROM " . DB_PREFIX . "workflow_task t 
                                   LEFT JOIN " . DB_PREFIX . "user u ON (t.assignee_id = u.user_id)
                                   WHERE t.title LIKE '%" . $this->db->escape($keyword) . "%'
                                   OR t.description LIKE '%" . $this->db->escape($keyword) . "%'
                                   OR t.tags LIKE '%" . $this->db->escape($keyword) . "%'
                                   ORDER BY t.priority DESC, t.due_date ASC");
        return $query->rows;
    }
} 