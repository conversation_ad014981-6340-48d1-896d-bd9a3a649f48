{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="button" id="button-generate" data-toggle="tooltip" title="{{ button_generate }}" class="btn btn-primary">
          <i class="fa fa-cogs"></i> {{ button_generate }}
        </button>
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default">
          <i class="fa fa-reply"></i> {{ button_cancel }}
        </a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        <li><a href="{{ home }}">{{ text_home }}</a></li>
        <li><a href="{{ cancel }}">{{ text_accounts }}</a></li>
        <li class="active">{{ heading_title }}</li>
      </ul>
    </div>
  </div>
  
  <div class="container-fluid">
    <!-- إحصائيات سريعة -->
    <div class="row">
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-primary">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-building fa-5x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ subsidiaries|length }}</div>
                <div>{{ text_subsidiaries }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-green">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-file-text fa-5x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ recent_consolidations|length }}</div>
                <div>{{ text_recent_reports }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-yellow">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-calendar fa-5x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ text_current_period }}</div>
                <div>{{ text_period }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-red">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-exchange fa-5x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ consolidation_settings.default_currency|default('EGP') }}</div>
                <div>{{ text_currency }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- نموذج إنشاء التوحيد -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-plus"></i> {{ text_new_consolidation }}</h3>
      </div>
      <div class="panel-body">
        <form id="form-consolidation" class="form-horizontal">
          <div class="row">
            <div class="col-md-6">
              <div class="form-group required">
                <label class="col-sm-3 control-label" for="input-period-start">{{ entry_period_start }}</label>
                <div class="col-sm-9">
                  <div class="input-group date">
                    <input type="text" name="period_start" value="" placeholder="{{ entry_period_start }}" 
                           id="input-period-start" class="form-control" required />
                    <span class="input-group-btn">
                      <button class="btn btn-default" type="button">
                        <i class="fa fa-calendar"></i>
                      </button>
                    </span>
                  </div>
                </div>
              </div>
              
              <div class="form-group required">
                <label class="col-sm-3 control-label" for="input-period-end">{{ entry_period_end }}</label>
                <div class="col-sm-9">
                  <div class="input-group date">
                    <input type="text" name="period_end" value="" placeholder="{{ entry_period_end }}" 
                           id="input-period-end" class="form-control" required />
                    <span class="input-group-btn">
                      <button class="btn btn-default" type="button">
                        <i class="fa fa-calendar"></i>
                      </button>
                    </span>
                  </div>
                </div>
              </div>
              
              <div class="form-group required">
                <label class="col-sm-3 control-label" for="input-method">{{ entry_consolidation_method }}</label>
                <div class="col-sm-9">
                  <select name="consolidation_method" id="input-method" class="form-control" required>
                    <option value="">{{ text_select }}</option>
                    <option value="full">{{ text_full_consolidation }}</option>
                    <option value="proportional">{{ text_proportional_consolidation }}</option>
                    <option value="equity">{{ text_equity_method }}</option>
                  </select>
                </div>
              </div>
            </div>
            
            <div class="col-md-6">
              <div class="form-group required">
                <label class="col-sm-3 control-label" for="input-currency">{{ entry_currency }}</label>
                <div class="col-sm-9">
                  <select name="currency" id="input-currency" class="form-control" required>
                    <option value="EGP" selected>{{ text_egp }}</option>
                    <option value="USD">{{ text_usd }}</option>
                    <option value="EUR">{{ text_eur }}</option>
                  </select>
                </div>
              </div>
              
              <div class="form-group">
                <label class="col-sm-3 control-label" for="input-subsidiaries">{{ entry_subsidiaries }}</label>
                <div class="col-sm-9">
                  <select name="subsidiaries[]" id="input-subsidiaries" class="form-control" multiple>
                    {% for subsidiary in subsidiaries %}
                    <option value="{{ subsidiary.subsidiary_id }}">{{ subsidiary.name }} ({{ subsidiary.subsidiary_type }})</option>
                    {% endfor %}
                  </select>
                  <div class="help-block">{{ help_subsidiaries }}</div>
                </div>
              </div>
              
              <div class="form-group">
                <div class="col-sm-offset-3 col-sm-9">
                  <div class="checkbox">
                    <label>
                      <input type="checkbox" name="include_adjustments" value="1" checked />
                      {{ text_include_adjustments }}
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>

    <!-- آخر التقارير -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-history"></i> {{ text_recent_consolidations }}</h3>
      </div>
      <div class="panel-body">
        {% if recent_consolidations %}
        <div class="table-responsive">
          <table class="table table-bordered table-hover">
            <thead>
              <tr>
                <th>{{ column_reference }}</th>
                <th>{{ column_period }}</th>
                <th>{{ column_method }}</th>
                <th>{{ column_currency }}</th>
                <th>{{ column_status }}</th>
                <th>{{ column_created_by }}</th>
                <th>{{ column_date_created }}</th>
                <th>{{ column_action }}</th>
              </tr>
            </thead>
            <tbody>
              {% for consolidation in recent_consolidations %}
              <tr>
                <td>{{ consolidation.reference }}</td>
                <td>{{ consolidation.period_start }} - {{ consolidation.period_end }}</td>
                <td>
                  {% if consolidation.consolidation_method == 'full' %}
                    <span class="label label-primary">{{ text_full_consolidation }}</span>
                  {% elseif consolidation.consolidation_method == 'proportional' %}
                    <span class="label label-info">{{ text_proportional_consolidation }}</span>
                  {% else %}
                    <span class="label label-warning">{{ text_equity_method }}</span>
                  {% endif %}
                </td>
                <td>{{ consolidation.reporting_currency }}</td>
                <td>
                  {% if consolidation.status == 'completed' %}
                    <span class="label label-success">{{ text_completed }}</span>
                  {% elseif consolidation.status == 'processing' %}
                    <span class="label label-warning">{{ text_processing }}</span>
                  {% else %}
                    <span class="label label-danger">{{ text_failed }}</span>
                  {% endif %}
                </td>
                <td>{{ consolidation.created_by_name }}</td>
                <td>{{ consolidation.date_created|date('d/m/Y H:i') }}</td>
                <td class="text-center">
                  <div class="btn-group">
                    <a href="{{ url_link('accounts/consolidation/view', 'consolidation_id=' ~ consolidation.consolidation_id ~ '&user_token=' ~ user_token) }}" 
                       data-toggle="tooltip" title="{{ button_view }}" class="btn btn-info btn-sm">
                      <i class="fa fa-eye"></i>
                    </a>
                    <a href="{{ url_link('accounts/consolidation/export', 'consolidation_id=' ~ consolidation.consolidation_id ~ '&format=pdf&user_token=' ~ user_token) }}" 
                       data-toggle="tooltip" title="{{ button_export }}" class="btn btn-success btn-sm">
                      <i class="fa fa-download"></i>
                    </a>
                  </div>
                </td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
        {% else %}
        <div class="text-center">
          <p>{{ text_no_results }}</p>
        </div>
        {% endif %}
      </div>
    </div>

    <!-- الشركات التابعة -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-building"></i> {{ text_subsidiaries_list }}</h3>
      </div>
      <div class="panel-body">
        {% if subsidiaries %}
        <div class="table-responsive">
          <table class="table table-bordered table-hover">
            <thead>
              <tr>
                <th>{{ column_name }}</th>
                <th>{{ column_type }}</th>
                <th>{{ column_currency }}</th>
                <th>{{ column_ownership }}</th>
                <th>{{ column_status }}</th>
              </tr>
            </thead>
            <tbody>
              {% for subsidiary in subsidiaries %}
              <tr>
                <td>{{ subsidiary.name }}</td>
                <td>
                  {% if subsidiary.subsidiary_type == 'subsidiary' %}
                    <span class="label label-primary">{{ text_subsidiary }}</span>
                  {% elseif subsidiary.subsidiary_type == 'associate' %}
                    <span class="label label-info">{{ text_associate }}</span>
                  {% else %}
                    <span class="label label-warning">{{ text_joint_venture }}</span>
                  {% endif %}
                </td>
                <td>{{ subsidiary.functional_currency }} {{ subsidiary.symbol_left }}{{ subsidiary.symbol_right }}</td>
                <td>{{ subsidiary.ownership_percentage }}%</td>
                <td>
                  {% if subsidiary.status %}
                    <span class="label label-success">{{ text_active }}</span>
                  {% else %}
                    <span class="label label-danger">{{ text_inactive }}</span>
                  {% endif %}
                </td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
        {% else %}
        <div class="text-center">
          <p>{{ text_no_subsidiaries }}</p>
        </div>
        {% endif %}
      </div>
    </div>
  </div>
</div>

<!-- Modal للتأكيد -->
<div class="modal fade" id="modal-confirm" tabindex="-1" role="dialog">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">
          <span>&times;</span>
        </button>
        <h4 class="modal-title">{{ text_confirm }}</h4>
      </div>
      <div class="modal-body">
        <p>{{ text_confirm_generate }}</p>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_cancel }}</button>
        <button type="button" class="btn btn-primary" id="button-confirm">{{ button_generate }}</button>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript">
$(document).ready(function() {
    // تهيئة Select2
    $('#input-subsidiaries').select2({
        placeholder: '{{ text_select_subsidiaries }}',
        allowClear: true
    });
    
    // تهيئة Date Range Picker
    $('#input-period-start, #input-period-end').daterangepicker({
        singleDatePicker: true,
        showDropdowns: true,
        locale: {
            format: 'YYYY-MM-DD'
        }
    });
    
    // زر إنشاء التوحيد
    $('#button-generate').on('click', function() {
        if ($('#form-consolidation')[0].checkValidity()) {
            $('#modal-confirm').modal('show');
        } else {
            $('#form-consolidation')[0].reportValidity();
        }
    });
    
    // تأكيد إنشاء التوحيد
    $('#button-confirm').on('click', function() {
        var formData = $('#form-consolidation').serialize();
        formData += '&action=generate_consolidation';
        
        $.ajax({
            url: '{{ ajax_generate_url }}',
            type: 'POST',
            data: formData,
            dataType: 'json',
            beforeSend: function() {
                $('#button-confirm').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> {{ text_processing }}');
            },
            success: function(json) {
                if (json.success) {
                    alert(json.success);
                    location.reload();
                } else if (json.error) {
                    alert(json.error);
                }
            },
            error: function() {
                alert('{{ error_ajax }}');
            },
            complete: function() {
                $('#button-confirm').prop('disabled', false).html('{{ button_generate }}');
                $('#modal-confirm').modal('hide');
            }
        });
    });
});
</script>

{{ footer }}
