<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/cloud/translate/v3/translation_service.proto

namespace Google\Cloud\Translate\V3;

if (false) {
    /**
     * This class is deprecated. Use Google\Cloud\Translate\V3\Glossary\LanguageCodesSet instead.
     * @deprecated
     */
    class Glossary_LanguageCodesSet {}
}
class_exists(Glossary\LanguageCodesSet::class);
@trigger_error('Google\Cloud\Translate\V3\Glossary_LanguageCodesSet is deprecated and will be removed in a future release. Use Google\Cloud\Translate\V3\Glossary\LanguageCodesSet instead', E_USER_DEPRECATED);

