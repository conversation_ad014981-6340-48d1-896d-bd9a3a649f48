<?php
/**
 * نموذج دليل الحسابات المحسن والمتكامل
 * يدعم الهيكل الشجري وتجميع الأرصدة والتكامل مع النظام
 */
class ModelAccountsChartaccount extends Model {

    /**
     * إضافة حساب جديد مع التحقق المتقدم والتكامل الكامل
     */
    public function addAccount($data) {
        // التحقق المتقدم من صحة البيانات
        $validation = $this->validateAccountDataAdvanced($data);
        if (!$validation['valid']) {
            throw new Exception($validation['error']);
        }

        // تحديد رقم الحساب التلقائي إذا لم يتم تحديده
        if (empty($data['account_code'])) {
            $data['account_code'] = $this->generateAccountCode($data['account_type'], $data['parent_id'] ?? null);
        }

        $this->db->query("START TRANSACTION");

        try {
            // إدراج الحساب مع البيانات المتوافقة مع قاعدة البيانات
            $this->db->query("INSERT INTO " . DB_PREFIX . "accounts SET
                account_code = '" . $this->db->escape($data['account_code']) . "',
                parent_id = " . (isset($data['parent_id']) && $data['parent_id'] ? "'" . (int)$data['parent_id'] . "'" : "0") . ",
                account_type = '" . $this->db->escape($data['account_type']) . "',
                status = '" . (int)(isset($data['status']) ? $data['status'] : 1) . "',
                date_added = NOW(),
                date_modified = NOW()");

            $account_id = $this->db->getLastId();

            // إدراج أوصاف الحساب متعددة اللغات
            if (isset($data['account_description'])) {
                foreach ($data['account_description'] as $language_id => $value) {
                    if (!empty($value['name'])) {
                        $this->db->query("INSERT INTO " . DB_PREFIX . "account_description SET
                            account_id = '" . (int)$account_id . "',
                            language_id = '" . (int)$language_id . "',
                            name = '" . $this->db->escape($value['name']) . "'");
                    }
                }
            }

            // إنشاء قيد الرصيد الافتتاحي إذا كان موجود
            if (!empty($data['opening_balance']) && $data['opening_balance'] != 0) {
                $this->createOpeningBalanceEntry($account_id, $data);
            }

            $this->db->query("COMMIT");
            $this->cache->delete('account');

            return $account_id;

        } catch (Exception $e) {
            $this->db->query("ROLLBACK");
            throw $e;
        }
    }

    /**
     * تعديل حساب موجود
     */
    public function editAccount($account_id, $data) {
        // التحقق من صحة البيانات
        if (!$this->validateAccountData($data, $account_id)) {
            return false;
        }

        // تحديث الحساب
        $this->db->query("UPDATE " . DB_PREFIX . "accounts SET
            account_code = '" . $this->db->escape($data['account_code']) . "',
            parent_id = " . (isset($data['parent_id']) && $data['parent_id'] ? "'" . (int)$data['parent_id'] . "'" : "0") . ",
            account_type = '" . $this->db->escape($data['account_type']) . "',
            status = '" . (int)(isset($data['status']) ? $data['status'] : 1) . "',
            date_modified = NOW()
            WHERE account_id = '" . (int)$account_id . "'");

        // حذف الأوصاف القديمة
        $this->db->query("DELETE FROM " . DB_PREFIX . "account_description WHERE account_id = '" . (int)$account_id . "'");

        // إدراج الأوصاف الجديدة
        if (isset($data['account_description'])) {
            foreach ($data['account_description'] as $language_id => $value) {
                if (!empty($value['name'])) {
                    $this->db->query("INSERT INTO " . DB_PREFIX . "account_description SET
                        account_id = '" . (int)$account_id . "',
                        language_id = '" . (int)$language_id . "',
                        name = '" . $this->db->escape($value['name']) . "'");
                }
            }
        }

        $this->cache->delete('account');
        return true;
    }

    /**
     * حذف حساب مع النسخ الاحتياطي
     */
    public function deleteAccount($account_id) {
        // التحقق من إمكانية الحذف
        if (!$this->canDeleteAccount($account_id)) {
            return false;
        }

        // إنشاء نسخة احتياطية قبل الحذف
        $this->createAccountBackup($account_id);

        $this->db->query("DELETE FROM " . DB_PREFIX . "accounts WHERE account_id = '" . (int)$account_id . "'");
        $this->db->query("DELETE FROM " . DB_PREFIX . "account_description WHERE account_id = '" . (int)$account_id . "'");

        $this->cache->delete('account');
        return true;
    }

    /**
     * إنشاء نسخة احتياطية للحساب قبل الحذف
     */
    private function createAccountBackup($account_id) {
        // الحصول على بيانات الحساب
        $account_query = $this->db->query("SELECT * FROM " . DB_PREFIX . "accounts WHERE account_id = '" . (int)$account_id . "'");

        if ($account_query->num_rows) {
            $account = $account_query->row;

            // إنشاء جدول النسخ الاحتياطية إذا لم يكن موجوداً
            $this->createBackupTableIfNotExists();

            // حفظ النسخة الاحتياطية
            $this->db->query("INSERT INTO " . DB_PREFIX . "accounts_backup SET
                original_account_id = '" . (int)$account['account_id'] . "',
                account_code = '" . $this->db->escape($account['account_code']) . "',
                parent_id = '" . (int)$account['parent_id'] . "',
                account_type = '" . $this->db->escape($account['account_type']) . "',
                account_nature = '" . $this->db->escape($account['account_nature']) . "',
                is_active = '" . (int)$account['is_active'] . "',
                sort_order = '" . (int)$account['sort_order'] . "',
                date_added = '" . $this->db->escape($account['date_added']) . "',
                date_modified = '" . $this->db->escape($account['date_modified']) . "',
                deleted_by = '" . (int)$this->user->getId() . "',
                deleted_date = NOW(),
                backup_reason = 'Account deletion'");

            $backup_id = $this->db->getLastId();

            // نسخ أوصاف الحساب
            $descriptions = $this->db->query("SELECT * FROM " . DB_PREFIX . "account_description WHERE account_id = '" . (int)$account_id . "'");

            foreach ($descriptions->rows as $description) {
                $this->db->query("INSERT INTO " . DB_PREFIX . "account_description_backup SET
                    backup_id = '" . (int)$backup_id . "',
                    language_id = '" . (int)$description['language_id'] . "',
                    name = '" . $this->db->escape($description['name']) . "',
                    description = '" . $this->db->escape($description['description']) . "'");
            }
        }
    }

    /**
     * إنشاء جداول النسخ الاحتياطية
     */
    private function createBackupTableIfNotExists() {
        // جدول النسخ الاحتياطية للحسابات
        $this->db->query("CREATE TABLE IF NOT EXISTS " . DB_PREFIX . "accounts_backup (
            backup_id int(11) NOT NULL AUTO_INCREMENT,
            original_account_id int(11) NOT NULL,
            account_code varchar(20) NOT NULL,
            parent_id int(11) NOT NULL DEFAULT 0,
            account_type enum('asset','liability','equity','revenue','expense') NOT NULL,
            account_nature enum('debit','credit') NOT NULL,
            is_active tinyint(1) NOT NULL DEFAULT 1,
            sort_order int(3) NOT NULL DEFAULT 0,
            date_added datetime NOT NULL,
            date_modified datetime NOT NULL,
            deleted_by int(11) NOT NULL,
            deleted_date datetime NOT NULL,
            backup_reason varchar(255) NOT NULL,
            restored tinyint(1) NOT NULL DEFAULT 0,
            restored_date datetime NULL,
            PRIMARY KEY (backup_id),
            KEY original_account_id (original_account_id),
            KEY deleted_by (deleted_by),
            KEY deleted_date (deleted_date)
        ) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;");

        // جدول النسخ الاحتياطية لأوصاف الحسابات
        $this->db->query("CREATE TABLE IF NOT EXISTS " . DB_PREFIX . "account_description_backup (
            backup_description_id int(11) NOT NULL AUTO_INCREMENT,
            backup_id int(11) NOT NULL,
            language_id int(11) NOT NULL,
            name varchar(255) NOT NULL,
            description text,
            PRIMARY KEY (backup_description_id),
            KEY backup_id (backup_id),
            KEY language_id (language_id)
        ) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;");
    }

    /**
     * أرشفة الحساب بدلاً من حذفه
     */
    public function archiveAccount($account_id) {
        // إضافة عمود الأرشفة إذا لم يكن موجوداً
        $this->addArchiveColumnsIfNotExists();

        $this->db->query("UPDATE " . DB_PREFIX . "accounts SET
            is_active = 0,
            archived = 1,
            archived_date = NOW(),
            archived_by = '" . (int)$this->user->getId() . "'
            WHERE account_id = '" . (int)$account_id . "'");

        $this->cache->delete('account');
        return true;
    }

    /**
     * إضافة أعمدة الأرشفة إذا لم تكن موجودة
     */
    private function addArchiveColumnsIfNotExists() {
        // التحقق من وجود عمود archived
        $check_archived = $this->db->query("SHOW COLUMNS FROM " . DB_PREFIX . "accounts LIKE 'archived'");
        if (!$check_archived->num_rows) {
            $this->db->query("ALTER TABLE " . DB_PREFIX . "accounts ADD COLUMN archived tinyint(1) NOT NULL DEFAULT 0");
        }

        // التحقق من وجود عمود archived_date
        $check_archived_date = $this->db->query("SHOW COLUMNS FROM " . DB_PREFIX . "accounts LIKE 'archived_date'");
        if (!$check_archived_date->num_rows) {
            $this->db->query("ALTER TABLE " . DB_PREFIX . "accounts ADD COLUMN archived_date datetime NULL");
        }

        // التحقق من وجود عمود archived_by
        $check_archived_by = $this->db->query("SHOW COLUMNS FROM " . DB_PREFIX . "accounts LIKE 'archived_by'");
        if (!$check_archived_by->num_rows) {
            $this->db->query("ALTER TABLE " . DB_PREFIX . "accounts ADD COLUMN archived_by int(11) NULL");
        }
    }

    /**
     * استعادة حساب من النسخة الاحتياطية
     */
    public function restoreAccountFromBackup($backup_id) {
        $backup_query = $this->db->query("SELECT * FROM " . DB_PREFIX . "accounts_backup
            WHERE backup_id = '" . (int)$backup_id . "' AND restored = 0");

        if ($backup_query->num_rows) {
            $backup = $backup_query->row;

            // استعادة الحساب
            $this->db->query("INSERT INTO " . DB_PREFIX . "accounts SET
                account_code = '" . $this->db->escape($backup['account_code']) . "',
                parent_id = '" . (int)$backup['parent_id'] . "',
                account_type = '" . $this->db->escape($backup['account_type']) . "',
                account_nature = '" . $this->db->escape($backup['account_nature']) . "',
                is_active = '" . (int)$backup['is_active'] . "',
                sort_order = '" . (int)$backup['sort_order'] . "',
                date_added = NOW(),
                date_modified = NOW()");

            $new_account_id = $this->db->getLastId();

            // استعادة الأوصاف
            $descriptions = $this->db->query("SELECT * FROM " . DB_PREFIX . "account_description_backup
                WHERE backup_id = '" . (int)$backup_id . "'");

            foreach ($descriptions->rows as $description) {
                $this->db->query("INSERT INTO " . DB_PREFIX . "account_description SET
                    account_id = '" . (int)$new_account_id . "',
                    language_id = '" . (int)$description['language_id'] . "',
                    name = '" . $this->db->escape($description['name']) . "',
                    description = '" . $this->db->escape($description['description']) . "'");
            }

            // تحديث حالة النسخة الاحتياطية
            $this->db->query("UPDATE " . DB_PREFIX . "accounts_backup SET
                restored = 1,
                restored_date = NOW()
                WHERE backup_id = '" . (int)$backup_id . "'");

            $this->cache->delete('account');
            return $new_account_id;
        }

        return false;
    }

    /**
     * الحصول على قائمة النسخ الاحتياطية
     */
    public function getAccountBackups($data = array()) {
        $sql = "SELECT ab.*, u.firstname, u.lastname
                FROM " . DB_PREFIX . "accounts_backup ab
                LEFT JOIN " . DB_PREFIX . "user u ON ab.deleted_by = u.user_id
                WHERE 1=1";

        if (!empty($data['filter_account_code'])) {
            $sql .= " AND ab.account_code LIKE '%" . $this->db->escape($data['filter_account_code']) . "%'";
        }

        if (!empty($data['filter_restored'])) {
            $sql .= " AND ab.restored = '" . (int)$data['filter_restored'] . "'";
        }

        $sql .= " ORDER BY ab.deleted_date DESC";

        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }

        $query = $this->db->query($sql);

        return $query->rows;
    }

    /**
     * الحصول على إجمالي عدد النسخ الاحتياطية
     */
    public function getTotalAccountBackups($data = array()) {
        $sql = "SELECT COUNT(*) as total FROM " . DB_PREFIX . "accounts_backup WHERE 1=1";

        if (!empty($data['filter_account_code'])) {
            $sql .= " AND account_code LIKE '%" . $this->db->escape($data['filter_account_code']) . "%'";
        }

        if (!empty($data['filter_restored'])) {
            $sql .= " AND restored = '" . (int)$data['filter_restored'] . "'";
        }

        $query = $this->db->query($sql);

        return $query->row['total'];
    }

    /**
     * الحصول على حساب واحد
     */
    public function getAccount($account_id) {
        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "accounts WHERE account_id = '" . (int)$account_id . "'");
        return $query->row;
    }

    /**
     * الحصول على قائمة الحسابات مع التصفية والترتيب
     */
    public function getAccounts($data = array()) {
        $sql = "SELECT a.*, ad.name,
                       CASE WHEN a.parent_id = 0 THEN a.account_code
                            ELSE CONCAT(pa.account_code, '-', a.account_code) END as full_code
                FROM " . DB_PREFIX . "accounts a
                LEFT JOIN " . DB_PREFIX . "account_description ad ON (a.account_id = ad.account_id)
                LEFT JOIN " . DB_PREFIX . "accounts pa ON (a.parent_id = pa.account_id)
                WHERE ad.language_id = '" . (int)$this->config->get('config_language_id') . "'";

        if (!empty($data['filter_name'])) {
            $sql .= " AND (ad.name LIKE '%" . $this->db->escape($data['filter_name']) . "%'
                     OR a.account_code LIKE '%" . $this->db->escape($data['filter_name']) . "%')";
        }

        if (!empty($data['filter_type'])) {
            $sql .= " AND a.account_type = '" . $this->db->escape($data['filter_type']) . "'";
        }

        if (isset($data['filter_status'])) {
            $sql .= " AND a.status = '" . (int)$data['filter_status'] . "'";
        }

        $sql .= " ORDER BY a.account_code ASC";

        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }
            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }
            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }

        $query = $this->db->query($sql);
        return $query->rows;
    }

    /**
     * الحصول على قائمة الحسابات للقيود المحاسبية
     */
    public function getAccountsForJournal($data = array()) {
        $sql = "SELECT a.account_id, a.account_code, a.account_type, a.status, ad.name
                FROM " . DB_PREFIX . "accounts a
                LEFT JOIN " . DB_PREFIX . "account_description ad ON (a.account_id = ad.account_id)
                WHERE a.status = 1
                AND ad.language_id = '" . (int)$this->config->get('config_language_id') . "'";

        if (!empty($data['filter_name'])) {
            $sql .= " AND (ad.name LIKE '%" . $this->db->escape($data['filter_name']) . "%'
                     OR a.account_code LIKE '%" . $this->db->escape($data['filter_name']) . "%')";
        }

        $sql .= " ORDER BY a.account_code ASC";

        $query = $this->db->query($sql);
        return $query->rows;
    }

    /**
     * الحصول على شجرة الحسابات
     */
    public function getAccountsTree($parent_id = 0, $level = 0) {
        $accounts = array();
        
        $sql = "SELECT a.*, ad.name
                FROM " . DB_PREFIX . "accounts a
                LEFT JOIN " . DB_PREFIX . "account_description ad ON (a.account_id = ad.account_id)
                WHERE a.parent_id = '" . (int)$parent_id . "'
                AND ad.language_id = '" . (int)$this->config->get('config_language_id') . "'
                ORDER BY a.account_code ASC";
        
        $query = $this->db->query($sql);
        
        foreach ($query->rows as $account) {
            $account['level'] = $level;
            $account['children'] = $this->getAccountsTree($account['account_id'], $level + 1);
            $accounts[] = $account;
        }
        
        return $accounts;
    }

    /**
     * الحصول على أوصاف الحساب
     */
    public function getAccountDescriptions($account_id) {
        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "account_description WHERE account_id = '" . (int)$account_id . "'");
        return $query->rows;
    }

    /**
     * الحصول على إجمالي عدد الحسابات
     */
    public function getTotalAccounts($data = array()) {
        $sql = "SELECT COUNT(DISTINCT a.account_id) AS total FROM " . DB_PREFIX . "accounts a
                LEFT JOIN " . DB_PREFIX . "account_description ad ON (a.account_id = ad.account_id)
                WHERE ad.language_id = '" . (int)$this->config->get('config_language_id') . "'";

        if (!empty($data['filter_name'])) {
            $sql .= " AND (ad.name LIKE '%" . $this->db->escape($data['filter_name']) . "%'
                     OR a.account_code LIKE '%" . $this->db->escape($data['filter_name']) . "%')";
        }

        if (!empty($data['filter_type'])) {
            $sql .= " AND a.account_type = '" . $this->db->escape($data['filter_type']) . "'";
        }

        if (isset($data['filter_status'])) {
            $sql .= " AND a.status = '" . (int)$data['filter_status'] . "'";
        }

        $query = $this->db->query($sql);
        return $query->row['total'];
    }

    /**
     * التحقق من صحة بيانات الحساب
     */
    private function validateAccountData($data, $account_id = null) {
        if (empty($data['account_code'])) {
            return false;
        }

        // التحقق من عدم تكرار رقم الحساب
        $sql = "SELECT COUNT(*) as total FROM " . DB_PREFIX . "accounts WHERE account_code = '" . $this->db->escape($data['account_code']) . "'";
        if ($account_id) {
            $sql .= " AND account_id != '" . (int)$account_id . "'";
        }
        
        $query = $this->db->query($sql);
        if ($query->row['total'] > 0) {
            return false;
        }

        return true;
    }

    /**
     * التحقق من إمكانية حذف الحساب
     */
    private function canDeleteAccount($account_id) {
        // التحقق من وجود حسابات فرعية
        $query = $this->db->query("SELECT COUNT(*) as total FROM " . DB_PREFIX . "accounts WHERE parent_id = '" . (int)$account_id . "'");
        if ($query->row['total'] > 0) {
            return false;
        }

        // التحقق من وجود قيود محاسبية مرتبطة
        $account_code_query = $this->db->query("SELECT account_code FROM " . DB_PREFIX . "accounts WHERE account_id = '" . (int)$account_id . "'");
        if ($account_code_query->row) {
            $query = $this->db->query("SELECT COUNT(*) as total FROM " . DB_PREFIX . "journal_entries WHERE account_code = '" . $this->db->escape($account_code_query->row['account_code']) . "'");
        } else {
            $query = (object)array('row' => array('total' => 0));
        }
        if ($query->row['total'] > 0) {
            return false;
        }

        return true;
    }

    /**
     * الحصول على رصيد الحساب
     */
    public function getAccountBalance($account_id, $date = null) {
        $sql = "SELECT 
                    COALESCE(SUM(CASE WHEN je.is_debit = 1 THEN je.amount ELSE 0 END), 0) as total_debit,
                    COALESCE(SUM(CASE WHEN je.is_debit = 0 THEN je.amount ELSE 0 END), 0) as total_credit
                FROM " . DB_PREFIX . "journal_entries je
                LEFT JOIN " . DB_PREFIX . "accounts a ON je.account_code = a.account_code
                WHERE a.account_id = '" . (int)$account_id . "'";
        
        if ($date) {
            $sql .= " AND je.date <= '" . $this->db->escape($date) . "'";
        }
        
        $query = $this->db->query($sql);
        return $query->row;
    }

    /**
     * البحث التلقائي في الحسابات
     */
    public function autocomplete($filter_name) {
        $sql = "SELECT a.account_id, a.account_code, ad.name
                FROM " . DB_PREFIX . "accounts a
                LEFT JOIN " . DB_PREFIX . "account_description ad ON (a.account_id = ad.account_id)
                WHERE (ad.name LIKE '%" . $this->db->escape($filter_name) . "%'
                OR a.account_code LIKE '%" . $this->db->escape($filter_name) . "%')
                AND ad.language_id = '" . (int)$this->config->get('config_language_id') . "'
                AND a.status = 1
                ORDER BY a.account_code ASC
                LIMIT 10";

        $query = $this->db->query($sql);
        return $query->rows;
    }

    /**
     * الحصول على الحسابات حسب النوع
     */
    public function getAccountsByType($account_type) {
        $sql = "SELECT a.*, ad.name
                FROM " . DB_PREFIX . "accounts a
                LEFT JOIN " . DB_PREFIX . "account_description ad ON (a.account_id = ad.account_id)
                WHERE a.account_type = '" . $this->db->escape($account_type) . "'
                AND a.status = 1
                AND ad.language_id = '" . (int)$this->config->get('config_language_id') . "'
                ORDER BY a.account_code ASC";

        $query = $this->db->query($sql);
        return $query->rows;
    }

    /**
     * التحقق المتقدم من صحة البيانات
     */
    private function validateAccountDataAdvanced($data, $account_id = null) {
        $errors = array();

        // التحقق من وجود اسم الحساب
        if (empty($data['account_description'])) {
            $errors[] = 'يجب إدخال اسم الحساب';
        } else {
            $has_name = false;
            foreach ($data['account_description'] as $language_id => $value) {
                if (!empty($value['name'])) {
                    $has_name = true;
                    break;
                }
            }
            if (!$has_name) {
                $errors[] = 'يجب إدخال اسم الحساب على الأقل بلغة واحدة';
            }
        }

        // التحقق من نوع الحساب
        if (empty($data['account_type']) || !in_array($data['account_type'], array('debit', 'credit'))) {
            $errors[] = 'يجب تحديد نوع الحساب (مدين/دائن)';
        }

        // التحقق من رقم الحساب
        if (!empty($data['account_code'])) {
            if (!is_numeric($data['account_code'])) {
                $errors[] = 'رقم الحساب يجب أن يكون رقماً';
            }
            
            // التحقق من عدم تكرار رقم الحساب
            $sql = "SELECT COUNT(*) as total FROM " . DB_PREFIX . "accounts WHERE account_code = '" . $this->db->escape($data['account_code']) . "'";
            if ($account_id) {
                $sql .= " AND account_id != '" . (int)$account_id . "'";
            }
            
            $query = $this->db->query($sql);
            if ($query->row['total'] > 0) {
                $errors[] = 'رقم الحساب مستخدم بالفعل';
            }
        }

        // التحقق من الحساب الأب
        if (!empty($data['parent_id'])) {
            $query = $this->db->query("SELECT COUNT(*) as total FROM " . DB_PREFIX . "accounts WHERE account_id = '" . (int)$data['parent_id'] . "'");
            if ($query->row['total'] == 0) {
                $errors[] = 'الحساب الأب غير موجود';
            }
        }

        return array(
            'valid' => empty($errors),
            'error' => implode(', ', $errors)
        );
    }

    /**
     * توليد رقم حساب تلقائي
     */
    private function generateAccountCode($account_type, $parent_id = null) {
        $prefix = ($account_type == 'debit') ? '1' : '2';
        
        if ($parent_id) {
            $query = $this->db->query("SELECT account_code FROM " . DB_PREFIX . "accounts WHERE account_id = '" . (int)$parent_id . "'");
            if ($query->row) {
                $parent_code = $query->row['account_code'];
                $sql = "SELECT MAX(CAST(SUBSTRING(account_code, " . (strlen($parent_code) + 1) . ") AS UNSIGNED)) as max_code
                        FROM " . DB_PREFIX . "accounts 
                        WHERE parent_id = '" . (int)$parent_id . "'
                        AND account_code LIKE '" . $this->db->escape($parent_code) . "%'";
                $query = $this->db->query($sql);
                $next_code = ($query->row['max_code'] ?? 0) + 1;
                return $parent_code . str_pad($next_code, 2, '0', STR_PAD_LEFT);
            }
        }
        
        // إذا لم يكن هناك حساب أب، ابحث عن أكبر رقم في المستوى الأول
        $sql = "SELECT MAX(CAST(account_code AS UNSIGNED)) as max_code
                FROM " . DB_PREFIX . "accounts 
                WHERE parent_id = 0
                AND account_code LIKE '" . $this->db->escape($prefix) . "%'";
        $query = $this->db->query($sql);
        $next_code = ($query->row['max_code'] ?? 0) + 1;
        
        return $next_code;
    }

    /**
     * إنشاء قيد الرصيد الافتتاحي
     */
    private function createOpeningBalanceEntry($account_id, $data) {
        // البحث عن حساب الأرباح والخسائر المرحلة
        $query = $this->db->query("SELECT account_id FROM " . DB_PREFIX . "accounts WHERE account_code = '397'");
        if (!$query->row) {
            return false;
        }
        
        $retained_earnings_account = $query->row['account_id'];
        $amount = abs($data['opening_balance']);
        
        // إنشاء قيد الرصيد الافتتاحي
        $this->db->query("INSERT INTO " . DB_PREFIX . "journal_entries SET
            date = NOW(),
            reference = 'رصيد افتتاحي',
            description = 'رصيد افتتاحي للحساب',
            created_by = '" . (int)$this->user->getId() . "',
            date_added = NOW()");
        
        $journal_id = $this->db->getLastId();
        
        // إضافة تفاصيل القيد
        if ($data['opening_balance'] > 0) {
            // رصيد مدين
            $this->db->query("INSERT INTO " . DB_PREFIX . "journal_entry_details SET
                journal_id = '" . (int)$journal_id . "',
                account_id = '" . (int)$account_id . "',
                is_debit = 1,
                amount = '" . (float)$amount . "'");
                
            $this->db->query("INSERT INTO " . DB_PREFIX . "journal_entry_details SET
                journal_id = '" . (int)$journal_id . "',
                account_id = '" . (int)$retained_earnings_account . "',
                is_debit = 0,
                amount = '" . (float)$amount . "'");
        } else {
            // رصيد دائن
            $this->db->query("INSERT INTO " . DB_PREFIX . "journal_entry_details SET
                journal_id = '" . (int)$journal_id . "',
                account_id = '" . (int)$account_id . "',
                is_debit = 0,
                amount = '" . (float)$amount . "'");
                
            $this->db->query("INSERT INTO " . DB_PREFIX . "journal_entry_details SET
                journal_id = '" . (int)$journal_id . "',
                account_id = '" . (int)$retained_earnings_account . "',
                is_debit = 1,
                amount = '" . (float)$amount . "'");
        }
        
        return true;
    }

    /**
     * الحصول على قائمة جميع الحسابات
     */
    public function getAllAccountsList() {
        $sql = "SELECT a.account_id, a.account_code, a.account_type, a.status, ad.name
                FROM " . DB_PREFIX . "accounts a
                LEFT JOIN " . DB_PREFIX . "account_description ad ON (a.account_id = ad.account_id)
                WHERE a.status = 1
                AND ad.language_id = '" . (int)$this->config->get('config_language_id') . "'
                ORDER BY a.account_code ASC";

        $query = $this->db->query($sql);
        return $query->rows;
    }

    /**
     * الحصول على الحساب برقم الحساب
     */
    public function getAccountByCode($account_code) {
        $query = $this->db->query("SELECT a.*, ad.name
                                   FROM " . DB_PREFIX . "accounts a
                                   LEFT JOIN " . DB_PREFIX . "account_description ad ON (a.account_id = ad.account_id)
                                   WHERE a.account_code = '" . $this->db->escape($account_code) . "'
                                   AND ad.language_id = '" . (int)$this->config->get('config_language_id') . "'");
        return $query->row;
    }

    /**
     * استيراد من ملف Excel مع التحقق المتقدم
     */
    public function importFromExcel($file_path, $validate_data = true) {
        if (!file_exists($file_path)) {
            throw new Exception('Excel file not found');
        }

        // التحقق من امتداد الملف
        $extension = pathinfo($file_path, PATHINFO_EXTENSION);
        if (!in_array(strtolower($extension), ['xlsx', 'xls', 'csv'])) {
            throw new Exception('Invalid file format. Only Excel and CSV files are supported.');
        }

        $imported_accounts = array();
        $errors = array();

        // قراءة ملف CSV بسيط (يمكن تطويره لاحقاً لدعم Excel)
        if (strtolower($extension) === 'csv') {
            $handle = fopen($file_path, 'r');
            if ($handle !== FALSE) {
                $row = 0;
                while (($data = fgetcsv($handle, 1000, ",")) !== FALSE) {
                    $row++;

                    // تخطي الصف الأول (العناوين)
                    if ($row === 1) continue;

                    // التحقق من البيانات
                    if ($validate_data) {
                        $validation_result = $this->validateImportRow($data, $row);
                        if (!$validation_result['valid']) {
                            $errors[] = "Row {$row}: " . implode(', ', $validation_result['errors']);
                            continue;
                        }
                    }

                    // إنشاء الحساب
                    try {
                        $account_data = array(
                            'account_code' => trim($data[0]),
                            'parent_id' => (int)trim($data[1]),
                            'account_type' => trim($data[2]),
                            'account_nature' => trim($data[3]),
                            'is_active' => isset($data[4]) ? (int)trim($data[4]) : 1,
                            'sort_order' => isset($data[5]) ? (int)trim($data[5]) : 0,
                            'account_description' => array(
                                1 => array( // Arabic
                                    'name' => isset($data[6]) ? trim($data[6]) : '',
                                    'description' => isset($data[7]) ? trim($data[7]) : ''
                                ),
                                2 => array( // English
                                    'name' => isset($data[8]) ? trim($data[8]) : '',
                                    'description' => isset($data[9]) ? trim($data[9]) : ''
                                )
                            )
                        );

                        $account_id = $this->addAccount($account_data);
                        $imported_accounts[] = $account_id;

                    } catch (Exception $e) {
                        $errors[] = "Row {$row}: " . $e->getMessage();
                    }
                }
                fclose($handle);
            }
        }

        return array(
            'imported' => count($imported_accounts),
            'errors' => $errors,
            'account_ids' => $imported_accounts
        );
    }

    /**
     * التحقق من صحة بيانات صف الاستيراد
     */
    private function validateImportRow($data, $row_number) {
        $errors = array();
        $valid = true;

        // التحقق من رمز الحساب
        if (empty(trim($data[0]))) {
            $errors[] = 'Account code is required';
            $valid = false;
        } elseif (strlen(trim($data[0])) > 20) {
            $errors[] = 'Account code too long (max 20 characters)';
            $valid = false;
        } elseif ($this->checkAccountCodeExists(trim($data[0]))) {
            $errors[] = 'Account code already exists';
            $valid = false;
        }

        // التحقق من نوع الحساب
        $valid_types = array('asset', 'liability', 'equity', 'revenue', 'expense');
        if (!empty(trim($data[2])) && !in_array(trim($data[2]), $valid_types)) {
            $errors[] = 'Invalid account type';
            $valid = false;
        }

        // التحقق من طبيعة الحساب
        $valid_natures = array('debit', 'credit');
        if (!empty(trim($data[3])) && !in_array(trim($data[3]), $valid_natures)) {
            $errors[] = 'Invalid account nature';
            $valid = false;
        }

        // التحقق من الحساب الأب
        if (!empty(trim($data[1]))) {
            $parent_id = (int)trim($data[1]);
            if ($parent_id > 0 && !$this->checkAccountExists($parent_id)) {
                $errors[] = 'Parent account does not exist';
                $valid = false;
            }
        }

        return array(
            'valid' => $valid,
            'errors' => $errors
        );
    }

    /**
     * التحقق من وجود رمز الحساب
     */
    private function checkAccountCodeExists($account_code) {
        $query = $this->db->query("SELECT COUNT(*) as total FROM " . DB_PREFIX . "accounts
            WHERE account_code = '" . $this->db->escape($account_code) . "'");

        return $query->row['total'] > 0;
    }

    /**
     * التحقق من وجود الحساب
     */
    private function checkAccountExists($account_id) {
        $query = $this->db->query("SELECT COUNT(*) as total FROM " . DB_PREFIX . "accounts
            WHERE account_id = '" . (int)$account_id . "'");

        return $query->row['total'] > 0;
    }

    // تحسين دليل الحسابات مع التخزين المؤقت
    public function getOptimizedAccounts($filter_data = array()) {
        $cache_key = 'chart_accounts_' . md5(serialize($filter_data));

        $cached_result = $this->cache->get($cache_key);
        if ($cached_result !== false) {
            return $cached_result;
        }

        $result = $this->getAccounts($filter_data);
        $this->cache->set($cache_key, $result, 1800);

        return $result;
    }

    // تحليل متقدم لدليل الحسابات
    public function getEnhancedChartAnalysis() {
        $cache_key = 'chart_analysis_' . date('Y-m-d');

        $cached_result = $this->cache->get($cache_key);
        if ($cached_result !== false) {
            return $cached_result;
        }

        $analysis = array();

        // تحليل الحسابات حسب النوع
        $type_query = $this->db->query("
            SELECT
                a.account_type,
                COUNT(a.account_id) as count,
                COUNT(CASE WHEN a.status = 1 THEN 1 END) as active_count,
                COUNT(CASE WHEN a.status = 0 THEN 1 END) as inactive_count
            FROM " . DB_PREFIX . "accounts a
            GROUP BY a.account_type
            ORDER BY count DESC
        ");

        $analysis['by_type'] = $type_query->rows;

        // تحليل الحسابات الأكثر استخداماً
        $usage_query = $this->db->query("
            SELECT
                a.account_code,
                ad.name,
                COUNT(je.entry_id) as usage_count,
                SUM(je.amount) as total_amount
            FROM " . DB_PREFIX . "accounts a
            LEFT JOIN " . DB_PREFIX . "account_description ad ON a.account_id = ad.account_id
            LEFT JOIN " . DB_PREFIX . "journal_entries je ON a.account_code = je.account_code
            WHERE a.status = 1
            GROUP BY a.account_id
            HAVING usage_count > 0
            ORDER BY usage_count DESC
            LIMIT 20
        ");

        $analysis['most_used'] = $usage_query->rows;

        // تحليل الحسابات غير المستخدمة
        $unused_query = $this->db->query("
            SELECT
                a.account_code,
                ad.name,
                a.date_added
            FROM " . DB_PREFIX . "accounts a
            LEFT JOIN " . DB_PREFIX . "account_description ad ON a.account_id = ad.account_id
            LEFT JOIN " . DB_PREFIX . "journal_entries je ON a.account_code = je.account_code
            WHERE a.status = 1 AND je.entry_id IS NULL
            ORDER BY a.date_added DESC
            LIMIT 20
        ");

        $analysis['unused'] = $unused_query->rows;

        $this->cache->set($cache_key, $analysis, 3600);

        return $analysis;
    }

    // البحث الذكي في دليل الحسابات
    public function getSmartAccountSearch($search_term, $limit = 20) {
        $cache_key = 'account_search_' . md5($search_term . '_' . $limit);

        $cached_result = $this->cache->get($cache_key);
        if ($cached_result !== false) {
            return $cached_result;
        }

        $search_term = $this->db->escape($search_term);

        $query = $this->db->query("
            SELECT
                a.account_id,
                a.account_code,
                ad.name,
                a.account_type,
                a.status,
                a.opening_balance
            FROM " . DB_PREFIX . "accounts a
            LEFT JOIN " . DB_PREFIX . "account_description ad ON a.account_id = ad.account_id
            WHERE (
                a.account_code LIKE '%" . $search_term . "%'
                OR ad.name LIKE '%" . $search_term . "%'
            )
            ORDER BY a.account_code ASC
            LIMIT " . (int)$limit
        );

        $results = $query->rows;

        $this->cache->set($cache_key, $results, 1800);

        return $results;
    }
}
