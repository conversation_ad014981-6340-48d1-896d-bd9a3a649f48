<?php
/**
 * إدارة سجل حركة المخزون المتطور المحسن (Enhanced Advanced Stock Movement Ledger Controller)
 * 
 * التحسينات الجديدة:
 * - تطبيق الخدمات المركزية (central_service_manager.php)
 * - نظام الصلاحيات المزدوج (hasPermission + hasKey)
 * - تسجيل شامل للأنشطة (logActivity)
 * - نظام الإشعارات المتقدم (sendNotification)
 * - معالجة أخطاء متقدمة (try-catch شامل)
 * - استخدام الإعدادات المركزية ($this->config->get)
 * - التكامل مع المخزون الوهمي
 * - واجهة محسنة مع Chart.js و DataTables
 * 
 * <AUTHOR> Team - Enhanced by Kiro AI
 * @version 2.0 - Enterprise Grade
 * @since 2025
 * @reference journal.php (957 lines) - Proven Example
 */

class ControllerInventoryStockMovement extends Controller {
    
    private $error = array();
    private $central_service;
    
    public function __construct($registry) {
        parent::__construct($registry);
        
        // تحميل الخدمات المركزية الخمس - Enterprise Grade Plus
        $this->load->model('core/central_service_manager');
        $this->central_service = $this->model_core_central_service_manager;
        
        // تحميل النماذج المطلوبة
        $this->load->model('inventory/stock_movement_enhanced');
        $this->load->model('inventory/warehouse');
        $this->load->model('inventory/category');
        $this->load->model('inventory/manufacturer');
        $this->load->model('user/user');
        
        // تحديد الصلاحيات المطلوبة
        $this->permissions = array(
            'access' => 'inventory/stock_movement',
            'export' => 'inventory/stock_movement',
            'view_cost' => 'inventory/stock_movement'
        );
        
        // تحميل ملفات اللغة
        $this->load->language('inventory/stock_movement');
        $this->load->language('common/header');
    }
    
    public function index() {
        try {
            // تحميل اللغة
            $this->load->language('inventory/stock_movement');
            
            // التحقق من الصلاحيات الأساسية
            if (!$this->user->hasPermission('access', 'inventory/stock_movement')) {
                $this->central_service->logActivity(
                    'access_denied',
                    'stock_movement',
                    'محاولة وصول غير مصرح بها لسجل حركة المخزون',
                    array('user_id' => $this->user->getId(), 'ip' => $this->request->server['REMOTE_ADDR'])
                );
                
                $this->session->data['error'] = $this->language->get('error_permission');
                $this->response->redirect($this->url->link('error/permission', 'user_token=' . $this->session->data['user_token'], true));
            }
            
            // تسجيل النشاط
            $this->central_service->logActivity(
                'view',
                'stock_movement',
                'عرض سجل حركة المخزون',
                array('user_id' => $this->user->getId())
            );
            
            // تحديد عنوان الصفحة
            $this->document->setTitle($this->language->get('heading_title'));
            
            // تحميل النماذج المطلوبة
            $this->load->model('inventory/stock_movement');
            $this->load->model('inventory/category');
            $this->load->model('inventory/manufacturer');
            $this->load->model('inventory/branch');
            $this->load->model('user/user');
            
            // معالجة الطلبات
            $this->getList();
            
        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'stock_movement',
                'خطأ في عرض سجل حركة المخزون: ' . $e->getMessage(),
                array('user_id' => $this->user->getId(), 'error' => $e->getTraceAsString())
            );
            
            $this->session->data['error'] = 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.';
            $this->response->redirect($this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true));
        }
    }  
  
    protected function getList() {
        try {
            // معالجة الفلاتر
            $filter_data = $this->getFilters();
            
            // التحقق من الصلاحيات المتقدمة
            $can_view_cost = $this->central_service->hasKey('inventory_view_cost');
            $can_view_all_branches = $this->central_service->hasKey('inventory_view_all_branches');
            
            // تطبيق قيود الفروع إذا لزم الأمر
            if (!$can_view_all_branches) {
                $user_branches = $this->central_service->getUserBranches($this->user->getId());
                if (!empty($user_branches)) {
                    $filter_data['filter_branch_id'] = implode(',', $user_branches);
                }
            }
            
            // إعداد الروابط
            $url = $this->buildUrl($filter_data);
            
            // إعداد البيانات الأساسية
            $data['breadcrumbs'] = array();
            
            $data['breadcrumbs'][] = array(
                'text' => $this->language->get('text_home'),
                'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
            );
            
            $data['breadcrumbs'][] = array(
                'text' => $this->language->get('heading_title'),
                'href' => $this->url->link('inventory/stock_movement', 'user_token=' . $this->session->data['user_token'] . $url, true)
            );
            
            // روابط الإجراءات مع التحقق من الصلاحيات
            $data['can_export'] = $this->user->hasPermission('export', 'inventory/stock_movement');
            if ($data['can_export']) {
                $data['export_excel'] = $this->url->link('inventory/stock_movement/exportExcel', 'user_token=' . $this->session->data['user_token'] . $url, true);
                $data['export_pdf'] = $this->url->link('inventory/stock_movement/exportPdf', 'user_token=' . $this->session->data['user_token'] . $url, true);
                $data['print'] = $this->url->link('inventory/stock_movement/print', 'user_token=' . $this->session->data['user_token'] . $url, true);
            }
            
            $data['refresh'] = $this->url->link('inventory/stock_movement', 'user_token=' . $this->session->data['user_token'], true);
            $data['lot_report'] = $this->url->link('inventory/stock_movement/lotReport', 'user_token=' . $this->session->data['user_token'] . $url, true);
            $data['expiring_lots'] = $this->url->link('inventory/stock_movement/expiringLots', 'user_token=' . $this->session->data['user_token'], true);
            
            // الحصول على البيانات
            $stock_movements = array();
            $filter_data_with_pagination = $filter_data;
            $filter_data_with_pagination['start'] = ($filter_data['page'] - 1) * $this->config->get('config_limit_admin');
            $filter_data_with_pagination['limit'] = $this->config->get('config_limit_admin');
            
            $results = $this->model_inventory_stock_movement->getStockMovements($filter_data_with_pagination);
            $total = $this->model_inventory_stock_movement->getTotalStockMovements($filter_data);
            
            foreach ($results as $result) {
                $movement_data = array(
                    'movement_id'           => $result['movement_id'],
                    'product_id'            => $result['product_id'],
                    'product_name'          => $result['product_name'],
                    'model'                 => $result['model'],
                    'sku'                   => $result['sku'],
                    'category_name'         => $result['category_name'],
                    'manufacturer_name'     => $result['manufacturer_name'],
                    'branch_name'           => $result['branch_name'],
                    'branch_type'           => $this->language->get('text_branch_type_' . $result['branch_type']),
                    'movement_type'         => $result['movement_type'],
                    'movement_type_text'    => $result['movement_type_text'],
                    'movement_type_class'   => $this->getMovementTypeClass($result['movement_type']),
                    'reference_type'        => $result['reference_type'],
                    'reference_type_text'   => $result['reference_type_text'],
                    'reference_id'          => $result['reference_id'],
                    'reference_number'      => $result['reference_number'],
                    'lot_number'            => $result['lot_number'],
                    'expiry_date'           => $result['expiry_date'] ? date($this->language->get('date_format_short'), strtotime($result['expiry_date'])) : '',
                    'expiry_date_raw'       => $result['expiry_date'],
                    'expiry_status'         => $this->getExpiryStatus($result['expiry_date']),
                    'unit_name'             => $result['unit_name'],
                    'unit_symbol'           => $result['unit_symbol'],
                    'quantity_in'           => $result['quantity_in'] > 0 ? number_format($result['quantity_in'], 2) : '',
                    'quantity_in_raw'       => $result['quantity_in'],
                    'quantity_out'          => $result['quantity_out'] > 0 ? number_format($result['quantity_out'], 2) : '',
                    'quantity_out_raw'      => $result['quantity_out'],
                    'net_quantity'          => number_format($result['net_quantity'], 2),
                    'net_quantity_raw'      => $result['net_quantity'],
                    'running_balance'       => number_format($result['running_balance'], 2),
                    'running_balance_raw'   => $result['running_balance'],
                    'notes'                 => $result['notes'],
                    'user_name'             => $result['user_name'],
                    'date_added'            => date($this->language->get('datetime_format'), strtotime($result['date_added'])),
                    'date_added_raw'        => $result['date_added'],
                    'view_reference'        => $this->getViewReferenceLink($result['reference_type'], $result['reference_id']),
                    'product_card'          => $this->url->link('inventory/stock_movement/productCard', 'user_token=' . $this->session->data['user_token'] . '&product_id=' . $result['product_id'] . '&branch_id=' . $result['branch_id'], true)
                );
                
                // إضافة معلومات التكلفة فقط للمصرح لهم
                if ($can_view_cost) {
                    $movement_data['unit_cost'] = $this->currency->format($result['unit_cost'], $this->config->get('config_currency'));
                    $movement_data['unit_cost_raw'] = $result['unit_cost'];
                    $movement_data['total_cost'] = $this->currency->format($result['total_cost'], $this->config->get('config_currency'));
                    $movement_data['total_cost_raw'] = $result['total_cost'];
                    $movement_data['average_cost_before'] = $this->currency->format($result['average_cost_before'], $this->config->get('config_currency'));
                    $movement_data['average_cost_after'] = $this->currency->format($result['average_cost_after'], $this->config->get('config_currency'));
                    $movement_data['cost_change'] = $result['average_cost_after'] - $result['average_cost_before'];
                    $movement_data['cost_change_formatted'] = $this->currency->format($result['average_cost_after'] - $result['average_cost_before'], $this->config->get('config_currency'));
                } else {
                    $movement_data['unit_cost'] = '***';
                    $movement_data['total_cost'] = '***';
                    $movement_data['average_cost_before'] = '***';
                    $movement_data['average_cost_after'] = '***';
                    $movement_data['cost_change_formatted'] = '***';
                }
                
                $stock_movements[] = $movement_data;
            }
            
            $data['stock_movements'] = $stock_movements;
            $data['can_view_cost'] = $can_view_cost;
            
            // الحصول على ملخص الحركات
            $summary = $this->model_inventory_stock_movement->getMovementSummary($filter_data);
            $data['summary'] = array(
                'total_movements'       => number_format($summary['total_movements']),
                'total_products'        => number_format($summary['total_products']),
                'total_branches'        => number_format($summary['total_branches']),
                'total_quantity_in'     => number_format($summary['total_quantity_in'], 2),
                'total_quantity_out'    => number_format($summary['total_quantity_out'], 2),
                'net_quantity'          => number_format($summary['total_quantity_in'] - $summary['total_quantity_out'], 2),
                'total_value'           => $can_view_cost ? $this->currency->format($summary['total_value'], $this->config->get('config_currency')) : '***',
                'avg_unit_cost'         => $can_view_cost ? $this->currency->format($summary['avg_unit_cost'], $this->config->get('config_currency')) : '***',
                'total_lots'            => number_format($summary['total_lots']),
                'movements_with_expiry' => number_format($summary['movements_with_expiry'])
            );
            
            // التحقق من وجود مشاكل في المخزون وإرسال إشعارات
            $this->checkInventoryIssues($filter_data);
            
            // باقي الكود...
            $this->setupFiltersForDisplay($data, $filter_data);
            $this->setupPagination($data, $total, $filter_data, $url);
            $this->setupMessages($data);
            
            $data['header'] = $this->load->controller('common/header');
            $data['column_left'] = $this->load->controller('common/column_left');
            $data['footer'] = $this->load->controller('common/footer');
            
            $this->response->setOutput($this->load->view('inventory/stock_movement_list_enhanced', $data));
            
        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'stock_movement',
                'خطأ في جلب قائمة حركات المخزون: ' . $e->getMessage(),
                array('user_id' => $this->user->getId(), 'error' => $e->getTraceAsString())
            );
            
            $this->session->data['error'] = 'حدث خطأ في جلب البيانات. يرجى المحاولة مرة أخرى.';
            $this->response->redirect($this->url->link('inventory/stock_movement', 'user_token=' . $this->session->data['user_token'], true));
        }
    }    

    /**
     * التحقق من مشاكل المخزون وإرسال إشعارات
     */
    private function checkInventoryIssues($filter_data) {
        try {
            // التحقق من الدفعات منتهية الصلاحية
            $expiring_lots = $this->model_inventory_stock_movement->getExpiringLots(
                $this->config->get('config_expiry_alert_days') ?: 30, 
                $filter_data
            );
            
            if (!empty($expiring_lots)) {
                $this->central_service->sendNotification(
                    'expiry_alert',
                    'تنبيه: يوجد ' . count($expiring_lots) . ' دفعة منتهية الصلاحية قريباً',
                    array(
                        'lots_count' => count($expiring_lots),
                        'lots' => array_slice($expiring_lots, 0, 5) // أول 5 دفعات فقط
                    ),
                    array('inventory_managers', 'warehouse_managers')
                );
            }
            
            // التحقق من تضارب الأرصدة
            if ($this->config->get('config_inventory_balance_check')) {
                $discrepancies = $this->model_inventory_stock_movement->validateInventoryBalances();
                
                if (!empty($discrepancies)) {
                    $this->central_service->sendNotification(
                        'inventory_discrepancy',
                        'تحذير: تم اكتشاف تضارب في أرصدة المخزون',
                        array(
                            'discrepancies_count' => count($discrepancies),
                            'discrepancies' => array_slice($discrepancies, 0, 3)
                        ),
                        array('inventory_managers', 'system_admins')
                    );
                }
            }
            
        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'stock_movement',
                'خطأ في فحص مشاكل المخزون: ' . $e->getMessage(),
                array('user_id' => $this->user->getId())
            );
        }
    }
    
    /**
     * إعداد الرسائل للعرض
     */
    private function setupMessages(&$data) {
        // رسائل النجاح والخطأ
        if (isset($this->session->data['success'])) {
            $data['success'] = $this->session->data['success'];
            unset($this->session->data['success']);
        } else {
            $data['success'] = '';
        }
        
        if (isset($this->error['warning'])) {
            $data['error_warning'] = $this->error['warning'];
        } else {
            $data['error_warning'] = '';
        }
        
        $data['user_token'] = $this->session->data['user_token'];
    }
    
    /**
     * إعداد الترقيم
     */
    private function setupPagination(&$data, $total, $filter_data, $url) {
        $pagination = new Pagination();
        $pagination->total = $total;
        $pagination->page = $filter_data['page'];
        $pagination->limit = $this->config->get('config_limit_admin');
        $pagination->url = $this->url->link('inventory/stock_movement', 'user_token=' . $this->session->data['user_token'] . $url . '&page={page}', true);
        
        $data['pagination'] = $pagination->render();
        $data['results'] = sprintf($this->language->get('text_pagination'), ($total) ? (($filter_data['page'] - 1) * $this->config->get('config_limit_admin')) + 1 : 0, ((($filter_data['page'] - 1) * $this->config->get('config_limit_admin')) > ($total - $this->config->get('config_limit_admin'))) ? $total : ((($filter_data['page'] - 1) * $this->config->get('config_limit_admin')) + $this->config->get('config_limit_admin')), $total, ceil($total / $this->config->get('config_limit_admin')));
        
        // إعداد الترتيب
        $data['sort'] = $filter_data['sort'];
        $data['order'] = $filter_data['order'];
    }
    
    /**
     * عرض كارت الصنف لمنتج محدد - محسن
     */
    public function productCard() {
        try {
            // التحقق من الصلاحيات
            if (!$this->user->hasPermission('access', 'inventory/stock_movement')) {
                $this->session->data['error'] = $this->language->get('error_permission');
                $this->response->redirect($this->url->link('error/permission', 'user_token=' . $this->session->data['user_token'], true));
            }
            
            $this->load->language('inventory/stock_movement');
            $this->load->model('inventory/stock_movement');
            
            $product_id = isset($this->request->get['product_id']) ? (int)$this->request->get['product_id'] : 0;
            $branch_id = isset($this->request->get['branch_id']) ? (int)$this->request->get['branch_id'] : 0;
            
            if (!$product_id) {
                $this->session->data['error'] = $this->language->get('error_product_required');
                $this->response->redirect($this->url->link('inventory/stock_movement', 'user_token=' . $this->session->data['user_token'], true));
            }
            
            // تسجيل النشاط
            $this->central_service->logActivity(
                'view',
                'stock_movement_card',
                'عرض كارت الصنف للمنتج: ' . $product_id,
                array('product_id' => $product_id, 'branch_id' => $branch_id)
            );
            
            $filter_data = $this->getFilters();
            $movements = $this->model_inventory_stock_movement->getProductCard($product_id, $branch_id, $filter_data);
            
            $data['movements'] = $movements;
            $data['product_id'] = $product_id;
            $data['branch_id'] = $branch_id;
            $data['can_view_cost'] = $this->central_service->hasKey('inventory_view_cost');
            
            $this->response->setOutput($this->load->view('inventory/stock_movement_card_enhanced', $data));
            
        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'stock_movement_card',
                'خطأ في عرض كارت الصنف: ' . $e->getMessage(),
                array('product_id' => $product_id, 'branch_id' => $branch_id, 'error' => $e->getTraceAsString())
            );
            
            $this->session->data['error'] = 'حدث خطأ في عرض كارت الصنف. يرجى المحاولة مرة أخرى.';
            $this->response->redirect($this->url->link('inventory/stock_movement', 'user_token=' . $this->session->data['user_token'], true));
        }
    }
    
    /**
     * تصدير إلى Excel - محسن
     */
    public function exportExcel() {
        try {
            // التحقق من الصلاحيات
            if (!$this->user->hasPermission('export', 'inventory/stock_movement')) {
                $this->session->data['error'] = $this->language->get('error_permission');
                $this->response->redirect($this->url->link('inventory/stock_movement', 'user_token=' . $this->session->data['user_token'], true));
            }
            
            $this->load->language('inventory/stock_movement');
            $this->load->model('inventory/stock_movement');
            
            $filter_data = $this->getFilters();
            $results = $this->model_inventory_stock_movement->exportToExcel($filter_data);
            
            // تسجيل النشاط
            $this->central_service->logActivity(
                'export',
                'stock_movement',
                'تصدير سجل حركة المخزون إلى Excel',
                array('records_count' => count($results), 'filters' => $filter_data)
            );
            
            // إنشاء ملف Excel
            $filename = 'stock_movements_' . date('Y-m-d_H-i-s') . '.csv';
            
            header('Content-Type: application/csv');
            header('Content-Disposition: attachment; filename="' . $filename . '"');
            header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
            header('Pragma: public');
            
            $output = fopen('php://output', 'w');
            
            // كتابة العناوين
            $headers = array(
                $this->language->get('column_date'),
                $this->language->get('column_product_name'),
                $this->language->get('column_branch'),
                $this->language->get('column_movement_type'),
                $this->language->get('column_reference'),
                $this->language->get('column_lot_number'),
                $this->language->get('column_quantity_in'),
                $this->language->get('column_quantity_out'),
                $this->language->get('column_running_balance'),
                $this->language->get('column_user'),
                $this->language->get('column_notes')
            );
            
            // إضافة أعمدة التكلفة للمصرح لهم
            if ($this->central_service->hasKey('inventory_view_cost')) {
                $headers[] = $this->language->get('column_unit_cost');
                $headers[] = $this->language->get('column_total_cost');
            }
            
            fputcsv($output, $headers);
            
            // كتابة البيانات
            foreach ($results as $result) {
                $row = array(
                    $result['date_added'],
                    $result['product_name'],
                    $result['branch_name'],
                    $result['movement_type_text'],
                    $result['reference_number'],
                    $result['lot_number'],
                    $result['quantity_in'],
                    $result['quantity_out'],
                    $result['running_balance'],
                    $result['user_name'],
                    $result['notes']
                );
                
                if ($this->central_service->hasKey('inventory_view_cost')) {
                    $row[] = $result['unit_cost'];
                    $row[] = $result['total_cost'];
                }
                
                fputcsv($output, $row);
            }
            
            fclose($output);
            exit;
            
        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'stock_movement_export',
                'خطأ في تصدير سجل حركة المخزون: ' . $e->getMessage(),
                array('user_id' => $this->user->getId(), 'error' => $e->getTraceAsString())
            );
            
            $this->session->data['error'] = 'حدث خطأ في التصدير. يرجى المحاولة مرة أخرى.';
            $this->response->redirect($this->url->link('inventory/stock_movement', 'user_token=' . $this->session->data['user_token'], true));
        }
    }    

    /**
     * معالجة الفلاتر - محسن
     */
    private function getFilters() {
        $filters = array(
            'filter_product_id'       => '',
            'filter_product_name'     => '',
            'filter_category_id'      => '',
            'filter_manufacturer_id'  => '',
            'filter_branch_id'        => '',
            'filter_branch_type'      => '',
            'filter_movement_type'    => '',
            'filter_reference_type'   => '',
            'filter_reference_number' => '',
            'filter_lot_number'       => '',
            'filter_user_id'          => '',
            'filter_date_from'        => '',
            'filter_date_to'          => '',
            'filter_has_expiry'       => '',
            'filter_expiry_from'      => '',
            'filter_expiry_to'        => '',
            'sort'                    => 'pm.date_added',
            'order'                   => 'DESC',
            'page'                    => 1
        );
        
        foreach ($filters as $key => $default) {
            if (isset($this->request->get[$key])) {
                $filters[$key] = $this->request->get[$key];
            }
        }
        
        // تطبيق الإعدادات الافتراضية من الكونفيج
        if (empty($filters['filter_date_from']) && $this->config->get('config_default_date_range')) {
            $filters['filter_date_from'] = date('Y-m-d', strtotime('-' . $this->config->get('config_default_date_range') . ' days'));
        }
        
        return $filters;
    }
    
    /**
     * بناء رابط URL مع الفلاتر
     */
    private function buildUrl($filters) {
        $url = '';
        
        foreach ($filters as $key => $value) {
            if ($value !== '' && $key !== 'page') {
                $url .= '&' . $key . '=' . urlencode(html_entity_decode($value, ENT_QUOTES, 'UTF-8'));
            }
        }
        
        return $url;
    }
    
    /**
     * إعداد الفلاتر للعرض - محسن
     */
    private function setupFiltersForDisplay(&$data, $filters) {
        // نسخ الفلاتر للعرض
        foreach ($filters as $key => $value) {
            $data[$key] = $value;
        }
        
        // الحصول على قوائم الفلاتر
        $data['categories'] = $this->model_inventory_category->getCategories();
        $data['manufacturers'] = $this->model_inventory_manufacturer->getManufacturers();
        
        // تطبيق قيود الفروع حسب الصلاحيات
        if ($this->central_service->hasKey('inventory_view_all_branches')) {
            $data['branches'] = $this->model_inventory_branch->getBranches();
        } else {
            $user_branches = $this->central_service->getUserBranches($this->user->getId());
            $data['branches'] = $this->model_inventory_branch->getBranchesByIds($user_branches);
        }
        
        $data['users'] = $this->model_user_user->getUsers();
        
        // خيارات نوع الحركة
        $movement_types = $this->model_inventory_stock_movement->getMovementTypes();
        $data['movement_type_options'] = array();
        $data['movement_type_options'][] = array('value' => '', 'text' => $this->language->get('text_all'));
        foreach ($movement_types as $key => $value) {
            $data['movement_type_options'][] = array('value' => $key, 'text' => $value);
        }
        
        // خيارات نوع المرجع
        $reference_types = $this->model_inventory_stock_movement->getReferenceTypes();
        $data['reference_type_options'] = array();
        $data['reference_type_options'][] = array('value' => '', 'text' => $this->language->get('text_all'));
        foreach ($reference_types as $key => $value) {
            $data['reference_type_options'][] = array('value' => $key, 'text' => $value);
        }
        
        // خيارات نوع الفرع
        $data['branch_type_options'] = array(
            array('value' => '', 'text' => $this->language->get('text_all')),
            array('value' => 'store', 'text' => $this->language->get('text_branch_type_store')),
            array('value' => 'warehouse', 'text' => $this->language->get('text_branch_type_warehouse'))
        );
        
        // خيارات تتبع الصلاحية
        $data['expiry_options'] = array(
            array('value' => '', 'text' => $this->language->get('text_all')),
            array('value' => '1', 'text' => $this->language->get('text_with_expiry')),
            array('value' => '0', 'text' => $this->language->get('text_without_expiry'))
        );
    }
    
    /**
     * الحصول على فئة CSS لنوع الحركة
     */
    private function getMovementTypeClass($type) {
        switch ($type) {
            case 'purchase':
            case 'transfer_in':
            case 'adjustment_in':
            case 'production_in':
            case 'return_in':
            case 'opening_balance':
                return 'success';
            case 'sale':
            case 'transfer_out':
            case 'adjustment_out':
            case 'production_out':
            case 'return_out':
                return 'danger';
            case 'physical_count':
                return 'info';
            default:
                return 'default';
        }
    }
    
    /**
     * الحصول على نص نوع الحركة
     */
    private function getMovementTypeText($type) {
        $types = $this->model_inventory_stock_movement->getMovementTypes();
        return isset($types[$type]) ? $types[$type] : $type;
    }
    
    /**
     * الحصول على حالة انتهاء الصلاحية
     */
    private function getExpiryStatus($expiry_date) {
        if (!$expiry_date) {
            return '';
        }
        
        $days_to_expiry = (strtotime($expiry_date) - time()) / (60 * 60 * 24);
        
        if ($days_to_expiry < 0) {
            return 'expired';
        } elseif ($days_to_expiry <= 7) {
            return 'critical';
        } elseif ($days_to_expiry <= 30) {
            return 'warning';
        } else {
            return 'normal';
        }
    }
    
    /**
     * الحصول على فئة CSS لحالة انتهاء الصلاحية
     */
    private function getExpiryUrgencyClass($days_to_expiry) {
        if ($days_to_expiry < 0) {
            return 'danger';
        } elseif ($days_to_expiry <= 7) {
            return 'danger';
        } elseif ($days_to_expiry <= 30) {
            return 'warning';
        } else {
            return 'success';
        }
    }
    
    /**
     * الحصول على رابط عرض المرجع
     */
    private function getViewReferenceLink($reference_type, $reference_id) {
        switch ($reference_type) {
            case 'purchase_order':
                return $this->url->link('purchase/order/view', 'user_token=' . $this->session->data['user_token'] . '&order_id=' . $reference_id, true);
            case 'sale_order':
                return $this->url->link('sale/order/view', 'user_token=' . $this->session->data['user_token'] . '&order_id=' . $reference_id, true);
            case 'stock_transfer':
                return $this->url->link('inventory/transfer/view', 'user_token=' . $this->session->data['user_token'] . '&transfer_id=' . $reference_id, true);
            case 'stock_adjustment':
                return $this->url->link('inventory/adjustment/view', 'user_token=' . $this->session->data['user_token'] . '&adjustment_id=' . $reference_id, true);
            default:
                return '';
        }
    }
    
    /**
     * تقرير الدفعات منتهية الصلاحية - محسن
     */
    public function expiringLots() {
        try {
            // التحقق من الصلاحيات
            if (!$this->user->hasPermission('access', 'inventory/stock_movement')) {
                $this->session->data['error'] = $this->language->get('error_permission');
                $this->response->redirect($this->url->link('error/permission', 'user_token=' . $this->session->data['user_token'], true));
            }
            
            $this->load->language('inventory/stock_movement');
            $this->load->model('inventory/stock_movement');
            
            $days_ahead = isset($this->request->get['days']) ? (int)$this->request->get['days'] : 
                         ($this->config->get('config_expiry_alert_days') ?: 30);
            
            $filter_data = $this->getFilters();
            $expiring_lots = $this->model_inventory_stock_movement->getExpiringLots($days_ahead, $filter_data);
            
            // تسجيل النشاط
            $this->central_service->logActivity(
                'view',
                'expiring_lots',
                'عرض تقرير الدفعات منتهية الصلاحية',
                array('days_ahead' => $days_ahead, 'lots_count' => count($expiring_lots))
            );
            
            $data['expiring_lots'] = array();
            foreach ($expiring_lots as $lot) {
                $days_to_expiry = (strtotime($lot['expiry_date']) - time()) / (60 * 60 * 24);
                
                $data['expiring_lots'][] = array(
                    'product_name'      => $lot['product_name'],
                    'lot_number'        => $lot['lot_number'],
                    'branch_name'       => $lot['branch_name'],
                    'quantity'          => number_format($lot['quantity'], 2),
                    'unit_name'         => $lot['unit_name'],
                    'expiry_date'       => date($this->language->get('date_format_short'), strtotime($lot['expiry_date'])),
                    'days_to_expiry'    => round($days_to_expiry),
                    'urgency_class'     => $this->getExpiryUrgencyClass($days_to_expiry),
                    'value'             => $this->central_service->hasKey('inventory_view_cost') ? 
                                          $this->currency->format($lot['total_value'], $this->config->get('config_currency')) : '***'
                );
            }
            
            $data['days_ahead'] = $days_ahead;
            $data['total_lots'] = count($expiring_lots);
            $data['can_view_cost'] = $this->central_service->hasKey('inventory_view_cost');
            
            $this->response->setOutput($this->load->view('inventory/expiring_lots_enhanced', $data));
            
        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'expiring_lots',
                'خطأ في عرض الدفعات منتهية الصلاحية: ' . $e->getMessage(),
                array('error' => $e->getTraceAsString())
            );
            
            $this->session->data['error'] = 'حدث خطأ في عرض التقرير. يرجى المحاولة مرة أخرى.';
            $this->response->redirect($this->url->link('inventory/stock_movement', 'user_token=' . $this->session->data['user_token'], true));
        }
    }
    
    /**
     * تقرير الدفعات - محسن
     */
    public function lotReport() {
        try {
            // التحقق من الصلاحيات
            if (!$this->user->hasPermission('access', 'inventory/stock_movement')) {
                $this->session->data['error'] = $this->language->get('error_permission');
                $this->response->redirect($this->url->link('error/permission', 'user_token=' . $this->session->data['user_token'], true));
            }
            
            $this->load->language('inventory/stock_movement');
            $this->load->model('inventory/stock_movement');
            
            $filter_data = $this->getFilters();
            $lot_report = $this->model_inventory_stock_movement->getLotReport($filter_data);
            
            // تسجيل النشاط
            $this->central_service->logActivity(
                'view',
                'lot_report',
                'عرض تقرير الدفعات',
                array('lots_count' => count($lot_report))
            );
            
            $data['lot_report'] = array();
            foreach ($lot_report as $lot) {
                $data['lot_report'][] = array(
                    'product_name'      => $lot['product_name'],
                    'lot_number'        => $lot['lot_number'],
                    'branch_name'       => $lot['branch_name'],
                    'expiry_date'       => $lot['expiry_date'] ? date($this->language->get('date_format_short'), strtotime($lot['expiry_date'])) : '',
                    'total_in'          => number_format($lot['total_in'], 2),
                    'total_out'         => number_format($lot['total_out'], 2),
                    'balance'           => number_format($lot['balance'], 2),
                    'unit_name'         => $lot['unit_name'],
                    'movements_count'   => $lot['movements_count'],
                    'first_movement'    => date($this->language->get('date_format_short'), strtotime($lot['first_movement'])),
                    'last_movement'     => date($this->language->get('date_format_short'), strtotime($lot['last_movement'])),
                    'status'            => $lot['balance'] > 0 ? 'active' : 'depleted'
                );
            }
            
            $data['can_view_cost'] = $this->central_service->hasKey('inventory_view_cost');
            
            $this->response->setOutput($this->load->view('inventory/lot_report_enhanced', $data));
            
        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'lot_report',
                'خطأ في عرض تقرير الدفعات: ' . $e->getMessage(),
                array('error' => $e->getTraceAsString())
            );
            
            $this->session->data['error'] = 'حدث خطأ في عرض التقرير. يرجى المحاولة مرة أخرى.';
            $this->response->redirect($this->url->link('inventory/stock_movement', 'user_token=' . $this->session->data['user_token'], true));
        }
    }
    
    /**
     * طباعة التقرير - محسن
     */
    public function print() {
        try {
            // التحقق من الصلاحيات
            if (!$this->user->hasPermission('access', 'inventory/stock_movement')) {
                $this->session->data['error'] = $this->language->get('error_permission');
                $this->response->redirect($this->url->link('error/permission', 'user_token=' . $this->session->data['user_token'], true));
            }
            
            $this->load->language('inventory/stock_movement');
            $this->load->model('inventory/stock_movement');
            
            $filter_data = $this->getFilters();
            $results = $this->model_inventory_stock_movement->getStockMovements($filter_data);
            
            // تسجيل النشاط
            $this->central_service->logActivity(
                'print',
                'stock_movement',
                'طباعة سجل حركة المخزون',
                array('records_count' => count($results), 'filters' => $filter_data)
            );
            
            $data['stock_movements'] = $results;
            $data['can_view_cost'] = $this->central_service->hasKey('inventory_view_cost');
            $data['company_name'] = $this->config->get('config_name');
            $data['print_date'] = date($this->language->get('datetime_format'));
            $data['user_name'] = $this->user->getUserName();
            
            $this->response->setOutput($this->load->view('inventory/stock_movement_print_enhanced', $data));
            
        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'stock_movement_print',
                'خطأ في طباعة سجل حركة المخزون: ' . $e->getMessage(),
                array('error' => $e->getTraceAsString())
            );
            
            $this->session->data['error'] = 'حدث خطأ في الطباعة. يرجى المحاولة مرة أخرى.';
            $this->response->redirect($this->url->link('inventory/stock_movement', 'user_token=' . $this->session->data['user_token'], true));
        }
    }
    
    /**
     * تصدير إلى PDF - محسن
     */
    public function exportPdf() {
        try {
            // التحقق من الصلاحيات
            if (!$this->user->hasPermission('export', 'inventory/stock_movement')) {
                $this->session->data['error'] = $this->language->get('error_permission');
                $this->response->redirect($this->url->link('inventory/stock_movement', 'user_token=' . $this->session->data['user_token'], true));
            }
            
            $this->load->language('inventory/stock_movement');
            $this->load->model('inventory/stock_movement');
            
            $filter_data = $this->getFilters();
            $results = $this->model_inventory_stock_movement->exportToPdf($filter_data);
            
            // تسجيل النشاط
            $this->central_service->logActivity(
                'export',
                'stock_movement',
                'تصدير سجل حركة المخزون إلى PDF',
                array('records_count' => count($results), 'filters' => $filter_data)
            );
            
            // إنشاء ملف PDF
            require_once(DIR_SYSTEM . 'library/tcpdf/tcpdf.php');
            
            $pdf = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);
            
            // إعداد معلومات المستند
            $pdf->SetCreator('AYM ERP');
            $pdf->SetAuthor($this->user->getUserName());
            $pdf->SetTitle('سجل حركة المخزون');
            $pdf->SetSubject('تقرير حركة المخزون');
            
            // إعداد الخط العربي
            $pdf->SetFont('aealarabiya', '', 12);
            
            // إضافة صفحة
            $pdf->AddPage();
            
            // عنوان التقرير
            $pdf->Cell(0, 15, 'سجل حركة المخزون', 0, 1, 'C');
            $pdf->Cell(0, 10, 'التاريخ: ' . date('Y-m-d H:i:s'), 0, 1, 'C');
            $pdf->Ln(10);
            
            // جدول البيانات
            $html = '<table border="1" cellpadding="4">';
            $html .= '<tr style="background-color:#f0f0f0;">';
            $html .= '<th>التاريخ</th>';
            $html .= '<th>المنتج</th>';
            $html .= '<th>الفرع</th>';
            $html .= '<th>نوع الحركة</th>';
            $html .= '<th>الكمية الداخلة</th>';
            $html .= '<th>الكمية الخارجة</th>';
            $html .= '<th>الرصيد الجاري</th>';
            $html .= '</tr>';
            
            foreach ($results as $result) {
                $html .= '<tr>';
                $html .= '<td>' . date('Y-m-d', strtotime($result['date_added'])) . '</td>';
                $html .= '<td>' . $result['product_name'] . '</td>';
                $html .= '<td>' . $result['branch_name'] . '</td>';
                $html .= '<td>' . $result['movement_type_text'] . '</td>';
                $html .= '<td>' . ($result['quantity_in'] > 0 ? number_format($result['quantity_in'], 2) : '') . '</td>';
                $html .= '<td>' . ($result['quantity_out'] > 0 ? number_format($result['quantity_out'], 2) : '') . '</td>';
                $html .= '<td>' . number_format($result['running_balance'], 2) . '</td>';
                $html .= '</tr>';
            }
            
            $html .= '</table>';
            
            $pdf->writeHTML($html, true, false, true, false, '');
            
            // إخراج الملف
            $filename = 'stock_movements_' . date('Y-m-d_H-i-s') . '.pdf';
            $pdf->Output($filename, 'D');
            
        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'stock_movement_export_pdf',
                'خطأ في تصدير سجل حركة المخزون إلى PDF: ' . $e->getMessage(),
                array('error' => $e->getTraceAsString())
            );
            
            $this->session->data['error'] = 'حدث خطأ في التصدير. يرجى المحاولة مرة أخرى.';
            $this->response->redirect($this->url->link('inventory/stock_movement', 'user_token=' . $this->session->data['user_token'], true));
        }
    }
    
    /**
     * AJAX - الحصول على بيانات المنتج
     */
    public function getProductData() {
        try {
            $json = array();
            
            if (!$this->user->hasPermission('access', 'inventory/stock_movement')) {
                $json['error'] = $this->language->get('error_permission');
            } else {
                $this->load->model('inventory/stock_movement');
                
                $product_id = isset($this->request->get['product_id']) ? (int)$this->request->get['product_id'] : 0;
                $branch_id = isset($this->request->get['branch_id']) ? (int)$this->request->get['branch_id'] : 0;
                
                if ($product_id) {
                    $product_data = $this->model_inventory_stock_movement->getProductStockInfo($product_id, $branch_id);
                    
                    if ($product_data) {
                        $json['success'] = true;
                        $json['data'] = $product_data;
                    } else {
                        $json['error'] = 'المنتج غير موجود';
                    }
                } else {
                    $json['error'] = 'معرف المنتج مطلوب';
                }
            }
            
            $this->response->addHeader('Content-Type: application/json');
            $this->response->setOutput(json_encode($json));
            
        } catch (Exception $e) {
            $json = array('error' => 'حدث خطأ غير متوقع');
            $this->response->addHeader('Content-Type: application/json');
            $this->response->setOutput(json_encode($json));
        }
    }
    
    /**
     * AJAX - الحصول على إحصائيات سريعة
     */
    public function getQuickStats() {
        try {
            $json = array();
            
            if (!$this->user->hasPermission('access', 'inventory/stock_movement')) {
                $json['error'] = $this->language->get('error_permission');
            } else {
                $this->load->model('inventory/stock_movement');
                
                $filter_data = $this->getFilters();
                $stats = $this->model_inventory_stock_movement->getQuickStats($filter_data);
                
                $json['success'] = true;
                $json['stats'] = $stats;
            }
            
            $this->response->addHeader('Content-Type: application/json');
            $this->response->setOutput(json_encode($json));
            
        } catch (Exception $e) {
            $json = array('error' => 'حدث خطأ في جلب الإحصائيات');
            $this->response->addHeader('Content-Type: application/json');
            $this->response->setOutput(json_encode($json));
        }
    }
}