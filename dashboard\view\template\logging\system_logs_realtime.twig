{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="submit" form="logging\system_logs-form" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fas fa-save"></i></button>
        <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-light"><i class="fas fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    
    <div class="card">
      <div class="card-header"><i class="fas fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}chart-bar{% elif screen_type == 'settings' %}cog{% else %}pencil-alt{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="card-body">
        <form id="logging\system_logs-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-ai_context">{{ text_ai_context }}</label>
            <div class="col-sm-10">
              <input type="text" name="ai_context" value="{{ ai_context }}" placeholder="{{ text_ai_context }}" id="input-ai_context" class="form-control" />
              {% if error_ai_context %}
                <div class="invalid-feedback">{{ error_ai_context }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-back">{{ text_back }}</label>
            <div class="col-sm-10">
              <input type="text" name="back" value="{{ back }}" placeholder="{{ text_back }}" id="input-back" class="form-control" />
              {% if error_back %}
                <div class="invalid-feedback">{{ error_back }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-catalog_context">{{ text_catalog_context }}</label>
            <div class="col-sm-10">
              <input type="text" name="catalog_context" value="{{ catalog_context }}" placeholder="{{ text_catalog_context }}" id="input-catalog_context" class="form-control" />
              {% if error_catalog_context %}
                <div class="invalid-feedback">{{ error_catalog_context }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-clear">{{ text_clear }}</label>
            <div class="col-sm-10">
              <input type="text" name="clear" value="{{ clear }}" placeholder="{{ text_clear }}" id="input-clear" class="form-control" />
              {% if error_clear %}
                <div class="invalid-feedback">{{ error_clear }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-common_errors">{{ text_common_errors }}</label>
            <div class="col-sm-10">
              <input type="text" name="common_errors" value="{{ common_errors }}" placeholder="{{ text_common_errors }}" id="input-common_errors" class="form-control" />
              {% if error_common_errors %}
                <div class="invalid-feedback">{{ error_common_errors }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-export">{{ text_export }}</label>
            <div class="col-sm-10">
              <input type="text" name="export" value="{{ export }}" placeholder="{{ text_export }}" id="input-export" class="form-control" />
              {% if error_export %}
                <div class="invalid-feedback">{{ error_export }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-get_latest">{{ text_get_latest }}</label>
            <div class="col-sm-10">
              <input type="text" name="get_latest" value="{{ get_latest }}" placeholder="{{ text_get_latest }}" id="input-get_latest" class="form-control" />
              {% if error_get_latest %}
                <div class="invalid-feedback">{{ error_get_latest }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="invalid-feedback">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-inventory_context">{{ text_inventory_context }}</label>
            <div class="col-sm-10">
              <input type="text" name="inventory_context" value="{{ inventory_context }}" placeholder="{{ text_inventory_context }}" id="input-inventory_context" class="form-control" />
              {% if error_inventory_context %}
                <div class="invalid-feedback">{{ error_inventory_context }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-log">{{ text_log }}</label>
            <div class="col-sm-10">
              <input type="text" name="log" value="{{ log }}" placeholder="{{ text_log }}" id="input-log" class="form-control" />
              {% if error_log %}
                <div class="invalid-feedback">{{ error_log }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-log_analysis">{{ text_log_analysis }}</label>
            <div class="col-sm-10">
              <input type="text" name="log_analysis" value="{{ log_analysis }}" placeholder="{{ text_log_analysis }}" id="input-log_analysis" class="form-control" />
              {% if error_log_analysis %}
                <div class="invalid-feedback">{{ error_log_analysis }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-log_levels">{{ text_log_levels }}</label>
            <div class="col-sm-10">
              <input type="text" name="log_levels" value="{{ log_levels }}" placeholder="{{ text_log_levels }}" id="input-log_levels" class="form-control" />
              {% if error_log_levels %}
                <div class="invalid-feedback">{{ error_log_levels }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-log_stats">{{ text_log_stats }}</label>
            <div class="col-sm-10">
              <input type="text" name="log_stats" value="{{ log_stats }}" placeholder="{{ text_log_stats }}" id="input-log_stats" class="form-control" />
              {% if error_log_stats %}
                <div class="invalid-feedback">{{ error_log_stats }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-logs">{{ text_logs }}</label>
            <div class="col-sm-10">
              <input type="text" name="logs" value="{{ logs }}" placeholder="{{ text_logs }}" id="input-logs" class="form-control" />
              {% if error_logs %}
                <div class="invalid-feedback">{{ error_logs }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-performance_metrics">{{ text_performance_metrics }}</label>
            <div class="col-sm-10">
              <input type="text" name="performance_metrics" value="{{ performance_metrics }}" placeholder="{{ text_performance_metrics }}" id="input-performance_metrics" class="form-control" />
              {% if error_performance_metrics %}
                <div class="invalid-feedback">{{ error_performance_metrics }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-real_time">{{ text_real_time }}</label>
            <div class="col-sm-10">
              <input type="text" name="real_time" value="{{ real_time }}" placeholder="{{ text_real_time }}" id="input-real_time" class="form-control" />
              {% if error_real_time %}
                <div class="invalid-feedback">{{ error_real_time }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-realtime_config">{{ text_realtime_config }}</label>
            <div class="col-sm-10">
              <input type="text" name="realtime_config" value="{{ realtime_config }}" placeholder="{{ text_realtime_config }}" id="input-realtime_config" class="form-control" />
              {% if error_realtime_config %}
                <div class="invalid-feedback">{{ error_realtime_config }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-related_logs">{{ text_related_logs }}</label>
            <div class="col-sm-10">
              <input type="text" name="related_logs" value="{{ related_logs }}" placeholder="{{ text_related_logs }}" id="input-related_logs" class="form-control" />
              {% if error_related_logs %}
                <div class="invalid-feedback">{{ error_related_logs }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-settings">{{ text_settings }}</label>
            <div class="col-sm-10">
              <input type="text" name="settings" value="{{ settings }}" placeholder="{{ text_settings }}" id="input-settings" class="form-control" />
              {% if error_settings %}
                <div class="invalid-feedback">{{ error_settings }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-specialized_logs">{{ text_specialized_logs }}</label>
            <div class="col-sm-10">
              <input type="text" name="specialized_logs" value="{{ specialized_logs }}" placeholder="{{ text_specialized_logs }}" id="input-specialized_logs" class="form-control" />
              {% if error_specialized_logs %}
                <div class="invalid-feedback">{{ error_specialized_logs }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-system_modules">{{ text_system_modules }}</label>
            <div class="col-sm-10">
              <input type="text" name="system_modules" value="{{ system_modules }}" placeholder="{{ text_system_modules }}" id="input-system_modules" class="form-control" />
              {% if error_system_modules %}
                <div class="invalid-feedback">{{ error_system_modules }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-total">{{ text_total }}</label>
            <div class="col-sm-10">
              <input type="text" name="total" value="{{ total }}" placeholder="{{ text_total }}" id="input-total" class="form-control" />
              {% if error_total %}
                <div class="invalid-feedback">{{ error_total }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="invalid-feedback">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-websocket_config">{{ text_websocket_config }}</label>
            <div class="col-sm-10">
              <input type="text" name="websocket_config" value="{{ websocket_config }}" placeholder="{{ text_websocket_config }}" id="input-websocket_config" class="form-control" />
              {% if error_websocket_config %}
                <div class="invalid-feedback">{{ error_websocket_config }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}