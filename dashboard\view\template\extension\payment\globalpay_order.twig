<h2>{{ text_payment_info }}</h2>
<div class="alert alert-success" id="globalpay-transaction-msg" style="display:none;"></div>
<table class="table table-striped table-bordered">
  <tr>
    <td>{{ text_order_ref }}</td>
    <td>{{ globalpay_order.order_ref }}</td>
  </tr>
  <tr>
    <td>{{ text_order_total }}</td>
    <td>{{ globalpay_order.total_formatted }}</td>
  </tr>
  <tr>
    <td>{{ text_total_captured }}</td>
    <td id="globalpay-total-captured">{{ globalpay_order.total_captured_formatted }}</td>
  </tr>
  <tr>
    <td>{{ text_capture_status }}</td>
    <td id="capture_status">
      {% if globalpay_order.capture_status == 1 %}
        <span class="capture-text">{{ text_yes }}</span>
      {% else %}
        <span class="capture-text">{{ text_no }}</span>&nbsp;&nbsp;
        {% if globalpay_order.void_status == 0 %}
          <input type="text" width="10" id="capture-amount" value="{{ globalpay_order.total }}"/>
          <a class="button btn btn-primary" id="button-capture">{{ button_capture }}</a>
          <span class="btn btn-primary" id="loading-capture" style="display:none;"><i class="fa fa-circle-o-notch fa-spin fa-lg"></i></span>
      {% endif %}
      {% endif %}</td>
  </tr>
  <tr>
    <td>{{ text_void_status }}</td>
    <td id="void_status">
      {% if globalpay_order.void_status == 1 %}
        <span class="void_text">{{ text_yes }}</span>
      {% else %}
        <span class="void_text">{{ text_no }}</span>&nbsp;&nbsp;
        <a class="button btn btn-primary" id="button-void">{{ button_void }}</a>
        <span class="btn btn-primary" id="loading-void" style="display:none;"><i class="fa fa-circle-o-notch fa-spin fa-lg"></i></span>
      {% endif %}
    </td>
  </tr>
  <tr>
    <td>{{ text_rebate_status }}</td>
    <td id="rebate_status">
      {% if globalpay_order.rebate_status == 1 %}
        <span class="rebate_text">{{ text_yes }}</span>
      {% else %}
        <span class="rebate_text">{{ text_no }}</span>&nbsp;&nbsp;
        {% if globalpay_order.total_captured > 0 and globalpay_order.void_status == 0 %}
          <input type="text" width="10" id="rebate-amount" />
          <a class="button btn btn-primary" id="button-rebate">{{ button_rebate }}</a>
          <span class="btn btn-primary" id="loading-rebate" style="display:none;"><i class="fa fa-circle-o-notch fa-spin fa-lg"></i></span>
        {% endif %}
      {% endif %}
    </td>
  </tr>
  <tr>
    <td>{{ text_transactions }}:</td>
    <td>
      <table class="table table-striped table-bordered" id="globalpay-transactions">
        <thead>
          <tr>
            <td class="text-left"><strong>{{ text_column_date_added }}</strong></td>
            <td class="text-left"><strong>{{ text_column_type }}</strong></td>
            <td class="text-left"><strong>{{ text_column_amount }}</strong></td>
          </tr>
        </thead>
        <tbody>
          {% for transaction in globalpay_order.transactions %}
            <tr>
              <td class="text-left">{{ transaction.date_added }}</td>
              <td class="text-left">{{ transaction.type }}</td>
              <td class="text-left">{{ transaction.amount }}</td>
            </tr>
          {% endfor %}
        </tbody>
      </table>
    </td>
  </tr>
</table>
<script type="text/javascript"><!--
  $("#button-void").click(function () {
    if (confirm('{{ text_confirm_void }}')) {
      $.ajax({
        type:'POST',
        dataType: 'json',
        data: {'order_id': '{{ order_id }}' },
        url: 'index.php?route=extension/payment/globalpay/void&user_token={{ user_token }}',
        beforeSend: function() {
          $('#button-void').hide();
          $('#loading-void').show();
          $('#globalpay-transaction-msg').hide();
        },
        success: function(data) {
          if (data.error == false) {
            html = '';
            html += '<tr>';
            html += '<td class="text-left">' + data.data.date_added + '</td>';
            html += '<td class="text-left">{{ text_void }}</td>';
            html += '<td class="text-left">0.00</td>';
            html += '</tr>';

            $('.void_text').text('{{ text_yes }}');
            $('#globalpay-transactions').append(html);
            $('#button-capture').hide();
            $('#capture-amount').hide();

            if (data.msg != '') {
              $('#globalpay-transaction-msg').empty().html('<i class="fa fa-check-circle"></i> ' + data.msg).fadeIn();
            }
          }
          if (data.error == true) {
            alert(data.msg);
            $('#button-void').show();
          }

          $('#loading-void').hide();
        }
      });
    }
  });
  $("#button-capture").click(function () {
    if (confirm('{{ text_confirm_capture }}')) {
      $.ajax({
        type:'POST',
        dataType: 'json',
        data: {'order_id' : '{{ order_id }}', 'amount' : $('#capture-amount').val() },
        url: 'index.php?route=extension/payment/globalpay/capture&user_token={{ user_token }}',
        beforeSend: function() {
          $('#button-capture').hide();
          $('#capture-amount').hide();
          $('#loading-capture').show();
          $('#globalpay-transaction-msg').hide();
        },
        success: function(data) {
          if (data.error == false) {
            html = '';
            html += '<tr>';
            html += '<td class="text-left">' + data.data.date_added + '</td>';
            html += '<td class="text-left">{{ text_payment }}</td>';
            html += '<td class="text-left">' + data.data.amount + '</td>';
            html += '</tr>';

            $('#globalpay-transactions').append(html);
            $('#globalpay-total-captured').text(data.data.total);

            if (data.data.capture_status == 1) {
              $('#button-void').hide();
              $('.capture-text').text('{{ text_yes }}');
            } else {
              $('#button-capture').show();
              $('#capture-amount').val('0.00');

              {% if auto_settle == 2 %}
                $('#capture-amount').show();
              {% endif %}
            }

            if (data.msg != '') {
              $('#globalpay-transaction-msg').empty().html('<i class="fa fa-check-circle"></i> ' + data.msg).fadeIn();
            }

            $('#button-rebate').show();
            $('#rebate-amount').val(0.00).show();
          }
          if (data.error == true) {
            alert(data.msg);
            $('#button-capture').show();
            $('#capture-amount').show();
          }

          $('#loading-capture').hide();
        }
      });
    }
  });
  $("#button-rebate").click(function () {
    if (confirm('{{ text_confirm_rebate }}')) {
      $.ajax({
        type:'POST',
        dataType: 'json',
        data: {'order_id': '{{ order_id }}', 'amount' : $('#rebate-amount').val() },
        url: 'index.php?route=extension/payment/globalpay/rebate&user_token={{ user_token }}',
        beforeSend: function() {
          $('#button-rebate').hide();
          $('#rebate-amount').hide();
          $('#loading-rebate').show();
          $('#globalpay-transaction-msg').hide();
        },
        success: function(data) {
          if (data.error == false) {
            html = '';
            html += '<tr>';
            html += '<td class="text-left">' + data.data.date_added + '</td>';
            html += '<td class="text-left">{{ text_rebate }}</td>';
            html += '<td class="text-left">' + data.data.amount + '</td>';
            html += '</tr>';

            $('#globalpay-transactions').append(html);
            $('#globalpay-total-captured').text(data.data.total_captured);

            if (data.data.rebate_status == 1) {
              $('.rebate_text').text('{{ text_yes }}');
            } else {
              $('#button-rebate').show();
              $('#rebate-amount').val(0.00).show();
            }

            if (data.msg != '') {
              $('#globalpay-transaction-msg').empty().html('<i class="fa fa-check-circle"></i> ' + data.msg).fadeIn();
            }
          }
          if (data.error == true) {
            alert(data.msg);
            $('#button-rebate').show();
          }

          $('#loading-rebate').hide();
        }
      });
    }
  });
//--></script>
