<!DOCTYPE html>
<html dir="{{ direction }}" lang="{{ lang }}" class="no-js">
<head>
<meta charset="UTF-8" />
<title>{{ heading_title }}</title>
<base href="{{ base }}" />
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
<link href="view/stylesheet/bootstrap.css" rel="stylesheet" media="screen" />
<style>
@media print {
  body { margin: 0; }
  .no-print { display: none !important; }
  .page-break { page-break-before: always; }
}

body {
  font-family: Arial, sans-serif;
  font-size: 11px;
  line-height: 1.3;
}

.header {
  text-align: center;
  margin-bottom: 30px;
  border-bottom: 2px solid #333;
  padding-bottom: 20px;
}

.company-logo {
  max-height: 80px;
  margin-bottom: 10px;
}

.company-name {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}

.report-title {
  font-size: 18px;
  font-weight: bold;
  color: #666;
  margin-bottom: 10px;
}

.report-info {
  font-size: 10px;
  color: #888;
}

.summary-box {
  background-color: #f8f9fa;
  padding: 15px;
  margin-bottom: 20px;
  border: 1px solid #dee2e6;
}

.ledger-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 20px;
  font-size: 9px;
}

.ledger-table th,
.ledger-table td {
  border: 1px solid #dee2e6;
  padding: 4px;
  text-align: left;
}

.ledger-table th {
  background-color: #f8f9fa;
  font-weight: bold;
}

.text-right {
  text-align: right;
}

.text-center {
  text-align: center;
}

.text-success {
  color: #28a745;
}

.text-warning {
  color: #ffc107;
}

.text-danger {
  color: #dc3545;
}

.text-info {
  color: #17a2b8;
}

.footer {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #dee2e6;
  font-size: 9px;
  color: #888;
}

.signature-section {
  margin-top: 40px;
  display: flex;
  justify-content: space-between;
}

.signature-box {
  width: 200px;
  text-align: center;
}

.signature-line {
  border-top: 1px solid #333;
  margin-top: 50px;
  padding-top: 5px;
}

.watermark {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(-45deg);
  font-size: 72px;
  color: rgba(0,0,0,0.05);
  z-index: -1;
  pointer-events: none;
}

.account-header {
  background-color: #e2e3e5;
  font-weight: bold;
  padding: 8px;
  margin-top: 20px;
  border: 1px solid #dee2e6;
}

.account-summary {
  background-color: #f8f9fa;
  padding: 5px;
  border: 1px solid #dee2e6;
  margin-bottom: 10px;
}
</style>
</head>
<body>

<!-- Watermark -->
<div class="watermark">{{ text_confidential }}</div>

<!-- Header -->
<div class="header">
  {% if company_logo %}
  <img src="{{ company_logo }}" alt="{{ company_name }}" class="company-logo">
  {% endif %}
  <div class="company-name">{{ company_name }}</div>
  <div class="report-title">{{ heading_title }}</div>
  <div class="report-info">
    {{ text_period }}: {{ date_start }} {{ text_to }} {{ date_end }} | {{ text_generated_on }}: {{ generated_date }}
  </div>
</div>

<!-- Report Information -->
<div class="summary-box">
  <h3 style="margin-top: 0;">{{ text_report_summary }}</h3>
  <div style="display: flex; justify-content: space-between;">
    <div>
      <strong>{{ text_period }}:</strong> {{ date_start }} {{ text_to }} {{ date_end }}<br>
      <strong>{{ text_currency }}:</strong> {{ currency }}<br>
      <strong>{{ text_total_accounts }}:</strong> {{ summary.total_accounts }}
    </div>
    <div>
      <strong>{{ text_total_debits }}:</strong> {{ summary.total_debits }}<br>
      <strong>{{ text_total_credits }}:</strong> {{ summary.total_credits }}<br>
      <strong>{{ text_total_transactions }}:</strong> {{ summary.total_transactions }}
    </div>
    <div>
      <strong>{{ text_generated_by }}:</strong> {{ generated_by }}<br>
      <strong>{{ text_report_type }}:</strong> {{ text_detailed_ledger }}<br>
      <strong>{{ text_compliance }}:</strong> {{ text_eas_compliant }}
    </div>
  </div>
</div>

<!-- General Ledger by Account -->
{% for account in accounts %}
<div class="account-header">
  {{ account.code }} - {{ account.name }}
</div>

<div class="account-summary">
  <div style="display: flex; justify-content: space-between;">
    <div>
      <strong>{{ text_opening_balance }}:</strong> {{ account.opening_balance }}
    </div>
    <div>
      <strong>{{ text_total_debits }}:</strong> {{ account.total_debits }}
    </div>
    <div>
      <strong>{{ text_total_credits }}:</strong> {{ account.total_credits }}
    </div>
    <div>
      <strong>{{ text_closing_balance }}:</strong> 
      <span class="{% if account.closing_balance >= 0 %}text-success{% else %}text-danger{% endif %}">
        {{ account.closing_balance }}
      </span>
    </div>
  </div>
</div>

{% if account.transactions|length > 0 %}
<table class="ledger-table">
  <thead>
    <tr>
      <th style="width: 10%;">{{ column_date }}</th>
      <th style="width: 15%;">{{ column_reference }}</th>
      <th style="width: 30%;">{{ column_description }}</th>
      <th style="width: 12%;" class="text-right">{{ column_debit }}</th>
      <th style="width: 12%;" class="text-right">{{ column_credit }}</th>
      <th style="width: 12%;" class="text-right">{{ column_balance }}</th>
      <th style="width: 9%;">{{ column_source }}</th>
    </tr>
  </thead>
  <tbody>
    <!-- Opening Balance -->
    <tr style="background-color: #f8f9fa;">
      <td>{{ account.period_start }}</td>
      <td>{{ text_opening_balance }}</td>
      <td>{{ text_brought_forward }}</td>
      <td class="text-right">-</td>
      <td class="text-right">-</td>
      <td class="text-right">{{ account.opening_balance }}</td>
      <td>{{ text_system }}</td>
    </tr>
    
    <!-- Transactions -->
    {% set running_balance = account.opening_balance %}
    {% for transaction in account.transactions %}
    {% set running_balance = running_balance + transaction.debit - transaction.credit %}
    <tr>
      <td>{{ transaction.date }}</td>
      <td>{{ transaction.reference }}</td>
      <td>{{ transaction.description }}</td>
      <td class="text-right">{{ transaction.debit > 0 ? transaction.debit : '-' }}</td>
      <td class="text-right">{{ transaction.credit > 0 ? transaction.credit : '-' }}</td>
      <td class="text-right {% if running_balance >= 0 %}text-success{% else %}text-danger{% endif %}">
        {{ running_balance }}
      </td>
      <td>{{ transaction.source }}</td>
    </tr>
    {% endfor %}
  </tbody>
  <tfoot>
    <tr style="background-color: #e2e3e5; font-weight: bold;">
      <td colspan="3">{{ text_closing_balance }}</td>
      <td class="text-right">{{ account.total_debits }}</td>
      <td class="text-right">{{ account.total_credits }}</td>
      <td class="text-right {% if account.closing_balance >= 0 %}text-success{% else %}text-danger{% endif %}">
        {{ account.closing_balance }}
      </td>
      <td>-</td>
    </tr>
  </tfoot>
</table>
{% else %}
<div style="text-align: center; padding: 20px; color: #888;">
  {{ text_no_transactions }}
</div>
{% endif %}

{% if not loop.last %}
<div class="page-break"></div>
{% endif %}
{% endfor %}

<!-- Summary Totals -->
<div class="page-break"></div>
<h3>{{ text_ledger_summary }}</h3>
<table class="ledger-table">
  <thead>
    <tr>
      <th style="width: 15%;">{{ column_account_code }}</th>
      <th style="width: 25%;">{{ column_account_name }}</th>
      <th style="width: 10%;">{{ column_account_type }}</th>
      <th style="width: 12%;" class="text-right">{{ column_opening_balance }}</th>
      <th style="width: 12%;" class="text-right">{{ column_total_debits }}</th>
      <th style="width: 12%;" class="text-right">{{ column_total_credits }}</th>
      <th style="width: 12%;" class="text-right">{{ column_closing_balance }}</th>
      <th style="width: 2%;" class="text-center">{{ column_transactions }}</th>
    </tr>
  </thead>
  <tbody>
    {% for account in accounts %}
    <tr>
      <td>{{ account.code }}</td>
      <td>{{ account.name }}</td>
      <td>{{ account.type_name }}</td>
      <td class="text-right">{{ account.opening_balance }}</td>
      <td class="text-right">{{ account.total_debits }}</td>
      <td class="text-right">{{ account.total_credits }}</td>
      <td class="text-right {% if account.closing_balance >= 0 %}text-success{% else %}text-danger{% endif %}">
        {{ account.closing_balance }}
      </td>
      <td class="text-center">{{ account.transaction_count }}</td>
    </tr>
    {% endfor %}
  </tbody>
  <tfoot>
    <tr style="background-color: #e2e3e5; font-weight: bold;">
      <td colspan="3">{{ text_grand_total }}</td>
      <td class="text-right">{{ summary.total_opening_balance }}</td>
      <td class="text-right">{{ summary.total_debits }}</td>
      <td class="text-right">{{ summary.total_credits }}</td>
      <td class="text-right">{{ summary.total_closing_balance }}</td>
      <td class="text-center">{{ summary.total_transactions }}</td>
    </tr>
  </tfoot>
</table>

<!-- Trial Balance Verification -->
<h3>{{ text_trial_balance_verification }}</h3>
<table class="ledger-table">
  <thead>
    <tr>
      <th>{{ column_verification_item }}</th>
      <th class="text-right">{{ column_amount }}</th>
      <th class="text-center">{{ column_status }}</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>{{ text_total_debits }}</td>
      <td class="text-right">{{ summary.total_debits }}</td>
      <td class="text-center">✓</td>
    </tr>
    <tr>
      <td>{{ text_total_credits }}</td>
      <td class="text-right">{{ summary.total_credits }}</td>
      <td class="text-center">✓</td>
    </tr>
    <tr style="background-color: {% if summary.balance_difference == 0 %}#d4edda{% else %}#f8d7da{% endif %};">
      <td><strong>{{ text_difference }}</strong></td>
      <td class="text-right"><strong>{{ summary.balance_difference }}</strong></td>
      <td class="text-center">
        {% if summary.balance_difference == 0 %}
          <span style="color: #28a745;">✓ {{ text_balanced }}</span>
        {% else %}
          <span style="color: #dc3545;">✗ {{ text_unbalanced }}</span>
        {% endif %}
      </td>
    </tr>
  </tbody>
</table>

<!-- Signatures -->
<div class="signature-section">
  <div class="signature-box">
    <div class="signature-line">{{ text_prepared_by }}</div>
  </div>
  <div class="signature-box">
    <div class="signature-line">{{ text_reviewed_by }}</div>
  </div>
  <div class="signature-box">
    <div class="signature-line">{{ text_approved_by }}</div>
  </div>
</div>

<!-- Footer -->
<div class="footer">
  <div style="display: flex; justify-content: space-between;">
    <div>
      {{ text_confidential }} - {{ text_internal_use }}
    </div>
    <div>
      {{ text_eas_compliant }} | {{ text_eta_ready }} | {{ text_general_ledger_control }}
    </div>
    <div>
      {{ text_page }} 1 {{ text_of }} 1
    </div>
  </div>
</div>

<script>
// Auto print when page loads
window.onload = function() {
  window.print();
};
</script>

</body>
</html>
