{{ header }}{{ column_left }}

<!-- Enterprise Grade Plus CSS for Budget Report -->
<style>
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --danger-color: #e74c3c;
    --warning-color: #f39c12;
    --info-color: #17a2b8;
    --budget-color: #9b59b6;
    --light-bg: #f8f9fa;
    --border-color: #dee2e6;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

.budget-report-container {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: var(--shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.budget-report-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--budget-color), var(--primary-color), var(--secondary-color));
}

.budget-report-header {
    text-align: center;
    border-bottom: 3px solid var(--budget-color);
    padding-bottom: 20px;
    margin-bottom: 30px;
    position: relative;
}

.budget-report-header h2 {
    color: var(--budget-color);
    font-weight: 700;
    font-size: 1.8rem;
    margin: 0;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.budget-summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.budget-summary-card {
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    text-align: center;
    box-shadow: var(--shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.budget-summary-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0,0,0,0.1);
}

.budget-summary-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
}

.budget-summary-card.budgeted::before { background: var(--budget-color); }
.budget-summary-card.actual::before { background: var(--info-color); }
.budget-summary-card.variance::before { background: var(--warning-color); }
.budget-summary-card.count::before { background: var(--success-color); }

.budget-summary-card h4 {
    color: var(--primary-color);
    margin-bottom: 10px;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.budget-summary-card .amount {
    font-size: 1.4rem;
    font-weight: 700;
    font-family: 'Courier New', monospace;
    margin-bottom: 5px;
}

.budget-summary-card .description {
    font-size: 0.8rem;
    color: #6c757d;
    font-weight: 500;
}

.card-budgeted .amount { color: var(--budget-color); }
.card-actual .amount { color: var(--info-color); }
.card-variance .amount { color: var(--warning-color); }
.card-count .amount { color: var(--success-color); }

.budget-report-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    background: #ffffff;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.budget-report-table th {
    background: linear-gradient(135deg, var(--budget-color), #8e44ad);
    color: white;
    padding: 15px 12px;
    text-align: center;
    font-weight: 600;
    font-size: 0.9rem;
    border-bottom: 2px solid var(--border-color);
}

.budget-report-table td {
    padding: 12px;
    border-bottom: 1px solid var(--border-color);
    text-align: center;
    transition: var(--transition);
}

.budget-report-table tbody tr:hover {
    background: var(--light-bg);
    transform: scale(1.005);
}

.budget-report-table tbody tr:nth-child(even) {
    background: #fafbfc;
}

.amount-cell {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    text-align: {{ direction == 'rtl' ? 'right' : 'left' }};
}

.variance-positive { 
    color: var(--success-color); 
    font-weight: 600;
}

.variance-negative { 
    color: var(--danger-color); 
    font-weight: 600;
}

.variance-neutral { 
    color: var(--secondary-color); 
    font-weight: 600;
}

.budget-actions {
    display: flex;
    gap: 5px;
    justify-content: center;
}

.budget-actions .btn {
    padding: 5px 10px;
    font-size: 0.8rem;
}

/* Chart Container */
.chart-container {
    background: #ffffff;
    border-radius: var(--border-radius);
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: var(--shadow);
}

.chart-container canvas {
    max-height: 400px;
}

/* Filter Panel */
.filter-panel {
    background: linear-gradient(135deg, #f8f9fa, #ffffff);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: var(--shadow);
}

/* RTL Support */
[dir="rtl"] .budget-report-table {
    direction: rtl;
}

[dir="rtl"] .amount-cell {
    text-align: right;
}

/* Print Styles */
@media print {
    .budget-report-container {
        box-shadow: none;
        border: 1px solid #000;
    }
    
    .budget-report-table th {
        background: #f0f0f0 !important;
        color: #000 !important;
    }
    
    .btn, .card-header, .filter-panel {
        display: none !important;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .budget-report-table {
        font-size: 0.8rem;
    }
    
    .budget-report-table th,
    .budget-report-table td {
        padding: 8px 6px;
    }
    
    .budget-summary-cards {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .budget-actions {
        flex-direction: column;
    }
}
</style>

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-{{ direction == 'rtl' ? 'start' : 'end' }}">
        <div class="btn-group" role="group" aria-label="{{ text_actions }}">
          <button type="button" class="btn btn-success btn-lg" onclick="generateReport()" 
                  data-bs-toggle="tooltip" title="{{ text_generate_report }}">
            <i class="fas fa-chart-bar me-2"></i> {{ button_generate }}
          </button>
          <div class="btn-group" role="group">
            <button type="button" class="btn btn-info dropdown-toggle" data-bs-toggle="dropdown" 
                    aria-expanded="false" data-bs-toggle="tooltip" title="{{ text_export_options }}">
              <i class="fas fa-download me-2"></i> {{ text_export }}
            </button>
            <ul class="dropdown-menu">
              <li><a class="dropdown-item" href="#" onclick="exportReport('excel')">
                <i class="fas fa-file-excel text-success me-2"></i> Excel
              </a></li>
              <li><a class="dropdown-item" href="#" onclick="exportReport('pdf')">
                <i class="fas fa-file-pdf text-danger me-2"></i> PDF
              </a></li>
              <li><a class="dropdown-item" href="#" onclick="exportReport('csv')">
                <i class="fas fa-file-csv text-info me-2"></i> CSV
              </a></li>
              <li><hr class="dropdown-divider"></li>
              <li><a class="dropdown-item" href="#" onclick="printReport()">
                <i class="fas fa-print text-secondary me-2"></i> {{ text_print }}
              </a></li>
            </ul>
          </div>
          <button type="button" class="btn btn-outline-primary" onclick="showVarianceAnalysis()"
                  data-bs-toggle="tooltip" title="{{ text_variance_analysis }}">
            <i class="fas fa-chart-line"></i>
          </button>
        </div>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li class="breadcrumb-item"><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>

  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible fade show">
      <i class="fas fa-exclamation-triangle"></i>
      {{ error_warning }}
      <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    {% endif %}

    {% if success %}
    <div class="alert alert-success alert-dismissible fade show">
      <i class="fas fa-check-circle"></i>
      {{ success }}
      <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    {% endif %}

    <!-- Filter Panel -->
    <div class="filter-panel">
      <h4>{{ text_report_filters }}</h4>
      <form id="budget-report-form" method="post">
        <div class="row">
          <div class="col-md-3">
            <div class="form-group">
              <label for="budget_id" class="form-label">{{ entry_budget }}</label>
              <select name="budget_id" id="budget_id" class="form-control select2">
                <option value="">{{ text_all_budgets }}</option>
                {% for budget in budgets %}
                <option value="{{ budget.budget_id }}"{% if budget.budget_id == budget_id %} selected{% endif %}>
                  {{ budget.budget_name }} ({{ budget.budget_year }})
                </option>
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="col-md-2">
            <div class="form-group">
              <label for="budget_year" class="form-label">{{ entry_budget_year }}</label>
              <select name="budget_year" id="budget_year" class="form-control">
                <option value="">{{ text_all_years }}</option>
                {% for year in financial_years %}
                <option value="{{ year }}"{% if year == budget_year %} selected{% endif %}>{{ year }}</option>
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="col-md-2">
            <div class="form-group">
              <label for="department_id" class="form-label">{{ entry_department }}</label>
              <select name="department_id" id="department_id" class="form-control">
                <option value="">{{ text_all_departments }}</option>
                {% for department in departments %}
                <option value="{{ department.department_id }}"{% if department.department_id == department_id %} selected{% endif %}>{{ department.name }}</option>
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="col-md-2">
            <div class="form-group">
              <label for="cost_center_id" class="form-label">{{ entry_cost_center }}</label>
              <select name="cost_center_id" id="cost_center_id" class="form-control">
                <option value="">{{ text_all_cost_centers }}</option>
                {% for cost_center in cost_centers %}
                <option value="{{ cost_center.cost_center_id }}"{% if cost_center.cost_center_id == cost_center_id %} selected{% endif %}>{{ cost_center.name }}</option>
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="col-md-3">
            <div class="form-group">
              <label class="form-label">&nbsp;</label>
              <div class="d-grid">
                <button type="submit" class="btn btn-primary">
                  <i class="fas fa-search me-2"></i>{{ button_filter }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>

    <!-- Budget Report Content -->
    {% if report_data %}
    <!-- Summary Cards -->
    <div class="budget-summary-cards">
      <div class="budget-summary-card card-budgeted budgeted">
        <h4>{{ text_total_budgeted }}</h4>
        <div class="amount">{{ report_data.summary.total_budgeted_formatted }}</div>
        <div class="description">{{ text_planned_amount }}</div>
      </div>
      <div class="budget-summary-card card-actual actual">
        <h4>{{ text_total_actual }}</h4>
        <div class="amount">{{ report_data.summary.total_actual_formatted }}</div>
        <div class="description">{{ text_actual_amount }}</div>
      </div>
      <div class="budget-summary-card card-variance variance">
        <h4>{{ text_total_variance }}</h4>
        <div class="amount">{{ report_data.summary.total_variance_formatted }}</div>
        <div class="description">{{ report_data.summary.variance_percentage|number_format(2) }}%</div>
      </div>
      <div class="budget-summary-card card-count count">
        <h4>{{ text_budget_count }}</h4>
        <div class="amount">{{ report_data.summary.budget_count }}</div>
        <div class="description">{{ text_budgets }}</div>
      </div>
    </div>

    <!-- Budget Report Table -->
    <div class="budget-report-container">
      <div class="budget-report-header">
        <h2>{{ text_budget_report_details }}</h2>
      </div>

      <div class="table-responsive">
        <table class="budget-report-table" id="budget-report-table">
          <thead>
            <tr>
              <th>{{ column_budget_name }}</th>
              <th>{{ column_budget_code }}</th>
              <th>{{ column_budget_year }}</th>
              <th>{{ column_department }}</th>
              <th>{{ column_budgeted_amount }}</th>
              <th>{{ column_actual_amount }}</th>
              <th>{{ column_variance }}</th>
              <th>{{ column_variance_percentage }}</th>
              <th>{{ column_status }}</th>
              <th>{{ column_action }}</th>
            </tr>
          </thead>
          <tbody>
            {% for budget in report_data.budgets %}
            <tr data-budget-id="{{ budget.budget_id }}">
              <td>
                <strong>{{ budget.budget_name }}</strong>
                <br>
                <small class="text-muted">{{ budget.start_date }} - {{ budget.end_date }}</small>
              </td>
              <td>{{ budget.budget_code }}</td>
              <td>{{ budget.budget_year }}</td>
              <td>{{ budget.department_name }}</td>
              <td class="amount-cell">
                <strong>{{ budget.budgeted_amount_formatted }}</strong>
              </td>
              <td class="amount-cell">
                {{ budget.actual_amount_formatted }}
              </td>
              <td class="amount-cell">
                <span class="{% if budget.variance >= 0 %}variance-positive{% elseif budget.variance < 0 %}variance-negative{% else %}variance-neutral{% endif %}">
                  {{ budget.variance_formatted }}
                </span>
              </td>
              <td class="amount-cell">
                <span class="{% if budget.variance_percentage >= 0 %}variance-positive{% elseif budget.variance_percentage < 0 %}variance-negative{% else %}variance-neutral{% endif %}">
                  {{ budget.variance_percentage|number_format(2) }}%
                </span>
              </td>
              <td>
                <span class="badge bg-{% if budget.status == 'approved' %}success{% elseif budget.status == 'active' %}primary{% elseif budget.status == 'draft' %}warning{% else %}secondary{% endif %}">
                  {{ budget.status }}
                </span>
              </td>
              <td>
                <div class="budget-actions">
                  <button type="button" class="btn btn-outline-info btn-sm"
                          onclick="viewBudgetDetails({{ budget.budget_id }})"
                          data-bs-toggle="tooltip" title="{{ text_view_details }}">
                    <i class="fas fa-eye"></i>
                  </button>
                  <button type="button" class="btn btn-outline-warning btn-sm"
                          onclick="viewVarianceDetails({{ budget.budget_id }})"
                          data-bs-toggle="tooltip" title="{{ text_variance_details }}">
                    <i class="fas fa-chart-line"></i>
                  </button>
                </div>
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>

    <!-- Charts Section -->
    <div class="row">
      <div class="col-md-6">
        <div class="chart-container">
          <h4>{{ text_budget_vs_actual_chart }}</h4>
          <canvas id="budgetVsActualChart"></canvas>
        </div>
      </div>
      <div class="col-md-6">
        <div class="chart-container">
          <h4>{{ text_variance_chart }}</h4>
          <canvas id="varianceChart"></canvas>
        </div>
      </div>
    </div>
    {% else %}
    <div class="alert alert-info">
      <i class="fas fa-info-circle me-2"></i>
      {{ text_no_data }}
    </div>
    {% endif %}
  </div>
</div>

<script>
// Enterprise Grade Plus JavaScript for Budget Report
class BudgetReportManager {
    constructor() {
        this.initializeTooltips();
        this.initializeDataTable();
        this.initializeKeyboardShortcuts();
        this.initializeCharts();
        this.initializeSelect2();
    }

    initializeTooltips() {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }

    initializeDataTable() {
        const table = document.getElementById('budget-report-table');
        if (table && typeof $ !== 'undefined' && $.fn.DataTable) {
            $(table).DataTable({
                responsive: true,
                pageLength: 25,
                order: [[2, 'desc'], [0, 'asc']], // Sort by year desc, then name asc
                columnDefs: [
                    { targets: [4, 5, 6, 7], className: 'text-end' },
                    { targets: [9], orderable: false }
                ],
                language: {
                    url: '{{ direction == "rtl" ? "/view/javascript/jquery/datatables-ar.json" : "/view/javascript/jquery/datatables-en.json" }}'
                }
            });
        }
    }

    initializeKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case 'g':
                        e.preventDefault();
                        this.generateReport();
                        break;
                    case 'e':
                        e.preventDefault();
                        this.showExportMenu();
                        break;
                    case 'p':
                        e.preventDefault();
                        this.printReport();
                        break;
                    case 'v':
                        e.preventDefault();
                        this.showVarianceAnalysis();
                        break;
                }
            }
        });
    }

    initializeCharts() {
        if (typeof Chart !== 'undefined') {
            this.createBudgetVsActualChart();
            this.createVarianceChart();
        }
    }

    initializeSelect2() {
        if (typeof $ !== 'undefined' && $.fn.select2) {
            $('.select2').select2({
                theme: 'bootstrap-5',
                width: '100%'
            });
        }
    }

    generateReport() {
        const form = document.getElementById('budget-report-form');
        const formData = new FormData(form);

        this.showLoadingState(true);

        fetch('{{ generate_url }}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            this.showLoadingState(false);
            if (data.success) {
                this.showAlert('{{ text_report_generated }}', 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                this.showAlert(data.error || '{{ error_generate_report }}', 'danger');
            }
        })
        .catch(error => {
            this.showLoadingState(false);
            this.showAlert('{{ error_generate_report }}: ' + error.message, 'danger');
        });
    }

    exportReport(format) {
        const params = new URLSearchParams({
            format: format,
            budget_id: document.getElementById('budget_id').value,
            budget_year: document.getElementById('budget_year').value,
            department_id: document.getElementById('department_id').value,
            cost_center_id: document.getElementById('cost_center_id').value
        });

        this.showAlert('{{ text_exporting }}...', 'info');
        window.open('{{ export_url }}&' + params.toString(), '_blank');
    }

    printReport() {
        window.print();
    }

    showVarianceAnalysis() {
        window.open('{{ variance_url }}', '_blank');
    }

    viewBudgetDetails(budgetId) {
        window.open('{{ url_link('accounts/budget_management_advanced', 'view') }}&budget_id=' + budgetId, '_blank');
    }

    viewVarianceDetails(budgetId) {
        window.open('{{ variance_url }}&budget_id=' + budgetId, '_blank');
    }

    createBudgetVsActualChart() {
        const ctx = document.getElementById('budgetVsActualChart');
        if (!ctx) return;

        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: {{ budget_names|json_encode|raw }},
                datasets: [{
                    label: '{{ text_budgeted_amount }}',
                    data: {{ budgeted_amounts|json_encode|raw }},
                    backgroundColor: '#9b59b6',
                    borderColor: '#8e44ad',
                    borderWidth: 1
                }, {
                    label: '{{ text_actual_amount }}',
                    data: {{ actual_amounts|json_encode|raw }},
                    backgroundColor: '#17a2b8',
                    borderColor: '#138496',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: '{{ text_budget_vs_actual_chart }}'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    createVarianceChart() {
        const ctx = document.getElementById('varianceChart');
        if (!ctx) return;

        new Chart(ctx, {
            type: 'line',
            data: {
                labels: {{ budget_names|json_encode|raw }},
                datasets: [{
                    label: '{{ text_variance_percentage }}',
                    data: {{ variance_percentages|json_encode|raw }},
                    borderColor: '#f39c12',
                    backgroundColor: 'rgba(243, 156, 18, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: '{{ text_variance_chart }}'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    }
                }
            }
        });
    }

    showExportMenu() {
        const exportButton = document.querySelector('.dropdown-toggle');
        if (exportButton) {
            exportButton.click();
        }
    }

    showLoadingState(show) {
        const generateBtn = document.querySelector('button[onclick="generateReport()"]');
        if (generateBtn) {
            if (show) {
                generateBtn.disabled = true;
                generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> {{ text_loading }}';
            } else {
                generateBtn.disabled = false;
                generateBtn.innerHTML = '<i class="fas fa-chart-bar me-2"></i> {{ button_generate }}';
            }
        }
    }

    showAlert(message, type) {
        const alertContainer = document.createElement('div');
        alertContainer.className = `alert alert-${type} alert-dismissible fade show`;
        alertContainer.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'}"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        const container = document.querySelector('#content .container-fluid');
        container.insertBefore(alertContainer, container.firstChild);

        setTimeout(() => {
            alertContainer.remove();
        }, 5000);
    }
}

// Global functions for backward compatibility
function generateReport() {
    budgetReportManager.generateReport();
}

function exportReport(format) {
    budgetReportManager.exportReport(format);
}

function printReport() {
    budgetReportManager.printReport();
}

function showVarianceAnalysis() {
    budgetReportManager.showVarianceAnalysis();
}

function viewBudgetDetails(budgetId) {
    budgetReportManager.viewBudgetDetails(budgetId);
}

function viewVarianceDetails(budgetId) {
    budgetReportManager.viewVarianceDetails(budgetId);
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.budgetReportManager = new BudgetReportManager();
});
</script>

{{ footer }}
