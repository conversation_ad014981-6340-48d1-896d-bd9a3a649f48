import os

def generate_tree():
    """Generates a tree structure of the project directory and saves it to tree.txt."""
    
    try:
        # The script is in the project root, so the script's directory is the project root.
        project_root = os.path.dirname(os.path.abspath(__file__))
        tree_file_path = os.path.join(project_root, "tree.txt")

        # Exclusions
        exclude_dirs = {'.git', 'vendor', 'node_modules', 'storage'}
        exclude_files = {'tree.txt', 'generate_tree.py'}

        with open(tree_file_path, "w", encoding='utf-8') as f:
            for root, dirs, files in os.walk(project_root, topdown=True):
                # Exclude specified directories
                dirs[:] = [d for d in dirs if d not in exclude_dirs]
                
                relative_root = os.path.relpath(root, project_root)

                if relative_root == ".":
                    level = 0
                    f.write(f"{os.path.basename(project_root)}/\n")
                else:
                    level = relative_root.count(os.sep) + 1
                    indent = " " * 4 * (level)
                    f.write(f"{indent}{os.path.basename(root)}/\n")

                sub_indent = " " * 4 * (level + 1)
                for file in files:
                    if file not in exclude_files:
                        f.write(f"{sub_indent}{file}\n")
        
        print(f"tree.txt has been generated successfully at: {tree_file_path}")

    except Exception as e:
        print(f"An error occurred: {e}")

if __name__ == "__main__":
    generate_tree()