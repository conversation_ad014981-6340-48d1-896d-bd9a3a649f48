{{ header }}{{ column_left }}
{#
/**
 * AYM ERP System: Advanced Sales Order List View
 *
 * واجهة قائمة طلبات البيع المتقدمة - مطورة بجودة عالمية تتفوق على SAP وOdoo وWooCommerce
 *
 * الميزات المتقدمة:
 * - واجهة مستخدم حديثة ومتجاوبة
 * - فلترة ذكية متعددة المعايير
 * - بحث متقدم في الوقت الفعلي
 * - إجراءات مجمعة متطورة
 * - تصدير متعدد الصيغ
 * - إحصائيات فورية
 * - تكامل مع Bootstrap 3.3
 *
 * @package    AYM ERP
 * <AUTHOR> Development Team
 * @copyright  2024 AYM ERP Systems
 * @license    Commercial License
 * @version    1.0.0
 * @since      2024-01-15
 */
#}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        {# أزرار الإجراءات الرئيسية المتقدمة #}
        <div class="btn-group">
          <a href="{{ add }}" data-toggle="tooltip" title="{{ button_add }}" class="btn btn-primary">
            <i class="fa fa-plus"></i> {{ button_add }}
          </a>
          <button type="button" data-toggle="dropdown" class="btn btn-default dropdown-toggle">
            <i class="fa fa-cog"></i> {{ text_actions|default('الإجراءات') }} <span class="caret"></span>
          </button>
          <ul class="dropdown-menu dropdown-menu-right">
            <li><a onclick="bulkAction('delete')"><i class="fa fa-trash-o"></i> {{ text_bulk_delete|default('حذف مجمع') }}</a></li>
            <li><a onclick="bulkAction('print')"><i class="fa fa-print"></i> {{ text_bulk_print|default('طباعة مجمعة') }}</a></li>
            <li><a onclick="bulkAction('export')"><i class="fa fa-download"></i> {{ text_bulk_export|default('تصدير مجمع') }}</a></li>
            <li class="divider"></li>
            <li><a href="{{ export|default('#') }}"><i class="fa fa-file-excel-o"></i> {{ text_export_excel|default('تصدير Excel') }}</a></li>
            <li><a href="{{ export|default('#') }}&format=csv"><i class="fa fa-file-text-o"></i> {{ text_export_csv|default('تصدير CSV') }}</a></li>
            <li><a href="{{ export|default('#') }}&format=pdf"><i class="fa fa-file-pdf-o"></i> {{ text_export_pdf|default('تصدير PDF') }}</a></li>
          </ul>
        </div>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>

  <div class="container-fluid">
    {# رسائل التنبيه المحسنة #}
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible">
      <i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}

    {% if success %}
    <div class="alert alert-success alert-dismissible">
      <i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}

    {# الإحصائيات السريعة المتقدمة #}
    <div class="row" style="margin-bottom: 20px;">
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-primary">
          <div class="panel-body">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-shopping-cart fa-3x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge" id="stat-total-orders">{{ statistics.total_orders|default('0') }}</div>
                <div>{{ text_total_orders|default('إجمالي الطلبات') }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="col-lg-3 col-md-6">
        <div class="panel panel-warning">
          <div class="panel-body">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-clock-o fa-3x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge" id="stat-pending-orders">{{ statistics.pending_orders|default('0') }}</div>
                <div>{{ text_pending_orders|default('الطلبات المعلقة') }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="col-lg-3 col-md-6">
        <div class="panel panel-success">
          <div class="panel-body">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-check-circle fa-3x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge" id="stat-completed-orders">{{ statistics.completed_orders|default('0') }}</div>
                <div>{{ text_completed_orders|default('الطلبات المكتملة') }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="col-lg-3 col-md-6">
        <div class="panel panel-info">
          <div class="panel-body">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-money fa-3x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge" id="stat-total-sales">{{ statistics.total_sales|default('0') }}</div>
                <div>{{ text_total_sales|default('إجمالي المبيعات') }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

      <div class="pull-left">
        {# أزرار إضافية #}
        <button style="display:none" type="button" id="button-sync" data-toggle="tooltip" title="Sync to Odoo" class="btn btn-primary">Sync to Odoo <i class="fa fa-send"></i></button>

        <button type="submit" id="button-shipping-list" form="form-order" formaction="{{ shippingList }}" formtarget="_blank" data-toggle="tooltip" title="طباعة قائمة التسليم لشركة الشحن أو المندوب" class="btn btn-primary">
          <i class="fa fa-truck"></i> طباعة قائمة التسليم
        </button>

        <button type="button" id="button-filter-toggle" data-toggle="tooltip" title="{{ button_filter }}" class="btn btn-default">
          <i class="fa fa-filter"></i> {{ text_filter }}
        </button>



        <button type="submit" id="button-invoice" form="form-order" formaction="{{ invoice }}" formtarget="_blank" data-toggle="tooltip" title="{{ button_invoice_print }}" class="btn btn-info">
          <i class="fa fa-print"></i>
        </button>
        <a href="{{ add }}" data-toggle="tooltip" title="{{ button_add }}" class="btn btn-primary">
          <i class="fa fa-plus"></i>
        </a>
      </div>

    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible">
      <i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    {% if success %}
    <div class="alert alert-success alert-dismissible">
      <i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    <div class="row">
      <div id="filter-order" class="col-12 mb-3 filter-hidden">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-filter"></i> {{ text_filter }}</h3>
          </div>
          <div class="panel-body">
            <div class="form-row">
              <div class="form-group col-md-1">
                <label class="control-label" for="input-order-id">{{ entry_order_id }}</label>
                <input type="text" name="filter_order_id" value="{{ filter_order_id }}" placeholder="{{ entry_order_id }}" id="input-order-id" class="form-control" />
              </div>
              <div class="form-group col-md-2">
                <label class="control-label" for="input-customer">{{ entry_customer }}</label>
                <input type="text" name="filter_customer" value="{{ filter_customer }}" placeholder="{{ entry_customer }}" id="input-customer" class="form-control" />
              </div>
              <div class="form-group col-md-2">
                <label class="control-label" for="input-customer-phone">رقم الموبايل</label>
                <input type="text" name="filter_telephone" value="{{ filter_telephone }}" placeholder="رقم الموبايل" id="input-customer-phone" class="form-control" />
              </div>

              <div class="form-group col-md-1">
                <label class="control-label" for="input-total">{{ entry_total }}</label>
                <input type="text" name="filter_total" value="{{ filter_total }}" placeholder="{{ entry_total }}" id="input-total" class="form-control" />
              </div>
              <div class="form-group col-md-2">
                <label class="control-label" for="input-payment-zone">المنطقة</label>
                <input type="text" name="filter_payment_zone" value="{{ filter_payment_zone }}" placeholder="المنطقة" id="input-payment-zone" class="form-control" />
              </div>
              <div class="form-group col-md-2">
                <label class="control-label" for="input-date-from">تاريخ الاضافة من</label>
                <div class="input-group date">
                  <input type="text" name="filter_date_from" value="{{ filter_date_from }}" placeholder="تاريخ الاضافة من" data-date-format="YYYY-MM-DD" id="input-date-from" class="form-control" />
                  <span class="input-group-btn">
                    <button type="button" class="btn btn-default"><i class="fa fa-calendar"></i></button>
                  </span>
                </div>
              </div>
              <div class="form-group col-md-2">
                <label class="control-label" for="input-date-to">تاريخ الاضافة الى</label>
                <div class="input-group date">
                  <input type="text" name="filter_date_to" value="{{ filter_date_to }}" placeholder="تاريخ الاضافة إلى" data-date-format="YYYY-MM-DD" id="input-date-to" class="form-control" />
                  <span class="input-group-btn">
                    <button type="button" class="btn btn-default"><i class="fa fa-calendar"></i></button>
                  </span>
                </div>
              </div>
              <div class="form-group col-md-10">
                <label class="control-label" for="input-order-status">{{ entry_order_status }}</label>
                <div class="aa">
                  <label class="control-label">
                    <input type="checkbox" class="option-input" name="filter_order_status_id[]" value="0" {% if '0' in filter_order_status_id %}checked{% endif %}> {{ text_missing }}
                  </label>
                  {% for order_status in order_statuses %}
                  <label class="control-label">
                    <input type="checkbox" class="option-input" name="filter_order_status_id[]" value="{{ order_status.order_status_id }}" {% if order_status.order_status_id in filter_order_status_id %}checked{% endif %}> {{ order_status.name }}
                  </label>
                  {% endfor %}
                </div>
              </div>
              <div class="form-group col-md-2">
                <button type="button" id="button-filter" class="btn btn-default" style="margin-top: 25px;">
                  <i class="fa fa-filter"></i> {{ button_filter }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div id="order-list" class="col-md-12 col-sm-12">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-list"></i> {{ text_list }}</h3>
          </div>
          <div class="panel-body">
            <form method="post" action="" enctype="multipart/form-data" id="form-order">
              <div class="table-responsive">
                <table class="table table-bordered table-hover">
                  <thead>
                    <tr>
                      <td style="width: 1px;" class="text-center"><input type="checkbox" onclick="$('input[name*=\'selected\']').prop('checked', this.checked);" /></td>
                      <td class="text-center">{% if sort == 'o.order_id' %} <a href="{{ sort_order }}" class="{{ order|lower }}">{{ column_order_id }}</a> {% else %} <a href="{{ sort_order }}">{{ column_order_id }}</a> {% endif %}</td>
                      <td style="display:none"  class="text-center">رابط دفع</td>
                      <td class="text-center">{% if sort == 'customer' %} <a href="{{ sort_customer }}" class="{{ order|lower }}">{{ column_customer }}</a> {% else %} <a href="{{ sort_customer }}">{{ column_customer }}</a> {% endif %}</td>
                      <td class="text-center">phone</td>
                      <td class="text-center">Address</td>
                      <td class="text-center">{% if sort == 'order_status' %} <a href="{{ sort_status }}" class="{{ order|lower }}">{{ column_status }}</a> {% else %} <a href="{{ sort_status }}">{{ column_status }}</a> {% endif %}</td>
                      <td class="text-center">{% if sort == 'o.total' %} <a href="{{ sort_total }}" class="{{ order|lower }}">{{ column_total }}</a> {% else %} <a href="{{ sort_total }}">{{ column_total }}</a> {% endif %}</td>
                      <td class="text-center">{% if sort == 'o.date_added' %} <a href="{{ sort_date_added }}" class="{{ order|lower }}">{{ column_date_added }}</a> {% else %} <a href="{{ sort_date_added }}">{{ column_date_added }}</a> {% endif %}</td>
                      <td class="text-center">{% if sort == 'o.date_modified' %} <a href="{{ sort_date_modified }}" class="{{ order|lower }}">{{ column_date_modified }}</a> {% else %} <a href="{{ sort_date_modified }}">{{ column_date_modified }}</a> {% endif %}</td>
                      <td class="text-center">{{ column_action }}</td>
                    </tr>
                  </thead>
                  <tbody>
                    {% if orders %}
                    {% for order in orders %}
                    <tr>
                      <td class="text-center">
                        {% if order.order_id in selected %}
                        <input type="checkbox" name="selected[]" value="{{ order.order_id }}" checked="checked" />
                        {% else %}
                        <input type="checkbox" name="selected[]" value="{{ order.order_id }}" />
                        {% endif %}
                        <input type="hidden" name="shipping_code[]" value="{{ order.shipping_code }}" />
                      </td>
                      <td class="text-center">{{ order.order_id }}</td>
                      <td style="display:none"  class="text-center">
                        <a href="{{ order.paymentlink }}" target="_blank" data-toggle="tooltip" title="توليد رابط الدفع" class="btn btn-info">
                          <i class="fa fa-money"></i> رابط الدفع
                        </a>
                      </td>
                      <td class="text-center">{{ order.customer }}</td>
                      <td class="text-center" style="direction:ltr">{{ order.telephone }}</td>
                      <td class="text-center">{{ order.payment_address_1 }} - {{ order.payment_city }} - {{ order.payment_zone }}</td>
                      <td class="text-center">{{ order.order_status }}</td>
                      <td class="text-center">{{ order.total }}</td>
                      <td class="text-center">{{ order.date_added }}</td>
                      <td class="text-center">{{ order.date_modified }}</td>
                      <td class="text-center">
                        <div style="min-width: 120px;">
                          <div class="btn-group">
                            <a href="{{ order.view }}" data-toggle="tooltip" title="{{ button_view }}" class="btn btn-primary">
                              <i class="fa fa-eye"></i>
                            </a>
                            <button style="display:none"  type="button" data-order-id="{{ order.order_id }}" class="btn btn-info sync-order" title="Sync to Odoo"><i class="fa fa-send"></i></button>
                            <button type="button" data-toggle="dropdown" class="btn btn-primary dropdown-toggle">
                              <span class="caret"></span>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-right">
                              <li><a href="{{ order.edit }}"><i class="fa fa-pencil"></i> {{ button_edit }}</a></li>
                              <li><a href="{{ order.order_id }}"><i class="fa fa-trash-o"></i> {{ button_delete }}</a></li>
                            </ul>
                          </div>
                        </div>
                      </td>
                    </tr>
                    {% endfor %}
                    {% else %}
                    <tr>
                      <td class="text-center" colspan="11">{{ text_no_results }}</td>
                    </tr>
                    {% endif %}
                  </tbody>
                </table>
              </div>
            </form>
            <div class="row">
              <div class="col-sm-6 text-left">{{ pagination }}</div>
              <div class="col-sm-6 text-right">{{ results }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
<script type="text/javascript">
$('#button-sync').on('click', function() {
  var selected = $('input[name^=\'selected\']:checked').map(function() {
    return this.value;
  }).get();

  if (selected.length == 0) {
    alert('Please select at least one order to sync.');
    return;
  }

  $.ajax({
    url: 'index.php?route=sale/order/bulkSyncToOdoo&user_token={{ user_token }}',
    type: 'post',
    dataType: 'json',
    data: { selected: selected },
    beforeSend: function() {
      $('#button-sync').button('loading');
    },
    complete: function() {
      $('#button-sync').button('reset');
    },
    success: function(json) {
      if (json) {
        var message = '';
        $.each(json, function(order_id, result) {
          message += 'Order #' + order_id + ': ' + (result.success ? 'Synced' : 'Failed') + '\n';
        });
        alert(message);
      }
    },
    error: function(xhr, ajaxOptions, thrownError) {
      alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
    }
  });
});


// Individual order sync
$('.sync-order').on('click', function() {
  var orderId = $(this).data('order-id');
  $.ajax({
    url: 'index.php?route=sale/order/sendToOdoo&user_token={{ user_token }}&order_id=' + orderId,
    type: 'get',
    dataType: 'json',
    beforeSend: function() {
      $('.sync-order[data-order-id="' + orderId + '"]').button('loading');
    },
    complete: function() {
      $('.sync-order[data-order-id="' + orderId + '"]').button('reset');
    },
    success: function(json) {
      alert(json.message);
    },
    error: function(xhr, ajaxOptions, thrownError) {
      alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
    }
  });
});
</script>

  <script type="text/javascript">
  $(document).ready(function() {
    $('#button-filter-toggle').on('click', function() {
      $('#filter-order').toggleClass('filter-visible filter-hidden');
    });

    $('#button-filter').on('click', function() {
      var url = '';

      var filter_order_id = $('#input-order-id').val();
      if (filter_order_id) {
        url += '&filter_order_id=' + encodeURIComponent(filter_order_id);
      }

      var filter_customer = $('#input-customer').val();
      if (filter_customer) {
        url += '&filter_customer=' + encodeURIComponent(filter_customer);
      }

      var filter_telephone = $('#input-customer-phone').val();
      if (filter_telephone) {
        url += '&filter_telephone=' + encodeURIComponent(filter_telephone);
      }

         var ids = [];
         var filter_order_status_id =  document.getElementsByName("filter_order_status_id[]");

        $(filter_order_status_id).each(function(i, e) {
               if($(this).is(':checked')) {
               ids.push($(this).val());
               }else{
               }
        });


        	if (ids.length > 0) {
        		url += '&filter_order_status_id=' + ids.join(',');
        	}
      var filter_total = $('#input-total').val();
      if (filter_total) {
        url += '&filter_total=' + encodeURIComponent(filter_total);
      }

      var filter_payment_zone = $('#input-payment-zone').val();
      if (filter_payment_zone) {
        url += '&filter_payment_zone=' + encodeURIComponent(filter_payment_zone);
      }

      var filter_date_from = $('#input-date-from').val();
      if (filter_date_from) {
        url += '&filter_date_from=' + encodeURIComponent(filter_date_from);
      }

      var filter_date_to = $('#input-date-to').val();
      if (filter_date_to) {
        url += '&filter_date_to=' + encodeURIComponent(filter_date_to);
      }

      location = 'index.php?route=sale/order&user_token={{ user_token }}' + url;
    });
  });

  $('#input-customer').autocomplete({
    'source': function(request, response) {
      $.ajax({
        url: 'index.php?route=customer/customer/autocomplete&user_token={{ user_token }}&filter_name=' + encodeURIComponent(request),
        dataType: 'json',
        success: function(json) {
          response($.map(json, function(item) {
            return {
              label: item['name'],
              value: item['customer_id']
            };
          }));
        }
      });
    },
    'select': function(item) {
      $('#input-customer').val(item['label']);
    }
  });

  $('input[name^="selected"]').on('change', function() {
    $('#button-shipping, #button-invoice, #button-shipping-list').prop('disabled', true);

    var selected = $('input[name^="selected"]:checked');

    if (selected.length) {
      $('#button-invoice').prop('disabled', false);
      $('#button-shipping-list').prop('disabled', false);
    }

    for (i = 0; i < selected.length; i++) {
      if ($(selected[i]).parent().find('input[name^="shipping_code"]').val()) {
        $('#button-shipping').prop('disabled', false);
        break;
      }
    }
  });

  $('#button-shipping, #button-invoice, #button-shipping-list').prop('disabled', true);

  $('#button-shipping, #button-invoice, #button-shipping-list').on('click', function(e) {
    $('#form-order').attr('action', this.getAttribute('formAction'));
  });

  $('#form-order li:last-child a').on('click', function(e) {
    e.preventDefault();

    var element = this;

    if (confirm('{{ text_confirm }}')) {
      $.ajax({
        url: '{{ catalog }}index.php?route=api/order/delete&api_token={{ api_token }}&store_id={{ store_id }}&order_id=' + $(element).attr('href'),
        dataType: 'json',
        beforeSend: function() {
          $(element).parent().parent().parent().find('button').button('loading');
        },
        complete: function() {
          $(element).parent().parent().parent().find('button').button('reset');
        },
        success: function(json) {
          $('.alert-dismissible').remove();

          if (json['error']) {
            $('#content > .container-fluid').prepend('<div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> ' + json['error'] + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
          }

          if (json['success']) {
            location = '{{ delete }}';
          }
        },
        error: function(xhr, ajaxOptions, thrownError) {
          alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
      });
    }
  });

  $('.date').datetimepicker({
    language: '{{ datepicker }}',
    pickTime: false
  });

  // الميزات المتقدمة الجديدة

  // الإجراءات المجمعة المتقدمة
  function bulkAction(action) {
    var selected = [];
    $('input[name*=\'selected\']:checked').each(function() {
      selected.push($(this).val());
    });

    if (selected.length < 1) {
      alert('{{ error_select_items|default("يرجى اختيار عنصر واحد على الأقل") }}');
      return false;
    }

    if (action == 'delete' && !confirm('{{ text_confirm|default("هل أنت متأكد؟") }}')) {
      return false;
    }

    $.ajax({
      url: 'index.php?route=sale/order/bulkAction&user_token={{ user_token }}',
      type: 'post',
      data: {
        action: action,
        selected: selected
      },
      dataType: 'json',
      beforeSend: function() {
        $('#loading-overlay').show();
      },
      complete: function() {
        $('#loading-overlay').hide();
      },
      success: function(json) {
        if (json['error']) {
          alert(json['error']);
        }

        if (json['success']) {
          alert(json['success']);
          location.reload();
        }
      },
      error: function(xhr, ajaxOptions, thrownError) {
        alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
      }
    });
  }

  // تحديث الإحصائيات في الوقت الفعلي
  function updateStatistics() {
    $.ajax({
      url: 'index.php?route=sale/order/getStatistics&user_token={{ user_token }}',
      type: 'get',
      dataType: 'json',
      success: function(json) {
        if (json['statistics']) {
          $('#stat-total-orders').text(json['statistics']['total_orders'] || '0');
          $('#stat-pending-orders').text(json['statistics']['pending_orders'] || '0');
          $('#stat-completed-orders').text(json['statistics']['completed_orders'] || '0');
          $('#stat-total-sales').text(json['statistics']['total_sales'] || '0');
        }
      }
    });
  }

  // تحديث الإحصائيات كل 30 ثانية
  setInterval(updateStatistics, 30000);

  // البحث المتقدم في الوقت الفعلي
  var searchTimeout;
  $('#input-customer, #input-order-id').on('keyup', function() {
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(function() {
      $('#button-filter').click();
    }, 1000);
  });

  // تفعيل التلميحات
  $('[data-toggle="tooltip"]').tooltip();

  // تحسين تجربة المستخدم
  $(document).ready(function() {
    // إضافة تأثيرات بصرية
    $('.panel').hover(
      function() { $(this).addClass('panel-hover'); },
      function() { $(this).removeClass('panel-hover'); }
    );

    // تحسين الجدول
    $('.table tbody tr').hover(
      function() { $(this).addClass('active'); },
      function() { $(this).removeClass('active'); }
    );
  });

  </script>

  {# CSS إضافي للميزات المتقدمة #}
  <style>
    .huge {
      font-size: 40px;
      font-weight: bold;
    }

    .panel-hover {
      box-shadow: 0 4px 8px rgba(0,0,0,0.1);
      transform: translateY(-2px);
      transition: all 0.3s ease;
    }

    .table tbody tr.active {
      background-color: #f5f5f5;
    }

    #loading-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.5);
      z-index: 9999;
      display: none;
    }

    #loading-overlay .loading-content {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: white;
      padding: 20px;
      border-radius: 5px;
      text-align: center;
    }

    .btn-group .dropdown-menu {
      min-width: 200px;
    }

    .statistics-card {
      transition: all 0.3s ease;
    }

    .statistics-card:hover {
      transform: scale(1.05);
    }
  </style>

  {# Loading Overlay #}
  <div id="loading-overlay">
    <div class="loading-content">
      <i class="fa fa-spinner fa-spin fa-3x"></i>
      <p>{{ text_processing|default('جاري المعالجة...') }}</p>
    </div>
  </div>
  <style>
    .filter-hidden {
      display: none;
    }

    #filter-order {
      transition: all 0.3s ease;
    }

    #order-list {
      transition: all 0.3s ease;
    }

    .checkbox-group label {
      display: block;
    }

    .aa {
      padding: 1px;
    }

    .aa > label {
      display: inline-block;
      line-height: 40px;
      text-align: right;
      min-width: 120px;
    }

    @media (min-width: 992px) {
      #button-filter-toggle {
        display: inline-block;
      }

      #filter-order.filter-visible {
        display: block;
      }

      #filter-order.filter-visible + #order-list {
        width: 100%;
      }
    }

    @media (max-width: 991px) {
      #filter-order {
        position: fixed;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        z-index: 1000;
        background: white;
        overflow-y: auto;
        padding: 20px;
      }

      #filter-order.filter-visible {
        left: 0;
      }
    }

    .option-input {
      -webkit-appearance: none;
      -moz-appearance: none;
      -ms-appearance: none;
      -o-appearance: none;
      appearance: none;
      position: relative;
      top: 12.33333px;
      right: 0px;
      bottom: 0;
      left: 0;
      height: 30px !important;
      width: 30px !important;
      transition: all 0.5s ease-out 0s;
      background: #cbd1d8;
      border: none;
      color: #fff;
      cursor: pointer;
      display: inline-block;
      outline: none;
      position: relative;
      z-index: 2;
    }

    .option-input:hover {
      background: #333;
    }

    .option-input:checked {
      background: #000;
    }

    .option-input:checked::before {
      height: 30px !important;
      width: 30px !important;
      position: absolute;
      content: '✔';
      display: inline-block;
      font-size: 20.66667px;
      text-align: center;
      line-height: 30px;
    }

    .option-input:checked::after {
      -webkit-animation: click-wave 0.65s;
      -moz-animation: click-wave 0.65s;
      animation: click-wave 0.65s;
      background: #40e0d0;
      content: '';
      display: block;
      position: relative;
      z-index: 1;
      width: 30px;
      height: 30px;
      top: 0 !important;
      right: 0 !important;
    }

    .option-input.radio {
      border-radius: 50%;
    }

    .option-input.radio::after {
      border-radius: 50%;
    }
  </style>


</div>
{{ footer }}
