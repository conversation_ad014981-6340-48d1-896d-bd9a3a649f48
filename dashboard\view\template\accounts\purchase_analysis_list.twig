{{ header }}{{ column_left }}
<div id="content">
<style>
/* تحسينات متقدمة لتحليل المشتريات - Enterprise Grade */
.panel-default {
    border: none;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.panel-default:hover {
    box-shadow: 0 0.25rem 2rem 0 rgba(58, 59, 69, 0.2);
}

.panel-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    border-radius: 8px 8px 0 0;
    padding: 15px;
}

/* تحسين الجدول */
.table {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 0.125rem 0.25rem rgba(0,0,0,0.075);
}

.table thead th {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    border: none;
    font-weight: 600;
    text-align: center;
}

.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
    transform: translateX(2px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* تحسين القيم */
.purchase-amount {
    color: #dc3545;
    font-weight: 600;
}

.quantity-amount {
    color: #007bff;
    font-weight: 600;
}

.cost-amount {
    color: #6f42c1;
    font-weight: 600;
}

.savings-amount {
    color: #28a745;
    font-weight: 600;
}

/* تحسين الفلاتر */
.form-inline {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.form-group {
    flex-grow: 1;
    margin-bottom: 10px;
    margin-right: 10px;
}

.form-control {
    border-radius: 6px;
    border: 1px solid #ced4da;
    transition: all 0.2s ease;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
    transform: scale(1.02);
}

/* تحسين الأزرار */
.btn {
    transition: all 0.2s ease;
    border-radius: 6px;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
    border: none;
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8 0%, #117a8b 100%);
    border: none;
}

/* تحسين ملخص المشتريات */
.purchase-summary {
    background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
    border-left: 4px solid #dc3545;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.purchase-summary h4 {
    color: #c53030;
    margin-top: 0;
    font-weight: 600;
}

/* تحسين بطاقات الإحصائيات */
.stats-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 15px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    text-align: center;
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.stats-card-icon {
    font-size: 2.5em;
    margin-bottom: 10px;
}

.stats-card-value {
    font-size: 1.8em;
    font-weight: 700;
    margin-bottom: 5px;
}

.stats-card-label {
    color: #6c757d;
    font-size: 0.9em;
}

.stats-purchases {
    border-left: 4px solid #dc3545;
}

.stats-orders {
    border-left: 4px solid #007bff;
}

.stats-average {
    border-left: 4px solid #17a2b8;
}

.stats-suppliers {
    border-left: 4px solid #6f42c1;
}

/* تحسين الرسوم البيانية */
.chart-container {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 0.125rem 0.25rem rgba(0,0,0,0.075);
    margin-bottom: 20px;
}

.chart-title {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 15px;
    text-align: center;
}

/* تحسين قوائم أفضل المنتجات والموردين */
.top-list {
    background: white;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.top-list-title {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 15px;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 10px;
}

.top-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #f8f9fa;
}

.top-item:last-child {
    border-bottom: none;
}

.top-item-name {
    font-weight: 500;
    color: #2c3e50;
}

.top-item-value {
    font-weight: 600;
    color: #dc3545;
}

/* تحسين تحليل التكاليف */
.cost-analysis {
    background: linear-gradient(135deg, #f8f4ff 0%, #e9d8fd 100%);
    border-left: 4px solid #6f42c1;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.cost-analysis h5 {
    color: #553c9a;
    margin-bottom: 10px;
    font-weight: 600;
}

.cost-metric {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
}

.cost-metric-label {
    color: #6c757d;
}

.cost-metric-value {
    font-weight: 600;
    color: #6f42c1;
}

/* تحسين التبويبات */
.nav-tabs {
    border-bottom: 2px solid #e9ecef;
    margin-bottom: 20px;
}

.nav-tabs .nav-link {
    border: none;
    border-radius: 0;
    color: #6c757d;
    font-weight: 500;
    padding: 12px 20px;
    transition: all 0.3s ease;
}

.nav-tabs .nav-link:hover {
    border-color: transparent;
    color: #007bff;
    background-color: #f8f9fa;
}

.nav-tabs .nav-link.active {
    color: #007bff;
    background-color: transparent;
    border-color: transparent;
    border-bottom: 2px solid #007bff;
}
</style>

<div class="page-header">
    <div class="container-fluid">
        <div class="pull-right">
            <button type="button" data-toggle="tooltip" title="{{ button_filter }}" onclick="$('#filter-purchase').toggle();" class="btn btn-default"><i class="fa fa-filter"></i></button>
            <button type="button" data-toggle="tooltip" title="تصدير Excel" onclick="exportPurchaseAnalysis('excel')" class="btn btn-success"><i class="fa fa-download"></i></button>
            <button type="button" data-toggle="tooltip" title="{{ button_print }}" onclick="printPurchaseAnalysis()" class="btn btn-info"><i class="fa fa-print"></i></button>
        </div>
        <h1>{{ heading_title }}</h1>
        <ul class="breadcrumb">
            {% for breadcrumb in breadcrumbs %}
            <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
            {% endfor %}
        </ul>
    </div>
</div>

<div class="container-fluid">
    <!-- ملخص المشتريات -->
    <div class="purchase-summary">
        <h4><i class="fa fa-shopping-cart"></i> ملخص تحليل المشتريات</h4>
        <div class="row">
            <div class="col-md-3">
                <div class="stats-card stats-purchases">
                    <div class="stats-card-icon">
                        <i class="fa fa-money-bill text-danger"></i>
                    </div>
                    <div class="stats-card-value purchase-amount">{{ total_purchases|default('0.00') }}</div>
                    <div class="stats-card-label">إجمالي المشتريات</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card stats-orders">
                    <div class="stats-card-icon">
                        <i class="fa fa-file-invoice text-primary"></i>
                    </div>
                    <div class="stats-card-value quantity-amount">{{ total_orders|default('0') }}</div>
                    <div class="stats-card-label">عدد أوامر الشراء</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card stats-average">
                    <div class="stats-card-icon">
                        <i class="fa fa-calculator text-info"></i>
                    </div>
                    <div class="stats-card-value cost-amount">{{ average_order|default('0.00') }}</div>
                    <div class="stats-card-label">متوسط أمر الشراء</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card stats-suppliers">
                    <div class="stats-card-icon">
                        <i class="fa fa-truck text-purple"></i>
                    </div>
                    <div class="stats-card-value">{{ top_suppliers|length|default('0') }}</div>
                    <div class="stats-card-label">الموردون النشطون</div>
                </div>
            </div>
        </div>
    </div>

    <!-- تحليل التكاليف -->
    {% if cost_analysis %}
    <div class="cost-analysis">
        <h5><i class="fa fa-chart-pie"></i> تحليل التكاليف المتقدم</h5>
        <div class="row">
            <div class="col-md-6">
                <div class="cost-metric">
                    <span class="cost-metric-label">تكلفة البضائع المباعة:</span>
                    <span class="cost-metric-value">{{ cost_analysis.cogs|default('0.00') }}</span>
                </div>
                <div class="cost-metric">
                    <span class="cost-metric-label">هامش الربح الإجمالي:</span>
                    <span class="cost-metric-value">{{ cost_analysis.gross_margin|default('0.00') }}%</span>
                </div>
            </div>
            <div class="col-md-6">
                <div class="cost-metric">
                    <span class="cost-metric-label">وفورات الكمية:</span>
                    <span class="cost-metric-value savings-amount">{{ cost_analysis.bulk_savings|default('0.00') }}</span>
                </div>
                <div class="cost-metric">
                    <span class="cost-metric-label">متوسط تكلفة الوحدة:</span>
                    <span class="cost-metric-value">{{ cost_analysis.avg_unit_cost|default('0.00') }}</span>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- فلاتر البحث -->
    <div id="filter-purchase" class="well" style="display: none;">
        <div class="row">
            <div class="col-sm-4">
                <div class="form-group">
                    <label class="control-label" for="input-date-start">تاريخ البداية</label>
                    <div class="input-group date">
                        <input type="text" name="filter_date_start" value="{{ filter_date_start }}" placeholder="تاريخ البداية" data-date-format="YYYY-MM-DD" id="input-date-start" class="form-control" />
                        <span class="input-group-btn">
                            <button class="btn btn-default" type="button"><i class="fa fa-calendar"></i></button>
                        </span>
                    </div>
                </div>
            </div>
            <div class="col-sm-4">
                <div class="form-group">
                    <label class="control-label" for="input-date-end">تاريخ النهاية</label>
                    <div class="input-group date">
                        <input type="text" name="filter_date_end" value="{{ filter_date_end }}" placeholder="تاريخ النهاية" data-date-format="YYYY-MM-DD" id="input-date-end" class="form-control" />
                        <span class="input-group-btn">
                            <button class="btn btn-default" type="button"><i class="fa fa-calendar"></i></button>
                        </span>
                    </div>
                </div>
            </div>
            <div class="col-sm-4">
                <div class="form-group">
                    <label class="control-label" for="input-supplier">المورد</label>
                    <select name="filter_supplier" id="input-supplier" class="form-control select2">
                        <option value="">جميع الموردين</option>
                        {% for supplier in suppliers %}
                        <option value="{{ supplier.supplier_id }}" {% if supplier.supplier_id == filter_supplier %}selected{% endif %}>{{ supplier.name }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-4">
                <div class="form-group">
                    <label class="control-label" for="input-product">المنتج</label>
                    <select name="filter_product" id="input-product" class="form-control select2">
                        <option value="">جميع المنتجات</option>
                        {% for product in products %}
                        <option value="{{ product.product_id }}" {% if product.product_id == filter_product %}selected{% endif %}>{{ product.name }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            <div class="col-sm-4">
                <div class="form-group">
                    <label class="control-label" for="input-category">الفئة</label>
                    <select name="filter_category" id="input-category" class="form-control select2">
                        <option value="">جميع الفئات</option>
                        {% for category in categories %}
                        <option value="{{ category.category_id }}" {% if category.category_id == filter_category %}selected{% endif %}>{{ category.name }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            <div class="col-sm-4">
                <button type="button" id="button-filter" class="btn btn-primary" style="margin-top: 25px;"><i class="fa fa-search"></i> فلترة</button>
            </div>
        </div>
    </div>

    <!-- التبويبات -->
    <ul class="nav nav-tabs" id="purchaseTabs" role="tablist">
        <li class="nav-item">
            <a class="nav-link active" id="overview-tab" data-toggle="tab" href="#overview" role="tab">نظرة عامة</a>
        </li>
        <li class="nav-item">
            <a class="nav-link" id="products-tab" data-toggle="tab" href="#products" role="tab">أكثر المنتجات شراءً</a>
        </li>
        <li class="nav-item">
            <a class="nav-link" id="suppliers-tab" data-toggle="tab" href="#suppliers" role="tab">أفضل الموردين</a>
        </li>
        <li class="nav-item">
            <a class="nav-link" id="trends-tab" data-toggle="tab" href="#trends" role="tab">اتجاهات التكلفة</a>
        </li>
    </ul>

    <div class="tab-content" id="purchaseTabsContent">
        <!-- تبويب النظرة العامة -->
        <div class="tab-pane fade show active" id="overview" role="tabpanel">
            <div class="row">
                <div class="col-md-8">
                    <div class="chart-container">
                        <div class="chart-title">اتجاه المشتريات الشهرية</div>
                        <canvas id="purchaseTrendChart" width="400" height="200"></canvas>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="chart-container">
                        <div class="chart-title">توزيع المشتريات حسب الفئة</div>
                        <canvas id="categoryChart" width="300" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- تبويب أكثر المنتجات شراءً -->
        <div class="tab-pane fade" id="products" role="tabpanel">
            <div class="top-list">
                <div class="top-list-title"><i class="fa fa-box"></i> أكثر المنتجات شراءً</div>
                {% if top_products %}
                {% for product in top_products %}
                <div class="top-item">
                    <div class="top-item-name">{{ product.name }}</div>
                    <div class="top-item-value">{{ product.total }}</div>
                </div>
                {% endfor %}
                {% else %}
                <div class="text-center text-muted">لا توجد بيانات متاحة</div>
                {% endif %}
            </div>
        </div>

        <!-- تبويب أفضل الموردين -->
        <div class="tab-pane fade" id="suppliers" role="tabpanel">
            <div class="top-list">
                <div class="top-list-title"><i class="fa fa-truck"></i> أفضل الموردين</div>
                {% if top_suppliers %}
                {% for supplier in top_suppliers %}
                <div class="top-item">
                    <div class="top-item-name">{{ supplier.name }}</div>
                    <div class="top-item-value">{{ supplier.total }}</div>
                </div>
                {% endfor %}
                {% else %}
                <div class="text-center text-muted">لا توجد بيانات متاحة</div>
                {% endif %}
            </div>
        </div>

        <!-- تبويب اتجاهات التكلفة -->
        <div class="tab-pane fade" id="trends" role="tabpanel">
            <div class="chart-container">
                <div class="chart-title">تحليل اتجاهات التكلفة والتوقعات</div>
                <canvas id="costTrendsChart" width="400" height="300"></canvas>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript"><!--
// تحسينات متقدمة لتحليل المشتريات
$(document).ready(function() {
    // تهيئة Select2
    $('.select2').select2({
        placeholder: 'اختر من القائمة',
        allowClear: true
    });
    
    // تهيئة فلاتر التاريخ
    $('.input-group.date').datetimepicker({
        pickTime: false,
        format: 'YYYY-MM-DD'
    });
    
    // إضافة تأثيرات للبطاقات
    $('.stats-card').each(function() {
        $(this).on('click', function() {
            $(this).toggleClass('selected');
        });
    });
    
    // إنشاء الرسوم البيانية
    initializePurchaseCharts();
    
    // إضافة مؤشر تحميل للعمليات
    $(document).ajaxStart(function() {
        $('body').addClass('loading');
    }).ajaxStop(function() {
        $('body').removeClass('loading');
    });
});

// دالة تهيئة الرسوم البيانية
function initializePurchaseCharts() {
    // رسم اتجاه المشتريات
    if ($('#purchaseTrendChart').length) {
        const ctx = document.getElementById('purchaseTrendChart').getContext('2d');
        const purchaseTrendChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [{% for month in purchases_by_month %}'{{ month.month }}'{% if not loop.last %},{% endif %}{% endfor %}],
                datasets: [{
                    label: 'المشتريات',
                    data: [{% for month in purchases_by_month %}{{ month.total }}{% if not loop.last %},{% endif %}{% endfor %}],
                    borderColor: '#dc3545',
                    backgroundColor: 'rgba(220,53,69,0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }
    
    // رسم توزيع الفئات
    if ($('#categoryChart').length) {
        const ctx2 = document.getElementById('categoryChart').getContext('2d');
        const categoryChart = new Chart(ctx2, {
            type: 'doughnut',
            data: {
                labels: [{% for category in purchases_by_category %}'{{ category.name }}'{% if not loop.last %},{% endif %}{% endfor %}],
                datasets: [{
                    data: [{% for category in purchases_by_category %}{{ category.total }}{% if not loop.last %},{% endif %}{% endfor %}],
                    backgroundColor: ['#dc3545', '#6f42c1', '#fd7e14', '#20c997', '#6610f2'],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                legend: {
                    position: 'bottom'
                }
            }
        });
    }
}

// دالة تصدير تحليل المشتريات
function exportPurchaseAnalysis(format) {
    const startDate = $('#input-date-start').val();
    const endDate = $('#input-date-end').val();
    const supplier = $('#input-supplier').val();
    const product = $('#input-product').val();
    const category = $('#input-category').val();
    
    const url = 'index.php?route=accounts/purchase_analysis/export&format=' + format + 
                '&date_start=' + startDate + '&date_end=' + endDate +
                '&supplier=' + supplier + '&product=' + product + '&category=' + category;
    window.open(url, '_blank');
}

// دالة طباعة تحليل المشتريات
function printPurchaseAnalysis() {
    window.print();
}

// دالة فلترة البيانات
$('#button-filter').on('click', function() {
    const startDate = $('#input-date-start').val();
    const endDate = $('#input-date-end').val();
    const supplier = $('#input-supplier').val();
    const product = $('#input-product').val();
    const category = $('#input-category').val();
    
    const url = 'index.php?route=accounts/purchase_analysis&filter_date_start=' + startDate + 
                '&filter_date_end=' + endDate + '&filter_supplier=' + supplier +
                '&filter_product=' + product + '&filter_category=' + category;
    window.location = url;
});
//--></script>
{{ footer }}