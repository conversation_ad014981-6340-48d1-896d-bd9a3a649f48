{{ header }}{{ column_left }}

<!-- Enterprise Grade Plus CSS for Inventory Valuation -->
<style>
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --danger-color: #e74c3c;
    --warning-color: #f39c12;
    --info-color: #17a2b8;
    --inventory-color: #8e44ad;
    --light-bg: #f8f9fa;
    --border-color: #dee2e6;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

.inventory-valuation-container {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: var(--shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.inventory-valuation-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--inventory-color), var(--primary-color), var(--secondary-color));
}

.inventory-valuation-header {
    text-align: center;
    border-bottom: 3px solid var(--inventory-color);
    padding-bottom: 20px;
    margin-bottom: 30px;
    position: relative;
}

.inventory-valuation-header h2 {
    color: var(--inventory-color);
    font-weight: 700;
    font-size: 1.8rem;
    margin: 0;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.inventory-valuation-summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.inventory-valuation-summary-card {
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    text-align: center;
    box-shadow: var(--shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.inventory-valuation-summary-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0,0,0,0.1);
}

.inventory-valuation-summary-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
}

.inventory-valuation-summary-card.total::before { background: var(--inventory-color); }
.inventory-valuation-summary-card.cost::before { background: var(--info-color); }
.inventory-valuation-summary-card.value::before { background: var(--success-color); }
.inventory-valuation-summary-card.variance::before { background: var(--warning-color); }

.inventory-valuation-summary-card h4 {
    color: var(--primary-color);
    margin-bottom: 10px;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.inventory-valuation-summary-card .amount {
    font-size: 1.4rem;
    font-weight: 700;
    font-family: 'Courier New', monospace;
    margin-bottom: 5px;
}

.inventory-valuation-summary-card .description {
    font-size: 0.8rem;
    color: #6c757d;
    font-weight: 500;
}

.card-total .amount { color: var(--inventory-color); }
.card-cost .amount { color: var(--info-color); }
.card-value .amount { color: var(--success-color); }
.card-variance .amount { color: var(--warning-color); }

.inventory-valuation-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    background: #ffffff;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.inventory-valuation-table th {
    background: linear-gradient(135deg, var(--inventory-color), #7d3c98);
    color: white;
    padding: 15px 12px;
    text-align: center;
    font-weight: 600;
    font-size: 0.9rem;
    border-bottom: 2px solid var(--border-color);
}

.inventory-valuation-table td {
    padding: 12px;
    border-bottom: 1px solid var(--border-color);
    text-align: center;
    transition: var(--transition);
}

.inventory-valuation-table tbody tr:hover {
    background: var(--light-bg);
    transform: scale(1.005);
}

.inventory-valuation-table tbody tr:nth-child(even) {
    background: #fafbfc;
}

.amount-cell {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    text-align: {{ direction == 'rtl' ? 'right' : 'left' }};
}

.amount-positive { 
    color: var(--success-color); 
    font-weight: 600;
}

.amount-negative { 
    color: var(--danger-color); 
    font-weight: 600;
}

.amount-neutral { 
    color: var(--secondary-color); 
    font-weight: 600;
}

/* Chart Container */
.chart-container {
    background: #ffffff;
    border-radius: var(--border-radius);
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: var(--shadow);
}

.chart-container canvas {
    max-height: 400px;
}

/* Filter Panel */
.filter-panel {
    background: linear-gradient(135deg, #f8f9fa, #ffffff);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: var(--shadow);
}

/* RTL Support */
[dir="rtl"] .inventory-valuation-table {
    direction: rtl;
}

[dir="rtl"] .amount-cell {
    text-align: right;
}

/* Print Styles */
@media print {
    .inventory-valuation-container {
        box-shadow: none;
        border: 1px solid #000;
    }
    
    .inventory-valuation-table th {
        background: #f0f0f0 !important;
        color: #000 !important;
    }
    
    .btn, .card-header, .filter-panel {
        display: none !important;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .inventory-valuation-table {
        font-size: 0.8rem;
    }
    
    .inventory-valuation-table th,
    .inventory-valuation-table td {
        padding: 8px 6px;
    }
    
    .inventory-valuation-summary-cards {
        grid-template-columns: repeat(2, 1fr);
    }
}
</style>

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-{{ direction == 'rtl' ? 'left' : 'right' }}">
        <div class="btn-group" role="group" aria-label="{{ text_actions }}">
          <button type="button" class="btn btn-success btn-lg" onclick="generateValuation()"
                  data-toggle="tooltip" title="{{ text_generate_valuation }}">
            <i class="fas fa-calculator"></i> {{ button_generate }}
          </button>
          <div class="btn-group" role="group">
            <button type="button" class="btn btn-info dropdown-toggle" data-toggle="dropdown"
                    aria-expanded="false" data-toggle="tooltip" title="{{ text_export_options }}">
              <i class="fas fa-download"></i> {{ text_export }}
            </button>
            <ul class="dropdown-menu">
              <li><a href="#" onclick="exportValuation('excel')">
                <i class="fas fa-file-excel text-success"></i> Excel
              </a></li>
              <li><a href="#" onclick="exportValuation('pdf')">
                <i class="fas fa-file-pdf text-danger"></i> PDF
              </a></li>
              <li><a href="#" onclick="exportValuation('csv')">
                <i class="fas fa-file-csv text-info"></i> CSV
              </a></li>
              <li class="divider"></li>
              <li><a href="#" onclick="printValuation()">
                <i class="fas fa-print text-muted"></i> {{ text_print }}
              </a></li>
            </ul>
          </div>
          <button type="button" class="btn btn-default" onclick="showAnalysis()"
                  data-toggle="tooltip" title="{{ text_inventory_analysis }}">
            <i class="fas fa-chart-bar"></i>
          </button>
          <button type="button" class="btn btn-default" onclick="showVarianceAnalysis()"
                  data-toggle="tooltip" title="{{ text_variance_analysis }}">
            <i class="fas fa-balance-scale"></i>
          </button>
        </div>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li class="breadcrumb-item"><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>

  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible">
      <button type="button" class="close" data-dismiss="alert">&times;</button>
      <i class="fas fa-exclamation-triangle"></i>
      {{ error_warning }}
    </div>
    {% endif %}

    {% if success %}
    <div class="alert alert-success alert-dismissible">
      <button type="button" class="close" data-dismiss="alert">&times;</button>
      <i class="fas fa-check-circle"></i>
      {{ success }}
    </div>
    {% endif %}

    <!-- Filter Panel -->
    <div class="filter-panel">
      <h4>{{ text_valuation_filters }}</h4>
      <form id="inventory-valuation-form" method="post" action="{{ action }}">
        <div class="row">
          <div class="col-md-3">
            <div class="form-group">
              <label for="date_start" class="control-label">{{ entry_date_start }}</label>
              <input type="date" name="date_start" id="date_start" value="{{ date_start }}" class="form-control" required>
            </div>
          </div>
          <div class="col-md-3">
            <div class="form-group">
              <label for="date_end" class="control-label">{{ entry_date_end }}</label>
              <input type="date" name="date_end" id="date_end" value="{{ date_end }}" class="form-control" required>
            </div>
          </div>
          <div class="col-md-2">
            <div class="form-group">
              <label for="category_id" class="control-label">{{ entry_category }}</label>
              <select name="category_id" id="category_id" class="form-control select2">
                <option value="">{{ text_all_categories }}</option>
                {% for category in categories %}
                <option value="{{ category.category_id }}"{% if category.category_id == category_id %} selected{% endif %}>{{ category.name }}</option>
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="col-md-2">
            <div class="form-group">
              <label for="warehouse_id" class="control-label">{{ entry_warehouse }}</label>
              <select name="warehouse_id" id="warehouse_id" class="form-control">
                <option value="">{{ text_all_warehouses }}</option>
                {% for warehouse in warehouses %}
                <option value="{{ warehouse.warehouse_id }}"{% if warehouse.warehouse_id == warehouse_id %} selected{% endif %}>{{ warehouse.name }}</option>
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="col-md-2">
            <div class="form-group">
              <label class="control-label">&nbsp;</label>
              <div>
                <button type="submit" class="btn btn-primary btn-block">
                  <i class="fas fa-search"></i> {{ button_filter }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>

    <!-- Inventory Valuation Content -->
    {% if inventory_items %}
    <!-- Summary Cards -->
    <div class="inventory-valuation-summary-cards">
      <div class="inventory-valuation-summary-card card-total total">
        <h4>{{ text_total_items }}</h4>
        <div class="amount">{{ summary.total_items }}</div>
        <div class="description">{{ text_inventory_items }}</div>
      </div>
      <div class="inventory-valuation-summary-card card-cost cost">
        <h4>{{ text_total_cost }}</h4>
        <div class="amount">{{ summary.total_cost_formatted }}</div>
        <div class="description">{{ text_cost_value }}</div>
      </div>
      <div class="inventory-valuation-summary-card card-value value">
        <h4>{{ text_market_value }}</h4>
        <div class="amount">{{ summary.market_value_formatted }}</div>
        <div class="description">{{ text_current_market_value }}</div>
      </div>
      <div class="inventory-valuation-summary-card card-variance variance">
        <h4>{{ text_variance }}</h4>
        <div class="amount">{{ summary.variance_formatted }}</div>
        <div class="description">{{ text_cost_vs_market }}</div>
      </div>
    </div>

    <!-- Inventory Valuation Table -->
    <div class="inventory-valuation-container">
      <div class="inventory-valuation-header">
        <h2>{{ text_inventory_valuation_details }}</h2>
      </div>

      <div class="table-responsive">
        <table class="inventory-valuation-table" id="inventory-valuation-table">
          <thead>
            <tr>
              <th>{{ column_product_name }}</th>
              <th>{{ column_product_code }}</th>
              <th>{{ column_category }}</th>
              <th>{{ column_warehouse }}</th>
              <th>{{ column_quantity }}</th>
              <th>{{ column_unit_cost }}</th>
              <th>{{ column_total_cost }}</th>
              <th>{{ column_market_price }}</th>
              <th>{{ column_market_value }}</th>
              <th>{{ column_variance }}</th>
              <th>{{ column_variance_percent }}</th>
            </tr>
          </thead>
          <tbody>
            {% for item in inventory_items %}
            <tr data-product-id="{{ item.product_id }}">
              <td>
                <strong>{{ item.product_name }}</strong>
                <br>
                <small class="text-muted">{{ item.model }}</small>
              </td>
              <td>{{ item.product_code }}</td>
              <td>{{ item.category_name }}</td>
              <td>{{ item.warehouse_name }}</td>
              <td class="amount-cell">
                <span class="amount-neutral">{{ item.quantity_formatted }}</span>
              </td>
              <td class="amount-cell">
                <span class="amount-neutral">{{ item.unit_cost_formatted }}</span>
              </td>
              <td class="amount-cell">
                <strong class="amount-neutral">{{ item.total_cost_formatted }}</strong>
              </td>
              <td class="amount-cell">
                <span class="amount-positive">{{ item.market_price_formatted }}</span>
              </td>
              <td class="amount-cell">
                <strong class="amount-positive">{{ item.market_value_formatted }}</strong>
              </td>
              <td class="amount-cell">
                <strong class="{% if item.variance >= 0 %}amount-positive{% else %}amount-negative{% endif %}">
                  {{ item.variance_formatted }}
                </strong>
              </td>
              <td class="amount-cell">
                <span class="{% if item.variance_percent >= 0 %}amount-positive{% else %}amount-negative{% endif %}">
                  {{ item.variance_percent }}%
                </span>
              </td>
            </tr>
            {% endfor %}
          </tbody>
          <tfoot>
            <tr class="table-info">
              <th colspan="6">{{ text_totals }}</th>
              <th class="amount-cell">
                <strong class="amount-neutral">{{ summary.total_cost_formatted }}</strong>
              </th>
              <th></th>
              <th class="amount-cell">
                <strong class="amount-positive">{{ summary.market_value_formatted }}</strong>
              </th>
              <th class="amount-cell">
                <strong class="{% if summary.variance >= 0 %}amount-positive{% else %}amount-negative{% endif %}">
                  {{ summary.variance_formatted }}
                </strong>
              </th>
              <th class="amount-cell">
                <span class="{% if summary.variance_percent >= 0 %}amount-positive{% else %}amount-negative{% endif %}">
                  {{ summary.variance_percent }}%
                </span>
              </th>
            </tr>
          </tfoot>
        </table>
      </div>
    </div>

    <!-- Charts Section -->
    <div class="row">
      <div class="col-md-6">
        <div class="chart-container">
          <h4>{{ text_inventory_by_category_chart }}</h4>
          <canvas id="inventoryByCategoryChart"></canvas>
        </div>
      </div>
      <div class="col-md-6">
        <div class="chart-container">
          <h4>{{ text_cost_vs_market_chart }}</h4>
          <canvas id="costVsMarketChart"></canvas>
        </div>
      </div>
    </div>

    <!-- Variance Analysis -->
    <div class="row">
      <div class="col-md-12">
        <div class="chart-container">
          <h4>{{ text_variance_analysis_chart }}</h4>
          <canvas id="varianceAnalysisChart"></canvas>
        </div>
      </div>
    </div>
    {% else %}
    <div class="alert alert-info">
      <i class="fas fa-info-circle me-2"></i>
      {{ text_no_inventory_found }}
    </div>
    {% endif %}
  </div>
</div>

<script>
// Enterprise Grade Plus JavaScript for Inventory Valuation
class InventoryValuationManager {
    constructor() {
        this.initializeTooltips();
        this.initializeDataTable();
        this.initializeKeyboardShortcuts();
        this.initializeCharts();
        this.initializeSelect2();
    }

    initializeTooltips() {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return $(tooltipTriggerEl).tooltip();
        });
    }

    initializeDataTable() {
        const table = document.getElementById('inventory-valuation-table');
        if (table && typeof $ !== 'undefined' && $.fn.DataTable) {
            $(table).DataTable({
                responsive: true,
                pageLength: 25,
                order: [[0, 'asc']], // Sort by product name asc
                columnDefs: [
                    { targets: [4, 5, 6, 7, 8, 9, 10], className: 'text-end' },
                    { targets: [3], className: 'text-center' }
                ],
                language: {
                    url: '{{ direction == "rtl" ? "/view/javascript/jquery/datatables-ar.json" : "/view/javascript/jquery/datatables-en.json" }}'
                },
                footerCallback: function (row, data, start, end, display) {
                    var api = this.api();

                    // Calculate totals for visible rows
                    var totalCost = api.column(6, { page: 'current' }).data().reduce(function (a, b) {
                        return parseFloat(a) + parseFloat($(b).text().replace(/[^\d.-]/g, ''));
                    }, 0);

                    var marketValue = api.column(8, { page: 'current' }).data().reduce(function (a, b) {
                        return parseFloat(a) + parseFloat($(b).text().replace(/[^\d.-]/g, ''));
                    }, 0);

                    var variance = marketValue - totalCost;
                }
            });
        }
    }

    initializeKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case 'g':
                        e.preventDefault();
                        this.generateValuation();
                        break;
                    case 'e':
                        e.preventDefault();
                        this.showExportMenu();
                        break;
                    case 'p':
                        e.preventDefault();
                        this.printValuation();
                        break;
                    case 'a':
                        e.preventDefault();
                        this.showAnalysis();
                        break;
                    case 'v':
                        e.preventDefault();
                        this.showVarianceAnalysis();
                        break;
                }
            }
        });
    }

    initializeCharts() {
        if (typeof Chart !== 'undefined') {
            this.createInventoryByCategoryChart();
            this.createCostVsMarketChart();
            this.createVarianceAnalysisChart();
        }
    }

    initializeSelect2() {
        if (typeof $ !== 'undefined' && $.fn.select2) {
            $('.select2').select2({
                theme: 'bootstrap',
                width: '100%'
            });
        }
    }

    generateValuation() {
        const form = document.getElementById('inventory-valuation-form');
        const formData = new FormData(form);

        this.showLoadingState(true);

        fetch('{{ url_link('accounts/inventory_valuation', 'generate') }}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            this.showLoadingState(false);
            if (data.success) {
                this.showAlert('{{ text_valuation_generated }}', 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                this.showAlert(data.error || '{{ error_generate_valuation }}', 'danger');
            }
        })
        .catch(error => {
            this.showLoadingState(false);
            this.showAlert('{{ error_generate_valuation }}: ' + error.message, 'danger');
        });
    }

    exportValuation(format) {
        const params = new URLSearchParams({
            format: format,
            date_start: document.getElementById('date_start').value,
            date_end: document.getElementById('date_end').value,
            category_id: document.getElementById('category_id').value,
            warehouse_id: document.getElementById('warehouse_id').value
        });

        this.showAlert('{{ text_exporting }}...', 'info');
        window.open('{{ export_url }}&' + params.toString(), '_blank');
    }

    printValuation() {
        window.print();
    }

    showAnalysis() {
        window.open('{{ analysis_url }}', '_blank');
    }

    showVarianceAnalysis() {
        window.open('{{ variance_url }}', '_blank');
    }

    createInventoryByCategoryChart() {
        const ctx = document.getElementById('inventoryByCategoryChart');
        if (!ctx) return;

        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: {{ category_names|json_encode|raw }},
                datasets: [{
                    data: {{ category_values|json_encode|raw }},
                    backgroundColor: ['#8e44ad', '#3498db', '#e74c3c', '#f39c12', '#27ae60'],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: '{{ text_inventory_by_category_chart }}'
                    },
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    createCostVsMarketChart() {
        const ctx = document.getElementById('costVsMarketChart');
        if (!ctx) return;

        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: {{ product_names|json_encode|raw }},
                datasets: [{
                    label: '{{ text_cost_value }}',
                    data: {{ cost_values|json_encode|raw }},
                    backgroundColor: '#17a2b8',
                    borderColor: '#138496',
                    borderWidth: 1
                }, {
                    label: '{{ text_market_value }}',
                    data: {{ market_values|json_encode|raw }},
                    backgroundColor: '#27ae60',
                    borderColor: '#1e8449',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: '{{ text_cost_vs_market_chart }}'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    createVarianceAnalysisChart() {
        const ctx = document.getElementById('varianceAnalysisChart');
        if (!ctx) return;

        new Chart(ctx, {
            type: 'line',
            data: {
                labels: {{ variance_dates|json_encode|raw }},
                datasets: [{
                    label: '{{ text_variance }}',
                    data: {{ variance_values|json_encode|raw }},
                    borderColor: '#8e44ad',
                    backgroundColor: 'rgba(142, 68, 173, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: '{{ text_variance_analysis_chart }}'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    showExportMenu() {
        const exportButton = document.querySelector('.dropdown-toggle');
        if (exportButton) {
            exportButton.click();
        }
    }

    showLoadingState(show) {
        const generateBtn = document.querySelector('button[onclick="generateValuation()"]');
        if (generateBtn) {
            if (show) {
                generateBtn.disabled = true;
                generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> {{ text_loading }}';
            } else {
                generateBtn.disabled = false;
                generateBtn.innerHTML = '<i class="fas fa-calculator me-2"></i> {{ button_generate }}';
            }
        }
    }

    showAlert(message, type) {
        const alertContainer = document.createElement('div');
        alertContainer.className = `alert alert-${type} alert-dismissible`;
        alertContainer.innerHTML = `
            <button type="button" class="close" data-dismiss="alert">&times;</button>
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'}"></i>
            ${message}
        `;

        const container = document.querySelector('#content .container-fluid');
        container.insertBefore(alertContainer, container.firstChild);

        setTimeout(() => {
            alertContainer.remove();
        }, 5000);
    }
}

// Global functions for backward compatibility
function generateValuation() {
    inventoryValuationManager.generateValuation();
}

function exportValuation(format) {
    inventoryValuationManager.exportValuation(format);
}

function printValuation() {
    inventoryValuationManager.printValuation();
}

function showAnalysis() {
    inventoryValuationManager.showAnalysis();
}

function showVarianceAnalysis() {
    inventoryValuationManager.showVarianceAnalysis();
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.inventoryValuationManager = new InventoryValuationManager();
});
</script>

{{ footer }}
