<?php
/**
 * ═══════════════════════════════════════════════════════════════════════════════
 * AYM ERP - Enterprise Grade Product Management Controller (الدستور الشامل)
 * ═══════════════════════════════════════════════════════════════════════════════
 * 
 * تطبيق كامل للدستور الشامل:
 * ✅ الخدمات المركزية الـ5 (التدقيق، الإشعارات، التواصل، المستندات، سير العمل)
 * ✅ نظام الصلاحيات المزدوج (hasPermission + hasKey)
 * ✅ التكامل المحاسبي التلقائي مع WAC
 * ✅ نظام المخزون المتقدم
 * ✅ التسعير الديناميكي
 * ✅ نظام الوحدات المتعددة
 * ✅ إدارة الباركود المتقدمة
 * ✅ حزم المنتجات والتوصيات
 * ✅ تتبع حركة المخزون
 * ✅ تحليل الطلبات والمبيعات
 * 
 * @package AYM ERP
 * @subpackage Catalog Management
 * @version 3.0.0 - Enterprise Grade Plus
 * <AUTHOR> ERP Development Team
 * @copyright 2025 AYM ERP Systems
 * @description نظام إدارة المنتجات المتقدم مع 12 تبويب شامل
 * ═══════════════════════════════════════════════════════════════════════════════
 */

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

class ControllerCatalogProduct extends Controller {
    private $error = array();
    private $central_service;
    
    /**
     * Initialize controller with required models and services - Enterprise Grade Plus
     * تطبيق الدستور الشامل للخدمات المركزية والتكامل المحاسبي
     */
    public function __construct($registry) {
        parent::__construct($registry);
        
        // ═══════════════════════════════════════════════════════════════════════════════
        // تحميل الخدمات المركزية الـ5 (الدستور الشامل)
        // ═══════════════════════════════════════════════════════════════════════════════
        $this->load->model('core/central_service_manager');
        $this->central_service = $this->model_core_central_service_manager;
        
        // تحميل خدمات التدقيق والإشعارات
        $this->load->model('logging/user_activity');
        $this->load->model('communication/unified_notification');
        $this->load->model('communication/internal_communication');
        $this->load->model('unified_document');
        $this->load->model('workflow/visual_workflow_engine');
        
        // ═══════════════════════════════════════════════════════════════════════════════
        // تحميل النماذج المطلوبة للمنتجات
        // ═══════════════════════════════════════════════════════════════════════════════
        $this->load->model('catalog/product');
        $this->load->model('catalog/category');
        $this->load->model('catalog/manufacturer');
        $this->load->model('catalog/filter');
        $this->load->model('catalog/option');
        $this->load->model('catalog/attribute');
        
        // نماذج المخزون والتسعير
        $this->load->model('inventory/warehouse');
        $this->load->model('inventory/inventory_manager');
        $this->load->model('catalog/pricing_manager');
        $this->load->model('catalog/unit_manager');
        
        // نماذج المحاسبة والتكامل
        $this->load->model('accounts/journal');
        $this->load->model('accounts/account');
        $this->load->model('setting/setting');
        
        // نماذج التجارة الإلكترونية
        $this->load->model('design/layout');
        $this->load->model('localisation/stock_status');
        $this->load->model('localisation/tax_class');
        $this->load->model('localisation/weight_class');
        $this->load->model('localisation/length_class');
        
        // ═══════════════════════════════════════════════════════════════════════════════
        // تحميل ملفات اللغة الشاملة
        // ═══════════════════════════════════════════════════════════════════════════════
        $this->load->language('catalog/product');
        $this->load->language('common/header');
        $this->load->language('inventory/inventory');
        $this->load->language('accounts/accounting');
        
        // تسجيل تهيئة الكونترولر في نظام التدقيق
        $this->central_service->logActivity(
            'controller_initialized',
            'Product controller initialized with central services',
            0,
            array(
                'user_id' => $this->user->getId(),
                'ip_address' => $this->request->server['REMOTE_ADDR'],
                'user_agent' => $this->request->server['HTTP_USER_AGENT']
            )
        );
    }
    
    /**
     * Main index method - displays product list with advanced filtering
     * تطبيق الدستور الشامل: الصلاحيات المزدوجة + التدقيق + الإشعارات
     */
    public function index() {
        // ═══════════════════════════════════════════════════════════════════════════════
        // نظام الصلاحيات المزدوج (الدستور الشامل)
        // ═══════════════════════════════════════════════════════════════════════════════
        if (!$this->user->hasPermission('access', 'catalog/product')) {
            $this->central_service->logActivity(
                'access_denied',
                'Attempted to access product list without permission',
                0,
                array('user_id' => $this->user->getId(), 'ip' => $this->request->server['REMOTE_ADDR'])
            );
            $this->session->data['error'] = $this->language->get('error_permission');
            $this->response->redirect($this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true));
        }
        
        // فحص الصلاحيات المتقدمة
        $advanced_features = array();
        if ($this->user->hasKey('product_advanced_search')) {
            $advanced_features[] = 'advanced_search';
        }
        if ($this->user->hasKey('product_bulk_operations')) {
            $advanced_features[] = 'bulk_operations';
        }
        if ($this->user->hasKey('product_export_import')) {
            $advanced_features[] = 'export_import';
        }
        
        $this->document->setTitle($this->language->get('heading_title'));
        
        // ═══════════════════════════════════════════════════════════════════════════════
        // معالجة العمليات المجمعة (Bulk Actions)
        // ═══════════════════════════════════════════════════════════════════════════════
        if (isset($this->request->post['selected']) && isset($this->request->post['action'])) {
            if (!$this->user->hasKey('product_bulk_operations')) {
                $this->session->data['error'] = $this->language->get('error_permission_advanced');
                $this->response->redirect($this->url->link('catalog/product', 'user_token=' . $this->session->data['user_token'], true));
            }
            $this->handleBulkActions();
        }
        
        // ═══════════════════════════════════════════════════════════════════════════════
        // إعداد الفلاتر المتقدمة
        // ═══════════════════════════════════════════════════════════════════════════════
        $filter_data = $this->getFilterData();
        
        // إضافة فلاتر المخزون والمحاسبة
        $filter_data['include_inventory'] = true;
        $filter_data['include_pricing'] = true;
        $filter_data['include_accounting'] = true;
        
        // ═══════════════════════════════════════════════════════════════════════════════
        // الحصول على البيانات مع التكامل الشامل
        // ═══════════════════════════════════════════════════════════════════════════════
        $products = $this->model_catalog_product->getProducts($filter_data);
        $product_total = $this->model_catalog_product->getTotalProducts($filter_data);
        
        // إحصائيات متقدمة للوحة المعلومات
        $statistics = array(
            'total_products' => $product_total,
            'active_products' => $this->model_catalog_product->getTotalProducts(array_merge($filter_data, array('filter_status' => 1))),
            'low_stock_products' => $this->model_catalog_product->getLowStockProductsCount(),
            'total_inventory_value' => $this->model_catalog_product->getTotalInventoryValue()
        );
        
        // ═══════════════════════════════════════════════════════════════════════════════
        // إعداد البيانات للعرض
        // ═══════════════════════════════════════════════════════════════════════════════
        $data = $this->prepareListData($products, $filter_data, $product_total);
        $data['statistics'] = $statistics;
        $data['advanced_features'] = $advanced_features;
        
        // إضافة URLs للعمليات المتقدمة
        $data['url_export'] = $this->url->link('catalog/product/export', 'user_token=' . $this->session->data['user_token'], true);
        $data['url_import'] = $this->url->link('catalog/product/import', 'user_token=' . $this->session->data['user_token'], true);
        $data['url_bulk_update'] = $this->url->link('catalog/product/bulk_update', 'user_token=' . $this->session->data['user_token'], true);
        
        // ═══════════════════════════════════════════════════════════════════════════════
        // تسجيل النشاط في نظام التدقيق
        // ═══════════════════════════════════════════════════════════════════════════════
        $this->central_service->logActivity(
            'product_list_viewed',
            'Viewed product list with ' . $product_total . ' products',
            0,
            array(
                'filter_data' => $filter_data,
                'total_products' => $product_total,
                'user_id' => $this->user->getId()
            )
        );
        
        // إرسال إشعار للمنتجات منخفضة المخزون
        if ($statistics['low_stock_products'] > 0) {
            $this->central_service->sendNotification(
                $this->user->getId(),
                sprintf($this->language->get('text_low_stock_alert'), $statistics['low_stock_products']),
                'warning',
                'catalog/product',
                0
            );
        }
        
        // ═══════════════════════════════════════════════════════════════════════════════
        // إعداد مسار التنقل
        // ═══════════════════════════════════════════════════════════════════════════════
        $data['breadcrumbs'] = $this->getBreadcrumbs();
        
        // ═══════════════════════════════════════════════════════════════════════════════
        // عرض الصفحة
        // ═══════════════════════════════════════════════════════════════════════════════
        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');
        
        $this->response->setOutput($this->load->view('catalog/product_list', $data));
    }
    
    /**
     * Add new product form - Enterprise Grade Plus
     * تطبيق الدستور الشامل: التكامل المحاسبي + WAC + الخدمات المركزية
     */
    public function add() {
        // ═══════════════════════════════════════════════════════════════════════════════
        // نظام الصلاحيات المزدوج (الدستور الشامل)
        // ═══════════════════════════════════════════════════════════════════════════════
        if (!$this->user->hasPermission('modify', 'catalog/product')) {
            $this->central_service->logActivity(
                'access_denied',
                'Attempted to add product without permission',
                0,
                array('user_id' => $this->user->getId(), 'ip' => $this->request->server['REMOTE_ADDR'])
            );
            $this->session->data['error'] = $this->language->get('error_permission');
            $this->response->redirect($this->url->link('catalog/product', 'user_token=' . $this->session->data['user_token'], true));
        }

        $this->document->setTitle($this->language->get('text_add'));

        // ═══════════════════════════════════════════════════════════════════════════════
        // معالجة إرسال النموذج مع التكامل الشامل
        // ═══════════════════════════════════════════════════════════════════════════════
        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm()) {
            // بدء Transaction للأمان
            $this->db->query("START TRANSACTION");

            try {
                // إضافة المنتج الأساسي
                $product_id = $this->model_catalog_product->addProduct($this->request->post);

                // ═══════════════════════════════════════════════════════════════════════════════
                // التكامل مع الخدمات المركزية الـ5
                // ═══════════════════════════════════════════════════════════════════════════════

                // 1. تسجيل النشاط في نظام التدقيق
                $this->central_service->logActivity(
                    'product_added',
                    'Added new product: ' . $this->request->post['product_description'][1]['name'],
                    $product_id,
                    array(
                        'product_data' => $this->request->post,
                        'user_id' => $this->user->getId(),
                        'timestamp' => date('Y-m-d H:i:s')
                    )
                );

                // 2. إرسال إشعار للمستخدمين المعنيين
                $this->central_service->sendNotification(
                    array($this->user->getId(), 'inventory_managers', 'accounting_team'),
                    sprintf($this->language->get('text_product_added_notification'), $this->request->post['product_description'][1]['name']),
                    'success',
                    'catalog/product/edit',
                    $product_id
                );

                // 3. إنشاء مستند في نظام المستندات
                if (isset($_FILES['product_documents'])) {
                    $this->central_service->uploadDocuments(
                        'product',
                        $product_id,
                        $_FILES['product_documents'],
                        $this->user->getId()
                    );
                }

                // 4. تشغيل سير العمل للموافقة (إذا مطلوب)
                if ($this->config->get('product_approval_workflow')) {
                    $this->central_service->startWorkflow(
                        'product_approval',
                        $product_id,
                        array(
                            'product_name' => $this->request->post['product_description'][1]['name'],
                            'created_by' => $this->user->getId(),
                            'requires_approval' => true
                        )
                    );
                }

                // ═══════════════════════════════════════════════════════════════════════════════
                // التكامل المحاسبي التلقائي (الدستور الشامل)
                // ═══════════════════════════════════════════════════════════════════════════════
                if (isset($this->request->post['initial_inventory']) && $this->request->post['initial_inventory'] > 0) {
                    $this->createInitialInventoryEntry($product_id, $this->request->post);
                }

                // إنشاء حسابات المنتج في دليل الحسابات
                $this->createProductAccounts($product_id, $this->request->post);

                // ═══════════════════════════════════════════════════════════════════════════════
                // إنشاء الباركود التلقائي
                // ═══════════════════════════════════════════════════════════════════════════════
                if ($this->config->get('auto_generate_barcode')) {
                    $this->generateProductBarcode($product_id);
                }

                // تأكيد Transaction
                $this->db->query("COMMIT");

                $this->session->data['success'] = $this->language->get('text_success_add');

                // إعادة توجيه حسب نوع الحفظ
                $redirect_url = $this->getRedirectUrl($product_id);
                $this->response->redirect($redirect_url);

            } catch (Exception $e) {
                // إلغاء Transaction في حالة الخطأ
                $this->db->query("ROLLBACK");

                $this->central_service->logActivity(
                    'product_add_failed',
                    'Failed to add product: ' . $e->getMessage(),
                    0,
                    array(
                        'error' => $e->getMessage(),
                        'user_id' => $this->user->getId(),
                        'post_data' => $this->request->post
                    )
                );

                $this->error['warning'] = $this->language->get('error_add_failed') . ': ' . $e->getMessage();
            }
        }

        $this->getForm();
    }

    /**
     * Edit existing product form - Enterprise Grade Plus
     * تطبيق الدستور الشامل: تتبع التغييرات + التكامل المحاسبي
     */
    public function edit() {
        // ═══════════════════════════════════════════════════════════════════════════════
        // نظام الصلاحيات المزدوج
        // ═══════════════════════════════════════════════════════════════════════════════
        if (!$this->user->hasPermission('modify', 'catalog/product')) {
            $this->central_service->logActivity(
                'access_denied',
                'Attempted to edit product without permission',
                isset($this->request->get['product_id']) ? $this->request->get['product_id'] : 0,
                array('user_id' => $this->user->getId(), 'ip' => $this->request->server['REMOTE_ADDR'])
            );
            $this->session->data['error'] = $this->language->get('error_permission');
            $this->response->redirect($this->url->link('catalog/product', 'user_token=' . $this->session->data['user_token'], true));
        }

        $this->document->setTitle($this->language->get('text_edit'));

        // ═══════════════════════════════════════════════════════════════════════════════
        // معالجة التعديل مع تتبع التغييرات
        // ═══════════════════════════════════════════════════════════════════════════════
        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm()) {
            $product_id = $this->request->get['product_id'];

            // الحصول على البيانات القديمة لتتبع التغييرات
            $old_product_data = $this->model_catalog_product->getProduct($product_id);

            // بدء Transaction
            $this->db->query("START TRANSACTION");

            try {
                // تحديث المنتج
                $this->model_catalog_product->editProduct($product_id, $this->request->post);

                // تتبع التغييرات المهمة
                $changes = $this->trackProductChanges($old_product_data, $this->request->post);

                // ═══════════════════════════════════════════════════════════════════════════════
                // التكامل مع الخدمات المركزية
                // ═══════════════════════════════════════════════════════════════════════════════

                // تسجيل التغييرات في نظام التدقيق
                $this->central_service->logActivity(
                    'product_edited',
                    'Edited product: ' . $this->request->post['product_description'][1]['name'],
                    $product_id,
                    array(
                        'changes' => $changes,
                        'old_data' => $old_product_data,
                        'new_data' => $this->request->post,
                        'user_id' => $this->user->getId()
                    )
                );

                // إرسال إشعارات للتغييرات المهمة
                if (!empty($changes['critical_changes'])) {
                    $this->central_service->sendNotification(
                        array('inventory_managers', 'accounting_team'),
                        sprintf($this->language->get('text_product_critical_changes'), $old_product_data['name']),
                        'warning',
                        'catalog/product/edit',
                        $product_id
                    );
                }

                // ═══════════════════════════════════════════════════════════════════════════════
                // التكامل المحاسبي للتغييرات
                // ═══════════════════════════════════════════════════════════════════════════════
                if (isset($changes['price_changes'])) {
                    $this->createPriceChangeEntries($product_id, $changes['price_changes']);
                }

                if (isset($changes['inventory_changes'])) {
                    $this->createInventoryAdjustmentEntries($product_id, $changes['inventory_changes']);
                }

                // تأكيد Transaction
                $this->db->query("COMMIT");

                $this->session->data['success'] = $this->language->get('text_success_edit');

                $redirect_url = $this->getRedirectUrl($product_id);
                $this->response->redirect($redirect_url);

            } catch (Exception $e) {
                $this->db->query("ROLLBACK");

                $this->central_service->logActivity(
                    'product_edit_failed',
                    'Failed to edit product: ' . $e->getMessage(),
                    $product_id,
                    array('error' => $e->getMessage(), 'user_id' => $this->user->getId())
                );

                $this->error['warning'] = $this->language->get('error_edit_failed') . ': ' . $e->getMessage();
            }
        }

        $this->getForm();
    }

    /**
     * Delete product(s) - Enterprise Grade Plus
     * تطبيق الدستور الشامل: التحقق من التبعيات + التكامل المحاسبي
     */
    public function delete() {
        if (!$this->user->hasPermission('modify', 'catalog/product')) {
            $this->session->data['error'] = $this->language->get('error_permission');
        } else {
            if (isset($this->request->post['selected']) && $this->validateDelete()) {
                $this->db->query("START TRANSACTION");

                try {
                    foreach ($this->request->post['selected'] as $product_id) {
                        $product_info = $this->model_catalog_product->getProduct($product_id);

                        // التحقق من التبعيات
                        $dependencies = $this->checkProductDependencies($product_id);
                        if (!empty($dependencies)) {
                            throw new Exception(sprintf($this->language->get('error_product_has_dependencies'), $product_info['name']));
                        }

                        // حذف المنتج مع التكامل المحاسبي
                        $this->deleteProductWithAccounting($product_id);

                        // تسجيل النشاط
                        $this->central_service->logActivity(
                            'product_deleted',
                            'Deleted product: ' . ($product_info ? $product_info['name'] : 'Unknown'),
                            $product_id,
                            array('user_id' => $this->user->getId(), 'product_data' => $product_info)
                        );
                    }

                    $this->db->query("COMMIT");
                    $this->session->data['success'] = $this->language->get('text_success_delete');

                } catch (Exception $e) {
                    $this->db->query("ROLLBACK");
                    $this->session->data['error'] = $e->getMessage();
                }
            }
        }

        $this->response->redirect($this->url->link('catalog/product', 'user_token=' . $this->session->data['user_token'], true));
    }

    /**
     * Copy product(s) - Enterprise Grade Plus
     */
    public function copy() {
        if (!$this->user->hasPermission('modify', 'catalog/product')) {
            $this->session->data['error'] = $this->language->get('error_permission');
        } else {
            if (isset($this->request->post['selected']) && $this->validateCopy()) {
                foreach ($this->request->post['selected'] as $product_id) {
                    $new_product_id = $this->model_catalog_product->copyProduct($product_id);

                    $this->central_service->logActivity(
                        'product_copied',
                        'Copied product ID: ' . $product_id . ' to new ID: ' . $new_product_id,
                        $new_product_id,
                        array('original_id' => $product_id, 'user_id' => $this->user->getId())
                    );
                }

                $this->session->data['success'] = $this->language->get('text_success_copy');
            }
        }

        $this->response->redirect($this->url->link('catalog/product', 'user_token=' . $this->session->data['user_token'], true));
    }

    /**
     * Auto-save functionality - Enterprise Grade Plus
     */
    public function autoSave() {
        $json = array();

        if ($this->request->server['REQUEST_METHOD'] == 'POST' && isset($this->request->post['auto_save'])) {
            try {
                // حفظ مؤقت في جدول منفصل
                $this->model_catalog_product->saveProductDraft($this->request->post, $this->user->getId());

                $json['success'] = true;
                $json['message'] = $this->language->get('text_auto_saved');

                $this->central_service->logActivity(
                    'product_auto_saved',
                    'Auto-saved product draft',
                    isset($this->request->post['product_id']) ? $this->request->post['product_id'] : 0,
                    array('user_id' => $this->user->getId())
                );

            } catch (Exception $e) {
                $json['success'] = false;
                $json['error'] = $e->getMessage();
            }
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * Prepare and display the product form (add/edit) - Enterprise Grade Plus
     */
    protected function getForm() {
        // إعداد البيانات الأساسية
        $data = array();

        // النصوص الأساسية
        $data['text_form'] = !isset($this->request->get['product_id']) ? $this->language->get('text_add') : $this->language->get('text_edit');

        // URLs
        if (!isset($this->request->get['product_id'])) {
            $data['action'] = $this->url->link('catalog/product/add', 'user_token=' . $this->session->data['user_token'], true);
        } else {
            $data['action'] = $this->url->link('catalog/product/edit', 'user_token=' . $this->session->data['user_token'] . '&product_id=' . $this->request->get['product_id'], true);
        }

        $data['cancel'] = $this->url->link('catalog/product', 'user_token=' . $this->session->data['user_token'], true);
        $data['auto_save_url'] = $this->url->link('catalog/product/autoSave', 'user_token=' . $this->session->data['user_token'], true);

        // معالجة الأخطاء
        if (isset($this->error['warning'])) {
            $data['error_warning'] = $this->error['warning'];
        } else {
            $data['error_warning'] = '';
        }

        // الحصول على بيانات المنتج إذا كان في وضع التعديل
        if (isset($this->request->get['product_id']) && ($this->request->server['REQUEST_METHOD'] != 'POST')) {
            $product_info = $this->model_catalog_product->getProduct($this->request->get['product_id']);
        }

        // إعداد جميع بيانات النموذج للـ12 تبويب
        $data = array_merge($data, $this->prepareFormData($product_info ?? array()));

        // إعداد مسار التنقل
        $data['breadcrumbs'] = $this->getBreadcrumbs($data['text_form']);

        // تحميل JavaScript و CSS المطلوبة
        $this->document->addScript('view/javascript/catalog/product_manager.js');
        $this->document->addScript('view/javascript/inventory/inventory_manager.js');
        $this->document->addScript('view/javascript/catalog/pricing_manager.js');
        $this->document->addScript('view/javascript/catalog/bundle_manager.js');
        $this->document->addScript('view/javascript/catalog/barcode_manager.js');
        $this->document->addScript('view/javascript/catalog/relations_manager.js');
        $this->document->addStyle('view/stylesheet/catalog/product_form.css');

        // عرض الصفحة
        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('catalog/product_form', $data));
    }

    // ═══════════════════════════════════════════════════════════════════════════════
    // الدوال المساعدة - Enterprise Grade Plus
    // ═══════════════════════════════════════════════════════════════════════════════

    /**
     * Validate form data - Enterprise Grade Plus
     */
    protected function validateForm() {
        if (!$this->user->hasPermission('modify', 'catalog/product')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }

        // التحقق من اسم المنتج
        foreach ($this->request->post['product_description'] as $language_id => $value) {
            if ((utf8_strlen($value['name']) < 1) || (utf8_strlen($value['name']) > 255)) {
                $this->error['name'][$language_id] = $this->language->get('error_name');
            }

            if ((utf8_strlen($value['meta_title']) < 1) || (utf8_strlen($value['meta_title']) > 255)) {
                $this->error['meta_title'][$language_id] = $this->language->get('error_meta_title');
            }
        }

        // التحقق من الموديل
        if ((utf8_strlen($this->request->post['model']) < 1) || (utf8_strlen($this->request->post['model']) > 64)) {
            $this->error['model'] = $this->language->get('error_model');
        }

        // التحقق من تفرد الموديل
        $product_info = $this->model_catalog_product->getProductByModel($this->request->post['model']);
        if ($product_info && (!isset($this->request->get['product_id']) || $product_info['product_id'] != $this->request->get['product_id'])) {
            $this->error['model'] = $this->language->get('error_model_exists');
        }

        return !$this->error;
    }

    /**
     * Validate delete operation
     */
    protected function validateDelete() {
        if (!$this->user->hasPermission('modify', 'catalog/product')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }

        return !$this->error;
    }

    /**
     * Validate copy operation
     */
    protected function validateCopy() {
        if (!$this->user->hasPermission('modify', 'catalog/product')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }

        return !$this->error;
    }

    /**
     * Check product dependencies before deletion
     */
    protected function checkProductDependencies($product_id) {
        $dependencies = array();

        // فحص الطلبات
        $orders = $this->model_catalog_product->getProductOrders($product_id);
        if (!empty($orders)) {
            $dependencies['orders'] = count($orders);
        }

        // فحص المخزون
        $inventory = $this->model_catalog_product->getProductInventory($product_id);
        foreach ($inventory as $inv) {
            if ($inv['quantity'] > 0) {
                $dependencies['inventory'] = true;
                break;
            }
        }

        // فحص الحزم
        $bundles = $this->model_catalog_product->getProductBundles($product_id);
        if (!empty($bundles)) {
            $dependencies['bundles'] = count($bundles);
        }

        return $dependencies;
    }

    /**
     * Delete product with accounting integration
     */
    protected function deleteProductWithAccounting($product_id) {
        // حذف القيود المحاسبية المرتبطة
        $this->model_accounts_journal->deleteProductEntries($product_id);

        // حذف المنتج
        $this->model_catalog_product->deleteProduct($product_id);
    }

    /**
     * Handle bulk actions
     */
    protected function handleBulkActions() {
        $action = $this->request->post['action'];
        $selected = $this->request->post['selected'];

        switch ($action) {
            case 'delete':
                $this->bulkDelete($selected);
                break;
            case 'enable':
                $this->bulkUpdateStatus($selected, 1);
                break;
            case 'disable':
                $this->bulkUpdateStatus($selected, 0);
                break;
            case 'update_category':
                $this->bulkUpdateCategory($selected);
                break;
            case 'update_price':
                $this->bulkUpdatePrice($selected);
                break;
        }
    }

    /**
     * Get filter data for product list
     */
    protected function getFilterData() {
        $filter_data = array();

        // الفلاتر الأساسية
        $filter_data['filter_name'] = isset($this->request->get['filter_name']) ? $this->request->get['filter_name'] : '';
        $filter_data['filter_model'] = isset($this->request->get['filter_model']) ? $this->request->get['filter_model'] : '';
        $filter_data['filter_status'] = isset($this->request->get['filter_status']) ? $this->request->get['filter_status'] : '';
        $filter_data['filter_category'] = isset($this->request->get['filter_category']) ? $this->request->get['filter_category'] : '';

        // فلاتر المخزون
        $filter_data['filter_quantity_min'] = isset($this->request->get['filter_quantity_min']) ? $this->request->get['filter_quantity_min'] : '';
        $filter_data['filter_quantity_max'] = isset($this->request->get['filter_quantity_max']) ? $this->request->get['filter_quantity_max'] : '';

        // فلاتر التسعير
        $filter_data['filter_price_min'] = isset($this->request->get['filter_price_min']) ? $this->request->get['filter_price_min'] : '';
        $filter_data['filter_price_max'] = isset($this->request->get['filter_price_max']) ? $this->request->get['filter_price_max'] : '';

        // الترتيب والصفحات
        $filter_data['sort'] = isset($this->request->get['sort']) ? $this->request->get['sort'] : 'p.product_id';
        $filter_data['order'] = isset($this->request->get['order']) ? $this->request->get['order'] : 'DESC';
        $filter_data['start'] = isset($this->request->get['page']) ? ($this->request->get['page'] - 1) * $this->config->get('config_limit_admin') : 0;
        $filter_data['limit'] = $this->config->get('config_limit_admin');

        return $filter_data;
    }

    /**
     * Prepare data for product list display
     */
    protected function prepareListData($products, $filter_data, $product_total) {
        $data = array();

        // إعداد بيانات المنتجات للعرض
        $data['products'] = array();

        $this->load->model('tool/image');

        foreach ($products as $result) {
            // الصورة
            if (is_file(DIR_IMAGE . $result['image'])) {
                $image = $this->model_tool_image->resize($result['image'], 40, 40);
            } else {
                $image = $this->model_tool_image->resize('no_image.png', 40, 40);
            }

            // المخزون
            $inventory = $this->model_catalog_product->getProductInventory($result['product_id']);

            $data['products'][] = array(
                'product_id' => $result['product_id'],
                'image' => $image,
                'name' => $result['name'],
                'model' => $result['model'],
                'price' => $this->currency->format($result['price'], $this->config->get('config_currency')),
                'quantity' => $this->getTotalQuantity($inventory),
                'status' => $result['status'] ? $this->language->get('text_enabled') : $this->language->get('text_disabled'),
                'edit' => $this->url->link('catalog/product/edit', 'user_token=' . $this->session->data['user_token'] . '&product_id=' . $result['product_id'], true)
            );
        }

        // URLs للعمليات
        $data['add'] = $this->url->link('catalog/product/add', 'user_token=' . $this->session->data['user_token'], true);
        $data['delete'] = $this->url->link('catalog/product/delete', 'user_token=' . $this->session->data['user_token'], true);

        // الفلاتر
        $data['filter_name'] = $filter_data['filter_name'];
        $data['filter_model'] = $filter_data['filter_model'];
        $data['filter_status'] = $filter_data['filter_status'];

        // الترقيم
        $pagination = new Pagination();
        $pagination->total = $product_total;
        $pagination->page = isset($this->request->get['page']) ? $this->request->get['page'] : 1;
        $pagination->limit = $this->config->get('config_limit_admin');
        $pagination->url = $this->url->link('catalog/product', 'user_token=' . $this->session->data['user_token'] . '&page={page}', true);

        $data['pagination'] = $pagination->render();
        $data['results'] = sprintf($this->language->get('text_pagination'), ($product_total) ? (($pagination->page - 1) * $this->config->get('config_limit_admin')) + 1 : 0, ((($pagination->page - 1) * $this->config->get('config_limit_admin')) > ($product_total - $this->config->get('config_limit_admin'))) ? $product_total : ((($pagination->page - 1) * $this->config->get('config_limit_admin')) + $this->config->get('config_limit_admin')), $product_total, ceil($product_total / $this->config->get('config_limit_admin')));

        return $data;
    }

    /**
     * Get breadcrumbs for navigation
     */
    protected function getBreadcrumbs($page_title = '') {
        $data['breadcrumbs'] = array();

        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );

        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('catalog/product', 'user_token=' . $this->session->data['user_token'], true)
        );

        if ($page_title) {
            $data['breadcrumbs'][] = array(
                'text' => $page_title,
                'href' => ''
            );
        }

        return $data['breadcrumbs'];
    }

    /**
     * Get redirect URL based on save action
     */
    protected function getRedirectUrl($product_id) {
        $save_action = isset($this->request->post['save_action']) ? $this->request->post['save_action'] : 'close';

        switch ($save_action) {
            case 'new':
                return $this->url->link('catalog/product/add', 'user_token=' . $this->session->data['user_token'], true);
            case 'copy':
                return $this->url->link('catalog/product/add', 'user_token=' . $this->session->data['user_token'] . '&copy_from=' . $product_id, true);
            case 'edit':
                return $this->url->link('catalog/product/edit', 'user_token=' . $this->session->data['user_token'] . '&product_id=' . $product_id, true);
            default:
                return $this->url->link('catalog/product', 'user_token=' . $this->session->data['user_token'], true);
        }
    }

    /**
     * Prepare form data for all 12 tabs
     */
    protected function prepareFormData($product_info) {
        $data = array();

        // بيانات أساسية
        $data['product_id'] = isset($product_info['product_id']) ? $product_info['product_id'] : 0;
        $data['model'] = isset($product_info['model']) ? $product_info['model'] : '';
        $data['sku'] = isset($product_info['sku']) ? $product_info['sku'] : '';
        $data['status'] = isset($product_info['status']) ? $product_info['status'] : 1;

        // وصف المنتج متعدد اللغات
        $this->load->model('localisation/language');
        $data['languages'] = $this->model_localisation_language->getLanguages();

        if (isset($this->request->post['product_description'])) {
            $data['product_description'] = $this->request->post['product_description'];
        } elseif (isset($product_info['product_id'])) {
            $data['product_description'] = $this->model_catalog_product->getProductDescriptions($product_info['product_id']);
        } else {
            $data['product_description'] = array();
        }

        // الفئات
        if (isset($this->request->post['product_category'])) {
            $categories = $this->request->post['product_category'];
        } elseif (isset($product_info['product_id'])) {
            $categories = $this->model_catalog_product->getProductCategories($product_info['product_id']);
        } else {
            $categories = array();
        }

        $data['product_categories'] = array();
        foreach ($categories as $category_id) {
            $category_info = $this->model_catalog_category->getCategory($category_id);
            if ($category_info) {
                $data['product_categories'][] = array(
                    'category_id' => $category_info['category_id'],
                    'name' => ($category_info['path']) ? $category_info['path'] . ' &gt; ' . $category_info['name'] : $category_info['name']
                );
            }
        }

        // الشركة المصنعة
        $data['manufacturer_id'] = isset($product_info['manufacturer_id']) ? $product_info['manufacturer_id'] : 0;
        $data['manufacturer'] = isset($product_info['manufacturer']) ? $product_info['manufacturer'] : '';

        // قوائم البيانات المساعدة
        $this->load->model('catalog/manufacturer');
        $data['manufacturers'] = $this->model_catalog_manufacturer->getManufacturers();

        $this->load->model('localisation/stock_status');
        $data['stock_statuses'] = $this->model_localisation_stock_status->getStockStatuses();

        $this->load->model('localisation/tax_class');
        $data['tax_classes'] = $this->model_localisation_tax_class->getTaxClasses();

        // بيانات المخزون والتسعير
        if (isset($product_info['product_id'])) {
            $data['inventory_data'] = $this->model_catalog_product->getProductInventory($product_info['product_id']);
            $data['pricing_data'] = $this->model_catalog_product->getProductPricing($product_info['product_id']);
            $data['units_data'] = $this->model_catalog_product->getProductUnits($product_info['product_id']);
        } else {
            $data['inventory_data'] = array();
            $data['pricing_data'] = array();
            $data['units_data'] = array();
        }

        // إعدادات المستخدم والصلاحيات
        $data['user_permissions'] = array(
            'modify' => $this->user->hasPermission('modify', 'catalog/product'),
            'delete' => $this->user->hasPermission('delete', 'catalog/product'),
            'advanced_features' => $this->user->hasKey('product_advanced_features')
        );

        // URLs للعمليات المختلفة
        $data['url_upload'] = $this->url->link('common/filemanager', 'user_token=' . $this->session->data['user_token'], true);
        $data['url_autocomplete_category'] = $this->url->link('catalog/category/autocomplete', 'user_token=' . $this->session->data['user_token'], true);
        $data['url_autocomplete_manufacturer'] = $this->url->link('catalog/manufacturer/autocomplete', 'user_token=' . $this->session->data['user_token'], true);

        return $data;
    }

    /**
     * Bulk operations methods
     */
    protected function bulkDelete($selected) {
        foreach ($selected as $product_id) {
            $this->deleteProductWithAccounting($product_id);
        }
    }

    protected function bulkUpdateStatus($selected, $status) {
        foreach ($selected as $product_id) {
            $this->model_catalog_product->updateProductStatus($product_id, $status);
        }
    }

    protected function bulkUpdateCategory($selected) {
        if (isset($this->request->post['bulk_category_id'])) {
            foreach ($selected as $product_id) {
                $this->model_catalog_product->updateProductCategory($product_id, $this->request->post['bulk_category_id']);
            }
        }
    }

    protected function bulkUpdatePrice($selected) {
        if (isset($this->request->post['bulk_price_adjustment'])) {
            foreach ($selected as $product_id) {
                $this->model_catalog_product->updateProductPrice($product_id, $this->request->post['bulk_price_adjustment']);
            }
        }
    }

    /**
     * Get total quantity across all warehouses
     */
    protected function getTotalQuantity($inventory) {
        $total = 0;
        foreach ($inventory as $inv) {
            $total += $inv['quantity'];
        }
        return $total;
    }

    /**
     * Track product changes for audit
     */
    protected function trackProductChanges($old_data, $new_data) {
        $changes = array();

        // تتبع تغييرات الأسعار
        if (isset($old_data['price']) && isset($new_data['price']) && $old_data['price'] != $new_data['price']) {
            $changes['price_changes'] = array(
                'old_price' => $old_data['price'],
                'new_price' => $new_data['price'],
                'change_amount' => $new_data['price'] - $old_data['price']
            );
            $changes['critical_changes'][] = 'price';
        }

        // تتبع تغييرات المخزون
        if (isset($old_data['quantity']) && isset($new_data['quantity']) && $old_data['quantity'] != $new_data['quantity']) {
            $changes['inventory_changes'] = array(
                'old_quantity' => $old_data['quantity'],
                'new_quantity' => $new_data['quantity'],
                'change_amount' => $new_data['quantity'] - $old_data['quantity']
            );
            $changes['critical_changes'][] = 'inventory';
        }

        // تتبع تغييرات الحالة
        if (isset($old_data['status']) && isset($new_data['status']) && $old_data['status'] != $new_data['status']) {
            $changes['status_changes'] = array(
                'old_status' => $old_data['status'],
                'new_status' => $new_data['status']
            );
            $changes['critical_changes'][] = 'status';
        }

        return $changes;
    }

    // ═══════════════════════════════════════════════════════════════════════════════
    // دوال التكامل المحاسبي - Enterprise Grade Plus
    // ═══════════════════════════════════════════════════════════════════════════════

    /**
     * Create initial inventory entry with accounting integration
     */
    protected function createInitialInventoryEntry($product_id, $product_data) {
        if (isset($product_data['initial_inventory']) && $product_data['initial_inventory'] > 0) {
            // إنشاء قيد المخزون الافتتاحي
            $journal_data = array(
                'description' => sprintf($this->language->get('text_initial_inventory'), $product_data['product_description'][1]['name']),
                'reference' => 'INIT-INV-' . $product_id,
                'entries' => array(
                    array(
                        'account_id' => $this->config->get('inventory_asset_account'),
                        'debit' => $product_data['initial_inventory'] * $product_data['cost_price'],
                        'credit' => 0
                    ),
                    array(
                        'account_id' => $this->config->get('inventory_opening_account'),
                        'debit' => 0,
                        'credit' => $product_data['initial_inventory'] * $product_data['cost_price']
                    )
                )
            );

            $this->model_accounts_journal->addJournalEntry($journal_data);
        }
    }

    /**
     * Create product accounts in chart of accounts
     */
    protected function createProductAccounts($product_id, $product_data) {
        // إنشاء حساب المنتج في المخزون
        $inventory_account = array(
            'code' => '1510' . str_pad($product_id, 6, '0', STR_PAD_LEFT),
            'name' => $product_data['product_description'][1]['name'] . ' - مخزون',
            'parent_id' => $this->config->get('inventory_parent_account'),
            'type' => 'asset'
        );

        $this->model_accounts_account->addAccount($inventory_account);

        // إنشاء حساب تكلفة البضاعة المباعة
        $cogs_account = array(
            'code' => '5110' . str_pad($product_id, 6, '0', STR_PAD_LEFT),
            'name' => $product_data['product_description'][1]['name'] . ' - تكلفة البضاعة المباعة',
            'parent_id' => $this->config->get('cogs_parent_account'),
            'type' => 'expense'
        );

        $this->model_accounts_account->addAccount($cogs_account);
    }

    /**
     * Generate product barcode
     */
    protected function generateProductBarcode($product_id) {
        // إنشاء باركود تلقائي
        $barcode = $this->config->get('barcode_prefix') . str_pad($product_id, 8, '0', STR_PAD_LEFT);

        // حفظ الباركود
        $this->model_catalog_product->updateProductBarcode($product_id, $barcode);

        return $barcode;
    }

    /**
     * Create price change accounting entries
     */
    protected function createPriceChangeEntries($product_id, $price_changes) {
        // قيود تعديل الأسعار حسب نظام WAC
        $journal_data = array(
            'description' => sprintf($this->language->get('text_price_adjustment'), $product_id),
            'reference' => 'PRICE-ADJ-' . $product_id . '-' . time(),
            'entries' => array()
        );

        // حساب الفرق في القيمة
        $quantity = $this->model_catalog_product->getTotalProductQuantity($product_id);
        $value_difference = ($price_changes['new_price'] - $price_changes['old_price']) * $quantity;

        if ($value_difference != 0) {
            $journal_data['entries'][] = array(
                'account_id' => $this->config->get('inventory_asset_account'),
                'debit' => $value_difference > 0 ? $value_difference : 0,
                'credit' => $value_difference < 0 ? abs($value_difference) : 0
            );

            $journal_data['entries'][] = array(
                'account_id' => $this->config->get('inventory_adjustment_account'),
                'debit' => $value_difference < 0 ? abs($value_difference) : 0,
                'credit' => $value_difference > 0 ? $value_difference : 0
            );

            $this->model_accounts_journal->addJournalEntry($journal_data);
        }
    }

    /**
     * Create inventory adjustment entries
     */
    protected function createInventoryAdjustmentEntries($product_id, $inventory_changes) {
        // قيود تعديل المخزون
        $cost_price = $this->model_catalog_product->getProductCostPrice($product_id);
        $value_change = $inventory_changes['change_amount'] * $cost_price;

        if ($value_change != 0) {
            $journal_data = array(
                'description' => sprintf($this->language->get('text_inventory_adjustment'), $product_id),
                'reference' => 'INV-ADJ-' . $product_id . '-' . time(),
                'entries' => array(
                    array(
                        'account_id' => $this->config->get('inventory_asset_account'),
                        'debit' => $value_change > 0 ? $value_change : 0,
                        'credit' => $value_change < 0 ? abs($value_change) : 0
                    ),
                    array(
                        'account_id' => $this->config->get('inventory_adjustment_account'),
                        'debit' => $value_change < 0 ? abs($value_change) : 0,
                        'credit' => $value_change > 0 ? $value_change : 0
                    )
                )
            );

            $this->model_accounts_journal->addJournalEntry($journal_data);
        }
    }

    // ═══════════════════════════════════════════════════════════════════════════════
    // دوال التصدير والاستيراد - Enterprise Grade Plus
    // ═══════════════════════════════════════════════════════════════════════════════

    /**
     * Export products to Excel/CSV - Enterprise Grade Plus
     */
    public function export() {
        if (!$this->user->hasKey('product_export_import')) {
            $this->session->data['error'] = $this->language->get('error_permission_advanced');
            $this->response->redirect($this->url->link('catalog/product', 'user_token=' . $this->session->data['user_token'], true));
        }

        $this->load->model('tool/export');

        // إعداد الفلاتر للتصدير
        $filter_data = $this->getFilterData();
        $filter_data['limit'] = 10000; // حد أقصى للتصدير

        // الحصول على البيانات
        $products = $this->model_catalog_product->getProducts($filter_data);

        // إعداد البيانات للتصدير
        $export_data = array();
        $export_data[] = array(
            'Product ID', 'Name', 'Model', 'SKU', 'Price', 'Quantity', 'Status',
            'Category', 'Manufacturer', 'Weight', 'Dimensions', 'Tax Class'
        );

        foreach ($products as $product) {
            $categories = $this->model_catalog_product->getProductCategories($product['product_id']);
            $category_names = array();
            foreach ($categories as $category_id) {
                $category = $this->model_catalog_category->getCategory($category_id);
                if ($category) {
                    $category_names[] = $category['name'];
                }
            }

            $export_data[] = array(
                $product['product_id'],
                $product['name'],
                $product['model'],
                $product['sku'],
                $product['price'],
                $product['quantity'],
                $product['status'] ? 'Enabled' : 'Disabled',
                implode(', ', $category_names),
                $product['manufacturer'],
                $product['weight'],
                $product['length'] . 'x' . $product['width'] . 'x' . $product['height'],
                $product['tax_class']
            );
        }

        // تسجيل النشاط
        $this->central_service->logActivity(
            'products_exported',
            'Exported ' . count($products) . ' products',
            0,
            array(
                'export_count' => count($products),
                'filter_data' => $filter_data,
                'user_id' => $this->user->getId()
            )
        );

        // تصدير الملف
        $filename = 'products_export_' . date('Y-m-d_H-i-s') . '.xlsx';
        $this->model_tool_export->exportToExcel($export_data, $filename);
    }

    /**
     * Import products from Excel/CSV - Enterprise Grade Plus
     */
    public function import() {
        if (!$this->user->hasKey('product_export_import')) {
            $this->session->data['error'] = $this->language->get('error_permission_advanced');
            $this->response->redirect($this->url->link('catalog/product', 'user_token=' . $this->session->data['user_token'], true));
        }

        $json = array();

        if ($this->request->server['REQUEST_METHOD'] == 'POST' && isset($this->request->files['import_file'])) {
            try {
                $this->load->model('tool/import');

                // التحقق من نوع الملف
                $file_extension = pathinfo($this->request->files['import_file']['name'], PATHINFO_EXTENSION);
                if (!in_array(strtolower($file_extension), array('xlsx', 'xls', 'csv'))) {
                    throw new Exception($this->language->get('error_invalid_file_type'));
                }

                // قراءة البيانات من الملف
                $import_data = $this->model_tool_import->readFile($this->request->files['import_file']['tmp_name'], $file_extension);

                if (empty($import_data)) {
                    throw new Exception($this->language->get('error_empty_file'));
                }

                // بدء Transaction
                $this->db->query("START TRANSACTION");

                $imported_count = 0;
                $errors = array();

                // تخطي الصف الأول (العناوين)
                array_shift($import_data);

                foreach ($import_data as $row_index => $row) {
                    try {
                        // التحقق من البيانات المطلوبة
                        if (empty($row[1]) || empty($row[2])) { // Name and Model required
                            $errors[] = sprintf($this->language->get('error_missing_required_data'), $row_index + 2);
                            continue;
                        }

                        // إعداد بيانات المنتج
                        $product_data = array(
                            'model' => $row[2],
                            'sku' => isset($row[3]) ? $row[3] : '',
                            'price' => isset($row[4]) ? (float)$row[4] : 0,
                            'quantity' => isset($row[5]) ? (int)$row[5] : 0,
                            'status' => isset($row[6]) ? ($row[6] == 'Enabled' ? 1 : 0) : 1,
                            'weight' => isset($row[9]) ? (float)$row[9] : 0,
                            'product_description' => array(
                                1 => array( // Default language
                                    'name' => $row[1],
                                    'description' => '',
                                    'meta_title' => $row[1],
                                    'meta_description' => '',
                                    'meta_keyword' => ''
                                )
                            )
                        );

                        // إضافة المنتج
                        $product_id = $this->model_catalog_product->addProduct($product_data);

                        // إضافة المخزون الأولي إذا وجد
                        if ($product_data['quantity'] > 0) {
                            $this->createInitialInventoryEntry($product_id, $product_data);
                        }

                        $imported_count++;

                    } catch (Exception $e) {
                        $errors[] = sprintf($this->language->get('error_import_row'), $row_index + 2, $e->getMessage());
                    }
                }

                // تأكيد Transaction
                $this->db->query("COMMIT");

                // تسجيل النشاط
                $this->central_service->logActivity(
                    'products_imported',
                    'Imported ' . $imported_count . ' products',
                    0,
                    array(
                        'imported_count' => $imported_count,
                        'errors_count' => count($errors),
                        'file_name' => $this->request->files['import_file']['name'],
                        'user_id' => $this->user->getId()
                    )
                );

                $json['success'] = true;
                $json['message'] = sprintf($this->language->get('text_import_success'), $imported_count);
                $json['imported_count'] = $imported_count;
                $json['errors'] = $errors;

            } catch (Exception $e) {
                $this->db->query("ROLLBACK");
                $json['success'] = false;
                $json['error'] = $e->getMessage();
            }
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    // ═══════════════════════════════════════════════════════════════════════════════
    // دوال AJAX للتفاعل المتقدم - Enterprise Grade Plus
    // ═══════════════════════════════════════════════════════════════════════════════

    /**
     * Get product data via AJAX
     */
    public function getProductData() {
        $json = array();

        if (isset($this->request->get['product_id'])) {
            $product_id = $this->request->get['product_id'];
            $product_info = $this->model_catalog_product->getProduct($product_id);

            if ($product_info) {
                // البيانات الأساسية
                $json['product'] = $product_info;

                // بيانات المخزون
                $json['inventory'] = $this->model_catalog_product->getProductInventory($product_id);

                // بيانات التسعير
                $json['pricing'] = $this->model_catalog_product->getProductPricing($product_id);

                // بيانات الوحدات
                $json['units'] = $this->model_catalog_product->getProductUnits($product_id);

                // الإحصائيات
                $json['statistics'] = array(
                    'total_sales' => $this->model_catalog_product->getProductTotalSales($product_id),
                    'total_orders' => $this->model_catalog_product->getProductTotalOrders($product_id),
                    'average_rating' => $this->model_catalog_product->getProductAverageRating($product_id),
                    'total_reviews' => $this->model_catalog_product->getProductTotalReviews($product_id)
                );

                $json['success'] = true;
            } else {
                $json['error'] = $this->language->get('error_product_not_found');
            }
        } else {
            $json['error'] = $this->language->get('error_product_id_required');
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * Update product field via AJAX
     */
    public function updateField() {
        $json = array();

        if (!$this->user->hasPermission('modify', 'catalog/product')) {
            $json['error'] = $this->language->get('error_permission');
        } else {
            if (isset($this->request->post['product_id']) && isset($this->request->post['field']) && isset($this->request->post['value'])) {
                $product_id = $this->request->post['product_id'];
                $field = $this->request->post['field'];
                $value = $this->request->post['value'];

                try {
                    // التحقق من صحة الحقل
                    $allowed_fields = array('status', 'price', 'quantity', 'model', 'sku');
                    if (!in_array($field, $allowed_fields)) {
                        throw new Exception($this->language->get('error_invalid_field'));
                    }

                    // تحديث الحقل
                    $this->model_catalog_product->updateProductField($product_id, $field, $value);

                    // تسجيل النشاط
                    $this->central_service->logActivity(
                        'product_field_updated',
                        'Updated ' . $field . ' for product ID: ' . $product_id,
                        $product_id,
                        array(
                            'field' => $field,
                            'new_value' => $value,
                            'user_id' => $this->user->getId()
                        )
                    );

                    $json['success'] = true;
                    $json['message'] = $this->language->get('text_field_updated');

                } catch (Exception $e) {
                    $json['error'] = $e->getMessage();
                }
            } else {
                $json['error'] = $this->language->get('error_missing_data');
            }
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    // ═══════════════════════════════════════════════════════════════════════════════
    // دوال التقارير والتحليلات - Enterprise Grade Plus
    // ═══════════════════════════════════════════════════════════════════════════════

    /**
     * Generate product performance report
     */
    public function performanceReport() {
        if (!$this->user->hasKey('product_reports')) {
            $this->session->data['error'] = $this->language->get('error_permission_advanced');
            $this->response->redirect($this->url->link('catalog/product', 'user_token=' . $this->session->data['user_token'], true));
        }

        $data = array();

        // فترة التقرير
        $date_start = isset($this->request->get['date_start']) ? $this->request->get['date_start'] : date('Y-m-01');
        $date_end = isset($this->request->get['date_end']) ? $this->request->get['date_end'] : date('Y-m-d');

        // تقرير الأداء
        $performance_data = $this->model_catalog_product->getProductPerformanceReport($date_start, $date_end);

        // إعداد البيانات للعرض
        $data['products'] = array();
        foreach ($performance_data as $product) {
            $data['products'][] = array(
                'product_id' => $product['product_id'],
                'name' => $product['name'],
                'model' => $product['model'],
                'total_sales' => $this->currency->format($product['total_sales'], $this->config->get('config_currency')),
                'total_quantity' => $product['total_quantity'],
                'total_orders' => $product['total_orders'],
                'average_order_value' => $this->currency->format($product['average_order_value'], $this->config->get('config_currency')),
                'profit_margin' => number_format($product['profit_margin'], 2) . '%',
                'inventory_turnover' => number_format($product['inventory_turnover'], 2),
                'edit' => $this->url->link('catalog/product/edit', 'user_token=' . $this->session->data['user_token'] . '&product_id=' . $product['product_id'], true)
            );
        }

        // إحصائيات عامة
        $data['statistics'] = array(
            'total_products' => count($performance_data),
            'total_sales_value' => array_sum(array_column($performance_data, 'total_sales')),
            'total_quantity_sold' => array_sum(array_column($performance_data, 'total_quantity')),
            'average_profit_margin' => array_sum(array_column($performance_data, 'profit_margin')) / count($performance_data)
        );

        // تسجيل النشاط
        $this->central_service->logActivity(
            'product_performance_report_generated',
            'Generated product performance report for period: ' . $date_start . ' to ' . $date_end,
            0,
            array(
                'date_start' => $date_start,
                'date_end' => $date_end,
                'products_count' => count($performance_data),
                'user_id' => $this->user->getId()
            )
        );

        $data['date_start'] = $date_start;
        $data['date_end'] = $date_end;

        // عرض التقرير
        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('catalog/product_performance_report', $data));
    }

    /**
     * Generate inventory valuation report
     */
    public function inventoryValuation() {
        if (!$this->user->hasKey('product_reports')) {
            $this->session->data['error'] = $this->language->get('error_permission_advanced');
            $this->response->redirect($this->url->link('catalog/product', 'user_token=' . $this->session->data['user_token'], true));
        }

        $data = array();

        // تقرير تقييم المخزون
        $valuation_data = $this->model_catalog_product->getInventoryValuationReport();

        $data['products'] = array();
        $total_valuation = 0;

        foreach ($valuation_data as $product) {
            $product_valuation = $product['quantity'] * $product['average_cost'];
            $total_valuation += $product_valuation;

            $data['products'][] = array(
                'product_id' => $product['product_id'],
                'name' => $product['name'],
                'model' => $product['model'],
                'quantity' => $product['quantity'],
                'unit' => $product['unit_name'],
                'average_cost' => $this->currency->format($product['average_cost'], $this->config->get('config_currency')),
                'total_value' => $this->currency->format($product_valuation, $this->config->get('config_currency')),
                'last_movement' => $product['last_movement_date'],
                'warehouse' => $product['warehouse_name']
            );
        }

        $data['total_valuation'] = $this->currency->format($total_valuation, $this->config->get('config_currency'));
        $data['report_date'] = date('Y-m-d H:i:s');

        // تسجيل النشاط
        $this->central_service->logActivity(
            'inventory_valuation_report_generated',
            'Generated inventory valuation report',
            0,
            array(
                'total_valuation' => $total_valuation,
                'products_count' => count($valuation_data),
                'user_id' => $this->user->getId()
            )
        );

        // عرض التقرير
        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('catalog/inventory_valuation_report', $data));
    }

    /**
     * Generate ABC analysis report
     */
    public function abcAnalysis() {
        if (!$this->user->hasKey('product_advanced_analytics')) {
            $this->session->data['error'] = $this->language->get('error_permission_advanced');
            $this->response->redirect($this->url->link('catalog/product', 'user_token=' . $this->session->data['user_token'], true));
        }

        $data = array();

        // فترة التحليل
        $date_start = isset($this->request->get['date_start']) ? $this->request->get['date_start'] : date('Y-m-01', strtotime('-12 months'));
        $date_end = isset($this->request->get['date_end']) ? $this->request->get['date_end'] : date('Y-m-d');

        // تحليل ABC
        $abc_data = $this->model_catalog_product->getABCAnalysis($date_start, $date_end);

        $data['products'] = array();
        foreach ($abc_data as $product) {
            $data['products'][] = array(
                'product_id' => $product['product_id'],
                'name' => $product['name'],
                'model' => $product['model'],
                'total_sales' => $this->currency->format($product['total_sales'], $this->config->get('config_currency')),
                'sales_percentage' => number_format($product['sales_percentage'], 2) . '%',
                'cumulative_percentage' => number_format($product['cumulative_percentage'], 2) . '%',
                'abc_category' => $product['abc_category'],
                'category_class' => strtolower($product['abc_category']),
                'recommendation' => $this->getABCRecommendation($product['abc_category'])
            );
        }

        // إحصائيات ABC
        $abc_summary = array(
            'A' => array('count' => 0, 'percentage' => 0),
            'B' => array('count' => 0, 'percentage' => 0),
            'C' => array('count' => 0, 'percentage' => 0)
        );

        foreach ($abc_data as $product) {
            $abc_summary[$product['abc_category']]['count']++;
        }

        $total_products = count($abc_data);
        foreach ($abc_summary as &$summary) {
            $summary['percentage'] = $total_products > 0 ? ($summary['count'] / $total_products * 100) : 0;
        }

        $data['abc_summary'] = $abc_summary;
        $data['date_start'] = $date_start;
        $data['date_end'] = $date_end;

        // تسجيل النشاط
        $this->central_service->logActivity(
            'abc_analysis_generated',
            'Generated ABC analysis for period: ' . $date_start . ' to ' . $date_end,
            0,
            array(
                'date_start' => $date_start,
                'date_end' => $date_end,
                'products_analyzed' => $total_products,
                'user_id' => $this->user->getId()
            )
        );

        // عرض التقرير
        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('catalog/abc_analysis_report', $data));
    }

    /**
     * Get ABC recommendation based on category
     */
    private function getABCRecommendation($category) {
        switch ($category) {
            case 'A':
                return $this->language->get('text_abc_recommendation_a');
            case 'B':
                return $this->language->get('text_abc_recommendation_b');
            case 'C':
                return $this->language->get('text_abc_recommendation_c');
            default:
                return '';
        }
    }

    // ═══════════════════════════════════════════════════════════════════════════════
    // دوال الذكاء الاصطناعي والتوصيات - Enterprise Grade Plus
    // ═══════════════════════════════════════════════════════════════════════════════

    /**
     * Generate AI-powered product recommendations
     */
    public function aiRecommendations() {
        if (!$this->user->hasKey('product_ai_features')) {
            $this->session->data['error'] = $this->language->get('error_permission_advanced');
            $this->response->redirect($this->url->link('catalog/product', 'user_token=' . $this->session->data['user_token'], true));
        }

        $json = array();

        if (isset($this->request->get['product_id'])) {
            $product_id = $this->request->get['product_id'];

            try {
                // تحميل مساعد الذكاء الاصطناعي
                $this->load->model('ai/ai_assistant');

                // الحصول على توصيات الذكاء الاصطناعي
                $recommendations = $this->model_ai_ai_assistant->getProductRecommendations($product_id);

                $json['recommendations'] = array(
                    'pricing' => $recommendations['pricing_suggestions'],
                    'inventory' => $recommendations['inventory_suggestions'],
                    'marketing' => $recommendations['marketing_suggestions'],
                    'related_products' => $recommendations['related_products'],
                    'seasonal_trends' => $recommendations['seasonal_trends']
                );

                // تسجيل النشاط
                $this->central_service->logActivity(
                    'ai_recommendations_generated',
                    'Generated AI recommendations for product ID: ' . $product_id,
                    $product_id,
                    array(
                        'recommendations_count' => count($recommendations),
                        'user_id' => $this->user->getId()
                    )
                );

                $json['success'] = true;

            } catch (Exception $e) {
                $json['error'] = $e->getMessage();
            }
        } else {
            $json['error'] = $this->language->get('error_product_id_required');
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * Predict demand using AI
     */
    public function demandPrediction() {
        if (!$this->user->hasKey('product_ai_features')) {
            $this->session->data['error'] = $this->language->get('error_permission_advanced');
            $this->response->redirect($this->url->link('catalog/product', 'user_token=' . $this->session->data['user_token'], true));
        }

        $json = array();

        if (isset($this->request->get['product_id'])) {
            $product_id = $this->request->get['product_id'];
            $forecast_period = isset($this->request->get['period']) ? $this->request->get['period'] : 30; // days

            try {
                // تحميل مساعد الذكاء الاصطناعي
                $this->load->model('ai/ai_assistant');

                // التنبؤ بالطلب
                $prediction = $this->model_ai_ai_assistant->predictDemand($product_id, $forecast_period);

                $json['prediction'] = array(
                    'forecasted_demand' => $prediction['forecasted_quantity'],
                    'confidence_level' => $prediction['confidence_percentage'],
                    'seasonal_factors' => $prediction['seasonal_adjustments'],
                    'trend_analysis' => $prediction['trend_direction'],
                    'recommended_stock_level' => $prediction['recommended_stock'],
                    'reorder_point' => $prediction['reorder_point'],
                    'forecast_chart_data' => $prediction['chart_data']
                );

                // تسجيل النشاط
                $this->central_service->logActivity(
                    'demand_prediction_generated',
                    'Generated demand prediction for product ID: ' . $product_id,
                    $product_id,
                    array(
                        'forecast_period' => $forecast_period,
                        'forecasted_demand' => $prediction['forecasted_quantity'],
                        'confidence_level' => $prediction['confidence_percentage'],
                        'user_id' => $this->user->getId()
                    )
                );

                $json['success'] = true;

            } catch (Exception $e) {
                $json['error'] = $e->getMessage();
            }
        } else {
            $json['error'] = $this->language->get('error_product_id_required');
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    // ═══════════════════════════════════════════════════════════════════════════════
    // دوال الأمان والتدقيق المتقدم - Enterprise Grade Plus
    // ═══════════════════════════════════════════════════════════════════════════════

    /**
     * Security audit for product operations
     */
    public function securityAudit() {
        if (!$this->user->hasKey('system_security_audit')) {
            $this->session->data['error'] = $this->language->get('error_permission_advanced');
            $this->response->redirect($this->url->link('catalog/product', 'user_token=' . $this->session->data['user_token'], true));
        }

        $data = array();

        // تدقيق أمني شامل
        $audit_results = array();

        // 1. فحص المنتجات بدون أسعار
        $products_without_prices = $this->model_catalog_product->getProductsWithoutPrices();
        if (!empty($products_without_prices)) {
            $audit_results[] = array(
                'type' => 'warning',
                'category' => 'pricing',
                'message' => sprintf($this->language->get('audit_products_without_prices'), count($products_without_prices)),
                'count' => count($products_without_prices),
                'action' => 'review_pricing'
            );
        }

        // 2. فحص المنتجات بمخزون سالب
        $products_negative_stock = $this->model_catalog_product->getProductsWithNegativeStock();
        if (!empty($products_negative_stock)) {
            $audit_results[] = array(
                'type' => 'error',
                'category' => 'inventory',
                'message' => sprintf($this->language->get('audit_negative_stock'), count($products_negative_stock)),
                'count' => count($products_negative_stock),
                'action' => 'fix_inventory'
            );
        }

        // 3. فحص المنتجات المكررة (نفس الموديل)
        $duplicate_models = $this->model_catalog_product->getDuplicateModels();
        if (!empty($duplicate_models)) {
            $audit_results[] = array(
                'type' => 'warning',
                'category' => 'data_integrity',
                'message' => sprintf($this->language->get('audit_duplicate_models'), count($duplicate_models)),
                'count' => count($duplicate_models),
                'action' => 'resolve_duplicates'
            );
        }

        // 4. فحص المنتجات بدون فئات
        $products_without_categories = $this->model_catalog_product->getProductsWithoutCategories();
        if (!empty($products_without_categories)) {
            $audit_results[] = array(
                'type' => 'info',
                'category' => 'categorization',
                'message' => sprintf($this->language->get('audit_products_without_categories'), count($products_without_categories)),
                'count' => count($products_without_categories),
                'action' => 'assign_categories'
            );
        }

        // 5. فحص المنتجات بدون صور
        $products_without_images = $this->model_catalog_product->getProductsWithoutImages();
        if (!empty($products_without_images)) {
            $audit_results[] = array(
                'type' => 'info',
                'category' => 'media',
                'message' => sprintf($this->language->get('audit_products_without_images'), count($products_without_images)),
                'count' => count($products_without_images),
                'action' => 'add_images'
            );
        }

        $data['audit_results'] = $audit_results;
        $data['audit_date'] = date('Y-m-d H:i:s');
        $data['total_issues'] = count($audit_results);

        // تصنيف المشاكل حسب الخطورة
        $severity_counts = array(
            'error' => 0,
            'warning' => 0,
            'info' => 0
        );

        foreach ($audit_results as $result) {
            $severity_counts[$result['type']]++;
        }

        $data['severity_counts'] = $severity_counts;

        // تسجيل النشاط
        $this->central_service->logActivity(
            'security_audit_performed',
            'Performed security audit on product catalog',
            0,
            array(
                'total_issues' => count($audit_results),
                'severity_breakdown' => $severity_counts,
                'user_id' => $this->user->getId()
            )
        );

        // عرض نتائج التدقيق
        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('catalog/security_audit_report', $data));
    }
}
