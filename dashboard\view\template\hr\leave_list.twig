{{ header }}
{{ column_left }}

<div id="content">

<!-- استبدال روابط CSS المحلية بروابط CDN -->
<link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.1.0-rc.0/css/select2.min.css" rel="stylesheet" />
<link href="https://cdnjs.cloudflare.com/ajax/libs/datatables/1.13.4/css/dataTables.bootstrap.min.css" rel="stylesheet" />
<link href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css" rel="stylesheet" />
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/4.17.47/css/bootstrap-datetimepicker.min.css" rel="stylesheet" />

    <style>
    .select2-container--default .select2-selection--single {
        height: 36px;
    }

    .loading-overlay {
        position: absolute;
        top:0;left:0;right:0;bottom:0;
        background: rgba(255,255,255,0.7);
        z-index:9999;
        display:flex;
        justify-content:center;
        align-items:center;
        font-size:24px;
        font-weight:bold;
        color:#333;
        display:none;
    }

    .modal-lg {
        width: 90% !important;
        max-width: 1200px;
    }

    .barcode-item {
        margin:5px;
    }

    .table thead th {
        vertical-align: middle !important;
    }

    .form-inline .form-group {
        margin-right: 10px;
    }

    /* Adjusting DataTables default style */
    .dataTables_wrapper .dataTables_filter {
        float: right;
        text-align: right;
    }

    .dataTables_wrapper .dataTables_length {
        float: left;
    }

    /* Filters section */
    .filter-container {
        margin-bottom:20px;
    }

    .tab-content {
        margin-top:20px;
    }

    /* Toast position */
    #toast-container {
        z-index:99999;
    }

    </style>

    
  <div class="page-header">
    <div class="container-fluid">
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>

  <div class="container-fluid">
    <!-- فلاتر متقدمة -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-filter"></i> {{ text_filter }}</h3>
      </div>
      <div class="panel-body">
        <form id="filter-form" class="form-inline">
          <div class="form-group">
            <label for="filter_user">{{ text_employee }}</label>
            <select id="filter_user" name="filter_user" class="form-control" style="width: 200px;">
              <option value="">{{ text_select_employee }}</option>
              {% for user in users %}
                <option value="{{ user.user_id }}">{{ user.firstname }} {{ user.lastname }}</option>
              {% endfor %}
            </select>
          </div>
          <div class="form-group">
            <label for="filter_leave_type">{{ text_leave_type }}</label>
            <select id="filter_leave_type" name="filter_leave_type" class="form-control" style="width: 200px;">
              <option value="">{{ text_all_leave_types }}</option>
              {% for lt in leave_types %}
                <option value="{{ lt.leave_type_id }}">{{ lt.name }}</option>
              {% endfor %}
            </select>
          </div>
          <div class="form-group">
            <label for="filter_date_start">{{ text_start_date }}</label>
            <div class='input-group date' id='filter_date_start'>
              <input type='text' class="form-control" name="filter_date_start" />
              <span class="input-group-addon">
                <span class="fa fa-calendar"></span>
              </span>
            </div>
          </div>
          <div class="form-group">
            <label for="filter_date_end">{{ text_end_date }}</label>
            <div class='input-group date' id='filter_date_end'>
              <input type='text' class="form-control" name="filter_date_end" />
              <span class="input-group-addon">
                <span class="fa fa-calendar"></span>
              </span>
            </div>
          </div>
          <div class="form-group">
            <label for="filter_status">{{ text_status }}</label>
            <select name="filter_status" id="filter_status" class="form-control select2" style="width:200px;">
              <option value="">{{ text_all_statuses }}</option>
              <option value="pending">{{ text_status_pending }}</option>
              <option value="approved">{{ text_status_approved }}</option>
              <option value="rejected">{{ text_status_rejected }}</option>
              <option value="cancelled">{{ text_status_cancelled }}</option>
            </select>
          </div>
          <button type="button" id="btn-filter" class="btn btn-primary"><i class="fa fa-search"></i> {{ button_filter }}</button>
          <button type="button" id="btn-reset" class="btn btn-default"><i class="fa fa-eraser"></i> {{ button_reset }}</button>
        </form>
      </div>
    </div>

    <!-- زر إضافة جديد -->
    <div class="text-right">
      <button type="button" class="btn btn-success" id="btn-add"><i class="fa fa-plus"></i> {{ button_add_leave_request }}</button>
    </div>

    <!-- جدول طلبات الإجازة -->
    <div class="panel panel-default" style="margin-top:10px;">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-plane"></i> {{ text_leave_list }}</h3>
      </div>
      <div class="panel-body">
        <table id="leave-table" class="table table-bordered table-striped">
          <thead>
            <tr>
              <th>{{ column_employee }}</th>
              <th>{{ column_leave_type }}</th>
              <th>{{ column_start_date }}</th>
              <th>{{ column_end_date }}</th>
              <th>{{ column_status }}</th>
              <th>{{ column_actions }}</th>
            </tr>
          </thead>
          <tbody>
            <!-- سيتم تعبئة المحتوى عبر DataTables Ajax -->
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>

<!-- مودال إضافة/تعديل طلب إجازة -->
<div class="modal fade" id="modal-leave" tabindex="-1" role="dialog" aria-labelledby="modalLeaveLabel">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <form id="leave-form">
        <div class="modal-header">
          <button type="button" class="close" data-dismiss="modal"><span>&times;</span></button>
          <h4 class="modal-title" id="modalLeaveLabel">{{ text_add_leave_request }}</h4>
        </div>
        <div class="modal-body">
          <input type="hidden" name="leave_request_id" value="" />
          <div class="form-group">
            <label>{{ text_employee }}</label>
            <select name="user_id" class="form-control select2" style="width:100%;">
              <option value="">{{ text_select_employee }}</option>
              {% for user in users %}
              <option value="{{ user.user_id }}">{{ user.firstname }} {{ user.lastname }}</option>
              {% endfor %}
            </select>
          </div>
          <div class="form-group">
            <label>{{ text_leave_type }}</label>
            <select name="leave_type_id" class="form-control select2" style="width:100%;">
              <option value="">{{ text_select_leave_type }}</option>
              {% for lt in leave_types %}
              <option value="{{ lt.leave_type_id }}">{{ lt.name }}</option>
              {% endfor %}
            </select>
          </div>
          <div class="form-group">
            <label>{{ text_start_date }}</label>
            <div class='input-group date' id='leave_start_picker'>
              <input type='text' name="start_date" class="form-control" required />
              <span class="input-group-addon"><span class="fa fa-calendar"></span></span>
            </div>
          </div>
          <div class="form-group">
            <label>{{ text_end_date }}</label>
            <div class='input-group date' id='leave_end_picker'>
              <input type='text' name="end_date" class="form-control" required />
              <span class="input-group-addon"><span class="fa fa-calendar"></span></span>
            </div>
          </div>
          <div class="form-group">
            <label>{{ text_status }}</label>
            <select name="status" class="form-control">
              <option value="pending">{{ text_status_pending }}</option>
              <option value="approved">{{ text_status_approved }}</option>
              <option value="rejected">{{ text_status_rejected }}</option>
              <option value="cancelled">{{ text_status_cancelled }}</option>
            </select>
          </div>
          <div class="form-group">
            <label>{{ text_reason }}</label>
            <textarea name="reason" class="form-control" rows="3"></textarea>
          </div>
          <div class="form-group">
            <label>{{ text_approved_by }}</label>
            <select name="approved_by" class="form-control select2" style="width:100%;">
              <option value="">{{ text_select_approver }}</option>
              {% for user in users %}
              <option value="{{ user.user_id }}">{{ user.firstname }} {{ user.lastname }}</option>
              {% endfor %}
            </select>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_close }}</button>
          <button type="submit" class="btn btn-primary">{{ button_save }}</button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- استبدال روابط JavaScript المحلية بروابط CDN -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.1.0-rc.0/js/select2.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/4.17.47/js/bootstrap-datetimepicker.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/datatables/1.13.4/js/jquery.dataTables.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/datatables/1.13.4/js/dataTables.bootstrap.min.js"></script>


<script type="text/javascript">
$(document).ready(function() {
    // تفعيل select2
    $('#filter_user').select2({ placeholder: "{{ text_select_employee }}" });
    $('#filter_leave_type').select2({ placeholder: "{{ text_all_leave_types }}" });
    $('#filter_status').select2({ placeholder: "{{ text_all_statuses }}" });

    $('select[name="user_id"]').select2({ placeholder: "{{ text_select_employee }}" });
    $('select[name="leave_type_id"]').select2({ placeholder: "{{ text_select_leave_type }}" });
    $('select[name="approved_by"]').select2({ placeholder: "{{ text_select_approver }}" });

    // تفعيل datetimepicker للتواريخ
    $('#filter_date_start').datetimepicker({ format: 'YYYY-MM-DD' });
    $('#filter_date_end').datetimepicker({ format: 'YYYY-MM-DD', useCurrent: false });
    $("#filter_date_start").on("dp.change", function (e) {
        $('#filter_date_end').data("DateTimePicker").minDate(e.date);
    });
    $("#filter_date_end").on("dp.change", function (e) {
        $('#filter_date_start').data("DateTimePicker").maxDate(e.date);
    });

    $('#leave_start_picker').datetimepicker({ format: 'YYYY-MM-DD' });
    $('#leave_end_picker').datetimepicker({ format: 'YYYY-MM-DD', useCurrent: false });
    $("#leave_start_picker").on("dp.change", function (e) {
        $('#leave_end_picker').data("DateTimePicker").minDate(e.date);
    });
    $("#leave_end_picker").on("dp.change", function (e) {
        $('#leave_start_picker').data("DateTimePicker").maxDate(e.date);
    });

    // DataTable مع Ajax
    var table = $('#leave-table').DataTable({
        "processing": true,
        "serverSide": true,
        "ajax": {
            "url": "{{ ajax_list_url }}",
            "type": "POST",
            "data": function (d) {
                d.user_token = "{{ user_token }}";
                d.filter_user = $('#filter_user').val();
                d.filter_leave_type = $('#filter_leave_type').val();
                d.filter_date_start = $('input[name="filter_date_start"]').val();
                d.filter_date_end = $('input[name="filter_date_end"]').val();
                d.filter_status = $('#filter_status').val();
            }
        },
        "columns": [
            { "data": "employee_name" },
            { "data": "leave_type" },
            { "data": "start_date" },
            { "data": "end_date" },
            { "data": "status" },
            { "data": "actions", "orderable": false, "searchable": false }
        ]
    });

    // أزرار الفلترة
    $('#btn-filter').on('click', function() {
        table.ajax.reload();
    });

    $('#btn-reset').on('click', function() {
        $('#filter_user').val('').trigger('change');
        $('#filter_leave_type').val('').trigger('change');
        $('input[name="filter_date_start"]').val('');
        $('input[name="filter_date_end"]').val('');
        $('#filter_status').val('').trigger('change');
        table.ajax.reload();
    });

    // زر إضافة
    $('#btn-add').on('click', function() {
        $('#leave-form')[0].reset();
        $('input[name="leave_request_id"]').val('');
        $('select[name="user_id"]').val('').trigger('change');
        $('select[name="leave_type_id"]').val('').trigger('change');
        $('select[name="status"]').val('pending');
        $('select[name="approved_by"]').val('').trigger('change');
        $('#modalLeaveLabel').text("{{ text_add_leave_request }}");
        $('#modal-leave').modal('show');
    });

    // إرسال نموذج الإجازة AJAX
    $('#leave-form').on('submit', function(e) {
        e.preventDefault();
        $.ajax({
            url: "{{ ajax_save_url }}",
            type: "POST",
            data: $(this).serialize() + '&user_token={{ user_token }}',
            dataType: "json",
            success: function(json) {
                if (json.error) {
                    toastr.error(json.error);
                } else if (json.success) {
                    toastr.success(json.success);
                    $('#modal-leave').modal('hide');
                    table.ajax.reload();
                }
            },
            error: function(xhr, status, error) {
                toastr.error("{{ text_ajax_error }}: " + error);
            }
        });
    });

    // تحرير سجل
    $(document).on('click', '.btn-edit', function() {
        var id = $(this).data('id');
        $.ajax({
            url: "{{ ajax_get_url }}",
            type: "POST",
            data: {leave_request_id: id, user_token: "{{ user_token }}"},
            dataType: "json",
            success: function(json) {
                if (json.error) {
                    toastr.error(json.error);
                } else {
                    var data = json.data;
                    $('input[name="leave_request_id"]').val(data.leave_request_id);
                    $('select[name="user_id"]').val(data.user_id).trigger('change');
                    $('select[name="leave_type_id"]').val(data.leave_type_id).trigger('change');
                    $('input[name="start_date"]').val(data.start_date);
                    $('input[name="end_date"]').val(data.end_date);
                    $('select[name="status"]').val(data.status);
                    $('textarea[name="reason"]').val(data.reason);
                    $('select[name="approved_by"]').val(data.approved_by).trigger('change');
                    $('#modalLeaveLabel').text("{{ text_edit_leave_request }}");
                    $('#modal-leave').modal('show');
                }
            },
            error: function(xhr, status, error) {
                toastr.error("{{ text_ajax_error }}: " + error);
            }
        });
    });

    // حذف سجل
    $(document).on('click', '.btn-delete', function() {
        var id = $(this).data('id');
        if (confirm("{{ text_confirm_delete }}")) {
            $.ajax({
                url: "{{ ajax_delete_url }}",
                type: "POST",
                data: {leave_request_id: id, user_token: "{{ user_token }}"},
                dataType: "json",
                success: function(json) {
                    if (json.error) {
                        toastr.error(json.error);
                    } else {
                        toastr.success(json.success);
                        table.ajax.reload();
                    }
                },
                error: function(xhr, status, error) {
                    toastr.error("{{ text_ajax_error }}: " + error);
                }
            });
        }
    });
});
</script>

{{ footer }}