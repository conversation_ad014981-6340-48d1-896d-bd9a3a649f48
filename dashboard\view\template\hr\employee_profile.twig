{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="submit" form="hr\employee-form" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fas fa-save"></i></button>
        <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-light"><i class="fas fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    
    <div class="card">
      <div class="card-header"><i class="fas fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}chart-bar{% elif screen_type == 'settings' %}cog{% else %}pencil-alt{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="card-body">
        <form id="hr\employee-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-ajax_delete_url">{{ text_ajax_delete_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="ajax_delete_url" value="{{ ajax_delete_url }}" placeholder="{{ text_ajax_delete_url }}" id="input-ajax_delete_url" class="form-control" />
              {% if error_ajax_delete_url %}
                <div class="invalid-feedback">{{ error_ajax_delete_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-ajax_document_delete_url">{{ text_ajax_document_delete_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="ajax_document_delete_url" value="{{ ajax_document_delete_url }}" placeholder="{{ text_ajax_document_delete_url }}" id="input-ajax_document_delete_url" class="form-control" />
              {% if error_ajax_document_delete_url %}
                <div class="invalid-feedback">{{ error_ajax_document_delete_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-ajax_document_upload_url">{{ text_ajax_document_upload_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="ajax_document_upload_url" value="{{ ajax_document_upload_url }}" placeholder="{{ text_ajax_document_upload_url }}" id="input-ajax_document_upload_url" class="form-control" />
              {% if error_ajax_document_upload_url %}
                <div class="invalid-feedback">{{ error_ajax_document_upload_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-ajax_documents_list_url">{{ text_ajax_documents_list_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="ajax_documents_list_url" value="{{ ajax_documents_list_url }}" placeholder="{{ text_ajax_documents_list_url }}" id="input-ajax_documents_list_url" class="form-control" />
              {% if error_ajax_documents_list_url %}
                <div class="invalid-feedback">{{ error_ajax_documents_list_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-ajax_get_url">{{ text_ajax_get_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="ajax_get_url" value="{{ ajax_get_url }}" placeholder="{{ text_ajax_get_url }}" id="input-ajax_get_url" class="form-control" />
              {% if error_ajax_get_url %}
                <div class="invalid-feedback">{{ error_ajax_get_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-ajax_list_url">{{ text_ajax_list_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="ajax_list_url" value="{{ ajax_list_url }}" placeholder="{{ text_ajax_list_url }}" id="input-ajax_list_url" class="form-control" />
              {% if error_ajax_list_url %}
                <div class="invalid-feedback">{{ error_ajax_list_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-ajax_save_url">{{ text_ajax_save_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="ajax_save_url" value="{{ ajax_save_url }}" placeholder="{{ text_ajax_save_url }}" id="input-ajax_save_url" class="form-control" />
              {% if error_ajax_save_url %}
                <div class="invalid-feedback">{{ error_ajax_save_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-attendance_records">{{ text_attendance_records }}</label>
            <div class="col-sm-10">
              <input type="text" name="attendance_records" value="{{ attendance_records }}" placeholder="{{ text_attendance_records }}" id="input-attendance_records" class="form-control" />
              {% if error_attendance_records %}
                <div class="invalid-feedback">{{ error_attendance_records }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-attendance_summary">{{ text_attendance_summary }}</label>
            <div class="col-sm-10">
              <input type="text" name="attendance_summary" value="{{ attendance_summary }}" placeholder="{{ text_attendance_summary }}" id="input-attendance_summary" class="form-control" />
              {% if error_attendance_summary %}
                <div class="invalid-feedback">{{ error_attendance_summary }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-button_add_document">{{ text_button_add_document }}</label>
            <div class="col-sm-10">
              <input type="text" name="button_add_document" value="{{ button_add_document }}" placeholder="{{ text_button_add_document }}" id="input-button_add_document" class="form-control" />
              {% if error_button_add_document %}
                <div class="invalid-feedback">{{ error_button_add_document }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-button_add_employee">{{ text_button_add_employee }}</label>
            <div class="col-sm-10">
              <input type="text" name="button_add_employee" value="{{ button_add_employee }}" placeholder="{{ text_button_add_employee }}" id="input-button_add_employee" class="form-control" />
              {% if error_button_add_employee %}
                <div class="invalid-feedback">{{ error_button_add_employee }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-button_close">{{ text_button_close }}</label>
            <div class="col-sm-10">
              <input type="text" name="button_close" value="{{ button_close }}" placeholder="{{ text_button_close }}" id="input-button_close" class="form-control" />
              {% if error_button_close %}
                <div class="invalid-feedback">{{ error_button_close }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-button_filter">{{ text_button_filter }}</label>
            <div class="col-sm-10">
              <input type="text" name="button_filter" value="{{ button_filter }}" placeholder="{{ text_button_filter }}" id="input-button_filter" class="form-control" />
              {% if error_button_filter %}
                <div class="invalid-feedback">{{ error_button_filter }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-button_reset">{{ text_button_reset }}</label>
            <div class="col-sm-10">
              <input type="text" name="button_reset" value="{{ button_reset }}" placeholder="{{ text_button_reset }}" id="input-button_reset" class="form-control" />
              {% if error_button_reset %}
                <div class="invalid-feedback">{{ error_button_reset }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-button_save">{{ text_button_save }}</label>
            <div class="col-sm-10">
              <input type="text" name="button_save" value="{{ button_save }}" placeholder="{{ text_button_save }}" id="input-button_save" class="form-control" />
              {% if error_button_save %}
                <div class="invalid-feedback">{{ error_button_save }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_actions">{{ text_column_actions }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_actions" value="{{ column_actions }}" placeholder="{{ text_column_actions }}" id="input-column_actions" class="form-control" />
              {% if error_column_actions %}
                <div class="invalid-feedback">{{ error_column_actions }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_document_actions">{{ text_column_document_actions }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_document_actions" value="{{ column_document_actions }}" placeholder="{{ text_column_document_actions }}" id="input-column_document_actions" class="form-control" />
              {% if error_column_document_actions %}
                <div class="invalid-feedback">{{ error_column_document_actions }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_document_description">{{ text_column_document_description }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_document_description" value="{{ column_document_description }}" placeholder="{{ text_column_document_description }}" id="input-column_document_description" class="form-control" />
              {% if error_column_document_description %}
                <div class="invalid-feedback">{{ error_column_document_description }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_document_name">{{ text_column_document_name }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_document_name" value="{{ column_document_name }}" placeholder="{{ text_column_document_name }}" id="input-column_document_name" class="form-control" />
              {% if error_column_document_name %}
                <div class="invalid-feedback">{{ error_column_document_name }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_employee_name">{{ text_column_employee_name }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_employee_name" value="{{ column_employee_name }}" placeholder="{{ text_column_employee_name }}" id="input-column_employee_name" class="form-control" />
              {% if error_column_employee_name %}
                <div class="invalid-feedback">{{ error_column_employee_name }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_hiring_date">{{ text_column_hiring_date }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_hiring_date" value="{{ column_hiring_date }}" placeholder="{{ text_column_hiring_date }}" id="input-column_hiring_date" class="form-control" />
              {% if error_column_hiring_date %}
                <div class="invalid-feedback">{{ error_column_hiring_date }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_job_title">{{ text_column_job_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_job_title" value="{{ column_job_title }}" placeholder="{{ text_column_job_title }}" id="input-column_job_title" class="form-control" />
              {% if error_column_job_title %}
                <div class="invalid-feedback">{{ error_column_job_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_salary">{{ text_column_salary }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_salary" value="{{ column_salary }}" placeholder="{{ text_column_salary }}" id="input-column_salary" class="form-control" />
              {% if error_column_salary %}
                <div class="invalid-feedback">{{ error_column_salary }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_status">{{ text_column_status }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_status" value="{{ column_status }}" placeholder="{{ text_column_status }}" id="input-column_status" class="form-control" />
              {% if error_column_status %}
                <div class="invalid-feedback">{{ error_column_status }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-department_stats">{{ text_department_stats }}</label>
            <div class="col-sm-10">
              <input type="text" name="department_stats" value="{{ department_stats }}" placeholder="{{ text_department_stats }}" id="input-department_stats" class="form-control" />
              {% if error_department_stats %}
                <div class="invalid-feedback">{{ error_department_stats }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-employee">{{ text_employee }}</label>
            <div class="col-sm-10">
              <input type="text" name="employee" value="{{ employee }}" placeholder="{{ text_employee }}" id="input-employee" class="form-control" />
              {% if error_employee %}
                <div class="invalid-feedback">{{ error_employee }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="invalid-feedback">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-hr_stats">{{ text_hr_stats }}</label>
            <div class="col-sm-10">
              <input type="text" name="hr_stats" value="{{ hr_stats }}" placeholder="{{ text_hr_stats }}" id="input-hr_stats" class="form-control" />
              {% if error_hr_stats %}
                <div class="invalid-feedback">{{ error_hr_stats }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-leave_history">{{ text_leave_history }}</label>
            <div class="col-sm-10">
              <input type="text" name="leave_history" value="{{ leave_history }}" placeholder="{{ text_leave_history }}" id="input-leave_history" class="form-control" />
              {% if error_leave_history %}
                <div class="invalid-feedback">{{ error_leave_history }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-new_hires">{{ text_new_hires }}</label>
            <div class="col-sm-10">
              <input type="text" name="new_hires" value="{{ new_hires }}" placeholder="{{ text_new_hires }}" id="input-new_hires" class="form-control" />
              {% if error_new_hires %}
                <div class="invalid-feedback">{{ error_new_hires }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-pending_leave_requests">{{ text_pending_leave_requests }}</label>
            <div class="col-sm-10">
              <input type="text" name="pending_leave_requests" value="{{ pending_leave_requests }}" placeholder="{{ text_pending_leave_requests }}" id="input-pending_leave_requests" class="form-control" />
              {% if error_pending_leave_requests %}
                <div class="invalid-feedback">{{ error_pending_leave_requests }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-performance_metrics">{{ text_performance_metrics }}</label>
            <div class="col-sm-10">
              <input type="text" name="performance_metrics" value="{{ performance_metrics }}" placeholder="{{ text_performance_metrics }}" id="input-performance_metrics" class="form-control" />
              {% if error_performance_metrics %}
                <div class="invalid-feedback">{{ error_performance_metrics }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-performance_reviews">{{ text_performance_reviews }}</label>
            <div class="col-sm-10">
              <input type="text" name="performance_reviews" value="{{ performance_reviews }}" placeholder="{{ text_performance_reviews }}" id="input-performance_reviews" class="form-control" />
              {% if error_performance_reviews %}
                <div class="invalid-feedback">{{ error_performance_reviews }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-salary_history">{{ text_salary_history }}</label>
            <div class="col-sm-10">
              <input type="text" name="salary_history" value="{{ salary_history }}" placeholder="{{ text_salary_history }}" id="input-salary_history" class="form-control" />
              {% if error_salary_history %}
                <div class="invalid-feedback">{{ error_salary_history }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_active">{{ text_text_active }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_active" value="{{ text_active }}" placeholder="{{ text_text_active }}" id="input-text_active" class="form-control" />
              {% if error_text_active %}
                <div class="invalid-feedback">{{ error_text_active }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_add_document">{{ text_text_add_document }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_add_document" value="{{ text_add_document }}" placeholder="{{ text_text_add_document }}" id="input-text_add_document" class="form-control" />
              {% if error_text_add_document %}
                <div class="invalid-feedback">{{ error_text_add_document }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_add_employee">{{ text_text_add_employee }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_add_employee" value="{{ text_add_employee }}" placeholder="{{ text_text_add_employee }}" id="input-text_add_employee" class="form-control" />
              {% if error_text_add_employee %}
                <div class="invalid-feedback">{{ error_text_add_employee }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_ajax_error">{{ text_text_ajax_error }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_ajax_error" value="{{ text_ajax_error }}" placeholder="{{ text_text_ajax_error }}" id="input-text_ajax_error" class="form-control" />
              {% if error_text_ajax_error %}
                <div class="invalid-feedback">{{ error_text_ajax_error }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_all_statuses">{{ text_text_all_statuses }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_all_statuses" value="{{ text_all_statuses }}" placeholder="{{ text_text_all_statuses }}" id="input-text_all_statuses" class="form-control" />
              {% if error_text_all_statuses %}
                <div class="invalid-feedback">{{ error_text_all_statuses }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_confirm_delete">{{ text_text_confirm_delete }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_confirm_delete" value="{{ text_confirm_delete }}" placeholder="{{ text_text_confirm_delete }}" id="input-text_confirm_delete" class="form-control" />
              {% if error_text_confirm_delete %}
                <div class="invalid-feedback">{{ error_text_confirm_delete }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_document_description">{{ text_text_document_description }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_document_description" value="{{ text_document_description }}" placeholder="{{ text_text_document_description }}" id="input-text_document_description" class="form-control" />
              {% if error_text_document_description %}
                <div class="invalid-feedback">{{ error_text_document_description }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_document_name">{{ text_text_document_name }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_document_name" value="{{ text_document_name }}" placeholder="{{ text_text_document_name }}" id="input-text_document_name" class="form-control" />
              {% if error_text_document_name %}
                <div class="invalid-feedback">{{ error_text_document_name }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_documents">{{ text_text_documents }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_documents" value="{{ text_documents }}" placeholder="{{ text_text_documents }}" id="input-text_documents" class="form-control" />
              {% if error_text_documents %}
                <div class="invalid-feedback">{{ error_text_documents }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_edit_employee">{{ text_text_edit_employee }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_edit_employee" value="{{ text_edit_employee }}" placeholder="{{ text_text_edit_employee }}" id="input-text_edit_employee" class="form-control" />
              {% if error_text_edit_employee %}
                <div class="invalid-feedback">{{ error_text_edit_employee }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_employee_list">{{ text_text_employee_list }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_employee_list" value="{{ text_employee_list }}" placeholder="{{ text_text_employee_list }}" id="input-text_employee_list" class="form-control" />
              {% if error_text_employee_list %}
                <div class="invalid-feedback">{{ error_text_employee_list }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_employee_name">{{ text_text_employee_name }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_employee_name" value="{{ text_employee_name }}" placeholder="{{ text_text_employee_name }}" id="input-text_employee_name" class="form-control" />
              {% if error_text_employee_name %}
                <div class="invalid-feedback">{{ error_text_employee_name }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_enter_employee_name">{{ text_text_enter_employee_name }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_enter_employee_name" value="{{ text_enter_employee_name }}" placeholder="{{ text_text_enter_employee_name }}" id="input-text_enter_employee_name" class="form-control" />
              {% if error_text_enter_employee_name %}
                <div class="invalid-feedback">{{ error_text_enter_employee_name }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_file">{{ text_text_file }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_file" value="{{ text_file }}" placeholder="{{ text_text_file }}" id="input-text_file" class="form-control" />
              {% if error_text_file %}
                <div class="invalid-feedback">{{ error_text_file }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_filter">{{ text_text_filter }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_filter" value="{{ text_filter }}" placeholder="{{ text_text_filter }}" id="input-text_filter" class="form-control" />
              {% if error_text_filter %}
                <div class="invalid-feedback">{{ error_text_filter }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_hiring_date">{{ text_text_hiring_date }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_hiring_date" value="{{ text_hiring_date }}" placeholder="{{ text_text_hiring_date }}" id="input-text_hiring_date" class="form-control" />
              {% if error_text_hiring_date %}
                <div class="invalid-feedback">{{ error_text_hiring_date }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_inactive">{{ text_text_inactive }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_inactive" value="{{ text_inactive }}" placeholder="{{ text_text_inactive }}" id="input-text_inactive" class="form-control" />
              {% if error_text_inactive %}
                <div class="invalid-feedback">{{ error_text_inactive }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_job_title">{{ text_text_job_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_job_title" value="{{ text_job_title }}" placeholder="{{ text_text_job_title }}" id="input-text_job_title" class="form-control" />
              {% if error_text_job_title %}
                <div class="invalid-feedback">{{ error_text_job_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_salary">{{ text_text_salary }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_salary" value="{{ text_salary }}" placeholder="{{ text_text_salary }}" id="input-text_salary" class="form-control" />
              {% if error_text_salary %}
                <div class="invalid-feedback">{{ error_text_salary }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_save_employee_first">{{ text_text_save_employee_first }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_save_employee_first" value="{{ text_save_employee_first }}" placeholder="{{ text_text_save_employee_first }}" id="input-text_save_employee_first" class="form-control" />
              {% if error_text_save_employee_first %}
                <div class="invalid-feedback">{{ error_text_save_employee_first }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_select_user">{{ text_text_select_user }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_select_user" value="{{ text_select_user }}" placeholder="{{ text_text_select_user }}" id="input-text_select_user" class="form-control" />
              {% if error_text_select_user %}
                <div class="invalid-feedback">{{ error_text_select_user }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_status">{{ text_text_status }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_status" value="{{ text_status }}" placeholder="{{ text_text_status }}" id="input-text_status" class="form-control" />
              {% if error_text_status %}
                <div class="invalid-feedback">{{ error_text_status }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_terminated">{{ text_text_terminated }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_terminated" value="{{ text_terminated }}" placeholder="{{ text_text_terminated }}" id="input-text_terminated" class="form-control" />
              {% if error_text_terminated %}
                <div class="invalid-feedback">{{ error_text_terminated }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_user_id">{{ text_text_user_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_user_id" value="{{ text_user_id }}" placeholder="{{ text_text_user_id }}" id="input-text_user_id" class="form-control" />
              {% if error_text_user_id %}
                <div class="invalid-feedback">{{ error_text_user_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-training_records">{{ text_training_records }}</label>
            <div class="col-sm-10">
              <input type="text" name="training_records" value="{{ training_records }}" placeholder="{{ text_training_records }}" id="input-training_records" class="form-control" />
              {% if error_training_records %}
                <div class="invalid-feedback">{{ error_training_records }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-upcoming_birthdays">{{ text_upcoming_birthdays }}</label>
            <div class="col-sm-10">
              <input type="text" name="upcoming_birthdays" value="{{ upcoming_birthdays }}" placeholder="{{ text_upcoming_birthdays }}" id="input-upcoming_birthdays" class="form-control" />
              {% if error_upcoming_birthdays %}
                <div class="invalid-feedback">{{ error_upcoming_birthdays }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="invalid-feedback">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-users">{{ text_users }}</label>
            <div class="col-sm-10">
              <input type="text" name="users" value="{{ users }}" placeholder="{{ text_users }}" id="input-users" class="form-control" />
              {% if error_users %}
                <div class="invalid-feedback">{{ error_users }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}