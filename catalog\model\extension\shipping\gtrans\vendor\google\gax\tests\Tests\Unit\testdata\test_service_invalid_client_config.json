{"interfaces": {"test.interface.v1.api": {"retry_codes": {"idempotent": ["DEADLINE_EXCEEDED", "UNAVAILABLE"], "non_idempotent": ["INTERNAL"]}, "retry_params": {"default": {"initial_retry_delay_millis": 100, "retry_delay_multiplier": 1.2, "initial_rpc_timeout_millis": 300, "rpc_timeout_multiplier": 1.3, "max_rpc_timeout_millis": 3000, "total_timeout_millis": 30000}}, "methods": {"SimpleMethod": {"timeout_millis": 40000, "retry_codes_name": "idempotent", "retry_params_name": "default"}, "PageStreamingMethod": {"timeout_millis": 40000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}}}}}