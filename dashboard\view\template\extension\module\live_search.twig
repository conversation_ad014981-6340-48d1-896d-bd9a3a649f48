{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="submit" form="form-module" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fa fa-save"></i></button>
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
          <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pencil"></i> {{ text_edit }} </h3>
      </div>
      <div class="panel-body">
        <form action="{{ action }}" method="post" enctype="multipart/form-data" id="form-module" class="form-horizontal">
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-show-image">{{ entry_show_image }}</label>
            <div class="col-sm-10">
              <select name="module_live_search_show_image" id="input-show-image" class="form-control">
                {% if module_live_search_show_image %}
                <option value="1" selected="selected">{{ text_enabled }}</option>
                <option value="0">{{ text_disabled }}</option>
                {% else %}
                <option value="1">{{ text_enabled }}</option>
                <option value="0" selected="selected">{{ text_disabled }}</option>
                {% endif %}
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-show-price">{{ entry_show_price }}</label>
            <div class="col-sm-10">
              <select name="module_live_search_show_price" id="input-show-price" class="form-control">
                {% if module_live_search_show_price %}
                <option value="1" selected="selected">{{ text_enabled }}</option>
                <option value="0">{{ text_disabled }}</option>
                {% else %}
                <option value="1">{{ text_enabled }}</option>
                <option value="0" selected="selected">{{ text_disabled }}</option>
                {% endif %}
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-show-description">{{ entry_show_description }}</label>
            <div class="col-sm-10">
              <select name="module_live_search_show_description" id="input-show-description" class="form-control">
                {% if module_live_search_show_description %}
                <option value="1" selected="selected">{{ text_enabled }}</option>
                <option value="0">{{ text_disabled }}</option>
                {% else %}
                <option value="1">{{ text_enabled }}</option>
                <option value="0" selected="selected">{{ text_disabled }}</option>
                {% endif %}
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-show-add-button">{{ entry_show_add_button }}</label>
            <div class="col-sm-10">
              <select name="module_live_search_show_add_button" id="input-show-add-button" class="form-control">
                {% if module_live_search_show_add_button %}
                <option value="1" selected="selected">{{ text_enabled }}</option>
                <option value="0">{{ text_disabled }}</option>
                {% else %}
                <option value="1">{{ text_enabled }}</option>
                <option value="0" selected="selected">{{ text_disabled }}</option>
                {% endif %}
              </select>
            </div>
          </div>
          
          <div class="form-group required">
            <label class="col-sm-2 control-label"><span data-toggle="tooltip" title="{{ help_view_all_results }}">{{ entry_view_all_results }}</span></label>
            <div class="col-sm-10">
              {% for language in languages %}
                <div class="input-group"> 
                  <span class="input-group-addon">
                    <img src="{{ language.flag_img }}" title="{{ language.name }}" />
                  </span>
                  <input 
                    type="text" 
                    name="module_live_search_view_all_results[{{ language.language_id }}][name]" 
                    value="{% if module_live_search_view_all_results[language.language_id] is not null %}{{ module_live_search_view_all_results[language.language_id].name }}{% endif %}" placeholder="{{ text_view_all_results }}" class="form-control" 
                  />
                </div>
                {% if error_view_all_results[language.language_id] is not null %}
                  <div class="text-danger">{{ error_view_all_results.language.language_id }}</div>
                {% endif %}
              {% endfor %}
            </div>
          </div>
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-limit">{{ entry_limit }}</label>
            <div class="col-sm-10">
              <input type="number" name="module_live_search_limit" value="{{ module_live_search_limit }}" placeholder="5" id="input-limit" class="form-control" />
              {% if error_limit %}
              <div class="text-danger">{{ error_limit }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-width">{{ entry_width }}</label>
            <div class="col-sm-10">
              <input type="number" name="module_live_search_image_width" value="{{ module_live_search_image_width }}" placeholder="50" id="input-width" class="form-control" />
              {% if error_width %}
              <div class="text-danger">{{ error_width }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-height">{{ entry_height }}</label>
            <div class="col-sm-10">
              <input type="number" name="module_live_search_image_height" value="{{ module_live_search_image_height }}" placeholder="50" id="input-height" class="form-control" />
              {% if error_height %}
              <div class="text-danger">{{ error_height }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-title-length"><span data-toggle="tooltip" title="{{ help_length }}">{{ entry_title_length }}</span></label>
            <div class="col-sm-10">
              <input type="number" name="module_live_search_title_length" value="{{ module_live_search_title_length }}" placeholder="100" id="input-title-length" class="form-control" />
              {% if error_title_length %}
              <div class="text-danger">{{ error_title_length }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-description-length"><span data-toggle="tooltip" title="{{ help_length }}">{{ entry_description_length }}</span></label>
            <div class="col-sm-10">
              <input type="number" name="module_live_search_description_length" value="{{ module_live_search_description_length }}" placeholder="100" id="input-description-length" class="form-control" />
              {% if error_description_length %}
              <div class="text-danger">{{ error_description_length }}</div>
              {% endif %}
            </div>
          </div>

          <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-min-length">{{ entry_min_length }}</label>
            <div class="col-sm-10">
              <input type="number" name="module_live_search_min_length" value="{{ module_live_search_min_length }}" placeholder="1" id="input-min-length" class="form-control" />
              {% if error_min_length %}
              <div class="text-danger">{{ error_min_length }}</div>
              {% endif %}
            </div>
          </div>

          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-status">{{ entry_status }}</label>
            <div class="col-sm-10">
              <select name="module_live_search_status" id="input-status" class="form-control">
                {% if module_live_search_status %}
                <option value="1" selected="selected">{{ text_enabled }}</option>
                <option value="0">{{ text_disabled }}</option>
                {% else %}
                <option value="1">{{ text_enabled }}</option>
                <option value="0" selected="selected">{{ text_disabled }}</option>
                {% endif %}
              </select>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
<script type="text/javascript"><!--

//--></script></div>
{{ footer }}