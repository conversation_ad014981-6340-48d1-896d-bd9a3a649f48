{{ header }}
<div id="account-reward" class="container">
  <div class="row">{{ column_left }}
    <div id="content" class="col" style="padding-left:10px;padding-right:10px;padding-top:10px">{{ content_top }}
      <p>{{ text_total }} <b>{{ total }}</b>.</p>
      <div class="table-responsive">
        <table class="table table-bordered table-hover">
          <thead>
            <tr>
              <th class="text-start">{{ column_date_added }}</th>
              <th class="text-start">{{ column_description }}</th>
              <th class="text-end">{{ column_points }}</th>
            </tr>
          </thead>
          <tbody>
            {% if rewards %}
              {% for reward in rewards %}
                <tr>
                  <td class="text-start">{{ reward.date_added }}</td>
                  <td class="text-start">{% if reward.order_id %} <a href="{{ reward.href }}">{{ reward.description }}</a> {% else %}
                      {{ reward.description }}
                    {% endif %}</td>
                  <td class="text-end">{{ reward.points }}</td>
                </tr>
              {% endfor %}
            {% else %}
              <tr>
                <td class="text-center" colspan="3">{{ text_no_results }}</td>
              </tr>
            {% endif %}
          </tbody>
        </table>
      </div>
      <div class="row mb-3">
        <div class="col-sm-6 text-start">{{ pagination }}</div>
        <div class="col-sm-6 text-end">{{ results }}</div>
      </div>
      <div class="text-end"><a href="{{ continue }}" class="btn btn-primary">{{ button_continue }}</a></div>
      {{ content_bottom }}</div>
    {{ column_right }}</div>
</div>
{{ footer }}
