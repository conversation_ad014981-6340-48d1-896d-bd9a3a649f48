<?php
/**
 * Header Model
 * نموذج الهيدر - يوفر البيانات والوظائف المطلوبة للهيدر
 */
class ModelCommonHeader extends Model {
    
    /**
     * الحصول على بيانات المستخدم الحالي
     */
    public function getCurrentUserData() {
        if (!$this->user->isLogged()) {
            return false;
        }
        
        $user_id = $this->user->getId();
        
        $sql = "SELECT u.*, ug.name as user_group_name 
                FROM " . DB_PREFIX . "user u 
                LEFT JOIN " . DB_PREFIX . "user_group ug ON (u.user_group_id = ug.user_group_id) 
                WHERE u.user_id = '" . (int)$user_id . "'";
        
        $query = $this->db->query($sql);
        
        return $query->row;
    }
    
    /**
     * الحصول على إحصائيات الإشعارات
     */
    public function getNotificationStats($user_id) {
        $stats = [
            'total' => 0,
            'unread' => 0,
            'critical' => 0,
            'urgent' => 0
        ];
        
        // إحصائيات الإشعارات العامة
        $sql = "SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN is_read = 0 THEN 1 ELSE 0 END) as unread,
                    SUM(CASE WHEN priority = 'critical' THEN 1 ELSE 0 END) as critical,
                    SUM(CASE WHEN priority = 'urgent' THEN 1 ELSE 0 END) as urgent
                FROM " . DB_PREFIX . "unified_notification 
                WHERE user_id = '" . (int)$user_id . "'";
        
        $query = $this->db->query($sql);
        
        if ($query->num_rows) {
            $stats = array_merge($stats, $query->row);
        }
        
        return $stats;
    }
    
    /**
     * الحصول على عدد المستخدمين النشطين
     */
    public function getActiveUsersCount() {
        // استخدام جدول user_session الموجود مع عمود date_modified بدلاً من last_activity
        $sql = "SELECT COUNT(DISTINCT user_id) as count
                FROM " . DB_PREFIX . "user_session
                WHERE date_modified > DATE_SUB(NOW(), INTERVAL 30 MINUTE)
                AND status = 1";

        $query = $this->db->query($sql);

        return $query->row['count'] ?? 0;
    }
    
    /**
     * الحصول على مبيعات اليوم
     */
    public function getTodaySales() {
        $sql = "SELECT COALESCE(SUM(total), 0) as total
                FROM " . DB_PREFIX . "order
                WHERE DATE(date_added) = CURDATE()
                AND order_status_id > 0";
        
        $query = $this->db->query($sql);
        
        return $query->row['total'] ?? 0;
    }
    
    /**
     * الحصول على عدد المهام المعلقة
     * استخدام جدول الإشعارات كبديل مؤقت حتى يتم إنشاء جدول المهام
     */
    public function getPendingTasksCount($user_id) {
        // استخدام الإشعارات من نوع 'approval' كبديل للمهام المعلقة
        $sql = "SELECT COUNT(*) as count
                FROM " . DB_PREFIX . "unified_notification
                WHERE user_id = '" . (int)$user_id . "'
                AND type = 'approval'
                AND read_at IS NULL";

        $query = $this->db->query($sql);

        return $query->row['count'] ?? 0;
    }
    
    /**
     * الحصول على حالة النظام
     */
    public function getSystemStatus() {
        $status = [
            'level' => 'healthy',
            'message' => 'System running normally',
            'checks' => []
        ];
        
        // فحص قاعدة البيانات
        try {
            $this->db->query("SELECT 1");
            $status['checks']['database'] = 'ok';
        } catch (Exception $e) {
            $status['level'] = 'critical';
            $status['message'] = 'Database connection failed';
            $status['checks']['database'] = 'error';
        }
        
        // فحص مساحة القرص
        if (function_exists('disk_free_space')) {
            $free_space = disk_free_space('/');
            $total_space = disk_total_space('/');
            
            if ($free_space && $total_space) {
                $usage_percent = (($total_space - $free_space) / $total_space) * 100;
                
                if ($usage_percent > 90) {
                    $status['level'] = 'warning';
                    $status['message'] = 'Low disk space';
                    $status['checks']['disk_space'] = 'warning';
                } else {
                    $status['checks']['disk_space'] = 'ok';
                }
            }
        }
        
        return $status;
    }
    
    /**
     * الحصول على مؤشرات الأداء السريعة
     */
    public function getQuickKPIs($user_id) {
        $kpis = [];
        
        try {
            // مبيعات اليوم
            $kpis['today_sales'] = $this->getTodaySales();
            
            // المهام المعلقة
            $kpis['pending_tasks'] = $this->getPendingTasksCount($user_id);
            
            // المستخدمين النشطين
            $kpis['active_users'] = $this->getActiveUsersCount();
            
            // الإشعارات غير المقروءة
            $stats = $this->getNotificationStats($user_id);
            $kpis['unread_notifications'] = $stats['unread'];
            
        } catch (Exception $e) {
            // في حالة الخطأ، إرجاع قيم افتراضية
            $kpis = [
                'today_sales' => 0,
                'pending_tasks' => 0,
                'active_users' => 1,
                'unread_notifications' => 0
            ];
        }
        
        return $kpis;
    }
    
    /**
     * تحديث آخر نشاط للمستخدم
     */
    public function updateUserActivity($user_id) {
        $sql = "INSERT INTO " . DB_PREFIX . "user_session 
                (user_id, last_activity) 
                VALUES ('" . (int)$user_id . "', NOW()) 
                ON DUPLICATE KEY UPDATE last_activity = NOW()";
        
        $this->db->query($sql);
    }
    
    /**
     * الحصول على إعدادات الهيدر للمستخدم
     */
    public function getUserHeaderSettings($user_id) {
        $sql = "SELECT setting_key, setting_value 
                FROM " . DB_PREFIX . "user_setting 
                WHERE user_id = '" . (int)$user_id . "' 
                AND setting_key LIKE 'header_%'";
        
        $query = $this->db->query($sql);
        
        $settings = [];
        foreach ($query->rows as $row) {
            $settings[$row['setting_key']] = $row['setting_value'];
        }
        
        return $settings;
    }
    
    /**
     * حفظ إعدادات الهيدر للمستخدم
     */
    public function saveUserHeaderSettings($user_id, $settings) {
        foreach ($settings as $key => $value) {
            $sql = "INSERT INTO " . DB_PREFIX . "user_setting 
                    (user_id, setting_key, setting_value) 
                    VALUES ('" . (int)$user_id . "', '" . $this->db->escape($key) . "', '" . $this->db->escape($value) . "') 
                    ON DUPLICATE KEY UPDATE setting_value = '" . $this->db->escape($value) . "'";
            
            $this->db->query($sql);
        }
    }
    
    /**
     * تنظيف البيانات القديمة
     */
    public function cleanupOldData() {
        // مسح جلسات المستخدمين القديمة (أكثر من 24 ساعة)
        $sql = "DELETE FROM " . DB_PREFIX . "user_session 
                WHERE last_activity < DATE_SUB(NOW(), INTERVAL 24 HOUR)";
        
        $this->db->query($sql);
        
        // مسح الإشعارات المقروءة القديمة (أكثر من 30 يوم)
        $sql = "DELETE FROM " . DB_PREFIX . "unified_notification 
                WHERE is_read = 1 
                AND created_at < DATE_SUB(NOW(), INTERVAL 30 DAY)";
        
        $this->db->query($sql);
    }
}
