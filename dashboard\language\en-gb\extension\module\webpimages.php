<?php
/******************************************************
 * @package Webp Images for OC2x, OC3x
 * @version 2.3
 * <AUTHOR>
 * @copyright Copyright (C)2020 aits.xyz All rights reserved.
 * @email:<EMAIL> 
 * $date: 11 JULY 2020
*******************************************************/
$_['text_version']	   = '2.3';
$_['heading_title']    = 'WebP Images 2.3';
$_['text_extension']   = 'Extensions';
$_['text_success']     = 'Success: You have modified WebP Images module!';
$_['text_edit']        = 'Edit Webp Images Module';
$_['text_gd']          = '<i class="fa fa-check" style="color:green;font-size:24px"></i>';
$_['text_error_gd']    = '<i class="fa fa-close" style="color:red;font-size:24px"></i>';
$_['entry_quality']    = 'Image Quality';
$_['entry_status']     = 'Status';
$_['entry_cookie']     = 'Cookie Support';
$_['help_gd'] ='';
$_['help_quality']		= 'Recommended image quality is 80;';
$_['error_permission']  = 'Warning: You do not have permission to modify WebP Image module!';
$_['help_gd']          = '<i class="fa fa-info" style="color:blue;font-size:24px;padding:10px"></i>PHP GD is required for this extension to work. <br>If GD WebP is not supported on your server. Please contact your hosting provider to enable Webp support. <br/><br/>For Extension Support visit <a href="https://aits.xyz/support" target="new">https://aits.xyz/support</a>';
$_['help_cookie']      = 'Cookie is recommended but if you are having issues with Litespeed server turning off is recommended';


