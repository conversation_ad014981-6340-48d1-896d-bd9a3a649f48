<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/cloud/translate/v3/translation_service.proto

namespace Google\Cloud\Translate\V3;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Stored in the
 * [google.longrunning.Operation.response][google.longrunning.Operation.response]
 * field returned by BatchTranslateText if at least one sentence is translated
 * successfully.
 *
 * Generated from protobuf message <code>google.cloud.translation.v3.BatchTranslateResponse</code>
 */
class BatchTranslateResponse extends \Google\Protobuf\Internal\Message
{
    /**
     * Total number of characters (Unicode codepoints).
     *
     * Generated from protobuf field <code>int64 total_characters = 1;</code>
     */
    private $total_characters = 0;
    /**
     * Number of successfully translated characters (Unicode codepoints).
     *
     * Generated from protobuf field <code>int64 translated_characters = 2;</code>
     */
    private $translated_characters = 0;
    /**
     * Number of characters that have failed to process (Unicode codepoints).
     *
     * Generated from protobuf field <code>int64 failed_characters = 3;</code>
     */
    private $failed_characters = 0;
    /**
     * Time when the operation was submitted.
     *
     * Generated from protobuf field <code>.google.protobuf.Timestamp submit_time = 4;</code>
     */
    private $submit_time = null;
    /**
     * The time when the operation is finished and
     * [google.longrunning.Operation.done][google.longrunning.Operation.done] is
     * set to true.
     *
     * Generated from protobuf field <code>.google.protobuf.Timestamp end_time = 5;</code>
     */
    private $end_time = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int|string $total_characters
     *           Total number of characters (Unicode codepoints).
     *     @type int|string $translated_characters
     *           Number of successfully translated characters (Unicode codepoints).
     *     @type int|string $failed_characters
     *           Number of characters that have failed to process (Unicode codepoints).
     *     @type \Google\Protobuf\Timestamp $submit_time
     *           Time when the operation was submitted.
     *     @type \Google\Protobuf\Timestamp $end_time
     *           The time when the operation is finished and
     *           [google.longrunning.Operation.done][google.longrunning.Operation.done] is
     *           set to true.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Cloud\Translate\V3\TranslationService::initOnce();
        parent::__construct($data);
    }

    /**
     * Total number of characters (Unicode codepoints).
     *
     * Generated from protobuf field <code>int64 total_characters = 1;</code>
     * @return int|string
     */
    public function getTotalCharacters()
    {
        return $this->total_characters;
    }

    /**
     * Total number of characters (Unicode codepoints).
     *
     * Generated from protobuf field <code>int64 total_characters = 1;</code>
     * @param int|string $var
     * @return $this
     */
    public function setTotalCharacters($var)
    {
        GPBUtil::checkInt64($var);
        $this->total_characters = $var;

        return $this;
    }

    /**
     * Number of successfully translated characters (Unicode codepoints).
     *
     * Generated from protobuf field <code>int64 translated_characters = 2;</code>
     * @return int|string
     */
    public function getTranslatedCharacters()
    {
        return $this->translated_characters;
    }

    /**
     * Number of successfully translated characters (Unicode codepoints).
     *
     * Generated from protobuf field <code>int64 translated_characters = 2;</code>
     * @param int|string $var
     * @return $this
     */
    public function setTranslatedCharacters($var)
    {
        GPBUtil::checkInt64($var);
        $this->translated_characters = $var;

        return $this;
    }

    /**
     * Number of characters that have failed to process (Unicode codepoints).
     *
     * Generated from protobuf field <code>int64 failed_characters = 3;</code>
     * @return int|string
     */
    public function getFailedCharacters()
    {
        return $this->failed_characters;
    }

    /**
     * Number of characters that have failed to process (Unicode codepoints).
     *
     * Generated from protobuf field <code>int64 failed_characters = 3;</code>
     * @param int|string $var
     * @return $this
     */
    public function setFailedCharacters($var)
    {
        GPBUtil::checkInt64($var);
        $this->failed_characters = $var;

        return $this;
    }

    /**
     * Time when the operation was submitted.
     *
     * Generated from protobuf field <code>.google.protobuf.Timestamp submit_time = 4;</code>
     * @return \Google\Protobuf\Timestamp
     */
    public function getSubmitTime()
    {
        return isset($this->submit_time) ? $this->submit_time : null;
    }

    public function hasSubmitTime()
    {
        return isset($this->submit_time);
    }

    public function clearSubmitTime()
    {
        unset($this->submit_time);
    }

    /**
     * Time when the operation was submitted.
     *
     * Generated from protobuf field <code>.google.protobuf.Timestamp submit_time = 4;</code>
     * @param \Google\Protobuf\Timestamp $var
     * @return $this
     */
    public function setSubmitTime($var)
    {
        GPBUtil::checkMessage($var, \Google\Protobuf\Timestamp::class);
        $this->submit_time = $var;

        return $this;
    }

    /**
     * The time when the operation is finished and
     * [google.longrunning.Operation.done][google.longrunning.Operation.done] is
     * set to true.
     *
     * Generated from protobuf field <code>.google.protobuf.Timestamp end_time = 5;</code>
     * @return \Google\Protobuf\Timestamp
     */
    public function getEndTime()
    {
        return isset($this->end_time) ? $this->end_time : null;
    }

    public function hasEndTime()
    {
        return isset($this->end_time);
    }

    public function clearEndTime()
    {
        unset($this->end_time);
    }

    /**
     * The time when the operation is finished and
     * [google.longrunning.Operation.done][google.longrunning.Operation.done] is
     * set to true.
     *
     * Generated from protobuf field <code>.google.protobuf.Timestamp end_time = 5;</code>
     * @param \Google\Protobuf\Timestamp $var
     * @return $this
     */
    public function setEndTime($var)
    {
        GPBUtil::checkMessage($var, \Google\Protobuf\Timestamp::class);
        $this->end_time = $var;

        return $this;
    }

}

