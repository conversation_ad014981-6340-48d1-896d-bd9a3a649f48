<?php
// Heading
$_['heading_title']          = 'Warranty Management';

// Text
$_['text_success']           = 'Success: You have modified warranties!';
$_['text_list']              = 'Warranty List';
$_['text_add']               = 'Add New Warranty';
$_['text_edit']              = 'Edit Warranty';
$_['text_delete']            = 'Delete Warranty';
$_['text_view_warranty']     = 'View Warranty';
$_['text_enabled']           = 'Enabled';
$_['text_disabled']          = 'Disabled';
$_['text_standard_warranty'] = 'Standard Warranty';
$_['text_extended_warranty'] = 'Extended Warranty';
$_['text_premium_warranty']  = 'Premium Warranty';
$_['text_custom_warranty']   = 'Custom Warranty';
$_['text_days']              = 'Days';
$_['text_weeks']             = 'Weeks';
$_['text_months']            = 'Months';
$_['text_years']             = 'Years';
$_['text_status_active']     = 'Active';
$_['text_status_expired']    = 'Expired';
$_['text_status_cancelled']  = 'Cancelled';
$_['text_status_suspended']  = 'Suspended';
$_['text_no_results']        = 'No results found';
$_['text_confirm']           = 'Are you sure you want to delete the selected warranties?';

// Entry
$_['entry_warranty_number']  = 'Warranty Number';
$_['entry_customer']         = 'Customer';
$_['entry_product']          = 'Product';
$_['entry_order_id']         = 'Order ID';
$_['entry_warranty_type']    = 'Warranty Type';
$_['entry_warranty_period']  = 'Warranty Period';
$_['entry_period_unit']      = 'Period Unit';
$_['entry_start_date']       = 'Start Date';
$_['entry_end_date']         = 'End Date';
$_['entry_description']      = 'Description';
$_['entry_terms_conditions'] = 'Terms & Conditions';
$_['entry_status']           = 'Status';
$_['entry_date_added']       = 'Date Added';

// Column
$_['column_warranty_number'] = 'Warranty Number';
$_['column_customer']        = 'Customer';
$_['column_product']         = 'Product';
$_['column_order_id']        = 'Order ID';
$_['column_warranty_type']   = 'Warranty Type';
$_['column_warranty_period'] = 'Warranty Period';
$_['column_start_date']      = 'Start Date';
$_['column_end_date']        = 'End Date';
$_['column_status']          = 'Status';
$_['column_date_added']      = 'Date Added';
$_['column_action']          = 'Action';

// Tab
$_['tab_general']            = 'General';
$_['tab_details']            = 'Details';

// Button
$_['button_add']             = 'Add';
$_['button_edit']            = 'Edit';
$_['button_delete']          = 'Delete';
$_['button_save']            = 'Save';
$_['button_cancel']          = 'Cancel';
$_['button_filter']          = 'Filter';
$_['button_view']            = 'View';

// Error
$_['error_permission']       = 'Warning: You do not have permission to modify warranties!';
$_['error_warranty_number']  = 'Warranty number is required!';
$_['error_customer']         = 'Customer is required!';
$_['error_product']          = 'Product is required!';
$_['error_warranty_period']  = 'Warranty period is required!';
$_['error_load_failed']      = 'Failed to load warranty data!';
