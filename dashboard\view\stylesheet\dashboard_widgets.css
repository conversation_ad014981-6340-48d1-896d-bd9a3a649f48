/**
 * AYM ERP Dashboard Widgets CSS
 * ملف CSS للتصميم المتقدم للـ Widgets
 * 
 * يحتوي على:
 * - تصميم متقدم للـ Widgets
 * - نظام الشبكة المرن
 * - تأثيرات بصرية متقدمة
 * - تصميم متجاوب
 * - دعم RTL
 */

/* === DASHBOARD CONTAINER === */
.dashboard-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
    padding: 20px;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
}

/* === WIDGET BASE STYLES === */
.dashboard-widget {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: all 0.3s ease;
    position: relative;
    min-height: 300px;
}

.dashboard-widget:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.dashboard-widget.dragging {
    opacity: 0.8;
    transform: rotate(2deg);
}

/* === WIDGET HEADER === */
.widget-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: move;
    position: relative;
}

.widget-header h5 {
    margin: 0;
    font-weight: 600;
    font-size: 16px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.widget-header i {
    font-size: 18px;
    opacity: 0.9;
}

.widget-controls {
    display: flex;
    gap: 5px;
}

.widget-controls .btn {
    padding: 4px 8px;
    font-size: 12px;
    border: none;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.widget-controls .btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
}

/* === WIDGET BODY === */
.widget-body {
    padding: 20px;
    height: calc(100% - 60px);
    overflow-y: auto;
}

/* === KPI CARDS === */
.kpi-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
    margin-bottom: 15px;
    position: relative;
    overflow: hidden;
}

.kpi-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.kpi-card:hover::before {
    transform: translateX(100%);
}

.kpi-value {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 5px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.kpi-label {
    font-size: 14px;
    opacity: 0.9;
    margin-bottom: 10px;
}

.kpi-trend {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    font-size: 12px;
}

.kpi-trend.positive {
    color: #28a745;
}

.kpi-trend.negative {
    color: #dc3545;
}

/* === CHART CONTAINERS === */
.chart-container {
    position: relative;
    height: 300px;
    margin: 15px 0;
}

.chart-container canvas {
    border-radius: 8px;
}

/* === TABLES === */
.widget-table {
    width: 100%;
    border-collapse: collapse;
    margin: 15px 0;
}

.widget-table th,
.widget-table td {
    padding: 12px;
    text-align: right;
    border-bottom: 1px solid #e9ecef;
}

.widget-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #495057;
    font-size: 14px;
}

.widget-table tbody tr {
    transition: background-color 0.2s ease;
}

.widget-table tbody tr:hover {
    background-color: #f8f9fa;
    cursor: pointer;
}

.widget-table tbody tr.selected {
    background-color: #e3f2fd;
}

/* === GAUGE CHARTS === */
.gauge-container {
    text-align: center;
    padding: 20px;
}

.gauge-value {
    font-size: 2rem;
    font-weight: 700;
    color: #495057;
    margin-bottom: 5px;
}

.gauge-label {
    font-size: 14px;
    color: #6c757d;
    margin-bottom: 15px;
}

.gauge-bar {
    width: 100%;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

.gauge-fill {
    height: 100%;
    border-radius: 4px;
    transition: width 1s ease;
    position: relative;
}

.gauge-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* === FUNNEL CHART === */
.funnel-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    padding: 20px;
}

.funnel-step {
    width: 100%;
    max-width: 300px;
    padding: 15px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px;
    text-align: center;
    position: relative;
    transition: all 0.3s ease;
}

.funnel-step:hover {
    transform: scale(1.02);
}

.funnel-step:nth-child(1) { width: 100%; }
.funnel-step:nth-child(2) { width: 80%; }
.funnel-step:nth-child(3) { width: 60%; }

.funnel-value {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 5px;
}

.funnel-label {
    font-size: 14px;
    opacity: 0.9;
}

.funnel-rate {
    font-size: 12px;
    opacity: 0.8;
    margin-top: 5px;
}

/* === ALERT STYLES === */
.alert-item {
    padding: 12px;
    margin-bottom: 10px;
    border-radius: 8px;
    border-right: 4px solid;
    background: #f8f9fa;
    transition: all 0.2s ease;
}

.alert-item:hover {
    transform: translateX(-5px);
}

.alert-item.critical {
    border-color: #dc3545;
    background: #f8d7da;
    color: #721c24;
}

.alert-item.warning {
    border-color: #ffc107;
    background: #fff3cd;
    color: #856404;
}

.alert-item.info {
    border-color: #17a2b8;
    background: #d1ecf1;
    color: #0c5460;
}

.alert-item.success {
    border-color: #28a745;
    background: #d4edda;
    color: #155724;
}

/* === LOADING STATES === */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-overlay i {
    font-size: 3rem;
    color: #667eea;
}

.widget-loading {
    position: relative;
    min-height: 200px;
}

.widget-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* === COMMAND BAR === */
.command-bar {
    background: #ffffff;
    padding: 15px 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.command-bar .input-group {
    max-width: 600px;
    margin: 0 auto;
}

.command-bar input {
    border: 2px solid #e9ecef;
    border-radius: 25px 0 0 25px;
    padding: 12px 20px;
    font-size: 16px;
    transition: all 0.3s ease;
}

.command-bar input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.command-bar .btn {
    border-radius: 0 25px 25px 0;
    padding: 12px 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    font-weight: 600;
    transition: all 0.3s ease;
}

.command-bar .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

/* === WIDGET SELECTOR === */
.widget-option {
    background: #ffffff;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.widget-option:hover {
    border-color: #667eea;
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
}

.widget-option-header {
    margin-bottom: 10px;
}

.widget-option-header i {
    font-size: 2rem;
    color: #667eea;
    margin-bottom: 10px;
    display: block;
}

.widget-option-header h6 {
    margin: 0;
    font-weight: 600;
    color: #495057;
}

.widget-option p {
    margin: 0;
    font-size: 14px;
    line-height: 1.4;
}

/* === DRILL-DOWN INDICATOR === */
.drill-down-indicator {
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 10px 20px;
    border-radius: 25px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

.drill-down-indicator .close {
    margin-right: 10px;
    cursor: pointer;
    opacity: 0.8;
}

.drill-down-indicator .close:hover {
    opacity: 1;
}

/* === RESPONSIVE DESIGN === */
@media (max-width: 1200px) {
    .dashboard-container {
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 15px;
        padding: 15px;
    }
}

@media (max-width: 768px) {
    .dashboard-container {
        grid-template-columns: 1fr;
        gap: 10px;
        padding: 10px;
    }
    
    .dashboard-widget {
        min-height: 250px;
    }
    
    .widget-header {
        padding: 12px 15px;
    }
    
    .widget-header h5 {
        font-size: 14px;
    }
    
    .widget-body {
        padding: 15px;
    }
    
    .kpi-value {
        font-size: 2rem;
    }
    
    .chart-container {
        height: 250px;
    }
    
    .command-bar {
        padding: 10px 15px;
    }
    
    .command-bar input {
        font-size: 14px;
        padding: 10px 15px;
    }
}

@media (max-width: 480px) {
    .dashboard-container {
        padding: 5px;
    }
    
    .dashboard-widget {
        min-height: 200px;
        border-radius: 8px;
    }
    
    .widget-header {
        padding: 10px 12px;
    }
    
    .widget-body {
        padding: 12px;
    }
    
    .kpi-value {
        font-size: 1.5rem;
    }
    
    .chart-container {
        height: 200px;
    }
}

/* === RTL SUPPORT === */
[dir="rtl"] .dashboard-container {
    direction: rtl;
}

[dir="rtl"] .widget-table th,
[dir="rtl"] .widget-table td {
    text-align: right;
}

[dir="rtl"] .widget-header {
    flex-direction: row-reverse;
}

[dir="rtl"] .widget-controls {
    flex-direction: row-reverse;
}

[dir="rtl"] .kpi-trend {
    flex-direction: row-reverse;
}

[dir="rtl"] .funnel-step {
    text-align: center;
}

[dir="rtl"] .alert-item {
    border-right: none;
    border-left: 4px solid;
}

[dir="rtl"] .alert-item:hover {
    transform: translateX(5px);
}

[dir="rtl"] .command-bar input {
    border-radius: 0 25px 25px 0;
}

[dir="rtl"] .command-bar .btn {
    border-radius: 25px 0 0 25px;
}

[dir="rtl"] .drill-down-indicator {
    right: auto;
    left: 20px;
}

[dir="rtl"] .drill-down-indicator .close {
    margin-right: 0;
    margin-left: 10px;
}

/* === DARK MODE SUPPORT === */
@media (prefers-color-scheme: dark) {
    .dashboard-container {
        background: linear-gradient(135deg, #1a1a1a 0%, #2d3748 100%);
    }
    
    .dashboard-widget {
        background: #2d3748;
        color: #e2e8f0;
    }
    
    .widget-header {
        background: linear-gradient(135deg, #4c51bf 0%, #553c9a 100%);
    }
    
    .kpi-card {
        background: linear-gradient(135deg, #4c51bf 0%, #553c9a 100%);
    }
    
    .widget-table th {
        background: #4a5568;
        color: #e2e8f0;
    }
    
    .widget-table tbody tr:hover {
        background-color: #4a5568;
    }
    
    .widget-table tbody tr.selected {
        background-color: #2c5282;
    }
    
    .gauge-bar {
        background: #4a5568;
    }
    
    .command-bar {
        background: #2d3748;
    }
    
    .command-bar input {
        background: #4a5568;
        border-color: #4a5568;
        color: #e2e8f0;
    }
    
    .widget-option {
        background: #2d3748;
        border-color: #4a5568;
        color: #e2e8f0;
    }
    
    .widget-option:hover {
        border-color: #4c51bf;
    }
    
    .widget-option-header h6 {
        color: #e2e8f0;
    }
}

/* === ANIMATIONS === */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.dashboard-widget {
    animation: fadeInUp 0.6s ease;
}

.dashboard-widget:nth-child(1) { animation-delay: 0.1s; }
.dashboard-widget:nth-child(2) { animation-delay: 0.2s; }
.dashboard-widget:nth-child(3) { animation-delay: 0.3s; }
.dashboard-widget:nth-child(4) { animation-delay: 0.4s; }
.dashboard-widget:nth-child(5) { animation-delay: 0.5s; }
.dashboard-widget:nth-child(6) { animation-delay: 0.6s; }

/* === PRINT STYLES === */
@media print {
    .dashboard-container {
        display: block;
        background: white;
    }
    
    .dashboard-widget {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ddd;
        margin-bottom: 20px;
    }
    
    .widget-controls,
    .command-bar,
    .drill-down-indicator {
        display: none !important;
    }
    
    .chart-container {
        height: 200px;
    }
} 