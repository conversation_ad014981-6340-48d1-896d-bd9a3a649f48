{{ header }}
{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  
  <div class="container-fluid">
    <!-- Filters -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-filter"></i> {{ text_filter }}</h3>
      </div>
      <div class="panel-body">
        <form id="filter-form" class="form-inline">
          <div class="form-group">
            <label for="filter_name">{{ text_lead_name }}</label>
            <input type="text" name="filter_name" id="filter_name" class="form-control" placeholder="{{ text_enter_lead_name }}" style="width:200px;" />
          </div>
          <div class="form-group">
            <label for="filter_status">{{ text_status }}</label>
            <select name="filter_status" id="filter_status" class="form-control select2" style="width:200px;">
              <option value="">{{ text_all_statuses }}</option>
              <option value="new">{{ text_status_new }}</option>
              <option value="contacted">{{ text_status_contacted }}</option>
              <option value="qualified">{{ text_status_qualified }}</option>
              <option value="unqualified">{{ text_status_unqualified }}</option>
              <option value="converted">{{ text_status_converted }}</option>
            </select>
          </div>
          <button type="button" id="btn-filter" class="btn btn-primary"><i class="fa fa-search"></i> {{ button_filter }}</button>
          <button type="button" id="btn-reset" class="btn btn-default"><i class="fa fa-eraser"></i> {{ button_reset }}</button>
        </form>
      </div>
    </div>
    
    <div class="text-right">
      <button type="button" class="btn btn-success" id="btn-add"><i class="fa fa-plus"></i> {{ button_add_lead }}</button>
    </div>
    
    <div class="panel panel-default" style="margin-top:10px;">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-user-plus"></i> {{ text_lead_list }}</h3>
      </div>
      <div class="panel-body">
        <table id="lead-table" class="table table-bordered table-striped">
          <thead>
            <tr>
              <th>{{ column_name }}</th>
              <th>{{ column_company }}</th>
              <th>{{ column_email }}</th>
              <th>{{ column_phone }}</th>
              <th>{{ column_status }}</th>
              <th>{{ column_actions }}</th>
            </tr>
          </thead>
          <tbody>
            <!-- DataTables AJAX -->
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>

<!-- Modal Add/Edit Lead -->
<div class="modal fade" id="modal-lead" tabindex="-1" role="dialog" aria-labelledby="modalLeadLabel">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <form id="lead-form">
        <div class="modal-header">
          <button type="button" class="close" data-dismiss="modal"><span>&times;</span></button>
          <h4 class="modal-title" id="modalLeadLabel">{{ text_add_lead }}</h4>
        </div>
        <div class="modal-body">
          <input type="hidden" name="lead_id" value="" />
          <div class="form-group">
            <label>{{ text_firstname }}</label>
            <input type="text" name="firstname" class="form-control" required />
          </div>
          <div class="form-group">
            <label>{{ text_lastname }}</label>
            <input type="text" name="lastname" class="form-control" />
          </div>
          <div class="form-group">
            <label>{{ text_company }}</label>
            <input type="text" name="company" class="form-control" />
          </div>
          <div class="form-group">
            <label>{{ text_email }}</label>
            <input type="email" name="email" class="form-control" />
          </div>
          <div class="form-group">
            <label>{{ text_phone }}</label>
            <input type="text" name="phone" class="form-control" />
          </div>
          <div class="form-group">
            <label>{{ text_source }}</label>
            <input type="text" name="source" class="form-control" />
          </div>
          <div class="form-group">
            <label>{{ text_status }}</label>
            <select name="status" class="form-control">
              <option value="new">{{ text_status_new }}</option>
              <option value="contacted">{{ text_status_contacted }}</option>
              <option value="qualified">{{ text_status_qualified }}</option>
              <option value="unqualified">{{ text_status_unqualified }}</option>
              <option value="converted">{{ text_status_converted }}</option>
            </select>
          </div>
          <div class="form-group">
            <label>{{ text_assigned_to }}</label>
            <select name="assigned_to_user_id" class="form-control select2" style="width:100%;">
              <option value="">{{ text_select_user }}</option>
              {% for user in users %}
              <option value="{{ user.user_id }}">{{ user.firstname }} {{ user.lastname }}</option>
              {% endfor %}
            </select>
          </div>
          <div class="form-group">
            <label>{{ text_notes }}</label>
            <textarea name="notes" class="form-control" rows="3"></textarea>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_close }}</button>
          <button type="submit" class="btn btn-primary">{{ button_save }}</button>
        </div>
      </form>
    </div>
  </div>
</div>

{{ footer }}

<script type="text/javascript">
$(document).ready(function() {
    $('#filter_status').select2({ placeholder: "{{ text_all_statuses }}" });
    $('select[name="assigned_to_user_id"]').select2({ placeholder: "{{ text_select_user }}" });

    var table = $('#lead-table').DataTable({
        "processing": true,
        "serverSide": true,
        "ajax": {
            "url": "{{ ajax_list_url }}",
            "type": "POST",
            "data": function (d) {
                d.user_token = "{{ user_token }}";
                d.filter_name = $('#filter_name').val();
                d.filter_status = $('#filter_status').val();
            }
        },
        "columns": [
            { "data": "name" },
            { "data": "company" },
            { "data": "email" },
            { "data": "phone" },
            { "data": "status" },
            { "data": "actions", "orderable": false, "searchable": false }
        ]
    });

    $('#btn-filter').on('click', function() {
        table.ajax.reload();
    });
    $('#btn-reset').on('click', function() {
        $('#filter_name').val('');
        $('#filter_status').val('').trigger('change');
        table.ajax.reload();
    });

    $('#btn-add').on('click', function() {
        $('#lead-form')[0].reset();
        $('input[name="lead_id"]').val('');
        $('select[name="status"]').val('new');
        $('select[name="assigned_to_user_id"]').val('').trigger('change');
        $('#modalLeadLabel').text("{{ text_add_lead }}");
        $('#modal-lead').modal('show');
    });

    $('#lead-form').on('submit', function(e) {
        e.preventDefault();
        $.ajax({
            url: "{{ ajax_save_url }}",
            type: "POST",
            data: $(this).serialize()+'&user_token={{ user_token }}',
            dataType: "json",
            success: function(json) {
                if (json.error) {
                    toastr.error(json.error);
                } else if (json.success) {
                    toastr.success(json.success);
                    $('#modal-lead').modal('hide');
                    table.ajax.reload();
                }
            },
            error: function() {
                toastr.error("{{ text_ajax_error }}");
            }
        });
    });

    $(document).on('click', '.btn-edit', function() {
        var id = $(this).data('id');
        $.ajax({
            url: "{{ ajax_get_url }}",
            type: "POST",
            data: {lead_id:id,user_token:"{{ user_token }}"},
            dataType: "json",
            success: function(json) {
                if (json.error) {
                    toastr.error(json.error);
                } else {
                    var data = json.data;
                    $('input[name="lead_id"]').val(data.lead_id);
                    $('input[name="firstname"]').val(data.firstname);
                    $('input[name="lastname"]').val(data.lastname);
                    $('input[name="company"]').val(data.company);
                    $('input[name="email"]').val(data.email);
                    $('input[name="phone"]').val(data.phone);
                    $('input[name="source"]').val(data.source);
                    $('select[name="status"]').val(data.status);
                    $('select[name="assigned_to_user_id"]').val(data.assigned_to_user_id).trigger('change');
                    $('textarea[name="notes"]').val(data.notes);
                    $('#modalLeadLabel').text("{{ text_edit_lead }}");
                    $('#modal-lead').modal('show');
                }
            },
            error: function() {
                toastr.error("{{ text_ajax_error }}");
            }
        });
    });

    $(document).on('click', '.btn-delete', function() {
        var id = $(this).data('id');
        if (confirm("{{ text_confirm_delete }}")) {
            $.ajax({
                url: "{{ ajax_delete_url }}",
                type: "POST",
                data: {lead_id:id,user_token:"{{ user_token }}"},
                dataType: "json",
                success: function(json) {
                    if (json.error) {
                        toastr.error(json.error);
                    } else {
                        toastr.success(json.success);
                        table.ajax.reload();
                    }
                },
                error: function() {
                    toastr.error("{{ text_ajax_error }}");
                }
            });
        }
    });

});
</script>
