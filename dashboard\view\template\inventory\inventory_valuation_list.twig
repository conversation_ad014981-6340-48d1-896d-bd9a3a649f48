{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <div class="btn-group">
          <button type="button" class="btn btn-success dropdown-toggle" data-toggle="dropdown">
            <i class="fa fa-download"></i> {{ button_export_excel }}
            <span class="caret"></span>
          </button>
          <ul class="dropdown-menu dropdown-menu-right">
            <li><a href="{{ export_excel }}"><i class="fa fa-file-excel-o"></i> {{ button_export_excel }}</a></li>
            <li><a href="{{ export_pdf }}"><i class="fa fa-file-pdf-o"></i> {{ button_export_pdf }}</a></li>
            <li class="divider"></li>
            <li><a href="{{ print }}" target="_blank"><i class="fa fa-print"></i> {{ button_print }}</a></li>
          </ul>
        </div>
        <div class="btn-group">
          <button type="button" class="btn btn-info dropdown-toggle" data-toggle="dropdown">
            <i class="fa fa-line-chart"></i> التحليلات المتقدمة
            <span class="caret"></span>
          </button>
          <ul class="dropdown-menu dropdown-menu-right">
            <li><a href="{{ compare_dates }}"><i class="fa fa-calendar"></i> {{ button_compare_dates }}</a></li>
            <li><a href="#" data-toggle="modal" data-target="#valuation-history-modal"><i class="fa fa-history"></i> {{ button_valuation_history }}</a></li>
          </ul>
        </div>
        <a href="{{ refresh }}" data-toggle="tooltip" title="{{ button_refresh }}" class="btn btn-default">
          <i class="fa fa-refresh"></i>
        </a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>

  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible">
      <i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}

    {% if success %}
    <div class="alert alert-success alert-dismissible">
      <i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}

    <!-- ملخص التقييم الشامل -->
    <div class="row">
      <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="panel panel-primary">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-cubes fa-2x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ summary.total_products }}</div>
                <div>{{ text_total_products }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="panel panel-green">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-money fa-2x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ summary.total_cost_value }}</div>
                <div>{{ text_total_cost_value }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="panel panel-blue">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-shopping-cart fa-2x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ summary.total_selling_value }}</div>
                <div>{{ text_total_selling_value }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="panel panel-yellow">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-line-chart fa-2x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ summary.total_profit }}</div>
                <div>{{ text_total_profit }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="panel panel-purple">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-percent fa-2x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ summary.avg_profit_percentage }}</div>
                <div>{{ text_avg_profit_percentage }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="panel panel-orange">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-calculator fa-2x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ summary.total_quantity }}</div>
                <div>{{ text_total_quantity }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- تنبيهات المخزون -->
    {% if summary.out_of_stock_count > 0 or summary.low_stock_count > 0 or summary.overstock_count > 0 %}
    <div class="row">
      <div class="col-md-12">
        <div class="panel panel-warning">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-exclamation-triangle"></i> {{ text_alerts }}</h3>
          </div>
          <div class="panel-body">
            <div class="row">
              {% if summary.out_of_stock_count > 0 %}
              <div class="col-md-4">
                <div class="alert alert-danger">
                  <strong>{{ summary.out_of_stock_count }}</strong> منتج نفد من المخزون
                </div>
              </div>
              {% endif %}

              {% if summary.low_stock_count > 0 %}
              <div class="col-md-4">
                <div class="alert alert-warning">
                  <strong>{{ summary.low_stock_count }}</strong> منتج منخفض المخزون
                </div>
              </div>
              {% endif %}

              {% if summary.overstock_count > 0 %}
              <div class="col-md-4">
                <div class="alert alert-info">
                  <strong>{{ summary.overstock_count }}</strong> منتج زائد المخزون
                </div>
              </div>
              {% endif %}
            </div>
          </div>
        </div>
      </div>
    </div>
    {% endif %}

    <!-- التحليلات والتقارير -->
    <div class="row">
      <!-- التقييم حسب التصنيف -->
      {% if valuation_by_category %}
      <div class="col-md-6">
        <div class="panel panel-info">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-pie-chart"></i> {{ text_valuation_by_category }}</h3>
          </div>
          <div class="panel-body">
            <p class="text-muted">{{ text_valuation_by_category_desc }}</p>
            <div class="table-responsive">
              <table class="table table-condensed">
                <thead>
                  <tr>
                    <th>{{ column_category }}</th>
                    <th class="text-right">{{ text_total_cost_value }}</th>
                    <th class="text-center">{{ text_percentage_of_total }}</th>
                  </tr>
                </thead>
                <tbody>
                  {% for category in valuation_by_category %}
                  <tr>
                    <td><strong>{{ category.category_name }}</strong></td>
                    <td class="text-right">{{ category.total_cost_value }}</td>
                    <td class="text-center">
                      <span class="badge badge-info">{{ category.percentage_of_total }}</span>
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
      {% endif %}

      <!-- التقييم حسب الفرع -->
      {% if valuation_by_branch %}
      <div class="col-md-6">
        <div class="panel panel-success">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-building"></i> {{ text_valuation_by_branch }}</h3>
          </div>
          <div class="panel-body">
            <p class="text-muted">{{ text_valuation_by_branch_desc }}</p>
            <div class="table-responsive">
              <table class="table table-condensed">
                <thead>
                  <tr>
                    <th>{{ column_branch }}</th>
                    <th class="text-right">{{ text_total_cost_value }}</th>
                    <th class="text-center">{{ text_percentage_of_total }}</th>
                  </tr>
                </thead>
                <tbody>
                  {% for branch in valuation_by_branch %}
                  <tr>
                    <td>
                      <strong>{{ branch.branch_name }}</strong>
                      <br><small class="text-muted">{{ branch.branch_type }}</small>
                    </td>
                    <td class="text-right">{{ branch.total_cost_value }}</td>
                    <td class="text-center">
                      <span class="badge badge-success">{{ branch.percentage_of_total }}</span>
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
      {% endif %}
    </div>

    <!-- أعلى المنتجات قيمة وأكثرها ربحية -->
    <div class="row">
      <!-- أعلى المنتجات قيمة -->
      {% if top_value_products %}
      <div class="col-md-6">
        <div class="panel panel-primary">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-star"></i> {{ text_top_value_products }}</h3>
          </div>
          <div class="panel-body">
            <p class="text-muted">{{ text_top_value_products_desc }}</p>
            <div class="table-responsive">
              <table class="table table-condensed">
                <thead>
                  <tr>
                    <th>{{ column_product_name }}</th>
                    <th class="text-right">{{ text_total_cost_value }}</th>
                    <th class="text-right">{{ text_total_profit }}</th>
                  </tr>
                </thead>
                <tbody>
                  {% for product in top_value_products %}
                  <tr>
                    <td>
                      <strong>{{ product.product_name }}</strong>
                      <br><small class="text-muted">{{ product.model }}</small>
                    </td>
                    <td class="text-right">{{ product.total_cost_value }}</td>
                    <td class="text-right">{{ product.total_profit }}</td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
      {% endif %}

      <!-- أكثر المنتجات ربحية -->
      {% if most_profitable_products %}
      <div class="col-md-6">
        <div class="panel panel-warning">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-trophy"></i> {{ text_most_profitable_products }}</h3>
          </div>
          <div class="panel-body">
            <p class="text-muted">{{ text_most_profitable_products_desc }}</p>
            <div class="table-responsive">
              <table class="table table-condensed">
                <thead>
                  <tr>
                    <th>{{ column_product_name }}</th>
                    <th class="text-right">{{ text_total_profit }}</th>
                    <th class="text-center">{{ column_profit_percentage }}</th>
                  </tr>
                </thead>
                <tbody>
                  {% for product in most_profitable_products %}
                  <tr>
                    <td>
                      <strong>{{ product.product_name }}</strong>
                      <br><small class="text-muted">{{ product.model }}</small>
                    </td>
                    <td class="text-right">{{ product.total_profit }}</td>
                    <td class="text-center">
                      <span class="badge badge-warning">{{ product.avg_profit_percentage }}</span>
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
      {% endif %}
    </div>

    <!-- الفلاتر المتقدمة -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title">
          <i class="fa fa-filter"></i> {{ text_advanced_filters }}
          <button type="button" class="btn btn-xs btn-default pull-right" data-toggle="collapse" data-target="#filter-panel">
            <i class="fa fa-chevron-down"></i>
          </button>
        </h3>
      </div>
      <div id="filter-panel" class="panel-collapse collapse">
        <div class="panel-body">
          <form method="get" id="filter-form">
            <input type="hidden" name="route" value="inventory/inventory_valuation" />
            <input type="hidden" name="user_token" value="{{ user_token }}" />

            <div class="row">
              <div class="col-md-3">
                <div class="form-group">
                  <label for="valuation_date">{{ entry_valuation_date }}</label>
                  <input type="date" name="valuation_date" value="{{ valuation_date }}" id="valuation_date" class="form-control" />
                  <div class="help-block">{{ help_valuation_date }}</div>
                </div>
              </div>

              <div class="col-md-3">
                <div class="form-group">
                  <label for="filter_product_name">{{ entry_filter_product_name }}</label>
                  <input type="text" name="filter_product_name" value="{{ filter_product_name }}" placeholder="{{ entry_filter_product_name }}" id="filter_product_name" class="form-control" />
                  <div class="help-block">{{ help_filter_product_name }}</div>
                </div>
              </div>

              <div class="col-md-3">
                <div class="form-group">
                  <label for="filter_category_id">{{ entry_filter_category }}</label>
                  <select name="filter_category_id" id="filter_category_id" class="form-control">
                    <option value="">{{ text_all }}</option>
                    {% for category in categories %}
                    <option value="{{ category.category_id }}"{% if category.category_id == filter_category_id %} selected="selected"{% endif %}>{{ category.name }}</option>
                    {% endfor %}
                  </select>
                </div>
              </div>

              <div class="col-md-3">
                <div class="form-group">
                  <label for="filter_branch_id">{{ entry_filter_branch }}</label>
                  <select name="filter_branch_id" id="filter_branch_id" class="form-control">
                    <option value="">{{ text_all }}</option>
                    {% for branch in branches %}
                    <option value="{{ branch.branch_id }}"{% if branch.branch_id == filter_branch_id %} selected="selected"{% endif %}>{{ branch.name }}</option>
                    {% endfor %}
                  </select>
                </div>
              </div>
            </div>

            <div class="row">
              <div class="col-md-3">
                <div class="form-group">
                  <label for="filter_min_value">{{ entry_filter_min_value }}</label>
                  <input type="number" name="filter_min_value" value="{{ filter_min_value }}" placeholder="{{ entry_filter_min_value }}" id="filter_min_value" class="form-control" step="0.01" />
                </div>
              </div>

              <div class="col-md-3">
                <div class="form-group">
                  <label for="filter_max_value">{{ entry_filter_max_value }}</label>
                  <input type="number" name="filter_max_value" value="{{ filter_max_value }}" placeholder="{{ entry_filter_max_value }}" id="filter_max_value" class="form-control" step="0.01" />
                </div>
              </div>

              <div class="col-md-3">
                <div class="form-group">
                  <label for="filter_min_profit_percentage">{{ entry_filter_min_profit_percentage }}</label>
                  <input type="number" name="filter_min_profit_percentage" value="{{ filter_min_profit_percentage }}" placeholder="{{ entry_filter_min_profit_percentage }}" id="filter_min_profit_percentage" class="form-control" step="0.01" />
                </div>
              </div>

              <div class="col-md-3">
                <div class="form-group">
                  <label>&nbsp;</label>
                  <div>
                    <button type="submit" class="btn btn-primary"><i class="fa fa-filter"></i> {{ button_filter }}</button>
                    <a href="{{ refresh }}" class="btn btn-default"><i class="fa fa-refresh"></i> {{ button_clear }}</a>
                  </div>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- جدول تقييم المخزون -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-list"></i> {{ text_list }}</h3>
      </div>
      <div class="panel-body">
        <div class="table-responsive">
          <table class="table table-bordered table-hover">
            <thead>
              <tr>
                <th>{{ column_product_name }}</th>
                <th>{{ column_category }}</th>
                <th>{{ column_branch }}</th>
                <th class="text-right">{{ column_quantity }}</th>
                <th class="text-right">{{ column_average_cost }}</th>
                <th class="text-right">{{ column_total_value }}</th>
                <th class="text-right">{{ column_selling_price }}</th>
                <th class="text-right">{{ column_total_profit }}</th>
                <th class="text-center">{{ column_profit_percentage }}</th>
                <th class="text-center">{{ column_stock_status }}</th>
                <th class="text-center">{{ column_action }}</th>
              </tr>
            </thead>
            <tbody>
              {% if inventory_valuation %}
              {% for item in inventory_valuation %}
              <tr>
                <td>
                  <strong>{{ item.product_name }}</strong>
                  {% if item.model %}
                  <br><small class="text-muted">{{ item.model }}</small>
                  {% endif %}
                  {% if item.sku %}
                  <br><small class="text-muted">{{ item.sku }}</small>
                  {% endif %}
                </td>
                <td>{{ item.category_name }}</td>
                <td>
                  {{ item.branch_name }}
                  <br><small class="text-muted">{{ item.branch_type }}</small>
                </td>
                <td class="text-right">
                  <strong>{{ item.quantity }}</strong>
                  <br><small class="text-muted">{{ item.unit_symbol }}</small>
                </td>
                <td class="text-right">
                  {{ item.average_cost }}
                  {% if item.historical_avg_cost %}
                  <br><small class="text-muted">تاريخي: {{ item.historical_avg_cost }}</small>
                  {% endif %}
                </td>
                <td class="text-right">
                  <strong>{{ item.total_value }}</strong>
                </td>
                <td class="text-right">
                  {{ item.selling_price }}
                  <br><small class="text-muted">إجمالي: {{ item.total_selling_value }}</small>
                </td>
                <td class="text-right">
                  {% if item.total_profit_raw >= 0 %}
                  <span class="text-success"><strong>{{ item.total_profit }}</strong></span>
                  {% else %}
                  <span class="text-danger"><strong>{{ item.total_profit }}</strong></span>
                  {% endif %}
                </td>
                <td class="text-center">
                  <span class="label label-{{ item.profit_class }}">{{ item.profit_percentage }}</span>
                </td>
                <td class="text-center">
                  <span class="label label-{{ item.stock_status_class }}">{{ item.stock_status_text }}</span>
                </td>
                <td class="text-center">
                  <div class="btn-group">
                    <a href="{{ item.view_movements }}" data-toggle="tooltip" title="{{ button_view_movements }}" class="btn btn-info btn-xs">
                      <i class="fa fa-list"></i>
                    </a>
                    <a href="{{ item.edit_product }}" data-toggle="tooltip" title="{{ button_edit_product }}" class="btn btn-primary btn-xs">
                      <i class="fa fa-pencil"></i>
                    </a>
                  </div>
                </td>
              </tr>
              {% endfor %}
              {% else %}
              <tr>
                <td class="text-center" colspan="11">{{ text_no_results }}</td>
              </tr>
              {% endif %}
            </tbody>
          </table>
        </div>

        <div class="row">
          <div class="col-sm-6 text-left">{{ pagination }}</div>
          <div class="col-sm-6 text-right">{{ results }}</div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
.huge {
    font-size: 28px;
}

.panel-green {
    border-color: #5cb85c;
}

.panel-green > .panel-heading {
    border-color: #5cb85c;
    color: white;
    background-color: #5cb85c;
}

.panel-blue {
    border-color: #337ab7;
}

.panel-blue > .panel-heading {
    border-color: #337ab7;
    color: white;
    background-color: #337ab7;
}

.panel-yellow {
    border-color: #f0ad4e;
}

.panel-yellow > .panel-heading {
    border-color: #f0ad4e;
    color: white;
    background-color: #f0ad4e;
}

.panel-purple {
    border-color: #9b59b6;
}

.panel-purple > .panel-heading {
    border-color: #9b59b6;
    color: white;
    background-color: #9b59b6;
}

.panel-orange {
    border-color: #e67e22;
}

.panel-orange > .panel-heading {
    border-color: #e67e22;
    color: white;
    background-color: #e67e22;
}

.badge-info {
    background-color: #5bc0de;
}

.badge-success {
    background-color: #5cb85c;
}

.badge-warning {
    background-color: #f0ad4e;
}

.table > tbody > tr > td {
    vertical-align: middle;
}

.profit-positive {
    background-color: #f0f9ff;
}

.profit-negative {
    background-color: #fff5f5;
}
</style>

<script type="text/javascript">
$(document).ready(function() {
    // تهيئة التلميحات
    $('[data-toggle="tooltip"]').tooltip();

    // تلوين الصفوف حسب الربحية
    $('table tbody tr').each(function() {
        var profitText = $(this).find('td:nth-child(8)').text();
        if (profitText.includes('-')) {
            $(this).addClass('profit-negative');
        } else {
            $(this).addClass('profit-positive');
        }
    });

    // تحديث تلقائي عند تغيير تاريخ التقييم
    $('#valuation_date').on('change', function() {
        $('#filter-form').submit();
    });

    // فلترة سريعة
    $('#filter_product_name').on('keyup', function() {
        var value = $(this).val().toLowerCase();
        $('table tbody tr').filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
        });
    });
});
</script>

{{ footer }}
