<?php
/**
 * ═══════════════════════════════════════════════════════════════════════════════
 * AYM ERP SYSTEM - Unified Document Management System
 * ═══════════════════════════════════════════════════════════════════════════════
 * 
 * نظام إدارة المستندات الموحد المتقدم - يدير جميع المستندات في النظام
 * 
 * الميزات:
 * - تخزين مركزي للمستندات
 * - نظام إصدارات متقدم
 * - تحكم في الصلاحيات
 * - بحث متقدم
 * - تصنيف وتنظيم
 * - تكامل مع جميع الوحدات
 * - نظام مراجعة وموافقات
 * - أرشفة تلقائية
 * - تتبع التغييرات
 * 
 * @package    AYM ERP
 * <AUTHOR> Development Team
 * @copyright  2025 AYM ERP Systems
 * @license    Commercial License
 * @version    1.0.0
 * @since      2025-07-12
 */

class ModelUnifiedDocument extends Model {
    
    /**
     * إضافة مستند جديد
     *
     * @param array $data بيانات المستند
     * @return int معرف المستند الجديد
     */
    public function addDocument($data) {
        // التحقق من البيانات المطلوبة
        if (empty($data['title']) || empty($data['file_path'])) {
            throw new Exception('Title and file path are required');
        }

        // تعيين القيم الافتراضية
        $data['version'] = $data['version'] ?? '1.0';
        $data['status'] = $data['status'] ?? 'draft';
        $data['created_at'] = date('Y-m-d H:i:s');
        $data['created_by'] = $data['created_by'] ?? $this->user->getId();

        $this->db->query("INSERT INTO " . DB_PREFIX . "unified_document SET
            title = '" . $this->db->escape($data['title']) . "',
            description = '" . $this->db->escape($data['description']) . "',
            file_path = '" . $this->db->escape($data['file_path']) . "',
            file_name = '" . $this->db->escape($data['file_name']) . "',
            file_size = '" . (int)$data['file_size'] . "',
            file_type = '" . $this->db->escape($data['file_type']) . "',
            mime_type = '" . $this->db->escape($data['mime_type']) . "',
            category = '" . $this->db->escape($data['category']) . "',
            tags = '" . $this->db->escape(json_encode($data['tags'] ?? [])) . "',
            version = '" . $this->db->escape($data['version']) . "',
            module = '" . $this->db->escape($data['module']) . "',
            reference_type = '" . $this->db->escape($data['reference_type']) . "',
            reference_id = '" . (int)$data['reference_id'] . "',
            status = '" . $this->db->escape($data['status']) . "',
            is_public = '" . (int)$data['is_public'] . "',
            created_at = '" . $this->db->escape($data['created_at']) . "',
            created_by = '" . (int)$data['created_by'] . "'");

        $document_id = $this->db->getLastId();

        // إضافة سجل الإصدار
        $this->addVersion($document_id, $data);

        // تسجيل النشاط باستخدام الخدمات المركزية
        try {
            $this->load->model('core/central_service_manager');
            $this->model_core_central_service_manager->logCreate(
                'documents',
                'document',
                $document_id,
                "تم إنشاء مستند جديد: {$data['title']}",
                [
                    'document_data' => $data,
                    'created_by' => $data['created_by']
                ]
            );
        } catch (Exception $e) {
            error_log("Failed to log document creation via central service: " . $e->getMessage());

            // النظام الاحتياطي
            try {
                $this->load->model('activity_log');
                $activity_data = array(
                    'action_type' => 'document_created',
                    'module' => 'documents',
                    'description' => "تم إنشاء مستند جديد: {$data['title']}",
                    'user_id' => $data['created_by'],
                    'related_id' => $document_id,
                    'related_type' => 'document',
                    'additional_data' => json_encode($data)
                );
                $this->model_activity_log->addActivity($activity_data);
            } catch (Exception $e2) {
                error_log("CRITICAL: Both logging systems failed: " . $e2->getMessage());
            }
        }

        return $document_id;
    }

    /**
     * تحديث مستند موجود
     */
    public function updateDocument($document_id, $data) {
        $document = $this->getDocument($document_id);
        if (!$document) {
            return false;
        }

        // التحقق من الصلاحيات
        if (!$this->hasPermission($document_id, 'edit')) {
            throw new Exception('No permission to edit this document');
        }

        // إنشاء إصدار جديد إذا تم تغيير الملف
        if (!empty($data['file_path']) && $data['file_path'] !== $document['file_path']) {
            $data['version'] = $this->incrementVersion($document['version']);
            $this->addVersion($document_id, $data);
        }

        $this->db->query("UPDATE " . DB_PREFIX . "unified_document SET
            title = '" . $this->db->escape($data['title']) . "',
            description = '" . $this->db->escape($data['description']) . "',
            file_path = '" . $this->db->escape($data['file_path']) . "',
            file_name = '" . $this->db->escape($data['file_name']) . "',
            file_size = '" . (int)$data['file_size'] . "',
            file_type = '" . $this->db->escape($data['file_type']) . "',
            mime_type = '" . $this->db->escape($data['mime_type']) . "',
            category = '" . $this->db->escape($data['category']) . "',
            tags = '" . $this->db->escape(json_encode($data['tags'] ?? [])) . "',
            version = '" . $this->db->escape($data['version']) . "',
            status = '" . $this->db->escape($data['status']) . "',
            is_public = '" . (int)$data['is_public'] . "',
            updated_at = NOW(),
            updated_by = '" . (int)$data['updated_by'] . "'
            WHERE document_id = '" . (int)$document_id . "'");

        // تسجيل النشاط باستخدام الخدمات المركزية
        try {
            $this->load->model('core/central_service_manager');
            $this->model_core_central_service_manager->logUpdate(
                'documents',
                'document',
                $document_id,
                "تم تحديث المستند: {$data['title']}",
                [
                    'old_data' => $document,
                    'new_data' => $data,
                    'updated_by' => $data['updated_by']
                ]
            );
        } catch (Exception $e) {
            error_log("Failed to log document update via central service: " . $e->getMessage());
        }

        return true;
    }

    /**
     * حذف مستند
     */
    public function deleteDocument($document_id) {
        $document = $this->getDocument($document_id);
        if (!$document) {
            return false;
        }

        // التحقق من الصلاحيات
        if (!$this->hasPermission($document_id, 'delete')) {
            throw new Exception('No permission to delete this document');
        }

        // حذف الملف الفعلي
        if (file_exists($document['file_path'])) {
            unlink($document['file_path']);
        }

        // حذف من قاعدة البيانات
        $this->db->query("DELETE FROM " . DB_PREFIX . "unified_document WHERE document_id = '" . (int)$document_id . "'");
        $this->db->query("DELETE FROM " . DB_PREFIX . "document_version WHERE document_id = '" . (int)$document_id . "'");
        $this->db->query("DELETE FROM " . DB_PREFIX . "document_permission WHERE document_id = '" . (int)$document_id . "'");

        // تسجيل النشاط باستخدام الخدمات المركزية
        try {
            $this->load->model('core/central_service_manager');
            $this->model_core_central_service_manager->logDelete(
                'documents',
                'document',
                $document_id,
                "تم حذف المستند: {$document['title']}",
                [
                    'deleted_data' => $document,
                    'deleted_by' => $this->user->getId()
                ]
            );
        } catch (Exception $e) {
            error_log("Failed to log document deletion via central service: " . $e->getMessage());
        }

        return true;
    }

    /**
     * الحصول على مستند
     */
    public function getDocument($document_id) {
        $query = $this->db->query("SELECT ud.*, u.username, u.firstname, u.lastname 
            FROM " . DB_PREFIX . "unified_document ud
            LEFT JOIN " . DB_PREFIX . "user u ON (ud.created_by = u.user_id)
            WHERE ud.document_id = '" . (int)$document_id . "'");
        
        return $query->num_rows ? $query->row : false;
    }

    /**
     * الحصول على المستندات
     */
    public function getDocuments($filter = [], $limit = 20, $start = 0) {
        $sql = "SELECT ud.*, u.username, u.firstname, u.lastname 
            FROM " . DB_PREFIX . "unified_document ud
            LEFT JOIN " . DB_PREFIX . "user u ON (ud.created_by = u.user_id)
            WHERE 1=1";

        // تطبيق الفلاتر
        if (!empty($filter['category'])) {
            $sql .= " AND ud.category = '" . $this->db->escape($filter['category']) . "'";
        }

        if (!empty($filter['module'])) {
            $sql .= " AND ud.module = '" . $this->db->escape($filter['module']) . "'";
        }

        if (!empty($filter['reference_type'])) {
            $sql .= " AND ud.reference_type = '" . $this->db->escape($filter['reference_type']) . "'";
        }

        if (!empty($filter['reference_id'])) {
            $sql .= " AND ud.reference_id = '" . (int)$filter['reference_id'] . "'";
        }

        if (!empty($filter['status'])) {
            $sql .= " AND ud.status = '" . $this->db->escape($filter['status']) . "'";
        }

        if (!empty($filter['search'])) {
            $search = $this->db->escape($filter['search']);
            $sql .= " AND (ud.title LIKE '%{$search}%' OR ud.description LIKE '%{$search}%')";
        }

        // فلترة حسب الصلاحيات
        if (!$this->user->hasPermission('access', 'document/all')) {
            $user_id = $this->user->getId();
            $sql .= " AND (ud.created_by = '{$user_id}' OR ud.is_public = '1' OR 
                ud.document_id IN (SELECT document_id FROM " . DB_PREFIX . "document_permission 
                WHERE user_id = '{$user_id}' OR group_id IN (SELECT user_group_id FROM " . DB_PREFIX . "user WHERE user_id = '{$user_id}')))";
        }

        $sql .= " ORDER BY ud.created_at DESC";
        $sql .= " LIMIT " . (int)$start . "," . (int)$limit;

        $query = $this->db->query($sql);
        return $query->rows;
    }

    /**
     * الحصول على إجمالي عدد المستندات
     */
    public function getTotalDocuments($filter = []) {
        $sql = "SELECT COUNT(*) as total FROM " . DB_PREFIX . "unified_document ud WHERE 1=1";

        // تطبيق نفس الفلاتر
        if (!empty($filter['category'])) {
            $sql .= " AND ud.category = '" . $this->db->escape($filter['category']) . "'";
        }

        if (!empty($filter['module'])) {
            $sql .= " AND ud.module = '" . $this->db->escape($filter['module']) . "'";
        }

        if (!empty($filter['status'])) {
            $sql .= " AND ud.status = '" . $this->db->escape($filter['status']) . "'";
        }

        if (!empty($filter['search'])) {
            $search = $this->db->escape($filter['search']);
            $sql .= " AND (ud.title LIKE '%{$search}%' OR ud.description LIKE '%{$search}%')";
        }

        // فلترة حسب الصلاحيات
        if (!$this->user->hasPermission('access', 'document/all')) {
            $user_id = $this->user->getId();
            $sql .= " AND (ud.created_by = '{$user_id}' OR ud.is_public = '1' OR 
                ud.document_id IN (SELECT document_id FROM " . DB_PREFIX . "document_permission 
                WHERE user_id = '{$user_id}' OR group_id IN (SELECT user_group_id FROM " . DB_PREFIX . "user WHERE user_id = '{$user_id}')))";
        }

        $query = $this->db->query($sql);
        return $query->row['total'];
    }

    /**
     * رفع ملف
     */
    public function uploadFile($file, $allowed_types = []) {
        if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
            throw new Exception('No file uploaded');
        }

        // التحقق من نوع الملف
        $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!empty($allowed_types) && !in_array($file_extension, $allowed_types)) {
            throw new Exception('File type not allowed');
        }

        // التحقق من حجم الملف
        $max_size = $this->config->get('config_max_file_size', 10485760); // 10MB default
        if ($file['size'] > $max_size) {
            throw new Exception('File size exceeds limit');
        }

        // إنشاء مجلد التخزين
        $upload_dir = DIR_STORAGE . 'documents/' . date('Y/m/');
        if (!is_dir($upload_dir)) {
            mkdir($upload_dir, 0755, true);
        }

        // إنشاء اسم فريد للملف
        $filename = uniqid() . '_' . $file['name'];
        $file_path = $upload_dir . $filename;

        // نقل الملف
        if (!move_uploaded_file($file['tmp_name'], $file_path)) {
            throw new Exception('Failed to move uploaded file');
        }

        return [
            'file_path' => $file_path,
            'file_name' => $file['name'],
            'file_size' => $file['size'],
            'file_type' => $file_extension,
            'mime_type' => $file['type']
        ];
    }

    /**
     * إضافة إصدار جديد
     */
    private function addVersion($document_id, $data) {
        $this->db->query("INSERT INTO " . DB_PREFIX . "document_version SET
            document_id = '" . (int)$document_id . "',
            version = '" . $this->db->escape($data['version']) . "',
            file_path = '" . $this->db->escape($data['file_path']) . "',
            file_name = '" . $this->db->escape($data['file_name']) . "',
            file_size = '" . (int)$data['file_size'] . "',
            created_at = NOW(),
            created_by = '" . (int)$data['created_by'] . "'");
    }

    /**
     * زيادة رقم الإصدار
     */
    private function incrementVersion($version) {
        $parts = explode('.', $version);
        $parts[count($parts) - 1]++;
        return implode('.', $parts);
    }

    /**
     * الحصول على إصدارات المستند
     */
    public function getDocumentVersions($document_id) {
        $query = $this->db->query("SELECT dv.*, u.username, u.firstname, u.lastname 
            FROM " . DB_PREFIX . "document_version dv
            LEFT JOIN " . DB_PREFIX . "user u ON (dv.created_by = u.user_id)
            WHERE dv.document_id = '" . (int)$document_id . "'
            ORDER BY dv.created_at DESC");
        
        return $query->rows;
    }

    /**
     * التحقق من الصلاحيات
     */
    public function hasPermission($document_id, $action) {
        $user_id = $this->user->getId();
        
        // المدير لديه جميع الصلاحيات
        if ($this->user->hasPermission('access', 'document/all')) {
            return true;
        }

        // صاحب المستند لديه جميع الصلاحيات
        $document = $this->getDocument($document_id);
        if ($document && $document['created_by'] == $user_id) {
            return true;
        }

        // التحقق من الصلاحيات المخصصة
        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "document_permission 
            WHERE document_id = '" . (int)$document_id . "' 
            AND (user_id = '" . (int)$user_id . "' OR group_id IN (SELECT user_group_id FROM " . DB_PREFIX . "user WHERE user_id = '" . (int)$user_id . "'))
            AND action = '" . $this->db->escape($action) . "'");
        
        return $query->num_rows > 0;
    }

    /**
     * إضافة صلاحية
     */
    public function addPermission($document_id, $user_id, $group_id, $action) {
        $this->db->query("INSERT INTO " . DB_PREFIX . "document_permission SET
            document_id = '" . (int)$document_id . "',
            user_id = '" . (int)$user_id . "',
            group_id = '" . (int)$group_id . "',
            action = '" . $this->db->escape($action) . "',
            created_at = NOW()");
    }

    /**
     * البحث في المستندات
     */
    public function searchDocuments($query, $limit = 20) {
        $search = $this->db->escape($query);
        
        $sql = "SELECT ud.*, u.username, u.firstname, u.lastname 
            FROM " . DB_PREFIX . "unified_document ud
            LEFT JOIN " . DB_PREFIX . "user u ON (ud.created_by = u.user_id)
            WHERE (ud.title LIKE '%{$search}%' 
                OR ud.description LIKE '%{$search}%'
                OR ud.tags LIKE '%{$search}%')
            AND (ud.is_public = '1' OR ud.created_by = '" . (int)$this->user->getId() . "')
            ORDER BY ud.created_at DESC
            LIMIT " . (int)$limit;

        $query = $this->db->query($sql);
        return $query->rows;
    }

    /**
     * الحصول على فئات المستندات
     */
    public function getDocumentCategories() {
        $query = $this->db->query("SELECT category, COUNT(*) as count 
            FROM " . DB_PREFIX . "unified_document 
            GROUP BY category 
            ORDER BY count DESC");
        
        return $query->rows;
    }

    /**
     * أرشفة المستندات القديمة
     */
    public function archiveOldDocuments($days = 365) {
        $this->db->query("UPDATE " . DB_PREFIX . "unified_document SET
            status = 'archived'
            WHERE created_at < DATE_SUB(NOW(), INTERVAL " . (int)$days . " DAY)
            AND status = 'active'");
    }

    /**
     * الحصول على إحصائيات المستندات
     */
    public function getDocumentStats() {
        $sql = "SELECT 
            COUNT(*) as total_documents,
            COUNT(CASE WHEN status = 'active' THEN 1 END) as active_documents,
            COUNT(CASE WHEN status = 'draft' THEN 1 END) as draft_documents,
            COUNT(CASE WHEN status = 'archived' THEN 1 END) as archived_documents,
            SUM(file_size) as total_size,
            COUNT(DISTINCT created_by) as unique_creators
            FROM " . DB_PREFIX . "unified_document";
        
        $query = $this->db->query($sql);
        return $query->row;
    }
} 