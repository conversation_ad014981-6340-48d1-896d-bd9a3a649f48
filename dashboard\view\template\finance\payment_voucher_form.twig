{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="submit" form="finance\payment_voucher-form" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fas fa-save"></i></button>
        <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-light"><i class="fas fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    
    <div class="card">
      <div class="card-header"><i class="fas fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}chart-bar{% elif screen_type == 'settings' %}cog{% else %}pencil-alt{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="card-body">
        <form id="finance\payment_voucher-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-add">{{ text_add }}</label>
            <div class="col-sm-10">
              <input type="text" name="add" value="{{ add }}" placeholder="{{ text_add }}" id="input-add" class="form-control" />
              {% if error_add %}
                <div class="invalid-feedback">{{ error_add }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-approve_url">{{ text_approve_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="approve_url" value="{{ approve_url }}" placeholder="{{ text_approve_url }}" id="input-approve_url" class="form-control" />
              {% if error_approve_url %}
                <div class="invalid-feedback">{{ error_approve_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-bank_accounts">{{ text_bank_accounts }}</label>
            <div class="col-sm-10">
              <input type="text" name="bank_accounts" value="{{ bank_accounts }}" placeholder="{{ text_bank_accounts }}" id="input-bank_accounts" class="form-control" />
              {% if error_bank_accounts %}
                <div class="invalid-feedback">{{ error_bank_accounts }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-cash_accounts">{{ text_cash_accounts }}</label>
            <div class="col-sm-10">
              <input type="text" name="cash_accounts" value="{{ cash_accounts }}" placeholder="{{ text_cash_accounts }}" id="input-cash_accounts" class="form-control" />
              {% if error_cash_accounts %}
                <div class="invalid-feedback">{{ error_cash_accounts }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-delete">{{ text_delete }}</label>
            <div class="col-sm-10">
              <input type="text" name="delete" value="{{ delete }}" placeholder="{{ text_delete }}" id="input-delete" class="form-control" />
              {% if error_delete %}
                <div class="invalid-feedback">{{ error_delete }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-employees">{{ text_employees }}</label>
            <div class="col-sm-10">
              <input type="text" name="employees" value="{{ employees }}" placeholder="{{ text_employees }}" id="input-employees" class="form-control" />
              {% if error_employees %}
                <div class="invalid-feedback">{{ error_employees }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_amount">{{ text_error_amount }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_amount" value="{{ error_amount }}" placeholder="{{ text_error_amount }}" id="input-error_amount" class="form-control" />
              {% if error_error_amount %}
                <div class="invalid-feedback">{{ error_error_amount }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_payee_type">{{ text_error_payee_type }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_payee_type" value="{{ error_payee_type }}" placeholder="{{ text_error_payee_type }}" id="input-error_payee_type" class="form-control" />
              {% if error_error_payee_type %}
                <div class="invalid-feedback">{{ error_error_payee_type }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_voucher_date">{{ text_error_voucher_date }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_voucher_date" value="{{ error_voucher_date }}" placeholder="{{ text_error_voucher_date }}" id="input-error_voucher_date" class="form-control" />
              {% if error_error_voucher_date %}
                <div class="invalid-feedback">{{ error_error_voucher_date }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-expense_accounts">{{ text_expense_accounts }}</label>
            <div class="col-sm-10">
              <input type="text" name="expense_accounts" value="{{ expense_accounts }}" placeholder="{{ text_expense_accounts }}" id="input-expense_accounts" class="form-control" />
              {% if error_expense_accounts %}
                <div class="invalid-feedback">{{ error_expense_accounts }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="invalid-feedback">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-order">{{ text_order }}</label>
            <div class="col-sm-10">
              <input type="text" name="order" value="{{ order }}" placeholder="{{ text_order }}" id="input-order" class="form-control" />
              {% if error_order %}
                <div class="invalid-feedback">{{ error_order }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-pagination">{{ text_pagination }}</label>
            <div class="col-sm-10">
              <input type="text" name="pagination" value="{{ pagination }}" placeholder="{{ text_pagination }}" id="input-pagination" class="form-control" />
              {% if error_pagination %}
                <div class="invalid-feedback">{{ error_pagination }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-post_url">{{ text_post_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="post_url" value="{{ post_url }}" placeholder="{{ text_post_url }}" id="input-post_url" class="form-control" />
              {% if error_post_url %}
                <div class="invalid-feedback">{{ error_post_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-results">{{ text_results }}</label>
            <div class="col-sm-10">
              <input type="text" name="results" value="{{ results }}" placeholder="{{ text_results }}" id="input-results" class="form-control" />
              {% if error_results %}
                <div class="invalid-feedback">{{ error_results }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort">{{ text_sort }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort" value="{{ sort }}" placeholder="{{ text_sort }}" id="input-sort" class="form-control" />
              {% if error_sort %}
                <div class="invalid-feedback">{{ error_sort }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_amount">{{ text_sort_amount }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_amount" value="{{ sort_amount }}" placeholder="{{ text_sort_amount }}" id="input-sort_amount" class="form-control" />
              {% if error_sort_amount %}
                <div class="invalid-feedback">{{ error_sort_amount }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_payee">{{ text_sort_payee }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_payee" value="{{ sort_payee }}" placeholder="{{ text_sort_payee }}" id="input-sort_payee" class="form-control" />
              {% if error_sort_payee %}
                <div class="invalid-feedback">{{ error_sort_payee }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_voucher_date">{{ text_sort_voucher_date }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_voucher_date" value="{{ sort_voucher_date }}" placeholder="{{ text_sort_voucher_date }}" id="input-sort_voucher_date" class="form-control" />
              {% if error_sort_voucher_date %}
                <div class="invalid-feedback">{{ error_sort_voucher_date }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_voucher_number">{{ text_sort_voucher_number }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_voucher_number" value="{{ sort_voucher_number }}" placeholder="{{ text_sort_voucher_number }}" id="input-sort_voucher_number" class="form-control" />
              {% if error_sort_voucher_number %}
                <div class="invalid-feedback">{{ error_sort_voucher_number }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-supplier_balance_url">{{ text_supplier_balance_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="supplier_balance_url" value="{{ supplier_balance_url }}" placeholder="{{ text_supplier_balance_url }}" id="input-supplier_balance_url" class="form-control" />
              {% if error_supplier_balance_url %}
                <div class="invalid-feedback">{{ error_supplier_balance_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-supplier_invoices_url">{{ text_supplier_invoices_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="supplier_invoices_url" value="{{ supplier_invoices_url }}" placeholder="{{ text_supplier_invoices_url }}" id="input-supplier_invoices_url" class="form-control" />
              {% if error_supplier_invoices_url %}
                <div class="invalid-feedback">{{ error_supplier_invoices_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-suppliers">{{ text_suppliers }}</label>
            <div class="col-sm-10">
              <input type="text" name="suppliers" value="{{ suppliers }}" placeholder="{{ text_suppliers }}" id="input-suppliers" class="form-control" />
              {% if error_suppliers %}
                <div class="invalid-feedback">{{ error_suppliers }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_form">{{ text_text_form }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_form" value="{{ text_form }}" placeholder="{{ text_text_form }}" id="input-text_form" class="form-control" />
              {% if error_text_form %}
                <div class="invalid-feedback">{{ error_text_form }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="invalid-feedback">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-voucher">{{ text_voucher }}</label>
            <div class="col-sm-10">
              <input type="text" name="voucher" value="{{ voucher }}" placeholder="{{ text_voucher }}" id="input-voucher" class="form-control" />
              {% if error_voucher %}
                <div class="invalid-feedback">{{ error_voucher }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-voucher_date">{{ text_voucher_date }}</label>
            <div class="col-sm-10">
              <input type="text" name="voucher_date" value="{{ voucher_date }}" placeholder="{{ text_voucher_date }}" id="input-voucher_date" class="form-control" />
              {% if error_voucher_date %}
                <div class="invalid-feedback">{{ error_voucher_date }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-vouchers">{{ text_vouchers }}</label>
            <div class="col-sm-10">
              <input type="text" name="vouchers" value="{{ vouchers }}" placeholder="{{ text_vouchers }}" id="input-vouchers" class="form-control" />
              {% if error_vouchers %}
                <div class="invalid-feedback">{{ error_vouchers }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}