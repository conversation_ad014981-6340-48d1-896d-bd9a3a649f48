{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="submit" form="workflow\triggers-form" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fa fa-save"></i></button>
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="close" data-dismiss="alert">&times;</button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="close" data-dismiss="alert">&times;</button>
      </div>
    {{% endif %}}

    <div class="panel panel-default">
      <div class="panel-heading"><i class="fa fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}bar-chart{% elif screen_type == 'settings' %}cog{% else %}pencil{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="panel-body">
        <form id="workflow\triggers-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-action_nodes">{{ text_action_nodes }}</label>
            <div class="col-sm-10">
              <input type="text" name="action_nodes" value="{{ action_nodes }}" placeholder="{{ text_action_nodes }}" id="input-action_nodes" class="form-control" />
              {% if error_action_nodes %}
                <div class="text-danger">{{ error_action_nodes }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-active_triggers">{{ text_active_triggers }}</label>
            <div class="col-sm-10">
              <input type="text" name="active_triggers" value="{{ active_triggers }}" placeholder="{{ text_active_triggers }}" id="input-active_triggers" class="form-control" />
              {% if error_active_triggers %}
                <div class="text-danger">{{ error_active_triggers }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-add">{{ text_add }}</label>
            <div class="col-sm-10">
              <input type="text" name="add" value="{{ add }}" placeholder="{{ text_add }}" id="input-add" class="form-control" />
              {% if error_add %}
                <div class="text-danger">{{ error_add }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-back">{{ text_back }}</label>
            <div class="col-sm-10">
              <input type="text" name="back" value="{{ back }}" placeholder="{{ text_back }}" id="input-back" class="form-control" />
              {% if error_back %}
                <div class="text-danger">{{ error_back }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-editor_config">{{ text_editor_config }}</label>
            <div class="col-sm-10">
              <input type="text" name="editor_config" value="{{ editor_config }}" placeholder="{{ text_editor_config }}" id="input-editor_config" class="form-control" />
              {% if error_editor_config %}
                <div class="text-danger">{{ error_editor_config }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-export_workflow">{{ text_export_workflow }}</label>
            <div class="col-sm-10">
              <input type="text" name="export_workflow" value="{{ export_workflow }}" placeholder="{{ text_export_workflow }}" id="input-export_workflow" class="form-control" />
              {% if error_export_workflow %}
                <div class="text-danger">{{ error_export_workflow }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-get_live_data">{{ text_get_live_data }}</label>
            <div class="col-sm-10">
              <input type="text" name="get_live_data" value="{{ get_live_data }}" placeholder="{{ text_get_live_data }}" id="input-get_live_data" class="form-control" />
              {% if error_get_live_data %}
                <div class="text-danger">{{ error_get_live_data }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="text-danger">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-import_export">{{ text_import_export }}</label>
            <div class="col-sm-10">
              <input type="text" name="import_export" value="{{ import_export }}" placeholder="{{ text_import_export }}" id="input-import_export" class="form-control" />
              {% if error_import_export %}
                <div class="text-danger">{{ error_import_export }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-load_workflow">{{ text_load_workflow }}</label>
            <div class="col-sm-10">
              <input type="text" name="load_workflow" value="{{ load_workflow }}" placeholder="{{ text_load_workflow }}" id="input-load_workflow" class="form-control" />
              {% if error_load_workflow %}
                <div class="text-danger">{{ error_load_workflow }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-logic_nodes">{{ text_logic_nodes }}</label>
            <div class="col-sm-10">
              <input type="text" name="logic_nodes" value="{{ logic_nodes }}" placeholder="{{ text_logic_nodes }}" id="input-logic_nodes" class="form-control" />
              {% if error_logic_nodes %}
                <div class="text-danger">{{ error_logic_nodes }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-monitoring">{{ text_monitoring }}</label>
            <div class="col-sm-10">
              <input type="text" name="monitoring" value="{{ monitoring }}" placeholder="{{ text_monitoring }}" id="input-monitoring" class="form-control" />
              {% if error_monitoring %}
                <div class="text-danger">{{ error_monitoring }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-monitoring_config">{{ text_monitoring_config }}</label>
            <div class="col-sm-10">
              <input type="text" name="monitoring_config" value="{{ monitoring_config }}" placeholder="{{ text_monitoring_config }}" id="input-monitoring_config" class="form-control" />
              {% if error_monitoring_config %}
                <div class="text-danger">{{ error_monitoring_config }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-monitoring_stats">{{ text_monitoring_stats }}</label>
            <div class="col-sm-10">
              <input type="text" name="monitoring_stats" value="{{ monitoring_stats }}" placeholder="{{ text_monitoring_stats }}" id="input-monitoring_stats" class="form-control" />
              {% if error_monitoring_stats %}
                <div class="text-danger">{{ error_monitoring_stats }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-performance_metrics">{{ text_performance_metrics }}</label>
            <div class="col-sm-10">
              <input type="text" name="performance_metrics" value="{{ performance_metrics }}" placeholder="{{ text_performance_metrics }}" id="input-performance_metrics" class="form-control" />
              {% if error_performance_metrics %}
                <div class="text-danger">{{ error_performance_metrics }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-recent_executions">{{ text_recent_executions }}</label>
            <div class="col-sm-10">
              <input type="text" name="recent_executions" value="{{ recent_executions }}" placeholder="{{ text_recent_executions }}" id="input-recent_executions" class="form-control" />
              {% if error_recent_executions %}
                <div class="text-danger">{{ error_recent_executions }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-save_workflow">{{ text_save_workflow }}</label>
            <div class="col-sm-10">
              <input type="text" name="save_workflow" value="{{ save_workflow }}" placeholder="{{ text_save_workflow }}" id="input-save_workflow" class="form-control" />
              {% if error_save_workflow %}
                <div class="text-danger">{{ error_save_workflow }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-settings">{{ text_settings }}</label>
            <div class="col-sm-10">
              <input type="text" name="settings" value="{{ settings }}" placeholder="{{ text_settings }}" id="input-settings" class="form-control" />
              {% if error_settings %}
                <div class="text-danger">{{ error_settings }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-test_trigger">{{ text_test_trigger }}</label>
            <div class="col-sm-10">
              <input type="text" name="test_trigger" value="{{ test_trigger }}" placeholder="{{ text_test_trigger }}" id="input-test_trigger" class="form-control" />
              {% if error_test_trigger %}
                <div class="text-danger">{{ error_test_trigger }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-test_workflow">{{ text_test_workflow }}</label>
            <div class="col-sm-10">
              <input type="text" name="test_workflow" value="{{ test_workflow }}" placeholder="{{ text_test_workflow }}" id="input-test_workflow" class="form-control" />
              {% if error_test_workflow %}
                <div class="text-danger">{{ error_test_workflow }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-total">{{ text_total }}</label>
            <div class="col-sm-10">
              <input type="text" name="total" value="{{ total }}" placeholder="{{ text_total }}" id="input-total" class="form-control" />
              {% if error_total %}
                <div class="text-danger">{{ error_total }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-trigger_nodes">{{ text_trigger_nodes }}</label>
            <div class="col-sm-10">
              <input type="text" name="trigger_nodes" value="{{ trigger_nodes }}" placeholder="{{ text_trigger_nodes }}" id="input-trigger_nodes" class="form-control" />
              {% if error_trigger_nodes %}
                <div class="text-danger">{{ error_trigger_nodes }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-trigger_stats">{{ text_trigger_stats }}</label>
            <div class="col-sm-10">
              <input type="text" name="trigger_stats" value="{{ trigger_stats }}" placeholder="{{ text_trigger_stats }}" id="input-trigger_stats" class="form-control" />
              {% if error_trigger_stats %}
                <div class="text-danger">{{ error_trigger_stats }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-trigger_types">{{ text_trigger_types }}</label>
            <div class="col-sm-10">
              <input type="text" name="trigger_types" value="{{ trigger_types }}" placeholder="{{ text_trigger_types }}" id="input-trigger_types" class="form-control" />
              {% if error_trigger_types %}
                <div class="text-danger">{{ error_trigger_types }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-triggers">{{ text_triggers }}</label>
            <div class="col-sm-10">
              <input type="text" name="triggers" value="{{ triggers }}" placeholder="{{ text_triggers }}" id="input-triggers" class="form-control" />
              {% if error_triggers %}
                <div class="text-danger">{{ error_triggers }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="text-danger">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-visual_editor">{{ text_visual_editor }}</label>
            <div class="col-sm-10">
              <input type="text" name="visual_editor" value="{{ visual_editor }}" placeholder="{{ text_visual_editor }}" id="input-visual_editor" class="form-control" />
              {% if error_visual_editor %}
                <div class="text-danger">{{ error_visual_editor }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-websocket_config">{{ text_websocket_config }}</label>
            <div class="col-sm-10">
              <input type="text" name="websocket_config" value="{{ websocket_config }}" placeholder="{{ text_websocket_config }}" id="input-websocket_config" class="form-control" />
              {% if error_websocket_config %}
                <div class="text-danger">{{ error_websocket_config }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-workflow_templates">{{ text_workflow_templates }}</label>
            <div class="col-sm-10">
              <input type="text" name="workflow_templates" value="{{ workflow_templates }}" placeholder="{{ text_workflow_templates }}" id="input-workflow_templates" class="form-control" />
              {% if error_workflow_templates %}
                <div class="text-danger">{{ error_workflow_templates }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}