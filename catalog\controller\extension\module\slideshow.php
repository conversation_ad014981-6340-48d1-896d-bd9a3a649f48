<?php
class ControllerExtensionModuleSlideshow extends Controller {
	public function index($setting) {
		static $module = 0;		

		$this->load->model('design/banner');
		$this->load->model('tool/image');

		//$this->document->addStyle('catalog/view/javascript/jquery/swiper/css/swiper.min.css');
		//$this->document->addStyle('catalog/view/javascript/jquery/swiper/css/opencart.css');
		//$this->document->addScript('catalog/view/javascript/jquery/swiper/js/swiper.jquery.min.js');
		
		$data['banners'] = array();


		$data['module_id'] = $module;

		$results = $this->model_design_banner->getBanner($setting['banner_id']);

		foreach ($results as $result) {
			if (is_file(DIR_IMAGE . $result['image'])) {
				$data['banners'][] = array(
                    'module_id' => $module,
                    'title' => $result['title'],
					'link'  => $result['link'],
					'image' => $this->model_tool_image->resize($result['image'], $setting['width'], $setting['height'])
				);
			}
		}

		$data['module'] = $module++;

		return $this->load->view('module/slideshow', $data);
	}
}