<?php
/**
 * نموذج دفتر الأستاذ العام الشامل والمتكامل
 * يجمع بين القوة والبساطة - يتفوق على SAP وOracle في سهولة الاستخدام
 * مع الاحتفاظ بالقوة والمرونة المطلوبة للشركات الكبيرة
 * متوافق مع معايير المحاسبة المصرية ومتطلبات ETA
 */
class ModelAccountsGeneralLedger extends Model {

    /**
     * توليد دفتر الأستاذ العام
     */
    public function generateGeneralLedger($filter_data = array()) {
        try {
            $sql = "SELECT 
                        ca.account_id,
                        ca.code,
                        ca.name,
                        ca.type,
                        ca.parent_id,
                        COALESCE(SUM(CASE WHEN je.type = 'debit' THEN je.amount ELSE 0 END), 0) as total_debit,
                        COALESCE(SUM(CASE WHEN je.type = 'credit' THEN je.amount ELSE 0 END), 0) as total_credit,
                        COALESCE(ca.opening_balance, 0) as opening_balance,
                        COUNT(je.entry_id) as transaction_count
                    FROM " . DB_PREFIX . "accounts a
                    LEFT JOIN " . DB_PREFIX . "journal_entries je ON a.account_code = je.account_code";

            $where_conditions = array();
            $where_conditions[] = "a.status = '1'";

            // فلترة حسب الفترة
            if (!empty($filter_data['date_start'])) {
                $where_conditions[] = "je.date_added >= '" . $this->db->escape($filter_data['date_start']) . "'";
            }

            if (!empty($filter_data['date_end'])) {
                $where_conditions[] = "je.date_added <= '" . $this->db->escape($filter_data['date_end']) . "'";
            }

            // فلترة حسب الحساب
            if (!empty($filter_data['account_id'])) {
                $where_conditions[] = "ca.account_id = '" . (int)$filter_data['account_id'] . "'";
            }

            // فلترة حسب نوع الحساب
            if (!empty($filter_data['account_type'])) {
                $where_conditions[] = "ca.type = '" . $this->db->escape($filter_data['account_type']) . "'";
            }

            // فلترة حسب مركز التكلفة
            if (!empty($filter_data['cost_center_id'])) {
                $where_conditions[] = "je.cost_center_id = '" . (int)$filter_data['cost_center_id'] . "'";
            }

            // فلترة حسب الفرع
            if (!empty($filter_data['branch_id'])) {
                $where_conditions[] = "je.branch_id = '" . (int)$filter_data['branch_id'] . "'";
            }

            if (!empty($where_conditions)) {
                $sql .= " WHERE " . implode(" AND ", $where_conditions);
            }

            $sql .= " GROUP BY ca.account_id, ca.code, ca.name, ca.type, ca.parent_id, ca.opening_balance";
            $sql .= " ORDER BY ca.code ASC";

            $query = $this->db->query($sql);

            $general_ledger = array();

            foreach ($query->rows as $row) {
                // حساب الرصيد الختامي
                $closing_balance = $row['opening_balance'];
                
                if (in_array($row['type'], array('asset', 'expense'))) {
                    // الأصول والمصروفات: مدين موجب، دائن سالب
                    $closing_balance += ($row['total_debit'] - $row['total_credit']);
                } else {
                    // الخصوم والإيرادات وحقوق الملكية: دائن موجب، مدين سالب
                    $closing_balance += ($row['total_credit'] - $row['total_debit']);
                }

                $general_ledger[] = array(
                    'account_id' => $row['account_id'],
                    'code' => $row['code'],
                    'name' => $row['name'],
                    'type' => $row['type'],
                    'parent_id' => $row['parent_id'],
                    'opening_balance' => number_format($row['opening_balance'], 2),
                    'total_debit' => number_format($row['total_debit'], 2),
                    'total_credit' => number_format($row['total_credit'], 2),
                    'closing_balance' => number_format($closing_balance, 2),
                    'net_movement' => number_format($row['total_debit'] - $row['total_credit'], 2),
                    'transaction_count' => $row['transaction_count']
                );
            }

            return array(
                'success' => true,
                'data' => $general_ledger,
                'summary' => $this->calculateSummary($general_ledger)
            );

        } catch (Exception $e) {
            return array(
                'success' => false,
                'error' => $e->getMessage()
            );
        }
    }

    /**
     * جلب معاملات حساب محدد
     */
    public function getAccountTransactions($account_id, $filter_data = array()) {
        try {
            $sql = "SELECT 
                        je.entry_id,
                        je.journal_id,
                        je.date_added,
                        je.description,
                        je.type,
                        je.amount,
                        je.reference,
                        j.journal_number,
                        j.description as journal_description
                    FROM " . DB_PREFIX . "journal_entries je
                    LEFT JOIN " . DB_PREFIX . "journals j ON je.journal_id = j.journal_id
                    WHERE je.account_id = '" . (int)$account_id . "'";

            $where_conditions = array();

            // فلترة حسب الفترة
            if (!empty($filter_data['date_start'])) {
                $where_conditions[] = "je.date_added >= '" . $this->db->escape($filter_data['date_start']) . "'";
            }

            if (!empty($filter_data['date_end'])) {
                $where_conditions[] = "je.date_added <= '" . $this->db->escape($filter_data['date_end']) . "'";
            }

            if (!empty($where_conditions)) {
                $sql .= " AND " . implode(" AND ", $where_conditions);
            }

            $sql .= " ORDER BY je.date_added ASC, je.entry_id ASC";

            $query = $this->db->query($sql);

            $transactions = array();
            $running_balance = 0;

            foreach ($query->rows as $row) {
                if ($row['type'] == 'debit') {
                    $running_balance += $row['amount'];
                } else {
                    $running_balance -= $row['amount'];
                }

                $transactions[] = array(
                    'entry_id' => $row['entry_id'],
                    'journal_id' => $row['journal_id'],
                    'journal_number' => $row['journal_number'],
                    'date_added' => $row['date_added'],
                    'description' => $row['description'],
                    'journal_description' => $row['journal_description'],
                    'type' => $row['type'],
                    'debit' => $row['type'] == 'debit' ? number_format($row['amount'], 2) : '0.00',
                    'credit' => $row['type'] == 'credit' ? number_format($row['amount'], 2) : '0.00',
                    'balance' => number_format($running_balance, 2),
                    'reference' => $row['reference']
                );
            }

            return array(
                'success' => true,
                'data' => $transactions
            );

        } catch (Exception $e) {
            return array(
                'success' => false,
                'error' => $e->getMessage()
            );
        }
    }

    /**
     * حساب ملخص دفتر الأستاذ
     */
    private function calculateSummary($general_ledger) {
        $summary = array(
            'total_accounts' => count($general_ledger),
            'total_debit' => 0,
            'total_credit' => 0,
            'total_opening_balance' => 0,
            'total_closing_balance' => 0,
            'accounts_by_type' => array()
        );

        foreach ($general_ledger as $account) {
            $summary['total_debit'] += (float)str_replace(',', '', $account['total_debit']);
            $summary['total_credit'] += (float)str_replace(',', '', $account['total_credit']);
            $summary['total_opening_balance'] += (float)str_replace(',', '', $account['opening_balance']);
            $summary['total_closing_balance'] += (float)str_replace(',', '', $account['closing_balance']);

            if (!isset($summary['accounts_by_type'][$account['type']])) {
                $summary['accounts_by_type'][$account['type']] = 0;
            }
            $summary['accounts_by_type'][$account['type']]++;
        }

        // تنسيق الأرقام
        $summary['total_debit'] = number_format($summary['total_debit'], 2);
        $summary['total_credit'] = number_format($summary['total_credit'], 2);
        $summary['total_opening_balance'] = number_format($summary['total_opening_balance'], 2);
        $summary['total_closing_balance'] = number_format($summary['total_closing_balance'], 2);

        return $summary;
    }

    /**
     * جلب قائمة الحسابات للفلترة
     */
    public function getAccounts() {
        $query = $this->db->query("SELECT account_id, account_code as code, name, account_type as type FROM " . DB_PREFIX . "accounts WHERE status = '1' ORDER BY account_code ASC");
        
        return $query->rows;
    }

    /**
     * جلب قائمة مراكز التكلفة
     */
    public function getCostCenters() {
        $query = $this->db->query("SELECT cost_center_id, name FROM " . DB_PREFIX . "cost_center WHERE status = '1' ORDER BY name ASC");
        
        return $query->rows;
    }

    /**
     * جلب قائمة الفروع
     */
    public function getBranches() {
        $query = $this->db->query("SELECT branch_id, name FROM " . DB_PREFIX . "branch WHERE status = '1' ORDER BY name ASC");
        
        return $query->rows;
    }

    /**
     * تحضير بيانات التصدير
     */
    public function prepareExportData($general_ledger_data, $format = 'excel') {
        $export_data = array();

        // إضافة العناوين
        $headers = array(
            'كود الحساب',
            'اسم الحساب',
            'نوع الحساب',
            'الرصيد الافتتاحي',
            'إجمالي المدين',
            'إجمالي الدائن',
            'الرصيد الختامي',
            'صافي الحركة',
            'عدد المعاملات'
        );

        $export_data[] = $headers;

        // إضافة البيانات
        foreach ($general_ledger_data as $account) {
            $export_data[] = array(
                $account['code'],
                $account['name'],
                $this->getAccountTypeText($account['type']),
                $account['opening_balance'],
                $account['total_debit'],
                $account['total_credit'],
                $account['closing_balance'],
                $account['net_movement'],
                $account['transaction_count']
            );
        }

        return $export_data;
    }

    /**
     * ترجمة نوع الحساب
     */
    private function getAccountTypeText($type) {
        $types = array(
            'asset' => 'أصول',
            'liability' => 'خصوم',
            'equity' => 'حقوق ملكية',
            'revenue' => 'إيرادات',
            'expense' => 'مصروفات'
        );

        return isset($types[$type]) ? $types[$type] : $type;
    }

    /**
     * التحقق من صحة البيانات
     */
    public function validateFilterData($filter_data) {
        $errors = array();

        // التحقق من التواريخ
        if (!empty($filter_data['date_start']) && !empty($filter_data['date_end'])) {
            if (strtotime($filter_data['date_start']) > strtotime($filter_data['date_end'])) {
                $errors[] = 'تاريخ البداية يجب أن يكون قبل تاريخ النهاية';
            }
        }

        // التحقق من وجود الحساب
        if (!empty($filter_data['account_id'])) {
            $query = $this->db->query("SELECT account_id FROM " . DB_PREFIX . "accounts WHERE account_id = '" . (int)$filter_data['account_id'] . "' AND status = '1'");
            if (!$query->num_rows) {
                $errors[] = 'الحساب المحدد غير موجود';
            }
        }

        return $errors;
    }

    /**
     * تحليل مرئي لدفتر الأستاذ العام
     */
    public function getVisualAnalysis($filter_data) {
        // تحليل الحسابات حسب النوع
        $account_types_analysis = $this->db->query("
            SELECT
                ca.type,
                COUNT(*) as account_count,
                SUM(COALESCE(je.amount, 0)) as total_amount,
                AVG(COALESCE(je.amount, 0)) as avg_amount
            FROM " . DB_PREFIX . "chartaccount ca
            LEFT JOIN " . DB_PREFIX . "journal_entry je ON ca.account_id = je.account_id
                AND je.date_added BETWEEN '" . $this->db->escape($filter_data['date_start']) . "'
                                      AND '" . $this->db->escape($filter_data['date_end']) . "'
            WHERE ca.status = '1'
            GROUP BY ca.type
            ORDER BY total_amount DESC
        ");

        // تحليل النشاط الشهري
        $monthly_activity = $this->db->query("
            SELECT
                DATE_FORMAT(je.date_added, '%Y-%m') as month,
                COUNT(*) as transaction_count,
                SUM(CASE WHEN je.type = 'debit' THEN je.amount ELSE 0 END) as total_debits,
                SUM(CASE WHEN je.type = 'credit' THEN je.amount ELSE 0 END) as total_credits
            FROM " . DB_PREFIX . "journal_entry je
            WHERE je.date_added BETWEEN '" . $this->db->escape($filter_data['date_start']) . "'
                                    AND '" . $this->db->escape($filter_data['date_end']) . "'
            GROUP BY DATE_FORMAT(je.date_added, '%Y-%m')
            ORDER BY month ASC
        ");

        // أكثر الحسابات نشاطاً
        $most_active_accounts = $this->db->query("
            SELECT
                ca.code,
                ca.name,
                COUNT(je.entry_id) as transaction_count,
                SUM(je.amount) as total_amount
            FROM " . DB_PREFIX . "chartaccount ca
            INNER JOIN " . DB_PREFIX . "journal_entry je ON ca.account_id = je.account_id
            WHERE je.date_added BETWEEN '" . $this->db->escape($filter_data['date_start']) . "'
                                    AND '" . $this->db->escape($filter_data['date_end']) . "'
            GROUP BY ca.account_id, ca.code, ca.name
            ORDER BY transaction_count DESC
            LIMIT 10
        ");

        // تحليل التوازن
        $balance_analysis = $this->db->query("
            SELECT
                SUM(CASE WHEN je.type = 'debit' THEN je.amount ELSE 0 END) as total_debits,
                SUM(CASE WHEN je.type = 'credit' THEN je.amount ELSE 0 END) as total_credits,
                COUNT(DISTINCT je.account_id) as active_accounts,
                COUNT(*) as total_transactions
            FROM " . DB_PREFIX . "journal_entry je
            WHERE je.date_added BETWEEN '" . $this->db->escape($filter_data['date_start']) . "'
                                    AND '" . $this->db->escape($filter_data['date_end']) . "'
        ");

        return array(
            'account_types' => $account_types_analysis->rows,
            'monthly_activity' => $monthly_activity->rows,
            'most_active_accounts' => $most_active_accounts->rows,
            'balance_summary' => $balance_analysis->row,
            'period' => array(
                'start' => $filter_data['date_start'],
                'end' => $filter_data['date_end']
            )
        );
    }

    /**
     * إحصائيات سريعة لدفتر الأستاذ
     */
    public function getQuickStats($filter_data) {
        $stats = array();

        // إجمالي الحسابات النشطة
        $active_accounts = $this->db->query("
            SELECT COUNT(DISTINCT ca.account_id) as count
            FROM " . DB_PREFIX . "chartaccount ca
            INNER JOIN " . DB_PREFIX . "journal_entry je ON ca.account_id = je.account_id
            WHERE je.date_added BETWEEN '" . $this->db->escape($filter_data['date_start']) . "'
                                    AND '" . $this->db->escape($filter_data['date_end']) . "'
              AND ca.status = '1'
        ");
        $stats['active_accounts'] = $active_accounts->row['count'];

        // إجمالي المعاملات
        $total_transactions = $this->db->query("
            SELECT COUNT(*) as count
            FROM " . DB_PREFIX . "journal_entry je
            WHERE je.date_added BETWEEN '" . $this->db->escape($filter_data['date_start']) . "'
                                    AND '" . $this->db->escape($filter_data['date_end']) . "'
        ");
        $stats['total_transactions'] = $total_transactions->row['count'];

        // متوسط المعاملات اليومية
        $days = (strtotime($filter_data['date_end']) - strtotime($filter_data['date_start'])) / (60 * 60 * 24) + 1;
        $stats['avg_daily_transactions'] = round($stats['total_transactions'] / $days, 2);

        // أكبر معاملة
        $largest_transaction = $this->db->query("
            SELECT MAX(amount) as amount
            FROM " . DB_PREFIX . "journal_entry je
            WHERE je.date_added BETWEEN '" . $this->db->escape($filter_data['date_start']) . "'
                                    AND '" . $this->db->escape($filter_data['date_end']) . "'
        ");
        $stats['largest_transaction'] = $largest_transaction->row['amount'];

        return $stats;
    }

    /**
     * فحص التوازن المحاسبي
     */
    public function checkAccountingBalance($filter_data) {
        $balance_check = $this->db->query("
            SELECT
                SUM(CASE WHEN je.type = 'debit' THEN je.amount ELSE 0 END) as total_debits,
                SUM(CASE WHEN je.type = 'credit' THEN je.amount ELSE 0 END) as total_credits
            FROM " . DB_PREFIX . "journal_entry je
            WHERE je.date_added BETWEEN '" . $this->db->escape($filter_data['date_start']) . "'
                                    AND '" . $this->db->escape($filter_data['date_end']) . "'
        ");

        $total_debits = $balance_check->row['total_debits'];
        $total_credits = $balance_check->row['total_credits'];
        $difference = $total_debits - $total_credits;

        return array(
            'total_debits' => $total_debits,
            'total_credits' => $total_credits,
            'difference' => $difference,
            'is_balanced' => abs($difference) < 0.01, // تسامح صغير للأخطاء العشرية
            'balance_percentage' => $total_debits > 0 ? round(($total_credits / $total_debits) * 100, 2) : 0
        );
    }

    /**
     * تحسينات الأداء والأمان المضافة
     */

    // تحسين دفتر الأستاذ العام مع التخزين المؤقت
    public function getOptimizedGeneralLedger($filter_data = array()) {
        $cache_key = 'general_ledger_' . md5(serialize($filter_data));

        // التحقق من التخزين المؤقت
        $cached_result = $this->cache->get($cache_key);
        if ($cached_result !== false) {
            return $cached_result;
        }

        // إنشاء دفتر الأستاذ العام
        $result = $this->generateGeneralLedger($filter_data);

        // حفظ في التخزين المؤقت لمدة 15 دقيقة
        $this->cache->set($cache_key, $result, 900);

        return $result;
    }

    // تحليل متقدم لدفتر الأستاذ العام
    public function getAdvancedLedgerAnalysis($filter_data = array()) {
        $cache_key = 'ledger_analysis_' . md5(serialize($filter_data));

        // التحقق من التخزين المؤقت
        $cached_result = $this->cache->get($cache_key);
        if ($cached_result !== false) {
            return $cached_result;
        }

        $analysis = array();

        // تحليل الحسابات الأكثر نشاطاً
        $analysis['most_active_accounts'] = $this->getMostActiveAccounts($filter_data);

        // تحليل الاتجاهات الشهرية
        $analysis['monthly_trends'] = $this->getMonthlyTrends($filter_data);

        // تحليل التوزيع حسب نوع الحساب
        $analysis['account_type_distribution'] = $this->getAccountTypeDistribution($filter_data);

        // حفظ في التخزين المؤقت لمدة 30 دقيقة
        $this->cache->set($cache_key, $analysis, 1800);

        return $analysis;
    }

    // التحقق من صحة البيانات
    private function validateLedgerData($filter_data) {
        $errors = array();

        // التحقق من التواريخ
        if (!empty($filter_data['date_start']) && !$this->validateDate($filter_data['date_start'])) {
            $errors[] = 'Invalid start date';
        }

        if (!empty($filter_data['date_end']) && !$this->validateDate($filter_data['date_end'])) {
            $errors[] = 'Invalid end date';
        }

        // التحقق من أن تاريخ البداية قبل تاريخ النهاية
        if (!empty($filter_data['date_start']) && !empty($filter_data['date_end'])) {
            if (strtotime($filter_data['date_start']) > strtotime($filter_data['date_end'])) {
                $errors[] = 'Start date must be before end date';
            }
        }

        // التحقق من معرف الحساب إذا تم تمريره
        if (!empty($filter_data['account_id']) && (!is_numeric($filter_data['account_id']) || $filter_data['account_id'] <= 0)) {
            $errors[] = 'Invalid account ID';
        }

        return $errors;
    }

    // التحقق من صحة التاريخ
    private function validateDate($date) {
        if (empty($date)) return false;

        $d = DateTime::createFromFormat('Y-m-d', $date);
        return $d && $d->format('Y-m-d') === $date;
    }
}
?>
