<?php
/**
 * تحكم الإقرار الضريبي الشامل والمتكامل
 * يجمع بين القوة والبساطة - يتفوق على SAP وOracle في سهولة الاستخدام
 * مع الاحتفاظ بالقوة والمرونة المطلوبة للشركات الكبيرة
 * متوافق مع القانون الضريبي المصري ومتطلبات ETA
 */
class ControllerAccountsTaxReturn extends Controller {
    private $error = array();
    private $central_service;

    public function __construct($registry) {
        parent::__construct($registry);

        // تحميل الخدمات المركزية
        $this->load->model('core/central_service_manager');
        $this->central_service = $this->model_core_central_service_manager;
    }

    public function index() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/tax_return') ||
            !$this->user->hasKey('accounting_tax_return_view')) {

            $this->central_service->logActivity('unauthorized_access', 'accounts',
                'محاولة وصول غير مصرح بها للإقرار الضريبي', [
                'user_id' => $this->user->getId(),
                'ip_address' => $this->request->server['REMOTE_ADDR']
            ]);

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }
        $this->load->language('accounts/tax_return');
        $this->document->setTitle($this->language->get('heading_title'));

        // إضافة CSS و JavaScript المتقدم
        $this->document->addStyle('view/stylesheet/accounts/tax_return.css');
        $this->document->addScript('view/javascript/accounts/tax_return.js');
        $this->document->addScript('view/javascript/jquery/accounting.min.js');
        $this->document->addScript('view/javascript/jquery/select2.min.js');
        $this->document->addStyle('view/javascript/jquery/select2.min.css');
        $this->document->addScript('view/javascript/jquery/daterangepicker.min.js');
        $this->document->addStyle('view/javascript/jquery/daterangepicker.css');
        $this->document->addScript('view/javascript/jquery/chart.min.js');

        // تسجيل الوصول للشاشة
        $this->central_service->logActivity('view', 'accounts',
            'عرض شاشة الإقرار الضريبي', [
            'user_id' => $this->user->getId(),
            'screen' => 'accounts/tax_return'
        ]);

        $data['action'] = $this->url->link('accounts/tax_return/generate', 'user_token=' . $this->session->data['user_token'], true);

        // URLs للدوال المتقدمة
        $data['export_url'] = $this->url->link('accounts/tax_return/export', 'user_token=' . $this->session->data['user_token'], true);
        $data['eta_submit_url'] = $this->url->link('accounts/tax_return/submitToETA', 'user_token=' . $this->session->data['user_token'], true);
        $data['provision_url'] = $this->url->link('accounts/tax_return/calculateProvision', 'user_token=' . $this->session->data['user_token'], true);

        $data['heading_title'] = $this->language->get('heading_title');
        $data['text_form'] = $this->language->get('text_form');
        $data['entry_date_start'] = $this->language->get('entry_date_start');
        $data['entry_date_end'] = $this->language->get('entry_date_end');
        $data['button_filter'] = $this->language->get('button_filter');

        $data['user_token'] = $this->session->data['user_token'];
        $data['error_warning'] = isset($this->error['warning'])?$this->error['warning']:'';

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/tax_return_form', $data));
    }

    /**
     * توليد الإقرار الضريبي المتقدم
     */
    public function generate() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/tax_return') ||
            !$this->user->hasKey('accounting_tax_return_generate')) {

            $this->central_service->logActivity('unauthorized_generate', 'accounts',
                'محاولة توليد إقرار ضريبي غير مصرح بها', [
                'user_id' => $this->user->getId(),
                'action' => 'generate_tax_return'
            ]);

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/tax_return');
        $this->load->model('accounts/tax_return');

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm()) {
            try {
                $filter_data = $this->prepareFilterData();

                // توليد الإقرار المتقدم
                $tax_return_data = $this->model_accounts_tax_return->generateTaxReturn($filter_data);

                // حساب مخصص الضريبة
                $tax_provision = $this->model_accounts_tax_return->calculateTaxProvision($tax_return_data);

                // تسجيل توليد الإقرار
                $this->central_service->logActivity('generate_tax_return', 'accounts',
                    'توليد الإقرار الضريبي للسنة المالية: ' . $filter_data['financial_year'], [
                    'user_id' => $this->user->getId(),
                    'financial_year' => $filter_data['financial_year'],
                    'taxable_profit' => $tax_return_data['taxable_profit'] ?? 0,
                    'tax_due' => $tax_return_data['tax_due'] ?? 0
                ]);

                // إرسال إشعار للمدير المالي
                $this->central_service->sendNotification(
                    'tax_return_generated',
                    'توليد الإقرار الضريبي',
                    'تم توليد الإقرار الضريبي للسنة المالية ' . $filter_data['financial_year'] . ' بواسطة ' . $this->user->getFirstName(),
                    [$this->config->get('config_chief_accountant_id')],
                    [
                        'financial_year' => $filter_data['financial_year'],
                        'user_name' => $this->user->getFirstName() . ' ' . $this->user->getLastName(),
                        'taxable_profit' => $tax_return_data['taxable_profit'] ?? 0,
                        'tax_due' => $tax_return_data['tax_due'] ?? 0
                    ]
                );

                $this->session->data['success'] = $this->language->get('text_success_generate');
                $this->session->data['tax_return_data'] = $tax_return_data;
                $this->session->data['tax_provision'] = $tax_provision;

                // إعادة توجيه حسب الإجراء المطلوب
                if (isset($this->request->post['generate_and_new'])) {
                    $this->response->redirect($this->url->link('accounts/tax_return', 'user_token=' . $this->session->data['user_token'], true));
                } elseif (isset($this->request->post['generate_and_export'])) {
                    $this->response->redirect($this->url->link('accounts/tax_return/export', 'format=excel&user_token=' . $this->session->data['user_token'], true));
                } elseif (isset($this->request->post['generate_and_eta'])) {
                    $this->response->redirect($this->url->link('accounts/tax_return/submitToETA', 'user_token=' . $this->session->data['user_token'], true));
                } else {
                    $this->response->redirect($this->url->link('accounts/tax_return/view', 'user_token=' . $this->session->data['user_token'], true));
                }
            } catch (Exception $e) {
                $this->error['warning'] = $e->getMessage();
            }
        }

        $this->getForm();
    }

    public function print() {
        $this->load->language('accounts/tax_return');
        $this->load->model('accounts/tax_return');

        $data['title'] = $this->language->get('print_title');
        $data['printdate'] = date('Y-m-d H:i:s');
        $data['user_token'] = $this->session->data['user_token'];
        $data['lang'] = $this->language->get('code');
        $data['direction'] = $this->language->get('direction');  
        $data['whoprint'] = $this->user->getUserName();

        $date_start = $this->request->post['date_start'] ?: date('Y-01-01');
        $date_end = $this->request->post['date_end'] ?: date('Y-m-d');

        $data['start_date'] = date($this->language->get('date_format_short'), strtotime($date_start));
        $data['end_date'] = date($this->language->get('date_format_short'), strtotime($date_end));

        if ($date_start && $date_end) {
            $results = $this->model_accounts_tax_return->getTaxReturnData($date_start, $date_end);
            $data['accounting_profit'] = $results['accounting_profit'];
            $data['non_deductible'] = $results['non_deductible'];
            $data['exempt_income'] = $results['exempt_income'];
            $data['taxable_profit'] = $results['taxable_profit'];
            $data['tax_rate'] = $results['tax_rate'];
            $data['tax_due'] = $results['tax_due'];
        } else {
            $data['accounting_profit'] = $this->currency->format(0, $this->config->get('config_currency'));
            $data['non_deductible'] = $this->currency->format(0, $this->config->get('config_currency'));
            $data['exempt_income'] = $this->currency->format(0, $this->config->get('config_currency'));
            $data['taxable_profit'] = $this->currency->format(0, $this->config->get('config_currency'));
            $data['tax_rate'] = 0;
            $data['tax_due'] = $this->currency->format(0, $this->config->get('config_currency'));
            $this->error['warning'] = $this->language->get('error_no_data');
        }

        $data['text_tax_return'] = $this->language->get('text_tax_return');
        $data['text_period'] = $this->language->get('text_period');
        $data['text_from'] = $this->language->get('text_from');
        $data['text_to'] = $this->language->get('text_to');
        $data['text_accounting_profit'] = $this->language->get('text_accounting_profit');
        $data['text_non_deductible'] = $this->language->get('text_non_deductible');
        $data['text_exempt_income'] = $this->language->get('text_exempt_income');
        $data['text_taxable_profit'] = $this->language->get('text_taxable_profit');
        $data['text_tax_rate'] = $this->language->get('text_tax_rate');
        $data['text_tax_due'] = $this->language->get('text_tax_due');

        $this->response->setOutput($this->load->view('accounts/tax_return_list', $data));
    }

    /**
     * حساب مخصص الضريبة
     */
    public function calculateProvision() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/tax_return') ||
            !$this->user->hasKey('accounting_tax_provision')) {

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/tax_return');
        $this->load->model('accounts/tax_return');

        $json = array();

        if (isset($this->request->get['financial_year'])) {
            try {
                $financial_year = $this->request->get['financial_year'];
                $provision_data = $this->model_accounts_tax_return->calculateTaxProvision(['financial_year' => $financial_year]);

                $json['success'] = true;
                $json['provision'] = $provision_data;

                // تسجيل العملية
                $this->central_service->logActivity('calculate_tax_provision', 'accounts',
                    'حساب مخصص الضريبة للسنة المالية ' . $financial_year, [
                    'user_id' => $this->user->getId(),
                    'financial_year' => $financial_year,
                    'provision_amount' => $provision_data['provision_amount'] ?? 0
                ]);

            } catch (Exception $e) {
                $json['error'] = $e->getMessage();
            }
        } else {
            $json['error'] = $this->language->get('error_financial_year_required');
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * إرسال إلى ETA
     */
    public function submitToETA() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('modify', 'accounts/tax_return') ||
            !$this->user->hasKey('accounting_tax_eta_submit')) {

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/tax_return');
        $this->load->model('accounts/tax_return');

        $tax_return_data = $this->session->data['tax_return_data'] ?? array();

        if (empty($tax_return_data)) {
            $this->session->data['error'] = $this->language->get('error_no_data_submit');
            $this->response->redirect($this->url->link('accounts/tax_return', 'user_token=' . $this->session->data['user_token'], true));
            return;
        }

        try {
            // إرسال إلى ETA
            $eta_response = $this->model_accounts_tax_return->submitToETA($tax_return_data);

            // تسجيل الإرسال
            $this->central_service->logActivity('submit_tax_return_eta', 'accounts',
                'إرسال الإقرار الضريبي إلى ETA', [
                'user_id' => $this->user->getId(),
                'financial_year' => $tax_return_data['financial_year'] ?? '',
                'eta_reference' => $eta_response['reference'] ?? ''
            ]);

            $this->session->data['success'] = $this->language->get('text_success_eta_submit');
            $this->response->redirect($this->url->link('accounts/tax_return/view', 'user_token=' . $this->session->data['user_token'], true));

        } catch (Exception $e) {
            $this->session->data['error'] = $e->getMessage();
            $this->response->redirect($this->url->link('accounts/tax_return', 'user_token=' . $this->session->data['user_token'], true));
        }
    }

    /**
     * التحقق من صحة النموذج
     */
    protected function validateForm() {
        if (empty($this->request->post['financial_year'])) {
            $this->error['financial_year'] = $this->language->get('error_financial_year_required');
        }

        if (!empty($this->request->post['date_start']) && !empty($this->request->post['date_end'])) {
            if (strtotime($this->request->post['date_start']) > strtotime($this->request->post['date_end'])) {
                $this->error['date_range'] = $this->language->get('error_date_range');
            }
        }

        return !$this->error;
    }

    /**
     * إعداد بيانات الفلترة
     */
    protected function prepareFilterData() {
        return array(
            'financial_year' => $this->request->post['financial_year'] ?: date('Y'),
            'date_start' => $this->request->post['date_start'] ?: date('Y-01-01'),
            'date_end' => $this->request->post['date_end'] ?: date('Y-12-31'),
            'include_deferred_tax' => isset($this->request->post['include_deferred_tax']) ? 1 : 0,
            'include_carry_forward' => isset($this->request->post['include_carry_forward']) ? 1 : 0,
            'tax_rate' => $this->request->post['tax_rate'] ?? $this->config->get('config_tax_rate'),
            'branch_id' => $this->request->post['branch_id'] ?? ''
        );
    }

    /**
     * عرض النموذج
     */
    protected function getForm() {
        $data['breadcrumbs'] = array();
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('accounts/tax_return', 'user_token=' . $this->session->data['user_token'], true)
        );

        // معدلات الضريبة المصرية
        $data['tax_rates'] = array(
            '22.5' => '22.5% - الشركات العادية',
            '20' => '20% - الشركات الصغيرة',
            '10' => '10% - الشركات الناشئة',
            '0' => '0% - معفاة من الضريبة'
        );

        // السنوات المالية
        $data['financial_years'] = array();
        for ($i = 0; $i < 5; $i++) {
            $year = date('Y') - $i;
            $data['financial_years'][] = array(
                'value' => $year,
                'text' => $year
            );
        }

        $data['user_token'] = $this->session->data['user_token'];
        $data['heading_title'] = $this->language->get('heading_title');
        $data['text_no_results'] = $this->language->get('text_no_results');

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/tax_return_form', $data));
    }

    /**
     * دالة تنظيف المخرجات (CONSTITUTIONAL REQUIREMENT)
     * Sanitize all output data to prevent XSS attacks
     */
    private function sanitizeOutputData($data) {
        if (is_array($data)) {
            foreach ($data as $key => $value) {
                $data[$key] = $this->sanitizeOutputData($value);
            }
        } elseif (is_string($data)) {
            $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
        }
        return $data;
    }

    /**
     * Enterprise Grade Plus Security Enhancements
     */
    private function validateInputData($data) {
        $validated = array();

        if (isset($data['financial_year'])) {
            $validated['financial_year'] = (int)$data['financial_year'];
        }

        if (isset($data['date_start'])) {
            $validated['date_start'] = htmlspecialchars($data['date_start'], ENT_QUOTES, 'UTF-8');
        }

        if (isset($data['date_end'])) {
            $validated['date_end'] = htmlspecialchars($data['date_end'], ENT_QUOTES, 'UTF-8');
        }

        if (isset($data['tax_rate'])) {
            $validated['tax_rate'] = (float)$data['tax_rate'];
        }

        if (isset($data['return_type'])) {
            $validated['return_type'] = htmlspecialchars($data['return_type'], ENT_QUOTES, 'UTF-8');
        }

        return $validated;
    }

    /**
     * Rate Limiting Implementation
     */
    private function checkRateLimit() {
        $ip = $this->request->server['REMOTE_ADDR'];
        $user_id = $this->user->getId();

        return $this->central_service->checkRateLimit('tax_return', $ip, $user_id, 10, 3600); // 10 requests per hour
    }

    /**
     * Performance Optimization - Memory Management
     */
    private function optimizeMemoryUsage() {
        ini_set('memory_limit', '512M');
        ini_set('max_execution_time', 300); // 5 minutes for tax calculations
    }
}
