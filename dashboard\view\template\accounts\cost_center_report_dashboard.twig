{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <a href="{{ generate }}" data-toggle="tooltip" title="{{ button_generate }}" class="btn btn-primary"><i class="fa fa-cogs"></i></a>
        <a href="{{ export_excel }}" data-toggle="tooltip" title="{{ button_export_excel }}" class="btn btn-success"><i class="fa fa-file-excel-o"></i></a>
        <a href="{{ export_pdf }}" data-toggle="tooltip" title="{{ button_export_pdf }}" class="btn btn-danger"><i class="fa fa-file-pdf-o"></i></a>
        <a href="{{ print }}" data-toggle="tooltip" title="{{ button_print }}" class="btn btn-warning" target="_blank"><i class="fa fa-print"></i></a>
        <a href="{{ back }}" data-toggle="tooltip" title="{{ button_back }}" class="btn btn-default"><i class="fa fa-reply"></i></a>
      </div>
      <h1>{{ heading_title }} - {{ text_dashboard }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if success %}
    <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    
    <!-- KPI Cards -->
    <div class="row">
      <div class="col-md-3">
        <div class="info-box bg-blue">
          <span class="info-box-icon"><i class="fa fa-building"></i></span>
          <div class="info-box-content">
            <span class="info-box-text">{{ text_total_cost_centers }}</span>
            <span class="info-box-number">{{ summary.total_cost_centers }}</span>
            <div class="progress">
              <div class="progress-bar" style="width: 100%"></div>
            </div>
            <span class="progress-description">{{ text_active_centers }}</span>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="info-box bg-green">
          <span class="info-box-icon"><i class="fa fa-money"></i></span>
          <div class="info-box-content">
            <span class="info-box-text">{{ text_total_revenues }}</span>
            <span class="info-box-number">{{ summary.total_revenues }}</span>
            <div class="progress">
              <div class="progress-bar" style="width: {{ summary.revenue_achievement }}%"></div>
            </div>
            <span class="progress-description">{{ summary.revenue_achievement }}% {{ text_of_target }}</span>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="info-box bg-yellow">
          <span class="info-box-icon"><i class="fa fa-shopping-cart"></i></span>
          <div class="info-box-content">
            <span class="info-box-text">{{ text_total_expenses }}</span>
            <span class="info-box-number">{{ summary.total_expenses }}</span>
            <div class="progress">
              <div class="progress-bar" style="width: {{ summary.expense_control }}%"></div>
            </div>
            <span class="progress-description">{{ summary.expense_control }}% {{ text_controlled }}</span>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="info-box bg-red">
          <span class="info-box-icon"><i class="fa fa-line-chart"></i></span>
          <div class="info-box-content">
            <span class="info-box-text">{{ text_profit_margin }}</span>
            <span class="info-box-number">{{ summary.overall_profit_margin }}%</span>
            <div class="progress">
              <div class="progress-bar" style="width: {{ summary.overall_profit_margin }}%"></div>
            </div>
            <span class="progress-description">{{ text_overall_performance }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Performance Overview -->
    <div class="row">
      <div class="col-md-8">
        <div class="panel panel-primary">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-bar-chart"></i> {{ text_performance_overview }}</h3>
          </div>
          <div class="panel-body">
            <canvas id="performance-chart" width="400" height="200"></canvas>
          </div>
        </div>
      </div>
      <div class="col-md-4">
        <div class="panel panel-success">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-trophy"></i> {{ text_top_performers }}</h3>
          </div>
          <div class="panel-body">
            <ul class="list-group">
              {% for center in top_performers %}
              <li class="list-group-item">
                <span class="badge bg-green">{{ center.profit_margin }}%</span>
                <strong>{{ center.cost_center_name }}</strong>
                <br><small>{{ center.department }}</small>
              </li>
              {% endfor %}
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- Profitability Analysis -->
    <div class="row">
      <div class="col-md-6">
        <div class="panel panel-info">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-pie-chart"></i> {{ text_profitability_distribution }}</h3>
          </div>
          <div class="panel-body">
            <canvas id="profitability-chart" width="300" height="200"></canvas>
            <div class="row text-center" style="margin-top: 20px;">
              <div class="col-md-4">
                <div class="description-block">
                  <span class="description-percentage text-success">{{ summary.profitable_centers }}</span>
                  <h5 class="description-header">{{ text_profitable }}</h5>
                  <span class="description-text">{{ text_centers }}</span>
                </div>
              </div>
              <div class="col-md-4">
                <div class="description-block">
                  <span class="description-percentage text-warning">{{ summary.break_even_centers }}</span>
                  <h5 class="description-header">{{ text_break_even }}</h5>
                  <span class="description-text">{{ text_centers }}</span>
                </div>
              </div>
              <div class="col-md-4">
                <div class="description-block">
                  <span class="description-percentage text-danger">{{ summary.loss_making_centers }}</span>
                  <h5 class="description-header">{{ text_loss_making }}</h5>
                  <span class="description-text">{{ text_centers }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="panel panel-warning">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-exclamation-triangle"></i> {{ text_attention_required }}</h3>
          </div>
          <div class="panel-body">
            {% if attention_required|length > 0 %}
            <ul class="list-group">
              {% for center in attention_required %}
              <li class="list-group-item">
                <span class="label label-{% if center.severity == 'high' %}danger{% elseif center.severity == 'medium' %}warning{% else %}info{% endif %}">
                  {{ center.severity }}
                </span>
                <strong>{{ center.cost_center_name }}</strong>
                <br><small>{{ center.issue_description }}</small>
                <div class="progress progress-xs">
                  <div class="progress-bar progress-bar-{% if center.severity == 'high' %}danger{% elseif center.severity == 'medium' %}warning{% else %}info{% endif %}" 
                       style="width: {{ center.impact_percentage }}%"></div>
                </div>
              </li>
              {% endfor %}
            </ul>
            {% else %}
            <div class="alert alert-success">
              <i class="fa fa-check-circle"></i> {{ text_all_centers_performing_well }}
            </div>
            {% endif %}
          </div>
        </div>
      </div>
    </div>

    <!-- Trends Analysis -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-line-chart"></i> {{ text_trends_analysis }}</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-md-9">
            <canvas id="trends-chart" width="400" height="200"></canvas>
          </div>
          <div class="col-md-3">
            <h4>{{ text_trend_insights }}</h4>
            <ul class="list-unstyled">
              {% for insight in trend_insights %}
              <li>
                <i class="fa fa-{% if insight.trend == 'up' %}arrow-up text-success{% elseif insight.trend == 'down' %}arrow-down text-danger{% else %}minus text-warning{% endif %}"></i>
                {{ insight.description }}
              </li>
              {% endfor %}
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- Budget vs Actual -->
    <div class="panel panel-primary">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-balance-scale"></i> {{ text_budget_vs_actual }}</h3>
      </div>
      <div class="panel-body">
        <div class="table-responsive">
          <table class="table table-striped" id="budget-comparison-table">
            <thead>
              <tr>
                <th>{{ column_cost_center }}</th>
                <th class="text-right">{{ column_budget_revenue }}</th>
                <th class="text-right">{{ column_actual_revenue }}</th>
                <th class="text-right">{{ column_revenue_variance }}</th>
                <th class="text-right">{{ column_budget_expense }}</th>
                <th class="text-right">{{ column_actual_expense }}</th>
                <th class="text-right">{{ column_expense_variance }}</th>
                <th class="text-center">{{ column_overall_performance }}</th>
              </tr>
            </thead>
            <tbody>
              {% for center in cost_centers %}
              {% if center.budget_comparison %}
              <tr>
                <td>
                  <strong>{{ center.cost_center_name }}</strong>
                  <br><small>{{ center.cost_center_code }}</small>
                </td>
                <td class="text-right">{{ center.budget_comparison.budget_revenues }}</td>
                <td class="text-right">{{ center.budget_comparison.actual_revenues }}</td>
                <td class="text-right {% if center.budget_comparison.revenue_variance >= 0 %}text-success{% else %}text-danger{% endif %}">
                  {{ center.budget_comparison.revenue_variance_percentage }}%
                </td>
                <td class="text-right">{{ center.budget_comparison.budget_expenses }}</td>
                <td class="text-right">{{ center.budget_comparison.actual_expenses }}</td>
                <td class="text-right {% if center.budget_comparison.expense_variance <= 0 %}text-success{% else %}text-danger{% endif %}">
                  {{ center.budget_comparison.expense_variance_percentage }}%
                </td>
                <td class="text-center">
                  <span class="label label-{% if center.performance_rating == 'excellent' %}success{% elseif center.performance_rating == 'good' %}info{% elseif center.performance_rating == 'fair' %}warning{% else %}danger{% endif %}">
                    {{ center.performance_rating }}
                  </span>
                </td>
              </tr>
              {% endif %}
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="panel panel-success">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-flash"></i> {{ text_quick_actions }}</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-md-3">
            <a href="{{ profitability_analysis }}" class="btn btn-block btn-primary">
              <i class="fa fa-line-chart"></i><br>
              {{ text_profitability_analysis }}
            </a>
          </div>
          <div class="col-md-3">
            <a href="{{ variance_analysis }}" class="btn btn-block btn-warning">
              <i class="fa fa-exclamation-triangle"></i><br>
              {{ text_variance_analysis }}
            </a>
          </div>
          <div class="col-md-3">
            <a href="{{ cost_allocation }}" class="btn btn-block btn-info">
              <i class="fa fa-share-alt"></i><br>
              {{ text_cost_allocation }}
            </a>
          </div>
          <div class="col-md-3">
            <a href="{{ detailed_report }}" class="btn btn-block btn-success">
              <i class="fa fa-file-text"></i><br>
              {{ text_detailed_report }}
            </a>
          </div>
        </div>
      </div>
    </div>

  </div>
</div>

<script type="text/javascript"><!--
// Performance Chart
var ctx1 = document.getElementById('performance-chart').getContext('2d');
var performanceChart = new Chart(ctx1, {
    type: 'bar',
    data: {
        labels: [
            {% for center in cost_centers|slice(0, 10) %}
            '{{ center.cost_center_code }}'{% if not loop.last %},{% endif %}
            {% endfor %}
        ],
        datasets: [{
            label: '{{ text_revenues }}',
            data: [
                {% for center in cost_centers|slice(0, 10) %}
                {{ center.total_revenues }}{% if not loop.last %},{% endif %}
                {% endfor %}
            ],
            backgroundColor: 'rgba(54, 162, 235, 0.8)'
        }, {
            label: '{{ text_expenses }}',
            data: [
                {% for center in cost_centers|slice(0, 10) %}
                {{ center.total_expenses }}{% if not loop.last %},{% endif %}
                {% endfor %}
            ],
            backgroundColor: 'rgba(255, 99, 132, 0.8)'
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// Profitability Chart
var ctx2 = document.getElementById('profitability-chart').getContext('2d');
var profitabilityChart = new Chart(ctx2, {
    type: 'doughnut',
    data: {
        labels: ['{{ text_profitable }}', '{{ text_break_even }}', '{{ text_loss_making }}'],
        datasets: [{
            data: [{{ summary.profitable_centers }}, {{ summary.break_even_centers }}, {{ summary.loss_making_centers }}],
            backgroundColor: ['#28a745', '#ffc107', '#dc3545']
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false
    }
});

// Trends Chart
var ctx3 = document.getElementById('trends-chart').getContext('2d');
var trendsChart = new Chart(ctx3, {
    type: 'line',
    data: {
        labels: [
            {% for trend in trends %}
            '{{ trend.period }}'{% if not loop.last %},{% endif %}
            {% endfor %}
        ],
        datasets: [{
            label: '{{ text_total_profit }}',
            data: [
                {% for trend in trends %}
                {{ trend.net_profit }}{% if not loop.last %},{% endif %}
                {% endfor %}
            ],
            borderColor: '#28a745',
            backgroundColor: 'rgba(40, 167, 69, 0.1)',
            fill: true
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// DataTables
$('#budget-comparison-table').DataTable({
    "language": {
        "url": "{{ datatable_language }}"
    },
    "order": [[ 3, "desc" ]],
    "pageLength": 10
});

// Tooltips
$('[data-toggle="tooltip"]').tooltip();

// Auto refresh every 5 minutes
setInterval(function() {
    location.reload();
}, 300000);
//--></script>

{{ footer }}
