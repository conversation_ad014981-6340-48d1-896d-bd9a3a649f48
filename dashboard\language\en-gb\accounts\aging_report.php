<?php
// Heading
$_['heading_title']                    = 'Aging Report';

// Text
$_['text_success']                     = 'Success: Aging Report has been generated successfully!';
$_['text_list']                        = 'Aging Report List';
$_['text_form']                        = 'Aging Report Form';
$_['text_view']                        = 'View Aging Report';
$_['text_generate']                    = 'Generate Aging Report';
$_['text_export']                      = 'Export Aging Report';
$_['text_compare']                     = 'Compare Aging Report';
$_['text_print']                       = 'Print Aging Report';
$_['text_aging_report']                = 'Aging Report';
$_['text_period_end']                  = 'As of';
$_['text_loading']                     = 'Loading...';
$_['text_processing']                  = 'Processing...';
$_['text_success_generate']            = 'Aging Report generated successfully!';
$_['text_success_export']              = 'Aging Report exported successfully!';
$_['text_success_compare']             = 'Comparison completed successfully!';

// Aging Periods
$_['text_0_30_days']                   = '0-30 Days';
$_['text_31_60_days']                  = '31-60 Days';
$_['text_61_90_days']                  = '61-90 Days';
$_['text_over_90_days']                = 'Over 90 Days';
$_['text_0_30']                        = '0-30 Days';
$_['text_31_60']                       = '31-60 Days';
$_['text_61_90']                       = '61-90 Days';
$_['text_over_90']                     = '>90 Days';
$_['text_current']                     = 'Current';
$_['text_overdue']                     = 'Overdue';

// Report Types
$_['text_customers']                   = 'Customers';
$_['text_suppliers']                   = 'Suppliers';
$_['text_customer_aging']              = 'Customer Aging';
$_['text_supplier_aging']              = 'Supplier Aging';
$_['text_receivables']                 = 'Receivables';
$_['text_payables']                    = 'Payables';

// Customer/Supplier Information
$_['text_customer']                    = 'Customer';
$_['text_customer_name']               = 'Customer Name';
$_['text_customer_code']               = 'Customer Code';
$_['text_supplier']                    = 'Supplier';
$_['text_supplier_name']               = 'Supplier Name';
$_['text_supplier_code']               = 'Supplier Code';
$_['text_contact_person']              = 'Contact Person';
$_['text_phone']                       = 'Phone';
$_['text_email']                       = 'Email';
$_['text_credit_limit']                = 'Credit Limit';
$_['text_credit_used']                 = 'Credit Used';
$_['text_credit_available']            = 'Credit Available';

// Amounts and Balances
$_['text_total_balance']               = 'Total Balance';
$_['text_total_overdue']               = 'Total Overdue';
$_['text_total_current']               = 'Total Current';
$_['text_amount']                      = 'Amount';
$_['text_balance']                     = 'Balance';
$_['text_outstanding']                 = 'Outstanding';
$_['text_paid']                        = 'Paid';
$_['text_unpaid']                      = 'Unpaid';

// Column
$_['column_customer']                  = 'Customer';
$_['column_supplier']                  = 'Supplier';
$_['column_total_balance']             = 'Total Balance';
$_['column_current']                   = 'Current';
$_['column_0_30_days']                 = '0-30 Days';
$_['column_31_60_days']                = '31-60 Days';
$_['column_61_90_days']                = '61-90 Days';
$_['column_over_90_days']              = '+90 Days';
$_['column_percentage']                = 'Percentage';
$_['column_risk_level']                = 'Risk Level';

// Entry
$_['entry_date_end']                   = 'Report Date';
$_['entry_report_type']                = 'Report Type';
$_['entry_customer_id']                = 'Customer';
$_['entry_supplier_id']                = 'Supplier';
$_['entry_branch_id']                  = 'Branch';
$_['entry_include_zero_balances']      = 'Include Zero Balances';
$_['entry_aging_periods']              = 'Aging Periods';
$_['entry_export_format']              = 'Export Format';
$_['entry_show_details']               = 'Show Details';
$_['entry_group_by']                   = 'Group By';

// Button
$_['button_generate']                  = 'Generate';
$_['button_export']                    = 'Export';
$_['button_compare']                   = 'Compare';
$_['button_print']                     = 'Print';
$_['button_filter']                    = 'Filter';
$_['button_reset']                     = 'Reset';
$_['button_close']                     = 'Close';
$_['button_send_reminder']             = 'Send Reminder';
$_['button_collection_action']         = 'Collection Action';

// Tab
$_['tab_general']                      = 'General';
$_['tab_filters']                      = 'Filters';
$_['tab_options']                      = 'Options';
$_['tab_summary']                      = 'Summary';
$_['tab_details']                      = 'Details';
$_['tab_analysis']                     = 'Analysis';

// Help
$_['help_date_end']                    = 'Select the aging report date';
$_['help_report_type']                 = 'Select report type (customers or suppliers)';
$_['help_aging_periods']               = 'Define the aging periods required';
$_['help_include_zero']                = 'Include customers/suppliers with zero balance';

// Error
$_['error_warning']                    = 'Warning: Please check the form carefully for errors!';
$_['error_permission']                 = 'Warning: You do not have permission to access Aging Report!';
$_['error_date_end']                   = 'Report date is required!';
$_['error_no_data']                    = 'No data found for selected date!';
$_['error_export']                     = 'Error exporting data!';
$_['error_form']                       = 'Form data error!';
$_['error_missing_data']               = 'Missing data!';

// Export Formats
$_['text_excel']                       = 'Excel';
$_['text_pdf']                         = 'PDF';
$_['text_csv']                         = 'CSV';

// Print Options
$_['text_print_title']                 = 'Aging Report';
$_['print_title']                      = 'Print Aging Report';
$_['text_print_date']                  = 'Print Date';
$_['text_print_user']                  = 'Printed By';

// Status Messages
$_['text_generating']                  = 'Generating aging report...';
$_['text_exporting']                   = 'Exporting data...';
$_['text_comparing']                   = 'Performing comparison...';
$_['text_completed']                   = 'Completed successfully!';

// Risk Analysis
$_['text_risk_analysis']               = 'Risk Analysis';
$_['text_low_risk']                    = 'Low Risk';
$_['text_medium_risk']                 = 'Medium Risk';
$_['text_high_risk']                   = 'High Risk';
$_['text_critical_risk']               = 'Critical Risk';
$_['text_risk_score']                  = 'Risk Score';
$_['text_collection_priority']         = 'Collection Priority';

// Summary Information
$_['text_buckets']                     = 'Aging Buckets Summary';
$_['text_customer_details']            = 'Detailed by Customer';
$_['text_supplier_details']            = 'Detailed by Supplier';
$_['text_total_customers']             = 'Total Customers';
$_['text_total_suppliers']             = 'Total Suppliers';
$_['text_overdue_percentage']          = 'Overdue Percentage';
$_['text_collection_efficiency']      = 'Collection Efficiency';

// Collection Actions
$_['text_send_statement']              = 'Send Statement';

// Missing variables from audit report - Critical fixes
$_['accounts/aging_report']            = '';
$_['code']                             = 'Code';
$_['date_format_short']                = 'm/d/Y';
$_['direction']                        = 'ltr';
$_['lang']                             = 'en';

// Controller language variables
$_['log_unauthorized_access_aging_report'] = 'Unauthorized access attempt to aging report';
$_['log_view_aging_report_screen']     = 'View aging report screen';
$_['log_unauthorized_generate_aging_report'] = 'Unauthorized aging report generation attempt';
$_['log_generate_aging_report_date']   = 'Generate aging report for date';
$_['text_high_risk_customers_alert']   = 'Warning: High Risk Customers';
$_['text_high_risk_customers_detected'] = 'Detected';
$_['text_high_risk_customer_in_aging_report'] = 'high risk customer in aging report';
$_['text_aging_report_generated']      = 'Aging Report Generated';
$_['text_aging_report_generated_notification'] = 'Aging report generated for date';
$_['log_view_aging_report']            = 'View aging report';
$_['log_unauthorized_export_aging_report'] = 'Unauthorized aging report export attempt';
$_['log_export_aging_report']          = 'Export aging report';
$_['text_aging_report_exported']       = 'Aging Report Exported';
$_['text_aging_report_exported_notification'] = 'Aging report exported in format';

// Enterprise Grade Plus template variables
$_['text_actions']                     = 'Actions';
$_['text_generate_tooltip']            = 'Generate aging report for selected date';
$_['text_export_tooltip']              = 'Export aging report in various formats';
$_['text_print']                       = 'Print';
$_['text_generating']                  = 'Generating...';
$_['text_exporting']                   = 'Exporting...';
$_['error_generate']                   = 'Error generating aging report';
$_['text_filter']                      = 'Filter';
$_['entry_date_end']                   = 'End Date';
$_['entry_report_type']                = 'Report Type';
$_['entry_branch']                     = 'Branch';
$_['text_customers']                   = 'Customers';
$_['text_suppliers']                   = 'Suppliers';
$_['text_all_branches']                = 'All Branches';
$_['button_filter']                    = 'Filter';
$_['text_as_of']                       = 'As of';
$_['text_report_type']                 = 'Report Type';
$_['text_current_amount']              = 'Current Amount';
$_['text_overdue_amount']              = 'Overdue Amount';
$_['text_total_amount']                = 'Total Amount';
$_['text_customer_supplier']           = 'Customer/Supplier';
$_['text_contact_info']                = 'Contact Info';
$_['text_current']                     = 'Current';
$_['text_days']                        = 'Days';
$_['text_risk_level']                  = 'Risk Level';
$_['text_high_risk_alert']             = 'High Risk Alert';
$_['text_high_risk_customers_found']   = 'High risk customers found';
$_['text_total_risk_amount']           = 'Total Risk Amount';
$_['text_view_risk_details']           = 'View Risk Details';
$_['text_no_data']                     = 'No data to display. Please select date and click generate.';
$_['error_date_required']              = 'End date is required';
$_['warning_future_date']              = 'Warning: Selected date is in the future';
$_['text_success_generation']          = 'Aging report generated successfully';
$_['text_no_high_risk_customers']      = 'No high risk customers found';
$_['text_send_reminder']               = 'Send Reminder';
$_['text_schedule_call']               = 'Schedule Call';
$_['text_legal_action']                = 'Legal Action';
$_['text_write_off']                   = 'Write Off';

// Egyptian Legal Compliance
$_['text_egyptian_law_compliant']      = 'Compliant with Egyptian Laws';
$_['text_eas_compliant']               = 'Compliant with Egyptian Accounting Standards';
$_['text_eta_ready']                   = 'Ready for ETA Integration';
$_['text_egyptian_gaap']               = 'According to Egyptian Generally Accepted Accounting Principles';

// Additional Fields
$_['text_total']                       = 'Total';
$_['text_no_results']                  = 'No results found';

// Enhanced performance and analytics variables
$_['text_optimized_aging']             = 'Optimized Aging Report';
$_['text_aging_trends']                = 'Aging Trends';
$_['text_high_risk_customers']         = 'High Risk Customers';
$_['text_customer_performance']        = 'Customer Performance';
$_['text_financial_performance']       = 'Financial Performance';
$_['text_cache_enabled']               = 'Cache Enabled';
$_['text_report_cached']               = 'Report Cached';
$_['text_trend_analysis']              = 'Trend Analysis';
$_['text_risk_threshold']              = 'Risk Threshold';
$_['text_overdue_orders']              = 'Overdue Orders';
$_['text_avg_days_overdue']            = 'Average Days Overdue';
$_['text_max_days_overdue']            = 'Maximum Days Overdue';
$_['text_paid_amount']                 = 'Paid Amount';
$_['text_outstanding_amount']          = 'Outstanding Amount';
$_['button_trend_analysis']            = 'Trend Analysis';
$_['button_risk_analysis']             = 'Risk Analysis';
$_['text_loading_trends']              = 'Loading trends...';
$_['text_trends_ready']                = 'Trends ready';
