<?php return array(
    'root' => array(
        'name' => 'opencart/opencart',
        'pretty_version' => '3.0.x-dev',
        'version' => '3.0.9999999.9999999-dev',
        'reference' => '2dab8defa6e9f574813d8c82566add2575949d0f',
        'type' => 'project',
        'install_path' => __DIR__ . '/../../../../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        'braintree/braintree_php' => array(
            'pretty_version' => '3.40.0',
            'version' => '3.40.0.0',
            'reference' => '840fc6ebf8d96756fed475cce94565fef178187d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../braintree/braintree_php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'cardinity/cardinity-sdk-php' => array(
            'pretty_version' => 'v2.1.0',
            'version' => '2.1.0.0',
            'reference' => '602f6c40f81d4dfae0e1e9f327736c2c2e31a82b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../cardinity/cardinity-sdk-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'divido/divido-php' => array(
            'pretty_version' => 'v1.15-stable',
            'version' => '1.15.0.0',
            'reference' => '8edd902ec2be8151331985021107031292b41ca1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../divido/divido-php',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/guzzle' => array(
            'pretty_version' => '6.5.8',
            'version' => '6.5.8.0',
            'reference' => 'a52f0440530b54fa079ce76e8c5d196a42cad981',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/guzzle',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/oauth-subscriber' => array(
            'pretty_version' => '0.3.0',
            'version' => '0.3.0.0',
            'reference' => '04960cdef3cd80ea401d6b0ca8b3e110e9bf12cf',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/oauth-subscriber',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/promises' => array(
            'pretty_version' => '1.5.3',
            'version' => '1.5.3.0',
            'reference' => '67ab6e18aaa14d753cc148911d273f6e6cb6721e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/promises',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/psr7' => array(
            'pretty_version' => '1.9.1',
            'version' => '1.9.1.0',
            'reference' => 'e4490cabc77465aaee90b20cfc9a770f8c04be6b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/psr7',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'opencart/opencart' => array(
            'pretty_version' => '3.0.x-dev',
            'version' => '3.0.9999999.9999999-dev',
            'reference' => '2dab8defa6e9f574813d8c82566add2575949d0f',
            'type' => 'project',
            'install_path' => __DIR__ . '/../../../../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-message' => array(
            'pretty_version' => '1.1',
            'version' => '1.1.0.0',
            'reference' => 'cb6ce4845ce34a8ad9e68117c10ee90a29919eba',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-message',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-message-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'ralouphie/getallheaders' => array(
            'pretty_version' => '3.0.3',
            'version' => '3.0.3.0',
            'reference' => '120b605dfeb996808c31b6477290a714d356e822',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ralouphie/getallheaders',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'scssphp/scssphp' => array(
            'pretty_version' => 'v1.12.1',
            'version' => '1.12.1.0',
            'reference' => '394ed1e960138710a60d035c1a85d43d0bf0faeb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../scssphp/scssphp',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-ctype' => array(
            'pretty_version' => 'v1.29.0',
            'version' => '1.29.0.0',
            'reference' => 'ef4d7e442ca910c4764bce785146269b30cb5fc4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-ctype',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-idn' => array(
            'pretty_version' => 'v1.29.0',
            'version' => '1.29.0.0',
            'reference' => 'a287ed7475f85bf6f61890146edbc932c0fff919',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-idn',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-normalizer' => array(
            'pretty_version' => 'v1.29.0',
            'version' => '1.29.0.0',
            'reference' => 'bc45c394692b948b4d383a08d7753968bed9a83d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-normalizer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-mbstring' => array(
            'pretty_version' => 'v1.29.0',
            'version' => '1.29.0.0',
            'reference' => '9773676c8a1bb1f8d4340a62efe641cf76eda7ec',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-mbstring',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php72' => array(
            'pretty_version' => 'v1.29.0',
            'version' => '1.29.0.0',
            'reference' => '861391a8da9a04cbad2d232ddd9e4893220d6e25',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php72',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php80' => array(
            'pretty_version' => 'v1.29.0',
            'version' => '1.29.0.0',
            'reference' => '87b68208d5c1188808dd7839ee1e6c8ec3b02f1b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php80',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/translation-contracts' => array(
            'pretty_version' => 'v2.5.2',
            'version' => '2.5.2.0',
            'reference' => '136b19dd05cdf0709db6537d058bcab6dd6e2dbe',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/translation-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/validator' => array(
            'pretty_version' => 'v4.4.48',
            'version' => '4.4.48.0',
            'reference' => '54781a4c41efbd283b779110bf8ae7f263737775',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/validator',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'twig/twig' => array(
            'pretty_version' => 'v3.8.0',
            'version' => '3.8.0.0',
            'reference' => '9d15f0ac07f44dc4217883ec6ae02fd555c6f71d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../twig/twig',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'zoujingli/wechat-developer' => array(
            'pretty_version' => 'v1.2.54',
            'version' => '1.2.54.0',
            'reference' => '812aae37ffc5b6038b03163796f6eed97bb79730',
            'type' => 'library',
            'install_path' => __DIR__ . '/../zoujingli/wechat-developer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'zoujingli/wechat-php-sdk' => array(
            'pretty_version' => 'v1.3.18',
            'version' => '1.3.18.0',
            'reference' => 'd37d0c1919ede2ee54e65100ac3792e947b1e0ef',
            'type' => 'project',
            'install_path' => __DIR__ . '/../zoujingli/wechat-php-sdk',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
