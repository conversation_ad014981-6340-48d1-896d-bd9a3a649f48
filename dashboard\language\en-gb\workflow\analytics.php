<?php
/**
 * English Language File - Workflow Analytics
 * 
 * @package    AYM ERP
 * <AUTHOR> Development Team
 * @copyright  2025 AYM ERP Systems
 * @license    Commercial License
 * @version    1.0.0
 * @since      2025-07-15
 */

// Main heading
$_['heading_title'] = 'Workflow Analytics';

// General texts
$_['text_success'] = 'Success: Analytics generated successfully!';
$_['text_analytics'] = 'Analytics';
$_['text_insights'] = 'Insights';
$_['text_intelligence'] = 'Intelligence';
$_['text_dashboard'] = 'Analytics Dashboard';
$_['text_reports'] = 'Analytical Reports';
$_['text_metrics'] = 'Metrics';
$_['text_kpis'] = 'Key Performance Indicators';
$_['text_loading'] = 'Analyzing...';
$_['text_no_data'] = 'No data to analyze!';

// Analysis types
$_['text_analysis_types'] = 'Analysis Types';
$_['text_descriptive_analytics'] = 'Descriptive Analytics';
$_['text_diagnostic_analytics'] = 'Diagnostic Analytics';
$_['text_predictive_analytics'] = 'Predictive Analytics';
$_['text_prescriptive_analytics'] = 'Prescriptive Analytics';
$_['text_real_time_analytics'] = 'Real-time Analytics';
$_['text_historical_analytics'] = 'Historical Analytics';

// Performance metrics
$_['text_performance_metrics'] = 'Performance Metrics';
$_['text_cycle_time'] = 'Cycle Time';
$_['text_lead_time'] = 'Lead Time';
$_['text_throughput'] = 'Throughput';
$_['text_efficiency'] = 'Efficiency';
$_['text_utilization'] = 'Utilization';
$_['text_quality'] = 'Quality';
$_['text_cost_per_transaction'] = 'Cost per Transaction';
$_['text_resource_utilization'] = 'Resource Utilization';

// Key performance indicators
$_['text_key_performance_indicators'] = 'Key Performance Indicators';
$_['text_completion_rate'] = 'Completion Rate';
$_['text_on_time_delivery'] = 'On-time Delivery';
$_['text_first_pass_yield'] = 'First Pass Yield';
$_['text_rework_rate'] = 'Rework Rate';
$_['text_escalation_rate'] = 'Escalation Rate';
$_['text_customer_satisfaction'] = 'Customer Satisfaction';
$_['text_employee_productivity'] = 'Employee Productivity';
$_['text_cost_savings'] = 'Cost Savings';

// Time analysis
$_['text_time_analysis'] = 'Time Analysis';
$_['text_processing_time'] = 'Processing Time';
$_['text_waiting_time'] = 'Waiting Time';
$_['text_idle_time'] = 'Idle Time';
$_['text_overtime'] = 'Overtime';
$_['text_peak_hours'] = 'Peak Hours';
$_['text_off_peak_hours'] = 'Off-peak Hours';
$_['text_seasonal_patterns'] = 'Seasonal Patterns';

// Volume analysis
$_['text_volume_analysis'] = 'Volume Analysis';
$_['text_transaction_volume'] = 'Transaction Volume';
$_['text_workload_distribution'] = 'Workload Distribution';
$_['text_capacity_analysis'] = 'Capacity Analysis';
$_['text_demand_forecasting'] = 'Demand Forecasting';
$_['text_resource_planning'] = 'Resource Planning';

// Quality analysis
$_['text_quality_analysis'] = 'Quality Analysis';
$_['text_error_rate'] = 'Error Rate';
$_['text_defect_rate'] = 'Defect Rate';
$_['text_accuracy'] = 'Accuracy';
$_['text_compliance_rate'] = 'Compliance Rate';
$_['text_audit_results'] = 'Audit Results';
$_['text_quality_trends'] = 'Quality Trends';

// Cost analysis
$_['text_cost_analysis'] = 'Cost Analysis';
$_['text_operational_cost'] = 'Operational Cost';
$_['text_labor_cost'] = 'Labor Cost';
$_['text_technology_cost'] = 'Technology Cost';
$_['text_overhead_cost'] = 'Overhead Cost';
$_['text_cost_per_unit'] = 'Cost per Unit';
$_['text_roi'] = 'Return on Investment';

// Bottleneck analysis
$_['text_bottleneck_analysis'] = 'Bottleneck Analysis';
$_['text_process_bottlenecks'] = 'Process Bottlenecks';
$_['text_resource_bottlenecks'] = 'Resource Bottlenecks';
$_['text_system_bottlenecks'] = 'System Bottlenecks';
$_['text_capacity_constraints'] = 'Capacity Constraints';
$_['text_optimization_opportunities'] = 'Optimization Opportunities';

// Predictive analytics
$_['text_predictive_modeling'] = 'Predictive Modeling';
$_['text_trend_forecasting'] = 'Trend Forecasting';
$_['text_demand_prediction'] = 'Demand Prediction';
$_['text_risk_prediction'] = 'Risk Prediction';
$_['text_performance_prediction'] = 'Performance Prediction';
$_['text_machine_learning'] = 'Machine Learning';
$_['text_ai_insights'] = 'AI Insights';

// Comparative analysis
$_['text_comparative_analysis'] = 'Comparative Analysis';
$_['text_benchmark_analysis'] = 'Benchmark Analysis';
$_['text_before_after_comparison'] = 'Before/After Comparison';
$_['text_department_comparison'] = 'Department Comparison';
$_['text_period_comparison'] = 'Period Comparison';
$_['text_best_practices'] = 'Best Practices';

// Visualizations
$_['text_visualizations'] = 'Data Visualizations';
$_['text_charts'] = 'Charts';
$_['text_graphs'] = 'Graphs';
$_['text_dashboards'] = 'Dashboards';
$_['text_heat_maps'] = 'Heat Maps';
$_['text_scatter_plots'] = 'Scatter Plots';
$_['text_trend_lines'] = 'Trend Lines';
$_['text_pie_charts'] = 'Pie Charts';
$_['text_bar_charts'] = 'Bar Charts';
$_['text_line_charts'] = 'Line Charts';

// Analytical reports
$_['text_analytical_reports'] = 'Analytical Reports';
$_['text_executive_summary'] = 'Executive Summary';
$_['text_detailed_analysis'] = 'Detailed Analysis';
$_['text_trend_report'] = 'Trend Report';
$_['text_performance_report'] = 'Performance Report';
$_['text_efficiency_report'] = 'Efficiency Report';
$_['text_quality_report'] = 'Quality Report';
$_['text_cost_report'] = 'Cost Report';

// Filters and grouping
$_['text_filters'] = 'Filters';
$_['text_grouping'] = 'Grouping';
$_['text_drill_down'] = 'Drill Down';
$_['text_drill_up'] = 'Drill Up';
$_['text_slice_and_dice'] = 'Slice and Dice';
$_['text_pivot_analysis'] = 'Pivot Analysis';

// Time periods
$_['text_time_periods'] = 'Time Periods';
$_['text_hourly'] = 'Hourly';
$_['text_daily'] = 'Daily';
$_['text_weekly'] = 'Weekly';
$_['text_monthly'] = 'Monthly';
$_['text_quarterly'] = 'Quarterly';
$_['text_yearly'] = 'Yearly';
$_['text_custom_period'] = 'Custom Period';

// Statistical analysis
$_['text_statistical_analysis'] = 'Statistical Analysis';
$_['text_mean'] = 'Mean';
$_['text_median'] = 'Median';
$_['text_mode'] = 'Mode';
$_['text_standard_deviation'] = 'Standard Deviation';
$_['text_variance'] = 'Variance';
$_['text_correlation'] = 'Correlation';
$_['text_regression'] = 'Regression';
$_['text_confidence_interval'] = 'Confidence Interval';

// Anomaly detection
$_['text_anomaly_detection'] = 'Anomaly Detection';
$_['text_outliers'] = 'Outliers';
$_['text_unusual_patterns'] = 'Unusual Patterns';
$_['text_deviation_analysis'] = 'Deviation Analysis';
$_['text_threshold_violations'] = 'Threshold Violations';

// Optimization and recommendations
$_['text_optimization'] = 'Optimization';
$_['text_recommendations'] = 'Recommendations';
$_['text_improvement_opportunities'] = 'Improvement Opportunities';
$_['text_action_items'] = 'Action Items';
$_['text_priority_areas'] = 'Priority Areas';
$_['text_quick_wins'] = 'Quick Wins';

// Export and sharing
$_['text_export'] = 'Export';
$_['text_export_data'] = 'Export Data';
$_['text_export_report'] = 'Export Report';
$_['text_export_chart'] = 'Export Chart';
$_['text_share_analysis'] = 'Share Analysis';
$_['text_schedule_report'] = 'Schedule Report';

// Columns and fields
$_['column_metric'] = 'Metric';
$_['column_value'] = 'Value';
$_['column_trend'] = 'Trend';
$_['column_target'] = 'Target';
$_['column_variance'] = 'Variance';
$_['column_percentage'] = 'Percentage';
$_['column_rank'] = 'Rank';

// Buttons and actions
$_['button_analyze'] = 'Analyze';
$_['button_refresh'] = 'Refresh';
$_['button_drill_down'] = 'Drill Down';
$_['button_export_analysis'] = 'Export Analysis';
$_['button_save_view'] = 'Save View';
$_['button_share'] = 'Share';
$_['button_schedule'] = 'Schedule';

// Advanced analytics
$_['text_advanced_analytics'] = 'Advanced Analytics';
$_['text_big_data_analytics'] = 'Big Data Analytics';
$_['text_real_time_streaming'] = 'Real-time Streaming';
$_['text_data_mining'] = 'Data Mining';
$_['text_pattern_recognition'] = 'Pattern Recognition';
$_['text_sentiment_analysis'] = 'Sentiment Analysis';

// Integrations
$_['text_integrations'] = 'Integrations';
$_['text_data_sources'] = 'Data Sources';
$_['text_external_data'] = 'External Data';
$_['text_api_integration'] = 'API Integration';
$_['text_database_connection'] = 'Database Connection';

// Error messages
$_['error_permission'] = 'Warning: You do not have permission to access workflow analytics!';
$_['error_insufficient_data'] = 'Insufficient data for analysis!';
$_['error_invalid_parameters'] = 'Invalid analysis parameters!';
$_['error_analysis_failed'] = 'Analysis failed!';
$_['error_export_failed'] = 'Analysis export failed!';

// Help and tips
$_['help_analytics'] = 'Use analytics to understand workflow performance';
$_['help_kpis'] = 'Monitor key performance indicators regularly';
$_['help_trends'] = 'Track trends to identify improvement opportunities';
$_['help_benchmarks'] = 'Compare performance against benchmarks';

// Alerts
$_['alert_analysis_complete'] = 'Analysis completed successfully';
$_['alert_trend_detected'] = 'New trend detected';
$_['alert_anomaly_found'] = 'Anomaly found in data';
$_['alert_threshold_exceeded'] = 'Threshold exceeded';
$_['alert_improvement_opportunity'] = 'Improvement opportunity identified';

// Dates and times
$_['text_analysis_date'] = 'Analysis Date';
$_['text_data_freshness'] = 'Data Freshness';
$_['text_last_updated'] = 'Last Updated';
$_['text_analysis_period'] = 'Analysis Period';
$_['text_forecast_horizon'] = 'Forecast Horizon';

// Data quality
$_['text_data_quality'] = 'Data Quality';
$_['text_completeness'] = 'Completeness';
$_['text_accuracy'] = 'Accuracy';
$_['text_consistency'] = 'Consistency';
$_['text_timeliness'] = 'Timeliness';
$_['text_validity'] = 'Validity';

// Machine learning
$_['text_ml_models'] = 'ML Models';
$_['text_model_training'] = 'Model Training';
$_['text_model_validation'] = 'Model Validation';
$_['text_prediction_accuracy'] = 'Prediction Accuracy';
$_['text_feature_importance'] = 'Feature Importance';

// Business intelligence
$_['text_business_intelligence'] = 'Business Intelligence';
$_['text_data_warehouse'] = 'Data Warehouse';
$_['text_olap'] = 'OLAP';
$_['text_data_mart'] = 'Data Mart';
$_['text_etl'] = 'ETL';
?>
