{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <a href="{{ edit }}" data-toggle="tooltip" title="{{ button_edit }}" class="btn btn-primary">
          <i class="fa fa-pencil"></i>
        </a>
        <a href="{{ copy }}" data-toggle="tooltip" title="{{ button_copy }}" class="btn btn-success">
          <i class="fa fa-copy"></i>
        </a>
        <a href="{{ back }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default">
          <i class="fa fa-reply"></i>
        </a>
      </div>
      <h1>{{ heading_title }} - {{ unit_info.name }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  
  <div class="container-fluid">
    <div class="row">
      <!-- معلومات الوحدة الأساسية -->
      <div class="col-md-6">
        <div class="panel panel-primary">
          <div class="panel-heading">
            <h3 class="panel-title">
              <i class="fa fa-info-circle"></i> معلومات الوحدة الأساسية
            </h3>
          </div>
          <div class="panel-body">
            <div class="row">
              <div class="col-md-6">
                <div class="text-center">
                  <div class="unit-icon">
                    <i class="fa fa-balance-scale fa-4x text-primary"></i>
                  </div>
                  <h3>{{ unit_info.name }}</h3>
                  <h4><span class="label label-info label-lg">{{ unit_info.symbol }}</span></h4>
                </div>
              </div>
              <div class="col-md-6">
                <table class="table table-condensed">
                  <tr>
                    <td><strong>نوع الوحدة:</strong></td>
                    <td><span class="label label-primary">{{ unit_info.unit_type }}</span></td>
                  </tr>
                  <tr>
                    <td><strong>وحدة أساسية:</strong></td>
                    <td>
                      {% if unit_info.is_base_unit %}
                      <span class="label label-success"><i class="fa fa-star"></i> نعم</span>
                      {% else %}
                      <span class="label label-default">لا</span>
                      {% endif %}
                    </td>
                  </tr>
                  <tr>
                    <td><strong>معامل التحويل:</strong></td>
                    <td><span class="badge badge-warning">{{ unit_info.conversion_factor }}</span></td>
                  </tr>
                  <tr>
                    <td><strong>الحالة:</strong></td>
                    <td>
                      {% if unit_info.is_active %}
                      <span class="label label-success">مفعل</span>
                      {% else %}
                      <span class="label label-danger">معطل</span>
                      {% endif %}
                    </td>
                  </tr>
                  <tr>
                    <td><strong>ترتيب العرض:</strong></td>
                    <td>{{ unit_info.sort_order }}</td>
                  </tr>
                </table>
              </div>
            </div>
            
            {% if unit_info.description %}
            <div class="row">
              <div class="col-md-12">
                <hr>
                <h5><strong>الوصف:</strong></h5>
                <p class="text-muted">{{ unit_info.description }}</p>
              </div>
            </div>
            {% endif %}
            
            {% if unit_info.base_unit_name %}
            <div class="row">
              <div class="col-md-12">
                <hr>
                <h5><strong>الوحدة الأساسية:</strong></h5>
                <p>{{ unit_info.base_unit_name }} ({{ unit_info.base_unit_symbol }})</p>
              </div>
            </div>
            {% endif %}
          </div>
        </div>
      </div>
      
      <!-- إحصائيات الاستخدام -->
      <div class="col-md-6">
        <div class="panel panel-success">
          <div class="panel-heading">
            <h3 class="panel-title">
              <i class="fa fa-bar-chart"></i> إحصائيات الاستخدام
            </h3>
          </div>
          <div class="panel-body">
            <div class="row">
              <div class="col-md-6">
                <div class="stat-box text-center">
                  <i class="fa fa-cubes fa-2x text-primary"></i>
                  <h4>{{ unit_info.products_count ?: 0 }}</h4>
                  <p>المنتجات المرتبطة</p>
                </div>
              </div>
              <div class="col-md-6">
                <div class="stat-box text-center">
                  <i class="fa fa-qrcode fa-2x text-info"></i>
                  <h4>{{ unit_info.barcodes_count ?: 0 }}</h4>
                  <p>الباركود المرتبط</p>
                </div>
              </div>
            </div>
            
            <div class="row">
              <div class="col-md-6">
                <div class="stat-box text-center">
                  <i class="fa fa-exchange fa-2x text-warning"></i>
                  <h4>{{ unit_info.movements_30_days ?: 0 }}</h4>
                  <p>حركات آخر 30 يوم</p>
                </div>
              </div>
              <div class="col-md-6">
                <div class="stat-box text-center">
                  <i class="fa fa-sitemap fa-2x text-success"></i>
                  <h4>{{ sub_units|length }}</h4>
                  <p>الوحدات الفرعية</p>
                </div>
              </div>
            </div>
            
            <div class="row">
              <div class="col-md-12">
                <hr>
                <div class="text-center">
                  <small class="text-muted">
                    <strong>تاريخ الإضافة:</strong> {{ unit_info.date_added|date('d/m/Y H:i') }}<br>
                    <strong>آخر تعديل:</strong> {{ unit_info.date_modified|date('d/m/Y H:i') }}
                  </small>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="row">
      <!-- جدول التحويلات -->
      <div class="col-md-6">
        <div class="panel panel-info">
          <div class="panel-heading">
            <h3 class="panel-title">
              <i class="fa fa-exchange"></i> جدول التحويلات المتاحة
            </h3>
          </div>
          <div class="panel-body">
            {% if conversion_table %}
            <div class="table-responsive">
              <table class="table table-striped table-condensed">
                <thead>
                  <tr>
                    <th>إلى الوحدة</th>
                    <th class="text-center">معامل التحويل</th>
                    <th class="text-center">مثال</th>
                  </tr>
                </thead>
                <tbody>
                  {% for conversion in conversion_table %}
                  <tr>
                    <td>
                      <strong>{{ conversion.to_unit_name }}</strong>
                      <br><small class="text-muted">{{ conversion.to_unit_symbol }}</small>
                    </td>
                    <td class="text-center">
                      <span class="badge badge-info">{{ conversion.conversion_factor|number_format(6) }}</span>
                    </td>
                    <td class="text-center">
                      <small class="text-muted">
                        1 {{ unit_info.symbol }} = {{ conversion.conversion_factor|number_format(4) }} {{ conversion.to_unit_symbol }}
                      </small>
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
            {% else %}
            <div class="text-center text-muted">
              <i class="fa fa-info-circle fa-3x"></i>
              <p>لا توجد تحويلات متاحة لهذه الوحدة</p>
            </div>
            {% endif %}
          </div>
        </div>
      </div>
      
      <!-- الوحدات الفرعية -->
      <div class="col-md-6">
        <div class="panel panel-warning">
          <div class="panel-heading">
            <h3 class="panel-title">
              <i class="fa fa-sitemap"></i> الوحدات الفرعية
              {% if unit_info.is_base_unit %}
              <button type="button" class="btn btn-xs btn-success pull-right" data-toggle="modal" data-target="#add-sub-unit-modal">
                <i class="fa fa-plus"></i> إضافة وحدة فرعية
              </button>
              {% endif %}
            </h3>
          </div>
          <div class="panel-body">
            {% if sub_units %}
            <div class="table-responsive">
              <table class="table table-striped table-condensed">
                <thead>
                  <tr>
                    <th>اسم الوحدة</th>
                    <th class="text-center">الرمز</th>
                    <th class="text-center">معامل التحويل</th>
                  </tr>
                </thead>
                <tbody>
                  {% for sub_unit in sub_units %}
                  <tr>
                    <td>{{ sub_unit.name }}</td>
                    <td class="text-center">
                      <span class="label label-default">{{ sub_unit.symbol }}</span>
                    </td>
                    <td class="text-center">
                      <span class="badge badge-warning">{{ sub_unit.conversion_factor|number_format(6) }}</span>
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
            {% else %}
            <div class="text-center text-muted">
              <i class="fa fa-sitemap fa-3x"></i>
              <p>لا توجد وحدات فرعية</p>
              {% if unit_info.is_base_unit %}
              <button type="button" class="btn btn-primary btn-sm" data-toggle="modal" data-target="#add-sub-unit-modal">
                <i class="fa fa-plus"></i> إضافة أول وحدة فرعية
              </button>
              {% endif %}
            </div>
            {% endif %}
          </div>
        </div>
      </div>
    </div>
    
    <!-- حاسبة التحويل السريع -->
    <div class="row">
      <div class="col-md-12">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title">
              <i class="fa fa-calculator"></i> حاسبة التحويل السريع
            </h3>
          </div>
          <div class="panel-body">
            <form class="form-inline text-center">
              <div class="form-group">
                <input type="number" id="quick-quantity" class="form-control" value="1" step="0.000001" style="width: 120px;" />
              </div>
              
              <div class="form-group">
                <span class="form-control-static">{{ unit_info.symbol }}</span>
              </div>
              
              <div class="form-group">
                <span class="form-control-static">=</span>
              </div>
              
              <div class="form-group">
                <select id="quick-target-unit" class="form-control" style="width: 200px;">
                  <option value="">اختر الوحدة المستهدفة</option>
                  {% for conversion in conversion_table %}
                  <option value="{{ conversion.conversion_factor }}">{{ conversion.to_unit_name }} ({{ conversion.to_unit_symbol }})</option>
                  {% endfor %}
                </select>
              </div>
              
              <div class="form-group">
                <button type="button" class="btn btn-primary" onclick="quickConvert()">
                  <i class="fa fa-calculator"></i> حساب
                </button>
              </div>
            </form>
            
            <div id="quick-result" class="text-center" style="margin-top: 15px; display: none;">
              <div class="alert alert-success">
                <h4 id="quick-result-text"></h4>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- نافذة إضافة وحدة فرعية -->
{% if unit_info.is_base_unit %}
<div class="modal fade" id="add-sub-unit-modal" tabindex="-1" role="dialog">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title"><i class="fa fa-plus"></i> إضافة وحدة فرعية</h4>
      </div>
      <div class="modal-body">
        <form id="sub-unit-form">
          <div class="form-group">
            <label for="sub-unit-name">اسم الوحدة الفرعية</label>
            <input type="text" id="sub-unit-name" class="form-control" required />
          </div>
          
          <div class="form-group">
            <label for="sub-unit-symbol">رمز الوحدة</label>
            <input type="text" id="sub-unit-symbol" class="form-control" required />
          </div>
          
          <div class="form-group">
            <label for="sub-unit-factor">معامل التحويل</label>
            <input type="number" id="sub-unit-factor" class="form-control" step="0.000001" required />
            <small class="help-block">كم {{ unit_info.symbol }} يساوي وحدة واحدة من الوحدة الفرعية</small>
          </div>
          
          <div class="form-group">
            <label for="sub-unit-description">الوصف</label>
            <textarea id="sub-unit-description" class="form-control" rows="2"></textarea>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-primary" onclick="addSubUnit()">
          <i class="fa fa-save"></i> حفظ
        </button>
        <button type="button" class="btn btn-default" data-dismiss="modal">إلغاء</button>
      </div>
    </div>
  </div>
</div>
{% endif %}

<style>
.unit-icon {
  margin-bottom: 15px;
}

.label-lg {
  font-size: 16px;
  padding: 8px 12px;
}

.stat-box {
  padding: 15px;
  margin-bottom: 15px;
}

.stat-box h4 {
  margin: 10px 0 5px 0;
  font-weight: bold;
}

.stat-box p {
  margin: 0;
  color: #666;
  font-size: 12px;
}

.badge-info {
  background-color: #5bc0de;
}

.badge-warning {
  background-color: #f0ad4e;
}
</style>

<script type="text/javascript">
$(document).ready(function() {
    $('[data-toggle="tooltip"]').tooltip();
});

function quickConvert() {
    var quantity = parseFloat($('#quick-quantity').val());
    var factor = parseFloat($('#quick-target-unit').val());
    var targetText = $('#quick-target-unit option:selected').text();
    
    if (!quantity || !factor || !targetText) {
        alert('يرجى ملء جميع الحقول');
        return;
    }
    
    var result = quantity * factor;
    var resultText = quantity + ' {{ unit_info.symbol }} = ' + result.toFixed(6) + ' ' + targetText.split('(')[1].replace(')', '');
    
    $('#quick-result-text').text(resultText);
    $('#quick-result').show();
}

{% if unit_info.is_base_unit %}
function addSubUnit() {
    var name = $('#sub-unit-name').val();
    var symbol = $('#sub-unit-symbol').val();
    var factor = $('#sub-unit-factor').val();
    var description = $('#sub-unit-description').val();
    
    if (!name || !symbol || !factor) {
        alert('يرجى ملء الحقول المطلوبة');
        return;
    }
    
    // هنا ستتم عملية الإضافة الفعلية عبر AJAX
    alert('سيتم إضافة الوحدة الفرعية: ' + name + ' (' + symbol + ')');
    $('#add-sub-unit-modal').modal('hide');
    
    // إعادة تحميل الصفحة أو تحديث القائمة
    location.reload();
}
{% endif %}
</script>

{{ footer }}
