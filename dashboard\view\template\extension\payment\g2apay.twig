{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="submit" form="form-payment" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fa fa-save"></i></button>
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a></div>
      <h1>{{ heading_title }}</h1>      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
	{% if error_warning %}
		<div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
		  <button type="button" class="close" data-dismiss="alert">&times;</button>
		</div>
	{% endif %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pencil"></i> {{ text_edit }}</h3>
      </div>
      <div class="panel-body">
        <form action="{{ action }}" method="post" enctype="multipart/form-data" id="form-payment" class="form-horizontal">
		  <ul class="nav nav-tabs">
			<li class="active"><a href="#tab-general" data-toggle="tab">{{ tab_settings }}</a></li>
			<li><a href="#tab-order-status" data-toggle="tab">{{ tab_order_status }}</a></li>
		  </ul>
		  <div class="tab-content">
			<div class="tab-pane active" id="tab-general">
			  <div class="form-group required">
				<label class="col-sm-2 control-label" for="input-username">{{ entry_username }}</label>
				<div class="col-sm-10">
				  <input type="text" name="payment_g2apay_username" value="{{ payment_g2apay_username }}" placeholder="{{ entry_username }}" id="input-username" class="form-control" />
				  <span class="help-block">{{ help_username }}</span>
				  {% if error_username %}
					  <div class="text-danger">{{ error_username }}</div>
				  {% endif %}
				</div>
			  </div>
			  <div class="form-group required">
				<label class="col-sm-2 control-label" for="input-secret">{{ entry_secret }}</label>
				<div class="col-sm-10">
				  <input type="text" name="payment_g2apay_secret" value="{{ payment_g2apay_secret }}" placeholder="{{ entry_secret }}" id="input-secret" class="form-control" />
				  {% if error_secret %}
					  <div class="text-danger">{{ error_secret }}</div>
				  {% endif %}
				</div>
			  </div>
			  <div class="form-group required">
				<label class="col-sm-2 control-label" for="input-api-hash">{{ entry_api_hash }}</label>
				<div class="col-sm-10">
				  <input type="text" name="payment_g2apay_api_hash" value="{{ payment_g2apay_api_hash }}" placeholder="{{ entry_api_hash }}" id="input-api-hash" class="form-control" />
				  {% if error_api_hash %}
					  <div class="text-danger">{{ error_api_hash }}</div>
				  {% endif %}
				</div>
			  </div>
			  <div class="form-group">
				<label class="col-sm-2 control-label" for="input-environment">{{ entry_environment }}</label>
				<div class="col-sm-10">
				  <select name="payment_g2apay_environment" class="form-control" id="input-environment">
					{% if payment_g2apay_environment %}
						<option value="1" selected="selected">{{ g2apay_environment_live }}</option>
						<option value="0">{{ g2apay_environment_test }}</option>
					{% else %}
						<option value="1">{{ g2apay_environment_live }}</option>
						<option value="0" selected="selected">{{ g2apay_environment_test }}</option>
					{% endif %}
				  </select>
				</div>
			  </div>
			  <div class="form-group">
				<label class="col-sm-2 control-label" for="input-secret-token"><span data-toggle="tooltip" title="{{ help_secret_token }}">{{ entry_secret_token }}</span></label>
				<div class="col-sm-10">
				  <input type="text" name="payment_g2apay_secret_token" value="{{ payment_g2apay_secret_token }}" id="input-secret-token" class="form-control" />
				</div>
			  </div>
			  <div class="form-group">
				<label class="col-sm-2 control-label" for="input-ipn-url"><span data-toggle="tooltip" title="{{ help_ipn_url }}">{{ entry_ipn_url }}</span></label>
				<div class="col-sm-10">
				  <div class="input-group"><span class="input-group-addon"><i class="fa fa-link"></i></span>
					<input type="text" readonly value="{{ g2apay_ipn_url }}" id="input-ipn-url" class="form-control" />
				  </div>
				</div>
			  </div>
			  <div class="form-group">
				<label class="col-sm-2 control-label" for="input-total">{{ entry_total }} </label>
				<div class="col-sm-10">
				  <input type="text" name="payment_g2apay_total" value="{{ payment_g2apay_total }}" placeholder="{{ entry_total }}" id="input-total" class="form-control" />
				  <span class="help-block">{{ help_total }}</span> </div>
			  </div>
			  <div class="form-group">
				<label class="col-sm-2 control-label" for="input-geo-zone">{{ entry_geo_zone }}</label>
				<div class="col-sm-10">
				  <select name="payment_g2apay_geo_zone_id" id="input-geo-zone" class="form-control">
					<option value="0">{{ text_all_zones }}</option>
					{% for geo_zone in geo_zones %}
						{% if geo_zone.geo_zone_id == payment_g2apay_geo_zone_id %}
							<option value="{{ geo_zone.geo_zone_id }}" selected="selected">{{ geo_zone.name }}</option>
						{% else %}
							<option value="{{ geo_zone.geo_zone_id }}">{{ geo_zone.name }}</option>
						{% endif %}
					{% endfor %}
				  </select>
				</div>
			  </div>
			  <div class="form-group">
				<label class="col-sm-2 control-label" for="input-debug">{{ entry_debug }}</label>
				<div class="col-sm-10">
				  <select name="payment_g2apay_debug" id="input-debug" class="form-control">
					{% if payment_g2apay_debug %}
						<option value="1" selected="selected">{{ text_enabled }}</option>
						<option value="0">{{ text_disabled }}</option>
					{% else %}
						<option value="1">{{ text_enabled }}</option>
						<option value="0" selected="selected">{{ text_disabled }}</option>
					{% endif %}
				  </select>
				  <span class="help-block">{{ help_debug }}</span>
				</div>
			  </div>
			  <div class="form-group">
				<label class="col-sm-2 control-label" for="input-status">{{ entry_status }}</label>
				<div class="col-sm-10">
				  <select name="payment_g2apay_status" id="input-status" class="form-control">
					{% if payment_g2apay_status %}
						<option value="1" selected="selected">{{ text_enabled }}</option>
						<option value="0">{{ text_disabled }}</option>
					{% else %}
						<option value="1">{{ text_enabled }}</option>
						<option value="0" selected="selected">{{ text_disabled }}</option>
					{% endif %}
				  </select>
				</div>
			  </div>
			  <div class="form-group">
				<label class="col-sm-2 control-label" for="input-sort-order">{{ entry_sort_order }}</label>
				<div class="col-sm-10">
				  <input type="text" name="payment_g2apay_sort_order" value="{{ payment_g2apay_sort_order }}" placeholder="{{ entry_sort_order }}" id="input-sort-order" class="form-control" />
				</div>
			  </div>
			</div>
			<div class="tab-pane" id="tab-order-status">
			  <div class="form-group">
				<label class="col-sm-2 control-label" for="input-order-status">{{ entry_order_status }}</label>
				<div class="col-sm-10">
				  <select name="payment_g2apay_order_status_id" id="input-order-status" class="form-control">
					{% for order_status in order_statuses %}
						{% if order_status.order_status_id == payment_g2apay_order_status_id %}
							<option value="{{ order_status.order_status_id }}" selected="selected">{{ order_status.name }}</option>
						{% else %}
							<option value="{{ order_status.order_status_id }}">{{ order_status.name }}</option>
						{% endif %}
					{% endfor %}
				  </select>
				</div>
			  </div>
			  <div class="form-group">
				<label class="col-sm-2 control-label">{{ entry_complete_status }}</label>
				<div class="col-sm-10">
				  <select name="payment_g2apay_complete_status_id" class="form-control">
					{% for order_status in order_statuses %}
						{% if order_status.order_status_id == payment_g2apay_complete_status_id %}
							<option value="{{ order_status.order_status_id }}" selected="selected">{{ order_status.name }}</option>
						{% else %}
							<option value="{{ order_status.order_status_id }}">{{ order_status.name }}</option>
						{% endif %}
					{% endfor %}
				  </select>
				</div>
			  </div>
			  <div class="form-group">
				<label class="col-sm-2 control-label">{{ entry_rejected_status }}</label>
				<div class="col-sm-10">
				  <select name="payment_g2apay_rejected_status_id" class="form-control">
					{% for order_status in order_statuses %}
						{% if order_status.order_status_id == payment_g2apay_rejected_status_id %}
							<option value="{{ order_status.order_status_id }}" selected="selected">{{ order_status.name }}</option>
						{% else %}
							<option value="{{ order_status.order_status_id }}">{{ order_status.name }}</option>
						{% endif %}
					{% endfor %}
				  </select>
				</div>
			  </div>
			  <div class="form-group">
				<label class="col-sm-2 control-label">{{ entry_cancelled_status }}</label>
				<div class="col-sm-10">
				  <select name="payment_g2apay_cancelled_status_id" class="form-control">
					{% for order_status in order_statuses %}
						{% if order_status.order_status_id == payment_g2apay_cancelled_status_id %}
							<option value="{{ order_status.order_status_id }}" selected="selected">{{ order_status.name }}</option>
						{% else %}
							<option value="{{ order_status.order_status_id }}">{{ order_status.name }}</option>
						{% endif %}
					{% endfor %}
				  </select>
				</div>
			  </div>
			  <div class="form-group">
				<label class="col-sm-2 control-label">{{ entry_pending_status }}</label>
				<div class="col-sm-10">
				  <select name="payment_g2apay_pending_status_id" class="form-control">
					{% for order_status in order_statuses %}
						{% if order_status.order_status_id == payment_g2apay_pending_status_id %}
							<option value="{{ order_status.order_status_id }}" selected="selected">{{ order_status.name }}</option>
						{% else %}
							<option value="{{ order_status.order_status_id }}">{{ order_status.name }}</option>
						{% endif %}
					{% endfor %}
				  </select>
				</div>
			  </div>
			  <div class="form-group">
				<label class="col-sm-2 control-label">{{ entry_refunded_status }}</label>
				<div class="col-sm-10">
				  <select name="payment_g2apay_refunded_status_id" class="form-control">
					{% for order_status in order_statuses %}
						{% if order_status.order_status_id == payment_g2apay_refunded_status_id %}
							<option value="{{ order_status.order_status_id }}" selected="selected">{{ order_status.name }}</option>
						{% else %}
							<option value="{{ order_status.order_status_id }}">{{ order_status.name }}</option>
						{% endif %}
					{% endfor %}
				  </select>
				</div>
			  </div>
			  <div class="form-group">
				<label class="col-sm-2 control-label">{{ entry_partially_refunded_status }}</label>
				<div class="col-sm-10">
				  <select name="payment_g2apay_partially_refunded_status_id" class="form-control">
					{% for order_status in order_statuses %}
						{% if order_status.order_status_id == payment_g2apay_partially_refunded_status_id %}
							<option value="{{ order_status.order_status_id }}" selected="selected">{{ order_status.name }}</option>
						{% else %}
							<option value="{{ order_status.order_status_id }}">{{ order_status.name }}</option>
						{% endif %}
					{% endfor %}
				  </select>
				</div>
			  </div>
			</div>
		  </div>
        </form>
      </div>
    </div>
  </div>
</div>
{{ footer }}