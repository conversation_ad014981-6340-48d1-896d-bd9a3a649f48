{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <a href="{{ back }}" data-toggle="tooltip" title="{{ button_back }}" class="btn btn-default"><i class="fa fa-reply"></i></a>
      </div>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-info-circle"></i> {{ text_handover_details }}</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-sm-6">
            <div class="well">
              <h4>{{ text_handover_info }}</h4>
              <div class="form-group">
                <label class="col-sm-4 control-label">{{ entry_handover_id }}</label>
                <div class="col-sm-8">{{ handover.handover_id }}</div>
              </div>
              <div class="form-group">
                <label class="col-sm-4 control-label">{{ entry_shift }}</label>
                <div class="col-sm-8">{{ handover.shift_id }}</div>
              </div>
              <div class="form-group">
                <label class="col-sm-4 control-label">{{ entry_from_user }}</label>
                <div class="col-sm-8">{{ handover.from_user }}</div>
              </div>
              <div class="form-group">
                <label class="col-sm-4 control-label">{{ entry_to_user }}</label>
                <div class="col-sm-8">{{ handover.to_user }}</div>
              </div>
              <div class="form-group">
                <label class="col-sm-4 control-label">{{ entry_time }}</label>
                <div class="col-sm-8">{{ handover.handover_time }}</div>
              </div>
              <div class="form-group">
                <label class="col-sm-4 control-label">{{ entry_amount }}</label>
                <div class="col-sm-8">{{ handover.amount }}</div>
              </div>
            </div>
          </div>
          <div class="col-sm-6">
            <div class="well">
              <h4>{{ text_shift_info }}</h4>
              {% if shift %}
              <div class="form-group">
                <label class="col-sm-4 control-label">{{ entry_shift_id }}</label>
                <div class="col-sm-8">{{ shift.shift_id }}</div>
              </div>
              <div class="form-group">
                <label class="col-sm-4 control-label">{{ entry_terminal }}</label>
                <div class="col-sm-8">{{ shift.terminal_name }}</div>
              </div>
              <div class="form-group">
                <label class="col-sm-4 control-label">{{ entry_branch }}</label>
                <div class="col-sm-8">{{ shift.branch_name }}</div>
              </div>
              <div class="form-group">
                <label class="col-sm-4 control-label">{{ entry_start_time }}</label>
                <div class="col-sm-8">{{ shift.start_time }}</div>
              </div>
              <div class="form-group">
                <label class="col-sm-4 control-label">{{ entry_status }}</label>
                <div class="col-sm-8">
                  <span class="label label-{{ shift.status == 'active' ? 'success' : 'default' }}">{{ shift.status }}</span>
                </div>
              </div>
              {% else %}
              <div class="text-center">{{ text_no_shift_info }}</div>
              {% endif %}
            </div>
          </div>
        </div>
        
        <div class="row">
          <div class="col-sm-12">
            <div class="panel panel-default">
              <div class="panel-heading">
                <h3 class="panel-title">{{ text_notes }}</h3>
              </div>
              <div class="panel-body">
                <div class="well well-sm">{{ handover.notes ? handover.notes : text_no_notes }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{{ footer }}