### Rewrite Rules Added by CyberPanel Rewrite Rule Generator

RewriteEngine On
RewriteCond %{HTTP_HOST} ^www.(.*)$
RewriteRule ^(.*)$ http://%1/$1 [L,R=301]

### End CyberPanel Generated Rules.

### Rewrite Rules Added by CyberPanel Rewrite Rule Generator

RewriteEngine On
RewriteCond %{HTTPS}  !=on
RewriteRule ^/?(.*) https://%{SERVER_NAME}/$1 [R,L]

### End CyberPanel Generated Rules.


# 1.To use URL Alias you need to be running apache with mod_rewrite enabled.

# 2. In your opencart directory rename htaccess.txt to .htaccess.

# For any support issues please visit: http://www.opencart.com

Options +FollowSymlinks

# Prevent Directory listing
Options -Indexes

# Prevent Direct Access to files
<FilesMatch "(?i)((\.tpl|\.twig|\.ini|\.log|(?<!robots)\.txt))">
 Require all denied
## For apache 2.2 and older, replace "Require all denied" with these two lines :
# Order deny,allow
# Deny from all
</FilesMatch>

# Enable CORS
<IfModule mod_headers.c>
    Header set Access-Control-Allow-Origin "*"
</IfModule>

# Cache-Control Headers
<FilesMatch "\.(ico|jpg|jpeg|png|gif|webp|avif|svg|js|css|swf|woff|woff2|ttf|eot)$">
    <IfModule mod_headers.c>
        Header set Cache-Control "max-age=31536000, public"
    </IfModule>
</FilesMatch>

# Prevent caching of dynamic content
<FilesMatch "\.(php|cgi|pl|htm|html|htaccess)$">
    <IfModule mod_headers.c>
        Header set Cache-Control "no-cache, no-store, must-revalidate"
        Header set Pragma "no-cache"
        Header set Expires 0
    </IfModule>
</FilesMatch>

# Add correct content-type for fonts
<IfModule mod_mime.c>
    AddType application/vnd.ms-fontobject .eot
    AddType application/x-font-ttf .ttf
    AddType application/x-font-opentype .otf
    AddType application/x-font-woff .woff
    AddType application/x-font-woff2 .woff2
    AddType image/svg+xml .svg
</IfModule>

# Compress HTML, CSS, JavaScript, Text, XML and fonts
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/vnd.ms-fontobject
    AddOutputFilterByType DEFLATE application/x-font
    AddOutputFilterByType DEFLATE application/x-font-opentype
    AddOutputFilterByType DEFLATE application/x-font-otf
    AddOutputFilterByType DEFLATE application/x-font-truetype
    AddOutputFilterByType DEFLATE application/x-font-ttf
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE font/opentype
    AddOutputFilterByType DEFLATE font/otf
    AddOutputFilterByType DEFLATE font/ttf
    AddOutputFilterByType DEFLATE image/svg+xml
    AddOutputFilterByType DEFLATE image/x-icon
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/javascript
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/xml
</IfModule>

# Set ETag
FileETag MTime Size

# Set Expires Headers
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/webp "access plus 1 year"
    ExpiresByType image/avif "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType text/html "access plus 1 second"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType text/x-javascript "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-shockwave-flash "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    ExpiresByType font/ttf "access plus 1 year"
    ExpiresByType font/otf "access plus 1 year"
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresDefault "access plus 2 days"
</IfModule>

# Prevent AJAX requests from being cached
<IfModule mod_headers.c>
    <FilesMatch "\.(php)$">
        Header set Cache-Control "no-cache, no-store, must-revalidate"
        Header set Pragma "no-cache"
        Header set Expires 0
    </FilesMatch>
</IfModule>
# SEO URL Settings
RewriteEngine On
# If your opencart installation does not run in the main web folder make sure the following is set to the folder it does run in, i.e. / becomes /shop/
RewriteBase /
RewriteRule ^sitemap.xml$ index.php?route=extension/feed/google_sitemap [L]
RewriteRule ^googlebase.xml$ index.php?route=extension/feed/google_base [L]
RewriteRule ^system/storage/(.*) index.php?route=error/not_found [L]
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} !.*\.(ico|gif|jpg|jpeg|png|webp|js|css)
RewriteRule ^([^?]*) index.php?_route_=$1 [L,QSA]



### Additional Settings that may need to be enabled for some servers
### Uncomment the commands by removing the # sign in front of it.
### If you get an "Internal Server Error 500" after enabling any of the following settings, restore the # as this means your host doesn't allow that.

# 1. Set max upload file size. Most hosts will limit this and not allow it to be overridden but you can try.
# php_value upload_max_filesize 999M

# 2. Set max post size. Uncomment this line if you have a lot of product options or are getting errors where forms are not saving all fields.
# php_value post_max_size 999M

# 3. Set max time script can take. Uncomment this line if you have a lot of product options or are getting errors where forms are not saving all fields.
# php_value max_execution_time 200

# 4. Set max time for input to be received. Uncomment this line if you have a lot of product options or are getting errors where forms are not saving all fields.
# php_value max_input_time 200

# 5. Disable open_basedir limitations. Most hosts will limit this and not allow it to be overridden but you can try.
# php_value open_basedir none
RewriteCond %{HTTP_HOST} ^store\.codaym\.com$ [OR]
RewriteCond %{HTTP_HOST} ^www\.store\.codaym\.com$
RewriteRule ^(.*)$ "https\:\/\/codaym\.com\/$1" [R=301,L]

# php -- BEGIN cPanel-generated handler, do not edit
# Set the “ea-php82” package as the default “PHP” programming language.
<IfModule mime_module>
  AddHandler application/x-httpd-ea-php82 .php .php8 .phtml
</IfModule>
# php -- END cPanel-generated handler, do not edit
