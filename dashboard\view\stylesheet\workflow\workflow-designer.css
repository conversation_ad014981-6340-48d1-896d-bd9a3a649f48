/**
 * Workflow Designer CSS
 */

/* Main container */
.workflow-container {
    position: relative;
    width: 100%;
    height: 600px;
    border: 1px solid #ddd;
    overflow: hidden;
    background: #f9f9f9;
}

/* Toolbar */
.workflow-toolbar {
    width: 100%;
    padding: 10px;
    background: #f5f5f5;
    border-bottom: 1px solid #ddd;
}

.workflow-toolbar .btn {
    margin-right: 5px;
}

/* Canvas area */
.workflow-canvas {
    position: relative;
    width: 5000px;
    height: 5000px;
    transform-origin: 0 0;
}

/* Node palette */
.workflow-palette {
    position: absolute;
    top: 50px;
    left: 10px;
    width: 150px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
    z-index: 100;
}

.workflow-palette-title {
    font-weight: bold;
    margin-bottom: 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid #eee;
}

/* Node styles */
.workflow-node {
    position: absolute;
    width: 150px;
    height: 80px;
    background: #fff;
    border: 2px solid #337ab7;
    border-radius: 5px;
    z-index: 10;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.workflow-node.selected {
    border-color: #ff9800;
    box-shadow: 0 0 8px rgba(255,152,0,0.6);
}

.workflow-node-title {
    padding: 5px 10px;
    background: #337ab7;
    color: #fff;
    font-weight: bold;
    font-size: 12px;
    border-radius: 3px 3px 0 0;
    cursor: move;
}

.workflow-node-connectors {
    position: relative;
    height: 54px;
}

.workflow-node-connector {
    position: absolute;
    width: 12px;
    height: 12px;
    background: #aaa;
    border-radius: 50%;
    cursor: pointer;
}

.workflow-node-connector:hover {
    background: #666;
}

.workflow-node-input {
    top: 21px;
    left: -6px;
}

.workflow-node-output {
    top: 21px;
    right: -6px;
}

/* Different node types */
.workflow-node-start {
    border-color: #5cb85c;
}

.workflow-node-start .workflow-node-title {
    background: #5cb85c;
}

.workflow-node-end {
    border-color: #d9534f;
}

.workflow-node-end .workflow-node-title {
    background: #d9534f;
}

.workflow-node-task {
    border-color: #5bc0de;
}

.workflow-node-task .workflow-node-title {
    background: #5bc0de;
}

.workflow-node-decision {
    border-color: #f0ad4e;
}

.workflow-node-decision .workflow-node-title {
    background: #f0ad4e;
}

.workflow-node-email {
    border-color: #9c27b0;
}

.workflow-node-email .workflow-node-title {
    background: #9c27b0;
}

.workflow-node-delay {
    border-color: #795548;
}

.workflow-node-delay .workflow-node-title {
    background: #795548;
}

/* Connection lines */
.workflow-connector-line {
    background: #aaa;
    position: absolute;
    z-index: 5;
}

.workflow-connector-permanent {
    background: #666;
}

/* Properties panel */
.workflow-property-panel {
    position: absolute;
    top: 50px;
    right: 10px;
    width: 300px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    z-index: 100;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    display: none;
}

.workflow-property-panel-header {
    padding: 10px;
    background: #f5f5f5;
    border-bottom: 1px solid #ddd;
    font-weight: bold;
    position: relative;
}

.workflow-property-panel-close {
    position: absolute;
    right: 10px;
    top: 10px;
    cursor: pointer;
}

.workflow-property-panel-body {
    padding: 15px;
    max-height: 400px;
    overflow-y: auto;
}

.workflow-property-panel-footer {
    padding: 10px;
    background: #f5f5f5;
    border-top: 1px solid #ddd;
    text-align: right;
}

.property-field {
    margin-bottom: 15px;
}

.property-field label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

/* Node palette items */
.workflow-palette-item {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 3px;
    margin-bottom: 5px;
    padding: 8px;
    cursor: move;
    font-size: 12px;
}

.workflow-palette-item:hover {
    background: #f5f5f5;
    border-color: #aaa;
}

/* Zoom controls */
.workflow-zoom-controls {
    position: absolute;
    bottom: 15px;
    right: 15px;
    background: rgba(255,255,255,0.8);
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 5px;
    z-index: 50;
}

.workflow-zoom-controls .btn {
    padding: 2px 5px;
    font-size: 12px;
}

/* Meta fields */
.workflow-meta {
    margin-bottom: 15px;
}

.workflow-meta .form-group {
    margin-bottom: 10px;
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
    .workflow-property-panel {
        width: 100%;
        right: 0;
        left: 0;
    }
    
    .workflow-palette {
        width: 100px;
    }
} 