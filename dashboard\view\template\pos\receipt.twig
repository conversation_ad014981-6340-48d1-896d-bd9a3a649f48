<!DOCTYPE html>
<html lang="{{ lang }}">
<head>
    <meta charset="UTF-8">
    <title>{{ text_receipt }}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
        }

        .receipt {
            width: 300px;
            padding: 10px;
            border: 1px solid #000;
        }

        .header {
            text-align: center;
            border-bottom: 1px solid #000;
            padding-bottom: 10px;
            margin-bottom: 10px;
        }

        .header h1 {
            margin: 0;
            font-size: 18px;
        }

        .header p {
            margin: 0;
            font-size: 14px;
        }

        .details {
            margin-bottom: 10px;
        }

        .details p {
            margin: 0;
            font-size: 14px;
        }

        .products {
            width: 100%;
            border-collapse: collapse;
        }

        .products th,
        .products td {
            border: 1px solid #000;
            padding: 5px;
            font-size: 14px;
        }

        .products th {
            text-align: left;
        }

        .totals {
            width: 100%;
            margin-top: 10px;
        }

        .totals th,
        .totals td {
            padding: 5px;
            font-size: 14px;
        }

        .totals th {
            text-align: left;
        }

        .totals td {
            text-align: right;
        }

        .footer {
            text-align: center;
            border-top: 1px solid #000;
            padding-top: 10px;
            margin-top: 10px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="receipt">
        <div class="header">
            <h1>{{ text_receipt }}</h1>
            <p>{{ store_name }}</p>
            <p>{{ store_address }}</p>
        </div>
        <div class="details">
            <p>{{ text_date }}: {{ order.date_added }}</p>
            <p>{{ text_order_id }}: {{ order.order_id }}</p>
            <p>{{ text_cashier }}: {{ cashier_name }}</p>
        </div>
        <table class="products">
            <thead>
                <tr>
                    <th>{{ text_product }}</th>
                    <th>{{ text_quantity }}</th>
                    <th>{{ text_price }}</th>
                    <th>{{ text_total }}</th>
                </tr>
            </thead>
            <tbody>
                {% for product in products %}
                <tr>
                    <td>{{ product.name }}</td>
                    <td>{{ product.quantity }}</td>
                    <td>{{ product.price }}</td>
                    <td>{{ product.total }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        <table class="totals">
            <tr>
                <th>{{ text_subtotal }}</th>
                <td>{{ totals.subtotal }}</td>
            </tr>
            <tr>
                <th>{{ text_discount }}</th>
                <td>{{ totals.discount }}</td>
            </tr>
            <tr>
                <th>{{ text_total }}</th>
                <td>{{ totals.total }}</td>
            </tr>
        </table>
        <div class="footer">
            <p>{{ text_thank_you }}</p>
        </div>
    </div>
</body>
</html>
