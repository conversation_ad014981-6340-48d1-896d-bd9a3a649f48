{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="submit" form="accounts\period_closing-form" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fa fa-save"></i></button>
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="close" data-dismiss="alert">&times;</button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="close" data-dismiss="alert">&times;</button>
      </div>
    {{% endif %}}
    
    <div class="panel panel-default">
      <div class="panel-heading"><i class="fa fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}bar-chart{% elif screen_type == 'settings' %}cog{% else %}pencil{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="panel-body">
        <form id="accounts\period_closing-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-back">{{ text_back }}</label>
            <div class="col-sm-10">
              <input type="text" name="back" value="{{ back }}" placeholder="{{ text_back }}" id="input-back" class="form-control" />
              {% if error_back %}
                <div class="invalid-feedback">{{ error_back }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-close">{{ text_close }}</label>
            <div class="col-sm-10">
              <input type="text" name="close" value="{{ close }}" placeholder="{{ text_close }}" id="input-close" class="form-control" />
              {% if error_close %}
                <div class="invalid-feedback">{{ error_close }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-closing_notes">{{ text_closing_notes }}</label>
            <div class="col-sm-10">
              <input type="text" name="closing_notes" value="{{ closing_notes }}" placeholder="{{ text_closing_notes }}" id="input-closing_notes" class="form-control" />
              {% if error_closing_notes %}
                <div class="invalid-feedback">{{ error_closing_notes }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-current_period">{{ text_current_period }}</label>
            <div class="col-sm-10">
              <input type="text" name="current_period" value="{{ current_period }}" placeholder="{{ text_current_period }}" id="input-current_period" class="form-control" />
              {% if error_current_period %}
                <div class="invalid-feedback">{{ error_current_period }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-end_date">{{ text_end_date }}</label>
            <div class="col-sm-10">
              <input type="text" name="end_date" value="{{ end_date }}" placeholder="{{ text_end_date }}" id="input-end_date" class="form-control" />
              {% if error_end_date %}
                <div class="invalid-feedback">{{ error_end_date }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_end_date">{{ text_error_end_date }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_end_date" value="{{ error_end_date }}" placeholder="{{ text_error_end_date }}" id="input-error_end_date" class="form-control" />
              {% if error_error_end_date %}
                <div class="invalid-feedback">{{ error_error_end_date }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_period_name">{{ text_error_period_name }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_period_name" value="{{ error_period_name }}" placeholder="{{ text_error_period_name }}" id="input-error_period_name" class="form-control" />
              {% if error_error_period_name %}
                <div class="invalid-feedback">{{ error_error_period_name }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_start_date">{{ text_error_start_date }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_start_date" value="{{ error_start_date }}" placeholder="{{ text_error_start_date }}" id="input-error_start_date" class="form-control" />
              {% if error_error_start_date %}
                <div class="invalid-feedback">{{ error_error_start_date }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="invalid-feedback">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-period_name">{{ text_period_name }}</label>
            <div class="col-sm-10">
              <input type="text" name="period_name" value="{{ period_name }}" placeholder="{{ text_period_name }}" id="input-period_name" class="form-control" />
              {% if error_period_name %}
                <div class="invalid-feedback">{{ error_period_name }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-periods">{{ text_periods }}</label>
            <div class="col-sm-10">
              <input type="text" name="periods" value="{{ periods }}" placeholder="{{ text_periods }}" id="input-periods" class="form-control" />
              {% if error_periods %}
                <div class="invalid-feedback">{{ error_periods }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-preview">{{ text_preview }}</label>
            <div class="col-sm-10">
              <input type="text" name="preview" value="{{ preview }}" placeholder="{{ text_preview }}" id="input-preview" class="form-control" />
              {% if error_preview %}
                <div class="invalid-feedback">{{ error_preview }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-start_date">{{ text_start_date }}</label>
            <div class="col-sm-10">
              <input type="text" name="start_date" value="{{ start_date }}" placeholder="{{ text_start_date }}" id="input-start_date" class="form-control" />
              {% if error_start_date %}
                <div class="invalid-feedback">{{ error_start_date }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}