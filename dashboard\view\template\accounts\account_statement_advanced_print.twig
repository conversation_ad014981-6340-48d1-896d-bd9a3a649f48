<!DOCTYPE html>
<html dir="{{ direction }}" lang="{{ lang }}" class="no-js">
<head>
<meta charset="UTF-8" />
<title>{{ heading_title }}</title>
<base href="{{ base }}" />
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
<link href="view/stylesheet/bootstrap.css" rel="stylesheet" media="screen" />
<style>
@media print {
  body { margin: 0; }
  .no-print { display: none !important; }
  .page-break { page-break-before: always; }
}

body {
  font-family: Arial, sans-serif;
  font-size: 12px;
  line-height: 1.4;
}

.header {
  text-align: center;
  margin-bottom: 30px;
  border-bottom: 2px solid #333;
  padding-bottom: 20px;
}

.company-logo {
  max-height: 80px;
  margin-bottom: 10px;
}

.company-name {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}

.report-title {
  font-size: 18px;
  font-weight: bold;
  color: #666;
  margin-bottom: 10px;
}

.report-info {
  font-size: 11px;
  color: #888;
}

.account-info {
  background-color: #f8f9fa;
  padding: 15px;
  margin-bottom: 20px;
  border: 1px solid #dee2e6;
}

.summary-box {
  background-color: #e9ecef;
  padding: 15px;
  margin-bottom: 20px;
  border-left: 4px solid #007bff;
}

.statement-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 20px;
}

.statement-table th,
.statement-table td {
  border: 1px solid #dee2e6;
  padding: 8px;
  text-align: left;
}

.statement-table th {
  background-color: #f8f9fa;
  font-weight: bold;
}

.text-right {
  text-align: right;
}

.text-center {
  text-align: center;
}

.text-success {
  color: #28a745;
}

.text-danger {
  color: #dc3545;
}

.text-warning {
  color: #ffc107;
}

.text-info {
  color: #17a2b8;
}

.footer {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #dee2e6;
  font-size: 10px;
  color: #888;
}

.signature-section {
  margin-top: 40px;
  display: flex;
  justify-content: space-between;
}

.signature-box {
  width: 200px;
  text-align: center;
}

.signature-line {
  border-top: 1px solid #333;
  margin-top: 50px;
  padding-top: 5px;
}

.watermark {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(-45deg);
  font-size: 72px;
  color: rgba(0,0,0,0.05);
  z-index: -1;
  pointer-events: none;
}
</style>
</head>
<body>

<!-- Watermark -->
<div class="watermark">{{ text_confidential }}</div>

<!-- Header -->
<div class="header">
  {% if company_logo %}
  <img src="{{ company_logo }}" alt="{{ company_name }}" class="company-logo">
  {% endif %}
  <div class="company-name">{{ company_name }}</div>
  <div class="report-title">{{ heading_title }}</div>
  <div class="report-info">
    {{ text_generated_on }}: {{ generated_date }} | {{ text_generated_by }}: {{ generated_by }}
  </div>
</div>

<!-- Account Information -->
<div class="account-info">
  <h3 style="margin-top: 0;">{{ text_account_info }}</h3>
  <div style="display: flex; justify-content: space-between;">
    <div>
      <strong>{{ text_account_code }}:</strong> {{ account.code }}<br>
      <strong>{{ text_account_name }}:</strong> {{ account.name }}<br>
      <strong>{{ text_account_type }}:</strong> {{ account.type_name }}
    </div>
    <div>
      <strong>{{ text_statement_period }}:</strong> {{ date_start }} {{ text_to }} {{ date_end }}<br>
      <strong>{{ text_report_date }}:</strong> {{ generated_date }}<br>
      <strong>{{ text_page }}:</strong> 1 {{ text_of }} 1
    </div>
  </div>
</div>

<!-- Summary -->
<div class="summary-box">
  <h3 style="margin-top: 0;">{{ text_statement_summary }}</h3>
  <div style="display: flex; justify-content: space-between;">
    <div>
      <strong>{{ text_opening_balance }}:</strong> {{ summary.opening_balance }}<br>
      <strong>{{ text_total_debit }}:</strong> {{ summary.total_debit }}<br>
      <strong>{{ text_total_credit }}:</strong> {{ summary.total_credit }}
    </div>
    <div>
      <strong>{{ text_closing_balance }}:</strong> {{ summary.closing_balance }}<br>
      <strong>{{ text_net_movement }}:</strong> 
      <span class="{% if summary.net_movement >= 0 %}text-success{% else %}text-danger{% endif %}">
        {{ summary.net_movement }}
      </span><br>
      <strong>{{ text_transaction_count }}:</strong> {{ summary.transaction_count }}
    </div>
  </div>
</div>

<!-- Statement Details -->
<h3>{{ text_statement_details }}</h3>
<table class="statement-table">
  <thead>
    <tr>
      <th style="width: 12%;">{{ column_date }}</th>
      <th style="width: 40%;">{{ column_description }}</th>
      <th style="width: 15%;">{{ column_reference }}</th>
      <th style="width: 11%;" class="text-right">{{ column_debit }}</th>
      <th style="width: 11%;" class="text-right">{{ column_credit }}</th>
      <th style="width: 11%;" class="text-right">{{ column_running_balance }}</th>
    </tr>
  </thead>
  <tbody>
    {% if include_opening and summary.opening_balance != '0.00' %}
    <tr style="background-color: #d1ecf1;">
      <td>{{ date_start }}</td>
      <td><strong>{{ text_opening_balance }}</strong></td>
      <td>-</td>
      <td class="text-right">-</td>
      <td class="text-right">-</td>
      <td class="text-right"><strong>{{ summary.opening_balance }}</strong></td>
    </tr>
    {% endif %}
    
    {% for transaction in transactions %}
    <tr>
      <td>{{ transaction.date }}</td>
      <td>{{ transaction.description }}</td>
      <td>{{ transaction.reference }}</td>
      <td class="text-right{% if transaction.debit > 0 %} text-danger{% endif %}">
        {% if transaction.debit > 0 %}{{ transaction.debit }}{% else %}-{% endif %}
      </td>
      <td class="text-right{% if transaction.credit > 0 %} text-success{% endif %}">
        {% if transaction.credit > 0 %}{{ transaction.credit }}{% else %}-{% endif %}
      </td>
      <td class="text-right">{{ transaction.running_balance }}</td>
    </tr>
    {% endfor %}
    
    {% if include_closing %}
    <tr style="background-color: #fff3cd;">
      <td>{{ date_end }}</td>
      <td><strong>{{ text_closing_balance }}</strong></td>
      <td>-</td>
      <td class="text-right">-</td>
      <td class="text-right">-</td>
      <td class="text-right"><strong>{{ summary.closing_balance }}</strong></td>
    </tr>
    {% endif %}
  </tbody>
  <tfoot>
    <tr style="background-color: #e2e3e5; font-weight: bold;">
      <td colspan="3" class="text-center">{{ text_total }}</td>
      <td class="text-right">{{ summary.total_debit }}</td>
      <td class="text-right">{{ summary.total_credit }}</td>
      <td class="text-right">{{ summary.closing_balance }}</td>
    </tr>
  </tfoot>
</table>

<!-- Analysis (if included) -->
{% if analysis and include_analysis %}
<div class="page-break"></div>
<h3>{{ text_advanced_analysis }}</h3>

<div style="display: flex; justify-content: space-between; margin-bottom: 20px;">
  <div style="width: 48%;">
    <h4>{{ text_statistics }}</h4>
    <table class="statement-table">
      <tr>
        <td>{{ text_average_balance }}:</td>
        <td class="text-right">{{ analysis.average_balance }}</td>
      </tr>
      <tr>
        <td>{{ text_minimum_balance }}:</td>
        <td class="text-right">{{ analysis.minimum_balance }}</td>
      </tr>
      <tr>
        <td>{{ text_maximum_balance }}:</td>
        <td class="text-right">{{ analysis.maximum_balance }}</td>
      </tr>
      <tr>
        <td>{{ text_largest_transaction }}:</td>
        <td class="text-right">{{ analysis.largest_transaction }}</td>
      </tr>
      <tr>
        <td>{{ text_smallest_transaction }}:</td>
        <td class="text-right">{{ analysis.smallest_transaction }}</td>
      </tr>
      <tr>
        <td>{{ text_volatility }}:</td>
        <td class="text-right">{{ analysis.volatility }}%</td>
      </tr>
    </table>
  </div>
  
  <div style="width: 48%;">
    <h4>{{ text_activity_analysis }}</h4>
    <table class="statement-table">
      <tr>
        <td>{{ text_active_days }}:</td>
        <td class="text-right">{{ analysis.active_days }}</td>
      </tr>
      <tr>
        <td>{{ text_inactive_days }}:</td>
        <td class="text-right">{{ analysis.inactive_days }}</td>
      </tr>
      <tr>
        <td>{{ text_activity_rate }}:</td>
        <td class="text-right">{{ analysis.activity_rate }}%</td>
      </tr>
    </table>
    
    {% if analysis.trend %}
    <h4>{{ text_trend_analysis }}</h4>
    <div style="padding: 10px; background-color: {% if analysis.trend == 'up' %}#d4edda{% elseif analysis.trend == 'down' %}#f8d7da{% else %}#d1ecf1{% endif %}; border: 1px solid {% if analysis.trend == 'up' %}#c3e6cb{% elseif analysis.trend == 'down' %}#f5c6cb{% else %}#bee5eb{% endif %};">
      {% if analysis.trend == 'up' %}
        ↗ {{ text_improvement }}
      {% elseif analysis.trend == 'down' %}
        ↘ {{ text_deterioration }}
      {% else %}
        → {{ text_stable }}
      {% endif %}
    </div>
    {% endif %}
  </div>
</div>
{% endif %}

<!-- Period Comparison (if included) -->
{% if comparison and include_comparison %}
<h3>{{ text_period_comparison }}</h3>
<table class="statement-table">
  <thead>
    <tr>
      <th>{{ text_metric }}</th>
      <th class="text-right">{{ text_previous_period }}</th>
      <th class="text-right">{{ text_current_period }}</th>
      <th class="text-right">{{ text_variance }}</th>
      <th class="text-right">{{ text_variance_percentage }}</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>{{ text_opening_balance }}</td>
      <td class="text-right">{{ comparison.previous.opening_balance }}</td>
      <td class="text-right">{{ comparison.current.opening_balance }}</td>
      <td class="text-right{% if comparison.variance.opening_balance >= 0 %} text-success{% else %} text-danger{% endif %}">
        {{ comparison.variance.opening_balance }}
      </td>
      <td class="text-right{% if comparison.variance_percentage.opening_balance >= 0 %} text-success{% else %} text-danger{% endif %}">
        {{ comparison.variance_percentage.opening_balance }}%
      </td>
    </tr>
    <tr>
      <td>{{ text_closing_balance }}</td>
      <td class="text-right">{{ comparison.previous.closing_balance }}</td>
      <td class="text-right">{{ comparison.current.closing_balance }}</td>
      <td class="text-right{% if comparison.variance.closing_balance >= 0 %} text-success{% else %} text-danger{% endif %}">
        {{ comparison.variance.closing_balance }}
      </td>
      <td class="text-right{% if comparison.variance_percentage.closing_balance >= 0 %} text-success{% else %} text-danger{% endif %}">
        {{ comparison.variance_percentage.closing_balance }}%
      </td>
    </tr>
    <tr>
      <td>{{ text_transaction_count }}</td>
      <td class="text-right">{{ comparison.previous.transaction_count }}</td>
      <td class="text-right">{{ comparison.current.transaction_count }}</td>
      <td class="text-right{% if comparison.variance.transaction_count >= 0 %} text-success{% else %} text-danger{% endif %}">
        {{ comparison.variance.transaction_count }}
      </td>
      <td class="text-right{% if comparison.variance_percentage.transaction_count >= 0 %} text-success{% else %} text-danger{% endif %}">
        {{ comparison.variance_percentage.transaction_count }}%
      </td>
    </tr>
  </tbody>
</table>
{% endif %}

<!-- Signatures -->
<div class="signature-section">
  <div class="signature-box">
    <div class="signature-line">{{ text_prepared_by }}</div>
  </div>
  <div class="signature-box">
    <div class="signature-line">{{ text_reviewed_by }}</div>
  </div>
  <div class="signature-box">
    <div class="signature-line">{{ text_approved_by }}</div>
  </div>
</div>

<!-- Footer -->
<div class="footer">
  <div style="display: flex; justify-content: space-between;">
    <div>
      {{ text_confidential }} - {{ text_internal_use }}
    </div>
    <div>
      {{ text_eas_compliant }} | {{ text_eta_ready }}
    </div>
    <div>
      {{ text_page }} 1 {{ text_of }} 1
    </div>
  </div>
</div>

<script>
// Auto print when page loads
window.onload = function() {
  window.print();
};
</script>

</body>
</html>
