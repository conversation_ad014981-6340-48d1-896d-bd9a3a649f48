{"name": "cardinity/cardinity-sdk-php", "description": "Client library for Cardinity credit card processing API", "type": "library", "license": "MIT", "homepage": "https://cardinity.com", "require": {"php": ">=5.5.9", "guzzlehttp/guzzle": "^6.2.1", "guzzlehttp/oauth-subscriber": "0.3.*", "symfony/validator": "~3.0 || ~4.0"}, "require-dev": {"monolog/monolog": "~1.0", "phpspec/phpspec": "~2.1", "phpunit/phpunit": "~4.3"}, "config": {"bin-dir": "bin"}, "autoload": {"psr-4": {"Cardinity\\": "src"}}, "autoload-dev": {"psr-4": {"Cardinity\\Tests\\": "tests/"}}}