/**
 * Workflow Designer Styles
 */

/* Workflow Container */
.workflow-container {
    position: relative;
    width: 100%;
    height: 100%;
    min-height: 600px;
    overflow: hidden;
    border: 1px solid #ddd;
    background: #f5f5f5;
    border-radius: 4px;
}

/* Workflow Canvas */
.workflow-canvas {
    position: relative;
    width: 4000px;
    height: 3000px;
    background-color: #fff;
    background-image: linear-gradient(#eee 1px, transparent 1px),
                      linear-gradient(90deg, #eee 1px, transparent 1px);
    background-size: 20px 20px;
    transform-origin: 0 0;
    transition: transform 0.3s;
}

/* Workflow Toolbar */
.workflow-toolbar {
    display: flex;
    background: #f8f8f8;
    border-bottom: 1px solid #ddd;
    padding: 10px;
    gap: 5px;
}

.workflow-toolbar button {
    margin-right: 5px;
}

/* Workflow Sidebar */
.workflow-sidebar {
    position: absolute;
    left: 0;
    top: 50px;
    width: 200px;
    background: #f8f8f8;
    border-right: 1px solid #ddd;
    height: calc(100% - 50px);
    padding: 10px;
    overflow-y: auto;
}

.workflow-sidebar h4 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 14px;
    font-weight: bold;
}

/* Node Palette */
.workflow-node-palette {
    margin-bottom: 20px;
}

.workflow-palette-node {
    padding: 8px;
    margin-bottom: 8px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: move;
    text-align: center;
    transition: all 0.2s;
}

.workflow-palette-node:hover {
    background: #f0f0f0;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

/* Workflow Nodes */
.workflow-node {
    position: absolute;
    width: 120px;
    min-height: 60px;
    background: #fff;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    z-index: 10;
    cursor: move;
}

.workflow-node.selected {
    border: 2px solid #428bca;
    box-shadow: 0 0 8px rgba(66, 139, 202, 0.6);
}

.workflow-node-title {
    padding: 8px;
    background: #f5f5f5;
    border-bottom: 1px solid #ddd;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    font-weight: bold;
    text-align: center;
    cursor: move;
}

.workflow-node-connectors {
    padding: 8px;
    display: flex;
    justify-content: space-between;
}

.workflow-node-connector {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #555;
    cursor: pointer;
    transition: background 0.2s;
}

.workflow-node-connector:hover {
    background: #428bca;
}

.workflow-node-input {
    margin-right: auto;
}

.workflow-node-output {
    margin-left: auto;
}

/* Node Types */
.workflow-node-start {
    border-color: #5cb85c;
}

.workflow-node-start .workflow-node-title {
    background: #5cb85c;
    color: #fff;
    border-color: #4cae4c;
}

.workflow-node-end {
    border-color: #d9534f;
}

.workflow-node-end .workflow-node-title {
    background: #d9534f;
    color: #fff;
    border-color: #d43f3a;
}

.workflow-node-task {
    border-color: #428bca;
}

.workflow-node-task .workflow-node-title {
    background: #428bca;
    color: #fff;
    border-color: #357ebd;
}

.workflow-node-decision {
    border-color: #f0ad4e;
}

.workflow-node-decision .workflow-node-title {
    background: #f0ad4e;
    color: #fff;
    border-color: #eea236;
}

/* Connections */
.workflow-connector-line {
    position: absolute;
    height: 2px;
    background: #666;
    z-index: 5;
    pointer-events: none;
}

.workflow-connector-permanent {
    background: #428bca;
}

/* Property Panel */
.workflow-property-panel {
    position: absolute;
    right: 0;
    top: 50px;
    width: 300px;
    background: #f8f8f8;
    border-left: 1px solid #ddd;
    height: calc(100% - 50px);
    padding: 15px;
    display: none;
    overflow-y: auto;
}

.property-panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.property-panel-title {
    font-size: 16px;
    font-weight: bold;
    margin: 0;
}

.property-panel-close {
    cursor: pointer;
    font-size: 18px;
}

.property-field {
    margin-bottom: 15px;
}

.property-field label {
    display: block;
    font-weight: bold;
    margin-bottom: 5px;
}

.workflow-panel-footer {
    margin-top: 20px;
    text-align: right;
}

/* Workflow Main Area */
.workflow-main {
    position: absolute;
    left: 200px;
    top: 50px;
    width: calc(100% - 500px);
    height: calc(100% - 50px);
    overflow: auto;
}

/* Responsive adjustments */
@media (max-width: 992px) {
    .workflow-property-panel {
        width: 240px;
    }
    
    .workflow-main {
        width: calc(100% - 440px);
    }
}

@media (max-width: 768px) {
    .workflow-sidebar {
        width: 160px;
    }
    
    .workflow-main {
        left: 160px;
        width: calc(100% - 400px);
    }
}

/* Zoom controls */
.workflow-zoom-controls {
    position: absolute;
    bottom: 20px;
    right: 320px;
    z-index: 100;
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 5px;
}

.workflow-zoom-controls button {
    margin: 0 3px;
}

/* Loading indicator */
.workflow-loading {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.workflow-loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #3498db;
    border-radius: 50%;
    animation: spin 2s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Tooltip */
.workflow-tooltip {
    position: absolute;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 100;
    pointer-events: none;
}

/* Node status indicators */
.workflow-node-status {
    position: absolute;
    top: -5px;
    right: -5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid white;
}

.workflow-node-status-complete {
    background-color: #5cb85c;
}

.workflow-node-status-active {
    background-color: #428bca;
}

.workflow-node-status-error {
    background-color: #d9534f;
}

/* Mini-map */
.workflow-minimap {
    position: absolute;
    bottom: 20px;
    left: 220px;
    width: 200px;
    height: 150px;
    border: 1px solid #ddd;
    background: white;
    z-index: 50;
    overflow: hidden;
}

.workflow-minimap-content {
    transform-origin: 0 0;
    position: absolute;
} 