{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <a href="{{ add }}" data-toggle="tooltip" title="{{ button_add }}" class="btn btn-primary"><i class="fa fa-plus"></i></a>
        <button type="button" data-toggle="tooltip" title="{{ button_delete }}" class="btn btn-danger" onclick="confirm('{{ text_confirm }}') ? $('#form-partner').submit() : false;"><i class="fa fa-trash-o"></i></button>
      </div>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    {% if success %}
    <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-list"></i> {{ text_list }}</h3>
      </div>
      <div class="panel-body">
        <form action="{{ delete }}" method="post" enctype="multipart/form-data" id="form-partner">
          <div class="table-responsive">
            <table class="table table-bordered table-hover">
              <thead>
                <tr>
                  <td style="width: 1px;" class="text-center"><input type="checkbox" onclick="$('input[name*=\'selected\']').prop('checked', this.checked);" /></td>
                  <td class="text-left">{{ column_name }}</td>
                  <td class="text-left">{{ column_type }}</td>
                  <td class="text-right">{{ column_percentage }}</td>
                  <td class="text-left">{{ column_account_number }}</td>
                  <td class="text-right">{{ column_current_balance }}</td>
                  <td class="text-left">{{ column_status }}</td>
                  <td class="text-left">{{ column_date_added }}</td>
                  <td class="text-right">{{ column_action }}</td>
                </tr>
              </thead>
              <tbody>
                {% if partners %}
                {% for partner in partners %}
                <tr>
                  <td class="text-center">
                    {% if partner.partner_id in selected %}
                    <input type="checkbox" name="selected[]" value="{{ partner.partner_id }}" checked="checked" />
                    {% else %}
                    <input type="checkbox" name="selected[]" value="{{ partner.partner_id }}" />
                    {% endif %}
                  </td>
                  <td class="text-left">{{ partner.name }}</td>
                  <td class="text-left">{{ partner.type }}</td>
                  <td class="text-right">{{ partner.percentage }}%</td>
                  <td class="text-left">{{ partner.account_number }}</td>
                  <td class="text-right">{{ partner.current_balance }}</td>
                  <td class="text-left">{{ partner.status }}</td>
                  <td class="text-left">{{ partner.date_added }}</td>
                  <td class="text-right">
                    <a href="{{ partner.edit }}" data-toggle="tooltip" title="{{ button_edit }}" class="btn btn-primary"><i class="fa fa-pencil"></i></a>
                  </td>
                </tr>
                {% endfor %}
                {% else %}
                <tr>
                  <td class="text-center" colspan="9">{{ text_no_results }}</td>
                </tr>
                {% endif %}
              </tbody>
            </table>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
{{ footer }}