{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="submit" form="form-tax-class" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fa fa-save"></i></button>
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a></div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid"> {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pencil"></i> {{ text_form }}</h3>
      </div>
      <div class="panel-body">
        <form action="{{ action }}" method="post" enctype="multipart/form-data" class="form-horizontal" id="form-tax-class">
          <fieldset>
            <legend>{{ text_tax_class }}</legend>
            <div class="form-group required">
              <label class="col-sm-2 control-label" for="input-title">{{ entry_title }}</label>
              <div class="col-sm-10">
                <input type="text" name="title" value="{{ title }}" placeholder="{{ entry_title }}" id="input-title" class="form-control" />
                {% if error_title %}
                <div class="text-danger">{{ error_title }}</div>
                {% endif %} </div>
            </div>
            <div class="form-group required">
              <label class="col-sm-2 control-label" for="input-description">{{ entry_description }}</label>
              <div class="col-sm-10">
                <input type="text" name="description" value="{{ description }}" placeholder="{{ entry_description }}" id="input-description" class="form-control" />
                {% if error_description %}
                <div class="text-danger">{{ error_description }}</div>
                {% endif %} </div>
            </div>
          </fieldset>
          <fieldset>
            <legend>{{ text_tax_rate }}</legend>
            <table id="tax-rule" class="table table-striped table-bordered table-hover">
              <thead>
                <tr>
                  <td class="text-left">{{ entry_rate }}</td>
                  <td class="text-left">{{ entry_based }}</td>
                  <td class="text-left">{{ entry_priority }}</td>
                  <td></td>
                </tr>
              </thead>
              <tbody>
              
              {% set tax_rule_row = 0 %}
              {% for tax_rule in tax_rules %}
              <tr id="tax-rule-row{{ tax_rule_row }}">
                <td class="text-left"><select name="tax_rule[{{ tax_rule_row }}][tax_rate_id]" class="form-control">
                    {% for tax_rate in tax_rates %}
                    {% if tax_rate.tax_rate_id == tax_rule.tax_rate_id %}
                    <option value="{{ tax_rate.tax_rate_id }}" selected="selected">{{ tax_rate.name }}</option>
                    {% else %}
                    <option value="{{ tax_rate.tax_rate_id }}">{{ tax_rate.name }}</option>
                    {% endif %}
                    {% endfor %}
                  </select></td>
                <td class="text-left"><select name="tax_rule[{{ tax_rule_row }}][based]" class="form-control">
                    {% if tax_rule.based == 'shipping' %}
                    <option value="shipping" selected="selected">{{ text_shipping }}</option>
                    {% else %}
                    <option value="shipping">{{ text_shipping }}</option>
                    {% endif %}
                     {% if tax_rule.based == 'payment' %}
                    <option value="payment" selected="selected">{{ text_payment }}</option>
                    {% else %}
                    <option value="payment">{{ text_payment }}</option>
                    {% endif %}
                    {% if tax_rule.based == 'store' %}
                    <option value="store" selected="selected">{{ text_store }}</option>
                    {% else %}
                    <option value="store">{{ text_store }}</option>
                    {% endif %}
                  </select></td>
                <td class="text-left"><input type="text" name="tax_rule[{{ tax_rule_row }}][priority]" value="{{ tax_rule.priority }}" placeholder="{{ entry_priority }}" class="form-control" /></td>
                <td class="text-left"><button type="button" onclick="$('#tax-rule-row{{ tax_rule_row }}').remove();" data-toggle="tooltip" title="{{ button_remove }}" class="btn btn-danger"><i class="fa fa-minus-circle"></i></button></td>
              </tr>
              {% set tax_rule_row = tax_rule_row + 1 %}
              {% endfor %}
                </tbody>
              
              <tfoot>
                <tr>
                  <td colspan="3"></td>
                  <td class="text-left"><button type="button" onclick="addRule();" data-toggle="tooltip" title="{{ button_rule_add }}" class="btn btn-primary"><i class="fa fa-plus-circle"></i></button></td>
                </tr>
              </tfoot>
	      
            </table>
          </fieldset>
        </form>
      </div>
    </div>
  </div>
  <script type="text/javascript"><!--
var tax_rule_row = {{ tax_rule_row }};

function addRule() {
	html  = '<tr id="tax-rule-row' + tax_rule_row + '">';
	html += '  <td class="text-left"><select name="tax_rule[' + tax_rule_row + '][tax_rate_id]" class="form-control">';
    {% for tax_rate in tax_rates %}
    html += '    <option value="{{ tax_rate.tax_rate_id }}">{{ tax_rate.name|escape('js') }}</option>';
    {% endfor %}
    html += '  </select></td>';
	html += '  <td class="text-left"><select name="tax_rule[' + tax_rule_row + '][based]" class="form-control">';
    html += '    <option value="shipping">{{ text_shipping }}</option>';
    html += '    <option value="payment">{{ text_payment }}</option>';
    html += '    <option value="store">{{ text_store }}</option>';
    html += '  </select></td>';
	html += '  <td class="text-left"><input type="text" name="tax_rule[' + tax_rule_row + '][priority]" value="" placeholder="{{ entry_priority }}" class="form-control" /></td>';
	html += '  <td class="text-left"><button type="button" onclick="$(\'#tax-rule-row' + tax_rule_row + '\').remove();" data-toggle="tooltip" title="{{ button_remove }}" class="btn btn-danger"><i class="fa fa-minus-circle"></i></button></td>';
	html += '</tr>';
	
	$('#tax-rule tbody').append(html);
	
	tax_rule_row++;
}
//--></script></div>
{{ footer }}
