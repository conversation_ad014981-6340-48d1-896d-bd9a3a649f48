<?php
// Heading
$_['heading_title']              = 'Account Statement';

// Text
$_['text_list']                  = 'Account List';
$_['text_form']                  = 'Account Statement';
$_['text_account_statement']     = 'Account Statement';
$_['text_select_account']        = 'Select Account';
$_['text_date_range']            = 'Date Range';
$_['text_opening_balance']       = 'Opening Balance';
$_['text_closing_balance']       = 'Closing Balance';
$_['text_debit']                 = 'Debit';
$_['text_credit']                = 'Credit';
$_['text_balance']               = 'Balance';
$_['text_total_debit']           = 'Total Debit';
$_['text_total_credit']          = 'Total Credit';
$_['text_no_transactions']       = 'No transactions in this period';
$_['text_print']                 = 'Print';
$_['text_export']                = 'Export';
$_['text_pdf']                   = 'PDF';
$_['text_excel']                 = 'Excel';
$_['text_actions']               = 'Actions';
$_['text_view_statement']        = 'View Statement';
$_['text_export_options']        = 'Export Options';
$_['text_filter']                = 'Filter';
$_['text_reset']                 = 'Reset';
$_['text_search']                = 'Search';
$_['text_all_accounts']          = 'All Accounts';
$_['text_account_details']       = 'Account Details';
$_['text_transaction_details']   = 'Transaction Details';
$_['text_running_balance']       = 'Running Balance';
$_['text_period_summary']        = 'Period Summary';
$_['text_account_activity']      = 'Account Activity';
$_['text_statement_summary']     = 'Statement Summary';

// Column
$_['column_date']                = 'Date';
$_['column_reference']           = 'Reference';
$_['column_description']         = 'Description';
$_['column_debit']               = 'Debit';
$_['column_credit']              = 'Credit';
$_['column_balance']             = 'Balance';
$_['column_account_code']        = 'Account Code';
$_['column_account_name']        = 'Account Name';
$_['column_account_type']        = 'Account Type';
$_['column_journal']             = 'Journal';
$_['column_voucher']             = 'Voucher No.';
$_['column_transaction_type']    = 'Transaction Type';
$_['column_running_balance']     = 'Running Balance';

// Entry
$_['entry_account']              = 'Account';
$_['entry_date_start']           = 'Date Start';
$_['entry_date_end']             = 'Date End';
$_['entry_account_code']         = 'Account Code';
$_['entry_account_name']         = 'Account Name';
$_['entry_include_opening']      = 'Include Opening Balance';
$_['entry_include_closing']      = 'Include Closing Balance';
$_['entry_show_zero_balance']    = 'Show Zero Balance Accounts';
$_['entry_group_by']             = 'Group By';
$_['entry_sort_by']              = 'Sort By';

// Button
$_['button_view']                = 'View';
$_['button_export']              = 'Export';
$_['button_print']               = 'Print';
$_['button_filter']              = 'Filter';
$_['button_reset']               = 'Reset';
$_['button_search']              = 'Search';
$_['button_generate']            = 'Generate';
$_['button_download']            = 'Download';
$_['button_email']               = 'Email';
$_['button_save']                = 'Save';
$_['button_cancel']              = 'Cancel';

// Error
$_['error_permission']           = 'Warning: You do not have permission to access account statements!';
$_['error_account']              = 'Account is required!';
$_['error_date_start']           = 'Date Start is required!';
$_['error_date_end']             = 'Date End is required!';
$_['error_date_range']           = 'Date End must be after Date Start!';
$_['error_no_data']              = 'No data found for the specified period!';
$_['error_export']               = 'Failed to export account statement!';
$_['error_invalid_account']      = 'Invalid account selected!';
$_['error_access_denied']        = 'Access denied!';

// Success
$_['success_export']             = 'Account statement exported successfully!';
$_['success_email']              = 'Account statement emailed successfully!';

// Help
$_['help_account']               = 'Select the account for which you want to view the statement';
$_['help_date_range']            = 'Specify the time period for the account statement';
$_['help_opening_balance']       = 'Balance at the beginning of the period';
$_['help_closing_balance']       = 'Balance at the end of the period';
$_['help_running_balance']       = 'Cumulative balance after each transaction';

// Controller language variables
$_['log_unauthorized_access']    = 'Unauthorized access attempt to account statements';
$_['log_view_screen']            = 'View account statements screen';
$_['log_unauthorized_export']    = 'Unauthorized export attempt for account statements';
$_['log_export']                 = 'Export account statement';

// Additional template variables
$_['text_statement_filters']     = 'Statement Filters';
$_['text_account_selection']     = 'Account Selection';
$_['text_date_selection']        = 'Date Selection';
$_['text_display_options']       = 'Display Options';
$_['text_statement_options']     = 'Statement Options';
$_['text_generate_statement']    = 'Generate Statement';
$_['text_statement_generated']   = 'Statement Generated';
$_['text_no_statement']          = 'No Statement';
$_['button_generate_statement']  = 'Generate Statement';
$_['error_invalid_date_range']   = 'Invalid date range';
$_['success_statement_generated'] = 'Statement generated successfully';
$_['error_statement_generation'] = 'Failed to generate statement';
$_['text_exporting']             = 'Exporting';
$_['text_loading']               = 'Loading';
$_['text_processing']            = 'Processing';

// Enhanced performance and analytics variables
$_['text_optimized_statement']         = 'Optimized Account Statement';
$_['text_statement_analysis']          = 'Statement Analysis';
$_['text_cache_enabled']               = 'Cache Enabled';
$_['text_account_analysis']            = 'Account Analysis';
$_['text_movements_analysis']          = 'Movements Analysis';
$_['text_total_transactions']          = 'Total Transactions';
$_['text_debit_count']                 = 'Debit Count';
$_['text_credit_count']                = 'Credit Count';
$_['text_avg_transaction']             = 'Average Transaction';
$_['text_max_transaction']             = 'Maximum Transaction';
$_['text_min_transaction']             = 'Minimum Transaction';
$_['text_monthly_distribution']        = 'Monthly Distribution';
$_['button_statement_analysis']        = 'Statement Analysis';
$_['text_loading_analysis']            = 'Loading statement analysis...';
$_['text_analysis_ready']              = 'Analysis ready';

// Enhanced performance and analytics variables for statement account
$_['text_enhanced_analysis']           = 'Enhanced Analysis';
$_['text_transaction_patterns']        = 'Transaction Patterns';
$_['text_extremes']                    = 'Extremes';
$_['text_smart_search']                = 'Smart Search';
$_['text_day_of_week']                 = 'Day of Week';
$_['text_hour_of_day']                 = 'Hour of Day';
$_['text_first_transaction_date']      = 'First Transaction Date';
$_['text_last_transaction_date']       = 'Last Transaction Date';
$_['text_statement_cache']             = 'Statement Cache';
$_['text_loading_statement_analysis']  = 'Loading enhanced statement analysis...';
$_['text_statement_analysis_ready']    = 'Statement analysis ready';
?>
