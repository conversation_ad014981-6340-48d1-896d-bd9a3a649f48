# Design Document - AYM ERP System Completion

## Overview

The AYM ERP system is a comprehensive enterprise resource planning solution designed specifically for Egyptian businesses. The system integrates inventory management, e-commerce, accounting, and multi-branch operations into a unified platform. The architecture follows enterprise-grade patterns with central services, queue management, and real-time synchronization across all modules.

The system currently manages 451 database tables with sophisticated relationships between inventory, catalog, accounting, and operational modules. The design emphasizes performance, scalability, and user experience while maintaining full Arabic language support and compliance with Egyptian business requirements including ETA integration.

## Architecture

### System Architecture Overview

```mermaid
graph TB
    subgraph "Presentation Layer"
        UI[Web Interface]
        API[REST API]
        Mobile[Mobile PWA]
    end
    
    subgraph "Application Layer"
        Controllers[Controllers]
        Services[Central Services]
        Queue[Queue Manager]
    end
    
    subgraph "Business Logic Layer"
        Models[Business Models]
        WAC[WAC Calculator]
        Pricing[Pricing Engine]
        Inventory[Inventory Manager]
    end
    
    subgraph "Data Layer"
        DB[(Database 451 Tables)]
        Cache[Redis Cache]
        Files[File Storage]
    end
    
    subgraph "External Integrations"
        ETA[ETA System]
        Payment[Payment Gateways]
        Shipping[Shipping APIs]
    end
    
    UI --> Controllers
    API --> Controllers
    Mobile --> Controllers
    Controllers --> Services
    Controllers --> Models
    Services --> Queue
    Models --> WAC
    Models --> Pricing
    Models --> Inventory
    Models --> DB
    Models --> Cache
    Queue --> DB
    Controllers --> ETA
    Controllers --> Payment
    Controllers --> Shipping
```

### Central Services Architecture

The system implements a central services pattern where all modules integrate with core services:

```mermaid
graph LR
    subgraph "Central Services"
        CSM[Central Service Manager]
        AL[Activity Log]
        UN[Unified Notification]
        JE[Journal Entry Service]
        QM[Queue Manager]
        WC[WAC Calculator]
    end
    
    subgraph "Modules"
        INV[Inventory]
        CAT[Catalog]
        ACC[Accounting]
        SALES[Sales]
        PUR[Purchasing]
    end
    
    INV --> CSM
    CAT --> CSM
    ACC --> CSM
    SALES --> CSM
    PUR --> CSM
    
    CSM --> AL
    CSM --> UN
    CSM --> JE
    CSM --> QM
    CSM --> WC
```

### Database Architecture

The 451-table database is organized into logical domains:

- **Core Tables (50+)**: User management, permissions, settings
- **Inventory Tables (80+)**: Products, stock, movements, valuations
- **Catalog Tables (60+)**: E-commerce products, categories, pricing
- **Accounting Tables (70+)**: Chart of accounts, journal entries, reconciliation
- **Sales/Purchase Tables (40+)**: Orders, invoices, payments
- **ETA Integration Tables (30+)**: Tax compliance, electronic invoicing
- **Analytics Tables (40+)**: Reports, KPIs, business intelligence
- **System Tables (81+)**: Logs, queues, workflows, configurations

## Components and Interfaces

### Inventory Management Component

**Purpose**: Manages physical inventory across multiple branches with real-time tracking and WAC calculations.

**Key Classes**:
- `InventoryManager`: Core inventory operations
- `StockMovementTracker`: Tracks all inventory movements
- `WACCalculator`: Calculates weighted average costs
- `MultiUnitConverter`: Handles unit conversions
- `StockReservationManager`: Manages inventory reservations

**Interfaces**:
```php
interface InventoryManagerInterface {
    public function updateStock($product_id, $quantity, $unit_id, $branch_id);
    public function reserveStock($product_id, $quantity, $unit_id, $order_id);
    public function transferStock($from_branch, $to_branch, $product_id, $quantity);
    public function calculateWAC($product_id, $branch_id);
    public function getAvailableStock($product_id, $unit_id, $branch_id);
}
```

### E-commerce Integration Component

**Purpose**: Manages online catalog, orders, and customer interactions with real-time inventory synchronization.

**Key Classes**:
- `CatalogManager`: Manages product catalog for e-commerce
- `OrderProcessor`: Processes online orders
- `PricingEngine`: Handles dynamic pricing and promotions
- `ProductRecommendationEngine`: AI-powered product recommendations
- `CartManager`: Manages shopping cart operations

**Interfaces**:
```php
interface CatalogManagerInterface {
    public function syncProductFromInventory($product_id);
    public function updateProductVisibility($product_id, $visible);
    public function manageBundles($bundle_id, $products);
    public function applyDynamicPricing($product_id, $customer_group);
    public function getProductRecommendations($customer_id, $product_id);
}
```

### ProductsPro Multi-Unit System

**Purpose**: Advanced product management with unlimited units, complex pricing, and conversion factors.

**Key Features**:
- Base unit + unlimited additional units
- Automatic conversion between units
- Unit-specific pricing and barcodes
- Weight and volume factors
- Purchase/sale unit restrictions

**Data Structure**:
```php
class ProductUnit {
    public $unit_id;
    public $product_id;
    public $unit_name;
    public $conversion_factor;
    public $price_factor;
    public $barcode;
    public $is_sellable;
    public $is_purchasable;
    public $weight_factor;
    public $volume_factor;
}
```

### Central Services Component

**Purpose**: Provides shared services across all modules for consistency and enterprise patterns.

**Services**:

1. **Central Service Manager**
   - Initializes all central services
   - Manages service dependencies
   - Provides unified access point

2. **Activity Log Service**
   - Logs all user activities
   - Tracks sensitive operations
   - Provides audit trails

3. **Unified Notification Service**
   - Sends notifications across channels
   - Manages notification preferences
   - Handles real-time updates

4. **Journal Entry Service**
   - Creates automatic accounting entries
   - Manages chart of accounts integration
   - Handles multi-currency transactions

5. **Queue Manager**
   - Processes background tasks
   - Manages job priorities
   - Handles retry logic

6. **WAC Calculator Service**
   - Calculates weighted average costs
   - Updates inventory valuations
   - Handles multi-branch calculations

### Permission System Component

**Purpose**: Implements dual-level permission system for security and access control.

**Permission Levels**:
- `hasPermission()`: Basic CRUD permissions (view, add, edit, delete)
- `hasKey()`: Advanced permissions (approve, export, advanced settings)

**Implementation**:
```php
class PermissionManager {
    public function hasPermission($user_id, $permission_key) {
        // Check basic permissions
    }
    
    public function hasKey($user_id, $key_name) {
        // Check advanced permissions
    }
    
    public function checkBranchAccess($user_id, $branch_id) {
        // Check branch-specific access
    }
}
```

## Data Models

### Core Product Model

```php
class Product {
    // Basic Information
    public $product_id;
    public $sku;
    public $name;
    public $description;
    public $category_id;
    public $manufacturer_id;
    public $status;
    
    // Inventory Information
    public $track_quantity;
    public $minimum_quantity;
    public $maximum_quantity;
    public $reorder_level;
    
    // Pricing Information
    public $base_price;
    public $cost_price;
    public $special_price;
    public $wholesale_price;
    public $half_wholesale_price;
    
    // Multi-Unit Support
    public $base_unit_id;
    public $units; // Array of ProductUnit objects
    
    // E-commerce Information
    public $meta_title;
    public $meta_description;
    public $meta_keywords;
    public $images; // Array of product images
    
    // Relationships
    public $bundles; // Array of bundle configurations
    public $options; // Array of product options
    public $attributes; // Array of product attributes
}
```

### Inventory Movement Model

```php
class InventoryMovement {
    public $movement_id;
    public $product_id;
    public $unit_id;
    public $branch_id;
    public $movement_type; // in, out, transfer, adjustment
    public $quantity;
    public $cost_per_unit;
    public $total_cost;
    public $reference_type; // sale, purchase, transfer, adjustment
    public $reference_id;
    public $user_id;
    public $date_created;
    public $notes;
    
    // WAC Calculation Fields
    public $old_average_cost;
    public $new_average_cost;
    public $old_quantity;
    public $new_quantity;
}
```

### Order Model

```php
class Order {
    public $order_id;
    public $customer_id;
    public $order_status_id;
    public $branch_id;
    public $currency_id;
    public $currency_code;
    public $currency_value;
    
    // Totals
    public $total;
    public $subtotal;
    public $tax;
    public $shipping;
    public $discount;
    
    // Order Items
    public $products; // Array of OrderProduct objects
    
    // Dates
    public $date_added;
    public $date_modified;
    public $date_shipped;
    
    // Integration
    public $eta_invoice_id;
    public $payment_status;
    public $shipping_status;
}
```

### WAC Calculation Model

```php
class WACCalculation {
    public $calculation_id;
    public $product_id;
    public $unit_id;
    public $branch_id;
    public $old_quantity;
    public $new_quantity;
    public $old_average_cost;
    public $new_average_cost;
    public $movement_cost;
    public $movement_quantity;
    public $calculation_date;
    public $user_id;
    public $movement_id;
}
```

## Error Handling

### Error Classification

1. **System Errors**: Database connectivity, service unavailability
2. **Business Logic Errors**: Insufficient stock, invalid pricing
3. **Validation Errors**: Invalid input data, missing required fields
4. **Permission Errors**: Unauthorized access, insufficient privileges
5. **Integration Errors**: ETA API failures, payment gateway issues

### Error Handling Strategy

```php
class ErrorHandler {
    public function handleError($error_type, $error_message, $context = []) {
        // Log error with context
        $this->logError($error_type, $error_message, $context);
        
        // Send notifications if critical
        if ($this->isCriticalError($error_type)) {
            $this->notifyAdministrators($error_message, $context);
        }
        
        // Return user-friendly error message
        return $this->getUserFriendlyMessage($error_type, $error_message);
    }
    
    private function logError($type, $message, $context) {
        // Log to activity log service
        $this->central_service->getActivityLog()->logError([
            'error_type' => $type,
            'message' => $message,
            'context' => $context,
            'user_id' => $this->user->getId(),
            'ip_address' => $this->request->server['REMOTE_ADDR'],
            'user_agent' => $this->request->server['HTTP_USER_AGENT'],
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }
}
```

### Error Response Format

```json
{
    "success": false,
    "error": {
        "code": "INSUFFICIENT_STOCK",
        "message": "الكمية المطلوبة غير متوفرة في المخزون",
        "details": {
            "product_id": 123,
            "requested_quantity": 10,
            "available_quantity": 5,
            "unit": "قطعة"
        }
    },
    "timestamp": "2025-01-27T10:30:00Z"
}
```

## Testing Strategy

### Testing Levels

1. **Unit Testing**
   - Test individual classes and methods
   - Mock external dependencies
   - Validate business logic

2. **Integration Testing**
   - Test module interactions
   - Validate database operations
   - Test API endpoints

3. **System Testing**
   - End-to-end workflow testing
   - Performance testing
   - Security testing

4. **User Acceptance Testing**
   - Business scenario testing
   - User interface testing
   - Accessibility testing

### Test Implementation

```php
class InventoryManagerTest extends PHPUnit\Framework\TestCase {
    private $inventory_manager;
    private $mock_database;
    
    public function setUp(): void {
        $this->mock_database = $this->createMock(Database::class);
        $this->inventory_manager = new InventoryManager($this->mock_database);
    }
    
    public function testUpdateStockIncreasesQuantity() {
        // Arrange
        $product_id = 1;
        $quantity = 10;
        $unit_id = 1;
        $branch_id = 1;
        
        // Act
        $result = $this->inventory_manager->updateStock(
            $product_id, $quantity, $unit_id, $branch_id
        );
        
        // Assert
        $this->assertTrue($result);
        $this->assertEquals(10, $this->inventory_manager->getStock($product_id, $unit_id, $branch_id));
    }
}
```

### Performance Testing

- **Load Testing**: Simulate 50,000+ concurrent users
- **Stress Testing**: Test system limits and failure points
- **Volume Testing**: Test with large datasets (1M+ products)
- **Endurance Testing**: Long-running operations stability

### Security Testing

- **Authentication Testing**: Login mechanisms and session management
- **Authorization Testing**: Permission system validation
- **Input Validation Testing**: SQL injection and XSS prevention
- **Data Encryption Testing**: Sensitive data protection

### Test Automation

```yaml
# CI/CD Pipeline Configuration
stages:
  - unit_tests
  - integration_tests
  - security_tests
  - performance_tests
  - deployment

unit_tests:
  script:
    - php vendor/bin/phpunit tests/unit/
  coverage: '/Lines:\s+(\d+\.\d+\%)/'

integration_tests:
  script:
    - php vendor/bin/phpunit tests/integration/
  services:
    - mysql:8.0
    - redis:6.0

performance_tests:
  script:
    - artillery run performance-tests.yml
  only:
    - main
```

This design provides a comprehensive foundation for completing the AYM ERP system with enterprise-grade quality, focusing on scalability, maintainability, and user experience while meeting all the specified requirements.