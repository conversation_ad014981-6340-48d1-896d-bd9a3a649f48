<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/expr/v1beta1/eval.proto

namespace Google\Api\Expr\V1beta1;

if (false) {
    /**
     * This class is deprecated. Use Google\Api\Expr\V1beta1\EvalState\Result instead.
     * @deprecated
     */
    class EvalState_Result {}
}
class_exists(EvalState\Result::class);
@trigger_error('Google\Api\Expr\V1beta1\EvalState_Result is deprecated and will be removed in the next major release. Use Google\Api\Expr\V1beta1\EvalState\Result instead', E_USER_DEPRECATED);

