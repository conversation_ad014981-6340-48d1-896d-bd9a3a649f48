{{ header }}{{ column_left }}

<!-- Enterprise Grade Plus CSS for Income Statement -->
<style>
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --danger-color: #e74c3c;
    --warning-color: #f39c12;
    --info-color: #17a2b8;
    --light-bg: #f8f9fa;
    --border-color: #dee2e6;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

.income-statement-container {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: var(--shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.income-statement-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color), var(--success-color));
}

.income-statement-header {
    text-align: center;
    border-bottom: 3px solid var(--primary-color);
    padding-bottom: 20px;
    margin-bottom: 30px;
    position: relative;
}

.income-statement-header h2 {
    color: var(--primary-color);
    font-weight: 700;
    font-size: 1.8rem;
    margin: 0;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.income-statement-section {
    margin-bottom: 30px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    background: #ffffff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    transition: var(--transition);
}

.income-statement-section:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.section-header {
    background: linear-gradient(135deg, var(--secondary-color), #2980b9);
    color: white;
    padding: 15px 20px;
    font-weight: 600;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.section-header i {
    margin-right: 10px;
    font-size: 1.2rem;
}

.section-content {
    padding: 20px;
    background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
}

.account-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    border-bottom: 1px solid var(--border-color);
    border-radius: 4px;
    margin-bottom: 5px;
    transition: var(--transition);
    background: #ffffff;
}

.account-row:hover {
    background: var(--light-bg);
    transform: translateX(5px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.account-row:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.account-name {
    flex: 1;
    color: var(--primary-color);
    font-weight: 500;
    font-size: 0.95rem;
}

.account-amount {
    font-weight: 600;
    color: var(--success-color);
    min-width: 140px;
    text-align: {{ direction == 'rtl' ? 'right' : 'left' }};
    font-size: 1rem;
    font-family: 'Courier New', monospace;
}

.total-row {
    background: linear-gradient(135deg, var(--light-bg), #e9ecef);
    padding: 15px 20px;
    font-weight: 700;
    font-size: 1.1rem;
    border-top: 3px solid var(--primary-color);
    color: var(--primary-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.net-income-section {
    background: linear-gradient(135deg, var(--success-color), #229954);
    color: white;
    padding: 25px;
    border-radius: var(--border-radius);
    text-align: center;
    font-size: 1.3rem;
    font-weight: 700;
    box-shadow: var(--shadow);
    position: relative;
    overflow: hidden;
}

.net-income-section::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
    transform: rotate(45deg);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

/* RTL Support */
[dir="rtl"] .account-row {
    direction: rtl;
}

[dir="rtl"] .account-amount {
    text-align: right;
}

/* Accessibility Enhancements */
.account-row:focus-within {
    outline: 2px solid var(--secondary-color);
    outline-offset: 2px;
}

/* Print Styles */
@media print {
    .income-statement-container {
        box-shadow: none;
        border: 1px solid #000;
    }

    .section-header {
        background: #f0f0f0 !important;
        color: #000 !important;
    }
}
</style>

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-{{ direction == 'rtl' ? 'left' : 'right' }}">
        <div class="btn-group" role="group" aria-label="{{ text_actions }}">
          <button type="button" class="btn btn-success btn-lg" onclick="generateIncomeStatement()"
                  data-toggle="tooltip" title="{{ text_generate_tooltip }}">
            <i class="fa fa-line-chart"></i> {{ button_generate }}
          </button>
          <div class="btn-group" role="group">
            <button type="button" class="btn btn-info dropdown-toggle" data-toggle="dropdown"
                    aria-expanded="false" data-toggle="tooltip" title="{{ text_export_tooltip }}">
              <i class="fa fa-download"></i> {{ text_export }}
            </button>
            <ul class="dropdown-menu">
              <li><a href="#" onclick="exportIncomeStatement('excel')">
                <i class="fa fa-file-excel-o text-success"></i> Excel
              </a></li>
              <li><a href="#" onclick="exportIncomeStatement('pdf')">
                <i class="fa fa-file-pdf-o text-danger"></i> PDF
              </a></li>
              <li><a href="#" onclick="exportIncomeStatement('csv')">
                <i class="fa fa-file-text-o text-info"></i> CSV
              </a></li>
              <li class="divider"></li>
              <li><a href="#" onclick="printIncomeStatement()">
                <i class="fa fa-print"></i> {{ text_print }}
              </a></li>
            </ul>
          </div>
          <button type="button" class="btn btn-default" onclick="showAdvancedAnalysis()"
                  data-toggle="tooltip" title="{{ text_advanced_analysis }}">
            <i class="fa fa-line-chart"></i>
          </button>
        </div>
      </div>
      <h1><i class="fa fa-bar-chart"></i> {{ heading_title }}</h1>
      <ol class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
          <li class="breadcrumb-item"><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ol>
    </div>
  </div>

  <div class="container-fluid">
    {% if success %}
      <div class="alert alert-success alert-dismissible">
        <i class="fa fa-check-circle"></i> {{ success }}
        <button type="button" class="close" data-dismiss="alert">&times;</button>
      </div>
    {% endif %}

    <!-- قسم الفلترة -->
    <div class="panel panel-default" style="margin-bottom: 20px;">
      <div class="panel-heading">
        <i class="fa fa-filter"></i> {{ tab_filters }}
      </div>
      <div class="panel-body">
        <form id="income-statement-filter-form" class="form-horizontal">
          <div class="col-md-4">
            <label for="date_start" class="control-label">{{ entry_date_start }}</label>
            <input type="date" class="form-control" id="date_start" name="date_start" value="{{ date_start }}">
          </div>
          <div class="col-md-4">
            <label for="date_end" class="form-label">{{ entry_date_end }}</label>
            <input type="date" class="form-control" id="date_end" name="date_end" value="{{ date_end }}">
          </div>
          <div class="col-md-4">
            <label for="branch_id" class="form-label">{{ entry_branch }}</label>
            <select class="form-control" id="branch_id" name="branch_id">
              <option value="">جميع الفروع</option>
              {% for branch in branches %}
                <option value="{{ branch.branch_id }}">{{ branch.name }}</option>
              {% endfor %}
            </select>
          </div>
        </form>
      </div>
    </div>

    <!-- قائمة الدخل الرئيسية -->
    <div class="income-statement-container">
      <div class="income-statement-header">
        <h2>{{ text_income_statement }}</h2>
        <p>{{ text_period }}: {{ text_from }} {{ date_start }} {{ text_to }} {{ date_end }}</p>
      </div>

      {% if income_statement_data %}
        <!-- قسم الإيرادات -->
        <div class="income-statement-section">
          <div class="section-header">
            <i class="fa fa-arrow-up"></i> {{ text_revenues }}
          </div>
          <div class="section-content">
            {% for revenue in revenues %}
              <div class="account-row">
                <span class="account-name">{{ revenue.account_name }}</span>
                <span class="account-amount">{{ revenue.amount|number_format(2) }} ج.م</span>
              </div>
            {% endfor %}
            <div class="total-row">
              <span>{{ text_total_revenues }}</span>
              <span class="pull-right">{{ total_revenues|number_format(2) }} ج.م</span>
            </div>
          </div>
        </div>

        <!-- قسم تكلفة البضاعة المباعة -->
        {% if cost_of_goods_sold %}
          <div class="income-statement-section">
            <div class="section-header">
              <i class="fa fa-cube"></i> {{ text_cost_of_goods_sold }}
            </div>
            <div class="section-content">
              {% for cogs in cost_of_goods_sold %}
                <div class="account-row">
                  <span class="account-name">{{ cogs.account_name }}</span>
                  <span class="account-amount">{{ cogs.amount|number_format(2) }} ج.م</span>
                </div>
              {% endfor %}
              <div class="total-row">
                <span>{{ text_cost_of_goods_sold }}</span>
                <span class="pull-right">{{ total_cogs|number_format(2) }} ج.م</span>
              </div>
            </div>
          </div>

          <!-- إجمالي الربح -->
          <div class="income-statement-section">
            <div class="total-row" style="background: {% if gross_profit >= 0 %}#d5f4e6{% else %}#ffeaa7{% endif %};">
              <span>{{ text_gross_profit }}</span>
              <span class="pull-right">{{ gross_profit|number_format(2) }} ج.م</span>
            </div>
          </div>
        {% endif %}

        <!-- قسم المصروفات التشغيلية -->
        <div class="income-statement-section">
          <div class="section-header">
            <i class="fa fa-arrow-down"></i> {{ text_operating_expenses }}
          </div>
          <div class="section-content">
            {% for expense in operating_expenses %}
              <div class="account-row">
                <span class="account-name">{{ expense.account_name }}</span>
                <span class="account-amount">{{ expense.amount|number_format(2) }} ج.م</span>
              </div>
            {% endfor %}
            <div class="total-row">
              <span>{{ text_total_operating_expenses }}</span>
              <span class="pull-right">{{ total_operating_expenses|number_format(2) }} ج.م</span>
            </div>
          </div>
        </div>

        <!-- الدخل التشغيلي -->
        <div class="income-statement-section">
          <div class="total-row" style="background: {% if operating_income >= 0 %}#d5f4e6{% else %}#ffeaa7{% endif %};">
            <span>{{ text_operating_income }}</span>
            <span class="pull-right">{{ operating_income|number_format(2) }} ج.م</span>
          </div>
        </div>

        <!-- صافي الدخل -->
        <div class="net-income-section">
          <i class="fa fa-trophy"></i>
          {{ text_net_income }}: {{ net_income|number_format(2) }} جنيه مصري
        </div>

      {% else %}
        <div class="alert alert-info text-center">
          <i class="fa fa-info-circle"></i> {{ error_no_data }}
        </div>
      {% endif %}
    </div>
  </div>
</div>

<script>
// Enterprise Grade Plus JavaScript for Income Statement
class IncomeStatementManager {
    constructor() {
        this.initializeTooltips();
        this.initializeFormValidation();
        this.initializeKeyboardShortcuts();
        this.initializeAutoSave();
    }

    initializeTooltips() {
        $('[data-toggle="tooltip"]').tooltip();
    }

    initializeFormValidation() {
        const form = document.getElementById('income-statement-filter-form');
        if (form) {
            form.addEventListener('submit', this.validateForm.bind(this));
        }
    }

    initializeKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case 'g':
                        e.preventDefault();
                        this.generateIncomeStatement();
                        break;
                    case 'e':
                        e.preventDefault();
                        this.showExportMenu();
                        break;
                    case 'p':
                        e.preventDefault();
                        this.printIncomeStatement();
                        break;
                }
            }
        });
    }

    initializeAutoSave() {
        const inputs = document.querySelectorAll('#income-statement-filter-form input, #income-statement-filter-form select');
        inputs.forEach(input => {
            input.addEventListener('change', () => {
                this.saveFormState();
            });
        });
        this.loadFormState();
    }

    validateForm(e) {
        e.preventDefault();
        const dateStart = document.getElementById('date_start').value;
        const dateEnd = document.getElementById('date_end').value;

        if (!dateStart || !dateEnd) {
            this.showAlert('{{ error_date_range }}', 'danger');
            return false;
        }

        if (new Date(dateStart) > new Date(dateEnd)) {
            this.showAlert('{{ error_date_range }}', 'danger');
            return false;
        }

        this.generateIncomeStatement();
        return true;
    }

    generateIncomeStatement() {
        const form = document.getElementById('income-statement-filter-form');
        const formData = new FormData(form);

        // Show loading state
        this.showLoadingState(true);

        fetch('{{ generate_url }}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            this.showLoadingState(false);
            if (data.success) {
                this.showAlert('{{ text_success_analysis }}', 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                this.showAlert(data.error || '{{ error_generate }}', 'danger');
            }
        })
        .catch(error => {
            this.showLoadingState(false);
            this.showAlert('{{ error_generate }}: ' + error.message, 'danger');
        });
    }

    exportIncomeStatement(format) {
        const params = new URLSearchParams({
            format: format,
            date_start: document.getElementById('date_start').value,
            date_end: document.getElementById('date_end').value,
            branch_id: document.getElementById('branch_id').value
        });

        this.showAlert('{{ text_exporting }}...', 'info');
        window.open('{{ export_url }}&' + params.toString(), '_blank');
    }

    printIncomeStatement() {
        window.print();
    }

    showAdvancedAnalysis() {
        // Advanced analysis modal implementation
        const modal = new bootstrap.Modal(document.getElementById('advancedAnalysisModal'));
        modal.show();
    }

    showExportMenu() {
        const exportButton = document.querySelector('.dropdown-toggle');
        if (exportButton) {
            exportButton.click();
        }
    }

    showLoadingState(show) {
        const generateBtn = document.querySelector('button[onclick="generateIncomeStatement()"]');
        if (generateBtn) {
            if (show) {
                generateBtn.disabled = true;
                generateBtn.innerHTML = '<i class="fa fa-spinner fa-spin"></i> {{ text_generating }}';
            } else {
                generateBtn.disabled = false;
                generateBtn.innerHTML = '<i class="fa fa-line-chart"></i> {{ button_generate }}';
            }
        }
    }

    showAlert(message, type) {
        const alertContainer = document.createElement('div');
        alertContainer.className = `alert alert-${type} alert-dismissible fade show`;
        alertContainer.innerHTML = `
            <i class="fa fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'}"></i>
            ${message}
            <button type="button" class="close" data-dismiss="alert">&times;</button>
        `;

        const container = document.querySelector('#content .container-fluid');
        container.insertBefore(alertContainer, container.firstChild);

        setTimeout(() => {
            alertContainer.remove();
        }, 5000);
    }

    saveFormState() {
        const formData = new FormData(document.getElementById('income-statement-filter-form'));
        const state = Object.fromEntries(formData);
        localStorage.setItem('income_statement_form_state', JSON.stringify(state));
    }

    loadFormState() {
        const savedState = localStorage.getItem('income_statement_form_state');
        if (savedState) {
            const state = JSON.parse(savedState);
            Object.keys(state).forEach(key => {
                const element = document.getElementById(key);
                if (element) {
                    element.value = state[key];
                }
            });
        }
    }
}

// Global functions for backward compatibility
function generateIncomeStatement() {
    incomeStatementManager.generateIncomeStatement();
}

function exportIncomeStatement(format) {
    incomeStatementManager.exportIncomeStatement(format);
}

function printIncomeStatement() {
    incomeStatementManager.printIncomeStatement();
}

function showAdvancedAnalysis() {
    incomeStatementManager.showAdvancedAnalysis();
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.incomeStatementManager = new IncomeStatementManager();
});
</script>

{{ footer }}
