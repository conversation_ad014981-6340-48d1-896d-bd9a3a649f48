<?php
// Heading
$_['heading_title']                = 'Advanced Annual Tax Report';

// Text
$_['text_list']                    = 'Annual Tax Reports List';
$_['text_form']                    = 'Annual Tax Report Form';
$_['text_view']                    = 'View Annual Tax Report';
$_['text_generate']                = 'Generate Annual Tax Report';
$_['text_success_generate']        = 'Annual tax report generated successfully!';
$_['text_loading']                 = 'Loading...';
$_['text_processing']              = 'Processing...';
$_['text_no_results']              = 'No results found';
$_['text_home']                    = 'Home';

// Report Types
$_['text_summary_report']          = 'Summary Report';
$_['text_detailed_report']         = 'Detailed Report';
$_['text_comparative_report']      = 'Comparative Report';

// Tax Types
$_['text_all_taxes']               = 'All Taxes';
$_['text_income_tax']              = 'Income Tax';
$_['text_vat']                     = 'Value Added Tax';
$_['text_withholding_tax']         = 'Withholding Tax';
$_['text_stamp_tax']               = 'Stamp Tax';
$_['text_other_taxes']             = 'Other Taxes';

// Summary Information
$_['text_summary']                 = 'Summary';
$_['text_detailed_summary']        = 'Detailed Summary';
$_['text_total_transactions']      = 'Total Transactions';
$_['text_total_tax_expense']       = 'Total Tax Expense';
$_['text_total_tax_liability']     = 'Total Tax Liability';
$_['text_net_tax_impact']          = 'Net Tax Impact';
$_['text_average_tax_amount']      = 'Average Tax Amount';
$_['text_highest_tax_amount']      = 'Highest Tax Amount';
$_['text_lowest_tax_amount']       = 'Lowest Tax Amount';
$_['text_tax_efficiency_ratio']    = 'Tax Efficiency Ratio';

// Monthly Breakdown
$_['text_monthly_breakdown']       = 'Monthly Breakdown';
$_['text_month']                   = 'Month';
$_['text_transactions_count']      = 'Transactions Count';
$_['text_tax_expense']             = 'Tax Expense';
$_['text_tax_liability']           = 'Tax Liability';

// Quarterly Analysis
$_['text_quarterly_analysis']      = 'Quarterly Analysis';
$_['text_quarter']                 = 'Quarter';
$_['text_q1']                      = 'Q1';
$_['text_q2']                      = 'Q2';
$_['text_q3']                      = 'Q3';
$_['text_q4']                      = 'Q4';

// Comparative Analysis
$_['text_comparative_analysis']    = 'Comparative Analysis';
$_['text_current_year']            = 'Current Year';
$_['text_previous_year']           = 'Previous Year';
$_['text_variance']                = 'Variance';
$_['text_growth_rate']             = 'Growth Rate';
$_['text_percentage_change']       = 'Percentage Change';

// Compliance Status
$_['text_compliance_status']       = 'Compliance Status';
$_['text_compliance_score']        = 'Compliance Score';
$_['text_compliance_excellent']    = 'Excellent';
$_['text_compliance_good']         = 'Good';
$_['text_compliance_fair']         = 'Fair';
$_['text_compliance_poor']         = 'Poor';
$_['text_quarterly_returns']       = 'Quarterly Returns';
$_['text_annual_return']           = 'Annual Return';
$_['text_withholding_compliance']  = 'Withholding Tax Compliance';
$_['text_vat_compliance']          = 'VAT Compliance';

// Violations and Recommendations
$_['text_violations']              = 'Violations';
$_['text_recommendations']         = 'Recommendations';
$_['text_overdue_payment']         = 'Overdue Payments';
$_['text_missing_returns']         = 'Missing Returns';
$_['text_severity_critical']       = 'Critical';
$_['text_severity_high']           = 'High';
$_['text_severity_medium']         = 'Medium';
$_['text_severity_low']            = 'Low';

// Charts and Visualization
$_['text_charts']                  = 'Charts';
$_['text_tax_distribution_chart']  = 'Tax Distribution Chart';
$_['text_monthly_trend_chart']     = 'Monthly Trend Chart';
$_['text_quarterly_comparison']    = 'Quarterly Comparison';
$_['text_tax_types_breakdown']     = 'Tax Types Breakdown';

// Export Options
$_['text_export_options']          = 'Export Options';
$_['text_export_excel']            = 'Export to Excel';
$_['text_export_pdf']              = 'Export to PDF';
$_['text_export_csv']              = 'Export to CSV';
$_['text_export_eta']              = 'Export ETA Format';
$_['text_include_charts']          = 'Include Charts';
$_['text_include_analysis']        = 'Include Analysis';

// Print Options
$_['text_print_options']           = 'Print Options';
$_['text_print_preview']           = 'Print Preview';
$_['text_print_detailed']          = 'Print Detailed';
$_['text_print_summary']           = 'Print Summary';

// Entry Fields
$_['entry_year']                   = 'Year';
$_['entry_report_type']            = 'Report Type';
$_['entry_tax_type']               = 'Tax Type';
$_['entry_include_eta_format']     = 'Include ETA Format';
$_['entry_include_comparative']    = 'Include Comparative Analysis';
$_['entry_include_analysis']       = 'Include Advanced Analysis';

// Column Headers
$_['column_month']                 = 'Month';
$_['column_quarter']               = 'Quarter';
$_['column_tax_type']              = 'Tax Type';
$_['column_amount']                = 'Amount';
$_['column_percentage']            = 'Percentage';
$_['column_transactions']          = 'Transactions';
$_['column_compliance_status']     = 'Compliance Status';

// Buttons
$_['button_generate']              = 'Generate';
$_['button_view']                  = 'View';
$_['button_export']                = 'Export';
$_['button_print']                 = 'Print';
$_['button_analyze']               = 'Analyze';
$_['button_clear']                 = 'Clear';
$_['button_reset']                 = 'Reset';
$_['button_refresh']               = 'Refresh';
$_['button_save']                  = 'Save';
$_['button_cancel']                = 'Cancel';
$_['button_back']                  = 'Back';

// Error Messages
$_['error_permission']             = 'Warning: You do not have permission to access annual tax report!';
$_['error_year']                   = 'Year is required!';
$_['error_report_type']            = 'Report type is required!';
$_['error_no_data']                = 'No data to display!';
$_['error_generation_failed']      = 'Report generation failed!';
$_['error_export_failed']          = 'Report export failed!';
$_['error_print_failed']           = 'Report printing failed!';

// Success Messages
$_['success_generated']            = 'Report generated successfully!';
$_['success_exported']             = 'Report exported successfully!';
$_['success_printed']              = 'Report sent to printer successfully!';

// Help Text
$_['help_annual_tax']              = 'Annual tax report provides comprehensive analysis of all tax activities during the fiscal year.';
$_['help_compliance_analysis']     = 'Compliance analysis helps identify potential violations and recommendations for improvement.';
$_['help_eta_integration']         = 'ETA integration ensures compliance with Egyptian Tax Authority requirements.';

// ETA Integration
$_['text_eta_integration']         = 'ETA Integration';
$_['text_eta_compliance']          = 'ETA Compliance';
$_['text_eta_ready']               = 'ETA Ready';
$_['text_eta_format']              = 'ETA Format';
$_['text_eta_submission']          = 'ETA Submission';

// Advanced Features
$_['text_advanced_analysis']       = 'Advanced Analysis';
$_['text_tax_optimization']        = 'Tax Optimization';
$_['text_seasonal_patterns']       = 'Seasonal Patterns';
$_['text_risk_assessment']         = 'Risk Assessment';
$_['text_forecasting']             = 'Forecasting';

// Status
$_['text_status_filed']            = 'Filed';
$_['text_status_pending']          = 'Pending';
$_['text_status_overdue']          = 'Overdue';
$_['text_status_compliant']        = 'Compliant';
$_['text_status_non_compliant']    = 'Non-Compliant';

// Additional
$_['text_total']                   = 'Total';
$_['text_subtotal']                = 'Subtotal';
$_['text_average']                 = 'Average';
$_['text_count']                   = 'Count';
$_['text_ratio']                   = 'Ratio';
$_['text_index']                   = 'Index';
$_['text_score']                   = 'Score';

// Compliance
$_['text_egyptian_tax_law']        = 'Egyptian Tax Law';
$_['text_international_standards'] = 'International Standards';
$_['text_audit_trail']             = 'Audit Trail';

// Missing variables from audit report - Critical fixes
$_['accounts/annual_tax']          = '';
$_['code']                         = 'Code';
$_['date_format_short']            = 'm/d/Y';
$_['direction']                    = 'ltr';
$_['lang']                         = 'en';

// Controller language variables
$_['log_unauthorized_access_annual_tax'] = 'Unauthorized access attempt to annual tax report';
$_['log_view_annual_tax_screen']   = 'View annual tax report screen';
$_['log_unauthorized_generate_annual_tax'] = 'Unauthorized annual tax report generation attempt';
$_['log_generate_annual_tax_year'] = 'Generate annual tax report for year';
$_['text_tax_compliance_violations_alert'] = 'Warning: Potential Tax Violations';
$_['text_tax_violations_detected'] = 'Detected';
$_['text_tax_violation_in_annual_report'] = 'potential tax violation in annual report';
$_['text_annual_tax_generated']    = 'Annual Tax Report Generated';
$_['text_annual_tax_generated_notification'] = 'Annual tax report generated for year';
$_['log_view_annual_tax']          = 'View annual tax report';
$_['log_export_annual_tax_format'] = 'Export annual tax report in format';

// Enterprise Grade Plus template variables
$_['text_actions']                 = 'Actions';
$_['text_generate_tooltip']        = 'Generate annual tax report for selected year';
$_['text_export_tooltip']          = 'Export annual tax report in various formats';
$_['text_print']                   = 'Print';
$_['text_generating']              = 'Generating...';
$_['text_exporting']               = 'Exporting...';
$_['error_generate']               = 'Error generating annual tax report';
$_['text_tax_filters']             = 'Tax Filters';
$_['entry_tax_year']               = 'Tax Year';
$_['entry_report_type']            = 'Report Type';
$_['entry_tax_type']               = 'Tax Type';
$_['text_summary_report']          = 'Summary Report';
$_['text_detailed_report']         = 'Detailed Report';
$_['text_compliance_report']       = 'Compliance Report';
$_['text_all_taxes']               = 'All Taxes';
$_['text_vat_only']                = 'VAT Only';
$_['text_income_tax_only']         = 'Income Tax Only';
$_['button_filter']                = 'Filter';
$_['text_include_compliance_analysis'] = 'Include Compliance Analysis';
$_['text_tax_year']                = 'Tax Year';
$_['text_report_type']             = 'Report Type';
$_['text_vat_liability']           = 'VAT Liability';
$_['text_income_tax_liability']    = 'Income Tax Liability';
$_['text_total_tax_liability']     = 'Total Tax Liability';
$_['text_compliance_score']        = 'Compliance Score';
$_['text_of_total']                = 'of Total';
$_['text_excellent']               = 'Excellent';
$_['text_good']                    = 'Good';
$_['text_needs_attention']         = 'Needs Attention';
$_['text_tax_category']            = 'Tax Category';
$_['text_taxable_amount']          = 'Taxable Amount';
$_['text_tax_rate']                = 'Tax Rate';
$_['text_calculated_tax']          = 'Calculated Tax';
$_['text_paid_tax']                = 'Paid Tax';
$_['text_balance']                 = 'Balance';
$_['text_compliance_status']       = 'Compliance Status';
$_['text_due_date']                = 'Due Date';
$_['text_overdue']                 = 'Overdue';
$_['text_compliance_violations_alert'] = 'Compliance Violations Alert';
$_['text_violations_found']        = 'Violations Found';
$_['text_total_penalty_risk']      = 'Total Penalty Risk';
$_['text_view_violations_details'] = 'View Violations Details';
$_['text_eta_integration_status']  = 'ETA Integration Status';
$_['text_last_sync']               = 'Last Sync';
$_['text_sync_status']             = 'Sync Status';
$_['text_submitted_returns']       = 'Submitted Returns';
$_['text_pending_submissions']     = 'Pending Submissions';
$_['text_connect_to_eta']          = 'Connect to ETA';
$_['text_sync_with_eta']           = 'Sync with ETA';
$_['text_no_data']                 = 'No data to display. Please select year and click generate.';
$_['error_year_required']          = 'Tax year is required';
$_['warning_future_year']          = 'Warning: Selected year is in the future';
$_['text_success_generation']      = 'Annual tax report generated successfully';
$_['text_compliance_issues_found'] = 'Compliance issues found';
$_['text_violations']              = 'violations';
$_['text_warnings']                = 'warnings';
$_['text_no_compliance_issues']    = 'No compliance issues found';
$_['text_connecting_to_eta']       = 'Connecting to ETA...';
$_['text_eta_connection_successful'] = 'ETA connection successful';
$_['text_syncing_with_eta']        = 'Syncing with ETA...';
$_['text_eta_sync_successful']     = 'ETA sync successful';
$_['text_eta_connection_lost']     = 'ETA connection lost';
$_['text_compliance_analysis']     = 'Compliance Analysis';

// Generated Information
$_['text_generated_by']            = 'Generated By';
$_['text_generated_on']            = 'Generated On';
$_['text_report_date']             = 'Report Date';
$_['text_page']                    = 'Page';
$_['text_of']                      = 'of';
$_['text_confidential']            = 'Confidential';
$_['text_internal_use']            = 'Internal Use Only';

// Legacy Fields (for backward compatibility)
$_['print_title']                  = 'Print Annual Tax Report';
$_['text_annual_tax_report']       = 'Annual Tax Report';
$_['text_year']                    = 'Year';
$_['text_total_taxes']             = 'Total Taxes';
$_['button_filter']                = 'Filter';

// Enhanced performance and compliance variables
$_['text_optimized_tax_report']        = 'Optimized Tax Report';
$_['text_tax_trends']                  = 'Tax Trends';
$_['text_compliance_analysis']         = 'Compliance Analysis';
$_['text_advanced_compliance']         = 'Advanced Compliance';
$_['text_cache_enabled']               = 'Cache Enabled';
$_['text_report_cached']               = 'Report Cached';
$_['text_compliance_score']            = 'Compliance Score';
$_['text_risk_level']                  = 'Risk Level';
$_['text_low_risk']                    = 'Low Risk';
$_['text_medium_risk']                 = 'Medium Risk';
$_['text_high_risk']                   = 'High Risk';
$_['text_recommendations']             = 'Recommendations';
$_['text_monthly_compliance']          = 'Monthly Compliance';
$_['text_quarterly_compliance']        = 'Quarterly Compliance';
$_['text_annual_compliance']           = 'Annual Compliance';
$_['text_tax_efficiency_ratio']        = 'Tax Efficiency Ratio';
$_['button_compliance_analysis']       = 'Compliance Analysis';
$_['button_tax_trends']                = 'Tax Trends';
$_['text_loading_compliance']          = 'Loading compliance analysis...';
$_['text_compliance_ready']            = 'Compliance analysis ready';
