<?php
$_['heading_title'] = 'Manage Permissions';

$_['text_list'] = 'List of Permissions';
$_['text_no_results'] = 'No Permissions.';
$_['text_success'] = 'Save Successfully!';
$_['text_add'] = 'Add New Permission';
$_['text_edit'] = 'Edit Permission';
$_['text_explanation'] = 'Add new permissions, associate them with a user or group. Use $this->user->hasPermissionKey(\'key\') to verify.';

// Entries
$_['entry_name'] = 'Permission Name';
$_['entry_key'] = 'Key';
$_['entry_type'] = 'Type';
$_['entry_user_groups'] = 'User Groups';
$_['entry_users'] = 'Users';
$_['entry_filter_name'] = 'Filter by Name';

// Buttons
$_['button_add'] = 'Add New Permission';
$_['button_save'] = 'Save';
$_['button_cancel'] = 'Cancel';
$_['button_edit'] = 'Edit';
$_['button_delete'] = 'Delete';
$_['button_filter'] = 'Filter';

// Errors
$_['error_permission'] = 'Warning: You do not have edit permissions!';
$_['error_name'] = 'Name must be at least 3 characters long!';
$_['text_confirm'] = 'Are you sure?';