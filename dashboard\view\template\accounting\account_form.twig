{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="submit" form="form-account" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fa fa-save"></i></button>
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a></div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pencil"></i> {{ text_form }}</h3>
      </div>
      <div class="panel-body">
        <form action="{{ action }}" method="post" enctype="multipart/form-data" id="form-account" class="form-horizontal">
          <ul class="nav nav-tabs">
            <li class="active"><a href="#tab-general" data-toggle="tab">{{ tab_general }}</a></li>
          </ul>
          <div class="tab-content">
            <div class="tab-pane active" id="tab-general">
              <div class="form-group required">
                <label class="col-sm-2 control-label" for="input-account-code">{{ entry_account_code }}</label>
                <div class="col-sm-10">
                  <input type="text" name="account_code" value="{{ account_code }}" placeholder="{{ entry_account_code }}" id="input-account-code" class="form-control" />
                  {% if error_account_code %}
                  <div class="text-danger">{{ error_account_code }}</div>
                  {% endif %}
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-parent">{{ entry_parent }}</label>
                <div class="col-sm-10">
                  <select name="parent_id" id="input-parent" class="form-control">
                    <option value="">{{ text_none }}</option>
                    {% for parent in parent_accounts %}
                    <option value="{{ parent.account_id }}"{% if parent.account_id == parent_id %} selected="selected"{% endif %}>{{ parent.name }}</option>
                    {% endfor %}
                  </select>
                </div>
              </div>
              <div class="form-group required">
                <label class="col-sm-2 control-label" for="input-account-type">{{ entry_account_type }}</label>
                <div class="col-sm-10">
                  <select name="account_type" id="input-account-type" class="form-control">
                    <option value="">{{ text_select }}</option>
                    <option value="asset"{% if account_type == 'asset' %} selected="selected"{% endif %}>{{ text_asset }}</option>
                    <option value="liability"{% if account_type == 'liability' %} selected="selected"{% endif %}>{{ text_liability }}</option>
                    <option value="equity"{% if account_type == 'equity' %} selected="selected"{% endif %}>{{ text_equity }}</option>
                    <option value="revenue"{% if account_type == 'revenue' %} selected="selected"{% endif %}>{{ text_revenue }}</option>
                    <option value="expense"{% if account_type == 'expense' %} selected="selected"{% endif %}>{{ text_expense }}</option>
                  </select>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-status">{{ entry_status }}</label>
                <div class="col-sm-10">
                  <select name="status" id="input-status" class="form-control">
                    {% if status %}
                    <option value="1" selected="selected">{{ text_enabled }}</option>
                    <option value="0">{{ text_disabled }}</option>
                    {% else %}
                    <option value="1">{{ text_enabled }}</option>
                    <option value="0" selected="selected">{{ text_disabled }}</option>
                    {% endif %}
                  </select>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label">{{ entry_name }}</label>
                <div class="col-sm-10">
                  <ul class="nav nav-tabs" id="language">
                    {% for language in languages %}
                    <li><a href="#language{{ language.language_id }}" data-toggle="tab"><img src="language/{{ language.code }}/{{ language.code }}.png" title="{{ language.name }}" /> {{ language.name }}</a></li>
                    {% endfor %}
                  </ul>
                  <div class="tab-content">
                    {% for language in languages %}
                    <div class="tab-pane" id="language{{ language.language_id }}">
                      <input type="text" name="account_description[{{ language.language_id }}][name]" value="{{ account_description[language.language_id] ? account_description[language.language_id].name }}" placeholder="{{ entry_name }}" class="form-control" />
                      {% if error_name[language.language_id] %}
                      <div class="text-danger">{{ error_name[language.language_id] }}</div>
                      {% endif %}
                    </div>
                    {% endfor %}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
<script type="text/javascript"><!--
$('#language a:first').tab('show');
//--></script>
{{ footer }} 