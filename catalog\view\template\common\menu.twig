{% if categories %}
<div class="row">
  <div class="container-fluid" style="padding:0px;">
    <nav id="menu" class="navbar navbar-expand-lg navbar-light bg-primary" >
      <div id="category" class="d-block d-sm-block d-lg-none">{{ text_category }}</div>
      <button title="menu" class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#narbar-menu"><i class="fa-solid fa-bars"></i></button>
      <div class="collapse navbar-collapse" id="narbar-menu">
        <ul class="nav navbar-nav">
          {% for category in categories %}
            {% if category.children %}
              <li class="nav-item dropdown"><a title="{{category.name}}"  style="font-size: 16px;" href="{{ category.href }}" class="nav-link">
                  {{ category.name }} 
                                    {% if category.children %}  <i style="font-size:20px;margin-right:10px;margin-left:10px" class="fa-solid fa-caret-down  dropdown-toggle"  data-bs-toggle="dropdown"></i> {% endif %}

                  </a>
                <div class="dropdown-menu">
                  <div class="dropdown-inner">
                    {% for children in category.children|batch(category.children|length / category.column|round(1, 'ceil')) %}
                      <ul class="list-unstyled">
                        {% for child in children %}
                         <li><a  title="{{child.name}}" style="font-size: 16px;" href="{{ child.href }}" childid="{{ child.childid }}" catid="{{ child.catid }}" class="nav-item">
                              {% if child.image %} <img class="imagemenu"  height="129" width="200" src="{{ child.image }}" alt="{{ child.name }}"> {% endif %}
                            <br><span  style="text-align: center;display: block;margin: 0 auto;color: #0f1740;font-size: 16px;font-weight: 800;padding-top:5px;padding-bottom:5px">{{ child.name }}</span>
                              </a></li>
                        {% endfor %}
                      </ul>
                    {% endfor %}
                  </div>
                </div>
              </li>
            {% else %}
              <li class="nav-item"><a style="font-size: 16px;"  href="{{ category.href }}" class="nav-link">
                  {{ category.name }}</a></li>
            {% endif %}
          {% endfor %}




        </ul>

        

            
            
            
      </div>
    </nav>
  </div>
</div>
{% endif %}
<style>
/* تحسينات قائمة سطح المكتب 2025 */
@media (min-width: 992px) {
  .navbar {
    padding: 0;
    box-shadow: 0 4px 15px rgba(0,0,0,0.06);
  }
  
  .navbar-nav {
    gap: 5px;
  }
  
  .nav-item {
    position: relative;
  }
  
  .nav-link {
    font-size: 16px;
    font-weight: 500;
    padding: 18px 16px;
    color: #f8f9fa;
    transition: all 0.3s ease;
    position: relative;
  }
  
  .nav-link:hover {
    color: #f99f1e;
  }
  
  /* تأثير خط تحت النص عند التحويم */
  .nav-link::after {
    content: '';
    position: absolute;
    bottom: 10px;
    left: 50%;
    width: 0;
    height: 2px;
    background-color: #f99f1e;
    transition: width 0.3s ease, left 0.3s ease;
  }
  
  .nav-link:hover::after {
    width: 60%;
    left: 20%;
  }
  
  .dropdown-menu {
    padding: 15px;
    border: none;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
    animation: fadeInDown 0.3s forwards;
    min-width: 230px;
    margin-top: 15px;
  }
  
  @keyframes fadeInDown {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  .dropdown-item {
    padding: 12px 15px;
    border-radius: 8px;
    transition: all 0.2s ease;
  }
  
  .dropdown-item:hover {
    background-color: rgba(249, 159, 30, 0.1);
    transform: translateX(5px);
  }
}    
</style>
