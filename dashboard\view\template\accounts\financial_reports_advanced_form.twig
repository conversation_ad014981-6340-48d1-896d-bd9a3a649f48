{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="button" id="button-generate" data-toggle="tooltip" title="{{ button_generate }}" class="btn btn-primary"><i class="fa fa-cogs"></i></button>
        <a href="{{ dashboard }}" data-toggle="tooltip" title="{{ button_dashboard }}" class="btn btn-info"><i class="fa fa-dashboard"></i></a>
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    {% if success %}
    <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-cogs"></i> {{ text_report_configuration }}</h3>
      </div>
      <div class="panel-body">
        <form id="form-financial-report" class="form-horizontal">
          
          <!-- نوع التقرير -->
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-report-type">{{ entry_report_type }}</label>
            <div class="col-sm-10">
              <select name="report_type" id="input-report-type" class="form-control">
                <option value="comprehensive">{{ text_comprehensive_report }}</option>
                <option value="income_statement">{{ text_income_statement }}</option>
                <option value="balance_sheet">{{ text_balance_sheet }}</option>
                <option value="cash_flow">{{ text_cash_flow_statement }}</option>
                <option value="equity_changes">{{ text_equity_changes_statement }}</option>
                <option value="financial_ratios">{{ text_financial_ratios_report }}</option>
                <option value="performance_analysis">{{ text_performance_analysis_report }}</option>
              </select>
            </div>
          </div>

          <!-- الفترة الزمنية -->
          <div class="form-group required">
            <label class="col-sm-2 control-label">{{ entry_period }}</label>
            <div class="col-sm-4">
              <div class="input-group date">
                <input type="text" name="date_start" value="{{ date_start }}" placeholder="{{ entry_date_start }}" id="input-date-start" class="form-control" />
                <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
              </div>
            </div>
            <div class="col-sm-4">
              <div class="input-group date">
                <input type="text" name="date_end" value="{{ date_end }}" placeholder="{{ entry_date_end }}" id="input-date-end" class="form-control" />
                <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
              </div>
            </div>
            <div class="col-sm-2">
              <select name="period_preset" id="input-period-preset" class="form-control">
                <option value="">{{ text_custom_period }}</option>
                <option value="current_month">{{ text_current_month }}</option>
                <option value="last_month">{{ text_last_month }}</option>
                <option value="current_quarter">{{ text_current_quarter }}</option>
                <option value="last_quarter">{{ text_last_quarter }}</option>
                <option value="current_year">{{ text_current_year }}</option>
                <option value="last_year">{{ text_last_year }}</option>
              </select>
            </div>
          </div>

          <!-- خيارات المقارنة -->
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-comparison">{{ entry_comparison_period }}</label>
            <div class="col-sm-10">
              <select name="comparison_period" id="input-comparison" class="form-control">
                <option value="none">{{ text_no_comparison }}</option>
                <option value="previous_period">{{ text_previous_period }}</option>
                <option value="previous_year">{{ text_previous_year }}</option>
                <option value="budget">{{ text_budget_comparison }}</option>
                <option value="custom">{{ text_custom_comparison }}</option>
              </select>
            </div>
          </div>

          <!-- فترة المقارنة المخصصة -->
          <div class="form-group" id="custom-comparison-period" style="display: none;">
            <label class="col-sm-2 control-label">{{ entry_comparison_dates }}</label>
            <div class="col-sm-4">
              <div class="input-group date">
                <input type="text" name="comparison_date_start" placeholder="{{ entry_date_start }}" class="form-control" />
                <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
              </div>
            </div>
            <div class="col-sm-4">
              <div class="input-group date">
                <input type="text" name="comparison_date_end" placeholder="{{ entry_date_end }}" class="form-control" />
                <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
              </div>
            </div>
          </div>

          <!-- خيارات التحليل -->
          <div class="form-group">
            <label class="col-sm-2 control-label">{{ entry_analysis_options }}</label>
            <div class="col-sm-10">
              <div class="checkbox">
                <label>
                  <input type="checkbox" name="include_ratios" value="1" checked> {{ text_include_financial_ratios }}
                </label>
              </div>
              <div class="checkbox">
                <label>
                  <input type="checkbox" name="include_trends" value="1" checked> {{ text_include_trend_analysis }}
                </label>
              </div>
              <div class="checkbox">
                <label>
                  <input type="checkbox" name="include_benchmarks" value="1"> {{ text_include_benchmark_analysis }}
                </label>
              </div>
              <div class="checkbox">
                <label>
                  <input type="checkbox" name="include_segments" value="1"> {{ text_include_segment_analysis }}
                </label>
              </div>
              <div class="checkbox">
                <label>
                  <input type="checkbox" name="include_budget" value="1"> {{ text_include_budget_comparison }}
                </label>
              </div>
            </div>
          </div>

          <!-- تحليل القطاعات -->
          <div class="form-group" id="segment-options" style="display: none;">
            <label class="col-sm-2 control-label">{{ entry_segment_type }}</label>
            <div class="col-sm-10">
              <div class="row">
                <div class="col-sm-3">
                  <div class="checkbox">
                    <label>
                      <input type="checkbox" name="segment_by_product" value="1"> {{ text_by_product }}
                    </label>
                  </div>
                </div>
                <div class="col-sm-3">
                  <div class="checkbox">
                    <label>
                      <input type="checkbox" name="segment_by_customer" value="1"> {{ text_by_customer }}
                    </label>
                  </div>
                </div>
                <div class="col-sm-3">
                  <div class="checkbox">
                    <label>
                      <input type="checkbox" name="segment_by_location" value="1"> {{ text_by_location }}
                    </label>
                  </div>
                </div>
                <div class="col-sm-3">
                  <div class="checkbox">
                    <label>
                      <input type="checkbox" name="segment_by_channel" value="1"> {{ text_by_channel }}
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- خيارات العرض -->
          <div class="form-group">
            <label class="col-sm-2 control-label">{{ entry_display_options }}</label>
            <div class="col-sm-10">
              <div class="row">
                <div class="col-sm-4">
                  <select name="currency" class="form-control">
                    <option value="EGP">{{ text_egyptian_pound }}</option>
                    <option value="USD">{{ text_us_dollar }}</option>
                    <option value="EUR">{{ text_euro }}</option>
                  </select>
                </div>
                <div class="col-sm-4">
                  <select name="rounding" class="form-control">
                    <option value="0">{{ text_no_rounding }}</option>
                    <option value="1000">{{ text_thousands }}</option>
                    <option value="1000000">{{ text_millions }}</option>
                  </select>
                </div>
                <div class="col-sm-4">
                  <select name="decimal_places" class="form-control">
                    <option value="0">{{ text_no_decimals }}</option>
                    <option value="2" selected>{{ text_two_decimals }}</option>
                    <option value="4">{{ text_four_decimals }}</option>
                  </select>
                </div>
              </div>
            </div>
          </div>

          <!-- خيارات التصدير -->
          <div class="form-group">
            <label class="col-sm-2 control-label">{{ entry_export_options }}</label>
            <div class="col-sm-10">
              <div class="checkbox">
                <label>
                  <input type="checkbox" name="include_charts" value="1" checked> {{ text_include_charts }}
                </label>
              </div>
              <div class="checkbox">
                <label>
                  <input type="checkbox" name="include_notes" value="1" checked> {{ text_include_notes }}
                </label>
              </div>
              <div class="checkbox">
                <label>
                  <input type="checkbox" name="include_watermark" value="1"> {{ text_include_watermark }}
                </label>
              </div>
            </div>
          </div>

          <!-- خيارات متقدمة -->
          <div class="panel panel-info">
            <div class="panel-heading">
              <h4 class="panel-title">
                <a data-toggle="collapse" href="#advanced-options">
                  <i class="fa fa-cog"></i> {{ text_advanced_options }}
                </a>
              </h4>
            </div>
            <div id="advanced-options" class="panel-collapse collapse">
              <div class="panel-body">
                
                <!-- فلترة الحسابات -->
                <div class="form-group">
                  <label class="col-sm-2 control-label">{{ entry_account_filter }}</label>
                  <div class="col-sm-10">
                    <select name="account_groups[]" multiple class="form-control select2">
                      <option value="assets">{{ text_assets }}</option>
                      <option value="liabilities">{{ text_liabilities }}</option>
                      <option value="equity">{{ text_equity }}</option>
                      <option value="revenues">{{ text_revenues }}</option>
                      <option value="expenses">{{ text_expenses }}</option>
                    </select>
                  </div>
                </div>

                <!-- فلترة مراكز التكلفة -->
                <div class="form-group">
                  <label class="col-sm-2 control-label">{{ entry_cost_centers }}</label>
                  <div class="col-sm-10">
                    <select name="cost_centers[]" multiple class="form-control select2">
                      {% for cost_center in cost_centers %}
                      <option value="{{ cost_center.cost_center_id }}">{{ cost_center.name }}</option>
                      {% endfor %}
                    </select>
                  </div>
                </div>

                <!-- فلترة الفروع -->
                <div class="form-group">
                  <label class="col-sm-2 control-label">{{ entry_branches }}</label>
                  <div class="col-sm-10">
                    <select name="branches[]" multiple class="form-control select2">
                      {% for branch in branches %}
                      <option value="{{ branch.branch_id }}">{{ branch.name }}</option>
                      {% endfor %}
                    </select>
                  </div>
                </div>

                <!-- خيارات الأداء -->
                <div class="form-group">
                  <label class="col-sm-2 control-label">{{ entry_performance_options }}</label>
                  <div class="col-sm-10">
                    <div class="checkbox">
                      <label>
                        <input type="checkbox" name="use_cache" value="1" checked> {{ text_use_cache }}
                      </label>
                    </div>
                    <div class="checkbox">
                      <label>
                        <input type="checkbox" name="parallel_processing" value="1"> {{ text_parallel_processing }}
                      </label>
                    </div>
                  </div>
                </div>

              </div>
            </div>
          </div>

        </form>
      </div>
    </div>

    <!-- معاينة سريعة -->
    <div class="panel panel-success" id="quick-preview" style="display: none;">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-eye"></i> {{ text_quick_preview }}</h3>
      </div>
      <div class="panel-body">
        <div id="preview-content">
          <!-- سيتم تحميل المعاينة هنا -->
        </div>
      </div>
    </div>

  </div>
</div>

<script type="text/javascript"><!--
// Date pickers
$('.date').datetimepicker({
    language: '{{ datepicker }}',
    pickTime: false
});

// Select2 initialization
$('.select2').select2({
    placeholder: '{{ text_select_options }}',
    allowClear: true
});

// Period preset handling
$('#input-period-preset').on('change', function() {
    var preset = $(this).val();
    if (preset) {
        var dates = getPresetDates(preset);
        $('#input-date-start').val(dates.start);
        $('#input-date-end').val(dates.end);
    }
});

// Comparison period handling
$('#input-comparison').on('change', function() {
    if ($(this).val() == 'custom') {
        $('#custom-comparison-period').show();
    } else {
        $('#custom-comparison-period').hide();
    }
});

// Segment analysis toggle
$('input[name="include_segments"]').on('change', function() {
    if ($(this).is(':checked')) {
        $('#segment-options').show();
    } else {
        $('#segment-options').hide();
    }
});

// Generate report
$('#button-generate').on('click', function() {
    var formData = $('#form-financial-report').serialize();
    
    $.ajax({
        url: '{{ action }}',
        type: 'post',
        data: formData,
        dataType: 'json',
        beforeSend: function() {
            $('#button-generate').button('loading');
        },
        complete: function() {
            $('#button-generate').button('reset');
        },
        success: function(json) {
            if (json['error']) {
                $('.alert-danger').remove();
                $('#content > .container-fluid').prepend('<div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> ' + json['error'] + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
            }
            
            if (json['success']) {
                $('.alert-success').remove();
                $('#content > .container-fluid').prepend('<div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> ' + json['success'] + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
                
                // Redirect to view page
                setTimeout(function() {
                    location = json['redirect'];
                }, 1000);
            }
        },
        error: function(xhr, ajaxOptions, thrownError) {
            alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
    });
});

// Quick preview
$('#form-financial-report input, #form-financial-report select').on('change', function() {
    // Debounced preview update
    clearTimeout(window.previewTimeout);
    window.previewTimeout = setTimeout(updatePreview, 1000);
});

function updatePreview() {
    var formData = $('#form-financial-report').serialize();
    
    $.ajax({
        url: '{{ preview_url }}',
        type: 'post',
        data: formData,
        dataType: 'json',
        success: function(json) {
            if (json['preview']) {
                $('#preview-content').html(json['preview']);
                $('#quick-preview').show();
            }
        }
    });
}

function getPresetDates(preset) {
    var today = new Date();
    var start, end;
    
    switch(preset) {
        case 'current_month':
            start = new Date(today.getFullYear(), today.getMonth(), 1);
            end = new Date(today.getFullYear(), today.getMonth() + 1, 0);
            break;
        case 'last_month':
            start = new Date(today.getFullYear(), today.getMonth() - 1, 1);
            end = new Date(today.getFullYear(), today.getMonth(), 0);
            break;
        case 'current_quarter':
            var quarter = Math.floor(today.getMonth() / 3);
            start = new Date(today.getFullYear(), quarter * 3, 1);
            end = new Date(today.getFullYear(), quarter * 3 + 3, 0);
            break;
        case 'current_year':
            start = new Date(today.getFullYear(), 0, 1);
            end = new Date(today.getFullYear(), 11, 31);
            break;
        case 'last_year':
            start = new Date(today.getFullYear() - 1, 0, 1);
            end = new Date(today.getFullYear() - 1, 11, 31);
            break;
    }
    
    return {
        start: start.toISOString().split('T')[0],
        end: end.toISOString().split('T')[0]
    };
}

// Tooltips
$('[data-toggle="tooltip"]').tooltip();
//--></script>

{{ footer }}
