<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/expr/v1beta1/expr.proto

namespace Google\Api\Expr\V1beta1;

if (false) {
    /**
     * This class is deprecated. Use Google\Api\Expr\V1beta1\Expr\Call instead.
     * @deprecated
     */
    class Expr_Call {}
}
class_exists(Expr\Call::class);
@trigger_error('Google\Api\Expr\V1beta1\Expr_Call is deprecated and will be removed in the next major release. Use Google\Api\Expr\V1beta1\Expr\Call instead', E_USER_DEPRECATED);

