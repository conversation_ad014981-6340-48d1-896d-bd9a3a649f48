/* محرر سير العمل المرئي - CSS */

/* تخطيط رئيسي */
.workflow-designer-container {
  display: flex;
  position: relative;
  height: calc(100vh - 280px);
  min-height: 600px;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
}

/* الشريط الجانبي للعقد */
.workflow-sidebar {
  width: 280px;
  background-color: #fff;
  border-right: 1px solid #ddd;
  overflow-y: auto;
  overflow-x: hidden;
  z-index: 10;
}

.workflow-sidebar-content {
  padding: 10px;
}

/* منطقة الرسم */
.workflow-canvas-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
  position: relative;
  overflow: hidden;
}

.workflow-canvas {
  flex: 1;
  background-image: 
    linear-gradient(to right, rgba(128, 128, 128, 0.1) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(128, 128, 128, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
  position: relative;
  overflow: auto;
}

/* شريط الأدوات */
.workflow-toolbar {
  height: 40px;
  background-color: #fff;
  border-bottom: 1px solid #ddd;
  display: flex;
  align-items: center;
  padding: 0 10px;
}

.workflow-toolbar .btn-group {
  margin-right: 15px;
}

/* لوحة الخصائص */
.workflow-properties-panel {
  width: 280px;
  background-color: #fff;
  border-left: 1px solid #ddd;
  overflow-y: auto;
  overflow-x: hidden;
  z-index: 10;
  display: none;
}

.workflow-properties-panel.active {
  display: block;
}

.workflow-properties-header {
  padding: 10px;
  border-bottom: 1px solid #ddd;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.workflow-properties-content {
  padding: 15px;
}

/* قائمة العقد */
.node-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.node-item {
  display: flex;
  align-items: center;
  padding: 8px;
  background-color: #f9f9f9;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: move;
  transition: all 0.2s;
}

.node-item:hover {
  background-color: #f0f0f0;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.node-icon {
  flex: 0 0 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10px;
  font-size: 16px;
  color: #555;
}

.node-info {
  flex: 1;
}

.node-title {
  font-weight: 600;
  margin-bottom: 3px;
}

.node-description {
  font-size: 11px;
  color: #777;
}

/* قائمة الاتصالات */
.connection-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.connection-item {
  padding: 10px;
  background-color: #f9f9f9;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.connection-icon {
  float: left;
  margin-right: 10px;
  font-size: 18px;
}

.connection-title {
  font-weight: 600;
  margin-bottom: 5px;
}

.connection-modules {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  margin-top: 5px;
}

.connection-module {
  font-size: 11px;
  padding: 2px 5px;
  background-color: #e9e9e9;
  border-radius: 3px;
}

/* عقد سير العمل المرئي */
.workflow-node {
  position: absolute;
  width: 200px;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  z-index: 5;
  user-select: none;
}

.workflow-node.selected {
  box-shadow: 0 0 0 2px #2196F3;
}

.workflow-node.dragging {
  opacity: 0.8;
  z-index: 100;
}

.node-header {
  padding: 8px 10px;
  display: flex;
  align-items: center;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  cursor: move;
}

.workflow-node-trigger .node-header {
  background-color: #ffebee;
  color: #d32f2f;
}

.workflow-node-action .node-header {
  background-color: #e3f2fd;
  color: #1976d2;
}

.workflow-node-flow .node-header {
  background-color: #f1f8e9;
  color: #689f38;
}

.node-header .node-icon {
  margin-right: 8px;
}

.node-header .node-title {
  flex: 1;
  font-weight: 600;
  margin: 0;
}

.node-actions {
  display: flex;
}

.node-action {
  background: none;
  border: none;
  color: inherit;
  padding: 0;
  margin-left: 8px;
  cursor: pointer;
  opacity: 0.7;
}

.node-action:hover {
  opacity: 1;
}

.node-body {
  padding: 10px;
  border-bottom: 1px solid #e0e0e0;
}

.node-ports {
  padding: 6px 10px;
  display: flex;
  justify-content: space-between;
}

.node-port {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background-color: #bdbdbd;
  cursor: pointer;
  position: relative;
}

.node-port:hover {
  background-color: #2196F3;
}

.node-input {
  margin-right: auto;
}

.node-output {
  margin-left: auto;
}

.node-output-approved {
  background-color: #4caf50;
  margin-right: 8px;
}

.node-output-rejected {
  background-color: #f44336;
}

/* الاتصالات بين العقد */
.workflow-connection {
  z-index: 4;
}

.workflow-connection path {
  stroke: #90a4ae;
  stroke-width: 2px;
  fill: none;
}

.workflow-connection.selected path {
  stroke: #2196F3;
  stroke-width: 3px;
}

/* خصائص العقدة */
.node-properties {
  font-size: 13px;
}

.node-properties .form-group {
  margin-bottom: 15px;
}

.node-properties label {
  font-weight: 600;
}

.no-node-selected {
  color: #9e9e9e;
  text-align: center;
  font-style: italic;
}

/* تبديل المفاتيح المتحركة (Toggle Switch) */
.switch {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 24px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
}

.slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: .4s;
}

input:checked + .slider {
  background-color: #2196F3;
}

input:focus + .slider {
  box-shadow: 0 0 1px #2196F3;
}

input:checked + .slider:before {
  transform: translateX(36px);
}

.slider.round {
  border-radius: 24px;
}

.slider.round:before {
  border-radius: 50%;
}

/* تنسيقات متنوعة */
.modal-approval-properties .modal-dialog {
  width: 500px;
}

/* تحسينات للأجهزة المحمولة */
@media (max-width: 992px) {
  .workflow-designer-container {
    flex-direction: column;
    height: auto;
  }
  
  .workflow-sidebar {
    width: 100%;
    height: 200px;
    border-right: none;
    border-bottom: 1px solid #ddd;
  }
  
  .workflow-properties-panel {
    width: 100%;
    border-left: none;
    border-top: 1px solid #ddd;
  }
} 