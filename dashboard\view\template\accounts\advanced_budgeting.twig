{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        {% if has_permission %}
        <button type="button" id="button-add" data-toggle="tooltip" title="{{ button_add }}" class="btn btn-primary">
          <i class="fa fa-plus"></i> {{ button_add }}
        </button>
        <button type="button" id="button-import" data-toggle="tooltip" title="{{ button_import }}" class="btn btn-info">
          <i class="fa fa-upload"></i> {{ button_import }}
        </button>
        <button type="button" id="button-export" data-toggle="tooltip" title="{{ button_export }}" class="btn btn-success">
          <i class="fa fa-download"></i> {{ button_export }}
        </button>
        {% endif %}
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    {% if success %}
    <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    
    <!-- إحصائيات الموازنة -->
    <div class="row">
      <div class="col-lg-3 col-xs-6">
        <div class="small-box bg-aqua">
          <div class="inner">
            <h3>{{ budget_statistics.total_budgets }}</h3>
            <p>{{ text_total_budgets }}</p>
          </div>
          <div class="icon">
            <i class="fa fa-file-text"></i>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-xs-6">
        <div class="small-box bg-green">
          <div class="inner">
            <h3>{{ budget_statistics.approved_budgets }}</h3>
            <p>{{ text_approved_budgets }}</p>
          </div>
          <div class="icon">
            <i class="fa fa-check-circle"></i>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-xs-6">
        <div class="small-box bg-yellow">
          <div class="inner">
            <h3>{{ budget_statistics.pending_budgets }}</h3>
            <p>{{ text_pending_budgets }}</p>
          </div>
          <div class="icon">
            <i class="fa fa-clock-o"></i>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-xs-6">
        <div class="small-box bg-red">
          <div class="inner">
            <h3>{{ budget_statistics.variance_percentage }}%</h3>
            <p>{{ text_average_variance }}</p>
          </div>
          <div class="icon">
            <i class="fa fa-line-chart"></i>
          </div>
        </div>
      </div>
    </div>

    <!-- فلاتر البحث -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-filter"></i> {{ text_filter }}</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-md-3">
            <div class="form-group">
              <label class="control-label">{{ text_budget_name }}</label>
              <input type="text" name="filter_budget_name" value="{{ filter_budget_name }}" placeholder="{{ text_budget_name }}" class="form-control" />
            </div>
          </div>
          <div class="col-md-3">
            <div class="form-group">
              <label class="control-label">{{ text_budget_year }}</label>
              <select name="filter_budget_year" class="form-control">
                <option value="">{{ text_all_years }}</option>
                {% for year in budget_years %}
                <option value="{{ year }}" {% if year == filter_budget_year %}selected{% endif %}>{{ year }}</option>
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="col-md-3">
            <div class="form-group">
              <label class="control-label">{{ text_department }}</label>
              <select name="filter_department" class="form-control">
                <option value="">{{ text_all_departments }}</option>
                {% for department in departments %}
                <option value="{{ department.department_id }}" {% if department.department_id == filter_department %}selected{% endif %}>{{ department.name }}</option>
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="col-md-3">
            <div class="form-group">
              <label class="control-label">{{ text_status }}</label>
              <select name="filter_status" class="form-control">
                <option value="">{{ text_all_statuses }}</option>
                <option value="draft" {% if filter_status == 'draft' %}selected{% endif %}>{{ text_status_draft }}</option>
                <option value="submitted" {% if filter_status == 'submitted' %}selected{% endif %}>{{ text_status_submitted }}</option>
                <option value="approved" {% if filter_status == 'approved' %}selected{% endif %}>{{ text_status_approved }}</option>
                <option value="rejected" {% if filter_status == 'rejected' %}selected{% endif %}>{{ text_status_rejected }}</option>
                <option value="active" {% if filter_status == 'active' %}selected{% endif %}>{{ text_status_active }}</option>
              </select>
            </div>
          </div>
        </div>
        <div class="text-right">
          <button type="button" id="button-filter" class="btn btn-default">
            <i class="fa fa-search"></i> {{ button_filter }}
          </button>
        </div>
      </div>
    </div>

    <!-- قائمة الموازنات -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-list"></i> {{ text_budget_list }}</h3>
      </div>
      <div class="panel-body">
        <form action="{{ delete }}" method="post" enctype="multipart/form-data" id="form-budget">
          <div class="table-responsive">
            <table class="table table-bordered table-hover">
              <thead>
                <tr>
                  <td style="width: 1px;" class="text-center">
                    <input type="checkbox" onclick="$('input[name*=\'selected\']').prop('checked', this.checked);" />
                  </td>
                  <td class="text-left">
                    {% if sort == 'budget_name' %}
                    <a href="{{ sort_budget_name }}" class="{% if order == 'ASC' %}asc{% else %}desc{% endif %}">{{ column_budget_name }}</a>
                    {% else %}
                    <a href="{{ sort_budget_name }}">{{ column_budget_name }}</a>
                    {% endif %}
                  </td>
                  <td class="text-left">{{ column_budget_year }}</td>
                  <td class="text-left">{{ column_department }}</td>
                  <td class="text-right">{{ column_total_amount }}</td>
                  <td class="text-center">{{ column_status }}</td>
                  <td class="text-center">{{ column_approval_level }}</td>
                  <td class="text-center">{{ column_variance }}</td>
                  <td class="text-right">{{ column_action }}</td>
                </tr>
              </thead>
              <tbody>
                {% if budgets %}
                {% for budget in budgets %}
                <tr>
                  <td class="text-center">
                    {% if budget.selected %}
                    <input type="checkbox" name="selected[]" value="{{ budget.budget_id }}" checked="checked" />
                    {% else %}
                    <input type="checkbox" name="selected[]" value="{{ budget.budget_id }}" />
                    {% endif %}
                  </td>
                  <td class="text-left">
                    <a href="{{ budget.view }}">{{ budget.budget_name }}</a>
                  </td>
                  <td class="text-left">{{ budget.budget_year }}</td>
                  <td class="text-left">{{ budget.department_name }}</td>
                  <td class="text-right">{{ budget.total_amount }}</td>
                  <td class="text-center">
                    {% if budget.status == 'draft' %}
                      <span class="label label-default">{{ text_status_draft }}</span>
                    {% elseif budget.status == 'submitted' %}
                      <span class="label label-warning">{{ text_status_submitted }}</span>
                    {% elseif budget.status == 'approved' %}
                      <span class="label label-success">{{ text_status_approved }}</span>
                    {% elseif budget.status == 'rejected' %}
                      <span class="label label-danger">{{ text_status_rejected }}</span>
                    {% elseif budget.status == 'active' %}
                      <span class="label label-info">{{ text_status_active }}</span>
                    {% endif %}
                  </td>
                  <td class="text-center">
                    <div class="progress" style="margin-bottom: 0;">
                      <div class="progress-bar progress-bar-info" style="width: {{ budget.approval_progress }}%">
                        {{ budget.current_approval_level }}/{{ budget.total_approval_levels }}
                      </div>
                    </div>
                  </td>
                  <td class="text-center">
                    {% if budget.variance_percentage %}
                      <span class="label {% if budget.variance_percentage > 10 %}label-danger{% elseif budget.variance_percentage > 5 %}label-warning{% else %}label-success{% endif %}">
                        {{ budget.variance_percentage }}%
                      </span>
                    {% else %}
                      <span class="label label-default">{{ text_no_data }}</span>
                    {% endif %}
                  </td>
                  <td class="text-right">
                    <div class="btn-group">
                      <a href="{{ budget.view }}" data-toggle="tooltip" title="{{ button_view }}" class="btn btn-info btn-xs">
                        <i class="fa fa-eye"></i>
                      </a>
                      {% if budget.edit %}
                      <a href="{{ budget.edit }}" data-toggle="tooltip" title="{{ button_edit }}" class="btn btn-primary btn-xs">
                        <i class="fa fa-pencil"></i>
                      </a>
                      {% endif %}
                      {% if budget.approve %}
                      <a href="{{ budget.approve }}" data-toggle="tooltip" title="{{ button_approve }}" class="btn btn-success btn-xs">
                        <i class="fa fa-check"></i>
                      </a>
                      {% endif %}
                      {% if budget.analyze %}
                      <a href="{{ budget.analyze }}" data-toggle="tooltip" title="{{ button_analyze }}" class="btn btn-warning btn-xs">
                        <i class="fa fa-line-chart"></i>
                      </a>
                      {% endif %}
                    </div>
                  </td>
                </tr>
                {% endfor %}
                {% else %}
                <tr>
                  <td class="text-center" colspan="9">{{ text_no_results }}</td>
                </tr>
                {% endif %}
              </tbody>
            </table>
          </div>
        </form>
        <div class="row">
          <div class="col-sm-6 text-left">{{ pagination }}</div>
          <div class="col-sm-6 text-right">{{ results }}</div>
        </div>
      </div>
    </div>

    <!-- الموازنات المعلقة للموافقة -->
    {% if pending_approvals %}
    <div class="panel panel-warning">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-exclamation-triangle"></i> {{ text_pending_approvals }}</h3>
      </div>
      <div class="panel-body">
        <div class="table-responsive">
          <table class="table table-bordered table-striped">
            <thead>
              <tr>
                <th>{{ column_budget_name }}</th>
                <th>{{ column_submitted_by }}</th>
                <th>{{ column_submission_date }}</th>
                <th>{{ column_current_level }}</th>
                <th>{{ column_action }}</th>
              </tr>
            </thead>
            <tbody>
              {% for approval in pending_approvals %}
              <tr>
                <td>{{ approval.budget_name }}</td>
                <td>{{ approval.submitted_by_name }}</td>
                <td>{{ approval.submission_date }}</td>
                <td>{{ approval.current_approval_level }}</td>
                <td>
                  <a href="{{ approval.review }}" class="btn btn-sm btn-primary">
                    <i class="fa fa-eye"></i> {{ button_review }}
                  </a>
                </td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    </div>
    {% endif %}

  </div>
</div>

<!-- نموذج إضافة موازنة جديدة -->
<div id="modal-budget" class="modal fade">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
        <h4 class="modal-title">{{ text_add_budget }}</h4>
      </div>
      <div class="modal-body">
        <form action="{{ add }}" method="post" enctype="multipart/form-data" id="form-budget-add">
          <div class="form-group required">
            <label class="control-label">{{ entry_budget_name }}</label>
            <input type="text" name="budget_name" value="" placeholder="{{ entry_budget_name }}" class="form-control" />
          </div>
          <div class="row">
            <div class="col-md-6">
              <div class="form-group required">
                <label class="control-label">{{ entry_budget_year }}</label>
                <select name="budget_year" class="form-control">
                  {% for year in budget_years %}
                  <option value="{{ year }}">{{ year }}</option>
                  {% endfor %}
                </select>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group required">
                <label class="control-label">{{ entry_department }}</label>
                <select name="department_id" class="form-control">
                  <option value="">{{ text_select_department }}</option>
                  {% for department in departments %}
                  <option value="{{ department.department_id }}">{{ department.name }}</option>
                  {% endfor %}
                </select>
              </div>
            </div>
          </div>
          <div class="form-group">
            <label class="control-label">{{ entry_description }}</label>
            <textarea name="description" rows="3" placeholder="{{ entry_description }}" class="form-control"></textarea>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_cancel }}</button>
        <button type="button" id="button-budget-save" class="btn btn-primary">{{ button_save }}</button>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript">
$(document).ready(function() {
    // إضافة موازنة جديدة
    $('#button-add').on('click', function() {
        $('#modal-budget').modal('show');
    });

    // حفظ الموازنة الجديدة
    $('#button-budget-save').on('click', function() {
        $.ajax({
            url: '{{ add }}',
            type: 'post',
            data: $('#form-budget-add').serialize(),
            dataType: 'json',
            success: function(json) {
                if (json['success']) {
                    window.location = json['redirect'];
                } else {
                    alert(json['error']);
                }
            }
        });
    });

    // فلترة النتائج
    $('#button-filter').on('click', function() {
        var url = 'index.php?route=accounts/advanced_budgeting&user_token={{ user_token }}';
        
        var filter_budget_name = $('input[name=\'filter_budget_name\']').val();
        if (filter_budget_name) {
            url += '&filter_budget_name=' + encodeURIComponent(filter_budget_name);
        }

        var filter_budget_year = $('select[name=\'filter_budget_year\']').val();
        if (filter_budget_year) {
            url += '&filter_budget_year=' + encodeURIComponent(filter_budget_year);
        }

        var filter_department = $('select[name=\'filter_department\']').val();
        if (filter_department) {
            url += '&filter_department=' + encodeURIComponent(filter_department);
        }

        var filter_status = $('select[name=\'filter_status\']').val();
        if (filter_status) {
            url += '&filter_status=' + encodeURIComponent(filter_status);
        }

        location = url;
    });

    // تصدير البيانات
    $('#button-export').on('click', function() {
        window.open('{{ export }}', '_blank');
    });

    // استيراد البيانات
    $('#button-import').on('click', function() {
        $('#modal-import').modal('show');
    });
});
</script>

{{ footer }}
