{{ header }}
{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="submit" form="form-category" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fa fa-save"></i></button>
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pencil"></i> {{ text_form }}</h3>
      </div>
      <div class="panel-body">
        <form action="{{ action }}" method="post" enctype="multipart/form-data" id="form-category" class="form-horizontal">
          <ul class="nav nav-tabs">
            <li class="active"><a href="#tab-general" data-toggle="tab">{{ tab_general }}</a></li>
            <li><a href="#tab-data" data-toggle="tab">{{ tab_data }}</a></li>
            <li><a href="#tab-seo" data-toggle="tab">{{ tab_seo }}</a></li>
            {% if category_posts is defined and category_posts %}
            <li><a href="#tab-posts" data-toggle="tab">{{ tab_posts }}</a></li>
            {% endif %}
          </ul>
          <div class="tab-content">
            <div class="tab-pane active" id="tab-general">
              <div class="form-group required">
                <label class="col-sm-2 control-label" for="input-name">{{ entry_name }}</label>
                <div class="col-sm-10">
                  <input type="text" name="name" value="{{ name }}" placeholder="{{ entry_name }}" id="input-name" class="form-control" />
                  {% if error_name %}
                  <div class="text-danger">{{ error_name }}</div>
                  {% endif %}
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-parent">{{ entry_parent }}</label>
                <div class="col-sm-10">
                  <select name="parent_id" id="input-parent" class="form-control">
                    <option value="0">{{ text_none }}</option>
                    {% for category in categories %}
                      {% if category_id is defined and category.category_id != category_id %}
                        {% if category.category_id == parent_id %}
                          <option value="{{ category.category_id }}" selected="selected">{{ category.name }}</option>
                        {% else %}
                          <option value="{{ category.category_id }}">{{ category.name }}</option>
                        {% endif %}
                      {% elseif category_id is not defined %}
                        <option value="{{ category.category_id }}">{{ category.name }}</option>
                      {% endif %}
                    {% endfor %}
                  </select>
                  <span class="help-block">{{ help_parent }}</span>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-description">{{ entry_description }}</label>
                <div class="col-sm-10">
                  <textarea name="description" placeholder="{{ entry_description }}" id="input-description" class="form-control">{{ description }}</textarea>
                </div>
              </div>
            </div>
            <div class="tab-pane" id="tab-data">
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-slug">{{ entry_slug }}</label>
                <div class="col-sm-10">
                  <div class="input-group">
                    <input type="text" name="slug" value="{{ slug }}" placeholder="{{ entry_slug }}" id="input-slug" class="form-control" />
                    <span class="input-group-btn">
                      <button type="button" id="button-generate-slug" class="btn btn-default"><i class="fa fa-refresh"></i> {{ button_generate }}</button>
                    </span>
                  </div>
                  {% if error_slug %}
                  <div class="text-danger">{{ error_slug }}</div>
                  {% endif %}
                  <span class="help-block">{{ help_slug }}</span>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-status">{{ entry_status }}</label>
                <div class="col-sm-10">
                  <select name="status" id="input-status" class="form-control">
                    {% if status %}
                    <option value="1" selected="selected">{{ text_enabled }}</option>
                    <option value="0">{{ text_disabled }}</option>
                    {% else %}
                    <option value="1">{{ text_enabled }}</option>
                    <option value="0" selected="selected">{{ text_disabled }}</option>
                    {% endif %}
                  </select>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-sort-order">{{ entry_sort_order }}</label>
                <div class="col-sm-10">
                  <input type="text" name="sort_order" value="{{ sort_order }}" placeholder="{{ entry_sort_order }}" id="input-sort-order" class="form-control" />
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-image">{{ entry_image }}</label>
                <div class="col-sm-10">
                  <a href="" id="thumb-image" data-toggle="image" class="img-thumbnail"><img src="{{ thumb }}" alt="" title="" data-placeholder="{{ placeholder }}" /></a>
                  <input type="hidden" name="image" value="{{ image }}" id="input-image" />
                </div>
              </div>
            </div>
            <div class="tab-pane" id="tab-seo">
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-meta-title">{{ entry_meta_title }}</label>
                <div class="col-sm-10">
                  <input type="text" name="meta_title" value="{{ meta_title }}" placeholder="{{ entry_meta_title }}" id="input-meta-title" class="form-control" />
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-meta-description">{{ entry_meta_description }}</label>
                <div class="col-sm-10">
                  <textarea name="meta_description" rows="5" placeholder="{{ entry_meta_description }}" id="input-meta-description" class="form-control">{{ meta_description }}</textarea>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-meta-keywords">{{ entry_meta_keywords }}</label>
                <div class="col-sm-10">
                  <input type="text" name="meta_keywords" value="{{ meta_keywords }}" placeholder="{{ entry_meta_keywords }}" id="input-meta-keywords" class="form-control" />
                </div>
              </div>
            </div>
            {% if category_posts is defined and category_posts %}
            <div class="tab-pane" id="tab-posts">
              <div class="table-responsive">
                <table class="table table-bordered table-hover">
                  <thead>
                    <tr>
                      <td class="text-left">{{ column_title }}</td>
                      <td class="text-center">{{ column_status }}</td>
                      <td class="text-center">{{ column_date_published }}</td>
                      <td class="text-right">{{ column_action }}</td>
                    </tr>
                  </thead>
                  <tbody>
                    {% for post in category_posts %}
                    <tr>
                      <td class="text-left">{{ post.title }}</td>
                      <td class="text-center">
                        {% if post.status %}
                        <span class="label label-success">{{ text_enabled }}</span>
                        {% else %}
                        <span class="label label-danger">{{ text_disabled }}</span>
                        {% endif %}
                      </td>
                      <td class="text-center">{{ post.date_published }}</td>
                      <td class="text-right">
                        <a href="index.php?route=catalog/blog/edit&user_token={{ user_token }}&post_id={{ post.post_id }}" data-toggle="tooltip" title="{{ button_edit }}" class="btn btn-primary"><i class="fa fa-pencil"></i></a>
                      </td>
                    </tr>
                    {% endfor %}
                  </tbody>
                </table>
              </div>
            </div>
            {% endif %}
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript"><!--
// محرر النصوص الغني
$('#input-description').summernote({
  height: 300
});

// توليد slug تلقائياً
$('#button-generate-slug').on('click', function() {
  var name = $('#input-name').val();
  if (name) {
    $.ajax({
      url: 'index.php?route=catalog/blog_category/generateSlug&user_token={{ user_token }}',
      type: 'POST',
      data: { title: name },
      dataType: 'json',
      success: function(json) {
        if (json.success) {
          $('#input-slug').val(json.slug);
        }
      }
    });
  }
});

// إنشاء عنوان ميتا تلقائياً إذا كان فارغاً
$('#input-name').on('blur', function() {
  if ($('#input-meta-title').val() == '') {
    $('#input-meta-title').val($(this).val());
  }
});
//--></script>

{{ footer }}