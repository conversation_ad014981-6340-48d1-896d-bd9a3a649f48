<?php
/**
 * Enhanced Queue System for AYM ERP
 * Improved version with priorities, monitoring, and cleanup
 */
class EnhancedQueue {
    private $db;
    private $max_attempts = 3;
    private $cleanup_after_days = 7; // تنظيف المهام المكتملة بعد 7 أيام
    private $batch_size = 50; // معالجة 50 مهمة في المرة الواحدة
    
    // أولويات المهام
    const PRIORITY_LOW = 1;
    const PRIORITY_NORMAL = 2;
    const PRIORITY_HIGH = 3;
    const PRIORITY_CRITICAL = 4;
    
    // حالات المهام
    const STATUS_PENDING = 'pending';
    const STATUS_PROCESSING = 'processing';
    const STATUS_DONE = 'done';
    const STATUS_FAILED = 'failed';
    const STATUS_CANCELLED = 'cancelled';
    
    public function __construct($db) {
        $this->db = $db;
        $this->initializeDatabase();
    }
    
    /**
     * إنشاء جدول Queue محسن إذا لم يكن موجوداً
     */
    private function initializeDatabase() {
        $sql = "CREATE TABLE IF NOT EXISTS `" . DB_PREFIX . "enhanced_queue_jobs` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `job_type` varchar(100) NOT NULL,
            `job_data` text NOT NULL,
            `priority` tinyint(1) NOT NULL DEFAULT 2,
            `status` enum('pending','processing','done','failed','cancelled') NOT NULL DEFAULT 'pending',
            `attempts` int(3) NOT NULL DEFAULT 0,
            `max_attempts` int(3) NOT NULL DEFAULT 3,
            `scheduled_at` datetime DEFAULT NULL,
            `started_at` datetime DEFAULT NULL,
            `completed_at` datetime DEFAULT NULL,
            `error_message` text DEFAULT NULL,
            `processing_time` decimal(10,3) DEFAULT NULL,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_status_priority` (`status`, `priority` DESC),
            KEY `idx_job_type` (`job_type`),
            KEY `idx_scheduled_at` (`scheduled_at`),
            KEY `idx_created_at` (`created_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $this->db->query($sql);
    }
    
    /**
     * إضافة مهمة جديدة إلى Queue
     */
    public function addJob($job_type, $job_data, $priority = self::PRIORITY_NORMAL, $scheduled_at = null, $max_attempts = null) {
        $max_attempts = $max_attempts ?: $this->max_attempts;
        $scheduled_at = $scheduled_at ?: date('Y-m-d H:i:s');
        
        $sql = "INSERT INTO `" . DB_PREFIX . "enhanced_queue_jobs` SET 
                `job_type` = '" . $this->db->escape($job_type) . "',
                `job_data` = '" . $this->db->escape(json_encode($job_data)) . "',
                `priority` = '" . (int)$priority . "',
                `max_attempts` = '" . (int)$max_attempts . "',
                `scheduled_at` = '" . $this->db->escape($scheduled_at) . "'";
        
        $this->db->query($sql);
        return $this->db->getLastId();
    }
    
    /**
     * الحصول على المهام المعلقة مرتبة حسب الأولوية والوقت
     */
    public function getPendingJobs($limit = null) {
        $limit = $limit ?: $this->batch_size;
        
        $sql = "SELECT * FROM `" . DB_PREFIX . "enhanced_queue_jobs` 
                WHERE (`status` = '" . self::STATUS_PENDING . "' OR 
                       (`status` = '" . self::STATUS_FAILED . "' AND `attempts` < `max_attempts`))
                AND (`scheduled_at` IS NULL OR `scheduled_at` <= NOW())
                ORDER BY `priority` DESC, `created_at` ASC 
                LIMIT " . (int)$limit;
        
        $query = $this->db->query($sql);
        return $query->rows;
    }
    
    /**
     * بدء معالجة مهمة
     */
    public function startProcessing($job_id) {
        $sql = "UPDATE `" . DB_PREFIX . "enhanced_queue_jobs` SET 
                `status` = '" . self::STATUS_PROCESSING . "',
                `started_at` = NOW(),
                `attempts` = `attempts` + 1
                WHERE `id` = '" . (int)$job_id . "'
                AND `status` IN ('" . self::STATUS_PENDING . "', '" . self::STATUS_FAILED . "')";
        
        $this->db->query($sql);
        return $this->db->countAffected() > 0;
    }
    
    /**
     * تسجيل نجاح المهمة
     */
    public function markJobAsCompleted($job_id, $processing_time = null) {
        $processing_time_sql = '';
        if ($processing_time !== null) {
            $processing_time_sql = ", `processing_time` = '" . (float)$processing_time . "'";
        }
        
        $sql = "UPDATE `" . DB_PREFIX . "enhanced_queue_jobs` SET 
                `status` = '" . self::STATUS_DONE . "',
                `completed_at` = NOW(),
                `error_message` = NULL
                " . $processing_time_sql . "
                WHERE `id` = '" . (int)$job_id . "'";
        
        $this->db->query($sql);
    }
    
    /**
     * تسجيل فشل المهمة
     */
    public function markJobAsFailed($job_id, $error_message = null) {
        $sql = "UPDATE `" . DB_PREFIX . "enhanced_queue_jobs` SET 
                `status` = '" . self::STATUS_FAILED . "',
                `completed_at` = NOW(),
                `error_message` = '" . $this->db->escape($error_message) . "'
                WHERE `id` = '" . (int)$job_id . "'";
        
        $this->db->query($sql);
    }
    
    /**
     * إلغاء مهمة
     */
    public function cancelJob($job_id) {
        $sql = "UPDATE `" . DB_PREFIX . "enhanced_queue_jobs` SET 
                `status` = '" . self::STATUS_CANCELLED . "',
                `completed_at` = NOW()
                WHERE `id` = '" . (int)$job_id . "'
                AND `status` IN ('" . self::STATUS_PENDING . "', '" . self::STATUS_FAILED . "')";
        
        $this->db->query($sql);
        return $this->db->countAffected() > 0;
    }
    
    /**
     * الحصول على إحصائيات Queue
     */
    public function getQueueStats() {
        $sql = "SELECT 
                    `status`,
                    COUNT(*) as `count`,
                    AVG(`processing_time`) as `avg_processing_time`
                FROM `" . DB_PREFIX . "enhanced_queue_jobs` 
                WHERE `created_at` >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
                GROUP BY `status`";
        
        $query = $this->db->query($sql);
        $stats = [];
        
        foreach ($query->rows as $row) {
            $stats[$row['status']] = [
                'count' => (int)$row['count'],
                'avg_processing_time' => (float)$row['avg_processing_time']
            ];
        }
        
        return $stats;
    }
    
    /**
     * تنظيف المهام القديمة المكتملة
     */
    public function cleanupOldJobs() {
        $sql = "DELETE FROM `" . DB_PREFIX . "enhanced_queue_jobs` 
                WHERE `status` IN ('" . self::STATUS_DONE . "', '" . self::STATUS_CANCELLED . "')
                AND `completed_at` < DATE_SUB(NOW(), INTERVAL " . (int)$this->cleanup_after_days . " DAY)";
        
        $this->db->query($sql);
        return $this->db->countAffected();
    }
    
    /**
     * إعادة تشغيل المهام المعلقة في حالة معالجة
     */
    public function resetStuckJobs($timeout_minutes = 30) {
        $sql = "UPDATE `" . DB_PREFIX . "enhanced_queue_jobs` SET 
                `status` = '" . self::STATUS_FAILED . "',
                `error_message` = 'Job timed out - reset by cleanup process'
                WHERE `status` = '" . self::STATUS_PROCESSING . "'
                AND `started_at` < DATE_SUB(NOW(), INTERVAL " . (int)$timeout_minutes . " MINUTE)";
        
        $this->db->query($sql);
        return $this->db->countAffected();
    }
    
    /**
     * الحصول على تفاصيل مهمة محددة
     */
    public function getJobDetails($job_id) {
        $sql = "SELECT * FROM `" . DB_PREFIX . "enhanced_queue_jobs` WHERE `id` = '" . (int)$job_id . "'";
        $query = $this->db->query($sql);
        
        if ($query->num_rows > 0) {
            $job = $query->row;
            $job['job_data'] = json_decode($job['job_data'], true);
            return $job;
        }
        
        return null;
    }
    
    /**
     * الحصول على المهام حسب النوع
     */
    public function getJobsByType($job_type, $status = null, $limit = 100) {
        $where_status = '';
        if ($status) {
            $where_status = " AND `status` = '" . $this->db->escape($status) . "'";
        }
        
        $sql = "SELECT * FROM `" . DB_PREFIX . "enhanced_queue_jobs` 
                WHERE `job_type` = '" . $this->db->escape($job_type) . "'
                " . $where_status . "
                ORDER BY `created_at` DESC 
                LIMIT " . (int)$limit;
        
        $query = $this->db->query($sql);
        return $query->rows;
    }
    
    /**
     * الحصول على حجم جدول Queue في قاعدة البيانات
     * Get the database table size for monitoring
     */
    public function getTableSize() {
        $sql = "SELECT 
                    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS size_mb 
                FROM 
                    information_schema.TABLES 
                WHERE 
                    table_schema = DATABASE() 
                    AND table_name = '" . DB_PREFIX . "enhanced_queue_jobs'";
        
        $query = $this->db->query($sql);
        
        if ($query->num_rows > 0) {
            return $query->row['size_mb'];
        }
        
        return 0;
    }
}
?>