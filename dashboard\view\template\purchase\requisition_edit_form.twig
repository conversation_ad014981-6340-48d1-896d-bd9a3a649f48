<form id="form-edit-requisition">
  <input type="hidden" name="requisition_id" value="{{ requisition.requisition_id }}">
    <div class="modal-header">
      <button type="button" class="close" data-dismiss="modal">
        <span>×</span>
      </button>
      <h4 class="modal-title">{{ text_edit_requisition }}</h4>
  </div>
    <div class="modal-body">
        <!-- ** نظام التابات (Tabs) ** -->
        <ul class="nav nav-tabs">
            <li class="active"><a href="#tab-general" data-toggle="tab">{{ tab_general }}</a></li>
            <li><a href="#tab-items" data-toggle="tab">{{ tab_items }}</a></li>
        </ul>
        <div class="tab-content" style="padding-top: 15px;">
            <!-- ** التاب العام (General Tab) ** -->
            <div class="tab-pane active" id="tab-general">
                 <div class="form-group">
                    <label>{{ entry_branch }}</label>
                    <select name="branch_id" class="form-control select2-branch" required>
                        <option value="">{{ text_select }}</option>
                        {% for br in branches %}
                        <option value="{{ br.branch_id }}" {% if br.branch_id == requisition.branch_id %}selected{% endif %}>{{ br.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="form-group">
                  <label>{{ entry_user_group }}</label>
                  <select name="user_group_id" class="form-control select2-user-group" required>
                      <option value="">{{ text_select }}</option>
                      {% for ug in user_groups %}
                          <option value="{{ ug.user_group_id }}" {% if ug.user_group_id == requisition.user_group_id %}selected{% endif %}>{{ ug.name }}</option>
                     {% endfor %}
                   </select>
                </div>
                <div class="form-group">
                    <label>{{ entry_required_date }}</label>
                  <input type="date" name="required_date" class="form-control" value="{{ requisition.required_date }}">
                </div>
                <div class="form-group">
                    <label>{{ entry_priority }}</label>
                    <select name="priority" class="form-control select2-priority">
                      <option value="low"    {{ requisition.priority == 'low'    ? 'selected' : ''}}>{{ text_priority_low }}</option>
                        <option value="medium" {{ requisition.priority == 'medium' ? 'selected' : ''}}>{{ text_priority_medium }}</option>
                        <option value="high"   {{ requisition.priority == 'high'  ? 'selected' : ''}}>{{ text_priority_high }}</option>
                        <option value="urgent" {{ requisition.priority == 'urgent' ? 'selected' : ''}}>{{ text_priority_urgent }}</option>
                  </select>
                </div>
                <div class="form-group">
                    <label>{{ entry_notes }}</label>
                    <textarea name="notes" rows="3" class="form-control">{{ requisition.notes }}</textarea>
                </div>
            </div>
            <!-- ** تاب البنود (Items Tab) ** -->
             <div class="tab-pane" id="tab-items">
              <div class="form-group">
                    <button type="button" id="btn-edit-add-item" class="btn btn-info">
                        <i class="fa fa-plus"></i> {{ text_add_item }}
                    </button>
                </div>
                <div class="table-responsive">
                    <table id="edit-req-items" class="table table-bordered">
                        <thead>
                            <tr>
                                <th>{{ column_product }}</th>
                                <th>{{ column_quantity }}</th>
                                 <th>{{ column_unit }}</th>
                                <th>{{ column_description }}</th>
                                <th style="width:50px;"></th>
                            </tr>
                        </thead>
                       <tbody>
                           {% if items %}
                             {% for it in items %}
                             <tr>
                               <td style="width:35%;">
                                    <select class="form-control product-select" name="item_product_id[]">
                                        <option value="{{ it.product_id }}" selected>{{ it.product_name }}</option>
                                    </select>
                                    <div class="product-details" style="margin-top: 10px; font-size: 0.9em;"></div>
                                    <div class="pending-requisitions" style="margin-top: 10px; font-size: 0.9em;"></div> {# Placeholder for pending reqs #}
                                </td>
                                <td style="width:10%;">
                                    <input type="number" step="0.01" class="form-control" name="item_quantity[]" value="{{ it.quantity }}">
                                 </td>
                                 <td style="width:15%;">
                                    <select name="item_unit_id[]" class="form-control unit-select">
                                       <option value="{{ it.unit_id }}" selected>{{ it.unit_name }}</option>
                                    </select>
                                 </td>
                                 <td style="width:30%;">
                                    <input type="text" class="form-control" name="item_description[]" value="{{ it.description }}">
                                 </td>
                                <td style="width:10%;">
                                  <button type="button" class="btn btn-danger btn-remove-line">
                                      <i class="fa fa-trash"></i>
                                  </button>
                                 </td>
                           </tr>
                            {% endfor %}
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div><!--/tab content-->
  </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">
           {{ text_close}}
       </button>
      <button type="submit" class="btn btn-primary">
           {{ button_save }}
        </button>
  </div>
</form>

<script>
     $('#btn-edit-add-item').on('click', function(){
          var newRow = $('#template-edit-item').html();
          $('#edit-req-items tbody').append(newRow);
         var $latest = $('#edit-req-items tbody tr:last').find('.product-select');
          initSelect2Product($latest);
      });

      $('#edit-req-items').on('click','.btn-remove-line', function(){
        $(this).closest('tr').remove();
     });
   
     function initSelect2Product($elem){
        $elem.select2({
              placeholder: "<?php echo $this->language->get('text_select_product');?>",
            allowClear: true,
            dropdownParent: $elem.parent(),
              ajax: {
                 url:'index.php?route=purchase/requisition/select2Product&user_token=<?php echo $this->session->data['user_token'];?>',
                   dataType:'json',
                 delay:250,
                 data:function(params){return {q:params.term};},
                 processResults:function(data){return {results:data};}
              }
          }).on('select2:select', function(e){
                var item = e.params.data;
                 var $row = $(this).closest('tr');
                var $row = $(this).closest('tr');
                var $unitSelect = $row.find('.unit-select');
                var $detailsDiv = $row.find('.product-details');
                var $pendingReqsDiv = $row.find('.pending-requisitions'); // Get pending reqs div
                var branchId = $('#form-edit-requisition select[name="branch_id"]').val(); // Get selected branch ID
                var currentRequisitionId = $('#form-edit-requisition input[name="requisition_id"]').val(); // Get current req ID

                $unitSelect.empty();
                $detailsDiv.empty();
                $pendingReqsDiv.empty(); // Clear previous pending reqs

                if (!branchId) {
                    $detailsDiv.html('<div class="alert alert-warning">Please select a branch first.</div>');
                    return; // Stop if no branch selected
                }

                if(item.units){
                    $.each(item.units,function(i,u){
                        var opt = new Option(u.text,u.id,false,false);
                        $unitSelect.append(opt);
                    });
                    $unitSelect.trigger('change'); // Trigger change if needed
                }

                if(item.id){
                    // Fetch Product Details (Stock & Cost for the selected branch)
                    $.ajax({
                        url: 'index.php?route=purchase/requisition/ajaxGetProductDetails&user_token={{ user_token }}&product_id=' + item.id + '&branch_id=' + branchId,
                        type: 'get',
                        dataType: 'json',
                        success: function(details){
                            if(details.units && details.units.length > 0){
                                var detailsHtml = '<strong style="color: #337ab7;">{{ text_stock_branch }}:</strong><br>'; // Add title
                                detailsHtml += '<table class="table table-condensed table-bordered" style="margin-bottom: 5px;">';
                                detailsHtml += '<thead><tr><th>{{ column_unit }}</th><th>{{ column_avg_cost }}</th><th>{{ column_stock_available }}</th></tr></thead><tbody>';
                                $.each(details.units, function(i, unit){
                                    detailsHtml += '<tr>';
                                    detailsHtml += '<td>'+unit.unit_name+'</td>';
                                    detailsHtml += '<td>'+ parseFloat(unit.average_cost).toFixed(4) +'</td>'; // Format cost
                                    detailsHtml += '<td>'+ parseFloat(unit.quantity_available).toFixed(2) +'</td>'; // Format quantity
                                    detailsHtml += '</tr>';
                                });
                                detailsHtml += '</tbody></table>';
                                $detailsDiv.html(detailsHtml);
                            } else {
                                $detailsDiv.html('<div class="text-muted">{{ text_no_stock_data }}</div>');
                            }
                        },
                        error: function(){
                            $detailsDiv.html('<div class="alert alert-danger">{{ error_loading_stock }}</div>');
                        }
                    });

                    // Fetch Pending Requisitions for this product
                    $.ajax({
                        url: 'index.php?route=purchase/requisition/ajaxGetPendingRequisitions&user_token={{ user_token }}&product_id=' + item.id + '&exclude_requisition_id=' + currentRequisitionId,
                        type: 'get',
                        dataType: 'json',
                        success: function(pendingData){
                            if(pendingData.pending_requisitions && pendingData.pending_requisitions.length > 0){
                                var pendingHtml = '<strong style="color: #d9534f;">{{ text_pending_reqs_product }}:</strong><br>'; // Add title
                                pendingHtml += '<table class="table table-condensed table-bordered" style="margin-bottom: 5px;">';
                                pendingHtml += '<thead><tr><th>{{ column_req_number }}</th><th>{{ column_branch }}</th><th>{{ column_quantity }}</th><th>{{ column_unit }}</th><th>{{ column_date_added }}</th></tr></thead><tbody>';
                                $.each(pendingData.pending_requisitions, function(i, req){
                                    pendingHtml += '<tr>';
                                    pendingHtml += '<td>'+req.req_number+'</td>';
                                    pendingHtml += '<td>'+req.branch_name+'</td>';
                                    pendingHtml += '<td>'+ parseFloat(req.quantity).toFixed(2) +'</td>';
                                    pendingHtml += '<td>'+req.unit_name+'</td>';
                                    pendingHtml += '<td>'+req.created_at.substring(0, 10)+'</td>'; // Just date part
                                    pendingHtml += '</tr>';
                                });
                                pendingHtml += '</tbody></table>';
                                $pendingReqsDiv.html(pendingHtml);
                            } else {
                                $pendingReqsDiv.html('<div class="text-muted">{{ text_no_pending_reqs }}</div>');
                            }
                        },
                        error: function(){
                            $pendingReqsDiv.html('<div class="alert alert-danger">{{ error_loading_pending_reqs }}</div>');
                        }
                    });
                }
            });
        }

       $('#edit-req-items .product-select').each(function(){
           initSelect2Product($(this));
       });
       
        $('#form-edit-requisition').on('submit', function(e){
          e.preventDefault();
          $.ajax({
            url:'index.php?route=purchase/requisition/ajaxEditRequisition&user_token={{ user_token }}',
           type:'post',
            dataType:'json',
             data:$(this).serialize(),
            success:function(json){
               if(json.error){
                 alert(json.error);
                }
               if(json.success){
                 $('#modal-edit-requisition').modal('hide');
                    loadRequisitions();
               }
           }
        });
       });
</script>

<table style="display:none;">
<tbody id="template-edit-item">
    <tr>
        <td style="width:35%;">
            <select class="form-control product-select" name="item_product_id[]"></select>
            <div class="product-details" style="margin-top: 10px; font-size: 0.9em;"></div>
            <div class="pending-requisitions" style="margin-top: 10px; font-size: 0.9em;"></div> {# Placeholder for pending reqs #}
        </td>
        <td style="width:10%;">
                <input type="number" name="item_quantity[]" step="0.01" class="form-control" value="1" min="1" required>
            </td>
            <td style="width:15%;">
             <select name="item_unit_id[]" class="form-control unit-select" required></select>
            </td>
            <td style="width:30%;">
                <input type="text" name="item_description[]" class="form-control">
           </td>
           <td style="width:10%;">
                <button type="button" class="btn btn-danger btn-remove-line"><i class="fa fa-trash"></i></button>
            </td>
       </tr>
    </tbody>
</table>
