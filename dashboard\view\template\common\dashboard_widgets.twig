{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="close" data-dismiss="alert">&times;</button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="close" data-dismiss="alert">&times;</button>
      </div>
    {{% endif %}}

    <div class="panel panel-default">
      <div class="panel-heading"><i class="fa fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}bar-chart{% elif screen_type == 'settings' %}cog{% else %}pencil{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="panel-body">
        <div class="row">
          <!-- KPI Cards -->
          <div class="col-lg-3 col-md-6">
            <div class="panel panel-primary">
              <div class="panel-body">
                <div class="row">
                  <div class="col-xs-8">
                    <h5>{{ text_total_sales }}</h5>
                    <h3>{{ total_sales }}</h3>
                  </div>
                  <div class="col-xs-4 text-right">
                    <i class="fa fa-shopping-cart fa-3x"></i>
                  </div>
                </div>
              </div>
              <div class="panel-footer">
                <a href="{{ sales_url }}" class="text-white">{{ text_view_details }}</a>
                <span class="pull-right">
                  {% if sales_growth > 0 %}
                  <i class="fa fa-arrow-up"></i> {{ sales_growth }}%
                  {% else %}
                  <i class="fa fa-arrow-down"></i> {{ sales_growth|abs }}%
                  {% endif %}
                </span>
              </div>
            </div>
          </div>
          
          <div class="col-lg-3 col-md-6">
            <div class="panel panel-success">
              <div class="panel-body">
                <div class="row">
                  <div class="col-xs-8">
                    <h5>{{ text_total_customers }}</h5>
                    <h3>{{ total_customers }}</h3>
                  </div>
                  <div class="col-xs-4 text-right">
                    <i class="fa fa-users fa-3x"></i>
                  </div>
                </div>
              </div>
              <div class="panel-footer">
                <a href="{{ customers_url }}" class="text-white">{{ text_view_details }}</a>
                <span class="pull-right">
                  {% if customer_growth > 0 %}
                  <i class="fa fa-arrow-up"></i> {{ customer_growth }}%
                  {% else %}
                  <i class="fa fa-arrow-down"></i> {{ customer_growth|abs }}%
                  {% endif %}
                </span>
              </div>
            </div>
          </div>
          
          <div class="col-lg-3 col-md-6">
            <div class="panel panel-info">
              <div class="panel-body">
                <div class="row">
                  <div class="col-xs-8">
                    <h5>{{ text_total_products }}</h5>
                    <h3>{{ total_products }}</h3>
                  </div>
                  <div class="col-xs-4 text-right">
                    <i class="fa fa-cube fa-3x"></i>
                  </div>
                </div>
              </div>
              <div class="panel-footer">
                <a href="{{ products_url }}" class="text-white">{{ text_view_details }}</a>
                <span class="pull-right">
                  {% if product_count_change > 0 %}
                  <i class="fa fa-arrow-up"></i> {{ product_count_change }}
                  {% else %}
                  <i class="fa fa-arrow-down"></i> {{ product_count_change|abs }}
                  {% endif %}
                </span>
              </div>
            </div>
          </div>

          <div class="col-lg-3 col-md-6">
            <div class="panel panel-warning">
              <div class="panel-body">
                <div class="row">
                  <div class="col-xs-8">
                    <h5>{{ text_total_orders }}</h5>
                    <h3>{{ total_orders }}</h3>
                  </div>
                  <div class="col-xs-4 text-right">
                    <i class="fa fa-file-text-o fa-3x"></i>
                  </div>
                </div>
              </div>
              <div class="panel-footer">
                <a href="{{ orders_url }}" class="text-white">{{ text_view_details }}</a>
                <span class="pull-right">
                  {% if order_growth > 0 %}
                  <i class="fa fa-arrow-up"></i> {{ order_growth }}%
                  {% else %}
                  <i class="fa fa-arrow-down"></i> {{ order_growth|abs }}%
                  {% endif %}
                </span>
              </div>
            </div>
          </div>
        </div>
        
        <div class="row">
          <!-- Chart Section -->
          <div class="col-lg-8 col-md-12">
            <div class="panel panel-default">
              <div class="panel-heading">
                <div class="row">
                  <div class="col-xs-6">
                    <h5><i class="fa fa-line-chart"></i> {{ text_sales_analytics }}</h5>
                  </div>
                  <div class="col-xs-6 text-right">
                    <div class="btn-group" role="group">
                      <button type="button" class="btn btn-sm btn-default active" id="btn-week">{{ text_week }}</button>
                      <button type="button" class="btn btn-sm btn-default" id="btn-month">{{ text_month }}</button>
                      <button type="button" class="btn btn-sm btn-default" id="btn-year">{{ text_year }}</button>
                    </div>
                  </div>
                </div>
              </div>
              <div class="panel-body">
                <canvas id="sales-chart" height="300"></canvas>
              </div>
            </div>
          </div>

          <!-- Recent Activity -->
          <div class="col-lg-4 col-md-12">
            <div class="panel panel-default">
              <div class="panel-heading">
                <h5><i class="fa fa-history"></i> {{ text_recent_activity }}</h5>
              </div>
              <div class="panel-body" style="padding: 0;">
                <div class="list-group">
                  {% if activities %}
                    {% for activity in activities %}
                      <div class="list-group-item">
                        <div class="row">
                          <div class="col-xs-9">
                            <h6>{{ activity.title }}</h6>
                            <p>{{ activity.description }}</p>
                            <small class="text-muted">{{ activity.user }}</small>
                          </div>
                          <div class="col-xs-3 text-right">
                            <small>{{ activity.time_ago }}</small>
                          </div>
                        </div>
                      </div>
                    {% endfor %}
                  {% else %}
                    <div class="list-group-item text-center">
                      <p>{{ text_no_activity }}</p>
                    </div>
                  {% endif %}
                </div>
              </div>
              <div class="panel-footer">
                <a href="{{ activity_url }}" class="btn btn-sm btn-default btn-block">{{ text_view_all_activity }}</a>
              </div>
            </div>
          </div>
        </div>
        
        <div class="row">
          <!-- Recent Orders -->
          <div class="col-lg-6 col-md-12">
            <div class="panel panel-default">
              <div class="panel-heading">
                <h5><i class="fa fa-shopping-bag"></i> {{ text_recent_orders }}</h5>
              </div>
              <div class="panel-body" style="padding: 0;">
                <div class="table-responsive">
                  <table class="table table-striped">
                    <thead>
                      <tr>
                        <th>{{ column_order_id }}</th>
                        <th>{{ column_customer }}</th>
                        <th>{{ column_status }}</th>
                        <th>{{ column_total }}</th>
                        <th>{{ column_date_added }}</th>
                        <th>{{ column_action }}</th>
                      </tr>
                    </thead>
                    <tbody>
                      {% if orders %}
                        {% for order in orders %}
                          <tr>
                            <td>{{ order.order_id }}</td>
                            <td>{{ order.customer }}</td>
                            <td>{{ order.status }}</td>
                            <td>{{ order.total }}</td>
                            <td>{{ order.date_added }}</td>
                            <td><a href="{{ order.view }}" data-toggle="tooltip" title="{{ button_view }}"><i class="fa fa-eye"></i></a></td>
                          </tr>
                        {% endfor %}
                      {% else %}
                        <tr>
                          <td colspan="6" class="text-center">{{ text_no_results }}</td>
                        </tr>
                      {% endif %}
                    </tbody>
                  </table>
                </div>
              </div>
              <div class="panel-footer">
                <a href="{{ order_list_url }}" class="btn btn-sm btn-default btn-block">{{ text_view_all_orders }}</a>
              </div>
            </div>
          </div>
          
          <!-- Quick Actions & Notifications -->
          <div class="col-lg-6 col-md-12">
            <div class="row">
              <div class="col-md-12">
                <div class="panel panel-default">
                  <div class="panel-heading">
                    <h5><i class="fa fa-bolt"></i> {{ text_quick_actions }}</h5>
                  </div>
                  <div class="panel-body">
                    <div class="row">
                      <div class="col-xs-4 text-center">
                        <a href="{{ add_product_url }}" class="btn btn-default btn-block">
                          <i class="fa fa-plus-circle fa-2x"></i><br>
                          {{ text_add_product }}
                        </a>
                      </div>
                      <div class="col-xs-4 text-center">
                        <a href="{{ add_order_url }}" class="btn btn-default btn-block">
                          <i class="fa fa-shopping-cart fa-2x"></i><br>
                          {{ text_add_order }}
                        </a>
                      </div>
                      <div class="col-xs-4 text-center">
                        <a href="{{ add_customer_url }}" class="btn btn-default btn-block">
                          <i class="fa fa-user-plus fa-2x"></i><br>
                          {{ text_add_customer }}
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <div class="col-md-12">
                <div class="panel panel-default">
                  <div class="panel-heading">
                    <div class="row">
                      <div class="col-xs-8">
                        <h5><i class="fa fa-bell"></i> {{ text_notifications }}</h5>
                      </div>
                      <div class="col-xs-4 text-right">
                        <span class="badge badge-danger">{{ notification_count }}</span>
                      </div>
                    </div>
                  </div>
                  <div class="panel-body" style="padding: 0;">
                    <div class="list-group">
                      {% if notifications %}
                        {% for notification in notifications %}
                          <div class="list-group-item">
                            <div class="row">
                              <div class="col-xs-9">
                                <h6>{{ notification.title }}</h6>
                                <p>{{ notification.message }}</p>
                              </div>
                              <div class="col-xs-3 text-right">
                                <small>{{ notification.time_ago }}</small>
                              </div>
                            </div>
                          </div>
                        {% endfor %}
                      {% else %}
                        <div class="list-group-item text-center">
                          <p>{{ text_no_notifications }}</p>
                        </div>
                      {% endif %}
                    </div>
                  </div>
                  <div class="panel-footer">
                    <a href="{{ notification_url }}" class="btn btn-sm btn-default btn-block">{{ text_view_all_notifications }}</a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

<script type="text/javascript"><!--
// Initialize sales chart
$(document).ready(function() {
    if (typeof Chart !== 'undefined' && $('#sales-chart').length) {
        var ctx = document.getElementById('sales-chart').getContext('2d');
        
        // Sample data - this would be replaced with real data from the controller
        var salesData = {
            labels: {% if chart_labels %}[{% for label in chart_labels %}'{{ label }}'{% if not loop.last %}, {% endif %}{% endfor %}]{% else %}['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']{% endif %},
            datasets: [{
                label: '{{ text_sales }}',
                data: {% if chart_data %}{% for data in chart_data %}{{ data }}{% if not loop.last %}, {% endif %}{% endfor %}{% else %}[65, 59, 80, 81, 56, 55, 40]{% endif %},
                backgroundColor: 'rgba(54, 162, 235, 0.2)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 1,
                tension: 0.4
            }]
        };
        
        var salesChart = new Chart(ctx, {
            type: 'line',
            data: salesData,
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    title: {
                        display: false,
                        text: '{{ text_sales_analytics }}'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
        
        // Time period buttons
        $('#btn-week, #btn-month, #btn-year').on('click', function() {
            $('#btn-week, #btn-month, #btn-year').removeClass('active');
            $(this).addClass('active');
            
            var period = this.id.replace('btn-', '');
            
            // Ajax call to get new data based on period
            $.ajax({
                url: 'index.php?route={screen_name}/chart&user_token={{ user_token }}&period=' + period,
                type: 'GET',
                dataType: 'json',
                success: function(json) {
                    if (json.success) {
                        salesChart.data.labels = json.labels;
                        salesChart.data.datasets[0].data = json.data;
                        salesChart.update();
                    }
                },
                error: function(xhr, ajaxOptions, thrownError) {
                    console.error(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
                }
            });
        });
    }
});
//--></script>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}