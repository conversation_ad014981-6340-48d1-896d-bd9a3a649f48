<?php

/**
 * HashAglorithm
 *
 * PHP version 5
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2016 <PERSON>
 * @license   http://www.opensource.org/licenses/mit-license.html  MIT License
 * @link      http://phpseclib.sourceforge.net
 */

declare(strict_types=1);

namespace phpseclib3\File\ASN1\Maps;

/**
 * HashAglorithm
 *
 * <AUTHOR> <<EMAIL>>
 */
abstract class HashAlgorithm
{
    public const MAP = AlgorithmIdentifier::MAP;
}
