<?php
// Heading
$_['heading_title'] = 'Facebook Conversion API + Facebook Pixel + Dynamic Ads';

// Text
$_['text_extension'] = 'Extensions';
$_['text_module'] = 'Module';
$_['text_total'] = 'Total';
$_['text_success'] = 'Success: You have modified module!';
$_['text_edit'] = 'Edit';

$_['text_yes'] = 'Yes';
$_['text_no'] = 'No';
$_['text_enabled'] = 'Enabled';
$_['text_disabled'] = 'Disabled';
$_['text_select_all'] = 'Check All';
$_['text_unselect_all'] = 'Uncheck All'; 
$_['text_free'] = 'Free';
$_['text_per'] = 'Percentage';
$_['text_fix'] = 'Fix Amount';
$_['text_all'] = 'All';
$_['text_filter'] = 'Filters';
 
// Entry
$_['entry_status']     = 'Status';
$_['entry_themenm'] = 'Theme Compatibility';

$_['entry_pxid'] = 'Pixel ID';
$_['entry_pxid_help'] = '<font style="font-size: 10px;">Required</font>';

$_['entry_apitok'] = 'API Token';
$_['entry_apitok_help'] = '<font style="font-size: 10px;">Optional - leave blank if you do not want to track Conversions API server based event</font>';

$_['entry_evcd'] = 'Test Event Code';
$_['entry_evcd_help'] = '<font style="font-size: 10px;">Optional - help you to view live events tracking at facebook pixel</font>';

// Error
$_['error_permission'] = 'Warning: You do not have permission to modify module!';