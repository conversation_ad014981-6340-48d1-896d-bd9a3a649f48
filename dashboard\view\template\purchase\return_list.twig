{{ header }}
{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="button" id="refresh-list" class="btn btn-default" onclick="ReturnManager.loadReturns(1);">
          <i class="fa fa-refresh"></i> {{ text_refresh }}
        </button>
        {% if can_add %}
        <button type="button" id="add-return" class="btn btn-primary" onclick="ReturnManager.addReturn();">
          <i class="fa fa-plus"></i> {{ button_add }}
        </button>
        {% endif %}
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>

  <div class="container-fluid">
    <!-- Alerts -->
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible fade in">
      <i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert" aria-label="Close">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    {% endif %}

    {% if success %}
    <div class="alert alert-success alert-dismissible fade in">
      <i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert" aria-label="Close">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    {% endif %}

    <!-- Search Filters -->
    <div class="panel panel-default filter-panel">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-filter"></i> {{ text_filter }}</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-sm-3">
            <div class="form-group">
              <label for="filter-return-id" class="control-label">{{ column_return_id }}</label>
              <input type="text" name="filter_return_id" id="filter-return-id" class="form-control" placeholder="{{ column_return_id }}">
            </div>
          </div>
          <div class="col-sm-3">
            <div class="form-group">
              <label for="filter-po-number" class="control-label">{{ column_po_number }}</label>
              <select id="filter-po-number" class="form-control select2-po"></select>
            </div>
          </div>
          <div class="col-sm-3">
            <div class="form-group">
              <label for="filter-supplier" class="control-label">{{ column_supplier }}</label>
              <select id="filter-supplier" class="form-control select2-supplier">
                <option value="">{{ text_all_suppliers }}</option>
                {% for supplier in suppliers %}
                <option value="{{ supplier.supplier_id }}">{{ supplier.name }}</option>
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="col-sm-3">
            <div class="form-group">
              <label for="filter-status" class="control-label">{{ column_status }}</label>
              <select id="filter-status" class="form-control">
                <option value="">{{ text_all_statuses }}</option>
                {# TODO: Add status options from controller/model #}
              </select>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-sm-4">
            <div class="form-group">
              <label for="filter-date-start" class="control-label">{{ text_date_start }}</label>
              <div class="input-group date">
                <input type="text" name="filter_date_start" placeholder="{{ text_date_start }}" id="filter-date-start" class="form-control" data-date-format="YYYY-MM-DD">
                <span class="input-group-btn">
                  <button type="button" class="btn btn-default"><i class="fa fa-calendar"></i></button>
                </span>
              </div>
            </div>
          </div>
          <div class="col-sm-4">
            <div class="form-group">
              <label for="filter-date-end" class="control-label">{{ text_date_end }}</label>
              <div class="input-group date">
                <input type="text" name="filter_date_end" placeholder="{{ text_date_end }}" id="filter-date-end" class="form-control" data-date-format="YYYY-MM-DD">
                <span class="input-group-btn">
                  <button type="button" class="btn btn-default"><i class="fa fa-calendar"></i></button>
                </span>
              </div>
            </div>
          </div>
          <div class="col-sm-4">
            <div class="form-group" style="margin-top: 24px;">
              <button type="button" id="clear-filter" class="btn btn-default btn-block" onclick="ReturnManager.clearFilters();">
                <i class="fa fa-eraser"></i> {{ button_clear }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Purchase Returns List -->
    <div class="panel panel-default list-panel">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-list"></i> {{ text_list }}</h3>
      </div>
      <div class="panel-body">
        <form id="form-return" method="post">
          <div class="table-responsive">
            <table id="return-table" class="table table-bordered table-hover">
              <thead>
                <tr>
                  <th style="width: 1px;" class="text-center">
                    <input type="checkbox" onclick="$('input[name*=\'selected\']').prop('checked', this.checked);" />
                  </th>
                  <th>{{ column_return_id }}</th>
                  <th>{{ column_po_number }}</th>
                  <th>{{ column_supplier }}</th>
                  <th>{{ column_status }}</th>
                  <th>{{ column_date_added }}</th>
                  <th>{{ column_date_modified }}</th>
                  <th class="text-right">{{ column_action }}</th>
                </tr>
              </thead>
              <tbody id="return-list">
                {# Data will be loaded via AJAX #}
                <tr>
                  <td class="text-center" colspan="8">{{ text_loading }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </form>

        <!-- Bulk Actions -->
        <div class="row">
          <div class="col-sm-6">
            <div class="form-inline bulk-actions">
              <div class="form-group">
                <select id="bulk-action" class="form-control">
                  <option value="">{{ text_bulk_action }}</option>
                  {# TODO: Add bulk actions based on permissions #}
                  <option value="delete">{{ text_delete_selected }}</option>
                </select>
              </div>
              <button type="button" id="bulk-action-apply" class="btn btn-primary" onclick="ReturnManager.executeBulkAction();">
                <i class="fa fa-check"></i> {{ button_apply }}
              </button>
            </div>
          </div>
          <div class="col-sm-6 text-right">
            {# Export button if needed #}
          </div>
        </div>

        <!-- Pagination -->
        <div class="row">
          <div class="col-sm-6 text-left" id="pagination"></div>
          <div class="col-sm-6 text-right pagination-info" id="results"></div>
        </div>

      </div>
    </div>
  </div>
</div>

<!-- Loading Overlay -->
<div id="loading-overlay">
  <div class="loading-spinner">
    <i class="fa fa-spinner fa-spin fa-3x fa-fw"></i>
    <span class="sr-only">{{ text_loading }}</span>
  </div>
</div>

<!-- Modal for Return Form -->
<div class="modal fade" id="modal-return-form" tabindex="-1" role="dialog" aria-labelledby="modal-return-form-label">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <!-- Will be loaded via AJAX -->
    </div>
  </div>
</div>

<!-- Modal for Return View -->
<div class="modal fade" id="modal-return-view" tabindex="-1" role="dialog" aria-labelledby="modal-return-view-label">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <!-- Will be loaded via AJAX -->
    </div>
  </div>
</div>

{{ footer }}

<script type="text/javascript">
// Global variables
var user_token = '{{ user_token }}';
var currentPage = 1;
var language = {
    text_no_results: '{{ text_no_results }}',
    text_confirm_approve: '{{ text_confirm_approve }}',
    text_confirm_reject: '{{ text_confirm_reject }}',
    text_confirm_delete: '{{ text_confirm_delete }}',
    text_confirm_create_credit: '{{ text_confirm_create_credit }}',
    text_confirm_bulk_approve: '{{ text_confirm_bulk_approve }}',
    text_confirm_bulk_reject: '{{ text_confirm_bulk_reject }}',
    text_confirm_bulk_delete: '{{ text_confirm_bulk_delete }}',
    text_enter_rejection_reason: '{{ text_enter_rejection_reason }}',
    text_select_status: '{{ text_select_status }}',
    text_select_supplier: '{{ text_select_supplier }}',
    text_refresh_list: '{{ text_refresh_list }}',
    error_no_selection: '{{ error_no_selection }}',
    error_select_action: '{{ error_select_action }}',
    error_loading_data: '{{ error_loading_data }}'
};

// Permissions
window.can_add = {{ can_add ? 'true' : 'false' }};
window.can_delete = {{ can_delete ? 'true' : 'false' }};
window.can_approve = {{ can_approve ? 'true' : 'false' }};
</script>

<script src="view/javascript/purchase/return_list.js"></script>

<script type="text/javascript">
/**
 * Purchase Return Manager (Legacy compatibility)
 */
var ReturnManager = {
  user_token: '{{ user_token }}',

  init: function() {
    // Use new functions from external JS file
    loadReturns(1);
    $('[data-toggle="tooltip"]').tooltip({container: 'body'});
  },

  initializeFilters: function() {
    $('#filter-po-number').select2({
      placeholder: '{{ text_select_po }}',
      allowClear: true,
      dropdownParent: $('#filter-po-number').parent(),
      ajax: {
        url: 'index.php?route=purchase/order/ajaxSearchPO&user_token=' + this.user_token, // Reuse PO search
        dataType: 'json',
        delay: 300,
        data: function(params) { return { q: params.term || '' }; },
        processResults: function(data) { return { results: data }; },
        cache: true
      }
    });

    $('#filter-supplier').select2({
      placeholder: '{{ text_select_supplier }}',
      allowClear: true,
      dropdownParent: $('#filter-supplier').parent()
    });

    $('.date').datetimepicker({
      pickTime: false,
      format: 'YYYY-MM-DD',
      useCurrent: false
    });
  },

  setupEventHandlers: function() {
    $('#filter-return-id, #filter-po-number, #filter-supplier, #filter-status').on('change', function() {
      ReturnManager.loadReturns(1);
    });
     $('#filter-return-id').on('keyup', function(e) {
        if (e.keyCode == 13) { // Enter key
            ReturnManager.loadReturns(1);
        }
    });
    $('.date').on('dp.change', function() {
      ReturnManager.loadReturns(1);
    });
  },

  showLoading: function() {
    $('#loading-overlay').fadeIn(200);
  },

  hideLoading: function() {
    $('#loading-overlay').fadeOut(200);
  },

  loadReturns: function(page) {
    // Use new function from external JS file
    loadReturns(page);
  },

  renderReturns: function(json) {
    var html = '';
    if (json.returns && json.returns.length > 0) {
      for (var i = 0; i < json.returns.length; i++) {
        var ret = json.returns[i];
        html += '<tr>';
        html += '  <td class="text-center"><input type="checkbox" name="selected[]" value="' + ret.return_id + '" /></td>';
        html += '  <td>' + ret.return_id + '</td>';
        html += '  <td>' + (ret.po_number || '') + '</td>';
        html += '  <td>' + ret.supplier_name + '</td>';
        html += '  <td class="text-center"><span class="label label-' + ret.status_class + '">' + ret.status_text + '</span></td>';
        html += '  <td>' + ret.date_added + '</td>';
        html += '  <td>' + ret.date_modified + '</td>';
        html += '  <td class="text-right">';
        html += '    <div class="btn-group" role="group">';
        // Add action buttons based on permissions and status
        if (ret.can_view) {
             html += '<button type="button" class="btn btn-info btn-sm" onclick="ReturnManager.viewReturn(' + ret.return_id + ');" data-toggle="tooltip" title="{{ button_view }}"><i class="fa fa-eye"></i></button>';
        }
        if (ret.can_edit) {
             html += '<button type="button" class="btn btn-primary btn-sm" onclick="ReturnManager.editReturn(' + ret.return_id + ');" data-toggle="tooltip" title="{{ button_edit }}"><i class="fa fa-pencil"></i></button>';
        }
        if (ret.can_delete) {
             html += '<button type="button" class="btn btn-danger btn-sm" onclick="ReturnManager.deleteReturn(' + ret.return_id + ');" data-toggle="tooltip" title="{{ button_delete }}"><i class="fa fa-trash"></i></button>';
        }
        html += '    </div>';
        html += '  </td>';
        html += '</tr>';
      }
    } else {
      html += '<tr><td class="text-center" colspan="8">{{ text_no_results }}</td></tr>';
    }
    $('#return-list').html(html);
    $('[data-toggle="tooltip"]').tooltip({container: 'body'});
    $('#pagination').html(json.pagination || '');
    $('#results').html(json.results || '');
  },

  clearFilters: function() {
    $('#filter-return-id').val('');
    $('#filter-po-number').val(null).trigger('change');
    $('#filter-supplier').val(null).trigger('change');
    $('#filter-status').val('');
    $('#filter-date-start').val('');
    $('#filter-date-end').val('');
    this.loadReturns(1);
  },

  addReturn: function() {
    // Use new function from external JS file
    openAddModal();
  },

  editReturn: function(return_id) {
    // Use new function from external JS file
    editReturn(return_id);
  },

  viewReturn: function(return_id) {
    // Use new function from external JS file
    viewReturn(return_id);
  },

  deleteReturn: function(return_id) {
    // Use new function from external JS file
    deleteReturn(return_id);
  },

  executeBulkAction: function() {
    // Use new function from external JS file
    var action = $('#bulk-action').val();
    var selected = getSelectedReturns();

    if (selected.length === 0) {
        alert(getLanguageString('error_no_selection'));
        return;
    }

    if (!action) {
        alert(getLanguageString('error_select_action'));
        return;
    }

    switch (action) {
        case 'approve':
            bulkApprove(selected);
            break;
        case 'reject':
            bulkReject(selected);
            break;
        case 'delete':
            bulkDelete(selected);
            break;
    }
  }

};

$(document).ready(function() {
  ReturnManager.init();
});
</script>
