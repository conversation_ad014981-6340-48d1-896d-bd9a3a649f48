{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="submit" form="form-fulfillment" data-toggle="tooltip" title="{{ button_fulfill }}" class="btn btn-primary">
          <i class="fa fa-check"></i> {{ button_fulfill }}
        </button>
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default">
          <i class="fa fa-reply"></i> {{ button_cancel }}
        </a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible">
      <i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title">
          <i class="fa fa-boxes"></i> {{ text_fulfill_order }} #{{ order.order_id }}
        </h3>
      </div>
      <div class="panel-body">
        
        <!-- معلومات الطلب الأساسية -->
        <div class="row">
          <div class="col-md-6">
            <div class="panel panel-info">
              <div class="panel-heading">
                <h4 class="panel-title">{{ text_order_details }}</h4>
              </div>
              <div class="panel-body">
                <table class="table table-bordered">
                  <tr>
                    <td><strong>{{ column_order_id }}:</strong></td>
                    <td>#{{ order.order_id }}</td>
                  </tr>
                  <tr>
                    <td><strong>{{ text_customer_information }}:</strong></td>
                    <td>{{ order.customer_name }}</td>
                  </tr>
                  <tr>
                    <td><strong>{{ column_email }}:</strong></td>
                    <td>{{ order.email }}</td>
                  </tr>
                  <tr>
                    <td><strong>{{ column_telephone }}:</strong></td>
                    <td>{{ order.telephone }}</td>
                  </tr>
                  <tr>
                    <td><strong>{{ column_total }}:</strong></td>
                    <td>{{ order.total }}</td>
                  </tr>
                  <tr>
                    <td><strong>{{ column_status }}:</strong></td>
                    <td>
                      <span class="label label-info">{{ order.order_status_name }}</span>
                    </td>
                  </tr>
                </table>
              </div>
            </div>
          </div>
          
          <div class="col-md-6">
            <div class="panel panel-success">
              <div class="panel-heading">
                <h4 class="panel-title">{{ text_shipping_address }}</h4>
              </div>
              <div class="panel-body">
                <address>
                  <strong>{{ order.shipping_firstname }} {{ order.shipping_lastname }}</strong><br>
                  {% if order.shipping_company %}{{ order.shipping_company }}<br>{% endif %}
                  {{ order.shipping_address_1 }}<br>
                  {% if order.shipping_address_2 %}{{ order.shipping_address_2 }}<br>{% endif %}
                  {{ order.shipping_city }}, {{ order.shipping_zone }}<br>
                  {{ order.shipping_country }} {{ order.shipping_postcode }}
                </address>
              </div>
            </div>
          </div>
        </div>
        
        <!-- منتجات الطلب -->
        <div class="panel panel-primary">
          <div class="panel-heading">
            <h4 class="panel-title">{{ text_order_products }}</h4>
          </div>
          <div class="panel-body">
            <div class="table-responsive">
              <table class="table table-bordered table-hover">
                <thead>
                  <tr>
                    <th>{{ column_product_name }}</th>
                    <th>{{ column_model }}</th>
                    <th>{{ column_unit }}</th>
                    <th>{{ text_options }}</th>
                    <th>{{ column_quantity }}</th>
                    <th>{{ column_stock_quantity }}</th>
                    <th>{{ text_barcode }}</th>
                    <th>{{ column_location }}</th>
                    <th>{{ column_can_fulfill }}</th>
                  </tr>
                </thead>
                <tbody>
                  {% for product in order.products %}
                  <tr class="{% if not product.can_fulfill %}danger{% endif %}">
                    <td>
                      {% if product.image %}
                      <img src="{{ product.image }}" alt="{{ product.name }}" class="img-thumbnail" style="width: 50px;">
                      {% endif %}
                      {{ product.name }}
                    </td>
                    <td>{{ product.model }}</td>
                    <td>
                      <span class="label label-default">{{ product.unit_name }}</span>
                      {% if product.conversion_factor != 1 %}
                      <small>({{ product.conversion_factor }})</small>
                      {% endif %}
                    </td>
                    <td>
                      {% if product.options %}
                        {% for option in product.options %}
                        <div class="option-item">
                          <strong>{{ option.option_name }}:</strong> {{ option.option_value_name }}
                          {% if option.option_subtract_stock %}
                          <span class="label label-warning">مخزون منفصل</span>
                          {% endif %}
                        </div>
                        {% endfor %}
                      {% else %}
                        <span class="text-muted">لا توجد خيارات</span>
                      {% endif %}
                    </td>
                    <td>
                      <span class="badge badge-primary">{{ product.quantity }}</span>
                    </td>
                    <td>
                      <span class="badge {% if product.available_quantity >= product.quantity %}badge-success{% else %}badge-danger{% endif %}">
                        {{ product.available_quantity }}
                      </span>
                    </td>
                    <td>
                      {% if product.specific_barcode %}
                      <div class="barcode-info">
                        <code>{{ product.specific_barcode }}</code>
                        <small class="text-muted">({{ product.specific_barcode_type }})</small>
                      </div>
                      {% else %}
                        <span class="text-muted">لا يوجد باركود</span>
                      {% endif %}
                    </td>
                    <td>{{ product.stock_location|default('غير محدد') }}</td>
                    <td>
                      {% if product.can_fulfill %}
                        <span class="label label-success">{{ text_can_fulfill }}</span>
                      {% else %}
                        <span class="label label-danger">{{ text_cannot_fulfill }}</span>
                      {% endif %}
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
            
            {% if not order.can_fulfill %}
            <div class="alert alert-warning">
              <i class="fa fa-exclamation-triangle"></i> {{ text_alert_stock_shortage }}
            </div>
            {% endif %}
          </div>
        </div>
        
        <!-- نموذج التجهيز -->
        <form action="{{ action }}" method="post" enctype="multipart/form-data" id="form-fulfillment" class="form-horizontal">
          <div class="panel panel-warning">
            <div class="panel-heading">
              <h4 class="panel-title">{{ text_fulfillment_information }}</h4>
            </div>
            <div class="panel-body">
              
              <!-- تفاصيل الطرد -->
              <div class="row">
                <div class="col-md-6">
                  <div class="form-group required">
                    <label class="col-sm-4 control-label" for="input-package-weight">{{ entry_package_weight }}</label>
                    <div class="col-sm-8">
                      <div class="input-group">
                        <input type="number" step="0.01" name="package_weight" value="{{ package_weight }}" placeholder="{{ entry_package_weight }}" id="input-package-weight" class="form-control" required />
                        <span class="input-group-addon">{{ text_kg }}</span>
                      </div>
                      {% if error_package_weight %}
                      <div class="text-danger">{{ error_package_weight }}</div>
                      {% endif %}
                      <div class="help-block">{{ help_package_weight }}</div>
                    </div>
                  </div>
                  
                  <div class="form-group required">
                    <label class="col-sm-4 control-label" for="input-package-dimensions">{{ entry_package_dimensions }}</label>
                    <div class="col-sm-8">
                      <div class="input-group">
                        <input type="text" name="package_dimensions" value="{{ package_dimensions }}" placeholder="30×20×15" id="input-package-dimensions" class="form-control" required />
                        <span class="input-group-addon">{{ text_cm }}</span>
                      </div>
                      {% if error_package_dimensions %}
                      <div class="text-danger">{{ error_package_dimensions }}</div>
                      {% endif %}
                      <div class="help-block">{{ help_package_dimensions }}</div>
                    </div>
                  </div>
                </div>
                
                <div class="col-md-6">
                  <div class="form-group">
                    <label class="col-sm-4 control-label" for="input-packing-notes">{{ entry_packing_notes }}</label>
                    <div class="col-sm-8">
                      <textarea name="packing_notes" rows="4" placeholder="{{ entry_packing_notes }}" id="input-packing-notes" class="form-control">{{ packing_notes }}</textarea>
                      <div class="help-block">{{ help_packing_notes }}</div>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- خيارات الشحن -->
              <div class="panel panel-info">
                <div class="panel-heading">
                  <h5 class="panel-title">{{ text_shipping_options }}</h5>
                </div>
                <div class="panel-body">
                  <div class="row">
                    <div class="col-md-6">
                      <div class="form-group">
                        <label class="col-sm-4 control-label" for="input-shipping-company">{{ entry_shipping_company }}</label>
                        <div class="col-sm-8">
                          <select name="shipping_company_id" id="input-shipping-company" class="form-control">
                            <option value="">{{ text_select }}</option>
                            {% for company in shipping_companies %}
                            <option value="{{ company.company_id }}" {% if shipping_company_id == company.company_id %}selected{% endif %}>
                              {{ company.name }}
                            </option>
                            {% endfor %}
                          </select>
                        </div>
                      </div>
                      
                      <div class="form-group">
                        <label class="col-sm-4 control-label" for="input-shipping-cost">{{ entry_shipping_cost }}</label>
                        <div class="col-sm-8">
                          <div class="input-group">
                            <input type="number" step="0.01" name="shipping_cost" value="{{ shipping_cost }}" placeholder="{{ entry_shipping_cost }}" id="input-shipping-cost" class="form-control" />
                            <span class="input-group-addon">{{ text_currency }}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <div class="col-md-6">
                      {% if order.payment_method == 'cod' %}
                      <div class="form-group">
                        <label class="col-sm-4 control-label" for="input-cod-amount">{{ entry_cod_amount }}</label>
                        <div class="col-sm-8">
                          <div class="input-group">
                            <input type="number" step="0.01" name="cod_amount" value="{{ order.total }}" placeholder="{{ entry_cod_amount }}" id="input-cod-amount" class="form-control" readonly />
                            <span class="input-group-addon">{{ text_currency }}</span>
                          </div>
                        </div>
                      </div>
                      {% endif %}
                      
                      <div class="form-group">
                        <label class="col-sm-4 control-label" for="input-special-instructions">{{ entry_special_instructions }}</label>
                        <div class="col-sm-8">
                          <textarea name="special_instructions" rows="3" placeholder="{{ entry_special_instructions }}" id="input-special-instructions" class="form-control">{{ special_instructions }}</textarea>
                          <div class="help-block">{{ help_special_instructions }}</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
            </div>
          </div>
        </form>
        
      </div>
    </div>
  </div>
</div>

<style>
.option-item {
  margin-bottom: 5px;
  padding: 3px 6px;
  background-color: #f8f9fa;
  border-radius: 3px;
  font-size: 12px;
}

.barcode-info {
  font-family: 'Courier New', monospace;
}

.barcode-info code {
  background-color: #f8f9fa;
  padding: 2px 4px;
  border-radius: 3px;
}

.table-responsive {
  max-height: 500px;
  overflow-y: auto;
}

.img-thumbnail {
  max-width: 50px;
  max-height: 50px;
}

.badge {
  font-size: 11px;
}

.panel-title {
  font-size: 16px;
  font-weight: bold;
}

.help-block {
  font-size: 11px;
  color: #737373;
}
</style>

<script type="text/javascript">
$(document).ready(function() {
    // تحديث تكلفة الشحن عند تغيير شركة الشحن
    $('#input-shipping-company').on('change', function() {
        var company_id = $(this).val();
        if (company_id) {
            // يمكن إضافة AJAX لحساب التكلفة التلقائي
            calculateShippingCost(company_id);
        }
    });
    
    // التحقق من صحة النموذج قبل الإرسال
    $('#form-fulfillment').on('submit', function(e) {
        var weight = $('#input-package-weight').val();
        var dimensions = $('#input-package-dimensions').val();
        
        if (!weight || weight <= 0) {
            alert('{{ error_package_weight }}');
            e.preventDefault();
            return false;
        }
        
        if (!dimensions) {
            alert('{{ error_package_dimensions }}');
            e.preventDefault();
            return false;
        }
        
        return true;
    });
});

function calculateShippingCost(company_id) {
    // يمكن تطوير هذه الدالة لحساب التكلفة عبر AJAX
    console.log('Calculating shipping cost for company: ' + company_id);
}
</script>

{{ footer }}
