<?php
// Heading
$_['heading_title']       = 'Internal Audit';

// Text
$_['text_list']           = 'Internal Audit List';
$_['text_modal_add']      = 'Add Internal Audit';
$_['text_modal_edit']     = 'Edit Internal Audit';
$_['text_confirm_delete'] = 'Are you sure you want to delete?';
$_['text_home']           = 'Home';

$_['text_all_statuses']   = '- All Statuses -';
$_['text_scheduled']      = 'Scheduled';
$_['text_in_progress']    = 'In Progress';
$_['text_completed']      = 'Completed';
$_['text_cancelled']      = 'Cancelled';

// Entry
$_['entry_status']             = 'Status';
$_['entry_audit_subject']      = 'Audit Subject';
$_['entry_description']        = 'Description';
$_['entry_auditor_user_id']    = 'Auditor (User ID)';
$_['entry_scheduled_date']     = 'Scheduled Date';
$_['entry_completion_date']    = 'Completion Date';
$_['entry_findings']           = 'Findings';
$_['entry_recommendations']    = 'Recommendations';

// Extra Filter
$_['text_date_start']          = 'Date Start';
$_['text_date_end']            = 'Date End';

// Columns
$_['column_audit_id']         = 'ID';
$_['column_audit_subject']    = 'Audit Subject';
$_['column_scheduled_date']   = 'Scheduled Date';
$_['column_status']           = 'Status';
$_['column_auditor']          = 'Auditor';
$_['column_action']           = 'Action';

// Buttons
$_['button_filter']           = 'Filter';
$_['button_add']              = 'Add';
$_['button_edit']             = 'Edit';
$_['button_delete']           = 'Delete';
$_['button_save']             = 'Save';
$_['button_close']            = 'Close';
