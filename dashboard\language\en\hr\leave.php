<?php
// Heading
$_['heading_title']                = 'Leave Management';

// Text
$_['text_filter']                  = 'Filter';
$_['text_leave_list']              = 'Leave List';
$_['text_add_leave_request']       = 'Add Leave Request';
$_['text_edit_leave_request']      = 'Edit Leave Request';
$_['text_ajax_error']              = 'An error occurred while connecting to the server';
$_['text_confirm_delete']          = 'Are you sure you want to delete?';
$_['text_employee']                = 'Employee';
$_['text_select_employee']         = 'Select Employee';
$_['text_leave_type']              = 'Leave Type';
$_['text_select_leave_type']       = 'Select Leave Type';
$_['text_date_start']              = 'Start Date';
$_['text_date_end']                = 'End Date';
$_['text_reason']                  = 'Reason';
$_['text_status']                  = 'Status';
$_['text_approved_by']             = 'Approved By';
$_['text_select_approver']         = 'Select Approver';
$_['text_all_leave_types']         = 'All Leave Types';
$_['text_all_statuses']            = 'All Statuses';
$_['text_status_pending']          = 'Pending';
$_['text_status_approved']         = 'Approved';
$_['text_status_rejected']         = 'Rejected';
$_['text_status_cancelled']        = 'Cancelled';

// Buttons
$_['button_filter']                = 'Search';
$_['button_reset']                 = 'Reset';
$_['button_add_leave_request']     = 'Add Leave Request';
$_['button_close']                 = 'Close';
$_['button_save']                  = 'Save';

// Columns
$_['column_employee']              = 'Employee';
$_['column_leave_type']            = 'Leave Type';
$_['column_start_date']            = 'Start Date';
$_['column_end_date']              = 'End Date';
$_['column_status']                = 'Status';
$_['column_actions']               = 'Actions';

// Errors/Success
$_['error_not_found']              = 'Record not found!';
$_['error_invalid_request']        = 'Invalid request!';
$_['error_permission']             = 'Warning: You do not have permission to modify leave requests!';
$_['error_required']               = 'Warning: Please fill in the required fields!';
$_['text_success_add']             = 'Leave request added successfully!';
$_['text_success_edit']            = 'Leave request updated successfully!';
$_['text_success_delete']          = 'Leave request deleted successfully!';

// Navigation
$_['text_home']                    = 'Home';