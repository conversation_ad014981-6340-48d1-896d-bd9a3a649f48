<table class="table table-bordered">
  {% if country_match %}
  <tr>
    <td>{{ text_country_match }}</td>
    <td>{{ country_match }}</td>
  </tr>
  {% endif %}
  {% if country_code %}
  <tr>
    <td>{{ text_country_code }}</td>
    <td>{{ country_code }}</td>
  </tr>
  {% endif %}
  {% if high_risk_country %}
  <tr>
    <td>{{ text_high_risk_country }}</td>
    <td>{{ high_risk_country }}</td>
  </tr>
  {% endif %}
  {% if distance %}
  <tr>
    <td>{{ text_distance }}</td>
    <td>{{ distance }}</td>
  </tr>
 {% endif %}
  {% if ip_region %}
  <tr>
    <td>{{ text_ip_region }}</td>
    <td>{{ ip_region }}</td>
  </tr>
  {% endif %}
  {% if ip_city %}
  <tr>
    <td>{{ text_ip_city }}</td>
    <td>{{ ip_city }}</td>
  </tr>
  {% endif %}
  {% if ip_latitude %}
  <tr>
    <td>{{ text_ip_latitude }}</td>
    <td>{{ ip_latitude }}</td>
  </tr>
  {% endif %}
  {% if ip_longitude %}
  <tr>
    <td>{{ text_ip_longitude }}</td>
    <td>{{ ip_longitude }}</td>
  </tr>
  {% endif %}
  {% if ip_isp %}
  <tr>
    <td>{{ text_ip_isp }}</td>
    <td>{{ ip_isp }}</td>
  </tr>
  {% endif %}
  {% if ip_org %}
  <tr>
    <td>{{ text_ip_org }}</td>
    <td>{{ ip_org }}</td>
  </tr>
  {% endif %}
  {% if ip_asnum %}
  <tr>
    <td>{{ text_ip_asnum }}</td>
    <td>{{ ip_asnum }}</td>
  </tr>
  {% endif %}
  {% if ip_user_type %}
  <tr>
    <td>{{ text_ip_user_type }}</td>
    <td>{{ ip_user_type }}</td>
  </tr>
  {% endif %}
  {% if ip_country_confidence %}
  <tr>
    <td>{{ text_ip_country_confidence }}</td>
    <td>{{ ip_country_confidence }}</td>
  </tr>
  {% endif %}
  {% if ip_region_confidence %}
  <tr>
    <td>{{ text_ip_region_confidence }}</td>
    <td>{{ ip_region_confidence }}</td>
  </tr>
  {% endif %}
  {% if ip_city_confidence %}
  <tr>
    <td>{{ text_ip_city_confidence }}</td>
    <td>{{ ip_city_confidence }}</td>
  </tr>
  {% endif %}
  {% if ip_postal_confidence %}
  <tr>
    <td>{{ text_ip_postal_confidence }}</td>
    <td>{{ ip_postal_confidence }}</td>
  </tr>
  {% endif %}
  {% if ip_postal_code %}
  <tr>
    <td>{{ text_ip_postal_code }}</td>
    <td>{{ ip_postal_code }}</td>
  </tr>
  {% endif %}
  {% if ip_accuracy_radius %}
  <tr>
    <td>{{ text_ip_accuracy_radius }}</td>
    <td>{{ ip_accuracy_radius }}</td>
  </tr>
  {% endif %}
  {% if ip_net_speed_cell %}
  <tr>
    <td>{{ text_ip_net_speed_cell }}</td>
    <td>{{ ip_net_speed_cell }}</td>
  </tr>
  {% endif %}
  {% if ip_metro_code %}
  <tr>
    <td>{{ text_ip_metro_code }}</td>
    <td>{{ ip_metro_code }}</td>
  </tr>
  {% endif %}
  {% if ip_area_code %}
  <tr>
    <td>{{ text_ip_area_code }}</td>
    <td>{{ ip_area_code }}</td>
  </tr>
  {% endif %}
  {% if ip_time_zone %}
  <tr>
    <td>{{ text_ip_time_zone }}</td>
    <td>{{ ip_time_zone }}</td>
  </tr>
  {% endif %}
  {% if ip_region_name %}
  <tr>
    <td>{{ text_ip_region_name }}</td>
    <td>{{ ip_region_name }}</td>
  </tr>
  {% endif %}
  {% if ip_domain %}
  <tr>
    <td>{{ text_ip_domain }}</td>
    <td>{{ ip_domain }}</td>
  </tr>
  {% endif %}
  {% if ip_country_name %}
  <tr>
    <td>{{ text_ip_country_name }}</td>
    <td>{{ ip_country_name }}</td>
  </tr>
  {% endif %}
  {% if ip_continent_code %}
  <tr>
    <td>{{ text_ip_continent_code }}</td>
    <td>{{ ip_continent_code }}</td>
  </tr>
  {% endif %}
  {% if ip_corporate_proxy %}
  <tr>
    <td>{{ text_ip_corporate_proxy }}</td>
    <td>{{ ip_corporate_proxy }}</td>
  </tr>
  {% endif %}
  {% if anonymous_proxy %}
  <tr>
    <td>{{ text_anonymous_proxy }}</td>
    <td>{{ anonymous_proxy }}</td>
  </tr>
  {% endif %}
  {% if proxy_score %}
  <tr>
    <td>{{ text_proxy_score }}</td>
    <td>{{ proxy_score }}</td>
  </tr>
  {% endif %}
  {% if is_trans_proxy %}
  <tr>
    <td>{{ text_is_trans_proxy }}</td>
    <td>{{ is_trans_proxy }}</td>
  </tr>
  {% endif %}
  {% if free_mail %}
  <tr>
    <td>{{ text_free_mail }}</td>
    <td>{{ free_mail }}</td>
  </tr>
  {% endif %}
  {% if carder_email %}
  <tr>
    <td>{{ text_carder_email }}</td>
    <td>{{ carder_email }}</td>
  </tr>
  {% endif %}
  {% if high_risk_username %}
  <tr>
    <td>{{ text_high_risk_username }}</td>
    <td>{{ high_risk_username }}</td>
  </tr>
  {% endif %}
  {% if high_risk_password %}
  <tr>
    <td>{{ text_high_risk_password }}</td>
    <td>{{ high_risk_password }}</td>
  </tr>
  {% endif %}
  {% if bin_match %}
  <tr>
    <td>{{ text_bin_match }}</td>
    <td>{{ bin_match }}</td>
  </tr>
  {% endif %}
  {% if bin_country %}
  <tr>
    <td>{{ text_bin_country }}</td>
    <td>{{ bin_country }}</td>
  </tr>
  {% endif %}
  {% if bin_name_match %}
  <tr>
    <td>{{ text_bin_name_match }}</td>
    <td>{{ bin_name_match }}</td>
  </tr>
  {% endif %}
  {% if bin_name %}
  <tr>
    <td>{{ text_bin_name }}</td>
    <td>{{ bin_name }}</td>
  </tr>
  {% endif %}
  {% if bin_phone_match %}
  <tr>
    <td>{{ text_bin_phone_match }}</td>
    <td>{{ bin_phone_match }}</td>
  </tr>
  {% endif %}
  {% if bin_phone %}
  <tr>
    <td>{{ text_bin_phone }}</td>
    <td>{{ bin_phone }}</td>
  </tr>
  {% endif %}
  {% if customer_phone_in_billing_location %}
  <tr>
    <td>{{ text_customer_phone_in_billing_location }}</td>
    <td>{{ customer_phone_in_billing_location }}</td>
  </tr>
  {% endif %}
  {% if ship_forward %}
  <tr>
    <td>{{ text_ship_forward }}</td>
    <td>{{ ship_forward }}</td>
  </tr>
  {% endif %}
  {% if city_postal_match %}
  <tr>
    <td>{{ text_city_postal_match }}</td>
    <td>{{ city_postal_match }}</td>
  </tr>
  {% endif %}
  {% if ship_city_postal_match %}
  <tr>
    <td>{{ text_ship_city_postal_match }}</td>
    <td>{{ ship_city_postal_match }}</td>
  </tr>
  {% endif %}
  {% if score %}
  <tr>
    <td>{{ text_score }}</td>
    <td>{{ score }}</td>
  </tr>
  {% endif %}
  {% if explanation %}
  <tr>
    <td>{{ text_explanation }}</td>
    <td>{{ explanation }}</td>
  </tr>
  {% endif %}
  {% if risk_score %}
  <tr>
    <td>{{ text_risk_score }}</td>
    <td>{{ risk_score }}</td>
  </tr>
  {% endif %}
  {% if queries_remaining %}
  <tr>
    <td>{{ text_queries_remaining }}</td>
    <td>{{ queries_remaining }}</td>
  </tr>
  {% endif %}
  {% if maxmind_id %}
  <tr>
    <td>{{ text_maxmind_id }}</td>
    <td>{{ maxmind_id }}</td>
  </tr>
  {% endif %}
  {% if error %}
  <tr>
    <td>{{ text_error }}</td>
    <td>{{ error }}</td>
  </tr>
  {% endif %}
</table>
