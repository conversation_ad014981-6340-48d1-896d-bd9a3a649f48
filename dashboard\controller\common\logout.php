<?php
class ControllerCommonLogout extends Controller {
	public function index() {
		// تسجيل نشاط تسجيل الخروج قبل الخروج الفعلي
		if ($this->user->isLogged()) {
			$user_id = $this->user->getId();

			// تسجيل الخروج باستخدام الخدمات المركزية
			$this->load->model('core/central_service_manager');
			$this->model_core_central_service_manager->logLogout(
				$user_id,
				'تسجيل خروج من ' . ($this->request->server['REMOTE_ADDR'] ?? 'غير معروف')
			);
		}

		// تسجيل الخروج الفعلي
		$this->user->logout();

		// حذف التوكن من الجلسة
		unset($this->session->data['user_token']);

		// حذف كوكيز التذكر إن وجدت
		if (isset($_COOKIE['aym_remember_token'])) {
			setcookie('aym_remember_token', '', time() - 3600, '/');
		}
		if (isset($_COOKIE['aym_remember_user'])) {
			setcookie('aym_remember_user', '', time() - 3600, '/');
		}

		// إعادة التوجيه لصفحة تسجيل الدخول
		$this->response->redirect($this->url->link('common/login', '', true));
	}
}