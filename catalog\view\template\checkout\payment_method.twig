{% if error_warning %}
  <div class="alert alert-warning alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}</div>
{% endif %}
{% if payment_methods %}
  <p>{{ text_payment_method }}</p>
  {% for payment_method in payment_methods %}
    <div class="radio">
      <label>{% if payment_method.code == code or not code %}
          {% set code = payment_method.code %}
          <input type="radio" name="payment_method" value="{{ payment_method.code }}" checked="checked"/>
        {% else %}
          <input type="radio" name="payment_method" value="{{ payment_method.code }}"/>
        {% endif %}
        {{ payment_method.name }}
        {% if payment_method.terms %}
          ({{ payment_method.terms }})
        {% endif %}</label>
    </div>
  {% endfor %}
{% endif %}
<p><strong>{{ text_comment }}</strong></p>
<p>
  <textarea name="comment" rows="8" class="form-control"/>{{ comment }}</textarea>
</p>
{% if text_agree %}
  <div class="buttons">
    <div class="pull-right">{{ text_agree }}
      {% if agree %}
        <input type="checkbox" name="agree" value="1" checked="checked"/>
      {% else %}
        <input type="checkbox" name="agree" value="1"/>
      {% endif %}
      &nbsp;
      <input type="button" value="{{ button_continue }}" data-loading-text="{{ text_loading }}" class="btn btn-primary" id="button-payment-method"/>
    </div>
  </div>
{% else %}
  <div class="buttons">
    <div class="pull-right">
      <input type="button" value="{{ button_continue }}" data-loading-text="{{ text_loading }}" class="btn btn-primary" id="button-payment-method"/>
    </div>
  </div>
{% endif %}