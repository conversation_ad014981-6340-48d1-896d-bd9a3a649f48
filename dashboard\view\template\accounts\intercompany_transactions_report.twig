{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        {% if has_permission %}
        <button type="button" id="button-export-excel" data-toggle="tooltip" title="{{ button_export_excel }}" class="btn btn-success">
          <i class="fa fa-file-excel-o"></i> {{ button_export_excel }}
        </button>
        <button type="button" id="button-export-pdf" data-toggle="tooltip" title="{{ button_export_pdf }}" class="btn btn-danger">
          <i class="fa fa-file-pdf-o"></i> {{ button_export_pdf }}
        </button>
        <button type="button" id="button-print" data-toggle="tooltip" title="{{ button_print }}" class="btn btn-info">
          <i class="fa fa-print"></i> {{ button_print }}
        </button>
        {% endif %}
        <a href="{{ back }}" data-toggle="tooltip" title="{{ button_back }}" class="btn btn-default">
          <i class="fa fa-reply"></i> {{ button_back }}
        </a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    
    <!-- فلاتر التقرير -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-filter"></i> {{ text_report_filters }}</h3>
      </div>
      <div class="panel-body">
        <form action="{{ action }}" method="get" enctype="multipart/form-data" id="form-filter">
          <div class="row">
            <div class="col-md-3">
              <div class="form-group">
                <label class="control-label">{{ text_date_from }}</label>
                <div class="input-group date">
                  <input type="text" name="filter_date_from" value="{{ filter_date_from }}" placeholder="{{ text_date_from }}" data-date-format="YYYY-MM-DD" class="form-control" />
                  <span class="input-group-btn">
                    <button class="btn btn-default" type="button"><i class="fa fa-calendar"></i></button>
                  </span>
                </div>
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <label class="control-label">{{ text_date_to }}</label>
                <div class="input-group date">
                  <input type="text" name="filter_date_to" value="{{ filter_date_to }}" placeholder="{{ text_date_to }}" data-date-format="YYYY-MM-DD" class="form-control" />
                  <span class="input-group-btn">
                    <button class="btn btn-default" type="button"><i class="fa fa-calendar"></i></button>
                  </span>
                </div>
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <label class="control-label">{{ text_company }}</label>
                <select name="filter_company" class="form-control">
                  <option value="">{{ text_all_companies }}</option>
                  {% for company in companies %}
                  <option value="{{ company.company_id }}" {% if company.company_id == filter_company %}selected{% endif %}>{{ company.company_name }}</option>
                  {% endfor %}
                </select>
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <label class="control-label">{{ text_status }}</label>
                <select name="filter_status" class="form-control">
                  <option value="">{{ text_all_statuses }}</option>
                  <option value="pending" {% if filter_status == 'pending' %}selected{% endif %}>{{ text_status_pending }}</option>
                  <option value="matched" {% if filter_status == 'matched' %}selected{% endif %}>{{ text_status_matched }}</option>
                  <option value="eliminated" {% if filter_status == 'eliminated' %}selected{% endif %}>{{ text_status_eliminated }}</option>
                </select>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-3">
              <div class="form-group">
                <label class="control-label">{{ text_transaction_type }}</label>
                <select name="filter_transaction_type" class="form-control">
                  <option value="">{{ text_all_types }}</option>
                  <option value="sales" {% if filter_transaction_type == 'sales' %}selected{% endif %}>{{ text_type_sales }}</option>
                  <option value="purchases" {% if filter_transaction_type == 'purchases' %}selected{% endif %}>{{ text_type_purchases }}</option>
                  <option value="loans" {% if filter_transaction_type == 'loans' %}selected{% endif %}>{{ text_type_loans }}</option>
                  <option value="transfers" {% if filter_transaction_type == 'transfers' %}selected{% endif %}>{{ text_type_transfers }}</option>
                </select>
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <label class="control-label">{{ text_amount_from }}</label>
                <input type="text" name="filter_amount_from" value="{{ filter_amount_from }}" placeholder="{{ text_amount_from }}" class="form-control" />
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <label class="control-label">{{ text_amount_to }}</label>
                <input type="text" name="filter_amount_to" value="{{ filter_amount_to }}" placeholder="{{ text_amount_to }}" class="form-control" />
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-group">
                <label class="control-label">&nbsp;</label>
                <div>
                  <button type="submit" id="button-filter" class="btn btn-primary">
                    <i class="fa fa-search"></i> {{ button_filter }}
                  </button>
                  <button type="button" id="button-clear" class="btn btn-default">
                    <i class="fa fa-refresh"></i> {{ button_clear }}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>

    <!-- ملخص التقرير -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-bar-chart"></i> {{ text_report_summary }}</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-md-3">
            <div class="info-box bg-aqua">
              <span class="info-box-icon"><i class="fa fa-list"></i></span>
              <div class="info-box-content">
                <span class="info-box-text">{{ text_total_transactions }}</span>
                <span class="info-box-number">{{ summary.total_transactions }}</span>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="info-box bg-yellow">
              <span class="info-box-icon"><i class="fa fa-clock-o"></i></span>
              <div class="info-box-content">
                <span class="info-box-text">{{ text_pending_transactions }}</span>
                <span class="info-box-number">{{ summary.pending_transactions }}</span>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="info-box bg-blue">
              <span class="info-box-icon"><i class="fa fa-link"></i></span>
              <div class="info-box-content">
                <span class="info-box-text">{{ text_matched_transactions }}</span>
                <span class="info-box-number">{{ summary.matched_transactions }}</span>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="info-box bg-green">
              <span class="info-box-icon"><i class="fa fa-check"></i></span>
              <div class="info-box-content">
                <span class="info-box-text">{{ text_eliminated_transactions }}</span>
                <span class="info-box-number">{{ summary.eliminated_transactions }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-md-4">
            <div class="info-box bg-purple">
              <span class="info-box-icon"><i class="fa fa-money"></i></span>
              <div class="info-box-content">
                <span class="info-box-text">{{ text_total_amount }}</span>
                <span class="info-box-number">{{ summary.total_amount }}</span>
              </div>
            </div>
          </div>
          <div class="col-md-4">
            <div class="info-box bg-orange">
              <span class="info-box-icon"><i class="fa fa-minus-circle"></i></span>
              <div class="info-box-content">
                <span class="info-box-text">{{ text_eliminated_amount }}</span>
                <span class="info-box-number">{{ summary.eliminated_amount }}</span>
              </div>
            </div>
          </div>
          <div class="col-md-4">
            <div class="info-box bg-red">
              <span class="info-box-icon"><i class="fa fa-exclamation-triangle"></i></span>
              <div class="info-box-content">
                <span class="info-box-text">{{ text_unmatched_amount }}</span>
                <span class="info-box-number">{{ summary.unmatched_amount }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- تفاصيل المعاملات -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-table"></i> {{ text_transaction_details }}</h3>
      </div>
      <div class="panel-body">
        <div class="table-responsive">
          <table class="table table-bordered table-hover">
            <thead>
              <tr>
                <th>{{ column_transaction_reference }}</th>
                <th>{{ column_transaction_date }}</th>
                <th>{{ column_source_company }}</th>
                <th>{{ column_target_company }}</th>
                <th>{{ column_transaction_type }}</th>
                <th class="text-right">{{ column_amount }}</th>
                <th>{{ column_currency }}</th>
                <th>{{ column_status }}</th>
                <th>{{ column_matching_reference }}</th>
                <th>{{ column_elimination_date }}</th>
              </tr>
            </thead>
            <tbody>
              {% for transaction in transactions %}
              <tr>
                <td>
                  <a href="{{ transaction.view_link }}">{{ transaction.transaction_reference }}</a>
                </td>
                <td>{{ transaction.transaction_date }}</td>
                <td>{{ transaction.source_company_name }}</td>
                <td>{{ transaction.target_company_name }}</td>
                <td>{{ transaction.transaction_type }}</td>
                <td class="text-right">{{ transaction.amount }}</td>
                <td>{{ transaction.currency }}</td>
                <td>
                  {% if transaction.status == 'pending' %}
                    <span class="label label-warning">{{ text_status_pending }}</span>
                  {% elseif transaction.status == 'matched' %}
                    <span class="label label-info">{{ text_status_matched }}</span>
                  {% elseif transaction.status == 'eliminated' %}
                    <span class="label label-success">{{ text_status_eliminated }}</span>
                  {% endif %}
                </td>
                <td>{{ transaction.matching_reference }}</td>
                <td>{{ transaction.elimination_date }}</td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
        <div class="row">
          <div class="col-sm-6 text-left">{{ pagination }}</div>
          <div class="col-sm-6 text-right">{{ results }}</div>
        </div>
      </div>
    </div>

    <!-- تحليل حسب الشركة -->
    {% if company_analysis %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pie-chart"></i> {{ text_company_analysis }}</h3>
      </div>
      <div class="panel-body">
        <div class="table-responsive">
          <table class="table table-bordered">
            <thead>
              <tr>
                <th>{{ column_company }}</th>
                <th class="text-right">{{ column_total_transactions }}</th>
                <th class="text-right">{{ column_total_amount }}</th>
                <th class="text-right">{{ column_pending_amount }}</th>
                <th class="text-right">{{ column_eliminated_amount }}</th>
                <th class="text-right">{{ column_completion_rate }}</th>
              </tr>
            </thead>
            <tbody>
              {% for company in company_analysis %}
              <tr>
                <td>{{ company.company_name }}</td>
                <td class="text-right">{{ company.total_transactions }}</td>
                <td class="text-right">{{ company.total_amount }}</td>
                <td class="text-right">{{ company.pending_amount }}</td>
                <td class="text-right">{{ company.eliminated_amount }}</td>
                <td class="text-right">
                  <div class="progress" style="margin-bottom: 0;">
                    <div class="progress-bar progress-bar-success" style="width: {{ company.completion_rate }}%">
                      {{ company.completion_rate }}%
                    </div>
                  </div>
                </td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    </div>
    {% endif %}

  </div>
</div>

<script type="text/javascript">
$(document).ready(function() {
    // تهيئة منتقي التاريخ
    $('.date').datetimepicker({
        language: '{{ datepicker }}',
        pickTime: false
    });

    // تصدير إكسل
    $('#button-export-excel').on('click', function() {
        var url = '{{ export_excel }}';
        var filter = $('#form-filter').serialize();
        window.open(url + '&' + filter, '_blank');
    });

    // تصدير PDF
    $('#button-export-pdf').on('click', function() {
        var url = '{{ export_pdf }}';
        var filter = $('#form-filter').serialize();
        window.open(url + '&' + filter, '_blank');
    });

    // طباعة
    $('#button-print').on('click', function() {
        window.print();
    });

    // مسح الفلاتر
    $('#button-clear').on('click', function() {
        window.location = '{{ clear }}';
    });
});
</script>

{{ footer }}
