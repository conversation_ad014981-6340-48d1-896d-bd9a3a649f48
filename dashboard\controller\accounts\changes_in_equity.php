<?php
/**
 * تحكم قائمة التغيرات في حقوق الملكية الشاملة والمتكاملة
 * يجمع بين القوة والبساطة - يتفوق على SAP وOracle في سهولة الاستخدام
 * مع الاحتفاظ بالقوة والمرونة المطلوبة للشركات الكبيرة
 * متوافق مع قانون الشركات المصري ومعايير المحاسبة المصرية
 */
class ControllerAccountsChangesInEquity extends Controller {
    private $error = array();
    private $central_service;

    public function __construct($registry) {
        parent::__construct($registry);

        // تحميل الخدمات المركزية
        $this->load->model('core/central_service_manager');
        $this->central_service = $this->model_core_central_service_manager;
    }

    public function index() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/changes_in_equity') ||
            !$this->user->hasKey('accounting_equity_view')) {

            $this->central_service->logActivity('unauthorized_access', 'accounts',
                $this->language->get('log_unauthorized_access_changes_in_equity'), [
                'user_id' => $this->user->getId(),
                'ip_address' => $this->request->server['REMOTE_ADDR']
            ]);

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/changes_in_equity');
        $this->document->setTitle($this->language->get('heading_title'));

        // إضافة CSS و JavaScript المتقدم
        $this->document->addStyle('view/stylesheet/accounts/changes_in_equity.css');
        $this->document->addScript('view/javascript/accounts/changes_in_equity.js');
        $this->document->addScript('view/javascript/jquery/accounting.min.js');
        $this->document->addScript('view/javascript/jquery/select2.min.js');
        $this->document->addStyle('view/javascript/jquery/select2.min.css');
        $this->document->addScript('view/javascript/jquery/daterangepicker.min.js');
        $this->document->addStyle('view/javascript/jquery/daterangepicker.css');
        $this->document->addScript('view/javascript/jquery/chart.min.js');

        // تسجيل الوصول للشاشة
        $this->central_service->logActivity('view', 'accounts',
            $this->language->get('log_view_changes_in_equity_screen'), [
            'user_id' => $this->user->getId(),
            'screen' => 'accounts/changes_in_equity'
        ]);

        $data['action'] = $this->url->link('accounts/changes_in_equity/print', 'user_token=' . $this->session->data['user_token'], true);

        $data['heading_title'] = $this->language->get('heading_title');
        $data['text_form'] = $this->language->get('text_form');
        $data['entry_date_start'] = $this->language->get('entry_date_start');
        $data['entry_date_end'] = $this->language->get('entry_date_end');
        $data['button_filter'] = $this->language->get('button_filter');

        $data['user_token'] = $this->session->data['user_token'];
        $data['error_warning'] = isset($this->error['warning'])?$this->error['warning']:'';

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/changes_in_equity_form', $data));
    }

    /**
     * توليد قائمة التغيرات في حقوق الملكية المتقدمة
     */
    public function generate() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/changes_in_equity') ||
            !$this->user->hasKey('accounting_equity_generate')) {

            $this->central_service->logActivity('unauthorized_generate', 'accounts',
                $this->language->get('log_unauthorized_generate_equity_changes'), [
                'user_id' => $this->user->getId(),
                'action' => 'generate_equity_changes'
            ]);

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/changes_in_equity');
        $this->load->model('accounts/changes_in_equity');

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm()) {
            try {
                $filter_data = $this->prepareFilterData();

                // تسجيل توليد التقرير
                $this->central_service->logActivity('generate_report', 'accounts',
                    $this->language->get('log_generate_equity_changes_period') . ': ' . $filter_data['date_start'] . ' ' . $this->language->get('text_to') . ' ' . $filter_data['date_end'], [
                    'user_id' => $this->user->getId(),
                    'date_start' => $filter_data['date_start'],
                    'date_end' => $filter_data['date_end']
                ]);

                $equity_changes_data = $this->model_accounts_changes_in_equity->generateEquityChanges($filter_data);

                // إرسال إشعار للمحاسب الرئيسي
                $this->central_service->sendNotification(
                    'equity_changes_generated',
                    $this->language->get('text_equity_changes_generated'),
                    $this->language->get('text_equity_changes_generated_message') . ' ' . $filter_data['date_start'] . ' ' . $this->language->get('text_to') . ' ' . $filter_data['date_end'] . ' ' . $this->language->get('text_by_user') . ' ' . $this->user->getFirstName(),
                    [$this->config->get('config_chief_accountant_id')],
                    [
                        'period' => $filter_data['date_start'] . ' - ' . $filter_data['date_end'],
                        'user_name' => $this->user->getFirstName() . ' ' . $this->user->getLastName(),
                        'total_equity_change' => $equity_changes_data['totals']['total_equity_change'] ?? 0
                    ]
                );

                $this->session->data['success'] = $this->language->get('text_success_generate');
                $this->session->data['equity_changes_data'] = $equity_changes_data;
                $this->session->data['equity_changes_filter'] = $filter_data;

                $this->response->redirect($this->url->link('accounts/changes_in_equity/view', 'user_token=' . $this->session->data['user_token'], true));
            } catch (Exception $e) {
                $this->error['warning'] = $e->getMessage();
            }
        }

        $this->getForm();
    }

    /**
     * عرض قائمة التغيرات في حقوق الملكية
     */
    public function view() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/changes_in_equity') ||
            !$this->user->hasKey('accounting_equity_view')) {

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/changes_in_equity');
        $this->document->setTitle($this->language->get('heading_title'));

        if (!isset($this->session->data['equity_changes_data'])) {
            $this->session->data['error'] = $this->language->get('error_no_data');
            $this->response->redirect($this->url->link('accounts/changes_in_equity', 'user_token=' . $this->session->data['user_token'], true));
            return;
        }

        $data = $this->session->data['equity_changes_data'];

        // تسجيل عرض التقرير
        $this->central_service->logActivity('view_report', 'accounts',
            $this->language->get('log_view_equity_changes_report'), [
            'user_id' => $this->user->getId(),
            'action' => 'view_equity_changes'
        ]);

        $data['breadcrumbs'] = array();
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('accounts/changes_in_equity', 'user_token=' . $this->session->data['user_token'], true)
        );

        $data['export_excel'] = $this->url->link('accounts/changes_in_equity/export', 'format=excel&user_token=' . $this->session->data['user_token'], true);
        $data['export_pdf'] = $this->url->link('accounts/changes_in_equity/export', 'format=pdf&user_token=' . $this->session->data['user_token'], true);
        $data['export_csv'] = $this->url->link('accounts/changes_in_equity/export', 'format=csv&user_token=' . $this->session->data['user_token'], true);

        $data['user_token'] = $this->session->data['user_token'];
        $data['heading_title'] = $this->language->get('heading_title');

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/changes_in_equity_view', $data));
    }

    /**
     * تصدير قائمة التغيرات في حقوق الملكية
     */
    public function export() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/changes_in_equity') ||
            !$this->user->hasKey('accounting_equity_export')) {

            $this->central_service->logActivity('unauthorized_export', 'accounts',
                $this->language->get('log_unauthorized_export_equity_changes'), [
                'user_id' => $this->user->getId(),
                'action' => 'export_equity_changes'
            ]);

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/changes_in_equity');
        $this->load->model('accounts/changes_in_equity');

        if (!isset($this->session->data['equity_changes_data'])) {
            $this->session->data['error'] = $this->language->get('error_no_data');
            $this->response->redirect($this->url->link('accounts/changes_in_equity', 'user_token=' . $this->session->data['user_token'], true));
            return;
        }

        $format = $this->request->get['format'] ?? 'excel';
        $equity_changes_data = $this->session->data['equity_changes_data'];
        $filter_data = $this->session->data['equity_changes_filter'] ?? array();

        // تسجيل عملية التصدير
        $this->central_service->logActivity('export', 'accounts',
            $this->language->get('log_export_equity_changes') . ' - ' . strtoupper($format), [
            'user_id' => $this->user->getId(),
            'format' => $format,
            'period' => ($filter_data['date_start'] ?? '') . ' - ' . ($filter_data['date_end'] ?? '')
        ]);

        // إرسال إشعار للمحاسب الرئيسي
        $this->central_service->sendNotification(
            'equity_changes_exported',
            $this->language->get('text_equity_changes_exported'),
            $this->language->get('text_equity_changes_exported_message') . ' ' . strtoupper($format) . ' ' . $this->language->get('text_by_user') . ' ' . $this->user->getFirstName(),
            [$this->config->get('config_chief_accountant_id')],
            [
                'format' => $format,
                'period' => ($filter_data['date_start'] ?? '') . ' - ' . ($filter_data['date_end'] ?? ''),
                'user_name' => $this->user->getFirstName() . ' ' . $this->user->getLastName()
            ]
        );

        switch ($format) {
            case 'excel':
                $this->exportToExcel($equity_changes_data, $filter_data);
                break;
            case 'pdf':
                $this->exportToPdf($equity_changes_data, $filter_data);
                break;
            case 'csv':
                $this->exportToCsv($equity_changes_data, $filter_data);
                break;
            default:
                $this->exportToExcel($equity_changes_data, $filter_data);
        }
    }

    public function print() {
        $this->load->language('accounts/changes_in_equity');
        $this->load->model('accounts/changes_in_equity');

        $data['title'] = $this->language->get('print_title');
        $data['printdate'] = date('Y-m-d H:i:s');
        $data['user_token'] = $this->session->data['user_token'];
        $data['lang'] = $this->language->get('code');
        $data['direction'] = $this->language->get('direction');  
        $data['whoprint'] = $this->user->getUserName();

        $date_start = $this->request->post['date_start'] ?: date('Y-01-01');
        $date_end = $this->request->post['date_end'] ?: date('Y-m-d');

        $data['start_date'] = date($this->language->get('date_format_short'), strtotime($date_start));
        $data['end_date'] = date($this->language->get('date_format_short'), strtotime($date_end));

        if ($date_start && $date_end) {
            $results = $this->model_accounts_changes_in_equity->getChangesInEquityData($date_start, $date_end);
            $data['accounts'] = $results['accounts'];
            $data['total_opening'] = $results['total_opening'];
            $data['total_movement'] = $results['total_movement'];
            $data['total_closing'] = $results['total_closing'];
        } else {
            $data['accounts'] = [];
            $data['total_opening'] = $this->currency->format(0, $this->config->get('config_currency'));
            $data['total_movement'] = $this->currency->format(0, $this->config->get('config_currency'));
            $data['total_closing'] = $this->currency->format(0, $this->config->get('config_currency'));
            $this->error['warning'] = $this->language->get('error_no_data');
        }

        $data['text_changes_in_equity'] = $this->language->get('text_changes_in_equity');
        $data['text_period'] = $this->language->get('text_period');
        $data['text_from'] = $this->language->get('text_from');
        $data['text_to'] = $this->language->get('text_to');
        $data['text_opening_balance'] = $this->language->get('text_opening_balance');
        $data['text_movement'] = $this->language->get('text_movement');
        $data['text_closing_balance'] = $this->language->get('text_closing_balance');
        $data['text_account_name'] = $this->language->get('text_account_name');
        $data['text_total'] = $this->language->get('text_total');

        $this->response->setOutput($this->load->view('accounts/changes_in_equity_list', $data));
    }

    /**
     * التحقق من صحة البيانات
     */
    protected function validateForm() {
        if (!$this->user->hasPermission('access', 'accounts/changes_in_equity')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }

        if (empty($this->request->post['date_start'])) {
            $this->error['date_start'] = $this->language->get('error_date_start');
        }

        if (empty($this->request->post['date_end'])) {
            $this->error['date_end'] = $this->language->get('error_date_end');
        }

        if (!empty($this->request->post['date_start']) && !empty($this->request->post['date_end'])) {
            if (strtotime($this->request->post['date_start']) > strtotime($this->request->post['date_end'])) {
                $this->error['date_range'] = $this->language->get('error_date_range');
            }
        }

        return !$this->error;
    }

    /**
     * إعداد بيانات الفلترة
     */
    protected function prepareFilterData() {
        return array(
            'date_start' => $this->request->post['date_start'] ?: date('Y-01-01'),
            'date_end' => $this->request->post['date_end'] ?: date('Y-m-d'),
            'include_zero_balances' => isset($this->request->post['include_zero_balances']) ? 1 : 0,
            'show_comparative' => isset($this->request->post['show_comparative']) ? 1 : 0,
            'comparative_date_start' => $this->request->post['comparative_date_start'] ?? '',
            'comparative_date_end' => $this->request->post['comparative_date_end'] ?? '',
            'currency' => $this->request->post['currency'] ?? $this->config->get('config_currency'),
            'equity_accounts' => $this->request->post['equity_accounts'] ?? array()
        );
    }

    /**
     * عرض النموذج
     */
    protected function getForm() {
        $data['breadcrumbs'] = array();
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('accounts/changes_in_equity', 'user_token=' . $this->session->data['user_token'], true)
        );

        $data['action'] = $this->url->link('accounts/changes_in_equity/generate', 'user_token=' . $this->session->data['user_token'], true);

        // تحميل قوائم البيانات
        $this->load->model('accounts/chartaccount');
        $this->load->model('branch/branch');

        $data['equity_accounts'] = $this->model_accounts_chartaccount->getEquityAccounts();
        $data['branches'] = $this->model_branch_branch->getBranches();

        // القيم الافتراضية
        $data['date_start'] = $this->request->post['date_start'] ?? date('Y-01-01');
        $data['date_end'] = $this->request->post['date_end'] ?? date('Y-m-d');
        $data['include_zero_balances'] = $this->request->post['include_zero_balances'] ?? false;

        $data['user_token'] = $this->session->data['user_token'];
        $data['heading_title'] = $this->language->get('heading_title');
        $data['text_no_results'] = $this->language->get('text_no_results');

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/changes_in_equity_form', $data));
    }

    /**
     * تصدير إلى Excel
     */
    private function exportToExcel($equity_data, $filter_data) {
        $filename = 'changes_in_equity_' . date('Y-m-d') . '.xls';

        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        echo '<table border="1">';
        echo '<tr><th colspan="5" style="text-align:center;font-weight:bold;background-color:#f0f0f0;">' . $this->language->get('heading_title') . '</th></tr>';
        echo '<tr><th colspan="5" style="text-align:center;">' . $this->language->get('text_period') . ': ' . $filter_data['date_start'] . ' - ' . $filter_data['date_end'] . '</th></tr>';
        echo '<tr style="background-color:#e0e0e0;font-weight:bold;">';
        echo '<th>' . $this->language->get('text_account_name') . '</th>';
        echo '<th>' . $this->language->get('text_opening_balance') . '</th>';
        echo '<th>' . $this->language->get('text_increase') . '</th>';
        echo '<th>' . $this->language->get('text_decrease') . '</th>';
        echo '<th>' . $this->language->get('text_closing_balance') . '</th>';
        echo '</tr>';

        // بيانات الحسابات
        if (isset($equity_data['accounts']) && !empty($equity_data['accounts'])) {
            foreach ($equity_data['accounts'] as $account) {
                echo '<tr>';
                echo '<td>' . htmlspecialchars($account['name']) . '</td>';
                echo '<td style="text-align:right;">' . $this->currency->format($account['opening_balance'], $this->config->get('config_currency')) . '</td>';
                echo '<td style="text-align:right;">' . $this->currency->format($account['increase'], $this->config->get('config_currency')) . '</td>';
                echo '<td style="text-align:right;">' . $this->currency->format($account['decrease'], $this->config->get('config_currency')) . '</td>';
                echo '<td style="text-align:right;">' . $this->currency->format($account['closing_balance'], $this->config->get('config_currency')) . '</td>';
                echo '</tr>';
            }
        }

        // الإجماليات
        echo '<tr style="background-color:#f0f0f0;font-weight:bold;">';
        echo '<td>' . $this->language->get('text_total') . '</td>';
        echo '<td style="text-align:right;">' . $this->currency->format($equity_data['totals']['total_opening'] ?? 0, $this->config->get('config_currency')) . '</td>';
        echo '<td style="text-align:right;">' . $this->currency->format($equity_data['totals']['total_increase'] ?? 0, $this->config->get('config_currency')) . '</td>';
        echo '<td style="text-align:right;">' . $this->currency->format($equity_data['totals']['total_decrease'] ?? 0, $this->config->get('config_currency')) . '</td>';
        echo '<td style="text-align:right;">' . $this->currency->format($equity_data['totals']['total_closing'] ?? 0, $this->config->get('config_currency')) . '</td>';
        echo '</tr>';

        echo '</table>';
        exit;
    }

    /**
     * تصدير إلى PDF
     */
    private function exportToPdf($equity_data, $filter_data) {
        require_once(DIR_SYSTEM . 'library/tcpdf/tcpdf.php');

        $pdf = new TCPDF('P', 'mm', 'A4', true, 'UTF-8');
        $pdf->SetCreator('AYM ERP System');
        $pdf->SetAuthor($this->config->get('config_name'));
        $pdf->SetTitle($this->language->get('heading_title'));
        $pdf->SetSubject($this->language->get('text_changes_in_equity'));
        $pdf->SetKeywords('equity, changes, financial, statement');

        // إعدادات الصفحة
        $pdf->SetMargins(15, 27, 15);
        $pdf->SetHeaderMargin(5);
        $pdf->SetFooterMargin(10);
        $pdf->SetAutoPageBreak(TRUE, 25);

        $pdf->AddPage();

        // العنوان الرئيسي
        $pdf->SetFont('dejavusans', 'B', 18);
        $pdf->Cell(0, 15, $this->language->get('heading_title'), 0, 1, 'C');

        // معلومات الفترة
        $pdf->SetFont('dejavusans', '', 12);
        $pdf->Cell(0, 10, $this->language->get('text_period') . ': ' . $filter_data['date_start'] . ' - ' . $filter_data['date_end'], 0, 1, 'C');
        $pdf->Ln(5);

        // رأس الجدول
        $pdf->SetFont('dejavusans', 'B', 10);
        $pdf->SetFillColor(230, 230, 230);
        $pdf->Cell(60, 8, $this->language->get('text_account_name'), 1, 0, 'C', 1);
        $pdf->Cell(30, 8, $this->language->get('text_opening_balance'), 1, 0, 'C', 1);
        $pdf->Cell(30, 8, $this->language->get('text_increase'), 1, 0, 'C', 1);
        $pdf->Cell(30, 8, $this->language->get('text_decrease'), 1, 0, 'C', 1);
        $pdf->Cell(30, 8, $this->language->get('text_closing_balance'), 1, 1, 'C', 1);

        // بيانات الحسابات
        $pdf->SetFont('dejavusans', '', 9);
        if (isset($equity_data['accounts']) && !empty($equity_data['accounts'])) {
            foreach ($equity_data['accounts'] as $account) {
                $pdf->Cell(60, 6, $account['name'], 1, 0, 'L');
                $pdf->Cell(30, 6, $this->currency->format($account['opening_balance'], $this->config->get('config_currency')), 1, 0, 'R');
                $pdf->Cell(30, 6, $this->currency->format($account['increase'], $this->config->get('config_currency')), 1, 0, 'R');
                $pdf->Cell(30, 6, $this->currency->format($account['decrease'], $this->config->get('config_currency')), 1, 0, 'R');
                $pdf->Cell(30, 6, $this->currency->format($account['closing_balance'], $this->config->get('config_currency')), 1, 1, 'R');
            }
        }

        // الإجماليات
        $pdf->SetFont('dejavusans', 'B', 10);
        $pdf->SetFillColor(240, 240, 240);
        $pdf->Cell(60, 8, $this->language->get('text_total'), 1, 0, 'C', 1);
        $pdf->Cell(30, 8, $this->currency->format($equity_data['totals']['total_opening'] ?? 0, $this->config->get('config_currency')), 1, 0, 'R', 1);
        $pdf->Cell(30, 8, $this->currency->format($equity_data['totals']['total_increase'] ?? 0, $this->config->get('config_currency')), 1, 0, 'R', 1);
        $pdf->Cell(30, 8, $this->currency->format($equity_data['totals']['total_decrease'] ?? 0, $this->config->get('config_currency')), 1, 0, 'R', 1);
        $pdf->Cell(30, 8, $this->currency->format($equity_data['totals']['total_closing'] ?? 0, $this->config->get('config_currency')), 1, 1, 'R', 1);

        $pdf->Output('changes_in_equity_' . date('Y-m-d') . '.pdf', 'D');
        exit;
    }

    /**
     * تصدير إلى CSV
     */
    private function exportToCsv($equity_data, $filter_data) {
        $filename = 'changes_in_equity_' . date('Y-m-d') . '.csv';

        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $output = fopen('php://output', 'w');
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

        // العنوان والفترة
        fputcsv($output, array($this->language->get('heading_title')));
        fputcsv($output, array($this->language->get('text_period') . ': ' . $filter_data['date_start'] . ' - ' . $filter_data['date_end']));
        fputcsv($output, array('')); // سطر فارغ

        // رأس الجدول
        fputcsv($output, array(
            $this->language->get('text_account_name'),
            $this->language->get('text_opening_balance'),
            $this->language->get('text_increase'),
            $this->language->get('text_decrease'),
            $this->language->get('text_closing_balance')
        ));

        // بيانات الحسابات
        if (isset($equity_data['accounts']) && !empty($equity_data['accounts'])) {
            foreach ($equity_data['accounts'] as $account) {
                fputcsv($output, array(
                    $account['name'],
                    $account['opening_balance'],
                    $account['increase'],
                    $account['decrease'],
                    $account['closing_balance']
                ));
            }
        }

        // سطر فارغ قبل الإجماليات
        fputcsv($output, array(''));

        // الإجماليات
        fputcsv($output, array(
            $this->language->get('text_total'),
            $equity_data['totals']['total_opening'] ?? 0,
            $equity_data['totals']['total_increase'] ?? 0,
            $equity_data['totals']['total_decrease'] ?? 0,
            $equity_data['totals']['total_closing'] ?? 0
        ));

        fclose($output);
        exit;
    }

    /**
     * دالة تنظيف المخرجات (CONSTITUTIONAL REQUIREMENT)
     * Sanitize all output data to prevent XSS attacks
     */
    private function sanitizeOutputData($data) {
        if (is_array($data)) {
            foreach ($data as $key => $value) {
                $data[$key] = $this->sanitizeOutputData($value);
            }
        } elseif (is_string($data)) {
            $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
        }
        return $data;
    }

    /**
     * Enterprise Grade Plus Security Enhancements
     */
    private function validateInputData($data) {
        $validated = array();

        if (isset($data['date_start'])) {
            $validated['date_start'] = date('Y-m-d', strtotime($data['date_start']));
        }

        if (isset($data['date_end'])) {
            $validated['date_end'] = date('Y-m-d', strtotime($data['date_end']));
        }

        if (isset($data['currency_id'])) {
            $validated['currency_id'] = (int)$data['currency_id'];
        }

        if (isset($data['branch_id'])) {
            $validated['branch_id'] = (int)$data['branch_id'];
        }

        return $validated;
    }

    /**
     * Rate Limiting Implementation
     */
    private function checkRateLimit() {
        $ip = $this->request->server['REMOTE_ADDR'];
        $user_id = $this->user->getId();

        return $this->central_service->checkRateLimit('equity_changes', $ip, $user_id, 25, 3600); // 25 requests per hour
    }

    /**
     * Performance Optimization - Memory Management
     */
    private function optimizeMemoryUsage() {
        ini_set('memory_limit', '512M');
        ini_set('max_execution_time', 300); // 5 minutes for equity changes generation
    }
}
