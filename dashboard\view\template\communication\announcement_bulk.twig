{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="submit" form="communication\announcements-form" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fas fa-save"></i></button>
        <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-light"><i class="fas fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    
    <div class="card">
      <div class="card-header"><i class="fas fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}chart-bar{% elif screen_type == 'settings' %}cog{% else %}pencil-alt{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="card-body">
        <form id="communication\announcements-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-active_announcements">{{ text_active_announcements }}</label>
            <div class="col-sm-10">
              <input type="text" name="active_announcements" value="{{ active_announcements }}" placeholder="{{ text_active_announcements }}" id="input-active_announcements" class="form-control" />
              {% if error_active_announcements %}
                <div class="invalid-feedback">{{ error_active_announcements }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-add">{{ text_add }}</label>
            <div class="col-sm-10">
              <input type="text" name="add" value="{{ add }}" placeholder="{{ text_add }}" id="input-add" class="form-control" />
              {% if error_add %}
                <div class="invalid-feedback">{{ error_add }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-analytics">{{ text_analytics }}</label>
            <div class="col-sm-10">
              <input type="text" name="analytics" value="{{ analytics }}" placeholder="{{ text_analytics }}" id="input-analytics" class="form-control" />
              {% if error_analytics %}
                <div class="invalid-feedback">{{ error_analytics }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-announcement">{{ text_announcement }}</label>
            <div class="col-sm-10">
              <input type="text" name="announcement" value="{{ announcement }}" placeholder="{{ text_announcement }}" id="input-announcement" class="form-control" />
              {% if error_announcement %}
                <div class="invalid-feedback">{{ error_announcement }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-announcement_stats">{{ text_announcement_stats }}</label>
            <div class="col-sm-10">
              <input type="text" name="announcement_stats" value="{{ announcement_stats }}" placeholder="{{ text_announcement_stats }}" id="input-announcement_stats" class="form-control" />
              {% if error_announcement_stats %}
                <div class="invalid-feedback">{{ error_announcement_stats }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-announcement_templates">{{ text_announcement_templates }}</label>
            <div class="col-sm-10">
              <input type="text" name="announcement_templates" value="{{ announcement_templates }}" placeholder="{{ text_announcement_templates }}" id="input-announcement_templates" class="form-control" />
              {% if error_announcement_templates %}
                <div class="invalid-feedback">{{ error_announcement_templates }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-announcement_types">{{ text_announcement_types }}</label>
            <div class="col-sm-10">
              <input type="text" name="announcement_types" value="{{ announcement_types }}" placeholder="{{ text_announcement_types }}" id="input-announcement_types" class="form-control" />
              {% if error_announcement_types %}
                <div class="invalid-feedback">{{ error_announcement_types }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-attachments">{{ text_attachments }}</label>
            <div class="col-sm-10">
              <input type="text" name="attachments" value="{{ attachments }}" placeholder="{{ text_attachments }}" id="input-attachments" class="form-control" />
              {% if error_attachments %}
                <div class="invalid-feedback">{{ error_attachments }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-back">{{ text_back }}</label>
            <div class="col-sm-10">
              <input type="text" name="back" value="{{ back }}" placeholder="{{ text_back }}" id="input-back" class="form-control" />
              {% if error_back %}
                <div class="invalid-feedback">{{ error_back }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-bulk_send">{{ text_bulk_send }}</label>
            <div class="col-sm-10">
              <input type="text" name="bulk_send" value="{{ bulk_send }}" placeholder="{{ text_bulk_send }}" id="input-bulk_send" class="form-control" />
              {% if error_bulk_send %}
                <div class="invalid-feedback">{{ error_bulk_send }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-comments">{{ text_comments }}</label>
            <div class="col-sm-10">
              <input type="text" name="comments" value="{{ comments }}" placeholder="{{ text_comments }}" id="input-comments" class="form-control" />
              {% if error_comments %}
                <div class="invalid-feedback">{{ error_comments }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-current_user_id">{{ text_current_user_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="current_user_id" value="{{ current_user_id }}" placeholder="{{ text_current_user_id }}" id="input-current_user_id" class="form-control" />
              {% if error_current_user_id %}
                <div class="invalid-feedback">{{ error_current_user_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-delete">{{ text_delete }}</label>
            <div class="col-sm-10">
              <input type="text" name="delete" value="{{ delete }}" placeholder="{{ text_delete }}" id="input-delete" class="form-control" />
              {% if error_delete %}
                <div class="invalid-feedback">{{ error_delete }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-edit">{{ text_edit }}</label>
            <div class="col-sm-10">
              <input type="text" name="edit" value="{{ edit }}" placeholder="{{ text_edit }}" id="input-edit" class="form-control" />
              {% if error_edit %}
                <div class="invalid-feedback">{{ error_edit }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-expired_announcements">{{ text_expired_announcements }}</label>
            <div class="col-sm-10">
              <input type="text" name="expired_announcements" value="{{ expired_announcements }}" placeholder="{{ text_expired_announcements }}" id="input-expired_announcements" class="form-control" />
              {% if error_expired_announcements %}
                <div class="invalid-feedback">{{ error_expired_announcements }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="invalid-feedback">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-personal_announcements">{{ text_personal_announcements }}</label>
            <div class="col-sm-10">
              <input type="text" name="personal_announcements" value="{{ personal_announcements }}" placeholder="{{ text_personal_announcements }}" id="input-personal_announcements" class="form-control" />
              {% if error_personal_announcements %}
                <div class="invalid-feedback">{{ error_personal_announcements }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-quick_actions">{{ text_quick_actions }}</label>
            <div class="col-sm-10">
              <input type="text" name="quick_actions" value="{{ quick_actions }}" placeholder="{{ text_quick_actions }}" id="input-quick_actions" class="form-control" />
              {% if error_quick_actions %}
                <div class="invalid-feedback">{{ error_quick_actions }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-related_announcements">{{ text_related_announcements }}</label>
            <div class="col-sm-10">
              <input type="text" name="related_announcements" value="{{ related_announcements }}" placeholder="{{ text_related_announcements }}" id="input-related_announcements" class="form-control" />
              {% if error_related_announcements %}
                <div class="invalid-feedback">{{ error_related_announcements }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-scheduled_announcements">{{ text_scheduled_announcements }}</label>
            <div class="col-sm-10">
              <input type="text" name="scheduled_announcements" value="{{ scheduled_announcements }}" placeholder="{{ text_scheduled_announcements }}" id="input-scheduled_announcements" class="form-control" />
              {% if error_scheduled_announcements %}
                <div class="invalid-feedback">{{ error_scheduled_announcements }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-specialized_analytics">{{ text_specialized_analytics }}</label>
            <div class="col-sm-10">
              <input type="text" name="specialized_analytics" value="{{ specialized_analytics }}" placeholder="{{ text_specialized_analytics }}" id="input-specialized_analytics" class="form-control" />
              {% if error_specialized_analytics %}
                <div class="invalid-feedback">{{ error_specialized_analytics }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-specialized_announcements">{{ text_specialized_announcements }}</label>
            <div class="col-sm-10">
              <input type="text" name="specialized_announcements" value="{{ specialized_announcements }}" placeholder="{{ text_specialized_announcements }}" id="input-specialized_announcements" class="form-control" />
              {% if error_specialized_announcements %}
                <div class="invalid-feedback">{{ error_specialized_announcements }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-templates">{{ text_templates }}</label>
            <div class="col-sm-10">
              <input type="text" name="templates" value="{{ templates }}" placeholder="{{ text_templates }}" id="input-templates" class="form-control" />
              {% if error_templates %}
                <div class="invalid-feedback">{{ error_templates }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-urgent_announcements">{{ text_urgent_announcements }}</label>
            <div class="col-sm-10">
              <input type="text" name="urgent_announcements" value="{{ urgent_announcements }}" placeholder="{{ text_urgent_announcements }}" id="input-urgent_announcements" class="form-control" />
              {% if error_urgent_announcements %}
                <div class="invalid-feedback">{{ error_urgent_announcements }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_groups">{{ text_user_groups }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_groups" value="{{ user_groups }}" placeholder="{{ text_user_groups }}" id="input-user_groups" class="form-control" />
              {% if error_user_groups %}
                <div class="invalid-feedback">{{ error_user_groups }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="invalid-feedback">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-users">{{ text_users }}</label>
            <div class="col-sm-10">
              <input type="text" name="users" value="{{ users }}" placeholder="{{ text_users }}" id="input-users" class="form-control" />
              {% if error_users %}
                <div class="invalid-feedback">{{ error_users }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-view_stats">{{ text_view_stats }}</label>
            <div class="col-sm-10">
              <input type="text" name="view_stats" value="{{ view_stats }}" placeholder="{{ text_view_stats }}" id="input-view_stats" class="form-control" />
              {% if error_view_stats %}
                <div class="invalid-feedback">{{ error_view_stats }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}