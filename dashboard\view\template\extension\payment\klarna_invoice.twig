{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="submit" form="form-klarna-invoice" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fa fa-save"></i></button>
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a></div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    {% if success %}
    <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pencil"></i> {{ text_edit }}</h3>
      </div>
      <div class="panel-body">
        <form action="{{ action }}" method="post" enctype="multipart/form-data" id="form-klarna-invoice" class="form-horizontal">
          <ul class="nav nav-tabs">
            <li class="active"><a href="#tab-general" data-toggle="tab">{{ tab_general }}</a></li>
            <li><a href="#tab-log" data-toggle="tab">{{ tab_log }}</a></li>
          </ul>
          <div class="tab-content">
            <div class="tab-pane active" id="tab-general"> <a href="https://merchants.klarna.com/signup?locale=en&partner_id=d5c87110cebc383a826364769047042e777da5e8&utm_campaign=Platform&utm_medium=Partners&utm_source=Opencart" target="_blank" style="float: right;"><img src="view/image/payment/klarna_banner.gif" /></a>
              <ul class="nav nav-tabs" id="country">
                {% for country in countries %}
                <li><a href="#tab-{{ country.code }}" data-toggle="tab">{{ country.name }}</a></li>
                {% endfor %}
              </ul>
              <div class="tab-content">
                {% for country in countries %}
                <div class="tab-pane" id="tab-{{ country.code }}">
                  <div class="form-group">
                    <label class="col-sm-2 control-label" for="input-merchant{{ country.code }}"><span data-toggle="tooltip" title="{{ help_merchant }}">{{ entry_merchant }}</span></label>
                    <div class="col-sm-10">
                      <input type="text" name="payment_klarna_invoice[{{ country.code }}][merchant]" value="{{ payment_klarna_invoice[country.code] ? payment_klarna_invoice[country.code].merchant }}" placeholder="{{ entry_merchant }}" id="input-merchant{{ country.code }}" class="form-control" />
                    </div>
                  </div>
                  <div class="form-group">
                    <label class="col-sm-2 control-label" for="input-secret{{ country.code }}"><span data-toggle="tooltip" title="{{ help_secret }}">{{ entry_secret }}</span></label>
                    <div class="col-sm-10">
                      <input type="text" name="payment_klarna_invoice[{{ country.code }}][secret]" value="{{ payment_klarna_invoice[country.code] ? payment_klarna_invoice[country.code].secret }}" placeholder="{{ entry_secret }}" id="input-secret{{ country.code }}" class="form-control" />
                    </div>
                  </div>
                  <div class="form-group">
                    <label class="col-sm-2 control-label" for="input-server{{ country.code }}">{{ entry_server }}</label>
                    <div class="col-sm-10">
                      <select name="payment_klarna_invoice[{{ country.code }}][server]" id="input-server{{ country.code }}" class="form-control">
                        {% if payment_klarna_invoice[country.code] is defined and payment_klarna_invoice[country.code].server == 'live' %}
                        <option value="live" selected="selected">{{ text_live }}</option>
                        {% else %}
                        <option value="live">{{ text_live }}</option>
                        {% endif %}
                        {% if payment_klarna_invoice[country.code] is defined and payment_klarna_invoice[country.code].server == 'beta' %}
                        <option value="beta" selected="selected">{{ text_beta }}</option>
                        {% else %}
                        <option value="beta">{{ text_beta }}</option>
                        {% endif %}
                      </select>
                    </div>
                  </div>
                  <div class="form-group">
                    <label class="col-sm-2 control-label" for="input-total{{ country.code }}"><span data-toggle="tooltip" title="{{ help_total }}">{{ entry_total }}</span></label>
                    <div class="col-sm-10">
                      <input type="text" name="payment_klarna_invoice[{{ country.code }}][total]" value="{{ payment_klarna_invoice[country.code] ? payment_klarna_invoice[country.code].total }}" placeholder="{{ entry_total }}" id="input-total{{ country.code }}" class="form-control" />
                    </div>
                  </div>
                  <div class="form-group">
                    <label class="col-sm-2 control-label" for="input-pending-status{{ country.code }}">{{ entry_pending_status }}</label>
                    <div class="col-sm-10">
                      <select name="payment_klarna_invoice[{{ country.code }}][pending_status_id]" id="input-pending-status{{ country.code }}" class="form-control">
                        {% for order_status in order_statuses %}
                        {% if payment_klarna_invoice[country.code] is defined and order_status.order_status_id == payment_klarna_invoice[country.code].pending_status_id %}
                        <option value="{{ order_status.order_status_id }}" selected="selected">{{ order_status.name }}</option>
                        {% else %}
                        <option value="{{ order_status.order_status_id }}">{{ order_status.name }}</option>
                        {% endif %}
                        {% endfor %}
                      </select>
                    </div>
                  </div>
                  <div class="form-group">
                    <label class="col-sm-2 control-label" for="input-accepted-status{{ country.code }}">{{ entry_accepted_status }}</label>
                    <div class="col-sm-10">
                      <select name="payment_klarna_invoice[{{ country.code }}][accepted_status_id]" id="input-accepted-status{{ country.code }}" class="form-control">
                        {% for order_status in order_statuses %}
                        {% if payment_klarna_invoice[country.code] is defined and order_status.order_status_id == payment_klarna_invoice[country.code].accepted_status_id %}
                        <option value="{{ order_status.order_status_id }}" selected="selected">{{ order_status.name }}</option>
                        {% else %}
                        <option value="{{ order_status.order_status_id }}">{{ order_status.name }}</option>
                        {% endif %}
                        {% endfor %}
                      </select>
                    </div>
                  </div>
                  <div class="form-group">
                    <label class="col-sm-2 control-label" for="input-geo-zone{{ country.code }}">{{ entry_geo_zone }}</label>
                    <div class="col-sm-10">
                      <select name="payment_klarna_invoice[{{ country.code }}][geo_zone_id]" id="input-geo-zone{{ country.code }}" class="form-control">
                        <option value="0">{{ text_all_zones }}</option>
                        {% for geo_zone in geo_zones %}
                        {% if payment_klarna_invoice[country.code] is defined and geo_zone.geo_zone_id == payment_klarna_invoice[country.code].geo_zone_id %}
                        <option value="{{ geo_zone.geo_zone_id }}" selected="selected">{{ geo_zone.name }}</option>
                        {% else %}
                        <option value="{{ geo_zone.geo_zone_id }}">{{ geo_zone.name }}</option>
                        {% endif %}
                        {% endfor %}
                      </select>
                    </div>
                  </div>
                  <div class="form-group">
                    <label class="col-sm-2 control-label" for="input-status{{ country.code }}">{{ entry_status }}</label>
                    <div class="col-sm-10">
                      <select name="payment_klarna_invoice[{{ country.code }}][status]" id="input-status{{ country.code }}" class="form-control">
                        {% if payment_klarna_invoice[country.code] and payment_klarna_invoice[country.code].status %}
                        <option value="1" selected="selected">{{ text_enabled }}</option>
                        <option value="0">{{ text_disabled }}</option>
                        {% else %}
                        <option value="1">{{ text_enabled }}</option>
                        <option value="0" selected="selected">{{ text_disabled }}</option>
                        {% endif %}
                      </select>
                    </div>
                  </div>
                  <div class="form-group">
                    <label class="col-sm-2 control-label" for="input-sort-order{{ country.code }}">{{ entry_sort_order }}</label>
                    <div class="col-sm-10">
                      <input type="text" name="payment_klarna_invoice[{{ country.code }}][sort_order]" value="{{ payment_klarna_invoice[country.code] ? payment_klarna_invoice[country.code].sort_order }}" placeholder="{{ entry_sort_order }}" id="input-sort-order{{ country.code }}" class="form-control" />
                    </div>
                  </div>
                </div>
                {% endfor %}
              </div>
            </div>
            <div class="tab-pane" id="tab-log">
              <p>
                <textarea wrap="off" rows="15" class="form-control">{{ log }}</textarea>
              </p>
              <div class="text-right"><a href="{{ clear }}" class="btn btn-danger"><i class="fa fa-eraser"></i> {{ button_clear }}</a></div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
  <script type="text/javascript"><!--
$('#country a:first').tab('show');
//--></script> </div>
{{ footer }}
