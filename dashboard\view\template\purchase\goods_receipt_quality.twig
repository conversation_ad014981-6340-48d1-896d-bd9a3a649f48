{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="submit" form="purchase\goods_receipt-form" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fas fa-save"></i></button>
        <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-light"><i class="fas fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    
    <div class="card">
      <div class="card-header"><i class="fas fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}chart-bar{% elif screen_type == 'settings' %}cog{% else %}pencil-alt{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="card-body">
        <form id="purchase\goods_receipt-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-action_item">{{ text_action_item }}</label>
            <div class="col-sm-10">
              <input type="text" name="action_item" value="{{ action_item }}" placeholder="{{ text_action_item }}" id="input-action_item" class="form-control" />
              {% if error_action_item %}
                <div class="invalid-feedback">{{ error_action_item }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-action_save">{{ text_action_save }}</label>
            <div class="col-sm-10">
              <input type="text" name="action_save" value="{{ action_save }}" placeholder="{{ text_action_save }}" id="input-action_save" class="form-control" />
              {% if error_action_save %}
                <div class="invalid-feedback">{{ error_action_save }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-back">{{ text_back }}</label>
            <div class="col-sm-10">
              <input type="text" name="back" value="{{ back }}" placeholder="{{ text_back }}" id="input-back" class="form-control" />
              {% if error_back %}
                <div class="invalid-feedback">{{ error_back }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-branches">{{ text_branches }}</label>
            <div class="col-sm-10">
              <input type="text" name="branches" value="{{ branches }}" placeholder="{{ text_branches }}" id="input-branches" class="form-control" />
              {% if error_branches %}
                <div class="invalid-feedback">{{ error_branches }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-button_back">{{ text_button_back }}</label>
            <div class="col-sm-10">
              <input type="text" name="button_back" value="{{ button_back }}" placeholder="{{ text_button_back }}" id="input-button_back" class="form-control" />
              {% if error_button_back %}
                <div class="invalid-feedback">{{ error_button_back }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-button_cancel">{{ text_button_cancel }}</label>
            <div class="col-sm-10">
              <input type="text" name="button_cancel" value="{{ button_cancel }}" placeholder="{{ text_button_cancel }}" id="input-button_cancel" class="form-control" />
              {% if error_button_cancel %}
                <div class="invalid-feedback">{{ error_button_cancel }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-button_check">{{ text_button_check }}</label>
            <div class="col-sm-10">
              <input type="text" name="button_check" value="{{ button_check }}" placeholder="{{ text_button_check }}" id="input-button_check" class="form-control" />
              {% if error_button_check %}
                <div class="invalid-feedback">{{ error_button_check }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-button_close">{{ text_button_close }}</label>
            <div class="col-sm-10">
              <input type="text" name="button_close" value="{{ button_close }}" placeholder="{{ text_button_close }}" id="input-button_close" class="form-control" />
              {% if error_button_close %}
                <div class="invalid-feedback">{{ error_button_close }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-button_complete">{{ text_button_complete }}</label>
            <div class="col-sm-10">
              <input type="text" name="button_complete" value="{{ button_complete }}" placeholder="{{ text_button_complete }}" id="input-button_complete" class="form-control" />
              {% if error_button_complete %}
                <div class="invalid-feedback">{{ error_button_complete }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-button_delete">{{ text_button_delete }}</label>
            <div class="col-sm-10">
              <input type="text" name="button_delete" value="{{ button_delete }}" placeholder="{{ text_button_delete }}" id="input-button_delete" class="form-control" />
              {% if error_button_delete %}
                <div class="invalid-feedback">{{ error_button_delete }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-button_download">{{ text_button_download }}</label>
            <div class="col-sm-10">
              <input type="text" name="button_download" value="{{ button_download }}" placeholder="{{ text_button_download }}" id="input-button_download" class="form-control" />
              {% if error_button_download %}
                <div class="invalid-feedback">{{ error_button_download }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-button_edit">{{ text_button_edit }}</label>
            <div class="col-sm-10">
              <input type="text" name="button_edit" value="{{ button_edit }}" placeholder="{{ text_button_edit }}" id="input-button_edit" class="form-control" />
              {% if error_button_edit %}
                <div class="invalid-feedback">{{ error_button_edit }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-button_print">{{ text_button_print }}</label>
            <div class="col-sm-10">
              <input type="text" name="button_print" value="{{ button_print }}" placeholder="{{ text_button_print }}" id="input-button_print" class="form-control" />
              {% if error_button_print %}
                <div class="invalid-feedback">{{ error_button_print }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-button_quality_check">{{ text_button_quality_check }}</label>
            <div class="col-sm-10">
              <input type="text" name="button_quality_check" value="{{ button_quality_check }}" placeholder="{{ text_button_quality_check }}" id="input-button_quality_check" class="form-control" />
              {% if error_button_quality_check %}
                <div class="invalid-feedback">{{ error_button_quality_check }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-button_save">{{ text_button_save }}</label>
            <div class="col-sm-10">
              <input type="text" name="button_save" value="{{ button_save }}" placeholder="{{ text_button_save }}" id="input-button_save" class="form-control" />
              {% if error_button_save %}
                <div class="invalid-feedback">{{ error_button_save }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-button_upload">{{ text_button_upload }}</label>
            <div class="col-sm-10">
              <input type="text" name="button_upload" value="{{ button_upload }}" placeholder="{{ text_button_upload }}" id="input-button_upload" class="form-control" />
              {% if error_button_upload %}
                <div class="invalid-feedback">{{ error_button_upload }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-can_add">{{ text_can_add }}</label>
            <div class="col-sm-10">
              <input type="text" name="can_add" value="{{ can_add }}" placeholder="{{ text_can_add }}" id="input-can_add" class="form-control" />
              {% if error_can_add %}
                <div class="invalid-feedback">{{ error_can_add }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-can_complete">{{ text_can_complete }}</label>
            <div class="col-sm-10">
              <input type="text" name="can_complete" value="{{ can_complete }}" placeholder="{{ text_can_complete }}" id="input-can_complete" class="form-control" />
              {% if error_can_complete %}
                <div class="invalid-feedback">{{ error_can_complete }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-can_delete">{{ text_can_delete }}</label>
            <div class="col-sm-10">
              <input type="text" name="can_delete" value="{{ can_delete }}" placeholder="{{ text_can_delete }}" id="input-can_delete" class="form-control" />
              {% if error_can_delete %}
                <div class="invalid-feedback">{{ error_can_delete }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-can_download">{{ text_can_download }}</label>
            <div class="col-sm-10">
              <input type="text" name="can_download" value="{{ can_download }}" placeholder="{{ text_can_download }}" id="input-can_download" class="form-control" />
              {% if error_can_download %}
                <div class="invalid-feedback">{{ error_can_download }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-can_edit">{{ text_can_edit }}</label>
            <div class="col-sm-10">
              <input type="text" name="can_edit" value="{{ can_edit }}" placeholder="{{ text_can_edit }}" id="input-can_edit" class="form-control" />
              {% if error_can_edit %}
                <div class="invalid-feedback">{{ error_can_edit }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-can_print">{{ text_can_print }}</label>
            <div class="col-sm-10">
              <input type="text" name="can_print" value="{{ can_print }}" placeholder="{{ text_can_print }}" id="input-can_print" class="form-control" />
              {% if error_can_print %}
                <div class="invalid-feedback">{{ error_can_print }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-can_quality_check">{{ text_can_quality_check }}</label>
            <div class="col-sm-10">
              <input type="text" name="can_quality_check" value="{{ can_quality_check }}" placeholder="{{ text_can_quality_check }}" id="input-can_quality_check" class="form-control" />
              {% if error_can_quality_check %}
                <div class="invalid-feedback">{{ error_can_quality_check }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-can_upload">{{ text_can_upload }}</label>
            <div class="col-sm-10">
              <input type="text" name="can_upload" value="{{ can_upload }}" placeholder="{{ text_can_upload }}" id="input-can_upload" class="form-control" />
              {% if error_can_upload %}
                <div class="invalid-feedback">{{ error_can_upload }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-can_view">{{ text_can_view }}</label>
            <div class="col-sm-10">
              <input type="text" name="can_view" value="{{ can_view }}" placeholder="{{ text_can_view }}" id="input-can_view" class="form-control" />
              {% if error_can_view %}
                <div class="invalid-feedback">{{ error_can_view }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_action">{{ text_column_action }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_action" value="{{ column_action }}" placeholder="{{ text_column_action }}" id="input-column_action" class="form-control" />
              {% if error_column_action %}
                <div class="invalid-feedback">{{ error_column_action }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_action_type">{{ text_column_action_type }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_action_type" value="{{ column_action_type }}" placeholder="{{ text_column_action_type }}" id="input-column_action_type" class="form-control" />
              {% if error_column_action_type %}
                <div class="invalid-feedback">{{ error_column_action_type }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_date">{{ text_column_date }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_date" value="{{ column_date }}" placeholder="{{ text_column_date }}" id="input-column_date" class="form-control" />
              {% if error_column_date %}
                <div class="invalid-feedback">{{ error_column_date }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_description">{{ text_column_description }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_description" value="{{ column_description }}" placeholder="{{ text_column_description }}" id="input-column_description" class="form-control" />
              {% if error_column_description %}
                <div class="invalid-feedback">{{ error_column_description }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_document_name">{{ text_column_document_name }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_document_name" value="{{ column_document_name }}" placeholder="{{ text_column_document_name }}" id="input-column_document_name" class="form-control" />
              {% if error_column_document_name %}
                <div class="invalid-feedback">{{ error_column_document_name }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_document_type">{{ text_column_document_type }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_document_type" value="{{ column_document_type }}" placeholder="{{ text_column_document_type }}" id="input-column_document_type" class="form-control" />
              {% if error_column_document_type %}
                <div class="invalid-feedback">{{ error_column_document_type }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_invoice_price">{{ text_column_invoice_price }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_invoice_price" value="{{ column_invoice_price }}" placeholder="{{ text_column_invoice_price }}" id="input-column_invoice_price" class="form-control" />
              {% if error_column_invoice_price %}
                <div class="invalid-feedback">{{ error_column_invoice_price }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_ordered_quantity">{{ text_column_ordered_quantity }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_ordered_quantity" value="{{ column_ordered_quantity }}" placeholder="{{ text_column_ordered_quantity }}" id="input-column_ordered_quantity" class="form-control" />
              {% if error_column_ordered_quantity %}
                <div class="invalid-feedback">{{ error_column_ordered_quantity }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_po_price">{{ text_column_po_price }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_po_price" value="{{ column_po_price }}" placeholder="{{ text_column_po_price }}" id="input-column_po_price" class="form-control" />
              {% if error_column_po_price %}
                <div class="invalid-feedback">{{ error_column_po_price }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_product">{{ text_column_product }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_product" value="{{ column_product }}" placeholder="{{ text_column_product }}" id="input-column_product" class="form-control" />
              {% if error_column_product %}
                <div class="invalid-feedback">{{ error_column_product }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_quality_result">{{ text_column_quality_result }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_quality_result" value="{{ column_quality_result }}" placeholder="{{ text_column_quality_result }}" id="input-column_quality_result" class="form-control" />
              {% if error_column_quality_result %}
                <div class="invalid-feedback">{{ error_column_quality_result }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_quantity">{{ text_column_quantity }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_quantity" value="{{ column_quantity }}" placeholder="{{ text_column_quantity }}" id="input-column_quantity" class="form-control" />
              {% if error_column_quantity %}
                <div class="invalid-feedback">{{ error_column_quantity }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_received_quantity">{{ text_column_received_quantity }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_received_quantity" value="{{ column_received_quantity }}" placeholder="{{ text_column_received_quantity }}" id="input-column_received_quantity" class="form-control" />
              {% if error_column_received_quantity %}
                <div class="invalid-feedback">{{ error_column_received_quantity }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_remarks">{{ text_column_remarks }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_remarks" value="{{ column_remarks }}" placeholder="{{ text_column_remarks }}" id="input-column_remarks" class="form-control" />
              {% if error_column_remarks %}
                <div class="invalid-feedback">{{ error_column_remarks }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_unit">{{ text_column_unit }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_unit" value="{{ column_unit }}" placeholder="{{ text_column_unit }}" id="input-column_unit" class="form-control" />
              {% if error_column_unit %}
                <div class="invalid-feedback">{{ error_column_unit }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_upload_date">{{ text_column_upload_date }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_upload_date" value="{{ column_upload_date }}" placeholder="{{ text_column_upload_date }}" id="input-column_upload_date" class="form-control" />
              {% if error_column_upload_date %}
                <div class="invalid-feedback">{{ error_column_upload_date }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_uploaded_by">{{ text_column_uploaded_by }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_uploaded_by" value="{{ column_uploaded_by }}" placeholder="{{ text_column_uploaded_by }}" id="input-column_uploaded_by" class="form-control" />
              {% if error_column_uploaded_by %}
                <div class="invalid-feedback">{{ error_column_uploaded_by }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_user">{{ text_column_user }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_user" value="{{ column_user }}" placeholder="{{ text_column_user }}" id="input-column_user" class="form-control" />
              {% if error_column_user %}
                <div class="invalid-feedback">{{ error_column_user }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-currencies">{{ text_currencies }}</label>
            <div class="col-sm-10">
              <input type="text" name="currencies" value="{{ currencies }}" placeholder="{{ text_currencies }}" id="input-currencies" class="form-control" />
              {% if error_currencies %}
                <div class="invalid-feedback">{{ error_currencies }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-documents">{{ text_documents }}</label>
            <div class="col-sm-10">
              <input type="text" name="documents" value="{{ documents }}" placeholder="{{ text_documents }}" id="input-documents" class="form-control" />
              {% if error_documents %}
                <div class="invalid-feedback">{{ error_documents }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_quality_status">{{ text_error_quality_status }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_quality_status" value="{{ error_quality_status }}" placeholder="{{ text_error_quality_status }}" id="input-error_quality_status" class="form-control" />
              {% if error_error_quality_status %}
                <div class="invalid-feedback">{{ error_error_quality_status }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="invalid-feedback">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-history">{{ text_history }}</label>
            <div class="col-sm-10">
              <input type="text" name="history" value="{{ history }}" placeholder="{{ text_history }}" id="input-history" class="form-control" />
              {% if error_history %}
                <div class="invalid-feedback">{{ error_history }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-home">{{ text_home }}</label>
            <div class="col-sm-10">
              <input type="text" name="home" value="{{ home }}" placeholder="{{ text_home }}" id="input-home" class="form-control" />
              {% if error_home %}
                <div class="invalid-feedback">{{ error_home }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-items">{{ text_items }}</label>
            <div class="col-sm-10">
              <input type="text" name="items" value="{{ items }}" placeholder="{{ text_items }}" id="input-items" class="form-control" />
              {% if error_items %}
                <div class="invalid-feedback">{{ error_items }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-receipt">{{ text_receipt }}</label>
            <div class="col-sm-10">
              <input type="text" name="receipt" value="{{ receipt }}" placeholder="{{ text_receipt }}" id="input-receipt" class="form-control" />
              {% if error_receipt %}
                <div class="invalid-feedback">{{ error_receipt }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-stats">{{ text_stats }}</label>
            <div class="col-sm-10">
              <input type="text" name="stats" value="{{ stats }}" placeholder="{{ text_stats }}" id="input-stats" class="form-control" />
              {% if error_stats %}
                <div class="invalid-feedback">{{ error_stats }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_approved">{{ text_text_approved }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_approved" value="{{ text_approved }}" placeholder="{{ text_text_approved }}" id="input-text_approved" class="form-control" />
              {% if error_text_approved %}
                <div class="invalid-feedback">{{ error_text_approved }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_branch">{{ text_text_branch }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_branch" value="{{ text_branch }}" placeholder="{{ text_text_branch }}" id="input-text_branch" class="form-control" />
              {% if error_text_branch %}
                <div class="invalid-feedback">{{ error_text_branch }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_check_item">{{ text_text_check_item }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_check_item" value="{{ text_check_item }}" placeholder="{{ text_text_check_item }}" id="input-text_check_item" class="form-control" />
              {% if error_text_check_item %}
                <div class="invalid-feedback">{{ error_text_check_item }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_created_by">{{ text_text_created_by }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_created_by" value="{{ text_created_by }}" placeholder="{{ text_text_created_by }}" id="input-text_created_by" class="form-control" />
              {% if error_text_created_by %}
                <div class="invalid-feedback">{{ error_text_created_by }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_date_added">{{ text_text_date_added }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_date_added" value="{{ text_date_added }}" placeholder="{{ text_text_date_added }}" id="input-text_date_added" class="form-control" />
              {% if error_text_date_added %}
                <div class="invalid-feedback">{{ error_text_date_added }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_documents">{{ text_text_documents }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_documents" value="{{ text_documents }}" placeholder="{{ text_text_documents }}" id="input-text_documents" class="form-control" />
              {% if error_text_documents %}
                <div class="invalid-feedback">{{ error_text_documents }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_history">{{ text_text_history }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_history" value="{{ text_history }}" placeholder="{{ text_text_history }}" id="input-text_history" class="form-control" />
              {% if error_text_history %}
                <div class="invalid-feedback">{{ error_text_history }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_invoice_amount">{{ text_text_invoice_amount }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_invoice_amount" value="{{ text_invoice_amount }}" placeholder="{{ text_text_invoice_amount }}" id="input-text_invoice_amount" class="form-control" />
              {% if error_text_invoice_amount %}
                <div class="invalid-feedback">{{ error_text_invoice_amount }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_invoice_date">{{ text_text_invoice_date }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_invoice_date" value="{{ text_invoice_date }}" placeholder="{{ text_text_invoice_date }}" id="input-text_invoice_date" class="form-control" />
              {% if error_text_invoice_date %}
                <div class="invalid-feedback">{{ error_text_invoice_date }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_invoice_number">{{ text_text_invoice_number }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_invoice_number" value="{{ text_invoice_number }}" placeholder="{{ text_text_invoice_number }}" id="input-text_invoice_number" class="form-control" />
              {% if error_text_invoice_number %}
                <div class="invalid-feedback">{{ error_text_invoice_number }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_items">{{ text_text_items }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_items" value="{{ text_items }}" placeholder="{{ text_text_items }}" id="input-text_items" class="form-control" />
              {% if error_text_items %}
                <div class="invalid-feedback">{{ error_text_items }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_items_received">{{ text_text_items_received }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_items_received" value="{{ text_items_received }}" placeholder="{{ text_text_items_received }}" id="input-text_items_received" class="form-control" />
              {% if error_text_items_received %}
                <div class="invalid-feedback">{{ error_text_items_received }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_journal_entry">{{ text_text_journal_entry }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_journal_entry" value="{{ text_journal_entry }}" placeholder="{{ text_text_journal_entry }}" id="input-text_journal_entry" class="form-control" />
              {% if error_text_journal_entry %}
                <div class="invalid-feedback">{{ error_text_journal_entry }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_list">{{ text_text_list }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_list" value="{{ text_list }}" placeholder="{{ text_text_list }}" id="input-text_list" class="form-control" />
              {% if error_text_list %}
                <div class="invalid-feedback">{{ error_text_list }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_no_documents">{{ text_text_no_documents }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_no_documents" value="{{ text_no_documents }}" placeholder="{{ text_text_no_documents }}" id="input-text_no_documents" class="form-control" />
              {% if error_text_no_documents %}
                <div class="invalid-feedback">{{ error_text_no_documents }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_no_history">{{ text_text_no_history }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_no_history" value="{{ text_no_history }}" placeholder="{{ text_text_no_history }}" id="input-text_no_history" class="form-control" />
              {% if error_text_no_history %}
                <div class="invalid-feedback">{{ error_text_no_history }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_no_items">{{ text_text_no_items }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_no_items" value="{{ text_no_items }}" placeholder="{{ text_text_no_items }}" id="input-text_no_items" class="form-control" />
              {% if error_text_no_items %}
                <div class="invalid-feedback">{{ error_text_no_items }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_notes">{{ text_text_notes }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_notes" value="{{ text_notes }}" placeholder="{{ text_text_notes }}" id="input-text_notes" class="form-control" />
              {% if error_text_notes %}
                <div class="invalid-feedback">{{ error_text_notes }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_partial">{{ text_text_partial }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_partial" value="{{ text_partial }}" placeholder="{{ text_text_partial }}" id="input-text_partial" class="form-control" />
              {% if error_text_partial %}
                <div class="invalid-feedback">{{ error_text_partial }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_partially_received">{{ text_text_partially_received }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_partially_received" value="{{ text_partially_received }}" placeholder="{{ text_text_partially_received }}" id="input-text_partially_received" class="form-control" />
              {% if error_text_partially_received %}
                <div class="invalid-feedback">{{ error_text_partially_received }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_pending_receipts">{{ text_text_pending_receipts }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_pending_receipts" value="{{ text_pending_receipts }}" placeholder="{{ text_text_pending_receipts }}" id="input-text_pending_receipts" class="form-control" />
              {% if error_text_pending_receipts %}
                <div class="invalid-feedback">{{ error_text_pending_receipts }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_po_number">{{ text_text_po_number }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_po_number" value="{{ text_po_number }}" placeholder="{{ text_text_po_number }}" id="input-text_po_number" class="form-control" />
              {% if error_text_po_number %}
                <div class="invalid-feedback">{{ error_text_po_number }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_quality_check">{{ text_text_quality_check }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_quality_check" value="{{ text_quality_check }}" placeholder="{{ text_text_quality_check }}" id="input-text_quality_check" class="form-control" />
              {% if error_text_quality_check %}
                <div class="invalid-feedback">{{ error_text_quality_check }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_quality_check_date">{{ text_text_quality_check_date }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_quality_check_date" value="{{ text_quality_check_date }}" placeholder="{{ text_text_quality_check_date }}" id="input-text_quality_check_date" class="form-control" />
              {% if error_text_quality_check_date %}
                <div class="invalid-feedback">{{ error_text_quality_check_date }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_quality_checked_by">{{ text_text_quality_checked_by }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_quality_checked_by" value="{{ text_quality_checked_by }}" placeholder="{{ text_text_quality_checked_by }}" id="input-text_quality_checked_by" class="form-control" />
              {% if error_text_quality_checked_by %}
                <div class="invalid-feedback">{{ error_text_quality_checked_by }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_quality_fail">{{ text_text_quality_fail }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_quality_fail" value="{{ text_quality_fail }}" placeholder="{{ text_text_quality_fail }}" id="input-text_quality_fail" class="form-control" />
              {% if error_text_quality_fail %}
                <div class="invalid-feedback">{{ error_text_quality_fail }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_quality_notes">{{ text_text_quality_notes }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_quality_notes" value="{{ text_quality_notes }}" placeholder="{{ text_text_quality_notes }}" id="input-text_quality_notes" class="form-control" />
              {% if error_text_quality_notes %}
                <div class="invalid-feedback">{{ error_text_quality_notes }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_quality_partial">{{ text_text_quality_partial }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_quality_partial" value="{{ text_quality_partial }}" placeholder="{{ text_text_quality_partial }}" id="input-text_quality_partial" class="form-control" />
              {% if error_text_quality_partial %}
                <div class="invalid-feedback">{{ error_text_quality_partial }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_quality_pass">{{ text_text_quality_pass }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_quality_pass" value="{{ text_quality_pass }}" placeholder="{{ text_text_quality_pass }}" id="input-text_quality_pass" class="form-control" />
              {% if error_text_quality_pass %}
                <div class="invalid-feedback">{{ error_text_quality_pass }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_receipt_date">{{ text_text_receipt_date }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_receipt_date" value="{{ text_receipt_date }}" placeholder="{{ text_text_receipt_date }}" id="input-text_receipt_date" class="form-control" />
              {% if error_text_receipt_date %}
                <div class="invalid-feedback">{{ error_text_receipt_date }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_receipt_details">{{ text_text_receipt_details }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_receipt_details" value="{{ text_receipt_details }}" placeholder="{{ text_text_receipt_details }}" id="input-text_receipt_details" class="form-control" />
              {% if error_text_receipt_details %}
                <div class="invalid-feedback">{{ error_text_receipt_details }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_receipt_info">{{ text_text_receipt_info }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_receipt_info" value="{{ text_receipt_info }}" placeholder="{{ text_text_receipt_info }}" id="input-text_receipt_info" class="form-control" />
              {% if error_text_receipt_info %}
                <div class="invalid-feedback">{{ error_text_receipt_info }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_receipt_number">{{ text_text_receipt_number }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_receipt_number" value="{{ text_receipt_number }}" placeholder="{{ text_text_receipt_number }}" id="input-text_receipt_number" class="form-control" />
              {% if error_text_receipt_number %}
                <div class="invalid-feedback">{{ error_text_receipt_number }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_receipt_total">{{ text_text_receipt_total }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_receipt_total" value="{{ text_receipt_total }}" placeholder="{{ text_text_receipt_total }}" id="input-text_receipt_total" class="form-control" />
              {% if error_text_receipt_total %}
                <div class="invalid-feedback">{{ error_text_receipt_total }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_received_receipts">{{ text_text_received_receipts }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_received_receipts" value="{{ text_received_receipts }}" placeholder="{{ text_text_received_receipts }}" id="input-text_received_receipts" class="form-control" />
              {% if error_text_received_receipts %}
                <div class="invalid-feedback">{{ error_text_received_receipts }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_rejected">{{ text_text_rejected }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_rejected" value="{{ text_rejected }}" placeholder="{{ text_text_rejected }}" id="input-text_rejected" class="form-control" />
              {% if error_text_rejected %}
                <div class="invalid-feedback">{{ error_text_rejected }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_status">{{ text_text_status }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_status" value="{{ text_status }}" placeholder="{{ text_text_status }}" id="input-text_status" class="form-control" />
              {% if error_text_status %}
                <div class="invalid-feedback">{{ error_text_status }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_supplier">{{ text_text_supplier }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_supplier" value="{{ text_supplier }}" placeholder="{{ text_text_supplier }}" id="input-text_supplier" class="form-control" />
              {% if error_text_supplier %}
                <div class="invalid-feedback">{{ error_text_supplier }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="invalid-feedback">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}