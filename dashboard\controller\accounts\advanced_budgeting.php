<?php
/**
 * تحكم الموازنات المتقدمة
 * نظام موازنات متطور يدعم الموافقات متعددة المستويات وتحليل الانحرافات
 * مع تكامل كامل مع محرر سير العمل المرئي
 */
class ControllerAccountsAdvancedBudgeting extends Controller {
    private $error = array();
    private $central_service;

    public function __construct($registry) {
        parent::__construct($registry);

        // تحميل الخدمات المركزية
        $this->load->model('core/central_service_manager');
        $this->central_service = $this->model_core_central_service_manager;
    }

    public function index() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/advanced_budgeting') ||
            !$this->user->hasKey('accounting_advanced_budgeting_view')) {

            $this->central_service->logActivity('unauthorized_access', 'accounts',
                $this->language->get('log_unauthorized_access_budgeting'), [
                'user_id' => $this->user->getId(),
                'ip_address' => $this->request->server['REMOTE_ADDR']
            ]);

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/advanced_budgeting');
        $this->document->setTitle($this->language->get('heading_title'));
        $this->load->model('accounts/advanced_budgeting');

        // إضافة CSS و JavaScript المتقدم
        $this->document->addStyle('view/stylesheet/accounts/advanced_budgeting.css');
        $this->document->addScript('view/javascript/accounts/advanced_budgeting.js');
        $this->document->addScript('view/javascript/jquery/accounting.min.js');
        $this->document->addScript('view/javascript/jquery/select2.min.js');
        $this->document->addStyle('view/javascript/jquery/select2.min.css');
        $this->document->addScript('view/javascript/jquery/daterangepicker.min.js');
        $this->document->addStyle('view/javascript/jquery/daterangepicker.css');
        $this->document->addScript('view/javascript/jquery/chart.min.js');
        $this->document->addScript('view/javascript/jquery/datatables.min.js');
        $this->document->addStyle('view/javascript/jquery/datatables.min.css');

        // تسجيل الوصول للشاشة
        $this->central_service->logActivity('view', 'accounts',
            $this->language->get('log_view_budgeting_screen'), [
            'user_id' => $this->user->getId(),
            'screen' => 'advanced_budgeting'
        ]);

        // معالجة الطلبات
        if ($this->request->server['REQUEST_METHOD'] == 'POST') {
            if (isset($this->request->post['action'])) {
                switch ($this->request->post['action']) {
                    case 'create_budget':
                        $this->createBudget();
                        break;
                    case 'submit_budget':
                        $this->submitBudget();
                        break;
                    case 'approve_budget':
                        $this->approveBudget();
                        break;
                    case 'copy_budget':
                        $this->copyBudget();
                        break;
                }
            }
        }

        $data = $this->getCommonData();

        // جلب الموازنات الحالية
        $data['budgets'] = $this->model_accounts_advanced_budgeting->getBudgets();
        
        // جلب الموازنات المعلقة للموافقة
        $data['pending_budgets'] = $this->model_accounts_advanced_budgeting->getPendingBudgets();

        // جلب إحصائيات الموازنات
        $data['budget_statistics'] = $this->model_accounts_advanced_budgeting->getBudgetStatistics();

        // جلب تحليل الانحرافات
        $data['variance_analysis'] = $this->model_accounts_advanced_budgeting->getVarianceAnalysis();

        // جلب الأقسام ومراكز التكلفة
        $data['departments'] = $this->model_accounts_advanced_budgeting->getDepartments();
        $data['cost_centers'] = $this->model_accounts_advanced_budgeting->getCostCenters();

        // روابط Ajax
        $data['ajax_create_url'] = $this->url->link('accounts/advanced_budgeting/create', 'user_token=' . $this->session->data['user_token'], true);
        $data['ajax_submit_url'] = $this->url->link('accounts/advanced_budgeting/submit', 'user_token=' . $this->session->data['user_token'], true);
        $data['ajax_approve_url'] = $this->url->link('accounts/advanced_budgeting/approve', 'user_token=' . $this->session->data['user_token'], true);
        $data['ajax_variance_url'] = $this->url->link('accounts/advanced_budgeting/variance', 'user_token=' . $this->session->data['user_token'], true);

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/advanced_budgeting', $data));
    }

    /**
     * إنشاء موازنة جديدة
     */
    public function create() {
        // فحص الصلاحيات المتقدمة
        if (!$this->user->hasKey('accounting_advanced_budgeting_create')) {
            $json['error'] = $this->language->get('error_permission_create');
            $this->response->addHeader('Content-Type: application/json');
            $this->response->setOutput(json_encode($json));
            return;
        }

        $this->load->model('accounts/advanced_budgeting');
        $json = array();

        try {
            $budget_data = array(
                'budget_name' => $this->request->post['budget_name'],
                'budget_type' => $this->request->post['budget_type'],
                'fiscal_year' => $this->request->post['fiscal_year'],
                'period_start' => $this->request->post['period_start'],
                'period_end' => $this->request->post['period_end'],
                'currency' => $this->request->post['currency'],
                'budget_lines' => $this->request->post['budget_lines'],
                'approval_workflow_id' => $this->request->post['approval_workflow_id']
            );

            $budget_id = $this->model_accounts_advanced_budgeting->createBudget($budget_data);

            if ($budget_id) {
                $json['success'] = $this->language->get('text_budget_created');
                $json['budget_id'] = $budget_id;
                
                // تسجيل العملية
                $this->central_service->logActivity('create', 'accounts',
                    $this->language->get('log_budget_created'), [
                    'user_id' => $this->user->getId(),
                    'budget_id' => $budget_id,
                    'budget_name' => $budget_data['budget_name']
                ]);

                // إرسال إشعار
                $this->central_service->sendNotification([
                    'type' => 'budget_created',
                    'title' => $this->language->get('notification_budget_title'),
                    'message' => sprintf($this->language->get('notification_budget_message'), $budget_data['budget_name']),
                    'user_id' => $this->user->getId(),
                    'url' => $this->url->link('accounts/advanced_budgeting/view', 'budget_id=' . $budget_id)
                ]);

            } else {
                $json['error'] = $this->language->get('error_budget_failed');
            }

        } catch (Exception $e) {
            $json['error'] = $e->getMessage();
            
            // تسجيل الخطأ
            $this->central_service->logActivity('error', 'accounts',
                'Budget creation failed: ' . $e->getMessage(), [
                'user_id' => $this->user->getId(),
                'error_details' => $e->getTraceAsString()
            ]);
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * تقديم الموازنة للموافقة
     */
    public function submit() {
        if (!$this->user->hasKey('accounting_advanced_budgeting_create')) {
            $json['error'] = $this->language->get('error_permission_submit');
            $this->response->addHeader('Content-Type: application/json');
            $this->response->setOutput(json_encode($json));
            return;
        }

        $this->load->model('accounts/advanced_budgeting');
        $json = array();

        try {
            $budget_id = $this->request->post['budget_id'];
            $submission_notes = $this->request->post['submission_notes'];

            $result = $this->model_accounts_advanced_budgeting->submitBudget($budget_id, $submission_notes);

            if ($result) {
                $json['success'] = $this->language->get('text_budget_submitted');
                
                // تسجيل العملية
                $this->central_service->logActivity('submit', 'accounts',
                    $this->language->get('log_budget_submitted'), [
                    'user_id' => $this->user->getId(),
                    'budget_id' => $budget_id
                ]);

                // بدء سير العمل للموافقة
                $this->startApprovalWorkflow($budget_id);

            } else {
                $json['error'] = $this->language->get('error_submission_failed');
            }

        } catch (Exception $e) {
            $json['error'] = $e->getMessage();
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * اعتماد الموازنة
     */
    public function approve() {
        if (!$this->user->hasKey('accounting_advanced_budgeting_approve')) {
            $json['error'] = $this->language->get('error_permission_approve');
            $this->response->addHeader('Content-Type: application/json');
            $this->response->setOutput(json_encode($json));
            return;
        }

        $this->load->model('accounts/advanced_budgeting');
        $json = array();

        try {
            $budget_id = $this->request->post['budget_id'];
            $approval_notes = $this->request->post['approval_notes'];
            $approval_action = $this->request->post['approval_action']; // approve, reject, request_changes

            $result = $this->model_accounts_advanced_budgeting->approveBudget($budget_id, $approval_action, $approval_notes);

            if ($result) {
                $json['success'] = $this->language->get('text_budget_' . $approval_action);
                
                // تسجيل العملية
                $this->central_service->logActivity('approve', 'accounts',
                    $this->language->get('log_budget_' . $approval_action), [
                    'user_id' => $this->user->getId(),
                    'budget_id' => $budget_id,
                    'action' => $approval_action
                ]);

            } else {
                $json['error'] = $this->language->get('error_approval_failed');
            }

        } catch (Exception $e) {
            $json['error'] = $e->getMessage();
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * نسخ موازنة
     */
    public function copy() {
        if (!$this->user->hasKey('accounting_advanced_budgeting_create')) {
            $json['error'] = $this->language->get('error_permission_copy');
            $this->response->addHeader('Content-Type: application/json');
            $this->response->setOutput(json_encode($json));
            return;
        }

        $this->load->model('accounts/advanced_budgeting');
        $json = array();

        try {
            $source_budget_id = $this->request->post['source_budget_id'];
            $new_budget_data = array(
                'budget_name' => $this->request->post['new_budget_name'],
                'fiscal_year' => $this->request->post['new_fiscal_year'],
                'period_start' => $this->request->post['new_period_start'],
                'period_end' => $this->request->post['new_period_end'],
                'adjustment_percentage' => $this->request->post['adjustment_percentage']
            );

            $new_budget_id = $this->model_accounts_advanced_budgeting->copyBudget($source_budget_id, $new_budget_data);

            if ($new_budget_id) {
                $json['success'] = $this->language->get('text_budget_copied');
                $json['budget_id'] = $new_budget_id;
                
                // تسجيل العملية
                $this->central_service->logActivity('copy', 'accounts',
                    $this->language->get('log_budget_copied'), [
                    'user_id' => $this->user->getId(),
                    'source_budget_id' => $source_budget_id,
                    'new_budget_id' => $new_budget_id
                ]);

            } else {
                $json['error'] = $this->language->get('error_copy_failed');
            }

        } catch (Exception $e) {
            $json['error'] = $e->getMessage();
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * تحليل الانحرافات
     */
    public function variance() {
        $this->load->model('accounts/advanced_budgeting');
        $json = array();

        try {
            $budget_id = $this->request->get['budget_id'];
            $period = $this->request->get['period'];

            $variance_data = $this->model_accounts_advanced_budgeting->getVarianceAnalysisDetailed($budget_id, $period);

            $json['success'] = true;
            $json['variance_data'] = $variance_data;

        } catch (Exception $e) {
            $json['error'] = $e->getMessage();
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * عرض تفاصيل الموازنة
     */
    public function view() {
        if (!$this->user->hasPermission('access', 'accounts/advanced_budgeting') ||
            !$this->user->hasKey('accounting_advanced_budgeting_view')) {
            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $budget_id = isset($this->request->get['budget_id']) ? (int)$this->request->get['budget_id'] : 0;

        if (!$budget_id) {
            $this->response->redirect($this->url->link('accounts/advanced_budgeting'));
            return;
        }

        $this->load->language('accounts/advanced_budgeting');
        $this->load->model('accounts/advanced_budgeting');

        $data = $this->getCommonData();
        
        // جلب بيانات الموازنة
        $budget = $this->model_accounts_advanced_budgeting->getBudget($budget_id);
        
        if (!$budget) {
            $this->response->redirect($this->url->link('accounts/advanced_budgeting'));
            return;
        }

        $data['budget'] = $budget;
        $data['budget_lines'] = $this->model_accounts_advanced_budgeting->getBudgetLines($budget_id);
        $data['approval_history'] = $this->model_accounts_advanced_budgeting->getApprovalHistory($budget_id);
        $data['variance_analysis'] = $this->model_accounts_advanced_budgeting->getVarianceAnalysisDetailed($budget_id);

        $this->document->setTitle($this->language->get('heading_title_view') . ' - ' . $budget['budget_name']);

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/advanced_budgeting_view', $data));
    }

    /**
     * بدء سير العمل للموافقة
     */
    private function startApprovalWorkflow($budget_id) {
        $this->load->model('workflow/visual_workflow_engine');
        
        $budget = $this->model_accounts_advanced_budgeting->getBudget($budget_id);
        
        if ($budget && $budget['approval_workflow_id']) {
            $workflow_data = array(
                'workflow_id' => $budget['approval_workflow_id'],
                'entity_type' => 'budget',
                'entity_id' => $budget_id,
                'initiated_by' => $this->user->getId(),
                'data' => array(
                    'budget_name' => $budget['budget_name'],
                    'budget_amount' => $budget['total_amount'],
                    'fiscal_year' => $budget['fiscal_year']
                )
            );

            $this->model_workflow_visual_workflow_engine->startWorkflow($workflow_data);
        }
    }

    /**
     * جلب البيانات المشتركة
     */
    private function getCommonData() {
        $data = array();

        // النصوص
        $data['heading_title'] = $this->language->get('heading_title');
        $data['text_list'] = $this->language->get('text_list');
        $data['text_no_results'] = $this->language->get('text_no_results');
        $data['text_confirm'] = $this->language->get('text_confirm');

        // الأزرار
        $data['button_create'] = $this->language->get('button_create');
        $data['button_submit'] = $this->language->get('button_submit');
        $data['button_approve'] = $this->language->get('button_approve');
        $data['button_copy'] = $this->language->get('button_copy');
        $data['button_view'] = $this->language->get('button_view');
        $data['button_cancel'] = $this->language->get('button_cancel');

        // الروابط
        $data['cancel'] = $this->url->link('accounts/advanced_budgeting', 'user_token=' . $this->session->data['user_token'], true);
        $data['user_token'] = $this->session->data['user_token'];

        return $data;
    }
}
