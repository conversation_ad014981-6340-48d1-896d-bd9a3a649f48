{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="submit" form="form-cash" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fa fa-save"></i></button>
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a></div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pencil"></i> {{ text_form }}</h3>
      </div>
      <div class="panel-body">
        <form action="{{ action }}" method="post" enctype="multipart/form-data" id="form-cash" class="form-horizontal">
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-name">{{ entry_name }}</label>
            <div class="col-sm-10">
              <input type="text" name="name" value="{{ name }}" placeholder="{{ entry_name }}" id="input-name" class="form-control" />
              {% if error_name %}
              <div class="text-danger">{{ error_name }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-code">{{ entry_code }}</label>
            <div class="col-sm-10">
              <input type="text" name="code" value="{{ code }}" placeholder="{{ entry_code }}" id="input-code" class="form-control" />
              {% if error_code %}
              <div class="text-danger">{{ error_code }}</div>
              {% endif %}
            </div>
          </div>
<div class="form-group">
  <label class="col-sm-2 control-label" for="input-initial-balance">{{ entry_initial_balance }}</label>
  <div class="col-sm-10">
    {% if not cash_id %}
      <input type="text" name="initial_balance" value="{{ initial_balance }}" placeholder="{{ entry_initial_balance }}" id="input-initial-balance" class="form-control" />
    {% else %}
      <input type="text" value="{{ initial_balance }}" placeholder="{{ entry_initial_balance }}" id="input-initial-balance" class="form-control" readonly />
    {% endif %}
  </div>
</div>
{% if cash_id %}
<div class="form-group">
  <label class="col-sm-2 control-label" for="input-current-balance">{{ entry_current_balance }}</label>
  <div class="col-sm-10">
    <input type="text" value="{{ current_balance }}" id="input-current-balance" class="form-control" readonly />
  </div>
</div>
{% endif %}

<div class="form-group">
  <label class="col-sm-2 control-label" for="input-responsible-user">{{ entry_responsible_person }}</label>
  <div class="col-sm-10">
    <select name="responsible_user_id" id="input-responsible-user" class="form-control">
      <option value="0">{{ text_select }}</option>
      {% for user in users %}
        {% if user.user_id == responsible_user_id %}
          <option value="{{ user.user_id }}" selected="selected">{{ user.username }}</option>
        {% else %}
          <option value="{{ user.user_id }}">{{ user.username }}</option>
        {% endif %}
      {% endfor %}
    </select>
  </div>
</div>


          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-status">{{ entry_status }}</label>
            <div class="col-sm-10">
              <select name="status" id="input-status" class="form-control">
                {% if status %}
                <option value="1" selected="selected">{{ text_enabled }}</option>
                <option value="0">{{ text_disabled }}</option>
                {% else %}
                <option value="1">{{ text_enabled }}</option>
                <option value="0" selected="selected">{{ text_disabled }}</option>
                {% endif %}
              </select>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
{{ footer }}