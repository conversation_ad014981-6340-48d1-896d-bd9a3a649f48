{{ header }}
{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  
  <div class="container-fluid">
    <!-- Filters -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-filter"></i> {{ text_filter }}</h3>
      </div>
      <div class="panel-body">
        <form id="filter-form" class="form-inline">
          <div class="form-group">
            <label for="filter_name">{{ text_deal_name }}</label>
            <input type="text" name="filter_name" id="filter_name" class="form-control" placeholder="{{ text_enter_deal_name }}" style="width:200px;" />
          </div>
          <div class="form-group">
            <label for="filter_date_start">{{ text_date_start }}</label>
            <div class='input-group date' id='filter_date_start'>
              <input type='text' name="filter_date_start" class="form-control" placeholder="YYYY-MM-DD" />
              <span class="input-group-addon"><span class="fa fa-calendar"></span></span>
            </div>
          </div>
          <div class="form-group">
            <label for="filter_date_end">{{ text_date_end }}</label>
            <div class='input-group date' id='filter_date_end'>
              <input type='text' name="filter_date_end" class="form-control" placeholder="YYYY-MM-DD" />
              <span class="input-group-addon"><span class="fa fa-calendar"></span></span>
            </div>
          </div>
          <button type="button" id="btn-filter" class="btn btn-primary"><i class="fa fa-search"></i> {{ button_filter }}</button>
          <button type="button" id="btn-reset" class="btn btn-default"><i class="fa fa-eraser"></i> {{ button_reset }}</button>
        </form>
      </div>
    </div>
    
    <div class="text-right">
      <button type="button" class="btn btn-success" id="btn-add"><i class="fa fa-plus"></i> {{ button_add_deal }}</button>
    </div>
    
    <div class="panel panel-default" style="margin-top:10px;">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-handshake-o"></i> {{ text_deal_list }}</h3>
      </div>
      <div class="panel-body">
        <table id="deal-table" class="table table-bordered table-striped">
          <thead>
            <tr>
              <th>{{ column_name }}</th>
              <th>{{ column_stage }}</th>
              <th>{{ column_amount }}</th>
              <th>{{ column_probability }}</th>
              <th>{{ column_status }}</th>
              <th>{{ column_actions }}</th>
            </tr>
          </thead>
          <tbody>
            <!-- DataTables AJAX -->
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>

<!-- Modal Add/Edit Deal -->
<div class="modal fade" id="modal-deal" tabindex="-1" role="dialog" aria-labelledby="modalDealLabel">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <form id="deal-form">
        <div class="modal-header">
          <button type="button" class="close" data-dismiss="modal"><span>&times;</span></button>
          <h4 class="modal-title" id="modalDealLabel">{{ text_add_deal }}</h4>
        </div>
        <div class="modal-body">
          <input type="hidden" name="deal_id" value="" />
          <div class="form-group">
            <label>{{ text_name }}</label>
            <input type="text" name="name" class="form-control" required />
          </div>
          <div class="form-group">
            <label>{{ text_stage }}</label>
            <select name="stage" class="form-control">
              <option value="qualification">{{ text_stage_qualification }}</option>
              <option value="proposal">{{ text_stage_proposal }}</option>
              <option value="negotiation">{{ text_stage_negotiation }}</option>
              <option value="closed_won">{{ text_stage_closed_won }}</option>
              <option value="closed_lost">{{ text_stage_closed_lost }}</option>
            </select>
          </div>
          <div class="form-group">
            <label>{{ text_probability }}</label>
            <input type="number" step="0.01" name="probability" class="form-control" min="0" max="100" />
          </div>
          <div class="form-group">
            <label>{{ text_amount }}</label>
            <input type="number" name="amount" class="form-control" step="0.0001" />
          </div>
          <div class="form-group">
            <label>{{ text_expected_close_date }}</label>
            <div class='input-group date' id='close_date_picker'>
              <input type='text' name="expected_close_date" class="form-control" />
              <span class="input-group-addon"><span class="fa fa-calendar"></span></span>
            </div>
          </div>
          <div class="form-group">
            <label>{{ text_assigned_to }}</label>
            <select name="assigned_to_user_id" class="form-control select2" style="width:100%;">
              <option value="">{{ text_select_user }}</option>
              {% for user in users %}
              <option value="{{ user.user_id }}">{{ user.firstname }} {{ user.lastname }}</option>
              {% endfor %}
            </select>
          </div>
          <div class="form-group">
            <label>{{ text_status }}</label>
            <select name="status" class="form-control">
              <option value="open">{{ text_status_open }}</option>
              <option value="closed">{{ text_status_closed }}</option>
              <option value="on_hold">{{ text_status_on_hold }}</option>
            </select>
          </div>
          <div class="form-group">
            <label>{{ text_notes }}</label>
            <textarea name="notes" class="form-control" rows="3"></textarea>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_close }}</button>
          <button type="submit" class="btn btn-primary">{{ button_save }}</button>
        </div>
      </form>
    </div>
  </div>
</div>

{{ footer }}

<script type="text/javascript">
$(document).ready(function() {
    $('#filter_date_start').datetimepicker({ format: 'YYYY-MM-DD' });
    $('#filter_date_end').datetimepicker({ format: 'YYYY-MM-DD', useCurrent:false });

    $("#filter_date_start").on("dp.change", function (e) {
        $('#filter_date_end').data("DateTimePicker").minDate(e.date);
    });
    $("#filter_date_end").on("dp.change", function (e) {
        $('#filter_date_start').data("DateTimePicker").maxDate(e.date);
    });

    $('#close_date_picker').datetimepicker({ format: 'YYYY-MM-DD' });
    $('select[name="assigned_to_user_id"]').select2({ placeholder: "{{ text_select_user }}" });

    var table = $('#deal-table').DataTable({
        "processing": true,
        "serverSide": true,
        "ajax": {
            "url": "{{ ajax_list_url }}",
            "type": "POST",
            "data": function (d) {
                d.user_token = "{{ user_token }}";
                d.filter_name = $('#filter_name').val();
                d.filter_date_start = $('input[name="filter_date_start"]').val();
                d.filter_date_end = $('input[name="filter_date_end"]').val();
            }
        },
        "columns": [
            { "data": "name" },
            { "data": "stage" },
            { "data": "amount" },
            { "data": "probability" },
            { "data": "status" },
            { "data": "actions", "orderable": false, "searchable": false }
        ]
    });

    $('#btn-filter').on('click', function() {
        table.ajax.reload();
    });
    $('#btn-reset').on('click', function() {
        $('#filter_name').val('');
        $('input[name="filter_date_start"]').val('');
        $('input[name="filter_date_end"]').val('');
        table.ajax.reload();
    });

    $('#btn-add').on('click', function() {
        $('#deal-form')[0].reset();
        $('input[name="deal_id"]').val('');
        $('select[name="stage"]').val('qualification');
        $('select[name="status"]').val('open');
        $('select[name="assigned_to_user_id"]').val('').trigger('change');
        $('#modalDealLabel').text("{{ text_add_deal }}");
        $('#modal-deal').modal('show');
    });

    $('#deal-form').on('submit', function(e) {
        e.preventDefault();
        $.ajax({
            url: "{{ ajax_save_url }}",
            type: "POST",
            data: $(this).serialize()+'&user_token={{ user_token }}',
            dataType: "json",
            success: function(json) {
                if (json.error) {
                    toastr.error(json.error);
                } else if (json.success) {
                    toastr.success(json.success);
                    $('#modal-deal').modal('hide');
                    table.ajax.reload();
                }
            },
            error: function() {
                toastr.error("{{ text_ajax_error }}");
            }
        });
    });

    $(document).on('click', '.btn-edit', function() {
        var id = $(this).data('id');
        $.ajax({
            url: "{{ ajax_get_url }}",
            type: "POST",
            data: {deal_id:id,user_token:"{{ user_token }}"},
            dataType: "json",
            success: function(json) {
                if (json.error) {
                    toastr.error(json.error);
                } else {
                    var data = json.data;
                    $('input[name="deal_id"]').val(data.deal_id);
                    $('input[name="name"]').val(data.name);
                    $('select[name="stage"]').val(data.stage);
                    $('input[name="probability"]').val(data.probability);
                    $('input[name="amount"]').val(data.amount);
                    $('input[name="expected_close_date"]').val(data.expected_close_date);
                    $('select[name="assigned_to_user_id"]').val(data.assigned_to_user_id).trigger('change');
                    $('select[name="status"]').val(data.status);
                    $('textarea[name="notes"]').val(data.notes);
                    $('#modalDealLabel').text("{{ text_edit_deal }}");
                    $('#modal-deal').modal('show');
                }
            },
            error: function() {
                toastr.error("{{ text_ajax_error }}");
            }
        });
    });

    $(document).on('click', '.btn-delete', function() {
        var id = $(this).data('id');
        if (confirm("{{ text_confirm_delete }}")) {
            $.ajax({
                url: "{{ ajax_delete_url }}",
                type: "POST",
                data: {deal_id:id,user_token:"{{ user_token }}"},
                dataType:"json",
                success: function(json) {
                    if (json.error) {
                        toastr.error(json.error);
                    } else {
                        toastr.success(json.success);
                        table.ajax.reload();
                    }
                },
                error: function() {
                    toastr.error("{{ text_ajax_error }}");
                }
            });
        }
    });

});
</script>
