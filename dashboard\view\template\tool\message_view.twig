{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <a href="{{ back }}" data-toggle="tooltip" title="{{ button_back }}" class="btn btn-default"><i class="fa fa-reply"></i></a>
        {% if not message.is_sent %}
        <button type="button" data-toggle="tooltip" title="{{ button_reply }}" class="btn btn-primary" onclick="$('#modal-reply').modal('show');"><i class="fa fa-reply"></i></button>
        <a href="{{ archive }}" data-toggle="tooltip" title="{{ button_archive }}" class="btn btn-warning"><i class="fa fa-archive"></i></a>
        {% endif %}
        <a href="{{ delete }}" data-toggle="tooltip" title="{{ button_delete }}" class="btn btn-danger" onclick="return confirm('{{ text_confirm }}');"><i class="fa fa-trash-o"></i></a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    {% if success %}
    <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-envelope"></i> {{ message.subject }}</h3>
      </div>
      <div class="panel-body">
        <div class="message-header">
          <div class="row">
            <div class="col-sm-6">
              <div class="form-group">
                <label>{{ text_from }}</label>
                <div>{{ message.from }}</div>
              </div>
              <div class="form-group">
                <label>{{ text_to }}</label>
                <div>{{ message.to }}</div>
              </div>
            </div>
            <div class="col-sm-6">
              <div class="form-group">
                <label>{{ text_date_added }}</label>
                <div>{{ message.date_added }}</div>
              </div>
              {% if message.attachments %}
              <div class="form-group">
                <label>{{ text_attachments }}</label>
                <div>
                  {% for attachment in message.attachments %}
                  <div><a href="{{ attachment.href }}" target="_blank"><i class="fa fa-paperclip"></i> {{ attachment.name }}</a> ({{ attachment.size }})</div>
                  {% endfor %}
                </div>
              </div>
              {% endif %}
            </div>
          </div>
        </div>
        <hr>
        <div class="message-content">
          {{ message.message }}
        </div>
        
        {% if message.history %}
        <hr>
        <div class="message-history">
          <h4>{{ text_message_history }}</h4>
          
          {% for history in message.history %}
          <div class="panel panel-default">
            <div class="panel-heading">
              <div class="row">
                <div class="col-sm-6">
                  <strong>{{ history.from }}</strong> {{ text_to }} {{ history.to }}
                </div>
                <div class="col-sm-6 text-right">
                  {{ history.date_added }}
                </div>
              </div>
            </div>
            <div class="panel-body">
              {{ history.message }}
              
              {% if history.attachments %}
              <hr>
              <div class="attachments">
                <label>{{ text_attachments }}</label>
                <div>
                  {% for attachment in history.attachments %}
                  <div><a href="{{ attachment.href }}" target="_blank"><i class="fa fa-paperclip"></i> {{ attachment.name }}</a> ({{ attachment.size }})</div>
                  {% endfor %}
                </div>
              </div>
              {% endif %}
            </div>
          </div>
          {% endfor %}
        </div>
        {% endif %}
      </div>
    </div>
  </div>
  
  <!-- Reply Modal -->
  <div class="modal fade" id="modal-reply" tabindex="-1" role="dialog" aria-labelledby="modal-reply-label" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
          <h4 class="modal-title" id="modal-reply-label">{{ text_reply }}: {{ message.subject }}</h4>
        </div>
        <div class="modal-body">
          <form id="form-reply" class="form-horizontal">
            <input type="hidden" name="parent_id" value="{{ message.message_id }}" />
            <input type="hidden" name="to" value="{{ message.reply_to }}" />
            <input type="hidden" name="subject" value="Re: {{ message.subject }}" />
            
            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-reply-message">{{ entry_message }}</label>
              <div class="col-sm-10">
                <textarea name="message" placeholder="{{ entry_message }}" id="input-reply-message" data-toggle="summernote" class="form-control"></textarea>
                <div id="reply-message-error" class="text-danger"></div>
              </div>
            </div>
            <div class="form-group">
              <label class="col-sm-2 control-label" for="input-attachment">{{ entry_attachment }}</label>
              <div class="col-sm-10">
                <button type="button" id="button-reply-attachment" data-loading-text="{{ text_loading }}" class="btn btn-default"><i class="fa fa-upload"></i> {{ button_upload }}</button>
                <div id="reply-attachments"></div>
              </div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_cancel }}</button>
          <button type="button" id="button-send-reply" class="btn btn-primary">{{ button_send }}</button>
        </div>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript"><!--
$('#input-reply-message').summernote({
  height: 300
});

$('#button-reply-attachment').on('click', function() {
  $('#form-upload').remove();
  
  $('body').prepend('<form enctype="multipart/form-data" id="form-upload" style="display: none;"><input type="file" name="file" /></form>');

  $('#form-upload input[name=\'file\']').trigger('click');
  
  if (typeof timer != 'undefined') {
    clearInterval(timer);
  }
  
  timer = setInterval(function() {
    if ($('#form-upload input[name=\'file\']').val() != '') {
      clearInterval(timer);
      
      $.ajax({
        url: 'index.php?route=tool/messaging/upload&user_token={{ user_token }}',
        type: 'post',
        dataType: 'json',
        data: new FormData($('#form-upload')[0]),
        cache: false,
        contentType: false,
        processData: false,
        beforeSend: function() {
          $('#button-reply-attachment').button('loading');
        },
        complete: function() {
          $('#button-reply-attachment').button('reset');
        },
        success: function(json) {
          if (json['error']) {
            alert(json['error']);
          }
          
          if (json['success']) {
            alert(json['success']);
            
            $('#reply-attachments').append('<div id="reply-attachment-' + json['attachment_id'] + '"><div class="pull-right"><button type="button" class="btn btn-danger btn-xs" onclick="$(\'#reply-attachment-' + json['attachment_id'] + '\').remove();"><i class="fa fa-times"></i></button></div><a href="' + json['href'] + '">' + json['name'] + '</a><input type="hidden" name="attachment[]" value="' + json['attachment_id'] + '" /></div>');
          }
        },
        error: function(xhr, ajaxOptions, thrownError) {
          alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
      });
    }
  }, 500);
});

$('#button-send-reply').on('click', function() {
  $.ajax({
    url: 'index.php?route=tool/messaging/reply&user_token={{ user_token }}',
    type: 'post',
    dataType: 'json',
    data: $('#form-reply').serialize(),
    beforeSend: function() {
      $('#button-send-reply').button('loading');
    },
    complete: function() {
      $('#button-send-reply').button('reset');
    },
    success: function(json) {
      $('.text-danger').remove();
      
      if (json['error']) {
        if (json['error']['message']) {
          $('#input-reply-message').after('<div class="text-danger">' + json['error']['message'] + '</div>');
        }
      }
      
      if (json['success']) {
        $('#modal-reply').modal('hide');
        
        alert(json['success']);
        
        location.reload();
      }
    },
    error: function(xhr, ajaxOptions, thrownError) {
      alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
    }
  });
});
//--></script>

{{ footer }} 