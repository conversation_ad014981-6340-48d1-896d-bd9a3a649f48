{{ header }}{{ column_left }}

<!-- Enterprise Grade Plus CSS for Advanced Bank Accounts -->
<style>
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --danger-color: #e74c3c;
    --warning-color: #f39c12;
    --info-color: #17a2b8;
    --bank-color: #16a085;
    --light-bg: #f8f9fa;
    --border-color: #dee2e6;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

.bank-container {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: var(--shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.bank-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--bank-color), var(--primary-color), var(--secondary-color));
}

.bank-header {
    text-align: center;
    border-bottom: 3px solid var(--bank-color);
    padding-bottom: 20px;
    margin-bottom: 30px;
    position: relative;
}

.bank-header h2 {
    color: var(--bank-color);
    font-weight: 700;
    font-size: 1.8rem;
    margin: 0;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.bank-summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.bank-card {
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    text-align: center;
    box-shadow: var(--shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.bank-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0,0,0,0.1);
}

.bank-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
}

.bank-card.total::before { background: var(--bank-color); }
.bank-card.active::before { background: var(--success-color); }
.bank-card.inactive::before { background: var(--warning-color); }
.bank-card.balance::before { background: var(--primary-color); }

.bank-card h4 {
    color: var(--primary-color);
    margin-bottom: 10px;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.bank-card .amount {
    font-size: 1.4rem;
    font-weight: 700;
    font-family: 'Courier New', monospace;
    margin-bottom: 5px;
}

.bank-card .count {
    font-size: 0.8rem;
    color: #6c757d;
    font-weight: 500;
}

.card-total .amount { color: var(--bank-color); }
.card-active .amount { color: var(--success-color); }
.card-inactive .amount { color: var(--warning-color); }
.card-balance .amount { color: var(--primary-color); }

.bank-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    background: #ffffff;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.bank-table th {
    background: linear-gradient(135deg, var(--bank-color), #138d75);
    color: white;
    padding: 15px 12px;
    text-align: center;
    font-weight: 600;
    font-size: 0.9rem;
    border-bottom: 2px solid var(--border-color);
}

.bank-table td {
    padding: 12px;
    border-bottom: 1px solid var(--border-color);
    text-align: center;
    transition: var(--transition);
}

.bank-table tbody tr:hover {
    background: var(--light-bg);
    transform: scale(1.005);
}

.bank-table tbody tr:nth-child(even) {
    background: #fafbfc;
}

.amount-cell {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    text-align: {{ direction == 'rtl' ? 'right' : 'left' }};
}

.status-active { 
    color: var(--success-color); 
    font-weight: 600;
}

.status-inactive { 
    color: var(--warning-color); 
    font-weight: 600;
}

.bank-actions {
    display: flex;
    gap: 5px;
    justify-content: center;
}

.bank-actions .btn {
    padding: 5px 10px;
    font-size: 0.8rem;
}

/* RTL Support */
[dir="rtl"] .bank-table {
    direction: rtl;
}

[dir="rtl"] .amount-cell {
    text-align: right;
}

/* Print Styles */
@media print {
    .bank-container {
        box-shadow: none;
        border: 1px solid #000;
    }
    
    .bank-table th {
        background: #f0f0f0 !important;
        color: #000 !important;
    }
    
    .btn, .card-header {
        display: none !important;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .bank-table {
        font-size: 0.8rem;
    }
    
    .bank-table th,
    .bank-table td {
        padding: 8px 6px;
    }
    
    .bank-summary-cards {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .bank-actions {
        flex-direction: column;
    }
}
</style>

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-{{ direction == 'rtl' ? 'start' : 'end' }}">
        <div class="btn-group" role="group" aria-label="{{ text_actions }}">
          <a href="{{ add }}" class="btn btn-success btn-lg" data-bs-toggle="tooltip" title="{{ button_add }}">
            <i class="fas fa-plus me-2"></i> {{ button_add }}
          </a>
          <div class="btn-group" role="group">
            <button type="button" class="btn btn-info dropdown-toggle" data-bs-toggle="dropdown" 
                    aria-expanded="false" data-bs-toggle="tooltip" title="{{ text_bank_operations }}">
              <i class="fas fa-university me-2"></i> {{ text_bank_operations }}
            </button>
            <ul class="dropdown-menu">
              <li><a class="dropdown-item" href="{{ reconcile }}">
                <i class="fas fa-balance-scale text-primary me-2"></i> {{ text_reconcile }}
              </a></li>
              <li><a class="dropdown-item" href="{{ transfer }}">
                <i class="fas fa-exchange-alt text-warning me-2"></i> {{ text_transfer }}
              </a></li>
              <li><a class="dropdown-item" href="{{ statement }}">
                <i class="fas fa-file-invoice text-info me-2"></i> {{ text_statement }}
              </a></li>
              <li><hr class="dropdown-divider"></li>
              <li><a class="dropdown-item" href="{{ import }}">
                <i class="fas fa-upload text-success me-2"></i> {{ text_import }}
              </a></li>
            </ul>
          </div>
          <button type="button" class="btn btn-outline-primary" onclick="refreshBalances()"
                  data-bs-toggle="tooltip" title="{{ text_refresh_balances }}">
            <i class="fas fa-sync-alt"></i>
          </button>
        </div>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li class="breadcrumb-item"><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>

  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible fade show">
      <i class="fas fa-exclamation-triangle"></i>
      {{ error_warning }}
      <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    {% endif %}

    {% if success %}
    <div class="alert alert-success alert-dismissible fade show">
      <i class="fas fa-check-circle"></i>
      {{ success }}
      <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    {% endif %}

    <!-- Bank Summary Cards -->
    <div class="bank-summary-cards">
      <div class="bank-card card-total total">
        <h4>{{ text_total_accounts }}</h4>
        <div class="amount">{{ summary.total_accounts }}</div>
        <div class="count">{{ text_accounts }}</div>
      </div>
      <div class="bank-card card-active active">
        <h4>{{ text_active_accounts }}</h4>
        <div class="amount">{{ summary.active_accounts }}</div>
        <div class="count">{{ text_active }}</div>
      </div>
      <div class="bank-card card-inactive inactive">
        <h4>{{ text_inactive_accounts }}</h4>
        <div class="amount">{{ summary.inactive_accounts }}</div>
        <div class="count">{{ text_inactive }}</div>
      </div>
      <div class="bank-card card-balance balance">
        <h4>{{ text_total_balance }}</h4>
        <div class="amount">{{ summary.total_balance_formatted }}</div>
        <div class="count">{{ text_all_currencies }}</div>
      </div>
    </div>

    <!-- Bank Accounts Table -->
    <div class="bank-container">
      <div class="bank-header">
        <h2>{{ text_bank_accounts_list }}</h2>
      </div>

      <div class="table-responsive">
        <table class="bank-table" id="bank-accounts-table">
          <thead>
            <tr>
              <th>{{ column_account_name }}</th>
              <th>{{ column_bank_name }}</th>
              <th>{{ column_account_number }}</th>
              <th>{{ column_iban }}</th>
              <th>{{ column_currency }}</th>
              <th>{{ column_balance }}</th>
              <th>{{ column_status }}</th>
              <th>{{ column_last_reconciled }}</th>
              <th>{{ column_action }}</th>
            </tr>
          </thead>
          <tbody>
            {% for bank_account in bank_accounts %}
            <tr data-account-id="{{ bank_account.bank_account_id }}">
              <td>
                <strong>{{ bank_account.account_name }}</strong>
                <br>
                <small class="text-muted">{{ bank_account.account_type }}</small>
              </td>
              <td>
                {{ bank_account.bank_name }}
                <br>
                <small class="text-muted">{{ bank_account.branch_name }}</small>
              </td>
              <td class="amount-cell">{{ bank_account.account_number }}</td>
              <td class="amount-cell">{{ bank_account.iban }}</td>
              <td>{{ bank_account.currency_code }}</td>
              <td class="amount-cell">
                <strong class="{% if bank_account.balance >= 0 %}text-success{% else %}text-danger{% endif %}">
                  {{ bank_account.balance_formatted }}
                </strong>
              </td>
              <td>
                <span class="status-{{ bank_account.status }}">
                  {{ bank_account.status_text }}
                </span>
              </td>
              <td>
                {% if bank_account.last_reconciled %}
                {{ bank_account.last_reconciled_formatted }}
                {% else %}
                <span class="text-muted">{{ text_never }}</span>
                {% endif %}
              </td>
              <td>
                <div class="bank-actions">
                  <a href="{{ bank_account.edit }}" class="btn btn-outline-primary btn-sm" 
                     data-bs-toggle="tooltip" title="{{ button_edit }}">
                    <i class="fas fa-edit"></i>
                  </a>
                  <a href="{{ bank_account.statement }}" class="btn btn-outline-info btn-sm"
                     data-bs-toggle="tooltip" title="{{ text_statement }}">
                    <i class="fas fa-file-invoice"></i>
                  </a>
                  <a href="{{ bank_account.reconcile }}" class="btn btn-outline-warning btn-sm"
                     data-bs-toggle="tooltip" title="{{ text_reconcile }}">
                    <i class="fas fa-balance-scale"></i>
                  </a>
                  {% if bank_account.status == 'active' %}
                  <button type="button" class="btn btn-outline-secondary btn-sm" 
                          onclick="toggleStatus({{ bank_account.bank_account_id }}, 'inactive')"
                          data-bs-toggle="tooltip" title="{{ text_deactivate }}">
                    <i class="fas fa-pause"></i>
                  </button>
                  {% else %}
                  <button type="button" class="btn btn-outline-success btn-sm" 
                          onclick="toggleStatus({{ bank_account.bank_account_id }}, 'active')"
                          data-bs-toggle="tooltip" title="{{ text_activate }}">
                    <i class="fas fa-play"></i>
                  </button>
                  {% endif %}
                </div>
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>

      <div class="row">
        <div class="col-sm-6 text-start">{{ pagination }}</div>
        <div class="col-sm-6 text-end">{{ results }}</div>
      </div>
    </div>
  </div>
</div>

<script>
// Enterprise Grade Plus JavaScript for Advanced Bank Accounts
class AdvancedBankAccountsManager {
    constructor() {
        this.initializeTooltips();
        this.initializeDataTable();
        this.initializeKeyboardShortcuts();
        this.initializeAutoRefresh();
    }

    initializeTooltips() {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }

    initializeDataTable() {
        const table = document.getElementById('bank-accounts-table');
        if (table && typeof $ !== 'undefined' && $.fn.DataTable) {
            $(table).DataTable({
                responsive: true,
                pageLength: 25,
                order: [[0, 'asc']], // Sort by account name
                columnDefs: [
                    { targets: [5], className: 'text-end' },
                    { targets: [8], orderable: false }
                ],
                language: {
                    url: '{{ direction == "rtl" ? "/view/javascript/jquery/datatables-ar.json" : "/view/javascript/jquery/datatables-en.json" }}'
                }
            });
        }
    }

    initializeKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case 'n':
                        e.preventDefault();
                        window.location.href = '{{ add }}';
                        break;
                    case 'r':
                        e.preventDefault();
                        this.refreshBalances();
                        break;
                    case 't':
                        e.preventDefault();
                        window.location.href = '{{ transfer }}';
                        break;
                }
            }
        });
    }

    initializeAutoRefresh() {
        // Auto-refresh balances every 5 minutes
        setInterval(() => {
            this.refreshBalances(true);
        }, 300000);
    }

    refreshBalances(silent = false) {
        if (!silent) {
            this.showAlert('{{ text_refreshing_balances }}...', 'info');
        }

        fetch('{{ refresh_url }}', {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                if (!silent) {
                    this.showAlert('{{ text_balances_refreshed }}', 'success');
                    setTimeout(() => location.reload(), 1000);
                }
                this.updateBalanceDisplay(data.balances);
            } else {
                if (!silent) {
                    this.showAlert(data.error || '{{ error_refresh_balances }}', 'danger');
                }
            }
        })
        .catch(error => {
            if (!silent) {
                this.showAlert('{{ error_refresh_balances }}: ' + error.message, 'danger');
            }
        });
    }

    toggleStatus(accountId, newStatus) {
        const confirmMessage = newStatus === 'active' ?
            '{{ text_confirm_activate }}' :
            '{{ text_confirm_deactivate }}';

        if (!confirm(confirmMessage)) {
            return;
        }

        fetch('{{ toggle_status_url }}', {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                account_id: accountId,
                status: newStatus
            })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                this.showAlert('{{ text_status_updated }}', 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                this.showAlert(data.error || '{{ error_update_status }}', 'danger');
            }
        })
        .catch(error => {
            this.showAlert('{{ error_update_status }}: ' + error.message, 'danger');
        });
    }

    updateBalanceDisplay(balances) {
        // Update balance display without full page reload
        balances.forEach(balance => {
            const row = document.querySelector(`[data-account-id="${balance.account_id}"]`);
            if (row) {
                const balanceCell = row.querySelector('.amount-cell strong');
                if (balanceCell) {
                    balanceCell.textContent = balance.formatted;
                    balanceCell.className = balance.amount >= 0 ? 'text-success' : 'text-danger';
                }
            }
        });
    }

    showAlert(message, type) {
        const alertContainer = document.createElement('div');
        alertContainer.className = `alert alert-${type} alert-dismissible fade show`;
        alertContainer.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'}"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        const container = document.querySelector('#content .container-fluid');
        container.insertBefore(alertContainer, container.firstChild);

        setTimeout(() => {
            alertContainer.remove();
        }, 5000);
    }
}

// Global functions for backward compatibility
function refreshBalances() {
    bankAccountsManager.refreshBalances();
}

function toggleStatus(accountId, newStatus) {
    bankAccountsManager.toggleStatus(accountId, newStatus);
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.bankAccountsManager = new AdvancedBankAccountsManager();
});
</script>

{{ footer }}
