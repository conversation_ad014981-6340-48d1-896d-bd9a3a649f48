<?php
// Heading
$_['heading_title'] = 'Internal Control';

// Text
$_['text_list'] = 'Internal Controls List';
$_['text_modal_add'] = 'Add New Officer';
$_['text_modal_edit'] = 'Edit Officer';
$_['text_confirm_delete'] = 'Are you sure you want to delete?';
$_['text_home'] = 'Home';
$_['text_all_statuses'] = '- All Statuses -';
$_['text_all_groups'] = '- All Departments -';
$_['text_no_group'] = 'No Group';
$_['text_active'] = 'Active';
$_['text_obsolete'] = 'Done';
$_['text_effective_date_start'] = 'Effective Date (From)';
$_['text_effective_date_end'] = 'Effective Date (To)';

// Entry
$_['entry_status'] = 'Status';
$_['entry_control_name'] = 'Controller Name';
$_['entry_description'] = 'Description';
$_['entry_responsible_group'] = 'Responsible Group';
$_['entry_effective_date'] = 'Effective Date';
$_['entry_review_date'] = 'Review Date';

// Columns
$_['column_control_id'] = 'Number';
$_['column_control_name'] = 'Controller Name';
$_['column_effective_date'] = 'Effective Date';
$_['column_status'] = 'Status';
$_['column_responsible'] = 'Administrator';
$_['column_action'] = 'Action';

// Buttons
$_['button_filter'] = 'Filter';
$_['button_add'] = 'Add';
$_['button_edit'] = 'Edit';
$_['button_delete'] = 'Delete';
$_['button_save'] = 'Save';
$_['button_close'] = 'Close';