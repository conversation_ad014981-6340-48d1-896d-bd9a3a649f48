############################################################
##
## PhpSpreadsheet - function name translations
##
## Svenska (Swedish)
##
############################################################


##
## Kubfunktioner (Cube Functions)
##
CUBEKPIMEMBER = KUBKPIMEDLEM
CUBEMEMBER = KUBMEDLEM
CUBEMEMBERPROPERTY = KU<PERSON>EDLEMSEGENSKAP
CUBERANKEDMEMBER = KUBRANGORDNADMEDLEM
CUBESET = KUBUPPSÄTTNING
CUBESETCOUNT = KUBUPPSÄTTNINGANTAL
CUBEVALUE = KUBVÄRDE

##
## Databasfunktioner (Database Functions)
##
DAVERAGE = DMEDEL
DCOUNT = DANTAL
DCOUNTA = DANTALV
DGET = DHÄMTA
DMAX = DMAX
DMIN = DMIN
DPRODUCT = DPRODUKT
DSTDEV = DSTDAV
DSTDEVP = DSTDAVP
DSUM = DSUMMA
DVAR = DVARIANS
DVARP = DVARIANSP

##
## Tid- och datumfunktioner (Date & Time Functions)
##
DATE = DATUM
DATEVALUE = DATUMVÄRDE
DAY = DAG
DAYS = DAGAR
DAYS360 = DAGAR360
EDATE = EDATUM
EOMONTH = SLUTMÅNAD
HOUR = TIMME
ISOWEEKNUM = ISOVECKONR
MINUTE = MINUT
MONTH = MÅNAD
NETWORKDAYS = NETTOARBETSDAGAR
NETWORKDAYS.INTL = NETTOARBETSDAGAR.INT
NOW = NU
SECOND = SEKUND
THAIDAYOFWEEK = THAIVECKODAG
THAIMONTHOFYEAR = THAIMÅNAD
THAIYEAR = THAIÅR
TIME = KLOCKSLAG
TIMEVALUE = TIDVÄRDE
TODAY = IDAG
WEEKDAY = VECKODAG
WEEKNUM = VECKONR
WORKDAY = ARBETSDAGAR
WORKDAY.INTL = ARBETSDAGAR.INT
YEAR = ÅR
YEARFRAC = ÅRDEL

##
## Tekniska funktioner (Engineering Functions)
##
BESSELI = BESSELI
BESSELJ = BESSELJ
BESSELK = BESSELK
BESSELY = BESSELY
BIN2DEC = BIN.TILL.DEC
BIN2HEX = BIN.TILL.HEX
BIN2OCT = BIN.TILL.OKT
BITAND = BITOCH
BITLSHIFT = BITVSKIFT
BITOR = BITELLER
BITRSHIFT = BITHSKIFT
BITXOR = BITXELLER
COMPLEX = KOMPLEX
CONVERT = KONVERTERA
DEC2BIN = DEC.TILL.BIN
DEC2HEX = DEC.TILL.HEX
DEC2OCT = DEC.TILL.OKT
DELTA = DELTA
ERF = FELF
ERF.PRECISE = FELF.EXAKT
ERFC = FELFK
ERFC.PRECISE = FELFK.EXAKT
GESTEP = SLSTEG
HEX2BIN = HEX.TILL.BIN
HEX2DEC = HEX.TILL.DEC
HEX2OCT = HEX.TILL.OKT
IMABS = IMABS
IMAGINARY = IMAGINÄR
IMARGUMENT = IMARGUMENT
IMCONJUGATE = IMKONJUGAT
IMCOS = IMCOS
IMCOSH = IMCOSH
IMCOT = IMCOT
IMCSC = IMCSC
IMCSCH = IMCSCH
IMDIV = IMDIV
IMEXP = IMEUPPHÖJT
IMLN = IMLN
IMLOG10 = IMLOG10
IMLOG2 = IMLOG2
IMPOWER = IMUPPHÖJT
IMPRODUCT = IMPRODUKT
IMREAL = IMREAL
IMSEC = IMSEK
IMSECH = IMSEKH
IMSIN = IMSIN
IMSINH = IMSINH
IMSQRT = IMROT
IMSUB = IMDIFF
IMSUM = IMSUM
IMTAN = IMTAN
OCT2BIN = OKT.TILL.BIN
OCT2DEC = OKT.TILL.DEC
OCT2HEX = OKT.TILL.HEX

##
## Finansiella funktioner (Financial Functions)
##
ACCRINT = UPPLRÄNTA
ACCRINTM = UPPLOBLRÄNTA
AMORDEGRC = AMORDEGRC
AMORLINC = AMORLINC
COUPDAYBS = KUPDAGBB
COUPDAYS = KUPDAGB
COUPDAYSNC = KUPDAGNK
COUPNCD = KUPNKD
COUPNUM = KUPANT
COUPPCD = KUPFKD
CUMIPMT = KUMRÄNTA
CUMPRINC = KUMPRIS
DB = DB
DDB = DEGAVSKR
DISC = DISK
DOLLARDE = DECTAL
DOLLARFR = BRÅK
DURATION = LÖPTID
EFFECT = EFFRÄNTA
FV = SLUTVÄRDE
FVSCHEDULE = FÖRRÄNTNING
INTRATE = ÅRSRÄNTA
IPMT = RBETALNING
IRR = IR
ISPMT = RALÅN
MDURATION = MLÖPTID
MIRR = MODIR
NOMINAL = NOMRÄNTA
NPER = PERIODER
NPV = NETNUVÄRDE
ODDFPRICE = UDDAFPRIS
ODDFYIELD = UDDAFAVKASTNING
ODDLPRICE = UDDASPRIS
ODDLYIELD = UDDASAVKASTNING
PDURATION = PLÖPTID
PMT = BETALNING
PPMT = AMORT
PRICE = PRIS
PRICEDISC = PRISDISK
PRICEMAT = PRISFÖRF
PV = NUVÄRDE
RATE = RÄNTA
RECEIVED = BELOPP
RRI = AVKPÅINVEST
SLN = LINAVSKR
SYD = ÅRSAVSKR
TBILLEQ = SSVXEKV
TBILLPRICE = SSVXPRIS
TBILLYIELD = SSVXRÄNTA
VDB = VDEGRAVSKR
XIRR = XIRR
XNPV = XNUVÄRDE
YIELD = NOMAVK
YIELDDISC = NOMAVKDISK
YIELDMAT = NOMAVKFÖRF

##
## Informationsfunktioner (Information Functions)
##
CELL = CELL
ERROR.TYPE = FEL.TYP
INFO = INFO
ISBLANK = ÄRTOM
ISERR = ÄRF
ISERROR = ÄRFEL
ISEVEN = ÄRJÄMN
ISFORMULA = ÄRFORMEL
ISLOGICAL = ÄRLOGISK
ISNA = ÄRSAKNAD
ISNONTEXT = ÄREJTEXT
ISNUMBER = ÄRTAL
ISODD = ÄRUDDA
ISREF = ÄRREF
ISTEXT = ÄRTEXT
N = N
NA = SAKNAS
SHEET = BLAD
SHEETS = ANTALBLAD
TYPE = VÄRDETYP

##
## Logiska funktioner (Logical Functions)
##
AND = OCH
FALSE = FALSKT
IF = OM
IFERROR = OMFEL
IFNA = OMSAKNAS
IFS = IFS
NOT = ICKE
OR = ELLER
SWITCH = VÄXLA
TRUE = SANT
XOR = XELLER

##
## Sök- och referensfunktioner (Lookup & Reference Functions)
##
ADDRESS = ADRESS
AREAS = OMRÅDEN
CHOOSE = VÄLJ
COLUMN = KOLUMN
COLUMNS = KOLUMNER
FORMULATEXT = FORMELTEXT
GETPIVOTDATA = HÄMTA.PIVOTDATA
HLOOKUP = LETAKOLUMN
HYPERLINK = HYPERLÄNK
INDEX = INDEX
INDIRECT = INDIREKT
LOOKUP = LETAUPP
MATCH = PASSA
OFFSET = FÖRSKJUTNING
ROW = RAD
ROWS = RADER
RTD = RTD
TRANSPOSE = TRANSPONERA
VLOOKUP = LETARAD
*RC = RK

##
## Matematiska och trigonometriska funktioner (Math & Trig Functions)
##
ABS = ABS
ACOS = ARCCOS
ACOSH = ARCCOSH
ACOT = ARCCOT
ACOTH = ARCCOTH
AGGREGATE = MÄNGD
ARABIC = ARABISKA
ASIN = ARCSIN
ASINH = ARCSINH
ATAN = ARCTAN
ATAN2 = ARCTAN2
ATANH = ARCTANH
BASE = BAS
CEILING.MATH = RUNDA.UPP.MATEMATISKT
CEILING.PRECISE = RUNDA.UPP.EXAKT
COMBIN = KOMBIN
COMBINA = KOMBINA
COS = COS
COSH = COSH
COT = COT
COTH = COTH
CSC = CSC
CSCH = CSCH
DECIMAL = DECIMAL
DEGREES = GRADER
ECMA.CEILING = ECMA.RUNDA.UPP
EVEN = JÄMN
EXP = EXP
FACT = FAKULTET
FACTDOUBLE = DUBBELFAKULTET
FLOOR.MATH = RUNDA.NER.MATEMATISKT
FLOOR.PRECISE = RUNDA.NER.EXAKT
GCD = SGD
INT = HELTAL
ISO.CEILING = ISO.RUNDA.UPP
LCM = MGM
LN = LN
LOG = LOG
LOG10 = LOG10
MDETERM = MDETERM
MINVERSE = MINVERT
MMULT = MMULT
MOD = REST
MROUND = MAVRUNDA
MULTINOMIAL = MULTINOMIAL
MUNIT = MENHET
ODD = UDDA
PI = PI
POWER = UPPHÖJT.TILL
PRODUCT = PRODUKT
QUOTIENT = KVOT
RADIANS = RADIANER
RAND = SLUMP
RANDBETWEEN = SLUMP.MELLAN
ROMAN = ROMERSK
ROUND = AVRUNDA
ROUNDBAHTDOWN = AVRUNDABAHTNEDÅT
ROUNDBAHTUP = AVRUNDABAHTUPPÅT
ROUNDDOWN = AVRUNDA.NEDÅT
ROUNDUP = AVRUNDA.UPPÅT
SEC = SEK
SECH = SEKH
SERIESSUM = SERIESUMMA
SIGN = TECKEN
SIN = SIN
SINH = SINH
SQRT = ROT
SQRTPI = ROTPI
SUBTOTAL = DELSUMMA
SUM = SUMMA
SUMIF = SUMMA.OM
SUMIFS = SUMMA.OMF
SUMPRODUCT = PRODUKTSUMMA
SUMSQ = KVADRATSUMMA
SUMX2MY2 = SUMMAX2MY2
SUMX2PY2 = SUMMAX2PY2
SUMXMY2 = SUMMAXMY2
TAN = TAN
TANH = TANH
TRUNC = AVKORTA

##
## Statistiska funktioner (Statistical Functions)
##
AVEDEV = MEDELAVV
AVERAGE = MEDEL
AVERAGEA = AVERAGEA
AVERAGEIF = MEDEL.OM
AVERAGEIFS = MEDEL.OMF
BETA.DIST = BETA.FÖRD
BETA.INV = BETA.INV
BINOM.DIST = BINOM.FÖRD
BINOM.DIST.RANGE = BINOM.FÖRD.INTERVALL
BINOM.INV = BINOM.INV
CHISQ.DIST = CHI2.FÖRD
CHISQ.DIST.RT = CHI2.FÖRD.RT
CHISQ.INV = CHI2.INV
CHISQ.INV.RT = CHI2.INV.RT
CHISQ.TEST = CHI2.TEST
CONFIDENCE.NORM = KONFIDENS.NORM
CONFIDENCE.T = KONFIDENS.T
CORREL = KORREL
COUNT = ANTAL
COUNTA = ANTALV
COUNTBLANK = ANTAL.TOMMA
COUNTIF = ANTAL.OM
COUNTIFS = ANTAL.OMF
COVARIANCE.P = KOVARIANS.P
COVARIANCE.S = KOVARIANS.S
DEVSQ = KVADAVV
EXPON.DIST = EXPON.FÖRD
F.DIST = F.FÖRD
F.DIST.RT = F.FÖRD.RT
F.INV = F.INV
F.INV.RT = F.INV.RT
F.TEST = F.TEST
FISHER = FISHER
FISHERINV = FISHERINV
FORECAST.ETS = PROGNOS.ETS
FORECAST.ETS.CONFINT = PROGNOS.ETS.KONFINT
FORECAST.ETS.SEASONALITY = PROGNOS.ETS.SÄSONGSBEROENDE
FORECAST.ETS.STAT = PROGNOS.ETS.STAT
FORECAST.LINEAR = PROGNOS.LINJÄR
FREQUENCY = FREKVENS
GAMMA = GAMMA
GAMMA.DIST = GAMMA.FÖRD
GAMMA.INV = GAMMA.INV
GAMMALN = GAMMALN
GAMMALN.PRECISE = GAMMALN.EXAKT
GAUSS = GAUSS
GEOMEAN = GEOMEDEL
GROWTH = EXPTREND
HARMEAN = HARMMEDEL
HYPGEOM.DIST = HYPGEOM.FÖRD
INTERCEPT = SKÄRNINGSPUNKT
KURT = TOPPIGHET
LARGE = STÖRSTA
LINEST = REGR
LOGEST = EXPREGR
LOGNORM.DIST = LOGNORM.FÖRD
LOGNORM.INV = LOGNORM.INV
MAX = MAX
MAXA = MAXA
MAXIFS = MAXIFS
MEDIAN = MEDIAN
MIN = MIN
MINA = MINA
MINIFS = MINIFS
MODE.MULT = TYPVÄRDE.FLERA
MODE.SNGL = TYPVÄRDE.ETT
NEGBINOM.DIST = NEGBINOM.FÖRD
NORM.DIST = NORM.FÖRD
NORM.INV = NORM.INV
NORM.S.DIST = NORM.S.FÖRD
NORM.S.INV = NORM.S.INV
PEARSON = PEARSON
PERCENTILE.EXC = PERCENTIL.EXK
PERCENTILE.INC = PERCENTIL.INK
PERCENTRANK.EXC = PROCENTRANG.EXK
PERCENTRANK.INC = PROCENTRANG.INK
PERMUT = PERMUT
PERMUTATIONA = PERMUTATIONA
PHI = PHI
POISSON.DIST = POISSON.FÖRD
PROB = SANNOLIKHET
QUARTILE.EXC = KVARTIL.EXK
QUARTILE.INC = KVARTIL.INK
RANK.AVG = RANG.MED
RANK.EQ = RANG.EKV
RSQ = RKV
SKEW = SNEDHET
SKEW.P = SNEDHET.P
SLOPE = LUTNING
SMALL = MINSTA
STANDARDIZE = STANDARDISERA
STDEV.P = STDAV.P
STDEV.S = STDAV.S
STDEVA = STDEVA
STDEVPA = STDEVPA
STEYX = STDFELYX
T.DIST = T.FÖRD
T.DIST.2T = T.FÖRD.2T
T.DIST.RT = T.FÖRD.RT
T.INV = T.INV
T.INV.2T = T.INV.2T
T.TEST = T.TEST
TREND = TREND
TRIMMEAN = TRIMMEDEL
VAR.P = VARIANS.P
VAR.S = VARIANS.S
VARA = VARA
VARPA = VARPA
WEIBULL.DIST = WEIBULL.FÖRD
Z.TEST = Z.TEST

##
## Textfunktioner (Text Functions)
##
BAHTTEXT = BAHTTEXT
CHAR = TECKENKOD
CLEAN = STÄDA
CODE = KOD
CONCAT = SAMMAN
DOLLAR = VALUTA
EXACT = EXAKT
FIND = HITTA
FIXED = FASTTAL
LEFT = VÄNSTER
LEN = LÄNGD
LOWER = GEMENER
MID = EXTEXT
NUMBERVALUE = TALVÄRDE
PROPER = INITIAL
REPLACE = ERSÄTT
REPT = REP
RIGHT = HÖGER
SEARCH = SÖK
SUBSTITUTE = BYT.UT
T = T
TEXT = TEXT
TEXTJOIN = TEXTJOIN
THAIDIGIT = THAISIFFRA
THAINUMSOUND = THAITALLJUD
THAINUMSTRING = THAITALSTRÄNG
THAISTRINGLENGTH = THAISTRÄNGLÄNGD
TRIM = RENSA
UNICHAR = UNITECKENKOD
UNICODE = UNICODE
UPPER = VERSALER
VALUE = TEXTNUM

##
## Webbfunktioner (Web Functions)
##
ENCODEURL = KODAWEBBADRESS
FILTERXML = FILTRERAXML
WEBSERVICE = WEBBTJÄNST

##
## Kompatibilitetsfunktioner (Compatibility Functions)
##
BETADIST = BETAFÖRD
BETAINV = BETAINV
BINOMDIST = BINOMFÖRD
CEILING = RUNDA.UPP
CHIDIST = CHI2FÖRD
CHIINV = CHI2INV
CHITEST = CHI2TEST
CONCATENATE = SAMMANFOGA
CONFIDENCE = KONFIDENS
COVAR = KOVAR
CRITBINOM = KRITBINOM
EXPONDIST = EXPONFÖRD
FDIST = FFÖRD
FINV = FINV
FLOOR = RUNDA.NER
FORECAST = PREDIKTION
FTEST = FTEST
GAMMADIST = GAMMAFÖRD
GAMMAINV = GAMMAINV
HYPGEOMDIST = HYPGEOMFÖRD
LOGINV = LOGINV
LOGNORMDIST = LOGNORMFÖRD
MODE = TYPVÄRDE
NEGBINOMDIST = NEGBINOMFÖRD
NORMDIST = NORMFÖRD
NORMINV = NORMINV
NORMSDIST = NORMSFÖRD
NORMSINV = NORMSINV
PERCENTILE = PERCENTIL
PERCENTRANK = PROCENTRANG
POISSON = POISSON
QUARTILE = KVARTIL
RANK = RANG
STDEV = STDAV
STDEVP = STDAVP
TDIST = TFÖRD
TINV = TINV
TTEST = TTEST
VAR = VARIANS
VARP = VARIANSP
WEIBULL = WEIBULL
ZTEST = ZTEST
