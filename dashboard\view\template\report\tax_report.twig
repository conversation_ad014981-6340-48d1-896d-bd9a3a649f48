{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <div class="btn-group">
          <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown">
            <i class="fa fa-download"></i> {{ text_export }} <span class="caret"></span>
          </button>
          <ul class="dropdown-menu dropdown-menu-right">
            <li><a href="#" onclick="exportReport('pdf')"><i class="fa fa-file-pdf-o"></i> PDF</a></li>
            <li><a href="#" onclick="exportReport('excel')"><i class="fa fa-file-excel-o"></i> Excel</a></li>
            <li><a href="#" onclick="exportReport('csv')"><i class="fa fa-file-text-o"></i> CSV</a></li>
          </ul>
        </div>
        <button type="button" class="btn btn-success" onclick="generateTaxFiling()">
          <i class="fa fa-file-text"></i> {{ text_generate_filing }}
        </button>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  
  <div class="container-fluid">
    <!-- Filter Panel -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-filter"></i> {{ text_filter }}</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-md-3">
            <div class="form-group">
              <label>{{ text_date_start }}</label>
              <input type="date" name="filter_date_start" value="{{ filter_date_start }}" class="form-control" />
            </div>
          </div>
          <div class="col-md-3">
            <div class="form-group">
              <label>{{ text_date_end }}</label>
              <input type="date" name="filter_date_end" value="{{ filter_date_end }}" class="form-control" />
            </div>
          </div>
          <div class="col-md-2">
            <div class="form-group">
              <label>{{ text_tax_type }}</label>
              <select name="filter_tax_type" class="form-control">
                <option value="">{{ text_all }}</option>
                <option value="VAT" {% if filter_tax_type == 'VAT' %}selected{% endif %}>VAT</option>
                <option value="Sales Tax" {% if filter_tax_type == 'Sales Tax' %}selected{% endif %}>Sales Tax</option>
              </select>
            </div>
          </div>
          <div class="col-md-2">
            <div class="form-group">
              <label>{{ text_eta_status }}</label>
              <select name="filter_eta_status" class="form-control">
                <option value="">{{ text_all }}</option>
                <option value="sent" {% if filter_eta_status == 'sent' %}selected{% endif %}>{{ text_sent }}</option>
                <option value="accepted" {% if filter_eta_status == 'accepted' %}selected{% endif %}>{{ text_accepted }}</option>
                <option value="rejected" {% if filter_eta_status == 'rejected' %}selected{% endif %}>{{ text_rejected }}</option>
              </select>
            </div>
          </div>
          <div class="col-md-2">
            <div class="form-group">
              <label>&nbsp;</label>
              <button type="button" class="btn btn-primary btn-block" onclick="applyFilters()">
                <i class="fa fa-search"></i> {{ text_filter }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Tax Summary Dashboard -->
    <div class="row">
      <div class="col-md-3">
        <div class="panel panel-primary">
          <div class="panel-body text-center">
            <h3>{{ tax_summary.total_orders|number_format }}</h3>
            <p>{{ text_total_orders }}</p>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="panel panel-success">
          <div class="panel-body text-center">
            <h3>{{ currency_format(tax_summary.total_tax_amount) }}</h3>
            <p>{{ text_total_tax_collected }}</p>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="panel panel-info">
          <div class="panel-body text-center">
            <h3>{{ tax_summary.eta_success_rate|number_format(1) }}%</h3>
            <p>{{ text_eta_success_rate }}</p>
          </div>
        </div>
      </div>
      <div class="col-md-3">
        <div class="panel panel-warning">
          <div class="panel-body text-center">
            <h3>{{ tax_summary.eta_pending_count|number_format }}</h3>
            <p>{{ text_pending_eta }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Charts Row -->
    <div class="row">
      <div class="col-md-6">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-line-chart"></i> {{ text_monthly_trends }}</h3>
          </div>
          <div class="panel-body">
            <canvas id="monthlyTrendsChart" height="300"></canvas>
          </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-pie-chart"></i> {{ text_eta_compliance }}</h3>
          </div>
          <div class="panel-body">
            <canvas id="etaComplianceChart" height="300"></canvas>
          </div>
        </div>
      </div>
    </div>

    <!-- Tax Breakdown Table -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-table"></i> {{ text_tax_breakdown }}</h3>
      </div>
      <div class="panel-body">
        <div class="table-responsive">
          <table class="table table-striped table-hover">
            <thead>
              <tr>
                <th>{{ text_tax_type }}</th>
                <th>{{ text_tax_rate }}</th>
                <th>{{ text_order_count }}</th>
                <th>{{ text_taxable_amount }}</th>
                <th>{{ text_tax_amount }}</th>
                <th>{{ text_average_tax }}</th>
                <th>{{ text_actions }}</th>
              </tr>
            </thead>
            <tbody>
              {% for breakdown in tax_breakdown %}
              <tr>
                <td>{{ breakdown.tax_name }}</td>
                <td>{{ breakdown.tax_rate }}%</td>
                <td>{{ breakdown.order_count|number_format }}</td>
                <td>{{ currency_format(breakdown.total_taxable_amount) }}</td>
                <td>{{ currency_format(breakdown.total_tax_amount) }}</td>
                <td>{{ currency_format(breakdown.average_tax_amount) }}</td>
                <td>
                  <button type="button" class="btn btn-sm btn-info" onclick="viewDetails('tax_details', '{{ breakdown.tax_name }}')">
                    <i class="fa fa-eye"></i> {{ text_view_details }}
                  </button>
                </td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Top Customers by Tax -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-users"></i> {{ text_top_customers }}</h3>
      </div>
      <div class="panel-body">
        <div class="table-responsive">
          <table class="table table-striped">
            <thead>
              <tr>
                <th>{{ text_customer_name }}</th>
                <th>{{ text_email }}</th>
                <th>{{ text_order_count }}</th>
                <th>{{ text_total_tax_paid }}</th>
                <th>{{ text_total_spent }}</th>
                <th>{{ text_avg_tax_per_order }}</th>
                <th>{{ text_last_order }}</th>
                <th>{{ text_actions }}</th>
              </tr>
            </thead>
            <tbody>
              {% for customer in top_customers %}
              <tr>
                <td>{{ customer.customer_name }}</td>
                <td>{{ customer.email }}</td>
                <td>{{ customer.order_count|number_format }}</td>
                <td>{{ currency_format(customer.total_tax_paid) }}</td>
                <td>{{ currency_format(customer.total_spent) }}</td>
                <td>{{ currency_format(customer.avg_tax_per_order) }}</td>
                <td>{{ customer.last_order_date|date('d/m/Y') }}</td>
                <td>
                  <button type="button" class="btn btn-sm btn-info" onclick="viewCustomerDetails({{ customer.customer_id }})">
                    <i class="fa fa-eye"></i> {{ text_view }}
                  </button>
                </td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Pending ETA Submissions -->
    {% if pending_eta %}
    <div class="panel panel-warning">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-exclamation-triangle"></i> {{ text_pending_eta_submissions }}</h3>
      </div>
      <div class="panel-body">
        <div class="table-responsive">
          <table class="table table-striped">
            <thead>
              <tr>
                <th>{{ text_order_id }}</th>
                <th>{{ text_customer }}</th>
                <th>{{ text_type }}</th>
                <th>{{ text_status }}</th>
                <th>{{ text_attempts }}</th>
                <th>{{ text_next_attempt }}</th>
                <th>{{ text_error }}</th>
                <th>{{ text_actions }}</th>
              </tr>
            </thead>
            <tbody>
              {% for item in pending_eta %}
              <tr>
                <td>{{ item.order_id }}</td>
                <td>{{ item.customer_name }}</td>
                <td>{{ item.type }}</td>
                <td>
                  <span class="label label-{% if item.status == 'pending' %}warning{% else %}danger{% endif %}">
                    {{ item.status }}
                  </span>
                </td>
                <td>{{ item.attempts }}</td>
                <td>{{ item.next_attempt|date('d/m/Y H:i') }}</td>
                <td>
                  {% if item.error_message %}
                  <span class="text-danger" title="{{ item.error_message }}">
                    {{ item.error_message|slice(0, 50) }}...
                  </span>
                  {% endif %}
                </td>
                <td>
                  <button type="button" class="btn btn-sm btn-primary" onclick="retryETASubmission({{ item.queue_id }})">
                    <i class="fa fa-refresh"></i> {{ text_retry }}
                  </button>
                </td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    </div>
    {% endif %}
  </div>
</div>

<!-- Details Modal -->
<div class="modal fade" id="detailsModal" tabindex="-1" role="dialog">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{{ text_details }}</h4>
      </div>
      <div class="modal-body">
        <div id="detailsContent">
          <div class="text-center">
            <i class="fa fa-spinner fa-spin fa-2x"></i>
            <p>{{ text_loading }}</p>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{{ text_close }}</button>
      </div>
    </div>
  </div>
</div>

<!-- Tax Filing Modal -->
<div class="modal fade" id="taxFilingModal" tabindex="-1" role="dialog">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{{ text_generate_tax_filing }}</h4>
      </div>
      <div class="modal-body">
        <form id="taxFilingForm">
          <div class="form-group">
            <label>{{ text_filing_period }}</label>
            <input type="month" name="filing_period" value="{{ 'now'|date('Y-m') }}" class="form-control" required />
          </div>
          <div class="form-group">
            <label>{{ text_filing_type }}</label>
            <select name="filing_type" class="form-control">
              <option value="monthly">{{ text_monthly }}</option>
              <option value="quarterly">{{ text_quarterly }}</option>
              <option value="yearly">{{ text_yearly }}</option>
            </select>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{{ text_cancel }}</button>
        <button type="button" class="btn btn-primary" onclick="submitTaxFiling()">
          <i class="fa fa-file-text"></i> {{ text_generate }}
        </button>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript">
// Chart.js initialization
var monthlyTrendsChart, etaComplianceChart;

$(document).ready(function() {
    initializeCharts();
});

function initializeCharts() {
    // Monthly Trends Chart
    var ctx1 = document.getElementById('monthlyTrendsChart').getContext('2d');
    monthlyTrendsChart = new Chart(ctx1, {
        type: 'line',
        data: {
            labels: [{% for trend in monthly_trends %}'{{ trend.month }}'{% if not loop.last %},{% endif %}{% endfor %}],
            datasets: [{
                label: '{{ text_tax_amount }}',
                data: [{% for trend in monthly_trends %}{{ trend.tax_amount }}{% if not loop.last %},{% endif %}{% endfor %}],
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1
            }, {
                label: '{{ text_order_count }}',
                data: [{% for trend in monthly_trends %}{{ trend.order_count }}{% if not loop.last %},{% endif %}{% endfor %}],
                borderColor: 'rgb(255, 99, 132)',
                backgroundColor: 'rgba(255, 99, 132, 0.2)',
                tension: 0.1,
                yAxisID: 'y1'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    grid: {
                        drawOnChartArea: false,
                    },
                }
            }
        }
    });

    // ETA Compliance Chart
    var ctx2 = document.getElementById('etaComplianceChart').getContext('2d');
    etaComplianceChart = new Chart(ctx2, {
        type: 'doughnut',
        data: {
            labels: [{% for compliance in eta_compliance %}'{{ compliance.status|title }}'{% if not loop.last %},{% endif %}{% endfor %}],
            datasets: [{
                data: [{% for compliance in eta_compliance %}{{ compliance.count }}{% if not loop.last %},{% endif %}{% endfor %}],
                backgroundColor: [
                    '#28a745', // sent - green
                    '#17a2b8', // accepted - blue
                    '#dc3545', // rejected - red
                    '#ffc107', // pending - yellow
                    '#6c757d'  // other - gray
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

function applyFilters() {
    var params = new URLSearchParams();
    params.append('filter_date_start', $('input[name="filter_date_start"]').val());
    params.append('filter_date_end', $('input[name="filter_date_end"]').val());
    params.append('filter_tax_type', $('select[name="filter_tax_type"]').val());
    params.append('filter_eta_status', $('select[name="filter_eta_status"]').val());
    
    window.location.href = 'index.php?route=report/tax_report&user_token={{ user_token }}&' + params.toString();
}

function exportReport(format) {
    var params = new URLSearchParams();
    params.append('filter_date_start', $('input[name="filter_date_start"]').val());
    params.append('filter_date_end', $('input[name="filter_date_end"]').val());
    params.append('filter_tax_type', $('select[name="filter_tax_type"]').val());
    params.append('filter_eta_status', $('select[name="filter_eta_status"]').val());
    
    var url = 'index.php?route=report/tax_report/export' + format.toUpperCase() + '&user_token={{ user_token }}&' + params.toString();
    window.open(url, '_blank');
}

function viewDetails(reportType, filter) {
    $('#detailsModal').modal('show');
    
    $.ajax({
        url: 'index.php?route=report/tax_report/ajax_get_detailed_data&user_token={{ user_token }}',
        type: 'POST',
        data: {
            report_type: reportType,
            date_start: $('input[name="filter_date_start"]').val(),
            date_end: $('input[name="filter_date_end"]').val(),
            filters: {
                tax_type: filter
            }
        },
        dataType: 'json',
        success: function(json) {
            if (json.success) {
                var html = '<div class="table-responsive"><table class="table table-striped"><thead><tr>';
                
                // Build table headers based on report type
                if (reportType === 'tax_details') {
                    html += '<th>{{ text_order_id }}</th><th>{{ text_date }}</th><th>{{ text_customer }}</th><th>{{ text_tax_amount }}</th><th>{{ text_eta_status }}</th>';
                }
                
                html += '</tr></thead><tbody>';
                
                // Build table rows
                $.each(json.data, function(index, item) {
                    html += '<tr>';
                    if (reportType === 'tax_details') {
                        html += '<td>' + item.order_id + '</td>';
                        html += '<td>' + item.date_added + '</td>';
                        html += '<td>' + item.customer_name + '</td>';
                        html += '<td>' + item.tax_amount + '</td>';
                        html += '<td>' + item.eta_status + '</td>';
                    }
                    html += '</tr>';
                });
                
                html += '</tbody></table></div>';
                $('#detailsContent').html(html);
            } else {
                $('#detailsContent').html('<div class="alert alert-danger">' + json.error + '</div>');
            }
        },
        error: function() {
            $('#detailsContent').html('<div class="alert alert-danger">{{ text_error_loading_data }}</div>');
        }
    });
}

function generateTaxFiling() {
    $('#taxFilingModal').modal('show');
}

function submitTaxFiling() {
    var formData = $('#taxFilingForm').serialize();
    
    $.ajax({
        url: 'index.php?route=report/tax_report/generateFiling&user_token={{ user_token }}',
        type: 'POST',
        data: formData,
        dataType: 'json',
        beforeSend: function() {
            $('#taxFilingModal .btn-primary').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> {{ text_generating }}');
        },
        complete: function() {
            $('#taxFilingModal .btn-primary').prop('disabled', false).html('<i class="fa fa-file-text"></i> {{ text_generate }}');
        },
        success: function(json) {
            if (json.success) {
                $('#taxFilingModal').modal('hide');
                alert('{{ text_filing_generated_success }}');
                if (json.download_url) {
                    window.open(json.download_url, '_blank');
                }
            } else {
                alert('{{ text_error }}: ' + json.error);
            }
        },
        error: function() {
            alert('{{ text_error_generating_filing }}');
        }
    });
}

function retryETASubmission(queueId) {
    $.ajax({
        url: 'index.php?route=extension/eta/invoice/retrySubmission&user_token={{ user_token }}',
        type: 'POST',
        data: {queue_id: queueId},
        dataType: 'json',
        success: function(json) {
            if (json.success) {
                alert('{{ text_retry_success }}');
                location.reload();
            } else {
                alert('{{ text_error }}: ' + json.error);
            }
        },
        error: function() {
            alert('{{ text_error_retry }}');
        }
    });
}

function viewCustomerDetails(customerId) {
    window.open('index.php?route=customer/customer/edit&user_token={{ user_token }}&customer_id=' + customerId, '_blank');
}
</script>

<!-- Include Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

{{ footer }}
