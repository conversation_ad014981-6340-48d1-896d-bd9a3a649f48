{{ header }}{{ column_left }}

<!-- Enterprise Grade Plus CSS for Journal Review -->
<style>
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --danger-color: #e74c3c;
    --warning-color: #f39c12;
    --info-color: #17a2b8;
    --review-color: #9b59b6;
    --light-bg: #f8f9fa;
    --border-color: #dee2e6;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

.journal-review-container {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: var(--shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.journal-review-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--review-color), var(--primary-color), var(--secondary-color));
}

.journal-review-header {
    text-align: center;
    border-bottom: 3px solid var(--review-color);
    padding-bottom: 20px;
    margin-bottom: 30px;
    position: relative;
}

.journal-review-header h2 {
    color: var(--review-color);
    font-weight: 700;
    font-size: 1.8rem;
    margin: 0;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.journal-review-summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.journal-review-summary-card {
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    text-align: center;
    box-shadow: var(--shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.journal-review-summary-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0,0,0,0.1);
}

.journal-review-summary-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
}

.journal-review-summary-card.total::before { background: var(--review-color); }
.journal-review-summary-card.pending::before { background: var(--warning-color); }
.journal-review-summary-card.approved::before { background: var(--success-color); }
.journal-review-summary-card.rejected::before { background: var(--danger-color); }

.journal-review-summary-card h4 {
    color: var(--primary-color);
    margin-bottom: 10px;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.journal-review-summary-card .amount {
    font-size: 1.4rem;
    font-weight: 700;
    font-family: 'Courier New', monospace;
    margin-bottom: 5px;
}

.journal-review-summary-card .description {
    font-size: 0.8rem;
    color: #6c757d;
    font-weight: 500;
}

.card-total .amount { color: var(--review-color); }
.card-pending .amount { color: var(--warning-color); }
.card-approved .amount { color: var(--success-color); }
.card-rejected .amount { color: var(--danger-color); }

.journal-review-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    background: #ffffff;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.journal-review-table th {
    background: linear-gradient(135deg, var(--review-color), #8e44ad);
    color: white;
    padding: 15px 12px;
    text-align: center;
    font-weight: 600;
    font-size: 0.9rem;
    border-bottom: 2px solid var(--border-color);
}

.journal-review-table td {
    padding: 12px;
    border-bottom: 1px solid var(--border-color);
    text-align: center;
    transition: var(--transition);
}

.journal-review-table tbody tr:hover {
    background: var(--light-bg);
    transform: scale(1.005);
}

.journal-review-table tbody tr:nth-child(even) {
    background: #fafbfc;
}

.amount-cell {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    text-align: {{ direction == 'rtl' ? 'right' : 'left' }};
}

.amount-positive { 
    color: var(--success-color); 
    font-weight: 600;
}

.amount-negative { 
    color: var(--danger-color); 
    font-weight: 600;
}

.amount-neutral { 
    color: var(--secondary-color); 
    font-weight: 600;
}

.journal-review-actions {
    display: flex;
    gap: 5px;
    justify-content: center;
}

.journal-review-actions .btn {
    padding: 5px 10px;
    font-size: 0.8rem;
}

/* Status Badges */
.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-pending { background: #fff3cd; color: #856404; }
.status-under-review { background: #d1ecf1; color: #0c5460; }
.status-approved { background: #d4edda; color: #155724; }
.status-rejected { background: #f8d7da; color: #721c24; }
.status-returned { background: #e2e3e5; color: #383d41; }

/* Priority Badges */
.priority-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
}

.priority-urgent { background: #dc3545; color: white; }
.priority-high { background: #fd7e14; color: white; }
.priority-medium { background: #ffc107; color: #212529; }
.priority-low { background: #6c757d; color: white; }

/* Filter Panel */
.filter-panel {
    background: linear-gradient(135deg, #f8f9fa, #ffffff);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: var(--shadow);
}

/* RTL Support */
[dir="rtl"] .journal-review-table {
    direction: rtl;
}

[dir="rtl"] .amount-cell {
    text-align: right;
}

/* Print Styles */
@media print {
    .journal-review-container {
        box-shadow: none;
        border: 1px solid #000;
    }
    
    .journal-review-table th {
        background: #f0f0f0 !important;
        color: #000 !important;
    }
    
    .btn, .panel-heading, .filter-panel {
        display: none !important;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .journal-review-table {
        font-size: 0.8rem;
    }
    
    .journal-review-table th,
    .journal-review-table td {
        padding: 8px 6px;
    }
    
    .journal-review-summary-cards {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .journal-review-actions {
        flex-direction: column;
    }
}
</style>

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-{{ direction == 'rtl' ? 'start' : 'end' }}">
        <div class="btn-group" role="group" aria-label="{{ text_actions }}">
          <button type="button" class="btn btn-success btn-lg" onclick="addReview()"
                  data-toggle="tooltip" title="{{ text_add_review }}">
            <i class="fa fa-plus"></i> {{ button_add }}
          </button>
          <div class="btn-group" role="group">
            <button type="button" class="btn btn-info dropdown-toggle" data-toggle="dropdown"
                    aria-expanded="false" data-toggle="tooltip" title="{{ text_export_options }}">
              <i class="fa fa-download"></i> {{ text_export }}
            </button>
            <ul class="dropdown-menu">
              <li><a class="dropdown-item" href="#" onclick="exportReviews('excel')">
                <i class="fa fa-file-excel text-success"></i> Excel
              </a></li>
              <li><a class="dropdown-item" href="#" onclick="exportReviews('pdf')">
                <i class="fa fa-file-pdf text-danger"></i> PDF
              </a></li>
              <li><a class="dropdown-item" href="#" onclick="exportReviews('csv')">
                <i class="fa fa-file-csv text-info"></i> CSV
              </a></li>
              <li><hr class="dropdown-divider"></li>
              <li><a class="dropdown-item" href="#" onclick="printReviews()">
                <i class="fa fa-print text-muted"></i> {{ text_print }}
              </a></li>
            </ul>
          </div>
          <button type="button" class="btn btn-default" onclick="bulkApprove()"
                  data-toggle="tooltip" title="{{ button_bulk_approve }}">
            <i class="fa fa-check"></i>
          </button>
          <button type="button" class="btn btn-default" onclick="bulkReject()"
                  data-toggle="tooltip" title="{{ button_bulk_reject }}">
            <i class="fa fa-times-circle"></i>
          </button>
        </div>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li class="breadcrumb-item"><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>

  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible">
      <i class="fa fa-exclamation-triangle"></i>
      {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}

    {% if success %}
    <div class="alert alert-success alert-dismissible">
      <i class="fa fa-check-circle"></i>
      {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}

    <!-- Filter Panel -->
    <div class="filter-panel">
      <h4>{{ text_review_filters }}</h4>
      <form id="journal-review-filter-form" method="post">
        <div class="row">
          <div class="col-md-2">
            <div class="form-group">
              <label for="filter_status" class="form-label">{{ entry_filter_status }}</label>
              <select name="filter_status" id="filter_status" class="form-control">
                <option value="">{{ text_all_statuses }}</option>
                <option value="pending_review"{% if filter_status == 'pending_review' %} selected{% endif %}>{{ text_pending_review }}</option>
                <option value="under_review"{% if filter_status == 'under_review' %} selected{% endif %}>{{ text_under_review }}</option>
                <option value="approved"{% if filter_status == 'approved' %} selected{% endif %}>{{ text_approved }}</option>
                <option value="rejected"{% if filter_status == 'rejected' %} selected{% endif %}>{{ text_rejected }}</option>
                <option value="returned"{% if filter_status == 'returned' %} selected{% endif %}>{{ text_returned }}</option>
              </select>
            </div>
          </div>
          <div class="col-md-2">
            <div class="form-group">
              <label for="filter_reviewer" class="form-label">{{ entry_filter_reviewer }}</label>
              <select name="filter_reviewer" id="filter_reviewer" class="form-control">
                <option value="">{{ text_all_reviewers }}</option>
                {% for reviewer in reviewers %}
                <option value="{{ reviewer.user_id }}"{% if reviewer.user_id == filter_reviewer %} selected{% endif %}>{{ reviewer.firstname }} {{ reviewer.lastname }}</option>
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="col-md-2">
            <div class="form-group">
              <label for="filter_priority" class="form-label">{{ entry_filter_priority }}</label>
              <select name="filter_priority" id="filter_priority" class="form-control">
                <option value="">{{ text_all_priorities }}</option>
                <option value="urgent"{% if filter_priority == 'urgent' %} selected{% endif %}>{{ text_urgent }}</option>
                <option value="high"{% if filter_priority == 'high' %} selected{% endif %}>{{ text_high_priority }}</option>
                <option value="medium"{% if filter_priority == 'medium' %} selected{% endif %}>{{ text_medium_priority }}</option>
                <option value="low"{% if filter_priority == 'low' %} selected{% endif %}>{{ text_low_priority }}</option>
              </select>
            </div>
          </div>
          <div class="col-md-2">
            <div class="form-group">
              <label for="filter_date_start" class="form-label">{{ entry_filter_date_start }}</label>
              <input type="date" name="filter_date_start" id="filter_date_start" value="{{ filter_date_start }}" class="form-control">
            </div>
          </div>
          <div class="col-md-2">
            <div class="form-group">
              <label for="filter_date_end" class="form-label">{{ entry_filter_date_end }}</label>
              <input type="date" name="filter_date_end" id="filter_date_end" value="{{ filter_date_end }}" class="form-control">
            </div>
          </div>
          <div class="col-md-2">
            <div class="form-group">
              <label class="form-label">&nbsp;</label>
              <div>
                <button type="submit" class="btn btn-primary btn-block">
                  <i class="fa fa-search"></i> {{ button_filter }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>

    <!-- Journal Review Content -->
    {% if reviews %}
    <!-- Summary Cards -->
    <div class="journal-review-summary-cards">
      <div class="journal-review-summary-card card-total total">
        <h4>{{ text_total_reviews }}</h4>
        <div class="amount">{{ summary.total_reviews }}</div>
        <div class="description">{{ text_reviews }}</div>
      </div>
      <div class="journal-review-summary-card card-pending pending">
        <h4>{{ text_pending_reviews }}</h4>
        <div class="amount">{{ summary.pending_reviews }}</div>
        <div class="description">{{ text_pending_review }}</div>
      </div>
      <div class="journal-review-summary-card card-approved approved">
        <h4>{{ text_approved }}</h4>
        <div class="amount">{{ summary.approved_reviews }}</div>
        <div class="description">{{ text_approved }}</div>
      </div>
      <div class="journal-review-summary-card card-rejected rejected">
        <h4>{{ text_rejected }}</h4>
        <div class="amount">{{ summary.rejected_reviews }}</div>
        <div class="description">{{ text_rejected }}</div>
      </div>
    </div>

    <!-- Journal Review Table -->
    <div class="journal-review-container">
      <div class="journal-review-header">
        <h2>{{ text_reviews_list }}</h2>
      </div>

      <div class="table-responsive">
        <table class="journal-review-table" id="journal-review-table">
          <thead>
            <tr>
              <th><input type="checkbox" id="select-all"></th>
              <th>{{ column_journal_number }}</th>
              <th>{{ column_journal_date }}</th>
              <th>{{ column_description }}</th>
              <th>{{ column_amount }}</th>
              <th>{{ column_status }}</th>
              <th>{{ column_reviewer }}</th>
              <th>{{ column_priority }}</th>
              <th>{{ column_review_date }}</th>
              <th>{{ column_action }}</th>
            </tr>
          </thead>
          <tbody>
            {% for review in reviews %}
            <tr data-review-id="{{ review.review_id }}">
              <td><input type="checkbox" name="selected[]" value="{{ review.review_id }}"></td>
              <td>
                <strong>{{ review.journal_number }}</strong>
                <br>
                <small class="text-muted">{{ review.journal_reference }}</small>
              </td>
              <td>{{ review.journal_date_formatted }}</td>
              <td>
                <div class="text-truncate" style="max-width: 200px;" title="{{ review.description }}">
                  {{ review.description }}
                </div>
              </td>
              <td class="amount-cell">
                <strong class="amount-neutral">{{ review.amount_formatted }}</strong>
              </td>
              <td>
                <span class="status-badge status-{{ review.status }}">
                  {{ review.status_text }}
                </span>
              </td>
              <td>
                {% if review.reviewer_name %}
                  {{ review.reviewer_name }}
                {% else %}
                  <span class="text-muted">{{ text_unassigned }}</span>
                {% endif %}
              </td>
              <td>
                <span class="priority-badge priority-{{ review.priority }}">
                  {{ review.priority_text }}
                </span>
              </td>
              <td>
                {% if review.review_date %}
                  {{ review.review_date_formatted }}
                {% else %}
                  <span class="text-muted">-</span>
                {% endif %}
              </td>
              <td>
                <div class="journal-review-actions">
                  <button type="button" class="btn btn-info btn-sm"
                          onclick="viewReview({{ review.review_id }})"
                          data-toggle="tooltip" title="{{ text_view }}">
                    <i class="fa fa-eye"></i>
                  </button>
                  {% if review.status == 'pending_review' or review.status == 'under_review' %}
                  <button type="button" class="btn btn-success btn-sm"
                          onclick="approveReview({{ review.review_id }})"
                          data-toggle="tooltip" title="{{ text_approve }}">
                    <i class="fa fa-check"></i>
                  </button>
                  <button type="button" class="btn btn-danger btn-sm"
                          onclick="rejectReview({{ review.review_id }})"
                          data-toggle="tooltip" title="{{ text_reject }}">
                    <i class="fa fa-times"></i>
                  </button>
                  <button type="button" class="btn btn-warning btn-sm"
                          onclick="returnReview({{ review.review_id }})"
                          data-toggle="tooltip" title="{{ text_return }}">
                    <i class="fa fa-undo"></i>
                  </button>
                  {% endif %}
                  <button type="button" class="btn btn-default btn-sm"
                          onclick="editReview({{ review.review_id }})"
                          data-toggle="tooltip" title="{{ text_edit }}">
                    <i class="fa fa-edit"></i>
                  </button>
                </div>
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>
    {% else %}
    <div class="alert alert-info">
      <i class="fa fa-info-circle"></i>
      {{ text_no_reviews }}
    </div>
    {% endif %}
  </div>
</div>

<script>
// Enterprise Grade Plus JavaScript for Journal Review
class JournalReviewManager {
    constructor() {
        this.initializeTooltips();
        this.initializeDataTable();
        this.initializeKeyboardShortcuts();
        this.initializeSelectAll();
    }

    initializeTooltips() {
        $('[data-toggle="tooltip"]').tooltip();
    }

    initializeDataTable() {
        const table = document.getElementById('journal-review-table');
        if (table && typeof $ !== 'undefined' && $.fn.DataTable) {
            $(table).DataTable({
                responsive: true,
                pageLength: 25,
                order: [[8, 'desc']], // Sort by review date desc
                columnDefs: [
                    { targets: [0, 9], orderable: false },
                    { targets: [4], className: 'text-end' },
                    { targets: [5, 6, 7], className: 'text-center' }
                ],
                language: {
                    url: '{{ direction == "rtl" ? "/view/javascript/jquery/datatables-ar.json" : "/view/javascript/jquery/datatables-en.json" }}'
                }
            });
        }
    }

    initializeKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case 'n':
                        e.preventDefault();
                        this.addReview();
                        break;
                    case 'e':
                        e.preventDefault();
                        this.showExportMenu();
                        break;
                    case 'p':
                        e.preventDefault();
                        this.printReviews();
                        break;
                    case 'a':
                        e.preventDefault();
                        this.bulkApprove();
                        break;
                    case 'r':
                        e.preventDefault();
                        this.bulkReject();
                        break;
                }
            }
        });
    }

    initializeSelectAll() {
        const selectAllCheckbox = document.getElementById('select-all');
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', (e) => {
                const checkboxes = document.querySelectorAll('input[name="selected[]"]');
                checkboxes.forEach(checkbox => {
                    checkbox.checked = e.target.checked;
                });
            });
        }
    }

    addReview() {
        window.open('{{ url_link('accounts/journal_review', 'add') }}', '_blank');
    }

    viewReview(reviewId) {
        window.open('{{ url_link('accounts/journal_review', 'view') }}&review_id=' + reviewId, '_blank');
    }

    editReview(reviewId) {
        window.open('{{ url_link('accounts/journal_review', 'edit') }}&review_id=' + reviewId, '_blank');
    }

    approveReview(reviewId) {
        if (confirm('{{ text_confirm_approve }}')) {
            this.showLoadingState(true);

            fetch('{{ url_link('accounts/journal_review', 'approve') }}', {
                method: 'POST',
                body: JSON.stringify({ review_id: reviewId }),
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                this.showLoadingState(false);
                if (data.success) {
                    this.showAlert('{{ success_journal_approved }}', 'success');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    this.showAlert(data.error || '{{ error_approve_review }}', 'danger');
                }
            })
            .catch(error => {
                this.showLoadingState(false);
                this.showAlert('{{ error_approve_review }}: ' + error.message, 'danger');
            });
        }
    }

    rejectReview(reviewId) {
        const reason = prompt('{{ text_rejection_reason }}:');
        if (reason) {
            this.showLoadingState(true);

            fetch('{{ url_link('accounts/journal_review', 'reject') }}', {
                method: 'POST',
                body: JSON.stringify({ review_id: reviewId, reason: reason }),
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                this.showLoadingState(false);
                if (data.success) {
                    this.showAlert('{{ success_journal_rejected }}', 'success');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    this.showAlert(data.error || '{{ error_reject_review }}', 'danger');
                }
            })
            .catch(error => {
                this.showLoadingState(false);
                this.showAlert('{{ error_reject_review }}: ' + error.message, 'danger');
            });
        }
    }

    returnReview(reviewId) {
        const reason = prompt('{{ text_return_reason }}:');
        if (reason) {
            this.showLoadingState(true);

            fetch('{{ url_link('accounts/journal_review', 'return') }}', {
                method: 'POST',
                body: JSON.stringify({ review_id: reviewId, reason: reason }),
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                this.showLoadingState(false);
                if (data.success) {
                    this.showAlert('{{ success_journal_returned }}', 'success');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    this.showAlert(data.error || '{{ error_return_review }}', 'danger');
                }
            })
            .catch(error => {
                this.showLoadingState(false);
                this.showAlert('{{ error_return_review }}: ' + error.message, 'danger');
            });
        }
    }

    bulkApprove() {
        const selected = this.getSelectedReviews();
        if (selected.length === 0) {
            this.showAlert('{{ text_no_selection }}', 'warning');
            return;
        }

        if (confirm('{{ text_confirm_bulk_approve }}')) {
            this.showLoadingState(true);

            fetch('{{ url_link('accounts/journal_review', 'bulkApprove') }}', {
                method: 'POST',
                body: JSON.stringify({ selected: selected }),
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                this.showLoadingState(false);
                if (data.success) {
                    this.showAlert('{{ success_bulk_approved }}', 'success');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    this.showAlert(data.error || '{{ error_bulk_approve }}', 'danger');
                }
            })
            .catch(error => {
                this.showLoadingState(false);
                this.showAlert('{{ error_bulk_approve }}: ' + error.message, 'danger');
            });
        }
    }

    bulkReject() {
        const selected = this.getSelectedReviews();
        if (selected.length === 0) {
            this.showAlert('{{ text_no_selection }}', 'warning');
            return;
        }

        const reason = prompt('{{ text_rejection_reason }}:');
        if (reason) {
            this.showLoadingState(true);

            fetch('{{ url_link('accounts/journal_review', 'bulkReject') }}', {
                method: 'POST',
                body: JSON.stringify({ selected: selected, reason: reason }),
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                this.showLoadingState(false);
                if (data.success) {
                    this.showAlert('{{ success_bulk_rejected }}', 'success');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    this.showAlert(data.error || '{{ error_bulk_reject }}', 'danger');
                }
            })
            .catch(error => {
                this.showLoadingState(false);
                this.showAlert('{{ error_bulk_reject }}: ' + error.message, 'danger');
            });
        }
    }

    exportReviews(format) {
        const params = new URLSearchParams({
            format: format,
            filter_status: document.getElementById('filter_status').value,
            filter_reviewer: document.getElementById('filter_reviewer').value,
            filter_priority: document.getElementById('filter_priority').value,
            filter_date_start: document.getElementById('filter_date_start').value,
            filter_date_end: document.getElementById('filter_date_end').value
        });

        this.showAlert('{{ text_exporting }}...', 'info');
        window.open('{{ export_url }}&' + params.toString(), '_blank');
    }

    printReviews() {
        window.print();
    }

    getSelectedReviews() {
        const checkboxes = document.querySelectorAll('input[name="selected[]"]:checked');
        return Array.from(checkboxes).map(cb => cb.value);
    }

    showExportMenu() {
        const exportButton = document.querySelector('.dropdown-toggle');
        if (exportButton) {
            exportButton.click();
        }
    }

    showLoadingState(show) {
        const buttons = document.querySelectorAll('.btn');
        buttons.forEach(btn => {
            if (show) {
                btn.disabled = true;
            } else {
                btn.disabled = false;
            }
        });
    }

    showAlert(message, type) {
        const alertContainer = document.createElement('div');
        alertContainer.className = `alert alert-${type} alert-dismissible fade show`;
        alertContainer.innerHTML = `
            <i class="fa fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'}"></i>
            ${message}
            <button type="button" class="close" data-dismiss="alert">&times;</button>
        `;

        const container = document.querySelector('#content .container-fluid');
        container.insertBefore(alertContainer, container.firstChild);

        setTimeout(() => {
            alertContainer.remove();
        }, 5000);
    }
}

// Global functions for backward compatibility
function addReview() {
    journalReviewManager.addReview();
}

function viewReview(reviewId) {
    journalReviewManager.viewReview(reviewId);
}

function editReview(reviewId) {
    journalReviewManager.editReview(reviewId);
}

function approveReview(reviewId) {
    journalReviewManager.approveReview(reviewId);
}

function rejectReview(reviewId) {
    journalReviewManager.rejectReview(reviewId);
}

function returnReview(reviewId) {
    journalReviewManager.returnReview(reviewId);
}

function bulkApprove() {
    journalReviewManager.bulkApprove();
}

function bulkReject() {
    journalReviewManager.bulkReject();
}

function exportReviews(format) {
    journalReviewManager.exportReviews(format);
}

function printReviews() {
    journalReviewManager.printReviews();
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.journalReviewManager = new JournalReviewManager();
});
</script>

{{ footer }}
