<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/type/color.proto

namespace GPBMetadata\Google\Type;

class Color
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        \GPBMetadata\Google\Protobuf\Wrappers::initOnce();
        $pool->internalAddGeneratedFile(hex2bin(
            "0aef010a17676f6f676c652f747970652f636f6c6f722e70726f746f120b" .
            "676f6f676c652e74797065225d0a05436f6c6f72120b0a03726564180120" .
            "012802120d0a05677265656e180220012802120c0a04626c756518032001" .
            "2802122a0a05616c70686118042001280b321b2e676f6f676c652e70726f" .
            "746f6275662e466c6f617456616c756542600a0f636f6d2e676f6f676c65" .
            "2e74797065420a436f6c6f7250726f746f50015a36676f6f676c652e676f" .
            "6c616e672e6f72672f67656e70726f746f2f676f6f676c65617069732f74" .
            "7970652f636f6c6f723b636f6c6f72f80101a20203475450620670726f74" .
            "6f33"
        ), true);

        static::$is_initialized = true;
    }
}

