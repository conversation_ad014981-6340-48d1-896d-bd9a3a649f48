{"name": "guzzlehttp/promises", "description": "Guzzle promises library", "keywords": ["promise"], "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "require": {"php": ">=5.5"}, "require-dev": {"symfony/phpunit-bridge": "^4.4 || ^5.1"}, "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}, "files": ["src/functions_include.php"]}, "autoload-dev": {"psr-4": {"GuzzleHttp\\Promise\\Tests\\": "tests/"}}, "scripts": {"test": "vendor/bin/simple-phpunit", "test-ci": "vendor/bin/simple-phpunit --coverage-text"}, "config": {"preferred-install": "dist", "sort-packages": true}}