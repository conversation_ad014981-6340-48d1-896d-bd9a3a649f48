{{ header }}{{ column_left }}

{# تقييم العملاء المحتملين - Lead Scoring #}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="button" data-toggle="tooltip" title="{{ button_filter }}" onclick="$('#filter-lead').toggleClass('hidden');" class="btn btn-default visible-xs">
          <i class="fa fa-filter"></i>
        </button>
        <a href="{{ add }}" data-toggle="tooltip" title="{{ button_add }}" class="btn btn-primary">
          <i class="fa fa-plus"></i>
        </a>
        <button type="button" data-toggle="tooltip" title="{{ button_delete }}" onclick="confirm('{{ text_confirm }}') ? $('#form-lead').submit() : false;" class="btn btn-danger">
          <i class="fa fa-trash"></i>
        </button>
        <button type="button" data-toggle="tooltip" title="{{ button_export }}" onclick="exportLeads();" class="btn btn-success">
          <i class="fa fa-download"></i>
        </button>
        <button type="button" data-toggle="tooltip" title="{{ button_import }}" onclick="$('#modal-import').modal('show');" class="btn btn-info">
          <i class="fa fa-upload"></i>
        </button>
      </div>
      <h1>{{ heading_title }}</h1>
      <ol class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
          <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ol>
    </div>
  </div>

  <div class="container-fluid">
    {# إحصائيات سريعة #}
    <div class="row">
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-primary">
          <div class="panel-body text-center">
            <div class="row">
              <div class="col-xs-8">
                <h4>{{ statistics.total_leads }}</h4>
                <p>{{ text_total_leads }}</p>
              </div>
              <div class="col-xs-4">
                <i class="fa fa-users fa-2x"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-success">
          <div class="panel-body text-center">
            <div class="row">
              <div class="col-xs-8">
                <h4>{{ statistics.high_score_leads }}</h4>
                <p>{{ text_high_score_leads }}</p>
              </div>
              <div class="col-xs-4">
                <i class="fa fa-star fa-2x"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-warning">
          <div class="panel-body text-center">
            <div class="row">
              <div class="col-xs-8">
                <h4>{{ statistics.avg_score }}%</h4>
                <p>{{ text_avg_score }}</p>
              </div>
              <div class="col-xs-4">
                <i class="fa fa-line-chart fa-2x"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-info">
          <div class="panel-body text-center">
            <div class="row">
              <div class="col-xs-8">
                <h4>{{ statistics.conversion_rate }}%</h4>
                <p>{{ text_conversion_rate }}</p>
              </div>
              <div class="col-xs-4">
                <i class="fa fa-exchange fa-2x"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    {# الرسوم البيانية #}
    <div class="row">
      <div class="col-lg-6">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title">
              <i class="fa fa-pie-chart"></i>
              {{ text_score_distribution }}
            </h3>
          </div>
          <div class="panel-body">
            <canvas id="scoreDistributionChart" height="300"></canvas>
          </div>
        </div>
      </div>
      <div class="col-lg-6">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title">
              <i class="fa fa-line-chart"></i>
              {{ text_score_trend }}
            </h3>
          </div>
          <div class="panel-body">
            <canvas id="scoreTrendChart" height="300"></canvas>
          </div>
        </div>
      </div>
    </div>

    {# الفلاتر المتقدمة #}
    <div class="panel panel-default hidden-xs" id="filter-lead">
      <div class="panel-heading">
        <h3 class="panel-title">
          <i class="fa fa-filter"></i>
          {{ text_filter }}
        </h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-lg-3">
            <div class="form-group">
              <label for="input-name" class="control-label">{{ entry_name }}</label>
              <input type="text" name="filter_name" value="{{ filter_name }}" placeholder="{{ entry_name }}" id="input-name" class="form-control">
            </div>
          </div>
          <div class="col-lg-3">
            <div class="form-group">
              <label for="input-email" class="control-label">{{ entry_email }}</label>
              <input type="text" name="filter_email" value="{{ filter_email }}" placeholder="{{ entry_email }}" id="input-email" class="form-control">
            </div>
          </div>
          <div class="col-lg-3">
            <div class="form-group">
              <label for="input-company" class="control-label">{{ entry_company }}</label>
              <input type="text" name="filter_company" value="{{ filter_company }}" placeholder="{{ entry_company }}" id="input-company" class="form-control">
            </div>
          </div>
          <div class="col-lg-3">
            <div class="form-group">
              <label for="input-source" class="control-label">{{ entry_source }}</label>
              <select name="filter_source" id="input-source" class="form-control">
                <option value="">{{ text_all_sources }}</option>
                {% for source in sources %}
                  <option value="{{ source.value }}"{% if source.value == filter_source %} selected{% endif %}>{{ source.text }}</option>
                {% endfor %}
              </select>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-lg-3">
            <div class="form-group">
              <label for="input-status" class="control-label">{{ entry_status }}</label>
              <select name="filter_status" id="input-status" class="form-control">
                <option value="">{{ text_all_statuses }}</option>
                {% for status in statuses %}
                  <option value="{{ status.value }}"{% if status.value == filter_status %} selected{% endif %}>{{ status.text }}</option>
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="col-lg-3">
            <div class="form-group">
              <label for="input-score-range" class="control-label">{{ entry_score_range }}</label>
              <select name="filter_score_range" id="input-score-range" class="form-control">
                <option value="">{{ text_all_scores }}</option>
                <option value="0-25"{% if filter_score_range == '0-25' %} selected{% endif %}>0-25 ({{ text_low }})</option>
                <option value="26-50"{% if filter_score_range == '26-50' %} selected{% endif %}>26-50 ({{ text_medium_low }})</option>
                <option value="51-75"{% if filter_score_range == '51-75' %} selected{% endif %}>51-75 ({{ text_medium_high }})</option>
                <option value="76-100"{% if filter_score_range == '76-100' %} selected{% endif %}>76-100 ({{ text_high }})</option>
              </select>
            </div>
          </div>
          <div class="col-lg-3">
            <div class="form-group">
              <label for="input-date-from" class="control-label">{{ entry_date_from }}</label>
              <input type="date" name="filter_date_from" value="{{ filter_date_from }}" id="input-date-from" class="form-control">
            </div>
          </div>
          <div class="col-lg-3">
            <div class="form-group">
              <label for="input-date-to" class="control-label">{{ entry_date_to }}</label>
              <input type="date" name="filter_date_to" value="{{ filter_date_to }}" id="input-date-to" class="form-control">
            </div>
          </div>
        </div>
        <div class="text-right">
          <button type="button" id="button-filter" class="btn btn-default">
            <i class="fa fa-filter"></i> {{ button_filter }}
          </button>
          <button type="button" onclick="filter();" class="btn btn-primary">
            <i class="fa fa-search"></i> {{ button_search }}
          </button>
        </div>
      </div>
    </div>

    {# جدول العملاء المحتملين #}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title">
          <i class="fa fa-list"></i>
          {{ text_list }}
        </h3>
        <div class="pull-right">
          <div class="input-group input-group-sm" style="width: 200px;">
            <input type="text" name="search" class="form-control" placeholder="{{ text_search }}" value="{{ search }}">
            <span class="input-group-btn">
              <button type="submit" class="btn btn-default">
                <i class="fa fa-search"></i>
              </button>
            </span>
          </div>
        </div>
      </div>
      <div class="panel-body table-responsive" style="padding: 0;">
        <form id="form-lead" method="post" data-oc-toggle="ajax" data-oc-load="{{ action }}" data-oc-target="#content">
          <table class="table table-hover text-nowrap">
            <thead>
              <tr>
                <th style="width: 1px;" class="text-center">
                  <input type="checkbox" onclick="$('input[name*=\'selected\']').prop('checked', $(this).prop('checked'));">
                </th>
                <th>
                  <a href="{{ sort_name }}">
                    {{ column_name }}
                    {% if sort == 'customer_name' %}
                      <i class="fa fa-sort-{{ order|lower == 'desc' ? 'desc' : 'asc' }}"></i>
                    {% endif %}
                  </a>
                </th>
                <th>
                  <a href="{{ sort_email }}">
                    {{ column_email }}
                    {% if sort == 'email' %}
                      <i class="fa fa-sort-{{ order|lower == 'desc' ? 'desc' : 'asc' }}"></i>
                    {% endif %}
                  </a>
                </th>
                <th>{{ column_company }}</th>
                <th>{{ column_source }}</th>
                <th>
                  <a href="{{ sort_score }}">
                    {{ column_total_score }}
                    {% if sort == 'total_score' %}
                      <i class="fa fa-sort-{{ order|lower == 'desc' ? 'desc' : 'asc' }}"></i>
                    {% endif %}
                  </a>
                </th>
                <th>{{ column_priority }}</th>
                <th>{{ column_status }}</th>
                <th>
                  <a href="{{ sort_date }}">
                    {{ column_date_created }}
                    {% if sort == 'date_created' %}
                      <i class="fa fa-sort-{{ order|lower == 'desc' ? 'desc' : 'asc' }}"></i>
                    {% endif %}
                  </a>
                </th>
                <th class="text-right">{{ column_action }}</th>
              </tr>
            </thead>
            <tbody>
              {% if leads %}
                {% for lead in leads %}
                  <tr>
                    <td class="text-center">
                      <input type="checkbox" name="selected[]" value="{{ lead.lead_id }}">
                    </td>
                    <td>
                      <div class="d-flex align-items-center">
                        <div class="avatar-sm me-2">
                          <div class="avatar-title rounded-circle bg-{{ lead.priority_color }}">
                            {{ lead.customer_name|slice(0, 1)|upper }}
                          </div>
                        </div>
                        <div>
                          <strong>{{ lead.customer_name }}</strong>
                          {% if lead.phone %}
                            <br><small class="text-muted">{{ lead.phone }}</small>
                          {% endif %}
                        </div>
                      </div>
                    </td>
                    <td>{{ lead.email }}</td>
                    <td>{{ lead.company }}</td>
                    <td>
                      <span class="badge bg-{{ lead.source_color }}">{{ lead.source }}</span>
                    </td>
                    <td>
                      <div class="d-flex align-items-center">
                        <div class="progress me-2" style="width: 60px; height: 8px;">
                          <div class="progress-bar bg-{{ lead.score_color }}" style="width: {{ lead.total_score }}%"></div>
                        </div>
                        <span class="fw-bold">{{ lead.total_score }}%</span>
                      </div>
                    </td>
                    <td>
                      <span class="badge bg-{{ lead.priority_color }}">{{ lead.priority }}</span>
                    </td>
                    <td>
                      <span class="badge bg-{{ lead.status_color }}">{{ lead.status }}</span>
                    </td>
                    <td>{{ lead.date_created }}</td>
                    <td class="text-right">
                      <div class="btn-group">
                        <button type="button" class="btn btn-sm btn-default dropdown-toggle" data-toggle="dropdown">
                          <i class="fa fa-cog"></i> <span class="caret"></span>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-right">
                          <li><a href="{{ lead.view }}"><i class="fa fa-eye"></i> {{ text_view }}</a></li>
                          <li><a href="{{ lead.edit }}"><i class="fa fa-edit"></i> {{ text_edit }}</a></li>
                          <li class="divider"></li>
                          <li><a href="javascript:void(0);" onclick="updateScore({{ lead.lead_id }});"><i class="fa fa-calculator"></i> {{ text_update_score }}</a></li>
                          <li><a href="javascript:void(0);" onclick="convertLead({{ lead.lead_id }});"><i class="fa fa-exchange"></i> {{ text_convert }}</a></li>
                          <li class="divider"></li>
                          <li><a href="javascript:void(0);" onclick="deleteLead({{ lead.lead_id }});" class="text-danger"><i class="fa fa-trash"></i> {{ text_delete }}</a></li>
                        </ul>
                      </div>
                    </td>
                  </tr>
                {% endfor %}
              {% else %}
                <tr>
                  <td class="text-center" colspan="10">{{ text_no_results }}</td>
                </tr>
              {% endif %}
            </tbody>
          </table>
        </form>
      </div>
      <div class="panel-footer">
        <div class="row">
          <div class="col-sm-6 text-left">{{ pagination }}</div>
          <div class="col-sm-6 text-right">{{ results }}</div>
        </div>
      </div>
    </div>
  </div>
</div>

{# مودال تحديث النقاط #}
<div class="modal fade" id="modal-update-score" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal"><span>&times;</span></button>
        <h4 class="modal-title">{{ text_update_score }}</h4>
      </div>
      <div class="modal-body">
        <form id="form-update-score">
          <input type="hidden" name="lead_id" id="update-lead-id">
          <div class="form-group">
            <label class="control-label">{{ text_recalculate_score }}</label>
            <p class="text-muted">{{ text_recalculate_help }}</p>
            <button type="button" class="btn btn-primary" onclick="recalculateScore();">
              <i class="fa fa-calculator"></i> {{ button_recalculate }}
            </button>
          </div>
          <div class="form-group">
            <label for="manual-score" class="control-label">{{ text_manual_score }}</label>
            <input type="number" name="manual_score" id="manual-score" class="form-control" min="0" max="100" placeholder="{{ text_enter_score }}">
            <p class="help-block">{{ text_manual_score_help }}</p>
          </div>
          <div class="form-group">
            <label for="score-notes" class="control-label">{{ text_notes }}</label>
            <textarea name="notes" id="score-notes" class="form-control" rows="3" placeholder="{{ text_score_notes_placeholder }}"></textarea>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_cancel }}</button>
        <button type="button" class="btn btn-primary" onclick="saveScore();">{{ button_save }}</button>
      </div>
    </div>
  </div>
</div>

{# مودال تحويل العميل المحتمل #}
<div class="modal fade" id="modal-convert-lead" tabindex="-1">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal"><span>&times;</span></button>
        <h4 class="modal-title">{{ text_convert_lead }}</h4>
      </div>
      <div class="modal-body">
        <form id="form-convert-lead">
          <input type="hidden" name="lead_id" id="convert-lead-id">
          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label for="customer-group" class="control-label">{{ text_customer_group }}</label>
                <select name="customer_group_id" id="customer-group" class="form-control">
                  {% for group in customer_groups %}
                    <option value="{{ group.customer_group_id }}">{{ group.name }}</option>
                  {% endfor %}
                </select>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label for="customer-status" class="control-label">{{ text_customer_status }}</label>
                <select name="status" id="customer-status" class="form-control">
                  <option value="1">{{ text_enabled }}</option>
                  <option value="0">{{ text_disabled }}</option>
                </select>
              </div>
            </div>
          </div>
          <div class="form-group">
            <label for="conversion-notes" class="control-label">{{ text_conversion_notes }}</label>
            <textarea name="conversion_notes" id="conversion-notes" class="form-control" rows="3" placeholder="{{ text_conversion_notes_placeholder }}"></textarea>
          </div>
          <div class="form-group">
            <div class="checkbox">
              <label>
                <input type="checkbox" name="send_welcome_email" id="send-welcome-email" checked>
                {{ text_send_welcome_email }}
              </label>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_cancel }}</button>
        <button type="button" class="btn btn-success" onclick="convertLeadConfirm();">
          <i class="fa fa-exchange"></i> {{ button_convert }}
        </button>
      </div>
    </div>
  </div>
</div>

{# مودال الاستيراد #}
<div class="modal fade" id="modal-import" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title">{{ text_import_leads }}</h4>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <div class="modal-body">
        <form id="form-import" enctype="multipart/form-data">
          <div class="mb-3">
            <label for="import-file" class="form-label">{{ text_select_file }}</label>
            <input type="file" name="import_file" id="import-file" class="form-control" accept=".csv,.xlsx,.xls">
            <div class="form-text">{{ text_import_help }}</div>
          </div>
          <div class="mb-3">
            <div class="form-check">
              <input type="checkbox" name="update_existing" id="update-existing" class="form-check-input">
              <label for="update-existing" class="form-check-label">{{ text_update_existing }}</label>
            </div>
          </div>
          <div class="mb-3">
            <div class="form-check">
              <input type="checkbox" name="calculate_scores" id="calculate-scores" class="form-check-input" checked>
              <label for="calculate-scores" class="form-check-label">{{ text_calculate_scores }}</label>
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ button_cancel }}</button>
        <button type="button" class="btn btn-primary" onclick="importLeads();">
          <i class="fas fa-upload"></i> {{ button_import }}
        </button>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript">
// تهيئة الرسوم البيانية
$(document).ready(function() {
    // رسم بياني لتوزيع النقاط
    var scoreDistributionCtx = document.getElementById('scoreDistributionChart').getContext('2d');
    var scoreDistributionChart = new Chart(scoreDistributionCtx, {
        type: 'doughnut',
        data: {
            labels: [
                '{{ text_low }} (0-25)',
                '{{ text_medium_low }} (26-50)',
                '{{ text_medium_high }} (51-75)',
                '{{ text_high }} (76-100)'
            ],
            datasets: [{
                data: [
                    {{ score_distribution.low }},
                    {{ score_distribution.medium_low }},
                    {{ score_distribution.medium_high }},
                    {{ score_distribution.high }}
                ],
                backgroundColor: [
                    '#dc3545',
                    '#ffc107',
                    '#17a2b8',
                    '#28a745'
                ],
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // رسم بياني لاتجاه النقاط
    var scoreTrendCtx = document.getElementById('scoreTrendChart').getContext('2d');
    var scoreTrendChart = new Chart(scoreTrendCtx, {
        type: 'line',
        data: {
            labels: [
                {% for trend in score_trend %}
                    '{{ trend.date }}'{% if not loop.last %},{% endif %}
                {% endfor %}
            ],
            datasets: [{
                label: '{{ text_avg_score }}',
                data: [
                    {% for trend in score_trend %}
                        {{ trend.avg_score }}{% if not loop.last %},{% endif %}
                    {% endfor %}
                ],
                borderColor: '#007bff',
                backgroundColor: 'rgba(0, 123, 255, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 100
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });

    // تهيئة التلميحات
    $('[data-toggle="tooltip"]').tooltip();
});

// وظائف الفلترة والبحث
function filter() {
    var url = 'index.php?route=crm/lead_scoring&user_token={{ user_token }}';

    var filter_name = $('input[name=\'filter_name\']').val();
    if (filter_name) {
        url += '&filter_name=' + encodeURIComponent(filter_name);
    }

    var filter_email = $('input[name=\'filter_email\']').val();
    if (filter_email) {
        url += '&filter_email=' + encodeURIComponent(filter_email);
    }

    var filter_company = $('input[name=\'filter_company\']').val();
    if (filter_company) {
        url += '&filter_company=' + encodeURIComponent(filter_company);
    }

    var filter_source = $('select[name=\'filter_source\']').val();
    if (filter_source) {
        url += '&filter_source=' + encodeURIComponent(filter_source);
    }

    var filter_status = $('select[name=\'filter_status\']').val();
    if (filter_status) {
        url += '&filter_status=' + encodeURIComponent(filter_status);
    }

    var filter_score_range = $('select[name=\'filter_score_range\']').val();
    if (filter_score_range) {
        url += '&filter_score_range=' + encodeURIComponent(filter_score_range);
    }

    var filter_date_from = $('input[name=\'filter_date_from\']').val();
    if (filter_date_from) {
        url += '&filter_date_from=' + encodeURIComponent(filter_date_from);
    }

    var filter_date_to = $('input[name=\'filter_date_to\']').val();
    if (filter_date_to) {
        url += '&filter_date_to=' + encodeURIComponent(filter_date_to);
    }

    location = url;
}

// تحديث النقاط
function updateScore(lead_id) {
    $('#update-lead-id').val(lead_id);
    $('#modal-update-score').modal('show');
}

function recalculateScore() {
    var lead_id = $('#update-lead-id').val();

    $.ajax({
        url: 'index.php?route=crm/lead_scoring/recalculate&user_token={{ user_token }}',
        type: 'post',
        data: {lead_id: lead_id},
        dataType: 'json',
        beforeSend: function() {
            $('#button-recalculate').prop('disabled', true);
        },
        complete: function() {
            $('#button-recalculate').prop('disabled', false);
        },
        success: function(json) {
            if (json['success']) {
                $('#alert').prepend('<div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> ' + json['success'] + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
                location.reload();
            }

            if (json['error']) {
                $('#alert').prepend('<div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> ' + json['error'] + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
            }
        },
        error: function(xhr, ajaxOptions, thrownError) {
            console.log(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
    });
}

function saveScore() {
    var lead_id = $('#update-lead-id').val();
    var manual_score = $('#manual-score').val();
    var notes = $('#score-notes').val();

    if (!manual_score) {
        alert('{{ error_manual_score_required }}');
        return;
    }

    $.ajax({
        url: 'index.php?route=crm/lead_scoring/updateScore&user_token={{ user_token }}',
        type: 'post',
        data: {
            lead_id: lead_id,
            manual_score: manual_score,
            notes: notes
        },
        dataType: 'json',
        beforeSend: function() {
            $('#modal-update-score .btn-primary').prop('disabled', true);
        },
        complete: function() {
            $('#modal-update-score .btn-primary').prop('disabled', false);
        },
        success: function(json) {
            if (json['success']) {
                $('#alert').prepend('<div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> ' + json['success'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
                $('#modal-update-score').modal('hide');
                location.reload();
            }

            if (json['error']) {
                $('#alert').prepend('<div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> ' + json['error'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
            }
        },
        error: function(xhr, ajaxOptions, thrownError) {
            console.log(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
    });
}

// تحويل العميل المحتمل
function convertLead(lead_id) {
    $('#convert-lead-id').val(lead_id);
    $('#modal-convert-lead').modal('show');
}

function convertLeadConfirm() {
    var lead_id = $('#convert-lead-id').val();
    var customer_group_id = $('#customer-group').val();
    var status = $('#customer-status').val();
    var conversion_notes = $('#conversion-notes').val();
    var send_welcome_email = $('#send-welcome-email').is(':checked') ? 1 : 0;

    $.ajax({
        url: 'index.php?route=crm/lead_scoring/convert&user_token={{ user_token }}',
        type: 'post',
        data: {
            lead_id: lead_id,
            customer_group_id: customer_group_id,
            status: status,
            conversion_notes: conversion_notes,
            send_welcome_email: send_welcome_email
        },
        dataType: 'json',
        beforeSend: function() {
            $('#modal-convert-lead .btn-success').prop('disabled', true);
        },
        complete: function() {
            $('#modal-convert-lead .btn-success').prop('disabled', false);
        },
        success: function(json) {
            if (json['success']) {
                $('#alert').prepend('<div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> ' + json['success'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
                $('#modal-convert-lead').modal('hide');
                location.reload();
            }

            if (json['error']) {
                $('#alert').prepend('<div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> ' + json['error'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
            }
        },
        error: function(xhr, ajaxOptions, thrownError) {
            console.log(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
    });
}

// حذف العميل المحتمل
function deleteLead(lead_id) {
    if (confirm('{{ text_confirm_delete }}')) {
        $.ajax({
            url: 'index.php?route=crm/lead_scoring/delete&user_token={{ user_token }}',
            type: 'post',
            data: {lead_id: lead_id},
            dataType: 'json',
            success: function(json) {
                if (json['success']) {
                    $('#alert').prepend('<div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> ' + json['success'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
                    location.reload();
                }

                if (json['error']) {
                    $('#alert').prepend('<div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> ' + json['error'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
                }
            },
            error: function(xhr, ajaxOptions, thrownError) {
                console.log(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
            }
        });
    }
}

// تصدير البيانات
function exportLeads() {
    var url = 'index.php?route=crm/lead_scoring/export&user_token={{ user_token }}';
    window.open(url, '_blank');
}

// استيراد البيانات
function importLeads() {
    var formData = new FormData($('#form-import')[0]);

    $.ajax({
        url: 'index.php?route=crm/lead_scoring/import&user_token={{ user_token }}',
        type: 'post',
        data: formData,
        processData: false,
        contentType: false,
        dataType: 'json',
        beforeSend: function() {
            $('#modal-import .btn-primary').prop('disabled', true);
        },
        complete: function() {
            $('#modal-import .btn-primary').prop('disabled', false);
        },
        success: function(json) {
            if (json['success']) {
                $('#alert').prepend('<div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> ' + json['success'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
                $('#modal-import').modal('hide');
                location.reload();
            }

            if (json['error']) {
                $('#alert').prepend('<div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> ' + json['error'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
            }
        },
        error: function(xhr, ajaxOptions, thrownError) {
            console.log(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
    });
}

// تطبيق الفلاتر عند الضغط على Enter
$('#filter-lead input').keydown(function(e) {
    if (e.keyCode == 13) {
        filter();
    }
});

// إعادة تعيين الفلاتر
$('#button-filter').click(function() {
    $('input[name=\'filter_name\']').val('');
    $('input[name=\'filter_email\']').val('');
    $('input[name=\'filter_company\']').val('');
    $('select[name=\'filter_source\']').val('');
    $('select[name=\'filter_status\']').val('');
    $('select[name=\'filter_score_range\']').val('');
    $('input[name=\'filter_date_from\']').val('');
    $('input[name=\'filter_date_to\']').val('');

    filter();
});
</script>

{{ footer }}
