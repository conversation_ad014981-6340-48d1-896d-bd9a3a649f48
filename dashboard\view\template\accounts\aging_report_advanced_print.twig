<!DOCTYPE html>
<html dir="{{ direction }}" lang="{{ lang }}" class="no-js">
<head>
<meta charset="UTF-8" />
<title>{{ heading_title }}</title>
<base href="{{ base }}" />
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
<link href="view/stylesheet/bootstrap.css" rel="stylesheet" media="screen" />
<style>
@media print {
  body { margin: 0; }
  .no-print { display: none !important; }
  .page-break { page-break-before: always; }
}

body {
  font-family: Arial, sans-serif;
  font-size: 11px;
  line-height: 1.3;
}

.header {
  text-align: center;
  margin-bottom: 30px;
  border-bottom: 2px solid #333;
  padding-bottom: 20px;
}

.company-logo {
  max-height: 80px;
  margin-bottom: 10px;
}

.company-name {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}

.report-title {
  font-size: 18px;
  font-weight: bold;
  color: #666;
  margin-bottom: 10px;
}

.report-info {
  font-size: 10px;
  color: #888;
}

.summary-box {
  background-color: #f8f9fa;
  padding: 15px;
  margin-bottom: 20px;
  border: 1px solid #dee2e6;
}

.risk-analysis-box {
  background-color: #fff3cd;
  padding: 15px;
  margin-bottom: 20px;
  border-left: 4px solid #ffc107;
}

.aging-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 20px;
  font-size: 10px;
}

.aging-table th,
.aging-table td {
  border: 1px solid #dee2e6;
  padding: 6px;
  text-align: left;
}

.aging-table th {
  background-color: #f8f9fa;
  font-weight: bold;
}

.text-right {
  text-align: right;
}

.text-center {
  text-align: center;
}

.text-success {
  color: #28a745;
}

.text-warning {
  color: #ffc107;
}

.text-danger {
  color: #dc3545;
}

.text-info {
  color: #17a2b8;
}

.footer {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #dee2e6;
  font-size: 9px;
  color: #888;
}

.signature-section {
  margin-top: 40px;
  display: flex;
  justify-content: space-between;
}

.signature-box {
  width: 200px;
  text-align: center;
}

.signature-line {
  border-top: 1px solid #333;
  margin-top: 50px;
  padding-top: 5px;
}

.watermark {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(-45deg);
  font-size: 72px;
  color: rgba(0,0,0,0.05);
  z-index: -1;
  pointer-events: none;
}

.risk-badge {
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 9px;
  font-weight: bold;
}

.risk-critical {
  background-color: #dc3545;
  color: white;
}

.risk-high {
  background-color: #ffc107;
  color: black;
}

.risk-medium {
  background-color: #17a2b8;
  color: white;
}

.risk-low {
  background-color: #28a745;
  color: white;
}
</style>
</head>
<body>

<!-- Watermark -->
<div class="watermark">{{ text_confidential }}</div>

<!-- Header -->
<div class="header">
  {% if company_logo %}
  <img src="{{ company_logo }}" alt="{{ company_name }}" class="company-logo">
  {% endif %}
  <div class="company-name">{{ company_name }}</div>
  <div class="report-title">{{ heading_title }}</div>
  <div class="report-info">
    {{ text_generated_on }}: {{ generated_date }} | {{ text_generated_by }}: {{ generated_by }}
  </div>
</div>

<!-- Report Information -->
<div class="summary-box">
  <h3 style="margin-top: 0;">{{ text_report_info }}</h3>
  <div style="display: flex; justify-content: space-between;">
    <div>
      <strong>{{ text_report_type }}:</strong> {{ aging_data.filter_data.report_type_name }}<br>
      <strong>{{ text_report_date }}:</strong> {{ aging_data.filter_data.date_end }}<br>
      <strong>{{ text_currency }}:</strong> {{ aging_data.filter_data.currency }}
    </div>
    <div>
      <strong>{{ text_aging_periods }}:</strong> {{ aging_data.filter_data.aging_periods }}<br>
      <strong>{{ text_total_entities }}:</strong> {{ aging_data.summary.total_entities }}<br>
      <strong>{{ text_total_amount }}:</strong> {{ aging_data.summary.total }}
    </div>
  </div>
</div>

<!-- Summary -->
<h3>{{ text_summary }}</h3>
<table class="aging-table">
  <thead>
    <tr>
      <th>{{ text_period }}</th>
      <th class="text-right">{{ text_amount }}</th>
      <th class="text-right">{{ text_percentage }}</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>{{ text_period_current }}</td>
      <td class="text-right">{{ aging_data.summary.current }}</td>
      <td class="text-right">{{ aging_data.summary.current_percentage }}%</td>
    </tr>
    <tr>
      <td>{{ text_period_1_30 }}</td>
      <td class="text-right">{{ aging_data.summary.period_1_30 }}</td>
      <td class="text-right">{{ aging_data.summary.period_1_30_percentage }}%</td>
    </tr>
    <tr>
      <td>{{ text_period_31_60 }}</td>
      <td class="text-right">{{ aging_data.summary.period_31_60 }}</td>
      <td class="text-right">{{ aging_data.summary.period_31_60_percentage }}%</td>
    </tr>
    <tr>
      <td>{{ text_period_61_90 }}</td>
      <td class="text-right">{{ aging_data.summary.period_61_90 }}</td>
      <td class="text-right">{{ aging_data.summary.period_61_90_percentage }}%</td>
    </tr>
    <tr>
      <td>{{ text_period_over_90 }}</td>
      <td class="text-right">{{ aging_data.summary.period_over_90 }}</td>
      <td class="text-right">{{ aging_data.summary.period_over_90_percentage }}%</td>
    </tr>
  </tbody>
  <tfoot>
    <tr style="background-color: #e2e3e5; font-weight: bold;">
      <td>{{ text_total }}</td>
      <td class="text-right">{{ aging_data.summary.total }}</td>
      <td class="text-right">100%</td>
    </tr>
  </tfoot>
</table>

<!-- Risk Analysis -->
{% if risk_analysis %}
<div class="risk-analysis-box">
  <h3 style="margin-top: 0;">{{ text_ai_analysis }}</h3>
  <div style="display: flex; justify-content: space-between; margin-bottom: 15px;">
    <div>
      <strong>{{ text_overall_risk }}:</strong> {{ risk_analysis.overall_risk_score }}%<br>
      <strong>{{ text_risk_critical }}:</strong> {{ risk_analysis.critical_risk_entities|length }}<br>
      <strong>{{ text_risk_high }}:</strong> {{ risk_analysis.high_risk_entities|length }}
    </div>
    <div>
      <strong>{{ text_risk_medium }}:</strong> {{ risk_analysis.medium_risk_entities|length }}<br>
      <strong>{{ text_risk_low }}:</strong> {{ risk_analysis.low_risk_entities|length }}<br>
      <strong>{{ text_total_risk_amount }}:</strong> {{ risk_analysis.total_critical_amount }}
    </div>
  </div>
  
  <h4>{{ text_recommendations }}</h4>
  <ul style="margin: 0; padding-left: 20px;">
    {% for recommendation in risk_analysis.recommendations %}
    <li>{{ recommendation }}</li>
    {% endfor %}
  </ul>
</div>
{% endif %}

<!-- Detailed Report -->
<h3>{{ text_detailed_summary }}</h3>
<table class="aging-table">
  <thead>
    <tr>
      <th style="width: 20%;">{{ column_entity }}</th>
      <th style="width: 12%;" class="text-right">{{ column_current }}</th>
      <th style="width: 12%;" class="text-right">{{ column_1_30 }}</th>
      <th style="width: 12%;" class="text-right">{{ column_31_60 }}</th>
      <th style="width: 12%;" class="text-right">{{ column_61_90 }}</th>
      <th style="width: 12%;" class="text-right">{{ column_over_90 }}</th>
      <th style="width: 12%;" class="text-right">{{ column_total }}</th>
      <th style="width: 8%;" class="text-center">{{ column_risk_score }}</th>
    </tr>
  </thead>
  <tbody>
    {% for entity in aging_data.details %}
    <tr>
      <td>
        <strong>{{ entity.name }}</strong><br>
        <small style="color: #666;">{{ entity.code }}</small>
      </td>
      <td class="text-right">{{ entity.current }}</td>
      <td class="text-right{% if entity.period_1_30_value > 0 %} text-warning{% endif %}">{{ entity.period_1_30 }}</td>
      <td class="text-right{% if entity.period_31_60_value > 0 %} text-warning{% endif %}">{{ entity.period_31_60 }}</td>
      <td class="text-right{% if entity.period_61_90_value > 0 %} text-danger{% endif %}">{{ entity.period_61_90 }}</td>
      <td class="text-right{% if entity.period_over_90_value > 0 %} text-danger{% endif %}">{{ entity.period_over_90 }}</td>
      <td class="text-right"><strong>{{ entity.total }}</strong></td>
      <td class="text-center">
        <span class="risk-badge risk-{% if entity.risk_score >= 80 %}critical{% elseif entity.risk_score >= 60 %}high{% elseif entity.risk_score >= 40 %}medium{% else %}low{% endif %}">
          {{ entity.risk_score }}
        </span>
      </td>
    </tr>
    {% endfor %}
  </tbody>
  <tfoot>
    <tr style="background-color: #e2e3e5; font-weight: bold;">
      <td>{{ text_total }}</td>
      <td class="text-right">{{ aging_data.summary.current }}</td>
      <td class="text-right">{{ aging_data.summary.period_1_30 }}</td>
      <td class="text-right">{{ aging_data.summary.period_31_60 }}</td>
      <td class="text-right">{{ aging_data.summary.period_61_90 }}</td>
      <td class="text-right">{{ aging_data.summary.period_over_90 }}</td>
      <td class="text-right">{{ aging_data.summary.total }}</td>
      <td class="text-center">-</td>
    </tr>
  </tfoot>
</table>

<!-- Critical Risk Entities (if any) -->
{% if risk_analysis and risk_analysis.critical_risk_entities %}
<div class="page-break"></div>
<h3 style="color: #dc3545;">{{ text_critical_alerts }}</h3>
<table class="aging-table">
  <thead>
    <tr>
      <th>{{ column_entity }}</th>
      <th class="text-right">{{ text_total_amount }}</th>
      <th class="text-right">{{ text_overdue_amount }}</th>
      <th class="text-center">{{ column_risk_score }}</th>
      <th>{{ text_recommendations }}</th>
    </tr>
  </thead>
  <tbody>
    {% for entity in risk_analysis.critical_risk_entities %}
    <tr>
      <td><strong>{{ entity.name }}</strong></td>
      <td class="text-right">{{ entity.total }}</td>
      <td class="text-right text-danger">{{ entity.overdue_amount }}</td>
      <td class="text-center">
        <span class="risk-badge risk-critical">{{ entity.risk_score }}</span>
      </td>
      <td>{{ entity.recommendation }}</td>
    </tr>
    {% endfor %}
  </tbody>
</table>
{% endif %}

<!-- Signatures -->
<div class="signature-section">
  <div class="signature-box">
    <div class="signature-line">{{ text_prepared_by }}</div>
  </div>
  <div class="signature-box">
    <div class="signature-line">{{ text_reviewed_by }}</div>
  </div>
  <div class="signature-box">
    <div class="signature-line">{{ text_approved_by }}</div>
  </div>
</div>

<!-- Footer -->
<div class="footer">
  <div style="display: flex; justify-content: space-between;">
    <div>
      {{ text_confidential }} - {{ text_internal_use }}
    </div>
    <div>
      {{ text_eas_compliant }} | {{ text_eta_ready }} | {{ text_risk_management }}
    </div>
    <div>
      {{ text_page }} 1 {{ text_of }} 1
    </div>
  </div>
</div>

<script>
// Auto print when page loads
window.onload = function() {
  window.print();
};
</script>

</body>
</html>
