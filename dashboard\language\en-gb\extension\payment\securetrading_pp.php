<?php
$_['heading_title']                              = 'Secure Trading Payment Pages';

$_['text_securetrading_pp']                      = '<a href="http://www.securetradingfs.com/partner/open-cart/" target="_blank"><img src="view/image/payment/secure_trading.png" alt="Secure Trading" title="Secure Trading" style="border: 1px solid #EEEEEE;" /></a>';
$_['text_extension']                             = 'Extensions';
$_['text_all_geo_zones']                         = 'All Geo Zones';
$_['text_process_immediately']                   = 'Process immediately';
$_['text_wait_x_days']                           = 'Wait %d days';
$_['text_success']                               = 'Success: You have modified Secure Trading module!';
$_['text_pending_settlement']                    = 'Pending Settlement';
$_['text_authorisation_reversed']                = 'Authorisation was successfully reversed';
$_['text_refund_issued']                         = 'Refund was successfully issued';
$_['text_pending_settlement_manually_overriden'] = 'Pending Settlement, manually overriden';
$_['text_pending_suspended']                     = 'Suspended';
$_['text_pending_settled']                       = 'Settled';
$_['text_reversed']                              = 'Reversed';
$_['text_payment']                               = "Payment";
$_['text_rebate']                                = 'Rebate';

$_['entry_site_reference']                       = 'Site reference';
$_['entry_version']                              = 'Version';
$_['entry_username']                             = 'Username';
$_['entry_password']                             = 'Password';
$_['entry_site_security_status']                 = 'Use Site Security hash';
$_['entry_site_security_password']               = 'Site Security password';
$_['entry_notification_password']                = 'Notification password';
$_['entry_order_status']                         = 'Order status';
$_['entry_declined_order_status']                = 'Declined order status';
$_['entry_refunded_order_status']                = 'Refunded order status';
$_['entry_authorisation_reversed_order_status']  = 'Authorisation reversed order status';
$_['entry_settle_status']                        = 'Settlement status';
$_['entry_settle_due_date']                      = 'Settlement due date';
$_['entry_geo_zone']                             = 'Geo Zone';
$_['entry_sort_order']                           = 'Sort Order';
$_['entry_status']                               = 'Status';
$_['entry_total']                                = 'Total';
$_['entry_parent_css']                           = 'Parent CSS';
$_['entry_child_css']                            = 'Child CSS';
$_['entry_cards_accepted']                       = 'Accepted cards';
$_['entry_reverse_authorisation']                = 'Reverse Authorisation:';
$_['entry_refunded']                             = 'Refunded:';
$_['entry_refund']                               = 'Issue refund (%s):';

$_['error_permission']                           = 'You do not have permissions to modify this module';
$_['error_site_reference']                       = 'Site Reference is required';
$_['error_version']                              = 'Version is required';
$_['error_notification_password']                = 'Notification password is required';
$_['error_cards_accepted']                       = 'Accepted cards is required';
$_['error_username']                             = 'Username is required';
$_['error_password']                             = 'Password is required';
$_['error_connection']                           = 'Could not connect to Secure Trading';
$_['error_data_missing']                         = 'Data missing';

$_['help_version']                               = 'Payment Version (e.g. 2)';
$_['help_username']                              = 'Your webservice username';
$_['help_password']                              = 'Your webservice password';
$_['help_refund']                                = 'Please include the decimal point and the decimal part of the amount';
$_['help_total']                                 = 'The checkout total the order must reach before this payment method becomes active';

$_['button_reverse_authorisation']               = 'Reverse Authorisation';
$_['button_refund']                              = 'Refund';


// Order page - payment tab
$_['text_payment_info']                          = 'Payment information';
$_['text_release_status']                        = 'Payment released';
$_['text_void_status']                           = 'Reverse Authorisation';
$_['text_rebate_status']                         = 'Payment rebated';
$_['text_order_ref']                             = 'Order ref';
$_['text_order_total']                           = 'Total authorised';
$_['text_total_released']                        = 'Total released';
$_['text_transactions']                          = 'Transactions';
$_['text_column_amount']                         = 'Amount';
$_['text_column_type']                           = 'Type';
$_['text_column_created']                        = 'Created';
$_['text_release_ok']                            = 'Release was successful';
$_['text_release_ok_order']                      = 'Release was successful, order status updated to success - settled';
$_['text_rebate_ok']                             = 'Rebate was successful';
$_['text_rebate_ok_order']                       = 'Rebate was successful, order status updated to rebated';
$_['text_void_ok']                               = 'Void was successful, order status updated to voided';

$_['text_confirm_void']                          = 'Are you sure you want to reverse the authorisation?';
$_['text_confirm_release']                       = 'Are you sure you want to release the payment?';
$_['text_confirm_rebate']                        = 'Are you sure you want to rebate the payment?';

$_['button_release']                             = 'Release';
$_['button_rebate']                              = 'Rebate / refund';
$_['button_void']                                = 'Reverse Authorisation';
