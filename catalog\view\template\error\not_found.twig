{{ header }}
<div id="error-not-found" class="container-fluid">
  <div class="row">{{ column_left }}
    <div id="content" class="col">{{ content_top }}
      


<section class="e404-qwTx7JvHJa e404-fullscreen e404-section" style="background-color:red;background-position: 50% 50%;
    background-repeat: no-repeat;
    background-size: cover;
    background-color: #eee;"><!--!-->
    <!--!--><div class="e404-overlay" style="opacity: 0.7; background-color: rgb(99, 90, 81);">
    </div>
    <div class="container e404-align-left" style="padding:20px;z-index: 99999999;position: relative;"><!--!-->
        <div class="e404-media-container-column e404-white col-md-12"><!--!-->
            <!--!--><h1 class="e404-section-subtitle py-3 e404-fonts-style e404-display-5">
                <span style="font-style: normal;" class="text-white">
                    {{ text_error }}
                </span>
            </h1>
            <div class="e404-section-btn py-4"><!--!-->
                <a class="e404-btn e404-btn-md e404-btn-white e404-display-4 text-white" href="{{ continue }}">{{ button_continue }}</a><!--!-->
            </div><!--!-->
        </div><!--!-->
    </div><!--!-->
</section>

<style>
.e404-section-title {
    font-style: normal;
    line-height: 1.2;
}
.e404-display-1 {
    font-size: 4.25rem;
}


.e404-section-btn {
    margin-left: -0.25rem;
    margin-right: -0.25rem;
    font-size: 0;
}
.e404-text {
    font-style: normal;
    line-height: 1.6;
}
.e404-display-5 {
    font-size: 1.5rem;
}
.e404-overlay {
    bottom: 0;
    left: 0;
    position: absolute;
    right: 0;
    top: 120px;
    z-index: 0;
	min-height: 108.2vh;
}
.e404-section-btn a.e404-btn:not(.btn-form) {
    border-radius: 100px;
}

.e404-display-4 {
    font-size: 1rem;
}
.e404-btn {
    font-weight: 500;
    border-width: 2px;
    font-style: normal;
    letter-spacing: 1px;
    margin: 0.4rem 0.8rem;
    white-space: normal;
    -webkit-transition: all .3s ease-in-out;
    -moz-transition: all .3s ease-in-out;
    transition: all .3s ease-in-out;
    padding: 1rem 3rem;
    border-radius: 3px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    word-break: break-word;
}
.e404-btn-md {
    font-weight: 500!important;
    letter-spacing: 1px!important;
    margin: 0.4rem 0.8rem!important;
    -webkit-transition: all .3s ease-in-out;
    -moz-transition: all .3s ease-in-out;
    transition: all .3s ease-in-out!important;
    padding: 1rem 3rem!important;
    border-radius: 3px;
}
.e404-btn-white, .e404-btn-white:active {
    background-color: #fff;
    border-color: #fff;
    color: #fff;
}

.e404-btn-white {
    color: #333!important;
}
.loader-overlay {
    position: fixed;
    width: 100vw;
    height: 100vh;
    background: #fff;
    z-index: 1000;
    transition: all .5s ease-in-out;
}
.invisible {
    visibility: hidden!important;
}
.overlay {
    display: none;
    position: fixed;
    width: 100vw;
    height: 100vh;
    background: rgba(var(--bs-gray-800),.9);
    z-index: 998;
    opacity: 0;
    transition: all .5s ease-in-out;
}
.bg-body .header .nav_topbar, .bg-body .header.navbar {
    z-index: 999999999999999;
}
.e404-fullscreen {
    display: flex;
    display: -webkit-flex;
    display: -moz-flex;
    display: -ms-flex;
    display: -o-flex;
    align-items: center;
    -webkit-align-items: center;
    min-height: 100vh;
    padding-top: 0rem;
    padding-bottom: 0rem;
}
</style>


      {{ content_bottom }}</div>
    {{ column_right }}</div>
</div>
{{ footer }}

