<?php
// Heading
$_['heading_title']                    = 'Period Closing';

// Text
$_['text_list']                        = 'Accounting Periods List';
$_['text_add']                         = 'Close New Period';
$_['text_edit']                        = 'Edit Period Closing';
$_['text_default']                     = 'Default';
$_['text_success_closed']              = 'Accounting period closed successfully!';
$_['text_success_reopened']            = 'Accounting period reopened successfully!';
$_['text_confirm_close']               = 'Are you sure you want to close this period? This action cannot be undone.';
$_['text_confirm_reopen']              = 'Are you sure you want to reopen this period?';
$_['text_no_results']                  = 'No accounting periods found';
$_['text_home']                        = 'Home';

// Period Status
$_['text_status_open']                 = 'Open';
$_['text_status_closed']               = 'Closed';
$_['text_status_reopened']             = 'Reopened';
$_['text_status_locked']               = 'Locked';

// Closing Process
$_['text_closing_preview']             = 'Closing Preview';
$_['text_closing_entries']             = 'Closing Entries';
$_['text_net_income']                  = 'Net Income';
$_['text_retained_earnings']           = 'Retained Earnings';
$_['text_temporary_accounts']          = 'Temporary Accounts';
$_['text_permanent_accounts']          = 'Permanent Accounts';
$_['text_revenue_accounts']            = 'Revenue Accounts';
$_['text_expense_accounts']            = 'Expense Accounts';
$_['text_income_summary']              = 'Income Summary';

// Backup & Security
$_['text_backup_created']              = 'Backup created successfully';
$_['text_backup_failed']               = 'Failed to create backup';
$_['text_create_backup']               = 'Create Backup';
$_['text_backup_before_closing']       = 'Create backup before closing';

// Financial Year
$_['text_financial_year']              = 'Financial Year';
$_['text_fiscal_period']               = 'Fiscal Period';
$_['text_quarter']                     = 'Quarter';
$_['text_month']                       = 'Month';
$_['text_year_end']                    = 'Year End';

// Validation & Checks
$_['text_balance_check']               = 'Balance Check';
$_['text_trial_balance']               = 'Trial Balance';
$_['text_adjusting_entries']           = 'Adjusting Entries';
$_['text_unadjusted_trial_balance']    = 'Unadjusted Trial Balance';
$_['text_adjusted_trial_balance']      = 'Adjusted Trial Balance';

// Workflow & Approvals
$_['text_approval_required']           = 'Approval Required';
$_['text_approved_by']                 = 'Approved By';
$_['text_approval_date']               = 'Approval Date';
$_['text_pending_approval']            = 'Pending Approval';
$_['text_workflow_status']             = 'Workflow Status';

// Column
$_['column_period_name']               = 'Period Name';
$_['column_start_date']                = 'Start Date';
$_['column_end_date']                  = 'End Date';
$_['column_status']                    = 'Status';
$_['column_closed_by']                 = 'Closed By';
$_['column_closed_date']               = 'Closed Date';
$_['column_net_income']                = 'Net Income';
$_['column_total_revenue']             = 'Total Revenue';
$_['column_total_expenses']            = 'Total Expenses';
$_['column_action']                    = 'Action';

// Entry
$_['entry_period_name']                = 'Period Name';
$_['entry_start_date']                 = 'Start Date';
$_['entry_end_date']                   = 'End Date';
$_['entry_closing_notes']              = 'Closing Notes';
$_['entry_financial_year']             = 'Financial Year';
$_['entry_period_type']                = 'Period Type';

// Button
$_['button_close_period']              = 'Close Period';
$_['button_reopen_period']             = 'Reopen Period';
$_['button_preview_closing']           = 'Preview Closing';
$_['button_create_backup']             = 'Create Backup';
$_['button_view_entries']              = 'View Entries';
$_['button_approve']                   = 'Approve';
$_['button_reject']                    = 'Reject';

// Error
$_['error_permission']                 = 'Warning: You do not have permission to access period closing!';
$_['error_period_name']                = 'Period name must be between 3 and 64 characters!';
$_['error_start_date']                 = 'Start date is required!';
$_['error_end_date']                   = 'End date is required!';
$_['error_date_range']                 = 'End date must be after start date!';
$_['error_period_exists']              = 'An accounting period with the same dates already exists!';
$_['error_period_not_found']           = 'Accounting period not found!';
$_['error_period_already_closed']      = 'Accounting period is already closed!';
$_['error_period_not_closed']          = 'Accounting period is not closed!';
$_['error_unbalanced_entries']         = 'There are unbalanced entries in the period!';
$_['error_pending_transactions']       = 'There are pending transactions in the period!';
$_['error_backup_failed']              = 'Failed to create backup!';
$_['error_closing_failed']             = 'Failed to close period!';
$_['error_reopening_failed']           = 'Failed to reopen period!';

// Success
$_['text_success_backup']              = 'Backup created successfully!';
$_['text_success_preview']             = 'Closing preview generated successfully!';
$_['text_success_approved']            = 'Period closing approved successfully!';
$_['text_success_rejected']            = 'Period closing rejected successfully!';

// Help
$_['help_period_name']                 = 'Descriptive name for the accounting period like "Q1 2025"';
$_['help_closing_notes']               = 'Additional notes about the closing process';
$_['help_backup_before_closing']       = 'It is highly recommended to create a backup before closing the period';
$_['help_closing_entries']             = 'Closing entries transfer temporary account balances to retained earnings';

// Additional
$_['text_total']                       = 'Total';
$_['text_subtotal']                    = 'Subtotal';
$_['text_grand_total']                 = 'Grand Total';
$_['text_currency']                    = 'Currency';
$_['text_egp']                         = 'Egyptian Pound';
$_['text_generated_by']                = 'Generated By';
$_['text_generated_on']                = 'Generated On';
$_['text_page']                        = 'Page';
$_['text_of']                          = 'of';

// Controller language variables - Direct Arabic texts
$_['log_unauthorized_access'] = 'Unauthorized access attempt to period closing';
$_['log_view_period_closing_screen'] = 'View period closing screen';
$_['log_unauthorized_close'] = 'Unauthorized period closing attempt';
$_['log_close_period'] = 'Close accounting period';
$_['notification_period_closed'] = 'Accounting period closed';
$_['notification_period_closed_message'] = 'Accounting period has been closed';
$_['default_period_name'] = 'Period';
$_['log_create_backup'] = 'Create backup before period closing';

// Additional template variables
$_['text_actions'] = 'Actions';
$_['text_closing_process'] = 'Closing Process';
$_['text_closing_description'] = 'Close accounting period safely and systematically';
$_['text_step_validation'] = 'Data Validation';
$_['text_step_backup'] = 'Create Backup';
$_['text_step_reports'] = 'Generate Reports';
$_['text_step_closing'] = 'Close Period';
$_['help_step_validation'] = 'Verify account balances and data integrity';
$_['help_step_backup'] = 'Create secure backup of data';
$_['help_step_reports'] = 'Generate final financial reports';
$_['help_step_closing'] = 'Close period and lock accounts';
$_['text_complete'] = 'Complete';
$_['text_period_details'] = 'Period Details';
$_['entry_period_start_date'] = 'Period Start Date';
$_['entry_period_end_date'] = 'Period End Date';
$_['entry_closing_reason'] = 'Closing Reason';
$_['text_select_reason'] = 'Select Reason';
$_['text_monthly_closing'] = 'Monthly Closing';
$_['text_quarterly_closing'] = 'Quarterly Closing';
$_['text_yearly_closing'] = 'Yearly Closing';
$_['text_special_closing'] = 'Special Closing';
$_['text_closing_checklist'] = 'Closing Checklist';
$_['text_period_summary'] = 'Period Summary';
$_['text_total_transactions'] = 'Total Transactions';
$_['text_total_debits'] = 'Total Debits';
$_['text_total_credits'] = 'Total Credits';
$_['text_balance_difference'] = 'Balance Difference';
$_['text_unposted_entries'] = 'Unposted Entries';
$_['text_pending_approvals'] = 'Pending Approvals';
$_['button_cancel'] = 'Cancel';
$_['button_preview'] = 'Preview';
$_['button_confirm_close'] = 'Confirm Close';
$_['button_validate'] = 'Validate';
$_['button_backup'] = 'Backup';
$_['button_reports'] = 'Reports';
$_['button_close'] = 'Close';
$_['button_validate_period'] = 'Validate Period';
$_['button_generate_reports'] = 'Generate Reports';
$_['success_validation_complete'] = 'Validation completed successfully';
$_['error_validation_failed'] = 'Validation failed';
$_['text_confirm_backup'] = 'Do you want to create a backup?';
$_['success_backup_created'] = 'Backup created successfully';
$_['success_reports_generated'] = 'Reports generated successfully';
$_['error_reports_failed'] = 'Failed to generate reports';
$_['text_confirm_closing'] = 'Are you sure you want to close the period?';
$_['success_period_closed'] = 'Period closed successfully';
$_['error_steps_incomplete'] = 'All steps must be completed first';
$_['text_confirm_cancel'] = 'Do you want to cancel the closing process?';
$_['error_period_name_required'] = 'Period name is required';
$_['error_start_date_required'] = 'Start date is required';
$_['error_end_date_required'] = 'End date is required';
$_['error_closing_reason_required'] = 'Closing reason is required';
$_['error_invalid_date_range'] = 'Invalid date range';
$_['text_pending'] = 'Pending';
$_['text_in_progress'] = 'In Progress';
$_['text_completed'] = 'Completed';
$_['text_failed'] = 'Failed';

// Enhanced performance and analytics variables
$_['text_optimized_closing']           = 'Optimized Period Closing';
$_['text_closing_analysis']            = 'Period Closing Analysis';
$_['text_cache_enabled']               = 'Cache Enabled';
$_['text_period_info']                 = 'Period Information';
$_['text_is_balanced']                 = 'Balanced';
$_['text_total_journals']              = 'Total Journals';
$_['text_total_entries']               = 'Total Entries';
$_['text_closing_statistics']          = 'Closing Statistics';
$_['button_closing_analysis']          = 'Closing Analysis';
$_['text_loading_analysis']            = 'Loading closing analysis...';
$_['text_analysis_ready']              = 'Analysis ready';

// Enhanced performance and analytics variables
$_['text_optimized_periods']           = 'Optimized Accounting Periods';
$_['text_period_analysis']             = 'Period Analysis';
$_['text_enhanced_analysis']           = 'Enhanced Analysis';
$_['text_periods_summary']             = 'Periods Summary';
$_['text_period_details']              = 'Period Details';
$_['text_common_errors']               = 'Common Errors';
$_['text_closing_readiness']           = 'Closing Readiness';
$_['text_readiness_check']             = 'Readiness Check';
$_['text_unbalanced_journals']         = 'Unbalanced Journals';
$_['text_unposted_journals']           = 'Unposted Journals';
$_['text_validation_log']              = 'Validation Log';
$_['button_period_analysis']           = 'Period Analysis';
$_['text_loading_period_analysis']     = 'Loading period analysis...';
$_['text_period_analysis_ready']       = 'Period analysis ready';
?>
