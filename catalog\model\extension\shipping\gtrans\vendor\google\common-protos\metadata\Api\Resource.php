<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/resource.proto

namespace GPBMetadata\Google\Api;

class Resource
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(hex2bin(
            "0afa030a19676f6f676c652f6170692f7265736f757263652e70726f746f" .
            "120a676f6f676c652e6170691a20676f6f676c652f70726f746f6275662f" .
            "64657363726970746f722e70726f746f22ff010a125265736f7572636544" .
            "657363726970746f72120c0a0474797065180120012809120f0a07706174" .
            "7465726e18022003280912120a0a6e616d655f6669656c64180320012809" .
            "12370a07686973746f727918042001280e32262e676f6f676c652e617069" .
            "2e5265736f7572636544657363726970746f722e486973746f7279120e0a" .
            "06706c7572616c18052001280912100a0873696e67756c61721806200128" .
            "09225b0a07486973746f727912170a13484953544f52595f554e53504543" .
            "49464945441000121d0a194f524947494e414c4c595f53494e474c455f50" .
            "41545445524e100112180a144655545552455f4d554c54495f5041545445" .
            "524e100222350a115265736f757263655265666572656e6365120c0a0474" .
            "79706518012001280912120a0a6368696c645f7479706518022001280942" .
            "6e0a0e636f6d2e676f6f676c652e617069420d5265736f7572636550726f" .
            "746f50015a41676f6f676c652e676f6c616e672e6f72672f67656e70726f" .
            "746f2f676f6f676c65617069732f6170692f616e6e6f746174696f6e733b" .
            "616e6e6f746174696f6e73f80101a2020447415049620670726f746f33"
        ), true);

        static::$is_initialized = true;
    }
}

