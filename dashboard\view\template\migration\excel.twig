{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="submit" form="form-migration" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fas fa-save"></i></button>
        <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-light"><i class="fas fa-reply"></i></a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ol class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
          <li class="breadcrumb-item"><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    <div class="row mb-4">
      <div class="col-12">
        <div class="progress-wizard">
          <div class="progress-wizard-step{% if step == 1 %} active{% endif %}{% if step > 1 %} complete{% endif %}">
            <div class="progress-wizard-dot">
              <i class="fas fa-file-excel"></i>
            </div>
            <div class="progress-wizard-info">
              <h4>{{ text_step1_title }}</h4>
              <p>{{ text_step1_description }}</p>
            </div>
          </div>
          <div class="progress-wizard-step{% if step == 2 %} active{% endif %}{% if step > 2 %} complete{% endif %}">
            <div class="progress-wizard-dot">
              <i class="fas fa-columns"></i>
            </div>
            <div class="progress-wizard-info">
              <h4>{{ text_step2_title }}</h4>
              <p>{{ text_step2_description }}</p>
            </div>
          </div>
          <div class="progress-wizard-step{% if step == 3 %} active{% endif %}{% if step > 3 %} complete{% endif %}">
            <div class="progress-wizard-dot">
              <i class="fas fa-check-circle"></i>
            </div>
            <div class="progress-wizard-info">
              <h4>{{ text_step3_title }}</h4>
              <p>{{ text_step3_description }}</p>
            </div>
          </div>
          <div class="progress-wizard-step{% if step == 4 %} active{% endif %}{% if step > 4 %} complete{% endif %}">
            <div class="progress-wizard-dot">
              <i class="fas fa-sync"></i>
            </div>
            <div class="progress-wizard-info">
              <h4>{{ text_step4_title }}</h4>
              <p>{{ text_step4_description }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="card">
      <div class="card-header"><i class="fas fa-pencil-alt"></i> {{ text_form }}</div>
      <div class="card-body">
        <form id="form-migration" action="{{ action }}" method="post" data-oc-toggle="ajax" enctype="multipart/form-data">
          <div class="row mb-3">
            <label for="input-file" class="col-sm-2 col-form-label">{{ entry_excel_file }}</label>
            <div class="col-sm-10">
              <div class="input-group">
                <input type="file" name="excel_file" accept=".xlsx,.xls" id="input-file" class="form-control" aria-describedby="fileHelp"/>
                <span class="input-group-text"><i class="fas fa-file-excel"></i></span>
              </div>
              <div id="error-file" class="invalid-feedback"></div>
              <div id="fileHelp" class="form-text">{{ help_excel_file }}</div>
              <div class="alert alert-info mt-2">
                <i class="fas fa-info-circle"></i> يرجى التأكد من أن ملف Excel يحتوي على الأعمدة المطلوبة لكل ورقة عمل. انقر على زر المساعدة لمزيد من المعلومات.
              </div>
            </div>
          </div>
          <hr>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label">{{ text_sheet_mapping }}</label>
            <div class="col-sm-10">
              <div class="row">
                <div class="col-md-6">
                  <div class="card h-100">
                    <div class="card-header bg-light"><i class="fas fa-database me-2"></i>{{ text_core_data }}</div>
                    <div class="card-body">
                      <div class="mb-3">
                        <label class="form-label">{{ text_products }}</label>
                        <select name="sheet_products" class="form-select">
                          <option value="">{{ text_select }}</option>
                          {% for sheet in sheets %}
                            <option value="{{ sheet.id }}">{{ sheet.name }}</option>
                          {% endfor %}
                        </select>
                      </div>
                      <div class="mb-3">
                        <label class="form-label">{{ text_customers }}</label>
                        <select name="sheet_customers" class="form-select">
                          <option value="">{{ text_select }}</option>
                          {% for sheet in sheets %}
                            <option value="{{ sheet.id }}">{{ sheet.name }}</option>
                          {% endfor %}
                        </select>
                      </div>
                      <div class="mb-3">
                        <label class="form-label">{{ text_orders }}</label>
                        <select name="sheet_orders" class="form-select">
                          <option value="">{{ text_select }}</option>
                          {% for sheet in sheets %}
                            <option value="{{ sheet.id }}">{{ sheet.name }}</option>
                          {% endfor %}
                        </select>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="card h-100">
                    <div class="card-header bg-light"><i class="fas fa-layer-group me-2"></i>{{ text_additional_data }}</div>
                    <div class="card-body">
                      <div class="mb-3">
                        <label class="form-label">{{ text_categories }}</label>
                        <select name="sheet_categories" class="form-select">
                          <option value="">{{ text_select }}</option>
                          {% for sheet in sheets %}
                            <option value="{{ sheet.id }}">{{ sheet.name }}</option>
                          {% endfor %}
                        </select>
                      </div>
                      <div class="mb-3">
                        <label class="form-label">{{ text_suppliers }}</label>
                        <select name="sheet_suppliers" class="form-select">
                          <option value="">{{ text_select }}</option>
                          {% for sheet in sheets %}
                            <option value="{{ sheet.id }}">{{ sheet.name }}</option>
                          {% endfor %}
                        </select>
                      </div>
                      <div class="mb-3">
                        <label class="form-label">{{ text_inventory }}</label>
                        <select name="sheet_inventory" class="form-select">
                          <option value="">{{ text_select }}</option>
                          {% for sheet in sheets %}
                            <option value="{{ sheet.id }}">{{ sheet.name }}</option>
                          {% endfor %}
                        </select>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
    <div class="card">
      <div class="card-header"><i class="fas fa-sync"></i> {{ text_progress }}</div>
      <div class="card-body">
        <div id="progress-status" class="alert alert-info d-none">
          <div class="d-flex align-items-center">
            <div class="spinner-border text-primary me-3" role="status">
              <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <div>
              <h5 class="alert-heading mb-1">{{ text_importing }}</h5>
              <p class="mb-0" id="current-operation">جاري تحليل البيانات...</p>
            </div>
          </div>
        </div>
        <div id="progress-bar" class="progress mb-3 d-none">
          <div class="progress-bar" role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
        </div>
        <div id="progress-result" class="alert d-none"></div>
      </div>
    </div>
  </div>
</div>
<style>
.progress-wizard {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30px;
  position: relative;
}

.progress-wizard::before {
  content: '';
  position: absolute;
  top: 30px;
  left: 0;
  width: 100%;
  height: 2px;
  background: #e9ecef;
  z-index: 0;
}

.progress-wizard-step {
  flex: 1;
  text-align: center;
  position: relative;
  z-index: 1;
}

.progress-wizard-dot {
  width: 60px;
  height: 60px;
  line-height: 60px;
  background: #fff;
  border: 2px solid #e9ecef;
  border-radius: 50%;
  margin: 0 auto 15px;
  color: #6c757d;
  transition: all 0.3s ease;
}

.progress-wizard-step.active .progress-wizard-dot,
.progress-wizard-step.complete .progress-wizard-dot {
  background: #28a745;
  border-color: #28a745;
  color: #fff;
}

.progress-wizard-info h4 {
  font-size: 16px;
  margin-bottom: 5px;
  color: #495057;
}

.progress-wizard-info p {
  font-size: 14px;
  color: #6c757d;
  margin: 0;
}

.card {
  box-shadow: 0 2px 10px rgba(0,0,0,0.05);
  border: none;
  margin-bottom: 20px;
}

.card-header {
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  padding: 15px 20px;
}

.form-select {
  border-color: #ced4da;
}

.form-select:focus {
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}

.progress {
  height: 10px;
  border-radius: 5px;
}

.progress-bar {
  background-color: #28a745;
  transition: width 0.3s ease;
}
</style>
<script type="text/javascript">
$('#form-migration').on('submit', function(e) {
    e.preventDefault();
    
    // Reset UI state
    $('#progress-status').removeClass('d-none');
    $('#progress-bar').removeClass('d-none');
    $('#progress-result').addClass('d-none');
    $('.progress-bar').css('width', '0%').attr('aria-valuenow', 0);
    
    // Validate inputs
    let isValid = true;
    const fileInput = $('#input-file');
    const errorFile = $('#error-file');
    const selectedSheets = {
        products: $('select[name="sheet_products"]').val(),
        customers: $('select[name="sheet_customers"]').val(),
        orders: $('select[name="sheet_orders"]').val(),
        categories: $('select[name="sheet_categories"]').val(),
        suppliers: $('select[name="sheet_suppliers"]').val(),
        inventory: $('select[name="sheet_inventory"]').val()
    };
    
    // Validate file
    if (!fileInput[0].files.length) {
        fileInput.addClass('is-invalid');
        errorFile.text('يرجى اختيار ملف Excel للاستيراد').show();
        isValid = false;
    } else if (!fileInput[0].files[0].name.match(/\.(xlsx|xls)$/i)) {
        fileInput.addClass('is-invalid');
        errorFile.text('يرجى اختيار ملف Excel صالح (.xlsx أو .xls)').show();
        isValid = false;
    } else {
        fileInput.removeClass('is-invalid');
        errorFile.hide();
    }
    
    // Validate sheet selection
    const selectedSheetCount = Object.values(selectedSheets).filter(Boolean).length;
    if (selectedSheetCount === 0) {
        $('#progress-result')
            .removeClass('d-none alert-success')
            .addClass('alert-warning')
            .html('<i class="fas fa-exclamation-triangle"></i> يرجى اختيار ورقة عمل واحدة على الأقل للاستيراد');
        isValid = false;
    }
    
    if (!isValid) {
        $('#progress-status').addClass('d-none');
        $('#progress-bar').addClass('d-none');
        return;
    }
    
    // Get selected sheets
    const selectedSheets = {
        products: $('select[name="sheet_products"]').val(),
        customers: $('select[name="sheet_customers"]').val(),
        orders: $('select[name="sheet_orders"]').val(),
        categories: $('select[name="sheet_categories"]').val(),
        suppliers: $('select[name="sheet_suppliers"]').val(),
        inventory: $('select[name="sheet_inventory"]').val()
    };

    // Create FormData object
    const formData = new FormData();
    formData.append('excel_file', fileInput[0].files[0]);
    formData.append('sheets', JSON.stringify(selectedSheets));

    // Start migration process
    $.ajax({
        url: 'index.php?route=migration/excel/migrate',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                let currentStep = 0;
                const totalSteps = Object.values(selectedSheets).filter(Boolean).length;
                const stepIncrement = 100 / totalSteps;
                const stepMessages = {
                    products: 'جاري استيراد بيانات المنتجات...',
                    customers: 'جاري استيراد بيانات العملاء...',
                    orders: 'جاري استيراد بيانات الطلبات...',
                    categories: 'جاري استيراد بيانات الفئات...',
                    suppliers: 'جاري استيراد بيانات الموردين...',
                    inventory: 'جاري استيراد بيانات المخزون...'
                };

                const processStep = function() {
                    const currentSheet = Object.entries(selectedSheets)
                        .find(([_, value]) => value !== '')?.[0];
                    
                    if (currentSheet) {
                        updateProgressStatus(currentStep + 1, stepMessages[currentSheet]);
                    }

                    $.ajax({
                        url: 'index.php?route=migration/excel/process_step',
                        type: 'POST',
                        data: { step: currentStep },
                        dataType: 'json',
                        success: function(stepResponse) {
                            if (stepResponse.success) {
                                currentStep++;
                                const progress = Math.min(currentStep * stepIncrement, 100);
                                $('.progress-bar').css('width', progress + '%').attr('aria-valuenow', progress);
                                
                                if (currentStep < totalSteps) {
                                    processStep();
                                } else {
                                    $('#progress-status').addClass('d-none');
                                    $('#progress-result')
                                        .removeClass('d-none alert-danger')
                                        .addClass('alert-success')
                                        .html(`
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-check-circle fs-4 me-2"></i>
                                                <div>
                                                    <h5 class="alert-heading mb-1">تم بنجاح!</h5>
                                                    <p class="mb-0">تم اكتمال عملية نقل البيانات بنجاح.</p>
                                                </div>
                                            </div>
                                        `);
                                }
                            } else {
                                handleError(stepResponse.error);
                            }
                        },
                        error: function() {
                            handleError('حدث خطأ أثناء معالجة البيانات');
                        }
                    });
                };

                processStep();
            } else {
                handleError(response.error);
            }
        },
        error: function() {
            handleError('فشل الاتصال بالخادم');
        }
    });
});

// Update available sheets when file is selected
$('#input-file').on('change', function() {
    const file = this.files[0];
    if (file) {
        const formData = new FormData();
        formData.append('excel_file', file);

        $.ajax({
            url: 'index.php?route=migration/excel/get_sheets',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    // Update all sheet select elements
                    const selects = $('select[name^="sheet_"]');
                    selects.each(function() {
                        const select = $(this);
                        select.find('option:not(:first)').remove();
                        
                        response.sheets.forEach(function(sheet) {
                            select.append(`<option value="${sheet.id}">${sheet.name}</option>`);
                        });
                    });
                }
            }
        });
    }
});

function updateProgressStatus(step, message) {
    $('#current-operation').text(message);
    $('.progress-bar')
        .css('width', (step * 25) + '%')
        .attr('aria-valuenow', step * 25)
        .text(step * 25 + '%');
}

function handleError(message) {
    $('#progress-status').addClass('d-none');
    $('#progress-result')
        .removeClass('d-none alert-success')
        .addClass('alert-danger')
        .html(`
            <div class="d-flex align-items-center">
                <i class="fas fa-exclamation-circle fs-4 me-2"></i>
                <div>
                    <h5 class="alert-heading mb-1">حدث خطأ</h5>
                    <p class="mb-0">${message}</p>
                </div>
            </div>
        `);
}
</script>
{{ footer }}