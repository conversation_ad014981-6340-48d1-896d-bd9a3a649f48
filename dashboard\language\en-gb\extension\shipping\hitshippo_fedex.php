<?php
// Heading
$_['heading_title']                            = 'Automated FedeX Shipping';

// Text
$_['text_shipping']                            = 'Shipping';
$_['text_success']                             = 'Success: You have Configured FEDEX shipping!';
$_['text_edit']                                = 'Configure FEDEX Shipping Account';

$_['text_shiiping_address']                     = 'Configure FEDEX Shipping Address';
$_['text_rates']                                = 'Configure FEDEX Shipping rates & Services';
$_['text_packing']                              = 'Configure FEDEX Shipping Package';
$_['text_fedex_1'] = 'FedEx First Overnight';
$_['text_fedex_2'] = 'FedEx Priority Overnight';
$_['text_fedex_3'] = 'FedEx Standard Overnight';
$_['text_fedex_4'] = 'FedEx 2Day A.M';
$_['text_fedex_5'] = 'FedEx 2Day';
$_['text_fedex_7'] = 'FedEx Same Day';
$_['text_fedex_8'] = 'FedEx Same Day City';
$_['text_fedex_9'] = 'FedEx Same Day Metro Afternoon';
$_['text_fedex_10'] = 'FedEx Same Day Metro Morning';
$_['text_fedex_11'] = 'FedEx Same Day Metro Rush';
$_['text_fedex_12'] = 'FedEx Express Saver';
$_['text_fedex_13'] = 'FedEx Ground Home Delivery';
$_['text_fedex_14'] = 'FedEx Ground';
$_['text_fedex_15'] = 'FedEx International Economy';
$_['text_fedex_16'] = 'FedEx International Economy Distribution';
$_['text_fedex_17'] = 'FedEx International First';
$_['text_fedex_18'] = 'FedEx International Ground';
$_['text_fedex_19'] = 'FedEx International Priority';
$_['text_fedex_20'] = 'FedEx International Priority Distribution';
$_['text_fedex_21'] = 'FedEx Europe First International Priority';
$_['text_fedex_22'] = 'FedEx International Priority Express';
$_['text_fedex_23'] = 'FedEx First International Priority Plus';
$_['text_fedex_24'] = 'FedEx International Distribution Fright';
$_['text_fedex_25'] = 'FedEx 1 Day Freight';
$_['text_fedex_26'] = 'FedEx 2 Day Freight';
$_['text_fedex_27'] = 'FedEx 3 Day Freight';
$_['text_fedex_28'] = 'FedEx Economy Freight';
$_['text_fedex_29'] = 'FedEx Priority Freight';
$_['text_fedex_30'] = 'FedEx Smart Post';
$_['text_fedex_31'] = 'FedEx First Freight';
$_['text_fedex_32'] = 'FedEx Freight Economy';
$_['text_fedex_B'] = 'FedEx Freight Priority';
$_['text_fedex_C'] = 'FedEx CARGO Airport to Airport';
$_['text_fedex_D'] = 'FedEx CARGO Freight FOrwarding';
$_['text_fedex_E'] = 'FedEx CARGO International Express Fright';
$_['text_fedex_F'] = 'FedEx CARGO International Premium';
$_['text_fedex_G'] = 'FedEx CARGO Mail';
$_['text_fedex_H'] = 'FedEx CARGO Registered Mail';
$_['text_fedex_I'] = 'FedEx CARGO Surface Mail';
$_['text_fedex_J'] = 'FedEx Custom Critical Air Expedite Exclusive Use';
$_['text_fedex_K'] = 'FedEx Custom Critical Air Expedite Network';
$_['text_fedex_L'] = 'FedEx Custom Critical Charter Air';
$_['text_fedex_M'] = 'FedEx Custom Critical Point to Point';
$_['text_fedex_N'] = 'FedEx Custom Critical Surface Expedite';
$_['text_fedex_O'] = 'FedEx Custom Critical Surface Expedite Exclusive Use';
$_['text_fedex_P'] = 'FedEx Custom Critical Temp Assure Air';
$_['text_fedex_Q'] = 'FedEx Custom Critical Temp Assure Validated Air';
$_['text_fedex_R'] = 'FedEx Custom Critical White Glove Services';
$_['text_fedex_S'] = 'Fedex Transborder Distribution Consolidation';
$_['text_fedex_T'] = 'FedEx Distance Deferred';
$_['text_fedex_U'] = 'FedEx Next Day Early Morning';
$_['text_fedex_V'] = 'FedEx Next Day Mid Morning';
$_['text_fedex_W'] = 'FedEx Next Day Afternoon';
$_['text_fedex_X'] = 'FedEx Next Day End of Day';
$_['text_fedex_Y'] = 'FedEx Next Day Freight';
$_['text_regular_pickup']                      = 'Regular Pickup';
$_['text_request_courier']                     = 'Request Courier';
$_['text_your_packaging']                      = 'Your Packaging';
$_['text_list_rate']                           = 'List Rate';
$_['text_account_rate']                        = 'Account Rate';

//shipping Address
$_['entry_shipper_name']						= 'Shipper name';
$_['entry_company_name']						= 'Company name';
$_['entry_phone_num']						= 'Phone Number';
$_['entry_email_addr']						= 'Email Address';
$_['entry_address1']						= 'Address Line1';
$_['entry_address2']						= 'Address Line2';
$_['entry_city']						= 'City';
$_['entry_state']						= 'State Code';
$_['entry_country_code']						= 'Country Code';
$_['entry_realtime_rates']						= 'Enable Real Time Rates';
$_['entry_insurance']						= 'Enable Insurance';

//packing
$_['_entry_weight']							= 'Weight/Dimension Unit';
$_['_entry_kgcm']							= 'KG-CM';
$_['_entry_lbin']							= 'LBS-IN';
$_['_entry_packing_type']							= 'Choose packing type';
$_['text_per_item']							= 'Default: Pack items individually ';
$_['text_fedex_box']							= 'Recommended: Pack into boxes with weights and dimensions ';
$_['text_fedex_weight_based']							= 'Weight based: Calculate shipping on the basis of order total weight ';
$_['text_peritem_head']							= 'Configure Pack items individually (if choosed)';
$_['text_box_head']							= 'Configure Pack into boxes with weights and dimensions (if choosed)';
$_['text_weight_head']							= 'Configure Weight based (if choosed)';
$_['text_box']							= 'FEDEX Box';
$_['text_fly']							= 'Flyer';
$_['text_fedex_yp']							= 'Your Pack';
$_['text_enable']							= 'Enable';
$_['text_disable']							= 'Disable';
$_['text_head1']							="Name";
$_['text_head2']							="Length";
$_['text_head3']							="Width";
$_['text_head4']							="Height";
$_['text_head5']							="Box Weight";
$_['text_head6']							="Max Weight";
$_['text_head7']							="Enabled";
$_['text_head8']							="Package Type";
$_['text_head9']							="Add Box";
$_['text_head10']							="Remove selected box(es)";
$_['text_head11']							="Preloaded the Dimension and Weight in unit Inches and Pound. If you have selected unit as Centimetre and Kilogram please convert it accordingly.";
$_['text_head12']							="Maximum Weight / Packing";
$_['text_head13']							="Pack heavier items first";
$_['text_head14']							="Pack lighter items first";
$_['text_head15']							="Pack purely divided by weight";
$_['text_head20']							="Printing Size";
$_['text_head21']							="PDF - PAPER_7X4.75";
$_['text_head22']							="PDF - PAPER_4X6";
$_['text_head23']							="PDF - PAPER_4X8";
$_['text_head24']							="PDF - PAPER_4X9";
$_['text_head25']							="PDF - PAPER_7X4.75";
$_['text_head26']							="PDF - PAPER_8.5X11_BOTTOM_HALF_LABEL";
$_['text_head27']							="PDF - PAPER_8.5X11_TOP_HALF_LABEL";
$_['text_head28']							="PDF - PAPER_LETTER";
$_['text_head29']							="PDF - STOCK_4X6";
$_['text_head30']							="PDF - STOCK_4X6.75_LEADING_DOC_TAB";
$_['text_head31']							="PDF - STOCK_4X6.75_TRAILING_DOC_TAB";
$_['text_head32']							="PDF - STOCK_4X8";
$_['text_head44']							="PDF - STOCK_4X9_LEADING_DOC_TAB";
$_['text_head45']							="PDF - STOCK_4X9_TRAILING_DOC_TAB";
$_['text_head33']							="Label Output Type";
$_['text_head34']							="COMMON2D";
$_['text_head35']							="LABEL_DATA_ONLY";
$_['text_head36']							="VICS_BILL_OF_LADING";
$_['text_head46']							="FEDEX_FREIGHT_STRAIGHT_BILL_OF_LADING";
$_['text_head37']							="REGULAR_PICKUP";
$_['text_head38']							="REQUEST_COURIER";
$_['text_head39']							="DROP_BOX";
$_['text_head40']							="BUSINESS_SERVICE_CENTER";
$_['text_head41']							="Drop Off Type";
$_['text_head42']							="Shipping Content Description";
$_['text_head43']							="Company Logo URL";
$_['text_head47']							="STATION";
$_['text_head48']							="Document Type";
$_['text_head49']							="COMMERCIAL INVOICE";
$_['text_head50']							="PRO FORMA INVOICE";
//label
$_['text_label']										="Configure Shipping Label";

$_['text_fedpack']										="Pack type";
$_['text_fedpack_1']										="YOUR_PACKAGING";
$_['text_fedpack_2']										="FEDEX_BOX";
$_['text_fedpack_3']										="FEDEX_PAK";
$_['text_fedpack_4']										="FEDEX_TUBE";
$_['text_fedpack_5']										="FEDEX_10KG_BOX";
$_['text_fedpack_6']										="FEDEX_25KG_BOX";
$_['text_fedpack_7']										="FEDEX_ENVELOPE";
$_['text_fedpack_8']										="FEDEX_EXTRA_LARGE_BOX";
$_['text_fedpack_9']										="FEDEX_LARGE_BOX";
$_['text_fedpack_10']										="FEDEX_MEDIUM_BOX";
$_['text_fedpack_11']										="FEDEX_SMALL_BOX";

// Entry
$_['entry_api_type']                           = 'API Type';
$_['entry_api_type_rest']                      = 'REST (I don\'t have Meter Number)';
$_['entry_api_type_soap']                      = 'SOAP (I have Meter Number)';
$_['entry_rest_api_key']                       = 'API Key';
$_['entry_rest_api_sec']                       = 'API Secret';
$_['entry_rest_api_grant']                     = 'Grant type';
$_['entry_rest_api_grant_client']              = 'Client Credentials';
$_['entry_key']                                = 'Web Service Key';
$_['entry_password']                           = 'Web Service Password';
$_['entry_account']                            = 'Account Number';
$_['entry_meter']                              = 'Meter Number';
$_['entry_postcode']                           = 'Post Code';
$_['entry_test']                               = 'Test Mode';
$_['entry_service']                            = 'Services';
$_['entry_dimension']                          = 'Box Dimensions (L x W x H)';
$_['entry_length_class']                       = 'Length Class';
$_['entry_length']                             = 'Length';
$_['entry_width']                              = 'Width';
$_['entry_height']                             = 'Height';
$_['entry_dropoff_type']                       = 'Drop Off Type';
$_['entry_packaging_type']                     = 'Packaging Type';
$_['entry_rate_type']                          = 'Rate Type';
$_['entry_front_end_logs']                     = 'Debug Logs (Checkout Page)';
$_['entry_display_weight']                     = 'Display Delivery Weight';
$_['entry_weight_class']                       = 'Weight Class';
$_['entry_tax_class']                          = 'Tax Class';
$_['entry_geo_zone']                           = 'Geo Zone';
$_['entry_status']                             = 'Status';
$_['entry_sort_order']                         = 'Sort Order';
$_['entry_residential']                         = 'Residential Delivery';

// Help
$_['help_length_class']                        = 'Set to inches or centimeters.';
$_['help_display_time']                        = 'By Enabling this, you will get requst and reponse log in Orders Page';
$_['help_display_weight']                      = 'Do you want to display the shipping weight? (e.g. Delivery Weight : 2.7674 kg)';
$_['help_weight_class']                        = 'Set to kilograms or pounds.';

// Error
$_['error_permission']                         = 'Warning: You do not have permission to modify FEDEX Express shipping!';
$_['error_rest_api_key']                       = 'API Key required!';
$_['error_rest_api_sec']                       = 'API Secret required!';
$_['error_rest_acc_num']                       = 'Account No required!';
$_['error_key']                                = 'Web Service Key required!';
$_['error_password']                           = 'Web Service Password required!';
$_['error_account']                            = 'Account No required!';
$_['error_meter']                              = 'Meter No required!';
$_['error_postcode']                           = 'Post Code required!';
$_['error_dimension']                          = 'Width &amp; Height required!';