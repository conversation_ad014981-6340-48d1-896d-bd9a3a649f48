<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/iam/credentials/v1/common.proto

namespace GPBMetadata\Google\Iam\Credentials\V1;

class Common
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        \GPBMetadata\Google\Api\FieldBehavior::initOnce();
        \GPBMetadata\Google\Api\Resource::initOnce();
        \GPBMetadata\Google\Protobuf\Duration::initOnce();
        \GPBMetadata\Google\Protobuf\Timestamp::initOnce();
        $pool->internalAddGeneratedFile(hex2bin(
            "0abb090a26676f6f676c652f69616d2f63726564656e7469616c732f7631" .
            "2f636f6d6d6f6e2e70726f746f1219676f6f676c652e69616d2e63726564" .
            "656e7469616c732e76311a19676f6f676c652f6170692f7265736f757263" .
            "652e70726f746f1a1e676f6f676c652f70726f746f6275662f6475726174" .
            "696f6e2e70726f746f1a1f676f6f676c652f70726f746f6275662f74696d" .
            "657374616d702e70726f746f22a9010a1a47656e65726174654163636573" .
            "73546f6b656e5265717565737412370a046e616d651801200128094229e0" .
            "4102fa41230a2169616d2e676f6f676c65617069732e636f6d2f53657276" .
            "6963654163636f756e7412110a0964656c65676174657318022003280912" .
            "120a0573636f70651804200328094203e04102122b0a086c69666574696d" .
            "6518072001280b32192e676f6f676c652e70726f746f6275662e44757261" .
            "74696f6e22640a1b47656e6572617465416363657373546f6b656e526573" .
            "706f6e736512140a0c6163636573735f746f6b656e180120012809122f0a" .
            "0b6578706972655f74696d6518032001280b321a2e676f6f676c652e7072" .
            "6f746f6275662e54696d657374616d7022730a0f5369676e426c6f625265" .
            "717565737412370a046e616d651801200128094229e04102fa41230a2169" .
            "616d2e676f6f676c65617069732e636f6d2f536572766963654163636f75" .
            "6e7412110a0964656c65676174657318032003280912140a077061796c6f" .
            "616418052001280c4203e0410222370a105369676e426c6f62526573706f" .
            "6e7365120e0a066b65795f696418012001280912130a0b7369676e65645f" .
            "626c6f6218042001280c22720a0e5369676e4a7774526571756573741237" .
            "0a046e616d651801200128094229e04102fa41230a2169616d2e676f6f67" .
            "6c65617069732e636f6d2f536572766963654163636f756e7412110a0964" .
            "656c65676174657318032003280912140a077061796c6f61641805200128" .
            "094203e0410222350a0f5369676e4a7774526573706f6e7365120e0a066b" .
            "65795f696418012001280912120a0a7369676e65645f6a77741802200128" .
            "092292010a1647656e65726174654964546f6b656e526571756573741237" .
            "0a046e616d651801200128094229e04102fa41230a2169616d2e676f6f67" .
            "6c65617069732e636f6d2f536572766963654163636f756e7412110a0964" .
            "656c65676174657318022003280912150a0861756469656e636518032001" .
            "28094203e0410212150a0d696e636c7564655f656d61696c180420012808" .
            "22280a1747656e65726174654964546f6b656e526573706f6e7365120d0a" .
            "05746f6b656e18012001280942e7010a23636f6d2e676f6f676c652e636c" .
            "6f75642e69616d2e63726564656e7469616c732e7631421949414d437265" .
            "64656e7469616c73436f6d6d6f6e50726f746f50015a44676f6f676c652e" .
            "676f6c616e672e6f72672f67656e70726f746f2f676f6f676c6561706973" .
            "2f69616d2f63726564656e7469616c732f76313b63726564656e7469616c" .
            "73f80101ea41590a2169616d2e676f6f676c65617069732e636f6d2f5365" .
            "72766963654163636f756e74123470726f6a656374732f7b70726f6a6563" .
            "747d2f736572766963654163636f756e74732f7b736572766963655f6163" .
            "636f756e747d620670726f746f33"
        ), true);

        static::$is_initialized = true;
    }
}

