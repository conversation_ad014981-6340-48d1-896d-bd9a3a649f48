<!DOCTYPE html>
<html dir="{{ direction }}" lang="{{ lang }}">
<head>
    <meta charset="UTF-8" />
    <title>{{ heading_title }}</title>
    <base href="{{ base }}" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
    <meta name="robots" content="noindex, nofollow" />
    <link href="view/stylesheet/bootstrap.css" rel="stylesheet" media="screen" />
    <link href="view/stylesheet/font-awesome.min.css" rel="stylesheet" type="text/css" />
    <link href="view/javascript/jquery/datetimepicker/bootstrap-datetimepicker.min.css" rel="stylesheet" media="screen" />
    <link href="view/stylesheet/stylesheet.css" rel="stylesheet" type="text/css" />
    <script src="view/javascript/jquery/jquery-2.1.1.min.js" type="text/javascript"></script>
    <script src="view/javascript/bootstrap/js/bootstrap.min.js" type="text/javascript"></script>
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            padding: 20px;
        }
        
        .two-factor-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 500px;
            width: 100%;
            text-align: center;
        }
        
        .logo {
            margin-bottom: 30px;
        }
        
        .logo h1 {
            color: #333;
            font-size: 28px;
            font-weight: 700;
            margin: 0;
        }
        
        .verification-methods {
            margin: 30px 0;
        }
        
        .method-tab {
            display: inline-block;
            padding: 10px 20px;
            margin: 5px;
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .method-tab:hover {
            background: #e9ecef;
            transform: translateY(-2px);
        }
        
        .method-tab.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }
        
        .method-content {
            display: none;
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        .method-content.active {
            display: block;
        }
        
        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }
        
        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #007bff;
            outline: none;
            box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
        }
        
        .btn {
            padding: 12px 30px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 5px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 123, 255, 0.3);
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-info {
            background: #17a2b8;
            color: white;
        }
        
        .alert {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .trust-device {
            margin: 20px 0;
            text-align: left;
        }
        
        .trust-device label {
            display: flex;
            align-items: center;
            cursor: pointer;
            font-weight: 500;
        }
        
        .trust-device input[type="checkbox"] {
            margin-left: 10px;
            transform: scale(1.2);
        }
        
        .security-info {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 10px;
            padding: 15px;
            margin: 20px 0;
            font-size: 14px;
            color: #1565c0;
        }
        
        .code-input {
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            letter-spacing: 5px;
            font-family: 'Courier New', monospace;
        }
        
        @media (max-width: 576px) {
            .two-factor-container {
                padding: 20px;
                margin: 10px;
            }
            
            .method-tab {
                display: block;
                margin: 5px 0;
            }
        }
    </style>
</head>
<body>
    <div class="two-factor-container">
        <div class="logo">
            <h1><i class="fa fa-shield"></i> {{ heading_title }}</h1>
            <p>{{ text_verification }}</p>
        </div>

        {% if error_warning %}
            <div class="alert alert-danger">
                <i class="fa fa-exclamation-circle"></i> {{ error_warning }}
            </div>
        {% endif %}

        {% if success %}
            <div class="alert alert-success">
                <i class="fa fa-check-circle"></i> {{ success }}
            </div>
        {% endif %}

        <form action="{{ action }}" method="post" id="two-factor-form">
            <input type="hidden" name="verification_method" id="verification_method" value="totp">

            <!-- طرق التحقق -->
            <div class="verification-methods">
                <div class="method-tab active" data-method="totp">
                    <i class="fa fa-mobile"></i> {{ text_totp }}
                </div>
                
                {% if sms_enabled %}
                <div class="method-tab" data-method="sms">
                    <i class="fa fa-comment"></i> {{ text_sms }}
                </div>
                {% endif %}
                
                {% if email_enabled %}
                <div class="method-tab" data-method="email">
                    <i class="fa fa-envelope"></i> {{ text_email }}
                </div>
                {% endif %}
                
                <div class="method-tab" data-method="backup">
                    <i class="fa fa-key"></i> {{ text_backup }}
                </div>
            </div>

            <!-- محتوى TOTP -->
            <div class="method-content active" id="totp-content">
                <div class="form-group">
                    <label for="totp_code">{{ entry_code }}</label>
                    <input type="text" name="totp_code" id="totp_code" class="form-control code-input" placeholder="000000" maxlength="6" autocomplete="off">
                </div>
                <div class="security-info">
                    <i class="fa fa-info-circle"></i>
                    أدخل الرمز المكون من 6 أرقام من تطبيق المصادقة الخاص بك
                </div>
            </div>

            <!-- محتوى SMS -->
            {% if sms_enabled %}
            <div class="method-content" id="sms-content">
                <div class="form-group">
                    <label for="sms_code">{{ entry_code }}</label>
                    <input type="text" name="sms_code" id="sms_code" class="form-control code-input" placeholder="000000" maxlength="6" autocomplete="off">
                </div>
                <button type="button" class="btn btn-info" id="send-sms">
                    <i class="fa fa-paper-plane"></i> {{ button_send_sms }}
                </button>
                <div class="security-info">
                    <i class="fa fa-info-circle"></i>
                    سيتم إرسال رمز التحقق إلى رقم هاتفك المسجل
                </div>
            </div>
            {% endif %}

            <!-- محتوى Email -->
            {% if email_enabled %}
            <div class="method-content" id="email-content">
                <div class="form-group">
                    <label for="email_code">{{ entry_code }}</label>
                    <input type="text" name="email_code" id="email_code" class="form-control code-input" placeholder="000000" maxlength="6" autocomplete="off">
                </div>
                <button type="button" class="btn btn-info" id="send-email">
                    <i class="fa fa-paper-plane"></i> {{ button_send_email }}
                </button>
                <div class="security-info">
                    <i class="fa fa-info-circle"></i>
                    سيتم إرسال رمز التحقق إلى بريدك الإلكتروني
                </div>
            </div>
            {% endif %}

            <!-- محتوى Backup -->
            <div class="method-content" id="backup-content">
                <div class="form-group">
                    <label for="backup_code">{{ entry_backup_code }}</label>
                    <input type="text" name="backup_code" id="backup_code" class="form-control code-input" placeholder="12345678" maxlength="8" autocomplete="off">
                </div>
                <div class="security-info">
                    <i class="fa fa-warning"></i>
                    استخدم أحد رموز النسخ الاحتياطي التي حفظتها عند تفعيل المصادقة الثنائية
                </div>
            </div>

            <!-- خيار الثقة بالجهاز -->
            <div class="trust-device">
                <label>
                    <i class="fa fa-laptop"></i> {{ text_trust_device }}
                    <input type="checkbox" name="trust_device" value="1">
                </label>
            </div>

            <!-- أزرار التحكم -->
            <div class="form-actions">
                <button type="submit" class="btn btn-primary">
                    <i class="fa fa-check"></i> {{ button_verify }}
                </button>
                <a href="{{ cancel }}" class="btn btn-secondary">
                    <i class="fa fa-times"></i> {{ button_cancel }}
                </a>
            </div>
        </form>
    </div>

    <script>
        $(document).ready(function() {
            // تبديل طرق التحقق
            $('.method-tab').click(function() {
                var method = $(this).data('method');
                
                // تحديث التبويبات
                $('.method-tab').removeClass('active');
                $(this).addClass('active');
                
                // تحديث المحتوى
                $('.method-content').removeClass('active');
                $('#' + method + '-content').addClass('active');
                
                // تحديث الحقل المخفي
                $('#verification_method').val(method);
                
                // مسح الحقول
                $('input[type="text"]').val('');
            });

            // إرسال SMS
            $('#send-sms').click(function() {
                var btn = $(this);
                btn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> جاري الإرسال...');
                
                $.ajax({
                    url: 'index.php?route=common/two_factor_verify/sendSMS',
                    type: 'POST',
                    dataType: 'json',
                    success: function(json) {
                        if (json.success) {
                            alert(json.success);
                        } else if (json.error) {
                            alert(json.error);
                        }
                    },
                    error: function() {
                        alert('حدث خطأ في الإرسال');
                    },
                    complete: function() {
                        btn.prop('disabled', false).html('<i class="fa fa-paper-plane"></i> {{ button_send_sms }}');
                    }
                });
            });

            // إرسال Email
            $('#send-email').click(function() {
                var btn = $(this);
                btn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> جاري الإرسال...');
                
                $.ajax({
                    url: 'index.php?route=common/two_factor_verify/sendEmail',
                    type: 'POST',
                    dataType: 'json',
                    success: function(json) {
                        if (json.success) {
                            alert(json.success);
                        } else if (json.error) {
                            alert(json.error);
                        }
                    },
                    error: function() {
                        alert('حدث خطأ في الإرسال');
                    },
                    complete: function() {
                        btn.prop('disabled', false).html('<i class="fa fa-paper-plane"></i> {{ button_send_email }}');
                    }
                });
            });

            // تحسين إدخال الأرقام
            $('.code-input').on('input', function() {
                this.value = this.value.replace(/[^0-9]/g, '');
            });

            // التركيز التلقائي
            $('#totp_code').focus();
        });
    </script>
</body>
</html>
