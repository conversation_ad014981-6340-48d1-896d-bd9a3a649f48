{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="submit" form="accounts\journal_security_advanced-form" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fa fa-save"></i></button>
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="close" data-dismiss="alert">&times;</button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="close" data-dismiss="alert">&times;</button>
      </div>
    {{% endif %}}
    
    <div class="panel panel-default">
      <div class="panel-heading"><i class="fa fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}bar-chart{% elif screen_type == 'settings' %}cog{% else %}pencil{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="panel-body">
        <form id="accounts\journal_security_advanced-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-access_attempts_url">{{ text_access_attempts_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="access_attempts_url" value="{{ access_attempts_url }}" placeholder="{{ text_access_attempts_url }}" id="input-access_attempts_url" class="form-control" />
              {% if error_access_attempts_url %}
                <div class="invalid-feedback">{{ error_access_attempts_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="invalid-feedback">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-prevent_delete_url">{{ text_prevent_delete_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="prevent_delete_url" value="{{ prevent_delete_url }}" placeholder="{{ text_prevent_delete_url }}" id="input-prevent_delete_url" class="form-control" />
              {% if error_prevent_delete_url %}
                <div class="invalid-feedback">{{ error_prevent_delete_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-prevent_edit_url">{{ text_prevent_edit_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="prevent_edit_url" value="{{ prevent_edit_url }}" placeholder="{{ text_prevent_edit_url }}" id="input-prevent_edit_url" class="form-control" />
              {% if error_prevent_edit_url %}
                <div class="invalid-feedback">{{ error_prevent_edit_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-secure_url">{{ text_secure_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="secure_url" value="{{ secure_url }}" placeholder="{{ text_secure_url }}" id="input-secure_url" class="form-control" />
              {% if error_secure_url %}
                <div class="invalid-feedback">{{ error_secure_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-security_report_url">{{ text_security_report_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="security_report_url" value="{{ security_report_url }}" placeholder="{{ text_security_report_url }}" id="input-security_report_url" class="form-control" />
              {% if error_security_report_url %}
                <div class="invalid-feedback">{{ error_security_report_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-unsecure_url">{{ text_unsecure_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="unsecure_url" value="{{ unsecure_url }}" placeholder="{{ text_unsecure_url }}" id="input-unsecure_url" class="form-control" />
              {% if error_unsecure_url %}
                <div class="invalid-feedback">{{ error_unsecure_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="invalid-feedback">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}