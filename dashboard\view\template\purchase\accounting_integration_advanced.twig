{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="submit" form="purchase\accounting_integration_advanced-form" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fas fa-save"></i></button>
        <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-light"><i class="fas fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    
    <div class="card">
      <div class="card-header"><i class="fas fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}chart-bar{% elif screen_type == 'settings' %}cog{% else %}pencil-alt{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="card-body">
        <form id="purchase\accounting_integration_advanced-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-can_generate_entries">{{ text_can_generate_entries }}</label>
            <div class="col-sm-10">
              <input type="text" name="can_generate_entries" value="{{ can_generate_entries }}" placeholder="{{ text_can_generate_entries }}" id="input-can_generate_entries" class="form-control" />
              {% if error_can_generate_entries %}
                <div class="invalid-feedback">{{ error_can_generate_entries }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-can_sync">{{ text_can_sync }}</label>
            <div class="col-sm-10">
              <input type="text" name="can_sync" value="{{ can_sync }}" placeholder="{{ text_can_sync }}" id="input-can_sync" class="form-control" />
              {% if error_can_sync %}
                <div class="invalid-feedback">{{ error_can_sync }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-can_update_settings">{{ text_can_update_settings }}</label>
            <div class="col-sm-10">
              <input type="text" name="can_update_settings" value="{{ can_update_settings }}" placeholder="{{ text_can_update_settings }}" id="input-can_update_settings" class="form-control" />
              {% if error_can_update_settings %}
                <div class="invalid-feedback">{{ error_can_update_settings }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-generate_entries_url">{{ text_generate_entries_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="generate_entries_url" value="{{ generate_entries_url }}" placeholder="{{ text_generate_entries_url }}" id="input-generate_entries_url" class="form-control" />
              {% if error_generate_entries_url %}
                <div class="invalid-feedback">{{ error_generate_entries_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="invalid-feedback">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-products">{{ text_products }}</label>
            <div class="col-sm-10">
              <input type="text" name="products" value="{{ products }}" placeholder="{{ text_products }}" id="input-products" class="form-control" />
              {% if error_products %}
                <div class="invalid-feedback">{{ error_products }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-report_url">{{ text_report_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="report_url" value="{{ report_url }}" placeholder="{{ text_report_url }}" id="input-report_url" class="form-control" />
              {% if error_report_url %}
                <div class="invalid-feedback">{{ error_report_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-settings_url">{{ text_settings_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="settings_url" value="{{ settings_url }}" placeholder="{{ text_settings_url }}" id="input-settings_url" class="form-control" />
              {% if error_settings_url %}
                <div class="invalid-feedback">{{ error_settings_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-status_url">{{ text_status_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="status_url" value="{{ status_url }}" placeholder="{{ text_status_url }}" id="input-status_url" class="form-control" />
              {% if error_status_url %}
                <div class="invalid-feedback">{{ error_status_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-suppliers">{{ text_suppliers }}</label>
            <div class="col-sm-10">
              <input type="text" name="suppliers" value="{{ suppliers }}" placeholder="{{ text_suppliers }}" id="input-suppliers" class="form-control" />
              {% if error_suppliers %}
                <div class="invalid-feedback">{{ error_suppliers }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sync_inventory_url">{{ text_sync_inventory_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="sync_inventory_url" value="{{ sync_inventory_url }}" placeholder="{{ text_sync_inventory_url }}" id="input-sync_inventory_url" class="form-control" />
              {% if error_sync_inventory_url %}
                <div class="invalid-feedback">{{ error_sync_inventory_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-update_settings_url">{{ text_update_settings_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="update_settings_url" value="{{ update_settings_url }}" placeholder="{{ text_update_settings_url }}" id="input-update_settings_url" class="form-control" />
              {% if error_update_settings_url %}
                <div class="invalid-feedback">{{ error_update_settings_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-update_wac_url">{{ text_update_wac_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="update_wac_url" value="{{ update_wac_url }}" placeholder="{{ text_update_wac_url }}" id="input-update_wac_url" class="form-control" />
              {% if error_update_wac_url %}
                <div class="invalid-feedback">{{ error_update_wac_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="invalid-feedback">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-validate_url">{{ text_validate_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="validate_url" value="{{ validate_url }}" placeholder="{{ text_validate_url }}" id="input-validate_url" class="form-control" />
              {% if error_validate_url %}
                <div class="invalid-feedback">{{ error_validate_url }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}