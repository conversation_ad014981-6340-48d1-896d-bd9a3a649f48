{{ header }}
{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="button" data-bs-toggle="tooltip" title="{{ button_filter }}" onclick="$('#filter-stock').toggleClass('d-none');" class="btn btn-light d-md-none d-lg-none"><i class="fas fa-filter"></i></button>
        <a href="{{ add }}" data-bs-toggle="tooltip" title="{{ button_add }}" class="btn btn-primary"><i class="fas fa-plus"></i></a>
        <button type="submit" form="form-stock" formaction="{{ delete }}" data-bs-toggle="tooltip" title="{{ button_delete }}" onclick="return confirm('{{ text_confirm_delete }}');" class="btn btn-danger"><i class="fas fa-trash-alt"></i></button>
        <a href="{{ export }}" data-bs-toggle="tooltip" title="{{ button_export }}" class="btn btn-success"><i class="fas fa-file-excel"></i></a>
        <a href="{{ reorder_report }}" data-bs-toggle="tooltip" title="{{ button_reorder_report }}" class="btn btn-warning"><i class="fas fa-shopping-cart"></i></a>
        <a href="{{ overstock_report }}" data-bs-toggle="tooltip" title="{{ button_overstock_report }}" class="btn btn-info"><i class="fas fa-boxes"></i></a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ol class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
          <li class="breadcrumb-item"><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{ error_warning }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {% endif %}
    {% if success %}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{ success }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {% endif %}
    <div class="row">
      <div id="filter-stock" class="col-lg-3 col-md-12 order-lg-last d-none d-lg-block mb-3">
        <div class="card">
          <div class="card-header"><i class="fas fa-filter"></i> {{ button_filter }}</div>
          <div class="card-body">
            <div class="mb-3">
              <label for="input-product" class="form-label">{{ entry_product }}</label>
              <input type="text" name="filter_product" value="{{ filter_product }}" placeholder="{{ entry_product }}" id="input-product" class="form-control"/>
            </div>
            <div class="mb-3">
              <label for="input-branch" class="form-label">{{ entry_branch }}</label>
              <select name="filter_branch" id="input-branch" class="form-select">
                <option value="">{{ text_select }}</option>
                {% for branch in branches %}
                  <option value="{{ branch.branch_id }}" {% if branch.branch_id == filter_branch %}selected{% endif %}>{{ branch.name }}</option>
                {% endfor %}
              </select>
            </div>
            <div class="mb-3">
              <label for="input-status" class="form-label">{{ entry_status }}</label>
              <select name="filter_status" id="input-status" class="form-select">
                <option value="">{{ text_select }}</option>
                <option value="1" {% if filter_status == '1' %}selected{% endif %}>{{ text_enabled }}</option>
                <option value="0" {% if filter_status == '0' %}selected{% endif %}>{{ text_disabled }}</option>
              </select>
            </div>
            <div class="text-end">
              <button type="button" id="button-filter" class="btn btn-primary"><i class="fas fa-filter"></i> {{ button_filter }}</button>
            </div>
          </div>
        </div>
      </div>
      <div class="col-lg-9 col-md-12">
        <div class="card">
          <div class="card-header"><i class="fas fa-list"></i> {{ text_list }}</div>
          <div class="card-body">
            <form id="form-stock" method="post">
              <div class="table-responsive">
                <table class="table table-bordered table-hover">
                  <thead>
                    <tr>
                      <td style="width: 1px;" class="text-center"><input type="checkbox" onclick="$('input[name*=\'selected\']').prop('checked', $(this).prop('checked'));" class="form-check-input"/></td>
                      <td class="text-start"><a href="{{ sort_product }}" {% if sort == 'pd.name' %}class="{{ order|lower }}"{% endif %}>{{ column_product }}</a></td>
                      <td class="text-start"><a href="{{ sort_branch }}" {% if sort == 'b.name' %}class="{{ order|lower }}"{% endif %}>{{ column_branch }}</a></td>
                      <td class="text-end"><a href="{{ sort_minimum }}" {% if sort == 'sl.minimum_stock' %}class="{{ order|lower }}"{% endif %}>{{ column_minimum_stock }}</a></td>
                      <td class="text-end"><a href="{{ sort_reorder }}" {% if sort == 'sl.reorder_point' %}class="{{ order|lower }}"{% endif %}>{{ column_reorder_point }}</a></td>
                      <td class="text-end"><a href="{{ sort_maximum }}" {% if sort == 'sl.maximum_stock' %}class="{{ order|lower }}"{% endif %}>{{ column_maximum_stock }}</a></td>
                      <td class="text-end"><a href="{{ sort_current }}" {% if sort == 'current_stock' %}class="{{ order|lower }}"{% endif %}>{{ column_current_stock }}</a></td>
                      <td class="text-center">{{ column_stock_status }}</td>
                      <td class="text-start"><a href="{{ sort_status }}" {% if sort == 'sl.status' %}class="{{ order|lower }}"{% endif %}>{{ column_status }}</a></td>
                      <td class="text-end">{{ column_action }}</td>
                    </tr>
                  </thead>
                  <tbody>
                    {% if stock_levels %}
                      {% for stock_level in stock_levels %}
                        <tr>
                          <td class="text-center"><input type="checkbox" name="selected[]" value="{{ stock_level.stock_level_id }}" class="form-check-input"/></td>
                          <td class="text-start">{{ stock_level.product_name }}</td>
                          <td class="text-start">{{ stock_level.branch_name }}</td>
                          <td class="text-end">{{ stock_level.minimum_stock }} {{ stock_level.unit_name }}</td>
                          <td class="text-end">{{ stock_level.reorder_point }} {{ stock_level.unit_name }}</td>
                          <td class="text-end">{{ stock_level.maximum_stock }} {{ stock_level.unit_name }}</td>
                          <td class="text-end">{{ stock_level.current_stock }} {{ stock_level.unit_name }}</td>
                          <td class="text-center">
                            {% if stock_level.stock_status == 'low' %}
                              <span class="badge bg-danger">{{ text_low }}</span>
                            {% elseif stock_level.stock_status == 'high' %}
                              <span class="badge bg-warning text-dark">{{ text_high }}</span>
                            {% else %}
                              <span class="badge bg-success">{{ text_normal }}</span>
                            {% endif %}
                          </td>
                          <td class="text-start">
                            {% if stock_level.status %}
                              <span class="badge bg-success">{{ text_enabled }}</span>
                            {% else %}
                              <span class="badge bg-danger">{{ text_disabled }}</span>
                            {% endif %}
                          </td>
                          <td class="text-end">
                            <a href="{{ stock_level.edit }}" data-bs-toggle="tooltip" title="{{ button_edit }}" class="btn btn-primary"><i class="fas fa-pencil-alt"></i></a>
                          </td>
                        </tr>
                      {% endfor %}
                    {% else %}
                      <tr>
                        <td class="text-center" colspan="10">{{ text_no_results }}</td>
                      </tr>
                    {% endif %}
                  </tbody>
                </table>
              </div>
            </form>
            <div class="row">
              <div class="col-sm-6 text-start">{{ pagination }}</div>
              <div class="col-sm-6 text-end">{{ results }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<script type="text/javascript">
$('#button-filter').on('click', function() {
	var url = 'index.php?route=inventory/stock_level&user_token={{ user_token }}';

	var filter_product = $('#input-product').val();
	if (filter_product) {
		url += '&filter_product=' + encodeURIComponent(filter_product);
	}

	var filter_branch = $('#input-branch').val();
	if (filter_branch) {
		url += '&filter_branch=' + encodeURIComponent(filter_branch);
	}

	var filter_status = $('#input-status').val();
	if (filter_status !== '') {
		url += '&filter_status=' + encodeURIComponent(filter_status);
	}

	location = url;
});
</script>
{{ footer }}
