{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="submit" form="form-shipping" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fa fa-save"></i></button>
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a></div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pencil"></i> {{ text_edit }}</h3>
      </div>
      <div class="panel-body">
        <form action="{{ action }}" method="post" enctype="multipart/form-data" id="form-shipping" class="form-horizontal">
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-key">{{ entry_key }}</label>
            <div class="col-sm-10">
              <input type="text" name="shipping_fedex_key" value="{{ shipping_fedex_key }}" placeholder="{{ entry_key }}" id="input-key" class="form-control" />
              {% if error_key %}
              <div class="text-danger">{{ error_key }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-password">{{ entry_password }}</label>
            <div class="col-sm-10">
              <input type="text" name="shipping_fedex_password" value="{{ shipping_fedex_password }}" placeholder="{{ entry_password }}" id="input-password" class="form-control" />
              {% if error_password %}
              <div class="text-danger">{{ error_password }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-account">{{ entry_account }}</label>
            <div class="col-sm-10">
              <input type="text" name="shipping_fedex_account" value="{{ shipping_fedex_account }}" placeholder="{{ entry_account }}" id="input-account" class="form-control" />
              {% if error_account %}
              <div class="text-danger">{{ error_account }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-meter">{{ entry_meter }}</label>
            <div class="col-sm-10">
              <input type="text" name="shipping_fedex_meter" value="{{ shipping_fedex_meter }}" placeholder="{{ entry_meter }}" id="input-meter" class="form-control" />
              {% if error_meter %}
              <div class="text-danger">{{ error_meter }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-postcode">{{ entry_postcode }}</label>
            <div class="col-sm-10">
              <input type="text" name="shipping_fedex_postcode" value="{{ shipping_fedex_postcode }}" placeholder="{{ entry_postcode }}" id="input-postcode" class="form-control" />
              {% if error_postcode %}
              <div class="text-danger">{{ error_postcode }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label">{{ entry_test }}</label>
            <div class="col-sm-10">
              <label class="radio-inline">
                {% if shipping_fedex_test %}
                <input type="radio" name="shipping_fedex_test" value="1" checked="checked" />
                {{ text_yes }}
                {% else %}
                <input type="radio" name="shipping_fedex_test" value="1" />
                {{ text_yes }}
                {% endif %}
              </label>
              <label class="radio-inline">
                {% if not shipping_fedex_test %}
                <input type="radio" name="shipping_fedex_test" value="0" checked="checked" />
                {{ text_no }}
                {% else %}
                <input type="radio" name="shipping_fedex_test" value="0" />
                {{ text_no }}
                {% endif %}
              </label>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label">{{ entry_service }}</label>
            <div class="col-sm-10">
              <div class="well well-sm" style="height: 150px; overflow: auto;">
                {% for service in services %}
                <div class="checkbox">
                  <label>
                    {% if service.value in shipping_fedex_service %}
                    <input type="checkbox" name="shipping_fedex_service[]" value="{{ service.value }}" checked="checked" />
                    {{ service.text }}
                    {% else %}
                    <input type="checkbox" name="shipping_fedex_service[]" value="{{ service.value }}" />
                    {{ service.text }}
                    {% endif %}
                  </label>
                </div>
                {% endfor %}
              </div>
              <button type="button" onclick="$(this).parent().find(':checkbox').prop('checked', true);" class="btn btn-link">{{ text_select_all }}</button> / <button type="button" onclick="$(this).parent().find(':checkbox').prop('checked', false);" class="btn btn-link">{{ text_unselect_all }}</button></div>
          </div>
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-length">{{ entry_dimension }}</label>
            <div class="col-sm-10">
              <div class="row">
                <div class="col-sm-4">
                  <input type="text" name="shipping_fedex_length" value="{{ shipping_fedex_length }}" placeholder="{{ entry_length }}" id="input-length" class="form-control" />
                </div>
                <div class="col-sm-4">
                  <input type="text" name="shipping_fedex_width" value="{{ shipping_fedex_width }}" placeholder="{{ entry_width }}" id="input-width" class="form-control" />
                </div>
                <div class="col-sm-4">
                  <input type="text" name="shipping_fedex_height" value="{{ shipping_fedex_height }}" placeholder="{{ entry_height }}" id="input-height" class="form-control" />
                </div>
              </div>
              {% if error_dimension %}
              <div class="text-danger">{{ error_dimension }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-length-class"><span data-toggle="tooltip" title="{{ help_length_class }}">{{ entry_length_class }}</span></label>
            <div class="col-sm-10">
              <select name="shipping_fedex_length_class_id" id="input-length-class" class="form-control">
                {% for length_class in length_classes %}
                {% if length_class.length_class_id == shipping_fedex_length_class_id %}
                <option value="{{ length_class.length_class_id }}" selected="selected">{{ length_class.title }}</option>
                {% else %}
                <option value="{{ length_class.length_class_id }}">{{ length_class.title }}</option>
                {% endif %}
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-dropoff-type">{{ entry_dropoff_type }}</label>
            <div class="col-sm-10">
              <select name="shipping_fedex_dropoff_type" id="input-dropoff-type" class="form-control">
                {% if shipping_fedex_dropoff_type == 'REGULAR_PICKUP' %}
                <option value="REGULAR_PICKUP" selected="selected">{{ text_regular_pickup }}</option>
                {% else %}
                <option value="REGULAR_PICKUP">{{ text_regular_pickup }}</option>
                {% endif %}
                {% if shipping_fedex_dropoff_type == 'REQUEST_COURIER' %}
                <option value="REQUEST_COURIER" selected="selected">{{ text_request_courier }}</option>
                {% else %}
                <option value="REQUEST_COURIER">{{ text_request_courier }}</option>
                {% endif %}
                {% if shipping_fedex_dropoff_type == 'DROP_BOX' %}
                <option value="DROP_BOX" selected="selected">{{ text_drop_box }}</option>
                {% else %}
                <option value="DROP_BOX">{{ text_drop_box }}</option>
                {% endif %}
                {% if shipping_fedex_dropoff_type == 'BUSINESS_SERVICE_CENTER' %}
                <option value="BUSINESS_SERVICE_CENTER" selected="selected">{{ text_business_service_center }}</option>
                {% else %}
                <option value="BUSINESS_SERVICE_CENTER">{{ text_business_service_center }}</option>
                {% endif %}
                {% if shipping_fedex_dropoff_type == 'STATION' %}
                <option value="STATION" selected="selected">{{ text_station }}</option>
                {% else %}
                <option value="STATION">{{ text_station }}</option>
                {% endif %}
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-packaging-type">{{ entry_packaging_type }}</label>
            <div class="col-sm-10">
              <select name="shipping_fedex_packaging_type" id="input-packaging-type" class="form-control">
                {% if shipping_fedex_packaging_type == 'FEDEX_ENVELOPE' %}
                <option value="FEDEX_ENVELOPE" selected="selected">{{ text_fedex_envelope }}</option>
                {% else %}
                <option value="FEDEX_ENVELOPE">{{ text_fedex_envelope }}</option>
                {% endif %}
                {% if shipping_fedex_packaging_type == 'FEDEX_PAK' %}
                <option value="FEDEX_PAK" selected="selected">{{ text_fedex_pak }}</option>
                {% else %}
                <option value="FEDEX_PAK">{{ text_fedex_pak }}</option>
                {% endif %}
                {% if shipping_fedex_packaging_type == 'FEDEX_BOX' %}
                <option value="FEDEX_BOX" selected="selected">{{ text_fedex_box }}</option>
                {% else %}
                <option value="FEDEX_BOX">{{ text_fedex_box }}</option>
                {% endif %}
                {% if shipping_fedex_packaging_type == 'FEDEX_TUBE' %}
                <option value="FEDEX_TUBE" selected="selected">{{ text_fedex_tube }}</option>
                {% else %}
                <option value="FEDEX_TUBE">{{ text_fedex_tube }}</option>
                {% endif %}
                {% if shipping_fedex_packaging_type == 'FEDEX_10KG_BOX' %}
                <option value="FEDEX_10KG_BOX" selected="selected">{{ text_fedex_10kg_box }}</option>
                {% else %}
                <option value="FEDEX_10KG_BOX">{{ text_fedex_10kg_box }}</option>
                {% endif %}
                {% if shipping_fedex_packaging_type == 'FEDEX_25KG_BOX' %}
                <option value="FEDEX_25KG_BOX" selected="selected">{{ text_fedex_25kg_box }}</option>
                {% else %}
                <option value="FEDEX_25KG_BOX">{{ text_fedex_25kg_box }}</option>
                {% endif %}
                {% if shipping_fedex_packaging_type == 'YOUR_PACKAGING' %}
                <option value="YOUR_PACKAGING" selected="selected">{{ text_your_packaging }}</option>
                {% else %}
                <option value="YOUR_PACKAGING">{{ text_your_packaging }}</option>
                {% endif %}
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-rate-type">{{ entry_rate_type }}</label>
            <div class="col-sm-10">
              <select name="shipping_fedex_rate_type" id="input-rate-type" class="form-control">
                {% if shipping_fedex_rate_type == 'LIST' %}
                <option value="LIST" selected="selected">{{ text_list_rate }}</option>
                {% else %}
                <option value="LIST">{{ text_list_rate }}</option>
                {% endif %}
                {% if shipping_fedex_rate_type == 'ACCOUNT' %}
                <option value="ACCOUNT" selected="selected">{{ text_account_rate }}</option>
                {% else %}
                <option value="ACCOUNT">{{ text_account_rate }}</option>
                {% endif %}
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label"><span data-toggle="tooltip" title="{{ help_display_time }}">{{ entry_display_time }}</span></label>
            <div class="col-sm-10">
              <label class="radio-inline">
                {% if shipping_fedex_display_time %}
                <input type="radio" name="shipping_fedex_display_time" value="1" checked="checked" />
                {{ text_yes }}
                {% else %}
                <input type="radio" name="shipping_fedex_display_time" value="1" />
                {{ text_yes }}
                {% endif %}
              </label>
              <label class="radio-inline">
                {% if not shipping_fedex_display_time %}
                <input type="radio" name="shipping_fedex_display_time" value="0" checked="checked" />
                {{ text_no }}
                {% else %}
                <input type="radio" name="shipping_fedex_display_time" value="0" />
                {{ text_no }}
                {% endif %}
              </label>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label"><span data-toggle="tooltip" title="{{ help_display_weight }}">{{ entry_display_weight }}</span></label>
            <div class="col-sm-10">
              <label class="radio-inline">
                {% if shipping_fedex_display_weight %}
                <input type="radio" name="shipping_fedex_display_weight" value="1" checked="checked" />
                {{ text_yes }}
                {% else %}
                <input type="radio" name="shipping_fedex_display_weight" value="1" />
                {{ text_yes }}
                {% endif %}
              </label>
              <label class="radio-inline">
                {% if not shipping_fedex_display_weight %}
                <input type="radio" name="shipping_fedex_display_weight" value="0" checked="checked" />
                {{ text_no }}
                {% else %}
                <input type="radio" name="shipping_fedex_display_weight" value="0" />
                {{ text_no }}
                {% endif %}
              </label>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-weight-class"><span data-toggle="tooltip" title="{{ help_weight_class }}">{{ entry_weight_class }}</span></label>
            <div class="col-sm-10">
              <select name="shipping_fedex_weight_class_id" id="input-weight-class" class="form-control">
                {% for weight_class in weight_classes %}
                {% if weight_class.weight_class_id == shipping_fedex_weight_class_id %}
                <option value="{{ weight_class.weight_class_id }}" selected="selected">{{ weight_class.title }}</option>
                {% else %}
                <option value="{{ weight_class.weight_class_id }}">{{ weight_class.title }}</option>
                {% endif %}
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-tax-class">{{ entry_tax_class }}</label>
            <div class="col-sm-10">
              <select name="shipping_fedex_tax_class_id" id="input-tax-class" class="form-control">
                <option value="0">{{ text_none }}</option>
                {% for tax_class in tax_classes %}
                {% if tax_class.tax_class_id == shipping_fedex_tax_class_id %}
                <option value="{{ tax_class.tax_class_id }}" selected="selected">{{ tax_class.title }}</option>
                {% else %}
                <option value="{{ tax_class.tax_class_id }}">{{ tax_class.title }}</option>
                {% endif %}
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-geo-zone">{{ entry_geo_zone }}</label>
            <div class="col-sm-10">
              <select name="shipping_fedex_geo_zone_id" id="input-geo-zone" class="form-control">
                <option value="0">{{ text_all_zones }}</option>
                {% for geo_zone in geo_zones %}
                {% if geo_zone.geo_zone_id == shipping_fedex_geo_zone_id %}
                <option value="{{ geo_zone.geo_zone_id }}" selected="selected">{{ geo_zone.name }}</option>
                {% else %}
                <option value="{{ geo_zone.geo_zone_id }}">{{ geo_zone.name }}</option>
                {% endif %}
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-status">{{ entry_status }}</label>
            <div class="col-sm-10">
              <select name="shipping_fedex_status" id="input-status" class="form-control">
                {% if shipping_fedex_status %}
                <option value="1" selected="selected">{{ text_enabled }}</option>
                <option value="0">{{ text_disabled }}</option>
                {% else %}
                <option value="1">{{ text_enabled }}</option>
                <option value="0" selected="selected">{{ text_disabled }}</option>
                {% endif %}
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-sort-order">{{ entry_sort_order }}</label>
            <div class="col-sm-10">
              <input type="text" name="shipping_fedex_sort_order" value="{{ shipping_fedex_sort_order }}" placeholder="{{ entry_sort_order }}" id="input-sort-order" class="form-control" />
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
{{ footer }}
