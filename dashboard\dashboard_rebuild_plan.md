
# خطة إعادة بناء لوحة التحكم (Dashboard)

## 1. الهدف

إعادة بناء لوحة التحكم الرئيسية (`common/dashboard`) لتعتمد بشكل كامل على البيانات الفعلية من `db.txt` (قاعدة البيانات الحقيقية)، مع تعليق جميع مؤشرات الأداء الرئيسية (KPIs) والإحصائيات التي لا يمكن حسابها من الجداول المتاحة حاليًا. سيتم التركيز على عرض البيانات الأساسية بشكل واضح وفعال.

## 2. الوضع الحالي

- **قاعدة البيانات:** ضخمة جدًا (أكثر من 450 جدول) كما هو موضح في `dashboard/db.txt`.
- **المتحكم (`dashboard/controller/common/dashboard.php`):** يحتوي على منطق لعرض عدد كبير من الإحصائيات ومؤشرات الأداء، معظمها معلق بالفعل.
- **الموديل (`dashboard/model/common/dashboard.php`):** يحتوي على عدد هائل من الدوال لجلب البيانات لمؤشرات الأداء، ومعظمها معلق أيضًا.
- **العرض (`dashboard/view/template/common/dashboard`):** غير محدد حاليًا، ولكن سيتم إنشاؤه ليعكس الخطة الجديدة.
- **ملف اللغة (`dashboard/language/ar/common/dashboard.php`):** سيتم إنشاؤه لدعم النصوص في لوحة التحكم الجديدة.

## 3. الخطة المقترحة

### المرحلة الأولى: تعليق الكود غير المستخدم

- **المتحكم (`Controller`):**
    - التأكد من أن جميع استدعاءات الدوال الخاصة بمؤشرات الأداء (KPIs) والإحصائيات غير المعتمدة على `db.txt` معلقة بشكل صحيح.
    - تبسيط منطق المتحكم ليعكس فقط الويدجت (Widgets) التي سيتم عرضها.
- **الموديل (`Model`):**
    - التأكد من أن جميع دوال مؤشرات الأداء (KPIs) غير المستخدمة معلقة.
    - مراجعة الدوال الحالية التي تجلب البيانات الأساسية (مثل `getInventoryStats`, `getSalesStats`, etc.) للتأكد من أنها تعمل بكفاءة مع `db.txt`.

### المرحلة الثانية: إعادة بناء واجهة المستخدم (View)

سيتم إنشاء ملف عرض جديد (`dashboard/view/template/common/dashboard.twig` أو `.tpl`) يركز على عرض الويدجت التالية فقط:

1.  **إحصائيات المخزون (`Inventory Stats`):**
    - إجمالي المنتجات النشطة.
    - عدد المنتجات منخفضة المخزون.
    - عدد المنتجات نافدة المخزون.
    - القيمة الإجمالية للمخزون (باستخدام `average_cost` أو `price`).
2.  **إحصائيات المبيعات (`Sales Stats`):**
    - مبيعات اليوم (الإجمالي وعدد الطلبات).
    - مبيعات الشهر الحالي.
    - مقارنة بسيطة مع اليوم السابق أو الشهر السابق.
3.  **أداء الفروع (`Branch Stats`):**
    - عرض قائمة بالفروع مع إجمالي المبيعات وعدد الطلبات لكل فرع.
4.  **أفضل المنتجات مبيعًا (`Top Selling Products`):**
    - قائمة بأفضل 5 أو 10 منتجات مبيعًا بناءً على الكمية أو الإيرادات.
5.  **إحصائيات التجارة الإلكترونية (`E-commerce Stats`):**
    - عدد الزوار اليومي.
    - معدل التحويل.
    - معدل التخلي عن السلة.

### المرحلة الثالثة: إنشاء ملف اللغة

- إنشاء ملف لغة جديد (`dashboard/language/ar/common/dashboard.php`) يحتوي على جميع النصوص المستخدمة في واجهة المستخدم الجديدة، لضمان سهولة الترجمة والصيانة.

## 4. خطوات التنفيذ

1.  **[ ] مراجعة وتعليق الكود:**
    - مراجعة `dashboard/controller/common/dashboard.php` و `dashboard/model/common/dashboard.php` لتعليق أي كود غير ضروري بشكل نهائي.
2.  **[ ] إنشاء ملف العرض:**
    - إنشاء ملف `dashboard/view/template/common/dashboard.twig` (أو `.tpl`) جديد.
    - تصميم واجهة مستخدم بسيطة وواضحة باستخدام HTML و CSS (يمكن الاستفادة من Bootstrap إذا كان متاحًا في المشروع).
    - تقسيم الواجهة إلى أقسام (Widgets) لكل من الإحصائيات المذكورة أعلاه.
3.  **[ ] ربط البيانات بالعرض:**
    - في المتحكم، التأكد من تمرير البيانات من الموديل إلى العرض بشكل صحيح.
    - في ملف العرض، استخدام متغيرات Twig/TPL لعرض البيانات الديناميكية.
4.  **[ ] إنشاء ملف اللغة:**
    - إنشاء ملف `dashboard/language/ar/common/dashboard.php`.
    - إضافة جميع النصوص الثابتة (مثل عناوين الويدجت، أسماء الحقول، إلخ) إلى ملف اللغة.
    - استدعاء نصوص اللغة في المتحكم والعرض.
5.  **[ ] الاختبار والمراجعة:**
    - اختبار لوحة التحكم الجديدة للتأكد من أن جميع البيانات تُعرض بشكل صحيح.
    - التأكد من عدم وجود أخطاء وأن الأداء جيد.
