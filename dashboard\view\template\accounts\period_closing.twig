{{ header }}{{ column_left }}

<!-- Enterprise Grade Plus CSS for Period Closing -->
<style>
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --danger-color: #e74c3c;
    --warning-color: #f39c12;
    --info-color: #17a2b8;
    --closing-color: #6f42c1;
    --light-bg: #f8f9fa;
    --border-color: #dee2e6;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

.period-closing-container {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: var(--shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.period-closing-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--closing-color), var(--primary-color), var(--secondary-color));
}

.period-closing-header {
    text-align: center;
    border-bottom: 3px solid var(--closing-color);
    padding-bottom: 20px;
    margin-bottom: 30px;
    position: relative;
}

.period-closing-header h2 {
    color: var(--closing-color);
    font-weight: 700;
    font-size: 1.8rem;
    margin: 0;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.period-closing-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.period-closing-step {
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: var(--shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.period-closing-step:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0,0,0,0.1);
}

.period-closing-step::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
}

.period-closing-step.step-1::before { background: var(--info-color); }
.period-closing-step.step-2::before { background: var(--warning-color); }
.period-closing-step.step-3::before { background: var(--success-color); }
.period-closing-step.step-4::before { background: var(--closing-color); }

.period-closing-step h4 {
    color: var(--primary-color);
    margin-bottom: 15px;
    font-size: 1.1rem;
    font-weight: 600;
}

.period-closing-step .step-number {
    position: absolute;
    top: 15px;
    right: 15px;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: var(--closing-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 0.9rem;
}

.period-closing-step .step-description {
    color: #6c757d;
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: 15px;
}

.period-closing-step .step-status {
    padding: 5px 10px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-pending { background: #fff3cd; color: #856404; }
.status-in-progress { background: #d1ecf1; color: #0c5460; }
.status-completed { background: #d4edda; color: #155724; }
.status-failed { background: #f8d7da; color: #721c24; }

.period-closing-form {
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: var(--shadow);
}

.period-closing-form h3 {
    color: var(--closing-color);
    margin-bottom: 20px;
    font-weight: 600;
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 8px;
    display: block;
}

.form-control {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 10px 15px;
    transition: var(--transition);
    width: 100%;
}

.form-control:focus {
    border-color: var(--closing-color);
    box-shadow: 0 0 0 0.2rem rgba(111, 66, 193, 0.25);
    outline: none;
}

.period-closing-summary {
    background: linear-gradient(135deg, #f8f9fa, #ffffff);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: var(--shadow);
}

.period-closing-summary h4 {
    color: var(--closing-color);
    margin-bottom: 15px;
    font-weight: 600;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid var(--border-color);
}

.summary-item:last-child {
    border-bottom: none;
}

.summary-label {
    font-weight: 500;
    color: var(--primary-color);
}

.summary-value {
    font-weight: 600;
    font-family: 'Courier New', monospace;
}

.period-closing-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
    margin-top: 20px;
}

.btn-closing {
    background: linear-gradient(135deg, var(--closing-color), #5a2d91);
    border: none;
    color: white;
    padding: 12px 25px;
    border-radius: var(--border-radius);
    font-weight: 600;
    transition: var(--transition);
    cursor: pointer;
}

.btn-closing:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(111, 66, 193, 0.3);
}

.progress-bar-container {
    background: #e9ecef;
    border-radius: 10px;
    height: 20px;
    margin: 15px 0;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--closing-color), #5a2d91);
    border-radius: 10px;
    transition: width 0.5s ease;
    position: relative;
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 0.8rem;
    font-weight: 600;
}

/* Checklist Styles */
.closing-checklist {
    background: #ffffff;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: var(--shadow);
}

.checklist-item {
    display: flex;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #f0f0f0;
}

.checklist-item:last-child {
    border-bottom: none;
}

.checklist-checkbox {
    margin-right: 15px;
    transform: scale(1.2);
}

.checklist-label {
    flex: 1;
    color: var(--primary-color);
    font-weight: 500;
}

.checklist-status {
    padding: 3px 8px;
    border-radius: 8px;
    font-size: 0.7rem;
    font-weight: 600;
}

/* RTL Support */
[dir="rtl"] .period-closing-step .step-number {
    right: auto;
    left: 15px;
}

[dir="rtl"] .checklist-checkbox {
    margin-right: 0;
    margin-left: 15px;
}

/* Print Styles */
@media print {
    .period-closing-container {
        box-shadow: none;
        border: 1px solid #000;
    }
    
    .btn, .panel-heading {
        display: none !important;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .period-closing-steps {
        grid-template-columns: 1fr;
    }
    
    .period-closing-actions {
        flex-direction: column;
    }
    
    .summary-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
}
</style>

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-{{ direction == 'rtl' ? 'start' : 'end' }}">
        <div class="btn-group" role="group" aria-label="{{ text_actions }}">
          <button type="button" class="btn btn-info btn-lg" onclick="validatePeriod()"
                  data-toggle="tooltip" title="{{ button_validate_period }}">
            <i class="fa fa-check-circle"></i> {{ button_validate }}
          </button>
          <button type="button" class="btn btn-warning btn-lg" onclick="createBackup()"
                  data-toggle="tooltip" title="{{ button_create_backup }}">
            <i class="fa fa-database"></i> {{ button_backup }}
          </button>
          <button type="button" class="btn btn-success btn-lg" onclick="generateReports()"
                  data-toggle="tooltip" title="{{ button_generate_reports }}">
            <i class="fa fa-file-text"></i> {{ button_reports }}
          </button>
          <button type="button" class="btn btn-closing btn-lg" onclick="closePeriod()"
                  data-toggle="tooltip" title="{{ button_close_period }}">
            <i class="fa fa-lock"></i> {{ button_close }}
          </button>
        </div>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li class="breadcrumb-item"><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>

  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible">
      <i class="fa fa-exclamation-triangle"></i>
      {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}

    {% if success %}
    <div class="alert alert-success alert-dismissible">
      <i class="fa fa-check-circle"></i>
      {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}

    <!-- Period Closing Steps -->
    <div class="period-closing-container">
      <div class="period-closing-header">
        <h2>{{ text_closing_process }}</h2>
        <p>{{ text_closing_description }}</p>
      </div>

      <div class="period-closing-steps">
        <div class="period-closing-step step-1">
          <div class="step-number">1</div>
          <h4>{{ text_step_validation }}</h4>
          <div class="step-description">{{ help_step_validation }}</div>
          <div class="step-status status-{{ validation_status }}">{{ validation_status_text }}</div>
        </div>
        
        <div class="period-closing-step step-2">
          <div class="step-number">2</div>
          <h4>{{ text_step_backup }}</h4>
          <div class="step-description">{{ help_step_backup }}</div>
          <div class="step-status status-{{ backup_status }}">{{ backup_status_text }}</div>
        </div>
        
        <div class="period-closing-step step-3">
          <div class="step-number">3</div>
          <h4>{{ text_step_reports }}</h4>
          <div class="step-description">{{ help_step_reports }}</div>
          <div class="step-status status-{{ reports_status }}">{{ reports_status_text }}</div>
        </div>
        
        <div class="period-closing-step step-4">
          <div class="step-number">4</div>
          <h4>{{ text_step_closing }}</h4>
          <div class="step-description">{{ help_step_closing }}</div>
          <div class="step-status status-{{ closing_status }}">{{ closing_status_text }}</div>
        </div>
      </div>

      <!-- Progress Bar -->
      <div class="progress-bar-container">
        <div class="progress-bar" style="width: {{ progress_percentage }}%">
          <div class="progress-text">{{ progress_percentage }}% {{ text_complete }}</div>
        </div>
      </div>
    </div>

    <!-- Period Closing Form -->
    <div class="period-closing-form">
      <h3>{{ text_period_details }}</h3>
      <form id="period-closing-form" method="post">
        <div class="row">
          <div class="col-md-6">
            <div class="form-group">
              <label for="period_name" class="form-label">{{ entry_period_name }}</label>
              <input type="text" name="period_name" id="period_name" value="{{ period_name }}" class="form-control" required>
            </div>
          </div>
          <div class="col-md-3">
            <div class="form-group">
              <label for="period_start_date" class="form-label">{{ entry_period_start_date }}</label>
              <input type="date" name="period_start_date" id="period_start_date" value="{{ period_start_date }}" class="form-control" required>
            </div>
          </div>
          <div class="col-md-3">
            <div class="form-group">
              <label for="period_end_date" class="form-label">{{ entry_period_end_date }}</label>
              <input type="date" name="period_end_date" id="period_end_date" value="{{ period_end_date }}" class="form-control" required>
            </div>
          </div>
        </div>

        <div class="row">
          <div class="col-md-6">
            <div class="form-group">
              <label for="closing_reason" class="form-label">{{ entry_closing_reason }}</label>
              <select name="closing_reason" id="closing_reason" class="form-control" required>
                <option value="">{{ text_select_reason }}</option>
                <option value="monthly"{% if closing_reason == 'monthly' %} selected{% endif %}>{{ text_monthly_closing }}</option>
                <option value="quarterly"{% if closing_reason == 'quarterly' %} selected{% endif %}>{{ text_quarterly_closing }}</option>
                <option value="yearly"{% if closing_reason == 'yearly' %} selected{% endif %}>{{ text_yearly_closing }}</option>
                <option value="special"{% if closing_reason == 'special' %} selected{% endif %}>{{ text_special_closing }}</option>
              </select>
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-group">
              <label for="closing_notes" class="form-label">{{ entry_closing_notes }}</label>
              <textarea name="closing_notes" id="closing_notes" class="form-control" rows="3">{{ closing_notes }}</textarea>
            </div>
          </div>
        </div>
      </form>
    </div>

    <!-- Closing Checklist -->
    <div class="closing-checklist">
      <h4>{{ text_closing_checklist }}</h4>
      {% for item in checklist_items %}
      <div class="checklist-item">
        <input type="checkbox" class="checklist-checkbox" id="check_{{ item.id }}" {% if item.completed %}checked{% endif %}>
        <label for="check_{{ item.id }}" class="checklist-label">{{ item.description }}</label>
        <div class="checklist-status status-{{ item.status }}">{{ item.status_text }}</div>
      </div>
      {% endfor %}
    </div>

    <!-- Period Summary -->
    <div class="period-closing-summary">
      <h4>{{ text_period_summary }}</h4>
      <div class="summary-item">
        <div class="summary-label">{{ text_total_transactions }}</div>
        <div class="summary-value">{{ summary.total_transactions }}</div>
      </div>
      <div class="summary-item">
        <div class="summary-label">{{ text_total_debits }}</div>
        <div class="summary-value">{{ summary.total_debits_formatted }}</div>
      </div>
      <div class="summary-item">
        <div class="summary-label">{{ text_total_credits }}</div>
        <div class="summary-value">{{ summary.total_credits_formatted }}</div>
      </div>
      <div class="summary-item">
        <div class="summary-label">{{ text_balance_difference }}</div>
        <div class="summary-value">{{ summary.balance_difference_formatted }}</div>
      </div>
      <div class="summary-item">
        <div class="summary-label">{{ text_unposted_entries }}</div>
        <div class="summary-value">{{ summary.unposted_entries }}</div>
      </div>
      <div class="summary-item">
        <div class="summary-label">{{ text_pending_approvals }}</div>
        <div class="summary-value">{{ summary.pending_approvals }}</div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="period-closing-actions">
      <button type="button" class="btn btn-default" onclick="cancelClosing()">
        <i class="fa fa-times"></i> {{ button_cancel }}
      </button>
      <button type="button" class="btn btn-info" onclick="previewClosing()">
        <i class="fa fa-eye"></i> {{ button_preview }}
      </button>
      <button type="button" class="btn btn-closing" onclick="confirmClosing()">
        <i class="fa fa-lock"></i> {{ button_confirm_close }}
      </button>
    </div>
  </div>
</div>

<script>
// Enterprise Grade Plus JavaScript for Period Closing
class PeriodClosingManager {
    constructor() {
        this.initializeTooltips();
        this.initializeFormValidation();
        this.initializeKeyboardShortcuts();
        this.initializeProgressTracking();
        this.initializeAutoSave();
    }

    initializeTooltips() {
        $('[data-toggle="tooltip"]').tooltip();
    }

    initializeFormValidation() {
        const form = document.getElementById('period-closing-form');
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.validateForm();
            });
        }
    }

    initializeKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case 'v':
                        e.preventDefault();
                        this.validatePeriod();
                        break;
                    case 'b':
                        e.preventDefault();
                        this.createBackup();
                        break;
                    case 'r':
                        e.preventDefault();
                        this.generateReports();
                        break;
                    case 'l':
                        e.preventDefault();
                        this.closePeriod();
                        break;
                    case 'p':
                        e.preventDefault();
                        this.previewClosing();
                        break;
                }
            }
        });
    }

    initializeProgressTracking() {
        this.updateProgress();
        setInterval(() => {
            this.updateProgress();
        }, 30000); // Update every 30 seconds
    }

    initializeAutoSave() {
        const form = document.getElementById('period-closing-form');
        if (form) {
            const inputs = form.querySelectorAll('input, select, textarea');
            inputs.forEach(input => {
                input.addEventListener('change', () => {
                    this.autoSave();
                });
            });
        }
    }

    validatePeriod() {
        this.showLoadingState(true);

        fetch('{{ url_link('accounts/period_closing', 'validate') }}', {
            method: 'POST',
            body: JSON.stringify(this.getFormData()),
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            this.showLoadingState(false);
            if (data.success) {
                this.showAlert('{{ success_validation_complete }}', 'success');
                this.updateStepStatus('validation', 'completed');
            } else {
                this.showAlert(data.error || '{{ error_validation_failed }}', 'danger');
                this.updateStepStatus('validation', 'failed');
            }
        })
        .catch(error => {
            this.showLoadingState(false);
            this.showAlert('{{ error_validation_failed }}: ' + error.message, 'danger');
        });
    }

    createBackup() {
        if (!confirm('{{ text_confirm_backup }}')) {
            return;
        }

        this.showLoadingState(true);

        fetch('{{ url_link('accounts/period_closing', 'backup') }}', {
            method: 'POST',
            body: JSON.stringify(this.getFormData()),
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            this.showLoadingState(false);
            if (data.success) {
                this.showAlert('{{ success_backup_created }}', 'success');
                this.updateStepStatus('backup', 'completed');
            } else {
                this.showAlert(data.error || '{{ error_backup_failed }}', 'danger');
                this.updateStepStatus('backup', 'failed');
            }
        })
        .catch(error => {
            this.showLoadingState(false);
            this.showAlert('{{ error_backup_failed }}: ' + error.message, 'danger');
        });
    }

    generateReports() {
        this.showLoadingState(true);

        fetch('{{ url_link('accounts/period_closing', 'reports') }}', {
            method: 'POST',
            body: JSON.stringify(this.getFormData()),
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            this.showLoadingState(false);
            if (data.success) {
                this.showAlert('{{ success_reports_generated }}', 'success');
                this.updateStepStatus('reports', 'completed');
                if (data.reports) {
                    this.showReportsModal(data.reports);
                }
            } else {
                this.showAlert(data.error || '{{ error_reports_failed }}', 'danger');
                this.updateStepStatus('reports', 'failed');
            }
        })
        .catch(error => {
            this.showLoadingState(false);
            this.showAlert('{{ error_reports_failed }}: ' + error.message, 'danger');
        });
    }

    closePeriod() {
        if (!this.validateAllSteps()) {
            this.showAlert('{{ error_steps_incomplete }}', 'warning');
            return;
        }

        if (!confirm('{{ text_confirm_closing }}')) {
            return;
        }

        this.showLoadingState(true);

        fetch('{{ url_link('accounts/period_closing', 'close') }}', {
            method: 'POST',
            body: JSON.stringify(this.getFormData()),
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            this.showLoadingState(false);
            if (data.success) {
                this.showAlert('{{ success_period_closed }}', 'success');
                this.updateStepStatus('closing', 'completed');
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            } else {
                this.showAlert(data.error || '{{ error_closing_failed }}', 'danger');
                this.updateStepStatus('closing', 'failed');
            }
        })
        .catch(error => {
            this.showLoadingState(false);
            this.showAlert('{{ error_closing_failed }}: ' + error.message, 'danger');
        });
    }

    previewClosing() {
        const formData = this.getFormData();
        window.open('{{ url_link('accounts/period_closing', 'preview') }}&' + new URLSearchParams(formData).toString(), '_blank');
    }

    confirmClosing() {
        const modal = new bootstrap.Modal(document.getElementById('confirmClosingModal'));
        modal.show();
    }

    cancelClosing() {
        if (confirm('{{ text_confirm_cancel }}')) {
            window.location.href = '{{ cancel_url }}';
        }
    }

    validateForm() {
        const form = document.getElementById('period-closing-form');
        const formData = new FormData(form);
        let isValid = true;
        let errors = [];

        // Validate required fields
        if (!formData.get('period_name')) {
            errors.push('{{ error_period_name_required }}');
            isValid = false;
        }

        if (!formData.get('period_start_date')) {
            errors.push('{{ error_start_date_required }}');
            isValid = false;
        }

        if (!formData.get('period_end_date')) {
            errors.push('{{ error_end_date_required }}');
            isValid = false;
        }

        if (!formData.get('closing_reason')) {
            errors.push('{{ error_closing_reason_required }}');
            isValid = false;
        }

        // Validate date range
        const startDate = new Date(formData.get('period_start_date'));
        const endDate = new Date(formData.get('period_end_date'));

        if (startDate >= endDate) {
            errors.push('{{ error_invalid_date_range }}');
            isValid = false;
        }

        if (!isValid) {
            this.showAlert(errors.join('<br>'), 'danger');
        }

        return isValid;
    }

    validateAllSteps() {
        const steps = ['validation', 'backup', 'reports'];
        return steps.every(step => {
            const stepElement = document.querySelector(`.step-${step} .step-status`);
            return stepElement && stepElement.classList.contains('status-completed');
        });
    }

    updateStepStatus(step, status) {
        const stepElement = document.querySelector(`.step-${step} .step-status`);
        if (stepElement) {
            stepElement.className = `step-status status-${status}`;
            stepElement.textContent = this.getStatusText(status);
        }
        this.updateProgress();
    }

    updateProgress() {
        const steps = document.querySelectorAll('.step-status');
        const completedSteps = document.querySelectorAll('.status-completed').length;
        const totalSteps = steps.length;
        const percentage = Math.round((completedSteps / totalSteps) * 100);

        const progressBar = document.querySelector('.progress-bar');
        const progressText = document.querySelector('.progress-text');

        if (progressBar) {
            progressBar.style.width = percentage + '%';
        }

        if (progressText) {
            progressText.textContent = percentage + '% {{ text_complete }}';
        }
    }

    getStatusText(status) {
        const statusTexts = {
            'pending': '{{ text_pending }}',
            'in-progress': '{{ text_in_progress }}',
            'completed': '{{ text_completed }}',
            'failed': '{{ text_failed }}'
        };
        return statusTexts[status] || status;
    }

    getFormData() {
        const form = document.getElementById('period-closing-form');
        const formData = new FormData(form);
        const data = {};

        for (let [key, value] of formData.entries()) {
            data[key] = value;
        }

        return data;
    }

    autoSave() {
        const formData = this.getFormData();

        fetch('{{ url_link('accounts/period_closing', 'autoSave') }}', {
            method: 'POST',
            body: JSON.stringify(formData),
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .catch(error => {
            console.log('Auto-save failed:', error);
        });
    }

    showReportsModal(reports) {
        // Implementation for showing reports modal
        console.log('Reports generated:', reports);
    }

    showLoadingState(show) {
        const buttons = document.querySelectorAll('.btn');
        buttons.forEach(btn => {
            if (show) {
                btn.disabled = true;
                if (btn.querySelector('i')) {
                    btn.querySelector('i').className = 'fa fa-spinner fa-spin';
                }
            } else {
                btn.disabled = false;
                // Restore original icons
                location.reload();
            }
        });
    }

    showAlert(message, type) {
        const alertContainer = document.createElement('div');
        alertContainer.className = `alert alert-${type} alert-dismissible fade show`;
        alertContainer.innerHTML = `
            <i class="fa fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'}"></i>
            ${message}
            <button type="button" class="close" data-dismiss="alert">&times;</button>
        `;

        const container = document.querySelector('#content .container-fluid');
        container.insertBefore(alertContainer, container.firstChild);

        setTimeout(() => {
            alertContainer.remove();
        }, 5000);
    }
}

// Global functions for backward compatibility
function validatePeriod() {
    periodClosingManager.validatePeriod();
}

function createBackup() {
    periodClosingManager.createBackup();
}

function generateReports() {
    periodClosingManager.generateReports();
}

function closePeriod() {
    periodClosingManager.closePeriod();
}

function previewClosing() {
    periodClosingManager.previewClosing();
}

function confirmClosing() {
    periodClosingManager.confirmClosing();
}

function cancelClosing() {
    periodClosingManager.cancelClosing();
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.periodClosingManager = new PeriodClosingManager();
});
</script>

{{ footer }}
