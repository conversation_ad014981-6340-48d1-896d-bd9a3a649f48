<?php
namespace Session;

final class DB {
	public $maxlifetime;
	public $db;
	public $config;

	public function __construct($registry) {
		$this->db = $registry->get('db');
		$this->config = $registry->get('config');

		// استخدام إعدادات محسنة للجلسة
		$this->maxlifetime = $this->config->get('session_lifetime') ? (int)$this->config->get('session_lifetime') : 28800; // 8 ساعات افتراضي

		$this->gc();
	}

	public function read($session_id) {
		$query = $this->db->query("SELECT `data` FROM `" . DB_PREFIX . "session` WHERE `session_id` = '" . $this->db->escape($session_id) . "' AND `expire` > '" . $this->db->escape(gmdate('Y-m-d H:i:s', time())) . "'");

		if ($query->num_rows) {
			return json_decode($query->row['data'], true);
		} else {
			return array();
		}
	}

	public function write($session_id, $data) {
		if ($session_id) {
			// تحديث وقت انتهاء الجلسة في كل كتابة (تمديد الجلسة عند النشاط)
			$expire_time = time() + (int)$this->maxlifetime;

			// إذا كان هناك remember_me token، استخدم مدة أطول
			if (isset($data['remember_me']) && $data['remember_me']) {
				$remember_lifetime = $this->config->get('session_remember_me') ? (int)$this->config->get('session_remember_me') : 604800; // أسبوع
				$expire_time = time() + $remember_lifetime;
			}

			$this->db->query("REPLACE INTO `" . DB_PREFIX . "session` SET `session_id` = '" . $this->db->escape($session_id) . "', `data` = '" . $this->db->escape(json_encode($data)) . "', `expire` = '" . $this->db->escape(gmdate('Y-m-d H:i:s', $expire_time)) . "'");
		}

		return true;
	}

	public function destroy($session_id) {
		$this->db->query("DELETE FROM `" . DB_PREFIX . "session` WHERE `session_id` = '" . $this->db->escape($session_id) . "'");

		return true;
	}

	public function gc() {
		if (ini_get('session.gc_divisor') && $gc_divisor = (int)ini_get('session.gc_divisor')) {
			$gc_divisor = $gc_divisor === 0 ? 100 : $gc_divisor;
		} else {
			$gc_divisor = 100;
		}

		if (ini_get('session.gc_probability')) {
			$gc_probability = (int)ini_get('session.gc_probability');
		} else {
			$gc_probability = 1;
		}

		if (mt_rand() / mt_getrandmax() < $gc_probability / $gc_divisor) {
			$this->db->query("DELETE FROM `" . DB_PREFIX . "session` WHERE `expire` < '" . $this->db->escape(gmdate('Y-m-d H:i:s', time())) . "'");

			return true;
		}
	}
}
