{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="submit" form="form-settings" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fa fa-save"></i></button>
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a></div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-cogs"></i> {{ text_accounting }}</h3>
      </div>
      <div class="panel-body">
        <form action="{{ action }}" method="post" enctype="multipart/form-data" id="form-settings" class="form-horizontal">
          <ul class="nav nav-tabs">
            <li class="active"><a href="#tab-general" data-toggle="tab">{{ text_accounting }}</a></li>
            <li><a href="#tab-inventory" data-toggle="tab">{{ text_inventory }}</a></li>
          </ul>
          <div class="tab-content">
            <div class="tab-pane active" id="tab-general">
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-accounting-status">{{ entry_accounting_status }}</label>
                <div class="col-sm-10">
                  <select name="accounting_status" id="input-accounting-status" class="form-control">
                    {% if accounting_status %}
                    <option value="1" selected="selected">{{ text_enabled }}</option>
                    <option value="0">{{ text_disabled }}</option>
                    {% else %}
                    <option value="1">{{ text_enabled }}</option>
                    <option value="0" selected="selected">{{ text_disabled }}</option>
                    {% endif %}
                  </select>
                </div>
              </div>
            </div>
            <div class="tab-pane" id="tab-inventory">
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-purchase-inventory">{{ entry_purchase_inventory_account }}</label>
                <div class="col-sm-10">
                  <select name="accounting_purchase_inventory_account" id="input-purchase-inventory" class="form-control">
                    <option value="">{{ text_select }}</option>
                    {% for account in accounts %}
                    <option value="{{ account.account_id }}"{% if account.account_id == accounting_purchase_inventory_account %} selected="selected"{% endif %}>{{ account.name }}</option>
                    {% endfor %}
                  </select>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-purchase-contra">{{ entry_purchase_contra_account }}</label>
                <div class="col-sm-10">
                  <select name="accounting_purchase_contra_account" id="input-purchase-contra" class="form-control">
                    <option value="">{{ text_select }}</option>
                    {% for account in accounts %}
                    <option value="{{ account.account_id }}"{% if account.account_id == accounting_purchase_contra_account %} selected="selected"{% endif %}>{{ account.name }}</option>
                    {% endfor %}
                  </select>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-sale-inventory">{{ entry_sale_inventory_account }}</label>
                <div class="col-sm-10">
                  <select name="accounting_sale_inventory_account" id="input-sale-inventory" class="form-control">
                    <option value="">{{ text_select }}</option>
                    {% for account in accounts %}
                    <option value="{{ account.account_id }}"{% if account.account_id == accounting_sale_inventory_account %} selected="selected"{% endif %}>{{ account.name }}</option>
                    {% endfor %}
                  </select>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-sale-contra">{{ entry_sale_contra_account }}</label>
                <div class="col-sm-10">
                  <select name="accounting_sale_contra_account" id="input-sale-contra" class="form-control">
                    <option value="">{{ text_select }}</option>
                    {% for account in accounts %}
                    <option value="{{ account.account_id }}"{% if account.account_id == accounting_sale_contra_account %} selected="selected"{% endif %}>{{ account.name }}</option>
                    {% endfor %}
                  </select>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
{{ footer }}
