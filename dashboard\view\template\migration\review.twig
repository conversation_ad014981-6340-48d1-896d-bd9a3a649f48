{{ header }}{{ column_left }}
{#
/**
 * AYM ERP System: Migration Review View
 *
 * عرض مراجعة الهجرة - مطور بجودة عالمية
 *
 * الميزات المتقدمة:
 * - مراجعة شاملة لعمليات الهجرة
 * - موافقة ورفض البيانات المهاجرة
 * - إمكانية التراجع عن الهجرة
 * - تتبع حالة الهجرة
 * - واجهة مستخدم احترافية
 * - تكامل مع Bootstrap 3.3
 *
 * @package    AYM ERP
 * <AUTHOR> Development Team
 * @copyright  2024 AYM ERP Systems
 * @license    Commercial License
 * @version    1.0.0
 * @since      2024-01-15
 */
#}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="button" class="btn btn-success" onclick="approveSelected()">
          <i class="fa fa-check"></i> {{ button_approve }}
        </button>
        <button type="button" class="btn btn-danger" onclick="rejectSelected()">
          <i class="fa fa-times"></i> {{ button_reject }}
        </button>
        <button type="button" class="btn btn-warning" onclick="rollbackSelected()">
          <i class="fa fa-undo"></i> {{ button_rollback }}
        </button>
        <button type="button" class="btn btn-default" onclick="refreshMigrations()">
          <i class="fa fa-refresh"></i> تحديث
        </button>
      </div>
      <h1><i class="fa fa-check-square-o"></i> {{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>

  <div class="container-fluid">
    {# رسائل التنبيه #}
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible">
      <i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}

    {% if success %}
    <div class="alert alert-success alert-dismissible">
      <i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}

    {# تنبيه المراجعة المطلوبة #}
    <div class="alert alert-info alert-dismissible">
      <i class="fa fa-info-circle"></i> {{ alert_review_needed }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>

    {# إحصائيات سريعة #}
    <div class="row">
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-primary">
          <div class="panel-body">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-clock-o fa-5x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge" id="pending-count">-</div>
                <div>في الانتظار</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-green">
          <div class="panel-body">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-check fa-5x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge" id="approved-count">-</div>
                <div>موافق عليها</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-red">
          <div class="panel-body">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-times fa-5x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge" id="rejected-count">-</div>
                <div>مرفوضة</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-yellow">
          <div class="panel-body">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-exclamation-triangle fa-5x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge" id="failed-count">-</div>
                <div>فاشلة</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    {# جدول عمليات الهجرة #}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title">
          <i class="fa fa-list"></i> عمليات الهجرة المطلوب مراجعتها
        </h3>
      </div>
      <div class="panel-body">
        <div id="migration-loading" class="text-center" style="display: none;">
          <i class="fa fa-spinner fa-spin fa-3x"></i>
          <p>جاري تحميل البيانات...</p>
        </div>
        
        <div class="table-responsive">
          <table class="table table-striped table-hover" id="migration-table">
            <thead>
              <tr>
                <th width="5%">
                  <input type="checkbox" id="select-all" />
                </th>
                <th width="15%">{{ column_source }}</th>
                <th width="15%">{{ column_destination }}</th>
                <th width="10%">{{ column_status }}</th>
                <th width="15%">{{ column_date }}</th>
                <th width="10%">{{ column_user }}</th>
                <th width="10%">{{ column_records }}</th>
                <th width="20%">{{ column_action }}</th>
              </tr>
            </thead>
            <tbody id="migration-tbody">
              <tr>
                <td colspan="8" class="text-center text-muted">
                  <i class="fa fa-info-circle"></i> جاري تحميل البيانات...
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    {# معلومات إضافية #}
    <div class="row">
      <div class="col-md-6">
        <div class="panel panel-info">
          <div class="panel-heading">
            <h3 class="panel-title">
              <i class="fa fa-info-circle"></i> معلومات المراجعة
            </h3>
          </div>
          <div class="panel-body">
            <dl class="dl-horizontal">
              <dt>{{ status_pending }}:</dt>
              <dd>عمليات الهجرة التي تحتاج إلى مراجعة وموافقة</dd>
              
              <dt>{{ status_approved }}:</dt>
              <dd>عمليات الهجرة التي تمت الموافقة عليها وتطبيقها</dd>
              
              <dt>{{ status_rejected }}:</dt>
              <dd>عمليات الهجرة التي تم رفضها وإلغاؤها</dd>
              
              <dt>{{ status_completed }}:</dt>
              <dd>عمليات الهجرة المكتملة بنجاح</dd>
              
              <dt>{{ status_failed }}:</dt>
              <dd>عمليات الهجرة التي فشلت أثناء التنفيذ</dd>
            </dl>
          </div>
        </div>
      </div>
      
      <div class="col-md-6">
        <div class="panel panel-warning">
          <div class="panel-heading">
            <h3 class="panel-title">
              <i class="fa fa-exclamation-triangle"></i> تحذيرات مهمة
            </h3>
          </div>
          <div class="panel-body">
            <ul>
              <li><strong>النسخ الاحتياطي:</strong> تأكد من وجود نسخة احتياطية قبل الموافقة</li>
              <li><strong>التحقق:</strong> راجع البيانات بعناية قبل الموافقة</li>
              <li><strong>التراجع:</strong> يمكن التراجع عن العمليات المعتمدة فقط</li>
              <li><strong>الصلاحيات:</strong> تأكد من وجود الصلاحيات المناسبة</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

{# نافذة تفاصيل الهجرة #}
<div class="modal fade" id="migration-detail-modal" tabindex="-1" role="dialog">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title"><i class="fa fa-info-circle"></i> تفاصيل عملية الهجرة</h4>
      </div>
      <div class="modal-body" id="migration-detail-content">
        <!-- سيتم تحميل المحتوى ديناميكياً -->
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">إغلاق</button>
      </div>
    </div>
  </div>
</div>

{# نافذة رفض الهجرة #}
<div class="modal fade" id="reject-modal" tabindex="-1" role="dialog">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title"><i class="fa fa-times"></i> رفض عملية الهجرة</h4>
      </div>
      <div class="modal-body">
        <form id="reject-form">
          <div class="form-group">
            <label for="reject-reason">سبب الرفض:</label>
            <textarea id="reject-reason" name="reject_reason" class="form-control" rows="4" required 
                      placeholder="يرجى توضيح سبب رفض عملية الهجرة..."></textarea>
          </div>
          <input type="hidden" id="reject-migration-id" name="migration_id" />
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">إلغاء</button>
        <button type="button" class="btn btn-danger" onclick="confirmReject()">
          <i class="fa fa-times"></i> رفض
        </button>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript">
var currentPage = 1;
var recordsPerPage = 20;
var totalRecords = 0;

// تهيئة الصفحة
$(document).ready(function() {
    // تحميل البيانات
    loadMigrations();
    
    // تحديد/إلغاء تحديد جميع الصفوف
    $('#select-all').on('change', function() {
        $('input[name="selected[]"]').prop('checked', this.checked);
    });
    
    // تحديث كل 30 ثانية
    setInterval(loadMigrations, 30000);
});

// تحميل عمليات الهجرة
function loadMigrations() {
    $('#migration-loading').show();
    $('#migration-tbody').empty();
    
    $.ajax({
        url: 'index.php?route=migration/review/getMigrations&user_token={{ user_token }}',
        type: 'GET',
        dataType: 'json',
        success: function(data) {
            $('#migration-loading').hide();
            
            if (data.error) {
                showAlert('danger', data.error);
                return;
            }
            
            totalRecords = data.total || 0;
            updateStatistics(data.statistics);
            
            if (data.migrations && data.migrations.length > 0) {
                var tbody = '';
                $.each(data.migrations, function(index, migration) {
                    tbody += '<tr>';
                    tbody += '<td><input type="checkbox" name="selected[]" value="' + migration.migration_id + '" /></td>';
                    tbody += '<td>' + migration.source_system + '</td>';
                    tbody += '<td>' + migration.destination_system + '</td>';
                    tbody += '<td>' + getStatusBadge(migration.status) + '</td>';
                    tbody += '<td>' + migration.date_added + '</td>';
                    tbody += '<td>' + migration.user_name + '</td>';
                    tbody += '<td>' + migration.total_records + '</td>';
                    tbody += '<td>' + getActionButtons(migration) + '</td>';
                    tbody += '</tr>';
                });
                $('#migration-tbody').html(tbody);
            } else {
                $('#migration-tbody').html('<tr><td colspan="8" class="text-center text-muted"><i class="fa fa-info-circle"></i> لا توجد عمليات هجرة</td></tr>');
            }
        },
        error: function() {
            $('#migration-loading').hide();
            showAlert('danger', 'حدث خطأ أثناء تحميل البيانات');
        }
    });
}

// تحديث الإحصائيات
function updateStatistics(stats) {
    if (stats) {
        $('#pending-count').text(stats.pending || 0);
        $('#approved-count').text(stats.approved || 0);
        $('#rejected-count').text(stats.rejected || 0);
        $('#failed-count').text(stats.failed || 0);
    }
}

// الحصول على شارة الحالة
function getStatusBadge(status) {
    var badges = {
        'pending': '<span class="label label-warning">{{ status_pending }}</span>',
        'approved': '<span class="label label-success">{{ status_approved }}</span>',
        'rejected': '<span class="label label-danger">{{ status_rejected }}</span>',
        'completed': '<span class="label label-info">{{ status_completed }}</span>',
        'failed': '<span class="label label-danger">{{ status_failed }}</span>'
    };
    return badges[status] || '<span class="label label-default">' + status + '</span>';
}

// الحصول على أزرار الإجراءات
function getActionButtons(migration) {
    var buttons = '';
    
    // زر عرض التفاصيل
    buttons += '<button type="button" class="btn btn-info btn-xs" onclick="viewDetails(' + migration.migration_id + ')">';
    buttons += '<i class="fa fa-eye"></i> عرض';
    buttons += '</button> ';
    
    if (migration.status === 'pending') {
        // زر الموافقة
        buttons += '<button type="button" class="btn btn-success btn-xs" onclick="approveMigration(' + migration.migration_id + ')">';
        buttons += '<i class="fa fa-check"></i> موافقة';
        buttons += '</button> ';
        
        // زر الرفض
        buttons += '<button type="button" class="btn btn-danger btn-xs" onclick="rejectMigration(' + migration.migration_id + ')">';
        buttons += '<i class="fa fa-times"></i> رفض';
        buttons += '</button>';
    } else if (migration.status === 'approved') {
        // زر التراجع
        buttons += '<button type="button" class="btn btn-warning btn-xs" onclick="rollbackMigration(' + migration.migration_id + ')">';
        buttons += '<i class="fa fa-undo"></i> تراجع';
        buttons += '</button>';
    }
    
    return buttons;
}

// عرض تفاصيل الهجرة
function viewDetails(migrationId) {
    $('#migration-detail-modal').modal('show');
    $('#migration-detail-content').html('<div class="text-center"><i class="fa fa-spinner fa-spin"></i> جاري التحميل...</div>');
    
    $.ajax({
        url: 'index.php?route=migration/review/getDetails&user_token={{ user_token }}&migration_id=' + migrationId,
        type: 'GET',
        dataType: 'json',
        success: function(data) {
            if (data.migration) {
                var content = '<div class="row">';
                content += '<div class="col-md-6"><strong>النظام المصدر:</strong> ' + data.migration.source_system + '</div>';
                content += '<div class="col-md-6"><strong>النظام الوجهة:</strong> ' + data.migration.destination_system + '</div>';
                content += '<div class="col-md-6"><strong>الحالة:</strong> ' + getStatusBadge(data.migration.status) + '</div>';
                content += '<div class="col-md-6"><strong>التاريخ:</strong> ' + data.migration.date_added + '</div>';
                content += '<div class="col-md-6"><strong>المستخدم:</strong> ' + data.migration.user_name + '</div>';
                content += '<div class="col-md-6"><strong>إجمالي السجلات:</strong> ' + data.migration.total_records + '</div>';
                if (data.migration.reject_reason) {
                    content += '<div class="col-md-12"><strong>سبب الرفض:</strong><br>' + data.migration.reject_reason + '</div>';
                }
                content += '</div>';
                $('#migration-detail-content').html(content);
            } else {
                $('#migration-detail-content').html('<div class="alert alert-danger">لم يتم العثور على تفاصيل الهجرة</div>');
            }
        }
    });
}

// موافقة على الهجرة
function approveMigration(migrationId) {
    if (confirm('هل أنت متأكد من الموافقة على هذه العملية؟')) {
        $.ajax({
            url: 'index.php?route=migration/review/approve&user_token={{ user_token }}',
            type: 'POST',
            data: {migration_id: migrationId},
            dataType: 'json',
            success: function(data) {
                if (data.success) {
                    showAlert('success', data.success);
                    loadMigrations();
                } else {
                    showAlert('danger', data.error || 'حدث خطأ أثناء الموافقة');
                }
            }
        });
    }
}

// رفض الهجرة
function rejectMigration(migrationId) {
    $('#reject-migration-id').val(migrationId);
    $('#reject-reason').val('');
    $('#reject-modal').modal('show');
}

// تأكيد الرفض
function confirmReject() {
    var migrationId = $('#reject-migration-id').val();
    var rejectReason = $('#reject-reason').val().trim();
    
    if (!rejectReason) {
        alert('يرجى إدخال سبب الرفض');
        return;
    }
    
    $.ajax({
        url: 'index.php?route=migration/review/reject&user_token={{ user_token }}',
        type: 'POST',
        data: {
            migration_id: migrationId,
            reject_reason: rejectReason
        },
        dataType: 'json',
        success: function(data) {
            $('#reject-modal').modal('hide');
            if (data.success) {
                showAlert('success', data.success);
                loadMigrations();
            } else {
                showAlert('danger', data.error || 'حدث خطأ أثناء الرفض');
            }
        }
    });
}

// التراجع عن الهجرة
function rollbackMigration(migrationId) {
    if (confirm('هل أنت متأكد من التراجع عن هذه العملية؟ سيتم استعادة البيانات السابقة.')) {
        $.ajax({
            url: 'index.php?route=migration/review/rollback&user_token={{ user_token }}',
            type: 'POST',
            data: {migration_id: migrationId},
            dataType: 'json',
            success: function(data) {
                if (data.success) {
                    showAlert('success', data.success);
                    loadMigrations();
                } else {
                    showAlert('danger', data.error || 'حدث خطأ أثناء التراجع');
                }
            }
        });
    }
}

// موافقة على المحدد
function approveSelected() {
    var selected = $('input[name="selected[]"]:checked').map(function() {
        return this.value;
    }).get();
    
    if (selected.length === 0) {
        alert('يرجى تحديد عملية واحدة على الأقل');
        return;
    }
    
    if (confirm('هل أنت متأكد من الموافقة على العمليات المحددة؟')) {
        $.each(selected, function(index, migrationId) {
            approveMigration(migrationId);
        });
    }
}

// رفض المحدد
function rejectSelected() {
    var selected = $('input[name="selected[]"]:checked').map(function() {
        return this.value;
    }).get();
    
    if (selected.length === 0) {
        alert('يرجى تحديد عملية واحدة على الأقل');
        return;
    }
    
    // للبساطة، سنطلب سبب رفض واحد لجميع العمليات
    var rejectReason = prompt('يرجى إدخال سبب الرفض:');
    if (rejectReason && rejectReason.trim()) {
        $.each(selected, function(index, migrationId) {
            $.ajax({
                url: 'index.php?route=migration/review/reject&user_token={{ user_token }}',
                type: 'POST',
                data: {
                    migration_id: migrationId,
                    reject_reason: rejectReason.trim()
                },
                dataType: 'json',
                success: function(data) {
                    if (data.success) {
                        showAlert('success', data.success);
                    } else {
                        showAlert('danger', data.error || 'حدث خطأ أثناء الرفض');
                    }
                }
            });
        });
        setTimeout(loadMigrations, 1000);
    }
}

// التراجع عن المحدد
function rollbackSelected() {
    var selected = $('input[name="selected[]"]:checked').map(function() {
        return this.value;
    }).get();
    
    if (selected.length === 0) {
        alert('يرجى تحديد عملية واحدة على الأقل');
        return;
    }
    
    if (confirm('هل أنت متأكد من التراجع عن العمليات المحددة؟')) {
        $.each(selected, function(index, migrationId) {
            rollbackMigration(migrationId);
        });
    }
}

// تحديث البيانات
function refreshMigrations() {
    loadMigrations();
}

// عرض التنبيهات
function showAlert(type, message) {
    var alertClass = 'alert-' + type;
    var alert = '<div class="alert ' + alertClass + ' alert-dismissible">';
    alert += '<button type="button" class="close" data-dismiss="alert">&times;</button>';
    alert += message + '</div>';
    
    $('.container-fluid').prepend(alert);
    
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);
}
</script>

<style>
.huge {
    font-size: 40px;
}

.panel-green {
    border-color: #5cb85c;
}

.panel-green > .panel-heading {
    border-color: #5cb85c;
    color: white;
    background-color: #5cb85c;
}

.panel-yellow {
    border-color: #f0ad4e;
}

.panel-yellow > .panel-heading {
    border-color: #f0ad4e;
    color: white;
    background-color: #f0ad4e;
}

.panel-red {
    border-color: #d9534f;
}

.panel-red > .panel-heading {
    border-color: #d9534f;
    color: white;
    background-color: #d9534f;
}

.panel-body {
    color: white;
}

.panel-primary .panel-body,
.panel-green .panel-body,
.panel-yellow .panel-body,
.panel-red .panel-body {
    color: white;
}

.label {
    margin-right: 5px;
}
</style>

{{ footer }}
