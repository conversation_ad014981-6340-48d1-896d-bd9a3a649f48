<?php
// Heading
$_['heading_title']                    = 'Advanced Financial Reports';

// Text
$_['text_success']                     = 'Success: Advanced financial report has been generated successfully!';
$_['text_list']                        = 'Advanced Financial Reports List';
$_['text_form']                        = 'Advanced Financial Report Form';
$_['text_view']                        = 'View Advanced Financial Report';
$_['text_generate']                    = 'Generate Advanced Financial Report';
$_['text_export']                      = 'Export Advanced Financial Report';
$_['text_compare']                     = 'Compare Financial Reports';
$_['text_print']                       = 'Print Financial Report';
$_['text_financial_reports_advanced']  = 'Advanced Financial Reports';
$_['text_period']                      = 'Period';
$_['text_from']                        = 'From';
$_['text_to']                          = 'To';
$_['text_loading']                     = 'Loading...';
$_['text_processing']                  = 'Processing...';
$_['text_success_generate']            = 'Financial report generated successfully!';
$_['text_success_export']              = 'Financial report exported successfully!';
$_['text_success_compare']             = 'Comparison completed successfully!';

// Report Types
$_['text_comprehensive']               = 'Comprehensive Financial Report';
$_['text_income_statement']            = 'Income Statement';
$_['text_balance_sheet']               = 'Balance Sheet';
$_['text_cash_flow']                   = 'Cash Flow Statement';
$_['text_equity_changes']              = 'Statement of Changes in Equity';
$_['text_financial_ratios']            = 'Financial Ratios';
$_['text_performance_analysis']        = 'Performance Analysis';

// Comparison Periods
$_['text_none']                        = 'No Comparison';
$_['text_previous_month']              = 'Previous Month';
$_['text_previous_quarter']            = 'Previous Quarter';
$_['text_previous_year']               = 'Previous Year';
$_['text_budget']                      = 'Budget';
$_['text_custom']                      = 'Custom Period';

// Consolidation Levels
$_['text_company']                     = 'Company Level';
$_['text_division']                    = 'Division Level';
$_['text_department']                  = 'Department Level';
$_['text_cost_center']                 = 'Cost Center Level';

// Financial Analysis
$_['text_financial_analysis']          = 'Financial Analysis';
$_['text_ratio_analysis']              = 'Ratio Analysis';
$_['text_trend_analysis']              = 'Trend Analysis';
$_['text_variance_analysis']           = 'Variance Analysis';
$_['text_benchmark_analysis']          = 'Benchmark Analysis';
$_['text_segment_analysis']            = 'Segment Analysis';
$_['text_performance_indicators']      = 'Performance Indicators';

// Financial Ratios
$_['text_liquidity_ratios']            = 'Liquidity Ratios';
$_['text_profitability_ratios']        = 'Profitability Ratios';
$_['text_leverage_ratios']             = 'Leverage Ratios';
$_['text_efficiency_ratios']           = 'Efficiency Ratios';
$_['text_market_ratios']               = 'Market Ratios';
$_['text_current_ratio']               = 'Current Ratio';
$_['text_quick_ratio']                 = 'Quick Ratio';
$_['text_debt_ratio']                  = 'Debt Ratio';
$_['text_equity_ratio']                = 'Equity Ratio';
$_['text_roa']                         = 'Return on Assets';
$_['text_roe']                         = 'Return on Equity';
$_['text_gross_margin']                = 'Gross Margin';
$_['text_net_margin']                  = 'Net Margin';

// Column
$_['column_account']                   = 'Account';
$_['column_current_period']            = 'Current Period';
$_['column_previous_period']           = 'Previous Period';
$_['column_variance']                  = 'Variance';
$_['column_percentage']                = 'Percentage';
$_['column_ratio']                     = 'Ratio';
$_['column_value']                     = 'Value';
$_['column_benchmark']                 = 'Benchmark';
$_['column_status']                    = 'Status';

// Entry
$_['entry_report_type']                = 'Report Type';
$_['entry_date_start']                 = 'Start Date';
$_['entry_date_end']                   = 'End Date';
$_['entry_comparison_period']          = 'Comparison Period';
$_['entry_consolidation_level']        = 'Consolidation Level';
$_['entry_include_notes']              = 'Include Notes';
$_['entry_show_details']               = 'Show Details';
$_['entry_currency']                   = 'Currency';
$_['entry_branch']                     = 'Branch';
$_['entry_export_format']              = 'Export Format';

// Button
$_['button_generate']                  = 'Generate';
$_['button_export']                    = 'Export';
$_['button_compare']                   = 'Compare';
$_['button_print']                     = 'Print';
$_['button_filter']                    = 'Filter';
$_['button_reset']                     = 'Reset';
$_['button_close']                     = 'Close';
$_['button_analyze']                   = 'Analyze';

// Tab
$_['tab_general']                      = 'General';
$_['tab_filters']                      = 'Filters';
$_['tab_options']                      = 'Options';
$_['tab_comparison']                   = 'Comparison';
$_['tab_analysis']                     = 'Analysis';
$_['tab_ratios']                       = 'Financial Ratios';
$_['tab_trends']                       = 'Trends';

// Help
$_['help_report_type']                 = 'Select the type of financial report required';
$_['help_date_start']                  = 'Select the start date for the report period';
$_['help_date_end']                    = 'Select the end date for the report period';
$_['help_comparison']                  = 'Select a period to compare with the current period';
$_['help_consolidation']               = 'Select the consolidation level for the report';

// Error
$_['error_warning']                    = 'Warning: Please check the form carefully for errors!';
$_['error_permission']                 = 'Warning: You do not have permission to access advanced financial reports!';
$_['error_date_start']                 = 'Start date is required!';
$_['error_date_end']                   = 'End date is required!';
$_['error_date_range']                 = 'Start date must be before end date!';
$_['error_report_type']                = 'Report type is required!';
$_['error_no_data']                    = 'No data found for the selected period!';
$_['error_export']                     = 'Error exporting data!';
$_['error_form']                       = 'Form data error!';
$_['error_missing_data']               = 'Missing data!';

// Export Formats
$_['text_excel']                       = 'Excel';
$_['text_pdf']                         = 'PDF';
$_['text_csv']                         = 'CSV';

// Print Options
$_['text_print_title']                 = 'Advanced Financial Report';
$_['print_title']                      = 'Print Advanced Financial Report';
$_['text_print_date']                  = 'Print Date';
$_['text_print_user']                  = 'Printed By';

// Status Messages
$_['text_generating']                  = 'Generating financial report...';
$_['text_exporting']                   = 'Exporting data...';
$_['text_comparing']                   = 'Performing comparison...';
$_['text_analyzing']                   = 'Performing financial analysis...';
$_['text_completed']                   = 'Completed successfully!';

// Advanced Features
$_['text_drill_down']                  = 'Drill Down';
$_['text_consolidation']               = 'Consolidation';
$_['text_multi_currency']              = 'Multi-Currency';
$_['text_multi_branch']                = 'Multi-Branch';
$_['text_automated_analysis']          = 'Automated Analysis';
$_['text_predictive_analytics']        = 'Predictive Analytics';

// Egyptian Compliance
$_['text_egyptian_gaap']               = 'According to Egyptian Generally Accepted Accounting Principles';
$_['text_eta_compliant']               = 'ETA Compliant';
$_['text_cbe_compliant']               = 'Central Bank of Egypt Compliant';
$_['text_egyptian_tax_law']            = 'Egyptian Tax Law Compliant';

// Controller language variables
$_['log_unauthorized_access_financial_reports'] = 'Unauthorized access attempt to advanced financial reports';
$_['log_view_financial_reports_screen'] = 'View advanced financial reports screen';
$_['log_unauthorized_generate_financial_report'] = 'Unauthorized financial report generation attempt';
$_['log_generate_financial_report_period'] = 'Generate financial report';
$_['text_financial_report_generated'] = 'Advanced Financial Report Generated';
$_['text_financial_report_generated_message'] = 'Advanced financial report generated';
$_['text_by_user'] = 'by';
$_['text_no_data_period'] = 'No data found for the specified period';
$_['error_generate_financial_report'] = 'Error generating financial report';
$_['text_audit_trail_failed_report'] = 'Failed to generate financial report';
$_['text_no_report_data'] = 'No financial report data to display';
$_['text_no_export_data'] = 'No data to export';
$_['text_no_ratio_data'] = 'No data for financial ratio calculation';
$_['text_no_analysis_data'] = 'No data for financial analysis';
$_['text_no_kpi_data'] = 'No data for performance indicators calculation';
$_['text_no_trend_data'] = 'No data for trend analysis';
$_['text_no_benchmark_data'] = 'No data for benchmark comparison';
$_['text_no_variance_data'] = 'No data for variance analysis';
$_['text_no_segment_data'] = 'No data for segment analysis';

// Additional variables for template
$_['text_actions'] = 'Actions';
$_['text_generate_report'] = 'Generate Report';
$_['text_export_options'] = 'Export Options';
$_['text_financial_analysis'] = 'Financial Analysis';
$_['text_report_filters'] = 'Report Filters';
$_['text_all_branches'] = 'All Branches';
$_['text_all_currencies'] = 'All Currencies';
$_['text_no_data'] = 'No data to display';
$_['text_report_generated'] = 'Report generated successfully';
$_['error_generate_report'] = 'Error generating report';
$_['text_exporting'] = 'Exporting';
$_['text_loading'] = 'Loading';

// Missing variables from audit report - Critical fixes
$_['accounts/financial_reports_advanced'] = '';
$_['code']                             = 'Code';
$_['date_format_short']                = 'm/d/Y';
$_['direction']                        = 'ltr';
$_['lang']                             = 'en';

// Enhanced performance and analytics variables
$_['text_optimized_reports']           = 'Optimized Reports';
$_['text_advanced_analysis']           = 'Advanced Analysis';
$_['text_financial_analysis']          = 'Financial Analysis';
$_['text_cache_enabled']               = 'Cache Enabled';
$_['text_report_cached']               = 'Report Cached';
$_['text_profitability_analysis']      = 'Profitability Analysis';
$_['text_liquidity_analysis']          = 'Liquidity Analysis';
$_['text_efficiency_analysis']         = 'Efficiency Analysis';
$_['text_leverage_analysis']           = 'Leverage Analysis';
$_['text_growth_analysis']             = 'Growth Analysis';
$_['text_market_value_analysis']       = 'Market Value Analysis';
$_['text_gross_profit']                = 'Gross Profit';
$_['text_gross_profit_margin']         = 'Gross Profit Margin';
$_['text_cost_ratio']                  = 'Cost Ratio';
$_['text_current_ratio']               = 'Current Ratio';
$_['text_working_capital']             = 'Working Capital';
$_['text_liquidity_rating']            = 'Liquidity Rating';
$_['text_excellent']                   = 'Excellent';
$_['text_good']                        = 'Good';
$_['text_fair']                        = 'Fair';
$_['text_poor']                        = 'Poor';
$_['text_financial_kpis']              = 'Financial KPIs';
$_['text_revenue_growth']              = 'Revenue Growth';
$_['text_return_on_assets']            = 'Return on Assets';
$_['text_return_on_equity']            = 'Return on Equity';
$_['text_debt_to_equity']              = 'Debt to Equity';
$_['button_advanced_analysis']         = 'Advanced Analysis';
$_['button_financial_kpis']            = 'Financial KPIs';
$_['text_loading_analysis']            = 'Loading financial analysis...';
$_['text_analysis_ready']              = 'Analysis ready';
?>
