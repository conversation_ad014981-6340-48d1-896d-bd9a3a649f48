{{ header }}
{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="button" id="refresh-list" class="btn btn-default" onclick="PaymentManager.loadPayments(1);">
          <i class="fa fa-refresh"></i> {{ text_refresh }}
        </button>
        {% if can_add %}
        <button type="button" id="add-payment" class="btn btn-primary" onclick="PaymentManager.addPayment();">
          <i class="fa fa-plus"></i> {{ button_add }}
        </button>
        {% endif %}
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  
  <div class="container-fluid">
    <!-- Alerts -->
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible fade in">
      <i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert" aria-label="Close">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    {% endif %}
    
    {% if success %}
    <div class="alert alert-success alert-dismissible fade in">
      <i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert" aria-label="Close">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    {% endif %}
    
    <!-- Search Filters -->
    <div class="panel panel-default filter-panel">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-filter"></i> {{ text_filter }}</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-sm-3">
            <div class="form-group">
              <label for="filter-payment-id" class="control-label">{{ column_payment_id }}</label>
              <input type="text" name="filter_payment_id" id="filter-payment-id" class="form-control" placeholder="{{ column_payment_id }}">
            </div>
          </div>
           <div class="col-sm-3">
            <div class="form-group">
              <label for="filter-supplier" class="control-label">{{ column_supplier }}</label>
              <select id="filter-supplier" class="form-control select2-supplier">
                <option value="">{{ text_all_suppliers }}</option>
                {% for supplier in suppliers %}
                <option value="{{ supplier.supplier_id }}">{{ supplier.name }}</option>
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="col-sm-3">
            <div class="form-group">
              <label for="filter-payment-method" class="control-label">{{ column_payment_method }}</label>
              <select id="filter-payment-method" class="form-control">
                 <option value="">{{ text_all_methods }}</option>
                 {# TODO: Populate payment methods #}
              </select>
            </div>
          </div>
          <div class="col-sm-3">
            <div class="form-group">
              <label for="filter-status" class="control-label">{{ column_status }}</label>
              <select id="filter-status" class="form-control">
                <option value="">{{ text_all_statuses }}</option>
                {# TODO: Add status options #}
              </select>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-sm-4">
            <div class="form-group">
              <label for="filter-date-start" class="control-label">{{ text_date_start }}</label>
              <div class="input-group date">
                <input type="text" name="filter_date_start" placeholder="{{ text_date_start }}" id="filter-date-start" class="form-control" data-date-format="YYYY-MM-DD">
                <span class="input-group-btn">
                  <button type="button" class="btn btn-default"><i class="fa fa-calendar"></i></button>
                </span>
              </div>
            </div>
          </div>
          <div class="col-sm-4">
            <div class="form-group">
              <label for="filter-date-end" class="control-label">{{ text_date_end }}</label>
              <div class="input-group date">
                <input type="text" name="filter_date_end" placeholder="{{ text_date_end }}" id="filter-date-end" class="form-control" data-date-format="YYYY-MM-DD">
                <span class="input-group-btn">
                  <button type="button" class="btn btn-default"><i class="fa fa-calendar"></i></button>
                </span>
              </div>
            </div>
          </div>
          <div class="col-sm-4">
            <div class="form-group" style="margin-top: 24px;">
              <button type="button" id="clear-filter" class="btn btn-default btn-block" onclick="PaymentManager.clearFilters();">
                <i class="fa fa-eraser"></i> {{ button_clear }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Vendor Payments List -->
    <div class="panel panel-default list-panel">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-list"></i> {{ text_list }}</h3>
      </div>
      <div class="panel-body">
        <form id="form-payment" method="post">
          <div class="table-responsive">
            <table id="payment-table" class="table table-bordered table-hover">
              <thead>
                <tr>
                  <th style="width: 1px;" class="text-center">
                    <input type="checkbox" onclick="$('input[name*=\'selected\']').prop('checked', this.checked);" />
                  </th>
                  <th>{{ column_payment_id }}</th>
                  <th>{{ column_payment_date }}</th>
                  <th>{{ column_supplier }}</th>
                  <th>{{ column_payment_method }}</th>
                  <th class="text-right">{{ column_amount }}</th>
                  <th>{{ column_reference }}</th>
                  <th>{{ column_status }}</th>
                  <th class="text-right">{{ column_action }}</th>
                </tr>
              </thead>
              <tbody id="payment-list">
                {# Data will be loaded via AJAX #}
                <tr>
                  <td class="text-center" colspan="9">{{ text_loading }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </form>
        
        <!-- Bulk Actions -->
        <div class="row">
          <div class="col-sm-6">
            <div class="form-inline bulk-actions">
              <div class="form-group">
                <select id="bulk-action" class="form-control">
                  <option value="">{{ text_bulk_action }}</option>
                  {# TODO: Add bulk actions based on permissions #}
                  <option value="delete">{{ text_delete_selected }}</option> 
                </select>
              </div>
              <button type="button" id="bulk-action-apply" class="btn btn-primary" onclick="PaymentManager.executeBulkAction();">
                <i class="fa fa-check"></i> {{ button_apply }}
              </button>
            </div>
          </div>
          <div class="col-sm-6 text-right">
            {# Export button if needed #}
          </div>
        </div>
        
        <!-- Pagination -->
        <div class="row">
          <div class="col-sm-6 text-left" id="pagination"></div>
          <div class="col-sm-6 text-right pagination-info" id="results"></div>
        </div>
        
      </div>
    </div>
  </div>
</div>

<!-- Loading Overlay -->
<div id="loading-overlay">
  <div class="loading-spinner">
    <i class="fa fa-spinner fa-spin fa-3x fa-fw"></i>
    <span class="sr-only">{{ text_loading }}</span>
  </div>
</div>

<!-- Modal for Payment Form -->
<div class="modal fade" id="modal-payment-form" tabindex="-1" role="dialog" aria-labelledby="modal-payment-form-label">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <!-- Will be loaded via AJAX -->
    </div>
  </div>
</div>

<!-- Modal for Payment View -->
<div class="modal fade" id="modal-payment-view" tabindex="-1" role="dialog" aria-labelledby="modal-payment-view-label">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <!-- Will be loaded via AJAX -->
    </div>
  </div>
</div>

{{ footer }}

<script type="text/javascript">
/**
 * Vendor Payment Manager
 */
var PaymentManager = {
  user_token: '{{ user_token }}',
  
  init: function() {
    this.initializeFilters();
    this.loadPayments(1);
    this.setupEventHandlers();
    $('[data-toggle="tooltip"]').tooltip({container: 'body'});
  },
  
  initializeFilters: function() {
    $('#filter-supplier').select2({
      placeholder: '{{ text_select_supplier }}',
      allowClear: true,
      dropdownParent: $('#filter-supplier').parent()
    });
    
    $('.date').datetimepicker({
      pickTime: false,
      format: 'YYYY-MM-DD',
      useCurrent: false
    });
  },
  
  setupEventHandlers: function() {
    $('#filter-payment-id, #filter-supplier, #filter-payment-method, #filter-status').on('change', function() {
      PaymentManager.loadPayments(1);
    });
     $('#filter-payment-id').on('keyup', function(e) {
        if (e.keyCode == 13) { // Enter key
            PaymentManager.loadPayments(1);
        }
    });
    $('.date').on('dp.change', function() {
      PaymentManager.loadPayments(1);
    });
  },
  
  showLoading: function() {
    $('#loading-overlay').fadeIn(200);
  },
  
  hideLoading: function() {
    $('#loading-overlay').fadeOut(200);
  },
  
  loadPayments: function(page) {
    this.showLoading();
    $.ajax({
      url: 'index.php?route=purchase/vendor_payment/ajaxList&user_token=' + this.user_token,
      type: 'GET',
      data: {
        filter_payment_id: $('#filter-payment-id').val() || '',
        filter_supplier_id: $('#filter-supplier').val() || '',
        filter_payment_method_id: $('#filter-payment-method').val() || '', // Assuming value is ID
        filter_status: $('#filter-status').val() || '',
        filter_date_start: $('#filter-date-start').val() || '',
        filter_date_end: $('#filter-date-end').val() || '',
        page: page || 1
      },
      dataType: 'json',
      success: function(json) {
        PaymentManager.renderPayments(json);
        PaymentManager.hideLoading();
      },
      error: function(xhr, status, error) {
        toastr.error('{{ error_ajax }}');
        PaymentManager.hideLoading();
      }
    });
  },
  
  renderPayments: function(json) {
    var html = '';
    if (json.payments && json.payments.length > 0) {
      for (var i = 0; i < json.payments.length; i++) {
        var payment = json.payments[i];
        html += '<tr>';
        html += '  <td class="text-center"><input type="checkbox" name="selected[]" value="' + payment.payment_id + '" /></td>';
        html += '  <td>' + payment.payment_id + '</td>';
        html += '  <td>' + payment.payment_date + '</td>';
        html += '  <td>' + payment.supplier_name + '</td>';
        html += '  <td>' + payment.payment_method_name + '</td>';
        html += '  <td class="text-right">' + payment.amount_formatted + '</td>';
        html += '  <td>' + (payment.reference || '') + '</td>';
        html += '  <td class="text-center"><span class="label label-' + payment.status_class + '">' + payment.status_text + '</span></td>';
        html += '  <td class="text-right">';
        html += '    <div class="btn-group" role="group">';
        // Add action buttons based on permissions and status
        if (payment.can_view) {
             html += '<button type="button" class="btn btn-info btn-sm" onclick="PaymentManager.viewPayment(' + payment.payment_id + ');" data-toggle="tooltip" title="{{ button_view }}"><i class="fa fa-eye"></i></button>';
        }
        if (payment.can_edit) { // Might be restricted based on status
             html += '<button type="button" class="btn btn-primary btn-sm" onclick="PaymentManager.editPayment(' + payment.payment_id + ');" data-toggle="tooltip" title="{{ button_edit }}"><i class="fa fa-pencil"></i></button>';
        }
        if (payment.can_delete) { // Might be restricted based on status
             html += '<button type="button" class="btn btn-danger btn-sm" onclick="PaymentManager.deletePayment(' + payment.payment_id + ');" data-toggle="tooltip" title="{{ button_delete }}"><i class="fa fa-trash"></i></button>';
        }
        html += '    </div>';
        html += '  </td>';
        html += '</tr>';
      }
    } else {
      html += '<tr><td class="text-center" colspan="9">{{ text_no_results }}</td></tr>';
    }
    $('#payment-list').html(html);
    $('[data-toggle="tooltip"]').tooltip({container: 'body'});
    $('#pagination').html(json.pagination || '');
    $('#results').html(json.results || '');
  },
  
  clearFilters: function() {
    $('#filter-payment-id').val('');
    $('#filter-supplier').val(null).trigger('change');
    $('#filter-payment-method').val('');
    $('#filter-status').val('');
    $('#filter-date-start').val('');
    $('#filter-date-end').val('');
    this.loadPayments(1);
  },
  
  addPayment: function() {
    this.showLoading();
    $('#modal-payment-form .modal-content').load('index.php?route=purchase/vendor_payment/form&user_token=' + this.user_token, function() {
      $('#modal-payment-form').modal('show');
      PaymentManager.hideLoading();
    });
  },
  
  editPayment: function(payment_id) {
     this.showLoading();
    $('#modal-payment-form .modal-content').load('index.php?route=purchase/vendor_payment/form&user_token=' + this.user_token + '&payment_id=' + payment_id, function() {
      $('#modal-payment-form').modal('show');
      PaymentManager.hideLoading();
    });
  },

  viewPayment: function(payment_id) {
     this.showLoading();
    $('#modal-payment-view .modal-content').load('index.php?route=purchase/vendor_payment/view&user_token=' + this.user_token + '&payment_id=' + payment_id, function() {
      $('#modal-payment-view').modal('show');
      PaymentManager.hideLoading();
    });
  },
  
  deletePayment: function(payment_id) {
     // TODO: Implement delete confirmation and AJAX call
     alert('Delete function not implemented yet for payment ID: ' + payment_id);
  },

  executeBulkAction: function() {
      // TODO: Implement bulk actions
      alert('Bulk actions not implemented yet.');
  }

};

$(document).ready(function() {
  PaymentManager.init();
});
</script>
