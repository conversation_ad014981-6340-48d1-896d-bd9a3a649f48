<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/consumer.proto

namespace Google\Api;

if (false) {
    /**
     * This class is deprecated. Use Google\Api\Property\PropertyType instead.
     * @deprecated
     */
    class Property_PropertyType {}
}
class_exists(Property\PropertyType::class);
@trigger_error('Google\Api\Property_PropertyType is deprecated and will be removed in the next major release. Use Google\Api\Property\PropertyType instead', E_USER_DEPRECATED);

