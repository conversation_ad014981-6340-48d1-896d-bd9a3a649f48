{{ header }}{{ column_left }}
<div id="content">
    <div class="page-header">
        <div class="container-fluid">
            <div class="pull-right">
                <a href="{{ back }}" class="btn btn-default"><i class="fa fa-reply"></i> {{ button_back }}</a>
            </div>
            <h1>{{ heading_title }}</h1>
            <ul class="breadcrumb">
                {% for breadcrumb in breadcrumbs %}
                <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
                {% endfor %}
            </ul>
        </div>
    </div>
    <div class="container-fluid">
        <div class="panel panel-default">
            <div class="panel-heading">
                <h3 class="panel-title"><i class="fa fa-pencil"></i> {{ text_form }}</h3>
            </div>
            <div class="panel-body">
                <form action="{{ action }}" method="post" enctype="multipart/form-data" id="form-invoice" class="form-horizontal">
                    <div class="form-group">
                        <label class="col-sm-2 control-label" for="input-invoice-id">{{ entry_invoice_id }}</label>
                        <div class="col-sm-10">
                            <input type="text" name="invoice_id" value="{{ invoice.invoice_id }}" placeholder="{{ entry_invoice_id }}" id="input-invoice-id" class="form-control" readonly />
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label" for="input-customer">{{ entry_customer }}</label>
                        <div class="col-sm-10">
                            <input type="text" name="customer" value="{{ invoice.customer }}" placeholder="{{ entry_customer }}" id="input-customer" class="form-control" readonly />
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label" for="input-status">{{ entry_status }}</label>
                        <div class="col-sm-10">
                            <select name="status" id="input-status" class="form-control">
                                <option value="pending" {% if invoice.status == 'pending' %}selected{% endif %}>{{ text_pending }}</option>
                                <option value="submitted" {% if invoice.status == 'submitted' %}selected{% endif %}>{{ text_submitted }}</option>
                                <option value="accepted" {% if invoice.status == 'accepted' %}selected{% endif %}>{{ text_accepted }}</option>
                                <option value="rejected" {% if invoice.status == 'rejected' %}selected{% endif %}>{{ text_rejected }}</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label" for="input-date-issued">{{ entry_date_issued }}</label>
                        <div class="col-sm-10">
                            <input type="text" name="date_issued" value="{{ invoice.date_issued }}" placeholder="{{ entry_date_issued }}" id="input-date-issued" class="form-control" readonly />
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label" for="input-total">{{ entry_total }}</label>
                        <div class="col-sm-10">
                            <input type="text" name="total" value="{{ invoice.total }}" placeholder="{{ entry_total }}" id="input-total" class="form-control" readonly />
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{{ footer }}
