<h2>{{ text_payment_info }}</h2>
<div class="alert alert-success" id="payment_bluepay_hosted_transaction_msg" style="display:none;"></div>
<table class="table table-striped table-bordered">
  <tr>
    <td>{{ text_order_ref }}</td>
    <td>{{ bluepay_hosted_order.transaction_id }}</td>
  </tr>
  <tr>
    <td>{{ text_order_total }}</td>
    <td>{{ bluepay_hosted_order.total_formatted }}</td>
  </tr>
  <tr>
    <td>{{ text_total_released }}</td>
    <td id="bluepay_hosted_total_released">{{ bluepay_hosted_order.total_released_formatted }}</td>
  </tr>
  <tr>
    <td>{{ text_release_status }}</td>
    <td id="release_status">{% if bluepay_hosted_order.release_status == 1 %}
      <span class="release_text">{{ text_yes }}</span>
      {% else %}
      <span class="release_text">{{ text_no }}</span>&nbsp;&nbsp;
      {% if bluepay_hosted_order.void_status == 0 %}
      <input type="text" width="10" id="release_amount" value="{{ bluepay_hosted_order.total }}"/>
      <a class="button btn btn-primary" id="button-release">{{ button_release }}</a> <span class="btn btn-primary" id="img_loading_release" style="display:none;"><i class="fa fa-circle-o-notch fa-spin fa-lg"></i></span>
      {% endif %}
      {% endif %}</td>
  </tr>
  <tr>
    <td>{{ text_void_status }}</td>
    <td id="void_status">
      {% if bluepay_hosted_order.void_status == 1 %}
      <span class="void_text">{{ text_yes }}</span>
      {% elseif bluepay_hosted_order.void_status == 0 and bluepay_hosted_order.release_status == 1 and bluepay_hosted_order.rebate_status != 1 %}
      <span class="void_text">{{ text_no }}</span>&nbsp;&nbsp; <a class="button btn btn-primary" id="button-void">{{ button_void }}</a> <span class="btn btn-primary" id="img_loading_void" style="display:none;"><i class="fa fa-circle-o-notch fa-spin fa-lg"></i></span>
      {% else %}
      <span class="void_text">{{ text_no }}</span>
      {% endif %}/td>
  </tr>
  <tr>
    <td>{{ text_rebate_status }}</td>
    <td id="rebate_status">
      {% if bluepay_hosted_order.rebate_status == 1 %}
      <span class="rebate_text">{{ text_yes }}</span>
      {% else %}
      <span class="rebate_text">{{ text_no }}</span>&nbsp;&nbsp;
      {% if bluepay_hosted_order.total_released > 0 and bluepay_hosted_order.void_status == 0 %}
      <input type="text" width="10" id="rebate_amount" />
      <a class="button btn btn-primary" id="button-rebate">{{ button_rebate }}</a> <span class="btn btn-primary" id="img_loading_rebate" style="display:none;"><i class="fa fa-circle-o-notch fa-spin fa-lg"></i></span>
      {% endif %}
      {% endif %}</td>
  </tr>
  <tr>
    <td>{{ text_transactions }}:</td>
    <td><table class="table table-striped table-bordered" id="bluepay_hosted_transactions">
        <thead>
          <tr>
            <td class="text-left"><strong>{{ text_column_date_added }}</strong></td>
            <td class="text-left"><strong>{{ text_column_type }}</strong></td>
            <td class="text-left"><strong>{{ text_column_amount }}</strong></td>
          </tr>
        </thead>
        <tbody>
          {% for transaction in bluepay_hosted_order.transactions %}
          <tr>
            <td class="text-left">{{ transaction.date_added }}</td>
            <td class="text-left">{{ transaction.type }}</td>
            <td class="text-left">{{ transaction.amount }}</td>
          </tr>
          {% endfor %}
        </tbody>
      </table></td>
  </tr>
</table>
<script type="text/javascript"><!--
  $(document).on('click', '#button-void', function(){
		if (confirm('{{ text_confirm_void }}')) {
			$.ajax({
				type: 'POST',
				dataType: 'json',
				data: {'order_id': {{ order_id }}},
				url: 'index.php?route=extension/payment/bluepay_hosted/void&user_token={{ user_token }}',
				beforeSend: function() {
					$('#button-void').hide();
					$('#img_loading_void').show();
					$('#bluepay_hosted_transaction_msg').hide();
				},
				success: function(data) {
					if (data.error == false) {
						html = '';
						html += '<tr>';
						html += '<td class="text-left">' + data.data.date_added + '</td>';
						html += '<td class="text-left">void</td>';
						html += '<td class="text-left">' + data.data.total + '</td>';
						html += '</tr>';

						$('.void_text').text('{{ text_yes }}');
						$('.rebate_text').text('{{ text_no }}');
						$('#bluepay_hosted_transactions').append(html);
						$('#button-release').hide();
						$('#release_amount').hide();
						$('#button-rebate').hide();
						$('#rebate_amount').hide();

						if (data.msg != '') {
							$('#bluepay_hosted_transaction_msg').empty().html('<i class="fa fa-check-circle"></i> ' + data.msg).fadeIn();
						}
					}
					if (data.error == true) {
						alert(data.msg);
						$('#button-void').show();
					}

					$('#img_loading_void').hide();
				}
			});
		}
	});
	$(document).on('click', '#button-release', function(){
		if (confirm('{{ text_confirm_release }}')) {
			$.ajax({
				type: 'POST',
				dataType: 'json',
				data: {'order_id': {{ order_id }}, 'amount': $('#release_amount').val()},
				url: 'index.php?route=extension/payment/bluepay_hosted/release&user_token={{ user_token }}',
				beforeSend: function() {
					$('#button-release').hide();
					$('#release_amount').hide();
					$('#img_loading_release').show();
					$('#bluepay_hosted_transaction_msg').hide();
				},
				success: function(data) {
					if (data.error == false) {
						html = '';
						html += '<tr>';
						html += '<td class="text-left">' + data.data.date_added + '</td>';
						html += '<td class="text-left">{{ text_payment }}</td>';
						html += '<td class="text-left">' + data.data.amount + '</td>';
						html += '</tr>';

						$('#bluepay_hosted_transactions').append(html);
						$('#bluepay_hosted_total_released').text(data.data.total);

						if (data.data.release_status == 1) {
							$('.void_text').after('<a style="margin-left: 10px;" id="button-void" class="button btn btn-primary">Void</a>');
							$('.rebate_text').after('<input style="margin-left: 10px;" width="10" type="text" id="rebate_amount"><a style="margin-left: 5px;" id="button-rebate" class="button btn btn-primary">{{ button_rebate }}</a>');
							$('.release_text').text('{{ text_yes }}');
							$('#rebate_amount').val(0.00).show();
						} else {
							$('#button-release').show();
							$('#release_amount').val(0.00);
						}

						if (data.msg != '') {
							$('#bluepay_hosted_transaction_msg').empty().html('<i class="fa fa-check-circle"></i> ' + data.msg).fadeIn();
						}

						$('#button-rebate').show();
						$('#rebate_amount').val(0.00).show();
					}
					if (data.error == true) {
						alert(data.msg);
						$('#button-release').show();
						$('#release_amount').show();
					}

					$('#img_loading_release').hide();
				}
			});
		}
	});
	$(document).on('click', '#button-rebate', function(){
		if (confirm('{{ text_confirm_rebate }}')) {
			$.ajax({
				type: 'POST',
				dataType: 'json',
				data: {'order_id': {{ order_id }}, 'amount': $('#rebate_amount').val()},
				url: 'index.php?route=extension/payment/bluepay_hosted/rebate&user_token={{ user_token }}',
				beforeSend: function() {
					$('#button-rebate').hide();
					$('#rebate_amount').hide();
					$('#img_loading_rebate').show();
					$('#bluepay_hosted_transaction_msg').hide();
				},
				success: function(data) {
					if (data.error == false) {
						html = '';
						html += '<tr>';
						html += '<td class="text-left">' + data.data.date_added + '</td>';
						html += '<td class="text-left">{{ text_rebate }}</td>';
						html += '<td class="text-left">' + data.data.amount + '</td>';
						html += '</tr>';

						$('#bluepay_hosted_transactions').append(html);
						$('#bluepay_hosted_total_released').text(data.data.total_released);

						if (data.data.rebate_status == 1) {
							$('.rebate_text').text('{{ text_yes }}');
							$('#button-void').hide();
						} else {
							$('#button-rebate').show();
							$('#rebate_amount').show();
						}

						if (data.msg != '') {
							$('#bluepay_hosted_transaction_msg').empty().html('<i class="fa fa-check-circle"></i> ' + data.msg).fadeIn();
						}
					}
					if (data.error == true) {
						alert(data.msg);
						$('#button-rebate').show();
						$('#rebate_amount').show();
					}

					$('#img_loading_rebate').hide();
				}
			});
		}
	});
//--></script>