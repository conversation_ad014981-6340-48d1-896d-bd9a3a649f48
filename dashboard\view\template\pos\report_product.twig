{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-search"></i> {{ text_filter }}</h3>
      </div>
      <div class="panel-body">
        <div class="well">
          <div class="row">
            <div class="col-sm-3">
              <div class="form-group">
                <label class="control-label" for="input-date-start">{{ entry_date_start }}</label>
                <div class="input-group date">
                  <input type="text" name="filter_date_start" value="{{ filter_date_start }}" placeholder="{{ entry_date_start }}" data-date-format="YYYY-MM-DD" id="input-date-start" class="form-control" />
                  <span class="input-group-btn">
                    <button type="button" class="btn btn-default"><i class="fa fa-calendar"></i></button>
                  </span>
                </div>
              </div>
            </div>
            <div class="col-sm-3">
              <div class="form-group">
                <label class="control-label" for="input-date-end">{{ entry_date_end }}</label>
                <div class="input-group date">
                  <input type="text" name="filter_date_end" value="{{ filter_date_end }}" placeholder="{{ entry_date_end }}" data-date-format="YYYY-MM-DD" id="input-date-end" class="form-control" />
                  <span class="input-group-btn">
                    <button type="button" class="btn btn-default"><i class="fa fa-calendar"></i></button>
                  </span>
                </div>
              </div>
            </div>
            <div class="col-sm-3">
              <div class="form-group">
                <label class="control-label" for="input-category">{{ entry_category }}</label>
                <select name="filter_category_id" id="input-category" class="form-control">
                  <option value="0">{{ text_all_categories }}</option>
                  {% for category in categories %}
                  <option value="{{ category.category_id }}" {{ category.category_id == filter_category_id ? 'selected="selected"' }}>{{ category.name }}</option>
                  {% endfor %}
                </select>
              </div>
            </div>
            <div class="col-sm-3">
              <div class="form-group">
                <label class="control-label" for="input-branch">{{ entry_branch }}</label>
                <select name="filter_branch_id" id="input-branch" class="form-control">
                  <option value="0">{{ text_all_branches }}</option>
                  {% for branch in branches %}
                  <option value="{{ branch.branch_id }}" {{ branch.branch_id == filter_branch_id ? 'selected="selected"' }}>{{ branch.name }}</option>
                  {% endfor %}
                </select>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-sm-12 text-right">
              <button type="button" id="button-filter" class="btn btn-primary"><i class="fa fa-filter"></i> {{ button_filter }}</button>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="row">
      <div class="col-md-6">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-pie-chart"></i> {{ text_top_categories }}</h3>
          </div>
          <div class="panel-body">
            <canvas id="categoryChart" width="400" height="300"></canvas>
          </div>
        </div>
      </div>
      <div class="col-md-6">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-line-chart"></i> {{ text_daily_sales }}</h3>
          </div>
          <div class="panel-body">
            <canvas id="dailyChart" width="400" height="300"></canvas>
          </div>
        </div>
      </div>
    </div>
    
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-list"></i> {{ text_products_list }}</h3>
      </div>
      <div class="panel-body">
        <div class="table-responsive">
          <table class="table table-bordered table-hover">
            <thead>
              <tr>
                <td class="text-left">{{ column_name }}</td>
                <td class="text-left">{{ column_model }}</td>
                <td class="text-left">{{ column_category }}</td>
                <td class="text-right">{{ column_quantity }}</td>
                <td class="text-right">{{ column_total }}</td>
                <td class="text-right">{{ column_avg_price }}</td>
              </tr>
            </thead>
            <tbody>
              {% if products %}
              {% for product in products %}
              <tr>
                <td class="text-left">{{ product.name }}</td>
                <td class="text-left">{{ product.model }}</td>
                <td class="text-left">{{ product.category }}</td>
                <td class="text-right">{{ product.quantity }}</td>
                <td class="text-right">{{ product.total }}</td>
                <td class="text-right">{{ product.avg_price }}</td>
              </tr>
              {% endfor %}
              {% else %}
              <tr>
                <td class="text-center" colspan="6">{{ text_no_results }}</td>
              </tr>
              {% endif %}
            </tbody>
          </table>
        </div>
        <div class="row">
          <div class="col-sm-6 text-left">{{ pagination }}</div>
          <div class="col-sm-6 text-right">{{ results }}</div>
        </div>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript">
$(document).ready(function() {
  $('.date').datetimepicker({
    pickTime: false
  });
  
  $('#button-filter').on('click', function() {
    var url = 'index.php?route=pos/reports/product&user_token={{ user_token }}';
    
    var filter_date_start = $('input[name=\'filter_date_start\']').val();
    if (filter_date_start) {
      url += '&filter_date_start=' + encodeURIComponent(filter_date_start);
    }
    
    var filter_date_end = $('input[name=\'filter_date_end\']').val();
    if (filter_date_end) {
      url += '&filter_date_end=' + encodeURIComponent(filter_date_end);
    }
    
    var filter_category_id = $('select[name=\'filter_category_id\']').val();
    if (filter_category_id) {
      url += '&filter_category_id=' + encodeURIComponent(filter_category_id);
    }
    
    var filter_branch_id = $('select[name=\'filter_branch_id\']').val();
    if (filter_branch_id) {
      url += '&filter_branch_id=' + encodeURIComponent(filter_branch_id);
    }
    
    location = url;
  });
  
  // Category Chart
  var ctxCategory = document.getElementById('categoryChart').getContext('2d');
  var categoryChart = new Chart(ctxCategory, {
    type: 'pie',
    data: {
      labels: {{ categories_chart.labels|json_encode|raw }},
      datasets: [{
        data: {{ categories_chart.data|json_encode|raw }},
        backgroundColor: [
          'rgba(255, 99, 132, 0.6)',
          'rgba(54, 162, 235, 0.6)',
          'rgba(255, 206, 86, 0.6)',
          'rgba(75, 192, 192, 0.6)',
          'rgba(153, 102, 255, 0.6)'
        ],
        borderColor: [
          'rgba(255, 99, 132, 1)',
          'rgba(54, 162, 235, 1)',
          'rgba(255, 206, 86, 1)',
          'rgba(75, 192, 192, 1)',
          'rgba(153, 102, 255, 1)'
        ],
        borderWidth: 1
      }]
    },
    options: {
      responsive: true,
      legend: {
        position: 'bottom'
      },
      title: {
        display: true,
        text: '{{ text_top_categories_chart }}'
      }
    }
  });
  
  // Daily Sales Chart
  var ctxDaily = document.getElementById('dailyChart').getContext('2d');
  var dailyChart = new Chart(ctxDaily, {
    type: 'line',
    data: {
      labels: {{ daily_chart.labels|json_encode|raw }},
      datasets: [{
        label: '{{ text_daily_sales }}',
        data: {{ daily_chart.data|json_encode|raw }},
        backgroundColor: 'rgba(54, 162, 235, 0.2)',
        borderColor: 'rgba(54, 162, 235, 1)',
        borderWidth: 2,
        fill: true,
        tension: 0.4
      }]
    },
    options: {
      responsive: true,
      scales: {
        y: {
          beginAtZero: true
        }
      },
      title: {
        display: true,
        text: '{{ text_daily_sales_chart }}'
      }
    }
  });
});
</script>
{{ footer }}