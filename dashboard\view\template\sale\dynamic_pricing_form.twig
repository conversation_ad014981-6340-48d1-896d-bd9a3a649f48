{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="submit" form="form-pricing" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fa fa-save"></i></button>
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a></div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    {% if error_name %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_name }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pencil"></i> {{ text_form }}</h3>
      </div>
      <div class="panel-body">
        <form action="{{ action }}" method="post" enctype="multipart/form-data" id="form-pricing" class="form-horizontal">
          <ul class="nav nav-tabs">
            <li class="active"><a href="#tab-general" data-toggle="tab">{{ tab_general }}</a></li>
            <li><a href="#tab-conditions" data-toggle="tab">{{ tab_conditions }}</a></li>
            <li><a href="#tab-actions" data-toggle="tab">{{ tab_actions }}</a></li>
          </ul>
          <div class="tab-content">
            <div class="tab-pane active" id="tab-general">
              <div class="form-group required">
                <label class="col-sm-2 control-label" for="input-name">{{ entry_name }}</label>
                <div class="col-sm-10">
                  <input type="text" name="name" value="{{ name }}" placeholder="{{ entry_name }}" id="input-name" class="form-control" />
                  {% if error_name %}
                  <div class="text-danger">{{ error_name }}</div>
                  {% endif %}
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-rule-type">{{ entry_rule_type }}</label>
                <div class="col-sm-10">
                  <select name="rule_type" id="input-rule-type" class="form-control">
                    <option value="product"{% if rule_type == 'product' %} selected="selected"{% endif %}>{{ text_product }}</option>
                    <option value="category"{% if rule_type == 'category' %} selected="selected"{% endif %}>{{ text_category }}</option>
                    <option value="customer"{% if rule_type == 'customer' %} selected="selected"{% endif %}>{{ text_customer }}</option>
                    <option value="global"{% if rule_type == 'global' %} selected="selected"{% endif %}>{{ text_global }}</option>
                  </select>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-priority">{{ entry_priority }}</label>
                <div class="col-sm-10">
                  <input type="number" name="priority" value="{{ priority }}" placeholder="{{ entry_priority }}" id="input-priority" class="form-control" min="1" max="100" />
                  <small class="form-text text-muted">{{ help_priority }}</small>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-status">{{ entry_status }}</label>
                <div class="col-sm-10">
                  <label class="radio-inline">
                    {% if status %}
                    <input type="radio" name="status" value="1" checked="checked" />
                    {% else %}
                    <input type="radio" name="status" value="1" />
                    {% endif %}
                    {{ text_enabled }}
                  </label>
                  <label class="radio-inline">
                    {% if not status %}
                    <input type="radio" name="status" value="0" checked="checked" />
                    {% else %}
                    <input type="radio" name="status" value="0" />
                    {% endif %}
                    {{ text_disabled }}
                  </label>
                </div>
              </div>
            </div>
            <div class="tab-pane" id="tab-conditions">
              <div class="form-group required">
                <label class="col-sm-2 control-label" for="input-condition-type">{{ entry_condition_type }}</label>
                <div class="col-sm-10">
                  <select name="condition_type" id="input-condition-type" class="form-control">
                    <option value="quantity"{% if condition_type == 'quantity' %} selected="selected"{% endif %}>{{ text_quantity }}</option>
                    <option value="customer_group"{% if condition_type == 'customer_group' %} selected="selected"{% endif %}>{{ text_customer_group }}</option>
                    <option value="product_category"{% if condition_type == 'product_category' %} selected="selected"{% endif %}>{{ text_product_category }}</option>
                    <option value="total_amount"{% if condition_type == 'total_amount' %} selected="selected"{% endif %}>{{ text_total_amount }}</option>
                    <option value="time_of_day"{% if condition_type == 'time_of_day' %} selected="selected"{% endif %}>{{ text_time_of_day }}</option>
                    <option value="day_of_week"{% if condition_type == 'day_of_week' %} selected="selected"{% endif %}>{{ text_day_of_week }}</option>
                  </select>
                </div>
              </div>
              <div class="form-group required">
                <label class="col-sm-2 control-label" for="input-condition-value">{{ entry_condition_value }}</label>
                <div class="col-sm-10">
                  <input type="text" name="condition_value" value="{{ condition_value }}" placeholder="{{ entry_condition_value }}" id="input-condition-value" class="form-control" />
                  <small class="form-text text-muted" id="condition-help">{{ help_condition_value }}</small>
                  {% if error_condition_value %}
                  <div class="text-danger">{{ error_condition_value }}</div>
                  {% endif %}
                </div>
              </div>
            </div>
            <div class="tab-pane" id="tab-actions">
              <div class="form-group required">
                <label class="col-sm-2 control-label" for="input-action-type">{{ entry_action_type }}</label>
                <div class="col-sm-10">
                  <select name="action_type" id="input-action-type" class="form-control">
                    <option value="percentage"{% if action_type == 'percentage' %} selected="selected"{% endif %}>{{ text_percentage_discount }}</option>
                    <option value="fixed_amount"{% if action_type == 'fixed_amount' %} selected="selected"{% endif %}>{{ text_fixed_amount_discount }}</option>
                    <option value="multiply"{% if action_type == 'multiply' %} selected="selected"{% endif %}>{{ text_multiply_price }}</option>
                    <option value="set_price"{% if action_type == 'set_price' %} selected="selected"{% endif %}>{{ text_set_price }}</option>
                  </select>
                </div>
              </div>
              <div class="form-group required">
                <label class="col-sm-2 control-label" for="input-action-value">{{ entry_action_value }}</label>
                <div class="col-sm-10">
                  <input type="number" name="action_value" value="{{ action_value }}" placeholder="{{ entry_action_value }}" id="input-action-value" class="form-control" step="0.01" min="0" />
                  <small class="form-text text-muted" id="action-help">{{ help_action_value }}</small>
                  {% if error_action_value %}
                  <div class="text-danger">{{ error_action_value }}</div>
                  {% endif %}
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript">
$('#input-condition-type').on('change', function() {
    var conditionType = $(this).val();
    var helpText = '';
    
    switch(conditionType) {
        case 'quantity':
            helpText = '{{ help_condition_quantity }}';
            break;
        case 'customer_group':
            helpText = '{{ help_condition_customer_group }}';
            break;
        case 'product_category':
            helpText = '{{ help_condition_product_category }}';
            break;
        case 'total_amount':
            helpText = '{{ help_condition_total_amount }}';
            break;
        case 'time_of_day':
            helpText = '{{ help_condition_time_of_day }}';
            break;
        case 'day_of_week':
            helpText = '{{ help_condition_day_of_week }}';
            break;
    }
    
    $('#condition-help').text(helpText);
});

$('#input-action-type').on('change', function() {
    var actionType = $(this).val();
    var helpText = '';
    
    switch(actionType) {
        case 'percentage':
            helpText = '{{ help_action_percentage }}';
            break;
        case 'fixed_amount':
            helpText = '{{ help_action_fixed_amount }}';
            break;
        case 'multiply':
            helpText = '{{ help_action_multiply }}';
            break;
        case 'set_price':
            helpText = '{{ help_action_set_price }}';
            break;
    }
    
    $('#action-help').text(helpText);
});

// Trigger change events on page load
$('#input-condition-type').trigger('change');
$('#input-action-type').trigger('change');
</script>
{{ footer }} 