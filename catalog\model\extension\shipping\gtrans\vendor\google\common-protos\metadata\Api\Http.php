<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/http.proto

namespace GPBMetadata\Google\Api;

class Http
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(hex2bin(
            "0aa2040a15676f6f676c652f6170692f687474702e70726f746f120a676f" .
            "6f676c652e61706922540a044874747012230a0572756c65731801200328" .
            "0b32142e676f6f676c652e6170692e4874747052756c6512270a1f66756c" .
            "6c795f6465636f64655f72657365727665645f657870616e73696f6e1802" .
            "200128082281020a084874747052756c6512100a0873656c6563746f7218" .
            "0120012809120d0a036765741802200128094800120d0a03707574180320" .
            "0128094800120e0a04706f7374180420012809480012100a0664656c6574" .
            "651805200128094800120f0a0570617463681806200128094800122f0a06" .
            "637573746f6d18082001280b321d2e676f6f676c652e6170692e43757374" .
            "6f6d487474705061747465726e4800120c0a04626f647918072001280912" .
            "150a0d726573706f6e73655f626f6479180c2001280912310a1361646469" .
            "74696f6e616c5f62696e64696e6773180b2003280b32142e676f6f676c65" .
            "2e6170692e4874747052756c6542090a077061747465726e222f0a114375" .
            "73746f6d487474705061747465726e120c0a046b696e6418012001280912" .
            "0c0a0470617468180220012809426a0a0e636f6d2e676f6f676c652e6170" .
            "6942094874747050726f746f50015a41676f6f676c652e676f6c616e672e" .
            "6f72672f67656e70726f746f2f676f6f676c65617069732f6170692f616e" .
            "6e6f746174696f6e733b616e6e6f746174696f6e73f80101a20204474150" .
            "49620670726f746f33"
        ), true);

        static::$is_initialized = true;
    }
}

