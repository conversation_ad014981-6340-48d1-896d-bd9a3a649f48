{{ header }}{{ column_left }}
<div id="content">

<link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css" rel="stylesheet" />
<script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>
<link href="https://cdn.datatables.net/1.10.24/css/jquery.dataTables.min.css" rel="stylesheet" />
<script src="https://cdn.datatables.net/1.10.24/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/buttons/1.7.0/js/dataTables.buttons.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
<script src="https://cdn.datatables.net/buttons/1.7.0/js/buttons.html5.min.js"></script>
<script src="https://cdn.datatables.net/buttons/1.7.0/js/buttons.print.min.js"></script>

<div class="page-header">
  <div class="container-fluid">
    <div class="pull-right">
      <a href="{{ add }}" data-toggle="tooltip" title="{{ button_add }}" class="btn btn-primary"><i class="fa fa-plus"></i></a>
      <button type="button" data-toggle="tooltip" title="{{ button_delete }}" class="btn btn-danger" onclick="confirmDelete();"><i class="fa fa-trash-o"></i></button>
    </div>
    <ul class="breadcrumb">
      {% for breadcrumb in breadcrumbs %}
      <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
      {% endfor %}
    </ul>
  </div>
</div>
<div class="container-fluid">
  {% if error_warning %}
  <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
    <button type="button" class="close" data-dismiss="alert">&times;</button>
  </div>
  {% endif %}
  {% if success %}
  <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{ success }}
    <button type="button" class="close" data-dismiss="alert">&times;</button>
  </div>
  {% endif %}
  <div class="panel panel-default">
    <div class="panel-heading">
      <h3 class="panel-title"><i class="fa fa-list"></i> {{ text_list }}</h3>
    </div>
    <div class="panel-body">
      <form action="{{ delete }}" method="post" enctype="multipart/form-data" id="form-requisition">
<div id="filter-requisition" class="well">
  <div class="row">
    <div class="col-sm-4">
      <div class="form-group">
        <label class="control-label" for="input-requisition-id">{{ entry_requisition_id }}</label>
        <input type="text" name="filter_requisition_id" value="{{ filter_requisition_id }}" placeholder="{{ entry_requisition_id }}" id="input-requisition-id" class="form-control" />
      </div>
      <div class="form-group">
        <label class="control-label" for="input-user">{{ entry_user }}</label>
        <input type="text" name="filter_user" value="{{ filter_user }}" placeholder="{{ entry_user }}" id="input-user" class="form-control" />
      </div>
    </div>
    <div class="col-sm-4">
      <div class="form-group">
        <label class="control-label" for="input-department">{{ entry_department }}</label>
        <select name="filter_department" id="input-department" class="form-control">
          <option value=""></option>
          {% for department in departments %}
          {% if department.department_id == filter_department %}
          <option value="{{ department.department_id }}" selected="selected">{{ department.name }}</option>
          {% else %}
          <option value="{{ department.department_id }}">{{ department.name }}</option>
          {% endif %}
          {% endfor %}
        </select>
      </div>
      <div class="form-group">
        <label class="control-label" for="input-branch">{{ entry_branch }}</label>
        <select name="filter_branch" id="input-branch" class="form-control">
          <option value=""></option>
          {% for branch in branches %}
          {% if branch.branch_id == filter_branch %}
          <option value="{{ branch.branch_id }}" selected="selected">{{ branch.name }}</option>
          {% else %}
          <option value="{{ branch.branch_id }}">{{ branch.name }}</option>
          {% endif %}
          {% endfor %}
        </select>
      </div>
    </div>
    <div class="col-sm-4">
      <div class="form-group">
        <label class="control-label" for="input-status">{{ entry_status }}</label>
        <select name="filter_status" id="input-status" class="form-control">
          <option value=""></option>
          {% for status in statuses %}
          {% if status.status == filter_status %}
          <option value="{{ status.status }}" selected="selected">{{ status.name }}</option>
          {% else %}
          <option value="{{ status.status }}">{{ status.name }}</option>
          {% endif %}
          {% endfor %}
        </select>
      </div>
      <div class="form-group">
        <label class="control-label" for="input-date-added">{{ entry_date_added }}</label>
        <div class="input-group date">
          <input type="text" name="filter_date_added" value="{{ filter_date_added }}" placeholder="{{ entry_date_added }}" data-date-format="YYYY-MM-DD" id="input-date-added" class="form-control" />
          <span class="input-group-btn">
          <button type="button" class="btn btn-default"><i class="fa fa-calendar"></i></button>
          </span>
        </div>
      </div>
    </div>
  </div>
  <div class="row">
    <div class="col-sm-12">
      <button type="button" id="button-filter" class="btn btn-primary pull-right"><i class="fa fa-filter"></i> {{ button_filter }}</button>
    </div>
  </div>
</div>
        <div class="table-responsive">
<div class="table-responsive">
  <table class="table table-bordered table-hover">
    <thead>
      <tr>
        <td style="width: 1px;" class="text-center"><input type="checkbox" onclick="$('input[name*=\'selected\']').prop('checked', this.checked);" /></td>
        <td class="text-left">{% if sort == 'r.requisition_id' %}
          <a href="{{ sort_requisition_id }}" class="{{ order|lower }}">{{ column_requisition_id }}</a>
          {% else %}
          <a href="{{ sort_requisition_id }}">{{ column_requisition_id }}</a>
          {% endif %}</td>
        <td class="text-left">{% if sort == 'user' %}
          <a href="{{ sort_user }}" class="{{ order|lower }}">{{ column_user }}</a>
          {% else %}
          <a href="{{ sort_user }}">{{ column_user }}</a>
          {% endif %}</td>
        <td class="text-left">{% if sort == 'department' %}
          <a href="{{ sort_department }}" class="{{ order|lower }}">{{ column_department }}</a>
          {% else %}
          <a href="{{ sort_department }}">{{ column_department }}</a>
          {% endif %}</td>
        <td class="text-left">{% if sort == 'branch' %}
          <a href="{{ sort_branch }}" class="{{ order|lower }}">{{ column_branch }}</a>
          {% else %}
          <a href="{{ sort_branch }}">{{ column_branch }}</a>
          {% endif %}</td>
        <td class="text-left">{% if sort == 'r.status' %}
          <a href="{{ sort_status }}" class="{{ order|lower }}">{{ column_status }}</a>
          {% else %}
          <a href="{{ sort_status }}">{{ column_status }}</a>
          {% endif %}</td>
        <td class="text-left">{% if sort == 'r.date_added' %}
          <a href="{{ sort_date_added }}" class="{{ order|lower }}">{{ column_date_added }}</a>
          {% else %}
          <a href="{{ sort_date_added }}">{{ column_date_added }}</a>
          {% endif %}</td>
        <td class="text-left">{% if sort == 'r.date_required' %}
          <a href="{{ sort_date_required }}" class="{{ order|lower }}">{{ column_date_required }}</a>
          {% else %}
          <a href="{{ sort_date_required }}">{{ column_date_required }}</a>
          {% endif %}</td>
        <td class="text-right">{{ column_action }}</td>
      </tr>
    </thead>
    <tbody>
      {% if requisitions %}
      {% for requisition in requisitions %}
      <tr>
        <td class="text-center">{% if requisition.requisition_id in selected %}
          <input type="checkbox" name="selected[]" value="{{ requisition.requisition_id }}" checked="checked" />
          {% else %}
          <input type="checkbox" name="selected[]" value="{{ requisition.requisition_id }}" />
          {% endif %}</td>
        <td class="text-left">{{ requisition.requisition_id }}</td>
        <td class="text-left">{{ requisition.user }}</td>
        <td class="text-left">{{ requisition.department }}</td>
        <td class="text-left">{{ requisition.branch }}</td>
        <td class="text-left">{{ requisition.status }}</td>
        <td class="text-left">{{ requisition.date_added }}</td>
        <td class="text-left">{{ requisition.date_required }}</td>
        <td class="text-right"><a href="{{ requisition.view }}" data-toggle="tooltip" title="{{ button_view }}" class="btn btn-info"><i class="fa fa-eye"></i></a> <a href="{{ requisition.edit }}" data-toggle="tooltip" title="{{ button_edit }}" class="btn btn-primary"><i class="fa fa-pencil"></i></a> <a href="{{ requisition.delete }}" data-toggle="tooltip" title="{{ button_delete }}" class="btn btn-danger" onclick="return confirm('{{ text_confirm }}');"><i class="fa fa-trash-o"></i></a></td>
      </tr>
      {% endfor %}
      {% else %}
      <tr>
        <td class="text-center" colspan="9">{{ text_no_results }}</td>
      </tr>
      {% endif %}
    </tbody>
  </table>
</div>
        </div>
      </form>
    </div>
  </div>
</div>

<script type="text/javascript"><!--
$(document).ready(function() {
    // Initialize Select2 Elements
    $('.form-control').select2();
    
    // Initialize Datepicker
    $('.date').datetimepicker({
        pickTime: false
    });

    var table = $('#requisition-table').DataTable({
        "processing": true,
        "serverSide": true,
        "ajax": {
            "url": "{{ ajax_url }}",
            "type": "POST",
            "data": function (d) {
                d.filter_product = $('input[name=\'filter_product\']').val();
                d.filter_department = $('select[name=\'filter_department\']').val();
                d.filter_branch = $('select[name=\'filter_branch\']').val();
                d.filter_status = $('select[name=\'filter_status\']').val();
                d.filter_user = $('input[name=\'filter_user\']').val();
                d.filter_unit = $('select[name=\'filter_unit\']').val();
                d.filter_date_start = $('input[name=\'filter_date_start\']').val();
                d.filter_date_end = $('input[name=\'filter_date_end\']').val();
            }
        },
        "columns": [
            { "data": "checkbox", "orderable": false },
            { "data": "requisition_id" },
            { "data": "username" },
            { "data": "department" },
            { "data": "branch" },
            { "data": "status" },
            { "data": "date_added" },
            { "data": "date_required" },
            { "data": "action", "orderable": false }
        ],
        "order": [[ 1, "desc" ]],
        "pageLength": 25,
        "dom": 'Bfrtip',
        "buttons": [
            'copy', 'csv', 'excel', 'pdf', 'print'
        ]
    });

    // Trigger search on input change
    $('input[name=\'filter_product\'], input[name=\'filter_user\'], input[name=\'filter_date_start\'], input[name=\'filter_date_end\']').on('keyup', function() {
        table.ajax.reload();
    });

    $('select[name=\'filter_department\'], select[name=\'filter_branch\'], select[name=\'filter_status\'], select[name=\'filter_unit\']').on('change', function() {
        table.ajax.reload();
    });
});

function confirmDelete() {
    if (confirm('{{ text_confirm }}')) {
        $('#form-requisition').submit();
    }
}
//--></script>
{{ footer }}