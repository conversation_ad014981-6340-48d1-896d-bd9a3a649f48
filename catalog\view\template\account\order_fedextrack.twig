{{ header }}

<div id="account-order" class="container">
  <ul class="breadcrumb">
    {% for breadcrumb in breadcrumbs %}
    <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
    {% endfor %}
  </ul>
  </div>
  
{# <div class="history-tl-container">
<h2>{{ text_track_status }}</h2>
 {{no_info}}
  <ul class="tl">
  
    {% for activity in activities %}
    <li class="tl-item" ng-repeat="item in retailer_history">
      <div class="" id="align-dat">
      
        {{ activity.date}} <br>  {{ activity.time|date('H:i:s') }}
      </div>
      <div class="item-title">{{ activity.event_status }}</div>
      <div class="item-detail">{{ activity.ServiceArea.Description }}</div>
    </li>
     {% endfor %}
  </ul>
  

</div> #}
<div class="history-tl-container">
<h2>{{ text_track_status }}</h2>
 {{no_info}}
 {% if activities %}
  <ul class="tl">
    <li class="tl-item" ng-repeat="item in retailer_history">
      <div class="" id="align-dat">
      
        {{ activities.date}} <br>  {{ activities.time|date('H:i:s') }}
      </div>
      <div class="item-title">{{ activities.event_status }}</div>
      <div class="item-detail">{{ activities.ServiceArea.Description }}</div>
    </li>
     
  </ul>
  
{% endif %}
</div>


{{ footer }}