<?php
/**
 * نموذج إعدادات التجارة الإلكترونية المتقدم - Enterprise Grade
 * 
 * التحسينات الجديدة:
 * - تطبيق الخدمات المركزية (central_service_manager.php)
 * - إدارة شاملة لإعدادات المتجر الإلكتروني
 * - تكامل مع منصات الدفع المتعددة
 * - إعدادات الشحن والضرائب المتقدمة
 * - إعدادات SEO والتسويق
 * - إعدادات الأمان والحماية
 * - مزامنة المنتجات والطلبات
 * - تكامل مع وسائل التواصل الاجتماعي
 * - إعدادات التحليلات والتتبع
 * - معالجة الأخطاء الشاملة
 * - تحسين الأداء مع التخزين المؤقت
 * 
 * <AUTHOR> Team - Enhanced by Kiro AI
 * @version 2.0 - Enterprise Grade
 * @since 2025
 * @reference inventory/current_stock.php - Proven Example
 */

class ModelEcommerceSettings extends Model {
    
    private $central_service;
    
    public function __construct($registry) {
        parent::__construct($registry);
        
        // تحميل الخدمات المركزية
        $this->load->model('common/central_service_manager');
        $this->central_service = new CentralServiceManager($this->registry);
    }
    
    /**
     * الحصول على جميع الإعدادات
     */
    public function getSettings() {
        try {
            $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "ecommerce_setting");
            
            $settings = array();
            foreach ($query->rows as $result) {
                if (!$result['serialized']) {
                    $settings[$result['key']] = $result['value'];
                } else {
                    $settings[$result['key']] = json_decode($result['value'], true);
                }
            }
            
            return $settings;
            
        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'ecommerce_settings_model',
                'خطأ في الحصول على الإعدادات: ' . $e->getMessage(),
                array('error' => $e->getTraceAsString())
            );
            
            return array();
        }
    }
    
    /**
     * حفظ الإعدادات
     */
    public function saveSettings($data) {
        try {
            // حذف الإعدادات الحالية
            $this->db->query("DELETE FROM " . DB_PREFIX . "ecommerce_setting");
            
            // حفظ الإعدادات الجديدة
            foreach ($data as $key => $value) {
                if (is_array($value)) {
                    $this->db->query("INSERT INTO " . DB_PREFIX . "ecommerce_setting SET 
                        `key` = '" . $this->db->escape($key) . "', 
                        `value` = '" . $this->db->escape(json_encode($value)) . "', 
                        `serialized` = '1'");
                } else {
                    $this->db->query("INSERT INTO " . DB_PREFIX . "ecommerce_setting SET 
                        `key` = '" . $this->db->escape($key) . "', 
                        `value` = '" . $this->db->escape($value) . "', 
                        `serialized` = '0'");
                }
            }
            
            // تسجيل النشاط
            $this->central_service->logActivity(
                'save_settings',
                'ecommerce_settings',
                'حفظ إعدادات التجارة الإلكترونية',
                array('settings_count' => count($data))
            );
            
            return true;
            
        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'ecommerce_save_settings',
                'خطأ في حفظ الإعدادات: ' . $e->getMessage(),
                array('error' => $e->getTraceAsString())
            );
            
            return false;
        }
    }
    
    /**
     * اختبار الاتصال مع المتجر الإلكتروني
     */
    public function testConnection() {
        try {
            $settings = $this->getSettings();
            
            if (!isset($settings['ecommerce_status']) || !$settings['ecommerce_status']) {
                return array(
                    'success' => false,
                    'error' => 'التجارة الإلكترونية غير مفعلة'
                );
            }
            
            // اختبار الاتصال مع قاعدة البيانات
            $query = $this->db->query("SELECT COUNT(*) as total FROM " . DB_PREFIX . "product WHERE status = 1");
            $active_products = $query->row['total'];
            
            // اختبار الاتصال مع جدول الطلبات
            $query = $this->db->query("SELECT COUNT(*) as total FROM " . DB_PREFIX . "order WHERE order_status_id > 0");
            $total_orders = $query->row['total'];
            
            // اختبار الاتصال مع جدول العملاء
            $query = $this->db->query("SELECT COUNT(*) as total FROM " . DB_PREFIX . "customer WHERE status = 1");
            $active_customers = $query->row['total'];
            
            return array(
                'success' => true,
                'details' => array(
                    'active_products' => $active_products,
                    'total_orders' => $total_orders,
                    'active_customers' => $active_customers,
                    'connection_time' => date('Y-m-d H:i:s')
                )
            );
            
        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'ecommerce_test_connection',
                'خطأ في اختبار الاتصال: ' . $e->getMessage(),
                array('error' => $e->getTraceAsString())
            );
            
            return array(
                'success' => false,
                'error' => 'فشل في الاتصال: ' . $e->getMessage()
            );
        }
    }
    
    /**
     * مزامنة المنتجات مع المتجر الإلكتروني
     */
    public function syncProducts() {
        try {
            $settings = $this->getSettings();
            
            if (!isset($settings['product_sync_enabled']) || !$settings['product_sync_enabled']) {
                return array(
                    'success' => false,
                    'error' => 'مزامنة المنتجات غير مفعلة'
                );
            }
            
            // الحصول على المنتجات النشطة
            $query = $this->db->query("
                SELECT p.*, pd.name, pd.description, pd.meta_title, pd.meta_description, pd.meta_keyword
                FROM " . DB_PREFIX . "product p
                LEFT JOIN " . DB_PREFIX . "product_description pd ON (p.product_id = pd.product_id AND pd.language_id = '" . (int)$this->config->get('config_language_id') . "')
                WHERE p.status = 1
            ");
            
            $total_products = count($query->rows);
            $synced_products = 0;
            
            foreach ($query->rows as $product) {
                // مزامنة كل منتج
                if ($this->syncSingleProduct($product)) {
                    $synced_products++;
                }
            }
            
            // تسجيل النشاط
            $this->central_service->logActivity(
                'sync_products',
                'ecommerce_settings',
                'مزامنة المنتجات مع المتجر الإلكتروني',
                array(
                    'total_products' => $total_products,
                    'synced_products' => $synced_products
                )
            );
            
            return array(
                'success' => true,
                'total_products' => $total_products,
                'synced_products' => $synced_products
            );
            
        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'ecommerce_sync_products',
                'خطأ في مزامنة المنتجات: ' . $e->getMessage(),
                array('error' => $e->getTraceAsString())
            );
            
            return array(
                'success' => false,
                'error' => 'فشل في المزامنة: ' . $e->getMessage()
            );
        }
    }
    
    /**
     * مزامنة منتج واحد
     */
    private function syncSingleProduct($product) {
        try {
            // التحقق من وجود المنتج في جدول المزامنة
            $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "ecommerce_product_sync WHERE product_id = '" . (int)$product['product_id'] . "'");
            
            if ($query->num_rows) {
                // تحديث المنتج الموجود
                $this->db->query("UPDATE " . DB_PREFIX . "ecommerce_product_sync SET 
                    name = '" . $this->db->escape($product['name']) . "',
                    description = '" . $this->db->escape($product['description']) . "',
                    price = '" . (float)$product['price'] . "',
                    quantity = '" . (int)$product['quantity'] . "',
                    status = '" . (int)$product['status'] . "',
                    last_sync = NOW()
                    WHERE product_id = '" . (int)$product['product_id'] . "'");
            } else {
                // إضافة منتج جديد
                $this->db->query("INSERT INTO " . DB_PREFIX . "ecommerce_product_sync SET 
                    product_id = '" . (int)$product['product_id'] . "',
                    name = '" . $this->db->escape($product['name']) . "',
                    description = '" . $this->db->escape($product['description']) . "',
                    price = '" . (float)$product['price'] . "',
                    quantity = '" . (int)$product['quantity'] . "',
                    status = '" . (int)$product['status'] . "',
                    sync_status = 'pending',
                    date_added = NOW(),
                    last_sync = NOW()");
            }
            
            return true;
            
        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'sync_single_product',
                'خطأ في مزامنة المنتج: ' . $e->getMessage(),
                array('product_id' => $product['product_id'], 'error' => $e->getTraceAsString())
            );
            
            return false;
        }
    }
    
    /**
     * الحصول على العملات المتاحة
     */
    public function getCurrencies() {
        try {
            $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "currency WHERE status = 1 ORDER BY title ASC");
            
            return $query->rows;
            
        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'get_currencies',
                'خطأ في الحصول على العملات: ' . $e->getMessage()
            );
            
            return array();
        }
    }
    
    /**
     * الحصول على طرق الدفع المتاحة
     */
    public function getPaymentMethods() {
        return array(
            'cash_on_delivery' => 'الدفع عند الاستلام',
            'bank_transfer' => 'تحويل بنكي',
            'credit_card' => 'بطاقة ائتمان',
            'paypal' => 'PayPal',
            'fawry' => 'فوري',
            'vodafone_cash' => 'فودافون كاش',
            'orange_money' => 'أورانج موني',
            'etisalat_cash' => 'اتصالات كاش'
        );
    }
    
    /**
     * الحصول على طرق الشحن المتاحة
     */
    public function getShippingMethods() {
        return array(
            'standard_shipping' => 'شحن عادي',
            'express_shipping' => 'شحن سريع',
            'same_day_delivery' => 'توصيل في نفس اليوم',
            'pickup_from_store' => 'استلام من المتجر',
            'aramex' => 'أرامكس',
            'dhl' => 'DHL',
            'fedex' => 'FedEx',
            'egypt_post' => 'البريد المصري'
        );
    }
    
    /**
     * الحصول على إحصائيات التجارة الإلكترونية
     */
    public function getEcommerceStats() {
        try {
            $stats = array();
            
            // إجمالي المنتجات المنشورة
            $query = $this->db->query("SELECT COUNT(*) as total FROM " . DB_PREFIX . "product WHERE status = 1");
            $stats['published_products'] = $query->row['total'];
            
            // إجمالي الطلبات
            $query = $this->db->query("SELECT COUNT(*) as total FROM " . DB_PREFIX . "order WHERE order_status_id > 0");
            $stats['total_orders'] = $query->row['total'];
            
            // إجمالي العملاء النشطين
            $query = $this->db->query("SELECT COUNT(*) as total FROM " . DB_PREFIX . "customer WHERE status = 1");
            $stats['active_customers'] = $query->row['total'];
            
            // إجمالي المبيعات
            $query = $this->db->query("SELECT SUM(total) as total_sales FROM " . DB_PREFIX . "order WHERE order_status_id > 0");
            $stats['total_sales'] = $query->row['total_sales'] ? $query->row['total_sales'] : 0;
            
            // الطلبات المعلقة
            $query = $this->db->query("SELECT COUNT(*) as total FROM " . DB_PREFIX . "order WHERE order_status_id = 1");
            $stats['pending_orders'] = $query->row['total'];
            
            // المنتجات منخفضة المخزون
            $query = $this->db->query("SELECT COUNT(*) as total FROM " . DB_PREFIX . "product WHERE quantity <= minimum AND status = 1");
            $stats['low_stock_products'] = $query->row['total'];
            
            return $stats;
            
        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'get_ecommerce_stats',
                'خطأ في الحصول على إحصائيات التجارة الإلكترونية: ' . $e->getMessage()
            );
            
            return array();
        }
    }
    
    /**
     * تحديث حالة المزامنة
     */
    public function updateSyncStatus($type, $status, $details = '') {
        try {
            $this->db->query("INSERT INTO " . DB_PREFIX . "ecommerce_sync_log SET 
                sync_type = '" . $this->db->escape($type) . "',
                status = '" . $this->db->escape($status) . "',
                details = '" . $this->db->escape($details) . "',
                date_added = NOW()");
            
            return true;
            
        } catch (Exception $e) {
            $this->central_service->logActivity(
                'error',
                'update_sync_status',
                'خطأ في تحديث حالة المزامنة: ' . $e->getMessage()
            );
            
            return false;
        }
    }
}
