{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <a href="{{ export_excel }}" data-toggle="tooltip" title="{{ button_export_excel }}" class="btn btn-success"><i class="fa fa-file-excel-o"></i></a>
        <a href="{{ export_pdf }}" data-toggle="tooltip" title="{{ button_export_pdf }}" class="btn btn-danger"><i class="fa fa-file-pdf-o"></i></a>
        <a href="{{ export_csv }}" data-toggle="tooltip" title="{{ button_export_csv }}" class="btn btn-info"><i class="fa fa-file-text-o"></i></a>
        <a href="{{ print }}" data-toggle="tooltip" title="{{ button_print }}" class="btn btn-warning" target="_blank"><i class="fa fa-print"></i></a>
        <a href="{{ back }}" data-toggle="tooltip" title="{{ button_back }}" class="btn btn-default"><i class="fa fa-reply"></i></a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if success %}
    <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    
    <!-- معلومات التقرير -->
    <div class="panel panel-primary">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-info-circle"></i> {{ text_report_info }}</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-md-6">
            <table class="table table-striped">
              <tr>
                <td><strong>{{ text_report_type }}:</strong></td>
                <td>{{ aging_data.filter_data.report_type_name }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_report_date }}:</strong></td>
                <td>{{ aging_data.filter_data.date_end }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_currency }}:</strong></td>
                <td>{{ aging_data.filter_data.currency }}</td>
              </tr>
            </table>
          </div>
          <div class="col-md-6">
            <table class="table table-striped">
              <tr>
                <td><strong>{{ text_generated_on }}:</strong></td>
                <td>{{ aging_data.generated_at }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_generated_by }}:</strong></td>
                <td>{{ aging_data.generated_by_name }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_aging_periods }}:</strong></td>
                <td>{{ aging_data.filter_data.aging_periods }}</td>
              </tr>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- ملخص التقرير -->
    <div class="panel panel-success">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-bar-chart"></i> {{ text_summary }}</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-md-2">
            <div class="info-box bg-blue">
              <span class="info-box-icon"><i class="fa fa-clock-o"></i></span>
              <div class="info-box-content">
                <span class="info-box-text">{{ text_period_current }}</span>
                <span class="info-box-number">{{ aging_data.summary.current }}</span>
              </div>
            </div>
          </div>
          <div class="col-md-2">
            <div class="info-box bg-green">
              <span class="info-box-icon"><i class="fa fa-calendar"></i></span>
              <div class="info-box-content">
                <span class="info-box-text">{{ text_period_1_30 }}</span>
                <span class="info-box-number">{{ aging_data.summary.period_1_30 }}</span>
              </div>
            </div>
          </div>
          <div class="col-md-2">
            <div class="info-box bg-yellow">
              <span class="info-box-icon"><i class="fa fa-exclamation-triangle"></i></span>
              <div class="info-box-content">
                <span class="info-box-text">{{ text_period_31_60 }}</span>
                <span class="info-box-number">{{ aging_data.summary.period_31_60 }}</span>
              </div>
            </div>
          </div>
          <div class="col-md-2">
            <div class="info-box bg-orange">
              <span class="info-box-icon"><i class="fa fa-warning"></i></span>
              <div class="info-box-content">
                <span class="info-box-text">{{ text_period_61_90 }}</span>
                <span class="info-box-number">{{ aging_data.summary.period_61_90 }}</span>
              </div>
            </div>
          </div>
          <div class="col-md-2">
            <div class="info-box bg-red">
              <span class="info-box-icon"><i class="fa fa-times-circle"></i></span>
              <div class="info-box-content">
                <span class="info-box-text">{{ text_period_over_90 }}</span>
                <span class="info-box-number">{{ aging_data.summary.period_over_90 }}</span>
              </div>
            </div>
          </div>
          <div class="col-md-2">
            <div class="info-box bg-purple">
              <span class="info-box-icon"><i class="fa fa-calculator"></i></span>
              <div class="info-box-content">
                <span class="info-box-text">{{ text_total_amount }}</span>
                <span class="info-box-number">{{ aging_data.summary.total }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- تحليل المخاطر بالذكاء الاصطناعي -->
    {% if risk_analysis %}
    <div class="panel panel-warning">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-shield"></i> {{ text_ai_analysis }}</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-md-3">
            <div class="info-box bg-red">
              <span class="info-box-icon"><i class="fa fa-exclamation-circle"></i></span>
              <div class="info-box-content">
                <span class="info-box-text">{{ text_risk_critical }}</span>
                <span class="info-box-number">{{ risk_analysis.critical_risk_entities|length }}</span>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="info-box bg-yellow">
              <span class="info-box-icon"><i class="fa fa-warning"></i></span>
              <div class="info-box-content">
                <span class="info-box-text">{{ text_risk_high }}</span>
                <span class="info-box-number">{{ risk_analysis.high_risk_entities|length }}</span>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="info-box bg-blue">
              <span class="info-box-icon"><i class="fa fa-info-circle"></i></span>
              <div class="info-box-content">
                <span class="info-box-text">{{ text_risk_medium }}</span>
                <span class="info-box-number">{{ risk_analysis.medium_risk_entities|length }}</span>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="info-box bg-green">
              <span class="info-box-icon"><i class="fa fa-check-circle"></i></span>
              <div class="info-box-content">
                <span class="info-box-text">{{ text_risk_low }}</span>
                <span class="info-box-number">{{ risk_analysis.low_risk_entities|length }}</span>
              </div>
            </div>
          </div>
        </div>
        
        <div class="row">
          <div class="col-md-6">
            <h4>{{ text_overall_risk }}</h4>
            <div class="progress">
              <div class="progress-bar progress-bar-{% if risk_analysis.overall_risk_score >= 80 %}danger{% elseif risk_analysis.overall_risk_score >= 60 %}warning{% elseif risk_analysis.overall_risk_score >= 40 %}info{% else %}success{% endif %}" 
                   style="width: {{ risk_analysis.overall_risk_score }}%">
                {{ risk_analysis.overall_risk_score }}%
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <h4>{{ text_recommendations }}</h4>
            <ul>
              {% for recommendation in risk_analysis.recommendations %}
              <li>{{ recommendation }}</li>
              {% endfor %}
            </ul>
          </div>
        </div>
      </div>
    </div>
    {% endif %}

    <!-- رسم بياني للتوزيع -->
    {% if aging_data.summary %}
    <div class="panel panel-info">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pie-chart"></i> {{ text_aging_chart }}</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-md-8">
            <canvas id="aging-chart" width="400" height="200"></canvas>
          </div>
          <div class="col-md-4">
            <table class="table table-striped">
              <thead>
                <tr>
                  <th>{{ text_period }}</th>
                  <th class="text-right">{{ text_amount }}</th>
                  <th class="text-right">{{ text_percentage }}</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>{{ text_period_current }}</td>
                  <td class="text-right">{{ aging_data.summary.current }}</td>
                  <td class="text-right">{{ aging_data.summary.current_percentage }}%</td>
                </tr>
                <tr>
                  <td>{{ text_period_1_30 }}</td>
                  <td class="text-right">{{ aging_data.summary.period_1_30 }}</td>
                  <td class="text-right">{{ aging_data.summary.period_1_30_percentage }}%</td>
                </tr>
                <tr>
                  <td>{{ text_period_31_60 }}</td>
                  <td class="text-right">{{ aging_data.summary.period_31_60 }}</td>
                  <td class="text-right">{{ aging_data.summary.period_31_60_percentage }}%</td>
                </tr>
                <tr>
                  <td>{{ text_period_61_90 }}</td>
                  <td class="text-right">{{ aging_data.summary.period_61_90 }}</td>
                  <td class="text-right">{{ aging_data.summary.period_61_90_percentage }}%</td>
                </tr>
                <tr>
                  <td>{{ text_period_over_90 }}</td>
                  <td class="text-right">{{ aging_data.summary.period_over_90 }}</td>
                  <td class="text-right">{{ aging_data.summary.period_over_90_percentage }}%</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
    {% endif %}

    <!-- تفاصيل التقرير -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-list"></i> {{ text_detailed_summary }}</h3>
      </div>
      <div class="panel-body">
        <div class="table-responsive">
          <table class="table table-bordered table-hover" id="aging-table">
            <thead>
              <tr>
                <th>{{ column_entity }}</th>
                <th class="text-right">{{ column_current }}</th>
                <th class="text-right">{{ column_1_30 }}</th>
                <th class="text-right">{{ column_31_60 }}</th>
                <th class="text-right">{{ column_61_90 }}</th>
                <th class="text-right">{{ column_over_90 }}</th>
                <th class="text-right">{{ column_total }}</th>
                <th class="text-center">{{ column_risk_score }}</th>
                <th class="text-center">{{ text_status }}</th>
              </tr>
            </thead>
            <tbody>
              {% for entity in aging_data.details %}
              <tr>
                <td>
                  <strong>{{ entity.name }}</strong><br>
                  <small class="text-muted">{{ entity.code }}</small>
                </td>
                <td class="text-right">{{ entity.current }}</td>
                <td class="text-right{% if entity.period_1_30 > 0 %} text-warning{% endif %}">{{ entity.period_1_30 }}</td>
                <td class="text-right{% if entity.period_31_60 > 0 %} text-warning{% endif %}">{{ entity.period_31_60 }}</td>
                <td class="text-right{% if entity.period_61_90 > 0 %} text-danger{% endif %}">{{ entity.period_61_90 }}</td>
                <td class="text-right{% if entity.period_over_90 > 0 %} text-danger{% endif %}">{{ entity.period_over_90 }}</td>
                <td class="text-right"><strong>{{ entity.total }}</strong></td>
                <td class="text-center">
                  <span class="badge badge-{% if entity.risk_score >= 80 %}danger{% elseif entity.risk_score >= 60 %}warning{% elseif entity.risk_score >= 40 %}info{% else %}success{% endif %}">
                    {{ entity.risk_score }}
                  </span>
                </td>
                <td class="text-center">
                  <span class="label label-{% if entity.status == 'critical' %}danger{% elseif entity.status == 'overdue' %}warning{% elseif entity.status == 'current' %}success{% else %}default{% endif %}">
                    {{ entity.status_name }}
                  </span>
                </td>
              </tr>
              {% endfor %}
            </tbody>
            <tfoot>
              <tr class="info">
                <th>{{ text_total }}</th>
                <th class="text-right">{{ aging_data.summary.current }}</th>
                <th class="text-right">{{ aging_data.summary.period_1_30 }}</th>
                <th class="text-right">{{ aging_data.summary.period_31_60 }}</th>
                <th class="text-right">{{ aging_data.summary.period_61_90 }}</th>
                <th class="text-right">{{ aging_data.summary.period_over_90 }}</th>
                <th class="text-right">{{ aging_data.summary.total }}</th>
                <th class="text-center">-</th>
                <th class="text-center">-</th>
              </tr>
            </tfoot>
          </table>
        </div>
      </div>
    </div>

    <!-- معلومات الامتثال -->
    <div class="panel panel-success">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-check-circle"></i> {{ text_compliance_info }}</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-md-3">
            <div class="alert alert-success">
              <i class="fa fa-check"></i> {{ text_eas_compliant }}
            </div>
          </div>
          <div class="col-md-3">
            <div class="alert alert-info">
              <i class="fa fa-globe"></i> {{ text_eta_ready }}
            </div>
          </div>
          <div class="col-md-3">
            <div class="alert alert-warning">
              <i class="fa fa-balance-scale"></i> {{ text_egyptian_gaap }}
            </div>
          </div>
          <div class="col-md-3">
            <div class="alert alert-primary">
              <i class="fa fa-shield"></i> {{ text_risk_management }}
            </div>
          </div>
        </div>
      </div>
    </div>

  </div>
</div>

<script type="text/javascript"><!--
// DataTables initialization
$('#aging-table').DataTable({
    "language": {
        "url": "{{ datatable_language }}"
    },
    "order": [[ 6, "desc" ]],
    "pageLength": 25,
    "dom": 'Bfrtip',
    "buttons": [
        'copy', 'csv', 'excel', 'pdf', 'print'
    ]
});

// Aging Chart
{% if aging_data.summary %}
var ctx = document.getElementById('aging-chart').getContext('2d');
var agingChart = new Chart(ctx, {
    type: 'doughnut',
    data: {
        labels: ['{{ text_period_current }}', '{{ text_period_1_30 }}', '{{ text_period_31_60 }}', '{{ text_period_61_90 }}', '{{ text_period_over_90 }}'],
        datasets: [{
            data: [
                {{ aging_data.summary.current_value }},
                {{ aging_data.summary.period_1_30_value }},
                {{ aging_data.summary.period_31_60_value }},
                {{ aging_data.summary.period_61_90_value }},
                {{ aging_data.summary.period_over_90_value }}
            ],
            backgroundColor: [
                '#3c8dbc',
                '#00a65a',
                '#f39c12',
                '#ff851b',
                '#dd4b39'
            ]
        }]
    },
    options: {
        responsive: true,
        legend: {
            position: 'bottom'
        }
    }
});
{% endif %}

// Tooltips
$('[data-toggle="tooltip"]').tooltip();
//--></script>

{{ footer }}
