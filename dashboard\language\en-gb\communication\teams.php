<?php
/**
 * English Language File - Teams Management
 * 
 * @package    AYM ERP
 * <AUTHOR> Development Team
 * @copyright  2025 AYM ERP Systems
 * @license    Commercial License
 * @version    1.0.0
 * @since      2025-07-15
 */

// Main heading
$_['heading_title'] = 'Teams Management';

// General texts
$_['text_success'] = 'Success: You have modified teams!';
$_['text_list'] = 'Team List';
$_['text_add'] = 'Add Team';
$_['text_edit'] = 'Edit Team';
$_['text_view'] = 'View Team';
$_['text_delete'] = 'Delete';
$_['text_confirm'] = 'Are you sure?';
$_['text_no_results'] = 'No teams found!';
$_['text_loading'] = 'Loading...';

// Team types
$_['text_type_department'] = 'Department Team';
$_['text_type_project'] = 'Project Team';
$_['text_type_task_force'] = 'Task Force';
$_['text_type_committee'] = 'Committee';
$_['text_type_cross_functional'] = 'Cross-Functional Team';
$_['text_type_temporary'] = 'Temporary Team';
$_['text_type_permanent'] = 'Permanent Team';

// Team status
$_['text_status_active'] = 'Active';
$_['text_status_inactive'] = 'Inactive';
$_['text_status_suspended'] = 'Suspended';
$_['text_status_completed'] = 'Completed';
$_['text_status_archived'] = 'Archived';

// Member roles
$_['text_role_leader'] = 'Team Leader';
$_['text_role_co_leader'] = 'Co-Leader';
$_['text_role_member'] = 'Member';
$_['text_role_coordinator'] = 'Coordinator';
$_['text_role_specialist'] = 'Specialist';
$_['text_role_consultant'] = 'Consultant';
$_['text_role_observer'] = 'Observer';

// Entry fields
$_['entry_name'] = 'Team Name';
$_['entry_description'] = 'Description';
$_['entry_type'] = 'Team Type';
$_['entry_status'] = 'Status';
$_['entry_leader'] = 'Team Leader';
$_['entry_department'] = 'Department';
$_['entry_project'] = 'Project';
$_['entry_start_date'] = 'Start Date';
$_['entry_end_date'] = 'End Date';
$_['entry_objectives'] = 'Objectives';
$_['entry_budget'] = 'Budget';

// Columns
$_['column_name'] = 'Team Name';
$_['column_type'] = 'Type';
$_['column_leader'] = 'Leader';
$_['column_members'] = 'Members';
$_['column_department'] = 'Department';
$_['column_status'] = 'Status';
$_['column_created'] = 'Created';
$_['column_action'] = 'Action';

// Buttons
$_['button_add'] = 'Add Team';
$_['button_edit'] = 'Edit';
$_['button_delete'] = 'Delete';
$_['button_view'] = 'View';
$_['button_add_member'] = 'Add Member';
$_['button_remove_member'] = 'Remove Member';
$_['button_assign_task'] = 'Assign Task';
$_['button_view_tasks'] = 'View Tasks';
$_['button_team_chat'] = 'Team Chat';
$_['button_team_files'] = 'Team Files';
$_['button_team_calendar'] = 'Team Calendar';

// Member management
$_['text_members'] = 'Team Members';
$_['text_add_members'] = 'Add Members';
$_['text_remove_member'] = 'Remove Member';
$_['text_change_role'] = 'Change Role';
$_['text_member_since'] = 'Member Since';
$_['text_member_role'] = 'Member Role';
$_['text_member_status'] = 'Member Status';
$_['text_active_members'] = 'Active Members';
$_['text_inactive_members'] = 'Inactive Members';
$_['text_total_members'] = 'Total Members';

// Tasks and projects
$_['text_tasks'] = 'Tasks';
$_['text_projects'] = 'Projects';
$_['text_assign_task'] = 'Assign Task';
$_['text_task_progress'] = 'Task Progress';
$_['text_completed_tasks'] = 'Completed Tasks';
$_['text_pending_tasks'] = 'Pending Tasks';
$_['text_overdue_tasks'] = 'Overdue Tasks';

// Meetings
$_['text_meetings'] = 'Meetings';
$_['text_schedule_meeting'] = 'Schedule Meeting';
$_['text_meeting_history'] = 'Meeting History';
$_['text_next_meeting'] = 'Next Meeting';
$_['text_meeting_minutes'] = 'Meeting Minutes';
$_['text_meeting_agenda'] = 'Meeting Agenda';

// Communication
$_['text_communication'] = 'Communication';
$_['text_team_chat'] = 'Team Chat';
$_['text_announcements'] = 'Announcements';
$_['text_notifications'] = 'Notifications';
$_['text_team_updates'] = 'Team Updates';
$_['text_discussion_board'] = 'Discussion Board';

// Files and documents
$_['text_files'] = 'Files';
$_['text_documents'] = 'Documents';
$_['text_shared_files'] = 'Shared Files';
$_['text_upload_file'] = 'Upload File';
$_['text_file_library'] = 'File Library';
$_['text_document_templates'] = 'Document Templates';

// Performance and statistics
$_['text_performance'] = 'Performance';
$_['text_statistics'] = 'Statistics';
$_['text_team_performance'] = 'Team Performance';
$_['text_productivity'] = 'Productivity';
$_['text_efficiency'] = 'Efficiency';
$_['text_kpi'] = 'Key Performance Indicators';
$_['text_performance_report'] = 'Performance Report';

// Calendar and scheduling
$_['text_calendar'] = 'Calendar';
$_['text_schedule'] = 'Schedule';
$_['text_team_calendar'] = 'Team Calendar';
$_['text_events'] = 'Events';
$_['text_deadlines'] = 'Deadlines';
$_['text_milestones'] = 'Milestones';

// Settings
$_['text_settings'] = 'Team Settings';
$_['text_team_settings'] = 'Team Settings';
$_['text_permissions'] = 'Permissions';
$_['text_access_control'] = 'Access Control';
$_['text_privacy_settings'] = 'Privacy Settings';
$_['text_notification_settings'] = 'Notification Settings';

// Search and filter
$_['text_search'] = 'Search';
$_['text_search_teams'] = 'Search Teams';
$_['text_filter'] = 'Filter';
$_['text_filter_by_type'] = 'Filter by Type';
$_['text_filter_by_status'] = 'Filter by Status';
$_['text_filter_by_department'] = 'Filter by Department';
$_['text_filter_by_leader'] = 'Filter by Leader';

// Reports
$_['text_reports'] = 'Reports';
$_['text_team_reports'] = 'Team Reports';
$_['text_activity_report'] = 'Activity Report';
$_['text_progress_report'] = 'Progress Report';
$_['text_member_report'] = 'Member Report';
$_['text_export_report'] = 'Export Report';

// Goals and milestones
$_['text_goals'] = 'Goals';
$_['text_objectives'] = 'Objectives';
$_['text_milestones'] = 'Milestones';
$_['text_achievements'] = 'Achievements';
$_['text_goal_progress'] = 'Goal Progress';
$_['text_set_goal'] = 'Set Goal';

// Collaboration
$_['text_collaboration'] = 'Collaboration';
$_['text_teamwork'] = 'Teamwork';
$_['text_collaboration_tools'] = 'Collaboration Tools';
$_['text_shared_workspace'] = 'Shared Workspace';
$_['text_knowledge_sharing'] = 'Knowledge Sharing';

// Error messages
$_['error_permission'] = 'Warning: You do not have permission to access teams management!';
$_['error_name'] = 'Team name must be between 3 and 255 characters!';
$_['error_leader'] = 'Team leader must be selected!';
$_['error_type'] = 'Team type must be selected!';
$_['error_department'] = 'Department must be selected!';
$_['error_member_exists'] = 'Member already exists in the team!';
$_['error_member_not_found'] = 'Member not found!';
$_['error_team_not_found'] = 'Team not found!';
$_['error_cannot_remove_leader'] = 'Cannot remove team leader!';

// Confirmation messages
$_['text_confirm_delete'] = 'Are you sure you want to delete this team?';
$_['text_confirm_remove_member'] = 'Are you sure you want to remove this member?';
$_['text_confirm_archive'] = 'Are you sure you want to archive this team?';
$_['text_confirm_deactivate'] = 'Are you sure you want to deactivate this team?';

// Help and tips
$_['help_name'] = 'Enter a clear and distinctive name for the team';
$_['help_description'] = 'Brief description of team objectives and tasks';
$_['help_type'] = 'Choose the appropriate team type';
$_['help_leader'] = 'Select the responsible team leader';
$_['help_members'] = 'Add team members and define their roles';

// Alerts
$_['alert_team_created'] = 'Team created successfully';
$_['alert_team_updated'] = 'Team updated successfully';
$_['alert_team_deleted'] = 'Team deleted';
$_['alert_member_added'] = 'Member added to team';
$_['alert_member_removed'] = 'Member removed from team';
$_['alert_role_changed'] = 'Member role changed';

// Dates and times
$_['text_created_at'] = 'Created At';
$_['text_updated_at'] = 'Updated At';
$_['text_start_date'] = 'Start Date';
$_['text_end_date'] = 'End Date';
$_['text_duration'] = 'Duration';

// Advanced states
$_['text_team_size'] = 'Team Size';
$_['text_optimal_size'] = 'Optimal Size';
$_['text_team_composition'] = 'Team Composition';
$_['text_skill_matrix'] = 'Skill Matrix';
$_['text_team_dynamics'] = 'Team Dynamics';

// Integrations
$_['text_integrations'] = 'Integrations';
$_['text_project_integration'] = 'Project Integration';
$_['text_hr_integration'] = 'HR Integration';
$_['text_calendar_integration'] = 'Calendar Integration';
$_['text_communication_integration'] = 'Communication Integration';

// Security and privacy
$_['text_security'] = 'Security';
$_['text_privacy'] = 'Privacy';
$_['text_access_levels'] = 'Access Levels';
$_['text_confidential'] = 'Confidential';
$_['text_restricted'] = 'Restricted';
$_['text_public'] = 'Public';

// Training and development
$_['text_training'] = 'Training';
$_['text_development'] = 'Development';
$_['text_skill_development'] = 'Skill Development';
$_['text_team_building'] = 'Team Building';
$_['text_training_programs'] = 'Training Programs';

// Review and evaluation
$_['text_review'] = 'Review';
$_['text_evaluation'] = 'Evaluation';
$_['text_team_review'] = 'Team Review';
$_['text_performance_evaluation'] = 'Performance Evaluation';
$_['text_feedback'] = 'Feedback';
$_['text_improvement_areas'] = 'Improvement Areas';

// Approval notification messages
$_['text_new_approval_request_title'] = 'New approval request: ';
$_['text_you_have_new_approval_request'] = 'You have a new approval request that requires your review';
$_['text_approval_request_title'] = 'Approval request: ';
$_['text_conversation_for_approval_request'] = 'Conversation for approval request number ';
$_['text_new_approval_request_created'] = 'A new approval request has been created. Please review the details and respond with approval or rejection.';
$_['text_update_on_approval_request'] = 'Update on approval request: ';
$_['text_approved'] = 'approved';
$_['text_rejected'] = 'rejected';
$_['text_your_request'] = ' your request';
$_['text_approval_request_waiting'] = 'Approval request waiting for you: ';
$_['text_approval_request_forwarded'] = 'An approval request has been forwarded to you for review';
$_['text_action_completed'] = 'Completed ';
$_['text_approval_request_number'] = ' approval request number ';
?>
