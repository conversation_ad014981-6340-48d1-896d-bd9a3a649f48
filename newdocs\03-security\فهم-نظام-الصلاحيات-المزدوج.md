# فهم شامل لنظام الصلاحيات المزدوج في AYM ERP

## نظرة عامة

اكتشفت أن النظام يستخدم **نظام صلاحيات مزدوج** متطور يجمع بين نظامين مختلفين:

### 1. النظام الأول: `hasPermission($key, $value)` - النظام التقليدي لـ OpenCart

```php
public function hasPermission($key, $value) {
    return isset($this->permission[$key]) && in_array($value, $this->permission[$key]);
}
```

**كيف يعمل:**
- يعتمد على جدول `cod_user_group` وحقل `permission` (JSON)
- يحمل الصلاحيات من مجموعة المستخدم عند تسجيل الدخول
- يخزن الصلاحيات في مصفوفة `$this->permission`
- يستخدم في `startup/permission.php` للتحقق من الوصول للصفحات

**مثال الاستخدام:**
```php
// في startup/permission.php
if (!$this->user->hasPermission('access', $route)) {
    return new Action('error/permission');
}

// في الكونترولرز
if (!$this->user->hasPermission('modify', 'user/permission')) {
    $json['error'] = $this->language->get('error_permission');
}
```

### 2. النظام الثاني: `hasKey($key)` - النظام المتقدم المخصص

```php
public function hasKey($key) {
    $user_group_id = $this->getGroupId();
    if ($user_group_id == '1') return true; // المجموعة 1 لهم كل الصلاحيات دوما لانهم ادارة الشركة 
  
    $query = $this->db->query("SELECT permission_id FROM " . DB_PREFIX . "permission WHERE `key`='".$this->db->escape($key)."'");
    if (!$query->num_rows) return false;
    $permission_id = (int)$query->row['permission_id'];

    $check_group = $this->db->query("SELECT * FROM " . DB_PREFIX . "user_group_permission WHERE user_group_id='".(int)$user_group_id."' AND permission_id='".(int)$permission_id."'");
    if ($check_group->num_rows) return true;

    $user_id = $this->getId();
    $check_user = $this->db->query("SELECT * FROM " . DB_PREFIX . "user_permission WHERE user_id='".(int)$user_id."' AND permission_id='".(int)$permission_id."'");
    return $check_user->num_rows > 0;
}
```

**كيف يعمل:**
- يعتمد على جداول متخصصة: `cod_permission`, `cod_user_group_permission`, `cod_user_permission`
- يدعم **الصلاحيات الفردية** للمستخدمين (بجانب صلاحيات المجموعة)
- يدعم **الوراثة والتدرج** في الصلاحيات
- **المجموعة 1 (الإدارة) لها صلاحيات كاملة تلقائياً**
- يتحقق من الصلاحية في الوقت الفعلي من قاعدة البيانات

## الجداول المرتبطة بنظام الصلاحيات

### 1. جدول `cod_permission` - تعريف الصلاحيات
```sql
- permission_id (PK)
- name (اسم الصلاحية)
- key (مفتاح فريد للصلاحية)
- type (نوع الصلاحية)
- date_added, date_modified
```

### 2. جدول `cod_user_group_permission` - صلاحيات المجموعات
```sql
- user_group_id (FK)
- permission_id (FK)
```

### 3. جدول `cod_user_permission` - صلاحيات المستخدمين الفردية
```sql
- user_id (FK)
- permission_id (FK)
```

### 4. جدول `cod_user_group` - مجموعات المستخدمين (النظام التقليدي)
```sql
- user_group_id (PK)
- name
- permission (JSON) - للنظام التقليدي
```

## الفروق الجوهرية بين النظامين

| الخاصية | hasPermission | hasKey |
|---------|---------------|---------|
| **المرونة** | محدود بصلاحيات المجموعة | يدعم الصلاحيات الفردية |
| **التحديث** | يتطلب إعادة تسجيل دخول | فوري من قاعدة البيانات |
| **التعقيد** | بسيط (JSON) | متقدم (جداول متعددة) |
| **الأداء** | سريع (ذاكرة) | أبطأ (استعلام قاعدة بيانات) |
| **التحكم** | على مستوى المجموعة | على مستوى المستخدم والمجموعة |
| **الوراثة** | لا يدعم | يدعم الوراثة المتدرجة |

## أمثلة عملية للاستخدام

### استخدام hasPermission (النظام التقليدي)
```php
// للتحقق من الوصول لصفحة
if (!$this->user->hasPermission('access', 'sale/order')) {
    // منع الوصول
}

// للتحقق من صلاحية التعديل
if (!$this->user->hasPermission('modify', 'catalog/product')) {
    // منع التعديل
}
```

### استخدام hasKey (النظام المتقدم)
```php
// للتحقق من صلاحية متقدمة
if (!$this->user->hasKey('advanced_reporting')) {
    // منع الوصول للتقارير المتقدمة
}

// للتحقق من صلاحية مخصصة
if (!$this->user->hasKey('delete_financial_records')) {
    // منع حذف السجلات المالية
}

// للتحقق من صلاحية معقدة
if (!$this->user->hasKey('approve_purchase_orders_above_10000')) {
    // منع الموافقة على طلبات الشراء الكبيرة
}
```

## إدارة الصلاحيات المتقدمة

### إضافة صلاحية جديدة
```php
// في dashboard/controller/user/permission.php
public function addPermissionAjax() {
    $permission_id = $this->model_user_permission->addPermission($this->request->post);
    
    // ربط بمجموعات المستخدمين
    if (isset($this->request->post['user_group_ids'])) {
        $this->model_user_permission->setUserGroupPermissions($permission_id, $this->request->post['user_group_ids']);
    }
    
    // ربط بمستخدمين محددين
    if (isset($this->request->post['user_ids'])) {
        $this->model_user_permission->setUserPermissions($permission_id, $this->request->post['user_ids']);
    }
}
```

### توليد مفتاح فريد للصلاحية
```php
private function generateUniqueKey($key, $permission_id) {
    // تنظيف المفتاح وتحويله إلى حروف صغيرة
    $key = strtolower(preg_replace('/[^a-zA-Z0-9_]+/', '_', $key));
    
    // التأكد من أن المفتاح لا يبدأ برقم
    if (is_numeric(substr($key, 0, 1))) {
        $key = 'p_' . $key;
    }
    
    // محاولة إيجاد مفتاح فريد بشكل آمن
    // ... منطق التحقق من التفرد
    
    return $key;
}
```

## التكامل مع النظام الأمني

### في startup/login.php
```php
// تسجيل User في Registry
$this->registry->set('user', new Cart\User($this->registry));

// التحقق من تسجيل الدخول
if (!$this->user->isLogged() && !in_array($route, $ignore)) {
    return new Action('common/login');
}
```

### في startup/permission.php
```php
// التحقق من الصلاحيات لكل صفحة
if (!in_array($route, $ignore) && !$this->user->hasPermission('access', $route)) {
    return new Action('error/permission');
}
```

## الميزات المتقدمة المكتشفة

### 1. المجموعة الإدارية الخاصة
```php
if ($user_group_id == '1') return true; // المجموعة 1 لهم كل الصلاحيات دوما
```

### 2. نظام التدقيق والسجلات
```php
public function logActivity($action_type, $module, $description, $reference_type = null, $reference_id = null) {
    // تسجيل جميع الأنشطة للتدقيق
}
```

### 3. إدارة الجلسات المتقدمة
```php
private function updateLastActivity() {
    // تحديث وقت آخر نشاط كل 5 دقائق
}
```

### 4. نظام الإشعارات المتكامل
```php
public function getUnreadNotificationsCount($refresh = false) {
    // عدد الإشعارات غير المقروءة
}
```

## التوصيات للتطوير

### 1. استخدام النظام المناسب
- **hasPermission**: للصلاحيات الأساسية والصفحات العامة
- **hasKey**: للصلاحيات المتقدمة والعمليات الحساسة

### 2. أفضل الممارسات
```php
// دمج النظامين للحصول على أقصى مرونة
if ($this->user->hasPermission('access', 'financial/reports') && 
    $this->user->hasKey('view_sensitive_financial_data')) {
    // السماح بالوصول للتقارير المالية الحساسة
}
```

### 3. تحسين الأداء
```php
// تخزين مؤقت للصلاحيات المتكررة
private $permission_cache = [];

public function hasKeyCached($key) {
    if (!isset($this->permission_cache[$key])) {
        $this->permission_cache[$key] = $this->hasKey($key);
    }
    return $this->permission_cache[$key];
}
```

## الخلاصة

النظام يستخدم **نهج هجين ذكي** يجمع بين:
- **البساطة والسرعة** (hasPermission)
- **المرونة والتحكم الدقيق** (hasKey)

هذا التصميم يوفر:
- ✅ **مرونة عالية** في إدارة الصلاحيات
- ✅ **أمان متقدم** مع التدقيق الشامل
- ✅ **قابلية التوسع** لإضافة صلاحيات جديدة
- ✅ **التوافق** مع النظام الأساسي لـ OpenCart
- ✅ **الأداء المتوازن** بين السرعة والمرونة

هذا الفهم العميق سيساعد في تطوير الميزات المتقدمة وضمان الأمان في جميع أجزاء النظام.