<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
  <meta charset="UTF-8" />
  <title>{{ heading_title }}</title>
  <style type="text/css">
    body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; direction: rtl; }
    .header { text-align: center; margin-bottom: 20px; }
    .company-info { margin-bottom: 30px; }
    .report-info { margin-bottom: 20px; }
    .filters { margin-bottom: 20px; }
    table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
    th { background-color: #f5f5f5; }
    .text-end { text-align: left; }
    .total-row { font-weight: bold; background-color: #f9f9f9; }
    @media print {
      .no-print { display: none; }
      body { padding: 0; margin: 0; }
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>{{ heading_title }}</h1>
    <div class="company-info">
      <h3>{{ company_name }}</h3>
      <p>{{ company_address }}</p>
    </div>
  </div>

  <div class="report-info">
    <p>تاريخ التقرير: {{ date_generated }}</p>
    {% if branch_name %}
      <p>الفرع: {{ branch_name }}</p>
    {% endif %}
  </div>

  <div class="filters">
    <strong>معايير التصفية:</strong>
    <ul>
      {% if filter_branch %}<li>الفرع: {{ filter_branch }}</li>{% endif %}
      {% if filter_consignment %}<li>نوع البضاعة: {{ filter_consignment }}</li>{% endif %}
    </ul>
  </div>

  <table>
    <thead>
      <tr>
        <th>{{ column_branch }}</th>
        <th>{{ column_product }}</th>
        <th>{{ column_unit }}</th>
        <th class="text-end">{{ column_quantity }}</th>
        <th class="text-end">{{ column_average_cost }}</th>
        <th class="text-end">{{ column_total_value }}</th>
        <th class="text-end">{{ column_last_movement }}</th>
      </tr>
    </thead>
    <tbody>
      {% for item in inventory %}
        <tr>
          <td>{{ item.branch_name }}</td>
          <td>{{ item.product_name }}</td>
          <td>{{ item.unit_name }}</td>
          <td class="text-end">{{ item.quantity }}</td>
          <td class="text-end">{{ item.average_cost }}</td>
          <td class="text-end">{{ item.total_value }}</td>
        </tr>
      {% endfor %}
    </tbody>
    <tfoot>
      <tr class="total-row">
        <td colspan="3">الإجمالي</td>
        <td class="text-end">{{ total_quantity }}</td>
        <td class="text-end">-</td>
        <td class="text-end">{{ total_value }}</td>
      </tr>
    </tfoot>
  </table>

  <div class="no-print">
    <button onclick="window.print()">طباعة</button>
    <button onclick="window.close()">إغلاق</button>
  </div>

  <script type="text/javascript">
    window.onload = function() {
      if (window.opener && window.opener.document) {
        document.title = '{{ heading_title }} - ' + new Date().toLocaleDateString('ar-EG');
      }
    };
  </script>
</body>
</html>