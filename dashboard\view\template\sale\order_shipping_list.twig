<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
  <meta charset="UTF-8"/>
  <title>{{ title }}</title>
  <base href="{{ base }}"/>
  <link href="view/javascript/font-awesome/css/font-awesome.min.css" type="text/css" rel="stylesheet" />
  <script type="text/javascript" src="view/javascript/jquery/jquery-2.1.1.min.js"></script>
  <style type="text/css" media="print">
    .watermarked {
      position: relative;
    }
    body { font-size: 16px; padding-right: 10px; padding-left: 10px; }
    .watermarked:after {
      content: "";
      display: block;
      width: 100%;
      height: 100%;
      position: absolute;
      top: 250px;
      left: 0px;
      background-size: 100% 100%;
      background-position: 100% 100%;
      background-repeat: no-repeat;
      opacity: 0.08;
    }
    div.page {
      page-break-after: always;
      page-break-inside: avoid;
    }
    .bigmain {
      page-break-after: always;
      page-break-inside: avoid;
      margin: 0 auto;
    }
    .col-sm-4 { width: 33.3%; }
    .col-sm-3 { width: 25%; }
    .col-sm-5 { width: 44.7%; }
    .col-sm-7 { width: 55.3%; }
    .table thead tr td { background-color: #eee; text-align: center; }
    .table td, .table th {
      font-weight: 600;
    }
    @media print {
      .table thead tr td, .table tbody tr td {
        border-width: 1px !important;
        border-style: solid !important;
        border-color: black !important;
        font-size: 12px !important;
        background-color: #fff;
        padding: 4px !important; /* تعديل لتقليل حجم الخلايا */
        -webkit-print-color-adjust: exact;
      }
      .table thead tr td { background-color: #eee; text-align: center; }
      .table td, .table th {
        font-weight: 600;
        text-align: center;
        white-space: normal !important; /* جعل النص يلتف داخل الخلية */
        padding-right: 4px;
        padding-left: 4px;
      }
      .table-bordered th, .table-bordered td {
        border: 1px solid #000 !important;
      }
    }
    .table-bordered th, .table-bordered td {
      border: 1px solid #000 !important;
    }
  </style>
</head>
<body>
<div class="container">
  <div class="bigmain watermarked" style="width:100%;margin: 0 auto;page-break-after: always;position: relative;">
    <div style="display:table;width: 100%;padding-left:1%;padding-right:1%;padding-top: 60px;">
      <img height="50" style="position: absolute;top: 5px;left:5px;" src="https://erp.codaym.com/image/cache/catalog/dlogo-45x45.png" />  
      <h3 style="line-height: 30px;height: 80px;top: -20px;display: flex;position: absolute;right:5px;font-size:15px">
        قائمة تسليم الطلبات </br>
        طبعت في {{ "now"|date("d-m-Y h:i") }} 
      </h3>
      <div style="border-top: 1px solid #eee;" class="table-responsive">
        <table style="width:100%" class="table table-bordered table-hover">
          <thead>
            <tr>
              <td class="text-end">رقم الطلب</td>
              <td class="text-end">منتجات</td>
              <td class="text-end">كمية</td>
              <td class="text-start">العميل</td>
              <td class="text-start">جوال</td>
              <td class="text-end d-none d-lg-table-cell">قيمة الطلب</td>
              <td class="text-end">العنوان / ملاحظات العميل</td>
              <td style="min-width:100px" class="text-start">حالة الطلب</td>
              <td style="min-width:100px" class="text-end">ملاحظات</td>
            </tr>
          </thead>
          <tbody>
            {% if orders %}
              {% for order in orders %}
                <tr style="min-height: 35px;height: 35px;line-height: 35px;">
                  <td class="text-end">{{ order.order_id }}</td>
                  <td class="text-end">{{ order.product_count }}</td>
                  <td class="text-end">{{ order.total_quantity }}</td>
                  <td class="text-start">{{ order.customername }}</td>
                  <td class="text-start">{{ order.telephone }}</td>
                  <td class="text-end d-none d-lg-table-cell">{{ order.total }}</td>
                  <td class="text-start" style="max-width:400px;white-space: normal;display: block;line-height: 20px;">{{ order.payment_address }} <br>
                  {% if order.comment %}
                    ملاحظات العميل : {{ order.comment }}
                  {% endif %}
                  </td>
                  <td class="text-start">{{ order.order_status }}</td>
                  <td class="text-end"></td>
                </tr>
              {% endfor %}
            {% else %}
              <tr>
                <td class="text-center" colspan="9">لا توجد نتائج</td>
              </tr>
            {% endif %}
          </tbody>
        </table>
      </div>
    </div>
  </div>  
</div>
</body>
</html>
