{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <a href="{{ back }}" data-toggle="tooltip" title="{{ button_back }}" class="btn btn-default"><i class="fa fa-reply"></i></a>
        <button type="button" id="button-filter" data-toggle="tooltip" title="{{ button_filter }}" class="btn btn-primary"><i class="fa fa-filter"></i></button>
        <a href="{{ export }}" data-toggle="tooltip" title="{{ button_export }}" class="btn btn-success"><i class="fa fa-download"></i></a>
        <button type="button" onclick="window.print();" data-toggle="tooltip" title="{{ button_print }}" class="btn btn-info"><i class="fa fa-print"></i></button>
      </div>
      <h1>{{ heading_balance_sheet }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-balance-scale"></i> {{ text_balance_sheet }}</h3>
      </div>
      <div class="panel-body">
        <div class="well" style="display: none;">
          <div class="row">
            <div class="col-sm-6">
              <div class="form-group">
                <label class="control-label" for="input-date-to">{{ entry_date_to }}</label>
                <div class="input-group date">
                  <input type="text" name="filter_date_to" value="{{ filter_date_to }}" placeholder="{{ entry_date_to }}" data-date-format="YYYY-MM-DD" id="input-date-to" class="form-control" />
                  <span class="input-group-btn">
                  <button type="button" class="btn btn-default"><i class="fa fa-calendar"></i></button>
                  </span>
                </div>
              </div>
            </div>
            <div class="col-sm-6">
              <button type="button" id="button-apply-filter" class="btn btn-primary pull-right" style="margin-top: 25px;"><i class="fa fa-search"></i> {{ button_apply }}</button>
            </div>
          </div>
        </div>
        <div class="text-center">
          <h3>{{ heading_balance_sheet }}</h3>
          <p>{{ text_as_of }}: {{ filter_date_to }}</p>
        </div>
        
        <div class="row">
          <div class="col-md-6">
            <!-- Assets Section -->
            <h4 class="text-primary">{{ text_assets }}</h4>
            <div class="table-responsive">
              <table class="table table-bordered">
                <thead>
                  <tr>
                    <th class="text-left">{{ column_account_code }}</th>
                    <th class="text-left">{{ column_account_name }}</th>
                    <th class="text-right">{{ column_amount }}</th>
                  </tr>
                </thead>
                <tbody>
                  {% if assets %}
                  {% for account in assets %}
                  <tr>
                    <td class="text-left">{{ account.code }}</td>
                    <td class="text-left">{{ account.name }}</td>
                    <td class="text-right">{{ account.balance|number_format(2, '.', ',') }}</td>
                  </tr>
                  {% endfor %}
                  {% else %}
                  <tr>
                    <td class="text-center" colspan="3">{{ text_no_results }}</td>
                  </tr>
                  {% endif %}
                  <tr class="info">
                    <td class="text-right" colspan="2"><strong>{{ text_total_assets }}</strong></td>
                    <td class="text-right"><strong>{{ total_assets|number_format(2, '.', ',') }}</strong></td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
          
          <div class="col-md-6">
            <!-- Liabilities Section -->
            <h4 class="text-danger">{{ text_liabilities }}</h4>
            <div class="table-responsive">
              <table class="table table-bordered">
                <thead>
                  <tr>
                    <th class="text-left">{{ column_account_code }}</th>
                    <th class="text-left">{{ column_account_name }}</th>
                    <th class="text-right">{{ column_amount }}</th>
                  </tr>
                </thead>
                <tbody>
                  {% if liabilities %}
                  {% for account in liabilities %}
                  <tr>
                    <td class="text-left">{{ account.code }}</td>
                    <td class="text-left">{{ account.name }}</td>
                    <td class="text-right">{{ account.balance|number_format(2, '.', ',') }}</td>
                  </tr>
                  {% endfor %}
                  {% else %}
                  <tr>
                    <td class="text-center" colspan="3">{{ text_no_results }}</td>
                  </tr>
                  {% endif %}
                  <tr class="warning">
                    <td class="text-right" colspan="2"><strong>{{ text_total_liabilities }}</strong></td>
                    <td class="text-right"><strong>{{ total_liabilities|number_format(2, '.', ',') }}</strong></td>
                  </tr>
                </tbody>
              </table>
            </div>
            
            <!-- Equity Section -->
            <h4 class="text-success">{{ text_equity }}</h4>
            <div class="table-responsive">
              <table class="table table-bordered">
                <thead>
                  <tr>
                    <th class="text-left">{{ column_account_code }}</th>
                    <th class="text-left">{{ column_account_name }}</th>
                    <th class="text-right">{{ column_amount }}</th>
                  </tr>
                </thead>
                <tbody>
                  {% if equity %}
                  {% for account in equity %}
                  <tr>
                    <td class="text-left">{{ account.code }}</td>
                    <td class="text-left">{{ account.name }}</td>
                    <td class="text-right">{{ account.balance|number_format(2, '.', ',') }}</td>
                  </tr>
                  {% endfor %}
                  {% endif %}
                  <tr>
                    <td class="text-left"></td>
                    <td class="text-left">{{ text_retained_earnings }}</td>
                    <td class="text-right">{{ retained_earnings|number_format(2, '.', ',') }}</td>
                  </tr>
                  <tr class="success">
                    <td class="text-right" colspan="2"><strong>{{ text_total_equity }}</strong></td>
                    <td class="text-right"><strong>{{ total_equity|number_format(2, '.', ',') }}</strong></td>
                  </tr>
                </tbody>
              </table>
            </div>
            
            <!-- Total Liabilities and Equity -->
            <div class="panel panel-default">
              <div class="panel-heading">
                <h4 class="panel-title">{{ text_total_liabilities_equity }}</h4>
              </div>
              <div class="panel-body">
                <h4 class="text-right">{{ total_liabilities_equity|number_format(2, '.', ',') }}</h4>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Balance Check -->
        <div class="row">
          <div class="col-md-12">
            <div class="alert {% if total_assets == total_liabilities_equity %}alert-success{% else %}alert-danger{% endif %}">
              <i class="fa {% if total_assets == total_liabilities_equity %}fa-check-circle{% else %}fa-exclamation-circle{% endif %}"></i> 
              {{ text_balance_check }}: 
              {% if total_assets == total_liabilities_equity %}
                {{ text_balanced }}
              {% else %}
                {{ text_not_balanced }} ({{ text_difference }}: {{ (total_assets - total_liabilities_equity)|number_format(2, '.', ',') }})
              {% endif %}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <script type="text/javascript"><!--
$('#button-filter').on('click', function() {
  $('.well').slideToggle();
});

$('#button-apply-filter').on('click', function() {
  var url = 'index.php?route=accounting/report/balanceSheet&user_token={{ user_token }}';
  
  var filter_date_to = $('input[name=\'filter_date_to\']').val();
  
  if (filter_date_to) {
    url += '&filter_date_to=' + encodeURIComponent(filter_date_to);
  }
  
  location = url;
});

$('.date').datetimepicker({
  pickTime: false
});
//--></script>
</div>
{{ footer }}
