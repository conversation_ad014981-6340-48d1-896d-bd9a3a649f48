<?php
namespace Braintree\Test;

/**
 * Credit card information used for testing purposes
 *
 * The constants contained in the Test\CreditCardNumbers class provide
 * credit card numbers that should be used when working in the sandbox environment.
 * The sandbox will not accept any credit card numbers other than the ones listed below.
 *
 * @package    Braintree
 * @subpackage Test
 */
class CreditCardNumbers
{
    public static $amExes = [
        '***************',
        '***************',
        '***************',
        ];
    public static $carteBlanches = ['30569309025904',];
    public static $dinersClubs   = ['38520000023237',];
    public static $discoverCards = [
        '****************',
        '****************',
        ];

    public static $hiper = '6370950000000005';
    public static $hiperCard = '6062820524845321';

    public static $elo = '5066991111111118';
    public static $eloCards = [
        '5066991111111118'
    ];

    public static $JCBs          = [
        '3530111333300000',
        '3566002020360505',
        ];

    public static $masterCard    = '****************';
    public static $masterCardInternational = '****************';
    public static $masterCards   = [
        '****************',
        '****************',
        ];

    public static $visa          = '****************';
    public static $visaInternational = '****************';
    public static $visas         = [
        '****************',
        '****************',
        '****************',
        '****************',
        ];

    public static $unknowns       = [
        '1000000000000008',
        ];

    public static $failsSandboxVerification = [
        'AmEx'       => '***************',
        'Discover'   => '****************',
        'MasterCard' => '****************',
        'Visa'       => '****************',
        ];

    public static $amexPayWithPoints = [
        'Success' => "***************",
        'IneligibleCard' => "***************",
        'InsufficientPoints' => "***************",
        ];

    public static $disputes = [
        'Chargeback' => '****************',
    ];

    public static function getAll()
    {
        return array_merge(
                self::$amExes,
                self::$discoverCards,
                self::$eloCards,
                self::$masterCards,
                self::$visas
                );
    }
}
class_alias('Braintree\Test\CreditCardNumbers', 'Braintree_Test_CreditCardNumbers');
