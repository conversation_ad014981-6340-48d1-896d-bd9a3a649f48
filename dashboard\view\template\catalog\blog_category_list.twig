{{ header }}
{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <a href="{{ back }}" data-toggle="tooltip" title="{{ button_back_to_blog }}" class="btn btn-default"><i class="fa fa-reply"></i></a>
        {% if can_add %}
        <a href="{{ add }}" data-toggle="tooltip" title="{{ button_add }}" class="btn btn-primary"><i class="fa fa-plus"></i></a>
        {% endif %}
        {% if can_delete %}
        <button type="button" data-toggle="tooltip" title="{{ button_delete }}" class="btn btn-danger" onclick="confirm('{{ text_confirm }}') ? $('#form-category').submit() : false;"><i class="fa fa-trash-o"></i></button>
        {% endif %}
      </div>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    {% if success %}
    <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-list"></i> {{ text_list }}</h3>
      </div>
      <div class="panel-body">
        <div class="well">
          <div class="row">
            <div class="col-sm-4">
              <div class="form-group">
                <label class="control-label" for="input-name">{{ entry_filter }}</label>
                <input type="text" name="filter_name" value="{{ filter_name }}" placeholder="{{ entry_filter }}" id="input-name" class="form-control" />
              </div>
            </div>
            <div class="col-sm-4">
              <div class="form-group">
                <label class="control-label" for="input-status">{{ column_status }}</label>
                <select name="filter_status" id="input-status" class="form-control">
                  <option value="">{{ text_all_statuses }}</option>
                  <option value="1"{% if filter_status == '1' %} selected="selected"{% endif %}>{{ text_enabled }}</option>
                  <option value="0"{% if filter_status == '0' %} selected="selected"{% endif %}>{{ text_disabled }}</option>
                </select>
              </div>
            </div>
            <div class="col-sm-4">
              <div class="form-group">
                <label class="control-label">&nbsp;</label>
                <div>
                  <button type="button" id="button-filter" class="btn btn-primary pull-right"><i class="fa fa-filter"></i> {{ button_filter }}</button>
                </div>
              </div>
            </div>
          </div>
        </div>
        <form action="{{ delete }}" method="post" enctype="multipart/form-data" id="form-category">
          <div class="table-responsive">
            <table class="table table-bordered table-hover">
              <thead>
                <tr>
                  <td style="width: 1px;" class="text-center"><input type="checkbox" onclick="$('input[name*=\'selected\']').prop('checked', this.checked);" /></td>
                  <td class="text-left">{% if sort == 'name' %}
                    <a href="{{ sort_name }}" class="{{ order|lower }}">{{ column_name }}</a>
                    {% else %}
                    <a href="{{ sort_name }}">{{ column_name }}</a>
                    {% endif %}</td>
                  <td class="text-left">{{ column_parent }}</td>
                  <td class="text-center">{% if sort == 'sort_order' %}
                    <a href="{{ sort_sort_order }}" class="{{ order|lower }}">{{ column_sort_order }}</a>
                    {% else %}
                    <a href="{{ sort_sort_order }}">{{ column_sort_order }}</a>
                    {% endif %}</td>
                  <td class="text-center">{{ column_status }}</td>
                  <td class="text-center">{% if sort == 'posts_count' %}
                    <a href="{{ sort_posts }}" class="{{ order|lower }}">{{ column_posts }}</a>
                    {% else %}
                    <a href="{{ sort_posts }}">{{ column_posts }}</a>
                    {% endif %}</td>
                  <td class="text-right">{{ column_action }}</td>
                </tr>
              </thead>
              <tbody>
                {% if categories %}
                {% for category in categories %}
                <tr>
                  <td class="text-center">{% if category.category_id in selected %}
                    <input type="checkbox" name="selected[]" value="{{ category.category_id }}" checked="checked" />
                    {% else %}
                    <input type="checkbox" name="selected[]" value="{{ category.category_id }}" />
                    {% endif %}</td>
                  <td class="text-left">{{ category.name }}</td>
                  <td class="text-left">{{ category.parent_name }}</td>
                  <td class="text-center">{{ category.sort_order }}</td>
                  <td class="text-center"><span class="label label-{{ category.status_class }}">{{ category.status }}</span></td>
                  <td class="text-center">{{ category.posts_count }}</td>
                  <td class="text-right">
                    {% if can_edit %}
                    <a href="{{ category.edit }}" data-toggle="tooltip" title="{{ button_edit }}" class="btn btn-primary"><i class="fa fa-pencil"></i></a>
                    {% endif %}
                  </td>
                </tr>
                {% endfor %}
                {% else %}
                <tr>
                  <td class="text-center" colspan="7">{{ text_no_results }}</td>
                </tr>
                {% endif %}
              </tbody>
            </table>
          </div>
        </form>
        <div class="row">
          <div class="col-sm-6 text-left">{{ pagination }}</div>
          <div class="col-sm-6 text-right">{{ results }}</div>
        </div>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript"><!--
$('#button-filter').on('click', function() {
  var url = 'index.php?route=catalog/blog_category&user_token={{ user_token }}';

  var filter_name = $('input[name=\'filter_name\']').val();

  if (filter_name) {
    url += '&filter_name=' + encodeURIComponent(filter_name);
  }

  var filter_status = $('select[name=\'filter_status\']').val();

  if (filter_status !== '') {
    url += '&filter_status=' + encodeURIComponent(filter_status);
  }

  location = url;
});

$('input[name=\'filter_name\']').on('keydown', function(e) {
  if (e.keyCode == 13) {
    $('#button-filter').trigger('click');
  }
});
//--></script>

{{ footer }}