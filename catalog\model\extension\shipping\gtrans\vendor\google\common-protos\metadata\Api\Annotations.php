<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/annotations.proto

namespace GPBMetadata\Google\Api;

class Annotations
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        \GPBMetadata\Google\Api\Http::initOnce();
        $pool->internalAddGeneratedFile(hex2bin(
            "0ac4010a1c676f6f676c652f6170692f616e6e6f746174696f6e732e7072" .
            "6f746f120a676f6f676c652e6170691a20676f6f676c652f70726f746f62" .
            "75662f64657363726970746f722e70726f746f426e0a0e636f6d2e676f6f" .
            "676c652e6170694210416e6e6f746174696f6e7350726f746f50015a4167" .
            "6f6f676c652e676f6c616e672e6f72672f67656e70726f746f2f676f6f67" .
            "6c65617069732f6170692f616e6e6f746174696f6e733b616e6e6f746174" .
            "696f6e73a2020447415049620670726f746f33"
        ), true);

        static::$is_initialized = true;
    }
}

