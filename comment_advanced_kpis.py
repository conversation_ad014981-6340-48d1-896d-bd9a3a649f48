#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريبت تعليق KPIs المتقدمة في dashboard.php
يعلق جميع الدوال التي تستخدم جداول غير موجودة في db.txt
ويبقي فقط على الأساسيات التي تعمل فعلاً
"""

import re
import os
from datetime import datetime

def main():
    file_path = "dashboard/model/common/dashboard.php"
    
    if not os.path.exists(file_path):
        print(f"❌ الملف غير موجود: {file_path}")
        return
    
    print("🔧 بدء تعليق KPIs المتقدمة...")
    
    # قراءة الملف
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # إنشاء نسخة احتياطية
    backup_file = f"{file_path}.backup_before_commenting.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    with open(backup_file, 'w', encoding='utf-8') as f:
        f.write(content)
    print(f"📁 تم إنشاء نسخة احتياطية: {backup_file}")
    
    # الجداول غير الموجودة في db.txt - يجب تعليق أي دالة تستخدمها
    non_existing_tables = [
        # جداول AI
        'ai_fraud_detection', 'ai_sentiment_analysis', 'ai_price_optimization',
        'ai_demand_forecast', 'ai_chatbot_interactions', 'ai_customer_behavior',
        'ai_supply_chain_optimization', 'ai_classification', 'ai_analytics',
        
        # جداول Analytics المتقدمة
        'data_quality_assessment', 'report_generation_log', 'website_performance',
        'compliance_audit', 'internal_controls', 'security_incidents',
        
        # جداول Mobile
        'mobile_app_downloads', 'mobile_app_users', 'mobile_app_sessions',
        'mobile_app_reviews', 'mobile_app_performance', 'mobile_app_errors',
        'mobile_app_battery_usage', 'mobile_app_data_usage', 'mobile_app_feedback',
        'mobile_app_updates', 'mobile_session', 'mobile_performance',
        
        # جداول API & Integration
        'api_performance_log', 'integration_log', 'external_service_calls',
        'api_security_log', 'connection_log', 'test_coverage', 'bug_reports',
        'system_uptime', 'system_downtime',
        
        # جداول HR المتقدمة
        'performance_assessment', 'training_completion', 'training_feedback',
        'salary', 'benefits', 'market_salary', 'skill', 'employee_skill',
        'future_planning', 'safety_incident', 'employee_survey', 'leadership_profile',
        'talent_profile', 'mentorship', 'culture_survey', 'succession_plan',
        'succession_candidate', 'team', 'team_member', 'team_performance',
        'work_life_balance', 'innovation_idea', 'innovation_project_member',
        'internal_communication', 'communication_recipient', 'flexibility_assessment',
        'work_arrangement_request', 'job_requisition', 'job_application',
        'wellbeing_assessment', 'wellness_program_participation', 'mental_health_session',
        'support_request', 'career_profile', 'promotion', 'organizational_assessment',
        'sustainability_initiative', 'digital_technology', 'technology_usage',
        'technology_training', 'knowledge_article', 'knowledge_usage',
        'knowledge_contribution', 'learning_log',
        
        # جداول التجارة الإلكترونية المتقدمة
        'digital_marketing_campaign', 'digital_audience_analysis', 'digital_content',
        'content_performance', 'content_seo', 'content_user_engagement',
        'payment_transaction', 'payment_security', 'payment_performance',
        'payment_customer_feedback', 'seo_page', 'seo_technical', 'seo_keywords',
        'seo_organic', 'seo_competitor', 'social_share', 'loyalty_program_participation',
        'recommendation_view', 'recommendation_click', 'recommendation_cart_addition',
        'recommendation_purchase', 'personalization_score', 'customer_recommendation_feedback',
        'social_media_post', 'social_commerce', 'social_audience', 'social_paid_campaign',
        'email_conversion', 'email_analysis', 'email_timing', 'email_segment',
        
        # جداول المشتريات المتقدمة
        'quality_control', 'purchase_invoice', 'supply_chain', 'supply_chain_stage',
        'logistics_service', 'inventory_analysis', 'supply_chain_risk',
        'negotiation_contract', 'negotiation_notes', 'contract_terms',
        'contract_performance', 'contract_risk', 'procurement_risk', 'risk_supplier',
        'risk_product_category', 'risk_mitigation', 'risk_impact', 'quality_standards',
        'compliance_standards', 'corrective_action', 'quality_cost',
        'strategic_procurement', 'strategic_category', 'strategic_supplier',
        'procurement_kpi', 'market_analysis', 'csr_sustainability',
        'environmental_metrics', 'social_metrics', 'economic_metrics',
        'governance_metrics', 'sustainability_impact', 'digital_transformation',
        'procurement_system', 'digital_process', 'digital_kpi', 'regulatory_compliance',
        'compliance_requirement', 'compliance_cost', 'procurement_innovation',
        'supplier_innovation', 'innovative_product', 'innovation_kpi',
        'collaboration_partnership',
        
        # جداول أخرى متقدمة
        'production_cycle', 'equipment_schedule', 'equipment_downtime', 'equipment_oee',
        'material_consumption', 'production_batch', 'production_rework',
        'energy_consumption', 'carbon_footprint', 'waste_management',
        'water_consumption', 'energy_efficiency', 'inventory', 'warehouse',
        'inventory_movement', 'demand_analysis', 'inventory_kpi', 'forecast_planning',
        'procurement_plan', 'planning_kpi', 'scenario_analysis',
        
        # جداول مالية متقدمة
        'account_description', 'journal_entry', 'sales_target', 'commission',
        'pos_shift', 'cash_voucher', 'stock_movement', 'purchase_return',
        'quotation_comparison', 'quotation', 'cash_flow', 'tax_transaction',
        'fixed_assets', 'cost_center', 'investment', 'accounts_payable',
        'branch_costs', 'loan', 'expense_detail', 'loan_transaction', 'revenue_detail'
    ]
    
    # البحث عن الدوال التي تستخدم هذه الجداول وتعليقها
    lines = content.split('\n')
    in_function = False
    function_start = 0
    brace_count = 0
    commented_functions = []
    
    for i, line in enumerate(lines):
        # البحث عن بداية دالة
        if re.search(r'^\s*public function get\w+\(', line):
            in_function = True
            function_start = i
            brace_count = 0
            function_name = re.search(r'function (\w+)\(', line).group(1)
        
        if in_function:
            # عد الأقواس المجعدة
            brace_count += line.count('{') - line.count('}')
            
            # فحص إذا كانت الدالة تستخدم جداول غير موجودة
            for table in non_existing_tables:
                if f'DB_PREFIX . "{table}"' in line or f'" . DB_PREFIX . "{table}' in line:
                    # تعليق الدالة بالكامل
                    for j in range(function_start, i + 20):  # تعليق حتى نهاية الدالة
                        if j < len(lines):
                            if not lines[j].strip().startswith('//'):
                                lines[j] = '        // ' + lines[j] if lines[j].strip() else lines[j]
                    
                    commented_functions.append(function_name)
                    in_function = False
                    break
            
            # نهاية الدالة
            if brace_count == 0 and i > function_start:
                in_function = False
    
    # إضافة تعليق في بداية الملف
    header_comment = '''
    /*
     * ملاحظة: تم تعليق الدوال التالية مؤقتاً لأنها تستخدم جداول غير موجودة في db.txt
     * سيتم تفعيلها لاحقاً عند إنشاء الجداول المطلوبة
     * 
     * الدوال المُعلقة: ''' + ', '.join(commented_functions) + '''
     * 
     * تاريخ التعليق: ''' + datetime.now().strftime('%Y-%m-%d %H:%M:%S') + '''
     */
    '''
    
    # إدراج التعليق بعد السطر الأول
    lines.insert(1, header_comment)
    
    # حفظ الملف المُعدل
    modified_content = '\n'.join(lines)
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(modified_content)
    
    # إحصائيات النتائج
    final_db_prefix_count = modified_content.count('DB_PREFIX')
    
    print(f"\n📊 تقرير التعليق:")
    print(f"✅ تم تعليق {len(commented_functions)} دالة")
    print(f"📝 الدوال المُعلقة:")
    for func in commented_functions:
        print(f"   - {func}()")
    
    print(f"\n📈 استخدامات DB_PREFIX المتبقية: {final_db_prefix_count}")
    print(f"📁 النسخة الاحتياطية: {backup_file}")
    
    if final_db_prefix_count < 50:
        print("\n🎉 ممتاز! معظم المشاكل تم حلها!")
    else:
        print(f"\n⚠️  لا تزال هناك {final_db_prefix_count} استخدامات تحتاج مراجعة")
    
    print("🔧 انتهى التعليق!")

if __name__ == "__main__":
    main()
