{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">{% if not error_signature %}
        <button type="button" id="button-opencart" data-toggle="tooltip" title="{{ button_opencart }}" class="btn btn-info"><i class="fa fa-cog"></i></button>
        {% else %}
        <button type="button" id="button-opencart" data-toggle="tooltip" title="{{ error_signature }}"  data-placement="left" class="btn btn-danger"><i class="fa fa-exclamation-triangle"></i></button>
        {% endif %}</div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-puzzle-piece"></i> {{ text_list }}</h3>
      </div>
      <div class="panel-body">
        <div class="well">
          <div class="input-group" id="extension-filter">
            <input type="text" name="filter_search" value="{{ filter_search }}" placeholder="{{ text_search }}" class="form-control" />
            <div class="input-group-btn">{% for category in categories %}
              {% if category.value == filter_category %}
              <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">{{ text_category }} ({{ category.text }}) <span class="caret"></span></button>
              {% endif %}
              {% endfor %}
              <ul class="dropdown-menu">
                {% for category in categories %}
                <li><a href="{{ category.href }}">{{ category.text }}</a></li>
                {% endfor %}
              </ul>
              <input type="hidden" name="filter_category_id" value="{{ filter_category_id }}" class="form-control" />
              <input type="hidden" name="filter_download_id" value="{{ filter_download_id }}" class="form-control" />
              <input type="hidden" name="filter_rating" value="{{ filter_rating }}" class="form-control" />
              <input type="hidden" name="filter_license" value="{{ filter_license }}" class="form-control" />
              <input type="hidden" name="filter_partner" value="{{ filter_partner }}" class="form-control" />
              <input type="hidden" name="sort" value="{{ sort }}" class="form-control" />
              <button type="button" id="button-filter" class="btn btn-primary"><i class="fa fa-filter"></i></button>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-sm-9 col-xs-7">
            <div class="btn-group">{% for license in licenses %}
              {% if license.value == filter_license %}<a href="{{ license.href }}" class="btn btn-default active">{{ license.text }}</a>{% else %}<a href="{{ license.href }}" class="btn btn-default">{{ license.text }}</a>{% endif %}
              {% endfor %}</div>
          </div>
          <div class="col-sm-3 col-xs-5">
            <div class="input-group pull-right">
              <div class="input-group-addon"><i class="fa fa-sort-amount-asc"></i></div>
              <select onchange="location = this.value;" class="form-control">
                  {% for sorts in sorts %}
                  {% if sorts.value == sort %}
                  <option value="{{ sorts.href }}" selected="selected">{{ sorts.text }}</option>
                  {% else %}
                  <option value="{{ sorts.href }}">{{ sorts.text }}</option>
                  {% endif %}
                  {% endfor %}
              </select>
            </div>
          </div>
        </div>
        <br/>
        <div id="extension-list">{% if promotions %}
          <h3>{{ text_featured }}</h3>
          <div class="row">{% for extension in promotions %}
            {% if extension %}
            <div class="col-lg-3 col-md-4 col-sm-6 col-xs-12">
              <section>
                <div class="extension-preview"><a href="{{ extension.href }}">
                  <div class="extension-description"></div>
                  <img src="{{ extension.image }}" alt="{{ extension.name }}" title="{{ extension.name }}" class="img-responsive" /></a></div>
                <div class="extension-name">
                  <h4><a href="{{ extension.href }}">{{ extension.name }}</a></h4>
                  {{ extension.price }}</div>
                <div class="extension-rate">
                  <div class="row">
                    <div class="col-xs-6">{% for i in 1..5 %}
                      {% if extension.rating >= i %}<i class="fa fa-star"></i>{% else %}<i class="fa fa-star-o"></i>{% endif %}
                      {% endfor %}</div>
                    <div class="col-xs-6">
                      <div class="text-right">{{ extension.rating_total }} {{ text_reviews }}</div>
                    </div>
                  </div>
                </div>
              </section>
            </div>
            {% endif %}
            {% endfor %}</div>
          <hr />
          {% endif %}
          
          {% if extensions %}
          <div class="row"> {% for extension in extensions %}
            
            {% if extension %}
            <div class="col-lg-3 col-md-4 col-sm-6 col-xs-12">
              <section>
                <div class="extension-preview"><a href="{{ extension.href }}">
                  <div class="extension-description"></div>
                  <img src="{{ extension.image }}" alt="{{ extension.name }}" title="{{ extension.name }}" class="img-responsive" /></a></div>
                <div class="extension-name">
                  <h4><a href="{{ extension.href }}">{{ extension.name }}</a></h4>
                  {{ extension.price }}</div>
                <div class="extension-rate">
                  <div class="row">
                    <div class="col-xs-6">{% for i in 1..5 %}
                      {% if extension.rating >= i %}<i class="fa fa-star"></i>{% else %}<i class="fa fa-star-o"></i>{% endif %}
                      {% endfor %}</div>
                    <div class="col-xs-6">
                      <div class="text-right">{{ extension.rating_total }} {{ text_reviews }}</div>
                    </div>
                  </div>
                </div>
              </section>
            </div>
            {% endif %}
            {% endfor %} </div>
          {% else %}
          <p class="text-center">{{ text_no_results }}</p>
          {% endif %} </div>
        <div class="row">
          <div class="col-sm-12 text-center">{{ pagination }}</div>
        </div>
      </div>
    </div>
  </div>

<script type="text/javascript"><!--
$('#button-opencart').on('click', function(e) {
	$('#modal-opencart').remove();
	
	$.ajax({
		url: 'index.php?route=marketplace/api&user_token={{ user_token }}',
		dataType: 'html',
		beforeSend: function() {
			$('#button-opencart').button('loading');
		},
		complete: function() {
			$('#button-opencart').button('reset');
		},
		success: function(html) {
			$('body').append('<div id="modal-opencart" class="modal">' + html + '</div>');
			
			$('#modal-opencart').modal('show');
		},
		error: function(xhr, ajaxOptions, thrownError) {
			alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
		}
	});
});
//--></script> 
<script type="text/javascript"><!--
$('#button-filter').on('click', function(e) {
	var url = 'index.php?route=marketplace/marketplace&user_token={{ user_token }}';

	var input = $('#extension-filter :input');

	for (i = 0; i < input.length; i++) {
		if ($(input[i]).val() != '' && $(input[i]).val() != null) {
			url += '&' + $(input[i]).attr('name') + '=' + $(input[i]).val()
		}
	}

	location = url;
});

$('input[name="filter_search"]').keydown(function(e) {
	if (e.keyCode == 13) {
		e.preventDefault();

		$('#button-filter').trigger('click');
	}
});
//--></script></div>
{{ footer }} 
