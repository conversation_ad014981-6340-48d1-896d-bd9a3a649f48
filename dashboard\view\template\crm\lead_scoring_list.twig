{{ header }}{{ column_left }}
<div class="container-fluid">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="button" data-toggle="tooltip" title="{{ button_bulk_score }}" class="btn btn-info" onclick="bulkScore();"><i class="fa fa-calculator"></i></button>
        <button type="button" data-toggle="tooltip" title="{{ button_export }}" class="btn btn-success" onclick="exportData();"><i class="fa fa-download"></i></button>
        <button type="button" data-toggle="tooltip" title="{{ button_scoring_rules }}" class="btn btn-warning" onclick="location = '{{ scoring_rules }}';"><i class="fa fa-cogs"></i></button>
        <button type="button" data-toggle="tooltip" title="{{ button_analytics }}" class="btn btn-primary" onclick="location = '{{ analytics }}';"><i class="fa fa-bar-chart"></i></button>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  
  <!-- إحصائيات سريعة -->
  <div class="row">
    <div class="col-lg-3 col-md-6">
      <div class="panel panel-primary">
        <div class="panel-heading">
          <div class="row">
            <div class="col-xs-3">
              <i class="fa fa-users fa-5x"></i>
            </div>
            <div class="col-xs-9 text-right">
              <div class="huge">{{ statistics.total_leads }}</div>
              <div>{{ text_total_leads }}</div>
            </div>
          </div>
        </div>
        <div class="panel-footer">
          <span class="pull-left">{{ text_total_leads }}</span>
          <span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
          <div class="clearfix"></div>
        </div>
      </div>
    </div>
    
    <div class="col-lg-3 col-md-6">
      <div class="panel panel-red">
        <div class="panel-heading">
          <div class="row">
            <div class="col-xs-3">
              <i class="fa fa-fire fa-5x"></i>
            </div>
            <div class="col-xs-9 text-right">
              <div class="huge">{{ statistics.hot_leads }}</div>
              <div>{{ text_hot_leads }}</div>
            </div>
          </div>
        </div>
        <div class="panel-footer">
          <span class="pull-left">{{ text_priority_hot }}</span>
          <span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
          <div class="clearfix"></div>
        </div>
      </div>
    </div>
    
    <div class="col-lg-3 col-md-6">
      <div class="panel panel-yellow">
        <div class="panel-heading">
          <div class="row">
            <div class="col-xs-3">
              <i class="fa fa-star fa-5x"></i>
            </div>
            <div class="col-xs-9 text-right">
              <div class="huge">{{ statistics.avg_score }}</div>
              <div>{{ text_avg_score }}</div>
            </div>
          </div>
        </div>
        <div class="panel-footer">
          <span class="pull-left">{{ text_avg_score }}</span>
          <span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
          <div class="clearfix"></div>
        </div>
      </div>
    </div>
    
    <div class="col-lg-3 col-md-6">
      <div class="panel panel-green">
        <div class="panel-heading">
          <div class="row">
            <div class="col-xs-3">
              <i class="fa fa-exchange fa-5x"></i>
            </div>
            <div class="col-xs-9 text-right">
              <div class="huge">{{ statistics.conversion_rate }}%</div>
              <div>{{ text_conversion_rate }}</div>
            </div>
          </div>
        </div>
        <div class="panel-footer">
          <span class="pull-left">{{ text_conversion_rate }}</span>
          <span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
          <div class="clearfix"></div>
        </div>
      </div>
    </div>
  </div>

  <div class="panel panel-default">
    <div class="panel-heading">
      <h3 class="panel-title"><i class="fa fa-list"></i> {{ text_list }}</h3>
    </div>
    <div class="panel-body">
      <!-- فلاتر البحث -->
      <div class="well">
        <div class="row">
          <div class="col-sm-4">
            <div class="form-group">
              <label class="control-label" for="input-name">{{ entry_filter_name }}</label>
              <input type="text" name="filter_name" value="{{ filter_name }}" placeholder="{{ entry_filter_name }}" id="input-name" class="form-control" />
            </div>
          </div>
          <div class="col-sm-4">
            <div class="form-group">
              <label class="control-label" for="input-score-range">{{ entry_filter_score_range }}</label>
              <select name="filter_score_range" id="input-score-range" class="form-control">
                <option value="">{{ text_all }}</option>
                {% for key, value in score_ranges %}
                <option value="{{ key }}"{% if filter_score_range == key %} selected="selected"{% endif %}>{{ value }}</option>
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="col-sm-4">
            <div class="form-group">
              <label class="control-label" for="input-priority">{{ entry_filter_priority }}</label>
              <select name="filter_priority" id="input-priority" class="form-control">
                <option value="">{{ text_all }}</option>
                <option value="hot"{% if filter_priority == 'hot' %} selected="selected"{% endif %}>{{ text_priority_hot }}</option>
                <option value="warm"{% if filter_priority == 'warm' %} selected="selected"{% endif %}>{{ text_priority_warm }}</option>
                <option value="cold"{% if filter_priority == 'cold' %} selected="selected"{% endif %}>{{ text_priority_cold }}</option>
              </select>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-sm-4">
            <div class="form-group">
              <label class="control-label" for="input-status">{{ entry_filter_status }}</label>
              <select name="filter_status" id="input-status" class="form-control">
                <option value="">{{ text_all }}</option>
                <option value="new"{% if filter_status == 'new' %} selected="selected"{% endif %}>{{ text_status_new }}</option>
                <option value="contacted"{% if filter_status == 'contacted' %} selected="selected"{% endif %}>{{ text_status_contacted }}</option>
                <option value="qualified"{% if filter_status == 'qualified' %} selected="selected"{% endif %}>{{ text_status_qualified }}</option>
                <option value="converted"{% if filter_status == 'converted' %} selected="selected"{% endif %}>{{ text_status_converted }}</option>
              </select>
            </div>
          </div>
          <div class="col-sm-4">
            <div class="form-group">
              <label class="control-label" for="input-source">{{ entry_filter_source }}</label>
              <select name="filter_source" id="input-source" class="form-control">
                <option value="">{{ text_all }}</option>
                <option value="website"{% if filter_source == 'website' %} selected="selected"{% endif %}>{{ text_source_website }}</option>
                <option value="social_media"{% if filter_source == 'social_media' %} selected="selected"{% endif %}>{{ text_source_social }}</option>
                <option value="email"{% if filter_source == 'email' %} selected="selected"{% endif %}>{{ text_source_email }}</option>
                <option value="referral"{% if filter_source == 'referral' %} selected="selected"{% endif %}>{{ text_source_referral }}</option>
              </select>
            </div>
          </div>
          <div class="col-sm-4">
            <div class="form-group">
              <label class="control-label" for="input-assigned-to">{{ entry_filter_assigned_to }}</label>
              <select name="filter_assigned_to" id="input-assigned-to" class="form-control">
                <option value="">{{ text_all }}</option>
                {% for user in users %}
                <option value="{{ user.user_id }}"{% if filter_assigned_to == user.user_id %} selected="selected"{% endif %}>{{ user.firstname }}</option>
                {% endfor %}
              </select>
            </div>
          </div>
        </div>
        <div class="text-right">
          <button type="button" id="button-filter" class="btn btn-primary"><i class="fa fa-search"></i> {{ button_filter }}</button>
          <button type="button" id="button-clear" class="btn btn-default"><i class="fa fa-refresh"></i> {{ button_clear }}</button>
        </div>
      </div>

      <form action="{{ delete }}" method="post" enctype="multipart/form-data" id="form-lead">
        <div class="table-responsive">
          <table class="table table-bordered table-hover">
            <thead>
              <tr>
                <td style="width: 1px;" class="text-center"><input type="checkbox" onclick="$('input[name*=\'selected\']').prop('checked', this.checked);" /></td>
                <td class="text-left">{% if sort == 'customer_name' %}<a href="{{ sort_name }}" class="{{ order|lower }}">{{ column_customer_name }}</a>{% else %}<a href="{{ sort_name }}">{{ column_customer_name }}</a>{% endif %}</td>
                <td class="text-left">{{ column_email }}</td>
                <td class="text-left">{{ column_company }}</td>
                <td class="text-center">{{ column_source }}</td>
                <td class="text-center">{% if sort == 'total_score' %}<a href="{{ sort_score }}" class="{{ order|lower }}">{{ column_total_score }}</a>{% else %}<a href="{{ sort_score }}">{{ column_total_score }}</a>{% endif %}</td>
                <td class="text-center">{{ column_priority }}</td>
                <td class="text-center">{{ column_conversion_probability }}</td>
                <td class="text-right">{{ column_estimated_value }}</td>
                <td class="text-center">{{ column_last_activity }}</td>
                <td class="text-right">{{ column_action }}</td>
              </tr>
            </thead>
            <tbody>
              {% if leads %}
              {% for lead in leads %}
              <tr>
                <td class="text-center">{% if lead.selected %}<input type="checkbox" name="selected[]" value="{{ lead.lead_id }}" checked="checked" />{% else %}<input type="checkbox" name="selected[]" value="{{ lead.lead_id }}" />{% endif %}</td>
                <td class="text-left">
                  <strong>{{ lead.customer_name }}</strong><br>
                  <small class="text-muted">{{ lead.phone }}</small>
                </td>
                <td class="text-left">{{ lead.email }}</td>
                <td class="text-left">{{ lead.company }}</td>
                <td class="text-center">
                  <span class="label label-info">{{ lead.source }}</span>
                </td>
                <td class="text-center">
                  <span class="label label-{{ lead.score_class }}">{{ lead.total_score }}</span>
                </td>
                <td class="text-center">
                  <span class="label label-{{ lead.priority_class }}">{{ lead.priority_text }}</span>
                </td>
                <td class="text-center">
                  <div class="progress" style="margin-bottom: 0;">
                    <div class="progress-bar progress-bar-{{ lead.score_class }}" role="progressbar" style="width: {{ lead.conversion_probability }}%">
                      {{ lead.conversion_probability }}
                    </div>
                  </div>
                </td>
                <td class="text-right">{{ lead.estimated_value }} {{ text_currency }}</td>
                <td class="text-center">{{ lead.last_activity }}</td>
                <td class="text-right">
                  <div class="btn-group">
                    <button type="button" class="btn btn-primary btn-sm dropdown-toggle" data-toggle="dropdown">
                      {{ button_action }} <span class="caret"></span>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-right">
                      <li><a href="{{ lead.view }}"><i class="fa fa-eye"></i> {{ button_view }}</a></li>
                      <li><a href="{{ lead.edit }}"><i class="fa fa-pencil"></i> {{ button_edit }}</a></li>
                      <li><a href="{{ lead.convert }}"><i class="fa fa-exchange"></i> {{ button_convert }}</a></li>
                      <li><a href="{{ lead.activities }}"><i class="fa fa-list"></i> {{ button_activities }}</a></li>
                      <li class="divider"></li>
                      <li><a href="javascript:void(0);" onclick="recalculateScore({{ lead.lead_id }});"><i class="fa fa-calculator"></i> {{ button_recalculate }}</a></li>
                    </ul>
                  </div>
                </td>
              </tr>
              {% endfor %}
              {% else %}
              <tr>
                <td class="text-center" colspan="11">{{ text_no_leads }}</td>
              </tr>
              {% endif %}
            </tbody>
          </table>
        </div>
      </form>
      
      <div class="row">
        <div class="col-sm-6 text-left">{{ pagination }}</div>
        <div class="col-sm-6 text-right">{{ results }}</div>
      </div>
    </div>
  </div>
</div>

<!-- Modal للإجراءات المجمعة -->
<div class="modal fade" id="modal-bulk-score" tabindex="-1" role="dialog">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal"><span>&times;</span></button>
        <h4 class="modal-title">{{ button_bulk_score }}</h4>
      </div>
      <div class="modal-body">
        <p>{{ text_confirm_bulk_score }}</p>
        <div class="form-group">
          <label>{{ entry_scoring_method }}</label>
          <select class="form-control" id="bulk-scoring-method">
            <option value="auto">{{ text_auto_scoring }}</option>
            <option value="manual">{{ text_manual_scoring }}</option>
            <option value="hybrid">{{ text_hybrid_scoring }}</option>
          </select>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_cancel }}</button>
        <button type="button" class="btn btn-primary" onclick="confirmBulkScore();">{{ button_save }}</button>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript">
// فلترة البيانات
$('#button-filter').on('click', function() {
  var url = 'index.php?route=crm/lead_scoring&user_token={{ user_token }}';
  
  var filter_name = $('input[name=\'filter_name\']').val();
  if (filter_name) {
    url += '&filter_name=' + encodeURIComponent(filter_name);
  }
  
  var filter_score_range = $('select[name=\'filter_score_range\']').val();
  if (filter_score_range) {
    url += '&filter_score_range=' + encodeURIComponent(filter_score_range);
  }
  
  var filter_priority = $('select[name=\'filter_priority\']').val();
  if (filter_priority) {
    url += '&filter_priority=' + encodeURIComponent(filter_priority);
  }
  
  var filter_status = $('select[name=\'filter_status\']').val();
  if (filter_status) {
    url += '&filter_status=' + encodeURIComponent(filter_status);
  }
  
  var filter_source = $('select[name=\'filter_source\']').val();
  if (filter_source) {
    url += '&filter_source=' + encodeURIComponent(filter_source);
  }
  
  var filter_assigned_to = $('select[name=\'filter_assigned_to\']').val();
  if (filter_assigned_to) {
    url += '&filter_assigned_to=' + encodeURIComponent(filter_assigned_to);
  }
  
  location = url;
});

// مسح الفلاتر
$('#button-clear').on('click', function() {
  location = 'index.php?route=crm/lead_scoring&user_token={{ user_token }}';
});

// إعادة حساب النقاط
function recalculateScore(lead_id) {
  if (confirm('{{ text_confirm_recalculate }}')) {
    $.ajax({
      url: 'index.php?route=crm/lead_scoring/recalculate&user_token={{ user_token }}&lead_id=' + lead_id,
      type: 'post',
      dataType: 'json',
      success: function(json) {
        if (json['success']) {
          location.reload();
        }
        
        if (json['error']) {
          alert(json['error']);
        }
      }
    });
  }
}

// التقييم المجمع
function bulkScore() {
  var selected = $('input[name*=\'selected\']:checked');
  
  if (selected.length) {
    $('#modal-bulk-score').modal('show');
  } else {
    alert('{{ text_select_leads }}');
  }
}

function confirmBulkScore() {
  var selected = [];
  $('input[name*=\'selected\']:checked').each(function() {
    selected.push($(this).val());
  });
  
  var method = $('#bulk-scoring-method').val();
  
  $.ajax({
    url: 'index.php?route=crm/lead_scoring/bulkScore&user_token={{ user_token }}',
    type: 'post',
    data: {
      selected: selected,
      method: method
    },
    dataType: 'json',
    beforeSend: function() {
      $('#modal-bulk-score').modal('hide');
      $('.btn').prop('disabled', true);
    },
    complete: function() {
      $('.btn').prop('disabled', false);
    },
    success: function(json) {
      if (json['success']) {
        location.reload();
      }
      
      if (json['error']) {
        alert(json['error']);
      }
    }
  });
}

// تصدير البيانات
function exportData() {
  var url = 'index.php?route=crm/lead_scoring/export&user_token={{ user_token }}';
  
  // إضافة الفلاتر الحالية
  var filter_name = $('input[name=\'filter_name\']').val();
  if (filter_name) {
    url += '&filter_name=' + encodeURIComponent(filter_name);
  }
  
  window.open(url, '_blank');
}

// تفعيل التلميحات
$('[data-toggle="tooltip"]').tooltip();

// تحديث الإحصائيات كل 30 ثانية
setInterval(function() {
  $.ajax({
    url: 'index.php?route=crm/lead_scoring/getStatistics&user_token={{ user_token }}',
    type: 'get',
    dataType: 'json',
    success: function(json) {
      if (json['statistics']) {
        // تحديث الإحصائيات في الصفحة
        $('.huge').each(function(index) {
          var stat_keys = ['total_leads', 'hot_leads', 'avg_score', 'conversion_rate'];
          if (json['statistics'][stat_keys[index]]) {
            $(this).text(json['statistics'][stat_keys[index]]);
          }
        });
      }
    }
  });
}, 30000);
</script>

{{ footer }}
