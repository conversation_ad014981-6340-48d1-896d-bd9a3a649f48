<?php
// Heading
$_['heading_title']                    = 'Royal Mail';

// Text
$_['text_extension']                   = 'Extensions';
$_['text_success']                     = 'Success: You have modified Royal Mail shipping!';
$_['text_edit']                        = 'Edit Royal Mail Shipping';

// Entry
$_['entry_rate']                       = 'Rates';
$_['entry_rate_eu']                    = 'Europe Rates';
$_['entry_rate_non_eu']                = 'Non Europe Rates';
$_['entry_rate_zone_1']                = 'World Zone 1 Rates';
$_['entry_rate_zone_2']                = 'World Zone 2 Rates';
$_['entry_insurance']                  = 'Compensation Rates';
$_['entry_display_weight']             = 'Display Delivery Weight';
$_['entry_display_insurance']          = 'Display Insurance';
$_['entry_weight_class']               = 'Weight Class';
$_['entry_tax_class']                  = 'Tax Class';
$_['entry_geo_zone']                   = 'Geo Zone';
$_['entry_status']                     = 'Status';
$_['entry_sort_order']                 = 'Sort Order';

// Help
$_['help_rate']                        = 'Example: 5:10.00,7:12.00 Weight:Cost,Weight:Cost, etc..';
$_['help_insurance']                   = 'Enter values upto 5,2 decimal places. (12345.67) Example: 34:0,100:1,250:2.25 - Insurance cover for cart values upto 34 would cost 0.00 extra, those values more than 100 and upto 250 will cost 2.25 extra. Do not enter currency symbols.';
$_['help_display_weight']              = 'Do you want to display the shipping weight? (e.g. Delivery Weight : 2.7674 kg)';
$_['help_display_insurance']           = 'Do you want to display the shipping insurance? (e.g. Insured upto &pound;500)';
$_['help_international']               = '<p>Shipping services and prices guide is avaliable here:</p><p><a href="http://www.royalmail.com/international-zones" target="_blank">http://www.royalmail.com/international-zones</a></p><p><a href="http://www.royalmail.com/sites/default/files/RM_OurPrices_Mar2014a.pdf" target="_blank">http://www.royalmail.com/sites/default/files/RM_OurPrices_Mar2014a.pdf</a></p><p><a href="http://www.royalmail.com/sites/default/files/RoyalMail_International_TrackedCoverage_Jan2014.pdf" target="_blank">http://www.royalmail.com/sites/default/files/RoyalMail_International_TrackedCoverage_Jan2014.pdf</a></p>';

// Tab
$_['tab_special_delivery_500']         = 'Special Delivery Next Day (&pound;500)';
$_['tab_special_delivery_1000']        = 'Special Delivery Next Day (&pound;1000)';
$_['tab_special_delivery_2500']        = 'Special Delivery Next Day (&pound;2500)';
$_['tab_1st_class_signed']             = '1st Class Signed';
$_['tab_2nd_class_signed']             = '2nd Class Signed';
$_['tab_1st_class_standard']           = '1st Class Standard';
$_['tab_2nd_class_standard']           = '2nd Class Standard';
$_['tab_international_standard']       = 'International Standard';
$_['tab_international_tracked_signed'] = 'International Tracked & Signed';
$_['tab_international_tracked']        = 'International Tracked';
$_['tab_international_signed']         = 'International Signed';
$_['tab_international_economy']        = 'International Economy';

// Error
$_['error_permission']                 = 'Warning: You do not have permission to modify Royal Mail shipping!';
