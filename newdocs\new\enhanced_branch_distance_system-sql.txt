-- Enhanced Branch Distance System for AYM ERP
-- نظام المسافات المحسن للفروع - AYM ERP
-- Date: 20/7/2025

-- =====================================================
-- Branch Distance Matrix
-- مصفوفة المسافات بين الفروع والمحافظات
-- =====================================================

CREATE TABLE IF NOT EXISTS `cod_branch_distance` (
  `distance_id` int(11) NOT NULL AUTO_INCREMENT,
  `from_branch_id` int(11) NOT NULL COMMENT 'الفرع المرسل',
  `to_zone_id` int(11) NOT NULL COMMENT 'المحافظة المستقبلة',
  `distance_km` decimal(8,2) NOT NULL COMMENT 'المسافة بالكيلومتر',
  `estimated_delivery_hours` decimal(5,2) NOT NULL COMMENT 'ساعات التوصيل المتوقعة',
  `shipping_cost_per_kg` decimal(10,4) NOT NULL DEFAULT 0.0000 COMMENT 'تكلفة الشحن لكل كيلو',
  `priority` enum('primary','secondary','backup') NOT NULL DEFAULT 'primary' COMMENT 'أولوية الفرع لهذه المحافظة',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'نشط/غير نشط',
  `notes` text DEFAULT NULL COMMENT 'ملاحظات إضافية',
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_by` int(11) DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  PRIMARY KEY (`distance_id`),
  UNIQUE KEY `unique_branch_zone` (`from_branch_id`, `to_zone_id`),
  KEY `idx_branch_distance_from` (`from_branch_id`),
  KEY `idx_branch_distance_to` (`to_zone_id`),
  KEY `idx_branch_distance_priority` (`priority`),
  KEY `idx_branch_distance_active` (`is_active`),
  KEY `idx_branch_distance_delivery_time` (`estimated_delivery_hours`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- =====================================================
-- Zone Distance Matrix
-- مصفوفة المسافات بين المحافظات
-- =====================================================

CREATE TABLE IF NOT EXISTS `cod_zone_distance` (
  `zone_distance_id` int(11) NOT NULL AUTO_INCREMENT,
  `from_zone_id` int(11) NOT NULL COMMENT 'المحافظة المرسلة',
  `to_zone_id` int(11) NOT NULL COMMENT 'المحافظة المستقبلة',
  `distance_km` decimal(8,2) NOT NULL COMMENT 'المسافة بالكيلومتر',
  `travel_time_hours` decimal(5,2) NOT NULL COMMENT 'وقت السفر بالساعات',
  `road_quality` enum('excellent','good','fair','poor') NOT NULL DEFAULT 'good' COMMENT 'جودة الطريق',
  `toll_cost` decimal(8,2) NOT NULL DEFAULT 0.00 COMMENT 'رسوم الطرق',
  `fuel_cost_estimate` decimal(8,2) NOT NULL DEFAULT 0.00 COMMENT 'تقدير تكلفة الوقود',
  `is_direct_route` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'طريق مباشر أم لا',
  `alternative_routes` text DEFAULT NULL COMMENT 'الطرق البديلة',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  PRIMARY KEY (`zone_distance_id`),
  UNIQUE KEY `unique_zone_to_zone` (`from_zone_id`, `to_zone_id`),
  KEY `idx_zone_distance_from` (`from_zone_id`),
  KEY `idx_zone_distance_to` (`to_zone_id`),
  KEY `idx_zone_distance_km` (`distance_km`),
  KEY `idx_zone_travel_time` (`travel_time_hours`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- =====================================================
-- Smart Branch Selection Algorithm
-- خوارزمية اختيار الفرع الذكية
-- =====================================================

CREATE TABLE IF NOT EXISTS `cod_branch_selection_log` (
  `selection_id` int(11) NOT NULL AUTO_INCREMENT,
  `order_id` int(11) DEFAULT NULL COMMENT 'معرف الطلب',
  `customer_zone_id` int(11) NOT NULL COMMENT 'محافظة العميل',
  `product_id` int(11) NOT NULL COMMENT 'المنتج المطلوب',
  `required_quantity` decimal(15,4) NOT NULL COMMENT 'الكمية المطلوبة',
  `selected_branch_id` int(11) DEFAULT NULL COMMENT 'الفرع المختار',
  `selection_reason` enum('closest','available_stock','fastest_delivery','cost_effective','manual') NOT NULL COMMENT 'سبب الاختيار',
  `alternative_branches` text DEFAULT NULL COMMENT 'الفروع البديلة المتاحة (JSON)',
  `selection_score` decimal(5,2) DEFAULT NULL COMMENT 'نقاط الاختيار',
  `delivery_estimate_hours` decimal(5,2) DEFAULT NULL COMMENT 'تقدير التوصيل بالساعات',
  `shipping_cost_estimate` decimal(10,4) DEFAULT NULL COMMENT 'تقدير تكلفة الشحن',
  `selection_date` datetime NOT NULL DEFAULT current_timestamp(),
  `selected_by` int(11) DEFAULT NULL COMMENT 'المستخدم الذي اختار (NULL للتلقائي)',
  PRIMARY KEY (`selection_id`),
  KEY `idx_selection_order` (`order_id`),
  KEY `idx_selection_customer_zone` (`customer_zone_id`),
  KEY `idx_selection_product` (`product_id`),
  KEY `idx_selection_branch` (`selected_branch_id`),
  KEY `idx_selection_date` (`selection_date`),
  KEY `idx_selection_reason` (`selection_reason`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- =====================================================
-- Branch Inventory Alerts
-- تنبيهات مخزون الفروع
-- =====================================================

CREATE TABLE IF NOT EXISTS `cod_branch_inventory_alert` (
  `alert_id` int(11) NOT NULL AUTO_INCREMENT,
  `branch_id` int(11) NOT NULL COMMENT 'الفرع',
  `product_id` int(11) NOT NULL COMMENT 'المنتج',
  `alert_type` enum('low_stock','out_of_stock','overstock','expiry_warning') NOT NULL COMMENT 'نوع التنبيه',
  `current_quantity` decimal(15,4) NOT NULL COMMENT 'الكمية الحالية',
  `threshold_quantity` decimal(15,4) DEFAULT NULL COMMENT 'الحد المحدد للتنبيه',
  `recommended_action` enum('reorder','transfer_from_branch','transfer_to_branch','discount_sale','return_to_supplier') DEFAULT NULL COMMENT 'الإجراء المقترح',
  `priority` enum('low','medium','high','critical') NOT NULL DEFAULT 'medium' COMMENT 'أولوية التنبيه',
  `status` enum('active','acknowledged','resolved','ignored') NOT NULL DEFAULT 'active' COMMENT 'حالة التنبيه',
  `acknowledged_by` int(11) DEFAULT NULL COMMENT 'تم الاطلاع بواسطة',
  `acknowledged_at` datetime DEFAULT NULL COMMENT 'تاريخ الاطلاع',
  `resolved_by` int(11) DEFAULT NULL COMMENT 'تم الحل بواسطة',
  `resolved_at` datetime DEFAULT NULL COMMENT 'تاريخ الحل',
  `notes` text DEFAULT NULL COMMENT 'ملاحظات',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  PRIMARY KEY (`alert_id`),
  KEY `idx_alert_branch` (`branch_id`),
  KEY `idx_alert_product` (`product_id`),
  KEY `idx_alert_type` (`alert_type`),
  KEY `idx_alert_priority` (`priority`),
  KEY `idx_alert_status` (`status`),
  KEY `idx_alert_created` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- =====================================================
-- Enhanced Shipping Cost Calculator
-- حاسبة تكلفة الشحن المحسنة
-- =====================================================

CREATE TABLE IF NOT EXISTS `cod_shipping_cost_matrix` (
  `cost_id` int(11) NOT NULL AUTO_INCREMENT,
  `from_zone_id` int(11) NOT NULL COMMENT 'المحافظة المرسلة',
  `to_zone_id` int(11) NOT NULL COMMENT 'المحافظة المستقبلة',
  `shipping_company_id` int(11) NOT NULL COMMENT 'شركة الشحن',
  `weight_from` decimal(8,2) NOT NULL DEFAULT 0.00 COMMENT 'الوزن من (كيلو)',
  `weight_to` decimal(8,2) NOT NULL DEFAULT 999.99 COMMENT 'الوزن إلى (كيلو)',
  `base_cost` decimal(10,4) NOT NULL COMMENT 'التكلفة الأساسية',
  `cost_per_kg` decimal(10,4) NOT NULL DEFAULT 0.0000 COMMENT 'التكلفة لكل كيلو إضافي',
  `cod_fee` decimal(10,4) NOT NULL DEFAULT 0.0000 COMMENT 'رسوم الدفع عند الاستلام',
  `cod_percentage` decimal(5,4) NOT NULL DEFAULT 0.0000 COMMENT 'نسبة رسوم الدفع عند الاستلام',
  `fuel_surcharge` decimal(10,4) NOT NULL DEFAULT 0.0000 COMMENT 'رسوم الوقود الإضافية',
  `insurance_rate` decimal(5,4) NOT NULL DEFAULT 0.0000 COMMENT 'معدل التأمين',
  `min_charge` decimal(10,4) NOT NULL DEFAULT 0.0000 COMMENT 'الحد الأدنى للرسوم',
  `max_charge` decimal(10,4) DEFAULT NULL COMMENT 'الحد الأقصى للرسوم',
  `effective_from` date NOT NULL COMMENT 'ساري من تاريخ',
  `effective_to` date DEFAULT NULL COMMENT 'ساري حتى تاريخ',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'نشط/غير نشط',
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_by` int(11) DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL ON UPDATE current_timestamp(),
  PRIMARY KEY (`cost_id`),
  KEY `idx_cost_from_zone` (`from_zone_id`),
  KEY `idx_cost_to_zone` (`to_zone_id`),
  KEY `idx_cost_company` (`shipping_company_id`),
  KEY `idx_cost_weight_range` (`weight_from`, `weight_to`),
  KEY `idx_cost_effective_dates` (`effective_from`, `effective_to`),
  KEY `idx_cost_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- =====================================================
-- Branch Performance Metrics
-- مقاييس أداء الفروع
-- =====================================================

CREATE TABLE IF NOT EXISTS `cod_branch_performance` (
  `performance_id` int(11) NOT NULL AUTO_INCREMENT,
  `branch_id` int(11) NOT NULL COMMENT 'الفرع',
  `metric_date` date NOT NULL COMMENT 'تاريخ المقياس',
  `total_orders` int(11) NOT NULL DEFAULT 0 COMMENT 'إجمالي الطلبات',
  `fulfilled_orders` int(11) NOT NULL DEFAULT 0 COMMENT 'الطلبات المنفذة',
  `cancelled_orders` int(11) NOT NULL DEFAULT 0 COMMENT 'الطلبات الملغاة',
  `average_fulfillment_time` decimal(5,2) DEFAULT NULL COMMENT 'متوسط وقت التنفيذ (ساعات)',
  `total_revenue` decimal(15,4) NOT NULL DEFAULT 0.0000 COMMENT 'إجمالي الإيرادات',
  `total_shipping_cost` decimal(15,4) NOT NULL DEFAULT 0.0000 COMMENT 'إجمالي تكلفة الشحن',
  `customer_satisfaction_score` decimal(3,2) DEFAULT NULL COMMENT 'نقاط رضا العملاء (1-5)',
  `inventory_turnover_rate` decimal(5,2) DEFAULT NULL COMMENT 'معدل دوران المخزون',
  `stock_accuracy_percentage` decimal(5,2) DEFAULT NULL COMMENT 'نسبة دقة المخزون',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`performance_id`),
  UNIQUE KEY `unique_branch_date` (`branch_id`, `metric_date`),
  KEY `idx_performance_branch` (`branch_id`),
  KEY `idx_performance_date` (`metric_date`),
  KEY `idx_performance_fulfillment` (`average_fulfillment_time`),
  KEY `idx_performance_satisfaction` (`customer_satisfaction_score`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- =====================================================
-- Enhanced Indexes for Existing Tables
-- فهارس محسنة للجداول الموجودة
-- =====================================================

-- تحسين فهارس جدول الفروع
ALTER TABLE `cod_branch` 
ADD INDEX IF NOT EXISTS `idx_branch_type_online` (`type`, `available_online`),
ADD INDEX IF NOT EXISTS `idx_branch_manager` (`manager_id`),
ADD INDEX IF NOT EXISTS `idx_branch_eta` (`eta_branch_id`);

-- تحسين فهارس جدول مخزون المنتجات
ALTER TABLE `cod_product_inventory` 
ADD INDEX IF NOT EXISTS `idx_inventory_branch_product_qty` (`branch_id`, `product_id`, `quantity`),
ADD INDEX IF NOT EXISTS `idx_inventory_low_stock` (`branch_id`, `quantity`),
ADD INDEX IF NOT EXISTS `idx_inventory_product_branches` (`product_id`, `branch_id`);

-- تحسين فهارس جدول المحافظات
ALTER TABLE `cod_zone` 
ADD INDEX IF NOT EXISTS `idx_zone_country_status` (`country_id`, `status`),
ADD INDEX IF NOT EXISTS `idx_zone_name` (`name`),
ADD INDEX IF NOT EXISTS `idx_zone_code` (`code`);

-- تحسين فهارس جدول تغطية الشحن
ALTER TABLE `cod_shipping_coverage` 
ADD INDEX IF NOT EXISTS `idx_coverage_zone_priority` (`zone_id`, `priority`),
ADD INDEX IF NOT EXISTS `idx_coverage_delivery_days` (`delivery_days`),
ADD INDEX IF NOT EXISTS `idx_coverage_company_zone` (`company_id`, `zone_id`);

-- =====================================================
-- Sample Data for Egyptian Governorates
-- بيانات تجريبية للمحافظات المصرية
-- =====================================================

-- إدراج المحافظات المصرية الأساسية (عينة)
INSERT IGNORE INTO `cod_zone_distance` 
(`from_zone_id`, `to_zone_id`, `distance_km`, `travel_time_hours`, `road_quality`) VALUES
(1, 2, 165, 2.5, 'excellent'), -- القاهرة إلى الإسكندرية
(1, 3, 120, 1.8, 'good'),      -- القاهرة إلى الجيزة
(1, 4, 85, 1.2, 'excellent'),  -- القاهرة إلى القليوبية
(2, 1, 165, 2.5, 'excellent'), -- الإسكندرية إلى القاهرة
(2, 5, 95, 1.5, 'good'),       -- الإسكندرية إلى البحيرة
(3, 1, 120, 1.8, 'good'),      -- الجيزة إلى القاهرة
(4, 1, 85, 1.2, 'excellent');  -- القليوبية إلى القاهرة

-- =====================================================
-- Views for Easy Querying
-- Views للاستعلام السهل
-- =====================================================

CREATE OR REPLACE VIEW `view_branch_inventory_summary` AS
SELECT 
    b.branch_id,
    b.name as branch_name,
    b.type as branch_type,
    COUNT(pi.product_id) as total_products,
    SUM(pi.quantity) as total_quantity,
    SUM(pi.quantity * pi.average_cost) as total_value,
    COUNT(CASE WHEN pi.quantity <= pi.reorder_level THEN 1 END) as low_stock_items
FROM cod_branch b
LEFT JOIN cod_product_inventory pi ON b.branch_id = pi.branch_id
WHERE b.type = 'store' AND b.available_online = 1
GROUP BY b.branch_id, b.name, b.type;

CREATE OR REPLACE VIEW `view_optimal_branch_selection` AS
SELECT 
    bd.to_zone_id as customer_zone_id,
    b.branch_id,
    b.name as branch_name,
    bd.distance_km,
    bd.estimated_delivery_hours,
    bd.shipping_cost_per_kg,
    bd.priority,
    COUNT(pi.product_id) as available_products,
    SUM(pi.quantity) as total_stock
FROM cod_branch_distance bd
JOIN cod_branch b ON bd.from_branch_id = b.branch_id
LEFT JOIN cod_product_inventory pi ON b.branch_id = pi.branch_id AND pi.quantity > 0
WHERE b.type = 'store' AND b.available_online = 1 AND bd.is_active = 1
GROUP BY bd.to_zone_id, b.branch_id, b.name, bd.distance_km, bd.estimated_delivery_hours, bd.shipping_cost_per_kg, bd.priority
ORDER BY bd.to_zone_id, bd.priority, bd.estimated_delivery_hours;

-- =====================================================
-- End of Enhanced Branch Distance System
-- =====================================================
