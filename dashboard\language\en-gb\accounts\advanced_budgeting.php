<?php
// Heading
$_['heading_title']                    = 'Advanced Budgeting';

// Text
$_['text_success']                     = 'Success: You have modified advanced budgeting!';
$_['text_list']                        = 'Advanced Budgeting List';
$_['text_add']                         = 'Add Budget';
$_['text_edit']                        = 'Edit Budget';
$_['text_view']                        = 'View Budget';
$_['text_default']                     = 'Default';
$_['text_enabled']                     = 'Enabled';
$_['text_disabled']                    = 'Disabled';
$_['text_yes']                         = 'Yes';
$_['text_no']                          = 'No';
$_['text_none']                        = 'None';
$_['text_select']                      = 'Select';
$_['text_loading']                     = 'Loading...';
$_['text_no_results']                  = 'No results!';
$_['text_confirm']                     = 'Are you sure?';

// Budget specific
$_['text_budget_info']                 = 'Budget Information';
$_['text_budget_name']                 = 'Budget Name';
$_['text_budget_code']                 = 'Budget Code';
$_['text_budget_type']                 = 'Budget Type';
$_['text_budget_period']               = 'Budget Period';
$_['text_fiscal_year']                 = 'Fiscal Year';
$_['text_start_date']                  = 'Start Date';
$_['text_end_date']                    = 'End Date';
$_['text_department']                  = 'Department';
$_['text_cost_center']                 = 'Cost Center';
$_['text_budget_amount']               = 'Budget Amount';
$_['text_actual_amount']               = 'Actual Amount';
$_['text_variance']                    = 'Variance';
$_['text_variance_percentage']         = 'Variance Percentage';
$_['text_remaining_budget']            = 'Remaining Budget';

// Budget Types
$_['text_type_operational']            = 'Operational';
$_['text_type_capital']                = 'Capital';
$_['text_type_cash_flow']              = 'Cash Flow';
$_['text_type_master']                 = 'Master';
$_['text_type_flexible']               = 'Flexible';
$_['text_type_zero_based']             = 'Zero-Based';

// Status
$_['text_status_draft']                = 'Draft';
$_['text_status_submitted']            = 'Submitted';
$_['text_status_under_review']         = 'Under Review';
$_['text_status_approved']             = 'Approved';
$_['text_status_rejected']             = 'Rejected';
$_['text_status_active']               = 'Active';
$_['text_status_closed']               = 'Closed';

// Approval Workflow
$_['text_approval_workflow']           = 'Approval Workflow';
$_['text_approval_level']              = 'Approval Level';
$_['text_approver']                    = 'Approver';
$_['text_approval_date']               = 'Approval Date';
$_['text_approval_comments']           = 'Approval Comments';
$_['text_pending_approval']            = 'Pending Approval';
$_['text_approved_by']                 = 'Approved By';
$_['text_rejected_by']                 = 'Rejected By';

// Scenarios
$_['text_scenarios']                   = 'Scenarios';
$_['text_scenario_name']               = 'Scenario Name';
$_['text_scenario_type']               = 'Scenario Type';
$_['text_base_scenario']               = 'Base Scenario';
$_['text_optimistic_scenario']         = 'Optimistic Scenario';
$_['text_pessimistic_scenario']        = 'Pessimistic Scenario';
$_['text_realistic_scenario']          = 'Realistic Scenario';

// Variance Analysis
$_['text_variance_analysis']           = 'Variance Analysis';
$_['text_favorable_variance']          = 'Favorable Variance';
$_['text_unfavorable_variance']        = 'Unfavorable Variance';
$_['text_variance_reason']             = 'Variance Reason';
$_['text_corrective_action']           = 'Corrective Action';

// Statistics
$_['text_total_budgets']               = 'Total Budgets';
$_['text_active_budgets']              = 'Active Budgets';
$_['text_pending_budgets']             = 'Pending Budgets';
$_['text_approved_budgets']            = 'Approved Budgets';
$_['text_budget_utilization']          = 'Budget Utilization';
$_['text_overall_variance']            = 'Overall Variance';

// Buttons
$_['button_add']                       = 'Add';
$_['button_edit']                      = 'Edit';
$_['button_delete']                    = 'Delete';
$_['button_save']                      = 'Save';
$_['button_cancel']                    = 'Cancel';
$_['button_close']                     = 'Close';
$_['button_back']                      = 'Back';
$_['button_view']                      = 'View';
$_['button_print']                     = 'Print';
$_['button_export']                    = 'Export';
$_['button_submit']                    = 'Submit';
$_['button_approve']                   = 'Approve';
$_['button_reject']                    = 'Reject';
$_['button_copy']                      = 'Copy';
$_['button_analyze']                   = 'Analyze';
$_['button_scenario']                  = 'Scenario';

// Columns
$_['column_budget_name']               = 'Budget Name';
$_['column_budget_code']               = 'Budget Code';
$_['column_budget_type']               = 'Budget Type';
$_['column_fiscal_year']               = 'Fiscal Year';
$_['column_department']                = 'Department';
$_['column_budget_amount']             = 'Budget Amount';
$_['column_actual_amount']             = 'Actual Amount';
$_['column_variance']                  = 'Variance';
$_['column_status']                    = 'Status';
$_['column_approval_status']           = 'Approval Status';
$_['column_created_by']                = 'Created By';
$_['column_date_created']              = 'Date Created';
$_['column_action']                    = 'Action';

// Account columns
$_['column_account_code']              = 'Account Code';
$_['column_account_name']              = 'Account Name';
$_['column_q1_budget']                 = 'Q1 Budget';
$_['column_q2_budget']                 = 'Q2 Budget';
$_['column_q3_budget']                 = 'Q3 Budget';
$_['column_q4_budget']                 = 'Q4 Budget';
$_['column_annual_budget']             = 'Annual Budget';

// Entry fields
$_['entry_budget_name']                = 'Budget Name';
$_['entry_budget_code']                = 'Budget Code';
$_['entry_budget_type']                = 'Budget Type';
$_['entry_fiscal_year']                = 'Fiscal Year';
$_['entry_start_date']                 = 'Start Date';
$_['entry_end_date']                   = 'End Date';
$_['entry_department']                 = 'Department';
$_['entry_cost_center']                = 'Cost Center';
$_['entry_description']                = 'Description';
$_['entry_notes']                      = 'Notes';
$_['entry_approval_comments']          = 'Approval Comments';

// Help text
$_['help_budget_name']                 = 'Enter a descriptive name for the budget';
$_['help_budget_code']                 = 'Enter a unique code for the budget';
$_['help_budget_type']                 = 'Select the appropriate budget type';
$_['help_fiscal_year']                 = 'Select the fiscal year for the budget';

// Error messages
$_['error_permission']                 = 'Warning: You do not have permission to access advanced budgeting!';
$_['error_budget_name']                = 'Budget name must be between 3 and 64 characters!';
$_['error_budget_code']                = 'Budget code must be between 3 and 20 characters!';
$_['error_budget_type']                = 'Please select budget type!';
$_['error_fiscal_year']                = 'Please select fiscal year!';
$_['error_start_date']                 = 'Please enter start date!';
$_['error_end_date']                   = 'Please enter end date!';
$_['error_date_range']                 = 'End date must be after start date!';
$_['error_department']                 = 'Please select department!';
$_['error_duplicate_code']             = 'Budget code already exists!';
$_['error_invalid_amount']             = 'Invalid amount!';
$_['error_already_approved']           = 'Budget is already approved!';
$_['error_cannot_edit_approved']       = 'Cannot edit approved budget!';

// Success messages
$_['success_budget_added']             = 'Budget added successfully!';
$_['success_budget_updated']           = 'Budget updated successfully!';
$_['success_budget_deleted']           = 'Budget deleted successfully!';
$_['success_budget_submitted']         = 'Budget submitted for approval successfully!';
$_['success_budget_approved']          = 'Budget approved successfully!';
$_['success_budget_rejected']          = 'Budget rejected successfully!';
$_['success_scenario_created']         = 'Scenario created successfully!';

// Confirmation messages
$_['confirm_delete']                   = 'Are you sure you want to delete this budget?';
$_['confirm_submit']                   = 'Are you sure you want to submit the budget for approval?';
$_['confirm_approve']                  = 'Are you sure you want to approve this budget?';
$_['confirm_reject']                   = 'Are you sure you want to reject this budget?';

// Tabs
$_['tab_general']                      = 'General';
$_['tab_accounts']                     = 'Accounts';
$_['tab_quarterly']                    = 'Quarterly';
$_['tab_approval']                     = 'Approval';
$_['tab_scenarios']                    = 'Scenarios';
$_['tab_variance']                     = 'Variance Analysis';

// Reports
$_['text_budget_report']               = 'Budget Report';
$_['text_variance_report']             = 'Variance Report';
$_['text_budget_vs_actual']            = 'Budget vs Actual';
$_['text_department_analysis']         = 'Department Analysis';
$_['text_budget_utilization_report']   = 'Budget Utilization Report';

// Filters
$_['text_filter_department']           = 'Filter by Department';
$_['text_filter_type']                 = 'Filter by Type';
$_['text_filter_status']               = 'Filter by Status';
$_['text_filter_fiscal_year']          = 'Filter by Fiscal Year';

// Processing status
$_['text_processing']                  = 'Processing...';
$_['text_submitting']                  = 'Submitting...';
$_['text_approving']                   = 'Approving...';
$_['text_completed']                   = 'Completed';
$_['text_failed']                      = 'Failed';

// Additional features
$_['text_budget_templates']            = 'Budget Templates';
$_['text_copy_from_previous']          = 'Copy from Previous Year';
$_['text_auto_allocation']             = 'Auto Allocation';
$_['text_budget_alerts']               = 'Budget Alerts';
$_['text_spending_limits']             = 'Spending Limits';
?>
