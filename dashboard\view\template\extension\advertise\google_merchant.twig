{{ header }}
{{ column_left }}
<div id="content">
    <div class="page-header">
        <div class="container-fluid">
            <div class="pull-right">
                <a href="{{ text_video_tutorial_url_install }}" target="_blank" class="btn btn-info" data-toggle="tooltip" title="{{ button_video_tutorial_install }}"><i class="fa fa-video-camera"></i></a>
                <a href="{{ cancel }}" class="btn btn-default" data-toggle="tooltip" title="{{ button_cancel }}"><i class="fa fa-reply"></i></a>
            </div>
            <h1>{{ heading_title }}</h1>
            <ul class="breadcrumb">
                {% for breadcrumb in breadcrumbs %}
                <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
                {% endfor %}
            </ul>
        </div>
    </div>
    <div class="container-fluid">
        {{ steps }}
        {% if success %}
            <div class="alert alert-success alert-dismissible" role="alert">
                <button type="button" class="close" data-dismiss="alert" aria-label="{{ text_close }}"><span aria-hidden="true"><i class="fa fa-close"></i></span></button>
                <i class="fa fa-check-circle" aria-hidden="true"></i>&nbsp;{{ success }}
            </div>
        {% endif %}
        {% if error %}
            <div class="alert alert-danger alert-dismissible" role="alert">
                <button type="button" class="close" data-dismiss="alert" aria-label="{{ text_close }}"><span aria-hidden="true"><i class="fa fa-close"></i></span></button>
                <i class="fa fa-exclamation-triangle" aria-hidden="true"></i>&nbsp;{{ error }}
            </div>
        {% endif %}
        <div class="panel panel-default">
            <div class="panel-heading">
                <h3 class="panel-title"><i class="fa fa-user"></i>&nbsp;<span>{{ text_panel_heading_merchant }}</span></h3>
            </div>
            <div class="panel-body">
                <form action="{{ action }}" method="post" enctype="multipart/form-data" id="form" class="form-horizontal">
                    <div class="form-group">
                        <p class="col-md-12">{{ text_merchant_intro }}</p>
                        <div class="col-md-12">
                            <label class="control-label"><input type="radio" name="advertise_google_gmc_account_type" value="api" {% if advertise_google_gmc_account_type == "api" %} checked {% endif %} /> {{ text_new_merchant }}</label>
                        </div>
                        <div class="col-md-12">
                            <label class="control-label"><input type="radio" name="advertise_google_gmc_account_type" value="existing" {% if advertise_google_gmc_account_type == "existing" %} checked {% endif %} /> {{ text_existing_merchant }}</label>
                        </div>
                    </div>

                    <div class="alert alert-info text-left">
                        {{ text_merchant_website_claim }}
                        <hr />
                        <div class="checkbox">
                            <label><input type="checkbox" class="acknowledge" /> {{ text_acknowledge_merchant_tos }}</label>
                        </div>
                    </div>

                    <div class="pull-right">
                        <button id="proceed" disabled type="submit" class="btn btn-primary">{{ button_proceed }}</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
(function($) {
    var selector = {
        acknowledge: '.acknowledge',
        save: '#proceed',
        form: '#form'
    }

    $(document).on('change', selector.acknowledge, function() {
        $(selector.save).attr('disabled', $(selector.acknowledge + ':not(:checked)').length > 0);
    });

    $(selector.acknowledge).trigger('change');

    $(document).on('click', selector.save, function(e) {
        e.preventDefault();
        e.stopPropagation();

        $(selector.save).text('{{ text_loading }}').attr('disabled', true);

        $(selector.form).submit();
    });
})(jQuery);
</script>
{{ footer }}