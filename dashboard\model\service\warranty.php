<?php
class ModelServiceWarranty extends Model {
    public function getTotalWarranties($filter_data = array()) {
        $sql = "SELECT COUNT(*) as total 
                FROM `cod_warranty` w
                LEFT JOIN `cod_customer` c ON (w.customer_id = c.customer_id)
                LEFT JOIN `cod_product` p ON (w.product_id = p.product_id)
                WHERE 1";

        if (!empty($filter_data['filter_order_id'])) {
            $sql .= " AND w.order_id = '" . (int)$filter_data['filter_order_id'] . "'";
        }

        if (!empty($filter_data['filter_status'])) {
            $sql .= " AND w.warranty_status = '" . $this->db->escape($filter_data['filter_status']) . "'";
        }

        $query = $this->db->query($sql);
        return $query->row['total'];
    }

    public function getWarranties($filter_data = array()) {
        $sql = "SELECT w.*, 
                CONCAT(c.firstname,' ',c.lastname) as customer_name,
                p.name as product_name
                FROM `cod_warranty` w
                LEFT JOIN `cod_customer` c ON (w.customer_id = c.customer_id)
                LEFT JOIN `cod_product` p ON (w.product_id = p.product_id)
                WHERE 1";

        if (!empty($filter_data['filter_order_id'])) {
            $sql .= " AND w.order_id = '" . (int)$filter_data['filter_order_id'] . "'";
        }

        if (!empty($filter_data['filter_status'])) {
            $sql .= " AND w.warranty_status = '" . $this->db->escape($filter_data['filter_status']) . "'";
        }

        $sort_data = array('order_id','customer_name','product_name','start_date','end_date','warranty_status');
        $sort = (isset($filter_data['sort']) && in_array($filter_data['sort'], $sort_data)) ? $filter_data['sort'] : 'start_date';
        $order = (isset($filter_data['order']) && $filter_data['order'] == 'desc') ? 'DESC' : 'ASC';

        $sql .= " ORDER BY " . $sort . " " . $order;

        $start = isset($filter_data['start']) ? (int)$filter_data['start'] : 0;
        $limit = isset($filter_data['limit']) ? (int)$filter_data['limit'] : 10;

        if ($start < 0) { $start = 0; }
        if ($limit < 1) { $limit = 10; }

        $sql .= " LIMIT " . $start . "," . $limit;

        $query = $this->db->query($sql);
        return $query->rows;
    }

    public function getWarranty($warranty_id) {
        $query = $this->db->query("SELECT * FROM `cod_warranty` WHERE warranty_id = '" . (int)$warranty_id . "'");
        return $query->row;
    }

    public function addWarranty($data) {
        $this->db->query("INSERT INTO " . DB_PREFIX . "warranty SET 
            warranty_number = '" . $this->db->escape($data['warranty_number']) . "',
            customer_id = '" . (int)$data['customer_id'] . "',
            product_id = '" . (int)$data['product_id'] . "',
            order_id = '" . (int)$data['order_id'] . "',
            warranty_type = '" . $this->db->escape($data['warranty_type']) . "',
            warranty_period = '" . (int)$data['warranty_period'] . "',
            period_unit = '" . $this->db->escape($data['period_unit']) . "',
            start_date = '" . $this->db->escape($data['start_date']) . "',
            end_date = DATE_ADD('" . $this->db->escape($data['start_date']) . "', INTERVAL " . (int)$data['warranty_period'] . " " . $this->db->escape($data['period_unit']) . "),
            description = '" . $this->db->escape($data['description']) . "',
            terms_conditions = '" . $this->db->escape($data['terms_conditions']) . "',
            status = '" . (int)$data['status'] . "',
            date_added = NOW()");
        
        $warranty_id = $this->db->getLastId();
        
        // إضافة سجل في التاريخ
        $this->addWarrantyHistory($warranty_id, 'created', 'Warranty created');
        
        return $warranty_id;
    }

    public function editWarranty($warranty_id, $data) {
        $this->db->query("UPDATE `cod_warranty` SET
            order_id = '" . (int)$data['order_id'] . "',
            product_id = '" . (int)$data['product_id'] . "',
            customer_id = '" . (int)$data['customer_id'] . "',
            start_date = '" . $this->db->escape($data['start_date']) . "',
            end_date = '" . $this->db->escape($data['end_date']) . "',
            warranty_status = '" . $this->db->escape($data['warranty_status']) . "',
            notes = '" . $this->db->escape($data['notes']) . "'
            WHERE warranty_id = '" . (int)$warranty_id . "'");
    }

    public function deleteWarranty($warranty_id) {
        $this->db->query("DELETE FROM " . DB_PREFIX . "warranty WHERE warranty_id = '" . (int)$warranty_id . "'");
        $this->db->query("DELETE FROM " . DB_PREFIX . "warranty_history WHERE warranty_id = '" . (int)$warranty_id . "'");
    }

    public function updateWarranty($warranty_id, $data) {
        $this->db->query("UPDATE " . DB_PREFIX . "warranty SET 
            warranty_number = '" . $this->db->escape($data['warranty_number']) . "',
            customer_id = '" . (int)$data['customer_id'] . "',
            product_id = '" . (int)$data['product_id'] . "',
            order_id = '" . (int)$data['order_id'] . "',
            warranty_type = '" . $this->db->escape($data['warranty_type']) . "',
            warranty_period = '" . (int)$data['warranty_period'] . "',
            period_unit = '" . $this->db->escape($data['period_unit']) . "',
            start_date = '" . $this->db->escape($data['start_date']) . "',
            end_date = DATE_ADD('" . $this->db->escape($data['start_date']) . "', INTERVAL " . (int)$data['warranty_period'] . " " . $this->db->escape($data['period_unit']) . "),
            description = '" . $this->db->escape($data['description']) . "',
            terms_conditions = '" . $this->db->escape($data['terms_conditions']) . "',
            status = '" . (int)$data['status'] . "',
            date_modified = NOW()
            WHERE warranty_id = '" . (int)$warranty_id . "'");
        
        // إضافة سجل في التاريخ
        $this->addWarrantyHistory($warranty_id, 'updated', 'Warranty updated');
    }

    public function getWarrantyHistory($warranty_id) {
        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "warranty_history 
                                   WHERE warranty_id = '" . (int)$warranty_id . "' 
                                   ORDER BY date_added DESC");
        return $query->rows;
    }

    public function addWarrantyHistory($warranty_id, $action, $description) {
        $this->db->query("INSERT INTO " . DB_PREFIX . "warranty_history SET 
            warranty_id = '" . (int)$warranty_id . "',
            action = '" . $this->db->escape($action) . "',
            description = '" . $this->db->escape($description) . "',
            user_id = '" . (int)$this->user->getId() . "',
            date_added = NOW()");
    }

    public function getNextSequence() {
        $query = $this->db->query("SELECT COUNT(*) as total FROM " . DB_PREFIX . "warranty 
                                   WHERE YEAR(date_added) = '" . date('Y') . "' 
                                   AND MONTH(date_added) = '" . date('m') . "'");
        return $query->row['total'] + 1;
    }

    public function checkWarrantyValidity($warranty_id) {
        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "warranty 
                                   WHERE warranty_id = '" . (int)$warranty_id . "' 
                                   AND status = 'active' 
                                   AND end_date >= CURDATE()");
        return $query->row;
    }

    public function getExpiredWarranties() {
        $query = $this->db->query("SELECT w.*, 
                                   CONCAT(c.firstname, ' ', c.lastname) as customer_name,
                                   p.name as product_name
                                   FROM " . DB_PREFIX . "warranty w 
                                   LEFT JOIN " . DB_PREFIX . "customer c ON (w.customer_id = c.customer_id)
                                   LEFT JOIN " . DB_PREFIX . "product p ON (w.product_id = p.product_id)
                                   WHERE w.status = 'active' 
                                   AND w.end_date < CURDATE()
                                   ORDER BY w.end_date ASC");
        return $query->rows;
    }

    public function getWarrantyStatistics() {
        $stats = array();
        
        // إحصائيات الحالات
        $query = $this->db->query("SELECT status, COUNT(*) as count 
                                   FROM " . DB_PREFIX . "warranty 
                                   GROUP BY status");
        $stats['status_counts'] = $query->rows;
        
        // إحصائيات أنواع الضمان
        $query = $this->db->query("SELECT warranty_type, COUNT(*) as count 
                                   FROM " . DB_PREFIX . "warranty 
                                   GROUP BY warranty_type");
        $stats['type_counts'] = $query->rows;
        
        // إحصائيات الشهرية
        $query = $this->db->query("SELECT DATE_FORMAT(date_added, '%Y-%m') as month, COUNT(*) as count 
                                   FROM " . DB_PREFIX . "warranty 
                                   WHERE date_added >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
                                   GROUP BY DATE_FORMAT(date_added, '%Y-%m')
                                   ORDER BY date_added DESC");
        $stats['monthly_counts'] = $query->rows;
        
        // الضمانات المنتهية قريباً
        $query = $this->db->query("SELECT COUNT(*) as count 
                                   FROM " . DB_PREFIX . "warranty 
                                   WHERE status = 'active' 
                                   AND end_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 30 DAY)");
        $stats['expiring_soon'] = $query->row['count'];
        
        return $stats;
    }

    public function searchWarranty($warranty_number) {
        $query = $this->db->query("SELECT w.*, 
                                   CONCAT(c.firstname, ' ', c.lastname) as customer_name,
                                   p.name as product_name
                                   FROM " . DB_PREFIX . "warranty w 
                                   LEFT JOIN " . DB_PREFIX . "customer c ON (w.customer_id = c.customer_id)
                                   LEFT JOIN " . DB_PREFIX . "product p ON (w.product_id = p.product_id)
                                   WHERE w.warranty_number = '" . $this->db->escape($warranty_number) . "'");
        return $query->row;
    }
}
