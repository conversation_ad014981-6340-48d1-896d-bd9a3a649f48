{{ header }}{{ column_left }}

<!-- Enterprise Grade Plus CSS for Backup Management -->
<style>
.backup-container {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.backup-header {
    text-align: center;
    border-bottom: 3px solid #dc3545;
    padding-bottom: 20px;
    margin-bottom: 30px;
}

.backup-header h2 {
    color: #dc3545;
    font-weight: 700;
    font-size: 1.8rem;
    margin: 0;
}

.backup-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    background: #ffffff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.backup-table th {
    background: linear-gradient(135deg, #dc3545, #c82333);
    color: white;
    padding: 15px 12px;
    text-align: center;
    font-weight: 600;
    font-size: 0.9rem;
}

.backup-table td {
    padding: 12px;
    border-bottom: 1px solid #dee2e6;
    text-align: center;
    transition: all 0.3s ease;
}

.backup-table tbody tr:hover {
    background: #f8f9fa;
    transform: scale(1.005);
}

.backup-table tbody tr:nth-child(even) {
    background: #fafbfc;
}

.status-restored {
    color: #28a745;
    font-weight: 600;
}

.status-not-restored {
    color: #dc3545;
    font-weight: 600;
}

.btn-restore {
    background: linear-gradient(135deg, #28a745, #20c997);
    border: none;
    color: white;
    padding: 8px 16px;
    border-radius: 6px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-restore:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
}

.btn-restore:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.filter-panel {
    background: linear-gradient(135deg, #f8f9fa, #ffffff);
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.form-control {
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 8px 12px;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
    outline: none;
}

@media (max-width: 768px) {
    .backup-table {
        font-size: 0.8rem;
    }
    
    .backup-table th,
    .backup-table td {
        padding: 8px 6px;
    }
}
</style>

<div id="content">
    <div class="page-header">
        <div class="container-fluid">
            <div class="float-{{ direction == 'rtl' ? 'start' : 'end' }}">
                <a href="{{ add }}" data-bs-toggle="tooltip" title="{{ button_add }}" class="btn btn-primary">
                    <i class="fa fa-plus"></i>
                </a>
            </div>
            <h1>{{ heading_title }} - {{ text_backup_management }}</h1>
            <ul class="breadcrumb">
                {% for breadcrumb in breadcrumbs %}
                <li class="breadcrumb-item">
                    <a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a>
                </li>
                {% endfor %}
            </ul>
        </div>
    </div>

    <div class="container-fluid">
        {% if error_warning %}
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fa fa-exclamation-triangle"></i>
            {{ error_warning }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        {% endif %}

        {% if success %}
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fa fa-check-circle"></i>
            {{ success }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        {% endif %}

        <!-- Filter Panel -->
        <div class="filter-panel">
            <form method="get">
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="filter_account_code">{{ entry_account_code }}</label>
                            <input type="text" name="filter_account_code" value="{{ filter_account_code }}" 
                                   placeholder="{{ entry_account_code }}" id="filter_account_code" class="form-control">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="filter_restored">{{ entry_restored_status }}</label>
                            <select name="filter_restored" id="filter_restored" class="form-control">
                                <option value="">{{ text_all }}</option>
                                <option value="0"{% if filter_restored == '0' %} selected{% endif %}>{{ text_not_restored }}</option>
                                <option value="1"{% if filter_restored == '1' %} selected{% endif %}>{{ text_restored }}</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fa fa-search"></i> {{ button_filter }}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <input type="hidden" name="user_token" value="{{ user_token }}">
            </form>
        </div>

        <!-- Backup List -->
        <div class="backup-container">
            <div class="backup-header">
                <h2>{{ text_backup_management }}</h2>
                <p>{{ text_backup_description }}</p>
            </div>

            <div class="table-responsive">
                <table class="backup-table">
                    <thead>
                        <tr>
                            <th>{{ column_account_code }}</th>
                            <th>{{ column_account_type }}</th>
                            <th>{{ column_deleted_by }}</th>
                            <th>{{ column_deleted_date }}</th>
                            <th>{{ column_restored }}</th>
                            <th>{{ column_action }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% if backups %}
                        {% for backup in backups %}
                        <tr>
                            <td><strong>{{ backup.account_code }}</strong></td>
                            <td>{{ backup.account_type }}</td>
                            <td>{{ backup.deleted_by }}</td>
                            <td>{{ backup.deleted_date }}</td>
                            <td>
                                <span class="{% if backup.restored == text_yes %}status-restored{% else %}status-not-restored{% endif %}">
                                    {{ backup.restored }}
                                </span>
                            </td>
                            <td>
                                {% if backup.restored == text_no %}
                                <button type="button" class="btn-restore" 
                                        onclick="confirmRestore('{{ backup.restore }}')">
                                    <i class="fa fa-undo"></i> {{ button_restore }}
                                </button>
                                {% else %}
                                <button type="button" class="btn-restore" disabled>
                                    <i class="fa fa-check"></i> {{ text_restored }}
                                </button>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                        {% else %}
                        <tr>
                            <td colspan="6" class="text-center">{{ text_no_results }}</td>
                        </tr>
                        {% endif %}
                    </tbody>
                </table>
            </div>

            <div class="row">
                <div class="col-sm-6 text-start">{{ pagination }}</div>
                <div class="col-sm-6 text-end">{{ results }}</div>
            </div>
        </div>
    </div>
</div>

<script>
function confirmRestore(url) {
    if (confirm('{{ text_confirm_restore }}')) {
        location = url;
    }
}

// Initialize tooltips
document.addEventListener('DOMContentLoaded', function() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>

{{ footer }}
