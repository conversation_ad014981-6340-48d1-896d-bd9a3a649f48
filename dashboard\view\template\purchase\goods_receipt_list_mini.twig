{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="submit" form="purchase\purchase_return-form" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fas fa-save"></i></button>
        <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-light"><i class="fas fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    
    <div class="card">
      <div class="card-header"><i class="fas fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}chart-bar{% elif screen_type == 'settings' %}cog{% else %}pencil-alt{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="card-body">
        <form id="purchase\purchase_return-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-add">{{ text_add }}</label>
            <div class="col-sm-10">
              <input type="text" name="add" value="{{ add }}" placeholder="{{ text_add }}" id="input-add" class="form-control" />
              {% if error_add %}
                <div class="invalid-feedback">{{ error_add }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-approve">{{ text_approve }}</label>
            <div class="col-sm-10">
              <input type="text" name="approve" value="{{ approve }}" placeholder="{{ text_approve }}" id="input-approve" class="form-control" />
              {% if error_approve %}
                <div class="invalid-feedback">{{ error_approve }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-back">{{ text_back }}</label>
            <div class="col-sm-10">
              <input type="text" name="back" value="{{ back }}" placeholder="{{ text_back }}" id="input-back" class="form-control" />
              {% if error_back %}
                <div class="invalid-feedback">{{ error_back }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-base">{{ text_base }}</label>
            <div class="col-sm-10">
              <input type="text" name="base" value="{{ base }}" placeholder="{{ text_base }}" id="input-base" class="form-control" />
              {% if error_base %}
                <div class="invalid-feedback">{{ error_base }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-button_approve">{{ text_button_approve }}</label>
            <div class="col-sm-10">
              <input type="text" name="button_approve" value="{{ button_approve }}" placeholder="{{ text_button_approve }}" id="input-button_approve" class="form-control" />
              {% if error_button_approve %}
                <div class="invalid-feedback">{{ error_button_approve }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-button_back">{{ text_button_back }}</label>
            <div class="col-sm-10">
              <input type="text" name="button_back" value="{{ button_back }}" placeholder="{{ text_button_back }}" id="input-button_back" class="form-control" />
              {% if error_button_back %}
                <div class="invalid-feedback">{{ error_button_back }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-button_create_credit_note">{{ text_button_create_credit_note }}</label>
            <div class="col-sm-10">
              <input type="text" name="button_create_credit_note" value="{{ button_create_credit_note }}" placeholder="{{ text_button_create_credit_note }}" id="input-button_create_credit_note" class="form-control" />
              {% if error_button_create_credit_note %}
                <div class="invalid-feedback">{{ error_button_create_credit_note }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-button_print">{{ text_button_print }}</label>
            <div class="col-sm-10">
              <input type="text" name="button_print" value="{{ button_print }}" placeholder="{{ text_button_print }}" id="input-button_print" class="form-control" />
              {% if error_button_print %}
                <div class="invalid-feedback">{{ error_button_print }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-button_reject">{{ text_button_reject }}</label>
            <div class="col-sm-10">
              <input type="text" name="button_reject" value="{{ button_reject }}" placeholder="{{ text_button_reject }}" id="input-button_reject" class="form-control" />
              {% if error_button_reject %}
                <div class="invalid-feedback">{{ error_button_reject }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_comment">{{ text_column_comment }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_comment" value="{{ column_comment }}" placeholder="{{ text_column_comment }}" id="input-column_comment" class="form-control" />
              {% if error_column_comment %}
                <div class="invalid-feedback">{{ error_column_comment }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_date_added">{{ text_column_date_added }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_date_added" value="{{ column_date_added }}" placeholder="{{ text_column_date_added }}" id="input-column_date_added" class="form-control" />
              {% if error_column_date_added %}
                <div class="invalid-feedback">{{ error_column_date_added }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_product">{{ text_column_product }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_product" value="{{ column_product }}" placeholder="{{ text_column_product }}" id="input-column_product" class="form-control" />
              {% if error_column_product %}
                <div class="invalid-feedback">{{ error_column_product }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_quantity">{{ text_column_quantity }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_quantity" value="{{ column_quantity }}" placeholder="{{ text_column_quantity }}" id="input-column_quantity" class="form-control" />
              {% if error_column_quantity %}
                <div class="invalid-feedback">{{ error_column_quantity }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_status">{{ text_column_status }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_status" value="{{ column_status }}" placeholder="{{ text_column_status }}" id="input-column_status" class="form-control" />
              {% if error_column_status %}
                <div class="invalid-feedback">{{ error_column_status }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_total">{{ text_column_total }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_total" value="{{ column_total }}" placeholder="{{ text_column_total }}" id="input-column_total" class="form-control" />
              {% if error_column_total %}
                <div class="invalid-feedback">{{ error_column_total }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_unit">{{ text_column_unit }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_unit" value="{{ column_unit }}" placeholder="{{ text_column_unit }}" id="input-column_unit" class="form-control" />
              {% if error_column_unit %}
                <div class="invalid-feedback">{{ error_column_unit }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_unit_price">{{ text_column_unit_price }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_unit_price" value="{{ column_unit_price }}" placeholder="{{ text_column_unit_price }}" id="input-column_unit_price" class="form-control" />
              {% if error_column_unit_price %}
                <div class="invalid-feedback">{{ error_column_unit_price }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_user">{{ text_column_user }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_user" value="{{ column_user }}" placeholder="{{ text_column_user }}" id="input-column_user" class="form-control" />
              {% if error_column_user %}
                <div class="invalid-feedback">{{ error_column_user }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-credit_note">{{ text_credit_note }}</label>
            <div class="col-sm-10">
              <input type="text" name="credit_note" value="{{ credit_note }}" placeholder="{{ text_credit_note }}" id="input-credit_note" class="form-control" />
              {% if error_credit_note %}
                <div class="invalid-feedback">{{ error_credit_note }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-delete">{{ text_delete }}</label>
            <div class="col-sm-10">
              <input type="text" name="delete" value="{{ delete }}" placeholder="{{ text_delete }}" id="input-delete" class="form-control" />
              {% if error_delete %}
                <div class="invalid-feedback">{{ error_delete }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-direction">{{ text_direction }}</label>
            <div class="col-sm-10">
              <input type="text" name="direction" value="{{ direction }}" placeholder="{{ text_direction }}" id="input-direction" class="form-control" />
              {% if error_direction %}
                <div class="invalid-feedback">{{ error_direction }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_date_end">{{ text_filter_date_end }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_date_end" value="{{ filter_date_end }}" placeholder="{{ text_filter_date_end }}" id="input-filter_date_end" class="form-control" />
              {% if error_filter_date_end %}
                <div class="invalid-feedback">{{ error_filter_date_end }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_date_start">{{ text_filter_date_start }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_date_start" value="{{ filter_date_start }}" placeholder="{{ text_filter_date_start }}" id="input-filter_date_start" class="form-control" />
              {% if error_filter_date_start %}
                <div class="invalid-feedback">{{ error_filter_date_start }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_po_id">{{ text_filter_po_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_po_id" value="{{ filter_po_id }}" placeholder="{{ text_filter_po_id }}" id="input-filter_po_id" class="form-control" />
              {% if error_filter_po_id %}
                <div class="invalid-feedback">{{ error_filter_po_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_return_id">{{ text_filter_return_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_return_id" value="{{ filter_return_id }}" placeholder="{{ text_filter_return_id }}" id="input-filter_return_id" class="form-control" />
              {% if error_filter_return_id %}
                <div class="invalid-feedback">{{ error_filter_return_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_status">{{ text_filter_status }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_status" value="{{ filter_status }}" placeholder="{{ text_filter_status }}" id="input-filter_status" class="form-control" />
              {% if error_filter_status %}
                <div class="invalid-feedback">{{ error_filter_status }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_supplier">{{ text_filter_supplier }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_supplier" value="{{ filter_supplier }}" placeholder="{{ text_filter_supplier }}" id="input-filter_supplier" class="form-control" />
              {% if error_filter_supplier %}
                <div class="invalid-feedback">{{ error_filter_supplier }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-goods_receipt_id">{{ text_goods_receipt_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="goods_receipt_id" value="{{ goods_receipt_id }}" placeholder="{{ text_goods_receipt_id }}" id="input-goods_receipt_id" class="form-control" />
              {% if error_goods_receipt_id %}
                <div class="invalid-feedback">{{ error_goods_receipt_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="invalid-feedback">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-histories">{{ text_histories }}</label>
            <div class="col-sm-10">
              <input type="text" name="histories" value="{{ histories }}" placeholder="{{ text_histories }}" id="input-histories" class="form-control" />
              {% if error_histories %}
                <div class="invalid-feedback">{{ error_histories }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-items">{{ text_items }}</label>
            <div class="col-sm-10">
              <input type="text" name="items" value="{{ items }}" placeholder="{{ text_items }}" id="input-items" class="form-control" />
              {% if error_items %}
                <div class="invalid-feedback">{{ error_items }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-lang">{{ text_lang }}</label>
            <div class="col-sm-10">
              <input type="text" name="lang" value="{{ lang }}" placeholder="{{ text_lang }}" id="input-lang" class="form-control" />
              {% if error_lang %}
                <div class="invalid-feedback">{{ error_lang }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-notes">{{ text_notes }}</label>
            <div class="col-sm-10">
              <input type="text" name="notes" value="{{ notes }}" placeholder="{{ text_notes }}" id="input-notes" class="form-control" />
              {% if error_notes %}
                <div class="invalid-feedback">{{ error_notes }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-order">{{ text_order }}</label>
            <div class="col-sm-10">
              <input type="text" name="order" value="{{ order }}" placeholder="{{ text_order }}" id="input-order" class="form-control" />
              {% if error_order %}
                <div class="invalid-feedback">{{ error_order }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-pagination">{{ text_pagination }}</label>
            <div class="col-sm-10">
              <input type="text" name="pagination" value="{{ pagination }}" placeholder="{{ text_pagination }}" id="input-pagination" class="form-control" />
              {% if error_pagination %}
                <div class="invalid-feedback">{{ error_pagination }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-po_number">{{ text_po_number }}</label>
            <div class="col-sm-10">
              <input type="text" name="po_number" value="{{ po_number }}" placeholder="{{ text_po_number }}" id="input-po_number" class="form-control" />
              {% if error_po_number %}
                <div class="invalid-feedback">{{ error_po_number }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-print">{{ text_print }}</label>
            <div class="col-sm-10">
              <input type="text" name="print" value="{{ print }}" placeholder="{{ text_print }}" id="input-print" class="form-control" />
              {% if error_print %}
                <div class="invalid-feedback">{{ error_print }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-purchase_order_id">{{ text_purchase_order_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="purchase_order_id" value="{{ purchase_order_id }}" placeholder="{{ text_purchase_order_id }}" id="input-purchase_order_id" class="form-control" />
              {% if error_purchase_order_id %}
                <div class="invalid-feedback">{{ error_purchase_order_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-purchase_orders">{{ text_purchase_orders }}</label>
            <div class="col-sm-10">
              <input type="text" name="purchase_orders" value="{{ purchase_orders }}" placeholder="{{ text_purchase_orders }}" id="input-purchase_orders" class="form-control" />
              {% if error_purchase_orders %}
                <div class="invalid-feedback">{{ error_purchase_orders }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-receipt_number">{{ text_receipt_number }}</label>
            <div class="col-sm-10">
              <input type="text" name="receipt_number" value="{{ receipt_number }}" placeholder="{{ text_receipt_number }}" id="input-receipt_number" class="form-control" />
              {% if error_receipt_number %}
                <div class="invalid-feedback">{{ error_receipt_number }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-receipts">{{ text_receipts }}</label>
            <div class="col-sm-10">
              <input type="text" name="receipts" value="{{ receipts }}" placeholder="{{ text_receipts }}" id="input-receipts" class="form-control" />
              {% if error_receipts %}
                <div class="invalid-feedback">{{ error_receipts }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-reference">{{ text_reference }}</label>
            <div class="col-sm-10">
              <input type="text" name="reference" value="{{ reference }}" placeholder="{{ text_reference }}" id="input-reference" class="form-control" />
              {% if error_reference %}
                <div class="invalid-feedback">{{ error_reference }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-reject">{{ text_reject }}</label>
            <div class="col-sm-10">
              <input type="text" name="reject" value="{{ reject }}" placeholder="{{ text_reject }}" id="input-reject" class="form-control" />
              {% if error_reject %}
                <div class="invalid-feedback">{{ error_reject }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-results">{{ text_results }}</label>
            <div class="col-sm-10">
              <input type="text" name="results" value="{{ results }}" placeholder="{{ text_results }}" id="input-results" class="form-control" />
              {% if error_results %}
                <div class="invalid-feedback">{{ error_results }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-return">{{ text_return }}</label>
            <div class="col-sm-10">
              <input type="text" name="return" value="{{ return }}" placeholder="{{ text_return }}" id="input-return" class="form-control" />
              {% if error_return %}
                <div class="invalid-feedback">{{ error_return }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-return_date">{{ text_return_date }}</label>
            <div class="col-sm-10">
              <input type="text" name="return_date" value="{{ return_date }}" placeholder="{{ text_return_date }}" id="input-return_date" class="form-control" />
              {% if error_return_date %}
                <div class="invalid-feedback">{{ error_return_date }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-return_id">{{ text_return_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="return_id" value="{{ return_id }}" placeholder="{{ text_return_id }}" id="input-return_id" class="form-control" />
              {% if error_return_id %}
                <div class="invalid-feedback">{{ error_return_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-return_items">{{ text_return_items }}</label>
            <div class="col-sm-10">
              <input type="text" name="return_items" value="{{ return_items }}" placeholder="{{ text_return_items }}" id="input-return_items" class="form-control" />
              {% if error_return_items %}
                <div class="invalid-feedback">{{ error_return_items }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-return_type">{{ text_return_type }}</label>
            <div class="col-sm-10">
              <input type="text" name="return_type" value="{{ return_type }}" placeholder="{{ text_return_type }}" id="input-return_type" class="form-control" />
              {% if error_return_type %}
                <div class="invalid-feedback">{{ error_return_type }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-returns">{{ text_returns }}</label>
            <div class="col-sm-10">
              <input type="text" name="returns" value="{{ returns }}" placeholder="{{ text_returns }}" id="input-returns" class="form-control" />
              {% if error_returns %}
                <div class="invalid-feedback">{{ error_returns }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-save">{{ text_save }}</label>
            <div class="col-sm-10">
              <input type="text" name="save" value="{{ save }}" placeholder="{{ text_save }}" id="input-save" class="form-control" />
              {% if error_save %}
                <div class="invalid-feedback">{{ error_save }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-selected">{{ text_selected }}</label>
            <div class="col-sm-10">
              <input type="text" name="selected" value="{{ selected }}" placeholder="{{ text_selected }}" id="input-selected" class="form-control" />
              {% if error_selected %}
                <div class="invalid-feedback">{{ error_selected }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort">{{ text_sort }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort" value="{{ sort }}" placeholder="{{ text_sort }}" id="input-sort" class="form-control" />
              {% if error_sort %}
                <div class="invalid-feedback">{{ error_sort }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_created_by">{{ text_sort_created_by }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_created_by" value="{{ sort_created_by }}" placeholder="{{ text_sort_created_by }}" id="input-sort_created_by" class="form-control" />
              {% if error_sort_created_by %}
                <div class="invalid-feedback">{{ error_sort_created_by }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_po_number">{{ text_sort_po_number }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_po_number" value="{{ sort_po_number }}" placeholder="{{ text_sort_po_number }}" id="input-sort_po_number" class="form-control" />
              {% if error_sort_po_number %}
                <div class="invalid-feedback">{{ error_sort_po_number }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_return_date">{{ text_sort_return_date }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_return_date" value="{{ sort_return_date }}" placeholder="{{ text_sort_return_date }}" id="input-sort_return_date" class="form-control" />
              {% if error_sort_return_date %}
                <div class="invalid-feedback">{{ error_sort_return_date }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_return_id">{{ text_sort_return_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_return_id" value="{{ sort_return_id }}" placeholder="{{ text_sort_return_id }}" id="input-sort_return_id" class="form-control" />
              {% if error_sort_return_id %}
                <div class="invalid-feedback">{{ error_sort_return_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_status">{{ text_sort_status }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_status" value="{{ sort_status }}" placeholder="{{ text_sort_status }}" id="input-sort_status" class="form-control" />
              {% if error_sort_status %}
                <div class="invalid-feedback">{{ error_sort_status }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_supplier">{{ text_sort_supplier }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_supplier" value="{{ sort_supplier }}" placeholder="{{ text_sort_supplier }}" id="input-sort_supplier" class="form-control" />
              {% if error_sort_supplier %}
                <div class="invalid-feedback">{{ error_sort_supplier }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-status">{{ text_status }}</label>
            <div class="col-sm-10">
              <input type="text" name="status" value="{{ status }}" placeholder="{{ text_status }}" id="input-status" class="form-control" />
              {% if error_status %}
                <div class="invalid-feedback">{{ error_status }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-store_address">{{ text_store_address }}</label>
            <div class="col-sm-10">
              <input type="text" name="store_address" value="{{ store_address }}" placeholder="{{ text_store_address }}" id="input-store_address" class="form-control" />
              {% if error_store_address %}
                <div class="invalid-feedback">{{ error_store_address }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-store_email">{{ text_store_email }}</label>
            <div class="col-sm-10">
              <input type="text" name="store_email" value="{{ store_email }}" placeholder="{{ text_store_email }}" id="input-store_email" class="form-control" />
              {% if error_store_email %}
                <div class="invalid-feedback">{{ error_store_email }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-store_name">{{ text_store_name }}</label>
            <div class="col-sm-10">
              <input type="text" name="store_name" value="{{ store_name }}" placeholder="{{ text_store_name }}" id="input-store_name" class="form-control" />
              {% if error_store_name %}
                <div class="invalid-feedback">{{ error_store_name }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-store_telephone">{{ text_store_telephone }}</label>
            <div class="col-sm-10">
              <input type="text" name="store_telephone" value="{{ store_telephone }}" placeholder="{{ text_store_telephone }}" id="input-store_telephone" class="form-control" />
              {% if error_store_telephone %}
                <div class="invalid-feedback">{{ error_store_telephone }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-supplier">{{ text_supplier }}</label>
            <div class="col-sm-10">
              <input type="text" name="supplier" value="{{ supplier }}" placeholder="{{ text_supplier }}" id="input-supplier" class="form-control" />
              {% if error_supplier %}
                <div class="invalid-feedback">{{ error_supplier }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-supplier_id">{{ text_supplier_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="supplier_id" value="{{ supplier_id }}" placeholder="{{ text_supplier_id }}" id="input-supplier_id" class="form-control" />
              {% if error_supplier_id %}
                <div class="invalid-feedback">{{ error_supplier_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_approved">{{ text_text_approved }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_approved" value="{{ text_approved }}" placeholder="{{ text_text_approved }}" id="input-text_approved" class="form-control" />
              {% if error_text_approved %}
                <div class="invalid-feedback">{{ error_text_approved }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_canceled">{{ text_text_canceled }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_canceled" value="{{ text_canceled }}" placeholder="{{ text_text_canceled }}" id="input-text_canceled" class="form-control" />
              {% if error_text_canceled %}
                <div class="invalid-feedback">{{ error_text_canceled }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_completed">{{ text_text_completed }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_completed" value="{{ text_completed }}" placeholder="{{ text_text_completed }}" id="input-text_completed" class="form-control" />
              {% if error_text_completed %}
                <div class="invalid-feedback">{{ error_text_completed }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_confirm">{{ text_text_confirm }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_confirm" value="{{ text_confirm }}" placeholder="{{ text_text_confirm }}" id="input-text_confirm" class="form-control" />
              {% if error_text_confirm %}
                <div class="invalid-feedback">{{ error_text_confirm }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_date_added">{{ text_text_date_added }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_date_added" value="{{ text_date_added }}" placeholder="{{ text_text_date_added }}" id="input-text_date_added" class="form-control" />
              {% if error_text_date_added %}
                <div class="invalid-feedback">{{ error_text_date_added }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_form">{{ text_text_form }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_form" value="{{ text_form }}" placeholder="{{ text_text_form }}" id="input-text_form" class="form-control" />
              {% if error_text_form %}
                <div class="invalid-feedback">{{ error_text_form }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_history">{{ text_text_history }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_history" value="{{ text_history }}" placeholder="{{ text_text_history }}" id="input-text_history" class="form-control" />
              {% if error_text_history %}
                <div class="invalid-feedback">{{ error_text_history }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_no_results">{{ text_text_no_results }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_no_results" value="{{ text_no_results }}" placeholder="{{ text_text_no_results }}" id="input-text_no_results" class="form-control" />
              {% if error_text_no_results %}
                <div class="invalid-feedback">{{ error_text_no_results }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_note">{{ text_text_note }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_note" value="{{ text_note }}" placeholder="{{ text_text_note }}" id="input-text_note" class="form-control" />
              {% if error_text_note %}
                <div class="invalid-feedback">{{ error_text_note }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_order_number">{{ text_text_order_number }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_order_number" value="{{ text_order_number }}" placeholder="{{ text_text_order_number }}" id="input-text_order_number" class="form-control" />
              {% if error_text_order_number %}
                <div class="invalid-feedback">{{ error_text_order_number }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_pending">{{ text_text_pending }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_pending" value="{{ text_pending }}" placeholder="{{ text_text_pending }}" id="input-text_pending" class="form-control" />
              {% if error_text_pending %}
                <div class="invalid-feedback">{{ error_text_pending }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_reason">{{ text_text_reason }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_reason" value="{{ text_reason }}" placeholder="{{ text_text_reason }}" id="input-text_reason" class="form-control" />
              {% if error_text_reason %}
                <div class="invalid-feedback">{{ error_text_reason }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_receipt_number">{{ text_text_receipt_number }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_receipt_number" value="{{ text_receipt_number }}" placeholder="{{ text_text_receipt_number }}" id="input-text_receipt_number" class="form-control" />
              {% if error_text_receipt_number %}
                <div class="invalid-feedback">{{ error_text_receipt_number }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_rejected">{{ text_text_rejected }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_rejected" value="{{ text_rejected }}" placeholder="{{ text_text_rejected }}" id="input-text_rejected" class="form-control" />
              {% if error_text_rejected %}
                <div class="invalid-feedback">{{ error_text_rejected }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_return">{{ text_text_return }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_return" value="{{ text_return }}" placeholder="{{ text_text_return }}" id="input-text_return" class="form-control" />
              {% if error_text_return %}
                <div class="invalid-feedback">{{ error_text_return }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_return_details">{{ text_text_return_details }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_return_details" value="{{ text_return_details }}" placeholder="{{ text_text_return_details }}" id="input-text_return_details" class="form-control" />
              {% if error_text_return_details %}
                <div class="invalid-feedback">{{ error_text_return_details }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_return_items">{{ text_text_return_items }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_return_items" value="{{ text_return_items }}" placeholder="{{ text_text_return_items }}" id="input-text_return_items" class="form-control" />
              {% if error_text_return_items %}
                <div class="invalid-feedback">{{ error_text_return_items }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_return_number">{{ text_text_return_number }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_return_number" value="{{ text_return_number }}" placeholder="{{ text_text_return_number }}" id="input-text_return_number" class="form-control" />
              {% if error_text_return_number %}
                <div class="invalid-feedback">{{ error_text_return_number }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_status">{{ text_text_status }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_status" value="{{ text_status }}" placeholder="{{ text_text_status }}" id="input-text_status" class="form-control" />
              {% if error_text_status %}
                <div class="invalid-feedback">{{ error_text_status }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_supplier">{{ text_text_supplier }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_supplier" value="{{ text_supplier }}" placeholder="{{ text_text_supplier }}" id="input-text_supplier" class="form-control" />
              {% if error_text_supplier %}
                <div class="invalid-feedback">{{ error_text_supplier }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_total_amount">{{ text_text_total_amount }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_total_amount" value="{{ text_total_amount }}" placeholder="{{ text_text_total_amount }}" id="input-text_total_amount" class="form-control" />
              {% if error_text_total_amount %}
                <div class="invalid-feedback">{{ error_text_total_amount }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_view">{{ text_text_view }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_view" value="{{ text_view }}" placeholder="{{ text_text_view }}" id="input-text_view" class="form-control" />
              {% if error_text_view %}
                <div class="invalid-feedback">{{ error_text_view }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-title">{{ text_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="title" value="{{ title }}" placeholder="{{ text_title }}" id="input-title" class="form-control" />
              {% if error_title %}
                <div class="invalid-feedback">{{ error_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-total_amount">{{ text_total_amount }}</label>
            <div class="col-sm-10">
              <input type="text" name="total_amount" value="{{ total_amount }}" placeholder="{{ text_total_amount }}" id="input-total_amount" class="form-control" />
              {% if error_total_amount %}
                <div class="invalid-feedback">{{ error_total_amount }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="invalid-feedback">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}