{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="submit" form="form-purchase-request" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fa fa-save"></i></button>
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pencil"></i> {{ text_form }}</h3>
      </div>
      <div class="panel-body">
        <form action="{{ action }}" method="post" enctype="multipart/form-data" id="form-purchase-request" class="form-horizontal">
          <ul class="nav nav-tabs">
            <li class="active"><a href="#tab-general" data-toggle="tab">{{ tab_general }}</a></li>
            <li><a href="#tab-products" data-toggle="tab">{{ tab_products }}</a></li>
            {% if purchase_request_id %}
            <li><a href="#tab-history" data-toggle="tab">{{ tab_history }}</a></li>
            {% endif %}
          </ul>
          <div class="tab-content">
            <div class="tab-pane active" id="tab-general">
              <div class="form-group required">
                <label class="col-sm-2 control-label" for="input-reference">{{ entry_reference }}</label>
                <div class="col-sm-10">
                  <input type="text" name="reference_no" value="{{ reference_no }}" placeholder="{{ entry_reference }}" id="input-reference" class="form-control" />
                  {% if error_reference %}
                  <div class="text-danger">{{ error_reference }}</div>
                  {% endif %}
                </div>
              </div>
              <div class="form-group required">
                <label class="col-sm-2 control-label" for="input-department">{{ entry_department }}</label>
                <div class="col-sm-10">
                  <select name="department_id" id="input-department" class="form-control">
                    <option value="">{{ text_select }}</option>
                    {% for department in departments %}
                    {% if department.department_id == department_id %}
                    <option value="{{ department.department_id }}" selected="selected">{{ department.name }}</option>
                    {% else %}
                    <option value="{{ department.department_id }}">{{ department.name }}</option>
                    {% endif %}
                    {% endfor %}
                  </select>
                  {% if error_department %}
                  <div class="text-danger">{{ error_department }}</div>
                  {% endif %}
                </div>
              </div>
              <div class="form-group required">
                <label class="col-sm-2 control-label" for="input-branch">{{ entry_branch }}</label>
                <div class="col-sm-10">
                  <select name="branch_id" id="input-branch" class="form-control">
                    <option value="">{{ text_select }}</option>
                    {% for branch in branches %}
                    {% if branch.branch_id == branch_id %}
                    <option value="{{ branch.branch_id }}" selected="selected">{{ branch.name }}</option>
                    {% else %}
                    <option value="{{ branch.branch_id }}">{{ branch.name }}</option>
                    {% endif %}
                    {% endfor %}
                  </select>
                  {% if error_branch %}
                  <div class="text-danger">{{ error_branch }}</div>
                  {% endif %}
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-priority">{{ entry_priority }}</label>
                <div class="col-sm-10">
                  <select name="priority" id="input-priority" class="form-control">
                    {% if priority == '1' %}
                    <option value="1" selected="selected">{{ text_priority_low }}</option>
                    {% else %}
                    <option value="1">{{ text_priority_low }}</option>
                    {% endif %}
                    {% if priority == '2' %}
                    <option value="2" selected="selected">{{ text_priority_medium }}</option>
                    {% else %}
                    <option value="2">{{ text_priority_medium }}</option>
                    {% endif %}
                    {% if priority == '3' %}
                    <option value="3" selected="selected">{{ text_priority_high }}</option>
                    {% else %}
                    <option value="3">{{ text_priority_high }}</option>
                    {% endif %}
                    {% if priority == '4' %}
                    <option value="4" selected="selected">{{ text_priority_urgent }}</option>
                    {% else %}
                    <option value="4">{{ text_priority_urgent }}</option>
                    {% endif %}
                  </select>
                </div>
              </div>
              <div class="form-group required">
                <label class="col-sm-2 control-label" for="input-date-required">{{ entry_date_required }}</label>
                <div class="col-sm-10">
                  <div class="input-group date">
                    <input type="text" name="date_required" value="{{ date_required }}" placeholder="{{ entry_date_required }}" data-date-format="YYYY-MM-DD" id="input-date-required" class="form-control" />
                    <span class="input-group-btn">
                      <button type="button" class="btn btn-default"><i class="fa fa-calendar"></i></button>
                    </span>
                  </div>
                  {% if error_date_required %}
                  <div class="text-danger">{{ error_date_required }}</div>
                  {% endif %}
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-status">{{ entry_status }}</label>
                <div class="col-sm-10">
                  <select name="status" id="input-status" class="form-control">
                    {% if status == '1' %}
                    <option value="1" selected="selected">{{ text_status_pending }}</option>
                    {% else %}
                    <option value="1">{{ text_status_pending }}</option>
                    {% endif %}
                    {% if status == '2' %}
                    <option value="2" selected="selected">{{ text_status_approved }}</option>
                    {% else %}
                    <option value="2">{{ text_status_approved }}</option>
                    {% endif %}
                    {% if status == '3' %}
                    <option value="3" selected="selected">{{ text_status_rejected }}</option>
                    {% else %}
                    <option value="3">{{ text_status_rejected }}</option>
                    {% endif %}
                    {% if status == '4' %}
                    <option value="4" selected="selected">{{ text_status_ordered }}</option>
                    {% else %}
                    <option value="4">{{ text_status_ordered }}</option>
                    {% endif %}
                    {% if status == '5' %}
                    <option value="5" selected="selected">{{ text_status_cancelled }}</option>
                    {% else %}
                    <option value="5">{{ text_status_cancelled }}</option>
                    {% endif %}
                  </select>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-notes">{{ entry_notes }}</label>
                <div class="col-sm-10">
                  <textarea name="notes" rows="5" placeholder="{{ entry_notes }}" id="input-notes" class="form-control">{{ notes }}</textarea>
                </div>
              </div>
            </div>
            <div class="tab-pane" id="tab-products">
              <div class="table-responsive">
                <table id="products" class="table table-striped table-bordered table-hover">
                  <thead>
                    <tr>
                      <td class="text-left">{{ entry_product }}</td>
                      <td class="text-right">{{ entry_quantity }}</td>
                      <td class="text-left">{{ entry_unit }}</td>
                      <td class="text-right">{{ entry_price }}</td>
                      <td class="text-right">{{ entry_total }}</td>
                      <td></td>
                    </tr>
                  </thead>
                  <tbody>
                    {% set product_row = 0 %}
                    {% for product in products %}
                    <tr id="product-row{{ product_row }}">
                      <td class="text-left">
                        <input type="text" name="products[{{ product_row }}][name]" value="{{ product.name }}" placeholder="{{ entry_product }}" class="form-control" />
                        <input type="hidden" name="products[{{ product_row }}][product_id]" value="{{ product.product_id }}" />
                      </td>
                      <td class="text-right">
                        <input type="text" name="products[{{ product_row }}][quantity]" value="{{ product.quantity }}" placeholder="{{ entry_quantity }}" class="form-control" />
                      </td>
                      <td class="text-left">
                        <select name="products[{{ product_row }}][unit_id]" class="form-control">
                          {% for unit in units %}
                          {% if unit.unit_id == product.unit_id %}
                          <option value="{{ unit.unit_id }}" selected="selected">{{ unit.name }}</option>
                          {% else %}
                          <option value="{{ unit.unit_id }}">{{ unit.name }}</option>
                          {% endif %}
                          {% endfor %}
                        </select>
                      </td>
                      <td class="text-right">
                        <input type="text" name="products[{{ product_row }}][price]" value="{{ product.price }}" placeholder="{{ entry_price }}" class="form-control" />
                      </td>
                      <td class="text-right">
                        <input type="text" name="products[{{ product_row }}][total]" value="{{ product.total }}" placeholder="{{ entry_total }}" class="form-control" readonly />
                      </td>
                      <td class="text-left"><button type="button" onclick="$('#product-row{{ product_row }}').remove();" data-toggle="tooltip" title="{{ button_remove }}" class="btn btn-danger"><i class="fa fa-minus-circle"></i></button></td>
                    </tr>
                    {% set product_row = product_row + 1 %}
                    {% endfor %}
                  </tbody>
                  <tfoot>
                    <tr>
                      <td colspan="5"></td>
                      <td class="text-left"><button type="button" onclick="addProduct();" data-toggle="tooltip" title="{{ button_add_product }}" class="btn btn-primary"><i class="fa fa-plus-circle"></i></button></td>
                    </tr>
                  </tfoot>
                </table>
              </div>
            </div>
            {% if purchase_request_id %}
            <div class="tab-pane" id="tab-history">
              <div id="history"></div>
              <br />
              <fieldset>
                <legend>{{ text_history_add }}</legend>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-history-status">{{ entry_status }}</label>
                  <div class="col-sm-10">
                    <select name="history_status" id="input-history-status" class="form-control">
                      {% for status_option in status_options %}
                      <option value="{{ status_option.status_id }}">{{ status_option.name }}</option>
                      {% endfor %}
                    </select>
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-history-comment">{{ entry_comment }}</label>
                  <div class="col-sm-10">
                    <textarea name="history_comment" rows="8" placeholder="{{ entry_comment }}" id="input-history-comment" class="form-control"></textarea>
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label" for="input-history-notify">{{ entry_notify }}</label>
                  <div class="col-sm-10">
                    <label class="radio-inline">
                      <input type="radio" name="history_notify" value="1" /> {{ text_yes }}
                    </label>
                    <label class="radio-inline">
                      <input type="radio" name="history_notify" value="0" checked="checked" /> {{ text_no }}
                    </label>
                  </div>
                </div>
                <div class="text-right">
                  <button id="button-history" data-loading-text="{{ text_loading }}" class="btn btn-primary"><i class="fa fa-plus-circle"></i> {{ button_history_add }}</button>
                </div>
              </fieldset>
            </div>
            {% endif %}
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
<script type="text/javascript"><!--
var product_row = {{ product_row }};

function addProduct() {
  html = '<tr id="product-row' + product_row + '">';
  html += '  <td class="text-left"><input type="text" name="products[' + product_row + '][name]" value="" placeholder="{{ entry_product }}" class="form-control" /><input type="hidden" name="products[' + product_row + '][product_id]" value="" /></td>';
  html += '  <td class="text-right"><input type="text" name="products[' + product_row + '][quantity]" value="1" placeholder="{{ entry_quantity }}" class="form-control" /></td>';
  html += '  <td class="text-left"><select name="products[' + product_row + '][unit_id]" class="form-control">';
  {% for unit in units %}
  html += '    <option value="{{ unit.unit_id }}">{{ unit.name }}</option>';
  {% endfor %}
  html += '  </select></td>';
  html += '  <td class="text-right"><input type="text" name="products[' + product_row + '][price]" value="0" placeholder="{{ entry_price }}" class="form-control" /></td>';
  html += '  <td class="text-right"><input type="text" name="products[' + product_row + '][total]" value="0" placeholder="{{ entry_total }}" class="form-control" readonly /></td>';
  html += '  <td class="text-left"><button type="button" onclick="$(\'#product-row' + product_row + '\').remove();" data-toggle="tooltip" title="{{ button_remove }}" class="btn btn-danger"><i class="fa fa-minus-circle"></i></button></td>';
  html += '</tr>';

  $('#products tbody').append(html);

  $('input[name=\'products[' + product_row + '][name]\']').autocomplete({
    'source': function(request, response) {
      $.ajax({
        url: 'index.php?route=catalog/product/autocomplete&user_token={{ user_token }}&filter_name=' +  encodeURIComponent(request),
        dataType: 'json',
        success: function(json) {
          response($.map(json, function(item) {
            return {
              label: item['name'],
              value: item['product_id']
            }
          }));
        }
      });
    },
    'select': function(item) {
      $('input[name=\'products[' + product_row + '][name]\']').val(item['label']);
      $('input[name=\'products[' + product_row + '][product_id]\']').val(item['value']);
    }
  });

  product_row++;
}

$('#products tbody tr').each(function(index, element) {
  var row = index;

  $('input[name=\'products[' + row + '][name]\']').autocomplete({
    'source': function(request, response) {
      $.ajax({
        url: 'index.php?route=catalog/product/autocomplete&user_token={{ user_token }}&filter_name=' +  encodeURIComponent(request),
        dataType: 'json',
        success: function(json) {
          response($.map(json, function(item) {
            return {
              label: item['name'],
              value: item['product_id']
            }
          }));
        }
      });
    },
    'select': function(item) {
      $('input[name=\'products[' + row + '][name]\']').val(item['label']);
      $('input[name=\'products[' + row + '][product_id]\']').val(item['value']);
    }
  });
});

$('#history').delegate('.pagination a', 'click', function(e) {
  e.preventDefault();

  $('#history').load(this.href);
});

$('#history').load('index.php?route=purchase/purchase_request/history&user_token={{ user_token }}&purchase_request_id={{ purchase_request_id }}');

$('#button-history').on('click', function(e) {
  e.preventDefault();

  $.ajax({
    url: 'index.php?route=purchase/purchase_request/addHistory&user_token={{ user_token }}&purchase_request_id={{ purchase_request_id }}',
    type: 'post',
    dataType: 'json',
    data: 'status=' + encodeURIComponent($('select[name=\'history_status\']').val()) + '&comment=' + encodeURIComponent($('textarea[name=\'history_comment\']').val()) + '&notify=' + ($('input[name=\'history_notify\']:checked').val() ? 1 : 0),
    beforeSend: function() {
      $('#button-history').button('loading');
    },
    complete: function() {
      $('#button-history').button('reset');
    },
    success: function(json) {
      $('.alert-dismissible').remove();

      if (json['error']) {
        $('#tab-history').prepend('<div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> ' + json['error'] + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
      }

      if (json['success']) {
        $('#tab-history').prepend('<div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> ' + json['success'] + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');

        $('#history').load('index.php?route=purchase/purchase_request/history&user_token={{ user_token }}&purchase_request_id={{ purchase_request_id }}');

        $('textarea[name=\'history_comment\']').val('');
      }
    }
  });
});

$('.date').datetimepicker({
  language: '{{ datepicker }}',
  pickTime: false
});
//--></script>
{{ footer }}
