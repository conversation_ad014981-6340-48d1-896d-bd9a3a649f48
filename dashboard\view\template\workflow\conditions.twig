{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="submit" form="workflow\conditions-form" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fa fa-save"></i></button>
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="close" data-dismiss="alert">&times;</button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="close" data-dismiss="alert">&times;</button>
      </div>
    {{% endif %}}

    <div class="panel panel-default">
      <div class="panel-heading"><i class="fa fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}bar-chart{% elif screen_type == 'settings' %}cog{% else %}pencil{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="panel-body">
        <form id="workflow\conditions-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-add">{{ text_add }}</label>
            <div class="col-sm-10">
              <input type="text" name="add" value="{{ add }}" placeholder="{{ text_add }}" id="input-add" class="form-control" />
              {% if error_add %}
                <div class="text-danger">{{ error_add }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-available_operators">{{ text_available_operators }}</label>
            <div class="col-sm-10">
              <input type="text" name="available_operators" value="{{ available_operators }}" placeholder="{{ text_available_operators }}" id="input-available_operators" class="form-control" />
              {% if error_available_operators %}
                <div class="text-danger">{{ error_available_operators }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-back">{{ text_back }}</label>
            <div class="col-sm-10">
              <input type="text" name="back" value="{{ back }}" placeholder="{{ text_back }}" id="input-back" class="form-control" />
              {% if error_back %}
                <div class="text-danger">{{ error_back }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-builder_config">{{ text_builder_config }}</label>
            <div class="col-sm-10">
              <input type="text" name="builder_config" value="{{ builder_config }}" placeholder="{{ text_builder_config }}" id="input-builder_config" class="form-control" />
              {% if error_builder_config %}
                <div class="text-danger">{{ error_builder_config }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-condition_builder">{{ text_condition_builder }}</label>
            <div class="col-sm-10">
              <input type="text" name="condition_builder" value="{{ condition_builder }}" placeholder="{{ text_condition_builder }}" id="input-condition_builder" class="form-control" />
              {% if error_condition_builder %}
                <div class="text-danger">{{ error_condition_builder }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-condition_library">{{ text_condition_library }}</label>
            <div class="col-sm-10">
              <input type="text" name="condition_library" value="{{ condition_library }}" placeholder="{{ text_condition_library }}" id="input-condition_library" class="form-control" />
              {% if error_condition_library %}
                <div class="text-danger">{{ error_condition_library }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-condition_stats">{{ text_condition_stats }}</label>
            <div class="col-sm-10">
              <input type="text" name="condition_stats" value="{{ condition_stats }}" placeholder="{{ text_condition_stats }}" id="input-condition_stats" class="form-control" />
              {% if error_condition_stats %}
                <div class="text-danger">{{ error_condition_stats }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-condition_templates">{{ text_condition_templates }}</label>
            <div class="col-sm-10">
              <input type="text" name="condition_templates" value="{{ condition_templates }}" placeholder="{{ text_condition_templates }}" id="input-condition_templates" class="form-control" />
              {% if error_condition_templates %}
                <div class="text-danger">{{ error_condition_templates }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-conditions">{{ text_conditions }}</label>
            <div class="col-sm-10">
              <input type="text" name="conditions" value="{{ conditions }}" placeholder="{{ text_conditions }}" id="input-conditions" class="form-control" />
              {% if error_conditions %}
                <div class="text-danger">{{ error_conditions }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="text-danger">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-helper_functions">{{ text_helper_functions }}</label>
            <div class="col-sm-10">
              <input type="text" name="helper_functions" value="{{ helper_functions }}" placeholder="{{ text_helper_functions }}" id="input-helper_functions" class="form-control" />
              {% if error_helper_functions %}
                <div class="text-danger">{{ error_helper_functions }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-import_export">{{ text_import_export }}</label>
            <div class="col-sm-10">
              <input type="text" name="import_export" value="{{ import_export }}" placeholder="{{ text_import_export }}" id="input-import_export" class="form-control" />
              {% if error_import_export %}
                <div class="text-danger">{{ error_import_export }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-monitoring">{{ text_monitoring }}</label>
            <div class="col-sm-10">
              <input type="text" name="monitoring" value="{{ monitoring }}" placeholder="{{ text_monitoring }}" id="input-monitoring" class="form-control" />
              {% if error_monitoring %}
                <div class="text-danger">{{ error_monitoring }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-performance_metrics">{{ text_performance_metrics }}</label>
            <div class="col-sm-10">
              <input type="text" name="performance_metrics" value="{{ performance_metrics }}" placeholder="{{ text_performance_metrics }}" id="input-performance_metrics" class="form-control" />
              {% if error_performance_metrics %}
                <div class="text-danger">{{ error_performance_metrics }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-popular_conditions">{{ text_popular_conditions }}</label>
            <div class="col-sm-10">
              <input type="text" name="popular_conditions" value="{{ popular_conditions }}" placeholder="{{ text_popular_conditions }}" id="input-popular_conditions" class="form-control" />
              {% if error_popular_conditions %}
                <div class="text-danger">{{ error_popular_conditions }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-recent_conditions">{{ text_recent_conditions }}</label>
            <div class="col-sm-10">
              <input type="text" name="recent_conditions" value="{{ recent_conditions }}" placeholder="{{ text_recent_conditions }}" id="input-recent_conditions" class="form-control" />
              {% if error_recent_conditions %}
                <div class="text-danger">{{ error_recent_conditions }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-save_condition">{{ text_save_condition }}</label>
            <div class="col-sm-10">
              <input type="text" name="save_condition" value="{{ save_condition }}" placeholder="{{ text_save_condition }}" id="input-save_condition" class="form-control" />
              {% if error_save_condition %}
                <div class="text-danger">{{ error_save_condition }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-settings">{{ text_settings }}</label>
            <div class="col-sm-10">
              <input type="text" name="settings" value="{{ settings }}" placeholder="{{ text_settings }}" id="input-settings" class="form-control" />
              {% if error_settings %}
                <div class="text-danger">{{ error_settings }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-system_variables">{{ text_system_variables }}</label>
            <div class="col-sm-10">
              <input type="text" name="system_variables" value="{{ system_variables }}" placeholder="{{ text_system_variables }}" id="input-system_variables" class="form-control" />
              {% if error_system_variables %}
                <div class="text-danger">{{ error_system_variables }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-test_condition">{{ text_test_condition }}</label>
            <div class="col-sm-10">
              <input type="text" name="test_condition" value="{{ test_condition }}" placeholder="{{ text_test_condition }}" id="input-test_condition" class="form-control" />
              {% if error_test_condition %}
                <div class="text-danger">{{ error_test_condition }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-total">{{ text_total }}</label>
            <div class="col-sm-10">
              <input type="text" name="total" value="{{ total }}" placeholder="{{ text_total }}" id="input-total" class="form-control" />
              {% if error_total %}
                <div class="text-danger">{{ error_total }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="text-danger">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-validate_syntax">{{ text_validate_syntax }}</label>
            <div class="col-sm-10">
              <input type="text" name="validate_syntax" value="{{ validate_syntax }}" placeholder="{{ text_validate_syntax }}" id="input-validate_syntax" class="form-control" />
              {% if error_validate_syntax %}
                <div class="text-danger">{{ error_validate_syntax }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}