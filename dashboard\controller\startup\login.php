<?php
class ControllerStartupLogin extends Controller {
	public function index() {
		$route = isset($this->request->get['route']) ? $this->request->get['route'] : '';

		$ignore = array(
			'common/login',
			'common/forgotten',
			'common/reset'
		);

		// تسجيل User
		$this->registry->set('user', new Cart\User($this->registry));

		// التحقق من تسجيل الدخول
		if (!$this->user->isLogged() && !in_array($route, $ignore)) {
			return new Action('common/login');
		}

		// التحقق من التوكن فقط للمستخدمين المسجلين
		if ($this->user->isLogged()) {
			$token_ignore = array(
				'common/login',
				'common/logout',
				'common/forgotten',
				'common/reset',
				'error/not_found',
				'error/permission'
			);

			// تخطي فحص التوكن للصفحات المستثناة
			if (!in_array($route, $token_ignore)) {
				// إذا لم يكن هناك توكن في الجلسة، إنشاء واحد
				if (!isset($this->session->data['user_token'])) {
					$this->session->data['user_token'] = token(32);
				}

				// إذا لم يكن هناك توكن في الطلب، إعادة توجيه مع التوكن
				if (!isset($this->request->get['user_token'])) {
					$url = str_replace('&amp;', '&', $this->url->link($route, 'user_token=' . $this->session->data['user_token'], true));
					$this->response->redirect($url);
				}

				// التحقق من تطابق التوكن
				if ($this->request->get['user_token'] != $this->session->data['user_token']) {
					return new Action('common/login');
				}
			}
		}
	}
}
