{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="submit" form="assets\fixed_assets-form" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fas fa-save"></i></button>
        <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-light"><i class="fas fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    
    <div class="card">
      <div class="card-header"><i class="fas fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}chart-bar{% elif screen_type == 'settings' %}cog{% else %}pencil-alt{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="card-body">
        <form id="assets\fixed_assets-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-accounts">{{ text_accounts }}</label>
            <div class="col-sm-10">
              <input type="text" name="accounts" value="{{ accounts }}" placeholder="{{ text_accounts }}" id="input-accounts" class="form-control" />
              {% if error_accounts %}
                <div class="invalid-feedback">{{ error_accounts }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-add">{{ text_add }}</label>
            <div class="col-sm-10">
              <input type="text" name="add" value="{{ add }}" placeholder="{{ text_add }}" id="input-add" class="form-control" />
              {% if error_add %}
                <div class="invalid-feedback">{{ error_add }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-asset_account_id">{{ text_asset_account_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="asset_account_id" value="{{ asset_account_id }}" placeholder="{{ text_asset_account_id }}" id="input-asset_account_id" class="form-control" />
              {% if error_asset_account_id %}
                <div class="invalid-feedback">{{ error_asset_account_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-asset_code">{{ text_asset_code }}</label>
            <div class="col-sm-10">
              <input type="text" name="asset_code" value="{{ asset_code }}" placeholder="{{ text_asset_code }}" id="input-asset_code" class="form-control" />
              {% if error_asset_code %}
                <div class="invalid-feedback">{{ error_asset_code }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-assets">{{ text_assets }}</label>
            <div class="col-sm-10">
              <input type="text" name="assets" value="{{ assets }}" placeholder="{{ text_assets }}" id="input-assets" class="form-control" />
              {% if error_assets %}
                <div class="invalid-feedback">{{ error_assets }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-categories">{{ text_categories }}</label>
            <div class="col-sm-10">
              <input type="text" name="categories" value="{{ categories }}" placeholder="{{ text_categories }}" id="input-categories" class="form-control" />
              {% if error_categories %}
                <div class="invalid-feedback">{{ error_categories }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-category_id">{{ text_category_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="category_id" value="{{ category_id }}" placeholder="{{ text_category_id }}" id="input-category_id" class="form-control" />
              {% if error_category_id %}
                <div class="invalid-feedback">{{ error_category_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-delete">{{ text_delete }}</label>
            <div class="col-sm-10">
              <input type="text" name="delete" value="{{ delete }}" placeholder="{{ text_delete }}" id="input-delete" class="form-control" />
              {% if error_delete %}
                <div class="invalid-feedback">{{ error_delete }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-depreciation">{{ text_depreciation }}</label>
            <div class="col-sm-10">
              <input type="text" name="depreciation" value="{{ depreciation }}" placeholder="{{ text_depreciation }}" id="input-depreciation" class="form-control" />
              {% if error_depreciation %}
                <div class="invalid-feedback">{{ error_depreciation }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-depreciation_account_id">{{ text_depreciation_account_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="depreciation_account_id" value="{{ depreciation_account_id }}" placeholder="{{ text_depreciation_account_id }}" id="input-depreciation_account_id" class="form-control" />
              {% if error_depreciation_account_id %}
                <div class="invalid-feedback">{{ error_depreciation_account_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-depreciation_method">{{ text_depreciation_method }}</label>
            <div class="col-sm-10">
              <input type="text" name="depreciation_method" value="{{ depreciation_method }}" placeholder="{{ text_depreciation_method }}" id="input-depreciation_method" class="form-control" />
              {% if error_depreciation_method %}
                <div class="invalid-feedback">{{ error_depreciation_method }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="invalid-feedback">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-name">{{ text_name }}</label>
            <div class="col-sm-10">
              <input type="text" name="name" value="{{ name }}" placeholder="{{ text_name }}" id="input-name" class="form-control" />
              {% if error_name %}
                <div class="invalid-feedback">{{ error_name }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-purchase_cost">{{ text_purchase_cost }}</label>
            <div class="col-sm-10">
              <input type="text" name="purchase_cost" value="{{ purchase_cost }}" placeholder="{{ text_purchase_cost }}" id="input-purchase_cost" class="form-control" />
              {% if error_purchase_cost %}
                <div class="invalid-feedback">{{ error_purchase_cost }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-purchase_date">{{ text_purchase_date }}</label>
            <div class="col-sm-10">
              <input type="text" name="purchase_date" value="{{ purchase_date }}" placeholder="{{ text_purchase_date }}" id="input-purchase_date" class="form-control" />
              {% if error_purchase_date %}
                <div class="invalid-feedback">{{ error_purchase_date }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-salvage_value">{{ text_salvage_value }}</label>
            <div class="col-sm-10">
              <input type="text" name="salvage_value" value="{{ salvage_value }}" placeholder="{{ text_salvage_value }}" id="input-salvage_value" class="form-control" />
              {% if error_salvage_value %}
                <div class="invalid-feedback">{{ error_salvage_value }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-status">{{ text_status }}</label>
            <div class="col-sm-10">
              <input type="text" name="status" value="{{ status }}" placeholder="{{ text_status }}" id="input-status" class="form-control" />
              {% if error_status %}
                <div class="invalid-feedback">{{ error_status }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_form">{{ text_text_form }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_form" value="{{ text_form }}" placeholder="{{ text_text_form }}" id="input-text_form" class="form-control" />
              {% if error_text_form %}
                <div class="invalid-feedback">{{ error_text_form }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-useful_life">{{ text_useful_life }}</label>
            <div class="col-sm-10">
              <input type="text" name="useful_life" value="{{ useful_life }}" placeholder="{{ text_useful_life }}" id="input-useful_life" class="form-control" />
              {% if error_useful_life %}
                <div class="invalid-feedback">{{ error_useful_life }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}