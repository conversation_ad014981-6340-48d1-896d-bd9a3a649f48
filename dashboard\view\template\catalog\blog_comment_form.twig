{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="submit" form="catalog\blog_comment-form" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fas fa-save"></i></button>
        <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-light"><i class="fas fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    
    <div class="card">
      <div class="card-header"><i class="fas fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}chart-bar{% elif screen_type == 'settings' %}cog{% else %}pencil-alt{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="card-body">
        <form id="catalog\blog_comment-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-add">{{ text_add }}</label>
            <div class="col-sm-10">
              <input type="text" name="add" value="{{ add }}" placeholder="{{ text_add }}" id="input-add" class="form-control" />
              {% if error_add %}
                <div class="invalid-feedback">{{ error_add }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-author">{{ text_author }}</label>
            <div class="col-sm-10">
              <input type="text" name="author" value="{{ author }}" placeholder="{{ text_author }}" id="input-author" class="form-control" />
              {% if error_author %}
                <div class="invalid-feedback">{{ error_author }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-back">{{ text_back }}</label>
            <div class="col-sm-10">
              <input type="text" name="back" value="{{ back }}" placeholder="{{ text_back }}" id="input-back" class="form-control" />
              {% if error_back %}
                <div class="invalid-feedback">{{ error_back }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-can_add">{{ text_can_add }}</label>
            <div class="col-sm-10">
              <input type="text" name="can_add" value="{{ can_add }}" placeholder="{{ text_can_add }}" id="input-can_add" class="form-control" />
              {% if error_can_add %}
                <div class="invalid-feedback">{{ error_can_add }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-can_delete">{{ text_can_delete }}</label>
            <div class="col-sm-10">
              <input type="text" name="can_delete" value="{{ can_delete }}" placeholder="{{ text_can_delete }}" id="input-can_delete" class="form-control" />
              {% if error_can_delete %}
                <div class="invalid-feedback">{{ error_can_delete }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-can_edit">{{ text_can_edit }}</label>
            <div class="col-sm-10">
              <input type="text" name="can_edit" value="{{ can_edit }}" placeholder="{{ text_can_edit }}" id="input-can_edit" class="form-control" />
              {% if error_can_edit %}
                <div class="invalid-feedback">{{ error_can_edit }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-comment">{{ text_comment }}</label>
            <div class="col-sm-10">
              <input type="text" name="comment" value="{{ comment }}" placeholder="{{ text_comment }}" id="input-comment" class="form-control" />
              {% if error_comment %}
                <div class="invalid-feedback">{{ error_comment }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-comments">{{ text_comments }}</label>
            <div class="col-sm-10">
              <input type="text" name="comments" value="{{ comments }}" placeholder="{{ text_comments }}" id="input-comments" class="form-control" />
              {% if error_comments %}
                <div class="invalid-feedback">{{ error_comments }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-content">{{ text_content }}</label>
            <div class="col-sm-10">
              <input type="text" name="content" value="{{ content }}" placeholder="{{ text_content }}" id="input-content" class="form-control" />
              {% if error_content %}
                <div class="invalid-feedback">{{ error_content }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-delete">{{ text_delete }}</label>
            <div class="col-sm-10">
              <input type="text" name="delete" value="{{ delete }}" placeholder="{{ text_delete }}" id="input-delete" class="form-control" />
              {% if error_delete %}
                <div class="invalid-feedback">{{ error_delete }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-edit">{{ text_edit }}</label>
            <div class="col-sm-10">
              <input type="text" name="edit" value="{{ edit }}" placeholder="{{ text_edit }}" id="input-edit" class="form-control" />
              {% if error_edit %}
                <div class="invalid-feedback">{{ error_edit }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-email">{{ text_email }}</label>
            <div class="col-sm-10">
              <input type="text" name="email" value="{{ email }}" placeholder="{{ text_email }}" id="input-email" class="form-control" />
              {% if error_email %}
                <div class="invalid-feedback">{{ error_email }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_author">{{ text_error_author }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_author" value="{{ error_author }}" placeholder="{{ text_error_author }}" id="input-error_author" class="form-control" />
              {% if error_error_author %}
                <div class="invalid-feedback">{{ error_error_author }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_content">{{ text_error_content }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_content" value="{{ error_content }}" placeholder="{{ text_error_content }}" id="input-error_content" class="form-control" />
              {% if error_error_content %}
                <div class="invalid-feedback">{{ error_error_content }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_email">{{ text_error_email }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_email" value="{{ error_email }}" placeholder="{{ text_error_email }}" id="input-error_email" class="form-control" />
              {% if error_error_email %}
                <div class="invalid-feedback">{{ error_error_email }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_author">{{ text_filter_author }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_author" value="{{ filter_author }}" placeholder="{{ text_filter_author }}" id="input-filter_author" class="form-control" />
              {% if error_filter_author %}
                <div class="invalid-feedback">{{ error_filter_author }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_date_added">{{ text_filter_date_added }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_date_added" value="{{ filter_date_added }}" placeholder="{{ text_filter_date_added }}" id="input-filter_date_added" class="form-control" />
              {% if error_filter_date_added %}
                <div class="invalid-feedback">{{ error_filter_date_added }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_post_id">{{ text_filter_post_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_post_id" value="{{ filter_post_id }}" placeholder="{{ text_filter_post_id }}" id="input-filter_post_id" class="form-control" />
              {% if error_filter_post_id %}
                <div class="invalid-feedback">{{ error_filter_post_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_status">{{ text_filter_status }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_status" value="{{ filter_status }}" placeholder="{{ text_filter_status }}" id="input-filter_status" class="form-control" />
              {% if error_filter_status %}
                <div class="invalid-feedback">{{ error_filter_status }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="invalid-feedback">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-ip">{{ text_ip }}</label>
            <div class="col-sm-10">
              <input type="text" name="ip" value="{{ ip }}" placeholder="{{ text_ip }}" id="input-ip" class="form-control" />
              {% if error_ip %}
                <div class="invalid-feedback">{{ error_ip }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-notify">{{ text_notify }}</label>
            <div class="col-sm-10">
              <input type="text" name="notify" value="{{ notify }}" placeholder="{{ text_notify }}" id="input-notify" class="form-control" />
              {% if error_notify %}
                <div class="invalid-feedback">{{ error_notify }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-order">{{ text_order }}</label>
            <div class="col-sm-10">
              <input type="text" name="order" value="{{ order }}" placeholder="{{ text_order }}" id="input-order" class="form-control" />
              {% if error_order %}
                <div class="invalid-feedback">{{ error_order }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-pagination">{{ text_pagination }}</label>
            <div class="col-sm-10">
              <input type="text" name="pagination" value="{{ pagination }}" placeholder="{{ text_pagination }}" id="input-pagination" class="form-control" />
              {% if error_pagination %}
                <div class="invalid-feedback">{{ error_pagination }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-parent_comment">{{ text_parent_comment }}</label>
            <div class="col-sm-10">
              <input type="text" name="parent_comment" value="{{ parent_comment }}" placeholder="{{ text_parent_comment }}" id="input-parent_comment" class="form-control" />
              {% if error_parent_comment %}
                <div class="invalid-feedback">{{ error_parent_comment }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-parent_id">{{ text_parent_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="parent_id" value="{{ parent_id }}" placeholder="{{ text_parent_id }}" id="input-parent_id" class="form-control" />
              {% if error_parent_id %}
                <div class="invalid-feedback">{{ error_parent_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-post_id">{{ text_post_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="post_id" value="{{ post_id }}" placeholder="{{ text_post_id }}" id="input-post_id" class="form-control" />
              {% if error_post_id %}
                <div class="invalid-feedback">{{ error_post_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-post_link">{{ text_post_link }}</label>
            <div class="col-sm-10">
              <input type="text" name="post_link" value="{{ post_link }}" placeholder="{{ text_post_link }}" id="input-post_link" class="form-control" />
              {% if error_post_link %}
                <div class="invalid-feedback">{{ error_post_link }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-post_title">{{ text_post_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="post_title" value="{{ post_title }}" placeholder="{{ text_post_title }}" id="input-post_title" class="form-control" />
              {% if error_post_title %}
                <div class="invalid-feedback">{{ error_post_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-posts">{{ text_posts }}</label>
            <div class="col-sm-10">
              <input type="text" name="posts" value="{{ posts }}" placeholder="{{ text_posts }}" id="input-posts" class="form-control" />
              {% if error_posts %}
                <div class="invalid-feedback">{{ error_posts }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-replies">{{ text_replies }}</label>
            <div class="col-sm-10">
              <input type="text" name="replies" value="{{ replies }}" placeholder="{{ text_replies }}" id="input-replies" class="form-control" />
              {% if error_replies %}
                <div class="invalid-feedback">{{ error_replies }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-reply">{{ text_reply }}</label>
            <div class="col-sm-10">
              <input type="text" name="reply" value="{{ reply }}" placeholder="{{ text_reply }}" id="input-reply" class="form-control" />
              {% if error_reply %}
                <div class="invalid-feedback">{{ error_reply }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-results">{{ text_results }}</label>
            <div class="col-sm-10">
              <input type="text" name="results" value="{{ results }}" placeholder="{{ text_results }}" id="input-results" class="form-control" />
              {% if error_results %}
                <div class="invalid-feedback">{{ error_results }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-selected">{{ text_selected }}</label>
            <div class="col-sm-10">
              <input type="text" name="selected" value="{{ selected }}" placeholder="{{ text_selected }}" id="input-selected" class="form-control" />
              {% if error_selected %}
                <div class="invalid-feedback">{{ error_selected }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort">{{ text_sort }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort" value="{{ sort }}" placeholder="{{ text_sort }}" id="input-sort" class="form-control" />
              {% if error_sort %}
                <div class="invalid-feedback">{{ error_sort }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_author">{{ text_sort_author }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_author" value="{{ sort_author }}" placeholder="{{ text_sort_author }}" id="input-sort_author" class="form-control" />
              {% if error_sort_author %}
                <div class="invalid-feedback">{{ error_sort_author }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_date_added">{{ text_sort_date_added }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_date_added" value="{{ sort_date_added }}" placeholder="{{ text_sort_date_added }}" id="input-sort_date_added" class="form-control" />
              {% if error_sort_date_added %}
                <div class="invalid-feedback">{{ error_sort_date_added }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_post">{{ text_sort_post }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_post" value="{{ sort_post }}" placeholder="{{ text_sort_post }}" id="input-sort_post" class="form-control" />
              {% if error_sort_post %}
                <div class="invalid-feedback">{{ error_sort_post }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_status">{{ text_sort_status }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_status" value="{{ sort_status }}" placeholder="{{ text_sort_status }}" id="input-sort_status" class="form-control" />
              {% if error_sort_status %}
                <div class="invalid-feedback">{{ error_sort_status }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-status">{{ text_status }}</label>
            <div class="col-sm-10">
              <input type="text" name="status" value="{{ status }}" placeholder="{{ text_status }}" id="input-status" class="form-control" />
              {% if error_status %}
                <div class="invalid-feedback">{{ error_status }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_form">{{ text_text_form }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_form" value="{{ text_form }}" placeholder="{{ text_text_form }}" id="input-text_form" class="form-control" />
              {% if error_text_form %}
                <div class="invalid-feedback">{{ error_text_form }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="invalid-feedback">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-website">{{ text_website }}</label>
            <div class="col-sm-10">
              <input type="text" name="website" value="{{ website }}" placeholder="{{ text_website }}" id="input-website" class="form-control" />
              {% if error_website %}
                <div class="invalid-feedback">{{ error_website }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}