{{ header }}{{ column_left }}
<div id="content" class="container-fluid">
    <div class="page-header">
        <div class="container-fluid">
            <h1>{{ heading_title }}</h1>
        </div>
    </div>
    {% if error_warning %}
    <div class="alert alert-danger"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}</div>
    {% endif %}
    <form action="{{ action }}" method="post" enctype="multipart/form-data" id="form-installment">
        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label class="control-label" for="input-name">{{ entry_name }}</label>
                    <input type="text" name="name" value="{{ name }}" placeholder="{{ entry_name }}" id="input-name" class="form-control" />
                    {% if error_name %}
                    <div class="text-danger">{{ error_name }}</div>
                    {% endif %}
                </div>
                <div class="form-group">
                    <label class="control-label" for="input-description">{{ entry_description }}</label>
                    <textarea name="description" placeholder="{{ entry_description }}" id="input-description" class="form-control">{{ description }}</textarea>
                </div>
                <div class="form-group">
                    <label class="control-label" for="input-total_amount">{{ entry_total_amount }}</label>
                    <input type="text" name="total_amount" value="{{ total_amount }}" placeholder="{{ entry_total_amount }}" id="input-total_amount" class="form-control" />
                    {% if error_total_amount %}
                    <div class="text-danger">{{ error_total_amount }}</div>
                    {% endif %}
                </div>
                <div class="form-group">
                    <label class="control-label" for="input-number_of_installments">{{ entry_number_of_installments }}</label>
                    <input type="number" name="number_of_installments" value="{{ number_of_installments }}" placeholder="{{ entry_number_of_installments }}" id="input-number_of_installments" class="form-control" />
                    {% if error_number_of_installments %}
                    <div class="text-danger">{{ error_number_of_installments }}</div>
                    {% endif %}
                </div>
                <div class="form-group">
                    <label class="control-label" for="input-interest_rate">{{ entry_interest_rate }}</label>
                    <input type="text" name="interest_rate" value="{{ interest_rate }}" placeholder="{{ entry_interest_rate }}" id="input-interest_rate" class="form-control" />
                    {% if error_interest_rate %}
                    <div class="text-danger">{{ error_interest_rate }}</div>
                    {% endif %}
                </div>
                <div class="form-group">
                    <label class="control-label" for="input-status">{{ entry_status }}</label>
                    <select name="status" id="input-status" class="form-control">
                        <option value="1" {% if status == 1 %}selected{% endif %}>Enabled</option>
                        <option value="0" {% if status == 0 %}selected{% endif %}>Disabled</option>
                    </select>
                </div>
                <div class="buttons">
                    <div class="pull-right">
                        <button type="submit" class="btn btn-primary">{{ button_save }}</button>
                        <a href="{{ cancel }}" class="btn btn-default">{{ button_cancel }}</a>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
{{ footer }}
