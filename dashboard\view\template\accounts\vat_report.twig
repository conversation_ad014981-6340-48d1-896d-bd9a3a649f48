{{ header }}{{ column_left }}

<!-- Enterprise Grade Plus CSS for VAT Report -->
<style>
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --danger-color: #e74c3c;
    --warning-color: #f39c12;
    --info-color: #17a2b8;
    --vat-color: #6f42c1;
    --light-bg: #f8f9fa;
    --border-color: #dee2e6;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

.vat-report-container {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: var(--shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.vat-report-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--vat-color), var(--primary-color), var(--secondary-color));
}

.vat-report-header {
    text-align: center;
    border-bottom: 3px solid var(--vat-color);
    padding-bottom: 20px;
    margin-bottom: 30px;
    position: relative;
}

.vat-report-header h2 {
    color: var(--vat-color);
    font-weight: 700;
    font-size: 1.8rem;
    margin: 0;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.vat-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.vat-card {
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    text-align: center;
    box-shadow: var(--shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.vat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0,0,0,0.1);
}

.vat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
}

.vat-card.input-vat::before { background: var(--info-color); }
.vat-card.output-vat::before { background: var(--success-color); }
.vat-card.net-vat::before { background: var(--vat-color); }
.vat-card.refund-vat::before { background: var(--warning-color); }

.vat-card h4 {
    color: var(--primary-color);
    margin-bottom: 10px;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.vat-card .amount {
    font-size: 1.4rem;
    font-weight: 700;
    font-family: 'Courier New', monospace;
    margin-bottom: 5px;
}

.vat-card .description {
    font-size: 0.8rem;
    color: #6c757d;
    font-weight: 500;
}

.vat-card.input-vat .amount { color: var(--info-color); }
.vat-card.output-vat .amount { color: var(--success-color); }
.vat-card.net-vat .amount { color: var(--vat-color); }
.vat-card.refund-vat .amount { color: var(--warning-color); }

.vat-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    background: #ffffff;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.vat-table th {
    background: linear-gradient(135deg, var(--vat-color), #5a32a3);
    color: white;
    padding: 15px 12px;
    text-align: center;
    font-weight: 600;
    font-size: 0.9rem;
    border-bottom: 2px solid var(--border-color);
}

.vat-table td {
    padding: 12px;
    border-bottom: 1px solid var(--border-color);
    text-align: center;
    transition: var(--transition);
}

.vat-table tbody tr:hover {
    background: var(--light-bg);
    transform: scale(1.005);
}

.vat-table tbody tr:nth-child(even) {
    background: #fafbfc;
}

.amount-cell {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    text-align: {{ direction == 'rtl' ? 'right' : 'left' }};
}

.amount-input { 
    color: var(--info-color); 
    font-weight: 600;
}

.amount-output { 
    color: var(--success-color); 
    font-weight: 600;
}

.amount-net { 
    color: var(--vat-color); 
    font-weight: 600;
}

/* Filter Panel */
.filter-panel {
    background: linear-gradient(135deg, #f8f9fa, #ffffff);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: var(--shadow);
}

.filter-panel h4 {
    color: var(--vat-color);
    margin-bottom: 15px;
    font-weight: 600;
}

.form-group {
    margin-bottom: 15px;
}

.form-label {
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 5px;
    display: block;
}

.form-control {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 8px 12px;
    transition: var(--transition);
    width: 100%;
}

.form-control:focus {
    border-color: var(--vat-color);
    box-shadow: 0 0 0 0.2rem rgba(111, 66, 193, 0.25);
    outline: none;
}

/* ETA Integration */
.eta-status {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.eta-status.submitted {
    background: rgba(39, 174, 96, 0.1);
    color: var(--success-color);
}

.eta-status.pending {
    background: rgba(243, 156, 18, 0.1);
    color: var(--warning-color);
}

.eta-status.draft {
    background: rgba(23, 162, 184, 0.1);
    color: var(--info-color);
}

/* VAT Rate Badges */
.vat-rate-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-align: center;
}

.vat-rate-14 {
    background: rgba(111, 66, 193, 0.1);
    color: var(--vat-color);
}

.vat-rate-10 {
    background: rgba(23, 162, 184, 0.1);
    color: var(--info-color);
}

.vat-rate-5 {
    background: rgba(243, 156, 18, 0.1);
    color: var(--warning-color);
}

.vat-rate-0 {
    background: rgba(39, 174, 96, 0.1);
    color: var(--success-color);
}

/* RTL Support */
[dir="rtl"] .vat-table {
    direction: rtl;
}

[dir="rtl"] .amount-cell {
    text-align: right;
}

/* Print Styles */
@media print {
    .vat-report-container {
        box-shadow: none;
        border: 1px solid #000;
    }
    
    .vat-table th {
        background: #f0f0f0 !important;
        color: #000 !important;
    }
    
    .btn, .card-header, .filter-panel {
        display: none !important;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .vat-table {
        font-size: 0.8rem;
    }
    
    .vat-table th,
    .vat-table td {
        padding: 8px 6px;
    }
    
    .vat-summary {
        grid-template-columns: repeat(2, 1fr);
    }
}
</style>

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-{{ direction == 'rtl' ? 'start' : 'end' }}">
        <div class="btn-group" role="group" aria-label="{{ text_actions }}">
          <button type="button" class="btn btn-success btn-lg" onclick="generateVATReport()" 
                  data-bs-toggle="tooltip" title="{{ button_generate_report }}">
            <i class="fas fa-file-invoice me-2"></i> {{ button_generate }}
          </button>
          <div class="btn-group" role="group">
            <button type="button" class="btn btn-info dropdown-toggle" data-bs-toggle="dropdown" 
                    aria-expanded="false" data-bs-toggle="tooltip" title="{{ text_export_options }}">
              <i class="fas fa-download me-2"></i> {{ text_export }}
            </button>
            <ul class="dropdown-menu">
              <li><a class="dropdown-item" href="#" onclick="exportVATReport('excel')">
                <i class="fas fa-file-excel text-success me-2"></i> Excel
              </a></li>
              <li><a class="dropdown-item" href="#" onclick="exportVATReport('pdf')">
                <i class="fas fa-file-pdf text-danger me-2"></i> PDF
              </a></li>
              <li><hr class="dropdown-divider"></li>
              <li><a class="dropdown-item" href="#" onclick="printVATReport()">
                <i class="fas fa-print text-secondary me-2"></i> {{ text_print }}
              </a></li>
            </ul>
          </div>
          <button type="button" class="btn btn-warning" onclick="submitToETA()"
                  data-bs-toggle="tooltip" title="{{ button_submit_eta }}">
            <i class="fas fa-paper-plane me-2"></i> {{ text_submit_eta }}
          </button>
          <button type="button" class="btn btn-outline-primary" onclick="advancedAnalysis()"
                  data-bs-toggle="tooltip" title="{{ button_advanced_analysis }}">
            <i class="fas fa-chart-line"></i>
          </button>
        </div>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li class="breadcrumb-item"><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>

  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible fade show">
      <i class="fas fa-exclamation-triangle"></i>
      {{ error_warning }}
      <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    {% endif %}

    {% if success %}
    <div class="alert alert-success alert-dismissible fade show">
      <i class="fas fa-check-circle"></i>
      {{ success }}
      <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    {% endif %}

    <!-- Filter Panel -->
    <div class="filter-panel">
      <h4>{{ text_vat_report_filters }}</h4>
      <form id="vat-report-filter-form" method="post">
        <div class="row">
          <div class="col-md-3">
            <div class="form-group">
              <label for="date_start" class="form-label">{{ entry_date_start }}</label>
              <input type="date" name="date_start" id="date_start" value="{{ date_start }}" class="form-control" required>
            </div>
          </div>
          <div class="col-md-3">
            <div class="form-group">
              <label for="date_end" class="form-label">{{ entry_date_end }}</label>
              <input type="date" name="date_end" id="date_end" value="{{ date_end }}" class="form-control" required>
            </div>
          </div>
          <div class="col-md-3">
            <div class="form-group">
              <label for="vat_rate" class="form-label">{{ entry_vat_rate }}</label>
              <select name="vat_rate" id="vat_rate" class="form-control">
                <option value="">{{ text_all_rates }}</option>
                {% for rate, description in vat_rates %}
                <option value="{{ rate }}"{% if rate == vat_rate %} selected{% endif %}>{{ description }}</option>
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="col-md-3">
            <div class="form-group">
              <label class="form-label">&nbsp;</label>
              <div class="d-grid">
                <button type="submit" class="btn btn-primary">
                  <i class="fas fa-search me-2"></i>{{ button_generate }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>

    <!-- VAT Report Content -->
    {% if vat_report_data %}
    <!-- VAT Summary Cards -->
    <div class="vat-summary">
      <div class="vat-card input-vat">
        <h4>{{ text_input_vat }}</h4>
        <div class="amount">{{ vat_report_data.total_input_vat_formatted }}</div>
        <div class="description">{{ text_input_vat_description }}</div>
      </div>

      <div class="vat-card output-vat">
        <h4>{{ text_output_vat }}</h4>
        <div class="amount">{{ vat_report_data.total_output_vat_formatted }}</div>
        <div class="description">{{ text_output_vat_description }}</div>
      </div>

      <div class="vat-card net-vat">
        <h4>{{ text_net_vat }}</h4>
        <div class="amount">{{ vat_report_data.net_vat_formatted }}</div>
        <div class="description">{{ text_net_vat_description }}</div>
      </div>

      <div class="vat-card refund-vat">
        <h4>{{ vat_report_data.net_vat > 0 ? text_vat_payable : text_vat_refund }}</h4>
        <div class="amount">{{ vat_report_data.net_vat_abs_formatted }}</div>
        <div class="description">{{ vat_report_data.net_vat > 0 ? text_vat_payable_description : text_vat_refund_description }}</div>
      </div>
    </div>

    <!-- VAT Report Details -->
    <div class="vat-report-container">
      <div class="vat-report-header">
        <h2>{{ text_vat_report_details }}</h2>
        <p><strong>{{ text_period }}:</strong> {{ date_start }} {{ text_to }} {{ date_end }}</p>
        {% if vat_rate %}
        <p><strong>{{ text_vat_rate }}:</strong> {{ vat_rate }}%</p>
        {% endif %}
        <div class="eta-status {{ vat_report_data.eta_status }}">
          <i class="fas fa-{{ vat_report_data.eta_status == 'submitted' ? 'check-circle' : vat_report_data.eta_status == 'pending' ? 'clock' : 'edit' }}"></i>
          {{ vat_report_data.eta_status_text }}
        </div>
      </div>

      <!-- Input VAT Details Table -->
      <h4>{{ text_input_vat_details }}</h4>
      <div class="table-responsive">
        <table class="vat-table" id="input-vat-table">
          <thead>
            <tr>
              <th>{{ column_transaction_date }}</th>
              <th>{{ column_supplier_name }}</th>
              <th>{{ column_invoice_number }}</th>
              <th>{{ column_net_amount }}</th>
              <th>{{ column_vat_rate }}</th>
              <th>{{ column_vat_amount }}</th>
              <th>{{ column_total_amount }}</th>
            </tr>
          </thead>
          <tbody>
            {% for transaction in vat_report_data.input_vat_details %}
            <tr>
              <td>{{ transaction.transaction_date }}</td>
              <td>{{ transaction.supplier_name }}</td>
              <td>{{ transaction.invoice_number }}</td>
              <td class="amount-cell">{{ transaction.net_amount_formatted }}</td>
              <td>
                <span class="vat-rate-badge vat-rate-{{ transaction.vat_rate|replace({'.': ''}) }}">
                  {{ transaction.vat_rate }}%
                </span>
              </td>
              <td class="amount-cell amount-input">{{ transaction.vat_amount_formatted }}</td>
              <td class="amount-cell">{{ transaction.total_amount_formatted }}</td>
            </tr>
            {% endfor %}
          </tbody>
          <tfoot>
            <tr style="background: #f8f9fa; font-weight: bold;">
              <td colspan="3"><strong>{{ text_total_input_vat }}</strong></td>
              <td class="amount-cell">{{ vat_report_data.total_input_net_formatted }}</td>
              <td>-</td>
              <td class="amount-cell amount-input">{{ vat_report_data.total_input_vat_formatted }}</td>
              <td class="amount-cell">{{ vat_report_data.total_input_total_formatted }}</td>
            </tr>
          </tfoot>
        </table>
      </div>

      <!-- Output VAT Details Table -->
      <h4>{{ text_output_vat_details }}</h4>
      <div class="table-responsive">
        <table class="vat-table" id="output-vat-table">
          <thead>
            <tr>
              <th>{{ column_transaction_date }}</th>
              <th>{{ column_customer_name }}</th>
              <th>{{ column_invoice_number }}</th>
              <th>{{ column_net_amount }}</th>
              <th>{{ column_vat_rate }}</th>
              <th>{{ column_vat_amount }}</th>
              <th>{{ column_total_amount }}</th>
            </tr>
          </thead>
          <tbody>
            {% for transaction in vat_report_data.output_vat_details %}
            <tr>
              <td>{{ transaction.transaction_date }}</td>
              <td>{{ transaction.customer_name }}</td>
              <td>{{ transaction.invoice_number }}</td>
              <td class="amount-cell">{{ transaction.net_amount_formatted }}</td>
              <td>
                <span class="vat-rate-badge vat-rate-{{ transaction.vat_rate|replace({'.': ''}) }}">
                  {{ transaction.vat_rate }}%
                </span>
              </td>
              <td class="amount-cell amount-output">{{ transaction.vat_amount_formatted }}</td>
              <td class="amount-cell">{{ transaction.total_amount_formatted }}</td>
            </tr>
            {% endfor %}
          </tbody>
          <tfoot>
            <tr style="background: #f8f9fa; font-weight: bold;">
              <td colspan="3"><strong>{{ text_total_output_vat }}</strong></td>
              <td class="amount-cell">{{ vat_report_data.total_output_net_formatted }}</td>
              <td>-</td>
              <td class="amount-cell amount-output">{{ vat_report_data.total_output_vat_formatted }}</td>
              <td class="amount-cell">{{ vat_report_data.total_output_total_formatted }}</td>
            </tr>
          </tfoot>
        </table>
      </div>

      <!-- VAT Summary Calculation -->
      <div class="vat-report-container">
        <div class="vat-report-header">
          <h2>{{ text_vat_calculation_summary }}</h2>
        </div>

        <div class="table-responsive">
          <table class="vat-table">
            <tbody>
              <tr>
                <td><strong>{{ text_total_output_vat }}</strong></td>
                <td class="amount-cell amount-output">{{ vat_report_data.total_output_vat_formatted }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_total_input_vat }}</strong></td>
                <td class="amount-cell amount-input">{{ vat_report_data.total_input_vat_formatted }}</td>
              </tr>
              <tr style="background: {{ vat_report_data.net_vat > 0 ? '#ffebee' : '#e8f5e8' }};">
                <td><strong>{{ text_net_vat_position }}</strong></td>
                <td class="amount-cell amount-net">{{ vat_report_data.net_vat_formatted }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_previous_period_balance }}</strong></td>
                <td class="amount-cell">{{ vat_report_data.previous_balance_formatted }}</td>
              </tr>
              <tr style="background: {{ vat_report_data.final_balance > 0 ? '#ffebee' : '#e8f5e8' }};">
                <td><strong>{{ vat_report_data.final_balance > 0 ? text_amount_payable : text_amount_refundable }}</strong></td>
                <td class="amount-cell amount-net">{{ vat_report_data.final_balance_formatted }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- VAT Rate Analysis -->
      {% if vat_report_data.rate_analysis %}
      <div class="vat-report-container">
        <div class="vat-report-header">
          <h2>{{ text_vat_rate_analysis }}</h2>
        </div>

        <div class="table-responsive">
          <table class="vat-table" id="rate-analysis-table">
            <thead>
              <tr>
                <th>{{ column_vat_rate }}</th>
                <th>{{ column_input_net }}</th>
                <th>{{ column_input_vat }}</th>
                <th>{{ column_output_net }}</th>
                <th>{{ column_output_vat }}</th>
                <th>{{ column_net_position }}</th>
              </tr>
            </thead>
            <tbody>
              {% for rate_data in vat_report_data.rate_analysis %}
              <tr>
                <td>
                  <span class="vat-rate-badge vat-rate-{{ rate_data.rate|replace({'.': ''}) }}">
                    {{ rate_data.rate }}%
                  </span>
                </td>
                <td class="amount-cell">{{ rate_data.input_net_formatted }}</td>
                <td class="amount-cell amount-input">{{ rate_data.input_vat_formatted }}</td>
                <td class="amount-cell">{{ rate_data.output_net_formatted }}</td>
                <td class="amount-cell amount-output">{{ rate_data.output_vat_formatted }}</td>
                <td class="amount-cell amount-net">{{ rate_data.net_position_formatted }}</td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
      {% endif %}
    </div>
    {% else %}
    <div class="alert alert-info">
      <i class="fas fa-info-circle me-2"></i>
      {{ text_no_vat_report }}
    </div>
    {% endif %}
  </div>
</div>

<script>
// Enterprise Grade Plus JavaScript for VAT Report
class VATReportManager {
    constructor() {
        this.initializeTooltips();
        this.initializeDataTables();
        this.initializeKeyboardShortcuts();
        this.initializeFormValidation();
        this.initializeETAIntegration();
    }

    initializeTooltips() {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }

    initializeDataTables() {
        const tables = ['input-vat-table', 'output-vat-table', 'rate-analysis-table'];

        tables.forEach(tableId => {
            const table = document.getElementById(tableId);
            if (table && typeof $ !== 'undefined' && $.fn.DataTable) {
                $(table).DataTable({
                    responsive: true,
                    pageLength: 25,
                    order: [[0, 'desc']], // Sort by date desc
                    columnDefs: [
                        { targets: [3, 5, 6], className: 'text-end' },
                        { targets: [4], className: 'text-center' }
                    ],
                    language: {
                        url: '{{ direction == "rtl" ? "/view/javascript/jquery/datatables-ar.json" : "/view/javascript/jquery/datatables-en.json" }}'
                    }
                });
            }
        });
    }

    initializeKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case 'g':
                        e.preventDefault();
                        this.generateVATReport();
                        break;
                    case 'e':
                        e.preventDefault();
                        this.showExportMenu();
                        break;
                    case 'p':
                        e.preventDefault();
                        this.printVATReport();
                        break;
                    case 's':
                        e.preventDefault();
                        this.submitToETA();
                        break;
                    case 'a':
                        e.preventDefault();
                        this.advancedAnalysis();
                        break;
                }
            }
        });
    }

    initializeFormValidation() {
        const form = document.getElementById('vat-report-filter-form');
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.validateAndSubmitForm();
            });
        }
    }

    initializeETAIntegration() {
        // Initialize ETA integration features
        this.checkETAConnection();
    }

    validateAndSubmitForm() {
        const form = document.getElementById('vat-report-filter-form');
        const formData = new FormData(form);

        // Validate date range
        const startDate = new Date(formData.get('date_start'));
        const endDate = new Date(formData.get('date_end'));

        if (!formData.get('date_start')) {
            this.showAlert('{{ error_date_start }}', 'danger');
            return;
        }

        if (!formData.get('date_end')) {
            this.showAlert('{{ error_date_end }}', 'danger');
            return;
        }

        if (startDate >= endDate) {
            this.showAlert('{{ error_invalid_date_range }}', 'danger');
            return;
        }

        // Submit form
        this.showLoadingState(true);
        form.submit();
    }

    generateVATReport() {
        const form = document.getElementById('vat-report-filter-form');
        if (form) {
            this.validateAndSubmitForm();
        }
    }

    exportVATReport(format) {
        const dateStart = document.getElementById('date_start').value;
        const dateEnd = document.getElementById('date_end').value;
        const vatRate = document.getElementById('vat_rate').value;

        if (!dateStart || !dateEnd) {
            this.showAlert('{{ error_complete_form }}', 'danger');
            return;
        }

        const params = new URLSearchParams({
            date_start: dateStart,
            date_end: dateEnd,
            vat_rate: vatRate,
            format: format
        });

        this.showAlert('{{ text_exporting }}...', 'info');
        window.open('{{ url_link('accounts/vat_report', 'export') }}&' + params.toString(), '_blank');
    }

    printVATReport() {
        window.print();
    }

    submitToETA() {
        const dateStart = document.getElementById('date_start').value;
        const dateEnd = document.getElementById('date_end').value;

        if (!dateStart || !dateEnd) {
            this.showAlert('{{ error_complete_form }}', 'danger');
            return;
        }

        if (!confirm('{{ confirm_submit_eta }}')) {
            return;
        }

        this.showAlert('{{ text_submitting_eta }}...', 'info');

        fetch('{{ url_link('accounts/vat_report', 'submit_eta') }}', {
            method: 'POST',
            body: JSON.stringify({
                date_start: dateStart,
                date_end: dateEnd
            }),
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.showAlert('{{ success_eta_submission }}', 'success');
                setTimeout(() => location.reload(), 2000);
            } else {
                this.showAlert(data.error || '{{ error_eta_submission }}', 'danger');
            }
        })
        .catch(error => {
            this.showAlert('{{ error_eta_submission }}: ' + error.message, 'danger');
        });
    }

    advancedAnalysis() {
        const dateStart = document.getElementById('date_start').value;
        const dateEnd = document.getElementById('date_end').value;

        if (!dateStart || !dateEnd) {
            this.showAlert('{{ error_complete_form }}', 'danger');
            return;
        }

        this.showAlert('{{ text_generating_analysis }}...', 'info');

        fetch('{{ url_link('accounts/vat_report', 'advanced_analysis') }}', {
            method: 'POST',
            body: JSON.stringify({
                date_start: dateStart,
                date_end: dateEnd
            }),
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.showAlert('{{ success_analysis_generated }}', 'success');
                // Redirect to advanced analysis view
                window.location.href = '{{ url_link('accounts/vat_report', 'view_analysis') }}';
            } else {
                this.showAlert(data.error || '{{ error_analysis_generation }}', 'danger');
            }
        })
        .catch(error => {
            this.showAlert('{{ error_analysis_generation }}: ' + error.message, 'danger');
        });
    }

    checkETAConnection() {
        // Check ETA connection status
        fetch('{{ url_link('accounts/vat_report', 'check_eta_status') }}', {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            this.updateETAStatus(data.status);
        })
        .catch(error => {
            console.log('ETA status check failed:', error);
        });
    }

    updateETAStatus(status) {
        const statusElements = document.querySelectorAll('.eta-status');
        statusElements.forEach(element => {
            element.className = `eta-status ${status}`;
            element.innerHTML = `<i class="fas fa-${status === 'connected' ? 'check-circle' : 'exclamation-triangle'}"></i> ${status === 'connected' ? '{{ text_eta_connected }}' : '{{ text_eta_disconnected }}'}`;
        });
    }

    showExportMenu() {
        const exportButton = document.querySelector('.dropdown-toggle');
        if (exportButton) {
            exportButton.click();
        }
    }

    showLoadingState(show) {
        const buttons = document.querySelectorAll('.btn');
        buttons.forEach(btn => {
            if (show) {
                btn.disabled = true;
                if (btn.querySelector('i')) {
                    btn.querySelector('i').className = 'fas fa-spinner fa-spin me-2';
                }
            } else {
                btn.disabled = false;
                location.reload();
            }
        });
    }

    showAlert(message, type) {
        const alertContainer = document.createElement('div');
        alertContainer.className = `alert alert-${type} alert-dismissible fade show`;
        alertContainer.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'}"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        const container = document.querySelector('#content .container-fluid');
        container.insertBefore(alertContainer, container.firstChild);

        setTimeout(() => {
            alertContainer.remove();
        }, 5000);
    }
}

// Global functions for backward compatibility
function generateVATReport() {
    vatReportManager.generateVATReport();
}

function exportVATReport(format) {
    vatReportManager.exportVATReport(format);
}

function printVATReport() {
    vatReportManager.printVATReport();
}

function submitToETA() {
    vatReportManager.submitToETA();
}

function advancedAnalysis() {
    vatReportManager.advancedAnalysis();
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.vatReportManager = new VATReportManager();
});
</script>

{{ footer }}
