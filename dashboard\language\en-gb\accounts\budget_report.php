<?php
// Heading
$_['heading_title']                    = 'Budget Report';

// Text
$_['text_success']                     = 'Success: Budget Report has been generated successfully!';
$_['text_list']                        = 'Budget Report List';
$_['text_form']                        = 'Budget Report Form';
$_['text_view']                        = 'View Budget Report';
$_['text_generate']                    = 'Generate Budget Report';
$_['text_export']                      = 'Export Budget Report';
$_['text_compare']                     = 'Compare Budget Report';
$_['text_print']                       = 'Print Budget Report';
$_['text_budget_report']               = 'Budget Report';
$_['text_period']                      = 'Period';
$_['text_from']                        = 'From';
$_['text_to']                          = 'To';
$_['text_loading']                     = 'Loading...';
$_['text_processing']                  = 'Processing...';
$_['text_success_generate']            = 'Budget Report generated successfully!';
$_['text_success_export']              = 'Budget Report exported successfully!';
$_['text_success_compare']             = 'Comparison completed successfully!';

// Budget Components
$_['text_budget']                      = 'Budget';
$_['text_actual']                      = 'Actual';
$_['text_variance']                    = 'Variance';
$_['text_variance_percentage']         = 'Variance %';
$_['text_budget_amount']               = 'Budget Amount';
$_['text_actual_amount']               = 'Actual Amount';
$_['text_variance_amount']             = 'Variance Amount';
$_['text_favorable_variance']          = 'Favorable Variance';
$_['text_unfavorable_variance']        = 'Unfavorable Variance';
$_['text_no_variance']                 = 'No Variance';

// Budget Categories
$_['text_revenue_budget']              = 'Revenue Budget';
$_['text_expense_budget']              = 'Expense Budget';
$_['text_capital_budget']              = 'Capital Budget';
$_['text_operating_budget']            = 'Operating Budget';
$_['text_cash_budget']                 = 'Cash Budget';
$_['text_master_budget']               = 'Master Budget';

// Account Types
$_['text_revenue']                     = 'Revenue';
$_['text_expenses']                    = 'Expenses';
$_['text_cost_of_sales']               = 'Cost of Sales';
$_['text_operating_expenses']          = 'Operating Expenses';
$_['text_administrative_expenses']     = 'Administrative Expenses';
$_['text_selling_expenses']            = 'Selling & Marketing Expenses';
$_['text_financial_expenses']          = 'Financial Expenses';
$_['text_other_income']                = 'Other Income';
$_['text_other_expenses']              = 'Other Expenses';

// Variance Analysis
$_['text_variance_analysis']           = 'Variance Analysis';
$_['text_significant_variances']       = 'Significant Variances';
$_['text_variance_threshold']          = 'Variance Threshold';
$_['text_variance_reason']             = 'Variance Reason';
$_['text_corrective_action']           = 'Corrective Action';
$_['text_responsible_person']          = 'Responsible Person';
$_['text_variance_trend']              = 'Variance Trend';
$_['text_variance_impact']             = 'Variance Impact';

// Performance Indicators
$_['text_budget_performance']          = 'Budget Performance';
$_['text_achievement_rate']            = 'Achievement Rate';
$_['text_efficiency_ratio']            = 'Efficiency Ratio';
$_['text_budget_utilization']          = 'Budget Utilization';
$_['text_cost_control']                = 'Cost Control';
$_['text_revenue_performance']         = 'Revenue Performance';
$_['text_expense_control']             = 'Expense Control';

// Column
$_['column_account']                   = 'Account';
$_['column_account_name']              = 'Account Name';
$_['column_budget']                    = 'Budget';
$_['column_actual']                    = 'Actual';
$_['column_variance']                  = 'Variance';
$_['column_variance_percentage']       = 'Variance %';
$_['column_department']                = 'Department';
$_['column_category']                  = 'Category';
$_['column_period']                    = 'Period';

// Entry
$_['entry_date_start']                 = 'Period Start Date';
$_['entry_date_end']                   = 'Period End Date';
$_['entry_budget_id']                  = 'Budget';
$_['entry_department_id']              = 'Department';
$_['entry_account_group']              = 'Account Group';
$_['entry_branch_id']                  = 'Branch';
$_['entry_include_zero_variances']     = 'Include Zero Variances';
$_['entry_variance_threshold']         = 'Variance Threshold (%)';
$_['entry_show_percentages']           = 'Show Percentages';
$_['entry_export_format']              = 'Export Format';

// Button
$_['button_generate']                  = 'Generate';
$_['button_export']                    = 'Export';
$_['button_compare']                   = 'Compare';
$_['button_print']                     = 'Print';
$_['button_filter']                    = 'Filter';
$_['button_reset']                     = 'Reset';
$_['button_close']                     = 'Close';
$_['button_analyze']                   = 'Analyze';
$_['button_forecast']                  = 'Forecast';

// Tab
$_['tab_general']                      = 'General';
$_['tab_filters']                      = 'Filters';
$_['tab_options']                      = 'Options';
$_['tab_summary']                      = 'Summary';
$_['tab_details']                      = 'Details';
$_['tab_variance']                     = 'Variances';
$_['tab_analysis']                     = 'Analysis';

// Help
$_['help_date_start']                  = 'Select the start date for budget report period';
$_['help_date_end']                    = 'Select the end date for budget report period';
$_['help_budget_id']                   = 'Select specific budget or leave empty for all budgets';
$_['help_variance_threshold']          = 'Set percentage threshold to show only significant variances';
$_['help_department']                  = 'Select specific department or all departments';

// Error
$_['error_warning']                    = 'Warning: Please check the form carefully for errors!';
$_['error_permission']                 = 'Warning: You do not have permission to access Budget Report!';
$_['error_date_start']                 = 'Period start date is required!';
$_['error_date_end']                   = 'Period end date is required!';
$_['error_date_range']                 = 'Start date must be before end date!';
$_['error_no_data']                    = 'No data for selected period!';
$_['error_export']                     = 'Error exporting data!';
$_['error_form']                       = 'Form data error!';
$_['error_missing_data']               = 'Missing data!';
$_['error_budget_not_found']           = 'Budget not found!';

// Export Formats
$_['text_excel']                       = 'Excel';
$_['text_pdf']                         = 'PDF';
$_['text_csv']                         = 'CSV';

// Print Options
$_['text_print_title']                 = 'Budget Report';
$_['print_title']                      = 'Print Budget Report';
$_['text_print_date']                  = 'Print Date';
$_['text_print_user']                  = 'Printed By';

// Status Messages
$_['text_generating']                  = 'Generating budget report...';
$_['text_exporting']                   = 'Exporting data...';
$_['text_comparing']                   = 'Performing comparison...';
$_['text_completed']                   = 'Completed successfully!';

// Summary Information
$_['text_total_budget']                = 'Total Budget';
$_['text_total_actual']                = 'Total Actual';
$_['text_total_variance']              = 'Total Variance';
$_['text_budget_summary']              = 'Budget Summary';
$_['text_performance_summary']         = 'Performance Summary';

// Forecasting
$_['text_forecast']                    = 'Forecast';
$_['text_projected_amount']            = 'Projected Amount';
$_['text_year_end_forecast']           = 'Year-end Forecast';
$_['text_trend_analysis']              = 'Trend Analysis';
$_['text_seasonal_adjustment']         = 'Seasonal Adjustment';

// Budget Control
$_['text_budget_control']              = 'Budget Control';
$_['text_budget_approval']             = 'Budget Approval';
$_['text_budget_revision']             = 'Budget Revision';
$_['text_budget_monitoring']           = 'Budget Monitoring';
$_['text_budget_alert']                = 'Budget Alert';

// Egyptian Financial System
$_['text_egyptian_budget_standards']   = 'Compliant with Egyptian Budget Standards';
$_['text_eas_compliant']               = 'Compliant with Egyptian Accounting Standards';
$_['text_eta_ready']                   = 'Ready for ETA Integration';
$_['text_egyptian_gaap']               = 'According to Egyptian Generally Accepted Accounting Principles';

// Additional Options
$_['text_show_details']                = 'Show Details';
$_['text_show_summary']                = 'Show Summary';
$_['text_group_by_department']         = 'Group by Department';
$_['text_group_by_category']           = 'Group by Category';
$_['text_group_by_month']              = 'Group by Month';

// Additional Fields
$_['text_total']                       = 'Total';
$_['text_no_results']                  = 'No results found';
$_['text_account']                     = 'Account';

// Controller language variables
$_['log_unauthorized_access_budget_report'] = 'Unauthorized access attempt to budget report';
$_['log_view_budget_report_screen'] = 'View budget report screen';
$_['log_unauthorized_generate_budget_report'] = 'Unauthorized budget report generation attempt';
$_['log_generate_budget_report_period'] = 'Generate budget report for period';
$_['text_to'] = 'to';

// Additional variables for template
$_['text_actions'] = 'Actions';
$_['text_generate_report'] = 'Generate Report';
$_['text_export_options'] = 'Export Options';
$_['text_variance_analysis'] = 'Variance Analysis';
$_['text_report_filters'] = 'Report Filters';
$_['text_all_budgets'] = 'All Budgets';
$_['text_all_years'] = 'All Years';
$_['text_all_departments'] = 'All Departments';
$_['text_all_cost_centers'] = 'All Cost Centers';
$_['text_total_budgeted'] = 'Total Budgeted';
$_['text_total_actual'] = 'Total Actual';
$_['text_total_variance'] = 'Total Variance';
$_['text_budget_count'] = 'Budget Count';
$_['text_planned_amount'] = 'Planned Amount';
$_['text_actual_amount'] = 'Actual Amount';
$_['text_budgets'] = 'Budgets';
$_['text_budget_report_details'] = 'Budget Report Details';
$_['column_budget_name'] = 'Budget Name';
$_['column_budget_code'] = 'Budget Code';
$_['column_budget_year'] = 'Budget Year';
$_['column_department'] = 'Department';
$_['column_budgeted_amount'] = 'Budgeted Amount';
$_['column_actual_amount'] = 'Actual Amount';
$_['column_variance'] = 'Variance';
$_['column_variance_percentage'] = 'Variance %';
$_['column_status'] = 'Status';
$_['column_action'] = 'Action';
$_['text_view_details'] = 'View Details';
$_['text_variance_details'] = 'Variance Details';
$_['text_budget_vs_actual_chart'] = 'Budget vs Actual Chart';
$_['text_variance_chart'] = 'Variance Chart';
$_['text_no_data'] = 'No data to display';
$_['text_report_generated'] = 'Report generated successfully';
$_['error_generate_report'] = 'Error generating report';
$_['text_exporting'] = 'Exporting';
$_['text_budgeted_amount'] = 'Budgeted Amount';
$_['text_variance_percentage'] = 'Variance Percentage';
$_['text_loading'] = 'Loading';
$_['button_generate'] = 'Generate Report';
$_['button_filter'] = 'Filter';
$_['entry_budget'] = 'Budget';
$_['entry_budget_year'] = 'Budget Year';
$_['entry_department'] = 'Department';
$_['entry_cost_center'] = 'Cost Center';
?>
