<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/type/date.proto

namespace Google\Type;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Represents a whole or partial calendar date, e.g. a birthday. The time of day
 * and time zone are either specified elsewhere or are not significant. The date
 * is relative to the Proleptic Gregorian Calendar. This can represent:
 * * A full date, with non-zero year, month and day values
 * * A month and day value, with a zero year, e.g. an anniversary
 * * A year on its own, with zero month and day values
 * * A year and month value, with a zero day, e.g. a credit card expiration date
 * Related types are [google.type.TimeOfDay][google.type.TimeOfDay] and `google.protobuf.Timestamp`.
 *
 * Generated from protobuf message <code>google.type.Date</code>
 */
class Date extends \Google\Protobuf\Internal\Message
{
    /**
     * Year of date. Must be from 1 to 9999, or 0 if specifying a date without
     * a year.
     *
     * Generated from protobuf field <code>int32 year = 1;</code>
     */
    private $year = 0;
    /**
     * Month of year. Must be from 1 to 12, or 0 if specifying a year without a
     * month and day.
     *
     * Generated from protobuf field <code>int32 month = 2;</code>
     */
    private $month = 0;
    /**
     * Day of month. Must be from 1 to 31 and valid for the year and month, or 0
     * if specifying a year by itself or a year and month where the day is not
     * significant.
     *
     * Generated from protobuf field <code>int32 day = 3;</code>
     */
    private $day = 0;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $year
     *           Year of date. Must be from 1 to 9999, or 0 if specifying a date without
     *           a year.
     *     @type int $month
     *           Month of year. Must be from 1 to 12, or 0 if specifying a year without a
     *           month and day.
     *     @type int $day
     *           Day of month. Must be from 1 to 31 and valid for the year and month, or 0
     *           if specifying a year by itself or a year and month where the day is not
     *           significant.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Type\Date::initOnce();
        parent::__construct($data);
    }

    /**
     * Year of date. Must be from 1 to 9999, or 0 if specifying a date without
     * a year.
     *
     * Generated from protobuf field <code>int32 year = 1;</code>
     * @return int
     */
    public function getYear()
    {
        return $this->year;
    }

    /**
     * Year of date. Must be from 1 to 9999, or 0 if specifying a date without
     * a year.
     *
     * Generated from protobuf field <code>int32 year = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setYear($var)
    {
        GPBUtil::checkInt32($var);
        $this->year = $var;

        return $this;
    }

    /**
     * Month of year. Must be from 1 to 12, or 0 if specifying a year without a
     * month and day.
     *
     * Generated from protobuf field <code>int32 month = 2;</code>
     * @return int
     */
    public function getMonth()
    {
        return $this->month;
    }

    /**
     * Month of year. Must be from 1 to 12, or 0 if specifying a year without a
     * month and day.
     *
     * Generated from protobuf field <code>int32 month = 2;</code>
     * @param int $var
     * @return $this
     */
    public function setMonth($var)
    {
        GPBUtil::checkInt32($var);
        $this->month = $var;

        return $this;
    }

    /**
     * Day of month. Must be from 1 to 31 and valid for the year and month, or 0
     * if specifying a year by itself or a year and month where the day is not
     * significant.
     *
     * Generated from protobuf field <code>int32 day = 3;</code>
     * @return int
     */
    public function getDay()
    {
        return $this->day;
    }

    /**
     * Day of month. Must be from 1 to 31 and valid for the year and month, or 0
     * if specifying a year by itself or a year and month where the day is not
     * significant.
     *
     * Generated from protobuf field <code>int32 day = 3;</code>
     * @param int $var
     * @return $this
     */
    public function setDay($var)
    {
        GPBUtil::checkInt32($var);
        $this->day = $var;

        return $this;
    }

}

