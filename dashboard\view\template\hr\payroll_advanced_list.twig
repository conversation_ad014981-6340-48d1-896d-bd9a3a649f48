{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="submit" form="hr\payroll_advanced-form" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fas fa-save"></i></button>
        <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-light"><i class="fas fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    
    <div class="card">
      <div class="card-header"><i class="fas fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}chart-bar{% elif screen_type == 'settings' %}cog{% else %}pencil-alt{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="card-body">
        <form id="hr\payroll_advanced-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-approve_url">{{ text_approve_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="approve_url" value="{{ approve_url }}" placeholder="{{ text_approve_url }}" id="input-approve_url" class="form-control" />
              {% if error_approve_url %}
                <div class="invalid-feedback">{{ error_approve_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-back">{{ text_back }}</label>
            <div class="col-sm-10">
              <input type="text" name="back" value="{{ back }}" placeholder="{{ text_back }}" id="input-back" class="form-control" />
              {% if error_back %}
                <div class="invalid-feedback">{{ error_back }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-charts_data">{{ text_charts_data }}</label>
            <div class="col-sm-10">
              <input type="text" name="charts_data" value="{{ charts_data }}" placeholder="{{ text_charts_data }}" id="input-charts_data" class="form-control" />
              {% if error_charts_data %}
                <div class="invalid-feedback">{{ error_charts_data }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-create">{{ text_create }}</label>
            <div class="col-sm-10">
              <input type="text" name="create" value="{{ create }}" placeholder="{{ text_create }}" id="input-create" class="form-control" />
              {% if error_create %}
                <div class="invalid-feedback">{{ error_create }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-cycle">{{ text_cycle }}</label>
            <div class="col-sm-10">
              <input type="text" name="cycle" value="{{ cycle }}" placeholder="{{ text_cycle }}" id="input-cycle" class="form-control" />
              {% if error_cycle %}
                <div class="invalid-feedback">{{ error_cycle }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-cycle_name">{{ text_cycle_name }}</label>
            <div class="col-sm-10">
              <input type="text" name="cycle_name" value="{{ cycle_name }}" placeholder="{{ text_cycle_name }}" id="input-cycle_name" class="form-control" />
              {% if error_cycle_name %}
                <div class="invalid-feedback">{{ error_cycle_name }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-cycles">{{ text_cycles }}</label>
            <div class="col-sm-10">
              <input type="text" name="cycles" value="{{ cycles }}" placeholder="{{ text_cycles }}" id="input-cycles" class="form-control" />
              {% if error_cycles %}
                <div class="invalid-feedback">{{ error_cycles }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-dashboard">{{ text_dashboard }}</label>
            <div class="col-sm-10">
              <input type="text" name="dashboard" value="{{ dashboard }}" placeholder="{{ text_dashboard }}" id="input-dashboard" class="form-control" />
              {% if error_dashboard %}
                <div class="invalid-feedback">{{ error_dashboard }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-disburse_url">{{ text_disburse_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="disburse_url" value="{{ disburse_url }}" placeholder="{{ text_disburse_url }}" id="input-disburse_url" class="form-control" />
              {% if error_disburse_url %}
                <div class="invalid-feedback">{{ error_disburse_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-export_url">{{ text_export_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="export_url" value="{{ export_url }}" placeholder="{{ text_export_url }}" id="input-export_url" class="form-control" />
              {% if error_export_url %}
                <div class="invalid-feedback">{{ error_export_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_date_end">{{ text_filter_date_end }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_date_end" value="{{ filter_date_end }}" placeholder="{{ text_filter_date_end }}" id="input-filter_date_end" class="form-control" />
              {% if error_filter_date_end %}
                <div class="invalid-feedback">{{ error_filter_date_end }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_date_start">{{ text_filter_date_start }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_date_start" value="{{ filter_date_start }}" placeholder="{{ text_filter_date_start }}" id="input-filter_date_start" class="form-control" />
              {% if error_filter_date_start %}
                <div class="invalid-feedback">{{ error_filter_date_start }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_status">{{ text_filter_status }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_status" value="{{ filter_status }}" placeholder="{{ text_filter_status }}" id="input-filter_status" class="form-control" />
              {% if error_filter_status %}
                <div class="invalid-feedback">{{ error_filter_status }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="invalid-feedback">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-order">{{ text_order }}</label>
            <div class="col-sm-10">
              <input type="text" name="order" value="{{ order }}" placeholder="{{ text_order }}" id="input-order" class="form-control" />
              {% if error_order %}
                <div class="invalid-feedback">{{ error_order }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-pay_date">{{ text_pay_date }}</label>
            <div class="col-sm-10">
              <input type="text" name="pay_date" value="{{ pay_date }}" placeholder="{{ text_pay_date }}" id="input-pay_date" class="form-control" />
              {% if error_pay_date %}
                <div class="invalid-feedback">{{ error_pay_date }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-period_end">{{ text_period_end }}</label>
            <div class="col-sm-10">
              <input type="text" name="period_end" value="{{ period_end }}" placeholder="{{ text_period_end }}" id="input-period_end" class="form-control" />
              {% if error_period_end %}
                <div class="invalid-feedback">{{ error_period_end }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-period_start">{{ text_period_start }}</label>
            <div class="col-sm-10">
              <input type="text" name="period_start" value="{{ period_start }}" placeholder="{{ text_period_start }}" id="input-period_start" class="form-control" />
              {% if error_period_start %}
                <div class="invalid-feedback">{{ error_period_start }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-records">{{ text_records }}</label>
            <div class="col-sm-10">
              <input type="text" name="records" value="{{ records }}" placeholder="{{ text_records }}" id="input-records" class="form-control" />
              {% if error_records %}
                <div class="invalid-feedback">{{ error_records }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-selected">{{ text_selected }}</label>
            <div class="col-sm-10">
              <input type="text" name="selected" value="{{ selected }}" placeholder="{{ text_selected }}" id="input-selected" class="form-control" />
              {% if error_selected %}
                <div class="invalid-feedback">{{ error_selected }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort">{{ text_sort }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort" value="{{ sort }}" placeholder="{{ text_sort }}" id="input-sort" class="form-control" />
              {% if error_sort %}
                <div class="invalid-feedback">{{ error_sort }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-statistics">{{ text_statistics }}</label>
            <div class="col-sm-10">
              <input type="text" name="statistics" value="{{ statistics }}" placeholder="{{ text_statistics }}" id="input-statistics" class="form-control" />
              {% if error_statistics %}
                <div class="invalid-feedback">{{ error_statistics }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_form">{{ text_text_form }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_form" value="{{ text_form }}" placeholder="{{ text_text_form }}" id="input-text_form" class="form-control" />
              {% if error_text_form %}
                <div class="invalid-feedback">{{ error_text_form }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-totals">{{ text_totals }}</label>
            <div class="col-sm-10">
              <input type="text" name="totals" value="{{ totals }}" placeholder="{{ text_totals }}" id="input-totals" class="form-control" />
              {% if error_totals %}
                <div class="invalid-feedback">{{ error_totals }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="invalid-feedback">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}