{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="submit" form="form-main" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fa fa-save"></i></button>
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pencil"></i> {{ text_form }}</h3>
      </div>
      <div class="panel-body">
        <form action="{{ action }}" method="post" enctype="multipart/form-data" id="form-main" class="form-horizontal">

          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-escalation_after_days">{{ entry_escalation_after_days }}</label>
            <div class="col-sm-10">
              <input type="text" name="escalation_after_days" value="{{ escalation_after_days }}" placeholder="{{ entry_escalation_after_days }}" id="input-escalation_after_days" class="form-control" />
              {% if error_escalation_after_days %}
              <div class="text-danger">{{ error_escalation_after_days }}</div>
              {% endif %}
            </div>
          </div>

          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-name">{{ entry_name }}</label>
            <div class="col-sm-10">
              <input type="text" name="name" value="{{ name }}" placeholder="{{ entry_name }}" id="input-name" class="form-control" />
              {% if error_name %}
              <div class="text-danger">{{ error_name }}</div>
              {% endif %}
            </div>
          </div>

          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-workflow_type">{{ entry_workflow_type }}</label>
            <div class="col-sm-10">
              <input type="text" name="workflow_type" value="{{ workflow_type }}" placeholder="{{ entry_workflow_type }}" id="input-workflow_type" class="form-control" />
              {% if error_workflow_type %}
              <div class="text-danger">{{ error_workflow_type }}</div>
              {% endif %}
            </div>
          </div>

          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-notify_creator">{{ entry_notify_creator }}</label>
            <div class="col-sm-10">
              <input type="text" name="notify_creator" value="{{ notify_creator }}" placeholder="{{ entry_notify_creator }}" id="input-notify_creator" class="form-control" />
              {% if error_notify_creator %}
              <div class="text-danger">{{ error_notify_creator }}</div>
              {% endif %}
            </div>
          </div>

          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-department">{{ entry_department }}</label>
            <div class="col-sm-10">
              <input type="text" name="department" value="{{ department }}" placeholder="{{ entry_department }}" id="input-department" class="form-control" />
              {% if error_department %}
              <div class="text-danger">{{ error_department }}</div>
              {% endif %}
            </div>
          </div>

          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-description">{{ entry_description }}</label>
            <div class="col-sm-10">
              <input type="text" name="description" value="{{ description }}" placeholder="{{ entry_description }}" id="input-description" class="form-control" />
              {% if error_description %}
              <div class="text-danger">{{ error_description }}</div>
              {% endif %}
            </div>
          </div>

          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-status">{{ entry_status }}</label>
            <div class="col-sm-10">
              <input type="text" name="status" value="{{ status }}" placeholder="{{ entry_status }}" id="input-status" class="form-control" />
              {% if error_status %}
              <div class="text-danger">{{ error_status }}</div>
              {% endif %}
            </div>
          </div>

          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-escalation_enabled">{{ entry_escalation_enabled }}</label>
            <div class="col-sm-10">
              <input type="text" name="escalation_enabled" value="{{ escalation_enabled }}" placeholder="{{ entry_escalation_enabled }}" id="input-escalation_enabled" class="form-control" />
              {% if error_escalation_enabled %}
              <div class="text-danger">{{ error_escalation_enabled }}</div>
              {% endif %}
            </div>
          </div>

        </form>
      </div>
    </div>
  </div>
</div>
{{ footer }}
