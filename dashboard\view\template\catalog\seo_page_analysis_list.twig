{{ header }}
{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="button" id="button-analyze" data-toggle="tooltip" title="{{ button_analyze }}" class="btn btn-warning"><i class="fa fa-refresh"></i></button>
        <a href="{{ add }}" data-toggle="tooltip" title="{{ button_add }}" class="btn btn-primary"><i class="fa fa-plus"></i></a>
        <button type="button" data-toggle="tooltip" title="{{ button_delete }}" class="btn btn-danger" onclick="confirm('{{ text_confirm }}') ? $('#form-page-analysis').submit() : false;"><i class="fa fa-trash-o"></i></button>
        <a href="{{ back }}" data-toggle="tooltip" title="{{ button_back }}" class="btn btn-default"><i class="fa fa-reply"></i></a>
      </div>
      <h1><i class="fa fa-file-text-o"></i> {{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    {% if success %}
    <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-list"></i> {{ text_page_analysis_list }}</h3>
      </div>
<div class="panel-body">
        <div class="well">
          <div class="row">
            <div class="col-sm-6">
              <div class="form-group">
                <label class="control-label" for="input-page-url">{{ column_page_url }}</label>
                <input type="text" name="filter_page_url" value="{{ filter_page_url }}" placeholder="{{ column_page_url }}" id="input-page-url" class="form-control" />
              </div>
            </div>
            <div class="col-sm-6">
              <div class="form-group">
                <label class="control-label" for="input-target-keyword">{{ column_target_keyword }}</label>
                <input type="text" name="filter_target_keyword" value="{{ filter_target_keyword }}" placeholder="{{ column_target_keyword }}" id="input-target-keyword" class="form-control" />
              </div>
              <button type="button" id="button-filter" class="btn btn-primary pull-right"><i class="fa fa-filter"></i> {{ button_filter }}</button>
            </div>
          </div>
        </div>
        <form action="{{ delete }}" method="post" enctype="multipart/form-data" id="form-page-analysis">
          <div class="table-responsive">
            <table class="table table-bordered table-hover">
              <thead>
                <tr>
                  <td style="width: 1px;" class="text-center"><input type="checkbox" onclick="$('input[name*=\'selected\']').prop('checked', this.checked);" /></td>
                  <td class="text-left">{% if sort == 'page_url' %}<a href="{{ sort_page_url }}" class="{{ order|lower }}">{{ column_page_url }}</a>{% else %}<a href="{{ sort_page_url }}">{{ column_page_url }}</a>{% endif %}</td>
                  <td class="text-left">{% if sort == 'target_keyword' %}<a href="{{ sort_target_keyword }}" class="{{ order|lower }}">{{ column_target_keyword }}</a>{% else %}<a href="{{ sort_target_keyword }}">{{ column_target_keyword }}</a>{% endif %}</td>
                  <td class="text-left">{% if sort == 'overall_score' %}<a href="{{ sort_overall_score }}" class="{{ order|lower }}">{{ column_overall_score }}</a>{% else %}<a href="{{ sort_overall_score }}">{{ column_overall_score }}</a>{% endif %}</td>
                  <td class="text-left">{% if sort == 'date_analysis' %}<a href="{{ sort_date_analysis }}" class="{{ order|lower }}">{{ column_date_analysis }}</a>{% else %}<a href="{{ sort_date_analysis }}">{{ column_date_analysis }}</a>{% endif %}</td>
                  <td class="text-right">{{ column_action }}</td>
                </tr>
              </thead>
              <tbody>
                {% if page_analyses %}
                {% for analysis in page_analyses %}
                <tr>
                  <td class="text-center">
                    {% if analysis.analysis_id in selected %}
                    <input type="checkbox" name="selected[]" value="{{ analysis.analysis_id }}" checked="checked" />
                    {% else %}
                    <input type="checkbox" name="selected[]" value="{{ analysis.analysis_id }}" />
                    {% endif %}
                  </td>
                  <td class="text-left">{{ analysis.page_url }}</td>
                  <td class="text-left">{{ analysis.target_keyword }}</td>
                  <td class="text-left">
                    <div class="progress">
                      <div class="progress-bar progress-bar-{{ analysis.score_class }}" role="progressbar" aria-valuenow="{{ analysis.overall_score }}" aria-valuemin="0" aria-valuemax="100" style="width: {{ analysis.overall_score }}%;">
                        {{ analysis.overall_score }}%
                      </div>
                    </div>
                  </td>
                  <td class="text-left">{{ analysis.date_analysis }}</td>
                  <td class="text-right">
                    <a href="{{ analysis.view }}" data-toggle="tooltip" title="{{ button_view }}" class="btn btn-info"><i class="fa fa-eye"></i></a>
                  </td>
                </tr>
                {% endfor %}
                {% else %}
                <tr>
                  <td class="text-center" colspan="6">{{ text_no_results }}</td>
                </tr>
                {% endif %}
              </tbody>
            </table>
          </div>
        </form>
        <div class="row">
          <div class="col-sm-6 text-left">{{ pagination }}</div>
          <div class="col-sm-6 text-right">{{ results }}</div>
        </div>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript">
$('#button-filter').on('click', function() {
	var url = 'index.php?route=catalog/seo/pageAnalysis&user_token={{ user_token }}';

	var filter_page_url = $('input[name=\'filter_page_url\']').val();
	if (filter_page_url) {
		url += '&filter_page_url=' + encodeURIComponent(filter_page_url);
	}

	var filter_target_keyword = $('input[name=\'filter_target_keyword\']').val();
	if (filter_target_keyword) {
		url += '&filter_target_keyword=' + encodeURIComponent(filter_target_keyword);
	}

	location = url;
});

// Analizar todas las páginas
$('#button-analyze').on('click', function() {
    var $btn = $(this);
    $btn.button('loading');
    
    $.ajax({
        url: '{{ analyze }}',
        dataType: 'json',
        beforeSend: function() {
            $btn.button('loading');
        },
        complete: function() {
            $btn.button('reset');
        },
        success: function(json) {
            if (json['success']) {
                location.reload();
            }
        },
        error: function(xhr, ajaxOptions, thrownError) {
            alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
            $btn.button('reset');
        }
    });
});
</script>
<style>
/* Estilos generales para el módulo SEO */
.seo-navigation {
    margin-bottom: 30px;
}

.seo-navigation .panel-body {
    padding: 15px;
    transition: all 0.3s ease;
}

.seo-navigation .panel-body:hover {
    background-color: #f8f8f8;
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
}

.seo-navigation .btn-link {
    color: #333;
    text-decoration: none;
    padding: 15px 0;
}

.seo-navigation i.fa {
    margin-bottom: 10px;
    color: #23a1d1;
}

/* Tarjetas de estadísticas */
.panel-primary, .panel-green, .panel-red, .panel-yellow {
    border-color: #ddd;
}

.panel-primary .panel-heading {
    background-color: #337ab7;
    border-color: #337ab7;
    color: #fff;
}

.panel-green .panel-heading {
    background-color: #5cb85c;
    border-color: #5cb85c;
    color: #fff;
}

.panel-red .panel-heading {
    background-color: #d9534f;
    border-color: #d9534f;
    color: #fff;
}

.panel-yellow .panel-heading {
    background-color: #f0ad4e;
    border-color: #f0ad4e;
    color: #fff;
}

.panel-footer {
    color: #333;
}

.panel .huge {
    font-size: 40px;
    line-height: 1;
    margin-bottom: 5px;
}

/* Barras de progreso */
.progress {
    margin-bottom: 10px;
    height: 20px;
}

.progress-bar {
    line-height: 20px;
    font-size: 12px;
}

.progress-bar-success {
    background-color: #5cb85c;
}

.progress-bar-warning {
    background-color: #f0ad4e;
}

.progress-bar-danger {
    background-color: #d9534f;
}

/* Lista de sugerencias */
.seo-suggestions-list {
    padding-left: 15px;
    list-style-type: none;
}

.seo-suggestions-list li {
    padding: 5px 0;
    border-bottom: 1px dotted #eee;
}

.seo-suggestions-list i {
    margin-right: 8px;
}

/* Estilos para estados */
.label-improved {
    background-color: #5cb85c;
}

.label-declined {
    background-color: #d9534f;
}

.label-unchanged {
    background-color: #5bc0de;
}

.label-new {
    background-color: #f0ad4e;
}

/* Indicadores de cambio en posiciones */
.text-success .fa, 
.text-danger .fa {
    margin-right: 3px;
}

/* Formularios de análisis */
.analysis-results-container {
    margin-top: 20px;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #f9f9f9;
}

/* Responsive */
@media (max-width: 768px) {
    .panel .huge {
        font-size: 30px;
    }
    
    .form-group [class*='col-xs-'] {
        margin-bottom: 15px;
    }
}

/* Animaciones */
.fade-in {
    animation: fadeIn ease 0.5s;
    -webkit-animation: fadeIn ease 0.5s;
    -moz-animation: fadeIn ease 0.5s;
    -o-animation: fadeIn ease 0.5s;
    -ms-animation: fadeIn ease 0.5s;
}

@keyframes fadeIn {
    0% {opacity:0;}
    100% {opacity:1;}
}

@-moz-keyframes fadeIn {
    0% {opacity:0;}
    100% {opacity:1;}
}

@-webkit-keyframes fadeIn {
    0% {opacity:0;}
    100% {opacity:1;}
}

@-o-keyframes fadeIn {
    0% {opacity:0;}
    100% {opacity:1;}
}

@-ms-keyframes fadeIn {
    0% {opacity:0;}
    100% {opacity:1;}
}    
</style>
{{ footer }}