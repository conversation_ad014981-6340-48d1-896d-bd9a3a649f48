{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <a href="{{ back }}" class="btn btn-default"><i class="fa fa-arrow-left"></i> {{ button_back }}</a>
        <a href="{{ print }}" target="_blank" class="btn btn-info"><i class="fa fa-print"></i> {{ button_print }}</a>
        <a href="{{ export_excel }}" class="btn btn-success"><i class="fa fa-file-excel-o"></i> {{ button_export_excel }}</a>
        <a href="{{ refresh }}" class="btn btn-warning"><i class="fa fa-refresh"></i> {{ button_refresh }}</a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible">
      <i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    
    <!-- إعدادات التقرير -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-cog"></i> {{ text_report_settings }}</h3>
      </div>
      <div class="panel-body">
        <form method="get" class="form-inline">
          <input type="hidden" name="route" value="inventory/stock_movement/expiringLots" />
          <input type="hidden" name="user_token" value="{{ user_token }}" />
          
          <div class="form-group">
            <label for="days">{{ entry_days_ahead }}:</label>
            <select name="days" id="days" class="form-control">
              <option value="7"{% if days_ahead == 7 %} selected{% endif %}>7 أيام</option>
              <option value="15"{% if days_ahead == 15 %} selected{% endif %}>15 يوم</option>
              <option value="30"{% if days_ahead == 30 %} selected{% endif %}>30 يوم</option>
              <option value="60"{% if days_ahead == 60 %} selected{% endif %}>60 يوم</option>
              <option value="90"{% if days_ahead == 90 %} selected{% endif %}>90 يوم</option>
            </select>
          </div>
          
          <button type="submit" class="btn btn-primary">
            <i class="fa fa-search"></i> {{ button_update }}
          </button>
        </form>
      </div>
    </div>
    
    <!-- ملخص التقرير -->
    <div class="row">
      <div class="col-md-3">
        <div class="panel panel-danger">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-exclamation-triangle fa-2x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ total_lots }}</div>
                <div>{{ text_total_expiring_lots }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-md-3">
        <div class="panel panel-warning">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-clock-o fa-2x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ days_ahead }}</div>
                <div>{{ text_days_range }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-md-3">
        <div class="panel panel-info">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-cubes fa-2x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ total_products }}</div>
                <div>{{ text_affected_products }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-md-3">
        <div class="panel panel-success">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-money fa-2x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ total_value }}</div>
                <div>{{ text_total_value }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- جدول الدفعات منتهية الصلاحية -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title">
          <i class="fa fa-exclamation-triangle"></i> {{ text_expiring_lots_list }}
          <small class="text-muted">({{ text_within_days|replace({'%days%': days_ahead}) }})</small>
        </h3>
      </div>
      <div class="panel-body">
        <div class="table-responsive">
          <table class="table table-bordered table-hover" id="expiring-lots-table">
            <thead>
              <tr>
                <th>{{ column_product_name }}</th>
                <th>{{ column_branch }}</th>
                <th>{{ column_lot_number }}</th>
                <th>{{ column_expiry_date }}</th>
                <th class="text-center">{{ column_days_to_expiry }}</th>
                <th class="text-right">{{ column_quantity }}</th>
                <th>{{ column_unit }}</th>
                {% if can_view_cost %}
                <th class="text-right">{{ column_value }}</th>
                {% endif %}
                <th class="text-center">{{ column_urgency }}</th>
                <th class="text-center">{{ column_action }}</th>
              </tr>
            </thead>
            <tbody>
              {% for lot in expiring_lots %}
              <tr class="{% if lot.days_to_expiry < 0 %}danger{% elseif lot.days_to_expiry <= 7 %}warning{% endif %}">
                <td>
                  <strong>{{ lot.product_name }}</strong>
                  {% if lot.model %}
                    <br><small class="text-muted">{{ lot.model }}</small>
                  {% endif %}
                </td>
                <td>{{ lot.branch_name }}</td>
                <td>
                  <span class="label label-info">{{ lot.lot_number }}</span>
                </td>
                <td>{{ lot.expiry_date }}</td>
                <td class="text-center">
                  <span class="label label-{{ lot.urgency_class }}">
                    {% if lot.days_to_expiry < 0 %}
                      منتهية منذ {{ lot.days_to_expiry|abs }} يوم
                    {% else %}
                      {{ lot.days_to_expiry }} يوم
                    {% endif %}
                  </span>
                </td>
                <td class="text-right">
                  <strong>{{ lot.quantity }}</strong>
                </td>
                <td>{{ lot.unit_name }}</td>
                {% if can_view_cost %}
                <td class="text-right">{{ lot.value }}</td>
                {% endif %}
                <td class="text-center">
                  {% if lot.days_to_expiry < 0 %}
                    <i class="fa fa-times-circle text-danger" title="منتهية الصلاحية"></i>
                  {% elseif lot.days_to_expiry <= 3 %}
                    <i class="fa fa-exclamation-triangle text-danger" title="حرجة جداً"></i>
                  {% elseif lot.days_to_expiry <= 7 %}
                    <i class="fa fa-exclamation-triangle text-warning" title="حرجة"></i>
                  {% else %}
                    <i class="fa fa-clock-o text-info" title="تحذير"></i>
                  {% endif %}
                </td>
                <td class="text-center">
                  <div class="btn-group">
                    <a href="{{ lot.product_card }}" class="btn btn-xs btn-info" title="{{ button_product_card }}">
                      <i class="fa fa-list-alt"></i>
                    </a>
                    {% if lot.adjustment_link %}
                    <a href="{{ lot.adjustment_link }}" class="btn btn-xs btn-warning" title="{{ button_create_adjustment }}">
                      <i class="fa fa-edit"></i>
                    </a>
                    {% endif %}
                  </div>
                </td>
              </tr>
              {% else %}
              <tr>
                <td class="text-center" colspan="{% if can_view_cost %}10{% else %}9{% endif %}">
                  <div class="alert alert-success">
                    <i class="fa fa-check-circle"></i> {{ text_no_expiring_lots }}
                  </div>
                </td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    </div>
    
    <!-- توصيات الإجراءات -->
    {% if expiring_lots %}
    <div class="panel panel-info">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-lightbulb-o"></i> {{ text_recommendations }}</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-md-6">
            <h4><i class="fa fa-exclamation-triangle text-danger"></i> {{ text_immediate_actions }}</h4>
            <ul>
              <li>{{ text_recommendation_1 }}</li>
              <li>{{ text_recommendation_2 }}</li>
              <li>{{ text_recommendation_3 }}</li>
            </ul>
          </div>
          <div class="col-md-6">
            <h4><i class="fa fa-calendar text-warning"></i> {{ text_preventive_actions }}</h4>
            <ul>
              <li>{{ text_recommendation_4 }}</li>
              <li>{{ text_recommendation_5 }}</li>
              <li>{{ text_recommendation_6 }}</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
    {% endif %}
  </div>
</div>

<style>
.huge {
    font-size: 30px;
}

.panel-danger > .panel-heading {
    color: white;
    background-color: #d9534f;
    border-color: #d9534f;
}

.panel-warning > .panel-heading {
    color: white;
    background-color: #f0ad4e;
    border-color: #f0ad4e;
}

.panel-info > .panel-heading {
    color: white;
    background-color: #5bc0de;
    border-color: #5bc0de;
}

.panel-success > .panel-heading {
    color: white;
    background-color: #5cb85c;
    border-color: #5cb85c;
}

#expiring-lots-table {
    font-size: 13px;
}

#expiring-lots-table th {
    background-color: #f5f5f5;
    font-weight: 600;
}

.table > tbody > tr.danger > td {
    background-color: #f2dede;
}

.table > tbody > tr.warning > td {
    background-color: #fcf8e3;
}
</style>

<script>
$(document).ready(function() {
    // تهيئة DataTable
    $('#expiring-lots-table').DataTable({
        "paging": true,
        "searching": true,
        "ordering": true,
        "info": true,
        "responsive": true,
        "pageLength": 25,
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Arabic.json"
        },
        "order": [[ 4, "asc" ]], // ترتيب حسب أيام انتهاء الصلاحية
        "columnDefs": [
            {
                "targets": [4], // عمود أيام انتهاء الصلاحية
                "type": "num"
            }
        ]
    });
    
    // تحديث تلقائي كل 5 دقائق
    setInterval(function() {
        location.reload();
    }, 300000);
});
</script>

{{ footer }}