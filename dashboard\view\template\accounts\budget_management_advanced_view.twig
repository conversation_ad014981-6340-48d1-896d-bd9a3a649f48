{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <a href="{{ edit }}" data-toggle="tooltip" title="{{ button_edit }}" class="btn btn-primary"><i class="fa fa-pencil"></i></a>
        <a href="{{ copy }}" data-toggle="tooltip" title="{{ button_copy }}" class="btn btn-info"><i class="fa fa-copy"></i></a>
        <a href="{{ export_excel }}" data-toggle="tooltip" title="{{ button_export_excel }}" class="btn btn-success"><i class="fa fa-file-excel-o"></i></a>
        <a href="{{ export_pdf }}" data-toggle="tooltip" title="{{ button_export_pdf }}" class="btn btn-danger"><i class="fa fa-file-pdf-o"></i></a>
        <a href="{{ print }}" data-toggle="tooltip" title="{{ button_print }}" class="btn btn-warning" target="_blank"><i class="fa fa-print"></i></a>
        {% if budget.status == 'draft' %}
        <a href="{{ approve }}" data-toggle="tooltip" title="{{ button_approve }}" class="btn btn-success" onclick="return confirm('{{ text_confirm_approve }}');"><i class="fa fa-check"></i></a>
        {% endif %}
        <a href="{{ back }}" data-toggle="tooltip" title="{{ button_back }}" class="btn btn-default"><i class="fa fa-reply"></i></a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if success %}
    <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    
    <!-- معلومات الموازنة -->
    <div class="panel panel-primary">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-info-circle"></i> {{ text_budget_info }}</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-md-6">
            <table class="table table-striped">
              <tr>
                <td><strong>{{ text_budget_name }}:</strong></td>
                <td>{{ budget.budget_name }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_budget_type }}:</strong></td>
                <td>{{ budget.budget_type_name }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_budget_year }}:</strong></td>
                <td>{{ budget.budget_year }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_currency }}:</strong></td>
                <td>{{ budget.currency }}</td>
              </tr>
            </table>
          </div>
          <div class="col-md-6">
            <table class="table table-striped">
              <tr>
                <td><strong>{{ text_start_date }}:</strong></td>
                <td>{{ budget.start_date }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_end_date }}:</strong></td>
                <td>{{ budget.end_date }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_status }}:</strong></td>
                <td>
                  <span class="label label-{% if budget.status == 'approved' %}success{% elseif budget.status == 'draft' %}warning{% else %}default{% endif %}">
                    {{ budget.status_name }}
                  </span>
                </td>
              </tr>
              <tr>
                <td><strong>{{ text_created_by }}:</strong></td>
                <td>{{ budget.created_by_name }}</td>
              </tr>
            </table>
          </div>
        </div>
        {% if budget.budget_description %}
        <div class="row">
          <div class="col-md-12">
            <h4>{{ text_description }}</h4>
            <p>{{ budget.budget_description }}</p>
          </div>
        </div>
        {% endif %}
      </div>
    </div>

    <!-- ملخص الموازنة -->
    <div class="panel panel-success">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-bar-chart"></i> {{ text_budget_summary }}</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-md-3">
            <div class="info-box bg-blue">
              <span class="info-box-icon"><i class="fa fa-plus"></i></span>
              <div class="info-box-content">
                <span class="info-box-text">{{ text_total_revenue }}</span>
                <span class="info-box-number">{{ budget_summary.total_revenue }}</span>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="info-box bg-red">
              <span class="info-box-icon"><i class="fa fa-minus"></i></span>
              <div class="info-box-content">
                <span class="info-box-text">{{ text_total_expenses }}</span>
                <span class="info-box-number">{{ budget_summary.total_expenses }}</span>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="info-box bg-green">
              <span class="info-box-icon"><i class="fa fa-calculator"></i></span>
              <div class="info-box-content">
                <span class="info-box-text">{{ text_net_income }}</span>
                <span class="info-box-number">{{ budget_summary.net_income }}</span>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="info-box bg-yellow">
              <span class="info-box-icon"><i class="fa fa-percent"></i></span>
              <div class="info-box-content">
                <span class="info-box-text">{{ text_profit_margin }}</span>
                <span class="info-box-number">{{ budget_summary.profit_margin }}%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- بنود الموازنة -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-list"></i> {{ text_budget_lines }}</h3>
      </div>
      <div class="panel-body">
        <div class="table-responsive">
          <table class="table table-bordered table-hover" id="budget-lines-table">
            <thead>
              <tr>
                <th>{{ column_account }}</th>
                <th>{{ column_account_name }}</th>
                <th class="text-right">{{ column_jan }}</th>
                <th class="text-right">{{ column_feb }}</th>
                <th class="text-right">{{ column_mar }}</th>
                <th class="text-right">{{ column_apr }}</th>
                <th class="text-right">{{ column_may }}</th>
                <th class="text-right">{{ column_jun }}</th>
                <th class="text-right">{{ column_jul }}</th>
                <th class="text-right">{{ column_aug }}</th>
                <th class="text-right">{{ column_sep }}</th>
                <th class="text-right">{{ column_oct }}</th>
                <th class="text-right">{{ column_nov }}</th>
                <th class="text-right">{{ column_dec }}</th>
                <th class="text-right">{{ column_total }}</th>
              </tr>
            </thead>
            <tbody>
              {% for line in budget_lines %}
              <tr>
                <td>{{ line.account_code }}</td>
                <td>{{ line.account_name }}</td>
                <td class="text-right">{{ line.jan }}</td>
                <td class="text-right">{{ line.feb }}</td>
                <td class="text-right">{{ line.mar }}</td>
                <td class="text-right">{{ line.apr }}</td>
                <td class="text-right">{{ line.may }}</td>
                <td class="text-right">{{ line.jun }}</td>
                <td class="text-right">{{ line.jul }}</td>
                <td class="text-right">{{ line.aug }}</td>
                <td class="text-right">{{ line.sep }}</td>
                <td class="text-right">{{ line.oct }}</td>
                <td class="text-right">{{ line.nov }}</td>
                <td class="text-right">{{ line.dec }}</td>
                <td class="text-right"><strong>{{ line.total }}</strong></td>
              </tr>
              {% endfor %}
            </tbody>
            <tfoot>
              <tr class="info">
                <th colspan="2">{{ text_total }}</th>
                <th class="text-right">{{ budget_totals.jan }}</th>
                <th class="text-right">{{ budget_totals.feb }}</th>
                <th class="text-right">{{ budget_totals.mar }}</th>
                <th class="text-right">{{ budget_totals.apr }}</th>
                <th class="text-right">{{ budget_totals.may }}</th>
                <th class="text-right">{{ budget_totals.jun }}</th>
                <th class="text-right">{{ budget_totals.jul }}</th>
                <th class="text-right">{{ budget_totals.aug }}</th>
                <th class="text-right">{{ budget_totals.sep }}</th>
                <th class="text-right">{{ budget_totals.oct }}</th>
                <th class="text-right">{{ budget_totals.nov }}</th>
                <th class="text-right">{{ budget_totals.dec }}</th>
                <th class="text-right">{{ budget_totals.total }}</th>
              </tr>
            </tfoot>
          </table>
        </div>
      </div>
    </div>

    <!-- تحليل الموازنة -->
    {% if variance_analysis %}
    <div class="panel panel-warning">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-line-chart"></i> {{ text_variance_analysis }}</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-md-8">
            <canvas id="variance-chart" width="400" height="200"></canvas>
          </div>
          <div class="col-md-4">
            <table class="table table-striped">
              <thead>
                <tr>
                  <th>{{ text_metric }}</th>
                  <th class="text-right">{{ text_variance }}</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>{{ text_revenue_variance }}</td>
                  <td class="text-right {% if variance_analysis.revenue_variance >= 0 %}text-success{% else %}text-danger{% endif %}">
                    {{ variance_analysis.revenue_variance }}
                  </td>
                </tr>
                <tr>
                  <td>{{ text_expense_variance }}</td>
                  <td class="text-right {% if variance_analysis.expense_variance <= 0 %}text-success{% else %}text-danger{% endif %}">
                    {{ variance_analysis.expense_variance }}
                  </td>
                </tr>
                <tr>
                  <td>{{ text_net_variance }}</td>
                  <td class="text-right {% if variance_analysis.net_variance >= 0 %}text-success{% else %}text-danger{% endif %}">
                    <strong>{{ variance_analysis.net_variance }}</strong>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
    {% endif %}

    <!-- تحليل الأداء -->
    {% if performance_analysis %}
    <div class="panel panel-info">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-dashboard"></i> {{ text_performance_analysis }}</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-md-6">
            <h4>{{ text_achievement_rate }}</h4>
            <div class="progress">
              <div class="progress-bar progress-bar-{% if performance_analysis.achievement_rate >= 90 %}success{% elseif performance_analysis.achievement_rate >= 70 %}info{% elseif performance_analysis.achievement_rate >= 50 %}warning{% else %}danger{% endif %}" 
                   style="width: {{ performance_analysis.achievement_rate }}%">
                {{ performance_analysis.achievement_rate }}%
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <h4>{{ text_efficiency_score }}</h4>
            <div class="progress">
              <div class="progress-bar progress-bar-{% if performance_analysis.efficiency_score >= 90 %}success{% elseif performance_analysis.efficiency_score >= 70 %}info{% elseif performance_analysis.efficiency_score >= 50 %}warning{% else %}danger{% endif %}" 
                   style="width: {{ performance_analysis.efficiency_score }}%">
                {{ performance_analysis.efficiency_score }}%
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    {% endif %}

    <!-- معلومات الامتثال -->
    <div class="panel panel-success">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-check-circle"></i> {{ text_compliance_info }}</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-md-3">
            <div class="alert alert-success">
              <i class="fa fa-check"></i> {{ text_eas_compliant }}
            </div>
          </div>
          <div class="col-md-3">
            <div class="alert alert-info">
              <i class="fa fa-globe"></i> {{ text_eta_ready }}
            </div>
          </div>
          <div class="col-md-3">
            <div class="alert alert-warning">
              <i class="fa fa-balance-scale"></i> {{ text_egyptian_gaap }}
            </div>
          </div>
          <div class="col-md-3">
            <div class="alert alert-primary">
              <i class="fa fa-shield"></i> {{ text_budget_control }}
            </div>
          </div>
        </div>
      </div>
    </div>

  </div>
</div>

<script type="text/javascript"><!--
// DataTables initialization
$('#budget-lines-table').DataTable({
    "language": {
        "url": "{{ datatable_language }}"
    },
    "order": [[ 0, "asc" ]],
    "pageLength": 25,
    "scrollX": true,
    "dom": 'Bfrtip',
    "buttons": [
        'copy', 'csv', 'excel', 'pdf', 'print'
    ]
});

// Variance Chart
{% if variance_analysis %}
var ctx = document.getElementById('variance-chart').getContext('2d');
var varianceChart = new Chart(ctx, {
    type: 'bar',
    data: {
        labels: ['{{ text_revenue }}', '{{ text_expenses }}', '{{ text_net_income }}'],
        datasets: [{
            label: '{{ text_budget }}',
            data: [{{ variance_analysis.budget_revenue }}, {{ variance_analysis.budget_expenses }}, {{ variance_analysis.budget_net }}],
            backgroundColor: 'rgba(54, 162, 235, 0.2)',
            borderColor: 'rgba(54, 162, 235, 1)',
            borderWidth: 1
        }, {
            label: '{{ text_actual }}',
            data: [{{ variance_analysis.actual_revenue }}, {{ variance_analysis.actual_expenses }}, {{ variance_analysis.actual_net }}],
            backgroundColor: 'rgba(255, 99, 132, 0.2)',
            borderColor: 'rgba(255, 99, 132, 1)',
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});
{% endif %}

// Tooltips
$('[data-toggle="tooltip"]').tooltip();
//--></script>

{{ footer }}
