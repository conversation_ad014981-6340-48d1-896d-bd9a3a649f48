{{ header }}{{ column_left }}
<div id="content">
<style>
/* تحسينات متقدمة لإقفال الفترة - Enterprise Grade */
.panel-default {
    border: none;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.panel-default:hover {
    box-shadow: 0 0.25rem 2rem 0 rgba(58, 59, 69, 0.2);
}

.panel-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    border-radius: 8px 8px 0 0;
    padding: 15px;
}

/* تحسين الجدول */
.table {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 0.125rem 0.25rem rgba(0,0,0,0.075);
}

.table thead th {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    border: none;
    font-weight: 600;
    text-align: center;
}

.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
    transform: translateX(2px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* تحسين حالات الإقفال */
.status-open {
    color: #28a745;
    font-weight: 600;
}

.status-closed {
    color: #dc3545;
    font-weight: 600;
}

.status-pending {
    color: #ffc107;
    font-weight: 600;
}

/* تحسين الفلاتر */
.form-inline {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.form-group {
    flex-grow: 1;
    margin-bottom: 10px;
    margin-right: 10px;
}

.form-control {
    border-radius: 6px;
    border: 1px solid #ced4da;
    transition: all 0.2s ease;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
    transform: scale(1.02);
}

/* تحسين الأزرار */
.btn {
    transition: all 0.2s ease;
    border-radius: 6px;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    border: none;
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
    border: none;
}

/* تحسين ملخص الإقفال */
.closing-summary {
    background: linear-gradient(135deg, #f1f8ff 0%, #e6f3ff 100%);
    border-left: 4px solid #007bff;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.closing-summary h4 {
    color: #0056b3;
    margin-top: 0;
    font-weight: 600;
}

/* تحسين خطوات الإقفال */
.closing-steps {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.step {
    display: flex;
    align-items: center;
    padding: 15px;
    margin-bottom: 10px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.step:hover {
    background-color: #f8f9fa;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    margin-right: 15px;
}

.step-content {
    flex-grow: 1;
}

.step-title {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 5px;
}

.step-description {
    color: #6c757d;
    font-size: 0.9em;
}

.step-status {
    margin-left: 15px;
}

.step-completed {
    background-color: #d4edda;
    border-left: 4px solid #28a745;
}

.step-pending {
    background-color: #fff3cd;
    border-left: 4px solid #ffc107;
}

.step-error {
    background-color: #f8d7da;
    border-left: 4px solid #dc3545;
}

/* تحسين التحذيرات */
.warning-box {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border-left: 4px solid #ffc107;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.error-box {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    border-left: 4px solid #dc3545;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.success-box {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    border-left: 4px solid #28a745;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}
</style>

<div class="page-header">
    <div class="container-fluid">
        <div class="pull-right">
            <button type="button" data-toggle="tooltip" title="تحديث البيانات" onclick="refreshClosingData()" class="btn btn-info"><i class="fa fa-refresh"></i></button>
            <button type="button" data-toggle="tooltip" title="تصدير التقرير" onclick="exportClosingReport()" class="btn btn-success"><i class="fa fa-download"></i></button>
        </div>
        <h1>{{ heading_title }}</h1>
        <ul class="breadcrumb">
            {% for breadcrumb in breadcrumbs %}
            <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
            {% endfor %}
        </ul>
    </div>
</div>

<div class="container-fluid">
    {% if success %}
    <div class="success-box">
        <i class="fa fa-check-circle"></i> {{ success }}
    </div>
    {% endif %}
    
    {% if error_warning %}
    <div class="error-box">
        <i class="fa fa-exclamation-triangle"></i> {{ error_warning }}
    </div>
    {% endif %}
    
    <div class="panel panel-default">
        <div class="panel-header">
            <h3 class="panel-title"><i class="fa fa-calendar-times"></i> إقفال الفترة المحاسبية</h3>
        </div>
        
        <!-- ملخص الإقفال -->
        <div class="closing-summary">
            <h4><i class="fa fa-info-circle"></i> ملخص حالة الإقفال</h4>
            <div class="row">
                <div class="col-md-3">
                    <div class="text-center">
                        <h5>الفترة الحالية</h5>
                        <span class="badge badge-info">{{ current_period }}</span>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h5>حالة الإقفال</h5>
                        <span class="status-{{ period_status }}">{{ period_status_text }}</span>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h5>آخر إقفال</h5>
                        <span class="text-muted">{{ last_closing_date }}</span>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h5>المستخدم</h5>
                        <span class="text-muted">{{ closing_user }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- تحذير مهم -->
        <div class="warning-box">
            <h5><i class="fa fa-exclamation-triangle"></i> تحذير مهم</h5>
            <p>إقفال الفترة المحاسبية عملية لا يمكن التراجع عنها. تأكد من مراجعة جميع القيود والتقارير قبل الإقفال.</p>
        </div>

        <!-- خطوات الإقفال -->
        <div class="closing-steps">
            <h4><i class="fa fa-list-ol"></i> خطوات الإقفال</h4>
            
            <div class="step step-completed">
                <div class="step-number">1</div>
                <div class="step-content">
                    <div class="step-title">مراجعة القيود المحاسبية</div>
                    <div class="step-description">التأكد من ترحيل جميع القيود وعدم وجود قيود معلقة</div>
                </div>
                <div class="step-status">
                    <i class="fa fa-check-circle text-success"></i>
                </div>
            </div>
            
            <div class="step step-completed">
                <div class="step-number">2</div>
                <div class="step-content">
                    <div class="step-title">مراجعة ميزان المراجعة</div>
                    <div class="step-description">التأكد من توازن الميزان وصحة الأرصدة</div>
                </div>
                <div class="step-status">
                    <i class="fa fa-check-circle text-success"></i>
                </div>
            </div>
            
            <div class="step step-pending">
                <div class="step-number">3</div>
                <div class="step-content">
                    <div class="step-title">قيود التسوية</div>
                    <div class="step-description">إدخال قيود التسوية والإهلاك والمخصصات</div>
                </div>
                <div class="step-status">
                    <i class="fa fa-clock-o text-warning"></i>
                </div>
            </div>
            
            <div class="step step-pending">
                <div class="step-number">4</div>
                <div class="step-content">
                    <div class="step-title">القوائم المالية</div>
                    <div class="step-description">إعداد ومراجعة القوائم المالية النهائية</div>
                </div>
                <div class="step-status">
                    <i class="fa fa-clock-o text-warning"></i>
                </div>
            </div>
            
            <div class="step step-pending">
                <div class="step-number">5</div>
                <div class="step-content">
                    <div class="step-title">إقفال الحسابات</div>
                    <div class="step-description">إقفال حسابات الإيرادات والمصروفات</div>
                </div>
                <div class="step-status">
                    <i class="fa fa-clock-o text-warning"></i>
                </div>
            </div>
        </div>

        <!-- نموذج الإقفال -->
        <form action="{{ action }}" method="post" enctype="multipart/form-data" id="form-period-closing">
            <div class="form-group">
                <label class="control-label" for="input-period">الفترة المحاسبية:</label>
                <select name="period_id" id="input-period" class="form-control">
                    {% for period in periods %}
                    <option value="{{ period.period_id }}" {% if period.period_id == selected_period %}selected{% endif %}>
                        {{ period.period_name }} ({{ period.start_date }} - {{ period.end_date }})
                    </option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="form-group">
                <label class="control-label" for="input-closing-date">تاريخ الإقفال:</label>
                <div class="input-group date">
                    <input type="text" name="closing_date" value="{{ closing_date }}" placeholder="تاريخ الإقفال" id="input-closing-date" class="form-control" />
                    <span class="input-group-btn">
                        <button class="btn btn-default" type="button"><i class="fa fa-calendar"></i></button>
                    </span>
                </div>
            </div>
            
            <div class="form-group">
                <label class="control-label" for="input-notes">ملاحظات الإقفال:</label>
                <textarea name="closing_notes" rows="4" placeholder="ملاحظات الإقفال" id="input-notes" class="form-control">{{ closing_notes }}</textarea>
            </div>
            
            <div class="text-right">
                <button type="button" onclick="confirmClosing()" class="btn btn-danger btn-lg">
                    <i class="fa fa-lock"></i> إقفال الفترة المحاسبية
                </button>
            </div>
        </form>
    </div>
</div>

<script type="text/javascript"><!--
// تحسينات متقدمة لإقفال الفترة
$(document).ready(function() {
    // تحسين فلاتر التاريخ
    $('.input-group.date').datetimepicker({
        pickTime: false,
        format: 'YYYY-MM-DD'
    });
    
    // إضافة مؤشر تحميل للعمليات
    $(document).ajaxStart(function() {
        $('body').addClass('loading');
    }).ajaxStop(function() {
        $('body').removeClass('loading');
    });
    
    // تحسين عرض الخطوات
    $('.step').each(function() {
        $(this).on('click', function() {
            $(this).toggleClass('expanded');
        });
    });
    
    // تحديث حالة الخطوات ديناميكياً
    updateStepsStatus();
});

// دالة تأكيد الإقفال
function confirmClosing() {
    if (confirm('هل أنت متأكد من إقفال الفترة المحاسبية؟ هذه العملية لا يمكن التراجع عنها.')) {
        if (confirm('تأكيد نهائي: سيتم إقفال الفترة المحاسبية نهائياً. هل تريد المتابعة؟')) {
            $('#form-period-closing').submit();
        }
    }
}

// دالة تحديث البيانات
function refreshClosingData() {
    location.reload();
}

// دالة تصدير تقرير الإقفال
function exportClosingReport() {
    const url = 'index.php?route=accounts/period_closing/export';
    window.open(url, '_blank');
}

// دالة تحديث حالة الخطوات
function updateStepsStatus() {
    // محاكاة فحص حالة الخطوات
    setTimeout(function() {
        $('.step-pending').first().removeClass('step-pending').addClass('step-completed');
        $('.step-completed').last().find('.step-status i').removeClass('fa-clock-o text-warning').addClass('fa-check-circle text-success');
    }, 2000);
}

// دالة معاينة الإقفال
function previewClosing() {
    const periodId = $('#input-period').val();
    const closingDate = $('#input-closing-date').val();
    
    if (periodId && closingDate) {
        const url = 'index.php?route=accounts/period_closing/preview&period_id=' + periodId + '&closing_date=' + closingDate;
        window.open(url, '_blank', 'width=800,height=600');
    } else {
        alert('يرجى تحديد الفترة المحاسبية وتاريخ الإقفال');
    }
}
//--></script>
{{ footer }}