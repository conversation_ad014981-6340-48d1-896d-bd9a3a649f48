// Custom OpenCart variables
$blue:       #1e91cf;
$indigo:     #6610f2;
$purple:     #6f42c1;
$pink:       #d63384;
$red:        #e3503e;
$orange:     #fd7e14;
$yellow:     #f3a638;
$green:      #4cb64c;
$teal:       #20c997;
$cyan:       #54b7d3;
$white:      #fff;
$gray-100:   #f8f9fa;
$gray-200:   #e9ecef;
$gray-300:   #dee2e6;
$gray-400:   #ced4da;
$gray-500:   #adb5bd;
$gray-600:   #6c757d;
$gray-700:   #495057;
$gray-800:   #343a40;
$gray-900:   #212529;
$black:      #000;
$light:      $white;
$variable-prefix:           bs-; // Deprecated in v5.2.0 for the shorter `$prefix`
$prefix:                    $variable-prefix;
$min-contrast-ratio:        2;
$enable-caret:              false;
$border-radius:             .2308rem;
$border-radius-sm:          .1538rem;
$border-radius-lg:          .3846rem;
$font-size-base:            .8125rem;
$line-height-base:          1.4;
$input-btn-padding-y:       .5385rem;
$input-btn-padding-x:       .7692rem;
$input-btn-padding-y-lg:    .6923rem;
$input-btn-padding-x-lg:    1.385rem;
$navbar-padding-y:          0;
$navbar-nav-link-padding-x: 1.5rem;
$spacer:                    1rem;
$card-spacer-y:             $spacer;
$card-cap-padding-y:        $card-spacer-y / 1.3;
$breadcrumb-padding-x:      .65rem;
$breadcrumb-item-padding:   .3rem;
$breadcrumb-divider-color:  $gray-400;
$breadcrumb-divider:        "\f105";



@import "scss/bootstrap";



// Custom OpenCart styles
.breadcrumb {
  display: inline-block;
}
.breadcrumb-item a {
  color: #{$gray-600};
}
.breadcrumb-item:last-child a {
  color: #{$blue};
}
.breadcrumb-item {
  text-shadow: 0 1px #{$white};
  font-size: .9231rem;
  display: inline-block;
  + .breadcrumb-item {
    padding-left: var(--#{$prefix}breadcrumb-item-padding-x);
    &::before {
      font-family: "Font Awesome 6 Free";
      font-weight: 600;
      font-size: .7692rem;
      color: #{$gray-400};
      float: none;
    }
  }
}
.card-header {
  font-size: 0.9375rem;
}
@each $color, $value in $theme-colors {
  .btn-#{$color} {
    --#{$prefix}btn-border-color: #{darken($value, 10)};
    --#{$prefix}btn-hover-border-color: #{darken($value, 30)};
  }
}
.btn-light, .btn-outline-secondary {
  --#{$prefix}btn-border-color: #{$gray-400};
}
.table > :not(caption) > * > * {
	background: transparent;
}
