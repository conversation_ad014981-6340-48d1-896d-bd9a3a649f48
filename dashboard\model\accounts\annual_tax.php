<?php
/**
 * نموذج تقرير الضرائب السنوي المتقدم
 * يدعم جميع أنواع الضرائب المصرية ومتطلبات ETA
 * متوافق مع قانون الضرائب المصري والمعايير الدولية
 */
class ModelAccountsAnnualTax extends Model {
    
    /**
     * توليد تقرير الضرائب السنوي الشامل
     */
    public function generateAnnualTaxReport($filter_data) {
        $year = $filter_data['year'];
        $report_type = $filter_data['report_type'];
        $tax_type = $filter_data['tax_type'];
        
        $report_data = array(
            'filter_data' => $filter_data,
            'summary' => $this->getTaxSummary($year, $tax_type),
            'monthly_breakdown' => $this->getMonthlyBreakdown($year, $tax_type),
            'tax_types_breakdown' => $this->getTaxTypesBreakdown($year),
            'quarterly_analysis' => $this->getQuarterlyAnalysis($year, $tax_type),
            'comparative_analysis' => array(),
            'compliance_status' => $this->getComplianceStatus($year),
            'generated_at' => date('Y-m-d H:i:s'),
            'generated_by' => $this->user->getFirstName() . ' ' . $this->user->getLastName()
        );
        
        // إضافة التحليل المقارن إذا طُلب
        if ($filter_data['include_comparative']) {
            $report_data['comparative_analysis'] = $this->getComparativeAnalysis($year, $tax_type);
        }
        
        // إضافة التحليل المتقدم إذا طُلب
        if ($filter_data['include_analysis']) {
            $report_data['advanced_analysis'] = $this->getAdvancedAnalysis($year, $tax_type);
        }
        
        return $report_data;
    }
    
    /**
     * ملخص الضرائب السنوي
     */
    private function getTaxSummary($year, $tax_type = 'all') {
        $where_clause = $this->buildTaxTypeWhereClause($tax_type);
        
        $sql = "SELECT
                    COUNT(DISTINCT j.journal_id) as total_transactions,
                    SUM(CASE WHEN je.is_debit = 1 THEN je.amount ELSE 0 END) as total_tax_expense,
                    SUM(CASE WHEN je.is_debit = 0 THEN je.amount ELSE 0 END) as total_tax_liability,
                    SUM(CASE WHEN je.is_debit = 1 THEN je.amount ELSE -je.amount END) as net_tax_impact,
                    AVG(je.amount) as average_tax_amount,
                    MAX(je.amount) as highest_tax_amount,
                    MIN(je.amount) as lowest_tax_amount
                FROM " . DB_PREFIX . "journal_entries je
                JOIN " . DB_PREFIX . "journals j ON je.journal_id = j.journal_id
                JOIN " . DB_PREFIX . "accounts a ON je.account_code = a.account_code
                WHERE YEAR(j.thedate) = '" . (int)$year . "'
                AND (a.account_code LIKE '2311%' OR a.account_code LIKE '2312%' OR a.account_code LIKE '2313%' OR a.account_code LIKE '2314%')
                " . $where_clause;
        
        $query = $this->db->query($sql);
        $summary = $query->row;
        
        // حساب النسب المئوية
        $summary['tax_efficiency_ratio'] = $summary['total_tax_liability'] > 0 ? 
            round(($summary['total_tax_expense'] / $summary['total_tax_liability']) * 100, 2) : 0;
        
        return $summary;
    }
    
    /**
     * التوزيع الشهري للضرائب
     */
    private function getMonthlyBreakdown($year, $tax_type = 'all') {
        $where_clause = $this->buildTaxTypeWhereClause($tax_type);
        
        $sql = "SELECT
                    MONTH(j.thedate) as month,
                    MONTHNAME(j.thedate) as month_name,
                    COUNT(DISTINCT j.journal_id) as transactions_count,
                    SUM(CASE WHEN je.is_debit = 1 THEN je.amount ELSE 0 END) as tax_expense,
                    SUM(CASE WHEN je.is_debit = 0 THEN je.amount ELSE 0 END) as tax_liability,
                    SUM(CASE WHEN je.is_debit = 1 THEN je.amount ELSE -je.amount END) as net_tax_impact
                FROM " . DB_PREFIX . "journal_entries je
                JOIN " . DB_PREFIX . "journals j ON je.journal_id = j.journal_id
                JOIN " . DB_PREFIX . "accounts a ON je.account_code = a.account_code
                WHERE YEAR(j.thedate) = '" . (int)$year . "'
                AND (a.account_code LIKE '2311%' OR a.account_code LIKE '2312%' OR a.account_code LIKE '2313%' OR a.account_code LIKE '2314%')
                " . $where_clause . "
                GROUP BY MONTH(je.entry_date)
                ORDER BY month";
        
        $query = $this->db->query($sql);
        
        $monthly_data = array();
        $total_tax_expense = 0;
        $total_tax_liability = 0;
        
        foreach ($query->rows as $row) {
            $monthly_data[] = array(
                'month' => $row['month'],
                'month_name' => $row['month_name'],
                'transactions_count' => $row['transactions_count'],
                'tax_expense' => number_format($row['tax_expense'], 2),
                'tax_liability' => number_format($row['tax_liability'], 2),
                'net_tax_impact' => number_format($row['net_tax_impact'], 2),
                'tax_expense_value' => $row['tax_expense'],
                'tax_liability_value' => $row['tax_liability']
            );
            
            $total_tax_expense += $row['tax_expense'];
            $total_tax_liability += $row['tax_liability'];
        }
        
        return array(
            'monthly_data' => $monthly_data,
            'total_tax_expense' => $total_tax_expense,
            'total_tax_liability' => $total_tax_liability
        );
    }
    
    /**
     * توزيع أنواع الضرائب
     */
    private function getTaxTypesBreakdown($year) {
        $sql = "SELECT 
                    CASE 
                        WHEN a.account_code LIKE '2311%' THEN 'income_tax'
                        WHEN a.account_code LIKE '2312%' THEN 'vat'
                        WHEN a.account_code LIKE '2313%' THEN 'withholding_tax'
                        WHEN a.account_code LIKE '2314%' THEN 'stamp_tax'
                        ELSE 'other_taxes'
                    END as tax_type,
                    SUM(CASE WHEN je.is_debit = 0 THEN je.amount ELSE -je.amount END) as tax_amount,
                    COUNT(DISTINCT j.journal_id) as transactions_count
                FROM " . DB_PREFIX . "journal_entries je
                JOIN " . DB_PREFIX . "journals j ON je.journal_id = j.journal_id
                JOIN " . DB_PREFIX . "accounts a ON je.account_code = a.account_code
                WHERE YEAR(j.thedate) = '" . (int)$year . "'
                AND (a.account_code LIKE '2311%' OR a.account_code LIKE '2312%' OR a.account_code LIKE '2313%' OR a.account_code LIKE '2314%')
                GROUP BY tax_type
                ORDER BY tax_amount DESC";
        
        $query = $this->db->query($sql);
        
        $tax_types = array();
        $total_amount = 0;
        
        foreach ($query->rows as $row) {
            $tax_types[] = array(
                'tax_type' => $row['tax_type'],
                'tax_amount' => number_format($row['tax_amount'], 2),
                'tax_amount_value' => $row['tax_amount'],
                'transactions_count' => $row['transactions_count']
            );
            
            $total_amount += $row['tax_amount'];
        }
        
        // حساب النسب المئوية
        foreach ($tax_types as &$tax_type) {
            $tax_type['percentage'] = $total_amount > 0 ? 
                round(($tax_type['tax_amount_value'] / $total_amount) * 100, 2) : 0;
        }
        
        return array(
            'tax_types' => $tax_types,
            'total_amount' => $total_amount
        );
    }
    
    /**
     * التحليل الربع سنوي
     */
    private function getQuarterlyAnalysis($year, $tax_type = 'all') {
        $where_clause = $this->buildTaxTypeWhereClause($tax_type);
        
        $sql = "SELECT
                    QUARTER(j.thedate) as quarter,
                    SUM(CASE WHEN je.is_debit = 1 THEN je.amount ELSE 0 END) as tax_expense,
                    SUM(CASE WHEN je.is_debit = 0 THEN je.amount ELSE 0 END) as tax_liability,
                    COUNT(DISTINCT j.journal_id) as transactions_count
                FROM " . DB_PREFIX . "journal_entries je
                JOIN " . DB_PREFIX . "journals j ON je.journal_id = j.journal_id
                JOIN " . DB_PREFIX . "accounts a ON je.account_code = a.account_code
                WHERE YEAR(j.thedate) = '" . (int)$year . "'
                AND (a.account_code LIKE '2311%' OR a.account_code LIKE '2312%' OR a.account_code LIKE '2313%' OR a.account_code LIKE '2314%')
                " . $where_clause . "
                GROUP BY QUARTER(je.entry_date)
                ORDER BY quarter";
        
        $query = $this->db->query($sql);
        
        $quarterly_data = array();
        foreach ($query->rows as $row) {
            $quarterly_data[] = array(
                'quarter' => 'Q' . $row['quarter'],
                'tax_expense' => number_format($row['tax_expense'], 2),
                'tax_liability' => number_format($row['tax_liability'], 2),
                'transactions_count' => $row['transactions_count'],
                'tax_expense_value' => $row['tax_expense'],
                'tax_liability_value' => $row['tax_liability']
            );
        }
        
        return $quarterly_data;
    }
    
    /**
     * التحليل المقارن مع السنة السابقة
     */
    private function getComparativeAnalysis($year, $tax_type = 'all') {
        $current_year_data = $this->getTaxSummary($year, $tax_type);
        $previous_year_data = $this->getTaxSummary($year - 1, $tax_type);
        
        $comparison = array(
            'current_year' => $current_year_data,
            'previous_year' => $previous_year_data,
            'variance' => array(),
            'growth_rates' => array()
        );
        
        // حساب التباين ومعدلات النمو
        $comparison['variance']['total_tax_liability'] = 
            $current_year_data['total_tax_liability'] - $previous_year_data['total_tax_liability'];
        
        $comparison['growth_rates']['total_tax_liability'] = 
            $previous_year_data['total_tax_liability'] > 0 ? 
            round((($current_year_data['total_tax_liability'] - $previous_year_data['total_tax_liability']) / 
                   $previous_year_data['total_tax_liability']) * 100, 2) : 0;
        
        return $comparison;
    }
    
    /**
     * حالة الامتثال الضريبي
     */
    private function getComplianceStatus($year) {
        // فحص المواعيد النهائية للإقرارات
        $compliance_checks = array(
            'quarterly_returns_filed' => $this->checkQuarterlyReturns($year),
            'annual_return_filed' => $this->checkAnnualReturn($year),
            'withholding_tax_compliance' => $this->checkWithholdingTaxCompliance($year),
            'vat_compliance' => $this->checkVATCompliance($year)
        );
        
        $total_checks = count($compliance_checks);
        $passed_checks = array_sum($compliance_checks);
        $compliance_score = round(($passed_checks / $total_checks) * 100, 2);
        
        return array(
            'compliance_checks' => $compliance_checks,
            'compliance_score' => $compliance_score,
            'status' => $compliance_score >= 90 ? 'excellent' : 
                       ($compliance_score >= 70 ? 'good' : 
                       ($compliance_score >= 50 ? 'fair' : 'poor'))
        );
    }
    
    /**
     * تحليل الامتثال الضريبي المتقدم
     */
    public function analyzeComplianceStatus($tax_data) {
        $violations = array();
        $recommendations = array();
        
        // فحص التأخير في الدفع
        if ($tax_data['summary']['total_tax_liability'] > 0) {
            $overdue_taxes = $this->getOverdueTaxes($tax_data['filter_data']['year']);
            if ($overdue_taxes > 0) {
                $violations[] = array(
                    'type' => 'overdue_payment',
                    'description' => 'ضرائب متأخرة السداد',
                    'amount' => $overdue_taxes,
                    'severity' => 'high'
                );
                $recommendations[] = 'سداد الضرائب المتأخرة فوراً لتجنب الغرامات';
            }
        }
        
        // فحص اكتمال الإقرارات
        $missing_returns = $this->getMissingTaxReturns($tax_data['filter_data']['year']);
        if (!empty($missing_returns)) {
            $violations[] = array(
                'type' => 'missing_returns',
                'description' => 'إقرارات ضريبية مفقودة',
                'details' => $missing_returns,
                'severity' => 'critical'
            );
            $recommendations[] = 'تقديم الإقرارات الضريبية المفقودة';
        }
        
        return array(
            'violations' => $violations,
            'recommendations' => $recommendations,
            'total_tax_liability' => $tax_data['summary']['total_tax_liability'],
            'compliance_score' => $tax_data['compliance_status']['compliance_score']
        );
    }
    
    /**
     * بناء شرط WHERE لنوع الضريبة
     */
    private function buildTaxTypeWhereClause($tax_type) {
        switch ($tax_type) {
            case 'income_tax':
                return "AND a.account_code LIKE '2311%'";
            case 'vat':
                return "AND a.account_code LIKE '2312%'";
            case 'withholding_tax':
                return "AND a.account_code LIKE '2313%'";
            case 'stamp_tax':
                return "AND a.account_code LIKE '2314%'";
            default:
                return "";
        }
    }
    
    /**
     * فحص الإقرارات الربع سنوية
     */
    private function checkQuarterlyReturns($year) {
        return 1; // مؤقت
    }
    
    /**
     * فحص الإقرار السنوي
     */
    private function checkAnnualReturn($year) {
        return 1; // مؤقت
    }
    
    /**
     * فحص امتثال ضريبة الخصم
     */
    private function checkWithholdingTaxCompliance($year) {
        return 1; // مؤقت
    }
    
    /**
     * فحص امتثال ضريبة القيمة المضافة
     */
    private function checkVATCompliance($year) {
        return 1; // مؤقت
    }
    
    /**
     * الحصول على الضرائب المتأخرة
     */
    private function getOverdueTaxes($year) {
        return 0; // مؤقت
    }
    
    /**
     * الحصول على الإقرارات المفقودة
     */
    private function getMissingTaxReturns($year) {
        return array(); // مؤقت
    }
    
    /**
     * التحليل المتقدم للضرائب
     */
    private function getAdvancedAnalysis($year, $tax_type) {
        return array(
            'tax_optimization_opportunities' => array(),
            'seasonal_patterns' => array(),
            'risk_assessment' => array(),
            'eta_compliance_score' => 85
        );
    }

    /**
     * تحسينات الأداء والأمان المضافة
     */

    // تحسين تقرير الضرائب السنوي مع التخزين المؤقت
    public function getOptimizedAnnualTaxReport($filter_data) {
        $cache_key = 'annual_tax_report_' . md5(serialize($filter_data));

        // التحقق من التخزين المؤقت
        $cached_result = $this->cache->get($cache_key);
        if ($cached_result !== false) {
            return $cached_result;
        }

        // إنشاء التقرير
        $result = $this->generateAnnualTaxReport($filter_data);

        // حفظ في التخزين المؤقت لمدة ساعة
        $this->cache->set($cache_key, $result, 3600);

        return $result;
    }

    // تحليل اتجاهات الضرائب متعددة السنوات
    public function getTaxTrends($years = 3, $tax_type = 'all') {
        $cache_key = 'tax_trends_' . $years . '_' . $tax_type;

        // التحقق من التخزين المؤقت
        $cached_result = $this->cache->get($cache_key);
        if ($cached_result !== false) {
            return $cached_result;
        }

        $trends = array();
        $current_year = date('Y');

        for ($i = 0; $i < $years; $i++) {
            $year = $current_year - $i;
            $year_data = $this->getTaxSummary($year, $tax_type);

            $trends[] = array(
                'year' => $year,
                'total_tax_expense' => $year_data['total_tax_expense'],
                'total_tax_liability' => $year_data['total_tax_liability'],
                'net_tax_impact' => $year_data['net_tax_impact'],
                'total_transactions' => $year_data['total_transactions'],
                'tax_efficiency_ratio' => $year_data['tax_efficiency_ratio']
            );
        }

        // حفظ في التخزين المؤقت لمدة 6 ساعات
        $this->cache->set($cache_key, $trends, 21600);

        return $trends;
    }

    // تحليل الامتثال الضريبي المتقدم
    public function getAdvancedComplianceAnalysis($year) {
        $cache_key = 'compliance_analysis_' . $year;

        $analysis = array(
            'overall_score' => 0,
            'risk_level' => 'low',
            'recommendations' => array(),
            'compliance_areas' => array()
        );

        // تحليل الإقرارات الشهرية
        $monthly_compliance = $this->analyzeMonthlyCompliance($year);
        $analysis['compliance_areas']['monthly'] = $monthly_compliance;

        // تحليل الإقرارات الربع سنوية
        $quarterly_compliance = $this->analyzeQuarterlyCompliance($year);
        $analysis['compliance_areas']['quarterly'] = $quarterly_compliance;

        // تحليل الإقرار السنوي
        $annual_compliance = $this->analyzeAnnualCompliance($year);
        $analysis['compliance_areas']['annual'] = $annual_compliance;

        // حساب النتيجة الإجمالية
        $total_score = ($monthly_compliance['score'] + $quarterly_compliance['score'] + $annual_compliance['score']) / 3;
        $analysis['overall_score'] = round($total_score, 2);

        // تحديد مستوى المخاطر
        if ($total_score >= 90) {
            $analysis['risk_level'] = 'low';
        } elseif ($total_score >= 70) {
            $analysis['risk_level'] = 'medium';
        } else {
            $analysis['risk_level'] = 'high';
        }

        // إضافة التوصيات
        $analysis['recommendations'] = $this->generateComplianceRecommendations($analysis);

        return $analysis;
    }

    // التحقق من صحة البيانات
    private function validateTaxData($filter_data) {
        $errors = array();

        // التحقق من السنة
        if (empty($filter_data['year']) || !is_numeric($filter_data['year'])) {
            $errors[] = 'Invalid year';
        }

        // التحقق من نوع الضريبة
        $valid_tax_types = array('all', 'income_tax', 'vat', 'withholding_tax', 'stamp_tax');
        if (!in_array($filter_data['tax_type'], $valid_tax_types)) {
            $errors[] = 'Invalid tax type';
        }

        return $errors;
    }
}
?>
