{{ header }}{{ column_left }}

<!-- Enterprise Grade Plus CSS for Advanced Aging Report -->
<style>
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --danger-color: #e74c3c;
    --warning-color: #f39c12;
    --info-color: #17a2b8;
    --critical-color: #8e44ad;
    --light-bg: #f8f9fa;
    --border-color: #dee2e6;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

.advanced-aging-container {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: var(--shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.advanced-aging-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--critical-color), var(--danger-color));
}

.advanced-header {
    text-align: center;
    border-bottom: 3px solid var(--primary-color);
    padding-bottom: 20px;
    margin-bottom: 30px;
    position: relative;
}

.advanced-header h2 {
    color: var(--primary-color);
    font-weight: 700;
    font-size: 1.8rem;
    margin: 0;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.risk-dashboard {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.risk-card {
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    text-align: center;
    box-shadow: var(--shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.risk-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0,0,0,0.1);
}

.risk-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
}

.risk-card.critical::before { background: var(--critical-color); }
.risk-card.high::before { background: var(--danger-color); }
.risk-card.medium::before { background: var(--warning-color); }
.risk-card.low::before { background: var(--success-color); }
.risk-card.total::before { background: var(--primary-color); }

.risk-card h4 {
    color: var(--primary-color);
    margin-bottom: 10px;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.risk-card .amount {
    font-size: 1.3rem;
    font-weight: 700;
    font-family: 'Courier New', monospace;
    margin-bottom: 5px;
}

.risk-card .count {
    font-size: 0.8rem;
    color: #6c757d;
    font-weight: 500;
}

.card-critical .amount { color: var(--critical-color); }
.card-high .amount { color: var(--danger-color); }
.card-medium .amount { color: var(--warning-color); }
.card-low .amount { color: var(--success-color); }
.card-total .amount { color: var(--primary-color); }

.advanced-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    background: #ffffff;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.advanced-table th {
    background: linear-gradient(135deg, var(--primary-color), #34495e);
    color: white;
    padding: 15px 10px;
    text-align: center;
    font-weight: 600;
    font-size: 0.85rem;
    border-bottom: 2px solid var(--border-color);
}

.advanced-table td {
    padding: 12px 10px;
    border-bottom: 1px solid var(--border-color);
    text-align: center;
    transition: var(--transition);
    font-size: 0.9rem;
}

.advanced-table tbody tr:hover {
    background: var(--light-bg);
    transform: scale(1.005);
}

.advanced-table tbody tr:nth-child(even) {
    background: #fafbfc;
}

.risk-indicator {
    display: inline-block;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    margin-right: 8px;
    position: relative;
}

.risk-indicator::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: rgba(255,255,255,0.8);
    transform: translate(-50%, -50%);
}

.risk-critical { background: var(--critical-color); }
.risk-high { background: var(--danger-color); }
.risk-medium { background: var(--warning-color); }
.risk-low { background: var(--success-color); }

.amount-cell {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    text-align: {{ direction == 'rtl' ? 'right' : 'left' }};
}

.amount-overdue { color: var(--danger-color); font-weight: 700; }
.amount-current { color: var(--success-color); }

.trend-indicator {
    font-size: 0.8rem;
    margin-left: 5px;
}

.trend-up { color: var(--danger-color); }
.trend-down { color: var(--success-color); }
.trend-stable { color: var(--info-color); }

.analytics-section {
    background: linear-gradient(135deg, #f8f9fa, #ffffff);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    margin-top: 30px;
}

.analytics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.chart-container {
    background: #ffffff;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 15px;
    height: 300px;
}

/* RTL Support */
[dir="rtl"] .advanced-table {
    direction: rtl;
}

[dir="rtl"] .amount-cell {
    text-align: right;
}

/* Accessibility Enhancements */
.advanced-table tr:focus-within {
    outline: 2px solid var(--secondary-color);
    outline-offset: 2px;
}

/* Print Styles */
@media print {
    .advanced-aging-container {
        box-shadow: none;
        border: 1px solid #000;
    }
    
    .advanced-table th {
        background: #f0f0f0 !important;
        color: #000 !important;
    }
    
    .chart-container {
        display: none;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .advanced-table {
        font-size: 0.75rem;
    }
    
    .advanced-table th,
    .advanced-table td {
        padding: 8px 5px;
    }
    
    .risk-dashboard {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .analytics-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-{{ direction == 'rtl' ? 'start' : 'end' }}">
        <div class="btn-group" role="group" aria-label="{{ text_actions }}">
          <button type="button" class="btn btn-success btn-lg" onclick="generateAdvancedAgingReport()" 
                  data-bs-toggle="tooltip" title="{{ text_generate_tooltip }}">
            <i class="fas fa-chart-line me-2"></i> {{ button_generate }}
          </button>
          <div class="btn-group" role="group">
            <button type="button" class="btn btn-info dropdown-toggle" data-bs-toggle="dropdown" 
                    aria-expanded="false" data-bs-toggle="tooltip" title="{{ text_export_tooltip }}">
              <i class="fas fa-download me-2"></i> {{ text_export }}
            </button>
            <ul class="dropdown-menu">
              <li><a class="dropdown-item" href="#" onclick="exportAdvancedAgingReport('excel')">
                <i class="fas fa-file-excel text-success me-2"></i> Excel
              </a></li>
              <li><a class="dropdown-item" href="#" onclick="exportAdvancedAgingReport('pdf')">
                <i class="fas fa-file-pdf text-danger me-2"></i> PDF
              </a></li>
              <li><a class="dropdown-item" href="#" onclick="exportAdvancedAgingReport('csv')">
                <i class="fas fa-file-csv text-info me-2"></i> CSV
              </a></li>
              <li><hr class="dropdown-divider"></li>
              <li><a class="dropdown-item" href="#" onclick="printAdvancedAgingReport()">
                <i class="fas fa-print text-secondary me-2"></i> {{ text_print }}
              </a></li>
            </ul>
          </div>
          <button type="button" class="btn btn-outline-danger" onclick="showCriticalRiskAnalysis()"
                  data-bs-toggle="tooltip" title="{{ text_critical_risk_analysis }}">
            <i class="fas fa-exclamation-triangle"></i>
          </button>
          <button type="button" class="btn btn-outline-primary" onclick="showTrendAnalysis()"
                  data-bs-toggle="tooltip" title="{{ text_trend_analysis }}">
            <i class="fas fa-chart-area"></i>
          </button>
        </div>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li class="breadcrumb-item"><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>

  <div class="container-fluid">
    <!-- Advanced Filter Form -->
    <div class="card mb-4">
      <div class="card-header">
        <h3 class="card-title">
          <i class="fas fa-sliders-h me-2"></i>{{ text_advanced_filters }}
        </h3>
      </div>
      <div class="card-body">
        <form id="advanced-aging-filter-form" method="post">
          <div class="row">
            <div class="col-md-2">
              <div class="form-group">
                <label for="date_end" class="form-label">{{ entry_date_end }}</label>
                <input type="date" name="date_end" id="date_end" value="{{ date_end }}" class="form-control" required>
              </div>
            </div>
            <div class="col-md-2">
              <div class="form-group">
                <label for="report_type" class="form-label">{{ entry_report_type }}</label>
                <select name="report_type" id="report_type" class="form-control">
                  <option value="customers"{% if report_type == 'customers' %} selected{% endif %}>{{ text_customers }}</option>
                  <option value="suppliers"{% if report_type == 'suppliers' %} selected{% endif %}>{{ text_suppliers }}</option>
                  <option value="both"{% if report_type == 'both' %} selected{% endif %}>{{ text_both }}</option>
                </select>
              </div>
            </div>
            <div class="col-md-2">
              <div class="form-group">
                <label for="risk_level" class="form-label">{{ entry_risk_level }}</label>
                <select name="risk_level" id="risk_level" class="form-control">
                  <option value="all"{% if risk_level == 'all' %} selected{% endif %}>{{ text_all_risks }}</option>
                  <option value="critical"{% if risk_level == 'critical' %} selected{% endif %}>{{ text_critical_risk }}</option>
                  <option value="high"{% if risk_level == 'high' %} selected{% endif %}>{{ text_high_risk }}</option>
                  <option value="medium"{% if risk_level == 'medium' %} selected{% endif %}>{{ text_medium_risk }}</option>
                  <option value="low"{% if risk_level == 'low' %} selected{% endif %}>{{ text_low_risk }}</option>
                </select>
              </div>
            </div>
            <div class="col-md-2">
              <div class="form-group">
                <label for="sort_by" class="form-label">{{ entry_sort_by }}</label>
                <select name="sort_by" id="sort_by" class="form-control">
                  <option value="name"{% if sort_by == 'name' %} selected{% endif %}>{{ text_sort_by_name }}</option>
                  <option value="amount"{% if sort_by == 'amount' %} selected{% endif %}>{{ text_sort_by_amount }}</option>
                  <option value="risk"{% if sort_by == 'risk' %} selected{% endif %}>{{ text_sort_by_risk }}</option>
                  <option value="overdue_days"{% if sort_by == 'overdue_days' %} selected{% endif %}>{{ text_sort_by_overdue_days }}</option>
                </select>
              </div>
            </div>
            <div class="col-md-2">
              <div class="form-group">
                <label for="branch_id" class="form-label">{{ entry_branch }}</label>
                <select name="branch_id" id="branch_id" class="form-control">
                  <option value="">{{ text_all_branches }}</option>
                  {% for branch in branches %}
                  <option value="{{ branch.branch_id }}"{% if branch.branch_id == branch_id %} selected{% endif %}>{{ branch.name }}</option>
                  {% endfor %}
                </select>
              </div>
            </div>
            <div class="col-md-2">
              <div class="form-group">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                  <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search me-2"></i>{{ button_filter }}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>

    <!-- Advanced Aging Report Content -->
    {% if aging_data %}
    <div class="advanced-aging-container">
      <div class="advanced-header">
        <h2>{{ heading_title }}</h2>
        <p class="text-muted">{{ text_as_of }} {{ date_end_formatted }} - {{ text_report_type }}: {{ report_type_text }}</p>
      </div>

      <!-- Risk Dashboard -->
      <div class="risk-dashboard">
        <div class="risk-card card-critical critical">
          <h4>{{ text_critical_risk }}</h4>
          <div class="amount">{{ aging_data.risk_summary.critical_amount_formatted }}</div>
          <div class="count">{{ aging_data.risk_summary.critical_count }} {{ text_entities }}</div>
        </div>
        <div class="risk-card card-high high">
          <h4>{{ text_high_risk }}</h4>
          <div class="amount">{{ aging_data.risk_summary.high_amount_formatted }}</div>
          <div class="count">{{ aging_data.risk_summary.high_count }} {{ text_entities }}</div>
        </div>
        <div class="risk-card card-medium medium">
          <h4>{{ text_medium_risk }}</h4>
          <div class="amount">{{ aging_data.risk_summary.medium_amount_formatted }}</div>
          <div class="count">{{ aging_data.risk_summary.medium_count }} {{ text_entities }}</div>
        </div>
        <div class="risk-card card-low low">
          <h4>{{ text_low_risk }}</h4>
          <div class="amount">{{ aging_data.risk_summary.low_amount_formatted }}</div>
          <div class="count">{{ aging_data.risk_summary.low_count }} {{ text_entities }}</div>
        </div>
        <div class="risk-card card-total total">
          <h4>{{ text_total_exposure }}</h4>
          <div class="amount">{{ aging_data.risk_summary.total_amount_formatted }}</div>
          <div class="count">{{ aging_data.risk_summary.total_count }} {{ text_entities }}</div>
        </div>
      </div>

      <!-- Advanced Aging Table -->
      <div class="table-responsive">
        <table class="advanced-table" id="advanced-aging-table">
          <thead>
            <tr>
              <th>{{ text_entity_name }}</th>
              <th>{{ text_contact_info }}</th>
              <th>{{ text_credit_limit }}</th>
              <th>{{ text_current }}</th>
              <th>1-30 {{ text_days }}</th>
              <th>31-60 {{ text_days }}</th>
              <th>61-90 {{ text_days }}</th>
              <th>90+ {{ text_days }}</th>
              <th>{{ text_total_outstanding }}</th>
              <th>{{ text_risk_score }}</th>
              <th>{{ text_trend }}</th>
              <th>{{ text_actions }}</th>
            </tr>
          </thead>
          <tbody>
            {% for item in aging_data.items %}
            <tr data-entity-id="{{ item.entity_id }}" data-risk="{{ item.risk_level }}" data-type="{{ item.entity_type }}">
              <td>
                <strong>{{ item.name }}</strong>
                <br>
                <small class="text-muted">{{ item.code }}</small>
                {% if item.entity_type == 'customer' %}
                <span class="badge bg-primary">{{ text_customer }}</span>
                {% else %}
                <span class="badge bg-secondary">{{ text_supplier }}</span>
                {% endif %}
              </td>
              <td>
                <small>
                  {% if item.email %}<i class="fas fa-envelope"></i> {{ item.email }}<br>{% endif %}
                  {% if item.telephone %}<i class="fas fa-phone"></i> {{ item.telephone }}<br>{% endif %}
                  {% if item.address %}<i class="fas fa-map-marker-alt"></i> {{ item.address }}{% endif %}
                </small>
              </td>
              <td class="amount-cell">{{ item.credit_limit_formatted }}</td>
              <td class="amount-cell amount-current">{{ item.current_formatted }}</td>
              <td class="amount-cell {% if item.days_1_30 > 0 %}amount-overdue{% endif %}">{{ item.days_1_30_formatted }}</td>
              <td class="amount-cell {% if item.days_31_60 > 0 %}amount-overdue{% endif %}">{{ item.days_31_60_formatted }}</td>
              <td class="amount-cell {% if item.days_61_90 > 0 %}amount-overdue{% endif %}">{{ item.days_61_90_formatted }}</td>
              <td class="amount-cell {% if item.days_over_90 > 0 %}amount-overdue{% endif %}">{{ item.days_over_90_formatted }}</td>
              <td class="amount-cell"><strong>{{ item.total_formatted }}</strong></td>
              <td>
                <span class="risk-indicator risk-{{ item.risk_level }}"></span>
                {{ item.risk_score }}/100
                <br>
                <small class="text-{{ item.risk_level == 'critical' ? 'danger' : (item.risk_level == 'high' ? 'warning' : 'muted') }}">
                  {{ item.risk_text }}
                </small>
              </td>
              <td>
                <span class="trend-indicator trend-{{ item.trend_direction }}">
                  <i class="fas fa-arrow-{{ item.trend_direction == 'up' ? 'up' : (item.trend_direction == 'down' ? 'down' : 'right') }}"></i>
                  {{ item.trend_percentage }}%
                </span>
              </td>
              <td>
                <div class="btn-group btn-group-sm">
                  <button type="button" class="btn btn-outline-primary btn-sm" onclick="viewEntityDetails('{{ item.entity_id }}', '{{ item.entity_type }}')"
                          data-bs-toggle="tooltip" title="{{ text_view_details }}">
                    <i class="fas fa-eye"></i>
                  </button>
                  <button type="button" class="btn btn-outline-warning btn-sm" onclick="sendStatement('{{ item.entity_id }}', '{{ item.entity_type }}')"
                          data-bs-toggle="tooltip" title="{{ text_send_statement }}">
                    <i class="fas fa-envelope"></i>
                  </button>
                  {% if item.risk_level in ['high', 'critical'] %}
                  <button type="button" class="btn btn-outline-danger btn-sm" onclick="escalateRisk('{{ item.entity_id }}', '{{ item.entity_type }}')"
                          data-bs-toggle="tooltip" title="{{ text_escalate_risk }}">
                    <i class="fas fa-exclamation-triangle"></i>
                  </button>
                  {% endif %}
                </div>
              </td>
            </tr>
            {% endfor %}
          </tbody>
          <tfoot>
            <tr class="table-dark">
              <th colspan="3">{{ text_total }}</th>
              <th class="amount-cell">{{ aging_data.totals.current_formatted }}</th>
              <th class="amount-cell">{{ aging_data.totals.days_1_30_formatted }}</th>
              <th class="amount-cell">{{ aging_data.totals.days_31_60_formatted }}</th>
              <th class="amount-cell">{{ aging_data.totals.days_61_90_formatted }}</th>
              <th class="amount-cell">{{ aging_data.totals.days_over_90_formatted }}</th>
              <th class="amount-cell"><strong>{{ aging_data.totals.total_formatted }}</strong></th>
              <th colspan="3"></th>
            </tr>
          </tfoot>
        </table>
      </div>

      <!-- Critical Risk Alert -->
      {% if aging_data.risk_analysis.critical_count > 0 %}
      <div class="alert alert-danger mt-4">
        <h5><i class="fas fa-exclamation-triangle me-2"></i>{{ text_critical_risk_alert }}</h5>
        <p>{{ text_critical_entities_found }}: <strong>{{ aging_data.risk_analysis.critical_count }}</strong></p>
        <p>{{ text_total_critical_exposure }}: <strong>{{ aging_data.risk_analysis.total_critical_amount_formatted }}</strong></p>
        <button type="button" class="btn btn-outline-danger btn-sm" onclick="showCriticalRiskAnalysis()">
          {{ text_view_critical_details }}
        </button>
      </div>
      {% endif %}

      <!-- Analytics Section -->
      <div class="analytics-section">
        <h4><i class="fas fa-chart-bar me-2"></i>{{ text_advanced_analytics }}</h4>
        <div class="analytics-grid">
          <div class="chart-container">
            <canvas id="riskDistributionChart"></canvas>
          </div>
          <div class="chart-container">
            <canvas id="agingTrendChart"></canvas>
          </div>
        </div>
      </div>
    </div>
    {% else %}
    <div class="alert alert-info">
      <i class="fas fa-info-circle me-2"></i>
      {{ text_no_data }}
    </div>
    {% endif %}
  </div>
</div>

<script>
// Enterprise Grade Plus JavaScript for Advanced Aging Report
class AdvancedAgingReportManager {
    constructor() {
        this.initializeTooltips();
        this.initializeFormValidation();
        this.initializeKeyboardShortcuts();
        this.initializeAutoSave();
        this.initializeDataTable();
        this.initializeCharts();
        this.initializeRiskAnalysis();
    }

    initializeTooltips() {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }

    initializeFormValidation() {
        const form = document.getElementById('advanced-aging-filter-form');
        if (form) {
            form.addEventListener('submit', this.validateForm.bind(this));
        }
    }

    initializeKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case 'g':
                        e.preventDefault();
                        this.generateAdvancedAgingReport();
                        break;
                    case 'e':
                        e.preventDefault();
                        this.showExportMenu();
                        break;
                    case 'p':
                        e.preventDefault();
                        this.printAdvancedAgingReport();
                        break;
                    case 'r':
                        e.preventDefault();
                        this.showCriticalRiskAnalysis();
                        break;
                    case 't':
                        e.preventDefault();
                        this.showTrendAnalysis();
                        break;
                }
            }
        });
    }

    initializeAutoSave() {
        const inputs = document.querySelectorAll('#advanced-aging-filter-form input, #advanced-aging-filter-form select');
        inputs.forEach(input => {
            input.addEventListener('change', () => {
                this.saveFormState();
            });
        });
        this.loadFormState();
    }

    initializeDataTable() {
        const table = document.getElementById('advanced-aging-table');
        if (table && typeof $ !== 'undefined' && $.fn.DataTable) {
            $(table).DataTable({
                responsive: true,
                pageLength: 25,
                order: [[9, 'desc']], // Sort by risk score
                columnDefs: [
                    { targets: [3, 4, 5, 6, 7, 8], className: 'text-end' },
                    { targets: [11], orderable: false }
                ],
                language: {
                    url: '{{ direction == "rtl" ? "/view/javascript/jquery/datatables-ar.json" : "/view/javascript/jquery/datatables-en.json" }}'
                }
            });
        }
    }

    initializeCharts() {
        // Initialize Chart.js charts
        this.initializeRiskDistributionChart();
        this.initializeAgingTrendChart();
    }

    initializeRiskDistributionChart() {
        const ctx = document.getElementById('riskDistributionChart');
        if (ctx && typeof Chart !== 'undefined') {
            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['{{ text_critical_risk }}', '{{ text_high_risk }}', '{{ text_medium_risk }}', '{{ text_low_risk }}'],
                    datasets: [{
                        data: [
                            {{ aging_data.risk_summary.critical_count ?? 0 }},
                            {{ aging_data.risk_summary.high_count ?? 0 }},
                            {{ aging_data.risk_summary.medium_count ?? 0 }},
                            {{ aging_data.risk_summary.low_count ?? 0 }}
                        ],
                        backgroundColor: ['#8e44ad', '#e74c3c', '#f39c12', '#27ae60']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: '{{ text_risk_distribution }}'
                        }
                    }
                }
            });
        }
    }

    initializeAgingTrendChart() {
        const ctx = document.getElementById('agingTrendChart');
        if (ctx && typeof Chart !== 'undefined') {
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['{{ text_current }}', '1-30', '31-60', '61-90', '90+'],
                    datasets: [{
                        label: '{{ text_amount }}',
                        data: [
                            {{ aging_data.totals.current ?? 0 }},
                            {{ aging_data.totals.days_1_30 ?? 0 }},
                            {{ aging_data.totals.days_31_60 ?? 0 }},
                            {{ aging_data.totals.days_61_90 ?? 0 }},
                            {{ aging_data.totals.days_over_90 ?? 0 }}
                        ],
                        borderColor: '#3498db',
                        backgroundColor: 'rgba(52, 152, 219, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: '{{ text_aging_trend }}'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }
    }

    initializeRiskAnalysis() {
        // Highlight critical and high-risk rows
        const criticalRows = document.querySelectorAll('[data-risk="critical"]');
        criticalRows.forEach(row => {
            row.style.backgroundColor = '#fdf2f8';
            row.style.borderLeft = '4px solid var(--critical-color)';
        });

        const highRiskRows = document.querySelectorAll('[data-risk="high"]');
        highRiskRows.forEach(row => {
            row.style.backgroundColor = '#fff5f5';
            row.style.borderLeft = '4px solid var(--danger-color)';
        });
    }

    validateForm(e) {
        e.preventDefault();
        const dateEnd = document.getElementById('date_end').value;

        if (!dateEnd) {
            this.showAlert('{{ error_date_required }}', 'danger');
            return false;
        }

        if (new Date(dateEnd) > new Date()) {
            this.showAlert('{{ warning_future_date }}', 'warning');
        }

        this.generateAdvancedAgingReport();
        return true;
    }

    generateAdvancedAgingReport() {
        const form = document.getElementById('advanced-aging-filter-form');
        const formData = new FormData(form);

        // Show loading state
        this.showLoadingState(true);

        fetch('{{ generate_url }}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            this.showLoadingState(false);
            if (data.success) {
                this.showAlert('{{ text_success_generation }}', 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                this.showAlert(data.error || '{{ error_generate }}', 'danger');
            }
        })
        .catch(error => {
            this.showLoadingState(false);
            this.showAlert('{{ error_generate }}: ' + error.message, 'danger');
        });
    }

    exportAdvancedAgingReport(format) {
        const params = new URLSearchParams({
            format: format,
            date_end: document.getElementById('date_end').value,
            report_type: document.getElementById('report_type').value,
            risk_level: document.getElementById('risk_level').value,
            sort_by: document.getElementById('sort_by').value,
            branch_id: document.getElementById('branch_id').value
        });

        this.showAlert('{{ text_exporting }}...', 'info');
        window.open('{{ export_url }}&' + params.toString(), '_blank');
    }

    printAdvancedAgingReport() {
        window.print();
    }

    showCriticalRiskAnalysis() {
        const criticalRows = document.querySelectorAll('[data-risk="critical"]');
        const highRiskRows = document.querySelectorAll('[data-risk="high"]');
        const totalRiskRows = criticalRows.length + highRiskRows.length;

        if (totalRiskRows > 0) {
            this.showAlert('{{ text_critical_entities_found }}: ' + criticalRows.length + ', {{ text_high_risk_entities_found }}: ' + highRiskRows.length, 'warning');
            // Scroll to first critical risk entity
            if (criticalRows.length > 0) {
                criticalRows[0].scrollIntoView({ behavior: 'smooth', block: 'center' });
            } else if (highRiskRows.length > 0) {
                highRiskRows[0].scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        } else {
            this.showAlert('{{ text_no_critical_entities }}', 'success');
        }
    }

    showTrendAnalysis() {
        // Trend analysis implementation
        this.showAlert('{{ text_trend_analysis_loading }}', 'info');
    }

    viewEntityDetails(entityId, entityType) {
        // View entity details implementation
        window.open(`{{ url_link('sale/customer', 'edit') }}&customer_id=${entityId}`, '_blank');
    }

    sendStatement(entityId, entityType) {
        // Send statement implementation
        this.showAlert('{{ text_statement_sent }}', 'success');
    }

    escalateRisk(entityId, entityType) {
        // Risk escalation implementation
        this.showAlert('{{ text_risk_escalated }}', 'warning');
    }

    showExportMenu() {
        const exportButton = document.querySelector('.dropdown-toggle');
        if (exportButton) {
            exportButton.click();
        }
    }

    showLoadingState(show) {
        const generateBtn = document.querySelector('button[onclick="generateAdvancedAgingReport()"]');
        if (generateBtn) {
            if (show) {
                generateBtn.disabled = true;
                generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> {{ text_generating }}';
            } else {
                generateBtn.disabled = false;
                generateBtn.innerHTML = '<i class="fas fa-chart-line me-2"></i> {{ button_generate }}';
            }
        }
    }

    showAlert(message, type) {
        const alertContainer = document.createElement('div');
        alertContainer.className = `alert alert-${type} alert-dismissible fade show`;
        alertContainer.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'}"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        const container = document.querySelector('#content .container-fluid');
        container.insertBefore(alertContainer, container.firstChild);

        setTimeout(() => {
            alertContainer.remove();
        }, 5000);
    }

    saveFormState() {
        const formData = new FormData(document.getElementById('advanced-aging-filter-form'));
        const state = Object.fromEntries(formData);
        localStorage.setItem('advanced_aging_form_state', JSON.stringify(state));
    }

    loadFormState() {
        const savedState = localStorage.getItem('advanced_aging_form_state');
        if (savedState) {
            const state = JSON.parse(savedState);
            Object.keys(state).forEach(key => {
                const element = document.getElementById(key);
                if (element) {
                    element.value = state[key];
                }
            });
        }
    }
}

// Global functions for backward compatibility
function generateAdvancedAgingReport() {
    advancedAgingReportManager.generateAdvancedAgingReport();
}

function exportAdvancedAgingReport(format) {
    advancedAgingReportManager.exportAdvancedAgingReport(format);
}

function printAdvancedAgingReport() {
    advancedAgingReportManager.printAdvancedAgingReport();
}

function showCriticalRiskAnalysis() {
    advancedAgingReportManager.showCriticalRiskAnalysis();
}

function showTrendAnalysis() {
    advancedAgingReportManager.showTrendAnalysis();
}

function viewEntityDetails(entityId, entityType) {
    advancedAgingReportManager.viewEntityDetails(entityId, entityType);
}

function sendStatement(entityId, entityType) {
    advancedAgingReportManager.sendStatement(entityId, entityType);
}

function escalateRisk(entityId, entityType) {
    advancedAgingReportManager.escalateRisk(entityId, entityType);
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.advancedAgingReportManager = new AdvancedAgingReportManager();
});
</script>

{{ footer }}
