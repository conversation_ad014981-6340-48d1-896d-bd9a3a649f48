{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="submit" form="crm\lead_scoring-form" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fas fa-save"></i></button>
        <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-light"><i class="fas fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    
    <div class="card">
      <div class="card-header"><i class="fas fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}chart-bar{% elif screen_type == 'settings' %}cog{% else %}pencil-alt{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="card-body">
        <form id="crm\lead_scoring-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-activities">{{ text_activities }}</label>
            <div class="col-sm-10">
              <input type="text" name="activities" value="{{ activities }}" placeholder="{{ text_activities }}" id="input-activities" class="form-control" />
              {% if error_activities %}
                <div class="invalid-feedback">{{ error_activities }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-add">{{ text_add }}</label>
            <div class="col-sm-10">
              <input type="text" name="add" value="{{ add }}" placeholder="{{ text_add }}" id="input-add" class="form-control" />
              {% if error_add %}
                <div class="invalid-feedback">{{ error_add }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-analytics">{{ text_analytics }}</label>
            <div class="col-sm-10">
              <input type="text" name="analytics" value="{{ analytics }}" placeholder="{{ text_analytics }}" id="input-analytics" class="form-control" />
              {% if error_analytics %}
                <div class="invalid-feedback">{{ error_analytics }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-bulk_score">{{ text_bulk_score }}</label>
            <div class="col-sm-10">
              <input type="text" name="bulk_score" value="{{ bulk_score }}" placeholder="{{ text_bulk_score }}" id="input-bulk_score" class="form-control" />
              {% if error_bulk_score %}
                <div class="invalid-feedback">{{ error_bulk_score }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-export">{{ text_export }}</label>
            <div class="col-sm-10">
              <input type="text" name="export" value="{{ export }}" placeholder="{{ text_export }}" id="input-export" class="form-control" />
              {% if error_export %}
                <div class="invalid-feedback">{{ error_export }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_assigned_to">{{ text_filter_assigned_to }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_assigned_to" value="{{ filter_assigned_to }}" placeholder="{{ text_filter_assigned_to }}" id="input-filter_assigned_to" class="form-control" />
              {% if error_filter_assigned_to %}
                <div class="invalid-feedback">{{ error_filter_assigned_to }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_name">{{ text_filter_name }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_name" value="{{ filter_name }}" placeholder="{{ text_filter_name }}" id="input-filter_name" class="form-control" />
              {% if error_filter_name %}
                <div class="invalid-feedback">{{ error_filter_name }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_priority">{{ text_filter_priority }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_priority" value="{{ filter_priority }}" placeholder="{{ text_filter_priority }}" id="input-filter_priority" class="form-control" />
              {% if error_filter_priority %}
                <div class="invalid-feedback">{{ error_filter_priority }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_score_range">{{ text_filter_score_range }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_score_range" value="{{ filter_score_range }}" placeholder="{{ text_filter_score_range }}" id="input-filter_score_range" class="form-control" />
              {% if error_filter_score_range %}
                <div class="invalid-feedback">{{ error_filter_score_range }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_source">{{ text_filter_source }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_source" value="{{ filter_source }}" placeholder="{{ text_filter_source }}" id="input-filter_source" class="form-control" />
              {% if error_filter_source %}
                <div class="invalid-feedback">{{ error_filter_source }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_status">{{ text_filter_status }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_status" value="{{ filter_status }}" placeholder="{{ text_filter_status }}" id="input-filter_status" class="form-control" />
              {% if error_filter_status %}
                <div class="invalid-feedback">{{ error_filter_status }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="invalid-feedback">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-lead">{{ text_lead }}</label>
            <div class="col-sm-10">
              <input type="text" name="lead" value="{{ lead }}" placeholder="{{ text_lead }}" id="input-lead" class="form-control" />
              {% if error_lead %}
                <div class="invalid-feedback">{{ error_lead }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-lead_statistics">{{ text_lead_statistics }}</label>
            <div class="col-sm-10">
              <input type="text" name="lead_statistics" value="{{ lead_statistics }}" placeholder="{{ text_lead_statistics }}" id="input-lead_statistics" class="form-control" />
              {% if error_lead_statistics %}
                <div class="invalid-feedback">{{ error_lead_statistics }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-leads">{{ text_leads }}</label>
            <div class="col-sm-10">
              <input type="text" name="leads" value="{{ leads }}" placeholder="{{ text_leads }}" id="input-leads" class="form-control" />
              {% if error_leads %}
                <div class="invalid-feedback">{{ error_leads }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-pagination">{{ text_pagination }}</label>
            <div class="col-sm-10">
              <input type="text" name="pagination" value="{{ pagination }}" placeholder="{{ text_pagination }}" id="input-pagination" class="form-control" />
              {% if error_pagination %}
                <div class="invalid-feedback">{{ error_pagination }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-predictions">{{ text_predictions }}</label>
            <div class="col-sm-10">
              <input type="text" name="predictions" value="{{ predictions }}" placeholder="{{ text_predictions }}" id="input-predictions" class="form-control" />
              {% if error_predictions %}
                <div class="invalid-feedback">{{ error_predictions }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-results">{{ text_results }}</label>
            <div class="col-sm-10">
              <input type="text" name="results" value="{{ results }}" placeholder="{{ text_results }}" id="input-results" class="form-control" />
              {% if error_results %}
                <div class="invalid-feedback">{{ error_results }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-rule_categories">{{ text_rule_categories }}</label>
            <div class="col-sm-10">
              <input type="text" name="rule_categories" value="{{ rule_categories }}" placeholder="{{ text_rule_categories }}" id="input-rule_categories" class="form-control" />
              {% if error_rule_categories %}
                <div class="invalid-feedback">{{ error_rule_categories }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-rules">{{ text_rules }}</label>
            <div class="col-sm-10">
              <input type="text" name="rules" value="{{ rules }}" placeholder="{{ text_rules }}" id="input-rules" class="form-control" />
              {% if error_rules %}
                <div class="invalid-feedback">{{ error_rules }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-score_breakdown">{{ text_score_breakdown }}</label>
            <div class="col-sm-10">
              <input type="text" name="score_breakdown" value="{{ score_breakdown }}" placeholder="{{ text_score_breakdown }}" id="input-score_breakdown" class="form-control" />
              {% if error_score_breakdown %}
                <div class="invalid-feedback">{{ error_score_breakdown }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-score_ranges">{{ text_score_ranges }}</label>
            <div class="col-sm-10">
              <input type="text" name="score_ranges" value="{{ score_ranges }}" placeholder="{{ text_score_ranges }}" id="input-score_ranges" class="form-control" />
              {% if error_score_ranges %}
                <div class="invalid-feedback">{{ error_score_ranges }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-scoring_rules">{{ text_scoring_rules }}</label>
            <div class="col-sm-10">
              <input type="text" name="scoring_rules" value="{{ scoring_rules }}" placeholder="{{ text_scoring_rules }}" id="input-scoring_rules" class="form-control" />
              {% if error_scoring_rules %}
                <div class="invalid-feedback">{{ error_scoring_rules }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sources">{{ text_sources }}</label>
            <div class="col-sm-10">
              <input type="text" name="sources" value="{{ sources }}" placeholder="{{ text_sources }}" id="input-sources" class="form-control" />
              {% if error_sources %}
                <div class="invalid-feedback">{{ error_sources }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-statistics">{{ text_statistics }}</label>
            <div class="col-sm-10">
              <input type="text" name="statistics" value="{{ statistics }}" placeholder="{{ text_statistics }}" id="input-statistics" class="form-control" />
              {% if error_statistics %}
                <div class="invalid-feedback">{{ error_statistics }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="invalid-feedback">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-users">{{ text_users }}</label>
            <div class="col-sm-10">
              <input type="text" name="users" value="{{ users }}" placeholder="{{ text_users }}" id="input-users" class="form-control" />
              {% if error_users %}
                <div class="invalid-feedback">{{ error_users }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}