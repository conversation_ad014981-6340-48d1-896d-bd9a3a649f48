<!DOCTYPE html>
<html dir="{{ direction }}" lang="{{ lang }}" class="no-js">
<head>
<meta charset="UTF-8" />
<title>{{ heading_title }}</title>
<base href="{{ base }}" />
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />
<link href="view/stylesheet/bootstrap.css" rel="stylesheet" media="screen" />
<style>
@media print {
  body { margin: 0; }
  .no-print { display: none !important; }
  .page-break { page-break-before: always; }
}

body {
  font-family: Arial, sans-serif;
  font-size: 12px;
  line-height: 1.4;
}

.header {
  text-align: center;
  margin-bottom: 30px;
  border-bottom: 2px solid #333;
  padding-bottom: 20px;
}

.company-logo {
  max-height: 80px;
  margin-bottom: 10px;
}

.company-name {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}

.report-title {
  font-size: 18px;
  font-weight: bold;
  color: #666;
  margin-bottom: 10px;
}

.report-info {
  font-size: 11px;
  color: #888;
}

.summary-box {
  background-color: #f8f9fa;
  padding: 20px;
  margin-bottom: 30px;
  border: 1px solid #dee2e6;
  border-radius: 5px;
}

.vat-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 20px;
  font-size: 11px;
}

.vat-table th,
.vat-table td {
  border: 1px solid #dee2e6;
  padding: 8px;
  text-align: left;
}

.vat-table th {
  background-color: #f8f9fa;
  font-weight: bold;
}

.text-right {
  text-align: right;
}

.text-center {
  text-align: center;
}

.text-success {
  color: #28a745;
}

.text-warning {
  color: #ffc107;
}

.text-danger {
  color: #dc3545;
}

.text-info {
  color: #17a2b8;
}

.footer {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #dee2e6;
  font-size: 10px;
  color: #888;
}

.signature-section {
  margin-top: 40px;
  display: flex;
  justify-content: space-between;
}

.signature-box {
  width: 200px;
  text-align: center;
}

.signature-line {
  border-top: 1px solid #333;
  margin-top: 50px;
  padding-top: 5px;
}

.watermark {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(-45deg);
  font-size: 72px;
  color: rgba(0,0,0,0.05);
  z-index: -1;
  pointer-events: none;
}

.eta-compliance {
  background-color: #e7f3ff;
  padding: 15px;
  margin-bottom: 20px;
  border: 1px solid #b3d9ff;
  border-radius: 5px;
}

.vat-breakdown {
  margin-bottom: 30px;
}

.breakdown-item {
  display: flex;
  justify-content: space-between;
  padding: 5px 0;
  border-bottom: 1px dotted #ccc;
}
</style>
</head>
<body>

<!-- Watermark -->
<div class="watermark">{{ text_confidential }}</div>

<!-- Header -->
<div class="header">
  {% if company_logo %}
  <img src="{{ company_logo }}" alt="{{ company_name }}" class="company-logo">
  {% endif %}
  <div class="company-name">{{ company_name }}</div>
  <div class="report-title">{{ heading_title }}</div>
  <div class="report-info">
    {{ text_period }}: {{ date_start }} {{ text_to }} {{ date_end }} | {{ text_generated_on }}: {{ generated_date }}
  </div>
</div>

<!-- ETA Compliance Status -->
<div class="eta-compliance">
  <h3 style="margin-top: 0; color: #0066cc;">{{ text_eta_compliance_status }}</h3>
  <div style="display: flex; justify-content: space-between;">
    <div>
      <strong>{{ text_total_invoices }}:</strong> {{ eta_compliance.total_invoices }}<br>
      <strong>{{ text_submitted_invoices }}:</strong> {{ eta_compliance.submitted_invoices }}
    </div>
    <div>
      <strong>{{ text_approved_invoices }}:</strong> {{ eta_compliance.approved_invoices }}<br>
      <strong>{{ text_compliance_rate }}:</strong> {{ eta_compliance.compliance_rate }}%
    </div>
  </div>
</div>

<!-- VAT Summary -->
<div class="summary-box">
  <h3 style="margin-top: 0;">{{ text_vat_summary }}</h3>
  <div style="display: flex; justify-content: space-between;">
    <div>
      <strong>{{ text_vat_sales }}:</strong> {{ basic_data.vat_sales }}<br>
      <strong>{{ text_vat_purchases }}:</strong> {{ basic_data.vat_purchases }}
    </div>
    <div>
      <strong>{{ text_net_vat }}:</strong> 
      <span class="{% if basic_data.net_vat_raw >= 0 %}text-success{% else %}text-info{% endif %}">
        {{ basic_data.net_vat }}
      </span>
    </div>
    <div>
      <strong>{{ text_generated_by }}:</strong> {{ generated_by }}<br>
      <strong>{{ text_report_type }}:</strong> {{ text_detailed_vat_report }}
    </div>
  </div>
</div>

<!-- VAT Breakdown by Rates -->
<div class="vat-breakdown">
  <h3>{{ text_vat_breakdown_by_rates }}</h3>
  <table class="vat-table">
    <thead>
      <tr>
        <th style="width: 15%;">{{ column_vat_rate }}</th>
        <th style="width: 25%;">{{ column_description }}</th>
        <th style="width: 15%;" class="text-right">{{ column_sales_vat }}</th>
        <th style="width: 15%;" class="text-right">{{ column_purchases_vat }}</th>
        <th style="width: 15%;" class="text-right">{{ column_net_vat }}</th>
        <th style="width: 15%;" class="text-center">{{ column_transactions }}</th>
      </tr>
    </thead>
    <tbody>
      {% for rate, data in detailed_breakdown %}
      <tr>
        <td>{{ data.rate }}</td>
        <td>{{ data.description }}</td>
        <td class="text-right">{{ data.sales_vat|number_format(2) }}</td>
        <td class="text-right">{{ data.purchases_vat|number_format(2) }}</td>
        <td class="text-right {% if data.net_vat >= 0 %}text-success{% else %}text-info{% endif %}">
          {{ data.net_vat|number_format(2) }}
        </td>
        <td class="text-center">{{ data.sales_transactions + data.purchases_transactions }}</td>
      </tr>
      {% endfor %}
    </tbody>
    <tfoot>
      <tr style="background-color: #e2e3e5; font-weight: bold;">
        <td colspan="2">{{ text_total }}</td>
        <td class="text-right">{{ basic_data.vat_sales }}</td>
        <td class="text-right">{{ basic_data.vat_purchases }}</td>
        <td class="text-right {% if basic_data.net_vat_raw >= 0 %}text-success{% else %}text-info{% endif %}">
          {{ basic_data.net_vat }}
        </td>
        <td class="text-center">{{ total_transactions }}</td>
      </tr>
    </tfoot>
  </table>
</div>

<!-- VAT Reconciliation -->
<div class="vat-breakdown">
  <h3>{{ text_vat_reconciliation }}</h3>
  <table class="vat-table">
    <thead>
      <tr>
        <th style="width: 50%;">{{ column_source }}</th>
        <th style="width: 25%;" class="text-right">{{ column_amount }}</th>
        <th style="width: 25%;" class="text-center">{{ column_status }}</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>{{ text_journal_entries_vat }}</td>
        <td class="text-right">{{ vat_reconciliation.journal_entries_vat|number_format(2) }}</td>
        <td class="text-center">✓</td>
      </tr>
      <tr>
        <td>{{ text_electronic_invoices_vat }}</td>
        <td class="text-right">{{ vat_reconciliation.electronic_invoices_vat|number_format(2) }}</td>
        <td class="text-center">✓</td>
      </tr>
      <tr>
        <td>{{ text_manual_adjustments }}</td>
        <td class="text-right">{{ vat_reconciliation.manual_adjustments|number_format(2) }}</td>
        <td class="text-center">✓</td>
      </tr>
      <tr style="background-color: {% if vat_reconciliation.reconciliation_status == 'متطابق' %}#d4edda{% else %}#f8d7da{% endif %};">
        <td><strong>{{ text_variance }}</strong></td>
        <td class="text-right"><strong>{{ vat_reconciliation.variance|number_format(2) }}</strong></td>
        <td class="text-center">
          {% if vat_reconciliation.reconciliation_status == 'متطابق' %}
            <span style="color: #28a745;">✓ {{ text_reconciled }}</span>
          {% else %}
            <span style="color: #dc3545;">⚠ {{ text_needs_review }}</span>
          {% endif %}
        </td>
      </tr>
    </tbody>
  </table>
</div>

<!-- Comparative Analysis -->
{% if comparative_analysis %}
<div class="vat-breakdown">
  <h3>{{ text_comparative_analysis }}</h3>
  <table class="vat-table">
    <thead>
      <tr>
        <th style="width: 30%;">{{ column_item }}</th>
        <th style="width: 25%;" class="text-right">{{ column_current_period }}</th>
        <th style="width: 25%;" class="text-right">{{ column_previous_period }}</th>
        <th style="width: 20%;" class="text-center">{{ column_change }}</th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td>{{ text_vat_sales }}</td>
        <td class="text-right">{{ comparative_analysis.current_period.vat_sales }}</td>
        <td class="text-right">{{ comparative_analysis.previous_period.vat_sales }}</td>
        <td class="text-center {% if comparative_analysis.sales_vat_change >= 0 %}text-success{% else %}text-danger{% endif %}">
          {{ comparative_analysis.sales_vat_change }}%
        </td>
      </tr>
      <tr>
        <td>{{ text_vat_purchases }}</td>
        <td class="text-right">{{ comparative_analysis.current_period.vat_purchases }}</td>
        <td class="text-right">{{ comparative_analysis.previous_period.vat_purchases }}</td>
        <td class="text-center {% if comparative_analysis.purchases_vat_change >= 0 %}text-warning{% else %}text-success{% endif %}">
          {{ comparative_analysis.purchases_vat_change }}%
        </td>
      </tr>
      <tr>
        <td>{{ text_net_vat }}</td>
        <td class="text-right">{{ comparative_analysis.current_period.net_vat }}</td>
        <td class="text-right">{{ comparative_analysis.previous_period.net_vat }}</td>
        <td class="text-center {% if comparative_analysis.net_vat_change >= 0 %}text-success{% else %}text-info{% endif %}">
          {{ comparative_analysis.net_vat_change }}%
        </td>
      </tr>
    </tbody>
  </table>
</div>
{% endif %}

<!-- Recommendations -->
{% if recommendations %}
<div class="vat-breakdown">
  <h3>{{ text_recommendations }}</h3>
  <ul>
    {% for recommendation in recommendations %}
    <li>{{ recommendation }}</li>
    {% endfor %}
  </ul>
</div>
{% endif %}

<!-- Signatures -->
<div class="signature-section">
  <div class="signature-box">
    <div class="signature-line">{{ text_prepared_by }}</div>
  </div>
  <div class="signature-box">
    <div class="signature-line">{{ text_reviewed_by }}</div>
  </div>
  <div class="signature-box">
    <div class="signature-line">{{ text_approved_by }}</div>
  </div>
</div>

<!-- Footer -->
<div class="footer">
  <div style="display: flex; justify-content: space-between;">
    <div>
      {{ text_confidential }} - {{ text_internal_use }}
    </div>
    <div>
      {{ text_eta_compliant }} | {{ text_egyptian_tax_authority }} | {{ text_vat_report_control }}
    </div>
    <div>
      {{ text_page }} 1 {{ text_of }} 1
    </div>
  </div>
</div>

<script>
// Auto print when page loads
window.onload = function() {
  window.print();
};
</script>

</body>
</html>
