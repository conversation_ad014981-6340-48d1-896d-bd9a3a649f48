{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <h1>{{ heading_title }}</h1>
    </div>
  </div>
  <div class="container-fluid">

    {% if error_warning %}
    <div class="alert alert-danger">{{ error_warning }}</div>
    {% endif %}
    {% if success %}
    <div class="alert alert-success">{{ success }}</div>
    {% endif %}

    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title">{{ text_list }}</h3>
      </div>
      <div class="panel-body">
        <form id="filter-form" class="form-inline">
          <div class="form-group">
            <select name="filter_user_id" class="form-control">
              <option value="">{{ entry_user }}</option>
              {% for u in users %}
                <option value="{{ u.user_id }}">{{ u.username }}</option>
              {% endfor %}
            </select>
          </div>
          <div class="form-group">
            <input type="text" name="filter_action" class="form-control" placeholder="{{ entry_action }}">
          </div>
          <div class="form-group">
            <input type="text" name="filter_reference_type" class="form-control" placeholder="{{ entry_reference_type }}">
          </div>
          <div class="form-group">
            <input type="date" name="filter_date_start" class="form-control" placeholder="{{ entry_date_start }}">
          </div>
          <div class="form-group">
            <input type="date" name="filter_date_end" class="form-control" placeholder="{{ entry_date_end }}">
          </div>
          <button type="submit" class="btn btn-default">{{ button_filter }}</button>
        </form>

        <br>
        <div style="max-height:400px; overflow:auto;">
        <table class="table table-bordered table-hover">
          <thead>
            <tr>
              <th>Username</th>
              <th>Action</th>
              <th>Reference Type</th>
              <th>Reference ID</th>
              <th>Timestamp</th>
              <th>{{ button_action }}</th>
            </tr>
          </thead>
          <tbody id="audit-list">
            <tr><td colspan="6">{{ text_no_results }}</td></tr>
          </tbody>
        </table>
        </div>
        <div id="pagination" class="text-center"></div>
      </div>
    </div>
  </div>
</div>
{{ footer }}

<script>
$(document).ready(function(){
    var page = 1;
    var limit = 20;

    function loadData() {
        $('#audit-list').html('<tr><td colspan="6">Loading...</td></tr>');
        var formData = $('#filter-form').serializeArray();
        formData.push({name:'start', value:(page-1)*limit});
        formData.push({name:'limit', value:limit});

        $.ajax({
            url:'index.php?route=tool/audit_log/loadData&user_token={{ user_token }}',
            type:'post',
            dataType:'json',
            data:formData,
            success:function(json){
                if(json.logs && json.logs.length) {
                    var html = '';
                    for(var i=0; i<json.logs.length; i++){
                        var log = json.logs[i];
                        html += '<tr data-id="'+log.log_id+'">'+
                                '<td>'+ (log.username?log.username:'-') +'</td>'+
                                '<td>'+log.action+'</td>'+
                                '<td>'+log.reference_type+'</td>'+
                                '<td>'+ (log.reference_id?log.reference_id:'-') +'</td>'+
                                '<td>'+log.timestamp+'</td>'+
                                '<td><button class="btn btn-sm btn-danger btn-delete">{{ button_delete }}</button></td>'+
                                '</tr>';
                    }
                    $('#audit-list').html(html);
                } else {
                    $('#audit-list').html('<tr><td colspan="6">{{ text_no_results }}</td></tr>');
                }
                // Add delete event
                $('.btn-delete').click(function(){
                    if(!confirm('Are you sure?')) return;
                    var tr = $(this).closest('tr');
                    var id = tr.data('id');
                    $.ajax({
                        url:'index.php?route=tool/audit_log/deleteLog&user_token={{ user_token }}',
                        type:'post',
                        dataType:'json',
                        data:{log_id:id},
                        success:function(r){
                            if(r.error) alert(r.error);
                            else loadData();
                        }
                    });
                });

                // Pagination
                var total = json.total;
                var totalPages = Math.ceil(total/limit);
                var pagHtml = '';
                if (totalPages > 1) {
                    for (var p=1; p<=totalPages; p++) {
                        pagHtml += '<button class="btn '+(p==page?'btn-primary':'btn-default')+' pag-btn" data-page="'+p+'">'+p+'</button> ';
                    }
                }
                $('#pagination').html(pagHtml);
                $('.pag-btn').click(function(){
                    page = $(this).data('page');
                    loadData();
                });
            }
        });
    }

    $('#filter-form').submit(function(e){
        e.preventDefault();
        page = 1;
        loadData();
    });

    loadData(); // Initial load
});
</script>
