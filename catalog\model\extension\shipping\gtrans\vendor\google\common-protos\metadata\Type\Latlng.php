<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/type/latlng.proto

namespace GPBMetadata\Google\Type;

class Latlng
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(hex2bin(
            "0ac3010a18676f6f676c652f747970652f6c61746c6e672e70726f746f12" .
            "0b676f6f676c652e74797065222d0a064c61744c6e6712100a086c617469" .
            "7475646518012001280112110a096c6f6e67697475646518022001280142" .
            "630a0f636f6d2e676f6f676c652e74797065420b4c61744c6e6750726f74" .
            "6f50015a38676f6f676c652e676f6c616e672e6f72672f67656e70726f74" .
            "6f2f676f6f676c65617069732f747970652f6c61746c6e673b6c61746c6e" .
            "67f80101a20203475450620670726f746f33"
        ), true);

        static::$is_initialized = true;
    }
}

