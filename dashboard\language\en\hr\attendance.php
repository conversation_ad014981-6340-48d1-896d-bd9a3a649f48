<?php
// Heading
$_['heading_title']                = 'Attendance Management';

// Text
$_['text_filter']                  = 'Filter';
$_['text_attendance_list']         = 'Attendance List';
$_['text_add_attendance']          = 'Add Attendance';
$_['text_edit_attendance']         = 'Edit Attendance';
$_['text_ajax_error']              = 'An error occurred while connecting to the server';
$_['text_confirm_delete']          = 'Are you sure you want to delete?';
$_['text_employee']                = 'Employee';
$_['text_select_employee']         = 'Select Employee';
$_['text_date']                    = 'Date';
$_['text_date_start']              = 'Start Date';
$_['text_date_end']                = 'End Date';
$_['text_checkin']                 = 'Check In';
$_['text_checkout']                = 'Check Out';
$_['text_status']                  = 'Status';
$_['text_notes']                   = 'Notes';
$_['text_present']                 = 'Present';
$_['text_absent']                  = 'Absent';
$_['text_late']                    = 'Late';
$_['text_on_leave']                = 'On Leave';

// Buttons
$_['button_filter']                = 'Search';
$_['button_reset']                 = 'Reset';
$_['button_add_attendance']        = 'Add Attendance';
$_['button_close']                 = 'Close';
$_['button_save']                  = 'Save';

// Columns
$_['column_employee']              = 'Employee';
$_['column_date']                  = 'Date';
$_['column_checkin']               = 'Check In';
$_['column_checkout']              = 'Check Out';
$_['column_status']                = 'Status';
$_['column_actions']               = 'Actions';

// Errors/Success
$_['error_not_found']              = 'Record not found!';
$_['error_invalid_request']        = 'Invalid request!';
$_['error_permission']             = 'Warning: You do not have permission to modify attendance!';
$_['error_required']               = 'Warning: Please fill in the required fields!';
$_['text_success_add']             = 'Attendance added successfully!';
$_['text_success_edit']            = 'Attendance updated successfully!';
$_['text_success_delete']          = 'Attendance deleted successfully!';

// Navigation
$_['text_home']                    = 'Home';