{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="submit" form="finance\cash-form" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fas fa-save"></i></button>
        <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-light"><i class="fas fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    
    <div class="card">
      <div class="card-header"><i class="fas fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}chart-bar{% elif screen_type == 'settings' %}cog{% else %}pencil-alt{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="card-body">
        <form id="finance\cash-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-add">{{ text_add }}</label>
            <div class="col-sm-10">
              <input type="text" name="add" value="{{ add }}" placeholder="{{ text_add }}" id="input-add" class="form-control" />
              {% if error_add %}
                <div class="invalid-feedback">{{ error_add }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-add_transaction">{{ text_add_transaction }}</label>
            <div class="col-sm-10">
              <input type="text" name="add_transaction" value="{{ add_transaction }}" placeholder="{{ text_add_transaction }}" id="input-add_transaction" class="form-control" />
              {% if error_add_transaction %}
                <div class="invalid-feedback">{{ error_add_transaction }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-amount">{{ text_amount }}</label>
            <div class="col-sm-10">
              <input type="text" name="amount" value="{{ amount }}" placeholder="{{ text_amount }}" id="input-amount" class="form-control" />
              {% if error_amount %}
                <div class="invalid-feedback">{{ error_amount }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-back">{{ text_back }}</label>
            <div class="col-sm-10">
              <input type="text" name="back" value="{{ back }}" placeholder="{{ text_back }}" id="input-back" class="form-control" />
              {% if error_back %}
                <div class="invalid-feedback">{{ error_back }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-button_add">{{ text_button_add }}</label>
            <div class="col-sm-10">
              <input type="text" name="button_add" value="{{ button_add }}" placeholder="{{ text_button_add }}" id="input-button_add" class="form-control" />
              {% if error_button_add %}
                <div class="invalid-feedback">{{ error_button_add }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-button_delete">{{ text_button_delete }}</label>
            <div class="col-sm-10">
              <input type="text" name="button_delete" value="{{ button_delete }}" placeholder="{{ text_button_delete }}" id="input-button_delete" class="form-control" />
              {% if error_button_delete %}
                <div class="invalid-feedback">{{ error_button_delete }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-button_edit">{{ text_button_edit }}</label>
            <div class="col-sm-10">
              <input type="text" name="button_edit" value="{{ button_edit }}" placeholder="{{ text_button_edit }}" id="input-button_edit" class="form-control" />
              {% if error_button_edit %}
                <div class="invalid-feedback">{{ error_button_edit }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-cash_box">{{ text_cash_box }}</label>
            <div class="col-sm-10">
              <input type="text" name="cash_box" value="{{ cash_box }}" placeholder="{{ text_cash_box }}" id="input-cash_box" class="form-control" />
              {% if error_cash_box %}
                <div class="invalid-feedback">{{ error_cash_box }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-cash_box_id">{{ text_cash_box_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="cash_box_id" value="{{ cash_box_id }}" placeholder="{{ text_cash_box_id }}" id="input-cash_box_id" class="form-control" />
              {% if error_cash_box_id %}
                <div class="invalid-feedback">{{ error_cash_box_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-cash_boxes">{{ text_cash_boxes }}</label>
            <div class="col-sm-10">
              <input type="text" name="cash_boxes" value="{{ cash_boxes }}" placeholder="{{ text_cash_boxes }}" id="input-cash_boxes" class="form-control" />
              {% if error_cash_boxes %}
                <div class="invalid-feedback">{{ error_cash_boxes }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-code">{{ text_code }}</label>
            <div class="col-sm-10">
              <input type="text" name="code" value="{{ code }}" placeholder="{{ text_code }}" id="input-code" class="form-control" />
              {% if error_code %}
                <div class="invalid-feedback">{{ error_code }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_action">{{ text_column_action }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_action" value="{{ column_action }}" placeholder="{{ text_column_action }}" id="input-column_action" class="form-control" />
              {% if error_column_action %}
                <div class="invalid-feedback">{{ error_column_action }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_balance">{{ text_column_balance }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_balance" value="{{ column_balance }}" placeholder="{{ text_column_balance }}" id="input-column_balance" class="form-control" />
              {% if error_column_balance %}
                <div class="invalid-feedback">{{ error_column_balance }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_code">{{ text_column_code }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_code" value="{{ column_code }}" placeholder="{{ text_column_code }}" id="input-column_code" class="form-control" />
              {% if error_column_code %}
                <div class="invalid-feedback">{{ error_column_code }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_name">{{ text_column_name }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_name" value="{{ column_name }}" placeholder="{{ text_column_name }}" id="input-column_name" class="form-control" />
              {% if error_column_name %}
                <div class="invalid-feedback">{{ error_column_name }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_status">{{ text_column_status }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_status" value="{{ column_status }}" placeholder="{{ text_column_status }}" id="input-column_status" class="form-control" />
              {% if error_column_status %}
                <div class="invalid-feedback">{{ error_column_status }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-delete">{{ text_delete }}</label>
            <div class="col-sm-10">
              <input type="text" name="delete" value="{{ delete }}" placeholder="{{ text_delete }}" id="input-delete" class="form-control" />
              {% if error_delete %}
                <div class="invalid-feedback">{{ error_delete }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-description">{{ text_description }}</label>
            <div class="col-sm-10">
              <input type="text" name="description" value="{{ description }}" placeholder="{{ text_description }}" id="input-description" class="form-control" />
              {% if error_description %}
                <div class="invalid-feedback">{{ error_description }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_code">{{ text_error_code }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_code" value="{{ error_code }}" placeholder="{{ text_error_code }}" id="input-error_code" class="form-control" />
              {% if error_error_code %}
                <div class="invalid-feedback">{{ error_error_code }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_name">{{ text_error_name }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_name" value="{{ error_name }}" placeholder="{{ text_error_name }}" id="input-error_name" class="form-control" />
              {% if error_error_name %}
                <div class="invalid-feedback">{{ error_error_name }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="invalid-feedback">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-name">{{ text_name }}</label>
            <div class="col-sm-10">
              <input type="text" name="name" value="{{ name }}" placeholder="{{ text_name }}" id="input-name" class="form-control" />
              {% if error_name %}
                <div class="invalid-feedback">{{ error_name }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-reference_number">{{ text_reference_number }}</label>
            <div class="col-sm-10">
              <input type="text" name="reference_number" value="{{ reference_number }}" placeholder="{{ text_reference_number }}" id="input-reference_number" class="form-control" />
              {% if error_reference_number %}
                <div class="invalid-feedback">{{ error_reference_number }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-responsible_user_id">{{ text_responsible_user_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="responsible_user_id" value="{{ responsible_user_id }}" placeholder="{{ text_responsible_user_id }}" id="input-responsible_user_id" class="form-control" />
              {% if error_responsible_user_id %}
                <div class="invalid-feedback">{{ error_responsible_user_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-status">{{ text_status }}</label>
            <div class="col-sm-10">
              <input type="text" name="status" value="{{ status }}" placeholder="{{ text_status }}" id="input-status" class="form-control" />
              {% if error_status %}
                <div class="invalid-feedback">{{ error_status }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_confirm">{{ text_text_confirm }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_confirm" value="{{ text_confirm }}" placeholder="{{ text_text_confirm }}" id="input-text_confirm" class="form-control" />
              {% if error_text_confirm %}
                <div class="invalid-feedback">{{ error_text_confirm }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_form">{{ text_text_form }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_form" value="{{ text_form }}" placeholder="{{ text_text_form }}" id="input-text_form" class="form-control" />
              {% if error_text_form %}
                <div class="invalid-feedback">{{ error_text_form }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_list">{{ text_text_list }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_list" value="{{ text_list }}" placeholder="{{ text_text_list }}" id="input-text_list" class="form-control" />
              {% if error_text_list %}
                <div class="invalid-feedback">{{ error_text_list }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_no_results">{{ text_text_no_results }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_no_results" value="{{ text_no_results }}" placeholder="{{ text_text_no_results }}" id="input-text_no_results" class="form-control" />
              {% if error_text_no_results %}
                <div class="invalid-feedback">{{ error_text_no_results }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-transaction_date">{{ text_transaction_date }}</label>
            <div class="col-sm-10">
              <input type="text" name="transaction_date" value="{{ transaction_date }}" placeholder="{{ text_transaction_date }}" id="input-transaction_date" class="form-control" />
              {% if error_transaction_date %}
                <div class="invalid-feedback">{{ error_transaction_date }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-transaction_type">{{ text_transaction_type }}</label>
            <div class="col-sm-10">
              <input type="text" name="transaction_type" value="{{ transaction_type }}" placeholder="{{ text_transaction_type }}" id="input-transaction_type" class="form-control" />
              {% if error_transaction_type %}
                <div class="invalid-feedback">{{ error_transaction_type }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-transactions">{{ text_transactions }}</label>
            <div class="col-sm-10">
              <input type="text" name="transactions" value="{{ transactions }}" placeholder="{{ text_transactions }}" id="input-transactions" class="form-control" />
              {% if error_transactions %}
                <div class="invalid-feedback">{{ error_transactions }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="invalid-feedback">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-users">{{ text_users }}</label>
            <div class="col-sm-10">
              <input type="text" name="users" value="{{ users }}" placeholder="{{ text_users }}" id="input-users" class="form-control" />
              {% if error_users %}
                <div class="invalid-feedback">{{ error_users }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}