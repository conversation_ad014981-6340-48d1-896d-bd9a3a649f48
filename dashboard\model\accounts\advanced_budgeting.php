<?php
/**
 * نموذج الموازنات المتقدمة
 * نظام موازنات متطور يدعم الموافقات متعددة المستويات وتحليل الانحرافات
 * مع تكامل كامل مع محرر سير العمل المرئي
 */
class ModelAccountsAdvancedBudgeting extends Model {
    private $central_service;

    public function __construct($registry) {
        parent::__construct($registry);
        
        // تحميل الخدمات المركزية
        $this->load->model('core/central_service_manager');
        $this->central_service = $this->model_core_central_service_manager;
    }

    /**
     * إنشاء موازنة جديدة
     */
    public function createBudget($data) {
        $this->db->query("START TRANSACTION");

        try {
            // إنشاء الموازنة الأساسية
            $reference = $this->generateBudgetReference($data['budget_type']);
            
            $this->db->query("INSERT INTO cod_advanced_budget SET 
                reference = '" . $this->db->escape($reference) . "',
                budget_name = '" . $this->db->escape($data['budget_name']) . "',
                budget_type = '" . $this->db->escape($data['budget_type']) . "',
                fiscal_year = '" . (int)$data['fiscal_year'] . "',
                period_start = '" . $this->db->escape($data['period_start']) . "',
                period_end = '" . $this->db->escape($data['period_end']) . "',
                currency = '" . $this->db->escape($data['currency']) . "',
                approval_workflow_id = '" . (int)$data['approval_workflow_id'] . "',
                status = 'draft',
                created_by = '" . (int)$this->user->getId() . "',
                date_created = NOW(),
                date_modified = NOW()
            ");

            $budget_id = $this->db->getLastId();

            // إضافة بنود الموازنة
            $total_amount = 0;
            foreach ($data['budget_lines'] as $line) {
                $this->db->query("INSERT INTO cod_advanced_budget_lines SET 
                    budget_id = '" . (int)$budget_id . "',
                    account_id = '" . (int)$line['account_id'] . "',
                    department_id = '" . (int)$line['department_id'] . "',
                    cost_center_id = '" . (int)$line['cost_center_id'] . "',
                    budget_amount = '" . (float)$line['budget_amount'] . "',
                    q1_amount = '" . (float)$line['q1_amount'] . "',
                    q2_amount = '" . (float)$line['q2_amount'] . "',
                    q3_amount = '" . (float)$line['q3_amount'] . "',
                    q4_amount = '" . (float)$line['q4_amount'] . "',
                    notes = '" . $this->db->escape($line['notes']) . "',
                    date_added = NOW()
                ");

                $total_amount += $line['budget_amount'];
            }

            // تحديث إجمالي الموازنة
            $this->db->query("UPDATE cod_advanced_budget SET 
                total_amount = '" . (float)$total_amount . "'
                WHERE budget_id = '" . (int)$budget_id . "'
            ");

            $this->db->query("COMMIT");
            return $budget_id;

        } catch (Exception $e) {
            $this->db->query("ROLLBACK");
            throw $e;
        }
    }

    /**
     * تقديم الموازنة للموافقة
     */
    public function submitBudget($budget_id, $submission_notes) {
        $this->db->query("START TRANSACTION");

        try {
            // التحقق من صحة الموازنة
            if (!$this->validateBudget($budget_id)) {
                throw new Exception('Budget validation failed');
            }

            // تحديث حالة الموازنة
            $this->db->query("UPDATE cod_advanced_budget SET 
                status = 'submitted',
                submission_notes = '" . $this->db->escape($submission_notes) . "',
                submitted_by = '" . (int)$this->user->getId() . "',
                date_submitted = NOW(),
                date_modified = NOW()
                WHERE budget_id = '" . (int)$budget_id . "'
            ");

            // إضافة سجل في تاريخ الموافقات
            $this->db->query("INSERT INTO cod_budget_approval_history SET 
                budget_id = '" . (int)$budget_id . "',
                action = 'submitted',
                notes = '" . $this->db->escape($submission_notes) . "',
                user_id = '" . (int)$this->user->getId() . "',
                date_action = NOW()
            ");

            $this->db->query("COMMIT");
            return true;

        } catch (Exception $e) {
            $this->db->query("ROLLBACK");
            throw $e;
        }
    }

    /**
     * اعتماد الموازنة
     */
    public function approveBudget($budget_id, $action, $notes) {
        $this->db->query("START TRANSACTION");

        try {
            $new_status = '';
            switch ($action) {
                case 'approve':
                    $new_status = 'approved';
                    break;
                case 'reject':
                    $new_status = 'rejected';
                    break;
                case 'request_changes':
                    $new_status = 'changes_requested';
                    break;
                default:
                    throw new Exception('Invalid approval action');
            }

            // تحديث حالة الموازنة
            $this->db->query("UPDATE cod_advanced_budget SET 
                status = '" . $this->db->escape($new_status) . "',
                approved_by = '" . (int)$this->user->getId() . "',
                date_approved = NOW(),
                date_modified = NOW()
                WHERE budget_id = '" . (int)$budget_id . "'
            ");

            // إضافة سجل في تاريخ الموافقات
            $this->db->query("INSERT INTO cod_budget_approval_history SET 
                budget_id = '" . (int)$budget_id . "',
                action = '" . $this->db->escape($action) . "',
                notes = '" . $this->db->escape($notes) . "',
                user_id = '" . (int)$this->user->getId() . "',
                date_action = NOW()
            ");

            // إذا تم الاعتماد، تفعيل الموازنة
            if ($action == 'approve') {
                $this->activateBudget($budget_id);
            }

            $this->db->query("COMMIT");
            return true;

        } catch (Exception $e) {
            $this->db->query("ROLLBACK");
            throw $e;
        }
    }

    /**
     * نسخ موازنة
     */
    public function copyBudget($source_budget_id, $new_data) {
        $this->db->query("START TRANSACTION");

        try {
            // جلب بيانات الموازنة الأصلية
            $source_budget = $this->getBudget($source_budget_id);
            if (!$source_budget) {
                throw new Exception('Source budget not found');
            }

            // إنشاء الموازنة الجديدة
            $reference = $this->generateBudgetReference($source_budget['budget_type']);
            $adjustment_factor = 1 + ($new_data['adjustment_percentage'] / 100);
            
            $this->db->query("INSERT INTO cod_advanced_budget SET 
                reference = '" . $this->db->escape($reference) . "',
                budget_name = '" . $this->db->escape($new_data['budget_name']) . "',
                budget_type = '" . $this->db->escape($source_budget['budget_type']) . "',
                fiscal_year = '" . (int)$new_data['fiscal_year'] . "',
                period_start = '" . $this->db->escape($new_data['period_start']) . "',
                period_end = '" . $this->db->escape($new_data['period_end']) . "',
                currency = '" . $this->db->escape($source_budget['currency']) . "',
                approval_workflow_id = '" . (int)$source_budget['approval_workflow_id'] . "',
                status = 'draft',
                copied_from = '" . (int)$source_budget_id . "',
                created_by = '" . (int)$this->user->getId() . "',
                date_created = NOW(),
                date_modified = NOW()
            ");

            $new_budget_id = $this->db->getLastId();

            // نسخ بنود الموازنة مع التعديل
            $source_lines = $this->getBudgetLines($source_budget_id);
            $total_amount = 0;

            foreach ($source_lines as $line) {
                $adjusted_amount = $line['budget_amount'] * $adjustment_factor;
                $adjusted_q1 = $line['q1_amount'] * $adjustment_factor;
                $adjusted_q2 = $line['q2_amount'] * $adjustment_factor;
                $adjusted_q3 = $line['q3_amount'] * $adjustment_factor;
                $adjusted_q4 = $line['q4_amount'] * $adjustment_factor;

                $this->db->query("INSERT INTO cod_advanced_budget_lines SET 
                    budget_id = '" . (int)$new_budget_id . "',
                    account_id = '" . (int)$line['account_id'] . "',
                    department_id = '" . (int)$line['department_id'] . "',
                    cost_center_id = '" . (int)$line['cost_center_id'] . "',
                    budget_amount = '" . (float)$adjusted_amount . "',
                    q1_amount = '" . (float)$adjusted_q1 . "',
                    q2_amount = '" . (float)$adjusted_q2 . "',
                    q3_amount = '" . (float)$adjusted_q3 . "',
                    q4_amount = '" . (float)$adjusted_q4 . "',
                    notes = '" . $this->db->escape($line['notes']) . "',
                    date_added = NOW()
                ");

                $total_amount += $adjusted_amount;
            }

            // تحديث إجمالي الموازنة الجديدة
            $this->db->query("UPDATE cod_advanced_budget SET 
                total_amount = '" . (float)$total_amount . "'
                WHERE budget_id = '" . (int)$new_budget_id . "'
            ");

            $this->db->query("COMMIT");
            return $new_budget_id;

        } catch (Exception $e) {
            $this->db->query("ROLLBACK");
            throw $e;
        }
    }

    /**
     * جلب الموازنات
     */
    public function getBudgets($filter = array()) {
        $sql = "SELECT ab.*, u1.username as created_by_name, u2.username as approved_by_name
                FROM cod_advanced_budget ab
                LEFT JOIN cod_user u1 ON (ab.created_by = u1.user_id)
                LEFT JOIN cod_user u2 ON (ab.approved_by = u2.user_id)
                WHERE 1=1";

        if (!empty($filter['status'])) {
            $sql .= " AND ab.status = '" . $this->db->escape($filter['status']) . "'";
        }

        if (!empty($filter['fiscal_year'])) {
            $sql .= " AND ab.fiscal_year = '" . (int)$filter['fiscal_year'] . "'";
        }

        if (!empty($filter['budget_type'])) {
            $sql .= " AND ab.budget_type = '" . $this->db->escape($filter['budget_type']) . "'";
        }

        $sql .= " ORDER BY ab.date_created DESC";

        $query = $this->db->query($sql);
        return $query->rows;
    }

    /**
     * جلب الموازنات المعلقة للموافقة
     */
    public function getPendingBudgets() {
        $query = $this->db->query("SELECT ab.*, u.username as created_by_name
                                   FROM cod_advanced_budget ab
                                   LEFT JOIN cod_user u ON (ab.created_by = u.user_id)
                                   WHERE ab.status = 'submitted'
                                   ORDER BY ab.date_submitted ASC
        ");

        return $query->rows;
    }

    /**
     * جلب إحصائيات الموازنات
     */
    public function getBudgetStatistics() {
        $stats = array();

        // إجمالي الموازنات
        $query = $this->db->query("SELECT COUNT(*) as total FROM cod_advanced_budget");
        $stats['total_budgets'] = $query->row['total'];

        // الموازنات المعتمدة
        $query = $this->db->query("SELECT COUNT(*) as approved FROM cod_advanced_budget WHERE status = 'approved'");
        $stats['approved_budgets'] = $query->row['approved'];

        // الموازنات المعلقة
        $query = $this->db->query("SELECT COUNT(*) as pending FROM cod_advanced_budget WHERE status = 'submitted'");
        $stats['pending_budgets'] = $query->row['pending'];

        // إجمالي المبالغ المعتمدة
        $query = $this->db->query("SELECT SUM(total_amount) as total_amount FROM cod_advanced_budget WHERE status = 'approved'");
        $stats['total_approved_amount'] = $query->row['total_amount'] ?? 0;

        return $stats;
    }

    /**
     * جلب تحليل الانحرافات
     */
    public function getVarianceAnalysis() {
        $current_year = date('Y');
        
        $query = $this->db->query("SELECT 
                                      abl.account_id,
                                      ad.name as account_name,
                                      SUM(abl.budget_amount) as budget_amount,
                                      COALESCE(actual.actual_amount, 0) as actual_amount,
                                      (COALESCE(actual.actual_amount, 0) - SUM(abl.budget_amount)) as variance,
                                      CASE 
                                          WHEN SUM(abl.budget_amount) > 0 THEN 
                                              ((COALESCE(actual.actual_amount, 0) - SUM(abl.budget_amount)) / SUM(abl.budget_amount)) * 100
                                          ELSE 0 
                                      END as variance_percentage
                                   FROM cod_advanced_budget_lines abl
                                   INNER JOIN cod_advanced_budget ab ON (abl.budget_id = ab.budget_id)
                                   LEFT JOIN cod_account_description ad ON (abl.account_id = ad.account_id AND ad.language_id = '" . (int)$this->config->get('config_language_id') . "')
                                   LEFT JOIN (
                                       SELECT account_id, SUM(debit - credit) as actual_amount
                                       FROM cod_journal_entries
                                       WHERE YEAR(entry_date) = '" . (int)$current_year . "'
                                       GROUP BY account_id
                                   ) actual ON (abl.account_id = actual.account_id)
                                   WHERE ab.status = 'approved' AND ab.fiscal_year = '" . (int)$current_year . "'
                                   GROUP BY abl.account_id, ad.name, actual.actual_amount
                                   ORDER BY ABS(variance) DESC
                                   LIMIT 10
        ");

        return $query->rows;
    }

    /**
     * جلب بيانات موازنة محددة
     */
    public function getBudget($budget_id) {
        $query = $this->db->query("SELECT ab.*, u1.username as created_by_name, u2.username as approved_by_name
                                   FROM cod_advanced_budget ab
                                   LEFT JOIN cod_user u1 ON (ab.created_by = u1.user_id)
                                   LEFT JOIN cod_user u2 ON (ab.approved_by = u2.user_id)
                                   WHERE ab.budget_id = '" . (int)$budget_id . "'
        ");

        return $query->num_rows ? $query->row : null;
    }

    /**
     * جلب بنود الموازنة
     */
    public function getBudgetLines($budget_id) {
        $query = $this->db->query("SELECT abl.*, ad.name as account_name, a.code as account_code,
                                          d.name as department_name, cc.name as cost_center_name
                                   FROM cod_advanced_budget_lines abl
                                   LEFT JOIN cod_accounts a ON (abl.account_id = a.account_id)
                                   LEFT JOIN cod_account_description ad ON (a.account_id = ad.account_id AND ad.language_id = '" . (int)$this->config->get('config_language_id') . "')
                                   LEFT JOIN cod_departments d ON (abl.department_id = d.department_id)
                                   LEFT JOIN cod_cost_centers cc ON (abl.cost_center_id = cc.cost_center_id)
                                   WHERE abl.budget_id = '" . (int)$budget_id . "'
                                   ORDER BY ad.name ASC
        ");

        return $query->rows;
    }

    /**
     * دوال مساعدة
     */
    private function generateBudgetReference($type) {
        $type_prefix = strtoupper(substr($type, 0, 3));
        $year = date('Y');
        
        $query = $this->db->query("SELECT reference FROM cod_advanced_budget 
            WHERE reference LIKE '" . $type_prefix . "-" . $year . "%' 
            ORDER BY reference DESC LIMIT 1
        ");

        if ($query->num_rows) {
            $last_number = (int)substr($query->row['reference'], -4);
            $new_number = $last_number + 1;
        } else {
            $new_number = 1;
        }

        return $type_prefix . '-' . $year . '-' . str_pad($new_number, 4, '0', STR_PAD_LEFT);
    }

    private function validateBudget($budget_id) {
        // التحقق من وجود بنود في الموازنة
        $query = $this->db->query("SELECT COUNT(*) as line_count FROM cod_advanced_budget_lines WHERE budget_id = '" . (int)$budget_id . "'");
        
        if ($query->row['line_count'] == 0) {
            return false;
        }

        // التحقق من صحة المبالغ
        $query = $this->db->query("SELECT COUNT(*) as invalid_lines FROM cod_advanced_budget_lines WHERE budget_id = '" . (int)$budget_id . "' AND budget_amount <= 0");
        
        if ($query->row['invalid_lines'] > 0) {
            return false;
        }

        return true;
    }

    private function activateBudget($budget_id) {
        // تفعيل الموازنة وإنشاء القيود المحاسبية إذا لزم الأمر
        $this->db->query("UPDATE cod_advanced_budget SET 
            status = 'active',
            date_activated = NOW()
            WHERE budget_id = '" . (int)$budget_id . "'
        ");
    }

    public function getDepartments() {
        $query = $this->db->query("SELECT * FROM cod_departments WHERE status = '1' ORDER BY name ASC");
        return $query->rows;
    }

    public function getCostCenters() {
        $query = $this->db->query("SELECT * FROM cod_cost_centers WHERE status = '1' ORDER BY name ASC");
        return $query->rows;
    }

    public function getApprovalHistory($budget_id) {
        $query = $this->db->query("SELECT bah.*, u.username
                                   FROM cod_budget_approval_history bah
                                   LEFT JOIN cod_user u ON (bah.user_id = u.user_id)
                                   WHERE bah.budget_id = '" . (int)$budget_id . "'
                                   ORDER BY bah.date_action DESC
        ");

        return $query->rows;
    }

    public function getVarianceAnalysisDetailed($budget_id, $period = null) {
        $where_period = '';
        if ($period) {
            $where_period = " AND QUARTER(jel.entry_date) = '" . (int)$period . "'";
        }

        $query = $this->db->query("SELECT 
                                      abl.*,
                                      ad.name as account_name,
                                      a.code as account_code,
                                      COALESCE(actual.actual_amount, 0) as actual_amount,
                                      (COALESCE(actual.actual_amount, 0) - abl.budget_amount) as variance,
                                      CASE 
                                          WHEN abl.budget_amount > 0 THEN 
                                              ((COALESCE(actual.actual_amount, 0) - abl.budget_amount) / abl.budget_amount) * 100
                                          ELSE 0 
                                      END as variance_percentage
                                   FROM cod_advanced_budget_lines abl
                                   LEFT JOIN cod_accounts a ON (abl.account_id = a.account_id)
                                   LEFT JOIN cod_account_description ad ON (a.account_id = ad.account_id AND ad.language_id = '" . (int)$this->config->get('config_language_id') . "')
                                   LEFT JOIN (
                                       SELECT account_id, SUM(debit - credit) as actual_amount
                                       FROM cod_journal_entries jel
                                       INNER JOIN cod_journals je ON (jel.journal_id = je.journal_id)
                                       WHERE 1=1 " . $where_period . "
                                       GROUP BY account_id
                                   ) actual ON (abl.account_id = actual.account_id)
                                   WHERE abl.budget_id = '" . (int)$budget_id . "'
                                   ORDER BY ad.name ASC
        ");

        return $query->rows;
    }
}
