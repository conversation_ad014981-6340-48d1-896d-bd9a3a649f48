<?php
/**
 * Enhanced Queue Model for AYM ERP
 * Advanced queue data management with statistics and monitoring
 */
class ModelQueueQueue extends Model {
    private $max_attempts = 3;
    
    /**
     * الحصول على المهام المعلقة
     */
    public function getPendingTasks() {
        $sql = "SELECT * FROM " . DB_PREFIX . "enhanced_queue_jobs 
                WHERE (status = 'pending' OR (status = 'failed' AND attempts < max_attempts))
                AND (scheduled_at IS NULL OR scheduled_at <= NOW())
                ORDER BY priority DESC, created_at ASC";
        
        $query = $this->db->query($sql);
        return $query->rows;
    }
    
    /**
     * تحديث حالة المهمة
     */
    public function updateTaskStatus($task_id, $status) {
        $sql = "UPDATE " . DB_PREFIX . "enhanced_queue_jobs 
                SET status = '" . $this->db->escape($status) . "', 
                    updated_at = NOW() 
                WHERE id = '" . (int)$task_id . "'
                AND (status = 'pending' OR (status = 'failed' AND attempts < max_attempts))";
        
        $this->db->query($sql);
        return $this->db->countAffected() > 0;
    }
    
    /**
     * زيادة عدد المحاولات
     */
    public function incrementTaskAttempts($task_id) {
        $sql = "UPDATE " . DB_PREFIX . "enhanced_queue_jobs 
                SET attempts = attempts + 1 
                WHERE id = '" . (int)$task_id . "'";
        
        $this->db->query($sql);
    }
    
    /**
     * الحصول على إحصائيات Queue
     */
    public function getQueueStats() {
        $sql = "SELECT 
                    status,
                    COUNT(*) as count,
                    AVG(processing_time) as avg_processing_time,
                    MIN(created_at) as oldest_job,
                    MAX(created_at) as newest_job
                FROM " . DB_PREFIX . "enhanced_queue_jobs 
                WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
                GROUP BY status";
        
        $query = $this->db->query($sql);
        $stats = [];
        
        foreach ($query->rows as $row) {
            $stats[$row['status']] = [
                'count' => (int)$row['count'],
                'avg_processing_time' => (float)$row['avg_processing_time'],
                'oldest_job' => $row['oldest_job'],
                'newest_job' => $row['newest_job']
            ];
        }
        
        return $stats;
    }
    
    /**
     * عدد المهام المعلقة
     */
    public function getPendingJobsCount() {
        $sql = "SELECT COUNT(*) as total FROM " . DB_PREFIX . "enhanced_queue_jobs 
                WHERE status = 'pending' 
                AND (scheduled_at IS NULL OR scheduled_at <= NOW())";
        
        $query = $this->db->query($sql);
        return (int)$query->row['total'];
    }
    
    /**
     * عدد المهام قيد المعالجة
     */
    public function getProcessingJobsCount() {
        $sql = "SELECT COUNT(*) as total FROM " . DB_PREFIX . "enhanced_queue_jobs 
                WHERE status = 'processing'";
        
        $query = $this->db->query($sql);
        return (int)$query->row['total'];
    }
    
    /**
     * عدد المهام الفاشلة
     */
    public function getFailedJobsCount() {
        $sql = "SELECT COUNT(*) as total FROM " . DB_PREFIX . "enhanced_queue_jobs 
                WHERE status = 'failed'";
        
        $query = $this->db->query($sql);
        return (int)$query->row['total'];
    }
    
    /**
     * عدد المهام المكتملة
     */
    public function getCompletedJobsCount() {
        $sql = "SELECT COUNT(*) as total FROM " . DB_PREFIX . "enhanced_queue_jobs 
                WHERE status = 'done' 
                AND completed_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)";
        
        $query = $this->db->query($sql);
        return (int)$query->row['total'];
    }
    
    /**
     * الحصول على قائمة المهام مع فلترة
     */
    public function getJobs($data = []) {
        $sql = "SELECT * FROM " . DB_PREFIX . "enhanced_queue_jobs WHERE 1=1";
        
        if (!empty($data['status'])) {
            $sql .= " AND status = '" . $this->db->escape($data['status']) . "'";
        }
        
        if (!empty($data['job_type'])) {
            $sql .= " AND job_type = '" . $this->db->escape($data['job_type']) . "'";
        }
        
        if (!empty($data['priority'])) {
            $sql .= " AND priority = '" . (int)$data['priority'] . "'";
        }
        
        if (!empty($data['date_from'])) {
            $sql .= " AND created_at >= '" . $this->db->escape($data['date_from']) . "'";
        }
        
        if (!empty($data['date_to'])) {
            $sql .= " AND created_at <= '" . $this->db->escape($data['date_to']) . " 23:59:59'";
        }
        
        $sql .= " ORDER BY created_at DESC";
        
        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }
            
            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }
            
            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }
        
        $query = $this->db->query($sql);
        return $query->rows;
    }
    
    /**
     * الحصول على العدد الإجمالي للمهام
     */
    public function getTotalJobs($data = []) {
        $sql = "SELECT COUNT(*) as total FROM " . DB_PREFIX . "enhanced_queue_jobs WHERE 1=1";
        
        if (!empty($data['status'])) {
            $sql .= " AND status = '" . $this->db->escape($data['status']) . "'";
        }
        
        if (!empty($data['job_type'])) {
            $sql .= " AND job_type = '" . $this->db->escape($data['job_type']) . "'";
        }
        
        if (!empty($data['priority'])) {
            $sql .= " AND priority = '" . (int)$data['priority'] . "'";
        }
        
        if (!empty($data['date_from'])) {
            $sql .= " AND created_at >= '" . $this->db->escape($data['date_from']) . "'";
        }
        
        if (!empty($data['date_to'])) {
            $sql .= " AND created_at <= '" . $this->db->escape($data['date_to']) . " 23:59:59'";
        }
        
        $query = $this->db->query($sql);
        return (int)$query->row['total'];
    }
    
    /**
     * إعادة تشغيل مهمة فاشلة
     */
    public function retryJob($job_id) {
        $sql = "UPDATE " . DB_PREFIX . "enhanced_queue_jobs 
                SET status = 'pending', 
                    attempts = 0, 
                    error_message = NULL,
                    started_at = NULL,
                    completed_at = NULL,
                    updated_at = NOW()
                WHERE id = '" . (int)$job_id . "'
                AND status = 'failed'";
        
        $this->db->query($sql);
        return $this->db->countAffected() > 0;
    }
    
    /**
     * الحصول على تفاصيل مهمة محددة
     */
    public function getJobDetails($job_id) {
        $sql = "SELECT * FROM " . DB_PREFIX . "enhanced_queue_jobs 
                WHERE id = '" . (int)$job_id . "'";
        
        $query = $this->db->query($sql);
        
        if ($query->num_rows > 0) {
            $job = $query->row;
            $job['job_data'] = json_decode($job['job_data'], true);
            return $job;
        }
        
        return null;
    }
    
    /**
     * الحصول على المهام حسب النوع
     */
    public function getJobsByType($job_type, $status = null, $limit = 100) {
        $sql = "SELECT * FROM " . DB_PREFIX . "enhanced_queue_jobs 
                WHERE job_type = '" . $this->db->escape($job_type) . "'";
        
        if ($status) {
            $sql .= " AND status = '" . $this->db->escape($status) . "'";
        }
        
        $sql .= " ORDER BY created_at DESC LIMIT " . (int)$limit;
        
        $query = $this->db->query($sql);
        return $query->rows;
    }
    
    /**
     * حذف المهام القديمة المكتملة
     */
    public function deleteOldCompletedJobs($days = 7) {
        $sql = "DELETE FROM " . DB_PREFIX . "enhanced_queue_jobs 
                WHERE status IN ('done', 'cancelled')
                AND completed_at < DATE_SUB(NOW(), INTERVAL " . (int)$days . " DAY)";
        
        $this->db->query($sql);
        return $this->db->countAffected();
    }
    
    /**
     * إحصائيات الأداء اليومية
     */
    public function getDailyPerformanceStats($days = 7) {
        $sql = "SELECT 
                    DATE(created_at) as date,
                    COUNT(*) as total_jobs,
                    SUM(CASE WHEN status = 'done' THEN 1 ELSE 0 END) as completed_jobs,
                    SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_jobs,
                    AVG(CASE WHEN processing_time IS NOT NULL THEN processing_time ELSE NULL END) as avg_processing_time
                FROM " . DB_PREFIX . "enhanced_queue_jobs 
                WHERE created_at >= DATE_SUB(NOW(), INTERVAL " . (int)$days . " DAY)
                GROUP BY DATE(created_at)
                ORDER BY date DESC";
        
        $query = $this->db->query($sql);
        return $query->rows;
    }
    
    /**
     * إحصائيات حسب نوع المهمة
     */
    public function getJobTypeStats($days = 7) {
        $sql = "SELECT 
                    job_type,
                    COUNT(*) as total_jobs,
                    SUM(CASE WHEN status = 'done' THEN 1 ELSE 0 END) as completed_jobs,
                    SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_jobs,
                    SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_jobs,
                    AVG(CASE WHEN processing_time IS NOT NULL THEN processing_time ELSE NULL END) as avg_processing_time
                FROM " . DB_PREFIX . "enhanced_queue_jobs 
                WHERE created_at >= DATE_SUB(NOW(), INTERVAL " . (int)$days . " DAY)
                GROUP BY job_type
                ORDER BY total_jobs DESC";
        
        $query = $this->db->query($sql);
        return $query->rows;
    }
    
    /**
     * الحصول على المهام المعلقة منذ فترة طويلة
     */
    public function getStuckJobs($timeout_minutes = 30) {
        $sql = "SELECT * FROM " . DB_PREFIX . "enhanced_queue_jobs 
                WHERE status = 'processing'
                AND started_at < DATE_SUB(NOW(), INTERVAL " . (int)$timeout_minutes . " MINUTE)
                ORDER BY started_at ASC";
        
        $query = $this->db->query($sql);
        return $query->rows;
    }
    
    /**
     * إضافة مهمة جديدة (wrapper للنظام القديم)
     */
    public function addJob($job_type, $job_data, $priority = 2, $scheduled_at = null) {
        require_once(DIR_SYSTEM . 'library/enhanced_queue.php');
        $queue = new EnhancedQueue($this->db);
        return $queue->addJob($job_type, $job_data, $priority, $scheduled_at);
    }
    
    /**
     * إحصائيات الذاكرة والأداء
     */
    public function getSystemStats() {
        $this->load->library('enhanced_queue');
        
        // Get counts for each status
        $total_query = $this->db->query("SELECT COUNT(*) as total FROM " . DB_PREFIX . "enhanced_queue_jobs");
        $pending_query = $this->db->query("SELECT COUNT(*) as total FROM " . DB_PREFIX . "enhanced_queue_jobs WHERE status = 'pending'");
        $processing_query = $this->db->query("SELECT COUNT(*) as total FROM " . DB_PREFIX . "enhanced_queue_jobs WHERE status = 'processing'");
        $completed_query = $this->db->query("SELECT COUNT(*) as total FROM " . DB_PREFIX . "enhanced_queue_jobs WHERE status = 'done'");
        $failed_query = $this->db->query("SELECT COUNT(*) as total FROM " . DB_PREFIX . "enhanced_queue_jobs WHERE status = 'failed'");
        $cancelled_query = $this->db->query("SELECT COUNT(*) as total FROM " . DB_PREFIX . "enhanced_queue_jobs WHERE status = 'cancelled'");
        
        // Get table size using the new method
        $table_size = $this->enhanced_queue->getTableSize();
        
        return [
            'total_jobs' => (int)$total_query->row['total'],
            'pending_jobs' => (int)$pending_query->row['total'],
            'processing_jobs' => (int)$processing_query->row['total'],
            'completed_jobs' => (int)$completed_query->row['total'],
            'failed_jobs' => (int)$failed_query->row['total'],
            'cancelled_jobs' => (int)$cancelled_query->row['total'],
            'table_size_mb' => $table_size
        ];
    }
}
?>