{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <a href="{{ cancel }}" class="btn btn-default"><i class="fa fa-reply"></i> {{ button_cancel }}</a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-database"></i> {{ text_database_setup }}</h3>
      </div>
      <div class="panel-body">
        <p>{{ text_setup_info }}</p>
        
        <div class="well">
          <h4>{{ text_workflow_tables }}</h4>
          <pre>
-- Main workflow table
CREATE TABLE `{{ db_prefix }}workflow` (
  `workflow_id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` text,
  `workflow_data` mediumtext NOT NULL,
  `status` tinyint(1) NOT NULL DEFAULT '0',
  `date_added` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `date_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`workflow_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Workflow instances
CREATE TABLE `{{ db_prefix }}workflow_instance` (
  `instance_id` int(11) NOT NULL AUTO_INCREMENT,
  `workflow_id` int(11) NOT NULL,
  `reference_id` int(11) NOT NULL,
  `reference_type` varchar(50) NOT NULL,
  `status` varchar(20) NOT NULL DEFAULT 'pending',
  `current_step` varchar(50) NOT NULL,
  `instance_data` mediumtext,
  `created_by` int(11) NOT NULL,
  `date_created` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `date_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`instance_id`),
  KEY `workflow_id` (`workflow_id`),
  KEY `reference_id` (`reference_id`,`reference_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Workflow tasks (for task nodes)
CREATE TABLE `{{ db_prefix }}workflow_task` (
  `task_id` int(11) NOT NULL AUTO_INCREMENT,
  `instance_id` int(11) NOT NULL,
  `node_id` varchar(50) NOT NULL,
  `assignee_type` varchar(20) NOT NULL DEFAULT 'user',
  `assignee_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text,
  `status` varchar(20) NOT NULL DEFAULT 'pending',
  `comment` text,
  `due_date` datetime DEFAULT NULL,
  `date_added` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `date_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`task_id`),
  KEY `instance_id` (`instance_id`),
  KEY `assignee_type` (`assignee_type`,`assignee_id`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Workflow history/logs
CREATE TABLE `{{ db_prefix }}workflow_history` (
  `history_id` int(11) NOT NULL AUTO_INCREMENT,
  `instance_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `node_id` varchar(50) DEFAULT NULL,
  `action` varchar(50) NOT NULL,
  `data` text,
  `date_added` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`history_id`),
  KEY `instance_id` (`instance_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Workflow schedule (for delay nodes)
CREATE TABLE `{{ db_prefix }}workflow_schedule` (
  `schedule_id` int(11) NOT NULL AUTO_INCREMENT,
  `instance_id` int(11) NOT NULL,
  `wake_time` datetime NOT NULL,
  `status` varchar(20) NOT NULL DEFAULT 'scheduled',
  `date_added` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `date_modified` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`schedule_id`),
  KEY `instance_id` (`instance_id`),
  KEY `wake_time` (`wake_time`,`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
          </pre>
        </div>
        
        <div class="alert alert-info">
          <i class="fa fa-info-circle"></i> {{ text_execution_instructions }}
        </div>
        
        <div class="buttons">
          <a href="{{ workflow_list }}" class="btn btn-primary">{{ button_workflow_list }}</a>
        </div>
      </div>
    </div>
  </div>
</div>
{{ footer }} 