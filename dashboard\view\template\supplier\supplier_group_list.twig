{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <a href="{{ add }}" data-toggle="tooltip" title="{{ button_add }}" class="btn btn-primary"><i class="fa fa-plus"></i></a>
        <button type="button" data-toggle="tooltip" title="{{ button_delete }}" class="btn btn-danger" onclick="confirm('{{ text_confirm }}') ? $('#form-supplier-group').submit() : false;"><i class="fa fa-trash-o"></i></button>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    {% if success %}
    <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-list"></i> {{ text_list }}</h3>
      </div>
      <div class="panel-body">
        <form action="{{ delete }}" method="post" enctype="multipart/form-data" id="form-supplier-group">
          <div class="table-responsive">
            <table class="table table-bordered table-hover">
              <thead>
                <tr>
                  <td style="width: 1px;" class="text-center"><input type="checkbox" onclick="$('input[name*=\'selected\']').prop('checked', this.checked);" /></td>
                  <td class="text-left">{% if sort == 'sgd.name' %}<a href="{{ sort_name }}" class="{% if order == 'ASC' %}asc{% else %}desc{% endif %}">{{ column_name }}</a>{% else %}<a href="{{ sort_name }}">{{ column_name }}</a>{% endif %}</td>
                  <td class="text-left">{{ column_description }}</td>
                  <td class="text-center">{{ column_approval }}</td>
                  <td class="text-right">{% if sort == 'sg.sort_order' %}<a href="{{ sort_sort_order }}" class="{% if order == 'ASC' %}asc{% else %}desc{% endif %}">{{ column_sort_order }}</a>{% else %}<a href="{{ sort_sort_order }}">{{ column_sort_order }}</a>{% endif %}</td>
                  <td class="text-center">{{ column_supplier_count }}</td>
                  <td class="text-right">{{ column_action }}</td>
                </tr>
              </thead>
              <tbody>
                {% if supplier_groups %}
                {% for supplier_group in supplier_groups %}
                <tr>
                  <td class="text-center">{% if supplier_group.selected %}<input type="checkbox" name="selected[]" value="{{ supplier_group.supplier_group_id }}" checked="checked" />{% else %}<input type="checkbox" name="selected[]" value="{{ supplier_group.supplier_group_id }}" />{% endif %}</td>
                  <td class="text-left">{{ supplier_group.name }}</td>
                  <td class="text-left">{{ supplier_group.description }}</td>
                  <td class="text-center">
                    {% if supplier_group.approval %}
                    <span class="label label-success">{{ text_yes }}</span>
                    {% else %}
                    <span class="label label-default">{{ text_no }}</span>
                    {% endif %}
                  </td>
                  <td class="text-right">{{ supplier_group.sort_order }}</td>
                  <td class="text-center">
                    <span class="badge">{{ supplier_group.supplier_count|default(0) }}</span>
                  </td>
                  <td class="text-right">
                    <div class="btn-group">
                      <a href="{{ supplier_group.edit }}" data-toggle="tooltip" title="{{ button_edit }}" class="btn btn-primary btn-sm"><i class="fa fa-pencil"></i></a>
                      <button type="button" data-toggle="dropdown" class="btn btn-primary btn-sm dropdown-toggle"><span class="caret"></span></button>
                      <ul class="dropdown-menu dropdown-menu-right">
                        <li><a href="{{ supplier_group.edit }}"><i class="fa fa-pencil"></i> {{ button_edit }}</a></li>
                        <li><a href="#" onclick="copyGroup({{ supplier_group.supplier_group_id }})"><i class="fa fa-copy"></i> {{ button_copy }}</a></li>
                        <li class="divider"></li>
                        <li><a href="#" onclick="toggleApproval({{ supplier_group.supplier_group_id }})"><i class="fa fa-toggle-on"></i> {{ button_toggle_approval }}</a></li>
                        <li><a href="#" onclick="setDefault({{ supplier_group.supplier_group_id }})"><i class="fa fa-star"></i> {{ button_set_default }}</a></li>
                        <li class="divider"></li>
                        <li><a href="#" onclick="moveSuppliers({{ supplier_group.supplier_group_id }})"><i class="fa fa-arrows"></i> {{ button_move_suppliers }}</a></li>
                      </ul>
                    </div>
                  </td>
                </tr>
                {% endfor %}
                {% else %}
                <tr>
                  <td class="text-center" colspan="7">{{ text_no_results }}</td>
                </tr>
                {% endif %}
              </tbody>
            </table>
          </div>
        </form>
        <div class="row">
          <div class="col-sm-6 text-left">{{ pagination }}</div>
          <div class="col-sm-6 text-right">{{ results }}</div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Copy Group Modal -->
<div id="modal-copy-group" class="modal fade" tabindex="-1" role="dialog">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
        <h4 class="modal-title">{{ button_copy }}</h4>
      </div>
      <div class="modal-body">
        <p>{{ modal_copy_text }}</p>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_cancel }}</button>
        <button type="button" class="btn btn-primary" onclick="confirmCopy()">{{ button_copy }}</button>
      </div>
    </div>
  </div>
</div>

<!-- Move Suppliers Modal -->
<div id="modal-move-suppliers" class="modal fade" tabindex="-1" role="dialog">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
        <h4 class="modal-title">{{ button_move_suppliers }}</h4>
      </div>
      <div class="modal-body">
        <form id="form-move-suppliers">
          <input type="hidden" name="from_group_id" id="from-group-id" value="" />
          <div class="form-group">
            <label for="to-group-id">{{ modal_move_text }}</label>
            <select name="to_group_id" id="to-group-id" class="form-control">
              {% for supplier_group in supplier_groups %}
              <option value="{{ supplier_group.supplier_group_id }}">{{ supplier_group.name }}</option>
              {% endfor %}
            </select>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_cancel }}</button>
        <button type="button" class="btn btn-primary" onclick="confirmMove()">{{ button_move_suppliers }}</button>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript"><!--
var copy_group_id = 0;

function copyGroup(supplier_group_id) {
    copy_group_id = supplier_group_id;
    $('#modal-copy-group').modal('show');
}

function confirmCopy() {
    $.ajax({
        url: 'index.php?route=supplier/supplier_group/copy&user_token={{ user_token }}',
        type: 'post',
        data: {supplier_group_id: copy_group_id},
        dataType: 'json',
        beforeSend: function() {
            $('#modal-copy-group .btn-primary').button('loading');
        },
        complete: function() {
            $('#modal-copy-group .btn-primary').button('reset');
        },
        success: function(json) {
            $('.alert-dismissible').remove();
            
            if (json['error']) {
                $('#content > .container-fluid').prepend('<div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> ' + json['error'] + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
            }
            
            if (json['success']) {
                $('#content > .container-fluid').prepend('<div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> ' + json['success'] + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
                
                $('#modal-copy-group').modal('hide');
                
                setTimeout(function() {
                    location.reload();
                }, 1000);
            }
        },
        error: function(xhr, ajaxOptions, thrownError) {
            alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
    });
}

function toggleApproval(supplier_group_id) {
    $.ajax({
        url: 'index.php?route=supplier/supplier_group/toggle&user_token={{ user_token }}',
        type: 'post',
        data: {supplier_group_id: supplier_group_id},
        dataType: 'json',
        success: function(json) {
            $('.alert-dismissible').remove();
            
            if (json['error']) {
                $('#content > .container-fluid').prepend('<div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> ' + json['error'] + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
            }
            
            if (json['success']) {
                $('#content > .container-fluid').prepend('<div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> ' + json['success'] + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
                
                setTimeout(function() {
                    location.reload();
                }, 1000);
            }
        },
        error: function(xhr, ajaxOptions, thrownError) {
            alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
    });
}

function setDefault(supplier_group_id) {
    if (confirm('{{ text_confirm }}')) {
        $.ajax({
            url: 'index.php?route=supplier/supplier_group/setDefault&user_token={{ user_token }}',
            type: 'post',
            data: {supplier_group_id: supplier_group_id},
            dataType: 'json',
            success: function(json) {
                $('.alert-dismissible').remove();
                
                if (json['error']) {
                    $('#content > .container-fluid').prepend('<div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> ' + json['error'] + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
                }
                
                if (json['success']) {
                    $('#content > .container-fluid').prepend('<div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> ' + json['success'] + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
                    
                    setTimeout(function() {
                        location.reload();
                    }, 1000);
                }
            },
            error: function(xhr, ajaxOptions, thrownError) {
                alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
            }
        });
    }
}

function moveSuppliers(supplier_group_id) {
    $('#from-group-id').val(supplier_group_id);
    $('#to-group-id option[value="' + supplier_group_id + '"]').prop('disabled', true);
    $('#modal-move-suppliers').modal('show');
}

function confirmMove() {
    $.ajax({
        url: 'index.php?route=supplier/supplier_group/moveSuppliers&user_token={{ user_token }}',
        type: 'post',
        data: $('#form-move-suppliers').serialize(),
        dataType: 'json',
        beforeSend: function() {
            $('#modal-move-suppliers .btn-primary').button('loading');
        },
        complete: function() {
            $('#modal-move-suppliers .btn-primary').button('reset');
        },
        success: function(json) {
            $('.alert-dismissible').remove();
            
            if (json['error']) {
                $('#content > .container-fluid').prepend('<div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> ' + json['error'] + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
            }
            
            if (json['success']) {
                $('#content > .container-fluid').prepend('<div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> ' + json['success'] + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
                
                $('#modal-move-suppliers').modal('hide');
                
                setTimeout(function() {
                    location.reload();
                }, 1000);
            }
        },
        error: function(xhr, ajaxOptions, thrownError) {
            alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
    });
}
//--></script>
{{ footer }}
