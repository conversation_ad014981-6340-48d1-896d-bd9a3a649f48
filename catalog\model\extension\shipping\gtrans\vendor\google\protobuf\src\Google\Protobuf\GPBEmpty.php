<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/protobuf/empty.proto

namespace Google\Protobuf;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * A generic empty message that you can re-use to avoid defining duplicated
 * empty messages in your APIs. A typical example is to use it as the request
 * or the response type of an API method. For instance:
 *     service Foo {
 *       rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty);
 *     }
 * The JSON representation for `Empty` is empty JSON object `{}`.
 *
 * Generated from protobuf message <code>google.protobuf.Empty</code>
 */
class GPBEmpty extends \Google\Protobuf\Internal\Message
{

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Protobuf\GPBEmpty::initOnce();
        parent::__construct($data);
    }

}

