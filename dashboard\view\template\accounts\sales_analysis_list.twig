{{ header }}{{ column_left }}
<div id="content">
<style>
/* تحسينات متقدمة لتحليل المبيعات - Enterprise Grade */
.panel-default {
    border: none;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.panel-default:hover {
    box-shadow: 0 0.25rem 2rem 0 rgba(58, 59, 69, 0.2);
}

.panel-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    border-radius: 8px 8px 0 0;
    padding: 15px;
}

/* تحسين الجدول */
.table {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 0.125rem 0.25rem rgba(0,0,0,0.075);
}

.table thead th {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    border: none;
    font-weight: 600;
    text-align: center;
}

.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
    transform: translateX(2px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* تحسين القيم */
.sales-amount {
    color: #28a745;
    font-weight: 600;
}

.quantity-amount {
    color: #007bff;
    font-weight: 600;
}

.average-amount {
    color: #17a2b8;
    font-weight: 600;
}

/* تحسين الفلاتر */
.form-inline {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.form-group {
    flex-grow: 1;
    margin-bottom: 10px;
    margin-right: 10px;
}

.form-control {
    border-radius: 6px;
    border: 1px solid #ced4da;
    transition: all 0.2s ease;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
    transform: scale(1.02);
}

/* تحسين الأزرار */
.btn {
    transition: all 0.2s ease;
    border-radius: 6px;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
    border: none;
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8 0%, #117a8b 100%);
    border: none;
}

/* تحسين ملخص المبيعات */
.sales-summary {
    background: linear-gradient(135deg, #f1f8ff 0%, #e6f3ff 100%);
    border-left: 4px solid #007bff;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.sales-summary h4 {
    color: #0056b3;
    margin-top: 0;
    font-weight: 600;
}

/* تحسين بطاقات الإحصائيات */
.stats-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 15px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    text-align: center;
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.stats-card-icon {
    font-size: 2.5em;
    margin-bottom: 10px;
}

.stats-card-value {
    font-size: 1.8em;
    font-weight: 700;
    margin-bottom: 5px;
}

.stats-card-label {
    color: #6c757d;
    font-size: 0.9em;
}

.stats-sales {
    border-left: 4px solid #28a745;
}

.stats-orders {
    border-left: 4px solid #007bff;
}

.stats-average {
    border-left: 4px solid #17a2b8;
}

.stats-customers {
    border-left: 4px solid #ffc107;
}

/* تحسين الرسوم البيانية */
.chart-container {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 0.125rem 0.25rem rgba(0,0,0,0.075);
    margin-bottom: 20px;
}

.chart-title {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 15px;
    text-align: center;
}

/* تحسين قوائم أفضل المنتجات والعملاء */
.top-list {
    background: white;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.top-list-title {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 15px;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 10px;
}

.top-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #f8f9fa;
}

.top-item:last-child {
    border-bottom: none;
}

.top-item-name {
    font-weight: 500;
    color: #2c3e50;
}

.top-item-value {
    font-weight: 600;
    color: #28a745;
}

/* تحسين التبويبات */
.nav-tabs {
    border-bottom: 2px solid #e9ecef;
    margin-bottom: 20px;
}

.nav-tabs .nav-link {
    border: none;
    border-radius: 0;
    color: #6c757d;
    font-weight: 500;
    padding: 12px 20px;
    transition: all 0.3s ease;
}

.nav-tabs .nav-link:hover {
    border-color: transparent;
    color: #007bff;
    background-color: #f8f9fa;
}

.nav-tabs .nav-link.active {
    color: #007bff;
    background-color: transparent;
    border-color: transparent;
    border-bottom: 2px solid #007bff;
}
</style>

<div class="page-header">
    <div class="container-fluid">
        <div class="pull-right">
            <button type="button" data-toggle="tooltip" title="{{ button_filter }}" onclick="$('#filter-sales').toggle();" class="btn btn-default"><i class="fa fa-filter"></i></button>
            <button type="button" data-toggle="tooltip" title="تصدير Excel" onclick="exportSalesAnalysis('excel')" class="btn btn-success"><i class="fa fa-download"></i></button>
            <button type="button" data-toggle="tooltip" title="{{ button_print }}" onclick="printSalesAnalysis()" class="btn btn-info"><i class="fa fa-print"></i></button>
        </div>
        <h1>{{ heading_title }}</h1>
        <ul class="breadcrumb">
            {% for breadcrumb in breadcrumbs %}
            <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
            {% endfor %}
        </ul>
    </div>
</div>

<div class="container-fluid">
    <!-- ملخص المبيعات -->
    <div class="sales-summary">
        <h4><i class="fa fa-chart-line"></i> ملخص تحليل المبيعات</h4>
        <div class="row">
            <div class="col-md-3">
                <div class="stats-card stats-sales">
                    <div class="stats-card-icon">
                        <i class="fa fa-dollar-sign text-success"></i>
                    </div>
                    <div class="stats-card-value sales-amount">{{ total_sales|default('0.00') }}</div>
                    <div class="stats-card-label">إجمالي المبيعات</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card stats-orders">
                    <div class="stats-card-icon">
                        <i class="fa fa-shopping-cart text-primary"></i>
                    </div>
                    <div class="stats-card-value quantity-amount">{{ total_orders|default('0') }}</div>
                    <div class="stats-card-label">عدد الطلبات</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card stats-average">
                    <div class="stats-card-icon">
                        <i class="fa fa-calculator text-info"></i>
                    </div>
                    <div class="stats-card-value average-amount">{{ average_order|default('0.00') }}</div>
                    <div class="stats-card-label">متوسط الطلب</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card stats-customers">
                    <div class="stats-card-icon">
                        <i class="fa fa-users text-warning"></i>
                    </div>
                    <div class="stats-card-value">{{ top_customers|length|default('0') }}</div>
                    <div class="stats-card-label">العملاء النشطون</div>
                </div>
            </div>
        </div>
    </div>

    <!-- فلاتر البحث -->
    <div id="filter-sales" class="well" style="display: none;">
        <div class="row">
            <div class="col-sm-4">
                <div class="form-group">
                    <label class="control-label" for="input-date-start">تاريخ البداية</label>
                    <div class="input-group date">
                        <input type="text" name="filter_date_start" value="{{ filter_date_start }}" placeholder="تاريخ البداية" data-date-format="YYYY-MM-DD" id="input-date-start" class="form-control" />
                        <span class="input-group-btn">
                            <button class="btn btn-default" type="button"><i class="fa fa-calendar"></i></button>
                        </span>
                    </div>
                </div>
            </div>
            <div class="col-sm-4">
                <div class="form-group">
                    <label class="control-label" for="input-date-end">تاريخ النهاية</label>
                    <div class="input-group date">
                        <input type="text" name="filter_date_end" value="{{ filter_date_end }}" placeholder="تاريخ النهاية" data-date-format="YYYY-MM-DD" id="input-date-end" class="form-control" />
                        <span class="input-group-btn">
                            <button class="btn btn-default" type="button"><i class="fa fa-calendar"></i></button>
                        </span>
                    </div>
                </div>
            </div>
            <div class="col-sm-4">
                <div class="form-group">
                    <label class="control-label" for="input-customer">العميل</label>
                    <select name="filter_customer" id="input-customer" class="form-control select2">
                        <option value="">جميع العملاء</option>
                        {% for customer in customers %}
                        <option value="{{ customer.customer_id }}" {% if customer.customer_id == filter_customer %}selected{% endif %}>{{ customer.name }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-sm-4">
                <div class="form-group">
                    <label class="control-label" for="input-product">المنتج</label>
                    <select name="filter_product" id="input-product" class="form-control select2">
                        <option value="">جميع المنتجات</option>
                        {% for product in products %}
                        <option value="{{ product.product_id }}" {% if product.product_id == filter_product %}selected{% endif %}>{{ product.name }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            <div class="col-sm-4">
                <div class="form-group">
                    <label class="control-label" for="input-category">الفئة</label>
                    <select name="filter_category" id="input-category" class="form-control select2">
                        <option value="">جميع الفئات</option>
                        {% for category in categories %}
                        <option value="{{ category.category_id }}" {% if category.category_id == filter_category %}selected{% endif %}>{{ category.name }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            <div class="col-sm-4">
                <button type="button" id="button-filter" class="btn btn-primary" style="margin-top: 25px;"><i class="fa fa-search"></i> فلترة</button>
            </div>
        </div>
    </div>

    <!-- التبويبات -->
    <ul class="nav nav-tabs" id="salesTabs" role="tablist">
        <li class="nav-item">
            <a class="nav-link active" id="overview-tab" data-toggle="tab" href="#overview" role="tab">نظرة عامة</a>
        </li>
        <li class="nav-item">
            <a class="nav-link" id="products-tab" data-toggle="tab" href="#products" role="tab">أفضل المنتجات</a>
        </li>
        <li class="nav-item">
            <a class="nav-link" id="customers-tab" data-toggle="tab" href="#customers" role="tab">أفضل العملاء</a>
        </li>
        <li class="nav-item">
            <a class="nav-link" id="trends-tab" data-toggle="tab" href="#trends" role="tab">الاتجاهات</a>
        </li>
    </ul>

    <div class="tab-content" id="salesTabsContent">
        <!-- تبويب النظرة العامة -->
        <div class="tab-pane fade show active" id="overview" role="tabpanel">
            <div class="row">
                <div class="col-md-8">
                    <div class="chart-container">
                        <div class="chart-title">اتجاه المبيعات الشهرية</div>
                        <canvas id="salesTrendChart" width="400" height="200"></canvas>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="chart-container">
                        <div class="chart-title">توزيع المبيعات حسب الفئة</div>
                        <canvas id="categoryChart" width="300" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- تبويب أفضل المنتجات -->
        <div class="tab-pane fade" id="products" role="tabpanel">
            <div class="top-list">
                <div class="top-list-title"><i class="fa fa-trophy"></i> أفضل المنتجات مبيعاً</div>
                {% if top_products %}
                {% for product in top_products %}
                <div class="top-item">
                    <div class="top-item-name">{{ product.name }}</div>
                    <div class="top-item-value">{{ product.total }}</div>
                </div>
                {% endfor %}
                {% else %}
                <div class="text-center text-muted">لا توجد بيانات متاحة</div>
                {% endif %}
            </div>
        </div>

        <!-- تبويب أفضل العملاء -->
        <div class="tab-pane fade" id="customers" role="tabpanel">
            <div class="top-list">
                <div class="top-list-title"><i class="fa fa-star"></i> أفضل العملاء</div>
                {% if top_customers %}
                {% for customer in top_customers %}
                <div class="top-item">
                    <div class="top-item-name">{{ customer.name }}</div>
                    <div class="top-item-value">{{ customer.total }}</div>
                </div>
                {% endfor %}
                {% else %}
                <div class="text-center text-muted">لا توجد بيانات متاحة</div>
                {% endif %}
            </div>
        </div>

        <!-- تبويب الاتجاهات -->
        <div class="tab-pane fade" id="trends" role="tabpanel">
            <div class="chart-container">
                <div class="chart-title">تحليل الاتجاهات والتوقعات</div>
                <canvas id="trendsChart" width="400" height="300"></canvas>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript"><!--
// تحسينات متقدمة لتحليل المبيعات
$(document).ready(function() {
    // تهيئة Select2
    $('.select2').select2({
        placeholder: 'اختر من القائمة',
        allowClear: true
    });
    
    // تهيئة فلاتر التاريخ
    $('.input-group.date').datetimepicker({
        pickTime: false,
        format: 'YYYY-MM-DD'
    });
    
    // إضافة تأثيرات للبطاقات
    $('.stats-card').each(function() {
        $(this).on('click', function() {
            $(this).toggleClass('selected');
        });
    });
    
    // إنشاء الرسوم البيانية
    initializeCharts();
    
    // إضافة مؤشر تحميل للعمليات
    $(document).ajaxStart(function() {
        $('body').addClass('loading');
    }).ajaxStop(function() {
        $('body').removeClass('loading');
    });
});

// دالة تهيئة الرسوم البيانية
function initializeCharts() {
    // رسم اتجاه المبيعات
    if ($('#salesTrendChart').length) {
        const ctx = document.getElementById('salesTrendChart').getContext('2d');
        const salesTrendChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [{% for month in sales_by_month %}'{{ month.month }}'{% if not loop.last %},{% endif %}{% endfor %}],
                datasets: [{
                    label: 'المبيعات',
                    data: [{% for month in sales_by_month %}{{ month.total }}{% if not loop.last %},{% endif %}{% endfor %}],
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0,123,255,0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }
    
    // رسم توزيع الفئات
    if ($('#categoryChart').length) {
        const ctx2 = document.getElementById('categoryChart').getContext('2d');
        const categoryChart = new Chart(ctx2, {
            type: 'doughnut',
            data: {
                labels: [{% for category in sales_by_category %}'{{ category.name }}'{% if not loop.last %},{% endif %}{% endfor %}],
                datasets: [{
                    data: [{% for category in sales_by_category %}{{ category.total }}{% if not loop.last %},{% endif %}{% endfor %}],
                    backgroundColor: ['#007bff', '#28a745', '#ffc107', '#dc3545', '#17a2b8'],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                legend: {
                    position: 'bottom'
                }
            }
        });
    }
}

// دالة تصدير تحليل المبيعات
function exportSalesAnalysis(format) {
    const startDate = $('#input-date-start').val();
    const endDate = $('#input-date-end').val();
    const customer = $('#input-customer').val();
    const product = $('#input-product').val();
    const category = $('#input-category').val();
    
    const url = 'index.php?route=accounts/sales_analysis/export&format=' + format + 
                '&date_start=' + startDate + '&date_end=' + endDate +
                '&customer=' + customer + '&product=' + product + '&category=' + category;
    window.open(url, '_blank');
}

// دالة طباعة تحليل المبيعات
function printSalesAnalysis() {
    window.print();
}

// دالة فلترة البيانات
$('#button-filter').on('click', function() {
    const startDate = $('#input-date-start').val();
    const endDate = $('#input-date-end').val();
    const customer = $('#input-customer').val();
    const product = $('#input-product').val();
    const category = $('#input-category').val();
    
    const url = 'index.php?route=accounts/sales_analysis&filter_date_start=' + startDate + 
                '&filter_date_end=' + endDate + '&filter_customer=' + customer +
                '&filter_product=' + product + '&filter_category=' + category;
    window.location = url;
});
//--></script>
{{ footer }}