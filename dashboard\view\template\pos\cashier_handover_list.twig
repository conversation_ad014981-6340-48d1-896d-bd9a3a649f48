{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        {% if active_shift %}
        <a href="{{ add }}" data-toggle="tooltip" title="{{ button_add_handover }}" class="btn btn-primary"><i class="fa fa-plus"></i></a>
        {% endif %}
      </div>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if success %}
    <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    {% if error %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    
    {% if not active_shift %}
    <div class="alert alert-warning"><i class="fa fa-exclamation-circle"></i> {{ text_no_active_shift }}</div>
    {% endif %}
    
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-list"></i> {{ text_list }}</h3>
      </div>
      <div class="panel-body">
        <div class="table-responsive">
          <table class="table table-bordered table-hover">
            <thead>
              <tr>
                <td class="text-left">{{ column_handover_id }}</td>
                <td class="text-left">{{ column_from_user }}</td>
                <td class="text-left">{{ column_to_user }}</td>
                <td class="text-right">{{ column_amount }}</td>
                <td class="text-left">{{ column_time }}</td>
                <td class="text-right">{{ column_action }}</td>
              </tr>
            </thead>
            <tbody>
              {% if handovers %}
              {% for handover in handovers %}
              <tr>
                <td class="text-left">{{ handover.handover_id }}</td>
                <td class="text-left">{{ handover.from_user }}</td>
                <td class="text-left">{{ handover.to_user }}</td>
                <td class="text-right">{{ handover.amount }}</td>
                <td class="text-left">{{ handover.handover_time }}</td>
                <td class="text-right">
                  <a href="{{ handover.view }}" data-toggle="tooltip" title="{{ button_view }}" class="btn btn-info"><i class="fa fa-eye"></i></a>
                </td>
              </tr>
              {% endfor %}
              {% else %}
              <tr>
                <td class="text-center" colspan="6">{{ text_no_results }}</td>
              </tr>
              {% endif %}
            </tbody>
          </table>
        </div>
        <div class="row">
          <div class="col-sm-6 text-left">{{ pagination }}</div>
          <div class="col-sm-6 text-right">{{ results }}</div>
        </div>
      </div>
    </div>
  </div>
</div>
{{ footer }}