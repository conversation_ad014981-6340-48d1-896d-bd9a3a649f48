<?php
// Heading
$_['heading_title']                    = 'Cost Center Report';

// Text
$_['text_success']                     = 'Success: Cost Center Report has been generated successfully!';
$_['text_list']                        = 'Cost Center Report List';
$_['text_form']                        = 'Cost Center Report Form';
$_['text_view']                        = 'View Cost Center Report';
$_['text_generate']                    = 'Generate Cost Center Report';
$_['text_export']                      = 'Export Cost Center Report';
$_['text_compare']                     = 'Compare Cost Center Report';
$_['text_print']                       = 'Print Cost Center Report';
$_['text_cost_center_report']          = 'Cost Center Report';
$_['text_period']                      = 'Period';
$_['text_from']                        = 'From';
$_['text_to']                          = 'To';
$_['text_loading']                     = 'Loading...';
$_['text_processing']                  = 'Processing...';
$_['text_success_generate']            = 'Cost Center Report generated successfully!';
$_['text_success_export']              = 'Cost Center Report exported successfully!';
$_['text_success_compare']             = 'Comparison completed successfully!';

// Cost Center Components
$_['text_cost_center']                 = 'Cost Center';
$_['text_cost_center_name']            = 'Cost Center Name';
$_['text_cost_center_code']            = 'Cost Center Code';
$_['text_direct_costs']                = 'Direct Costs';
$_['text_indirect_costs']              = 'Indirect Costs';
$_['text_total_costs']                 = 'Total Costs';
$_['text_allocated_costs']             = 'Allocated Costs';
$_['text_unallocated_costs']           = 'Unallocated Costs';

// Cost Types
$_['text_material_costs']              = 'Material Costs';
$_['text_labor_costs']                 = 'Labor Costs';
$_['text_overhead_costs']              = 'Overhead Costs';
$_['text_fixed_costs']                 = 'Fixed Costs';
$_['text_variable_costs']              = 'Variable Costs';
$_['text_semi_variable_costs']         = 'Semi-Variable Costs';
$_['text_manufacturing_costs']         = 'Manufacturing Costs';
$_['text_administrative_costs']        = 'Administrative Costs';
$_['text_selling_costs']               = 'Selling Costs';

// Revenue and Profitability
$_['text_revenue']                     = 'Revenue';
$_['text_gross_profit']                = 'Gross Profit';
$_['text_net_profit']                  = 'Net Profit';
$_['text_profit_margin']               = 'Profit Margin';
$_['text_profit_loss']                 = 'Profit/Loss';
$_['text_profitability']               = 'Profitability';
$_['text_profitability_analysis']      = 'Profitability Analysis';
$_['text_contribution_margin']         = 'Contribution Margin';
$_['text_break_even_point']            = 'Break-even Point';

// Cost Allocation Methods
$_['text_allocation_method']           = 'Allocation Method';
$_['text_direct_allocation']           = 'Direct Allocation';
$_['text_step_allocation']             = 'Step Allocation';
$_['text_reciprocal_allocation']       = 'Reciprocal Allocation';
$_['text_activity_based_costing']      = 'Activity-Based Costing';
$_['text_standard_costing']            = 'Standard Costing';
$_['text_actual_costing']              = 'Actual Costing';

// Performance Indicators
$_['text_cost_efficiency']             = 'Cost Efficiency';
$_['text_cost_per_unit']               = 'Cost per Unit';
$_['text_cost_variance']               = 'Cost Variance';
$_['text_budget_vs_actual']            = 'Budget vs Actual';
$_['text_cost_trend']                  = 'Cost Trend';
$_['text_cost_control']                = 'Cost Control';

// Column
$_['column_cost_center']               = 'Cost Center';
$_['column_cost_center_name']          = 'Cost Center Name';
$_['column_direct_costs']              = 'Direct Costs';
$_['column_indirect_costs']            = 'Indirect Costs';
$_['column_total_costs']               = 'Total Costs';
$_['column_revenue']                   = 'Revenue';
$_['column_profit_loss']               = 'Profit/Loss';
$_['column_profit_margin']             = 'Profit Margin';
$_['column_department']                = 'Department';
$_['column_project']                   = 'Project';

// Entry
$_['entry_date_start']                 = 'Period Start Date';
$_['entry_date_end']                   = 'Period End Date';
$_['entry_cost_center_id']             = 'Cost Center';
$_['entry_department_id']              = 'Department';
$_['entry_project_id']                 = 'Project';
$_['entry_cost_type']                  = 'Cost Type';
$_['entry_branch_id']                  = 'Branch';
$_['entry_include_indirect_costs']     = 'Include Indirect Costs';
$_['entry_allocation_method']          = 'Allocation Method';
$_['entry_show_profitability']         = 'Show Profitability Analysis';
$_['entry_export_format']              = 'Export Format';

// Button
$_['button_generate']                  = 'Generate';
$_['button_export']                    = 'Export';
$_['button_compare']                   = 'Compare';
$_['button_print']                     = 'Print';
$_['button_filter']                    = 'Filter';
$_['button_reset']                     = 'Reset';
$_['button_close']                     = 'Close';
$_['button_analyze']                   = 'Analyze';
$_['button_allocate']                  = 'Allocate';

// Tab
$_['tab_general']                      = 'General';
$_['tab_filters']                      = 'Filters';
$_['tab_options']                      = 'Options';
$_['tab_summary']                      = 'Summary';
$_['tab_details']                      = 'Details';
$_['tab_profitability']                = 'Profitability';
$_['tab_allocation']                   = 'Allocation';

// Help
$_['help_date_start']                  = 'Select the start date for cost center report period';
$_['help_date_end']                    = 'Select the end date for cost center report period';
$_['help_cost_center_id']              = 'Select specific cost center or leave empty for all centers';
$_['help_allocation_method']           = 'Choose method for allocating indirect costs';
$_['help_include_indirect']            = 'Include indirect costs in the report';

// Error
$_['error_warning']                    = 'Warning: Please check the form carefully for errors!';
$_['error_permission']                 = 'Warning: You do not have permission to access Cost Center Report!';
$_['error_date_start']                 = 'Period start date is required!';
$_['error_date_end']                   = 'Period end date is required!';
$_['error_date_range']                 = 'Start date must be before end date!';
$_['error_no_data']                    = 'No data for selected period!';
$_['error_export']                     = 'Error exporting data!';
$_['error_form']                       = 'Form data error!';
$_['error_missing_data']               = 'Missing data!';
$_['error_cost_center_not_found']      = 'Cost center not found!';

// Export Formats
$_['text_excel']                       = 'Excel';
$_['text_pdf']                         = 'PDF';
$_['text_csv']                         = 'CSV';

// Print Options
$_['text_print_title']                 = 'Cost Center Report';
$_['print_title']                      = 'Print Cost Center Report';
$_['text_print_date']                  = 'Print Date';
$_['text_print_user']                  = 'Printed By';

// Status Messages
$_['text_generating']                  = 'Generating cost center report...';
$_['text_exporting']                   = 'Exporting data...';
$_['text_comparing']                   = 'Performing comparison...';
$_['text_completed']                   = 'Completed successfully!';

// Generate Options
$_['button_generate_and_new']          = 'Generate and New';
$_['button_generate_and_export']       = 'Generate and Export';
$_['button_generate_and_profitability'] = 'Generate and Analyze Profitability';

// Dashboard & Analytics
$_['text_cost_centers_dashboard']      = 'Cost Centers Dashboard';
$_['text_profitability_chart']         = 'Profitability Chart';
$_['text_variance_analysis']           = 'Variance Analysis';
$_['text_trend_analysis']              = 'Trend Analysis';

// Status Labels
$_['text_over_budget']                 = 'Over Budget';
$_['text_under_budget']                = 'Under Budget';
$_['text_on_budget']                   = 'On Budget';

// Additional Columns
$_['column_budget']                    = 'Budget';
$_['column_actual']                    = 'Actual';
$_['column_variance']                  = 'Variance';
$_['column_variance_percent']          = 'Variance %';
$_['column_status']                    = 'Status';

// Actions
$_['button_drill_down']                = 'Drill Down';
$_['button_profitability_analysis']    = 'Profitability Analysis';
$_['text_view_report']                 = 'View Report';
$_['text_export_report']               = 'Export Report';
$_['text_profitability_analysis']      = 'Profitability Analysis';
?>

// Summary Information
$_['text_total_cost_centers']          = 'Total Cost Centers';
$_['text_profitable_centers']          = 'Profitable Centers';
$_['text_loss_making_centers']         = 'Loss-making Centers';
$_['text_cost_summary']                = 'Cost Summary';
$_['text_profitability_summary']       = 'Profitability Summary';

// Cost Analysis
$_['text_cost_analysis']               = 'Cost Analysis';
$_['text_cost_breakdown']              = 'Cost Breakdown';
$_['text_cost_distribution']           = 'Cost Distribution';
$_['text_cost_comparison']             = 'Cost Comparison';
$_['text_cost_optimization']           = 'Cost Optimization';

// Egyptian Cost Accounting Standards
$_['text_egyptian_cost_standards']     = 'Compliant with Egyptian Cost Accounting Standards';
$_['text_eas_compliant']               = 'Compliant with Egyptian Accounting Standards';
$_['text_eta_ready']                   = 'Ready for ETA Integration';
$_['text_egyptian_gaap']               = 'According to Egyptian Generally Accepted Accounting Principles';

// Additional Options
$_['text_show_details']                = 'Show Details';
$_['text_show_summary']                = 'Show Summary';
$_['text_group_by_department']         = 'Group by Department';
$_['text_group_by_project']            = 'Group by Project';
$_['text_group_by_cost_type']          = 'Group by Cost Type';

// Cost Center Types
$_['text_production_center']           = 'Production Center';
$_['text_service_center']              = 'Service Center';
$_['text_support_center']              = 'Support Center';
$_['text_administrative_center']       = 'Administrative Center';
$_['text_sales_center']                = 'Sales Center';

// Additional Fields
$_['text_total']                       = 'Total';
$_['text_no_results']                  = 'No results found';
$_['text_all_cost_centers']            = 'All Cost Centers';
$_['text_all_departments']             = 'All Departments';
$_['text_all_projects']                = 'All Projects';

// ABC Analysis
$_['text_abc_analysis']                = 'ABC Analysis';
$_['text_abc_view']                    = 'ABC Analysis View';
$_['text_success_abc_analysis']        = 'ABC analysis generated successfully!';
$_['error_no_abc_data']                = 'No ABC analysis data available!';
$_['column_abc_category']              = 'ABC Category';
$_['column_cost_percentage']           = 'Cost Percentage';
$_['column_cumulative_percentage']     = 'Cumulative Percentage';
$_['column_priority']                  = 'Priority';
$_['column_management_focus']          = 'Management Focus';
$_['text_category_a']                  = 'Category A - High Importance';
$_['text_category_b']                  = 'Category B - Medium Importance';
$_['text_category_c']                  = 'Category C - Low Importance';

// Cost Forecasting
$_['text_cost_forecast']               = 'Cost Forecast';
$_['text_forecast_generated']          = 'Forecast generated successfully!';
$_['entry_forecast_periods']           = 'Forecast Periods';
$_['text_forecast_method']             = 'Forecast Method';
$_['text_confidence_level']            = 'Confidence Level';
$_['text_forecasted_costs']            = 'Forecasted Costs';
$_['text_forecasted_revenues']         = 'Forecasted Revenues';
$_['text_confidence_interval']         = 'Confidence Interval';
$_['text_trend_analysis']              = 'Trend Analysis';
$_['text_seasonality_factors']         = 'Seasonality Factors';
$_['text_risk_assessment']             = 'Risk Assessment';

// Advanced Variance Analysis
$_['text_advanced_variance_analysis']  = 'Advanced Variance Analysis';
$_['text_budget_variances']            = 'Budget Variances';
$_['text_period_variances']            = 'Period Variances';
$_['text_early_warning_indicators']    = 'Early Warning Indicators';
$_['text_revenue_variance']            = 'Revenue Variance';
$_['text_expense_variance']            = 'Expense Variance';
$_['text_variance_amount']             = 'Variance Amount';
$_['text_variance_percentage']         = 'Variance Percentage';
$_['text_favorable']                   = 'Favorable';
$_['text_unfavorable']                 = 'Unfavorable';
$_['text_increasing']                  = 'Increasing';
$_['text_decreasing']                  = 'Decreasing';
$_['text_improving']                   = 'Improving';
$_['text_deteriorating']               = 'Deteriorating';

// Controller language variables
$_['log_unauthorized_access_cost_center_report'] = 'Unauthorized access attempt to cost center report';
$_['log_view_cost_center_report_screen'] = 'View cost center report screen';
$_['log_unauthorized_generate_cost_center_report'] = 'Unauthorized cost center report generation attempt';
$_['log_generate_cost_center_report_period'] = 'Generate cost center report for period';

// Additional variables for template
$_['text_actions'] = 'Actions';
$_['text_generate_report'] = 'Generate Report';
$_['text_export_options'] = 'Export Options';
$_['text_cost_center_analysis'] = 'Cost Center Analysis';
$_['text_report_filters'] = 'Report Filters';
$_['text_all_cost_centers'] = 'All Cost Centers';
$_['text_all_departments'] = 'All Departments';
$_['text_cost_center_report_details'] = 'Cost Center Report Details';
$_['column_cost_center_name'] = 'Cost Center Name';
$_['column_cost_center_code'] = 'Cost Center Code';
$_['column_department'] = 'Department';
$_['column_revenue'] = 'Revenue';
$_['column_expenses'] = 'Expenses';
$_['column_net_profit'] = 'Net Profit';
$_['column_profit_margin'] = 'Profit Margin';
$_['column_budget_variance'] = 'Budget Variance';
$_['column_action'] = 'Action';
$_['text_view_details'] = 'View Details';
$_['text_budget_comparison'] = 'Budget Comparison';
$_['text_revenue_vs_expenses_chart'] = 'Revenue vs Expenses Chart';
$_['text_profit_margin_chart'] = 'Profit Margin Chart';
$_['text_profit_margin'] = 'Profit Margin';
$_['text_no_data'] = 'No data to display';
$_['text_report_generated'] = 'Report generated successfully';
$_['error_generate_report'] = 'Error generating report';
$_['text_exporting'] = 'Exporting';
$_['text_loading'] = 'Loading';
$_['button_filter'] = 'Filter';
$_['entry_cost_center'] = 'Cost Center';
$_['entry_date_start'] = 'Start Date';
$_['entry_date_end'] = 'End Date';
$_['entry_department'] = 'Department';

// Enhanced performance and analytics variables
$_['text_optimized_cost_center']       = 'Optimized Cost Centers';
$_['text_performance_analysis']        = 'Performance Analysis';
$_['text_advanced_performance']        = 'Advanced Performance';
$_['text_cache_enabled']               = 'Cache Enabled';
$_['text_report_cached']               = 'Report Cached';
$_['text_comparative_analysis']        = 'Comparative Analysis';
$_['text_variance_analysis']           = 'Variance Analysis';
$_['text_efficiency_ratio']            = 'Efficiency Ratio';
$_['text_market_share']                = 'Market Share';
$_['text_growth_rate']                 = 'Growth Rate';
$_['text_performance_rating']          = 'Performance Rating';
$_['text_excellent']                   = 'Excellent';
$_['text_good']                        = 'Good';
$_['text_satisfactory']                = 'Satisfactory';
$_['text_needs_improvement']           = 'Needs Improvement';
$_['text_revenue_variance']            = 'Revenue Variance';
$_['text_expense_variance']            = 'Expense Variance';
$_['text_profit_variance']             = 'Profit Variance';
$_['text_variance_percentage']         = 'Variance Percentage';
$_['text_overall_performance']         = 'Overall Performance';
$_['text_budget_vs_actual']            = 'Budget vs Actual';
$_['button_performance_analysis']      = 'Performance Analysis';
$_['button_comparative_analysis']      = 'Comparative Analysis';
$_['button_variance_analysis']         = 'Variance Analysis';
$_['text_loading_analysis']            = 'Loading analysis...';
$_['text_analysis_ready']              = 'Analysis ready';
?>
