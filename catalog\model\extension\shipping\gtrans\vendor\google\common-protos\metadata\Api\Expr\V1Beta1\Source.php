<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/expr/v1beta1/source.proto

namespace GPBMetadata\Google\Api\Expr\V1Beta1;

class Source
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(hex2bin(
            "0ab7030a24676f6f676c652f6170692f657870722f763162657461312f73" .
            "6f757263652e70726f746f1217676f6f676c652e6170692e657870722e76" .
            "31626574613122ad010a0a536f75726365496e666f12100a086c6f636174" .
            "696f6e18022001280912140a0c6c696e655f6f6666736574731803200328" .
            "0512450a09706f736974696f6e7318042003280b32322e676f6f676c652e" .
            "6170692e657870722e763162657461312e536f75726365496e666f2e506f" .
            "736974696f6e73456e7472791a300a0e506f736974696f6e73456e747279" .
            "120b0a036b6579180120012805120d0a0576616c75651802200128053a02" .
            "380122500a0e536f75726365506f736974696f6e12100a086c6f63617469" .
            "6f6e180120012809120e0a066f6666736574180220012805120c0a046c69" .
            "6e65180320012805120e0a06636f6c756d6e180420012805426c0a1b636f" .
            "6d2e676f6f676c652e6170692e657870722e76316265746131420b536f75" .
            "72636550726f746f50015a3b676f6f676c652e676f6c616e672e6f72672f" .
            "67656e70726f746f2f676f6f676c65617069732f6170692f657870722f76" .
            "3162657461313b65787072f80101620670726f746f33"
        ), true);

        static::$is_initialized = true;
    }
}

