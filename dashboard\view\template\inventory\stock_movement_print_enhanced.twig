<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ text_print_title }}</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            font-size: 12px;
            line-height: 1.4;
            margin: 0;
            padding: 20px;
            direction: rtl;
            text-align: right;
        }
        
        .header {
            text-align: center;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 24px;
            margin: 0 0 10px 0;
            color: #333;
        }
        
        .header .company-name {
            font-size: 18px;
            font-weight: bold;
            color: #666;
            margin-bottom: 5px;
        }
        
        .header .report-info {
            font-size: 14px;
            color: #888;
        }
        
        .info-section {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
        }
        
        .info-section .info-item {
            flex: 1;
        }
        
        .info-section .info-item strong {
            display: block;
            color: #495057;
            margin-bottom: 5px;
        }
        
        .summary-section {
            margin-bottom: 30px;
        }
        
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .summary-item {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #007bff;
            text-align: center;
        }
        
        .summary-item .value {
            font-size: 20px;
            font-weight: bold;
            color: #007bff;
            display: block;
        }
        
        .summary-item .label {
            font-size: 12px;
            color: #6c757d;
            margin-top: 5px;
        }
        
        .table-section {
            margin-top: 30px;
        }
        
        .section-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            padding-bottom: 5px;
            border-bottom: 1px solid #dee2e6;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            font-size: 11px;
        }
        
        table th,
        table td {
            border: 1px solid #dee2e6;
            padding: 8px;
            text-align: right;
        }
        
        table th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #495057;
        }
        
        table tbody tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        .text-center {
            text-align: center;
        }
        
        .text-right {
            text-align: right;
        }
        
        .text-success {
            color: #28a745;
            font-weight: bold;
        }
        
        .text-danger {
            color: #dc3545;
            font-weight: bold;
        }
        
        .text-muted {
            color: #6c757d;
        }
        
        .movement-in {
            background-color: #d4edda !important;
        }
        
        .movement-out {
            background-color: #f8d7da !important;
        }
        
        .footer {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            text-align: center;
            color: #6c757d;
            font-size: 10px;
        }
        
        .page-break {
            page-break-before: always;
        }
        
        @media print {
            body {
                padding: 0;
            }
            
            .no-print {
                display: none;
            }
        }
        
        .label {
            display: inline-block;
            padding: 2px 6px;
            font-size: 10px;
            font-weight: bold;
            border-radius: 3px;
            color: white;
        }
        
        .label-success {
            background-color: #28a745;
        }
        
        .label-danger {
            background-color: #dc3545;
        }
        
        .label-warning {
            background-color: #ffc107;
            color: #212529;
        }
        
        .label-info {
            background-color: #17a2b8;
        }
        
        .label-default {
            background-color: #6c757d;
        }
    </style>
</head>
<body>
    <!-- رأس التقرير -->
    <div class="header">
        <div class="company-name">{{ company_name }}</div>
        <h1>{{ text_print_title }}</h1>
        <div class="report-info">
            {{ text_print_date }}: {{ print_date }} | {{ text_print_user }}: {{ user_name }}
        </div>
    </div>
    
    <!-- معلومات التقرير -->
    <div class="info-section">
        <div class="info-item">
            <strong>{{ text_report_period }}:</strong>
            {% if filter_date_from and filter_date_to %}
                من {{ filter_date_from }} إلى {{ filter_date_to }}
            {% elseif filter_date_from %}
                من {{ filter_date_from }}
            {% elseif filter_date_to %}
                حتى {{ filter_date_to }}
            {% else %}
                جميع الفترات
            {% endif %}
        </div>
        <div class="info-item">
            <strong>{{ text_filters_applied }}:</strong>
            {% set filter_count = 0 %}
            {% if filter_product_name %}
                {% set filter_count = filter_count + 1 %}
                المنتج: {{ filter_product_name }}
            {% endif %}
            {% if filter_branch_id %}
                {% set filter_count = filter_count + 1 %}
                {% if filter_count > 1 %}, {% endif %}الفرع: {{ filter_branch_name }}
            {% endif %}
            {% if filter_movement_type %}
                {% set filter_count = filter_count + 1 %}
                {% if filter_count > 1 %}, {% endif %}نوع الحركة: {{ filter_movement_type_text }}
            {% endif %}
            {% if filter_count == 0 %}
                لا توجد فلاتر مطبقة
            {% endif %}
        </div>
        <div class="info-item">
            <strong>{{ text_total_records }}:</strong>
            {{ stock_movements|length }} حركة
        </div>
    </div>
    
    <!-- ملخص الحركات -->
    {% if summary %}
    <div class="summary-section">
        <div class="section-title">{{ text_movement_summary }}</div>
        <div class="summary-grid">
            <div class="summary-item">
                <span class="value">{{ summary.total_movements }}</span>
                <div class="label">{{ text_total_movements }}</div>
            </div>
            <div class="summary-item">
                <span class="value">{{ summary.total_products }}</span>
                <div class="label">{{ text_total_products }}</div>
            </div>
            <div class="summary-item">
                <span class="value">{{ summary.total_quantity_in }}</span>
                <div class="label">{{ text_total_quantity_in }}</div>
            </div>
            <div class="summary-item">
                <span class="value">{{ summary.total_quantity_out }}</span>
                <div class="label">{{ text_total_quantity_out }}</div>
            </div>
            <div class="summary-item">
                <span class="value">{{ summary.net_quantity }}</span>
                <div class="label">{{ text_net_quantity }}</div>
            </div>
            {% if can_view_cost %}
            <div class="summary-item">
                <span class="value">{{ summary.total_value }}</span>
                <div class="label">{{ text_total_value }}</div>
            </div>
            {% endif %}
        </div>
    </div>
    {% endif %}
    
    <!-- جدول الحركات -->
    <div class="table-section">
        <div class="section-title">{{ text_movement_details }}</div>
        <table>
            <thead>
                <tr>
                    <th>{{ column_date }}</th>
                    <th>{{ column_product_name }}</th>
                    <th>{{ column_branch }}</th>
                    <th>{{ column_movement_type }}</th>
                    <th>{{ column_reference }}</th>
                    <th>{{ column_lot_number }}</th>
                    <th class="text-right">{{ column_quantity_in }}</th>
                    <th class="text-right">{{ column_quantity_out }}</th>
                    <th class="text-right">{{ column_running_balance }}</th>
                    {% if can_view_cost %}
                    <th class="text-right">{{ column_unit_cost }}</th>
                    <th class="text-right">{{ column_total_cost }}</th>
                    {% endif %}
                    <th>{{ column_user }}</th>
                </tr>
            </thead>
            <tbody>
                {% for movement in stock_movements %}
                <tr class="{% if movement.quantity_in_raw > 0 %}movement-in{% elseif movement.quantity_out_raw > 0 %}movement-out{% endif %}">
                    <td>{{ movement.date_added|date('d/m/Y H:i') }}</td>
                    <td>
                        <strong>{{ movement.product_name }}</strong>
                        {% if movement.model %}
                            <br><small class="text-muted">{{ movement.model }}</small>
                        {% endif %}
                        {% if movement.sku %}
                            <br><small class="text-muted">{{ movement.sku }}</small>
                        {% endif %}
                    </td>
                    <td>
                        {{ movement.branch_name }}
                        {% if movement.branch_type %}
                            <br><small class="text-muted">{{ movement.branch_type }}</small>
                        {% endif %}
                    </td>
                    <td>
                        <span class="label label-{{ movement.movement_type_class }}">
                            {{ movement.movement_type_text }}
                        </span>
                    </td>
                    <td>
                        {% if movement.reference_number %}
                            {{ movement.reference_number }}
                            {% if movement.reference_type_text %}
                                <br><small class="text-muted">{{ movement.reference_type_text }}</small>
                            {% endif %}
                        {% else %}
                            <span class="text-muted">---</span>
                        {% endif %}
                    </td>
                    <td>
                        {% if movement.lot_number %}
                            <span class="label label-info">{{ movement.lot_number }}</span>
                            {% if movement.expiry_date %}
                                <br><small class="text-muted">{{ movement.expiry_date }}</small>
                            {% endif %}
                        {% else %}
                            <span class="text-muted">---</span>
                        {% endif %}
                    </td>
                    <td class="text-right">
                        {% if movement.quantity_in_raw > 0 %}
                            <span class="text-success">+{{ movement.quantity_in }}</span>
                        {% else %}
                            <span class="text-muted">---</span>
                        {% endif %}
                    </td>
                    <td class="text-right">
                        {% if movement.quantity_out_raw > 0 %}
                            <span class="text-danger">-{{ movement.quantity_out }}</span>
                        {% else %}
                            <span class="text-muted">---</span>
                        {% endif %}
                    </td>
                    <td class="text-right">
                        <strong>{{ movement.running_balance }}</strong>
                    </td>
                    {% if can_view_cost %}
                    <td class="text-right">{{ movement.unit_cost }}</td>
                    <td class="text-right">{{ movement.total_cost }}</td>
                    {% endif %}
                    <td>
                        {{ movement.user_name }}
                        {% if movement.notes %}
                            <br><small class="text-muted">{{ movement.notes }}</small>
                        {% endif %}
                    </td>
                </tr>
                {% else %}
                <tr>
                    <td class="text-center" colspan="{% if can_view_cost %}12{% else %}10{% endif %}">
                        {{ text_no_results }}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    
    <!-- تذييل التقرير -->
    <div class="footer">
        <div>{{ text_print_title }} - {{ company_name }}</div>
        <div>{{ text_generated_on }}: {{ print_date }}</div>
        <div>{{ text_print_user }}: {{ user_name }}</div>
        <div style="margin-top: 10px;">
            <small>{{ text_confidential_notice }}</small>
        </div>
    </div>
    
    <script>
        // طباعة تلقائية عند تحميل الصفحة
        window.onload = function() {
            window.print();
        };
    </script>
</body>
</html>