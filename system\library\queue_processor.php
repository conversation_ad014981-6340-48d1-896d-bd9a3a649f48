<?php
/**
 * Queue Processor for AYM ERP
 * Advanced job processing with error handling and monitoring
 */
class QueueProcessor {
    private $db;
    private $queue;
    private $registry;
    private $log;
    private $max_execution_time = 300; // 5 دقائق
    private $memory_limit = '256M';
    
    public function __construct($registry) {
        $this->registry = $registry;
        $this->db = $registry->get('db');
        $this->queue = new EnhancedQueue($this->db);
        $this->log = new Log('queue_processor.log');
        
        // تعيين حدود الذاكرة والوقت
        ini_set('memory_limit', $this->memory_limit);
        set_time_limit($this->max_execution_time);
    }
    
    /**
     * معالجة المهام المعلقة
     */
    public function processJobs($max_jobs = 50) {
        $start_time = microtime(true);
        $processed_count = 0;
        $success_count = 0;
        $failed_count = 0;
        
        $this->log->write('Starting queue processing - Max jobs: ' . $max_jobs);
        
        try {
            // تنظيف المهام المعلقة أولاً
            $reset_count = $this->queue->resetStuckJobs();
            if ($reset_count > 0) {
                $this->log->write('Reset ' . $reset_count . ' stuck jobs');
            }
            
            // الحصول على المهام المعلقة
            $jobs = $this->queue->getPendingJobs($max_jobs);
            
            foreach ($jobs as $job) {
                if ($processed_count >= $max_jobs) {
                    break;
                }
                
                $job_start_time = microtime(true);
                
                // بدء معالجة المهمة
                if (!$this->queue->startProcessing($job['id'])) {
                    $this->log->write('Failed to start processing job ID: ' . $job['id']);
                    continue;
                }
                
                try {
                    // معالجة المهمة
                    $result = $this->executeJob($job);
                    
                    $processing_time = microtime(true) - $job_start_time;
                    
                    if ($result['success']) {
                        $this->queue->markJobAsCompleted($job['id'], $processing_time);
                        $success_count++;
                        $this->log->write('Job completed successfully - ID: ' . $job['id'] . ', Type: ' . $job['job_type'] . ', Time: ' . round($processing_time, 3) . 's');
                    } else {
                        $this->queue->markJobAsFailed($job['id'], $result['error']);
                        $failed_count++;
                        $this->log->write('Job failed - ID: ' . $job['id'] . ', Type: ' . $job['job_type'] . ', Error: ' . $result['error']);
                    }
                    
                } catch (Exception $e) {
                    $this->queue->markJobAsFailed($job['id'], $e->getMessage());
                    $failed_count++;
                    $this->log->write('Job exception - ID: ' . $job['id'] . ', Type: ' . $job['job_type'] . ', Exception: ' . $e->getMessage());
                }
                
                $processed_count++;
                
                // فحص استخدام الذاكرة
                if (memory_get_usage(true) > (256 * 1024 * 1024 * 0.8)) { // 80% من 256MB
                    $this->log->write('Memory usage high, stopping processing');
                    break;
                }
            }
            
        } catch (Exception $e) {
            $this->log->write('Queue processing error: ' . $e->getMessage());
        }
        
        $total_time = microtime(true) - $start_time;
        
        $this->log->write(sprintf(
            'Queue processing completed - Processed: %d, Success: %d, Failed: %d, Time: %.3fs',
            $processed_count, $success_count, $failed_count, $total_time
        ));
        
        return [
            'processed' => $processed_count,
            'success' => $success_count,
            'failed' => $failed_count,
            'execution_time' => $total_time
        ];
    }
    
    /**
     * تنفيذ مهمة محددة
     */
    private function executeJob($job) {
        $job_data = json_decode($job['job_data'], true);
        
        try {
            switch ($job['job_type']) {
                case 'eta_invoice':
                case 'eta_submit_invoice':
                    return $this->processETAInvoice($job_data);

                case 'eta_receipt':
                    return $this->processETAReceipt($job_data);

                case 'eta_credit_note':
                    return $this->processETACreditNote($job_data);

                case 'eta_debit_note':
                    return $this->processETADebitNote($job_data);

                case 'inventory_update':
                case 'inventory_update_stock':
                    return $this->processInventoryUpdate($job_data);

                case 'accounting_entry':
                case 'accounting_record_journal':
                    return $this->processAccountingEntry($job_data);

                case 'email_notification':
                    return $this->processEmailNotification($job_data);

                case 'report_generation':
                    return $this->processReportGeneration($job_data);

                default:
                    return [
                        'success' => false,
                        'error' => 'Unknown job type: ' . $job['job_type']
                    ];
            }
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => 'Job execution error: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * معالجة فاتورة ETA المحسنة
     */
    private function processETAInvoice($data) {
        $start_time = microtime(true);
        $order_id = $data['order_id'] ?? null;

        try {
            if (!$order_id) {
                throw new Exception('Order ID is required for ETA invoice processing');
            }

            // تسجيل بداية المعالجة
            $this->logETAActivity('eta_processing_start', 'order', $order_id,
                'بدء معالجة إرسال الفاتورة إلى ETA', $data);

            // تحميل controller الخاص بـ ETA
            $this->registry->set('request', new Request());
            $this->registry->set('response', new Response());

            $eta_controller = new ControllerExtensionEtaInvoice($this->registry);

            // إرسال الفاتورة إلى ETA
            if (isset($data['invoice_data'])) {
                // استخدام البيانات المحضرة مسبقاً
                $result = $eta_controller->submitInvoiceData($data['invoice_data']);
            } else {
                // إنشاء البيانات من الطلب
                $result = $eta_controller->submitInvoiceToETA($order_id);
            }

            if ($result && isset($result['success']) && $result['success']) {
                // إنشاء سجل في جدول مستندات ETA
                $document_id = $this->createETADocument($order_id, $data['invoice_data'] ?? [], $result);

                // تحديث حالة الطلب
                $this->db->query("UPDATE `" . DB_PREFIX . "order` SET
                    eta_status = 'submitted',
                    eta_document_id = '" . (int)$document_id . "',
                    eta_uuid = '" . $this->db->escape($result['uuid'] ?? '') . "',
                    eta_long_id = '" . $this->db->escape($result['longId'] ?? '') . "',
                    eta_submission_uuid = '" . $this->db->escape($result['submissionUUID'] ?? '') . "',
                    eta_submitted_at = NOW(),
                    eta_retry_count = 0,
                    eta_next_retry = NULL
                    WHERE order_id = '" . (int)$order_id . "'");

                // تسجيل النجاح
                $processing_time = microtime(true) - $start_time;
                $this->logETAActivity('eta_submit_success', 'order', $order_id,
                    'تم إرسال الفاتورة إلى ETA بنجاح', [
                        'eta_uuid' => $result['uuid'] ?? '',
                        'eta_long_id' => $result['longId'] ?? '',
                        'document_id' => $document_id,
                        'processing_time' => $processing_time
                    ]);

                // تحديث الإحصائيات
                $this->updateETAStatistics('submitted');

                return [
                    'success' => true,
                    'message' => 'تم إرسال الفاتورة بنجاح',
                    'data' => $result,
                    'processing_time' => $processing_time
                ];
            } else {
                // تحديث عداد المحاولات
                $this->incrementETARetryCount($order_id);

                $error_message = $result['error'] ?? 'ETA submission failed';

                // تسجيل الفشل
                $this->logETAActivity('eta_submit_failed', 'order', $order_id,
                    'فشل في إرسال الفاتورة إلى ETA: ' . $error_message, [
                        'error_details' => $result['error_details'] ?? null,
                        'validation_errors' => $result['validation_errors'] ?? null,
                        'full_response' => $result
                    ]);

                // تحديث الإحصائيات
                $this->updateETAStatistics('failed');

                return [
                    'success' => false,
                    'error' => $error_message,
                    'retry' => true,
                    'details' => $result
                ];
            }

        } catch (Exception $e) {
            // تحديث عداد المحاولات
            if ($order_id) {
                $this->incrementETARetryCount($order_id);
            }

            // تسجيل الخطأ
            $this->logETAActivity('eta_submit_error', 'order', $order_id,
                'خطأ في معالجة إرسال الفاتورة إلى ETA: ' . $e->getMessage(), [
                    'exception' => $e->getTraceAsString(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                ]);

            return [
                'success' => false,
                'error' => 'ETA Invoice processing error: ' . $e->getMessage(),
                'retry' => true
            ];
        }
    }
    
    /**
     * معالجة إيصال ETA
     */
    private function processETAReceipt($data) {
        try {
            $eta_controller = new ControllerExtensionEtaInvoice($this->registry);
            $result = $eta_controller->submitReceiptToETA($data['receipt_id']);
            
            if ($result && isset($result['success']) && $result['success']) {
                return ['success' => true];
            } else {
                return [
                    'success' => false,
                    'error' => isset($result['error']) ? $result['error'] : 'ETA receipt submission failed'
                ];
            }
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => 'ETA Receipt processing error: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * معالجة إشعار دائن ETA
     */
    private function processETACreditNote($data) {
        try {
            $eta_controller = new ControllerExtensionEtaInvoice($this->registry);
            $result = $eta_controller->submitCreditNoteToETA($data['credit_note_id']);
            
            return $result && isset($result['success']) && $result['success'] 
                ? ['success' => true]
                : ['success' => false, 'error' => isset($result['error']) ? $result['error'] : 'ETA credit note submission failed'];
                
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => 'ETA Credit Note processing error: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * معالجة إشعار مدين ETA
     */
    private function processETADebitNote($data) {
        try {
            $eta_controller = new ControllerExtensionEtaInvoice($this->registry);
            $result = $eta_controller->submitDebitNoteToETA($data['debit_note_id']);
            
            return $result && isset($result['success']) && $result['success'] 
                ? ['success' => true]
                : ['success' => false, 'error' => isset($result['error']) ? $result['error'] : 'ETA debit note submission failed'];
                
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => 'ETA Debit Note processing error: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * معالجة تحديث المخزون
     */
    private function processInventoryUpdate($data) {
        try {
            // تحديث كميات المخزون
            $inventory_model = $this->registry->get('model_inventory_inventory');
            
            if (!$inventory_model) {
                $this->load->model('inventory/inventory');
                $inventory_model = $this->model_inventory_inventory;
            }
            
            $result = $inventory_model->updateProductQuantity(
                $data['product_id'], 
                $data['quantity_change'], 
                $data['reason'] ?? 'Queue update'
            );
            
            return $result 
                ? ['success' => true]
                : ['success' => false, 'error' => 'Failed to update inventory'];
                
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => 'Inventory update error: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * معالجة قيد محاسبي
     */
    private function processAccountingEntry($data) {
        try {
            $accounting_model = $this->registry->get('model_accounting_journal');
            
            if (!$accounting_model) {
                $this->load->model('accounting/journal');
                $accounting_model = $this->model_accounting_journal;
            }
            
            $result = $accounting_model->addJournalEntry($data);
            
            return $result 
                ? ['success' => true]
                : ['success' => false, 'error' => 'Failed to create accounting entry'];
                
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => 'Accounting entry error: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * معالجة إشعار بريد إلكتروني
     */
    private function processEmailNotification($data) {
        try {
            $mail = new Mail();
            $mail->protocol = $this->registry->get('config')->get('config_mail_engine');
            $mail->hostname = $this->registry->get('config')->get('config_mail_smtp_hostname');
            $mail->username = $this->registry->get('config')->get('config_mail_smtp_username');
            $mail->password = $this->registry->get('config')->get('config_mail_smtp_password');
            $mail->port = $this->registry->get('config')->get('config_mail_smtp_port');
            $mail->timeout = $this->registry->get('config')->get('config_mail_smtp_timeout');
            
            $mail->setTo($data['to']);
            $mail->setFrom($data['from'] ?? $this->registry->get('config')->get('config_email'));
            $mail->setSender($data['sender'] ?? $this->registry->get('config')->get('config_name'));
            $mail->setSubject($data['subject']);
            $mail->setHtml($data['message']);
            
            $result = $mail->send();
            
            return $result 
                ? ['success' => true]
                : ['success' => false, 'error' => 'Failed to send email'];
                
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => 'Email notification error: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * معالجة توليد تقرير
     */
    private function processReportGeneration($data) {
        try {
            // هذه دالة مؤقتة - يجب تطويرها حسب نوع التقرير
            return ['success' => true];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => 'Report generation error: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * تنظيف المهام القديمة
     */
    public function cleanup() {
        $deleted_count = $this->queue->cleanupOldJobs();
        $this->log->write('Cleaned up ' . $deleted_count . ' old jobs');
        return $deleted_count;
    }
    
    /**
     * الحصول على إحصائيات Queue
     */
    public function getStats() {
        return $this->queue->getQueueStats();
    }

    /**
     * إنشاء سجل مستند ETA
     */
    private function createETADocument($order_id, $invoice_data, $eta_result) {
        // إدراج المستند الرئيسي
        $this->db->query("INSERT INTO `" . DB_PREFIX . "eta_documents` SET
            `entity_type` = 'order',
            `entity_id` = '" . (int)$order_id . "',
            `internal_id` = '" . $this->db->escape($invoice_data['internalID'] ?? 'INV-' . $order_id) . "',
            `eta_uuid` = '" . $this->db->escape($eta_result['uuid'] ?? '') . "',
            `eta_long_id` = '" . $this->db->escape($eta_result['longId'] ?? '') . "',
            `submission_uuid` = '" . $this->db->escape($eta_result['submissionUUID'] ?? '') . "',
            `document_type` = 'I',
            `document_version` = '1.0',
            `status` = 'submitted',
            `eta_status` = '" . $this->db->escape($eta_result['status'] ?? 'submitted') . "',
            `submission_data` = '" . $this->db->escape(json_encode($invoice_data)) . "',
            `eta_response` = '" . $this->db->escape(json_encode($eta_result)) . "',
            `signature_value` = '" . $this->db->escape($eta_result['signatureValue'] ?? '') . "',
            `qr_code` = '" . $this->db->escape($eta_result['qrCode'] ?? '') . "',
            `total_amount` = '" . (float)($invoice_data['totalAmount'] ?? 0) . "',
            `tax_amount` = '" . (float)($invoice_data['taxTotals'][0]['amount'] ?? 0) . "',
            `currency_code` = 'EGP',
            `exchange_rate` = 1.000000,
            `date_issued` = NOW(),
            `submitted_at` = NOW(),
            `created_at` = NOW(),
            `created_by` = 0");

        $document_id = $this->db->getLastId();

        // إدراج بنود المستند
        if (isset($invoice_data['invoiceLines']) && is_array($invoice_data['invoiceLines'])) {
            foreach ($invoice_data['invoiceLines'] as $index => $line) {
                $this->db->query("INSERT INTO `" . DB_PREFIX . "eta_document_lines` SET
                    `document_id` = '" . (int)$document_id . "',
                    `line_number` = '" . (int)($index + 1) . "',
                    `item_code` = '" . $this->db->escape($line['itemCode'] ?? '') . "',
                    `internal_code` = '" . $this->db->escape($line['internalCode'] ?? '') . "',
                    `description` = '" . $this->db->escape($line['description'] ?? '') . "',
                    `item_type` = '" . $this->db->escape($line['itemType'] ?? 'GS1') . "',
                    `unit_type` = '" . $this->db->escape($line['unitType'] ?? 'EA') . "',
                    `quantity` = '" . (float)($line['quantity'] ?? 0) . "',
                    `unit_price` = '" . (float)($line['unitValue']['amountEGP'] ?? 0) . "',
                    `sales_total` = '" . (float)($line['salesTotal'] ?? 0) . "',
                    `discount_amount` = '" . (float)($line['discount']['amount'] ?? 0) . "',
                    `discount_rate` = '" . (float)($line['discount']['rate'] ?? 0) . "',
                    `net_total` = '" . (float)($line['netTotal'] ?? 0) . "',
                    `tax_type` = '" . $this->db->escape($line['taxableItems'][0]['taxType'] ?? 'T1') . "',
                    `tax_subtype` = '" . $this->db->escape($line['taxableItems'][0]['subType'] ?? 'V009') . "',
                    `tax_rate` = '" . (float)($line['taxableItems'][0]['rate'] ?? 0) . "',
                    `tax_amount` = '" . (float)($line['taxableItems'][0]['amount'] ?? 0) . "',
                    `total_amount` = '" . (float)($line['total'] ?? 0) . "',
                    `currency_sold` = '" . $this->db->escape($line['unitValue']['currencySold'] ?? 'EGP') . "',
                    `currency_exchange_rate` = '" . (float)($line['unitValue']['currencyExchangeRate'] ?? 1) . "',
                    `created_at` = NOW()");
            }
        }

        return $document_id;
    }

    /**
     * زيادة عداد محاولات ETA
     */
    private function incrementETARetryCount($order_id) {
        // الحصول على العداد الحالي
        $query = $this->db->query("SELECT eta_retry_count FROM `" . DB_PREFIX . "order`
            WHERE order_id = '" . (int)$order_id . "'");

        $current_count = $query->num_rows ? (int)$query->row['eta_retry_count'] : 0;
        $new_count = $current_count + 1;

        // حساب موعد المحاولة التالية (تأخير متزايد)
        $delay_minutes = min(60, pow(2, $new_count)); // تأخير متزايد حتى 60 دقيقة
        $next_retry = date('Y-m-d H:i:s', strtotime("+{$delay_minutes} minutes"));

        // تحديث العداد والموعد
        $this->db->query("UPDATE `" . DB_PREFIX . "order` SET
            eta_retry_count = '" . (int)$new_count . "',
            eta_next_retry = '" . $this->db->escape($next_retry) . "',
            eta_status = 'failed',
            eta_updated_at = NOW()
            WHERE order_id = '" . (int)$order_id . "'");
    }

    /**
     * تحديث إحصائيات ETA
     */
    private function updateETAStatistics($status) {
        $today = date('Y-m-d');
        $hour = date('H');

        // تحديث الإحصائيات اليومية
        $this->db->query("INSERT INTO `" . DB_PREFIX . "eta_statistics`
            (stat_date, documents_" . $status . ") VALUES
            ('" . $today . "', 1)
            ON DUPLICATE KEY UPDATE
            documents_" . $status . " = documents_" . $status . " + 1,
            updated_at = NOW()");

        // تحديث الإحصائيات بالساعة
        $this->db->query("INSERT INTO `" . DB_PREFIX . "eta_statistics`
            (stat_date, stat_hour, documents_" . $status . ") VALUES
            ('" . $today . "', '" . (int)$hour . "', 1)
            ON DUPLICATE KEY UPDATE
            documents_" . $status . " = documents_" . $status . " + 1,
            updated_at = NOW()");
    }

    /**
     * تسجيل نشاط ETA
     */
    private function logETAActivity($activity_type, $entity_type, $entity_id, $description, $details = []) {
        $this->db->query("INSERT INTO `" . DB_PREFIX . "eta_activity_log` SET
            `activity_type` = '" . $this->db->escape($activity_type) . "',
            `entity_type` = '" . $this->db->escape($entity_type) . "',
            `entity_id` = '" . (int)$entity_id . "',
            `description` = '" . $this->db->escape($description) . "',
            `details` = '" . $this->db->escape(json_encode($details)) . "',
            `status` = 'info',
            `execution_time` = '" . (microtime(true) - ($_SERVER['REQUEST_TIME_FLOAT'] ?? microtime(true))) . "',
            `memory_usage` = '" . memory_get_usage() . "',
            `created_at` = NOW()");
    }
}
?>