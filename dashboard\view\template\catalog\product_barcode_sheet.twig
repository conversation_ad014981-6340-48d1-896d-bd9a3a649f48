{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="submit" form="catalog\product-form" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fas fa-save"></i></button>
        <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-light"><i class="fas fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    
    <div class="card">
      <div class="card-header"><i class="fas fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}chart-bar{% elif screen_type == 'settings' %}cog{% else %}pencil-alt{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="card-body">
        <form id="catalog\product-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-add">{{ text_add }}</label>
            <div class="col-sm-10">
              <input type="text" name="add" value="{{ add }}" placeholder="{{ text_add }}" id="input-add" class="form-control" />
              {% if error_add %}
                <div class="invalid-feedback">{{ error_add }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-branches">{{ text_branches }}</label>
            <div class="col-sm-10">
              <input type="text" name="branches" value="{{ branches }}" placeholder="{{ text_branches }}" id="input-branches" class="form-control" />
              {% if error_branches %}
                <div class="invalid-feedback">{{ error_branches }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-categories">{{ text_categories }}</label>
            <div class="col-sm-10">
              <input type="text" name="categories" value="{{ categories }}" placeholder="{{ text_categories }}" id="input-categories" class="form-control" />
              {% if error_categories %}
                <div class="invalid-feedback">{{ error_categories }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_branch">{{ text_column_branch }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_branch" value="{{ column_branch }}" placeholder="{{ text_column_branch }}" id="input-column_branch" class="form-control" />
              {% if error_column_branch %}
                <div class="invalid-feedback">{{ error_column_branch }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_cost">{{ text_column_cost }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_cost" value="{{ column_cost }}" placeholder="{{ text_column_cost }}" id="input-column_cost" class="form-control" />
              {% if error_column_cost %}
                <div class="invalid-feedback">{{ error_column_cost }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_date_added">{{ text_column_date_added }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_date_added" value="{{ column_date_added }}" placeholder="{{ text_column_date_added }}" id="input-column_date_added" class="form-control" />
              {% if error_column_date_added %}
                <div class="invalid-feedback">{{ error_column_date_added }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_new_cost">{{ text_column_new_cost }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_new_cost" value="{{ column_new_cost }}" placeholder="{{ text_column_new_cost }}" id="input-column_new_cost" class="form-control" />
              {% if error_column_new_cost %}
                <div class="invalid-feedback">{{ error_column_new_cost }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_quantity">{{ text_column_quantity }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_quantity" value="{{ column_quantity }}" placeholder="{{ text_column_quantity }}" id="input-column_quantity" class="form-control" />
              {% if error_column_quantity %}
                <div class="invalid-feedback">{{ error_column_quantity }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_reference">{{ text_column_reference }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_reference" value="{{ column_reference }}" placeholder="{{ text_column_reference }}" id="input-column_reference" class="form-control" />
              {% if error_column_reference %}
                <div class="invalid-feedback">{{ error_column_reference }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_type">{{ text_column_type }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_type" value="{{ column_type }}" placeholder="{{ text_column_type }}" id="input-column_type" class="form-control" />
              {% if error_column_type %}
                <div class="invalid-feedback">{{ error_column_type }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_unit">{{ text_column_unit }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_unit" value="{{ column_unit }}" placeholder="{{ text_column_unit }}" id="input-column_unit" class="form-control" />
              {% if error_column_unit %}
                <div class="invalid-feedback">{{ error_column_unit }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_user">{{ text_column_user }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_user" value="{{ column_user }}" placeholder="{{ text_column_user }}" id="input-column_user" class="form-control" />
              {% if error_column_user %}
                <div class="invalid-feedback">{{ error_column_user }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-copy">{{ text_copy }}</label>
            <div class="col-sm-10">
              <input type="text" name="copy" value="{{ copy }}" placeholder="{{ text_copy }}" id="input-copy" class="form-control" />
              {% if error_copy %}
                <div class="invalid-feedback">{{ error_copy }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-currencies">{{ text_currencies }}</label>
            <div class="col-sm-10">
              <input type="text" name="currencies" value="{{ currencies }}" placeholder="{{ text_currencies }}" id="input-currencies" class="form-control" />
              {% if error_currencies %}
                <div class="invalid-feedback">{{ error_currencies }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-currency_id">{{ text_currency_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="currency_id" value="{{ currency_id }}" placeholder="{{ text_currency_id }}" id="input-currency_id" class="form-control" />
              {% if error_currency_id %}
                <div class="invalid-feedback">{{ error_currency_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-customer_groups">{{ text_customer_groups }}</label>
            <div class="col-sm-10">
              <input type="text" name="customer_groups" value="{{ customer_groups }}" placeholder="{{ text_customer_groups }}" id="input-customer_groups" class="form-control" />
              {% if error_customer_groups %}
                <div class="invalid-feedback">{{ error_customer_groups }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-date_available">{{ text_date_available }}</label>
            <div class="col-sm-10">
              <input type="text" name="date_available" value="{{ date_available }}" placeholder="{{ text_date_available }}" id="input-date_available" class="form-control" />
              {% if error_date_available %}
                <div class="invalid-feedback">{{ error_date_available }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-delete">{{ text_delete }}</label>
            <div class="col-sm-10">
              <input type="text" name="delete" value="{{ delete }}" placeholder="{{ text_delete }}" id="input-delete" class="form-control" />
              {% if error_delete %}
                <div class="invalid-feedback">{{ error_delete }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-ean">{{ text_ean }}</label>
            <div class="col-sm-10">
              <input type="text" name="ean" value="{{ ean }}" placeholder="{{ text_ean }}" id="input-ean" class="form-control" />
              {% if error_ean %}
                <div class="invalid-feedback">{{ error_ean }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_keyword">{{ text_error_keyword }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_keyword" value="{{ error_keyword }}" placeholder="{{ text_error_keyword }}" id="input-error_keyword" class="form-control" />
              {% if error_error_keyword %}
                <div class="invalid-feedback">{{ error_error_keyword }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_meta_title">{{ text_error_meta_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_meta_title" value="{{ error_meta_title }}" placeholder="{{ text_error_meta_title }}" id="input-error_meta_title" class="form-control" />
              {% if error_error_meta_title %}
                <div class="invalid-feedback">{{ error_error_meta_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_model">{{ text_error_model }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_model" value="{{ error_model }}" placeholder="{{ text_error_model }}" id="input-error_model" class="form-control" />
              {% if error_error_model %}
                <div class="invalid-feedback">{{ error_error_model }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_name">{{ text_error_name }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_name" value="{{ error_name }}" placeholder="{{ text_error_name }}" id="input-error_name" class="form-control" />
              {% if error_error_name %}
                <div class="invalid-feedback">{{ error_error_name }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-export_action">{{ text_export_action }}</label>
            <div class="col-sm-10">
              <input type="text" name="export_action" value="{{ export_action }}" placeholder="{{ text_export_action }}" id="input-export_action" class="form-control" />
              {% if error_export_action %}
                <div class="invalid-feedback">{{ error_export_action }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_category">{{ text_filter_category }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_category" value="{{ filter_category }}" placeholder="{{ text_filter_category }}" id="input-filter_category" class="form-control" />
              {% if error_filter_category %}
                <div class="invalid-feedback">{{ error_filter_category }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_has_image">{{ text_filter_has_image }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_has_image" value="{{ filter_has_image }}" placeholder="{{ text_filter_has_image }}" id="input-filter_has_image" class="form-control" />
              {% if error_filter_has_image %}
                <div class="invalid-feedback">{{ error_filter_has_image }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_model">{{ text_filter_model }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_model" value="{{ filter_model }}" placeholder="{{ text_filter_model }}" id="input-filter_model" class="form-control" />
              {% if error_filter_model %}
                <div class="invalid-feedback">{{ error_filter_model }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_name">{{ text_filter_name }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_name" value="{{ filter_name }}" placeholder="{{ text_filter_name }}" id="input-filter_name" class="form-control" />
              {% if error_filter_name %}
                <div class="invalid-feedback">{{ error_filter_name }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_price">{{ text_filter_price }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_price" value="{{ filter_price }}" placeholder="{{ text_filter_price }}" id="input-filter_price" class="form-control" />
              {% if error_filter_price %}
                <div class="invalid-feedback">{{ error_filter_price }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_quantity">{{ text_filter_quantity }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_quantity" value="{{ filter_quantity }}" placeholder="{{ text_filter_quantity }}" id="input-filter_quantity" class="form-control" />
              {% if error_filter_quantity %}
                <div class="invalid-feedback">{{ error_filter_quantity }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_quantity_max">{{ text_filter_quantity_max }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_quantity_max" value="{{ filter_quantity_max }}" placeholder="{{ text_filter_quantity_max }}" id="input-filter_quantity_max" class="form-control" />
              {% if error_filter_quantity_max %}
                <div class="invalid-feedback">{{ error_filter_quantity_max }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_quantity_min">{{ text_filter_quantity_min }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_quantity_min" value="{{ filter_quantity_min }}" placeholder="{{ text_filter_quantity_min }}" id="input-filter_quantity_min" class="form-control" />
              {% if error_filter_quantity_min %}
                <div class="invalid-feedback">{{ error_filter_quantity_min }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_status">{{ text_filter_status }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_status" value="{{ filter_status }}" placeholder="{{ text_filter_status }}" id="input-filter_status" class="form-control" />
              {% if error_filter_status %}
                <div class="invalid-feedback">{{ error_filter_status }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_unit">{{ text_filter_unit }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_unit" value="{{ filter_unit }}" placeholder="{{ text_filter_unit }}" id="input-filter_unit" class="form-control" />
              {% if error_filter_unit %}
                <div class="invalid-feedback">{{ error_filter_unit }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-format">{{ text_format }}</label>
            <div class="col-sm-10">
              <input type="text" name="format" value="{{ format }}" placeholder="{{ text_format }}" id="input-format" class="form-control" />
              {% if error_format %}
                <div class="invalid-feedback">{{ error_format }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="invalid-feedback">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-height">{{ text_height }}</label>
            <div class="col-sm-10">
              <input type="text" name="height" value="{{ height }}" placeholder="{{ text_height }}" id="input-height" class="form-control" />
              {% if error_height %}
                <div class="invalid-feedback">{{ error_height }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-image">{{ text_image }}</label>
            <div class="col-sm-10">
              <input type="text" name="image" value="{{ image }}" placeholder="{{ text_image }}" id="input-image" class="form-control" />
              {% if error_image %}
                <div class="invalid-feedback">{{ error_image }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-is_default">{{ text_is_default }}</label>
            <div class="col-sm-10">
              <input type="text" name="is_default" value="{{ is_default }}" placeholder="{{ text_is_default }}" id="input-is_default" class="form-control" />
              {% if error_is_default %}
                <div class="invalid-feedback">{{ error_is_default }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-isbn">{{ text_isbn }}</label>
            <div class="col-sm-10">
              <input type="text" name="isbn" value="{{ isbn }}" placeholder="{{ text_isbn }}" id="input-isbn" class="form-control" />
              {% if error_isbn %}
                <div class="invalid-feedback">{{ error_isbn }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-jan">{{ text_jan }}</label>
            <div class="col-sm-10">
              <input type="text" name="jan" value="{{ jan }}" placeholder="{{ text_jan }}" id="input-jan" class="form-control" />
              {% if error_jan %}
                <div class="invalid-feedback">{{ error_jan }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-js_all_units">{{ text_js_all_units }}</label>
            <div class="col-sm-10">
              <input type="text" name="js_all_units" value="{{ js_all_units }}" placeholder="{{ text_js_all_units }}" id="input-js_all_units" class="form-control" />
              {% if error_js_all_units %}
                <div class="invalid-feedback">{{ error_js_all_units }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-js_branches">{{ text_js_branches }}</label>
            <div class="col-sm-10">
              <input type="text" name="js_branches" value="{{ js_branches }}" placeholder="{{ text_js_branches }}" id="input-js_branches" class="form-control" />
              {% if error_js_branches %}
                <div class="invalid-feedback">{{ error_js_branches }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-js_product_inventory">{{ text_js_product_inventory }}</label>
            <div class="col-sm-10">
              <input type="text" name="js_product_inventory" value="{{ js_product_inventory }}" placeholder="{{ text_js_product_inventory }}" id="input-js_product_inventory" class="form-control" />
              {% if error_js_product_inventory %}
                <div class="invalid-feedback">{{ error_js_product_inventory }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-js_product_pricing">{{ text_js_product_pricing }}</label>
            <div class="col-sm-10">
              <input type="text" name="js_product_pricing" value="{{ js_product_pricing }}" placeholder="{{ text_js_product_pricing }}" id="input-js_product_pricing" class="form-control" />
              {% if error_js_product_pricing %}
                <div class="invalid-feedback">{{ error_js_product_pricing }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-js_product_units">{{ text_js_product_units }}</label>
            <div class="col-sm-10">
              <input type="text" name="js_product_units" value="{{ js_product_units }}" placeholder="{{ text_js_product_units }}" id="input-js_product_units" class="form-control" />
              {% if error_js_product_units %}
                <div class="invalid-feedback">{{ error_js_product_units }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-languages">{{ text_languages }}</label>
            <div class="col-sm-10">
              <input type="text" name="languages" value="{{ languages }}" placeholder="{{ text_languages }}" id="input-languages" class="form-control" />
              {% if error_languages %}
                <div class="invalid-feedback">{{ error_languages }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-layouts">{{ text_layouts }}</label>
            <div class="col-sm-10">
              <input type="text" name="layouts" value="{{ layouts }}" placeholder="{{ text_layouts }}" id="input-layouts" class="form-control" />
              {% if error_layouts %}
                <div class="invalid-feedback">{{ error_layouts }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-length">{{ text_length }}</label>
            <div class="col-sm-10">
              <input type="text" name="length" value="{{ length }}" placeholder="{{ text_length }}" id="input-length" class="form-control" />
              {% if error_length %}
                <div class="invalid-feedback">{{ error_length }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-length_class_id">{{ text_length_class_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="length_class_id" value="{{ length_class_id }}" placeholder="{{ text_length_class_id }}" id="input-length_class_id" class="form-control" />
              {% if error_length_class_id %}
                <div class="invalid-feedback">{{ error_length_class_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-length_classes">{{ text_length_classes }}</label>
            <div class="col-sm-10">
              <input type="text" name="length_classes" value="{{ length_classes }}" placeholder="{{ text_length_classes }}" id="input-length_classes" class="form-control" />
              {% if error_length_classes %}
                <div class="invalid-feedback">{{ error_length_classes }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-location">{{ text_location }}</label>
            <div class="col-sm-10">
              <input type="text" name="location" value="{{ location }}" placeholder="{{ text_location }}" id="input-location" class="form-control" />
              {% if error_location %}
                <div class="invalid-feedback">{{ error_location }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-manufacturer">{{ text_manufacturer }}</label>
            <div class="col-sm-10">
              <input type="text" name="manufacturer" value="{{ manufacturer }}" placeholder="{{ text_manufacturer }}" id="input-manufacturer" class="form-control" />
              {% if error_manufacturer %}
                <div class="invalid-feedback">{{ error_manufacturer }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-manufacturer_id">{{ text_manufacturer_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="manufacturer_id" value="{{ manufacturer_id }}" placeholder="{{ text_manufacturer_id }}" id="input-manufacturer_id" class="form-control" />
              {% if error_manufacturer_id %}
                <div class="invalid-feedback">{{ error_manufacturer_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-min_quantity">{{ text_min_quantity }}</label>
            <div class="col-sm-10">
              <input type="text" name="min_quantity" value="{{ min_quantity }}" placeholder="{{ text_min_quantity }}" id="input-min_quantity" class="form-control" />
              {% if error_min_quantity %}
                <div class="invalid-feedback">{{ error_min_quantity }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-minimum">{{ text_minimum }}</label>
            <div class="col-sm-10">
              <input type="text" name="minimum" value="{{ minimum }}" placeholder="{{ text_minimum }}" id="input-minimum" class="form-control" />
              {% if error_minimum %}
                <div class="invalid-feedback">{{ error_minimum }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-model">{{ text_model }}</label>
            <div class="col-sm-10">
              <input type="text" name="model" value="{{ model }}" placeholder="{{ text_model }}" id="input-model" class="form-control" />
              {% if error_model %}
                <div class="invalid-feedback">{{ error_model }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-movements">{{ text_movements }}</label>
            <div class="col-sm-10">
              <input type="text" name="movements" value="{{ movements }}" placeholder="{{ text_movements }}" id="input-movements" class="form-control" />
              {% if error_movements %}
                <div class="invalid-feedback">{{ error_movements }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-mpn">{{ text_mpn }}</label>
            <div class="col-sm-10">
              <input type="text" name="mpn" value="{{ mpn }}" placeholder="{{ text_mpn }}" id="input-mpn" class="form-control" />
              {% if error_mpn %}
                <div class="invalid-feedback">{{ error_mpn }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-option_values">{{ text_option_values }}</label>
            <div class="col-sm-10">
              <input type="text" name="option_values" value="{{ option_values }}" placeholder="{{ text_option_values }}" id="input-option_values" class="form-control" />
              {% if error_option_values %}
                <div class="invalid-feedback">{{ error_option_values }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-order">{{ text_order }}</label>
            <div class="col-sm-10">
              <input type="text" name="order" value="{{ order }}" placeholder="{{ text_order }}" id="input-order" class="form-control" />
              {% if error_order %}
                <div class="invalid-feedback">{{ error_order }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-pagination">{{ text_pagination }}</label>
            <div class="col-sm-10">
              <input type="text" name="pagination" value="{{ pagination }}" placeholder="{{ text_pagination }}" id="input-pagination" class="form-control" />
              {% if error_pagination %}
                <div class="invalid-feedback">{{ error_pagination }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-placeholder">{{ text_placeholder }}</label>
            <div class="col-sm-10">
              <input type="text" name="placeholder" value="{{ placeholder }}" placeholder="{{ text_placeholder }}" id="input-placeholder" class="form-control" />
              {% if error_placeholder %}
                <div class="invalid-feedback">{{ error_placeholder }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-points">{{ text_points }}</label>
            <div class="col-sm-10">
              <input type="text" name="points" value="{{ points }}" placeholder="{{ text_points }}" id="input-points" class="form-control" />
              {% if error_points %}
                <div class="invalid-feedback">{{ error_points }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-price">{{ text_price }}</label>
            <div class="col-sm-10">
              <input type="text" name="price" value="{{ price }}" placeholder="{{ text_price }}" id="input-price" class="form-control" />
              {% if error_price %}
                <div class="invalid-feedback">{{ error_price }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-price_id">{{ text_price_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="price_id" value="{{ price_id }}" placeholder="{{ text_price_id }}" id="input-price_id" class="form-control" />
              {% if error_price_id %}
                <div class="invalid-feedback">{{ error_price_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-product">{{ text_product }}</label>
            <div class="col-sm-10">
              <input type="text" name="product" value="{{ product }}" placeholder="{{ text_product }}" id="input-product" class="form-control" />
              {% if error_product %}
                <div class="invalid-feedback">{{ error_product }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-product_attributes">{{ text_product_attributes }}</label>
            <div class="col-sm-10">
              <input type="text" name="product_attributes" value="{{ product_attributes }}" placeholder="{{ text_product_attributes }}" id="input-product_attributes" class="form-control" />
              {% if error_product_attributes %}
                <div class="invalid-feedback">{{ error_product_attributes }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-product_barcodes">{{ text_product_barcodes }}</label>
            <div class="col-sm-10">
              <input type="text" name="product_barcodes" value="{{ product_barcodes }}" placeholder="{{ text_product_barcodes }}" id="input-product_barcodes" class="form-control" />
              {% if error_product_barcodes %}
                <div class="invalid-feedback">{{ error_product_barcodes }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-product_bundles">{{ text_product_bundles }}</label>
            <div class="col-sm-10">
              <input type="text" name="product_bundles" value="{{ product_bundles }}" placeholder="{{ text_product_bundles }}" id="input-product_bundles" class="form-control" />
              {% if error_product_bundles %}
                <div class="invalid-feedback">{{ error_product_bundles }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-product_categories">{{ text_product_categories }}</label>
            <div class="col-sm-10">
              <input type="text" name="product_categories" value="{{ product_categories }}" placeholder="{{ text_product_categories }}" id="input-product_categories" class="form-control" />
              {% if error_product_categories %}
                <div class="invalid-feedback">{{ error_product_categories }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-product_cross_sells">{{ text_product_cross_sells }}</label>
            <div class="col-sm-10">
              <input type="text" name="product_cross_sells" value="{{ product_cross_sells }}" placeholder="{{ text_product_cross_sells }}" id="input-product_cross_sells" class="form-control" />
              {% if error_product_cross_sells %}
                <div class="invalid-feedback">{{ error_product_cross_sells }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-product_description">{{ text_product_description }}</label>
            <div class="col-sm-10">
              <input type="text" name="product_description" value="{{ product_description }}" placeholder="{{ text_product_description }}" id="input-product_description" class="form-control" />
              {% if error_product_description %}
                <div class="invalid-feedback">{{ error_product_description }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-product_discounts">{{ text_product_discounts }}</label>
            <div class="col-sm-10">
              <input type="text" name="product_discounts" value="{{ product_discounts }}" placeholder="{{ text_product_discounts }}" id="input-product_discounts" class="form-control" />
              {% if error_product_discounts %}
                <div class="invalid-feedback">{{ error_product_discounts }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-product_filters">{{ text_product_filters }}</label>
            <div class="col-sm-10">
              <input type="text" name="product_filters" value="{{ product_filters }}" placeholder="{{ text_product_filters }}" id="input-product_filters" class="form-control" />
              {% if error_product_filters %}
                <div class="invalid-feedback">{{ error_product_filters }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-product_id">{{ text_product_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="product_id" value="{{ product_id }}" placeholder="{{ text_product_id }}" id="input-product_id" class="form-control" />
              {% if error_product_id %}
                <div class="invalid-feedback">{{ error_product_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-product_images">{{ text_product_images }}</label>
            <div class="col-sm-10">
              <input type="text" name="product_images" value="{{ product_images }}" placeholder="{{ text_product_images }}" id="input-product_images" class="form-control" />
              {% if error_product_images %}
                <div class="invalid-feedback">{{ error_product_images }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-product_inventory">{{ text_product_inventory }}</label>
            <div class="col-sm-10">
              <input type="text" name="product_inventory" value="{{ product_inventory }}" placeholder="{{ text_product_inventory }}" id="input-product_inventory" class="form-control" />
              {% if error_product_inventory %}
                <div class="invalid-feedback">{{ error_product_inventory }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-product_layout">{{ text_product_layout }}</label>
            <div class="col-sm-10">
              <input type="text" name="product_layout" value="{{ product_layout }}" placeholder="{{ text_product_layout }}" id="input-product_layout" class="form-control" />
              {% if error_product_layout %}
                <div class="invalid-feedback">{{ error_product_layout }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-product_options">{{ text_product_options }}</label>
            <div class="col-sm-10">
              <input type="text" name="product_options" value="{{ product_options }}" placeholder="{{ text_product_options }}" id="input-product_options" class="form-control" />
              {% if error_product_options %}
                <div class="invalid-feedback">{{ error_product_options }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-product_pricing">{{ text_product_pricing }}</label>
            <div class="col-sm-10">
              <input type="text" name="product_pricing" value="{{ product_pricing }}" placeholder="{{ text_product_pricing }}" id="input-product_pricing" class="form-control" />
              {% if error_product_pricing %}
                <div class="invalid-feedback">{{ error_product_pricing }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-product_relateds">{{ text_product_relateds }}</label>
            <div class="col-sm-10">
              <input type="text" name="product_relateds" value="{{ product_relateds }}" placeholder="{{ text_product_relateds }}" id="input-product_relateds" class="form-control" />
              {% if error_product_relateds %}
                <div class="invalid-feedback">{{ error_product_relateds }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-product_reward">{{ text_product_reward }}</label>
            <div class="col-sm-10">
              <input type="text" name="product_reward" value="{{ product_reward }}" placeholder="{{ text_product_reward }}" id="input-product_reward" class="form-control" />
              {% if error_product_reward %}
                <div class="invalid-feedback">{{ error_product_reward }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-product_seo_url">{{ text_product_seo_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="product_seo_url" value="{{ product_seo_url }}" placeholder="{{ text_product_seo_url }}" id="input-product_seo_url" class="form-control" />
              {% if error_product_seo_url %}
                <div class="invalid-feedback">{{ error_product_seo_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-product_store">{{ text_product_store }}</label>
            <div class="col-sm-10">
              <input type="text" name="product_store" value="{{ product_store }}" placeholder="{{ text_product_store }}" id="input-product_store" class="form-control" />
              {% if error_product_store %}
                <div class="invalid-feedback">{{ error_product_store }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-product_units">{{ text_product_units }}</label>
            <div class="col-sm-10">
              <input type="text" name="product_units" value="{{ product_units }}" placeholder="{{ text_product_units }}" id="input-product_units" class="form-control" />
              {% if error_product_units %}
                <div class="invalid-feedback">{{ error_product_units }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-product_upsells">{{ text_product_upsells }}</label>
            <div class="col-sm-10">
              <input type="text" name="product_upsells" value="{{ product_upsells }}" placeholder="{{ text_product_upsells }}" id="input-product_upsells" class="form-control" />
              {% if error_product_upsells %}
                <div class="invalid-feedback">{{ error_product_upsells }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-products">{{ text_products }}</label>
            <div class="col-sm-10">
              <input type="text" name="products" value="{{ products }}" placeholder="{{ text_products }}" id="input-products" class="form-control" />
              {% if error_products %}
                <div class="invalid-feedback">{{ error_products }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-quantity">{{ text_quantity }}</label>
            <div class="col-sm-10">
              <input type="text" name="quantity" value="{{ quantity }}" placeholder="{{ text_quantity }}" id="input-quantity" class="form-control" />
              {% if error_quantity %}
                <div class="invalid-feedback">{{ error_quantity }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-results">{{ text_results }}</label>
            <div class="col-sm-10">
              <input type="text" name="results" value="{{ results }}" placeholder="{{ text_results }}" id="input-results" class="form-control" />
              {% if error_results %}
                <div class="invalid-feedback">{{ error_results }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-selected">{{ text_selected }}</label>
            <div class="col-sm-10">
              <input type="text" name="selected" value="{{ selected }}" placeholder="{{ text_selected }}" id="input-selected" class="form-control" />
              {% if error_selected %}
                <div class="invalid-feedback">{{ error_selected }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-shipping">{{ text_shipping }}</label>
            <div class="col-sm-10">
              <input type="text" name="shipping" value="{{ shipping }}" placeholder="{{ text_shipping }}" id="input-shipping" class="form-control" />
              {% if error_shipping %}
                <div class="invalid-feedback">{{ error_shipping }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sku">{{ text_sku }}</label>
            <div class="col-sm-10">
              <input type="text" name="sku" value="{{ sku }}" placeholder="{{ text_sku }}" id="input-sku" class="form-control" />
              {% if error_sku %}
                <div class="invalid-feedback">{{ error_sku }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort">{{ text_sort }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort" value="{{ sort }}" placeholder="{{ text_sort }}" id="input-sort" class="form-control" />
              {% if error_sort %}
                <div class="invalid-feedback">{{ error_sort }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_model">{{ text_sort_model }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_model" value="{{ sort_model }}" placeholder="{{ text_sort_model }}" id="input-sort_model" class="form-control" />
              {% if error_sort_model %}
                <div class="invalid-feedback">{{ error_sort_model }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_name">{{ text_sort_name }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_name" value="{{ sort_name }}" placeholder="{{ text_sort_name }}" id="input-sort_name" class="form-control" />
              {% if error_sort_name %}
                <div class="invalid-feedback">{{ error_sort_name }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_order">{{ text_sort_order }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_order" value="{{ sort_order }}" placeholder="{{ text_sort_order }}" id="input-sort_order" class="form-control" />
              {% if error_sort_order %}
                <div class="invalid-feedback">{{ error_sort_order }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_price">{{ text_sort_price }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_price" value="{{ sort_price }}" placeholder="{{ text_sort_price }}" id="input-sort_price" class="form-control" />
              {% if error_sort_price %}
                <div class="invalid-feedback">{{ error_sort_price }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_product_id">{{ text_sort_product_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_product_id" value="{{ sort_product_id }}" placeholder="{{ text_sort_product_id }}" id="input-sort_product_id" class="form-control" />
              {% if error_sort_product_id %}
                <div class="invalid-feedback">{{ error_sort_product_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_quantity">{{ text_sort_quantity }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_quantity" value="{{ sort_quantity }}" placeholder="{{ text_sort_quantity }}" id="input-sort_quantity" class="form-control" />
              {% if error_sort_quantity %}
                <div class="invalid-feedback">{{ error_sort_quantity }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_status">{{ text_sort_status }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_status" value="{{ sort_status }}" placeholder="{{ text_sort_status }}" id="input-sort_status" class="form-control" />
              {% if error_sort_status %}
                <div class="invalid-feedback">{{ error_sort_status }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-statistics">{{ text_statistics }}</label>
            <div class="col-sm-10">
              <input type="text" name="statistics" value="{{ statistics }}" placeholder="{{ text_statistics }}" id="input-statistics" class="form-control" />
              {% if error_statistics %}
                <div class="invalid-feedback">{{ error_statistics }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-status">{{ text_status }}</label>
            <div class="col-sm-10">
              <input type="text" name="status" value="{{ status }}" placeholder="{{ text_status }}" id="input-status" class="form-control" />
              {% if error_status %}
                <div class="invalid-feedback">{{ error_status }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-stock_status_id">{{ text_stock_status_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="stock_status_id" value="{{ stock_status_id }}" placeholder="{{ text_stock_status_id }}" id="input-stock_status_id" class="form-control" />
              {% if error_stock_status_id %}
                <div class="invalid-feedback">{{ error_stock_status_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-stock_statuses">{{ text_stock_statuses }}</label>
            <div class="col-sm-10">
              <input type="text" name="stock_statuses" value="{{ stock_statuses }}" placeholder="{{ text_stock_statuses }}" id="input-stock_statuses" class="form-control" />
              {% if error_stock_statuses %}
                <div class="invalid-feedback">{{ error_stock_statuses }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-store_name">{{ text_store_name }}</label>
            <div class="col-sm-10">
              <input type="text" name="store_name" value="{{ store_name }}" placeholder="{{ text_store_name }}" id="input-store_name" class="form-control" />
              {% if error_store_name %}
                <div class="invalid-feedback">{{ error_store_name }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-stores">{{ text_stores }}</label>
            <div class="col-sm-10">
              <input type="text" name="stores" value="{{ stores }}" placeholder="{{ text_stores }}" id="input-stores" class="form-control" />
              {% if error_stores %}
                <div class="invalid-feedback">{{ error_stores }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-subtract">{{ text_subtract }}</label>
            <div class="col-sm-10">
              <input type="text" name="subtract" value="{{ subtract }}" placeholder="{{ text_subtract }}" id="input-subtract" class="form-control" />
              {% if error_subtract %}
                <div class="invalid-feedback">{{ error_subtract }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-supplier_id">{{ text_supplier_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="supplier_id" value="{{ supplier_id }}" placeholder="{{ text_supplier_id }}" id="input-supplier_id" class="form-control" />
              {% if error_supplier_id %}
                <div class="invalid-feedback">{{ error_supplier_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-suppliers">{{ text_suppliers }}</label>
            <div class="col-sm-10">
              <input type="text" name="suppliers" value="{{ suppliers }}" placeholder="{{ text_suppliers }}" id="input-suppliers" class="form-control" />
              {% if error_suppliers %}
                <div class="invalid-feedback">{{ error_suppliers }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-tab_barcode">{{ text_tab_barcode }}</label>
            <div class="col-sm-10">
              <input type="text" name="tab_barcode" value="{{ tab_barcode }}" placeholder="{{ text_tab_barcode }}" id="input-tab_barcode" class="form-control" />
              {% if error_tab_barcode %}
                <div class="invalid-feedback">{{ error_tab_barcode }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-tab_bundle">{{ text_tab_bundle }}</label>
            <div class="col-sm-10">
              <input type="text" name="tab_bundle" value="{{ tab_bundle }}" placeholder="{{ text_tab_bundle }}" id="input-tab_bundle" class="form-control" />
              {% if error_tab_bundle %}
                <div class="invalid-feedback">{{ error_tab_bundle }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-tab_data">{{ text_tab_data }}</label>
            <div class="col-sm-10">
              <input type="text" name="tab_data" value="{{ tab_data }}" placeholder="{{ text_tab_data }}" id="input-tab_data" class="form-control" />
              {% if error_tab_data %}
                <div class="invalid-feedback">{{ error_tab_data }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-tab_general">{{ text_tab_general }}</label>
            <div class="col-sm-10">
              <input type="text" name="tab_general" value="{{ tab_general }}" placeholder="{{ text_tab_general }}" id="input-tab_general" class="form-control" />
              {% if error_tab_general %}
                <div class="invalid-feedback">{{ error_tab_general }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-tab_image">{{ text_tab_image }}</label>
            <div class="col-sm-10">
              <input type="text" name="tab_image" value="{{ tab_image }}" placeholder="{{ text_tab_image }}" id="input-tab_image" class="form-control" />
              {% if error_tab_image %}
                <div class="invalid-feedback">{{ error_tab_image }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-tab_inventory">{{ text_tab_inventory }}</label>
            <div class="col-sm-10">
              <input type="text" name="tab_inventory" value="{{ tab_inventory }}" placeholder="{{ text_tab_inventory }}" id="input-tab_inventory" class="form-control" />
              {% if error_tab_inventory %}
                <div class="invalid-feedback">{{ error_tab_inventory }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-tab_movement">{{ text_tab_movement }}</label>
            <div class="col-sm-10">
              <input type="text" name="tab_movement" value="{{ tab_movement }}" placeholder="{{ text_tab_movement }}" id="input-tab_movement" class="form-control" />
              {% if error_tab_movement %}
                <div class="invalid-feedback">{{ error_tab_movement }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-tab_option">{{ text_tab_option }}</label>
            <div class="col-sm-10">
              <input type="text" name="tab_option" value="{{ tab_option }}" placeholder="{{ text_tab_option }}" id="input-tab_option" class="form-control" />
              {% if error_tab_option %}
                <div class="invalid-feedback">{{ error_tab_option }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-tab_orders">{{ text_tab_orders }}</label>
            <div class="col-sm-10">
              <input type="text" name="tab_orders" value="{{ tab_orders }}" placeholder="{{ text_tab_orders }}" id="input-tab_orders" class="form-control" />
              {% if error_tab_orders %}
                <div class="invalid-feedback">{{ error_tab_orders }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-tab_pricing">{{ text_tab_pricing }}</label>
            <div class="col-sm-10">
              <input type="text" name="tab_pricing" value="{{ tab_pricing }}" placeholder="{{ text_tab_pricing }}" id="input-tab_pricing" class="form-control" />
              {% if error_tab_pricing %}
                <div class="invalid-feedback">{{ error_tab_pricing }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-tab_recommend">{{ text_tab_recommend }}</label>
            <div class="col-sm-10">
              <input type="text" name="tab_recommend" value="{{ tab_recommend }}" placeholder="{{ text_tab_recommend }}" id="input-tab_recommend" class="form-control" />
              {% if error_tab_recommend %}
                <div class="invalid-feedback">{{ error_tab_recommend }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-tab_units">{{ text_tab_units }}</label>
            <div class="col-sm-10">
              <input type="text" name="tab_units" value="{{ tab_units }}" placeholder="{{ text_tab_units }}" id="input-tab_units" class="form-control" />
              {% if error_tab_units %}
                <div class="invalid-feedback">{{ error_tab_units }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-tax_class_id">{{ text_tax_class_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="tax_class_id" value="{{ tax_class_id }}" placeholder="{{ text_tax_class_id }}" id="input-tax_class_id" class="form-control" />
              {% if error_tax_class_id %}
                <div class="invalid-feedback">{{ error_tax_class_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-tax_classes">{{ text_tax_classes }}</label>
            <div class="col-sm-10">
              <input type="text" name="tax_classes" value="{{ tax_classes }}" placeholder="{{ text_tax_classes }}" id="input-tax_classes" class="form-control" />
              {% if error_tax_classes %}
                <div class="invalid-feedback">{{ error_tax_classes }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_category">{{ text_text_category }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_category" value="{{ text_category }}" placeholder="{{ text_text_category }}" id="input-text_category" class="form-control" />
              {% if error_text_category %}
                <div class="invalid-feedback">{{ error_text_category }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_current_stock">{{ text_text_current_stock }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_current_stock" value="{{ text_current_stock }}" placeholder="{{ text_text_current_stock }}" id="input-text_current_stock" class="form-control" />
              {% if error_text_current_stock %}
                <div class="invalid-feedback">{{ error_text_current_stock }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_form">{{ text_text_form }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_form" value="{{ text_form }}" placeholder="{{ text_text_form }}" id="input-text_form" class="form-control" />
              {% if error_text_form %}
                <div class="invalid-feedback">{{ error_text_form }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_model">{{ text_text_model }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_model" value="{{ text_model }}" placeholder="{{ text_text_model }}" id="input-text_model" class="form-control" />
              {% if error_text_model %}
                <div class="invalid-feedback">{{ error_text_model }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_movement_history">{{ text_text_movement_history }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_movement_history" value="{{ text_movement_history }}" placeholder="{{ text_text_movement_history }}" id="input-text_movement_history" class="form-control" />
              {% if error_text_movement_history %}
                <div class="invalid-feedback">{{ error_text_movement_history }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_net_change">{{ text_text_net_change }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_net_change" value="{{ text_net_change }}" placeholder="{{ text_text_net_change }}" id="input-text_net_change" class="form-control" />
              {% if error_text_net_change %}
                <div class="invalid-feedback">{{ error_text_net_change }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_product">{{ text_text_product }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_product" value="{{ text_product }}" placeholder="{{ text_text_product }}" id="input-text_product" class="form-control" />
              {% if error_text_product %}
                <div class="invalid-feedback">{{ error_text_product }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_sku">{{ text_text_sku }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_sku" value="{{ text_sku }}" placeholder="{{ text_text_sku }}" id="input-text_sku" class="form-control" />
              {% if error_text_sku %}
                <div class="invalid-feedback">{{ error_text_sku }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_total_incoming">{{ text_text_total_incoming }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_total_incoming" value="{{ text_total_incoming }}" placeholder="{{ text_text_total_incoming }}" id="input-text_total_incoming" class="form-control" />
              {% if error_text_total_incoming %}
                <div class="invalid-feedback">{{ error_text_total_incoming }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_total_outgoing">{{ text_text_total_outgoing }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_total_outgoing" value="{{ text_total_outgoing }}" placeholder="{{ text_text_total_outgoing }}" id="input-text_total_outgoing" class="form-control" />
              {% if error_text_total_outgoing %}
                <div class="invalid-feedback">{{ error_text_total_outgoing }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-thumb">{{ text_thumb }}</label>
            <div class="col-sm-10">
              <input type="text" name="thumb" value="{{ thumb }}" placeholder="{{ text_thumb }}" id="input-thumb" class="form-control" />
              {% if error_thumb %}
                <div class="invalid-feedback">{{ error_thumb }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-title">{{ text_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="title" value="{{ title }}" placeholder="{{ text_title }}" id="input-title" class="form-control" />
              {% if error_title %}
                <div class="invalid-feedback">{{ error_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-type">{{ text_type }}</label>
            <div class="col-sm-10">
              <input type="text" name="type" value="{{ type }}" placeholder="{{ text_type }}" id="input-type" class="form-control" />
              {% if error_type %}
                <div class="invalid-feedback">{{ error_type }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-unit_id">{{ text_unit_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="unit_id" value="{{ unit_id }}" placeholder="{{ text_unit_id }}" id="input-unit_id" class="form-control" />
              {% if error_unit_id %}
                <div class="invalid-feedback">{{ error_unit_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-units">{{ text_units }}</label>
            <div class="col-sm-10">
              <input type="text" name="units" value="{{ units }}" placeholder="{{ text_units }}" id="input-units" class="form-control" />
              {% if error_units %}
                <div class="invalid-feedback">{{ error_units }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-upc">{{ text_upc }}</label>
            <div class="col-sm-10">
              <input type="text" name="upc" value="{{ upc }}" placeholder="{{ text_upc }}" id="input-upc" class="form-control" />
              {% if error_upc %}
                <div class="invalid-feedback">{{ error_upc }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="invalid-feedback">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-weight">{{ text_weight }}</label>
            <div class="col-sm-10">
              <input type="text" name="weight" value="{{ weight }}" placeholder="{{ text_weight }}" id="input-weight" class="form-control" />
              {% if error_weight %}
                <div class="invalid-feedback">{{ error_weight }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-weight_class_id">{{ text_weight_class_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="weight_class_id" value="{{ weight_class_id }}" placeholder="{{ text_weight_class_id }}" id="input-weight_class_id" class="form-control" />
              {% if error_weight_class_id %}
                <div class="invalid-feedback">{{ error_weight_class_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-weight_classes">{{ text_weight_classes }}</label>
            <div class="col-sm-10">
              <input type="text" name="weight_classes" value="{{ weight_classes }}" placeholder="{{ text_weight_classes }}" id="input-weight_classes" class="form-control" />
              {% if error_weight_classes %}
                <div class="invalid-feedback">{{ error_weight_classes }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-width">{{ text_width }}</label>
            <div class="col-sm-10">
              <input type="text" name="width" value="{{ width }}" placeholder="{{ text_width }}" id="input-width" class="form-control" />
              {% if error_width %}
                <div class="invalid-feedback">{{ error_width }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-xcategories">{{ text_xcategories }}</label>
            <div class="col-sm-10">
              <input type="text" name="xcategories" value="{{ xcategories }}" placeholder="{{ text_xcategories }}" id="input-xcategories" class="form-control" />
              {% if error_xcategories %}
                <div class="invalid-feedback">{{ error_xcategories }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}