<?php
/**
 * English Language File - Advanced Account Statement
 * 
 * @package    AYM ERP
 * <AUTHOR> ERP Development Team
 * @copyright  2025 AYM ERP. All Rights Reserved.
 * @license    Proprietary - AYM ERP Commercial License
 * @version    1.0.0
 * @link       https://aym-erp.com
 * @since      File available since AYM ERP 1.0.0
 */

// Heading
$_['heading_title']                    = 'Advanced Account Statement';

// Text
$_['text_success']                     = 'Success: You have modified advanced account statement!';
$_['text_list']                        = 'Account Statement List';
$_['text_add']                         = 'Add Account Statement';
$_['text_edit']                        = 'Edit Account Statement';
$_['text_view']                        = 'View Account Statement';
$_['text_print']                       = 'Print';
$_['text_export']                      = 'Export';
$_['text_excel']                       = 'Excel';
$_['text_pdf']                         = 'PDF';
$_['text_csv']                         = 'CSV';
$_['text_loading']                     = 'Loading...';
$_['text_no_results']                  = 'No results!';
$_['text_confirm']                     = 'Are you sure?';
$_['text_default']                     = 'Default';
$_['text_enabled']                     = 'Enabled';
$_['text_disabled']                    = 'Disabled';
$_['text_yes']                         = 'Yes';
$_['text_no']                          = 'No';
$_['text_none']                        = 'None';
$_['text_select']                      = 'Select';
$_['text_all_zones']                   = 'All Zones';
$_['text_please_select']               = 'Please Select';
$_['text_pagination']                  = 'Showing %d to %d of %d (%d Pages)';

// Entry
$_['entry_account']                    = 'Account';
$_['entry_account_code']               = 'Account Code';
$_['entry_account_name']               = 'Account Name';
$_['entry_date_start']                 = 'Start Date';
$_['entry_date_end']                   = 'End Date';
$_['entry_include_opening_balance']    = 'Include Opening Balance';
$_['entry_include_closing_balance']    = 'Include Closing Balance';
$_['entry_show_zero_balances']         = 'Show Zero Balances';
$_['entry_group_by_month']             = 'Group by Month';
$_['entry_detailed_view']              = 'Detailed View';
$_['entry_currency']                   = 'Currency';
$_['entry_branch']                     = 'Branch';
$_['entry_cost_center']                = 'Cost Center';
$_['entry_format']                     = 'Format';
$_['entry_sort_by']                    = 'Sort By';
$_['entry_sort_order']                 = 'Sort Order';

// Column
$_['column_date']                      = 'Date';
$_['column_reference']                 = 'Reference';
$_['column_description']               = 'Description';
$_['column_debit']                     = 'Debit';
$_['column_credit']                    = 'Credit';
$_['column_balance']                   = 'Balance';
$_['column_running_balance']           = 'Running Balance';
$_['column_journal_entry']             = 'Journal Entry';
$_['column_source']                    = 'Source';
$_['column_created_by']                = 'Created By';
$_['column_action']                    = 'Action';

// Button
$_['button_generate']                  = 'Generate Statement';
$_['button_view']                      = 'View';
$_['button_print']                     = 'Print';
$_['button_export']                    = 'Export';
$_['button_excel']                     = 'Export Excel';
$_['button_pdf']                       = 'Export PDF';
$_['button_csv']                       = 'Export CSV';
$_['button_back']                      = 'Back';
$_['button_reset']                     = 'Reset';
$_['button_search']                    = 'Search';
$_['button_filter']                    = 'Filter';
$_['button_clear']                     = 'Clear';
$_['button_save']                      = 'Save';
$_['button_cancel']                    = 'Cancel';
$_['button_close']                     = 'Close';

// Error
$_['error_permission']                 = 'Warning: You do not have permission to access advanced account statement!';
$_['error_account_id']                 = 'Account selection is required!';
$_['error_date_start']                 = 'Start date is required!';
$_['error_date_end']                   = 'End date is required!';
$_['error_date_range']                 = 'Start date must be before end date!';
$_['error_no_data']                    = 'No data to display!';
$_['error_export']                     = 'Error exporting data!';
$_['error_print']                      = 'Error printing report!';
$_['error_generate']                   = 'Error generating account statement!';

// Warning
$_['warning_no_transactions']          = 'No transactions found for the account in the specified period';
$_['warning_no_data_export']           = 'No data to export';
$_['warning_no_data_print']            = 'No data to print';
$_['warning_no_data_view']             = 'No account statement data to view';

// Success
$_['success_generate']                 = 'Account statement generated successfully!';
$_['success_export']                   = 'Data exported successfully!';
$_['success_print']                    = 'Report prepared for printing!';

// Help
$_['help_account']                     = 'Select the account to generate statement for.';
$_['help_date_range']                  = 'Specify the time period for the account statement.';
$_['help_opening_balance']             = 'Include opening balance at the beginning of the statement.';
$_['help_closing_balance']             = 'Include closing balance at the end of the statement.';
$_['help_detailed_view']               = 'Show complete details for each transaction in the account.';

// Tab
$_['tab_general']                      = 'General';
$_['tab_options']                      = 'Options';
$_['tab_filters']                      = 'Filters';
$_['tab_format']                       = 'Format';
$_['tab_preview']                      = 'Preview';

// Summary
$_['text_opening_balance']             = 'Opening Balance';
$_['text_closing_balance']             = 'Closing Balance';
$_['text_total_debit']                 = 'Total Debit';
$_['text_total_credit']                = 'Total Credit';
$_['text_net_movement']                = 'Net Movement';
$_['text_transaction_count']           = 'Transaction Count';
$_['text_period']                      = 'Period';
$_['text_account_info']                = 'Account Information';
$_['text_statement_summary']           = 'Statement Summary';

// Advanced Features
$_['text_compare_periods']             = 'Compare Periods';
$_['text_trend_analysis']              = 'Trend Analysis';
$_['text_balance_chart']               = 'Balance Chart';
$_['text_monthly_summary']             = 'Monthly Summary';
$_['text_quarterly_summary']           = 'Quarterly Summary';
$_['text_yearly_summary']              = 'Yearly Summary';

// Export Options
$_['text_export_options']              = 'Export Options';
$_['text_include_summary']             = 'Include Summary';
$_['text_include_charts']              = 'Include Charts';
$_['text_page_orientation']            = 'Page Orientation';
$_['text_portrait']                    = 'Portrait';
$_['text_landscape']                   = 'Landscape';

// Sort Options
$_['text_sort_date_asc']               = 'Date (Ascending)';
$_['text_sort_date_desc']              = 'Date (Descending)';
$_['text_sort_amount_asc']             = 'Amount (Ascending)';
$_['text_sort_amount_desc']            = 'Amount (Descending)';
$_['text_sort_reference_asc']          = 'Reference (Ascending)';
$_['text_sort_reference_desc']         = 'Reference (Descending)';

// Filter Options
$_['text_filter_all']                  = 'All Transactions';
$_['text_filter_debit_only']           = 'Debit Only';
$_['text_filter_credit_only']          = 'Credit Only';
$_['text_filter_above_amount']         = 'Above Amount';
$_['text_filter_below_amount']         = 'Below Amount';

// AJAX Messages
$_['text_account_required']            = 'Account ID is required';
$_['text_entry_required']              = 'Entry ID is required';
$_['text_comparison_incomplete']       = 'Comparison data is incomplete';
$_['text_no_analysis_data']            = 'No account statement data for analysis';

// PDF/Excel Headers
$_['text_account_statement']           = 'Account Statement';
$_['text_account_statement_for']       = 'Account Statement for';
$_['text_period_from_to']              = 'From';
$_['text_to']                          = 'To';
$_['text_date_header']                 = 'Date';
$_['text_entry_number']                = 'Entry No.';
$_['text_description_header']          = 'Description';
$_['text_debit_header']                = 'Debit';
$_['text_credit_header']               = 'Credit';
$_['text_balance_header']              = 'Balance';

// Controller language variables
$_['log_unauthorized_access_account_statement_advanced'] = 'Unauthorized access attempt to advanced account statement';
$_['log_view_account_statement_advanced_screen'] = 'View advanced account statement screen';
$_['log_generate_statement_period'] = 'Generate advanced account statement for period';
$_['text_statement_generated_notification'] = 'Advanced account statement generated for account number';
$_['text_by_user'] = 'by';
$_['text_period_to'] = 'to';
$_['log_generate_statement_failed'] = 'Failed to generate advanced account statement';
$_['error_generate_statement'] = 'Error generating account statement';

// Missing variables from audit report - Critical fixes
$_['accounts/account_statement_advanced'] = '';
$_['code']                             = 'Code';
$_['date_format_short']                = 'm/d/Y';
$_['direction']                        = 'ltr';
$_['lang']                             = 'en';

// Enhanced performance and security variables
$_['text_optimized_statement']         = 'Optimized Statement';
$_['text_performance_analysis']        = 'Performance Analysis';
$_['text_cache_enabled']               = 'Cache Enabled';
$_['text_statement_cached']            = 'Statement Cached';
$_['error_invalid_account_id']         = 'Invalid account ID';
$_['error_invalid_start_date']         = 'Invalid start date';
$_['error_invalid_end_date']           = 'Invalid end date';
$_['error_date_range']                 = 'Start date must be before end date';
$_['text_monthly_analysis']            = 'Monthly Analysis';
$_['text_transaction_trends']          = 'Transaction Trends';
$_['button_performance_analysis']      = 'Performance Analysis';
$_['text_loading_statement']           = 'Loading statement...';
$_['text_statement_ready']             = 'Statement ready';
?>
