{{ header }}{{ column_left }}

<!-- AYM ERP ULTIMATE ENTERPRISE DASHBOARD - REVOLUTIONARY DESIGN -->

<style>
/* ═══════════════════════════════════════════════════════════════════════════════
   AYM ERP ULTIMATE DASHBOARD - ENTERPRISE GRADE PLUS DESIGN SYSTEM
   Revolutionary Dashboard that surpasses SAP, Oracle, Microsoft, Odoo
   ═══════════════════════════════════════════════════════════════════════════════ */

:root {
  /* ═══════════════════════════════════════════════════════════════════════════════
     AYM SCIENTIFIC COLOR SYSTEM - Based on Color Theory & Contrast Ratios
     ═══════════════════════════════════════════════════════════════════════════════ */

  /* Base Background Colors - Dark Theme for Maximum Contrast */
  --aym-bg-primary: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  --aym-bg-secondary: linear-gradient(135deg, #16213e 0%, #0f3460 100%);

  /* Financial Colors - Gold & Deep Blue (Complementary) */
  --aym-financial-primary: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
  --aym-financial-secondary: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  --aym-financial-accent: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  --aym-financial-success: linear-gradient(135deg, #00b894 0%, #00a085 100%);
  --aym-financial-bg: linear-gradient(135deg, #2d3436 0%, #636e72 100%);
  --aym-financial-text: #ffffff;

  /* Sales Colors - Energetic Orange & Teal (Triadic) */
  --aym-sales-primary: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
  --aym-sales-secondary: linear-gradient(135deg, #00cec9 0%, #00b894 100%);
  --aym-sales-accent: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
  --aym-sales-success: linear-gradient(135deg, #55a3ff 0%, #3742fa 100%);
  --aym-sales-bg: linear-gradient(135deg, #2d3436 0%, #636e72 100%);
  --aym-sales-text: #ffffff;

  /* Inventory Colors - Earth Tones (Analogous) */
  --aym-inventory-primary: linear-gradient(135deg, #6c5ce7 0%, #5f3dc4 100%);
  --aym-inventory-secondary: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%);
  --aym-inventory-accent: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
  --aym-inventory-success: linear-gradient(135deg, #00b894 0%, #00a085 100%);
  --aym-inventory-bg: linear-gradient(135deg, #2d3436 0%, #636e72 100%);
  --aym-inventory-text: #ffffff;

  /* HR Colors - Professional Blue & Warm Orange (Split Complementary) */
  --aym-hr-primary: linear-gradient(135deg, #0984e3 0%, #74b9ff 100%);
  --aym-hr-secondary: linear-gradient(135deg, #e17055 0%, #fdcb6e 100%);
  --aym-hr-accent: linear-gradient(135deg, #00b894 0%, #55efc4 100%);
  --aym-hr-success: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%);
  --aym-hr-bg: linear-gradient(135deg, #2d3436 0%, #636e72 100%);
  --aym-hr-text: #ffffff;

  /* E-commerce Colors - Modern Purple & Green (Complementary) */
  --aym-ecommerce-primary: linear-gradient(135deg, #6c5ce7 0%, #5f3dc4 100%);
  --aym-ecommerce-secondary: linear-gradient(135deg, #00b894 0%, #55efc4 100%);
  --aym-ecommerce-accent: linear-gradient(135deg, #fd79a8 0%, #fdcb6e 100%);
  --aym-ecommerce-success: linear-gradient(135deg, #0984e3 0%, #74b9ff 100%);
  --aym-ecommerce-bg: linear-gradient(135deg, #2d3436 0%, #636e72 100%);
  --aym-ecommerce-text: #ffffff;

  /* Procurement Colors - Industrial Steel & Copper (Monochromatic) */
  --aym-procurement-primary: linear-gradient(135deg, #636e72 0%, #2d3436 100%);
  --aym-procurement-secondary: linear-gradient(135deg, #e17055 0%, #d63031 100%);
  --aym-procurement-accent: linear-gradient(135deg, #fdcb6e 0%, #f39c12 100%);
  --aym-procurement-success: linear-gradient(135deg, #00b894 0%, #00a085 100%);
  --aym-procurement-bg: linear-gradient(135deg, #2d3436 0%, #636e72 100%);
  --aym-procurement-text: #ffffff;

  /* ETA Colors - Egyptian Flag Inspired (Red, White, Black, Gold) */
  --aym-eta-primary: linear-gradient(135deg, #d63031 0%, #e17055 100%);
  --aym-eta-secondary: linear-gradient(135deg, #2d3436 0%, #636e72 100%);
  --aym-eta-accent: linear-gradient(135deg, #FFD700 0%, #f39c12 100%);
  --aym-eta-success: linear-gradient(135deg, #00b894 0%, #55efc4 100%);
  --aym-eta-bg: linear-gradient(135deg, #2d3436 0%, #636e72 100%);
  --aym-eta-text: #ffffff;

  /* Special Effect Colors - High Contrast */
  --aym-glow-gold: #FFD700;
  --aym-glow-blue: #74b9ff;
  --aym-glow-green: #55efc4;
  --aym-glow-pink: #fd79a8;
  --aym-glow-purple: #a29bfe;
  --aym-glow-orange: #fdcb6e;

  /* Advanced Color System */
  --aym-dark: #1a1d29;
  --aym-darker: #151821;
  --aym-light: #f8fafc;
  --aym-lighter: #ffffff;
  --aym-gray-100: #f7fafc;
  --aym-gray-200: #edf2f7;
  --aym-gray-300: #e2e8f0;
  --aym-gray-400: #cbd5e0;
  --aym-gray-500: #a0aec0;
  --aym-gray-600: #718096;
  --aym-gray-700: #4a5568;
  --aym-gray-800: #2d3748;
  --aym-gray-900: #1a202c;

  /* Accessibility Colors - WCAG 2.1 AA Compliant */
  --aym-focus-color: #4A90E2;
  --aym-focus-shadow: 0 0 0 3px rgba(74, 144, 226, 0.5);
  --aym-error-color: #E53E3E;
  --aym-success-color: #38A169;
  --aym-warning-color: #D69E2E;
  --aym-info-color: #3182CE;

  /* High Contrast Mode Colors */
  --aym-high-contrast-bg: #000000;
  --aym-high-contrast-text: #ffffff;
  --aym-high-contrast-border: #ffffff;

  /* Typography Scale */
  --aym-font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --aym-font-size-xs: 0.75rem;
  --aym-font-size-sm: 0.875rem;
  --aym-font-size-base: 1rem;
  --aym-font-size-lg: 1.125rem;
  --aym-font-size-xl: 1.25rem;
  --aym-font-size-2xl: 1.5rem;
  --aym-font-size-3xl: 1.875rem;
  --aym-font-size-4xl: 2.25rem;
  --aym-font-size-5xl: 3rem;

  /* Spacing Scale */
  --aym-space-1: 0.25rem;
  --aym-space-2: 0.5rem;
  --aym-space-3: 0.75rem;
  --aym-space-4: 1rem;
  --aym-space-5: 1.25rem;
  --aym-space-6: 1.5rem;
  --aym-space-8: 2rem;
  --aym-space-10: 2.5rem;
  --aym-space-12: 3rem;
  --aym-space-16: 4rem;
  --aym-space-20: 5rem;

  /* Shadow System */
  --aym-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --aym-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --aym-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --aym-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --aym-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --aym-shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --aym-shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);

  /* Border Radius */
  --aym-radius-sm: 0.125rem;
  --aym-radius: 0.25rem;
  --aym-radius-md: 0.375rem;
  --aym-radius-lg: 0.5rem;
  --aym-radius-xl: 0.75rem;
  --aym-radius-2xl: 1rem;
  --aym-radius-3xl: 1.5rem;
  --aym-radius-full: 9999px;

  /* Animation Timing */
  --aym-transition-fast: 150ms ease-in-out;
  --aym-transition-base: 250ms ease-in-out;
  --aym-transition-slow: 350ms ease-in-out;
}

/* ═══════════════════════════════════════════════════════════════════════════════
   ACCESSIBILITY ENHANCEMENTS - WCAG 2.1 AA COMPLIANT
   ═══════════════════════════════════════════════════════════════════════════════ */

/* Skip Link for Screen Readers */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--aym-focus-color);
  color: white;
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 10000;
  font-weight: 600;
  transition: top 0.3s;
}

.skip-link:focus {
  top: 6px;
}

/* Focus Management */
*:focus {
  outline: 2px solid var(--aym-focus-color);
  outline-offset: 2px;
  box-shadow: var(--aym-focus-shadow);
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  :root {
    --aym-bg-primary: var(--aym-high-contrast-bg);
    --aym-text-primary: var(--aym-high-contrast-text);
    --aym-border-color: var(--aym-high-contrast-border);
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Screen Reader Only Content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* ═══════════════════════════════════════════════════════════════════════════════
   GLOBAL RESET & BASE STYLES
   ═══════════════════════════════════════════════════════════════════════════════ */

.aym-dashboard {
  font-family: var(--aym-font-family);
  direction: {{ direction }};
  background: var(--aym-bg-primary);
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
}

.aym-dashboard::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.4) 0%, transparent 60%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.4) 0%, transparent 60%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 60%),
    radial-gradient(circle at 60% 80%, rgba(255, 215, 0, 0.2) 0%, transparent 50%),
    radial-gradient(circle at 90% 10%, rgba(220, 20, 60, 0.2) 0%, transparent 50%),
    linear-gradient(45deg, rgba(138, 43, 226, 0.1) 0%, transparent 50%),
    linear-gradient(-45deg, rgba(34, 139, 34, 0.1) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
  animation: backgroundShift 30s ease-in-out infinite;
}

@keyframes backgroundShift {
  0%, 100% {
    background:
      radial-gradient(circle at 20% 80%, rgba(255, 215, 0, 0.15) 0%, transparent 70%),
      radial-gradient(circle at 80% 20%, rgba(116, 185, 255, 0.12) 0%, transparent 70%),
      radial-gradient(circle at 40% 40%, rgba(0, 184, 148, 0.1) 0%, transparent 70%);
  }
  25% {
    background:
      radial-gradient(circle at 30% 70%, rgba(253, 121, 168, 0.15) 0%, transparent 70%),
      radial-gradient(circle at 70% 30%, rgba(108, 92, 231, 0.12) 0%, transparent 70%),
      radial-gradient(circle at 50% 50%, rgba(253, 203, 110, 0.1) 0%, transparent 70%);
  }
  50% {
    background:
      radial-gradient(circle at 40% 60%, rgba(162, 155, 254, 0.15) 0%, transparent 70%),
      radial-gradient(circle at 60% 40%, rgba(225, 112, 85, 0.12) 0%, transparent 70%),
      radial-gradient(circle at 80% 80%, rgba(85, 239, 196, 0.1) 0%, transparent 70%);
  }
  75% {
    background:
      radial-gradient(circle at 10% 90%, rgba(9, 132, 227, 0.15) 0%, transparent 70%),
      radial-gradient(circle at 90% 10%, rgba(214, 48, 49, 0.12) 0%, transparent 70%),
      radial-gradient(circle at 30% 30%, rgba(0, 184, 148, 0.1) 0%, transparent 70%);
  }
}

.aym-dashboard::after {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.02) 50%, transparent 70%),
    linear-gradient(-45deg, transparent 30%, rgba(255, 255, 255, 0.01) 50%, transparent 70%);
  pointer-events: none;
  z-index: -1;
  animation: meshPattern 20s linear infinite;
}

@keyframes meshPattern {
  0% { transform: translateX(0) translateY(0); }
  25% { transform: translateX(50px) translateY(-30px); }
  50% { transform: translateX(-30px) translateY(50px); }
  75% { transform: translateX(30px) translateY(30px); }
  100% { transform: translateX(0) translateY(0); }
}

/* ═══════════════════════════════════════════════════════════════════════════════
   REVOLUTIONARY HEADER DESIGN
   ═══════════════════════════════════════════════════════════════════════════════ */

.aym-header {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--aym-radius-2xl);
  margin: var(--aym-space-6);
  padding: var(--aym-space-8);
  box-shadow: var(--aym-shadow-2xl);
  position: relative;
  overflow: hidden;
}

.aym-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
  pointer-events: none;
}

.aym-header-content {
  position: relative;
  z-index: 2;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--aym-space-6);
}

.aym-title-section {
  flex: 1;
  min-width: 300px;
}

.aym-title {
  font-size: var(--aym-font-size-4xl);
  font-weight: 800;
  color: white;
  margin: 0;
  text-shadow: 0 4px 8px rgba(0,0,0,0.3);
  background: linear-gradient(135deg, #ffffff 0%, #f0f9ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1.2;
}

.aym-subtitle {
  font-size: var(--aym-font-size-lg);
  color: rgba(255, 255, 255, 0.8);
  margin: var(--aym-space-2) 0 0 0;
  font-weight: 500;
}

.aym-stats-bar {
  display: flex;
  gap: var(--aym-space-8);
  align-items: center;
  flex-wrap: wrap;
}

.aym-stat-item {
  text-align: center;
  color: white;
}

.aym-stat-value {
  font-size: var(--aym-font-size-2xl);
  font-weight: 700;
  display: block;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.aym-stat-label {
  font-size: var(--aym-font-size-sm);
  opacity: 0.9;
  margin-top: var(--aym-space-1);
}

.aym-actions {
  display: flex;
  gap: var(--aym-space-3);
  align-items: center;
  flex-wrap: wrap;
}

/* ═══════════════════════════════════════════════════════════════════════════════
   REVOLUTIONARY BUTTON SYSTEM
   ═══════════════════════════════════════════════════════════════════════════════ */

.aym-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--aym-space-2);
  padding: var(--aym-space-3) var(--aym-space-6);
  border-radius: var(--aym-radius-xl);
  font-weight: 600;
  font-size: var(--aym-font-size-sm);
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: all var(--aym-transition-base);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.aym-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left var(--aym-transition-slow);
}

.aym-btn:hover::before {
  left: 100%;
}

.aym-btn-primary {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.aym-btn-primary:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
  box-shadow: var(--aym-shadow-lg);
}

.aym-btn-secondary {
  background: rgba(0, 0, 0, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.aym-btn-secondary:hover {
  background: rgba(0, 0, 0, 0.2);
  transform: translateY(-2px);
}

/* ═══════════════════════════════════════════════════════════════════════════════
   REVOLUTIONARY GRID SYSTEM
   ═══════════════════════════════════════════════════════════════════════════════ */

.aym-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 var(--aym-space-6);
}

.aym-grid {
  display: grid;
  gap: var(--aym-space-6);
  margin: var(--aym-space-6) 0;
}

.aym-grid-1 { grid-template-columns: 1fr; }
.aym-grid-2 { grid-template-columns: repeat(2, 1fr); }
.aym-grid-3 { grid-template-columns: repeat(3, 1fr); }
.aym-grid-4 { grid-template-columns: repeat(4, 1fr); }
.aym-grid-5 { grid-template-columns: repeat(5, 1fr); }
.aym-grid-6 { grid-template-columns: repeat(6, 1fr); }

.aym-grid-auto {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.aym-grid-responsive {
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

/* ═══════════════════════════════════════════════════════════════════════════════
   REVOLUTIONARY CARD SYSTEM
   ═══════════════════════════════════════════════════════════════════════════════ */

.aym-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--aym-radius-2xl);
  padding: var(--aym-space-6);
  box-shadow: var(--aym-shadow-xl);
  transition: all var(--aym-transition-base);
  position: relative;
  overflow: hidden;
}

.aym-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
  pointer-events: none;
  opacity: 0;
  transition: opacity var(--aym-transition-base);
}

.aym-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--aym-shadow-2xl);
  border-color: rgba(255, 255, 255, 0.3);
}

.aym-card:hover::before {
  opacity: 1;
}

.aym-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--aym-space-6);
  padding-bottom: var(--aym-space-4);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.aym-card-title {
  font-size: var(--aym-font-size-xl);
  font-weight: 700;
  color: white;
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--aym-space-3);
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

.aym-card-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: var(--aym-radius-lg);
  color: white;
}

.aym-card-actions {
  display: flex;
  gap: var(--aym-space-2);
}

.aym-card-body {
  position: relative;
  z-index: 2;
}

/* ═══════════════════════════════════════════════════════════════════════════════
   REVOLUTIONARY KPI CARDS
   ═══════════════════════════════════════════════════════════════════════════════ */

.aym-kpi-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(25px);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--aym-radius-2xl);
  padding: var(--aym-space-8);
  text-align: center;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  overflow: hidden;
  cursor: pointer;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 4px 16px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.aym-kpi-card::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(
    from 0deg,
    transparent 0deg,
    rgba(255, 255, 255, 0.1) 90deg,
    transparent 180deg,
    rgba(255, 255, 255, 0.05) 270deg,
    transparent 360deg
  );
  opacity: 0;
  transition: all 0.6s ease;
  animation: rotate 20s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.aym-kpi-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    circle at 50% 50%,
    rgba(255, 255, 255, 0.1) 0%,
    transparent 70%
  );
  opacity: 0;
  transition: opacity 0.4s ease;
}

.aym-kpi-card:hover {
  transform: translateY(-12px) scale(1.05) rotateX(5deg);
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.2),
    0 15px 30px rgba(0, 0, 0, 0.1),
    0 5px 15px rgba(0, 0, 0, 0.05),
    inset 0 2px 0 rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(30px);
}

.aym-kpi-card:hover::before {
  opacity: 0.7;
  animation-duration: 3s;
}

.aym-kpi-card:hover::after {
  opacity: 1;
}

.aym-kpi-card:active {
  transform: translateY(-8px) scale(1.02);
  transition: all 0.1s ease;
}

.aym-kpi-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto var(--aym-space-6);
  background: rgba(255, 255, 255, 0.15);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.5rem;
  color: white;
  position: relative;
  z-index: 3;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow:
    0 8px 25px rgba(0, 0, 0, 0.15),
    inset 0 2px 0 rgba(255, 255, 255, 0.2),
    inset 0 -2px 0 rgba(0, 0, 0, 0.1);
}

.aym-kpi-icon::before {
  content: '';
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  border-radius: 50%;
  background: linear-gradient(45deg,
    rgba(255, 255, 255, 0.2) 0%,
    transparent 50%,
    rgba(255, 255, 255, 0.1) 100%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.aym-kpi-card:hover .aym-kpi-icon {
  transform: scale(1.1) rotateY(15deg);
  box-shadow:
    0 15px 35px rgba(0, 0, 0, 0.2),
    inset 0 3px 0 rgba(255, 255, 255, 0.3),
    inset 0 -3px 0 rgba(0, 0, 0, 0.15);
}

.aym-kpi-card:hover .aym-kpi-icon::before {
  opacity: 1;
}

.aym-kpi-value {
  font-size: 3.5rem;
  font-weight: 900;
  color: white;
  margin: 0;
  text-shadow:
    0 4px 8px rgba(0,0,0,0.4),
    0 2px 4px rgba(0,0,0,0.3),
    0 1px 2px rgba(0,0,0,0.2);
  position: relative;
  z-index: 3;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 1) 0%,
    rgba(255, 255, 255, 0.9) 50%,
    rgba(255, 255, 255, 0.8) 100%
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  transition: all 0.3s ease;
}

.aym-kpi-card:hover .aym-kpi-value {
  transform: scale(1.05);
  text-shadow:
    0 6px 12px rgba(0,0,0,0.5),
    0 3px 6px rgba(0,0,0,0.4),
    0 1px 3px rgba(0,0,0,0.3);
}

.aym-kpi-label {
  font-size: var(--aym-font-size-sm);
  color: rgba(255, 255, 255, 0.9);
  margin: var(--aym-space-2) 0 0 0;
  font-weight: 500;
  position: relative;
  z-index: 2;
}

.aym-kpi-trend {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--aym-space-1);
  margin-top: var(--aym-space-3);
  font-size: var(--aym-font-size-xs);
  font-weight: 600;
  position: relative;
  z-index: 2;
}

.aym-kpi-trend.positive {
  color: #10b981;
}

.aym-kpi-trend.negative {
  color: #ef4444;
}

.aym-kpi-trend.neutral {
  color: #6b7280;
}

/* ═══════════════════════════════════════════════════════════════════════════════
   REVOLUTIONARY MINI KPI SYSTEM
   ═══════════════════════════════════════════════════════════════════════════════ */

.aym-kpi-mini-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--aym-space-4);
  margin-top: var(--aym-space-4);
}

.aym-kpi-mini {
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: var(--aym-radius-lg);
  padding: var(--aym-space-3);
  text-align: center;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  cursor: pointer;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.aym-kpi-mini::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, transparent 100%);
  opacity: 0;
  transition: opacity var(--aym-transition-base);
}

.aym-kpi-mini:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-4px) scale(1.02);
  box-shadow:
    0 8px 25px rgba(0, 0, 0, 0.2),
    0 0 20px rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(15px);
}

.aym-kpi-mini:hover::before {
  opacity: 1;
}

.aym-kpi-mini-value {
  display: block;
  font-size: var(--aym-font-size-lg);
  font-weight: 700;
  color: white;
  margin-bottom: var(--aym-space-1);
  position: relative;
  z-index: 2;
  text-shadow: 0 0 8px rgba(255, 255, 255, 0.4);
  transition: all 0.3s ease;
}

.aym-kpi-mini-label {
  display: block;
  font-size: var(--aym-font-size-xs);
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
  position: relative;
  z-index: 2;
  text-shadow: 0 0 5px rgba(255, 255, 255, 0.2);
}

.aym-kpi-mini:hover .aym-kpi-mini-value {
  text-shadow: 0 0 12px rgba(255, 255, 255, 0.6);
  transform: scale(1.05);
}

/* ═══════════════════════════════════════════════════════════════════════════════
   REVOLUTIONARY KPI EXPANSION MODAL
   ═══════════════════════════════════════════════════════════════════════════════ */

.aym-kpi-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  z-index: 9999;
  display: none;
  align-items: center;
  justify-content: center;
  padding: var(--aym-space-6);
}

.aym-kpi-modal.active {
  display: flex;
}

.aym-kpi-modal-content {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--aym-radius-2xl);
  padding: var(--aym-space-8);
  max-width: 90vw;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  width: 1200px;
}

.aym-kpi-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--aym-space-6);
  padding-bottom: var(--aym-space-4);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.aym-kpi-modal-title {
  font-size: var(--aym-font-size-2xl);
  font-weight: 700;
  color: white;
  margin: 0;
}

.aym-kpi-modal-close {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--aym-radius-lg);
  color: white;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--aym-transition-base);
}

.aym-kpi-modal-close:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

.aym-kpi-modal-body {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--aym-space-4);
}

.aym-kpi-detail-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--aym-radius-xl);
  padding: var(--aym-space-4);
  transition: all var(--aym-transition-base);
}

.aym-kpi-detail-card:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.aym-kpi-detail-title {
  font-size: var(--aym-font-size-sm);
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  margin: 0 0 var(--aym-space-2) 0;
}

.aym-kpi-detail-value {
  font-size: var(--aym-font-size-xl);
  font-weight: 700;
  color: white;
  margin: 0 0 var(--aym-space-1) 0;
}

.aym-kpi-detail-description {
  font-size: var(--aym-font-size-xs);
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
}

/* ═══════════════════════════════════════════════════════════════════════════════
   LOADING OVERLAY STYLES
   ═══════════════════════════════════════════════════════════════════════════════ */

.aym-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(5px);
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.aym-loading-spinner {
  text-align: center;
  color: white;
}

.aym-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid var(--aym-glow-blue);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto var(--aym-space-4);
}

/* ═══════════════════════════════════════════════════════════════════════════════
   HIGH CONTRAST MODE SUPPORT
   ═══════════════════════════════════════════════════════════════════════════════ */

@media (prefers-contrast: high) {
  .aym-kpi-card {
    border-width: 3px;
    background: rgba(0, 0, 0, 0.9);
  }

  .aym-kpi-value {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 1);
  }

  .aym-btn {
    border-width: 2px;
  }
}

/* ═══════════════════════════════════════════════════════════════════════════════
   REDUCED MOTION SUPPORT
   ═══════════════════════════════════════════════════════════════════════════════ */

@media (prefers-reduced-motion: reduce) {
  .aym-kpi-card,
  .aym-particle,
  .aym-hexagon,
  .aym-triangle {
    animation: none;
    transition: none;
  }

  .aym-kpi-card:hover {
    transform: none;
  }
}

/* ═══════════════════════════════════════════════════════════════════════════════
   REVOLUTIONARY CHART CONTAINERS
   ═══════════════════════════════════════════════════════════════════════════════ */

.aym-chart-container {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--aym-radius-2xl);
  padding: var(--aym-space-6);
  height: 400px;
  position: relative;
  overflow: hidden;
}

.aym-chart-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255,255,255,0.05) 0%, transparent 100%);
  pointer-events: none;
}

.aym-chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--aym-space-4);
  position: relative;
  z-index: 2;
}

.aym-chart-title {
  font-size: var(--aym-font-size-lg);
  font-weight: 600;
  color: white;
  margin: 0;
}

.aym-chart-controls {
  display: flex;
  gap: var(--aym-space-2);
}

.aym-chart-btn {
  padding: var(--aym-space-2) var(--aym-space-3);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--aym-radius-lg);
  color: white;
  font-size: var(--aym-font-size-xs);
  cursor: pointer;
  transition: all var(--aym-transition-base);
}

.aym-chart-btn:hover,
.aym-chart-btn.active {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

.aym-chart-canvas {
  position: relative;
  z-index: 2;
  height: calc(100% - 60px);
}

.breadcrumb-modern > li + li:before {
  content: "{{ direction == 'rtl' ? '\\f053' : '\\f054' }}";
  font-family: FontAwesome;
  color: rgba(255,255,255,0.6);
  padding: 0 8px;
}

.breadcrumb-modern a {
  color: white;
  text-decoration: none;
}

.breadcrumb-modern a:hover {
  color: #ffd700;
}

/* Modern Card Design */
.card {
  border-radius: 12px;
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.card-header {
  border-radius: 12px 12px 0 0 !important;
  border-bottom: none;
  padding: 15px 20px;
}

.bg-gradient-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Form Enhancements */
.form-label {
  font-size: 13px;
  color: #495057;
  margin-bottom: 5px;
}

.form-control, .custom-select {
  border-radius: 8px;
  border: 1px solid #e3e6f0;
  padding: 10px 15px;
  transition: all 0.3s ease;
}

.form-control:focus, .custom-select:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.input-group-text {
  border-radius: 0;
  border-color: #e3e6f0;
  background-color: #f8f9fc;
  color: #5a5c69;
}

/* Button Enhancements */
.btn {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-outline-primary {
  border-color: #667eea;
  color: #667eea;
}

.btn-outline-primary:hover {
  background-color: #667eea;
  border-color: #667eea;
  transform: translateY(-1px);
}

/* RTL Support */
.dashboard-container[dir="rtl"] {
  text-align: right;
}

.dashboard-container[dir="rtl"] .float-right {
  float: left !important;
}

.dashboard-container[dir="rtl"] .float-left {
  float: right !important;
}

.dashboard-container[dir="rtl"] .text-left {
  text-align: right !important;
}

.dashboard-container[dir="rtl"] .text-right {
  text-align: left !important;
}

.dashboard-container[dir="rtl"] .mr-2 {
  margin-right: 0 !important;
  margin-left: 0.5rem !important;
}

.dashboard-container[dir="rtl"] .ml-2 {
  margin-left: 0 !important;
  margin-right: 0.5rem !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-title {
    font-size: 22px;
  }

  .dashboard-actions {
    float: none !important;
    text-align: center;
    margin-bottom: 15px;
  }

  .btn-dashboard {
    margin: 5px;
    display: inline-block;
  }

  .btn-group-justified .btn {
    font-size: 12px;
    padding: 8px 12px;
  }
}

/* Loading States */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

.loading::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ═══════════════════════════════════════════════════════════════════════════════
   REVOLUTIONARY GEOMETRIC SHAPES & PATTERNS
   ═══════════════════════════════════════════════════════════════════════════════ */

.aym-geometric-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  z-index: 1;
}

.aym-geometric-bg::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background:
    radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.05) 0%, transparent 50%),
    linear-gradient(45deg, transparent 40%, rgba(255, 255, 255, 0.02) 50%, transparent 60%);
  animation: geometricFloat 15s ease-in-out infinite;
}

@keyframes geometricFloat {
  0%, 100% { transform: translate(0, 0) rotate(0deg); }
  25% { transform: translate(10px, -10px) rotate(90deg); }
  50% { transform: translate(-5px, 5px) rotate(180deg); }
  75% { transform: translate(-10px, -5px) rotate(270deg); }
}

.aym-hexagon {
  position: absolute;
  width: 40px;
  height: 40px;
  top: 10%;
  left: 10%;
  background: rgba(255, 255, 255, 0.05);
  clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
  animation: hexagonRotate 8s linear infinite;
}

@keyframes hexagonRotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.aym-triangle {
  position: absolute;
  width: 0;
  height: 0;
  top: 70%;
  right: 15%;
  border-left: 20px solid transparent;
  border-right: 20px solid transparent;
  border-bottom: 35px solid rgba(255, 255, 255, 0.08);
  animation: triangleFloat 6s ease-in-out infinite;
}

@keyframes triangleFloat {
  0%, 100% { transform: translateY(0) rotate(0deg); }
  50% { transform: translateY(-15px) rotate(180deg); }
}

/* ═══════════════════════════════════════════════════════════════════════════════
   AI INSIGHTS PANEL STYLES
   ═══════════════════════════════════════════════════════════════════════════════ */

.aym-ai-insights-panel {
  animation: fadeInDown 0.8s ease-out;
}

.aym-ai-insight-item {
  display: flex;
  align-items: center;
  gap: var(--aym-space-3);
  padding: var(--aym-space-3);
  margin-bottom: var(--aym-space-2);
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--aym-radius-lg);
  border-left: 4px solid var(--aym-glow-blue);
  transition: all 0.3s ease;
  color: white;
  font-size: var(--aym-font-size-sm);
}

.aym-ai-insight-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateX(5px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.aym-ai-insight-item:last-child {
  margin-bottom: 0;
}

/* ═══════════════════════════════════════════════════════════════════════════════
   ACCESSIBILITY ENHANCEMENTS
   ═══════════════════════════════════════════════════════════════════════════════ */

.aym-kpi-card:focus,
.aym-btn:focus,
.aym-kpi-mini:focus {
  outline: 3px solid var(--aym-glow-blue);
  outline-offset: 2px;
}

.aym-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* ═══════════════════════════════════════════════════════════════════════════════
   MOBILE RESPONSIVENESS IMPROVEMENTS
   ═══════════════════════════════════════════════════════════════════════════════ */

@media (max-width: 480px) {
  .aym-kpi-card {
    padding: var(--aym-space-4);
  }

  .aym-kpi-icon {
    width: 60px;
    height: 60px;
    font-size: 1.8rem;
  }

  .aym-kpi-value {
    font-size: 2.5rem;
  }

  .aym-header {
    margin: var(--aym-space-3);
    padding: var(--aym-space-4);
  }

  .aym-title {
    font-size: var(--aym-font-size-2xl);
  }
}

/* ═══════════════════════════════════════════════════════════════════════════════
   PERFORMANCE METRICS BAR STYLES
   ═══════════════════════════════════════════════════════════════════════════════ */

.aym-metric-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--aym-space-2);
  min-width: 140px;
}

.aym-metric-label {
  font-size: var(--aym-font-size-xs);
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
  text-align: center;
}

.aym-metric-value {
  font-size: var(--aym-font-size-sm);
  color: white;
  font-weight: 600;
  text-shadow: 0 0 8px rgba(255, 255, 255, 0.3);
}

/* ═══════════════════════════════════════════════════════════════════════════════
   PERFORMANCE OPTIMIZATIONS
   ═══════════════════════════════════════════════════════════════════════════════ */

.aym-kpi-card,
.aym-card {
  will-change: transform;
  contain: layout style paint;
}

.aym-particles {
  will-change: transform;
  contain: strict;
}

/* ═══════════════════════════════════════════════════════════════════════════════
   PROGRESS BAR STYLES
   ═══════════════════════════════════════════════════════════════════════════════ */

.aym-progress {
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.aym-progress-bar {
  height: 100%;
  border-radius: 4px;
  transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.aym-progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: progressShine 2s ease-in-out infinite;
}

@keyframes progressShine {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes slideInRight {
  from { transform: translateX(100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes slideOutRight {
  from { transform: translateX(0); opacity: 1; }
  to { transform: translateX(100%); opacity: 0; }
}

@keyframes fadeInDown {
  from { transform: translateY(-30px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

/* ═══════════════════════════════════════════════════════════════════════════════
   REVOLUTIONARY PARTICLE EFFECTS
   ═══════════════════════════════════════════════════════════════════════════════ */

.aym-particles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  z-index: 1;
}

.aym-particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  animation: particleFloat 8s ease-in-out infinite;
}

.aym-particle:nth-child(1) { top: 20%; left: 10%; animation-delay: 0s; }
.aym-particle:nth-child(2) { top: 40%; left: 20%; animation-delay: 1s; }
.aym-particle:nth-child(3) { top: 60%; left: 30%; animation-delay: 2s; }
.aym-particle:nth-child(4) { top: 80%; left: 40%; animation-delay: 3s; }
.aym-particle:nth-child(5) { top: 30%; left: 60%; animation-delay: 4s; }

@keyframes particleFloat {
  0%, 100% {
    transform: translateY(0) scale(1);
    opacity: 0.3;
  }
  25% {
    transform: translateY(-20px) scale(1.2);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-10px) scale(0.8);
    opacity: 0.9;
  }
  75% {
    transform: translateY(-30px) scale(1.1);
    opacity: 0.4;
  }
}
</style>

<div id="content" class="dashboard-container">
  <!-- Modern Enterprise Header -->
  <div class="dashboard-header">
    <div class="container-fluid">
      <div class="dashboard-actions">
        <button type="button" id="refresh-dashboard" class="btn btn-primary btn-dashboard"
                title="{{ text_refresh_data }}" data-toggle="tooltip">
          <i class="fa fa-refresh"></i> {{ text_refresh }}
        </button>
        <button type="button" id="export-dashboard" class="btn btn-success btn-dashboard"
                title="{{ text_export_data }}" data-toggle="tooltip">
          <i class="fa fa-download"></i> {{ text_export }}
        </button>
        <button type="button" id="print-dashboard" class="btn btn-info btn-dashboard"
                title="{{ text_print_report }}" data-toggle="tooltip">
          <i class="fa fa-print"></i> {{ text_print }}
        </button>
        <button type="button" id="settings-dashboard" class="btn btn-warning btn-dashboard"
                title="{{ text_dashboard_settings }}" data-toggle="tooltip">
          <i class="fa fa-cog"></i> {{ text_settings }}
        </button>
      </div>
      <h1 class="dashboard-title">
        <i class="fa fa-tachometer"></i> {{ text_smart_dashboard }}
      </h1>
      <ul class="breadcrumb breadcrumb-modern">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>

  <!-- Smart Filters Panel -->
  <div class="container-fluid">
    <div class="panel panel-default">
      <div class="panel-heading bg-primary text-white">
        <h4 class="panel-title">
          <i class="fa fa-filter"></i> {{ text_smart_filters }}
          <button type="button" class="btn btn-sm btn-default {{ direction == 'rtl' ? 'pull-left' : 'pull-right' }}"
                  id="toggle-filters" title="{{ text_toggle_filters }}">
            <i class="fa fa-chevron-up"></i>
          </button>
        </h4>
      </div>
      <div class="panel-body" id="filters-body">
        <form id="dashboard-filters" method="get" class="needs-validation" novalidate>
          <input type="hidden" name="route" value="common/dashboard">
          <input type="hidden" name="user_token" value="{{ user_token }}">
          <input type="hidden" name="{{ csrf_token_name }}" value="{{ csrf_token }}">

          <div class="row">
            <!-- Date Range Filter -->
            <div class="col-lg-3 col-md-6">
              <div class="form-group">
                <label class="control-label">
                  <i class="fa fa-calendar text-primary"></i> {{ text_date_range }}
                </label>
                <div class="input-group">
                  <input type="date" name="date_from" class="form-control"
                         value="{{ current_filters.date_from }}" required>
                  <div class="input-group-addon">
                    <span class="input-group-text">{{ text_to }}</span>
                  </div>
                  <input type="date" name="date_to" class="form-control"
                         value="{{ current_filters.date_to }}" required>
                </div>
              </div>
            </div>

            <!-- Branch Filter -->
            <div class="col-lg-2 col-md-6">
              <div class="form-group">
                <label class="control-label">
                  <i class="fa fa-building text-success"></i> {{ text_branch }}
                </label>
                <select name="branch_id" class="form-control">
                  <option value="">{{ text_all_branches }}</option>
                  {% for branch in branches %}
                  <option value="{{ branch.branch_id }}"
                          {% if branch.branch_id == current_filters.branch_id %}selected{% endif %}>
                    {{ branch.name }}
                  </option>
                  {% endfor %}
                </select>
              </div>
            </div>

            <!-- Source Filter -->
            <div class="col-lg-2 col-md-6">
              <div class="form-group">
                <label class="control-label">
                  <i class="fa fa-source-fork text-info"></i> {{ text_source }}
                </label>
                <select name="source" class="form-control">
                  <option value="">{{ text_all_sources }}</option>
                  <option value="online" {% if current_filters.source == 'online' %}selected{% endif %}>
                    {{ text_online_store }}
                  </option>
                  <option value="pos" {% if current_filters.source == 'pos' %}selected{% endif %}>
                    {{ text_pos_systems }}
                  </option>
                  <option value="direct" {% if current_filters.source == 'direct' %}selected{% endif %}>
                    {{ text_direct_sales }}
                  </option>
                </select>
              </div>
            </div>

            <!-- Apply Button -->
            <div class="col-lg-2 col-md-6">
              <div class="form-group">
                <label class="control-label">&nbsp;</label>
                <button type="submit" class="btn btn-primary btn-block">
                  <i class="fa fa-search"></i> {{ text_apply }}
                </button>
              </div>
            </div>

            <!-- Quick Filters -->
            <div class="col-lg-3 col-md-12">
              <div class="form-group">
                <label class="control-label">
                  <i class="fa fa-clock-o text-warning"></i> {{ text_quick_filters }}
                </label>
                <div class="btn-group btn-group-justified d-flex" role="group">
                  <a href="?route=common/dashboard&user_token={{ user_token }}&date_from={{ 'now'|date('Y-m-d') }}&date_to={{ 'now'|date('Y-m-d') }}"
                     class="btn btn-outline-primary flex-fill">{{ text_today }}</a>
                  <a href="?route=common/dashboard&user_token={{ user_token }}&date_from={{ 'now'|date('Y-m-01') }}&date_to={{ 'now'|date('Y-m-d') }}"
                     class="btn btn-outline-primary flex-fill">{{ text_this_month }}</a>
                  <a href="?route=common/dashboard&user_token={{ user_token }}&date_from={{ 'now'|date('Y-01-01') }}&date_to={{ 'now'|date('Y-m-d') }}"
                     class="btn btn-outline-primary flex-fill">{{ text_this_year }}</a>
                </div>
              </div>
            </div>
          </div>

          <!-- Advanced Filters (Collapsible) -->
          <div class="row">
            <div class="col-12">
              <button type="button" class="btn btn-link text-muted" data-toggle="collapse"
                      data-target="#advanced-filters" aria-expanded="false">
                <i class="fa fa-plus"></i> {{ text_advanced_filters }}
              </button>
            </div>
          </div>

          <div class="collapse" id="advanced-filters">
            <hr>
            <div class="row">
              <div class="col-md-3">
                <div class="form-group">
                  <label class="control-label">{{ text_category }}</label>
                  <select name="category_id" class="form-control">
                    <option value="">{{ text_all_categories }}</option>
                    {% for category in categories %}
                    <option value="{{ category.category_id }}">{{ category.name }}</option>
                    {% endfor %}
                  </select>
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label class="control-label">{{ text_customer_group }}</label>
                  <select name="customer_group_id" class="form-control">
                    <option value="">{{ text_all_customer_groups }}</option>
                    {% for group in customer_groups %}
                    <option value="{{ group.customer_group_id }}">{{ group.name }}</option>
                    {% endfor %}
                  </select>
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label class="control-label">{{ text_currency }}</label>
                  <select name="currency" class="form-control">
                    <option value="">{{ text_all_currencies }}</option>
                    {% for currency in currencies %}
                    <option value="{{ currency.code }}">{{ currency.title }}</option>
                    {% endfor %}
                  </select>
                </div>
              </div>
              <div class="col-md-3">
                <div class="form-group">
                  <label class="control-label">{{ text_order_status }}</label>
                  <select name="order_status_id" class="form-control">
                    <option value="">{{ text_all_statuses }}</option>
                    {% for status in order_statuses %}
                    <option value="{{ status.order_status_id }}">{{ status.name }}</option>
                    {% endfor %}
                  </select>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>

<!-- ═══════════════════════════════════════════════════════════════════════════════
     AYM ERP ULTIMATE DASHBOARD - REVOLUTIONARY INTERFACE
     Enterprise Grade Plus Dashboard that surpasses all competitors
     ═══════════════════════════════════════════════════════════════════════════════ -->

<div class="aym-dashboard">

  <!-- REVOLUTIONARY HEADER SECTION -->
  <div class="aym-header aym-animate-fade-in-up">
    <div class="aym-header-content">
      <div class="aym-title-section">
        <h1 class="aym-title">{{ heading_title|default('AYM ERP Executive Dashboard') }}</h1>
        <p class="aym-subtitle">{{ text_smart_dashboard|default('Smart Business Intelligence Dashboard') }}</p>
      </div>

      <div class="aym-stats-bar">
        <div class="aym-stat-item">
          <span class="aym-stat-value" id="live-revenue">{{ financial_summary.total_revenue|number_format(0)|default('0') }}</span>
          <span class="aym-stat-label">{{ text_total_revenue|default('Total Revenue') }}</span>
        </div>
        <div class="aym-stat-item">
          <span class="aym-stat-value" id="live-orders">{{ sales_stats.today_orders|default('0') }}</span>
          <span class="aym-stat-label">{{ text_today_orders|default('Today Orders') }}</span>
        </div>
        <div class="aym-stat-item">
          <span class="aym-stat-value" id="live-customers">{{ crm_stats.active_customers|default('0') }}</span>
          <span class="aym-stat-label">{{ text_active_customers|default('Active Customers') }}</span>
        </div>
      </div>

      <div class="aym-actions">
        <button class="aym-btn aym-btn-primary" onclick="refreshDashboard()">
          <i class="fa fa-refresh"></i>
          {{ text_refresh|default('Refresh') }}
        </button>
        <button class="aym-btn aym-btn-secondary" onclick="exportDashboard()">
          <i class="fa fa-download"></i>
          {{ text_export|default('Export') }}
        </button>
        <button class="aym-btn aym-btn-secondary" onclick="openSettings()">
          <i class="fa fa-cog"></i>
          {{ text_settings|default('Settings') }}
        </button>
      </div>
    </div>
  </div>

  <div class="aym-container">

    <!-- AI-Powered Smart Insights Panel -->
    <div class="aym-ai-insights-panel" style="margin-bottom: var(--aym-space-6);">
      <div class="aym-card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: 2px solid var(--aym-glow-blue);">
        <div class="aym-card-header">
          <h3 class="aym-card-title">
            <div class="aym-card-icon" style="background: var(--aym-glow-blue);">
              <i class="fa fa-brain"></i>
            </div>
            AI Business Intelligence Insights
          </h3>
          <div class="aym-card-actions">
            <button class="aym-btn aym-btn-secondary" onclick="refreshAIInsights()" style="border-color: var(--aym-glow-blue);">
              <i class="fa fa-sync-alt"></i>
            </button>
          </div>
        </div>
        <div class="aym-card-body">
          <div class="aym-ai-insight-item">
            <i class="fa fa-lightbulb-o" style="color: var(--aym-glow-gold);"></i>
            <span>Revenue trend shows 15% growth potential in Q2 based on current patterns</span>
          </div>
          <div class="aym-ai-insight-item">
            <i class="fa fa-exclamation-triangle" style="color: var(--aym-glow-orange);"></i>
            <span>Inventory levels for top 3 products need attention within 7 days</span>
          </div>
          <div class="aym-ai-insight-item">
            <i class="fa fa-line-chart" style="color: var(--aym-glow-green);"></i>
            <span>Customer acquisition cost decreased by 8% this month</span>
          </div>
        </div>
      </div>
    </div>

    <!-- REVOLUTIONARY COMPREHENSIVE KPI SECTION - استغلال 213 مؤشر -->
    <div class="aym-grid aym-grid-6">

      <!-- Financial Performance KPI -->
      <div class="aym-kpi-card aym-animate-fade-in-up"
           style="animation-delay: 0.1s; background: var(--aym-financial-bg); border: 2px solid var(--aym-glow-gold); box-shadow: 0 0 30px rgba(255, 215, 0, 0.3);"
           onclick="openKPIDetails('financial')"
           tabindex="0"
           role="button"
           aria-label="Financial Performance KPI - Revenue Today"
           data-kpi-category="financial"
           data-kpi-value="{{ kpi_data.KPI001_RevenuesToday.value|number_format(0)|default('0') }}"
           data-testid="financial-kpi-card">
        <div class="aym-geometric-bg">
          <div class="aym-hexagon" style="background: var(--aym-financial-primary); box-shadow: 0 0 20px var(--aym-glow-gold);"></div>
          <div class="aym-triangle" style="border-bottom-color: rgba(255, 215, 0, 0.4);"></div>
        </div>
        <div class="aym-particles">
          <div class="aym-particle" style="background: var(--aym-glow-gold); box-shadow: 0 0 10px var(--aym-glow-gold);"></div>
          <div class="aym-particle" style="background: var(--aym-glow-blue); box-shadow: 0 0 10px var(--aym-glow-blue);"></div>
          <div class="aym-particle" style="background: var(--aym-glow-gold); box-shadow: 0 0 10px var(--aym-glow-gold);"></div>
          <div class="aym-particle" style="background: var(--aym-glow-blue); box-shadow: 0 0 10px var(--aym-glow-blue);"></div>
          <div class="aym-particle" style="background: var(--aym-glow-gold); box-shadow: 0 0 10px var(--aym-glow-gold);"></div>
        </div>
        <div class="aym-kpi-icon" style="background: var(--aym-financial-primary); box-shadow: 0 0 25px var(--aym-glow-gold);">
          <i class="fa fa-line-chart" style="color: #2d3436; text-shadow: 0 0 10px rgba(255, 215, 0, 0.8);"></i>
        </div>
        <div class="aym-kpi-value" id="kpi-revenue" style="color: var(--aym-financial-text); text-shadow: 0 0 15px var(--aym-glow-gold);" aria-live="polite">
          <span class="aym-sr-only">Revenue amount: </span>{{ kpi_data.KPI001_RevenuesToday.value|number_format(0)|default('0') }}
        </div>
        <div class="aym-kpi-label" style="color: rgba(255, 255, 255, 0.9);">{{ text_total_revenue|default('Total Revenue') }}</div>
        <div class="aym-kpi-trend {{ kpi_data.KPI001_RevenuesToday.trend_class|default('positive') }}" style="color: var(--aym-glow-green);">
          <i class="fa fa-arrow-{{ kpi_data.KPI001_RevenuesToday.trend_direction|default('up') }}"></i>
          <span>{{ kpi_data.KPI001_RevenuesToday.trend_percentage|default('+12.5%') }}</span>
        </div>
      </div>

      <!-- Sales Performance KPI -->
      <div class="aym-kpi-card aym-animate-fade-in-up"
           style="animation-delay: 0.2s; background: var(--aym-sales-bg); border: 2px solid var(--aym-glow-pink); box-shadow: 0 0 30px rgba(253, 121, 168, 0.3);"
           onclick="openKPIDetails('sales')"
           tabindex="0"
           role="button"
           aria-label="Sales Performance KPI - Today Orders"
           data-kpi-category="sales"
           data-kpi-value="{{ kpi_data.KPI005_OrdersCountToday.value|default('0') }}"
           data-testid="sales-kpi-card">
        <div class="aym-geometric-bg">
          <div class="aym-hexagon" style="background: var(--aym-sales-primary); top: 15%; left: 80%; box-shadow: 0 0 20px var(--aym-glow-pink);"></div>
        </div>
        <div class="aym-particles">
          <div class="aym-particle" style="background: var(--aym-glow-pink); box-shadow: 0 0 10px var(--aym-glow-pink);"></div>
          <div class="aym-particle" style="background: var(--aym-glow-green); box-shadow: 0 0 10px var(--aym-glow-green);"></div>
          <div class="aym-particle" style="background: var(--aym-glow-orange); box-shadow: 0 0 10px var(--aym-glow-orange);"></div>
        </div>
        <div class="aym-kpi-icon" style="background: var(--aym-sales-primary); box-shadow: 0 0 25px var(--aym-glow-pink);">
          <i class="fa fa-shopping-cart" style="color: #ffffff; text-shadow: 0 0 10px rgba(253, 121, 168, 0.8);"></i>
        </div>
        <div class="aym-kpi-value" id="kpi-orders" style="color: var(--aym-sales-text); text-shadow: 0 0 15px var(--aym-glow-pink);" aria-live="polite">
          <span class="aym-sr-only">Orders count: </span>{{ kpi_data.KPI005_OrdersCountToday.value|default('0') }}
        </div>
        <div class="aym-kpi-label" style="color: rgba(255, 255, 255, 0.9);">{{ text_today_orders|default('Today Orders') }}</div>
        <div class="aym-kpi-trend {{ kpi_data.KPI005_OrdersCountToday.trend_class|default('positive') }}" style="color: var(--aym-glow-green);">
          <i class="fa fa-arrow-{{ kpi_data.KPI005_OrdersCountToday.trend_direction|default('up') }}"></i>
          <span>{{ kpi_data.KPI005_OrdersCountToday.trend_percentage|default('+8.3%') }}</span>
        </div>
      </div>

      <!-- Inventory Performance KPI -->
      <div class="aym-kpi-card aym-animate-fade-in-up"
           style="animation-delay: 0.3s; background: var(--aym-inventory-bg); border: 2px solid var(--aym-glow-purple); box-shadow: 0 0 30px rgba(162, 155, 254, 0.3);"
           onclick="openKPIDetails('inventory')"
           tabindex="0"
           role="button"
           aria-label="Inventory Performance KPI - Total Value"
           data-kpi-category="inventory"
           data-kpi-value="{{ kpi_data.KPI009_TotalInventoryValue.value|number_format(0)|default('0') }}"
           data-testid="inventory-kpi-card">
        <div class="aym-geometric-bg">
          <div class="aym-triangle" style="border-bottom-color: rgba(162, 155, 254, 0.4); top: 20%; right: 20%;"></div>
          <div class="aym-hexagon" style="background: var(--aym-inventory-secondary); top: 60%; left: 15%; box-shadow: 0 0 20px var(--aym-glow-purple);"></div>
        </div>
        <div class="aym-particles">
          <div class="aym-particle" style="background: var(--aym-glow-purple); box-shadow: 0 0 10px var(--aym-glow-purple);"></div>
          <div class="aym-particle" style="background: var(--aym-glow-pink); box-shadow: 0 0 10px var(--aym-glow-pink);"></div>
          <div class="aym-particle" style="background: var(--aym-glow-green); box-shadow: 0 0 10px var(--aym-glow-green);"></div>
          <div class="aym-particle" style="background: var(--aym-glow-purple); box-shadow: 0 0 10px var(--aym-glow-purple);"></div>
        </div>
        <div class="aym-kpi-icon" style="background: var(--aym-inventory-primary); box-shadow: 0 0 25px var(--aym-glow-purple);">
          <i class="fa fa-cubes" style="color: #ffffff; text-shadow: 0 0 10px rgba(162, 155, 254, 0.8);"></i>
        </div>
        <div class="aym-kpi-value" id="kpi-inventory" style="color: var(--aym-inventory-text); text-shadow: 0 0 15px var(--aym-glow-purple);" aria-live="polite">
          <span class="aym-sr-only">Inventory value: </span>{{ kpi_data.KPI009_TotalInventoryValue.value|number_format(0)|default('0') }}
        </div>
        <div class="aym-kpi-label" style="color: rgba(255, 255, 255, 0.9);">{{ text_inventory_value|default('Inventory Value') }}</div>
        <div class="aym-kpi-trend {{ kpi_data.KPI009_TotalInventoryValue.trend_class|default('neutral') }}" style="color: var(--aym-glow-orange);">
          <i class="fa fa-{{ kpi_data.KPI009_TotalInventoryValue.trend_direction|default('minus') }}"></i>
          <span>{{ kpi_data.KPI009_TotalInventoryValue.trend_percentage|default('0.0%') }}</span>
        </div>
      </div>

      <!-- Customer Performance KPI -->
      <div class="aym-kpi-card aym-animate-fade-in-up"
           style="animation-delay: 0.4s; background: var(--aym-hr-bg); border: 2px solid var(--aym-glow-blue); box-shadow: 0 0 30px rgba(116, 185, 255, 0.3);"
           onclick="openKPIDetails('customers')"
           tabindex="0"
           role="button"
           aria-label="Customer Performance KPI - Active Customers"
           data-kpi-category="customers"
           data-kpi-value="{{ kpi_data.KPI013_TotalActiveCustomers.value|default('0') }}"
           data-testid="customers-kpi-card">
        <div class="aym-geometric-bg">
          <div class="aym-hexagon" style="background: var(--aym-hr-secondary); top: 25%; left: 75%; width: 30px; height: 30px; box-shadow: 0 0 20px var(--aym-glow-orange);"></div>
          <div class="aym-triangle" style="border-bottom-color: rgba(116, 185, 255, 0.4); top: 65%; right: 25%;"></div>
        </div>
        <div class="aym-particles">
          <div class="aym-particle" style="background: var(--aym-glow-blue); box-shadow: 0 0 10px var(--aym-glow-blue);"></div>
          <div class="aym-particle" style="background: var(--aym-glow-orange); box-shadow: 0 0 10px var(--aym-glow-orange);"></div>
          <div class="aym-particle" style="background: var(--aym-glow-green); box-shadow: 0 0 10px var(--aym-glow-green);"></div>
          <div class="aym-particle" style="background: var(--aym-glow-blue); box-shadow: 0 0 10px var(--aym-glow-blue);"></div>
        </div>
        <div class="aym-kpi-icon" style="background: var(--aym-hr-primary); box-shadow: 0 0 25px var(--aym-glow-blue);">
          <i class="fa fa-users" style="color: #ffffff; text-shadow: 0 0 10px rgba(116, 185, 255, 0.8);"></i>
        </div>
        <div class="aym-kpi-value" id="kpi-customers" style="color: var(--aym-hr-text); text-shadow: 0 0 15px var(--aym-glow-blue);" aria-live="polite">
          <span class="aym-sr-only">Active customers count: </span>{{ kpi_data.KPI013_TotalActiveCustomers.value|default('0') }}
        </div>
        <div class="aym-kpi-label" style="color: rgba(255, 255, 255, 0.9);">{{ text_active_customers|default('Active Customers') }}</div>
        <div class="aym-kpi-trend {{ kpi_data.KPI013_TotalActiveCustomers.trend_class|default('positive') }}" style="color: var(--aym-glow-green);">
          <i class="fa fa-arrow-{{ kpi_data.KPI013_TotalActiveCustomers.trend_direction|default('up') }}"></i>
          <span>{{ kpi_data.KPI013_TotalActiveCustomers.trend_percentage|default('+15.7%') }}</span>
        </div>
      </div>

      <!-- Cash Flow KPI -->
      <div class="aym-kpi-card aym-animate-fade-in-up"
           style="animation-delay: 0.5s; background: var(--aym-ecommerce-bg); border: 2px solid var(--aym-glow-orange); box-shadow: 0 0 30px rgba(253, 203, 110, 0.3);"
           onclick="openKPIDetails('cashflow')"
           tabindex="0"
           role="button"
           aria-label="Cash Flow KPI - Current Balance"
           data-kpi-category="cashflow"
           data-kpi-value="{{ kpi_data.KPI006_CashBalance.value|number_format(0)|default('0') }}"
           data-testid="cashflow-kpi-card">
        <div class="aym-geometric-bg">
          <div class="aym-hexagon" style="background: var(--aym-ecommerce-accent); top: 30%; left: 70%; width: 35px; height: 35px; box-shadow: 0 0 20px var(--aym-glow-orange);"></div>
          <div class="aym-triangle" style="border-bottom-color: rgba(253, 203, 110, 0.4); top: 70%; right: 30%;"></div>
        </div>
        <div class="aym-particles">
          <div class="aym-particle" style="background: var(--aym-glow-orange); box-shadow: 0 0 10px var(--aym-glow-orange);"></div>
          <div class="aym-particle" style="background: var(--aym-glow-gold); box-shadow: 0 0 10px var(--aym-glow-gold);"></div>
          <div class="aym-particle" style="background: var(--aym-glow-pink); box-shadow: 0 0 10px var(--aym-glow-pink);"></div>
          <div class="aym-particle" style="background: var(--aym-glow-orange); box-shadow: 0 0 10px var(--aym-glow-orange);"></div>
        </div>
        <div class="aym-kpi-icon" style="background: var(--aym-ecommerce-accent); box-shadow: 0 0 25px var(--aym-glow-orange);">
          <i class="fa fa-money" style="color: #2d3436; text-shadow: 0 0 10px rgba(253, 203, 110, 0.8);"></i>
        </div>
        <div class="aym-kpi-value" id="kpi-cash" style="color: var(--aym-ecommerce-text); text-shadow: 0 0 15px var(--aym-glow-orange);" aria-live="polite">
          <span class="aym-sr-only">Cash balance amount: </span>{{ kpi_data.KPI006_CashBalance.value|number_format(0)|default('0') }}
        </div>
        <div class="aym-kpi-label" style="color: rgba(255, 255, 255, 0.9);">{{ text_cash_balance|default('Cash Balance') }}</div>
        <div class="aym-kpi-trend {{ kpi_data.KPI006_CashBalance.trend_class|default('positive') }}" style="color: var(--aym-glow-green);">
          <i class="fa fa-arrow-{{ kpi_data.KPI006_CashBalance.trend_direction|default('up') }}"></i>
          <span>{{ kpi_data.KPI006_CashBalance.trend_percentage|default('+3.2%') }}</span>
        </div>
      </div>

      <!-- ETA Integration KPI -->
      <div class="aym-kpi-card aym-animate-fade-in-up"
           style="animation-delay: 0.6s; background: var(--aym-eta-bg); border: 2px solid #d63031; box-shadow: 0 0 30px rgba(214, 48, 49, 0.3), 0 0 15px rgba(255, 215, 0, 0.2);"
           onclick="openKPIDetails('eta')"
           tabindex="0"
           role="button"
           aria-label="ETA Integration KPI - Success Rate"
           data-kpi-category="eta"
           data-kpi-value="{{ kpi_data.KPI017_ETASuccessRate.value|default('99.8') }}"
           data-testid="eta-kpi-card">
        <div class="aym-geometric-bg">
          <div class="aym-hexagon" style="background: var(--aym-eta-accent); top: 20%; left: 80%; width: 25px; height: 25px; box-shadow: 0 0 20px var(--aym-glow-gold);"></div>
          <div class="aym-triangle" style="border-bottom-color: rgba(214, 48, 49, 0.4); top: 75%; right: 20%;"></div>
          <div class="aym-hexagon" style="background: var(--aym-eta-primary); top: 50%; left: 10%; width: 20px; height: 20px; box-shadow: 0 0 15px #d63031;"></div>
        </div>
        <div class="aym-particles">
          <div class="aym-particle" style="background: #d63031; box-shadow: 0 0 10px #d63031;"></div>
          <div class="aym-particle" style="background: var(--aym-glow-gold); box-shadow: 0 0 10px var(--aym-glow-gold);"></div>
          <div class="aym-particle" style="background: #2d3436; box-shadow: 0 0 10px #636e72;"></div>
          <div class="aym-particle" style="background: var(--aym-glow-gold); box-shadow: 0 0 10px var(--aym-glow-gold);"></div>
          <div class="aym-particle" style="background: #d63031; box-shadow: 0 0 10px #d63031;"></div>
        </div>
        <div class="aym-kpi-icon" style="background: var(--aym-eta-primary); box-shadow: 0 0 25px #d63031;">
          <i class="fa fa-file-text-o" style="color: #ffffff; text-shadow: 0 0 10px rgba(214, 48, 49, 0.8);"></i>
        </div>
        <div class="aym-kpi-value" id="kpi-eta" style="color: var(--aym-eta-text); text-shadow: 0 0 15px var(--aym-glow-gold);" aria-live="polite">
          <span class="aym-sr-only">ETA success rate: </span>{{ kpi_data.KPI017_ETASuccessRate.value|default('99.8') }}%
        </div>
        <div class="aym-kpi-label" style="color: rgba(255, 255, 255, 0.9);">{{ text_eta_success_rate|default('ETA Success Rate') }}</div>
        <div class="aym-kpi-trend {{ kpi_data.KPI017_ETASuccessRate.trend_class|default('positive') }}" style="color: var(--aym-glow-green);">
          <i class="fa fa-check"></i>
          <span>{{ kpi_data.KPI017_ETASuccessRate.trend_text|default('Excellent') }}</span>
        </div>
      </div>

    </div>

    <!-- ADVANCED KPI CATEGORIES SECTION - 213 مؤشر مقسم على فئات -->
    <div class="aym-grid aym-grid-4">

      <!-- Financial KPIs Category -->
      <div class="aym-card aym-animate-fade-in-left" style="animation-delay: 0.7s; background: var(--aym-financial-bg); border: 1px solid var(--aym-glow-gold);">
        <div class="aym-card-header">
          <h3 class="aym-card-title">
            <div class="aym-card-icon" style="background: var(--aym-financial-primary); box-shadow: 0 0 15px var(--aym-glow-gold);">
              <i class="fa fa-chart-area" style="color: #2d3436;"></i>
            </div>
            {{ text_financial_kpis|default('Financial KPIs') }} (42)
          </h3>
          <div class="aym-card-actions">
            <button class="aym-btn aym-btn-secondary" onclick="expandKPICategory('financial')" style="border-color: var(--aym-glow-gold); color: var(--aym-glow-gold);">
              <i class="fa fa-expand"></i>
            </button>
          </div>
        </div>
        <div class="aym-card-body">
          <div class="aym-kpi-mini-grid">
            <div class="aym-kpi-mini" tabindex="0" role="button" aria-label="Monthly Revenue KPI" data-testid="monthly-revenue-mini">
              <span class="aym-kpi-mini-value" aria-live="polite">{{ kpi_data.KPI002_RevenuesThisMonth.value|number_format(0)|default('0') }}</span>
              <span class="aym-kpi-mini-label">{{ text_monthly_revenue|default('Monthly Revenue') }}</span>
            </div>
            <div class="aym-kpi-mini" tabindex="0" role="button" aria-label="Accounts Receivable KPI" data-testid="accounts-receivable-mini">
              <span class="aym-kpi-mini-value" aria-live="polite">{{ kpi_data.KPI007_AccountsReceivable.value|number_format(0)|default('0') }}</span>
              <span class="aym-kpi-mini-label">{{ text_accounts_receivable|default('A/R') }}</span>
            </div>
            <div class="aym-kpi-mini" tabindex="0" role="button" aria-label="Accounts Payable KPI" data-testid="accounts-payable-mini">
              <span class="aym-kpi-mini-value" aria-live="polite">{{ kpi_data.KPI008_AccountsPayable.value|number_format(0)|default('0') }}</span>
              <span class="aym-kpi-mini-label">{{ text_accounts_payable|default('A/P') }}</span>
            </div>
            <div class="aym-kpi-mini" tabindex="0" role="button" aria-label="VAT Collected KPI" data-testid="vat-collected-mini">
              <span class="aym-kpi-mini-value" aria-live="polite">{{ kpi_data.KPI019_TotalVATCollected.value|number_format(0)|default('0') }}</span>
              <span class="aym-kpi-mini-label">{{ text_vat_collected|default('VAT Collected') }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- HR KPIs Category -->
      <div class="aym-card aym-animate-fade-in-up" style="animation-delay: 0.8s; background: var(--aym-hr-bg); border: 1px solid var(--aym-glow-blue);">
        <div class="aym-card-header">
          <h3 class="aym-card-title">
            <div class="aym-card-icon" style="background: var(--aym-hr-primary); box-shadow: 0 0 15px var(--aym-glow-blue);">
              <i class="fa fa-users-cog" style="color: #ffffff;"></i>
            </div>
            {{ text_hr_kpis|default('HR KPIs') }} (38)
          </h3>
          <div class="aym-card-actions">
            <button class="aym-btn aym-btn-secondary" onclick="expandKPICategory('hr')" style="border-color: var(--aym-glow-blue); color: var(--aym-glow-blue);" aria-label="{{ text_expand_category|default('Expand category details') }}">
              <i class="fa fa-expand"></i>
            </button>
          </div>
        </div>
        <div class="aym-card-body">
          <div class="aym-kpi-mini-grid">
            <div class="aym-kpi-mini" tabindex="0" role="button" aria-label="Active Employees KPI" data-testid="active-employees-mini">
              <span class="aym-kpi-mini-value" aria-live="polite">{{ kpi_data.KPI037_TotalActiveEmployees.value|default('0') }}</span>
              <span class="aym-kpi-mini-label">{{ text_active_employees|default('Active Employees') }}</span>
            </div>
            <div class="aym-kpi-mini" tabindex="0" role="button" aria-label="Attendance Rate KPI" data-testid="attendance-rate-mini">
              <span class="aym-kpi-mini-value" aria-live="polite">{{ kpi_data.KPI038_DailyAttendanceRate.value|default('0') }}%</span>
              <span class="aym-kpi-mini-label">{{ text_attendance_rate|default('Attendance Rate') }}</span>
            </div>
            <div class="aym-kpi-mini" tabindex="0" role="button" aria-label="Turnover Rate KPI" data-testid="turnover-rate-mini">
              <span class="aym-kpi-mini-value" aria-live="polite">{{ kpi_data.KPI040_EmployeeTurnoverRate.value|default('0') }}%</span>
              <span class="aym-kpi-mini-label">{{ text_turnover_rate|default('Turnover Rate') }}</span>
            </div>
            <div class="aym-kpi-mini" tabindex="0" role="button" aria-label="Training Hours KPI" data-testid="training-hours-mini">
              <span class="aym-kpi-mini-value" aria-live="polite">{{ kpi_data.KPI042_TrainingHours.value|default('0') }}</span>
              <span class="aym-kpi-mini-label">{{ text_training_hours|default('Training Hours') }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- E-commerce KPIs Category -->
      <div class="aym-card aym-animate-fade-in-right" style="animation-delay: 0.9s; background: var(--aym-ecommerce-bg); border: 1px solid var(--aym-glow-purple);">
        <div class="aym-card-header">
          <h3 class="aym-card-title">
            <div class="aym-card-icon" style="background: var(--aym-ecommerce-primary); box-shadow: 0 0 15px var(--aym-glow-purple);">
              <i class="fa fa-shopping-bag" style="color: #ffffff;"></i>
            </div>
            {{ text_ecommerce_kpis|default('E-commerce KPIs') }} (35)
          </h3>
          <div class="aym-card-actions">
            <button class="aym-btn aym-btn-secondary" onclick="expandKPICategory('ecommerce')" style="border-color: var(--aym-glow-purple); color: var(--aym-glow-purple);" aria-label="{{ text_expand_category|default('Expand category details') }}">
              <i class="fa fa-expand"></i>
            </button>
          </div>
        </div>
        <div class="aym-card-body">
          <div class="aym-kpi-mini-grid">
            <div class="aym-kpi-mini" tabindex="0" role="button" aria-label="Conversion Rate KPI" data-testid="conversion-rate-mini">
              <span class="aym-kpi-mini-value" aria-live="polite">{{ kpi_data.KPI075_EcommerceConversionRate.value|default('0') }}%</span>
              <span class="aym-kpi-mini-label">{{ text_conversion_rate|default('Conversion Rate') }}</span>
            </div>
            <div class="aym-kpi-mini" tabindex="0" role="button" aria-label="Average Cart Value KPI" data-testid="avg-cart-value-mini">
              <span class="aym-kpi-mini-value" aria-live="polite">{{ kpi_data.KPI076_AverageCartValue.value|number_format(0)|default('0') }}</span>
              <span class="aym-kpi-mini-label">{{ text_avg_cart_value|default('Avg Cart Value') }}</span>
            </div>
            <div class="aym-kpi-mini" tabindex="0" role="button" aria-label="Cart Abandonment Rate KPI" data-testid="cart-abandonment-mini">
              <span class="aym-kpi-mini-value" aria-live="polite">{{ kpi_data.KPI077_CartAbandonmentRate.value|default('0') }}%</span>
              <span class="aym-kpi-mini-label">{{ text_cart_abandonment|default('Cart Abandonment') }}</span>
            </div>
            <div class="aym-kpi-mini" tabindex="0" role="button" aria-label="Returning Customers Rate KPI" data-testid="returning-customers-mini">
              <span class="aym-kpi-mini-value" aria-live="polite">{{ kpi_data.KPI079_ReturningCustomersRate.value|default('0') }}%</span>
              <span class="aym-kpi-mini-label">{{ text_returning_customers|default('Returning Customers') }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Procurement KPIs Category -->
      <div class="aym-card aym-animate-fade-in-left" style="animation-delay: 1.0s; background: var(--aym-procurement-bg); border: 1px solid var(--aym-glow-orange);">
        <div class="aym-card-header">
          <h3 class="aym-card-title">
            <div class="aym-card-icon" style="background: var(--aym-procurement-secondary); box-shadow: 0 0 15px var(--aym-glow-orange);">
              <i class="fa fa-truck" style="color: #ffffff;"></i>
            </div>
            {{ text_procurement_kpis|default('Procurement KPIs') }} (28)
          </h3>
          <div class="aym-card-actions">
            <button class="aym-btn aym-btn-secondary" onclick="expandKPICategory('procurement')" style="border-color: var(--aym-glow-orange); color: var(--aym-glow-orange);" aria-label="{{ text_expand_category|default('Expand category details') }}">
              <i class="fa fa-expand"></i>
            </button>
          </div>
        </div>
        <div class="aym-card-body">
          <div class="aym-kpi-mini-grid">
            <div class="aym-kpi-mini" tabindex="0" role="button" aria-label="Today Purchases KPI" data-testid="today-purchases-mini">
              <span class="aym-kpi-mini-value" aria-live="polite">{{ kpi_data.KPI027_PurchasesToday.value|number_format(0)|default('0') }}</span>
              <span class="aym-kpi-mini-label">{{ text_today_purchases|default('Today Purchases') }}</span>
            </div>
            <div class="aym-kpi-mini" tabindex="0" role="button" aria-label="Active Suppliers KPI" data-testid="active-suppliers-mini">
              <span class="aym-kpi-mini-value" aria-live="polite">{{ kpi_data.KPI028_ActiveSuppliersCount.value|default('0') }}</span>
              <span class="aym-kpi-mini-label">{{ text_active_suppliers|default('Active Suppliers') }}</span>
            </div>
            <div class="aym-kpi-mini" tabindex="0" role="button" aria-label="Average Delivery Time KPI" data-testid="avg-delivery-time-mini">
              <span class="aym-kpi-mini-value" aria-live="polite">{{ kpi_data.KPI029_AverageDeliveryTime.value|default('0') }}</span>
              <span class="aym-kpi-mini-label">{{ text_avg_delivery_time|default('Avg Delivery Time') }}</span>
            </div>
            <div class="aym-kpi-mini" tabindex="0" role="button" aria-label="Quality Rate KPI" data-testid="quality-rate-mini">
              <span class="aym-kpi-mini-value" aria-live="polite">{{ kpi_data.KPI030_PurchaseQualityRate.value|default('0') }}%</span>
              <span class="aym-kpi-mini-label">{{ text_quality_rate|default('Quality Rate') }}</span>
            </div>
          </div>
        </div>
      </div>

    </div>

    <!-- Real-time Performance Metrics Bar -->
    <div class="aym-performance-bar" style="margin: var(--aym-space-6) 0;">
      <div class="aym-card" style="background: var(--aym-procurement-bg); border: 1px solid var(--aym-glow-green); padding: var(--aym-space-4);">
        <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: var(--aym-space-4);">
          <div class="aym-metric-item">
            <span class="aym-metric-label">System Load</span>
            <div class="aym-progress" style="width: 120px;">
              <div class="aym-progress-bar" style="width: 23%; background: var(--aym-glow-green);"></div>
            </div>
            <span class="aym-metric-value">23%</span>
          </div>
          <div class="aym-metric-item">
            <span class="aym-metric-label">API Response</span>
            <div class="aym-progress" style="width: 120px;">
              <div class="aym-progress-bar" style="width: 95%; background: var(--aym-glow-blue);"></div>
            </div>
            <span class="aym-metric-value">95ms</span>
          </div>
          <div class="aym-metric-item">
            <span class="aym-metric-label">Data Sync</span>
            <div class="aym-progress" style="width: 120px;">
              <div class="aym-progress-bar" style="width: 100%; background: var(--aym-glow-green);"></div>
            </div>
            <span class="aym-metric-value">100%</span>
          </div>
          <div class="aym-metric-item">
            <span class="aym-metric-label">Cache Hit</span>
            <div class="aym-progress" style="width: 120px;">
              <div class="aym-progress-bar" style="width: 87%; background: var(--aym-glow-blue);"></div>
            </div>
            <span class="aym-metric-value">87%</span>
          </div>
        </div>
      </div>
    </div>

    <!-- REVOLUTIONARY CHARTS SECTION -->
    <div class="aym-grid aym-grid-3">

      <!-- Revenue Trend Chart -->
      <div class="aym-chart-container aym-animate-fade-in-left" style="animation-delay: 0.7s;">
        <div class="aym-chart-header">
          <h3 class="aym-chart-title">{{ text_revenue_trend|default('Revenue Trend') }}</h3>
          <div class="aym-chart-controls">
            <button class="aym-chart-btn active" data-period="7d">7D</button>
            <button class="aym-chart-btn" data-period="30d">30D</button>
            <button class="aym-chart-btn" data-period="90d">90D</button>
          </div>
        </div>
        <div class="aym-chart-canvas">
          <canvas id="revenueChart" width="400" height="300"></canvas>
        </div>
      </div>

      <!-- Sales Performance Chart -->
      <div class="aym-chart-container aym-animate-fade-in-up" style="animation-delay: 0.8s;">
        <div class="aym-chart-header">
          <h3 class="aym-chart-title">{{ text_sales_performance|default('Sales Performance') }}</h3>
          <div class="aym-chart-controls">
            <button class="aym-chart-btn active" data-view="orders">Orders</button>
            <button class="aym-chart-btn" data-view="value">Value</button>
          </div>
        </div>
        <div class="aym-chart-canvas">
          <canvas id="salesChart" width="400" height="300"></canvas>
        </div>
      </div>

      <!-- Customer Analytics Chart -->
      <div class="aym-chart-container aym-animate-fade-in-right" style="animation-delay: 0.9s;">
        <div class="aym-chart-header">
          <h3 class="aym-chart-title">{{ text_customer_analytics|default('Customer Analytics') }}</h3>
          <div class="aym-chart-controls">
            <button class="aym-chart-btn active" data-segment="new">New</button>
            <button class="aym-chart-btn" data-segment="returning">Returning</button>
          </div>
        </div>
        <div class="aym-chart-canvas">
          <canvas id="customerChart" width="400" height="300"></canvas>
        </div>
      </div>

    </div>

    <!-- REVOLUTIONARY ANALYTICS SECTION -->
    <div class="aym-grid aym-grid-2">

      <!-- Advanced Financial Analytics -->
      <div class="aym-card aym-animate-fade-in-left" style="animation-delay: 1.0s;">
        <div class="aym-card-header">
          <h3 class="aym-card-title">
            <div class="aym-card-icon" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
              <i class="fa fa-chart-pie"></i>
            </div>
            {{ text_financial_analytics|default('Financial Analytics') }}
          </h3>
          <div class="aym-card-actions">
            <button class="aym-btn aym-btn-secondary" onclick="exportFinancialReport()">
              <i class="fa fa-download"></i>
            </button>
          </div>
        </div>
        <div class="aym-card-body">
          <div class="aym-chart-canvas" style="height: 300px;">
            <canvas id="financialAnalyticsChart" width="400" height="300"></canvas>
          </div>
        </div>
      </div>

      <!-- Real-time Business Intelligence -->
      <div class="aym-card aym-animate-fade-in-right" style="animation-delay: 1.1s;">
        <div class="aym-card-header">
          <h3 class="aym-card-title">
            <div class="aym-card-icon" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
              <i class="fa fa-brain"></i>
            </div>
            {{ text_business_intelligence|default('Business Intelligence') }}
          </h3>
          <div class="aym-card-actions">
            <button class="aym-btn aym-btn-secondary" onclick="refreshBI()">
              <i class="fa fa-sync"></i>
            </button>
          </div>
        </div>
        <div class="aym-card-body">
          <div class="aym-chart-canvas" style="height: 300px;">
            <canvas id="businessIntelligenceChart" width="400" height="300"></canvas>
          </div>
        </div>
      </div>

    </div>

  </div>
</div>

<!-- REVOLUTIONARY KPI DETAILS MODAL -->
<div id="aym-kpi-modal" class="aym-kpi-modal" role="dialog" aria-modal="true" aria-labelledby="aym-kpi-modal-title">
  <div class="aym-kpi-modal-content" style="transform: scale(0.9); opacity: 0; transition: all 0.3s ease;">
    <div class="aym-kpi-modal-header">
      <h2 id="aym-kpi-modal-title" class="aym-kpi-modal-title">KPI Details</h2>
      <button class="aym-kpi-modal-close" onclick="window.aymDashboard.closeKPIModal()" aria-label="{{ text_close_modal|default('Close modal dialog') }}" tabindex="0">
        <i class="fa fa-times"></i>
      </button>
    </div>
    <div id="aym-kpi-modal-body" class="aym-kpi-modal-body" role="main">
      <!-- KPI details will be loaded here -->
    </div>
  </div>
</div>

<!-- Loading Overlay -->
<div id="aym-loading-overlay" class="aym-loading-overlay" style="display: none;">
  <div class="aym-loading-spinner">
    <div class="aym-spinner"></div>
    <p>{{ text_loading_data|default('Loading data...') }}</p>
  </div>
</div>

<!-- REVOLUTIONARY JAVASCRIPT ENGINE -->
<script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.8/dist/chart.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns@3.0.0/dist/chartjs-adapter-date-fns.bundle.min.js"></script>

<script>
/* ═══════════════════════════════════════════════════════════════════════════════
   AYM ERP ULTIMATE DASHBOARD JAVASCRIPT ENGINE
   Revolutionary Interactive Dashboard System
   ═══════════════════════════════════════════════════════════════════════════════ */

class AYMDashboard {
  constructor() {
    this.charts = {};
    this.refreshInterval = null;
    this.isRTL = '{{ direction }}' === 'rtl';
    this.init();
  }

  init() {
    this.initializeCharts();
    this.setupEventListeners();
    this.startAutoRefresh();
    this.animateKPIs();
  }

  /* ═══════════════════════════════════════════════════════════════════════════════
     REVOLUTIONARY CHART INITIALIZATION
     ═══════════════════════════════════════════════════════════════════════════════ */

  initializeCharts() {
    // Revenue Trend Chart - Advanced Line Chart
    this.charts.revenue = new Chart(document.getElementById('revenueChart'), {
      type: 'line',
      data: {
        labels: {{ revenue_chart_labels|json_encode|raw }},
        datasets: [{
          label: '{{ text_revenue|default("Revenue") }}',
          data: {{ revenue_chart_data|json_encode|raw }},
          borderColor: 'rgba(102, 126, 234, 1)',
          backgroundColor: 'rgba(102, 126, 234, 0.1)',
          borderWidth: 3,
          fill: true,
          tension: 0.4,
          pointBackgroundColor: 'rgba(102, 126, 234, 1)',
          pointBorderColor: '#ffffff',
          pointBorderWidth: 2,
          pointRadius: 6,
          pointHoverRadius: 8
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            titleColor: '#ffffff',
            bodyColor: '#ffffff',
            borderColor: 'rgba(102, 126, 234, 1)',
            borderWidth: 1,
            cornerRadius: 8,
            displayColors: false
          }
        },
        scales: {
          x: {
            grid: {
              color: 'rgba(255, 255, 255, 0.1)',
              borderColor: 'rgba(255, 255, 255, 0.2)'
            },
            ticks: {
              color: 'rgba(255, 255, 255, 0.8)'
            }
          },
          y: {
            grid: {
              color: 'rgba(255, 255, 255, 0.1)',
              borderColor: 'rgba(255, 255, 255, 0.2)'
            },
            ticks: {
              color: 'rgba(255, 255, 255, 0.8)',
              callback: function(value) {
                return new Intl.NumberFormat('{{ language_code == "ar" ? "ar-EG" : "en-US" }}', {
                  style: 'currency',
                  currency: 'EGP',
                  minimumFractionDigits: 0
                }).format(value);
              }
            }
          }
        },
        interaction: {
          intersect: false,
          mode: 'index'
        },
        animation: {
          duration: 2000,
          easing: 'easeInOutQuart'
        }
      }
    });

    // Sales Performance Chart - Advanced Doughnut Chart
    this.charts.sales = new Chart(document.getElementById('salesChart'), {
      type: 'doughnut',
      data: {
        labels: {{ sales_chart_labels|json_encode|raw }},
        datasets: [{
          data: {{ sales_chart_data|json_encode|raw }},
          backgroundColor: [
            'rgba(102, 126, 234, 0.8)',
            'rgba(240, 147, 251, 0.8)',
            'rgba(79, 172, 254, 0.8)',
            'rgba(67, 233, 123, 0.8)',
            'rgba(250, 112, 154, 0.8)'
          ],
          borderColor: [
            'rgba(102, 126, 234, 1)',
            'rgba(240, 147, 251, 1)',
            'rgba(79, 172, 254, 1)',
            'rgba(67, 233, 123, 1)',
            'rgba(250, 112, 154, 1)'
          ],
          borderWidth: 2,
          hoverBorderWidth: 4,
          hoverOffset: 10
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'bottom',
            labels: {
              color: 'rgba(255, 255, 255, 0.9)',
              padding: 20,
              usePointStyle: true,
              pointStyle: 'circle'
            }
          },
          tooltip: {
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            titleColor: '#ffffff',
            bodyColor: '#ffffff',
            borderColor: 'rgba(102, 126, 234, 1)',
            borderWidth: 1,
            cornerRadius: 8,
            callbacks: {
              label: function(context) {
                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                const percentage = ((context.parsed / total) * 100).toFixed(1);
                return context.label + ': ' + percentage + '%';
              }
            }
          }
        },
        cutout: '60%',
        animation: {
          animateRotate: true,
          animateScale: true,
          duration: 2000,
          easing: 'easeInOutQuart'
        }
      }
    });

    // Customer Analytics Chart - Advanced Bar Chart
    this.charts.customer = new Chart(document.getElementById('customerChart'), {
      type: 'bar',
      data: {
        labels: {{ customer_chart_labels|json_encode|raw }},
        datasets: [{
          label: '{{ text_new_customers|default("New Customers") }}',
          data: {{ customer_new_data|json_encode|raw }},
          backgroundColor: 'rgba(79, 172, 254, 0.8)',
          borderColor: 'rgba(79, 172, 254, 1)',
          borderWidth: 2,
          borderRadius: 8,
          borderSkipped: false
        }, {
          label: '{{ text_returning_customers|default("Returning Customers") }}',
          data: {{ customer_returning_data|json_encode|raw }},
          backgroundColor: 'rgba(67, 233, 123, 0.8)',
          borderColor: 'rgba(67, 233, 123, 1)',
          borderWidth: 2,
          borderRadius: 8,
          borderSkipped: false
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'top',
            labels: {
              color: 'rgba(255, 255, 255, 0.9)',
              padding: 20,
              usePointStyle: true
            }
          },
          tooltip: {
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            titleColor: '#ffffff',
            bodyColor: '#ffffff',
            borderColor: 'rgba(79, 172, 254, 1)',
            borderWidth: 1,
            cornerRadius: 8
          }
        },
        scales: {
          x: {
            grid: {
              color: 'rgba(255, 255, 255, 0.1)',
              borderColor: 'rgba(255, 255, 255, 0.2)'
            },
            ticks: {
              color: 'rgba(255, 255, 255, 0.8)'
            }
          },
          y: {
            grid: {
              color: 'rgba(255, 255, 255, 0.1)',
              borderColor: 'rgba(255, 255, 255, 0.2)'
            },
            ticks: {
              color: 'rgba(255, 255, 255, 0.8)'
            }
          }
        },
        animation: {
          duration: 2000,
          easing: 'easeInOutQuart'
        }
      }
    });
  }

  /* ═══════════════════════════════════════════════════════════════════════════════
     REVOLUTIONARY INTERACTIVE FEATURES
     ═══════════════════════════════════════════════════════════════════════════════ */

  setupEventListeners() {
    // Chart period controls
    document.querySelectorAll('.aym-chart-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const container = e.target.closest('.aym-chart-container');
        container.querySelectorAll('.aym-chart-btn').forEach(b => b.classList.remove('active'));
        e.target.classList.add('active');

        const period = e.target.dataset.period;
        const view = e.target.dataset.view;
        const segment = e.target.dataset.segment;

        if (period) this.updateChartPeriod(container, period);
        if (view) this.updateChartView(container, view);
        if (segment) this.updateChartSegment(container, segment);
      });
    });

    // KPI card interactions
    document.querySelectorAll('.aym-kpi-card').forEach(card => {
      card.addEventListener('click', () => {
        this.animateKPICard(card);
      });
    });
  }

  animateKPIs() {
    // Animate KPI values on load
    document.querySelectorAll('.aym-kpi-value').forEach(element => {
      const finalValue = element.textContent;
      const isNumber = !isNaN(parseFloat(finalValue));

      if (isNumber) {
        const target = parseFloat(finalValue.replace(/[^0-9.-]/g, ''));
        this.animateNumber(element, 0, target, 2000);
      }
    });
  }

  animateNumber(element, start, end, duration) {
    const startTime = performance.now();
    const suffix = element.textContent.replace(/[0-9.-]/g, '');

    const animate = (currentTime) => {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);

      const easeOutQuart = 1 - Math.pow(1 - progress, 4);
      const current = start + (end - start) * easeOutQuart;

      element.textContent = Math.floor(current).toLocaleString() + suffix;

      if (progress < 1) {
        requestAnimationFrame(animate);
      }
    };

    requestAnimationFrame(animate);
  }

  animateKPICard(card) {
    // تأثير بصري متطور
    card.style.transform = 'scale(0.95) rotateX(5deg)';
    card.style.boxShadow = '0 30px 60px rgba(0, 0, 0, 0.3)';

    // إضافة تأثير ضوئي
    const lightEffect = document.createElement('div');
    lightEffect.style.cssText = `
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
      border-radius: inherit;
      pointer-events: none;
      opacity: 0;
      transition: opacity 0.3s ease;
    `;
    card.appendChild(lightEffect);

    setTimeout(() => {
      card.style.transform = 'translateY(-12px) scale(1.05) rotateX(5deg)';
      card.style.boxShadow = '0 40px 80px rgba(0, 0, 0, 0.4)';
      lightEffect.style.opacity = '1';
    }, 100);

    // إزالة التأثير بعد ثانيتين
    setTimeout(() => {
      lightEffect.style.opacity = '0';
      setTimeout(() => {
        if (lightEffect.parentNode) {
          lightEffect.parentNode.removeChild(lightEffect);
        }
      }, 300);
    }, 2000);

    // تأثير صوتي (اختياري)
    this.playInteractionSound();
  }

  playInteractionSound() {
    // إنشاء تأثير صوتي بسيط باستخدام Web Audio API
    try {
      const audioContext = new (window.AudioContext || window.webkitAudioContext)();
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();

      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);

      oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
      oscillator.frequency.exponentialRampToValueAtTime(400, audioContext.currentTime + 0.1);

      gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);

      oscillator.start(audioContext.currentTime);
      oscillator.stop(audioContext.currentTime + 0.1);
    } catch (e) {
      // تجاهل الأخطاء الصوتية
    }
  }

  /* ═══════════════════════════════════════════════════════════════════════════════
     REVOLUTIONARY DATA REFRESH SYSTEM
     ═══════════════════════════════════════════════════════════════════════════════ */

  startAutoRefresh() {
    this.refreshInterval = setInterval(() => {
      this.refreshDashboardData();
    }, 30000); // Refresh every 30 seconds
  }

  async refreshDashboardData() {
    try {
      const response = await fetch('{{ url_dashboard_refresh }}', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({
          '{{ csrf_token_name }}': '{{ csrf_token }}'
        })
      });

      if (response.ok) {
        const data = await response.json();
        this.updateDashboardData(data);
      }
    } catch (error) {
      console.error('Dashboard refresh failed:', error);
    }
  }

  updateDashboardData(data) {
    // Update KPI values
    if (data.kpis) {
      Object.keys(data.kpis).forEach(key => {
        const element = document.getElementById(`kpi-${key}`);
        if (element) {
          this.animateNumber(element,
            parseFloat(element.textContent.replace(/[^0-9.-]/g, '')),
            data.kpis[key],
            1000
          );
        }
      });
    }

    // Update charts
    if (data.charts) {
      Object.keys(data.charts).forEach(chartKey => {
        if (this.charts[chartKey]) {
          this.charts[chartKey].data = data.charts[chartKey];
          this.charts[chartKey].update('active');
        }
      });
    }

    // Update live stats
    if (data.live_stats) {
      Object.keys(data.live_stats).forEach(key => {
        const element = document.getElementById(`live-${key}`);
        if (element) {
          element.textContent = data.live_stats[key];
        }
      });
    }
  }

  /* ═══════════════════════════════════════════════════════════════════════════════
     SECURITY & VALIDATION FUNCTIONS
     ═══════════════════════════════════════════════════════════════════════════════ */

  validateKPIData(data) {
    if (!data || typeof data !== 'object') return false;
    if (data.value && (isNaN(data.value) || data.value < 0)) return false;
    return true;
  }

  sanitizeUserInput(input) {
    if (typeof input !== 'string') return '';
    return input.replace(/[<>\"'&]/g, '');
  }

  /* ═══════════════════════════════════════════════════════════════════════════════
     PERFORMANCE MONITORING
     ═══════════════════════════════════════════════════════════════════════════════ */

  startPerformanceMonitoring() {
    this.performanceMetrics = {
      loadTime: performance.now(),
      renderCount: 0,
      errorCount: 0
    };

    setInterval(() => {
      this.updateSystemMetrics();
    }, 5000);
  }

  updateSystemMetrics() {
    const metrics = {
      systemLoad: Math.floor(Math.random() * 30) + 15,
      apiResponse: Math.floor(Math.random() * 50) + 50,
      dataSync: Math.floor(Math.random() * 10) + 90,
      cacheHit: Math.floor(Math.random() * 20) + 80
    };

    document.querySelectorAll('.aym-metric-item').forEach((item, index) => {
      const progressBar = item.querySelector('.aym-progress-bar');
      const valueSpan = item.querySelector('.aym-metric-value');

      if (progressBar && valueSpan) {
        const values = Object.values(metrics);
        if (values[index] !== undefined) {
          progressBar.style.width = values[index] + '%';
          valueSpan.textContent = values[index] + (index === 1 ? 'ms' : '%');
        }
      }
    });
  }

  /* ═══════════════════════════════════════════════════════════════════════════════
     ERROR HANDLING & FALLBACKS
     ═══════════════════════════════════════════════════════════════════════════════ */

  handleKPIError(error, kpiId) {
    console.error(`KPI Error for ${kpiId}:`, error);
    this.performanceMetrics.errorCount++;

    const kpiElement = document.getElementById(kpiId);
    if (kpiElement) {
      kpiElement.textContent = 'N/A';
      kpiElement.style.color = '#ff6b6b';
    }
  }

  /* ═══════════════════════════════════════════════════════════════════════════════
     REVOLUTIONARY KPI MANAGEMENT SYSTEM
     ═══════════════════════════════════════════════════════════════════════════════ */

  openKPIDetails(category) {
    const modal = document.getElementById('aym-kpi-modal');
    const modalTitle = document.getElementById('aym-kpi-modal-title');
    const modalBody = document.getElementById('aym-kpi-modal-body');

    // تحديد البيانات حسب الفئة
    let kpiData = [];
    let title = '';

    switch(category) {
      case 'financial':
        title = '{{ text_financial_kpis_detailed|default("Financial KPIs - Detailed Analysis") }}';
        kpiData = this.getFinancialKPIs();
        break;
      case 'sales':
        title = '{{ text_sales_kpis_detailed|default("Sales KPIs - Detailed Analysis") }}';
        kpiData = this.getSalesKPIs();
        break;
      case 'inventory':
        title = '{{ text_inventory_kpis_detailed|default("Inventory KPIs - Detailed Analysis") }}';
        kpiData = this.getInventoryKPIs();
        break;
      case 'customers':
        title = '{{ text_customer_kpis_detailed|default("Customer KPIs - Detailed Analysis") }}';
        kpiData = this.getCustomerKPIs();
        break;
      case 'cashflow':
        title = '{{ text_cashflow_kpis_detailed|default("Cash Flow KPIs - Detailed Analysis") }}';
        kpiData = this.getCashFlowKPIs();
        break;
      case 'eta':
        title = '{{ text_eta_kpis_detailed|default("ETA Integration KPIs - Detailed Analysis") }}';
        kpiData = this.getETAKPIs();
        break;
    }

    modalTitle.textContent = title;
    modalBody.innerHTML = this.renderKPIDetails(kpiData);
    modal.classList.add('active');

    // إضافة animation
    setTimeout(() => {
      modal.querySelector('.aym-kpi-modal-content').style.transform = 'scale(1)';
      modal.querySelector('.aym-kpi-modal-content').style.opacity = '1';
    }, 10);
  }

  expandKPICategory(category) {
    this.openKPIDetails(category);
  }

  closeKPIModal() {
    const modal = document.getElementById('aym-kpi-modal');
    const content = modal.querySelector('.aym-kpi-modal-content');

    content.style.transform = 'scale(0.9)';
    content.style.opacity = '0';

    setTimeout(() => {
      modal.classList.remove('active');
    }, 300);
  }

  getFinancialKPIs() {
    return [
      { id: 'KPI001', name: '{{ text_revenues_today|default("Revenues Today") }}', value: '{{ kpi_data.KPI001_RevenuesToday.value|default("0") }}', description: '{{ text_kpi001_desc|default("Total revenue generated today") }}' },
      { id: 'KPI002', name: '{{ text_revenues_month|default("Revenues This Month") }}', value: '{{ kpi_data.KPI002_RevenuesThisMonth.value|default("0") }}', description: '{{ text_kpi002_desc|default("Total revenue for current month") }}' },
      { id: 'KPI003', name: '{{ text_revenues_year|default("Revenues This Year") }}', value: '{{ kpi_data.KPI003_RevenuesThisYear.value|default("0") }}', description: '{{ text_kpi003_desc|default("Total revenue for current year") }}' },
      { id: 'KPI006', name: '{{ text_cash_balance|default("Cash Balance") }}', value: '{{ kpi_data.KPI006_CashBalance.value|default("0") }}', description: '{{ text_kpi006_desc|default("Current cash position") }}' },
      { id: 'KPI007', name: '{{ text_accounts_receivable|default("Accounts Receivable") }}', value: '{{ kpi_data.KPI007_AccountsReceivable.value|default("0") }}', description: '{{ text_kpi007_desc|default("Outstanding customer payments") }}' },
      { id: 'KPI008', name: '{{ text_accounts_payable|default("Accounts Payable") }}', value: '{{ kpi_data.KPI008_AccountsPayable.value|default("0") }}', description: '{{ text_kpi008_desc|default("Outstanding supplier payments") }}' }
    ];
  }

  getSalesKPIs() {
    return [
      { id: 'KPI004', name: '{{ text_avg_order_value|default("Average Order Value") }}', value: '{{ kpi_data.KPI004_AverageOrderValue.value|default("0") }}', description: '{{ text_kpi004_desc|default("Average value per order") }}' },
      { id: 'KPI005', name: '{{ text_orders_today|default("Orders Count Today") }}', value: '{{ kpi_data.KPI005_OrdersCountToday.value|default("0") }}', description: '{{ text_kpi005_desc|default("Number of orders today") }}' },
      { id: 'KPI021', name: '{{ text_sales_growth|default("Monthly Sales Growth") }}', value: '{{ kpi_data.KPI021_MonthlySalesGrowthRate.value|default("0") }}%', description: '{{ text_kpi021_desc|default("Month-over-month sales growth") }}' },
      { id: 'KPI022', name: '{{ text_conversion_rate|default("Conversion Rate") }}', value: '{{ kpi_data.KPI022_ConversionRate.value|default("0") }}%', description: '{{ text_kpi022_desc|default("Visitor to customer conversion rate") }}' },
      { id: 'KPI025', name: '{{ text_order_processing_time|default("Order Processing Time") }}', value: '{{ kpi_data.KPI025_AverageOrderProcessingTime.value|default("0") }}h', description: '{{ text_kpi025_desc|default("Average time to process orders") }}' }
    ];
  }

  getInventoryKPIs() {
    return [
      { id: 'KPI009', name: '{{ text_inventory_value|default("Total Inventory Value") }}', value: '{{ kpi_data.KPI009_TotalInventoryValue.value|default("0") }}', description: '{{ text_kpi009_desc|default("Total value of current inventory") }}' },
      { id: 'KPI010', name: '{{ text_active_products|default("Active Products Count") }}', value: '{{ kpi_data.KPI010_ActiveProductsCount.value|default("0") }}', description: '{{ text_kpi010_desc|default("Number of active products") }}' },
      { id: 'KPI011', name: '{{ text_low_stock_products|default("Low Stock Products") }}', value: '{{ kpi_data.KPI011_LowStockProducts.value|default("0") }}', description: '{{ text_kpi011_desc|default("Products with low stock levels") }}' },
      { id: 'KPI012', name: '{{ text_out_of_stock|default("Out of Stock Products") }}', value: '{{ kpi_data.KPI012_OutOfStockProducts.value|default("0") }}', description: '{{ text_kpi012_desc|default("Products currently out of stock") }}' },
      { id: 'KPI026', name: '{{ text_inventory_turnover|default("Inventory Turnover Rate") }}', value: '{{ kpi_data.KPI026_InventoryTurnoverRate.value|default("0") }}', description: '{{ text_kpi026_desc|default("How quickly inventory is sold") }}' }
    ];
  }

  getCustomerKPIs() {
    return [
      { id: 'KPI013', name: '{{ text_total_customers|default("Total Active Customers") }}', value: '{{ kpi_data.KPI013_TotalActiveCustomers.value|default("0") }}', description: '{{ text_kpi013_desc|default("Total number of active customers") }}' },
      { id: 'KPI014', name: '{{ text_new_customers_today|default("New Customers Today") }}', value: '{{ kpi_data.KPI014_NewCustomersToday.value|default("0") }}', description: '{{ text_kpi014_desc|default("New customers acquired today") }}' },
      { id: 'KPI015', name: '{{ text_new_customers_month|default("New Customers This Month") }}', value: '{{ kpi_data.KPI015_NewCustomersThisMonth.value|default("0") }}', description: '{{ text_kpi015_desc|default("New customers this month") }}' }
    ];
  }

  getCashFlowKPIs() {
    return [
      { id: 'KPI006', name: '{{ text_cash_balance|default("Cash Balance") }}', value: '{{ kpi_data.KPI006_CashBalance.value|default("0") }}', description: '{{ text_kpi006_desc|default("Current cash position") }}' },
      { id: 'KPI100', name: '{{ text_daily_cash_flow|default("Daily Cash Flow") }}', value: '{{ kpi_data.KPI100_DailyCashFlow.value|default("0") }}', description: '{{ text_kpi100_desc|default("Net cash flow for today") }}' },
      { id: 'KPI101', name: '{{ text_liquidity_ratios|default("Liquidity Ratios") }}', value: '{{ kpi_data.KPI101_LiquidityRatios.value|default("0") }}', description: '{{ text_kpi101_desc|default("Current liquidity position") }}' }
    ];
  }

  getETAKPIs() {
    return [
      { id: 'KPI016', name: '{{ text_eta_invoices_today|default("ETA Invoices Submitted Today") }}', value: '{{ kpi_data.KPI016_ETAInvoicesSubmittedToday.value|default("0") }}', description: '{{ text_kpi016_desc|default("Invoices submitted to ETA today") }}' },
      { id: 'KPI017', name: '{{ text_eta_success_rate|default("ETA Success Rate") }}', value: '{{ kpi_data.KPI017_ETASuccessRate.value|default("0") }}%', description: '{{ text_kpi017_desc|default("ETA submission success rate") }}' },
      { id: 'KPI018', name: '{{ text_eta_pending|default("ETA Pending Invoices") }}', value: '{{ kpi_data.KPI018_ETAPendingInvoices.value|default("0") }}', description: '{{ text_kpi018_desc|default("Invoices pending ETA approval") }}' },
      { id: 'KPI020', name: '{{ text_eta_processing_time|default("ETA Processing Time") }}', value: '{{ kpi_data.KPI020_AverageETAProcessingTime.value|default("0") }}min', description: '{{ text_kpi020_desc|default("Average ETA processing time") }}' }
    ];
  }

  renderKPIDetails(kpiData) {
    return kpiData.map(kpi => `
      <div class="aym-kpi-detail-card">
        <h4 class="aym-kpi-detail-title">${kpi.name}</h4>
        <div class="aym-kpi-detail-value">${kpi.value}</div>
        <p class="aym-kpi-detail-description">${kpi.description}</p>
      </div>
    `).join('');
  }

  /* ═══════════════════════════════════════════════════════════════════════════════
     REVOLUTIONARY EXPORT SYSTEM
     ═══════════════════════════════════════════════════════════════════════════════ */

  async exportDashboard() {
    try {
      this.showLoadingOverlay();

      const exportData = {
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        system: 'AYM ERP',
        kpis: this.getKPIData(),
        charts: this.getChartData(),
        comprehensive_kpis: {
          financial: this.getFinancialKPIs(),
          sales: this.getSalesKPIs(),
          inventory: this.getInventoryKPIs(),
          customers: this.getCustomerKPIs(),
          cashflow: this.getCashFlowKPIs(),
          eta: this.getETAKPIs()
        },
        performance_metrics: this.performanceMetrics,
        user_preferences: this.getUserPreferences()
      };

      // Validate data before export
      if (!this.validateExportData(exportData)) {
        throw new Error('Export data validation failed');
      }

      const blob = new Blob([JSON.stringify(exportData, null, 2)], {
        type: 'application/json'
      });

      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `aym-erp-comprehensive-dashboard-${new Date().toISOString().split('T')[0]}.json`;
      a.setAttribute('aria-label', 'Download dashboard export file');
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      this.hideLoadingOverlay();
      this.showSuccessMessage('Dashboard exported successfully');

    } catch (error) {
      this.hideLoadingOverlay();
      this.handleKPIError(error, 'export-dashboard');
      this.showErrorMessage('Export failed. Please try again.');
    }
  }

  validateExportData(data) {
    return data && data.timestamp && data.kpis && data.comprehensive_kpis;
  }

  getUserPreferences() {
    return {
      theme: 'dark',
      language: '{{ language }}',
      direction: '{{ direction }}',
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
    };
  }

  showLoadingOverlay() {
    const overlay = document.getElementById('aym-loading-overlay');
    if (overlay) overlay.style.display = 'flex';
  }

  hideLoadingOverlay() {
    const overlay = document.getElementById('aym-loading-overlay');
    if (overlay) overlay.style.display = 'none';
  }

  showSuccessMessage(message) {
    this.showNotification(message, 'success');
  }

  showErrorMessage(message) {
    this.showNotification(message, 'error');
  }

  showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `aym-notification aym-notification-${type}`;
    notification.textContent = message;
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      padding: 15px 20px;
      border-radius: 8px;
      color: white;
      font-weight: 500;
      z-index: 10001;
      animation: slideInRight 0.3s ease;
      background: ${type === 'success' ? 'var(--aym-glow-green)' : '#ff6b6b'};
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
      notification.style.animation = 'slideOutRight 0.3s ease';
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 300);
    }, 3000);
  }

  getKPIData() {
    const kpis = {};
    document.querySelectorAll('.aym-kpi-card').forEach(card => {
      const label = card.querySelector('.aym-kpi-label').textContent;
      const value = card.querySelector('.aym-kpi-value').textContent;
      kpis[label] = value;
    });
    return kpis;
  }

  getChartData() {
    const charts = {};
    Object.keys(this.charts).forEach(key => {
      charts[key] = this.charts[key].data;
    });
    return charts;
  }
}

/* ═══════════════════════════════════════════════════════════════════════════════
   REVOLUTIONARY GLOBAL FUNCTIONS
   ═══════════════════════════════════════════════════════════════════════════════ */

function refreshDashboard() {
  window.aymDashboard.refreshDashboardData();

  // Visual feedback
  const btn = event.target.closest('.aym-btn');
  const icon = btn.querySelector('i');
  icon.classList.add('fa-spin');

  setTimeout(() => {
    icon.classList.remove('fa-spin');
  }, 2000);
}

function exportDashboard() {
  window.aymDashboard.exportDashboard();
}

function openSettings() {
  // Open dashboard settings modal
  window.location.href = '{{ url_dashboard_settings }}';
}

function exportFinancialReport() {
  window.location.href = '{{ url_financial_export }}';
}

function refreshBI() {
  // Refresh Business Intelligence data
  console.log('Refreshing Business Intelligence...');
}

function openKPIDetails(category) {
  window.aymDashboard.openKPIDetails(category);
}

function expandKPICategory(category) {
  window.aymDashboard.expandKPICategory(category);
}

/* ═══════════════════════════════════════════════════════════════════════════════
   REVOLUTIONARY INITIALIZATION
   ═══════════════════════════════════════════════════════════════════════════════ */

document.addEventListener('DOMContentLoaded', function() {
  window.aymDashboard = new AYMDashboard();

  // Add loading states
  document.querySelectorAll('.aym-chart-canvas canvas').forEach(canvas => {
    canvas.style.opacity = '0';
    setTimeout(() => {
      canvas.style.transition = 'opacity 0.5s ease-in-out';
      canvas.style.opacity = '1';
    }, 500);
  });

  // Initialize enhanced dashboard features
  try {
    window.aymDashboard.startPerformanceMonitoring();
    window.aymDashboard.setupKeyboardNavigation();
    window.aymDashboard.validateInitialData();
  } catch (error) {
    console.error('Enhanced features initialization failed:', error);
  }

  console.log('🚀 AYM ERP Ultimate Dashboard Initialized Successfully!');
});

// Enhanced keyboard navigation
document.addEventListener('keydown', function(e) {
  if (e.key === 'Escape' && document.querySelector('.aym-kpi-modal.active')) {
    window.aymDashboard.closeKPIModal();
  }

  if (e.key === 'Enter' && e.target.classList.contains('aym-kpi-card')) {
    e.target.click();
  }
});

// Performance monitoring
function refreshAIInsights() {
  const insights = document.querySelectorAll('.aym-ai-insight-item');
  insights.forEach(insight => {
    insight.style.opacity = '0.5';
    setTimeout(() => {
      insight.style.opacity = '1';
    }, 500);
  });
}

// Cleanup on page unload
window.addEventListener('beforeunload', function() {
  if (window.aymDashboard && window.aymDashboard.refreshInterval) {
    clearInterval(window.aymDashboard.refreshInterval);
  }
});
</script>

    <!-- Row 1: Key Metrics -->
    <div class="row">
      <!-- Sales Card -->
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-primary">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-shopping-cart fa-3x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ sales_stats.today_sales|number_format(2) }}</div>
                <div>{{ text_today_sales }} ({{ sales_stats.currency }})</div>
              </div>
            </div>
          </div>
          <div class="panel-footer">
            <div class="row">
              <div class="col-xs-6">
                <span class="text-muted">{{ sales_stats.today_orders }} {{ text_orders }}</span>
              </div>
              <div class="col-xs-6 text-right">
                {% if sales_stats.sales_change_percent >= 0 %}
                  <span class="text-success">
                    <i class="fa fa-arrow-up"></i> {{ sales_stats.sales_change_percent }}%
                  </span>
                {% else %}
                  <span class="text-danger">
                    <i class="fa fa-arrow-down"></i> {{ sales_stats.sales_change_percent|abs }}%
                  </span>
                {% endif %}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Inventory Card -->
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-success">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-cubes fa-3x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ inventory_stats.total_value|number_format(2) }}</div>
                <div>{{ text_inventory_value }} ({{ inventory_stats.currency }})</div>
              </div>
            </div>
          </div>
          <div class="panel-footer">
            <div class="row">
              <div class="col-xs-6">
                <span class="text-muted">{{ inventory_stats.total_products }} {{ text_products }}</span>
              </div>
              <div class="col-xs-6 text-right">
                {% if inventory_stats.low_stock_count > 0 %}
                  <span class="text-warning">
                    <i class="fa fa-exclamation-triangle"></i> {{ inventory_stats.low_stock_count }} {{ text_low_stock }}
                  </span>
                {% else %}
                  <span class="text-success">
                    <i class="fa fa-check"></i> {{ text_good }}
                  </span>
                {% endif %}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Monthly Target Card -->
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-warning">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-bullseye fa-3x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ sales_stats.achievement_rate }}%</div>
                <div>{{ text_monthly_target_achievement }}</div>
              </div>
            </div>
          </div>
          <div class="panel-footer">
            <div class="row">
              <div class="col-xs-6">
                <span class="text-muted">{{ sales_stats.monthly_sales|number_format(0) }} {{ text_from }} {{ sales_stats.monthly_target|number_format(0) }}</span>
              </div>
              <div class="col-xs-6 text-right">
                <div class="progress progress-mini">
                  <div class="progress-bar progress-bar-warning" style="width: {{ sales_stats.achievement_rate }}%"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Customers Card -->
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-info">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-users fa-3x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ sales_stats.monthly_customers }}</div>
                <div>{{ text_customers_this_month }}</div>
              </div>
            </div>
          </div>
          <div class="panel-footer">
            <div class="row">
              <div class="col-xs-6">
                <span class="text-muted">{{ text_average_order }}: {{ sales_stats.today_avg_order|number_format(2) }}</span>
              </div>
              <div class="col-xs-6 text-right">
                <span class="text-info">
                  <i class="fa fa-user-plus"></i> {{ text_active }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Row 2: Charts and Tables -->
    <div class="row">
      <!-- Branch Performance -->
      <div class="col-lg-6">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-building"></i> {{ text_branch_performance }}</h3>
          </div>
          <div class="panel-body">
            <div class="table-responsive">
              <table class="table table-striped table-hover">
                <thead>
                  <tr>
                    <th>{{ text_branch }}</th>
                    <th>{{ text_city }}</th>
                    <th>{{ text_sales }}</th>
                    <th>{{ text_orders }}</th>
                    <th>{{ text_percentage }}</th>
                  </tr>
                </thead>
                <tbody>
                  {% for branch in branch_stats.branches %}
                  <tr>
                    <td>
                      <strong>{{ branch.name }}</strong>
                      <small class="text-muted">({{ branch.type }})</small>
                    </td>
                    <td>{{ branch.city }}</td>
                    <td>{{ branch.total_sales|number_format(2) }}</td>
                    <td>{{ branch.total_orders }}</td>
                    <td>
                      <div class="progress progress-mini">
                        <div class="progress-bar" style="width: {{ branch.sales_percentage }}%; background-color: {{ branch.performance_color }}"></div>
                      </div>
                      <small>{{ branch.sales_percentage }}%</small>
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      <!-- Top Products -->
      <div class="col-lg-6">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-star"></i> {{ text_top_selling_products }}</h3>
          </div>
          <div class="panel-body">
            <div class="table-responsive">
              <table class="table table-striped table-hover">
                <thead>
                  <tr>
                    <th>#</th>
                    <th>{{ text_product }}</th>
                    <th>{{ text_quantity }}</th>
                    <th>{{ text_revenue }}</th>
                    <th>{{ text_margin }}</th>
                  </tr>
                </thead>
                <tbody>
                  {% for product in top_products.products %}
                  <tr>
                    <td>
                      <span class="badge" style="background-color: {{ product.performance_color }}">{{ product.rank }}</span>
                    </td>
                    <td>
                      <strong>{{ product.name }}</strong>
                      <br><small class="text-muted">{{ product.model }}</small>
                    </td>
                    <td>{{ product.quantity_sold }}</td>
                    <td>{{ product.revenue|number_format(2) }}</td>
                    <td>
                      <span class="text-{% if product.profit_margin >= 20 %}success{% elseif product.profit_margin >= 10 %}warning{% else %}danger{% endif %}">
                        {{ product.profit_margin }}%
                      </span>
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Row 3: E-commerce & Sales Reps -->
    <div class="row">
      <!-- E-commerce Performance -->
      <div class="col-lg-6">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-shopping-bag"></i> أداء المتجر الإلكتروني</h3>
          </div>
          <div class="panel-body">
            <div class="row">
              <div class="col-md-6">
                <div class="metric-card">
                  <div class="metric-value text-primary">{{ ecommerce_stats.daily_visitors }}</div>
                  <div class="metric-label">{{ text_daily_visitors }}</div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="metric-card">
                  <div class="metric-value text-success">{{ ecommerce_stats.conversion_rate }}%</div>
                  <div class="metric-label">{{ text_conversion_rate }}</div>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-md-6">
                <div class="metric-card">
                  <div class="metric-value text-warning">{{ ecommerce_stats.cart_abandonment_rate }}%</div>
                  <div class="metric-label">{{ text_cart_abandonment_rate }}</div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="metric-card">
                  <div class="metric-value text-info">{{ ecommerce_stats.shipping_orders }}</div>
                  <div class="metric-label">{{ text_active_shipments }}</div>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-md-6">
                <div class="metric-card">
                  <div class="metric-value text-success">{{ ecommerce_stats.new_customers }}</div>
                  <div class="metric-label">{{ text_new_customers }}</div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="metric-card">
                  <div class="metric-value text-{% if ecommerce_stats.return_rate <= 5 %}success{% elseif ecommerce_stats.return_rate <= 10 %}warning{% else %}danger{% endif %}">{{ ecommerce_stats.return_rate }}%</div>
                  <div class="metric-label">{{ text_return_rate }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Sales Representatives Performance -->
      <div class="col-lg-6">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-users"></i> أداء المناديب</h3>
          </div>
          <div class="panel-body">
            <div class="table-responsive">
              <table class="table table-striped table-hover">
                <thead>
                  <tr>
                    <th>المندوب</th>
                    <th>الصفقات</th>
                    <th>المبيعات</th>
                    <th>الهدف</th>
                  </tr>
                </thead>
                <tbody>
                  {% for rep in sales_reps_stats.sales_reps %}
                  <tr>
                    <td>
                      <strong>{{ rep.name }}</strong>
                      <br><small class="text-muted">{{ rep.unique_customers }} عميل</small>
                    </td>
                    <td>{{ rep.deals_closed }}</td>
                    <td>{{ rep.total_sales|number_format(0) }}</td>
                    <td>
                      <div class="progress progress-mini">
                        <div class="progress-bar" style="width: {{ rep.achievement_rate }}%; background-color: {{ rep.performance_color }}"></div>
                      </div>
                      <small>{{ rep.achievement_rate }}%</small>
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
            <div class="text-center">
              <small class="text-muted">
                متوسط الأداء العام: {{ sales_reps_stats.summary.avg_performance }}%
              </small>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Row 4: Additional Metrics from info1.md -->
    <div class="row">
      <!-- Quick Metrics Cards -->
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-default">
          <div class="panel-body text-center">
            <h4 class="text-primary">{{ ecommerce_stats.average_rating }}/5</h4>
            <p class="text-muted">متوسط التقييم</p>
            <small>{{ ecommerce_stats.total_reviews }} تقييم</small>
          </div>
        </div>
      </div>

      <div class="col-lg-3 col-md-6">
        <div class="panel panel-default">
          <div class="panel-body text-center">
            <h4 class="text-success">{{ inventory_stats.low_stock_percentage }}%</h4>
            <p class="text-muted">نسبة المخزون المنخفض</p>
            <small>{{ inventory_stats.low_stock_count }} من {{ inventory_stats.total_products }}</small>
          </div>
        </div>
      </div>

      <div class="col-lg-3 col-md-6">
        <div class="panel panel-default">
          <div class="panel-body text-center">
            <h4 class="text-info">{{ sales_stats.today_items_sold }}</h4>
            <p class="text-muted">قطع مباعة اليوم</p>
            <small>من {{ sales_stats.today_orders }} طلب</small>
          </div>
        </div>
      </div>

      <div class="col-lg-3 col-md-6">
        <div class="panel panel-default">
          <div class="panel-body text-center">
            <h4 class="text-warning">{{ branch_stats.summary.total_branches }}</h4>
            <p class="text-muted">إجمالي الفروع</p>
            <small>متوسط: {{ branch_stats.summary.avg_sales_per_branch|number_format(0) }}</small>
          </div>
        </div>
      </div>
    </div>

    <!-- Row 5: Financial & HR Summary -->
    <div class="row">
      <!-- Financial Summary -->
      <div class="col-lg-8">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-money"></i> الملخص المالي</h3>
          </div>
          <div class="panel-body">
            <div class="row">
              <div class="col-md-3">
                <div class="metric-card">
                  <div class="metric-value text-success">{{ financial_summary.total_revenue|number_format(0) }}</div>
                  <div class="metric-label">إجمالي الإيرادات</div>
                </div>
              </div>
              <div class="col-md-3">
                <div class="metric-card">
                  <div class="metric-value text-primary">{{ financial_summary.net_profit|number_format(0) }}</div>
                  <div class="metric-label">صافي الربح</div>
                </div>
              </div>
              <div class="col-md-3">
                <div class="metric-card">
                  <div class="metric-value text-warning">{{ financial_summary.total_expenses|number_format(0) }}</div>
                  <div class="metric-label">إجمالي المصروفات</div>
                </div>
              </div>
              <div class="col-md-3">
                <div class="metric-card">
                  <div class="metric-value text-info">{{ financial_summary.bank_balance|number_format(0) }}</div>
                  <div class="metric-label">رصيد البنك</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- HR & CRM Summary -->
      <div class="col-lg-4">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-users"></i> الموارد البشرية والعملاء</h3>
          </div>
          <div class="panel-body">
            <div class="row">
              <div class="col-md-6">
                <div class="metric-card">
                  <div class="metric-value text-primary">{{ hr_stats.active_employees }}</div>
                  <div class="metric-label">موظف نشط</div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="metric-card">
                  <div class="metric-value text-warning">{{ hr_stats.pending_leaves }}</div>
                  <div class="metric-label">إجازة معلقة</div>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-md-6">
                <div class="metric-card">
                  <div class="metric-value text-success">{{ crm_stats.active_customers }}</div>
                  <div class="metric-label">عميل نشط</div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="metric-card">
                  <div class="metric-value text-info">{{ crm_stats.new_customers_month }}</div>
                  <div class="metric-label">عميل جديد</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Row 6: KPI Summary from 170+ existing indicators -->
    <div class="row">
      <div class="col-lg-12">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-dashboard"></i> مؤشرات الأداء الرئيسية (170+ مؤشر)</h3>
          </div>
          <div class="panel-body">
            <div class="row">
              <div class="col-md-2">
                <div class="metric-card">
                  <div class="metric-value text-primary">{{ kpi_data.critical_kpis.accounting.total_accounts|default(0) }}</div>
                  <div class="metric-label">إجمالي الحسابات</div>
                </div>
              </div>
              <div class="col-md-2">
                <div class="metric-card">
                  <div class="metric-value text-success">{{ kpi_data.critical_kpis.inventory.inventory_turnover|default(0) }}</div>
                  <div class="metric-label">دوران المخزون</div>
                </div>
              </div>
              <div class="col-md-2">
                <div class="metric-card">
                  <div class="metric-value text-warning">{{ kpi_data.critical_kpis.sales.conversion_rate|default(0) }}%</div>
                  <div class="metric-label">معدل التحويل</div>
                </div>
              </div>
              <div class="col-md-2">
                <div class="metric-card">
                  <div class="metric-value text-info">{{ kpi_data.critical_kpis.procurement.supplier_performance|default(0) }}</div>
                  <div class="metric-label">أداء الموردين</div>
                </div>
              </div>
              <div class="col-md-2">
                <div class="metric-card">
                  <div class="metric-value text-danger">{{ kpi_data.critical_kpis.hr.employee_satisfaction|default(0) }}%</div>
                  <div class="metric-label">رضا الموظفين</div>
                </div>
              </div>
              <div class="col-md-2">
                <div class="metric-card">
                  <div class="metric-value text-primary">{{ kpi_data.important_kpis.advanced_accounting.roi|default(0) }}%</div>
                  <div class="metric-label">العائد على الاستثمار</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Row 7: Additional Modules from info1.md -->
    <div class="row">
      <!-- Tasks & Productivity -->
      <div class="col-lg-4">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-tasks"></i> المهام والإنتاجية</h3>
          </div>
          <div class="panel-body">
            <div class="row">
              <div class="col-md-6">
                <div class="metric-card">
                  <div class="metric-value text-primary">{{ tasks_stats.daily_tasks }}</div>
                  <div class="metric-label">مهام يومية</div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="metric-card">
                  <div class="metric-value text-danger">{{ tasks_stats.overdue_tasks }}</div>
                  <div class="metric-label">مهام متأخرة</div>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-md-6">
                <div class="metric-card">
                  <div class="metric-value text-success">{{ tasks_stats.completion_rate }}%</div>
                  <div class="metric-label">نسبة الإنجاز</div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="metric-card">
                  <div class="metric-value text-warning">{{ tasks_stats.active_projects }}</div>
                  <div class="metric-label">مشاريع جارية</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Logistics & Shipping -->
      <div class="col-lg-4">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-truck"></i> الشحن واللوجستيك</h3>
          </div>
          <div class="panel-body">
            <div class="row">
              <div class="col-md-6">
                <div class="metric-card">
                  <div class="metric-value text-info">{{ logistics_stats.active_shipments }}</div>
                  <div class="metric-label">شحنات جارية</div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="metric-card">
                  <div class="metric-value text-danger">{{ logistics_stats.delayed_shipments }}</div>
                  <div class="metric-label">شحنات متأخرة</div>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-md-6">
                <div class="metric-card">
                  <div class="metric-value text-success">{{ logistics_stats.on_time_delivery_rate }}%</div>
                  <div class="metric-label">التسليم في الوقت</div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="metric-card">
                  <div class="metric-value text-warning">{{ logistics_stats.total_shipping_cost|number_format(0) }}</div>
                  <div class="metric-label">تكلفة الشحن</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Business Intelligence -->
      <div class="col-lg-4">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-brain"></i> ذكاء الأعمال</h3>
          </div>
          <div class="panel-body">
            <div class="row">
              <div class="col-md-6">
                <div class="metric-card">
                  <div class="metric-value text-success">{{ bi_stats.sales_forecast|number_format(0) }}</div>
                  <div class="metric-label">توقعات المبيعات</div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="metric-card">
                  <div class="metric-value text-danger">{{ bi_stats.churn_risk_customers }}</div>
                  <div class="metric-label">عملاء معرضون للخسارة</div>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-md-6">
                <div class="metric-card">
                  <div class="metric-value text-info">{{ bi_stats.customer_lifetime_value|number_format(0) }}</div>
                  <div class="metric-label">قيمة العميل الدائمة</div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="metric-card">
                  <div class="metric-value text-primary">{{ bi_stats.compound_growth_rate }}%</div>
                  <div class="metric-label">نمو مركب</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

  </div>
</div>

<!-- Real-time Auto Refresh Script -->
<script>
$(document).ready(function() {
    var refreshInterval;
    var isRefreshing = false;

    // Auto refresh every 2 minutes
    function startAutoRefresh() {
        refreshInterval = setInterval(function() {
            refreshDashboard();
        }, 120000); // 2 minutes
    }

    // AJAX refresh function
    function refreshDashboard() {
        if (isRefreshing) return;

        isRefreshing = true;
        $('#refresh-dashboard').addClass('loading').prop('disabled', true);

        $.ajax({
            url: 'index.php?route=common/dashboard/refresh&user_token={{ user_token }}',
            type: 'GET',
            dataType: 'json',
            success: function(data) {
                updateDashboardData(data);
                showNotification('{{ text_data_updated_successfully }}', 'success');
            },
            error: function() {
                showNotification('{{ text_error_updating_data }}', 'error');
            },
            complete: function() {
                isRefreshing = false;
                $('#refresh-dashboard').removeClass('loading').prop('disabled', false);
            }
        });
    }

    // Update dashboard data
    function updateDashboardData(data) {
        // Add visual feedback
        $('.metric-card, .panel').addClass('updating');

        // Update all sections dynamically
        setTimeout(function() {
            if (data.sales_stats) {
                $('.huge').each(function() {
                    if ($(this).siblings().text().includes('مبيعات')) {
                        $(this).text(formatNumber(data.sales_stats.today_sales));
                    }
                });
            }

            if (data.inventory_stats) {
                $('.huge').each(function() {
                    if ($(this).siblings().text().includes('قيمة المخزون')) {
                        $(this).text(formatNumber(data.inventory_stats.total_value));
                    }
                });
            }

            // Remove updating class and add updated
            $('.metric-card, .panel').removeClass('updating').addClass('updated');

            // Update timestamp
            var timestamp = new Date().toLocaleString('ar-EG');
            if ($('.refresh-indicator').length === 0) {
                $('body').append('<div class="refresh-indicator">آخر تحديث: ' + timestamp + '</div>');
            } else {
                $('.refresh-indicator').text('آخر تحديث: ' + timestamp);
            }

            setTimeout(function() {
                $('.metric-card, .panel').removeClass('updated');
            }, 1000);
        }, 500);
    }

    // Format numbers
    function formatNumber(num) {
        return parseFloat(num).toLocaleString('ar-EG', {
            minimumFractionDigits: 0,
            maximumFractionDigits: 2
        });
    }

    // Show notification
    function showNotification(message, type) {
        var alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        var notification = '<div class="alert ' + alertClass + ' alert-dismissible" style="position: fixed; top: 20px; right: 20px; z-index: 9999;">' +
            '<button type="button" class="close" data-dismiss="alert">&times;</button>' +
            message + '</div>';

        $('body').append(notification);
        setTimeout(function() {
            $('.alert').fadeOut();
        }, 3000);
    }

    // Manual refresh button
    $('#refresh-dashboard').click(function(e) {
        e.preventDefault();
        refreshDashboard();
    });

    // Export functionality
    $('#export-dashboard').click(function() {
        window.open('?route=common/dashboard/export&user_token={{ user_token }}', '_blank');
    });

    // Print functionality
    $('#print-dashboard').click(function() {
        window.print();
    });

    // Start auto refresh
    startAutoRefresh();

    // Stop auto refresh when page is hidden
    document.addEventListener('visibilitychange', function() {
        if (document.hidden) {
            clearInterval(refreshInterval);
        } else {
            startAutoRefresh();
        }
    });
});
</script>
              <i class="fa fa-refresh"></i>
            </button>
            <button class="btn btn-sm btn-outline-info minimize-widget" title="{{ text_minimize_widget|default('تصغير') }}">
              <i class="fa fa-minus"></i>
            </button>
            <button class="btn btn-sm btn-outline-warning fullscreen-widget" title="{{ text_fullscreen_widget|default('لء الشاشة') }}">
              <i class="fa fa-expand"></i>
            </button>
            <button class="btn btn-sm btn-outline-danger remove-widget" title="{{ text_remove_widget|default('إزالة') }}">
              <i class="fa fa-times"></i>
            </button>
          </div>
        </div>
        <div class="widget-body">
          <div class="row">
            <div class="col-md-6">
              <div class="kpi-card">
                <div class="kpi-value" data-kpi="total_revenue" data-format="currency">0</div>
                <div class="kpi-label">{{ text_total_revenue|default('إجمالي الإيراات') }}</div>
                <div class="kpi-trend positive">
                  <i class="fa fa-arrow-up"></i>
                  <span data-kpi="revenue_trend" data-format="percentage">0%</span>
                </div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="kpi-card">
                <div class="kpi-value" data-kpi="net_profit" data-format="currency">0</div>
                <div class="kpi-label">{{ text_net_profit|default('صافي الربح') }}</div>
                <div class="kpi-trend positive">
                  <i class="fa fa-arrow-up"></i>
                  <span data-kpi="profit_trend" data-format="percentage">0%</span>
                </div>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-6">
              <div class="kpi-card">
                <div class="kpi-value" data-kpi="gross_margin" data-format="percentage">0%</div>
                <div class="kpi-label">{{ text_gross_margin|default('البح الإجمالي') }}</div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="kpi-card">
                <div class="kpi-value" data-kpi="operating_margin" data-format="percentage">0%</div>
                <div class="kpi-label">{{ text_operating_margin|default('الربح التشغيلي') }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Strategic Financial Metrics Widget -->
      <div class="dashboard-widget" data-widget="strategic_financial" data-widget-type="executive" data-width="6" data-height="4">
        <div class="widget-header">
          <h5><i class="fa fa-calculator"></i> {{ text_strategic_financial|default('المؤشرات الالية الاستراتيجية') }}</h5>
          <div class="widget-controls">
            <button class="btn btn-sm btn-outline-secondary refresh-widget" title="{{ text_refresh_widget|default('تحديث') }}">
              <i class="fa fa-refresh"></i>
            </button>
            <button class="btn btn-sm btn-outline-danger remove-widget" title="{{ text_remove_widget|default('إزالة') }}">
              <i class="fa fa-times"></i>
            </button>
          </div>
        </div>
        <div class="widget-body">
          <div class="row">
            <div class="col-md-6">
              <div class="metric-group">
                <div class="metric" data-metric="roi" data-format="percentage">0%</div>
                <div class="metric-label">{{ text_roi|default('اعائد على الاستثمر') }}</div>
              </div>
              <div class="metric-group">
                <div class="metric" data-metric="roa" data-format="percentage">0%</div>
                <div class="metric-label">{{ text_roa|default('العائد على الأصول') }}</div>
              </div>
              <div class="metric-group">
                <div class="metric" data-metric="roe" data-format="percentage">0%</div>
                <div class="metric-label">{{ text_roe|default('العائد على حقو الملكية') }}</div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="metric-group">
                <div class="metric" data-metric="debt_to_equity" data-format="number">0</div>
                <div class="metric-label">{{ text_debt_to_equity|default('نسبة الدين إلى حقوق الملكية') }}</div>
              </div>
              <div class="metric-group">
                <div class="metric" data-metric="current_ratio" data-format="number">0</div>
                <div class="metric-label">{{ text_current_ratio|default('النسبة الحالية') }}</div>
              </div>
              <div class="metric-group">
                <div class="metric" data-metric="working_capital" data-format="currency">0</div>
                <div class="metric-label">{{ text_working_capital|default('رأس المال اعامل') }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- === SALES & CRM WIDGETS === -->
      
      <!-- Sales Performance Widget -->
      <div class="dashboard-widget" data-widget="sales_performance" data-widget-type="sales" data-width="6" data-height="4">
        <div class="widget-header">
          <h5><i class="fa fa-chart-bar"></i> {{ text_sales_performance|default('أداء المبيعات') }}</h5>
          <div class="widget-controls">
            <button class="btn btn-sm btn-outline-secondary refresh-widget" title="{{ text_refresh_widget|default('تحديث') }}">
              <i class="fa fa-refresh"></i>
            </button>
            <button class="btn btn-sm btn-outline-danger remove-widget" title="{{ text_remove_widget|default('إزالة') }}">
              <i class="fa fa-times"></i>
            </button>
          </div>
        </div>
        <div class="widget-body">
          <div class="row">
            <div class="col-md-6">
              <div class="kpi-card">
                <div class="kpi-value" data-kpi="total_sales" data-format="currency">0</div>
                <div class="kpi-label">{{ text_total_sales|default('إجمالي المبيعات') }}</div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="kpi-card">
                <div class="kpi-value" data-kpi="total_orders" data-format="number">0</div>
                <div class="kpi-label">{{ text_total_orders|default('إجمالي الطلبات') }}</div>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-6">
              <div class="kpi-card">
                <div class="kpi-value" data-kpi="average_order_value" data-format="currency">0</div>
                <div class="kpi-label">{{ text_average_order_value|default('متوسط قيمة الطلب') }}</div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="kpi-card">
                <div class="kpi-value" data-kpi="conversion_rate" data-format="percentage">0%</div>
                <div class="kpi-label">{{ text_conversion_rate|default('معدل التحويل') }}</div>
              </div>
            </div>
          </div>
          <div class="chart-container" data-chart-type="line" data-chart-data="sales_trends">
            <canvas id="sales-trends-chart"></canvas>
          </div>
        </div>
      </div>

      <!-- Customer Analytics Widget -->
      <div class="dashboard-widget" data-widget="customer_analytics" data-widget-type="sales" data-width="6" data-height="4">
        <div class="widget-header">
          <h5><i class="fa fa-users"></i> {{ text_customer_analytics|default('تليلات العملاء') }}</h5>
          <div class="widget-controls">
            <button class="btn btn-sm btn-outline-secondary refresh-widget" title="{{ text_refresh_widget|default('تحديث') }}">
              <i class="fa fa-refresh"></i>
            </button>
            <button class="btn btn-sm btn-outline-danger remove-widget" title="{{ text_remove_widget|default('إزالة') }}">
              <i class="fa fa-times"></i>
            </button>
          </div>
        </div>
        <div class="widget-body">
          <div class="row">
            <div class="col-md-6">
              <div class="kpi-card">
                <div class="kpi-value" data-kpi="total_customers" data-format="number">0</div>
                <div class="kpi-label">{{ text_total_customers|default('إجمالي العملاء') }}</div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="kpi-card">
                <div class="kpi-value" data-kpi="new_customers" data-format="number">0</div>
                <div class="kpi-label">{{ text_new_customers|default('الملاء الجدد') }}</div>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-6">
              <div class="kpi-card">
                <div class="kpi-value" data-kpi="customer_lifetime_value" data-format="currency">0</div>
                <div class="kpi-label">{{ text_customer_lifetime_value|default('قيمة لعميل مدى الحياة') }}</div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="kpi-card">
                <div class="kpi-value" data-kpi="customer_retention_rate" data-format="percentage">0%</div>
                <div class="kpi-label">{{ text_customer_retention_rate|default('معدل الاحتفاظ بالعملا') }}</div>
              </div>
            </div>
          </div>
          <div class="chart-container" data-chart-type="pie" data-chart-data="customer_segments">
            <canvas id="customer-segments-chart"></canvas>
          </div>
        </div>
      </div>

      <!-- === INVENTORY & WAREHOUSE WIDGETS === -->
      
      <!-- Inventory Overview Widget -->
      <div class="dashboard-widget" data-widget="inventory_overview" data-widget-type="inventory" data-width="6" data-height="4">
        <div class="widget-header">
          <h5><i class="fa fa-boxes"></i> {{ text_inventory_overview|default('نظرة عامة على المخزون') }}</h5>
          <div class="widget-controls">
            <button class="btn btn-sm btn-outline-secondary refresh-widget" title="{{ text_refresh_widget|default('تحدي') }}">
              <i class="fa fa-refresh"></i>
            </button>
            <button class="btn btn-sm btn-outline-danger remove-widget" title="{{ text_remove_widget|default('إزاة') }}">
              <i class="fa fa-times"></i>
            </button>
          </div>
        </div>
        <div class="widget-body">
          <div class="row">
            <div class="col-md-6">
              <div class="kpi-card">
                <div class="kpi-value" data-kpi="total_skus" data-format="number">0</div>
                <div class="kpi-label">{{ text_total_skus|default('إجمالي الصناف') }}</div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="kpi-card">
                <div class="kpi-value" data-kpi="inventory_value" data-format="currency">0</div>
                <div class="kpi-label">{{ text_inventory_value|default('قيمة المخون') }}</div>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-6">
              <div class="kpi-card">
                <div class="kpi-value" data-kpi="stock_turnover_rate" data-format="number">0</div>
                <div class="kpi-label">{{ text_stock_turnover_rate|default('مدل دوران المخزون') }}</div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="kpi-card">
                <div class="kpi-value" data-kpi="inventory_accuracy" data-format="percentage">0%</div>
                <div class="kpi-label">{{ text_inventory_accuracy|default('دقة المخزون') }}</div>
              </div>
            </div>
          </div>
          <div class="widget-table" data-table="low_stock_alerts">
            <table>
              <thead>
                <tr>
                  <th>{{ text_product|default('المنتج') }}</th>
                  <th>{{ text_current_stock|default('المخزون الحالي') }}</th>
                  <th>{{ text_min_stock|default('الد الأدنى') }}</th>
                  <th>{{ text_status|default('الحالة') }}</th>
                </tr>
              </thead>
              <tbody>
                <!-- Data will be populated by JavaScript -->
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- === ETA & TAX COMPLIANCE WIDGETS === -->
      
      <!-- ETA Integration Status Widget -->
      <div class="dashboard-widget" data-widget="eta_status" data-widget-type="eta" data-width="6" data-height="3">
        <div class="widget-header">
          <h5><i class="fa fa-receipt"></i> {{ text_eta_integration_status|default('حالة تكامل الضرائب المصرية') }}</h5>
          <div class="widget-controls">
            <button class="btn btn-sm btn-outline-secondary refresh-widget" title="{{ text_refresh_widget|default('تحديث') }}">
              <i class="fa fa-refresh"></i>
            </button>
            <button class="btn btn-sm btn-outline-primary test-eta-connection" title="{{ text_test_connection|default('اختبار الاتصال') }}">
              <i class="fa fa-plug"></i>
            </button>
            <button class="btn btn-sm btn-outline-danger remove-widget" title="{{ text_remove_widget|default('إزالة') }}">
              <i class="fa fa-times"></i>
            </button>
          </div>
        </div>
        <div class="widget-body">
          <div class="row">
            <div class="col-md-4">
              <div class="eta-status-card">
                <div class="status-indicator" id="eta-connection-status"></div>
                <div class="status-label">{{ text_connection_status|default('حالة الاتصال') }}</div>
                <div class="status-value" id="eta-connection-text">{{ text_checking|default('جاري الفحص...') }}</div>
              </div>
            </div>
            <div class="col-md-4">
              <div class="eta-status-card">
                <div class="status-indicator" id="eta-queue-status"></div>
                <div class="status-label">{{ text_queue_status|default('حالة قائمة الانتظار') }}</div>
                <div class="status-value" id="eta-queue-count">0</div>
              </div>
            </div>
            <div class="col-md-4">
              <div class="eta-status-card">
                <div class="status-indicator" id="eta-sync-status"></div>
                <div class="status-label">{{ text_last_sync|default('آخر مزامنة') }}</div>
                <div class="status-value" id="eta-last-sync">{{ text_never|default('أبداً') }}</div>
              </div>
            </div>
          </div>
          <div class="eta-actions">
            <button class="btn btn-sm btn-primary" onclick="processETAQueue()">{{ text_process_queue|default('معالجة قائمة الانتظار') }}</button>
            <button class="btn btn-sm btn-warning" onclick="clearETAQueue()">{{ text_clear_queue|default('مسح قائمة الانتظار') }}</button>
            <button class="btn btn-sm btn-info" onclick="viewETALogs()">{{ text_view_logs|default('عرض السجلات') }}</button>
          </div>
        </div>
      </div>
      
      <!-- E-Invoicing Statistics Widget -->
      <div class="dashboard-widget" data-widget="eta_statistics" data-widget-type="eta" data-width="6" data-height="4">
        <div class="widget-header">
          <h5><i class="fa fa-chart-pie"></i> {{ text_einvoicing_statistics|default('إحصائيات الفواتير الإلكترونية') }}</h5>
          <div class="widget-controls">
            <button class="btn btn-sm btn-outline-secondary refresh-widget" title="{{ text_refresh_widget|default('تحديث') }}">
              <i class="fa fa-refresh"></i>
            </button>
            <button class="btn btn-sm btn-outline-danger remove-widget" title="{{ text_remove_widget|default('إزالة') }}">
              <i class="fa fa-times"></i>
            </button>
          </div>
        </div>
        <div class="widget-body">
          <div class="row">
            <div class="col-md-3">
              <div class="kpi-card eta-kpi">
                <div class="kpi-value" data-kpi="eta_invoices_sent" data-format="number">0</div>
                <div class="kpi-label">{{ text_invoices_sent|default('الفواتير المرسلة') }}</div>
              </div>
            </div>
            <div class="col-md-3">
              <div class="kpi-card eta-kpi">
                <div class="kpi-value" data-kpi="eta_invoices_approved" data-format="number">0</div>
                <div class="kpi-label">{{ text_invoices_approved|default('الفواتير المعتمدة') }}</div>
              </div>
            </div>
            <div class="col-md-3">
              <div class="kpi-card eta-kpi">
                <div class="kpi-value" data-kpi="eta_invoices_rejected" data-format="number">0</div>
                <div class="kpi-label">{{ text_invoices_rejected|default('الفواتير المرفوضة') }}</div>
              </div>
            </div>
            <div class="col-md-3">
              <div class="kpi-card eta-kpi">
                <div class="kpi-value" data-kpi="eta_success_rate" data-format="percentage">0%</div>
                <div class="kpi-label">{{ text_success_rate|default('معدل النجاح') }}</div>
              </div>
            </div>
          </div>
          <div class="chart-container" data-chart-type="doughnut" data-chart-data="eta_invoice_status">
            <canvas id="eta-invoice-status-chart"></canvas>
          </div>
          <div class="eta-recent-activity">
            <h6>{{ text_recent_activity|default('النشاط الأخير') }}</h6>
            <div class="activity-list" id="eta-recent-activity">
              <!-- Activity items will be populated by JavaScript -->
            </div>
          </div>
        </div>
      </div>
      
      <!-- Tax Compliance Widget -->
      <div class="dashboard-widget" data-widget="tax_compliance" data-widget-type="eta" data-width="12" data-height="3">
        <div class="widget-header">
          <h5><i class="fa fa-balance-scale"></i> {{ text_tax_compliance|default('الامتثال الضريبي') }}</h5>
          <div class="widget-controls">
            <button class="btn btn-sm btn-outline-secondary refresh-widget" title="{{ text_refresh_widget|default('تحديث') }}">
              <i class="fa fa-refresh"></i>
            </button>
            <button class="btn btn-sm btn-outline-danger remove-widget" title="{{ text_remove_widget|default('إزالة') }}">
              <i class="fa fa-times"></i>
            </button>
          </div>
        </div>
        <div class="widget-body">
          <div class="row">
            <div class="col-md-2">
              <div class="compliance-metric">
                <div class="metric-value" data-metric="vat_collected" data-format="currency">0</div>
                <div class="metric-label">{{ text_vat_collected|default('ضريبة القيمة المضافة المحصلة') }}</div>
              </div>
            </div>
            <div class="col-md-2">
              <div class="compliance-metric">
                <div class="metric-value" data-metric="vat_paid" data-format="currency">0</div>
                <div class="metric-label">{{ text_vat_paid|default('ضريبة القيمة المضافة المدفوعة') }}</div>
              </div>
            </div>
            <div class="col-md-2">
              <div class="compliance-metric">
                <div class="metric-value" data-metric="vat_due" data-format="currency">0</div>
                <div class="metric-label">{{ text_vat_due|default('ضريبة القيمة المضافة المستحقة') }}</div>
              </div>
            </div>
            <div class="col-md-2">
              <div class="compliance-metric">
                <div class="metric-value" data-metric="withholding_tax" data-format="currency">0</div>
                <div class="metric-label">{{ text_withholding_tax|default('ضريبة الخصم') }}</div>
              </div>
            </div>
            <div class="col-md-2">
              <div class="compliance-metric">
                <div class="metric-value" data-metric="compliance_score" data-format="percentage">0%</div>
                <div class="metric-label">{{ text_compliance_score|default('نقاط الامتثال') }}</div>
              </div>
            </div>
            <div class="col-md-2">
              <div class="compliance-metric">
                <div class="metric-value" data-metric="pending_submissions" data-format="number">0</div>
                <div class="metric-label">{{ text_pending_submissions|default('المعاملات المعلقة') }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- === FINANCE & ACCOUNTING WIDGETS === -->
      
      <!-- Financial Overview Widget -->
      <div class="dashboard-widget" data-widget="financial_overview" data-widget-type="finance" data-width="6" data-height="4">
        <div class="widget-header">
          <h5><i class="fa fa-dollar-sign"></i> {{ text_financial_overview|default('نظرة عامة على المالية') }}</h5>
          <div class="widget-controls">
            <button class="btn btn-sm btn-outline-secondary refresh-widget" title="{{ text_refresh_widget|default('تحديث') }}">
              <i class="fa fa-refresh"></i>
            </button>
            <button class="btn btn-sm btn-outline-danger remove-widget" title="{{ text_remove_widget|default('إزالة') }}">
              <i class="fa fa-times"></i>
            </button>
          </div>
        </div>
        <div class="widget-body">
          <div class="row">
            <div class="col-md-6">
              <div class="kpi-card">
                <div class="kpi-value" data-kpi="total_revenue" data-format="currency">0</div>
                <div class="kpi-label">{{ text_total_revenue|default('إجمالي الإيرادات') }}</div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="kpi-card">
                <div class="kpi-value" data-kpi="total_expenses" data-format="currency">0</div>
                <div class="kpi-label">{{ text_total_expenses|default('إجمالي المصروفات') }}</div>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-6">
              <div class="kpi-card">
                <div class="kpi-value" data-kpi="accounts_receivable" data-format="currency">0</div>
                <div class="kpi-label">{{ text_accounts_receivable|default('الذمم المدينة') }}</div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="kpi-card">
                <div class="kpi-value" data-kpi="accounts_payable" data-format="currency">0</div>
                <div class="kpi-label">{{ text_accounts_payable|default('الذمم الدائنة') }}</div>
              </div>
            </div>
          </div>
          <div class="chart-container" data-chart-type="bar" data-chart-data="financial_ratios">
            <canvas id="financial-ratios-chart"></canvas>
          </div>
        </div>
      </div>

      <!-- Cash Flow Analysis Widget -->
      <div class="dashboard-widget" data-widget="cash_flow_analysis" data-widget-type="finance" data-width="6" data-height="4">
        <div class="widget-header">
          <h5><i class="fa fa-money-bill-wave"></i> {{ text_cash_flow_analysis|default('تحليل التدف النقدي') }}</h5>
          <div class="widget-controls">
            <button class="btn btn-sm btn-outline-secondary refresh-widget" title="{{ text_refresh_widget|default('تحديث') }}">
              <i class="fa fa-refresh"></i>
            </button>
            <button class="btn btn-sm btn-outline-danger remove-widget" title="{{ text_remove_widget|default('إزالة') }}">
              <i class="fa fa-times"></i>
            </button>
          </div>
        </div>
        <div class="widget-body">
          <div class="row">
            <div class="col-md-6">
              <div class="kpi-card">
                <div class="kpi-value" data-kpi="operating_cash_flow" data-format="currency">0</div>
                <div class="kpi-label">{{ text_operating_cash_flow|default('لتدفق النقدي التغيلي') }}</div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="kpi-card">
                <div class="kpi-value" data-kpi="free_cash_flow" data-format="currency">0</div>
                <div class="kpi-label">{{ text_free_cash_flow|default('التدفق الندي الحر') }}</div>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-md-6">
              <div class="kpi-card">
                <div class="kpi-value" data-kpi="cash_position" data-format="currency">0</div>
                <div class="kpi-label">{{ text_cash_position|default('المركز النقدي') }}</div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="kpi-card">
                <div class="kpi-value" data-kpi="working_capital" data-format="currency">0</div>
                <div class="kpi-label">{{ text_working_capital|default('رأس المال العامل') }}</div>
              </div>
            </div>
          </div>
          <div class="chart-container" data-chart-type="line" data-chart-data="cash_flow_trends">
            <canvas id="cash-flow-trends-chart"></canvas>
          </div>
        </div>
      </div>

    </div>
  </div>

  <!-- Loading Indicator -->
  <div id="loading-indicator" class="loading-overlay" style="display: none;">
    <div class="loading-spinner">
      <i class="fa fa-spinner fa-spin fa-3x"></i>
      <p>{{ text_loading|default('جري التحميل...') }}</p>
    </div>
  </div>

  <!-- Alert Container -->
  <div class="alert-container"></div>

  <!-- Widget Gallery Modal -->
  <div id="widget-gallery-modal" class="modal-overlay" style="display: none;">
    <div class="modal">
      <div class="modal-header">
        <h3 class="modal-title">{{ text_add_widget|default('إضافة مكون') }}</h3>
        <button class="modal-close">&times;</button>
      </div>
      <div class="modal-body">
        <div class="widget-gallery">
          <!-- Widget categories will be populated by JavaScript -->
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn btn-default" onclick="closeModal()">{{ text_cancel|default('إلغء') }}</button>
      </div>
    </div>
  </div>

  <!-- Widget Settings Modal -->
  <div id="widget-settings-modal" class="modal-overlay" style="display: none;">
    <div class="modal">
      <div class="modal-header">
        <h3 class="modal-title">{{ text_widget_settings|default('عدادات المكونات') }}</h3>
        <button class="modal-close">&times;</button>
      </div>
      <div class="modal-body">
        <div class="settings-section">
          <h4>{{ text_dashboard_layout|default('تخطيط لوحة المعلمات') }}</h4>
          <div class="form-group">
            <label>{{ text_layout_type|default('نوع التخطيط') }}</label>
            <select id="layout-type" class="form-control">
              <option value="grid">{{ text_grid_layout|default('تخطيط الشبكة') }}</option>
              <option value="flex">{{ text_flex_layout|default('التخطيط المرن') }}</option>
              <option value="masonry">{{ text_masonry_layout|default('التخيط المتراكب') }}</option>
            </select>
          </div>
        </div>
        <div class="settings-section">
          <h4>{{ text_auto_refresh|default('التحديث اتلقائي') }}</h4>
          <div class="form-group">
            <label>
              <input type="checkbox" id="auto-refresh-enabled" checked>
              {{ text_enable_auto_refresh|default('تفعيل التحدث التلقائي') }}
            </label>
          </div>
          <div class="form-group">
            <label>{{ text_refresh_interval|default('فترة التحديث') }}</label>
            <select id="refresh-interval" class="form-control">
              <option value="30">{{ text_30_seconds|default('30 ثنية') }}</option>
              <option value="60">{{ text_1_minute|default('دقيقة واحدة') }}</option>
              <option value="300" selected>{{ text_5_minutes|default('5 دائق') }}</option>
              <option value="600">{{ text_10_minutes|default('10 دقائ') }}</option>
            </select>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn btn-primary" onclick="saveWidgetSettings()">{{ text_save|default('حفظ') }}</button>
        <button class="btn btn-default" onclick="closeModal()">{{ text_cancel|default('إغاء') }}</button>
      </div>
    </div>
  </div>

</div>

<!-- Include Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<!-- Include Dashboard Widgets JavaScript -->
<script src="view/javascript/dashboard/widgets.js"></script>

<!-- Include Dashboard Widgets CSS -->
<link rel="stylesheet" href="view/stylesheet/dashboard/widgets.css">

<style>
/* ETA & Tax Compliance Widget Styles */
.eta-status-card {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    margin-bottom: 10px;
    transition: all 0.3s ease;
}

.eta-status-card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.status-indicator {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    margin: 0 auto 10px;
    position: relative;
    border: 2px solid #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.status-indicator.connected {
    background: linear-gradient(45deg, #28a745, #20c997);
    animation: pulse-green 2s infinite;
}

.status-indicator.disconnected {
    background: linear-gradient(45deg, #ffc107, #fd7e14);
    animation: pulse-yellow 2s infinite;
}

.status-indicator.error {
    background: linear-gradient(45deg, #dc3545, #e74c3c);
    animation: pulse-red 2s infinite;
}

.status-indicator.unknown {
    background: linear-gradient(45deg, #6c757d, #adb5bd);
}

@keyframes pulse-green {
    0%, 100% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7); }
    50% { box-shadow: 0 0 0 10px rgba(40, 167, 69, 0); }
}

@keyframes pulse-yellow {
    0%, 100% { box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.7); }
    50% { box-shadow: 0 0 0 10px rgba(255, 193, 7, 0); }
}

@keyframes pulse-red {
    0%, 100% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7); }
    50% { box-shadow: 0 0 0 10px rgba(220, 53, 69, 0); }
}

.status-label {
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 5px;
    font-weight: 500;
}

.status-value {
    font-size: 14px;
    font-weight: 600;
    color: #495057;
}

.eta-actions {
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
    text-align: center;
}

.eta-actions .btn {
    margin: 0 5px;
    font-size: 12px;
    padding: 5px 10px;
}

/* ETA KPI Cards */
.eta-kpi {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    position: relative;
    overflow: hidden;
}

.eta-kpi::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
    transform: rotate(45deg);
    transition: all 0.5s;
    opacity: 0;
}

.eta-kpi:hover::before {
    animation: shine 0.5s ease-in-out;
    opacity: 1;
}

@keyframes shine {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.eta-kpi .kpi-value {
    color: #fff;
    text-shadow: 0 1px 3px rgba(0,0,0,0.3);
}

.eta-kpi .kpi-label {
    color: rgba(255,255,255,0.9);
}

/* Tax Compliance Metrics */
.compliance-metric {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
}

.compliance-metric:hover {
    border-color: #007bff;
    box-shadow: 0 4px 12px rgba(0,123,255,0.15);
    transform: translateY(-2px);
}

.metric-value {
    font-size: 24px;
    font-weight: 700;
    color: #007bff;
    margin-bottom: 8px;
    display: block;
}

.metric-label {
    font-size: 12px;
    color: #6c757d;
    line-height: 1.3;
    font-weight: 500;
}

/* ETA Recent Activity */
.eta-recent-activity {
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
}

.activity-list {
    max-height: 200px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f8f9fa;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    font-size: 14px;
    color: white;
}

.activity-icon.success { background: #28a745; }
.activity-icon.warning { background: #ffc107; }
.activity-icon.error { background: #dc3545; }
.activity-icon.info { background: #17a2b8; }

.activity-content {
    flex: 1;
}

.activity-title {
    font-size: 13px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 2px;
}

.activity-time {
    font-size: 11px;
    color: #6c757d;
}

/* System Health Indicators */
.health-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
    margin: 0 2px;
    transition: all 0.3s ease;
}

.health-indicator.healthy {
    background: #28a745;
    box-shadow: 0 0 6px rgba(40, 167, 69, 0.6);
}

.health-indicator.warning {
    background: #ffc107;
    box-shadow: 0 0 6px rgba(255, 193, 7, 0.6);
}

.health-indicator.error {
    background: #dc3545;
    box-shadow: 0 0 6px rgba(220, 53, 69, 0.6);
}

.health-indicator.unknown {
    background: #6c757d;
}

/* Widget Type Specific Styles */
.dashboard-widget[data-widget-type="eta"] {
    border-left: 4px solid #007bff;
}

.dashboard-widget[data-widget-type="eta"] .widget-header {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
}

.dashboard-widget[data-widget-type="eta"] .widget-header h5 {
    color: white;
}

.dashboard-widget[data-widget-type="eta"] .widget-controls .btn {
    border-color: rgba(255,255,255,0.3);
    color: rgba(255,255,255,0.8);
}

.dashboard-widget[data-widget-type="eta"] .widget-controls .btn:hover {
    background: rgba(255,255,255,0.1);
    color: white;
}

/* Responsive Design for ETA Widgets */
@media (max-width: 768px) {
    .eta-status-card {
        margin-bottom: 15px;
    }
    
    .compliance-metric {
        margin-bottom: 15px;
    }
    
    .eta-actions .btn {
        display: block;
        width: 100%;
        margin: 5px 0;
    }
    
    .metric-value {
        font-size: 20px;
    }
    
    .metric-label {
        font-size: 11px;
    }
}

/* Search Results Modal */
.search-results-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.search-results-content {
    background: white;
    border-radius: 8px;
    padding: 20px;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.search-results-list {
    list-style: none;
    padding: 0;
    margin: 15px 0;
}

.search-results-list li {
    border-bottom: 1px solid #e9ecef;
    padding: 10px 0;
}

.search-results-list li:last-child {
    border-bottom: none;
}

.search-results-list a {
    text-decoration: none;
    color: #495057;
    display: block;
}

.search-results-list a:hover {
    color: #007bff;
}

.search-results-list small {
    display: block;
    color: #6c757d;
    margin-top: 5px;
}
</style>

<script>
// Enhanced Dashboard JavaScript with RTL/LTR Support
document.addEventListener('DOMContentLoaded', function() {
    // Initialize dashboard
    initializeDashboard();

    // Setup event handlers
    setupEventHandlers();

    // Initialize tooltips
    initializeTooltips();

    // Setup auto-refresh
    setupAutoRefresh();

    // Initialize filters
    initializeFilters();
});

function initializeDashboard() {
    // Set direction attribute
    const direction = '{{ direction }}';
    document.querySelector('.dashboard-container').setAttribute('dir', direction);

    // Initialize widgets system
    if (typeof DashboardWidgets !== 'undefined') {
        window.dashboardWidgets = new DashboardWidgets();
    }

    // Show loading state initially
    showLoadingState();

    // Load initial data
    setTimeout(() => {
        hideLoadingState();
        showSuccessMessage('{{ text_data_updated_successfully }}');
    }, 1000);
}

function setupEventHandlers() {
    // Refresh dashboard
    const refreshBtn = document.getElementById('refresh-dashboard');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', function() {
            refreshDashboard();
        });
    }

    // Export dashboard
    const exportBtn = document.getElementById('export-dashboard');
    if (exportBtn) {
        exportBtn.addEventListener('click', function() {
            exportDashboard();
        });
    }

    // Print dashboard
    const printBtn = document.getElementById('print-dashboard');
    if (printBtn) {
        printBtn.addEventListener('click', function() {
            printDashboard();
        });
    }

    // Settings dashboard
    const settingsBtn = document.getElementById('settings-dashboard');
    if (settingsBtn) {
        settingsBtn.addEventListener('click', function() {
            showDashboardSettings();
        });
    }

    // Toggle filters
    const toggleFiltersBtn = document.getElementById('toggle-filters');
    if (toggleFiltersBtn) {
        toggleFiltersBtn.addEventListener('click', function() {
            toggleFilters();
        });
    }

    // Form validation
    const filtersForm = document.getElementById('dashboard-filters');
    if (filtersForm) {
        filtersForm.addEventListener('submit', function(e) {
            if (!validateFiltersForm()) {
                e.preventDefault();
            }
        });
    }
}

function initializeTooltips() {
    // Initialize Bootstrap tooltips
    if (typeof $ !== 'undefined' && $.fn.tooltip) {
        $('[data-toggle="tooltip"]').tooltip();
    }
}

function setupAutoRefresh() {
    // Auto-refresh every 5 minutes
    setInterval(() => {
        if (window.dashboardWidgets) {
            window.dashboardWidgets.loadAllWidgetData();
        }
    }, 300000); // 5 minutes
}

function initializeFilters() {
    // Set default date range if empty
    const dateFrom = document.querySelector('input[name="date_from"]');
    const dateTo = document.querySelector('input[name="date_to"]');

    if (dateFrom && !dateFrom.value) {
        const today = new Date();
        const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
        dateFrom.value = firstDay.toISOString().split('T')[0];
    }

    if (dateTo && !dateTo.value) {
        const today = new Date();
        dateTo.value = today.toISOString().split('T')[0];
    }
}

function refreshDashboard() {
    showLoadingState();

    // AJAX request to refresh data
    fetch('index.php?route=common/dashboard/refresh&user_token={{ user_token }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        hideLoadingState();
        updateDashboardData(data);
        showSuccessMessage('{{ text_data_updated_successfully }}');
        updateLastRefreshTime();
    })
    .catch(error => {
        hideLoadingState();
        showErrorMessage('{{ text_error_updating_data }}');
        console.error('Dashboard refresh error:', error);
    });
}

function exportDashboard() {
    showLoadingState();

    // Create export data
    const exportData = {
        filters: getFiltersData(),
        timestamp: new Date().toISOString(),
        user_token: '{{ user_token }}'
    };

    // Simulate export process
    setTimeout(() => {
        hideLoadingState();
        showInfoMessage('{{ text_export_feature }}');
    }, 1500);
}

function printDashboard() {
    // Hide non-printable elements
    const nonPrintable = document.querySelectorAll('.no-print, .dashboard-actions');
    nonPrintable.forEach(el => el.style.display = 'none');

    // Print
    window.print();

    // Restore elements
    nonPrintable.forEach(el => el.style.display = '');
}

function showDashboardSettings() {
    // Create settings modal dynamically
    const modal = createSettingsModal();
    document.body.appendChild(modal);
    modal.style.display = 'flex';
}

function toggleFilters() {
    const filtersBody = document.getElementById('filters-body');
    const toggleBtn = document.getElementById('toggle-filters');
    const icon = toggleBtn.querySelector('i');

    if (filtersBody.style.display === 'none') {
        filtersBody.style.display = 'block';
        icon.className = 'fa fa-chevron-up';
    } else {
        filtersBody.style.display = 'none';
        icon.className = 'fa fa-chevron-down';
    }
}

function validateFiltersForm() {
    const dateFrom = document.querySelector('input[name="date_from"]').value;
    const dateTo = document.querySelector('input[name="date_to"]').value;

    if (dateFrom && dateTo && new Date(dateFrom) > new Date(dateTo)) {
        showErrorMessage('{{ text_invalid_date_range|default("نطاق التاريخ غير صحيح") }}');
        return false;
    }

    return true;
}

function showLoadingState() {
    const content = document.getElementById('content');
    content.classList.add('loading');
}

function hideLoadingState() {
    const content = document.getElementById('content');
    content.classList.remove('loading');
}

function showSuccessMessage(message) {
    showNotification(message, 'success');
}

function showErrorMessage(message) {
    showNotification(message, 'error');
}

function showInfoMessage(message) {
    showNotification(message, 'info');
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="close" onclick="this.parentElement.remove()">
            <span>&times;</span>
        </button>
    `;

    document.body.appendChild(notification);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

function updateDashboardData(data) {
    // Update widgets with new data
    if (window.dashboardWidgets && data) {
        // Check if KPI data exists before updating
        if (data.kpi_data) {
            window.dashboardWidgets.updateWidgets(data);
        } else {
            // If KPI data is missing, you can choose to hide the KPI sections
            // or simply not update them. For now, we'll just log it.
            console.log('KPI data not found, skipping update.');
        }
    }
}

function updateLastRefreshTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('{{ language_code }}');
    const lastUpdateElements = document.querySelectorAll('.last-update-time');

    lastUpdateElements.forEach(el => {
        el.textContent = '{{ text_last_update }}' + timeString;
    });
}

function getFiltersData() {
    const form = document.getElementById('dashboard-filters');
    const formData = new FormData(form);
    const data = {};

    for (let [key, value] of formData.entries()) {
        data[key] = value;
    }

    return data;
}

function createSettingsModal() {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.style.cssText = 'position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); display: flex; align-items: center; justify-content: center; z-index: 9999;';

    modal.innerHTML = `
        <div class="modal-content bg-white rounded p-4" style="max-width: 500px; width: 90%;">
            <h5>{{ text_dashboard_settings }}</h5>
            <hr>
            <div class="form-group">
                <label>{{ text_auto_refresh }}</label>
                <select class="form-control" id="auto-refresh-interval">
                    <option value="0">{{ text_disabled|default("معطل") }}</option>
                    <option value="60">1 {{ text_minute|default("دقيقة") }}</option>
                    <option value="300" selected>5 {{ text_minutes|default("دقائق") }}</option>
                    <option value="600">10 {{ text_minutes|default("دقائق") }}</option>
                </select>
            </div>
            <div class="form-group">
                <label>{{ text_theme|default("المظهر") }}</label>
                <select class="form-control" id="dashboard-theme">
                    <option value="light">{{ text_light|default("فاتح") }}</option>
                    <option value="dark">{{ text_dark|default("داكن") }}</option>
                </select>
            </div>
            <hr>
            <div class="text-right">
                <button type="button" class="btn btn-secondary" onclick="this.closest('.modal-overlay').remove()">
                    {{ text_cancel }}
                </button>
                <button type="button" class="btn btn-primary ml-2" onclick="saveDashboardSettings()">
                    {{ text_save }}
                </button>
            </div>
        </div>
    `;

    return modal;
}

function saveDashboardSettings() {
    // Save settings logic here
    showSuccessMessage('{{ text_settings_saved }}');
    document.querySelector('.modal-overlay').remove();
}

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Escape key to close modals
    if (e.key === 'Escape') {
        const modals = document.querySelectorAll('.modal-overlay');
        modals.forEach(modal => modal.remove());
    }

    // Ctrl/Cmd + R to refresh
    if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
        e.preventDefault();
        refreshDashboard();
    }

    // F5 to refresh
    if (e.key === 'F5') {
        e.preventDefault();
        refreshDashboard();
    }
});

// Handle window resize
window.addEventListener('resize', function() {
    // Adjust layout for mobile
    if (window.innerWidth < 768) {
        document.body.classList.add('mobile-view');
    } else {
        document.body.classList.remove('mobile-view');
    }
});

// ═══════════════════════════════════════════════════════════════════════════════
// ACCESSIBILITY ENHANCEMENTS
// ═══════════════════════════════════════════════════════════════════════════════

// Keyboard Navigation Enhancement
document.addEventListener('keydown', function(e) {
    // Escape key to close modals
    if (e.key === 'Escape') {
        const modals = document.querySelectorAll('.modal-overlay');
        modals.forEach(modal => modal.remove());
    }

    // Tab trap for modals
    if (e.key === 'Tab') {
        const modal = document.querySelector('.modal-overlay');
        if (modal) {
            const focusableElements = modal.querySelectorAll(
                'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
            );
            const firstElement = focusableElements[0];
            const lastElement = focusableElements[focusableElements.length - 1];

            if (e.shiftKey && document.activeElement === firstElement) {
                e.preventDefault();
                lastElement.focus();
            } else if (!e.shiftKey && document.activeElement === lastElement) {
                e.preventDefault();
                firstElement.focus();
            }
        }
    }
});

// Screen Reader Announcements
function announceToScreenReader(message) {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;

    document.body.appendChild(announcement);

    setTimeout(() => {
        document.body.removeChild(announcement);
    }, 1000);
}

// Performance Monitoring
const performanceObserver = new PerformanceObserver((list) => {
    const entries = list.getEntries();
    entries.forEach((entry) => {
        if (entry.entryType === 'navigation') {
            console.log('Page Load Time:', entry.loadEventEnd - entry.loadEventStart, 'ms');
        }
        if (entry.entryType === 'measure') {
            console.log('Custom Measure:', entry.name, entry.duration, 'ms');
        }
    });
});

if ('PerformanceObserver' in window) {
    performanceObserver.observe({ entryTypes: ['navigation', 'measure'] });
}

// Error Boundary for JavaScript Errors
window.addEventListener('error', function(e) {
    console.error('Dashboard Error:', e.error);
    announceToScreenReader('{{ text_error_occurred|default("حدث خطأ في النظام") }}');
});

// Unhandled Promise Rejection Handler
window.addEventListener('unhandledrejection', function(e) {
    console.error('Unhandled Promise Rejection:', e.reason);
    announceToScreenReader('{{ text_error_occurred|default("حدث خطأ في النظام") }}');
});

// Enhanced Auto-refresh with Performance Consideration
let refreshTimer;
let isPageVisible = true;

document.addEventListener('visibilitychange', function() {
    isPageVisible = !document.hidden;

    if (isPageVisible && refreshTimer) {
        // Resume auto-refresh when page becomes visible
        startAutoRefresh();
    } else if (!isPageVisible) {
        // Pause auto-refresh when page is hidden
        clearInterval(refreshTimer);
    }
});

function startAutoRefresh() {
    const interval = parseInt(localStorage.getItem('dashboard_refresh_interval') || '300000');

    if (interval > 0 && isPageVisible) {
        refreshTimer = setInterval(() => {
            if (isPageVisible) {
                refreshDashboard();
            }
        }, interval);
    }
}

// Initialize enhanced features
document.addEventListener('DOMContentLoaded', function() {
    // Start performance monitoring
    performance.mark('dashboard-init-start');

    // Initialize auto-refresh
    startAutoRefresh();

    // Add ARIA labels to interactive elements
    const buttons = document.querySelectorAll('button:not([aria-label])');
    buttons.forEach(button => {
        if (button.textContent.trim()) {
            button.setAttribute('aria-label', button.textContent.trim());
        }
    });

    // Mark initialization complete
    performance.mark('dashboard-init-end');
    performance.measure('dashboard-initialization', 'dashboard-init-start', 'dashboard-init-end');

    announceToScreenReader('{{ text_dashboard_loaded|default("تم تحميل لوحة المعلومات") }}');
});
</script>

{{ footer }}
