{{ header }}{{ column_left }}
<div id="content">
    
<br>   
{% if error_warning %}
  <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
    <button type="button" class="close" data-dismiss="alert">&times;</button>
  </div>
  {% endif %}
  {% if success %}
  <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{ success }}
    <button type="button" class="close" data-dismiss="alert">&times;</button>
  </div>
  {% endif %}
  
  <div class="container-fluid"> 
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-magic"></i> {{ text_form }}</h3>
      </div>
      <div class="panel-body">
        <form action="{{action}}" method="post" enctype="multipart/form-data" id="form-product" class="form-horizontal">
              <div class="form-group required">
                <label class="col-sm-2 control-label" for="input-order_id">رقم الطلب</label>
                <div class="col-sm-10">
                  <input type="text" name="order_id" value="" placeholder="" id="input-order_id" class="form-control" required/>
                   {% if error_order_id %}
                    <div class="text-danger">{{ error_order_id }}</div>
                  {% endif %}                 
                </div>
              </div>
              <div class="form-group required">
                <label class="col-sm-2 control-label" for="input-payment_method">العملة</label>
                  <select style="max-width: 80%;margin-inline-start:15px;" class="col-sm-10 form-control" name="order_currency">
                      <option value="EGP">EGP</option>
                      <option value="USD">USD</option>
                  </select>
              </div>               
              <div class="form-group required">
                <label class="col-sm-2 control-label" for="input-order_total">إجمالي الطلب</label>
                <div class="col-sm-10">
                  <input type="text" name="order_total" value="" placeholder="" id="input-order_total" class="form-control" required/>
                   {% if error_order_total %}
                    <div class="text-danger">{{ error_order_total }}</div>
                  {% endif %}                 
                </div>
              </div>              
              <div class="form-group required">
                <label class="col-sm-2 control-label" for="input-phone">موبايل العميل</label>
                <div class="col-sm-10">
                  <input type="text" name="phone" value="{{ phone }}" placeholder="01xxxxxxxxx" id="input-phone" class="form-control" required/>
                   {% if error_phone %}
                    <div class="text-danger">{{ error_phone }}</div>
                  {% endif %}                                   
                </div>
             </div>     
              <div class="form-group required">
                <label class="col-sm-2 control-label" for="input-email">إيميل العميل</label>
                <div class="col-sm-10">
                  <input type="text" name="email" value="" placeholder="" id="input-email" class="form-control" required/>
                   {% if error_email %}
                    <div class="text-danger">{{ error_email}}</div>
                  {% endif %}                                   
                </div>
              </div> 
    
              
                <div class="form-group required">
                  <label class="col-sm-2 control-label" for="input-order_desc">وصف مختصر</label>
                  <div class="col-sm-10">
                    <textarea name="order_desc" rows="1" id="input-order_desc" class="form-control" required></textarea>
                    
                  </div>
                </div>              
        </form>
      </div>
  <div class="page-footer">
    <div class="container">
        <button style="min-width: 350px;height: 50px;margin: 0 auto;display: block;" type="submit" form="form-product" data-toggle="tooltip" title="توليد رابط الدفع" class="btn btn-primary"> <i class="fa fa-magic"></i>  توليد رابط الدفع  </button>
    </div>
  </div>    
  <br>
    </div>
  </div>
  
  <link href="view/javascript/codemirror/lib/codemirror.css" rel="stylesheet"/>
  <link href="view/javascript/codemirror/theme/monokai.css" rel="stylesheet"/>
  <script type="text/javascript" src="view/javascript/codemirror/lib/codemirror.js"></script>
  <script type="text/javascript" src="view/javascript/codemirror/lib/xml.js"></script>
  <script type="text/javascript" src="view/javascript/codemirror/lib/formatting.js"></script>
  <script type="text/javascript" src="view/javascript/summernote/summernote.js"></script>
  <link href="view/javascript/summernote/summernote.css" rel="stylesheet"/>
  <script type="text/javascript" src="view/javascript/summernote/summernote-image-attributes.js"></script>
  <script type="text/javascript" src="view/javascript/summernote/opencart.js"></script>

  <script type="text/javascript"><!--
  $('.date').datetimepicker({
	  language: '{{ datepicker }}',
	  pickTime: false
  });

  $('.time').datetimepicker({
	  language: '{{ datepicker }}',
	  pickDate: false
  });

  $('.datetime').datetimepicker({
	  language: '{{ datepicker }}',
	  pickDate: true,
	  pickTime: true
  });
  //--></script>

</div>
{{ footer }} 
