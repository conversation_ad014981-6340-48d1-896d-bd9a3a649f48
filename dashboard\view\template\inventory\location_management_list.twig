{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <a href="{{ add }}" data-toggle="tooltip" title="{{ button_add }}" class="btn btn-primary">
          <i class="fa fa-plus"></i>
        </a>
        <a href="{{ location_map }}" data-toggle="tooltip" title="{{ button_location_map }}" class="btn btn-info">
          <i class="fa fa-map-marker"></i>
        </a>
        <a href="{{ barcode_scanner }}" data-toggle="tooltip" title="{{ button_barcode_scanner }}" class="btn btn-warning">
          <i class="fa fa-qrcode"></i>
        </a>
        <a href="{{ usage_report }}" data-toggle="tooltip" title="{{ button_usage_report }}" class="btn btn-success">
          <i class="fa fa-bar-chart"></i>
        </a>
        <button type="button" data-toggle="tooltip" title="{{ button_delete }}" class="btn btn-danger" onclick="confirm('{{ text_confirm }}') ? $('#form-location').submit() : false;">
          <i class="fa fa-trash-o"></i>
        </button>
        <a href="{{ refresh }}" data-toggle="tooltip" title="{{ button_refresh }}" class="btn btn-default">
          <i class="fa fa-refresh"></i>
        </a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible">
      <i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    
    {% if success %}
    <div class="alert alert-success alert-dismissible">
      <i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    
    <!-- إحصائيات المواقع -->
    <div class="row">
      <div class="col-lg-2 col-md-3 col-sm-6">
        <div class="panel panel-primary">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-map-marker fa-2x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ statistics.total_locations }}</div>
                <div>{{ text_total_locations }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-lg-2 col-md-3 col-sm-6">
        <div class="panel panel-success">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-check-circle fa-2x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ statistics.active_locations }}</div>
                <div>{{ text_active_locations }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-lg-2 col-md-3 col-sm-6">
        <div class="panel panel-info">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-cubes fa-2x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ statistics.products_with_locations }}</div>
                <div>{{ text_products_with_locations }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-lg-2 col-md-3 col-sm-6">
        <div class="panel panel-warning">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-pie-chart fa-2x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ statistics.overall_usage_percentage }}%</div>
                <div>{{ text_overall_usage_percentage }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-lg-2 col-md-3 col-sm-6">
        <div class="panel panel-green">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-building fa-2x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ statistics.warehouses_with_locations }}</div>
                <div>{{ text_warehouses_with_locations }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-lg-2 col-md-3 col-sm-6">
        <div class="panel panel-purple">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-trophy fa-2x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge-text">{{ statistics.most_used_location }}</div>
                <div>{{ text_most_used_location }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- الفلاتر المتقدمة -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title">
          <i class="fa fa-filter"></i> فلاتر البحث المتقدم
          <button type="button" class="btn btn-xs btn-default pull-right" data-toggle="collapse" data-target="#filter-panel">
            <i class="fa fa-chevron-down"></i>
          </button>
        </h3>
      </div>
      <div id="filter-panel" class="panel-collapse collapse">
        <div class="panel-body">
          <form method="get" id="filter-form">
            <input type="hidden" name="route" value="inventory/location_management" />
            <input type="hidden" name="user_token" value="{{ user_token }}" />
            
            <div class="row">
              <div class="col-md-3">
                <div class="form-group">
                  <label for="filter_name">{{ entry_filter_name }}</label>
                  <input type="text" name="filter_name" value="{{ filter_name }}" placeholder="{{ entry_filter_name }}" id="filter_name" class="form-control" />
                </div>
              </div>
              
              <div class="col-md-3">
                <div class="form-group">
                  <label for="filter_location_code">{{ entry_filter_location_code }}</label>
                  <input type="text" name="filter_location_code" value="{{ filter_location_code }}" placeholder="{{ entry_filter_location_code }}" id="filter_location_code" class="form-control" />
                </div>
              </div>
              
              <div class="col-md-3">
                <div class="form-group">
                  <label for="filter_location_type">{{ entry_filter_location_type }}</label>
                  <select name="filter_location_type" id="filter_location_type" class="form-control">
                    <option value="">{{ text_all }}</option>
                    {% for type_key, type_name in location_types %}
                    <option value="{{ type_key }}"{% if type_key == filter_location_type %} selected="selected"{% endif %}>{{ type_name }}</option>
                    {% endfor %}
                  </select>
                </div>
              </div>
              
              <div class="col-md-3">
                <div class="form-group">
                  <label for="filter_branch_id">{{ entry_filter_branch }}</label>
                  <select name="filter_branch_id" id="filter_branch_id" class="form-control">
                    <option value="">{{ text_all }}</option>
                    {% for branch in branches %}
                    <option value="{{ branch.branch_id }}"{% if branch.branch_id == filter_branch_id %} selected="selected"{% endif %}>{{ branch.name }}</option>
                    {% endfor %}
                  </select>
                </div>
              </div>
            </div>
            
            <div class="row">
              <div class="col-md-3">
                <div class="form-group">
                  <label for="filter_warehouse_id">{{ entry_filter_warehouse }}</label>
                  <select name="filter_warehouse_id" id="filter_warehouse_id" class="form-control">
                    <option value="">{{ text_all }}</option>
                    {% for warehouse in warehouses %}
                    <option value="{{ warehouse.warehouse_id }}"{% if warehouse.warehouse_id == filter_warehouse_id %} selected="selected"{% endif %}>{{ warehouse.name }}</option>
                    {% endfor %}
                  </select>
                </div>
              </div>
              
              <div class="col-md-3">
                <div class="form-group">
                  <label for="filter_occupancy_status">{{ entry_filter_occupancy_status }}</label>
                  <select name="filter_occupancy_status" id="filter_occupancy_status" class="form-control">
                    {% for option in occupancy_options %}
                    <option value="{{ option.value }}"{% if option.value == filter_occupancy_status %} selected="selected"{% endif %}>{{ option.text }}</option>
                    {% endfor %}
                  </select>
                </div>
              </div>
              
              <div class="col-md-3">
                <div class="form-group">
                  <label for="filter_is_active">{{ entry_filter_is_active }}</label>
                  <select name="filter_is_active" id="filter_is_active" class="form-control">
                    {% for option in status_options %}
                    <option value="{{ option.value }}"{% if option.value == filter_is_active %} selected="selected"{% endif %}>{{ option.text }}</option>
                    {% endfor %}
                  </select>
                </div>
              </div>
              
              <div class="col-md-3">
                <div class="form-group">
                  <label>&nbsp;</label>
                  <div>
                    <button type="submit" class="btn btn-primary"><i class="fa fa-filter"></i> {{ button_filter }}</button>
                    <a href="{{ refresh }}" class="btn btn-default"><i class="fa fa-refresh"></i> {{ button_clear }}</a>
                  </div>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
    
    <!-- جدول المواقع -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-list"></i> {{ text_list }}</h3>
      </div>
      <div class="panel-body">
        <form action="{{ delete }}" method="post" enctype="multipart/form-data" id="form-location">
          <div class="table-responsive">
            <table class="table table-bordered table-hover">
              <thead>
                <tr>
                  <td style="width: 1px;" class="text-center">
                    <input type="checkbox" onclick="$('input[name*=\'selected\']').prop('checked', this.checked);" />
                  </td>
                  <td>{{ column_name }}</td>
                  <td class="text-center">{{ column_location_code }}</td>
                  <td>{{ column_location_type }}</td>
                  <td>{{ column_full_address }}</td>
                  <td class="text-center">{{ column_capacity }}</td>
                  <td class="text-center">{{ column_usage_percentage }}</td>
                  <td class="text-center">{{ column_occupancy_status }}</td>
                  <td class="text-center">{{ column_products_count }}</td>
                  <td class="text-center">{{ column_gps_coordinates }}</td>
                  <td class="text-center">{{ column_status }}</td>
                  <td class="text-center">{{ column_action }}</td>
                </tr>
              </thead>
              <tbody>
                {% if locations %}
                {% for location in locations %}
                <tr>
                  <td class="text-center">
                    <input type="checkbox" name="selected[]" value="{{ location.location_id }}" />
                  </td>
                  <td>
                    <strong>{{ location.name }}</strong>
                    {% if location.description %}
                    <br><small class="text-muted">{{ location.description|slice(0, 50) }}{% if location.description|length > 50 %}...{% endif %}</small>
                    {% endif %}
                  </td>
                  <td class="text-center">
                    <span class="label label-info">{{ location.location_code }}</span>
                  </td>
                  <td>
                    <span class="label label-primary">{{ location.location_type_text }}</span>
                  </td>
                  <td>
                    <small>{{ location.full_address ?: text_none }}</small>
                  </td>
                  <td class="text-center">
                    <div class="capacity-info">
                      <small>
                        <strong>{{ location.current_units }}</strong> / {{ location.capacity_units }}
                        {% if location.capacity_units > 0 %}
                        <div class="progress progress-xs">
                          <div class="progress-bar progress-bar-{{ location.occupancy_status_class }}" style="width: {{ location.usage_percentage }}%"></div>
                        </div>
                        {% endif %}
                      </small>
                    </div>
                  </td>
                  <td class="text-center">
                    <span class="badge badge-{{ location.occupancy_status_class }}">{{ location.usage_percentage }}%</span>
                  </td>
                  <td class="text-center">
                    <span class="label label-{{ location.occupancy_status_class }}">{{ location.occupancy_status_text }}</span>
                  </td>
                  <td class="text-center">
                    <span class="badge badge-primary">{{ location.products_count }}</span>
                  </td>
                  <td class="text-center">
                    {% if location.has_gps %}
                    <button type="button" class="btn btn-xs btn-info" data-toggle="tooltip" title="{{ location.gps_coordinates }}" onclick="showLocationOnMap({{ location.location_id }})">
                      <i class="fa fa-map-marker"></i>
                    </button>
                    {% else %}
                    <span class="text-muted">-</span>
                    {% endif %}
                  </td>
                  <td class="text-center">
                    <span class="label label-{{ location.is_active_class }}">{{ location.is_active_text }}</span>
                  </td>
                  <td class="text-center">
                    <div class="btn-group">
                      <a href="{{ location.view }}" data-toggle="tooltip" title="{{ button_view }}" class="btn btn-info btn-xs">
                        <i class="fa fa-eye"></i>
                      </a>
                      <a href="{{ location.edit }}" data-toggle="tooltip" title="{{ button_edit }}" class="btn btn-primary btn-xs">
                        <i class="fa fa-pencil"></i>
                      </a>
                      <button type="button" data-toggle="tooltip" title="{{ button_generate_qr }}" class="btn btn-warning btn-xs" onclick="generateQRCode({{ location.location_id }})">
                        <i class="fa fa-qrcode"></i>
                      </button>
                      <button type="button" data-toggle="tooltip" title="{{ button_update_quantities }}" class="btn btn-success btn-xs" onclick="updateQuantities({{ location.location_id }})">
                        <i class="fa fa-refresh"></i>
                      </button>
                    </div>
                  </td>
                </tr>
                {% endfor %}
                {% else %}
                <tr>
                  <td class="text-center" colspan="12">{{ text_no_results }}</td>
                </tr>
                {% endif %}
              </tbody>
            </table>
          </div>
        </form>
        
        <div class="row">
          <div class="col-sm-6 text-left">{{ pagination }}</div>
          <div class="col-sm-6 text-right">{{ results }}</div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- نافذة عرض الموقع على الخريطة -->
<div class="modal fade" id="map-modal" tabindex="-1" role="dialog">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title"><i class="fa fa-map-marker"></i> موقع على الخريطة</h4>
      </div>
      <div class="modal-body">
        <div id="location-map" style="height: 400px;"></div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">إغلاق</button>
      </div>
    </div>
  </div>
</div>

<!-- نافذة QR Code -->
<div class="modal fade" id="qr-modal" tabindex="-1" role="dialog">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title"><i class="fa fa-qrcode"></i> QR Code للموقع</h4>
      </div>
      <div class="modal-body text-center">
        <div id="qr-code-content">
          <div class="text-center">
            <i class="fa fa-spinner fa-spin fa-2x"></i>
            <p>{{ text_loading }}</p>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-primary" onclick="printQRCode()">طباعة</button>
        <button type="button" class="btn btn-default" data-dismiss="modal">إغلاق</button>
      </div>
    </div>
  </div>
</div>

<style>
.huge { font-size: 28px; font-weight: bold; }
.huge-text { font-size: 14px; font-weight: bold; }
.panel-green { border-color: #5cb85c; }
.panel-green > .panel-heading { border-color: #5cb85c; color: white; background-color: #5cb85c; }
.panel-purple { border-color: #9b59b6; }
.panel-purple > .panel-heading { border-color: #9b59b6; color: white; background-color: #9b59b6; }
.badge-primary { background-color: #337ab7; }
.badge-success { background-color: #5cb85c; }
.badge-warning { background-color: #f0ad4e; }
.badge-info { background-color: #5bc0de; }
.badge-danger { background-color: #d9534f; }
.badge-default { background-color: #777; }
.progress-xs { height: 4px; margin-bottom: 2px; }
.capacity-info { font-size: 11px; }
</style>

<script type="text/javascript">
$(document).ready(function() {
    $('[data-toggle="tooltip"]').tooltip();
    
    // تأثيرات بصرية للإحصائيات
    $('.huge').each(function() {
        var $this = $(this);
        var finalValue = parseInt($this.text().replace(/,/g, '').replace('%', ''));
        if (!isNaN(finalValue)) {
            $this.text('0');
            
            $({ counter: 0 }).animate({ counter: finalValue }, {
                duration: 1500,
                easing: 'swing',
                step: function() {
                    var value = Math.ceil(this.counter);
                    if ($this.parent().parent().find('div:last').text().includes('%')) {
                        $this.text(value + '%');
                    } else {
                        $this.text(value.toLocaleString());
                    }
                }
            });
        }
    });
});

function showLocationOnMap(locationId) {
    $('#map-modal').modal('show');
    
    // هنا يمكن تحميل الخريطة باستخدام Google Maps أو OpenStreetMap
    $('#location-map').html('<div class="text-center"><i class="fa fa-spinner fa-spin fa-2x"></i><p>جاري تحميل الخريطة...</p></div>');
    
    // محاكاة تحميل الخريطة
    setTimeout(function() {
        $('#location-map').html('<div class="text-center" style="padding: 150px 0;"><i class="fa fa-map-marker fa-4x text-danger"></i><br><br><h4>موقع المخزن</h4><p>خط العرض: 30.0444, خط الطول: 31.2357</p></div>');
    }, 1000);
}

function generateQRCode(locationId) {
    $('#qr-modal').modal('show');
    
    $.ajax({
        url: 'index.php?route=inventory/location_management/generateQR&user_token={{ user_token }}&location_id=' + locationId,
        type: 'GET',
        dataType: 'json',
        success: function(data) {
            if (data.success) {
                var qrHtml = '<div class="qr-code-display">';
                qrHtml += '<div style="border: 2px solid #333; padding: 20px; display: inline-block; background: white;">';
                qrHtml += '<div style="width: 200px; height: 200px; background: url(\'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgZmlsbD0iIzAwMCIvPjxyZWN0IHg9IjEwIiB5PSIxMCIgd2lkdGg9IjE4MCIgaGVpZ2h0PSIxODAiIGZpbGw9IiNmZmYiLz48L3N2Zz4=\') center center; background-size: contain;"></div>';
                qrHtml += '</div>';
                qrHtml += '<br><br><strong>كود الموقع:</strong> ' + data.qr_code;
                qrHtml += '</div>';
                
                $('#qr-code-content').html(qrHtml);
            } else {
                $('#qr-code-content').html('<div class="alert alert-danger">' + data.error + '</div>');
            }
        },
        error: function() {
            $('#qr-code-content').html('<div class="alert alert-danger">خطأ في إنشاء QR Code</div>');
        }
    });
}

function updateQuantities(locationId) {
    $.ajax({
        url: 'index.php?route=inventory/location_management/updateQuantities&user_token={{ user_token }}&location_id=' + locationId,
        type: 'GET',
        dataType: 'json',
        success: function(data) {
            if (data.success) {
                showNotification(data.message, 'success');
                location.reload();
            } else {
                showNotification(data.error, 'error');
            }
        },
        error: function() {
            showNotification('خطأ في تحديث الكميات', 'error');
        }
    });
}

function printQRCode() {
    var printContent = $('#qr-code-content').html();
    var printWindow = window.open('', '_blank');
    printWindow.document.write('<html><head><title>طباعة QR Code</title></head><body>' + printContent + '</body></html>');
    printWindow.document.close();
    printWindow.print();
}

function showNotification(message, type) {
    var alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    var icon = type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle';
    
    var notification = $('<div class="alert ' + alertClass + ' alert-dismissible" style="position: fixed; top: 70px; right: 20px; z-index: 9999; min-width: 300px;"><i class="fa ' + icon + '"></i> ' + message + '<button type="button" class="close" data-dismiss="alert">&times;</button></div>');
    
    $('body').append(notification);
    
    setTimeout(function() {
        notification.fadeOut(function() { notification.remove(); });
    }, 5000);
}
</script>

{{ footer }}
