<?php
/**
 * نموذج القيود المحاسبية المتقدم والمتكامل
 * يدعم القيود التلقائية والمراجعة والاعتماد والتكامل الكامل مع النظام
 */
class ModelAccountsJournalEntry extends Model {

    /**
     * إضافة قيد محاسبي جديد مع التحقق المتقدم
     */
    public function addJournalEntry($data) {
        try {
            // بدء المعاملة لضمان سلامة البيانات
            $this->db->query("START TRANSACTION");

            // 1. إدخال رأس القيد المحاسبي
            $sql = "INSERT INTO `" . DB_PREFIX . "journals` SET 
                    `thedate` = '" . $this->db->escape($data['journal_date']) . "', 
                    `description` = '" . $this->db->escape($data['description']) . "',
                    `reference` = '" . $this->db->escape($data['reference'] ?? '') . "',
                    `transaction_type` = '" . $this->db->escape($data['transaction_type'] ?? 'general') . "',
                    `created_by` = '" . (int)$this->user->getId() . "', 
                    `date_added` = NOW(),
                    `date_modified` = NOW()";
            
            $this->db->query($sql);
            $journal_id = $this->db->getLastId();

            // 2. إدخال بنود القيد
            $total_debit = 0;
            $total_credit = 0;
            
            foreach ($data['entries'] as $entry) {
                // التحقق من صحة البيانات قبل الإدخال
                if (empty($entry['account_code']) || !isset($entry['is_debit']) || !isset($entry['amount'])) {
                    throw new Exception("بيانات البند غير مكتملة");
                }
                
                // التحقق من وجود الحساب
                $account_query = $this->db->query("SELECT account_id FROM `" . DB_PREFIX . "accounts` WHERE account_code = '" . $this->db->escape($entry['account_code']) . "'");
                if ($account_query->num_rows == 0) {
                    throw new Exception("الحساب غير موجود: " . $entry['account_code']);
                }
                
                $sql = "INSERT INTO `" . DB_PREFIX . "journal_entries` SET 
                        `journal_id` = '" . (int)$journal_id . "', 
                        `account_code` = '" . $this->db->escape($entry['account_code']) . "', 
                        `is_debit` = '" . (int)$entry['is_debit'] . "', 
                        `amount` = '" . (float)$entry['amount'] . "',
                        `description` = '" . $this->db->escape($entry['description'] ?? '') . "',
                        `date_added` = NOW()";
                
                $this->db->query($sql);
                
                // حساب المجاميع للتوازن
                if ($entry['is_debit']) {
                    $total_debit += (float)$entry['amount'];
                } else {
                    $total_credit += (float)$entry['amount'];
                }
            }
            
            // 3. التحقق من توازن القيد
            if (abs($total_debit - $total_credit) > 0.001) { // للتعامل مع فروق الكسور العشرية
                throw new Exception("القيد غير متوازن: مجموع المدين " . $total_debit . " مجموع الدائن " . $total_credit);
            }
            
            // 4. تحديث الأرصدة في حسابات الأستاذ العام إذا لزم الأمر
            $this->updateGeneralLedgerBalances($data['journal_date']);
            
            // 5. تأكيد المعاملة
            $this->db->query("COMMIT");

            return $journal_id;

        } catch (Exception $e) {
            // التراجع عن المعاملة في حالة الخطأ
            $this->db->query("ROLLBACK");
            
            // تسجيل الخطأ
            error_log("Journal Entry Error: " . $e->getMessage());
            
            // رمي الاستثناء مرة أخرى للتعامل معه في الطبقة العليا
            throw new Exception($e->getMessage());
        }
    }

    /**
     * تعديل قيد محاسبي موجود
     */
    public function editJournalEntry($journal_id, $data) {
        // التحقق من إمكانية التعديل
        $journal = $this->getJournalEntry($journal_id);
        if (!$journal) {
            throw new Exception('القيد غير موجود');
        }

        if ($journal['audited'] == 1 && !$this->user->hasPermission('modify', 'accounts/posted_entries')) {
            throw new Exception('لا يمكن تعديل قيد مرحل');
        }

        // التحقق المتقدم من صحة البيانات
        $validation = $this->validateJournalEntryAdvanced($data);
        if (!$validation['valid']) {
            throw new Exception($validation['error']);
        }

        $this->db->query("START TRANSACTION");

        try {
            // حفظ القيم القديمة للتدقيق
            $old_values = $journal;

            // تحديث القيد الرئيسي
            $this->db->query("UPDATE " . DB_PREFIX . "journals SET
                refnum = '" . $this->db->escape($data['refnum']) . "',
                thedate = '" . $this->db->escape($data['thedate']) . "',
                description = '" . $this->db->escape($data['description']) . "',
                last_edit_by = '" . $this->db->escape($this->user->getUsername()) . "',
                is_cancelled = '" . (int)(isset($data['is_cancelled']) ? $data['is_cancelled'] : 0) . "',
                audited = '" . (int)(isset($data['audited']) ? $data['audited'] : 0) . "',
                entrytype = '" . (int)(isset($data['entrytype']) ? $data['entrytype'] : 1) . "',
                updated_at = NOW()
                WHERE journal_id = '" . (int)$journal_id . "'");

            // حذف البنود القديمة
            $this->db->query("DELETE FROM " . DB_PREFIX . "journal_entries WHERE journal_id = '" . (int)$journal_id . "'");

            // إدراج البنود الجديدة
            $line_number = 1;
            foreach ($data['entries'] as $entry) {
                $this->addJournalEntryLine($journal_id, $entry, $line_number);
                $line_number++;
            }

            // إعادة حساب أرصدة الحسابات
            if ($journal['audited'] == 1) {
                $this->reverseAccountBalances($journal_id, $old_values);
            }

            if (($data['audited'] ?? 0) == 1) {
                $this->updateAccountBalances($journal_id);
            }

            $this->db->query("COMMIT");
            $this->cache->delete('journal_entry');

            return true;

        } catch (Exception $e) {
            $this->db->query("ROLLBACK");
            throw $e;
        }
    }

    /**
     * حذف قيد محاسبي
     */
    public function deleteJournalEntry($journal_id) {
        $journal = $this->getJournalEntry($journal_id);
        if (!$journal) {
            throw new Exception('القيد غير موجود');
        }

        if ($journal['audited'] == 1 && !$this->user->hasPermission('modify', 'accounts/posted_entries')) {
            throw new Exception('لا يمكن حذف قيد مرحل');
        }

        $this->db->query("START TRANSACTION");

        try {
            // عكس تأثير القيد على أرصدة الحسابات إذا كان مرحل
            if ($journal['audited'] == 1) {
                $this->reverseAccountBalances($journal_id, $journal);
            }

            // حذف بنود القيد
            $this->db->query("DELETE FROM " . DB_PREFIX . "journal_entries WHERE journal_id = '" . (int)$journal_id . "'");

            // حذف القيد الرئيسي
            $this->db->query("DELETE FROM " . DB_PREFIX . "journals WHERE journal_id = '" . (int)$journal_id . "'");

            $this->db->query("COMMIT");
            $this->cache->delete('journal_entry');

            return true;

        } catch (Exception $e) {
            $this->db->query("ROLLBACK");
            throw $e;
        }
    }

    /**
     * ترحيل قيد محاسبي
     */
    public function postJournalEntry($journal_id) {
        $journal = $this->getJournalEntry($journal_id);
        if (!$journal) {
            throw new Exception('القيد غير موجود');
        }

        if ($journal['audited'] == 1) {
            throw new Exception('القيد مرحل بالفعل');
        }

        if ($journal['is_cancelled'] == 1) {
            throw new Exception('لا يمكن ترحيل قيد ملغي');
        }

        // التحقق من توازن القيد
        if (!$this->validateJournalForPosting($journal_id)) {
            throw new Exception('القيد غير متوازن');
        }

        $this->db->query("START TRANSACTION");

        try {
            // تحديث حالة القيد
            $this->db->query("UPDATE " . DB_PREFIX . "journals SET
                audited = 1,
                audit_date = NOW(),
                audit_by = '" . $this->db->escape($this->user->getUsername()) . "',
                updated_at = NOW()
                WHERE journal_id = '" . (int)$journal_id . "'");

            // تحديث أرصدة الحسابات
            $this->updateAccountBalances($journal_id);

            $this->db->query("COMMIT");
            $this->cache->delete('journal_entry');

            return true;

        } catch (Exception $e) {
            $this->db->query("ROLLBACK");
            throw $e;
        }
    }

    /**
     * إلغاء ترحيل قيد محاسبي
     */
    public function unpostJournalEntry($journal_id) {
        $journal = $this->getJournalEntry($journal_id);
        if (!$journal) {
            throw new Exception('القيد غير موجود');
        }

        if ($journal['audited'] == 0) {
            throw new Exception('القيد غير مرحل');
        }

        if (!$this->user->hasPermission('modify', 'accounts/posted_entries')) {
            throw new Exception('ليس لديك صلاحية لإلغاء ترحيل القيود');
        }

        $this->db->query("START TRANSACTION");

        try {
            // عكس تأثير القيد على أرصدة الحسابات
            $this->reverseAccountBalances($journal_id, $journal);

            // تحديث حالة القيد
            $this->db->query("UPDATE " . DB_PREFIX . "journals SET
                audited = 0,
                audit_date = NULL,
                audit_by = NULL,
                updated_at = NOW()
                WHERE journal_id = '" . (int)$journal_id . "'");

            $this->db->query("COMMIT");
            $this->cache->delete('journal_entry');

            return true;

        } catch (Exception $e) {
            $this->db->query("ROLLBACK");
            throw $e;
        }
    }

    /**
     * الحصول على قيد محاسبي واحد
     */
    public function getJournalEntry($journal_id) {
        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "journals WHERE journal_id = '" . (int)$journal_id . "'");
        return $query->row;
    }

    /**
     * الحصول على بنود القيد المحاسبي
     */
    public function getJournalEntryLines($journal_id) {
        $query = $this->db->query("SELECT je.*, ad.name as account_name
                                   FROM " . DB_PREFIX . "journal_entries je
                                   LEFT JOIN " . DB_PREFIX . "accounts a ON je.account_code = a.account_code
                                   LEFT JOIN " . DB_PREFIX . "account_description ad ON (a.account_id = ad.account_id)
                                   WHERE je.journal_id = '" . (int)$journal_id . "'
                                   AND ad.language_id = '" . (int)$this->config->get('config_language_id') . "'
                                   ORDER BY je.entry_id ASC");
        return $query->rows;
    }

    /**
     * الحصول على قائمة القيود المحاسبية
     */
    public function getJournalEntries($data = array()) {
        $sql = "SELECT j.*, 
                       COALESCE(SUM(CASE WHEN je.is_debit = 1 THEN je.amount ELSE 0 END), 0) as total_debit,
                       COALESCE(SUM(CASE WHEN je.is_debit = 0 THEN je.amount ELSE 0 END), 0) as total_credit
                FROM " . DB_PREFIX . "journals j
                LEFT JOIN " . DB_PREFIX . "journal_entries je ON j.journal_id = je.journal_id";

        $sql .= " WHERE 1=1";

        if (!empty($data['filter_refnum'])) {
            $sql .= " AND j.refnum LIKE '%" . $this->db->escape($data['filter_refnum']) . "%'";
        }

        if (!empty($data['filter_description'])) {
            $sql .= " AND j.description LIKE '%" . $this->db->escape($data['filter_description']) . "%'";
        }

        if (!empty($data['filter_date_from'])) {
            $sql .= " AND j.thedate >= '" . $this->db->escape($data['filter_date_from']) . "'";
        }

        if (!empty($data['filter_date_to'])) {
            $sql .= " AND j.thedate <= '" . $this->db->escape($data['filter_date_to']) . "'";
        }

        if (isset($data['filter_audited'])) {
            $sql .= " AND j.audited = '" . (int)$data['filter_audited'] . "'";
        }

        if (isset($data['filter_cancelled'])) {
            $sql .= " AND j.is_cancelled = '" . (int)$data['filter_cancelled'] . "'";
        }

        $sql .= " GROUP BY j.journal_id";
        $sql .= " ORDER BY j.thedate DESC, j.journal_id DESC";

        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }
            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }
            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }

        $query = $this->db->query($sql);
        return $query->rows;
    }

    /**
     * الحصول على إجمالي عدد القيود المحاسبية
     */
    public function getTotalJournalEntries($data = array()) {
        $sql = "SELECT COUNT(DISTINCT j.journal_id) AS total FROM " . DB_PREFIX . "journals j";

        $sql .= " WHERE 1=1";

        if (!empty($data['filter_refnum'])) {
            $sql .= " AND j.refnum LIKE '%" . $this->db->escape($data['filter_refnum']) . "%'";
        }

        if (!empty($data['filter_description'])) {
            $sql .= " AND j.description LIKE '%" . $this->db->escape($data['filter_description']) . "%'";
        }

        if (!empty($data['filter_date_from'])) {
            $sql .= " AND j.thedate >= '" . $this->db->escape($data['filter_date_from']) . "'";
        }

        if (!empty($data['filter_date_to'])) {
            $sql .= " AND j.thedate <= '" . $this->db->escape($data['filter_date_to']) . "'";
        }

        if (isset($data['filter_audited'])) {
            $sql .= " AND j.audited = '" . (int)$data['filter_audited'] . "'";
        }

        if (isset($data['filter_cancelled'])) {
            $sql .= " AND j.is_cancelled = '" . (int)$data['filter_cancelled'] . "'";
        }

        $query = $this->db->query($sql);
        return $query->row['total'];
    }

    /**
     * إضافة بند قيد محاسبي
     */
    private function addJournalEntryLine($journal_id, $line_data, $line_number) {
        $this->db->query("INSERT INTO " . DB_PREFIX . "journal_entries SET
            journal_id = '" . (int)$journal_id . "',
            account_code = '" . (int)$line_data['account_code'] . "',
            is_debit = '" . (int)$line_data['is_debit'] . "',
            amount = '" . (float)$line_data['amount'] . "'");
    }

    /**
     * التحقق المتقدم من صحة بيانات القيد المحاسبي
     */
    private function validateJournalEntryAdvanced($data) {
        $errors = array();

        // التحقق من وجود التاريخ
        if (empty($data['thedate'])) {
            $errors[] = 'تاريخ القيد مطلوب';
        }

        // التحقق من وجود الوصف
        if (empty($data['description'])) {
            $errors[] = 'وصف القيد مطلوب';
        }

        // التحقق من وجود البنود
        if (empty($data['entries']) || !is_array($data['entries'])) {
            $errors[] = 'يجب إدخال بنود القيد';
        } else {
            $total_debit = 0;
            $total_credit = 0;

            foreach ($data['entries'] as $index => $entry) {
                $line_validation = $this->validateJournalEntryLine($entry, $index + 1);
                if (!$line_validation['valid']) {
                    $errors[] = $line_validation['error'];
                }

                if ($entry['is_debit']) {
                    $total_debit += $entry['amount'];
                } else {
                    $total_credit += $entry['amount'];
                }
            }

            // التحقق من توازن القيد
            if (abs($total_debit - $total_credit) > 0.01) {
                $errors[] = 'القيد غير متوازن - إجمالي المدين: ' . $total_debit . '، إجمالي الدائن: ' . $total_credit;
            }
        }

        return array(
            'valid' => empty($errors),
            'error' => implode(', ', $errors)
        );
    }

    /**
     * التحقق من صحة بند القيد المحاسبي
     */
    private function validateJournalEntryLine($line, $line_number) {
        if (empty($line['account_code'])) {
            return array('valid' => false, 'error' => 'رقم الحساب مطلوب في البند ' . $line_number);
        }

        if (!isset($line['is_debit'])) {
            return array('valid' => false, 'error' => 'نوع البند مطلوب في البند ' . $line_number);
        }

        if (empty($line['amount']) || $line['amount'] <= 0) {
            return array('valid' => false, 'error' => 'المبلغ مطلوب ويجب أن يكون أكبر من صفر في البند ' . $line_number);
        }

        // التحقق من وجود الحساب
        $query = $this->db->query("SELECT COUNT(*) as total FROM " . DB_PREFIX . "accounts WHERE account_code = '" . (int)$line['account_code'] . "'");
        if ($query->row['total'] == 0) {
            return array('valid' => false, 'error' => 'الحساب غير موجود في البند ' . $line_number);
        }

        return array('valid' => true);
    }

    /**
     * حساب إجمالي المدين
     */
    private function calculateTotalDebit($entries) {
        $total = 0;
        foreach ($entries as $entry) {
            if ($entry['is_debit']) {
                $total += $entry['amount'];
            }
        }
        return $total;
    }

    /**
     * حساب إجمالي الدائن
     */
    private function calculateTotalCredit($entries) {
        $total = 0;
        foreach ($entries as $entry) {
            if (!$entry['is_debit']) {
                $total += $entry['amount'];
            }
        }
        return $total;
    }

    /**
     * توليد رقم قيد تلقائي
     */
    private function generateJournalNumber($journal_date) {
        $year = date('Y', strtotime($journal_date));
        $sql = "SELECT MAX(CAST(SUBSTRING(refnum, 6) AS UNSIGNED)) as max_number
                FROM " . DB_PREFIX . "journals 
                WHERE refnum LIKE 'JE-" . $year . "-%'";
        
        $query = $this->db->query($sql);
        $next_number = ($query->row['max_number'] ?? 0) + 1;
        
        return 'JE-' . $year . '-' . str_pad($next_number, 6, '0', STR_PAD_LEFT);
    }

    /**
     * تحديث أرصدة الحسابات
     */
    private function updateAccountBalances($journal_id) {
        $entries = $this->getJournalEntryLines($journal_id);
        
        foreach ($entries as $entry) {
            // تحديث رصيد الحساب في جدول الحسابات
            if ($entry['is_debit']) {
                // زيادة رصيد الحساب المدين
                $this->db->query("UPDATE " . DB_PREFIX . "accounts SET 
                    current_balance = current_balance + '" . (float)$entry['amount'] . "'
                    WHERE account_code = '" . (int)$entry['account_code'] . "'");
            } else {
                // زيادة رصيد الحساب الدائن
                $this->db->query("UPDATE " . DB_PREFIX . "accounts SET 
                    current_balance = current_balance + '" . (float)$entry['amount'] . "'
                    WHERE account_code = '" . (int)$entry['account_code'] . "'");
            }
        }
    }

    /**
     * عكس تأثير القيد على أرصدة الحسابات
     */
    private function reverseAccountBalances($journal_id, $journal_data) {
        $entries = $this->getJournalEntryLines($journal_id);
        
        foreach ($entries as $entry) {
            // عكس رصيد الحساب
            if ($entry['is_debit']) {
                // تقليل رصيد الحساب المدين
                $this->db->query("UPDATE " . DB_PREFIX . "accounts SET 
                    current_balance = current_balance - '" . (float)$entry['amount'] . "'
                    WHERE account_code = '" . (int)$entry['account_code'] . "'");
            } else {
                // تقليل رصيد الحساب الدائن
                $this->db->query("UPDATE " . DB_PREFIX . "accounts SET 
                    current_balance = current_balance - '" . (float)$entry['amount'] . "'
                    WHERE account_code = '" . (int)$entry['account_code'] . "'");
            }
        }
    }

    /**
     * التحقق من توازن القيد للترحيل
     */
    private function validateJournalForPosting($journal_id) {
        $entries = $this->getJournalEntryLines($journal_id);
        
        $total_debit = 0;
        $total_credit = 0;
        
        foreach ($entries as $entry) {
            if ($entry['is_debit']) {
                $total_debit += $entry['amount'];
            } else {
                $total_credit += $entry['amount'];
            }
        }
        
        return abs($total_debit - $total_credit) < 0.01;
    }

    /**
     * البحث في القيود المحاسبية
     */
    public function searchJournalEntries($search_term, $limit = 10) {
        $sql = "SELECT j.*, 
                       COALESCE(SUM(CASE WHEN je.is_debit = 1 THEN je.amount ELSE 0 END), 0) as total_debit,
                       COALESCE(SUM(CASE WHEN je.is_debit = 0 THEN je.amount ELSE 0 END), 0) as total_credit
                FROM " . DB_PREFIX . "journals j
                LEFT JOIN " . DB_PREFIX . "journal_entries je ON j.journal_id = je.journal_id
                WHERE j.refnum LIKE '%" . $this->db->escape($search_term) . "%'
                OR j.description LIKE '%" . $this->db->escape($search_term) . "%'
                GROUP BY j.journal_id
                ORDER BY j.thedate DESC
                LIMIT " . (int)$limit;

        $query = $this->db->query($sql);
        return $query->rows;
    }

    // تحسين القيود المحاسبية مع التخزين المؤقت
    public function getOptimizedJournalEntries($filter_data) {
        $cache_key = 'journal_entries_' . md5(serialize($filter_data));

        $cached_result = $this->cache->get($cache_key);
        if ($cached_result !== false) {
            return $cached_result;
        }

        $result = $this->getJournalEntries($filter_data);
        $this->cache->set($cache_key, $result, 600);

        return $result;
    }

    // تحليل متقدم للقيود المحاسبية
    public function getAdvancedJournalAnalysis($filter_data) {
        $cache_key = 'journal_analysis_' . md5(serialize($filter_data));

        $cached_result = $this->cache->get($cache_key);
        if ($cached_result !== false) {
            return $cached_result;
        }

        $analysis = array();

        // إحصائيات عامة
        $stats_query = $this->db->query("
            SELECT
                COUNT(DISTINCT j.journal_id) as total_journals,
                COUNT(je.journal_entry_id) as total_entries,
                SUM(CASE WHEN je.is_debit = 1 THEN je.amount ELSE 0 END) as total_debits,
                SUM(CASE WHEN je.is_debit = 0 THEN je.amount ELSE 0 END) as total_credits
            FROM " . DB_PREFIX . "journals j
            LEFT JOIN " . DB_PREFIX . "journal_entries je ON j.journal_id = je.journal_id
            WHERE j.status = 'posted'
        ");

        $analysis['statistics'] = $stats_query->row;

        $this->cache->set($cache_key, $analysis, 1800);

        return $analysis;
    }

    // التحقق من صحة البيانات
    private function validateJournalData($data) {
        $errors = array();

        if (empty($data['thedate']) || !$this->validateDate($data['thedate'])) {
            $errors[] = 'Invalid date';
        }

        if (empty($data['description'])) {
            $errors[] = 'Description is required';
        }

        return $errors;
    }

    private function validateDate($date) {
        if (empty($date)) return false;

        $d = DateTime::createFromFormat('Y-m-d', $date);
        return $d && $d->format('Y-m-d') === $date;
    }

    // تحسين قيود اليومية مع التخزين المؤقت
    public function getOptimizedJournalEntries($filter_data = array()) {
        $cache_key = 'journal_entries_' . md5(serialize($filter_data));

        $cached_result = $this->cache->get($cache_key);
        if ($cached_result !== false) {
            return $cached_result;
        }

        $result = $this->getJournalEntries($filter_data);
        $this->cache->set($cache_key, $result, 1800);

        return $result;
    }

    // البحث الذكي في قيود اليومية
    public function getSmartJournalEntrySearch($search_term, $limit = 20) {
        $cache_key = 'journal_entry_search_' . md5($search_term . '_' . $limit);

        $cached_result = $this->cache->get($cache_key);
        if ($cached_result !== false) {
            return $cached_result;
        }

        $search_term = $this->db->escape($search_term);

        $query = $this->db->query("
            SELECT
                j.journal_id,
                j.refnum,
                j.description,
                j.thedate,
                j.status,
                COUNT(je.entry_id) as entry_count,
                SUM(CASE WHEN je.is_debit = 1 THEN je.amount ELSE 0 END) as total_debits,
                SUM(CASE WHEN je.is_debit = 0 THEN je.amount ELSE 0 END) as total_credits
            FROM " . DB_PREFIX . "journals j
            LEFT JOIN " . DB_PREFIX . "journal_entries je ON j.journal_id = je.journal_id
            WHERE (
                j.refnum LIKE '%" . $search_term . "%'
                OR j.description LIKE '%" . $search_term . "%'
                OR je.description LIKE '%" . $search_term . "%'
                OR je.account_code LIKE '%" . $search_term . "%'
            )
            GROUP BY j.journal_id
            ORDER BY j.thedate DESC, j.journal_id DESC
            LIMIT " . (int)$limit
        );

        $results = $query->rows;

        $this->cache->set($cache_key, $results, 1800);

        return $results;
    }

}
