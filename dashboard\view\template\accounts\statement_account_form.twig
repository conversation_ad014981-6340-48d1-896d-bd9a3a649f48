{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="submit" form="accounts\statement_account-form" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fa fa-save"></i></button>
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="close" data-dismiss="alert">&times;</button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="close" data-dismiss="alert">&times;</button>
      </div>
    {{% endif %}}
    
    <div class="panel panel-default">
      <div class="panel-heading"><i class="fa fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}bar-chart{% elif screen_type == 'settings' %}cog{% else %}pencil{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="panel-body">
        <form id="accounts\statement_account-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-account">{{ text_account }}</label>
            <div class="col-sm-10">
              <input type="text" name="account" value="{{ account }}" placeholder="{{ text_account }}" id="input-account" class="form-control" />
              {% if error_account %}
                <div class="invalid-feedback">{{ error_account }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-accounts">{{ text_accounts }}</label>
            <div class="col-sm-10">
              <input type="text" name="accounts" value="{{ accounts }}" placeholder="{{ text_accounts }}" id="input-accounts" class="form-control" />
              {% if error_accounts %}
                <div class="invalid-feedback">{{ error_accounts }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-back">{{ text_back }}</label>
            <div class="col-sm-10">
              <input type="text" name="back" value="{{ back }}" placeholder="{{ text_back }}" id="input-back" class="form-control" />
              {% if error_back %}
                <div class="invalid-feedback">{{ error_back }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-date_end">{{ text_date_end }}</label>
            <div class="col-sm-10">
              <input type="text" name="date_end" value="{{ date_end }}" placeholder="{{ text_date_end }}" id="input-date_end" class="form-control" />
              {% if error_date_end %}
                <div class="invalid-feedback">{{ error_date_end }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-date_end_formatted">{{ text_date_end_formatted }}</label>
            <div class="col-sm-10">
              <input type="text" name="date_end_formatted" value="{{ date_end_formatted }}" placeholder="{{ text_date_end_formatted }}" id="input-date_end_formatted" class="form-control" />
              {% if error_date_end_formatted %}
                <div class="invalid-feedback">{{ error_date_end_formatted }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-date_start">{{ text_date_start }}</label>
            <div class="col-sm-10">
              <input type="text" name="date_start" value="{{ date_start }}" placeholder="{{ text_date_start }}" id="input-date_start" class="form-control" />
              {% if error_date_start %}
                <div class="invalid-feedback">{{ error_date_start }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-date_start_formatted">{{ text_date_start_formatted }}</label>
            <div class="col-sm-10">
              <input type="text" name="date_start_formatted" value="{{ date_start_formatted }}" placeholder="{{ text_date_start_formatted }}" id="input-date_start_formatted" class="form-control" />
              {% if error_date_start_formatted %}
                <div class="invalid-feedback">{{ error_date_start_formatted }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-export_excel">{{ text_export_excel }}</label>
            <div class="col-sm-10">
              <input type="text" name="export_excel" value="{{ export_excel }}" placeholder="{{ text_export_excel }}" id="input-export_excel" class="form-control" />
              {% if error_export_excel %}
                <div class="invalid-feedback">{{ error_export_excel }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-export_pdf">{{ text_export_pdf }}</label>
            <div class="col-sm-10">
              <input type="text" name="export_pdf" value="{{ export_pdf }}" placeholder="{{ text_export_pdf }}" id="input-export_pdf" class="form-control" />
              {% if error_export_pdf %}
                <div class="invalid-feedback">{{ error_export_pdf }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="invalid-feedback">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-print">{{ text_print }}</label>
            <div class="col-sm-10">
              <input type="text" name="print" value="{{ print }}" placeholder="{{ text_print }}" id="input-print" class="form-control" />
              {% if error_print %}
                <div class="invalid-feedback">{{ error_print }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-statement">{{ text_statement }}</label>
            <div class="col-sm-10">
              <input type="text" name="statement" value="{{ statement }}" placeholder="{{ text_statement }}" id="input-statement" class="form-control" />
              {% if error_statement %}
                <div class="invalid-feedback">{{ error_statement }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-view_statement">{{ text_view_statement }}</label>
            <div class="col-sm-10">
              <input type="text" name="view_statement" value="{{ view_statement }}" placeholder="{{ text_view_statement }}" id="input-view_statement" class="form-control" />
              {% if error_view_statement %}
                <div class="invalid-feedback">{{ error_view_statement }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}