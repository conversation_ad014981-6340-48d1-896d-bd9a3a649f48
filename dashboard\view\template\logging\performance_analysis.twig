{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="submit" form="logging\performance-form" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fas fa-save"></i></button>
        <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-light"><i class="fas fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    
    <div class="card">
      <div class="card-header"><i class="fas fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}chart-bar{% elif screen_type == 'settings' %}cog{% else %}pencil-alt{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="card-body">
        <form id="logging\performance-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-back">{{ text_back }}</label>
            <div class="col-sm-10">
              <input type="text" name="back" value="{{ back }}" placeholder="{{ text_back }}" id="input-back" class="form-control" />
              {% if error_back %}
                <div class="invalid-feedback">{{ error_back }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-bottleneck_analysis">{{ text_bottleneck_analysis }}</label>
            <div class="col-sm-10">
              <input type="text" name="bottleneck_analysis" value="{{ bottleneck_analysis }}" placeholder="{{ text_bottleneck_analysis }}" id="input-bottleneck_analysis" class="form-control" />
              {% if error_bottleneck_analysis %}
                <div class="invalid-feedback">{{ error_bottleneck_analysis }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-business_analysis">{{ text_business_analysis }}</label>
            <div class="col-sm-10">
              <input type="text" name="business_analysis" value="{{ business_analysis }}" placeholder="{{ text_business_analysis }}" id="input-business_analysis" class="form-control" />
              {% if error_business_analysis %}
                <div class="invalid-feedback">{{ error_business_analysis }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-critical_operations">{{ text_critical_operations }}</label>
            <div class="col-sm-10">
              <input type="text" name="critical_operations" value="{{ critical_operations }}" placeholder="{{ text_critical_operations }}" id="input-critical_operations" class="form-control" />
              {% if error_critical_operations %}
                <div class="invalid-feedback">{{ error_critical_operations }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-database_performance">{{ text_database_performance }}</label>
            <div class="col-sm-10">
              <input type="text" name="database_performance" value="{{ database_performance }}" placeholder="{{ text_database_performance }}" id="input-database_performance" class="form-control" />
              {% if error_database_performance %}
                <div class="invalid-feedback">{{ error_database_performance }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-detailed_analysis">{{ text_detailed_analysis }}</label>
            <div class="col-sm-10">
              <input type="text" name="detailed_analysis" value="{{ detailed_analysis }}" placeholder="{{ text_detailed_analysis }}" id="input-detailed_analysis" class="form-control" />
              {% if error_detailed_analysis %}
                <div class="invalid-feedback">{{ error_detailed_analysis }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-export_analysis">{{ text_export_analysis }}</label>
            <div class="col-sm-10">
              <input type="text" name="export_analysis" value="{{ export_analysis }}" placeholder="{{ text_export_analysis }}" id="input-export_analysis" class="form-control" />
              {% if error_export_analysis %}
                <div class="invalid-feedback">{{ error_export_analysis }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-get_metrics">{{ text_get_metrics }}</label>
            <div class="col-sm-10">
              <input type="text" name="get_metrics" value="{{ get_metrics }}" placeholder="{{ text_get_metrics }}" id="input-get_metrics" class="form-control" />
              {% if error_get_metrics %}
                <div class="invalid-feedback">{{ error_get_metrics }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="invalid-feedback">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-historical_metrics">{{ text_historical_metrics }}</label>
            <div class="col-sm-10">
              <input type="text" name="historical_metrics" value="{{ historical_metrics }}" placeholder="{{ text_historical_metrics }}" id="input-historical_metrics" class="form-control" />
              {% if error_historical_metrics %}
                <div class="invalid-feedback">{{ error_historical_metrics }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-module_performance">{{ text_module_performance }}</label>
            <div class="col-sm-10">
              <input type="text" name="module_performance" value="{{ module_performance }}" placeholder="{{ text_module_performance }}" id="input-module_performance" class="form-control" />
              {% if error_module_performance %}
                <div class="invalid-feedback">{{ error_module_performance }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-optimization">{{ text_optimization }}</label>
            <div class="col-sm-10">
              <input type="text" name="optimization" value="{{ optimization }}" placeholder="{{ text_optimization }}" id="input-optimization" class="form-control" />
              {% if error_optimization %}
                <div class="invalid-feedback">{{ error_optimization }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-optimization_history">{{ text_optimization_history }}</label>
            <div class="col-sm-10">
              <input type="text" name="optimization_history" value="{{ optimization_history }}" placeholder="{{ text_optimization_history }}" id="input-optimization_history" class="form-control" />
              {% if error_optimization_history %}
                <div class="invalid-feedback">{{ error_optimization_history }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-optimization_options">{{ text_optimization_options }}</label>
            <div class="col-sm-10">
              <input type="text" name="optimization_options" value="{{ optimization_options }}" placeholder="{{ text_optimization_options }}" id="input-optimization_options" class="form-control" />
              {% if error_optimization_options %}
                <div class="invalid-feedback">{{ error_optimization_options }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-optimization_recommendations">{{ text_optimization_recommendations }}</label>
            <div class="col-sm-10">
              <input type="text" name="optimization_recommendations" value="{{ optimization_recommendations }}" placeholder="{{ text_optimization_recommendations }}" id="input-optimization_recommendations" class="form-control" />
              {% if error_optimization_recommendations %}
                <div class="invalid-feedback">{{ error_optimization_recommendations }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-performance_alerts">{{ text_performance_alerts }}</label>
            <div class="col-sm-10">
              <input type="text" name="performance_alerts" value="{{ performance_alerts }}" placeholder="{{ text_performance_alerts }}" id="input-performance_alerts" class="form-control" />
              {% if error_performance_alerts %}
                <div class="invalid-feedback">{{ error_performance_alerts }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-performance_analysis">{{ text_performance_analysis }}</label>
            <div class="col-sm-10">
              <input type="text" name="performance_analysis" value="{{ performance_analysis }}" placeholder="{{ text_performance_analysis }}" id="input-performance_analysis" class="form-control" />
              {% if error_performance_analysis %}
                <div class="invalid-feedback">{{ error_performance_analysis }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-performance_overview">{{ text_performance_overview }}</label>
            <div class="col-sm-10">
              <input type="text" name="performance_overview" value="{{ performance_overview }}" placeholder="{{ text_performance_overview }}" id="input-performance_overview" class="form-control" />
              {% if error_performance_overview %}
                <div class="invalid-feedback">{{ error_performance_overview }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-performance_predictions">{{ text_performance_predictions }}</label>
            <div class="col-sm-10">
              <input type="text" name="performance_predictions" value="{{ performance_predictions }}" placeholder="{{ text_performance_predictions }}" id="input-performance_predictions" class="form-control" />
              {% if error_performance_predictions %}
                <div class="invalid-feedback">{{ error_performance_predictions }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-real_time">{{ text_real_time }}</label>
            <div class="col-sm-10">
              <input type="text" name="real_time" value="{{ real_time }}" placeholder="{{ text_real_time }}" id="input-real_time" class="form-control" />
              {% if error_real_time %}
                <div class="invalid-feedback">{{ error_real_time }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-realtime_config">{{ text_realtime_config }}</label>
            <div class="col-sm-10">
              <input type="text" name="realtime_config" value="{{ realtime_config }}" placeholder="{{ text_realtime_config }}" id="input-realtime_config" class="form-control" />
              {% if error_realtime_config %}
                <div class="invalid-feedback">{{ error_realtime_config }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-reports">{{ text_reports }}</label>
            <div class="col-sm-10">
              <input type="text" name="reports" value="{{ reports }}" placeholder="{{ text_reports }}" id="input-reports" class="form-control" />
              {% if error_reports %}
                <div class="invalid-feedback">{{ error_reports }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-settings">{{ text_settings }}</label>
            <div class="col-sm-10">
              <input type="text" name="settings" value="{{ settings }}" placeholder="{{ text_settings }}" id="input-settings" class="form-control" />
              {% if error_settings %}
                <div class="invalid-feedback">{{ error_settings }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="invalid-feedback">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-websocket_config">{{ text_websocket_config }}</label>
            <div class="col-sm-10">
              <input type="text" name="websocket_config" value="{{ websocket_config }}" placeholder="{{ text_websocket_config }}" id="input-websocket_config" class="form-control" />
              {% if error_websocket_config %}
                <div class="invalid-feedback">{{ error_websocket_config }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

<script type="text/javascript"><!--
$('#logging\performance-form').on('submit', function(e) {
    e.preventDefault();
    
    var element = this;
    
    $.ajax({
        url: $(element).attr('action'),
        type: 'post',
        data: $(element).serialize(),
        dataType: 'json',
        beforeSend: function() {
            $(element).find('button[type=\'submit\']').prop('disabled', true).addClass('loading');
        },
        complete: function() {
            $(element).find('button[type=\'submit\']').prop('disabled', false).removeClass('loading');
        },
        success: function(json) {
            $('.alert-dismissible').remove();
            $(element).find('.is-invalid').removeClass('is-invalid');
            $(element).find('.invalid-feedback').removeClass('d-block');
            
            if (json['error']) {
                if (json['error']['warning']) {
                    $('#alert').prepend('<div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> ' + json['error']['warning'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
                }
                
                for (key in json['error']) {
                    $('#input-' + key.replaceAll('_', '-')).addClass('is-invalid').next().html(json['error'][key]).addClass('d-block');
                }
            }
            
            if (json['success']) {
                $('#alert').prepend('<div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> ' + json['success'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
                
                // Refresh or redirect
                if (json['redirect']) {
                    location = json['redirect'];
                }
            }
        },
        error: function(xhr, ajaxOptions, thrownError) {
            console.error(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
    });
});

// AJAX action for realtime
$('body').on('click', '.btn-realtime', function(e) {
    e.preventDefault();
    
    var element = this;
    var id = $(element).data('id');
    
    $.ajax({
        url: 'index.php?route=logging\performance/realtime&user_token={{ user_token }}',
        type: 'post',
        data: {
            'id': id
        },
        dataType: 'json',
        beforeSend: function() {
            $(element).prop('disabled', true);
        },
        complete: function() {
            $(element).prop('disabled', false);
        },
        success: function(json) {
            $('.alert-dismissible').remove();
            
            if (json['error']) {
                $('#alert').prepend('<div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> ' + json['error'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
            }
            
            if (json['success']) {
                $('#alert').prepend('<div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> ' + json['success'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
                
                // Refresh the table
                $('#button-refresh').trigger('click');
            }
        },
        error: function(xhr, ajaxOptions, thrownError) {
            console.error(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
    });
});

// AJAX action for getMetrics
$('body').on('click', '.btn-getMetrics', function(e) {
    e.preventDefault();
    
    var element = this;
    var id = $(element).data('id');
    
    $.ajax({
        url: 'index.php?route=logging\performance/getMetrics&user_token={{ user_token }}',
        type: 'post',
        data: {
            'id': id
        },
        dataType: 'json',
        beforeSend: function() {
            $(element).prop('disabled', true);
        },
        complete: function() {
            $(element).prop('disabled', false);
        },
        success: function(json) {
            $('.alert-dismissible').remove();
            
            if (json['error']) {
                $('#alert').prepend('<div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> ' + json['error'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
            }
            
            if (json['success']) {
                $('#alert').prepend('<div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> ' + json['success'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
                
                // Refresh the table
                $('#button-refresh').trigger('click');
            }
        },
        error: function(xhr, ajaxOptions, thrownError) {
            console.error(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
    });
});

// AJAX action for detailed_analysis
$('body').on('click', '.btn-detailed_analysis', function(e) {
    e.preventDefault();
    
    var element = this;
    var id = $(element).data('id');
    
    $.ajax({
        url: 'index.php?route=logging\performance/detailed_analysis&user_token={{ user_token }}',
        type: 'post',
        data: {
            'id': id
        },
        dataType: 'json',
        beforeSend: function() {
            $(element).prop('disabled', true);
        },
        complete: function() {
            $(element).prop('disabled', false);
        },
        success: function(json) {
            $('.alert-dismissible').remove();
            
            if (json['error']) {
                $('#alert').prepend('<div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> ' + json['error'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
            }
            
            if (json['success']) {
                $('#alert').prepend('<div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> ' + json['success'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
                
                // Refresh the table
                $('#button-refresh').trigger('click');
            }
        },
        error: function(xhr, ajaxOptions, thrownError) {
            console.error(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
    });
});

// AJAX action for optimization
$('body').on('click', '.btn-optimization', function(e) {
    e.preventDefault();
    
    var element = this;
    var id = $(element).data('id');
    
    $.ajax({
        url: 'index.php?route=logging\performance/optimization&user_token={{ user_token }}',
        type: 'post',
        data: {
            'id': id
        },
        dataType: 'json',
        beforeSend: function() {
            $(element).prop('disabled', true);
        },
        complete: function() {
            $(element).prop('disabled', false);
        },
        success: function(json) {
            $('.alert-dismissible').remove();
            
            if (json['error']) {
                $('#alert').prepend('<div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> ' + json['error'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
            }
            
            if (json['success']) {
                $('#alert').prepend('<div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> ' + json['success'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
                
                // Refresh the table
                $('#button-refresh').trigger('click');
            }
        },
        error: function(xhr, ajaxOptions, thrownError) {
            console.error(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
    });
});

// AJAX action for logPerformanceMetric
$('body').on('click', '.btn-logPerformanceMetric', function(e) {
    e.preventDefault();
    
    var element = this;
    var id = $(element).data('id');
    
    $.ajax({
        url: 'index.php?route=logging\performance/logPerformanceMetric&user_token={{ user_token }}',
        type: 'post',
        data: {
            'id': id
        },
        dataType: 'json',
        beforeSend: function() {
            $(element).prop('disabled', true);
        },
        complete: function() {
            $(element).prop('disabled', false);
        },
        success: function(json) {
            $('.alert-dismissible').remove();
            
            if (json['error']) {
                $('#alert').prepend('<div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> ' + json['error'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
            }
            
            if (json['success']) {
                $('#alert').prepend('<div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> ' + json['success'] + ' <button type="button" class="btn-close" data-bs-dismiss="alert"></button></div>');
                
                // Refresh the table
                $('#button-refresh').trigger('click');
            }
        },
        error: function(xhr, ajaxOptions, thrownError) {
            console.error(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
    });
});
//--></script>
      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}