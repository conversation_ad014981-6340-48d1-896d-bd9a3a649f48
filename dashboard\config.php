<?php
// HTTP
define('HTTP_SERVER', 'https://v1.codaym.com/dashboard/');
define('HTTP_CATALOG', 'https://v1.codaym.com/');

// HTTPS
define('HTTPS_SERVER', 'https://v1.codaym.com/dashboard/');
define('HTTPS_CATALOG', 'https://v1.codaym.com/');

// DIR
define('DIR_APPLICATION', '/home/<USER>/public_html/dashboard/');
define('DIR_SYSTEM', '/home/<USER>/public_html/system/');
define('DIR_IMAGE', '/home/<USER>/public_html/image/');
define('DIR_STORAGE', '/home/<USER>/storage/');
define('DIR_CATALOG', '/home/<USER>/public_html/catalog/');
define('DIR_LANGUAGE', DIR_APPLICATION . 'language/');
define('DIR_TEMPLATE', DIR_APPLICATION . 'view/template/');
define('DIR_CONFIG', DIR_SYSTEM . 'config/');
define('DIR_CACHE', DIR_STORAGE . 'cache/');
define('DIR_DOWNLOAD', DIR_STORAGE . 'download/');
define('DIR_LOGS', DIR_STORAGE . 'logs/');
define('DIR_MODIFICATION', DIR_STORAGE . 'modification/');
define('DIR_SESSION', DIR_STORAGE . 'session/');
define('DIR_UPLOAD', DIR_STORAGE . 'upload/');

// DB
define('DB_DRIVER', 'mysqli');
define('DB_HOSTNAME', 'localhost');
define('DB_USERNAME', 'v1_db');
define('DB_PASSWORD', 'v1_dbv1_db');
define('DB_DATABASE', 'v1_db');
define('DB_PORT', '3306');
define('DB_PREFIX', 'cod_');

// OpenCart API
define('OPENCART_SERVER', 'https://www.opencart.com/');
