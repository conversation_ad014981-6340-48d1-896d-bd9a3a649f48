{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <a href="{{ export_excel }}" data-toggle="tooltip" title="{{ button_export_excel }}" class="btn btn-success"><i class="fa fa-file-excel-o"></i></a>
        <a href="{{ export_pdf }}" data-toggle="tooltip" title="{{ button_export_pdf }}" class="btn btn-danger"><i class="fa fa-file-pdf-o"></i></a>
        <a href="{{ export_csv }}" data-toggle="tooltip" title="{{ button_export_csv }}" class="btn btn-info"><i class="fa fa-file-text-o"></i></a>
        <a href="{{ print }}" data-toggle="tooltip" title="{{ button_print }}" class="btn btn-warning" target="_blank"><i class="fa fa-print"></i></a>
        <a href="{{ back }}" data-toggle="tooltip" title="{{ button_back }}" class="btn btn-default"><i class="fa fa-reply"></i></a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if success %}
    <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    
    <!-- معلومات التقرير -->
    <div class="panel panel-primary">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-info-circle"></i> {{ text_report_info }}</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-md-6">
            <table class="table table-striped">
              <tr>
                <td><strong>{{ text_period }}:</strong></td>
                <td>{{ date_start }} {{ text_to }} {{ date_end }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_report_type }}:</strong></td>
                <td>{{ text_advanced_cash_flow }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_currency }}:</strong></td>
                <td>{{ currency }}</td>
              </tr>
            </table>
          </div>
          <div class="col-md-6">
            <table class="table table-striped">
              <tr>
                <td><strong>{{ text_generated_on }}:</strong></td>
                <td>{{ generated_date }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_generated_by }}:</strong></td>
                <td>{{ generated_by }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_analysis_type }}:</strong></td>
                <td>{{ text_ai_powered }}</td>
              </tr>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- ملخص التدفق النقدي -->
    <div class="panel panel-success">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-bar-chart"></i> {{ text_cash_flow_summary }}</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-md-3">
            <div class="info-box bg-blue">
              <span class="info-box-icon"><i class="fa fa-arrow-up"></i></span>
              <div class="info-box-content">
                <span class="info-box-text">{{ text_operating_activities }}</span>
                <span class="info-box-number">{{ cash_flow_data.operating_total }}</span>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="info-box bg-yellow">
              <span class="info-box-icon"><i class="fa fa-building"></i></span>
              <div class="info-box-content">
                <span class="info-box-text">{{ text_investing_activities }}</span>
                <span class="info-box-number">{{ cash_flow_data.investing_total }}</span>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="info-box bg-red">
              <span class="info-box-icon"><i class="fa fa-bank"></i></span>
              <div class="info-box-content">
                <span class="info-box-text">{{ text_financing_activities }}</span>
                <span class="info-box-number">{{ cash_flow_data.financing_total }}</span>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="info-box bg-green">
              <span class="info-box-icon"><i class="fa fa-money"></i></span>
              <div class="info-box-content">
                <span class="info-box-text">{{ text_net_cash_flow }}</span>
                <span class="info-box-number">{{ cash_flow_data.net_cash_flow }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- نسب السيولة -->
    {% if liquidity_ratios %}
    <div class="panel panel-info">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-tint"></i> {{ text_liquidity_ratios }}</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-md-3">
            <div class="panel panel-default">
              <div class="panel-heading">{{ text_current_ratio }}</div>
              <div class="panel-body text-center">
                <h2 class="{% if liquidity_ratios.current_ratio >= 2 %}text-success{% elseif liquidity_ratios.current_ratio >= 1 %}text-warning{% else %}text-danger{% endif %}">
                  {{ liquidity_ratios.current_ratio }}
                </h2>
                <small>{{ text_times }}</small>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="panel panel-default">
              <div class="panel-heading">{{ text_quick_ratio }}</div>
              <div class="panel-body text-center">
                <h2 class="{% if liquidity_ratios.quick_ratio >= 1 %}text-success{% elseif liquidity_ratios.quick_ratio >= 0.5 %}text-warning{% else %}text-danger{% endif %}">
                  {{ liquidity_ratios.quick_ratio }}
                </h2>
                <small>{{ text_times }}</small>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="panel panel-default">
              <div class="panel-heading">{{ text_cash_ratio }}</div>
              <div class="panel-body text-center">
                <h2 class="{% if liquidity_ratios.cash_ratio >= 0.2 %}text-success{% elseif liquidity_ratios.cash_ratio >= 0.1 %}text-warning{% else %}text-danger{% endif %}">
                  {{ liquidity_ratios.cash_ratio }}
                </h2>
                <small>{{ text_times }}</small>
              </div>
            </div>
          </div>
          <div class="col-md-3">
            <div class="panel panel-default">
              <div class="panel-heading">{{ text_cash_coverage_days }}</div>
              <div class="panel-body text-center">
                <h2 class="{% if liquidity_ratios.cash_coverage_days >= 90 %}text-success{% elseif liquidity_ratios.cash_coverage_days >= 30 %}text-warning{% else %}text-danger{% endif %}">
                  {{ liquidity_ratios.cash_coverage_days }}
                </h2>
                <small>{{ text_days }}</small>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    {% endif %}

    <!-- تحليل الاتجاهات -->
    {% if trend_analysis %}
    <div class="panel panel-warning">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-line-chart"></i> {{ text_trend_analysis }}</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-md-8">
            <canvas id="trend-chart" width="400" height="200"></canvas>
          </div>
          <div class="col-md-4">
            <table class="table table-striped">
              <thead>
                <tr>
                  <th>{{ text_period }}</th>
                  <th class="text-right">{{ text_growth_rate }}</th>
                </tr>
              </thead>
              <tbody>
                {% for trend in trend_analysis|slice(-6) %}
                <tr>
                  <td>{{ trend.period }}</td>
                  <td class="text-right {% if trend.growth_rate >= 0 %}text-success{% else %}text-danger{% endif %}">
                    {{ trend.growth_rate }}%
                  </td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
    {% endif %}

    <!-- دورة تحويل النقد -->
    {% if cash_conversion_cycle %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-refresh"></i> {{ text_cash_conversion_cycle }}</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-md-8">
            <div class="row">
              <div class="col-md-4">
                <div class="info-box bg-aqua">
                  <span class="info-box-icon"><i class="fa fa-clock-o"></i></span>
                  <div class="info-box-content">
                    <span class="info-box-text">{{ text_collection_period }}</span>
                    <span class="info-box-number">{{ cash_conversion_cycle.average_collection_period }}</span>
                    <span class="info-box-more">{{ text_days }}</span>
                  </div>
                </div>
              </div>
              <div class="col-md-4">
                <div class="info-box bg-green">
                  <span class="info-box-icon"><i class="fa fa-cubes"></i></span>
                  <div class="info-box-content">
                    <span class="info-box-text">{{ text_inventory_period }}</span>
                    <span class="info-box-number">{{ cash_conversion_cycle.average_inventory_period }}</span>
                    <span class="info-box-more">{{ text_days }}</span>
                  </div>
                </div>
              </div>
              <div class="col-md-4">
                <div class="info-box bg-yellow">
                  <span class="info-box-icon"><i class="fa fa-credit-card"></i></span>
                  <div class="info-box-content">
                    <span class="info-box-text">{{ text_payment_period }}</span>
                    <span class="info-box-number">{{ cash_conversion_cycle.average_payment_period }}</span>
                    <span class="info-box-more">{{ text_days }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-4">
            <div class="panel panel-primary">
              <div class="panel-heading">{{ text_cash_conversion_cycle }}</div>
              <div class="panel-body text-center">
                <h1 class="{% if cash_conversion_cycle.cash_conversion_cycle < 30 %}text-success{% elseif cash_conversion_cycle.cash_conversion_cycle < 60 %}text-warning{% else %}text-danger{% endif %}">
                  {{ cash_conversion_cycle.cash_conversion_cycle }}
                </h1>
                <p>{{ text_days }}</p>
                <span class="label label-{% if cash_conversion_cycle.efficiency_rating == 'excellent' %}success{% elseif cash_conversion_cycle.efficiency_rating == 'good' %}info{% else %}warning{% endif %}">
                  {{ cash_conversion_cycle.efficiency_rating }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    {% endif %}

    <!-- التنبؤ بالتدفق النقدي -->
    {% if forecasting %}
    <div class="panel panel-success">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-crystal-ball"></i> {{ text_cash_flow_forecast }}</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-md-8">
            <canvas id="forecast-chart" width="400" height="200"></canvas>
          </div>
          <div class="col-md-4">
            <table class="table table-striped">
              <thead>
                <tr>
                  <th>{{ text_period }}</th>
                  <th class="text-right">{{ text_forecasted_flow }}</th>
                  <th class="text-center">{{ text_confidence }}</th>
                </tr>
              </thead>
              <tbody>
                {% for forecast in forecasting %}
                <tr>
                  <td>{{ forecast.period }}</td>
                  <td class="text-right {% if forecast.forecasted_net_flow >= 0 %}text-success{% else %}text-danger{% endif %}">
                    {{ forecast.forecasted_net_flow }}
                  </td>
                  <td class="text-center">
                    <span class="label label-{% if forecast.confidence_level == 'high' %}success{% elseif forecast.confidence_level == 'medium' %}warning{% else %}danger{% endif %}">
                      {{ forecast.confidence_level }}
                    </span>
                  </td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
    {% endif %}

    <!-- تقييم المخاطر -->
    {% if risk_assessment %}
    <div class="panel panel-danger">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-exclamation-triangle"></i> {{ text_risk_assessment }}</h3>
      </div>
      <div class="panel-body">
        {% if risk_assessment|length > 0 %}
        <div class="alert alert-warning">
          <h4>{{ text_identified_risks }}</h4>
          {% for risk in risk_assessment %}
          <div class="risk-item">
            <span class="label label-{% if risk.severity == 'high' %}danger{% elseif risk.severity == 'medium' %}warning{% else %}info{% endif %}">
              {{ risk.severity }}
            </span>
            <strong>{{ risk.description }}</strong>
            <p>{{ risk.impact }}</p>
          </div>
          {% endfor %}
        </div>
        {% else %}
        <div class="alert alert-success">
          <i class="fa fa-check-circle"></i> {{ text_no_significant_risks }}
        </div>
        {% endif %}
      </div>
    </div>
    {% endif %}

    <!-- التوصيات -->
    {% if recommendations %}
    <div class="panel panel-info">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-lightbulb-o"></i> {{ text_recommendations }}</h3>
      </div>
      <div class="panel-body">
        <ul class="list-group">
          {% for recommendation in recommendations %}
          <li class="list-group-item">
            <i class="fa fa-arrow-right text-primary"></i> {{ recommendation }}
          </li>
          {% endfor %}
        </ul>
      </div>
    </div>
    {% endif %}

    <!-- معلومات الامتثال -->
    <div class="panel panel-success">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-check-circle"></i> {{ text_compliance_info }}</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-md-3">
            <div class="alert alert-success">
              <i class="fa fa-check"></i> {{ text_eas_compliant }}
            </div>
          </div>
          <div class="col-md-3">
            <div class="alert alert-info">
              <i class="fa fa-globe"></i> {{ text_eta_ready }}
            </div>
          </div>
          <div class="col-md-3">
            <div class="alert alert-warning">
              <i class="fa fa-balance-scale"></i> {{ text_egyptian_gaap }}
            </div>
          </div>
          <div class="col-md-3">
            <div class="alert alert-primary">
              <i class="fa fa-shield"></i> {{ text_cash_flow_control }}
            </div>
          </div>
        </div>
      </div>
    </div>

  </div>
</div>

<script type="text/javascript"><!--
// Trend Chart
{% if trend_analysis %}
var ctx1 = document.getElementById('trend-chart').getContext('2d');
var trendChart = new Chart(ctx1, {
    type: 'line',
    data: {
        labels: [
            {% for trend in trend_analysis %}
            '{{ trend.period }}'{% if not loop.last %},{% endif %}
            {% endfor %}
        ],
        datasets: [{
            label: '{{ text_net_cash_flow }}',
            data: [
                {% for trend in trend_analysis %}
                {{ trend.net_cash_flow }}{% if not loop.last %},{% endif %}
                {% endfor %}
            ],
            borderColor: '#3c8dbc',
            backgroundColor: 'rgba(60, 141, 188, 0.1)',
            fill: true
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});
{% endif %}

// Forecast Chart
{% if forecasting %}
var ctx2 = document.getElementById('forecast-chart').getContext('2d');
var forecastChart = new Chart(ctx2, {
    type: 'bar',
    data: {
        labels: [
            {% for forecast in forecasting %}
            '{{ forecast.period }}'{% if not loop.last %},{% endif %}
            {% endfor %}
        ],
        datasets: [{
            label: '{{ text_forecasted_flow }}',
            data: [
                {% for forecast in forecasting %}
                {{ forecast.forecasted_net_flow }}{% if not loop.last %},{% endif %}
                {% endfor %}
            ],
            backgroundColor: [
                {% for forecast in forecasting %}
                {% if forecast.forecasted_net_flow >= 0 %}'rgba(40, 167, 69, 0.8)'{% else %}'rgba(220, 53, 69, 0.8)'{% endif %}{% if not loop.last %},{% endif %}
                {% endfor %}
            ]
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});
{% endif %}

// Tooltips
$('[data-toggle="tooltip"]').tooltip();
//--></script>

{{ footer }}
