{{ header }}{{ column_left }}

<!-- Enterprise Grade Plus CSS for Tax Return -->
<style>
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --danger-color: #e74c3c;
    --warning-color: #f39c12;
    --info-color: #17a2b8;
    --tax-color: #dc3545;
    --light-bg: #f8f9fa;
    --border-color: #dee2e6;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

.tax-return-container {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: var(--shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.tax-return-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--tax-color), var(--primary-color), var(--secondary-color));
}

.tax-return-header {
    text-align: center;
    border-bottom: 3px solid var(--tax-color);
    padding-bottom: 20px;
    margin-bottom: 30px;
    position: relative;
}

.tax-return-header h2 {
    color: var(--tax-color);
    font-weight: 700;
    font-size: 1.8rem;
    margin: 0;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.tax-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.tax-card {
    background: linear-gradient(135deg, #ffffff, #f8f9fa);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    text-align: center;
    box-shadow: var(--shadow);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.tax-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0,0,0,0.1);
}

.tax-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
}

.tax-card.income::before { background: var(--success-color); }
.tax-card.expenses::before { background: var(--warning-color); }
.tax-card.taxable::before { background: var(--info-color); }
.tax-card.tax-due::before { background: var(--tax-color); }

.tax-card h4 {
    color: var(--primary-color);
    margin-bottom: 10px;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.tax-card .amount {
    font-size: 1.4rem;
    font-weight: 700;
    font-family: 'Courier New', monospace;
    margin-bottom: 5px;
}

.tax-card .description {
    font-size: 0.8rem;
    color: #6c757d;
    font-weight: 500;
}

.tax-card.income .amount { color: var(--success-color); }
.tax-card.expenses .amount { color: var(--warning-color); }
.tax-card.taxable .amount { color: var(--info-color); }
.tax-card.tax-due .amount { color: var(--tax-color); }

.tax-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    background: #ffffff;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.tax-table th {
    background: linear-gradient(135deg, var(--tax-color), #c82333);
    color: white;
    padding: 15px 12px;
    text-align: center;
    font-weight: 600;
    font-size: 0.9rem;
    border-bottom: 2px solid var(--border-color);
}

.tax-table td {
    padding: 12px;
    border-bottom: 1px solid var(--border-color);
    text-align: center;
    transition: var(--transition);
}

.tax-table tbody tr:hover {
    background: var(--light-bg);
    transform: scale(1.005);
}

.tax-table tbody tr:nth-child(even) {
    background: #fafbfc;
}

.amount-cell {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    text-align: {{ direction == 'rtl' ? 'right' : 'left' }};
}

.amount-income { 
    color: var(--success-color); 
    font-weight: 600;
}

.amount-expense { 
    color: var(--warning-color); 
    font-weight: 600;
}

.amount-tax { 
    color: var(--tax-color); 
    font-weight: 600;
}

/* Filter Panel */
.filter-panel {
    background: linear-gradient(135deg, #f8f9fa, #ffffff);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: var(--shadow);
}

.filter-panel h4 {
    color: var(--tax-color);
    margin-bottom: 15px;
    font-weight: 600;
}

.form-group {
    margin-bottom: 15px;
}

.form-label {
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 5px;
    display: block;
}

.form-control {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 8px 12px;
    transition: var(--transition);
    width: 100%;
}

.form-control:focus {
    border-color: var(--tax-color);
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
    outline: none;
}

/* ETA Integration */
.eta-status {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.eta-status.submitted {
    background: rgba(39, 174, 96, 0.1);
    color: var(--success-color);
}

.eta-status.pending {
    background: rgba(243, 156, 18, 0.1);
    color: var(--warning-color);
}

.eta-status.draft {
    background: rgba(23, 162, 184, 0.1);
    color: var(--info-color);
}

/* RTL Support */
[dir="rtl"] .tax-table {
    direction: rtl;
}

[dir="rtl"] .amount-cell {
    text-align: right;
}

/* Print Styles */
@media print {
    .tax-return-container {
        box-shadow: none;
        border: 1px solid #000;
    }
    
    .tax-table th {
        background: #f0f0f0 !important;
        color: #000 !important;
    }
    
    .btn, .panel-heading, .filter-panel {
        display: none !important;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .tax-table {
        font-size: 0.8rem;
    }
    
    .tax-table th,
    .tax-table td {
        padding: 8px 6px;
    }
    
    .tax-summary {
        grid-template-columns: repeat(2, 1fr);
    }
}
</style>

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-{{ direction == 'rtl' ? 'start' : 'end' }}">
        <div class="btn-group" role="group" aria-label="{{ text_actions }}">
          <button type="button" class="btn btn-success btn-lg" onclick="generateTaxReturn()"
                  data-toggle="tooltip" title="{{ button_generate_return }}">
            <i class="fa fa-file-text"></i> {{ button_generate }}
          </button>
          <div class="btn-group" role="group">
            <button type="button" class="btn btn-info dropdown-toggle" data-toggle="dropdown"
                    aria-expanded="false" data-toggle="tooltip" title="{{ text_export_options }}">
              <i class="fa fa-download"></i> {{ text_export }}
            </button>
            <ul class="dropdown-menu">
              <li><a class="dropdown-item" href="#" onclick="exportTaxReturn('excel')">
                <i class="fa fa-file-excel text-success"></i> Excel
              </a></li>
              <li><a class="dropdown-item" href="#" onclick="exportTaxReturn('pdf')">
                <i class="fa fa-file-pdf text-danger"></i> PDF
              </a></li>
              <li><hr class="dropdown-divider"></li>
              <li><a class="dropdown-item" href="#" onclick="printTaxReturn()">
                <i class="fa fa-print text-muted"></i> {{ text_print }}
              </a></li>
            </ul>
          </div>
          <button type="button" class="btn btn-warning" onclick="submitToETA()"
                  data-toggle="tooltip" title="{{ button_submit_eta }}">
            <i class="fa fa-paper-plane"></i> {{ text_submit_eta }}
          </button>
          <button type="button" class="btn btn-primary" onclick="calculateProvision()"
                  data-toggle="tooltip" title="{{ button_calculate_provision }}">
            <i class="fa fa-calculator"></i>
          </button>
        </div>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li class="breadcrumb-item"><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>

  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible">
      <i class="fa fa-exclamation-triangle"></i>
      {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}

    {% if success %}
    <div class="alert alert-success alert-dismissible">
      <i class="fa fa-check-circle"></i>
      {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}

    <!-- Filter Panel -->
    <div class="filter-panel">
      <h4>{{ text_tax_return_filters }}</h4>
      <form id="tax-return-filter-form" method="post">
        <div class="row">
          <div class="col-md-3">
            <div class="form-group">
              <label for="financial_year" class="form-label">{{ entry_financial_year }}</label>
              <select name="financial_year" id="financial_year" class="form-control" required>
                <option value="">{{ text_select_year }}</option>
                {% for year in financial_years %}
                <option value="{{ year.year }}"{% if year.year == financial_year %} selected{% endif %}>{{ year.year }}</option>
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="col-md-3">
            <div class="form-group">
              <label for="tax_rate" class="form-label">{{ entry_tax_rate }}</label>
              <select name="tax_rate" id="tax_rate" class="form-control" required>
                {% for rate, description in tax_rates %}
                <option value="{{ rate }}"{% if rate == tax_rate %} selected{% endif %}>{{ description }}</option>
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="col-md-3">
            <div class="form-group">
              <label for="return_type" class="form-label">{{ entry_return_type }}</label>
              <select name="return_type" id="return_type" class="form-control" required>
                <option value="annual">{{ text_annual_return }}</option>
                <option value="quarterly">{{ text_quarterly_return }}</option>
                <option value="monthly">{{ text_monthly_return }}</option>
              </select>
            </div>
          </div>
          <div class="col-md-3">
            <div class="form-group">
              <label class="form-label">&nbsp;</label>
              <div>
                <button type="submit" class="btn btn-primary btn-block">
                  <i class="fa fa-search"></i> {{ button_generate }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>

    <!-- Tax Return Content -->
    {% if tax_return_data %}
    <!-- Tax Summary Cards -->
    <div class="tax-summary">
      <div class="tax-card income">
        <h4>{{ text_total_income }}</h4>
        <div class="amount">{{ tax_return_data.total_income_formatted }}</div>
        <div class="description">{{ text_total_income_description }}</div>
      </div>

      <div class="tax-card expenses">
        <h4>{{ text_total_expenses }}</h4>
        <div class="amount">{{ tax_return_data.total_expenses_formatted }}</div>
        <div class="description">{{ text_total_expenses_description }}</div>
      </div>

      <div class="tax-card taxable">
        <h4>{{ text_taxable_income }}</h4>
        <div class="amount">{{ tax_return_data.taxable_income_formatted }}</div>
        <div class="description">{{ text_taxable_income_description }}</div>
      </div>

      <div class="tax-card tax-due">
        <h4>{{ text_tax_due }}</h4>
        <div class="amount">{{ tax_return_data.tax_due_formatted }}</div>
        <div class="description">{{ text_tax_due_description }}</div>
      </div>
    </div>

    <!-- Tax Return Details -->
    <div class="tax-return-container">
      <div class="tax-return-header">
        <h2>{{ text_tax_return_details }}</h2>
        <p><strong>{{ text_financial_year }}:</strong> {{ financial_year }}</p>
        <p><strong>{{ text_tax_rate }}:</strong> {{ tax_rate }}%</p>
        <div class="eta-status {{ tax_return_data.eta_status }}">
          <i class="fa fa-{{ tax_return_data.eta_status == 'submitted' ? 'check-circle' : tax_return_data.eta_status == 'pending' ? 'clock-o' : 'edit' }}"></i>
          {{ tax_return_data.eta_status_text }}
        </div>
      </div>

      <!-- Income Details Table -->
      <h4>{{ text_income_details }}</h4>
      <div class="table-responsive">
        <table class="tax-table" id="income-table">
          <thead>
            <tr>
              <th>{{ column_account_name }}</th>
              <th>{{ column_account_code }}</th>
              <th>{{ column_amount }}</th>
              <th>{{ column_percentage }}</th>
            </tr>
          </thead>
          <tbody>
            {% for income in tax_return_data.income_details %}
            <tr>
              <td>{{ income.account_name }}</td>
              <td>{{ income.account_code }}</td>
              <td class="amount-cell amount-income">{{ income.amount_formatted }}</td>
              <td>{{ income.percentage }}%</td>
            </tr>
            {% endfor %}
          </tbody>
          <tfoot>
            <tr style="background: #f8f9fa; font-weight: bold;">
              <td colspan="2"><strong>{{ text_total_income }}</strong></td>
              <td class="amount-cell amount-income">{{ tax_return_data.total_income_formatted }}</td>
              <td>100%</td>
            </tr>
          </tfoot>
        </table>
      </div>

      <!-- Expenses Details Table -->
      <h4>{{ text_expenses_details }}</h4>
      <div class="table-responsive">
        <table class="tax-table" id="expenses-table">
          <thead>
            <tr>
              <th>{{ column_account_name }}</th>
              <th>{{ column_account_code }}</th>
              <th>{{ column_amount }}</th>
              <th>{{ column_percentage }}</th>
              <th>{{ column_deductible }}</th>
            </tr>
          </thead>
          <tbody>
            {% for expense in tax_return_data.expenses_details %}
            <tr>
              <td>{{ expense.account_name }}</td>
              <td>{{ expense.account_code }}</td>
              <td class="amount-cell amount-expense">{{ expense.amount_formatted }}</td>
              <td>{{ expense.percentage }}%</td>
              <td>
                <span class="badge bg-{{ expense.deductible ? 'success' : 'danger' }}">
                  {{ expense.deductible ? text_yes : text_no }}
                </span>
              </td>
            </tr>
            {% endfor %}
          </tbody>
          <tfoot>
            <tr style="background: #f8f9fa; font-weight: bold;">
              <td colspan="2"><strong>{{ text_total_expenses }}</strong></td>
              <td class="amount-cell amount-expense">{{ tax_return_data.total_expenses_formatted }}</td>
              <td>100%</td>
              <td>-</td>
            </tr>
          </tfoot>
        </table>
      </div>

      <!-- Tax Calculation Summary -->
      <div class="tax-return-container">
        <div class="tax-return-header">
          <h2>{{ text_tax_calculation }}</h2>
        </div>

        <div class="table-responsive">
          <table class="tax-table">
            <tbody>
              <tr>
                <td><strong>{{ text_gross_income }}</strong></td>
                <td class="amount-cell amount-income">{{ tax_return_data.total_income_formatted }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_deductible_expenses }}</strong></td>
                <td class="amount-cell amount-expense">{{ tax_return_data.deductible_expenses_formatted }}</td>
              </tr>
              <tr style="background: #e3f2fd;">
                <td><strong>{{ text_taxable_income }}</strong></td>
                <td class="amount-cell amount-tax">{{ tax_return_data.taxable_income_formatted }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_tax_rate }} ({{ tax_rate }}%)</strong></td>
                <td class="amount-cell">{{ tax_rate }}%</td>
              </tr>
              <tr style="background: #ffebee;">
                <td><strong>{{ text_tax_due }}</strong></td>
                <td class="amount-cell amount-tax">{{ tax_return_data.tax_due_formatted }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_tax_paid }}</strong></td>
                <td class="amount-cell amount-expense">{{ tax_return_data.tax_paid_formatted }}</td>
              </tr>
              <tr style="background: {{ tax_return_data.balance_due > 0 ? '#ffebee' : '#e8f5e8' }};">
                <td><strong>{{ tax_return_data.balance_due > 0 ? text_balance_due : text_refund_due }}</strong></td>
                <td class="amount-cell amount-tax">{{ tax_return_data.balance_formatted }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
    {% else %}
    <div class="alert alert-info">
      <i class="fa fa-info-circle"></i>
      {{ text_no_tax_return }}
    </div>
    {% endif %}
  </div>
</div>

<script>
// Enterprise Grade Plus JavaScript for Tax Return
class TaxReturnManager {
    constructor() {
        this.initializeTooltips();
        this.initializeDataTables();
        this.initializeKeyboardShortcuts();
        this.initializeFormValidation();
        this.initializeETAIntegration();
    }

    initializeTooltips() {
        $('[data-toggle="tooltip"]').tooltip();
    }

    initializeDataTables() {
        const tables = ['income-table', 'expenses-table'];

        tables.forEach(tableId => {
            const table = document.getElementById(tableId);
            if (table && typeof $ !== 'undefined' && $.fn.DataTable) {
                $(table).DataTable({
                    responsive: true,
                    pageLength: 25,
                    order: [[2, 'desc']], // Sort by amount desc
                    columnDefs: [
                        { targets: [2], className: 'text-end' },
                        { targets: [3], className: 'text-center' }
                    ],
                    language: {
                        url: '{{ direction == "rtl" ? "/view/javascript/jquery/datatables-ar.json" : "/view/javascript/jquery/datatables-en.json" }}'
                    }
                });
            }
        });
    }

    initializeKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case 'g':
                        e.preventDefault();
                        this.generateTaxReturn();
                        break;
                    case 'e':
                        e.preventDefault();
                        this.showExportMenu();
                        break;
                    case 'p':
                        e.preventDefault();
                        this.printTaxReturn();
                        break;
                    case 's':
                        e.preventDefault();
                        this.submitToETA();
                        break;
                    case 'c':
                        e.preventDefault();
                        this.calculateProvision();
                        break;
                }
            }
        });
    }

    initializeFormValidation() {
        const form = document.getElementById('tax-return-filter-form');
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.validateAndSubmitForm();
            });
        }
    }

    initializeETAIntegration() {
        // Initialize ETA integration features
        this.checkETAConnection();
    }

    validateAndSubmitForm() {
        const form = document.getElementById('tax-return-filter-form');
        const formData = new FormData(form);

        // Validate financial year
        if (!formData.get('financial_year')) {
            this.showAlert('{{ error_financial_year }}', 'danger');
            return;
        }

        // Validate tax rate
        if (!formData.get('tax_rate')) {
            this.showAlert('{{ error_tax_rate }}', 'danger');
            return;
        }

        // Submit form
        this.showLoadingState(true);
        form.submit();
    }

    generateTaxReturn() {
        const form = document.getElementById('tax-return-filter-form');
        if (form) {
            this.validateAndSubmitForm();
        }
    }

    exportTaxReturn(format) {
        const financialYear = document.getElementById('financial_year').value;
        const taxRate = document.getElementById('tax_rate').value;
        const returnType = document.getElementById('return_type').value;

        if (!financialYear || !taxRate) {
            this.showAlert('{{ error_complete_form }}', 'danger');
            return;
        }

        const params = new URLSearchParams({
            financial_year: financialYear,
            tax_rate: taxRate,
            return_type: returnType,
            format: format
        });

        this.showAlert('{{ text_exporting }}...', 'info');
        window.open('{{ url_link('accounts/tax_return', 'export') }}&' + params.toString(), '_blank');
    }

    printTaxReturn() {
        window.print();
    }

    submitToETA() {
        const financialYear = document.getElementById('financial_year').value;

        if (!financialYear) {
            this.showAlert('{{ error_financial_year }}', 'danger');
            return;
        }

        if (!confirm('{{ confirm_submit_eta }}')) {
            return;
        }

        this.showAlert('{{ text_submitting_eta }}...', 'info');

        fetch('{{ url_link('accounts/tax_return', 'submit_eta') }}', {
            method: 'POST',
            body: JSON.stringify({
                financial_year: financialYear
            }),
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.showAlert('{{ success_eta_submission }}', 'success');
                setTimeout(() => location.reload(), 2000);
            } else {
                this.showAlert(data.error || '{{ error_eta_submission }}', 'danger');
            }
        })
        .catch(error => {
            this.showAlert('{{ error_eta_submission }}: ' + error.message, 'danger');
        });
    }

    calculateProvision() {
        const financialYear = document.getElementById('financial_year').value;

        if (!financialYear) {
            this.showAlert('{{ error_financial_year }}', 'danger');
            return;
        }

        this.showAlert('{{ text_calculating }}...', 'info');

        fetch('{{ url_link('accounts/tax_return', 'calculate_provision') }}', {
            method: 'POST',
            body: JSON.stringify({
                financial_year: financialYear
            }),
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.showAlert('{{ success_provision_calculated }}', 'success');
                // Update provision display
                if (data.provision_amount) {
                    this.updateProvisionDisplay(data.provision_amount);
                }
            } else {
                this.showAlert(data.error || '{{ error_provision_calculation }}', 'danger');
            }
        })
        .catch(error => {
            this.showAlert('{{ error_provision_calculation }}: ' + error.message, 'danger');
        });
    }

    checkETAConnection() {
        // Check ETA connection status
        fetch('{{ url_link('accounts/tax_return', 'check_eta_status') }}', {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            this.updateETAStatus(data.status);
        })
        .catch(error => {
            console.log('ETA status check failed:', error);
        });
    }

    updateETAStatus(status) {
        const statusElements = document.querySelectorAll('.eta-status');
        statusElements.forEach(element => {
            element.className = `eta-status ${status}`;
            element.innerHTML = `<i class="fa fa-${status === 'connected' ? 'check-circle' : 'exclamation-triangle'}"></i> ${status === 'connected' ? '{{ text_eta_connected }}' : '{{ text_eta_disconnected }}'}`;
        });
    }

    updateProvisionDisplay(amount) {
        // Update provision amount display
        const provisionElements = document.querySelectorAll('.provision-amount');
        provisionElements.forEach(element => {
            element.textContent = amount;
        });
    }

    showExportMenu() {
        const exportButton = document.querySelector('.dropdown-toggle');
        if (exportButton) {
            exportButton.click();
        }
    }

    showLoadingState(show) {
        const buttons = document.querySelectorAll('.btn');
        buttons.forEach(btn => {
            if (show) {
                btn.disabled = true;
                if (btn.querySelector('i')) {
                    btn.querySelector('i').className = 'fa fa-spinner fa-spin';
                }
            } else {
                btn.disabled = false;
                location.reload();
            }
        });
    }

    showAlert(message, type) {
        const alertContainer = document.createElement('div');
        alertContainer.className = `alert alert-${type} alert-dismissible fade show`;
        alertContainer.innerHTML = `
            <i class="fa fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-triangle' : 'info-circle'}"></i>
            ${message}
            <button type="button" class="close" data-dismiss="alert">&times;</button>
        `;

        const container = document.querySelector('#content .container-fluid');
        container.insertBefore(alertContainer, container.firstChild);

        setTimeout(() => {
            alertContainer.remove();
        }, 5000);
    }
}

// Global functions for backward compatibility
function generateTaxReturn() {
    taxReturnManager.generateTaxReturn();
}

function exportTaxReturn(format) {
    taxReturnManager.exportTaxReturn(format);
}

function printTaxReturn() {
    taxReturnManager.printTaxReturn();
}

function submitToETA() {
    taxReturnManager.submitToETA();
}

function calculateProvision() {
    taxReturnManager.calculateProvision();
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.taxReturnManager = new TaxReturnManager();
});
</script>

{{ footer }}
