<?php
// Heading
$_['heading_title']                    = 'Multi-Currency Revaluation';

// Text
$_['text_success']                     = 'Success: You have modified multi-currency revaluation!';
$_['text_list']                        = 'Multi-Currency Revaluation List';
$_['text_add']                         = 'Add Revaluation';
$_['text_edit']                        = 'Edit Revaluation';
$_['text_view']                        = 'View Revaluation';
$_['text_default']                     = 'Default';
$_['text_enabled']                     = 'Enabled';
$_['text_disabled']                    = 'Disabled';
$_['text_yes']                         = 'Yes';
$_['text_no']                          = 'No';
$_['text_none']                        = 'None';
$_['text_select']                      = 'Select';
$_['text_loading']                     = 'Loading...';
$_['text_no_results']                  = 'No results!';
$_['text_confirm']                     = 'Are you sure?';

// Revaluation specific
$_['text_revaluation_info']            = 'Revaluation Information';
$_['text_revaluation_name']            = 'Revaluation Name';
$_['text_revaluation_date']            = 'Revaluation Date';
$_['text_revaluation_method']          = 'Revaluation Method';
$_['text_base_currency']               = 'Base Currency';
$_['text_foreign_currency']            = 'Foreign Currency';
$_['text_exchange_rate']               = 'Exchange Rate';
$_['text_old_rate']                    = 'Old Rate';
$_['text_new_rate']                    = 'New Rate';
$_['text_rate_change']                 = 'Rate Change';
$_['text_revaluation_amount']          = 'Revaluation Amount';
$_['text_gain_loss']                   = 'Gain/Loss';
$_['text_unrealized_gain']             = 'Unrealized Gain';
$_['text_unrealized_loss']             = 'Unrealized Loss';
$_['text_realized_gain']               = 'Realized Gain';
$_['text_realized_loss']               = 'Realized Loss';

// Status
$_['text_status_draft']                = 'Draft';
$_['text_status_processing']           = 'Processing';
$_['text_status_completed']            = 'Completed';
$_['text_status_posted']               = 'Posted';
$_['text_status_cancelled']            = 'Cancelled';

// Methods
$_['text_method_current_rate']         = 'Current Rate Method';
$_['text_method_temporal']             = 'Temporal Method';
$_['text_method_monetary_nonmonetary'] = 'Monetary/Non-monetary Method';

// Account Types
$_['text_monetary_assets']             = 'Monetary Assets';
$_['text_nonmonetary_assets']          = 'Non-monetary Assets';
$_['text_monetary_liabilities']        = 'Monetary Liabilities';
$_['text_nonmonetary_liabilities']     = 'Non-monetary Liabilities';

// Statistics
$_['text_total_accounts']              = 'Total Accounts';
$_['text_affected_accounts']           = 'Affected Accounts';
$_['text_total_gain']                  = 'Total Gains';
$_['text_total_loss']                  = 'Total Losses';
$_['text_net_effect']                  = 'Net Effect';

// Buttons
$_['button_add']                       = 'Add';
$_['button_edit']                      = 'Edit';
$_['button_delete']                    = 'Delete';
$_['button_save']                      = 'Save';
$_['button_cancel']                    = 'Cancel';
$_['button_close']                     = 'Close';
$_['button_back']                      = 'Back';
$_['button_view']                      = 'View';
$_['button_print']                     = 'Print';
$_['button_export']                    = 'Export';
$_['button_process']                   = 'Process';
$_['button_post']                      = 'Post';
$_['button_preview']                   = 'Preview';
$_['button_calculate']                 = 'Calculate';
$_['button_refresh_rates']             = 'Refresh Rates';

// Columns
$_['column_revaluation_name']          = 'Revaluation Name';
$_['column_revaluation_date']          = 'Date';
$_['column_currency']                  = 'Currency';
$_['column_old_rate']                  = 'Old Rate';
$_['column_new_rate']                  = 'New Rate';
$_['column_rate_change']               = 'Rate Change %';
$_['column_gain_loss']                 = 'Gain/Loss';
$_['column_status']                    = 'Status';
$_['column_created_by']                = 'Created By';
$_['column_date_created']              = 'Date Created';
$_['column_action']                    = 'Action';

// Account columns
$_['column_account_code']              = 'Account Code';
$_['column_account_name']              = 'Account Name';
$_['column_account_type']              = 'Account Type';
$_['column_original_balance']          = 'Original Balance';
$_['column_revalued_balance']          = 'Revalued Balance';
$_['column_difference']                = 'Difference';

// Entry fields
$_['entry_revaluation_name']           = 'Revaluation Name';
$_['entry_revaluation_date']           = 'Revaluation Date';
$_['entry_base_currency']              = 'Base Currency';
$_['entry_foreign_currency']           = 'Foreign Currency';
$_['entry_new_exchange_rate']          = 'New Exchange Rate';
$_['entry_revaluation_method']         = 'Revaluation Method';
$_['entry_description']                = 'Description';
$_['entry_notes']                      = 'Notes';

// Help text
$_['help_revaluation_name']            = 'Enter a descriptive name for the revaluation';
$_['help_revaluation_date']            = 'Revaluation date should be at the end of an accounting period';
$_['help_revaluation_method']          = 'Choose the appropriate revaluation method according to accounting standards';
$_['help_exchange_rate']               = 'Enter the new exchange rate for the foreign currency';

// Error messages
$_['error_permission']                 = 'Warning: You do not have permission to access multi-currency revaluation!';
$_['error_revaluation_name']           = 'Revaluation name must be between 3 and 64 characters!';
$_['error_revaluation_date']           = 'Please enter revaluation date!';
$_['error_base_currency']              = 'Please select base currency!';
$_['error_foreign_currency']           = 'Please select foreign currency!';
$_['error_exchange_rate']              = 'Please enter a valid exchange rate!';
$_['error_same_currency']              = 'Base and foreign currencies cannot be the same!';
$_['error_invalid_date']               = 'Invalid date!';
$_['error_future_date']                = 'Date cannot be in the future!';
$_['error_no_accounts']                = 'No accounts available for processing!';
$_['error_processing']                 = 'Error occurred while processing revaluation!';
$_['error_already_posted']             = 'Revaluation has already been posted!';

// Success messages
$_['success_revaluation_added']        = 'Revaluation added successfully!';
$_['success_revaluation_updated']      = 'Revaluation updated successfully!';
$_['success_revaluation_deleted']      = 'Revaluation deleted successfully!';
$_['success_revaluation_processed']    = 'Revaluation processed successfully!';
$_['success_revaluation_posted']       = 'Revaluation posted successfully!';
$_['success_rates_updated']            = 'Exchange rates updated successfully!';

// Confirmation messages
$_['confirm_delete']                   = 'Are you sure you want to delete this revaluation?';
$_['confirm_process']                  = 'Are you sure you want to process this revaluation?';
$_['confirm_post']                     = 'Are you sure you want to post this revaluation?';

// Tabs
$_['tab_general']                      = 'General';
$_['tab_currencies']                   = 'Currencies';
$_['tab_accounts']                     = 'Accounts';
$_['tab_journal_entries']              = 'Journal Entries';
$_['tab_notes']                        = 'Notes';

// Reports
$_['text_revaluation_report']          = 'Revaluation Report';
$_['text_gain_loss_report']            = 'Gain/Loss Report';
$_['text_currency_exposure']           = 'Currency Exposure';

// IAS 21 Compliance
$_['text_ias_21_compliance']           = 'IAS 21 Compliance';
$_['text_functional_currency']         = 'Functional Currency';
$_['text_presentation_currency']       = 'Presentation Currency';
$_['text_translation_differences']     = 'Translation Differences';

// Processing status
$_['text_calculating']                 = 'Calculating...';
$_['text_posting']                     = 'Posting...';
$_['text_completed']                   = 'Completed';
$_['text_failed']                      = 'Failed';

// Additional features
$_['text_auto_rates']                  = 'Auto Rates';
$_['text_manual_rates']                = 'Manual Rates';
$_['text_rate_source']                 = 'Rate Source';
$_['text_central_bank']                = 'Central Bank';
$_['text_commercial_bank']             = 'Commercial Bank';
$_['text_market_rate']                 = 'Market Rate';

// Warnings
$_['warning_rate_change']              = 'Warning: Significant exchange rate change!';
$_['warning_large_impact']             = 'Warning: Large impact on financial statements!';
$_['warning_period_end']               = 'Warning: Revaluation recommended at period end!';
?>
