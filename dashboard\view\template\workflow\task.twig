{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="button" data-toggle="tooltip" title="{{ button_filter }}" onclick="$('#filter-task').toggleClass('hidden-sm hidden-xs')" class="btn btn-default hidden-md hidden-lg"><i class="fa fa-filter"></i></button>
        <a href="{{ add }}" data-toggle="tooltip" title="{{ button_add }}" class="btn btn-primary"><i class="fa fa-plus"></i></a>
        <button type="button" data-toggle="tooltip" title="{{ button_delete }}" class="btn btn-danger" onclick="confirm('{{ text_confirm }}') ? $('#form-task').submit() : false;"><i class="fa fa-trash-o"></i></button>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    {% if success %}
    <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    <div class="row">
      <div id="filter-task" class="col-md-3 col-md-push-9 col-sm-12 hidden-sm hidden-xs">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-filter"></i> {{ text_filter }}</h3>
          </div>
          <div class="panel-body">
            <div class="form-group">
              <label class="control-label" for="input-title">{{ entry_title }}</label>
              <input type="text" name="filter_title" value="{{ filter_title }}" placeholder="{{ entry_title }}" id="input-title" class="form-control" />
            </div>
            <div class="form-group">
              <label class="control-label" for="input-assignee">{{ entry_assignee }}</label>
              <input type="text" name="filter_assignee" value="{{ filter_assignee }}" placeholder="{{ entry_assignee }}" id="input-assignee" class="form-control" />
            </div>
            <div class="form-group">
              <label class="control-label" for="input-priority">{{ entry_priority }}</label>
              <select name="filter_priority" id="input-priority" class="form-control">
                <option value=""></option>
                <option value="low"{% if filter_priority == 'low' %} selected="selected"{% endif %}>{{ text_priority_low }}</option>
                <option value="medium"{% if filter_priority == 'medium' %} selected="selected"{% endif %}>{{ text_priority_medium }}</option>
                <option value="high"{% if filter_priority == 'high' %} selected="selected"{% endif %}>{{ text_priority_high }}</option>
                <option value="urgent"{% if filter_priority == 'urgent' %} selected="selected"{% endif %}>{{ text_priority_urgent }}</option>
              </select>
            </div>
            <div class="form-group">
              <label class="control-label" for="input-status">{{ entry_status }}</label>
              <select name="filter_status" id="input-status" class="form-control">
                <option value=""></option>
                <option value="pending"{% if filter_status == 'pending' %} selected="selected"{% endif %}>{{ text_status_pending }}</option>
                <option value="in_progress"{% if filter_status == 'in_progress' %} selected="selected"{% endif %}>{{ text_status_in_progress }}</option>
                <option value="review"{% if filter_status == 'review' %} selected="selected"{% endif %}>{{ text_status_review }}</option>
                <option value="completed"{% if filter_status == 'completed' %} selected="selected"{% endif %}>{{ text_status_completed }}</option>
                <option value="cancelled"{% if filter_status == 'cancelled' %} selected="selected"{% endif %}>{{ text_status_cancelled }}</option>
              </select>
            </div>
            <div class="form-group">
              <label class="control-label" for="input-due-date">{{ entry_due_date }}</label>
              <div class="input-group date">
                <input type="text" name="filter_due_date" value="{{ filter_due_date }}" placeholder="{{ entry_due_date }}" data-date-format="YYYY-MM-DD" id="input-due-date" class="form-control" />
                <span class="input-group-btn">
                <button class="btn btn-default" type="button"><i class="fa fa-calendar"></i></button>
                </span></div>
            </div>
            <div class="form-group text-right">
              <button type="button" id="button-filter" class="btn btn-default"><i class="fa fa-filter"></i> {{ button_filter }}</button>
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-9 col-md-pull-3 col-sm-12">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-list"></i> {{ text_list }}</h3>
          </div>
          <div class="panel-body">
            <form method="post" action="{{ delete }}" enctype="multipart/form-data" id="form-task">
              <div class="table-responsive">
                <table class="table table-bordered table-hover">
                  <thead>
                    <tr>
                      <td style="width: 1px;" class="text-center"><input type="checkbox" onclick="$('input[name*=\'selected\']').prop('checked', this.checked);" /></td>
                      <td class="text-left">{% if sort == 'title' %}
                        <a href="{{ sort_title }}" class="{{ order|lower }}">{{ column_title }}</a>
                        {% else %}
                        <a href="{{ sort_title }}">{{ column_title }}</a>
                        {% endif %}</td>
                      <td class="text-left">{% if sort == 'assignee' %}
                        <a href="{{ sort_assignee }}" class="{{ order|lower }}">{{ column_assignee }}</a>
                        {% else %}
                        <a href="{{ sort_assignee }}">{{ column_assignee }}</a>
                        {% endif %}</td>
                      <td class="text-left">{% if sort == 'priority' %}
                        <a href="{{ sort_priority }}" class="{{ order|lower }}">{{ column_priority }}</a>
                        {% else %}
                        <a href="{{ sort_priority }}">{{ column_priority }}</a>
                        {% endif %}</td>
                      <td class="text-left">{% if sort == 'status' %}
                        <a href="{{ sort_status }}" class="{{ order|lower }}">{{ column_status }}</a>
                        {% else %}
                        <a href="{{ sort_status }}">{{ column_status }}</a>
                        {% endif %}</td>
                      <td class="text-left">{{ column_progress }}</td>
                      <td class="text-left">{% if sort == 'due_date' %}
                        <a href="{{ sort_due_date }}" class="{{ order|lower }}">{{ column_due_date }}</a>
                        {% else %}
                        <a href="{{ sort_due_date }}">{{ column_due_date }}</a>
                        {% endif %}</td>
                      <td class="text-right">{{ column_action }}</td>
                    </tr>
                  </thead>
                  <tbody id="tasks-tbody">
                    <!-- سيتم تحميل البيانات عبر AJAX -->
                  </tbody>
                </table>
              </div>
            </form>
            <div class="row">
              <div class="col-sm-6 text-left" id="pagination">
                <!-- سيتم تحميل الترقيم عبر AJAX -->
              </div>
              <div class="col-sm-6 text-right" id="results">
                <!-- سيتم تحميل النتائج عبر AJAX -->
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript">
$(document).ready(function() {
    loadTasks();
});

$('#button-filter').on('click', function() {
    loadTasks();
});

function loadTasks(page = 1) {
    var filter_data = {
        filter_title: $('input[name=\'filter_title\']').val(),
        filter_assignee: $('input[name=\'filter_assignee\']').val(),
        filter_priority: $('select[name=\'filter_priority\']').val(),
        filter_status: $('select[name=\'filter_status\']').val(),
        filter_due_date: $('input[name=\'filter_due_date\']').val(),
        start: (page - 1) * 20,
        limit: 20
    };
    
    $.ajax({
        url: 'index.php?route=workflow/task/getList&user_token={{ user_token }}',
        type: 'GET',
        data: filter_data,
        dataType: 'json',
        success: function(json) {
            var html = '';
            
            if (json.tasks && json.tasks.length > 0) {
                $.each(json.tasks, function(i, task) {
                    html += '<tr>';
                    html += '<td class="text-center"><input type="checkbox" name="selected[]" value="' + task.task_id + '" /></td>';
                    html += '<td class="text-left">' + task.title + '</td>';
                    html += '<td class="text-left">' + task.assignee_name + '</td>';
                    html += '<td class="text-left"><span class="label label-' + getPriorityClass(task.priority) + '">' + task.priority_text + '</span></td>';
                    html += '<td class="text-left"><span class="label label-' + getStatusClass(task.status) + '">' + task.status_text + '</span></td>';
                    html += '<td class="text-left">';
                    html += '<div class="progress" style="margin-bottom: 0;">';
                    html += '<div class="progress-bar" role="progressbar" style="width: ' + task.progress + '%;">' + task.progress + '%</div>';
                    html += '</div>';
                    html += '</td>';
                    html += '<td class="text-left">' + task.due_date + '</td>';
                    html += '<td class="text-right">';
                    html += '<a href="' + task.view + '" data-toggle="tooltip" title="{{ button_view }}" class="btn btn-primary"><i class="fa fa-eye"></i></a> ';
                    html += '<a href="' + task.edit + '" data-toggle="tooltip" title="{{ button_edit }}" class="btn btn-info"><i class="fa fa-pencil"></i></a>';
                    html += '</td>';
                    html += '</tr>';
                });
            } else {
                html = '<tr><td class="text-center" colspan="8">{{ text_no_results }}</td></tr>';
            }
            
            $('#tasks-tbody').html(html);
        },
        error: function() {
            alert('{{ error_load_failed }}');
        }
    });
}

function getPriorityClass(priority) {
    switch(priority) {
        case 'low':
            return 'default';
        case 'medium':
            return 'info';
        case 'high':
            return 'warning';
        case 'urgent':
            return 'danger';
        default:
            return 'default';
    }
}

function getStatusClass(status) {
    switch(status) {
        case 'pending':
            return 'warning';
        case 'in_progress':
            return 'info';
        case 'review':
            return 'primary';
        case 'completed':
            return 'success';
        case 'cancelled':
            return 'danger';
        default:
            return 'default';
    }
}

$('.date').datetimepicker({
    pickTime: false
});
</script>
{{ footer }} 