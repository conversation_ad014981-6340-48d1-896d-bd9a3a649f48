{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="button" data-toggle="tooltip" title="{{ button_test_notification }}" class="btn btn-info" onclick="$('#test-modal').modal('show');"><i class="fa fa-flask"></i></button>
        <button type="button" data-toggle="tooltip" title="{{ button_view_logs }}" class="btn btn-warning" onclick="window.location='{{ logs_url }}';"><i class="fa fa-list"></i></button>
        <button type="button" data-toggle="tooltip" title="{{ button_view_analytics }}" class="btn btn-success" onclick="window.location='{{ analytics_url }}';"><i class="fa fa-bar-chart"></i></button>
        <button type="submit" form="form-notification-settings" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fa fa-save"></i></button>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    {% if success %}
    <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pencil"></i> {{ text_edit }}</h3>
      </div>
      <div class="panel-body">
        <form action="{{ action }}" method="post" enctype="multipart/form-data" id="form-notification-settings" class="form-horizontal">
          
          <!-- Navigation Tabs -->
          <ul class="nav nav-tabs">
            <li class="active"><a href="#tab-general" data-toggle="tab">{{ tab_general }}</a></li>
            <li><a href="#tab-email" data-toggle="tab">{{ tab_email }}</a></li>
            <li><a href="#tab-sms" data-toggle="tab">{{ tab_sms }}</a></li>
            <li><a href="#tab-push" data-toggle="tab">{{ tab_push }}</a></li>
            <li><a href="#tab-events" data-toggle="tab">{{ tab_events }}</a></li>
            <li><a href="#tab-templates" data-toggle="tab">{{ tab_templates }}</a></li>
            <li><a href="#tab-rules" data-toggle="tab">{{ tab_rules }}</a></li>
            <li><a href="#tab-escalation" data-toggle="tab">{{ tab_escalation }}</a></li>
            <li><a href="#tab-digest" data-toggle="tab">{{ tab_digest }}</a></li>
          </ul>
          
          <div class="tab-content">
            
            <!-- General Settings Tab -->
            <div class="tab-pane active" id="tab-general">
              <div class="row">
                <div class="col-sm-6">
                  <div class="form-group">
                    <label class="col-sm-4 control-label" for="input-notification-enabled">{{ entry_notification_enabled }}</label>
                    <div class="col-sm-8">
                      <select name="purchase_notification_enabled" id="input-notification-enabled" class="form-control">
                        {% if notification_enabled %}
                        <option value="1" selected="selected">{{ text_enabled }}</option>
                        <option value="0">{{ text_disabled }}</option>
                        {% else %}
                        <option value="1">{{ text_enabled }}</option>
                        <option value="0" selected="selected">{{ text_disabled }}</option>
                        {% endif %}
                      </select>
                      <div class="help-block">{{ help_notification_enabled }}</div>
                    </div>
                  </div>
                  
                  <div class="form-group">
                    <label class="col-sm-4 control-label" for="input-email-enabled">{{ entry_email_enabled }}</label>
                    <div class="col-sm-8">
                      <select name="purchase_notification_email_enabled" id="input-email-enabled" class="form-control">
                        {% if email_enabled %}
                        <option value="1" selected="selected">{{ text_enabled }}</option>
                        <option value="0">{{ text_disabled }}</option>
                        {% else %}
                        <option value="1">{{ text_enabled }}</option>
                        <option value="0" selected="selected">{{ text_disabled }}</option>
                        {% endif %}
                      </select>
                      <div class="help-block">{{ help_email_enabled }}</div>
                    </div>
                  </div>
                  
                  <div class="form-group">
                    <label class="col-sm-4 control-label" for="input-sms-enabled">{{ entry_sms_enabled }}</label>
                    <div class="col-sm-8">
                      <select name="purchase_notification_sms_enabled" id="input-sms-enabled" class="form-control">
                        {% if sms_enabled %}
                        <option value="1" selected="selected">{{ text_enabled }}</option>
                        <option value="0">{{ text_disabled }}</option>
                        {% else %}
                        <option value="1">{{ text_enabled }}</option>
                        <option value="0" selected="selected">{{ text_disabled }}</option>
                        {% endif %}
                      </select>
                      <div class="help-block">{{ help_sms_enabled }}</div>
                    </div>
                  </div>
                </div>
                
                <div class="col-sm-6">
                  <div class="form-group">
                    <label class="col-sm-4 control-label" for="input-push-enabled">{{ entry_push_enabled }}</label>
                    <div class="col-sm-8">
                      <select name="purchase_notification_push_enabled" id="input-push-enabled" class="form-control">
                        {% if push_enabled %}
                        <option value="1" selected="selected">{{ text_enabled }}</option>
                        <option value="0">{{ text_disabled }}</option>
                        {% else %}
                        <option value="1">{{ text_enabled }}</option>
                        <option value="0" selected="selected">{{ text_disabled }}</option>
                        {% endif %}
                      </select>
                      <div class="help-block">{{ help_push_enabled }}</div>
                    </div>
                  </div>
                  
                  <div class="form-group">
                    <label class="col-sm-4 control-label" for="input-internal-enabled">{{ entry_internal_enabled }}</label>
                    <div class="col-sm-8">
                      <select name="purchase_notification_internal_enabled" id="input-internal-enabled" class="form-control">
                        {% if internal_enabled %}
                        <option value="1" selected="selected">{{ text_enabled }}</option>
                        <option value="0">{{ text_disabled }}</option>
                        {% else %}
                        <option value="1">{{ text_enabled }}</option>
                        <option value="0" selected="selected">{{ text_disabled }}</option>
                        {% endif %}
                      </select>
                      <div class="help-block">{{ help_internal_enabled }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Email Settings Tab -->
            <div class="tab-pane" id="tab-email">
              <div class="row">
                <div class="col-sm-6">
                  <div class="form-group required">
                    <label class="col-sm-4 control-label" for="input-email-from-name">{{ entry_email_from_name }}</label>
                    <div class="col-sm-8">
                      <input type="text" name="purchase_notification_email_from_name" value="{{ email_from_name }}" placeholder="{{ entry_email_from_name }}" id="input-email-from-name" class="form-control" />
                      <div class="help-block">{{ help_email_from_name }}</div>
                    </div>
                  </div>
                  
                  <div class="form-group required">
                    <label class="col-sm-4 control-label" for="input-email-from-address">{{ entry_email_from_address }}</label>
                    <div class="col-sm-8">
                      <input type="email" name="purchase_notification_email_from_address" value="{{ email_from_address }}" placeholder="{{ entry_email_from_address }}" id="input-email-from-address" class="form-control" />
                      <div class="help-block">{{ help_email_from_address }}</div>
                    </div>
                  </div>
                </div>
                
                <div class="col-sm-6">
                  <div class="form-group">
                    <label class="col-sm-4 control-label" for="input-email-reply-to">{{ entry_email_reply_to }}</label>
                    <div class="col-sm-8">
                      <input type="email" name="purchase_notification_email_reply_to" value="{{ email_reply_to }}" placeholder="{{ entry_email_reply_to }}" id="input-email-reply-to" class="form-control" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- SMS Settings Tab -->
            <div class="tab-pane" id="tab-sms">
              <div class="row">
                <div class="col-sm-6">
                  <div class="form-group">
                    <label class="col-sm-4 control-label" for="input-sms-provider">{{ entry_sms_provider }}</label>
                    <div class="col-sm-8">
                      <select name="purchase_notification_sms_provider" id="input-sms-provider" class="form-control">
                        {% for provider_key, provider_name in sms_providers %}
                        {% if provider_key == sms_provider %}
                        <option value="{{ provider_key }}" selected="selected">{{ provider_name }}</option>
                        {% else %}
                        <option value="{{ provider_key }}">{{ provider_name }}</option>
                        {% endif %}
                        {% endfor %}
                      </select>
                      <div class="help-block">{{ help_sms_provider }}</div>
                    </div>
                  </div>
                  
                  <div class="form-group">
                    <label class="col-sm-4 control-label" for="input-sms-api-key">{{ entry_sms_api_key }}</label>
                    <div class="col-sm-8">
                      <input type="text" name="purchase_notification_sms_api_key" value="{{ sms_api_key }}" placeholder="{{ entry_sms_api_key }}" id="input-sms-api-key" class="form-control" />
                    </div>
                  </div>
                </div>
                
                <div class="col-sm-6">
                  <div class="form-group">
                    <label class="col-sm-4 control-label" for="input-sms-api-secret">{{ entry_sms_api_secret }}</label>
                    <div class="col-sm-8">
                      <input type="password" name="purchase_notification_sms_api_secret" value="{{ sms_api_secret }}" placeholder="{{ entry_sms_api_secret }}" id="input-sms-api-secret" class="form-control" />
                    </div>
                  </div>
                  
                  <div class="form-group">
                    <label class="col-sm-4 control-label" for="input-sms-from-number">{{ entry_sms_from_number }}</label>
                    <div class="col-sm-8">
                      <input type="text" name="purchase_notification_sms_from_number" value="{{ sms_from_number }}" placeholder="{{ entry_sms_from_number }}" id="input-sms-from-number" class="form-control" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Push Notifications Tab -->
            <div class="tab-pane" id="tab-push">
              <div class="row">
                <div class="col-sm-6">
                  <div class="form-group">
                    <label class="col-sm-4 control-label" for="input-push-provider">{{ entry_push_provider }}</label>
                    <div class="col-sm-8">
                      <select name="purchase_notification_push_provider" id="input-push-provider" class="form-control">
                        {% for provider_key, provider_name in push_providers %}
                        {% if provider_key == push_provider %}
                        <option value="{{ provider_key }}" selected="selected">{{ provider_name }}</option>
                        {% else %}
                        <option value="{{ provider_key }}">{{ provider_name }}</option>
                        {% endif %}
                        {% endfor %}
                      </select>
                      <div class="help-block">{{ help_push_provider }}</div>
                    </div>
                  </div>
                  
                  <div class="form-group">
                    <label class="col-sm-4 control-label" for="input-push-api-key">{{ entry_push_api_key }}</label>
                    <div class="col-sm-8">
                      <input type="text" name="purchase_notification_push_api_key" value="{{ push_api_key }}" placeholder="{{ entry_push_api_key }}" id="input-push-api-key" class="form-control" />
                    </div>
                  </div>
                </div>
                
                <div class="col-sm-6">
                  <div class="form-group">
                    <label class="col-sm-4 control-label" for="input-push-app-id">{{ entry_push_app_id }}</label>
                    <div class="col-sm-8">
                      <input type="text" name="purchase_notification_push_app_id" value="{{ push_app_id }}" placeholder="{{ entry_push_app_id }}" id="input-push-app-id" class="form-control" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Notification Events Tab -->
            <div class="tab-pane" id="tab-events">
              <div class="form-group">
                <div class="col-sm-12">
                  <button type="button" class="btn btn-primary" onclick="addNotificationEvent();"><i class="fa fa-plus"></i> {{ button_add_event }}</button>
                </div>
              </div>
              
              <div class="table-responsive">
                <table class="table table-striped table-bordered table-hover" id="notification-events-table">
                  <thead>
                    <tr>
                      <td class="text-left">{{ column_event_name }}</td>
                      <td class="text-left">{{ column_event_type }}</td>
                      <td class="text-left">{{ column_delivery_methods }}</td>
                      <td class="text-left">{{ column_recipients }}</td>
                      <td class="text-left">{{ column_template }}</td>
                      <td class="text-center">{{ column_priority }}</td>
                      <td class="text-center">{{ column_status }}</td>
                      <td class="text-center">{{ column_action }}</td>
                    </tr>
                  </thead>
                  <tbody>
                    {% set event_row = 0 %}
                    {% for event in notification_events %}
                    <tr id="event-row{{ event_row }}">
                      <td class="text-left">
                        <input type="text" name="notification_events[{{ event_row }}][event_name]" value="{{ event.event_name }}" placeholder="{{ entry_event_name }}" class="form-control" />
                      </td>
                      <td class="text-left">
                        <select name="notification_events[{{ event_row }}][event_type]" class="form-control">
                          <option value="purchase_order_created" {% if event.event_type == 'purchase_order_created' %}selected="selected"{% endif %}>{{ event_purchase_order_created }}</option>
                          <option value="purchase_order_approved" {% if event.event_type == 'purchase_order_approved' %}selected="selected"{% endif %}>{{ event_purchase_order_approved }}</option>
                          <option value="purchase_order_rejected" {% if event.event_type == 'purchase_order_rejected' %}selected="selected"{% endif %}>{{ event_purchase_order_rejected }}</option>
                          <option value="purchase_order_delivered" {% if event.event_type == 'purchase_order_delivered' %}selected="selected"{% endif %}>{{ event_purchase_order_delivered }}</option>
                          <option value="budget_exceeded" {% if event.event_type == 'budget_exceeded' %}selected="selected"{% endif %}>{{ event_budget_exceeded }}</option>
                        </select>
                      </td>
                      <td class="text-left">
                        <div class="checkbox-group">
                          <label><input type="checkbox" name="notification_events[{{ event_row }}][delivery_methods][]" value="email" {% if 'email' in event.delivery_methods %}checked{% endif %}> {{ delivery_email }}</label><br>
                          <label><input type="checkbox" name="notification_events[{{ event_row }}][delivery_methods][]" value="sms" {% if 'sms' in event.delivery_methods %}checked{% endif %}> {{ delivery_sms }}</label><br>
                          <label><input type="checkbox" name="notification_events[{{ event_row }}][delivery_methods][]" value="push" {% if 'push' in event.delivery_methods %}checked{% endif %}> {{ delivery_push }}</label><br>
                          <label><input type="checkbox" name="notification_events[{{ event_row }}][delivery_methods][]" value="internal" {% if 'internal' in event.delivery_methods %}checked{% endif %}> {{ delivery_internal }}</label>
                        </div>
                      </td>
                      <td class="text-left">
                        <select name="notification_events[{{ event_row }}][recipients][]" class="form-control" multiple>
                          <option value="requester" {% if 'requester' in event.recipients %}selected{% endif %}>{{ recipient_requester }}</option>
                          <option value="approver" {% if 'approver' in event.recipients %}selected{% endif %}>{{ recipient_approver }}</option>
                          <option value="manager" {% if 'manager' in event.recipients %}selected{% endif %}>{{ recipient_manager }}</option>
                          <option value="finance" {% if 'finance' in event.recipients %}selected{% endif %}>{{ recipient_finance }}</option>
                          <option value="supplier" {% if 'supplier' in event.recipients %}selected{% endif %}>{{ recipient_supplier }}</option>
                        </select>
                      </td>
                      <td class="text-left">
                        <select name="notification_events[{{ event_row }}][template_id]" class="form-control">
                          <option value="0">{{ text_none }}</option>
                          {% for template in notification_templates %}
                          {% if template.template_id == event.template_id %}
                          <option value="{{ template.template_id }}" selected="selected">{{ template.name }}</option>
                          {% else %}
                          <option value="{{ template.template_id }}">{{ template.name }}</option>
                          {% endif %}
                          {% endfor %}
                        </select>
                      </td>
                      <td class="text-center">
                        <select name="notification_events[{{ event_row }}][priority]" class="form-control">
                          <option value="low" {% if event.priority == 'low' %}selected{% endif %}>{{ priority_low }}</option>
                          <option value="normal" {% if event.priority == 'normal' %}selected{% endif %}>{{ priority_normal }}</option>
                          <option value="high" {% if event.priority == 'high' %}selected{% endif %}>{{ priority_high }}</option>
                          <option value="urgent" {% if event.priority == 'urgent' %}selected{% endif %}>{{ priority_urgent }}</option>
                        </select>
                      </td>
                      <td class="text-center">
                        <select name="notification_events[{{ event_row }}][status]" class="form-control">
                          {% if event.status %}
                          <option value="1" selected="selected">{{ text_enabled }}</option>
                          <option value="0">{{ text_disabled }}</option>
                          {% else %}
                          <option value="1">{{ text_enabled }}</option>
                          <option value="0" selected="selected">{{ text_disabled }}</option>
                          {% endif %}
                        </select>
                      </td>
                      <td class="text-center">
                        <button type="button" onclick="removeEvent({{ event_row }});" data-toggle="tooltip" title="{{ button_remove }}" class="btn btn-danger btn-sm"><i class="fa fa-minus-circle"></i></button>
                      </td>
                    </tr>
                    {% set event_row = event_row + 1 %}
                    {% endfor %}
                  </tbody>
                </table>
              </div>
            </div>
            
            <!-- Additional tabs would continue here... -->
            
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<!-- Test Notification Modal -->
<div id="test-modal" class="modal fade" tabindex="-1" role="dialog">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title">{{ button_test_notification }}</h4>
      </div>
      <div class="modal-body">
        <form id="test-form">
          <div class="form-group">
            <label for="test-type">{{ entry_test_type }}</label>
            <select id="test-type" class="form-control" required>
              <option value="purchase_order_created">{{ event_purchase_order_created }}</option>
              <option value="purchase_order_approved">{{ event_purchase_order_approved }}</option>
              <option value="budget_exceeded">{{ event_budget_exceeded }}</option>
            </select>
          </div>
          <div class="form-group">
            <label for="test-method">{{ entry_test_method }}</label>
            <select id="test-method" class="form-control" required>
              <option value="email">{{ delivery_email }}</option>
              <option value="sms">{{ delivery_sms }}</option>
              <option value="push">{{ delivery_push }}</option>
              <option value="internal">{{ delivery_internal }}</option>
            </select>
          </div>
          <div class="form-group">
            <label for="test-recipient">{{ entry_test_recipient }}</label>
            <input type="text" id="test-recipient" class="form-control" placeholder="<EMAIL> or +1234567890" required />
          </div>
          <div class="form-group">
            <label for="test-message">{{ entry_test_message }}</label>
            <textarea id="test-message" class="form-control" rows="3" placeholder="{{ entry_test_message }}">Test notification from AYM ERP Purchase System</textarea>
          </div>
        </form>
        <div id="test-results" style="display: none;">
          <h5>{{ text_test_result }}</h5>
          <div id="test-results-content"></div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-primary" onclick="runNotificationTest();">{{ button_send_test }}</button>
        <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_close }}</button>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript"><!--
var event_row = {{ notification_events|length }};

// Add notification event
function addNotificationEvent() {
    html = '<tr id="event-row' + event_row + '">';
    html += '  <td class="text-left"><input type="text" name="notification_events[' + event_row + '][event_name]" placeholder="{{ entry_event_name }}" class="form-control" /></td>';
    html += '  <td class="text-left"><select name="notification_events[' + event_row + '][event_type]" class="form-control"><option value="purchase_order_created">{{ event_purchase_order_created }}</option><option value="purchase_order_approved">{{ event_purchase_order_approved }}</option></select></td>';
    html += '  <td class="text-left"><div class="checkbox-group"><label><input type="checkbox" name="notification_events[' + event_row + '][delivery_methods][]" value="email"> {{ delivery_email }}</label><br><label><input type="checkbox" name="notification_events[' + event_row + '][delivery_methods][]" value="sms"> {{ delivery_sms }}</label></div></td>';
    html += '  <td class="text-left"><select name="notification_events[' + event_row + '][recipients][]" class="form-control" multiple><option value="requester">{{ recipient_requester }}</option><option value="approver">{{ recipient_approver }}</option></select></td>';
    html += '  <td class="text-left"><select name="notification_events[' + event_row + '][template_id]" class="form-control"><option value="0">{{ text_none }}</option></select></td>';
    html += '  <td class="text-center"><select name="notification_events[' + event_row + '][priority]" class="form-control"><option value="normal" selected>{{ priority_normal }}</option></select></td>';
    html += '  <td class="text-center"><select name="notification_events[' + event_row + '][status]" class="form-control"><option value="1" selected>{{ text_enabled }}</option><option value="0">{{ text_disabled }}</option></select></td>';
    html += '  <td class="text-center"><button type="button" onclick="removeEvent(' + event_row + ');" class="btn btn-danger btn-sm"><i class="fa fa-minus-circle"></i></button></td>';
    html += '</tr>';
    
    $('#notification-events-table tbody').append(html);
    
    event_row++;
}

function removeEvent(row) {
    $('#event-row' + row).remove();
}

// Test notification
function runNotificationTest() {
    var notification_type = $('#test-type').val();
    var delivery_method = $('#test-method').val();
    var recipient = $('#test-recipient').val();
    var test_message = $('#test-message').val();
    
    if (!notification_type || !delivery_method || !recipient) {
        alert('{{ error_test_data }}');
        return;
    }
    
    $.ajax({
        url: 'index.php?route=purchase/notification_settings/test&user_token={{ user_token }}',
        type: 'post',
        data: {
            notification_type: notification_type,
            delivery_method: delivery_method,
            recipient: recipient,
            test_message: test_message
        },
        dataType: 'json',
        success: function(json) {
            if (json.success) {
                var html = '<div class="alert alert-success">' + json.message + '</div>';
                if (json.details) {
                    html += '<p><strong>Method:</strong> ' + json.details.method + '</p>';
                    html += '<p><strong>Recipient:</strong> ' + json.details.recipient + '</p>';
                }
                $('#test-results-content').html(html);
                $('#test-results').show();
            } else {
                $('#test-results-content').html('<div class="alert alert-danger">' + json.error + '</div>');
                $('#test-results').show();
            }
        },
        error: function(xhr, ajaxOptions, thrownError) {
            alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
        }
    });
}

// Initialize tooltips
$('[data-toggle="tooltip"]').tooltip();
//--></script>

{{ footer }}
