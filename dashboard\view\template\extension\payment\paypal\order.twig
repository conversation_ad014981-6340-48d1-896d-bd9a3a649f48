<div class="transaction-id">{{ text_transaction_id }}: <a href="{{ transaction_url }}" target="_blank">{{ transaction_id }}</a></div><br />
<div class="transaction-status">{{ attribute(_context, 'text_transaction_' ~ transaction_status) }}</div><br />
{% if (transaction_status == 'created') %}
<button type="button" class="btn btn-primary button-capture-payment">{{ button_capture_payment }}</button>
<button type="button" class="btn btn-primary button-reauthorize-payment">{{ button_reauthorize_payment }}</button>
<button type="button" class="btn btn-primary button-void-payment">{{ button_void_payment }}</button>
{% endif %}
{% if (transaction_status == 'completed') %}
<button type="button" class="btn btn-primary button-refund-payment">{{ button_refund_payment }}</button>
{% endif %}
<script type="text/javascript">

$('#tab-paypal').on('click', '.button-capture-payment', function() {
	$.ajax({
		type: 'post',
		url: '{{ capture_url }}',
		data: {'order_id' : '{{ order_id }}', 'transaction_id' : '{{ transaction_id }}'},
		dataType: 'json',
		beforeSend: function() {
			$('#tab-paypal .btn').prop('disabled', true);
		},
		complete: function() {
			$('#tab-paypal .btn').prop('disabled', false);
		},
		success: function(json) {
			$('.alert-dismissible').remove();
			
			if (json['error'] && json['error']['warning']) {
				$('#content > .container-fluid').prepend('<div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> ' + json['error']['warning'] + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
				
				$('html, body').animate({scrollTop: $('#content > .container-fluid .alert-danger').offset().top}, 'slow');
			}
			
			if (json['success']) {
				$('#content > .container-fluid').prepend('<div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> ' + json['success'] + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
				
				$('html, body').animate({scrollTop: $('#content > .container-fluid .alert-success').offset().top}, 'slow');
				
				$('#tab-paypal').load('{{ info_url }}');
			}
		},
		error: function(xhr, ajaxOptions, thrownError) {
			console.log(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
		}
	});
});


$('#tab-paypal').on('click', '.button-reauthorize-payment', function() {
	$.ajax({
		type: 'post',
		url: '{{ reauthorize_url }}',
		data: {'order_id' : '{{ order_id }}', 'transaction_id' : '{{ transaction_id }}'},
		dataType: 'json',
		beforeSend: function() {
			$('#tab-paypal .btn').prop('disabled', true);
		},
		complete: function() {
			$('#tab-paypal .btn').prop('disabled', false);
		},
		success: function(json) {
			$('.alert-dismissible').remove();
			
			if (json['error'] && json['error']['warning']) {
				$('#content > .container-fluid').prepend('<div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> ' + json['error']['warning'] + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
				
				$('html, body').animate({scrollTop: $('#content > .container-fluid .alert-danger').offset().top}, 'slow');
			}
			
			if (json['success']) {
				$('#content > .container-fluid').prepend('<div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> ' + json['success'] + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
				
				$('html, body').animate({scrollTop: $('#content > .container-fluid .alert-success').offset().top}, 'slow');
				
				$('#tab-paypal').load('{{ info_url }}');
			}
		},
		error: function(xhr, ajaxOptions, thrownError) {
			console.log(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
		}
	});
});


$('#tab-paypal').on('click', '.button-void-payment', function() {
	$.ajax({
		type: 'post',
		url: '{{ void_url }}',
		data: {'order_id' : '{{ order_id }}', 'transaction_id' : '{{ transaction_id }}'},
		dataType: 'json',
		beforeSend: function() {
			$('#tab-paypal .btn').prop('disabled', true);
		},
		complete: function() {
			$('#tab-paypal .btn').prop('disabled', false);
		},
		success: function(json) {
			$('.alert-dismissible').remove();
			
			if (json['error'] && json['error']['warning']) {
				$('#content > .container-fluid').prepend('<div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> ' + json['error']['warning'] + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
				
				$('html, body').animate({scrollTop: $('#content > .container-fluid .alert-danger').offset().top}, 'slow');
			}
			
			if (json['success']) {
				$('#content > .container-fluid').prepend('<div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> ' + json['success'] + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
				
				$('html, body').animate({scrollTop: $('#content > .container-fluid .alert-success').offset().top}, 'slow');
				
				$('#tab-paypal').load('{{ info_url }}');
			}
		},
		error: function(xhr, ajaxOptions, thrownError) {
			console.log(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
		}
	});
});

$('#tab-paypal').on('click', '.button-refund-payment', function() {
	$.ajax({
		type: 'post',
		url: '{{ refund_url }}',
		data: {'order_id' : '{{ order_id }}', 'transaction_id' : '{{ transaction_id }}'},
		dataType: 'json',
		beforeSend: function() {
			$('#tab-paypal .btn').prop('disabled', true);
		},
		complete: function() {
			$('#tab-paypal .btn').prop('disabled', false);
		},
		success: function(json) {
			$('.alert-dismissible').remove();
			
			if (json['error'] && json['error']['warning']) {
				$('#content > .container-fluid').prepend('<div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> ' + json['error']['warning'] + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
				
				$('html, body').animate({scrollTop: $('#content > .container-fluid .alert-danger').offset().top}, 'slow');
			}
			
			if (json['success']) {
				$('#content > .container-fluid').prepend('<div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> ' + json['success'] + ' <button type="button" class="close" data-dismiss="alert">&times;</button></div>');
				
				$('html, body').animate({ scrollTop: $('#content > .container-fluid .alert-success').offset().top}, 'slow');
				
				$('#tab-paypal').load('{{ info_url }}');
			}
		},
		error: function(xhr, ajaxOptions, thrownError) {
			console.log(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
		}
	});
});

</script>