<?php
// Heading
$_['heading_title']                      = 'Messaging System';

// Text
$_['text_success']                       = 'Success: You have modified your message!';
$_['text_list']                          = 'Message List';
$_['text_add']                           = 'Add Message';
$_['text_view']                          = 'View Message';
$_['text_default']                       = 'Default';
$_['text_no_results']                    = 'No messages to display';
$_['text_conversation']                  = 'Conversation';
$_['text_reply']                         = 'Reply';
$_['text_upload']                        = 'Your file has been successfully uploaded!';
$_['text_draft_saved']                   = 'Success: Your draft has been saved!';
$_['text_draft_updated']                 = 'Success: Your draft has been updated!';
$_['text_read']                          = 'Read';
$_['text_unread']                        = 'Unread';
$_['text_sent']                          = 'Sent';
$_['text_received']                      = 'Received';
$_['text_attachments']                   = 'Attachments';
$_['text_add_attachment']                = 'Add Attachment';
$_['text_message_sent']                  = 'Success: Your message has been sent!';
$_['text_reply_sent']                    = 'Success: Your reply has been sent!';
$_['text_message']                       = 'Message';
$_['text_from']                          = 'From';
$_['text_to']                            = 'To';
$_['text_date']                          = 'Date';
$_['text_loading']                       = 'Loading...';
$_['text_folders']                       = 'Folders';
$_['text_inbox']                         = 'Inbox';
$_['text_draft']                         = 'Drafts';
$_['text_archived']                      = 'Archived';
$_['text_all']                           = 'All Messages';
$_['text_compose']                       = 'Compose New Message';
$_['text_select']                        = '--- Please Select ---';
$_['text_conversations']                 = 'Conversations';
$_['text_message_history']               = 'Message History';
$_['text_notifications']                 = 'Notifications';
$_['text_notification_read']             = 'Success: Notification marked as read!';
$_['text_notification_deleted']          = 'Success: Notification deleted!';
$_['text_all_notifications_read']        = 'Success: All notifications marked as read!';
$_['text_marked_read']                   = 'Success: Message marked as read!';
$_['text_marked_unread']                 = 'Success: Message marked as unread!';
$_['text_archived']                      = 'Success: Message archived!';
$_['text_unarchived']                    = 'Success: Message moved back to inbox!';
$_['text_confirm']                       = 'Are you sure?';
$_['text_history']                       = 'Message History';
$_['text_conversation_created']          = 'Success: Conversation created!';
$_['text_conversation_updated']          = 'Success: Conversation updated!';
$_['text_conversation_deleted']          = 'Success: Conversation deleted!';
$_['text_member_added']                  = 'Success: Member added to conversation!';
$_['text_member_removed']                = 'Success: Member removed from conversation!';
$_['text_added_to_conversation']         = 'Added to Conversation';
$_['text_added_to_conversation_message'] = '%s has added you to the conversation "%s"';
$_['text_removed_from_conversation']     = 'Removed from Conversation';
$_['text_removed_from_conversation_message'] = 'You have been removed from the conversation "%s"';
$_['text_priority_low']                  = 'Low';
$_['text_priority_normal']               = 'Normal';
$_['text_priority_high']                 = 'High';
$_['text_create_conversation']           = 'Create New Conversation';
$_['text_manage_members']                = 'Manage Members';
$_['text_members']                       = 'Conversation Members';
$_['text_group_conversation']            = 'Group Conversation';
$_['text_private_conversation']          = 'Private Conversation';
$_['text_notification_settings']         = 'Notification Settings';

// Column
$_['column_subject']                     = 'Subject';
$_['column_sender']                      = 'Sender';
$_['column_recipient']                   = 'Recipient';
$_['column_from']                        = 'From';
$_['column_to']                          = 'To';
$_['column_date_added']                  = 'Date Added';
$_['column_date_modified']               = 'Date Modified';
$_['column_status']                      = 'Status';
$_['column_action']                      = 'Action';
$_['column_priority']                    = 'Priority';
$_['column_type']                        = 'Type';
$_['column_notification']                = 'Notification';

// Entry
$_['entry_subject']                      = 'Subject';
$_['entry_sender']                       = 'Sender';
$_['entry_recipient']                    = 'Recipient';
$_['entry_message']                      = 'Message';
$_['entry_attachment']                   = 'Attachment';
$_['entry_reply']                        = 'Reply';
$_['entry_status']                       = 'Status';
$_['entry_priority']                     = 'Priority';
$_['entry_title']                        = 'Conversation Title';
$_['entry_is_group']                     = 'Group Conversation';
$_['entry_members']                      = 'Members';
$_['entry_is_admin']                     = 'Administrator';
$_['entry_filter_subject']               = 'Filter by Subject';
$_['entry_filter_sender']                = 'Filter by Sender';
$_['entry_filter_recipient']             = 'Filter by Recipient';
$_['entry_filter_date_start']            = 'Filter by Date Start';
$_['entry_filter_date_end']              = 'Filter by Date End';
$_['entry_filter_priority']              = 'Filter by Priority';
$_['entry_filter_read']                  = 'Filter by Read Status';
$_['entry_filter_folder']                = 'Filter by Folder';
$_['entry_filter_conversation']          = 'Filter by Conversation';

// Button
$_['button_send']                        = 'Send Message';
$_['button_reply']                       = 'Reply';
$_['button_forward']                     = 'Forward';
$_['button_save_draft']                  = 'Save Draft';
$_['button_upload']                      = 'Upload';
$_['button_delete']                      = 'Delete';
$_['button_cancel']                      = 'Cancel';
$_['button_add']                         = 'Add Message';
$_['button_view']                        = 'View';
$_['button_edit']                        = 'Edit';
$_['button_archive']                     = 'Archive';
$_['button_unarchive']                   = 'Unarchive';
$_['button_compose']                     = 'Compose';
$_['button_mark_read']                   = 'Mark as Read';
$_['button_mark_unread']                 = 'Mark as Unread';
$_['button_filter']                      = 'Filter';
$_['button_reset']                       = 'Reset';
$_['button_back']                        = 'Back';
$_['button_refresh']                     = 'Refresh';
$_['button_mark_all_read']               = 'Mark All Read';
$_['button_create_conversation']         = 'Create Conversation';
$_['button_add_member']                  = 'Add Member';
$_['button_remove_member']               = 'Remove';
$_['button_leave_conversation']          = 'Leave Conversation';

// Error
$_['error_permission']                   = 'Warning: You do not have permission to modify messages!';
$_['error_subject']                      = 'Message Subject must be between 3 and 255 characters!';
$_['error_message']                      = 'Message must be at least 10 characters!';
$_['error_reply']                        = 'Reply must be at least 10 characters!';
$_['error_filename']                     = 'Filename must be between 3 and 128 characters!';
$_['error_filetype']                     = 'Invalid file type!';
$_['error_upload']                       = 'Upload required!';
$_['error_filesize']                     = 'File too large! Max 2MB allowed.';
$_['error_recipient']                    = 'Recipient required!';
$_['error_recipient_exists']             = 'Recipient does not exist!';
$_['error_conversation']                 = 'Conversation does not exist!';
$_['error_title']                        = 'Conversation title must be between 3 and 255 characters!';
$_['error_user']                         = 'User required!';
$_['error_user_exists']                  = 'User does not exist!';
$_['error_last_admin']                   = 'Cannot remove the last admin from a conversation!';
$_['error_notification']                 = 'Notification does not exist!';
$_['error_attachment']                   = 'Attachment does not exist!';
$_['error_file']                         = 'File does not exist!';
$_['error_message']                      = 'Message does not exist!'; 