{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="submit" form="crm\lead-form" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fas fa-save"></i></button>
        <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-light"><i class="fas fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    
    <div class="card">
      <div class="card-header"><i class="fas fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}chart-bar{% elif screen_type == 'settings' %}cog{% else %}pencil-alt{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="card-body">
        <form id="crm\lead-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-ajax_delete_url">{{ text_ajax_delete_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="ajax_delete_url" value="{{ ajax_delete_url }}" placeholder="{{ text_ajax_delete_url }}" id="input-ajax_delete_url" class="form-control" />
              {% if error_ajax_delete_url %}
                <div class="invalid-feedback">{{ error_ajax_delete_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-ajax_get_url">{{ text_ajax_get_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="ajax_get_url" value="{{ ajax_get_url }}" placeholder="{{ text_ajax_get_url }}" id="input-ajax_get_url" class="form-control" />
              {% if error_ajax_get_url %}
                <div class="invalid-feedback">{{ error_ajax_get_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-ajax_list_url">{{ text_ajax_list_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="ajax_list_url" value="{{ ajax_list_url }}" placeholder="{{ text_ajax_list_url }}" id="input-ajax_list_url" class="form-control" />
              {% if error_ajax_list_url %}
                <div class="invalid-feedback">{{ error_ajax_list_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-ajax_save_url">{{ text_ajax_save_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="ajax_save_url" value="{{ ajax_save_url }}" placeholder="{{ text_ajax_save_url }}" id="input-ajax_save_url" class="form-control" />
              {% if error_ajax_save_url %}
                <div class="invalid-feedback">{{ error_ajax_save_url }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-button_add_lead">{{ text_button_add_lead }}</label>
            <div class="col-sm-10">
              <input type="text" name="button_add_lead" value="{{ button_add_lead }}" placeholder="{{ text_button_add_lead }}" id="input-button_add_lead" class="form-control" />
              {% if error_button_add_lead %}
                <div class="invalid-feedback">{{ error_button_add_lead }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-button_close">{{ text_button_close }}</label>
            <div class="col-sm-10">
              <input type="text" name="button_close" value="{{ button_close }}" placeholder="{{ text_button_close }}" id="input-button_close" class="form-control" />
              {% if error_button_close %}
                <div class="invalid-feedback">{{ error_button_close }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-button_filter">{{ text_button_filter }}</label>
            <div class="col-sm-10">
              <input type="text" name="button_filter" value="{{ button_filter }}" placeholder="{{ text_button_filter }}" id="input-button_filter" class="form-control" />
              {% if error_button_filter %}
                <div class="invalid-feedback">{{ error_button_filter }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-button_reset">{{ text_button_reset }}</label>
            <div class="col-sm-10">
              <input type="text" name="button_reset" value="{{ button_reset }}" placeholder="{{ text_button_reset }}" id="input-button_reset" class="form-control" />
              {% if error_button_reset %}
                <div class="invalid-feedback">{{ error_button_reset }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-button_save">{{ text_button_save }}</label>
            <div class="col-sm-10">
              <input type="text" name="button_save" value="{{ button_save }}" placeholder="{{ text_button_save }}" id="input-button_save" class="form-control" />
              {% if error_button_save %}
                <div class="invalid-feedback">{{ error_button_save }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_actions">{{ text_column_actions }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_actions" value="{{ column_actions }}" placeholder="{{ text_column_actions }}" id="input-column_actions" class="form-control" />
              {% if error_column_actions %}
                <div class="invalid-feedback">{{ error_column_actions }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_company">{{ text_column_company }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_company" value="{{ column_company }}" placeholder="{{ text_column_company }}" id="input-column_company" class="form-control" />
              {% if error_column_company %}
                <div class="invalid-feedback">{{ error_column_company }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_email">{{ text_column_email }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_email" value="{{ column_email }}" placeholder="{{ text_column_email }}" id="input-column_email" class="form-control" />
              {% if error_column_email %}
                <div class="invalid-feedback">{{ error_column_email }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_name">{{ text_column_name }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_name" value="{{ column_name }}" placeholder="{{ text_column_name }}" id="input-column_name" class="form-control" />
              {% if error_column_name %}
                <div class="invalid-feedback">{{ error_column_name }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_phone">{{ text_column_phone }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_phone" value="{{ column_phone }}" placeholder="{{ text_column_phone }}" id="input-column_phone" class="form-control" />
              {% if error_column_phone %}
                <div class="invalid-feedback">{{ error_column_phone }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-column_status">{{ text_column_status }}</label>
            <div class="col-sm-10">
              <input type="text" name="column_status" value="{{ column_status }}" placeholder="{{ text_column_status }}" id="input-column_status" class="form-control" />
              {% if error_column_status %}
                <div class="invalid-feedback">{{ error_column_status }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-conversion_funnel">{{ text_conversion_funnel }}</label>
            <div class="col-sm-10">
              <input type="text" name="conversion_funnel" value="{{ conversion_funnel }}" placeholder="{{ text_conversion_funnel }}" id="input-conversion_funnel" class="form-control" />
              {% if error_conversion_funnel %}
                <div class="invalid-feedback">{{ error_conversion_funnel }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-crm_stats">{{ text_crm_stats }}</label>
            <div class="col-sm-10">
              <input type="text" name="crm_stats" value="{{ crm_stats }}" placeholder="{{ text_crm_stats }}" id="input-crm_stats" class="form-control" />
              {% if error_crm_stats %}
                <div class="invalid-feedback">{{ error_crm_stats }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="invalid-feedback">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-leads_by_stage">{{ text_leads_by_stage }}</label>
            <div class="col-sm-10">
              <input type="text" name="leads_by_stage" value="{{ leads_by_stage }}" placeholder="{{ text_leads_by_stage }}" id="input-leads_by_stage" class="form-control" />
              {% if error_leads_by_stage %}
                <div class="invalid-feedback">{{ error_leads_by_stage }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-performance_metrics">{{ text_performance_metrics }}</label>
            <div class="col-sm-10">
              <input type="text" name="performance_metrics" value="{{ performance_metrics }}" placeholder="{{ text_performance_metrics }}" id="input-performance_metrics" class="form-control" />
              {% if error_performance_metrics %}
                <div class="invalid-feedback">{{ error_performance_metrics }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-pipeline_data">{{ text_pipeline_data }}</label>
            <div class="col-sm-10">
              <input type="text" name="pipeline_data" value="{{ pipeline_data }}" placeholder="{{ text_pipeline_data }}" id="input-pipeline_data" class="form-control" />
              {% if error_pipeline_data %}
                <div class="invalid-feedback">{{ error_pipeline_data }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-pipeline_stages">{{ text_pipeline_stages }}</label>
            <div class="col-sm-10">
              <input type="text" name="pipeline_stages" value="{{ pipeline_stages }}" placeholder="{{ text_pipeline_stages }}" id="input-pipeline_stages" class="form-control" />
              {% if error_pipeline_stages %}
                <div class="invalid-feedback">{{ error_pipeline_stages }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-pipeline_stats">{{ text_pipeline_stats }}</label>
            <div class="col-sm-10">
              <input type="text" name="pipeline_stats" value="{{ pipeline_stats }}" placeholder="{{ text_pipeline_stats }}" id="input-pipeline_stats" class="form-control" />
              {% if error_pipeline_stats %}
                <div class="invalid-feedback">{{ error_pipeline_stats }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-recent_activities">{{ text_recent_activities }}</label>
            <div class="col-sm-10">
              <input type="text" name="recent_activities" value="{{ recent_activities }}" placeholder="{{ text_recent_activities }}" id="input-recent_activities" class="form-control" />
              {% if error_recent_activities %}
                <div class="invalid-feedback">{{ error_recent_activities }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_add_lead">{{ text_text_add_lead }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_add_lead" value="{{ text_add_lead }}" placeholder="{{ text_text_add_lead }}" id="input-text_add_lead" class="form-control" />
              {% if error_text_add_lead %}
                <div class="invalid-feedback">{{ error_text_add_lead }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_ajax_error">{{ text_text_ajax_error }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_ajax_error" value="{{ text_ajax_error }}" placeholder="{{ text_text_ajax_error }}" id="input-text_ajax_error" class="form-control" />
              {% if error_text_ajax_error %}
                <div class="invalid-feedback">{{ error_text_ajax_error }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_all_statuses">{{ text_text_all_statuses }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_all_statuses" value="{{ text_all_statuses }}" placeholder="{{ text_text_all_statuses }}" id="input-text_all_statuses" class="form-control" />
              {% if error_text_all_statuses %}
                <div class="invalid-feedback">{{ error_text_all_statuses }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_assigned_to">{{ text_text_assigned_to }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_assigned_to" value="{{ text_assigned_to }}" placeholder="{{ text_text_assigned_to }}" id="input-text_assigned_to" class="form-control" />
              {% if error_text_assigned_to %}
                <div class="invalid-feedback">{{ error_text_assigned_to }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_company">{{ text_text_company }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_company" value="{{ text_company }}" placeholder="{{ text_text_company }}" id="input-text_company" class="form-control" />
              {% if error_text_company %}
                <div class="invalid-feedback">{{ error_text_company }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_confirm_delete">{{ text_text_confirm_delete }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_confirm_delete" value="{{ text_confirm_delete }}" placeholder="{{ text_text_confirm_delete }}" id="input-text_confirm_delete" class="form-control" />
              {% if error_text_confirm_delete %}
                <div class="invalid-feedback">{{ error_text_confirm_delete }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_edit_lead">{{ text_text_edit_lead }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_edit_lead" value="{{ text_edit_lead }}" placeholder="{{ text_text_edit_lead }}" id="input-text_edit_lead" class="form-control" />
              {% if error_text_edit_lead %}
                <div class="invalid-feedback">{{ error_text_edit_lead }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_email">{{ text_text_email }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_email" value="{{ text_email }}" placeholder="{{ text_text_email }}" id="input-text_email" class="form-control" />
              {% if error_text_email %}
                <div class="invalid-feedback">{{ error_text_email }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_enter_lead_name">{{ text_text_enter_lead_name }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_enter_lead_name" value="{{ text_enter_lead_name }}" placeholder="{{ text_text_enter_lead_name }}" id="input-text_enter_lead_name" class="form-control" />
              {% if error_text_enter_lead_name %}
                <div class="invalid-feedback">{{ error_text_enter_lead_name }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_filter">{{ text_text_filter }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_filter" value="{{ text_filter }}" placeholder="{{ text_text_filter }}" id="input-text_filter" class="form-control" />
              {% if error_text_filter %}
                <div class="invalid-feedback">{{ error_text_filter }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_firstname">{{ text_text_firstname }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_firstname" value="{{ text_firstname }}" placeholder="{{ text_text_firstname }}" id="input-text_firstname" class="form-control" />
              {% if error_text_firstname %}
                <div class="invalid-feedback">{{ error_text_firstname }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_lastname">{{ text_text_lastname }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_lastname" value="{{ text_lastname }}" placeholder="{{ text_text_lastname }}" id="input-text_lastname" class="form-control" />
              {% if error_text_lastname %}
                <div class="invalid-feedback">{{ error_text_lastname }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_lead_list">{{ text_text_lead_list }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_lead_list" value="{{ text_lead_list }}" placeholder="{{ text_text_lead_list }}" id="input-text_lead_list" class="form-control" />
              {% if error_text_lead_list %}
                <div class="invalid-feedback">{{ error_text_lead_list }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_lead_name">{{ text_text_lead_name }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_lead_name" value="{{ text_lead_name }}" placeholder="{{ text_text_lead_name }}" id="input-text_lead_name" class="form-control" />
              {% if error_text_lead_name %}
                <div class="invalid-feedback">{{ error_text_lead_name }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_notes">{{ text_text_notes }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_notes" value="{{ text_notes }}" placeholder="{{ text_text_notes }}" id="input-text_notes" class="form-control" />
              {% if error_text_notes %}
                <div class="invalid-feedback">{{ error_text_notes }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_phone">{{ text_text_phone }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_phone" value="{{ text_phone }}" placeholder="{{ text_text_phone }}" id="input-text_phone" class="form-control" />
              {% if error_text_phone %}
                <div class="invalid-feedback">{{ error_text_phone }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_select_user">{{ text_text_select_user }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_select_user" value="{{ text_select_user }}" placeholder="{{ text_text_select_user }}" id="input-text_select_user" class="form-control" />
              {% if error_text_select_user %}
                <div class="invalid-feedback">{{ error_text_select_user }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_source">{{ text_text_source }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_source" value="{{ text_source }}" placeholder="{{ text_text_source }}" id="input-text_source" class="form-control" />
              {% if error_text_source %}
                <div class="invalid-feedback">{{ error_text_source }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_status">{{ text_text_status }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_status" value="{{ text_status }}" placeholder="{{ text_text_status }}" id="input-text_status" class="form-control" />
              {% if error_text_status %}
                <div class="invalid-feedback">{{ error_text_status }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_status_contacted">{{ text_text_status_contacted }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_status_contacted" value="{{ text_status_contacted }}" placeholder="{{ text_text_status_contacted }}" id="input-text_status_contacted" class="form-control" />
              {% if error_text_status_contacted %}
                <div class="invalid-feedback">{{ error_text_status_contacted }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_status_converted">{{ text_text_status_converted }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_status_converted" value="{{ text_status_converted }}" placeholder="{{ text_text_status_converted }}" id="input-text_status_converted" class="form-control" />
              {% if error_text_status_converted %}
                <div class="invalid-feedback">{{ error_text_status_converted }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_status_new">{{ text_text_status_new }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_status_new" value="{{ text_status_new }}" placeholder="{{ text_text_status_new }}" id="input-text_status_new" class="form-control" />
              {% if error_text_status_new %}
                <div class="invalid-feedback">{{ error_text_status_new }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_status_qualified">{{ text_text_status_qualified }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_status_qualified" value="{{ text_status_qualified }}" placeholder="{{ text_text_status_qualified }}" id="input-text_status_qualified" class="form-control" />
              {% if error_text_status_qualified %}
                <div class="invalid-feedback">{{ error_text_status_qualified }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_status_unqualified">{{ text_text_status_unqualified }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_status_unqualified" value="{{ text_status_unqualified }}" placeholder="{{ text_text_status_unqualified }}" id="input-text_status_unqualified" class="form-control" />
              {% if error_text_status_unqualified %}
                <div class="invalid-feedback">{{ error_text_status_unqualified }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-top_leads">{{ text_top_leads }}</label>
            <div class="col-sm-10">
              <input type="text" name="top_leads" value="{{ top_leads }}" placeholder="{{ text_top_leads }}" id="input-top_leads" class="form-control" />
              {% if error_top_leads %}
                <div class="invalid-feedback">{{ error_top_leads }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-upcoming_tasks">{{ text_upcoming_tasks }}</label>
            <div class="col-sm-10">
              <input type="text" name="upcoming_tasks" value="{{ upcoming_tasks }}" placeholder="{{ text_upcoming_tasks }}" id="input-upcoming_tasks" class="form-control" />
              {% if error_upcoming_tasks %}
                <div class="invalid-feedback">{{ error_upcoming_tasks }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="invalid-feedback">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-users">{{ text_users }}</label>
            <div class="col-sm-10">
              <input type="text" name="users" value="{{ users }}" placeholder="{{ text_users }}" id="input-users" class="form-control" />
              {% if error_users %}
                <div class="invalid-feedback">{{ error_users }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}