{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="submit" form="form-payment" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fa fa-save"></i></button>
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a></div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid"> {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    {% if success %}
    <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pencil"></i> {{ text_edit }}</h3>
      </div>
      <div class="panel-body">
        <form action="{{ action }}" method="post" enctype="multipart/form-data" id="form-payment" class="form-horizontal">
          <ul class="nav nav-tabs">
            <li class="active" id="li-tab-settings"><a href="#tab-settings" data-toggle="tab">{{ tab_settings }}</a></li>
            <li class="" id="li-tab-order-status"><a href="#tab-order-status" data-toggle="tab">{{ tab_order_status }}</a></li>
          </ul>
          <div class="tab-content">
            <div class="tab-pane active" id="tab-settings">
              <div class="form-group required">
                <label class="col-sm-2 control-label" for="input-cardconnect-merchant-id"><span data-toggle="tooltip" title="{{ help_merchant_id }}">{{ entry_merchant_id }}</span></label>
                <div class="col-sm-10">
                  <input type="text" name="payment_cardconnect_merchant_id" value="{{ payment_cardconnect_merchant_id }}" placeholder="{{ entry_merchant_id }}" id="input-cardconnect-merchant-id" class="form-control" />
                  {% if error_payment_cardconnect_merchant_id %}
                  <div class="text-danger">{{ error_payment_cardconnect_merchant_id }}</div>
                  {% endif %} </div>
              </div>
              <div class="form-group required">
                <label class="col-sm-2 control-label" for="input-cardconnect-api-username"><span data-toggle="tooltip" title="{{ help_api_username }}">{{ entry_api_username }}</span></label>
                <div class="col-sm-10">
                  <input type="text" name="cardconnect_api_username" value="{{ cardconnect_api_username }}" placeholder="{{ entry_api_username }}" id="input-cardconnect-api-username" class="form-control" />
                  {% if error_cardconnect_api_username %}
                  <div class="text-danger">{{ error_cardconnect_api_username }}</div>
                  {% endif %} </div>
              </div>
              <div class="form-group required">
                <label class="col-sm-2 control-label" for="input-cardconnect-api-password"><span data-toggle="tooltip" title="{{ help_api_password }}">{{ entry_api_password }}</span></label>
                <div class="col-sm-10">
                  <input type="text" name="cardconnect_api_password" value="{{ cardconnect_api_password }}" placeholder="{{ entry_api_password }}" id="input-cardconnect-api-password" class="form-control" />
                  {% if error_cardconnect_api_password %}
                  <div class="text-danger">{{ error_cardconnect_api_password }}</div>
                  {% endif %} </div>
              </div>
              <div class="form-group required">
                <label class="col-sm-2 control-label" for="input-cardconnect-token"><span data-toggle="tooltip" title="{{ help_token }}">{{ entry_token }}</span></label>
                <div class="col-sm-10">
                  <input type="text" name="cardconnect_token" value="{{ cardconnect_token }}" placeholder="{{ entry_token }}" id="input-cardconnect-token" class="form-control" />
                  {% if error_cardconnect_token %}
                  <div class="text-danger">{{ error_cardconnect_token }}</div>
                  {% endif %} </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-cardconnect-transaction"><span data-toggle="tooltip" title="{{ help_transaction }}">{{ entry_transaction }}</span></label>
                <div class="col-sm-10">
                  <select name="cardconnect_transaction" id="input-cardconnect-transaction" class="form-control">
                    
                    {% if cardconnect_transaction == 'payment' %}
                    
                    <option value="payment" selected="selected">{{ text_payment }}</option>
                    
                    {% else %}
                    
                    <option value="payment">{{ text_payment }}</option>
                    
                    {% endif %}
                    {% if cardconnect_transaction == 'authorize' %}
                    
                    <option value="authorize" selected="selected">{{ text_authorize }}</option>
                    
                    {% else %}
                    
                    <option value="authorize">{{ text_authorize }}</option>
                    
                    {% endif %}
                  
                  </select>
                </div>
              </div>
              <div class="form-group required">
                <label class="col-sm-2 control-label" for="input-cardconnect-site"><span data-toggle="tooltip" title="{{ help_site }}">{{ entry_site }}</span></label>
                <div class="col-sm-10">
                  <input type="text" name="cardconnect_site" value="{{ cardconnect_site }}" placeholder="{{ entry_site }}" id="input-cardconnect-site" class="form-control" />
                  {% if error_cardconnect_site %}
                  <div class="text-danger">{{ error_cardconnect_site }}</div>
                  {% endif %} </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-cardconnect-environment">{{ entry_environment }}</label>
                <div class="col-sm-10">
                  <select name="cardconnect_environment" id="input-cardconnect-environment" class="form-control">
                    
                    {% if cardconnect_environment == 'live' %}
                    
                    <option value="live" selected="selected">{{ text_live }}</option>
                    
                    {% else %}
                    
                    <option value="live">{{ text_live }}</option>
                    
                    {% endif %}
                    {% if cardconnect_environment == 'test' %}
                    
                    <option value="test" selected="selected">{{ text_test }}</option>
                    
                    {% else %}
                    
                    <option value="test">{{ text_test }}</option>
                    
                    {% endif %}
                  
                  </select>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-cardconnect-store-cards"><span data-toggle="tooltip" title="{{ help_store_cards }}">{{ entry_store_cards }}</span></label>
                <div class="col-sm-10">
                  <select name="cardconnect_store_cards" id="input-cardconnect-store-cards" class="form-control">
                    
                    {% if cardconnect_store_cards %}
                      
                    <option value="1" selected="selected">{{ text_enabled }}</option>
                    <option value="0">{{ text_disabled }}</option>
                    
                    {% else %}
                      
                    <option value="1">{{ text_enabled }}</option>
                    <option value="0" selected="selected">{{ text_disabled }}</option>
                    
                    {% endif %}
			      
                  </select>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-cardconnect-echeck"><span data-toggle="tooltip" title="{{ help_echeck }}">{{ entry_echeck }}</span></label>
                <div class="col-sm-10">
                  <select name="cardconnect_echeck" id="input-cardconnect-echeck" class="form-control">
                    
                    {% if cardconnect_echeck %}
                      
                    <option value="1" selected="selected">{{ text_enabled }}</option>
                    <option value="0">{{ text_disabled }}</option>
                    
                    {% else %}
                      
                    <option value="1">{{ text_enabled }}</option>
                    <option value="0" selected="selected">{{ text_disabled }}</option>
                    
                    {% endif %}
			      
                  </select>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-cardconnect-total"><span data-toggle="tooltip" title="{{ help_total }}">{{ entry_total }}</span></label>
                <div class="col-sm-10">
                  <input type="text" name="cardconnect_total" value="{{ cardconnect_total }}" placeholder="{{ entry_total }}" id="input-cardconnect-total" class="form-control" />
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-cardconnect-geo-zone">{{ entry_geo_zone }}</label>
                <div class="col-sm-10">
                  <select name="cardconnect_geo_zone" id="input-cardconnect-geo-zone" class="form-control">
                    <option value="0">{{ text_all_zones }}</option>
                    
				    {% for geo_zone in geo_zones %}
                    {% if geo_zone.geo_zone_id == cardconnect_geo_zone %}
				        
                    <option value="{{ geo_zone.geo_zone_id }}" selected="selected">{{ geo_zone.name }}</option>
                    
					  {% else %}
				        
                    <option value="{{ geo_zone.geo_zone_id }}">{{ geo_zone.name }}</option>
                    
    			      {% endif %}
				    {% endfor %}
				  
                  </select>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-cardconnect-status">{{ entry_status }}</label>
                <div class="col-sm-10">
                  <select name="cardconnect_status" id="input-cardconnect-status" class="form-control">
                    
                    {% if cardconnect_status %}
                      
                    <option value="1" selected="selected">{{ text_enabled }}</option>
                    <option value="0">{{ text_disabled }}</option>
                    
                    {% else %}
                      
                    <option value="1">{{ text_enabled }}</option>
                    <option value="0" selected="selected">{{ text_disabled }}</option>
                    
                    {% endif %}
			      
                  </select>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-cardconnect-logging"><span data-toggle="tooltip" title="{{ help_logging }}">{{ entry_logging }}</span></label>
                <div class="col-sm-10">
                  <select name="cardconnect_logging" id="input-cardconnect-logging" class="form-control">
                    
                    {% if cardconnect_logging %}
                      
                    <option value="1" selected="selected">{{ text_enabled }}</option>
                    <option value="0">{{ text_disabled }}</option>
                    
                    {% else %}
                      
                    <option value="1">{{ text_enabled }}</option>
                    <option value="0" selected="selected">{{ text_disabled }}</option>
                    
                    {% endif %}
			      
                  </select>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-cardconnect-sort-order">{{ entry_sort_order }}</label>
                <div class="col-sm-10">
                  <input type="text" name="cardconnect_sort_order" value="{{ cardconnect_sort_order }}" placeholder="{{ entry_sort_order }}" id="input-cardconnect-sort-order" class="form-control" />
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-cardconnect-cron-url"><span data-toggle="tooltip" title="{{ help_cron_url }}">{{ entry_cron_url }}</span></label>
                <div class="col-sm-10">
                  <input type="text" name="cardconnect_cron_url" value="{{ cardconnect_cron_url }}" readonly placeholder="{{ entry_cron_url }}" id="input-cardconnect-cron-url" class="form-control" />
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-cardconnect-cron-time"><span data-toggle="tooltip" title="{{ help_cron_time }}">{{ entry_cron_time }}</span></label>
                <div class="col-sm-10">
                  <input type="text" name="cardconnect_cron_time" value="{{ cardconnect_cron_time }}" readonly disabled placeholder="{{ entry_cron_time }}" id="input-cardconnect-cron-time" class="form-control" />
                </div>
              </div>
            </div>
            <div class="tab-pane" id="tab-order-status">
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-cardconnect-order-status-pending"><span data-toggle="tooltip" title="{{ help_order_status_pending }}">{{ entry_order_status_pending }}</span></label>
                <div class="col-sm-10">
                  <select name="cardconnect_order_status_id_pending" id="input-cardconnect-order-status-pending" class="form-control">
                    
                    {% for order_status in order_statuses %}
                      {% if order_status.order_status_id == cardconnect_order_status_id_pending %}
                        
                    <option value="{{ order_status.order_status_id }}" selected="selected">{{ order_status.name }}</option>
                    
                      {% else %}
                        
                    <option value="{{ order_status.order_status_id }}">{{ order_status.name }}</option>
                    
                      {% endif %}
                    {% endfor %}
                  
                  </select>
                </div>
              </div>
              <div class="form-group">
                <label class="col-sm-2 control-label" for="input-cardconnect-order-status-processing"><span data-toggle="tooltip" title="{{ help_order_status_processing }}">{{ entry_order_status_processing }}</span></label>
                <div class="col-sm-10">
                  <select name="cardconnect_order_status_id_processing" id="input-cardconnect-order-status-processing" class="form-control">
                    
                    {% for order_status in order_statuses %}
                      {% if order_status.order_status_id == cardconnect_order_status_id_processing %}
                        
                    <option value="{{ order_status.order_status_id }}" selected="selected">{{ order_status.name }}</option>
                    
                      {% else %}
                        
                    <option value="{{ order_status.order_status_id }}">{{ order_status.name }}</option>
                    
                      {% endif %}
                    {% endfor %}
                  
                  </select>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
{{ footer }}