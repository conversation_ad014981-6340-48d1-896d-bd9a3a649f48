<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/expr/v1beta1/expr.proto

namespace Google\Api\Expr\V1beta1;

if (false) {
    /**
     * This class is deprecated. Use Google\Api\Expr\V1beta1\Expr\Comprehension instead.
     * @deprecated
     */
    class Expr_Comprehension {}
}
class_exists(Expr\Comprehension::class);
@trigger_error('Google\Api\Expr\V1beta1\Expr_Comprehension is deprecated and will be removed in the next major release. Use Google\Api\Expr\V1beta1\Expr\Comprehension instead', E_USER_DEPRECATED);

