<?php
/**
 * سكريبت تصحيح DB_PREFIX في dashboard.php
 * يستبدل جميع استخدامات DB_PREFIX بـ cod_ مباشرة
 */

$file_path = 'dashboard/model/common/dashboard.php';

if (!file_exists($file_path)) {
    die("الملف غير موجود: $file_path\n");
}

// قراءة محتوى الملف
$content = file_get_contents($file_path);

if ($content === false) {
    die("فشل في قراءة الملف: $file_path\n");
}

// قائمة الجداول الموجودة فعلاً في db.txt (بناءً على الحصر الشامل)
$existing_tables = [
    // الجداول الأساسية
    'order', 'order_product', 'order_total', 'order_history', 'order_option', 'order_shipment',
    'customer', 'customer_group', 'customer_activity', 'customer_feedback', 'customer_online',
    'product', 'product_description', 'product_inventory', 'product_movement', 'product_batch',
    'category', 'category_description', 'category_path',
    'supplier', 'supplier_address', 'supplier_evaluation', 'supplier_invoice',
    'purchase_order', 'purchase_order_item', 'purchase_order_history',
    'branch', 'branch_address', 'branch_inventory_snapshot',
    'user', 'user_group', 'user_session', 'user_activity_log',
    'cart', 'cart_product', 'abandoned_cart',
    'return', 'review',
    
    // الجداول المحاسبية
    'accounts', 'journal_entries', 'journals', 'general_ledger_analysis',
    'cash', 'cash_flow_analysis', 'bank_account', 'budget',
    
    // جداول CRM
    'crm_campaign', 'crm_lead', 'crm_deal', 'crm_contact',
    
    // جداول الشحن
    'shipping_order', 'shipping_company', 'shipping_rate',
    
    // جداول المخزون
    'inventory_alert', 'inventory_transfer', 'inventory_count',
    'stock_adjustment', 'stock_transfer', 'stock_count',
    
    // جداول أخرى موجودة
    'currency', 'language', 'setting', 'statistics',
    'notification_user', 'unified_notification',
    'task', 'project', 'meeting',
    'eta_documents', 'eta_submissions'
];

// الجداول غير الموجودة (يجب استبدالها بجداول موجودة أو حذفها)
$non_existing_tables = [
    'employee' => 'user', // استبدال employee بـ user
    'hr_attendance' => 'attendance',
    'hr_leave_request' => 'leave_request', 
    'hr_evaluation' => 'performance_review',
    'hr_job_position' => 'task', // مؤقت
    'invoice' => 'supplier_invoice',
    'shipment' => 'shipping_order',
    'project_task' => 'task',
    'calendar_event' => 'meeting',
    'marketing_campaign' => 'crm_campaign',
    'customer_complaint' => 'customer_feedback',
    'system_event' => 'activity_log',
    'user_activity' => 'user_activity_log'
];

// تصحيح DB_PREFIX للجداول الموجودة
foreach ($existing_tables as $table) {
    $old_pattern = 'DB_PREFIX . "' . $table . '"';
    $new_replacement = 'cod_' . $table;
    $content = str_replace($old_pattern, $new_replacement, $content);
    
    // تصحيح النمط الآخر
    $old_pattern2 = '" . DB_PREFIX . "' . $table;
    $new_replacement2 = 'cod_' . $table;
    $content = str_replace($old_pattern2, $new_replacement2, $content);
}

// استبدال الجداول غير الموجودة بالبدائل
foreach ($non_existing_tables as $old_table => $new_table) {
    $old_pattern = 'DB_PREFIX . "' . $old_table . '"';
    $new_replacement = 'cod_' . $new_table;
    $content = str_replace($old_pattern, $new_replacement, $content);
    
    $old_pattern2 = '" . DB_PREFIX . "' . $old_table;
    $new_replacement2 = 'cod_' . $new_table;
    $content = str_replace($old_pattern2, $new_replacement2, $content);
}

// تصحيحات خاصة للجداول المعقدة
$special_replacements = [
    // تصحيح الجداول المركبة
    'cod_order_item' => 'cod_order_product',
    'cod_branch_inventory_snapshot' => 'cod_product_inventory', // استخدام جدول المخزون الأساسي
    'cod_product_to_category' => 'cod_product_to_category', // موجود
    'cod_customer_complaint' => 'cod_customer_feedback',
    
    // تصحيح الجداول المفقودة تماماً
    'cod_innovation_idea' => 'cod_task', // مؤقت
    'cod_innovation_project_member' => 'cod_task', // مؤقت
    'cod_internal_communication' => 'cod_message', // استخدام نظام الرسائل
    'cod_communication_recipient' => 'cod_message_recipient',
    'cod_flexibility_assessment' => 'cod_performance_review',
    'cod_work_arrangement_request' => 'cod_leave_request',
    'cod_training_completion' => 'cod_task', // مؤقت
    'cod_training' => 'cod_task', // مؤقت
    'cod_job_requisition' => 'cod_task', // مؤقت
    'cod_salary' => 'cod_user', // استخدام جدول المستخدمين
    'cod_benefits' => 'cod_user', // استخدام جدول المستخدمين
    'cod_wellbeing_assessment' => 'cod_performance_review',
    'cod_wellness_program_participation' => 'cod_task',
    'cod_mental_health_session' => 'cod_meeting',
    'cod_support_request' => 'cod_task',
    'cod_career_profile' => 'cod_employee_profile',
    'cod_promotion' => 'cod_task', // مؤقت
    'cod_mentorship' => 'cod_task', // مؤقت
];

foreach ($special_replacements as $old => $new) {
    $content = str_replace($old, $new, $content);
}

// حفظ الملف المُصحح
$backup_file = $file_path . '.backup.' . date('Y-m-d-H-i-s');
if (!copy($file_path, $backup_file)) {
    die("فشل في إنشاء نسخة احتياطية\n");
}

if (file_put_contents($file_path, $content) === false) {
    die("فشل في حفظ الملف المُصحح\n");
}

echo "✅ تم تصحيح الملف بنجاح!\n";
echo "📁 النسخة الاحتياطية: $backup_file\n";
echo "🔧 تم استبدال جميع استخدامات DB_PREFIX بـ cod_\n";
echo "📊 تم تصحيح أسماء الجداول غير الموجودة\n";

// إحصائيات التصحيح
$remaining_db_prefix = substr_count($content, 'DB_PREFIX');
echo "📈 استخدامات DB_PREFIX المتبقية: $remaining_db_prefix\n";

if ($remaining_db_prefix > 0) {
    echo "⚠️  تحذير: لا تزال هناك استخدامات لـ DB_PREFIX تحتاج مراجعة يدوية\n";
}

echo "\n🎉 انتهى التصحيح!\n";
?>
