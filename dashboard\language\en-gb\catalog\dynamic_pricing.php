<?php

// Tab
$_['tab_dynamic_pricing']         = 'Dynamic Pricing';

// Heading
$_['heading_title']               = 'Dynamic Pricing Rule';

// Text
$_['text_success']                = 'Success: You have modified dynamic pricing rules!';
$_['text_list']                   = 'Dynamic Pricing Rule List';
$_['text_add']                    = 'Add Dynamic Pricing Rule';
$_['text_edit']                   = 'Edit Dynamic Pricing Rule';
$_['text_percent']                = 'Percentage';
$_['text_amount']                 = 'Fixed Amount';
$_['text_formula']                = 'Formula';

// Column
$_['column_name']                 = 'Rule Name';
$_['column_type']                 = 'Type';
$_['column_value']                = 'Value';
$_['column_priority']             = 'Priority';
$_['column_date_start']           = 'Date Start';
$_['column_date_end']             = 'Date End';
$_['column_status']               = 'Status';

// Entry
$_['entry_name']                  = 'Rule Name';
$_['entry_type']                  = 'Type';
$_['entry_value']                 = 'Value';
$_['entry_formula']               = 'Formula';
$_['entry_condition_type']        = 'Condition Type';
$_['entry_condition_value']       = 'Condition Value';
$_['entry_priority']              = 'Priority';
$_['entry_date_start']            = 'Date Start';
$_['entry_date_end']              = 'Date End';
$_['entry_status']                = 'Status';

// Help
$_['help_formula']                = 'You can use {price} as a placeholder for the original price. Example: {price} * 0.9';
$_['help_condition_value']        = 'For customer group, enter the customer group ID. For total spent, enter the amount. For purchase history, enter the number of orders. For time period, enter start time and end time separated by a comma (e.g., 09:00,17:00). For stock level, enter the stock quantity. For competitor price, enter the competitor\'s price.';

// Error
$_['error_permission']            = 'Warning: You do not have permission to modify dynamic pricing rules!';
$_['error_name']                  = 'Rule Name must be between 3 and 255 characters!';
$_['error_value']                 = 'Value required!';