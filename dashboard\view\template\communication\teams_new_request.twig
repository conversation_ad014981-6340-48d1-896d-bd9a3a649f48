{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="submit" form="communication\teams-form" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fas fa-save"></i></button>
        <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-light"><i class="fas fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    
    <div class="card">
      <div class="card-header"><i class="fas fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}chart-bar{% elif screen_type == 'settings' %}cog{% else %}pencil-alt{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="card-body">
        <form id="communication\teams-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-active_teams">{{ text_active_teams }}</label>
            <div class="col-sm-10">
              <input type="text" name="active_teams" value="{{ active_teams }}" placeholder="{{ text_active_teams }}" id="input-active_teams" class="form-control" />
              {% if error_active_teams %}
                <div class="invalid-feedback">{{ error_active_teams }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-approve_all">{{ text_approve_all }}</label>
            <div class="col-sm-10">
              <input type="text" name="approve_all" value="{{ approve_all }}" placeholder="{{ text_approve_all }}" id="input-approve_all" class="form-control" />
              {% if error_approve_all %}
                <div class="invalid-feedback">{{ error_approve_all }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-completed_approvals">{{ text_completed_approvals }}</label>
            <div class="col-sm-10">
              <input type="text" name="completed_approvals" value="{{ completed_approvals }}" placeholder="{{ text_completed_approvals }}" id="input-completed_approvals" class="form-control" />
              {% if error_completed_approvals %}
                <div class="invalid-feedback">{{ error_completed_approvals }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-current_user_id">{{ text_current_user_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="current_user_id" value="{{ current_user_id }}" placeholder="{{ text_current_user_id }}" id="input-current_user_id" class="form-control" />
              {% if error_current_user_id %}
                <div class="invalid-feedback">{{ error_current_user_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-current_user_name">{{ text_current_user_name }}</label>
            <div class="col-sm-10">
              <input type="text" name="current_user_name" value="{{ current_user_name }}" placeholder="{{ text_current_user_name }}" id="input-current_user_name" class="form-control" />
              {% if error_current_user_name %}
                <div class="invalid-feedback">{{ error_current_user_name }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-delegate">{{ text_delegate }}</label>
            <div class="col-sm-10">
              <input type="text" name="delegate" value="{{ delegate }}" placeholder="{{ text_delegate }}" id="input-delegate" class="form-control" />
              {% if error_delegate %}
                <div class="invalid-feedback">{{ error_delegate }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-departments">{{ text_departments }}</label>
            <div class="col-sm-10">
              <input type="text" name="departments" value="{{ departments }}" placeholder="{{ text_departments }}" id="input-departments" class="form-control" />
              {% if error_departments %}
                <div class="invalid-feedback">{{ error_departments }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-digital_processes">{{ text_digital_processes }}</label>
            <div class="col-sm-10">
              <input type="text" name="digital_processes" value="{{ digital_processes }}" placeholder="{{ text_digital_processes }}" id="input-digital_processes" class="form-control" />
              {% if error_digital_processes %}
                <div class="invalid-feedback">{{ error_digital_processes }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="invalid-feedback">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-new_team">{{ text_new_team }}</label>
            <div class="col-sm-10">
              <input type="text" name="new_team" value="{{ new_team }}" placeholder="{{ text_new_team }}" id="input-new_team" class="form-control" />
              {% if error_new_team %}
                <div class="invalid-feedback">{{ error_new_team }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-pending_approvals">{{ text_pending_approvals }}</label>
            <div class="col-sm-10">
              <input type="text" name="pending_approvals" value="{{ pending_approvals }}" placeholder="{{ text_pending_approvals }}" id="input-pending_approvals" class="form-control" />
              {% if error_pending_approvals %}
                <div class="invalid-feedback">{{ error_pending_approvals }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-pending_documents">{{ text_pending_documents }}</label>
            <div class="col-sm-10">
              <input type="text" name="pending_documents" value="{{ pending_documents }}" placeholder="{{ text_pending_documents }}" id="input-pending_documents" class="form-control" />
              {% if error_pending_documents %}
                <div class="invalid-feedback">{{ error_pending_documents }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-pending_tasks">{{ text_pending_tasks }}</label>
            <div class="col-sm-10">
              <input type="text" name="pending_tasks" value="{{ pending_tasks }}" placeholder="{{ text_pending_tasks }}" id="input-pending_tasks" class="form-control" />
              {% if error_pending_tasks %}
                <div class="invalid-feedback">{{ error_pending_tasks }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-products">{{ text_products }}</label>
            <div class="col-sm-10">
              <input type="text" name="products" value="{{ products }}" placeholder="{{ text_products }}" id="input-products" class="form-control" />
              {% if error_products %}
                <div class="invalid-feedback">{{ error_products }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-quick_actions">{{ text_quick_actions }}</label>
            <div class="col-sm-10">
              <input type="text" name="quick_actions" value="{{ quick_actions }}" placeholder="{{ text_quick_actions }}" id="input-quick_actions" class="form-control" />
              {% if error_quick_actions %}
                <div class="invalid-feedback">{{ error_quick_actions }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-reports">{{ text_reports }}</label>
            <div class="col-sm-10">
              <input type="text" name="reports" value="{{ reports }}" placeholder="{{ text_reports }}" id="input-reports" class="form-control" />
              {% if error_reports %}
                <div class="invalid-feedback">{{ error_reports }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-request_types">{{ text_request_types }}</label>
            <div class="col-sm-10">
              <input type="text" name="request_types" value="{{ request_types }}" placeholder="{{ text_request_types }}" id="input-request_types" class="form-control" />
              {% if error_request_types %}
                <div class="invalid-feedback">{{ error_request_types }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-specialized_teams">{{ text_specialized_teams }}</label>
            <div class="col-sm-10">
              <input type="text" name="specialized_teams" value="{{ specialized_teams }}" placeholder="{{ text_specialized_teams }}" id="input-specialized_teams" class="form-control" />
              {% if error_specialized_teams %}
                <div class="invalid-feedback">{{ error_specialized_teams }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-suppliers">{{ text_suppliers }}</label>
            <div class="col-sm-10">
              <input type="text" name="suppliers" value="{{ suppliers }}" placeholder="{{ text_suppliers }}" id="input-suppliers" class="form-control" />
              {% if error_suppliers %}
                <div class="invalid-feedback">{{ error_suppliers }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_groups">{{ text_user_groups }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_groups" value="{{ user_groups }}" placeholder="{{ text_user_groups }}" id="input-user_groups" class="form-control" />
              {% if error_user_groups %}
                <div class="invalid-feedback">{{ error_user_groups }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_stats">{{ text_user_stats }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_stats" value="{{ user_stats }}" placeholder="{{ text_user_stats }}" id="input-user_stats" class="form-control" />
              {% if error_user_stats %}
                <div class="invalid-feedback">{{ error_user_stats }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="invalid-feedback">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-workflow_management">{{ text_workflow_management }}</label>
            <div class="col-sm-10">
              <input type="text" name="workflow_management" value="{{ workflow_management }}" placeholder="{{ text_workflow_management }}" id="input-workflow_management" class="form-control" />
              {% if error_workflow_management %}
                <div class="invalid-feedback">{{ error_workflow_management }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-workflow_stats">{{ text_workflow_stats }}</label>
            <div class="col-sm-10">
              <input type="text" name="workflow_stats" value="{{ workflow_stats }}" placeholder="{{ text_workflow_stats }}" id="input-workflow_stats" class="form-control" />
              {% if error_workflow_stats %}
                <div class="invalid-feedback">{{ error_workflow_stats }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}