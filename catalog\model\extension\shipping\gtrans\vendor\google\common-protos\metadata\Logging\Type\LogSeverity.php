<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/logging/type/log_severity.proto

namespace GPBMetadata\Google\Logging\Type;

class LogSeverity
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        \GPBMetadata\Google\Api\Annotations::initOnce();
        $pool->internalAddGeneratedFile(hex2bin(
            "0aec020a26676f6f676c652f6c6f6767696e672f747970652f6c6f675f73" .
            "657665726974792e70726f746f1213676f6f676c652e6c6f6767696e672e" .
            "747970652a82010a0b4c6f675365766572697479120b0a0744454641554c" .
            "54100012090a054445425547106412090a04494e464f10c801120b0a064e" .
            "4f5449434510ac02120c0a075741524e494e47109003120a0a054552524f" .
            "5210f403120d0a08435249544943414c10d804120a0a05414c45525410bc" .
            "05120e0a09454d455247454e435910a006429f010a17636f6d2e676f6f67" .
            "6c652e6c6f6767696e672e7479706542104c6f6753657665726974795072" .
            "6f746f50015a38676f6f676c652e676f6c616e672e6f72672f67656e7072" .
            "6f746f2f676f6f676c65617069732f6c6f6767696e672f747970653b6c74" .
            "797065aa0219476f6f676c652e436c6f75642e4c6f6767696e672e547970" .
            "65ca0219476f6f676c655c436c6f75645c4c6f6767696e675c5479706562" .
            "0670726f746f33"
        ), true);

        static::$is_initialized = true;
    }
}

