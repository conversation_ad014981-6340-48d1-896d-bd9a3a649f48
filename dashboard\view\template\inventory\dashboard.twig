{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="button" id="refresh-dashboard" class="btn btn-primary" data-toggle="tooltip" title="{{ text_refresh }}">
          <i class="fa fa-refresh"></i> {{ text_refresh }}
        </button>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  
  <div class="container-fluid">
    <!-- الإحصائيات الأساسية -->
    <div class="row">
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-primary">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-cubes fa-5x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge" id="total-products">{{ stats.total_products }}</div>
                <div>{{ text_total_products }}</div>
              </div>
            </div>
          </div>
          <a href="{{ quick_links[0].href }}">
            <div class="panel-footer">
              <span class="pull-left">{{ text_view_details }}</span>
              <span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
              <div class="clearfix"></div>
            </div>
          </a>
        </div>
      </div>
      
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-green">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-money fa-5x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge" id="inventory-value">{{ stats.total_inventory_value|number_format(2) }}</div>
                <div>{{ text_inventory_value }}</div>
              </div>
            </div>
          </div>
          <a href="{{ quick_links[5].href }}">
            <div class="panel-footer">
              <span class="pull-left">{{ text_view_reports }}</span>
              <span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
              <div class="clearfix"></div>
            </div>
          </a>
        </div>
      </div>
      
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-yellow">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-exclamation-triangle fa-5x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge" id="low-stock-count">{{ stats.low_stock_products }}</div>
                <div>{{ text_low_stock_products }}</div>
              </div>
            </div>
          </div>
          <a href="{{ quick_links[1].href }}?filter_low_stock=1">
            <div class="panel-footer">
              <span class="pull-left">{{ text_view_details }}</span>
              <span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
              <div class="clearfix"></div>
            </div>
          </a>
        </div>
      </div>
      
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-red">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-clock-o fa-5x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge" id="expiring-count">{{ stats.expiring_products }}</div>
                <div>{{ text_expiring_products }}</div>
              </div>
            </div>
          </div>
          <a href="#">
            <div class="panel-footer">
              <span class="pull-left">{{ text_view_details }}</span>
              <span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
              <div class="clearfix"></div>
            </div>
          </a>
        </div>
      </div>
    </div>
    
    <!-- التنبيهات الذكية -->
    {% if alerts %}
    <div class="row">
      <div class="col-md-12">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-bell"></i> {{ text_smart_alerts }}</h3>
          </div>
          <div class="panel-body">
            {% for alert in alerts %}
            <div class="alert alert-{{ alert.type }} alert-dismissible" role="alert">
              <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
              </button>
              <i class="fa {{ alert.icon }}"></i>
              <strong>{{ alert.title }}</strong> {{ alert.message }}
              <a href="{{ alert.action_link }}" class="btn btn-sm btn-{{ alert.type }}" style="margin-left: 10px;">
                {{ alert.action_text }}
              </a>
            </div>
            {% endfor %}
          </div>
        </div>
      </div>
    </div>
    {% endif %}
    
    <!-- الرسوم البيانية -->
    <div class="row">
      <div class="col-md-8">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-line-chart"></i> {{ text_inventory_movement_chart }}</h3>
          </div>
          <div class="panel-body">
            <canvas id="movement-chart" height="100"></canvas>
          </div>
        </div>
      </div>
      
      <div class="col-md-4">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-pie-chart"></i> {{ text_abc_analysis_chart }}</h3>
          </div>
          <div class="panel-body">
            <canvas id="abc-chart" height="200"></canvas>
          </div>
        </div>
      </div>
    </div>
    
    <!-- المنتجات الأكثر حركة وأحدث الحركات -->
    <div class="row">
      <div class="col-md-6">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-fire"></i> {{ text_top_moving_products }}</h3>
          </div>
          <div class="panel-body">
            <div class="table-responsive">
              <table class="table table-striped table-hover">
                <thead>
                  <tr>
                    <th>{{ text_product_name }}</th>
                    <th class="text-right">{{ text_movement_quantity }}</th>
                    <th class="text-right">{{ text_movement_value }}</th>
                  </tr>
                </thead>
                <tbody>
                  {% for product in top_products %}
                  <tr>
                    <td>{{ product.name }}</td>
                    <td class="text-right">{{ product.total_movement }}</td>
                    <td class="text-right">{{ product.total_value|number_format(2) }}</td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-md-6">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-history"></i> {{ text_recent_movements }}</h3>
          </div>
          <div class="panel-body">
            <div class="table-responsive">
              <table class="table table-striped table-hover">
                <thead>
                  <tr>
                    <th>{{ text_product_name }}</th>
                    <th>{{ text_movement_type }}</th>
                    <th class="text-right">{{ text_quantity }}</th>
                    <th>{{ text_date }}</th>
                  </tr>
                </thead>
                <tbody>
                  {% for movement in recent_movements %}
                  <tr>
                    <td>{{ movement.product_name }}</td>
                    <td>
                      <span class="label label-{{ movement.type_class }}">
                        {{ movement.movement_type_text }}
                      </span>
                    </td>
                    <td class="text-right">{{ movement.quantity }}</td>
                    <td>{{ movement.date_added }}</td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- الروابط السريعة -->
    <div class="row">
      <div class="col-md-12">
        <div class="panel panel-default">
          <div class="panel-heading">
            <h3 class="panel-title"><i class="fa fa-link"></i> {{ text_quick_links }}</h3>
          </div>
          <div class="panel-body">
            <div class="row">
              {% for link in quick_links %}
              <div class="col-md-2 col-sm-4 col-xs-6">
                <a href="{{ link.href }}" class="btn btn-default btn-block" style="height: 80px; display: flex; flex-direction: column; justify-content: center; align-items: center;">
                  <i class="fa {{ link.icon }} fa-2x"></i>
                  <br>
                  <small>{{ link.name }}</small>
                </a>
              </div>
              {% endfor %}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- تضمين مكتبات الرسوم البيانية -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script type="text/javascript">
$(document).ready(function() {
    // تهيئة الرسوم البيانية
    initializeCharts();
    
    // تحديث لوحة المعلومات
    $('#refresh-dashboard').on('click', function() {
        refreshDashboard();
    });
    
    // تحديث تلقائي كل 5 دقائق
    setInterval(function() {
        refreshStats();
    }, 300000);
});

function initializeCharts() {
    // رسم بياني لحركة المخزون
    var movementCtx = document.getElementById('movement-chart').getContext('2d');
    var movementChart = new Chart(movementCtx, {
        type: 'line',
        data: {
            labels: {{ charts.inventory_movement|map(m => m.movement_date)|json_encode|raw }},
            datasets: [{
                label: '{{ text_inbound }}',
                data: {{ charts.inventory_movement|map(m => m.inbound)|json_encode|raw }},
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1
            }, {
                label: '{{ text_outbound }}',
                data: {{ charts.inventory_movement|map(m => m.outbound)|json_encode|raw }},
                borderColor: 'rgb(255, 99, 132)',
                backgroundColor: 'rgba(255, 99, 132, 0.2)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
    
    // رسم بياني لتحليل ABC
    var abcCtx = document.getElementById('abc-chart').getContext('2d');
    var abcChart = new Chart(abcCtx, {
        type: 'doughnut',
        data: {
            labels: {{ charts.abc_analysis|map(a => a.label)|json_encode|raw }},
            datasets: [{
                data: {{ charts.abc_analysis|map(a => a.value)|json_encode|raw }},
                backgroundColor: [
                    'rgba(255, 99, 132, 0.8)',
                    'rgba(54, 162, 235, 0.8)',
                    'rgba(255, 205, 86, 0.8)'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
}

function refreshDashboard() {
    $('#refresh-dashboard').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> {{ text_refreshing }}');
    
    // تحديث الإحصائيات
    refreshStats();
    
    // تحديث التنبيهات
    refreshAlerts();
    
    setTimeout(function() {
        $('#refresh-dashboard').prop('disabled', false).html('<i class="fa fa-refresh"></i> {{ text_refresh }}');
    }, 2000);
}

function refreshStats() {
    $.ajax({
        url: 'index.php?route=inventory/dashboard/refresh_stats&user_token={{ user_token }}',
        type: 'GET',
        dataType: 'json',
        success: function(data) {
            $('#total-products').text(data.total_products);
            $('#inventory-value').text(parseFloat(data.total_inventory_value).toLocaleString('en-US', {minimumFractionDigits: 2}));
            $('#low-stock-count').text(data.low_stock_products);
            $('#expiring-count').text(data.expiring_products);
        }
    });
}

function refreshAlerts() {
    $.ajax({
        url: 'index.php?route=inventory/dashboard/refresh_alerts&user_token={{ user_token }}',
        type: 'GET',
        dataType: 'json',
        success: function(data) {
            // تحديث التنبيهات
            // يمكن إضافة منطق تحديث التنبيهات هنا
        }
    });
}
</script>

<style>
.panel-green {
    border-color: #5cb85c;
}
.panel-green > .panel-heading {
    border-color: #5cb85c;
    color: white;
    background-color: #5cb85c;
}
.panel-green > a {
    color: #5cb85c;
}
.panel-green > a:hover {
    color: #3d8b3d;
}

.panel-yellow {
    border-color: #f0ad4e;
}
.panel-yellow > .panel-heading {
    border-color: #f0ad4e;
    color: white;
    background-color: #f0ad4e;
}
.panel-yellow > a {
    color: #f0ad4e;
}
.panel-yellow > a:hover {
    color: #df8a13;
}

.panel-red {
    border-color: #d9534f;
}
.panel-red > .panel-heading {
    border-color: #d9534f;
    color: white;
    background-color: #d9534f;
}
.panel-red > a {
    color: #d9534f;
}
.panel-red > a:hover {
    color: #c12e2a;
}

.huge {
    font-size: 40px;
}
</style>

{{ footer }}
