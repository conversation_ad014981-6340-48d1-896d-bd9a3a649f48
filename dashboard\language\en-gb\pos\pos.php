<?php
// Main headings and titles
$_['heading_title'] = 'Point Of Sale';

$_['text_cart_empty'] = 'Cart is empty';
$_['text_no_results'] = 'No results found';
$_['text_product_search_error'] = 'An error occurred while searching for products. Please try again.';
$_['text_customer_search_error'] = 'An error occurred while searching for customers. Please try again.';
$_['text_select_customer_group'] = 'Please select a customer group!';
$_['text_invalid_quantity'] = 'Invalid quantity. Please enter a positive number.';
$_['text_customer_required'] = 'Please select or add a customer before completing the sale.';
$_['text_generic_error'] = 'An error occurred. Please try again.';
$_['text_error'] = 'Error';
$_['text_clean_cart_success'] = 'تم تنظيف السلة المعلقة بنجاح.';
$_['text_clean_all_carts_success'] = 'تم تنظيف جميع السلات المعلقة بنجاح.';
$_['error_cart_not_found'] = 'لم يتم العثور على السلة المعلقة.';
$_['error_invalid_request'] = 'طلب غير صالح.';
$_['error_no_suspended_carts'] = 'لا توجد سلات معلقة للتنظيف.';
// Additional texts from your original code
$_['text_cart'] = 'Cart';
$_['text_select_category'] = 'Select Category';
$_['text_search_products'] = 'Search for Products';
$_['text_search_customers'] = 'Search for Customers';
$_['text_shipping_methods'] = 'Shipping Methods';
$_['text_payment_methods'] = 'Payment Methods';
$_['text_add_new_customer'] = 'Add New Customer';
$_['text_add_new_customer_tooltip'] = 'Click to add a new customer';
$_['text_apply_coupon_tooltip'] = 'Apply Coupon';
$_['text_suspend_sale_tooltip'] = 'Suspend Sale';
$_['text_resume_sale_tooltip'] = 'Resume Sale';
$_['text_void_sale_tooltip'] = 'Void Sale';
$_['text_complete_sale_tooltip'] = 'Complete Sale';
$_['text_enter_coupon'] = 'Enter Coupon';
$_['text_confirm_void'] = 'Are you sure you want to void the sale?';
$_['text_no_customers_found'] = 'No customers found';
$_['text_selected_customer'] = 'Selected Customer';
$_['text_added_customer'] = 'Customer Added';
$_['text_national_id'] = 'National ID';
$_['text_enter_national_id'] = 'Enter National ID';
$_['text_tax_id'] = 'Tax ID';
$_['text_enter_tax_id'] = 'Enter Tax ID';
$_['text_change_quantity'] = 'Change Quantity';
$_['text_remove_item'] = 'Remove Item';
$_['text_select_options'] = 'Select Options';
$_['text_quantity'] = 'Quantity';
$_['text_name'] = 'Name';
$_['text_phone'] = 'Phone';
$_['text_email'] = 'Email';
$_['text_address_1'] = 'Street Name';
$_['text_address_2'] = 'Building Number';
$_['text_city'] = 'City';
$_['text_zone'] = 'Zone/State';
$_['text_customer_group'] = 'Customer Group';
$_['text_rin_customer'] = 'RIN Customer';
$_['text_product_list'] = 'Product List';
$_['text_add_to_cart'] = 'Add to Cart';
$_['text_total'] = 'Total';
$_['text_sub_total'] = 'Sub Total';
$_['text_coupon'] = 'Discount Coupon';
$_['text_tax'] = 'VAT';
$_['text_shipping'] = 'Shipping Cost';
$_['text_enter_suspend_id'] = 'Enter Suspend ID';
$_['text_success'] = 'Product %s successfully added to cart!';
$_['error_exists']          = 'Email or Phone Exists';

$_['text_success_update'] = 'Cart updated successfully!';
$_['text_success_remove'] = 'Item removed successfully!';
$_['text_success_coupon'] = 'Coupon applied successfully!';
$_['text_suspend_success'] = 'Sale suspended successfully!';
$_['text_resume_success'] = 'Sale resumed successfully!';
$_['text_void_success'] = 'Sale voided successfully!';
$_['text_order_complete'] = 'Order #%s completed successfully!';
$_['text_print_success'] = 'Receipt printed successfully!';
$_['text_success_add_customer'] = 'Customer added successfully!';
$_['text_customer_selected'] = 'Customer selected successfully!';

// Buttons
$_['button_add_new_customer'] = 'Add New Customer';
$_['button_add'] = 'Add';
$_['button_cancel'] = 'Cancel';
$_['button_apply_coupon'] = 'Apply Coupon';
$_['button_suspend_sale'] = 'Suspend Sale';
$_['button_resume_sale'] = 'Resume Sale';
$_['button_void_sale'] = 'Void Sale';
$_['button_complete_sale'] = 'Complete Sale';
$_['button_add_to_cart'] = 'Add to Cart';

// Errors
$_['error_product'] = 'Product not found!';
$_['error_update'] = 'Error updating cart!';
$_['error_remove'] = 'Error removing item!';
$_['error_coupon'] = 'Coupon is invalid!';
$_['error_order_complete'] = 'Error completing order!';
$_['error_suspend'] = 'Error suspending sale!';
$_['error_resume'] = 'Error resuming sale!';
$_['error_void'] = 'Error voiding sale!';
$_['error_print'] = 'Error printing receipt!';
$_['error_add_customer'] = 'Error adding customer!';
$_['error_customer_selection'] = 'Error selecting customer!';
$_['error_product_options'] = 'Error retrieving product options!';
$_['error_product_id'] = 'Product ID required!';
$_['error_order_not_found'] = 'Order not found!';
$_['error_permission'] = 'You do not have permission to modify POS!';
$_['error_name'] = 'Customer name must be between 1 and 32 characters!';
$_['error_phone'] = 'Phone number must be between 1 and 32 characters!';
$_['error_email'] = 'Email is invalid!';
$_['error_address_1'] = 'Address must be between 3 and 128 characters!';
$_['error_city'] = 'City must be between 2 and 128 characters!';
$_['error_zone'] = 'Please select a region/state!';
$_['error_customer_group'] = 'Please select a customer group!';



// POS
$_['heading_title']                = 'Point of Sale';
$_['text_pos']                     = 'Point of Sale';

// Top Controls
$_['text_select_category']         = 'Select Category';
$_['text_search_products']         = 'Search Products';

// Pricing Types
$_['text_retail']                  = 'Retail';
$_['text_wholesale']               = 'Wholesale';
$_['text_half_wholesale']          = 'Half Wholesale';
$_['text_custom']                  = 'Custom';

// Product List
$_['text_select_options']          = 'Select Options';
$_['text_add_to_cart']             = 'Add to Cart';
$_['text_price']                   = 'Price:';
$_['text_special_price']           = 'Special:';

// Cart
$_['text_cart']                    = 'Cart';
$_['text_qty']                     = 'Qty';
$_['text_subtotal']                = 'Subtotal';
$_['text_total']                   = 'Total';

// Customer
$_['text_select_customer']         = 'Select Customer';
$_['text_add_new_customer']        = 'Add New Customer';
$_['text_customer_details']        = 'Customer Details';

// Payment
$_['text_payment_method']          = 'Payment Method';
$_['text_amount_due']              = 'Amount Due';
$_['text_amount_paid']             = 'Amount Paid';
$_['text_change']                  = 'Change';

// Actions
$_['button_complete_sale']         = 'Complete Sale';
$_['button_cancel_sale']           = 'Cancel Sale';
$_['button_hold_sale']             = 'Hold Sale';
$_['button_retrieve_sale']         = 'Retrieve Sale';

// Messages
$_['text_sale_complete']           = 'Sale completed successfully!';
$_['text_sale_cancelled']          = 'Sale cancelled.';
$_['text_sale_held']               = 'Sale placed on hold.';
$_['text_sale_retrieved']          = 'Sale retrieved from hold.';

// Errors
$_['error_no_products']            = 'No products in cart.';
$_['error_invalid_quantity']       = 'Invalid quantity.';
$_['error_insufficient_stock']     = 'Insufficient stock for one or more products.';
$_['error_no_payment_method']      = 'No payment method selected.';
$_['error_amount_paid']            = 'Amount paid is less than total amount.';

// Options
$_['text_option']                  = 'Available Options';
$_['text_select']                  = 'Select';
$_['text_radio']                   = 'Radio';
$_['text_checkbox']                = 'Checkbox';
$_['text_input']                   = 'Input';
$_['text_text']                    = 'Text';
$_['text_textarea']                = 'Textarea';
$_['text_file']                    = 'File';
$_['text_date']                    = 'Date';
$_['text_datetime']                = 'Date &amp; Time';
$_['text_time']                    = 'Time';

// Branch
$_['text_current_branch']          = 'Current Branch:';
