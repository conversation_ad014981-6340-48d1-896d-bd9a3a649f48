{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="submit" form="documents\versioning-form" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fas fa-save"></i></button>
        <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-light"><i class="fas fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    
    <div class="card">
      <div class="card-header"><i class="fas fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}chart-bar{% elif screen_type == 'settings' %}cog{% else %}pencil-alt{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="card-body">
        <form id="documents\versioning-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-advanced_features">{{ text_advanced_features }}</label>
            <div class="col-sm-10">
              <input type="text" name="advanced_features" value="{{ advanced_features }}" placeholder="{{ text_advanced_features }}" id="input-advanced_features" class="form-control" />
              {% if error_advanced_features %}
                <div class="invalid-feedback">{{ error_advanced_features }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-all_versions">{{ text_all_versions }}</label>
            <div class="col-sm-10">
              <input type="text" name="all_versions" value="{{ all_versions }}" placeholder="{{ text_all_versions }}" id="input-all_versions" class="form-control" />
              {% if error_all_versions %}
                <div class="invalid-feedback">{{ error_all_versions }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-analytics">{{ text_analytics }}</label>
            <div class="col-sm-10">
              <input type="text" name="analytics" value="{{ analytics }}" placeholder="{{ text_analytics }}" id="input-analytics" class="form-control" />
              {% if error_analytics %}
                <div class="invalid-feedback">{{ error_analytics }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-approve">{{ text_approve }}</label>
            <div class="col-sm-10">
              <input type="text" name="approve" value="{{ approve }}" placeholder="{{ text_approve }}" id="input-approve" class="form-control" />
              {% if error_approve %}
                <div class="invalid-feedback">{{ error_approve }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-available_actions">{{ text_available_actions }}</label>
            <div class="col-sm-10">
              <input type="text" name="available_actions" value="{{ available_actions }}" placeholder="{{ text_available_actions }}" id="input-available_actions" class="form-control" />
              {% if error_available_actions %}
                <div class="invalid-feedback">{{ error_available_actions }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-available_versions">{{ text_available_versions }}</label>
            <div class="col-sm-10">
              <input type="text" name="available_versions" value="{{ available_versions }}" placeholder="{{ text_available_versions }}" id="input-available_versions" class="form-control" />
              {% if error_available_versions %}
                <div class="invalid-feedback">{{ error_available_versions }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-back">{{ text_back }}</label>
            <div class="col-sm-10">
              <input type="text" name="back" value="{{ back }}" placeholder="{{ text_back }}" id="input-back" class="form-control" />
              {% if error_back %}
                <div class="invalid-feedback">{{ error_back }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-base_documents">{{ text_base_documents }}</label>
            <div class="col-sm-10">
              <input type="text" name="base_documents" value="{{ base_documents }}" placeholder="{{ text_base_documents }}" id="input-base_documents" class="form-control" />
              {% if error_base_documents %}
                <div class="invalid-feedback">{{ error_base_documents }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-bulk_operations">{{ text_bulk_operations }}</label>
            <div class="col-sm-10">
              <input type="text" name="bulk_operations" value="{{ bulk_operations }}" placeholder="{{ text_bulk_operations }}" id="input-bulk_operations" class="form-control" />
              {% if error_bulk_operations %}
                <div class="invalid-feedback">{{ error_bulk_operations }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-cleanup_wizard">{{ text_cleanup_wizard }}</label>
            <div class="col-sm-10">
              <input type="text" name="cleanup_wizard" value="{{ cleanup_wizard }}" placeholder="{{ text_cleanup_wizard }}" id="input-cleanup_wizard" class="form-control" />
              {% if error_cleanup_wizard %}
                <div class="invalid-feedback">{{ error_cleanup_wizard }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-compare">{{ text_compare }}</label>
            <div class="col-sm-10">
              <input type="text" name="compare" value="{{ compare }}" placeholder="{{ text_compare }}" id="input-compare" class="form-control" />
              {% if error_compare %}
                <div class="invalid-feedback">{{ error_compare }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-comparison">{{ text_comparison }}</label>
            <div class="col-sm-10">
              <input type="text" name="comparison" value="{{ comparison }}" placeholder="{{ text_comparison }}" id="input-comparison" class="form-control" />
              {% if error_comparison %}
                <div class="invalid-feedback">{{ error_comparison }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-comparison_result">{{ text_comparison_result }}</label>
            <div class="col-sm-10">
              <input type="text" name="comparison_result" value="{{ comparison_result }}" placeholder="{{ text_comparison_result }}" id="input-comparison_result" class="form-control" />
              {% if error_comparison_result %}
                <div class="invalid-feedback">{{ error_comparison_result }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-comparison_stats">{{ text_comparison_stats }}</label>
            <div class="col-sm-10">
              <input type="text" name="comparison_stats" value="{{ comparison_stats }}" placeholder="{{ text_comparison_stats }}" id="input-comparison_stats" class="form-control" />
              {% if error_comparison_stats %}
                <div class="invalid-feedback">{{ error_comparison_stats }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-create_version">{{ text_create_version }}</label>
            <div class="col-sm-10">
              <input type="text" name="create_version" value="{{ create_version }}" placeholder="{{ text_create_version }}" id="input-create_version" class="form-control" />
              {% if error_create_version %}
                <div class="invalid-feedback">{{ error_create_version }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-document_types">{{ text_document_types }}</label>
            <div class="col-sm-10">
              <input type="text" name="document_types" value="{{ document_types }}" placeholder="{{ text_document_types }}" id="input-document_types" class="form-control" />
              {% if error_document_types %}
                <div class="invalid-feedback">{{ error_document_types }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-download">{{ text_download }}</label>
            <div class="col-sm-10">
              <input type="text" name="download" value="{{ download }}" placeholder="{{ text_download }}" id="input-download" class="form-control" />
              {% if error_download %}
                <div class="invalid-feedback">{{ error_download }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-edit">{{ text_edit }}</label>
            <div class="col-sm-10">
              <input type="text" name="edit" value="{{ edit }}" placeholder="{{ text_edit }}" id="input-edit" class="form-control" />
              {% if error_edit %}
                <div class="invalid-feedback">{{ error_edit }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="invalid-feedback">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-metadata_templates">{{ text_metadata_templates }}</label>
            <div class="col-sm-10">
              <input type="text" name="metadata_templates" value="{{ metadata_templates }}" placeholder="{{ text_metadata_templates }}" id="input-metadata_templates" class="form-control" />
              {% if error_metadata_templates %}
                <div class="invalid-feedback">{{ error_metadata_templates }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-pending_reviews">{{ text_pending_reviews }}</label>
            <div class="col-sm-10">
              <input type="text" name="pending_reviews" value="{{ pending_reviews }}" placeholder="{{ text_pending_reviews }}" id="input-pending_reviews" class="form-control" />
              {% if error_pending_reviews %}
                <div class="invalid-feedback">{{ error_pending_reviews }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-publish">{{ text_publish }}</label>
            <div class="col-sm-10">
              <input type="text" name="publish" value="{{ publish }}" placeholder="{{ text_publish }}" id="input-publish" class="form-control" />
              {% if error_publish %}
                <div class="invalid-feedback">{{ error_publish }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-recent_versions">{{ text_recent_versions }}</label>
            <div class="col-sm-10">
              <input type="text" name="recent_versions" value="{{ recent_versions }}" placeholder="{{ text_recent_versions }}" id="input-recent_versions" class="form-control" />
              {% if error_recent_versions %}
                <div class="invalid-feedback">{{ error_recent_versions }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-reviews">{{ text_reviews }}</label>
            <div class="col-sm-10">
              <input type="text" name="reviews" value="{{ reviews }}" placeholder="{{ text_reviews }}" id="input-reviews" class="form-control" />
              {% if error_reviews %}
                <div class="invalid-feedback">{{ error_reviews }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-settings">{{ text_settings }}</label>
            <div class="col-sm-10">
              <input type="text" name="settings" value="{{ settings }}" placeholder="{{ text_settings }}" id="input-settings" class="form-control" />
              {% if error_settings %}
                <div class="invalid-feedback">{{ error_settings }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-total">{{ text_total }}</label>
            <div class="col-sm-10">
              <input type="text" name="total" value="{{ total }}" placeholder="{{ text_total }}" id="input-total" class="form-control" />
              {% if error_total %}
                <div class="invalid-feedback">{{ error_total }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-usage_analytics">{{ text_usage_analytics }}</label>
            <div class="col-sm-10">
              <input type="text" name="usage_analytics" value="{{ usage_analytics }}" placeholder="{{ text_usage_analytics }}" id="input-usage_analytics" class="form-control" />
              {% if error_usage_analytics %}
                <div class="invalid-feedback">{{ error_usage_analytics }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="invalid-feedback">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-version">{{ text_version }}</label>
            <div class="col-sm-10">
              <input type="text" name="version" value="{{ version }}" placeholder="{{ text_version }}" id="input-version" class="form-control" />
              {% if error_version %}
                <div class="invalid-feedback">{{ error_version }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-version_1">{{ text_version_1 }}</label>
            <div class="col-sm-10">
              <input type="text" name="version_1" value="{{ version_1 }}" placeholder="{{ text_version_1 }}" id="input-version_1" class="form-control" />
              {% if error_version_1 %}
                <div class="invalid-feedback">{{ error_version_1 }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-version_2">{{ text_version_2 }}</label>
            <div class="col-sm-10">
              <input type="text" name="version_2" value="{{ version_2 }}" placeholder="{{ text_version_2 }}" id="input-version_2" class="form-control" />
              {% if error_version_2 %}
                <div class="invalid-feedback">{{ error_version_2 }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-version_history">{{ text_version_history }}</label>
            <div class="col-sm-10">
              <input type="text" name="version_history" value="{{ version_history }}" placeholder="{{ text_version_history }}" id="input-version_history" class="form-control" />
              {% if error_version_history %}
                <div class="invalid-feedback">{{ error_version_history }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-version_options">{{ text_version_options }}</label>
            <div class="col-sm-10">
              <input type="text" name="version_options" value="{{ version_options }}" placeholder="{{ text_version_options }}" id="input-version_options" class="form-control" />
              {% if error_version_options %}
                <div class="invalid-feedback">{{ error_version_options }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-version_policies">{{ text_version_policies }}</label>
            <div class="col-sm-10">
              <input type="text" name="version_policies" value="{{ version_policies }}" placeholder="{{ text_version_policies }}" id="input-version_policies" class="form-control" />
              {% if error_version_policies %}
                <div class="invalid-feedback">{{ error_version_policies }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-version_stats">{{ text_version_stats }}</label>
            <div class="col-sm-10">
              <input type="text" name="version_stats" value="{{ version_stats }}" placeholder="{{ text_version_stats }}" id="input-version_stats" class="form-control" />
              {% if error_version_stats %}
                <div class="invalid-feedback">{{ error_version_stats }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-version_statuses">{{ text_version_statuses }}</label>
            <div class="col-sm-10">
              <input type="text" name="version_statuses" value="{{ version_statuses }}" placeholder="{{ text_version_statuses }}" id="input-version_statuses" class="form-control" />
              {% if error_version_statuses %}
                <div class="invalid-feedback">{{ error_version_statuses }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-versioned_documents">{{ text_versioned_documents }}</label>
            <div class="col-sm-10">
              <input type="text" name="versioned_documents" value="{{ versioned_documents }}" placeholder="{{ text_versioned_documents }}" id="input-versioned_documents" class="form-control" />
              {% if error_versioned_documents %}
                <div class="invalid-feedback">{{ error_versioned_documents }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-versioning_stats">{{ text_versioning_stats }}</label>
            <div class="col-sm-10">
              <input type="text" name="versioning_stats" value="{{ versioning_stats }}" placeholder="{{ text_versioning_stats }}" id="input-versioning_stats" class="form-control" />
              {% if error_versioning_stats %}
                <div class="invalid-feedback">{{ error_versioning_stats }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-versioning_strategies">{{ text_versioning_strategies }}</label>
            <div class="col-sm-10">
              <input type="text" name="versioning_strategies" value="{{ versioning_strategies }}" placeholder="{{ text_versioning_strategies }}" id="input-versioning_strategies" class="form-control" />
              {% if error_versioning_strategies %}
                <div class="invalid-feedback">{{ error_versioning_strategies }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}