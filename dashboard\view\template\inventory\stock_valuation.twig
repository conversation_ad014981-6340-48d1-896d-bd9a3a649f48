{{ header }}
{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="button" data-bs-toggle="tooltip" title="{{ button_filter }}" onclick="$('#filter-valuation').toggleClass('d-none');" class="btn btn-light d-md-none d-lg-none"><i class="fas fa-filter"></i></button>
        <a href="{{ recalculate }}" data-bs-toggle="tooltip" title="{{ button_recalculate }}" class="btn btn-warning" onclick="return confirm('{{ text_confirm_recalculate }}');"><i class="fas fa-calculator"></i></a>
        <a href="{{ compare }}" data-bs-toggle="tooltip" title="{{ button_compare }}" class="btn btn-info"><i class="fas fa-balance-scale"></i></a>
        <a href="{{ analytics }}" data-bs-toggle="tooltip" title="{{ button_analytics }}" class="btn btn-primary"><i class="fas fa-chart-line"></i></a>
        <a href="{{ export }}" data-bs-toggle="tooltip" title="{{ button_export }}" class="btn btn-success"><i class="fas fa-file-excel"></i></a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ol class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
          <li class="breadcrumb-item"><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    
    <!-- ملخص التقييم -->
    <div class="row mb-3">
      <div class="col-lg-3 col-md-6">
        <div class="card bg-primary text-white">
          <div class="card-body">
            <div class="d-flex justify-content-between">
              <div>
                <h4>{{ summary.total_products|number_format }}</h4>
                <p class="mb-0">{{ text_total_products }}</p>
              </div>
              <div class="align-self-center">
                <i class="fas fa-boxes fa-2x"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-md-6">
        <div class="card bg-success text-white">
          <div class="card-body">
            <div class="d-flex justify-content-between">
              <div>
                <h4>{{ summary.total_quantity|number_format(2) }}</h4>
                <p class="mb-0">{{ text_total_quantity }}</p>
              </div>
              <div class="align-self-center">
                <i class="fas fa-cubes fa-2x"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-md-6">
        <div class="card bg-warning text-white">
          <div class="card-body">
            <div class="d-flex justify-content-between">
              <div>
                <h4>{{ summary.total_value|number_format(2) }}</h4>
                <p class="mb-0">{{ text_total_value }}</p>
              </div>
              <div class="align-self-center">
                <i class="fas fa-dollar-sign fa-2x"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-md-6">
        <div class="card bg-info text-white">
          <div class="card-body">
            <div class="d-flex justify-content-between">
              <div>
                <h4>{{ summary.avg_value_per_product|number_format(2) }}</h4>
                <p class="mb-0">{{ text_avg_value_per_product }}</p>
              </div>
              <div class="align-self-center">
                <i class="fas fa-chart-bar fa-2x"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="row">
      <!-- فلاتر التقييم -->
      <div id="filter-valuation" class="col-lg-3 col-md-12 order-lg-last d-none d-lg-block mb-3">
        <div class="card">
          <div class="card-header"><i class="fas fa-filter"></i> {{ text_filter }}</div>
          <div class="card-body">
            <div class="mb-3">
              <label for="input-valuation-method" class="form-label">{{ entry_valuation_method }}</label>
              <select name="filter_valuation_method" id="input-valuation-method" class="form-select">
                {% for key, value in valuation_methods %}
                  <option value="{{ key }}" {% if key == filter_valuation_method %}selected="selected"{% endif %}>{{ value }}</option>
                {% endfor %}
              </select>
            </div>
            <div class="mb-3">
              <label for="input-warehouse" class="form-label">{{ entry_warehouse }}</label>
              <select name="filter_warehouse_id" id="input-warehouse" class="form-select">
                <option value="">{{ text_all_warehouses }}</option>
                {% for warehouse in warehouses %}
                  <option value="{{ warehouse.warehouse_id }}" {% if warehouse.warehouse_id == filter_warehouse_id %}selected="selected"{% endif %}>{{ warehouse.name }}</option>
                {% endfor %}
              </select>
            </div>
            <div class="mb-3">
              <label for="input-category" class="form-label">{{ entry_category }}</label>
              <select name="filter_category_id" id="input-category" class="form-select">
                <option value="">{{ text_all_categories }}</option>
                {% for category in categories %}
                  <option value="{{ category.category_id }}" {% if category.category_id == filter_category_id %}selected="selected"{% endif %}>{{ category.name }}</option>
                {% endfor %}
              </select>
            </div>
            <div class="mb-3">
              <label for="input-manufacturer" class="form-label">{{ entry_manufacturer }}</label>
              <select name="filter_manufacturer_id" id="input-manufacturer" class="form-select">
                <option value="">{{ text_all_manufacturers }}</option>
                {% for manufacturer in manufacturers %}
                  <option value="{{ manufacturer.manufacturer_id }}" {% if manufacturer.manufacturer_id == filter_manufacturer_id %}selected="selected"{% endif %}>{{ manufacturer.name }}</option>
                {% endfor %}
              </select>
            </div>
            <div class="mb-3">
              <label for="input-date" class="form-label">{{ entry_valuation_date }}</label>
              <input type="date" name="filter_date" value="{{ filter_date }}" id="input-date" class="form-control"/>
            </div>
            <div class="mb-3">
              <div class="form-check">
                <input type="checkbox" name="filter_include_zero_stock" value="1" id="input-include-zero" class="form-check-input" {% if filter_include_zero_stock %}checked="checked"{% endif %}>
                <label for="input-include-zero" class="form-check-label">{{ text_include_zero_stock }}</label>
              </div>
            </div>
            <div class="text-end">
              <button type="button" id="button-filter" class="btn btn-light"><i class="fas fa-filter"></i> {{ button_filter }}</button>
            </div>
          </div>
        </div>
        
        <!-- معلومات طريقة التقييم -->
        <div class="card mt-3">
          <div class="card-header"><i class="fas fa-info-circle"></i> {{ text_valuation_method_info }}</div>
          <div class="card-body">
            <div id="method-info">
              {% if filter_valuation_method == 'fifo' %}
                <p><strong>{{ text_fifo }}:</strong> {{ help_fifo }}</p>
              {% elseif filter_valuation_method == 'lifo' %}
                <p><strong>{{ text_lifo }}:</strong> {{ help_lifo }}</p>
              {% elseif filter_valuation_method == 'wac' %}
                <p><strong>{{ text_wac }}:</strong> {{ help_wac }}</p>
              {% elseif filter_valuation_method == 'standard' %}
                <p><strong>{{ text_standard_cost }}:</strong> {{ help_standard_cost }}</p>
              {% elseif filter_valuation_method == 'latest' %}
                <p><strong>{{ text_latest_cost }}:</strong> {{ help_latest_cost }}</p>
              {% elseif filter_valuation_method == 'average' %}
                <p><strong>{{ text_average_cost }}:</strong> {{ help_average_cost }}</p>
              {% endif %}
            </div>
          </div>
        </div>
      </div>
      
      <!-- قائمة التقييمات -->
      <div class="col-lg-9 col-md-12">
        <div class="card">
          <div class="card-header">
            <i class="fas fa-calculator"></i> {{ text_list }}
            <div class="card-tools">
              <span class="badge bg-primary">{{ filter_valuation_method|upper }}</span>
              <span class="badge bg-secondary">{{ filter_date }}</span>
            </div>
          </div>
          <div class="card-body">
            {% if valuations %}
            <div class="table-responsive">
              <table class="table table-bordered table-hover">
                <thead>
                  <tr>
                    <td><a href="{{ sort_product }}" {% if sort == 'pd.name' %}class="{% if order == 'ASC' %}asc{% else %}desc{% endif %}"{% endif %}>{{ column_product }}</a></td>
                    <td>{{ column_model }}</td>
                    <td>{{ column_sku }}</td>
                    <td><a href="{{ sort_warehouse }}" {% if sort == 'w.name' %}class="{% if order == 'ASC' %}asc{% else %}desc{% endif %}"{% endif %}>{{ column_warehouse }}</a></td>
                    <td>{{ column_category }}</td>
                    <td class="text-center"><a href="{{ sort_quantity }}" {% if sort == 'quantity' %}class="{% if order == 'ASC' %}asc{% else %}desc{% endif %}"{% endif %}>{{ column_quantity }}</a></td>
                    <td class="text-center"><a href="{{ sort_unit_cost }}" {% if sort == 'unit_cost' %}class="{% if order == 'ASC' %}asc{% else %}desc{% endif %}"{% endif %}>{{ column_unit_cost }}</a></td>
                    <td class="text-center"><a href="{{ sort_total_value }}" {% if sort == 'total_value' %}class="{% if order == 'ASC' %}asc{% else %}desc{% endif %}"{% endif %}>{{ column_total_value }}</a></td>
                    <td class="text-center">{{ column_profit_margin }}</td>
                    <td class="text-center">{{ column_turnover_ratio }}</td>
                    <td class="text-center">{{ column_last_updated }}</td>
                  </tr>
                </thead>
                <tbody>
                  {% for valuation in valuations %}
                  <tr>
                    <td>{{ valuation.product_name }}</td>
                    <td>{{ valuation.model }}</td>
                    <td>{{ valuation.sku }}</td>
                    <td>{{ valuation.warehouse_name }}</td>
                    <td>{{ valuation.category_name }}</td>
                    <td class="text-center">
                      <span class="badge bg-{% if valuation.quantity > 0 %}success{% else %}danger{% endif %}">
                        {{ valuation.quantity }}
                      </span>
                    </td>
                    <td class="text-center">{{ valuation.unit_cost }}</td>
                    <td class="text-center">
                      <strong>{{ valuation.total_value }}</strong>
                    </td>
                    <td class="text-center">
                      {% if valuation.profit_margin != 'N/A' %}
                        <span class="badge bg-{% if valuation.profit_margin|replace({'%': ''})|number_format > 20 %}success{% elseif valuation.profit_margin|replace({'%': ''})|number_format > 10 %}warning{% else %}danger{% endif %}">
                          {{ valuation.profit_margin }}
                        </span>
                      {% else %}
                        <span class="text-muted">{{ valuation.profit_margin }}</span>
                      {% endif %}
                    </td>
                    <td class="text-center">
                      {% if valuation.turnover_ratio != 'N/A' %}
                        <span class="badge bg-{% if valuation.turnover_ratio > 4 %}success{% elseif valuation.turnover_ratio > 2 %}warning{% else %}danger{% endif %}">
                          {{ valuation.turnover_ratio }}
                        </span>
                      {% else %}
                        <span class="text-muted">{{ valuation.turnover_ratio }}</span>
                      {% endif %}
                    </td>
                    <td class="text-center">{{ valuation.last_updated }}</td>
                  </tr>
                  {% endfor %}
                </tbody>
                <tfoot>
                  <tr class="table-info">
                    <td colspan="5"><strong>{{ text_totals }}</strong></td>
                    <td class="text-center"><strong>{{ summary.total_quantity|number_format(2) }}</strong></td>
                    <td class="text-center">-</td>
                    <td class="text-center"><strong>{{ summary.total_value|number_format(2) }}</strong></td>
                    <td class="text-center">-</td>
                    <td class="text-center">-</td>
                    <td class="text-center">-</td>
                  </tr>
                </tfoot>
              </table>
            </div>
            <div class="row">
              <div class="col-sm-6 text-start">{{ pagination }}</div>
              <div class="col-sm-6 text-end">{{ results }}</div>
            </div>
            {% else %}
            <div class="text-center">
              <i class="fas fa-calculator fa-3x text-muted mb-3"></i>
              <h4>{{ text_no_results }}</h4>
              <p class="text-muted">{{ text_no_valuation_message }}</p>
              <a href="{{ recalculate }}" class="btn btn-primary" onclick="return confirm('{{ text_confirm_recalculate }}');">
                <i class="fas fa-calculator"></i> {{ button_recalculate_now }}
              </a>
            </div>
            {% endif %}
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript">
// تطبيق الفلاتر
$('#button-filter').on('click', function() {
    var url = 'index.php?route=inventory/stock_valuation&user_token={{ user_token }}';
    
    var filter_valuation_method = $('select[name=\'filter_valuation_method\']').val();
    if (filter_valuation_method) {
        url += '&filter_valuation_method=' + encodeURIComponent(filter_valuation_method);
    }
    
    var filter_warehouse_id = $('select[name=\'filter_warehouse_id\']').val();
    if (filter_warehouse_id) {
        url += '&filter_warehouse_id=' + filter_warehouse_id;
    }
    
    var filter_category_id = $('select[name=\'filter_category_id\']').val();
    if (filter_category_id) {
        url += '&filter_category_id=' + filter_category_id;
    }
    
    var filter_manufacturer_id = $('select[name=\'filter_manufacturer_id\']').val();
    if (filter_manufacturer_id) {
        url += '&filter_manufacturer_id=' + filter_manufacturer_id;
    }
    
    var filter_date = $('input[name=\'filter_date\']').val();
    if (filter_date) {
        url += '&filter_date=' + filter_date;
    }
    
    var filter_include_zero_stock = $('input[name=\'filter_include_zero_stock\']:checked').val();
    if (filter_include_zero_stock) {
        url += '&filter_include_zero_stock=' + filter_include_zero_stock;
    }
    
    location = url;
});

// تحديث معلومات طريقة التقييم عند التغيير
$('#input-valuation-method').on('change', function() {
    var method = $(this).val();
    var info = '';
    
    switch(method) {
        case 'fifo':
            info = '<p><strong>{{ text_fifo }}:</strong> {{ help_fifo }}</p>';
            break;
        case 'lifo':
            info = '<p><strong>{{ text_lifo }}:</strong> {{ help_lifo }}</p>';
            break;
        case 'wac':
            info = '<p><strong>{{ text_wac }}:</strong> {{ help_wac }}</p>';
            break;
        case 'standard':
            info = '<p><strong>{{ text_standard_cost }}:</strong> {{ help_standard_cost }}</p>';
            break;
        case 'latest':
            info = '<p><strong>{{ text_latest_cost }}:</strong> {{ help_latest_cost }}</p>';
            break;
        case 'average':
            info = '<p><strong>{{ text_average_cost }}:</strong> {{ help_average_cost }}</p>';
            break;
    }
    
    $('#method-info').html(info);
});

// تطبيق الفلتر تلقائياً عند تغيير طريقة التقييم
$('#input-valuation-method').on('change', function() {
    $('#button-filter').click();
});
</script>

{{ footer }}
