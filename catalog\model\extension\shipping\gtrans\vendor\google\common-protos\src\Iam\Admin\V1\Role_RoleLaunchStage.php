<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/iam/admin/v1/iam.proto

namespace Google\Iam\Admin\V1;

if (false) {
    /**
     * This class is deprecated. Use Google\Iam\Admin\V1\Role\RoleLaunchStage instead.
     * @deprecated
     */
    class Role_RoleLaunchStage {}
}
class_exists(Role\RoleLaunchStage::class);
@trigger_error('Google\Iam\Admin\V1\Role_RoleLaunchStage is deprecated and will be removed in the next major release. Use Google\Iam\Admin\V1\Role\RoleLaunchStage instead', E_USER_DEPRECATED);

