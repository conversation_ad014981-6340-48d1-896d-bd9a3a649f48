<?php
/**
 * تحكم التنبؤ بالتدفق النقدي المتقدم
 * نظام تنبؤ ذكي بالتدفقات النقدية مع تحليل السيناريوهات والذكاء الاصطناعي
 * يدعم التنبؤ قصير وطويل المدى مع تحليل المخاطر
 */
class ControllerAccountsCashFlowForecasting extends Controller {
    private $error = array();
    private $central_service;

    public function __construct($registry) {
        parent::__construct($registry);

        // تحميل الخدمات المركزية
        $this->load->model('core/central_service_manager');
        $this->central_service = $this->model_core_central_service_manager;
    }

    public function index() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/cash_flow_forecasting') ||
            !$this->user->hasKey('accounting_cash_flow_forecasting_view')) {

            $this->central_service->logActivity('unauthorized_access', 'accounts',
                $this->language->get('log_unauthorized_access_cash_flow'), [
                'user_id' => $this->user->getId(),
                'ip_address' => $this->request->server['REMOTE_ADDR']
            ]);

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/cash_flow_forecasting');
        $this->document->setTitle($this->language->get('heading_title'));
        $this->load->model('accounts/cash_flow_forecasting');

        // إضافة CSS و JavaScript المتقدم
        $this->document->addStyle('view/stylesheet/accounts/cash_flow_forecasting.css');
        $this->document->addScript('view/javascript/accounts/cash_flow_forecasting.js');
        $this->document->addScript('view/javascript/jquery/accounting.min.js');
        $this->document->addScript('view/javascript/jquery/select2.min.js');
        $this->document->addStyle('view/javascript/jquery/select2.min.css');
        $this->document->addScript('view/javascript/jquery/daterangepicker.min.js');
        $this->document->addStyle('view/javascript/jquery/daterangepicker.css');
        $this->document->addScript('view/javascript/jquery/chart.min.js');
        $this->document->addScript('view/javascript/jquery/datatables.min.js');
        $this->document->addStyle('view/javascript/jquery/datatables.min.css');

        // تسجيل الوصول للشاشة
        $this->central_service->logActivity('view', 'accounts',
            $this->language->get('log_view_cash_flow_screen'), [
            'user_id' => $this->user->getId(),
            'screen' => 'cash_flow_forecasting'
        ]);

        // معالجة الطلبات
        if ($this->request->server['REQUEST_METHOD'] == 'POST') {
            if (isset($this->request->post['action'])) {
                switch ($this->request->post['action']) {
                    case 'create_forecast':
                        $this->createForecast();
                        break;
                    case 'generate_forecast':
                        $this->generateForecast();
                        break;
                    case 'scenario_analysis':
                        $this->scenarioAnalysis();
                        break;
                    case 'ai_prediction':
                        $this->aiPrediction();
                        break;
                }
            }
        }

        $data = $this->getCommonData();

        // جلب التنبؤات الحالية
        $data['forecasts'] = $this->model_accounts_cash_flow_forecasting->getForecasts();
        
        // جلب التنبؤات النشطة
        $data['active_forecasts'] = $this->model_accounts_cash_flow_forecasting->getActiveForecasts();

        // جلب إحصائيات التدفق النقدي
        $data['cash_flow_statistics'] = $this->model_accounts_cash_flow_forecasting->getCashFlowStatistics();

        // جلب التحليل السريع
        $data['quick_analysis'] = $this->model_accounts_cash_flow_forecasting->getQuickAnalysis();

        // جلب السيناريوهات المحفوظة
        $data['saved_scenarios'] = $this->model_accounts_cash_flow_forecasting->getSavedScenarios();

        // روابط Ajax
        $data['ajax_create_url'] = $this->url->link('accounts/cash_flow_forecasting/create', 'user_token=' . $this->session->data['user_token'], true);
        $data['ajax_generate_url'] = $this->url->link('accounts/cash_flow_forecasting/generate', 'user_token=' . $this->session->data['user_token'], true);
        $data['ajax_scenario_url'] = $this->url->link('accounts/cash_flow_forecasting/scenario', 'user_token=' . $this->session->data['user_token'], true);
        $data['ajax_ai_url'] = $this->url->link('accounts/cash_flow_forecasting/ai_predict', 'user_token=' . $this->session->data['user_token'], true);

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/cash_flow_forecasting', $data));
    }

    /**
     * إنشاء تنبؤ جديد
     */
    public function create() {
        // فحص الصلاحيات المتقدمة
        if (!$this->user->hasKey('accounting_cash_flow_forecasting_create')) {
            $json['error'] = $this->language->get('error_permission_create');
            $this->response->addHeader('Content-Type: application/json');
            $this->response->setOutput(json_encode($json));
            return;
        }

        $this->load->model('accounts/cash_flow_forecasting');
        $json = array();

        try {
            $forecast_data = array(
                'forecast_name' => $this->request->post['forecast_name'],
                'forecast_type' => $this->request->post['forecast_type'],
                'period_start' => $this->request->post['period_start'],
                'period_end' => $this->request->post['period_end'],
                'currency' => $this->request->post['currency'],
                'forecast_method' => $this->request->post['forecast_method'],
                'base_scenario' => $this->request->post['base_scenario'],
                'include_historical' => isset($this->request->post['include_historical']),
                'forecast_lines' => $this->request->post['forecast_lines']
            );

            $forecast_id = $this->model_accounts_cash_flow_forecasting->createForecast($forecast_data);

            if ($forecast_id) {
                $json['success'] = $this->language->get('text_forecast_created');
                $json['forecast_id'] = $forecast_id;
                
                // تسجيل العملية
                $this->central_service->logActivity('create', 'accounts',
                    $this->language->get('log_forecast_created'), [
                    'user_id' => $this->user->getId(),
                    'forecast_id' => $forecast_id,
                    'forecast_name' => $forecast_data['forecast_name']
                ]);

                // إرسال إشعار
                $this->central_service->sendNotification([
                    'type' => 'cash_flow_forecast_created',
                    'title' => $this->language->get('notification_forecast_title'),
                    'message' => sprintf($this->language->get('notification_forecast_message'), $forecast_data['forecast_name']),
                    'user_id' => $this->user->getId(),
                    'url' => $this->url->link('accounts/cash_flow_forecasting/view', 'forecast_id=' . $forecast_id)
                ]);

            } else {
                $json['error'] = $this->language->get('error_forecast_failed');
            }

        } catch (Exception $e) {
            $json['error'] = $e->getMessage();
            
            // تسجيل الخطأ
            $this->central_service->logActivity('error', 'accounts',
                'Cash flow forecast creation failed: ' . $e->getMessage(), [
                'user_id' => $this->user->getId(),
                'error_details' => $e->getTraceAsString()
            ]);
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * توليد التنبؤ التلقائي
     */
    public function generate() {
        if (!$this->user->hasKey('accounting_cash_flow_forecasting_create')) {
            $json['error'] = $this->language->get('error_permission_generate');
            $this->response->addHeader('Content-Type: application/json');
            $this->response->setOutput(json_encode($json));
            return;
        }

        $this->load->model('accounts/cash_flow_forecasting');
        $json = array();

        try {
            $generation_data = array(
                'forecast_id' => $this->request->post['forecast_id'],
                'generation_method' => $this->request->post['generation_method'],
                'historical_periods' => $this->request->post['historical_periods'],
                'seasonality_adjustment' => isset($this->request->post['seasonality_adjustment']),
                'trend_analysis' => isset($this->request->post['trend_analysis']),
                'external_factors' => $this->request->post['external_factors']
            );

            $result = $this->model_accounts_cash_flow_forecasting->generateForecast($generation_data);

            if ($result) {
                $json['success'] = $this->language->get('text_forecast_generated');
                $json['forecast_data'] = $result;
                
                // تسجيل العملية
                $this->central_service->logActivity('generate', 'accounts',
                    $this->language->get('log_forecast_generated'), [
                    'user_id' => $this->user->getId(),
                    'forecast_id' => $generation_data['forecast_id'],
                    'method' => $generation_data['generation_method']
                ]);

            } else {
                $json['error'] = $this->language->get('error_generation_failed');
            }

        } catch (Exception $e) {
            $json['error'] = $e->getMessage();
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * تحليل السيناريوهات
     */
    public function scenario() {
        if (!$this->user->hasKey('accounting_cash_flow_forecasting_view')) {
            $json['error'] = $this->language->get('error_permission_scenario');
            $this->response->addHeader('Content-Type: application/json');
            $this->response->setOutput(json_encode($json));
            return;
        }

        $this->load->model('accounts/cash_flow_forecasting');
        $json = array();

        try {
            $scenario_data = array(
                'forecast_id' => $this->request->post['forecast_id'],
                'scenario_type' => $this->request->post['scenario_type'], // optimistic, pessimistic, realistic
                'adjustment_factors' => $this->request->post['adjustment_factors'],
                'risk_factors' => $this->request->post['risk_factors'],
                'save_scenario' => isset($this->request->post['save_scenario']),
                'scenario_name' => $this->request->post['scenario_name']
            );

            $scenario_result = $this->model_accounts_cash_flow_forecasting->analyzeScenario($scenario_data);

            if ($scenario_result) {
                $json['success'] = true;
                $json['scenario_data'] = $scenario_result;
                
                // تسجيل العملية
                $this->central_service->logActivity('scenario', 'accounts',
                    $this->language->get('log_scenario_analyzed'), [
                    'user_id' => $this->user->getId(),
                    'forecast_id' => $scenario_data['forecast_id'],
                    'scenario_type' => $scenario_data['scenario_type']
                ]);

            } else {
                $json['error'] = $this->language->get('error_scenario_failed');
            }

        } catch (Exception $e) {
            $json['error'] = $e->getMessage();
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * التنبؤ بالذكاء الاصطناعي
     */
    public function ai_predict() {
        if (!$this->user->hasKey('accounting_cash_flow_forecasting_ai')) {
            $json['error'] = $this->language->get('error_permission_ai');
            $this->response->addHeader('Content-Type: application/json');
            $this->response->setOutput(json_encode($json));
            return;
        }

        $this->load->model('accounts/cash_flow_forecasting');
        $this->load->model('ai/ai_assistant');
        $json = array();

        try {
            $ai_data = array(
                'forecast_id' => $this->request->post['forecast_id'],
                'prediction_horizon' => $this->request->post['prediction_horizon'],
                'include_external_data' => isset($this->request->post['include_external_data']),
                'market_indicators' => $this->request->post['market_indicators'],
                'confidence_level' => $this->request->post['confidence_level']
            );

            // استخدام الذكاء الاصطناعي للتنبؤ
            $ai_prediction = $this->model_ai_ai_assistant->predictCashFlow($ai_data);

            if ($ai_prediction) {
                $json['success'] = true;
                $json['ai_prediction'] = $ai_prediction;
                
                // تسجيل العملية
                $this->central_service->logActivity('ai_predict', 'accounts',
                    $this->language->get('log_ai_prediction'), [
                    'user_id' => $this->user->getId(),
                    'forecast_id' => $ai_data['forecast_id'],
                    'confidence' => $ai_prediction['confidence_score']
                ]);

            } else {
                $json['error'] = $this->language->get('error_ai_prediction_failed');
            }

        } catch (Exception $e) {
            $json['error'] = $e->getMessage();
        }

        $this->response->addHeader('Content-Type: application/json');
        $this->response->setOutput(json_encode($json));
    }

    /**
     * عرض تفاصيل التنبؤ
     */
    public function view() {
        if (!$this->user->hasPermission('access', 'accounts/cash_flow_forecasting') ||
            !$this->user->hasKey('accounting_cash_flow_forecasting_view')) {
            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $forecast_id = isset($this->request->get['forecast_id']) ? (int)$this->request->get['forecast_id'] : 0;

        if (!$forecast_id) {
            $this->response->redirect($this->url->link('accounts/cash_flow_forecasting'));
            return;
        }

        $this->load->language('accounts/cash_flow_forecasting');
        $this->load->model('accounts/cash_flow_forecasting');

        $data = $this->getCommonData();
        
        // جلب بيانات التنبؤ
        $forecast = $this->model_accounts_cash_flow_forecasting->getForecast($forecast_id);
        
        if (!$forecast) {
            $this->response->redirect($this->url->link('accounts/cash_flow_forecasting'));
            return;
        }

        $data['forecast'] = $forecast;
        $data['forecast_lines'] = $this->model_accounts_cash_flow_forecasting->getForecastLines($forecast_id);
        $data['variance_analysis'] = $this->model_accounts_cash_flow_forecasting->getVarianceAnalysis($forecast_id);
        $data['scenarios'] = $this->model_accounts_cash_flow_forecasting->getForecastScenarios($forecast_id);
        $data['chart_data'] = $this->model_accounts_cash_flow_forecasting->getChartData($forecast_id);

        $this->document->setTitle($this->language->get('heading_title_view') . ' - ' . $forecast['forecast_name']);

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/cash_flow_forecasting_view', $data));
    }

    /**
     * تقرير التدفق النقدي
     */
    public function report() {
        if (!$this->user->hasPermission('access', 'accounts/cash_flow_forecasting') ||
            !$this->user->hasKey('accounting_cash_flow_forecasting_view')) {
            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/cash_flow_forecasting');
        $this->load->model('accounts/cash_flow_forecasting');

        $data = $this->getCommonData();
        
        // جلب بيانات التقرير
        $filter = array(
            'date_from' => $this->request->get['date_from'] ?? date('Y-01-01'),
            'date_to' => $this->request->get['date_to'] ?? date('Y-12-31'),
            'forecast_type' => $this->request->get['forecast_type'] ?? '',
            'currency' => $this->request->get['currency'] ?? '',
            'include_scenarios' => isset($this->request->get['include_scenarios'])
        );

        $data['report_data'] = $this->model_accounts_cash_flow_forecasting->generateReport($filter);
        $data['filter'] = $filter;

        $this->document->setTitle($this->language->get('heading_title_report'));

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/cash_flow_forecasting_report', $data));
    }

    /**
     * جلب البيانات المشتركة
     */
    private function getCommonData() {
        $data = array();

        // النصوص
        $data['heading_title'] = $this->language->get('heading_title');
        $data['text_list'] = $this->language->get('text_list');
        $data['text_no_results'] = $this->language->get('text_no_results');
        $data['text_confirm'] = $this->language->get('text_confirm');

        // الأزرار
        $data['button_create'] = $this->language->get('button_create');
        $data['button_generate'] = $this->language->get('button_generate');
        $data['button_scenario'] = $this->language->get('button_scenario');
        $data['button_ai_predict'] = $this->language->get('button_ai_predict');
        $data['button_view'] = $this->language->get('button_view');
        $data['button_cancel'] = $this->language->get('button_cancel');

        // الروابط
        $data['cancel'] = $this->url->link('accounts/cash_flow_forecasting', 'user_token=' . $this->session->data['user_token'], true);
        $data['user_token'] = $this->session->data['user_token'];

        return $data;
    }
}
