<?php
// HTTP
define('HTTP_SERVER', 'https://demo.codaym.com/');

// HTTPS
define('HTTPS_SERVER', 'https://demo.codaym.com/');

// DIR
define('DIR_APPLICATION', '/home/<USER>/public_html/catalog/');
define('DIR_SYSTEM', '/home/<USER>/public_html/system/');
define('DIR_IMAGE', '/home/<USER>/public_html/image/');
define('DIR_STORAGE', '/home/<USER>/storage/');
define('DIR_LANGUAGE', DIR_APPLICATION . 'language/');
define('DIR_TEMPLATE', DIR_APPLICATION . 'view/template/');
define('DIR_CONFIG', DIR_SYSTEM . 'config/');
define('DIR_CACHE', DIR_STORAGE . 'cache/');
define('DIR_DOWNLOAD', DIR_STORAGE . 'download/');
define('DIR_LOGS', DIR_STORAGE . 'logs/');
define('DIR_MODIFICATION', DIR_STORAGE . 'modification/');
define('DIR_SESSION', DIR_STORAGE . 'session/');
define('DIR_UPLOAD', DIR_STORAGE . 'upload/');

// DB
define('DB_DRIVER', 'mysqli');
define('DB_HOSTNAME', 'localhost');
define('DB_USERNAME', 'demo_admin');
define('DB_PASSWORD', 'MNmfmaN20vBc1@wJ');
define('DB_DATABASE', 'demo_aym_erp');
define('DB_PORT', '3306');
define('DB_PREFIX', 'cod_');