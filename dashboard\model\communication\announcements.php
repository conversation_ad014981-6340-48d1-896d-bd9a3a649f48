<?php
/**
 * نموذج الإعلانات المتقدم
 * Advanced Announcements Model
 * 
 * نموذج البيانات لنظام الإعلانات المتقدم مع تكامل catalog/inventory
 * مطور بمستوى عالمي لتفوق على Odoo
 * 
 * @package    AYM ERP
 * <AUTHOR> ERP Development Team
 * @copyright  2024 AYM ERP
 * @license    Proprietary
 * @version    1.0.0
 * @link       https://aym-erp.com
 * @since      2024-12-19
 */

class ModelCommunicationAnnouncements extends Model {
    
    /**
     * إضافة إعلان جديد
     */
    public function addAnnouncement($data) {
        $this->db->query("INSERT INTO " . DB_PREFIX . "announcement SET 
            title = '" . $this->db->escape($data['title']) . "',
            content = '" . $this->db->escape($data['content']) . "',
            type = '" . $this->db->escape($data['type']) . "',
            priority = '" . $this->db->escape($data['priority']) . "',
            status = '" . $this->db->escape($data['status']) . "',
            start_date = '" . $this->db->escape($data['start_date']) . "',
            end_date = '" . $this->db->escape($data['end_date']) . "',
            target_groups = '" . $this->db->escape(json_encode($data['target_groups'] ?? [])) . "',
            target_users = '" . $this->db->escape(json_encode($data['target_users'] ?? [])) . "',
            created_by = '" . (int)$this->user->getId() . "',
            created_at = NOW()");
        
        $announcement_id = $this->db->getLastId();
        
        // تسجيل النشاط باستخدام الخدمات المركزية
        try {
            $this->load->model('core/central_service_manager');
            $this->model_core_central_service_manager->logCreate(
                'communication',
                'announcement',
                $announcement_id,
                'تم إنشاء إعلان جديد: ' . ($data['title'] ?? 'بدون عنوان'),
                [
                    'announcement_data' => $data,
                    'target_groups' => $data['target_groups'] ?? [],
                    'target_users' => $data['target_users'] ?? []
                ]
            );
        } catch (Exception $e) {
            error_log("Failed to log announcement creation via central service: " . $e->getMessage());

            // النظام الاحتياطي
            try {
                $this->load->model('activity_log');
                $this->model_activity_log->logCreate('communication', 'announcement', $announcement_id, $data);
            } catch (Exception $e2) {
                error_log("CRITICAL: Both logging systems failed: " . $e2->getMessage());
            }
        }
        
        return $announcement_id;
    }
    
    /**
     * تحديث إعلان
     */
    public function editAnnouncement($announcement_id, $data) {
        $this->db->query("UPDATE " . DB_PREFIX . "announcement SET 
            title = '" . $this->db->escape($data['title']) . "',
            content = '" . $this->db->escape($data['content']) . "',
            type = '" . $this->db->escape($data['type']) . "',
            priority = '" . $this->db->escape($data['priority']) . "',
            status = '" . $this->db->escape($data['status']) . "',
            start_date = '" . $this->db->escape($data['start_date']) . "',
            end_date = '" . $this->db->escape($data['end_date']) . "',
            target_groups = '" . $this->db->escape(json_encode($data['target_groups'] ?? [])) . "',
            target_users = '" . $this->db->escape(json_encode($data['target_users'] ?? [])) . "',
            updated_at = NOW()
            WHERE announcement_id = '" . (int)$announcement_id . "'");
        
        // تسجيل النشاط
        $this->load->model('activity_log');
        $this->model_activity_log->logUpdate('communication', 'announcement', $announcement_id, [], $data);
    }
    
    /**
     * حذف إعلان
     */
    public function deleteAnnouncement($announcement_id) {
        $announcement = $this->getAnnouncement($announcement_id);
        
        $this->db->query("DELETE FROM " . DB_PREFIX . "announcement WHERE announcement_id = '" . (int)$announcement_id . "'");
        $this->db->query("DELETE FROM " . DB_PREFIX . "announcement_view WHERE announcement_id = '" . (int)$announcement_id . "'");
        $this->db->query("DELETE FROM " . DB_PREFIX . "announcement_comment WHERE announcement_id = '" . (int)$announcement_id . "'");
        
        // تسجيل النشاط
        $this->load->model('activity_log');
        $this->model_activity_log->logDelete('communication', 'announcement', $announcement_id, $announcement);
    }
    
    /**
     * الحصول على إعلان
     */
    public function getAnnouncement($announcement_id) {
        $query = $this->db->query("SELECT a.*, CONCAT(u.firstname, ' ', u.lastname) as creator_name
            FROM " . DB_PREFIX . "announcement a
            LEFT JOIN " . DB_PREFIX . "user u ON (a.created_by = u.user_id)
            WHERE a.announcement_id = '" . (int)$announcement_id . "'");
        
        if ($query->num_rows) {
            $announcement = $query->row;
            $announcement['target_groups'] = json_decode($announcement['target_groups'], true);
            $announcement['target_users'] = json_decode($announcement['target_users'], true);
            return $announcement;
        }
        
        return false;
    }
    
    /**
     * الحصول على الإعلانات النشطة
     */
    public function getActiveAnnouncements($data = []) {
        $sql = "SELECT a.*, CONCAT(u.firstname, ' ', u.lastname) as creator_name,
                COUNT(av.view_id) as view_count
                FROM " . DB_PREFIX . "announcement a
                LEFT JOIN " . DB_PREFIX . "user u ON (a.created_by = u.user_id)
                LEFT JOIN " . DB_PREFIX . "announcement_view av ON (a.announcement_id = av.announcement_id)
                WHERE a.status = 'active' 
                AND (a.start_date <= NOW() OR a.start_date IS NULL)
                AND (a.end_date >= NOW() OR a.end_date IS NULL)";
        
        if (!empty($data['filter_type'])) {
            $sql .= " AND a.type = '" . $this->db->escape($data['filter_type']) . "'";
        }
        
        $sql .= " GROUP BY a.announcement_id ORDER BY a.priority DESC, a.created_at DESC";
        
        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }
            
            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }
            
            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }
        
        $query = $this->db->query($sql);
        
        return $query->rows;
    }
    
    /**
     * الحصول على الإعلانات المجدولة
     */
    public function getScheduledAnnouncements($data = []) {
        $sql = "SELECT a.*, CONCAT(u.firstname, ' ', u.lastname) as creator_name
                FROM " . DB_PREFIX . "announcement a
                LEFT JOIN " . DB_PREFIX . "user u ON (a.created_by = u.user_id)
                WHERE a.status = 'scheduled' 
                AND a.start_date > NOW()
                ORDER BY a.start_date ASC";
        
        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }
            
            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }
            
            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }
        
        $query = $this->db->query($sql);
        
        return $query->rows;
    }
    
    /**
     * الحصول على الإعلانات المنتهية
     */
    public function getExpiredAnnouncements($data = []) {
        $sql = "SELECT a.*, CONCAT(u.firstname, ' ', u.lastname) as creator_name
                FROM " . DB_PREFIX . "announcement a
                LEFT JOIN " . DB_PREFIX . "user u ON (a.created_by = u.user_id)
                WHERE a.end_date < NOW()
                ORDER BY a.end_date DESC";
        
        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }
            
            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }
            
            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }
        
        $query = $this->db->query($sql);
        
        return $query->rows;
    }
    
    /**
     * الحصول على إعلانات catalog
     */
    public function getCatalogAnnouncements($type) {
        $query = $this->db->query("SELECT a.*
            FROM " . DB_PREFIX . "announcement a
            WHERE a.type = 'catalog' 
            AND a.status = 'active'
            AND JSON_EXTRACT(a.metadata, '$.catalog_type') = '" . $this->db->escape($type) . "'
            ORDER BY a.created_at DESC
            LIMIT 5");
        
        return $query->rows;
    }
    
    /**
     * الحصول على إعلانات inventory
     */
    public function getInventoryAnnouncements($type) {
        $query = $this->db->query("SELECT a.*
            FROM " . DB_PREFIX . "announcement a
            WHERE a.type = 'inventory' 
            AND a.status = 'active'
            AND JSON_EXTRACT(a.metadata, '$.inventory_type') = '" . $this->db->escape($type) . "'
            ORDER BY a.created_at DESC
            LIMIT 5");
        
        return $query->rows;
    }
    
    /**
     * تسجيل مشاهدة إعلان
     */
    public function recordView($announcement_id, $user_id) {
        $check = $this->db->query("SELECT view_id FROM " . DB_PREFIX . "announcement_view 
            WHERE announcement_id = '" . (int)$announcement_id . "' 
            AND user_id = '" . (int)$user_id . "'");
        
        if (!$check->num_rows) {
            $this->db->query("INSERT INTO " . DB_PREFIX . "announcement_view SET
                announcement_id = '" . (int)$announcement_id . "',
                user_id = '" . (int)$user_id . "',
                viewed_at = NOW()");
        }
    }
    
    /**
     * الحصول على إحصائيات الإعلانات
     */
    public function getTotalAnnouncements() {
        $query = $this->db->query("SELECT COUNT(*) as total FROM " . DB_PREFIX . "announcement");
        return $query->row['total'];
    }
    
    public function getActiveAnnouncementsCount() {
        $query = $this->db->query("SELECT COUNT(*) as total FROM " . DB_PREFIX . "announcement 
            WHERE status = 'active' 
            AND (start_date <= NOW() OR start_date IS NULL)
            AND (end_date >= NOW() OR end_date IS NULL)");
        return $query->row['total'];
    }
    
    public function getScheduledAnnouncementsCount() {
        $query = $this->db->query("SELECT COUNT(*) as total FROM " . DB_PREFIX . "announcement 
            WHERE status = 'scheduled' AND start_date > NOW()");
        return $query->row['total'];
    }
    
    public function getTodayViews() {
        $query = $this->db->query("SELECT COUNT(*) as total FROM " . DB_PREFIX . "announcement_view 
            WHERE DATE(viewed_at) = CURDATE()");
        return $query->row['total'];
    }
    
    public function getEngagementRate() {
        $query = $this->db->query("SELECT 
            COUNT(DISTINCT a.announcement_id) as total_announcements,
            COUNT(DISTINCT av.announcement_id) as viewed_announcements
            FROM " . DB_PREFIX . "announcement a
            LEFT JOIN " . DB_PREFIX . "announcement_view av ON (a.announcement_id = av.announcement_id)
            WHERE a.status = 'active'");
        
        $row = $query->row;
        if ($row['total_announcements'] > 0) {
            return round(($row['viewed_announcements'] / $row['total_announcements']) * 100, 2);
        }
        
        return 0;
    }
    
    /**
     * الحصول على الإعلانات العاجلة
     */
    public function getUrgentAnnouncements() {
        $query = $this->db->query("SELECT a.*
            FROM " . DB_PREFIX . "announcement a
            WHERE a.priority = 'urgent' 
            AND a.status = 'active'
            AND (a.start_date <= NOW() OR a.start_date IS NULL)
            AND (a.end_date >= NOW() OR a.end_date IS NULL)
            ORDER BY a.created_at DESC
            LIMIT 3");
        
        return $query->rows;
    }
    
    /**
     * الحصول على الإعلانات الشخصية للمستخدم
     */
    public function getPersonalAnnouncements($user_id) {
        $query = $this->db->query("SELECT a.*
            FROM " . DB_PREFIX . "announcement a
            WHERE a.status = 'active'
            AND (a.start_date <= NOW() OR a.start_date IS NULL)
            AND (a.end_date >= NOW() OR a.end_date IS NULL)
            AND (JSON_CONTAINS(a.target_users, '\"" . (int)$user_id . "\"')
                 OR JSON_CONTAINS(a.target_groups, (
                     SELECT JSON_ARRAYAGG(user_group_id)
                     FROM " . DB_PREFIX . "user_to_group
                     WHERE user_id = '" . (int)$user_id . "'
                 )))
            ORDER BY a.priority DESC, a.created_at DESC
            LIMIT 5");

        return $query->rows;
    }

    /**
     * الحصول على مرفقات الإعلان
     */
    public function getAnnouncementAttachments($announcement_id) {
        $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "announcement_attachment
            WHERE announcement_id = '" . (int)$announcement_id . "'
            ORDER BY sort_order ASC");

        return $query->rows;
    }

    /**
     * الحصول على تعليقات الإعلان
     */
    public function getAnnouncementComments($announcement_id) {
        $query = $this->db->query("SELECT ac.*, CONCAT(u.firstname, ' ', u.lastname) as author_name
            FROM " . DB_PREFIX . "announcement_comment ac
            LEFT JOIN " . DB_PREFIX . "user u ON (ac.user_id = u.user_id)
            WHERE ac.announcement_id = '" . (int)$announcement_id . "'
            AND ac.status = 'approved'
            ORDER BY ac.created_at ASC");

        return $query->rows;
    }

    /**
     * الحصول على إحصائيات المشاهدة
     */
    public function getViewStats($announcement_id) {
        $query = $this->db->query("SELECT
            COUNT(*) as total_views,
            COUNT(DISTINCT user_id) as unique_viewers,
            DATE(viewed_at) as view_date,
            COUNT(*) as daily_views
            FROM " . DB_PREFIX . "announcement_view
            WHERE announcement_id = '" . (int)$announcement_id . "'
            GROUP BY DATE(viewed_at)
            ORDER BY view_date DESC");

        return $query->rows;
    }

    /**
     * الحصول على الإعلانات ذات الصلة
     */
    public function getRelatedAnnouncements($announcement_id) {
        $announcement = $this->getAnnouncement($announcement_id);
        if (!$announcement) {
            return [];
        }

        $query = $this->db->query("SELECT a.*
            FROM " . DB_PREFIX . "announcement a
            WHERE a.announcement_id != '" . (int)$announcement_id . "'
            AND a.type = '" . $this->db->escape($announcement['type']) . "'
            AND a.status = 'active'
            ORDER BY a.created_at DESC
            LIMIT 5");

        return $query->rows;
    }

    /**
     * إرسال إعلان جماعي
     */
    public function sendBulkAnnouncement($data) {
        try {
            $announcement_id = $this->addAnnouncement($data);

            $sent_count = 0;
            $targets = [];

            // جمع المستهدفين
            if (!empty($data['target_groups'])) {
                foreach ($data['target_groups'] as $group_id) {
                    $query = $this->db->query("SELECT user_id FROM " . DB_PREFIX . "user_to_group
                        WHERE user_group_id = '" . (int)$group_id . "'");
                    foreach ($query->rows as $row) {
                        $targets[] = $row['user_id'];
                    }
                }
            }

            if (!empty($data['target_users'])) {
                $targets = array_merge($targets, $data['target_users']);
            }

            $targets = array_unique($targets);
            $sent_count = count($targets);

            return [
                'success' => true,
                'sent_count' => $sent_count,
                'announcement_id' => $announcement_id
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * الحصول على إجمالي المشاهدات
     */
    public function getTotalViews() {
        $query = $this->db->query("SELECT COUNT(*) as total FROM " . DB_PREFIX . "announcement_view");
        return $query->row['total'];
    }

    /**
     * الحصول على متوسط التفاعل
     */
    public function getAverageEngagement() {
        $query = $this->db->query("SELECT
            AVG(view_count) as avg_views,
            AVG(comment_count) as avg_comments
            FROM (
                SELECT a.announcement_id,
                    COUNT(DISTINCT av.view_id) as view_count,
                    COUNT(DISTINCT ac.comment_id) as comment_count
                FROM " . DB_PREFIX . "announcement a
                LEFT JOIN " . DB_PREFIX . "announcement_view av ON (a.announcement_id = av.announcement_id)
                LEFT JOIN " . DB_PREFIX . "announcement_comment ac ON (a.announcement_id = ac.announcement_id)
                WHERE a.status = 'active'
                GROUP BY a.announcement_id
            ) as stats");

        return $query->row;
    }

    /**
     * الحصول على النوع الأكثر مشاهدة
     */
    public function getMostViewedType() {
        $query = $this->db->query("SELECT a.type, COUNT(av.view_id) as view_count
            FROM " . DB_PREFIX . "announcement a
            LEFT JOIN " . DB_PREFIX . "announcement_view av ON (a.announcement_id = av.announcement_id)
            WHERE a.status = 'active'
            GROUP BY a.type
            ORDER BY view_count DESC
            LIMIT 1");

        return $query->num_rows ? $query->row['type'] : '';
    }

    /**
     * الحصول على التحليلات حسب النوع
     */
    public function getAnalyticsByType() {
        $query = $this->db->query("SELECT a.type,
            COUNT(DISTINCT a.announcement_id) as total_announcements,
            COUNT(DISTINCT av.view_id) as total_views,
            COUNT(DISTINCT ac.comment_id) as total_comments
            FROM " . DB_PREFIX . "announcement a
            LEFT JOIN " . DB_PREFIX . "announcement_view av ON (a.announcement_id = av.announcement_id)
            LEFT JOIN " . DB_PREFIX . "announcement_comment ac ON (a.announcement_id = ac.announcement_id)
            GROUP BY a.type
            ORDER BY total_views DESC");

        return $query->rows;
    }

    /**
     * الحصول على التحليلات حسب الشهر
     */
    public function getAnalyticsByMonth() {
        $query = $this->db->query("SELECT
            DATE_FORMAT(a.created_at, '%Y-%m') as month,
            COUNT(DISTINCT a.announcement_id) as announcements_count,
            COUNT(DISTINCT av.view_id) as views_count
            FROM " . DB_PREFIX . "announcement a
            LEFT JOIN " . DB_PREFIX . "announcement_view av ON (a.announcement_id = av.announcement_id)
            WHERE a.created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
            GROUP BY month
            ORDER BY month DESC");

        return $query->rows;
    }

    /**
     * الحصول على اتجاهات التفاعل
     */
    public function getEngagementTrends() {
        $query = $this->db->query("SELECT
            DATE(av.viewed_at) as date,
            COUNT(*) as views,
            COUNT(DISTINCT av.user_id) as unique_viewers
            FROM " . DB_PREFIX . "announcement_view av
            WHERE av.viewed_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            GROUP BY DATE(av.viewed_at)
            ORDER BY date DESC");

        return $query->rows;
    }

    /**
     * الحصول على أفضل الإعلانات
     */
    public function getTopAnnouncements($limit = 10) {
        $query = $this->db->query("SELECT a.*,
            COUNT(DISTINCT av.view_id) as view_count,
            COUNT(DISTINCT ac.comment_id) as comment_count
            FROM " . DB_PREFIX . "announcement a
            LEFT JOIN " . DB_PREFIX . "announcement_view av ON (a.announcement_id = av.announcement_id)
            LEFT JOIN " . DB_PREFIX . "announcement_comment ac ON (a.announcement_id = ac.announcement_id)
            WHERE a.status = 'active'
            GROUP BY a.announcement_id
            ORDER BY view_count DESC, comment_count DESC
            LIMIT " . (int)$limit);

        return $query->rows;
    }

    /**
     * الحصول على تحليلات إعلانات الكتالوج
     */
    public function getCatalogAnnouncementsAnalytics() {
        $query = $this->db->query("SELECT
            JSON_EXTRACT(a.metadata, '$.catalog_type') as catalog_type,
            COUNT(*) as announcements_count,
            AVG(view_count) as avg_views
            FROM " . DB_PREFIX . "announcement a
            LEFT JOIN (
                SELECT announcement_id, COUNT(*) as view_count
                FROM " . DB_PREFIX . "announcement_view
                GROUP BY announcement_id
            ) av ON (a.announcement_id = av.announcement_id)
            WHERE a.type = 'catalog'
            GROUP BY catalog_type");

        return $query->rows;
    }

    /**
     * الحصول على تحليلات تنبيهات المخزون
     */
    public function getInventoryAlertsAnalytics() {
        $query = $this->db->query("SELECT
            JSON_EXTRACT(a.metadata, '$.inventory_type') as inventory_type,
            COUNT(*) as alerts_count,
            AVG(view_count) as avg_views,
            SUM(CASE WHEN av.view_count > 0 THEN 1 ELSE 0 END) as viewed_alerts
            FROM " . DB_PREFIX . "announcement a
            LEFT JOIN (
                SELECT announcement_id, COUNT(*) as view_count
                FROM " . DB_PREFIX . "announcement_view
                GROUP BY announcement_id
            ) av ON (a.announcement_id = av.announcement_id)
            WHERE a.type = 'inventory'
            GROUP BY inventory_type");

        return $query->rows;
    }
}
