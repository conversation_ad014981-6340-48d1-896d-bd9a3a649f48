<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/distribution.proto

namespace Google\Api\Distribution\BucketOptions;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Specifies a set of buckets with arbitrary widths.
 * There are `size(bounds) + 1` (= N) buckets. Bucket `i` has the following
 * boundaries:
 *    Upper bound (0 <= i < N-1):     bounds[i]
 *    Lower bound (1 <= i < N);       bounds[i - 1]
 * The `bounds` field must contain at least one element. If `bounds` has
 * only one element, then there are no finite buckets, and that single
 * element is the common boundary of the overflow and underflow buckets.
 *
 * Generated from protobuf message <code>google.api.Distribution.BucketOptions.Explicit</code>
 */
class Explicit extends \Google\Protobuf\Internal\Message
{
    /**
     * The values must be monotonically increasing.
     *
     * Generated from protobuf field <code>repeated double bounds = 1;</code>
     */
    private $bounds;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type float[]|\Google\Protobuf\Internal\RepeatedField $bounds
     *           The values must be monotonically increasing.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Api\Distribution::initOnce();
        parent::__construct($data);
    }

    /**
     * The values must be monotonically increasing.
     *
     * Generated from protobuf field <code>repeated double bounds = 1;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getBounds()
    {
        return $this->bounds;
    }

    /**
     * The values must be monotonically increasing.
     *
     * Generated from protobuf field <code>repeated double bounds = 1;</code>
     * @param float[]|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setBounds($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::DOUBLE);
        $this->bounds = $arr;

        return $this;
    }

}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(Explicit::class, \Google\Api\Distribution_BucketOptions_Explicit::class);

