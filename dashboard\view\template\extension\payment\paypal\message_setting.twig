{{ header }}{{ column_left }}
<div id="content" class="payment-paypal">
	<div class="page-header">
		<div class="container-fluid">
			<div class="pull-right">
				<button data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary button-save"><i class="fa fa-save"></i></button>
				<a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a>
			</div>
			<h1>{{ heading_title_main }}</h1>
			<ul class="breadcrumb">
				{% for breadcrumb in breadcrumbs %}
				<li><a href="{{ breadcrumb['href'] }}">{{ breadcrumb['text'] }}</a></li>
				{% endfor %}
			</ul>
		</div>
	</div>
	<div class="container-fluid">
		{% if error_warning %}
		<div class="alert alert-danger"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}</div>
		{% endif %}
		{% if text_version %}
		<div class="alert alert-info"><i class="fa fa-info-circle"></i> {{ text_version }}</div>
		{% endif %}
		<div class="panel panel-default">
			<div class="panel-heading">
				<h3 class="panel-title"><i class="fa fa-pencil"></i> {{ text_edit }}</h3>
			</div>
			<div class="panel-body">
				<form action="{{ action }}" method="post" enctype="multipart/form-data" id="form_payment">
					<a href="{{ href_dashboard }}" class="back-dashboard"><i class="icon icon-back-dashboard"></i>{{ text_tab_dashboard }}</a>
					<ul class="nav nav-tabs">
						<li class="nav-tab"><a href="{{ href_general }}" class="tab"><i class="tab-icon tab-icon-general"></i><span class="tab-title">{{ text_tab_general }}</span></a></li>
						<li class="nav-tab"><a href="{{ href_button }}" class="tab"><i class="tab-icon tab-icon-button"></i><span class="tab-title">{{ text_tab_button }}</span></a></li>
						<li class="nav-tab"><a href="{{ href_googlepay_button }}" class="tab"><i class="tab-icon tab-icon-googlepay-button"></i><span class="tab-title">{{ text_tab_googlepay_button }}</span></a></li>
						<li class="nav-tab"><a href="{{ href_applepay_button }}" class="tab"><i class="tab-icon tab-icon-applepay-button"></i><span class="tab-title">{{ text_tab_applepay_button }}</span></a></li>
						<li class="nav-tab"><a href="{{ href_card }}" class="tab"><i class="tab-icon tab-icon-card"></i><span class="tab-title">{{ text_tab_card }}</span></a></li>
						<li class="nav-tab"><a href="{{ href_message_configurator }}" class="tab"><i class="tab-icon tab-icon-message-configurator"></i><span class="tab-title">{{ text_tab_message_configurator }}</span></a></li>
						<li class="nav-tab active"><a href="{{ href_message_setting }}" class="tab"><i class="tab-icon tab-icon-message-setting"></i><span class="tab-title">{{ text_tab_message_setting }}</span></a></li>
						<li class="nav-tab"><a href="{{ href_order_status }}" class="tab"><i class="tab-icon tab-icon-order-status"></i><span class="tab-title">{{ text_tab_order_status }}</span></a></li>
						<li class="nav-tab"><a href="{{ href_contact }}" class="tab"><i class="tab-icon tab-icon-contact"></i><span class="tab-title">{{ text_tab_contact }}</span></a></li>
					</ul>
					<div class="section-content">
						<ul class="nav nav-pills">
							{% for message in setting['message'] %}
							{% if (message['page_code'] != 'checkout') %}
							<li class="nav-pill {% if (message['page_code'] == 'cart') %}active{% endif %}"><a href="#pill_{{ message['page_code'] }}" class="pill" data-toggle="tab">{{ attribute(_context, message['page_name']) }}</a></li>
							{% endif %}
							{% endfor %}
						</ul>
						<hr class="hr" />
						<div class="tab-content">
							{% for message in setting['message'] %}
							{% if (message['page_code'] != 'checkout') %}
							<div id="pill_{{ message['page_code'] }}" class="tab-pane {% if (message['page_code'] == 'cart') %}active{% endif %}">
								<div class="section-message-setting">
									<div class="row">
										<div class="col col-md-6">
											<legend class="legend">{{ text_message_settings }}</legend>
										</div>
									</div>
									{% if text_message_alert %}
									{#<div class="form-group">
										<p class="alert alert-info">{{ text_message_alert }}</p>
									</div>
									<div class="form-group">
										<p class="footnote">{{ text_message_footnote }}</p>
									</div>#}
									{% endif %}
									<div class="row">
										<div class="col col-md-6">
											<div class="form-group">
												<label class="control-label" for="input_message_{{ message['page_code'] }}_insert_tag">{{ entry_message_insert_tag }}</label>
												<input type="text" name="payment_paypal_setting[message][{{ message['page_code'] }}][insert_tag]" value="{{ message['insert_tag'] }}" id="input_message_{{ message['page_code'] }}_insert_tag" class="form-control" />
											</div>
										</div>
										<div class="col col-md-6">
											<div class="form-group">
												<label class="control-label" for="input_message_{{ message['page_code'] }}_insert_type">{{ entry_message_insert_type }}</label>
												<select name="payment_paypal_setting[message][{{ message['page_code'] }}][insert_type]" id="input_message_{{ message['page_code'] }}_insert_type" class="form-control">
													{% for message_insert_type in setting['message_insert_type'] %}
													{% if (message_insert_type['code'] == message['insert_type']) %}
													<option value="{{ message_insert_type['code'] }}" selected="selected">{{ attribute(_context, message_insert_type['name']) }}</option>
													{% else %}
													<option value="{{ message_insert_type['code'] }}">{{ attribute(_context, message_insert_type['name']) }}</option>
													{% endif %}
													{% endfor %}
												</select>
											</div>
										</div>									
									</div>
								</div>
							</div>
							{% endif %}
							{% endfor %}
						</div>
					</div>
				</form>
			</div>
		</div>
	</div>
</div>
<script type="text/javascript">

$('.payment-paypal').on('click', '.button-save', function() {
    $.ajax({
		type: 'post',
		url: $('#form_payment').attr('action'),
		data: $('#form_payment').serialize(),
		dataType: 'json',
		success: function(json) {
			$('.payment-paypal .alert-success').remove();
			
			if (json['success']) {
				$('.payment-paypal > .container-fluid').prepend('<div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i><button type="button" class="close" data-dismiss="alert">&times;</button> ' + json['success'] + '</div>');
				
				$('html, body').animate({scrollTop: $('.payment-paypal > .container-fluid .alert-success').offset().top}, 'slow');
			}
		},
		error: function(xhr, ajaxOptions, thrownError) {
			console.log(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
		}
    });  
});

$('.payment-paypal').on('click', '.button-agree', function() {
	$.ajax({
		type: 'post',
		url: '{{ agree_url }}',
		data: '',
		dataType: 'json',
		success: function(json) {
			$('.payment-paypal .alert').remove();
			
			if (json['success']) {
				$('.payment-paypal > .container-fluid').prepend('<div class="alert alert-success"><i class="fa fa-check-circle"></i><button type="button" class="close" data-dismiss="alert">&times;</button> ' + json['success'] + '</div>');
			}
		},
		error: function(xhr, ajaxOptions, thrownError) {
			console.log(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
		}
	});
});

</script>
{{ footer }}