{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <button type="submit" form="form-shipping" data-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fa fa-save"></i></button>
        <a href="{{ cancel }}" data-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-default"><i class="fa fa-reply"></i></a></div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-pencil"></i> {{ text_edit }}</h3>
      </div>
      <div class="panel-body">
        <form action="{{ action }}" method="post" enctype="multipart/form-data" id="form-shipping" class="form-horizontal">
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-key"><span data-toggle="tooltip" title="{{ help_api_key }}">{{ entry_api_key }}</span></label>
            <div class="col-sm-10">
              <input type="text" name="shipping_ec_ship_api_key" value="{{ shipping_ec_ship_api_key }}" placeholder="{{ entry_api_key }}" id="input-key" class="form-control" />
              {% if error_api_key %}
              <div class="text-danger">{{ error_api_key }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-api_username"><span data-toggle="tooltip" title="{{ help_api_username }}">{{ entry_api_username }}</span></label>
            <div class="col-sm-10">
              <input type="text" name="shipping_ec_ship_api_username" value="{{ shipping_ec_ship_api_username }}" placeholder="{{ entry_api_username }}" id="input-api_username" class="form-control" />
              {% if error_api_username %}
              <div class="text-danger">{{ error_api_username }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group required">
            <label class="col-sm-2 control-label" for="input-username"><span data-toggle="tooltip" title="{{ help_username }}">{{ entry_username }}</span></label>
            <div class="col-sm-10">
              <input type="text" name="shipping_ec_ship_username" value="{{ shipping_ec_ship_username }}" placeholder="{{ entry_username }}" id="input-username" class="form-control" />
              {% if error_username %}
              <div class="text-danger">{{ error_username }}</div>
              {% endif %}
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label"><span data-toggle="tooltip" title="{{ help_test }}">{{ entry_test }}</span></label>
            <div class="col-sm-10">
              <label class="radio-inline">
                {% if shipping_ec_ship_test %}
                <input type="radio" name="shipping_ec_ship_test" value="1" checked="checked" />
                {{ text_yes }}
                {% else %}
                <input type="radio" name="shipping_ec_ship_test" value="1" />
                {{ text_yes }}
                {% endif %}
              </label>
              <label class="radio-inline">
                {% if not shipping_ec_ship_test %}
                <input type="radio" name="shipping_ec_ship_test" value="0" checked="checked" />
                {{ text_no }}
                {% else %}
                <input type="radio" name="shipping_ec_ship_test" value="0" />
                {{ text_no }}
                {% endif %}
              </label>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label"><span data-toggle="tooltip" title="{{ help_service }}">{{ entry_service }}</span></label>
            <div class="col-sm-10">
              <div id="service" class="well well-sm" style="height: 150px; overflow: auto;">
                  <div class="checkbox">
                    <label>
                      {% if shipping_ec_ship_air_registered_mail %}
                      <input type="checkbox" name="shipping_ec_ship_air_registered_mail" value="1" checked="checked" />
                      {{ text_air_registered_mail }}
                      {% else %}
                      <input type="checkbox" name="shipping_ec_ship_air_registered_mail" value="1" />
                      {{ text_air_registered_mail }}
                      {% endif %}
                    </label>
                  </div>
                  <div class="checkbox">
                    <label>
                      {% if shipping_ec_ship_air_parcel %}
                      <input type="checkbox" name="shipping_ec_ship_air_parcel" value="1" checked="checked" />
                      {{ text_air_parcel }}
                      {% else %}
                      <input type="checkbox" name="shipping_ec_ship_air_parcel" value="1" />
                      {{ text_air_parcel }}
                      {% endif %}
                    </label>
                  </div>
                  <div class="checkbox">
                    <label>
                      {% if shipping_ec_ship_e_express_service_to_us %}
                      <input type="checkbox" name="shipping_ec_ship_e_express_service_to_us" value="1" checked="checked" />
                      {{ text_e_express_service_to_us }}
                      {% else %}
                      <input type="checkbox" name="shipping_ec_ship_e_express_service_to_us" value="1" />
                      {{ text_e_express_service_to_us }}
                      {% endif %}
                    </label>
                  </div>
                  <div class="checkbox">
                    <label>
                      {% if shipping_ec_ship_e_express_service_to_canada %}
                      <input type="checkbox" name="shipping_ec_ship_e_express_service_to_canada" value="1" checked="checked" />
                      {{ text_e_express_service_to_canada }}
                      {% else %}
                      <input type="checkbox" name="shipping_ec_ship_e_express_service_to_canada" value="1" />
                      {{ text_e_express_service_to_canada }}
                      {% endif %}
                    </label>
                  </div>
                  <div class="checkbox">
                    <label>
                      {% if shipping_ec_ship_e_express_service_to_united_kingdom %}
                      <input type="checkbox" name="shipping_ec_ship_e_express_service_to_united_kingdom" value="1" checked="checked" />
                      {{ text_e_express_service_to_united_kingdom }}
                      {% else %}
                      <input type="checkbox" name="shipping_ec_ship_e_express_service_to_united_kingdom" value="1" />
                      {{ text_e_express_service_to_united_kingdom }}
                      {% endif %}
                    </label>
                  </div>
                  <div class="checkbox">
                    <label>
                      {% if shipping_ec_ship_e_express_service_to_russia %}
                      <input type="checkbox" name="shipping_ec_ship_e_express_service_to_russia" value="1" checked="checked" />
                      {{ text_e_express_service_to_russia }}
                      {% else %}
                      <input type="checkbox" name="shipping_ec_ship_e_express_service_to_russia" value="1" />
                      {{ text_e_express_service_to_russia }}
                      {% endif %}
                    </label>
                  </div>
                  <div class="checkbox">
                    <label>
                      {% if shipping_ec_ship_e_express_service_one %}
                      <input type="checkbox" name="shipping_ec_ship_e_express_service_one" value="1" checked="checked" />
                      {{ text_e_express_service_one }}
                      {% else %}
                      <input type="checkbox" name="shipping_ec_ship_e_express_service_one" value="1" />
                      {{ text_e_express_service_one }}
                      {% endif %}
                    </label>
                  </div>
                  <div class="checkbox">
                    <label>
                      {% if shipping_ec_ship_e_express_service_two %}
                      <input type="checkbox" name="shipping_ec_ship_e_express_service_two" value="1" checked="checked" />
                      {{ text_e_express_service_two }}
                      {% else %}
                      <input type="checkbox" name="shipping_ec_ship_e_express_service_two" value="1" />
                      {{ text_e_express_service_two }}
                      {% endif %}
                    </label>
                  </div>
                  <div class="checkbox">
                    <label>
                      {% if shipping_ec_ship_speed_post %}
                      <input type="checkbox" name="shipping_ec_ship_speed_post" value="1" checked="checked" />
                      {{ text_speed_post }}
                      {% else %}
                      <input type="checkbox" name="shipping_ec_ship_speed_post" value="1" />
                      {{ text_speed_post }}
                      {% endif %}
                    </label>
                  </div>
                  <div class="checkbox">
                    <label>
                      {% if shipping_ec_ship_smart_post %}
                      <input type="checkbox" name="shipping_ec_ship_smart_post" value="1" checked="checked" />
                      {{ text_smart_post }}
                      {% else %}
                      <input type="checkbox" name="shipping_ec_ship_smart_post" value="1" />
                      {{ text_smart_post }}
                      {% endif %}
                    </label>
                  </div>
                  <div class="checkbox">
                    <label>
                      {% if shipping_ec_ship_local_courier_post %}
                      <input type="checkbox" name="shipping_ec_ship_local_courier_post" value="1" checked="checked" />
                      {{ text_local_courier_post }}
                      {% else %}
                      <input type="checkbox" name="shipping_ec_ship_local_courier_post" value="1" />
                      {{ text_local_courier_post }}
                      {% endif %}
                    </label>
                  </div>
                  <div class="checkbox">
                    <label>
                      {% if shipping_ec_ship_local_parcel %}
                      <input type="checkbox" name="shipping_ec_ship_local_parcel" value="1" checked="checked" />
                     {{ text_local_parcel }}
                      {% else %}
                     <input type="checkbox" name="shipping_ec_ship_local_parcel" value="1" />
                    {{ text_local_parcel }}
                      {% endif %}
                    </label>
                  </div>
              </div>
              <button type="button" onclick="$(this).parent().find(':checkbox').prop('checked', true);" class="btn btn-link">{{ text_select_all }}</button> / <button type="button" onclick="$(this).parent().find(':checkbox').prop('checked', false);" class="btn btn-link">{{ text_unselect_all }}</button></div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-weight-class"><span data-toggle="tooltip" title="{{ help_weight_class }}">{{ entry_weight_class }}</span></label>
            <div class="col-sm-10">
              <select name="shipping_ec_ship_weight_class_id" id="input-weight-class" class="form-control">
                {% for weight_class in weight_classes %}
                {% if weight_class['weight_class_id'] == shipping_ec_ship_weight_class_id %}
                <option value="{{ weight_class['weight_class_id'] }}" selected="selected">{{ weight_class['title'] }}</option>
                {% else %}
                <option value="{{ weight_class['weight_class_id'] }}">{{ weight_class['title'] }}</option>
                {% endif %}
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-tax-class">{{ entry_tax_class }}</label>
            <div class="col-sm-10">
              <select name="shipping_ec_ship_tax_class_id" id="input-tax-class" class="form-control">
                <option value="0">{{ text_none }}</option>
                {% for tax_class in tax_classes %}
                {% if tax_class['tax_class_id'] == shipping_ec_ship_tax_class_id %}
                <option value="{{ tax_class['tax_class_id'] }}" selected="selected">{{ tax_class['title'] }}</option>
                {% else %}
                <option value="{{ tax_class['tax_class_id'] }}">{{ tax_class['title'] }}</option>
                {% endif %}
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-geo-zone">{{ entry_geo_zone }}</label>
            <div class="col-sm-10">
              <select name="shipping_ec_ship_geo_zone_id" id="input-geo-zone" class="form-control">
                <option value="0">{{ text_all_zones }}</option>
                {% for geo_zone in geo_zones %}
                {% if geo_zone['geo_zone_id'] == shipping_ec_ship_geo_zone_id %}
                <option value="{{ geo_zone['geo_zone_id'] }}" selected="selected">{{ geo_zone['name'] }}</option>
                {% else %}
                <option value="{{ geo_zone['geo_zone_id'] }}">{{ geo_zone['name'] }}</option>
                {% endif %}
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-status">{{ entry_status }}</label>
            <div class="col-sm-10">
              <select name="shipping_ec_ship_status" id="input-status" class="form-control">
                {% if (shipping_ec_ship_status) %}
                <option value="1" selected="selected">{{ text_enabled }}</option>
                <option value="0">{{ text_disabled }}</option>
                {% else %}
                <option value="1">{{ text_enabled }}</option>
                <option value="0" selected="selected">{{ text_disabled }}</option>
                {% endif %}
              </select>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-2 control-label" for="input-sort-order">{{ entry_sort_order }}</label>
            <div class="col-sm-10">
              <input type="text" name="shipping_ec_ship_sort_order" value="{{ shipping_ec_ship_sort_order }}" placeholder="{{ entry_sort_order }}" id="input-sort-order" class="form-control" />
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
{{ footer }}
