{{header}}
{{column_left}}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <ul class="breadcrumb">
        <li><a href="{{ home }}">{{ text_home }}</a></li>
        <li><a href="#">{{ heading_title }}</a></li>
      </ul>
      <div class="pull-right">
        <a href="{{ add }}" data-toggle="tooltip" title="{{ button_add }}" class="btn btn-primary">
          <i class="fa fa-plus"></i></a>
      </div>
    </div>
  </div>

  <div class="container-fluid">
    {% if error_warning %}
      <div class="alert alert-danger"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
        <button type="button" class="close" data-dismiss="alert">&times;</button>
      </div>
    {% endif %}
    {% if success %}
      <div class="alert alert-success alert-dismissible">
        <i class="fa fa-check-circle"></i> {{ success }}
        <button type="button" class="close" data-dismiss="alert">&times;</button>
      </div>
    {% endif %}

    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-list"></i> {{ text_list }}</h3>
      </div>
      <div class="panel-body">
        <form action="{{ delete }}" method="post" enctype="multipart/form-data" id="form-stock-count">
          <div class="table-responsive">
            <table class="table table-bordered table-hover">
              <thead>
                <tr>
                  <td>{{ column_stock_count_id }}</td>
                  <td>{{ column_reference_code }}</td>
                  <td>{{ column_branch }}</td>
                  <td>{{ column_count_date }}</td>
                  <td>{{ column_status }}</td>
                  <td>{{ column_created_by }}</td>
                  <td>{{ column_created_at }}</td>
                  <td>{{ column_action }}</td>
                </tr>
              </thead>
              <tbody>
              {% if stock_counts %}
                {% for sc in stock_counts %}
                  <tr>
                    <td>{{ sc.stock_count_id }}</td>
                    <td>{{ sc.reference_code }}</td>
                    <td>{{ sc.branch_name }}</td>
                    <td>{{ sc.count_date }}</td>
                    <td>
                      {% if sc.status == 'draft' %}{{ text_draft }}
                      {% elseif sc.status == 'in_progress' %}{{ text_in_progress }}
                      {% elseif sc.status == 'completed' %}{{ text_completed }}
                      {% elseif sc.status == 'cancelled' %}{{ text_cancelled }}
                      {% endif %}
                    </td>
                    <td>{{ sc.created_by_name }}</td>
                    <td>{{ sc.created_at }}</td>
                    <td>
                      <a href="{{ sc.edit }}" class="btn btn-primary" title="{{ button_edit }}"><i class="fa fa-pencil"></i></a>
                    </td>
                  </tr>
                {% endfor %}
              {% else %}
                <tr>
                  <td class="text-center" colspan="8">{{ text_no_results }}</td>
                </tr>
              {% endif %}
              </tbody>
            </table>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
{{footer}}