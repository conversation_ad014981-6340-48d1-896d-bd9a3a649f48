<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/cloud/translate/v3/translation_service.proto

namespace Google\Cloud\Translate\V3;

if (false) {
    /**
     * This class is deprecated. Use Google\Cloud\Translate\V3\BatchTranslateMetadata\State instead.
     * @deprecated
     */
    class BatchTranslateMetadata_State {}
}
class_exists(BatchTranslateMetadata\State::class);
@trigger_error('Google\Cloud\Translate\V3\BatchTranslateMetadata_State is deprecated and will be removed in a future release. Use Google\Cloud\Translate\V3\BatchTranslateMetadata\State instead', E_USER_DEPRECATED);

