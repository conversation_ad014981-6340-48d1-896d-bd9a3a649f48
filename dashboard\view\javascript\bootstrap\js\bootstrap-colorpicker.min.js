/*!
 * Bootstrap Colorpicker - Simple and customizable colorpicker component for Twitter Bootstrap.
 * @package bootstrap-colorpicker
 * @version v3.0.0-wip
 * @license MIT
 * @link https://farbelous.github.io/bootstrap-colorpicker/
 * @link https://github.com/farbelous/bootstrap-colorpicker.git
 */
!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(require("jQuery")):"function"==typeof define&&define.amd?define("bootstrap-colorpicker",["jQuery"],e):"object"==typeof exports?exports["bootstrap-colorpicker"]=e(require("jQuery")):t["bootstrap-colorpicker"]=e(t.jQuery)}(this,function(o){return function(o){var r={};function i(t){if(r[t])return r[t].exports;var e=r[t]={i:t,l:!1,exports:{}};return o[t].call(e.exports,e,e.exports,i),e.l=!0,e.exports}return i.m=o,i.c=r,i.d=function(t,e,o){i.o(t,e)||Object.defineProperty(t,e,{configurable:!1,enumerable:!0,get:o})},i.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return i.d(e,"a",e),e},i.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},i.p="",i(i.s=3)}([function(t,e){t.exports=o},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r,i=function(){function r(t,e){for(var o=0;o<e.length;o++){var r=e[o];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(t,e,o){return e&&r(t.prototype,e),o&&r(t,o),t}}(),n=o(0),a=(r=n)&&r.__esModule?r:{default:r};var s=function(){function o(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};if(function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,o),this.colorpicker=t,this.options=e,!this.colorpicker.element||!this.colorpicker.element.length)throw new Error("Extension: this.colorpicker.element is not valid");this.colorpicker.element.on("colorpickerCreate.colorpicker-ext",a.default.proxy(this.onCreate,this)),this.colorpicker.element.on("colorpickerDestroy.colorpicker-ext",a.default.proxy(this.onDestroy,this)),this.colorpicker.element.on("colorpickerUpdate.colorpicker-ext",a.default.proxy(this.onUpdate,this)),this.colorpicker.element.on("colorpickerChange.colorpicker-ext",a.default.proxy(this.onChange,this)),this.colorpicker.element.on("colorpickerInvalid.colorpicker-ext",a.default.proxy(this.onInvalid,this)),this.colorpicker.element.on("colorpickerShow.colorpicker-ext",a.default.proxy(this.onShow,this)),this.colorpicker.element.on("colorpickerHide.colorpicker-ext",a.default.proxy(this.onHide,this)),this.colorpicker.element.on("colorpickerEnable.colorpicker-ext",a.default.proxy(this.onEnable,this)),this.colorpicker.element.on("colorpickerDisable.colorpicker-ext",a.default.proxy(this.onDisable,this))}return i(o,[{key:"resolveColor",value:function(t){return!1}},{key:"onCreate",value:function(t){}},{key:"onDestroy",value:function(t){this.colorpicker.element.off(".colorpicker-ext")}},{key:"onUpdate",value:function(t){}},{key:"onChange",value:function(t){}},{key:"onInvalid",value:function(t){}},{key:"onHide",value:function(t){}},{key:"onShow",value:function(t){}},{key:"onDisable",value:function(t){}},{key:"onEnable",value:function(t){}}]),o}();e.default=s},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r,i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},n=function(){function r(t,e){for(var o=0;o<e.length;o++){var r=e[o];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(t,e,o){return e&&r(t.prototype,e),o&&r(t,o),t}}(),a=o(1),s=(r=a)&&r.__esModule?r:{default:r};var l={colors:null,namesAsValues:!0},c=function(t){function r(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,r);var o=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(r.__proto__||Object.getPrototypeOf(r)).call(this,t,Object.assign({},l,e)));return Array.isArray(o.options.colors)||"object"===i(o.options.colors)||(o.options.colors=null),o}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(r,s.default),n(r,[{key:"colors",get:function(){return this.options.colors}}]),n(r,[{key:"getLength",value:function(){return this.options.colors?Array.isArray(this.options.colors)?this.options.colors.length:"object"===i(this.options.colors)?Object.keys(this.options.colors).length:0:0}},{key:"resolveColor",value:function(t){return!(this.getLength()<=0)&&(Array.isArray(this.options.colors)?0<=this.options.colors.indexOf(t)?t:0<=this.options.colors.indexOf(t.toUpperCase())?t.toUpperCase():0<=this.options.colors.indexOf(t.toLowerCase())&&t.toLowerCase():"object"===i(this.options.colors)&&(this.options.namesAsValues?this.getName(t,this.getName("#"+t,this.getValue(t,!1))):this.getValue(t,!1)))}},{key:"getName",value:function(t){var e=1<arguments.length&&void 0!==arguments[1]&&arguments[1];if("string"!=typeof t||!this.options.colors)return e;for(var o in this.options.colors)if(this.options.colors.hasOwnProperty(o)&&this.options.colors[o].toLowerCase()===t.toLowerCase())return o;return e}},{key:"getValue",value:function(t){var e=1<arguments.length&&void 0!==arguments[1]&&arguments[1];return"string"==typeof t&&this.options.colors&&this.options.colors.hasOwnProperty(t)?this.options.colors[t]:e}}]),r}();e.default=c},function(t,e,o){"use strict";var a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s=r(o(4)),l=r(o(0));function r(t){return t&&t.__esModule?t:{default:t}}var c="colorpicker";l.default[c]=s.default,l.default.fn[c]=function(r){var i=Array.prototype.slice.call(arguments,1),t=1===this.length,n=null,e=this.each(function(){var t=(0,l.default)(this),e=t.data(c),o="object"===(void 0===r?"undefined":a(r))?r:{};e||(e=new s.default(this,o),t.data(c,e)),"string"==typeof r?"colorpicker"===r?n=e:l.default.isFunction(e[r])?n=e[r].apply(e,i):(i.length&&(e[r]=i[0]),n=e[r]):n=t});return t?n:e},l.default.fn[c].constructor=s.default},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r=function(){function r(t,e){for(var o=0;o<e.length;o++){var r=e[o];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(t,e,o){return e&&r(t.prototype,e),o&&r(t,o),t}}(),s=n(o(5)),i=n(o(1)),a=n(o(7)),l=n(o(8)),c=n(o(0));function n(t){return t&&t.__esModule?t:{default:t}}var u=0,h=function(){function n(t,e){var o=this;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,n),u+=1,this.id=u,this.element=(0,c.default)(t).addClass("colorpicker-element"),this.element.attr("data-colorpicker-id",this.id),this.options=Object.assign({},a.default,e,this.element.data()),this.extensions=[],Array.isArray(this.options.extensions)||(this.options.extensions=[]),this.component=this.options.component,this.component=!1!==this.component&&this.element.find(this.component),this.component&&0===this.component.length&&(this.component=!1),this.container=!0===this.options.container?this.element:this.options.container,this.container=!1!==this.container&&(0,c.default)(this.container),this.currentSlider=null,this.mousePointer={left:0,top:0},this.lastEvent={name:null,e:null},this.input=this.element.is("input")?this.element:!!this.options.input&&this.element.find(this.options.input),this.input&&0===this.input.length&&(this.input=!1),this.options.debug&&this.options.extensions.push({name:"Debugger"}),this.options.extensions.forEach(function(t){o.addExtension(t.name,l.default[t.name.toLowerCase()],t)});var r=!1!==this.options.color?this.options.color:this.getValue();this.color=!!r&&this.createColor(r),!1===this.options.format&&(this.options.format=this.color.format),this.disabled=!1;var i=this.picker=(0,c.default)(this.options.template);this.options.customClass&&i.addClass(this.options.customClass),this.options.inline?i.addClass("colorpicker-inline colorpicker-visible"):i.addClass("colorpicker-hidden"),this.options.horizontal&&i.addClass("colorpicker-horizontal"),(this.options.useAlpha||this.hasColor()&&this.color.hasTransparency())&&!1!==this.options.useAlpha&&(this.options.useAlpha=!0,i.addClass("colorpicker-with-alpha")),"right"===this.options.align&&i.addClass("colorpicker-right"),!0===this.options.inline&&i.addClass("colorpicker-no-arrow"),i.on("mousedown.colorpicker touchstart.colorpicker",c.default.proxy(function(t){t.target===t.currentTarget&&t.preventDefault()},this)),i.find(".colorpicker-saturation, .colorpicker-hue, .colorpicker-alpha").on("mousedown.colorpicker touchstart.colorpicker",c.default.proxy(this._mousedown,this)),i.appendTo(this.container?this.container:(0,c.default)("body")),this.hasInput()&&(this.input.on({"keyup.colorpicker":c.default.proxy(this._keyup,this)}),this.input.on({"change.colorpicker":c.default.proxy(this._change,this)}),!1===this.component&&this.element.on({"focus.colorpicker":c.default.proxy(this.show,this)}),!1===this.options.inline&&this.element.on({"focusout.colorpicker":c.default.proxy(this.hide,this)})),!1!==this.component&&this.component.on({"click.colorpicker":c.default.proxy(this.show,this)}),!1!==this.hasInput()||!1!==this.component||this.element.has(".colorpicker")||this.element.on({"click.colorpicker":c.default.proxy(this.show,this)}),this.hasInput()&&!1!==this.component&&"color"===this.input.attr("type")&&this.input.on({"click.colorpicker":c.default.proxy(this.show,this),"focus.colorpicker":c.default.proxy(this.show,this)}),this.update(!1!==this.options.color),(0,c.default)(c.default.proxy(function(){this.element.trigger({type:"colorpickerCreate",colorpicker:this,color:this.color})},this))}return r(n,[{key:"color",get:function(){return this.element.data("color")},set:function(t){this.element.data("color",t)}}],[{key:"Color",get:function(){return s.default}},{key:"Extension",get:function(){return i.default}},{key:"Extensions",get:function(){return l.default}}]),r(n,[{key:"addExtension",value:function(t,e){var o=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{},r=t instanceof i.default?t:new e(this,o);return this.extensions.push(r),r}},{key:"destroy",value:function(){this.picker.remove(),this.element.removeData("colorpicker","color").off(".colorpicker"),this.hasInput()&&this.input.off(".colorpicker"),!1!==this.component&&this.component.off(".colorpicker"),this.element.removeClass("colorpicker-element"),this.element.trigger({type:"colorpickerDestroy",colorpicker:this,color:this.color})}},{key:"hasColor",value:function(){return this.color instanceof s.default}},{key:"toInputColorString",value:function(){var t=this.toCssColorString();return t?(!1===this.options.useHashPrefix&&(t=t.replace(/^#/g,"")),this._resolveColor(t)):t}},{key:"toCssColorString",value:function(){return this.hasColor()?this.color.toString(this.format):""}},{key:"_reposition",value:function(t){if(this.lastEvent.name="reposition",this.lastEvent.e=t,!1!==this.options.inline||this.options.container)return!1;var e=this.container&&this.container[0]!==window.document.body?"position":"offset",o=this.component||this.element,r=o[e]();return"right"===this.options.align&&(r.left-=this.picker.outerWidth()-o.outerWidth()),this.picker.css({top:r.top+o.outerHeight(),left:r.left}),!0}},{key:"show",value:function(t){return this.lastEvent.name="show",this.lastEvent.e=t,!this.isVisible()&&!this.isDisabled()&&(this.picker.addClass("colorpicker-visible").removeClass("colorpicker-hidden"),this._reposition(t),(0,c.default)(window).on("resize.colorpicker",c.default.proxy(this._reposition,this)),!t||this.hasInput()&&"color"!==this.input.attr("type")||t.stopPropagation&&t.preventDefault&&(t.stopPropagation(),t.preventDefault()),!this.component&&this.input||!1!==this.options.inline||(0,c.default)(window.document).on({"mousedown.colorpicker":c.default.proxy(this.hide,this)}),this.element.trigger({type:"colorpickerShow",colorpicker:this,color:this.color}),!0)}},{key:"hide",value:function(t){return this.lastEvent.name="hide",this.lastEvent.e=t,!this.isHidden()&&((void 0===t||!t.target||!(0<(0,c.default)(t.currentTarget).parents(".colorpicker").length||0<(0,c.default)(t.target).parents(".colorpicker").length))&&(this.picker.addClass("colorpicker-hidden").removeClass("colorpicker-visible"),(0,c.default)(window).off("resize.colorpicker",this._reposition),(0,c.default)(window.document).off({"mousedown.colorpicker":this.hide}),this.element.trigger({type:"colorpickerHide",colorpicker:this,color:this.color}),!0))}},{key:"isVisible",value:function(){return this.picker.hasClass("colorpicker-visible")&&!this.picker.hasClass("colorpicker-hidden")}},{key:"isHidden",value:function(){return this.picker.hasClass("colorpicker-hidden")&&!this.picker.hasClass("colorpicker-visible")}},{key:"_updateInput",value:function(){if(this.hasInput()){var t=this.toInputColorString();if(t===this.input.prop("value"))return;this.input.prop("value",t||""),this.input.trigger({type:"change",colorpicker:this,color:this.color,value:t})}}},{key:"_updatePicker",value:function(){if(this.hasColor()){var t=!1===this.options.horizontal,e=t?this.options.sliders:this.options.slidersHorz,o=this.picker.find(".colorpicker-saturation .colorpicker-guide"),r=this.picker.find(".colorpicker-hue .colorpicker-guide"),i=this.picker.find(".colorpicker-alpha .colorpicker-guide"),n=this.color.hsvaRatio;r.length&&r.css(t?"top":"left",(t?e.hue.maxTop:e.hue.maxLeft)*(1-n.h)),i.length&&i.css(t?"top":"left",(t?e.alpha.maxTop:e.alpha.maxLeft)*(1-n.a)),o.length&&o.css({top:e.saturation.maxTop-n.v*e.saturation.maxTop,left:n.s*e.saturation.maxLeft}),this.picker.find(".colorpicker-saturation").css("backgroundColor",this.color.getHueOnlyCopy().toHexString()),this.picker.find(".colorpicker-alpha").css("backgroundColor",this.color.toString("hex6"))}}},{key:"_updateComponent",value:function(){if(this.hasColor()&&!1!==this.component){var t=this.component.find("i").eq(0);0<t.length?t.css({backgroundColor:this.toCssColorString()}):this.component.css({backgroundColor:this.toCssColorString()})}}},{key:"_shouldUpdate",value:function(){return this.hasColor()&&!1!==this.getValue(!1)}},{key:"update",value:function(){var t=0<arguments.length&&void 0!==arguments[0]&&arguments[0];(this._shouldUpdate()||!0===t)&&(this._updateComponent(),!0!==this.options.autoInputFallback&&"keyup"===this.lastEvent.name||this._updateInput(),this._updatePicker(),this.element.trigger({type:"colorpickerUpdate",colorpicker:this,color:this.color}))}},{key:"getValue",value:function(){var t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:null;t=void 0===t?this.fallbackColor:t;var e=[],o=!1;return this.hasInput()&&(e.push(this.input.val()),e.push(this.input.data("color"))),e.push(this.element.data("color")),e.map(function(t){t&&!1===o&&(o=t)}),(o=!1===o?t:o)instanceof s.default?o.toString(this.format):o}},{key:"setValue",value:function(t){if(!this.hasColor()||!this.color.equals(t)){var e=!!t&&this.createColor(t);if(this.hasColor()||e){var o=this.hasColor()&&!e;this.color=e,this.element.trigger({type:"colorpickerChange",colorpicker:this,color:this.color,value:t}),this.update(o)}}}},{key:"createColor",value:function(t){var e=!(1<arguments.length&&void 0!==arguments[1])||arguments[1],o=new s.default(this._resolveColor(t),{format:this.format});if(!o.isValid()){var r=o,i=void 0;if(e&&(i=this.fallbackColor instanceof s.default&&this.fallbackColor.isValid()?this.fallbackColor:this._resolveColor(this.fallbackColor),!(o=new s.default(i,{format:this.format})).isValid()&&e))throw new Error("The fallback color is invalid.");o.previous=r,this.element.trigger({type:"colorpickerInvalid",colorpicker:this,color:o,value:t})}if(!this.isAlphaEnabled()&&o.hasTransparency()&&o.setAlpha(1),!this.hasColor())return o;var n=o.hsvaRatio,a=this.color.hsvaRatio;return 0===n.s&&0===n.h&&0!==a.h&&o.setHueRatio(a.h),!this.isAlphaEnabled()&&o.hasTransparency()&&o.setAlpha(1),o}},{key:"isInvalidColor",value:function(){return!this.hasColor()||!this.color.isValid()||!!this.color.previous}},{key:"isAlphaEnabled",value:function(){return!0===this.options.useAlpha}},{key:"_resolveColor",value:function(o){var r=!1;return c.default.each(this.extensions,function(t,e){!1===r&&(r=e.resolveColor(o))}),!1!==r&&(o=r),o}},{key:"hasInput",value:function(){return!1!==this.input}},{key:"isDisabled",value:function(){return!0===this.disabled}},{key:"disable",value:function(){return this.hasInput()&&this.input.prop("disabled",!0),this.disabled=!0,this.element.trigger({type:"colorpickerDisable",colorpicker:this,color:this.color}),!0}},{key:"enable",value:function(){return this.hasInput()&&this.input.prop("disabled",!1),this.disabled=!1,this.element.trigger({type:"colorpickerEnable",colorpicker:this,color:this.color}),!0}},{key:"_mousedown",value:function(t){this.lastEvent.name="mousedown",!(this.lastEvent.e=t).pageX&&!t.pageY&&t.originalEvent&&t.originalEvent.touches&&(t.pageX=t.originalEvent.touches[0].pageX,t.pageY=t.originalEvent.touches[0].pageY),t.stopPropagation(),t.preventDefault();var e=(0,c.default)(t.target).closest("div"),o=this.options.horizontal?this.options.slidersHorz:this.options.sliders;if(!e.is(".colorpicker")){if(e.is(".colorpicker-saturation"))this.currentSlider=c.default.extend({},o.saturation);else if(e.is(".colorpicker-hue"))this.currentSlider=c.default.extend({},o.hue);else{if(!e.is(".colorpicker-alpha"))return!1;this.currentSlider=c.default.extend({},o.alpha)}var r=e.offset();this.currentSlider.guide=e.find(".colorpicker-guide")[0].style,this.currentSlider.left=t.pageX-r.left,this.currentSlider.top=t.pageY-r.top,this.mousePointer={left:t.pageX,top:t.pageY},(0,c.default)(window.document).on({"mousemove.colorpicker":c.default.proxy(this._mousemove,this),"touchmove.colorpicker":c.default.proxy(this._mousemove,this),"mouseup.colorpicker":c.default.proxy(this._mouseup,this),"touchend.colorpicker":c.default.proxy(this._mouseup,this)}).trigger("mousemove")}return!1}},{key:"_mousemove",value:function(t){this.lastEvent.name="mousemove",this.lastEvent.e=t;var e=this.hasColor()?this.color.getCopy():this.createColor(this.fallbackColor);!t.pageX&&!t.pageY&&t.originalEvent&&t.originalEvent.touches&&(t.pageX=t.originalEvent.touches[0].pageX,t.pageY=t.originalEvent.touches[0].pageY),t.stopPropagation(),t.preventDefault();var o=Math.max(0,Math.min(this.currentSlider.maxLeft,this.currentSlider.left+((t.pageX||this.mousePointer.left)-this.mousePointer.left))),r=Math.max(0,Math.min(this.currentSlider.maxTop,this.currentSlider.top+((t.pageY||this.mousePointer.top)-this.mousePointer.top)));return this.currentSlider.guide.left=o+"px",this.currentSlider.guide.top=r+"px",this.currentSlider.callLeft&&e[this.currentSlider.callLeft].call(e,o/this.currentSlider.maxLeft),this.currentSlider.callTop&&e[this.currentSlider.callTop].call(e,r/this.currentSlider.maxTop),this.setValue(e),!1}},{key:"_mouseup",value:function(t){return this.lastEvent.name="mouseup",(this.lastEvent.e=t).stopPropagation(),t.preventDefault(),(0,c.default)(window.document).off({"mousemove.colorpicker":this._mousemove,"touchmove.colorpicker":this._mousemove,"mouseup.colorpicker":this._mouseup,"touchend.colorpicker":this._mouseup}),!1}},{key:"_change",value:function(t){this.lastEvent.name="change",this.lastEvent.e=t;var e=this.input.val();e!==this.toInputColorString()&&this.setValue(e)}},{key:"_keyup",value:function(t){this.lastEvent.name="keyup",this.lastEvent.e=t;var e=this.input.val();e!==this.toInputColorString()&&this.setValue(e)}},{key:"fallbackColor",get:function(){return this.options.fallbackColor?this.options.fallbackColor:this.hasColor()?this.color:"#000"}},{key:"format",get:function(){return this.options.format?this.options.format:this.hasColor()&&this.color.hasTransparency()&&this.color.format.match(/^hex/)?this.options.enableHex8?"hex8":this.isAlphaEnabled()?"rgba":"hex":this.hasColor()?this.color.format:null}}]),n}();e.default=h},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r,n=function(){function r(t,e){for(var o=0;o<e.length;o++){var r=e[o];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(t,e,o){return e&&r(t.prototype,e),o&&r(t,o),t}}(),i=o(6),a=(r=i)&&r.__esModule?r:{default:r};function s(t){return t instanceof String||"string"==typeof t?t.replace(/a$/gi,""):t}var l=function(t){function i(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{format:null};!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,i),e.format&&(e.format=s(e.format));var o,r=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(i.__proto__||Object.getPrototypeOf(i)).call(this,(o=t)instanceof a.default?{r:o._r,g:o._g,b:o._b,a:o._a}:o,e));return r._originalInput=t,r._hbak=r.hsva.h,r.previous=null,r}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(i,a.default),n(i,[{key:"id",get:function(){return this._tc_id}},{key:"format",get:function(){return this._format}},{key:"options",get:function(){return{format:this._format,gradientType:this._gradientType}}},{key:"hsva",get:function(){return this.toHsv()}},{key:"hsvaRatio",get:function(){var t=this.hsva;return{h:t.h/360,s:t.s,v:t.v,a:t.a}}}]),n(i,[{key:"equals",value:function(t){return t instanceof a.default&&(this._r===t._r&&this._g===t._g&&this._b===t._b&&this._a===t._a&&this._roundA===t._roundA&&this._format===t._format&&this._gradientType===t._gradientType&&this._ok===t._ok)}},{key:"importColor",value:function(t){if(!(t instanceof a.default))throw new Error("Color.importColor: The color argument is not an instance of tinycolor.");this._originalInput=t._originalInput,this._r=t._r,this._g=t._g,this._b=t._b,this._a=t._a,this._roundA=t._roundA,this._format=s(t._format),this._gradientType=t._gradientType,this._ok=t._ok}},{key:"importRgb",value:function(t){if(!t instanceof i)throw new Error("Color.importColor: The color argument is not an instance of tinycolor.");this._r=t._r,this._g=t._g,this._b=t._b,this._a=t._a,this._ok=t._ok,this._hbak=t._hbak}},{key:"importHsv",value:function(t){this._hbak=t.h,this.importRgb(new i(t,this.options))}},{key:"getCopy",value:function(){return new i(this.hsva,this.options)}},{key:"getHueOnlyCopy",value:function(){return new i({h:this._hbak?this._hbak:this.hsva.h,s:100,v:100},this.options)}},{key:"getOpaqueCopy",value:function(){return new i(Object.assign({},this.hsva,{a:1}),this.options)}},{key:"setHue",value:function(t){this.importHsv(Object.assign({},this.hsva,{h:t}))}},{key:"setSaturation",value:function(t){this.importHsv(Object.assign({},this.hsva,{s:t}))}},{key:"setBrightness",value:function(t){this.importHsv(Object.assign({},this.hsva,{v:t}))}},{key:"setHueRatio",value:function(t){0!==t&&this.setHue(360*(1-t))}},{key:"setSaturationRatio",value:function(t){this.setSaturation(t)}},{key:"setBrightnessRatio",value:function(t){this.setBrightness(1-t)}},{key:"setAlphaRatio",value:function(t){this.setAlpha(1-t)}},{key:"isTransparent",value:function(){return 0===this._a}},{key:"hasTransparency",value:function(){return 1!==this._a}},{key:"toString",value:function(){var t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:null;t=t?s(t):this.format;var e=function t(e,o,r){null===e&&(e=Function.prototype);var i=Object.getOwnPropertyDescriptor(e,o);if(void 0===i){var n=Object.getPrototypeOf(e);return null===n?void 0:t(n,o,r)}if("value"in i)return i.value;var a=i.get;return void 0!==a?a.call(r):void 0}(i.prototype.__proto__||Object.getPrototypeOf(i.prototype),"toString",this).call(this,t);return e&&e.match(/^#[0-9a-f]{3,8}$/i)&&this.isTransparent()&&0===this._r&&0===this._g&&0===this._b?"transparent":e}}]),i}();e.default=l},function(q,U,N){var X;!function(h){var f=/^\s+/,p=/\s+$/,r=0,a=h.round,d=h.min,g=h.max,t=h.random;function u(t,e){if(e=e||{},(t=t||"")instanceof u)return t;if(!(this instanceof u))return new u(t,e);var o=function(t){var e={r:0,g:0,b:0},o=1,r=null,i=null,n=null,a=!1,s=!1;"string"==typeof t&&(t=function(t){t=t.replace(f,"").replace(p,"").toLowerCase();var e,o=!1;if(A[t])t=A[t],o=!0;else if("transparent"==t)return{r:0,g:0,b:0,a:0,format:"name"};if(e=V.rgb.exec(t))return{r:e[1],g:e[2],b:e[3]};if(e=V.rgba.exec(t))return{r:e[1],g:e[2],b:e[3],a:e[4]};if(e=V.hsl.exec(t))return{h:e[1],s:e[2],l:e[3]};if(e=V.hsla.exec(t))return{h:e[1],s:e[2],l:e[3],a:e[4]};if(e=V.hsv.exec(t))return{h:e[1],s:e[2],v:e[3]};if(e=V.hsva.exec(t))return{h:e[1],s:e[2],v:e[3],a:e[4]};if(e=V.hex8.exec(t))return{r:T(e[1]),g:T(e[2]),b:T(e[3]),a:D(e[4]),format:o?"name":"hex8"};if(e=V.hex6.exec(t))return{r:T(e[1]),g:T(e[2]),b:T(e[3]),format:o?"name":"hex"};if(e=V.hex4.exec(t))return{r:T(e[1]+""+e[1]),g:T(e[2]+""+e[2]),b:T(e[3]+""+e[3]),a:D(e[4]+""+e[4]),format:o?"name":"hex8"};if(e=V.hex3.exec(t))return{r:T(e[1]+""+e[1]),g:T(e[2]+""+e[2]),b:T(e[3]+""+e[3]),format:o?"name":"hex"};return!1}(t));"object"==typeof t&&(z(t.r)&&z(t.g)&&z(t.b)?(l=t.r,c=t.g,u=t.b,e={r:255*P(l,255),g:255*P(c,255),b:255*P(u,255)},a=!0,s="%"===String(t.r).substr(-1)?"prgb":"rgb"):z(t.h)&&z(t.s)&&z(t.v)?(r=R(t.s),i=R(t.v),e=function(t,e,o){t=6*P(t,360),e=P(e,100),o=P(o,100);var r=h.floor(t),i=t-r,n=o*(1-e),a=o*(1-i*e),s=o*(1-(1-i)*e),l=r%6;return{r:255*[o,a,n,n,s,o][l],g:255*[s,o,o,a,n,n][l],b:255*[n,n,s,o,o,a][l]}}(t.h,r,i),a=!0,s="hsv"):z(t.h)&&z(t.s)&&z(t.l)&&(r=R(t.s),n=R(t.l),e=function(t,e,o){var r,i,n;function a(t,e,o){return o<0&&(o+=1),1<o&&(o-=1),o<1/6?t+6*(e-t)*o:o<.5?e:o<2/3?t+(e-t)*(2/3-o)*6:t}if(t=P(t,360),e=P(e,100),o=P(o,100),0===e)r=i=n=o;else{var s=o<.5?o*(1+e):o+e-o*e,l=2*o-s;r=a(l,s,t+1/3),i=a(l,s,t),n=a(l,s,t-1/3)}return{r:255*r,g:255*i,b:255*n}}(t.h,r,n),a=!0,s="hsl"),t.hasOwnProperty("a")&&(o=t.a));var l,c,u;return o=j(o),{ok:a,format:t.format||s,r:d(255,g(e.r,0)),g:d(255,g(e.g,0)),b:d(255,g(e.b,0)),a:o}}(t);this._originalInput=t,this._r=o.r,this._g=o.g,this._b=o.b,this._a=o.a,this._roundA=a(100*this._a)/100,this._format=e.format||o.format,this._gradientType=e.gradientType,this._r<1&&(this._r=a(this._r)),this._g<1&&(this._g=a(this._g)),this._b<1&&(this._b=a(this._b)),this._ok=o.ok,this._tc_id=r++}function i(t,e,o){t=P(t,255),e=P(e,255),o=P(o,255);var r,i,n=g(t,e,o),a=d(t,e,o),s=(n+a)/2;if(n==a)r=i=0;else{var l=n-a;switch(i=.5<s?l/(2-n-a):l/(n+a),n){case t:r=(e-o)/l+(e<o?6:0);break;case e:r=(o-t)/l+2;break;case o:r=(t-e)/l+4}r/=6}return{h:r,s:i,l:s}}function n(t,e,o){t=P(t,255),e=P(e,255),o=P(o,255);var r,i,n=g(t,e,o),a=d(t,e,o),s=n,l=n-a;if(i=0===n?0:l/n,n==a)r=0;else{switch(n){case t:r=(e-o)/l+(e<o?6:0);break;case e:r=(o-t)/l+2;break;case o:r=(t-e)/l+4}r/=6}return{h:r,s:i,v:s}}function e(t,e,o,r){var i=[H(a(t).toString(16)),H(a(e).toString(16)),H(a(o).toString(16))];return r&&i[0].charAt(0)==i[0].charAt(1)&&i[1].charAt(0)==i[1].charAt(1)&&i[2].charAt(0)==i[2].charAt(1)?i[0].charAt(0)+i[1].charAt(0)+i[2].charAt(0):i.join("")}function s(t,e,o,r){return[H(I(r)),H(a(t).toString(16)),H(a(e).toString(16)),H(a(o).toString(16))].join("")}function o(t,e){e=0===e?0:e||10;var o=u(t).toHsl();return o.s-=e/100,o.s=E(o.s),u(o)}function l(t,e){e=0===e?0:e||10;var o=u(t).toHsl();return o.s+=e/100,o.s=E(o.s),u(o)}function c(t){return u(t).desaturate(100)}function v(t,e){e=0===e?0:e||10;var o=u(t).toHsl();return o.l+=e/100,o.l=E(o.l),u(o)}function b(t,e){e=0===e?0:e||10;var o=u(t).toRgb();return o.r=g(0,d(255,o.r-a(-e/100*255))),o.g=g(0,d(255,o.g-a(-e/100*255))),o.b=g(0,d(255,o.b-a(-e/100*255))),u(o)}function y(t,e){e=0===e?0:e||10;var o=u(t).toHsl();return o.l-=e/100,o.l=E(o.l),u(o)}function m(t,e){var o=u(t).toHsl(),r=(o.h+e)%360;return o.h=r<0?360+r:r,u(o)}function k(t){var e=u(t).toHsl();return e.h=(e.h+180)%360,u(e)}function _(t){var e=u(t).toHsl(),o=e.h;return[u(t),u({h:(o+120)%360,s:e.s,l:e.l}),u({h:(o+240)%360,s:e.s,l:e.l})]}function x(t){var e=u(t).toHsl(),o=e.h;return[u(t),u({h:(o+90)%360,s:e.s,l:e.l}),u({h:(o+180)%360,s:e.s,l:e.l}),u({h:(o+270)%360,s:e.s,l:e.l})]}function w(t){var e=u(t).toHsl(),o=e.h;return[u(t),u({h:(o+72)%360,s:e.s,l:e.l}),u({h:(o+216)%360,s:e.s,l:e.l})]}function C(t,e,o){e=e||6,o=o||30;var r=u(t).toHsl(),i=360/o,n=[u(t)];for(r.h=(r.h-(i*e>>1)+720)%360;--e;)r.h=(r.h+i)%360,n.push(u(r));return n}function O(t,e){e=e||6;for(var o=u(t).toHsv(),r=o.h,i=o.s,n=o.v,a=[],s=1/e;e--;)a.push(u({h:r,s:i,v:n})),n=(n+s)%1;return a}u.prototype={isDark:function(){return this.getBrightness()<128},isLight:function(){return!this.isDark()},isValid:function(){return this._ok},getOriginalInput:function(){return this._originalInput},getFormat:function(){return this._format},getAlpha:function(){return this._a},getBrightness:function(){var t=this.toRgb();return(299*t.r+587*t.g+114*t.b)/1e3},getLuminance:function(){var t,e,o,r=this.toRgb();return t=r.r/255,e=r.g/255,o=r.b/255,.2126*(t<=.03928?t/12.92:h.pow((t+.055)/1.055,2.4))+.7152*(e<=.03928?e/12.92:h.pow((e+.055)/1.055,2.4))+.0722*(o<=.03928?o/12.92:h.pow((o+.055)/1.055,2.4))},setAlpha:function(t){return this._a=j(t),this._roundA=a(100*this._a)/100,this},toHsv:function(){var t=n(this._r,this._g,this._b);return{h:360*t.h,s:t.s,v:t.v,a:this._a}},toHsvString:function(){var t=n(this._r,this._g,this._b),e=a(360*t.h),o=a(100*t.s),r=a(100*t.v);return 1==this._a?"hsv("+e+", "+o+"%, "+r+"%)":"hsva("+e+", "+o+"%, "+r+"%, "+this._roundA+")"},toHsl:function(){var t=i(this._r,this._g,this._b);return{h:360*t.h,s:t.s,l:t.l,a:this._a}},toHslString:function(){var t=i(this._r,this._g,this._b),e=a(360*t.h),o=a(100*t.s),r=a(100*t.l);return 1==this._a?"hsl("+e+", "+o+"%, "+r+"%)":"hsla("+e+", "+o+"%, "+r+"%, "+this._roundA+")"},toHex:function(t){return e(this._r,this._g,this._b,t)},toHexString:function(t){return"#"+this.toHex(t)},toHex8:function(t){return function(t,e,o,r,i){var n=[H(a(t).toString(16)),H(a(e).toString(16)),H(a(o).toString(16)),H(I(r))];if(i&&n[0].charAt(0)==n[0].charAt(1)&&n[1].charAt(0)==n[1].charAt(1)&&n[2].charAt(0)==n[2].charAt(1)&&n[3].charAt(0)==n[3].charAt(1))return n[0].charAt(0)+n[1].charAt(0)+n[2].charAt(0)+n[3].charAt(0);return n.join("")}(this._r,this._g,this._b,this._a,t)},toHex8String:function(t){return"#"+this.toHex8(t)},toRgb:function(){return{r:a(this._r),g:a(this._g),b:a(this._b),a:this._a}},toRgbString:function(){return 1==this._a?"rgb("+a(this._r)+", "+a(this._g)+", "+a(this._b)+")":"rgba("+a(this._r)+", "+a(this._g)+", "+a(this._b)+", "+this._roundA+")"},toPercentageRgb:function(){return{r:a(100*P(this._r,255))+"%",g:a(100*P(this._g,255))+"%",b:a(100*P(this._b,255))+"%",a:this._a}},toPercentageRgbString:function(){return 1==this._a?"rgb("+a(100*P(this._r,255))+"%, "+a(100*P(this._g,255))+"%, "+a(100*P(this._b,255))+"%)":"rgba("+a(100*P(this._r,255))+"%, "+a(100*P(this._g,255))+"%, "+a(100*P(this._b,255))+"%, "+this._roundA+")"},toName:function(){return 0===this._a?"transparent":!(this._a<1)&&(S[e(this._r,this._g,this._b,!0)]||!1)},toFilter:function(t){var e="#"+s(this._r,this._g,this._b,this._a),o=e,r=this._gradientType?"GradientType = 1, ":"";if(t){var i=u(t);o="#"+s(i._r,i._g,i._b,i._a)}return"progid:DXImageTransform.Microsoft.gradient("+r+"startColorstr="+e+",endColorstr="+o+")"},toString:function(t){var e=!!t;t=t||this._format;var o=!1,r=this._a<1&&0<=this._a;return e||!r||"hex"!==t&&"hex6"!==t&&"hex3"!==t&&"hex4"!==t&&"hex8"!==t&&"name"!==t?("rgb"===t&&(o=this.toRgbString()),"prgb"===t&&(o=this.toPercentageRgbString()),"hex"!==t&&"hex6"!==t||(o=this.toHexString()),"hex3"===t&&(o=this.toHexString(!0)),"hex4"===t&&(o=this.toHex8String(!0)),"hex8"===t&&(o=this.toHex8String()),"name"===t&&(o=this.toName()),"hsl"===t&&(o=this.toHslString()),"hsv"===t&&(o=this.toHsvString()),o||this.toHexString()):"name"===t&&0===this._a?this.toName():this.toRgbString()},clone:function(){return u(this.toString())},_applyModification:function(t,e){var o=t.apply(null,[this].concat([].slice.call(e)));return this._r=o._r,this._g=o._g,this._b=o._b,this.setAlpha(o._a),this},lighten:function(){return this._applyModification(v,arguments)},brighten:function(){return this._applyModification(b,arguments)},darken:function(){return this._applyModification(y,arguments)},desaturate:function(){return this._applyModification(o,arguments)},saturate:function(){return this._applyModification(l,arguments)},greyscale:function(){return this._applyModification(c,arguments)},spin:function(){return this._applyModification(m,arguments)},_applyCombination:function(t,e){return t.apply(null,[this].concat([].slice.call(e)))},analogous:function(){return this._applyCombination(C,arguments)},complement:function(){return this._applyCombination(k,arguments)},monochromatic:function(){return this._applyCombination(O,arguments)},splitcomplement:function(){return this._applyCombination(w,arguments)},triad:function(){return this._applyCombination(_,arguments)},tetrad:function(){return this._applyCombination(x,arguments)}},u.fromRatio=function(t,e){if("object"==typeof t){var o={};for(var r in t)t.hasOwnProperty(r)&&(o[r]="a"===r?t[r]:R(t[r]));t=o}return u(t,e)},u.equals=function(t,e){return!(!t||!e)&&u(t).toRgbString()==u(e).toRgbString()},u.random=function(){return u.fromRatio({r:t(),g:t(),b:t()})},u.mix=function(t,e,o){o=0===o?0:o||50;var r=u(t).toRgb(),i=u(e).toRgb(),n=o/100;return u({r:(i.r-r.r)*n+r.r,g:(i.g-r.g)*n+r.g,b:(i.b-r.b)*n+r.b,a:(i.a-r.a)*n+r.a})},u.readability=function(t,e){var o=u(t),r=u(e);return(h.max(o.getLuminance(),r.getLuminance())+.05)/(h.min(o.getLuminance(),r.getLuminance())+.05)},u.isReadable=function(t,e,o){var r,i,n=u.readability(t,e);switch(i=!1,(r=function(t){var e,o;e=((t=t||{level:"AA",size:"small"}).level||"AA").toUpperCase(),o=(t.size||"small").toLowerCase(),"AA"!==e&&"AAA"!==e&&(e="AA");"small"!==o&&"large"!==o&&(o="small");return{level:e,size:o}}(o)).level+r.size){case"AAsmall":case"AAAlarge":i=4.5<=n;break;case"AAlarge":i=3<=n;break;case"AAAsmall":i=7<=n}return i},u.mostReadable=function(t,e,o){var r,i,n,a,s=null,l=0;i=(o=o||{}).includeFallbackColors,n=o.level,a=o.size;for(var c=0;c<e.length;c++)l<(r=u.readability(t,e[c]))&&(l=r,s=u(e[c]));return u.isReadable(t,s,{level:n,size:a})||!i?s:(o.includeFallbackColors=!1,u.mostReadable(t,["#fff","#000"],o))};var A=u.names={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"0ff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000",blanchedalmond:"ffebcd",blue:"00f",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",burntsienna:"ea7e5d",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"0ff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkgrey:"a9a9a9",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkslategrey:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dimgrey:"696969",dodgerblue:"1e90ff",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"f0f",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",grey:"808080",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgray:"d3d3d3",lightgreen:"90ee90",lightgrey:"d3d3d3",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslategray:"789",lightslategrey:"789",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"0f0",limegreen:"32cd32",linen:"faf0e6",magenta:"f0f",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370db",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"db7093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",rebeccapurple:"663399",red:"f00",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",slategrey:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",wheat:"f5deb3",white:"fff",whitesmoke:"f5f5f5",yellow:"ff0",yellowgreen:"9acd32"},S=u.hexNames=function(t){var e={};for(var o in t)t.hasOwnProperty(o)&&(e[t[o]]=o);return e}(A);function j(t){return t=parseFloat(t),(isNaN(t)||t<0||1<t)&&(t=1),t}function P(t,e){var o;"string"==typeof(o=t)&&-1!=o.indexOf(".")&&1===parseFloat(o)&&(t="100%");var r,i="string"==typeof(r=t)&&-1!=r.indexOf("%");return t=d(e,g(0,parseFloat(t))),i&&(t=parseInt(t*e,10)/100),h.abs(t-e)<1e-6?1:t%e/parseFloat(e)}function E(t){return d(1,g(0,t))}function T(t){return parseInt(t,16)}function H(t){return 1==t.length?"0"+t:""+t}function R(t){return t<=1&&(t=100*t+"%"),t}function I(t){return h.round(255*parseFloat(t)).toString(16)}function D(t){return T(t)/255}var L,M,F,V=(M="[\\s|\\(]+("+(L="(?:[-\\+]?\\d*\\.\\d+%?)|(?:[-\\+]?\\d+%?)")+")[,|\\s]+("+L+")[,|\\s]+("+L+")\\s*\\)?",F="[\\s|\\(]+("+L+")[,|\\s]+("+L+")[,|\\s]+("+L+")[,|\\s]+("+L+")\\s*\\)?",{CSS_UNIT:new RegExp(L),rgb:new RegExp("rgb"+M),rgba:new RegExp("rgba"+F),hsl:new RegExp("hsl"+M),hsla:new RegExp("hsla"+F),hsv:new RegExp("hsv"+M),hsva:new RegExp("hsva"+F),hex3:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex6:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,hex4:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex8:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/});function z(t){return!!V.CSS_UNIT.exec(t)}void 0!==q&&q.exports?q.exports=u:void 0===(X=function(){return u}.call(U,N,U,q))||(q.exports=X)}(Math)},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default={debug:!1,color:!1,format:!1,horizontal:!1,inline:!1,input:"input",container:!1,component:".add-on, .input-group-addon",fallbackColor:!1,autoInputFallback:!1,useHashPrefix:!0,useAlpha:!0,enableHex8:!1,sliders:{saturation:{maxLeft:100,maxTop:100,callLeft:"setSaturationRatio",callTop:"setBrightnessRatio"},hue:{maxLeft:0,maxTop:100,callLeft:!1,callTop:"setHueRatio"},alpha:{maxLeft:0,maxTop:100,callLeft:!1,callTop:"setAlphaRatio"}},slidersHorz:{saturation:{maxLeft:100,maxTop:100,callLeft:"setSaturationRatio",callTop:"setBrightnessRatio"},hue:{maxLeft:100,maxTop:0,callLeft:"setHueRatio",callTop:!1},alpha:{maxLeft:100,maxTop:0,callLeft:"setAlphaRatio",callTop:!1}},align:"right",customClass:null,template:'<div class="colorpicker">\n    <div class="colorpicker-saturation"><i class="colorpicker-guide"><i /></div>\n    <div class="colorpicker-hue"><i class="colorpicker-guide"></i></div>\n    <div class="colorpicker-alpha"><i class="colorpicker-guide"></i></div></div>',extensions:[{name:"preview",showText:!1}]}},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.Palette=e.Swatches=e.Preview=e.Debugger=void 0;var r=s(o(9)),i=s(o(10)),n=s(o(11)),a=s(o(2));function s(t){return t&&t.__esModule?t:{default:t}}e.Debugger=r.default,e.Preview=i.default,e.Swatches=n.default,e.Palette=a.default,e.default={debugger:r.default,preview:i.default,swatches:n.default,palette:a.default}},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i=function(){function r(t,e){for(var o=0;o<e.length;o++){var r=e[o];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(t,e,o){return e&&r(t.prototype,e),o&&r(t,o),t}}(),n=function t(e,o,r){null===e&&(e=Function.prototype);var i=Object.getOwnPropertyDescriptor(e,o);if(void 0===i){var n=Object.getPrototypeOf(e);return null===n?void 0:t(n,o,r)}if("value"in i)return i.value;var a=i.get;return void 0!==a?a.call(r):void 0},a=r(o(1)),s=r(o(0));function r(t){return t&&t.__esModule?t:{default:t}}var l=function(t){function r(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,r);var o=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(r.__proto__||Object.getPrototypeOf(r)).call(this,t,e));return o.eventCounter=0,o.colorpicker.hasInput()&&o.colorpicker.input.on("change.colorpicker-ext",s.default.proxy(o.onChangeInput,o)),o}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(r,a.default),i(r,[{key:"log",value:function(t){for(var e,o=arguments.length,r=Array(1<o?o-1:0),i=1;i<o;i++)r[i-1]=arguments[i];this.eventCounter+=1;var n="#"+this.eventCounter+": Colorpicker#"+this.colorpicker.id+" ["+t+"]";(e=console).debug.apply(e,[n].concat(r)),this.colorpicker.element.trigger({type:"colorpickerDebug",colorpicker:this.colorpicker,color:this.color,debug:{debugger:this,eventName:t,logArgs:r,logMessage:n}})}},{key:"resolveColor",value:function(t){return this.log("resolveColor()",t),!1}},{key:"onCreate",value:function(t){return this.log("colorpickerCreate"),n(r.prototype.__proto__||Object.getPrototypeOf(r.prototype),"onCreate",this).call(this,t)}},{key:"onDestroy",value:function(t){return this.log("colorpickerDestroy"),this.eventCounter=0,this.colorpicker.hasInput()&&this.colorpicker.input.off(".colorpicker-ext"),n(r.prototype.__proto__||Object.getPrototypeOf(r.prototype),"onDestroy",this).call(this,t)}},{key:"onUpdate",value:function(t){this.log("colorpickerUpdate")}},{key:"onChangeInput",value:function(t){this.log("input:change.colorpicker",t.value,t.color)}},{key:"onChange",value:function(t){this.log("colorpickerChange",t.value,t.color)}},{key:"onInvalid",value:function(t){this.log("colorpickerInvalid",t.value,t.color)}},{key:"onHide",value:function(t){this.log("colorpickerHide"),this.eventCounter=0}},{key:"onShow",value:function(t){this.log("colorpickerShow")}},{key:"onDisable",value:function(t){this.log("colorpickerDisable")}},{key:"onEnable",value:function(t){this.log("colorpickerEnable")}}]),r}();e.default=l},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i=function(){function r(t,e){for(var o=0;o<e.length;o++){var r=e[o];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(t,e,o){return e&&r(t.prototype,e),o&&r(t,o),t}}(),n=function t(e,o,r){null===e&&(e=Function.prototype);var i=Object.getOwnPropertyDescriptor(e,o);if(void 0===i){var n=Object.getPrototypeOf(e);return null===n?void 0:t(n,o,r)}if("value"in i)return i.value;var a=i.get;return void 0!==a?a.call(r):void 0},a=r(o(1)),s=r(o(0));function r(t){return t&&t.__esModule?t:{default:t}}var l=function(t){function r(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,r);var o=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(r.__proto__||Object.getPrototypeOf(r)).call(this,t,Object.assign({},{template:'<div class="colorpicker-bar colorpicker-preview"><div /></div>',showText:!0,format:t.format},e)));return o.element=(0,s.default)(o.options.template),o.elementInner=o.element.find("div"),o}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(r,a.default),i(r,[{key:"onCreate",value:function(t){n(r.prototype.__proto__||Object.getPrototypeOf(r.prototype),"onCreate",this).call(this,t),this.colorpicker.picker.append(this.element)}},{key:"onUpdate",value:function(t){n(r.prototype.__proto__||Object.getPrototypeOf(r.prototype),"onUpdate",this).call(this,t),this.elementInner.css("backgroundColor",t.color.toRgbString()),this.options.showText&&(this.elementInner.html(t.color.toString(this.options.format||this.colorpicker.format)),t.color.isDark()?this.elementInner.css("color","white"):this.elementInner.css("color","black"))}}]),r}();e.default=l},function(t,e,o){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r=function(){function r(t,e){for(var o=0;o<e.length;o++){var r=e[o];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(t,e,o){return e&&r(t.prototype,e),o&&r(t,o),t}}(),i=n(o(2)),s=n(o(0));function n(t){return t&&t.__esModule?t:{default:t}}var a={barTemplate:'<div class="colorpicker-bar colorpicker-swatches"></div>',swatchTemplate:'<i class="colorpicker-swatch"></i>'},l=function(t){function o(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,o),function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!=typeof e&&"function"!=typeof e?t:e}(this,(o.__proto__||Object.getPrototypeOf(o)).call(this,t,Object.assign({},a,e)))}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(o,i.default),r(o,[{key:"isEnabled",value:function(){return 0<this.getLength()}},{key:"onCreate",value:function(t){var r=this;if(function t(e,o,r){null===e&&(e=Function.prototype);var i=Object.getOwnPropertyDescriptor(e,o);if(void 0===i){var n=Object.getPrototypeOf(e);return null===n?void 0:t(n,o,r)}if("value"in i)return i.value;var a=i.get;return void 0!==a?a.call(r):void 0}(o.prototype.__proto__||Object.getPrototypeOf(o.prototype),"onCreate",this).call(this,t),this.isEnabled()){var i=this.colorpicker,n=(0,s.default)(this.options.barTemplate),a=!0===this.options.namesAsValues&&!Array.isArray(this.colors);s.default.each(this.colors,function(t,e){var o=(0,s.default)(r.options.swatchTemplate).css("background-color",e).attr("data-name",t).attr("data-value",e).attr("title",t+": "+e);o.on("mousedown.colorpicker touchstart.colorpicker",function(t){t.preventDefault(),i.setValue(a?(0,s.default)(this).data("name"):(0,s.default)(this).data("value"))}),n.append(o)}),i.picker.append(n)}}}]),o}();e.default=l}])});