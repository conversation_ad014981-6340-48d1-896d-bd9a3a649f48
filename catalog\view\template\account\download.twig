{{ header }}
<div id="account-download" class="container-fluid">
  <div class="row">{{ column_left }}
    <div id="content" class="col" style="padding-left:10px;padding-right:10px;padding-top:10px">{{ content_top }}
      {% if downloads %}
        <div class="table-responsive">
          <table class="table table-bordered table-hover">
            <thead>
              <tr>
                <td class="text-end"><strong>{{ column_order_id }}</strong></td>
                <td class="text-start"><strong>{{ column_name }}</strong></td>
                <td class="text-start"><strong>{{ column_size }}</strong></td>
                <td class="text-start"><strong>{{ column_date_added }}</strong></td>
                <td></td>
              </tr>
            </thead>
            <tbody>
              {% for download in downloads %}
                <tr>
                  <td class="text-end">{{ download.order_id }}</td>
                  <td class="text-start">{{ download.name }}</td>
                  <td class="text-start">{{ download.size }}</td>
                  <td class="text-start">{{ download.date_added }}</td>
                  <td><a href="{{ download.href }}" data-bs-toggle="tooltip" title="{{ button_download }}" class="btn btn-primary"><i class="fa-solid fa-cloud-arrow-down"></i></a></td>
                </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
        <div class="row mb-3">
          <div class="col-sm-6 text-start">{{ pagination }}</div>
          <div class="col-sm-6 text-end">{{ results }}</div>
        </div>
      {% else %}
        <p>{{ text_no_results }}</p>
      {% endif %}
      <div class="text-end">
        <a href="{{ continue }}" class="btn btn-primary">{{ button_continue }}</a>
      </div>
      {{ content_bottom }}</div>
    {{ column_right }}</div>
</div>
{{ footer }}
