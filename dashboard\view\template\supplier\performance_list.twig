{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <a href="{{ dashboard }}" data-toggle="tooltip" title="{{ button_dashboard }}" class="btn btn-info"><i class="fa fa-dashboard"></i></a>
        <button type="button" data-toggle="tooltip" title="{{ button_export_report }}" class="btn btn-success" onclick="exportReport();"><i class="fa fa-download"></i></button>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    {% if success %}
    <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    
    <!-- Performance Overview Cards -->
    <div class="row">
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-primary">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-users fa-5x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ suppliers|length }}</div>
                <div>{{ metric_total_suppliers }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-success">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-star fa-5x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ excellent_count|default(0) }}</div>
                <div>{{ metric_excellent_suppliers }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-warning">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-exclamation-triangle fa-5x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ poor_count|default(0) }}</div>
                <div>{{ metric_poor_suppliers }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-md-6">
        <div class="panel panel-info">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-line-chart fa-5x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ avg_score|number_format(1) }}%</div>
                <div>{{ metric_avg_score }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-list"></i> {{ text_list }}</h3>
      </div>
      <div class="panel-body">
        <div class="well">
          <div class="row">
            <div class="col-sm-4">
              <div class="form-group">
                <label class="control-label" for="input-supplier">{{ filter_supplier }}</label>
                <input type="text" name="filter_supplier" value="{{ filter_supplier }}" placeholder="{{ filter_supplier }}" id="input-supplier" class="form-control" />
              </div>
            </div>
            <div class="col-sm-4">
              <div class="form-group">
                <label class="control-label" for="input-score-min">{{ entry_score_min }}</label>
                <input type="number" name="filter_score_min" value="{{ filter_score_min }}" placeholder="{{ entry_score_min }}" id="input-score-min" class="form-control" min="0" max="100" />
              </div>
            </div>
            <div class="col-sm-4">
              <div class="form-group">
                <label class="control-label" for="input-performance-level">{{ filter_performance_level }}</label>
                <select name="filter_performance_level" id="input-performance-level" class="form-control">
                  <option value="">{{ text_all_suppliers }}</option>
                  <option value="excellent" {% if filter_performance_level == 'excellent' %}selected="selected"{% endif %}>{{ performance_excellent }}</option>
                  <option value="good" {% if filter_performance_level == 'good' %}selected="selected"{% endif %}>{{ performance_good }}</option>
                  <option value="poor" {% if filter_performance_level == 'poor' %}selected="selected"{% endif %}>{{ performance_poor }}</option>
                </select>
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-sm-12 text-right">
              <button type="button" id="button-filter" class="btn btn-primary"><i class="fa fa-search"></i> {{ button_filter }}</button>
              <button type="button" id="button-reset" class="btn btn-default"><i class="fa fa-refresh"></i> {{ button_reset }}</button>
            </div>
          </div>
        </div>
        
        <div class="table-responsive">
          <table class="table table-bordered table-hover">
            <thead>
              <tr>
                <td class="text-left">{% if sort == 's.name' %}<a href="{{ sort_name }}" class="{{ order|lower }}">{{ column_supplier }}</a>{% else %}<a href="{{ sort_name }}">{{ column_supplier }}</a>{% endif %}</td>
                <td class="text-center">{% if sort == 'overall_score' %}<a href="{{ sort_overall_score }}" class="{{ order|lower }}">{{ column_overall_score }}</a>{% else %}<a href="{{ sort_overall_score }}">{{ column_overall_score }}</a>{% endif %}</td>
                <td class="text-center">{% if sort == 'delivery_score' %}<a href="{{ sort_delivery_score }}" class="{{ order|lower }}">{{ column_delivery_score }}</a>{% else %}<a href="{{ sort_delivery_score }}">{{ column_delivery_score }}</a>{% endif %}</td>
                <td class="text-center">{% if sort == 'quality_score' %}<a href="{{ sort_quality_score }}" class="{{ order|lower }}">{{ column_quality_score }}</a>{% else %}<a href="{{ sort_quality_score }}">{{ column_quality_score }}</a>{% endif %}</td>
                <td class="text-center">{% if sort == 'cost_score' %}<a href="{{ sort_cost_score }}" class="{{ order|lower }}">{{ column_cost_score }}</a>{% else %}<a href="{{ sort_cost_score }}">{{ column_cost_score }}</a>{% endif %}</td>
                <td class="text-center">{{ column_total_orders }}</td>
                <td class="text-center">{{ column_last_evaluation }}</td>
                <td class="text-right">{{ column_action }}</td>
              </tr>
            </thead>
            <tbody>
              {% if suppliers %}
              {% for supplier in suppliers %}
              <tr>
                <td class="text-left">
                  <strong>{{ supplier.name }}</strong>
                  {% if supplier.overall_score >= 80 %}
                  <span class="label label-success">{{ text_excellent }}</span>
                  {% elseif supplier.overall_score >= 60 %}
                  <span class="label label-warning">{{ text_good }}</span>
                  {% else %}
                  <span class="label label-danger">{{ text_poor }}</span>
                  {% endif %}
                </td>
                <td class="text-center">
                  <div class="progress" style="margin-bottom: 0;">
                    {% if supplier.overall_score >= 80 %}
                    <div class="progress-bar progress-bar-success" style="width: {{ supplier.overall_score }}%">{{ supplier.overall_score }}%</div>
                    {% elseif supplier.overall_score >= 60 %}
                    <div class="progress-bar progress-bar-warning" style="width: {{ supplier.overall_score }}%">{{ supplier.overall_score }}%</div>
                    {% else %}
                    <div class="progress-bar progress-bar-danger" style="width: {{ supplier.overall_score }}%">{{ supplier.overall_score }}%</div>
                    {% endif %}
                  </div>
                </td>
                <td class="text-center">{{ supplier.delivery_score }}%</td>
                <td class="text-center">{{ supplier.quality_score }}%</td>
                <td class="text-center">{{ supplier.cost_score }}%</td>
                <td class="text-center">{{ supplier.total_orders }}</td>
                <td class="text-center">{{ supplier.last_evaluation }}</td>
                <td class="text-right">
                  <div class="btn-group">
                    <a href="{{ supplier.view }}" data-toggle="tooltip" title="{{ button_view_details }}" class="btn btn-primary btn-sm"><i class="fa fa-eye"></i></a>
                    <a href="{{ supplier.evaluate }}" data-toggle="tooltip" title="{{ button_evaluate }}" class="btn btn-warning btn-sm"><i class="fa fa-star"></i></a>
                  </div>
                </td>
              </tr>
              {% endfor %}
              {% else %}
              <tr>
                <td class="text-center" colspan="8">{{ text_no_results }}</td>
              </tr>
              {% endif %}
            </tbody>
          </table>
        </div>
        <div class="row">
          <div class="col-sm-6 text-left">{{ pagination }}</div>
          <div class="col-sm-6 text-right">{{ results }}</div>
        </div>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript"><!--
$('#button-filter').on('click', function() {
    var url = 'index.php?route=supplier/performance&user_token={{ user_token }}';
    
    var filter_supplier = $('input[name=\'filter_supplier\']').val();
    if (filter_supplier) {
        url += '&filter_supplier=' + encodeURIComponent(filter_supplier);
    }
    
    var filter_score_min = $('input[name=\'filter_score_min\']').val();
    if (filter_score_min) {
        url += '&filter_score_min=' + filter_score_min;
    }
    
    var filter_performance_level = $('select[name=\'filter_performance_level\']').val();
    if (filter_performance_level) {
        url += '&filter_performance_level=' + filter_performance_level;
    }
    
    location = url;
});

$('#button-reset').on('click', function() {
    location = 'index.php?route=supplier/performance&user_token={{ user_token }}';
});

function exportReport() {
    var url = 'index.php?route=supplier/performance/report&user_token={{ user_token }}';
    
    // Add current filters to export
    var filter_supplier = $('input[name=\'filter_supplier\']').val();
    if (filter_supplier) {
        url += '&filter_supplier=' + encodeURIComponent(filter_supplier);
    }
    
    var filter_score_min = $('input[name=\'filter_score_min\']').val();
    if (filter_score_min) {
        url += '&filter_score_min=' + filter_score_min;
    }
    
    var filter_performance_level = $('select[name=\'filter_performance_level\']').val();
    if (filter_performance_level) {
        url += '&filter_performance_level=' + filter_performance_level;
    }
    
    window.open(url, '_blank');
}

// Initialize tooltips
$('[data-toggle="tooltip"]').tooltip();
//--></script>

{{ footer }}
