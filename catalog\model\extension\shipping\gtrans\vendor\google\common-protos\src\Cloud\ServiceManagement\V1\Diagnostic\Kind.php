<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/servicemanagement/v1/resources.proto

namespace Google\Cloud\ServiceManagement\V1\Diagnostic;

use UnexpectedValueException;

/**
 * The kind of diagnostic information possible.
 *
 * Protobuf type <code>google.api.servicemanagement.v1.Diagnostic.Kind</code>
 */
class Kind
{
    /**
     * Warnings and errors
     *
     * Generated from protobuf enum <code>WARNING = 0;</code>
     */
    const WARNING = 0;
    /**
     * Only errors
     *
     * Generated from protobuf enum <code>ERROR = 1;</code>
     */
    const ERROR = 1;

    private static $valueToName = [
        self::WARNING => 'WARNING',
        self::ERROR => 'ERROR',
    ];

    public static function name($value)
    {
        if (!isset(self::$valueToName[$value])) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no name defined for value %s', __CLASS__, $value));
        }
        return self::$valueToName[$value];
    }


    public static function value($name)
    {
        $const = __CLASS__ . '::' . strtoupper($name);
        if (!defined($const)) {
            throw new UnexpectedValueException(sprintf(
                    'Enum %s has no value defined for name %s', __CLASS__, $name));
        }
        return constant($const);
    }
}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(Kind::class, \Google\Cloud\ServiceManagement\V1\Diagnostic_Kind::class);

