<?php
// Heading
$_['heading_title']                            = 'Automated Aramex Express';

// Text
$_['text_shipping']                            = 'Shipping';
$_['text_success']                             = 'Success: You have Configured aramex Express shipping!';
$_['text_edit']                                = 'Configure aramex Shipping Account';
$_['text_info']                                = 'Informations Before Setup';
$_['text_info1']                                = 'Get aramex XML Api informations and Fill all required fields.';
$_['text_info2']                                = 'Please add Product weight/Dimansions to the require shipping products';
$_['text_info3']                                = 'Check the aramex From Country Currency is declared in your currency section (for Currency conversion)';
$_['text_info4']                                = 'Please activate the Licence Key without forget.';
$_['text_shiiping_address']                                = 'Configure aramex Shipping Address';
$_['text_rates']                                = 'Configure aramex Shipping rates & Services';
$_['text_packing']                                = 'Configure aramex Shipping Package';
$_['text_aramex_1'] = '[PDX] Priority Document Express';
$_['text_aramex_2'] = '[PPX] Priority Parcel Express';
$_['text_aramex_3'] = '[PLX] Priority Letter Express';
$_['text_aramex_4'] = '[DDX] Deferred Document Express';
$_['text_aramex_5'] = '[DPX] Deferred Parcel Express';
$_['text_aramex_6'] = '[GDX] Ground Document Express';
$_['text_aramex_7'] = '[GPX] Ground Parcel Express';

$_['text_regular_pickup']                      = 'Regular Pickup';
$_['text_request_courier']                     = 'Request Courier';
$_['text_your_packaging']                      = 'Your Packaging';
$_['text_prepaid_payment']                     = 'Prepaid';
$_['text_account_rate']                        = 'Account Rate';

//shipping Address
$_['entry_shipper_name']						= 'Shipper name';
$_['entry_company_name']						= 'Company name';
$_['entry_phone_num']						= 'Phone Number';
$_['entry_email_addr']						= 'Email Address';
$_['entry_address1']						= 'Address Line1';
$_['entry_address2']						= 'Address Line2';
$_['entry_city']						= 'City';
$_['entry_state']						= 'State';
$_['entry_country_code']						= 'Country Code';
$_['entry_realtime_rates']						= 'Enable Real Time Rates';
$_['entry_insurance']						= 'Enable Insurance';

//packing
$_['_entry_weight']							= 'Weight/Dimension Unit';
$_['_entry_kgcm']							= 'KG-CM';
$_['_entry_lbin']							= 'LBS-IN';
$_['_entry_packing_type']							= 'Choose packing type';
$_['text_per_item']							= 'Default: Pack items individually ';
$_['text_aramex_box']							= 'Recommended: Pack into boxes with weights and dimensions ';
$_['text_aramex_weight_based']							= 'Weight based: Calculate shipping on the basis of order total weight ';
$_['text_peritem_head']							= 'Configure Pack items individually (if choosed)';
$_['text_box_head']							= 'Configure Pack into boxes with weights and dimensions (if choosed)';
$_['text_weight_head']							= 'Configure Weight based (if choosed)';
$_['text_box']							= 'aramex Box';
$_['text_fly']							= 'Flyer';
$_['text_aramex_yp']							= 'Your Pack';
$_['text_enable']							= 'Enable';
$_['text_disable']							= 'Disable';
$_['text_head1']							="Name";
$_['text_head2']							="Length";
$_['text_head3']							="Width";
$_['text_head4']							="Height";
$_['text_head5']							="Box Weight";
$_['text_head6']							="Max Weight";
$_['text_head7']							="Enabled";
$_['text_head8']							="Package Type";
$_['text_head9']							="Add Box";
$_['text_head10']							="Remove selected box(es)";
$_['text_head11']							="Preloaded the Dimension and Weight in unit Inches and Pound. If you have selected unit as Centimetre and Kilogram please convert it accordingly.";
$_['text_head12']							="Maximum Weight / Packing";
$_['text_head13']							="Pack heavier items first";
$_['text_head14']							="Pack lighter items first";
$_['text_head15']							="Pack purely divided by weight";
$_['text_head16']							="PaperLess Trade (PLT)";
$_['text_head17']							="Signature Required";
$_['text_head18']							="aramex Tracking Message to Customers";
$_['text_head19']							="Request Archive Air Waybill";
$_['text_head20']							="Printing Size ";
$_['text_head21']							="8X4_A4_PDF";
$_['text_head22']							="8X4_thermal";
$_['text_head23']							="8X4_A4_TC_PDF";
$_['text_head24']							="8X4_CI_PDF";
$_['text_head25']							="8X4_CI_thermal";
$_['text_head26']							="8X4_RU_A4_PDF";
$_['text_head27']							="8X4_PDF";
$_['text_head28']							="8X4_CustBarCode_PDF";
$_['text_head29']							="8X4_CustBarCode_thermal";
$_['text_head30']							="6X4_A4_PDF";
$_['text_head31']							="6X4_thermal";
$_['text_head32']							="6X4_PDF";
$_['text_head33']							="Label Output Type";
$_['text_head34']							="PDF Output ";
$_['text_head35']							="ZPL2 Output";
$_['text_head36']							="EPL2 Output";
$_['text_head37']							="None";
$_['text_head38']							="Shipper";
$_['text_head39']							="Recipient";
$_['text_head40']							="Third Party/Other";
$_['text_head41']							="Duty Payment";
$_['text_head42']							="Shipping Content Description";
$_['text_head43']							="Company Logo URL";
$_['text_head44']							="Enable COD";
$_['text_head45']							="Auto Pickup Schedule";
$_['text_head46']							="Add Comment";
//label
$_['text_label']										="Configure Shipping Label";

$_['text_pickup']										="Configure Pickup";
// Entry
$_['entry_key']                                = 'User Name';
$_['entry_password']                           = 'Password';
$_['entry_account']                            = 'Account Number';
$_['entry_account_entity']                     = 'Account Entity';
$_['entry_account_pin']                        = 'Account PIN';
$_['entry_meter']                              = 'Meter Number';
$_['entry_postcode']                           = 'Post Code';
$_['entry_test']                               = 'Test Mode';
$_['entry_service']                            = 'Services';
$_['entry_dimension']                          = 'Box Dimensions (L x W x H)';
$_['entry_length_class']                       = 'Length Class';
$_['entry_length']                             = 'Length';
$_['entry_width']                              = 'Width';
$_['entry_height']                             = 'Height';
$_['entry_dropoff_type']                       = 'Drop Off Type';
$_['entry_packaging_type']                     = 'Packaging Type';
$_['entry_payment_type']                       = 'Payment Type';
$_['entry_display_time']                       = 'Debug Logs ( Orders Page )';
$_['entry_front_end_logs']                     = 'Debug Logs ( Checkout Page )';
$_['entry_display_weight']                     = 'Display Delivery Weight';
$_['entry_weight_class']                       = 'Weight Class';
$_['entry_tax_class']                          = 'Tax Class';
$_['entry_geo_zone']                           = 'Geo Zone';
$_['entry_status']                             = 'Status';
$_['entry_sort_order']                         = 'Sort Order';
$_['entry_pic_loc_type']                         = 'Location Type';

// Help
$_['help_length_class']                        = 'Set to inches or centimeters.';
$_['help_display_time']                        = 'By Enabling this, you will get requst and reponse log in Orders Page';
$_['help_display_weight']                      = 'Do you want to display the shipping weight? (e.g. Delivery Weight : 2.7674 kg)';
$_['help_weight_class']                        = 'Set to kilograms or pounds.';

// Error
$_['error_permission']                         = 'Warning: You do not have permission to modify aramex Express shipping!';
$_['error_key']                                = 'Key required!';
$_['error_password']                           = 'Password required!';
$_['error_account']                            = 'Account required!';
$_['error_meter']                              = 'Meter required!';
$_['error_postcode']                           = 'Post Code required!';
$_['error_dimension']                          = 'Width &amp; Height required!';