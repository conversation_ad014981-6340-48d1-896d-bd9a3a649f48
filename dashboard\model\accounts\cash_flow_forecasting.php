<?php
/**
 * نموذج التنبؤ بالتدفق النقدي المتقدم
 * نظام تنبؤ ذكي بالتدفقات النقدية مع تحليل السيناريوهات والذكاء الاصطناعي
 * يدعم التنبؤ قصير وطويل المدى مع تحليل المخاطر
 */
class ModelAccountsCashFlowForecasting extends Model {
    private $central_service;

    public function __construct($registry) {
        parent::__construct($registry);
        
        // تحميل الخدمات المركزية
        $this->load->model('core/central_service_manager');
        $this->central_service = $this->model_core_central_service_manager;
    }

    /**
     * إنشاء تنبؤ جديد
     */
    public function createForecast($data) {
        $this->db->query("START TRANSACTION");

        try {
            // إنشاء التنبؤ الأساسي
            $this->db->query("INSERT INTO cod_cash_flow_forecast SET 
                forecast_name = '" . $this->db->escape($data['forecast_name']) . "',
                forecast_type = '" . $this->db->escape($data['forecast_type']) . "',
                period_start = '" . $this->db->escape($data['period_start']) . "',
                period_end = '" . $this->db->escape($data['period_end']) . "',
                currency = '" . $this->db->escape($data['currency']) . "',
                forecast_method = '" . $this->db->escape($data['forecast_method']) . "',
                base_scenario = '" . $this->db->escape($data['base_scenario']) . "',
                include_historical = '" . (isset($data['include_historical']) ? 1 : 0) . "',
                status = 'draft',
                created_by = '" . (int)$this->user->getId() . "',
                date_created = NOW(),
                date_modified = NOW()
            ");

            $forecast_id = $this->db->getLastId();

            // إضافة بنود التنبؤ
            if (isset($data['forecast_lines']) && is_array($data['forecast_lines'])) {
                foreach ($data['forecast_lines'] as $line) {
                    $this->db->query("INSERT INTO cod_cash_flow_forecast_lines SET 
                        forecast_id = '" . (int)$forecast_id . "',
                        account_id = '" . (int)$line['account_id'] . "',
                        forecast_date = '" . $this->db->escape($line['forecast_date']) . "',
                        forecast_type = '" . $this->db->escape($line['forecast_type']) . "',
                        forecast_amount = '" . (float)$line['forecast_amount'] . "',
                        confidence_level = '" . $this->db->escape($line['confidence_level']) . "',
                        notes = '" . $this->db->escape($line['notes']) . "',
                        date_added = NOW()
                    ");
                }
            }

            $this->db->query("COMMIT");
            return $forecast_id;

        } catch (Exception $e) {
            $this->db->query("ROLLBACK");
            throw $e;
        }
    }

    /**
     * توليد التنبؤ التلقائي
     */
    public function generateForecast($data) {
        $this->db->query("START TRANSACTION");

        try {
            $forecast_id = $data['forecast_id'];
            $method = $data['generation_method'];
            $historical_periods = $data['historical_periods'];

            // جلب البيانات التاريخية
            $historical_data = $this->getHistoricalCashFlowData($historical_periods);

            // تطبيق خوارزمية التنبؤ حسب الطريقة المختارة
            $forecast_data = array();
            switch ($method) {
                case 'moving_average':
                    $forecast_data = $this->calculateMovingAverage($historical_data, $forecast_id);
                    break;
                case 'linear_regression':
                    $forecast_data = $this->calculateLinearRegression($historical_data, $forecast_id);
                    break;
                case 'seasonal_decomposition':
                    $forecast_data = $this->calculateSeasonalDecomposition($historical_data, $forecast_id);
                    break;
                case 'exponential_smoothing':
                    $forecast_data = $this->calculateExponentialSmoothing($historical_data, $forecast_id);
                    break;
                default:
                    throw new Exception('Invalid forecasting method');
            }

            // حفظ النتائج المولدة
            $this->saveForecastResults($forecast_id, $forecast_data);

            // تطبيق تعديلات الموسمية إذا كانت مطلوبة
            if (isset($data['seasonality_adjustment']) && $data['seasonality_adjustment']) {
                $this->applySeasonalityAdjustment($forecast_id);
            }

            // تطبيق تحليل الاتجاه إذا كان مطلوباً
            if (isset($data['trend_analysis']) && $data['trend_analysis']) {
                $this->applyTrendAnalysis($forecast_id);
            }

            // تحديث حالة التنبؤ
            $this->db->query("UPDATE cod_cash_flow_forecast SET 
                status = 'generated',
                generation_method = '" . $this->db->escape($method) . "',
                date_generated = NOW(),
                date_modified = NOW()
                WHERE forecast_id = '" . (int)$forecast_id . "'
            ");

            $this->db->query("COMMIT");
            return $forecast_data;

        } catch (Exception $e) {
            $this->db->query("ROLLBACK");
            throw $e;
        }
    }

    /**
     * تحليل السيناريوهات
     */
    public function analyzeScenario($data) {
        try {
            $forecast_id = $data['forecast_id'];
            $scenario_type = $data['scenario_type'];
            $adjustment_factors = $data['adjustment_factors'];

            // جلب بيانات التنبؤ الأساسي
            $base_forecast = $this->getForecastLines($forecast_id);

            // تطبيق عوامل التعديل حسب نوع السيناريو
            $scenario_data = array();
            foreach ($base_forecast as $line) {
                $adjusted_amount = $line['forecast_amount'];
                
                // تطبيق عوامل التعديل
                if (isset($adjustment_factors[$line['account_id']])) {
                    $factor = $adjustment_factors[$line['account_id']];
                    $adjusted_amount = $line['forecast_amount'] * (1 + $factor / 100);
                }

                // تطبيق عوامل المخاطر حسب نوع السيناريو
                switch ($scenario_type) {
                    case 'optimistic':
                        $adjusted_amount *= 1.1; // زيادة 10%
                        break;
                    case 'pessimistic':
                        $adjusted_amount *= 0.9; // نقص 10%
                        break;
                    case 'realistic':
                        // بدون تعديل إضافي
                        break;
                }

                $scenario_data[] = array(
                    'account_id' => $line['account_id'],
                    'forecast_date' => $line['forecast_date'],
                    'original_amount' => $line['forecast_amount'],
                    'adjusted_amount' => $adjusted_amount,
                    'variance' => $adjusted_amount - $line['forecast_amount'],
                    'variance_percentage' => (($adjusted_amount - $line['forecast_amount']) / $line['forecast_amount']) * 100
                );
            }

            // حفظ السيناريو إذا كان مطلوباً
            if (isset($data['save_scenario']) && $data['save_scenario']) {
                $this->saveScenario($forecast_id, $data['scenario_name'], $scenario_type, $scenario_data);
            }

            return $scenario_data;

        } catch (Exception $e) {
            throw $e;
        }
    }

    /**
     * جلب التنبؤات
     */
    public function getForecasts($filter = array()) {
        $sql = "SELECT cf.*, u.username as created_by_name
                FROM cod_cash_flow_forecast cf
                LEFT JOIN cod_user u ON (cf.created_by = u.user_id)
                WHERE 1=1";

        if (!empty($filter['status'])) {
            $sql .= " AND cf.status = '" . $this->db->escape($filter['status']) . "'";
        }

        if (!empty($filter['forecast_type'])) {
            $sql .= " AND cf.forecast_type = '" . $this->db->escape($filter['forecast_type']) . "'";
        }

        if (!empty($filter['date_from'])) {
            $sql .= " AND cf.period_start >= '" . $this->db->escape($filter['date_from']) . "'";
        }

        if (!empty($filter['date_to'])) {
            $sql .= " AND cf.period_end <= '" . $this->db->escape($filter['date_to']) . "'";
        }

        $sql .= " ORDER BY cf.date_created DESC";

        $query = $this->db->query($sql);
        return $query->rows;
    }

    /**
     * جلب التنبؤات النشطة
     */
    public function getActiveForecasts() {
        $query = $this->db->query("SELECT cf.*, u.username as created_by_name
                                   FROM cod_cash_flow_forecast cf
                                   LEFT JOIN cod_user u ON (cf.created_by = u.user_id)
                                   WHERE cf.status = 'active'
                                   ORDER BY cf.period_start ASC
        ");

        return $query->rows;
    }

    /**
     * جلب إحصائيات التدفق النقدي
     */
    public function getCashFlowStatistics() {
        $stats = array();

        // إجمالي التنبؤات
        $query = $this->db->query("SELECT COUNT(*) as total FROM cod_cash_flow_forecast");
        $stats['total_forecasts'] = $query->row['total'];

        // التنبؤات النشطة
        $query = $this->db->query("SELECT COUNT(*) as active FROM cod_cash_flow_forecast WHERE status = 'active'");
        $stats['active_forecasts'] = $query->row['active'];

        // متوسط دقة التنبؤات
        $query = $this->db->query("SELECT AVG(accuracy_score) as avg_accuracy FROM cod_cash_flow_forecast WHERE accuracy_score IS NOT NULL");
        $stats['average_accuracy'] = $query->row['avg_accuracy'] ?? 0;

        // إجمالي التدفقات المتوقعة للشهر الحالي
        $current_month = date('Y-m');
        $query = $this->db->query("SELECT 
                                      SUM(CASE WHEN forecast_type = 'inflow' THEN forecast_amount ELSE 0 END) as total_inflows,
                                      SUM(CASE WHEN forecast_type = 'outflow' THEN forecast_amount ELSE 0 END) as total_outflows
                                   FROM cod_cash_flow_forecast_lines cfl
                                   INNER JOIN cod_cash_flow_forecast cf ON (cfl.forecast_id = cf.forecast_id)
                                   WHERE cf.status = 'active' 
                                   AND DATE_FORMAT(cfl.forecast_date, '%Y-%m') = '" . $current_month . "'
        ");

        $stats['monthly_inflows'] = $query->row['total_inflows'] ?? 0;
        $stats['monthly_outflows'] = $query->row['total_outflows'] ?? 0;
        $stats['monthly_net_flow'] = $stats['monthly_inflows'] - $stats['monthly_outflows'];

        return $stats;
    }

    /**
     * جلب التحليل السريع
     */
    public function getQuickAnalysis() {
        $analysis = array();

        // تحليل الاتجاه العام
        $query = $this->db->query("SELECT 
                                      forecast_date,
                                      SUM(CASE WHEN forecast_type = 'inflow' THEN forecast_amount ELSE -forecast_amount END) as net_flow
                                   FROM cod_cash_flow_forecast_lines cfl
                                   INNER JOIN cod_cash_flow_forecast cf ON (cfl.forecast_id = cf.forecast_id)
                                   WHERE cf.status = 'active'
                                   AND cfl.forecast_date >= CURDATE()
                                   AND cfl.forecast_date <= DATE_ADD(CURDATE(), INTERVAL 3 MONTH)
                                   GROUP BY forecast_date
                                   ORDER BY forecast_date ASC
        ");

        $analysis['trend_data'] = $query->rows;

        // تحديد الفترات الحرجة (التدفق النقدي السالب)
        $critical_periods = array();
        $cumulative_flow = 0;
        foreach ($query->rows as $row) {
            $cumulative_flow += $row['net_flow'];
            if ($cumulative_flow < 0) {
                $critical_periods[] = array(
                    'date' => $row['forecast_date'],
                    'cumulative_flow' => $cumulative_flow,
                    'severity' => $this->calculateSeverity($cumulative_flow)
                );
            }
        }

        $analysis['critical_periods'] = $critical_periods;

        return $analysis;
    }

    /**
     * جلب السيناريوهات المحفوظة
     */
    public function getSavedScenarios() {
        $query = $this->db->query("SELECT * FROM cod_cash_flow_scenarios ORDER BY date_created DESC LIMIT 10");
        return $query->rows;
    }

    /**
     * جلب بيانات تنبؤ محدد
     */
    public function getForecast($forecast_id) {
        $query = $this->db->query("SELECT cf.*, u.username as created_by_name
                                   FROM cod_cash_flow_forecast cf
                                   LEFT JOIN cod_user u ON (cf.created_by = u.user_id)
                                   WHERE cf.forecast_id = '" . (int)$forecast_id . "'
        ");

        return $query->num_rows ? $query->row : null;
    }

    /**
     * جلب بنود التنبؤ
     */
    public function getForecastLines($forecast_id) {
        $query = $this->db->query("SELECT cfl.*, ad.name as account_name, a.code as account_code
                                   FROM cod_cash_flow_forecast_lines cfl
                                   LEFT JOIN cod_accounts a ON (cfl.account_id = a.account_id)
                                   LEFT JOIN cod_account_description ad ON (a.account_id = ad.account_id AND ad.language_id = '" . (int)$this->config->get('config_language_id') . "')
                                   WHERE cfl.forecast_id = '" . (int)$forecast_id . "'
                                   ORDER BY cfl.forecast_date ASC
        ");

        return $query->rows;
    }

    /**
     * دوال مساعدة للخوارزميات
     */
    private function getHistoricalCashFlowData($periods) {
        $start_date = date('Y-m-d', strtotime('-' . $periods . ' months'));
        
        $query = $this->db->query("SELECT 
                                      DATE_FORMAT(je.journal_date, '%Y-%m') as period,
                                      jel.account_id,
                                      SUM(jel.debit - jel.credit) as net_amount
                                   FROM cod_journals je
                                   INNER JOIN cod_journal_entries jel ON (je.journal_id = jel.journal_id)
                                   WHERE je.journal_date >= '" . $start_date . "'
                                   GROUP BY DATE_FORMAT(je.journal_date, '%Y-%m'), jel.account_id
                                   ORDER BY period ASC
        ");

        return $query->rows;
    }

    private function calculateMovingAverage($historical_data, $forecast_id) {
        // تطبيق خوارزمية المتوسط المتحرك
        $forecast_data = array();
        
        // تجميع البيانات حسب الحساب
        $account_data = array();
        foreach ($historical_data as $row) {
            $account_data[$row['account_id']][] = $row['net_amount'];
        }

        // حساب المتوسط المتحرك لكل حساب
        foreach ($account_data as $account_id => $amounts) {
            $average = array_sum($amounts) / count($amounts);
            $forecast_data[] = array(
                'account_id' => $account_id,
                'forecast_amount' => $average,
                'confidence_level' => 'medium'
            );
        }

        return $forecast_data;
    }

    private function calculateLinearRegression($historical_data, $forecast_id) {
        // تطبيق خوارزمية الانحدار الخطي
        // هذه نسخة مبسطة - يمكن تطويرها أكثر
        return $this->calculateMovingAverage($historical_data, $forecast_id);
    }

    private function calculateSeasonalDecomposition($historical_data, $forecast_id) {
        // تطبيق تحليل الموسمية
        return $this->calculateMovingAverage($historical_data, $forecast_id);
    }

    private function calculateExponentialSmoothing($historical_data, $forecast_id) {
        // تطبيق التنعيم الأسي
        return $this->calculateMovingAverage($historical_data, $forecast_id);
    }

    private function saveForecastResults($forecast_id, $forecast_data) {
        // حذف النتائج السابقة
        $this->db->query("DELETE FROM cod_cash_flow_forecast_lines WHERE forecast_id = '" . (int)$forecast_id . "'");

        // إضافة النتائج الجديدة
        foreach ($forecast_data as $data) {
            $this->db->query("INSERT INTO cod_cash_flow_forecast_lines SET 
                forecast_id = '" . (int)$forecast_id . "',
                account_id = '" . (int)$data['account_id'] . "',
                forecast_date = CURDATE(),
                forecast_type = '" . ($data['forecast_amount'] >= 0 ? 'inflow' : 'outflow') . "',
                forecast_amount = '" . (float)abs($data['forecast_amount']) . "',
                confidence_level = '" . $this->db->escape($data['confidence_level']) . "',
                date_added = NOW()
            ");
        }
    }

    private function applySeasonalityAdjustment($forecast_id) {
        // تطبيق تعديلات الموسمية
        // يمكن تطوير هذه الدالة لتطبيق تعديلات موسمية معقدة
    }

    private function applyTrendAnalysis($forecast_id) {
        // تطبيق تحليل الاتجاه
        // يمكن تطوير هذه الدالة لتطبيق تحليل اتجاه متقدم
    }

    private function saveScenario($forecast_id, $scenario_name, $scenario_type, $scenario_data) {
        $this->db->query("INSERT INTO cod_cash_flow_scenarios SET 
            forecast_id = '" . (int)$forecast_id . "',
            scenario_name = '" . $this->db->escape($scenario_name) . "',
            scenario_type = '" . $this->db->escape($scenario_type) . "',
            scenario_data = '" . $this->db->escape(json_encode($scenario_data)) . "',
            created_by = '" . (int)$this->user->getId() . "',
            date_created = NOW()
        ");
    }

    private function calculateSeverity($cumulative_flow) {
        if ($cumulative_flow < -100000) {
            return 'critical';
        } elseif ($cumulative_flow < -50000) {
            return 'high';
        } elseif ($cumulative_flow < -10000) {
            return 'medium';
        } else {
            return 'low';
        }
    }

    public function getVarianceAnalysis($forecast_id) {
        // تحليل الانحرافات بين المتوقع والفعلي
        $query = $this->db->query("SELECT 
                                      cfl.*,
                                      ad.name as account_name,
                                      COALESCE(actual.actual_amount, 0) as actual_amount,
                                      (COALESCE(actual.actual_amount, 0) - cfl.forecast_amount) as variance
                                   FROM cod_cash_flow_forecast_lines cfl
                                   LEFT JOIN cod_accounts a ON (cfl.account_id = a.account_id)
                                   LEFT JOIN cod_account_description ad ON (a.account_id = ad.account_id AND ad.language_id = '" . (int)$this->config->get('config_language_id') . "')
                                   LEFT JOIN (
                                       SELECT account_id, SUM(debit - credit) as actual_amount
                                       FROM cod_journal_entries
                                       WHERE entry_date = CURDATE()
                                       GROUP BY account_id
                                   ) actual ON (cfl.account_id = actual.account_id)
                                   WHERE cfl.forecast_id = '" . (int)$forecast_id . "'
                                   ORDER BY ABS(variance) DESC
        ");

        return $query->rows;
    }

    public function getForecastScenarios($forecast_id) {
        $query = $this->db->query("SELECT * FROM cod_cash_flow_scenarios WHERE forecast_id = '" . (int)$forecast_id . "' ORDER BY date_created DESC");
        return $query->rows;
    }

    public function getChartData($forecast_id) {
        $query = $this->db->query("SELECT 
                                      forecast_date,
                                      SUM(CASE WHEN forecast_type = 'inflow' THEN forecast_amount ELSE 0 END) as inflows,
                                      SUM(CASE WHEN forecast_type = 'outflow' THEN forecast_amount ELSE 0 END) as outflows
                                   FROM cod_cash_flow_forecast_lines
                                   WHERE forecast_id = '" . (int)$forecast_id . "'
                                   GROUP BY forecast_date
                                   ORDER BY forecast_date ASC
        ");

        return $query->rows;
    }

    public function generateReport($filter) {
        // توليد تقرير شامل للتدفق النقدي
        $report_data = array();
        
        // بيانات التنبؤات
        $report_data['forecasts'] = $this->getForecasts($filter);
        
        // تحليل الدقة
        $report_data['accuracy_analysis'] = $this->getAccuracyAnalysis($filter);
        
        // تحليل الاتجاهات
        $report_data['trend_analysis'] = $this->getTrendAnalysis($filter);

        return $report_data;
    }

    private function getAccuracyAnalysis($filter) {
        // تحليل دقة التنبؤات
        return array();
    }

    private function getTrendAnalysis($filter) {
        // تحليل الاتجاهات
        return array();
    }
}
