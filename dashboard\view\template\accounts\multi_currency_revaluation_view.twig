{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        {% if has_permission %}
        <button type="button" id="button-edit" data-toggle="tooltip" title="{{ button_edit }}" class="btn btn-primary">
          <i class="fa fa-pencil"></i> {{ button_edit }}
        </button>
        <button type="button" id="button-reprocess" data-toggle="tooltip" title="{{ button_reprocess }}" class="btn btn-warning">
          <i class="fa fa-refresh"></i> {{ button_reprocess }}
        </button>
        <button type="button" id="button-post" data-toggle="tooltip" title="{{ button_post }}" class="btn btn-success">
          <i class="fa fa-check"></i> {{ button_post }}
        </button>
        <button type="button" id="button-export" data-toggle="tooltip" title="{{ button_export }}" class="btn btn-info">
          <i class="fa fa-download"></i> {{ button_export }}
        </button>
        {% endif %}
        <a href="{{ back }}" data-toggle="tooltip" title="{{ button_back }}" class="btn btn-default">
          <i class="fa fa-reply"></i> {{ button_back }}
        </a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    {% if success %}
    <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    
    <!-- معلومات إعادة التقييم الأساسية -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-info-circle"></i> {{ text_revaluation_info }}</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-md-6">
            <table class="table table-bordered">
              <tr>
                <td><strong>{{ text_revaluation_name }}:</strong></td>
                <td>{{ revaluation.revaluation_name }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_revaluation_date }}:</strong></td>
                <td>{{ revaluation.revaluation_date }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_base_currency }}:</strong></td>
                <td>{{ revaluation.base_currency }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_revaluation_method }}:</strong></td>
                <td>{{ revaluation.revaluation_method }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_status }}:</strong></td>
                <td>
                  {% if revaluation.status == 'draft' %}
                    <span class="label label-warning">{{ text_status_draft }}</span>
                  {% elseif revaluation.status == 'processed' %}
                    <span class="label label-info">{{ text_status_processed }}</span>
                  {% elseif revaluation.status == 'posted' %}
                    <span class="label label-success">{{ text_status_posted }}</span>
                  {% else %}
                    <span class="label label-danger">{{ text_status_error }}</span>
                  {% endif %}
                </td>
              </tr>
            </table>
          </div>
          <div class="col-md-6">
            <table class="table table-bordered">
              <tr>
                <td><strong>{{ text_total_gain_loss }}:</strong></td>
                <td class="{% if revaluation.total_gain_loss >= 0 %}text-success{% else %}text-danger{% endif %}">
                  {{ revaluation.total_gain_loss }}
                </td>
              </tr>
              <tr>
                <td><strong>{{ text_created_by }}:</strong></td>
                <td>{{ revaluation.created_by_name }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_date_created }}:</strong></td>
                <td>{{ revaluation.date_created }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_last_updated }}:</strong></td>
                <td>{{ revaluation.date_modified }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_journal_entry }}:</strong></td>
                <td>
                  {% if revaluation.journal_entry_id %}
                    <a href="{{ journal_entry_link }}" target="_blank">{{ revaluation.journal_entry_id }}</a>
                  {% else %}
                    {{ text_not_posted }}
                  {% endif %}
                </td>
              </tr>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- تفاصيل إعادة التقييم -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-list"></i> {{ text_revaluation_details }}</h3>
      </div>
      <div class="panel-body">
        <div class="table-responsive">
          <table class="table table-bordered table-hover">
            <thead>
              <tr>
                <th>{{ column_currency }}</th>
                <th>{{ column_account }}</th>
                <th class="text-right">{{ column_original_amount }}</th>
                <th class="text-right">{{ column_original_rate }}</th>
                <th class="text-right">{{ column_new_rate }}</th>
                <th class="text-right">{{ column_revalued_amount }}</th>
                <th class="text-right">{{ column_gain_loss }}</th>
                <th>{{ column_status }}</th>
              </tr>
            </thead>
            <tbody>
              {% for detail in revaluation_details %}
              <tr>
                <td>{{ detail.currency_code }}</td>
                <td>{{ detail.account_name }}</td>
                <td class="text-right">{{ detail.original_amount }}</td>
                <td class="text-right">{{ detail.original_rate }}</td>
                <td class="text-right">{{ detail.new_rate }}</td>
                <td class="text-right">{{ detail.revalued_amount }}</td>
                <td class="text-right {% if detail.gain_loss >= 0 %}text-success{% else %}text-danger{% endif %}">
                  {{ detail.gain_loss }}
                </td>
                <td>
                  {% if detail.status == 'calculated' %}
                    <span class="label label-info">{{ text_calculated }}</span>
                  {% elseif detail.status == 'posted' %}
                    <span class="label label-success">{{ text_posted }}</span>
                  {% else %}
                    <span class="label label-warning">{{ text_pending }}</span>
                  {% endif %}
                </td>
              </tr>
              {% endfor %}
            </tbody>
            <tfoot>
              <tr class="info">
                <td colspan="6"><strong>{{ text_total }}</strong></td>
                <td class="text-right"><strong class="{% if total_gain_loss >= 0 %}text-success{% else %}text-danger{% endif %}">{{ total_gain_loss }}</strong></td>
                <td></td>
              </tr>
            </tfoot>
          </table>
        </div>
      </div>
    </div>

    <!-- أسعار الصرف المستخدمة -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-exchange"></i> {{ text_exchange_rates }}</h3>
      </div>
      <div class="panel-body">
        <div class="table-responsive">
          <table class="table table-bordered">
            <thead>
              <tr>
                <th>{{ column_currency }}</th>
                <th class="text-right">{{ column_previous_rate }}</th>
                <th class="text-right">{{ column_current_rate }}</th>
                <th class="text-right">{{ column_rate_change }}</th>
                <th>{{ column_rate_source }}</th>
                <th>{{ column_rate_date }}</th>
              </tr>
            </thead>
            <tbody>
              {% for rate in exchange_rates %}
              <tr>
                <td>{{ rate.currency_code }}</td>
                <td class="text-right">{{ rate.previous_rate }}</td>
                <td class="text-right">{{ rate.current_rate }}</td>
                <td class="text-right {% if rate.rate_change >= 0 %}text-success{% else %}text-danger{% endif %}">
                  {{ rate.rate_change }}%
                </td>
                <td>{{ rate.rate_source }}</td>
                <td>{{ rate.rate_date }}</td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- القيد المحاسبي المقترح -->
    {% if journal_entry_preview %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-book"></i> {{ text_journal_entry_preview }}</h3>
      </div>
      <div class="panel-body">
        <div class="table-responsive">
          <table class="table table-bordered">
            <thead>
              <tr>
                <th>{{ column_account }}</th>
                <th>{{ column_description }}</th>
                <th class="text-right">{{ column_debit }}</th>
                <th class="text-right">{{ column_credit }}</th>
              </tr>
            </thead>
            <tbody>
              {% for entry in journal_entry_preview %}
              <tr>
                <td>{{ entry.account_name }}</td>
                <td>{{ entry.description }}</td>
                <td class="text-right">{{ entry.debit }}</td>
                <td class="text-right">{{ entry.credit }}</td>
              </tr>
              {% endfor %}
            </tbody>
            <tfoot>
              <tr class="info">
                <td colspan="2"><strong>{{ text_total }}</strong></td>
                <td class="text-right"><strong>{{ total_debit }}</strong></td>
                <td class="text-right"><strong>{{ total_credit }}</strong></td>
              </tr>
            </tfoot>
          </table>
        </div>
      </div>
    </div>
    {% endif %}

    <!-- ملاحظات -->
    {% if revaluation.notes %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-sticky-note"></i> {{ text_notes }}</h3>
      </div>
      <div class="panel-body">
        <p>{{ revaluation.notes|nl2br }}</p>
      </div>
    </div>
    {% endif %}

  </div>
</div>

<script type="text/javascript">
$(document).ready(function() {
    // إعادة المعالجة
    $('#button-reprocess').on('click', function() {
        if (confirm('{{ text_confirm_reprocess }}')) {
            window.location = '{{ reprocess }}';
        }
    });

    // ترحيل القيد
    $('#button-post').on('click', function() {
        if (confirm('{{ text_confirm_post }}')) {
            $.ajax({
                url: '{{ post }}',
                type: 'post',
                dataType: 'json',
                success: function(json) {
                    if (json['success']) {
                        location.reload();
                    } else {
                        alert(json['error']);
                    }
                }
            });
        }
    });

    // تصدير البيانات
    $('#button-export').on('click', function() {
        window.open('{{ export }}', '_blank');
    });

    // تحرير إعادة التقييم
    $('#button-edit').on('click', function() {
        window.location = '{{ edit }}';
    });
});
</script>

{{ footer }}
