{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        {% if has_permission %}
        <button type="button" id="button-edit" data-toggle="tooltip" title="{{ button_edit }}" class="btn btn-primary">
          <i class="fa fa-pencil"></i> {{ button_edit }}
        </button>
        <button type="button" id="button-regenerate" data-toggle="tooltip" title="{{ button_regenerate }}" class="btn btn-warning">
          <i class="fa fa-refresh"></i> {{ button_regenerate }}
        </button>
        <button type="button" id="button-export" data-toggle="tooltip" title="{{ button_export }}" class="btn btn-success">
          <i class="fa fa-download"></i> {{ button_export }}
        </button>
        <button type="button" id="button-print" data-toggle="tooltip" title="{{ button_print }}" class="btn btn-info">
          <i class="fa fa-print"></i> {{ button_print }}
        </button>
        {% endif %}
        <a href="{{ back }}" data-toggle="tooltip" title="{{ button_back }}" class="btn btn-default">
          <i class="fa fa-reply"></i> {{ button_back }}
        </a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    {% if success %}
    <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    
    <!-- معلومات التوحيد الأساسية -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-info-circle"></i> {{ text_consolidation_info }}</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-md-6">
            <table class="table table-bordered">
              <tr>
                <td><strong>{{ text_consolidation_name }}:</strong></td>
                <td>{{ consolidation.consolidation_name }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_consolidation_date }}:</strong></td>
                <td>{{ consolidation.consolidation_date }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_period_start }}:</strong></td>
                <td>{{ consolidation.period_start }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_period_end }}:</strong></td>
                <td>{{ consolidation.period_end }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_status }}:</strong></td>
                <td>
                  {% if consolidation.status == 'draft' %}
                    <span class="label label-warning">{{ text_status_draft }}</span>
                  {% elseif consolidation.status == 'processing' %}
                    <span class="label label-info">{{ text_status_processing }}</span>
                  {% elseif consolidation.status == 'completed' %}
                    <span class="label label-success">{{ text_status_completed }}</span>
                  {% else %}
                    <span class="label label-danger">{{ text_status_error }}</span>
                  {% endif %}
                </td>
              </tr>
            </table>
          </div>
          <div class="col-md-6">
            <table class="table table-bordered">
              <tr>
                <td><strong>{{ text_base_currency }}:</strong></td>
                <td>{{ consolidation.base_currency }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_consolidation_method }}:</strong></td>
                <td>{{ consolidation.consolidation_method }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_created_by }}:</strong></td>
                <td>{{ consolidation.created_by_name }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_date_created }}:</strong></td>
                <td>{{ consolidation.date_created }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_last_updated }}:</strong></td>
                <td>{{ consolidation.date_modified }}</td>
              </tr>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- الشركات التابعة المشمولة -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-building"></i> {{ text_subsidiaries_included }}</h3>
      </div>
      <div class="panel-body">
        <div class="table-responsive">
          <table class="table table-bordered table-hover">
            <thead>
              <tr>
                <th>{{ column_subsidiary_name }}</th>
                <th>{{ column_ownership_percentage }}</th>
                <th>{{ column_currency }}</th>
                <th>{{ column_consolidation_method }}</th>
                <th>{{ column_status }}</th>
              </tr>
            </thead>
            <tbody>
              {% for subsidiary in subsidiaries %}
              <tr>
                <td>{{ subsidiary.subsidiary_name }}</td>
                <td>{{ subsidiary.ownership_percentage }}%</td>
                <td>{{ subsidiary.currency }}</td>
                <td>{{ subsidiary.consolidation_method }}</td>
                <td>
                  {% if subsidiary.status == 'included' %}
                    <span class="label label-success">{{ text_included }}</span>
                  {% else %}
                    <span class="label label-warning">{{ text_excluded }}</span>
                  {% endif %}
                </td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- القوائم المالية الموحدة -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-file-text"></i> {{ text_consolidated_statements }}</h3>
      </div>
      <div class="panel-body">
        <ul class="nav nav-tabs" role="tablist">
          <li role="presentation" class="active">
            <a href="#balance-sheet" aria-controls="balance-sheet" role="tab" data-toggle="tab">
              <i class="fa fa-balance-scale"></i> {{ tab_balance_sheet }}
            </a>
          </li>
          <li role="presentation">
            <a href="#income-statement" aria-controls="income-statement" role="tab" data-toggle="tab">
              <i class="fa fa-line-chart"></i> {{ tab_income_statement }}
            </a>
          </li>
          <li role="presentation">
            <a href="#cash-flow" aria-controls="cash-flow" role="tab" data-toggle="tab">
              <i class="fa fa-money"></i> {{ tab_cash_flow }}
            </a>
          </li>
          <li role="presentation">
            <a href="#eliminations" aria-controls="eliminations" role="tab" data-toggle="tab">
              <i class="fa fa-minus-circle"></i> {{ tab_eliminations }}
            </a>
          </li>
        </ul>

        <div class="tab-content">
          <!-- الميزانية العمومية الموحدة -->
          <div role="tabpanel" class="tab-pane active" id="balance-sheet">
            <div class="table-responsive" style="margin-top: 15px;">
              <table class="table table-bordered">
                <thead>
                  <tr class="info">
                    <th>{{ column_account }}</th>
                    <th class="text-right">{{ column_amount }}</th>
                  </tr>
                </thead>
                <tbody>
                  <tr class="success">
                    <td colspan="2"><strong>{{ text_assets }}</strong></td>
                  </tr>
                  {% for asset in balance_sheet.assets %}
                  <tr>
                    <td style="padding-left: 20px;">{{ asset.account_name }}</td>
                    <td class="text-right">{{ asset.amount }}</td>
                  </tr>
                  {% endfor %}
                  <tr class="warning">
                    <td colspan="2"><strong>{{ text_liabilities }}</strong></td>
                  </tr>
                  {% for liability in balance_sheet.liabilities %}
                  <tr>
                    <td style="padding-left: 20px;">{{ liability.account_name }}</td>
                    <td class="text-right">{{ liability.amount }}</td>
                  </tr>
                  {% endfor %}
                  <tr class="info">
                    <td colspan="2"><strong>{{ text_equity }}</strong></td>
                  </tr>
                  {% for equity in balance_sheet.equity %}
                  <tr>
                    <td style="padding-left: 20px;">{{ equity.account_name }}</td>
                    <td class="text-right">{{ equity.amount }}</td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>

          <!-- قائمة الدخل الموحدة -->
          <div role="tabpanel" class="tab-pane" id="income-statement">
            <div class="table-responsive" style="margin-top: 15px;">
              <table class="table table-bordered">
                <thead>
                  <tr class="info">
                    <th>{{ column_account }}</th>
                    <th class="text-right">{{ column_amount }}</th>
                  </tr>
                </thead>
                <tbody>
                  <tr class="success">
                    <td colspan="2"><strong>{{ text_revenues }}</strong></td>
                  </tr>
                  {% for revenue in income_statement.revenues %}
                  <tr>
                    <td style="padding-left: 20px;">{{ revenue.account_name }}</td>
                    <td class="text-right">{{ revenue.amount }}</td>
                  </tr>
                  {% endfor %}
                  <tr class="warning">
                    <td colspan="2"><strong>{{ text_expenses }}</strong></td>
                  </tr>
                  {% for expense in income_statement.expenses %}
                  <tr>
                    <td style="padding-left: 20px;">{{ expense.account_name }}</td>
                    <td class="text-right">{{ expense.amount }}</td>
                  </tr>
                  {% endfor %}
                  <tr class="info">
                    <td><strong>{{ text_net_income }}</strong></td>
                    <td class="text-right"><strong>{{ income_statement.net_income }}</strong></td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <!-- قائمة التدفق النقدي الموحدة -->
          <div role="tabpanel" class="tab-pane" id="cash-flow">
            <div class="table-responsive" style="margin-top: 15px;">
              <table class="table table-bordered">
                <thead>
                  <tr class="info">
                    <th>{{ column_activity }}</th>
                    <th class="text-right">{{ column_amount }}</th>
                  </tr>
                </thead>
                <tbody>
                  <tr class="success">
                    <td colspan="2"><strong>{{ text_operating_activities }}</strong></td>
                  </tr>
                  {% for operating in cash_flow.operating %}
                  <tr>
                    <td style="padding-left: 20px;">{{ operating.activity_name }}</td>
                    <td class="text-right">{{ operating.amount }}</td>
                  </tr>
                  {% endfor %}
                  <tr class="warning">
                    <td colspan="2"><strong>{{ text_investing_activities }}</strong></td>
                  </tr>
                  {% for investing in cash_flow.investing %}
                  <tr>
                    <td style="padding-left: 20px;">{{ investing.activity_name }}</td>
                    <td class="text-right">{{ investing.amount }}</td>
                  </tr>
                  {% endfor %}
                  <tr class="info">
                    <td colspan="2"><strong>{{ text_financing_activities }}</strong></td>
                  </tr>
                  {% for financing in cash_flow.financing %}
                  <tr>
                    <td style="padding-left: 20px;">{{ financing.activity_name }}</td>
                    <td class="text-right">{{ financing.amount }}</td>
                  </tr>
                  {% endfor %}
                  <tr class="active">
                    <td><strong>{{ text_net_cash_flow }}</strong></td>
                    <td class="text-right"><strong>{{ cash_flow.net_cash_flow }}</strong></td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <!-- قيود الإلغاء -->
          <div role="tabpanel" class="tab-pane" id="eliminations">
            <div class="table-responsive" style="margin-top: 15px;">
              <table class="table table-bordered table-hover">
                <thead>
                  <tr>
                    <th>{{ column_elimination_type }}</th>
                    <th>{{ column_description }}</th>
                    <th class="text-right">{{ column_amount }}</th>
                    <th>{{ column_status }}</th>
                  </tr>
                </thead>
                <tbody>
                  {% for elimination in eliminations %}
                  <tr>
                    <td>{{ elimination.elimination_type }}</td>
                    <td>{{ elimination.description }}</td>
                    <td class="text-right">{{ elimination.amount }}</td>
                    <td>
                      {% if elimination.status == 'applied' %}
                        <span class="label label-success">{{ text_applied }}</span>
                      {% else %}
                        <span class="label label-warning">{{ text_pending }}</span>
                      {% endif %}
                    </td>
                  </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- ملاحظات وتعليقات -->
    {% if consolidation.notes %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-sticky-note"></i> {{ text_notes }}</h3>
      </div>
      <div class="panel-body">
        <p>{{ consolidation.notes|nl2br }}</p>
      </div>
    </div>
    {% endif %}

  </div>
</div>

<script type="text/javascript">
$(document).ready(function() {
    $('#button-regenerate').on('click', function() {
        if (confirm('{{ text_confirm_regenerate }}')) {
            window.location = '{{ regenerate }}';
        }
    });

    $('#button-export').on('click', function() {
        window.open('{{ export }}', '_blank');
    });

    $('#button-print').on('click', function() {
        window.print();
    });

    $('#button-edit').on('click', function() {
        window.location = '{{ edit }}';
    });
});
</script>

{{ footer }}
