{{ header }}{{ column_left }}
<div id="content">
	<div class="page-header">
		<div class="container-fluid">
			<div class="pull-right">
				<a href="{{ back }}" data-toggle="tooltip" title="{{ button_back }}" class="btn btn-default"><i class="fa fa-reply"></i></a>
			</div>
			<h1>{{ heading_title }}</h1>
			<ul class="breadcrumb">
				{% for breadcrumb in breadcrumbs %}
				<li><a href="{{ breadcrumb['href'] }}">{{ breadcrumb['text'] }}</a></li>
				{% endfor %}
			</ul>
		</div>
	</div>
	<div class="container-fluid">
		{% if (error_warning) %}
		<div class="alert alert-danger"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
			<button type="button" class="close" data-dismiss="alert">&times;</button>
		</div>
		{% endif %}
		{% if (success) %}
		<div class="alert alert-success"><i class="fa fa-check-circle"></i> {{ success }}
			<button type="button" class="close" data-dismiss="alert">&times;</button>
		</div>
		{% endif %}
		{% if ((not error_warning) and (not success)) %}
		<div id="export_import_welcome" class="alert alert-info">
			<i class="fa fa-info-circle"></i>
			<button type="button" class="close" data-dismiss="alert">&times;</button>
			{{ text_welcome }}<br /><br />{{ text_used_category_ids}} {{ text_used_product_ids }}
		</div>
		{% endif %}

		<div class="panel panel-default">
			<div class="panel-body">

				<ul class="nav nav-tabs">
					<li class="active"><a href="#tab-export" data-toggle="tab">{{ tab_export }}</a></li>
					<li><a href="#tab-import" data-toggle="tab">{{ tab_import }}</a></li>
					<li><a href="#tab-settings" data-toggle="tab">{{ tab_settings }}</a></li>
					<li><a href="#tab-support" data-toggle="tab">{{ tab_support }}</a></li>
				</ul>

				<div class="tab-content">

					<div class="tab-pane active" id="tab-export">
						<form action="{{ export }}" method="post" enctype="multipart/form-data" id="export" class="form-horizontal">
							<div class="form-group" style="margin:0px;">
								<div class="col-sm-12">{{ entry_export }}</div>
							</div>
							<div class="form-group" style="margin:0px;">
								<div class="col-sm-12">{{ entry_export_type }}
								</div>
								<div class="col-sm-12">
									<label class="radio-inline">
										{% if (export_type=='c') %}
										<input type="radio" name="export_type" value="c" checked="checked" />
										{% else %}
										<input type="radio" name="export_type" value="c" />
										{% endif %}
										{{ text_export_type_category }}
									</label>
									<br />
									<label class="radio-inline">
										{% if (export_type=='p') %}
										<input type="radio" name="export_type" value="p" checked="checked" />
										{% else %}
										<input type="radio" name="export_type" value="p" />
										{% endif %}
										{{ text_export_type_product }}
									</label>
									<br />
									<label class="radio-inline">
										{% if (export_type=='o') %}
										<input type="radio" name="export_type" value="o" checked="checked" />
										{% else %}
										<input type="radio" name="export_type" value="o" />
										{% endif %}
										{{ text_export_type_option }}
									</label>
									<br />
									<label class="radio-inline">
										{% if (export_type=='a') %}
										<input type="radio" name="export_type" value="a" checked="checked" />
										{% else %}
										<input type="radio" name="export_type" value="a" />
										{% endif %}
										{{ text_export_type_attribute }}
									</label>
									<br />
									{% if (exist_filter) %}
									<label class="radio-inline">
										{% if (export_type=='f') %}
										<input type="radio" name="export_type" value="f" checked="checked" />
										{% else %}
										<input type="radio" name="export_type" value="f" />
										{% endif %}
										{{ text_export_type_filter }}
									</label>
									<br />
									{% endif %}
									<label class="radio-inline">
										{% if (export_type=='u') %}
										<input type="radio" name="export_type" value="u" checked="checked" />
										{% else %}
										<input type="radio" name="export_type" value="u" />
										{% endif %}
										{{ text_export_type_customer }}
									</label>
									<br />
								</div>
							</div>
							<div class="form-group" style="margin:0px;" id="export_filter">
								<div class="col-sm-6">
									{{ entry_category_filter }}
									<span class="help">{{ help_category_filter }}</span>
									<br />
									<input type="text" name="category" value="" placeholder="{{ entry_category }}" id="input-category" class="form-control" />
									<div id="categories" class="well well-sm" style="height: 100px; overflow: auto;"> 
										{% for category in categories %}
										<div id="category{{ category.category_id }}"><i class="fa fa-minus-circle"></i> {{ category.name }}
											<input type="hidden" name="categories[]" value="{{ category.category_id }}" />
										</div>
										{% endfor %}
									</div>
								</div>
								<div class="col-sm-6">
									{{ entry_manufacturer_filter }}
									<span class="help">{{ help_manufacturer_filter }}</span>
									<br />
									<input type="text" name="manufacturer" value="" placeholder="{{ entry_manufacturer }}" id="input-manufacturer" class="form-control" />
									<div id="manufacturers" class="well well-sm" style="height: 100px; overflow: auto;"> 
										{% for manufacturer in manufacturers %}
										<div id="manufacturer{{ manufacturer.manufacturer_id }}"><i class="fa fa-minus-circle"></i> {{ manufacturer.name }}
											<input type="hidden" name="manufacturers[]" value="{{ manufacturer.manufacturer_id }}" />
										</div>
										{% endfor %}
									</div>
								</div>
							</div>
							<div class="form-group" style="margin:0px;" id="range_type">
								<div class="col-sm-12">
									{{ entry_range_type }}
									<span class="help">{{ help_range_type }}</span>
								</div>
								<div class="col-sm-12">
									<label class="radio-inline">
										<input type="radio" name="range_type" value="id" id="range_type_id">{{ button_export_id }} &nbsp;&nbsp;
									</label>
									<label class="radio-inline">
										<input type="radio" name="range_type" value="page" id="range_type_page">{{ button_export_page }}
									</label>
									<br /><br />
									<span class="id">{{ entry_start_id }}</span>
									<span class="page">{{ entry_start_index }}</span>
									<br />
									<input type="text" name="min" value="{{ min }}" />
									<br />
									<span class="id">{{ entry_end_id }}</span>
									<span class="page">{{ entry_end_index }}</span>
									<br />
									<input type="text" name="max" value="{{ max }}" />
								</div>
							</div>
							<div class="form-group" style="margin:0px;">
								<div class="col-sm-12">
									<a onclick="downloadData();" class="btn btn-primary"><span>{{ button_export }}</span></a>
								</div>
							</div>
						</form>
					</div>

					<div class="tab-pane" id="tab-import">
						<form action="{{ import }}" method="post" enctype="multipart/form-data" id="import" class="form-horizontal">
							<div class="form-group" style="margin:0px;">
								<div class="col-sm-12">
									{{ entry_import }}
									<span class="help">{{ help_import }}</span>
									<span class="help">{{ help_format }}</span>
								</div>
							</div>
							<div class="form-group" style="margin:0px;">
								<div class="col-sm-12">
									{{ entry_incremental }}
								</div>
								<div class="col-sm-12">
									{% if (incremental) %}
									<label class="radio-inline">
										<input type="radio" name="incremental" value="1" checked="checked" />
										{{ text_yes }} {{ help_incremental_yes }}
									</label>
									<br />
									<label class="radio-inline">
										<input type="radio" name="incremental" value="0" />
										{{ text_no }} {{ help_incremental_no }}
									</label>
									{% else %}
									<label class="radio-inline">
										<input type="radio" name="incremental" value="1" />
										{{ text_yes }} {{ help_incremental_yes }}
									</label>
									<br />
									<label class="radio-inline">
										<input type="radio" name="incremental" value="0" checked="checked" />
										{{ text_no }} {{ help_incremental_no }}
									</label>
									{% endif %}
								</div>
							</div>
							<div class="form-group" style="margin:0px;">
								<div class="col-sm-12">
									{{ entry_upload }}<br /><br />
									<input type="file" name="upload" id="upload" />
								</div>
							</div>
							<div class="form-group" style="margin:0px;">
								<div class="col-sm-12">
									<a onclick="uploadData();" class="btn btn-primary"><span>{{ button_import }}</span></a>
								</div>
							</div>
						</form>
					</div>

					<div class="tab-pane" id="tab-settings">
						<form action="{{ settings }}" method="post" enctype="multipart/form-data" id="settings" class="form-horizontal">
							<div class="form-group" style="margin:0px;">
								<label class="radio-inline">
									{% if (settings_use_option_id) %}
									<input type="checkbox" name="export_import_settings_use_option_id" value="1" checked="checked" /> {{ entry_settings_use_option_id }}
									{% else %}
									<input type="checkbox" name="export_import_settings_use_option_id" value="1" /> {{ entry_settings_use_option_id }}
									{% endif %}
								</label>
							</div>
							<div class="form-group" style="margin:0px;">
								<label class="radio-inline">
									{% if (settings_use_option_value_id) %}
									<input type="checkbox" name="export_import_settings_use_option_value_id" value="1" checked="checked" /> {{ entry_settings_use_option_value_id }}
									{% else %}
									<input type="checkbox" name="export_import_settings_use_option_value_id" value="1" /> {{ entry_settings_use_option_value_id }}
									{% endif %}
								</label>
							</div>
							<div class="form-group" style="margin:0px;">
								<label class="radio-inline">
									{% if (settings_use_attribute_group_id) %}
									<input type="checkbox" name="export_import_settings_use_attribute_group_id" value="1" checked="checked" /> {{ entry_settings_use_attribute_group_id }}
									{% else %}
									<input type="checkbox" name="export_import_settings_use_attribute_group_id" value="1" /> {{ entry_settings_use_attribute_group_id }}
									{% endif %}
								</label>
							</div>
							<div class="form-group" style="margin:0px;">
								<label class="radio-inline">
									{% if (settings_use_attribute_id) %}
									<input type="checkbox" name="export_import_settings_use_attribute_id" value="1" checked="checked" /> {{ entry_settings_use_attribute_id }}
									{% else %}
									<input type="checkbox" name="export_import_settings_use_attribute_id" value="1" /> {{ entry_settings_use_attribute_id }}
									{% endif %}
								</label>
							</div>
							{% if (exist_filter) %}
							<div class="form-group" style="margin:0px;">
								<label class="radio-inline">
									{% if (settings_use_filter_group_id) %}
									<input type="checkbox" name="export_import_settings_use_filter_group_id" value="1" checked="checked" /> {{ entry_settings_use_filter_group_id }}
									{% else %}
									<input type="checkbox" name="export_import_settings_use_filter_group_id" value="1" /> {{ entry_settings_use_filter_group_id }}
									{% endif %}
								</label>
							</div>
							<div class="form-group" style="margin:0px;">
								<label class="radio-inline">
									{% if (settings_use_filter_id) %}
									<input type="checkbox" name="export_import_settings_use_filter_id" value="1" checked="checked" /> {{ entry_settings_use_filter_id }}
									{% else %}
									<input type="checkbox" name="export_import_settings_use_filter_id" value="1" /> {{ entry_settings_use_filter_id }}
									{% endif %}
								</label>
							</div>
							{% endif %}
							<div class="form-group" style="margin:0px;">
								<div class="col-sm-12">
									<a onclick="updateSettings();" class="btn btn-primary"><span>{{ button_settings }}</span></a>
								</div>
							</div>
						</form>
					</div>

					<div class="tab-pane" id="tab-support">
						<div id="export_import_notification" class="alert alert-info"><i class="fa fa-info-circle"></i>
							<div id="export_import_loading"><img src="view/image/export-import/loading.gif" />{{ text_loading_notifications }}</div>
							<button type="button" class="close" data-dismiss="alert">&times;</button>
						</div>
						<table class="table table-bordered table-hover">
							<tbody>
								<tr>
									<td class="text-nowrap">{{ entry_version }}</td>
									<td>{{ version_export_import }}</td>
								</tr>
								<tr>
									<td class="text-nowrap">{{ entry_oc_version }}</td>
									<td>{{ version_opencart }}</td>
								</tr>
								<tr>
									<td class="text-nowrap">{{ entry_php_version }}</td>
									<td>{{ version_php }}</td>
								</tr>
								<tr>
									<td class="text-nowrap" style="vertical-align:top;">{{ entry_license }}</td>
									<td class="text-wrap">{{ text_license }}</td>
								</tr>
							</tbody>
						</table>
					</div>

				</div>
			</div>
		</div>

	</div>

<script type="text/javascript"><!--

function getNotifications() {
	$('#export_import_notification').html('<i class="fa fa-info-circle"></i><button type="button" class="close" data-dismiss="alert">&times;</button> <div id="export_import_loading"><img src="view/image/export-import/loading.gif" />{{ text_loading_notifications }}</div>');
	setTimeout(
		function(){
			$.ajax({
				type: 'GET',
				url: 'index.php?route=extension/export_import/getNotifications&user_token={{ user_token }}',
				dataType: 'json',
				success: function(json) {
					if (json['error']) {
						$('#export_import_notification').html('<i class="fa fa-info-circle"></i><button type="button" class="close" data-dismiss="alert">&times;</button> '+json['error']+' <span style="cursor:pointer;font-weight:bold;text-decoration:underline;float:right;" onclick="getNotifications();">{{ text_retry }}</span>');
					} else if (json['message']) {
						$('#export_import_notification').html('<i class="fa fa-info-circle"></i><button type="button" class="close" data-dismiss="alert">&times;</button> '+json['message']);
					} else {
						$('#export_import_notification').html('<i class="fa fa-info-circle"></i><button type="button" class="close" data-dismiss="alert">&times;</button> '+'{{ error_no_news }}');
					}
				},
				failure: function(){
					$('#export_import_notification').html('<i class="fa fa-info-circle"></i><button type="button" class="close" data-dismiss="alert">&times;</button> '+'{{ error_notifications }} <span style="cursor:pointer;font-weight:bold;text-decoration:underline;float:right;" onclick="getNotifications();">{{ text_retry }}</span>');
				},
				error: function() {
					$('#export_import_notification').html('<i class="fa fa-info-circle"></i><button type="button" class="close" data-dismiss="alert">&times;</button> '+'{{ error_notifications }} <span style="cursor:pointer;font-weight:bold;text-decoration:underline;float:right;" onclick="getNotifications();">{{ text_retry }}</span>');
				}
			});
		},
		500
	);
}

function check_export_filter(export_type) {
	if (export_type=='p') {
		$('#export_filter').show();
	} else {
		$('#export_filter').hide();
	}
}

function check_range_type(export_type) {
	if ((export_type=='p') || (export_type=='c') || (export_type=='u')) {
		$('#range_type').show();
		$('#range_type_id').prop('checked',true);
		$('#range_type_page').prop('checked',false);
		$('.id').show();
		$('.page').hide();
	} else {
		$('#range_type').hide();
	}
}

$(document).ready(function() {

	check_export_filter($('input[name=export_type]:checked').val());
	check_range_type($('input[name=export_type]:checked').val());

	$("#range_type_id").click(function() {
		$(".page").hide();
		$(".id").show();
	});

	$("#range_type_page").click(function() {
		$(".id").hide();
		$(".page").show();
	});

	$('input[name=export_type]').click(function() {
		check_export_filter($(this).val());
		check_range_type($(this).val());
	});

	$('span.close').click(function() {
		$(this).parent().remove();
	});

	$('a[data-toggle="tab"]').click(function() {
		$('#export_import_welcome').remove();
	});

	$('a[href="#tab-support"').click(function() {
		if ($(this).parent().attr('class')!='active') {
			getNotifications();
		}
	});

});

function checkFileSize(id) {
	// See also http://stackoverflow.com/questions/3717793/javascript-file-upload-size-validation for details
	var input, file, file_size;

	if (!window.FileReader) {
		// The file API isn't yet supported on user's browser
		return true;
	}

	input = document.getElementById(id);
	if (!input) {
		// couldn't find the file input element
		return true;
	}
	else if (!input.files) {
		// browser doesn't seem to support the `files` property of file inputs
		return true;
	}
	else if (!input.files[0]) {
		// no file has been selected for the upload
		alert( "{{ error_select_file }}" );
		return false;
	}
	else {
		file = input.files[0];
		file_size = file.size;
		{% if ((post_max_size is defined) and (post_max_size)) %}
		// check against PHP's post_max_size
		post_max_size = {{ post_max_size }};
		if (file_size > post_max_size) {
			alert( "{{ error_post_max_size }}" );
			return false;
		}
		{% endif %}
		{% if ((upload_max_filesize is defined) and (upload_max_filesize)) %}
		// check against PHP's upload_max_filesize
		upload_max_filesize = {{ upload_max_filesize }};
		if (file_size > upload_max_filesize) {
			alert( "{{ error_upload_max_filesize }}" );
			return false;
		}
		{% endif %}
		return true;
	}
}

function uploadData() {
	if (checkFileSize('upload')) {
		$('#import').submit();
	}
}

function isNumber(txt){ 
	var regExp=/^[\d]{1,}$/;
	return regExp.test(txt); 
}

count_product = {{ count_product }};

function updateCountProducts() {
	$.ajax({
		url: 'index.php?route=extension/export_import/getCountProduct&user_token={{ user_token }}',
		type: 'post',
		dataType: 'json',
		data: $("input[name='categories[]'],input[name='manufacturers[]']").serialize(),
		success: function(json) {
			if (json['count']) {
				count_product = json['count'];
			} else {
			}
		},
		error: function(xhr, ajaxOptions, thrownError) {
			alert(thrownError + "\r\n" + xhr.statusText + "\r\n" + xhr.responseText);
		}
	});
}

function validateExportForm(id) {
	var export_type = $('input[name=export_type]:checked').val();
	if ((export_type!='c') && (export_type!='p') && (export_type!='u')) {
		return true;
	}

	var val = $("input[name=range_type]:checked").val();
	var min = $("input[name=min]").val();
	var max = $("input[name=max]").val();

	if ((min=='') && (max=='')) {
		return true;
	}

	if (!isNumber(min) || !isNumber(max)) {
		alert("{{ error_param_not_number }}");
		return false;
	}

	var count_item;
	switch (export_type) {
		case 'p': count_item = count_product-1;  break;
		case 'c': count_item = {{ count_category-1 }}; break;
		default:  count_item = {{ count_customer-1 }}; break;
	}
	var batchNo = parseInt(count_item/parseInt(min))+1; // Maximum number of item-batches, namely, item number/min, and then rounded up (that is, integer plus 1)
	var minItemId;
	switch (export_type) {
		case 'p': minItemId = parseInt( {{ min_product_id }} );  break;
		case 'c': minItemId = parseInt( {{ min_category_id }} ); break;
		default:  minItemId = parseInt( {{ min_customer_id }} ); break;
	
	}
	var maxItemId;
	switch (export_type) {
		case 'p': maxItemId = parseInt( {{ max_product_id }} );  break;
		case 'c': maxItemId = parseInt( {{ max_category_id }} ); break;
		default:  maxItemId = parseInt( {{ max_customer_id }} ); break;
	
	}

	if (val=="page") {  // Min for the batch size, Max for the batch number
		if (parseInt(max) <= 0) {
			alert("{{ error_batch_number }}");
			return false;
		}
		if (parseInt(max) > batchNo) {        
			alert("{{ error_page_no_data }}"); 
			return false;
		} else {
			$("input[name=max]").val(parseInt(max)+1);
		}
	} else {
		if (minItemId <= 0) {
			alert("{{ error_min_item_id }}");
			return false;
		}
		if (parseInt(min) > maxItemId || parseInt(max) < minItemId || parseInt(min) > parseInt(max)) {  
			alert("{{ error_id_no_data }}"); 
			return false;
		}
	}
	return true;
}

function downloadData() {
	if (validateExportForm('export')) {
		$('#export').submit();
	}
}

function updateSettings() {
	$('#settings').submit();
}

// Category
$('input[name=\'category\']').autocomplete({
	'source': function(request, response) {
		$.ajax({
			url: 'index.php?route=catalog/category/autocomplete&user_token={{ user_token }}&filter_name=' +  encodeURIComponent(request),
			dataType: 'json',
			success: function(json) {
				response($.map(json, function(item) {
					return {
						label: item['name'],
						value: item['category_id']
					}
				}));
			}
		});
	},
	'select': function(item) {
		$('input[name=\'category\']').val('');
		$('#category' + item['value']).remove();
		$('#categories').append('<div id="category' + item['value'] + '"><i class="fa fa-minus-circle"></i> ' + item['label'] + '<input type="hidden" name="categories[]" value="' + item['value'] + '" /></div>');
		updateCountProducts();
	}
});

$('#categories').delegate('.fa-minus-circle', 'click', function() {
	$(this).parent().remove();
	updateCountProducts();
});

// Manufacturer
$('input[name=\'manufacturer\']').autocomplete({
	'source': function(request, response) {
		$.ajax({
			url: 'index.php?route=catalog/manufacturer/autocomplete&user_token={{ user_token }}&filter_name=' +  encodeURIComponent(request),
			dataType: 'json',
			success: function(json) {
				response($.map(json, function(item) {
					return {
						label: item['name'],
						value: item['manufacturer_id']
					}
				}));
			}
		});
	},
	'select': function(item) {
		$('input[name=\'manufacturer\']').val('');
		$('#manufacturer' + item['value']).remove();
		$('#manufacturers').append('<div id="manufacturer' + item['value'] + '"><i class="fa fa-minus-circle"></i> ' + item['label'] + '<input type="hidden" name="manufacturers[]" value="' + item['value'] + '" /></div>');
		updateCountProducts();
	}
});

$('#manufacturers').delegate('.fa-minus-circle', 'click', function() {
	$(this).parent().remove();
	updateCountProducts();
});
//--></script>

</div>
{{ footer }}
