{{ header }}
<div class="container" id="checkout-cart">
  {% if attention %}
    <div class="alert alert-info alert-dismissible" role="alert">
      <i class="fa fa-info-circle"></i> {{ attention }}
      <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
  {% endif %}
  {% if success %}
    <div class="alert alert-success alert-dismissible" role="alert">
      <i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
  {% endif %}
  {% if error_warning %}
    <div class="alert alert-danger alert-dismissible" role="alert">
      <i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
  {% endif %}
  <div class="row">
    {{ column_left }}
    {% if column_left and column_right %}
      {% set class = 'col-md-6' %}
    {% elseif column_left or column_right %}
      {% set class = 'col-md-9' %}
    {% else %}
      {% set class = 'col-md-12' %}
    {% endif %}
    <div class="{{ class }}" id="content">
      {{ content_top }}
      <h1>{{ heading_title }}
        {% if weight %}
          &nbsp;({{ weight }})
        {% endif %}
      </h1>
      <form action="{{ action }}" method="post" enctype="multipart/form-data">
        <div class="table-responsive">
          <table class="table table-bordered">
            <thead>
              <tr>
                <th class="text-center">{{ column_image }}</th>
                <th class="text-start">{{ column_name }}</th>
                <th class="text-start">{{ column_quantity }}</th>
                <th class="text-end">{{ column_price }}</th>
                <th class="text-end">{{ column_total }}</th>
              </tr>
            </thead>
            <tbody>
              {% for product in products %}
                <tr>
                  <td class="text-center">
                    {% if product.thumb %}
                      <a href="{{ product.href }}"><img src="{{ product.thumb }}" alt="{{ product.name }}" title="{{ product.name }}" class="img-thumbnail"/></a>
                    {% endif %}
                  </td>
                  <td class="text-start"><a href="{{ product.href }}">{{ product.name }}</a>
                                      <br>
                    <small> - {{ text_unit }}: {{ product.unit }}</small>
                    {% if not product.stock %}
                      <span class="text-danger">***</span>
                    {% endif %}
                    {% if product.option %}
                      {% for option in product.option %}
                        <br/>
                        <small>{{ option.name }}: {{ option.value }}</small>
                      {% endfor %}
                    {% endif %}
                    {% if product.reward %}
                      <br/>
                      <small>{{ product.reward }}</small>
                    {% endif %}
                  </td>
                  <td class="text-center">
                    <div class="input-group">
                      <input style="min-width: 80px;text-align: center;" type="text" name="quantity[{{ product.cart_id }}]" value="{{ product.quantity }}" size="1" class="form-control"/>
                      <button type="submit" data-bs-toggle="tooltip" title="{{ button_update }}" class="btn btn-primary"><i class="fa fa-refresh"></i></button>
                      <button type="button" data-bs-toggle="tooltip" title="{{ button_remove }}" class="btn btn-danger" onclick="cart.remove('{{ product.cart_id }}');"><i class="fa fa-times-circle"></i></button>
                    </div>
                  </td>
                  <td class="text-end">{{ product.price }}</td>
                  <td class="text-end">{{ product.total }}</td>
                </tr>
              {% endfor %}
              {% for voucher in vouchers %}
                <tr>
                  <td></td>
                  <td class="text-start">{{ voucher.description }}</td>
                  <td class="text-start"></td>
                  <td class="text-start">
                    <div class="input-group">
                      <input type of="text" name="" value="1" size="1" disabled class="form-control"/>
                      <button type="button" data-bs-toggle="tooltip" title="{{ button_remove }}" class="btn btn-danger" onclick="voucher.remove('{{ voucher.key }}');"><i class="fa fa-times-circle"></i></button>
                    </div>
                  </td>
                  <td class="text-end">{{ voucher.amount }}</td>
                  <td class="text-end">{{ voucher.amount }}</td>
                </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </form>

      <div class="row">
        <div class="col-md-4 offset-md-8">
          <table class="table table-bordered">
            {% for total in totals %}
              <tr>
                <td class="text-end"><strong>{{ total.title }}:</strong></td>
                <td class="text-end">{{ total.text }}</td>
              </tr>
            {% endfor %}
          </table>
        </div>
      </div>
      <div class="buttons clearfix">
        <div class="text-center"><div  style="cursor: pointer;color:#000 !important;font-size:12px;width: 100%;display: block;height: 45px;line-height: 35px;"  class="floatcart btn btn-warning bold-btn">{{ button_checkout }}</div></div>
      </div>
      {{ content_bottom }}
    </div>
    {{ column_right }}
  </div>
</div>
{{ footer }}
