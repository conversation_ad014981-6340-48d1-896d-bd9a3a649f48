<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/expr/v1alpha1/checked.proto

namespace Google\Api\Expr\V1alpha1\Type;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * List type with typed elements, e.g. `list<example.proto.MyMessage>`.
 *
 * Generated from protobuf message <code>google.api.expr.v1alpha1.Type.ListType</code>
 */
class ListType extends \Google\Protobuf\Internal\Message
{
    /**
     * The element type.
     *
     * Generated from protobuf field <code>.google.api.expr.v1alpha1.Type elem_type = 1;</code>
     */
    private $elem_type = null;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type \Google\Api\Expr\V1alpha1\Type $elem_type
     *           The element type.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Api\Expr\V1Alpha1\Checked::initOnce();
        parent::__construct($data);
    }

    /**
     * The element type.
     *
     * Generated from protobuf field <code>.google.api.expr.v1alpha1.Type elem_type = 1;</code>
     * @return \Google\Api\Expr\V1alpha1\Type
     */
    public function getElemType()
    {
        return $this->elem_type;
    }

    /**
     * The element type.
     *
     * Generated from protobuf field <code>.google.api.expr.v1alpha1.Type elem_type = 1;</code>
     * @param \Google\Api\Expr\V1alpha1\Type $var
     * @return $this
     */
    public function setElemType($var)
    {
        GPBUtil::checkMessage($var, \Google\Api\Expr\V1alpha1\Type::class);
        $this->elem_type = $var;

        return $this;
    }

}

// Adding a class alias for backwards compatibility with the previous class name.
class_alias(ListType::class, \Google\Api\Expr\V1alpha1\Type_ListType::class);

