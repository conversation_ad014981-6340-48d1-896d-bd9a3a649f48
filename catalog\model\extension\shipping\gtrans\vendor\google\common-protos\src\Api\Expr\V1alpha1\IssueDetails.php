<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/expr/v1alpha1/conformance_service.proto

namespace Google\Api\Expr\V1alpha1;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Warnings or errors in service execution are represented by
 * [google.rpc.Status][google.rpc.Status] messages, with the following message
 * in the details field.
 *
 * Generated from protobuf message <code>google.api.expr.v1alpha1.IssueDetails</code>
 */
class IssueDetails extends \Google\Protobuf\Internal\Message
{
    /**
     * The severity of the issue.
     *
     * Generated from protobuf field <code>.google.api.expr.v1alpha1.IssueDetails.Severity severity = 1;</code>
     */
    private $severity = 0;
    /**
     * Position in the source, if known.
     *
     * Generated from protobuf field <code>.google.api.expr.v1alpha1.SourcePosition position = 2;</code>
     */
    private $position = null;
    /**
     * Expression ID from [Expr][google.api.expr.v1alpha1.Expr], 0 if unknown.
     *
     * Generated from protobuf field <code>int64 id = 3;</code>
     */
    private $id = 0;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $severity
     *           The severity of the issue.
     *     @type \Google\Api\Expr\V1alpha1\SourcePosition $position
     *           Position in the source, if known.
     *     @type int|string $id
     *           Expression ID from [Expr][google.api.expr.v1alpha1.Expr], 0 if unknown.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Api\Expr\V1Alpha1\ConformanceService::initOnce();
        parent::__construct($data);
    }

    /**
     * The severity of the issue.
     *
     * Generated from protobuf field <code>.google.api.expr.v1alpha1.IssueDetails.Severity severity = 1;</code>
     * @return int
     */
    public function getSeverity()
    {
        return $this->severity;
    }

    /**
     * The severity of the issue.
     *
     * Generated from protobuf field <code>.google.api.expr.v1alpha1.IssueDetails.Severity severity = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setSeverity($var)
    {
        GPBUtil::checkEnum($var, \Google\Api\Expr\V1alpha1\IssueDetails_Severity::class);
        $this->severity = $var;

        return $this;
    }

    /**
     * Position in the source, if known.
     *
     * Generated from protobuf field <code>.google.api.expr.v1alpha1.SourcePosition position = 2;</code>
     * @return \Google\Api\Expr\V1alpha1\SourcePosition
     */
    public function getPosition()
    {
        return $this->position;
    }

    /**
     * Position in the source, if known.
     *
     * Generated from protobuf field <code>.google.api.expr.v1alpha1.SourcePosition position = 2;</code>
     * @param \Google\Api\Expr\V1alpha1\SourcePosition $var
     * @return $this
     */
    public function setPosition($var)
    {
        GPBUtil::checkMessage($var, \Google\Api\Expr\V1alpha1\SourcePosition::class);
        $this->position = $var;

        return $this;
    }

    /**
     * Expression ID from [Expr][google.api.expr.v1alpha1.Expr], 0 if unknown.
     *
     * Generated from protobuf field <code>int64 id = 3;</code>
     * @return int|string
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * Expression ID from [Expr][google.api.expr.v1alpha1.Expr], 0 if unknown.
     *
     * Generated from protobuf field <code>int64 id = 3;</code>
     * @param int|string $var
     * @return $this
     */
    public function setId($var)
    {
        GPBUtil::checkInt64($var);
        $this->id = $var;

        return $this;
    }

}

