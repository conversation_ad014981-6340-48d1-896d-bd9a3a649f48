<?php
// Text
$_['text_title']						= 'Credit Card / Debit Card (Cardinity)';
$_['text_payment_success']				= 'Payment success, details below';
$_['text_payment_failed']				= 'Payment failed, details below';

// Entry
$_['entry_holder']						= 'Card Holder Name';
$_['entry_pan']							= 'Card Number';
$_['entry_expires']						= 'Expires';
$_['entry_exp_month']					= 'Month';
$_['entry_exp_year']					= 'Year';
$_['entry_cvc']							= 'CVC';

// Error
$_['error_process_order']				= 'There was an error processing your order. Please contact the shop administrator for help.';
$_['error_invalid_currency']			= 'Please use a valid currency.';
$_['error_finalizing_payment']			= 'Error finalizing payment.';
$_['error_unknown_order_id']			= 'Could not find cardinity payment with this order_id.';
$_['error_invalid_hash']				= 'Invalid hash.';
$_['error_payment_declined']			= 'Payment was declined by issuing bank.';

// Button
$_['button_confirm']					= 'Pay Now';