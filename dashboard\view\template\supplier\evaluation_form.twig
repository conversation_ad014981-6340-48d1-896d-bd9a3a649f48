{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="submit" form="supplier\evaluation-form" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fas fa-save"></i></button>
        <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-light"><i class="fas fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    
    <div class="card">
      <div class="card-header"><i class="fas fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}chart-bar{% elif screen_type == 'settings' %}cog{% else %}pencil-alt{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="card-body">
        <form id="supplier\evaluation-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-add">{{ text_add }}</label>
            <div class="col-sm-10">
              <input type="text" name="add" value="{{ add }}" placeholder="{{ text_add }}" id="input-add" class="form-control" />
              {% if error_add %}
                <div class="invalid-feedback">{{ error_add }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-comments">{{ text_comments }}</label>
            <div class="col-sm-10">
              <input type="text" name="comments" value="{{ comments }}" placeholder="{{ text_comments }}" id="input-comments" class="form-control" />
              {% if error_comments %}
                <div class="invalid-feedback">{{ error_comments }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-delete">{{ text_delete }}</label>
            <div class="col-sm-10">
              <input type="text" name="delete" value="{{ delete }}" placeholder="{{ text_delete }}" id="input-delete" class="form-control" />
              {% if error_delete %}
                <div class="invalid-feedback">{{ error_delete }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-delivery_score">{{ text_delivery_score }}</label>
            <div class="col-sm-10">
              <input type="text" name="delivery_score" value="{{ delivery_score }}" placeholder="{{ text_delivery_score }}" id="input-delivery_score" class="form-control" />
              {% if error_delivery_score %}
                <div class="invalid-feedback">{{ error_delivery_score }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_delivery_score">{{ text_error_delivery_score }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_delivery_score" value="{{ error_delivery_score }}" placeholder="{{ text_error_delivery_score }}" id="input-error_delivery_score" class="form-control" />
              {% if error_error_delivery_score %}
                <div class="invalid-feedback">{{ error_error_delivery_score }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_evaluation_date">{{ text_error_evaluation_date }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_evaluation_date" value="{{ error_evaluation_date }}" placeholder="{{ text_error_evaluation_date }}" id="input-error_evaluation_date" class="form-control" />
              {% if error_error_evaluation_date %}
                <div class="invalid-feedback">{{ error_error_evaluation_date }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_price_score">{{ text_error_price_score }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_price_score" value="{{ error_price_score }}" placeholder="{{ text_error_price_score }}" id="input-error_price_score" class="form-control" />
              {% if error_error_price_score %}
                <div class="invalid-feedback">{{ error_error_price_score }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_quality_score">{{ text_error_quality_score }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_quality_score" value="{{ error_quality_score }}" placeholder="{{ text_error_quality_score }}" id="input-error_quality_score" class="form-control" />
              {% if error_error_quality_score %}
                <div class="invalid-feedback">{{ error_error_quality_score }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_service_score">{{ text_error_service_score }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_service_score" value="{{ error_service_score }}" placeholder="{{ text_error_service_score }}" id="input-error_service_score" class="form-control" />
              {% if error_error_service_score %}
                <div class="invalid-feedback">{{ error_error_service_score }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_supplier_id">{{ text_error_supplier_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_supplier_id" value="{{ error_supplier_id }}" placeholder="{{ text_error_supplier_id }}" id="input-error_supplier_id" class="form-control" />
              {% if error_error_supplier_id %}
                <div class="invalid-feedback">{{ error_error_supplier_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-evaluation_date">{{ text_evaluation_date }}</label>
            <div class="col-sm-10">
              <input type="text" name="evaluation_date" value="{{ evaluation_date }}" placeholder="{{ text_evaluation_date }}" id="input-evaluation_date" class="form-control" />
              {% if error_evaluation_date %}
                <div class="invalid-feedback">{{ error_evaluation_date }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-evaluation_history">{{ text_evaluation_history }}</label>
            <div class="col-sm-10">
              <input type="text" name="evaluation_history" value="{{ evaluation_history }}" placeholder="{{ text_evaluation_history }}" id="input-evaluation_history" class="form-control" />
              {% if error_evaluation_history %}
                <div class="invalid-feedback">{{ error_evaluation_history }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-evaluations">{{ text_evaluations }}</label>
            <div class="col-sm-10">
              <input type="text" name="evaluations" value="{{ evaluations }}" placeholder="{{ text_evaluations }}" id="input-evaluations" class="form-control" />
              {% if error_evaluations %}
                <div class="invalid-feedback">{{ error_evaluations }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-evaluators">{{ text_evaluators }}</label>
            <div class="col-sm-10">
              <input type="text" name="evaluators" value="{{ evaluators }}" placeholder="{{ text_evaluators }}" id="input-evaluators" class="form-control" />
              {% if error_evaluators %}
                <div class="invalid-feedback">{{ error_evaluators }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_date_end">{{ text_filter_date_end }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_date_end" value="{{ filter_date_end }}" placeholder="{{ text_filter_date_end }}" id="input-filter_date_end" class="form-control" />
              {% if error_filter_date_end %}
                <div class="invalid-feedback">{{ error_filter_date_end }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_date_start">{{ text_filter_date_start }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_date_start" value="{{ filter_date_start }}" placeholder="{{ text_filter_date_start }}" id="input-filter_date_start" class="form-control" />
              {% if error_filter_date_start %}
                <div class="invalid-feedback">{{ error_filter_date_start }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_evaluator_id">{{ text_filter_evaluator_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_evaluator_id" value="{{ filter_evaluator_id }}" placeholder="{{ text_filter_evaluator_id }}" id="input-filter_evaluator_id" class="form-control" />
              {% if error_filter_evaluator_id %}
                <div class="invalid-feedback">{{ error_filter_evaluator_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-filter_supplier_id">{{ text_filter_supplier_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="filter_supplier_id" value="{{ filter_supplier_id }}" placeholder="{{ text_filter_supplier_id }}" id="input-filter_supplier_id" class="form-control" />
              {% if error_filter_supplier_id %}
                <div class="invalid-feedback">{{ error_filter_supplier_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="invalid-feedback">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-order">{{ text_order }}</label>
            <div class="col-sm-10">
              <input type="text" name="order" value="{{ order }}" placeholder="{{ text_order }}" id="input-order" class="form-control" />
              {% if error_order %}
                <div class="invalid-feedback">{{ error_order }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-pagination">{{ text_pagination }}</label>
            <div class="col-sm-10">
              <input type="text" name="pagination" value="{{ pagination }}" placeholder="{{ text_pagination }}" id="input-pagination" class="form-control" />
              {% if error_pagination %}
                <div class="invalid-feedback">{{ error_pagination }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-price_score">{{ text_price_score }}</label>
            <div class="col-sm-10">
              <input type="text" name="price_score" value="{{ price_score }}" placeholder="{{ text_price_score }}" id="input-price_score" class="form-control" />
              {% if error_price_score %}
                <div class="invalid-feedback">{{ error_price_score }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-quality_score">{{ text_quality_score }}</label>
            <div class="col-sm-10">
              <input type="text" name="quality_score" value="{{ quality_score }}" placeholder="{{ text_quality_score }}" id="input-quality_score" class="form-control" />
              {% if error_quality_score %}
                <div class="invalid-feedback">{{ error_quality_score }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-results">{{ text_results }}</label>
            <div class="col-sm-10">
              <input type="text" name="results" value="{{ results }}" placeholder="{{ text_results }}" id="input-results" class="form-control" />
              {% if error_results %}
                <div class="invalid-feedback">{{ error_results }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-selected">{{ text_selected }}</label>
            <div class="col-sm-10">
              <input type="text" name="selected" value="{{ selected }}" placeholder="{{ text_selected }}" id="input-selected" class="form-control" />
              {% if error_selected %}
                <div class="invalid-feedback">{{ error_selected }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-service_score">{{ text_service_score }}</label>
            <div class="col-sm-10">
              <input type="text" name="service_score" value="{{ service_score }}" placeholder="{{ text_service_score }}" id="input-service_score" class="form-control" />
              {% if error_service_score %}
                <div class="invalid-feedback">{{ error_service_score }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort">{{ text_sort }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort" value="{{ sort }}" placeholder="{{ text_sort }}" id="input-sort" class="form-control" />
              {% if error_sort %}
                <div class="invalid-feedback">{{ error_sort }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_date">{{ text_sort_date }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_date" value="{{ sort_date }}" placeholder="{{ text_sort_date }}" id="input-sort_date" class="form-control" />
              {% if error_sort_date %}
                <div class="invalid-feedback">{{ error_sort_date }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_evaluator">{{ text_sort_evaluator }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_evaluator" value="{{ sort_evaluator }}" placeholder="{{ text_sort_evaluator }}" id="input-sort_evaluator" class="form-control" />
              {% if error_sort_evaluator %}
                <div class="invalid-feedback">{{ error_sort_evaluator }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_overall_score">{{ text_sort_overall_score }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_overall_score" value="{{ sort_overall_score }}" placeholder="{{ text_sort_overall_score }}" id="input-sort_overall_score" class="form-control" />
              {% if error_sort_overall_score %}
                <div class="invalid-feedback">{{ error_sort_overall_score }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_supplier">{{ text_sort_supplier }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_supplier" value="{{ sort_supplier }}" placeholder="{{ text_sort_supplier }}" id="input-sort_supplier" class="form-control" />
              {% if error_sort_supplier %}
                <div class="invalid-feedback">{{ error_sort_supplier }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-supplier_id">{{ text_supplier_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="supplier_id" value="{{ supplier_id }}" placeholder="{{ text_supplier_id }}" id="input-supplier_id" class="form-control" />
              {% if error_supplier_id %}
                <div class="invalid-feedback">{{ error_supplier_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-supplier_report">{{ text_supplier_report }}</label>
            <div class="col-sm-10">
              <input type="text" name="supplier_report" value="{{ supplier_report }}" placeholder="{{ text_supplier_report }}" id="input-supplier_report" class="form-control" />
              {% if error_supplier_report %}
                <div class="invalid-feedback">{{ error_supplier_report }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-suppliers">{{ text_suppliers }}</label>
            <div class="col-sm-10">
              <input type="text" name="suppliers" value="{{ suppliers }}" placeholder="{{ text_suppliers }}" id="input-suppliers" class="form-control" />
              {% if error_suppliers %}
                <div class="invalid-feedback">{{ error_suppliers }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_form">{{ text_text_form }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_form" value="{{ text_form }}" placeholder="{{ text_text_form }}" id="input-text_form" class="form-control" />
              {% if error_text_form %}
                <div class="invalid-feedback">{{ error_text_form }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_token">{{ text_user_token }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_token" value="{{ user_token }}" placeholder="{{ text_user_token }}" id="input-user_token" class="form-control" />
              {% if error_user_token %}
                <div class="invalid-feedback">{{ error_user_token }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}