<?php
/**
 * تحكم تقرير مراكز التكلفة الشامل والمتكامل
 * يجمع بين القوة والبساطة - يتفوق على SAP وOracle في سهولة الاستخدام
 * مع الاحتفاظ بالقوة والمرونة المطلوبة للشركات الكبيرة
 * متوافق مع معايير المحاسبة الإدارية المصرية ونظم التكاليف
 */
class ControllerAccountsCostCenterReport extends Controller {
    private $error = array();
    private $central_service;

    public function __construct($registry) {
        parent::__construct($registry);
        
        // تحميل الخدمات المركزية
        $this->load->model('core/central_service_manager');
        $this->central_service = $this->model_core_central_service_manager;
    }

    public function index() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/cost_center_report') || 
            !$this->user->hasKey('accounting_cost_center_report_view')) {
            
            $this->central_service->logActivity('unauthorized_access', 'accounts',
                $this->language->get('log_unauthorized_access_cost_center_report'), [
                'user_id' => $this->user->getId(),
                'ip_address' => $this->request->server['REMOTE_ADDR']
            ]);
            
            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/cost_center_report');
        $this->document->setTitle($this->language->get('heading_title'));

        // إضافة CSS و JavaScript المتقدم
        $this->document->addStyle('view/stylesheet/accounts/cost_center_report.css');
        $this->document->addScript('view/javascript/accounts/cost_center_report.js');
        $this->document->addScript('view/javascript/jquery/accounting.min.js');
        $this->document->addScript('view/javascript/jquery/select2.min.js');
        $this->document->addStyle('view/javascript/jquery/select2.min.css');
        $this->document->addScript('view/javascript/jquery/daterangepicker.min.js');
        $this->document->addStyle('view/javascript/jquery/daterangepicker.css');
        $this->document->addScript('view/javascript/jquery/chart.min.js');
        $this->document->addScript('view/javascript/jquery/datatables.min.js');
        $this->document->addStyle('view/javascript/jquery/datatables.min.css');

        // تسجيل الوصول للشاشة
        $this->central_service->logActivity('view', 'accounts',
            $this->language->get('log_view_cost_center_report_screen'), [
            'user_id' => $this->user->getId(),
            'screen' => 'accounts/cost_center_report'
        ]);

        $this->getForm();
    }

    /**
     * توليد تقرير مراكز التكلفة المتقدم
     */
    public function generate() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/cost_center_report') || 
            !$this->user->hasKey('accounting_cost_center_report_generate')) {
            
            $this->central_service->logActivity('unauthorized_generate', 'accounts',
                $this->language->get('log_unauthorized_generate_cost_center_report'), [
                'user_id' => $this->user->getId(),
                'action' => 'generate_cost_center_report'
            ]);
            
            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/cost_center_report');
        $this->load->model('accounts/cost_center_report');

        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm()) {
            try {
                $filter_data = $this->prepareFilterData();

                // تسجيل توليد التقرير
                $this->central_service->logActivity('generate_report', 'accounts',
                    $this->language->get('log_generate_cost_center_report_period') . ': ' . $filter_data['date_start'] . ' ' . $this->language->get('text_to') . ' ' . $filter_data['date_end'], [
                    'user_id' => $this->user->getId(),
                    'date_start' => $filter_data['date_start'],
                    'date_end' => $filter_data['date_end'],
                    'cost_center_id' => $filter_data['cost_center_id'] ?? 'all'
                ]);

                $cost_center_data = $this->model_accounts_cost_center_report->generateCostCenterReport($filter_data);

                // إرسال إشعار للمدير المالي
                $this->central_service->sendNotification(
                    'cost_center_report_generated', 
                    'توليد تقرير مراكز التكلفة', 
                    'تم توليد تقرير مراكز التكلفة للفترة ' . $filter_data['date_start'] . ' إلى ' . $filter_data['date_end'] . ' بواسطة ' . $this->user->getFirstName(), 
                    [$this->config->get('config_financial_manager_id')], 
                    [
                        'period' => $filter_data['date_start'] . ' - ' . $filter_data['date_end'],
                        'user_name' => $this->user->getFirstName() . ' ' . $this->user->getLastName(),
                        'total_cost' => $cost_center_data['totals']['total_cost'] ?? 0
                    ]
                );

                $this->session->data['success'] = $this->language->get('text_success_generate');
                $this->session->data['cost_center_report_data'] = $cost_center_data;

                // إعادة توجيه حسب الإجراء المطلوب
                if (isset($this->request->post['generate_and_new'])) {
                    $this->response->redirect($this->url->link('accounts/cost_center_report', 'user_token=' . $this->session->data['user_token'], true));
                } elseif (isset($this->request->post['generate_and_export'])) {
                    $this->response->redirect($this->url->link('accounts/cost_center_report/export', 'format=excel&user_token=' . $this->session->data['user_token'], true));
                } elseif (isset($this->request->post['generate_and_profitability'])) {
                    $this->response->redirect($this->url->link('accounts/cost_center_report/profitability_analysis', 'user_token=' . $this->session->data['user_token'], true));
                } else {
                    $this->response->redirect($this->url->link('accounts/cost_center_report/view', 'user_token=' . $this->session->data['user_token'], true));
                }
            } catch (Exception $e) {
                $this->error['warning'] = $e->getMessage();
            }
        }

        $this->getForm();
    }

    /**
     * عرض تقرير مراكز التكلفة
     */
    public function view() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/cost_center_report') || 
            !$this->user->hasKey('accounting_cost_center_report_view')) {
            
            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/cost_center_report');
        $this->document->setTitle($this->language->get('heading_title'));

        if (!isset($this->session->data['cost_center_report_data'])) {
            $this->session->data['error'] = $this->language->get('error_no_data');
            $this->response->redirect($this->url->link('accounts/cost_center_report', 'user_token=' . $this->session->data['user_token'], true));
            return;
        }

        $data = $this->session->data['cost_center_report_data'];
        
        // تسجيل عرض التقرير
        $this->central_service->logActivity('view_report', 'accounts', 
            'عرض تقرير مراكز التكلفة', [
            'user_id' => $this->user->getId(),
            'action' => 'view_cost_center_report'
        ]);

        $data['breadcrumbs'] = array();
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('accounts/cost_center_report', 'user_token=' . $this->session->data['user_token'], true)
        );

        $data['export_excel'] = $this->url->link('accounts/cost_center_report/export', 'format=excel&user_token=' . $this->session->data['user_token'], true);
        $data['export_pdf'] = $this->url->link('accounts/cost_center_report/export', 'format=pdf&user_token=' . $this->session->data['user_token'], true);
        $data['export_csv'] = $this->url->link('accounts/cost_center_report/export', 'format=csv&user_token=' . $this->session->data['user_token'], true);

        $data['user_token'] = $this->session->data['user_token'];
        $data['heading_title'] = $this->language->get('heading_title');

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/cost_center_report_view', $data));
    }

    /**
     * تحليل ربحية مراكز التكلفة
     */
    public function profitability_analysis() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/cost_center_report') || 
            !$this->user->hasKey('accounting_cost_center_profitability')) {
            
            $this->central_service->logActivity('unauthorized_profitability_analysis', 'accounts', 
                'محاولة تحليل ربحية مراكز تكلفة غير مصرح بها', [
                'user_id' => $this->user->getId(),
                'action' => 'profitability_analysis'
            ]);
            
            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/cost_center_report');
        $this->load->model('accounts/cost_center_report');

        if (!isset($this->session->data['cost_center_report_data'])) {
            $this->session->data['error'] = $this->language->get('error_no_data');
            $this->response->redirect($this->url->link('accounts/cost_center_report', 'user_token=' . $this->session->data['user_token'], true));
            return;
        }

        $cost_center_data = $this->session->data['cost_center_report_data'];
        $profitability_analysis = $this->model_accounts_cost_center_report->analyzeProfitability($cost_center_data);

        // تسجيل تحليل الربحية
        $this->central_service->logActivity('profitability_analysis', 'accounts', 
            'تحليل ربحية مراكز التكلفة', [
            'user_id' => $this->user->getId(),
            'action' => 'profitability_analysis',
            'profitable_centers' => count($profitability_analysis['profitable_centers'] ?? [])
        ]);

        $data = $profitability_analysis;
        $data['breadcrumbs'] = array();
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('accounts/cost_center_report', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_profitability_analysis'),
            'href' => $this->url->link('accounts/cost_center_report/profitability_analysis', 'user_token=' . $this->session->data['user_token'], true)
        );

        $data['user_token'] = $this->session->data['user_token'];
        $data['heading_title'] = $this->language->get('text_profitability_analysis');

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/cost_center_profitability_analysis', $data));
    }

    /**
     * تصدير تقرير مراكز التكلفة
     */
    public function export() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/cost_center_report') ||
            !$this->user->hasKey('accounting_cost_center_report_export')) {

            $this->central_service->logActivity('unauthorized_export', 'accounts',
                'محاولة تصدير تقرير مراكز تكلفة غير مصرح بها', [
                'user_id' => $this->user->getId(),
                'action' => 'export_cost_center_report'
            ]);

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/cost_center_report');
        $this->load->model('accounts/cost_center_report');

        if (!isset($this->session->data['cost_center_report_data'])) {
            $this->session->data['error'] = $this->language->get('error_no_data');
            $this->response->redirect($this->url->link('accounts/cost_center_report', 'user_token=' . $this->session->data['user_token'], true));
            return;
        }

        $format = $this->request->get['format'] ?? 'excel';
        $cost_center_data = $this->session->data['cost_center_report_data'];
        $filter_data = $this->session->data['cost_center_report_filter'] ?? array();

        // تسجيل عملية التصدير
        $this->central_service->logActivity('export', 'accounts',
            'تصدير تقرير مراكز التكلفة - ' . strtoupper($format), [
            'user_id' => $this->user->getId(),
            'format' => $format,
            'period' => ($filter_data['date_start'] ?? '') . ' - ' . ($filter_data['date_end'] ?? '')
        ]);

        // إرسال إشعار للمدير المالي
        $this->central_service->sendNotification(
            'cost_center_report_exported',
            'تصدير تقرير مراكز التكلفة',
            'تم تصدير تقرير مراكز التكلفة بصيغة ' . strtoupper($format) . ' بواسطة ' . $this->user->getFirstName(),
            [$this->config->get('config_financial_manager_id')],
            [
                'format' => $format,
                'period' => ($filter_data['date_start'] ?? '') . ' - ' . ($filter_data['date_end'] ?? ''),
                'user_name' => $this->user->getFirstName() . ' ' . $this->user->getLastName()
            ]
        );

        switch ($format) {
            case 'excel':
                $this->exportToExcel($cost_center_data, $filter_data);
                break;
            case 'pdf':
                $this->exportToPdf($cost_center_data, $filter_data);
                break;
            case 'csv':
                $this->exportToCsv($cost_center_data, $filter_data);
                break;
            default:
                $this->exportToExcel($cost_center_data, $filter_data);
        }
    }

    /**
     * التحقق من صحة البيانات
     */
    protected function validateForm() {
        if (!$this->user->hasPermission('access', 'accounts/cost_center_report')) {
            $this->error['warning'] = $this->language->get('error_permission');
        }

        if (empty($this->request->post['date_start'])) {
            $this->error['date_start'] = $this->language->get('error_date_start');
        }

        if (empty($this->request->post['date_end'])) {
            $this->error['date_end'] = $this->language->get('error_date_end');
        }

        if (!empty($this->request->post['date_start']) && !empty($this->request->post['date_end'])) {
            if (strtotime($this->request->post['date_start']) > strtotime($this->request->post['date_end'])) {
                $this->error['date_range'] = $this->language->get('error_date_range');
            }
        }

        return !$this->error;
    }

    /**
     * إعداد بيانات الفلترة
     */
    protected function prepareFilterData() {
        return array(
            'date_start' => $this->request->post['date_start'] ?: date('Y-01-01'),
            'date_end' => $this->request->post['date_end'] ?: date('Y-m-d'),
            'cost_center_id' => $this->request->post['cost_center_id'] ?? '',
            'department_id' => $this->request->post['department_id'] ?? '',
            'project_id' => $this->request->post['project_id'] ?? '',
            'cost_type' => $this->request->post['cost_type'] ?? 'all',
            'include_indirect_costs' => isset($this->request->post['include_indirect_costs']) ? 1 : 0,
            'allocation_method' => $this->request->post['allocation_method'] ?? 'direct',
            'show_profitability' => isset($this->request->post['show_profitability']) ? 1 : 0,
            'currency' => $this->request->post['currency'] ?? $this->config->get('config_currency'),
            'branch_id' => $this->request->post['branch_id'] ?? ''
        );
    }

    /**
     * عرض النموذج
     */
    protected function getForm() {
        $data['breadcrumbs'] = array();
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('accounts/cost_center_report', 'user_token=' . $this->session->data['user_token'], true)
        );

        $data['action'] = $this->url->link('accounts/cost_center_report/generate', 'user_token=' . $this->session->data['user_token'], true);

        // تحميل قوائم البيانات
        $this->load->model('cost_center/cost_center');
        $this->load->model('department/department');
        $this->load->model('project/project');
        $this->load->model('branch/branch');

        $data['cost_centers'] = $this->model_cost_center_cost_center->getCostCenters();
        $data['departments'] = $this->model_department_department->getDepartments();
        $data['projects'] = $this->model_project_project->getProjects();
        $data['branches'] = $this->model_branch_branch->getBranches();

        // القيم الافتراضية
        $data['date_start'] = $this->request->post['date_start'] ?? date('Y-01-01');
        $data['date_end'] = $this->request->post['date_end'] ?? date('Y-m-d');
        $data['cost_center_id'] = $this->request->post['cost_center_id'] ?? '';
        $data['include_indirect_costs'] = $this->request->post['include_indirect_costs'] ?? true;
        $data['allocation_method'] = $this->request->post['allocation_method'] ?? 'direct';
        $data['show_profitability'] = $this->request->post['show_profitability'] ?? true;

        $data['user_token'] = $this->session->data['user_token'];
        $data['heading_title'] = $this->language->get('heading_title');
        $data['text_no_results'] = $this->language->get('text_no_results');

        if (isset($this->error['warning'])) {
            $data['error_warning'] = $this->error['warning'];
        } else {
            $data['error_warning'] = '';
        }

        if (isset($this->session->data['success'])) {
            $data['success'] = $this->session->data['success'];
            unset($this->session->data['success']);
        } else {
            $data['success'] = '';
        }

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/cost_center_report_form', $data));
    }

    /**
     * تصدير إلى Excel
     */
    private function exportToExcel($data, $filter_data) {
        $filename = 'cost_center_report_' . date('Y-m-d') . '.xls';

        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        echo '<table border="1">';
        echo '<tr><th colspan="6">' . $this->language->get('heading_title') . '</th></tr>';
        echo '<tr><th>' . $this->language->get('text_cost_center') . '</th>';
        echo '<th>' . $this->language->get('text_direct_costs') . '</th>';
        echo '<th>' . $this->language->get('text_indirect_costs') . '</th>';
        echo '<th>' . $this->language->get('text_total_costs') . '</th>';
        echo '<th>' . $this->language->get('text_revenue') . '</th>';
        echo '<th>' . $this->language->get('text_profit_loss') . '</th></tr>';

        echo '</table>';
        exit;
    }

    /**
     * تصدير إلى PDF
     */
    private function exportToPdf($data, $filter_data) {
        require_once(DIR_SYSTEM . 'library/tcpdf/tcpdf.php');

        $pdf = new TCPDF('P', 'mm', 'A4', true, 'UTF-8');
        $pdf->SetCreator('AYM ERP System');
        $pdf->SetAuthor($this->config->get('config_name'));
        $pdf->SetTitle($this->language->get('heading_title'));

        $pdf->AddPage();
        $pdf->SetFont('dejavusans', 'B', 16);
        $pdf->Cell(0, 10, $this->language->get('heading_title'), 0, 1, 'C');

        $pdf->Output('cost_center_report_' . date('Y-m-d') . '.pdf', 'D');
        exit;
    }

    /**
     * تصدير إلى CSV
     */
    private function exportToCsv($data, $filter_data) {
        $filename = 'cost_center_report_' . date('Y-m-d') . '.csv';

        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $output = fopen('php://output', 'w');
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

        fputcsv($output, array(
            $this->language->get('text_cost_center'),
            $this->language->get('text_direct_costs'),
            $this->language->get('text_indirect_costs'),
            $this->language->get('text_total_costs'),
            $this->language->get('text_revenue'),
            $this->language->get('text_profit_loss')
        ));

        fclose($output);
        exit;
    }

    /**
     * لوحة معلومات مراكز التكلفة التفاعلية
     */
    public function dashboard() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/cost_center_report') ||
            !$this->user->hasKey('accounting_cost_center_dashboard')) {

            $this->central_service->logActivity('unauthorized_access', 'accounts',
                'محاولة وصول غير مصرح بها للوحة معلومات مراكز التكلفة', [
                'user_id' => $this->user->getId(),
                'ip_address' => $this->request->server['REMOTE_ADDR']
            ]);

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/cost_center_report');
        $this->document->setTitle($this->language->get('heading_title') . ' - ' . $this->language->get('text_dashboard'));
        $this->load->model('accounts/cost_center_report');

        // إضافة CSS و JavaScript المتقدم للوحة المعلومات
        $this->document->addStyle('view/stylesheet/accounts/cost_center_dashboard.css');
        $this->document->addScript('view/javascript/accounts/cost_center_dashboard.js');
        $this->document->addScript('view/javascript/jquery/chart.min.js');
        $this->document->addScript('view/javascript/jquery/datatables.min.js');
        $this->document->addStyle('view/javascript/jquery/datatables.min.css');

        // تسجيل الوصول للوحة المعلومات
        $this->central_service->logActivity('view_dashboard', 'accounts',
            'عرض لوحة معلومات مراكز التكلفة', [
            'user_id' => $this->user->getId()
        ]);

        // إعداد بيانات الفلترة الافتراضية (آخر 3 أشهر)
        $filter_data = array(
            'date_start' => date('Y-m-01', strtotime('-2 months')),
            'date_end' => date('Y-m-d'),
            'cost_center_id' => '',
            'department_id' => '',
            'include_indirect_costs' => 1,
            'allocation_method' => 'sales_based',
            'currency' => $this->config->get('config_currency')
        );

        try {
            // الحصول على بيانات التقرير الشامل
            $report_data = $this->model_accounts_cost_center_report->generateCostCenterReport($filter_data);

            // تحليل الربحية المتقدم
            $profitability_analysis = $this->model_accounts_cost_center_report->analyzeProfitability($report_data);

            // إعداد بيانات لوحة المعلومات
            $data['summary'] = $report_data['summary'];
            $data['summary']['revenue_achievement'] = 85; // مؤقت - يحسب من الموازنة
            $data['summary']['expense_control'] = 92; // مؤقت - يحسب من الموازنة
            $data['summary']['break_even_centers'] = $data['summary']['total_cost_centers'] -
                                                   $data['summary']['profitable_centers'] -
                                                   $data['summary']['loss_making_centers'];

            $data['cost_centers'] = $report_data['cost_centers'];
            $data['trends'] = $report_data['trends'];
            $data['recommendations'] = $report_data['recommendations'];

            // أفضل المؤدين (أعلى 5 مراكز ربحية)
            $data['top_performers'] = array_slice(
                array_filter($report_data['cost_centers'], function($center) {
                    return $center['net_profit'] > 0;
                }),
                0, 5
            );

            // المراكز التي تحتاج انتباه
            $data['attention_required'] = array();
            foreach ($report_data['cost_centers'] as $center) {
                if ($center['net_profit'] < 0) {
                    $data['attention_required'][] = array(
                        'cost_center_name' => $center['cost_center_name'],
                        'severity' => abs($center['net_profit']) > 50000 ? 'high' : 'medium',
                        'issue_description' => 'خسائر في مركز التكلفة',
                        'impact_percentage' => min(100, abs($center['net_profit']) / 1000)
                    );
                } elseif ($center['profit_margin'] < 5 && $center['profit_margin'] > 0) {
                    $data['attention_required'][] = array(
                        'cost_center_name' => $center['cost_center_name'],
                        'severity' => 'low',
                        'issue_description' => 'هامش ربح منخفض',
                        'impact_percentage' => $center['profit_margin'] * 10
                    );
                }
            }

            // رؤى الاتجاهات
            $data['trend_insights'] = array(
                array('trend' => 'up', 'description' => 'نمو في الإيرادات بنسبة 12%'),
                array('trend' => 'down', 'description' => 'انخفاض في التكاليف بنسبة 5%'),
                array('trend' => 'up', 'description' => 'تحسن في هوامش الربح'),
                array('trend' => 'stable', 'description' => 'استقرار في الأداء العام')
            );

            // إرسال إشعار للإدارة العليا عن الأداء
            $this->central_service->sendNotification(
                'cost_center_dashboard_viewed',
                'مراجعة لوحة معلومات مراكز التكلفة',
                'تم عرض لوحة معلومات مراكز التكلفة - إجمالي الربح: ' . $data['summary']['total_profit'],
                [$this->config->get('config_cfo_id'), $this->config->get('config_ceo_id')],
                [
                    'total_profit' => $data['summary']['total_profit'],
                    'profit_margin' => $data['summary']['overall_profit_margin'],
                    'profitable_centers' => $data['summary']['profitable_centers'],
                    'loss_making_centers' => $data['summary']['loss_making_centers'],
                    'viewed_by' => $this->user->getFirstName() . ' ' . $this->user->getLastName()
                ]
            );

        } catch (Exception $e) {
            $this->error['warning'] = 'خطأ في تحميل بيانات لوحة المعلومات: ' . $e->getMessage();

            // بيانات افتراضية في حالة الخطأ
            $data['summary'] = array(
                'total_cost_centers' => 0,
                'total_revenues' => 0,
                'total_expenses' => 0,
                'total_profit' => 0,
                'overall_profit_margin' => 0,
                'profitable_centers' => 0,
                'loss_making_centers' => 0
            );
            $data['cost_centers'] = array();
            $data['trends'] = array();
            $data['top_performers'] = array();
            $data['attention_required'] = array();
            $data['trend_insights'] = array();
        }

        // إعداد الروابط
        $data['breadcrumbs'] = array();
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('accounts/cost_center_report', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_dashboard'),
            'href' => $this->url->link('accounts/cost_center_report/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );

        $data['generate'] = $this->url->link('accounts/cost_center_report', 'user_token=' . $this->session->data['user_token'], true);
        $data['export_excel'] = $this->url->link('accounts/cost_center_report/export', 'user_token=' . $this->session->data['user_token'] . '&format=excel', true);
        $data['export_pdf'] = $this->url->link('accounts/cost_center_report/export', 'user_token=' . $this->session->data['user_token'] . '&format=pdf', true);
        $data['print'] = $this->url->link('accounts/cost_center_report/print', 'user_token=' . $this->session->data['user_token'], true);
        $data['back'] = $this->url->link('accounts/cost_center_report', 'user_token=' . $this->session->data['user_token'], true);

        $data['profitability_analysis'] = $this->url->link('accounts/cost_center_report/profitability_analysis', 'user_token=' . $this->session->data['user_token'], true);
        $data['variance_analysis'] = $this->url->link('accounts/cost_center_report/variance_analysis', 'user_token=' . $this->session->data['user_token'], true);
        $data['cost_allocation'] = $this->url->link('accounts/cost_center_report/cost_allocation', 'user_token=' . $this->session->data['user_token'], true);
        $data['detailed_report'] = $this->url->link('accounts/cost_center_report/view', 'user_token=' . $this->session->data['user_token'], true);

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/cost_center_report_dashboard', $data));
    }

    /**
     * تحليل ABC لمراكز التكلفة
     */
    public function abcAnalysis() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/cost_center_report') ||
            !$this->user->hasKey('accounting_cost_center_abc_analysis')) {

            $this->central_service->logActivity('unauthorized_abc_analysis', 'accounts',
                'محاولة تحليل ABC لمراكز التكلفة غير مصرح بها', [
                'user_id' => $this->user->getId(),
                'ip_address' => $this->request->server['REMOTE_ADDR']
            ]);

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/cost_center_report');
        $this->document->setTitle($this->language->get('heading_title') . ' - ' . $this->language->get('text_abc_analysis'));
        $this->load->model('accounts/cost_center_report');

        // إضافة CSS و JavaScript المتقدم للتحليل
        $this->document->addStyle('view/stylesheet/accounts/cost_center_abc.css');
        $this->document->addScript('view/javascript/accounts/cost_center_abc.js');
        $this->document->addScript('view/javascript/jquery/chart.min.js');

        if ($this->request->server['REQUEST_METHOD'] == 'POST') {
            try {
                $filter_data = $this->prepareFilterData();

                // تسجيل إنشاء تحليل ABC
                $this->central_service->logActivity('generate_abc_analysis', 'accounts',
                    'إنشاء تحليل ABC لمراكز التكلفة للفترة: ' . $filter_data['date_start'] . ' إلى ' . $filter_data['date_end'], [
                    'user_id' => $this->user->getId(),
                    'filter_data' => $filter_data
                ]);

                // الحصول على تحليل ABC
                $abc_analysis = $this->model_accounts_cost_center_report->performABCAnalysis($filter_data);

                // إرسال إشعار للإدارة المالية
                $this->central_service->sendNotification(
                    'abc_analysis_generated',
                    'تحليل ABC لمراكز التكلفة',
                    'تم إنشاء تحليل ABC لمراكز التكلفة للفترة ' . $filter_data['date_start'] . ' إلى ' . $filter_data['date_end'],
                    [$this->config->get('config_cfo_id'), $this->config->get('config_cost_manager_id')],
                    [
                        'category_a_count' => $abc_analysis['summary']['category_a_count'],
                        'category_b_count' => $abc_analysis['summary']['category_b_count'],
                        'category_c_count' => $abc_analysis['summary']['category_c_count'],
                        'total_costs' => $abc_analysis['summary']['total_costs'],
                        'generated_by' => $this->user->getFirstName() . ' ' . $this->user->getLastName()
                    ]
                );

                $this->session->data['success'] = $this->language->get('text_success_abc_analysis');
                $this->session->data['abc_analysis'] = $abc_analysis;

                $this->response->redirect($this->url->link('accounts/cost_center_report/abcView', 'user_token=' . $this->session->data['user_token'], true));
            } catch (Exception $e) {
                $this->error['warning'] = $e->getMessage();
            }
        }

        // عرض نموذج تحليل ABC
        $data['breadcrumbs'] = array();
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('accounts/cost_center_report', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_abc_analysis'),
            'href' => $this->url->link('accounts/cost_center_report/abcAnalysis', 'user_token=' . $this->session->data['user_token'], true)
        );

        $data['action'] = $this->url->link('accounts/cost_center_report/abcAnalysis', 'user_token=' . $this->session->data['user_token'], true);
        $data['cancel'] = $this->url->link('accounts/cost_center_report', 'user_token=' . $this->session->data['user_token'], true);

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/cost_center_abc_analysis_form', $data));
    }

    /**
     * التنبؤ بالتكاليف المستقبلية
     */
    public function forecastCosts() {
        // فحص الصلاحيات المزدوجة
        if (!$this->user->hasPermission('access', 'accounts/cost_center_report') ||
            !$this->user->hasKey('accounting_cost_center_forecast')) {

            $this->response->redirect($this->url->link('error/permission'));
            return;
        }

        $this->load->language('accounts/cost_center_report');
        $this->document->setTitle($this->language->get('text_cost_forecast'));

        if ($this->request->server['REQUEST_METHOD'] == 'POST') {
            try {
                $cost_center_id = $this->request->post['cost_center_id'];
                $forecast_periods = $this->request->post['forecast_periods'] ?? 12;

                // تسجيل إنشاء التنبؤ
                $this->central_service->logActivity('generate_cost_forecast', 'accounts',
                    'إنشاء تنبؤ التكاليف لمركز التكلفة: ' . $cost_center_id, [
                    'user_id' => $this->user->getId(),
                    'cost_center_id' => $cost_center_id,
                    'forecast_periods' => $forecast_periods
                ]);

                // الحصول على التنبؤ
                $forecast = $this->model_accounts_cost_center_report->forecastFutureCosts($cost_center_id, $forecast_periods);

                $this->session->data['success'] = $this->language->get('text_forecast_generated');
                $this->session->data['cost_forecast'] = $forecast;

                $this->response->redirect($this->url->link('accounts/cost_center_report/forecastView', 'user_token=' . $this->session->data['user_token'], true));
            } catch (Exception $e) {
                $this->error['warning'] = $e->getMessage();
            }
        }

        $data['breadcrumbs'] = array();
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_home'),
            'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('heading_title'),
            'href' => $this->url->link('accounts/cost_center_report', 'user_token=' . $this->session->data['user_token'], true)
        );
        $data['breadcrumbs'][] = array(
            'text' => $this->language->get('text_cost_forecast'),
            'href' => $this->url->link('accounts/cost_center_report/forecastCosts', 'user_token=' . $this->session->data['user_token'], true)
        );

        $data['action'] = $this->url->link('accounts/cost_center_report/forecastCosts', 'user_token=' . $this->session->data['user_token'], true);
        $data['cancel'] = $this->url->link('accounts/cost_center_report', 'user_token=' . $this->session->data['user_token'], true);

        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        $this->response->setOutput($this->load->view('accounts/cost_center_forecast_form', $data));
    }

    /**
     * دالة تنظيف المخرجات (CONSTITUTIONAL REQUIREMENT)
     * Sanitize all output data to prevent XSS attacks
     */
    private function sanitizeOutputData($data) {
        if (is_array($data)) {
            foreach ($data as $key => $value) {
                $data[$key] = $this->sanitizeOutputData($value);
            }
        } elseif (is_string($data)) {
            $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
        }
        return $data;
    }

    /**
     * Enterprise Grade Plus Security Enhancements
     */
    private function validateInputData($data) {
        $validated = array();

        if (isset($data['cost_center_id'])) {
            $validated['cost_center_id'] = (int)$data['cost_center_id'];
        }

        if (isset($data['date_start'])) {
            $validated['date_start'] = date('Y-m-d', strtotime($data['date_start']));
        }

        if (isset($data['date_end'])) {
            $validated['date_end'] = date('Y-m-d', strtotime($data['date_end']));
        }

        if (isset($data['department_id'])) {
            $validated['department_id'] = (int)$data['department_id'];
        }

        return $validated;
    }

    /**
     * Rate Limiting Implementation
     */
    private function checkRateLimit() {
        $ip = $this->request->server['REMOTE_ADDR'];
        $user_id = $this->user->getId();

        return $this->central_service->checkRateLimit('cost_center_report', $ip, $user_id, 30, 3600); // 30 requests per hour
    }

    /**
     * Performance Optimization - Memory Management
     */
    private function optimizeMemoryUsage() {
        ini_set('memory_limit', '512M');
        ini_set('max_execution_time', 300); // 5 minutes for cost center report generation
    }
}
