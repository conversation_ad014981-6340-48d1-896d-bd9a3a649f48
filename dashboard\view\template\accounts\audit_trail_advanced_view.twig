{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        {% if has_permission %}
        <button type="button" id="button-edit" data-toggle="tooltip" title="{{ button_edit }}" class="btn btn-primary">
          <i class="fa fa-pencil"></i> {{ button_edit }}
        </button>
        <button type="button" id="button-complete" data-toggle="tooltip" title="{{ button_complete }}" class="btn btn-success">
          <i class="fa fa-check"></i> {{ button_complete }}
        </button>
        <button type="button" id="button-add-finding" data-toggle="tooltip" title="{{ button_add_finding }}" class="btn btn-warning">
          <i class="fa fa-plus"></i> {{ button_add_finding }}
        </button>
        <button type="button" id="button-export" data-toggle="tooltip" title="{{ button_export }}" class="btn btn-info">
          <i class="fa fa-download"></i> {{ button_export }}
        </button>
        {% endif %}
        <a href="{{ back }}" data-toggle="tooltip" title="{{ button_back }}" class="btn btn-default">
          <i class="fa fa-reply"></i> {{ button_back }}
        </a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    {% if success %}
    <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    
    <!-- معلومات التدقيق الأساسية -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-info-circle"></i> {{ text_audit_info }}</h3>
      </div>
      <div class="panel-body">
        <div class="row">
          <div class="col-md-6">
            <table class="table table-bordered">
              <tr>
                <td><strong>{{ text_audit_name }}:</strong></td>
                <td>{{ audit.audit_name }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_audit_type }}:</strong></td>
                <td>
                  {% if audit.audit_type == 'financial' %}
                    <span class="label label-primary">{{ text_financial_audit }}</span>
                  {% elseif audit.audit_type == 'operational' %}
                    <span class="label label-info">{{ text_operational_audit }}</span>
                  {% elseif audit.audit_type == 'compliance' %}
                    <span class="label label-warning">{{ text_compliance_audit }}</span>
                  {% else %}
                    <span class="label label-default">{{ text_internal_audit }}</span>
                  {% endif %}
                </td>
              </tr>
              <tr>
                <td><strong>{{ text_audit_scope }}:</strong></td>
                <td>{{ audit.audit_scope }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_start_date }}:</strong></td>
                <td>{{ audit.start_date }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_end_date }}:</strong></td>
                <td>{{ audit.end_date }}</td>
              </tr>
            </table>
          </div>
          <div class="col-md-6">
            <table class="table table-bordered">
              <tr>
                <td><strong>{{ text_status }}:</strong></td>
                <td>
                  {% if audit.status == 'planning' %}
                    <span class="label label-default">{{ text_status_planning }}</span>
                  {% elseif audit.status == 'in_progress' %}
                    <span class="label label-warning">{{ text_status_in_progress }}</span>
                  {% elseif audit.status == 'completed' %}
                    <span class="label label-success">{{ text_status_completed }}</span>
                  {% elseif audit.status == 'cancelled' %}
                    <span class="label label-danger">{{ text_status_cancelled }}</span>
                  {% endif %}
                </td>
              </tr>
              <tr>
                <td><strong>{{ text_lead_auditor }}:</strong></td>
                <td>{{ audit.lead_auditor_name }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_audit_team }}:</strong></td>
                <td>{{ audit.audit_team }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_workflow_id }}:</strong></td>
                <td>
                  {% if audit.workflow_id %}
                    <a href="{{ audit.workflow_link }}" class="btn btn-xs btn-info">
                      <i class="fa fa-sitemap"></i> {{ text_view_workflow }}
                    </a>
                  {% else %}
                    <span class="text-muted">{{ text_no_workflow }}</span>
                  {% endif %}
                </td>
              </tr>
              <tr>
                <td><strong>{{ text_created_by }}:</strong></td>
                <td>{{ audit.created_by_name }}</td>
              </tr>
              <tr>
                <td><strong>{{ text_date_created }}:</strong></td>
                <td>{{ audit.date_created }}</td>
              </tr>
            </table>
          </div>
        </div>
        {% if audit.description %}
        <div class="row">
          <div class="col-md-12">
            <h4>{{ text_description }}</h4>
            <p>{{ audit.description|nl2br }}</p>
          </div>
        </div>
        {% endif %}
      </div>
    </div>

    <!-- إحصائيات التدقيق -->
    <div class="row">
      <div class="col-lg-3 col-xs-6">
        <div class="small-box bg-aqua">
          <div class="inner">
            <h3>{{ statistics.total_findings }}</h3>
            <p>{{ text_total_findings }}</p>
          </div>
          <div class="icon">
            <i class="fa fa-search"></i>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-xs-6">
        <div class="small-box bg-red">
          <div class="inner">
            <h3>{{ statistics.high_risk_findings }}</h3>
            <p>{{ text_high_risk_findings }}</p>
          </div>
          <div class="icon">
            <i class="fa fa-exclamation-triangle"></i>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-xs-6">
        <div class="small-box bg-yellow">
          <div class="inner">
            <h3>{{ statistics.medium_risk_findings }}</h3>
            <p>{{ text_medium_risk_findings }}</p>
          </div>
          <div class="icon">
            <i class="fa fa-warning"></i>
          </div>
        </div>
      </div>
      <div class="col-lg-3 col-xs-6">
        <div class="small-box bg-green">
          <div class="inner">
            <h3>{{ statistics.completion_percentage }}%</h3>
            <p>{{ text_completion_percentage }}</p>
          </div>
          <div class="icon">
            <i class="fa fa-check-circle"></i>
          </div>
        </div>
      </div>
    </div>

    <!-- تفاصيل التدقيق -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-list"></i> {{ text_audit_details }}</h3>
      </div>
      <div class="panel-body">
        <ul class="nav nav-tabs" role="tablist">
          <li role="presentation" class="active">
            <a href="#findings" aria-controls="findings" role="tab" data-toggle="tab">
              <i class="fa fa-search"></i> {{ tab_findings }}
              {% if statistics.total_findings > 0 %}
                <span class="badge">{{ statistics.total_findings }}</span>
              {% endif %}
            </a>
          </li>
          <li role="presentation">
            <a href="#evidence" aria-controls="evidence" role="tab" data-toggle="tab">
              <i class="fa fa-file"></i> {{ tab_evidence }}
              {% if statistics.total_evidence > 0 %}
                <span class="badge">{{ statistics.total_evidence }}</span>
              {% endif %}
            </a>
          </li>
          <li role="presentation">
            <a href="#workflow" aria-controls="workflow" role="tab" data-toggle="tab">
              <i class="fa fa-sitemap"></i> {{ tab_workflow }}
            </a>
          </li>
          <li role="presentation">
            <a href="#ai-checks" aria-controls="ai-checks" role="tab" data-toggle="tab">
              <i class="fa fa-magic"></i> {{ tab_ai_checks }}
            </a>
          </li>
        </ul>

        <div class="tab-content">
          <!-- النتائج والملاحظات -->
          <div role="tabpanel" class="tab-pane active" id="findings">
            <div style="margin-top: 15px;">
              {% if findings %}
              <div class="table-responsive">
                <table class="table table-bordered table-hover">
                  <thead>
                    <tr>
                      <th>{{ column_finding_title }}</th>
                      <th>{{ column_risk_level }}</th>
                      <th>{{ column_category }}</th>
                      <th>{{ column_status }}</th>
                      <th>{{ column_assigned_to }}</th>
                      <th>{{ column_due_date }}</th>
                      <th>{{ column_action }}</th>
                    </tr>
                  </thead>
                  <tbody>
                    {% for finding in findings %}
                    <tr>
                      <td>
                        <strong>{{ finding.finding_title }}</strong>
                        {% if finding.description %}
                          <br><small class="text-muted">{{ finding.description|truncate(100) }}</small>
                        {% endif %}
                      </td>
                      <td>
                        {% if finding.risk_level == 'high' %}
                          <span class="label label-danger">{{ text_high_risk }}</span>
                        {% elseif finding.risk_level == 'medium' %}
                          <span class="label label-warning">{{ text_medium_risk }}</span>
                        {% else %}
                          <span class="label label-info">{{ text_low_risk }}</span>
                        {% endif %}
                      </td>
                      <td>{{ finding.category }}</td>
                      <td>
                        {% if finding.status == 'open' %}
                          <span class="label label-warning">{{ text_status_open }}</span>
                        {% elseif finding.status == 'in_progress' %}
                          <span class="label label-info">{{ text_status_in_progress }}</span>
                        {% elseif finding.status == 'resolved' %}
                          <span class="label label-success">{{ text_status_resolved }}</span>
                        {% else %}
                          <span class="label label-default">{{ text_status_closed }}</span>
                        {% endif %}
                      </td>
                      <td>{{ finding.assigned_to_name }}</td>
                      <td>
                        {% if finding.due_date %}
                          {{ finding.due_date }}
                          {% if finding.is_overdue %}
                            <span class="label label-danger">{{ text_overdue }}</span>
                          {% endif %}
                        {% else %}
                          <span class="text-muted">{{ text_no_due_date }}</span>
                        {% endif %}
                      </td>
                      <td>
                        <div class="btn-group">
                          <a href="{{ finding.view }}" class="btn btn-xs btn-info">
                            <i class="fa fa-eye"></i> {{ button_view }}
                          </a>
                          {% if finding.edit %}
                          <a href="{{ finding.edit }}" class="btn btn-xs btn-primary">
                            <i class="fa fa-pencil"></i> {{ button_edit }}
                          </a>
                          {% endif %}
                        </div>
                      </td>
                    </tr>
                    {% endfor %}
                  </tbody>
                </table>
              </div>
              {% else %}
              <div class="alert alert-info">
                <i class="fa fa-info-circle"></i> {{ text_no_findings }}
              </div>
              {% endif %}
            </div>
          </div>

          <!-- الأدلة والمستندات -->
          <div role="tabpanel" class="tab-pane" id="evidence">
            <div style="margin-top: 15px;">
              {% if evidence %}
              <div class="table-responsive">
                <table class="table table-bordered table-hover">
                  <thead>
                    <tr>
                      <th>{{ column_evidence_title }}</th>
                      <th>{{ column_evidence_type }}</th>
                      <th>{{ column_file_name }}</th>
                      <th>{{ column_file_size }}</th>
                      <th>{{ column_uploaded_by }}</th>
                      <th>{{ column_upload_date }}</th>
                      <th>{{ column_action }}</th>
                    </tr>
                  </thead>
                  <tbody>
                    {% for item in evidence %}
                    <tr>
                      <td>{{ item.evidence_title }}</td>
                      <td>
                        {% if item.evidence_type == 'document' %}
                          <span class="label label-primary">{{ text_document }}</span>
                        {% elseif item.evidence_type == 'screenshot' %}
                          <span class="label label-info">{{ text_screenshot }}</span>
                        {% elseif item.evidence_type == 'report' %}
                          <span class="label label-success">{{ text_report }}</span>
                        {% else %}
                          <span class="label label-default">{{ text_other }}</span>
                        {% endif %}
                      </td>
                      <td>
                        {% if item.file_name %}
                          <i class="fa fa-file"></i> {{ item.file_name }}
                        {% else %}
                          <span class="text-muted">{{ text_no_file }}</span>
                        {% endif %}
                      </td>
                      <td>{{ item.file_size }}</td>
                      <td>{{ item.uploaded_by_name }}</td>
                      <td>{{ item.upload_date }}</td>
                      <td>
                        <div class="btn-group">
                          {% if item.download %}
                          <a href="{{ item.download }}" class="btn btn-xs btn-success">
                            <i class="fa fa-download"></i> {{ button_download }}
                          </a>
                          {% endif %}
                          {% if item.view %}
                          <a href="{{ item.view }}" class="btn btn-xs btn-info" target="_blank">
                            <i class="fa fa-eye"></i> {{ button_view }}
                          </a>
                          {% endif %}
                        </div>
                      </td>
                    </tr>
                    {% endfor %}
                  </tbody>
                </table>
              </div>
              {% else %}
              <div class="alert alert-info">
                <i class="fa fa-info-circle"></i> {{ text_no_evidence }}
              </div>
              {% endif %}
            </div>
          </div>

          <!-- سير العمل -->
          <div role="tabpanel" class="tab-pane" id="workflow">
            <div style="margin-top: 15px;">
              {% if audit.workflow_id %}
              <div class="panel panel-info">
                <div class="panel-heading">
                  <h4 class="panel-title">{{ text_linked_workflow }}</h4>
                </div>
                <div class="panel-body">
                  <p><strong>{{ text_workflow_name }}:</strong> {{ workflow.workflow_name }}</p>
                  <p><strong>{{ text_workflow_status }}:</strong> 
                    {% if workflow.status == 'active' %}
                      <span class="label label-success">{{ text_active }}</span>
                    {% elseif workflow.status == 'paused' %}
                      <span class="label label-warning">{{ text_paused }}</span>
                    {% else %}
                      <span class="label label-default">{{ text_completed }}</span>
                    {% endif %}
                  </p>
                  <p><strong>{{ text_current_step }}:</strong> {{ workflow.current_step }}</p>
                  <div class="progress">
                    <div class="progress-bar progress-bar-info" style="width: {{ workflow.completion_percentage }}%">
                      {{ workflow.completion_percentage }}%
                    </div>
                  </div>
                  <div class="text-right">
                    <a href="{{ workflow.view_link }}" class="btn btn-info">
                      <i class="fa fa-sitemap"></i> {{ button_view_workflow }}
                    </a>
                  </div>
                </div>
              </div>
              {% else %}
              <div class="alert alert-warning">
                <i class="fa fa-warning"></i> {{ text_no_workflow_linked }}
                <div class="text-right" style="margin-top: 10px;">
                  <button type="button" id="button-link-workflow" class="btn btn-primary">
                    <i class="fa fa-link"></i> {{ button_link_workflow }}
                  </button>
                </div>
              </div>
              {% endif %}
            </div>
          </div>

          <!-- فحوصات الذكاء الاصطناعي -->
          <div role="tabpanel" class="tab-pane" id="ai-checks">
            <div style="margin-top: 15px;">
              {% if ai_checks %}
              <div class="table-responsive">
                <table class="table table-bordered table-hover">
                  <thead>
                    <tr>
                      <th>{{ column_check_name }}</th>
                      <th>{{ column_check_type }}</th>
                      <th>{{ column_result }}</th>
                      <th>{{ column_confidence_score }}</th>
                      <th>{{ column_recommendations }}</th>
                      <th>{{ column_run_date }}</th>
                    </tr>
                  </thead>
                  <tbody>
                    {% for check in ai_checks %}
                    <tr>
                      <td>{{ check.check_name }}</td>
                      <td>
                        {% if check.check_type == 'anomaly_detection' %}
                          <span class="label label-warning">{{ text_anomaly_detection }}</span>
                        {% elseif check.check_type == 'pattern_analysis' %}
                          <span class="label label-info">{{ text_pattern_analysis }}</span>
                        {% elseif check.check_type == 'risk_assessment' %}
                          <span class="label label-danger">{{ text_risk_assessment }}</span>
                        {% else %}
                          <span class="label label-default">{{ text_compliance_check }}</span>
                        {% endif %}
                      </td>
                      <td>
                        {% if check.result == 'pass' %}
                          <span class="label label-success">{{ text_pass }}</span>
                        {% elseif check.result == 'fail' %}
                          <span class="label label-danger">{{ text_fail }}</span>
                        {% elseif check.result == 'warning' %}
                          <span class="label label-warning">{{ text_warning }}</span>
                        {% else %}
                          <span class="label label-info">{{ text_review_required }}</span>
                        {% endif %}
                      </td>
                      <td>
                        <div class="progress" style="margin-bottom: 0;">
                          <div class="progress-bar {% if check.confidence_score >= 90 %}progress-bar-success{% elseif check.confidence_score >= 70 %}progress-bar-warning{% else %}progress-bar-danger{% endif %}" style="width: {{ check.confidence_score }}%">
                            {{ check.confidence_score }}%
                          </div>
                        </div>
                      </td>
                      <td>
                        {% if check.recommendations %}
                          <button type="button" class="btn btn-xs btn-info" data-toggle="popover" data-content="{{ check.recommendations }}" data-trigger="hover">
                            <i class="fa fa-lightbulb-o"></i> {{ button_view_recommendations }}
                          </button>
                        {% else %}
                          <span class="text-muted">{{ text_no_recommendations }}</span>
                        {% endif %}
                      </td>
                      <td>{{ check.run_date }}</td>
                    </tr>
                    {% endfor %}
                  </tbody>
                </table>
              </div>
              <div class="text-right">
                <button type="button" id="button-run-ai-checks" class="btn btn-warning">
                  <i class="fa fa-magic"></i> {{ button_run_ai_checks }}
                </button>
              </div>
              {% else %}
              <div class="alert alert-info">
                <i class="fa fa-info-circle"></i> {{ text_no_ai_checks }}
                <div class="text-right" style="margin-top: 10px;">
                  <button type="button" id="button-run-ai-checks" class="btn btn-warning">
                    <i class="fa fa-magic"></i> {{ button_run_ai_checks }}
                  </button>
                </div>
              </div>
              {% endif %}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- ملاحظات -->
    {% if audit.notes %}
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-sticky-note"></i> {{ text_notes }}</h3>
      </div>
      <div class="panel-body">
        <p>{{ audit.notes|nl2br }}</p>
      </div>
    </div>
    {% endif %}

  </div>
</div>

<!-- نموذج إضافة نتيجة جديدة -->
<div id="modal-finding" class="modal fade">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
        <h4 class="modal-title">{{ text_add_finding }}</h4>
      </div>
      <div class="modal-body">
        <form action="{{ add_finding }}" method="post" enctype="multipart/form-data" id="form-finding">
          <div class="form-group required">
            <label class="control-label">{{ entry_finding_title }}</label>
            <input type="text" name="finding_title" value="" placeholder="{{ entry_finding_title }}" class="form-control" />
          </div>
          <div class="row">
            <div class="col-md-6">
              <div class="form-group required">
                <label class="control-label">{{ entry_risk_level }}</label>
                <select name="risk_level" class="form-control">
                  <option value="low">{{ text_low_risk }}</option>
                  <option value="medium">{{ text_medium_risk }}</option>
                  <option value="high">{{ text_high_risk }}</option>
                </select>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group required">
                <label class="control-label">{{ entry_category }}</label>
                <input type="text" name="category" value="" placeholder="{{ entry_category }}" class="form-control" />
              </div>
            </div>
          </div>
          <div class="form-group required">
            <label class="control-label">{{ entry_description }}</label>
            <textarea name="description" rows="4" placeholder="{{ entry_description }}" class="form-control"></textarea>
          </div>
          <div class="row">
            <div class="col-md-6">
              <div class="form-group">
                <label class="control-label">{{ entry_assigned_to }}</label>
                <select name="assigned_to" class="form-control">
                  <option value="">{{ text_select_user }}</option>
                  {% for user in users %}
                  <option value="{{ user.user_id }}">{{ user.firstname }} {{ user.lastname }}</option>
                  {% endfor %}
                </select>
              </div>
            </div>
            <div class="col-md-6">
              <div class="form-group">
                <label class="control-label">{{ entry_due_date }}</label>
                <div class="input-group date">
                  <input type="text" name="due_date" value="" placeholder="{{ entry_due_date }}" data-date-format="YYYY-MM-DD" class="form-control" />
                  <span class="input-group-btn">
                    <button class="btn btn-default" type="button"><i class="fa fa-calendar"></i></button>
                  </span>
                </div>
              </div>
            </div>
          </div>
          <div class="form-group">
            <label class="control-label">{{ entry_recommendations }}</label>
            <textarea name="recommendations" rows="3" placeholder="{{ entry_recommendations }}" class="form-control"></textarea>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">{{ button_cancel }}</button>
        <button type="button" id="button-finding-save" class="btn btn-primary">{{ button_save }}</button>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript">
$(document).ready(function() {
    // تهيئة منتقي التاريخ
    $('.date').datetimepicker({
        language: '{{ datepicker }}',
        pickTime: false
    });

    // تهيئة Popover للتوصيات
    $('[data-toggle="popover"]').popover();

    // إضافة نتيجة جديدة
    $('#button-add-finding').on('click', function() {
        $('#modal-finding').modal('show');
    });

    // حفظ النتيجة الجديدة
    $('#button-finding-save').on('click', function() {
        $.ajax({
            url: '{{ add_finding }}',
            type: 'post',
            data: $('#form-finding').serialize(),
            dataType: 'json',
            success: function(json) {
                if (json['success']) {
                    location.reload();
                } else {
                    alert(json['error']);
                }
            }
        });
    });

    // إكمال التدقيق
    $('#button-complete').on('click', function() {
        if (confirm('{{ text_confirm_complete }}')) {
            $.ajax({
                url: '{{ complete }}',
                type: 'post',
                dataType: 'json',
                success: function(json) {
                    if (json['success']) {
                        location.reload();
                    } else {
                        alert(json['error']);
                    }
                }
            });
        }
    });

    // تشغيل فحوصات الذكاء الاصطناعي
    $('#button-run-ai-checks').on('click', function() {
        if (confirm('{{ text_confirm_ai_checks }}')) {
            $.ajax({
                url: '{{ run_ai_checks }}',
                type: 'post',
                dataType: 'json',
                beforeSend: function() {
                    $('#button-run-ai-checks').prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> {{ text_processing }}');
                },
                success: function(json) {
                    if (json['success']) {
                        location.reload();
                    } else {
                        alert(json['error']);
                    }
                },
                complete: function() {
                    $('#button-run-ai-checks').prop('disabled', false).html('<i class="fa fa-magic"></i> {{ button_run_ai_checks }}');
                }
            });
        }
    });

    // ربط سير العمل
    $('#button-link-workflow').on('click', function() {
        // يمكن إضافة نموذج لاختيار سير العمل
        alert('{{ text_feature_coming_soon }}');
    });

    // تصدير التقرير
    $('#button-export').on('click', function() {
        window.open('{{ export }}', '_blank');
    });

    // تحرير التدقيق
    $('#button-edit').on('click', function() {
        window.location = '{{ edit }}';
    });
});
</script>

{{ footer }}
