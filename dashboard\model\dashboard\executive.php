<?php
/**
 * Executive Dashboard Model
 * نموذج لوحة القيادة للإدارة العليا
 * 
 * يحتوي على جميع الاستعلامات المتعلقة بـ:
 * - المؤشرات الرئيسية العالمية
 * - ملخص الأرباح والخسائر
 * - المبيعات حسب القناة
 * - مؤشرات صحة الشركة
 * - خريطة المبيعات الجغرافية
 * - تتبع الأهداف
 * - الموافقات عالية القيمة
 */

class ModelDashboardExecutive extends Model {
    
    /**
     * Check if table exists
     */
    private function tableExists($table_name) {
        $sql = "SHOW TABLES LIKE '" . DB_PREFIX . $table_name . "'";
        $query = $this->db->query($sql);
        return $query->num_rows > 0;
    }
    
    /**
     * Safe query execution with error handling
     */
    private function safeQuery($sql, $default_value = null) {
        try {
            $query = $this->db->query($sql);
            return $query;
        } catch (Exception $e) {
            error_log('Executive Dashboard Query Error: ' . $e->getMessage() . ' SQL: ' . $sql);
            if ($default_value === null) {
                // إنشاء كائن استعلام فارغ مؤقت
                return (object)['row' => [], 'rows' => [], 'num_rows' => 0];
            }
            return $default_value;
        }
    }
    
    /**
     * Get Global KPIs
     * المؤشرات الرئيسية العالمية
     */
    public function getGlobalKPIs($filters = array()) {
        $date_filter = isset($filters['date_range']) ? $filters['date_range'] : 'today';
        $branch_filter = isset($filters['branch_id']) ? $filters['branch_id'] : null;
        
        $where_clause = "WHERE 1=1";
        
        // Date filter
        switch($date_filter) {
            case 'today':
                $where_clause .= " AND DATE(o.date_added) = CURDATE()";
                break;
            case 'week':
                $where_clause .= " AND o.date_added >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)";
                break;
            case 'month':
                $where_clause .= " AND o.date_added >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)";
                break;
            case 'quarter':
                $where_clause .= " AND o.date_added >= DATE_SUB(CURDATE(), INTERVAL 90 DAY)";
                break;
            case 'year':
                $where_clause .= " AND YEAR(o.date_added) = YEAR(CURDATE())";
                break;
        }
        
        // Branch filter
        if ($branch_filter) {
            $where_clause .= " AND o.store_id = " . (int)$branch_filter;
        }
        
        if (!$this->tableExists('order')) {
            return array(
                'total_revenue' => 0,
                'net_profit' => 0,
                'inventory_value' => 0,
                'cash_flow' => 0,
                'total_orders' => 0,
                'total_customers' => 0,
                'avg_order_value' => 0,
                'conversion_rate' => 0
            );
        }
        
        $sql = "SELECT 
                    COALESCE(SUM(o.total), 0) as total_revenue,
                    COALESCE(SUM(o.total - COALESCE(od.total_cost, 0)), 0) as net_profit,
                    COUNT(DISTINCT o.order_id) as total_orders,
                    COUNT(DISTINCT o.customer_id) as total_customers,
                    COALESCE(AVG(o.total), 0) as avg_order_value
                FROM " . DB_PREFIX . "order o
                LEFT JOIN (
                    SELECT 
                        order_id,
                        SUM(quantity * cost) as total_cost
                    FROM " . DB_PREFIX . "order_product
                    GROUP BY order_id
                ) od ON o.order_id = od.order_id
                " . $where_clause . "
                AND o.order_status_id NOT IN (7)"; // Exclude cancelled orders
        
        $query = $this->safeQuery($sql);
        $result = $query && isset($query->row) ? $query->row : array();
        
        // Get inventory value
        $inventory_value = $this->getInventoryValue($filters);
        
        // Get cash flow
        $cash_flow = $this->getCashFlow($filters);
        
        // Calculate conversion rate
        $conversion_rate = $this->getConversionRate($filters);
        
        return array(
            'total_revenue' => $result['total_revenue'] ?? 0,
            'net_profit' => $result['net_profit'] ?? 0,
            'inventory_value' => $inventory_value,
            'cash_flow' => $cash_flow,
            'total_orders' => $result['total_orders'] ?? 0,
            'total_customers' => $result['total_customers'] ?? 0,
            'avg_order_value' => $result['avg_order_value'] ?? 0,
            'conversion_rate' => $conversion_rate
        );
    }
    
    /**
     * Get Profit & Loss Summary
     * ملخص الأرباح والخسائر
     */
    public function getProfitLossSummary($filters = array()) {
        $date_filter = isset($filters['date_range']) ? $filters['date_range'] : 'month';
        $branch_filter = isset($filters['branch_id']) ? $filters['branch_id'] : null;
        
        $where_clause = "WHERE 1=1";
        
        // Date filter
        switch($date_filter) {
            case 'month':
                $where_clause .= " AND YEAR(o.date_added) = YEAR(CURDATE()) AND MONTH(o.date_added) = MONTH(CURDATE())";
                break;
            case 'quarter':
                $where_clause .= " AND o.date_added >= DATE_SUB(CURDATE(), INTERVAL 90 DAY)";
                break;
            case 'year':
                $where_clause .= " AND YEAR(o.date_added) = YEAR(CURDATE())";
                break;
        }
        
        // Branch filter
        if ($branch_filter) {
            $where_clause .= " AND o.store_id = " . (int)$branch_filter;
        }
        
        if (!$this->tableExists('order')) {
            return array(
                'revenue' => 0,
                'cost_of_goods' => 0,
                'gross_profit' => 0,
                'operating_expenses' => 0,
                'net_profit' => 0,
                'gross_margin' => 0,
                'net_margin' => 0
            );
        }
        
        $sql = "SELECT 
                    COALESCE(SUM(o.total), 0) as revenue,
                    COALESCE(SUM(COALESCE(od.total_cost, 0)), 0) as cost_of_goods,
                    COALESCE(SUM(o.total - COALESCE(od.total_cost, 0)), 0) as gross_profit
                FROM " . DB_PREFIX . "order o
                LEFT JOIN (
                    SELECT 
                        order_id,
                        SUM(quantity * cost) as total_cost
                    FROM " . DB_PREFIX . "order_product
                    GROUP BY order_id
                ) od ON o.order_id = od.order_id
                " . $where_clause . "
                AND o.order_status_id NOT IN (7)";
        
        $query = $this->safeQuery($sql);
        $result = $query && isset($query->row) ? $query->row : array();
        
        $revenue = $result['revenue'] ?? 0;
        $cost_of_goods = $result['cost_of_goods'] ?? 0;
        $gross_profit = $result['gross_profit'] ?? 0;
        
        // Get operating expenses (simplified - you might want to add actual expense tracking)
        $operating_expenses = $this->getOperatingExpenses($filters);
        
        $net_profit = $gross_profit - $operating_expenses;
        
        // Calculate margins
        $gross_margin = $revenue > 0 ? ($gross_profit / $revenue) * 100 : 0;
        $net_margin = $revenue > 0 ? ($net_profit / $revenue) * 100 : 0;
        
        return array(
            'revenue' => $revenue,
            'cost_of_goods' => $cost_of_goods,
            'gross_profit' => $gross_profit,
            'operating_expenses' => $operating_expenses,
            'net_profit' => $net_profit,
            'gross_margin' => round($gross_margin, 2),
            'net_margin' => round($net_margin, 2)
        );
    }
    
    /**
     * Get Sales by Channel
     * المبيعات حسب القناة
     */
    public function getSalesByChannel($filters = array()) {
        $date_filter = isset($filters['date_range']) ? $filters['date_range'] : 'month';
        $branch_filter = isset($filters['branch_id']) ? $filters['branch_id'] : null;
        
        $where_clause = "WHERE 1=1";
        
        // Date filter
        switch($date_filter) {
            case 'month':
                $where_clause .= " AND YEAR(o.date_added) = YEAR(CURDATE()) AND MONTH(o.date_added) = MONTH(CURDATE())";
                break;
            case 'quarter':
                $where_clause .= " AND o.date_added >= DATE_SUB(CURDATE(), INTERVAL 90 DAY)";
                break;
            case 'year':
                $where_clause .= " AND YEAR(o.date_added) = YEAR(CURDATE())";
                break;
        }
        
        // Branch filter
        if ($branch_filter) {
            $where_clause .= " AND o.store_id = " . (int)$branch_filter;
        }
        
        if (!$this->tableExists('order')) {
            return array();
        }
        
        $sql = "SELECT 
                    CASE 
                        WHEN o.store_id = 0 THEN 'Online Store'
                        WHEN o.store_id > 0 THEN CONCAT('Branch ', o.store_id)
                        ELSE 'Direct Sales'
                    END as channel,
                    COALESCE(SUM(o.total), 0) as total_sales,
                    COUNT(*) as order_count,
                    COALESCE(AVG(o.total), 0) as avg_order_value
                FROM " . DB_PREFIX . "order o
                " . $where_clause . "
                AND o.order_status_id NOT IN (7)
                GROUP BY o.store_id
                ORDER BY total_sales DESC";
        
        $query = $this->safeQuery($sql);
        return $query && isset($query->rows) ? $query->rows : array();
    }
    
    /**
     * Get Company Health Gauges
     * مؤشرات صحة الشركة
     */
    public function getCompanyHealthGauges($filters = array()) {
        // Current Ratio (Current Assets / Current Liabilities)
        $current_ratio = $this->getCurrentRatio($filters);
        
        // Net Profit Margin
        $net_profit_margin = $this->getNetProfitMargin($filters);
        
        // Inventory Turnover
        $inventory_turnover = $this->getInventoryTurnover($filters);
        
        // Customer Satisfaction Score
        $customer_satisfaction = $this->getCustomerSatisfaction($filters);
        
        return array(
            'current_ratio' => $current_ratio,
            'net_profit_margin' => $net_profit_margin,
            'inventory_turnover' => $inventory_turnover,
            'customer_satisfaction' => $customer_satisfaction,
            'overall_health_score' => $this->calculateOverallHealthScore($current_ratio, $net_profit_margin, $inventory_turnover, $customer_satisfaction)
        );
    }
    
    /**
     * Get Geographic Sales Heatmap
     * خريطة المبيعات الجغرافية
     */
    public function getGeographicSalesHeatmap($filters = array()) {
        $date_filter = isset($filters['date_range']) ? $filters['date_range'] : 'month';
        
        $where_clause = "WHERE 1=1";
        
        // Date filter
        switch($date_filter) {
            case 'month':
                $where_clause .= " AND YEAR(o.date_added) = YEAR(CURDATE()) AND MONTH(o.date_added) = MONTH(CURDATE())";
                break;
            case 'quarter':
                $where_clause .= " AND o.date_added >= DATE_SUB(CURDATE(), INTERVAL 90 DAY)";
                break;
            case 'year':
                $where_clause .= " AND YEAR(o.date_added) = YEAR(CURDATE())";
                break;
        }
        
        if (!$this->tableExists('order') || !$this->tableExists('zone')) {
            return array();
        }
        
        $sql = "SELECT 
                    z.name as governorate,
                    COALESCE(SUM(o.total), 0) as total_sales,
                    COUNT(*) as order_count,
                    COALESCE(AVG(o.total), 0) as avg_order_value
                FROM " . DB_PREFIX . "order o
                LEFT JOIN " . DB_PREFIX . "zone z ON o.payment_zone_id = z.zone_id
                " . $where_clause . "
                AND o.order_status_id NOT IN (7)
                GROUP BY z.zone_id, z.name
                ORDER BY total_sales DESC";
        
        $query = $this->safeQuery($sql);
        return $query && isset($query->rows) ? $query->rows : array();
    }
    
    /**
     * Get Goal Tracking
     * تتبع الأهداف
     */
    public function getGoalTracking($filters = array()) {
        // This would typically come from a goals table
        // For now, we'll return sample data
        return array(
            'revenue_goal' => array(
                'target' => 1000000,
                'current' => 750000,
                'percentage' => 75,
                'remaining_days' => 45
            ),
            'customer_goal' => array(
                'target' => 5000,
                'current' => 3800,
                'percentage' => 76,
                'remaining_days' => 45
            ),
            'order_goal' => array(
                'target' => 10000,
                'current' => 7200,
                'percentage' => 72,
                'remaining_days' => 45
            )
        );
    }
    
    /**
     * Get Pending High-Value Approvals
     * الموافقات عالية القيمة المعلقة
     */
    public function getPendingHighValueApprovals($filters = array()) {
        $approvals = array();
        
        // Purchase Orders requiring approval
        if ($this->tableExists('purchase_order')) {
            $sql = "SELECT 
                        po_id,
                        supplier_name,
                        total_amount,
                        date_added,
                        'purchase_order' as type
                    FROM " . DB_PREFIX . "purchase_order
                    WHERE status = 'pending_approval'
                    AND total_amount > 50000
                    ORDER BY total_amount DESC
                    LIMIT 10";
            
            $query = $this->safeQuery($sql);
            if ($query && isset($query->rows)) {
                foreach ($query->rows as $row) {
                    $approvals[] = $row;
                }
            }
        }
        
        // Expense approvals
        if ($this->tableExists('expense')) {
            $sql = "SELECT 
                        expense_id,
                        description as supplier_name,
                        amount as total_amount,
                        date_added,
                        'expense' as type
                    FROM " . DB_PREFIX . "expense
                    WHERE status = 'pending_approval'
                    AND amount > 10000
                    ORDER BY amount DESC
                    LIMIT 10";
            
            $query = $this->safeQuery($sql);
            if ($query && isset($query->rows)) {
                foreach ($query->rows as $row) {
                    $approvals[] = $row;
                }
            }
        }
        
        // Sort by amount and limit to top 10
        usort($approvals, function($a, $b) {
            return $b['total_amount'] <=> $a['total_amount'];
        });
        
        return array_slice($approvals, 0, 10);
    }
    
    // Helper methods
    private function getInventoryValue($filters) {
        if (!$this->tableExists('product')) {
            return 0;
        }
        
        $sql = "SELECT COALESCE(SUM(quantity * cost), 0) as inventory_value
                FROM " . DB_PREFIX . "product
                WHERE status = 1";
        
        $query = $this->safeQuery($sql);
        return $query && isset($query->row['inventory_value']) ? $query->row['inventory_value'] : 0;
    }
    
    private function getCashFlow($filters) {
        // Simplified cash flow calculation
        // In a real system, this would be more complex
        return 0;
    }
    
    private function getConversionRate($filters) {
        if (!$this->tableExists('customer') || !$this->tableExists('order')) {
            return 0;
        }
        
        $sql = "SELECT 
                    CASE 
                        WHEN COUNT(DISTINCT c.customer_id) > 0 
                        THEN (COUNT(DISTINCT o.customer_id) / COUNT(DISTINCT c.customer_id)) * 100 
                        ELSE 0 
                    END as conversion_rate
                FROM " . DB_PREFIX . "customer c
                LEFT JOIN " . DB_PREFIX . "order o ON c.customer_id = o.customer_id
                WHERE o.order_status_id NOT IN (7)";
        
        $query = $this->safeQuery($sql);
        return $query && isset($query->row['conversion_rate']) ? round($query->row['conversion_rate'], 2) : 0;
    }
    
    private function getOperatingExpenses($filters) {
        // Simplified - in real system, this would come from expense tracking
        return 0;
    }
    
    private function getCurrentRatio($filters) {
        // Simplified calculation
        return 2.5; // Sample value
    }
    
    private function getNetProfitMargin($filters) {
        $pnl = $this->getProfitLossSummary($filters);
        return $pnl['net_margin'];
    }
    
    private function getInventoryTurnover($filters) {
        // Simplified calculation
        return 12; // Sample value - times per year
    }
    
    private function getCustomerSatisfaction($filters) {
        // Simplified - would come from customer feedback system
        return 4.2; // Sample value out of 5
    }
    
    private function calculateOverallHealthScore($current_ratio, $net_profit_margin, $inventory_turnover, $customer_satisfaction) {
        // Weighted average of health indicators
        $score = (
            ($current_ratio / 3) * 0.25 + // Current ratio (target: 3)
            ($net_profit_margin / 20) * 0.25 + // Net profit margin (target: 20%)
            ($inventory_turnover / 12) * 0.25 + // Inventory turnover (target: 12)
            ($customer_satisfaction / 5) * 0.25 // Customer satisfaction (target: 5)
        ) * 100;
        
        return round($score, 1);
    }
} 