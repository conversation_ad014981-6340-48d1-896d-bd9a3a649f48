<?php
/**
 * نموذج توحيد القوائم المالية المتقدم
 * يدعم معايير IFRS والمحاسبة المصرية
 * يتعامل مع الشركات التابعة والزميلة والمشاريع المشتركة
 */
class ModelAccountsConsolidation extends Model {

    /**
     * جلب قائمة الشركات التابعة
     */
    public function getSubsidiaries($data = array()) {
        $sql = "SELECT s.*, c.name as currency_name, c.symbol_left, c.symbol_right
                FROM " . DB_PREFIX . "consolidation_subsidiaries s
                LEFT JOIN " . DB_PREFIX . "currency c ON (s.functional_currency = c.code)
                WHERE s.status = 1";

        if (!empty($data['filter_name'])) {
            $sql .= " AND s.name LIKE '%" . $this->db->escape($data['filter_name']) . "%'";
        }

        if (!empty($data['filter_type'])) {
            $sql .= " AND s.subsidiary_type = '" . $this->db->escape($data['filter_type']) . "'";
        }

        $sql .= " ORDER BY s.name ASC";

        if (isset($data['start']) || isset($data['limit'])) {
            if ($data['start'] < 0) {
                $data['start'] = 0;
            }

            if ($data['limit'] < 1) {
                $data['limit'] = 20;
            }

            $sql .= " LIMIT " . (int)$data['start'] . "," . (int)$data['limit'];
        }

        $query = $this->db->query($sql);

        return $query->rows;
    }

    /**
     * جلب آخر عمليات التوحيد
     */
    public function getRecentConsolidations($limit = 10) {
        $query = $this->db->query("
            SELECT c.*, u.username as created_by_name
            FROM " . DB_PREFIX . "consolidation_reports c
            LEFT JOIN " . DB_PREFIX . "user u ON (c.created_by = u.user_id)
            ORDER BY c.date_created DESC
            LIMIT " . (int)$limit
        );

        return $query->rows;
    }

    /**
     * جلب إعدادات التوحيد
     */
    public function getConsolidationSettings() {
        $query = $this->db->query("
            SELECT * FROM " . DB_PREFIX . "consolidation_settings
            WHERE status = 1
            ORDER BY sort_order ASC
        ");

        $settings = array();
        foreach ($query->rows as $row) {
            $settings[$row['setting_key']] = $row['setting_value'];
        }

        return $settings;
    }

    /**
     * توليد تقرير التوحيد
     */
    public function generateConsolidation($data) {
        $this->db->query("START TRANSACTION");

        try {
            // إنشاء سجل التوحيد الرئيسي
            $consolidation_id = $this->createConsolidationRecord($data);

            // جلب البيانات المالية للشركات المحددة
            $financial_data = $this->getFinancialDataForPeriod($data);

            // تطبيق تعديلات التوحيد
            $this->applyConsolidationAdjustments($consolidation_id, $financial_data, $data);

            // حساب القوائم الموحدة
            $consolidated_statements = $this->calculateConsolidatedStatements($consolidation_id, $financial_data, $data);

            // حفظ النتائج
            $this->saveConsolidationResults($consolidation_id, $consolidated_statements);

            $this->db->query("COMMIT");

            return $consolidation_id;

        } catch (Exception $e) {
            $this->db->query("ROLLBACK");
            throw $e;
        }
    }

    /**
     * إنشاء سجل التوحيد الرئيسي
     */
    private function createConsolidationRecord($data) {
        $reference = 'CONS-' . date('Y') . '-' . str_pad($this->getNextConsolidationNumber(), 6, '0', STR_PAD_LEFT);

        $this->db->query("
            INSERT INTO " . DB_PREFIX . "consolidation_reports SET
            reference = '" . $this->db->escape($reference) . "',
            period_start = '" . $this->db->escape($data['period_start']) . "',
            period_end = '" . $this->db->escape($data['period_end']) . "',
            consolidation_method = '" . $this->db->escape($data['consolidation_method']) . "',
            reporting_currency = '" . $this->db->escape($data['currency']) . "',
            include_adjustments = " . (isset($data['include_adjustments']) ? 1 : 0) . ",
            status = 'processing',
            created_by = " . (int)$this->user->getId() . ",
            date_created = NOW()
        ");

        $consolidation_id = $this->db->getLastId();

        // حفظ الشركات المشاركة في التوحيد
        if (!empty($data['subsidiaries'])) {
            foreach ($data['subsidiaries'] as $subsidiary_id) {
                $this->db->query("
                    INSERT INTO " . DB_PREFIX . "consolidation_subsidiaries_included SET
                    consolidation_id = " . (int)$consolidation_id . ",
                    subsidiary_id = " . (int)$subsidiary_id . ",
                    date_added = NOW()
                ");
            }
        }

        return $consolidation_id;
    }

    /**
     * جلب البيانات المالية للفترة المحددة
     */
    private function getFinancialDataForPeriod($data) {
        $financial_data = array();

        // جلب بيانات الشركة الأم
        $parent_data = $this->getCompanyFinancialData(0, $data['period_start'], $data['period_end']);
        $financial_data['parent'] = $parent_data;

        // جلب بيانات الشركات التابعة
        if (!empty($data['subsidiaries'])) {
            foreach ($data['subsidiaries'] as $subsidiary_id) {
                $subsidiary_data = $this->getCompanyFinancialData($subsidiary_id, $data['period_start'], $data['period_end']);
                $financial_data['subsidiaries'][$subsidiary_id] = $subsidiary_data;
            }
        }

        return $financial_data;
    }

    /**
     * جلب البيانات المالية لشركة محددة
     */
    private function getCompanyFinancialData($company_id, $period_start, $period_end) {
        $data = array();

        // جلب أرصدة الحسابات
        $query = $this->db->query("
            SELECT 
                a.account_code,
                a.account_name,
                a.account_type,
                COALESCE(SUM(CASE WHEN je.is_debit = 1 THEN je.amount ELSE -je.amount END), 0) as balance
            FROM " . DB_PREFIX . "accounts a
            LEFT JOIN " . DB_PREFIX . "journal_entries je ON (a.account_code = je.account_code)
            LEFT JOIN " . DB_PREFIX . "journals j ON (je.journal_id = j.journal_id)
            WHERE j.thedate BETWEEN '" . $this->db->escape($period_start) . "' 
                AND '" . $this->db->escape($period_end) . "'
                AND j.status = 'posted'
                " . ($company_id > 0 ? "AND j.company_id = " . (int)$company_id : "") . "
            GROUP BY a.account_code, a.account_name, a.account_type
            ORDER BY a.account_code
        ");

        $data['accounts'] = $query->rows;

        // جلب المعاملات بين الشركات
        if ($company_id > 0) {
            $intercompany_query = $this->db->query("
                SELECT 
                    ic.transaction_type,
                    ic.amount,
                    ic.description,
                    ic.related_company_id
                FROM " . DB_PREFIX . "intercompany_transactions ic
                WHERE ic.company_id = " . (int)$company_id . "
                    AND ic.transaction_date BETWEEN '" . $this->db->escape($period_start) . "' 
                    AND '" . $this->db->escape($period_end) . "'
                    AND ic.status = 'confirmed'
            ");

            $data['intercompany_transactions'] = $intercompany_query->rows;
        }

        return $data;
    }

    /**
     * تطبيق تعديلات التوحيد
     */
    private function applyConsolidationAdjustments($consolidation_id, &$financial_data, $data) {
        // إلغاء المعاملات بين الشركات
        $this->eliminateIntercompanyTransactions($financial_data);

        // تعديلات القيمة العادلة
        $this->applyFairValueAdjustments($financial_data, $data);

        // تعديلات العملة
        if ($data['currency'] != $this->config->get('config_currency')) {
            $this->applyCurrencyTranslation($financial_data, $data['currency']);
        }

        // تعديلات الشهرة
        $this->calculateGoodwill($financial_data, $data);
    }

    /**
     * إلغاء المعاملات بين الشركات
     */
    private function eliminateIntercompanyTransactions(&$financial_data) {
        // تنفيذ منطق إلغاء المعاملات بين الشركات
        // هذا يتطلب مطابقة المعاملات وإلغاؤها
        
        foreach ($financial_data['subsidiaries'] as $subsidiary_id => &$subsidiary_data) {
            if (isset($subsidiary_data['intercompany_transactions'])) {
                foreach ($subsidiary_data['intercompany_transactions'] as $transaction) {
                    // البحث عن المعاملة المقابلة وإلغاؤها
                    $this->eliminateMatchingTransaction($financial_data, $transaction, $subsidiary_id);
                }
            }
        }
    }

    /**
     * حساب القوائم الموحدة
     */
    private function calculateConsolidatedStatements($consolidation_id, $financial_data, $data) {
        $consolidated = array();

        // توحيد الميزانية العمومية
        $consolidated['balance_sheet'] = $this->consolidateBalanceSheet($financial_data);

        // توحيد قائمة الدخل
        $consolidated['income_statement'] = $this->consolidateIncomeStatement($financial_data);

        // توحيد قائمة التدفقات النقدية
        $consolidated['cash_flow'] = $this->consolidateCashFlow($financial_data);

        // توحيد قائمة التغيرات في حقوق الملكية
        $consolidated['equity_changes'] = $this->consolidateEquityChanges($financial_data);

        return $consolidated;
    }

    /**
     * حفظ نتائج التوحيد
     */
    private function saveConsolidationResults($consolidation_id, $consolidated_statements) {
        foreach ($consolidated_statements as $statement_type => $statement_data) {
            foreach ($statement_data as $account_code => $account_data) {
                $this->db->query("
                    INSERT INTO " . DB_PREFIX . "consolidation_results SET
                    consolidation_id = " . (int)$consolidation_id . ",
                    statement_type = '" . $this->db->escape($statement_type) . "',
                    account_code = '" . $this->db->escape($account_code) . "',
                    account_name = '" . $this->db->escape($account_data['name']) . "',
                    amount = " . (float)$account_data['amount'] . ",
                    date_added = NOW()
                ");
            }
        }

        // تحديث حالة التوحيد
        $this->db->query("
            UPDATE " . DB_PREFIX . "consolidation_reports SET
            status = 'completed',
            date_completed = NOW()
            WHERE consolidation_id = " . (int)$consolidation_id
        );
    }

    /**
     * جلب رقم التوحيد التالي
     */
    private function getNextConsolidationNumber() {
        $query = $this->db->query("
            SELECT MAX(CAST(SUBSTRING(reference, 10) AS UNSIGNED)) as max_number
            FROM " . DB_PREFIX . "consolidation_reports
            WHERE reference LIKE 'CONS-" . date('Y') . "-%'
        ");

        return ($query->row['max_number'] ?? 0) + 1;
    }

    /**
     * جلب تفاصيل التوحيد
     */
    public function getConsolidation($consolidation_id) {
        $query = $this->db->query("
            SELECT c.*, u.username as created_by_name
            FROM " . DB_PREFIX . "consolidation_reports c
            LEFT JOIN " . DB_PREFIX . "user u ON (c.created_by = u.user_id)
            WHERE c.consolidation_id = " . (int)$consolidation_id
        );

        return $query->row;
    }

    /**
     * جلب تفاصيل نتائج التوحيد
     */
    public function getConsolidationDetails($consolidation_id) {
        $query = $this->db->query("
            SELECT *
            FROM " . DB_PREFIX . "consolidation_results
            WHERE consolidation_id = " . (int)$consolidation_id
            ORDER BY statement_type, account_code
        ");

        return $query->rows;
    }

    /**
     * جلب تعديلات التوحيد
     */
    public function getConsolidationAdjustments($consolidation_id) {
        $query = $this->db->query("
            SELECT *
            FROM " . DB_PREFIX . "consolidation_adjustments
            WHERE consolidation_id = " . (int)$consolidation_id
            ORDER BY date_added DESC
        ");

        return $query->rows;
    }

    /**
     * حفظ تعديلات التوحيد
     */
    public function saveAdjustments($data) {
        $this->db->query("START TRANSACTION");

        try {
            foreach ($data['adjustments'] as $adjustment) {
                $this->db->query("
                    INSERT INTO " . DB_PREFIX . "consolidation_adjustments SET
                    consolidation_id = " . (int)$data['consolidation_id'] . ",
                    adjustment_type = '" . $this->db->escape($adjustment['type']) . "',
                    account_code = '" . $this->db->escape($adjustment['account_code']) . "',
                    debit_amount = " . (float)$adjustment['debit_amount'] . ",
                    credit_amount = " . (float)$adjustment['credit_amount'] . ",
                    description = '" . $this->db->escape($adjustment['description']) . "',
                    notes = '" . $this->db->escape($data['notes']) . "',
                    created_by = " . (int)$this->user->getId() . ",
                    date_added = NOW()
                ");
            }

            $this->db->query("COMMIT");
            return true;

        } catch (Exception $e) {
            $this->db->query("ROLLBACK");
            throw $e;
        }
    }

    /**
     * تصدير التوحيد
     */
    public function exportConsolidation($consolidation_id, $format = 'pdf') {
        $consolidation = $this->getConsolidation($consolidation_id);
        $details = $this->getConsolidationDetails($consolidation_id);

        if (!$consolidation) {
            return false;
        }

        // تحضير البيانات للتصدير
        $export_data = array(
            'consolidation' => $consolidation,
            'details' => $details,
            'format' => $format
        );

        // تحديد نوع التصدير
        switch ($format) {
            case 'pdf':
                return $this->exportToPDF($export_data);
            case 'excel':
                return $this->exportToExcel($export_data);
            case 'csv':
                return $this->exportToCSV($export_data);
            default:
                return false;
        }
    }

    /**
     * التصدير إلى PDF
     */
    private function exportToPDF($data) {
        // تنفيذ التصدير إلى PDF
        // يتطلب مكتبة PDF مثل TCPDF أو FPDF
        
        return array(
            'filename' => 'consolidation_' . $data['consolidation']['reference'] . '.pdf',
            'mime_type' => 'application/pdf',
            'content' => '' // محتوى PDF
        );
    }

    /**
     * التصدير إلى Excel
     */
    private function exportToExcel($data) {
        // تنفيذ التصدير إلى Excel
        // يتطلب مكتبة مثل PhpSpreadsheet
        
        return array(
            'filename' => 'consolidation_' . $data['consolidation']['reference'] . '.xlsx',
            'mime_type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'content' => '' // محتوى Excel
        );
    }

    /**
     * التصدير إلى CSV
     */
    private function exportToCSV($data) {
        $csv_content = '';
        
        // إضافة headers
        $csv_content .= "Account Code,Account Name,Statement Type,Amount\n";
        
        // إضافة البيانات
        foreach ($data['details'] as $detail) {
            $csv_content .= '"' . $detail['account_code'] . '","' . 
                           $detail['account_name'] . '","' . 
                           $detail['statement_type'] . '","' . 
                           $detail['amount'] . '"' . "\n";
        }
        
        return array(
            'filename' => 'consolidation_' . $data['consolidation']['reference'] . '.csv',
            'mime_type' => 'text/csv',
            'content' => $csv_content
        );
    }
}
