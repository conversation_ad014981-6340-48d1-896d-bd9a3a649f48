{{ header }}{{ column_left }}

<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="submit" form="user\user-form" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fas fa-save"></i></button>
        <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-light"><i class="fas fa-reply"></i></a>
      </div>
      <h1>{{{{ heading_title }}}}</h1>
      <ol class="breadcrumb">
        {{% for breadcrumb in breadcrumbs %}}
          <li class="breadcrumb-item"><a href="{{{{ breadcrumb.href }}}}">{{{{ breadcrumb.text }}}}</a></li>
        {{% endfor %}}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    {{% if success %}}
      <div class="alert alert-success alert-dismissible"><i class="fas fa-check-circle"></i> {{{{ success }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    {{% if error_warning %}}
      <div class="alert alert-danger alert-dismissible"><i class="fas fa-exclamation-circle"></i> {{{{ error_warning }}}}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {{% endif %}}
    
    <div class="card">
      <div class="card-header"><i class="fas fa-{% if screen_type == 'list' %}list{% elif screen_type == 'form' %}edit{% elif screen_type == 'report' %}chart-bar{% elif screen_type == 'settings' %}cog{% else %}pencil-alt{% endif %}"></i> {{{{ text_{screen_name} }}}}</div>
      <div class="card-body">
        <form id="user\user-form" action="{{ action }}" method="post" data-oc-toggle="ajax" class="form-horizontal">

          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-activities">{{ text_activities }}</label>
            <div class="col-sm-10">
              <input type="text" name="activities" value="{{ activities }}" placeholder="{{ text_activities }}" id="input-activities" class="form-control" />
              {% if error_activities %}
                <div class="invalid-feedback">{{ error_activities }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-add">{{ text_add }}</label>
            <div class="col-sm-10">
              <input type="text" name="add" value="{{ add }}" placeholder="{{ text_add }}" id="input-add" class="form-control" />
              {% if error_add %}
                <div class="invalid-feedback">{{ error_add }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-confirm">{{ text_confirm }}</label>
            <div class="col-sm-10">
              <input type="text" name="confirm" value="{{ confirm }}" placeholder="{{ text_confirm }}" id="input-confirm" class="form-control" />
              {% if error_confirm %}
                <div class="invalid-feedback">{{ error_confirm }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-delete">{{ text_delete }}</label>
            <div class="col-sm-10">
              <input type="text" name="delete" value="{{ delete }}" placeholder="{{ text_delete }}" id="input-delete" class="form-control" />
              {% if error_delete %}
                <div class="invalid-feedback">{{ error_delete }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-email">{{ text_email }}</label>
            <div class="col-sm-10">
              <input type="text" name="email" value="{{ email }}" placeholder="{{ text_email }}" id="input-email" class="form-control" />
              {% if error_email %}
                <div class="invalid-feedback">{{ error_email }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_confirm">{{ text_error_confirm }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_confirm" value="{{ error_confirm }}" placeholder="{{ text_error_confirm }}" id="input-error_confirm" class="form-control" />
              {% if error_error_confirm %}
                <div class="invalid-feedback">{{ error_error_confirm }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_email">{{ text_error_email }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_email" value="{{ error_email }}" placeholder="{{ text_error_email }}" id="input-error_email" class="form-control" />
              {% if error_error_email %}
                <div class="invalid-feedback">{{ error_error_email }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_firstname">{{ text_error_firstname }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_firstname" value="{{ error_firstname }}" placeholder="{{ text_error_firstname }}" id="input-error_firstname" class="form-control" />
              {% if error_error_firstname %}
                <div class="invalid-feedback">{{ error_error_firstname }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_lastname">{{ text_error_lastname }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_lastname" value="{{ error_lastname }}" placeholder="{{ text_error_lastname }}" id="input-error_lastname" class="form-control" />
              {% if error_error_lastname %}
                <div class="invalid-feedback">{{ error_error_lastname }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_password">{{ text_error_password }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_password" value="{{ error_password }}" placeholder="{{ text_error_password }}" id="input-error_password" class="form-control" />
              {% if error_error_password %}
                <div class="invalid-feedback">{{ error_error_password }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-error_username">{{ text_error_username }}</label>
            <div class="col-sm-10">
              <input type="text" name="error_username" value="{{ error_username }}" placeholder="{{ text_error_username }}" id="input-error_username" class="form-control" />
              {% if error_error_username %}
                <div class="invalid-feedback">{{ error_error_username }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-export">{{ text_export }}</label>
            <div class="col-sm-10">
              <input type="text" name="export" value="{{ export }}" placeholder="{{ text_export }}" id="input-export" class="form-control" />
              {% if error_export %}
                <div class="invalid-feedback">{{ error_export }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-firstname">{{ text_firstname }}</label>
            <div class="col-sm-10">
              <input type="text" name="firstname" value="{{ firstname }}" placeholder="{{ text_firstname }}" id="input-firstname" class="form-control" />
              {% if error_firstname %}
                <div class="invalid-feedback">{{ error_firstname }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-heading_title">{{ text_heading_title }}</label>
            <div class="col-sm-10">
              <input type="text" name="heading_title" value="{{ heading_title }}" placeholder="{{ text_heading_title }}" id="input-heading_title" class="form-control" />
              {% if error_heading_title %}
                <div class="invalid-feedback">{{ error_heading_title }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-image">{{ text_image }}</label>
            <div class="col-sm-10">
              <input type="text" name="image" value="{{ image }}" placeholder="{{ text_image }}" id="input-image" class="form-control" />
              {% if error_image %}
                <div class="invalid-feedback">{{ error_image }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-import">{{ text_import }}</label>
            <div class="col-sm-10">
              <input type="text" name="import" value="{{ import }}" placeholder="{{ text_import }}" id="input-import" class="form-control" />
              {% if error_import %}
                <div class="invalid-feedback">{{ error_import }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-lastname">{{ text_lastname }}</label>
            <div class="col-sm-10">
              <input type="text" name="lastname" value="{{ lastname }}" placeholder="{{ text_lastname }}" id="input-lastname" class="form-control" />
              {% if error_lastname %}
                <div class="invalid-feedback">{{ error_lastname }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-order">{{ text_order }}</label>
            <div class="col-sm-10">
              <input type="text" name="order" value="{{ order }}" placeholder="{{ text_order }}" id="input-order" class="form-control" />
              {% if error_order %}
                <div class="invalid-feedback">{{ error_order }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-pagination">{{ text_pagination }}</label>
            <div class="col-sm-10">
              <input type="text" name="pagination" value="{{ pagination }}" placeholder="{{ text_pagination }}" id="input-pagination" class="form-control" />
              {% if error_pagination %}
                <div class="invalid-feedback">{{ error_pagination }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-password">{{ text_password }}</label>
            <div class="col-sm-10">
              <input type="text" name="password" value="{{ password }}" placeholder="{{ text_password }}" id="input-password" class="form-control" />
              {% if error_password %}
                <div class="invalid-feedback">{{ error_password }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-placeholder">{{ text_placeholder }}</label>
            <div class="col-sm-10">
              <input type="text" name="placeholder" value="{{ placeholder }}" placeholder="{{ text_placeholder }}" id="input-placeholder" class="form-control" />
              {% if error_placeholder %}
                <div class="invalid-feedback">{{ error_placeholder }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-recent_activities">{{ text_recent_activities }}</label>
            <div class="col-sm-10">
              <input type="text" name="recent_activities" value="{{ recent_activities }}" placeholder="{{ text_recent_activities }}" id="input-recent_activities" class="form-control" />
              {% if error_recent_activities %}
                <div class="invalid-feedback">{{ error_recent_activities }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-results">{{ text_results }}</label>
            <div class="col-sm-10">
              <input type="text" name="results" value="{{ results }}" placeholder="{{ text_results }}" id="input-results" class="form-control" />
              {% if error_results %}
                <div class="invalid-feedback">{{ error_results }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-selected">{{ text_selected }}</label>
            <div class="col-sm-10">
              <input type="text" name="selected" value="{{ selected }}" placeholder="{{ text_selected }}" id="input-selected" class="form-control" />
              {% if error_selected %}
                <div class="invalid-feedback">{{ error_selected }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort">{{ text_sort }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort" value="{{ sort }}" placeholder="{{ text_sort }}" id="input-sort" class="form-control" />
              {% if error_sort %}
                <div class="invalid-feedback">{{ error_sort }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_date_added">{{ text_sort_date_added }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_date_added" value="{{ sort_date_added }}" placeholder="{{ text_sort_date_added }}" id="input-sort_date_added" class="form-control" />
              {% if error_sort_date_added %}
                <div class="invalid-feedback">{{ error_sort_date_added }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_status">{{ text_sort_status }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_status" value="{{ sort_status }}" placeholder="{{ text_sort_status }}" id="input-sort_status" class="form-control" />
              {% if error_sort_status %}
                <div class="invalid-feedback">{{ error_sort_status }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-sort_username">{{ text_sort_username }}</label>
            <div class="col-sm-10">
              <input type="text" name="sort_username" value="{{ sort_username }}" placeholder="{{ text_sort_username }}" id="input-sort_username" class="form-control" />
              {% if error_sort_username %}
                <div class="invalid-feedback">{{ error_sort_username }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-statistics">{{ text_statistics }}</label>
            <div class="col-sm-10">
              <input type="text" name="statistics" value="{{ statistics }}" placeholder="{{ text_statistics }}" id="input-statistics" class="form-control" />
              {% if error_statistics %}
                <div class="invalid-feedback">{{ error_statistics }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-status">{{ text_status }}</label>
            <div class="col-sm-10">
              <input type="text" name="status" value="{{ status }}" placeholder="{{ text_status }}" id="input-status" class="form-control" />
              {% if error_status %}
                <div class="invalid-feedback">{{ error_status }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-text_form">{{ text_text_form }}</label>
            <div class="col-sm-10">
              <input type="text" name="text_form" value="{{ text_form }}" placeholder="{{ text_text_form }}" id="input-text_form" class="form-control" />
              {% if error_text_form %}
                <div class="invalid-feedback">{{ error_text_form }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-thumb">{{ text_thumb }}</label>
            <div class="col-sm-10">
              <input type="text" name="thumb" value="{{ thumb }}" placeholder="{{ text_thumb }}" id="input-thumb" class="form-control" />
              {% if error_thumb %}
                <div class="invalid-feedback">{{ error_thumb }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-total_activities">{{ text_total_activities }}</label>
            <div class="col-sm-10">
              <input type="text" name="total_activities" value="{{ total_activities }}" placeholder="{{ text_total_activities }}" id="input-total_activities" class="form-control" />
              {% if error_total_activities %}
                <div class="invalid-feedback">{{ error_total_activities }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_group_id">{{ text_user_group_id }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_group_id" value="{{ user_group_id }}" placeholder="{{ text_user_group_id }}" id="input-user_group_id" class="form-control" />
              {% if error_user_group_id %}
                <div class="invalid-feedback">{{ error_user_group_id }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_groups">{{ text_user_groups }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_groups" value="{{ user_groups }}" placeholder="{{ text_user_groups }}" id="input-user_groups" class="form-control" />
              {% if error_user_groups %}
                <div class="invalid-feedback">{{ error_user_groups }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_info">{{ text_user_info }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_info" value="{{ user_info }}" placeholder="{{ text_user_info }}" id="input-user_info" class="form-control" />
              {% if error_user_info %}
                <div class="invalid-feedback">{{ error_user_info }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-user_statistics">{{ text_user_statistics }}</label>
            <div class="col-sm-10">
              <input type="text" name="user_statistics" value="{{ user_statistics }}" placeholder="{{ text_user_statistics }}" id="input-user_statistics" class="form-control" />
              {% if error_user_statistics %}
                <div class="invalid-feedback">{{ error_user_statistics }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-username">{{ text_username }}</label>
            <div class="col-sm-10">
              <input type="text" name="username" value="{{ username }}" placeholder="{{ text_username }}" id="input-username" class="form-control" />
              {% if error_username %}
                <div class="invalid-feedback">{{ error_username }}</div>
              {% endif %}
            </div>
          </div>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label" for="input-users">{{ text_users }}</label>
            <div class="col-sm-10">
              <input type="text" name="users" value="{{ users }}" placeholder="{{ text_users }}" id="input-users" class="form-control" />
              {% if error_users %}
                <div class="invalid-feedback">{{ error_users }}</div>
              {% endif %}
            </div>
          </div>
          <input type="hidden" name="user_token" value="{{{{ user_token }}}}">
        </form>

      </div>
    </div>
  </div>
</div>

{{{{ footer }}}}