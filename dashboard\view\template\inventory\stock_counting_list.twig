{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="pull-right">
        <a href="{{ add }}" data-toggle="tooltip" title="{{ button_add }}" class="btn btn-primary">
          <i class="fa fa-plus"></i>
        </a>
        <div class="btn-group">
          <button type="button" class="btn btn-success dropdown-toggle" data-toggle="dropdown">
            <i class="fa fa-download"></i> {{ button_export_excel }}
            <span class="caret"></span>
          </button>
          <ul class="dropdown-menu dropdown-menu-right">
            <li><a href="{{ export_excel }}"><i class="fa fa-file-excel-o"></i> {{ button_export_excel }}</a></li>
            <li><a href="{{ export_pdf }}"><i class="fa fa-file-pdf-o"></i> {{ button_export_pdf }}</a></li>
            <li class="divider"></li>
            <li><a href="{{ print }}" target="_blank"><i class="fa fa-print"></i> {{ button_print }}</a></li>
          </ul>
        </div>
        <button type="button" data-toggle="tooltip" title="{{ button_delete }}" class="btn btn-danger" onclick="confirm('{{ text_confirm }}') ? $('#form-stock-counting').submit() : false;">
          <i class="fa fa-trash-o"></i>
        </button>
        <a href="{{ refresh }}" data-toggle="tooltip" title="{{ button_refresh }}" class="btn btn-default">
          <i class="fa fa-refresh"></i>
        </a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ul class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
        <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ul>
    </div>
  </div>
  
  <div class="container-fluid">
    {% if error_warning %}
    <div class="alert alert-danger alert-dismissible">
      <i class="fa fa-exclamation-circle"></i> {{ error_warning }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    
    {% if success %}
    <div class="alert alert-success alert-dismissible">
      <i class="fa fa-check-circle"></i> {{ success }}
      <button type="button" class="close" data-dismiss="alert">&times;</button>
    </div>
    {% endif %}
    
    <!-- ملخص الجرد -->
    <div class="row">
      <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="panel panel-primary">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-clipboard fa-2x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ summary.total_countings }}</div>
                <div>{{ text_total_countings }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="panel panel-default">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-file-o fa-2x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ summary.draft_count }}</div>
                <div>{{ text_draft_count }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="panel panel-warning">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-clock-o fa-2x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ summary.in_progress_count }}</div>
                <div>{{ text_in_progress_count }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="panel panel-info">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-check fa-2x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ summary.completed_count }}</div>
                <div>{{ text_completed_count }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="panel panel-success">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-check-circle fa-2x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ summary.posted_count }}</div>
                <div>{{ text_posted_count }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="panel panel-purple">
          <div class="panel-heading">
            <div class="row">
              <div class="col-xs-3">
                <i class="fa fa-calculator fa-2x"></i>
              </div>
              <div class="col-xs-9 text-right">
                <div class="huge">{{ summary.avg_items_per_counting }}</div>
                <div>{{ text_avg_items_per_counting }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- الفلاتر المتقدمة -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title">
          <i class="fa fa-filter"></i> {{ text_advanced_filters }}
          <button type="button" class="btn btn-xs btn-default pull-right" data-toggle="collapse" data-target="#filter-panel">
            <i class="fa fa-chevron-down"></i>
          </button>
        </h3>
      </div>
      <div id="filter-panel" class="panel-collapse collapse">
        <div class="panel-body">
          <form method="get" id="filter-form">
            <input type="hidden" name="route" value="inventory/stock_counting" />
            <input type="hidden" name="user_token" value="{{ user_token }}" />
            
            <div class="row">
              <div class="col-md-3">
                <div class="form-group">
                  <label for="filter_counting_number">{{ entry_filter_counting_number }}</label>
                  <input type="text" name="filter_counting_number" value="{{ filter_counting_number }}" placeholder="{{ entry_filter_counting_number }}" id="filter_counting_number" class="form-control" />
                </div>
              </div>
              
              <div class="col-md-3">
                <div class="form-group">
                  <label for="filter_counting_name">{{ entry_filter_counting_name }}</label>
                  <input type="text" name="filter_counting_name" value="{{ filter_counting_name }}" placeholder="{{ entry_filter_counting_name }}" id="filter_counting_name" class="form-control" />
                </div>
              </div>
              
              <div class="col-md-3">
                <div class="form-group">
                  <label for="filter_status">{{ entry_filter_status }}</label>
                  <select name="filter_status" id="filter_status" class="form-control">
                    {% for option in status_options %}
                    <option value="{{ option.value }}"{% if option.value == filter_status %} selected="selected"{% endif %}>{{ option.text }}</option>
                    {% endfor %}
                  </select>
                </div>
              </div>
              
              <div class="col-md-3">
                <div class="form-group">
                  <label for="filter_counting_type">{{ entry_filter_counting_type }}</label>
                  <select name="filter_counting_type" id="filter_counting_type" class="form-control">
                    {% for option in counting_type_options %}
                    <option value="{{ option.value }}"{% if option.value == filter_counting_type %} selected="selected"{% endif %}>{{ option.text }}</option>
                    {% endfor %}
                  </select>
                </div>
              </div>
            </div>
            
            <div class="row">
              <div class="col-md-3">
                <div class="form-group">
                  <label for="filter_branch_id">{{ entry_filter_branch }}</label>
                  <select name="filter_branch_id" id="filter_branch_id" class="form-control">
                    <option value="">{{ text_all }}</option>
                    {% for branch in branches %}
                    <option value="{{ branch.branch_id }}"{% if branch.branch_id == filter_branch_id %} selected="selected"{% endif %}>{{ branch.name }}</option>
                    {% endfor %}
                  </select>
                </div>
              </div>
              
              <div class="col-md-3">
                <div class="form-group">
                  <label for="filter_date_from">{{ entry_filter_date_from }}</label>
                  <input type="date" name="filter_date_from" value="{{ filter_date_from }}" id="filter_date_from" class="form-control" />
                </div>
              </div>
              
              <div class="col-md-3">
                <div class="form-group">
                  <label for="filter_date_to">{{ entry_filter_date_to }}</label>
                  <input type="date" name="filter_date_to" value="{{ filter_date_to }}" id="filter_date_to" class="form-control" />
                </div>
              </div>
              
              <div class="col-md-3">
                <div class="form-group">
                  <label>&nbsp;</label>
                  <div>
                    <button type="submit" class="btn btn-primary"><i class="fa fa-filter"></i> {{ button_filter }}</button>
                    <a href="{{ refresh }}" class="btn btn-default"><i class="fa fa-refresh"></i> {{ button_clear }}</a>
                  </div>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
    
    <!-- جدول الجرد المخزني -->
    <div class="panel panel-default">
      <div class="panel-heading">
        <h3 class="panel-title"><i class="fa fa-list"></i> {{ text_list }}</h3>
      </div>
      <div class="panel-body">
        <form action="{{ delete }}" method="post" enctype="multipart/form-data" id="form-stock-counting">
          <div class="table-responsive">
            <table class="table table-bordered table-hover">
              <thead>
                <tr>
                  <td style="width: 1px;" class="text-center">
                    <input type="checkbox" onclick="$('input[name*=\'selected\']').prop('checked', this.checked);" />
                  </td>
                  <td>{{ column_counting_number }}</td>
                  <td>{{ column_counting_name }}</td>
                  <td>{{ column_counting_type }}</td>
                  <td class="text-center">{{ column_status }}</td>
                  <td>{{ column_branch }}</td>
                  <td class="text-center">{{ column_progress }}</td>
                  <td class="text-right">{{ column_total_variance_value }}</td>
                  <td>{{ column_counting_date }}</td>
                  <td class="text-center">{{ column_action }}</td>
                </tr>
              </thead>
              <tbody>
                {% if stock_countings %}
                {% for counting in stock_countings %}
                <tr>
                  <td class="text-center">
                    {% if counting.status == 'draft' %}
                    <input type="checkbox" name="selected[]" value="{{ counting.counting_id }}" />
                    {% endif %}
                  </td>
                  <td>
                    <strong>{{ counting.counting_number }}</strong>
                  </td>
                  <td>
                    {{ counting.counting_name }}
                    {% if counting.notes %}
                    <br><small class="text-muted">{{ counting.notes }}</small>
                    {% endif %}
                  </td>
                  <td>
                    <span class="label label-info">{{ counting.counting_type_text }}</span>
                  </td>
                  <td class="text-center">
                    <span class="label label-{{ counting.status_class }}">{{ counting.status_text }}</span>
                  </td>
                  <td>
                    {{ counting.branch_name }}
                    <br><small class="text-muted">{{ counting.branch_type }}</small>
                  </td>
                  <td class="text-center">
                    <div class="progress">
                      <div class="progress-bar progress-bar-{{ counting.progress_class }}" role="progressbar" style="width: {{ counting.progress_percentage }}%">
                        {{ counting.progress_percentage }}%
                      </div>
                    </div>
                    <small>{{ counting.counted_items }} / {{ counting.total_items }}</small>
                  </td>
                  <td class="text-right">
                    {% if counting.total_variance_value != '0.00' %}
                    <span class="text-{{ counting.variance_class }}">{{ counting.total_variance_value }}</span>
                    {% else %}
                    <span class="text-muted">{{ counting.total_variance_value }}</span>
                    {% endif %}
                  </td>
                  <td>
                    {{ counting.counting_date }}
                    <br><small class="text-muted">{{ counting.user_name }}</small>
                  </td>
                  <td class="text-center">
                    <div class="btn-group">
                      <a href="{{ counting.view }}" data-toggle="tooltip" title="{{ button_view }}" class="btn btn-info btn-xs">
                        <i class="fa fa-eye"></i>
                      </a>
                      {% if counting.status == 'draft' or counting.status == 'in_progress' %}
                      <a href="{{ counting.count }}" data-toggle="tooltip" title="{{ button_count }}" class="btn btn-warning btn-xs">
                        <i class="fa fa-clipboard"></i>
                      </a>
                      {% endif %}
                      {% if counting.status == 'draft' %}
                      <a href="{{ counting.edit }}" data-toggle="tooltip" title="{{ button_edit }}" class="btn btn-primary btn-xs">
                        <i class="fa fa-pencil"></i>
                      </a>
                      {% endif %}
                    </div>
                  </td>
                </tr>
                {% endfor %}
                {% else %}
                <tr>
                  <td class="text-center" colspan="10">{{ text_no_results }}</td>
                </tr>
                {% endif %}
              </tbody>
            </table>
          </div>
        </form>
        
        <div class="row">
          <div class="col-sm-6 text-left">{{ pagination }}</div>
          <div class="col-sm-6 text-right">{{ results }}</div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
.huge {
    font-size: 28px;
}

.panel-purple {
    border-color: #9b59b6;
}

.panel-purple > .panel-heading {
    border-color: #9b59b6;
    color: white;
    background-color: #9b59b6;
}

.progress {
    margin-bottom: 5px;
}

.progress-bar-success {
    background-color: #5cb85c;
}

.progress-bar-info {
    background-color: #5bc0de;
}

.progress-bar-warning {
    background-color: #f0ad4e;
}

.progress-bar-danger {
    background-color: #d9534f;
}

.table > tbody > tr > td {
    vertical-align: middle;
}

.counting-draft {
    background-color: #f9f9f9;
}

.counting-in-progress {
    background-color: #fff3cd;
}

.counting-completed {
    background-color: #d1ecf1;
}

.counting-posted {
    background-color: #d4edda;
}

.counting-cancelled {
    background-color: #f8d7da;
}
</style>

<script type="text/javascript">
$(document).ready(function() {
    // تهيئة التلميحات
    $('[data-toggle="tooltip"]').tooltip();
    
    // تلوين الصفوف حسب الحالة
    $('table tbody tr').each(function() {
        var status = $(this).find('.label').text().trim();
        if (status.includes('مسودة')) {
            $(this).addClass('counting-draft');
        } else if (status.includes('قيد التنفيذ')) {
            $(this).addClass('counting-in-progress');
        } else if (status.includes('مكتمل')) {
            $(this).addClass('counting-completed');
        } else if (status.includes('مرحل')) {
            $(this).addClass('counting-posted');
        } else if (status.includes('ملغي')) {
            $(this).addClass('counting-cancelled');
        }
    });
    
    // تحديث تلقائي كل 5 دقائق للجرد قيد التنفيذ
    setInterval(function() {
        if ($('.counting-in-progress').length > 0) {
            location.reload();
        }
    }, 300000);
    
    // فلترة سريعة
    $('#filter_counting_name').on('keyup', function() {
        var value = $(this).val().toLowerCase();
        $('table tbody tr').filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
        });
    });
    
    // تحديث شريط التقدم
    $('.progress-bar').each(function() {
        var percentage = $(this).attr('style').match(/width: (\d+)%/);
        if (percentage) {
            $(this).animate({
                width: percentage[1] + '%'
            }, 1000);
        }
    });
});
</script>

{{ footer }}
