<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/api/servicemanagement/v1/resources.proto

namespace Google\Cloud\ServiceManagement\V1;

if (false) {
    /**
     * This class is deprecated. Use Google\Cloud\ServiceManagement\V1\Rollout\RolloutStatus instead.
     * @deprecated
     */
    class Rollout_RolloutStatus {}
}
class_exists(Rollout\RolloutStatus::class);
@trigger_error('Google\Cloud\ServiceManagement\V1\Rollout_RolloutStatus is deprecated and will be removed in the next major release. Use Google\Cloud\ServiceManagement\V1\Rollout\RolloutStatus instead', E_USER_DEPRECATED);

