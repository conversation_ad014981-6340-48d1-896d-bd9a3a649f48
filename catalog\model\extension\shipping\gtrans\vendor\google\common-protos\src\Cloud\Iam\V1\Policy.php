<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: google/iam/v1/policy.proto

namespace Google\Cloud\Iam\V1;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Defines an Identity and Access Management (IAM) policy. It is used to
 * specify access control policies for Cloud Platform resources.
 * A `Policy` is a collection of `bindings`. A `binding` binds one or more
 * `members` to a single `role`. Members can be user accounts, service accounts,
 * Google groups, and domains (such as G Suite). A `role` is a named list of
 * permissions (defined by IAM or configured by users). A `binding` can
 * optionally specify a `condition`, which is a logic expression that further
 * constrains the role binding based on attributes about the request and/or
 * target resource.
 * **JSON Example**
 *     {
 *       "bindings": [
 *         {
 *           "role": "roles/resourcemanager.organizationAdmin",
 *           "members": [
 *             "user:mike&#64;example.com",
 *             "group:admins&#64;example.com",
 *             "domain:google.com",
 *             "serviceAccount:my-project-id&#64;appspot.gserviceaccount.com"
 *           ]
 *         },
 *         {
 *           "role": "roles/resourcemanager.organizationViewer",
 *           "members": ["user:eve&#64;example.com"],
 *           "condition": {
 *             "title": "expirable access",
 *             "description": "Does not grant access after Sep 2020",
 *             "expression": "request.time <
 *             timestamp('2020-10-01T00:00:00.000Z')",
 *           }
 *         }
 *       ]
 *     }
 * **YAML Example**
 *     bindings:
 *     - members:
 *       - user:mike&#64;example.com
 *       - group:admins&#64;example.com
 *       - domain:google.com
 *       - serviceAccount:my-project-id&#64;appspot.gserviceaccount.com
 *       role: roles/resourcemanager.organizationAdmin
 *     - members:
 *       - user:eve&#64;example.com
 *       role: roles/resourcemanager.organizationViewer
 *       condition:
 *         title: expirable access
 *         description: Does not grant access after Sep 2020
 *         expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
 * For a description of IAM and its features, see the
 * [IAM developer's guide](https://cloud.google.com/iam/docs).
 *
 * Generated from protobuf message <code>google.iam.v1.Policy</code>
 */
class Policy extends \Google\Protobuf\Internal\Message
{
    /**
     * Specifies the format of the policy.
     * Valid values are 0, 1, and 3. Requests specifying an invalid value will be
     * rejected.
     * Operations affecting conditional bindings must specify version 3. This can
     * be either setting a conditional policy, modifying a conditional binding,
     * or removing a binding (conditional or unconditional) from the stored
     * conditional policy.
     * Operations on non-conditional policies may specify any valid value or
     * leave the field unset.
     * If no etag is provided in the call to `setIamPolicy`, version compliance
     * checks against the stored policy is skipped.
     *
     * Generated from protobuf field <code>int32 version = 1;</code>
     */
    private $version = 0;
    /**
     * Associates a list of `members` to a `role`. Optionally may specify a
     * `condition` that determines when binding is in effect.
     * `bindings` with no members will result in an error.
     *
     * Generated from protobuf field <code>repeated .google.iam.v1.Binding bindings = 4;</code>
     */
    private $bindings;
    /**
     * `etag` is used for optimistic concurrency control as a way to help
     * prevent simultaneous updates of a policy from overwriting each other.
     * It is strongly suggested that systems make use of the `etag` in the
     * read-modify-write cycle to perform policy updates in order to avoid race
     * conditions: An `etag` is returned in the response to `getIamPolicy`, and
     * systems are expected to put that etag in the request to `setIamPolicy` to
     * ensure that their change will be applied to the same version of the policy.
     * If no `etag` is provided in the call to `setIamPolicy`, then the existing
     * policy is overwritten. Due to blind-set semantics of an etag-less policy,
     * 'setIamPolicy' will not fail even if the incoming policy version does not
     * meet the requirements for modifying the stored policy.
     *
     * Generated from protobuf field <code>bytes etag = 3;</code>
     */
    private $etag = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type int $version
     *           Specifies the format of the policy.
     *           Valid values are 0, 1, and 3. Requests specifying an invalid value will be
     *           rejected.
     *           Operations affecting conditional bindings must specify version 3. This can
     *           be either setting a conditional policy, modifying a conditional binding,
     *           or removing a binding (conditional or unconditional) from the stored
     *           conditional policy.
     *           Operations on non-conditional policies may specify any valid value or
     *           leave the field unset.
     *           If no etag is provided in the call to `setIamPolicy`, version compliance
     *           checks against the stored policy is skipped.
     *     @type \Google\Cloud\Iam\V1\Binding[]|\Google\Protobuf\Internal\RepeatedField $bindings
     *           Associates a list of `members` to a `role`. Optionally may specify a
     *           `condition` that determines when binding is in effect.
     *           `bindings` with no members will result in an error.
     *     @type string $etag
     *           `etag` is used for optimistic concurrency control as a way to help
     *           prevent simultaneous updates of a policy from overwriting each other.
     *           It is strongly suggested that systems make use of the `etag` in the
     *           read-modify-write cycle to perform policy updates in order to avoid race
     *           conditions: An `etag` is returned in the response to `getIamPolicy`, and
     *           systems are expected to put that etag in the request to `setIamPolicy` to
     *           ensure that their change will be applied to the same version of the policy.
     *           If no `etag` is provided in the call to `setIamPolicy`, then the existing
     *           policy is overwritten. Due to blind-set semantics of an etag-less policy,
     *           'setIamPolicy' will not fail even if the incoming policy version does not
     *           meet the requirements for modifying the stored policy.
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\Google\Iam\V1\Policy::initOnce();
        parent::__construct($data);
    }

    /**
     * Specifies the format of the policy.
     * Valid values are 0, 1, and 3. Requests specifying an invalid value will be
     * rejected.
     * Operations affecting conditional bindings must specify version 3. This can
     * be either setting a conditional policy, modifying a conditional binding,
     * or removing a binding (conditional or unconditional) from the stored
     * conditional policy.
     * Operations on non-conditional policies may specify any valid value or
     * leave the field unset.
     * If no etag is provided in the call to `setIamPolicy`, version compliance
     * checks against the stored policy is skipped.
     *
     * Generated from protobuf field <code>int32 version = 1;</code>
     * @return int
     */
    public function getVersion()
    {
        return $this->version;
    }

    /**
     * Specifies the format of the policy.
     * Valid values are 0, 1, and 3. Requests specifying an invalid value will be
     * rejected.
     * Operations affecting conditional bindings must specify version 3. This can
     * be either setting a conditional policy, modifying a conditional binding,
     * or removing a binding (conditional or unconditional) from the stored
     * conditional policy.
     * Operations on non-conditional policies may specify any valid value or
     * leave the field unset.
     * If no etag is provided in the call to `setIamPolicy`, version compliance
     * checks against the stored policy is skipped.
     *
     * Generated from protobuf field <code>int32 version = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setVersion($var)
    {
        GPBUtil::checkInt32($var);
        $this->version = $var;

        return $this;
    }

    /**
     * Associates a list of `members` to a `role`. Optionally may specify a
     * `condition` that determines when binding is in effect.
     * `bindings` with no members will result in an error.
     *
     * Generated from protobuf field <code>repeated .google.iam.v1.Binding bindings = 4;</code>
     * @return \Google\Protobuf\Internal\RepeatedField
     */
    public function getBindings()
    {
        return $this->bindings;
    }

    /**
     * Associates a list of `members` to a `role`. Optionally may specify a
     * `condition` that determines when binding is in effect.
     * `bindings` with no members will result in an error.
     *
     * Generated from protobuf field <code>repeated .google.iam.v1.Binding bindings = 4;</code>
     * @param \Google\Cloud\Iam\V1\Binding[]|\Google\Protobuf\Internal\RepeatedField $var
     * @return $this
     */
    public function setBindings($var)
    {
        $arr = GPBUtil::checkRepeatedField($var, \Google\Protobuf\Internal\GPBType::MESSAGE, \Google\Cloud\Iam\V1\Binding::class);
        $this->bindings = $arr;

        return $this;
    }

    /**
     * `etag` is used for optimistic concurrency control as a way to help
     * prevent simultaneous updates of a policy from overwriting each other.
     * It is strongly suggested that systems make use of the `etag` in the
     * read-modify-write cycle to perform policy updates in order to avoid race
     * conditions: An `etag` is returned in the response to `getIamPolicy`, and
     * systems are expected to put that etag in the request to `setIamPolicy` to
     * ensure that their change will be applied to the same version of the policy.
     * If no `etag` is provided in the call to `setIamPolicy`, then the existing
     * policy is overwritten. Due to blind-set semantics of an etag-less policy,
     * 'setIamPolicy' will not fail even if the incoming policy version does not
     * meet the requirements for modifying the stored policy.
     *
     * Generated from protobuf field <code>bytes etag = 3;</code>
     * @return string
     */
    public function getEtag()
    {
        return $this->etag;
    }

    /**
     * `etag` is used for optimistic concurrency control as a way to help
     * prevent simultaneous updates of a policy from overwriting each other.
     * It is strongly suggested that systems make use of the `etag` in the
     * read-modify-write cycle to perform policy updates in order to avoid race
     * conditions: An `etag` is returned in the response to `getIamPolicy`, and
     * systems are expected to put that etag in the request to `setIamPolicy` to
     * ensure that their change will be applied to the same version of the policy.
     * If no `etag` is provided in the call to `setIamPolicy`, then the existing
     * policy is overwritten. Due to blind-set semantics of an etag-less policy,
     * 'setIamPolicy' will not fail even if the incoming policy version does not
     * meet the requirements for modifying the stored policy.
     *
     * Generated from protobuf field <code>bytes etag = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setEtag($var)
    {
        GPBUtil::checkString($var, False);
        $this->etag = $var;

        return $this;
    }

}

