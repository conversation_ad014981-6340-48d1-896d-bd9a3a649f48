{{ header }}
<div id="common-success" class="container">
  <ul class="breadcrumb">
    {% for breadcrumb in breadcrumbs %}
    <li><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
    {% endfor %}
  </ul>
  <div class="row">{{ column_left }}
    {% if column_left and column_right %}
    {% set class = 'col-sm-6' %}
    {% elseif column_left or column_right %}
    {% set class = 'col-sm-9' %}
    {% else %}
    {% set class = 'col-sm-12' %}
    {% endif %}
    <div id="content" class="{{ class }}">{{ content_top }}
    {% if error %}
        <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ text_failure }}
            <button type="button" class="close" data-dismiss="alert">&times;</button>
        </div>
    {% endif %}
    {% if success %}
        <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{ text_success }}
            <button type="button" class="close" data-dismiss="alert">&times;</button>
        </div>
    {% endif %}
      <h1>{{ heading_title }}</h1>
      {{ text_message }}
      <div class="buttons">
        <div class="pull-right"><a href="{{ continue }}" class="btn btn-primary">{{ button_continue }}</a></div>
      </div>
      {{ content_bottom }}</div>
    {{ column_right }}</div>
</div>
{{ footer }}