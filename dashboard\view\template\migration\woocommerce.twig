{{ header }}{{ column_left }}
<div id="content">
  <div class="page-header">
    <div class="container-fluid">
      <div class="float-end">
        <button type="submit" form="form-migration" data-bs-toggle="tooltip" title="{{ button_save }}" class="btn btn-primary"><i class="fas fa-save"></i></button>
        <a href="{{ cancel }}" data-bs-toggle="tooltip" title="{{ button_cancel }}" class="btn btn-light"><i class="fas fa-reply"></i></a>
      </div>
      <h1>{{ heading_title }}</h1>
      <ol class="breadcrumb">
        {% for breadcrumb in breadcrumbs %}
          <li class="breadcrumb-item"><a href="{{ breadcrumb.href }}">{{ breadcrumb.text }}</a></li>
        {% endfor %}
      </ol>
    </div>
  </div>
  <div class="container-fluid">
    <div class="row mb-4">
      <div class="col-12">
        <div class="progress-wizard">
          <div class="progress-wizard-step{% if step == 1 %} active{% endif %}{% if step > 1 %} complete{% endif %}">
            <div class="progress-wizard-dot">
              <i class="fas fa-key"></i>
            </div>
            <div class="progress-wizard-info">
              <h4>{{ text_step1_title }}</h4>
              <p>{{ text_step1_description }}</p>
            </div>
          </div>
          <div class="progress-wizard-step{% if step == 2 %} active{% endif %}{% if step > 2 %} complete{% endif %}">
            <div class="progress-wizard-dot">
              <i class="fas fa-database"></i>
            </div>
            <div class="progress-wizard-info">
              <h4>{{ text_step2_title }}</h4>
              <p>{{ text_step2_description }}</p>
            </div>
          </div>
          <div class="progress-wizard-step{% if step == 3 %} active{% endif %}{% if step > 3 %} complete{% endif %}">
            <div class="progress-wizard-dot">
              <i class="fas fa-check-circle"></i>
            </div>
            <div class="progress-wizard-info">
              <h4>{{ text_step3_title }}</h4>
              <p>{{ text_step3_description }}</p>
            </div>
          </div>
          <div class="progress-wizard-step{% if step == 4 %} active{% endif %}{% if step > 4 %} complete{% endif %}">
            <div class="progress-wizard-dot">
              <i class="fas fa-sync"></i>
            </div>
            <div class="progress-wizard-info">
              <h4>{{ text_step4_title }}</h4>
              <p>{{ text_step4_description }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="card">
      <div class="card-header"><i class="fas fa-pencil-alt"></i> {{ text_form }}</div>
      <div class="card-body">
        <form id="form-migration" action="{{ action }}" method="post" data-oc-toggle="ajax">
          <div class="row mb-3">
            <label for="input-site-url" class="col-sm-2 col-form-label">{{ entry_site_url }}</label>
            <div class="col-sm-10">
              <input type="text" name="site_url" value="" placeholder="{{ entry_site_url }}" id="input-site-url" class="form-control"/>
              <div id="error-site-url" class="invalid-feedback"></div>
              <div class="form-text">{{ help_site_url }}</div>
            </div>
          </div>
          <div class="row mb-3">
            <label for="input-consumer-key" class="col-sm-2 col-form-label">{{ entry_consumer_key }}</label>
            <div class="col-sm-10">
              <input type="text" name="consumer_key" value="" placeholder="{{ entry_consumer_key }}" id="input-consumer-key" class="form-control"/>
              <div id="error-consumer-key" class="invalid-feedback"></div>
            </div>
          </div>
          <div class="row mb-3">
            <label for="input-consumer-secret" class="col-sm-2 col-form-label">{{ entry_consumer_secret }}</label>
            <div class="col-sm-10">
              <input type="password" name="consumer_secret" value="" placeholder="{{ entry_consumer_secret }}" id="input-consumer-secret" class="form-control"/>
              <div id="error-consumer-secret" class="invalid-feedback"></div>
            </div>
          </div>
          <hr>
          <div class="row mb-3">
            <label class="col-sm-2 col-form-label">{{ text_data_selection }}</label>
            <div class="col-sm-10">
              <div class="row">
                <div class="col-md-6">
                  <div class="card h-100">
                    <div class="card-header bg-light">{{ text_core_data }}</div>
                    <div class="card-body">
                      <div class="form-check mb-2">
                        <input type="checkbox" name="import_products" value="1" class="form-check-input" checked>
                        <label class="form-check-label">{{ text_products }}</label>
                      </div>
                      <div class="form-check mb-2">
                        <input type="checkbox" name="import_customers" value="1" class="form-check-input" checked>
                        <label class="form-check-label">{{ text_customers }}</label>
                      </div>
                      <div class="form-check mb-2">
                        <input type="checkbox" name="import_orders" value="1" class="form-check-input" checked>
                        <label class="form-check-label">{{ text_orders }}</label>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="card h-100">
                    <div class="card-header bg-light">{{ text_additional_data }}</div>
                    <div class="card-body">
                      <div class="form-check mb-2">
                        <input type="checkbox" name="import_categories" value="1" class="form-check-input">
                        <label class="form-check-label">{{ text_categories }}</label>
                      </div>
                      <div class="form-check mb-2">
                        <input type="checkbox" name="import_attributes" value="1" class="form-check-input">
                        <label class="form-check-label">{{ text_attributes }}</label>
                      </div>
                      <div class="form-check mb-2">
                        <input type="checkbox" name="import_reviews" value="1" class="form-check-input">
                        <label class="form-check-label">{{ text_reviews }}</label>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
    <div class="card">
      <div class="card-header"><i class="fas fa-sync"></i> {{ text_progress }}</div>
      <div class="card-body">
        <div id="progress-status" class="alert alert-info d-none">
          <i class="fas fa-spinner fa-spin"></i> {{ text_importing }}
        </div>
        <div id="progress-bar" class="progress mb-3 d-none">
          <div class="progress-bar" role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
        </div>
        <div id="progress-result" class="alert d-none"></div>
      </div>
    </div>
  </div>
</div>
<style>
.progress-wizard {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30px;
  position: relative;
}

.progress-wizard::before {
  content: '';
  position: absolute;
  top: 30px;
  left: 0;
  width: 100%;
  height: 2px;
  background: #e9ecef;
  z-index: 0;
}

.progress-wizard-step {
  flex: 1;
  text-align: center;
  position: relative;
  z-index: 1;
}

.progress-wizard-dot {
  width: 60px;
  height: 60px;
  line-height: 60px;
  background: #fff;
  border: 2px solid #e9ecef;
  border-radius: 50%;
  margin: 0 auto 15px;
  color: #6c757d;
  transition: all 0.3s ease;
}

.progress-wizard-step.active .progress-wizard-dot,
.progress-wizard-step.complete .progress-wizard-dot {
  background: #28a745;
  border-color: #28a745;
  color: #fff;
}

.progress-wizard-info h4 {
  font-size: 16px;
  margin-bottom: 5px;
  color: #495057;
}

.progress-wizard-info p {
  font-size: 14px;
  color: #6c757d;
  margin: 0;
}

.card {
  box-shadow: 0 2px 10px rgba(0,0,0,0.05);
  border: none;
  margin-bottom: 20px;
}

.card-header {
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  padding: 15px 20px;
}

.form-check-input:checked {
  background-color: #28a745;
  border-color: #28a745;
}

.progress {
  height: 10px;
  border-radius: 5px;
}

.progress-bar {
  background-color: #28a745;
  transition: width 0.3s ease;
}
</style>
<script type="text/javascript">
$('#form-migration').on('submit', function(e) {
    e.preventDefault();
    
    // Show progress elements
    $('#progress-status').removeClass('d-none');
    $('#progress-bar').removeClass('d-none');
    $('#progress-result').addClass('d-none');
    
    // Reset progress bar
    $('.progress-bar').css('width', '0%').attr('aria-valuenow', 0);
    
    // Validate form fields
    let isValid = true;
    const requiredFields = ['site_url', 'consumer_key', 'consumer_secret'];
    
    requiredFields.forEach(field => {
        const input = $(`#input-${field}`);
        const error = $(`#error-${field}`);
        
        if (!input.val().trim()) {
            input.addClass('is-invalid');
            error.text('هذا الحقل مطلوب').show();
            isValid = false;
        } else {
            input.removeClass('is-invalid');
            error.hide();
        }
    });
    
    if (!isValid) {
        $('#progress-status').addClass('d-none');
        $('#progress-bar').addClass('d-none');
        return;
    }
    
    // Get selected data types
    const selectedData = {
        products: $('input[name="import_products"]').is(':checked'),
        customers: $('input[name="import_customers"]').is(':checked'),
        orders: $('input[name="import_orders"]').is(':checked'),
        categories: $('input[name="import_categories"]').is(':checked'),
        attributes: $('input[name="import_attributes"]').is(':checked'),
        reviews: $('input[name="import_reviews"]').is(':checked')
    };

    // Prepare migration data
    const migrationData = {
        connection: {
            site_url: $('#input-site-url').val(),
            consumer_key: $('#input-consumer-key').val(),
            consumer_secret: $('#input-consumer-secret').val()
        },
        data_types: selectedData
    };

    // Start migration process
    $.ajax({
        url: 'index.php?route=migration/woocommerce/migrate',
        type: 'POST',
        data: migrationData,
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                let currentStep = 0;
                const totalSteps = Object.values(selectedData).filter(Boolean).length;
                const stepIncrement = 100 / totalSteps;

                const processStep = function() {
                    $.ajax({
                        url: 'index.php?route=migration/woocommerce/process_step',
                        type: 'POST',
                        data: { step: currentStep },
                        dataType: 'json',
                        success: function(stepResponse) {
                            if (stepResponse.success) {
                                currentStep++;
                                const progress = Math.min(currentStep * stepIncrement, 100);
                                $('.progress-bar').css('width', progress + '%').attr('aria-valuenow', progress);
                                
                                if (currentStep < totalSteps) {
                                    processStep();
                                } else {
                                    $('#progress-status').addClass('d-none');
                                    $('#progress-result')
                                        .removeClass('d-none alert-danger')
                                        .addClass('alert-success')
                                        .html('<i class="fas fa-check-circle"></i> تم اكتمال عملية النقل بنجاح!');
                                }
                            } else {
                                handleError(stepResponse.error);
                            }
                        },
                        error: function() {
                            handleError('حدث خطأ أثناء معالجة البيانات');
                        }
                    });
                };

                processStep();
            } else {
                handleError(response.error);
            }
        },
        error: function() {
            handleError('فشل الاتصال بالخادم');
        }
    });
});

function handleError(message) {
    $('#progress-status').addClass('d-none');
    $('#progress-result')
        .removeClass('d-none alert-success')
        .addClass('alert-danger')
        .html(`<i class="fas fa-exclamation-circle"></i> ${message}`);
}
</script>
{{ footer }}