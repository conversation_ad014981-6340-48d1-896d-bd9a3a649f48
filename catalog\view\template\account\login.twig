{{ header }}
<div id="account-login" class="container" style="padding-top:10px">

  {% if success %}
  <div class="alert alert-success alert-dismissible"><i class="fa fa-check-circle"></i> {{ success }}</div>
  {% endif %}
  {% if error_warning %}
  <div class="alert alert-danger alert-dismissible"><i class="fa fa-exclamation-circle"></i> {{ error_warning }}</div>
  {% endif %}
  <div class="row">{{ column_left }}
    {% if column_left and column_right %}
    {% set class = 'col-sm-6' %}
    {% elseif column_left or column_right %}
    {% set class = 'col-sm-9' %}
    {% else %}
    {% set class = 'col-sm-12' %}
    {% endif %}
    <div id="content" class="{{ class }}">{{ content_top }}
      <div class="row">
        <div class="col-sm-12">
          <div class="well">
            <form action="{{ action }}" method="post" enctype="multipart/form-data">
              <div class="form-group">
                <label class="control-label" for="input-email">{{ entry_email }}</label>
                <input type="text" name="email" value="{{ email }}" placeholder="{{ entry_email }}" id="input-email" class="form-control" />
              </div>
              <div class="form-group">
                <label class="control-label" for="input-password">{{ entry_password }}</label>
                <input type="password" name="password" value="{{ password }}" placeholder="{{ entry_password }}" id="input-password" class="form-control" />
                <a href="{{ forgotten }}">{{ text_forgotten }}</a></div>
              <input type="submit" value="{{ text_returning_customer }}" class="btn btn-warning bold-btn w-100" id="qc-login" style="background-color: orange !important; color: #000 !important; height: 50px; font-size: 20px;" />

              {% if redirect %}
              <input type="hidden" name="redirect" value="{{ redirect }}" />
              {% endif %}
            </form>
          </div>
        </div>
      </div>
      {{ content_bottom }}</div>
    {{ column_right }}</div>
</div>
{{ footer }}
